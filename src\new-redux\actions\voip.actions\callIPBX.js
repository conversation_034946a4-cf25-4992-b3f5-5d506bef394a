import {
  START_CALL,
  STOP_CALL,
  STOP_CALL_VIDEO,
  START_CALL_VIDEO,
  UPDATE_CALL_IPBX,
} from "../../constants";

export const start_call = (payload) => {
  return {
    type: START_CALL,
    payload,
  };
};
export const updateCallStatus = (payload) => {
  return {
    type: UPDATE_CALL_IPBX,
    payload,
  };
};
export const stop_call = () => {
  return {
    type: STOP_CALL,
  };
};

export const start_video_call = (number) => {
  return {
    type: START_CALL_VIDEO,
    payload: number,
  };
};

export const stop_video_call = () => {
  return {
    type: STOP_CALL_VIDEO,
  };
};
