export const navigateUrlAccordingToModuleId = (relationType) => {
  const relationTypeMap = {
    0: "/tasks",
    1: "/companies",
    2: "/contacts",
    3: "/deals",
    5: "/products",
    6: "/tickets",
    7: "/projects",
    8: "/booking",
    9: "/leads",
    11: "/invoices",
    1000: "/notes",
  };

  return relationTypeMap[relationType] || ""; // Default to empty string or handle as needed
};
