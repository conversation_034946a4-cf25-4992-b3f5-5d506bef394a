import { Button, Input, Select, Space, Tag } from "antd";
import React, { useEffect, useRef } from "react";

const StageComponents = ({
  index,
  handleInputChange,
  handleStageColorChange,
  handleStageProbChange,
  inputRef,
  colors,
  save,
  input,
  loadingPip,
  handleRemoveStage,
  obj,
}) => {
  const inputStageRef = useRef(null);

  useEffect(() => {
    inputStageRef.current.focus();
  }, []);

  return (
    <Space style={{ width: "100%" }}>
      <Input
        type="text"
        onChange={(event) => handleInputChange(index, event)}
        ref={inputStageRef}
        value={obj.label}
      />
      <Select
        style={{
          width: 70,
        }}
        options={colors.map((el) => ({
          label: <Tag color={el.value}></Tag>,
          value: el.value,
        }))}
        onChange={(value, options) => handleStageColorChange(index, value)}
        value={obj.color}
      />
      <Input
        addonAfter="%"
        onChange={(event) => handleStageProbChange(index, event)}
        value={obj.propability}
      />

      <Button
        htmlType="submit"
        type="primary"
        onClick={() => save(input, index)}
        loading={loadingPip}
        disabled={!obj.label || !obj.color || !obj.propability ? true : false}
      >
        Save
      </Button>
      <Button onClick={() => handleRemoveStage(index)}>Cancel</Button>
    </Space>
  );
};

export default StageComponents;
