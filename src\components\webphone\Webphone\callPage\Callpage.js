import "./Callpage.css";
import React, { useState, useEffect, useMemo, useCallback } from "react";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";

import {
  TeamOutlined,
  PauseOutlined,
  AudioOutlined,
  SwapOutlined,
  HolderOutlined,
  CaretRightOutlined,
  AudioMutedOutlined,
  CalendarOutlined,
  MessageOutlined,
  PlusCircleOutlined,
  ArrowDownOutlined,
} from "@ant-design/icons";
import {
  Col,
  Row,
  Button,
  Tooltip,
  Popover,
  Dropdown,
  Popconfirm,
  Spin,
  Skeleton,
  Space,
  Radio,
  Tag,
  Badge,
} from "antd";
import { BsTelephonePlus } from "react-icons/bs";
import { AvatarChat } from "../../../Chat";
import { getName } from "../../../../pages/layouts/chat/utils/ConversationUtils";
import { useLocation } from "react-router-dom";
import useTimer from "../../../Timer";

import { useDispatch } from "react-redux";
import { Cg<PERSON><PERSON>lane } from "react-icons/cg";
import { HiOutlineBuildingOffice, HiPhone } from "react-icons/hi2";
import MainService from "services/main.service";
import { checkIfPathOnView360 } from "pages/voip/helpers/helpersFunc";
import useActionCall from "pages/voip/helpers/ActionCall";
import { SET_RECEIVER_INFO } from "new-redux/constants";
import AutoCompleteForCall from "./AutoCompleteForCall";
import { URL_ENV } from "index";
import { FcCallTransfer } from "react-icons/fc";
import { LuInfo } from "react-icons/lu";
import DisplayAvatar from "pages/voip/components/DisplayAvatar";

const style = {
  background: "#F1F5F9",
  padding: "0",
};
const dtmfNumber = [1, 2, 3, 4, 5, 6, 7, 8, 9, "*", 0, "#"];
function CallPage({
  activecall,
  setActivecall,
  holdSession,
  hungUp,
  upBtn,
  session,
  setSession,
  changeSession,
  ConferenceCall,
  sipMergeSessions,
  transfertCall,
  muteSession,
  sendDTMF,
  webphoneDown,
  sendMessageAndHungUP,
  isCreationDone,
  handleDrawerSiderLayout,
  handleActionWebPhone,
  setTheReceiverInfo,
  handleOpenMgsDrawer,
  attendedTransfer,
  mergeCallsToConference,
}) {
  const call = useActionCall();
  const [t] = useTranslation("common");
  const location = useLocation();
  const { user } = useSelector(({ user }) => user);
  const access = user.access || {};

  const [hold, setHold] = useState(<PauseOutlined />);
  const [callActive, setCallActif] = useState(
    null
    // {
    //   conf: null,
    //   date: new Date().getTime(),
    //   statusAppel: "accepted",
    //   type: "audio",
    //   isOnHold: () => {},
    // }
  );
  const [inputDTMF, setInputDTMF] = useState("");
  const [inputState, setInputState] = useState({
    conf: {
      open: false,
      value: "",
    },
    newCall: {
      open: false,
      value: "",
    },
    transfert: {
      open: false,
      value: "",
    },
  });
  const [isAttendedTransfer, setIsAttendedTransfer] = useState(false);
  const [attendedTransferDist, setAttendedTransferDist] = useState(null);

  const forwardingCall = useSelector(
    (state) => state.voipBlackList.forwardingCall
  );
  // null
  const dispatch = useDispatch();
  const receiverInfo = useSelector((state) => state.voipBlackList.receiverInfo);
  const confInfo = useSelector((state) => state.voipBlackList.confInfo);

  // {
  //   name: "aymen_dev",
  //   image:
  //     "https://spherebackdev.cmk.biz:4543/storage/uploads/wajdi-enfant.png",
  //   extension: 470,
  //   id: "64ee1579c7a99afd3509fa62",
  //   uid: "9a01c982-b1aa-42a9-b8bc-c769692d86b8",
  //   phones: [["+216", "98336214"]],
  //   number: "470",
  //   email: ["<EMAIL>"],
  //   family_id: 4,
  // };
  const items = [
    {
      key: "1",
      label: (
        <div
          onClick={() =>
            sendMessageAndHungUP(
              callActive,
              callActive?.remoteIdentity?.uri?.user,
              "Je vous rappelle plus tard"
            )
          }
        >
          Je vous rappelle plus tard
        </div>
      ),
    },
    {
      key: "2",
      label: (
        <div
          onClick={() =>
            sendMessageAndHungUP(
              callActive,

              callActive?.remoteIdentity?.uri?.user,
              "Rappelez-moi plus tard"
            )
          }
        >
          Rappelez-moi plus tard
        </div>
      ),
    },
    {
      key: "3",
      label: (
        <div
          onClick={() =>
            sendMessageAndHungUP(
              callActive,

              callActive?.remoteIdentity?.uri?.user,
              "Je suis occupé Maintenant"
            )
          }
        >
          Je suis occupé Maintenant
        </div>
      ),
    },
  ];

  useEffect(() => {
    let callActiveLocal;
    // console.log({ session, activecall });
    if (session.length > 1) {
      callActiveLocal = !activecall?.uuid ? session[0] : activecall;
    } else {
      callActiveLocal = session[0];
    }
    dispatch({
      type: SET_RECEIVER_INFO,
      payload: callActiveLocal?.receiverInfo,
    });
    setCallActif(callActiveLocal);
    if (callActiveLocal) {
      callActiveLocal.isOnHold().local
        ? setHold(<CaretRightOutlined />)
        : setHold(<PauseOutlined />);
    }
    // console.log({ callActiveLocal });
  }, [session, activecall, activecall?.uuid, webphoneDown]);

  const timer = useTimer(
    callActive?.date,
    callActive?.statusAppel !== "ringing"
  );

  // console.log(timer, callActive?.statusAppel);

  const containerStyle = {
    position: "relative",
  };

  const makeHold = (actif) => {
    let time;
    holdSession(actif);

    clearTimeout(time);

    time = setTimeout(() => {
      actif.isOnHold().local
        ? setHold(<CaretRightOutlined />)
        : setHold(<PauseOutlined />);
      clearTimeout(time);
    }, 500);
  };

  function deleteSession(session) {
    return session.conf !== "inviter";
  }

  const selectDTMF = (e) => {
    setInputDTMF((prev) => prev.concat(" " + e));
    const time = setTimeout(() => {
      const container = document.getElementById("container_dtmf");
      if (container) container.scrollLeft = container.scrollWidth;
      clearTimeout(time);
    }, 150);
    sendDTMF(callActive, e);
  };

  const multipleSession = useMemo(
    () =>
      (session.length > 1 || session[0]?.conf || confInfo) &&
      session.filter(deleteSession).map((newSess) =>
        newSess?.uuid === callActive?.uuid ? (
          newSess.conf === null && !confInfo ? (
            newSess.statusAppel !== "ringing" &&
            newSess?.uuid !== callActive?.uuid ? (
              <Button
                // className="border-0 bg-transparent font-bold"
                type="primary"
                onClick={() => changeSession(newSess)}
              >
                <p className="font-semibold">
                  {
                    newSess?.receiverInfo?.name
                      ?.replaceAll("_", " ")
                      ?.split(" ")?.[0]
                  }
                </p>
              </Button>
            ) : null
          ) : (
            <div className=" flex flex-col items-center justify-center px-3 py-1.5">
              <span className="font-semibold leading-5">{t("voip.conf")}</span>
              <span className="leading-4 text-slate-500">{`${
                newSess?.receiverInfo?.number
              } & ${
                newSess?.conf?.receiverInfo?.number || confInfo?.number
              }`}</span>
              <Button
                className="mt-1"
                type="primary"
                shape="circle"
                size="small"
                danger
                // disabled={!callActive?.conf.established}
                // disabled={!!confInfo}
                icon={
                  <HiPhone
                    style={{
                      fontSize: 18,
                      transform: "rotate(135deg)",
                      marginTop: 2,
                    }}
                  />
                }
                onClick={() => {
                  !!callActive?.conf && hungUp(callActive?.conf);
                  !!callActive && hungUp(callActive);
                }}
              />
            </div>
          )
        ) : newSess.statusAppel === "ringing" ? (
          <div className="flex justify-between  px-3 py-1.5 ">
            {newSess?.receiverInfo?.number ? (
              <div className="flex items-center justify-center space-x-1">
                <DisplayAvatar
                  urlImg={newSess?.receiverInfo?.image}
                  name={newSess?.receiverInfo?.name}
                  icon={
                    newSess?.receiverInfo?.extension === "sphere_visio" &&
                    newSess?.receiverInfo?.image
                  }
                  size={38}
                />
                <div className="flex flex-col justify-center">
                  <span className=" max-w-44 truncate font-semibold leading-4">
                    {newSess?.receiverInfo?.name?.replaceAll("_", " ") ||
                      newSess?.receiverInfo?.number}
                    {/* Hassine Ben Ali Ben Hassine Turki Basla */}
                  </span>
                  {!!newSess?.receiverInfo?.name && (
                    <span className="leading-4 text-slate-500">
                      {newSess?.receiverInfo?.number}
                    </span>
                  )}
                </div>
              </div>
            ) : (
              <Space>
                <Skeleton.Avatar active size="default" shape="circle" />
                <Skeleton.Input active size="small" />
              </Space>
            )}
            <div className="flex items-center space-x-1">
              {newSess.direction === "incoming" &&
              (session.length < 3 || !!callActive.conf) ? (
                // <div className="flex items-center space-x-2">
                <Button
                  className="bg-green-700"
                  type="primary"
                  shape="circle"
                  size="small"
                  icon={<HiPhone style={{ fontSize: 16 }} />}
                  onClick={() => upBtn(newSess)}
                />
              ) : // </div>
              null}
              <Button
                type="primary"
                shape="circle"
                size="small"
                danger
                icon={
                  <HiPhone
                    style={{ transform: "rotate(135deg)", fontSize: 16 }}
                  />
                }
                onClick={() => hungUp(newSess)}
              />
            </div>
          </div>
        ) : (
          // in case one session is on hold
          <div className="flex items-center justify-between space-x-1 px-3 py-1.5">
            <div className="flex items-center justify-center space-x-1">
              <DisplayAvatar
                urlImg={newSess?.receiverInfo?.image}
                name={newSess?.receiverInfo?.name}
                icon={
                  newSess?.receiverInfo?.extension === "sphere_visio" &&
                  newSess?.receiverInfo?.image
                }
                size={38}
              />
              <div className="flex flex-col justify-center">
                <span className=" max-w-40 truncate font-semibold leading-4">
                  {newSess?.receiverInfo?.name?.replaceAll("_", " ") ||
                    newSess?.receiverInfo?.number}
                  {/* Hassine Ben Ali Ben Hassine Turki Basla */}
                </span>
                {!!newSess?.receiverInfo?.name && (
                  <span className="leading-4 text-slate-500">
                    {newSess?.receiverInfo?.number}
                  </span>
                )}
              </div>
            </div>
            <div className="flex flex-row items-center space-x-2">
              <Tooltip
                title={`${t("voip.switchTo")} ${
                  newSess?.receiverInfo?.name
                    ?.replaceAll("_", " ")
                    ?.slice(0, 15) || newSess?.receiverInfo?.number
                }`}
              >
                <Button
                  onClick={() => changeSession(newSess)}
                  size="small"
                  // type="primary"
                  shape="circle"
                  icon={<SwapOutlined style={{ fontSize: 16 }} />}
                />
              </Tooltip>
              <Tooltip key="voip-conférence" title={t("voip.conférence")}>
                <Button
                  icon={<TeamOutlined style={{ fontSize: 16 }} />}
                  size="small"
                  shape="circle"
                  type="primary"
                  // className="bg-green-700"
                  onClick={
                    () => mergeCallsToConference()
                    /*upBtn(newSess)*/
                  }
                />
              </Tooltip>
            </div>
          </div>
        )
      ),
    [session, session?.length, t, callActive?.uuid, confInfo]
  );

  const onSubmit = (e, type, attendTransfer) => {
    try {
      let value = e?.extension;
      // console.log({ type, attendTransfer, value, e });
      if (type === "newCall") call(value);
      else if (type === "transfert") {
        if (isAttendedTransfer) {
          setAttendedTransferDist(value);
          call(value);
          activecall.hold();
          activecall.statusAppel = "holding";
        } else transfertCall(value, e);
      } else if (type === "conf") ConferenceCall(value);
      const timeout = setTimeout(() => {
        setInputState((prev) => ({
          ...prev,
          [type]: {
            open: false,
            value: "",
          },
        }));
        clearTimeout(timeout);
      }, 500);
    } catch (e) {
      console.log("error", e);
    }
  };
  // );
  const HandleAddNewContact = useCallback(() => {
    const createItem = (key, labelKey, Icon, familyId) => ({
      key,
      label: t(labelKey),
      icon: <Icon style={{ fontSize: 15 }} />,
      onClick: () => {
        handleActionWebPhone("create_element", {
          familyId,
          number: receiverInfo?.number,
        });
      },
    });

    const items = [
      createItem("0", "voip.addCompany", HiOutlineBuildingOffice, 1),
      createItem("1", "voip.addContact", TeamOutlined, 2),
      createItem("3", "voip.addLead", CgUserlane, 9),
    ];

    return (
      <Dropdown menu={{ items }} trigger={["click"]} placement="bottomRight">
        <Button
          size="small"
          shape="circle"
          type="text"
          icon={<PlusCircleOutlined style={{ fontSize: 15 }} />}
        />
      </Dropdown>
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [receiverInfo?.number]);
  //
  // Catch if the creation of the a new family (contacts , company, ..)  is done so, re-call the api numberCallApiIPBX to refresh the receiver info
  useEffect(() => {
    async function fetchData() {
      try {
        if (session.length !== 0 && receiverInfo.number) {
          const resp = await MainService.numberCallApiIPBX(receiverInfo.number);
          setTheReceiverInfo(resp, null, receiverInfo.number);
        }
      } catch (e) {
        console.log(e);
      }
    }
    if (isCreationDone) {
      fetchData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isCreationDone]);

  //  webphone full height
  return (
    <div
      id="container"
      style={containerStyle}
      className={`z-5 relative flex h-[26.6rem] w-full flex-col items-center justify-center  ${
        callActive?.statusAppel === "holding" && !!multipleSession
          ? "pb-3 pt-10"
          : !multipleSession
          ? "pb-3"
          : !!multipleSession
          ? "pt-1.5"
          : ""
      }`}
    >
      {multipleSession && (
        <di
          className="absolute left-0 top-0 z-10 w-full rounded-b-md shadow-md"
          // style={{
          //   textAlign: "center",
          //   marginBottom: 6,
          // }}
        >
          {multipleSession}
        </di>
      )}
      {inputDTMF && (
        <div
          id="container_dtmf"
          className="absolute top-5 w-[200px] flex-1 flex-nowrap  overflow-x-auto overflow-y-hidden  whitespace-nowrap text-sm    "
        >
          <p className="  w-full py-1 text-center text-slate-500   ">
            {inputDTMF}
          </p>
        </div>
      )}
      {forwardingCall &&
        (receiverInfo?.number === forwardingCall?.dst ||
          receiverInfo?.id === forwardingCall?.dst_id) &&
        forwardingCall?.dst_forwarding.length > 0 && (
          <>
            <div className="flex max-h-36  w-full flex-1 flex-col overflow-y-auto py-2 pl-11">
              <div className="flex  items-center  space-x-1.5  ">
                <AvatarChat
                  name={
                    receiverInfo?.name?.length > 0
                      ? getName(receiverInfo?.name, "avatar")
                      : "?"
                  }
                  url={receiverInfo?.image}
                  size={40}
                  fontSize={12}
                  hasImage={receiverInfo?.image}
                />
                <div className="flex flex-col items-center justify-start">
                  <div className=" flex flex-col ">
                    <span className=" max-w-[150px]  truncate text-sm font-semibold">
                      {receiverInfo?.name
                        ? getName(receiverInfo?.name, "name")
                        : receiverInfo?.number}
                    </span>

                    <span
                      style={{ fontSize: "0.9rem" }}
                      className=" truncate text-sm font-medium leading-4 text-slate-500"
                    >
                      {receiverInfo?.number}
                    </span>
                    {!receiverInfo?.name && (
                      <div className="ml-1">{<HandleAddNewContact />}</div>
                    )}
                  </div>
                </div>
              </div>
              {forwardingCall?.dst_forwarding?.map((item, index) => (
                <div
                  className="flex w-full  flex-col  justify-center"
                  key={`forwarding_${item.num}_${index}`}
                >
                  {/* {console.log(
                    { item },
                    URL_ENV?.REACT_APP_BASE_URL +
                      URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                      item?.image
                  )} */}
                  <div className="my-2 flex  items-center space-x-4 ">
                    <ArrowDownOutlined
                      style={{
                        color: "rgb(234 88 12)",
                      }}
                      className="  ml-2  text-xl"
                    />
                    <span className=" text-xs italic text-gray-400">
                      {" "}
                      {t("chat.forward.text_message_forwarded") +
                        " " +
                        t("chat.forward.to")}{" "}
                    </span>
                  </div>

                  <div className="flex items-center  space-x-2 ">
                    <AvatarChat
                      name={
                        item?.name?.length > 0
                          ? getName(item?.name, "avatar")
                          : "?"
                      }
                      url={
                        URL_ENV?.REACT_APP_BASE_URL +
                        URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                        item?.image
                      }
                      size={40}
                      fontSize={12}
                      hasImage={item?.image}
                    />
                    <div className="flex flex-col items-center justify-start">
                      <div className=" flex flex-col ">
                        {!!item?.name && item?.name !== "null" && (
                          <span className=" max-w-[150px]  truncate text-sm font-semibold">
                            {item?.name
                              ? getName(item?.name, "name")
                              : item?.num}
                            {/* {console.log({ item })} */}
                          </span>
                        )}

                        <span
                          style={{ fontSize: "0.9rem" }}
                          className="   truncate text-sm font-medium leading-4 text-slate-500"
                        >
                          {item?.num}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </>
        )}

      {forwardingCall && timer && timer !== "--:--" && (
        //
        <time className="ml-2 mt-2  font-semibold text-slate-400">
          <Tag bordered={false} color="success">
            {timer}
          </Tag>
        </time>
      )}

      {/* render when call incoming and ringing */}
      {callActive?.direction === "incoming" &&
      callActive?.statusAppel === "ringing" ? (
        <>
          <Spin spinning={callActive.loading === true} />
          <div className="flex flex-col items-center">
            <div className="mb-2 text-center">
              {callActive.loading === true ? (
                <Skeleton.Avatar active size={100} />
              ) : (
                <DisplayAvatar
                  urlImg={receiverInfo?.image}
                  name={receiverInfo?.name}
                  icon={
                    receiverInfo?.extension === "sphere_visio" &&
                    receiverInfo?.image
                  }
                  size={100}
                />
                // <AvatarChat
                //   name={
                //     receiverInfo?.name
                //       ? getName(receiverInfo?.name, "avatar")
                //       : "?"
                //   }
                //   url={receiverInfo?.image}
                //   size={100}
                //   hasImage={receiverInfo?.image}
                // />
              )}
            </div>

            <div className="relative mt-1 flex w-[220px] items-center justify-center space-x-1">
              <span className=" max-w-[180px]  truncate text-sm font-semibold">
                {receiverInfo?.name
                  ? getName(receiverInfo?.name, "name")
                  : receiverInfo?.number}
                {/* Hassine Ben Ali Ben Hassine Turki Basla */}
              </span>
              {receiverInfo?.extension !== "sphere_visio" && (
                <div className="absolute right-0">
                  {!!receiverInfo?.name &&
                    (checkIfPathOnView360(location.pathname) ||
                    // location.pathname === "/directory"
                    location.pathname === "/telephony/directory" ? (
                      <Popconfirm
                        title={t("voip.view360")}
                        description={t("voip.alreadyInView360")}
                        zIndex={9999}
                        onConfirm={() =>
                          handleDrawerSiderLayout("open", "navigate")
                        }
                        okText={t("voip.yes")}
                        cancelText={t("voip.no")}
                        overlayStyle={{ maxWidth: 360 }}
                      >
                        <Button
                          size="small"
                          shape="circle"
                          type="link"
                          icon={<LuInfo style={{ fontSize: 18 }} />}
                          disabled={
                            checkIfPathOnView360(location.pathname) &&
                            location.pathname?.split("/")?.pop() ===
                              receiverInfo?.id
                          }
                        />
                      </Popconfirm>
                    ) : (
                      <Button
                        onClick={() => handleDrawerSiderLayout("open")}
                        size="small"
                        shape="circle"
                        type="link"
                        icon={<LuInfo style={{ fontSize: 18 }} />}
                      />
                    ))}
                </div>
              )}
            </div>

            <div className=" mt-1 flex items-center justify-center space-x-1">
              <span
                style={{ fontSize: "0.9rem" }}
                className=" max-w-[150px] truncate text-sm font-medium  text-slate-600"
              >
                {receiverInfo?.number}
              </span>
              {!receiverInfo?.name && !callActive.loading && (
                <HandleAddNewContact />
              )}
            </div>
          </div>

          <div className="contents">
            <div className="mt-4 w-full">
              {receiverInfo?.name && callActive?.uuid && (
                <div className="pb-3 pt-1 text-center">
                  <Dropdown
                    menu={{
                      items,
                    }}
                    placement="top"
                    // arrow
                    trigger={["click"]}
                  >
                    <Button type="link">{t("voip.sendMsgWhenRinging")}</Button>
                  </Dropdown>
                </div>
              )}
              <div className="flex w-full justify-center gap-14">
                <Button
                  className="bg-green-700"
                  type="primary"
                  shape="circle"
                  size="large"
                  icon={
                    <HiPhone
                      style={{
                        fontSize: 22,
                      }}
                    />
                  }
                  onClick={() => upBtn(callActive)}
                />

                <Button
                  type="primary"
                  shape="circle"
                  size="large"
                  danger
                  icon={
                    <HiPhone
                      style={{
                        fontSize: 22,
                        transform: "rotate(135deg)",
                        marginTop: 2,
                      }}
                    />
                  }
                  onClick={() => hungUp(callActive)}
                />
              </div>
            </div>
          </div>
        </>
      ) : (
        // else call outging and status != riging
        <>
          {/*render when conf === null and audio call */}
          {callActive?.conf === null &&
          callActive?.type !== "video" &&
          !confInfo ? (
            <>
              {!forwardingCall && (
                <>
                  <DisplayAvatar
                    urlImg={receiverInfo?.image}
                    name={receiverInfo?.name}
                    icon={
                      receiverInfo?.extension === "sphere_visio" &&
                      receiverInfo?.image
                    }
                    size={100}
                  />
                  {/* <AvatarChat
                    name={
                      receiverInfo?.name?.length > 0
                        ? getName(receiverInfo?.name, "avatar")
                        : "?"
                    }
                    url={receiverInfo?.image}
                    size={100}
                    fontSize={30}
                    hasImage={receiverInfo?.image}
                  /> */}

                  <div className="mt-2 flex max-h-20 max-w-[250px] flex-wrap items-center justify-center overflow-y-auto ">
                    <div className="flex flex-col items-center">
                      <div className=" relative mt-1 flex w-[220px] items-center justify-center space-x-1">
                        <span className="max-w-[180px]  truncate text-sm font-semibold">
                          {receiverInfo?.name
                            ? getName(receiverInfo?.name, "name")
                            : receiverInfo?.number}
                        </span>
                        <div className="absolute right-0">
                          {session?.length > 1 &&
                          isAttendedTransfer &&
                          attendedTransferDist === receiverInfo?.number ? (
                            <Tooltip title={t("voip.confirmTransfer")}>
                              <Button
                                onClick={() =>
                                  attendedTransfer(
                                    attendedTransferDist,
                                    callActive
                                  )
                                }
                                // size="small"
                                shape="circle"
                                // type="primary"
                                icon={
                                  // <CheckCircleTwoTone twoToneColor="#52c41a" />
                                  <FcCallTransfer style={{ fontSize: 20 }} />
                                }
                              />
                            </Tooltip>
                          ) : receiverInfo?.extension !== "sphere_visio" ? (
                            receiverInfo?.name &&
                            (checkIfPathOnView360(location.pathname) ||
                            // location.pathname === "/directory"
                            location.pathname === "/telephony/directory" ? (
                              <Popconfirm
                                title={t("voip.view360")}
                                description={t("voip.alreadyInView360")}
                                zIndex={9999}
                                onConfirm={() =>
                                  handleDrawerSiderLayout("open", "navigate")
                                }
                                // onCancel={cancel}
                                okText={t("voip.yes")}
                                cancelText={t("voip.no")}
                                overlayStyle={{ maxWidth: 360 }}
                              >
                                <Button
                                  size="small"
                                  shape="circle"
                                  type="link"
                                  icon={<LuInfo style={{ fontSize: 18 }} />}
                                  disabled={
                                    checkIfPathOnView360(location.pathname) &&
                                    location.pathname?.split("/")?.pop() ===
                                      receiverInfo?.id
                                  }
                                />
                              </Popconfirm>
                            ) : (
                              <Button
                                onClick={() => handleDrawerSiderLayout("open")}
                                size="small"
                                shape="circle"
                                type="link"
                                icon={<LuInfo style={{ fontSize: 18 }} />}
                              />
                            ))
                          ) : null}
                        </div>
                      </div>

                      <div className="mt-1 flex items-center justify-center space-x-1">
                        <span className=" max-w-[150px] truncate text-sm font-medium  text-slate-600">
                          {receiverInfo?.number}
                        </span>
                        {!receiverInfo?.name && <HandleAddNewContact />}
                      </div>
                    </div>
                  </div>
                </>
              )}

              {!forwardingCall && (
                <time className="ml-2 mt-2  font-semibold ">
                  {timer && timer !== "--:--" ? (
                    <Tag bordered={false} color="success">
                      {timer}
                    </Tag>
                  ) : (
                    <Tag
                      bordered={false}
                      color={
                        callActive?.statusAppel === "ringing"
                          ? "processing"
                          : "success"
                      }
                    >
                      {callActive?.statusAppel === "ringing"
                        ? `${t("voip.coursAppel")}...`
                        : `${t("voip.connectingInProgress")}`}
                    </Tag>
                  )}
                </time>
              )}

              {callActive?.statusAppel === "holding" ? (
                <div className="mt-1 flex font-semibold">
                  <Tag bordered={false} color="error">
                    {t("voip.pauseAppel")}
                  </Tag>
                </div>
              ) : null}
              <div className="my-4 flex flex-col space-y-3 px-0">
                <div className="flex w-full items-center justify-between space-x-3">
                  <Col className="gutter-row" span={3}>
                    <div className="flex w-full flex-col items-center  text-xs text-slate-400">
                      <Tooltip
                        title={
                          callActive?.isOnHold()?.local
                            ? t("voip.playBut")
                            : t("voip.pauseBut")
                        }
                      >
                        <Button
                          icon={hold}
                          size="middle"
                          className="text-slate-500"
                          onClick={() => makeHold(callActive)}
                          disabled={callActive?.statusAppel === "ringing"}
                        />
                      </Tooltip>
                    </div>
                  </Col>
                  <Col className="gutter-row" span={3}>
                    <div className="flex w-full flex-col items-center text-xs text-slate-400">
                      <Tooltip title={t("voip.silence")}>
                        <Button
                          icon={
                            callActive?.ismute === "yes" ? (
                              <AudioMutedOutlined />
                            ) : (
                              <AudioOutlined />
                            )
                          }
                          size="middle"
                          className="text-slate-500"
                          onClick={() => muteSession(callActive)}
                        />
                      </Tooltip>
                    </div>
                  </Col>

                  <Col className="gutter-row" span={3}>
                    <div className="flex w-full flex-col items-center  text-xs text-slate-400">
                      <Button
                        icon={<MessageOutlined />}
                        size="middle"
                        className="text-slate-500"
                        onClick={() =>
                          receiverInfo?.uid &&
                          handleOpenMgsDrawer(receiverInfo?.uid)
                        }
                        disabled={
                          receiverInfo?.uid &&
                          callActive?.statusAppel !== "ringing"
                            ? false
                            : true
                        }
                      />
                    </div>
                  </Col>

                  <Col className="gutter-row" span={3}>
                    <div className="flex w-full flex-col items-center text-xs text-slate-400">
                      <Tooltip
                        key="voip-nouvelAppel"
                        placement="bottom"
                        title={t("voip.nouvelAppel")}
                      >
                        <Popover
                          key="voip-nouvelAppel"
                          overlayClassName={
                            !callActive?.established ? "hidden" : " "
                          }
                          title={t("voip.nouvelAppel")}
                          // placement="topRight"
                          content={
                            <div className=" w-39">
                              <Row gutter={[2, 2]}>
                                <AutoCompleteForCall
                                  callBack={(e) =>
                                    setInputState((prev) => ({
                                      ...prev,
                                      newCall: {
                                        open: true,
                                        value: e,
                                      },
                                    }))
                                  }
                                  onSubmit={onSubmit}
                                  type={"newCall"}
                                />
                              </Row>
                            </div>
                          }
                          open={inputState.newCall?.open}
                          onOpenChange={(open) => {
                            setInputState((prev) => ({
                              ...prev,
                              newCall: {
                                ...prev.newCall,
                                open,
                              },
                            }));
                          }}
                          trigger={["click"]}
                        >
                          <Button
                            icon={
                              <BsTelephonePlus
                                className="mt-1 text-slate-500"
                                style={{
                                  fontSize: "16px",
                                }}
                              />
                            }
                            disabled={
                              // !callActive?.established ||
                              (attendedTransfer && isAttendedTransfer) ||
                              callActive?.statusAppel === "ringing"
                            }
                            size="middle"
                            className="text-slate-400"
                          />
                        </Popover>
                      </Tooltip>
                    </div>
                  </Col>
                </div>
                <div className="flex w-full items-center  justify-between space-x-3">
                  <Col className="gutter-row" span={3}>
                    <div className="flex w-full flex-col items-center text-xs text-slate-400">
                      <Tooltip
                        key="voip-transfert"
                        placement="bottom"
                        title={t("voip.transfert")}
                      >
                        <Popover
                          key="voip-transfert"
                          title={t("voip.transfert")}
                          placement="topRight"
                          //   title="Clavier"

                          open={inputState.transfert.open}
                          onOpenChange={(open) => {
                            setInputState((prev) => ({
                              ...prev,
                              transfert: {
                                ...prev.transfert,
                                open,
                              },
                            }));
                          }}
                          content={
                            <div className="w-39">
                              <Space direction="vertical">
                                <Radio.Group
                                  defaultValue="blind"
                                  buttonStyle="solid"
                                  onChange={(e) =>
                                    setIsAttendedTransfer(
                                      e.target.value === "attended"
                                    )
                                  }
                                >
                                  <Space direction="vertical">
                                    <Radio value="blind">
                                      {t("voip.blind")}
                                    </Radio>
                                    <Radio value="attended">
                                      {t("voip.attended")}
                                    </Radio>
                                  </Space>
                                </Radio.Group>
                                <Row gutter={[2, 2]}>
                                  <AutoCompleteForCall
                                    callBack={(e) =>
                                      setInputState((prev) => ({
                                        ...prev,
                                        transfert: {
                                          open: true,
                                          value: e,
                                        },
                                      }))
                                    }
                                    onSubmit={onSubmit}
                                    type={"transfert"}
                                    isAttendedTransfer={isAttendedTransfer}
                                  />
                                </Row>
                              </Space>
                            </div>
                          }
                          trigger="click"
                        >
                          <Button
                            disabled={
                              (isAttendedTransfer && attendedTransfer) ||
                              !callActive?.established ||
                              callActive?.statusAppel === "ringing"
                            }
                            icon={<SwapOutlined />}
                            size="middle"
                            className="text-slate-500"
                          />
                        </Popover>
                      </Tooltip>
                    </div>
                  </Col>
                  <Col className="gutter-row" span={3}>
                    <div className="flex w-full flex-col items-center text-xs text-slate-400">
                      <Tooltip key={"DTMF"} title="DTMF">
                        <Popover
                          key={"DTMF"}
                          placement="topRight"
                          //   title="Clavier"
                          content={
                            <div className="w-32">
                              <Row gutter={[2, 2]}>
                                {dtmfNumber.map((item) => (
                                  <Col
                                    key={`dtmf_${item}`}
                                    className="gutter-row"
                                    span={8}
                                  >
                                    <div style={style}>
                                      <Button
                                        //   onKeyUp={stopToneDTMF}
                                        // onKeyDown={() =>
                                        //   playToneDtmfFunction(item)
                                        // }
                                        type="text"
                                        size="large"
                                        block
                                        onClick={() => selectDTMF(item)}
                                      >
                                        {item}
                                      </Button>
                                    </div>
                                  </Col>
                                ))}
                              </Row>
                            </div>
                          }
                          trigger="click"
                        >
                          <Button
                            icon={<HolderOutlined />}
                            size="middle"
                            className="text-slate-500"
                            disabled={callActive?.statusAppel === "ringing"}
                          />
                        </Popover>
                      </Tooltip>
                    </div>
                  </Col>
                  <Col className="gutter-row" span={3}>
                    <div className="flex w-full flex-col items-center text-xs text-slate-400">
                      <Tooltip
                        key="voip-conférence"
                        placement="bottom"
                        title={t("voip.conférence")}
                      >
                        <Popover
                          key="voip-conférence"
                          placement="topRight"
                          title={t("voip.conférence")}
                          content={
                            <div className="w-39">
                              <Row gutter={[2, 2]}>
                                <AutoCompleteForCall
                                  callBack={(e) =>
                                    setInputState((prev) => ({
                                      ...prev,
                                      conf: {
                                        open: true,
                                        value: e,
                                      },
                                    }))
                                  }
                                  onSubmit={onSubmit}
                                  type={"conf"}
                                />
                              </Row>
                            </div>
                          }
                          trigger={["click"]}
                          open={inputState.conf.open}
                          onOpenChange={(open) => {
                            setInputState((prev) => ({
                              ...prev,
                              conf: {
                                ...prev.conf,
                                open,
                              },
                            }));
                          }}
                        >
                          <Button
                            icon={<TeamOutlined />}
                            size="middle"
                            className="text-slate-500"
                            disabled={
                              (isAttendedTransfer && attendedTransfer) ||
                              callActive?.statusAppel === "ringing" ||
                              session.some(
                                (item) => item.statusAppel === "holding"
                              )
                            }
                          />
                        </Popover>
                      </Tooltip>
                    </div>
                  </Col>
                  <Col className="gutter-row" span={3}>
                    <div className="flex w-full flex-col items-center text-xs text-slate-400">
                      <Tooltip title={t("voip.taskCall")}>
                        <Button
                          icon={<CalendarOutlined />}
                          size="middle"
                          className="text-slate-500"
                          disabled={
                            callActive?.statusAppel === "ringing" ||
                            access.activities !== "1"
                          }
                          onClick={() => handleActionWebPhone("create_task")}
                        />
                      </Tooltip>
                    </div>
                  </Col>
                </div>
              </div>
              <Button
                type="primary"
                shape="circle"
                size="middle"
                danger
                disabled={!callActive?.established}
                icon={
                  <HiPhone
                    style={{
                      fontSize: 22,
                      transform: "rotate(135deg)",
                      marginTop: 2,
                    }}
                  />
                }
                onClick={() => hungUp(callActive)}
              />
            </>
          ) : (
            (callActive?.conf || confInfo) && (
              <>
                {timer && timer !== "--:--" && (
                  <time className="font-semibold text-slate-400">
                    <Tag bordered={false} color="success">
                      {timer}
                    </Tag>
                  </time>
                )}
                <div className="relative flex w-full justify-around">
                  <div className=" w-[45%] space-y-2">
                    <div className="text-center">
                      <Badge
                        count={confInfo ? "Mod" : 0}
                        size="small"
                        color="blue"
                        offset={[-30, 52]}
                        style={{ fontWeight: 600 }}
                      >
                        <DisplayAvatar
                          urlImg={receiverInfo?.image}
                          name={receiverInfo?.name}
                          icon={
                            receiverInfo?.extension === "sphere_visio" &&
                            receiverInfo?.image
                          }
                          size={60}
                        />
                        {/* <AvatarChat
                          name={
                            receiverInfo?.name
                              ? getName(receiverInfo?.name, "avatar")
                              : "?"
                          }
                          url={receiverInfo?.image}
                          size={60}
                          hasImage={receiverInfo?.image}
                        /> */}
                      </Badge>
                    </div>
                    <div className="flex flex-col text-center text-sm ">
                      <span className="truncate font-semibold leading-5">
                        {receiverInfo?.name || receiverInfo?.number}
                        {/* hassine Ben Ali Ben Hassine Turki Basla */}
                      </span>
                      <span className="leading-4 text-slate-500">
                        {receiverInfo?.number}
                      </span>
                    </div>

                    <div className="flex flex-col items-center space-y-1">
                      <div className="flex flex-row items-center space-x-2">
                        <Button
                          className="text-slate-500"
                          shape="circle"
                          size="middle"
                          icon={
                            callActive?.statusAppel === "holding" ? (
                              <CaretRightOutlined />
                            ) : (
                              <PauseOutlined />
                            )
                          }
                          onClick={() => holdSession(callActive)}
                        />

                        <Button
                          className="text-slate-500"
                          shape="circle"
                          size="middle"
                          icon={
                            callActive?.ismute === "yes" ? (
                              <AudioMutedOutlined />
                            ) : (
                              <AudioOutlined />
                            )
                          }
                          onClick={() => muteSession(callActive)}
                        />
                      </div>

                      <Button
                        type="primary"
                        shape="circle"
                        danger
                        icon={
                          <HiPhone
                            style={{
                              fontSize: 20,
                              transform: "rotate(135deg)",
                              marginTop: 2,
                            }}
                          />
                        }
                        onClick={() => hungUp(callActive)}
                      />
                    </div>
                  </div>

                  <div className=" w-[45%] space-y-2">
                    <div className=" text-center">
                      <DisplayAvatar
                        urlImg={
                          callActive?.conf?.receiverInfo?.image ||
                          confInfo?.image
                        }
                        name={
                          callActive?.conf?.receiverInfo?.name || confInfo?.name
                        }
                        icon={
                          receiverInfo?.extension === "sphere_visio" &&
                          receiverInfo?.image
                        }
                        size={60}
                      />
                      {/* <AvatarChat
                        name={
                          callActive?.conf?.receiverInfo?.name || confInfo?.name
                            ? getName(
                                callActive?.conf?.receiverInfo?.name ||
                                  confInfo?.name,
                                "avatar"
                              )
                            : "?"
                        }
                        url={
                          callActive?.conf?.receiverInfo?.image ||
                          confInfo?.image
                        }
                        size={60}
                        hasImage={
                          callActive?.conf?.receiverInfo?.image ||
                          confInfo?.image
                        }
                      /> */}
                    </div>
                    <div className="flex flex-col text-center text-sm ">
                      <span className="truncate font-semibold leading-5">
                        {callActive?.conf?.receiverInfo?.name
                          ? callActive?.conf?.receiverInfo?.name
                          : callActive?.conf?.receiverInfo?.number
                          ? callActive?.conf?.receiverInfo?.number
                          : confInfo?.name || confInfo?.number}
                      </span>
                      <span className="leading-4 text-slate-500">
                        {callActive?.conf?.receiverInfo?.number ||
                          confInfo?.number}
                      </span>
                    </div>

                    <div className="flex flex-col items-center space-y-1">
                      <div className="flex flex-row items-center space-x-2">
                        <Button
                          className="text-slate-500"
                          shape="circle"
                          size="middle"
                          icon={
                            callActive?.conf?.statusAppel === "holding" ? (
                              <CaretRightOutlined />
                            ) : (
                              <PauseOutlined />
                            )
                          }
                          onClick={() => holdSession(callActive?.conf)}
                          disabled={
                            !callActive?.conf?.established || !!confInfo
                          }
                        />

                        <Button
                          className=" text-slate-500"
                          shape="circle"
                          size="middle"
                          icon={
                            callActive?.conf?.ismute === "yes" ? (
                              <AudioMutedOutlined />
                            ) : (
                              <AudioOutlined />
                            )
                          }
                          onClick={() => muteSession(callActive?.conf)}
                          disabled={
                            !callActive?.conf?.established || !!confInfo
                          }
                          // disabled={callActive?.conf.statusAppel !== "ringing" ? false : true}
                        />
                      </div>
                      <Button
                        type="primary"
                        shape="circle"
                        size="middle"
                        danger
                        disabled={!callActive?.conf?.established || !!confInfo}
                        icon={
                          <HiPhone
                            style={{
                              fontSize: 20,
                              transform: "rotate(135deg)",
                              marginTop: 2,
                            }}
                          />
                        }
                        onClick={() => hungUp(callActive?.conf)}
                      />
                    </div>
                  </div>
                </div>
              </>
            )
          )}
        </>
      )}
    </div>
  );
}

export default CallPage;
