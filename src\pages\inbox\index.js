import { FilterTwoTone } from "@ant-design/icons";
import { <PERSON><PERSON>, <PERSON><PERSON>, Divider, Input, Layout, Spin, Typography } from "antd";
import React, { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { FiSearch } from "react-icons/fi";
import { useSelector } from "react-redux";
import MainService from "services/main.service";
import ListInteractionInInbox from "././ListInteractionInInbox";
import { formatDateComparison } from "pages/voip/helpers/helpersFunc";
import DetailsMessage from "pages/rmc/mailing/detailEmail/DetailsMessage";
import { setNumberEmailThread } from "new-redux/actions/mail.actions";
import { generateAxios } from "services/axiosInstance";
import { URL_ENV } from "index";
import FakeList from "./FakeList";
const { Content, Sider } = Layout;

const Inbox = () => {
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState([]);
  const [listBeforeChanged, setListBeforeChanged] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [lastPage, setLastPage] = useState(1);

  const [detailsMail, setDetailsMail] = useState([]);
  const { dataAccounts, numberEmailThread, refreshMailVueSphere } = useSelector(
    (state) => state.mailReducer
  );
  const [loadingDetails, setLoadingDetails] = useState(false);
  const [pageDetail, setPageDetail] = useState(0);

  const [selectedItem, setSelectedItem] = useState("");
  const [search, setSearch] = useState("");
  const [detailState, setDetailState] = useState(false);

  const { user } = useSelector((state) => state.user);
  const [t] = useTranslation("common");

  // useEffect(() => {
  //   const getLogs = async () => {
  //     setLoading(true);
  //     try {
  //       const { data } = await generateAxios(
  //         URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  //       ).get(`log-inbox?page=${currentPage}&limit=${20}&search=${search}`);
  //       setListBeforeChanged([
  //         ...listBeforeChanged,
  //         ...data.data.filter((el) => !el.user.includes("JOB")),
  //       ]);

  //       const filteredData = [
  //         ...listBeforeChanged,
  //         ...data.data.filter((el) => !el.user.includes("JOB")),
  //       ];
  //       // .filter(
  //       //   (el) =>
  //       //     el.type === "voip" ||
  //       //     el.type === "email" ||
  //       //     (el.family_id === 9 && el.type.includes("RMC"))
  //       //   // || el.type === "Deal"
  //       // );
  //       // Création d'un objet Map pour regrouper les éléments par date
  //       const groupedData = new Map();

  //       // Parcours du tableau de données
  //       filteredData.forEach((item) => {
  //         const date = item.date.split(" ")[0]; // Extraction de la date sans l'heure
  //         if (groupedData.has(date)) {
  //           groupedData.get(date).push(item);
  //         } else {
  //           groupedData.set(date, [item]);
  //         }
  //       });

  //       // Conversion du Map en tableau de groupes
  //       const groupedArray = Array.from(groupedData, ([date, items]) => ({
  //         date,
  //         items,
  //       }));
  //       setList(groupedArray);

  //       setCurrentPage(data.meta.current_page);
  //       setLastPage(data.meta.last_page);
  //       setLoading(false);
  //     } catch (err) {
  //       setLoading(false);
  //     }
  //   };
  //   getLogs();
  // }, [currentPage]);
  const getDetailsMessageInbox = useCallback(
    async (idDetails, accountId) => {
      var formData = new FormData();
      formData.append("id", idDetails);
      formData.append("accountId", accountId);
      formData.append("folderMailing", "inbox");
      formData.append("limit", "5");
      if (numberEmailThread <= 5) {
        formData.append("first_id_email", "");
        formData.append("last_id_email", "");
      }
      if (numberEmailThread > 5) {
        formData.append("nbrEmails", numberEmailThread);
      }

      try {
        setLoadingDetails(true);
        setDetailsMail([]);
        let response;

        if (numberEmailThread > 5) {
          response = await MainService.getDetailsInboxFirstLast(formData);
        } else {
          response = await MainService.getDetailsInbox(1, formData);
        }
        if (response.status === 200) {
          setDetailsMail(response?.data);
          setLoadingDetails(false);
        }
      } catch (error) {
        setLoadingDetails(false);
        console.log(error);
      }
    },
    [selectedItem?.data?.account_id, refreshMailVueSphere]
  );

  // useEffect(() => {
  //   if (selectedItem?.id_data && selectedItem?.family_id === "email") {
  //     getDetailsMessageInbox(
  //       selectedItem?.data?.email_id,
  //       selectedItem?.data?.account_id
  //     );
  //     setDetailState(true);
  //     // dispatch(setNumberEmailThread(1));
  //   }
  // }, [selectedItem?.data?.email_id, selectedItem?.family_i]);
  useEffect(() => {
    if (selectedItem === "email") {
      getDetailsMessageInbox("67a4c1a2b3699fe616041b36", 2);
      setDetailState(true);
      // dispatch(setNumberEmailThread(1));
    }
  }, [selectedItem]);

  return (
    <>
      <Spin spinning={false}>
        <Layout>
          <Sider
            width="360px"
            style={{
              background: "white",
              //   padding: "12px 0px",
              borderRight: "1px solid #e8e8e8",
              height: "calc(100vh - 58px)",
              // marginTop: "-20px",
            }}
          >
            <div className="flex flex-col gap-2 p-2">
              <Typography.Title level={4}>
                {t("vue360.listOfInteractions")}
              </Typography.Title>
              <div className="flex items-center justify-between gap-x-2">
                <Input
                  prefix={<FiSearch className="text-slate-500" />}
                  placeholder={t("table.search")}
                  //   value={search.trimStart()}
                  onChange={(e) => setSearch(e.target.value)}
                />
                <Button
                  type="text"
                  shape="circle"
                  icon={
                    <Badge dot={0}>
                      <FilterTwoTone />
                    </Badge>
                  }
                  //   onClick={() => setOpenPopover(true)}
                />
              </div>
            </div>
            <Divider style={{ margin: "12px 0" }} />
            <FakeList
              setSelectedItem={setSelectedItem}
              selectedItem={selectedItem}
            />
            {/* <ListInteractionInInbox
              list={list}
              setSelectedItem={setSelectedItem}
              selectedItem={selectedItem}
              setCurrentPage={setCurrentPage}
              lastPage={lastPage}
              loading={loading}
            /> */}
          </Sider>
          <Layout
            style={{
              // padding: "0 24px 24px",

              background: "white",
            }}
          >
            <Content
              style={{
                margin: 0,
                minHeight: 280,
                maxHeight: `calc(100vh - 60px)`,
                background: "#********",
                overflow: "auto",
              }}
            >
              {selectedItem === "email" ? (
                // <div style={{ height: `calc(100vh - 60px)`, overflow: "auto" }}>
                <DetailsMessage
                  messageDetails={detailsMail}
                  getDetailsMessageInbox={getDetailsMessageInbox}
                  ListAccounts={dataAccounts}
                  detailsMail={detailsMail}
                  setDetailsMail={setDetailsMail}
                  page={pageDetail}
                  setPage={setPageDetail}
                  accountId360={"2" || selectedItem?.data?.account_id}
                  idEmail360={
                    "67a4c1a2b3699fe616041b36" || selectedItem?.id_data
                  }
                  // setModalOpen={setModalOpen}
                  // openEditor={openEditor}
                  // setopenEditor={setopenEditor}
                  // openTasksDrawer={openTasksDrawer}
                  // setOpenTasksDrawer={setOpenTasksDrawer}
                  loadingDetails={loadingDetails}
                  type="360"
                  setDetailState={setDetailState}
                />
              ) : // </div>
              selectedItem === "whatsapp" ? (
                <iframe
                  src={`${URL_ENV?.REACT_APP_RMC_URL}?conversation=11068&vue=360`}
                  title="chat"
                  display="block"
                  width="100%"
                  // height= {`${deviceHeight}px -120px`}
                  sendbox="allow-same-origin allow-popups"
                  allowfullscreen="true"
                  style={{
                    height: `calc(100vh - 60px)`,
                    border: "none",
                  }}
                  allowtransparency="true"
                  // onLoad={() => setHide(true)}
                ></iframe>
              ) : null}
              {/* {selectedItem?.id_data && selectedItem?.family_id === "email" ? (
                <DetailsMessage
                  messageDetails={detailsMail}
                  getDetailsMessageInbox={getDetailsMessageInbox}
                  ListAccounts={dataAccounts}
                  detailsMail={detailsMail}
                  setDetailsMail={setDetailsMail}
                  page={pageDetail}
                  setPage={setPageDetail}
                  accountId360={selectedItem?.data?.account_id}
                  idEmail360={selectedItem?.id_data}
                  // setModalOpen={setModalOpen}
                  // openEditor={openEditor}
                  // setopenEditor={setopenEditor}
                  // openTasksDrawer={openTasksDrawer}
                  // setOpenTasksDrawer={setOpenTasksDrawer}
                  loadingDetails={loadingDetails}
                  type="360"
                  setDetailState={setDetailState}
                />
              ) : selectedItem?.id_data && selectedItem?.family_id === 9 ? (
                <iframe
                  src={`${URL_ENV?.REACT_APP_RMC_URL}?conversation=${selectedItem?.data?.conversation_id}&vue=360`}
                  title="chat"
                  display="block"
                  width="100%"
                  // height= {`${deviceHeight}px -120px`}
                  sendbox="allow-same-origin allow-popups"
                  allowfullscreen="true"
                  style={{
                    height: `calc(100vh - 60px)`,
                    border: "none",
                  }}
                  allowtransparency="true"
                  // onLoad={() => setHide(true)}
                ></iframe>
              ) : null} */}
            </Content>
          </Layout>
        </Layout>
      </Spin>
    </>
  );
};

export default Inbox;
