import React, {
  useState,
  useEffect,
  useCallback,
  ReactNode,
  useRef,
  useLayoutEffect,
} from "react";
import { Editor, Range, Extension } from "@tiptap/core";
import Suggestion from "@tiptap/suggestion";
import { ReactRenderer } from "@tiptap/react";
import tippy from "tippy.js";
import {
  Bold,
  Heading1,
  Heading2,
  Heading3,
  Image,
  ImageIcon,
  Italic,
  List,
  ListOrdered,
  ListTodo,
  MessageSquarePlus,
  Table,
  Table2,
  Text,
} from "lucide-react";
import { Button, message } from "antd";
import { Plugin, PluginKey } from "prosemirror-state";
import { Decoration, DecorationSet } from "prosemirror-view";
import { useTranslation } from "react-i18next";
import i18next from "i18next";
const Command = Extension.create({
  name: "slash-command",
  addOptions() {
    return {
      suggestion: {
        char: "/",
        command: ({ editor, range, props }) => {
          props.command({ editor, range });
        },
      },
    };
  },
  addProseMirrorPlugins() {
    return [
      Suggestion({
        editor: this.editor,
        ...this.options.suggestion,
      }),
    ];
  },
});

const handleImageChange = (file, editor) => {
  let filesize = (file.size / 1024 / 1024).toFixed(4);
  if (!file.type.includes("image")) {
    message.open({
      type: "error",
      content: i18next.t("common:chat.bot.acceptedOnlyImageType"),
    });
  }
  if (file && file.type.includes("image") && filesize < 10) {
    const reader = new FileReader();
    reader.onloadend = () => {
      if (typeof reader.result === "string") {
        editor.commands.setImage({
          src: reader.result,
        });
      }
    };
    reader.readAsDataURL(file);
  }
};
const getSuggestionItems = ({ query }) => {
  return [
    {
      title: "Heading 1",
      description: "Big section heading.",
      icon: <Heading1 size={18} />,
      command: ({ editor, range }) => {
        editor
          .chain()
          .focus()
          .deleteRange(range)
          .setNode("heading", { level: 1 })
          .run();
      },
    },
    {
      title: "Heading 2",
      description: "Medium section heading.",
      icon: <Heading2 size={18} />,
      command: ({ editor, range }) => {
        editor
          .chain()
          .focus()
          .deleteRange(range)
          .setNode("heading", { level: 2 })
          .run();
      },
    },
    {
      title: "Heading 3",
      description: "Small section heading.",
      icon: <Heading3 size={18} />,
      command: ({ editor, range }) => {
        editor
          .chain()
          .focus()
          .deleteRange(range)
          .setNode("heading", { level: 3 })
          .run();
      },
    },
    {
      title: "Text",
      description: "Just start typing with plain text.",
      icon: <Text size={18} />,
      command: ({ editor, range }) => {
        editor
          .chain()
          .focus()
          .deleteRange(range)
          .toggleNode("paragraph", "paragraph")
          .run();
      },
    },
    {
      title: "Bold",
      description: "Make text bold.",
      icon: <Bold size={18} />,
      command: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).setMark("bold").run();
      },
    },

    {
      title: "Italic",
      description: "Make text italic.",
      icon: <Italic size={18} />,
      command: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).setMark("italic").run();
      },
    },
    {
      title: "Bullet List",
      description: "Create a simple bullet list.",
      icon: <List size={18} />,
      command: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).toggleBulletList().run();
      },
    },
    {
      title: "Numbered List",
      description: "Create a list with numbering.",
      icon: <ListOrdered size={18} />,
      command: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).toggleOrderedList().run();
      },
    },
    {
      title: "Image",
      description: "Upload an image from your computer.",
      icon: <ImageIcon size={18} />,
      command: ({ editor, range }) => {
        editor.chain().focus().deleteRange(range).run();
        // upload image
        const input = document.createElement("input");
        input.type = "file";
        input.accept = "image/*";
        input.onchange = async (event) => {
          if (input.files?.length) {
            const file = input.files[0];
            return handleImageChange(file, editor);
          }
        };
        input.click();
      },
    },
  ].filter((item) => {
    if (typeof query === "string" && query.length > 0) {
      return item.title.toLowerCase().includes(query.toLowerCase());
    }
    return true;
  });
  // .slice(0, 10);
};

export const updateScrollView = (container, item) => {
  const containerHeight = container.offsetHeight;
  const itemHeight = item ? item.offsetHeight : 0;

  const top = item.offsetTop;
  const bottom = top + itemHeight;

  if (top < container.scrollTop) {
    container.scrollTop -= container.scrollTop - top + 5;
  } else if (bottom > containerHeight + container.scrollTop) {
    container.scrollTop += bottom - containerHeight - container.scrollTop + 5;
  }
};

const CommandList = ({ items, command, editor }) => {
  const [selectedIndex, setSelectedIndex] = useState(0);
  const commandListContainer = useRef(null);
  const selectedButtonRef = useRef(null);
  const fileInputRef = useRef(null);
  const selectItem = useCallback(
    (index) => {
      const item = items[index];
      if (item) {
        command(item);
      }
    },
    [command, items]
  );

  useEffect(() => {
    const navigationKeys = ["ArrowUp", "ArrowDown", "Enter"];
    const onKeyDown = (e) => {
      if (navigationKeys.includes(e.key)) {
        e.preventDefault();
        if (e.key === "ArrowUp") {
          setSelectedIndex((selectedIndex + items.length - 1) % items.length);
          return true;
        }
        if (e.key === "ArrowDown") {
          setSelectedIndex((selectedIndex + 1) % items.length);
          return true;
        }
        if (e.key === "Enter") {
          selectItem(selectedIndex);
          return true;
        }
        return false;
      }
    };
    document.addEventListener("keydown", onKeyDown);
    return () => {
      document.removeEventListener("keydown", onKeyDown);
    };
  }, [items, selectedIndex, setSelectedIndex, selectItem]);

  useEffect(() => {
    setSelectedIndex(0);
  }, [items]);

  const handleIconClick = () => {
    fileInputRef.current?.click();
  };
  // useEffect(() => {
  //   const container = commandListContainer.current;
  //   const item = selectedButtonRef.current;

  //   if (item && container) {
  //     container.scrollTop = item.offsetTop - container.offsetTop;

  //     item.focus();
  //   }

  //   if (selectedIndex === 0 && items.length > 0) {
  //     setTimeout(() => {
  //       selectedButtonRef.current?.focus();
  //     }, 10);
  //   }
  // }, [selectedIndex, items]);

  return items.length > 0 ? (
    <div
      ref={commandListContainer}
      className="tippyEmail z-50 h-auto w-72 space-y-2 overflow-y-auto scroll-smooth rounded-md border border-gray-200 bg-white px-1  py-2 transition-all"
    >
      {items.map((itemItemProps, index) => {
        const isSelected = index === selectedIndex;
        return (
          <Button
            ref={isSelected ? selectedButtonRef : null}
            className={`flex h-full w-full items-center space-x-2 rounded-md px-2  text-left text-sm text-gray-900 hover:bg-gray-100 ${
              isSelected ? "bg-gray-100 text-gray-900" : ""
            }`}
            type="text"
            key={index}
            onClick={() => selectItem(index)}
          >
            <div className="flex  items-center justify-center rounded-md border border-gray-200 bg-white">
              {itemItemProps.icon}
            </div>
            <div>
              <p className="font-medium">{itemItemProps.title}</p>
              <p className="text-xs text-gray-500">
                {itemItemProps.description}
              </p>
            </div>
          </Button>
        );
      })}
    </div>
  ) : null;
};

const renderItems = () => {
  let component = null;
  let popup = null;
  let localProps;
  return {
    onStart: (props) => {
      localProps = { ...props, event: "" };
      component = new ReactRenderer(CommandList, {
        props,
        editor: props.editor,
      });

      // @ts-ignore
      popup = tippy("body", {
        getReferenceClientRect: localProps.clientRect,
        appendTo: () => document.body,
        content: component.element,
        showOnCreate: true,
        interactive: true,
        trigger: "manual",
        placement: "bottom-start",
        animation: "shift-toward-subtle",
        duration: 250,
      });
    },
    onUpdate: (props) => {
      component?.updateProps(props);

      popup &&
        popup[0].setProps({
          getReferenceClientRect: props.clientRect,
        });
    },
    onKeyDown: (props) => {
      if (props.event.key === "Escape") {
        popup?.[0].hide();

        return true;
      }

      // @ts-ignore
      return component?.ref?.onKeyDown(props);
    },
    onExit: () => {
      popup?.[0].destroy();
      component?.destroy();
    },
  };
};

const SlashCommand = Command.configure({
  suggestion: {
    items: getSuggestionItems,
    render: renderItems,
  },
});

export default SlashCommand;
