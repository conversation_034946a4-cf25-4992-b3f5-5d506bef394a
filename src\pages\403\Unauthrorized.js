import { Button, Result } from "antd";
import { useTranslation } from "react-i18next";
import { Link, useLocation } from "react-router-dom";

const Unauthorized = () => {
  const location = useLocation();
  const { from } = location.state || { from: { pathname: "/dashboard" } };
  //console.log(from);
  const [t] = useTranslation("common");

  return (
    <Result
      status="403"
      title="403"
      subTitle={t("unauthorizedSubtitle")}
      extra={
        <Link to="/dashboard" replace>
          <Button type="primary">{t("BackToHome")}</Button>
        </Link>
      }
    />
  );
};
export default Unauthorized;
