import { memo } from "react";
import { Divider, List, Space, Tag } from "antd";
import { HardDrive } from "lucide-react";
import DisplayAvatar from "pages/voip/components/DisplayAvatar";
import { renderHighlight, renderTitle } from ".";
import { getIcon } from "pages/drive/drive.component";
import { humanDate } from "pages/voip/helpers/helpersFunc";

const RenderDriveItem = ({ t, item, imgBaseUrl, handleClickOnItem }) => {
  //
  const {
    id,
    name,
    type,
    source,
    extension: fileExtension,
    size,
    path_array,
    created_at,
    created_by,
    updated_at,
    highlight,
    thumbnail_url,
  } = item;
  //
  return (
    <List.Item
      className="custom-list-item-global-search"
      style={{
        alignItems: "center",
      }}
      key={id}
      onClick={() => handleClickOnItem(`/drive?id=${id}`)}
    >
      <List.Item.Meta
        avatar={
          <DisplayAvatar
            size={44}
            urlImg={thumbnail_url}
            icon={!thumbnail_url && getIcon({ type }, 24)}
          />
        }
        title={renderTitle(name, "Drive")}
        description={
          <div className="flex flex-col space-y-0.5">
            <Space size={2} split={<Divider type="vertical" />}>
              <p>{`${t("contacts.createdAt")} : ${humanDate(
                created_at,
                t,
                "table"
              )}`}</p>
              {!!updated_at && (
                <p>{`${t("contacts.updatedAt")} : ${humanDate(
                  updated_at,
                  t,
                  "table"
                )}`}</p>
              )}
            </Space>
            <Space size={2} split={<Divider type="vertical" />}>
              <Space size={2}>
                <p>{t("globalSearch.owner")}</p>
                <DisplayAvatar
                  cursor="help"
                  tooltip={true}
                  size={20}
                  name={created_by?.label}
                  urlImg={
                    !!created_by?.avatar && `${imgBaseUrl}${created_by?.avatar}`
                  }
                />
              </Space>
              {source === "shared" && (
                <Tag
                  bordered={false}
                  style={{ fontSize: 12, paddingLeft: 2, paddingRight: 2 }}
                >
                  {t("globalSearch.shared")}
                </Tag>
              )}
              {path_array.length ? (
                <p>
                  {t("drive.path")}: {path_array.map((path) => `${path}/`)}
                </p>
              ) : null}
              {!!size && (
                <p>
                  {t("drive.size")}: {size}
                </p>
              )}
            </Space>
            {renderHighlight(highlight)}
          </div>
        }
      />
    </List.Item>
  );
};

export default memo(RenderDriveItem);
