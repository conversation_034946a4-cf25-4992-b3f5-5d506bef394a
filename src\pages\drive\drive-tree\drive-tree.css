.drive-tree .ant-tree-node-content-wrapper {
width: 100%;
  border-radius: 4px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
}

.drive-tree .ant-tree-node-content-wrapper:hover {
  background-color: transparent;
}
.drive-tree .ant-tree-node-content-wrapper.ant-tree-node-selected {
  background-color: transparent;

}

.drive-tree .ant-tree-treenode.ant-tree-treenode-selected:not(.ant-tree-treenode-leaf-last:not(.ant-tree-treenode-switcher-close,  .ant-tree-treenode-switcher-open)) {
  background-color: rgb(219, 234, 254);
  border-radius: 4px;
  
}
.drive-tree .ant-tree-node-content-wrapper.ant-tree-node-content-wrapper-close.ant-tree-node-selected .ant-tree-iconEle.ant-tree-icon__customize  {
  color: #243fff;
}
.drive-tree .ant-tree-node-content-wrapper.ant-tree-node-content-wrapper-open.ant-tree-node-selected .ant-tree-iconEle.ant-tree-icon__customize  {
  color: #243fff;
}
.drive-tree .ant-tree-node-content-wrapper.ant-tree-node-content-wrapper-normal.ant-tree-node-selected .ant-tree-iconEle.ant-tree-icon__customize  {
  color: #243fff;
}
.drive-tree .ant-tree-title {
  font-size: 14px;
  margin-left: 8px;
}

.drive-tree .ant-tree-switcher {
  width: 20px;
  height: 20px;
}
.drive-tree .ant-tree-treenode{
  width: 100%;
}
.drive-tree .ant-tree-treenode:hover {
  background-color: rgba(0, 0, 0, 0.04);
}
.drive-tree .ant-tree-indent-unit {
  width: 20px;
}

.drive-tree-container {
  height: 100%;
  overflow-y: auto;
}

.drive-files-container {
  height: 100%;
  overflow-y: auto;
}

.drive-tree-header {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.drive-files-header {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

/* Context menu styling */
.tree-node-title {
  display: inline-block;
  width: 100%;
  cursor: pointer;
  user-select: none;
}

.tree-node-title:hover {
  background-color: transparent;
}

