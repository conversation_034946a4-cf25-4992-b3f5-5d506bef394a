import { useSelector } from "react-redux";

const useAvatar = (userId) => {
  const currentUser = useSelector((state) => state?.user?.user);
  const users = useSelector((state) => state?.chat?.userList);

  if (userId == currentUser.id) {
    return {
      avatar: currentUser.avatar,
      name: currentUser.label,
      isYou: true,
    };
  } else {
    //search for the user in users list with the id
    const user = users.find((user) => user.uuid == userId);

    return {
      avatar: user?.image,
      name: user?.name,
      isYou: false,
    };
  }
};

export default useAvatar;
