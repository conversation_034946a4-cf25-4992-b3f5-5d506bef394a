import React, {
  Suspense,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import MainService from "../../../services/main.service";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import {
  convertToPlain,
  getName,
} from "../../layouts/chat/utils/ConversationUtils";
import {
  Avatar,
  Button,
  Card,
  Divider,
  Empty,
  Image,
  Skeleton,
  Space,
  Spin,
  Typography,
} from "antd";
import {
  formatDateComparison,
  humanDate,
} from "../../voip/helpers/helpersFunc";
import { useTranslation } from "react-i18next";
import axios from "axios";
import InputChat from "../../layouts/chat/conversation/input";
import { ArrowLeftOutlined, ArrowRightOutlined } from "@ant-design/icons";
import { useSelector } from "react-redux";
import { moment_timezone } from "../../../App";
import InputRmc from "./InputRmc";
import ShowFileList from "../../layouts/chat/conversation/input/FileInput/ShowFileList";
import { toastNotification } from "../../../components/ToastNotification";

const ChatRmcWithoutInput = ({
  idLead = "",
  dataSteps,
  setLoadChangeConv = () => {},
  guest = true,
  from = "",
  headerHeight,
  contactInfo,
}) => {
  const [t] = useTranslation("common");
  const { activeTab360 } = useSelector((state) => state?.vue360);
  const [messages, setMessages] = useState([]);
  const [details, setDetails] = useState({});
  const [load, setLoad] = useState(false);
  const [isMessageSent, setIsMessageSent] = useState(false);
  const [fileList, setFileList] = useState([]);
  const [fileFromPase, setFileFromPaste] = useState(null);
  const [cancel, setCancel] = useState(null);
  const [textInput, setTextInput] = useState("");
  const [loadSend, setLoadSend] = useState(false);
  const [heightInput, setHeightInput] = useState(58);

  const { id } = useParams();
  const containerRef = useRef(null);
  const divRef = useRef(null);
  const location = useLocation();
  useEffect(() => {
    const observer = new ResizeObserver((entries) => {
      if (entries && entries[0] && entries[0].target) {
        const height = entries[0].target.offsetHeight;
        setHeightInput(height);
      }
    });

    if (divRef.current) {
      observer.observe(divRef.current);
    }

    return () => {
      if (divRef.current) {
        observer.unobserve(divRef.current);
      }
    };
  }, [divRef.current?.offsetHeight]);
  function areMessagesEqual(message1, message2) {
    return (
      moment_timezone(message1.creation_time).format("YYYY-MM-DD HH:mm") ===
        moment_timezone(message2.creation_time).format("YYYY-MM-DD HH:mm") &&
      message1.user_id === message2.user_id
    );
  }
  function isImagePath(value) {
    const imageExtensions = /\.(jpe?g|png|gif|bmp|svg)$/i;

    return imageExtensions.test(value);
  }
  function regrouperMessages(messages) {
    let groupedMessages = [];
    for (let i = 0; i < messages.length; i++) {
      let currentMessage = messages[i];
      let group = currentMessage.message.split("\n").map((el, index) => ({
        ...currentMessage,
        message: el,
        attachments:
          index == currentMessage.message.split("\n").length - 1
            ? currentMessage.attachments
            : "",
      }));

      // Parcourir les messages suivants avec le même user_id et la même date, heure, minute
      for (let j = i + 1; j < messages.length; j++) {
        let nextMessage = messages[j];
        if (areMessagesEqual(currentMessage, nextMessage)) {
          group.push(
            ...nextMessage.message.split("\n").map((el, index) => ({
              ...nextMessage,
              message: el,
              attachments:
                index == nextMessage.message.split("\n").length - 1
                  ? nextMessage.attachments
                  : "",
            }))
          );
          i = j; // Mettre à jour l'index pour éviter de revisiter les messages déjà regroupés
        } else {
          break; // Arrêter la recherche si les messages ne correspondent pas
        }
      }
      groupedMessages.push(group);
    }

    return groupedMessages;
  }
  const onRemoveFile = async (item) => {
    setCancel(item.uid);
    setFileList((prev) => prev.filter((f) => f.uid !== item.uid));

    await MainService.deleteFile("EXTERNE", item.file_name).catch(() => {
      setFileList((prev) => [...prev, item]);
      toastNotification("error", t("toasts.errorFetchApi"), "topRight");
    });
  };
  const scrollToLastElement = () => {
    if (containerRef.current) {
      containerRef.current.scrollIntoView();
    }
  };
  useEffect(() => {
    const getChatRmc360 = async () => {
      setLoadChangeConv(true);

      setLoad(true);
      let formdata = new FormData(); //formdata object
      formdata.append("token", "c1adab37bcb62f2a7f99b675c2ad6c0fdc50b51e");
      formdata.append("function", "get-conversation");
      formdata.append("user_id", "1470");
      formdata.append("conversation_id", "1285");
      try {
        const res = await MainService.getChatRmc360({
          id: id || contactInfo?.id,
        });
        // const res = await axios.post(
        //   "https://rmcdemo.comunikcrm.info/comuniksocial/include/api.php",
        //   formdata
        // );
        setDetails(res.data.response.details);
        const groupedMessages = {};
        res.data.response.messages.forEach((message) => {
          const date = message.creation_time.split(" ")[0]; // Extraire la date de création
          if (groupedMessages[date]) {
            groupedMessages[date].push(message); // Ajouter le message au tableau existant
          } else {
            groupedMessages[date] = [message]; // Créer un nouveau tableau avec le premier message
          }
        });

        setMessages(
          Object.entries(groupedMessages).map((el) => [
            el[0],
            regrouperMessages(el[1]),
          ])
        );
        // Résultat final : un tableau de tableaux contenant les messages regroupés
        setLoadChangeConv(false);
        setLoad(false);
        scrollToLastElement();
      } catch (err) {
        setLoad(false);
        setLoadChangeConv(false);
      }
    };
    const getListRmc = async () => {
      setLoadChangeConv(true);

      setLoad(true);
      try {
        const res = await MainService.getChatRmc360({
          id: idLead,
        });
        // const res = await axios.post(
        //   "https://rmcdemo.comunikcrm.info/comuniksocial/include/api.php",
        //   formdata
        // );
        setDetails(res.data.response.details);
        const groupedMessages = {};
        res.data.response.messages.forEach((message) => {
          const date = message.creation_time.split(" ")[0]; // Extraire la date de création
          if (groupedMessages[date]) {
            groupedMessages[date].push(message); // Ajouter le message au tableau existant
          } else {
            groupedMessages[date] = [message]; // Créer un nouveau tableau avec le premier message
          }
        });

        setMessages(
          Object.entries(groupedMessages).map((el) => [
            el[0],
            regrouperMessages(el[1]),
          ])
        );
        // Résultat final : un tableau de tableaux contenant les messages regroupés
        setLoadChangeConv(false);
        setLoad(false);
        scrollToLastElement();
      } catch (err) {
        setLoad(false);
        setLoadChangeConv(false);
      }
    };
    if (activeTab360 === 5) guest ? getChatRmc360() : getListRmc();
  }, [activeTab360, contactInfo.id, guest]);
  //convertToPlain

  const sendChat = async (message) => {
    const lines = message ? message.split("\n").filter((el) => el) : [""];
    setLoadSend(true);
    try {
      let formData = new FormData(); //formdata object
      formData.append("id", id);
      formData.append("conversation_id", details.id);
      formData.append("message", message);
      fileList.forEach((el) => {
        formData.append("attachments[filename][]", el.file_name);
        formData.append("attachments[url][]", el.path);
      });
      await MainService.sendChatRmc360(formData);
      if (
        messages.some((el) => el[0] === moment_timezone().format("YYYY-MM-DD"))
      ) {
        const index = messages.findIndex(
          (el) => el[0] === moment_timezone().format("YYYY-MM-DD")
        );

        if (
          messages[index][1].findIndex(
            (el) =>
              moment_timezone(el.map((t) => t.creation_time)[0]).format(
                "YYYY-MM-DD HH:mm"
              ) === moment_timezone().format("YYYY-MM-DD HH:mm")
          ) > -1
        ) {
          setMessages(
            messages.map((msg) =>
              msg[0] === moment_timezone().format("YYYY-MM-DD")
                ? [
                    moment_timezone().format("YYYY-MM-DD"),
                    messages[index][1].map((el, i) =>
                      i ===
                      messages[index][1].findIndex(
                        (el) =>
                          moment_timezone(
                            el.map((t) => t.creation_time)[0]
                          ).format("YYYY-MM-DD HH:mm") ===
                          moment_timezone().format("YYYY-MM-DD HH:mm")
                      )
                        ? [
                            ...messages[index][1][i],
                            lines.map((el, i) => ({
                              user_type: "bot",
                              first_name: "Comunik BOT",
                              status_code: "0",
                              message: el,
                              attachments:
                                i === lines.length - 1 && fileList.length > 0
                                  ? JSON.stringify(
                                      fileList.map((el) => [
                                        el.file_name,
                                        el.path,
                                      ])
                                    )
                                  : "",
                              creation_time:
                                moment_timezone().format("YYYY-MM-DD HH:mm:s"),
                              conversation_id: details?.id,
                              profile_image:
                                "https://rmcdemo.comunikcrm.info/comuniksocial/uploads/25-11-22/8748_85265_logo arrondi copie.png",
                            })),
                          ].flat()
                        : el
                    ),
                  ]
                : msg
            )
          );
        } else {
          const data = messages.map((el) =>
            el[0] === moment_timezone().format("YYYY-MM-DD")
              ? [
                  messages[index][0],
                  [
                    ...messages[index][1],

                    lines.map((el, i) => ({
                      user_type: "bot",
                      first_name: "Comunik BOT",
                      attachments:
                        i === lines.length - 1 && fileList.length > 0
                          ? JSON.stringify(
                              fileList.map((el) => [el.file_name, el.path])
                            )
                          : "",
                      status_code: "0",
                      creation_time:
                        moment_timezone().format("YYYY-MM-DD HH:mm:s"),
                      conversation_id: details?.id,
                      message: el,
                      profile_image:
                        "https://rmcdemo.comunikcrm.info/comuniksocial/uploads/25-11-22/8748_85265_logo arrondi copie.png",
                    })),
                  ],
                ]
              : el
          );
          setMessages(data);
        }
      } else {
        const data = [
          ...messages,
          [
            moment_timezone().format("YYYY-MM-DD"),
            [
              lines.map((el, i) => ({
                user_type: "bot",
                first_name: "Comunik BOT",
                attachments:
                  i === lines.length - 1 && fileList.length > 0
                    ? JSON.stringify(
                        fileList.map((el) => [el.file_name, el.path])
                      )
                    : "",
                status_code: "0",
                message: el,

                creation_time: moment_timezone().format("YYYY-MM-DD HH:mm:s"),
                conversation_id: details?.id,
                profile_image:
                  "https://rmcdemo.comunikcrm.info/comuniksocial/uploads/25-11-22/8748_85265_logo arrondi copie.png",
              })),
            ],
          ],
        ];
        setMessages(data);

        // setMessages((prev) => [
        //   ...prev,
        //   [
        //     moment_timezone().add(1, "day").format("YYYY-MM-DD"),
        //     [
        //       {
        //         user_type: "bot",
        //         first_name: "Comunik BOT",
        //         attachments: "",
        //         status_code: "0",
        //         message: message.message,

        //         creation_time: moment_timezone()
        //           .add(1, "day")
        //           .format("YYYY-MM-DD H:m:s"),
        //         conversation_id: details?.id,
        //         profile_image:
        //           "https://rmcdemo.comunikcrm.info/comuniksocial/uploads/25-11-22/8748_85265_logo arrondi copie.png",
        //       },
        //     ],
        //   ],
        // ]);
      }

      setFileList([]);
      setLoadSend(false);
      scrollToLastElement();
      setTextInput("");

      // setMessages((prev) => [
      //   ...prev,
      //   {
      //     user_type: "bot",
      //     first_name: "Comunik BOT",
      //     attachments: "",
      //     status_code: "0",
      //     creation_time: moment_timezone().format("YY-M-D H:m:s"),
      //     conversation_id: details?.id,
      //     message: message.message,
      //   },
      // ]);
      // console.log(res);
    } catch (err) {
      setLoadSend(false);

      t("toast.mesageNotSent");
    }
  };
  const navigate = useNavigate();
  const handleKeyDown = (event) => {
    if (!event.shiftKey && event.key === "Enter") {
      sendChat(textInput);
      // setHeightInput((prev) => prev + 22);
    }
  };
  return (
    <>
      {load ? (
        <div className="flex h-[calc(100vh-250px)] items-center justify-center">
          <Spin />
        </div>
      ) : (
        <div>
          {/* {details?.id ? (
            <div className="flex justify-end">
              <Button
                type="link"
                onClick={() => navigate("/rmc", { state: details.id })}
                icon={<ArrowRightOutlined />}
              >
                {t("chat.goto")} RMC
              </Button>
            </div>
          ) : null} */}
          <div
            style={{
              height:
                messages.length > 0
                  ? `calc(100vh - ${
                      from === "viewSphere"
                        ? headerHeight + 171
                        : dataSteps.length > 0
                        ? "300"
                        : "175"
                    }px)`
                  : //   `calc(100vh - ${175 + Number(heightInput)}px)`
                    "auto",

              overflow: "auto",
              marginRight: from === "viewSphere" ? "-17px" : "-19px",
              paddingRight: from === "viewSphere" ? "13px" : "32px",
            }}
          >
            {messages.length > 0 ? (
              <>
                {messages.map(([date, messages]) => (
                  <div className="chat-container" key={date}>
                    <Divider
                      style={{
                        textAlign: "center",
                        position: "sticky",
                        top: "0",
                        background: location.pathname.includes("v3")
                          ? "#F8FAFC"
                          : "white",
                        zIndex: "20",
                        marginTop: "-9px",
                        padding: "20px 0",
                      }}
                    >
                      {" "}
                      <Typography.Title level={5}>
                        {formatDateComparison(date, t)}
                      </Typography.Title>
                    </Divider>

                    <div className="flex flex-col">
                      {messages.map((message, i) => (
                        <div
                          className={`message ${
                            message[0]?.user_type !== "user" &&
                            message[0]?.user_type !== "lead"
                              ? "self-end"
                              : "self-start"
                          } max-w-[70%]`}
                          key={i}
                        >
                          <Space
                            style={{
                              display: "flex",
                              justifyContent:
                                message[0]?.user_type !== "user" &&
                                message[0]?.user_type !== "lead"
                                  ? "right "
                                  : "left ",
                              flexDirection:
                                message[0]?.user_type !== "user" &&
                                message[0]?.user_type !== "lead"
                                  ? " row-reverse "
                                  : "row ",
                            }}
                          >
                            <Avatar src={message[0].profile_image} />

                            <span className="text-xs text-gray-500">
                              {message[0].first_name} {message[0].last_name}
                            </span>
                            <span className="text-xs text-gray-500">
                              {moment_timezone(message[0].creation_time).format(
                                "HH"
                              ) +
                                ":" +
                                moment_timezone(
                                  message[0].creation_time
                                ).format("mm")}
                            </span>
                          </Space>
                          <Card
                            key={message[0].id}
                            className={`message mb-2 w-full ${
                              message[0].user_type !== "user" &&
                              message[0].user_type !== "lead"
                                ? location.pathname.includes("v3")
                                  ? "bg-white"
                                  : "bg-slate-50"
                                : "bg-blue-50"
                            } `}
                          >
                            {message.map((el, i) => (
                              <div className="message-content my-2" key={i}>
                                <span>{convertToPlain(el.message)}</span>
                                <div className="flex flex-col space-y-2">
                                  {el.attachments &&
                                  Array.isArray(JSON.parse(el.attachments))
                                    ? JSON.parse(el.attachments)?.map((el, i) =>
                                        isImagePath(el[1]) ? (
                                          <Image
                                            width={320}
                                            src={el[1]}
                                            key={i}
                                          />
                                        ) : null
                                      )
                                    : null}
                                </div>
                              </div>
                            ))}
                          </Card>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
                <div ref={containerRef} />
              </>
            ) : (
              <div className="mt-5">
                <Empty />
              </div>
            )}
          </div>
        </div>
      )}
      {/* {messages.length > 0 ? (
        <div className="sticky bottom-0 z-50  w-full py-3" ref={divRef}>
          <InputRmc
            fileList={fileList}
            setFileList={setFileList}
            textInput={textInput}
            setTextInput={setTextInput}
            fileFromPase={fileFromPase}
            onRemoveFile={onRemoveFile}
            cancel={cancel}
            setCancel={setCancel}
            sendMessage={sendChat}
            loadSend={loadSend}
            handleKeyDown={handleKeyDown}
          />

          {!isMessageSent && fileList && fileList.length > 0 && (
            <Suspense fallback={<Skeleton.Button size="large" />}>
              <ShowFileList
                from={false}
                fileList={fileList}
                fileFromPase={fileFromPase}
                onRemove={onRemoveFile}
                sendChat={sendChat}
                textInput={textInput}
                setTextInput={setTextInput}
              />
            </Suspense>
          )}
        </div>
      ) : null} */}
    </>
  );
};

export default ChatRmcWithoutInput;
