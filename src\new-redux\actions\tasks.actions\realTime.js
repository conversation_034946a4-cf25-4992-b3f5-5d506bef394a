import {
  GET_SELECTED_VIEW_IN_TASK,
  IS_USER_NOTIFIED,
  NEW_INCOMING_TASK_NOTIFICATION,
  ADD_TASK_NOTIFICATION_ACTION_TYPE,
  TASK_NOTIFICATION_PAYLOAD,
  TASK_NOTIFICATION_DESCRIPTION,
  TOTAL_TASKS_NOTIFICATIONS,
  TASKS_FILTERS,
  HANDLE_ACTIVE_FILTERS,
  SET_REMINDERS,
  ADD_REMINDER,
  IS_OVERVIEW_MODAL_OPEN,
  HANDLE_SELECTED_PIPELINE,
  HANDLE_RELATION_ID,
  HANDLE_RELATION_TYPE,
} from "../../constants";

export const setSelectedViewInTask = (payload) => ({
  type: GET_SELECTED_VIEW_IN_TASK,
  payload,
});

export const setIsUserNotified = (payload) => ({
  type: IS_USER_NOTIFIED,
  payload,
});

export const setNewIncomingTaskNotification = (payload) => ({
  type: NEW_INCOMING_TASK_NOTIFICATION,
  payload,
});

export const setTaskNotificationAction = (payload) => ({
  type: ADD_TASK_NOTIFICATION_ACTION_TYPE,
  payload,
});

export const setNotificationPayload = (payload) => ({
  type: TASK_NOTIFICATION_PAYLOAD,
  payload,
});

export const setNotificationDescription = (payload) => ({
  type: TASK_NOTIFICATION_DESCRIPTION,
  payload,
});

export const setTotalNotificationsNumber = (payload) => ({
  type: TOTAL_TASKS_NOTIFICATIONS,
  payload,
});

export const setTasksFilters = (payload) => ({
  type: TASKS_FILTERS,
  payload,
});

export const setActiveTasksFilters = (payload) => ({
  type: HANDLE_ACTIVE_FILTERS,
  payload,
});

export const setRemindersArray = (payload) => ({
  type: SET_REMINDERS,
  payload,
});

export const addReminder = (payload) => ({
  type: ADD_REMINDER,
  payload,
});

export const isOverviewModalOpen = (payload) => ({
  type: IS_OVERVIEW_MODAL_OPEN,
  payload,
});

export const setPipelinePreference = (payload) => ({
  type: HANDLE_SELECTED_PIPELINE,
  payload,
});

export const setRelationId = (payload) => ({
  type: HANDLE_RELATION_ID,
  payload,
});

export const setRelationType = (payload) => ({
  type: HANDLE_RELATION_TYPE,
  payload,
});
