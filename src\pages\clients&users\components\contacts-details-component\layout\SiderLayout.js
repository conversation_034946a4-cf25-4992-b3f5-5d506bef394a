import { Suspense, useCallback, useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import {
  Badge,
  Button,
  Divider,
  Dropdown,
  Space,
  Tooltip,
  Typography,
  message,
} from "antd";
import { ArrowLeftOutlined, MoreOutlined } from "@ant-design/icons";
import { FiEdit, FiTrash } from "react-icons/fi";
import { Archive, BookCopy } from "lucide-react";
import { useTranslation } from "react-i18next";
import { checkIfPathOnView360 } from "../../../../voip/helpers/helpersFunc";
import { toastNotification } from "../../../../../components/ToastNotification";
import FormUpdate from "../../FormUpdate";
import ActionsList from "./components/ActionsList";
import RelationFamily from "./components/RelationFamily";
import { getElementSystemDetails } from "../../../services/services";
import { useWindowSize } from "../../WindowSize";
import ModalMessage from "../../../../rmc/mailing/ModalMessage";
import { URL_ENV } from "index";
import { Loader } from "components/Chat";
import DisplayAvatar from "pages/voip/components/DisplayAvatar";

const SiderLayout = ({
  addTab,
  elementID,
  module,
  source,
  setOpenModalCheckList = () => {},
  handleMinimized = () => {},
}) => {
  //
  const dispatch = useDispatch();
  const [t] = useTranslation("common");
  const navigate = useNavigate();
  const location = useLocation();
  const windowSize = useWindowSize();
  //
  const elementInfo = useSelector(
    (state) => state?.contacts?.contactHeaderInfo
  );
  const { id, name, image, type, email, family_id } = elementInfo;

  const [openDrawerUpdate, setOpenDrawerUpdate] = useState(false);
  const [isUpdate, setIsUpdate] = useState(false);
  const [catchAddRemoveAssociation, setCatchAddRemoveAssociation] =
    useState(false);

  const fetchElementInfo = useCallback(async () => {
    const elementId = location?.pathname?.split("/")?.pop();
    if (id && !isUpdate && id === elementId && name && image && email) return;
    try {
      const { data } = await getElementSystemDetails(elementID || elementId);
      dispatch({
        type: "SET_CONTACT_HEADER_INFO",
        payload:
          {
            ...data,
            name: data?.name
              ? data.name
              : data?.reference
              ? data.reference
              : name,
            email: data?.email || email,
            type: data?.type || type,
            image:
              data?.image &&
              `${
                URL_ENV?.REACT_APP_BASE_URL +
                URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
              }${data?.image}`,
          } || {},
      });
      setIsUpdate(false);
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");

      dispatch({
        type: "RESET_CONTACT_HEADER_INFO",
      });
      source !== "webPhone" &&
        navigate(
          location?.pathname?.substring(0, location?.pathname?.lastIndexOf("/"))
        );
      throw new Error(err?.message ? err.message : err);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location.pathname, id, isUpdate, elementID]);

  useEffect(() => {
    fetchElementInfo();
  }, [fetchElementInfo]);

  //
  const handleDropdownClick = (e) => {
    const key = e?.key;
    switch (key) {
      case "edit":
        elementInfo?.id && setOpenDrawerUpdate(true);
        break;
      default:
        message.info("This feature is not ready yet!");
        break;
    }
  };

  const DropdownProps = {
    items: [
      {
        label: t("contacts.edit"),
        key: "edit",
        icon: <FiEdit className="h-4 w-4 text-slate-500" />,
      },

      {
        label: "Clone",
        icon: <BookCopy className="w-[16px] text-slate-500" />,

        key: "3",
      },
      {
        label: "Archived",
        icon: <Archive className="w-[16px] text-slate-500" />,
        key: "4",
      },
      {
        type: "divider",
      },
      {
        label: t("contacts.delete"),
        key: "1",
        icon: <FiTrash className="h-4 w-4 " />,
        danger: true,
      },
    ],
    onClick: (e) => handleDropdownClick(e),
  };
  //
  return (
    <div
      className="flex w-full justify-center border-r bg-slate-100"
      style={{
        height: windowSize?.height - 60,
      }}
    >
      <Space direction="vertical" className="w-full">
        <div className="flex justify-between p-3">
          {!checkIfPathOnView360(location.pathname) ? (
            <span />
          ) : (
            <Tooltip title="Go back">
              <Button
                onClick={() => navigate(-1)}
                icon={<ArrowLeftOutlined />}
                shape="circle"
                type="text"
              />
            </Tooltip>
          )}

          {source === "webPhone" || source === "voip" ? (
            <span />
          ) : (
            <Dropdown
              trigger={["click"]}
              placement="bottomRight"
              arrow={false}
              menu={DropdownProps}
            >
              <Button
                icon={<MoreOutlined style={{ fontSize: "15px" }} />}
                shape="circle"
                type="text"
              />
            </Dropdown>
          )}
        </div>
        <div className="flex  items-center px-4">
          <div className="flex  items-start">
            <div className="flex items-center space-x-2 ">
              <DisplayAvatar
                size={80}
                urlImg={image}
                name={name}
                source="viewSphere"
              />

              <div className="flex flex-col space-y-1.5">
                <Typography.Title level={2} className="text-center">
                  {name?.length > 12 ? (
                    <Tooltip title={name}>
                      {family_id === 6
                        ? name?.replaceAll(" ", "_")?.slice(0, 12)
                        : name?.replaceAll("_", " ")?.slice(0, 12)}
                      ...
                    </Tooltip>
                  ) : family_id === 6 ? (
                    name?.replaceAll(" ", "_")
                  ) : (
                    name?.replaceAll("_", " ")
                  )}
                </Typography.Title>
                {type && <Badge color="blue" text={type} />}
              </div>
            </div>
          </div>
        </div>
        <ActionsList
          {...elementInfo}
          elementID={elementID}
          module={module}
          source={source}
          isElementUpdate={isUpdate}
          isAssociationUpdate={catchAddRemoveAssociation}
          setOpenModalCheckList={setOpenModalCheckList}
          setCatchChange={setCatchAddRemoveAssociation}
        />

        <Divider style={{ marginBottom: "0px" }} />
        <RelationFamily
          {...elementInfo}
          // setActiveTab={setActiveTab}
          addTab={addTab}
          isElementUpdate={isUpdate}
          source={source}
          setAddRemoveAssoc={setCatchAddRemoveAssociation}
          catchChange={catchAddRemoveAssociation}
          elementID={elementID}
          handleMinimized={handleMinimized}
        />
      </Space>
      <FormUpdate
        open={openDrawerUpdate}
        setOpen={setOpenDrawerUpdate}
        familyId={elementInfo?.family_id}
        elementDetails={{
          id: elementInfo?.id,
          label: elementInfo?.name,
        }}
        mask={false}
        setCatchChange={setIsUpdate}
      />
    </div>
  );
};

export default SiderLayout;
