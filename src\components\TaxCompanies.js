import React, { useEffect, useRef } from "react";
import { Form, Button, Space, Switch, InputNumber, Spin } from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { toastNotification } from "components/ToastNotification";
import { setSearch } from "new-redux/actions/menu.actions/menu";
import { SubmitKeyPress } from "utils/SubmitKeyPress";
import Header from "components/configurationHelpDesk/Header";
import NewTableDraggable from "components/NewTableDraggable";
import BottomButtonAddRow from "components/BottomButtonAddRow";
import TabsCompanies from "./TabsCompanies";
import { RollbackOutlined } from "@ant-design/icons";
import { showNameOrg } from "new-redux/actions/configCompanies.actions/configCompaniesAction";
import { useNavigate, useParams } from "react-router-dom";
import MainService from "services/main.service";
import NotFoundPage from "pages/404";

const TaxCompanies = () => {
  const [form] = Form.useForm();
  const [data, setData] = useState([]);
  const [editingKey, setEditingKey] = useState("");
  const [id, setId] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState(true);
  const [loadTable, setLoadTable] = useState(false);

  const [pageSize, setPageSize] = useState(20);
  const isEditing = (record) => record.key === editingKey;
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const { search } = useSelector((state) => state.form);
  const [keyTab, setKeyTab] = useState("3");
  const [show404, setShow404] = useState(false);

  const inputRefs = useRef([]);
  useEffect(() => {
    inputRefs.current.forEach((input) => {
      input?.focus();
    });
  }, [data.length, id]);

  const onFinishFailed = (values) => {
    console.log(values);
  };

  const navigate = useNavigate();
  const params = useParams();
  const EditableCell = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    const inputNode =
      inputType === "switch" ? (
        <>
          <Switch
            size="small"
            defaultChecked={
              !loadTable
                ? record.status == 1
                  ? true
                  : false
                : form.getFieldsValue().status == 0
                ? false
                : true
            }
          />
        </>
      ) : (
        <InputNumber
          onKeyDown={handleInputNumberKeyDown}
          min="0"
          max="100"
          addonAfter="%"
          placeholder={title}
          onKeyPress={handleKeyPress}
          ref={(el) => (inputRefs.current[index] = el)}
        />
      );

    return (
      <td {...restProps}>
        {editing ? (
          <Form.Item
            name={dataIndex.toLowerCase()}
            style={{
              margin: 0,
            }}
            rules={[
              {
                required: dataIndex.toLowerCase() === "rate" ? true : false,
                message: `${title} ${t("table.header.isrequired")}`,
              },
            ]}
          >
            {inputNode}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };
  function handleInputNumberKeyDown(e) {
    if (e.key === "." && e.target.value.includes(".")) {
      e.preventDefault();
    }
    if (e.key === "-" && !e.target.value) {
      e.preventDefault();
    }
    if (e.key === "e") {
      e.preventDefault();
    }
    if (
      isNaN(parseInt(e.key)) &&
      ![
        "Tab",
        "Backspace",
        "Delete",
        "ArrowLeft",
        "ArrowRight",
        "Enter",
      ].includes(e.key)
    ) {
      e.preventDefault();
    }
  }
  const edit = (record) => {
    if (record) {
      form.setFieldsValue({
        rate: record.rate,
        status: record.status,
      });
      setId(record.id);
    } else {
      form.setFieldsValue({
        rate: "",
        status: "",
      });
    }
    setEditingKey(record.key);
  };
  const cancel = (record) => {
    setEditingKey("");
    setId(null);
    if (!record.id) {
      setData(data.filter((item) => item.key !== record.key));
    }
  };

  const UpdateSwitch = async (record, status) => {
    setLoadTable(true);
    try {
      const res = await MainService.isActiveTaxCompany(
        {
          rate: record.rate,
          status: status ? 1 : 0,
          company_id: params.id,
        },
        record.id
      );
      setEditingKey("");
      setData(
        data.map((el) =>
          el.id === res.data.data.id
            ? {
                ...res.data.data,
                key: res.data.data.id,
              }
            : el
        )
      );
      form.setFieldsValue({
        rate: "",
        status: "",
      });
      setLoadTable(false);
      toastNotification("success", "Tax" + t("toasts.edit"), "topRight");
    } catch (errInfo) {
      setLoadTable(false);

      toastNotification("error", t("toasts.somethingWrong"), "topRight");
    }
  };

  const save = async (key) => {
    setLoadTable(true);
    if (id) {
      try {
        const row = await form.validateFields();
        const res = await MainService.updateTaxCompany(
          {
            ...row,
            status: row.status ? 1 : 0,
            company_id: params?.id,
          },
          id
        );
        setEditingKey("");
        setData(
          data.map((el) =>
            el.id === res.data.data.id
              ? {
                  ...res.data.data,
                  key: res.data.data.id,
                }
              : el
          )
        );
        form.setFieldsValue({
          rate: "",
          status: "",
        });
        setLoadTable(false);
        toastNotification("success", "Tax" + t("toasts.edit"), "topRight");
      } catch (errInfo) {
        setLoadTable(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    } else {
      try {
        const row = await form.validateFields();
        const res = await MainService.createTaxCompany({
          ...row,
          status: row.status ? 1 : 0,
          company_id: params.id,
        });
        setEditingKey("");
        setData([
          ...data.filter((el) => el.id),
          { ...res.data.data, key: res.data.data.id },
        ]);
        form.setFieldsValue({
          rate: "",
          status: "",
        });
        setLoadTable(false);
        toastNotification("success", "Tax" + t("toasts.created"), "topRight");
      } catch (errInfo) {
        setLoadTable(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    }
  };
  useEffect(() => {
    const getFolders = async () => {
      setLoading(true);
      try {
        const {
          data: { data },
        } = await MainService.getTaxesCompany(params.id);
        setData(data.map((el, i) => ({ ...el, key: el.id, rank: i + 1 })));

        setLoading(false);
      } catch (err) {
        if (err.response.status === 404) {
          setShow404(true);
        } else
          toastNotification("error", t("toasts.somethingWrong"), "topRight");
        setLoading(false);
      }
    };
    getFolders();
    return () => dispatch(setSearch(""));
  }, []);

  const handleKeyPress = (event) => {
    SubmitKeyPress(event, form);
  };

  const columns = [
    {
      title: t("familyProduct.rate"),
      dataIndex: "rate",
      key: "rate",
      editable: true,
      sorter: (a, b) => a.rate - b.rate,
      render: (_, record) => (
        <span>
          {record.rate} {typeof record.rate === "number" ? "%" : ""}
        </span>
      ),
    },
    {
      title: t("helpDesk.actif"),
      key: "status",
      dataIndex: "status",
      editable: true,
      filters: [
        {
          text: t(`helpDesk.actif`),
          value: "1",
        },
        {
          text: t(`helpDesk.noActif`),
          value: "0",
        },
      ],
      onFilter: (value, record) => record.status == value,
      sorter: (a, b) => a.status - b.status,

      render: (_, record) => (
        <>
          <Switch
            size="small"
            checked={record.status == 1 ? true : false}
            onChange={(e) => UpdateSwitch(record, e)}
          />
        </>
      ),
    },
  ];

  const handleAdd = () => {
    const ids = data.map((object) => {
      return object.id;
    });
    setId(null);
    const newData = {
      key: Math.max(...ids) + 1,
      rate: `  `,
      status: false,
      disabled: true,
    };
    setCurrentPage(Math.ceil([...data, newData].length / pageSize));
    setData([...data, newData]);
    form.setFieldsValue({
      rate: "",
      status: false,
    });
    setEditingKey(Math.max(...ids) + 1);
  };
  //do not delete this line
  const onRow = () => {};
  if (show404) {
    return (
      <>
        <Button
          icon={<RollbackOutlined />}
          onClick={() => {
            navigate("/settings/general/companies");
            dispatch(showNameOrg(""));
          }}
          style={{ margin: "16px 16px 0 16px" }}
        >
          {/* {t("companies.accessTableCompanies")} */}
        </Button>
        <div className="height-screen bg-white">
          <NotFoundPage />
        </div>
      </>
    );
  }
  return (
    <>
      <Button
        icon={<RollbackOutlined />}
        onClick={() => {
          navigate("/settings/general/companies");
          dispatch(showNameOrg(""));
        }}
        style={{ margin: "16px 16px 0 16px" }}
      >
        {/* {t("companies.accessTableCompanies")} */}
      </Button>
      <TabsCompanies keyTab={keyTab} setKeyTab={setKeyTab} companie_id={id} />
      {loading ? (
        <div className="flex h-[calc(100vh-57px)] items-center justify-center">
          <Spin />
        </div>
      ) : (
        <Space direction="vertical" style={{ width: "100%" }}>
          <Header
            active={"5"}
            editingKey={editingKey}
            handleAdd={handleAdd}
            btnText={t("familyProduct.addTax")}
            disabled={
              loading ? true : editingKey ? true : search ? true : false
            }
            api="taxes"
          />

          <NewTableDraggable
            columns={columns}
            setLoading={setLoadTable}
            isEditing={isEditing}
            data={data}
            setData={setData}
            loading={loadTable}
            save={save}
            edit={edit}
            EditableCell={EditableCell}
            onFinishFailed={onFinishFailed}
            cancel={cancel}
            form={form}
            apiRank="/rank-taxes"
            editingKey={editingKey}
            api="taxes"
            onRow={onRow}
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            pageSize={pageSize}
            setPageSize={setPageSize}
          />

          <BottomButtonAddRow
            editingKey={editingKey}
            data={data}
            text={t("familyProduct.addTax")}
            handleAdd={handleAdd}
            loading={loading}
            search={search || ""}
          />
        </Space>
      )}
    </>
  );
};
export default TaxCompanies;
