/* .ant-list-item-meta-title {
    margin-bottom: .25rem !important;
} */

.list-card-groups .ant-list-item-action {
  display: flex;
  justify-content: end;
}

.voip-list .ant-list-vertical .ant-list-item .ant-list-item-meta {
  margin-block-end: 0px !important;
}

.voip-list .ant-list.membersList .ant-list-item {
  @apply hover:bg-slate-100;
}

.webPhone-voip-list .ant-list.membersList .ant-list-item {
  @apply hover:bg-slate-100;
}

.voip-list
  .ant-list.membersList
  .ant-list-item
  .ant-list-item-meta
  .ant-list-item-meta-description {
  @apply truncate text-sm;
}

.unread-voice {
  background-color: rgba(0, 0, 0, 0.02);
}

:where(.css-dev-only-do-not-override-1xz77jp).ant-list-vertical
  .ant-list-item
  .ant-list-item-meta {
  margin-block-end: 0 !important;
  padding: 0.4rem 1rem;
}

/* audio control */
.DurationColumn audio::-webkit-media-controls-play-button,
.DurationColumn audio::-webkit-media-controls-panel {
  background-color: #fff;
  color: #fff;
}

.DurationColumn audio:hover::-webkit-media-controls-play-button,
.DurationColumn audio:hover::-webkit-media-controls-panel {
  background-color: #fafafa;
  color: #fafafa;
}

.DurationColumn .audio-controls {
  height: 2.2rem;
  margin-bottom: 0;
}

/* Popover */
.popover-tag-voip .ant-popover-inner-content {
  display: flex;
  justify-content: center;
}

.voice-msg-webPhone .ant-card .ant-card-body {
  padding: 0px;
}

.tag-content .ant-card-small > .ant-card-body {
  @apply bg-white;
}

.directory-scroll::-webkit-scrollbar {
  width: 6px;
  /* height: 7px; */
}

.directory-scroll::-webkit-scrollbar-thumb {
  -webkit-border-radius: 10px;
  border-radius: 10px;
  background: #eaeaea;
  border-radius: 0px;
}

.directory-scroll::-webkit-scrollbar-track {
  background-color: white;
  -webkit-border-radius: 10px;
  border-radius: 10px;
  border-radius: 0px;
}

.directory-scroll::-webkit-scrollbar-thumb:hover {
  background: #eaeaea;
  border-radius: 0px;
}
