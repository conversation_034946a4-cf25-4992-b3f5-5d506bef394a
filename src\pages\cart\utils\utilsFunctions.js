import { ExclamationCircleFilled } from "@ant-design/icons";
import { Modal } from "antd";

export const handleResetRow = (form, record) => {
 // console.log({ record });
  let dd = form.getFieldsValue();
  let obj = dd["data"][record?.id];

  form.setFieldsValue({
    ...dd,
    data: {
      ...dd["data"],
      [record?.id]: {
        ...obj,
        discount: 0,
        quantity: 0,
        selectedProduct: null,
        selectedProductDescription: undefined,
        unit: undefined,
        unitPrice: 0,
        tax: 0,
      },
    },
  });
};

export const preventNonNumericCharacters = (event) => {
  // Allow: backspace, delete, tab, escape, enter
  if (
    [46, 8, 9, 27, 13].includes(event.keyCode) ||
    // Allow: Ctrl+A/Ctrl+C/Ctrl+X
    ((event.keyCode === 65 || event.keyCode === 67 || event.keyCode === 88) &&
      (event.ctrlKey || event.metaKey)) ||
    // Allow: home, end, left, right
    (event.keyCode >= 35 && event.keyCode <= 39) ||
    // Ensure that it is a digit
    (!event.shiftKey &&
      ((event.keyCode >= 48 && event.keyCode <= 57) ||
        (event.keyCode >= 96 && event.keyCode <= 105)))
  ) {
    // Let it happen, don't do anything
    return;
  }
  // Prevent the keypress
  event.preventDefault();
};

export const calculateTotal = (cartsData, totalDiscount, setter) => {
  let total_discount = 0;
  let total_tax = 0;
  let total_amount = 0;
  const carts = [];

  cartsData &&
    cartsData.forEach((cartData) => {
      const amount_ht = Number(cartData.unitPrice) * cartData.quantity;
      const unit_discount = amount_ht * (cartData.discount / 100);
      const unit_tax = amount_ht * (Number(cartData.tax) / 100);
      const amount_ttc = amount_ht - unit_discount + unit_tax;

      total_discount += unit_discount;
      total_tax += unit_tax;
      total_amount += amount_ttc;

      const itemTotal = total_amount.toFixed(2); // Round to 2 decimal places
      carts.push({ ...cartData, itemTotal });
    });

  const discount_total =
    ((total_amount - total_discount) * totalDiscount) / 100;
  const total = total_amount - total_discount - discount_total + total_tax;

  const cartTotal = total.toFixed(2); // Round to 2 decimal places
  setter(carts);
  return { cartTotal, carts };
};

export const formatJsonPayload = (id, currency, total, item, newProduct) => {
  return {
    element_id: id,
    type: "deal",
    discount_total_percent: 0,
    currency: currency,
    total_price: total,
    carts: [{
      product_id: item?.product,
      product_name:"ki",
      quantity: item?.qty ?? 0,
      unit: item?.unit ?? null,
      unit_price: item?.unitPrice ?? 0,
      unit_tax: item?.tax ?? null,
      unit_discount: item?.discount ?? 0,
      description: item?.productDescription ?? null,
    }],
  };
};

export const formatDataObject = (unit) => {
  return {
    value: unit?.id,
    label: `${unit?.currency} (${unit?.currency_symbol})`,
    symbol: unit?.currency_symbol,
    currencyName: unit?.currency_name,
  };
};

export const isUUID = (id) => {
  const regex =
    /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return regex.test(id);
};

export const showConfirmModal = (deleteFunction) => {
  Modal.confirm({
    title: "Do you want to delete This Cart",
    centered: true,
    icon: <ExclamationCircleFilled style={{ color: "red" }} />,
    okText: "Yes, i want to delete this cart",
    cancelText: "Cancel",
    okButtonProps: {
      danger: true,
    },
    confirmLoading: true,
    onOk: () => deleteFunction(),
    onCancel: () => {},
  });
};

export const resetFields = (formInstance, ID) => {
  formInstance.setFieldsValue({
    id: ID,
    product: null,
    qty: 0,
    unitPrice: 0,
    discount: 0,
    amount: 0,
    tax: 0,
  });
};

export const doesCartDetailsExist = (object) => {
  let isObject = Object.keys(object);
  return isObject?.length > 0;
};
