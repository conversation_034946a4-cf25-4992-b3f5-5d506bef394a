/* App.css */
body {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: Arial, sans-serif;
    background-color: white;
  }
  
  .container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 10px;
  }
  
  .content {
    width: 100%;
    max-width: 600px;
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  }
  
  .btn-chat {
    display: inline-block;
    padding: 10px 20px;
    background-color: #1a9260;
    color: white;
    text-align: center;
    text-decoration: none;
    border-radius: 5px;
    font-size: 16px;
    transition: background-color 0.3s;
    border: none;
    cursor: pointer;
  }
  
  .btn-chat:hover {
    background-color: #0772ba;
  }
  
  /* .sb-chat-btn {
    background-color: #1a9260 !important;
  }
  
  .sb-chat .sb-scroll-area .sb-header {
    background-color: #1a9260 !important;
  } */