import React from "react";
import { Skeleton, Card, Divider } from "antd";
import { useLocation } from "react-router-dom";

const LoadingViewSphere = () => {
  const { pathname } = useLocation();
  return (
    <div style={{ height: "calc(100vh - 57px)" }}>
      {/* Header */}
      <div className="max-h-[94px] border-b border-gray-200 bg-gradient-to-t from-blue-50 to-white shadow-sm">
        {/* Contact Header */}
        <div className="w-full px-6 py-1">
          <div className="mb-4 flex w-full items-center justify-between">
            <div className="flex w-full items-center space-x-4">
              <div className="flex h-16 w-16 items-center justify-center rounded-full bg-[#DBEAFE] shadow-md">
                <Skeleton.Avatar active size={60} className="bg-transparent" />
              </div>
              <div className="w-full space-y-2">
                <Skeleton.Input active size="small" className="w-20" />
                {/* &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <Skeleton.Input active size="small" className="w-20" />
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <Skeleton.Input active size="small" className="w-20" />
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <Skeleton.Input active size="small" className="w-20" /> */}
                <div className="flex w-full justify-between">
                  <div className="flex items-center space-x-3">
                    <Skeleton.Input active size="small" className="w-20" />
                    <div className="h-1 w-1 rounded-full bg-gray-300"></div>
                    <div className="flex h-5 w-5 items-center justify-center rounded-full bg-[#DBEAFE]">
                      <Skeleton.Avatar
                        active
                        size="small"
                        className="bg-transparent"
                      />
                    </div>
                    <Skeleton.Input active size="small" className="w-20" />
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    <Skeleton.Input active size="small" className="w-20" />
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    <Skeleton.Input active size="small" className="w-20" />
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    <Skeleton.Input active size="small" className="w-32" />
                  </div>
                  <div className="flex items-center space-x-3">
                    {/* <div className="h-6 w-px bg-gray-300"></div> */}
                    <Skeleton.Avatar
                      active
                      size={28}
                      className="bg-transparent"
                      shape="square"
                    />
                    <Skeleton.Avatar
                      active
                      size={28}
                      className="bg-transparent"
                      shape="square"
                    />
                    <Skeleton.Avatar
                      active
                      size={28}
                      className="bg-transparent"
                      shape="square"
                    />
                    <Skeleton.Avatar
                      active
                      size={28}
                      className="bg-transparent"
                      shape="square"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex">
        {/* Sidebar */}
        <div
          className={`min-h-[calc(100vh-180px)] ${
            pathname !== "/telephony/directory" ? "w-[180px]" : "w-[80px]"
          } border-r border-gray-200 bg-[#FAFCFD]  `}
        >
          <div className="space-y-5 p-6">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="flex items-center space-x-3">
                <div className="flex h-4 w-4 items-center justify-center rounded bg-blue-400">
                  <Skeleton.Avatar
                    active
                    size="small"
                    shape="square"
                    className="bg-transparent"
                  />
                </div>
                {pathname !== "/telephony/directory" && (
                  <Skeleton.Input active size="small" className="flex-1" />
                )}
              </div>
            ))}
          </div>
          <Divider className="!m-0" />
          <div className="mt-5 px-6">
            {pathname !== "/telephony/directory" ? (
              <Skeleton.Input active size="small" className=" w-9" />
            ) : (
              <div className="">
                <div className="flex items-center space-x-2">
                  <div className="flex h-4 w-4 items-center justify-center rounded text-blue-500">
                    <Skeleton.Avatar
                      active
                      size="small"
                      shape="square"
                      className="bg-transparent"
                    />
                  </div>
                </div>
              </div>
            )}
            <div className="space-y-4 py-6">
              {[...Array(2)].map((_, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="flex h-4 w-4 items-center justify-center rounded bg-blue-400">
                      <Skeleton.Avatar
                        active
                        size="small"
                        shape="square"
                        className="bg-transparent"
                      />
                    </div>
                    {pathname !== "/telephony/directory" && (
                      <Skeleton.Input active size="small" className="w-9" />
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex">
          <div className="flex-1 bg-white p-6">
            {/* Dashboard */}
            <div className="mb-8">
              <Skeleton.Input active size="large" className="mb-4 w-32" />

              <Card className="mb-6">
                <div className="mb-4">
                  <Skeleton.Input active size="default" className="mb-4 w-48" />
                  <div className="mb-4 grid grid-cols-3 gap-4">
                    <Skeleton.Input active size="small" className="w-full" />
                    <Skeleton.Input active size="small" className="w-full" />
                    <Skeleton.Input active size="small" className="w-full" />
                  </div>
                </div>

                {/* Stats Grid */}
                <div className="mb-6 grid grid-cols-4 gap-6">
                  {["Appels directs", "Emails", "Meeting", "Visio"].map(
                    (title, index) => (
                      <div key={index} className="text-center">
                        <Skeleton.Input
                          active
                          size="small"
                          className="mx-auto mb-2 w-24"
                        />
                        <Skeleton.Input
                          active
                          size="large"
                          className="mx-auto w-8"
                        />
                      </div>
                    )
                  )}
                </div>

                <div className="grid grid-cols-4 gap-6">
                  {["Notes", "Comments", "Todolist", "Files"].map(
                    (title, index) => (
                      <div key={index} className="text-center">
                        <Skeleton.Input
                          active
                          size="small"
                          className="mx-auto mb-2 w-20"
                        />
                        <Skeleton.Input
                          active
                          size="large"
                          className="mx-auto w-8"
                        />
                      </div>
                    )
                  )}
                </div>
              </Card>
            </div>

            {/* Enterprises Table */}
            <Card className="mb-6">
              <div className="mb-4 flex items-center justify-between">
                <Skeleton.Input active size="default" className="w-32" />
                <Skeleton.Button active size="small" shape="circle" />
              </div>

              {/* Table Header */}
              <div className="mb-4 grid grid-cols-6 gap-4 border-b border-gray-200 pb-3">
                {[
                  "NOM COMPLET",
                  "PROPRIÉTAIRE",
                  "CHAMP",
                  "PIPELINE",
                  "STAGE",
                  "",
                ].map((header, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <Skeleton.Input active size="small" className="w-20" />
                    {index < 5 && (
                      <Skeleton.Button active size="small" shape="circle" />
                    )}
                  </div>
                ))}
              </div>

              {/* Table Rows */}
              {[...Array(3)].map((_, rowIndex) => (
                <div
                  key={rowIndex}
                  className="grid grid-cols-6 gap-4 border-b border-gray-100 py-3"
                >
                  <div className="flex items-center space-x-3">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-500">
                      <Skeleton.Avatar
                        active
                        size="small"
                        shape="circle"
                        className="bg-transparent"
                      />
                    </div>
                    <Skeleton.Input active size="small" className="w-24" />
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-400">
                      <Skeleton.Avatar
                        active
                        size="small"
                        className="bg-transparent"
                      />
                    </div>
                    <Skeleton.Input active size="small" className="w-20" />
                  </div>
                  <Skeleton.Input active size="small" className="w-24" />
                  <Skeleton.Input active size="small" className="w-16" />
                  <Skeleton.Button active size="small" className="w-20" />
                  <Skeleton.Button active size="small" shape="circle" />
                </div>
              ))}
            </Card>

            {/* Offers Section */}
            <Card>
              <div className="mb-4 flex items-center justify-between">
                <Skeleton.Input active size="default" className="w-24" />
                <Skeleton.Button active size="small" shape="circle" />
              </div>

              <div className="mb-4 grid grid-cols-6 gap-4 border-b border-gray-200 pb-3">
                {[...Array(6)].map((_, index) => (
                  <Skeleton.Input
                    key={index}
                    active
                    size="small"
                    className="w-20"
                  />
                ))}
              </div>

              {[...Array(2)].map((_, rowIndex) => (
                <div key={rowIndex} className="grid grid-cols-6 gap-4 py-3">
                  <Skeleton.Input active size="small" className="w-32" />
                  <div className="flex items-center space-x-2">
                    <div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-400">
                      <Skeleton.Avatar
                        active
                        size="small"
                        className="bg-transparent"
                      />
                    </div>
                    <Skeleton.Input active size="small" className="w-20" />
                  </div>
                  <Skeleton.Input active size="small" className="w-24" />
                  <Skeleton.Input active size="small" className="w-16" />
                  <Skeleton.Button active size="small" className="w-20" />
                  <Skeleton.Button active size="small" shape="circle" />
                </div>
              ))}
            </Card>

            {/* Pagination */}
            <div className="mt-6 flex justify-center">
              <div className="flex items-center space-x-2">
                <Skeleton.Button active size="small" shape="circle" />
                <Skeleton.Input active size="small" className="w-16" />
              </div>
            </div>
          </div>
          <div
            className={`flex h-[calc(100vh-141px)] w-[80px] flex-col items-center justify-center bg-[#FAFCFD]`}
          >
            <div className="space-y-6  p-6">
              {[...Array(7)].map((_, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <div className="flex h-4 w-4 items-center justify-center rounded bg-gray-800">
                    <Skeleton.Avatar
                      active
                      size="small"
                      shape="square"
                      className="bg-transparent"
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoadingViewSphere;
