import { useEffect, useState } from "react";
import { Button, Card, Upload, Input, Form, Switch } from "antd";
import { FiUpload } from "react-icons/fi";
import { CheckOutlined, CloseOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { fileTypes } from "../../../../utils/acceptedFileTypes";

const FileField = ({
  fieldId,
  label,
  TooltipDescription,
  description,
  required,
  form,
  value,
  reset,
  readOnly,
}) => {
  const [t] = useTranslation("common");
  // console.log(fieldId, form.getFieldValue(fieldId));
  const [fileList, setFileList] = useState(value?.files || []);

  const handleChange = ({ fileList }) => setFileList([...fileList]);

  useEffect(() => {
    if (reset) setFileList([]);
    return () => {
      setFileList([]);
    };
  }, [reset]);

  // console.log({ value });

  //
  return (
    <Card style={{ marginBottom: "1rem" }}>
      <Form.Item
        key={fieldId}
        label={label}
        tooltip={TooltipDescription(description)}
        name={fieldId}
        rules={[
          {
            required: required,
            message: t("contacts.fieldXRequired", { x: label }),
          },
        ]}
      >
        <Upload
          listType="text"
          disabled={readOnly}
          multiple={true}
          accept={fileTypes}
          beforeUpload={() => false}
          fileList={fileList}
          onChange={handleChange}
        >
          <Button icon={<FiUpload className="mr-1" />}>
            {t("contacts.upload")}
          </Button>
        </Upload>
      </Form.Item>
      {form.getFieldValue(fieldId)?.fileList?.length ||
      form.getFieldValue(fieldId)?.length ? (
        <div className="flex flex-col">
          <Form.Item key={`${fieldId}filename`} name={`${fieldId}filename`}>
            <Input
              placeholder={t("contacts.enterFileName")}
              defaultValue={value?.fileName}
              disabled={readOnly}
              // onChange={(e) =>
              //   form.setFieldValue(`${fieldId}fileName`, e.target.value)
              // }
            />
          </Form.Item>
          <div className="flex flex-grow space-y-2">
            <span className="m-2">{t("contacts.validated")}: </span>
            <Form.Item
              key={`${fieldId}isActive`}
              name={`${fieldId}isActive`}
              style={{ marginTop: "0px" }}
            >
              <Switch
                // onChange={(event) =>
                //   form.setFieldValue(`${fieldId}isValid`, event ? 1 : 0)
                // }
                checkedChildren={<CheckOutlined />}
                unCheckedChildren={<CloseOutlined />}
                defaultChecked={value?.isActive}
                disabled={readOnly}
                // checkedChildren="Validated"
                // unCheckedChildren="Not validated"
              />
            </Form.Item>
          </div>
        </div>
      ) : (
        ""
      )}
    </Card>
  );
};

export default FileField;
