import DisplayAvatar from "pages/voip/components/DisplayAvatar";
import { humanDate } from "../../voip/helpers/helpersFunc";
import { URL_ENV } from "index";

const formattingData = async (data, t) => {
  const baseUrlImg =
    URL_ENV?.REACT_APP_BASE_URL + URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL;
  const result = [];
  let set = {};
  data?.forEach((element) => {
    const {
      field_value,
      etat,
      created_at,
      updated_at,
      created_by,
      invitation,
      can_create_room,
      pipeline_id,
      stage,
      final_stage,
      resolved_stage,
      guest,
      is_overdue,
      sla,
    } = element;

    set["key"] = element["_id"];
    set["status"] = etat;
    set["guest"] = guest;
    set["invitation"] = invitation;
    set["can_create_room"] = can_create_room;
    set["sla_ticket"] = {
      is_overdue,
      time: sla ? humanDate(sla, t) : null,
    };
    set["created_by"] = (
      <DisplayAvatar
        name={created_by?.label}
        urlImg={!!created_by?.avatar && `${baseUrlImg}${created_by?.avatar}`}
        tooltip={true}
        size={28}
        cursor="help"
      />
    );
    set["created_at"] = created_at && (
      <span className=" text-slate-700	">
        {humanDate(created_at, t, "table")}
      </span>
    );
    set["updated_at"] = updated_at && (
      <span className=" text-slate-700	">
        {humanDate(updated_at, t, "table")}
      </span>
    );

    if (Array.isArray(field_value)) {
      let pipelineId = null;
      field_value?.forEach((field) => {
        if (field === null) {
          return;
        }
        const {
          field_id,
          enteredValue,
          field_type,
          related_type,
          country_module,
          family_module,
          module,
          avatar,
        } = field;

        if (!avatar) {
          set[field_id] = {
            value: enteredValue,
            fieldType: field_type,
          };
        } else {
          set["avatar"] = enteredValue;
        }

        if (related_type) {
          set[field_id].relatedType = related_type;
          if (related_type === "country")
            set[field_id].relatedOptions = country_module;
          if (related_type === "module") {
            // Handle Pipeline: add stage and color
            if (field_id === pipeline_id && !!stage) {
              pipelineId = module?.id;
              set[field_id].relatedOptions = {
                ...module,
                label: `${module?.label} | ${stage.label}`,
                pipelineLabel: module?.label,
                stageLabel: stage?.label,
                color: stage?.color,
                isPipeline: true,
              };
            } else set[field_id].relatedOptions = module;
          }
          if (related_type === "family_module")
            set[field_id].relatedOptions = family_module;
        }
      });
      set["pipelineStage"] = { isFinalStage: final_stage, pipelineId };
      set["resolvedStage"] = resolved_stage;
      result.push(set);
      set = {};
      pipelineId = null;
    }
  });
  return result;
};

export default formattingData;
