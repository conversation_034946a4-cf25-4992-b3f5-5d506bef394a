import { queryClient } from "index";

export const updateParticipantsList = (new_list, discussion_id, type) => {
  queryClient.setQueryData(["INFO_CANAL", discussion_id, type], (oldData) => {
    if (!oldData) return oldData;

    const newData = oldData;

    return {
      ...newData,
      data: {
        ...newData.data,
        room_info: {
          ...newData.data.room_info,
          participants: new_list,
        },
      },
    };
  });
};
