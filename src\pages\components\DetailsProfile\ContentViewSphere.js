import { lazy } from "react";
import {
  ApartmentOutlined,
  CalendarOutlined,
  CommentOutlined,
  ContainerOutlined,
  FileAddOutlined,
  FileTextOutlined,
  FullscreenExitOutlined,
  FullscreenOutlined,
  MailOutlined,
  MessageOutlined,
  MinusOutlined,
  Pie<PERSON>hartOutlined,
  PlusOutlined,
  SelectOutlined,
  ShareAltOutlined,
  SwapOutlined,
  ShoppingCartOutlined,
} from "@ant-design/icons";
import {
  <PERSON>ge,
  Button,
  Menu,
  Tabs,
  Tooltip,
  Layout,
  Popover,
  Typography,
  Drawer,
  Modal,
  Form,
  Radio,
  Divider,
} from "antd";
import {
  setActiveActivity360,
  setActiveMenu360,
  setActiveTab360,
} from "new-redux/actions/vue360.actions/vue360";
import React, { Suspense, useEffect, useState, useRef } from "react";
import { useDispatch } from "react-redux";
import { familyIcons } from "./ViewSphere2";
import RelationsViewSphere from "./RelationsViewSphere";
import { URL_ENV } from "index";
import GridView from "./GridView";
import { FiFolderPlus, FiList } from "react-icons/fi";
import ProfileDetails from "pages/clients&users/components/contacts-details-component/ProfileDetails";
import TimeLine from "./Timeline";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import ChatViewSphere from "../ChatViewSphere";
import CallLogs360 from "pages/voip/call_logs/CallLogs360";
import ChatRmc from "./ChatRmc";
import Overview from "./Activities/Overview";
import { Loader } from "lucide-react";
import { setSearch } from "new-redux/actions/menu.actions/menu";
import ListItemsRelations from "./ListItemsRelations";
import Notes from "./Activities/Notes";
import { HiOutlinePhone } from "react-icons/hi2";
import CheckList from "./CheckList";
import ModalMessage from "pages/rmc/mailing/ModalMessage";
import FormUpdate from "pages/clients&users/components/FormUpdate";
import FormCreate from "pages/clients&users/components/FormCreate";
import ShowPipelineStage from "components/ShowPipelineStage";
import CreateTask from "pages/voip/components/CreateTask";
import FieldsContainer from "components/FieldSettings/FieldsContainer";
import Mails from "./Activities/Mails";
import Files from "./Activities/Files";
import { setOpenModalEmail } from "new-redux/actions/mail.actions";
import { setChatSelectedConversation } from "new-redux/actions/chat.actions";
import { HiOutlinePencilAlt } from "react-icons/hi";
import { lazyRetry } from "utils/lazyRetry";
import { isGuestConnected, roles } from "utils/role";
import Cart from "pages/cart";
import { Refs_IDs } from "components/tour/tourConfig";

const NotesList = lazy(
  () => lazyRetry(() => import("./NotesList")),
  "NotesList"
);

const { Content, Sider } = Layout;
const acceptedFamilyIds = [1, 2, 4, 9];

const ContentViewSphere = ({
  selectedKey,
  headerHeight,
  from,
  setSelectedKey,
  setSelectedItem,
  relations,
  collapsed,
  contactInfo,
  setRelations,
  generalInfo,
  loading,
  windowHeight,
  kpi,
  openFields,
  setOpenFields,
  isUpdate,
  setIsUpdate,
  dataSteps,
  listConv,
  tasksTypes,
  setSelectedKeySideBar,
  countTasks,
  setTasksTypes,
  setCountTasks,
  setKpi,
  disabledRelation,
  selectedItem,
  selectedKeySideBar,
  mountChat,
  setMountchat,
  openTask,
  setOpenTask,
  setCollapsed,
  openDrawerUpdate,
  setOpenDrawerUpdate,
  openPipeline,
  setOpenPipeline,
  handleSumitReasonsForm,
  finalStageForm,
  selectedReasonType,
  setSelectedReasonType,
  reasons,
  setReasons,
  setListConv,
  actionDeal,
  loadFinalStage,
  openView360InDrawer,
  refreshInfo,
  setRefreshInfo,
  setActionDeal,
}) => {
  const [t] = useTranslation("common");
  const {
    activeTab360,
    newInteraction,
    chatInViewSphere,
    chatInViewSphereFromDrawer,
  } = useSelector((state) => state?.vue360);
  const { dataAccounts } = useSelector((state) => state.mailReducer);
  const { user } = useSelector((state) => state.user);
  const [listFilter, setFilter] = useState({ selected: [] });
  const [openModalCheckList, setOpenModalCheckList] = useState(false);
  const [isFullScreenNotes, setIsFullScreenNotes] = useState(false);
  const [isFullScreenFiles, setIsFullScreenFiles] = useState(false);
  const [isFullScreenList, setIsFullScreenList] = useState(false);
  const [isFullScreenChat, setIsFullScreenChat] = useState(false);
  const [isFullScreenRmc, setIsFullScreenRmc] = useState(false);
  const [openNotes, setOpenNotes] = useState(false);
  const [openFiles, setOpenFiles] = useState(false);
  const [keyPopOverFile, setKeyPopOverFile] = useState(0);
  const [openList, setOpenList] = useState(false);
  const [openChat, setOpenChat] = useState(false);
  const [openRmc, setOpenRmc] = useState(false);
  const [openFormCreate, setOpenFormCreate] = useState(false);
  const [openFamilyId] = useState(false);
  const [selectedConvViewSphere, setSelectedConvViewSphere] = useState({});
  const [elementDetailsGridView, setElementDetailsGridView] = useState({});

  const chatSelectedConv = useSelector(
    (state) => state?.ChatRealTime?.selectedConversation
  );

  const { task360 } = useSelector((state) => state.form);

  useEffect(() => {
    if (task360 && Object.keys(task360).length > 0) {
      setSelectedKey("3");
    }
  }, [task360]);
  useEffect(() => {
    if (!openDrawerUpdate) {
      setElementDetailsGridView({});
    }
  }, [openDrawerUpdate]);

  const onChangeTabs = (key) => {
    // setActiveTab(key);
    dispatch(setSearch(""));
    if (key === 9) {
      if (selectedConvViewSphere?.id) {
        dispatch(
          setChatSelectedConversation({
            selectedConversation: {
              ...selectedConvViewSphere,
            },
          })
        );
      }
      dispatch(setActiveTab360(Number(key)));
      setSelectedKeySideBar("");
    } else {
      if (selectedKeySideBar !== "Chat" && !openChat) {
        dispatch(
          setChatSelectedConversation({
            selectedConversation: null,
          })
        );
        if (key !== 5)
          localStorage.setItem("activeTabInteractionsViewSphere", key);
      }
      dispatch(setActiveTab360(Number(key)));
    }
  };
  const tabsItems = [
    {
      label: t("vue360.history"),
      key: 3,
      children: (
        <Overview
          tasksTypes={tasksTypes}
          source={"viewSphere2"}
          contactInfo={contactInfo}
          // addTab={addTab}
          headerHeight={headerHeight}
          dataSteps={dataSteps}
          elementID={contactInfo?.id}
          setIsUpdate={setIsUpdate}
          isUpdate={isUpdate}
          // module={module}
          from={from}
        />
      ),
      closable: false,
    },

    !isGuestConnected() && {
      label: t("menu1.callLog"),
      key: 4,
      children: (
        <CallLogs360
          contactId={contactInfo?.id}
          isStepsDisplayed={headerHeight}
        />
      ),
      closable: false,
    },
    !isGuestConnected() && {
      label: "Email",
      key: 7,
      disabled: dataAccounts?.length < 1,
      children: (
        <div>
          {!acceptedFamilyIds.includes(contactInfo?.family_id) ? (
            <div className="mb-2 flex justify-end">
              <Button
                icon={<MailOutlined />}
                onClick={() => dispatch(setOpenModalEmail(true))}
              >
                {t("mailing.newMail")}
              </Button>
            </div>
          ) : null}

          <Mails contactInfo={contactInfo} setSelectedKey={setSelectedKey} />
        </div>
      ),
      closable: false,
    },
    !isGuestConnected() &&
      user.access["chat"] === "1" && {
        label: t("menu1.chat"),
        key: 9,
        disabled:
          contactInfo?.access_discussion === 0 || selectedKeySideBar === "Chat",
        children: (
          <div
            className={`${
              collapsed && !openView360InDrawer
                ? "chatViewSphereCollapsed"
                : !collapsed && !openView360InDrawer
                ? "chatViewSphere"
                : collapsed && openView360InDrawer
                ? "chatViewSphereCollapsedFromDrawer"
                : "chatViewSphereFromDrawer"
            } `}
          >
            <ChatViewSphere
              elementId={contactInfo?.id}
              headerHeight={headerHeight}
              canCreateRoom={contactInfo?.access_discussion}
              mountChat={mountChat}
              setMountchat={setMountchat}
              setSelectedConvViewSphere={setSelectedConvViewSphere}
              selectedConvViewSphere={selectedConvViewSphere}
            />
          </div>
        ),
        closable: false,
      },
    !isGuestConnected() && {
      label: t("dashboard.socialMedia"),
      key: 5,
      disabled:
        activeTab360 == "5" ||
        user.rmc_access !== "OUI" ||
        listConv?.length === 0
          ? true
          : false,
      children: (
        <ChatRmc
          dataSteps={dataSteps}
          from="viewSphere"
          headerHeight={headerHeight}
          contactInfo={contactInfo}
          listConv={listConv}
        />
      ),
      closable: false,
    },

    // {
    //   label: "Notes",
    //   key: 6,
    //   children: <Notes />,
    //   closable: false,
    // },

    // {
    //   label: t("layout_profile_details.files"),
    //   key: 8,
    //   children: <Files />,
    //   closable: false,
    // },
  ];
  const items = [
    {
      key: "grp",
      label: "",
      type: "group",
      visible: true,

      children: [
        URL_ENV.REACT_APP_CLIENT_NAME !== "ACP" && {
          key: "1",
          id: "dashboard-view-sphere",
          icon: <PieChartOutlined />,
          label: "Dashboard",
          component: (
            <div className="mt-3 flex flex-col gap-y-4">
              <span className="text-lg font-semibold">Dashboard</span>
              <span
                className="overflow-y-auto overflow-x-hidden"
                style={{
                  height: `calc(100vh - ${headerHeight + 132}px)`,
                  marginRight: "-10px",
                  paddingRight: 15,
                }}
              >
                <GridView
                  generalInfo={generalInfo}
                  newInteraction={newInteraction}
                  setSelectedKey={setSelectedKey}
                  contactInfo={contactInfo}
                  kpi={kpi}
                  setFilter={setFilter}
                  height={Math.trunc(windowHeight)}
                  loading={loading}
                  setOpenFields={setOpenFields}
                  isUpdate={isUpdate}
                  setIsUpdate={setIsUpdate}
                  relations={
                    isGuestConnected()
                      ? []
                      : relations?.filter((el) => el.number > 0)
                  }
                  setOpenDrawerUpdate={setOpenDrawerUpdate}
                  setElementDetailsGridView={setElementDetailsGridView}
                  setOpenChat={setOpenChat}
                  setSelectedKeySideBar={setSelectedKeySideBar}
                  setOpenPipeline={setOpenPipeline}
                  openView360InDrawer={openView360InDrawer}
                  setOpenFiles={setOpenFiles}
                  setOpenNotes={setOpenNotes}
                  setOpenList={setOpenList}
                />
              </span>
            </div>
          ),
          visible: true,
        },
        {
          key: "4",
          id: "informations-view-sphere",
          icon: <ContainerOutlined />,
          label: t("vue360.informationSheet"),
          component: (
            <div className="mt-3 flex flex-col gap-y-2">
              <div className="flex items-center justify-between space-x-3">
                <span className="text-lg font-semibold">
                  {t("vue360.informationSheet")}
                </span>
                {/* {!isGuestConnected() && roles.includes(user.role) && (
                  <Button
                    type="primary"
                    icon={<FiFolderPlus />}
                    size="small"
                    onClick={() => setOpenFields(true)}
                    ghost
                  >
                    {t("tasks.openAttachment") + " " + t("menu2.fields")}
                  </Button>
                )} */}
              </div>
              {contactInfo?.id && !loading ? (
                <ProfileDetails
                  familyId={contactInfo?.family_id}
                  contactId={contactInfo?.id}
                  numberOfColumns={3}
                  source="viewSphere"
                  isUpdate={
                    isUpdate ||
                    newInteraction?.type.includes("updateStage") ||
                    refreshInfo
                  }
                  setIsUpdate={
                    newInteraction?.type.includes("updateStage")
                      ? () => {}
                      : refreshInfo
                      ? setRefreshInfo
                      : setIsUpdate
                  }
                  headerHeight={headerHeight}
                />
              ) : null}
            </div>
          ),
          visible: true,
        },
        {
          label: t("layout_profile_details.recentInteractions"),
          id: "interactions-view-sphere",
          icon: <ApartmentOutlined />,
          key: "2",
          component: (
            <Tabs
              // defaultActiveKey={3}
              hideAdd
              onChange={onChangeTabs}
              activeKey={activeTab360}
              // type="editable-card"
              items={tabsItems}
              // onEdit={onEditTabs}
              className="tabs360-general"
            />
          ),
          visible: true,
        },
        // {
        //   key: "5",
        //   icon: <PhoneOutlined />,
        //   label: t("menu1.callLog"),
        //   component: (
        //     <div className="mt-3 flex flex-col gap-y-4">
        //       <span className="text-lg font-semibold">
        //         {t("menu1.callLog")}
        //       </span>
        //       <span>
        //         <CallLogs360
        //           contactId={params?.id || contactInfo?.id}
        //           isStepsDisplayed={dataSteps?.length}
        //         />
        //       </span>
        //     </div>
        //   ),
        // },
        // {
        //   key: "6",
        //   icon: <MailOutlined />,
        //   label: "Mail",
        //   component: (
        //     <div className="mt-3 flex flex-col gap-y-4">
        //       <span className="text-lg font-semibold">Mail</span>
        //       <Mails contactInfo={contactInfo} />
        //     </div>
        //   ),
        // },

        {
          key: "3",
          id: "tasks-view-sphere",
          icon: (
            <span>
              {collapsed && countTasks?.all > 0 ? (
                <Badge
                  count={countTasks?.all}
                  offset={[8, 10]}
                  color="#80808014"
                  style={{ color: "black" }}
                >
                  <CalendarOutlined />
                </Badge>
              ) : (
                <CalendarOutlined />
              )}
            </span>
          ),
          label: (
            <div className="flex items-center justify-between">
              <span>{t("menu1.tasks")}</span>
              {URL_ENV.REACT_APP_CLIENT_NAME !== "ACP" ? (
                <Badge
                  count={countTasks?.all > 0 ? countTasks?.all : 0}
                  color={countTasks?.all > 0 ? "#80808014" : ""}
                  style={{ color: collapsed ? "white" : "black" }}
                />
              ) : null}
            </div>
          ),

          component: (
            <>
              {URL_ENV.REACT_APP_CLIENT_NAME === "ACP" ? null : (
                <div className="mt-3 flex flex-col gap-y-4">
                  <span className="text-lg font-semibold">
                    {t("menu1.tasks")}
                  </span>
                  <span>
                    <TimeLine
                      setOpenChat={setOpenChat}
                      setSelectedKeySideBar={setSelectedKeySideBar}
                      collapsed={collapsed}
                      headerHeight={headerHeight}
                      tasksTypes={tasksTypes}
                      setTasksTypes={setTasksTypes}
                      countTasks={countTasks}
                      setCountTasks={setCountTasks}
                      setOpenModal={setOpenModalCheckList}
                      openModal={openModalCheckList}
                      dataSteps={dataSteps}
                      setKpi={setKpi}
                      kpi={kpi}
                      setFilter={setFilter}
                      listFilter={listFilter}
                      from="viewSphere"
                      contactInfo={contactInfo}
                      setSelectedKey={setSelectedKey}
                      relations={relations?.filter(
                        (el) =>
                          el.number > 0 &&
                          (el.family_id === 4 ||
                            el.family_id === 1 ||
                            el.family_id === 2)
                      )}
                    />
                  </span>
                </div>
              )}
            </>
          ),
          visible: true,
        },
        isGuestConnected() &&
          process.env.REACT_APP_BRANCH === "devLocal" &&
          user?.access?.["chat"] === "1" && {
            key: "chat",
            disabled: contactInfo?.access_discussion === 0,
            icon: (
              <span>
                <Badge count={chatInViewSphere?.number} offset={[3, 0]}>
                  <MessageOutlined className="text-gray-400" />
                </Badge>{" "}
                {/* {collapsed && countTasks?.all > 0 ? (
                <Badge
                  count={countTasks?.all}
                  offset={[8, 10]}
                  color="#80808014"
                  style={{ color: "black" }}
                >
                  <HiOutlinePencilAlt style={{ fontSize: "21px" }} />
                </Badge>
              ) : (
                <HiOutlinePencilAlt style={{ fontSize: "21px" }} />
              )} */}
              </span>
            ),
            label: (
              <div className="flex items-center justify-between">
                <span>{t("menu1.chat")}</span>
                {/* <Badge
                count={5}
                color="#80808014"
                style={{ color: collapsed ? "white" : "black" }}
              /> */}
              </div>
            ),

            component: (
              <>
                <div className="mt-3 flex flex-col gap-y-4">
                  <span className="text-lg font-semibold">
                    {t("menu1.chat")}
                  </span>
                  <div
                    className={`${
                      collapsed
                        ? "chatViewSphereCollapsedGuest"
                        : "chatViewSphereGuest"
                    } `}
                  >
                    <ChatViewSphere
                      elementId={contactInfo?.id}
                      headerHeight={headerHeight}
                      canCreateRoom={contactInfo?.access_discussion}
                      mountChat={mountChat}
                      setMountchat={setMountchat}
                      setSelectedConvViewSphere={setSelectedConvViewSphere}
                      selectedConvViewSphere={selectedConvViewSphere}
                    />
                  </div>
                </div>
              </>
            ),
            visible: true,
          },

        !isGuestConnected() && {
          key: "note_key",
          id: "notes-view-sphere",
          icon: (
            <span>
              <HiOutlinePencilAlt style={{ fontSize: "16px" }} />
              {/* {collapsed && countTasks?.all > 0 ? (
                <Badge
                  count={countTasks?.all}
                  offset={[8, 10]}
                  color="#80808014"
                  style={{ color: "black" }}
                >
                  <HiOutlinePencilAlt style={{ fontSize: "21px" }} />
                </Badge>
              ) : (
                <HiOutlinePencilAlt style={{ fontSize: "21px" }} />
              )} */}
            </span>
          ),
          label: (
            <div className="flex items-center justify-between">
              <span>Notes</span>
              <Badge
                count={
                  kpi?.find((el) => el.title === "Notes")?.value > 0
                    ? kpi?.find((el) => el.title === "Notes")?.value
                    : 0
                }
                color={
                  kpi?.find((el) => el.title === "Notes")?.value > 0
                    ? "#80808014"
                    : ""
                }
                style={{ color: collapsed ? "white" : "black" }}
              />
              {/* <Badge
                count={5}
                color="#80808014"
                style={{ color: collapsed ? "white" : "black" }}
              /> */}
            </div>
          ),

          component: (
            <>
              <div className="mt-3 flex flex-col gap-y-4">
                <span className="text-lg font-semibold">Notes</span>
                <span>
                  <NotesList contactInfo={contactInfo} />
                  {/* <TimeLine
                      collapsed={collapsed}
                      headerHeight={headerHeight}
                      tasksTypes={tasksTypes}
                      setTasksTypes={setTasksTypes}
                      countTasks={countTasks}
                      setCountTasks={setCountTasks}
                      setOpenModal={setOpenModalCheckList}
                      openModal={openModalCheckList}
                      dataSteps={dataSteps}
                      setKpi={setKpi}
                      kpi={kpi}
                      from="viewSphere"
                      contactInfo={contactInfo}
                      setSelectedKey={setSelectedKey}
                    /> */}
                </span>
              </div>
            </>
          ),
          visible: true,
        },
        isGuestConnected() && {
          key: "comment",
          id: "comment-guest-view-sphere",
          icon: (
            <span>
              <CommentOutlined />
            </span>
          ),
          label: (
            <div className="flex items-center justify-between">
              <span>{t("tasks.commentsTab")}</span>
              {/* <Badge
                count={5}
                color="#80808014"
                style={{ color: collapsed ? "white" : "black" }}
              /> */}
            </div>
          ),

          component: (
            <>
              <div className="mt-3 flex flex-col gap-y-4">
                <span className="text-lg font-semibold">
                  {t("tasks.commentsTab")}
                </span>
                <span>
                  <Notes
                    headerHeight={headerHeight}
                    setKpi={setKpi}
                    contactInfo={contactInfo}
                  />
                </span>
              </div>
            </>
          ),
          visible: true,
        },
        isGuestConnected() && {
          id: "files-guest-view-sphere",
          key: "Files",
          icon: (
            <span>
              <FileAddOutlined />
            </span>
          ),
          label: (
            <div className="flex items-center justify-between">
              <span> {t("layout_profile_details.files")}</span>
              {/* <Badge
                count={5}
                color="#80808014"
                style={{ color: collapsed ? "white" : "black" }}
              /> */}
            </div>
          ),

          component: (
            <>
              <div className="mt-3 flex flex-col gap-y-4">
                <span className="text-lg font-semibold">
                  {t("layout_profile_details.files")}
                </span>
                <span>
                  <Files
                    headerHeight={headerHeight}
                    setKpi={setKpi}
                    contactInfo={contactInfo}
                  />
                </span>
              </div>
            </>
          ),
          visible: true,
        },
        // {
        //   label: t("helpDesk.folder"),
        //   icon: <FolderOutlined />,
        //   key: "7",
        //   component: (
        //     <Tabs
        //       // defaultActiveKey={3}
        //       hideAdd
        //       onChange={onChangeTabs}
        //       activeKey={activeTab360}
        //       // type="editable-card"
        //       items={tabsItemsFiles}
        //       // onEdit={onEditTabs}
        //       className="tabs360-general"
        //     />
        //   ),
        //   visible: true,
        // },

        process.env.REACT_APP_BRANCH !== "prod" && {
          label: t("layout_profile_details.cart"),
          key: "8",
          icon: <ShoppingCartOutlined />,
          component: <Cart />,
          visible: true,
        },
      ],
    },

    !isGuestConnected() && {
      type: "divider",
      visible: true,
    },
    !isGuestConnected() && {
      key: "grp3",
      label: (
        <div
          className={`flex items-center ${
            collapsed ? "justify-center" : "justify-between"
          }`}
        >
          <span>{!collapsed ? "Association" : null}</span>

          <span style={{ marginRight: !collapsed && "-9px" }}>
            <RelationsViewSphere
              relations={relations}
              setRelations={setRelations}
              disabled={disabledRelation}
              setSelectedItem={setSelectedItem}
              contactInfo={contactInfo}
              setListConv={setListConv}
              btn={
                <Tooltip title={t("voip.associate")}>
                  <Button
                    ref={Refs_IDs.addAssociationViewSphere}
                    icon={<PlusOutlined />}
                    type="link"
                    shape="circle"
                    // onClick={(e) => e.stopPropagation()}
                  />
                </Tooltip>
              }
            />
          </span>
        </div>
      ),
      type: "group",
      visible: true,

      children: [
        ...relations
          ?.filter((el) => el.number > 0)
          .map((el, i) => ({
            label: (
              <div className="group/item flex items-center justify-between">
                <span>
                  {
                    familyIcons(t).find((fam) => fam.value === el.family_id)
                      ?.label
                  }
                </span>
                <span className="flex items-center">
                  <div className="group/edit   hidden group-hover/item:block ">
                    <div className="-mr-2 flex  items-center">
                      {
                        <RelationsViewSphere
                          module={el}
                          relations={relations}
                          setRelations={setRelations}
                          setSelectedItem={setSelectedItem}
                          disabled={el.typeRelation === 1}
                          contactInfo={contactInfo}
                          setListConv={setListConv}
                          btn={
                            el.fieldType !== "multiselect" &&
                            el.type_relation === 0 && (
                              <Tooltip title={t("vue360.oneRelation")}>
                                <Button
                                  disabled={el.typeRelation === 1}
                                  icon={<SwapOutlined />}
                                  type="link"
                                  shape="circle"
                                  onClick={(e) => e.stopPropagation()}
                                />
                              </Tooltip>
                            )
                          }
                        />
                      }

                      {/* <Tooltip title={t("voip.add")}>
                            <Button
                              type="link"
                              icon={<PlusOutlined />}
                              // className="associateViewSphere text-black hover:text-black hover:opacity-60"
                              shape="circle"
                              onClick={() => {
                                setOpenFamilyId(el.family_id);
                                setOpenFormCreate(true);
                              }}
                            />
                          </Tooltip> */}
                    </div>
                    {/* <Dropdown
                          key={el?.family_id}
                          arrow
                          autoAdjustOverflow
                          menu={{
                            items: itemsRelations,
                            onClick: (e) => {
                              e.domEvent.stopPropagation();
                              if (e.key === "0") {
                                // setFamilyId(1);
                              }
                              if (e.key === "1") {
                                setOpenFamilyId(el.family_id);
                                setOpenFormCreate(true);
                              }
                            },
                          }}
                          trigger={["click"]}
                        >
                          <MoreOutlined
                            onClick={(e) => {
                              e.stopPropagation();
                              setFamilyId(el.family_id);
                            }}
                          />
                        </Dropdown> */}
                  </div>
                  {!collapsed ? (
                    <div className="group/edit block  group-hover/item:hidden">
                      <Badge
                        count={el.number > 0 ? el.number : 0}
                        color="#80808014"
                        style={{ color: collapsed ? "white" : "black" }}
                        overflowCount={999}
                      />
                    </div>
                  ) : (
                    <span className="pl-1">
                      {el.number > 99 ? "+99" : el.number}
                    </span>
                  )}
                </span>
              </div>
            ),
            key: el?.label,
            // familyId: el.family_id,
            component: (
              <ListItemsRelations
                openView360InDrawer={openView360InDrawer}
                familyId={el.family_id}
                list={el.child}
                setRelations={setRelations}
                setSelectedItem={setSelectedItem}
                selectedItem={selectedItem}
                headerHeight={headerHeight}
                data={el}
                contactInfo={contactInfo}
                setSelectedKey={setSelectedKey}
                setOpenChat={setOpenChat}
                setSelectedKeySideBar={setSelectedKeySideBar}
                setListConv={setListConv}
                setElementDetailsGridView={setElementDetailsGridView}
                elementDetailsGridView={elementDetailsGridView}
                openDrawerUpdate={openDrawerUpdate}
                setOpenDrawerUpdate={setOpenDrawerUpdate}
              />
            ),

            icon: (
              <span>
                {collapsed ? (
                  <span className="absolute left-[10px] top-[12px] flex items-center gap-x-1 text-gray-800">
                    <span>
                      {
                        familyIcons(t).find((fa) => fa.value == el.family_id)
                          ?.icon
                      }
                    </span>
                    <span className="text-xs">
                      {el?.number > 99
                        ? "+99"
                        : el.number > 0
                        ? el.number
                        : null}
                    </span>
                  </span>
                ) : (
                  familyIcons(t).find((fa) => fa.value == el.family_id)?.icon
                )}
              </span>
            ),
            // visible: el.number > 0,
          })),
      ],
    },
    {
      key: "grp4",
      label: "Dossier",
      type: "group",
      visible: false,

      children: [
        {
          label: "Notes",
          key: "5",
          component: <Notes headerHeight={headerHeight} />,
          icon: <FileTextOutlined />,
          visible: false,
        },
        {
          label: t("layout_profile_details.files"),
          key: "6",
          component: <Files headerHeight={headerHeight} />,
          icon: <FileAddOutlined />,
          visible: false,
        },
      ],
    },
  ];
  const itemsSidebar = [
    // {
    //   label: "Mail",
    //   icon: <MailOutlined />,
    //   key: "mail",

    //   closable: false,
    // },
    {
      label: t("menu1.callLog"),
      id: "raccourci-log-calls-view-sphere",
      icon: (
        <HiOutlinePhone
          style={{ fontSize: "16px", position: "relative", right: "-3px" }}
        />
      ),
      key: "phone",
    },
    {
      label: "Email",
      id: "raccourci-emails-view-sphere",
      icon: <MailOutlined style={{ position: "relative", right: "-3px" }} />,
      key: "mail",
    },

    {
      type: "divider",
      visible: true,
    },
    user?.access?.["chat"] === "1" && {
      label: openChat ? "" : t("menu1.chat"),
      key: "Chat",
      id: "raccourciChatViewSphere",
      disabled:
        activeTab360 == "9" || contactInfo?.access_discussion === 0
          ? true
          : false,

      icon: (
        <Popover
          key={contactInfo?.id}
          overlayStyle={{
            zIndex: isFullScreenChat || openView360InDrawer ? 1200 : 130,
          }}
          content={
            <div
              style={{
                width: isFullScreenChat
                  ? `calc(${openView360InDrawer ? "1080px" : "100vw"} - 145px)`
                  : "600px",
                paddingLeft: isFullScreenChat ? "8px" : 0,
                height: "calc(100vh - 27px)",
              }}
            >
              <span
                className=" mb-4 flex items-center justify-between space-x-1"
                style={{ borderBottom: "1px solid #80808014" }}
              >
                <Typography.Title level={5}>{t("menu1.chat")}</Typography.Title>
                <div>
                  <Tooltip
                    title={t("chat.goto") + " " + t("dashboard.socialMedia")}
                  >
                    <Button
                      type="text"
                      icon={<SelectOutlined />}
                      onClick={(e) => {
                        e.stopPropagation();
                        setOpenChat(false);
                        setSelectedKey("2");
                        setSelectedKeySideBar("");
                        dispatch(setActiveTab360(9));
                      }}
                    />
                  </Tooltip>
                  <Tooltip title={t("vue360.maximize")}>
                    <Button
                      type="text"
                      icon={
                        !isFullScreenChat ? (
                          <FullscreenOutlined />
                        ) : (
                          <FullscreenExitOutlined />
                        )
                      }
                      onClick={(e) => {
                        e.stopPropagation();

                        // setOpenChat(true);

                        // e.stopPropagation();
                        setIsFullScreenChat(!isFullScreenChat);
                      }}
                    />
                  </Tooltip>

                  <Tooltip title={t("vue360.minimize")}>
                    <Button
                      type="text"
                      icon={<MinusOutlined />}
                      onClick={(e) => {
                        e.stopPropagation();
                        setOpenChat(false);
                        // dispatch(setOpenChatInViewSPhere(false));
                        // dispatch(
                        //   updateChatSelectedConversation({
                        //     external: true,
                        //   })
                        // );
                        setSelectedKeySideBar("");
                      }}
                    />
                  </Tooltip>
                </div>
              </span>
              <span
                className={`${
                  isFullScreenChat && !openView360InDrawer
                    ? "chatPopoverMax"
                    : !isFullScreenChat && !openView360InDrawer
                    ? "chatPopover"
                    : isFullScreenChat && openView360InDrawer
                    ? "chatPopoverDrawerMax"
                    : "chatPopover"
                } `}
              >
                {openChat && (
                  <ChatViewSphere
                    from="menuRight"
                    elementId={contactInfo?.id}
                    headerHeight={headerHeight}
                    canCreateRoom={contactInfo?.access_discussion}
                    mountChat={mountChat}
                    setSelectedConvViewSphere={setSelectedConvViewSphere}
                    selectedConvViewSphere={selectedConvViewSphere}
                    setMountchat={setMountchat}
                  />
                )}
              </span>
            </div>
          }
          trigger="click"
          placement="left"
          open={
            activeTab360 == "9" || contactInfo?.access_discussion === 0
              ? false
              : openChat
          }
          // onOpenChange={(set) => {
          //   console.log(set);
          //   if (set) {
          //     setOpenChat(true);

          //     setSelectedKeySideBar("chat");
          //     if (selectedConvViewSphere?.id) {
          //       dispatch(
          //         setChatSelectedConversation({
          //           selectedConversation: {
          //             ...selectedConvViewSphere,
          //           },
          //         })
          //       );
          //     }
          //   }
          // }}
        >
          <span className="">
            <Badge
              count={
                openView360InDrawer
                  ? chatInViewSphereFromDrawer?.number
                  : chatInViewSphere?.number
              }
              showZero
              offset={[8, 10]}
              style={{
                visibility:
                  openChat && chatSelectedConv?.type === "room"
                    ? "hidden"
                    : activeTab360 !== 9 &&
                      contactInfo?.access_discussion === 1 &&
                      ((openView360InDrawer &&
                        chatInViewSphereFromDrawer?.number > 0) ||
                        (!openView360InDrawer && chatInViewSphere?.number > 0))
                    ? "visible"
                    : "hidden",
              }}
            >
              <MessageOutlined
                style={{
                  background: activeTab360 == "9" && "transparent",
                  color: activeTab360 == "9" && "#00000040",
                  position: "relative",
                  right: "-3px",
                }}
              />
            </Badge>
          </span>
          {/* <MessageOutlined /> */}
        </Popover>
      ),
    },
    {
      label: openRmc ? "" : t("dashboard.socialMedia"),
      key: "RMC",
      id: "raccourciRmcViewSphere",
      disabled:
        activeTab360 == "5" ||
        user.rmc_access !== "OUI" ||
        listConv?.length === 0
          ? true
          : false,
      icon: (
        <Popover
          key={contactInfo?.id}
          content={
            <div
              style={{
                width: isFullScreenRmc
                  ? `calc(${openView360InDrawer ? "1080px" : "100vw"} - 145px)`
                  : "600px",
                paddingLeft: isFullScreenRmc ? "8px" : 0,
                height: "calc(100vh - 28px)",
              }}
            >
              <span
                className=" mb-4 flex items-center justify-between space-x-1"
                style={{ borderBottom: "1px solid #80808014" }}
              >
                <Typography.Title level={5}>
                  {t("dashboard.socialMedia")}
                </Typography.Title>
                <div>
                  <Tooltip
                    tilte={t("chat.goto") + " " + t("dashboard.socialMedia")}
                  >
                    <Button
                      type="text"
                      icon={<SelectOutlined />}
                      onClick={(e) => {
                        e.stopPropagation();
                        setOpenRmc(false);
                        setSelectedKey("2");
                        dispatch(setActiveTab360(5));
                        setSelectedKeySideBar("");
                      }}
                    />
                  </Tooltip>
                  <Tooltip title={t("vue360.maximize")}>
                    <Button
                      type="text"
                      icon={
                        !isFullScreenRmc ? (
                          <FullscreenOutlined />
                        ) : (
                          <FullscreenExitOutlined />
                        )
                      }
                      onClick={(e) => {
                        // e.stopPropagation();
                        setIsFullScreenRmc(!isFullScreenRmc);
                      }}
                    />
                  </Tooltip>
                  <Tooltip title={t("vue360.minimize")}>
                    <Button
                      type="text"
                      icon={<MinusOutlined />}
                      onClick={(e) => {
                        e.stopPropagation();
                        setOpenRmc(false);
                        setSelectedKeySideBar("");
                      }}
                    />
                  </Tooltip>
                </div>
              </span>
              {/* {openRmc && ( */}
              <ChatRmc
                dataSteps={dataSteps}
                from="viewSphere"
                source="popover"
                headerHeight={headerHeight}
                contactInfo={contactInfo}
                listConv={listConv}
              />
              {/* )} */}
            </div>
          }
          trigger="click"
          placement="left"
          open={
            activeTab360 == "5" ||
            listConv?.length === 0 ||
            user.rmc_access === "NON"
              ? false
              : openRmc
          }
          onOpenChange={(set) => {
            if (activeTab360 != "5") {
              setOpenRmc(set);
              !set && setSelectedKeySideBar("");
            }
          }}
        >
          <span>
            <Badge
              count={user?.rmc_access === "OUI" ? listConv?.length : 0}
              offset={[8, 10]}
              color={
                user?.rmc_access === "OUI" && listConv?.length > 0
                  ? "#80808014"
                  : ""
              }
              style={{ color: "black" }}
            >
              <ShareAltOutlined
                style={{
                  background: activeTab360 == "5" && "transparent",
                  color: activeTab360 == "5" && "#00000040",
                  position: "relative",
                  right: "-3px",
                }}
              />
            </Badge>
          </span>
        </Popover>
      ),
    },
    {
      type: "divider",
      visible: true,
    },
    {
      label: t("tasks.commentsTab"),
      key: "Notes",
      id: "commentsViewSphere",
      disabled: URL_ENV.REACT_APP_CLIENT_NAME === "ACP",

      icon: (
        <Popover
          key={contactInfo?.id}
          content={
            <div
              style={{
                width: isFullScreenNotes
                  ? `calc(${openView360InDrawer ? "1080px" : "100vw"} - 145px)`
                  : "650px",
                paddingLeft: isFullScreenNotes ? "8px" : 0,
                height: "calc(100vh - 27px)",
              }}
              // tabIndex={0}
              // onFocus={() => {
              //   console.log("focus");
              // }}
              // onBlur={() => console.log("blur")}
              // onClick={() => console.log("click")}
              // onChange={() => console.log("change")}
            >
              <span
                className="mb-4  flex items-center justify-between space-x-1 "
                style={{ borderBottom: "1px solid #80808014" }}
              >
                <Typography.Title level={5}>
                  {t("tasks.commentsTab")}
                </Typography.Title>
                <div>
                  <Tooltip title={t("vue360.maximize")}>
                    <Button
                      type="text"
                      icon={
                        !isFullScreenNotes ? (
                          <FullscreenOutlined />
                        ) : (
                          <FullscreenExitOutlined />
                        )
                      }
                      onClick={(e) => {
                        // e.stopPropagation();
                        setIsFullScreenNotes(!isFullScreenNotes);
                      }}
                    />
                  </Tooltip>
                  <Tooltip title={t("vue360.minimize")}>
                    <Button
                      type="text"
                      icon={<MinusOutlined />}
                      onClick={(e) => {
                        e.stopPropagation();
                        setOpenNotes(false);
                        setSelectedKeySideBar("");
                      }}
                    />
                  </Tooltip>
                </div>
              </span>
              <Notes
                headerHeight={headerHeight}
                from="sidebarViewSphere"
                setKpi={setKpi}
                contactInfo={contactInfo}
              />
            </div>
          }
          trigger="click"
          placement="left"
          open={URL_ENV.REACT_APP_CLIENT_NAME === "ACP" ? false : openNotes}
          onOpenChange={(set) => {
            setOpenNotes(set);
            !set && setSelectedKeySideBar("");
          }}
        >
          <span>
            <Badge
              count={
                kpi.find((el) => el.title === "Comments")?.value > 0
                  ? kpi.find((el) => el.title === "Comments")?.value
                  : 0
              }
              offset={[8, 10]}
              color={
                kpi.find((el) => el.title === "Comments")?.value > 0
                  ? "#80808014"
                  : ""
              }
              style={{ color: "black" }}
            >
              <CommentOutlined
                style={{ position: "relative", right: "-3px" }}
              />
            </Badge>
          </span>
        </Popover>
      ),
    },
    {
      label: t("layout_profile_details.files"),
      key: "Files",
      id: "filesViewSphere",
      disabled: URL_ENV.REACT_APP_CLIENT_NAME === "ACP",
      icon: (
        <Popover
          key={keyPopOverFile}
          content={
            <div
              style={{
                width: isFullScreenFiles
                  ? `calc(${openView360InDrawer ? "1080px" : "100vw"} - 145px)`
                  : "600px",
                paddingLeft: isFullScreenFiles ? "8px" : 0,
                height: "calc(100vh - 27px)",
              }}
            >
              <span
                className=" mb-4  flex items-center justify-between space-x-1 "
                style={{ borderBottom: "1px solid #80808014" }}
              >
                <Typography.Title level={5}>
                  {t("layout_profile_details.files")}
                </Typography.Title>
                <div>
                  <Tooltip title={t("vue360.maximize")}>
                    <Button
                      type="text"
                      icon={
                        !isFullScreenFiles ? (
                          <FullscreenOutlined />
                        ) : (
                          <FullscreenExitOutlined />
                        )
                      }
                      onClick={(e) => {
                        // e.stopPropagation();
                        setIsFullScreenFiles(!isFullScreenFiles);
                      }}
                    />
                  </Tooltip>
                  <Tooltip title={t("vue360.minimize")}>
                    <Button
                      type="text"
                      icon={<MinusOutlined />}
                      onClick={(e) => {
                        e.stopPropagation();
                        setOpenFiles(false);
                        setSelectedKeySideBar("");
                      }}
                    />
                  </Tooltip>
                </div>
              </span>
              <Files
                headerHeight={headerHeight}
                from="sidebarViewSphere"
                setKpi={setKpi}
                contactInfo={contactInfo}
                open={openFiles}
              />
            </div>
          }
          trigger="click"
          placement="left"
          open={URL_ENV.REACT_APP_CLIENT_NAME === "ACP" ? false : openFiles}
          onOpenChange={(set) => {
            if (set) {
              setKeyPopOverFile((prev) => prev + 1);
            }
            setOpenFiles(set);
            !set && setSelectedKeySideBar("");
          }}
        >
          <span>
            <Badge
              count={
                kpi.find((el) => el.title === "Files")?.value > 0
                  ? kpi.find((el) => el.title === "Files")?.value
                  : ""
              }
              offset={[8, 10]}
              color={
                kpi.find((el) => el.title === "Files")?.value > 0
                  ? "#80808014"
                  : ""
              }
              style={{ color: "black" }}
            >
              <FileAddOutlined
                style={{ position: "relative", right: "-3px" }}
              />
            </Badge>
          </span>
        </Popover>
      ),
    },
    {
      label: "List",
      key: "List",
      id: "list-view-sphere",
      disabled: URL_ENV.REACT_APP_CLIENT_NAME === "ACP",
      icon: (
        <Popover
          key={contactInfo?.id}
          content={
            <div
              style={{
                width: isFullScreenList
                  ? `calc(${openView360InDrawer ? "1080px" : "100vw"} - 145px)`
                  : "600px",
                paddingLeft: isFullScreenList ? "8px" : 0,
                height: "calc(100vh - 27px)",
              }}
            >
              <span
                className=" mb-4  flex items-center justify-between space-x-1 "
                style={{ borderBottom: "1px solid #80808014" }}
              >
                <Typography.Title level={5}>
                  {t("vue360.todoList")}
                </Typography.Title>
                <div>
                  <Tooltip title={t("vue360.maximize")}>
                    <Button
                      type="text"
                      icon={
                        !isFullScreenList ? (
                          <FullscreenOutlined />
                        ) : (
                          <FullscreenExitOutlined />
                        )
                      }
                      onClick={(e) => {
                        // e.stopPropagation();
                        setIsFullScreenList(!isFullScreenList);
                      }}
                    />
                  </Tooltip>

                  <Tooltip title={t("vue360.minimize")}>
                    <Button
                      type="text"
                      icon={<MinusOutlined />}
                      onClick={(e) => {
                        e.stopPropagation();
                        setOpenList(false);
                        setSelectedKeySideBar("");
                      }}
                    />
                  </Tooltip>
                </div>
              </span>

              <CheckList
                headerHeight={headerHeight}
                openModal={openModalCheckList}
                setOpenModal={setOpenModalCheckList}
                setKpi={setKpi}
                from={from}
                contactInfo={contactInfo}
              />
            </div>
          }
          trigger="click"
          placement="left"
          open={URL_ENV.REACT_APP_CLIENT_NAME === "ACP" ? false : openList}
          onOpenChange={(set) => {
            if (openModalCheckList) {
              setOpenList(true);
            } else {
              setOpenList(set);
              !set && setSelectedKeySideBar("");
            }
          }}
        >
          <span>
            <Badge
              count={
                kpi.find((el) => el.title === "Todolist")?.value > 0
                  ? kpi.find((el) => el.title === "Todolist")?.value
                  : ""
              }
              offset={[8, 10]}
              color={
                kpi.find((el) => el.title === "Todolist")?.value > 0
                  ? "#80808014"
                  : ""
              }
              style={{ color: "black" }}
            >
              <FiList style={{ position: "relative", right: "-3px" }} />
            </Badge>
          </span>
        </Popover>
      ),
    },
    // { label: "Activity", key: "Activity", icon: <CalendarOutlined /> },
  ];

  const dispatch = useDispatch();

  // Handle changes in reasons form.
  const handleChooseReasons = (changedFields, allFields) => {
    if (changedFields[0]?.name === "reasonType") {
      finalStageForm.resetFields("reasons");
    }
  };
  const handleChangeReasonsItems = (e) => {
    setSelectedReasonType(e?.target?.value);
  };
  const subItems = items
    .filter((el) => el.children)
    .map((el) => el.children)
    .flat();
  return (
    <>
      {!loading && (
        <Layout className="relative bg-white">
          {/* <span className="w-4 bg-white"></span> */}
          <Sider
            width={180}
            className={`${
              openView360InDrawer
                ? "ViewSphereExterne"
                : from === "viewSphere"
                ? "viewSphere"
                : "ViewSphereExterne"
            } relative `}
            style={{
              background: "#FAFCFD",
              height: openView360InDrawer
                ? `calc(100vh - ${
                    headerHeight + (from === "viewSphere" ? 104 : 90)
                  })`
                : "auto",
              //   margin: "8px",
              //   marginRight: "-2px",
              //   background: "#F8FAFC",
              // height: `calc(100vh - ${headerHeight + 124}px)`,
              // minHeight: "100%",
              // overflow: "auto",
            }}
            collapsible
            collapsed={collapsed}
            onCollapse={(value) => setCollapsed(value)}
          >
            {/* <div className="flex space-x-[2px]  px-[2px] py-2">
        <Input placeholder="Recherche ..." prefix={<SearchOutlined />} />
        <Button icon={<SettingOutlined />} shape="circle" type="text" />
      </div> */}
            {/* <Collapse
            bordered={false}
            defaultActiveKey={["1"]}
            expandIcon={({ isActive }) => (
              <CaretRightOutlined rotate={isActive ? 90 : 0} />
            )}
            style={{
              background: token.colorBgContainer,
            }}
            items={getItems(panelStyle)}
          /> */}

            <Menu
              selectedKeys={[selectedKey]}
              mode="inline"
              style={{
                background: "transparent",
                height: `calc(100vh - ${
                  headerHeight +
                  (from === "viewSphere"
                    ? openView360InDrawer
                      ? 106
                      : 174
                    : 120)
                }px)`,
                // minHeight: "100%",
                overflow: "auto",
              }}
              onClick={(item) => {
                setSelectedKey(item?.key);

                dispatch(setActiveMenu360(item?.key));
                //  if (typeof item.key !== "string")
                // dispatch(setOpenChatInViewSPhere(false));
                if (item.key === "1") {
                  localStorage.setItem("selectedKeyViewSphere", item?.key);
                  dispatch(setActiveTab360(null));
                  localStorage.removeItem("activeTabInteractionsViewSphere");
                }

                if (item.key === "2") {
                  localStorage.setItem("selectedKeyViewSphere", item?.key);
                  localStorage.setItem("activeTabInteractionsViewSphere", 3);
                  dispatch(setActiveTab360(3));
                }
                if (item.key === "3") {
                  localStorage.setItem("selectedKeyViewSphere", item?.key);
                  dispatch(setActiveActivity360("1"));
                  localStorage.removeItem("activeTabInteractionsViewSphere");

                  dispatch(setActiveTab360(null));
                }
                if (item.key === "4") {
                  localStorage.setItem("selectedKeyViewSphere", item?.key);
                  localStorage.removeItem("activeTabInteractionsViewSphere");

                  setRefreshInfo(true);
                }
                if (item.key === "7") {
                  localStorage.setItem("selectedKeyViewSphere", item?.key);
                  localStorage.setItem("activeTabInteractionsViewSphere", 6);
                  dispatch(setActiveTab360(6));
                }
                if (item.key === "note_key") {
                  localStorage.setItem("selectedKeyViewSphere", item?.key);
                  localStorage.removeItem("activeTabInteractionsViewSphere");
                }
                if (typeof item.key === "string") {
                  setSelectedItem(
                    relations.find((el) => el.label === item.key)?.child[0]
                  );
                }
              }}
              items={items.filter((el) => el.visible)}
              className={
                collapsed
                  ? "menuViewSphereLeft"
                  : "menu1ViewSphere menuViewSphereLeft"
              }
              // className="menuViewSphere"
            />
          </Sider>

          <Layout
          //   style={{
          //     padding: "0 24px 24px",
          //   }}
          >
            <Content
              style={{
                margin: 0,
                minHeight: 280,
                // background: "#f8fafc",
                // backgroundColor: "#f8fbff",
                height: `calc(100vh - ${headerHeight + 58 + "px"})`,

                background: "white",
                // borderRadius: borderRadiusLG,
                // border: "2px solid #f9fafb",
              }}
            >
              <div className="flex w-full justify-between gap-0">
                <div
                  className="w-full px-3 py-2"
                  style={{
                    width: isGuestConnected() ? "99%" : "calc(100% - 75px)",
                  }}
                >
                  {
                    // idModule ? (
                    //   <TableInterface familyId={idModule} relation_id={params?.id} />
                    // ) :
                    subItems?.find((el) => el.key === selectedKey) &&
                    subItems?.find((el) => el.key === selectedKey)?.component
                      ? subItems?.find((el) => el.key === selectedKey)
                          ?.component
                      : null
                  }
                </div>
                {/* // bg-gradient-to-t from-blue-50 to-slate-50 */}
                {!isGuestConnected() ? (
                  <div
                    className={` w-[75px] overflow-y-auto overflow-x-hidden shadow-sm
              `}
                    // style={{ height: `calc(100vh - ${headerHeight + 100}px) ` }}
                    style={{
                      background: "#FAFCFD",
                    }}
                  >
                    <Menu
                      style={{
                        height: `calc(100vh - ${headerHeight + 57}px) `,
                        display: "flex",
                        flexDirection: "column",
                        justifyContent: "center",
                        background: "transparent",
                        width: "75px",
                      }}
                      // theme="dark"
                      className="menuViewSphere"
                      selectedKeys={[selectedKeySideBar]}
                      mode="inline"
                      inlineCollapsed={true}
                      items={itemsSidebar}
                      onClick={(item) => {
                        // dispatch(setOpenChatInViewSPhere(false));

                        if (item.key === "mail") {
                          setSelectedKey("2");
                          dispatch(setActiveTab360(7));

                          localStorage.setItem("selectedKeyViewSphere", 2);
                          localStorage.setItem(
                            "activeTabInteractionsViewSphere",
                            7
                          );
                          // dispatch(setOpenModalEmail(true));
                        }
                        if (item.key === "phone") {
                          setSelectedKey("2");
                          dispatch(setActiveTab360(4));
                          localStorage.setItem("selectedKeyViewSphere", 2);
                          localStorage.setItem(
                            "activeTabInteractionsViewSphere",
                            4
                          );
                          // dispatch(setOpenModalEmail(true));
                        } else if (item.key === "Notes") {
                          setOpenChat(false);
                          setSelectedKeySideBar(item.key);
                          setOpenNotes(true);
                        } else if (item.key === "Files") {
                          setOpenChat(false);
                          setSelectedKeySideBar(item.key);
                          setOpenFiles(true);
                        } else if (item.key === "List") {
                          setOpenChat(false);
                          setSelectedKeySideBar(item.key);
                          setOpenList(true);
                          // setSelectedKey("3");
                          // dispatch(setActiveActivity360("7"));
                          // setOpenModalCheckList(true);
                        } else if (item.key === "Activity") {
                          setOpenChat(false);
                          setSelectedKeySideBar(item.key);
                          setOpenTask(true);
                        } else if (item.key === "Chat") {
                          if (
                            activeTab360 != "9" &&
                            contactInfo?.access_discussion === 1
                          ) {
                            setSelectedKeySideBar(item.key);

                            if (!openChat && selectedKeySideBar !== "Chat") {
                              setOpenChat(true);
                              setSelectedKeySideBar(item.key);
                              if (selectedConvViewSphere?.id) {
                                dispatch(
                                  setChatSelectedConversation({
                                    selectedConversation: {
                                      ...selectedConvViewSphere,
                                    },
                                  })
                                );
                              }
                            }
                            // else {
                            //   setSelectedKeySideBar("");
                            //   setTimeout(() => {
                            //     dispatch(
                            //       setChatSelectedConversation({
                            //         selectedConversation: null,
                            //       })
                            //     );
                            //   }, 200);
                            // }
                            // setOpenChat((prev) => !prev);
                          }
                          // dispatch(setOpenChatInViewSPhere(true));
                        } else if (item.key === "RMC") {
                          if (
                            user.rmc_access === "OUI" &&
                            listConv?.length > 0
                          ) {
                            setSelectedKeySideBar(item.key);
                            // dispatch(setActiveTab360(5));
                            setMountchat(true);
                            setOpenRmc(true);
                          }
                        }
                      }}
                    />
                  </div>
                ) : null}
              </div>
            </Content>
          </Layout>
        </Layout>
      )}
      {/* {openModalEmail && !openView360InDrawer && !isGuestConnected() && (
        <Suspense
          fallback={
            <div className="fixed inset-0 z-[9999] flex h-full w-full   items-center justify-center bg-black/5 ">
              <Loader size="2rem" />
            </div>
          }
        >
          <ModalMessage
            title={t("chat.header.shareWithEmail")}
            setSelectedKey={setSelectedKeySideBar}
            receiver={
              senderAssoc
                ? senderAssoc
                : Array.isArray(contactInfo?.email) &&
                  contactInfo?.email.length > 0
                ? contactInfo?.email[0]
                : null
            }
            sender={
              user?.accounts_email?.length > 0
                ? user?.accounts_email.find(
                    (item) =>
                      item.primary_account === 1 || parseInt(item.shared) === 1
                  )?.id
                : undefined
            }
            contactId={contactInfo?.id}
            familyId={contactInfo?.family_id}
          />
        </Suspense>
      )} */}
      <FormUpdate
        open={openDrawerUpdate}
        setOpen={setOpenDrawerUpdate}
        familyId={
          Object.keys(elementDetailsGridView).length > 0
            ? elementDetailsGridView?.familyId
            : contactInfo?.family_id
        }
        elementDetails={{
          id:
            Object.keys(elementDetailsGridView).length > 0
              ? elementDetailsGridView?.id
              : contactInfo?.id,
          label:
            Object.keys(elementDetailsGridView).length > 0
              ? elementDetailsGridView?.label
              : contactInfo?.name,
        }}
        mask={false}
        from="viewSphere"
        setCatchChange={setIsUpdate}
      />
      <FormCreate
        open={openFormCreate}
        setOpen={setOpenFormCreate}
        familyId={openFamilyId}
        idRelation={contactInfo?.id}
        setCatchChange={false}
      />
      {!isGuestConnected() && roles.includes(user.role) ? (
        <Drawer
          width={1100}
          onClose={() => {
            setOpenPipeline(false);
          }}
          open={openPipeline}
          styles={{
            body: {
              padding: 0,
              overflowY: "hidden",
            },
          }}
          title={t("menu2.pipeline")}
        >
          {openPipeline ? (
            <div className="p-2">
              <ShowPipelineStage
                items={items}
                keyTab={contactInfo?.family_id}
              />
            </div>
          ) : null}
        </Drawer>
      ) : null}
      {!isGuestConnected() ? (
        <Modal
          // key={keyModalChangeStage}
          open={reasons?.length > 0}
          title={t("sales.selectReasonTitle")}
          onCancel={() => {
            setReasons(null);
            setSelectedReasonType("");
            finalStageForm.resetFields();
            setActionDeal("");
          }}
          centered
          footer={() => (
            <div className="left-1/2">
              <Button
                type="primary"
                form="reasons-form"
                htmlType="submit"
                loading={loadFinalStage}
              >
                {t("wiki.Confirm")}
              </Button>
            </div>
          )}
        >
          <Form
            form={finalStageForm}
            layout="vertical"
            id="reasons-form"
            onFinish={handleSumitReasonsForm}
            onFieldsChange={handleChooseReasons}
          >
            {typeof actionDeal !== "number" && (
              <Form.Item
                name="reasonType"
                rules={[
                  { required: true, message: t("import.requiredFields") },
                ]}
              >
                <Radio.Group
                  buttonStyle="solid"
                  defaultValue={null}
                  onChange={handleChangeReasonsItems}
                  style={{ paddingTop: "10px", paddingBottom: "10px" }}
                >
                  {reasons &&
                    reasons.map((item, i) => (
                      <Radio.Button value={item?.reason_type} key={i}>
                        {item?.reason_type === 1
                          ? t("sales.successReason")
                          : t("sales.failReason")}
                      </Radio.Button>
                    ))}
                </Radio.Group>
              </Form.Item>
            )}
            {selectedReasonType !== null && (
              <Form.Item
                name="reasons"
                rules={[
                  { required: true, message: t("import.requiredFields") },
                ]}
              >
                <Radio.Group
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "left",
                  }}
                  defaultValue={null}
                  options={
                    reasons &&
                    reasons
                      .find((el) => el?.reason_type === selectedReasonType)
                      ?.reasons.map((item) => ({
                        value: item?.id,
                        label: item?.label,
                      }))
                  }
                />
              </Form.Item>
            )}
          </Form>
        </Modal>
      ) : null}
      {openTask && (
        <CreateTask
          open={openTask}
          setOpen={setOpenTask}
          mask={false}
          fromVue360={true}
          setSelectedKey={setSelectedKey}
        />
      )}
      {!isGuestConnected() && roles?.includes(user?.role) ? (
        <Drawer
          width={1080}
          onClose={() => {
            setOpenFields(false);
          }}
          open={openFields}
          styles={{
            body: {
              padding: 0,
            },
          }}
          title={t("menu2.fields")}
        >
          {openFields ? (
            <FieldsContainer
              familyId={contactInfo?.family_id}
              setIsUpdateFromVue360={setIsUpdate}
            />
          ) : null}
        </Drawer>
      ) : null}
    </>
  );
};

export default ContentViewSphere;
