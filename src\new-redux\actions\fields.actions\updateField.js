import { UPDATE_FIELD_SUCCESS, UPDATE_FIELD_ERROR, UPDATE_FIELD_LOADING } from "../../constants";
import MainService from "../../../services/main.service";

export const updateField = (payload) => async (dispatch) => {
  try {
    dispatch({ type: UPDATE_FIELD_LOADING });
    const response = await MainService.updateSpecificField(payload);
    dispatch({
      type: UPDATE_FIELD_SUCCESS,
      payload: { response: response?.data, updatedFieldId: payload?.fieldId },
    });
  } catch (error) {
    if (error?.status !== 401) {
      dispatch({
        type: UPDATE_FIELD_ERROR,
        payload: error,
      });
    }
  }
};
