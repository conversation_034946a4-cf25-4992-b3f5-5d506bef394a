import {
  SET_EVENT_MERCURE,
  SET_ONLINE_USER,
  SET_TYPING_USER,
  SET_ARCHIVED_LIST_FETCHED,
  SET_CHAT_MEMBERS_GROUPS_FETCHED,
  RESET_STATE,
  SET_CHAT_SELECTED_CONVERSATION,
  UPDATE__CHAT_SELECTED_CONVERSATION,
  SET_ALL_PINNED_LIST_FETCHED,
  SET_ALL_STARRED_LIST_FETCHED,
  GET_VISIO_EVENT_ID,
} from "../constants";

const initialState = {
  eventMercure: null,

  onlineUser: {},
  typingUsers: [],
  archivedListFetched: false,
  membersGroupsChatFetched: false,
  allStarredListFetched: false,
  allPinnedListFetched: false,
  selectedConversation: null,
  visioEventId: null,
};
const ChatRealTime = (state = initialState, action) => {
  const { payload, type } = action;
  switch (type) {
    case SET_EVENT_MERCURE:
      return { ...state, eventMercure: payload };

    case SET_ONLINE_USER:
      let copyOnline = Object.values(payload).length === 0 ? {} : { ...state.onlineUser };
      for (let userId in payload) {
        copyOnline[userId] = payload[userId];
      }

      return {
        ...state,
        onlineUser: copyOnline,
      };

    case SET_TYPING_USER: {
      return {
        ...state,
        typingUsers: !payload
          ? []
          : payload.typing === "delete"
          ? state.typingUsers.filter((item) => item.user_id === payload.user_id)
          : payload.typing
          ? [
              ...state.typingUsers,
              {
                user_id: payload.user_id,
                room_id: payload.room_id,
                date: Math.floor(new Date().getTime() / 1000),
              },
            ]
          : !payload.typing
          ? state.typingUsers.filter(
              (item) => !(item.room_id === payload.room_id && item.user_id === payload.user_id)
            )
          : [...state.typingUsers],
      };
    }
    case SET_ARCHIVED_LIST_FETCHED: {
      return {
        ...state,
        archivedListFetched: payload,
      };
    }
    case SET_CHAT_MEMBERS_GROUPS_FETCHED: {
      return {
        ...state,
        membersGroupsChatFetched: payload,
      };
    }

    case SET_CHAT_SELECTED_CONVERSATION: {
      return {
        ...state,
        selectedConversation: payload.selectedConversation,
      };
    }

    case UPDATE__CHAT_SELECTED_CONVERSATION: {
      return {
        ...state,
        selectedConversation: {
          ...state.selectedConversation,
          ...payload,
        },
      };
    }

    case SET_ALL_STARRED_LIST_FETCHED: {
      return {
        ...state,
        allStarredListFetched: payload,
      };
    }
    case SET_ALL_PINNED_LIST_FETCHED: {
      return {
        ...state,
        allPinnedListFetched: payload,
      };
    }
    case GET_VISIO_EVENT_ID: {
      return {
        ...state,
        visioEventId: payload,
      };
    }

    case RESET_STATE: {
      return initialState;
    }
    default:
      return state;
  }
};

export default ChatRealTime;
