import { useState } from "react";
import { EyeOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Too<PERSON><PERSON> } from "antd";
import { useTranslation } from "react-i18next";

import { URL_ENV } from "index";
import MainService from "services/main.service";
import { toastNotification } from "components/ToastNotification";

const OpenFileButton = ({ file, GoToComponent }) => {
  const [activeFileKey, setActiveFileKey] = useState(null);

  const [t] = useTranslation("common");

  const openFileOnOnlyOffice = async () => {
    try {
      let payload = {
        file_id: file?._id,
        mode: null,
        mode_co_editing: "fast",
      };
      const response = await MainService.getFileOfficeConfigs(payload);
      if (response?.status === 200) {
        const url = new URL(
          `${
            process.env.REACT_APP_BRANCH === "devLocal"
              ? process.env.REACT_APP_LOCAL_URL
              : URL_ENV?.REACT_APP_DOMAIN
          }/editor`
        );
        url.searchParams.set("fileUrl", file.path);
        url.searchParams.set("token", response?.data?.token);
        url.searchParams.set(
          "fileType",
          response?.data?.document_details?.fileType
        );
        url.searchParams.set(
          "documentType",
          response?.data?.document_details?.documentType
        );
        url.searchParams.set("key", response?.data?.document_details?.key);
        url.searchParams.set("url", response?.data?.document_details?.url);
        url.searchParams.set("mode", response?.data?.editor_config?.mode);
        url.searchParams.set("lang", response?.data?.editor_config?.lang);
        url.searchParams.set(
          "editingMode",
          response?.data?.editor_config?.coEditing?.mode
        );
        url.searchParams.set(
          "change",
          response?.data?.editor_config?.coEditing?.change
        );
        url.searchParams.set(
          "email",
          response?.data?.editor_config?.user?.email ?? "null"
        );
        url.searchParams.set("id", response?.data?.editor_config?.user?.id);
        url.searchParams.set(
          "image",
          response?.data?.editor_config?.user?.image ?? "null"
        );
        url.searchParams.set("name", response?.data?.editor_config?.user?.name ?? "null");
        url.searchParams.set("title", response?.data?.document_details?.title);
        const a = document.createElement("a");
        a.style.display = "none";
        a.href = url;
        a.target = "_blank";
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
      }
    } catch (error) {
      toastNotification("error", t("chat.retrieveFileDataError"), "topRight");
      console.log({ error });
    } finally {
      setActiveFileKey(null);
    }
  };
  return (
    <Tooltip
      title={
        <div
          dangerouslySetInnerHTML={{
            __html: t("chat.viewFileWithTitle", {
              fileName: file?.file_name,
            }),
          }}
        ></div>
      }
    >
      <Button
        className="mr-3"
        loading={file?._id === activeFileKey}
        onClick={(e) => {
          setActiveFileKey(file?._id);
          openFileOnOnlyOffice(file);
          localStorage.setItem("document", JSON.stringify(file?._id));
          localStorage.setItem("drive", JSON.stringify(false));
        }}
        icon={<EyeOutlined />}
        size="small"
      >
        {GoToComponent ? "" : t("files360.viewFile")}
      </Button>
    </Tooltip>
  );
};

export default OpenFileButton;
