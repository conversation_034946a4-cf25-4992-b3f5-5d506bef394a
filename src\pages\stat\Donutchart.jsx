import React from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";

// Enable custom render functions
const Donutchart = ({ pivotData }) => {
    if (!pivotData || !pivotData.rowKeys || !pivotData.colKeys || !pivotData.getAggregator) {
        return <p className="text-center text-red-600">No Data</p>;
    }

    const rowKeys = pivotData.rowKeys;
    const colKeys = pivotData.colKeys;

    if (rowKeys.length === 0 && colKeys.length === 0) {
        return <p className="text-center text-red-600">No Data</p>;
    }

    const keys = rowKeys.length > 0 ? rowKeys : colKeys;
    const otherAxis = rowKeys.length > 0 ? colKeys : rowKeys;

    const pieData = keys.map((entry, i) => {
        const label = entry.join(" - ") || "Total";
        const sum = (otherAxis.length > 0
            ? otherAxis.reduce((acc, val) => {
                const r = rowKeys.length > 0 ? entry : val;
                const c = rowKeys.length > 0 ? val : entry;
                return acc + (pivotData.getAggregator(r, c)?.value() || 0);
            }, 0)
            : (pivotData.getAggregator(entry, [])?.value() || pivotData.getAggregator([], entry)?.value() || 0)
        );

        return { name: label, y: sum };
    }).filter(item => item.y > 0);

    const total = pieData.reduce((acc, item) => acc + item.y, 0);

    if (pieData.length === 0) {
        return <p className="text-center text-red-600">No Data</p>;
    }

    const options = {
        chart: {
            type: 'pie',
            custom: {},
            events: {
                render() {
                    const chart = this;
                    const series = chart.series[0];
                    let customLabel = chart.options.chart.custom.label;

                    if (!customLabel) {
                        customLabel = chart.options.chart.custom.label = chart.renderer.label(
                            'Total<br/><strong>' + total.toLocaleString() + '</strong>'
                        ).css({
                            color: '#000',
                            textAnchor: 'middle'
                        }).add();
                    }

                    const x = series.center[0] + chart.plotLeft;
                    const y = series.center[1] + chart.plotTop - (customLabel.getBBox().height / 2);

                    customLabel.attr({ x, y });
                    customLabel.css({
                        fontSize: `${series.center[2] / 12}px`
                    });
                }
            }
        },
        accessibility: {
            point: {
                valueSuffix: '%'
            }
        },
        title: {
            text: `📊
            ${pivotData.props.rows.length ? pivotData.props.rows.join(" ") : ''}
            ${pivotData.props.cols.length ? ' : ' + pivotData.props.cols.join(" ") : ''}`,
            align: "left",
            dispalay: "block",
            fontFamily: "Arial, sans-serif",
            style: {
                fontWeight: "normal",
                fontSize: "20px",
                fontFamily: "Inter, sans-serif"
            }
        },
        tooltip: {
            pointFormat: '{series.name}: <b>{point.percentage:.0f}%</b>'
        },
        legend: {
            enabled: false
        },
        credits: {
            enabled: false
        },
        plotOptions: {
            series: {
                allowPointSelect: true,
                cursor: 'pointer',
                borderRadius: 8,
                dataLabels: [{
                    enabled: true,
                    distance: 20,
                    format: '{point.name}'
                }, {
                    enabled: true,
                    distance: -15,
                    format: '{point.percentage:.0f}%',
                    style: {
                        fontSize: '0.9em'
                    }
                }],
                showInLegend: true
            }
        },
        series: [{
            name: 'Values',
            colorByPoint: true,
            innerSize: '75%',
            data: pieData
        }]
    };

    return (
        <div className="highcharts-figure" style={{ width: "100%", height: "100%" }}>
            <HighchartsReact highcharts={Highcharts} options={options} />
        </div>
    );
};

export default Donutchart;
