import { memo, useCallback, useEffect, useMemo, useRef, useState } from "react";
import {
  AutoComplete,
  Button,
  Empty,
  Input,
  Space,
  Tooltip,
  Typography,
} from "antd";
import { LoadingOutlined, MessageOutlined } from "@ant-design/icons";
import { FiCopy, FiSearch } from "react-icons/fi";
import { debounce } from "lodash";
import {
  loaderOptionTemplate,
  transformData,
} from "pages/layouts/webphone/call";
import { useTranslation } from "react-i18next";
import { suggestLog } from "pages/voip/helpers/helpersFunc";
import { useSelector } from "react-redux";
import { openDrawerChat } from "new-redux/actions/voip.actions/handleDrawerChat";
import { useDispatch } from "react-redux";
import DisplayAvatar from "pages/voip/components/DisplayAvatar";
import { HighlightSearchW } from "pages/voip/components";
import MainService from "services/main.service";
import { toastNotification } from "components/ToastNotification";

const AutoCompleteForCall = ({
  callBack,
  onSubmit,
  type,
  isAttendedTransfer,
}) => {
  //
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const inputRef = useRef(null);
  const user = useSelector((state) => state.user.user);
  const userPoste = `${user?.extension}`;
  const receiverInfo = useSelector((state) => state.voipBlackList.receiverInfo);
  const log = useSelector((state) => state.voip.logs);
  const dialCode = useSelector(
    ({ user: { user } }) => user?.location?.dial_code
  );
  //
  const [searchOptions, setSearchOptions] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [search, setSearch] = useState("");
  const [displaySearch, setDisplaySearch] = useState("");
  //
  const fetchSearchOptions = useCallback(async () => {
    try {
      setIsLoading(true);
      if (!search?.length) return;
      const modifiedSearch = /^\+?[0-9]+$/.test(search)
        ? search.replace("+", "00")
        : search;
      const { data } = await MainService.searchByNumOrName({
        key: modifiedSearch,
      });
      if (!data?.length) {
        setSearchOptions([]);
        return;
      }
      const transformedOptions = transformData(data, search, dialCode, t);
      setSearchOptions(transformedOptions);
    } catch (err) {
      if (err?.response?.status !== 401) {
        setSearchOptions([]);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
      throw new Error(err?.message ? err.message : err);
    } finally {
      setIsLoading(false);
    }
  }, [dialCode, search, t]);

  useEffect(() => {
    fetchSearchOptions();
  }, [fetchSearchOptions]);
  //
  const handleOpenChatDrawer = (uuid) => {
    dispatch(openDrawerChat(uuid));
  };
  //
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedSearch = useCallback(
    debounce((nextValue) => setSearch(nextValue), 300),
    []
  );

  const handleSearchChange = (e) => {
    const inputValue = e.target.value;

    if (/^[0-9\s]*$/.test(inputValue)) {
      const searchText = inputValue.replace(/\s+/g, "");
      setDisplaySearch(searchText);
      debouncedSearch(searchText.trim());
    } else {
      setDisplaySearch(inputValue);
      debouncedSearch(inputValue.trim());
    }
  };
  //
  const suggestLogOptions = useMemo(
    () => suggestLog(log, userPoste, t),
    [log, t, userPoste]
  );
  //
  const options = useMemo(() => {
    if (!search.length && (!suggestLogOptions || !suggestLogOptions.length))
      return [];
    else if (!search.length) {
      return [
        {
          label: (
            <span className="text-xs font-semibold">{t("voip.suggested")}</span>
          ),
          options: suggestLogOptions
            // ?.filter((el) => el?.id !== user?.id && el.id !== receiverInfo?.id)
            ?.map((item) =>
              renderItem(
                item,
                search,
                handleOpenChatDrawer,
                onSubmit,
                t,
                type,
                isAttendedTransfer,
                item?.id === user?.id || item?.id === receiverInfo?.id
              )
            ),
        },
      ];
    } else {
      return searchOptions.map((item, i) => ({
        label: <span className="text-xs font-semibold	">{item.label}</span>,
        options: item.values
          // ?.filter((el) => el?.id !== user?.id && el.id !== receiverInfo?.id)
          .slice(0, 5)
          .map((value) =>
            renderItem(
              value,
              search,
              handleOpenChatDrawer,
              onSubmit,
              t,
              type,
              isAttendedTransfer,
              value?.id === user?.id || value?.id === receiverInfo?.id
            )
          ),
      }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [search, searchOptions, suggestLogOptions, t, isAttendedTransfer]);
  //
  // console.log({ isAttendedTransfer });
  //
  return (
    <AutoComplete
      // popupClassName="bg-slate-50"
      popupClassName="autoComplete-input-call pr-0 pb-2 rounded-t-sm"
      style={{
        width: "17rem",
        cursor: "default",
      }}
      // open={false}
      notFoundContent={
        isLoading ? (
          loaderOptionTemplate(4)
        ) : (
          <div className="flex justify-center p-4 text-sm font-semibold">
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={"No Contacts Found"}
            />
          </div>
        )
      }
      options={options}
      value={displaySearch}
      onChange={(e) => callBack(e)}
    >
      <Input
        allowClear
        ref={inputRef}
        style={{ width: "100%" }}
        onChange={handleSearchChange}
        onPressEnter={() =>
          !!Number(search) &&
          search.length > 0 &&
          onSubmit(
            {
              image: searchOptions?.[0]?.values?.[0]?.baseImg || null,
              extension: search,
              name: searchOptions?.[0]?.values?.[0]?.name || null,
            },
            type,
            isAttendedTransfer
          )
        }
        prefix={
          <FiSearch className="text-slate-400" style={{ fontSize: 16 }} />
        }
        suffix={
          <Space>
            {isLoading ? (
              <LoadingOutlined
                style={{
                  fontSize: 16,
                  color: "rgb(22, 119, 255)",
                }}
              />
            ) : null}
          </Space>
        }
        placeholder={t("voip.inputMessage")}
      />
    </AutoComplete>
  );
};

const copyIcon = (text) => (
  <div className="pl-1">
    <Typography.Paragraph
      copyable={{
        text: text,
        icon: [
          <FiCopy
            style={{
              color: "rgb(22, 119, 255)",
              marginTop: "4px",
              fontSize: "16px",
            }}
          />,
        ],
      }}
    />
  </div>
);

export function renderItem(
  item,
  search,
  handleOpenChat,
  onSubmit,
  t,
  type,
  isAttendedTransfer,
  disabled
) {
  //
  const { image, name, date, extension, phone, uuid, id, family_id, baseImg } =
    item;
  //
  function generateUniqueID() {
    const timestamp = new Date().getTime();
    const randomSegment = Math.random().toString(36).substring(2, 15);
    const uniqueID = `key_${timestamp}_${randomSegment}`;
    return uniqueID;
  }
  //
  return {
    key: generateUniqueID(),
    value: extension || phone?.callNum,
    disabled: disabled,
    label: (
      <div key={item} className="relative flex w-full flex-row ">
        <div
          onClick={() => {
            !disabled &&
              onSubmit(
                {
                  image: baseImg,
                  extension: extension || phone?.callNum,
                  name: name || null,
                  id: id || null,
                  family_id: family_id || null,
                },
                type,
                isAttendedTransfer
              );
          }}
          className="relative  flex w-9/12 flex-row items-center space-x-2.5"
        >
          <div>
            <DisplayAvatar urlImg={image} name={name} size={35} />
          </div>
          <div className="w-10/12">
            <Tooltip title={!disabled && t("voip.call")}>
              <p className="truncate font-semibold leading-5">
                {HighlightSearchW(name || phone?.displayNum, search)}
              </p>
              {name ? (
                <p className="leading-4 text-slate-500">
                  {HighlightSearchW(extension || phone?.displayNum, search)}
                </p>
              ) : date ? (
                <p className="leading-4 text-slate-500">{date}</p>
              ) : null}
            </Tooltip>
          </div>
        </div>

        <div className="absolute right-0 top-1 flex flex-row items-center space-x-1">
          {uuid && (
            <Button
              onClick={() => handleOpenChat(uuid)}
              type="link"
              size="small"
              icon={<MessageOutlined style={{ fontSize: 14 }} />}
            />
          )}
          {copyIcon(extension || phone?.copyNum)}
        </div>
      </div>
    ),
  };
}

export default memo(AutoCompleteForCall);
