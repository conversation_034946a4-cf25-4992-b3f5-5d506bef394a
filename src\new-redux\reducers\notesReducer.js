import {
  ADD_NOTE360,
  REMOVE_NOTE360,
  UPDATE_NOTE360,
  SET_NOTES360,
  ADD_NOTES360_TO_LIST,
} from "../constants";

const initialState = {
  notes: [],
};

const notes = (state = initialState, action) => {
  const { type, payload } = action;
  switch (type) {
    case ADD_NOTE360:
      return {
        ...state,
        notes: [payload, ...state.notes],
      };
    case REMOVE_NOTE360:
      return {
        ...state,
        notes: state.notes.filter((note) => note?._id !== payload),
      };
    case UPDATE_NOTE360:
      return {
        ...state,
        // notes: state.notes.map((note) =>
        //   note?.note?._id === payload?._id ? payload : note
        // ),
        //update only the content of the note
        notes: state.notes.map((note) =>
          note?._id === payload?._id
            ? { ...note, content: payload.content, public: payload.public }
            : note  
        ),
      };

    case SET_NOTES360:
      return {
        ...state,
        notes: payload,
      };

    case ADD_NOTES360_TO_LIST:
      //merge new notes with old notes and remove duplicates

      let mergedNotes = [...state.notes, ...payload];

      for (let i = 0; i < mergedNotes.length; i++) {
        for (let j = i + 1; j < mergedNotes.length; j++) {
          if (mergedNotes[i]?._id === mergedNotes[j]?._id) {
            mergedNotes.splice(j, 1);
          }
        }
      }

      return {
        ...state,
        notes: mergedNotes,
      };

    default:
      return state;
  }
};

export default notes;
