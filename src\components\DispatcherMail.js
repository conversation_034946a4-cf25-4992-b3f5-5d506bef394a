import {
  <PERSON><PERSON>,
  Di<PERSON><PERSON>,
  Popover,
  Select,
  Skeleton,
  Space,
  Tooltip,
} from "antd";
import React, { useEffect, useRef, useState } from "react";
import { generateAxios } from "services/axiosInstance";
import { URL_ENV } from "index";
import { AvatarChat } from "./Chat";
import { getName } from "pages/layouts/chat/utils/ConversationUtils";
import { useTranslation } from "react-i18next";
import { SET_USER_INFOS } from "new-redux/constants";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";

const DispatcherMail = ({
  departement_ids,
  setData,
  dispatcheur,
  id,
  setLoading,
  loading,
  type,
  creatorId,
  processing_time,
}) => {
  const [openSelect, setOpenSelect] = useState(false);
  const [optionsUsers, setOptionsUsers] = useState([]);
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.user);
  useEffect(() => {
    const handleCLickPopover = async () => {
      try {
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post(`get-dispatcheur-departement`, {
          departement_id: departement_ids,
        });
        setOptionsUsers(res.data.data);
        setTimeout(() => {
          setLoading(false);
        }, 400);
      } catch (err) {
        setLoading(false);
      }
    };
    if (departement_ids) handleCLickPopover();
  }, [departement_ids]);

  const handleAddUsers = async (usersSelected) => {
    try {
      const res = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).post(`config-mails/update/${id}`, {
        dispatcheur: usersSelected.length > 0 ? usersSelected : "",
        account_id: id,
        type,
        departement_ids,
        processing_time,
      });
      dispatch({
        type: SET_USER_INFOS,
        payload: {
          ...user,
          accounts_email: Array.isArray(res.data.data) ? res.data.data : [],
        },
      });
      setData((prev) =>
        prev.map((el) =>
          el.id === id
            ? {
                ...el,
                dispatcheur: usersSelected,
              }
            : el
        )
      );
      // console.log(usersSelected);
    } catch (err) {}
  };
  return (
    <>
      {/* <Divider orientation="left" plain>
        {t("mailing.selectUsers")}{" "}
      </Divider> */}

      {loading ? (
        <div className="skeletonSelectDepartments">
          <Skeleton.Input
            style={{
              width: "100%",
              minWidth: 150,
              display: "inline-flex",
            }}
            size="large"
          />
        </div>
      ) : (
        <Select
          mode="multiple"
          style={{
            minWidth: 150,
            width: "100%",
            border: 0,
          }}
          // maxTagCount="responsive"
          className={`selectUserPostApi`}
          onDropdownVisibleChange={(e) => setOpenSelect(e)}
          placeholder={t("mailing.selectUsers")}
          onChange={(value) => handleAddUsers(value)}
          disabled={user?.id !== creatorId}
          size="large"
          optionLabelProp="label2"
          options={optionsUsers.map((el) => ({
            value: el.owner,
            label: (
              <>
                <Space>
                  <AvatarChat
                    fontSize="0.875rem"
                    url={
                      URL_ENV?.REACT_APP_BASE_URL +
                      URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                      el?.avatar?.path
                    }
                    type="user"
                    size={24}
                    height={10}
                    width={10}
                    name={getName(el.label_data, "avatar")}
                    hasImage={
                      el?.avatar?.path &&
                      el?.avatar?.path !== "/storage/uploads/"
                    }
                  />

                  {getName(el.label_data, "name")}
                </Space>
              </>
            ),
            label2: (
              <div className="z-index-1000">
                <Space>
                  <Tooltip title={getName(el.label_data, "name")}>
                    <div>
                      <AvatarChat
                        fontSize="0.875rem"
                        url={
                          URL_ENV?.REACT_APP_BASE_URL +
                          URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                          el?.avatar?.path
                        }
                        type="user"
                        size={24}
                        height={10}
                        width={10}
                        name={getName(el.label_data, "avatar")}
                        hasImage={
                          el.avatar?.path &&
                          el?.avatar?.path !== "/storage/uploads/"
                        }
                      />
                    </div>
                  </Tooltip>
                </Space>
              </div>
            ),
          }))}
          defaultValue={dispatcheur || []}
          filterOption={(input, option) =>
            (
              option?.label?.props?.children?.props?.children[1]?.toLowerCase() ??
              ""
            ).includes(input.toLowerCase())
          }
        />
      )}
    </>
  );
};
// (option?.label?.props?.children?.props?.children[1]?.toLowerCase() ?? "").includes(
//   input.toLowerCase()
// )
export default DispatcherMail;
