import { Suspense } from "react";
import {
  DeleteOutlined,
  ExportOutlined,
  FieldTimeOutlined,
  FileOutlined,
} from "@ant-design/icons";
import { Button, Empty, Image, Popconfirm, Skeleton } from "antd";
import { useTranslation } from "react-i18next";

import { moment_timezone } from "App";
import { ImageRender } from "components/Chat";
import { URL_ENV } from "index";

const AttachmentsList = ({ attachmentsList, removeUploadedFile }) => {
  const [t] = useTranslation("common");

  // Show loader.
  const showLoader = () =>
    [...Array(6)].map((_, i) => (
      <Skeleton.Image key={`image_key_${i}`} active />
    ));
  /*   const showLoader = () =>
    Array.from({ length: 6 }, (_, i) => i + 1).map((item) => (
        <Skeleton.Image key={`image_key_${item}`} active />
    )); */

  return (
    <div className="max-h-[calc(100vh - 340px)] overflow-auto p-0">
      {attachmentsList?.length === 0 ? (
        <Empty description={t("chat.info.noMedia")} />
      ) : (
        <ul className="grid grid-cols-2 gap-x-4 gap-y-2 px-0.5 ">
          <Image.PreviewGroup>
            {attachmentsList?.map((img, index) => (
              <li
                key={img?.id}
                className="relative mt-0.5 list-none grid-cols-1 rounded-sm shadow-lg  ring-2 ring-gray-200"
              >
                <Suspense fallback={showLoader}>
                  <ImageRender
                    source="activities"
                    array={attachmentsList}
                    file={img}
                    index={index}
                    height={120}
                  />
                </Suspense>

                <p className="  block   text-xs ">
                  <span className="mr-0.5 text-gray-400">
                    <FieldTimeOutlined />{" "}
                  </span>
                  <span></span>
                  {moment_timezone(img?.created_at).format(
                    " DD MMM YYYY HH:mm"
                  )}
                </p>
                <p className="flex items-center">
                  <span className="mr-0.5 text-gray-400">
                    <FileOutlined />{" "}
                  </span>
                  <span className="truncate text-xs">{img?.fileName}</span>
                </p>
                <div className="flex w-full flex-row justify-between">
                  <Button
                    onClick={() =>
                      window.open(
                        `${URL_ENV?.REACT_APP_BASE_URL}${img?.path}`,
                        "_blank"
                      )
                    }
                    block
                    className=" flex items-center justify-center"
                    type="link"
                  >
                    {t("tasks.openAttachment")}
                    <ExportOutlined style={{ fontSize: "8px" }} />
                  </Button>
                  <Popconfirm
                    description={t("voip.areYouSureToDelete")}
                    onConfirm={() => removeUploadedFile(img)}
                    okText={t("fields_management.popupConfirmYesBtn")}
                    cancelText={t("fields_management.popupConfirmNoBtn")}
                  >
                    <Button
                      icon={<DeleteOutlined />}
                      danger
                      className=" flex items-center justify-end"
                      type="link"
                    />
                  </Popconfirm>
                </div>
              </li>
            ))}
          </Image.PreviewGroup>
        </ul>
      )}
    </div>
  );
};

export default AttachmentsList;
