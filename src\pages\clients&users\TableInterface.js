import {
  useState,
  useEffect,
  useCallback,
  useRef,
  memo,
  Suspense,
} from "react";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import { Input, Button, Tooltip } from "antd";
import {
  TableOutlined,
  InfoCircleOutlined,
  SettingOutlined,
  QuestionCircleOutlined,
} from "@ant-design/icons";
import { FiSearch } from "react-icons/fi";
//
import { toastNotification } from "../../components/ToastNotification";
import TableView from "./components/TableView";
import ShowHideColumns from "./components/ShowHideColumns";
import ExportButton from "./components/ExportButton";
import FormCreate from "./components/FormCreate";
import FormUpdate from "./components/FormUpdate";
//
import {
  formattingHeader,
  formattingData,
  manageShowHideCol,
  handleDelete,
  sendInvitation,
  handleEventMercure,
  convertLeadToContact,
  convertContactToGuest,
} from "./helpers/index";
//
import { getTableHeader, getTableData } from "./services/services";
import {
  setOpenChildrenImportDrawer,
  setOpenImportDrawer,
  setUpdateElementSuccessfully,
} from "../../new-redux/actions/form.actions/form";
import ImportDrawer from "./utility-component/ImportDrawer";
import NavHeader from "../../components/NavHeader";
import KanbanBoard from "../tasks/tasksViews/kanban/KanbanBoard";
import { useTranslation } from "react-i18next";
import debounce from "lodash/debounce";
import { isGuestConnected, roles } from "../../utils/role";
import MainService from "../../services/main.service";
import ActionDropDown from "./components/ActionDropDown";
import blockOrUnblockUser from "./helpers/blockUser";
import ModalActionTicket from "../../components/ModalActionTicket";
import SelectFolder from "../../components/SelectFolder";
import InvitationLinkModal from "./components/InvitationLinkModal";
import { MdOutlineTableChart } from "react-icons/md";
import KpiFamily from "./components/KpiFamily";
import ProjectWrapper from "pages/project/ProjectWrapper";
import { setSavePreferences } from "new-redux/actions/vue360.actions/vue360";
import { checkIfPathOnView360 } from "pages/voip/helpers/helpersFunc";
import { setOpenTaskRoomDrawer } from "new-redux/actions/tasks.actions/handleTaskRoomDrawer";
import { LoaderDrawer } from "pages/layouts/chat";
import TasksRoom from "pages/tasks/tasksRoom";
import FilterFamilyTable from "./components/filter/FilterFamilyTable";
import DisplayElementInfo from "pages/voip/components/DisplayElementInfo";
import Confirm from "components/GenericModal";
import SortFamilyTables from "./components/filter/sort/SortFamilyTables";
import StatsDrawer from "pages/components/StatsDrawer";
import { Refs_IDs } from "components/tour/tourConfig";
import { SET_CONTACT_SORT_TABLE } from "new-redux/constants";

const TableInterface = ({ familyId, relation_id }) => {
  //
  const selectedView = localStorage.getItem("selectedView");
  const [t] = useTranslation("common");
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const location = useLocation();
  const tableRef = useRef(null);
  const { openCreateForm, dropAction } = useSelector((state) => state?.form);
  const { userEvent, familyEvent, sortTable } = useSelector(
    (state) => state?.contacts
  );
  const user = useSelector(({ user }) => user?.user);
  const chatRole = useSelector((state) => state?.chat?.currentUser?.role);
  const { state } = useLocation();
  const {
    openImportDrawer,
    openChildrenImportDrawer,
    typeChildren,
    titleChildren,
  } = useSelector((state) => state.form);
  const userPreferences = useSelector(
    (state) => state?.TasksRealTime?.userPreferences
  );
  const openTaskRoomDrawer = useSelector(
    (state) => state?.TasksRealTime?.openTaskRoomDrawer
  );
  //
  const [preferencesArray, setPreferencesArray] = useState(
    userPreferences ?? []
  );
  const [shouldFetchData, setShouldFetchData] = useState(false);
  const [isFirstRender, setIsFirstRender] = useState(true);
  const [columns, setColumns] = useState([]);
  const [showColumns, setShowColumns] = useState({});
  const [dataTable, setDataTable] = useState([]);
  const [tableIsLoading, setTableIsLoading] = useState(true);
  const [isDataExist, setIsDataExist] = useState(true);
  const [totalNumberOfElements, setTotalNumberOfElements] = useState(0);
  const [numberOfPage, setNumberOfPage] = useState(1);
  const [pageLimit, setPageLimit] = useState(20);
  const [searchElement, setSearchElement] = useState("");
  const [displaySearch, setDisplaySearch] = useState("");
  const [dynamicFilter, setDynamicFilter] = useState([]);
  const [sortedInfo, setSortedInfo] = useState(sortTable?.[familyId] || {});
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  //
  const { families } = useSelector((state) => state?.families);

  const [openDrawerCreate, setOpenDrawerCreate] = useState(false);
  const [openDrawerUpdate, setOpenDrawerUpdate] = useState(false);
  const [elementDetailToUpdate, setElementDetailToUpdate] = useState({});
  const [openDrawerInfo, setOpenDrawerInfo] = useState(false);
  const [elementInfo, setElementInfo] = useState({});
  const [selectedPipeline, setSelectedPipeline] = useState(
    userPreferences?.find((el) => el?.currentRoute === location?.pathname)
      ?.selectedPipeline
  );
  const [displayedView, setDisplayedView] = useState(selectedView ?? "Table");
  //
  const [pipelines, setPipelines] = useState([]);
  const [catchCreateOrUpdate, setCatchCreateOrUpdate] = useState(true);
  const [openModal, setOpenModal] = useState(false);
  const [modalType, setModalType] = useState("");
  const [dataFolders, setDataFolders] = useState([]);
  const [ticketsFolderId, setTicketsFolderId] = useState("");
  const [openModalCopyInvite, setOpenModalCopyInvite] = useState(false);
  const [activeRoomId, setActiveRoomId] = useState(null);
  const [selectedStageId, setSelectedStageId] = useState(null);
  const pipelinesRef = useRef(pipelines);
  //
  // console.log({ sortedInfo, familyId });
  //
  const handleChangeSortData = (info) => {
    dispatch({
      type: SET_CONTACT_SORT_TABLE,
      payload: {
        familyId,
        info,
      },
    });
    setSortedInfo(info);
  };

  const getPipelines = async () => {
    try {
      const response = await MainService.getPipelinesByFamily(familyId);
      setPipelines(response?.data?.data);
      if (displayedView === "Kanban" && !selectedPipeline) {
        setSelectedPipeline(response?.data?.data[0]?.id);
      }
    } catch (error) {
      console.log(`Error ${error}`);
      toastNotification("error", t("toasts.somethingWrong"));
    }
  };

  useEffect(() => {
    if (location?.pathname !== "/tasks") {
      getPipelines();
    }
    return () => {
      setPipelines([]);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [familyId]);

  useEffect(() => {
    pipelinesRef.current = pipelines;
  }, [pipelines]);
  //
  useEffect(() => {
    if (!!familyEvent) {
      setShouldFetchData(true);
      let timer = setTimeout(() => {
        dispatch({ type: "RESET_FAMILY_EVENT" });
      }, 50);
      return () => clearTimeout(timer);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [familyEvent]);
  //
  const getColumns = useCallback(async () => {
    if (displayedView === "Kanban") return;
    try {
      const resp = await getTableHeader(familyId, selectedPipeline);
      const { result } = await formattingHeader(
        resp?.data?.data,
        handleDropDownOptions,
        user,
        familyId,
        dispatch,
        location,
        navigate,
        t,
        relation_id,
        sortedInfo,
        familyId === 6 && ticketsFolderId
      );
      setShowColumns(manageShowHideCol(result));
      setColumns(result);
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? `error: ${err.message}` : { err });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [familyId, selectedPipeline, ticketsFolderId, displayedView]);

  useEffect(() => {
    getColumns();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [getColumns]);
  //
  const getDataTable = useCallback(async () => {
    if (!shouldFetchData || displayedView === "Kanban") return;

    try {
      const isGuest = location?.pathname === "/settings/guests";
      setTableIsLoading(true);
      const resp = await getTableData(
        familyId,
        numberOfPage,
        pageLimit,
        searchElement,
        isGuest,
        relation_id,
        state?.importId,
        !!checkIfPathOnView360(location.pathname) ? null : selectedPipeline,
        ticketsFolderId,
        dynamicFilter,
        sortedInfo
      );
      const dataSource = await formattingData(resp?.data?.data, t);
      setDataTable(dataSource);
      setTotalNumberOfElements(resp?.data?.meta?.total);
      if (!dataSource?.length) setIsDataExist(false);
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? `error: ${err.message}` : err);
    } finally {
      setTableIsLoading(false);
      setShouldFetchData(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shouldFetchData, displayedView]);

  useEffect(() => {
    getDataTable();
  }, [getDataTable]);
  //
  // trigger the getDataTable if any params change
  useEffect(() => {
    const canDisplay =
      displayedView === "Kanban"
        ? searchElement.length >= 3 || !searchElement?.length
        : columns?.length &&
          !isFirstRender &&
          (searchElement.length >= 3 || !searchElement?.length);

    if (canDisplay) {
      setShouldFetchData(true);
    }

    setIsDataExist(true);

    if (
      (columns?.length || displayedView === "Kanban") &&
      (!isFirstRender || displayedView === "Kanban") &&
      (searchElement.length >= 3 || !searchElement?.length)
    )
      setShouldFetchData(true);
    setIsDataExist(true);
  }, [
    numberOfPage,
    pageLimit,
    columns,
    catchCreateOrUpdate,
    searchElement,
    selectedPipeline,
    displayedView,
    ticketsFolderId,
    dynamicFilter,
    sortedInfo,
    isFirstRender,
  ]);
  // Set page to 1 if any params chnage that require toback to page 1
  useEffect(() => {
    setNumberOfPage(1);
  }, [searchElement, dynamicFilter, sortedInfo]);
  // handle event Mercure: block users & unblock users & invite users
  const eventMercure = useCallback(() => {
    if (familyId !== 4 || !userEvent) return;
    dispatch(handleEventMercure(setDataTable, userEvent, t));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userEvent]);

  useEffect(() => {
 
    eventMercure();
  }, [eventMercure]);
  //
  async function handleDropDownOptions(option, id, label, pipelineId) {
    switch (option) {
      case "edit":
        setElementDetailToUpdate({ id: id, label: label });
        setOpenDrawerUpdate(true);
        break;
      case "moreInfo":
        setElementInfo({ id: id, label: label, familyId });
        setOpenDrawerInfo(true);
        break;
      case "invite":
        await sendInvitation(t, id, label);
        break;
      case "block":
        await blockOrUnblockUser(t, id, label, null, null, "block");
        break;
      case "unblock":
        await blockOrUnblockUser(t, id, label, null, null, "unblock");
        break;
      case "delete":
        await handleDelete(
          t,
          familyId,
          id,
          label,
          null,
          null,
          setShouldFetchData,
          dispatch
        );
        // message.error("This Action Is Under Dev 🤌", [5]);
        break;
      case "transfer":
        setModalType("transfer");
        setElementDetailToUpdate({ id: id, label: label });
        setOpenModal(true);
        break;
      case "assign":
        setModalType("assign");
        setElementDetailToUpdate({ id: id, label: label });
        setOpenModal(true);
        break;
      case "launch":
        setModalType("launch");
        setElementDetailToUpdate({ id: id, label: label });
        setOpenModal(true);
        break;
      case "view360":
        navigate(`${location.pathname}/${id}`);
        break;
      case "view360-2":
        navigate(`${location.pathname}/v2/${id}`);
        break;
      case "copyInvitation":
        setElementDetailToUpdate({ id, label: label?.replaceAll("_", " ") });
        setOpenModalCopyInvite(true);
        break;
      case "deplacerByOne":
        setModalType("deplacerByOne");
        setElementDetailToUpdate({ id: id, label: label });
        setOpenModal(true);
        break;
      case "remettre":
        setModalType("remettre");
        setElementDetailToUpdate({ id: id, label: label });
        setOpenModal(true);
        break;
      case "discussion":
        dispatch(setOpenTaskRoomDrawer(true));
        setActiveRoomId(id);
        break;
      case "close":
        Confirm(
          t("helpDesk.closeTicketConfirmMessage"),
          "Confirm",
          <QuestionCircleOutlined
            style={{
              color: "#faad14",
            }}
          />,
          function func() {
            return closeTicketForGuest(id, label, pipelineId);
          },
          true
        );
        break;
      case "convertToContact":
        await convertLeadToContact(
          { id, label },
          t,
          setTableIsLoading,
          setShouldFetchData
        );
        break;
      case "reOpen":
        Confirm(
          t("helpDesk.reopenTicketConfirmMessage"),
          "Confirm",
          <QuestionCircleOutlined
            style={{
              color: "#faad14",
            }}
          />,
          function func() {
            return reopenTicketForGuest(id, pipelineId);
          },
          false
        );
        break;
      case "inviteGuest":
        convertContactToGuest(
          t,
          id,
          label,
          setShouldFetchData,
          true,
          user?.role
        );
        break;
      default:
        break;
    }
  }
  //
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedSearch = useCallback(
    debounce((nextValue) => setSearchElement(nextValue), 500),
    []
  );

  const handleSearch = (event) => {
    const inputValue = event.target.value;

    if (inputValue.trim() === "") {
      setDisplaySearch("");
      debouncedSearch("");
    } else if (!inputValue.includes("  ")) {
      setDisplaySearch(inputValue);
      inputValue.length > 2 && debouncedSearch(inputValue);
    }
  };
  //
  // Clear states if pathName change
  const clearStates = useCallback(() => {
    setColumns([]);
    setShowColumns({});
    setDataTable([]);
    setTableIsLoading(false);
    setTotalNumberOfElements(0);
    setNumberOfPage(1);
    setPageLimit(20);
    setSearchElement("");
    setDisplaySearch("");
    setSelectedRowKeys([]);
    setOpenDrawerCreate(false);
    setOpenDrawerUpdate(false);
    setElementDetailToUpdate({
      id: "",
      label: "",
    });
    setPipelines([]);
    setKanbanData([]);
    setDynamicFilter([]);
    setIsFirstRender(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location?.pathname]);
  //
  // ********** Deals kanban JS***********
  const [kanbanData, setKanbanData] = useState([]);
  const [loadDeals, setLoadDeals] = useState(false);
  const [scrollingStageIdInElements, setScrollingStageIdInElements] =
    useState(null);
  const [scrollParameterElements, setScrollParameterElements] = useState([]);

  const getKanbanData = useCallback(async () => {
    let isMounted = true;
    try {
      setLoadDeals(true);
      const response = await MainService.getKanbanElements(
        familyId,
        selectedPipeline,
        searchElement,
        ticketsFolderId
      );
      if (isMounted) {
        setKanbanData(response?.data?.stages);
        setScrollParameterElements(
          response?.data?.stages?.map((stage) => {
            return {
              ...scrollParameterElements,
              id: stage?.stage_id,
              page: 1,
              lastPage: stage?.last_page,
            };
          })
        );
        setLoadDeals(false);
      }
    } catch (error) {
      console.log(`Error ${error}`);
      toastNotification("error", t("toasts.somethingWrong"));
      setLoadDeals(false);
    } finally {
      setShouldFetchData(false);
      isMounted = false;
    }
  }, [
    selectedPipeline,
    familyId,
    searchElement,
    ticketsFolderId,
    catchCreateOrUpdate,
  ]);

  useEffect(() => {
    if (selectedPipeline && displayedView === "Kanban") {
      getKanbanData();
    }
    return () => {
      setKanbanData([]);
    };
  }, [getKanbanData, displayedView]);

  const handleSelectChange = (value) => {
    setSelectedPipeline(value);
    handleSavePreferences(value);
  };

  const displayPipelines = (pipelinesArray, selectedView) => {
    if (selectedView === "Table") {
      return (
        pipelinesArray &&
        [{ label: t("tasks.allPipelines"), id: null }, ...pipelinesArray].map(
          (pipeline) => ({
            label: pipeline?.label,
            value: pipeline?.id,
          })
        )
      );
    } else {
      return (
        pipelinesArray &&
        pipelinesArray.map((pipeline) => ({
          label: pipeline?.label,
          value: pipeline?.id,
        }))
      );
    }
  };

  let defaultValues = { selectedPipeline, switchViews: displayedView };

  const handleSegmentedChange = (value) => {
    // setPipelines([]);
    if (value !== "Gantt") {
      localStorage.setItem("selectedView", value);
    } else return null;
    setKanbanData([]);
    if (value === "Kanban" && !selectedPipeline) {
      setSelectedPipeline(pipelines?.[0]?.id);
    }
    setDisplayedView(value);
  };

  const handleSavePreferences = (pipelineValue) => {
    let currentRoute = location?.pathname;

    // Find the index of the entry with the currentRoute
    let index =
      preferencesArray &&
      preferencesArray.findIndex(
        (preference) => preference?.currentRoute === currentRoute
      );

    let newPreferencesArray;

    if (index > -1) {
      // Update the selectedPipeline if the route exists
      newPreferencesArray = preferencesArray.map((preference, i) =>
        i === index
          ? { ...preference, selectedPipeline: pipelineValue }
          : preference
      );
    } else {
      // Add a new entry if the route does not exist
      newPreferencesArray = [
        ...preferencesArray,
        { currentRoute, selectedPipeline },
      ];
    }

    // Update the state with the new preferences array
    setPreferencesArray(newPreferencesArray);

    dispatch(setSavePreferences(newPreferencesArray));
  };

  useEffect(() => {
    if (displayedView === "Kanban") {
      handleSavePreferences(selectedPipeline ?? pipelines[0]?.id);
    }
  }, [displayedView, pipelines]);

  const closeTicketForGuest = async (elementId, elementLabel, idPipeline) => {
    try {
      let selectedPip = selectedPipeline || idPipeline;
      let pipelineObj =
        pipelinesRef?.current &&
        pipelinesRef?.current?.find(
          (item) => Number(item?.id) === Number(selectedPip)
        );
      if (pipelineObj) {
        let finalStage = pipelineObj?.stages?.find(
          (stage) => stage?.default && stage?.final && stage?.resolved === 0
        )?.id;
        if (finalStage) {
          const { data } = await MainService.updateElementStage(
            familyId,
            {
              new_stage_id: finalStage,
              id_element: elementId,
            },
            finalStage
          );
          if (data?.success && data?.required_fields === 1) {
            setElementDetailToUpdate({
              id: elementId,
              label: elementLabel,
            });
            dispatch(
              setUpdateElementSuccessfully({
                ...dropAction,
                destination: finalStage,
              })
            );
            setOpenDrawerUpdate(true);
          } else if (data?.is_final === 1 && data?.required_fields === 0) {
            dispatch(
              setUpdateElementSuccessfully({
                ...dropAction,
                destination: finalStage,
                elementId: elementId,
              })
            );
          } else {
            displayedView === "Kanban"
              ? getKanbanData()
              : setShouldFetchData(true);
          }
        }
      }
    } catch (error) {
      console.log(`Error ${error}`);
      toastNotification("error", t("toasts.somethingWrong"));
    }
  };

  const reopenTicketForGuest = async (elementId, idPipeline) => {
    try {
      let selectedPip = selectedPipeline || idPipeline;
      let pipelineObj =
        pipelinesRef?.current &&
        pipelinesRef?.current?.find(
          (item) => Number(item?.id) === Number(selectedPip)
        );
      await MainService.updateElementStage(
        familyId,
        {
          new_stage_id: pipelineObj?.stages[0]?.id,
          id_element: elementId,
        },
        pipelineObj?.stages[0]?.id
      );
      displayedView === "Kanban" ? getKanbanData() : setShouldFetchData(true);
    } catch (error) {
      console.log(`Error reopen, ${error}`);
      toastNotification("error", t("toasts.somethingWrong"));
    }
  };
  //
  useEffect(() => {

    return () => clearStates();
  }, [clearStates]);
  //
  return (
    <>
      <div className="flex items-center justify-center pt-2">
        {!checkIfPathOnView360(location.pathname) &&
          location?.pathname !== "/settings/guests" && (
            <KpiFamily
              familyId={familyId}
              shouldFetchData={shouldFetchData}
              pipelineId={selectedPipeline}
              pipelines={pipelines}
              dataFolders={dataFolders}
              idFolder={ticketsFolderId}
              total={totalNumberOfElements}
            />
          )}
      </div>
      <div
        className={`pt-2" relative w-full ${
          displayedView === "Kanban" ? "" : " space-y-3"
        }`}
      >
        <div className="flex w-full flex-row items-center overflow-hidden px-4">
          <div className="basis-1/3 ">
            <div className="flex justify-start space-x-2">
              {selectedRowKeys.length && (familyId === 4 || familyId === 6) ? (
                <ActionDropDown
                  selectedRowKeys={selectedRowKeys}
                  role={user?.role}
                  familyId={familyId}
                  setSelectedRowKeys={setSelectedRowKeys}
                  setShouldFetchData={setShouldFetchData}
                  setModalType={setModalType}
                  setOpenModal={setOpenModal}
                  folderId={familyId === 6 ? ticketsFolderId : null}
                />
              ) : selectedRowKeys.length ? (
                <Button
                  disabled={!roles?.includes(user?.role) && familyId === 4}
                  type="primary"
                  danger
                  onClick={() =>
                    handleDelete(
                      t,
                      familyId,
                      null,
                      null,
                      selectedRowKeys,
                      setSelectedRowKeys,
                      setShouldFetchData,
                      dispatch
                    )
                  }
                >
                  {`${t("contacts.delete")} (${selectedRowKeys.length})`}
                </Button>
              ) : (
                <></>
              )}
              <div ref={Refs_IDs.families_input_search}>
                <Input
                  disabled={
                    !dataTable?.length &&
                    !searchElement?.length &&
                    displayedView === "Table"
                  }
                  style={{ maxWidth: "15rem", minWidth: "12rem" }}
                  allowClear
                  placeholder={t("table.search3")}
                  value={displaySearch}
                  onChange={handleSearch}
                  prefix={<FiSearch className="text-slate-400" />}
                  suffix={
                    <Tooltip
                      title={
                        displayedView === "Kanban"
                          ? t("companies.searchInfoInModuleInKanban")
                          : t("companies.searchInfoInModuleInTable")
                      }
                    >
                      <InfoCircleOutlined style={{ color: "#1890ff" }} />
                    </Tooltip>
                  }
                />
              </div>
              {!checkIfPathOnView360(location.pathname) &&
                displayedView === "Table" && (
                  <div className="flex space-x-2">
                    <FilterFamilyTable
                      familyId={familyId}
                      // pipelineId={selectedPipeline}
                      disabled={!dataTable?.length && !dynamicFilter.length}
                      setDynamicFilter={setDynamicFilter}
                      dynamicFilter={dynamicFilter}
                      handlePipelineChange={handleSelectChange}
                      setTicketsFolderId={setTicketsFolderId}
                      tableRef={tableRef}
                      setIsFirstRender={setIsFirstRender}
                    />

                    {/* {(user.id === "66a3b88d956860ca6308cb44" ||
                        user.id === "66827c78558f7d4aed076212" ||
                        user.id === "667c1a0641b03e7f73004b13" ||
                        user.id === "667ef9a9177386131e0474e4") && ( */}
                    <SortFamilyTables
                      sortedInfo={sortedInfo}
                      handleSort={handleChangeSortData}
                      disabled={!dataTable?.length || !columns.length}
                      columns={columns}
                    />
                  </div>
                )}

              {familyId === 6 && (
                <Tooltip
                  title={
                    !!dynamicFilter.length && displayedView !== "Kanban"
                      ? t("contacts.selectFolderDisabled")
                      : ""
                  }
                >
                  <div>
                    <SelectFolder
                      setIdFolder={setTicketsFolderId}
                      idFolder={ticketsFolderId}
                      dataFolders={dataFolders}
                      setDataFolders={setDataFolders}
                      isDisabled={
                        (!dataTable.length &&
                          !ticketsFolderId &&
                          displayedView === "Table") ||
                        (!!dynamicFilter.length && displayedView !== "Kanban")
                      }
                    />
                  </div>
                </Tooltip>
              )}
            </div>
          </div>
          <div className="basis-1/3 ">
            <div className=" flex justify-center">
              {!checkIfPathOnView360(location.pathname) && (
                <div className="relative mt-2.5 space-x-2">
                  <NavHeader
                    source="module"
                    SelectOptions={displayPipelines(pipelines, displayedView)}
                    segmentedData={[
                      {
                        value: "Table",
                        icon: (
                          <Tooltip
                            title={
                              !dynamicFilter.length
                                ? t("tasks.tableViewTooltip")
                                : ""
                            }
                          >
                            <TableOutlined
                              style={{ fontSize: 15, marginTop: 6 }}
                            />
                          </Tooltip>
                        ),
                      },
                      {
                        value: "Kanban",
                        icon: (
                          <Tooltip
                            title={!dynamicFilter.length ? "Kanban" : ""}
                          >
                            <MdOutlineTableChart
                              style={{ fontSize: 17, marginTop: 5 }}
                            />
                          </Tooltip>
                        ),
                      },
                    ]?.filter((el) => el !== null)}
                    handleSegmentedChange={handleSegmentedChange}
                    onSelectChange={handleSelectChange}
                    defaultValues={
                      pipelines && Object.values(pipelines)?.length > 0
                        ? defaultValues
                        : { selectedPipeline: null, switchViews: displayedView }
                    }
                    setPipelines={setPipelines}
                    isDisabled={
                      !!dynamicFilter.length && displayedView !== "Kanban"
                    }
                  />
                </div>
              )}
            </div>
          </div>
          <div className="basis-1/3 ">
            <div className="flex justify-end space-x-2">
              {(familyId === 6 || familyId === 9 || familyId === 2) && (
                <div ref={Refs_IDs.families_statistics}>
                  <StatsDrawer familyId={familyId} />
                </div>
              )}
              {!tableIsLoading &&
                columns?.length > 0 &&
                dataTable?.length > 0 && (
                  <ExportButton
                    familyId={familyId}
                    search={searchElement}
                    total={totalNumberOfElements}
                    role={user?.role}
                    // pathName={location?.pathname}
                    pipelineId={selectedPipeline}
                    folderId={familyId === 6 && ticketsFolderId}
                    filters={dynamicFilter}
                    sort={sortedInfo}
                  />
                )}
              {columns.length > 0 && displayedView === "Table" && (
                <ShowHideColumns
                  showColumns={showColumns}
                  setShowColumns={setShowColumns}
                  familyId={familyId}
                />
              )}

              {displayedView === "Kanban" &&
                !isGuestConnected(chatRole, user?.role) &&
                roles.includes(user?.role) && (
                  <Tooltip
                    /*title={`Go to ${location?.pathname.split("/")[1]}' pipelines settings`}*/ title={
                      <div
                        dangerouslySetInnerHTML={{
                          __html: t("companies.kanbanSettingsInfo", {
                            moduleLabel: location?.pathname.split("/")[1],
                          }),
                        }}
                      ></div>
                    }
                  >
                    <Button
                      icon={<SettingOutlined />}
                      type="text"
                      shape="circle"
                      onClick={() => {
                        let pathname;
                        const firstSegment = location?.pathname?.split("/")[1];
                        const secondSegment = location?.pathname?.split("/")[2];

                        if (firstSegment === "leads") {
                          pathname = "/lead";
                        } else if (firstSegment === "booking") {
                          pathname = "/booking";
                        } else if (firstSegment.includes("companie")) {
                          pathname = "/organisation";
                        } else if (secondSegment === "products") {
                          pathname = "/product";
                        } else {
                          pathname = firstSegment
                            ? `/${firstSegment.slice(0, -1)}`
                            : "/";
                        }
                    
                         const family = families.find(item=>item?.label?.toLowerCase() === pathname.toLowerCase()
                          .slice(1, pathname.length))
                       
                        navigate(
                          `/settings/pipeline/${family?.label}`
                        
                        );
                      }}
                    />
                  </Tooltip>
                )}
            </div>
          </div>
        </div>
        {displayedView === "Kanban" ? (
          <KanbanBoard
            columns={kanbanData}
            setColumns={setKanbanData}
            source=""
            setElementDetailToUpdate={setElementDetailToUpdate}
            setOpenDrawerUpdate={setOpenDrawerUpdate}
            setOpenDrawerCreate={setOpenDrawerCreate}
            loadDeals={loadDeals}
            getKanbanData={getKanbanData}
            familyId={familyId}
            scrollParameterElements={scrollParameterElements}
            setScrollParameterElements={setScrollParameterElements}
            scrollingStageIdInElements={scrollingStageIdInElements}
            setScrollingStageIdInElements={setScrollingStageIdInElements}
            getPipelines={getPipelines}
            pipelines={pipelines}
            searchElement={searchElement}
            selectedPipeline={selectedPipeline}
            elementDetailToUpdate={elementDetailToUpdate}
            setSelectedStageId={setSelectedStageId}
            setModalType={setModalType}
            setOpenModal={setOpenModal}
            setActiveRoomId={setActiveRoomId}
            activeRoomId={activeRoomId}
            ticketsFolderId={ticketsFolderId}
            setElementInfo={setElementInfo}
            setOpenDrawerInfo={setOpenDrawerInfo}
            closeTicketForGuest={closeTicketForGuest}
            reopenTicketForGuest={reopenTicketForGuest}
          />
        ) : displayedView === "Gantt" ? (
          <ProjectWrapper />
        ) : (
          <TableView
            columns={columns}
            showColumns={showColumns}
            dataTable={dataTable}
            isLoading={tableIsLoading}
            total={totalNumberOfElements}
            pageLimit={pageLimit}
            numberOfPage={numberOfPage}
            setNumberOfPage={setNumberOfPage}
            setPageLimit={setPageLimit}
            setSelectedRowKeys={setSelectedRowKeys}
            selectedRowKeys={selectedRowKeys}
            isDataExist={isDataExist}
            tableRef={tableRef}
            handleSort={handleChangeSortData}
            sortedInfo={sortedInfo}
          />
        )}
        <DisplayElementInfo
          open={openDrawerInfo}
          setOpen={setOpenDrawerInfo}
          elementDetails={elementInfo}
        />
        {(openDrawerCreate || openCreateForm) && (
          <FormCreate
            open={openDrawerCreate || openCreateForm}
            setOpen={setOpenDrawerCreate}
            familyId={familyId}
            setCatchChange={setCatchCreateOrUpdate}
            selectedPipeline={selectedPipeline}
            selectedStageId={selectedStageId}
            setSelectedStageId={setSelectedStageId}
          />
        )}
        {openDrawerUpdate && (
          <FormUpdate
            open={openDrawerUpdate}
            setOpen={setOpenDrawerUpdate}
            elementDetails={elementDetailToUpdate}
            familyId={familyId}
            setCatchChange={setCatchCreateOrUpdate}
          />
        )}
        {!!openImportDrawer && (
          <ImportDrawer
            open={openImportDrawer}
            setOpen={setOpenImportDrawer}
            family_id={familyId}
            setOpenChildren={setOpenChildrenImportDrawer}
            openChildren={openChildrenImportDrawer}
            typeChildren={typeChildren}
            titleChildren={titleChildren}
          />
        )}
        {openModal && (
          <ModalActionTicket
            isModalOpen={openModal}
            setIsModalOpen={setOpenModal}
            type={modalType}
            detailsElement={elementDetailToUpdate}
            ticketIds={selectedRowKeys}
            setShouldFetchData={setShouldFetchData}
          />
        )}

        {openModalCopyInvite && (
          <InvitationLinkModal
            open={openModalCopyInvite}
            setOpen={setOpenModalCopyInvite}
            t={t}
            data={dataTable}
            elementInfo={elementDetailToUpdate}
          />
        )}
        {openTaskRoomDrawer && (
          <Suspense fallback={<LoaderDrawer />}>
            <TasksRoom
              key={activeRoomId}
              elementId={activeRoomId}
              canCreateRoom={1}
            />
          </Suspense>
        )}
      </div>
    </>
  );
};

export default memo(TableInterface);
