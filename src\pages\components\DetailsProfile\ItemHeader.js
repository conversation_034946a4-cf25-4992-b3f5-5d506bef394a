import { CheckOutlined, CopyOutlined } from "@ant-design/icons";
import { Tooltip, Typography } from "antd";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";

const ItemHeader = ({
  text,
  icon,
  title,
  colorText,
  colorIcon,
  noIconCopy = false,
}) => {
  const [isCopied, setIsCopied] = useState(false);
  const [showCopy, setShowCopy] = useState(false);
  const [t] = useTranslation("common");
  async function copyTextToClipboard(text) {
    if ("clipboard" in navigator) {
      await navigator.clipboard.writeText(text);
      setIsCopied(true);
    } else {
      return document.execCommand("copy", true, text);
    }
  }
  const handleCopyClick = (copyText) => {
    // Asynchronously call copyTextToClipboard
    copyTextToClipboard(copyText)
      .then(() => {
        // If successful, update the isCopied state value
        setIsCopied(true);
        setTimeout(() => {
          setIsCopied(false);
        }, 3000);
      })
      .catch((err) => {
        console.log(err);
      });
  };
  return (
    <div
      className="flex  items-center  space-x-1 "
      onMouseEnter={() => setShowCopy(true)}
      onMouseLeave={() => setShowCopy(false)}
    >
      <Tooltip title={title}>{icon}</Tooltip>

      <Typography.Paragraph
        className={` w-full text-sm ${colorText}`}
        ellipsis={true}
      >
        {text}
        {!noIconCopy ? (
          <Tooltip
            title={isCopied ? t("chat.action.copied") : t("chat.action.copy")}
          >
            <CopyOutlined
              onClick={() => handleCopyClick(text)}
              className={`mx-1  hover:opacity-80 ${colorIcon} ${
                showCopy && !isCopied ? "visible" : "invisible"
              } ${isCopied ? "hidden" : "inline"} `}
            />
            {isCopied && (
              <CheckOutlined
                className={`mx-1  hover:opacity-80 ${colorIcon} ${
                  isCopied ? "visible" : "invisible"
                }`}
              />
            )}
          </Tooltip>
        ) : null}
      </Typography.Paragraph>
    </div>
  );
};

export default ItemHeader;
