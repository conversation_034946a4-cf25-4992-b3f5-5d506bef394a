/**
 * @name Import
 *
 * @description `Import` component enables the system to integrate and bring in data from external sources.
 * This module allows users to import customer information, sales data, leads, contacts, and other relevant
 * business data from various file formats like CSV, Excel.
 *
 * @param {Number} familyId This is used when using the import in other module (identify in which module the import is going to occur).
 * @param {Boolean} fromDrawer This is also used when using the import in other module.
 *
 * @returns {JSX.Element} import module.
 */

// React and 3rd party libraries imports.
import { useEffect, useState, useRef, useCallback } from "react";
import {
  ArrowRightOutlined,
  CloseOutlined,
  FileDoneOutlined,
  FileExcelOutlined,
  FileTextOutlined,
  InfoCircleOutlined,
  LoadingOutlined,
  QuestionCircleOutlined,
  ReloadOutlined,
  RestOutlined,
  RightOutlined,
  WarningOutlined,
} from "@ant-design/icons";
import {
  Button,
  Steps,
  Upload,
  message,
  Checkbox,
  Form,
  Space,
  Table,
  Select,
  Col,
  Radio,
  Tooltip,
  Progress,
  Spin,
  Row,
  Collapse,
  Drawer,
  Divider,
  Typography,
  Popconfirm,
  Tag,
} from "antd";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import uniq from "lodash/uniq";
import { filter } from "lodash";
import { useNavigate, useLocation, Link } from "react-router-dom";
import moment from "moment";
import { motion } from "framer-motion";
import party from "party-js";
import { BsFiletypeXlsx } from "react-icons/bs";
import { BsFiletypeXls } from "react-icons/bs";
import { BsFiletypeCsv } from "react-icons/bs";

// Common imports.
import SearchInTable from "../components/Search";
import Confirm from "../../components/GenericModal";
import { toastNotification } from "../../components/ToastNotification";
import { generateAxios } from "../../services/axiosInstance";
import { setSelectedFamily } from "../../new-redux/actions/families.actions/getFamilies";
import { setOpenChildrenImportDrawer } from "../../new-redux/actions/form.actions/form";
import { displayRightIcon } from "../../utils/displayIcon";
import { getSelectedFamilyText } from "../../utils/importSample";
import { humanDate } from "../voip/helpers/helpersFunc";
import RolesUsers from "../../components/RolesUsers";
import Departments from "../../components/Departments";
import TypesContacts from "../clients&users/utility-component/TypesContacts";
import Tags from "../settings/Tags";
import TableCompanies from "../settings/TableCompanies";
import Services from "../../components/Services";
import Severities from "../../components/Severities";
import SlaHelpDesk from "../../components/SlaHelpDesk";
import General from "../settings/General";
import { setIsImportJobDone } from "../../new-redux/actions/import.actions/realtime";
import { URL_ENV } from "index";
import { AvatarChat } from "components/Chat";
import { getName } from "pages/layouts/chat/utils/ConversationUtils";
import { EXTENSIONS_ARRAY } from "pages/tasks/helpers/calculateSum";
import GetListMappingTemplate from "./ListMappingTemplate";
import GetColsMappingTemplate from "./ColumnsMappingTemplate";
import { optionsDateFormat } from "pages/settings/LocalisationSettings";
import "./import.css";

let interval = undefined;

const Import = ({ familyId, fromDrawer }) => {
  const [current, setCurrent] = useState(0);
  const [loadingTable, setLoadingTable] = useState(false);
  const [loadingResult, setLoadingResult] = useState(false);
  const [showPrimaryKeyForce, setShowPrimaryKeyForce] = useState(false);
  const [showPrimaryKeyExtraFields, setShowPrimaryKeyExtraFields] =
    useState(false);
  const [fileUploadSuccess, setFileUploadSuccess] = useState(false);
  const [
    fileUploadedSuccessfullyInStepOne,
    setFileUploadedSuccessfullyInStepOne,
  ] = useState(false);
  const [nameOfColumnDoesExist, setNameOfColumnDoesExist] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [isRestoring, setIsRestoring] = useState(false);
  const [historyData, setHistoryData] = useState([]);
  const [total, setTotal] = useState(0);
  const [importProgress, setImportProgress] = useState(0);
  const [filePreviewHeader, setFilePreviewHeader] = useState([]);
  const [fileSelectOptions, setFileSelectOptions] = useState([]);
  const [dbSelectOptions, setDbSelectOptions] = useState([]);
  const [pkSelectOptions, setPkSelectOptions] = useState([]);
  const [filePreviewData, setFilePreviewData] = useState([]);
  const [listValues, setListValues] = useState([]);
  const [mappedFieldsValues, setMappedFieldsValues] = useState({});
  const [mappedListsValues, setMappedListsValues] = useState({});
  const [delimiter, setDelimiter] = useState("");
  const [uploadSuccessHint, setUploadSuccessHint] = useState("");
  const [family, setFamily] = useState();
  const [filenameMapping, setFilenameMapping] = useState("");
  const [originalFilename, setOriginalFilename] = useState("");
  const [filenameList, setFilenameList] = useState("");
  const [configuration, setConfiguration] = useState("standard");
  const [primaryKey, setPrimaryKey] = useState(null);
  const [historyId, setHistoryId] = useState(0);
  const [fileUploadNbrRows, setFileUploadNbrRows] = useState(0);
  const [fileUploadNbrColumns, setFileUploadNbrColumns] = useState(0);
  const [fileUploadSize, setFileUploadSize] = useState("");
  const [departments, setDepartments] = useState("");
  const [departmentsOptions, setDepartmentOptions] = useState([]);
  const [requiredFields, setRequiredFields] = useState("");
  const [uniqueFields, setUniqueFields] = useState([]);
  const [choosedPkValue, setChoosedPkValue] = useState("");
  const [activateAutoMapping, setActivateAutoMapping] = useState(false);
  const [namelessCsvColsIndexes, setNamelessCsvColsIndexes] = useState([]);
  const [verifRelationLoading, setVerfiRelationLoading] = useState(false);
  const [idOfClickedRecord, setIdOfClickedRecord] = useState(null);
  const [shareWithOthers, setShareWithOthers] = useState(false);
  const [changedListValues, setChangedListValues] = useState([]);
  const [firstTimeSaveMappingArr, setFirstTimeSaveMappingArr] = useState([]);
  const [usersUploadedFileId, setUsersUploadedFileId] = useState(null);
  const [fields, setFields] = useState([]);
  const [hasFamilyId, setHasFamilyId] = useState(false);
  const [dialCodeField, setDialCodeField] = useState([]);
  const [currencies, setCurrencies] = useState([]);
  const [importFieldsParams, setImportFieldsParams] = useState([]);
  const [defaultDialCode, setDefaultDialCode] = useState(null);
  const [defaultCurrency, setDefaultCurrency] = useState(null);
  const [dateTimeConfig, setDateTimeConfig] = useState({
    date: null,
    time: null,
  });
  const [mappingListLoading, setMappingListLoading] = useState(false);
  const [isPhone, setIsPhone] = useState(false);
  const [isMonetary, setIsMonetary] = useState(false);
  const [isDate, setIsDate] = useState(false);
  const [isTime, setIsTime] = useState(false);
  const [columnMappingDefaultValue, setColumnMappingDefaultValue] =
    useState(null);
  const [saveMappingInStepOne, setSaveMappingInStepOne] = useState(false);
  const [uploadedFileId, setUploadedFileId] = useState(null);
  const [restoredMapping, setRestoredMapping] = useState(null);
  const [restoredListMapping, setRestoredListMapping] = useState(null);
  const [confirmLastMapping, setConfirmLastMapping] = useState(false);
  const [loadFields, setLoadFields] = useState(false);
  const [page, setPage] = useState(1);
  const [dateTimeFields, setDateTimeFields] = useState([]);

  const { search } = useSelector((state) => state?.menu);
  const { user } = useSelector((state) => state?.user);
  const historySearch = useSelector((state) => state?.form?.search);
  const { families, selectedFamily } = useSelector((state) => state?.families);
  const { openChildrenImportDrawer, typeChildren, titleChildren } = useSelector(
    (state) => state?.form
  );
  const { inviteUsersJobDone } = useSelector((state) => state?.importReducer);
  // Handles the configuration form values (step_1)
  const [configurationForm] = Form.useForm();
  // Handles if the first line of the file contains the column names (step_1)
  const [nameOfColumnDoesExistForm] = Form.useForm();
  // Handles the values of the columns mapping (step_2)
  const [mappedFieldsForm] = Form.useForm();
  // Handles the values of the lists mapping (step_3)
  const [mappedListsForm] = Form.useForm();
  const [t, i18n] = useTranslation("common");
  const dispatch = useDispatch();
  const { Dragger } = Upload;
  const { Panel } = Collapse;
  const nameLessCsvColumnRef = useRef(null);
  const navigate = useNavigate();
  const location = useLocation();

  //Handle navigate to the module after the import was successfull.
  const navigateToFamily = () => {
    switch (family) {
      case 1:
        window.open("/companies", "_blank");
        break;
      case 2:
        window.open("/contacts", "_blank");
        break;
      case 3:
        window.open("/deals", "_blank");
        break;
      case 4:
        window.open("/settings/users", "_blank");
        break;
      case 5:
        window.open("/settings/products", "_blank");
        break;
      case 6:
        window.open("/tickets", "_blank");
        break;
      case 7:
        window.open("/projects", "_blank");
        break;
      case 8:
        window.open("/booking", "_blank");
        break;
      case 11:
        window.open("/invoices", "_blank");
        break;
      case 12:
        window.open("/transactions", "_blank");
        break;
      default:
        break;
    }
  };

  // name of the steps in the stepper component
  const itemsOfSteps = [
    {
      title: t("import.uploadSample"),
    },
    {
      title: t("import.mapColumns"),
    },
    {
      title: t("import.mapLists"),
    },
    {
      title: t("import.import"),
    },
  ];

  //Display files to download in history table.
  const DownloadSamplesFromTable = ({ txtFile, xlsFile }) => {
    return (
      <div className="divide-y-1 flex h-full flex-col justify-around">
        <Tooltip title="Download excel file">
          <Tag
            icon={<FileExcelOutlined />}
            bordered={false}
            color="success"
            style={{ marginBottom: "3px", cursor: "pointer" }}
            onClick={() => {
              if (txtFile && txtFile !== null) {
                downloadFile(
                  `${
                    URL_ENV?.REACT_APP_BASE_URL +
                    process.env.REACT_APP_SUFFIX_API
                  }download-file/${xlsFile}`,
                  xlsFile
                );
              }
            }}
          >
            Excel
          </Tag>
        </Tooltip>
        <Tooltip title="Download text file" placement="right">
          <Tag
            icon={<FileTextOutlined />}
            bordered={false}
            color="default"
            style={{ marginTop: "3px", cursor: "pointer" }}
            onClick={() => {
              if (txtFile && txtFile !== null) {
                downloadFile(
                  `${
                    URL_ENV?.REACT_APP_BASE_URL +
                    process.env.REACT_APP_SUFFIX_API
                  }download-file/${txtFile}`,
                  txtFile
                );
              }
            }}
          >
            Text
          </Tag>
        </Tooltip>
      </div>
    );
  };

  // columns data in the history table
  const columnsHistory = [
    {
      title: t("import.date"),
      dataIndex: "date",
      key: "date",
      fixed: "left",
      sorter: (a, b) => moment(a.date).unix() - moment(b.date).unix(),
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title={humanDate(record.date, t)}>
            {/* <span>{moment(record.date).format("YYYY/MM/DD HH:mm")}</span> */}
            <span>
              {moment(record?.date).format(
                `${user?.location?.date_format} ${user?.location?.time_format}`
              )}
            </span>
          </Tooltip>
        </Space>
      ),
    },
    {
      title: t("import.fileName"),
      dataIndex: "originalFileName",
      key: "filename",
      fixed: "left",
      width: 200,
      render: (record) => {
        return (
          <Typography.Text ellipsis={{ tooltip: record }}>
            {record}
          </Typography.Text>
        );
      },
    },
    {
      title: t("import.ok"),
      dataIndex: "ok",
      key: "ok",
      render: (_, record) => (
        <Space size="middle">
          {record.ok > 0 && (
            <span>
              {record.ok} &nbsp;
              {record.filename && (
                <Tooltip
                  title={t("import.downloadFileWithTitle", {
                    filename: record.filename,
                  })}
                >
                  {/* [ */}
                  <a
                    onClick={() =>
                      downloadFile(
                        `${
                          URL_ENV?.REACT_APP_BASE_URL +
                          process.env.REACT_APP_SUFFIX_API
                        }download-file/${record.filename}`,
                        record.filename
                      )
                    }
                  >
                    {t("import.download")}
                  </a>
                  {/* ] */}
                </Tooltip>
              )}
            </span>
          )}
          {record.ok === 0 && <span>{record.ok}</span>}
        </Space>
      ),
    },
    ...(Number(family) === 4
      ? [
          {
            title: "Invite",
            dataIndex: "invite",
            key: "invite",
            width: 90,
            render: (_, record) => (
              <div>
                {record?.ok > 0 && (
                  <Space size="middle">
                    <Tooltip
                      title={t("import.inviteUsersTooltip", {
                        invitedNumber: record?.invite,
                        importedNumber: record?.ok,
                      })}
                      className="flex flex-row items-center justify-between"
                    >
                      {record?.invite}/{record?.ok}
                    </Tooltip>
                  </Space>
                )}
                <span
                  onClick={() =>
                    navigate("/settings/users", {
                      state: { importId: record?.id },
                    })
                  }
                  className="text-[#1677ff] transition duration-300 hover:cursor-pointer hover:text-[#69b1ff]"
                >
                  {" "}
                  {t("import.inviteUsers")}
                </span>
              </div>
            ),
          },
        ]
      : []),
    {
      title: (
        <>
          {t("import.duplicates")}{" "}
          <Tooltip title="You can download the file in .txt format or .xls format">
            <QuestionCircleOutlined style={{ cursor: "help" }} />
          </Tooltip>
        </>
      ),
      dataIndex: "duplicates",
      key: "duplicates",
      align: "center",
      className: "no-padding-cell",
      render: (_, record) => {
        return (
          <div className="divide-x-1 flex w-full flex-row items-center justify-around">
            <p>{record.duplicates}</p>
            <DownloadSamplesFromTable
              txtFile={record?.txtFileDuplicate}
              xlsFile={record?.xlsFileDuplicate}
            />
          </div>
        );
      },
    },
    {
      title: (
        <>
          {t("import.errors")}{" "}
          <Tooltip title="You can download the file in .txt format or .xls format">
            <QuestionCircleOutlined style={{ cursor: "help" }} />
          </Tooltip>
        </>
      ),
      dataIndex: "errors",
      key: "errors",
      align: "center",
      className: "no-padding-cell",
      render: (_, record) => (
        <div className="divide-x-1 flex w-full flex-row items-center justify-around">
          <p>{record?.errors}</p>
          <DownloadSamplesFromTable
            txtFile={record?.txtFileError}
            xlsFile={record?.xlsFileError}
          />
        </div>
      ),
    },
    {
      title: t("import.archived"),
      dataIndex: "archived",
      key: "archived",
      render: (_, record) => (
        <>
          <Space size="middle">
            <span>{record.archived}</span>
          </Space>
        </>
      ),
    },
    {
      title: t("import.deleted"),
      dataIndex: "deleted",
      key: "deleted",
      render: (_, record) => (
        <>
          <Space size="middle">
            <span>{record.deleted}</span>
          </Space>
        </>
      ),
    },
    {
      title: t("import.status"),
      dataIndex: "status",
      key: "status",
      render: (_, record) => (
        <>
          <Space size="middle">
            {(record.ok !== 0 ||
              record.duplicates !== 0 ||
              record.errors !== 0) && <span>{t("import.completed")}</span>}
            {record.ok === 0 &&
              record.duplicates === 0 &&
              record.errors === 0 &&
              record.mapping_list === null && (
                <span>{t("import.mappingColumn")}</span>
              )}
            {record.ok === 0 &&
              record.duplicates === 0 &&
              record.errors === 0 &&
              record.mapping_list !== null && (
                <span>{t("import.mappingList")}</span>
              )}
          </Space>
        </>
      ),
    },
    {
      title: "Type",
      key: "actions",
      render: (_, { force, extraFields }) => (
        <>
          {force == "1"
            ? "Force"
            : extraFields == "1"
            ? "Extra fields "
            : "Standard"}
        </>
      ),
      filter,
    },
    {
      title: "Owner",
      key: "importOwner",
      render: (prop) => {
        return (
          <Tooltip title={prop?.importOwner?.name.replace("_", " ")}>
            <div className="flex items-center justify-center">
              <AvatarChat
                fontSize={"0.875rem"}
                className="mx-1.5 flex items-center justify-center"
                height={"32px"}
                width={"32px"}
                url={`${
                  URL_ENV?.REACT_APP_BASE_URL +
                  URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                }${prop?.importOwner?.avatar}`}
                hasImage={EXTENSIONS_ARRAY?.includes(
                  prop?.importOwner?.avatar?.split(".")?.pop()
                )}
                name={getName(props?.importOwner?.name, "avatar")}
                type="user"
              />
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: t("import.actions"),
      dataIndex: "actions",
      key: "actions",
      render: (_, record) => (
        <Space>
          {record.archived === 0 && record.deleted === 0 && (
            <>
              {idOfClickedRecord === record?.id && verifRelationLoading && (
                <LoadingOutlined spin />
              )}
              <a
                onClick={() => {
                  setIdOfClickedRecord(record.id);
                  showConfirmDelete(record.id, record.familyElementsCount);
                }}
              >
                {t("import.delete")}
              </a>
            </>
          )}
          {record.ok === 0 &&
            record.duplicates === 0 &&
            record.errors === 0 && (
              <a onClick={() => restoreMapping(record?.id, record?.fileName)}>
                {t("import.continue")}
              </a>
            )}
          {record.imported_filename !== null && (
            <a onClick={() => downloadFileImported(record)}>
              {t("import.download")}
            </a>
          )}
        </Space>
      ),
    },
  ];

  //This message will pop up in case when the user wants to delete a row from the history table
  //and the entities of that import have relations in the sphere db.
  const confirmDeleteMessage = (object) => {
    let families = Object.keys(object?.families).map(
      (el) => `[${el}: ${object?.families[el]?.length} relation(s)]`
    );
    return families.join(", ");
  };

  // delete popup in an import record from the history table
  const showConfirmDelete = async (historyId, familyElementsCount) => {
    try {
      setVerfiRelationLoading(true);
      const verifRelationResponse = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).get(`verif-relation/${historyId}`);
      if (verifRelationResponse?.status === 200) {
        setVerfiRelationLoading(false);
        Confirm(
          t("import.deleteRecordsNotification"),
          t("import.deleteAction"),
          <RestOutlined style={{ color: "red" }} />,
          function func() {
            return handleDeleteAndArchiveRecords(historyId);
          },
          true,
          t("import.deleteDescription", {
            historyId,
          })
        );
      }
      setIdOfClickedRecord(null);
    } catch (error) {
      setVerfiRelationLoading(false);
      if (error?.response?.status === 409) {
        let okText = null;
        Confirm(
          t("import.deleteAndArchiveRecordsNotification"),
          okText,
          <RestOutlined style={{ color: "red" }} />,
          function func() {
            return handleDeleteAndArchiveRecords(historyId);
          },
          true,
          t("import.deleteArchiveSummary", {
            historyId,
            families: confirmDeleteMessage(error?.response?.data?.message),
            archived: error?.response?.data.message?.archive,
            deleted: error?.response?.data?.message?.delete,
          })
        );
      }
      console.log(error);
      setIdOfClickedRecord(null);
    }
  };

  // delete import record action in the popup
  const handleDeleteAndArchiveRecords = async (historyId) => {
    try {
      await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).delete(`upload/${historyId}`);
      toastNotification("success", t("toasts.success"), "topRight");
      getDataHistory();
    } catch (error) {
      console.log(error);
    }
  };

  // onChange of the generic tab families
  const onChangeFamilyTabsItem = (key) => {
    setIsLoading(true);
    dispatch(setSelectedFamily(Number(key)));
    setTimeout(() => {
      setIsLoading(false);
      setConfiguration("standard");
      setFamily(key);
      handleChangeFamilies(key);
      setFileUploadSuccess(false);
      setFileUploadedSuccessfullyInStepOne(false);
      setShowPrimaryKeyExtraFields(false);
      setShowPrimaryKeyForce(false);
    }, 300);
  };

  // Handle download file (used in download sample and in history table).
  const downloadFile = async (targetUrl, filename) => {
    try {
      const data = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).get(targetUrl);
      const blob = new Blob([data?.data]);
      var a = document.createElement("a");
      a.href = window.URL.createObjectURL(blob);
      a.download = filename;
      a.dispatchEvent(new MouseEvent("click"));
    } catch (err) {
      console.log("Error: ", err);
    }
  };

  // download a file from a `download-imported-file/${record.id}` web service
  const downloadFileImported = async (record) => {
    try {
      const { data: result } = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).get(`download-imported-file/${record.id}`);
      const blob = new Blob([result], { type: "text/plain;charset=utf-8" });
      var a = document.createElement("a");
      a.href = window.URL.createObjectURL(blob);
      a.download = record.originalFileName;
      a.dispatchEvent(new MouseEvent("click"));
    } catch (err) {
      console.log("Error: ", err);
    }
  };

  // map unknown data from the res array and store it in th dataArray used for restoring data on continue click from the history table
  function insertDataIntoArray(res, i, dataArray) {
    let dataObj = {};
    res.data.message?.data[i]?.map((eData, index) => {
      dataObj = {
        ...dataObj,
        [t("import.unkownColumnName", { index: index + 1 })]: eData,
      };
    });
    dataObj = { ...dataObj, key: `${i}` };
    dataArray.push(dataObj);
  }

  // map unknown data from the res array and store it in th dataArray used for restoring data on continue click from the history table
  function insertCustomDataIntoArray(res, i, dataArray) {
    let dataObj = {};
    res.message?.data[i]?.map((eData, index) => {
      dataObj = {
        ...dataObj,
        [res.message?.data[0][index].toLowerCase()]: eData,
      };
    });
    dataObj = { ...dataObj, key: `${i}` };
    dataArray.push(dataObj);
  }

  // map Header data if header argument is 1 else map unknown column name used for displaying Columns Header data on step 1 (columns mapping) when clicking restore
  function addNewColumnData(columsArray, e, index, header) {
    if (header === 1) {
      columsArray.push({
        title: e.toUpperCase(),
        dataIndex: e.toLowerCase(),
        key: `${index}`,
      });
    } else {
      columsArray.push({
        title: t("import.unkownColumnName", { index: index + 1 }),
        dataIndex: t("import.unkownColumnName", { index: index + 1 }),
        key: `${index}`,
      });
    }
  }

  // Handle retrieve rows example from the downloaded file.
  const getExampleRows = async (ID) => {
    try {
      let url = `get-example-rows/${ID}`;
      const fmData = new FormData();
      let config = {
        headers: { "content-type": "multipart/form-data" },
      };

      const response = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).get(url, fmData, config);
      return response;
    } catch (error) {
      message.error(
        `${t("import.restoreMappingFailed", {
          error: error.response?.data?.message,
        })}`
      );
      setIsLoading(false);
      console.log("Error: ", error);
    }
  };

  // GET MAPPING RETURNS WHERE THE MAPPING WAS STOPPED
  // all the steps for restore mapping used in columns mapping and list mapping (step1+2)
  const restoreMapping = async (IdHistory, filename) => {
    try {
      setIsLoading(true);
      let step = 0;
      let mappingFormValues = {};
      let valuesCSV = [];
      setHistoryId(IdHistory);
      setIsRestoring(true);
      setFileUploadSuccess(true);
      mappedFieldsForm.resetFields();
      mappedListsForm.resetFields();
      window.scrollTo(0, 0);
      let url = `get-mapping/${IdHistory}`;
      let config = {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      };
      const resGetMapping = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).get(url, config);
      if (resGetMapping?.data?.step === 1) {
        let expandedArray = Object.entries(
          resGetMapping?.data?.mapping
        ).flatMap(([key, value]) => {
          if (Array.isArray(value)) {
            return value.map((subValue) => ({ [key]: subValue }));
          } else {
            return { [key]: value };
          }
        });
        setRestoredMapping(expandedArray);
        setCurrent(1);
      } else if (resGetMapping?.data?.step === 2) {
        setRestoredListMapping(resGetMapping?.data?.mapping_list);
        setCurrent(2);
        Object.values(resGetMapping?.data?.data).findIndex(
          (el) => el?.family !== ""
        ) > -1
          ? setHasFamilyId(true)
          : setHasFamilyId(false);
        setListValues(resGetMapping?.data?.data);

        Object.keys(resGetMapping?.data?.data).map((keyList, keyListIndex) => {
          resGetMapping?.data?.data[keyList]?.values_csv.map(
            (csv, csvIndex) => {
              valuesCSV.push(csv);
            }
          );
        });
        let mappedListsFormValues = {};
        for (
          let mappedListIndex = 0;
          mappedListIndex < valuesCSV.length;
          mappedListIndex++
        ) {
          const csvItem = valuesCSV[mappedListIndex];
          const listItem = resGetMapping.data?.mapping_list[mappedListIndex];
          if (listItem !== "null") {
            mappedListsFormValues = {
              ...mappedListsFormValues,
              [`${csvItem}-${mappedListIndex}`]: listItem,
            };
          } else {
            mappedListsFormValues = {
              ...mappedListsFormValues,
              [`${csvItem}-${mappedListIndex}`]: undefined,
            };
          }
        }
        mappedListsForm.setFieldsValue(mappedListsFormValues);
      }
      setFamily(resGetMapping.data.family_id);
      if (resGetMapping?.data?.mapping) {
        let expandedArray = Object.entries(
          resGetMapping?.data?.mapping
        ).flatMap(([key, value]) => {
          if (Array.isArray(value)) {
            return value.map((subValue) => ({ [key]: subValue }));
          } else {
            return { [key]: value };
          }
        });
        setRestoredMapping(expandedArray);
      }
      if (resGetMapping.data.header === 0) {
        setNameOfColumnDoesExist(false);
      } else {
        setNameOfColumnDoesExist(true);
      }
      if (resGetMapping.data.force && resGetMapping.data.force === 1) {
        setConfiguration("force");
      } else if (
        resGetMapping.data.fusion_extra &&
        resGetMapping.data.fusion_extra === 1
      ) {
        setConfiguration("extraFields");
      }

      if (resGetMapping.data?.shared !== null) {
        setDepartments(resGetMapping.data?.shared);
      }

      if (resGetMapping.data.mapping !== null) {
        setActivateAutoMapping(true);
      }

      setPrimaryKey(resGetMapping.data.primary_key);
      const { data } = await getExampleRows(IdHistory);
      let columsArray = [];
      let dataArray = [];
      data.message?.data[0]?.map((eColumn, index) => {
        if (index === 0 && data.message?.data[0].length === 1) {
          const exampleData = data.message?.data[0][0].split(
            data.message.delimiter
          );
          exampleData?.map((e, index) => {
            addNewColumnData(columsArray, e, index, resGetMapping.data.header);
          });
        } else {
          addNewColumnData(
            columsArray,
            eColumn,
            index,
            resGetMapping.data.header
          );
        }
      });
      insertCustomDataIntoArray(data, 1, dataArray);
      setFilePreviewData(dataArray);
      setDelimiter(data.message.delimiter);
      setFilePreviewHeader(columsArray);
      handleChangeFamilies(resGetMapping.data.family_id);
      if (resGetMapping.data.mapping) {
        Object.keys(resGetMapping.data.mapping).forEach((e, i) => {
          mappingFormValues = {
            ...mappingFormValues,
            [resGetMapping.data.mapping[e]]: parseInt(e),
          };
        });
        mappedFieldsForm.setFieldsValue(mappingFormValues);
        setMappedFieldsValues(mappingFormValues);
      }
      mappedFieldsForm.setFieldsValue(mappingFormValues);
      setMappedFieldsValues(mappingFormValues);
      setIsLoading(false);
    } catch (err) {
      message.error(
        `${t("import.restoreMappingFailed", {
          error: err.response?.data?.message,
        })}`
      );
      setIsLoading(false);
      console.log("Error: ", err);
    }
  };

  // the submit of file upload in the step 0 and setting the fields state in the step 1
  const uploadFile = async (options) => {
    const { onSuccess, onError, file } = options;

    const allowedFileList = [
      "application/excel",
      "application/vnd.ms-excel",
      "application/x-excel",
      "application/x-msexcel",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "text/csv",
    ];

    try {
      await configurationForm.validateFields();
    } catch (err) {
      return;
    }

    if (allowedFileList.includes(file.type) === false) {
      message.error(`${file.name} ${t("import.notSupportedFormat")}`);
      return;
    }

    let primeKey = configurationForm.getFieldsValue()?.primaryKey;

    setPrimaryKey(primeKey);

    const fmData = new FormData();
    const configData = {
      headers: { "content-type": "multipart/form-data" },
    };

    fmData.append("import_file", file);
    fmData.append("family_id", family);

    if (nameOfColumnDoesExist) {
      fmData.append("header", 1);
    } else {
      fmData.append("header", 0);
    }

    try {
      setIsLoading(true);
      const res = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).post("upload-file", fmData, configData);
      message.success(`${file.name} ${t("import.uploadedSuccess")}`);
      setUploadedFileId(res?.data?.message?.id);
      onSuccess("Ok");
      let columsArray = [];
      let dataArray = [];

      setNamelessCsvColsIndexes([
        ...namelessCsvColsIndexes,
        res?.data?.message?.data[0]
          .map((el, i) => (el === "" ? i : null))
          .filter((el) => el !== null),
      ]);
      for (let i = 0; i < res.data.message?.data.length; i++) {
        if (i === 0) {
          res.data.message?.data[i]?.map((eColumn, index) => {
            if (nameOfColumnDoesExist === true) {
              columsArray.push({
                title: eColumn.toUpperCase(),
                dataIndex: eColumn.toLowerCase(),
                key: `${index}`,
              });
            } else {
              columsArray.push({
                title: t("import.unkownColumnName", { index: index + 1 }),
                dataIndex: t("import.unkownColumnName", { index: index + 1 }),
                key: `${index}`,
              });
            }
          });
          if (nameOfColumnDoesExist === false) {
            insertDataIntoArray(res, i, dataArray);
          }
        } else {
          if (nameOfColumnDoesExist === true) {
            insertCustomDataIntoArray(res, i, dataArray);
          } else {
            insertDataIntoArray(res, i, dataArray);
          }
        }
      }
      setFilePreviewHeader(columsArray);
      setFilePreviewData(dataArray);
      setFileUploadSuccess(true);
      setFileUploadedSuccessfullyInStepOne(true);
      setFilenameMapping(res.data.message?.filename);
      setOriginalFilename(res.data.message?.origine_filename);
      setDelimiter(res.data.message?.delimiter);
      setHistoryId(res.data.message?.id);
      setFileUploadNbrRows(res.data.message?.rows);
      setFileUploadSize(res.data.message?.size);
      setFileUploadNbrColumns(res.data.message?.columns);
      restoreMapping(res.data.message?.id, res.data.message?.filename);
      setDefaultDialCode(res.data.message?.dial_code);
      Number(family) === 4 && setUsersUploadedFileId(res?.data?.message?.id);
    } catch (err) {
      console.log("Error: ", err);
      setDbSelectOptions([]);
      setIsLoading(false);
      message.error(`${file.name} ${t("import.uploadFailed")}`);
      onError({ err });
    }
  };

  /**
   * Object containing properties for file upload configuration.
   * @type {Object}
   * @property {Function} customRequest - The function to handle custom file upload requests.
   * @property {boolean} multiple - Indicates whether multiple files can be uploaded.
   * @property {Array} fileList - An array to store the list of files uploaded.
   */
  const props = {
    customRequest: uploadFile,
    multiple: false,
    fileList: [],
  };

  // getPrimayKeys WS
  const getPrimaryKeys = async (value) => {
    let url = `getFieldsPrimaryKey/${value}`;
    let config = {
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
      },
    };
    try {
      const res = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).get(url, config);
      let pkArray = [];
      res.data?.message?.map((e, index) => {
        pkArray.push({
          key: e.id,
          value: e.id,
          label: e.label,
        });
      });
      setPkSelectOptions(pkArray);
      setFamily(value);
    } catch (err) {
      setPkSelectOptions([]);
      console.log("Error: ", err);
    }
  };

  // on family tab change set the primary keys and fields for the import options and step 1 (mapping columns)
  const handleChangeFamilies = async (value) => {
    setIsLoading(true);
    setFamily(Number(value));
    setIsLoading(false);
  };

  // set current step to 1 (column mapping)
  const showStepTwo = async () => {
    const { data } = await saveUploadConfig();
    if (data?.success) {
      setCurrent(1);
    } else {
      message.error("Something went wrong");
    }
    // setCurrent(1);
  };

  // pagination of history table
  const getDataHistory = useCallback(
    async (offset) => {
      setLoadingTable(true);
      try {
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get(`histories/${family}?page=${page}&search=${historySearch}`);
        setLoadingTable(false);
        let historiesArray = [];
        res?.data?.data?.map((e, index) => {
          historiesArray.push({
            key: `${index}`,
            date: e?.created_at,
            originalFileName: e?.origine_filename,
            fileName: e?.filename,
            ok: e?.nbr_ok,
            duplicates: e?.nbr_doublons,
            errors: e?.nbr_error,
            id: e?.id,
            txtFileError: e?.file_error,
            xlsFileError: e?.file_csv_error,
            txtFileDuplicate: e?.file_doublons,
            xlsFileDuplicate: e?.file_csv_doublons,
            archived: e?.archived,
            deleted: e?.deleted,
            familyElementsCount: e?.family_elements_count,
            fileLog: e?.file_log,
            mapping_list: e?.mapping_list,
            imported_filename: e?.imported_filename,
            force: e?.force,
            extraFields: e?.fusion_extra,
            importOwner: e?.user,
            invite: e?.invited,
          });
        });
        setHistoryData(historiesArray);
        setTotal(Number(res?.data?.meta?.total));
      } catch (err) {
        console.log("Error: ", err);
      }
    },
    [page, family, historySearch]
  );

  // when submitting current step 2 mapping column :
  // 1) check if the selected fields contain lists or modules then pass to mapping list (step 3)
  // 2) if the selected fields doesn't contain any lists then pass to step 4
  const submitMapping = async () => {
    try {
      await configurationForm.validateFields();
    } catch (err) {
      return;
    }
    const { fmData, config } = getSubmitMappingFormData(configuration);

    fmData.append("history_id", historyId);

    let mappedFields = mappedFieldsForm.getFieldsValue();
    let fieldsArray = formatMappingValues(Object.values(mappedFields));
    let payload = {};
    fmData.forEach((value, key) => (payload[key] = value));
    let jsonData = JSON.stringify({ ...payload, fields: fieldsArray });

    try {
      setMappingListLoading(true);
      const res = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).post("mapping-list", jsonData, config);

      Object.values(res.data?.data).findIndex((el) => el?.family !== "") > -1
        ? setHasFamilyId(true)
        : setHasFamilyId(false);
      setListValues(res.data?.data);
      if (Object.keys(res.data?.data).length > 0) {
        setCurrent(2);
      } else {
        setLoadingResult(true);
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post("upload-data", fmData, config);
        if (res.data?.success) {
          message.success(`${t("import.jobLaunched")}`);
          setCurrent(3);
          window.scrollTo(0, 0);
          setLoadingResult(false);
        } else {
          message.error(`${t("import.jobFailed")}`);
        }
      }
      setMappingListLoading(false);
    } catch (err) {
      message.error({
        content: `${t("import.jobFailed")} : ${err.response?.data?.message}`,
        duration: 0,
        onClick: () => message.destroy(),
      });
      console.log("Error: ", err.response?.data?.message);
      setMappingListLoading(false);
    }
  };

  //Middleware to format the payload of the submit mapping list
  function getSubmitMappingFormData(force) {
    const fmData = new FormData();
    const config = {
      headers: { "content-type": "application/json" },
    };

    fmData.append("dial_code", defaultDialCode ? defaultDialCode : "");
    fmData.append("currency", defaultCurrency ? defaultCurrency : "");
    fmData.append(
      "dateFormat",
      dateTimeConfig?.date ?? user?.location?.date_format
    );
    fmData.append(
      "timeFormat",
      dateTimeConfig?.time ?? user?.location?.time_format
    );

    let configs = configurationForm.getFieldsValue();

    if (configs?.shareWithRadioGroup === "others") {
      configs?.shareWith?.forEach((department) => {
        fmData.append("shared[]", department);
      });
    } else if (configs?.shareWithRadioGroup === "onlyMe") {
      fmData.append("shared[]", "");
    }

    let mappedFields = {};
    if (Object.values(mappedFieldsValues).length > 0 && isRestoring) {
      mappedFields = mappedFieldsValues;
    } else {
      setMappedFieldsValues(mappedFieldsForm.getFieldsValue());
      mappedFields = mappedFieldsForm.getFieldsValue();
    }
    return { fmData, config };
  }

  // submit list mapping (step 2) with custom values as suggested from the backend web service
  const submitListMapping = async () => {
    const fmData = new FormData();
    const config = {
      headers: { "content-type": "multipart/form-data" },
    };

    let mappedLists = {};
    if (Object.values(mappedListsValues).length > 0) {
      mappedLists = mappedListsValues;
    } else {
      setMappedListsValues(mappedListsForm.getFieldsValue());
      mappedLists = mappedListsForm.getFieldsValue();
    }

    let currentIndex = 0;
    Object.keys(listValues).map((listValueKey, listValueIndex) => {
      listValues[listValueKey].values_csv.map((valueCsv, indexvalueCsv) => {
        let mappedListItem = Object.values(mappedLists)[currentIndex];
        fmData.append(
          `replace_value[${listValueKey}][${valueCsv}]`,
          mappedListItem === undefined ||
            mappedListItem === "" ||
            mappedListItem === null
            ? ""
            : typeof mappedListItem === "object"
            ? mappedListItem?.value
            : mappedListItem
        );
        currentIndex += 1;
      });
    });

    fmData.append("history_id", historyId);

    try {
      setConfirmLastMapping(true);
      const resConfirmMapping = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).post("confirm-mapping-list", fmData, config);
      if (resConfirmMapping.data?.success) {
        setFilenameList(resConfirmMapping.data?.message?.filename);
        setDelimiter(resConfirmMapping.data?.message?.delimiter);
        await uploadDataInsideMappingList(
          configuration,
          resConfirmMapping.data?.message?.filename,
          resConfirmMapping.data?.message?.delimiter
        );
      }
      setHasFamilyId(false);
      setConfirmLastMapping(false);
    } catch (err) {
      message.error(
        `${t("import.jobFailed")} : ${err.response?.data?.message}`
      );
      console.log("Error: ", err.response?.data?.message);
      setConfirmLastMapping(false);
    }
  };

  // helper fuction inside submitMappingList (final submit)
  async function uploadDataInsideMappingList(force, filenameOpt, delimiterOpt) {
    const { fmData, config } = getSubmitMappingFormData(force);
    fmData.delete("filename");
    fmData.append("filename", filenameOpt);
    fmData.delete("delimiter");
    fmData.append("history_id", historyId);
    setLoadingResult(true);
    const resUploadData = await generateAxios(
      URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
    ).post("upload-data", fmData, config);
    if (resUploadData.data?.success) {
      message.success(`${t("import.jobLaunched")}`);
      setCurrent(3);
      // getDataHistory();
      mappedFieldsForm.resetFields();
      window.scrollTo(0, 0);
      setLoadingResult(false);
    }
  }

  //Handle the case of the phone fields, where we add 0.1 and 0.2 to differentiate between
  //the phone and the dialcode to be sent in the api.
  const formatMappingValues = (array) => {
    const arrayOfFloat = array
      .map((el) => (el !== null ? Number(el) : el))
      .filter((e) => e && !Number.isInteger(e));

    const result = {};

    array
      .map((el) => (el !== null ? Number(el) : el))
      .forEach((value, index) => {
        if (value !== undefined && value !== "") {
          if (!Number.isInteger(value)) {
            arrayOfFloat.forEach((value1, index) => {
              const integerPart = parseInt(value1);

              if (!result[integerPart]) {
                result[integerPart] = {
                  option: String(value1).includes(".1")
                    ? array.indexOf(value1)
                    : "",
                  value: String(value1).includes(".2")
                    ? array.indexOf(value1)
                    : "",
                };
              } else {
                if (String(value1).includes(".1")) {
                  result[integerPart].option = array.indexOf(value1);
                } else if (String(value).includes(".2")) {
                  result[integerPart].value = array.indexOf(value1);
                }
              }
            });
          } else {
            if (result[value] === undefined) result[value] = index;
            else
              result[value] = result[value].length
                ? [...result[value], index]
                : [result[value], index];
          }
        }
      });
    return result;
  };

  // This saves the config of the upload for restoring in case the import is interrupted.
  const saveUploadConfig = async () => {
    try {
      setIsLoading(true);
      let formData = new FormData();
      let selectedPrimaryKey = localStorage.getItem("pk");

      if (
        (configuration === "force" || configuration === "extraFields") &&
        selectedPrimaryKey !== undefined
      ) {
        formData.append("primary_key", selectedPrimaryKey);
      }

      configuration === "force" && formData.append("force", 1);
      configuration === "extraFields" && formData.append("fusion_extra", 1);

      formData.append("header", nameOfColumnDoesExist ? 1 : 0);

      departments?.length > 0 &&
        departments.forEach((department) => {
          formData.append("shared[]", department);
        });

      setChoosedPkValue(selectedPrimaryKey);

      const response = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).post(`/save-config-upload/${uploadedFileId}`, formData, {
        headers: { "content-type": "multipart/form-data" },
      });

      setColumnMappingDefaultValue(response?.data?.message?.auto);
      setIsLoading(false);
      return response;
    } catch (error) {
      console.log(`Error ${error}`);
      setIsLoading(false);
    }
  };

  // save mapping (step 2) when selecting fields
  const saveMapping = async (
    invitationState = false,
    array = [],
    paramValue = {}
  ) => {
    const fmData = new FormData();

    fmData.append("id", historyId);
    fmData.append("step", current);

    let fieldsMappingValues = {};

    if (current === 1) {
      let dataArray = mappedFieldsForm.getFieldsValue();
      let array = Object.values(dataArray);
      dialCodeField &&
        dialCodeField?.length > 0 &&
        fmData.append(
          "dial_code",
          paramValue?.source === "dial" && paramValue?.value !== null
            ? paramValue?.value
            : defaultDialCode
        );
      fmData.append(
        "currency",
        paramValue?.source === "currency" && paramValue?.value !== null
          ? paramValue?.value
          : defaultCurrency
      );
      fmData.append(
        "dateFormat",
        paramValue?.date ?? dateTimeConfig?.date ?? user?.location?.date_format
      );
      fmData.append(
        "timeFormat",
        paramValue?.time ?? dateTimeConfig?.time ?? user?.location?.time_format
      );
      // fmData.append("timeFormat", paramValue?.source === "currency" && paramValue?.value === null ? defaultCurrency : paramValue?.value);
      fieldsMappingValues = formatMappingValues(array);
      setMappedFieldsValues(dataArray);
      setSaveMappingInStepOne(true);
    } else if (current === 2) {
      let firstTimeArray = firstTimeSaveMappingArr;
      let arr = Object.values(mappedListsForm.getFieldsValue());
      let formValues = await mappedListsForm.validateFields();
      const obj =
        Object.values(formValues) &&
        Object.values(formValues).map(
          (value, key) => value !== undefined && { value: value, index: key }
        );

      let mappingListObject = mappedListsForm.getFieldsValue();
      let arrayOfValues = Object.entries(mappingListObject).map(
        ([key, value]) => ({
          [key]: value,
        })
      );

      arrayOfValues.map((item, i) => {
        let splittedStr = Object.keys(item)[0].split("id-");
        let key = splittedStr[splittedStr.length - 1];

        fmData.append(
          `mapping_list[${key}][]`,
          Object.values(item)[0] !== undefined &&
            Object.values(item)[0] !== null
            ? typeof Object.values(item)[0] === "object"
              ? Object.values(item)[0]?.value
              : Object.values(item)[0]
            : ""
        );
      });
    }

    var object = {};
    let jsonData = {};
    if (current === 1) {
      fmData.forEach((value, key) => (object[key] = value));
      jsonData = JSON.stringify({ ...object, fields: fieldsMappingValues });
    }

    try {
      await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).post("save-mapping", current === 1 ? jsonData : fmData, {
        headers:
          current === 1
            ? { "content-type": "application/json" }
            : { "content-type": "multipart/form-data" },
      });
      setSaveMappingInStepOne(false);
    } catch (err) {
      console.log("Error: ", err);
    }
  };

  //Check if one the types (currency,phone,date,time) exists in the fields api to show
  //the select force/default value. ln2507
  useEffect(() => {
    let dataArray = Object.values(mappedFieldsForm.getFieldsValue());
    let specialFields = [];
    let isPhoneId = false;
    let isCurrencyId = false;
    let isDate = false;
    let isTime = false;

    if (current === 1 && saveMappingInStepOne) {
      if (dataArray) {
        dbSelectOptions &&
          dbSelectOptions.forEach((group) => {
            group?.options.forEach((field) => {
              if (!Number.isInteger(field?.value)) {
                specialFields.push(field);
              }
              if (field?.type === "date" || field?.type === "date_time") {
                isDate = true;
              }
              if (field?.type === "time" || field?.type === "date_time") {
                isTime = true;
              }
            });
          });

        let matchedValues = dataArray.map((value) => {
          return specialFields.find(
            (specialField) => value === specialField?.value
          );
        });

        matchedValues
          .filter((el) => el !== undefined)
          .forEach((el) => {
            if (el?.type === "phone") {
              isPhoneId = true;
            } else if (el?.type === "monetary") {
              isCurrencyId = true;
            }
          });
        setIsPhone(isPhoneId);
        setIsMonetary(isCurrencyId);
        setIsDate(isDate);
        setIsTime(isTime);
      }
    }
  }, [mappedFieldsForm, dbSelectOptions, current, saveMappingInStepOne]);

  //Detect dates fields (date,time,date_time,range)
  const getFieldsByIdAndType = (data, fieldTypes) => {
    const result = [];

    for (const groupKey in data) {
      const group = data[groupKey];

      for (const field of group?.fields) {
        if (fieldTypes.includes(field?.field_type.trim())) {
          result.push({ id: field?.id, dateFormat: field?.format });
        }
      }
    }
    setDateTimeFields(result);
    return result;
  };

  // Get fields according to selected family (1st step).
  const getFields = useCallback(async () => {
    let dbOptionsFields = [];
    let requiredFamilyFields = [];
    setDbSelectOptions([]);
    try {
      let url = "";
      let config = {};
      let res = null;
      let finalArray = [];
      //Retrieving fields depends on the configuration (standard/fusion/force).
      if (configuration === "extraFields") {
        url = `get-fields-for-fusion-extra/${family}`;
        config = {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
        };
      } else {
        url = `get-fields/${family}/1`;
        config = {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
          params: {
            all: "1",
          },
        };
      }
      setLoadFields(true);
      res = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).get(url, config);

      if (configuration === "extraFields") {
        finalArray = res.data?.data;
      } else {
        finalArray = res.data?.family.groups;
      }
      finalArray?.map((e, index) => {
        let option = {
          label: e.label,
          key: `g-${index}`,
        };
        let subOptions = [];
        //When a type phone is detected, we force another field for dialcode (0.1: dialcode,0.2: phonenumber)
        //When a type monetary is detected, we force another field for currency (0.1: currency,0.2: value)
        e.fields.forEach((e) => {
          e?.field_type === "monetary"
            ? subOptions.push(
                {
                  label: (
                    <div className="flex items-center">
                      {displayRightIcon(e.field_type, 4, 4)} &nbsp;{" "}
                      {e.required
                        ? `${e.alias} (${t("import.monetaryCurrency")}) *`
                        : `${e.alias} (${t("import.monetaryCurrency")})`}
                    </div>
                  ),
                  value: e.id + 0.1,
                  type: e.field_type,
                  labelText: `${e.required ? `${e.alias} *` : e.alias}`,
                  key: e?.id + 0.1,
                },
                {
                  label: (
                    <div className="flex items-center">
                      {displayRightIcon(e.field_type, 4, 4)} &nbsp;{" "}
                      {e.required
                        ? `${e.alias} (${t("import.monetaryValue")}) *`
                        : `${e.alias} (${t("import.monetaryValue")})`}
                    </div>
                  ),
                  value: e.id + 0.2,
                  type: e.field_type,
                  labelText: `${e.required ? `${e.alias} *` : e.alias}`,
                  key: e?.id + 0.2,
                }
              )
            : e?.field_type === "phone"
            ? subOptions.push(
                {
                  label: (
                    <div className="flex items-center">
                      {displayRightIcon(e.field_type, 4, 4)} &nbsp;{" "}
                      {e.required
                        ? `${e.alias} (${t("import.dualCode")}) *`
                        : `${e.alias} (${t("import.dualCode")})`}
                    </div>
                  ),
                  value: e.id + 0.1,
                  type: e.field_type,
                  labelText: `${e.required ? `${e.alias} *` : e.alias}`,
                  key: e?.id + 0.1,
                },
                {
                  label: (
                    <div className="flex items-center">
                      {displayRightIcon(e.field_type, 4, 4)} &nbsp;{" "}
                      {e.required
                        ? `${e.alias} (${t("import.phoneCode")}) *`
                        : `${e.alias} (${t("import.phoneCode")})`}
                    </div>
                  ),
                  value: e.id + 0.2,
                  type: e.field_type,
                  labelText: `${e.required ? `${e.alias} *` : e.alias}`,
                  key: e?.id + 0.2,
                }
              )
            : subOptions.push({
                label: (
                  <div className="flex items-center">
                    {displayRightIcon(e.field_type, 4, 4)} &nbsp;{" "}
                    {e.required ? `${e.alias} *` : e.alias}
                  </div>
                ),
                value: e.id,
                type: e.field_type,
                labelText: `${e.required ? `${e.alias} *` : e.alias}`,
                key: e?.id,
              });
          //Detect the required fields to be displayed after uploading the file.
          if (e?.required) {
            requiredFamilyFields.push(e.alias);
          } else if (e?.uniqueValue) {
            setUniqueFields((prev) => uniq([...prev, e.alias]));
          }
        });
        option.options = subOptions;
        dbOptionsFields.push(option);
      });
      //If configuration isn't the standard, then a primary key should be added.
      if (configuration === "force" || configuration === "extraFields") {
        let option = {
          label: "Primary Key",
          key: Number(dbOptionsFields[dbOptionsFields.length - 1]["key"]) + 1,
        };
        let subOptions = [
          {
            label: "CMK REF",
            value: choosedPkValue,
            labelText: "CMK REF",
          },
        ];
        option.options = subOptions;
        dbOptionsFields = [option, ...dbOptionsFields];
      }
      if (res?.data?.family?.dial_code) {
        setDialCodeField(res?.data?.family?.dial_code);
      }
      setCurrencies(res?.data?.family?.currencies);
      setDbSelectOptions(dbOptionsFields);
      setImportFieldsParams(res?.data?.family?.param);
      setFields(res?.data?.family?.groups);
      if (configuration !== "extraFields") {
        let requiredElements = res?.data?.family?.groups.map((group) =>
          group?.fields.map((el) => (el?.required ? el?.alias : null))
        );
        setRequiredFields(
          requiredElements
            .map((innerArray) => innerArray.filter((value) => value !== null))
            .flat()
            .toString()
        );
      }
      getFieldsByIdAndType(res?.data?.family?.groups, [
        "date",
        "date_time",
        "range",
        "time",
      ]);
      setLoadFields(false);
    } catch (e) {
      setLoadFields(false);
      console.log(e);
    }
  }, [configuration, family]);

  // Trigger the  get fields api whenever the family ID changes
  useEffect(() => {
    if (family) {
      getFields();
    }
  }, [family, getFields]);

  // show primary keys fields in step 1
  const setShowPk = (e) => {
    if (e?.target?.value === "extraFields" || e?.target?.value === "force") {
      getPrimaryKeys(family);
    }
    setConfiguration(e?.target?.value);
    setShowPrimaryKeyForce(e?.target?.value === "force");
    setShowPrimaryKeyExtraFields(e?.target?.value === "extraFields");
  };

  // set state of NameOfColumnDoesExist with the form value
  const toggleNameOfColumnExist = (e) => {
    setNameOfColumnDoesExist(e.target.checked);
  };

  // custom component for primary key
  const PrimaryKeyFormItem = () => {
    return (
      <Form.Item
        name="primaryKey"
        rules={[
          {
            required: true,
            message: t("import.choosePk"),
          },
        ]}
        label={t("import.choosePk")}
      >
        <Select
          style={{
            width: 200,
          }}
          options={pkSelectOptions}
          placeholder={t("import.selectPk")}
          showSearch
          allowClear
          optionFilterProp={["label"]}
          filterOption={(input, option) =>
            (option?.label.toLowerCase() ?? "").includes(input.toLowerCase())
          }
          onChange={(val) => {
            localStorage.setItem("pk", val);
          }}
        />
      </Form.Item>
    );
  };

  //Save mapping at first load (before the user changes anything).
  useEffect(() => {
    if (current === 1 && !isLoading) {
      saveMapping();
    } else if (current === 2 && !isLoading) {
      saveMapping(false, changedListValues);
    }
  }, [current, isLoading]);

  // reset import states
  const resetImport = () => {
    setCurrent(0);
    setFileUploadSuccess(false);
    setFileUploadedSuccessfullyInStepOne(false);
    mappedFieldsForm.resetFields();
    mappedListsForm.resetFields();
    configurationForm.resetFields();
    nameOfColumnDoesExistForm.setFieldValue("nameOfColumnExistCheckBox", true);
    setListValues([]);
    setRequiredFields("");
    setMappedListsValues({});
    setMappedFieldsValues({});
    setDepartments([]);
    setHasFamilyId(false);
    if (location?.pathname !== "/settings/users") {
      setFamily();
    }
    setNamelessCsvColsIndexes([]);
    setConfiguration("standard");
    setShowPrimaryKeyForce(false);
    setShowPrimaryKeyExtraFields(false);
    setDefaultCurrency(null);
    setDateTimeConfig({ date: null, time: null });
    setDateTimeFields([]);
  };

  //Load departments api.
  const loadDepartments = async () => {
    try {
      let url = `departments`;
      let config = {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      };
      const res = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).get(url, config);
      const dbOptionsFields = [];
      res.data?.data?.map((e) => {
        let option = {
          label: e?.label,
          value: e?.id,
        };
        dbOptionsFields.push(option);
      });
      setDepartmentOptions(dbOptionsFields);
    } catch (e) {
      setDepartmentOptions([]);
      console.log(e);
    }
  };

  //Handle changes in the select departments (before uploading the file).
  const handleChangeDepartments = (e) => {
    setDepartments(e);
  };

  // change family id and fields and primary keys on familyId change
  useEffect(() => {
    handleChangeFamilies(familyId);
    dispatch(setSelectedFamily(Number(familyId)));
    loadDepartments();
  }, [familyId]);

  // on change file Preview Header data set select options in step 2 (mapping column)
  useEffect(() => {
    const fileFieldsArray = [];
    filePreviewHeader?.map((e) => {
      fileFieldsArray.push({ value: e.key, label: e.title });
    });
    setFileSelectOptions(fileFieldsArray);
  }, [filePreviewHeader]);

  // refetch histories data on dependencies change
  useEffect(() => {
    if (family || (inviteUsersJobDone && Number(family) === 4)) {
      getDataHistory();
    }
  }, [search, family, familyId, inviteUsersJobDone, page]);

  // set the loader on step 3 and pass automatically to step 4 in finished
  useEffect(() => {
    (async () => {
      if (current === 4) {
        const url = `history/${historyId}`;
        try {
          const res = await generateAxios(
            URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
          ).get(url);
          setUploadSuccessHint(
            `${t("import.uploadSuccessHint", {
              historyId: historyId,
              nbr_doublons: res.data.message.nbr_doublons,
              nbr_ok: res.data.message.nbr_ok,
              nbr_error: res.data.message.nbr_error,
              family: families?.find((e) => e.id == family)?.label,
            })}`
          );
        } catch (error) {
          console.log(error);
        }
      } else if (current === 3) {
        if (importProgress === 0) {
          clearInterval(interval);
          setCurrent(4);
          setImportProgress(0);
        } else {
          interval = setInterval(() => {
            setImportProgress((prev) => prev + 0);
          }, 500);
        }
      }
    })();
    return () => clearInterval(interval);
  }, [current, importProgress]);

  // update select values from step 3 (mapping list) when adding custom types
  useEffect(() => {
    (async () => {
      if (openChildrenImportDrawer === false && current === 2) {
        const { fmData, config } = getSubmitMappingFormData(configuration);

        fmData.append("history_id", historyId);

        try {
          const res = await generateAxios(
            URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
          ).post("mapping-list", fmData, config);
          Object.values(res.data?.data).findIndex((el) => el?.family !== "") >
          -1
            ? setHasFamilyId(true)
            : setHasFamilyId(false);
          setListValues(res.data?.data);
        } catch (e) {
          console.log(e);
        }
      }
    })();
  }, [openChildrenImportDrawer]);

  //Scroll page to top on the second mapping (list mapping).
  useEffect(() => {
    const container = document.getElementById("import-container");
    if (container)
      container.scrollIntoView({
        behavior: "smooth",
        block: "start",
        inline: "nearest",
      });
  }, [current, isLoading]);

  //Add animation (party-js) at the end of the import.
  useEffect(() => {
    const particleContainer = document.getElementById("import-container");
    if (inviteUsersJobDone && particleContainer) {
      // Trigger the particle animation
      party.confetti(particleContainer, {
        count: party.variation.range(60, 80),
      });
    }
  }, [dispatch, inviteUsersJobDone]);

  //Set the form value when 'all' is selected, in order to disable the rest of options.
  useEffect(() => {
    if (departments && departments.length > 0) {
      if (departments.includes("all")) {
        configurationForm.setFieldsValue({
          shareWithTags: "all",
        });
      }
    }
  }, [configurationForm, departments]);

  // Handle download sample file.
  const handleDownloadSample = () => {
    if (Number(family)) {
      downloadFile(
        `${
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        }download-sample/${family}`,
        `${
          URL_ENV?.REACT_APP_DOWNLOAD_FILE_PREFIX
        }_Sample_${getSelectedFamilyText(Number(selectedFamily), t)}.csv`
      );
    }
  };

  // Handle search in select (select module and select shared departments).
  const handleSearchInSelect = (inputValue, option) => {
    return option?.label.toLowerCase().includes(inputValue.toLowerCase());
  };

  // Search inside dialcode list select.
  const handleSearchInDialcodeSelect = (input, option) =>
    option?.nameEn.toLowerCase().includes(input.toLowerCase()) ||
    option?.nameFr.toLowerCase().includes(input.toLowerCase()) ||
    option?.value.toLowerCase().includes(input.toLowerCase());

  // Handle change of the list of dialcode.
  const handleDialcodeFieldChange = (value) => {
    saveMapping(false, [], { value, source: "dial" });
    setDefaultDialCode(value);
  };
  // setDateTimeConfig

  // Handle change of the list of currency.
  const handleCurrencyFieldChange = (value) => {
    setDefaultCurrency(value);
    saveMapping(false, [], { value, source: "currency" });
  };

  // Handle change of the list of date formats.
  const handleDateFieldChange = (source, value) => {
    saveMapping(false, [], { ...dateTimeConfig, date: value, source });
    setDateTimeConfig({
      ...dateTimeConfig,
      date: value,
    });
  };

  // Handle change of the list of time formats.
  const handleTimeFieldChange = (source, value) => {
    saveMapping(false, [], { ...dateTimeConfig, time: value, source });
    setDateTimeConfig({
      ...dateTimeConfig,
      time: value,
    });
  };

  // JSX of import component
  return (
    <div id="import-container">
      {/* Upload file, set configs and file summary after upload (step_1) */}
      <div className="flex items-center justify-center p-6">
        <Steps
          current={current}
          size="small"
          labelPlacement="vertical"
          items={itemsOfSteps}
        />
      </div>
      {current === 0 && !fromDrawer && !fileUploadedSuccessfullyInStepOne && (
        <>
          <div className="pl-6 pr-6">
            <Form form={configurationForm} layout="vertical">
              <Form.Item
                name="familySelect"
                label={t("import.selectFamily")}
                rules={[
                  {
                    required: true,
                    message: t("import.selectFamilyError"),
                  },
                ]}
              >
                <Select
                  showSearch
                  optionFilterProp={["label"]}
                  filterOption={handleSearchInSelect}
                  value={family}
                  className=" w-full"
                  onChange={onChangeFamilyTabsItem}
                  options={
                    families &&
                    families
                      .map((el) => ({
                        value: el.id,
                        label: el.label,
                      }))
                      .sort((a, b) => a?.label.localeCompare(b?.label))
                  }
                  placeholder={t("import.selectFamilyPlaceholder")}
                />
              </Form.Item>
            </Form>
          </div>
        </>
      )}

      <div className="p-6">
        {current === 0 && (
          <>
            {isLoading && (
              <div className="flex items-center justify-center p-6">
                <Spin size="large" />
              </div>
            )}
            {isLoading === false && (
              <>
                <>
                  {!fileUploadedSuccessfullyInStepOne && (
                    <>
                      <div className="flex justify-between">
                        <Spin spinning={loadFields}>
                          <Form
                            form={configurationForm}
                            name="configuration"
                            initialValues={{
                              forceOrExtraFieldsOption: configuration,
                            }}
                          >
                            <Form.Item
                              name="forceOrExtraFieldsOption"
                              label="Import Options"
                            >
                              <Radio.Group
                                onChange={setShowPk}
                                style={{ marginTop: "5px" }}
                                disabled={!family}
                              >
                                <Space direction="vertical">
                                  <Radio value="standard">
                                    {t("import.standardOption")}
                                  </Radio>
                                  <Radio value="force">
                                    {t("import.forceOption")}
                                  </Radio>
                                  {showPrimaryKeyForce && (
                                    <PrimaryKeyFormItem key="forcePrimaryKey" />
                                  )}
                                  <Radio value="extraFields">
                                    {t("import.extraFieldOption")}
                                  </Radio>
                                  {showPrimaryKeyExtraFields && (
                                    <PrimaryKeyFormItem key="fusionPrimaryKey" />
                                  )}
                                </Space>
                              </Radio.Group>
                            </Form.Item>
                          </Form>
                        </Spin>
                        <Tooltip
                          title={t("import.downloadSampleDescription")}
                          color="red"
                          className="relative"
                        >
                          <Link
                            disabled={!family}
                            onClick={handleDownloadSample}
                          >
                            {t("import.downloadSample")}
                          </Link>
                        </Tooltip>
                      </div>

                      <Form
                        form={nameOfColumnDoesExistForm}
                        name="nameOfColumnDoesExist"
                        style={{
                          maxWidth: 600,
                          marginBottom: "15px",
                          marginTop: "15px",
                        }}
                        initialValues={{
                          nameOfColumnExistCheckBox: nameOfColumnDoesExist,
                        }}
                      >
                        <Form.Item
                          name="nameOfColumnExistCheckBox"
                          valuePropName="checked"
                        >
                          <Checkbox
                            onChange={toggleNameOfColumnExist}
                            disabled={!family}
                          >
                            {t("import.nameOfColumnExist")}
                          </Checkbox>
                        </Form.Item>
                      </Form>
                    </>
                  )}
                  {configuration === "standard" &&
                    !fileUploadedSuccessfullyInStepOne &&
                    family !== 4 && (
                      <Form form={configurationForm}>
                        <Form.Item
                          name="shareWithRadioGroup"
                          label={t("import.shareWith")}
                          tooltip={t("import.shareWithInfo")}
                          rules={[
                            {
                              required: true,
                              message: t("import.shareBtnError"),
                            },
                          ]}
                        >
                          <Radio.Group
                            onChange={(item) => {
                              if (item?.target?.value === "others") {
                                setShareWithOthers(true);
                              } else {
                                setShareWithOthers(false);
                              }
                            }}
                            disabled={!family}
                          >
                            <Radio value="onlyMe">{t("import.onlyMe")}</Radio>
                            <Radio value="others">{t("import.others")}</Radio>
                          </Radio.Group>
                        </Form.Item>
                        {shareWithOthers && (
                          <Form.Item
                            name="shareWithTags"
                            rules={[
                              {
                                required: true,
                                message: t("import.selectDeptsError"),
                              },
                            ]}
                            style={{ marginBottom: "30px" }}
                          >
                            <Select
                              mode="multiple"
                              size="middle"
                              placeholder={t("import.sharedWith")}
                              onChange={handleChangeDepartments}
                              style={{ width: "100%", marginTop: 10 }}
                              options={[
                                { label: t("import.selectAll"), value: "all" },
                                ...departmentsOptions,
                              ].map((el) => ({
                                label: el?.label,
                                value: el?.value,
                                disabled:
                                  el?.value !== "all" &&
                                  departments.includes("all"),
                              }))}
                              allowClear
                              showSearch
                              optionFilterProp={["label"]}
                              filterOption={handleSearchInSelect}
                            />
                          </Form.Item>
                        )}
                      </Form>
                    )}
                  {fileUploadSuccess === false && (
                    <Dragger
                      {...props}
                      disabled={fileUploadSuccess || !family}
                      accept={[
                        "application/excel",
                        "application/vnd.ms-excel",
                        "application/x-excel",
                        "application/x-msexcel",
                        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                        "text/csv",
                      ]}
                    >
                      <div className="ant-upload-drag-icon">
                        <p className="text-4xl">
                          <BsFiletypeXlsx style={{ color: "green" }} />
                          <BsFiletypeCsv style={{ color: "blue" }} />
                          <BsFiletypeXls style={{ color: "green" }} />
                        </p>
                      </div>
                      <p className="ant-upload-text">{t("import.upload")}</p>
                      <p className="ant-upload-hint">
                        {t("import.uploadHint")}
                      </p>
                      <p
                        className="ant-upload-hint"
                        style={{
                          color:
                            !fileUploadSuccess &&
                            !Number.isNaN(family) &&
                            "red",
                        }}
                      >
                        <WarningOutlined /> {t("import.uploadWarning")}
                      </p>
                    </Dragger>
                  )}
                  {/* Uploaded File Summary */}
                  {fileUploadSuccess === true && !isLoading && (
                    <>
                      <h2 className="pb-6">{t("import.fileInfos")}</h2>
                      <div className="grid grid-cols-5">
                        <div className="col-span-1 flex items-center justify-center text-8xl text-green-500">
                          <FileDoneOutlined />
                        </div>
                        <Form
                          name="delimiter"
                          layout="inline"
                          className="col-span-4 pb-6 pl-6"
                        >
                          <Form.Item
                            name="uploadRows"
                            label={t("import.uploadRows")}
                          >
                            <span className="ant-form-text mb-1 underline">
                              {fileUploadNbrRows}
                            </span>
                          </Form.Item>
                          <Form.Item
                            name="uploadColumns"
                            label={t("import.uploadColumns")}
                          >
                            <span className="ant-form-text mb-1 underline">
                              {fileUploadNbrColumns}
                            </span>
                          </Form.Item>
                          <Form.Item
                            name="uploadSize"
                            label={t("import.uploadSize")}
                          >
                            <span className="ant-form-text mb-1 underline">
                              {fileUploadSize}
                            </span>
                          </Form.Item>
                          <Form.Item
                            name="uploadFileName"
                            label={t("import.uploadFileName")}
                          >
                            <Tooltip title={originalFilename}>
                              <span className="ant-form-text mb-1 truncate underline">
                                {originalFilename}
                              </span>
                            </Tooltip>
                          </Form.Item>
                          <Form.Item
                            name="uploadFileName"
                            label={t("import.uploadFileFamily")}
                          >
                            <span className="ant-form-text mb-1 underline">
                              {families?.find((e) => e.id == family)?.label}
                            </span>
                          </Form.Item>
                          <Form.Item
                            name="uploadFileName"
                            label={t("import.requiredFields")}
                          >
                            <span className="ant-form-text mb-1 underline">
                              {requiredFields}
                            </span>
                          </Form.Item>
                          <Form.Item
                            name="uploadFileName"
                            label={t("import.uniqueFields")}
                          >
                            <span className="ant-form-text mb-1 underline">
                              {uniqueFields.join(", ")}
                            </span>
                          </Form.Item>
                          <br />
                        </Form>
                      </div>
                      <div className="flex flex-row justify-center">
                        <Button onClick={resetImport}>
                          {t("import.cancelBtn")}
                        </Button>
                        <Button
                          type="primary"
                          style={{
                            display: "flex",
                            flexDirection: "row-reverse",
                            alignItems: "center",
                            marginLeft: "20px",
                          }}
                          icon={
                            <ArrowRightOutlined
                              style={{
                                paddingTop: "5px",
                                paddingLeft: "5px",
                              }}
                            />
                          }
                          onClick={showStepTwo}
                          loading={isLoading}
                        >
                          {t("import.nextStepBtn")}
                        </Button>
                      </div>
                    </>
                  )}
                </>
              </>
            )}
          </>
        )}
        <div className={mappingListLoading && "spinner"}>
          <Spin
            spinning={mappingListLoading}
            fullscreen
            indicator={
              <div className="automation-loader-container">
                <svg
                  class="machine"
                  xmlns="http://www.w3.org/2000/svg"
                  x="0px"
                  y="0px"
                  viewBox="0 0 645 526"
                >
                  <defs />
                  <g>
                    <path
                      x="-173,694"
                      y="-173,694"
                      class="import_large-shadow"
                      d="M645 194v-21l-29-4c-1-10-3-19-6-28l25-14 -8-19 -28 7c-5-8-10-16-16-24L602 68l-15-15 -23 17c-7-6-15-11-24-16l7-28 -19-8 -14 25c-9-3-18-5-28-6L482 10h-21l-4 29c-10 1-19 3-28 6l-14-25 -19 8 7 28c-8 5-16 10-24 16l-23-17L341 68l17 23c-6 7-11 15-16 24l-28-7 -8 19 25 14c-3 9-5 18-6 28l-29 4v21l29 4c1 10 3 19 6 28l-25 14 8 19 28-7c5 8 10 16 16 24l-17 23 15 15 23-17c7 6 15 11 24 16l-7 28 19 8 14-25c9 3 18 5 28 6l4 29h21l4-29c10-1 19-3 28-6l14 25 19-8 -7-28c8-5 16-10 24-16l23 17 15-15 -17-23c6-7 11-15 16-24l28 7 8-19 -25-14c3-9 5-18 6-28L645 194zM471 294c-61 0-110-49-110-110S411 74 471 74s110 49 110 110S532 294 471 294z"
                    />
                  </g>
                  <g>
                    <path
                      x="-136,996"
                      y="-136,996"
                      class="import_medium-shadow"
                      d="M402 400v-21l-28-4c-1-10-4-19-7-28l23-17 -11-18L352 323c-6-8-13-14-20-20l11-26 -18-11 -17 23c-9-4-18-6-28-7l-4-28h-21l-4 28c-10 1-19 4-28 7l-17-23 -18 11 11 26c-8 6-14 13-20 20l-26-11 -11 18 23 17c-4 9-6 18-7 28l-28 4v21l28 4c1 10 4 19 7 28l-23 17 11 18 26-11c6 8 13 14 20 20l-11 26 18 11 17-23c9 4 18 6 28 7l4 28h21l4-28c10-1 19-4 28-7l17 23 18-11 -11-26c8-6 14-13 20-20l26 11 11-18 -23-17c4-9 6-18 7-28L402 400zM265 463c-41 0-74-33-74-74 0-41 33-74 74-74 41 0 74 33 74 74C338 430 305 463 265 463z"
                    />
                  </g>
                  <g>
                    <path
                      x="-100,136"
                      y="-100,136"
                      class="import_small-shadow"
                      d="M210 246v-21l-29-4c-2-10-6-18-11-26l18-23 -15-15 -23 18c-8-5-17-9-26-11l-4-29H100l-4 29c-10 2-18 6-26 11l-23-18 -15 15 18 23c-5 8-9 17-11 26L10 225v21l29 4c2 10 6 18 11 26l-18 23 15 15 23-18c8 5 17 9 26 11l4 29h21l4-29c10-2 18-6 26-11l23 18 15-15 -18-23c5-8 9-17 11-26L210 246zM110 272c-20 0-37-17-37-37s17-37 37-37c20 0 37 17 37 37S131 272 110 272z"
                    />
                  </g>
                  <g>
                    <path
                      x="-100,136"
                      y="-100,136"
                      class="import_small"
                      d="M200 236v-21l-29-4c-2-10-6-18-11-26l18-23 -15-15 -23 18c-8-5-17-9-26-11l-4-29H90l-4 29c-10 2-18 6-26 11l-23-18 -15 15 18 23c-5 8-9 17-11 26L0 215v21l29 4c2 10 6 18 11 26l-18 23 15 15 23-18c8 5 17 9 26 11l4 29h21l4-29c10-2 18-6 26-11l23 18 15-15 -18-23c5-8 9-17 11-26L200 236zM100 262c-20 0-37-17-37-37s17-37 37-37c20 0 37 17 37 37S121 262 100 262z"
                    />
                  </g>
                  <g>
                    <path
                      x="-173,694"
                      y="-173,694"
                      class="import_large"
                      d="M635 184v-21l-29-4c-1-10-3-19-6-28l25-14 -8-19 -28 7c-5-8-10-16-16-24L592 58l-15-15 -23 17c-7-6-15-11-24-16l7-28 -19-8 -14 25c-9-3-18-5-28-6L472 0h-21l-4 29c-10 1-19 3-28 6L405 9l-19 8 7 28c-8 5-16 10-24 16l-23-17L331 58l17 23c-6 7-11 15-16 24l-28-7 -8 19 25 14c-3 9-5 18-6 28l-29 4v21l29 4c1 10 3 19 6 28l-25 14 8 19 28-7c5 8 10 16 16 24l-17 23 15 15 23-17c7 6 15 11 24 16l-7 28 19 8 14-25c9 3 18 5 28 6l4 29h21l4-29c10-1 19-3 28-6l14 25 19-8 -7-28c8-5 16-10 24-16l23 17 15-15 -17-23c6-7 11-15 16-24l28 7 8-19 -25-14c3-9 5-18 6-28L635 184zM461 284c-61 0-110-49-110-110S401 64 461 64s110 49 110 110S522 284 461 284z"
                    />
                  </g>
                  <g>
                    <path
                      x="-136,996"
                      y="-136,996"
                      class="import_medium"
                      d="M392 390v-21l-28-4c-1-10-4-19-7-28l23-17 -11-18L342 313c-6-8-13-14-20-20l11-26 -18-11 -17 23c-9-4-18-6-28-7l-4-28h-21l-4 28c-10 1-19 4-28 7l-17-23 -18 11 11 26c-8 6-14 13-20 20l-26-11 -11 18 23 17c-4 9-6 18-7 28l-28 4v21l28 4c1 10 4 19 7 28l-23 17 11 18 26-11c6 8 13 14 20 20l-11 26 18 11 17-23c9 4 18 6 28 7l4 28h21l4-28c10-1 19-4 28-7l17 23 18-11 -11-26c8-6 14-13 20-20l26 11 11-18 -23-17c4-9 6-18 7-28L392 390zM255 453c-41 0-74-33-74-74 0-41 33-74 74-74 41 0 74 33 74 74C328 420 295 453 255 453z"
                    />
                  </g>
                </svg>
              </div>
            }
          />
        </div>
        {/* columns mapping (step_2) */}
        {current === 1 && !loadingResult && (
          <div className="w-full">
            {isLoading && (
              <div className="flex items-center justify-center p-6">
                <Spin size="large" />
              </div>
            )}
            {!isLoading && current === 1 && (
              <>
                <h2>{t("import.spreadsheetColumns")}</h2>
                <Form
                  form={mappedFieldsForm}
                  name="mappingdb"
                  layout="vertical"
                >
                  <Row style={{ padding: "10px 0" }}>
                    <Col span={10}>
                      <h4
                        className="mb-6 mt-2"
                        style={{
                          textDecoration: "underline",
                        }}
                      >
                        {t("import.titleOfFileColumns")}
                      </h4>
                    </Col>
                    <Col span={10}>
                      <h4
                        className="mb-6 mt-2"
                        style={{
                          textDecoration: "underline",
                          marginLeft: fromDrawer ? 97 : 130,
                        }}
                      >
                        {t("import.familyFields")}
                      </h4>
                    </Col>
                  </Row>
                  {filePreviewHeader.map((e, i) => (
                    <GetColsMappingTemplate
                      fileSelectOptions={fileSelectOptions}
                      nameLessCsvColumnRef={nameLessCsvColumnRef}
                      i={i}
                      filePreviewData={filePreviewData}
                      e={e}
                      t={t}
                      historyId={historyId}
                      dbSelectOptions={dbSelectOptions}
                      saveMapping={saveMapping}
                      current={current}
                      loadingResult={loadingResult}
                      columnMappingDefaultValue={columnMappingDefaultValue}
                      restoredMapping={restoredMapping}
                      dateTimeFields={dateTimeFields}
                      dateFormat={
                        dateTimeConfig?.date ?? user?.location?.date_format
                      }
                      timeFormat={
                        dateTimeConfig?.time ?? user?.location?.time_format
                      }
                    />
                  ))}
                </Form>

                <Form form={mappedFieldsForm}>
                  {isPhone && (
                    <>
                      <section
                        className="flex flex-row items-center pb-3"
                        style={{ paddingTop: "10px" }}
                      >
                        <h2>{t("import.dialCode")}</h2>
                        <Tooltip
                          title={t("import.dialCodeInfoTooltip", {
                            value: t("import.dialCode"),
                          })}
                        >
                          <InfoCircleOutlined className="text-[rgba(0, 0, 0, 0.45)] pl-1 transition duration-300 hover:cursor-help hover:text-[#1890ff]" />
                        </Tooltip>
                      </section>
                      <Row justify="center">
                        <Col span={10}>
                          <Form.Item>
                            <div
                              style={{
                                textOverflow: "ellipsis",
                                overflow: "hidden",
                                whiteSpace: "nowrap",
                              }}
                            >
                              {t("import.dialCodeLabel")}
                            </div>
                          </Form.Item>
                        </Col>
                        <Col span={2}>
                          <Form.Item>
                            <RightOutlined />
                          </Form.Item>
                        </Col>
                        <Col span={10}>
                          <Form.Item>
                            <Select
                              defaultValue={defaultDialCode && defaultDialCode}
                              showSearch
                              allowClear
                              options={
                                dialCodeField &&
                                dialCodeField.map((item, i) => ({
                                  label: (
                                    <>
                                      ({item?.dial_code}){" "}
                                      {i18n?.language === "en"
                                        ? item?.name_en
                                        : item?.name_fr}
                                    </>
                                  ),
                                  value: item?.dial_code,
                                  nameEn: item?.name_en,
                                  nameFr: item?.name_fr,
                                  key: i,
                                }))
                              }
                              placeholder={t("import.selectDialPlaceholder")}
                              optionFilterProp={["nameEn", "nameFr", "value"]}
                              filterOption={handleSearchInDialcodeSelect}
                              onChange={handleDialcodeFieldChange}
                            />
                          </Form.Item>
                        </Col>
                      </Row>
                      {(isMonetary || isDate || isTime) && (
                        <Divider style={{ margin: 0 }} />
                      )}
                    </>
                  )}
                  {isMonetary && (
                    <>
                      <section
                        className="flex flex-row items-center pb-3"
                        style={{ paddingTop: "10px" }}
                      >
                        <h2>{t("import.currency")}</h2>
                        <Tooltip
                          title={t("import.currencyInfoTooltip", {
                            value: t("import.currency"),
                          })}
                        >
                          <InfoCircleOutlined className="text-[rgba(0, 0, 0, 0.45)] pl-1 transition duration-300 hover:cursor-help hover:text-[#1890ff]" />
                        </Tooltip>
                      </section>
                      <Row justify="center">
                        <Col span={10}>
                          <Form.Item>
                            <div
                              style={{
                                textOverflow: "ellipsis",
                                overflow: "hidden",
                                whiteSpace: "nowrap",
                              }}
                            >
                              {t("import.monetary")}
                            </div>
                          </Form.Item>
                        </Col>
                        <Col span={2}>
                          <Form.Item>
                            <RightOutlined />
                          </Form.Item>
                        </Col>
                        <Col span={10}>
                          <Form.Item>
                            <Select
                              value={defaultCurrency}
                              showSearch
                              allowClear
                              options={
                                currencies &&
                                currencies.map((item, i) => ({
                                  label: item,
                                  value: item,
                                }))
                              }
                              placeholder={t(
                                "import.selectCurrencyPlaceholder"
                              )}
                              optionFilterProp={["label"]}
                              filterOption={handleSearchInSelect}
                              onChange={handleCurrencyFieldChange}
                            />
                          </Form.Item>
                        </Col>
                      </Row>
                      {(isDate || isTime) && <Divider style={{ margin: 0 }} />}
                    </>
                  )}
                  {isDate && (
                    <>
                      <section
                        className="flex flex-row items-center pb-3"
                        style={{ paddingTop: "10px" }}
                      >
                        <h2>{t("localisation.dateFormat")}</h2>
                      </section>
                      <Row justify="center">
                        <Col span={10}>
                          <Form.Item>
                            <div
                              style={{
                                textOverflow: "ellipsis",
                                overflow: "hidden",
                                whiteSpace: "nowrap",
                              }}
                            >
                              {t("fields_management.dateFormatLabel")}
                            </div>
                          </Form.Item>
                        </Col>
                        <Col span={2}>
                          <Form.Item>
                            <RightOutlined />
                          </Form.Item>
                        </Col>
                        <Col span={10}>
                          <Form.Item>
                            <Select
                              defaultValue={user?.location?.date_format}
                              showSearch
                              allowClear
                              options={optionsDateFormat(t)?.map(
                                (option, i) => ({
                                  label: option?.label,
                                  value: option?.value,
                                  key: `dateFormat_${i}`,
                                })
                              )}
                              placeholder={t(
                                "fields_management.dateFormatPlaceholder"
                              )}
                              filterOption={["label"]}
                              onChange={(value, _) =>
                                handleDateFieldChange("date", value)
                              }
                            />
                          </Form.Item>
                        </Col>
                      </Row>
                      {isTime && <Divider style={{ margin: 0 }} />}
                    </>
                  )}
                  {isTime && (
                    <>
                      <section
                        className="flex flex-row items-center pb-3"
                        style={{ paddingTop: "10px" }}
                      >
                        <h2>{t("localisation.timeFormat")}</h2>
                      </section>
                      <Row justify="center">
                        <Col span={10}>
                          <Form.Item>
                            <div
                              style={{
                                textOverflow: "ellipsis",
                                overflow: "hidden",
                                whiteSpace: "nowrap",
                              }}
                            >
                              {t("fields_management.timeFormatLabel")}
                            </div>
                          </Form.Item>
                        </Col>
                        <Col span={2}>
                          <Form.Item>
                            <RightOutlined />
                          </Form.Item>
                        </Col>
                        <Col span={10}>
                          <Form.Item>
                            <Select
                              defaultValue={user?.location?.time_format}
                              showSearch
                              allowClear
                              options={[
                                {
                                  value: "HH:mm",
                                  label: `24 ${t("tasks.hours")}`,
                                },
                                {
                                  value: "h:mm a",
                                  label: `12 ${t("tasks.hours")}`,
                                },
                              ]}
                              placeholder={t(
                                "fields_management.dateFormatPlaceholder"
                              )}
                              filterOption={["label"]}
                              onChange={(value, _) =>
                                handleTimeFieldChange("time", value)
                              }
                            />
                          </Form.Item>
                        </Col>
                      </Row>
                    </>
                  )}
                </Form>
                <Space
                  style={{
                    display: "flex",
                    marginTop: 50,
                  }}
                >
                  {current === 1 && restoredMapping === null && (
                    <Col span={2 / 10}>
                      <Button
                        onClick={() => {
                          setCurrent(0);
                          setFileUploadedSuccessfullyInStepOne(false);
                        }}
                      >
                        {t("import.previous")}
                      </Button>
                    </Col>
                  )}
                  <Col span={2 / 10}>
                    <Button
                      type="primary"
                      onClick={() => {
                        submitMapping({ force: configuration });
                      }}
                    >
                      {t("import.next")}
                    </Button>
                  </Col>
                  <Col span={2 / 10}>
                    <Popconfirm
                      title={t("import.cancelImportTitle")}
                      description={t("import.cancelImportDesc")}
                      cancelButtonProps={{
                        onClick: resetImport,
                        danger: true,
                      }}
                      cancelText={t("import.cancelImportTitle")}
                      okText={t("import.continueImportBtn")}
                      overlayClassName="w-[400px]"
                    >
                      <Button type="default" danger>
                        {t("import.cancelImport")}
                      </Button>
                    </Popconfirm>
                  </Col>
                </Space>
              </>
            )}
          </div>
        )}
        {/* Lists mapping (step_3) */}
        {current === 2 && !loadingResult && (
          <div className="w-full">
            {isLoading && (
              <div className="flex items-center justify-center p-6">
                <Spin size="large" />
              </div>
            )}
            {!isLoading && (
              <>
                <h2>{t("import.spreadsheetLists")}</h2>
                <h4 className="py-2">
                  {t("import.mappingListNotice")}{" "}
                  <span className="text-yellow-500">
                    <WarningOutlined />
                  </span>
                </h4>
                <Form
                  form={mappedListsForm}
                  name="mappingLists"
                  layout="vertical"
                >
                  {Object.keys(listValues)?.length > 0 &&
                    Object.keys(listValues).map((key, keyIndex) => (
                      <>
                        <Typography.Title
                          level={3}
                          style={{
                            textDecoration: "underline",
                            marginTop: 15,
                            position: "sticky",
                            top: "0",
                            backgroundColor: "white",
                            zIndex: 10,
                          }}
                        >
                          {listValues[key]?.label}
                        </Typography.Title>
                        {listValues[key]?.values_csv.map((csv, csvIndex) => (
                          <GetListMappingTemplate
                            csvIndex={csvIndex}
                            keyItem={key}
                            csv={csv}
                            listValues={listValues}
                            rowIndex={keyIndex}
                            changedListValues={changedListValues}
                            saveMapping={saveMapping}
                            setChangedListValues={setChangedListValues}
                            t={t}
                            restoredListMapping={restoredListMapping}
                          />
                        ))}
                        <Divider></Divider>
                      </>
                    ))}
                </Form>
                {/* MappingList */}
                <Space
                  style={{
                    display: "flex",
                    marginTop: 15,
                  }}
                >
                  <Col span={2 / 10}>
                    <Button
                      onClick={() => {
                        setCurrent(1);
                      }}
                    >
                      {t("import.previous")}
                    </Button>
                  </Col>
                  <Col span={2 / 10}>
                    <Button
                      type="primary"
                      onClick={submitListMapping}
                      loading={confirmLastMapping}
                    >
                      {t("import.startImport")}
                    </Button>
                  </Col>
                  <Col span={2 / 10}>
                    <Popconfirm
                      title={t("import.cancelImportTitle")}
                      description={t("import.cancelImportDesc")}
                      cancelButtonProps={{
                        onClick: () => resetImport(),
                        danger: true,
                      }}
                      cancelText={t("import.cancelImportTitle")}
                      okText={t("import.continueImportBtn")}
                      overlayClassName="w-[400px]"
                    >
                      <Button type="default" danger>
                        {t("import.cancelImport")}
                      </Button>
                    </Popconfirm>
                  </Col>
                </Space>
              </>
            )}
          </div>
        )}
      </div>
      {loadingResult && (
        <div className="flex items-center justify-center p-6">
          <Spin size="large" />
        </div>
      )}
      {!loadingResult && current === 3 && (
        <>
          <Space
            direction="horizontal"
            style={{ width: "100%", justifyContent: "center" }}
          >
            <Progress type="circle" percent={importProgress} />
          </Space>
        </>
      )}
      {/* Final step (step_4) */}
      <>
        {!loadingResult && current === 4 && (
          <>
            <div className=" flex justify-end">
              <Button type="link" onClick={navigateToFamily}>
                {t("chat.goto")}{" "}
                {t(
                  `import.${families
                    .find((el) => el.id === family)
                    ?.label?.toLowerCase()}`
                )}
              </Button>
            </div>
            {inviteUsersJobDone ? (
              <div id="particle-container">
                <div className="success-animation">
                  <svg
                    className="checkmark"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 52 52"
                  >
                    <circle
                      className="checkmark__circle"
                      cx="26"
                      cy="26"
                      r="25"
                      fill="none"
                    />
                    <path
                      className="checkmark__check"
                      fill="none"
                      d="M14.1 27.2l7.1 7.2 16.7-16.8"
                    />
                  </svg>
                </div>
                <div>
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: "auto", opacity: 1 }}
                    transition={{ duration: 0.5 }}
                    className="flex items-center justify-center text-base"
                  >
                    {t("import.importFinished")}
                  </motion.div>
                </div>
                <div
                  style={{
                    display: "flex",
                    justifyContent: "center",
                    marginTop: "10px",
                  }}
                >
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={() => {
                      resetImport();
                      dispatch(setIsImportJobDone(false));
                    }}
                    type="primary"
                  >
                    {t("import.startAnother")}
                  </Button>
                </div>
              </div>
            ) : (
              <div className="flex flex-row items-center justify-center text-base">
                <p>{t("import.importHasStarted")}</p>
                <Spin />
              </div>
            )}
          </>
        )}
      </>
      <>
        {fromDrawer && (
          <Collapse ghost>
            <Panel header={t("import.history")} key="1">
              <Space className="pb-6 pl-6">
                <SearchInTable />
              </Space>
              <Table
                columns={columnsHistory}
                dataSource={historyData}
                loading={loadingTable}
                pagination={{
                  total,
                  defaultPageSize: 5,
                  showTotal: (total, range) =>
                    t("tasks.tablePagination", {
                      range: `${range[0]}-${range[1]}`,
                      totalItems: total,
                    }),
                  onChange: (number) => {
                    setPage(number);
                  },
                }}
                size="small"
                scroll={{
                  x: 1300,
                }}
              />
            </Panel>
          </Collapse>
        )}

        {!fromDrawer && (
          <>
            <Collapse ghost>
              <Panel
                collapsible={!family && "disabled"}
                header={t("import.history")}
                key={family}
              >
                <Space className="pb-6 pl-6">
                  <SearchInTable />{" "}
                </Space>
                <Table
                  columns={columnsHistory}
                  dataSource={historyData}
                  loading={loadingTable}
                  pagination={{
                    total,
                    defaultPageSize: 5,
                    showTotal: (total, range) =>
                      t("tasks.tablePagination", {
                        range: `${range[0]}-${range[1]}`,
                        totalItems: total,
                      }),
                    onChange: (number) => {
                      setPage(number);
                    },
                  }}
                  scroll={{
                    x: 1300,
                  }}
                  size="small"
                />
              </Panel>
            </Collapse>

            <Drawer
              title={t("import.addType", { title: titleChildren })}
              placement="right"
              open={openChildrenImportDrawer}
              width={window.innerWidth / 2}
              closeIcon={
                <CloseOutlined
                  onClick={() => {
                    dispatch(
                      setOpenChildrenImportDrawer({
                        open: false,
                        type: typeChildren,
                      })
                    );
                  }}
                />
              }
            >
              {/* {typeChildren.toLowerCase().includes("pipelines") && <PipelineStages />} */}
              {typeChildren === "type-contacts" && <TypesContacts />}
              {typeChildren === "companies" && <TableCompanies />}
              {typeChildren === "departments" && <Departments />}
              {typeChildren === "services" && <Services />}
              {typeChildren === "roles" && <RolesUsers />}
              {typeChildren === "qualifications" && <Tags />}
              {typeChildren === "severities" && <Severities />}
              {typeChildren === "slas" && <SlaHelpDesk />}
              {typeChildren === "channels" && <General />}
            </Drawer>
          </>
        )}
      </>
    </div>
  );
};
export default Import;
