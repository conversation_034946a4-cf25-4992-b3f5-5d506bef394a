import React, { useState } from "react";
import moment from "moment-timezone";
import { Select } from "antd";

const TimezoneSelect = () => {
  const [timezone, setTimezone] = useState("");
  const [searchText, setSearchText] = useState("");

  const timezoneList = moment.tz.names().map((tz) => {
    const offset = moment.tz(tz).format("Z");
    return { name: tz, offset };
  });
  const filteredList = timezoneList.filter((tz) =>
    tz.name.toLowerCase().includes(searchText.toLowerCase())
  );

  return (
    <Select
      value={timezone}
      showSearch
      onChange={setTimezone}
      placeholder="Select Timezone"
      onSearch={setSearchText}
    >
      {timezoneList.map((tz) => (
        <Select.Option key={tz.name} value={tz.name}>
          {tz.name} ({tz.offset})
        </Select.Option>
      ))}
    </Select>
  );
};

export default TimezoneSelect;
