import { Extension } from "@tiptap/core";

// Extend the Commands interface to include the handleEnterKey command for Keymap

// Define and create the Keymap extension
const Keymap = Extension.create({
  name: "Keymap",

  addCommands() {
    return {
      handleEnterKey:
        () =>
        ({ state, chain }) => {
          const { selection } = state;
          const { $from, $to } = selection;

          // Handle Enter in taskItem
          if ($from.parent.type.name === "taskItem") {
            if ($from.pos === $from.end()) {
              return chain()
                .insertContentAt($from.pos + 1, {
                  type: "taskItem",
                  content: [{ type: "paragraph" }],
                })
                .focus($from.pos + 4)
                .run();
            }
          }

          // Handle Enter in rootblock
          if ($from.node(-1).type.name === "rootblock") {
            return chain()
              .insertContentAt($from.pos, {
                type: "rootblock",
                content: [{ type: "paragraph" }],
              })
              .focus($from.pos + 4)
              .run();
          }

          return false;
        },
    };
  },

  addKeyboardShortcuts() {
    return {
      Enter: ({ editor }) => editor.commands.handleEnterKey(),
    };
  },
});

export default Keymap;

// export default Keymap;
