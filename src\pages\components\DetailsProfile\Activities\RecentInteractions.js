import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Divider,
  Empty,
  Skeleton,
  Space,
  Spin,
  Timeline,
  Tooltip,
  Typography,
} from "antd";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { formatDateComparison } from "../../../voip/helpers/helpersFunc";
import { useSelector } from "react-redux";
import { Allfamilies } from "../../../../components/Allfamilies";
import { toastNotification } from "../../../../components/ToastNotification";
import MainService from "../../../../services/main.service";
import { useLocation, useParams } from "react-router-dom";
import { getName } from "../../../layouts/chat/utils/ConversationUtils";
import ChoiceIcons from "../../ChoiceIcons";
import SearchInTable from "../../Search";
import FilterTable from "../../../../components/FilterTable";
import {
  setActiveTab360,
  setNewInteraction,
} from "../../../../new-redux/actions/vue360.actions/vue360";
import { useDispatch } from "react-redux";
import ProfileDetails from "../../../clients&users/components/contacts-details-component/ProfileDetails";
import dayjs from "dayjs";
import ChangesInteractions from "./ChangesInteractions";
import { Audio } from "../../../../components/Chat";
import useCompAct360 from "pages/tasks/activityDetails/CompAct360";
import Activity360 from "pages/tasks/activityDetails/Activity360";
import { ConditionActions } from "pages/rmc/mailing/components/Log";
import { getIconRmc } from "../ChatRmc";
import { CommentOutlined } from "@ant-design/icons";
import OneInetraction from "./OneInetraction";
import OneInteractionFromData from "./OneInteractionFromData";
import { setOpenModalEmail } from "new-redux/actions/mail.actions";
import { setEmailFields } from "pages/rmc/mailing/main-components/email-composer-modal/utils";

const RecentInteractions = ({
  list,
  loading,
  setList,
  page,
  setPage,
  lastPage,
  addTab,
  listFilter,
  setFilter,
  setStart,
  setEnd,
  listBeforeChanged,
  setListBeforeChanged,
  dataSteps,
  headerHeight = 0,
  from = "",
  contactInfo,
}) => {
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const [loadScroll, setLoadScroll] = useState(false);
  const [openFilterTable, setOpenFilterTable] = useState(false);
  const [tasksTypes, setTasksTypes] = useState([]);
  const {
    singleTaskData,
    setSingleTaskData,
    form,
    checkedItems,
    setCheckedItems,
    checkedFollowers,
    setCheckedFollowers,
    loadGuests,
    loadSpecificTask,
    taskToUpdate,
    setTaskToUpdate,
    ownersList,
    guestsList,
    guestsSearchQuery,
    setGuestsSearchQuery,
    setFollowersSearchQuery,
    guestsListPage,
    setGuestsListPage,
    guestsListLastPage,
    loadOwners,
    files,
    setFiles,
    pipelines,
    openActivity360,
    setOpenActivity360,
    countChanges,
    setCountChanges,
    setSelectedFamilyMembers,
    setShowCardPopover,
    addOnsValues,
    setAddOnsValues,
  } = useCompAct360();
  const location = useLocation();

  const { families } = useSelector((state) => state?.families);
  const { user } = useSelector((state) => state?.user);
  const { dataAccounts } = useSelector((state) => state.mailReducer);

  useEffect(() => {
    const getTasksTypes = async () => {
      try {
        const response = await MainService.getTasksTypes();
        setTasksTypes(response?.data?.data?.tasks_type);
      } catch (error) {
        console.log(`Error ${error}`);
        toastNotification("error", t("toasts.somethingWrong"));
      }
    };
    getTasksTypes();
  }, []);

  // const [showTime, setShowTime] = useState({
  //   format: "HH:mm",
  //   defaultValue: [dayjs("00:00", "HH:mm"), dayjs("23:59", "HH:mm")],
  // });

  // useEffect(() => {
  //   // Fonction pour obtenir un icône aléatoire

  //   // Fonction pour obtenir un icône aléatoire parmi une liste

  //   const getLogs = async () => {
  //     try {
  //       const { data } = await MainService.getOverview360({
  //         family_id: module ? module : contactInfo.family_id,
  //         element_id: contactInfo?.id,
  //         page: page,
  //         search: "",
  //         filter:
  //           listFilter.selected?.length > 0
  //             ? listFilter.selected.join(",")
  //             : "",
  //       });
  //       // Création d'un objet Map pour regrouper les éléments par date
  //       const groupedData = new Map();

  //       // Parcours du tableau de données
  //       data.data.forEach((item) => {
  //         const date = item.date.split(" ")[0]; // Extraction de la date sans l'heure
  //         if (groupedData.has(date)) {
  //           groupedData.get(date).push(item);
  //         } else {
  //           groupedData.set(date, [item]);
  //         }
  //       });

  //       // Conversion du Map en tableau de groupes
  //       const groupedArray = Array.from(groupedData, ([date, items]) => ({
  //         date,
  //         items,
  //       }));
  //       alert("2");

  //       setList(groupedArray);
  //       setPage(data.meta.current_page);
  //       setCountChanges(0);
  //       // dispatch(setNewInteraction(false));
  //       //.sort((a,b)=>b.selected-a.selected));
  //     } catch (err) {}
  //   };
  //   if (countChanges > 0 || newInteraction.type) {
  //     getLogs();
  //   }
  // }, [
  //   contactInfo.id,
  //   contactInfo.family_id,
  //   countChanges,
  //   newInteraction.type,
  // ]);
  const selected = (item) => {
    return tasksTypes.find((el) => el.id === item.task_type_id);
  };
  const onScroll = async (e) => {
    if (
      Math.ceil(e.currentTarget.clientHeight + e.currentTarget.scrollTop) >=
        e.currentTarget.scrollHeight &&
      page + 1 <= lastPage
    ) {
      setPage(page + 1);

      setLoadScroll(true);
      try {
        const { data } = await MainService.getOverview360({
          family_id: contactInfo.family_id,
          element_id: contactInfo.id,
          page: page + 1,
        });
        setListBeforeChanged((prev) => [...prev, ...data.data]);

        const groupedData = new Map();
        // Parcours du tableau de données
        [...listBeforeChanged, ...data.data].forEach((item) => {
          const date = item.date.split(" ")[0]; // Extraction de la date sans l'heure
          if (groupedData.has(date)) {
            groupedData.get(date).push(item);
          } else {
            groupedData.set(date, [item]);
          }
        });

        // Conversion du Map en tableau de groupes
        const groupedArray = Array.from(groupedData, ([date, items]) => ({
          date,
          items,
        }));
        setList(groupedArray);

        setLoadScroll(false);
      } catch (err) {
        console.log(err);
        setLoadScroll(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    }
  };

  const rangePresets = (t) => [
    {
      label: t("voip.yesterday"),
      value: [
        dayjs().startOf("day").add(-1, "d"),
        dayjs().endOf("day").add(-1, "d"),
      ],
    },
    {
      label: t("voip.today"),
      value: [dayjs().startOf("day"), dayjs().endOf("day")],
    },
    {
      label: t("voip.today&yesterday"),
      value: [dayjs().startOf("day").add(-1, "d"), dayjs().endOf("day")],
    },
    {
      label: t("voip.currentWeek"),
      value: [dayjs().startOf("week"), dayjs().endOf("day")],
    },
    {
      label: t("voip.currentMonth"),
      value: [dayjs().startOf("month"), dayjs().endOf("month")],
    },

    {
      label: t("voip.last7days"),
      value: [dayjs().add(-7, "d"), dayjs()],
    },
    {
      label: t("voip.last14days"),
      value: [dayjs().add(-14, "d"), dayjs()],
    },
    {
      label: t("voip.last30days"),
      value: [dayjs().add(-30, "d"), dayjs()],
    },
  ];
  const typesCall = ["ANSWERED", "FAILED", "BUSY", "NO ANSWER", "CONGESTION"];
  return (
    // <List
    //   className="demo-loadmore-list"
    //   loading={false}
    //   itemLayout="horizontal"
    //   dataSource={list}
    //   renderItem={(item) => (
    //     <List.Item actions={[]}>
    //       <Skeleton avatar title={false} loading={item.loading} active>
    //         <List.Item.Meta
    //           avatar={<Avatar src={item.picture.large} />}
    //           title={<a href="https://ant.design">{item.name?.last}</a>}
    //           description="Ant Design, a design language for background applications, is refined by Ant UED Team"
    //         />
    //         <div>content</div>
    //       </Skeleton>
    //     </List.Item>
    //   )}
    // />
    <Spin spinning={loadScroll}>
      <Space direction="vertical" style={{ width: "100%" }} size="small">
        <Activity360
          key={taskToUpdate ? taskToUpdate : 1}
          openActivity360={openActivity360}
          setOpenActivity360={setOpenActivity360}
          taskToUpdate={taskToUpdate}
          singleTaskData={singleTaskData}
          setSingleTaskData={setSingleTaskData}
          loadSpecificTask={loadSpecificTask}
          tasksTypes={tasksTypes}
          setTaskToUpdate={setTaskToUpdate}
          pipelines={pipelines}
          guestsList={guestsList}
          checkedItems={checkedItems}
          guestsSearchQuery={guestsSearchQuery}
          setGuestsSearchQuery={setGuestsSearchQuery}
          guestsListPage={guestsListPage}
          setGuestsListPage={setGuestsListPage}
          guestsListLastPage={guestsListLastPage}
          setCheckedItems={setCheckedItems}
          ownersList={ownersList}
          checkedFollowers={checkedFollowers}
          setCheckedFollowers={setCheckedFollowers}
          setFollowersSearchQuery={setFollowersSearchQuery}
          loadOwners={loadOwners}
          loadGuests={loadGuests}
          addOnsValues={addOnsValues}
          setAddOnsValues={setAddOnsValues}
          files={files}
          setFiles={setFiles}
          countChanges={countChanges}
          setCountChanges={setCountChanges}
          setSelectedFamilyMembers={setSelectedFamilyMembers}
          form={form}
          setShowCardPopover={setShowCardPopover}
        />
        <Typography.Title level={3} style={{ color: "black" }}>
          {t("layout_profile_details.recentInteractions")}{" "}
          {/* <InfoCircleOutlined style={{ fontSize: "16px" }} /> */}
        </Typography.Title>
        <div className="flex justify-between">
          <div className="space-x-1">
            <SearchInTable source="viewSphere" />
          </div>
          <div className="inline-flex space-x-1">
            <FilterTable
              setFilter={setFilter}
              oneFilter={false}
              listFilter={listFilter}
              filters={[
                {
                  name: "Associations",
                  data: [
                    ...families.map((el) => ({
                      value: el.id,
                      text: el.label,
                    })),
                  ],
                },
                {
                  name: t("vue360.cardFilterCall"),
                  data: [
                    ...typesCall.map((el) => ({
                      value: el,
                      text: t(`vue360.${el}`),
                    })),
                  ],
                },
                {
                  name: t("vue360.cardFilterTask"),
                  data: [
                    ...tasksTypes.map((el) => ({
                      value: el.id,
                      text: el.label,
                    })),
                  ],
                },
                {
                  name: "Actions",
                  data: [
                    {
                      value: "create",
                      text: t("vue360.actionsCreate"),
                    },
                    { value: "update", text: t("vue360.actionsUpdate") },
                    { value: "delete", text: t("vue360.actionsDelete") },
                  ],
                },
              ]}
              t={t}
              openFilterTable={openFilterTable}
              setOpenFilterTable={setOpenFilterTable}
              disabled={false}
            />
            <DatePicker.RangePicker
              presets={rangePresets(t)}
              format={
                user?.location?.date_format
                  ? user?.location?.date_format +
                    " " +
                    user?.location?.time_format
                  : "YYYY-MM-DD HH:mm"
              }
              showTime={
                user?.location?.time_format
                  ? user?.location?.time_format
                  : "HH:mm"
              }
              onChange={(dates, datesStrings) => {
                setStart(datesStrings[0].toUpperCase());
                setEnd(datesStrings[1].toUpperCase());
                dispatch(setNewInteraction({ type: "search" }));
              }}
            />
          </div>
        </div>
        {loading ? (
          <Space direction="vertical" size="large" style={{ width: "100%" }}>
            <Skeleton
              avatar
              paragraph={{
                rows: 1,
              }}
            />
            <Skeleton
              avatar
              paragraph={{
                rows: 1,
              }}
            />
            {/* <Skeleton
            avatar
            paragraph={{
              rows: 1,
            }}
          /> */}
          </Space>
        ) : (
          <>
            {list.length > 0 ? (
              <div
                style={{
                  height:
                    from === "viewSphere"
                      ? `calc(100vh - ${headerHeight + 215}px)`
                      : from === "directory"
                      ? `calc(100vh - ${headerHeight + 215}px)`
                      : dataSteps.length > 0
                      ? "calc(100vh - 405px)"
                      : "calc(100vh - 310px)",
                  overflowY: "auto",
                  overflowX: "hidden",
                  marginRight: from === "viewSphere" ? "-15px" : "-22px",
                  paddingRight: "22px",
                  marginTop: `${from === "viewSphere" ? "2px" : "0"}`,
                  marginBottom: 0,
                }}
                className={`${location.pathname.includes("v3") ? "v2" : ""}`}
                onScroll={onScroll}
              >
                {list.map((data, i) => (
                  <div key={data.date?.replace(/\s/g, "") + "-" + i}>
                    <Divider
                      style={{
                        textAlign: "center",
                        position: "sticky",
                        top: "-1px",
                        background: location.pathname.includes("v3")
                          ? "#F8FAFC"
                          : "white",
                        zIndex: "20",
                        marginTop: "-10px",
                        padding: "5px 0",
                      }}
                    >
                      {formatDateComparison(data.date, t)}
                    </Divider>

                    <Timeline
                      key={i}
                      style={{ margin: "25px 15px" }}
                      // mode="left"
                      items={data.items.map((item, i) => ({
                        children: (
                          <Card
                            style={{ padding: 4 }}
                            styles={{ body: { padding: 4 } }}
                            // style={{ background: "#0000ff03" }}
                          >
                            <div
                              className={`rounded-lg px-3  text-black ${
                                from === "viewSphere" ? "v2" : ""
                              }`}
                              key={item?.date?.replace(/\s/g, "") + "-" + i}
                            >
                              {/* ${
                            item.action_type === "create"
                              ? " bg-[#00800014]"
                              : item.action_type === "update"
                              ? "bg-[#ebf3fe] "
                              : item.action_type === "delete"
                              ? "bg-[#ff000d21]"
                              : "bg-gray-50"
                          } */}
                              <span className="-mt-5 font-medium text-gray-500">
                                {item?.date?.split(" ")[1]?.slice(0, 5)}
                              </span>{" "}
                              <div>
                                {item.family_id === "email" ? (
                                  item.user[0] !== "JOB" &&
                                  item.action !== "Assign Email" &&
                                  item.action !== "chrono start" ? (
                                    <>
                                      <strong className="text-[14px]">
                                        {getName(item.user[0], "name")}
                                      </strong>{" "}
                                      <span
                                        dangerouslySetInnerHTML={ConditionActions(
                                          families,
                                          item
                                        )}
                                      />
                                      {item?.data?.subject && (
                                        <>
                                          {" " + t("vue360.withSubject") + " '"}
                                          <span className="font-medium">
                                            {item.data.subject}
                                          </span>
                                          {item.data.account_id &&
                                            dataAccounts.find(
                                              (el) =>
                                                el.value ===
                                                item.data.account_id
                                            )?.label &&
                                            "' " + t("vue360.toAddress") + " "}
                                          <Typography.Link
                                            onClick={() => {
                                              dispatch(
                                                setEmailFields({
                                                  sender: user.email,
                                                  receivers: [
                                                    dataAccounts.find(
                                                      (el) =>
                                                        el.value ===
                                                        item.data.account_id
                                                    )?.label,
                                                  ],
                                                  contactId: contactInfo?.id,
                                                  familyId:
                                                    contactInfo?.family_id,
                                                })
                                              );

                                              setTimeout(() => {
                                                dispatch(
                                                  setOpenModalEmail(true)
                                                );
                                              }, 100);
                                            }}
                                          >
                                            {
                                              dataAccounts.find(
                                                (el) =>
                                                  el.value ===
                                                  item.data.account_id
                                              )?.label
                                            }
                                          </Typography.Link>
                                        </>
                                      )}
                                    </>
                                  ) : (
                                    <span
                                      dangerouslySetInnerHTML={ConditionActions(
                                        families,
                                        item
                                      )}
                                    />
                                  )
                                ) : item.type === "comment" ||
                                  item.type === "note" ? (
                                  <div className="">
                                    <strong className="text-[14px]">
                                      {getName(item.user, "name")}
                                    </strong>
                                    &nbsp;
                                    <span>{item.action}</span>
                                  </div>
                                ) : (
                                  <div className="inline-flex items-center space-x-1">
                                    <strong className="text-[14px]">
                                      {getName(item.user, "name")}
                                    </strong>
                                    <span className="flex items-center gap-x-1">
                                      {item.action_type === "create"
                                        ? t("vue360.actionCreate")
                                        : item.action_type === "update"
                                        ? t("vue360.actionUpdate")
                                        : item.action_type === "ANSWERED"
                                        ? t("vue360.actionCall")
                                        : item.action_type === "FAILED"
                                        ? t("vue360.actionCall")
                                        : item.action_type === "BUSY"
                                        ? t("vue360.actionCall")
                                        : item.action_type === "NO ANSWER"
                                        ? t("vue360.actionCall")
                                        : item.action_type === "CONGESTION"
                                        ? t("vue360.actionCall")
                                        : item.action_type === "delete"
                                        ? t("vue360.actionDelete")
                                        : item.action_type === "transfer"
                                        ? t("vue360.transfer")
                                        : item.action_type === "relaunch"
                                        ? t("vue360.relaunch")
                                        : item.action_type === "move"
                                        ? t("vue360.move")
                                        : item.action_type === "assign"
                                        ? t("vue360.assign")
                                        : item.action_type === "affectation"
                                        ? t("vue360.affectation")
                                        : item.action_type === "qualification"
                                        ? t("vue360.qualification")
                                        : item.action_type === "merge"
                                        ? t("vue360.merge")
                                        : item.action_type === "return"
                                        ? t("vue360.return")
                                        : item.action_type ===
                                          "update_qualification"
                                        ? t("vue360.update_qualification")
                                        : item.action_type ===
                                          "delete_qualification"
                                        ? t("vue360.delete_qualification")
                                        : item.action_type ===
                                          "update_affectation"
                                        ? t("vue360.update_affectation")
                                        : item.action_type ===
                                          "delete_affectation"
                                        ? t("vue360.delete_affectation")
                                        : item.action_type === "association"
                                        ? t("vue360.association")
                                        : item.action_type ===
                                          "delete_association"
                                        ? t("vue360.delete_association")
                                        : item?.action_type}{" "}
                                      {item.family_id === 9
                                        ? getIconRmc(item.type)
                                        : item.type !== "voip"
                                        ? t(`vue360.${item.type}`)
                                        : null}
                                    </span>
                                    {item?.id_data === contactInfo.id ? (
                                      <Space>
                                        <Typography.Text
                                          type="link"
                                          className={`${
                                            item.type === "task"
                                              ? "cursor-pointer text-blue-500 hover:!text-blue-400"
                                              : "text-gray-700  hover:!text-gray-700"
                                          } p-0`}
                                          onClick={() =>
                                            item.type === "voip"
                                              ? dispatch(setActiveTab360(4))
                                              : item.type === "task"
                                              ? setOpenActivity360(true)
                                              : item.type === "email"
                                              ? null
                                              : addTab(
                                                  item.label_data,
                                                  <ProfileDetails
                                                    familyId={item.family_id}
                                                    relation_id={
                                                      item.association_id ||
                                                      item?.id_data
                                                    }
                                                    contactId={
                                                      item.association_id ||
                                                      item?.id_data
                                                    }
                                                    numberOfColumns={3}
                                                    contactName={
                                                      item.label_data
                                                    }
                                                    editable={true}
                                                  />
                                                )
                                          }
                                        >
                                          {item.action_type !== "send message"
                                            ? `${
                                                item.label_data
                                                  ? getName(
                                                      item.label_data +
                                                        (item.action_type ===
                                                        "return"
                                                          ? t(
                                                              "vue360.toTheDefaultFolder"
                                                            )
                                                          : ""),
                                                      "name"
                                                    )
                                                      ?.replace(
                                                        /from (\d+)/,
                                                        `${t("vue360.from")} $1`
                                                      )
                                                      ?.replace(
                                                        /in (\d+)/,
                                                        `${t("vue360.to")} $1`
                                                      )
                                                      ?.replace(
                                                        "with duration",
                                                        t("vue360.withDuration")
                                                      )
                                                  : item.action + "kkkk"
                                              }`
                                            : null}
                                        </Typography.Text>
                                        {item.family_id === "call" &&
                                          item?.data.map((el) => (
                                            <Audio
                                              url={el}
                                              isFullURL={true}
                                              key={i}
                                            />
                                          ))}
                                      </Space>
                                    ) : (
                                      <Button
                                        type="link"
                                        className={`${
                                          item.type !== "task" &&
                                          "text-gray-700 hover:!text-gray-700"
                                        } p-0`}
                                        onClick={() =>
                                          item.type === "voip"
                                            ? dispatch(setActiveTab360(4))
                                            : item.type === "task"
                                            ? (setTaskToUpdate(item.id_data),
                                              setOpenActivity360(true))
                                            : item.type === "email"
                                            ? null
                                            : addTab(
                                                item.label_data,
                                                <ProfileDetails
                                                  familyId={item.family_id}
                                                  relation_id={
                                                    item.association_id ||
                                                    item.id_data
                                                  }
                                                  contactId={
                                                    item.association_id ||
                                                    item.id_data
                                                  }
                                                  numberOfColumns={3}
                                                  contactName={item.label_data}
                                                  editable={true}
                                                />
                                              )
                                        }
                                      >
                                        {item.family_id !== "send message"
                                          ? `${
                                              item.label_data
                                                ? getName(
                                                    item.label_data,
                                                    "name"
                                                  )
                                                    ?.replace(
                                                      /from (\d+)/,
                                                      `${t("vue360.from")} $1`
                                                    )
                                                    ?.replace(
                                                      /in (\d+)/,
                                                      `${t("vue360.to")} $1`
                                                    )
                                                    ?.replace(
                                                      "with duration",
                                                      t("vue360.withDuration")
                                                    )
                                                : item.action
                                            }`
                                          : null}
                                      </Button>
                                    )}
                                  </div>
                                )}
                                {Object.entries(item.data).length > 0 &&
                                item.family_id === 6 ? (
                                  <ul>
                                    {Object.entries(item.data).map(
                                      ([key, value], index) => {
                                        console.log("value", value);
                                        return (
                                          typeof value === "string" &&
                                          item.action_type !== "delete" && (
                                            <OneInteractionFromData
                                              key={key}
                                              keyValue={key}
                                              value={value}
                                              index={index}
                                            />
                                          )
                                        );
                                      }
                                    )}
                                  </ul>
                                ) : null}

                                {Object.keys(item?.changes).length > 0 ? (
                                  <>
                                    <ChangesInteractions
                                      item={item}
                                      key={item?.date?.replace(/\s/g, "")}
                                    />
                                  </>
                                ) : null}
                              </div>
                            </div>
                          </Card>
                        ),
                        dot: (
                          <div className="#F8FAFC">
                            {/* className={`${
                               from==="viewSphere"
                                ? "m-0 bg-slate-50 p-0"
                                : "bg-white"
                            }`} */}
                            <Tooltip
                              title={
                                item.family_id !== "task"
                                  ? t(
                                      `${`vue360.${
                                        item.family_id === "call"
                                          ? item.action_type
                                          : null
                                      }`}`
                                    ) +
                                    " " +
                                    t(`vue360.${item.type}`)
                                  : selected(item)?.label +
                                    " " +
                                    "(" +
                                    t(`vue360.${item.type}`) +
                                    ")"
                              }
                            >
                              <div className="-mb-5">
                                <div
                                  className={`rounded-full p-2  ${
                                    item.action_type === "create"
                                      ? "bg-green-500"
                                      : item.action_type === "update"
                                      ? "bg-blue-500"
                                      : item.action_type === "delete"
                                      ? "bg-red-500"
                                      : item.action_type === "transfer" ||
                                        item.action_type === "relaunch" ||
                                        item.action_type === "move" ||
                                        item.action_type === "assign" ||
                                        item.action_type === "merge" ||
                                        item.action_type === "return"
                                      ? "bg-indigo-500"
                                      : item.action_type === "affectation" ||
                                        item.action_type === "qualification" ||
                                        item.action_type ===
                                          "update_qualification" ||
                                        item.action_type ===
                                          "delete_qualification" ||
                                        item.action_type ===
                                          "update_affectation" ||
                                        item.action_type ===
                                          "delete_affectation" ||
                                        item.family_id === "email"
                                      ? "bg-orange-400"
                                      : item.family_id === "call"
                                      ? location.pathname.includes("v3")
                                        ? "bg-white"
                                        : "bg-slate-100"
                                      : "bg-gray-400 "
                                  } `}
                                >
                                  {item.family_id === "call" ? (
                                    Allfamilies.find(
                                      (el) => el.id === item.action_type
                                    )?.icon
                                  ) : item.family_id === "task" ? (
                                    <span>
                                      <ChoiceIcons
                                        icon={selected(item)?.icons}
                                        fontSize={"18px"}
                                      />
                                    </span>
                                  ) : item.type === "comment" ||
                                    item.type === "note" ? (
                                    <CommentOutlined
                                      style={{ fontSize: "18px" }}
                                    />
                                  ) : item.family_id ? (
                                    Allfamilies.find(
                                      (el) => el.id === item.family_id
                                    )?.icon
                                  ) : (
                                    Allfamilies.find(
                                      (el) => el.id === item.type
                                    )?.icon
                                  )}
                                </div>
                              </div>
                            </Tooltip>
                          </div>
                        ),
                        color: "white",
                      }))}
                    />
                  </div>
                ))}

                {/* {loadScoll ? (
                <div
                  style={{
                    position: "absolute",
                    top: "50%",
                    left: "50%",
                    transform: "translate(-50%, -50%)",
                    zIndex: 500,
                    // background: "white",
                  }}
                >
                  <Spin />
                </div>
              ) : (
                ""
              )} */}

                {/* <List>
            <VirtualList
              data={list}
              height={Number(windowHeight) - 350}
              className="list-overview-360"
              itemKey="email"
              onScroll={onScroll}
            >
              {(item) => (
                <Card bordered={false} style={{ marginBottom: "10px" }}>
                  <List.Item actions={[humanDate(item.date, t)]}>
                    <Skeleton avatar title={false} loading={loading} active>
                      <List.Item.Meta
                        avatar={
                          <span
                            className={
                              item.action_type === "create"
                                ? "text-green-600"
                                : "text-black"
                            }
                          >
                            {" "}
                            {
                              Allfamilies.find((el) => el.id === item.family_id)
                                ?.icon
                            }
                          </span>
                        }
                        title={
                          (item.action_type === "create" ||
                          item.action_type === "creation"
                            ? t("vue360.titleCreation")
                            : item.action_type === "update"
                            ? t("vue360.titleUpdate")
                            : item.action_type === "delete"
                            ? t("vue360.delete")
                            : t(`vue360.${item.action_type}`)) +
                          (item.action_type && item.action_type !== null
                            ? ""
                            : " ") +
                          t(`${`vue360.${item.type}`}`)
                        }
                        description={
                          <div className={` text-black`}>
                            <span className="font-bold">{item.user}</span>{" "}
                            {item.action_type === "create"
                              ? t("vue360.actionCreate")
                              : item.action_type === "update"
                              ? t("vue360.actionUpdate")
                              : item.action_type === "ANSWERED"
                              ? t("vue360.actionCall")
                              : item.action_type === "FAILED"
                              ? t("vue360.actionCall")
                              : item.action_type === "BUSY"
                              ? t("vue360.actionCall")
                              : item.action_type === "NO ANSWER"
                              ? t("vue360.actionCall")
                              : item.action_type === "CONGESTION"
                              ? t("vue360.actionCall")
                              : item.action_type === "delete"
                              ? t("vue360.actionDelete")
                              : ""}{" "}
                            <Typography.Link>
                              {item.label_data
                                .replace("from", t("vue360.from"))
                                .replace("in", t("vue360.to"))}
                            </Typography.Link>
                            <ol className="list-none space-y-0.5">
                              {Object.keys(item?.changes).length > 0 &&
                                Object.entries(item?.changes).map(
                                  ([key, value]) => (
                                    <li key={key}>
                                      {item.action_type === "update" &&
                                      !value[0] ? (
                                        <span>
                                          <BsArrowReturnRight />{" "}
                                          {t("vue360.actionUpdateFrom")}{" "}
                                          <span className="font-semibold">
                                            {" "}
                                            {key}:{" "}
                                          </span>
                                          {t("vue360.by")}{" "}
                                          {isImagePath(value[1]) ? (
                                            <Image
                                              width={100}
                                              src={
                                                URL_ENV
                                                  .REACT_APP_AVATAR_URL +
                                                value[1]
                                              }
                                            />
                                          ) : (
                                            <span className="font-semibold">
                                              {value[1]}
                                            </span>
                                          )}
                                        </span>
                                      ) : item.action_type === "update" &&
                                        value[0] ? (
                                        <span>
                                          <BsArrowReturnRight />{" "}
                                          {t("vue360.actionUpdate")}{" "}
                                          <span className="font-semibold">
                                            {" "}
                                            {key}:{" "}
                                          </span>
                                          {t("vue360.from")}{" "}
                                          {isImagePath(value[0]) ? (
                                            <Image
                                              width={100}
                                              src={
                                                URL_ENV
                                                  .REACT_APP_AVATAR_URL +
                                                value[0]
                                              }
                                            />
                                          ) : (
                                            <span className="font-semibold">
                                              {value[0]}
                                            </span>
                                          )}{" "}
                                          {t("vue360.to")} &nbsp;
                                          {isImagePath(value[1]) ? (
                                            <Image
                                              width={100}
                                              src={
                                                URL_ENV
                                                  .REACT_APP_AVATAR_URL +
                                                value[1]
                                              }
                                            />
                                          ) : (
                                            <span className="font-semibold">
                                              {value[1]}
                                            </span>
                                          )}{" "}
                                        </span>
                                      ) : (
                                        ""
                                      )}
                                    </li>
                                  )
                                )}
                            </ol>
                          </div>
                        }
                      />
                    </Skeleton>
                  </List.Item>
                </Card>
              )}
            </VirtualList>
            {loadScoll ? <Spin /> : ""}
          </List> */}
              </div>
            ) : (
              <Empty />
            )}
          </>
        )}
      </Space>
    </Spin>
  );
};

export default RecentInteractions;
