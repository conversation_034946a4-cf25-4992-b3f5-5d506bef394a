import {
  GET_LOG_SUCCESS,
  IS_LOADING_LOG,
  SET_LAST_CALL_LOG,
  SET_LOADING_TRUE,
  SET_NEW_CALL_LOG,
} from "new-redux/constants";
import { getLogs } from "./getLogs";

export const setNewCallLog = (newCallLog, oldLog) => (dispatch) => {
  dispatch({ type: SET_LOADING_TRUE });
  // // setTimeout(() => {
  if (newCallLog?.length) {
    dispatch({ type: SET_LAST_CALL_LOG, payload: newCallLog?.[0] });
  }
  //  else {
  //   dispatch({ type: SET_LAST_CALL_LOG, payload: "not_found" });
  // }
  //   dispatch({
  //     type: SET_NEW_CALL_LOG,
  //     // payload: { newCall: newCallLog, oldLog },
  //     payload: newCallLog,
  //   });
  // } else {
  dispatch(
    getLogs(false, oldLog?.length < 50 ? Math.max(oldLog.length, 100) : 50)
  );
  // }
  // }, 1);
};

// export const setCallFirstTime = (callFirstTime, logs) => async (dispatch) => {
//   if (!callFirstTime || !callFirstTime._id) return;
//   const callId = callFirstTime?._id;
//   if (!logs.length) return;
//   const updatedCallLog = logs.map((call) =>
//     call?._id === callId ? { ...call, ...callFirstTime } : call
//   );

//   dispatch({
//     type: GET_LOG_SUCCESS,
//     payload: {
//       data: updatedCallLog,
//       total: updatedCallLog.length,
//     },
//   });
// };
