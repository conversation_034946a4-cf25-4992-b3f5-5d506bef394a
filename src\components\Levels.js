import React, { useEffect, useRef } from "react";
import {
  Form,
  Typography,
  Input,
  Button,
  Select,
  Badge,
  Space,
  Tag,
  Switch,
} from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { generateAxios } from "../services/axiosInstance";
import { toastNotification } from "../components/ToastNotification";
import Header from "../components/configurationHelpDesk/Header";

import { allIcons } from "../components/Icons";
import { colors } from "../components/Colors";
import { useDispatch, useSelector } from "react-redux";
import { setSearch } from "../new-redux/actions/menu.actions/menu";
import NewTableDraggable from "../components/NewTableDraggable";
import LabelTable from "../components/LabelTable";
import BottomButtonAddRow from "../components/BottomButtonAddRow";
import { SubmitKeyPress } from "../utils/SubmitKeyPress";
import MainService from "../services/main.service";
import { URL_ENV } from "index";

const Levels = () => {
  const [form] = Form.useForm();
  const [debounceValue, setDebounceValue] = useState("");
  const [dataform, setDataForm] = useState({});
  const [data, setData] = useState([]);
  const [editingKey, setEditingKey] = useState("");
  const [id, setId] = useState(null);

  const [dataUsers, setDataUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [loadingSwitch, setLoadingSwitch] = useState(false);
  const isEditing = (record) => record.key === editingKey;
  const [t] = useTranslation("common");

  const [currentPage, setCurrentPage] = useState(1);
  const [oldPage, setOldPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  const dispatch = useDispatch();
  const { search } = useSelector((state) => state.form);
  useEffect(() => {
    const timer = setTimeout(() => setDebounceValue(search), 100);

    return () => {
      clearTimeout(timer);
    };
  }, [search]);
  // useEffect(() => {
  //   if (editingKey && oldPage !== currentPage) {
  //     setData((prev) => prev.filter((el) => el.id));
  //     setEditingKey("");
  //   }
  // }, [currentPage, editingKey, oldPage]);
  const inputRefs = useRef([]);
  useEffect(() => {
    return () => {
      dispatch(setSearch(""));
    };
  }, []);

  const filterOption = (input, option) =>
    (option?.label ?? "").toLowerCase().includes(input.toLowerCase());

  const UsersData = async (abort) => {
    try {
      let formData = new FormData();
      formData.append("family_id[]", 4);

      let response = await MainService.getFamilyOptions(
        "",
        "",
        formData,
        abort.signal
      );
      console.log(response);
      if (response.status === 200) {
        setDataUsers(response.data.data);
      }
    } catch (err) {
      console.log(err);
    }
  };

  useEffect(() => {
    let abort = new AbortController();
    UsersData(abort);
    return () => abort?.abort();
  }, []);

  useEffect(() => {
    inputRefs.current.forEach((input) => {
      input?.focus();
    });
  }, [id, data.length]);

  const onFinishFailed = (values) => {
    console.log(values);
  };
  const handleKeyPress = (event) => {
    SubmitKeyPress(event, form);
  };

  const EditableCell = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    const inputNode =
      inputType === "switch" ? (
        <>
          <Switch
            size="small"
            defaultChecked={
              !loading
                ? record.status === true
                  ? true
                  : false
                : form.getFieldsValue().status == false
                ? false
                : true
            }
          />
        </>
      ) : inputType === "tasks" ? (
        <Select
          mode="multiple"
          placeholder={t("tags.selectUsers")}
          filterOption={(input, option) =>
            (option?.label ?? "").includes(input)
          }
          options={
            dataUsers.length > 0 &&
            dataUsers.map((item) => ({
              value: item.id,
              label: item.label,
            }))
          }
        />
      ) : (
        <Input
          ref={(el) => (inputRefs.current[0] = el)}
          onKeyPress={handleKeyPress}
          placeholder={t("activities.name")}
          autoFocus={true}
        />
      );
    return (
      <td {...restProps}>
        {editing ? (
          <Form.Item
            name={dataIndex.toLowerCase()}
            style={{
              margin: 0,
            }}
            rules={[
              {
                required:
                  dataIndex === "label" || dataIndex === "users" ? true : false,
                message: `${t(`tags.${dataIndex}`)} ${t(
                  "table.header.isrequired"
                )}`,
              },
            ]}
          >
            {inputNode}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };

  const edit = (record) => {
    if (record) {
      form.setFieldsValue({
        label: record.label.replace(/<\/?[^>]+(>|$)/g, ""),
        users: record.users.map((el) => el.id),
        status: record.status,
      });
      setDataForm({
        label: record.label,
        users: record.users.map((el) => el.id),
        status: record.status,
      });
      setId(record.id);
    } else {
      form.setFieldsValue({
        label: "",
        users: [],
        status: null,
      });
    }
    setEditingKey(record.id);
  };

  const cancel = (record) => {
    setEditingKey("");
    setId(null);
    if (!record.id) {
      setData(data.filter((item) => item.key !== record.key));
    }
  };

  const save = async (key) => {
    setLoading(true);
    if (id) {
      try {
        const row = await form.validateFields();

        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).put(
          `/levels/${id}`,
          {
            ...row,
            status: row.status ? 1 : 0,
          },
          {
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
            },
          }
        );
        setEditingKey("");

        setData((previous) =>
          previous.map((el) =>
            el.id === res.data.data[0].id
              ? {
                  ...res.data.data[0],
                  key: res.data.data[0].id,
                }
              : el
          )
        );
        form.setFieldsValue({
          label: null,
          status: null,
          users: [],
        });
        setDataForm({});
        setLoading(false);
        toastNotification("success", row.label + t("toasts.edit"), "topRight");
      } catch (errInfo) {
        setLoading(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    } else {
      try {
        const row = await form.validateFields();
        console.log("row", row);
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post("/levels", {
          ...row,
          status: row.status ? 1 : 0,
        });
        setEditingKey("");
        console.log(res.data.data[0]);
        setData((prev) => [
          ...prev.filter((el) => el.id),
          { ...res.data.data[0], key: res.data.data[0].id },
        ]);
        // setData(res.data.data.map((el) => ({ ...el, key: el.id })));
        form.setFieldsValue({
          label: "",
          users: [],
          status: null,
        });
        setDataForm({});

        setLoading(false);
        toastNotification(
          "success",
          row.label + t("toasts.created"),
          "topRight"
        );
      } catch (errInfo) {
        setLoading(false);
        console.log(errInfo);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    }
  };

  const UpdateSwitch = async (record, status) => {
    setLoadingSwitch(true);
    console.log("record", record);
    try {
      const res = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).put(
        `/levels/${record.id}`,

        {
          label: record.label,
          users: record.users.map((el) => el.id),
          status: status ? 1 : 0,
        },
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      );
      setEditingKey("");
      setData(
        data.map((el) =>
          el.id === res.data.data[0].id
            ? {
                ...res.data.data[0],
                key: res.data.data[0].id,
              }
            : el
        )
      );

      setLoadingSwitch(false);
      toastNotification("success", record.label + t("toasts.edit"), "topRight");
    } catch (errInfo) {
      setLoadingSwitch(false);

      toastNotification("error", t("toasts.somethingWrong"), "topRight");
    }
  };

  useEffect(() => {
    const getData = async () => {
      setLoading(true);
      try {
        const response = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get(`/levels`);

        if (response.status === 200) {
          setData(response.data.data.map((el) => ({ ...el, key: el.id })));
          setLoading(false);
        }
      } catch (err) {
        setLoading(false);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    };

    getData();
  }, []);

  const columns = [
    {
      title: t("helpDesk.Levels"),
      dataIndex: "label",
      key: "label",
      editable: true,
      sorter: (a, b) => a.label.localeCompare(b.label),
      render: (_, record) => {
        return (
          <LabelTable record={record} editingKey={editingKey} edit={edit} />
        );
      },
    },
    {
      title: "Users",
      dataIndex: "users",
      key: "users",
      editable: true,

      render: (_, { users }) => (
        <Space size={[0, 8]} wrap>
          {users &&
            users.length > 0 &&
            users.map((item, i) => <Tag key={i}>{item.label} </Tag>)}
        </Space>
      ),
    },
    {
      title: t("helpDesk.status"),
      key: "status",
      dataIndex: "status",
      editable: true,
      filters: [
        {
          text: t(`helpDesk.actif`),
          value: "1",
        },
        {
          text: t(`helpDesk.noActif`),
          value: "0",
        },
      ],
      onFilter: (value, record) => record.status == value,
      sorter: (a, b) => a.status - b.status,

      render: (_, record) => (
        <>
          <Switch
            loading={loadingSwitch}
            size="small"
            checked={record.status == 1 ? true : false}
            onChange={(e) => UpdateSwitch(record, e)}
          />
        </>
      ),
    },
  ];
  const onRow = () => {};

  const handleAdd = () => {
    const ids = data.map((object) => {
      return object.id;
    });
    setId(null);

    const newData = {
      key: Math.max(...ids) + 1,
      label: "",
      users: [],
      status: null,
      disabled: true,
    };
    setCurrentPage(Math.ceil([...data, newData].length / pageSize));
    setOldPage(Math.ceil([...data, newData].length / pageSize));

    setData([...data, newData]);
    form.setFieldsValue({
      label: "",
      users: [],
      status: null,
    });
    setEditingKey(Math.max(...ids) + 1);
  };
  return (
    <Space direction="vertical" style={{ width: "100%" }}>
      <div className="pt-4">
        <Header
          active={"4"}
          editingKey={editingKey}
          handleAdd={handleAdd}
          btnText={t("helpDesk.addLevel")}
          disabled={loading ? true : editingKey ? true : search ? true : false}
        />
      </div>

      <NewTableDraggable
        columns={columns}
        setLoading={setLoading}
        isEditing={isEditing}
        data={data}
        setData={setData}
        loading={loading}
        save={save}
        edit={edit}
        EditableCell={EditableCell}
        onFinishFailed={onFinishFailed}
        cancel={cancel}
        form={form}
        apiRank="rank-level"
        editingKey={editingKey}
        api="levels"
        onRow={onRow}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        pageSize={pageSize}
        setPageSize={setPageSize}
      />
      <BottomButtonAddRow
        editingKey={editingKey}
        data={data}
        text={t("helpDesk.addLevel")}
        handleAdd={handleAdd}
        loading={loading}
        search={search || ""}
      />
    </Space>
  );
};
export default Levels;
