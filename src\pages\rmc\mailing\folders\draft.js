import { <PERSON><PERSON>, Table, Tooltip, Space, Avatar, Badge } from "antd";
import { useCallback, useEffect, useMemo, useState } from "react";

import { AiOutlineDelete } from "react-icons/ai";

import { MdOutlineMarkunread } from "react-icons/md";
import { VscMailRead } from "react-icons/vsc";
import { DeleteOutlined } from "@ant-design/icons";

import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import MarkDown from "../Markdown";
import MainService from "../../../../services/main.service";
import FormCreate from "../../../clients&users/components/FormCreate";
import CreateTask from "../../../voip/components/CreateTask";
import useDebounce from "../../../components/UseDebounce/UseDebounce";
import dayjs from "dayjs";
import {
  ReadUnreadMessages,
  SeenMessage,
  TrashListMessages,
} from "../services/ActionsApi";

import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import {
  setNumberEmailThread,
  setPage,
  setPageSize,
} from "new-redux/actions/mail.actions";
import ActionsStarredImportant from "../actionsStarredImportant";
import { formatDateForDisplay } from "pages/tasks/helpers/formatDateToDisplay";
import DropdownActionsTable from "../dropdownActionsTable";
import ModalDeleteEmail from "../components/ModalDeleteEmail";
import "../mailing.css";
import { checkAccessRoleAccount } from "../mailing";

const Draft = ({
  setDetailsMail,
  dataAccounts,
  searchMail,
  refresh,
  setRefresh,
}) => {
  const [dataMailDraft, setDataMailDraft] = useState([]);
  const [metaMailDraft, setMetaMailDraft] = useState({});
  const [error, setError] = useState(false);
  const [openForm, setOpenForm] = useState(false);
  const [familyId, setFamilyId] = useState(null);
  const [openTask, setOpenTask] = useState(false);
  const [titleTask, setTiltleTask] = useState("");
  const [openModal, setOpenModal] = useState(false);
  const [EmailId, setEmailId] = useState("");
  const [typeDelete, setTypeDelete] = useState("");
  const [clickArchive, setClickArchive] = useState(false);
  const [loading, setLoading] = useState({ state: false, type: null });
  const [t] = useTranslation("common");
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const usedAccount = useMemo(
    () => dataAccounts?.find((item) => item?.selected),
    [dataAccounts]
  );
  const { page, pageSize, searchEmail } = useSelector(
    (state) => state.mailReducer
  );
  const { user } = useSelector(({ user }) => user);
  const debouncedSearchValue = useDebounce(searchEmail, 500);

  const isToday = require("dayjs/plugin/isToday");

  dayjs.extend(isToday);

  const getMailsDraft = useCallback(async () => {
    // if (Object.values(metaMailDraft).length > 0 && !refresh) return;
    setLoading({ state: true, type: "mails" });

    let response = "";
    try {
      if (debouncedSearchValue.length > 0) {
        response = await MainService.searchMailsDraft(
          usedAccount?.value,
          debouncedSearchValue,
          page,
          pageSize
        );
      } else {
        response = await MainService.getDraftEmails(
          usedAccount?.value,
          page,
          pageSize
        );
      }
      if (response?.status === 200) {
        checkAccessRoleAccount(response, navigate, t);

        setDataMailDraft(response.data.data);
        setMetaMailDraft(response.data.meta);
        setError(false);
      }
    } catch (err) {
      setError(true);
      console.log(err);
    } finally {
      setLoading(false);
      setRefresh(false);
    }
  }, [
    usedAccount?.value,
    debouncedSearchValue,
    page,
    refresh,
    metaMailDraft?.currentPage,
    pageSize,
  ]);

  const dataSourceInbox = dataMailDraft.map((item, i) => ({
    key: item.id,
    from: item.from.name,
    // to: item.to,
    toEmail: item.to,
    subject: item.subject,
    body: item.body,
    nbr: item.nbr,
    date: item.date,
    seen: item.seen,
    starred: item.starred,
    important: item.important,
  }));

  const columns = [
    {
      dataIndex: "brouillon",
      key: "Id",
      width: "120px",
      fixed: "left",
      render: (_, record) => (
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-evenly",
          }}
        >
          <ActionsStarredImportant
            record={record}
            getMails={getMailsDraft}
            usedAccount={usedAccount}
          />
          <p style={{ color: "red" }}>Brouillon</p>
        </div>
      ),
    },
    {
      fixed: "left",
      ellipsis: true,
      // title: t("mailing.Inbox.To"),
      dataIndex: "toEmail",
      width: "120px",
      render: (text, record) => {
        return (
          <div className={`flex cursor-pointer items-center `}>
            <Tooltip
              placement="topLeft"
              title={text
                .map((item) =>
                  item?.name?.length > 0 ? item.name : item.address
                )
                .join(", ")}
            >
              <p
                className=" max-w-sm truncate"
                style={{ width: "80%", cursor: "pointer", marginLeft: "3px" }}
              >
                {text
                  .map((item) =>
                    item?.name?.length > 0 ? item.name : item.address
                  )
                  .join(", ")}
              </p>
            </Tooltip>

            <div className="action-mail ">
              {" "}
              <Badge
                style={{
                  backgroundColor: "transparent",
                  border: "none",
                  outline: "none",
                  margin: 0,
                  color: "gray",
                  fontSize: 12,
                  fontWeight: record?.seen === 0 ? "bold" : "normal",
                }}
                count={record?.nbr}
              ></Badge>
              <div className="hidden items-center gap-x-1 group-hover:flex ">
                <DropdownActionsTable
                  record={record}
                  t={t}
                  conditionActions={false}
                  usedAccount={usedAccount}
                  dataMailOutbox={dataMailDraft}
                  setDataMailOutbox={setDataMailDraft}
                  user={user}
                  setOpenTask={setOpenTask}
                  setEmailId={setEmailId}
                  setOpenModal={setOpenModal}
                  setTypeDelete={setTypeDelete}
                  getMailsInbox={getMailsDraft}
                  clickArchive={clickArchive}
                  setClickArchive={setClickArchive}
                  type="drafts"
                />
              </div>
            </div>
          </div>
        );
      },
    },
    {
      title: t("mailing.Inbox.subject"),
      dataIndex: "subject",
      width: "150px",
      render: (text, record) => {
        return (
          <div style={{ cursor: "pointer" }}>
            <span
              style={{
                fontWeight: record.seen === 0 ? "bold" : "",
                cursor: "pointer",
              }}
            >
              {text?.length > 30 ? (
                // <MarkDown>{text.toString()?.substring(0, 30)}...</MarkDown>
                <span
                  dangerouslySetInnerHTML={{
                    __html: text.toString()?.substring(0, 30) + "...",
                  }}
                />
              ) : (
                // <MarkDown>{text}</MarkDown>
                <span dangerouslySetInnerHTML={{ __html: text }} />
              )}
            </span>
          </div>
        );
      },
    },
    {
      title: t("mailing.Inbox.message"),
      dataIndex: "body",
      width: "300px",
      ellipsis: true,
      render: (text, record) => {
        return (
          <div style={{ cursor: "pointer" }}>
            {/* <MarkDown>{text}</MarkDown> */}
            <span dangerouslySetInnerHTML={{ __html: text }} />
          </div>
        );
      },
    },

    {
      title: t("mailing.Inbox.date"),
      dataIndex: "date",
      width: "120px",
      render: (dateTime, record) => (
        <span
          style={{
            fontWeight: record.seen === 0 ? "bold" : "",
            cursor: "pointer",
          }}
        >
          {formatDateForDisplay(
            record.date,
            `${user?.location?.date_format} ${user?.location?.time_format}`,
            user,
            t
          )}
        </span>
      ),
    },
  ];

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };
  const hasSelected = selectedRowKeys.length > 0;

  const handleMarkAsReadUnread = async (status, selectedRowKeys) => {
    const response = await ReadUnreadMessages({
      usedAccount,
      status,
      selectedRowKeys,
      setSelectedRowKeys,
      t,
      dispatch,
    });

    if (response && debouncedSearchValue?.length === 0) getMailsDraft();
  };

  const DeleteMail = async () => {
    setLoading({ state: true, type: "delete" });

    const response = await TrashListMessages({
      usedAccount,
      id: EmailId,
      selectedRowKeys,
      setSelectedRowKeys,
      setOpenModal,
      folder: "draft",
      typeDelete,
      setLoading,
      t,
    });
    if (response) getMailsDraft();
  };

  useEffect(() => {
    if (dataAccounts.length > 0) {
      getMailsDraft(1);
    }
  }, [getMailsDraft]);

  return (
    <>
      {hasSelected ? (
        <div className="mb-[8px] ml-[20px] flex items-center space-x-3">
          <Button
            type="primary"
            danger
            icon={<DeleteOutlined />}
            onClick={() => {
              setOpenModal(true);
              setTypeDelete("multiple");
            }}
          >
            {t("mailing.DeleteButton")} ({selectedRowKeys.length}{" "}
            {selectedRowKeys.length > 1 ? "emails" : "email"})
          </Button>
          {usedAccount?.shared == 0 ? (
            <>
              <Button
                type="default"
                onClick={() => handleMarkAsReadUnread(1, selectedRowKeys)}
              >
                {t("mailing.markRead")} ({selectedRowKeys.length})
              </Button>

              <Button
                type="default"
                onClick={() => handleMarkAsReadUnread(0, selectedRowKeys)}
              >
                {t("mailing.markUnread")} ({selectedRowKeys.length})
              </Button>
            </>
          ) : null}
        </div>
      ) : null}
      <FormCreate open={openForm} setOpen={setOpenForm} familyId={familyId} />

      <CreateTask
        open={openTask}
        setOpen={setOpenTask}
        mask={false}
        source="mailing"
        object={titleTask}
      />

      <Table
        className="mailing-custom-row"
        loading={loading.state && loading.type === "mails"}
        rowSelection={rowSelection}
        columns={columns}
        dataSource={dataSourceInbox}
        showSizeChanger={false}
        pagination={{
          current: page,
          pageSize: pageSize,
          pageSizeOptions: ["10", "20", "30"],
          total: metaMailDraft.total === 0 ? 1 : metaMailDraft.total,
          showSizeChanger: true,
          onChange: (page, pageSize) => {
            dispatch(setPage(page));
            dispatch(setPageSize(pageSize));
          },
          showTotal: (total, range) =>
            `${range[0]}-${range[1]} of ${total} items`,
        }}
        scroll={{ y: "calc(100vh - 250px)" }}
        onRow={(record) => {
          return {
            onClick: () => {
              setDetailsMail([]);
              dispatch(setNumberEmailThread(record.nbr));
              navigate(`/mailing/${usedAccount?.value}/drafts/${record.key}`);
            },
          };
        }}
        rowClassName={(_, index) =>
          `${index === 5 ? "" : "clickable-row"} group`
        }
        size="small"
        locale={{
          emptyText: (
            <p className={error ? "mt-4 text-red-600" : "mt-4 text-[#898282]"}>
              {error
                ? t("toasts.errorFetchApi")
                : loading.state && loading.type === "mails"
                ? "Loading ..."
                : t("mailing.noData")}
            </p>
          ),
        }}
      />

      <ModalDeleteEmail
        t={t}
        openModal={openModal}
        setOpenModal={setOpenModal}
        loading={loading}
        DeleteMail={DeleteMail}
        typeDelete={typeDelete}
        selectedRowKeysLength={selectedRowKeys.length}
      />
    </>
  );
};

export default Draft;
