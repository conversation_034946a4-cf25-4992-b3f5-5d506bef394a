import {
  GET_USER_SUCCESS,
  GET_USER_ERROR,
  IS_LOADING_USER,
} from "../../constants";
import MainService from "../../../services/main.service";

export const getUser = () => async (dispatch) => {
  try {
    dispatch({ type: IS_LOADING_USER });
    const response = await MainService.collegueApiIPBX();
    dispatch({
      type: GET_USER_SUCCESS,
      payload: response?.data.data || [],
    });
  } catch (error) {
    dispatch({
      type: GET_USER_ERROR,
      payload: error,
    });
  }
};
