import React, { useState } from "react";
import { useMutation , useQueryClient} from "@tanstack/react-query";
import {  uploadFolderDrive } from "../../services/main.service";
import {
  Button,
  Dropdown,
  Space,
  Modal,
  Input,
  Form,
  message,
} from "antd";
import {
  FolderFilled,
  PlusOutlined,
  DownOutlined,
  FolderAddOutlined,
  FileAddOutlined,
  CloudUploadOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import UploadInput from "./UploadInput";

const CreateItemsDropdown = () => {
  const queryClient = useQueryClient();
  const { parentItem } = useSelector((state) => state.drive);
  
  const [isFolderModalVisible, setIsFolderModalVisible] = useState(false);
  const [isUploadModalVisible, setIsUploadModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [t] = useTranslation("common");



  const createFolderMutation = useMutation({
    mutationFn: (formData) => uploadFolderDrive(formData),
    onSuccess: () => {
      setIsFolderModalVisible(false);
      form.resetFields();
      queryClient.invalidateQueries([["drive-items"]], {exact:false } );
      queryClient.invalidateQueries(["drive-tree"], {exact:false } );
      queryClient.invalidateQueries(["drive-storage-info"], {exact:false } );
      message.success(t("drive.folderCreatedSuccessfully"));
    },
    onError: (error) => {
      console.error("Create folder error:", error);
      message.error(t("drive.failedToCreateFolder"));
    },
  });

  const handleCreateAction = (type) => {
    if (type === "file") {
      setIsUploadModalVisible(true);
    } else {
      setIsFolderModalVisible(true);
      form.resetFields();
    }
  };

  const handleUploadSuccess = () => {
    setIsUploadModalVisible(false);
    queryClient.invalidateQueries([["drive-items"]], {exact:false });
  };

  const handleCreateSubmit = async (values) => {
    try {
      const formData = new FormData();
      formData.append("name", values.name);
      !!parentItem && formData.append("parent", parentItem || "");
      formData.append("type", "folder");
      
      createFolderMutation.mutate(formData);
    } catch (error) {
      console.error("Error creating folder:", error);
    }
  };

  const handleFolderModalCancel = () => {
    setIsFolderModalVisible(false);
    form.resetFields();
  };

  const handleUploadModalCancel = () => {
    setIsUploadModalVisible(false);
  };

  const createItems = [
    {
      key: "folder",
      label: t("drive.createFolder"),
      icon: <FolderAddOutlined />,
      onClick: () => handleCreateAction("folder"),
    },
    {
      key: "file",
      label: t("drive.uploadFile"),
      icon: <FileAddOutlined />,
      onClick: () => handleCreateAction("file"),
    },
  ];

  return (
    <>
      <Dropdown menu={{ items: createItems }} trigger={["click"]}>
        <Button type="primary">
          <Space>
            <PlusOutlined />
            {t("drive.New")}
            <DownOutlined />
          </Space>
        </Button>
      </Dropdown>

      {/* Upload Modal */}
      <Modal
        title={
          <div className="flex items-center gap-2">
            <CloudUploadOutlined className="text-blue-600" />
            <span>{t("drive.uploadFiles")}</span>
          </div>
        }
        open={isUploadModalVisible}
        onCancel={handleUploadModalCancel}
        footer={null}
        width={800}
        destroyOnClose
      >
        <UploadInput
          onUploadSuccess={handleUploadSuccess}
          showDragDropUpload={true}
          className="p-4"
        />
      </Modal>

      {/* Create Folder Modal */}
      <Modal
        title={t("drive.createFolderModal.title")}
        open={isFolderModalVisible}
        onCancel={handleFolderModalCancel}
        footer={null}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateSubmit}
          initialValues={{
            name: "",
          }}
        >
          <Form.Item
            name="name"
            label={t("drive.createFolderModal.folderName")}
            rules={[
              { required: true, message: t("drive.createFolderModal.required") },
              { min: 2, message: t("drive.createFolderModal.tooShort") },
              { max: 30, message: t("drive.createFolderModal.tooLong") },
            ]}
          >
            <Input
              placeholder={t("drive.createFolderModal.placeholder")}
              autoFocus
              suffix={<FolderFilled />}
            />
          </Form.Item>

          <div className="flex justify-end space-x-2">
            <Button onClick={handleFolderModalCancel}>{t("drive.createFolderModal.cancel")}</Button>
            <Button 
              type="primary" 
              htmlType="submit"
              loading={createFolderMutation.isLoading}
            >
              {t("drive.createFolderModal.create")}
            </Button>
          </div>
        </Form>
      </Modal>
    </>
  );
};

export default CreateItemsDropdown;