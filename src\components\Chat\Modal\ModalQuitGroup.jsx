import React, { useCallback, useEffect, useState } from "react";
import ModalConfirm from "./ModalConfirm";
import { useDispatch, useSelector } from "react-redux";
import {
  archiveRoom,
  leaveParticpant,
} from "../../../new-redux/services/chat.services";
import { Select, Skeleton, Typography } from "antd";
import AvatarChat from "../Avatar/AvatarChat";
import { useTranslation } from "react-i18next";
import {
  setChatSelectedConversation,
  setOpenDrawer,
  setOpenQuitGroupModal,
} from "../../../new-redux/actions/chat.actions";
import { toastNotification } from "../../ToastNotification";
import { LogoutOutlined } from "@ant-design/icons";
import { getName } from "../../../pages/layouts/chat/utils/ConversationUtils";
import MainService from "../../../services/main.service";
import { URL_ENV } from "index";

const ModalQuitGroup = ({ item, informations }) => {
  const [t] = useTranslation("common");
  const [newAdminId, setNewAdminId] = useState(null);
  const [errAdminId, setErrdminId] = useState(false);
  const [participants, setSelectedParticipants] = useState([]);
  const [confirmLoading, setConfirmLoading] = useState({
    state: false,
    type: "",
  });
  const dispatch = useDispatch();
  const { selectedParticipants, currentUser, openModalQuitGroup } = useSelector(
    (state) => state.chat
  );
  const selectedConversation = useSelector(
    (state) => state.ChatRealTime.selectedConversation
  );

  const getParticipants = useCallback(async () => {
    try {
      if (
        selectedParticipants.length === 1 &&
        selectedParticipants[0]?._id === currentUser?._id
      )
        return;
      if (
        selectedParticipants?.length > 1 &&
        selectedConversation?.conversationId === item.conversationId &&
        selectedConversation?.type === "room"
      )
        setSelectedParticipants(
          selectedParticipants.filter((el) => el._id !== currentUser?._id)
        );
      else {
        setConfirmLoading({
          state: true,
          type: "participants",
        });
        const roomInfo = await MainService.getInfoRoom(item?.id);
        setSelectedParticipants(
          roomInfo?.data?.room?.participants?.filter(
            (el) => el._id !== currentUser?._id
          )
        );
        setConfirmLoading({
          state: false,
          type: "",
        });
      }
    } catch (err) {
      return;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    item?.conversationId,
    selectedConversation?.conversationId,
    selectedParticipants,
  ]);

  useEffect(() => {
    getParticipants();
  }, [getParticipants]);

  const handleChangeAdminId = (value) => {
    setNewAdminId(value);
    setErrdminId(!value);
  };

  const handleLeaveParticipant = async () => {
    try {
      setConfirmLoading({
        state: true,
        type: "leave",
      });
      if (currentUser._id !== item?.admin_id) {
        await dispatch(
          leaveParticpant({
            room_id: item?.id,
          })
        );

        if (selectedConversation?.id === item?.id)
          dispatch(
            setChatSelectedConversation({
              selectedConversation: null,
            })
          );
        dispatch(setOpenDrawer({ type: "" }));

        toastNotification(
          "success",
          t("chat.message_system.leaveSuccessGroup"),
          "topRight"
        );
      } else {
        if (!newAdminId && participants?.length > 1) {
          setErrdminId(true);

          return;
        }

        if (participants?.length === 0) await dispatch(archiveRoom(item?.id));
        else {
          await dispatch(
            leaveParticpant({
              room_id: item?.id,
              new_admin_id: newAdminId,
            })
          );
        }

        if (selectedConversation?.id === item?.id)
          dispatch(
            setChatSelectedConversation({
              selectedConversation: null,
            })
          );
        dispatch(setOpenDrawer({ type: "" }));

        toastNotification(
          "success",
          participants.length === 1
            ? t("chat.message_system.successDeleteGroup")
            : t("chat.message_system.successChangeQuitAdmin"),
          "topRight"
        );
        setNewAdminId(null);
        dispatch(setOpenQuitGroupModal(false));
      }
      dispatch(setOpenQuitGroupModal(false));
    } catch (err) {
      toastNotification(
        "error",
        t("chat.message_system.successAddMembers"),
        "topRight"
      );
    } finally {
      setConfirmLoading({
        state: false,
        type: "",
      });
    }
  };
  const handleCancelLeaveParticipant = () => {
    dispatch(setOpenQuitGroupModal(false));

    setNewAdminId(null);
    setErrdminId(false);
  };
  return (
    <ModalConfirm
      open={openModalQuitGroup}
      title={
        <div className="flex flex-col ">
          <div
            className="flex items-center"
            style={{
              color: "rgba(0, 0, 0, 0.88)",
              fontWeight: 600,
              fontSize: 16,
            }}>
            <LogoutOutlined className="mr-3" />
            <p>
              {participants?.length === 0
                ? t("chat.room.confirmDelete")
                : t("chat.room.confirmAbandonment")}
            </p>
          </div>
          {item?.name && !informations && (
            <span className="text-sm font-semibold">
              {t("chat.nameGroup")}:
              <Typography.Text mark className="text-sm">
                {item?.name}
              </Typography.Text>
            </span>
          )}
        </div>
      }
      content={
        <div className="w-full">
          {confirmLoading.state && confirmLoading.type === "participants" ? (
            <div className="flex w-full items-center justify-center">
              <Skeleton.Input active block size="large" />
            </div>
          ) : item?.admin_id === currentUser?._id &&
            participants?.length >= 1 ? (
            <>
              <Select
                showSearch
                autoFocus={true}
                placeholder={t("chat.error_message.selectNewAdmin")}
                optionFilterProp="children"
                onChange={handleChangeAdminId}
                value={newAdminId}
                style={{ width: "100%" }}
                disabled={
                  confirmLoading.state && confirmLoading.type === "participants"
                }
                filterOption={(input, option) => {
                  return (
                    option?.label?.props?.children[1]?.props?.children?.[0] ??
                    ""
                  )
                    ?.toLowerCase()
                    ?.includes(input?.toLowerCase());
                }}
                loading={
                  confirmLoading.state && confirmLoading.type === "participants"
                }
                options={participants.map((el) => ({
                  value: el._id,
                  label: (
                    <div className="flex items-center space-x-2">
                      {
                        <AvatarChat
                          fontSize="0.875rem"
                          type="user"
                          url={
                            URL_ENV?.REACT_APP_BASE_URL +
                            URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                            el.image
                          }
                          size={30}
                          height={8}
                          width={8}
                          name={getName(el?.name, "avatar")}
                          hasImage={el.image}
                        />
                      }
                      <span>{el.name} </span>
                    </div>
                  ),
                }))}
              />
              {errAdminId ? (
                <div className="ant-form-item-explain-error text-[#ff4d4f]">
                  {" "}
                  {t("chat.error_message.selectNewAdmin")}
                </div>
              ) : (
                ""
              )}
            </>
          ) : participants?.length === 0 ? (
            t("chat.room.deleteGroup")
          ) : (
            t("chat.room.quitGroup")
          )}
        </div>
      }
      dangerMode={true}
      okText={
        participants?.length === 0
          ? t("chat.deleteGroup")
          : t("chat.valid&quit")
      }
      cancelText={t("form.cancel")}
      onOk={handleLeaveParticipant}
      onCancel={handleCancelLeaveParticipant}
      loading={confirmLoading.state && confirmLoading.type === "leave"}
    />
  );
};

export default ModalQuitGroup;
