import React, { useRef } from "react";
import { Form, Input, Select, Tag, But<PERSON>, Spin } from "antd";
import { useTranslation } from "react-i18next";
import {
  CaretRightOutlined,
  SaveOutlined,
  CloseOutlined,
  MenuOutlined,
  RestOutlined,
} from "@ant-design/icons";
import { DndContext } from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { useSelector } from "react-redux";
import { generateAxios } from "../services/axiosInstance";
import { URL_ENV } from "index";
import { toastNotification } from "./ToastNotification";
import { FiEdit3, FiTrash2 } from "react-icons/fi";
import Confirm from "./GenericModal";

const SortableItem = ({
  item,
  data,
  setData,
  isEditing,
  onEdit,
  onSave,
  onCancel,
  form,
  setIdPipeline,
  departments,
  isSelected,
  onSelect,
  pipeline_id,
  editingKey,
  handleKeyPress,
  dataLength,
  loading,
}) => {
  const [t] = useTranslation("common");
  const { user } = useSelector((state) => state.user);
  const inputRef = useRef(null);

  const {
    attributes,
    listeners,
    setNodeRef,
    setActivatorNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: item.key,
    disabled: isEditing || !item.id || dataLength <= 1 || loading,
  });

  const style = {
    transform: CSS.Transform.toString(
      transform && {
        ...transform,
        scaleY: 1,
      }
    ),
    transition,
    ...(isDragging
      ? {
          position: "relative",
          zIndex: 99,
        }
      : {}),
    borderBottom: "1px solid #dedede",
  };

  const handleClick = (event) => {
    event.stopPropagation();
  };
  const handleDelete = async () => {
    try {
      const res = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).delete(`/pipelines/${item?.id}`);

      setData((prev) => prev.filter((el) => el.id !== item?.id));
      if (Number(pipeline_id) === Number(item?.id)) {
        setIdPipeline("");
      }
      toastNotification(
        "success",
        item?.label + t("toasts.deleted"),
        "topRight"
      );
    } catch (err) {
      if (err?.response?.status === 422) {
        toastNotification(
          "error",
          // t(`toasts.${err?.response?.data?.message}`),,
          err?.response?.data?.message,

          "topRight"
        );
      } else {
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    }
  };
  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      className={`
        group cursor-pointer border-b border-gray-200 px-1  py-2.5 transition-all duration-200 last:border-b-0
        ${
          isSelected
            ? "border-l-4 border-l-blue-500 bg-blue-50"
            : "bg-white hover:bg-gray-50"
        }
        ${isDragging ? "bg-gray-100 shadow-lg" : ""}
      `}
      onClick={() => !isEditing && !loading && onSelect(item)}
    >
      <div className="flex items-baseline gap-1">
        {/* Drag Handle à gauche */}
        <div className=" flex flex-col gap-1">
          {!isEditing &&
            item.id &&
            dataLength > 1 &&
            (user?.role === "SuperAdmin" || user?.role === "Admin") && (
              <div className="mr-1 flex items-center gap-x-1">
                <div
                  ref={setActivatorNodeRef}
                  {...listeners}
                  className=" cursor-move rounded p-1 hover:bg-gray-200 "
                  // opacity-0 group-hover:opacity-100 transition-opacity
                >
                  <MenuOutlined className="text-sm text-gray-400 hover:text-gray-600" />
                </div>
                <Button
                  type="text"
                  size="small"
                  shape="circle"
                  danger
                  icon={<FiTrash2 />}
                  disabled={
                    item?.is_used == 1 ||
                    item?.default == 1 ||
                    item?.seeder == 1 ||
                    item?.default == 2 ||
                    item?.system == 1 ||
                    item?.editable == 0
                      ? true
                      : false
                  }
                  onClick={(e) => {
                    e.stopPropagation();

                    item?.id
                      ? Confirm(
                          ` ${t("table.delete")} "${
                            item.name?.replace(/<\/?[^>]+(>|$)/g, "") ||
                            item.label_fr?.replace(/<\/?[^>]+(>|$)/g, "") ||
                            item.label?.replace(/<\/?[^>]+(>|$)/g, "") ||
                            item.Bank?.replace(/<\/?[^>]+(>|$)/g, "") ||
                            item.title?.replace(/<\/?[^>]+(>|$)/g, "") ||
                            item?.listElementValue?.replace(
                              /<\/?[^>]+(>|$)/g,
                              ""
                            )
                          }" !`,
                          "Confirm",
                          <RestOutlined style={{ color: "red" }} />,
                          function func() {
                            return handleDelete(
                              item.id == undefined
                                ? item.key
                                : item.id.toString() || item.key
                            );
                          },
                          true,
                          <span className="text-xs">
                            {t(`beforeDeleted.pipelines`)}
                          </span>
                        )
                      : setData(data.filter((rec) => rec.key !== item.key));
                  }}
                />

                <Button
                  type="link"
                  size="small"
                  icon={<FiEdit3 />}
                  onClick={(e) => {
                    e.stopPropagation();
                    if (!loading) onEdit(item);
                  }}
                  disabled={loading}
                  className="mr-1 "
                  // className="opacity-0 transition-opacity group-hover:opacity-100"
                />
              </div>
            )}
          {!isEditing && (
            <span className=" text-[0.72rem] font-semibold uppercase leading-[1rem] tracking-[0.05em] text-[#64748b] opacity-100">
              {t("table.header.department")}:{" "}
            </span>
          )}
        </div>
        <div className="min-w-0 flex-1">
          {isEditing ? (
            <Form
              form={form}
              onFinish={onSave}
              layout="vertical"
              className="space-y-2"
            >
              <div className="flex justify-end space-x-2">
                <Button
                  type="primary"
                  size="small"
                  icon={<SaveOutlined />}
                  htmlType="submit"
                  loading={loading}
                >
                  {t("form.save")}
                </Button>
                <Button
                  size="small"
                  icon={<CloseOutlined />}
                  onClick={() => onCancel(item)}
                  disabled={loading}
                >
                  {t("form.cancel")}
                </Button>
              </div>
              <Form.Item
                name="label"
                className="mb-2"
                rules={[
                  {
                    required: true,
                    message: `${
                      t("pipeline.pipelineName") +
                      " " +
                      t("table.header.isrequired")
                    }`,
                  },
                ]}
              >
                <Input
                  ref={inputRef}
                  placeholder={t("pipeline.pipelineName")}
                  onKeyPress={handleKeyPress}
                  onClick={handleClick}
                  autoFocus
                  className="font-semibold"
                />
              </Form.Item>
              <Form.Item name="department" className="mb-2">
                <Select
                  placeholder={t("services.selectdepartment")}
                  mode="multiple"
                  options={departments}
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label?.toLowerCase() ?? "").includes(
                      input.toLowerCase()
                    )
                  }
                  onClick={handleClick}
                />
              </Form.Item>
            </Form>
          ) : (
            <>
              <div className="flex items-start justify-between">
                <div className="min-w-0 flex-1">
                  <h4 className="mb-1 truncate text-base font-semibold text-gray-900">
                    {item.label}
                  </h4>
                  <div className="mt-1 flex flex-wrap gap-1">
                    {item.departments?.map((dept) => (
                      <Tag
                        key={dept.id}
                        className="text-xs"
                        color={dept?.color}
                      >
                        {dept.label}
                      </Tag>
                    ))}
                  </div>
                </div>
                <div className="ml-2 flex items-center space-x-2">
                  {item.id === pipeline_id && (
                    <CaretRightOutlined className="text-lg text-blue-500" />
                  )}
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

const PipelineSidebar = ({
  data,
  setData,
  setIdPipeline,
  loading,
  save,
  edit,
  cancel,
  form,
  isEditing,
  editingKey,
  departments,
  pipeline_id,
  onSelectChange,
  selectedRowKey,
  handleKeyPress,
  apiRank,
  setLoading,
}) => {
  const [t] = useTranslation("common");

  const handleSelect = (item) => {
    if (item.id) {
      onSelectChange([item.key], [item]);
    }
  };

  const onDragEnd = async ({ active, over }) => {
    if (active?.id && active?.id !== over?.id) {
      const newData = arrayMove(
        data,
        data.findIndex((i) => i.key === active.id),
        data.findIndex((i) => i.key === over?.id)
      );

      setData(newData);

      // Update ranks on server
      if (apiRank) {
        try {
          const formData = new FormData();
          newData &&
            newData.forEach((item, i) =>
              formData.append(`rank[${item?.id}]`, i + 1)
            );
          setLoading(true);

          await generateAxios(
            URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
          ).post(apiRank, formData);
        } catch (error) {
          console.error("Error updating ranks:", error);
          toastNotification("error", t("toasts.rankFailed"), "topRight");
        } finally {
          setLoading(false);
        }
      }
    }
  };

  return (
    <div className=" w-full">
      <Spin spinning={loading} tip={t("drive.loading")}>
        <div className="min-h-[80px]">
          <DndContext onDragEnd={onDragEnd}>
            <SortableContext
              items={data.map((i) => i.key)}
              strategy={verticalListSortingStrategy}
            >
              <div className="overflow-hidden rounded-lg border  border-gray-200 bg-white">
                {data.map((item) => (
                  <SortableItem
                    key={item.key}
                    item={item}
                    isEditing={isEditing(item)}
                    onEdit={edit}
                    onSave={save}
                    onCancel={cancel}
                    form={form}
                    departments={departments}
                    isSelected={selectedRowKey === item.key}
                    onSelect={handleSelect}
                    pipeline_id={pipeline_id}
                    editingKey={editingKey}
                    handleKeyPress={handleKeyPress}
                    dataLength={data.length}
                    loading={loading}
                    data={data}
                    setData={setData}
                    setIdPipeline={setIdPipeline}
                  />
                ))}
              </div>
            </SortableContext>
          </DndContext>
        </div>
      </Spin>
    </div>
  );
};

export default PipelineSidebar;
