import React, { useRef } from "react";
import { Form, Input, Select, Tag, Button } from "antd";
import { useTranslation } from "react-i18next";
import {
  CaretRightOutlined,
  EditOutlined,
  SaveOutlined,
  CloseOutlined,
  MenuOutlined,
} from "@ant-design/icons";
import { DndContext } from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { useSelector } from "react-redux";
import { generateAxios } from "../services/axiosInstance";
import { URL_ENV } from "index";
import { toastNotification } from "./ToastNotification";

const SortableItem = ({
  item,
  isEditing,
  onEdit,
  onSave,
  onCancel,
  form,
  departments,
  isSelected,
  onSelect,
  pipeline_id,
  editingKey,
  handleKeyPress,
}) => {
  const [t] = useTranslation("common");
  const { user } = useSelector((state) => state.user);
  const inputRef = useRef(null);

  const {
    attributes,
    listeners,
    setNodeRef,
    setActivatorNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: item.key,
    disabled: isEditing || !item.id,
  });

  const style = {
    transform: CSS.Transform.toString(
      transform && {
        ...transform,
        scaleY: 1,
      }
    ),
    transition,
    ...(isDragging
      ? {
          position: "relative",
          zIndex: 99,
        }
      : {}),
  };

  const handleClick = (event) => {
    event.stopPropagation();
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      className={`
        group mb-3 cursor-pointer rounded-lg border p-4 transition-all duration-200
        ${
          isSelected
            ? "border-blue-400 bg-blue-50 shadow-md"
            : "border-gray-200 bg-white"
        }
        ${
          isEditing
            ? "border-blue-500 shadow-lg"
            : "hover:border-gray-300 hover:shadow-md"
        }
        ${isDragging ? "bg-gray-50 shadow-xl" : "shadow-sm"}
      `}
      onClick={() => !isEditing && onSelect(item)}
    >
      <div className="flex items-start justify-between">
        <div className="min-w-0 flex-1">
          {isEditing ? (
            <Form
              form={form}
              onFinish={onSave}
              layout="vertical"
              className="space-y-2"
            >
              <Form.Item
                name="label"
                className="mb-2"
                rules={[
                  {
                    required: true,
                    message: `${
                      t("pipeline.pipelineName") +
                      " " +
                      t("table.header.isrequired")
                    }`,
                  },
                ]}
              >
                <Input
                  ref={inputRef}
                  placeholder={t("pipeline.pipelineName")}
                  onKeyPress={handleKeyPress}
                  onClick={handleClick}
                  autoFocus
                  className="font-semibold"
                />
              </Form.Item>
              <Form.Item name="department" className="mb-2">
                <Select
                  placeholder={t("services.selectdepartment")}
                  mode="multiple"
                  options={departments}
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label?.toLowerCase() ?? "").includes(
                      input.toLowerCase()
                    )
                  }
                  onClick={handleClick}
                />
              </Form.Item>
              <div className="flex justify-end space-x-2">
                <Button
                  type="primary"
                  size="small"
                  icon={<SaveOutlined />}
                  htmlType="submit"
                >
                  {t("common.save")}
                </Button>
                <Button
                  size="small"
                  icon={<CloseOutlined />}
                  onClick={() => onCancel(item)}
                >
                  {t("common.cancel")}
                </Button>
              </div>
            </Form>
          ) : (
            <>
              <div className="mb-2 flex items-start justify-between">
                <div className="min-w-0 flex-1">
                  <h4 className="mb-1 truncate text-base font-semibold text-gray-900">
                    {item.label}
                  </h4>
                  <div className="flex flex-wrap gap-1">
                    {item.departments?.map((dept) => (
                      <Tag key={dept.id} className="text-xs" color="blue">
                        {dept.label}
                      </Tag>
                    ))}
                  </div>
                </div>
                <div className="ml-2 flex items-center space-x-2">
                  {(user?.role === "SuperAdmin" || user?.role === "Admin") && (
                    <Button
                      type="text"
                      size="small"
                      icon={<EditOutlined />}
                      onClick={(e) => {
                        e.stopPropagation();
                        onEdit(item);
                      }}
                      className="opacity-0 transition-opacity group-hover:opacity-100"
                    />
                  )}
                  {item.id === pipeline_id && (
                    <CaretRightOutlined className="text-lg text-blue-500" />
                  )}
                </div>
              </div>
            </>
          )}
        </div>
        {!isEditing &&
          item.id &&
          (user?.role === "SuperAdmin" || user?.role === "Admin") && (
            <div
              ref={setActivatorNodeRef}
              {...listeners}
              className="ml-2 cursor-move rounded p-1 opacity-0 transition-opacity hover:bg-gray-100 group-hover:opacity-100"
            >
              <MenuOutlined className="text-sm text-gray-400 hover:text-gray-600" />
            </div>
          )}
      </div>
    </div>
  );
};

const PipelineSidebar = ({
  data,
  setData,
  loading,
  save,
  edit,
  cancel,
  form,
  isEditing,
  editingKey,
  departments,
  pipeline_id,
  onSelectChange,
  selectedRowKey,
  handleKeyPress,
  apiRank,
  setLoading,
}) => {
  const [t] = useTranslation("common");

  const handleSelect = (item) => {
    if (item.id) {
      onSelectChange([item.key], [item]);
    }
  };

  const onDragEnd = async ({ active, over }) => {
    if (active?.id && active?.id !== over?.id) {
      const newData = arrayMove(
        data,
        data.findIndex((i) => i.key === active.id),
        data.findIndex((i) => i.key === over?.id)
      );

      setData(newData);

      // Update ranks on server
      if (apiRank) {
        try {
          setLoading(true);
          const ranksToUpdate = newData
            .filter((item) => item.id) // Only items with IDs
            .map((item, index) => ({
              id: item.id,
              rank: index + 1,
            }));

          const config = {
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
            },
          };

          await generateAxios(
            URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
          ).put(apiRank, { data: ranksToUpdate }, config);

          toastNotification("success", t("toasts.rankUpdated"), "topRight");
        } catch (error) {
          console.error("Error updating ranks:", error);
          toastNotification("error", t("toasts.somethingWrong"), "topRight");
        } finally {
          setLoading(false);
        }
      }
    }
  };

  return (
    <div className="w-full">
      <DndContext onDragEnd={onDragEnd}>
        <SortableContext
          items={data.map((i) => i.key)}
          strategy={verticalListSortingStrategy}
        >
          <div className="space-y-2">
            {data.map((item) => (
              <SortableItem
                key={item.key}
                item={item}
                isEditing={isEditing(item)}
                onEdit={edit}
                onSave={save}
                onCancel={cancel}
                form={form}
                departments={departments}
                isSelected={selectedRowKey === item.key}
                onSelect={handleSelect}
                pipeline_id={pipeline_id}
                editingKey={editingKey}
                handleKeyPress={handleKeyPress}
              />
            ))}
          </div>
        </SortableContext>
      </DndContext>
    </div>
  );
};

export default PipelineSidebar;
