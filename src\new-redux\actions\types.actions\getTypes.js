import { GET_TYPES_SUCCESS, GET_TYPES_ERROR } from "../../constants";
import MainService from "../../../services/main.service";

export const getTypes = () => async (dispatch) => {
  try {
    const response = await MainService.getFieldTypes();
    dispatch({
      type: GET_TYPES_SUCCESS,
      payload: response?.data?.data,
    });
  } catch (error) {
    dispatch({
      type: GET_TYPES_ERROR,
      payload: error,
    });
  }
};
