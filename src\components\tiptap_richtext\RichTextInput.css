.textEditor {
  width: 100%; /* Set the desired width for the container */
  overflow-wrap: break-word; /* Enable word wrapping */
}

/* TitTap Editor Design */
.ProseMirror {
  padding: 10px;
  background: white;
  border-radius: 30px;
  border: 1px solid #d9d9d9;
  max-height: 200px;
  overflow: auto;
}
.ProseMirror-textarea {
  padding: 10px;
  background: white;
  border-radius: 5px;
  border: 1px solid #d9d9d9;
  max-height: 200px;
  height: 100px;
  overflow: auto;
}
.ProseMirror-chat-update-message {
  padding: 10px;
  background: white;
  border-radius: 5px;
  border: 1px solid #d9d9d9;
  max-height: 200px;
  overflow: auto;
  margin-right: 8px;
}

.ProseMirror-note-update {
  padding: 10px;
  background: white;
  border-radius: 5px;
  border: 1px solid #d9d9d9;
  max-height: 200px;
  overflow: auto;
}

.ProseMirror-notes {
  padding: 10px;
  background: white;
  border-radius: 5px;
  border: 1px solid #d9d9d9;
  /* max-height: 300px; */
  height: 120px;
  overflow: auto;
}

.Prosemirror-dropzone {
  padding: 10px;
  background: white;
  border-radius: 10px;
  border: 1px dashed #4373e6;
  height: 80px;
  overflow: auto;
}
.ProseMirror:focus {
  border: 1px solid #d9d9d9;
  outline: #d9d9d9;
}

.ProseMirror-textarea p {
  max-width: calc(100% - 100px);
  /* display: inline-block; */
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: normal;
  line-break: strict;
  hyphens: none;
  -webkit-hyphens: none;
  -moz-hyphens: none;
  /* padding-left: 5px; */
}

.ProseMirror p {
  max-width: calc(100% - 140px);
  /* display: inline-block; */
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: normal;
  line-break: strict;
  hyphens: none;
  -webkit-hyphens: none;
  -moz-hyphens: none;
  padding-left: 10px;
}
.ProseMirror p > p * {
  word-spacing: normal;
}

.ProseMirror > * + * {
  margin-top: 0.75em;
}

.ProseMirror ul,
.ProseMirror ol {
  padding: 0 2rem;
}

.ProseMirror h1,
.ProseMirror h2,
.ProseMirror h3,
.ProseMirror h4,
.ProseMirror h5,
.ProseMirror h6 {
  line-height: 1.1;
}

.ProseMirror code {
  background-color: rgba(#616161, 0.1);
  color: #616161;
}

.ProseMirror pre {
  background: #0d0d0d;
  color: #fff;
  font-family: "JetBrainsMono", monospace;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
}
.ProseMirror code {
  color: inherit;
  padding: 0;
  background: none;
  font-size: 0.8rem;
}

.ProseMirror img {
  max-width: 100%;
  height: auto;
}

.ProseMirror blockquote {
  padding-left: 1rem;
  border-left: 3px solid #999999;
}

.ProseMirror hr {
  border: none;
  border-top: 3px solid #999999;
  margin: 2rem 0;
}

/* Placeholder (at the top) */
.ProseMirror p.is-editor-empty:first-child::before {
  color: #adb5bd;
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}

/* ............................ */

.menuBarSourceNotes {
  height: auto;
  display: flex;
  /* position: absolute; */
  align-items: center;
  flex-direction: row;
  /* justify-content: space-between; */
  /* top: -56px; */
  border-radius: 5px;
  width: auto;
  background: #fff;
  margin-top: 5px;
  margin-bottom: 5px;
  /* box-shadow: 0px 3px 14px 1px rgba(166, 164, 164, 0.75);
  -webkit-box-shadow: 0px 3px 14px 1px rgba(166, 164, 164, 0.75);
  -moz-box-shadow: 0px 3px 14px 1px rgba(166, 164, 164, 0.75); */
}

.menuBarSourceNotes button {
  font-size: 18px;
  margin: 7px;
  /* margin-right: 15px; */
  outline: none;
  border: none;
  background: none;
  color: rgb(70, 70, 70);
  cursor: pointer;
}
.menuBarSourceNotes button:last-child {
  margin-right: 7px;
}

.menuBar {
  height: auto;
  display: flex;
  position: absolute;
  align-items: center;
  flex-direction: row;
  /* justify-content: space-between; */
  top: -56px;
  border-radius: 5px;
  width: auto;
  background: #fff;
  /* box-shadow: 0px 3px 14px 1px rgba(166, 164, 164, 0.75);
  -webkit-box-shadow: 0px 3px 14px 1px rgba(166, 164, 164, 0.75);
  -moz-box-shadow: 0px 3px 14px 1px rgba(166, 164, 164, 0.75); */
}
/* .menuBar section + section::before {
  content: "|";
  padding: 15px;
  height: 100%;
} */
.menuBar button {
  font-size: 18px;
  margin: 7px;
  /* margin-right: 15px; */
  outline: none;
  border: none;
  background: none;
  color: rgb(70, 70, 70);
  cursor: pointer;
}
.menuBar button:last-child {
  margin-right: 7px;
}

.heading3 {
  /* font-size: 15px; */
}

button.is_active {
  background: rgb(197, 197, 197, 0.4);
  padding: 2px 3px;
  border-radius: 2px;
}

.tippy-box {
  background-color: #fff !important;
  border-radius: 6px !important;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05), 0px 10px 20px rgba(0, 0, 0, 0.1);
  max-height: 250px;
  overflow-y: auto;
}

.tippy-content {
  padding: 0 !important;
}

.tippy-arrow {
  color: #fff !important;
}

.items {
  border-radius: 6px;
  color: rgba(0, 0, 0, 0.8);
  font-size: 0.9rem;
  position: relative;
  min-height: 0;
}

.item {
  background: transparent;
  border: 1px solid transparent;
  border-radius: 0.4rem;
  display: block;
  margin: 0;
  padding: 0.2rem 0.4rem;
  text-align: left;
  width: 100%;
}
.item:hover {
  background-color: rgb(226 232 240);
  border-radius: 8px;
  cursor: pointer;
}
.item.is-selected {
  background-color: rgb(226 232 240);
  border-radius: 8px;
}
.image-from-chat {
  display: none;
}
ul[data-type="taskList"] {
  list-style: none;
  margin-left: 0;
  padding: 0;
}

ul[data-type="taskList"] li {
  align-items: flex-start;
  display: flex;
}

ul[data-type="taskList"] li > label {
  flex: 0 0 auto;
  margin-right: 0.5rem;
  user-select: none;
}

ul[data-type="taskList"] li > div {
  flex: 1 1 auto;
}

ul[data-type="taskList"] li > div > p {
  padding-left: 0;
}

ul[data-type="taskList"] input[type="checkbox"] {
  cursor: pointer;
}

ul[data-type="taskList"] ul[data-type="taskList"] {
  margin: 0;
}

.disable-editor {
  @apply opacity-50 cursor-wait pointer-events-none
}