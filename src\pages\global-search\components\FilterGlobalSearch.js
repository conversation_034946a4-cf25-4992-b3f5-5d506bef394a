import { Space, Tag, Tooltip } from "antd";
import { isGuestConnected } from "utils/role";

const FilterGlobalSearch = ({
  selectedFilter,
  setSelectedFilter,
  setPage,
  t,
}) => {
  //
  const isGuest = isGuestConnected();
  //
  const filterWords = [
    { label: "All", key: "all" },
    ...(!isGuest ? [{ label: "Emails", key: "email" }] : []),
    { label: t("menu1.tasks"), key: "activity" },
    { label: "Visio", key: "visio" },
    { label: "Notes", key: "note" },
    { label: "Drive", key: "drive" },
    { label: t("globalSearch.other"), key: "familyValue" },
  ];
  //
  const handleChange = (tag, checked) => {
    if (tag === "all") {
      if (checked) {
        setSelectedFilter([]);
      }
    } else {
      setSelectedFilter((prev) => {
        if (checked) {
          return prev.length === filterWords.length - 2 ? [] : [...prev, tag];
        } else {
          return prev.filter((filter) => filter !== tag);
        }
      });
    }
    setPage(1);
  };
  //
  return (
    <Space size={0}>
      {filterWords.map(({ label, key }) => (
        <Tooltip
          key={`filterWords_${key}`}
          title={
            key === "familyValue"
              ? `${t("menu1.contacts")}, ${t("menu2.colleagues")}, ${t(
                  "menu1.deals"
                )}, ${t("menu1.projects")}...`
              : // ? "Contacts, Colleagues, Deals, Project..."
                null
          }
        >
          <Tag.CheckableTag
            key={key}
            checked={
              key === "all"
                ? !selectedFilter.length
                : selectedFilter.includes(key)
            }
            onChange={(checked) => handleChange(key, checked)}
            style={{ fontSize: 12, fontWeight: 600 }}
          >
            {label}
          </Tag.CheckableTag>
        </Tooltip>
      ))}
    </Space>
  );
};

export default FilterGlobalSearch;
