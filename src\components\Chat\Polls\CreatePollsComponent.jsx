import React, { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector, useDispatch } from "react-redux";
import { Button, DatePicker, Form, Input, Switch, Tooltip } from "antd";
import {
  MinusCircleOutlined,
  PlusOutlined,
  QuestionCircleOutlined,
} from "@ant-design/icons";
import ModalConfirm from "../Modal/ModalConfirm";

import { useSendMessage } from "../../../pages/layouts/chat/hooks/useSendMessage";
import { scrollToBottom } from "../../../new-redux/actions/chat.actions/Input";
import { dayjs_timezone } from "../../../App";

const formItemLayout = {
  labelCol: {
    xs: {
      span: 24,
    },
    sm: {
      span: 4,
    },
  },
  wrapperCol: {
    xs: {
      span: 24,
    },
    sm: {
      span: 20,
    },
  },
};
const formItemLayoutWithOutLabel = {
  wrapperCol: {
    xs: {
      span: 24,
      offset: 0,
    },
    sm: {
      span: 20,
      offset: 4,
    },
  },
};
function CreatePollsComponent({ open, setOpen }) {
  const { t, i18n } = useTranslation("common");
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const selectedConversation = useSelector(
    (state) => state.ChatRealTime.selectedConversation
  );
  const {
    mutate: handleActionMessage,
    isLoading,
    isSuccess,
    isError,
  } = useSendMessage("create_polls");
  const [showDate, setShowDate] = useState(false);
  const handleCancel = useCallback(() => {
    setOpen(false);
    form.resetFields();
  }, [form]);

  useEffect(() => {
    if (isSuccess && !isError && !isLoading) {
      handleCancel();
      dispatch(scrollToBottom(Math.floor(Math.random() * 1000000 + 1)));
    }
  }, [isSuccess, isError, isLoading, handleCancel]);
  return (
    <ModalConfirm
      width={600}
      open={open}
      title={
        <span className="ant-modal-confirm-title flex items-center space-x-2">
          {t("chat.polls.title_modal")}
        </span>
      }
      content={
        <div className=" ant-modal-confirm-content">
          <div className="flex flex-col">
            <Form
              labelCol={{
                flex: "1 1 50px",
              }}
              wrapperCol={{
                flex: "0 1 300px",
              }}
              labelAlign="left"
              className="w-full"
              // labelWrap
              layout="horizontal"
              form={form}
              name="create_poll"
              initialValues={{
                private: true,
                multi_answer: true,
                options: ["", ""],
                expire_At: dayjs_timezone().add(1, "day"),
              }}
              autoComplete="off"
            >
              <Form.Item
                label={t("chat.polls.question")}
                name="question"
                {...formItemLayout}
                rules={[
                  {
                    required: true,
                    max: 150,
                    message: t("chat.polls.control_question") + " !",
                  },
                ]}
              >
                <Input
                  className="flex "
                  maxLength={100}
                  size="middle"
                  placeholder={t("chat.polls.question_placeholder")}
                />
              </Form.Item>
              <Form.List
                name="options"
                rules={[
                  {
                    required: true,
                    validator: async (_, names) => {
                      const cleaned = (names || [])
                        .map((n) => (n || "").trim())
                        .filter(Boolean);
                      const unique = [...new Set(cleaned)];

                      if (cleaned.length < 2) {
                        return Promise.reject(
                          new Error(t("chat.polls.min_option", { count: 2 }))
                        );
                      }

                      if (cleaned.length !== unique.length) {
                        return Promise.reject(
                          new Error(t("chat.polls.unique_option"))
                        );
                      }

                      if (cleaned.length > 5) {
                        return Promise.reject(
                          new Error(t("chat.polls.max_option"))
                        );
                      }

                      return Promise.resolve();
                    },
                  },
                ]}
              >
                {(fields, { add, remove }, { errors }) => (
                  <>
                    {fields.map((field, index) => (
                      <Form.Item
                        {...(index === 0
                          ? formItemLayout
                          : formItemLayoutWithOutLabel)}
                        label={index === 0 ? "Options" : ""}
                        required={true}
                        key={field.key}
                      >
                        <Form.Item
                          {...field}
                          validateTrigger={["onChange", "onBlur"]}
                          // rules={[
                          //   {
                          //     required: true,
                          //     whitespace: true,
                          //     message:
                          //       "Please input passenger's name or delete this field.",
                          //   },
                          // ]}
                          noStyle
                        >
                          <Input
                            maxLength={100}
                            style={{
                              width: "95%",
                            }}
                            onChange={(e) =>
                              e.target.value.trim().length > 0 &&
                              index === fields.length - 1 &&
                              fields.length < 5
                                ? add()
                                : null
                            }
                            placeholder={
                              t("chat.polls.option_placeholder") +
                              ` ${index + 1}`
                            }
                          />
                        </Form.Item>
                        {fields.length > 1 && index > 1 ? (
                          <MinusCircleOutlined
                            className="dynamic-delete-button ml-1"
                            onClick={() => remove(field.name)}
                          />
                        ) : null}
                      </Form.Item>
                    ))}

                    <Form.Item
                      label={fields.length === 0 ? t("chat.polls.Options") : ""}
                      {...formItemLayoutWithOutLabel}
                    >
                      <Form.ErrorList errors={errors} />
                      {fields.length < 5 && (
                        <Button
                          type="dashed"
                          onClick={() => add()}
                          style={{
                            width: "100%",
                          }}
                          icon={<PlusOutlined />}
                        >
                          {t("chat.polls.add_option")}
                        </Button>
                      )}
                    </Form.Item>
                  </>
                )}
              </Form.List>
              <Form.Item
                valuePropName="checked"
                name="multi_answer"
                label={
                  <span className=" whitespace-normal">
                    {t("chat.polls.allow_multiple_answers")}
                  </span>
                }
              >
                <Switch
                  defaultChecked
                  checkedChildren={t("chat.polls.Yes")}
                  unCheckedChildren={t("chat.polls.No")}
                />
              </Form.Item>
              <Form.Item
                valuePropName="checked"
                colon={false}
                name="private"
                label={
                  <div className=" flex items-center space-x-1">
                    <span>{t("chat.polls.private")}</span>
                    <Tooltip title={t("chat.polls.public_tooltip")}>
                      <QuestionCircleOutlined />
                    </Tooltip>
                    <span>:</span>
                  </div>
                }
              >
                <Switch
                  defaultChecked
                  checkedChildren={t("chat.polls.Yes")}
                  unCheckedChildren={t("chat.polls.No")}
                />
              </Form.Item>

              <Form.Item
                className="  inline-block   w-full  "
                tooltip={t("chat.polls.limit_time_tooltip")}
                label={t("chat.polls.expire_At")}
              >
                <div className=" flex w-full items-center gap-x-5">
                  <Switch
                    className="mb-2"
                    checkedChildren={t("chat.polls.Yes")}
                    unCheckedChildren={t("chat.polls.No")}
                    onChange={(e) => {
                      setShowDate(e);
                    }}
                  />
                  <Form.Item className="flex flex-1" name="expire_At">
                    {showDate && (
                      <DatePicker
                        disabledDate={(current) =>
                          current &&
                          current <
                            dayjs_timezone().subtract(1, "day").endOf("day")
                        }
                        value={dayjs_timezone().add(1, "day")}
                        locale={
                          i18n.language === "en"
                            ? import("antd/locale/en_GB")
                            : import("antd/locale/fr_FR")
                        }
                      />
                    )}
                  </Form.Item>
                </div>
              </Form.Item>
            </Form>
          </div>
        </div>
      }
      dangerMode={false}
      okText={t("form.create")}
      cancelText={t("form.cancel")}
      onOk={() => {
        form
          .validateFields()
          .then((values) => {
            handleActionMessage({
              message_id: null,

              params: {
                message: values.question.trim(),
                file: [],
                taggedPerson: "",
                from: false,
                main_message: null,
                selectedConversation,
                values: {
                  ...values,
                  expire_At:
                    !showDate || !values.expire_At
                      ? ""
                      : dayjs_timezone(values.expire_At).endOf("day").format(),
                },
              },
              type_conversation: selectedConversation?.type,
              type_action: "create_polls",
            });
          })
          .catch((info) => {
            console.log("Validate Failed:", info);
          });
      }}
      onCancel={handleCancel}
      loading={isLoading}
    />
  );
}

export default CreatePollsComponent;
