/* import { useEffect, useState } from "react";
import PropTypes from "prop-types";
import useNetwork from "custom-hooks/useNetwork";
import { useTranslation } from "react-i18next";

function useTimer(starterDate, isClicked = false) {
  const { t } = useTranslation("common");
  const [timer, setTimer] = useState("--:--");
  const { isOnline } = useNetwork();
  const clearAll = (interval) => {
    setTimer("");
    clearInterval(interval);
  };
  useEffect(() => {
    var interval;
    if (isClicked || !starterDate) {
      clearAll(interval);
    }
    if (!starterDate || !isOnline) return;

    const comparedDate = starterDate || new Date().getTime();

    interval = setInterval(function () {
      const now = new Date().getTime();
      const elapsedTime = now - comparedDate;
      const minutes = Math.floor(
        (elapsedTime % (1000 * 60 * 60)) / (1000 * 60)
      );
      const seconds = Math.floor((elapsedTime % (1000 * 60)) / 1000);
      setTimer(
        (minutes > 9 ? "" : "0") +
          minutes +
          ":" +
          (seconds > 9 ? "" : "0") +
          seconds
      );
    }, 1000);

    return () => clearAll(interval);
  }, [starterDate, isClicked, isOnline]);
  useEffect(() => {
    if (!isOnline) {
      setTimer(t("webphone.reconnect"));
    }
  }, [isOnline]);
  return timer;
}

useTimer.propTypes = {
  starterDate: PropTypes.any,
  isClicked: PropTypes.bool,
};

export default useTimer;
 */
import { useEffect, useState } from "react";
import PropTypes from "prop-types";
import useNetwork from "custom-hooks/useNetwork";
import { useTranslation } from "react-i18next";

function useTimer(starterDate, remainingTimeInSeconds = 0, isClicked = false) {
  const { t } = useTranslation("common");
  const [timer, setTimer] = useState("--:--");
  const { isOnline } = useNetwork();

  const clearAll = (interval) => {
    setTimer("");
    clearInterval(interval);
  };

  useEffect(() => {
    let interval;

    if (
      isClicked ||
      (!starterDate && !remainingTimeInSeconds) ||
      isOnline === false
    ) {
      clearAll(interval);
    }

    // Return if no valid date is provided or if offline
    if ((!starterDate && !remainingTimeInSeconds) || !isOnline) return;

    // Handle Count Up (starterDate) or Count Down (startDate)
    if (starterDate) {
      // Count Up: elapsed time since starterDate
      const comparedDate = new Date(starterDate).getTime();

      if (isNaN(comparedDate)) {
        setTimer("Invalid starter date");
        return;
      }

      interval = setInterval(() => {
        const now = new Date().getTime();
        const elapsedTime = now - comparedDate;
        const minutes = Math.floor(
          (elapsedTime % (1000 * 60 * 60)) / (1000 * 60)
        );
        const seconds = Math.floor((elapsedTime % (1000 * 60)) / 1000);

        setTimer(
          (minutes > 9 ? "" : "0") +
            minutes +
            ":" +
            (seconds > 9 ? "" : "0") +
            seconds
        );
      }, 1000);
    }

    // Handle two cases for remainingTimeInSeconds:
    if (typeof remainingTimeInSeconds === "number") {
      // Case 1: Countdown with remaining time in seconds
      let remainingTime = remainingTimeInSeconds;

      interval = setInterval(() => {
        if (remainingTime <= 0) {
          setTimer("00:00");
          clearInterval(interval); // Stop countdown when it reaches zero
          return;
        }

        remainingTime -= 1; // Decrease the remaining time by 1 second

        const hours = Math.floor(remainingTime / 3600);
        const minutes = Math.floor((remainingTime % 3600) / 60);
        const seconds = remainingTime % 60;

        setTimer(
          (hours > 9 ? "" : "0") +
            hours +
            ":" +
            (minutes > 9 ? "" : "0") +
            minutes +
            ":" +
            (seconds > 9 ? "" : "0") +
            seconds
        );
      }, 1000);
    } else if (typeof remainingTimeInSeconds === "string") {
      // Count Down: time remaining to startDate
      const targetDate = new Date(remainingTimeInSeconds).getTime();
     // console.log("targetDate (countdown):", targetDate); // Debugging line

      if (isNaN(targetDate)) {
        setTimer("Invalid start date");
        return;
      }

      // Prevent the countdown if the targetDate is too close to the current time
      const now = new Date().getTime();
      const remainingTime = targetDate - now;
      if (remainingTime <= 0) {
        setTimer("00:00");
        clearInterval(interval);
        return;
      }

      // Only start the interval if the remaining time is greater than a small margin (e.g., 5 seconds)
      if (remainingTime > 5000) {
        // 5 seconds buffer before starting countdown
        interval = setInterval(() => {
          const now = new Date().getTime();
          const remainingTime = targetDate - now;
         // console.log("remainingTime:", remainingTime); // Debugging line

          if (remainingTime <= 0) {
            // If time is up, show 00:00 and stop the interval
            setTimer("00:00");
            clearInterval(interval);
            return;
          }

          const hours = Math.floor(remainingTime / (1000 * 60 * 60));
          const minutes = Math.floor(
            (remainingTime % (1000 * 60 * 60)) / (1000 * 60)
          );
          const seconds = Math.floor((remainingTime % (1000 * 60)) / 1000);

          setTimer(
            (hours > 9 ? "" : "0") +
              hours +
              ":" +
              (minutes > 9 ? "" : "0") +
              minutes +
              ":" +
              (seconds > 9 ? "" : "0") +
              seconds
          );
        }, 1000);
      } else {
        setTimer("Too close to start date"); // Prevent countdown if start date is too close
      }
    }

    // Cleanup on unmount or dependency change
    return () => clearAll(interval);
  }, [starterDate, remainingTimeInSeconds, isClicked, isOnline, t]); // Dependency array includes both dates and other states

  useEffect(() => {
    if (!isOnline) {
      setTimer(t("webphone.reconnect"));
    }
  }, [isOnline, t]);

  return timer;
}

useTimer.propTypes = {
  starterDate: PropTypes.any, // Optional, for count-up
  remainingTimeInSeconds: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.string,
  ]), // Can be number (for countdown) or string (for count-up)
  isClicked: PropTypes.bool,
};

export default useTimer;
