import { TeamOutlined, UserOutlined } from "@ant-design/icons";
import { moment_timezone } from "App";
import { Divider, Tooltip } from "antd";
import useGetMessage from "pages/layouts/chat/hooks/useGetMessage";
import { getName } from "pages/layouts/chat/utils/ConversationUtils";
import React, { useCallback, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
let prevItem;
const DetailForwardMessage = () => {
  const openDrawer = useSelector((state) => state.chat.openDrawer);
  const userList = useSelector((state) => state.chat.userList);
  const membersGroupsChat = useSelector(
    (state) => state.chat?.membersGroupsChat
  );
  const currentUser = useSelector((state) => state.chat?.currentUser);

  const { getMessagesData } = useGetMessage();

  const messageForwarded = useMemo(
    () =>
      getMessagesData?.pages[0] === undefined
        ? []
        : getMessagesData?.pages
            ?.map((page) => page.data)
            .reduce((accumulator, currentValue) => {
              return accumulator.concat(currentValue);
            }, [])
            .find((message) => message._id === openDrawer?.external?._id),

    [getMessagesData, openDrawer?.external?._id]
  );
  const { t } = useTranslation("common");

  const findNameFromId = useCallback(
    (type, id) => {
      let name;
      switch (type) {
        case 1:
          name = membersGroupsChat
            .filter((item) => item.room !== null)
            .find((item) => item.room?._id === id)?.room?.name;
          break;

        case 0:
          name =
            currentUser?._id === id
              ? currentUser?.name
              : userList.find((item) => item?._id === id)?.name;

          break;

        default:
          break;
      }
      return getName(name, "name");
    },
    [membersGroupsChat, userList, currentUser]
  );
  return messageForwarded && messageForwarded?.forward ? (
    <div className="my-5 flex flex-col space-y-3">
      <p
        className="text-xs text-gray-400"
        dangerouslySetInnerHTML={{
          __html:
            messageForwarded?.forward?.sender_id === currentUser?._id
              ? t("chat.forward.forwardTo")
              : t("chat.forward.forwardToOther", {
                  sent_name: findNameFromId(
                    0,
                    messageForwarded?.forward?.sender_id
                  ),
                }),
        }}
      />
      <div className=" flex flex-col space-y-1 overflow-y-auto">
        {messageForwarded?.forward?.data?.map((item, index) => {
          const sameDay =
            prevItem &&
            moment_timezone(
              moment_timezone(item.forwarded_at).format("YYYY-MM-DD")
            ).isSame(
              moment_timezone(prevItem?.forwarded_at).format("YYYY-MM-DD")
            );
          prevItem = item;

          return (
            <React.Fragment key={`${item._id}----${index}`}>
              {index === 0 && (
                <Divider plain className="py-2">
                  {moment_timezone(item.forwarded_at).format(`Do MMMM, YYYY`) +
                    ". "}
                </Divider>
              )}
              {index > 0 && !sameDay && (
                <Divider plain className="py-2 ">
                  {moment_timezone(item.forwarded_at).format(`Do MMMM, YYYY `) +
                    ". "}
                </Divider>
              )}
              <div className="gap-x-2 p-1 leading-5 text-gray-500">
                <span className="mr-0.5 truncate  whitespace-nowrap font-semibold">
                  {findNameFromId(0, item.forwarded_by)}
                </span>
                <span className=" lowercase">{t("chat.forward.to")}</span>
                <span className="mx-0.5 truncate  whitespace-nowrap font-semibold">
                  {findNameFromId(item.room, item.forwarded_to) ??
                    t("chat.forward.other_room")}
                </span>
                (
                <Tooltip
                  title={
                    <label className=" first-letter:uppercase">
                      {item.room === 1
                        ? t("chat.chat_group")
                        : t("chat.chat_member")}{" "}
                    </label>
                  }>
                  {item.room === 1 ? (
                    <TeamOutlined className=" text-sm" />
                  ) : (
                    <UserOutlined className=" text-xs" />
                  )}
                </Tooltip>
                ){", "}
                {moment_timezone(item.forwarded_at).format(
                  `[${t("chat.edit.at")}] LT`
                ) + ". "}
              </div>
            </React.Fragment>
          );
        })}
      </div>
    </div>
  ) : (
    <></>
  );
};

export default DetailForwardMessage;
