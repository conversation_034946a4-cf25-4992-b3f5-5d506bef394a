import { useEffect, useMemo, useState } from "react";
import { Button, Modal, Skeleton, Typography } from "antd";
import { CloseCircleFilled, InfoCircleFilled } from "@ant-design/icons";
import { FiCopy } from "react-icons/fi";
import { sendUserInvite } from "../services/services";
import { toastNotification } from "../../../components/ToastNotification";
import { useSelector } from "react-redux";
import { truncateString } from "pages/voip/helpers/helpersFunc";

const InvitationLinkModal = ({ open, setOpen, elementInfo, data, t }) => {
  //
  const { userEvent } = useSelector((state) => state.contacts);

  //
  const [truncateLink, setTruncateLink] = useState(true);
  const [isError, setIsError] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [timer, setTimer] = useState(15);
  //
  const invitationInfo = useMemo(() => {
    if (!open) return;
    const findInvitation = data?.find((item) => item.key === elementInfo.id);
    return {
      ...findInvitation?.invitation,
      prettyUrl: findInvitation?.invitation?.url?.split("?")?.[0],
    };
  }, [data, elementInfo.id, open]);
  //
  useEffect(() => {
    if (!open) {
      setIsError(false);
      setIsLoading(false);
    }
  }, [open]);
  //
  useEffect(() => {
    if (!userEvent) return;
    const {
      type_event,
      data: { error },
    } = userEvent;
    if (type_event === "send_invitation" && error) setIsError(true);
    else setIsLoading(false);
  }, [userEvent]);
  //
  const handleReInvite = async () => {
    try {
      setIsLoading(true);
      const formData = new FormData();
      formData.append("ids[]", elementInfo.id);
      const { status } = await sendUserInvite(formData);
      if (status === 200) {
        toastNotification(
          "success",
          <div className="flex flex-col">
            <p>
              Your action is currently being processed. This may take a little
              while.
            </p>
            <p>You will be notified once the process is complete.</p>
          </div>,
          "topRight",
          5
        );
      }
    } catch (err) {
      setIsError(true);
    }
  };
  //
  useEffect(() => {
    let interval;
    if (isError) {
      setTimer(15);
      interval = setInterval(() => {
        setTimer((prevTimer) => prevTimer - 1);
      }, 1000);

      const timerTimeout = setTimeout(() => {
        setOpen(false);
      }, 15000);

      return () => {
        clearTimeout(timerTimeout);
        clearInterval(interval);
      };
    }
  }, [isError, setOpen]);

  //
  return (
    <Modal
      title={
        <div className="relative flex w-full flex-row space-x-2 font-semibold">
          <p>{t("contacts.linkInvitation")}</p>
          <p className="w-7/12 truncate text-slate-400">{elementInfo?.label}</p>
        </div>
      }
      footer={
        invitationInfo?.expired && !isError ? (
          <div className="flex flex-row justify-end">
            <Button type="primary" loading={isLoading} onClick={handleReInvite}>
              {t("contacts.re-invited")}
            </Button>
          </div>
        ) : null
      }
      width={480}
      open={open}
      onCancel={() => setOpen(false)}
    >
      {isError ? (
        <div className="flex flex-row items-start space-x-2 ">
          <CloseCircleFilled
            style={{ fontSize: 18, color: "rgb(255, 77, 79)" }}
          />
          <div>
            <p>{t("contacts.linkInviteError")}</p>
            <p
              dangerouslySetInnerHTML={{
                __html: t("contacts.thisWindowWillClose", {
                  timer: timer,
                }),
              }}
            />
            {/* {t("contacts.thisWindowWillClose")} <strong>{timer}</strong> sec. */}
          </div>
        </div>
      ) : isLoading ? (
        <Skeleton active title={false} paragraph={{ rows: 2 }} />
      ) : invitationInfo?.expired ? (
        <div className="flex flex-row items-start space-x-2">
          <InfoCircleFilled
            style={{ fontSize: 18, color: "rgb(250, 173, 20)" }}
          />
          <p>{t("contacts.linkExpired")}</p>
        </div>
      ) : (
        <div
          onMouseEnter={() => setTruncateLink(false)}
          onMouseLeave={() => setTruncateLink(true)}
          className="relative flex w-full flex-row items-start justify-between space-x-3"
        >
          <p className="h-16 w-11/12 break-words font-semibold text-slate-400">
            {truncateLink
              ? invitationInfo?.prettyUrl
              : truncateString(invitationInfo?.url, 140)}
          </p>
          <Typography.Paragraph
            copyable={{
              text: invitationInfo?.url,
              icon: [
                <FiCopy
                  style={{
                    color: "rgb(22, 119, 255)",
                    fontSize: 18,
                  }}
                />,
              ],
            }}
          />
        </div>
      )}
    </Modal>
  );
};

export default InvitationLinkModal;
