import { But<PERSON>, List } from "antd";
import { useState } from "react";
import AvatarChat from "../Avatar/AvatarChat";
import { MessageOutlined, PhoneOutlined } from "@ant-design/icons";
import {
  resetStateOtherUser,
  setChatSelectedParticipants,
} from "../../../new-redux/actions/chat.actions";
import { useDispatch, useSelector } from "react-redux";
import { callApi } from "../../../new-redux/services/chat.services";
import { useTranslation } from "react-i18next";
import { getName } from "../../../pages/layouts/chat/utils/ConversationUtils";
import useActionCall from "../../../pages/voip/helpers/ActionCall";
import { URL_ENV } from "index";
import { isGuestConnected } from "utils/role";

export const ConnectedUsersListItem = ({
  item,
  source,
  setPreviousRoomParticipants = () => {},
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [clicked, setClicked] = useState(false);
  const currentUser = useSelector((state) => state?.chat?.currentUser);
  const sphereUserRole = useSelector((state) => state?.user?.user?.role);
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const selectedParticipants = useSelector(
    (state) => state.chat.selectedParticipants
  );
  const call = useActionCall();

  return (
    <List.Item
      key={item.email}
      className="hover:bg-slate-300"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <List.Item.Meta
        avatar={
          <AvatarChat
            fontSize="0.875rem"
            url={
              URL_ENV?.REACT_APP_BASE_URL +
              URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
              item.image
            }
            type="user"
            size={38}
            height={10}
            width={10}
            name={getName(item.name, "avatar")}
            hasImage={item.image}
          />
        }
        title={getName(item.name, "name")}
        description={item.email}
      />
      {/* {!source && ( */}
      <div className={`${isHovered ? "mr-1 block space-x-1" : "hidden"}`}>
        <Button
          type="link"
          icon={<MessageOutlined style={{ fontSize: "100%" }} />}
          disabled={
            currentUser?._id === item?._id ||
            isGuestConnected(currentUser?.role, sphereUserRole)
          }
          onClick={() => {
            dispatch(
              resetStateOtherUser({
                item: {
                  type: "user",
                  _id: item._id,
                  source,
                },
                forced: false,
                keepDrawerOpened: false,
              })
            );
            setPreviousRoomParticipants(selectedParticipants);
            dispatch(
              setChatSelectedParticipants({
                selectedParticipants: [
                  {
                    uuid: item?.uuid,
                    email: item?.email,
                    name: item?.name,
                    description: null,
                    image: item?.image,
                    admin_id: null,
                    bot: null,
                    _id: item?._id,
                    post_number: item?.post_number,
                    role: item?.role,
                    type: "user",
                    source,
                    // created_at: item.sender?.created_at,
                  },
                  currentUser,
                ],
              })
            );
          }}
        />
        <Button
          type="link"
          disabled={
            clicked ||
            currentUser?._id === item?._id ||
            !currentUser?.post_number ||
            isGuestConnected(currentUser?.role, sphereUserRole)
          }
          icon={<PhoneOutlined style={{ fontSize: "100%" }} />}
          onClick={() => {
            source === "task"
              ? call(item?.post_number, null, 4)
              : dispatch(
                  callApi({
                    setClicked: setClicked,
                    post_numberR: item?.post_number,
                    errorText: t("toasts.errorFetchApi"),
                  })
                );
          }}
        />
      </div>
      {/* )} */}
    </List.Item>
  );
};
