import MainService from "services/main.service";
import {
  SET_ACCOUNT_MAIL,
  SET_ACCOUNT_MAIL_FETCHED,
  SET_HAS_NEW_MESSAGE,
  SET_LOADING_ACCOUNT,
  SET_SELECTED_ACCOUNT,
  SET_EMAILS_STATS,
  SET_OPEN_MODAL_EMAIL,
  SET_OPEN_EDITOR,
  SET_REFRESH_SENT_MAIL,
  SET_PAGE_EMAILS,
  SET_REFRESH_TABLE_INBOX,
  SET_PAGE_EMAILS_SIZE,
  UPDATE_EMAILS_STATS,
  SET_SEARCH_EMAIL,
  SET_NOTIFICATION_MAILING,
  SET_SEARCH_PAGE_EMAILS,
  SET_REFRESH_KPI,
  SET_REFRESH_NUMBER_NOTIFICATION,
  SET_FILTER_EMAIL,
  SET_FILTER_ACTIVE,
  SET_NUMBER_EMAIL_THREAD,
  SET_REFRESH_VUE_SPHERE,
  SET_SYNCHRO_TYPE,
  RESET_PROPS_MODAL_EMAIL,
} from "../../constants";
import { store } from "new-redux/store";

export const setAccountData = (payload) => (dispatch) => {
  dispatch({
    type: SET_ACCOUNT_MAIL,
    payload,
  });
};
export const setFetchedAccount = (payload) => (dispatch) => {
  dispatch({
    type: SET_ACCOUNT_MAIL_FETCHED,
    payload,
  });
};

export const setLoadingAccount = (payload) => (dispatch) => {
  dispatch({
    type: SET_LOADING_ACCOUNT,
    payload,
  });
};

export const setSelectedAccount = (payload) => (dispatch) => {
  dispatch({
    type: SET_SELECTED_ACCOUNT,
    payload,
  });
};

export const setHasNewMessage = (payload) => (dispatch) => {
  dispatch({
    type: SET_HAS_NEW_MESSAGE,
    payload,
  });
};

export const setOpenModalEmail = (payload) => (dispatch) => {
  dispatch({
    type: SET_OPEN_MODAL_EMAIL,
    payload,
  });
};

export const resetPropsModalEmail = () => (dispatch) => {
  dispatch({
    type: RESET_PROPS_MODAL_EMAIL,
  });
};

export const setRefreshMail = (payload) => (dispatch) => {
  dispatch({
    type: SET_REFRESH_SENT_MAIL,
    payload,
  });
};

export const setOpenEditor = (payload) => (dispatch) => {
  dispatch({
    type: SET_OPEN_EDITOR,
    payload,
  });
};

export const setPage = (payload) => (dispatch) => {
  dispatch({
    type: SET_PAGE_EMAILS,
    payload,
  });
};

export const setSearchPage = (payload) => (dispatch) => {
  dispatch({
    type: SET_SEARCH_PAGE_EMAILS,
    payload,
  });
};
export const setRefreshMailInbox = (payload) => (dispatch) => {
  dispatch({
    type: SET_REFRESH_TABLE_INBOX,
    payload,
  });
};

export const setPageSize = (payload) => (dispatch) => {
  dispatch({
    type: SET_PAGE_EMAILS_SIZE,
    payload,
  });
};
export const updateEmailsStats = (payload) => (dispatch) => {
  dispatch({
    type: UPDATE_EMAILS_STATS,
    payload,
  });
};

export const getMailStats = () => async (dispatch) => {
  try {
    const dataAccounts = await store.getState().mailReducer?.dataAccounts;
    const formData = new FormData();

    (dataAccounts || []).forEach((account) =>
      formData.append("account_ids[]", account?.value)
    );
    const response = await MainService.getStatsMail(formData);
    if (response.status === 200) {
      let res = null;
      response?.data?.data?.forEach((item) => {
        res = {
          ...res,
          [item.account_id]: item,
        };
      });
      dispatch({
        type: SET_EMAILS_STATS,
        payload: res ?? null,
      });
    }
  } catch (error) {
    console.log(error);
  }
};

export const getMailNotification = () => async (dispatch) => {
  try {
    const response = await MainService.getNotificationsMailing();
    dispatch({
      type: SET_NOTIFICATION_MAILING,
      payload: {
        list: response.data,
        hasUnread: response.data.some((item) => item.seen === 1),
      },
    });
  } catch (error) {
    console.log(error);
  }
};

export const resetMailNotifcationByMail = (payload) => async (dispatch) => {
  try {
    const notificationMailCount = await store.getState().mailReducer
      .notificationMailCount;
    if (
      notificationMailCount.list?.find(
        (item) => item.account_id === Number(payload)
      )?.seen === 1
    ) {
      const response = await MainService.resetNotificationsMailing(payload);
      dispatch({
        type: SET_NOTIFICATION_MAILING,
        payload: {
          list: response.data,
          hasUnread: response.data.some((item) => item.seen === 1),
        },
      });
    }
  } catch (error) {
    console.log(error);
  }
};
export const setSearchEmail = (payload) => (dispatch) => {
  dispatch({
    type: SET_SEARCH_EMAIL,
    payload,
  });
};

export const setRefreshKpi = (payload) => (dispatch) => {
  dispatch({
    type: SET_REFRESH_KPI,
    payload,
  });
};

export const setRefreshNumberNotification = (payload) => (dispatch) => {
  dispatch({
    type: SET_REFRESH_NUMBER_NOTIFICATION,
    payload,
  });
};

export const setFilterEmail = (payload) => (dispatch) => {
  dispatch({
    type: SET_FILTER_EMAIL,
    payload,
  });
};

export const setFilterEmailActive = (payload) => (dispatch) => {
  dispatch({
    type: SET_FILTER_ACTIVE,
    payload,
  });
};

export const setNumberEmailThread = (payload) => (dispatch) => {
  dispatch({
    type: SET_NUMBER_EMAIL_THREAD,
    payload,
  });
};

export const setRefreshMailVueSphere = (payload) => (dispatch) => {
  dispatch({
    type: SET_REFRESH_VUE_SPHERE,
    payload,
  });
};

export const setSynchroType = (payload) => (dispatch) => {
  dispatch({
    type: SET_SYNCHRO_TYPE,
    payload,
  });
};
