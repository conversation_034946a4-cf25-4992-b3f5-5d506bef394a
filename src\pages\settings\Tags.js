import React, { useEffect, useRef } from "react";
import {
  Form,
  Typography,
  Input,
  Button,
  Select,
  Badge,
  Space,
  Tag,
} from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { PlusCircleOutlined } from "@ant-design/icons";
import { generateAxios } from "../../services/axiosInstance";
import { toastNotification } from "../../components/ToastNotification";
import Header from "../../components/configurationHelpDesk/Header";
import ChoiceIcons from "../components/ChoiceIcons";
import ColumnColors from "../../components/ColumnColors";
import { allIcons } from "../../components/Icons";
import { colors } from "../../components/Colors";
import { useDispatch, useSelector } from "react-redux";
import { setSearch } from "../../new-redux/actions/menu.actions/menu";
import NewTableDraggable from "../../components/NewTableDraggable";
import LabelTable from "../../components/LabelTable";
import BottomButtonAddRow from "../../components/BottomButtonAddRow";
import { SubmitKeyPress } from "../../utils/SubmitKeyPress";
import { URL_ENV } from "index";

const Tags = () => {
  const [form] = Form.useForm();
  const [debounceValue, setDebounceValue] = useState("");
  const [dataform, setDataForm] = useState({});
  const [data, setData] = useState([]);
  const [editingKey, setEditingKey] = useState("");
  const [id, setId] = useState(null);
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const isEditing = (record) => record.key === editingKey;
  const [t] = useTranslation("common");
  const [selectedItems, setSelectedItems] = useState([]);
  const { user } = useSelector((state) => state.user);
  const categories = [
    user?.access?.contact === "1" && { label: "Contacts", value: "Contacts" },
    user?.access?.deals === "1" && { label: t("menu1.deals"), value: "Deals" },
    user?.access?.companies === "1" && {
      label: t("menu1.companies"),
      value: "Companies",
    },
    user?.access?.logs === "1" && { label: t("menu2.logs"), value: "Call" },
    user?.access?.email === "1" && { label: "Email", value: "Email" },
    user?.access?.ticket === "1" && { label: "Ticket", value: "Ticket" },
    user?.access?.colleague === "1" && {
      label: t("menu2.users"),
      value: "User",
    },
    user?.access?.booking === "1" && {
      label: t("menu1.booking"),
      value: "Booking",
    },
    user?.access?.contact === "1" && { label: t("menu1.leads"), value: "Lead" },
    user?.access?.products === "1" && {
      label: t("menu1.products"),
      value: "Product",
    },
    user?.access?.projects === "1" && {
      label: t("menu1.projects"),
      value: "Projet",
    },
    user?.access?.visio === "1" && { label: "Visio", value: "Visio" },
    user?.access?.rmc === "1" && { label: "RMC", value: "RMC" },
    user?.access?.activities === "1" && {
      label: t("menu1.task"),
      value: "Task",
    },
  ].filter(Boolean);
  const filteredOptions = categories.filter((o) => !selectedItems.includes(o));
  const [currentPage, setCurrentPage] = useState(1);
  const [oldPage, setOldPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  const dispatch = useDispatch();
  const { search } = useSelector((state) => state.form);
  // useEffect(() => {
  //   const timer = setTimeout(() => setDebounceValue(search), 100);

  //   return () => {
  //     clearTimeout(timer);
  //   };
  // }, [search]);
  // useEffect(() => {
  //   if (editingKey && oldPage !== currentPage) {
  //     setData((prev) => prev.filter((el) => el.id));
  //     setEditingKey("");
  //   }
  // }, [currentPage, editingKey, oldPage]);
  const inputRefs = useRef([]);
  useEffect(() => {
    return () => {
      dispatch(setSearch(""));
    };
  }, []);

  useEffect(() => {
    inputRefs.current.forEach((input) => {
      input?.focus();
    });
  }, [id, data.length]);

  const onFinishFailed = (values) => {
    console.log(values);
  };
  const handleKeyPress = (event) => {
    SubmitKeyPress(event, form);
  };
  const EditableCell = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    const inputNode =
      inputType === "select" ? (
        <Select
          placeholder={t("tags.selecticon")}
          showSearch
          style={{
            minWidth: 100,
          }}
          options={allIcons.map((el) => ({
            label: (
              <Space className="px-1">
                <Typography.Text>{el.label}</Typography.Text>
                <Typography.Text type="secondary">
                  {el.value.replaceAll("Outlined", "")}{" "}
                </Typography.Text>
              </Space>
            ),
            value: el.value,
          }))}
          optionFilterProp="children"
          filterOption={(input, option) =>
            (option?.value.toLowerCase() ?? "").includes(input.toLowerCase())
          }
          filterSort={(optionA, optionB) =>
            (optionA?.value ?? "")
              .toLowerCase()
              .localeCompare((optionB?.value ?? "").toLowerCase())
          }
          allowClear
        />
      ) : inputType === "radio" ? (
        <Select
          showSearch
          placeholder={t("tags.selectcolor")}
          style={{
            minWidth: 100,
          }}
          options={colors.map((el) => ({
            label: (
              <Space>
                <Badge color={el.value} /> {t(`colors.${el.label}`)}
              </Space>
            ),
            value: el.value,
            label2: el.label,
          }))}
          optionFilterProp="children"
          filterOption={(input, option) => {
            var _a;
            return (
              (_a =
                option === null || option === void 0
                  ? void 0
                  : t(`colors.${option.label2}`)) !== null && _a !== void 0
                ? _a
                : ""
            )
              .toLowerCase()
              .includes(input.toLowerCase());
          }}
          filterSort={(optionA, optionB) =>
            (optionA?.value ?? "")
              .toLowerCase()
              .localeCompare((optionB?.value ?? "").toLowerCase())
          }
          allowClear
        />
      ) : inputType === "tasks" ? (
        <Select
          placeholder={t("tags.selecttypeactivity")}
          options={tasks.map((el) => ({ value: el.id, label: el.label }))}
          filterOption={(input, option) => {
            var _a;
            return (
              (_a =
                option === null || option === void 0
                  ? void 0
                  : t(`colors.${option.label2}`)) !== null && _a !== void 0
                ? _a
                : ""
            )
              .toLowerCase()
              .includes(input.toLowerCase());
          }}
          allowClear
        />
      ) : inputType === "categories" ? (
        <Select
          mode="multiple"
          placeholder={t("tags.selectcategories")}
          // placeholder="Inserted are removed"
          options={filteredOptions.length > 0 && filteredOptions}
          filterOption={(input, option) => {
            var _a;
            return (
              (_a =
                option === null || option === void 0
                  ? void 0
                  : option.label) !== null && _a !== void 0
                ? _a
                : ""
            )
              .toLowerCase()
              .includes(input.toLowerCase());
          }}
        />
      ) : (
        <Input
          ref={(el) => (inputRefs.current[0] = el)}
          onKeyPress={handleKeyPress}
          placeholder={t("activities.name")}
        />
      );
    return (
      <td {...restProps}>
        {editing ? (
          <Form.Item
            name={dataIndex.toLowerCase()}
            style={{
              margin: 0,
            }}
            rules={[
              {
                required: dataIndex === "label" ? true : false,
                message: `${t(`tags.${dataIndex}`)} ${t(
                  "table.header.isrequired"
                )}`,
              },
            ]}
          >
            {inputNode}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };
  const edit = (record) => {
    if (record) {
      form.setFieldsValue({
        label: record.label.replace(/<\/?[^>]+(>|$)/g, ""),
        icon: record.icon,
        color: record?.color?.replace(/<\/?[^>]+(>|$)/g, ""),
        typetask_id: record?.typetask?.id,
        categorie:
          record.categorie && record.categorie.length > 0
            ? record.categorie.map((el) => el?.replace(/<\/?[^>]+(>|$)/g, ""))
            : [],
      });
      setDataForm({
        label: record.label,
        icon: record.icon,
        color: record.color,
        typetask_id: record?.typetask?.id,
        categorie: record.categorie,
      });
      setId(record.id);
    } else {
      form.setFieldsValue({
        label: "",
        icon: null,
        color: "",
        typetask_id: "",
        categorie: [],
      });
    }
    setEditingKey(record.key);
  };
  const cancel = (record) => {
    setEditingKey("");
    setId(null);
    if (!record.id) {
      setData(data.filter((item) => item.key !== record.key));
    }
  };
  const save = async (key) => {
    setLoading(true);
    if (id) {
      try {
        const row = await form.validateFields();

        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post(`/qualifications/update/${id}`, {
          ...row,
          type_qualification: row.typetask_id ? 1 : 0,
        });
        setEditingKey("");
        setData((previous) =>
          previous.map((el) =>
            el.id === res.data.data.qualifications.id
              ? {
                  ...res.data.data.qualifications,
                  key: res.data.data.qualifications.id,
                }
              : el
          )
        );
        form.setFieldsValue({
          label: null,
          icon: null,
          color: null,
          typetask_id: null,
          categorie: [],
        });
        setDataForm({});
        setLoading(false);
        toastNotification("success", row.label + t("toasts.edit"), "topRight");
      } catch (errInfo) {
        setLoading(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    } else {
      try {
        const row = await form.validateFields();

        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post("/qualifications", {
          ...row,
          type_qualification: row.typetask_id ? 1 : 0,
        });
        setEditingKey("");
        setData(
          res.data.data.qualifications.map((el) => ({ ...el, key: el.id }))
        );

        form.setFieldsValue({
          label: null,
          icon: null,
          color: null,
          typetask_id: null,
          categorie: [],
        });
        setDataForm({});

        setLoading(false);
        toastNotification(
          "success",
          row.label + t("toasts.created"),
          "topRight"
        );
      } catch (errInfo) {
        setLoading(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    }
  };
  useEffect(() => {
    const getData = async () => {
      setLoading(true);
      try {
        const tags = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get(`/qualifications`);
        setData(
          tags.data.data.qualifications.map((el, i) => ({
            ...el,
            key: el.id,
            rank: i + 1,
          }))
        );
        // console.log(tags.data.data);

        if (tags.status === 200) {
          const tasks = await generateAxios(
            URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
          ).get(`/task-types`);
          setTasks(tasks.data.data.tasks_type);
          setLoading(false);
        }
      } catch (err) {
        setLoading(false);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    };
    const getActivities = async () => {
      setLoading(true);
      setEditingKey("");
      try {
        const {
          data: { data },
        } = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post(`/qualifications-search?label=${debounceValue}`);
        setData(
          data.original.results.map((el, i) => ({
            ...el,
            key: el.id,
          }))
        );
        setLoading(false);
      } catch (e) {
        setLoading(false);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    };
    getData();
    // if (!debounceValue) getData();
    // else getActivities();
  }, []);
  const handleClick = (event) => {
    event.stopPropagation();
  };
  const columns = [
    {
      title: t("activities.name"),
      dataIndex: "label",
      key: "label",
      editable: true,
      width: 200,
      sorter: (a, b) => a.label.localeCompare(b.label),
      render: (_, record) => {
        return (
          <LabelTable
            record={record}
            editingKey={editingKey}
            edit={edit}
            width={190}
          />
        );
      },
    },
    {
      title: t("tags.icon"),
      dataIndex: "icon",
      key: "icon",
      editable: true,
      width: "150px",

      render: (_, { icon }) => <ChoiceIcons icon={icon} fontSize={16} />,
    },
    {
      title: t("tags.typeTask"),
      dataIndex: "typetask_id",
      key: "typetask_id",
      editable: true,

      sorter: (a, b) => a.typetask?.label.localeCompare(b.typetask?.label),
      render: (_, { typetask, color, typetask_id }) =>
        typetask?.icons ? (
          <Space wrap style={{ color: typetask?.color }}>
            <ChoiceIcons icon={typetask?.icons} />

            <div dangerouslySetInnerHTML={{ __html: typetask.label }} />
          </Space>
        ) : (
          ""
        ),
    },
    {
      title: "Modules",
      dataIndex: "categorie",
      key: "categorie",
      editable: true,

      render: (_, { categorie }) => (
        <Space size={[0, 8]} wrap>
          {categorie &&
            categorie.length > 0 &&
            categorie.map((cat) => (
              <Tag key={cat}>
                {" "}
                <div
                  dangerouslySetInnerHTML={{
                    __html:
                      cat === "Deals"
                        ? t("menu1.deals")
                        : cat === "Companies"
                        ? t("menu1.companies")
                        : cat === "User"
                        ? t("menu2.users")
                        : cat === "Booking"
                        ? t("menu1.booking")
                        : cat === "Lead"
                        ? t("menu1.leads")
                        : cat === "Product"
                        ? t("menu1.products")
                        : cat === "Projects"
                        ? t("menu1.projects")
                        : cat === "Task"
                        ? t("menu1.task")
                        : cat === "Call"
                        ? t("menu2.logs")
                        : cat,
                  }}
                />{" "}
              </Tag>
            ))}
        </Space>
      ),
    },
    {
      title: t("tags.color"),
      dataIndex: "color",
      key: "color",
      editable: true,
      render: (_, { color }) =>
        color ? <ColumnColors color={color} colors={colors} /> : "",
    },
  ];
  const onRow = () => {};
  const handleAdd = () => {
    const ids = data.map((object) => {
      return object.id;
    });
    setId(null);

    const newData = {
      key: Math.max(...ids) + 1,
      label: "",
      icon: null,
      color: null,
      typetask_id: null,
      categorie: [],
      disabled: true,
    };
    setCurrentPage(Math.ceil([...data, newData].length / pageSize));
    setOldPage(Math.ceil([...data, newData].length / pageSize));

    setData([...data, newData]);
    form.setFieldsValue({
      label: "",
      icon: null,
      color: null,
      typetask_id: null,
      categorie: [],
    });
    setEditingKey(Math.max(...ids) + 1);
  };
  const filteredData = data.filter((tag) => {
    if (!search.trim()) return true; // Si search est vide, on retourne tout

    const searchTerm = search.toLowerCase();

    // Recherche dans le label du tag
    if (tag.label?.toLowerCase().includes(searchTerm)) {
      return true;
    }

    // Recherche dans la couleur (si elle existe)
    if (tag.color?.toLowerCase().includes(searchTerm)) {
      return true;
    }

    // Recherche dans les catégories
    if (tag.categorie?.some((cat) => cat.toLowerCase().includes(searchTerm))) {
      return true;
    }

    // Recherche dans le label du typetask (si typetask existe)
    if (tag.typetask?.label?.toLowerCase().includes(searchTerm)) {
      return true;
    }

    // Si rien ne correspond
    return false;
  });
  return (
    <Space direction="vertical" style={{ width: "100%" }}>
      <div className="pt-4">
        <Header
          active={"4"}
          editingKey={editingKey}
          handleAdd={handleAdd}
          btnText={t("tags.createTag")}
          disabled={loading ? true : editingKey ? true : search ? true : false}
        />
      </div>

      <NewTableDraggable
        columns={columns}
        setLoading={setLoading}
        isEditing={isEditing}
        data={filteredData}
        setData={setData}
        loading={loading}
        save={save}
        edit={edit}
        EditableCell={EditableCell}
        onFinishFailed={onFinishFailed}
        cancel={cancel}
        form={form}
        apiRank="rank-qualification"
        editingKey={editingKey}
        api="qualifications"
        onRow={onRow}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        pageSize={pageSize}
        setPageSize={setPageSize}
      />
      <BottomButtonAddRow
        editingKey={editingKey}
        data={data}
        text={t("tags.createTag")}
        handleAdd={handleAdd}
        loading={loading}
        search={search || ""}
      />
    </Space>
  );
};
export default Tags;
