import { Divider, Input, Layout, Spin, Tabs, Typography } from "antd";
import { URL_ENV } from "index";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { FiSearch } from "react-icons/fi";
import { generateAxios } from "services/axiosInstance";
import ListIntegrations from "./ListIntegrations";
import ContentIntegation from "./ContentIntegation";
import { toastNotification } from "components/ToastNotification";
const { Header, Content, Footer, Sider } = Layout;

const Integrations = () => {
  const [loading, setLoading] = useState(false);
  const [integrations, setIntegrations] = useState([]);
  const [selectedIntegration, setSelectedIntegration] = useState("");
  const [search, setSearch] = useState("");

  const [t] = useTranslation("common");
  useEffect(() => {
    const getListIntegrations = async () => {
      try {
        setLoading(true);
        const res = await generateAxios(
          URL_ENV?.REACT_APP_OAUTH_CHAT_API +
            "/" +
            process.env.REACT_APP_SUFFIX_API
        ).get("/integrations");
        setIntegrations(res.data.data);
        setLoading(false);

        // console.log(res.data);
      } catch (err) {
        setLoading(false);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    };
    getListIntegrations();
  }, []);
  const filteredOptions = integrations?.filter((el) =>
    el?.app_name?.toLowerCase()?.includes(search?.toLowerCase())
  );

  return (
    <>
      <Spin spinning={loading}>
        <Layout>
          <Sider
            width="360px"
            style={{
              background: "white",
              //   padding: "12px 0px",
              borderRight: "1px solid #e8e8e8",
              height: "calc(100vh - 58px)",
              // marginTop: "-20px",
            }}
          >
            <div className="flex flex-col gap-2 p-2">
              <div className="flex items-center justify-between">
                <div className="text-base font-semibold">
                  {t("integrations.listOfIntegrations")}
                </div>
              </div>
              <Input
                prefix={<FiSearch className="text-slate-500" />}
                placeholder={t("table.search")}
                //   value={search.trimStart()}
                onChange={(e) => setSearch(e.target.value)}
              />
            </div>
            <Tabs
              className="px-2"
              defaultActiveKey="1"
              items={[
                {
                  label: "Tickets",
                  key: "1",
                  children: (
                    <ListIntegrations
                      integrations={filteredOptions.filter(
                        (el) => el.type === 1
                      )}
                      selectedIntegration={selectedIntegration}
                      setSelectedIntegration={setSelectedIntegration}
                      setIntegrations={setIntegrations}
                    />
                  ),
                },
                {
                  label: t("integrations.iaTools"),
                  key: "2",
                  children: (
                    <ListIntegrations
                      integrations={filteredOptions.filter(
                        (el) => el.type === 2
                      )}
                      selectedIntegration={selectedIntegration}
                      setSelectedIntegration={setSelectedIntegration}
                      setIntegrations={setIntegrations}
                    />
                  ),
                },
              ]}
              onChange={() => setSelectedIntegration("")}
            />
          </Sider>
          <Layout
            style={{
              // padding: "0 24px 24px",

              background: "white",
            }}
          >
            <Content
              style={{
                margin: 0,
                minHeight: 280,
                background: "#80808008",
              }}
            >
              {selectedIntegration?.app_name ? (
                <ContentIntegation selectedIntegration={selectedIntegration} />
              ) : (
                <div className="flex h-[calc(100vh-57px)] items-center justify-center ">
                  <span className="text-sm font-medium">
                    {t("integrations.emptyIntegration")}
                  </span>
                </div>
              )}
            </Content>
          </Layout>
        </Layout>
      </Spin>
    </>
  );
};

export default Integrations;
