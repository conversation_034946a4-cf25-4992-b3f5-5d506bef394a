import { FilterTwoTone } from "@ant-design/icons";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Checkbox,
  Popover,
  Space,
  Tooltip,
  message,
} from "antd";
import React, { memo, useEffect, useState } from "react";
import CardFilter from "./CardFilter";
import { TagWithCloseIcon } from "pages/components/DetailsProfile/Activities/ListActivities";

const FilterTable = ({
  filters,
  setFilter,
  t,
  openFilterTable,
  setOpenFilterTable,
  disabled = false,
  oneFilter = true,
  selectedFilter = [],
  tasks = [],
  widthTooltip = "auto",
}) => {
  const [checkedList, setCheckedList] = useState([]);
  const [checkedListOk, setCheckedListOk] = useState([]);

  useEffect(() => {
    if (!openFilterTable) {
      setCheckedListOk([...selectedFilter]);
      setCheckedList([...selectedFilter]);
    }
  }, [openFilterTable, selectedFilter.length]);
  const onChange = (checkedValues) => {
    setCheckedList(checkedValues);
  };
  const handleReset = () => {
    setCheckedList([]);
    setFilter({ selected: [] });
    setOpenFilterTable(false);
    setCheckedListOk([]);
  };
  const handleOk = () => {
    setFilter({ selected: checkedList });
    setCheckedListOk(checkedList);
    setOpenFilterTable(false);
  };
  const content = (
    <div className="flex  flex-col space-y-4">
      <div className="max-h-[250px] overflow-y-auto">
        {oneFilter ? (
          <div className="flex space-x-2">
            <Checkbox.Group
              options={filters.map((el) => ({
                label: el.text,
                value: el.value,
              }))}
              onChange={onChange}
              value={checkedList}
              className="flex flex-col space-y-2"
            />
            {/* <Checkbox.Group
          options={filters.map((el) => ({ label: el.text, value: el.value }))}
          onChange={onChange}
          value={checkedList}
          className="flex flex-col space-y-2"
        /> */}
          </div>
        ) : (
          <div className="flex max-h-[200px] w-full  overflow-auto">
            <Space align="start">
              {filters.map((filter, i) => (
                <CardFilter
                  key={i}
                  filter={filter}
                  setFilter={setFilter}
                  setCheckedListFilter={setCheckedList}
                  checkedListFilter={checkedList}
                  checkedListOk={checkedListOk}
                  openFilterTable={openFilterTable}
                />
              ))}
            </Space>
          </div>
        )}
      </div>
      <div className="flex flex-row-reverse space-x-2">
        <Space>
          <Button size="small" type="link" onClick={handleReset}>
            {t("tags.reset")}
          </Button>
          <Button
            type="primary"
            size="small"
            onClick={
              !oneFilter
                ? () => message.info("This feature is under dev!")
                : handleOk
            }
          >
            {t("voip.apply")}
          </Button>
        </Space>
      </div>
    </div>
  );
  return (
    <Popover
      trigger={["click"]}
      placement="bottomRight"
      //   title={t("voip.filterCallByType")}
      content={content}
      open={openFilterTable}
      onOpenChange={(newOpen) => {
        setOpenFilterTable(newOpen);
        newOpen ? setCheckedList(checkedList) : setCheckedList(checkedListOk);
      }}
      overlayStyle={{ maxHeight: "300px" }}
    >
      <Popover
        open={
          tasks.filter((element) => selectedFilter.indexOf(element.id) !== -1)
            .length > 0
            ? true
            : false
        }
        placement="left"
        color="white"
        overlayClassName="filterTable"
        overlayInnerStyle={{
          width: "max-content",
          overflow: "auto",
          maxWidth: widthTooltip,
        }}
        title={
          <div className="flex items-center gap-2">
            {tasks
              .filter((element) => selectedFilter.indexOf(element.id) !== -1)
              .map((el) => (
                <TagWithCloseIcon
                  key={el.id}
                  color={el.color || "gray"}
                  onClose={() => {}}
                  children={el}
                  setFilter={setFilter}
                />
              ))}
          </div>
        }
      >
        <Button
          type="text"
          shape="circle"
          disabled={disabled}
          icon={
            <Badge dot={checkedListOk?.length}>
              <FilterTwoTone />
            </Badge>
          }
          //   onClick={() => setOpenPopover(true)}
        />
      </Popover>
    </Popover>
  );
};

export default memo(FilterTable);
