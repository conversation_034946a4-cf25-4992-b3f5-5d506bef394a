import { Avatar, Badge, Button, Select } from "antd";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import MainService from "../../../services/main.service";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { toastNotification } from "../../../components/ToastNotification";
import { URL_ENV } from "index";
import { setRefreshMailInbox } from "new-redux/actions/mail.actions";

const AssignContent = ({
  idEmail,
  owner,
  setDataMail,
  dataMailInbox,
  setOpen,
  openSelect,
  openPopCon,
  type,
  setDetailsMail,
  getDetailsMessageInbox,
}) => {
  const [dataUsers, setDataUsers] = useState([]);
  const [assigned, setAssigned] = useState(owner.owner ?? "");
  const [loading, setLoading] = useState(false);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const onlineUser = useSelector((state) => state.ChatRealTime.onlineUser);
  const dispatch = useDispatch();
  const { dataAccounts } = useSelector((state) => state.mailReducer);

  const usedAccount = useMemo(
    () => dataAccounts?.find((item) => item.selected === true),
    [dataAccounts]
  );
  const [t] = useTranslation("common");

  const getUsers = useCallback(async () => {
    setLoadingUsers(true);
    var formData = new FormData();
    formData.append("account_id", usedAccount.value);
    formData.append("filter", 0);
    for (let i = 0; i < usedAccount?.dispatcheur?.length; i++) {
      formData.append("dispatcheur[]", usedAccount.dispatcheur[i]);
    }
    try {
      const response = await MainService.getUsers(formData);
      if (response?.status === 200) {
        setDataUsers(response?.data?.data);
        setLoadingUsers(false);
      }
    } catch (error) {
      setLoadingUsers(false);
      console.log(error);
    }
  }, []);

  const guestOptionsInSelect =
    dataUsers &&
    dataUsers.map((element) => ({
      //   label: element?.label_data,
      label: (
        <div className="flex">
          <div>
            {element?.avatar?.length > 0 ? (
              <Badge
                color={
                  onlineUser[element?.uuid] === "away"
                    ? "orange"
                    : onlineUser[element?.uuid] === "busy"
                    ? "red"
                    : onlineUser[element?.uuid] === "online"
                    ? "green"
                    : "#a6a6a6"
                }
                dot={true}
                offset={[-10, 3]}
              >
                <Avatar
                  // style={{
                  //   // backgroundColor: "#c41d7f",
                  //   marginRight: "10px",
                  //   marginBottom: "4px",
                  // }}
                  style={{
                    // backgroundColor: "#c41d7f",
                    marginRight: "10px",
                    marginBottom: "4px",
                  }}
                  size={22}
                  src={
                    <img
                      src={`${
                        URL_ENV?.REACT_APP_BASE_URL +
                        URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                      }${element?.avatar}`}
                      alt="name"
                    />
                  }
                />
              </Badge>
            ) : (
              <Badge
                color={
                  onlineUser[element?.uuid] === "away"
                    ? "orange"
                    : onlineUser[element?.uuid] === "busy"
                    ? "red"
                    : onlineUser[element?.uuid] === "online"
                    ? "green"
                    : "#a6a6a6"
                }
                dot={true}
                offset={[-10, 3]}
              >
                <Avatar
                  style={{
                    backgroundColor: "#c41d7f",
                    marginRight: "10px",
                    marginBottom: "4px",
                  }}
                  size={22}
                  color={
                    onlineUser[owner?.uuid] === "away"
                      ? "orange"
                      : onlineUser[owner?.uuid] === "busy"
                      ? "red"
                      : onlineUser[owner?.uuid] === "online"
                      ? "green"
                      : "#a6a6a6"
                  }
                  dot={true}
                >
                  {element?.label_data?.charAt(0)?.toUpperCase()}
                </Avatar>
              </Badge>
            )}
          </div>
          <div className="w-[75%] ">{element?.label_data}</div>

          <div className="font-semibold ">{element?.kpi_Assign}</div>
        </div>
      ),
      value: element?.owner,
      searchOption: element?.label_data,
    }));

  const filterOption = (input, option) =>
    (option?.searchOption ?? "").toLowerCase().includes(input.toLowerCase());

  const MessageErrorAssign = (response) => {
    if (
      response ===
      "You are unable to perform actions on this assignment because it has been transferred to another account"
    ) {
      return toastNotification(
        "error",
        t("mailing.AssigntransferredError"),
        "topRight",
        3
      );
    } else if (
      response.includes(
        `You cannot perform this action because you don't have the dispatcher role`
      )
    ) {
      return toastNotification(
        "error",
        t("mailing.AssignDispatcherError"),
        "topRight",
        3
      );
    } else if (
      response.includes(
        "Please contact your administrator for dispatcher configuration"
      )
    ) {
      return toastNotification(
        "error",
        t("mailing.AssignContactAdmin"),
        "topRight",
        3
      );
    }
  };

  const AssignEmail = async () => {
    setLoading(true);

    var formData = new FormData();
    formData.append("id_email", idEmail);
    formData.append("owner", assigned);
    formData.append("account_id", usedAccount.value);
    for (let i = 0; i < usedAccount.departmentId.length; i++) {
      formData.append("departement_id[]", usedAccount.departmentId[i]);
    }
    for (let i = 0; i < usedAccount?.dispatcheur?.length; i++) {
      formData.append("dispatcheur[]", usedAccount.dispatcheur[i]);
    }

    try {
      const response = await MainService.assignEmail(formData);
      if (response?.status === 200) {
        setLoading(false);

        if (type === "inbox") {
          dispatch(setRefreshMailInbox(true));
          // setDataMail(
          //   dataMailInbox.map((item) =>
          //     item.id === idEmail
          //       ? { ...item, owner: response?.data.data[0] }
          //       : item
          //   )
          // );
        } else {
          setDetailsMail((p) => {
            let prev = { ...p };
            prev.data[prev?.data?.length - 1].owner = response?.data.data[0];
            return prev;
          });
        }
        setOpen(false);
        getDetailsMessageInbox();
        toastNotification(
          "success",
          t("mailing.EmailAssigned") + `${response?.data?.data[0]?.label_data}`,
          "topRight",
          3
        );
      }
    } catch (error) {
      console.log(error);
      setLoading(false);
      MessageErrorAssign(error?.response?.data?.message);
    }
  };

  useEffect(() => {
    if (openPopCon) {
      getUsers();
    }
  }, []);

  function findId(number) {
    for (let obj of guestOptionsInSelect) {
      if (obj.value === number) {
        return true; // Number found
      }
    }
    return false; // Number not found
  }

  return (
    <div>
      <Select
        loading={loadingUsers}
        defaultOpen={openSelect}
        style={{ width: "240px" }}
        placeholder={t("mailing.thanksAssign")}
        showSearch
        optionFilterProp={["label", "searchOption"]}
        filterOption={filterOption}
        onChange={(e) => {
          setAssigned(e);
        }}
        options={guestOptionsInSelect}
        value={
          loadingUsers ? "loading..." : !findId(assigned) ? null : assigned
        }
      ></Select>

      <div className="flex justify-end pb-2 pt-4 ">
        <Button
          size="small"
          type="primary"
          loading={loading}
          onClick={() => AssignEmail()}
          disabled={!assigned?.length}
        >
          {t("voip.save")}
        </Button>
      </div>
    </div>
  );
};

export default AssignContent;
