import { memo } from "react";
import { <PERSON><PERSON>, But<PERSON>, Divider, List, Space, Tag, Tooltip } from "antd";
import { HiOutlineCalendar, HiOutlineVideoCamera } from "react-icons/hi2";
import { renderHighlight, renderTitle } from ".";
import { humanDate } from "pages/voip/helpers/helpersFunc";
import {
  displayPriorityColor,
  handlePriorityLabelOnHover,
} from "pages/tasks/helpers/handlePriorities";
import { TbFlag3 } from "react-icons/tb";
import { SyncOutlined, VideoCameraOutlined } from "@ant-design/icons";
import { colors } from "components/Colors";
import DisplayAvatar from "pages/voip/components/DisplayAvatar";

const RenderTaskItem = ({
  item,
  t,
  imgBaseUrl,
  handleClickOnItem,
  handleJoinVisioMeeting,
}) => {
  //
  const {
    id,
    label,
    owner,
    pipeline,
    stage,
    created_at,
    created_by,
    priority,
    start_date,
    start_time,
    end_date,
    end_time,
    in_progress,
    visio: isVisio,
    highlight,
  } = item;

  const visioRoomId = item?.room_name;
  //
  return (
    <List.Item
      className="custom-list-item-global-search"
      key={id}
      style={{
        alignItems: "center",
      }}
      // onClick={() => navigate(`/${isVisio ? "visio" : "tasks"}/${id}`)}
      onClick={() => handleClickOnItem(`/${"tasks"}/${id}`)}
    >
      <List.Item.Meta
        avatar={
          <Badge
            status="processing"
            dot={isVisio && in_progress}
            offset={[-6, 3]}
          >
            <DisplayAvatar
              size={44}
              icon={
                isVisio ? (
                  <HiOutlineVideoCamera style={{ fontSize: 21 }} />
                ) : (
                  <HiOutlineCalendar style={{ fontSize: 23 }} />
                )
              }
            />
          </Badge>
        }
        title={renderTitle(label, isVisio ? "Visio" : "Activity")}
        description={
          <div className="flex flex-col space-y-0.5">
            <Space size={2} split={<Divider type="vertical" />}>
              <p>{`${t("unavailability.start_date")} : ${humanDate(
                `${start_date} ${start_time}`,
                t,
                "table"
              )}`}</p>
              {!!end_date && (
                <p>{`${t("unavailability.end_date")} : ${humanDate(
                  `${end_date} ${end_time}`,
                  t,
                  "table"
                )}`}</p>
              )}
              {!!priority && (
                <Tooltip title={handlePriorityLabelOnHover(priority)}>
                  <TbFlag3
                    style={{
                      fontSize: "18px",
                      cursor: "help",
                      color: displayPriorityColor(priority),
                    }}
                  />
                </Tooltip>
              )}
            </Space>
            <Space size={2} split={<Divider type="vertical" />}>
              <Space size={2}>
                <p>{t("globalSearch.createdBy")}</p>

                <DisplayAvatar
                  cursor="help"
                  tooltip={true}
                  size={20}
                  name={created_by?.label_data}
                  urlImg={
                    !!created_by?.avatar && `${imgBaseUrl}${created_by?.avatar}`
                  }
                />
              </Space>
              <Space size={2}>
                <p>{t("globalSearch.owner")}</p>
                <Tooltip title={owner?.label_data}>
                  <DisplayAvatar
                    cursor="help"
                    tooltip={true}
                    size={20}
                    name={owner?.label_data}
                    urlImg={!!owner?.avatar && `${imgBaseUrl}${owner?.avatar}`}
                  />
                </Tooltip>
              </Space>
              {!!pipeline && (
                <Tag
                  bordered={false}
                  style={{ fontSize: 12, paddingLeft: 2, paddingRight: 2 }}
                >
                  {pipeline.label}
                </Tag>
              )}
              {!!stage && (
                <Tag
                  bordered={false}
                  // color={stage?.color}
                  color={
                    colors.find((c) => c.value === stage?.color)?.tagColor ??
                    stage?.color
                  }
                  style={{ fontSize: 12, paddingLeft: 2, paddingRight: 2 }}
                >{`${stage?.label} ${
                  !!stage?.percent ? `${stage.percent}%` : ""
                }`}</Tag>
              )}
              {isVisio && in_progress && (
                <Tag
                  bordered={false}
                  icon={<SyncOutlined spin />}
                  color="processing"
                  style={{ fontSize: 12, paddingLeft: 2, paddingRight: 2 }}
                >
                  {t("globalSearch.visioInProgress")}
                </Tag>
              )}
              {isVisio && in_progress && !!visioRoomId && (
                <Button
                  size="small"
                  type="primary"
                  icon={<VideoCameraOutlined />}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleJoinVisioMeeting(visioRoomId);
                  }}
                >
                  {t("globalSearch.joinVisio")}
                </Button>
              )}
            </Space>
            {renderHighlight(highlight)}
          </div>
        }
      />
    </List.Item>
  );
};

export default memo(RenderTaskItem);
