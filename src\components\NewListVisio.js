import { useCallback, useEffect, useRef, useState } from "react";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Dropdown,
  Form,
  Input,
  Popover,
  Segmented,
  Skeleton,
  Space,
  Table,
  Tag,
  Tooltip,
  Typography,
  message,
  Select,
  Tour,
  Image,
} from "antd";
import {
  BellOutlined,
  CheckOutlined,
  ClockCircleOutlined,
  DeleteOutlined,
  DownOutlined,
  InfoCircleOutlined,
  PlusOutlined,
  RestOutlined,
  VideoCameraOutlined,
  LoadingOutlined,
} from "@ant-design/icons";
import moment from "moment";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { HiOutlineCalendar } from "react-icons/hi";
import { AiOutlineVideoCameraAdd } from "react-icons/ai";
import axios from "axios";
import { TbFlag3, TbFlag3Filled } from "react-icons/tb";
import { MdVideoCameraFront } from "react-icons/md";

import {
  displayPriorityColor,
  handlePriorityLabelOnHover,
  prioritiesList,
} from "pages/tasks/helpers/handlePriorities";
import { formatDateForDisplay } from "pages/tasks/helpers/formatDateToDisplay";
import { AvatarChat } from "./Chat";
import AvatarGroup from "./AvatarGroup";
import Confirm from "./GenericModal";
import MainService from "../services/main.service";
import { getName } from "../pages/layouts/chat/utils/ConversationUtils";
import { toastNotification } from "./ToastNotification";
import ShareVisio from "../pages/layouts/visio/components/share-visio";
import CreateTask from "../pages/voip/components/CreateTask";
import TasksRoom from "../pages/tasks/tasksRoom";
import {
  changeListNotifs,
  onChangeTabVisio,
  setAllNotifsAsRead,
  setCountNotificationVisio,
  setDetailsMeet,
  setHistory,
  setKeyMeet,
  setKpiDateVisio,
  setKpiVisio,
  setLater,
  setLimitVisio,
  setListMeet,
  setNotificationList,
  setNow,
  setPage,
  setRemindersList,
  setSearchListVisio,
  setTabKey,
  setCountReminders,
} from "../new-redux/actions/visio.actions/visio";
import UpdateTask from "../pages/tasks/UpdateTask";
import { getTokenRoom } from "../new-redux/actions/visio.actions/createVisio";
import NotificationsPopover from "./NotificationsPopover";
import useDebounce from "../pages/components/UseDebounce/UseDebounce";
import DropdownTask from "./DropdownTask";
import { URL_ENV } from "index";
import CardStat from "pages/components/CardStat";
import { displayStatKey } from "pages/tasks/KpiGrid";
import Activity360 from "pages/tasks/activityDetails/Activity360";
import {
  isOverviewModalOpen,
  setRemindersArray,
  setTotalNotificationsNumber,
} from "new-redux/actions/tasks.actions/realTime";
import { setOpenTaskRoomDrawer } from "new-redux/actions/tasks.actions/handleTaskRoomDrawer";
import { EXTENSIONS_ARRAY } from "pages/tasks/helpers/calculateSum";
import ActionsComponent from "pages/tasks/ActionsComponent";
import getContactDataAndDispatch from "pages/clients&users/helpers/getContactDataAndDispatch";
import ModuleElementDetails from "pages/tasks/ModuleElementDetails";
import EmptyPage from "./EmptyPage";
import { isGuestConnected } from "utils/role";
import PopOverMembersTask from "pages/tasks/activityDetails/PopOverMembersTask";
import { familyIcons } from "pages/components/DetailsProfile/ViewSphere2";
import { SET_CONTACT_INFO_FROM_DRAWER } from "new-redux/constants";
import { Refs_IDs } from "./tour/tourConfig";

dayjs.extend(relativeTime);

const NewListVisio = () => {
  const [taskToUpdate, setTaskToUpdate] = useState(null);
  const [deletedTaskIndicator, setDeletedTaskIndicator] = useState(false);
  const [loadChangePriority, setLoadChangePriority] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [showAddRow, setShowAddRow] = useState(false);
  const [loadTasks, setLoadTasks] = useState(false);
  const [priorities, setPriorities] = useState("");
  const [openQuickVideoCall, setOpenQuickVideoCall] = useState(false);
  const [openTaskAdvanced, setOpenTaskAdvanced] = useState(false);
  const [idTask, setIdTask] = useState(null);
  const [openTour, setOpenTour] = useState(false);
  const [steps, setSteps] = useState([]);
  const [externeUpdate, setExterneUpdate] = useState(false);
  const [tasksTypes, setTasksTypes] = useState([]);
  const [pipelines, setPipelines] = useState([]);
  const [guestsListPage, setGuestsListPage] = useState(1);
  const [guestsList, setGuestsList] = useState([]);
  const [guestsListLastPage, setGuestsListLastPage] = useState(null);
  const [followersListPage, setFollowersListPage] = useState(1);
  const [ownersList, setOwnersList] = useState([]);
  const [followersListLastPage, setFollowersListLastPage] = useState(null);
  const [guestsSearchQuery, setGuestsSearchQuery] = useState("");
  const [followersSearchQuery, setFollowersSearchQuery] = useState("");
  const [showNotificationsMenu, setShowNotificationsMenu] = useState(false);
  const [activityLabel, setActivityLabel] = useState("");
  const [isCopiedToClipboard, setIsCopiedToClipboard] = useState(false);
  const [notificationsPage, setNotificationsPage] = useState(1);
  const [filterNotifications, setFilterNotifications] = useState(0);
  const [openActivity360, setOpenActivity360] = useState(false);
  const [singleTaskData, setSingleTaskData] = useState({});
  const [loadSpecificTask, setLoadSpecificTask] = useState(false);
  const [checkedItems, setCheckedItems] = useState([]);
  const [checkedFollowers, setCheckedFollowers] = useState([]);
  const [stageFilter, setStageFilter] = useState([]);
  const [loadOwners, setLoadOwners] = useState(false);
  const [loadGuests, setLoadGuests] = useState(false);
  const [loadUpdateTaskStage, setLoadUpdateTaskStage] = useState(false);
  const [fetchVisio, setFetchVisio] = useState(false);
  const [addOnsValues, setAddOnsValues] = useState({
    description: "",
    note: "",
  });
  const [totalEntities, setTotalEntities] = useState({
    colleagues: 0,
    all: 0,
  });
  const [markAllAsReadLoading, setMarkAllAsReadLoading] = useState(false);
  const [filterParticipantsValue, setFilterParticipantsValue] = useState(0);

  const [selectedPipeline, setSelectedPipeline] = useState(0);
  const [open, setOpen] = useState(false);

  const [files, setFiles] = useState([]);
  const [elementDetails, setElementDetails] = useState({
    id: null,
    module: null,
  });
  const [openElementDetails, setOpenElementDetails] = useState(false);
  const [countChanges, setCountChanges] = useState(1);
  const [selectedFamilyMembers, setSelectedFamilyMembers] = useState([1, 2, 4]);

  const { isOpen } = useSelector((state) => state.visio);
  const { openTaskRoomDrawer } = useSelector((state) => state?.TasksRealTime);
  const {
    listMeet,
    page,
    loadTabs,
    detailsMeet,
    tabKey,
    now,
    later,
    countToday,
    countUpComing,
    countHistory,
    notificationCount,
    notificationList,
    pageNotificationList,
    lastPageNotificationList,
    countReminders,
    total,
    limit,
    search,
    listKpi,
    listKpiDate,
    remindersListVisio,
  } = useSelector((state) => state.visioList);
  const { remindersList, totalNotificationNumber } = useSelector(
    (state) => state?.TasksRealTime
  );
  const { openDrawerChat } = useSelector((state) => state.voip);
  const { user } = useSelector((state) => state?.user);
  const currentUser = useSelector((state) => state?.chat?.currentUser);
  const [t] = useTranslation("common");
  const debouncedSearchValue = useDebounce(search, 500);
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const debounceGuestsSearch = useDebounce(guestsSearchQuery, 500);
  const debounceFollowersSearch = useDebounce(followersSearchQuery, 500);
  const getSpecificTask = useCallback(async () => {
    try {
      setLoadSpecificTask(true);

      const response = await MainService.getSpecificTask(taskToUpdate);
      if (response?.data?.message === "This task was deleted") {
        setDeletedTaskIndicator(true);
      } else if (response?.data?.message === "Task not found") {
        setDeletedTaskIndicator(true);
      } else {
        setSingleTaskData(response?.data?.data);
        setCheckedItems(
          response?.data?.data?.guests != null
            ? response?.data?.data?.guests
            : checkedItems
        );
        setCheckedFollowers(
          response?.data?.data?.followers !== null
            ? response?.data?.data?.followers
            : checkedFollowers
        );
        setAddOnsValues({
          ...addOnsValues,
          note: response?.data?.data?.note,
          description: response?.data?.data?.description,
        });
        setFiles(response?.data?.data?.upload);
      }
      setLoadSpecificTask(false);
    } catch (error) {
      console.log(`Error ${error}`);
      toastNotification("error", t("toasts.somethingWrong"));
      setLoadSpecificTask(false);
    }
  }, [taskToUpdate]);

  // Useless useEffect + causes a bug on open the drawer of the update.
  // useEffect(() => {
  //   if (!openTaskRoomDrawer) {
  //     setIdTask(null);
  //   }
  // }, [openTaskRoomDrawer]);
  // On search in owners/followers list, setPages to 1
  useEffect(() => {
    setFollowersListPage(1);
    // const scrollableDiv = document.getElementById("scrollableFollowersDiv");
    // if (scrollableDiv) {
    //   scrollableDiv.scrollTo({ top: 0, behavior: "smooth" });
    // }
  }, [debounceFollowersSearch]);
  useEffect(() => {
    setCountChanges(1);
  }, [debouncedSearchValue]);

  // Get owners/followers API.
  const getOwners = useCallback(
    async (signal) => {
      try {
        setLoadOwners(true);
        let formData = new FormData();
        formData.append("family_id", 4);
        formData.append("search", debounceFollowersSearch);
        const response = await MainService.getFamilyOptions(
          followersListPage,
          50,
          formData
        );
        if (followersListPage > 1) {
          setOwnersList([...ownersList, ...response?.data?.data]);
        } else {
          setOwnersList(response?.data?.data);
        }
        setTotalEntities((prev) => ({
          ...prev,
          colleagues: response?.data?.meta?.total,
        }));
        setFollowersListLastPage(response?.data?.meta?.last_page);
        setLoadOwners(false);
      } catch (error) {
        setLoadOwners(false);
        console.log(`Error ${error}`);
        toastNotification("error", t("toasts.somethingWrong"));
      }
    },
    [debounceFollowersSearch, followersListPage]
  );

  // Get owners/followers on mount.
  useEffect(() => {
    let abort = new AbortController();
    getOwners(abort?.signal);
    return () => abort?.abort();
  }, [getOwners]);

  // On search in guests list, scroll to top and setPages to 1
  useEffect(() => {
    setGuestsListPage(1);
    const scrollableDiv = document.getElementById("scrollableParticipantsDiv");
    if (scrollableDiv) {
      scrollableDiv.scrollTo({ top: 0, behavior: "smooth" });
    }
  }, [debounceGuestsSearch]);

  // Get guests API.
  const getGuests = useCallback(async () => {
    try {
      let formData = new FormData();
      formData.append("family_id", selectedFamilyMembers.toString());
      formData.append("search", debounceGuestsSearch);
      setLoadGuests(true);
      const response = await MainService.getFamilyOptions(
        guestsListPage,
        50,
        formData
      );
      if (guestsListPage > 1) {
        setGuestsList([...guestsList, ...response?.data?.data]);
      } else {
        setGuestsList(response?.data?.data);
      }
      setTotalEntities((prev) => ({
        ...prev,
        all: response?.data?.meta?.total,
      }));
      setGuestsListLastPage(response?.data?.meta?.last_page);
      setLoadGuests(false);
    } catch (error) {
      console.log(`Error ${error}`);
      toastNotification("error", t("toasts.somethingWrong"));
      setLoadGuests(false);
    }
  }, [guestsListPage, debounceGuestsSearch, selectedFamilyMembers]);

  // Get guests on mount.
  useEffect(() => {
    let abort = new AbortController();
    if (openTaskAdvanced) {
      return;
    } else if (open) {
      return;
    } else getGuests();
    return () => abort?.abort();
  }, [getGuests, guestsListPage, debounceGuestsSearch]);

  useEffect(() => {
    let abort = new AbortController();
    if (openActivity360) {
      getGuests();
    }
    return () => abort?.abort();
  }, [getGuests, openActivity360]);
  const getPipelines = async () => {
    try {
      // setLoadPipelines(true);
      const response = await MainService.getPipelinesByFamilyTask();
      setSelectedPipeline(0);
      setPipelines(response?.data?.data);
      // setLoadPipelines(false);
    } catch (error) {
      // setLoadPipelines(false);
      toastNotification("error", t("toasts.somethingWrong"));
      console.log(`Error ${error}`);
    }
  };

  useEffect(() => {
    if (taskToUpdate) {
      getSpecificTask();
    }
  }, [getSpecificTask]);

  useEffect(() => {
    const getTasksTypes = async () => {
      try {
        const response = await MainService.getTasksTypes();
        setTasksTypes(response?.data?.data?.tasks_type);
      } catch (error) {}
    };
    getTasksTypes();
    return () => {
      dispatch(setPage(1));
    };
  }, [dispatch]);

  const fetchData = async () => {
    try {
      await Promise.all([
        // onChangeTab(1),
        dispatch(
          onChangeTabVisio({
            value: tabKey,
            keyTab: 1,
            t,
            limit,
            keyMeet: "",
            search: debouncedSearchValue,
            priorities,
            // source,
            page,
            stages_ids: stageFilter?.toString(),
          })
        ),
      ]);
      setCountChanges(0);
      setFetchVisio(true);
      // Les réponses de toutes les promesses sont disponibles ici
      // Faites quelque chose avec les données
    } catch (error) {
      // Gérez les erreurs ici
    }
  };
  useEffect(() => {
    if (countChanges === 0) setCountChanges((prev) => prev + 1);
  }, [tabKey]);

  useEffect(() => {
    // Get guests API.
    // const source = axios.CancelToken.source();

    if (!openActivity360 && countChanges > 0) {
      fetchData();
    }
    return () => {
      // source.cancel();

      dispatch(setDetailsMeet({}));
      dispatch(setKeyMeet([]));
      dispatch(setKeyMeet(""));
      // dispatch(setPage(1));
    };
  }, [
    dispatch,

    limit,
    tabKey,
    page,
    priorities,
    t,
    countChanges,
    openActivity360,
    debouncedSearchValue,
    stageFilter,
  ]);
  useEffect(() => {
    getPipelines();
    dispatch({
      type: SET_CONTACT_INFO_FROM_DRAWER,
      payload: {},
    });
    dispatch(setNotificationList({ page: 1, t }));
    // dispatch(getRemindersList({ t }));

    return () => {
      dispatch(setTabKey(1));
    };
  }, [dispatch]);

  const openChat = (id) => {
    dispatch(setOpenTaskRoomDrawer(true));
    setIdTask(id);
    // setRoomActivityId(id);
  };
  const firstUpdatableTaskIndex =
    listMeet.length > 0 &&
    listMeet.findIndex((record) => record.can_update_task === 1);
  const handleDelete = async (id, start_date) => {
    const idTask = id.join();
    try {
      let formData = new FormData();
      formData.append("id[]", idTask);
      const response = await MainService.deleteSpecificTask(formData);
      if (response?.status === 200) {
        dispatch(setListMeet(listMeet.filter((el) => el.id !== idTask)));
        const currentDate = moment().format(user.location.date_format);
        const startDate = moment(start_date, user.location.date_format);
        if (start_date === currentDate) {
          dispatch(setNow({ now: now, countToday: countToday - 1 }));
        }
        if (startDate.isAfter(currentDate, "day")) {
          dispatch(
            setLater({ later: later, countUpComing: countUpComing - 1 })
          );
        }
        if (startDate.isBefore(currentDate, "day")) {
          dispatch(setHistory({ countHistory: countHistory - 1 }));
        }
        dispatch(setDetailsMeet({}));
        toastNotification("success", t("toasts.taskDeleted"), "bottomRight");
      }
    } catch (error) {
      console.log(`Error ${error}`);
      toastNotification("error", t("toasts.somethingWrong"));
    }
  };

  const editTask = (props) => {
    setIdTask(props.id);
    dispatch(setDetailsMeet(props));
    setActivityLabel(props?.label);
    // Add 'setOpen(true)' here rather than in useEffect.
    setOpen(true);
  };

  const copyIdToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      setIsCopiedToClipboard(true);
      setTimeout(() => setIsCopiedToClipboard(false), 2000);
    } catch (err) {
      console.error("Failed to copy: ", err);
    }
  };

  // Change Function UpdateTaskStage from KanbanBoard to TasksWrapper to use it in TasksTableView
  const UpdateTaskStage = async (payload, source) => {
    try {
      setLoadTasks(true);
      await MainService.updateTaskStageInKanban(payload);
      fetchData();
      setLoadUpdateTaskStage(false);
    } catch (error) {
      console.log(`Error ${error}`);
      setLoadTasks(false);
      setLoadUpdateTaskStage(false);
    }
  };

  // Pipelines and stages options array
  const stagesLists =
    pipelines &&
    pipelines?.map((pipeline) => ({
      label: pipeline?.label,
      value: pipeline?.pipeline_key,
      options:
        pipeline?.stages &&
        pipeline?.stages?.map((stage) => ({
          label: (
            <>
              <Badge color={stage?.color} style={{ marginRight: "10px" }} />
              {stage?.label}{" "}
              {stage?.percent &&
              stage?.percent !== null &&
              Number(stage?.percent) !== 0 ? (
                <span className="text-[#8c8c8c]">{`(${stage?.percent}%)`}</span>
              ) : null}
            </>
          ),
          value: stage?.id,
        })),
    }));

  const defaultColumns = [
    {
      title: t("tasks.tableLabel"),
      dataIndex: "label",
      key: "label",

      fixed: "left",
      width: 270,
      primary: true,
      render: (_, props, index) => (
        <div className="flex justify-between">
          <Typography.Link
            ellipsis
            onClick={() => {
              // setActivityLabel(props?.label);
              // dispatch(setDetailsMeet(props));
              // setIdTask(props?.id);
              setOpenActivity360(true);
              setTaskToUpdate(props?.id);
            }}
          >
            {props?.label}
          </Typography.Link>
          {props?.visio_in_progress && props?.visio_in_progress === 1 ? (
            <Tooltip title={t("tasks.visioInProgress")}>
              <MdVideoCameraFront className="ml-2  animate-pulse text-sm text-red-500" />
            </Tooltip>
          ) : null}
          <div
            className="ml-auto flex items-center"
            ref={index === 0 ? Refs_IDs.MoreTask : null}
          >
            <DropdownTask
              props={props}
              handleDelete={handleDelete}
              editTask={editTask}
              handleOpenActivityIn360={handleOpenActivityIn360}
              openChat={openChat}
            />
          </div>
        </div>
      ),
    },
    {
      title: t("tasks.tablePriority"),
      dataIndex: "priority",
      key: "priority",
      width: 90,
      align: "center",

      render: (_, props, index) => (
        <Dropdown
          menu={{
            items: prioritiesList(),
            onClick: (e) => updatePriority(props?.id, { priority: e?.key }),
            selectedKeys: [props?.priority],
          }}
          trigger={["click"]}
          open={Boolean(props?.can_update_task === 0) ? false : undefined}
        >
          <span ref={index === 0 ? Refs_IDs.PriorityTask : null}>
            {props?.priority ? (
              <Tooltip title={handlePriorityLabelOnHover(props?.priority)}>
                <TbFlag3Filled
                  style={{
                    fontSize: "18px",
                    cursor:
                      props?.can_update_task === 0 ? "not-allowed" : "pointer",
                    color: displayPriorityColor(props?.priority),
                  }}
                />
              </Tooltip>
            ) : (
              <Tooltip title={t("tasks.setPriority")}>
                <TbFlag3
                  style={{
                    fontSize: "18px",
                    cursor:
                      props?.can_update_task === 0 ? "not-allowed" : "pointer",
                    color: "#bfbfbf",
                  }}
                />
              </Tooltip>
            )}
          </span>
        </Dropdown>
      ),
      filters: [
        {
          text: <Badge color="#bfbfbf" text={t("tasks.lowPriority")} />,
          value: "low",
        },
        {
          text: <Badge color="#69b1ff" text={t("tasks.mediumPriority")} />,
          value: "medium",
        },
        {
          text: <Badge color="#ffc53d" text={t("tasks.highPriority")} />,
          value: "high",
        },
        {
          text: <Badge color="#f5222d" text={t("tasks.urgentPriority")} />,
          value: "urgent",
        },
      ],
      onFilter: (value, record) => record.priority == value,
    },
    {
      title: t("tasks.startDate"),
      dataIndex: "startDate",
      key: "startDate",
      width: 120,

      render: (_, props) =>
        props?.start_date !== null && (
          <Tooltip title={`${props?.start_date} ${props?.start_time}`}>
            {formatDateForDisplay(
              `${props?.start_date} ${props?.start_time}`,
              `${user?.location?.date_format} ${user?.location?.time_format}`,
              user,
              t
            )}
          </Tooltip>
        ),
      /* : (
          <Typography.Link
            onClick={() => {
              editTask(props);
            }}
          >
            {t("tasks.addDate")}
          </Typography.Link>
        ), */
    },
    {
      title: t("tasks.tableEndDate"),
      dataIndex: "dueDate",
      key: "dueDate",
      width: 120,

      render: (_, record) =>
        ///!loadTasks ? (
        record?.end_date != null && (
          <Tooltip title={`${record?.end_date} ${record?.end_time}`}>
            <Typography.Text type={record?.is_overdue && "danger"}>
              {formatDateForDisplay(
                `${record?.end_date} ${record?.end_time}`,
                `${user?.location?.date_format} ${user?.location?.time_format}`,
                user,
                t
              )}
            </Typography.Text>
          </Tooltip>
        ),
      /* : (
            <Typography.Link
              onClick={() => {
                editTask(record);
              }}
            >
              {t("tasks.addDate")}
            </Typography.Link>
          ) */
      /* ) : (
          <Skeleton.Button className="w-full" />
        ), */
    },
    {
      title: "Pipeline/Stage",
      dataIndex: "pipeline",
      key: "pipeline",
      width: 180,
      render: (_, record, index) => {
        return (
          <span ref={index === 0 ? Refs_IDs.PipelineTask : null}>
            <Select
              suffixIcon={
                loadUpdateTaskStage ? (
                  <LoadingOutlined
                    style={{ color: "#000", fontSize: "10px" }}
                  />
                ) : (
                  <DownOutlined
                    style={{
                      color: "#000",
                      fontSize: "10px",
                      pointerEvents: "none",
                    }}
                  />
                )
              }
              style={{
                width: "100%",
                padding: 0,
                backgroundColor: "#0000000a",
                borderRadius: "6px",
              }}
              placeholder={t("tasks.choose")}
              bordered={false}
              value={
                record?.stage_id
                  ? {
                      label: (
                        <div>
                          <span>{record?.pipeline_label}</span> /{" "}
                          <span
                            style={{
                              color: pipelines
                                ?.find(
                                  (el) => el.label === record?.pipeline_label
                                )
                                ?.stages?.find(
                                  (item) => item.id === record?.stage_id
                                )?.color,
                            }}
                          >
                            {record?.stage_label}
                          </span>{" "}
                        </div>
                      ),
                      value: record?.stage_id,
                    }
                  : null
              }
              onChange={(e) => {
                UpdateTaskStage(
                  {
                    "task_id[]": record?.id,
                    new_stage_id: e,
                  },
                  "singleUpdate"
                );
              }}
              options={stagesLists}
              popupMatchSelectWidth={false}
              disabled={record?.is_follower === 1}
            />
          </span>
        );
      },
      /* filteredValue:
        source === "viewSphere"
          ? false
          : tasksFilters &&
            tasksFilters?.filters &&
            tasksFilters?.filters?.pipeline &&
            tasksFilters?.filters?.pipeline.map((el) => `${el}`), */
      filters:
        pipelines &&
        pipelines.map((pipeline) => ({
          text: pipeline?.label,
          value: pipeline?.pipeline_key,
          disabled: true,
          disableCheckbox: true,
          children:
            pipeline?.stages &&
            pipeline?.stages.map((stage) => ({
              text: stage?.label,
              value: stage?.id,
            })),
        })),
      filterMode: "tree",
    },
    {
      title: t("tasks.creatorRole"),
      dataIndex: "creator",
      key: "creator",
      width: 130,
      render: (_, record) => (
        <ActionsComponent elementValue={record?.creator}>
          <AvatarChat
            fontSize={"0.875rem"}
            className={"mx-1.5 flex items-center justify-center"}
            height={"32px"}
            width={"32px"}
            url={`${
              URL_ENV?.REACT_APP_BASE_URL + URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
            }${record?.creator?.avatar}`}
            hasImage={EXTENSIONS_ARRAY?.includes(
              record?.creator?.avatar?.split(".")?.pop()
            )}
            name={getName(record?.creator?.label, "avatar")}
            type="user"
          />
        </ActionsComponent>
      ),
    },
    {
      title: t("tasks.tableOwner"),
      dataIndex: "owner",
      key: "owner",
      width: 130,
      render: (_, record, index) => (
        <div className="flex items-center">
          <ActionsComponent elementValue={record?.owner_id}>
            <AvatarChat
              fontSize={"0.875rem"}
              className="mx-1.5 flex items-center justify-center"
              height={"32px"}
              width={"32px"}
              url={`${
                URL_ENV?.REACT_APP_BASE_URL +
                URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
              }${record?.owner_id?.avatar}`}
              hasImage={EXTENSIONS_ARRAY?.includes(
                record?.owner_id?.avatar?.split(".")?.pop()
              )}
              name={getName(record?.owner_id?.label, "avatar")}
              type="user"
            />
          </ActionsComponent>
          {record?.can_update_task === 1 ? (
            <span
              ref={index === firstUpdatableTaskIndex ? Refs_IDs.Owner : null}
            >
              <PopOverMembersTask
                from="columnTable"
                keyColumn="followers"
                type="visio"
                usersList={ownersList}
                searchQuery={followersSearchQuery}
                setSearchQuery={setFollowersSearchQuery}
                lastPage={followersListLastPage}
                currentPage={followersListPage}
                setCurrentPage={setFollowersListPage}
                checkedItems={checkedItems}
                loading={loadOwners}
                setTasksData={setListMeet}
                multipleAddMember={false}
                defaultOwner={record?.owner_id?.id}
                setCheckedItems={setCheckedItems}
                dispatchRemoveMembers={() => {}}
                setSelectedFamilyMembers={setSelectedFamilyMembers}
                setFilterParticipantsValue={setFilterParticipantsValue}
                filterParticipantsValue={filterParticipantsValue}
                totalEntities={totalEntities}
                tasksData={listMeet}
                item={record}
              />
            </span>
          ) : null}
        </div>
      ),
    },
    {
      title: t("tasks.tableGuests"),
      dataIndex: "guests",
      key: "guests",
      width: 150,
      render: (_, record, index) => (
        <>
          <div className="flex items-center gap-1">
            {record?.guests && record?.guests.length > 0 ? (
              <AvatarGroup
                source="taskTable"
                usersArray={record?.guests}
                uncheckUser={() => {}}
                disableDelete={true}
              />
            ) : null}
            {record?.can_update_task === 1 ? (
              <span
                ref={index === firstUpdatableTaskIndex ? Refs_IDs.Guest : null}
              >
                <PopOverMembersTask
                  from="columnTable"
                  // key={props?.id}
                  // key={"guestsList"}
                  type="visio"
                  keyColumn="guests"
                  usersList={guestsList}
                  searchQuery={guestsSearchQuery}
                  setSearchQuery={setGuestsSearchQuery}
                  lastPage={guestsListLastPage}
                  currentPage={guestsListPage}
                  setCurrentPage={setGuestsListPage}
                  checkedItems={checkedItems}
                  loading={loadGuests}
                  setTasksData={setListMeet}
                  setCheckedItems={setCheckedItems}
                  dispatchRemoveMembers={() => {}}
                  setSelectedFamilyMembers={setSelectedFamilyMembers}
                  setFilterParticipantsValue={setFilterParticipantsValue}
                  filterParticipantsValue={filterParticipantsValue}
                  totalEntities={totalEntities}
                  item={record}
                  tasksData={listMeet}
                />
              </span>
            ) : null}
          </div>
        </>
      ),
    },
    {
      title: t("tasks.followersListTitle"),
      dataIndex: "followers",
      key: "followers",
      width: 150,
      render: (_, record, index) => (
        <div className="flex items-center gap-1">
          {record?.followers && record?.followers.length > 0 ? (
            <AvatarGroup
              source="taskTable"
              usersArray={record?.followers}
              uncheckUser={() => {}}
              disableDelete={true}
            />
          ) : null}
          {record?.can_update_task === 1 ? (
            <span
              ref={index === firstUpdatableTaskIndex ? Refs_IDs.Follower : null}
            >
              <PopOverMembersTask
                from="columnTable"
                keyColumn="followers"
                type="visio"
                usersList={ownersList}
                searchQuery={followersSearchQuery}
                setSearchQuery={setFollowersSearchQuery}
                lastPage={followersListLastPage}
                currentPage={followersListPage}
                setCurrentPage={setFollowersListPage}
                checkedItems={checkedItems}
                loading={loadOwners}
                setTasksData={setListMeet}
                setCheckedItems={setCheckedItems}
                dispatchRemoveMembers={() => {}}
                setSelectedFamilyMembers={setSelectedFamilyMembers}
                setFilterParticipantsValue={setFilterParticipantsValue}
                filterParticipantsValue={filterParticipantsValue}
                totalEntities={totalEntities}
                item={record}
                tasksData={listMeet}
              />
            </span>
          ) : null}
        </div>
      ),
    },
    // {
    //   title: t("tasks.activityId"),
    //   dataIndex: "activityId",
    //   key: "activityId",
    //   width: 150,
    //   render: (_, record) => (
    //     <div className="group/item flex flex-row items-center justify-between">
    //       <Typography.Text ellipsis={{ tooltip: record?.id }}>
    //         {record?.id}
    //       </Typography.Text>
    //       <span
    //         className="group/icon invisible hover:cursor-pointer group-hover/item:visible"
    //         onClick={() => copyIdToClipboard(`ActivityID_${record?.id}`)}
    //       >
    //         {isCopiedToClipboard ? (
    //           <CheckOutlined style={{ color: "#73d13d" }} />
    //         ) : (
    //           <CopyOutlined style={{ color: "#1890ff" }} />
    //         )}
    //       </span>
    //     </div>
    //   ),
    // },
    {
      title: t("tasks.assignment"),
      dataIndex: "moduleElement",
      key: "moduleElement",
      width: 130,
      render: (_, record) => {
        return (
          record?.element_label && (
            <Tooltip
              title={t("tasks.moduleElement", {
                elementLabel: record?.element_label,
              })}
            >
              <Tag
                color="magenta"
                className="module-tag-text hover:cursor-pointer"
                onClick={() => handleClickOnElement(record)}
                style={{
                  height: "25px",
                  display: "flex",
                  alignItems: "center",
                }}
                bordered={false}
                icon={
                  <span className="relative top-[1px] pr-1">
                    {" "}
                    {
                      familyIcons(t).find((el) => el.key === record?.family_id)
                        ?.icon
                    }
                  </span>
                }
              >
                <span className="flex w-[100px] items-center truncate">
                  <span className="truncate">{`${record?.family_label}/${record?.element_label}`}</span>
                </span>{" "}
              </Tag>
            </Tooltip>
          )
        );
      },
    },
    {
      title: "",
      dataIndex: "actions",
      key: "actions",
      fixed: "right",
      width: 150,
      align: "center",
      render: (_, props, index) => (
        <Button
          ref={0 === index ? Refs_IDs.Participate : null}
          style={{ width: "auto" }}
          onClick={() =>
            dispatch(
              getTokenRoom({
                room: props.location,
                errorText1: t("toasts.errorFetchApi"),
                errorText2: t("toasts.errorRoomNotFound"),
              })
            )
          }
          block
          type="primary"
          icon={<VideoCameraOutlined />}
        >
          {t("visio.participate")}
        </Button>
      ),
    },
  ];

  const handleClickOnElement = (record) => {
    getContactDataAndDispatch(
      record?.family_id,
      record?.element_label,
      { key: record?.element_id },
      record,
      dispatch,
      null,
      () => {}
    );
    setElementDetails((prev) => ({
      ...prev,
      id: record?.element_id,
      module: record?.family_id,
    }));
    setOpenElementDetails(true);
  };

  const updatePriority = async (id, newPriorityStatus) => {
    try {
      setLoadChangePriority(true);
      const response = await MainService.updateTaskPriority(
        id,
        newPriorityStatus
      );
      if (response?.status === 200) {
        setLoadChangePriority(false);
        message.success(t("tasks.updatePriorityMsg"), 4.5);
        let itemIndex =
          listMeet && listMeet.findIndex((element) => element?.id == id);
        let newArray =
          listMeet &&
          listMeet.map((element, i) =>
            i === itemIndex ? response?.data?.data : element
          );
        dispatch(setListMeet(newArray));
      }
    } catch (error) {
      setLoadChangePriority(false);
      console.log(`Error ${error}`);
      message.error("Something went wrong!", 4.5);
    }
  };

  const onChange = (pagination, filters, sorter, extra) => {
    if (filters?.priority && filters?.priority?.length > 0) {
      setPriorities(filters?.priority?.join(","));
    } else if (filters?.pipeline && filters?.pipeline?.length > 0) {
      setStageFilter(filters?.pipeline);
    } else if (filters?.pipeline === null) {
      setStageFilter([]);
    } else setPriorities("");
  };

  const changeTab = (e) => {
    setCountChanges(1);
    dispatch(setPage(1));
    dispatch(setTabKey(e));
    setSelectedRowKeys([]);
  };

  const onSelectChange = (newSelectedRowKeys, record) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };
  const handleOpenActivityIn360 = (props) => {
    setTaskToUpdate(props?.id);
    setOpenActivity360(true);
  };
  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
    getCheckboxProps: (record) => ({
      disabled: record?.can_update_task === 0,
    }),
  };
  const markNotificationAsRead = async (type, payload) => {
    try {
      const response = await MainService.markNotificationAsRead(payload);
      let itemIndex =
        notificationList &&
        notificationList.findIndex((notif) =>
          notif?.id && notif?.id === payload?.log_id
            ? payload?.log_id
            : !notif?.id && notif?.id_data === payload?.task_id
            ? payload?.task_id
            : null
        );
      const res = await MainService.getNumberOfNotifications(page);

      if (filterNotifications === 1) {
        // Filter item by id not index.
        let newArray =
          notificationList &&
          notificationList.filter(
            (notification, i) => response?.data?.data?.id !== notification?.id
          );
        dispatch(changeListNotifs(newArray));
        dispatch(setTotalNotificationsNumber(res.data.task_count));
      } else if (type === "reminder") {
        let reminder =
          remindersListVisio?.data &&
          remindersListVisio?.data?.find(
            (item) => item?.id === payload?.log_id
          );
        if (reminder) {
          dispatch(
            setRemindersList({
              ...remindersListVisio,
              meta: {
                ...remindersListVisio?.meta,
                total: remindersListVisio?.meta.total - 1,
              },
              data: remindersListVisio?.data?.filter(
                (el) => el?.id !== reminder?.id
              ),
            })
          );
          dispatch(setCountReminders(countReminders - 1));
          dispatch(
            setRemindersArray({
              ...remindersList,
              meta: {
                ...remindersList?.meta,
                total: remindersList?.meta.total - 1,
              },
              data: remindersList?.data?.filter(
                (el) => el?.id !== reminder?.id
              ),
            })
          );
        }
      } else {
        let newArray =
          notificationList &&
          notificationList.map((notification, i) =>
            i === itemIndex ? response?.data?.data : notification
          );
        dispatch(changeListNotifs(newArray));
        dispatch(setCountNotificationVisio(notificationCount - 1));
        dispatch(setTotalNotificationsNumber(totalNotificationNumber - 1));
      }
      // setUnreadNotifications(unreadNotifications - 1);
    } catch (error) {
      console.log(`Error ${error}`);
    }
  };

  const markAllNotifsAsRead = async (reminder) => {
    setMarkAllAsReadLoading(true);

    try {
      const response = await MainService.markAllNotificationsAsRead(
        3,
        reminder
      );

      if (response?.status === 200) {
        const res = await MainService.getNumberOfNotifications(page);

        if (reminder === "visio") {
          dispatch(setCountReminders(0));
          dispatch(
            setRemindersList({
              ...remindersListVisio,
              meta: { current_page: 1, last_page: 1, total: 0 },
              data: [],
            })
          );
          dispatch(setAllNotifsAsRead(notificationList));

          dispatch(
            setRemindersArray({
              ...res.data.reminders,
            })
          );
        } else {
          // dispatch(setTotalNotificationsNumber(0));

          dispatch(setCountNotificationVisio(0));
          dispatch(setAllNotifsAsRead(notificationList));
          dispatch(setTotalNotificationsNumber(res.data.task_count));
        }
        setMarkAllAsReadLoading(false);
      }
    } catch (error) {
      setMarkAllAsReadLoading(false);

      console.log(`Error ${error}`);
    }
  };
  const onChangePage = async (page, filters) => {
    setCountChanges(1);
    dispatch(setPage(page));
  };

  const deleteTask = async (ids) => {
    let formData = new FormData();
    ids &&
      ids.forEach((id) => {
        return formData.append("id[]", id);
      });
    try {
      const response = await MainService.deleteSpecificTask(formData);
      if (response?.status === 200) {
        if (window.location.pathname.includes("visio")) {
          let kpi = { data: [] };
          kpi = await MainService.getKpiVisio(tabKey);
          dispatch(
            setKpiVisio(
              Object.entries(kpi.data).map(([key, value]) => {
                return { title: displayStatKey(t, key, "visio"), value: value };
              })
            )
          );
        }
        let newArr =
          listMeet &&
          listMeet.filter((element) => ids && ids.indexOf(element?.id) === -1);
        dispatch(setListMeet(newArr));
        setSelectedRowKeys([]);
        if (tabKey == 1) {
          dispatch(setNow({ now: now, countToday: countToday - ids?.length }));
        }
        if (tabKey == 2) {
          dispatch(
            setLater({
              later: later,
              countUpComing: countUpComing - ids?.length,
            })
          );
        }
        if (window.location.pathname.includes("visio")) {
          //update kpi in visio
          let kpi = { data: [] };
          let kpiDate = { data: [] };

          kpiDate = await MainService.getKpiDateVisio();
          kpi = await MainService.getKpiVisio(tabKey);

          dispatch(
            setKpiVisio(
              kpi &&
                kpi.data && [
                  ...Object.entries(kpi.data).map(([key, value]) => {
                    return {
                      title: displayStatKey(t, key, ""),
                      value: value,
                      tr: false,
                    };
                  }),
                ]
            )
          );
          dispatch(
            setKpiDateVisio(
              kpiDate &&
                kpiDate.data &&
                Object.entries(kpiDate.data).map(([key, value]) => {
                  return { title: key, value: value, tr: true };
                })
            )
          );
        }
        toastNotification("success", t("toasts.taskDeleted"), "bottomRight");
      }
    } catch (error) {
      console.log(`Error ${error}`);
      toastNotification("error", t("toasts.somethingWrong"));
    }
  };

  useEffect(() => {
    if (openActivity360 === true) {
      dispatch(isOverviewModalOpen(true));
    } else {
      dispatch(isOverviewModalOpen(false));
    }
  }, [openActivity360, dispatch]);

  const handleClick = (e) => {
    if (e?.key === "instantVisio") {
      setOpenQuickVideoCall(true);
      dispatch(setSearchListVisio(""));
    } else if (e?.key === "planVisio") {
      setOpenTaskAdvanced(true);
    }
  };
  return (
    <>
      {!isGuestConnected(currentUser?.role, user?.role) && (
        <Dropdown
          trigger={["click"]}
          menu={{
            items: [
              {
                label: t("visio.startInstantMeeting"),
                key: "instantVisio",
                icon: <AiOutlineVideoCameraAdd style={{ fontSize: "13px" }} />,
              },
              {
                label: t("chat.header.createVideoLater"),
                key: "planVisio",
                icon: <HiOutlineCalendar style={{ fontSize: "100%" }} />,
              },
            ],
            onClick: handleClick,
          }}
        >
          <Button
            className="fixed right-6 top-3 z-50"
            ref={Refs_IDs.CreateTask}
            icon={<PlusOutlined />}
            disabled={isOpen}
            type="primary"
          >
            {t("visio.CreateVisioBtn")} <DownOutlined />
          </Button>
        </Dropdown>
      )}
      <Space direction="vertical" style={{ width: "100%" }}>
        <div className="flex  flex-wrap gap-2 px-4 pt-4">
          {listKpi.map((el, i) =>
            loadTabs && listKpi.some((el) => typeof el.value !== "number") ? (
              // <Card style={{ width: "12.5%" }}>
              <div
                className="skeletonSelectDepartments grow"
                key={`skeletonSelectDepartments-${i}`}
              >
                <Skeleton.Input active={true} block />
              </div>
            ) : (
              // </Card>
              <CardStat
                key={`skeletonSelectDepartments-CardStat-${i}`}
                item={{
                  title: el.tr ? t(`visio.${el.title}`) : el.title,
                  value: el.value,
                }}
              />
            )
          )}
        </div>
        <div className=" flex w-full justify-between p-4">
          <div className="flex grow flex-wrap items-center gap-2">
            <Segmented
              ref={Refs_IDs.ChoiceDate}
              options={[
                {
                  label: (
                    <>
                      {t("visio.today")}{" "}
                      {countToday > 0 ? "(" + countToday + ")" : ""}
                    </>
                  ),
                  value: 1,
                },
                {
                  label: (
                    <>
                      {t("visio.upComing")}{" "}
                      {countUpComing > 0 ? "(" + countUpComing + ")" : ""}
                    </>
                  ),
                  value: 2,
                },
                {
                  label: t("visio.history"),
                  value: 0,
                },
              ]}
              value={tabKey}
              onChange={changeTab}
              disabled={loadTabs}
            />
            <span ref={Refs_IDs.Search}>
              <Input
                style={{ width: "250px" }}
                placeholder={t("table.search")}
                onChange={(e) => {
                  dispatch(setSearchListVisio(e.target.value));
                }}
                value={search}
                // ref={refSearch}
                allowClear
                suffix={
                  <Tooltip title={t("visio.searchInfo")}>
                    <InfoCircleOutlined style={{ color: "#1890ff" }} />
                  </Tooltip>
                }
              />
            </span>
            {listKpiDate.map((el, i) =>
              loadTabs &&
              listKpiDate.some((el) => typeof el.value !== "number") ? (
                // <Card style={{ width: "12.5%" }}>
                <div
                  className="skeletonSelectDepartments grow"
                  key={`secondSkeletonSelectDepartments-${i}`}
                >
                  <Skeleton.Input active={true} block />
                </div>
              ) : (
                // </Card>
                <CardStat
                  key={`secondSkeletonSelectDepartments-CardStat-${i}`}
                  item={{
                    title: el.tr ? t(`visio.${el.title}`) : el.title,
                    value: el.value,
                  }}
                />
              )
            )}
          </div>
          <div className=" flex items-center space-x-1 pl-1">
            <div>
              <Badge
                count={notificationCount + countReminders}
                size="small"
                style={{ top: "10px", right: "10px" }}
              >
                <Tooltip title={"Notifications"}>
                  <Popover
                    placement="bottom"
                    content={
                      <NotificationsPopover
                        notificationsList={notificationList}
                        notificationsPage={pageNotificationList}
                        setTaskToUpdate={setTaskToUpdate}
                        setNotificationsPage={setNotificationsPage}
                        lastNotificationsPage={lastPageNotificationList}
                        markNotificationAsRead={markNotificationAsRead}
                        markAllNotifsAsRead={markAllNotifsAsRead}
                        source="notifications"
                        from="listVisio"
                        setFilterNotifications={setFilterNotifications}
                        filterNotifications={filterNotifications}
                        setShowNotificationsMenu={setShowNotificationsMenu}
                        setOpen={setShowNotificationsMenu}
                        markAllAsReadLoading={markAllAsReadLoading}
                        setOpenActivity360={setOpenActivity360}
                      />
                    }
                    trigger={["click"]}
                    open={showNotificationsMenu}
                    onOpenChange={(open) => {
                      // if (!open) {
                      //   markAllNotifsAsRead();
                      // }
                      setShowNotificationsMenu(open);
                    }}
                  >
                    <Button
                      ref={Refs_IDs.Notif}
                      icon={<BellOutlined />}
                      shape="circle"
                      type="text"
                      size="large"
                    />
                  </Popover>
                </Tooltip>
              </Badge>
            </div>
          </div>
        </div>

        {selectedRowKeys.length > 0 && (
          <div className="px-4 pb-1">
            <Button
              danger
              icon={<DeleteOutlined />}
              type="primary"
              onClick={() =>
                Confirm(
                  t("tasks.deleteTaskModal", {
                    plural:
                      selectedRowKeys && selectedRowKeys.length > 1 ? "s" : "",
                  }),
                  "Confirm",
                  <RestOutlined style={{ color: "red" }} />,
                  function func() {
                    return deleteTask(selectedRowKeys && selectedRowKeys);
                  },
                  true
                )
              }
            >
              {t("tasks.deleteBtn", {
                s: selectedRowKeys.length,
                plural: selectedRowKeys.length > 1 ? "s" : "",
              })}
            </Button>
          </div>
        )}

        {listMeet?.length === 0 &&
        !loadTabs &&
        priorities === "" &&
        !isGuestConnected(currentUser?.role, user?.role) ? (
          <EmptyPage
            heroTitle={
              <span>
                {t("tasks.startCreateActivities")}{" "}
                <Typography.Link>{t("visio.visioMeet")}</Typography.Link>
              </span>
            }
            mainBtnTitle={t("visio.CreateVisioBtn")}
            handleMainBtnClick={() => setOpenTaskAdvanced(true)}
          />
        ) : (
          <div className="activities-table px-[14px]">
            <Table
              rowSelection={rowSelection}
              columns={defaultColumns.filter((col) => !col?.hidden)}
              dataSource={listMeet}
              showHeader={showAddRow ? false : true}
              pagination={{
                total:
                  tabKey == 1
                    ? countToday
                    : tabKey == 2
                    ? countUpComing
                    : countHistory,
                showTotal: (total, range) =>
                  t("tasks.tablePagination", {
                    range: `${range[0]}-${range[1]}`,
                    totalItems: total,
                  }),
                onChange: (page) => {
                  onChangePage(page);
                },
                onShowSizeChange: (current, size) => {
                  dispatch(setLimitVisio(size));
                },
                pageSizeOptions: [10, 20],
                pageSize: limit || 10,
                showSizeChanger: total > 10,
                current: page,
              }}
              rowClassName="task-table-row"
              loading={loadTabs}
              size="small"
              locale={{
                triggerDesc: t("tasks.descendSort"),
                triggerAsc: t("tasks.ascendSort"),
                cancelSort: t("tasks.cancelSort"),
              }}
              scroll={{
                y: "calc(100vh - 300px)",
              }}
              onChange={onChange}
            />
          </div>
        )}

        {openQuickVideoCall && (
          <ShareVisio
            open={openQuickVideoCall}
            onClose={() => setOpenQuickVideoCall(false)}
          />
        )}

        {/* Remove  '&& !openTaskRoomDrawer' from the condition because it prevents the room drawer from opening
            while the update drawer is open
        */}

        {!openTaskAdvanced && idTask ? (
          <UpdateTask
            from="home"
            id={idTask}
            externeUpdate={externeUpdate}
            setId={setIdTask}
            data={{
              ...detailsMeet,
              upload:
                detailsMeet.upload == 0
                  ? null
                  : Array.isArray(detailsMeet?.upload)
                  ? detailsMeet?.upload
                  : detailsMeet?.files,
            }}
            source="visio"
            tasksTypes={tasksTypes}
            pipelines={pipelines}
            ownersList={ownersList}
            setOwnersList={setOwnersList}
            guestsList={guestsList}
            setGuestsList={setGuestsList}
            guestsListPage={guestsListPage}
            setGuestsListPage={setGuestsListPage}
            followersListPage={followersListPage}
            setFollowersListPage={setFollowersListPage}
            followersListLastPage={followersListLastPage}
            setFollowersListLastPage={setFollowersListLastPage}
            setGuestsListLastPage={setGuestsListLastPage}
            guestsListLastPage={guestsListLastPage}
            setFollowersSearchQuery={setFollowersSearchQuery}
            followersSearchQuery={followersSearchQuery}
            setGuestsSearchQuery={setGuestsSearchQuery}
            guestsSearchQuery={guestsSearchQuery}
            activityLabel={activityLabel}
            open={open}
            setOpen={setOpen}
          />
        ) : null}
        {/* {!idTask ? ( */}
        <CreateTask
          titleLabel={"Visio Conf " + moment().format("DDMMYYYY HH:mm:ss")}
          open={openTaskAdvanced}
          source="visio"
          setOpen={setOpenTaskAdvanced}
          mask={true}
          listVisio={true}
        />
        {/* ) : null} */}
      </Space>
      <Activity360
        key={taskToUpdate ? taskToUpdate : 1}
        openActivity360={openActivity360}
        setOpenActivity360={setOpenActivity360}
        taskToUpdate={taskToUpdate}
        singleTaskData={singleTaskData}
        setSingleTaskData={setSingleTaskData}
        loadSpecificTask={loadSpecificTask}
        tasksTypes={tasksTypes}
        setTaskToUpdate={setTaskToUpdate}
        pipelines={pipelines}
        guestsList={guestsList}
        checkedItems={checkedItems}
        guestsSearchQuery={guestsSearchQuery}
        setGuestsSearchQuery={setGuestsSearchQuery}
        guestsListPage={guestsListPage}
        setGuestsListPage={setGuestsListPage}
        guestsListLastPage={guestsListLastPage}
        setCheckedItems={setCheckedItems}
        ownersList={ownersList}
        checkedFollowers={checkedFollowers}
        setCheckedFollowers={setCheckedFollowers}
        setFollowersSearchQuery={setFollowersSearchQuery}
        loadOwners={loadOwners}
        loadGuests={loadGuests}
        addOnsValues={addOnsValues}
        setAddOnsValues={setAddOnsValues}
        files={files}
        setFiles={setFiles}
        countChanges={countChanges}
        setCountChanges={setCountChanges}
        setSelectedFamilyMembers={setSelectedFamilyMembers}
        form={form}
        deletedTaskIndicator={deletedTaskIndicator}
        source="visio"
        totalEntities={totalEntities}
        setOwnersList={setOwnersList}
        followersListPage={followersListPage}
        setFollowersListPage={setFollowersListPage}
        followersSearchQuery={followersSearchQuery}
      />
      <ModuleElementDetails
        key={elementDetails?.id}
        openElementDetails={openElementDetails}
        setOpenElementDetails={setOpenElementDetails}
        setElementDetails={setElementDetails}
        elementDetails={elementDetails}
      />
      {idTask && !openTaskAdvanced && !openDrawerChat && openTaskRoomDrawer ? (
        <TasksRoom key={idTask} elementId={idTask} />
      ) : (
        ""
      )}
    </>
  );
};

export default NewListVisio;
