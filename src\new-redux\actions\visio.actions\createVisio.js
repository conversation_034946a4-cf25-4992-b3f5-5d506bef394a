import {
  CREATE_VISIO_SUCCESS,
  CREATE_VISIO_ERROR,
  IS_LOADING_VISIO,
  MAKE_VISIO_FLOAT,
  TOGGLE_VISIO,
  TOGGLE_SIZE_VISIO,
  SET_VISIO_PARAMS,
} from "../../constants";
import MainService from "../../../services/main.service";
import { toastNotification } from "../../../components/ToastNotification";
import axios from "axios";
import i18next from "i18next";
import { setTask360 } from "../chat.actions/Input";
import { store } from "../../store";
import { URL_ENV } from "index";

export const createVisio = (payload) => async (dispatch) => {
  try {
    dispatch({ type: IS_LOADING_VISIO });
    const response = await MainService.createVisio(payload);
    dispatch({
      type: CREATE_VISIO_SUCCESS,
      payload: response?.data?.data,
    });
  } catch (error) {
    dispatch({
      type: CREATE_VISIO_ERROR,
      payload: error?.response,
    });
    toastNotification(
      "error",
      // "Something went wrong, please try again!",
      "You don't have the power to accomplish this action!",
      "topRight"
    );
  }
};
export const makeVisioFloat = (payload) => async (dispatch) => {
  dispatch({
    type: MAKE_VISIO_FLOAT,
    payload,
  });
};
export const toggleVisio = (payload) => async (dispatch) => {
  dispatch({
    type: TOGGLE_VISIO,
    payload,
  });
};
export const resizeVisio = (payload) => async (dispatch) => {
  dispatch({
    type: TOGGLE_SIZE_VISIO,
    payload,
  });
};
export const setVisoParams = (payload) => async (dispatch) => {
  dispatch({
    type: SET_VISIO_PARAMS,
    payload,
  });
};

export const getTokenRoom =
  ({ room, external = false, errorText1, errorText2 }) =>
  async (dispatch) => {
    const user = await store.getState().user?.user;

    try {
      let response;
      const config = {
        method: "GET",
        url: `${
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        }get-meet-link-guest/${room}`,
        headers: {
          Authorization: process.env.REACT_APP_API_VISIO_KEY,
          "Content-Type": "application/json",
        },
      };

      if (external) response = await axios(config);
      else response = await MainService.getMeetLinkParticipant(room);
      if (response.status === 200) {
        dispatch(toggleVisio(true));
        if (
          !external &&
          user &&
          user?.access &&
          user?.access["visio"] === "0"
        ) {
          localStorage.removeItem("lastHref");

          return;
        }
        dispatch(
          setVisoParams({
            visio_name: response?.data?.message?.room_info?.label,
            external,
            moderator: response?.data?.message?.moderator === 1,
            token: response?.data?.message?.token,
            name: room,
            owner_id: response?.data?.message?.room_info?.owner_id,
            id_visio: response?.data?.message?.room_name,
            room_status: "open",
            creatorId: response?.data?.message?.room_info?.creator,
            visioStartDate: `${response?.data?.message?.room_info?.start_date} ${response?.data?.message?.room_info?.start_time}`,
            participants: response?.data?.message?.room_info?.participants,
          })
        );
        Promise.resolve("success");
        return true;
      } else {
        Promise.reject("error");
        return true;
      }
    } catch (error) {
      localStorage.removeItem("lastHref");

      if (
        error?.response?.status === 404 &&
        error?.response.data?.message === "Room not found"
      )
        toastNotification("error", errorText2, "topRight");
      else if (error?.response.data?.message === "Room is expired") {
        if (external) {
          dispatch(
            setVisoParams({
              external,
              name: room,
              room_status: "expired",
            })
          );
          Promise.resolve("success");
          return true;
        } else
          toastNotification(
            "error",
            i18next.t("common:visio.roomExpired"),
            "topRight"
          );
      } else if (error?.response.data?.message === "Room is closed") {
        if (external) {
          dispatch(
            setVisoParams({
              external,
              name: room,
              room_status: "closed",
              visioStartDate: error?.response.data?.eta,
            })
          );
          Promise.resolve("success");
          return true;
        } else
          toastNotification(
            "error",
            i18next.t("common:visio.roomClosed"),
            "topRight"
          );
      } else toastNotification("error", errorText1, "topRight");

      Promise.reject("error");
      return true;
    }
  };

export const joinMeet =
  ({ roomName, user_id, errorText, external }) =>
  async (dispatch) => {
    try {
      const user = await store.getState().user.user;
      if (user && user?.access && user?.access["visio"] === "0") {
        localStorage.removeItem("lastHref");

        return;
      }
      let response;
      const config = {
        method: "POST",
        url: `${
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        }update-status-meet-guest`,
        data: {
          room: roomName,
          user_meet: user_id ?? null,
        },
        headers: {
          Authorization: process.env.REACT_APP_API_VISIO_KEY,
          "Content-Type": "application/json",
        },
      };
      if (external) response = await axios(config);
      else
        response = await MainService.joinMeetLinkParticipant({
          room: roomName,
          user_meet: user_id ?? null,
        });
      if (response.status === 200) {
        dispatch({
          type: SET_VISIO_PARAMS,
          payload: {
            join_room: true,
          },
        });
      }
    } catch (error) {
      if (error?.response.status === 403) return;
      toastNotification("error", errorText, "topRight");
    }
  };

export const updateVisioName =
  ({ task_id, id_visio, label, errorText }) =>
  async (dispatch) => {
    try {
      let response;
      const urlEncoded = new URLSearchParams();
      urlEncoded.append("label", label);
      response = await MainService.updateTaskLabel(task_id, urlEncoded);
      if (response.status === 200) {
        // id_visio is null when it's created from chat for the first time.
        if (id_visio === null || id_visio === response.data.data?.location) {
          dispatch({
            type: SET_VISIO_PARAMS,
            payload: {
              visio_name: label,
            },
          });
        }
        if (
          /^\/(contacts|companies)\/([^/]+)$/.test(window.location.pathname)
        ) {
          dispatch(setTask360(response.data.data));
        }
      }
    } catch (error) {
      if (error?.response.status === 403) return;
      toastNotification("error", errorText, "topRight");
    }
  };
