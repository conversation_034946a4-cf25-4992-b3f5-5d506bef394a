import React, { useEffect, useState } from "react";
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  message,
  Space,
  Popconfirm,
  Drawer,
} from "antd";
import { useTranslation } from "react-i18next";
import MainService from "services/main.service";
import TourSteps from "./TourSteps";

function Tour() {
  const { t } = useTranslation("common");
  const [tours, setTours] = useState([]);
  const [loading, setLoading] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [deleteLoadingKey, setDeleteLoadingKey] = useState(null);
  const [openModal, setOpenModal] = useState(false);
  const [form] = Form.useForm();
  const [editingTour, setEditingTour] = useState(null);
  const [activeDrawerTourKey, setActiveDrawerTourKey] = useState(null);

  useEffect(() => {
    fetchTours();
  }, []);

  const fetchTours = async () => {
    setLoading(true);
    try {
      const res = await MainService.getAllTours();
      setTours(res.data);
    } catch (err) {
      message.error(t("tours.errorLoading"));
    } finally {
      setLoading(false);
    }
  };

  const onFinish = async (values) => {
    setSubmitLoading(true);
    try {
      let newTours = [...tours];
      if (editingTour) {
        await MainService.updateTour(editingTour.key, values);
        message.success(t("tours.successUpdated"));
        newTours = newTours.map((tour) =>
          tour.key === editingTour.key ? { ...tour, ...values } : tour
        );
      } else {
        const res = await MainService.createTour(values);
        message.success(t("tours.successCreated"));
        newTours.push(res.data); // assume API returns new tour
      }
      setTours(newTours);
      setOpenModal(false);
      form.resetFields();
      setEditingTour(null);
    } catch (err) {
      const msg = err.response?.data?.message || t("tours.errorGeneric");
      message.error(msg);
    } finally {
      setSubmitLoading(false);
    }
  };

  const onEdit = (tour) => {
    setEditingTour(tour);
    form.setFieldsValue(tour);
    setOpenModal(true);
  };

  const onDelete = async (key) => {
    setDeleteLoadingKey(key);
    try {
      await MainService.deleteTour(key);
      message.success(t("tours.successDeleted"));
      setTours(tours.filter((tour) => tour.key !== key));
    } catch {
      message.error(t("tours.errorDeleting"));
    } finally {
      setDeleteLoadingKey(null);
    }
  };

  const columns = [
    {
      title: t("tours.key"),
      dataIndex: "key",
    },
    {
      title: t("tours.name"),
      dataIndex: "name",
    },
    {
      title: t("tours.description"),
      dataIndex: "description",
    },
    {
      title: t("tours.actions"),
      render: (_, record) => (
        <Space>
          <Button size="small" onClick={() => onEdit(record)}>
            {t("tours.edit")}
          </Button>
          <Popconfirm
            title={t("tours.deleteConfirm")}
            onConfirm={() => onDelete(record.key)}
          >
            <Button
              size="small"
              danger
              loading={deleteLoadingKey === record.key}
            >
              {t("tours.delete")}
            </Button>
          </Popconfirm>
          <Button size="small" onClick={() => setActiveDrawerTourKey(record.key)}>
            {t("tours.steps.manage_steps")}
          </Button>
          <TourSteps
            tourKey={record.key}
            visible={activeDrawerTourKey === record.key}
            onClose={() => setActiveDrawerTourKey(null)}
          />
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          marginBottom: 16,
          marginTop: 16,
        }}
      >
        <Button type="primary" onClick={() => setOpenModal(true)}>
          {t("tours.add")}
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={tours}
        rowKey={(record) => record.key}
        loading={loading}
      />

      <Modal
        title={editingTour ? t("tours.edit") : t("tours.create")}
        open={openModal}
        onCancel={() => {
          setOpenModal(false);
          setEditingTour(null);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        confirmLoading={submitLoading}
        destroyOnClose
      >
        <Form layout="vertical" form={form} onFinish={onFinish}>
          <Form.Item
            label={t("tours.key")}
            name="key"
            rules={[{ required: true, message: t("tours.key_required") }]}
          >
            <Input disabled={!!editingTour} />
          </Form.Item>
          <Form.Item
            label={t("tours.name")}
            name="name"
            rules={[{ required: true, message: t("tours.name_required") }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label={t("tours.description")}
            name="description"
            rules={[{ required: true, message: t("tours.description_required") }]}
          >
            <Input.TextArea rows={3} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}

export default Tour;
