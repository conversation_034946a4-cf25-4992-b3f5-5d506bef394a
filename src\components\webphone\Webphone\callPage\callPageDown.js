import "./Callpage.css";
import { useSelector } from "react-redux";

import {
  PauseOutlined,
  AudioOutlined,
  CaretRightOutlined,
  AudioMutedOutlined,
} from "@ant-design/icons";
import { Col, Row, Button, Tooltip, Tag } from "antd";
import useTimer from "../../../Timer";
import { useEffect, useState } from "react";
import { PiArrowFatRightFill } from "react-icons/pi";
import { HiPhone } from "react-icons/hi2";
import { useTranslation } from "react-i18next";
import DisplayAvatar from "pages/voip/components/DisplayAvatar";

var nameCol;

function CallPageDown({
  activecall,
  holdSession,
  hungUp,
  upBtn,
  muteSession,
  session,
}) {
  const [t] = useTranslation("common");

  const forwardingCall = useSelector(
    (state) => state.voipBlackList.forwardingCall
  );
  const receiverInfo = useSelector((state) => state.voipBlackList.receiverInfo);
  const confInfo = useSelector((state) => state.voipBlackList.confInfo);

  const [callActive, setCallActif] = useState(null);
  const timer = useTimer(
    callActive?.date,
    callActive?.statusAppel !== "ringing"
  );

  useEffect(() => {
    let callActiveLocal;

    if (session.length > 1) {
      callActiveLocal = !activecall?.uuid ? session[0] : activecall;
    } else {
      callActiveLocal = session[0];
    }

    if (receiverInfo?.name) {
      nameCol = receiverInfo?.name;
    } else {
      nameCol = callActiveLocal?.remoteIdentity?.uri?.user;
    }

    setCallActif(callActiveLocal);
  }, [session, activecall, receiverInfo?.name, activecall?.uuid]);
  //
  // console.log({ receiverInfo, activecall, forwardingCall });
  //
  return (
    <div className="relative flex h-10 w-full items-center justify-between px-2 py-1">
      <div className="relative flex w-[70%] items-center justify-between space-x-1.5">
        {callActive?.conf || confInfo ? (
          <div className="flex flex-col">
            <span className="font-semibold leading-5">{t("voip.conf")}</span>
            <span className="leading-4 text-slate-500">{`${
              receiverInfo?.number
            } & ${
              confInfo?.number || callActive?.conf?.remoteIdentity?.uri?.user
            }`}</span>
          </div>
        ) : (
          // <div className="w-5/12 truncate text-sm font-medium">
          //   <div>Conférence audio</div>
          //   {console.log(callActive?.conf?.remoteIdentity?.uri?.user)}
          //   <div>{`${getName(nameCol, "name")} - ${
          //     callActive?.conf?.remoteIdentity?.uri?.user
          //   }`}</div>
          // </div>
          <div className="flex items-center justify-center space-x-1">
            {callActive?.remoteIdentity?.uri?.user !== forwardingCall?.dst && (
              <DisplayAvatar
                urlImg={receiverInfo?.image}
                name={receiverInfo?.name}
                icon={
                  receiverInfo?.extension === "sphere_visio" &&
                  receiverInfo?.image
                }
                size={30}
              />
            )}

            <div
              className={`flex ${
                callActive?.remoteIdentity?.uri?.user === forwardingCall?.dst
                  ? "max-w-16"
                  : "max-w-32"
              } flex-col justify-center`}
            >
              <span className=" truncate font-semibold leading-4">
                {receiverInfo?.name?.replaceAll("_", " ") ||
                  receiverInfo?.number}
                {/* Hassine Ben Ali Ben Hassine Turki Basla */}
              </span>
              {!!receiverInfo?.name && (
                <span className="truncate leading-4 text-slate-500">
                  {receiverInfo?.number}
                </span>
              )}
            </div>

            {callActive?.remoteIdentity?.uri?.user === forwardingCall?.dst && (
              <div className=" flex items-center space-x-1">
                <PiArrowFatRightFill
                  style={{
                    marginTop: 2,
                    fontSize: 14,
                    color: "rgb(234 88 12)",
                  }}
                />
                <div
                  className={`flex ${
                    callActive?.remoteIdentity?.uri?.user ===
                    forwardingCall?.dst
                      ? "max-w-16"
                      : "max-w-32"
                  } flex-col justify-center`}
                >
                  <span className=" truncate font-semibold leading-4">
                    {forwardingCall?.dst_forwarding
                      ?.at(-1)
                      ?.name?.replaceAll("_", " ") ||
                      forwardingCall?.dst_forwarding?.at(-1)?.num}
                    {/* Hassine Ben Ali Ben Hassine Turki Basla */}
                  </span>
                  {!!forwardingCall?.dst_forwarding?.at(-1)?.name && (
                    <span className="truncate leading-4 text-slate-500">
                      {forwardingCall?.dst_forwarding?.at(-1)?.num}
                    </span>
                  )}
                </div>
                {/* <PiArrowFatRightFill
                  style={{
                    marginTop: 2,
                    fontSize: 14,
                    color: "rgb(234 88 12)",
                  }}
                />{" "}
                <p className="mt-1 max-w-12 truncate text-sm font-medium">
                  {getName(
                    forwardingCall?.dst_forwarding?.at(-1)?.name,
                    "name"
                  )}
                </p> */}
              </div>
            )}
          </div>
        )}

        {timer && timer !== "--:--" && (
          <time className=" font-semibold text-slate-400">
            <Tag bordered={false} color="success">
              {timer}
            </Tag>
          </time>
        )}
      </div>
      <div className="flex w-[28%] justify-end">
        {callActive?.direction === "incoming" &&
        callActive?.statusAppel === "ringing" ? (
          <div className="flex flex-row space-x-1">
            <Button
              className="bg-green-700"
              type="primary"
              shape="circle"
              size="small"
              icon={<HiPhone />}
              onClick={() => upBtn(callActive)}
            />
            <Button
              type="primary"
              shape="circle"
              size="small"
              disabled={!callActive?.established}
              danger
              icon={
                <HiPhone
                  style={{
                    fontSize: 16,
                    transform: "rotate(135deg)",
                    marginTop: 2,
                  }}
                />
              }
              onClick={() => hungUp(callActive)}
            />
          </div>
        ) : !callActive?.conf ? (
          <div className="flex items-center space-x-1.5">
            <Row>
              <Col className="gutter-row" span={12}>
                <div className="mx-0.5 flex w-full flex-col items-center  text-xs text-slate-400">
                  <Tooltip title="Pause">
                    <Button
                      icon={
                        callActive?.statusAppel === "holding" ? (
                          <CaretRightOutlined />
                        ) : (
                          <PauseOutlined />
                        )
                      }
                      size="small"
                      className="text-slate-500"
                      onClick={() => holdSession(callActive)}
                      disabled={
                        callActive?.statusAppel !== "ringing" ? false : true
                      }
                    />
                  </Tooltip>
                </div>
              </Col>

              <Col className="gutter-row" span={12}>
                <div className="mx-0.5 flex w-full flex-col items-center text-xs text-slate-400">
                  <Tooltip title="Silence">
                    <Button
                      onClick={() => muteSession(callActive)}
                      icon={
                        callActive?.ismute === "yes" ? (
                          <AudioMutedOutlined />
                        ) : (
                          <AudioOutlined />
                        )
                      }
                      size="small"
                      className="text-slate-500"
                    />
                  </Tooltip>
                </div>
              </Col>
            </Row>

            <Button
              type="primary"
              shape="circle"
              size="small"
              disabled={!callActive?.established}
              danger
              icon={
                <HiPhone
                  style={{
                    fontSize: 16,
                    transform: "rotate(135deg)",
                    marginTop: 2,
                  }}
                />
              }
              onClick={() => hungUp(callActive)}
            />
          </div>
        ) : (
          <Button
            type="primary"
            shape="circle"
            size="small"
            disabled={!callActive?.established}
            danger
            icon={
              <HiPhone
                style={{
                  fontSize: 16,
                  transform: "rotate(135deg)",
                  marginTop: 2,
                }}
              />
            }
            onClick={() => {
              !!callActive?.conf && hungUp(callActive?.conf);
              !!callActive && hungUp(callActive);
            }}
          />
        )}
      </div>
    </div>
  );
}

export default CallPageDown;
