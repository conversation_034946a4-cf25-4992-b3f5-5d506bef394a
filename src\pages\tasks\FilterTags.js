/**
 * @name FilterTags
 *
 * @description `FilterTags` component is responsible for rendering a set of tags based on filtering criteria provided through its
 *               props. Each tag visually represents a specific filter that has been applied to the list of activities.
 *
 * @param {Object} parameter The active filters from retieved from the form.
 * @param {Function} handleCloseTag Handles the closing of the tag (cancelling that filter).
 * @param {Array} tasksTypes Array of activity types.
 *
 * @returns {JSX.Element} Tags of activated filter.
 */

import { useMemo } from "react";
import dayjs from "dayjs";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import {
  CalendarOutlined,
  ClockCircleOutlined,
  CloseOutlined,
  FlagOutlined,
  LinkOutlined,
  TagOutlined,
  UserSwitchOutlined,
} from "@ant-design/icons";

import ChoiceIcons from "pages/components/ChoiceIcons";
import CustomTag from "./helpers/CustomTag";
import { handlePriorityLabelOnHover } from "./helpers/handlePriorities";
import { rolesList } from "./helpers/filterCount";
import { AvatarChat } from "components/Chat";
import { URL_ENV } from "index";
import { EXTENSIONS_ARRAY } from "./helpers/calculateSum";
import { getName } from "pages/layouts/chat/utils/ConversationUtils";
import { UserCheck } from "lucide-react";
const FilterTags = ({
  parameter,
  handleCloseTag,
  tasksTypes,
  tags,
  setSelectedTags,
  setSelectedRoles,
  setAppliedFilters,
  filtersForm,
}) => {
  const { families } = useSelector((state) => state?.families);
  const { user } = useSelector((state) => state.user);
  const [t] = useTranslation("common");

  // Keys of the active filters.
  const paramKey = useMemo(() => Object.keys(parameter)[0], [parameter]);
  const paramValue = useMemo(() => Object.values(parameter)[0], [parameter]);

  // Find activity type by id.
  const findTaskType = (id) =>
    tasksTypes?.find((type) => Number(type?.id) === Number(id));
  // Find family label
  const findFamilyLabel = (id) =>
    families?.find((el) => Number(el?.id) === Number(id))?.label;
  // Filter users roles in activity (creator, owner, participant, follower).
  const filterRoles = (values) =>
    rolesList(t)
      ?.filter((el) => values?.some((e) => e === el?.value))
      ?.map((el) => (
        <div className="flex items-center gap-x-1 pl-1">
          <span>{el?.label}</span>{" "}
          {paramValue.length > 1 && (
            <CloseOutlined
              className="cursor-pointer text-gray-500 hover:text-black"
              style={{ fontSize: 8 }}
              onClick={() => {
                filtersForm.setFieldsValue({
                  selectRoles: paramValue.filter((role) => role !== el.value),
                });
                setSelectedRoles((prev) =>
                  prev.filter((el) => el !== el.value)
                );
                setAppliedFilters((prev) =>
                  prev.map((obj) => {
                    if (
                      obj.hasOwnProperty("selectRoles") &&
                      paramValue.length > 1
                    ) {
                      return {
                        ...obj,
                        selectRoles: paramValue.filter(
                          (role) => role !== el.value
                        ),
                      };
                    } else if (
                      obj.hasOwnProperty("selectRoles") &&
                      obj.tags.length === 1
                    ) {
                      const { tags, ...rest } = obj;
                      return rest;
                    }
                    return obj;
                  })
                );
              }}
            />
          )}
        </div>
      ));
  // ?.toString();

  // Rendered custom tag.
  const renderCustomTag = () => {
    switch (paramKey) {
      // On active the filter by type.
      case "types":
        const taskType = findTaskType(paramValue);
        return (
          <CustomTag
            icon={
              <>
                Type: <ChoiceIcons icon={taskType?.icons} />
                <span className="ml-1">{taskType?.label}</span>
              </>
            }
            handleCloseTag={() => handleCloseTag(parameter)}
            color="magenta"
          />
        );
      // On active the filter by date range.
      case "filterByDate":
        return (
          <CustomTag
            color="gold"
            handleCloseTag={() => handleCloseTag(parameter)}
          >
            <>
              <CalendarOutlined style={{ marginRight: "2px" }} />
              {`${t("vue360.from")} ${dayjs(paramValue[0])?.format(
                user?.location?.date_format
              )}`}{" "}
              {`${t("vue360.to")} ${dayjs(paramValue[1])?.format(
                user?.location?.date_format
              )}`}
            </>
          </CustomTag>
        );
      // On active the filter by module.
      case "filterByModule":
        return (
          <CustomTag
            color="cyan"
            handleCloseTag={() => handleCloseTag(parameter)}
          >
            <LinkOutlined style={{ marginRight: "2px" }} /> Module:{" "}
            {/* {findFamilyLabel(paramValue[0])} */}
            {findFamilyLabel(paramValue)}
          </CustomTag>
        );
      case "userName":
        return (
          <CustomTag
            color="orange"
            handleCloseTag={() => handleCloseTag(parameter)}
          >
            <UserCheck size={12} style={{ marginRight: "2px" }} />
            {t("fields_management.user")}: {getName(paramValue, "name")}
          </CustomTag>
        );
      case "module":
        return (
          <CustomTag
            color="gray"
            handleCloseTag={() => handleCloseTag(parameter)}
          >
            {paramValue}
          </CustomTag>
        );
      // On active the filter by roles.
      case "selectRoles":
        return (
          <CustomTag
            color="success"
            closeIcon={false}
            // handleCloseTag={() => handleCloseTag(parameter)}
          >
            <div className="flex items-center">
              <UserSwitchOutlined style={{ marginRight: "2px" }} /> Roles:{" "}
              {filterRoles(paramValue)}
            </div>
          </CustomTag>
        );
      // On active the filter by priority.
      case "priorityFilter":
        return (
          <CustomTag
            color="geekblue"
            handleCloseTag={() => handleCloseTag(parameter)}
          >
            <FlagOutlined style={{ marginRight: "2px" }} />
            {t("tasks.priority")}:{" "}
            {paramValue
              ?.map((el) => handlePriorityLabelOnHover(el))
              ?.toString()}
          </CustomTag>
        );

      case "tags":
        return (
          <>
            {paramValue.length > 0 ? (
              <CustomTag
                color="purple"
                handleCloseTag={() => handleCloseTag(parameter)}
                closeIcon={false}
              >
                <div className="flex items-center">
                  <TagOutlined style={{ marginRight: "2px" }} />
                  {t("menu2.tags")}:
                  {paramValue.map((id) => {
                    const item = tags.find((obj) => obj.value === id);
                    return item ? (
                      <div className="flex items-center gap-x-1 pl-1" key={id}>
                        <span>{item.label}</span>{" "}
                        <CloseOutlined
                          className="cursor-pointer text-gray-500 hover:text-black"
                          style={{ fontSize: 8 }}
                          onClick={() => {
                            filtersForm.setFieldsValue({
                              tags: paramValue.filter((el) => el !== id),
                            });
                            setSelectedTags((prev) =>
                              prev.filter((el) => el !== id)
                            );
                            setAppliedFilters((prev) =>
                              prev.map((obj) => {
                                if (
                                  obj.hasOwnProperty("tags") &&
                                  paramValue.length > 1
                                ) {
                                  return {
                                    ...obj,
                                    tags: paramValue.filter((el) => el !== id),
                                  };
                                } else if (
                                  obj.hasOwnProperty("tags") &&
                                  obj.tags.length === 1
                                ) {
                                  const { tags, ...rest } = obj;
                                  return rest;
                                }
                                return obj;
                              })
                            );
                          }}
                        />
                      </div>
                    ) : null;
                  })}
                </div>
              </CustomTag>
            ) : (
              <></>
            )}
          </>
        );
      // On active the filter starting from today.
      case "filterFromTodayOn":
        return (
          <CustomTag
            color="red"
            handleCloseTag={() => handleCloseTag(parameter)}
          >
            <ClockCircleOutlined style={{ marginRight: "2px" }} />
            {t("tasks.startFrom", {
              today: dayjs(dayjs().startOf("day")).format(
                user?.location?.date_format
              ),
            })}
          </CustomTag>
        );

      default:
        return null;
    }
  };

  return renderCustomTag();
};

export default FilterTags;
