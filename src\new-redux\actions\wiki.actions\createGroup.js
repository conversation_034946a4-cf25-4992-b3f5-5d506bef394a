import {
  CREATE_GROUP_WIKI_SUCCESS,
  CREATE_GROUP_WIKI_ERROR,
  IS_LOADING_WIKI,
} from "../../constants";
import MainService from "../../../services/main.service";
import { toastNotification } from "../../../components/ToastNotification";

export const createGroupWiki = (payload) => async (dispatch) => {
  try {
    dispatch({ type: IS_LOADING_WIKI });
    const response = await MainService.createGroup(payload.data);
    dispatch({
      type: CREATE_GROUP_WIKI_SUCCESS,
      payload: response?.data?.data,
    });
    payload.setOpenAdd(false);
    payload.form.resetFields();
    payload.setLogo([]);
    toastNotification(
      "success",
      payload.data.label_fr + " created successfully!",
      "topRight"
    );
  } catch (error) {
    dispatch({
      type: CREATE_GROUP_WIKI_ERROR,
      payload: error,
    });
  }
};
