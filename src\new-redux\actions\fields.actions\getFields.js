import { GET_FIELDS_SUCCESS, GET_FIELDS_ERROR, IS_LOADING } from "../../constants";
import MainService from "../../../services/main.service";

export const getFields = (familyId, page, limit, key, type) => async (dispatch) => {
  try {
    dispatch({ type: IS_LOADING });

    const response = await MainService.getFields(familyId, page, limit, key, type);
    dispatch({
      type: GET_FIELDS_SUCCESS,
      payload: response?.data,
    });
  } catch (error) {
    if (error?.status !== 401) {
      dispatch({
        type: GET_FIELDS_ERROR,
        payload: error,
      });
    }
  }
};
