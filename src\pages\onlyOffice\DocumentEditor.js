import { useState, useEffect } from "react";
import { DocumentEditor } from "@onlyoffice/document-editor-react";
import { useSearchParams } from "react-router-dom";

import { URL_ENV } from "index";
import MainService from "services/main.service";
import { getFileOfficeDrive } from "services/main.service";
import { useTranslation } from "react-i18next";

const DocEditor = () => {
  const [searchParams] = useSearchParams();

  const [documentConfig, setDocumentConfig] = useState({
    fileUrl: searchParams.get("fileUrl"),
    token: searchParams.get("token"),
    documentType: searchParams.get("documentType"),
    fileType: searchParams.get("fileType"),
    key: searchParams.get("key"),
    fileCompleteUrl: searchParams.get("url"),
    title: searchParams.get("title"),
    mode: searchParams.get("mode"),
    lng: searchParams.get("lang"),
    id: searchParams.get("id"),
    image: searchParams.get("image"),
    name: searchParams.get("name"),
    email: searchParams.get("email"),
    editingMode: searchParams.get("editingMode"),
    change: searchParams.get("change")?.trim(),
  });
  console.log("11gregre1", documentConfig);
  const openFileOnOnlyOffice = async (docId) => {
    try {
      let payload = {
        file_id: docId,
        mode: null,
        mode_co_editing: "fast",
      };
      const response = await MainService.getFileOfficeConfigs(payload);
      if (response?.status === 200) {
        setDocumentConfig((prev) => ({
          ...prev,
          fileUrl: response?.data?.document_details?.url,
          token: response?.data?.token,
          documentType: response?.data?.document_details?.documentType,
          fileType: response?.data?.document_details?.fileType,
          key: response?.data?.document_details?.key,
          fileCompleteUrl: response?.data?.document_details?.url,
          title: response?.data?.document_details?.title,
          mode: response?.data?.editor_config?.mode,
          lng: response?.data?.editor_config?.lang ?? "en",
          id: response?.data?.editor_config?.user?.id,
          image: response?.data?.editor_config?.user?.image,
          name: response?.data?.editor_config?.user?.name,
          email: response?.data?.editor_config?.user?.email,
          editingMode: response?.data?.editor_config?.coEditing?.mode,
          change: response?.data?.editor_config?.coEditing?.change,
        }));
      }
    } catch (error) {
      console.log(`Error loadin file: ${error}`);
    }
  };

  const openFileOnOnlyOfficeFromDrive = async (fileId) => {
    try {
      const response = await getFileOfficeDrive(fileId);
      if (response?.status === 200) {
        setDocumentConfig((prev) => ({
          ...prev,
          fileUrl: response?.data?.config?.document?.url,
          token: response?.data?.config?.token,
          documentType: response?.data?.config?.document?.documentType,
          fileType: response?.data?.config?.document?.fileType,
          key: response?.data?.config?.document?.key,
          fileCompleteUrl: response?.data?.config?.document?.url,
          title: response?.data?.config?.document?.title,
          mode: response?.data?.config?.editorConfig?.mode,
          lng: response?.data?.config?.editorConfig?.lang ?? "en",
          id: response?.data?.config?.editorConfig?.user?.id,
          image: response?.data?.config?.editorConfig?.user?.image ?? "null",
          name: response?.data?.config?.editorConfig?.user?.name ?? "null",
          email: response?.data?.config?.editorConfig?.user?.email ?? "null",
          editingMode:
            response?.data?.config?.editorConfig?.coEditing?.mode ?? "null",
          change:
            response?.data?.config?.editorConfig?.coEditing?.change ?? "null",
        }));
      }
    } catch (error) {
      console.log(`Error loadin file: ${error}`);
    }
  };

  useEffect(() => {
    const fileId = JSON.parse(localStorage.getItem("document") ?? null);
    const isDrive = JSON.parse(localStorage.getItem("drive") ?? null);
    if ((!documentConfig?.fileUrl || !documentConfig?.token) && fileId) {
      isDrive
        ? openFileOnOnlyOfficeFromDrive(fileId)
        : openFileOnOnlyOffice(fileId);
    }
  }, [documentConfig?.fileUrl, documentConfig?.token]);

  function onDocumentReady(event) {
    console.log();
  }

  function onLoadComponentError(errorCode, errorDescription) {
    switch (errorCode) {
      case -1: // Unknown error loading component
        console.log(errorDescription);
        break;

      case -2: // Error load DocsAPI from http://documentserver/
        console.log(errorDescription);
        break;

      case -3: // DocsAPI is not defined
        console.log(errorDescription);
        break;
      default:
        console.log({ errorCode, errorDescription });
        break;
    }
  }

  const {
    fileType,
    key,
    title,
    fileCompleteUrl,
    documentType,
    editingMode,
    change,
    id,
    name,
    email,
    image,
    mode,
    lng,
    token,
  } = documentConfig;
  let config = {};

  if (Object.values(documentConfig)?.some((e) => !e)) return <></>;
  config = {
    document: {
      fileType: fileType?.trim(),
      key: key?.trim(),
      title: title?.trim(),
      url: fileCompleteUrl?.trim(),
    },
    documentType: documentType?.trim(),
    editorConfig: {
      coEditing: {
        mode: editingMode?.trim(),
        change: JSON.parse(change ?? null),
      },
      user: {
        id: id?.trim(),
        name: name?.trim(),
        email: email?.trim(),
        image: `${URL_ENV?.REACT_APP_BASE_URL}${
          URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
        }${image?.trim()}`,
      },
      mode: mode?.trim() === "null" ? null : mode?.trim(),
      lang: lng?.trim() === "null" ? null : lng?.trim(),
    },
    token: token?.trim(),
  };
  // add comment
  console.log(999, config);
  if (config) {
    return (
      <DocumentEditor
        id="docxEditor"
        documentServerUrl="https://officedemo.cmk.biz/"
        config={config}
        events_onDocumentReady={onDocumentReady}
        onLoadComponentError={onLoadComponentError}
      />
    );
  } else {
    return <></>;
  }
};

export default DocEditor;
