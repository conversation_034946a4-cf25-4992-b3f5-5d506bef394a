import { UPDATE_GROUP_SUCCESS, UPDATE_GROUP_ERROR, UPDATE_GROUP_LOADING } from "../../constants";
import MainService from "../../../services/main.service";

export const updateGroup = (groupId, payload) => async (dispatch) => {
  try {
    dispatch({ type: UPDATE_GROUP_LOADING });
    const response = await MainService.updateGroupLabel(groupId, payload);
    dispatch({
      type: UPDATE_GROUP_SUCCESS,
      payload: { response: response?.data?.data, updatedGroupId: groupId },
    });
  } catch (error) {
    if (error?.status !== 401) {
      dispatch({
        type: UPDATE_GROUP_ERROR,
        payload: error,
      });
    }
  }
};
