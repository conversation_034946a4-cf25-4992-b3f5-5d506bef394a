import React, { useEffect, useRef } from "react";
import {
  Form,
  InputNumber,
  Input,
  Select,
  Space,
  Badge,
  Button,
  Switch,
  Spin,
  Table,
  Typography,
  Drawer,
  Popover,
  Divider,
  Tooltip,
} from "antd";
import { useState } from "react";
import { generateAxios } from "../services/axiosInstance";
import { useTranslation } from "react-i18next";
import {
  PlusCircleOutlined,
  PlusOutlined,
  SettingOutlined,
} from "@ant-design/icons";
import { colors } from "./Colors";

import { useParams } from "react-router-dom";
import { toastNotification } from "./ToastNotification";
import ColumnColors from "./ColumnColors";
import NewTableDraggable from "./NewTableDraggable";
import LabelTable from "./LabelTable";
import BottomButtonAddRow from "./BottomButtonAddRow";
import Header from "./configurationHelpDesk/Header";
import { useDispatch, useSelector } from "react-redux";
import { setSearch } from "../new-redux/actions/menu.actions/menu";
import { FiFolderPlus, FiSearch } from "react-icons/fi";
import { SubmitKeyPress } from "../utils/SubmitKeyPress";
import { Check, Flag, WrapText, XCircle } from "lucide-react";
import { FaFlagCheckered } from "react-icons/fa";
import { URL_ENV } from "index";
import MainService from "services/main.service";
import FieldsSettingArea from "./FieldSettings/FieldsSettingArea";
import { setNewInteraction } from "new-redux/actions/vue360.actions/vue360";
import { familyIcons } from "pages/components/DetailsProfile/ViewSphere2";
import { roles } from "utils/role";

const Stage = ({
  pipeline_id,
  setEditingKey,
  editingKey,
  data,
  setData,
  loadingPipeline,
  disabledAddStage,
  dataPipelines,
  handleAddPipeline,
  editingKeyPipeline,
}) => {
  const [form] = Form.useForm();
  const [count, setCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [finalCreate, setFinalCreate] = useState(0);
  const [isUpdateRank, setIsUpdateRank] = useState(null);
  const [groupId, setGroupId] = useState(null);
  const [id, setId] = useState(null);
  const [saveData, setSaveData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useState("");
  const isEditing = (record) => record.key === editingKey;
  const [t] = useTranslation("common");
  const [color, setColor] = useState("");
  const [rank, setRank] = useState(null);
  const [pageSize, setPageSize] = useState(20);
  const [errorForm, setErrorForm] = useState(false);
  const [openDrawer, setOpenDrawer] = useState(false);

  const [loadFinal, setLoadFinal] = useState(false);
  const inputRefs = useRef([]);
  const { id: family } = useParams();
  const { isDeleteRows } = useSelector((state) => state.table);
  const { fields } = useSelector((state) => state.fields);
  const { user } = useSelector((state) => state.user);
  const [updateFieldProps, setUpdateFieldProps] = useState({});
  const [familyId, setFamilyId] = useState(null);
  const [mem, setMem] = useState(
    Object.keys(updateFieldProps).length !== 0 ? updateFieldProps?.type : ""
  );
  const [limit, setLimit] = useState(70);
  const [selectedModule, setSelectedModule] = useState("");
  const [loadModule, setLoadModule] = useState(false);
  const [modulesList, setModulesList] = useState([]);
  const [optsValidationStatus, setOptsValidationStatus] = useState(false);
  const [deletedOptionId, setDeletedOptionId] = useState(null);
  const [groupList, setGroupList] = useState([]);
  const [loadGroups, setLoadGroups] = useState(false);
  const [selectedGroupId, setSelectedGroupId] = useState(null);
  const [selectedRowKey, setSelectedRowKey] = useState("");
  const [loadModuleList, setLoadModuleList] = useState(false);
  const [selectMultipleCountries, setSelectMultipleCountries] = useState(0);
  const [displayOptions, setDisplayOptions] = useState([]);
  const { families } = useSelector((state) => state.families);
  const { contactHeaderInfo: contactInfo } = useSelector(
    (state) => state?.contacts
  );
  const { contactInfoFromDrawer, openView360InDrawer } = useSelector(
    (state) => state?.vue360
  );
  const dispatch = useDispatch();
  useEffect(() => {
    setData([]);
  }, [family]);
  useEffect(() => {
    inputRefs.current.forEach((input) => {
      input?.focus();
    });
  }, [data.length, id, rank]);
  const handleKeyPress = (event) => {
    SubmitKeyPress(event, form);
  };
  const onFinishFailed = (values) => {};
  function handleInputNumberKeyDown(e) {
    if (e.key === "." && e.target.value.includes(".")) {
      e.preventDefault();
    }
    if (e.key === "-" && !e.target.value) {
      e.preventDefault();
    }
    if (e.key === "e") {
      e.preventDefault();
    }
    if (
      isNaN(parseInt(e.key)) &&
      ![
        "Tab",
        "Backspace",
        "Delete",
        "ArrowLeft",
        "ArrowRight",
        "Enter",
      ].includes(e.key)
    ) {
      e.preventDefault();
    }
  }
  const getModulesList = async () => {
    try {
      setLoadModuleList(true);
      const response = await MainService.getModules();
      setModulesList(response?.data?.data);
      setLoadModuleList(false);
    } catch (error) {
      setLoadModuleList(false);
      console.log(`Error ${error}`);
    }
  };
  const { data: dataFields } = useSelector((state) => state.fields);
  const [fieldsArray, setFieldsArray] = useState(
    dataFields?.fields ? dataFields?.fields?.dataFields?.fields : null
  );

  const onRow = () => {};

  const EditableCell = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    const inputNode =
      inputType === "select" ? (
        <Select
          showSearch
          placeholder={t("tags.selectcolor")}
          style={{
            minWidth: 100,
            maxWidth: "100%",
          }}
          options={colors.map((el) => ({
            label: (
              <Space>
                <Badge color={el.value} /> {t(`colors.${el.label}`)}
              </Space>
            ),
            value: el.value,
          }))}
          optionFilterProp="children"
          filterOption={(input, option) =>
            (
              colors
                .find((el) => el.value === option.value)
                ?.label?.toLowerCase() ?? ""
            ).includes(input.toLowerCase())
          }
          filterSort={(optionA, optionB) =>
            (optionA?.value ?? "")
              .toLowerCase()
              .localeCompare((optionB?.value ?? "").toLowerCase())
          }
          // allowClear
        />
      ) : inputType === "percent" ? (
        <InputNumber
          onKeyDown={handleInputNumberKeyDown}
          min="0"
          max="100"
          addonAfter="%"
          placeholder={title}
          onKeyPress={handleKeyPress}
        />
      ) : inputType === "requiredSwitch" ? (
        <>
          <Switch
            size="small"
            disabled={record.default}
            // onChange={(e) => form.setFieldsValue({ required: e })}
            defaultChecked={
              !loading ? record.final : form.getFieldsValue().required
            }
          />
        </>
      ) : (
        <Input
          ref={(el) => (inputRefs.current[index] = el)}
          placeholder={t("pipeline.stageName")}
          onKeyPress={handleKeyPress}
        />
      );

    return (
      <td {...restProps}>
        {editing ? (
          <Form.Item
            name={dataIndex.toLowerCase()}
            style={{
              margin: 0,
            }}
            rules={[
              {
                required: dataIndex === "label" ? true : false,
                message: `${
                  t("pipeline.stageName") + " " + t("table.header.isrequired")
                } `,
              },
            ]}
          >
            {inputNode}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };
  const edit = async (record) => {
    if (record) {
      form.setFieldsValue({
        label: record.label,
        color: record.color,
        percent: record.percent,
        required: record.final,
      });
      setRank(record.rank);
      setId(record.id);
      setColor(record.color);
      setEditingKey(record.key);
    } else {
      form.setFieldsValue({
        label: "",
        color: null,
        percent: "",
      });
    }
  };
  const cancel = (record) => {
    setEditingKey("");
    setErrorForm(false);

    setColor("");
    setFinalCreate(false);
    setId(null);
    if (!record.id) {
      setData(data.filter((item) => item.key !== record.key));
    }
  };
  const updateRequired = async (id, checked) => {
    setLoadFinal(true);
    try {
      const res = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).post(`/stages/update-final`, {
        final: checked == true ? 1 : 0,
        stage_id: id,
      });
      if (data.length === 2) {
        setData((prev) =>
          prev.map((el) =>
            el.id === id
              ? {
                  ...el,
                  final: res.data.data.find((dt) => dt.id === id)?.final,
                }
              : {
                  ...el,
                  final: !res.data.data.find((dt) => dt.id === id)?.final,
                }
          )
        );
      } else {
        setData((prev) =>
          prev.map((el) => (el.id === id ? { ...el, final: checked } : el))
        );
      }
      setLoadFinal(false);
    } catch (err) {
      setLoadFinal(false);

      if (err?.response?.status === 422) {
        if (
          err?.response?.data?.message === "You cannot update this stage" ||
          err?.response?.data?.message === "You cannot edit system pipeline"
        ) {
          toastNotification("error", t("pipeline.isDefault"), "topRight");
        } else
          toastNotification("error", t("pipeline.errFinalStage"), "topRight");
      } else toastNotification("error", t("toasts.somethingWrong"), "topRight");
    }
  };
  const save = async (values) => {
    setLoading(true);

    if (id) {
      const config = {
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      };
      try {
        const row = await form.validateFields();
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).put(
          `/stages/${id}`,
          {
            ...row,
            pipeline_id,
            rank,
            final: row.required ? 1 : 0,
          },
          config
        );
        setEditingKey("");
        setRank("");
        setErrorForm(false);
        if (data.length === 2) {
          setData((prev) =>
            prev.map((el) =>
              el.id === res.data.data.id
                ? {
                    ...res.data.data,
                    final: res.data.data.final,
                    key: el.key,
                    can_update_rank: el?.can_update_rank,
                  }
                : {
                    ...el,
                    final: !res.data.data.final,
                    key: el.key,
                    can_update_rank: el?.can_update_rank,
                  }
            )
          );
        } else {
          setData(
            data.map((el) =>
              el.id === res.data.data.id
                ? {
                    ...res.data.data,
                    key: res.data.data.id,
                    can_update_rank: el?.can_update_rank,
                    // ...el,
                  }
                : el
            )
          );
        }

        form.setFieldsValue({
          label: "",
          color: null,
          percent: "",
        });
        if (
          openView360InDrawer &&
          pipeline_id === contactInfoFromDrawer?.pipeline
        ) {
          dispatch(
            setNewInteraction({ type: "updateStageFromDrawer", user: user?.id })
          );
        } else if (
          !openView360InDrawer &&
          pipeline_id === contactInfo?.pipeline
        ) {
          dispatch(setNewInteraction({ type: "updateStage", user: user?.id }));
        }

        setLoading(false);
        toastNotification("success", row.label + t("toasts.edit"), "topRight");
      } catch (err) {
        setLoadFinal(false);
        if (err?.response?.status === 422) {
          if (
            err?.response?.data?.message === "Pipeline label already exists"
          ) {
            toastNotification(
              "error",
              t("toasts.labelExist", {
                label: t("pipeline.stageName"),
              }),
              "topRight"
            );
          } else if (
            err?.response?.data?.message === "You cannot update this stage"
          ) {
            toastNotification("error", t("pipeline.isDefault"), "topRight");
          } else
            toastNotification("error", t("pipeline.errFinalStage"), "topRight");
        } else
          toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    } else {
      try {
        const row = await form.validateFields();

        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post("/stages", {
          ...Object.fromEntries(
            Object.entries(row).filter(([_, v]) => v != "")
          ),
          pipeline_id,
          rank: saveData.length + 1,
          final: row.required ? 1 : 0,
          percent: values.percent == 0 ? 0 : row.percent,
        });
        setEditingKey("");
        setData([
          ...data.filter((el) => el.id),
          {
            ...res.data.data,
            key: res.data.data.id,
            can_update_rank: isUpdateRank,
          },
        ]);

        setSaveData([
          ...data.filter((el) => el.id),
          {
            ...res.data.data,
            key: res.data.data.id,
            can_update_rank: isUpdateRank,
          },
        ]);
        form.setFieldsValue({
          label: "",
          color: null,
          percent: "",
        });

        if (
          openView360InDrawer &&
          pipeline_id === contactInfoFromDrawer?.pipeline
        ) {
          dispatch(
            setNewInteraction({ type: "updateStageFromDrawer", user: user?.id })
          );
        } else if (
          !openView360InDrawer &&
          pipeline_id === contactInfo?.pipeline
        ) {
          dispatch(setNewInteraction({ type: "updateStage", user: user?.id }));
        }
        setLoading(false);
        setFinalCreate(false);
        toastNotification(
          "success",
          row.label + t("toasts.created"),
          "topRight"
        );
      } catch (err) {
        console.log(err);
        setLoading(false);

        if (err?.response?.status === 422) {
          if (err?.response?.data?.message === "Stage label already exists") {
            toastNotification(
              "error",
              t("toasts.labelExist", {
                label: t("pipeline.stageName"),
              }),
              "topRight"
            );
          } else if (
            err?.response?.data?.message === "You cannot update this stage"
          ) {
            toastNotification("error", t("pipeline.isDefault"), "topRight");
          } else
            toastNotification("error", t("pipeline.errFinalStage"), "topRight");
        } else
          toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    }
  };
  useEffect(() => {
    const getStages = async () => {
      setLoading(true);
      try {
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get(`/stages/getStagesByPipeline/${pipeline_id}`);
        setIsUpdateRank(res.data?.can_update_rank);
        setData(
          res?.data?.data?.map((el, i) => ({
            ...el,
            key: el.id,
            rank: i + 1,
            can_update_rank: res.data?.can_update_rank,
          }))
        );
        setSaveData(
          res?.data?.data?.map((el, i) => ({
            ...el,
            key: el.id,
            rank: i + 1,
            can_update_rank: res.data?.can_update_rank,
          }))
        );
        if (res?.data?.data?.length > 0) {
          setCount(Math.max(...res?.data?.data?.map((el) => el.id)));
        }

        setLoading(false);
      } catch (err) {
        setLoading(false);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    };
    if (pipeline_id) getStages();
  }, [pipeline_id, t, fields]);
  const handleClick = (event) => {
    event.stopPropagation();
  };
  const columns = [
    {
      title: t("activities.name"),
      dataIndex: "label",
      key: "label",
      width: "50%",
      editable: true,
      sorter: (a, b) => a.label.localeCompare(b.label),
      render: (_, record) => {
        return (
          <div className="flex items-center justify-between">
            {isUpdateRank === 1 ? (
              <LabelTable
                record={record}
                editingKey={editingKey}
                edit={edit}
                width={110}
              />
            ) : (
              <p>
                {" "}
                {record.label || record.name}{" "}
                {record.resolved == 1 ? `(${t("pipeline.resolved")})` : ""}
              </p>
            )}
            {record?.field_groups?.length > 0 ? (
              <Popover
                // open={true}
                content={
                  <div>
                    {record?.field_groups.map((el) => (
                      <span
                        className="flex items-center justify-between"
                        key={el.id}
                      >
                        <Typography.Link
                          underline
                          onClick={() => {
                            setGroupId(el.id);
                            console.log(el);
                            setOpenDrawer(true);
                          }}
                        >
                          {el.label}
                        </Typography.Link>{" "}
                      </span>
                    ))}
                  </div>
                }
                title={
                  <div>
                    <div className="flex items-center justify-between">
                      <Typography.Text>
                        {t("fields_management.groupNameCol")}
                      </Typography.Text>
                    </div>
                    <Divider style={{ margin: 5, padding: 0 }} />
                  </div>
                }
                trigger="hover"
              >
                <Badge
                  size="small"
                  count={record?.field_groups?.length}
                  offset={[-10, 4]}
                  style={{
                    backgroundColor: "#52c41a",
                  }}
                >
                  <Button
                    icon={
                      <FiFolderPlus
                        style={{ fontSize: "14px", color: "green" }}
                      />
                    }
                    type="link"
                    style={{ color: "green", margin: "-4px 0" }}
                  />
                </Badge>
              </Popover>
            ) : (
              <Tooltip title={t("pipeline.addStageToGrouName")}>
                <span onClick={() => setOpenDrawer(true)}>
                  <Badge
                    size="small"
                    count={0}
                    showZero
                    offset={[-10, 4]}
                    color="#6b7280"
                  >
                    <Button
                      icon={<FiFolderPlus style={{ fontSize: "14px" }} />}
                      type="link"
                      shape="circle"
                      style={{ margin: "-4px 0", color: "#4b5563" }}
                    />
                  </Badge>
                </span>
              </Tooltip>
            )}

            {/* <FaFlagCheckered className="drapeau-flottant" /> */}
          </div>
        );
      },
    },
    {
      title: t("table.header.percentage"),
      dataIndex: "percent",
      key: "percent",
      width: "20%",

      editable: true,
      sorter: (a, b) => a.id - b.id,
      render: (_, { percent }) => (
        <span>
          {percent} {typeof percent === "number" ? "%" : ""}
        </span>
      ),
    },
    {
      title: t("activities.color"),
      dataIndex: "Color",
      key: "color",
      width: "20%",

      editable: true,
      render: (_, { color }) => <ColumnColors color={color} colors={colors} />,
    },

    {
      title: t("pipeline.finalStage"),
      dataIndex: "required",
      key: "final",
      width: "10%",

      editable: true,
      render: (_, record) => (
        <Switch
          disabled={record.default}
          size="small"
          // defaultChecked={record.required === "true" ? true : false}
          checked={record.final == true ? true : false}
          onChange={(checked) => updateRequired(record.id, checked)}
          // disabled
          loading={loadFinal}
        />
      ),
    },
  ];
  const getGroupsByFamily = async () => {
    let familyIdentifier =
      families && families.find((element) => element?.label == family)?.id;
    try {
      setLoadGroups(true);
      const response = await MainService.getGroupsByFamily(familyIdentifier);
      setGroupList(response?.data?.data);
      setLoadGroups(false);
    } catch (error) {
      setLoadGroups(false);
      console.log(`Error ${error}`);
    }
  };
  const handleAdd = () => {
    setId(null);
    const ids = data.map((object) => {
      return object.id;
    });

    setEditingKey(null);
    const newData = {
      key: Math.max(...ids) + 1,
      label: `  `,
      color: "#9ca3af",
      percent: "",
      disabled: true,
    };
    setCurrentPage(Math.ceil([...data, newData].length / pageSize));

    setData([...data, newData]);
    form.setFieldsValue({
      label: "",
      color: "#9ca3af",
      percent: "",
    });
    setEditingKey(Math.max(...ids) + 1);
    setCount(Math.max(...ids) + 1);
  };
  const filteredData = data.filter((item) => {
    return (
      item.label?.toLowerCase().includes(search.toLowerCase()) ||
      item?.percent?.toString().includes(search.toString())
    );
  });
  return (
    <Space direction="vertical" style={{ width: "100%" }}>
      {/* <div className="ml-2 mr-2 mt-4	rounded px-2.5 py-0.5 text-base font-medium text-[#2253d5] dark:bg-blue-200 dark:text-blue-800">
        {t(`pipeline.stages${family}`)}
      </div>
      <HeaderP
        active={"2"}
        editingKey={editingKey}
        handleAdd={handleAdd}
        btnText={""}
        disabled={loading ? true : editingKey ? true : search ? true : false}
        shape={"circle"}
      /> */}

      {pipeline_id ? (
        <>
          <div className="ml-2 mr-2 px-2.5 text-base font-semibold text-[#2253d5] ">
            {t("pipeline.stage")} - (
            {openView360InDrawer && contactInfoFromDrawer?.id
              ? familyIcons(t).find(
                  (el) => el.key === contactInfoFromDrawer?.family_id
                )?.label + " / "
              : !openView360InDrawer && contactInfo?.id
              ? familyIcons(t).find((el) => el.key === contactInfo?.family_id)
                  ?.label + " / "
              : family
              ? family + " / "
              : ""}
            {dataPipelines?.find((el) => el.id === pipeline_id)?.label})
          </div>
          <div className="flex items-center justify-between ">
            <div
              className={`flex w-full pr-4 transition-all duration-200 ease-in ${
                isDeleteRows ? "pl-[220px]" : "pl-4"
              }  `}
            >
              <Input
                prefix={<FiSearch className="text-slate-400" />}
                onChange={(e) => setSearch(e.target.value)}
                placeholder={t("table.search")}
                style={{ width: "250px" }}
                value={search}
              />
            </div>

            {pipeline_id ? (
              <Button
                shape="circle"
                icon={<PlusOutlined />}
                disabled={
                  loading
                    ? true
                    : // : disabledAddStage
                    // ? true
                    !isUpdateRank
                    ? true
                    : editingKey
                    ? true
                    : search
                    ? true
                    : false
                }
                onClick={handleAdd}
                type="primary"
                className="mr-3"
              />
            ) : (
              ""
            )}
          </div>

          <NewTableDraggable
            columns={columns}
            setLoading={setLoading}
            isEditing={isEditing}
            data={filteredData}
            setData={setData}
            loading={loading}
            onRow={onRow}
            save={save}
            edit={edit}
            EditableCell={EditableCell}
            onFinishFailed={onFinishFailed}
            cancel={cancel}
            form={form}
            apiRank="/stages/updateRank"
            editingKey={editingKey}
            api="stages"
            btnText={t("helpDesk.addFolder")}
            pagination={false}
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            pageSize={pageSize}
            setPageSize={setPageSize}
          />
          {pipeline_id ? (
            <BottomButtonAddRow
              editingKey={editingKey}
              data={data}
              text={t("companies.addStage")}
              handleAdd={handleAdd}
              loading={loading}
              search={
                search
                // || disabledAddStage
              }
              pagination={false}
            />
          ) : (
            ""
          )}
        </>
      ) : !pipeline_id &&
        // !loading &&
        !loadingPipeline &&
        dataPipelines.filter((el) => el.id).length > 0 ? (
        <div className="absolute right-0 top-1/2 flex w-full -translate-y-1/2 transform cursor-pointer items-center justify-center">
          <WrapText className="mr-1" size={16} />
          <span className="text-sm font-medium">
            {t("pipeline.chooseAnotherPipeline")}
          </span>
        </div>
      ) : !pipeline_id &&
        // !loading &&
        !loadingPipeline &&
        dataPipelines.filter((el) => el.id).length === 0 ? (
        <div className="absolute right-0 top-1/2 flex w-full -translate-y-1/2 transform cursor-pointer items-center justify-center  ">
          {/* <WrapText className="mr-1" /> */}
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddPipeline}
            disabled={editingKeyPipeline ? true : false}
          >
            {t("pipeline.createPipelineFirst")}
          </Button>
        </div>
      ) : (
        <>
          {/* <div className="ml-2 mr-2 mt-4 rounded	 px-2.5 py-0.5 text-base font-medium text-[#2253d5] dark:bg-blue-200 dark:text-blue-800">
            {t("pipeline.stage")} {family}
          </div> */}
          {/* <Spin /> */}
          {/* <div className="flex items-center justify-between ">
            <div
              className={`flex w-full pr-4 transition-all duration-200 ease-in ${
                isDeleteRows ? "pl-[220px]" : "pl-4"
              }  `}
            >
              <Input
                prefix={<FiSearch className="text-slate-400" />}
                onChange={(e) => setSearch(e.target.value)}
                placeholder={t("table.search")}
                style={{ width: "250px" }}
                value={search}
                disabled={true}
              />
            </div>

            <Button
              shape="circle"
              icon={<PlusOutlined />}
              disabled={true}
              onClick={handleAdd}
              type="primary"
              className="mr-3"
            />
          </div>
          <NewTableDraggable
            columns={columns}
            setLoading={setLoading}
            isEditing={isEditing}
            data={filteredData}
            setData={setData}
            loading={loading}
            onRow={onRow}
            save={save}
            edit={edit}
            EditableCell={EditableCell}
            onFinishFailed={onFinishFailed}
            cancel={cancel}
            form={form}
            apiRank="/stages/updateRank"
            editingKey={editingKey}
            api="stages"
            btnText={t("helpDesk.addFolder")}
            pagination={false}
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            pageSize={pageSize}
            setPageSize={setPageSize}
          />{" "} */}
        </>
      )}
      <Drawer
        title={t("menu2.fields")}
        placement="right"
        width={"calc(100vw - 270px)"}
        onClose={() => {
          setOpenDrawer(false);
          setGroupId(null);
        }}
        afterOpenChange={(t) => !t && setGroupId(null)}
        open={openDrawer}
        // extra={
        //   <Space>
        //     <Button onClick={() => setOpenDrawer(false)}>Cancel</Button>
        //   </Space>
        // }
      >
        {openDrawer && roles?.includes(user?.role) && (
          <FieldsSettingArea
            // key={crypto.randomUUID()}
            source="drawer"
            groupId={groupId}
            setUpdateFieldProps={setUpdateFieldProps}
            loadFields={false}
            fieldsArray={fieldsArray}
            familyId={familyId}
            setLimit={setLimit}
            limit={limit}
            setTypeFilterId={() => {}}
            typeFilterId={""}
            page={1}
            groupList={groupList}
            setGroupList={setGroupList}
            loadGroups={loadGroups}
            setEditingKey={setEditingKey}
            editingKey={editingKey}
            selectedGroupId={selectedGroupId}
            setSelectedGroupId={setSelectedGroupId}
            setSelectedRowKey={setSelectedRowKey}
            selectedRowKey={selectedRowKey}
            modulesList={modulesList}
            openDrawer={openDrawer}
          />
        )}
      </Drawer>
    </Space>
  );
};
export default Stage;
