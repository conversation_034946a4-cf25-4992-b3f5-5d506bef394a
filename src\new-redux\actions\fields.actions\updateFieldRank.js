import {
  UPDATE_FIELD_RANK_SUCCESS,
  UPDATE_FIELD_RANK_ERROR,
  UPDATE_FIELD_RANK_LOADING,
} from "../../constants";
import MainService from "../../../services/main.service";

export const updateFieldsRank = (payload) => async (dispatch) => {
  try {
    dispatch({ type: UPDATE_FIELD_RANK_LOADING });
    const response = await MainService.changeFieldRank(payload);
    dispatch({
      type: UPDATE_FIELD_RANK_SUCCESS,
      payload: { response, groupId: payload?.groupId },
    });
  } catch (error) {
    if (error?.status !== 401) {
      dispatch({
        type: UPDATE_FIELD_RANK_ERROR,
        payload: error,
      });
    }
  }
};
