import {
  SET_INTERNALCALLFORWARD_SUCCESS,
  SET_INTERNALCALLFORWARD_ERROR,
} from "../../constants";
import { setConfigIPBX } from "../../../pages/voip/services/services";

export const setInternalForward = (formData) => async (dispatch) => {
  try {
    // dispatch({ type: IS_LOADING_VOICE });
    const response = await setConfigIPBX(formData);
    dispatch({
      type: SET_INTERNALCALLFORWARD_SUCCESS,
      payload: response?.data?.data,
    });
  } catch (error) {
    dispatch({
      type: SET_INTERNALCALLFORWARD_ERROR,
      payload: error,
    });
  }
};
