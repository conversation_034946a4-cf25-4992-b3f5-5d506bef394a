import { CloseOutlined } from "@ant-design/icons";
import { Drawer } from "antd";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import Departments from "../../../components/Departments";
import RolesUsers from "../../../components/RolesUsers";
import Services from "../../../components/Services";
import Import from "../../import/import";
import TableCompanies from "../../settings/TableCompanies";
import TypesContacts from "./TypesContacts";
import { uuid } from "pages/layouts/chat/utils/ConversationUtils";

const ImportDrawer = ({
  open,
  setOpen,
  family_id,
  setOpenChildren,
  openChildren,
  typeChildren,
  titleChildren,
}) => {
  const dispatch = useDispatch();
  const [t] = useTranslation("common");
  return (
    <Drawer
      title={`${t("menu1.import")} ${t("menu1.family", {
        family:
          family_id === 1
            ? t("menu2.companiesOnly")
            : family_id === 2
            ? t("menu2.contacts")
            : family_id === 3
            ? t("menu1.deals")
            : family_id === 4
            ? t("menu2.users")
            : family_id === 5
            ? t("menu2.products")
            : family_id === 6
            ? t("menu2.ticket")
            : family_id === 7
            ? t("menu2.project")
            : "",
      })}`}
      placement="right"
      width={window.innerWidth / 1.5}
      open={open}
      closeIcon={
        <CloseOutlined
          onClick={() => {
            dispatch(setOpen(false));
          }}
        />
      }
    >
      <Import key={uuid()} familyId={family_id} fromDrawer={true} />
      <Drawer
        title={t("import.addType", { title: titleChildren })}
        placement="right"
        open={openChildren}
        width={window.innerWidth / 2.5}
        closeIcon={
          <CloseOutlined
            onClick={() => {
              dispatch(setOpenChildren({ open: false, type: typeChildren }));
            }}
          />
        }
      >
        {typeChildren === "contact_type" && <TypesContacts />}

        {typeChildren === "Company" && <TableCompanies />}

        {typeChildren === "department" && <Departments />}

        {typeChildren === "Services" && <Services />}
        {typeChildren === "roles" && <RolesUsers />}
      </Drawer>
    </Drawer>
  );
};

export default ImportDrawer;
