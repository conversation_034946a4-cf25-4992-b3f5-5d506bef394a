import { toastNotification } from "components/ToastNotification";
import MainService from "services/main.service";

const convertLeadToContact = async (
  leadInfo = {},
  t,
  setTableIsLoading = () => {},
  setShouldFetchData = () => {}
) => {
  if (!leadInfo.id) return;

  try {
    setTableIsLoading(true);
    await MainService.convertToContact(leadInfo.id);
    setShouldFetchData(true);
    toastNotification(
      "success",
      `${leadInfo?.label} ${t("vue360.convertedToContact")}`,
      "topRight"
    );
  } catch (err) {
    setTableIsLoading(false);
    toastNotification("error", t("toasts.somethingWrong"), "topRight");
  }
};

export default convertLeadToContact;
