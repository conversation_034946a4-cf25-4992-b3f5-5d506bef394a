import { useState } from "react";
import { <PERSON><PERSON>, Too<PERSON><PERSON> } from "antd";
import { UploadOutlined } from "@ant-design/icons";
//
import { exportCallsLog } from "../services/services";
import { toastNotification } from "../../../components/ToastNotification";
import { useTranslation } from "react-i18next";
// import { roles } from "../../../utils/role";
import { URL_ENV } from "index";

const ExportButton = ({
  search,
  startDate,
  endDate,
  filter,
  isGroupLog,
  disabled,
}) => {
  const [t] = useTranslation("common");

  const downloadPrefix = URL_ENV.REACT_APP_DOWNLOAD_FILE_PREFIX;
  const [loading, setLoading] = useState(false);

  const handleClick = async () => {
    try {
      setLoading(true);
      const response = await exportCallsLog(
        search,
        startDate,
        endDate,
        filter,
        isGroupLog
      );

      const url = window.URL.createObjectURL(new Blob([response?.data]));
      const link = document.createElement("a");
      link.href = url;
      const attribute = `${downloadPrefix}_${
        isGroupLog ? t("voip.groups_queues") : t("menu1.callLog")
      }${
        startDate && endDate
          ? `_${startDate.replace(" ", "-")}/${endDate.replace(" ", "-")}`
          : ""
      }${search ? `_(${search})` : ""}.xlsx`;
      link.setAttribute("download", attribute);

      document.body.appendChild(link);
      link.click();
      setLoading(false);
    } catch (err) {
      toastNotification("error", t("toasts.somethingWrong"), "topRight");
      setLoading(false);
      throw new Error(err?.message ? `error: ${err.message}` : { err });
    }
  };

  return (
    <Tooltip
      title={
        !startDate && !endDate
          ? "Sélectionner une plage de dates pour l'exportation des données"
          : t("voip.exportLog")
      }
    >
      <Button
        onClick={handleClick}
        loading={loading}
        disabled={disabled || (!startDate && !endDate)}
        icon={<UploadOutlined />}
      />
    </Tooltip>
  );
};

export default ExportButton;
