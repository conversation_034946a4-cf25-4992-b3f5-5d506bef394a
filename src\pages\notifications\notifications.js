import React, { useEffect, useState } from "react";
import { List, Skeleton, Space, Tag } from "antd";
import VirtualList from "rc-virtual-list";

import { generateAxios } from "../../services/axiosInstance";
import { AvatarChat } from "../../components/Chat";
import { getName } from "../layouts/chat/utils/ConversationUtils";
import { URL_ENV } from "index";

const Notifications = () => {
  const [data, setData] = useState([]);
  const [screenHeight, setScreenHeight] = useState(0);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [lastPage, setLastPage] = useState(1);
  useEffect(() => {
    setScreenHeight(window.innerHeight);
  }, []);
  const appendData = async () => {
    setLoading(true);

    try {
      const { data: notif } = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).get(`/notifications?limit=15&page=${page}`);
      setLastPage(notif.last_page);

      setData((prev) => prev.concat(notif.data));
      setLoading(false);
    } catch (err) {
      setLoading(false);
    }
  };

  useEffect(() => {
    appendData();
  }, [page]);
  const updateNotification = async (id) => {
    try {
      const { data } = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).put(`/notifications/${id}`);
      // console.log(data);
      setData((prev) => prev.map((el) => (el._id === data._id ? data : el)));
      // setLoading(false);
    } catch (err) {
      setLoading(false);
      // toastNotification("error", t("toasts.somethingWrong"), "topRight");
    }
  };
  const ContainerHeight = parseInt(screenHeight - 120);
  console.log(ContainerHeight);

  const onScroll = (e) => {
    if (
      e.currentTarget.scrollHeight - e.currentTarget.scrollTop ===
      e.currentTarget.clientHeight
    ) {
      if (page < lastPage) {
        setPage((prev) => prev + 1);
      }
    }
  };
  const IconText = ({ icon, text }) => (
    <Space className="pl-2">
      {console.log(icon)}
      {React.createElement(icon)}
      {text}
    </Space>
  );
  return (
    <div className="p-6">
      <List itemLayout="vertical">
        <VirtualList
          data={data}
          loading={loading}
          height={ContainerHeight}
          itemHeight={47}
          itemKey="email"
          onScroll={onScroll}
          // style={{ padding: 25 }}
        >
          {(item) => (
            //   <List.Item key={item.email} className="hover:bg-slate-100 ">
            //     <List.Item.Meta
            //       avatar={<Avatar src={item.picture.large} />}
            //       title={<a href="https://ant.design">{item.name.last}</a>}
            //       description={item.email}
            //       className="pl-1"
            //     />
            //     <div className="pr-1">Content</div>
            //   </List.Item>
            <List.Item
              key={item.title}
              onClick={() => updateNotification(item._id)}
              className="hover:bg-slate-100"
              actions={[
                // <IconText
                //   icon={
                //     item?.receivers?.seen == 1
                //       ? EyeOutlined
                //       : EyeInvisibleOutlined
                //   }
                //   text={item?.receivers?.seen == 1 ? "Seen" : "Not seen"}
                //   key="list-vertical-star-o"
                // />,
                // <IconText
                //   icon={
                //     item?.receivers?.read == 1 ? ReadOutlined : WalletOutlined
                //   }
                //   text={item?.receivers?.read == 1 ? "Read" : "Unread"}
                //   key="list-vertical-like-o"
                // />,
                <Tag color="blue" style={{ marginLeft: "10px" }}>
                  {item?.type}
                </Tag>,

                <Tag color="geekblue">{item?.topic}</Tag>,
              ]}
              extra={
                <div className="pr-2">
                  {item?.receivers?.length > 0 &&
                  item?.receivers[0]?.read == 0 ? (
                    <span
                      className=""
                      style={{
                        height: "12px",
                        width: "12px",
                        backgroundColor: "#1976F1",
                        borderRadius: "50%",
                        display: "inline-block",
                      }}></span>
                  ) : null}
                </div>
              }>
              <Skeleton avatar title={false} loading={loading} active>
                <List.Item.Meta
                  avatar={
                    <AvatarChat
                      url={
                        URL_ENV?.REACT_APP_BASE_URL +
                        process.env.REACT_APP_SUFFIX_API_FILE +
                        item.user_image
                      }
                      type="user"
                      size={38}
                      height={10}
                      width={10}
                      name={getName(item?.user_name, "avatar")}
                      hasImage={
                        item.user_image &&
                        item.user_image !== "/storage/uploads/"
                      }
                    />
                  }
                  // title={<a href={item.href}>{item.name.last}</a>}
                  title={item.user_name}
                  description={item.title}
                  className="pl-2"
                />
              </Skeleton>
            </List.Item>
          )}
        </VirtualList>
      </List>
    </div>
  );
};
export default Notifications;
