import React, { Suspense } from "react";
import { useSelector } from "react-redux";
import { LoadingAnimation } from "../../../components/loader";
import VisioComponent from "./visio-component";

function VisioContainer() {
  const { isOpen, visioParams } = useSelector((state) => state?.visio);

  return isOpen && !visioParams?.external ? (
    <Suspense fallback={<LoadingAnimation />}>
      <VisioComponent />
    </Suspense>
  ) : (
    <></>
  );
}

export default VisioContainer;
