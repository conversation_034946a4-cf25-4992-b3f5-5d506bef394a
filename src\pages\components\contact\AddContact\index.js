import React from "react";
import { Avatar, Input, Modal } from "antd";
import { URL_ENV } from "index";

import IntlMessages from "util/IntlMessages";
import axios from "axios";
const BackendURL = "cmkvoip.comunikcrm.info";
const token = localStorage.getItem("token");
const compte = URL_ENV?.REACT_APP_VOIP_CLIENT ?? "client";
var userInfo = localStorage.getItem('infoUser');
const posteUser = (userInfo === null) ? "" : JSON.parse(userInfo).collegues[0].poste;

class AddContact extends React.Component {
  constructor(props) {
    super(props);

    const { id, thumb, name, email, phone, designation, selected, starred, frequently } = props.contact;
    this.state = {
      id,
      thumb,
      name,
      email,
      phone,
      designation,
      selected,
      starred,
      frequently
    }
  }


  render() {

    function addContactRequest(nom, mail, mobile, adress) {
      var addContactConfigAPI = {
        method: "get",
        url: `https://${BackendURL}:4444/comunik_ipbx/Api/WS/CRUDContactCarnetAdresseIPBXV3API?compte=${compte}&poste=${posteUser}&action=create&id&nom_prenom=${nom}&email=${mail}&adresse=${adress}&tel_portable=${mobile}&tel_fix=&tel_3=&tel_4=&tel_5=&commentaire=`,
        headers: {
          Authorization: token,
        },
      };
      axios(addContactConfigAPI)
        .then((res) => {

        })

    };

    const { onSaveContact, onContactClose, open, contact } = this.props;
    const { id, name, email, phone, designation, selected, starred, frequently } = this.state;
    let { thumb } = this.state;
    if (!thumb) {
      thumb = '/assets/images/placeholder.jpg';
    }
    return (
      <Modal
        title={contact.name === '' ?
          <IntlMessages id="contact.addContact" /> :
          <IntlMessages id="contact.saveContact" />}
        toggle={onContactClose} visible={open}
        closable={false}
        onOk={() => {
          if (name === '')
            return;
          addContactRequest(name, email, phone, designation);
          onContactClose();
          onSaveContact(
            {
              'id': id,
              'name': name,
              'thumb': thumb,
              'email': email,
              'phone': phone,
              'designation': designation,
              'selected': selected,
              'starred': starred,
              'frequently': frequently
            });
          this.setState({
            'id': id + 1,
            'name': '',
            'thumb': '',
            'email': '',
            'phone': '',
            'designation': '',
          })

        }}
        onCancel={onContactClose}>

        <div className="gx-modal-box-row">
          <div className="gx-modal-box-avatar">
            <Avatar size="large" src={thumb} />
          </div>

          <div className="gx-modal-box-form-item">
            <div className="gx-form-group">
              <Input
                required
                placeholder="Name"
                onChange={(event) => this.setState({ name: event.target.value })}
                defaultValue={name}
                margin="none" />
            </div>
            <div className="gx-form-group">
              <Input
                placeholder="Email"
                onChange={(event) => this.setState({ email: event.target.value })}
                value={email}
                margin="normal" />
            </div>
            <div className="gx-form-group">
              <Input
                placeholder="Phone"
                onChange={(event) => this.setState({ phone: event.target.value })}
                value={phone}
                margin="normal"
              />
            </div>
            <div className="gx-form-group">
              <Input
                placeholder="Designation"
                onChange={(event) => this.setState({ designation: event.target.value })}
                value={designation}
                autosize={{ minRows: 2, maxRows: 6 }}
                margin="normal" />
            </div>
          </div>
        </div>
      </Modal>
    );
  }
}

export default AddContact;
