import {
  IS_LOADING_WIKI,
  CREATE_GROUP_WIKI_SUCCESS,
  CREATE_GROUP_WIKI_ERROR,
  GET_GROUP_WIKI_SUCCESS,
  GET_GROUP_WIKI_ERROR,
  DELETE_GROUP_WIKI_SUCCESS,
  DELETE_GROUP_WIKI_ERROR,
  EDIT_GROUP_WIKI_SUCCESS,
  EDIT_GROUP_WIKI_ERROR,
  RESET_STATE,
} from "../constants";

const initialState = {
  groupWiki: [],
  errors: {},
  isLoading: false,
  new: "",
  loadingTable: true,
};

const wiki = (state = initialState, action) => {
  const { type, payload } = action;
  switch (type) {
    case IS_LOADING_WIKI:
      return {
        ...state,
        isLoading: true,
      };
    case GET_GROUP_WIKI_SUCCESS:
      let groups = [];
      payload.map((gr) => {
        groups.push({ ...gr, key: gr.id });
      });

      return {
        ...state,
        isLoading: false,
        groupWiki: groups,
        loading: false,
      };

    case CREATE_GROUP_WIKI_SUCCESS:
      return {
        ...state,
        isLoading: false,
        groupWiki: [
          ...state?.groupWiki,
          {
            id: payload.id,
            key: payload.id,
            label_fr: payload.label_fr,
            value: payload.value,
            label_en: payload.label_en,
            description_fr: payload.description_fr,
            description_en: payload.description_en,
            image: payload.image,
            default: payload.default,
          },
        ],
        new: payload.id,
      };
    case DELETE_GROUP_WIKI_SUCCESS:
      return {
        ...state,
        isLoading: false,
        groupWiki: state?.groupWiki.filter(
          (element) => element?.id != Number(payload)
        ),
      };
    case EDIT_GROUP_WIKI_SUCCESS:
      let groupIndex = state?.groupWiki.findIndex(
        (element) => element?.id == payload?.id
      );

      return {
        ...state,
        isLoading: false,
        groupWiki: [
          ...state?.groupWiki.map((item, i) =>
            groupIndex === i
              ? {
                  id: payload.response.id,
                  key: payload.response.id,
                  label_fr: payload.response.label_fr,
                  value: payload.response.value,
                  label_en: payload.response.label_en,
                  description_fr: payload.response.description_fr,
                  description_en: payload.response.description_en,
                  image: payload.response.image,
                }
              : item
          ),
        ],
      };

    case CREATE_GROUP_WIKI_ERROR:
    case GET_GROUP_WIKI_ERROR:
    case DELETE_GROUP_WIKI_ERROR:
    case EDIT_GROUP_WIKI_ERROR:
      console.log("erreur", payload);
      return {
        ...state,
        isLoading: false,
        errors: payload,
        loading: false,
      };
    case RESET_STATE: {
      return initialState;
    }
    default:
      return state;
  }
};

export default wiki;
