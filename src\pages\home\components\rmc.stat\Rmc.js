import { URL_ENV } from "index";
import React, { useEffect, useState } from "react";
import { generateAxios } from "services/axiosInstance";
import {
  DonutChart2,
  DonutChartWithDrillDown,
  OneBarChart,
} from "../ChartsDashboard";
import { Card, Col, Row } from "antd";
import { useTranslation } from "react-i18next";
import Card<PERSON><PERSON> from "../CardStat";
import dayjs from "dayjs";
import { useSelector } from "react-redux";
const KPI_ENDPOINTS = {
  STAT_EMAIL: "/stat-email",
};
const DashboardRmc = ({ start, end }) => {
  const [dataConvPerDep, setDataConvPerDep] = useState({});
  const [leads, setLeads] = useState({});
  const [agents, setAgents] = useState({});
  const [channels, setChannels] = useState({});
  const { user } = useSelector((state) => state.user);
  const { i18n } = useTranslation("common");

  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await generateAxios(
          `${URL_ENV?.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
        ).get(
          `/getchartBydepartment?date_start=${dayjs(
            start,
            user?.location?.date_format
          ).format("DD-MM-YYYY")}&date_end=${dayjs(
            end,
            user?.location?.date_format
          ).format("DD-MM-YYYY")}&lang=${i18n.language}`
        );
        const series = []; // Données principales pour le donut
        const drilldown = []; // Données pour les détails (drilldown)

        if (res.data.response && res.data.response.data) {
          for (const [department, channels] of Object.entries(
            res.data.response.data
          )) {
            const total = channels.reduce((sum, channel) => sum + channel.y, 0);

            series.push({
              name: department,
              y: total,
              drilldown: department, // Relier au drilldown
            });

            drilldown.push({
              name: department,
              id: department,
              data: channels.map((channel) => [channel.name, channel.y]),
            });
          }
        }

        setDataConvPerDep({
          ...res.data.response,
          drilldown: { series: drilldown },
          series,
        });
        // setDataConvPerDep(res.data.response);
      } catch (err) {}
    };
    fetchData();
  }, [start, end, i18n.language]);
  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await generateAxios(
          `${URL_ENV?.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
        ).get(
          `/get-chart-by-lead?date_start=${dayjs(
            start,
            user?.location?.date_format
          ).format("DD-MM-YYYY")}&date_end=${dayjs(
            end,
            user?.location?.date_format
          ).format("DD-MM-YYYY")}&lang=${i18n.language}`
        );
        setLeads(res.data.response);
      } catch (err) {}
    };

    fetchData();
  }, [start, end, i18n.language]);
  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await generateAxios(
          `${URL_ENV?.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
        ).get(
          `/get-chart-by-agent?date_start=${dayjs(
            start,
            user?.location?.date_format
          ).format("DD-MM-YYYY")}&date_end=${dayjs(
            end,
            user?.location?.date_format
          ).format("DD-MM-YYYY")}&lang=${i18n.language}`
        );
        setAgents(res.data.response);
      } catch (err) {}
    };
    fetchData();
  }, [start, end, i18n.language]);
  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await generateAxios(
          `${URL_ENV?.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
        ).get(
          `/get-chart-by-channel?date_start=${dayjs(
            start,
            user?.location?.date_format
          ).format("DD-MM-YYYY")}&date_end=${dayjs(
            end,
            user?.location?.date_format
          ).format("DD-MM-YYYY")}&lang=${i18n.language}`
        );
        setChannels(res.data.response);
      } catch (err) {}
    };
    fetchData();
  }, [start, end, i18n.language]);
  return (
    <Row gutter={[16, 16]}>
      <Col className="gutter-row" span={12}>
        <CardStat title={dataConvPerDep?.name}>
          <DonutChartWithDrillDown data={{ ...dataConvPerDep, name: "" }} />
        </CardStat>
      </Col>
      <Col className="gutter-row" span={12}>
        <CardStat title={leads?.name}>
          <OneBarChart data={{ ...leads, name: "" }} />
        </CardStat>
      </Col>
      <Col className="gutter-row" span={12}>
        <CardStat title={agents?.name}>
          <DonutChart2
            data={agents?.data}
            total={agents?.total}
            name={""}
            height={400}
          />
        </CardStat>
      </Col>
      <Col className="gutter-row" span={12}>
        <CardStat title={channels?.name}>
          <DonutChart2
            data={channels?.data}
            total={channels?.total}
            name={""}
            height={400}
          />
        </CardStat>
      </Col>
    </Row>
  );
};

export default DashboardRmc;
