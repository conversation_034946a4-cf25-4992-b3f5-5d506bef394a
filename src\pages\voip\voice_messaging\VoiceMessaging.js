import { useState, useEffect, useCallback, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
//
import { DatePicker, Empty, Input, Radio, Switch, Table, Tooltip } from "antd";
import { FiSearch } from "react-icons/fi";
//
import { toastNotification } from "../../../components/ToastNotification";
import { useWindowSize } from "../../clients&users/components/WindowSize";
import {
  rangePresets,
  disabledDate,
  disabledTime,
  handleDateTimeRange,
  billSecToHumanReadable,
  humanDate,
  handlePageSizeOptions,
  formatDatePickerRange,
} from "../helpers/helpersFunc";
import { useTranslation } from "react-i18next";
import moment from "moment";
import dayjs from "dayjs";
import "../index.css";
import {
  changeStateVoiceMsg,
  getFirstCallDate,
  getVoicesMailLog,
  seenCallsOrVoicesOrGroups,
} from "../services/services";
import FormCreate from "../../clients&users/components/FormCreate";
import DisplayElementInfo from "../components/DisplayElementInfo";
import useActionCall from "../helpers/ActionCall";
import { RESET_VOICE_MESSAGING } from "../../../new-redux/constants";
import DurationColumn from "../components/DurationColumn";
import CallerColumnMessaging from "../components/CallerColumnMessaging";
import "../../clients&users/index.css";
import { URL_ENV } from "index";
import { debounce } from "lodash";

const VoiceMessaging = () => {
  //
  const tableRef = useRef(null);
  //
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const call = useActionCall();
  const user = useSelector(({ user }) => user?.user);
  const poste = `${user?.extension}`;
  const voiceMessaging = useSelector((state) => state.voip.voice);
  // const loading = useSelector((state) => state.voip.loading);
  const { nbrVoiceMessaging } = useSelector(({ voip }) => voip);
  const windowSize = useWindowSize();
  //
  const [shouldFetchData, setShouldFetchData] = useState(true);
  const [dataSource, setDataSource] = useState([]);
  const [loadingTable, setLoadingTable] = useState(false);
  const [totalLogs, setTotalLogs] = useState(0);
  const [isDataExist, setIsDataExist] = useState(true);
  const [filterLog, setFilterLog] = useState(null);
  const [dateRange, setDateRange] = useState([null, null]);
  const [search, setSearch] = useState("");
  const [displaySearch, setDisplaySearch] = useState("");
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(20);
  const [disableDateToStart, setDisableDateToStart] = useState("");
  const [familyToAdd, setFamilyToAdd] = useState(null);
  const [openDrawerCreate, setOpenDrawerCreate] = useState(false);
  const [elementDetails, setElementDetails] = useState({});
  const [nbrPhoneToAdd, setNbrPhoneToAdd] = useState(null);
  const [openDrawerInfo, setOpenDrawerInfo] = useState(false);
  const [catchChange, setCatchChange] = useState(true);
  const [showTime, setShowTime] = useState(null);
  //
  // console.log({ dataSource });
  //
  useEffect(() => {
    setShouldFetchData(true); // Set the trigger for fetching data
    setIsDataExist(true);
  }, [page, search, dateRange, filterLog, limit, catchChange]);

  useEffect(() => {
    setPage(1);
  }, [search, filterLog, dateRange]);
  //
  // fetch voices mail log
  useEffect(() => {
    let isMounted = true;
    const getLog = async () => {
      if (!shouldFetchData) return;
      else if (isMounted) {
        try {
          setLoadingTable(true);
          const {
            data: {
              data,
              meta: { total },
            },
          } = await getVoicesMailLog(
            limit,
            page,
            search,
            dateRange?.[0],
            dateRange?.[1],
            filterLog
          );
          // console.log({ data, total });
          setDataSource(handleVoicesLog(data));
          setTotalLogs(total);
          setLoadingTable(false);
          if (data.length) {
            if (!disableDateToStart) {
              const {
                data: { first_call_date },
              } = await getFirstCallDate("voice-mail");
              setDisableDateToStart(first_call_date);
            }
          } else setIsDataExist(false);
        } catch (err) {
          if (err?.response?.status === 401 || err?.code === "ERR_CANCELED")
            return;
          else {
            toastNotification("error", t("toasts.somethingWrong"), "topRight");
            throw new Error(err?.message ? err.message : err);
          }
        } finally {
          setLoadingTable(false);
          setShouldFetchData(false);
        }
      }
    };
    //
    getLog();
    //
    return () => {
      isMounted = false; // Set it to false when component unmounts
      seenCallsOrVoicesOrGroups("voice_mail");
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shouldFetchData]);
  //
  // Catch new voice mail
  const catchNewVoiceMail = useCallback(() => {
    if (
      page !== 1 &&
      search?.length &&
      filterLog &&
      dateRange[0] &&
      dateRange[1]
    )
      return;
    if ((loadingTable && isDataExist) || !voiceMessaging.length) return;
    const newVoices = [];
    const lastVoiceId = dataSource?.[0]?._id;
    if (!lastVoiceId) return;
    for (const index in voiceMessaging) {
      const voice = voiceMessaging[index];
      if (voice?._id === lastVoiceId) break;
      else newVoices.push(voice);
    }
    if (newVoices.length) {
      setDataSource((prev) => [...handleVoicesLog(newVoices), ...prev]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [voiceMessaging]);

  useEffect(() => {
    catchNewVoiceMail();
  }, [catchNewVoiceMail]);
  //
  const handleNotifBadge = useCallback(() => {
    if (nbrVoiceMessaging) {
      dispatch({ type: RESET_VOICE_MESSAGING });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [nbrVoiceMessaging]);

  useEffect(() => {
    handleNotifBadge();
  }, [handleNotifBadge]);
  //
  //
  const playAudio = async (audio_id) => {
    try {
      const resp = await changeStateVoiceMsg(poste, audio_id);
      if (resp?.status === 200) {
        setDataSource((prev) =>
          prev?.map((voice) =>
            voice?.id === audio_id ? { ...voice, state: "Consulté" } : voice
          )
        );
        return;
      }
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      console.error(err);
    }
  };
  //
  const handleAddItem = (familyId, phoneNbr) => {
    setFamilyToAdd(familyId);
    setNbrPhoneToAdd(phoneNbr);
    setOpenDrawerCreate(true);
  };

  const handleDisplayElementInfo = (name, info) => {
    setElementDetails({
      label: name,
      id: info?.id,
      familyId: info?.familyId,
    });
    setOpenDrawerInfo(true);
  };
  //
  const columns = [
    {
      title: t("table.header.caller"),
      dataIndex: "name",
      key: "name",
      render: (_, record, index) => (
        <CallerColumnMessaging
          key={index}
          record={record}
          t={t}
          call={call}
          searchText={search}
          currentUser={user}
          handleDisplayElementInfo={handleDisplayElementInfo}
          handleAddItem={handleAddItem}
          dispatch={dispatch}
        />
      ),
    },
    {
      title: "Date",
      dataIndex: "date",
      key: "date",
      render: (date, { state }, index) => (
        <Tooltip key={index} title={moment(date).format("llll")}>
          <span
            key={index}
            className={state === "Nouveau" ? "font-semibold" : undefined}
          >
            {humanDate(date, t)}
          </span>
        </Tooltip>
      ),
    },
    {
      title: t("table.header.duration"),
      dataIndex: "duration",
      key: "duration",
      // width: "150px",
      // align: "center",
      render: (duration, { audio, id, state }, index) => (
        <CenterContents key={index} vertical={"center"}>
          {audio ? (
            <DurationColumn
              key={index}
              audioRecording={audio}
              // width={getColumnWidth(2)}
              state={state}
              id={id}
              actionSeen={playAudio}
              source="messaging"
              duration={duration}
              t={t}
            />
          ) : null}
        </CenterContents>
      ),
    },
  ];
  //
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedSearch = useCallback(
    debounce((nextValue) => setSearch(nextValue), 1000),
    []
  );

  const handleSearch = (event) => {
    const inputValue = event?.target?.value;
    setDisplaySearch(inputValue);
    const cleanInputValue = inputValue.trim();
    if (cleanInputValue.length > 2) debouncedSearch(cleanInputValue);
    else debouncedSearch("");
  };
  //
  const handleSwitchChange = (checked) => {
    if (checked) {
      setShowTime({
        format: "HH:mm",
        defaultValue: [dayjs("00:00", "HH:mm"), dayjs("23:59", "HH:mm")],
      });
    } else {
      setShowTime(null);
    }
  };
  //
  const handleFilterState = (e) => {
    const val = e.target.value;
    if (val === "all") {
      setFilterLog(null);
      return;
    } else {
      setFilterLog(val);
      return;
    }
  };
  //
  const handleShowTotal = (total, range) => (
    <span>{`${range[0]}-${range[1]} of ${total} `}</span>
  );
  //
  if (window.location.pathname !== "/telephony/voicemail") return null;

  //
  return (
    <div className="relative w-full space-y-2 pt-0">
      <div className="flex w-full justify-between px-6">
        <div className="flex flex-row space-x-2">
          <Input
            style={{ width: "300px" }}
            allowClear
            placeholder={`${t("voip.searchDirectory")}`}
            value={displaySearch}
            onChange={handleSearch}
            prefix={<FiSearch className="h-4 w-4 text-slate-400" />}
            disabled={!dataSource?.length && !search?.length}
          />
          <Radio.Group
            block
            value={filterLog === "unread" ? "unread" : "all"}
            options={[
              { label: t("voip.all"), value: "all" },
              { label: t("voip.unread"), value: "unread" },
            ]}
            onChange={handleFilterState}
            buttonStyle="solid"
            optionType="button"
            disabled={!dataSource?.length && !filterLog}
          />
          {/* <Radio.Button key={1} value={"all"}>
              {t("voip.all")}
            </Radio.Button>
            <Radio.Button key={2} value="unread">
              {t("voip.unread")}
            </Radio.Button>
          </Radio.Group> */}
        </div>
        <div>
          <DatePicker.RangePicker
            disabled={!dataSource?.length && !dateRange[0] && !dateRange[1]}
            placement="bottomRight"
            presets={rangePresets(t)}
            onChange={(date, dateString) =>
              handleDateTimeRange(
                dateString,
                setDateRange,
                showTime !== null,
                user?.location
              )
            }
            format={formatDatePickerRange(showTime, user?.location)}
            showTime={showTime}
            renderExtraFooter={() => (
              <div className="ml-2">
                <span>{t("voip.displayTime")}: </span>
                <Switch
                  size="small"
                  // defaultChecked
                  onChange={handleSwitchChange}
                />
              </div>
            )}
            allowClear
            disabledDate={(current) =>
              disabledDate(disableDateToStart, current)
            }
            disabledTime={disabledTime}
          />
        </div>
      </div>
      <div className="table-view">
        <Table
          ref={tableRef}
          columns={columns}
          dataSource={dataSource}
          rowClassName={(record) =>
            record.state === "Nouveau" ? "unread-voice" : ""
          }
          loading={
            loadingTable || (!loadingTable && !dataSource.length && isDataExist)
          }
          size={"small"}
          pagination={
            totalLogs <= 10
              ? false
              : {
                  showTotal: (total, range) => handleShowTotal(total, range),
                  showSizeChanger: true,
                  showQuickJumper: true,
                  total: totalLogs,
                  pageSize: limit,
                  current: page,
                  onChange: (page) => setPage(page),
                  onShowSizeChange: (current, size) => setLimit(size),
                  pageSizeOptions:
                    totalLogs && handlePageSizeOptions(totalLogs),
                  size: "small",
                }
          }
          locale={{
            emptyText:
              loadingTable || isDataExist ? (
                <div className=" flex h-64 flex-row items-center justify-center space-x-2 text-xl">
                  {t("contacts.loadingDataTable")}
                  <span className="animate-bounce">...</span>
                </div>
              ) : (
                <Empty />
              ),
          }}
          scroll={{
            y: windowSize?.height - 290,
          }}
        />
      </div>
      <FormCreate
        open={openDrawerCreate}
        setOpen={setOpenDrawerCreate}
        familyId={familyToAdd}
        setCatchChange={setCatchChange}
        nbrPhoneFromVoip={nbrPhoneToAdd}
      />
      <DisplayElementInfo
        open={openDrawerInfo}
        setOpen={setOpenDrawerInfo}
        elementDetails={elementDetails}
      />
    </div>
  );
};

const CenterContents = ({ vertical, horizontal, children }) => (
  <div
    style={{
      display: "flex",
      alignItems: vertical,
      justifyContent: horizontal,
      height: "2.5rem",
    }}
  >
    {children}
  </div>
);

export const handleVoicesLog = (data) =>
  data.length
    ? data?.map((voice) => ({
        key: voice.id,
        id: voice?.id,
        _id: voice?._id,
        info: {
          id: voice?.caller_id,
          familyId: voice?.caller_family_id,
          uuid: voice?.caller_uuid,
        },
        name: voice?.caller_name?.replaceAll("_", " "),
        image:
          voice?.caller_image &&
          `${
            URL_ENV?.REACT_APP_BASE_URL + URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
          }${voice?.caller_image}`,
        number: `${voice?.caller_num}`,
        duration:
          voice?.duration === "00:00:00"
            ? null
            : billSecToHumanReadable(voice?.duration),
        date: voice?.origtime,
        audio: voice?.chemain_fichier_audio,
        state: voice?.etat,
      }))
    : [];

export default VoiceMessaging;
