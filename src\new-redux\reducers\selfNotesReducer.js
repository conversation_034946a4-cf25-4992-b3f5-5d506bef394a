const initialState = {
  slefNotes: [],
  triggerSave: false,
  selfNotePending: false,
  selfNoteIsSaved: false,
  selfNoteErrorSaving: false,
  selectedNote: null,
  savePending: false,
  fetched: false,
  notificationsCount: null,
  notificationsList: null,
  noteOpenElementModal: null,
};

const selfNotesReducer = (state = initialState, action) => {
  const { type, payload } = action;
  switch (type) {
    case "SET_SELF_NOTES":
      return {
        ...state,
        slefNotes: payload,
        fetched: true,
      };
    case "TRIGGER_SAVE_NOTE":
      return {
        ...state,
        triggerSave: !state.triggerSave,
      };

    case "SAVE_SELF_NOTE_PENDING":
      return {
        ...state,
        selfNotePending: !state.selfNotePending,
      };

    case "SAVE_SELF_NOTE_SUCCESS":
      return {
        ...state,
        selfNoteIsSaved: !state.selfNoteIsSaved,
      };

    case "SAVE_SELF_NOTE_ERROR":
      return {
        ...state,
        selfNoteErrorSaving: !state.selfNoteErrorSaving,
      };

    case "MODIFY_SELF_NOTE":
      return {
        ...state,
        slefNotes: state.slefNotes.map((note) =>
          note._id == payload._id ? payload : note
        ),
      };

    case "DELETE_SELF_NOTE":
      return {
        ...state,
        slefNotes: state.slefNotes.filter((note) => note._id != payload),
      };

    case "ADD_SELF_NOTE":
      return {
        ...state,
        slefNotes: [payload, ...state.slefNotes],
      };

    case "ADD_SELF_NOTES_TO_LIST":
      let mergedNotes = [...state.slefNotes, ...payload];

      for (let i = 0; i < mergedNotes.length; i++) {
        for (let j = i + 1; j < mergedNotes.length; j++) {
          if (mergedNotes[i]._id == mergedNotes[j]._id) {
            mergedNotes.splice(j, 1);
          }
        }
      }

    case "SAVE_NEW_NOTE_AFTER_POST":
      console.log("PAYLOAD", payload);

      let noteLocalId = payload.localId;

      //replace the old note with the new note also the _id
      let result = state.slefNotes.map((note) => {
        if (note._id == noteLocalId) {
          return payload.newNote;
        }
        return note;
      });

      if (state.selectedNote?._id == noteLocalId) {
        return {
          ...state,
          slefNotes: result,
          selectedNote: payload.newNote,
        };
      }

      return {
        ...state,
        slefNotes: result,
      };

    case "REMOVE_NEW_NOTES_ON_UNMOUNT":
      //remove all notes with newNote = true
      let result2 = state.slefNotes.filter((note) => note?.newNote != true);

      return {
        ...state,
        slefNotes: result2,
      };

    case "SET_SELECTED_NOTE":
      return {
        ...state,
        selectedNote: payload,
      };

    case "REMOVE_SELECTED_NOTE":
      return {
        ...state,
        selectedNote: null,
      };

    case "MODIFY_SELECTED_NOTE_CONTENT":
      return {
        ...state,
        selectedNote: {
          ...state.selectedNote,
          content: payload,
        }, //search for the note and modify the content
        // slefNotes: state.slefNotes.map((note) =>
        //   note._id == state.selectedNote._id
        //     ? { ...note, content: payload }
        //     : note
        // ),
      };

    case "SET_NOTE_UNLOCKED":
      //search for the note and set it to unlocked
      let result3 = state.slefNotes.map((note) => {
        if (note._id == payload) {
          return {
            ...note,
            is_locked: false,
            locker_id: null,
            locker_uuid: null,
          };
        }
        return note;
      });

      return {
        ...state,
        slefNotes: result3,
      };

    case "SET_NOTE_LOCKED":
      //search for the note and set it to locked
      let result4 = state.slefNotes.map((note) => {
        if (note._id == payload.noteId) {
          return {
            ...note,
            is_locked: true,
            locker_id: payload.lockerId,
            locker_uuid: payload.lockerUuid,
          };
        }
        return note;
      });

      return {
        ...state,
        slefNotes: result4,
      };

    case "SET_SAVE_PENDING":
      return {
        ...state,
        savePending: true,
      };

    case "UNSET_SAVE_PENDING":
      return {
        ...state,
        savePending: false,
      };

    case "REMOVE_IS_NEW_NOTE":
      //remove all notes with isNew = true
      let result5 = state.slefNotes.filter((note) => note?.isNew != true);

      return {
        ...state,
        slefNotes: result5,
      };

    case "UPDATE_NOTE_FAMILY_AND_ELEMENT":
      let result6 = state.slefNotes.map((note) => {
        if (note._id == payload._id) {
          return {
            ...note,
            family_id: payload.family_id,
            element_id: payload.element_id,
            element_data: payload.element_data,
            family_data: payload.family_data,
          };
        }
        return note;
      });

      return {
        ...state,
        slefNotes: result6,
      };

    case "SET_NOTES_NOTIFICATIONS_COUNT":
      return {
        ...state,
        notificationsCount: payload,
      };

    case "INCREMENT_NOTES_NOTIFICATIONS_COUNT":
      return {
        ...state,
        notificationsCount: Number(state.notificationsCount) + 1,
      };

    case "DECREMENT_NOTES_NOTIFICATIONS_COUNT":
      return {
        ...state,
        notificationsCount:
          state.notificationsCount > 0 ? Number(state.notificationsCount - 1) : 0,
      };

    case "SET_NOTE_OPEN_ELEMENT_MODAL":
      return {
        ...state,
        noteOpenElementModal: payload,
      };

    case "SET_NOTES_NOTIFICATIONS_LIST":
      return {
        ...state,
        notificationsList: payload,
      };

    case "ADD_TO_NOTES_NOTIFICATIONS_LIST":
      if (state.notificationsList == null) {
        return {
          ...state,
          notificationsList: [payload],
        };
      } else {
        return {
          ...state,
          notificationsList: [payload, ...state.notificationsList],
        };
      }

    case "REMOVE_FROM_NOTES_NOTIFICATIONS_LIST":
      return {
        ...state,
        notificationsList: state.notificationsList.filter(
          (note) => note._id != payload
        ),
      };


    default:
      return state;
  }
};

export default selfNotesReducer;
