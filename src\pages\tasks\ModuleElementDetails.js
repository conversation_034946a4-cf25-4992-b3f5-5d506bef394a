/**
 * @name ModuleElementDetails
 *
 * @description `ModuleElementDetails` component is responsible for displaying the viwesphere of associated element.
 *
 * @param {Boolean} openElementDetails Enables the opening of the modal.
 * @param {Function} setOpenElementDetails Sets the 'openElementDetails' state.
 * @param {Function} setElementDetails Sets the 'elementDetails' state.
 * @param {Object} elementDetails Holds the id of element and its corresponding family.
 *
 * @returns {JSX.Element} modal shows the viewsphere of the associated element.
 */

import { Modal } from "antd";
import { useDispatch } from "react-redux";

import ViewSphere2 from "pages/components/DetailsProfile/ViewSphere2";

const ModuleElementDetails = ({
  openElementDetails,
  setOpenElementDetails,
  setElementDetails,
  elementDetails,
}) => {
  const dispatch = useDispatch();

  return (
    <Modal
      open={openElementDetails}
      centered
      onCancel={() => {
        setOpenElementDetails(false);
        setElementDetails({
          id: null,
          module: null,
        });
        dispatch({ type: "RESET_CONTACT_HEADER_INFO" });
      }}
      width={"calc(100vw - 80px)"}
      style={{ height: "auto", marginLeft: "64px" }}
      footer={null}
    >
      <ViewSphere2
        elementId={elementDetails?.id}
        module={elementDetails?.module} //not required
        from="viewSphere"
        source="task"
      />
    </Modal>
  );
};

export default ModuleElementDetails;
