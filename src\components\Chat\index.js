import { lazy } from "react";
import { lazyRetry } from "../../utils/lazyRetry";

const LazyAudio = lazy(() => lazyRetry(() => import("./audio/Audio"), "Audio"));

const LazyRenderSimpleMessage = lazy(() =>
  lazyRetry(
    () => import("./MessageRender/SimpleMessage/RenderSimpleMessage"),
    "RenderSimpleMessage"
  )
);
const LazyRenderFile = lazy(() =>
  lazyRetry(() => import("./MessageRender/RenderFile"), "RenderFile")
);
const LazyLinkPreview = lazy(() =>
  lazyRetry(() => import("./MessageRender/Media/Urls"), "LinkPreview")
);
const LazyFile = lazy(() =>
  lazyRetry(() => import("./MessageRender/Media/File"), "File")
);
const LazyImageRender = lazy(() =>
  lazyRetry(() => import("./MessageRender/Media/Image"), "ImageRender")
);
const LazyImageContainer = lazy(() =>
  lazyRetry(
    () => import("./MessageRender/Media/ImageContainer"),
    "ImageContainer"
  )
);

const LazyModalConfirm = lazy(() =>
  lazyRetry(() => import("./Modal/ModalConfirm"), "ModalConfirm")
);

const LazyCreatePollsComponent = lazy(() =>
  lazyRetry(
    () => import("./Polls/CreatePollsComponent"),
    "CreatePollsComponent"
  )
);
const LazyDropDownGroupIndex = lazy(() =>
  lazyRetry(() => import("./dropdown"), "DropDownGroupIndex")
);

const LazyHeaderMessage = lazy(() =>
  lazyRetry(
    () => import("./MessageRender/Other/HeaderMessage.jsx"),
    "HeaderMessage"
  )
);
const LazyForwardingMessage = lazy(() =>
  lazyRetry(
    () => import("./MessageRender/Other/ForwardingMessage.jsx"),
    "ForwardingMessage"
  )
);

const LazyModalAction = lazy(() =>
  lazyRetry(() => import("./Modal/ModalAction.jsx"), "ModalAction")
);
const LazyPollsMessage = lazy(() =>
  lazyRetry(
    () => import("./MessageRender/Other/PollsMessage.jsx"),
    "PollsMessage"
  )
);
const LazyReplyMessage = lazy(() =>
  lazyRetry(
    () => import("./MessageRender/Other/ReplyMessage.jsx"),
    "ReplyMessage"
  )
);

const LazySystemMessage = lazy(() =>
  lazyRetry(
    () => import("./MessageRender/Other/SystemMessage.jsx"),
    "SystemMessage"
  )
);

export { LazyHeaderMessage as HeaderMessage };
export { LazyForwardingMessage as ForwardingMessage };
export { LazyModalAction as ModalAction };
export { LazyPollsMessage as PollsMessage };
export { LazyReplyMessage as ReplyMessage };
export { LazySystemMessage as SystemMessage };

export { LazyAudio as Audio };
export { default as Loader } from "./Loader/Loader";
export { LazyRenderSimpleMessage as RenderSimpleMessage };
export { LazyRenderFile as RenderFile };
export { LazyFile as File };
export { LazyLinkPreview as LinkPreview };

export { LazyImageRender as ImageRender };
export { LazyImageContainer as ImageContainer };
export { default as ClassNames } from "./MessageRender/ClassNames";
export { LazyModalConfirm as ModalConfirm };
export { default as AvatarChat } from "./Avatar/AvatarChat";
export { LazyCreatePollsComponent as CreatePollsComponent };
export { LazyDropDownGroupIndex as DropDownGroupIndex };
