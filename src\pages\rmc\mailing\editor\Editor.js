import React, { useMemo } from "react";
import ReactQuill, { Quill } from "react-quill";
import EditorToolbar, { formats } from "./EditorToolbar";
import "react-quill/dist/quill.snow.css";
import "./editorCSS.css";
import BlotFormatter from "quill-blot-formatter";
import quillEmoji from "react-quill-emoji";
import "react-quill-emoji/dist/quill-emoji.css";

Quill.register("modules/blotFormatter", BlotFormatter);

Quill.register(
  {
    "formats/emoji": quillEmoji.EmojiBlot,
    "modules/emoji-toolbar": quillEmoji.ToolbarEmoji,
    "modules/emoji-textarea": quillEmoji.TextAreaEmoji,
    "modules/emoji-shortname": quillEmoji.ShortNameEmoji,
  },
  true
);
export const Editor = ({
  showFields,
  setShowFields,
  valueEditor,
  setValueEditor,
  quillRef,
}) => {
  const modules = useMemo(
    () => ({
      blotFormatter: {},
      toolbar: {
        container: [
          [{ header: [1, 2, 3, 4, 5, 6, false] }],
          ["bold", "italic", "blockquote"],
          [{ list: "ordered" }, { list: "bullet" }],
          [{ color: [] }],

          [{ align: [] }],
          ["image"],
          ["emoji"],
          ["code-block"],
        ],
      },
      imageResize: {
        parchment: Quill.import("parchment"),
        modules: ["Resize", "DisplaySize"],
      },
      "emoji-toolbar": true,
      "emoji-textarea": false,
      "emoji-shortname": true,
    }),
    []
  );

  return (
    <div className="text-editor">
      {/* <EditorToolbar setShowFields={setShowFields} showFields={showFields} /> */}
      <ReactQuill
        ref={quillRef}
        theme="snow"
        value={valueEditor}
        onChange={setValueEditor}
        placeholder="Enter your text here..."
        modules={modules}
        formats={formats}
      />
    </div>
  );
};

export default Editor;
