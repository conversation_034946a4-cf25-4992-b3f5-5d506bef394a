import { generateEventMercure } from "pages/voip/services/services";

export const handleConfEvents = async (callInfo, conf) => {
  try {
    const formData = new FormData();
    const participants = [];
    switch (conf) {
      case "start":
        formData.append("eventType", "start_conference");
        participants.push(callInfo?.receiverInfo);
        participants.push(callInfo?.conf?.receiverInfo);
        break;

      case "end":
        formData.append("eventType", "end_conference");
        participants.push(callInfo?.receiverInfo);
        break;

      default:
        break;
    }

    if (!participants.length) return;

    participants.forEach((participant) =>
      formData.append("users[]", participant?.id)
    );
    formData.append("eventPayload[]", JSON.stringify(participants));

    await generateEventMercure(formData);
  } catch (err) {
    throw new Error(err);
  }
};
