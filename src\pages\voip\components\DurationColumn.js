import { useRef, useState } from "react";
import { Badge, Button, Tooltip } from "antd";
import { billSecToHumanReadable } from "../helpers/helpersFunc";
import { PlayCircleOutlined } from "@ant-design/icons";
import { FaVoicemail } from "react-icons/fa";
import "../index.css";

const DurationColumn = ({
  audioRecording,
  state,
  actionSeen,
  id,
  idCall,
  duration,
  source,
  t,
}) => {
  const audioRef = useRef(null);
  const [isLoaded, setIsLoaded] = useState(false);

  const handlePlayClick = () => {
    if (!isLoaded) {
      audioRef.current.src = audioRecording;

      setIsLoaded(true);
      audioRef.current.play();
    }
    if (state === "Nouveau" && actionSeen) {
      actionSeen(id, idCall || null);
    }
  };

  // handle case when voice is 0sec, so make it seen automatically
  if (!duration && state === "Nouveau" && actionSeen)
    actionSeen(id, idCall || null);

  return duration ? (
    <div
      className="DurationColumn relative w-full"
      key={`${id}-${duration}-${audioRecording}`}
    >
      <audio
        ref={audioRef}
        className="audio-controls "
        style={{
          width: "100%",
        }}
        controls={isLoaded}
      ></audio>
      {!isLoaded ? (
        <div
          className="flex flex-row items-center space-x-0.5"
          key={`${id}-${duration}-${audioRecording}`}
        >
          {audioRecording ? (
            <Button
              // size="large"
              type="link"
              shape="circle"
              icon={<PlayCircleOutlined style={{ fontSize: 16 }} />}
              onClick={handlePlayClick}
            />
          ) : null}
          <div className="flex items-center space-x-2">
            {state === "Nouveau" ? <Badge status="processing" /> : null}
            <p>
              {source === "messaging"
                ? duration
                : billSecToHumanReadable(duration)}
            </p>
            {source === "callLog" ? (
              <Tooltip title={t("voip.afterCallForwardingToVoicemail")}>
                <FaVoicemail
                  style={{
                    fontSize: 20,
                    marginTop: -1,
                    color: "#1677FF",
                    cursor: "help",
                  }}
                />
              </Tooltip>
            ) : null}
          </div>
        </div>
      ) : null}
    </div>
  ) : (
    <p className="font-semibold">{`${t("voip.emptyContent")} (0 sec)`}</p>
  );
};

export default DurationColumn;
