import { Card, Select } from "antd";
import { setSelectedMail } from "new-redux/actions/dashboard.actions";
import { backgroundImagecard, GoTo, stylesCard } from "pages/home/<USER>";
import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { OneBarChart } from "../ChartsDashboard";

const CardStatEmail = () => {
  const { user } = useSelector((state) => state.user);
  const [t] = useTranslation("common");
  const navigate = useNavigate();
  const { totalEmails, selectedMail, loading } = useSelector(
    (state) => state.dashboardRealTime
  );

  const dispatch = useDispatch();
  const chartDataEmail = useMemo(
    () => ({
      data: [
        {
          name: t(`dashboard.receivedCalls`, {
            plural: selectedMail?.nb_email_received > 1 ? "s" : "",
            pluriel: selectedMail?.nb_email_received > 1 ? "x" : "",
          }),
          y: selectedMail?.nb_email_received,
          color: "oklch(0.792 0.209 151.711)",
        },
        {
          name: t(`dashboard.unreadEmailReceived`, {
            plural: selectedMail?.nb_email_received_unread > 1 ? "s" : "",
            pluriel: selectedMail?.nb_email_received_unread > 1 ? "x" : "",
          }),
          y: selectedMail?.nb_email_received_unread,
          color: "oklch(0.704 0.174 22.216)",
        },
        {
          name: t(`dashboard.totalEmailSent`, {
            plural: selectedMail?.nb_email_sent > 1 ? "s" : "",
            pluriel: selectedMail?.nb_email_sent > 1 ? "x" : "",
          }),
          y: selectedMail?.nb_email_sent,
          color: "oklch(0.707 0.165 254.624)",
        },
      ],
      name: "",
    }),
    [selectedMail, t]
  );
  return (
    <Card
      style={{ backgroundImage: backgroundImagecard }}
      title={
        <div className="flex items-center justify-between">
          <span>
            Email &nbsp;
            <span className="truncate">
              <Select
                loading={loading && totalEmails.length === 0}
                showSearch
                defaultValue={totalEmails[0]?.accountId}
                placeholder=""
                optionFilterProp="children"
                popupMatchSelectWidth={false}
                style={{ minWidth: 200 }}
                options={
                  Array.isArray(totalEmails) &&
                  totalEmails.length > 0 &&
                  totalEmails?.map((el) => ({
                    ...el,
                    value: el?.accountId,
                    label: el?.email,
                  }))
                }
                filterOption={(input, option) =>
                  (option?.label ?? "")
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
                value={selectedMail?.accountId}
                onChange={(value, values) => {
                  dispatch(setSelectedMail(values));
                  // setUnreadReceivedEmailByAccount(
                  //   values?.nb_email_received_unread
                  // );
                  // setReceivedEmailByAccount(
                  //   values?.nb_email_received
                  // );
                  // setTotalSentEmailByAccount(values?.nb_email_sent);
                }}
              />
            </span>
          </span>
          <GoTo
            to={"4"}
            title={"Email"}
            navigate={navigate}
            t={t}
            user={user}
          />
        </div>
      }
      styles={{
        ...stylesCard,
      }}
    >
      <OneBarChart data={chartDataEmail} height={290} />
    </Card>
  );
};

export default CardStatEmail;
