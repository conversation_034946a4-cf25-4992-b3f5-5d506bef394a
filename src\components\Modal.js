import { Fragment, useState } from "react";
import { Dialog, Transition } from "@headlessui/react";
import { Check<PERSON><PERSON>, XMarkIcon } from "@heroicons/react/24/outline";
import { CloseOutlined } from "@ant-design/icons";
import SelectFieldType from "./SelectFieldType";

const Modal = ({
  setAddNewFieldModal,
  setFieldType,
  setLabel,
  setFieldId,
  submitNewField,
  errors,
}) => {
  const [open, setOpen] = useState(true);

  const prefix = "cf_";

  return (
    <Transition.Root show={open} as={Fragment}>
      <Dialog
        as="div"
        className="relative z-10"
        onClose={() => {
          setOpen();
          setAddNewFieldModal(false);
        }}
      >
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel
                className="sm:max-w-lg relative z-50 inline-block w-full overflow-hidden rounded-lg bg-white text-left align-bottom shadow-xl dark:bg-neutral-900 sm:my-8 sm:align-middle"
                // className="relative transform overflow-hidden rounded-lg bg-white px-4 pt-5 pb-4 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-sm sm:p-6"
              >
                <form className="space-y-6" action="#" method="POST">
                  <div className="absolute top-1 right-2 pt-4 pr-4">
                    <button
                      className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                      onClick={() => setAddNewFieldModal(false)}
                    >
                      <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                    </button>
                  </div>
                  <div className="bg-white px-4 pt-5 pb-4 dark:bg-neutral-900 sm:p-6 sm:pb-4">
                    <div>
                      <h2 className="text-lg font-medium leading-6 text-neutral-700 dark:text-white">
                        Create New Custom Field
                      </h2>
                    </div>
                    <div className="mt-5">
                      <div className="mb-3">
                        <label
                          htmlFor="email"
                          className="block text-sm font-medium text-gray-700"
                        >
                          <span className="mr-1 text-sm text-red-600">*</span>{" "}
                          Field Type
                        </label>
                        <div className="mt-1">
                          {/* <input
                            id="email"
                            name="email"
                            type="email"
                            autoComplete="email"
                            required
                            className="block w-full appearance-none rounded-md border border-gray-300 px-3 py-2 placeholder-gray-400 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                          /> */}
                          <SelectFieldType />
                        </div>
                      </div>

                      <div className="mb-3">
                        <label
                          // htmlFor="password"
                          className="block text-sm font-medium text-gray-700"
                        >
                          <span className="mr-1 text-sm text-red-600">*</span>
                          Label
                        </label>
                        <div className="mt-1">
                          <input
                            id="label"
                            type="text"
                            onChange={(e) => setLabel(e.target.value)}
                            className="block w-full appearance-none rounded-md border border-gray-300 px-3 py-2 placeholder-gray-400 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                          />
                          {errors?.label && (
                            <p className="text-red-500">{errors?.label}</p>
                          )}
                        </div>
                      </div>

                      <div className="mb-3">
                        <label
                          // htmlFor="password"
                          className="block text-sm font-medium text-gray-700"
                        >
                          <span className="mr-1 text-sm text-red-600">*</span>
                          Field ID
                        </label>
                        <div className="relative ">
                          {/* <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3 text-sm dark:text-white">
                            cf_
                          </div> */}
                          <input
                            id="field_id"
                            type="text"
                            ref={(target) => {
                              if (target) {
                                target.value = prefix;
                              }
                            }}
                            className="block w-full appearance-none rounded-md border border-gray-300 px-3 py-2 placeholder-gray-400 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                            onChange={(e) => {
                              const input = e.target.value;
                              e.target.value =
                                prefix + input.substring(prefix.length);
                              setFieldId(e.target.value);
                            }}
                          />
                          {errors?.fieldId && (
                            <p className="text-red-500">{errors?.fieldId}</p>
                          )}
                        </div>
                        <p className="mt-2 text-sm text-gray-500">
                          Enter field ID in lowercase, only alpha characters
                          (a-z) and underscore (_) accepted.
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-neutral-50 px-4 py-3 sm:px-6">
                    <div className="flex justify-end space-x-3">
                      <button
                        onClick={() => setAddNewFieldModal(false)}
                        type="button"
                        className="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                        // className="flex w-full justify-center rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                      >
                        Cancel
                      </button>
                      <button
                        onClick={submitNewField}
                        type="submit"
                        className="inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                        // className="flex w-full justify-center rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                      >
                        Save
                      </button>
                    </div>
                  </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
};

export default Modal;
