import { Badge, Divider, List, Skeleton, Space, Spin } from "antd";
import React, { memo, useEffect, useState } from "react";
import ItemVisio from "./ItemVisio";
import { CalendarClock, CalendarRange } from "lucide-react";
import Typography from "antd/es/typography/Typography";
import VirtualList from "rc-virtual-list";
import { generateAxios } from "../../services/axiosInstance";
import { toastNotification } from "../../components/ToastNotification";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import {
  setDetailsMeet,
  setKeyMeet,
  setLater,
  setListMeet,
  setNow,
  setPage,
} from "../../new-redux/actions/visio.actions/visio";
import { humanDate } from "../voip/helpers/helpersFunc";
import DropdownVisio from "./DropdownVisio";
import moment from "moment";
import { URL_ENV } from "index";

const ListMeet = memo(
  ({
    t,
    setIdTask,
    listMeet,
    lastPage,
    keyMeet,
    page,
    loadTabs,
    tabKey,
    setExterneUpdate,
    setOpenDrawerMsg,
    notificationList,
  }) => {
    const ContainerHeight = 400;
    const [loading, setLoading] = useState(false);
    const { now, later, countToday, countUpComing } = useSelector(
      (state) => state.visioList
    );
    const dispatch = useDispatch();

    const [windowHeight, setWindowHeight] = useState(window.innerHeight);

    useEffect(() => {
      const handleResize = () => {
        setWindowHeight(window.innerHeight);
      };

      // Ajoute un écouteur d'événement pour le redimensionnement de la fenêtre
      window.addEventListener("resize", handleResize);

      // Nettoyage de l'écouteur d'événement lors de la suppression du composant
      return () => {
        window.removeEventListener("resize", handleResize);
      };
    }, []);
    const onScroll = async (e) => {
      if (
        e.currentTarget.clientHeight + e.currentTarget.scrollTop ===
          e.currentTarget.scrollHeight &&
        page + 1 <= lastPage
      ) {
        dispatch(setPage(page + 1));

        setLoading(true);
        try {
          const res = await generateAxios(
            URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
          ).get(`/get-tasks-visio?type=${tabKey}&page=${page + 1}`);
          dispatch(setListMeet([...listMeet, ...res.data.data]));

          setLoading(false);
        } catch (err) {
          dispatch(setListMeet({}));

          setLoading(false);

          toastNotification("error", t("toasts.somethingWrong"), "topRight");
        }
      }
    };
    const handleClick = (data) => {
      dispatch(setKeyMeet(data.id));
      // dispatch(getDetailsMeet({ keyMeet: id, t }));
      dispatch(setDetailsMeet(data));
      if (data.newMeet) {
        const currentDate = moment().format("YYYY-MM-DD");
        const startDate = moment(data.start_date, "YYYY-MM-DD");
        dispatch(
          setListMeet(
            listMeet.map((el) =>
              el.id === data.id ? { ...el, newMeet: false } : el
            )
          )
        );
        if (data?.start_date === currentDate && now > 0) {
          dispatch(setNow({ now: now - 1, countToday: countToday }));
        }

        if (startDate.isAfter(currentDate, "day") && later > 0) {
          dispatch(
            setLater({ later: later - 1, countUpComing: countUpComing })
          );
        }
      }
    };
    // useEffect(() => {
    //   const data = listMeet.map((objet) => {
    //     const objetDeuxiemeTableau = notificationList.find(
    //       (obj) => obj.id_data === objet.id
    //     );

    //     return objetDeuxiemeTableau
    //       ? {
    //           ...objet,
    //           read: objetDeuxiemeTableau.read,
    //           idLog: objetDeuxiemeTableau.id,
    //         }
    //       : objet;
    //   });
    //   dispatch(setListMeet(data));
    // }, [notificationList]);
    return (
      <>
        {/* <Typography.Paragraph>
        {t("visio.myMeetings")}{" "}
        {loadTabs ? (
          ""
        ) : (
          <Badge
            count={
              tabKey === 0
                ? countHistory
                : tabKey === 1
                ? countToday
                : countUpComing
            }
            color="blue"
            size="small"
            className={`${
              tabKey === 0
                ? countHistory > 0
                  ? ""
                  : "hidden"
                : tabKey === 1
                ? countToday > 0
                  ? ""
                  : "hidden"
                : countUpComing > 0
                ? ""
                : "hidden"
            }`}
          />
        )}
      </Typography.Paragraph> */}
        {loadTabs ? (
          <Skeleton
            active
            shape="square"
            avatar
            paragraph={{
              rows: 1,
            }}
            style={{
              width: "100%",
              marginTop: "10px",
            }}
          />
        ) : (
          <div className="-mr-[16px]">
            {/* <Divider orientation="left" plain>
        Mes réunions
      </Divider> */}

            <List>
              <VirtualList
                data={listMeet}
                height={windowHeight - 284}
                itemKey="email"
                onScroll={onScroll}>
                {(el) => (
                  <List.Item key={el.id} className="mr-2">
                    <div
                      className={` flex   w-full  items-center    ${
                        keyMeet === el.id ? "bg-slate-100" : ""
                      } py-2 pl-2 pr-1  hover:bg-slate-50`}
                      // onMouseEnter={() => setShowCopy(true)}
                      // onMouseLeave={() => setShowCopy(false)}
                    >
                      <ItemVisio
                        text={el.label}
                        icon={
                          <CalendarRange className="lg:h-[18px] lg:w-[18px] xl:h-[22px] xl:w-[22px]" />
                        }
                        setIdTask={setIdTask}
                        title={el?.label}
                        newMeet={el?.newMeet}
                        start_date={el?.start_date}
                        owner_id={el?.owner_id?.id}
                        setExterneUpdate={setExterneUpdate}
                        time={
                          <Space direction="vertical">
                            <div className=" flex items-center">
                              <CalendarClock size={18} /> :{" "}
                              {humanDate(
                                el.start_date + " " + el.start_time,
                                t
                              )}
                            </div>

                            {/* <div>
                          {t("visio.endDate")} :{" "}
                          {humanDate(el.end_date + " " + el.end_time, t)}
                        </div> */}
                          </Space>
                        }
                        keys={keyMeet}
                        id={el?.id}
                        room={el?.location}
                        guests={el?.guests}
                        followers={el?.followers}
                        creator={el?.creator}
                        setOpenDrawerMsg={setOpenDrawerMsg}
                        dataExternal={el}
                      />
                      <DropdownVisio
                        dataExternal={el}
                        setExterneUpdate={setExterneUpdate}
                        setIdTask={setIdTask}
                        setOpenDrawerMsg={setOpenDrawerMsg}
                      />
                    </div>
                  </List.Item>
                )}
              </VirtualList>
              {loading ? <Spin /> : ""}
            </List>

            {/* </Flex> */}
          </div>
        )}
      </>
    );
  }
);

export default ListMeet;
