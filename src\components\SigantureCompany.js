import React, { useEffect, useMemo, useRef, useState } from "react";
import TabsCompanies from "./TabsCompanies";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { Button, Col, Divider, Form, Input, Row, Spin } from "antd";
import { RollbackOutlined } from "@ant-design/icons";
import { showNameOrg } from "new-redux/actions/configCompanies.actions/configCompaniesAction";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import ReactQuill, { Quill } from "react-quill";
import "react-quill/dist/quill.snow.css";
import quillEmoji from "react-quill-emoji";
import "react-quill-emoji/dist/quill-emoji.css";
import MainService from "services/main.service";
import { URL_ENV } from "index";
import { FormFooter } from "pages/components/FormFooter";
import { generateAxios } from "services/axiosInstance";
import { toastNotification } from "./ToastNotification";
import BlotFormatter from "quill-blot-formatter";
import NotFoundPage from "pages/404";
const SigantureCompany = () => {
  const params = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [t] = useTranslation("common");
  const [form] = Form.useForm();
  const [screenfull, setScreenFull] = useState(false);
  const [company, setCompany] = useState("");

  const [formChanged, setIsFormChanged] = useState(false);
  const [loading, setLoading] = useState(false);
  const [load, setLoad] = useState(false);
  const [show404, setShow404] = useState(false);

  const [formsubmittedValues, setFormSubmittedValues] = useState(null);

  const quillRef = useRef(null);
  const senderNameRef = useRef(null);
  const systemEmailRef = useRef(null);
  Quill.register("modules/blotFormatter", BlotFormatter);

  Quill.register(
    {
      "formats/emoji": quillEmoji.EmojiBlot,
      "modules/emoji-toolbar": quillEmoji.ToolbarEmoji,
      "modules/emoji-textarea": quillEmoji.TextAreaEmoji,
      "modules/emoji-shortname": quillEmoji.ShortNameEmoji,
    },
    true
  );
  useEffect(() => {
    const observer = new MutationObserver(() => {
      const tooltips = document.querySelectorAll(".ql-tooltip");
      tooltips.forEach((tooltip) => {
        const computedStyle = window.getComputedStyle(tooltip);
        const left = parseFloat(computedStyle.left);
        if (left < 0) {
          tooltip.style.left = "0px";
        }
      });
    });

    // Cibler les changements dans le DOM
    observer.observe(document.body, {
      attributes: true,
      childList: true,
      subtree: true,
    });

    return () => observer.disconnect(); // Nettoyage de l'observateur
  }, []);
  useEffect(() => {
    const getSingature = async () => {
      setLoad(true);
      try {
        const res = await MainService.getCompanyInSettings(params.id);
        if (res.data.data && res.data.data.length > 0) {
          setCompany(res.data.data[0]);
          form.setFieldsValue({
            company: {
              ...res.data.data[0],
              signature_system_email:
                res.data.data[0]?.signature_system_email || "<p><br></p>",
            },
          });
          setIsFormChanged(false);
        }
        setLoad(false);
        setIsFormChanged(false);
      } catch (err) {
        if (err.response.status === 404) {
          setShow404(true);
        }
        setLoad(false);
      }
    };

    getSingature();
  }, [params.id]);
  const handleChange = (html, delta, source, editor) => {
    console.log(html, "---", delta, "---", source, "---", editor);
    if (html) form.setFieldsValue({ signature_system_email: html });
  };
  async function resizeImage(file, maxWidth) {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = function (event) {
        const img = new Image();
        img.src = event.target.result;
        img.onload = function () {
          const canvas = document.createElement("canvas");
          let width = img.width;
          let height = img.height;

          if (width > maxWidth) {
            height = Math.round((height * maxWidth) / width);
            width = maxWidth;
          }

          canvas.width = width;
          canvas.height = height;

          const ctx = canvas.getContext("2d");
          ctx.drawImage(img, 0, 0, width, height);

          canvas.toBlob(function (blob) {
            const resizedFile = new File([blob], file.name, {
              type: file.type,
            });
            resolve(resizedFile);
          }, file.type);
        };
      };
    });
  }
  const imageHandler = () => {
    if (quillRef?.current != null) {
      const editor = quillRef.current.getEditor();

      const input = document.createElement("input");
      input.setAttribute("type", "file");
      input.setAttribute("accept", "image/*");
      input.click();
      input.onchange = async function () {
        const file = input.files[0];
        const resizedImage = await resizeImage(file, 300);
        const formData = new FormData();
        formData.append("upload", resizedImage);
        formData.append("file_name", file?.name);

        MainService.uploadFile360(formData).then((res) => {
          const range = editor.getSelection();
          const link = `${URL_ENV?.REACT_APP_BASE_URL}${res.data.message.path}`;

          editor.insertEmbed(range.index, "image", link);
        });
      }.bind(this);
    }
  };
  const modules = useMemo(
    () => ({
      blotFormatter: {},
      toolbar: {
        container: [
          [{ header: [1, 2, 3, 4, 5, 6, false] }],
          [{ font: [] }],
          [{ size: [] }],
          [{ color: [] }, { background: [] }],
          ["bold", "italic", "strike", "blockquote", "underline"],
          [
            { list: "ordered" },
            { list: "bullet" },
            { indent: "-1" },
            { indent: "+1" },
          ],
          ["link", "image", "video"],
          // ['emoji'],
          [{ align: [] }],
          ["clean"],
          ["code-block"],
          ["omega"],
        ],
        handlers: {
          image: imageHandler,
          omega: () => {
            screenfull ? setScreenFull(false) : setScreenFull(true);
          },
        },
      },
    }),
    [screenfull]
  );
  const handleFormReset = () => {
    if (formsubmittedValues) {
      form.setFieldsValue({
        company: {
          ...formsubmittedValues,
          phone: formsubmittedValues.phone?.split("-")[1],
        },
      });
    } else {
      form.resetFields();
    }
  };
  const onFinish = async (values) => {
    let newValues = values.company;
    let formData = new FormData(); //formdata object
    let allValues = {
      ...company,
      ...newValues,
      signature_system_email: `<html><body>${newValues.signature_system_email}</body></html>`,
      icon: "",
      logo: "",
    };
    formData.append("companie_id", company.id);

    Object.keys(allValues)
      .filter((el) => el !== "Banks" && el !== "id")
      .map((el) => formData.append(el, allValues[el]));

    try {
      setLoading(true);
      const {
        data: { data },
      } = await MainService.submitGeneralInfoCompany(formData);
      setIsFormChanged(false);
      setLoading(false);
      setFormSubmittedValues(data);
      if (params.id === "new") {
        toastNotification(
          "success",
          data.label + t("toasts.created"),
          "topRight"
        );
      } else {
        toastNotification("success", data.label + t("toasts.edit"), "topRight");
      }
    } catch (err) {
      setLoading(false);

      toastNotification("error", t("toasts.somethingWrong"), "topRight");

      console.log(err);
    }
  };
  if (show404) {
    return (
      <>
        <Button
          icon={<RollbackOutlined />}
          onClick={() => {
            navigate("/settings/general/companies");
            dispatch(showNameOrg(""));
          }}
          style={{ margin: "16px 16px 0 16px" }}
        >
          {/* {t("companies.accessTableCompanies")} */}
        </Button>
        <div className="height-screen bg-white">
          <NotFoundPage />
        </div>
      </>
    );
  }

  return (
    <div className="">
      <Button
        icon={<RollbackOutlined />}
        onClick={() => {
          navigate("/settings/general/companies");
          dispatch(showNameOrg(""));
        }}
        style={{ margin: "16px 16px 0 16px" }}
      >
        {/* {t("companies.accessTableCompanies")} */}
      </Button>
      <TabsCompanies keyTab={"4"} setKeyTab={() => {}} companie_id={2} />
      {load ? (
        <div className="flex h-[calc(100vh-57px)] items-center justify-center">
          <Spin />
        </div>
      ) : (
        <div className="p-4">
          <Spin spinning={loading}>
            <Form
              onFinish={onFinish}
              layout={"vertical"}
              form={form}
              onValuesChange={(values) => {
                setIsFormChanged(true);
              }}
            >
              <Row gutter={16}>
                <Divider orientation="left">Sms </Divider>

                <Col sm={24} md={24} lg={8}>
                  <Form.Item
                    name={["company", "sender_sms"]}
                    label={t("emailTemplates.smsHeader")}
                    rules={[
                      {
                        required: true,
                        message: `${t("emailTemplates.smsHeader")} ${t(
                          "table.header.isrequired"
                        )}`,
                      },
                    ]}
                  >
                    <Input showCount maxLength={11} />
                  </Form.Item>
                </Col>
                <Divider orientation="left">Email </Divider>

                <Col sm={24} md={12} lg={12}>
                  <Form.Item
                    name={["company", "system_email"]}
                    label={t("companies.systemEmail")}
                    rules={[
                      {
                        type: "email",
                      },
                      {
                        required: true,
                        message: `${t("companies.systemEmail")} ${t(
                          "table.header.isrequired"
                        )}`,
                      },
                    ]}
                  >
                    <Input ref={systemEmailRef} />
                  </Form.Item>
                </Col>
                <Col sm={24} md={12} lg={12}>
                  <Form.Item
                    name={["company", "sender_name"]}
                    label={t("companies.senderName")}
                    rules={[
                      {
                        required: true,
                        message: `${t("companies.senderName")} ${t(
                          "table.header.isrequired"
                        )}`,
                      },
                    ]}
                  >
                    <Input ref={senderNameRef} />
                  </Form.Item>
                </Col>
                <Col sm={24} md={24} lg={24}>
                  <Form.Item
                    label="Signature"
                    name={["company", "signature_system_email"]}
                    initialValue={
                      company?.signature_system_email || "<p><br></p>"
                    }
                  >
                    <ReactQuill
                      theme="snow"
                      ref={quillRef}
                      // value={contentFr}
                      modules={modules}
                      style={{
                        position: screenfull ? "fixed" : "relative",
                        height: "auto",
                        zIndex: screenfull ? 1000 : 0,
                        top: 0,
                        left: screenfull ? 80 : 0,
                        bottom: 0,
                        right: 0,
                        background: screenfull ? "white" : "transparent",
                      }}
                      onChange={handleChange}
                    />
                  </Form.Item>
                </Col>
              </Row>
              <div className="py-5">
                <FormFooter
                  onClickCancel={() => {
                    setIsFormChanged(false);
                    handleFormReset();
                  }}
                  loadButton={loading}
                  submitDisabled={!formChanged}
                />
              </div>
            </Form>
          </Spin>
        </div>
      )}
    </div>
  );
};

export default SigantureCompany;
