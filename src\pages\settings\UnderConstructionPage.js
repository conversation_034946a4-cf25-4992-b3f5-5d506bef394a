import React from "react";
import { useTranslation } from "react-i18next";
import { Tabs } from "antd";

/**
 *
 * @returns Underconstruction page.
 */

const UnderConstructionPage = () => {
  const [t] = useTranslation("common");
  const onChange = (key) => {
    console.log(key);
  };

  const items = [
    {
      key: "1",
      label: `General`,
      children: `Content of Tab Pane 1`,
    },
    {
      key: "4",
      label: `My profil`,
      children: `Content of Tab Pane 4`,
    },
    {
      key: "2",
      label: `Company information`,
      children: `Content of Tab Pane 2`,
    },
    {
      key: "3",
      label: `Localization`,
      children: `Content of Tab Pane 3`,
    },
  ];

  return (
    <div className="p-6">
      {/* <Tabs defaultActiveKey="1" items={items} onChange={onChange} /> */}
    </div>
  );
};

export default UnderConstructionPage;
