import { URL_ENV } from "index";
import { generateAxios } from "../../../services/axiosInstance";

// const URL_ENV = window.URL_ENV || {};
// const baseURL = URL_ENV?.REACT_APP_BASE_URL+process.env.REACT_APP_SUFFIX_API;

export const getTableHeader = async (family_id, pipeline_id) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(
    `/get-header-by-family/${family_id}${
      !!pipeline_id ? `?pipeline_id=${pipeline_id}` : ""
    }`
  );
//
export const getTableData = async (
  family_id,
  numberOfPage,
  pageLimit,
  search,
  isGuest,
  relation_id,
  upload_id,
  pipeline_id,
  ticketsFolderId,
  filter,
  sortedInfo
) => {
  const encodedSearch = encodeURIComponent(search);
  const filters = filter.length
    ? filter
        .map((f, i) => {
          const adjustedFilter = {
            ...f,
            value:
              f.value === undefined
                ? null
                : f.field === "stages" && f.value
                ? f.value.map((item) => item[1])
                : f.value,
          };
          return `filter[${i}]=${encodeURIComponent(
            JSON.stringify(adjustedFilter)
          )}`;
        })
        .join("&")
    : "";
  //
  const queryString = [
    `page=${numberOfPage}`,
    `limit=${pageLimit}`,
    `search=${encodedSearch}`,
    isGuest ? `guest=${1}` : "",
    relation_id ? `relation_id=${relation_id}` : "",
    upload_id ? `upload_id=${upload_id}` : "",
    pipeline_id ? `pipeline_id=${pipeline_id}` : "",
    ticketsFolderId ? `tickets_folder_id=${ticketsFolderId}` : "",
    sortedInfo?.order ? `sort[${sortedInfo.field}]=${sortedInfo.order}` : "",
    filters,
  ]
    .filter(Boolean)
    .join("&");

  const url = `/get-fields-values_new/${family_id}?${queryString}`;

  return await generateAxios(
    `${URL_ENV?.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
  ).get(url);
};

//
export const getFieldsToCreate = async (
  family_id,
  method,
  profile,
  elementId,
  stageId
) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(
    profile === true
      ? "/get-fields-edit-profile"
      : `/get-fields-by-family/${family_id}/${method}${
          elementId ? `?element_id=${elementId}` : ""
        }${stageId ? `${elementId ? "&" : "?"}stage_id=${stageId}` : ""}`
  );
//
export const storeNewData = async (formData) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).post(`/generate-data-mongo`, formData);
//
export const getDataToUpdate = async (id, profile) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(profile ? `/get-data-profile` : `/get-data-mongo/${id}`);
//
export const getDataToConvert = async (familyId, id, type, msg) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(
    `/convert-to-family?familyId=${familyId}&id=${id}&type=${type}&subject=${
      msg || ""
    }`
  );
//
export const getDataProfileDetails = async () =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(`/get-data-profile-details`);
//
export const fetchDataDetails = async (id) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(`/get-details-data-mongo/${id}`);

//
export const updateElement = async (id, formData, profile) => {
  if (profile) {
    return await generateAxios(
      URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
    ).post(`/edit-profile`, formData);
  } else {
    return await generateAxios(
      URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
    ).post(`/update-data-mongo/update/${id}`, formData);
  }
};
//
export const deleteElements = async (formData) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).post(`/delete-data-mongo`, formData);
//
export const exportData = async (formData) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).post(`/export-data`, formData, {
    responseType: "blob",
  });
//
export const storeConfigColumns = async (config) => {
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).post(`config-family-columns-table`, config);
};
//
export const storeColumnsOrder = async (order, familyId) => {
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).post(`table/header/${familyId}`, order);
};
//
export const autoCompleteFamily = async (family_id, search) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(`autocomplete/${family_id}?search=${search}`);
//
export const autoCompleteModule = async (fieldModule, search) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(`autocomplete?field_module_id=${fieldModule}&search=${search}`);
//
export const sendUserInvite = async (formData) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).post(`/send-invitation-to-users`, formData);
//
export const blockOrUnblockUsers = async (formData) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).post(`block-unblock-multiple-users`, formData);
//
export const getKPI = async (familyId, elementId) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(`kpi-relation-families/${familyId}/${elementId} `);
//
export const getElementSystemDetails = async (elementId) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(`get-data-details/${elementId}`);
//
export const getGeneralInfo360 = async (zone, elementId) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(`get-data-relation/${zone}/${elementId}`);
//
export const getMailsAndPhones = async (familyId, elementId) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(`get-phone-emails/${familyId}/${elementId}`);
//
export const getElementByFamily = async (family_id, element_id, search) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(`get-elements-by-family/${family_id}/${element_id}?search=${search}`);
//
export const postAssociate = async (element_id, formData) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).post(`/associate-element/${element_id}`, formData);
//
export const deleteAssociation = async (element_id, formData) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).post(`delete-association/${element_id}`, formData);
//
export const getKpiFamily = async (family_id) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(`kpi-families/${family_id}`);
//
export const getKpiFamilyDeals = async (pipeline_id) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(`kpi-deals/${pipeline_id}`);
//
export const getKpiFamilyTicket = async (formData) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).post(`get_kpi_family`, formData);
//
export const getFilterFieldsFamily = async (family_id, pipeline_id) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(`get-fields-filter/${family_id}?pipeline_id=${pipeline_id ?? ""}`);
//
export const saveFilterFamily = async (formData) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).post(`filters-families`, formData);
//
export const convertToGuest = async (formData) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).post(`convert-contact-to-guest`, formData);
