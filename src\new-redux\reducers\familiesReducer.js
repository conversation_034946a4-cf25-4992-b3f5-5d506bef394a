import {
  GET_FAMILIES_SUCCESS,
  GET_FAMILIES_ERROR,
  GET_SELECTED_FAMILY,
  RESET_STATE,
} from "../constants";

const initialState = {
  families: [],
  selectedFamily: null,
  errors: {},
  isLoading: false,
};

const families = (state = initialState, action) => {
  const { type, payload } = action;
  switch (type) {
    case GET_FAMILIES_SUCCESS:
      return {
        ...state,
        isLoading: false,
        families: payload,
      };

    case GET_FAMILIES_ERROR:
      return {
        ...state,
        isLoading: false,
        errors: payload,
      };

    case GET_SELECTED_FAMILY:
      return {
        ...state,
        selectedFamily: payload,
      };
    case RESET_STATE: {
      return initialState;
    }
    default:
      return state;
  }
};

export default families;
