import { Button, Modal } from "antd";
import React from "react";

const ModalDeleteEmail = ({
  t,
  openModal,
  setOpenModal,
  loading,
  DeleteMail,
  typeDelete,
  selectedRowKeysLength,
}) => {
  return (
    <Modal
      title={
        typeDelete === "clearTrashMultiple"
          ? t("mailing.ConfirmClearTrashTitle")
          : t("mailing.Delete")
      }
      open={openModal}
      // onOk={handleOk}
      onCancel={() => setOpenModal(false)}
      okButtonProps={{
        style: {
          backgroundColor: "red",
          color: "white",
          width: "70",
          border: "none",
        },
      }}
      cancelButtonProps={{
        disabled: true,
      }}
      footer={
        <div style={{ display: "flex", justifyContent: "end", gap: "5px" }}>
          <Button key="cancel" onClick={() => setOpenModal(false)}>
            Cancel
          </Button>
          <Button
            key="Delete"
            loading={loading.type === "delete" && loading.state}
            style={{
              backgroundColor: "red",
              color: "white",
            }}
            onClick={DeleteMail}
          >
            {t("mailing.DeleteButton")}
          </Button>
        </div>
      }
    >
      <p>
        {typeDelete === "simple"
          ? t("mailing.DeleteMail") + "?"
          : typeDelete === "clearTrashMultiple"
          ? t("mailing.ConfirmClearTrashMessage")
          : typeDelete === "clearTrashById" && selectedRowKeysLength === 1
          ? t("mailing.DeleteMail") + ` ${t("mailing.forever")}` + "?"
          : `${t("mailing.DeleteMails")} ${selectedRowKeysLength} emails${
              typeDelete === "clearTrashMultiple" ||
              typeDelete === "clearTrashById"
                ? ` ${t("mailing.forever")}` + "?"
                : ""
            }`}
      </p>
    </Modal>
  );
};

export default ModalDeleteEmail;
