import MainService from "services/main.service";
import i18n from "translations/i18n";

export async function getStatFamilyByField(
  start,
  end,
  familyId,
  fieldId,
  selectedPipeline
) {
  try {
    const res = await MainService.getStatFamilyByField({
      date_start: start,
      date_end: end,
      lang: i18n.language,
      family_id: familyId,
      pipeline_id: selectedPipeline?.value,
      field_system_id: fieldId,
    });
    return res;
  } catch (error) {
    console.error("Error fetching stats:", error);
    throw error; // Vous pouvez également gérer l'erreur différemment ici
  }
}
export async function getStatByFamily(start, end, familyId, selectedPipeline) {
  try {
    const res = await MainService.getStatFamily({
      date_start: start,
      date_end: end,
      lang: i18n.language,
      family_id: familyId,
      pipeline_id: selectedPipeline?.value,
    });
    return res;
  } catch (error) {
    console.error("Error fetching stats:", error);
    throw error; // Vous pouvez également gérer l'erreur différemment ici
  }
}
