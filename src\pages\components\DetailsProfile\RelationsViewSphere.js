import { LinkOutlined, PlusOutlined } from "@ant-design/icons";
import { Button, Form, Tooltip } from "antd";
import { toastNotification } from "components/ToastNotification";
import { setNewInteraction } from "new-redux/actions/vue360.actions/vue360";
import { SET_CONTACT_INFO_FROM_DRAWER } from "new-redux/constants";
import PopOverSelectModule from "pages/clients&users/components/PopOverSelectModule";
import {
  getElementSystemDetails,
  getKPI,
  postAssociate,
} from "pages/clients&users/services/services";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import MainService from "services/main.service";

const RelationsViewSphere = ({
  module = {},
  btn,
  disabled,
  relations,
  contactInfo,
  setRelations,
  setListConv,
  setSelectedItem = () => {},
}) => {
  const [openPopOver, setOpenPopOver] = useState(false);
  const [openPopConfirm, setOpenPopConfirm] = useState(false);
  const [loadingSubmit, setLoadingSubmit] = useState(false);
  const [familyId, setFamilyId] = useState(null);
  const [form] = Form.useForm();
  const [t] = useTranslation("common");
  const { openView360InDrawer, activeTab360 } = useSelector(
    (state) => state?.vue360
  );
  const { contactHeaderInfo } = useSelector((state) => state?.contacts);

  const dispatch = useDispatch();
  const getAccessDisussion = async (contactInfo) => {
    try {
      const { data: element } = await getElementSystemDetails(contactInfo?.id);
      dispatch({
        type: openView360InDrawer
          ? SET_CONTACT_INFO_FROM_DRAWER
          : "SET_CONTACT_HEADER_INFO",
        payload: {
          ...contactInfo,
          access_discussion: element.access_discussion,
        },
      });
    } catch (err) {}
  };
  const onFinish = async (values) => {
    setLoadingSubmit(true);
    try {
      const formData = new FormData();
      formData.append("id", values.searchSelect);
      await postAssociate(contactInfo?.id, formData);
      const { data } = await getKPI(contactInfo?.family_id, contactInfo?.id);
      if (
        openView360InDrawer &&
        values.searchSelect === contactHeaderInfo?.id
      ) {
        dispatch(setNewInteraction({ type: "associateElement" }));
      } else if (activeTab360 == 3)
        dispatch(setNewInteraction({ type: "updateWithoutMercure" }));
      const convRmc = await MainService.getConvRmc360(contactInfo?.id);
      setListConv(convRmc.data.data.filter((el) => el.conversation_id));

      setRelations(
        data?.map((kpi) => ({
          ...kpi,
          children: kpi?.child?.length
            ? kpi?.child?.slice(0, 3)?.map((child) => ({
                label: child?.label_data,
                id: child?.id,
                pipeline: child?.pipeline_label,
                stage: child?.stage_label,
              }))
            : [],
        }))
      );
      if (
        data.find((el) => el.family_id === values.module)?.child?.length === 1
      ) {
        setSelectedItem(
          data.find((el) => el.family_id === values.module)?.child[0]
        );
      }
      getAccessDisussion(contactInfo);

      // setIsUpdate(true);
      // if (contactInfo.family_id && contactInfo?.id) {
      //   const { data } = await getKPI(contactInfo?.family_id, contactInfo?.id);

      //   setRelations(
      //     data?.map((kpi) => ({
      //       ...kpi,
      //       children: kpi?.child?.length
      //         ? kpi?.child?.slice(0, 3)?.map((child) => ({
      //             label: child?.label_data,
      //             id: child?.id,
      //             pipeline: child?.pipeline_label,
      //             stage: child?.stage_label,
      //           }))
      //         : [],
      //     }))
      //   );
      //   if (activeTab360 == 3)
      //     dispatch(setNewInteraction({ type: "interactions" }));
      // }
      // relations &&
      //   setRelations((prev) => {
      //     if (!prev.length) {
      //       return [];
      //     } else {
      //       return prev.map((element) =>
      //         element.family_id === values.module
      //           ? {
      //               ...element,
      //               children: [
      //                 {
      //                   label: data?.label_data,
      //                   id: data?.id,
      //                   pipeline: data?.pipeline_label,
      //                   stage: data?.stage_label,
      //                 },

      //                 ...element.children,
      //               ],
      //               child: [data, ...element.child],
      //               number: element?.number + 1,
      //             }
      //           : element
      //       );
      //     }
      //   });
      setOpenPopConfirm(false);
      setOpenPopOver(false);
      form.resetFields();

      toastNotification("success", "Association successful!", "topRight");
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification(
          "error",
          err?.response?.data?.message || t("toasts.somethingWrong"),
          "topRight"
        );
      throw new Error(err?.message ? err.message : err);
    } finally {
      setLoadingSubmit(false);
    }
  };
  return (
    <PopOverSelectModule
      onFinish={onFinish}
      action={t("voip.associate")}
      loadingSubmit={loadingSubmit}
      setOpenPopOver={setOpenPopOver}
      openPopOver={openPopOver}
      relations={relations}
      form={form}
      module={module}
      familyId={familyId}
      contactInfo={contactInfo}
      setsetFamilyId={setFamilyId}
      openPopConfirm={openPopConfirm}
      setOpenPopConfirm={setOpenPopConfirm}
      ButtonPopOver={
        btn ? (
          btn
        ) : (
          <Tooltip title={disabled ? "" : t("vue360.multipleRelations")}>
            {/* t("vue360.noAssociationsAvailable") */}
            <Button
              icon={<PlusOutlined />}
              disabled={disabled}
              type="link"
              shape="circle"
              onClick={(e) => e.stopPropagation()}
            />
          </Tooltip>
        )
      }
    />
  );
};

export default RelationsViewSphere;
