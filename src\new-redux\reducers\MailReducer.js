import {
  RESET_STATE,
  SET_ACCOUNT_MAIL,
  SET_ACCOUNT_MAIL_FETCHED,
  SET_LOADING_ACCOUNT,
  SET_SELECTED_ACCOUNT,
  SET_HAS_NEW_MESSAGE,
  SET_EMAILS_STATS,
  UPDATE_EMAILS_STATS,
  SET_OPEN_MODAL_EMAIL,
  SET_OPEN_EDITOR,
  SET_REFRESH_SENT_MAIL,
  SET_PAGE_EMAILS,
  SET_REFRESH_TABLE_INBOX,
  SET_PAGE_EMAILS_SIZE,
  SET_SEARCH_EMAIL,
  UPDATE_NOTIFICATION_MAILING,
  SET_NOTIFICATION_MAILING,
  SET_SEARCH_PAGE_EMAILS,
  SET_REFRESH_KPI,
  SET_REFRESH_NUMBER_NOTIFICATION,
  SET_FILTER_EMAIL,
  SET_FILTER_ACTIVE,
  SET_NUMBER_EMAIL_THREAD,
  SET_REFRESH_VUE_SPHERE,
  SET_SYNCHRO_TYPE,
  SET_DRAFT,
  RESET_DRAFT,
  RESET_PROPS_MODAL_EMAIL,
} from "../constants";
const initialState = {
  dataAccounts: [],
  loadingAccount: false,
  fetchedAccount: false,
  newMailNumber: 0,
  statsMail: null,
  notificationMailCount: null,
  openModalEmail: false,
  refreshMail: false,
  refreshMailInbox: 0,
  openEditor: { state: false, type: null },
  page: 1,
  searchPage: 1,
  pageSize: 20,
  searchEmail: "",
  refreshKPI: false,
  numberNotification: 0,
  filterEmail: false,
  filterEmailActive: false,
  numberEmailThread: 0,
  refreshMailVueSphere: 0,
  synchroType: "",
  draft: {},
  propsModalEmail: {},
  mailFilter: {},
};
const mailReducer = (state = initialState, action) => {
  const { type, payload } = action;
  switch (type) {
    case "SET_FILTER_MAIL":
      return {
        ...state,
        mailFilter: payload,
      };

    case SET_LOADING_ACCOUNT:
      return {
        ...state,
        loadingAccount: payload,
      };
    case SET_ACCOUNT_MAIL:
      return {
        ...state,
        dataAccounts: payload,
        fetchedAccount: true,
        loadingAccount: false,
      };
    case SET_ACCOUNT_MAIL_FETCHED:
      return {
        ...state,
        fetchedAccount: payload,
      };

    case SET_SELECTED_ACCOUNT:
      return {
        ...state,
        dataAccounts: state.dataAccounts.map((item) => {
          if (item.value === parseInt(payload)) {
            item.selected = true;
          } else item.selected = false;
          return item;
        }),
      };

    case SET_HAS_NEW_MESSAGE:
      return {
        ...state,
        newMailNumber: payload > 0 ? state.newMailNumber + payload : 0,
      };

    case SET_EMAILS_STATS:
      return {
        ...state,
        statsMail: payload,
      };

    case UPDATE_EMAILS_STATS:
      const { id, type, typeEmail, ids_labels } = payload;

      if (!id || !type || !typeEmail || !state.statsMail?.[id]) {
        return state;
      }

      const stats = {
        ...state.statsMail,
        [id]: {
          ...state.statsMail[id],
          labels: { ...state.statsMail[id]?.labels },
        },
      };

      const currentCount = stats[id][typeEmail] || 0;
      stats[id][typeEmail] =
        type === "add" ? currentCount + 1 : Math.max(0, currentCount - 1);

      if (type === "add" && ids_labels?.length) {
        for (const idLabel of ids_labels) {
          if (idLabel in stats[id].labels) {
            stats[id].labels[idLabel] = (stats[id].labels[idLabel] || 0) + 1;
          }
        }
      }

      return {
        ...state,
        statsMail: stats,
      };

    case SET_OPEN_MODAL_EMAIL:
      return {
        ...state,
        openModalEmail: payload,
      };
    case RESET_PROPS_MODAL_EMAIL:
      return {
        ...state,
        propsModalEmail: {},
      };

    case SET_OPEN_EDITOR:
      return {
        ...state,
        openEditor: payload,
      };
    case SET_REFRESH_SENT_MAIL:
      return {
        ...state,
        refreshMail: payload,
      };

    case SET_PAGE_EMAILS:
      return {
        ...state,
        page: payload,
      };

    case SET_SEARCH_PAGE_EMAILS:
      return {
        ...state,
        searchPage: payload,
      };

    case SET_REFRESH_TABLE_INBOX:
      return {
        ...state,
        refreshMailInbox: Date.now(),
      };

    case SET_PAGE_EMAILS_SIZE:
      return {
        ...state,
        pageSize: payload,
      };

    case SET_SEARCH_EMAIL:
      return {
        ...state,
        searchEmail: payload,
      };

    case SET_NOTIFICATION_MAILING: {
      return {
        ...state,
        notificationMailCount: payload,
      };
    }

    case SET_REFRESH_KPI:
      return {
        ...state,
        refreshKPI: payload,
      };

    case SET_REFRESH_NUMBER_NOTIFICATION:
      return {
        ...state,
        numberNotification: Math.floor(
          state.refreshMailInbox * Math.random() * 99 + 1
        ),
      };

    case SET_FILTER_EMAIL:
      return {
        ...state,
        filterEmail: payload,
      };

    case SET_FILTER_ACTIVE:
      return {
        ...state,
        filterEmailActive: payload,
      };

    case SET_NUMBER_EMAIL_THREAD:
      return {
        ...state,
        numberEmailThread: payload,
      };

    case UPDATE_NOTIFICATION_MAILING: {
      let list = state.notificationMailCount?.list ?? [];
      let hasUnread = state.notificationMailCount?.hasUnread;
      if (payload.seen === 1) {
        hasUnread = true;
      }
      if (list.find((item) => item.account_id === payload.account_id)) {
        list = list.map((item) => {
          if (item.account_id === payload.account_id) item.seen = payload.seen;
          return item;
        });
      } else {
        list.push(payload);
      }

      return {
        ...state,
        notificationMailCount: {
          list,
          hasUnread,
        },
      };
    }

    case SET_REFRESH_VUE_SPHERE:
      return {
        ...state,
        refreshMailVueSphere: Math.floor(
          state.refreshMailVueSphere * Math.random() * 99 + 1
        ),
      };
    case RESET_STATE: {
      return initialState;
    }
    case SET_SYNCHRO_TYPE: {
      return {
        ...state,
        synchroType: payload,
      };
    }
    case SET_DRAFT:
      return {
        ...state,
        draft: { ...state.draft, ...payload },
      };
    case RESET_DRAFT:
      if (state.draft[payload]) {
        const newDraft = { ...state.draft };
        delete newDraft[payload];
        return {
          ...state,
          draft: newDraft,
        };
      } else return state;
    default:
      return state;
  }
};
export default mailReducer;
