import {
  Result,
  Select,
  Skeleton,
  Typography,
  Form,
  Input,
  Button,
  Spin,
  Modal,
} from "antd";
import React, {
  Suspense,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useTranslation } from "react-i18next";
import { CgPassword } from "react-icons/cg";
import { Navigate, useParams } from "react-router-dom";
import GoBack from "./components/go-back-button";
import axios from "axios";
import useKeyDown from "../../custom-hooks/useKeyDown";
import { toastNotification } from "../../components/ToastNotification";
import useLocalStorage from "../../custom-hooks/useLocalStorage";
import { URL_ENV } from "../..";
import { lang } from "translations/lang";
import { redirectFunction } from "./utils";
import { CloseCircleFilled } from "@ant-design/icons";
const { Title, Text } = Typography;

const statusToken = {
  idle: "idle",
  loading: "loading",
  success: "success",
  error: "error",
};
const newAbortSignal = (timeoutMs) => {
  const abortController = new AbortController();
  setTimeout(() => abortController.abort(), timeoutMs || 0);
  return abortController;
};
function InvitationUsers() {
  const { i18n, t } = useTranslation("common");
  const { token } = useParams();
  const [form] = Form.useForm();
  const btnRef = useRef(null);
  useKeyDown(13, false, "keydown", () => btnRef.current.focus());

  const [loading, setLoading] = useState(false);
  const [name, setName] = useState("");
  const [tokenInvitation, setIsTokenValid] = useState(false);
  const [status, setStatus] = useState(statusToken.idle);
  const [newToken] = useLocalStorage(
    "accessToken",
    localStorage.getItem("accessToken")
  );

  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [submitted, setSubmitted] = useState(false);

  const isValidToken =
    newToken &&
    newToken !== "null" &&
    newToken !== undefined &&
    newToken !== "undefined" &&
    newToken !== null;

  const rules = useMemo(
    () => ({
      minLength: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      number: /[0-9]/.test(password),
      special: /[!@#$%^&*(),.?":{}|<>]/.test(password),
      match: password === confirmPassword && confirmPassword.length > 0,
    }),
    [password, confirmPassword]
  );
  const renderRule = useCallback(
    (isValid, label) => {
      const base = "text-sm flex items-center space-x-1";
      const color = submitted
        ? isValid
          ? "text-green-600"
          : "text-red-500"
        : isValid
        ? "text-green-600"
        : "text-gray-400";
      return (
        <div className={`${base} ${color}`}>
          <span>{isValid ? "✅" : submitted ? "❌" : "➖"}</span>
          <span>{label}</span>
        </div>
      );
    },
    [submitted]
  );

  const handleSubmit = useCallback(async () => {
    setSubmitted(true);
    setLoading(true);
    form
      .validateFields()
      .then(async (values) => {
        try {
          if (!tokenInvitation) return;
          const formData = new FormData();
          formData.append("password", values.password);
          formData.append(
            "password_confirmation",
            values.password_confirmation
          );
          formData.append("token", token);

          const res = await axios.post(
            URL_ENV?.REACT_APP_BASE_URL +
              process.env.REACT_APP_SUFFIX_API +
              "confirm-password",
            formData,
            {
              headers: {
                Authorization: process.env.REACT_APP_API_VISIO_KEY,
                "Content-Type": "multipart/form-data",
              },
            }
          );

          if (res.status === 200) {
            toastNotification(
              "success",
              t("login.reset_password.success_password_Set"),
              "topLeft",
              4
            );
            redirectFunction(
              res.data?.data?.response?.token,
              res.data?.data?.response?.user?.application,
              i18n.language
            );
          }
        } catch {
          toastNotification("error", t("toasts.somethingWrong"), "topLeft", 4);
        } finally {
          setLoading(false);
        }
      })
      .catch(() => {});
  }, [form, tokenInvitation, token, t, password, confirmPassword]);

  useEffect(() => {
    if (isValidToken) return;
    let isMounted = true;
    const abortController = newAbortSignal(10000);
    const checkToken = async () => {
      try {
        setStatus(statusToken.loading);
        const url = new URL(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        );
        url.pathname = url.pathname.concat("confirm-access");
        url.searchParams.append("token", token);
        const response = await axios.get(url.href, {
          headers: {
            Authorization: process.env.REACT_APP_API_VISIO_KEY,
            "Content-Type": "multipart/form-data",
          },
          signal: abortController.signal,
        });
        if (response.status === 200 && isMounted) {
          setName(response.data?.name || "");
          setStatus(statusToken.success);
          setIsTokenValid(true);
        }
      } catch {
        if (isMounted) setStatus(statusToken.error);
        setIsTokenValid(false);
      }
    };
    checkToken();
    return () => {
      abortController.abort();
      isMounted = false;
    };
  }, [isValidToken, token]);

  if (isValidToken) return <Navigate to="/dashboard" />;

  if (!token)
    return (
      <Result
        status="warning"
        title={t("login.reset_password.wrongLink")}
        extra={<GoBack />}
      />
    );

  return (
    <div className="flex h-screen w-screen items-center justify-center overflow-hidden">
      <div className="flex h-full w-full flex-1 flex-col items-center justify-between p-4 lg:p-1">
        <div className="h-1/8 flex w-full items-center justify-between px-2 py-3 lg:px-8">
          <img
            className="h-8 w-24 object-contain sm:w-auto lg:h-10 lg:w-auto"
            src="/login/img.png"
            alt="logo"
          />
          <Modal
            open={status !== statusToken.success}
            footer={null}
            closable={false}
            width={600}
            centered={true}
          >
            <div className="flex  items-start space-x-3">
              <Spin spinning={status === statusToken.loading} />
              {status === statusToken.error && (
                <CloseCircleFilled className="text-xl text-red-500" />
              )}
              <Title
                level={4}
                type={status === statusToken.error ? "danger" : "secondary"}
              >
                {status === statusToken.loading
                  ? t("login.reset_password.verifToken")
                  : t("login.reset_password.invitationExpired")}
              </Title>
            </div>
          </Modal>
          <Select
            className="w-24 sm:w-32 lg:w-32"
            value={i18n.language || "en"}
            onChange={(e) => {
              i18n.changeLanguage(e);
              localStorage.setItem("language", e);
            }}
            options={lang}
            placeholder={t("login.SelectLanguage")}
          />
        </div>

        <div className="relative flex w-full max-w-lg flex-1 flex-col items-center justify-center space-y-3 px-8">
          <Form
            layout="vertical"
            name="reset-password"
            className="flex h-full w-full flex-col items-center justify-center"
            form={form}
            onFinish={handleSubmit}
          >
            <Form.Item>
              <div className="flex items-center justify-center text-2xl font-bold sm:max-w-sm sm:text-4xl md:max-w-md lg:text-4xl xl:max-w-xl 2xl:max-w-full">
                <span className="whitespace-nowrap">{t("login.Title1")}</span>
                <p
                  title={name?.charAt(0).toUpperCase() + name?.slice(1)}
                  className="m-0 max-w-[150px] truncate pl-1 md:max-w-md lg:max-w-sm"
                >
                  {name?.charAt(0).toUpperCase() + name?.slice(1)}
                </p>
                <span className="ml-2">👋</span>
              </div>
            </Form.Item>

            <header className="flex w-full flex-col items-center space-y-2 py-2">
              <CgPassword className="mb-2 h-14 w-14 rounded-md p-1 text-gray-500 ring-1 ring-gray-300" />
              <Title level={4}>{t("login.reset_password.title")}</Title>
              <Text type="secondary" className="mb-2">
                {t("login.reset_password.subtitle")}
              </Text>
            </header>

            <Form.Item
              className="w-full"
              name="password"
              required={true}
              label={t("login.reset_password.password")}
              rules={[
                {
                  validateTrigger: ["onChange", "onSubmit"],
                  validator: async (_, value) => {
                    if (!value)
                      throw new Error(
                        t("login.reset_password.passwordRequired")
                      );
                  },
                },
              ]}
            >
              <Input.Password
                minLength={8}
                autoFocus
                onChange={(e) => {
                  setPassword(e.target.value);
                  setSubmitted(false);
                }}
                value={password}
                size="large"
                placeholder={t("login.reset_password.password")}
              />
            </Form.Item>

            <Form.Item
              label={t("login.reset_password.confirmPassword")}
              className="w-full"
              required={true}
              name="password_confirmation"
              rules={[
                {
                  validateTrigger: "onChange",
                  validator: async (_, value) => {
                    setConfirmPassword(value);
                    if (!value)
                      throw new Error(
                        t("login.reset_password.passwordRequired")
                      );
                    if (value !== password)
                      throw new Error(t("login.reset_password.passwordMatch"));
                  },
                },
              ]}
            >
              <Input.Password
                minLength={8}
                size="large"
                value={confirmPassword}
                onChange={(e) => {
                  setConfirmPassword(e.target.value);
                  setSubmitted(false);
                }}
                placeholder={t("login.reset_password.confirmPassword")}
              />
            </Form.Item>

            {status === statusToken.success && (
              <div className="mb-4 mt-1 w-full space-y-1 text-sm">
                {renderRule(
                  rules.minLength,
                  t("login.reset_password.minLength")
                )}
                {renderRule(
                  rules.uppercase,
                  t("login.reset_password.uppercase")
                )}
                {renderRule(rules.number, t("login.reset_password.number"))}
                {renderRule(
                  rules.special,
                  t("login.reset_password.specialChar")
                )}
                {renderRule(
                  rules.match,
                  t("login.reset_password.passwordMatch")
                )}
              </div>
            )}

            <Suspense fallback={<Skeleton.Button active block />}>
              <Form.Item className="removeCSS">
                <Button
                  disabled={loading || status !== statusToken.success}
                  ref={btnRef}
                  block
                  className="flex items-center justify-center px-10 py-5 text-center text-base"
                  type="primary"
                  loading={loading}
                  htmlType="submit"
                >
                  {t("login.reset_password.button")}
                </Button>
              </Form.Item>
            </Suspense>
          </Form>
        </div>

        <div className="h-1/8 mb-2 space-x-1">
          <span>© 2006 - {new Date().getFullYear()} </span>
          <a
            className="hover:underline"
            href="https://www.comunikcrm.com/"
            target="_blank"
            rel="noreferrer"
          >
            Comunik CRM
          </a>
        </div>
      </div>

      <div className="hidden h-screen w-1/2 flex-col md:flex">
        <div className="hidden flex-1 items-center justify-center bg-indigo-100 sm:flex">
          <img
            alt="background"
            loading="lazy"
            height="70%"
            className="m-12 object-contain xl:m-16"
            src="/login/back.webp"
            width="90%"
          />
        </div>
      </div>
    </div>
  );
}

export default InvitationUsers;
