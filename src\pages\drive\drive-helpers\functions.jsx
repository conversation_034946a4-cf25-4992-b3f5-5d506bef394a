import { message } from "antd";
import { getFileOfficeDrive, uploadFolderDrive, deleteDriveItem, updateDriveItem } from "../../../services/main.service";
import { URL_ENV } from "index";

export const openFileOnOnlyOfficeFromDrive = async (fileId, t) => {
  try {
    const response = await getFileOfficeDrive(fileId);

    
    const url = new URL(
      `${
        process.env.REACT_APP_BRANCH === "devLocal"
          ? process.env.REACT_APP_LOCAL_URL
          : URL_ENV?.REACT_APP_DOMAIN
      }/editor`
    );

    url.searchParams.set("fileUrl", response?.data?.config?.document?.url);
    url.searchParams.set("token", response?.data?.config?.token);
    url.searchParams.set("fileType", response?.data?.config?.document?.fileType);
    url.searchParams.set("documentType", response?.data?.config?.document?.documentType);
    url.searchParams.set("key", response?.data?.config?.document?.key);
    url.searchParams.set("url", response?.data?.config?.document?.url);
    url.searchParams.set("mode", response?.data?.config?.editorConfig?.mode);
    url.searchParams.set("lang", response?.data?.config?.editorConfig?.lang);
    url.searchParams.set("editingMode", response?.data?.config?.editorConfig?.coEditing?.mode);
    url.searchParams.set("change", response?.data?.config?.editorConfig?.coEditing?.change);
    url.searchParams.set("email", response?.data?.config?.editorConfig?.user?.email);
    url.searchParams.set("id", response?.data?.config?.editorConfig?.user?.id);
    url.searchParams.set("image", "null");
    url.searchParams.set("name", response?.data?.config?.editorConfig?.user?.name);
    url.searchParams.set("title", response?.data?.config?.document?.title);
    
 
    const a = document.createElement("a");
    a.style.display = "none";
    a.href = url;
    a.target = "_blank";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
    
 
    localStorage.setItem("document", JSON.stringify(fileId));
    localStorage.setItem("drive", JSON.stringify(true));
    
    return response;
  } catch (error) {
    console.error("Open file error:", error);
    message.error(t("drive.failedToOpenFile"));
    throw error;
  }
};

export const canOpenInOnlyOffice = (extension) => {
  const supportedExtensions = [
   
    'doc', 'docx', 'docm', 'dot', 'dotx', 'dotm', 'odt', 'fodt', 'ott', 'rtf', 'txt',
  
    'xls', 'xlsx', 'xlsm', 'xlt', 'xltx', 'xltm', 'ods', 'fods', 'ots', 'csv',
  
    'ppt', 'pptx', 'pptm', 'pot', 'potx', 'potm', 'odp', 'fodp', 'otp','pdf'
  ];
  
  return supportedExtensions.includes(extension?.toLowerCase());
};

/**
 * Creates a new folder in the drive
 * @param {string} folderName - Name of the folder to create
 * @param {string} parentId - ID of the parent folder (empty string for root)
 * @param {function} t - Translation function
 * @returns {Promise} - Promise that resolves with the created folder data
 */
export const createFolderInTree = async (folderName, parentId, t) => {
  try {
    const formData = new FormData();
    formData.append("name", folderName);
    if (parentId && parentId !== "1") {
      formData.append("parent", parentId);
    }
    formData.append("type", "folder");
    
    const response = await uploadFolderDrive(formData);
    message.success(t("drive.folderCreatedSuccessfully"));
    return response;
  } catch (error) {
    console.error("Create folder error:", error);
    message.error(t("drive.failedToCreateFolder"));
    throw error;
  }
};

/**
 * Deletes a folder from the drive
 * @param {string} folderId - ID of the folder to delete
 * @param {function} t - Translation function
 * @returns {Promise} - Promise that resolves when folder is deleted
 */
export const deleteFolderFromTree = async (folderId, t) => {
  try {
    const response = await deleteDriveItem(folderId);
    message.success(t("drive.folderDeletedSuccessfully"));
    return response;
  } catch (error) {
    console.error("Delete folder error:", error);
    message.error(t("drive.failedToDeleteFolder"));
    throw error;
  }
};

/**
 * Renames a folder in the drive
 * @param {string} folderId - ID of the folder to rename
 * @param {string} newName - New name for the folder
 * @param {function} t - Translation function
 * @returns {Promise} - Promise that resolves when folder is renamed
 */
export const renameFolderInTree = async (folderId, newName, t) => {
  try {
    const response = await updateDriveItem(folderId, { name: newName });
    message.success(t("drive.folderRenamedSuccessfully"));
    return response;
  } catch (error) {
    console.error("Rename folder error:", error);
    message.error(t("drive.failedToRenameFolder"));
    throw error;
  }
};

/**
 * Recursively finds a node in the tree by its key
 * @param {Array} treeData - The tree data array
 * @param {string} targetKey - The key to find
 * @returns {Object|null} - The found node or null
 */
export const findNodeInTree = (treeData, targetKey) => {
  for (const node of treeData) {
    if (node.key === targetKey) {
      return node;
    }
    if (node.children && node.children.length > 0) {
      const found = findNodeInTree(node.children, targetKey);
      if (found) {
        return found;
      }
    }
  }
  return null;
};

/**
 * Recursively removes a node from the tree
 * @param {Array} treeData - The tree data array
 * @param {string} targetKey - The key of the node to remove
 * @returns {Array} - Updated tree data
 */
export const removeNodeFromTree = (treeData, targetKey) => {
  return treeData.filter(node => {
    if (node.key === targetKey) {
      return false;
    }
    if (node.children && node.children.length > 0) {
      node.children = removeNodeFromTree(node.children, targetKey);
    }
    return true;
  });
};

/**
 * Recursively updates a node in the tree
 * @param {Array} treeData - The tree data array
 * @param {string} targetKey - The key of the node to update
 * @param {Object} updates - The updates to apply to the node
 * @returns {Array} - Updated tree data
 */
export const updateNodeInTree = (treeData, targetKey, updates) => {
  return treeData.map(node => {
    if (node.key === targetKey) {
      return { ...node, ...updates };
    }
    if (node.children && node.children.length > 0) {
      return {
        ...node,
        children: updateNodeInTree(node.children, targetKey, updates)
      };
    }
    return node;
  });
};

/**
 * Recursively adds a new node to the tree under a parent
 * @param {Array} treeData - The tree data array
 * @param {string} parentKey - The key of the parent node
 * @param {Object} newNode - The new node to add
 * @returns {Array} - Updated tree data
 */
export const addNodeToTree = (treeData, parentKey, newNode) => {
  if (parentKey === "1" || !parentKey) {
    // Add to root level
    return [...treeData, newNode];
  }
  
  return treeData.map(node => {
    if (node.key === parentKey) {
      return {
        ...node,
        children: [...(node.children || []), newNode],
        isLeaf: false
      };
    }
    if (node.children && node.children.length > 0) {
      return {
        ...node,
        children: addNodeToTree(node.children, parentKey, newNode)
      };
    }
    return node;
  });
};
