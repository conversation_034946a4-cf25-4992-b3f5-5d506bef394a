import React, { useCallback, useEffect, useRef } from "react";
import {
  Form,
  Input,
  Button,
  Space,
  Switch,
  Select,
  Tag,
  Divider,
  message,
} from "antd";
import { useState } from "react";
import { generateAxios } from "../services/axiosInstance";
import { useTranslation } from "react-i18next";
import { toastNotification } from "./ToastNotification";
import Header from "./configurationHelpDesk/Header";
import NewTableDraggable from "./NewTableDraggable";
import { setSearch } from "../new-redux/actions/menu.actions/menu";
import { useDispatch, useSelector } from "react-redux";
import LabelTable from "./LabelTable";
import BottomButtonAddRow from "./BottomButtonAddRow";
import { SubmitKeyPress } from "../utils/SubmitKeyPress";
import { URL_ENV } from "index";
import { PlusOutlined } from "@ant-design/icons";

const Subjects = () => {
  const [form] = Form.useForm();
  const [, setCount] = useState(0);

  const [data, setData] = useState([]);
  const [itemsLength, setItemsLength] = useState(0);

  const [editingKey, setEditingKey] = useState("");
  const [id, setId] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [oldPage, setOldPage] = useState(1);
  const [loading, setLoading] = useState(true);
  const [departments, setDepartments] = useState([]);
  const [pageSize, setPageSize] = useState(20);

  const [items, setItems] = useState([]);

  const isEditing = (record) => record.key === editingKey;
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const { search } = useSelector((state) => state.form);
  const inputRefs = useRef([]);
  const inputRef = useRef(null);

  useEffect(() => {
    inputRefs.current.forEach((input) => {
      input?.focus();
    });
  }, [data.length, id]);

  const onFinishFailed = (values) => {
    console.log(values);
  };

  const addItem = (e) => {
    e.preventDefault();
    e.stopPropagation();
    const inputValue = inputRef.current?.input?.value.trim();
    if (inputValue) {
      if (items.some((el) => el === inputValue)) {
        alert("no");
        return;
      }
      setItems([...items, inputValue]);
      e.stopPropagation();
      // Réinitialise l'input
      if (inputRef.current) {
        inputRef.current.input.value = "";
      }
      // Garde le dropdown ouvert et focus sur l'input
      setTimeout(() => {
        inputRef.current?.focus();
      }, 0);
    }
  };
  const EditableCellAlternative = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    const [isOpen, setIsOpen] = useState(false);
    useEffect(() => {
      if (items.length > itemsLength)
        setTimeout(() => {
          setIsOpen(true);
        }, 10);
    }, [items.length]);

    const handleAddItem = useCallback(
      (e) => {
        e.preventDefault();
        e.stopPropagation();

        const inputValue = inputRef.current?.input?.value.trim();
        if (inputValue) {
          if (items.some((el) => el === inputValue)) {
            message.open({
              type: "error",
              content: t("signature.labelUsed"),
            });
            return;
          }
          setItems((prevItems) => [...prevItems, inputValue]);

          if (inputRef.current) {
            inputRef.current.input.value = "";
          }

          // Le useEffect se chargera de rouvrir le dropdown
          setTimeout(() => inputRef.current?.focus(), 0);
        }
      },
      [items, setItems]
    );

    const inputNode =
      inputType === "customDropdown" ? (
        <Select
          open={isOpen}
          onDropdownVisibleChange={setIsOpen}
          style={{ width: 300 }}
          placeholder={t("tasks.selectTags")}
          mode="multiple"
          dropdownRender={(menu) => (
            <div onClick={(e) => e.stopPropagation()}>
              {menu}
              <Divider style={{ margin: "8px 0" }} />
              <Space style={{ padding: "0 8px 4px" }}>
                <Input
                  placeholder={t("tags.createTag")}
                  ref={inputRef}
                  allowClear
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      handleAddItem(e);
                    }
                    e.stopPropagation();
                  }}
                />
                <Button
                  icon={<PlusOutlined />}
                  type="primary"
                  onClick={handleAddItem}
                >
                  {t("voip.add")}
                </Button>
              </Space>
            </div>
          )}
          options={items.map((item) => ({ label: item, value: item }))}
        />
      ) : inputType === "requiredSwitch" ? (
        <Switch
          size="small"
          defaultChecked={
            !loading
              ? record.authorized === true
              : form.getFieldsValue().authorized == false
              ? false
              : true
          }
        />
      ) : inputType === "select" ? (
        <Select
          placeholder={t("services.selectdepartment")}
          options={departments}
          showSearch
          filterOption={(input, option) =>
            (option?.label?.toLowerCase() ?? "").includes(input.toLowerCase())
          }
        />
      ) : (
        <Input
          ref={(el) => (inputRefs.current[index] = el)}
          onKeyPress={handleKeyPress}
          placeholder={t("activities.name")}
        />
      );

    return (
      <td {...restProps}>
        {editing ? (
          <Form.Item name={dataIndex.toLowerCase()} style={{ margin: 0 }}>
            {inputNode}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };
  const edit = (record) => {
    if (record) {
      form.setFieldsValue({
        label: record.label,
        authorized: record.authorized,
        id_department: record.department?.id,
        tags: record.tags,
      });
      setItemsLength(Array.isArray(record.tags) ? record?.tags?.length : 0);
      setItems(record.tags ?? []);
      setId(record.id);
    } else {
      form.setFieldsValue({
        label: "",
        authorized: "",
        id_department: undefined,
        tags: [],
      });
    }
    setEditingKey(record.key);
  };
  const cancel = (record) => {
    setEditingKey("");
    setId(null);
    if (!record.id) {
      setData(data.filter((item) => item.key !== record.key));
    }
  };

  const UpdateSwitch = async (record, authorized) => {
    setLoading(true);
    try {
      const res = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).put(
        `/ticket-subjects/${record.id}`,
        {
          label: record.label,
          authorized: authorized === true || authorized === 1 ? 1 : 0,
        },
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      );
      setEditingKey("");
      setData(
        data.map((el) =>
          el.id === res.data.data.id
            ? {
                ...res.data.data,
                key: res.data.data.id,
              }
            : el
        )
      );
      form.setFieldsValue({
        label: "",
        authorized: "",
      });
      setLoading(false);
      toastNotification("success", record.label + t("toasts.edit"), "topRight");
    } catch (errInfo) {
      setLoading(false);

      toastNotification("error", t("toasts.somethingWrong"), "topRight");
    }
  };

  const save = async (key) => {
    // setLoading(true);
    if (id) {
      try {
        const row = await form.validateFields();
        const formData = new URLSearchParams();
        formData.append("label", row.label);
        formData.append(
          "authorized",
          row.authorized === true || row.authorized === 1 ? 1 : 0
        );
        row.id_department &&
          formData.append("id_department", row.id_department);
        console.log(row.tags);
        if (Array.isArray(row.tags) && row.tags.length > 0)
          row.tags.forEach((tag, index) => {
            formData.append(`tags[]`, tag);
          });
        else formData.append(`tags[]`, "");
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).put(`/ticket-subjects/${id}`, formData, {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        });
        setEditingKey("");
        setData(
          data.map((el) =>
            el.id === res.data.data.id
              ? {
                  ...res.data.data,
                  key: res.data.data.id,
                }
              : el
          )
        );
        form.setFieldsValue({
          label: "",
          authorized: "",
        });
        setLoading(false);
        toastNotification("success", row.label + t("toasts.edit"), "topRight");
      } catch (errInfo) {
        setLoading(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    } else {
      try {
        const row = await form.validateFields();
        const formData = new FormData();
        formData.append("label", row.label);
        formData.append(
          "authorized",
          row.authorized === true || row.authorized === 1 ? 1 : 0
        );
        row.id_department &&
          formData.append("id_department", row.id_department);
        if (Array.isArray(row.tags) && row.tags.length > 0)
          row.tags.forEach((tag, index) => {
            formData.append(`tags[]`, tag);
          });
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post("/ticket-subjects", formData, {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        });
        setEditingKey("");
        setData([
          ...data.filter((el) => el.id),
          { ...res.data.data, key: res.data.data.id },
        ]);
        form.setFieldsValue({
          label: "",
          authorized: "",
        });
        setLoading(false);
        toastNotification(
          "success",
          row.label + t("toasts.created"),
          "topRight"
        );
      } catch (errInfo) {
        setLoading(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    }
  };
  useEffect(() => {
    const getFolders = async () => {
      setLoading(true);
      try {
        const {
          data: { data },
        } = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get("/ticket-subjects");
        setData(data.map((el, i) => ({ ...el, key: el.id, rank: i + 1 })));
        if (data.length > 0) {
          setCount(Math.max(...data.map((el) => el.id)));
        }
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get("/departments");
        setDepartments(
          res.data.data.map((el) => ({
            label: el.label,
            value: el.id,
            color: el.color,
          }))
        );
        setLoading(false);
      } catch (err) {
        setLoading(false);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    };
    getFolders();
    return () => dispatch(setSearch(""));
  }, []);

  const handleKeyPress = (event) => {
    SubmitKeyPress(event, form);
  };
  const handleClick = (event) => {
    event.stopPropagation();
  };

  const columns = [
    {
      title: t("activities.name"),
      dataIndex: "label",
      key: "label",
      editable: true,
      sorter: (a, b) => a.label.localeCompare(b.label),
      render: (_, record) => {
        return (
          <LabelTable record={record} editingKey={editingKey} edit={edit} />
        );
      },
    },
    {
      title: t("helpDesk.actif"),
      key: "authorized",
      dataIndex: "authorized",
      width: 100,
      editable: true,
      filters: [
        {
          text: t(`helpDesk.actif`),
          value: "1",
        },
        {
          text: t(`helpDesk.noActif`),
          value: "0",
        },
      ],
      onFilter: (value, record) => record.authorized == value,
      sorter: (a, b) => a.authorized - b.authorized,

      render: (_, record) => (
        <>
          <Switch
            size="small"
            checked={record.authorized == 1 ? true : false}
            onChange={(e) => UpdateSwitch(record, e)}
          />
        </>
      ),
    },
    {
      title: t("table.header.department"),
      dataIndex: "id_department",
      key: "id_department",
      editable: true,
      filters: departments.map((el) => ({
        text: el.label,
        value: el.value.toString(),
      })),

      onFilter: (value, record) => record.departement_id == value,

      sorter: (a, b) =>
        departments
          .find((el) => el.value === a.departement_id)
          ?.label.localeCompare(
            departments.find((el) => el.value === b.departement_id)?.label
          ),

      render: (_, record) => (
        <>
          {record.department ? (
            <span
              style={{
                color: record.department.color,
              }}
            >
              {record.department?.label}{" "}
            </span>
          ) : (
            ""
          )}
        </>
      ),
    },

    {
      title: t("menu2.tags"),
      dataIndex: "tags",
      key: "tags",
      editable: true,
      // filters: departments.map((el) => ({
      //   text: el.label,
      //   value: el.value.toString(),
      // })),

      // onFilter: (value, record) => record.departement_id == value,

      // sorter: (a, b) =>
      //   departments
      //     .find((el) => el.value === a.departement_id)
      //     ?.label.localeCompare(
      //       departments.find((el) => el.value === b.departement_id)?.label
      //     ),

      render: (_, record) => (
        <>
          {record?.tags?.length > 0
            ? record?.tags?.map((el) => <Tag>{el}</Tag>)
            : ""}
        </>
      ),
    },
  ];

  const handleAdd = () => {
    const ids = data.map((object) => {
      return object.id;
    });
    setItems([]);
    setItemsLength(0);
    setId(null);
    const newData = {
      key: Math.max(...ids) + 1,
      label: `  `,
      authorized: true,
      id_department: undefined,
      tags: [],
      disabled: true,
    };
    setCurrentPage(Math.ceil([...data, newData].length / pageSize));
    setOldPage(Math.ceil([...data, newData].length / pageSize));
    setData([...data, newData]);
    form.setFieldsValue({
      label: "",
      authorized: 1,
      id_department: undefined,
      tags: [],
    });
    setEditingKey(Math.max(...ids) + 1);
    setCount(Math.max(...ids) + 1);
  };

  //do not delete this line
  const onRow = () => {};

  const filteredData = data.filter((item) => {
    return item.label.toLowerCase().includes(search.toLowerCase());
  });
  return (
    <Space direction="vertical" style={{ width: "100%" }}>
      <Header
        active={"5"}
        editingKey={editingKey}
        handleAdd={handleAdd}
        btnText={t("helpDesk.addSubject")}
        disabled={loading ? true : editingKey ? true : search ? true : false}
      />

      <NewTableDraggable
        columns={columns}
        setLoading={setLoading}
        isEditing={isEditing}
        data={filteredData}
        setData={setData}
        loading={loading}
        save={save}
        edit={edit}
        EditableCell={EditableCellAlternative}
        onFinishFailed={onFinishFailed}
        cancel={cancel}
        form={form}
        apiRank="/rank-subjects"
        editingKey={editingKey}
        api="ticket-subjects"
        onRow={onRow}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        pageSize={pageSize}
        setPageSize={setPageSize}
      />

      <BottomButtonAddRow
        editingKey={editingKey}
        data={filteredData}
        text={t("helpDesk.addSubject")}
        handleAdd={handleAdd}
        loading={loading}
        search={search || ""}
      />
    </Space>
  );
};
export default Subjects;
