import { SendOutlined } from "@ant-design/icons";
import { But<PERSON> } from "antd";
import { toastNotification } from "components/ToastNotification";
import { stopSearchMsg } from "new-redux/actions/chat.actions";
import { useActionMessage } from "pages/layouts/chat/hooks/useActionMessage";
import React, {
  useState,
  useEffect,
  useRef,
  useCallback,
  memo,
  useLayoutEffect,
} from "react";
import { useTranslation } from "react-i18next";
import {
  FaRegPauseCircle,
  FaRegPlayCircle,
  FaRegStopCircle,
} from "react-icons/fa";
import { FiMic, FiTrash2 } from "react-icons/fi";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import WaveSurfer from "wavesurfer.js";
import Timeline from "wavesurfer.js/dist/plugins/timeline.esm.js";
import RecordPlugin from "wavesurfer.js/dist/plugins/record.esm.js";
import "./style.css";
import { scrollToBottom } from "new-redux/actions/chat.actions/Input";
let wavesurferRecorded;
let intervalId;
const RecordComponent = ({
  isRecording,
  setIsRecording = () => {},
  open,
  close = () => {},
  openRecord,
  setOpenRecord = () => {},
  setRecordedBlobNotes = () => {},
  isFullURL = false,
}) => {
  const { selectedConversation } = useSelector((state) => state.ChatRealTime);
  const { openDrawerChat } = useSelector((state) => state.voip);
  const dispatch = useDispatch();
  const { t } = useTranslation("common");

  const [wavesurfer, setWaveSurfer] = useState(null);
  const [record, setRecord] = useState(null);

  const [recordBlob, setRecordBlob] = useState(null);

  const [scrollingWaveform, setScrollingWaveform] = useState(false);
  const [deleteRecording, setDeleteRecording] = useState(false);
  const [audioStatus, setAudioStatus] = useState("play");
  const [recordStatus, setRecordStatus] = useState("pause");
  const [recordingTime, setRecordingTime] = useState(0);
  const [sendMessageBeforeStop, setSendMessageBeforeStop] = useState(false);

  const [sendmessage, setSendMessage] = useState(false);

  // const [intervalId, setIntervalId] = useState(null);
  const [fullTime, setFullTime] = useState(0);
  const micRef = useRef(null);
  const sendButtonRef = useRef(null);
  const progressRef = useRef(null);
  const recordingsRef = useRef(null);
  const {
    mutate: handleActionMessage,
    isSuccess: isMessageSuccess,
    isLoading,
    isError,
  } = useActionMessage("send_message_voice");
  const sendMessage = useCallback(async () => {
    dispatch(stopSearchMsg(true));
    setSendMessage(true);
    const [minutes, seconds] = fullTime.split(":");
    const totalSeconds = parseInt(minutes) * 60 + parseInt(seconds);
    handleActionMessage({
      message_id: null,
      params: {
        voice: recordBlob,
        voice_size: totalSeconds,
      },

      type_conversation: selectedConversation?.type,
      type_action: "send_voice_message",
    });
    //     setTimeout(() => {
    // deleteRecordAfterSend();
    // }, 2000);
    // setRecordingTimeVoice(0);
    //   if(!isLoading ){
    //  deleteRecord()
    //   }

    // alert(recordingTimeVoice);
  }, [
    selectedConversation?.type,
    handleActionMessage,
    dispatch,
    recordBlob,
    isLoading,
    isMessageSuccess,
  ]);
  useLayoutEffect(() => {
    if (open && !recordBlob) {
      handleRecordButtonClick();
    }
    if (!open && recordBlob && isFullURL) {
      deleteRecord();
    }

    // if (!open && !recordedBlob?.blob) {
    //   handleDeleteRecording();
    // }
    // if (!open && recordedBlob?.blob) {
    //   setRecordedBlob(null);
    // }
    // return () => {
    //   if (open && recordedBlob) setRecordedBlob(null);
    // };
  }, [open, recordBlob]);
  useEffect(() => {
    // let timer
    if (fullTime === "01:00") {
      //  timer = setTimeout(() => {
      record.stopRecording();
      setIsRecording(false);
      // handlePauseButtonClick()
      // setFullTime("01:00");
      // setRecordingTimeVoice(6);
      // }, 60800);
    }
    // return () => clearTimeout(timer);
  }, [fullTime]);

  useEffect(() => {
    return () => {
      if (record?.stream) {
        setIsRecording(false);
        setDeleteRecording(true);
        setOpenRecord(false);
        record.stopRecording();
      }
    };
  }, [record]);
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === "Enter" && !isFullURL) {
        const button = sendButtonRef.current;
        if (button) {
          // Déclenchez le clic sur le bouton
          button.click();
        }
        // Ajoutez votre logique ici
      }
    };

    // Attache le gestionnaire d'événements à l'élément racine du composant
    document.addEventListener("keydown", handleKeyDown);

    // Nettoie le gestionnaire d'événements lorsque le composant est démonté
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [isFullURL]);
  useEffect(() => {
    createWaveSurfer();

    return () => {
      setRecord(null);
      setOpenRecord(false);

      destroyWaveSurfer();
      setRecordBlob(null);
    };
  }, []);
  // useEffect(() => {
  // if(record&&isRecording){
  //   setIsRecording(false)
  //   record.stopRecording();
  //   setRecordingTime(0)

  //       recordingsRef.current.innerHTML = "";
  //       progressRef.current.textContent = "";
  //   destroyWaveSurfer();
  // }
  // }, [selectedConversation?.id,record,isRecording])

  // useEffect(() => {
  //   if(isMessageSuccess ){
  //     deleteRecord()
  //     close()

  //   }

  // },[isMessageSuccess,sendMessage])

  // useEffect(() => {
  //   if(sendMessageBeforeStop && recordBlob){
  //     sendMessage();
  //     }
  // }, [sendMessageBeforeStop,recordBlob])

  useEffect(() => {
    if (recordBlob && sendMessageBeforeStop) {
      sendMessage();
      setSendMessageBeforeStop(false);
      if (isError) {
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
      dispatch(scrollToBottom(Math.floor(Math.random() * 1000000 + 1)));
    } else if (sendmessage && recordBlob && !isError && !isLoading) {
      //  close()
      deleteRecordAfterSend();
    }
    // if (isError) {
    //   close();
    // }
    // return () => {
    //   if (sendmessage && recordBlob && !isError&&!isLoading && isMessageSuccess) {
    //     setSendMessage(false);
    //   }
    // };
  }, [sendmessage, recordBlob, isError, isLoading, sendMessageBeforeStop]);
  const createWaveSurfer = () => {
    if (wavesurfer) {
      wavesurfer.destroy();
    }
    const wavesurferInstance = WaveSurfer.create({
      container: micRef.current,
      waveColor: "gray",
      progressColor: "gray",
      height: 40,
    });

    setWaveSurfer(wavesurferInstance);

    const recordPlugin = RecordPlugin.create({
      scrollingWaveform,
      renderRecordedAudio: false,
    });
    wavesurferInstance.registerPlugin(recordPlugin);
    setRecord(recordPlugin);
    recordingsRef.current.innerHTML = "";
    recordPlugin.on("record-start", () => {
      setOpenRecord(true);
    });
    // const clickedTime = progress * duration;
    // setRecordingTime(Math.floor(Number(clickedTime)));
    recordPlugin.on("record-end", (blob) => {
      setRecordBlob(blob);
      if (isFullURL) {
        setRecordedBlobNotes(blob);
      }

      // if(recordingsRef.current)
      // recordingsRef.current.innerHTML = "";
      const container = recordingsRef.current;
      const recordedUrl = URL.createObjectURL(blob);
      if (container) {
        wavesurferRecorded = WaveSurfer.create({
          container,
          waveColor: "gray",
          progressColor: "gray",
          url: recordedUrl,
          height: 40,
          plugins: Timeline.create(),
        });
        // const button = container.appendChild(document.createElement("button"));
        // button.textContent = "Play";
        // button.onclick = () => wavesurferRecorded.playPause();
        wavesurferRecorded.on("load", () => {
          setIsRecording(false);
        });

        wavesurferRecorded.on("pause", () => {
          setAudioStatus("play");
          clearInterval(intervalId);
        });
        wavesurferRecorded.on("play", () => {
          setAudioStatus("Pause");
        });
        wavesurferRecorded.on("timeupdate", () => {
          setRecordingTime(wavesurferRecorded?.getCurrentTime());
        });
        // setTimeout(() => {
        //   setIsRecording(false)

        // }, 100);
      }

      // wavesurferRecorded.on("seeking", (progress) => {
      //   if (progress > 0) {
      //     // console.log(String(progress).split('.')[1].slice(0,3) )
      //     // console.log(progress)
      //     console.log(Number(String(progress).split(".")[1].slice(0, 3)),progress)
      //     setTest(1000 - Number(String(progress).split(".")[1].slice(0, 3)));
      //   }
      //   // Récupérez la position en seconde où vous avez cliqué
      //   setRecordingTime(Math.floor(progress));

      //   // startRecordingTime();
      // });

      // const link = container.appendChild(document.createElement("a"));
      // Object.assign(link, {
      //   href: recordedUrl,
      //   download:
      //     "recording." + blob.type.split(";")[0].split("/")[1] || "webm",
      //   textContent: "Download recording",
      // });
    });

    recordPlugin.on("record-progress", (time) => {
      updateProgress(time);
    });
  };

  const destroyWaveSurfer = () => {
    if (wavesurfer) {
      wavesurfer.destroy();

      setWaveSurfer(null);
    }
  };

  function formatTime(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;
    const formattedHours = String(hours).padStart(2, "0");
    const formattedMinutes = String(minutes).padStart(2, "0");
    const formattedSeconds = String(remainingSeconds).padStart(2, "0");
    return seconds === 0
      ? "00:00"
      : `${formattedMinutes}:${
          formattedSeconds.split(".")[0] < 10
            ? 0 + formattedSeconds.split(".")[0]
            : formattedSeconds.split(".")[0]
        }`;
  }
  const deleteRecord = () => {
    setOpenRecord(false);

    setRecordingTime(0);
    setIsRecording(false);
    setDeleteRecording(true);
    setAudioStatus("play");
    setRecordStatus("pause");
    if (isFullURL) {
      setRecordedBlobNotes(null);
    }

    // setOpenRecord(false)

    // close();

    // record.pauseRecording();

    record.stopRecording();
    setTimeout(() => {
      setRecordingTime(0);

      recordingsRef.current.innerHTML = "";
      progressRef.current.textContent = "";
      setRecordBlob(null);
    }, 1);

    close();

    // recordingsRef.current.innerHTML = '';
    // setIsRecording(false);

    // setWaveSurfer(null);
    // setRecord(null)
    // record.stopRecording();
  };
  const deleteRecordAfterSend = () => {
    setRecordingTime(0);
    setIsRecording(false);
    setDeleteRecording(true);
    setRecordBlob(null);

    // setAudioStatus("play")
    // record.pauseRecording();
    setOpenRecord(false);
    if (isFullURL) {
      setRecordedBlobNotes(null);
    }

    close();

    setRecordStatus("pause");

    record.stopRecording();
    // setTimeout(() => {

    recordingsRef.current.innerHTML = "";
    progressRef.current.textContent = "";
    createWaveSurfer();

    destroyWaveSurfer();
    setRecordingTime(0);
    // }, 1);

    setSendMessage(false);
    setSendMessageBeforeStop(false);
    close();

    // recordingsRef.current.innerHTML = '';
    // setIsRecording(false);

    // setWaveSurfer(null);
    // setRecord(null)
    // record.stopRecording();
  };

  const updateProgress = (time) => {
    const formattedTime = [
      Math.floor((time % 3600000) / 60000), // minutes
      Math.floor((time % 60000) / 1000), // seconds
    ]
      .map((v) => (v < 10 ? "0" + v : v))
      .join(":");
    setFullTime(formattedTime);
    if (progressRef.current) progressRef.current.textContent = formattedTime;
  };

  const handlePauseButtonClick = () => {
    if (record.isPaused()) {
      record.resumeRecording();
      setRecordStatus("pause");

      // pauseButtonRef.current.textContent = "Pause";
      return;
    }

    record.pauseRecording();
    setRecordStatus("play");

    // pauseButtonRef.current.textContent = "Resume";
  };

  const handleMicSelectChange = (e) => {
    const deviceId = e.target.value;
    record.startRecording({ deviceId }).then(() => {
      // recButtonRef.current.textContent = "Stop";
      setIsRecording(true);
    });
  };

  const handleRecordButtonClick = () => {
    const deviceId = micRef.current.value;
    if (isRecording || record.isPaused()) {
      record.stopRecording();
      // setIsRecording(false);
      return;
    }

    // recButtonRef.current.disabled = true;

    record.startRecording({ deviceId }).then(() => {
      //   recButtonRef.current.textContent = "Stop";
      //   recButtonRef.current.disabled = false;
      //   pauseButtonRef.current.style.display = "inline";
      // setTimeout(() => {
      setIsRecording(true);

      // }, 300);
    });
  };

  const handleScrollingWaveformChange = (e) => {
    setScrollingWaveform(e.target.checked);
    createWaveSurfer();
  };
  return (
    <div className={`app-container z-20 flex h-[40px] items-center `}>
      {!isLoading && !sendMessageBeforeStop ? (
        <div
          className={`flex items-center ${
            open ? "w-full gap-2" : "w-0 gap-0"
          } `}
        >
          {/* <button ref={recButtonRef} onClick={handleRecordButtonClick} className={`${!isRecording?"visible":"invisible"}`}>
        Record
      </button> */}
          <div
            style={{
              display: isRecording && !sendMessageBeforeStop ? "block" : "none",
              width: open && !sendMessageBeforeStop ? "auto" : " 0",
            }}
          >
            <FiMic
              className="animate-blink text-red-600"
              style={{ fontSize: "18px" }}
            />
          </div>
          {isRecording ? (
            <Button
              type="text"
              size="default"
              shape="circle"
              style={{ display: isRecording ? "block" : "none" }}
              className={`group`}
              onClick={handleRecordButtonClick}
              icon={
                <FaRegStopCircle
                  className="hover:text-orange-600 group-hover:text-orange-600"
                  style={{
                    fontSize: "18px",
                  }}
                />

                // <FiStopCircle
                //   className="hover:text-orange-600 group-hover:text-orange-600"
                //   style={{
                //     fontSize: "18px",
                //   }}
                // />
              }
            />
          ) : (
            <Button
              type="text"
              size="default"
              shape="circle"
              style={{
                display:
                  recordBlob && !sendMessageBeforeStop ? "block" : "none",
              }}
              className={`${open ? " w-auto" : " w-0"} group`}
              onClick={() => wavesurferRecorded.playPause()}
              icon={
                audioStatus === "play" ? (
                  <FaRegPlayCircle
                    className=" text-gray-600 hover:text-green-600 group-hover:text-green-600"
                    style={{
                      fontSize: "18px",
                    }}
                  />
                ) : (
                  <FaRegPauseCircle
                    className="hover:text-blue-600 group-hover:text-blue-600"
                    style={{
                      fontSize: "18px",
                    }}
                  />
                )
              }
            />
          )}
          <Button
            type="text"
            size="default"
            shape="circle"
            style={{
              display: isRecording && !sendMessageBeforeStop ? "block" : "none",
            }}
            className={`group`}
            onClick={handlePauseButtonClick}
            icon={
              recordStatus === "play" ? (
                <FaRegPlayCircle
                  className="hover:text-green-600 group-hover:text-green-600"
                  style={{
                    fontSize: "18px",
                  }}
                />
              ) : (
                <FaRegPauseCircle
                  className="hover:text-blue-600 group-hover:text-blue-600"
                  style={{
                    fontSize: "18px",
                  }}
                />
              )
            }
          />
          <p
            ref={progressRef}
            className={`${
              isRecording && !sendMessageBeforeStop
                ? "visible"
                : "invisible w-0"
            }`}
          >
            {/* 00:00 */}
          </p>

          <div
            ref={micRef}
            style={{
              width:
                isRecording && !openDrawerChat
                  ? "250px"
                  : openDrawerChat && isRecording
                  ? "150px"
                  : 0,
              //   marginTop: "1rem",
            }}
          ></div>
          <div
            ref={recordingsRef}
            style={{
              width:
                recordBlob && !sendMessageBeforeStop && !isRecording
                  ? "250px"
                  : 0,
              opacity:
                recordBlob && !sendMessageBeforeStop && !isRecording ? 1 : 0,
              height: "40px",
              minHeight: "40px",
              maxHeight: "40px",
            }}
            className={`${
              recordBlob && !sendMessageBeforeStop && !isRecording
                ? "transition-opacity"
                : ""
            }`}
          ></div>

          {recordBlob && !sendMessageBeforeStop && !isRecording ? (
            <>
              <span className="w-[90px] ">
                {formatTime(recordingTime)} / {fullTime}
              </span>
            </>
          ) : null}
          <div className={`${isRecording ? "order-last" : "order-first"} `}>
            {" "}
            {open ? (
              <Button
                type="text"
                size="default"
                shape="circle"
                style={{
                  display:
                    (isRecording || recordBlob) && !sendMessageBeforeStop
                      ? "block"
                      : "none",
                }}
                danger
                onClick={deleteRecord}
                icon={<FiTrash2 style={{ fontSize: "18px" }} />}
              />
            ) : null}
          </div>
        </div>
      ) : null}
      {open && !isFullURL ? (
        <>
          {isLoading || sendMessageBeforeStop
            ? t("chat.loading") + "..."
            : !sendMessageBeforeStop && (
                <Button
                  id="send"
                  ref={sendButtonRef}
                  type="link"
                  icon={<SendOutlined />}
                  onClick={() => {
                    if (isRecording) {
                      setSendMessageBeforeStop(true);
                      setSendMessage(true);
                      handleRecordButtonClick();
                    } else {
                      if (recordBlob) {
                        sendMessage();
                        // setTimeout(() => {
                        // deleteRecord();
                        // }, 5000);
                      } else {
                        setIsRecording(false);
                        handleRecordButtonClick();
                        setSendMessage(true);
                      }
                    }
                  }}
                  // disabled={!recordBlob && isRecording}
                  loading={isLoading}
                />
              )}
        </>
      ) : null}
    </div>
  );
};

export default memo(RecordComponent);
