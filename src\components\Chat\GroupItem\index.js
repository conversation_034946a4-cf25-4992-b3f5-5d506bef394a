import React from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  EllipsisOutlined,
  MessageOutlined,
  MoreOutlined,
  QuestionCircleOutlined,
} from "@ant-design/icons";
import { Bad<PERSON>, <PERSON><PERSON>, List, Typography, Popconfirm, Dropdown } from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import MainService from "../../../services/main.service";
import {
  resetStateOtherUser,
  setChatSelectedParticipants,
} from "../../../new-redux/actions/chat.actions";
import {
  callApi,
  updateRoomInfosChat,
} from "../../../new-redux/services/chat.services";
import { toastNotification } from "../../ToastNotification";
import AvatarChat from "../Avatar/AvatarChat";
import {
  getOrGenerateTabId,
  getName,
} from "../../../pages/layouts/chat/utils/ConversationUtils";
import { HiOutlineTrash } from "react-icons/hi2";
import { HiOutlinePhone, HiOutlineUser } from "react-icons/hi";
import { URL_ENV } from "index";
import { isGuestConnected } from "utils/role";

const { Text } = Typography;

const GroupItemChat = ({ item, updateRoom, setSelected, selected }) => {
  const currentUser = useSelector((state) => state.chat.currentUser);
  const sphereUserRoler = useSelector((state) => state?.user?.user?.role);
  const onlineUser = useSelector((state) => state.ChatRealTime.onlineUser);
  const selectedConversation = useSelector(
    (state) => state.ChatRealTime.selectedConversation
  );

  const [confirmLoading, setConfirmLoading] = useState(false);
  const [loadingAdmin, setLoadingAdmin] = useState(false);

  const [openDropDown, setOpenDropDown] = useState({
    state: false,
    type: "",
  });
  const [clicked, setClicked] = useState(false);
  // const [itemPosition, setItemPosition] = useState(null);

  const dispatch = useDispatch();
  const [t] = useTranslation("common");
  //     useEffect(()=>{
  //       if(!openDropDown)
  // setOpen(false)
  //     },[openDropDown])
  const showPopconfirm = () => {
    setOpenDropDown((p) => ({
      ...p,
      type: "delete",
    }));
  };
  const handleOk = async (id, type) => {
    if (type === "delete") {
      setConfirmLoading(true);
      try {
        const response = await MainService.removeMembreFromRoom({
          room_id: selectedConversation?.id,
          user_id: id,
          tab_id: getOrGenerateTabId(),
        });

        updateRoom(id, "removeMembersTogroup", response.data.message_system_id);

        setSelected({ state: false, item: null, clientY: null });

        toastNotification(
          "success",
          t("chat.delete.delete_member"),
          "topRight"
        );
      } catch (err) {
        console.log(err);
        setConfirmLoading(false);
      }
    } else if (type === "admin") {
      try {
        setLoadingAdmin(true);
        const response = await dispatch(
          updateRoomInfosChat({
            id: selectedConversation?.id,
            new_admin_id: id,
            name: selectedConversation?.name,
            description: selectedConversation?.description,
            image: selectedConversation?.image,
          })
        );
        updateRoom(null, "admin", response.message_system_id);
        setOpenDropDown((p) => ({
          ...p,
          type: "",
        }));
        setSelected({ state: false, item: null, clientY: null });

        toastNotification(
          "success",
          t("chat.message_system.designateAdmin"),
          "topRight"
        );
      } catch (error) {
        toastNotification("error", t("toasts.errorFetchApi"), "topRight");
        setLoadingAdmin(false);
      }
    }
  };
  const handleCancel = (e) => {
    e.stopPropagation();
    setOpenDropDown((p) => ({
      ...p,
      type: "",
    }));
    setSelected({ state: false, item: null, clientY: null });
  };

  const itemsSubMenu = [
    {
      key: "00",
      label: t("chat.action.sendMessage"),
      icon: <MessageOutlined style={{ fontSize: "16px" }} />,
      show: "1",
      onClick: () => {
        dispatch(
          resetStateOtherUser({
            forced: false,
            keepDrawerOpened: false,
            item: {
              _id: item._id,
              type: "user",
            },
          })
        );

        dispatch(
          setChatSelectedParticipants({
            selectedParticipants: [
              {
                email: item.email,

                name: item.name,

                description: null,

                image: item.image,

                admin_id: null,

                bot: null,
                uuid: item.uuid,
                _id: item._id,

                post_number: item.post_number,

                role: item.role,

                type: "user",

                created_at: item.created_at,
              },
              currentUser,
            ],
          })
        );
      },
    },
    {
      key: "01",
      label: t("chat.action.call"),
      icon: <HiOutlinePhone style={{ fontSize: "18px" }} />,
      show: "1",
      onClick: () => {
        dispatch(
          callApi({
            setClicked: setClicked,
            post_numberR: item?.post_number,
            errorText: t("toasts.errorFetchApi"),
          })
        );
      },
      disabled: clicked || !currentUser?.post_number,
    },
    {
      key: "02",
      label: t("chat.action.newAdmin"),
      icon: (
        <Popconfirm
          title={t("chat.action.designateAdmin")}
          description={t("chat.error_message.designateAdmin")}
          open={openDropDown.state && openDropDown.type === "admin"}
          // onOpenChange={(e)=>setOpen(e)}
          onConfirm={() => handleOk(item._id, "admin")}
          okButtonProps={{
            loading: loadingAdmin,
          }}
          icon={<QuestionCircleOutlined style={{ color: "blue" }} />}
          onCancel={handleCancel}
          trigger={["click"]}
        >
          <HiOutlineUser style={{ fontSize: "18px" }} />
        </Popconfirm>
      ),
      show: selectedConversation?.admin_id === currentUser?._id ? "1" : "0",
    },
    {
      type: "divider",
      show: selectedConversation?.admin_id === currentUser?._id ? "1" : "0",
    },
    {
      key: "03",
      label: t("chat.delete.deleteMember"),
      icon: (
        <Popconfirm
          title={t("chat.delete.deleteMember") + " " + item.name}
          description={t("chat.error_message.delete_error_member")}
          open={openDropDown.state && openDropDown.type === "delete"}
          // onOpenChange={(e)=>setOpen(e)}
          onConfirm={() => handleOk(item._id, "delete")}
          okButtonProps={{
            loading: confirmLoading,
            danger: true,
          }}
          icon={<QuestionCircleOutlined style={{ color: "red" }} />}
          onCancel={handleCancel}
          trigger={["click"]}
        >
          <HiOutlineTrash style={{ fontSize: "18px" }} />
        </Popconfirm>
      ),
      danger: true,

      show: selectedConversation?.admin_id === currentUser?._id ? "1" : "0",
    },
  ];
  // const getPopupContainer = (trigger) => {
  //   // Vérifier si le trigger est contenu dans la div parent
  //   if (
  //     trigger &&
  //     trigger.parentNode &&
  //     trigger.parentNode.className === "scrollableDiv"
  //   ) {
  //     return trigger.parentNode;
  //   }
  //   return document.body;
  // };
  return (
    <div
      className={openDropDown.state ? "bg-slate-200 " : "group "}
      key={item._id}
    >
      <List.Item
        key={item.email}
        className={openDropDown.state ? "openDropDown " : "notOpenDropDown "}
        id={selected.item === item._id ? "list-item-2" : ""}
      >
        <List.Item.Meta
          avatar={
            <Badge
              dot
              color={
                onlineUser[item?.uuid] === "away"
                  ? "orange"
                  : onlineUser[item?.uuid] === "busy"
                  ? "red"
                  : onlineUser[item?.uuid] === "online"
                  ? "green"
                  : "#a6a6a6"
              }
              offset={[-5, 30]}
            >
              <AvatarChat
                fontSize="0.75rem"
                type="user"
                hasImage={item?.image}
                size={32}
                height={8}
                width={8}
                url={`${
                  URL_ENV?.REACT_APP_BASE_URL +
                  URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                }${item.image}`}
                name={getName(item?.name, "avatar")}
              />
            </Badge>
          }
          title={getName(item?.name, "name")}
        />
        <div className="flex items-center space-x-1 pr-1">
          <Text type="secondary" className="block text-xs  group-hover:block">
            {selectedConversation?.admin_id === item._id ? t("chat.admin") : ""}
          </Text>
          <div
            className={`${
              selected.state &&
              item?._id === selected.item &&
              currentUser?._id !== item?._id
                ? "block "
                : (selected.state && item?._id !== selected.item) ||
                  currentUser?._id === item?._id
                ? "hidden"
                : "hidden group-hover:block"
            }`}
          >
            {!isGuestConnected(currentUser, sphereUserRoler) && (
              <Dropdown
                trigger={["click"]}
                onOpenChange={(e) => {
                  setOpenDropDown((p) => {
                    if (p.type) return { ...p };
                    else
                      return {
                        state: e,
                        type: !e ? "" : p.type,
                      };
                  });
                  setSelected((prev) => {
                    return {
                      state: e,
                      item: e ? item._id : null,
                      clientY: e ? prev.clientY : null,
                    };
                  });
                }}
                //  getPopupContainer={getPopupContainer}
                //              getPopupContainer={() => document.getElementById("list-item-2")}
                overlayStyle={{ minWidth: "180px" }}
                placement="bottomRight"
                arrow={{
                  pointAtCenter: true,
                }}
                open={openDropDown.state}
                autoAdjustOverflow
                menu={{
                  items: itemsSubMenu.filter((el) => el.show === "1"),
                  // className: ` top-[${selected.clientY-200}px]` ,

                  onClick: (e) => {
                    if (e.key === "03") {
                      showPopconfirm(item);
                    } else if (e.key === "02") {
                      setOpenDropDown((p) => ({
                        ...p,
                        type: "admin",
                      }));
                    }
                  },
                }}
              >
                <Button
                  type="text"
                  size="small"
                  shape="circle"
                  className="text-slate-500"
                  icon={<MoreOutlined />}
                  onChange={(e) => {
                    setSelected((prev) => ({
                      state: e,
                      item: item._id,
                      clientY: e.clientY,
                    }));
                  }}
                />
              </Dropdown>
            )}

            {/* :""} */}
          </div>
        </div>
      </List.Item>
    </div>
  );
};

export default GroupItemChat;
