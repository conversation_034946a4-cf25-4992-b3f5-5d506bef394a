import { memo, useEffect, useMemo, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import {
  RESET_DRAFT,
  SET_DRAFT,
  SET_EMAIL_PROPS,
  SET_SENDING_MAIL,
} from "new-redux/constants";
import { toastNotification } from "components/ToastNotification";
import { Paperclip, Settings } from "lucide-react";
import { useTranslation } from "react-i18next";
import { HiOutlineEnvelope } from "react-icons/hi2";
import { LiaPenAltSolid } from "react-icons/lia";
import { RiMailSettingsLine } from "react-icons/ri";
import {
  setOpenModalEmail,
  setRefreshMailInbox,
} from "new-redux/actions/mail.actions";
import DisplayAvatar from "pages/voip/components/DisplayAvatar";
import MainService from "services/main.service";
import i18n from "translations/i18n";

const {
  CloseOutlined,
  CloseCircleOutlined,
  DeleteOutlined,
} = require("@ant-design/icons");
const {
  Tag,
  Select,
  Form,
  Tooltip,
  Upload,
  Button,
  Badge,
  message,
  Dropdown,
  Modal,
  Progress,
} = require("antd");

export const tagRender = (props) => {
  const { label, value, closable, onClose } = props;
  const onPreventMouseDown = (event) => {
    event.preventDefault();
    event.stopPropagation();
  };
  return (
    <Tag
      //   bordered={false}
      onMouseDown={onPreventMouseDown}
      closable={closable}
      onClose={onClose}
      style={{ marginInlineEnd: 4, borderRadius: 6 }}
      closeIcon={<CloseOutlined style={{ fontSize: 12 }} />}
    >
      <span className="text-sm">{label}</span>
    </Tag>
  );
};
/////////////////////////////////////////
export const SelectWithPrefix = memo(({ prefix, suffix, ...props }) => (
  <div className="flex items-center rounded border px-2">
    {prefix}

    <Select
      {...props}
      className="flex-1"
      popupMatchSelectWidth
      style={{ border: "none", boxShadow: "none" }}
    />

    {suffix ?? null}
  </div>
));
/////////////////////////////////////////
export const FormItem = ({ children, ...props }) => (
  <Form.Item {...props} style={{ marginBottom: 0 }}>
    {children}
  </Form.Item>
);
/////////////////////////////////////////
export const formRules = (label, t) => [
  {
    required: true,
    message: t("contacts.fieldXRequired", {
      x: label,
    }),
  },
];

/////////////////////////////////////////
const formatBytes = (bytes, decimals = 2) => {
  if (bytes === 0) return "0 B";
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes =
    i18n.language === "en"
      ? ["B", "KB", "MB", "GB", "TB"]
      : ["octets", "Ko", "Mo", "Go", "To"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + " " + sizes[i];
};
/////////////////////////////////////////
export const refactorDataAccounts = (dataAccounts) => {
  if (!dataAccounts?.length) return [];
  const result = [];
  dataAccounts.forEach(({ alias, label, value, default_signature }) => {
    if (alias.length) {
      alias.forEach((item) => {
        result.push({
          label: (
            <div className="flex items-center space-x-2">
              <span>{item.alias}</span>
              <span className="text-slate-500">{`<<${label}>>`}</span>
            </div>
          ),
          value: item.alias,
          accountId: value,
          accountLabel: item.alias,
          signature: item?.Signature,
        });
      });
    }
    result.push({
      label: label,
      value: label,
      accountId: value,
      signature: default_signature?.value,
      accountLabel: label,
      // accountId: value,
    });
  });
  // console.log({ result });
  return result;
};
/////////////////////////////////////////
export const BubbleEmail = memo(({ setIsMinimize, onClose }) => {
  //
  const isChatDrawerOpen = useSelector(({ voip }) => voip.openDrawerChat);

  const [isHovering, setIsHovering] = useState(false);

  return (
    <div
      key="bubble-email"
      className="bubble-email flex cursor-pointer flex-row rounded-full bg-slate-100 p-[3px] shadow-2xl drop-shadow-xl"
      style={{
        position: "fixed",
        bottom: 50,
        right: isChatDrawerOpen ? 120 : 45,
        // right: 45,
        zIndex: 9999,
      }}
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
      onClick={() => setIsMinimize(false)}
    >
      <Badge
        offset={[0, 0]}
        overflowCount={10}
        count={
          isHovering ? (
            <Button
              onClick={() => onClose()}
              size="small"
              type="default"
              shape="circle"
              icon={<CloseCircleOutlined style={{ fontSize: 14 }} />}
            />
          ) : null
        }
      >
        <DisplayAvatar
          cursor="pointer"
          size={55}
          icon={<HiOutlineEnvelope style={{ fontSize: 30 }} />}
        />
      </Badge>
    </div>
  );
});
/////////////////////////////////////////
export const UploadFiles = memo(({ fileList, setFileList }) => {
  //
  const [t] = useTranslation("common");
  const disallowedFiles = useRef([]);

  //
  const allowedExtensions = [
    "pdf",
    "docx",
    "xlsx",
    "pptx",
    "txt",
    "csv",
    "jpg",
    "jpeg",
    "png",
    "svg",
    "zip",
    "ovpn",
    "mp3",
    "wav",
  ];
  //

  const handleOnChange = ({ fileList }) => {
    const withSizes = fileList.map((file) => {
      if (file.status === "done") return file;
      return {
        ...file,
        status: "done",
        size: formatBytes(file.size),
        // name: `${truncateFileName(file.name, 100)} (${formatBytes(file.size)})`,
        // name: (
        //   <div className="flex items-center space-x-1 font-semibold">
        //     <span className="max-w-[90%] truncate">{file.name}</span>
        //     <span className="text-slate-500">
        //       {`(${formatBytes(file.size)})`}
        //     </span>
        //   </div>
        // ),
      };
    });
    setFileList(withSizes);
  };

  //
  const handleBeforeUpload = (file, fileList) => {
    // const fileName = file?.name
    const fileExt = file?.name?.split(".")?.pop()?.toLowerCase();
    const isAllowed = allowedExtensions?.includes(fileExt);

    if (!isAllowed) disallowedFiles.current.push(fileExt);

    if (
      fileList.indexOf(file) === fileList.length - 1 &&
      disallowedFiles.current.length > 0
    ) {
      const uniqueExtensions = [...new Set(disallowedFiles.current)];
      message.open({
        duration: 4,
        type: "error",
        content: (
          <span
            dangerouslySetInnerHTML={{
              __html: t("mailing.notAllowedExt", {
                ext: uniqueExtensions.join(", "),
              }),
            }}
          />
        ),
      });

      disallowedFiles.current = [];
    }

    if (isAllowed) {
      handleOnChange(file);
      return isAllowed;
    } else return Upload.LIST_IGNORE;
  };
  //
  //   console.log(fileList);
  //
  const uploadProps = {
    // listType: "picture",
    multiple: true,
    showUploadList: false,
    allowedExtensions,
    fileList,
    onChange: handleOnChange,
    beforeUpload: handleBeforeUpload,
  };
  //
  return (
    <Tooltip arrow={false} placement="bottom" title="Joindre des fichiers">
      <Upload {...uploadProps}>
        <Button
          type="text"
          className="text-slate-700"
          icon={
            <Badge size="small" overflowCount={9} count={fileList.length}>
              <Paperclip
                size={16}
                style={{ transform: "rotate(-45deg)", marginTop: 3 }}
              />
            </Badge>
          }
        />
      </Upload>
    </Tooltip>
  );
});
export const UploadItemRender = (originNode, file, fileList, actions) => {
  return (
    <div
      key={file.uid}
      className="mb-0.5 flex items-center justify-between font-semibold hover:bg-slate-100"
    >
      <div className="flex items-center space-x-1">
        <Paperclip className="text-slate-500" size={12} />
        <span className="max-w-[80%] truncate">{file.name}</span>
        <span className="text-slate-500">({file.size})</span>
      </div>
      {/* you can still wire up remove if you like: */}
      <Button
        danger
        size="small"
        type="text"
        onClick={() => actions.remove(file)}
        icon={<DeleteOutlined />}
      />
    </div>
  );
};
/////////////////////////////////////////
export const SignatureDropdown = memo(({ accountId, insertSignature }) => {
  //
  const [t] = useTranslation("common");
  const navigate = useNavigate();
  //
  const [dataSignatures, setDataSignatures] = useState([]);
  const [isLoadingData, setIsLoadingData] = useState(false);
  const [selectedSignature, setSelectedSignature] = useState("empty");
  //
  useEffect(() => {
    let isMounted = true;

    const fetchSignature = async () => {
      if (!accountId) return;
      setIsLoadingData(true);
      try {
        const {
          data: { data },
        } = await MainService.getSignature(accountId);
        if (isMounted) {
          setDataSignatures(data?.length ? data : []);
          // if (data.length) {
          //   const defaultSig =
          //     data.find((sig) => sig.default)?.value || "empty";
          //   setSelectedSignature(defaultSig);
          //   insertSignature(defaultSig);
          // }
        }
      } catch (err) {
        if (isMounted) {
          toastNotification("error", t("toasts.somethingWrong"), "topRight");
        }
      } finally {
        setIsLoadingData(false);
      }
    };

    fetchSignature();

    return () => {
      isMounted = false;
    };
  }, [accountId, t]);
  //
  const handleChangeSignature = (sig) => {
    setSelectedSignature(sig);
    if (sig === "empty" || sig === "navigate-to") return;
    insertSignature(sig);
  };

  //

  //
  const dropdownItems = useMemo(
    () => [
      ...(dataSignatures?.length
        ? dataSignatures.map((item) => ({
            label: `${item?.label}`,
            key: item.value,
          }))
        : [
            {
              label: `Aucune signature`,
              key: "empty",
              disabled: true,
            },
          ]),
      {
        type: "divider",
      },
      {
        label: "Gérer les signatures",
        key: "navigate-to",
        icon: <Settings size={14} />,
        onClick: () => navigate("/profile/signature"),
      },
    ],
    [dataSignatures, navigate]
  );
  //
  // console.log({ dropdownItems });
  //
  return (
    <Tooltip arrow={false} placement="bottom" title="Insérer une signature">
      <Dropdown
        placement="topLeft"
        trigger={["click"]}
        menu={{
          items: dropdownItems,
          selectable: true,
          selectedKeys: selectedSignature,
          onClick: ({ key }) => handleChangeSignature(key),
        }}
      >
        <Button
          type="text"
          className="text-slate-700"
          loading={isLoadingData}
          onClick={(e) => e.preventDefault()}
          icon={<LiaPenAltSolid size={21} style={{ marginTop: 1 }} />}
        />
      </Dropdown>
    </Tooltip>
  );
});
////////////////////////////////////////
export const TemplateDropdown = memo(({ insertTemplate }) => {
  const [t] = useTranslation("common");
  const navigate = useNavigate();

  const [dataTemplate, setDataTemplate] = useState([]);
  const [isLoadingData, setIsLoadingData] = useState(false);
  const [selectedKey, setSelectedKey] = useState("empty");

  // Modal state
  const [modalVisible, setModalVisible] = useState(false);
  const [pendingMailing, setPendingMailing] = useState({ title: "", body: "" });

  // Fetch mailings grouped by family
  useEffect(() => {
    let isMounted = true;

    async function fetchTemplate() {
      setIsLoadingData(true);
      try {
        const {
          data: { data },
        } = await MainService.folderByFamily();
        if (!isMounted) return;

        const filtered = Array.isArray(data)
          ? data.filter((item) => {
              const arr = Object.values(item)[0];
              return Array.isArray(arr) && arr.length > 0;
            })
          : [];

        setDataTemplate(filtered);
      } catch (err) {
        if (isMounted) {
          toastNotification("error", t("toasts.somethingWrong"), "topRight");
        }
      } finally {
        setIsLoadingData(false);
      }
    }

    fetchTemplate();

    return () => {
      isMounted = false;
    };
  }, [t]);

  //
  const dropdownItems = useMemo(() => {
    const groups = dataTemplate.map((item) => {
      const family = Object.keys(item)[0];
      const mailings = Object.values(item)[0];
      return {
        key: family,
        label: family,
        type: "group",
        children: mailings.map((m) => ({
          key: m.bodymail,
          label: m.title,
          data: { body: m.bodymail, title: m.title },
        })),
      };
    });

    return [
      ...(groups.length
        ? groups
        : [{ label: t("mailing.noMailings"), key: "empty", disabled: true }]),
      { type: "divider" },
      {
        label: t("mailing.manageMailings"),
        key: "navigate-to",
        icon: <Settings size={14} />,
        onClick: () => navigate("/settings/emailTemplates"),
      },
    ];
  }, [dataTemplate, navigate, t]);

  // console.log({ dropdownItems });

  const handleMenuClick = ({ key, item }) => {
    if (key === "empty" || key === "navigate-to") return;
    const { title, body } = item.props.data;
    setPendingMailing({ title, body });
    setModalVisible(true);
  };

  const onClearAndInsert = () => {
    insertTemplate(pendingMailing.body, "clearAndInsert");
    setSelectedKey(pendingMailing.body);
    setModalVisible(false);
  };
  const onAppend = () => {
    insertTemplate(pendingMailing.body, "insert");
    setSelectedKey(pendingMailing.body);
    setModalVisible(false);
  };

  return (
    <>
      <Tooltip
        arrow={false}
        placement="bottom"
        title={t("mailing.selectMailing")}
      >
        <Dropdown
          placement="topLeft"
          trigger={["click"]}
          menu={{
            items: dropdownItems,
            selectable: true,
            selectedKeys: selectedKey ? [selectedKey] : [],
            onClick: handleMenuClick,
          }}
        >
          <Button
            type="text"
            className="text-slate-700"
            loading={isLoadingData}
            onClick={(e) => e.preventDefault()}
            icon={<RiMailSettingsLine size={18} />}
          />
        </Dropdown>
      </Tooltip>

      <Modal
        open={modalVisible}
        title={t("mailing.insertMailing")}
        onCancel={() => setModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setModalVisible(false)}>
            {t("voip.cancel")}
          </Button>,
          <Button key="append" onClick={onAppend}>
            {t("mailing.append")}
          </Button>,
          <Button key="clear" type="primary" onClick={onClearAndInsert}>
            {t("mailing.clearAndInsert")}
          </Button>,
        ]}
      >
        <p>{t("mailing.insertPrompt")}</p>
        <div className="mt-2 max-h-80 overflow-auto rounded border bg-gray-50 p-2">
          {/* Preview of the mailing */}
          <div dangerouslySetInnerHTML={{ __html: pendingMailing.body }} />
        </div>
      </Modal>
    </>
  );
});
//////////////////////////////////////

export const setSendingMail = (payload) => (dispatch) => {
  dispatch({
    type: SET_SENDING_MAIL,
    payload,
  });
};

export const setEmailFields =
  (payload, open = true) =>
  (dispatch) => {
    /*
   payload need to be an object, fill fields that you need to be filled:
    - sender    : String(email) => required
    - receivers : [] array of emails => required 
    - subject   : string 
    - message   : string || html
    - contactId : id
    - familyId  : id
    */
    dispatch({
      type: SET_EMAIL_PROPS,
      payload,
    });
    open && dispatch(setOpenModalEmail(true));
  };
//////////////////////////////////////
export const SendingMailProgress = memo(({ data }) => {
  //
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  //
  const { dataAccounts } = useSelector((state) => state.mailReducer);

  const usedAccount = dataAccounts.find((e) => e.selected);
  //
  const [percent, setPercent] = useState(10);
  //
  const handleSendingMail = async () => {
    // setLoadingSubmit(true);
    const {
      sender = "",
      receivers = [],
      cc = [],
      cci = [],
      subject = "",
      editorContent = "",
      fileList = [],
      senderId = null,
      contactId = null,
      familyId = null,
    } = data;
    try {
      const formData = new FormData();
      formData.append("from", sender);
      formData.append("account_id", senderId);
      formData.append("subject", subject);
      formData.append("paramLog", 0);
      formData.append("body", editorContent);
      fileList.forEach((file) =>
        formData.append("attachments[]", file.originFileObj)
      );
      receivers.forEach((rec) => formData.append("receiver[]", rec));
      cc.forEach((e) => formData.append("cc[]", e));
      cci.forEach((e) => formData.append("cci[]", e));

      if (contactId && familyId) {
        formData.append("id_element", contactId);
        formData.append("family_id", familyId);
      }

      await MainService.PostMail(formData);
      dispatch(resetDraft());
      dispatch(setOpenModalEmail(false));
      Number(usedAccount?.value) === Number(senderId.current) &&
        dispatch(setRefreshMailInbox(true));
      toastNotification(
        "success",
        "Votre email a été envoyé avec succés",
        "topRight",
        3
      );
    } catch (err) {
      toastNotification(
        "error",
        err?.response?.data
          ? err?.response?.data?.message
          : t("toasts.somethingWrong"),
        "topRight"
      );
      dispatch(setSendingMail(false));
      dispatch(setOpenModalEmail(true));
      throw new Error(err?.message ? `error: ${err.message}` : { err });
    } finally {
      setPercent(100);
      const timer = setTimeout(() => {
        dispatch(setSendingMail(false));
        return () => clearTimeout(timer);
      }, 500);
    }
  };
  //
  useEffect(() => {
    handleSendingMail();
    const id = setInterval(() => {
      setPercent(
        (prev) =>
          prev +
          (prev < 50 ? 10 : prev >= 50 && prev < 80 ? 5 : prev > 95 ? 0 : 0)
      );
    }, 1500);

    return () => clearInterval(id);
  }, []);
  //
  const tooltipContent = (
    <div className="flex flex-grow items-center space-x-3">
      <span className="flex-shrink-0 whitespace-nowrap font-semibold">
        {t("mailing.sendingMail")}
      </span>
      <div className="w-52">
        <Progress
          success={{ percent: percent }}
          // percent={percent}
          showInfo={false}
          size={[200, 8]}
          trailColor="#E6F4FF"
        />
      </div>
    </div>
  );
  //
  return (
    <div
      className=" p-2"
      style={{
        position: "fixed",
        bottom: 10,
        left: 125,
        zIndex: 9999,
      }}
    >
      <Tooltip
        arrow={false}
        title={tooltipContent}
        open={true}
        placement="right"
        overlayInnerStyle={{
          width: "max-content",
          // padding: "12px",
          // borderRadius: "8px",
        }}
        // color="rgb(15 23 42)"
      >
        <div />
      </Tooltip>
    </div>
  );
});
//////////////////////////////////////
export const trackDraftData =
  ({
    id,
    sender,
    receivers,
    cc,
    cci,
    subject,
    editorContent,
    fileList,
    senderId,
  }) =>
  (dispatch) => {
    const draft = {
      sender,
      receivers,
      cc,
      cci,
      subject,
      editorContent,
      fileList,
      senderId,
    };

    const payload = { [id]: draft };

    dispatch({ type: SET_DRAFT, payload });
  };

export const resetDraft = () => (dispatch) => {
  dispatch({ type: RESET_DRAFT, payload: "newMsg" });
};
//////////////////////////
