import { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import party from "party-js";
import {
  resetStateOtherUser,
  setChatSelectedParticipants,
  setOpenDrawer,
  setSidebarDrawer,
} from "new-redux/actions/chat.actions";
import { setSearchMessageTerm } from "new-redux/actions/chat.actions/Input";
import {
  setMsgTask,
  setOpenTaskDrawer,
} from "new-redux/actions/tasks.actions/handleTaskDrawer";
import { callApi } from "new-redux/services/chat.services";
import { store } from "new-redux/store";
import { useTranslation } from "react-i18next";
import { getUserFromMsg } from "../utils/ConversationUtils";
import { SET_DATE_CONVERSATION_TO_TASK } from "new-redux/constants";

function useOnClickMessage() {
  const dispatch = useDispatch();
  const { currentUser, sidebarDrawer } = useSelector((state) => state.chat);
  const { t } = useTranslation("common");
  useEffect(() => {
    let time,
      mounted = true;
    const goToUser = async (user_id) => {
      if (!mounted) return;
      const archivedListIds = await store.getState().chat.archivedListIds;

      const isArchivedDiscussion = archivedListIds.find(
        (item) => item.receiver_id === user_id
      );

      if (isArchivedDiscussion && sidebarDrawer !== "archive") {
        dispatch(setSidebarDrawer("archive"));
      } else if (!isArchivedDiscussion && sidebarDrawer === "archive") {
        dispatch(setSidebarDrawer("chat"));
      }
      time = setTimeout(async () => {
        dispatch(
          resetStateOtherUser({
            forced: true,
            keepDrawerOpened: false,
            item: {
              _id: user_id,
              type: "user",
            },
          })
        );

        dispatch(
          setChatSelectedParticipants({
            selectedParticipants: [getUserFromMsg(user_id), currentUser],
          })
        );
      }, 1);

      return;
    };

    const handleClick = (event) => {
      if (!mounted) return;

      const { target } = event;
      const containerChatRef = document.getElementById("chatContainer");

      // Handle click on mentions
      if (target.matches('[data-type="mention"]')) {
        const user_id = target.getAttribute("userid");

        if (Number(user_id) !== 0 && user_id !== currentUser?._id) {
          // Handle go to user logic
          goToUser(user_id);
        }
      }

      // Handle click on tags
      else if (target.matches('[aria-label="hashtag"]')) {
        dispatch(setOpenDrawer({ type: "search" }));

        dispatch(
          setSearchMessageTerm({
            value: target?.innerHTML,
            forced: Math.floor(Math.random() * 1000 + 1),
          })
        );
      } else if (target.matches('[aria-label="phone-number"]')) {
        const phoneNumber = target.getAttribute("number");
        if (currentUser?.post_number)
          dispatch(
            callApi({
              post_numberR: phoneNumber,
              errorText: t("toasts.errorFetchApi"),
            })
          );
      } else if (target.matches('[aria-label="date"]')) {
        const date = target.getAttribute("date");
        window.dispatchEvent(
          new CustomEvent(SET_DATE_CONVERSATION_TO_TASK, {
            detail: {
              date,
            },
          })
        );

        dispatch(setOpenTaskDrawer(true));
      } else if (target.matches('[aria-label="birthday"]')) {
        party.confetti(containerChatRef, {
          count: party.variation.range(60, 60),
        });
      }
    };
    const handleHover = (event) => {
      if (!mounted) return;

      const { target } = event;

      // Handle hover on mentions
      if (target.matches('[data-type="mention"]')) {
        const user_id = target.getAttribute("userid");
        if (Number(user_id) !== 0 && user_id !== currentUser?._id) {
          target.classList.add("mentionHover");
        }
      }
    };

    const handleLeave = (event) => {
      if (!mounted) return;

      const { target } = event;

      // Handle mouse leave on mentions
      if (target.matches('[data-type="mention"]')) {
        target.classList.remove("mentionHover");
      }
    };
    // Attach the event listener to a parent element
    document.addEventListener("click", handleClick);
    document.addEventListener("mouseover", handleHover);
    document.addEventListener("mouseout", handleLeave);

    // Clean up the event listener
    return () => {
      dispatch(setMsgTask(""));
      clearTimeout(time);
      mounted = false;
      document.removeEventListener("click", handleClick);
      document.removeEventListener("mouseover", handleHover);
      document.removeEventListener("mouseout", handleLeave);
    };
  }, [dispatch, t, sidebarDrawer, currentUser?._id]);
  return null;
}

export default useOnClickMessage;
