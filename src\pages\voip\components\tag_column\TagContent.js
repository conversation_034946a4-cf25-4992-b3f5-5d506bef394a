import { useEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RESET_LAST_CALL_LOG } from "../../../../new-redux/constants";
import {
  Button,
  DatePicker,
  Divider,
  Form,
  Select,
  TimePicker,
  Input,
  Space,
  Popconfirm,
  Badge,
  Card,
  Tooltip,
} from "antd";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { toastNotification } from "../../../../components/ToastNotification";
import {
  createTagsActivity,
  deleteTags,
  updateTagsActivity,
} from "../../services/services";
import ChoiceIcons from "../../../components/ChoiceIcons";
import dayjs from "dayjs";
import { areArraysEqual } from "../../helpers/helpersFunc";
import { CloseOutlined, InfoCircleTwoTone } from "@ant-design/icons";
import "../../index.css";

const { Option } = Select;

/**
 * 
 * @param {*} param0 
* tags =>  for create: null & required for update [tag, tag ....] 
  id => "required": call id "_id"....
  info => not "required", can be null
  data => "required", Array of tags you can get this by "getTags" in services 
  setDataSource => setState if you need to get the return from server after submitting create/update and getting status 200
  setCatchChange => setState if you need to catch the change when status is 200 after submitting for create or update
  setOpen => state to close what you open (drawer, modal, etc) asshole 
 * 
 */

const TagContent = ({
  tags,
  id,
  info,
  data,
  setDataSource,
  setOpen,
  source,
  infoMetaTable,
}) => {
  const dispatch = useDispatch();
  const logs = useSelector(({ voip }) => voip.logs);
  const user = useSelector(({ user }) => user.user);
  const access = user.access || {};
  //
  const [t] = useTranslation("common");
  const [form] = Form.useForm();
  const [loadingSubmit, setLoadingSubmit] = useState(false);
  const [selectedOptions, setSelectedOptions] = useState([]);
  const [note, setNote] = useState("");
  // const [dateTimeTrack, setDateTimeTrack] = useState([]);
  const [disabledUpdate, setDisabledUpdate] = useState(true);
  const [formValues, setFormValues] = useState({});

  // console.log(selectedOptions);

  // const UpdateOnlyDataSource = useMemo(
  //   () =>
  //     infoMetaTable
  //       ? infoMetaTable?.page * infoMetaTable?.limit > logs.length
  //       : true,
  //   [infoMetaTable, logs.length]
  // );

  const updateTags = useMemo(
    () =>
      tags?.tags?.map(
        (tag) => `${tag?.id}${tag?.parent_id ? `-${tag?.parent_id}` : ""}`
      ) || [],
    [tags]
  );

  useEffect(() => {
    form.setFieldValue(`${id}tags`, updateTags);
  }, [form, id, updateTags]);
  //
  const datePickersToShow = useMemo(
    () => selectedOptions.filter((option) => option.value.includes("-")),
    [selectedOptions]
  );
  //
  //reset All fields if id call change
  useEffect(() => {
    form.resetFields();
    setSelectedOptions([]);
    setNote("");
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);
  //
  // reset form when user clears the selections & track update button if is disabled
  useEffect(() => {
    !selectedOptions?.length && !updateTags?.length && form.resetFields();
    if (!updateTags?.length) {
      setDisabledUpdate(false);
      return;
    }

    const selectedValues = form?.getFieldValue(`${id}tags`);
    const areSame = areArraysEqual(selectedValues, updateTags);
    if (areSame && form?.getFieldValue(`${id}note`) === (tags?.note || "")) {
      setDisabledUpdate(true);
    } else setDisabledUpdate(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedOptions, note]);
  //
  // const updateDateTimeTrack = (tag, newDate, newTime) => {
  //   setDateTimeTrack((prevState) => {
  //     const existingEntry = prevState.find((entry) => entry.tag === tag);

  //     if (!existingEntry && newDate) {
  //       return [...prevState, { tag, date: newDate, time: newTime }];
  //     }

  //     if (existingEntry) {
  //       if (newDate === null) {
  //         return prevState.filter((entry) => entry.tag !== tag);
  //       }

  //       return prevState.map((entry) =>
  //         entry.tag === tag
  //           ? {
  //               ...entry,
  //               date: newDate !== undefined ? newDate : entry.date,
  //               time: newTime !== undefined ? newTime : entry.time,
  //             }
  //           : entry
  //       );
  //     }
  //     return prevState;
  //   });
  // };
  //
  const onFinish = async (values) => {
    try {
      // console.log({ values });
      setLoadingSubmit(true);

      const formData = new FormData();
      const hasTags = tags?.tags?.length > 0;
      const name = info?.name ? info?.name : info?.number;

      formData.append("type", "call");
      if (!hasTags) {
        formData.append("id", id);
      }

      values?.[`${id}tags`]?.forEach((tag) => {
        const date = values?.[`${tag}date`]
          ? dayjs(values?.[`${tag}date`])?.format("YYYY-MM-DD")
          : "";
        const time =
          values?.[`${tag}date`] && values?.[`${tag}time`]
            ? dayjs(values?.[`${tag}time`])?.format("HH:mm")
            : "";
        const tagNote = values?.[`${tag}note`] || "";
        formData.append(`tags[${tag}]`, `${date || ""} ${time || ""}`);
        formData.append(`tags_notes[${tag}]`, tagNote);
      });

      formData.append("note", values?.[`${id}note`]);

      let response;
      if (updateTags?.length) {
        response = await updateTagsActivity(tags?._id, formData);
      } else {
        response = await createTagsActivity(formData);
      }
      response = response?.data?.data?.tags;
      if (source === "webPhone")
        handleTagCRUD(id, logs, null, response, dispatch, "add");
      else handleTagCRUD(id, null, setDataSource, response, dispatch, "add");

      form.resetFields();
      setLoadingSubmit(false);
      setOpen(false);

      const successText = hasTags
        ? t("voip.successUpdateQualify", { name: name })
        : t("voip.successCreateQualify", { name: name });

      toastNotification(
        "success",
        <div dangerouslySetInnerHTML={{ __html: successText }} />,
        "topRight",
        5
      );
      source === "webPhone" && dispatch({ type: RESET_LAST_CALL_LOG });
    } catch (err) {
      if (err?.response?.status !== 401) {
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
      setLoadingSubmit(false);
      setOpen(false);
      throw new Error(err?.message || err);
    }
  };
  //
  const confirmDeleteTags = async (tagsId) => {
    const formData = new FormData();
    formData.append("id", tagsId);
    formData.append("type", "call");
    return new Promise((resolve, reject) => {
      deleteTags(formData)
        .then(() => {
          // UpdateOnlyDataSource
          //   ?
          handleTagCRUD(id, null, setDataSource, null, dispatch, "delete");
          //   :
          // handleTagCRUD(id, logs, null, null, dispatch, "delete");

          toastNotification("success", t("voip.tagDelete"), "topRight");

          resolve();
        })
        .catch((err) => {
          err?.response?.status !== 401 &&
            toastNotification("error", t("toasts.somethingWrong"), "topRight");

          reject();
        });
    });
  };
  //
  const disabledDate = (current) =>
    current && current < moment().subtract(1, "days").endOf("day");

  const disablePastTime = (formId) => {
    const selectedDate = form.getFieldValue(formId);
    return () => {
      const current = dayjs();
      const selected = dayjs(selectedDate);

      if (selected && selected.isSame(current, "day")) {
        const currentHour = current.hour();
        const currentMinute = current.minute();

        return {
          disabledHours: () => Array.from(new Array(currentHour), (v, i) => i),
          disabledMinutes: (selectedHour) =>
            selectedHour === currentHour
              ? Array.from(new Array(currentMinute), (v, i) => i)
              : [],
        };
      }

      // If the selected date is not today, don't disable any hours or minutes
      return {};
    };
  };
  //
  // const DividerComponent = ({ label, color }) => (
  //   <Divider plain orientation="left" style={{ margin: "10px 0 5px" }}>
  //     <p className="truncate" style={{ color: color }}>
  //       {truncateString(label, 20)}
  //     </p>
  //   </Divider>
  // ); //
  return (
    <div>
      <div
        style={{
          width: source === "webPhone" ? "19.3rem" : "17rem",
          // maxHeight: source === "webPhone" ? "16.5rem" : "16.5rem",
          maxHeight: source === "webPhone" ? "20rem" : "16rem",
          overflowY: !datePickersToShow?.length && "hidden",
        }}
        className="flex flex-col space-y-2.5 overflow-y-auto  px-1 pb-1.5"
      >
        <Form
          form={form}
          onFinish={onFinish}
          scrollToFirstError={true}
          layout="vertical"
          autoComplete="off"
          style={{
            width: "100%",
          }}
          onFieldsChange={(value, allValues) => setFormValues(allValues)}
        >
          <Form.Item
            key={id}
            label={`${t("voip.qualification")}`}
            name={`${id}tags`}
            initialValue={updateTags}
            rules={[
              {
                required: true,
                message: `${t("voip.tagIsRequired", {
                  x: `${t("voip.qualification")}`,
                })}`,
              },
            ]}
            tooltip={{
              title: t("voip.qualificationInfo"),
              placement: "topLeft",
              icon: <InfoCircleTwoTone />,
            }}
          >
            <Select
              placeholder={t("voip.selectQualify")}
              allowClear
              showSearch
              popupMatchSelectWidth={false}
              mode="multiple"
              maxTagCount="responsive"
              optionLabelProp="label"
              filterOption={(input, option) =>
                option?.label.toLowerCase().includes(input.toLowerCase())
              }
              onChange={(values, options) => setSelectedOptions(options)}
            >
              {data?.map((item) => (
                <Option
                  key={item?.id}
                  value={item?.id}
                  label={item?.label}
                  color={item?.color}
                  icon={item?.icon}
                >
                  <Space>
                    <ChoiceIcons icon={item?.icon} />
                    <span style={{ color: item?.color }}>{item?.label}</span>
                    {item.id.includes("-") && (
                      <Tooltip title={t("voip.qualifContainsTask")}>
                        <Badge status="error" />
                      </Tooltip>
                    )}
                  </Space>
                </Option>
              ))}
            </Select>
          </Form.Item>
          {!updateTags?.length &&
          datePickersToShow?.length &&
          access?.activities === "1" ? (
            <div className="tag-content mt-2 flex flex-col space-y-4">
              {datePickersToShow?.map((option) => (
                <Card
                  key={`${id}-${option?.value}`}
                  size="small"
                  type="inner"
                  style={{
                    width: "98%",
                    paddingBottom: "1rem",
                    marginLeft: "1%",
                    border: `1px solid ${option.color}`,
                  }}
                  title={
                    <span style={{ color: option.color }}>{option?.label}</span>
                  }
                  extra={
                    <Tooltip title={`${t("voip.remove")} ${option?.label}`}>
                      <Button
                        danger
                        type="link"
                        size="small"
                        icon={<CloseOutlined />}
                        onClick={() => {
                          // Update selected options
                          setSelectedOptions((prevData) =>
                            prevData.filter(
                              (item) => item.value !== option.value
                            )
                          );
                          // Update form field value
                          form.setFieldValue(
                            `${id}tags`,
                            form
                              .getFieldValue(`${id}tags`)
                              .filter((item) => item !== option.value)
                          );
                        }}
                      />
                    </Tooltip>
                  }
                >
                  <div className="flex items-end space-x-1">
                    <Form.Item
                      key={`${id}-${option?.value}-date`}
                      label="Date"
                      name={`${option?.value}date`}
                      initialValue={dayjs()}
                    >
                      <DatePicker
                        allowClear={false}
                        placeholder="Select Date"
                        disabledDate={disabledDate}
                        // onChange={(date) => {
                        //   const formattedDate =
                        //     date?.format("YYYY-MM-DD") || null;
                        //   updateDateTimeTrack(
                        //     option?.value,
                        //     formattedDate,
                        //     undefined
                        //   );
                        // }}
                      />
                    </Form.Item>
                    <Form.Item
                      key={`${id}-${option?.value}-time`}
                      label="time"
                      name={`${option?.value}time`}
                      initialValue={dayjs().add(1, "hour")}
                    >
                      <TimePicker
                        allowClear={false}
                        placeholder="Select Time"
                        format={"HH:mm"}
                        minuteStep={5}
                        // onChange={(timeValue) => {
                        //   const formattedTime =
                        //     timeValue?.format("HH:mm") || null;
                        //   updateDateTimeTrack(
                        //     option?.value,
                        //     undefined,
                        //     formattedTime
                        //   );
                        // }}
                        disabledTime={disablePastTime(`${option?.value}date`)}
                      />
                    </Form.Item>
                  </div>
                  <Form.Item
                    key={`${id}-${option?.value}-note`}
                    label="Note"
                    name={`${option?.value}note`}
                    tooltip={{
                      title: (
                        <p
                          dangerouslySetInnerHTML={{
                            __html: t("voip.noteTaskInfo", {
                              taskName: option?.label,
                            }),
                          }}
                        />
                      ),
                      placement: "topLeft",
                      icon: <InfoCircleTwoTone />,
                    }}
                  >
                    <Input.TextArea
                      showCount
                      allowClear
                      autoSize={{
                        minRows: 1,
                        maxRows: 3,
                      }}
                      maxLength={200}
                      placeholder={t("voip.writeNote")}
                    />
                  </Form.Item>
                </Card>
              ))}
            </div>
          ) : null}
          <Divider style={{ margin: "20px 0px 5px" }} />
          <Form.Item
            key={`${id}note`}
            label={t("voip.qualifNote")}
            name={`${id}note`}
            initialValue={tags?.note || ""}
            tooltip={{
              title: t("voip.noteTagInfo"),
              placement: "topLeft",
              icon: <InfoCircleTwoTone />,
            }}
          >
            <Input.TextArea
              showCount
              allowClear
              autoSize={{
                minRows: 2,
                maxRows: 4,
              }}
              maxLength={300}
              placeholder={t("voip.writeNote")}
              onChange={(e) => setNote(e?.target?.value)}
            />
          </Form.Item>
        </Form>
      </div>
      <div className="flex flex-row justify-between px-2 py-4">
        {updateTags?.length ? (
          <Popconfirm
            title={t("voip.delete")}
            description={`${t("voip.areYouSureToDelete")} ${
              tags?.tags?.length > 1 ? t("voip.thoseTags") : t("voip.thisTag")
            }?`}
            onConfirm={() => confirmDeleteTags(tags?._id)}
            // onCancel={cancel}
            okText={t("voip.yes")}
            cancelText={t("voip.cancel")}
          >
            <Button size="small" type="primary" danger>
              {t("contacts.delete")}
            </Button>
          </Popconfirm>
        ) : (
          <div />
        )}
        {datePickersToShow?.length ? (
          <Button
            size="small"
            type="primary"
            loading={loadingSubmit}
            onClick={() => form.submit()}
          >
            {updateTags.length
              ? t("voip.save")
              : t("voip.save_createTask", {
                  nbr:
                    datePickersToShow?.length > 0
                      ? datePickersToShow?.length
                      : "",
                  s: datePickersToShow?.length > 1 ? "s" : "",
                })}
          </Button>
        ) : (
          <Button
            size="small"
            type="primary"
            loading={loadingSubmit}
            onClick={() => form.submit()}
            disabled={disabledUpdate}
          >
            {t("voip.save")}
          </Button>
        )}
      </div>
    </div>
  );
};

// to handle the Redux state and log state in the same time and switch between them if needed
export const handleTagCRUD = (
  idCall,
  callsLog,
  setState,
  newTag,
  dispatch,
  method // "add" to add/update tags, "delete" to remove tags
) => {
  if (callsLog) {
    const newLogs = callsLog.map((callLog) => {
      if (callLog._id === idCall) {
        return {
          ...callLog,
          tags: method === "add" ? newTag : null,
        };
      }
      return callLog;
    });
    dispatch({
      type: "GET_LOG_SUCCESS",
      payload: { data: newLogs },
    });
  } else {
    setState((prev) =>
      prev.map((call) => {
        if (call._id === idCall) {
          return {
            ...call,
            tags: method === "add" ? newTag : null,
          };
        }
        return call;
      })
    );
  }
};

export default TagContent;
