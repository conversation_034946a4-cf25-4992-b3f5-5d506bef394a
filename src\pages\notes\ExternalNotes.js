import React, { Suspense, useCallback, useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  But<PERSON>,
  Divider,
  Dropdown,
  Empty,
  List,
  Menu,
  Modal,
  Skeleton,
  Space,
  Tag,
  Tooltip,
  Typography,
} from "antd";
import VirtualList from "rc-virtual-list";
import MainService from "../../services/main.service";
import { useSelector, useDispatch } from "react-redux";

import { useWindowSize } from "../../custom-hooks/useWindowSize";
import { useLayoutEffect } from "react";
import {
  convertToPlain,
  getName,
} from "../layouts/chat/utils/ConversationUtils";
import {
  addSelfNotesToList,
  deleteSelfNote,
  modifySelfNote,
  removeNewNotesOnUnmount,
  removeSelectedNote,
  setNoteOpenElementModal,
  setSelectedNote,
  setSelfNotes,
} from "../../new-redux/actions/selfnotes.actions/selfnotes";
import { moment_timezone } from "../../App";
import {
  ApartmentOutlined,
  ApiOutlined,
  CalendarOutlined,
  DeleteOutlined,
  DiffOutlined,
  DownloadOutlined,
  EditOutlined,
  InfoCircleOutlined,
  InfoOutlined,
  LockOutlined,
  MessageOutlined,
  MoreOutlined,
  ReadOutlined,
  ReloadOutlined,
  ShareAltOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { toastNotification } from "components/ToastNotification";
import ShareWith from "./ShareWith";
import NoteAvatar from "pages/components/DetailsProfile/Activities/Notes/NoteAvatar";
import Confirm from "components/GenericModal";
import { AvatarChat } from "components/Chat";
import { URL_ENV } from "index";
// import { useTranslation } from "react-i18next";
import "./notes.css";
import moment from "moment";
import AffectNote from "./AffectNote";
import NoteInformations from "./NoteInformations";
import { extractTitle } from "./utils";
import useAvatar from "./useAvatar";
import { get } from "jquery";
import { closeDrawerChat } from "new-redux/actions/voip.actions/handleDrawerChat";
import { setOpenTaskRoomDrawer } from "new-redux/actions/tasks.actions/handleTaskRoomDrawer";
import { LoaderDrawer } from "pages/layouts/chat";
import TasksRoom from "pages/tasks/tasksRoom";
import ShareModal from "./ShareModal";
import {
  generateUrlToView360,
  humanDate,
  noteDate,
} from "pages/voip/helpers/helpersFunc";
import { useLocation, useNavigate } from "react-router-dom";
import jsPDF from "jspdf";
import { parse } from "node-html-parser"; // Optional for parsing HTML, you can also use DOMParser
import ModuleElementDetails from "pages/tasks/ModuleElementDetails";
import getContactDataAndDispatch from "pages/clients&users/helpers/getContactDataAndDispatch";
import { isGuestConnected } from "utils/role";
import { FiInfo } from "react-icons/fi";

function renderNodeRecursively(
  node,
  doc,
  x,
  y,
  lineHeight,
  pageWidth,
  pageHeight,
  indent = 0
) {
  if (!node) return y;

  const tagName = node.tagName?.toLowerCase() || null;
  const textContent = node.textContent?.trim() || "";
  const children = node.childNodes || [];
  const currentIndent = x + indent; // Indentation for nested elements
  const dataType = node.getAttribute?.("data-type") || null;

  switch (tagName) {
    case "div": // Div (container for any node types)
      if (dataType === "rootblock") {
        children.forEach((child) => {
          y = renderNodeRecursively(
            child,
            doc,
            x,
            y,
            lineHeight,
            pageWidth,
            pageHeight
          );
        });
      }
      break;

    case "h1": // Heading 1
      doc.setFontSize(18);
      doc.setFont("Helvetica", "bold");
      doc.text(textContent, currentIndent, y);
      y += lineHeight + 10;
      doc.setFontSize(12);
      doc.setFont("Helvetica", "normal");
      break;

    case "h2": // Heading 2
      doc.setFontSize(16);
      doc.setFont("Helvetica", "bold");
      doc.text(textContent, currentIndent, y);
      y += lineHeight + 8;
      doc.setFontSize(12);
      break;

    case "p": // Paragraph
      doc.setFont("Helvetica", "normal");
      doc.text(textContent, currentIndent, y, {
        maxWidth: pageWidth - currentIndent * 2,
      });
      y += lineHeight + 2;
      break;

    case "ul": // Unordered List
    case "ol": // Ordered List
      let index = 1; // Counter for ordered lists
      children.forEach((child) => {
        if (child.tagName?.toLowerCase() === "li") {
          // Render bullet or number
          if (tagName === "ol") {
            doc.text(`${index}.`, currentIndent, y); // Number for ordered list
          } else {
            doc.text("•", currentIndent, y); // Bullet for unordered list
          }

          // Recursively render the list item's content
          y = renderNodeRecursively(
            child,
            doc,
            currentIndent + 2,
            y,
            lineHeight,
            pageWidth,
            pageHeight,
            indent + 2
          );

          // Increment counter for ordered lists
          if (tagName === "ol") {
            index++;
          }
        }
        y += lineHeight; // Add spacing between list items
      });
      break;

    case "li": // List Item
      children.forEach((child) => {
        y = renderNodeRecursively(
          child,
          doc,
          currentIndent + 1,
          y,
          lineHeight,
          pageWidth,
          pageHeight,
          indent
        );
      });
      break;

    case "a": // Links
      const href = node.getAttribute("href");
      doc.setTextColor(0, 0, 255);
      doc.textWithLink(textContent, currentIndent, y, { url: href });
      doc.setTextColor(0, 0, 0);
      y += lineHeight;
      break;

    case "code": // Inline or Block Code
      doc.setFont("Courier", "normal");
      doc.setFontSize(10);
      const codeLines = textContent.split("\n");
      codeLines.forEach((line) => {
        doc.text(line.trim(), currentIndent + 2, y, {
          maxWidth: pageWidth - currentIndent * 2,
        });
        y += lineHeight;
      });
      doc.setFont("Helvetica", "normal");
      y += lineHeight;
      break;

    case "pre": // Preformatted block
      doc.setFont("Courier", "normal");
      console.log("textContent", textContent);
      //log children
      console.log("children", children);
      children.forEach((child) => {
        y = renderNodeRecursively(
          child,
          doc,
          currentIndent + 1,
          y,
          lineHeight,
          pageWidth,
          pageHeight,
          indent
        );
      });
      doc.setFont("Helvetica", "normal");
      y += lineHeight;
      break;

    default: // Fallback for unknown tags
      doc.text(textContent, currentIndent, y, {
        maxWidth: pageWidth - currentIndent * 2,
      });
      y += lineHeight;
      break;
  }

  // Handle page breaks
  if (y > pageHeight - lineHeight) {
    doc.addPage();
    y = 10; // Reset y for new page
  }

  return y;
}

function generatePDF(htmlContent) {
  const doc = new jsPDF();
  const x = 10; // Margin from left
  let y = 10; // Initial vertical position
  const lineHeight = 10; // Line height
  const pageWidth = doc.internal.pageSize.getWidth();
  const pageHeight = doc.internal.pageSize.getHeight();

  const root = parse(htmlContent); // Parse the HTML content

  console.log("root child nodes", root.childNodes);

  root.childNodes.forEach((node) => {
    y = renderNodeRecursively(
      node,
      doc,
      x,
      y,
      lineHeight,
      pageWidth,
      pageHeight
    );
  });

  doc.save("document.pdf");
}
// import jsPDF from "jspdf";

// const items = [
//   {
//     label: <span className="text-xs"> Share with </span>,
//     icon: (
//       <ShareAltOutlined
//         style={{
//           fontSize: "14px",
//         }}
//       />
//     ),

//     key: "0",
//     disabled: false,
//   },
//   {
//     label: <span className="text-xs"> Edit the name </span>,
//     icon: (
//       <EditOutlined
//         style={{
//           fontSize: "15px",
//         }}
//       />
//     ),
//     key: "1",
//     disabled: true,
//   },
//   {
//     type: "divider",
//   },
//   {
//     label: <span className="text-xs">{t("selfNotes.delete")}</span>,
//     icon: (
//       <DeleteOutlined
//         style={{
//           fontSize: "15px",
//         }}
//         onClick={() => {
//           console.log("delete");
//         }}
//       />
//     ),
//     key: "3",
//     danger: true,
//     disabled: false,
//   },
// ];

function stripHtmlTags(html) {
  const parser = new DOMParser();
  const doc = parser.parseFromString(html, "text/html");
  return doc.body.textContent || "";
}
const filterNotes = (notes, displayBy, currentUser, searchKeyWord) => {
  return notes
    .filter((note) => {
      switch (displayBy) {
        case "all":
          return true;
        case "my":
          return note.user === currentUser.id;
        case "shared":
          return note.shared_with?.length > 0 && note.user === currentUser.id;
        case "personal":
          return note.shared_with?.length === 0 || !note.shared_with;
        case "sharedWithMe":
          return note.user !== currentUser.id;
        default:
          return true;
      }
    })
    .filter((note) =>
      stripHtmlTags(note.content)
        ?.toLowerCase()
        ?.includes(searchKeyWord.toLowerCase())
    );
};

const sortNotes = (notes) => {
  return notes.sort((a, b) => {
    if (a.isNew && !b.isNew) return -1; // 'a' comes before 'b'
    if (!a.isNew && b.isNew) return 1; // 'b' comes before 'a'
    return new Date(b.updated_at) - new Date(a.updated_at);
  });
};

const createMenuItems = (item, currentUser, t) => {
  return [
    {
      label: <span className="text-xs">{t("selfNotes.informations")}</span>,
      icon: <FiInfo style={{ fontSize: "15px" }} />,
      key: "6",
      disabled: false,
    },
    {
      label: <span className="text-xs">{t("selfNotes.shareWith")}</span>,
      icon: <ShareAltOutlined style={{ fontSize: "14px" }} />,
      key: "0",
      disabled:
        item?.shared_with?.find((shared) => shared._id === currentUser.id) ||
        item?.isNew ||
        isGuestConnected(),
    },
    {
      label: <span className="text-xs">{t("selfNotes.associate")}</span>,
      icon: <ApartmentOutlined style={{ fontSize: "15px" }} />,
      key: "5",
      disabled:
        item?.shared_with?.find((shared) => shared._id === currentUser.id) ||
        item?.isNew ||
        isGuestConnected(),
    },
    {
      label: <span className="text-xs">{t("selfNotes.discussion")}</span>,
      icon: <MessageOutlined style={{ fontSize: "15px" }} />,
      key: "1",
      // disabled: true,
      disabled: item?.shared_with?.length === 0 || isGuestConnected(),
    },

    { type: "divider" },
    {
      label: <span className="text-xs">{t("selfNotes.archiver")}</span>,
      icon: <DiffOutlined style={{ fontSize: "15px" }} />,
      key: "2",
      disabled: true,
    },
    // {
    //   label: <span className="text-xs">{t("selfNotes.download")}</span>,
    //   icon: <DownloadOutlined style={{ fontSize: "15px" }} />,
    //   key: "4",
    // },
    {
      label: <span className="text-xs">{t("selfNotes.delete")}</span>,
      icon: <DeleteOutlined style={{ fontSize: "15px" }} />,
      key: "3",
      danger: true,
      disabled:
        item?.shared_with?.find((shared) => shared._id === currentUser.id) ||
        item?.isNew,
    },
  ];
};
const state = {
  0: "default",
  1: "fetching",
  2: "fetched",
  3: "error",
};
const ExternalNotes = ({
  isExternal,
  setClickedNote,
  selectedNote,
  searchKeyWord,
  displayBy,
}) => {
  const dispatch = useDispatch();
  const { t } = useTranslation("common");
  const notesRedux = useSelector((state) => state?.selfNotesReducer?.slefNotes);
  const currentUser = useSelector((state) => state?.user?.user);
  const [totalPages, setTotalPages] = useState(null);
  const [pageNum, setPageNum] = useState(1);
  const [statusNote, setStatusNote] = useState(state[0]);
  const [elHeight, setElHeight] = useState({
    parent: 0,
    child: 0,
  });

  const [elementDetails, setElementDetails] = useState({
    id: null,
    module: null,
  });

  const [openElementDetails, setOpenElementDetails] = useState(false);

  const dateConfig = useSelector(
    (state) => state?.user?.user?.location?.date_format
  );

  const dateTimeConfig = useSelector(
    (state) => state?.user?.user?.location?.time_format
  );

  const dateTimeZoneConfig = useSelector(
    (state) => state?.user?.user?.location?.default_timezone
  );

  const globalDateFormat = (date, time) => {
    return `${date} ${time}`;
  };

  const [roomNoteId, setRoomNoteId] = useState(null);

  const navigate = useNavigate();

  const roomForNote = (noteId, note) => {
    const room_name = extractTitle(note?.content);
    MainService.createRoomNote(noteId, room_name)
      .then((res) => {
        console.log("res", res);
      })
      .catch((err) => {
        console.log("err", err);
      })
      .finally(() => {
        console.log("Test");
      });
  };

  // const handleDownloadAsPDF = (note) => {
  //   const pdf = new jsPDF({
  //     orientation: "p", // 'p' for portrait, 'l' for landscape
  //     unit: "mm", // units for measurements
  //     format: "a4", // paper format (A4)
  //   });

  //   const topMargin = 10; // Top margin in mm
  //   const bottomMargin = 10; // Bottom margin in mm
  //   const leftMargin = 10; // Left margin in mm
  //   const rightMargin = 10; // Right margin in mm

  //   const pageHeight = pdf.internal.pageSize.height;
  //   const contentWidth = pdf.internal.pageSize.width - leftMargin - rightMargin;

  //   const content = note.content; // Assuming note.content is a string of HTML

  //   // Add initial page
  //   const addPage = () => {
  //     if (pdf.internal.getNumberOfPages() > 1) {
  //       pdf.addPage();
  //     }
  //     pdf.setPage(pdf.internal.getNumberOfPages());
  //     pdf.setY(topMargin);
  //   };

  //   // Process the content
  //   pdf.html(content, {
  //     callback: function (pdf) {
  //       // Adjust position if the content exceeds the page height
  //       let currentY = topMargin;
  //       const contentHeight =
  //         pdf.internal.pageSize.height - topMargin - bottomMargin;

  //       pdf.html(content, {
  //         callback: function (pdf) {
  //           const pageHeight = pdf.internal.pageSize.height;
  //           let y = topMargin;

  //           // Manually handle content overflow and pagination
  //           while (y + contentHeight > pageHeight - bottomMargin) {
  //             pdf.setY(y);
  //             pdf.html(content, {
  //               callback: function (pdf) {
  //                 pdf.addPage();
  //                 y = topMargin;
  //               },
  //               x: leftMargin,
  //               y: y,
  //               width: contentWidth,
  //               autoPaging: "text",
  //             });
  //             y += contentHeight;
  //           }

  //           // Finalize and save the PDF
  //           pdf.save(`${extractTitle(note.content)} - ${note.updated_at}.pdf`);
  //         },
  //         x: leftMargin,
  //         y: currentY,
  //         width: contentWidth,
  //         windowWidth: 900, // Adjust this to ensure correct content scaling
  //         margin: [topMargin, leftMargin, bottomMargin, rightMargin], // Margins in [top, left, bottom, right]
  //         autoPaging: "text", // Handle pagination automatically based on text overflow
  //       });
  //     },
  //     x: leftMargin,
  //     y: topMargin,
  //     width: contentWidth,
  //     windowWidth: 900, // Adjust this to ensure correct content scaling
  //     margin: [topMargin, leftMargin, bottomMargin, rightMargin], // Margins in [top, left, bottom, right]
  //     autoPaging: "text", // Handle pagination automatically based on text overflow
  //   });
  // };

  useEffect(() => {
    //find clicked note in the list
    if (selectedNote) {
      console.log("Here we are");
      const note = notesRedux?.find((note) => note._id == selectedNote._id);
      console.log("note is changing", note);
      if (note) {
        setClickedNote({
          ...note,
          content: note.content,
        });

        //remove the new note
      }
    }
  }, [notesRedux]);

  const users = useSelector((state) => state?.chat?.userList);

  const selfNoteSelected = useSelector(
    (state) => state.selfNotesReducer.selectedNote
  );

  const saveIsPending = useSelector(
    (state) => state.selfNotesReducer.savePending
  );

  const notesFetched = useSelector((state) => state?.selfNotesReducer?.fetched);

  const returnSpeceficSharedWithNoteFullUsers = (shared_with) => {
    //check if current user is in the shared_with array
    const usr = {
      name: currentUser.label,
      image: currentUser.avatar,
      id: currentUser.id,
    };

    let s = shared_with.map((shared) => {
      return users.find((user) => user.uuid == shared.uuid);
    });

    if (shared_with.find((shared) => shared._id === currentUser.id)) {
      s.push(usr);
    }

    return s;
  };

  const userLookupById = React.useMemo(() => {
    return users.reduce((acc, user) => {
      acc[user._id] = user; // Assuming user._id is unique
      return acc;
    }, {});
  }, [users]);

  const userLookupByName = React.useMemo(() => {
    return users.reduce((acc, user) => {
      acc[user.name] = user; // Assuming user.name is unique
      return acc;
    }, {});
  }, [users]);

  const [roomActivity, setRoomActivity] = useState(null);

  const { openTaskRoomDrawer } = useSelector((state) => state?.TasksRealTime);

  const handleOpenChatRoomFromDrawer = (note) => {
    dispatch(closeDrawerChat());
    dispatch(setOpenTaskRoomDrawer(true));
    setRoomActivity(note);
  };

  const handleClickOnElement = (record) => {
    console.log("record", record);
    getContactDataAndDispatch(
      record?.family_data?.family_id,
      record?.element_data?.label_data,
      { key: record?.element_data?.element_id },
      record,
      dispatch,
      null,
      () => {}
    );
    dispatch(
      setNoteOpenElementModal({
        noteId: record?._id,
        closeModalSignal: false,
      })
    );
    setElementDetails((prev) => ({
      ...prev,
      id: record?.element_data?.element_id,
      module: record?.family_data?.family_id,
    }));
    setOpenElementDetails(true);
  };

  const noteOpenElementModal = useSelector(
    (state) => state?.selfNotesReducer?.noteOpenElementModal
  );

  useEffect(() => {
    if (
      noteOpenElementModal?.noteId &&
      noteOpenElementModal?.closeModalSignal === true &&
      noteOpenElementModal?.noteToOpen
    ) {
      setOpenElementDetails(false);
      setElementDetails({
        id: null,
        module: null,
      });
      dispatch({ type: "RESET_CONTACT_HEADER_INFO" });

      //search for the noteToOpen and set it
      // const note = notesRedux?.find((note) => note._id == noteOpenElementModal?.noteToOpen);
      // console.log("note is changing", note);
      // setClickedNote({
      //   ...note,
      //   content: note?.content,
      // })

      dispatch(setNoteOpenElementModal(null));
    }
  }, [noteOpenElementModal]);

  const getAvatar = (item, role) => {
    if (role == "owner") {
      if (currentUser.id == item.user) {
        return {
          avatar: currentUser.avatar,
          name: currentUser.label,
          isYou: true,
        };
      } else {
        console.log("item", item);
        const user =
          userLookupById[item.user] ||
          userLookupByName[item.dataUser.label_data];
        console.log("user print", user);
        return {
          avatar: user?.image == null ? null : user?.image,
          name: user?.name,
          isYou: false,
        };
      }
    } else {
      if (item?._id == currentUser.id) {
        // console.log("item", item);
        // console.log("currentUser", {
        //   avatar: currentUser.avatar,
        //   name: currentUser.label,
        //   isYou: true,
        // });
        return {
          avatar: currentUser.avatar,
          name: currentUser.label,
          isYou: true,
        };
      } else {
        const user = users.find(
          (user) => user.uuid == item.uuid || item.id == user._id
        );
        return {
          avatar: user?.image,
          name: user?.name,
          isYou: false,
        };
      }
    }
  };

  const getNotesPages = useCallback(async () => {
    if (!isExternal || state[0] !== statusNote) return;
    try {
      setStatusNote(state[1]);
      const response = await MainService.getNotes360(
        null,
        null,
        pageNum,
        1000,
        isExternal
      );
      if (response.status === 200) {
        setStatusNote(state[2]);

        if (totalPages == null) {
          if (response?.data?.data?.data?.length > 0) {
            dispatch(setSelfNotes(response?.data?.data?.data));
          }
        } else {
          dispatch(addSelfNotesToList(response?.data?.data?.data));
        }
        if (!notesFetched && location.state?.id) {
          forcedProcessLocationState(
            response?.data?.data?.data,
            location.state.id
          );
        }
        setTotalPages((prev) => {
          if (prev === null) {
            return response?.data?.data?.last_page;
          }
          return prev;
        });
      } else throw new Error("Error fetching notes");
    } catch (error) {
      console.log("Error fetching notes", error);
      setStatusNote(state[3]);
    }
  }, [dispatch, pageNum, totalPages, statusNote, isExternal]);

  const onScroll = () => {
    if (pageNum < totalPages) {
      setPageNum((prev) => prev + 1);
    }
  };

  const filteredNotes = React.useMemo(() => {
    const filtered = filterNotes(
      notesRedux,
      displayBy,
      currentUser,
      searchKeyWord
    );
    return sortNotes(filtered);
  }, [notesRedux, displayBy, currentUser, searchKeyWord]);

  useEffect(() => {
    if (!notesFetched) {
      getNotesPages();
    }

    // return () => {
    //   dispatch(removeNewNotesOnUnmount());
    // };
  }, [dispatch, getNotesPages]);
  const windowSize = useWindowSize();

  useLayoutEffect(() => {
    let mount = true;
    const parentElement = document.getElementById("containerDIV");
    const childElement = document.getElementById("listItem-0");
    if (parentElement && mount) {
      mount = false;
      setElHeight({
        parent: windowSize[1] - 32 - 200,
        child: childElement?.offsetHeight + 10 || 60,
      });
    }

    return () => {
      mount = false;
    };
  }, [windowSize]);

  const [deleteModal, setDeleteModal] = useState(false);
  const [selectedItemToDelete, setSelectedItemToDelete] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);

  const [shareModal, setShareModal] = useState(false);
  const [selectedItemToShare, setSelectedItemToShare] = useState(null);
  const [shareLoading, setShareLoading] = useState(false);

  const [affectModal, setAffectModal] = useState(false);
  const [selectedItemToAffect, setSelectedItemToAffect] = useState(null);
  const [affectLoading, setAffectLoading] = useState(false);

  const [openInfosDrawer, setOpenInfosDrawer] = useState(false);
  const [selectedItemInfos, setSelectedItemInfos] = useState(null);
  const location = useLocation();

  const [fetched, setFetched] = useState(false);

  // useEffect(async () => {
  //   console.log("location state", location.state?.id);

  //   if (location.state?.id) {
  //     const note = notesRedux?.find((note) => note._id == location.state.id);
  //     console.log("note is changing", note);
  //     if (note) {
  //       console.log("note is here");
  //       dispatch(setSelectedNote(note));
  //       setClickedNote({
  //         ...note,
  //         content: note.content,
  //       });
  //     } else {
  //       console.log("not receiveing state", location.state?.id);
  //     }
  //   }
  // }, [location]);

  const processLocationState = () => {
    console.log("location state", location.state?.id);

    if (location.state?.id) {
      const note = notesRedux?.find((note) => note._id === location.state.id);
      console.log("note is changing", note);
      if (note) {
        console.log("note is here");
        dispatch(setSelectedNote(note));
        setClickedNote({
          ...note,
          content: note.content,
        });
      } else {
        // getNotesPages();
        console.log("not receiving state", location.state?.id);
      }
    }
  };

  const forcedProcessLocationState = (notes, id) => {
    console.log("location state", id);

    const note = notes?.find((note) => note._id === id);
    console.log("note is changing", note);
    if (note) {
      console.log("note is here");
      dispatch(setSelectedNote(note));
      setClickedNote({
        ...note,
        content: note.content,
      });
    } else {
      // getNotesPages();
      console.log("not receiving state", id);
    }
  };

  useEffect(() => {
    if (notesFetched === true) processLocationState();
  }, [location]);

  useEffect(async () => {
    if (location?.state?.roomId) {
      // setRoomActivity(location.state?.roomId);
      let note = { _id: location.state?.roomId };
      handleOpenChatRoomFromDrawer(note);
    }
  }, [location]);

  const shareNoteFunction = (noteId, data) => {
    setShareLoading(true);
    MainService.shareNoteWithCollegues(noteId, data)
      .then((response) => {
        console.log("share note ", response.data.data);
        setShareLoading(false);
        setShareModal(false);
        setSelectedItemToShare(null);
        toastNotification("success", t("selfNotes.sharingSuccess"));

        dispatch(modifySelfNote(response.data.data));
      })
      .catch((error) => {
        console.log("share note ", error);
        toastNotification("error", t("selfNotes.sharingError"));
      })
      .finally(() => {
        setShareLoading(false);
      });
  };

  const handleOk = (id) => {
    // setDeleteLoading(true);
    MainService.deleteNote360(id)
      .then((response) => {
        console.log("delete note ", response.data.data);
        console.log("delete note ", id);
        console.log("delete note ", selfNoteSelected);
        if (selfNoteSelected?._id == id) {
          setClickedNote(null);
          dispatch(removeSelectedNote());
        }
        dispatch(deleteSelfNote(id));

        // setSelectedItemToDelete(null);
        toastNotification("success", t("selfNotes.deleteSuccess"));
      })
      .catch((error) => {
        console.log("delete note ", error);
        toastNotification("error", t("selfNotes.deleteError"));
      })
      .finally(() => {
        // setDeleteLoading(false);
        // setDeleteLoading(false);
        // setDeleteModal(false);
        // setSelectedItemToDelete(null);
      });
  };

  const handleCancel = () => {
    setDeleteModal(false);
    setSelectedItemToDelete(null);
  };

  // const deleteModalDisplay = (note) => {
  //   return (

  //   );
  // };

  const onClickOnMenuItems = (key, item) => {
    console.log("key", key);
    console.log("item", item);
    switch (key.key) {
      case "0":
        console.log("share");
        setShareModal(true);
        setSelectedItemToShare(item);
        break;
      case "3":
        console.log("delete");
        // setDeleteModal(true);
        // setSelectedItemToDelete(item);
        // deleteModalDisplay(item);
        break;
      case "5":
        console.log("affect");
        setAffectModal(true);
        setSelectedItemToAffect(item);
        break;
      case "6":
        setOpenInfosDrawer(true);
        setSelectedItemInfos(item);
      default:
        break;
    }
  };

  return (
    <div id="containerDIV" className="h-full w-full">
      {shareModal && selectedItemToShare && (
        <ShareModal
          open={shareModal}
          setOpen={setShareModal}
          shareLoading={shareLoading}
          item={selectedItemToShare}
          shareFunction={shareNoteFunction}
          selectedNote={selectedItemToShare}
        ></ShareModal>
      )}

      {affectModal && selectedItemToAffect && (
        <AffectNote
          open={affectModal}
          setOpen={setAffectModal}
          item={selectedItemToAffect}
        />
      )}

      {openInfosDrawer && selectedItemInfos && (
        <NoteInformations
          openDrawer={openInfosDrawer}
          setOpenDrawer={setOpenInfosDrawer}
          note={selectedItemInfos}
          setSelectedNote={setSelectedItemInfos}
        />
      )}

      <Divider className="my-0  px-3" />
      {statusNote === state[1] ? (
        <div className="mx-0.5 mt-1 flex flex-col gap-y-2 space-y-1">
          {Array.from({ length: 7 }, (_, index) => (
            <Skeleton.Input active block key={index} />
          ))}
        </div>
      ) : statusNote === state[3] ? (
        <Empty
          className="  flex  flex-1 flex-col items-center justify-center "
          description={
            <Space size={3} className="flex flex-col">
              <Typography.Text type="danger">
                {t("toasts.errorFetchApi")}
              </Typography.Text>
              <Tooltip title={t("chat.reload")}></Tooltip>
            </Space>
          }
        >
          <Tooltip title={t("chat.reload")}>
            <Button
              loading={statusNote === state[1]}
              danger
              type="primary"
              onClick={() => {
                setStatusNote(state[0]);

                getNotesPages();
              }}
              icon={<ReloadOutlined />}
            >
              {t("chat.reload")}
            </Button>
          </Tooltip>
        </Empty>
      ) : statusNote === state[2] &&
        (notesRedux.length === 0 ||
          notesRedux.filter((note) =>
            stripHtmlTags(note.content)
              ?.toLowerCase()
              ?.includes(searchKeyWord.toLowerCase())
          ).length === 0) ? (
        <Empty />
      ) : (
        <List
          // dataSource={
          //   notesRedux
          //     .filter((note) => {
          //       if (displayBy === "all") {
          //         return true;
          //       } else if (displayBy === "my") {
          //         return note.user === currentUser.id;
          //       } else if (displayBy === "shared") {
          //         return (
          //           note.shared_with?.length > 0 && note.user === currentUser.id
          //         );
          //       } else if (displayBy === "personal") {
          //         if (note?.shared_with?.length === 0 || !note?.shared_with) {
          //           return true;
          //         }
          //       } else if (displayBy === "sharedWithMe") {
          //         // return note.shared_with?.find(
          //         //   (shared) => shared._id === currentUser.id
          //         // );
          //         if (note.user !== currentUser.id) {
          //           console.log("note.user", note.user);
          //         }
          //         return note.user !== currentUser.id;
          //       }
          //     })
          //     .filter((note) =>
          //       stripHtmlTags(note.content)
          //         ?.toLowerCase()
          //         ?.includes(searchKeyWord.toLowerCase())
          //     )
          //   //i want to add the displayBy
          // }
          className="notesList  h-full"
        >
          <VirtualList
            data={filteredNotes}
            height={elHeight.parent}
            itemHeight={70 || elHeight.child}
            itemKey="_id"
            onScroll={onScroll}
          >
            {(item, index) => (
              <List.Item
                key={`note_${index}_${item._id}`}
                className={`group m-0.5 cursor-pointer rounded-none px-3 py-2 transition duration-300 
                  ease-in-out hover:bg-gray-100  hover:shadow-sm
                  ${
                    selectedNote?._id === item._id
                      ? "bg-blue-100 shadow-inner"
                      : ""
                  }`}
                /* DRAFT TO ADD WHILE I HAD NOT SAVE THE FILE 
              
              */
                // extra={
                //     <span className="ml-1  block text-xs font-semibold text-pink-500 group-hover:hidden">
                //       {t("chat.message_system.brouillon")}
                //     </span>
                //
                actions={[
                  <Dropdown
                    key={`dropdown_${index}_${item._id}`}
                    overlay={
                      <Menu
                        items={createMenuItems(item, currentUser, t)}
                        onClick={({ key }) => {
                          if (key === "3") {
                            Confirm(
                              t("selfNotes.deleteNote"),
                              t("selfNotes.delete"),
                              <DeleteOutlined style={{ color: "red" }} />,
                              () => handleOk(item._id),
                              true,
                              t("selfNotes.deleteNoteRichText"),
                              "s"
                            );
                          } else if (key === "1") {
                            item?.shared_with_users_data &&
                              handleOpenChatRoomFromDrawer(item);
                          } else if (key === "4") {
                            generatePDF(item.content);
                          } else {
                            onClickOnMenuItems({ key }, item);
                          }
                        }}
                      />
                    }
                    trigger={["click"]}
                  >
                    <div className=" hidden transition-all duration-300 group-hover:block  group-hover:px-0.5">
                      <Button
                        icon={<MoreOutlined />}
                        type="text"
                        shape="circle"
                        size="small"
                      ></Button>
                    </div>
                  </Dropdown>,
                ]}
              >
                <div
                  className="w-full flex-col"
                  role="button"
                  onClick={() => {
                    setClickedNote(item);
                    dispatch(setSelectedNote(item));
                  }}
                >
                  <List.Item.Meta
                    title={
                      <div className=" w-full items-center justify-between px-2">
                        <div className="flex items-center">
                          {currentUser.id != item?.user && !item?.isNew && (
                            <div className="w-[20px]">
                              <Tooltip title={t("selfNotes.readOnly")}>
                                <LockOutlined
                                  // className=" mb-1 self-end text-sm text-gray-500"
                                  className="text-sm text-gray-400"
                                  // title={t("selfNotes.readOnly")}
                                />
                              </Tooltip>
                            </div>
                          )}
                          <time className="text-xs font-normal text-gray-500">
                            {/* {moment_timezone(item.updated_at).format("lll")} */}
                            {/* {moment
                          .tz(item.updated_at, dateTimeZoneConfig)

                          .format(globalDateFormat(dateConfig, dateTimeConfig))} */}

                            {noteDate(
                              item.updated_at,
                              t,
                              dateConfig,
                              dateTimeConfig,
                              dateTimeZoneConfig
                            )}
                            {/* {humanDate(item.updated_at, t)} */}
                          </time>
                        </div>
                        <div className="flex items-center justify-between">
                          <p
                            // className="h-6  max-w-[200px] truncate px-2 font-semibold  [&_p]:truncate"
                            className=" w-[400px] truncate  font-semibold  [&_p]:truncate"
                            dangerouslySetInnerHTML={{
                              // __html: convertToPlain(item.content),
                              __html: extractTitle(item.content),
                            }}
                          />
                        </div>
                        {/* {currentUser.id != item?.user && !item?.isNew && (
                          <Tooltip title={t("selfNotes.readOnly")}>
                            <LockOutlined
                              // className=" mb-1 self-end text-sm text-gray-500"
                              className="lock-icon"
                              // title={t("selfNotes.readOnly")}
                            />
                          </Tooltip>
                        )} */}
                      </div>
                    }
                    // description={
                    //   <time className="mb-2 px-2 text-gray-500">
                    //     {/* {moment_timezone(item.updated_at).format("lll")} */}
                    //     {/* {moment
                    //       .tz(item.updated_at, dateTimeZoneConfig)

                    //       .format(globalDateFormat(dateConfig, dateTimeConfig))} */}

                    //     {noteDate(
                    //       item.updated_at,
                    //       t,
                    //       dateConfig,
                    //       dateTimeConfig,
                    //       dateTimeZoneConfig
                    //     )}
                    //     {/* {humanDate(item.updated_at, t)} */}
                    //   </time>
                    // }
                  />

                  <div className="mt-1 flex w-full items-center ">
                    <div className=" w-[90px]">
                      <Avatar.Group
                        maxCount={3}
                        maxStyle={{
                          color: "#f56a00",
                          backgroundColor: "#fde3cf",
                          display: "flex",
                          flexDirection: "row",
                          justifyContent: "center",
                          alignItems: "center",
                          width: 24,
                          height: 24,
                        }}
                        maxPopoverTrigger="hover"
                        maxPopoverPlacement="top"
                        size="small"
                        className="mt-0.5 flex flex-row items-center px-2"
                      >
                        {item?.user_data && (
                          <Tooltip
                            title={`${t("selfNotes.owner")}: ${
                              item?.user_data?.label_data
                            }`}
                            key={index}
                            placement="top"
                          >
                            <div>
                              {/* <NoteAvatar
                              key={index}
                              name={item?.dataUser?.label_data}
                              avatar={item?.dataUser?.avatar}
                              size={25}
                            /> */}
                              <AvatarChat
                                url={
                                  URL_ENV?.REACT_APP_BASE_URL +
                                  URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                                  item?.user_data?.avatar
                                }
                                type="user"
                                size={24}
                                height={8}
                                width={8}
                                // name={getName(
                                //   item?.dataUser?.label_data,
                                //   "avatar"
                                // )}
                                // hasImage={item?.dataUser?.avatar}
                                name={getName(
                                  item?.user_data?.label_data,
                                  "avatar"
                                )}
                                hasImage={
                                  item?.user_data?.avatar ? true : false
                                }
                              />
                            </div>
                          </Tooltip>
                        )}

                        {item?.shared_with_users_data?.length > 0 &&
                          // returnSpeceficSharedWithNoteFullUsers(item?.shared_with)
                          item?.shared_with_users_data
                            .filter((user) => user)
                            .map((user, index) => (
                              // console.log("user", user),
                              <Tooltip
                                title={`${t("selfNotes.observer")}: ${
                                  user?.label_data
                                }`}
                                key={index}
                                placement="top"
                              >
                                <div>
                                  {/* <NoteAvatar
                                  key={index}
                                  name={user?.name}
                                  avatar={user?.image}
                                  size={25}
                                /> */}
                                  <AvatarChat
                                    url={
                                      URL_ENV?.REACT_APP_BASE_URL +
                                      URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                                      user?.avatar
                                    }
                                    type="user"
                                    size={24}
                                    height={8}
                                    width={8}
                                    // name={getName(user?.name, "avatar")}
                                    name={getName(user?.label_data, "avatar")}
                                    hasImage={user?.avatar}
                                  />
                                </div>
                              </Tooltip>
                            ))}
                      </Avatar.Group>
                    </div>
                    {/* 
                    <div className="w-[20px]">
                      {item?.room_id && item?.shared_with?.length > 0 && (
                        <Tooltip title={t("selfNotes.note_has_discussion")}>
                          <MessageOutlined
                            className="noteMessage text-sm text-gray-400"
                            onClick={(e) => {
                              handleOpenChatRoomFromDrawer(item);
                              e.stopPropagation();
                            }}
                          />
                        </Tooltip>
                      )}
                    </div> */}

                    <div className=" w-[120px]">
                      {item?.element_data && item?.family_data && (
                        <Tooltip
                          title={`${item?.family_data?.label_data}/
                            ${item?.element_data?.label_data}`}
                        >
                          <Tag
                            color="magenta"
                            className="module-tag-text  mt-2 h-5  max-w-[120px] cursor-pointer
                            truncate
                            "
                            // onClick={() => {
                            //   console.log("noteFamily", noteFamily);
                            //   handleClickOnElement(note?.family_id, noteFamily?.label);
                            // }}
                            onClick={(e) => {
                              // navigate(
                              //   generateUrlToView360(
                              //     Number(item?.family_data?.family_id),
                              //     item?.element_data?.element_id,
                              //     "v2"
                              //   )
                              // );
                              // setElementDetails({
                              //   id: item?.element_data?.element_id,
                              //   module: item?.family_data?.family_id,
                              // });
                              // setOpenElementDetails(true);
                              handleClickOnElement(item);
                              //stop propagation
                              e.stopPropagation();
                            }}
                          >
                            {item?.family_data?.label_data}/
                            {item?.element_data?.label_data}
                          </Tag>
                        </Tooltip>
                      )}
                    </div>
                  </div>

                  {/* {item?.element_id && <div className="mt-2">Test</div>} */}
                </div>
              </List.Item>
            )}
          </VirtualList>
        </List>
      )}

      <ModuleElementDetails
        key={elementDetails?.id}
        openElementDetails={openElementDetails}
        setOpenElementDetails={setOpenElementDetails}
        setElementDetails={setElementDetails}
        elementDetails={elementDetails}
      />
      {openTaskRoomDrawer && roomActivity && (
        <Suspense fallback={<LoaderDrawer />}>
          <TasksRoom
            // key={roomForNote}
            elementId={roomActivity?._id}
            canCreateRoom={1}
            setTaskToUpdate={null}
            setOpenActivity360={null}
            roomName={extractTitle(roomActivity?.content)}
            chatSource={"notes"}
          />
        </Suspense>
      )}
    </div>
  );
};
export default ExternalNotes;
