import { Col, Row, Tree } from "antd";
import GeneralInfos from "pages/tasks/activityDetails/GeneralInfos";
const { DirectoryTree } = Tree;

const treeData = [
  {
    title: "parent 0",
    key: "0-0",
    children: [
      {
        title: "leaf 0-0",
        key: "0-0-0",
        isLeaf: true,
      },
      {
        title: "leaf 0-1",
        key: "0-0-1",
        isLeaf: true,
      },
    ],
  },
  {
    title: "parent 1",
    key: "0-1",
    children: [
      {
        title: "leaf 1-0",
        key: "0-1-0",
        isLeaf: true,
      },
      {
        title: "leaf 1-1",
        key: "0-1-1",
        isLeaf: true,
      },
    ],
  },
];

const ProjectWrapper = () => {
  const onSelect = (keys, info) => {
    console.log("Trigger Select", keys, info);
  };
  const onExpand = (keys, info) => {
    console.log("Trigger Expand", keys, info);
  };
  return (
    <Row>
      <Col className="h-full bg-red-200" span={6}>
        {/* <DirectoryTree
          multiple
          defaultExpandAll
          onSelect={onSelect}
          onExpand={onExpand}
          treeData={treeData}
        /> */}
        Side Menu
      </Col>
      <Col className="h-full bg-blue-200" span={18}>
        {/* Main Page */}
        <GeneralInfos
          singleTaskData={{}}
          tasksTypes={[]}
          pipelines={[]}
          guestsList={[]}
          checkedItems={[]}
          guestsSearchQuery=""
          setGuestsSearchQuery={() => {}}
          guestsListPage={1}
          setGuestsListPage={() => {}}
          guestsListLastPage={1}
          setCheckedItems={() => {}}
          setSingleTaskData={() => {}}
          ownersList={[]}
          checkedFollowers={[]}
          setCheckedFollowers={() => {}}
          setFollowersSearchQuery={() => {}}
          loadOwners={false}
          loadGuests={false}
          form={{}}
          setCountChanges={() => {}}
          setSelectedFamilyMembers={() => {}}
          source=""
        />
      </Col>
    </Row>
  );
};

export default ProjectWrapper;
