import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { Avatar, Table, Badge, Space, Select } from "antd";
import MarkDown from "../../../rmc/mailing/Markdown";
import { useTranslation } from "react-i18next";

import MainService from "../../../../services/main.service";

import { useSelector } from "react-redux";
import DetailsMessage from "pages/rmc/mailing/detailEmail/DetailsMessage";
import { setNumberEmailThread } from "new-redux/actions/mail.actions";
import { useDispatch } from "react-redux";
import { useWindowSize } from "pages/clients&users/components/WindowSize";

const acceptedFamilyIds = [1, 2, 4, 9];

const Mails = ({ contactInfo, setSelectedKey = () => {} }) => {
  const [t] = useTranslation("common");
  const windowSize = useWindowSize();

  const { openView360InDrawer } = useSelector((state) => state?.vue360);
  const { dataAccounts, numberEmailThread, refreshMailVueSphere } = useSelector(
    (state) => state.mailReducer
  );
  //
  const usedAccountId = useMemo(() => {
    if (!!dataAccounts) {
      if (!!dataAccounts.length) {
        const findSelectedAccount = dataAccounts?.find(
          (item) => item?.selected
        );
        if (!!findSelectedAccount) return findSelectedAccount?.value;
        else return dataAccounts?.[0]?.value;
      }
    }
    return null;
  }, [dataAccounts]);

  const [loading, setLoading] = useState(false);
  const [detailState, setDetailState] = useState(false);

  const [dataMailByFamily, setDataMailByFamily] = useState([]);
  const [metaMailByFamily, setMetaMailByFamily] = useState({});

  const [page, setPage] = useState(1);
  const [pageDetail, setPageDetail] = useState(0);
  const [loadingDetails, setLoadingDetails] = useState(false);
  const [detailsMail, setDetailsMail] = useState([]);
  const [accountId360, setAccountId360] = useState(null);
  const [idEmail360, setIdEmail360] = useState(null);
  const [selectedAccount, setSelectedAccount] = useState(usedAccountId);
  // const [openModalMails, setOpenModalMails] = useState(false);

  const url_string = window.location.href;
  const url = new URL(url_string);

  const dispatch = useDispatch();
  // const accountId = url.pathname.split("/")[2];
  const getMailsInbox = useCallback(async () => {
    if (!contactInfo?.id) return;
    setLoading(true);

    try {
      const response = await MainService.getEmailsByFamily(
        contactInfo?.id,
        page,
        selectedAccount
      );

      if (response?.status === 200) {
        setDataMailByFamily(response?.data?.data);
        setMetaMailByFamily(response?.data?.meta);
        setLoading(false);
      }
    } catch (err) {
      console.log(err);
      setLoading(false);
    }
  }, [contactInfo?.id, page, selectedAccount]);

  const dataInboxByFamily = dataMailByFamily?.map((item, i) => ({
    key: item.id,
    from: { name: item.from.name, address: item.from.address, nbr: item?.nbr },
    subject: item.subject,
    body: item.body,
    date: item.date,
    seen: item.seen,
    starred: item.starred,
    important: item.important,
    exist: item.exist,
    third_id: item.third_id,
    accountId: item.account_id,
    nbr: item.nbr,
  }));

  const columns = [
    {
      title: t("mailing.Inbox.sender"),
      dataIndex: "from",
      width: "180px",
      render: (text, record) => {
        return (
          <div className="flex items-center space-x-0.5">
            <div className="w-[30px]">
              <Avatar
                style={{
                  backgroundColor: "#f56a00",
                }}
                size={28}
              >
                {record.from?.name
                  ? record.from?.name?.charAt(0)?.toUpperCase()
                  : record.from?.address?.charAt(0)?.toUpperCase()}
              </Avatar>
            </div>
            <span
              style={{
                fontWeight: record.seen === 0 ? "bold" : "",
              }}
              className="ml-[3px] flex max-w-[190px] items-center truncate"
            >
              {text?.address}
            </span>
            {text?.nbr > 1 ? (
              <Badge
                style={{
                  backgroundColor: "transparent",
                  border: "none",
                  outline: "none",
                  margin: 0,
                  color: "gray",
                  fontSize: 12,
                  fontWeight: record?.seen === 0 ? "bold" : "normal",
                }}
                count={text?.nbr}
              ></Badge>
            ) : null}
          </div>
        );
      },
    },
    {
      title: t("mailing.Inbox.subject"),
      dataIndex: "subject",
      width: "150px",
      render: (text, record) => {
        return (
          <div style={{ cursor: "pointer" }}>
            <span
              style={{
                fontWeight: record.seen === 0 ? "bold" : "",
                cursor: "pointer",
              }}
            >
              {text?.length > 30 ? (
                <p>{text?.toString()?.substring(0, 30)}...</p>
              ) : (
                text
              )}
            </span>
          </div>
        );
      },
    },
    {
      title: t("mailing.Inbox.message"),
      dataIndex: "body",
      width: "300px",
      ellipsis: true,
      render: (text, record) => {
        return (
          <div style={{ cursor: "pointer" }}>
            <MarkDown>{text}</MarkDown>
          </div>
        );
      },
    },
    {
      title: t("mailing.Inbox.date"),
      dataIndex: "date",
      width: "120px",
      render: (dateTime, record) => {
        return (
          <>
            <span
              style={{
                cursor: "pointer",
              }}
            >
              {dateTime}
              {/* {dayjs(record.date).isToday()
              ? moment(record.date).format("LT")
              : moment(record.date).format("llll")} */}
            </span>
          </>
        );
      },
    },
  ];

  const getDetailsMessageInbox = useCallback(
    async (idDetails, accountId) => {
      var formData = new FormData();
      formData.append("id", idDetails);
      formData.append("accountId", accountId);
      formData.append("folderMailing", "inbox");
      formData.append("limit", "5");
      if (numberEmailThread <= 5) {
        formData.append("first_id_email", "");
        formData.append("last_id_email", "");
      }
      if (numberEmailThread > 5) {
        formData.append("nbrEmails", numberEmailThread);
      }

      try {
        setLoadingDetails(true);
        setDetailsMail([]);
        let response;

        if (numberEmailThread > 5) {
          response = await MainService.getDetailsInboxFirstLast(formData);
        } else {
          response = await MainService.getDetailsInbox(1, formData);
        }
        if (response.status === 200) {
          setDetailsMail(response?.data);
          setLoadingDetails(false);
        }
      } catch (error) {
        setLoadingDetails(false);
        console.log(error);
      }
    },
    [accountId360, refreshMailVueSphere]
  );

  useEffect(() => {
    getMailsInbox();
  }, [getMailsInbox, refreshMailVueSphere]);

  return (
    <div>
      {detailState ? (
        <DetailsMessage
          messageDetails={detailsMail}
          getDetailsMessageInbox={getDetailsMessageInbox}
          ListAccounts={dataAccounts}
          detailsMail={detailsMail}
          setDetailsMail={setDetailsMail}
          page={pageDetail}
          setPage={setPageDetail}
          accountId360={accountId360}
          idEmail360={idEmail360}
          // setModalOpen={setModalOpen}
          // openEditor={openEditor}
          // setopenEditor={setopenEditor}
          // openTasksDrawer={openTasksDrawer}
          // setOpenTasksDrawer={setOpenTasksDrawer}
          loadingDetails={loadingDetails}
          type="360"
          setDetailState={setDetailState}
        />
      ) : (
        <Space direction="vertical">
          {!!dataAccounts && !!dataAccounts.length && !!selectedAccount && (
            <Select
              showSearch
              filterOption={(input, option) =>
                (option?.label ?? "")
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
              options={dataAccounts?.map((account) => ({
                value: account.value,
                label: account.label,
              }))}
              onChange={(value) => setSelectedAccount(value)}
              value={selectedAccount}
            />
          )}
          {/* <Divider orientation="left">Mails</Divider> */}
          <Table
            loading={loading}
            // rowSelection={rowSelection}
            columns={columns}
            dataSource={dataInboxByFamily}
            showSizeChanger={false}
            pagination={{
              pageSize: 20,
              total:
                metaMailByFamily?.total === 0 ? 1 : metaMailByFamily?.total,
              showSizeChanger: false,
              onChange: (page) => {
                setPage(page);
              },
              showTotal: (total, range) =>
                `${range[0]}-${range[1]} of ${total} items`,
            }}
            rowClassName={(_, index) => (index === 5 ? "" : "clickable-row")}
            onRow={(record) => {
              return {
                onClick: () => {
                  setAccountId360(record.accountId);
                  getDetailsMessageInbox(record.key, record.accountId);
                  setDetailState(true);
                  dispatch(setNumberEmailThread(record.nbr));

                  setIdEmail360(record.key);
                },
                // onMouseEnter: () => setHoverKey(record.key),
                // onMouseLeave: () => setHoverKey(null),
              };
            }}
            size="small"
            scroll={{
              y: windowSize?.height - 360,
            }}
          />
        </Space>
      )}
    </div>
  );
};

export default Mails;
