import { forwardRef, useEffect, useState } from "react";
import { Image, Skeleton } from "antd";
import { URL_ENV } from "index";
import { handleDownloadFile } from "pages/layouts/chat/utils/ConversationUtils";
import { isFileImage } from "pages/tasks/helpers/isFileImage";

const ImageRender = forwardRef(
  (
    { source = "chat", file, index, array, height, width, fromInfo = false },
    ref
  ) => {
    const [src, setSrc] = useState(null);
    const [currentFile, setCurrentFile] = useState(null);

    const handlePreviewVisibleChange = () => {
      let time;
      setSrc(
        source === "activities"
          ? `${URL_ENV?.REACT_APP_BASE_URL}${file?.path}`
          : `${
              URL_ENV?.REACT_APP_OAUTH_CHAT_API +
              process.env.REACT_APP_SUFFIX_CHAT_GROUP_STORAGE +
              array[index]?.path
            }`
      );
      clearTimeout(time);
      time = setTimeout(() => {
        let imagePreview = document.getElementsByClassName(
          "ant-image-preview-operations"
        )[0];

        let downloadChild = document.createElement("div");
        let nameFile = document.createElement("div");
        downloadChild.setAttribute("id", "download_option");
        nameFile.setAttribute("id", "name_option");

        downloadChild?.classList?.add("ant-image-preview-operations-operation");
        if (nameFile) {
          nameFile.style.alignItems = "start";
          nameFile?.classList?.add("flex-1");
          nameFile?.classList?.add("ml-3");
        }
        downloadChild.innerHTML = ` <span
       role="img"
       aria-label="swap"
       class="anticon anticon-swap ant-image-preview-operations-icon">
       <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" height="1.1em" width="1.1em" stroke="currentColor" >
       <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3" />
     </svg>
     
     </span>`;
        nameFile.innerHTML = `
          <p class=" underline whitespace-normal  block    text-sm  ">
         ${source === "activities" ? file?.fileName : file.file_name}
          <svg
            stroke="currentColor"
            fill="none"
            stroke-width="0"
            viewBox="0 0 15 15"
            height="1em"
            width="1em"
            xmlns="http://www.w3.org/2000/svg">
            <path
              d="M9.875 7.5C9.875 8.81168 8.81168 9.875 7.5 9.875C6.18832 9.875 5.125 8.81168 5.125 7.5C5.125 6.18832 6.18832 5.125 7.5 5.125C8.81168 5.125 9.875 6.18832 9.875 7.5Z"
              fill="currentColor"></path>
          </svg>
          ${file.size}
        </p>
     `;

        downloadChild?.addEventListener("click", (e) => {
          handleDownloadFile(e, array[index], undefined);
        });

        if (!document.getElementById("download_option")) {
          imagePreview?.appendChild(downloadChild);

          imagePreview?.appendChild(nameFile);
        }
        clearTimeout(time);
      }, 10);
    };

    useEffect(() => {
      setSrc(null);
    }, [currentFile]);

    return (
      <Image
        ref={ref}
        height={height ?? 320}
        className={`${
          fromInfo ? "object-contain" : "object-cover"
        } max-h-72 max-w-80 space-x-2  py-2`}
        width={width ?? undefined}
        key={`index_image${index}`}
        placeholder={
          <Skeleton.Image
            className="h-32 w-40"
            key={`index_image${index}`}
            active
          />
        }
        preview={
          isFileImage(file)
            ? {
                src,
                scaleStep: 3,
              }
            : false
        }
        fallback={
          <Skeleton.Image
            className="h-32 w-40"
            key={`index_image${index}`}
            active
          />
        }
        src={
          source === "activities"
            ? isFileImage(file)
              ? `${URL_ENV?.REACT_APP_BASE_URL}${file?.path}`
              : process.env.PUBLIC_URL + "/avatar/attach-file.png"
            : `${
                URL_ENV?.REACT_APP_OAUTH_CHAT_API +
                process.env.REACT_APP_SUFFIX_CHAT_GROUP_STORAGE +
                (parseFloat(file?.size?.split(" ")[0]) < 20
                  ? file.path
                  : file.thumbnail_url)
              }`
        }
        onChange={() => {
          setCurrentFile(file?._id);
          const time = setTimeout(() => {
            handlePreviewVisibleChange();
            clearTimeout(time);
          }, 1);
        }}
        onClick={() => {
          if (isFileImage(file)) {
            setCurrentFile(file?._id);
            const time = setTimeout(() => {
              handlePreviewVisibleChange();
              clearTimeout(time);
            }, 1);
          } else {
            window.open(
              `${URL_ENV?.REACT_APP_BASE_URL}${file?.path}`,
              "_blank"
            );
          }
        }}
      />
    );
  }
);

export default ImageRender;
