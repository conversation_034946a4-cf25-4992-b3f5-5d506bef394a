import { memo, useCallback, useEffect, useMemo, useState } from "react";
import {
  Badge,
  Button,
  Popover,
  Select,
  Space,
  Typography,
  Cascader,
} from "antd";
import {
  DownOutlined,
  FilterOutlined,
  FilterTwoTone,
  PlusOutlined,
} from "@ant-design/icons";
import { toastNotification } from "components/ToastNotification";
import { useTranslation } from "react-i18next";
import {
  getFilterFieldsFamily,
  saveFilterFamily,
} from "pages/clients&users/services/services";
import { FaRegTrashCan } from "react-icons/fa6";
import { configComparison, CustomizeFieldForFilterValue } from "./filterConfig";
import { uuid } from "pages/layouts/chat/utils/ConversationUtils";
import SavedFilters from "./SavedFilters";
import { Refs_IDs } from "components/tour/tourConfig";
import "./index.css";

const FilterFamilyTable = ({
  familyId,
  disabled,
  setDynamicFilter,
  dynamicFilter,
  handlePipelineChange,
  setTicketsFolderId = () => {},
  tableRef,
  setIsFirstRender,
}) => {
  //
  const [t] = useTranslation("common");
  const { SHOW_CHILD } = Cascader;
  //
  const [fields, setFields] = useState([]);
  const [config, setConfig] = useState({});
  const [fieldsOptions, setFieldsOptions] = useState({});
  const [filters, setFilters] = useState([]);
  //
  // console.log({ filters });
  //
  const fetchFilterFamily = useCallback(async () => {
    try {
      const {
        data: { data, config },
      } = await getFilterFieldsFamily(familyId /*pipelineId*/);
      if (!data) {
        setFields([]);
        setConfig({});
        setFieldsOptions({});
        return;
      }
      if (config?.last_filter?.length) {
        setDynamicFilter(config.last_filter);
        setFilters(config?.last_filter);
      }
      const { result, fieldsOptions } = await handleFamilyFilter(
        data,
        config,
        t
      );
      setFields(result);
      setConfig(config);
      setFieldsOptions(fieldsOptions);
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? err.message : err);
    } finally {
      setIsFirstRender(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [familyId, /*pipelineId,*/ t]);

  useEffect(() => {
    fetchFilterFamily();
  }, [fetchFilterFamily]);
  //
  const scrollToTopTable = () => {
    if (tableRef?.current) {
      tableRef.current.scrollTo({
        index: 0,
        behavior: "smooth",
      });
    }
  };
  //
  const applyFilter = () => {
    handlePipelineChange(null);
    setTicketsFolderId("");
    setDynamicFilter(filters);
    scrollToTopTable();
  };
  //
  const autoSaveLastFilter = async () => {
    if (JSON.stringify(config?.last_filter) === JSON.stringify(dynamicFilter))
      return;
    try {
      const formData = new FormData();
      formData.append("family_id", familyId);
      formData.append("type", "auto");
      formData.append("filter", JSON.stringify(dynamicFilter));
      await saveFilterFamily(formData);
      setConfig((prev) => ({ ...prev, last_filter: dynamicFilter }));
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? err.message : err);
    }
  };
  //
  const handleAddFilter = () => {
    const firstGroup = fields?.[0] || [];
    const firstField = firstGroup?.options?.[0] || {};
    setFilters([
      ...filters,
      {
        id: uuid(),
        logicalOperator: !filters.length ? "where" : "and",
        field: firstField.value,
        fieldType: firstField.fieldType,
        comparison: configComparison(firstField.fieldType, t)?.[0]?.value,
        value: null,
      },
    ]);
  };
  //
  const handleDeleteFilter = (id) => {
    let newFilter = filters.filter((filter) => filter.id !== id);
    if (newFilter.length > 0 && filters[0].id === id) {
      newFilter[0] = { ...newFilter[0], logicalOperator: "where" };
    }
    if (dynamicFilter.length) {
      setDynamicFilter(newFilter);
      scrollToTopTable();
    }
    setFilters(newFilter);
  };
  //
  const handleLogicalOperator = (value, id) => {
    let check = false;
    const updatedFilters = filters.map((filter) => {
      if (filter.id === id || check) {
        check = true;
        return {
          ...filter,
          logicalOperator: value,
        };
      } else return filter;
    });
    setFilters(updatedFilters);
  };
  //
  const handleFilterFieldChange = (value, option, id) => {
    const updatedFilters = filters.map((filter) =>
      filter.id === id
        ? {
            ...filter,
            field: value,
            fieldType: option.fieldType,
            comparison:
              option?.value === "stages"
                ? "contain_any_of"
                : configComparison(option.fieldType, t)?.[0]?.value,
            value: null,
            ...(filter?.fieldType?.includes("date") ||
            filter?.fieldType?.includes("time")
              ? { dateTimeValue: null }
              : {}),
          }
        : filter
    );
    setFilters(updatedFilters);
  };
  //
  const handleComparisonChange = (value, id) => {
    const updatedFilters = filters.map((filter) =>
      filter.id === id
        ? {
            ...filter,
            comparison: value,
            value:
              value === "is_blank" || value === "is_not_blank"
                ? null
                : (value !== "contain_any_of" ||
                    value !== "does_not_contain_any_of") &&
                  Array.isArray(filter.value)
                ? filter.value?.[0]
                : (value === "contain_any_of" ||
                    value === "does_not_contain_any_of") &&
                  !!filter.value
                ? [filter.value]
                : filter.value,
          }
        : filter
    );
    setFilters(updatedFilters);
  };
  //
  const handleValueChange = (value, id) => {
    // console.log({ value });
    const updatedFilters = filters.map((filter) =>
      filter.id === id
        ? {
            ...filter,
            value: value,
            //   filter?.fieldType?.includes("date") ||
            //   filter?.fieldType?.includes("time")
            //     ? dateTimeValue
            //     : value,
            // ...(filter?.fieldType?.includes("date") ||
            // filter?.fieldType?.includes("time")
            //   ? { dateTimeValue: value }
            //   : {}),
          }
        : filter
    );
    setFilters(updatedFilters);
  };
  //
  const displaySavedFiltersList = (isOnlySelect = false) => (
    /*canSaveFilter &&*/ <SavedFilters
      filters={filters}
      setFilters={setFilters}
      setDynamicFilter={setDynamicFilter}
      dynamicFilter={dynamicFilter}
      savedFilters={config?.filters ?? []}
      // lastFilter={config?.last_filter ?? []}
      isOnlySelect={isOnlySelect}
      familyId={familyId}
      setConfig={setConfig}
      applyFilter={applyFilter}
    />
  );
  //
  //
  const filterContent = useMemo(
    () => (
      <div className="flex flex-col space-y-6 py-5 pl-4">
        {
          !!filters.length && displaySavedFiltersList()
          // <div className="flex justify-end pr-8">{displaySavedFiltersList}</div>
        }
        <div
          className="filter-family-block relative flex flex-col space-y-2.5 overflow-y-auto pr-4"
          style={{ width: "43rem", maxHeight: "20rem" }}
        >
          {!!filters.length ? (
            filters.map((filter, i) => (
              <Space.Compact block key={filter.id}>
                <Select
                  style={{
                    width: "4.5rem",
                  }}
                  suffixIcon={
                    filter.logicalOperator === "where" ? null : <DownOutlined />
                  }
                  disabled={filter.logicalOperator === "where" || i > 1}
                  value={filter.logicalOperator}
                  options={[
                    {
                      value: "and",
                      label: t("filterFamily.and"),
                    },
                    {
                      value: "or",
                      label: t("filterFamily.or"),
                    },
                    ...(filter.logicalOperator === "where"
                      ? [
                          {
                            value: "where",
                            label: t("filterFamily.where"),
                          },
                        ]
                      : []),
                  ]}
                  onChange={(value) => handleLogicalOperator(value, filter.id)}
                />
                <Select
                  showSearch
                  popupMatchSelectWidth={false}
                  dropdownStyle={{
                    maxWidth: "16.5rem",
                  }}
                  style={{
                    width: "11rem",
                  }}
                  value={filter.field}
                  options={fields}
                  onChange={(value, option) =>
                    handleFilterFieldChange(value, option, filter.id)
                  }
                  optionFilterProp="label"
                  filterOption={(input, option) => {
                    if (typeof option.label === "string") {
                      return option.label
                        .toLowerCase()
                        .includes(input.toLowerCase());
                    }
                    return false;
                  }}
                />
                {filter.field !== "stages" && (
                  <Select
                    popupMatchSelectWidth={false}
                    style={{
                      width: "8rem",
                    }}
                    value={filter.comparison}
                    options={configComparison(filter.fieldType, t)}
                    onChange={(value) =>
                      handleComparisonChange(value, filter.id)
                    }
                  />
                )}
                {filter.field !== "stages" ? (
                  <CustomizeFieldForFilterValue
                    fieldType={filter.fieldType}
                    onChange={(value) => handleValueChange(value, filter.id)}
                    options={fieldsOptions?.[filter.field] || []}
                    value={filter.value}
                    dateTimeValue={filter.dateTimeValue}
                    configDateTime={{
                      dateFormat: config?.date_format,
                      timeFormat: config?.time_format,
                    }}
                    isMultiple={
                      filter.comparison === "contain_any_of" ||
                      filter.comparison === "does_not_contain_any_of"
                    }
                    isRange={filter.comparison === "is_within"}
                    isDisabled={
                      filter.comparison === "is_not_blank" ||
                      filter.comparison === "is_blank"
                    }
                    width={"16rem"}
                  />
                ) : (
                  <Cascader
                    style={{
                      width: "24rem",
                    }}
                    options={fieldsOptions?.[filter.field] || []}
                    onChange={(value, selectedOptions) =>
                      handleValueChange(value, filter.id)
                    }
                    value={filter?.value}
                    multiple
                    maxTagCount="responsive"
                    expandTrigger="hover"
                    showCheckedStrategy={SHOW_CHILD}
                    showSearch={{
                      filter: (inputValue, path) =>
                        path.some(
                          (option) =>
                            option.label
                              .toLowerCase()
                              .indexOf(inputValue.toLowerCase()) > -1
                        ),
                    }}
                  />
                )}
                <Button
                  icon={<FaRegTrashCan />}
                  onClick={() => handleDeleteFilter(filter.id)}
                />
              </Space.Compact>
            ))
          ) : (
            <div className="flex items-center justify-between">
              <span className="text-slate-500">
                {t("filterFamily.noFilterAdded")}
              </span>
              {displaySavedFiltersList(true)}
            </div>
          )}
        </div>
        <div className="flex items-center justify-between space-x-4 pr-7">
          <div className="flex space-x-2">
            <Button
              icon={<PlusOutlined style={{ fontSize: 12 }} />}
              style={{ paddingLeft: 8, paddingRight: 8 }}
              onClick={handleAddFilter}
            >
              {t("filterFamily.addFilter")}
            </Button>
            {!!filters.length && (
              <Button
                type="text"
                style={{ paddingLeft: 8, paddingRight: 8 }}
                onClick={() => {
                  setFilters([]);
                  dynamicFilter.length && setDynamicFilter([]);
                  scrollToTopTable();
                }}
              >
                {t("filterFamily.resetFilters")}
              </Button>
            )}
          </div>
          {!!filters.length && (
            <Button
              type="primary"
              // style={{ paddingLeft: 8, paddingRight: 8 }}
              disabled={
                JSON.stringify(filters) === JSON.stringify(dynamicFilter)
              }
              onClick={applyFilter}
            >
              {t("filterFamily.apply")}
            </Button>
          )}
        </div>
      </div>
    ),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [config, fields, fieldsOptions, filters, dynamicFilter]
  );
  //
  return (
    <Popover
      arrow={false}
      trigger="click"
      placement="bottomLeft"
      content={filterContent}
      overlayInnerStyle={{ padding: 2 }}
      onOpenChange={(open) => !open && autoSaveLastFilter()}
    >
      <Button
        ref={Refs_IDs.families_filter_icon}
        disabled={disabled || !fields.length}
        icon={
          <Badge dot={!!dynamicFilter.length}>
            {disabled || !fields.length ? (
              <FilterOutlined />
            ) : (
              <FilterTwoTone />
            )}
          </Badge>
        }
        type="text"
        style={{ paddingLeft: 8, paddingRight: 8 }}
      >
        <Space size={4}>
          {disabled || !fields.length ? (
            t("filterFamily.filter")
          ) : (
            <Typography.Link>{t("filterFamily.filter")}</Typography.Link>
          )}

          {!!dynamicFilter.length && (
            <span class="inline-flex items-center rounded-lg bg-blue-100 px-1.5 py-0.5 text-xs font-semibold text-blue-700 ring-1 ring-inset ring-blue-700/10">
              {dynamicFilter.length}
            </span>
          )}
        </Space>
      </Button>
    </Popover>
  );
};
//
export const handleFamilyFilter = async (data, config, t) => {
  const fieldsOptions = {};

  const result = data.map((group) => {
    const options = group.fields
      .filter(
        (field) =>
          !field.hidden &&
          field.field_type !== "autocomplete" &&
          field.field_type !== "range"
      )
      .map((field) => {
        if (field.field_type === "country") {
          fieldsOptions[field.id] = config?.countries || [];
        } else if (field.family_id) {
          fieldsOptions[field.id] =
            config?.list_values_family?.[field.family_id]?.map((e) => ({
              ...e,
              value: e.id,
            })) || [];
        } else if (field.module) {
          if (field?.label === "pipeline" && field.module === "Pipeline") {
            fieldsOptions["stages"] = (config?.stages || [])
              .map((pipeline) => {
                if (!pipeline?.stages?.length) return false;
                return {
                  value: pipeline.id,
                  label: pipeline.label,
                  children: pipeline.stages.map((stage) => ({
                    label: stage.label,
                    value: stage.id,
                  })),
                };
              })
              .filter(Boolean);
          } else
            fieldsOptions[field.id] =
              config?.module_values?.[field.module]?.map((e) => ({
                ...e,
                value: e.id,
              })) || [];
        } else if (field.field_list_value && field.field_list_value.length) {
          fieldsOptions[field.id] = field.field_list_value?.map((e) => ({
            ...e,
            value: e.id,
          }));
        }

        return {
          label:
            field?.label === "pipeline" && field.module === "Pipeline"
              ? `${field.alias} | ${t("pipeline.stage")}`
              : field.alias,
          value: field?.label === "pipeline" ? "stages" : field.id,
          fieldType: field.field_type,
        };
      });

    return {
      label: <span className="font-semibold">{group.label}</span>,
      title: group.label,
      options,
    };
  });
  // add Timestamps fields manually
  const Timestamps = {
    label: <span className="font-semibold">Timestamps</span>,
    title: "Timestamps",
    options: [
      {
        fieldType: "select",
        label: t("contacts.createdBy"),
        value: "created_by",
        disabled: !config?.list_values_family?.[4]?.length,
      },
      {
        fieldType: "date_time",
        label: t("contacts.createdAt"),
        value: "created_at",
      },
      {
        fieldType: "date_time",
        label: t("contacts.updatedAt"),
        value: "updated_at",
      },
    ],
  };

  if (config?.list_values_family?.[4]?.length)
    fieldsOptions["created_by"] =
      config?.list_values_family?.[4]?.map((e) => ({
        ...e,
        value: e.id,
      })) || [];

  result.push(Timestamps);

  // console.log({ result, fieldsOptions });

  return { result, fieldsOptions };
};
//
export const handleStagesOptions = (options) =>
  (options ?? []).map((pipeline) => ({
    label: <span className="font-semibold">{pipeline.label}</span>,
    title: pipeline.id,
    options: pipeline?.stages?.length
      ? pipeline.stages.map((stage) => ({
          label: stage.label,
          value: stage.id,
        }))
      : [],
  }));
//
export default memo(FilterFamilyTable);
