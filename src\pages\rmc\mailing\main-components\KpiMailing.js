import { memo, useCallback, useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { Skeleton } from "antd";
import CardStat from "pages/components/CardStat";
import MainService from "services/main.service";

const KpiMailing = ({ usedAccount }) => {
  //
  const [t] = useTranslation("common");
  //
  const refreshKPI = useSelector((state) => state.mailReducer.refreshKPI);
  const refreshMailInbox = useSelector(
    (state) => state.mailReducer.refreshMailInbox
  );
  //
  const [kpiMail, setKPIMail] = useState({});
  const [loadingKPI, setLoadingKPI] = useState(false);

  //
  const getKPI = useCallback(async () => {
    try {
      setLoadingKPI(true);
      const response = await MainService.getKPIByUser(usedAccount.value);
      if (response.status === 200) {
        setKPIMail(response?.data?.data);
      }
    } catch (err) {
      console.log(err);
    } finally {
      setLoadingKPI(false);
    }
  }, [usedAccount.value]);

  useEffect(() => {
    getKPI();
  }, [getKPI, refreshKPI, refreshMailInbox]);
  //
  return (
    <div className="flex flex-grow space-x-2 px-4">
      {loadingKPI ? (
        <Skeleton.Input active block />
      ) : Object.entries(kpiMail)?.length > 0 ? (
        Object.entries(kpiMail).map(([key, subject], i) => (
          <div className="custom-card-height flex-grow " key={i}>
            <CardStat
              key={i}
              item={{
                title: (
                  <span className="font-semibold uppercase">
                    {key === "Total_unassigned_emails"
                      ? t("mailing.notAssigned")
                      : key === "Assigned_emails_closed"
                      ? t("mailing.closed")
                      : key === "Assigned_emails_inProgress"
                      ? t("mailing.InProgress")
                      : key === "Assigned_emails_to_process"
                      ? t("mailing.PROCESS")
                      : key === "Total_assigned_emails"
                      ? t("mailing.Assigned")
                      : t("mailing.outOfTime")}
                  </span>
                ),
                key: key,
                value: subject,
              }}
            />
          </div>
        ))
      ) : null}
    </div>
  );
};

export default memo(KpiMailing);
