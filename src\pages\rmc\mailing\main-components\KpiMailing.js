import { memo, useCallback, useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { Skeleton, Tag } from "antd";
import MainService from "services/main.service";

const KpiMailing = ({ filter, setFilter, usedAccount }) => {
  const [t] = useTranslation("common");
  const refreshKPI = useSelector((state) => state.mailReducer.refreshKPI);
  const refreshMailInbox = useSelector(
    (state) => state.mailReducer.refreshMailInbox
  );

  const [kpiMail, setKPIMail] = useState({});
  const [loadingKPI, setLoadingKPI] = useState(false);

  useEffect(() => {
    let mounted = true;
    (async () => {
      try {
        setLoadingKPI(true);
        const response = await MainService.getKPIByUser(usedAccount.value);
        if (mounted && response.status === 200) {
          setKPIMail(response?.data?.data);
        }
      } catch (err) {
        console.log(err);
      } finally {
        setLoadingKPI(false);
      }
    })();
    return () => {
      mounted = false;
    };
  }, [usedAccount.value, refreshKPI, refreshMailInbox]);

  const handleChange = useCallback(
    (checked, key) => {
      setFilter((prev = {}) => {
        let next = { ...prev };
        const toggleArrayKey = (itemKey, value) => {
          const arr = prev[itemKey] ?? [];
          const updated = checked
            ? [...arr, value]
            : arr.filter((v) => v !== value);
          if (updated.length) {
            next[itemKey] = updated;
          } else {
            const { [itemKey]: _, ...rest } = next;
            next = rest;
          }
        };
        switch (key) {
          case "Assigned_emails_closed":
            toggleArrayKey("state", "closed");
            break;
          case "Assigned_emails_inProgress":
            toggleArrayKey("state", "in-progress");
            break;
          case "Assigned_emails_to_process":
            toggleArrayKey("state", "processed");
            break;
          case "Kpi_overdue_email":
            if (checked) next.chrono_filter = 2;
            else {
              const { chrono_filter, ...rest } = next;
              next = rest;
            }
            break;
          case "Total_assigned_emails":
            if (checked) next.assigned = 1;
            else {
              const { assigned, ...rest } = next;
              next = rest;
            }
            break;
          case "Total_unassigned_emails":
            if (checked) next.assigned = 5;
            else {
              const { assigned, ...rest } = next;
              next = rest;
            }
            break;
          default:
            break;
        }
        return next;
      });
    },
    [setFilter]
  );

  const KPI_LABELS = {
    Total_unassigned_emails: t("mailing.notAssigned"),
    Assigned_emails_closed: t("mailing.closed"),
    Assigned_emails_inProgress: t("mailing.InProgress"),
    Assigned_emails_to_process: t("mailing.PROCESS"),
    Total_assigned_emails: t("mailing.Assigned"),
    Kpi_overdue_email: t("mailing.outOfTime"),
  };

  const isChecked = (key) => {
    switch (key) {
      case "Kpi_overdue_email":
        return filter.chrono_filter === 2;
      case "Assigned_emails_closed":
        return filter.state?.includes("closed");
      case "Assigned_emails_inProgress":
        return filter.state?.includes("in-progress");
      case "Assigned_emails_to_process":
        return filter.state?.includes("processed");
      case "Total_assigned_emails":
        return filter.assigned === 1;
      case "Total_unassigned_emails":
        return filter.assigned === 5;
      default:
        return false;
    }
  };

  return (
    <div className="flex flex-grow space-x-2 px-4">
      {loadingKPI ? (
        <Skeleton.Input active block />
      ) : (
        Object.entries(kpiMail)?.map(([key, value]) => {
          const checked = isChecked(key);
          return (
            <Tag.CheckableTag
              key={key}
              style={{
                flexGrow: 1,
                display: "flex",
                alignItems: "center",
                borderWidth: 0.5,
                borderColor: "rgb(226 232 240)",
                boxShadow:
                  "0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",
              }}
              onChange={(checked) => handleChange(checked, key)}
              checked={checked}
            >
              <div className="flex flex-grow justify-between text-xs">
                <span
                  className={`mt-[1px] font-semibold ${
                    !checked ? "text-slate-500" : ""
                  }`}
                >
                  {KPI_LABELS[key] || key}
                </span>
                <span>{value}</span>
              </div>
            </Tag.CheckableTag>
          );
        })
      )}
    </div>
  );
};

export default memo(KpiMailing);
