import { CloseOutlined, SearchOutlined, UpOutlined } from "@ant-design/icons";
import {
  Avatar,
  Button,
  Checkbox,
  Divider,
  Drawer,
  Dropdown,
  Form,
  Input,
  List,
  Skeleton,
  Space,
  Spin,
  Switch,
  Typography,
} from "antd";
import React, { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import customParseFormat from "dayjs/plugin/customParseFormat";
import { motion, useAnimation } from "framer-motion";
import { useNavigate } from "react-router-dom";

import { toastNotification } from "../../components/ToastNotification";
import Header from "../../components/configurationHelpDesk/Header";
import { createVisio } from "../../new-redux/actions/visio.actions/createVisio";
import { getVisio } from "../../new-redux/actions/visio.actions/getVisio";
import { updateVisio } from "../../new-redux/actions/visio.actions/updateVisio";
import MainService from "../../services/main.service";
import CreateTaskForm from "../tasks/CreateTaskForm";
import InstantVisio from "./InstantVisio";
import dayjs from "dayjs";
import InfiniteScroll from "react-infinite-scroll-component";
import TableVisio from "./TableVisio";
import TableView from "../clients&users/components/TableView";
import { FiCopy, FiArrowUpRight, FiSave } from "react-icons/fi";
import { MdStart } from "react-icons/md";

dayjs.extend(customParseFormat);

const columns = [
  {
    title: "Title",
    dataIndex: "title",
    render: (text) => <a>{text}</a>,
  },
  {
    title: "Secret Code",
    dataIndex: "password",
  },
  {
    title: "Link",
    dataIndex: "meeting_url",
    render: (text) => (
      <Typography.Paragraph className="w-80 cursor-pointer truncate break-all hover:underline">
        <a target="_blank" href={text}>
          {text}
        </a>
      </Typography.Paragraph>
    ),
  },
  {
    title: "Date",
    dataIndex: "date",
  },
  {
    title: "Actions",
    dataIndex: "actions",
  },
];

const VisioList = () => {
  const [form] = Form.useForm();

  const [t] = useTranslation("common");

  const [newVisio, setNewVisio] = useState("");

  const [openDrawer, setOpenDrawer] = useState(false);
  const [openTaskDrawer, setOpenTaskDrawer] = useState(false);

  const [singleTaskData, setSingleTaskData] = useState({});
  const [showGuests, setShowGuests] = useState(false);
  const [countGuests, setCountGuests] = useState(0);
  const [showDescription, setShowDescription] = useState(false);
  const [filterTable, setFilterTable] = useState(0);
  const [propsTitle, setPropsTitle] = useState("");
  const [switchViews, setSwitchViews] = useState("");
  const [checkedItems, setCheckedItems] = useState([]);
  const [checkedFollowers, setCheckedFollowers] = useState([]);
  const [tasksData, setTasksData] = useState([]);
  const [tasksTypes, setTasksTypes] = useState([]);
  const [loadTasksTypes, setLoadTasksTypes] = useState(false);
  const [loadTasks, setLoadTasks] = useState(false);
  const [loadGuests, setLoadGuests] = useState(false);
  const [loadSpecificTask, setLoadSpecificTask] = useState(false);
  const [createAndUpdateTaskLoading, setCreateAndUpdateTaskLoading] =
    useState(false);
  const [companiesList, setCompaniesList] = useState([]);
  const [taskToUpdate, setTaskToUpdate] = useState(null);
  const [ownersList, setOwnersList] = useState([]);
  const [guestsList, setGuestsList] = useState([]);
  const [guestsSearchQuery, setGuestsSearchQuery] = useState("");
  const [ownersSearchQuery, setOwnersSearchQuery] = useState("");
  const [showTime, setShowTime] = useState(false);
  const [guestsListLimit, setGuestsListLimit] = useState(15);
  const [guestsListPage, setGuestsListPage] = useState(1);
  const [hasMoreGuests, setHasMoreGuests] = useState(true);
  const [selectedStartDate, setSelectedStartDate] = useState("");
  const [selectedEndDate, setSelectedEndDate] = useState("");
  const [selectedStartTime, setSelectedStartTime] = useState("");
  const [selectedEndTime, setSelectedEndTime] = useState("");
  const [modulesList, setModulesList] = useState([]);
  const [loadModuleList, setLoadModuleList] = useState(false);
  const [loadOwners, setLoadOwners] = useState(false);

  const [idType, setIdType] = useState("");

  const [numberOfPage, setNumberOfPage] = useState(1);

  const [planToTaskCheck, setPlanToTaskCheck] = useState(false);

  const [secretCodeVisio, setSecretCodeVisio] = useState("");

  const [visioToUpdate, setVisioToUpdate] = useState(null);

  const [selectedTaskType, setSelectedTaskType] = useState(null);

  const [files, setFiles] = useState([]);
  const [filesIds, setFilesIds] = useState([]);

  const [addOnsValues, setAddOnsValues] = useState({
    description: "",
    note: "",
  });

  const dispatch = useDispatch();
  const navigate = useNavigate();

  const newVisioReduxUrl = useSelector(
    (state) => state?.visio?.new?.meeting_url
  );
  const newVisioId = useSelector((state) => state?.visio?.new?.id);
  const newVisioTitle = useSelector((state) => state?.visio?.new?.title);

  const listVisio = useSelector((state) => state?.visio?.visioList);
  const totalVisio = useSelector((state) => state?.visio?.total);
  const pageLimitVisio = useSelector((state) => state?.visio?.pageLimit);
  const loadingVisio = useSelector((state) => state?.visio?.isLoading);

  const oneVisioId = useSelector((state) => state?.visio?.oneVisio?.id);
  const oneVisioPwd = useSelector((state) => state?.visio?.oneVisio?.password);
  const oneVisioTitle = useSelector((state) => state?.visio?.oneVisio?.title);
  const error500 = useSelector(
    (state) => state?.visio?.errors?.response?.status
  );

  // Create task API.
  const createTask = async (payload) => {
    try {
      setCreateAndUpdateTaskLoading(true);
      const response = await MainService.createNewTask(payload);
      if (response?.status === 200) {
        setCreateAndUpdateTaskLoading(false);
       // console.log("gggggggg", response?.data?.data);
        let newArr = response?.data?.data;
        setTasksData([...tasksData, newArr]);
       // console.log("resss", response, "tasks", tasksData);
        setOpenTaskDrawer(false);
        toastNotification(
          "success",
          "Task was added successfully",
          "bottomRight"
        );
        resetDrawerForm();
        setCheckedItems([]);
        setCheckedFollowers([]);
        navigate(`/tasks`);
      }
    } catch (error) {
      setCreateAndUpdateTaskLoading(false);
      // setOpenTaskDrawer(false);
      // resetDrawerForm();
      toastNotification("error", t("toasts.somethingWrong"));
      console.log(`Error ${error}`);
    }
  };

  // Update task API.
  const updateTask = async (taskId, payload) => {
    try {
      setCreateAndUpdateTaskLoading(true);
      const response = await MainService.updateTask(taskId, payload);
      if (response?.status === 200) {
        setCreateAndUpdateTaskLoading(false);
        let itemIndex =
          tasksData && tasksData.findIndex((element) => element?.id == taskId);
        let newArray =
          tasksData &&
          tasksData.map((element, i) =>
            i === itemIndex ? response?.data?.data : element
          );
        setTasksData(newArray);
        setOpenTaskDrawer(false);
        toastNotification(
          "success",
          "Task updated successfully",
          "bottomRight"
        );
        resetDrawerForm();
        setCheckedItems([]);
        navigate(`/tasks`);
      }
    } catch (error) {
      setCreateAndUpdateTaskLoading(false);
      toastNotification("error", t("toasts.somethingWrong"));
      console.log(`Error ${error}`);
    }
  };

  // Trigger after submitting the form and verifying data successfully
  const onFinish = (values) => {
    let formData = new FormData();
    formData.append("label", values?.title);
    formData.append("priority", values?.priority ? values?.priority : "");
    formData.append(
      "Reminder",
      values?.reminder && values?.addonAfter
        ? `${values?.reminder} ${values?.addonAfter}`
        : ""
    );
    formData.append(
      "start_date",
      values?.startDate ? dayjs(values?.startDate).format("YYYY-MM-DD") : ""
    );
    formData.append(
      "start_time",
      values?.startTime ? dayjs(values?.startTime).format("HH:mm") : ""
    );
    formData.append(
      "end_time",
      values?.endTime ? dayjs(values?.endTime).format("HH:mm") : ""
    );
    formData.append(
      "end_date",
      values?.endDate ? dayjs(values?.endDate).format("YYYY-MM-DD") : ""
    );
    formData.append("owner_id", values?.owner);
    // formData.append("module_id", values?.module ? values?.module.toString() : []);
    checkedItems &&
      checkedItems.forEach((el) => formData.append("guests[]", el?.id));
    checkedFollowers &&
      checkedFollowers.forEach((el) => formData.append("followers[]", el?.id));
    formData.append("tasks_type_id", values?.taskType);
    formData.append(
      "description",
      values?.description ? values?.description : ""
    );
    formData.append("note", addOnsValues?.note ? addOnsValues?.note : "");

    selectedTaskType === 1 && formData.append("location", values?.location);

    files &&
      files.length > 0 &&
      files.forEach((file) => formData.append("upload[]", file?.id));

    // Handle Files.
    // if (Object.keys(singleTaskData).length === 0) {
    //   values?.upload?.fileList &&
    //     values?.upload?.fileList.forEach((item, i) => {
    //       formData.append(`upload[]`, item?.originFileObj);
    //     });
    // } else if (Object.keys(singleTaskData).length !== 0) {
    //   singleTaskData?.upload !== null
    //     ? singleTaskData?.upload.forEach((item, i) => {
    //         formData.append(`upload[]`, item);
    //       })
    //     : values?.upload?.fileList.forEach((item, i) => {
    //         formData.append(`upload[]`, item?.originFileObj);
    //       });
    // }

    // Trigger the create or the update API.
    if (Object.keys(singleTaskData).length === 0) {
      createTask(formData);
    } else if (
      Object.keys(singleTaskData).length !== 0 &&
      taskToUpdate !== null
    ) {
      updateTask(taskToUpdate, formData);
    }
  };

  // Fill task label  input on select task type function.
  const prefillTaskLabel = (value) => {
    let index =
      tasksTypes && tasksTypes.findIndex((element) => element?.id == value);

    if (index > -1) {
      form.setFieldsValue({
        title: tasksTypes && tasksTypes[index]?.label,
      });
    }
  };

  // Get types API.
  const getTasksTypes = async () => {
    try {
      setLoadTasksTypes(true);
      const response = await MainService.getTasksTypes();
      setTasksTypes(response?.data?.data?.tasks_type);
      setLoadTasksTypes(false);
    } catch (error) {
      console.log(`Error ${error}`);
      setLoadTasksTypes(false);
    }
  };

  // Get owners API.
  const getOwners = useCallback(async () => {
    try {
      let formData = new FormData();
      setLoadOwners(true);
      formData.append("family_id[]", 4);
      const response = await MainService.getFamilyOptions(formData);
      setOwnersList(response?.data?.data);
      setLoadOwners(false);
    } catch (error) {
      setLoadOwners(false);
      console.log(`Error ${error}`);
    }
  }, [ownersSearchQuery]);

  // Get guests API.
  const getGuests = useCallback(async () => {
    try {
      setLoadGuests(true);
      const response = await MainService.getGuestsList(
        guestsListLimit,
        guestsListPage,
        guestsSearchQuery
      );
      setHasMoreGuests(response?.data?.last_page > guestsListPage);
      guestsSearchQuery !== ""
        ? setGuestsList(response.data.data)
        : setGuestsList([...guestsList, ...response.data.data]);
      setLoadGuests(false);
    } catch (error) {
      console.log(`Error ${error}`);
      setLoadGuests(false);
    }
  }, [guestsListPage, guestsSearchQuery]);

  // Get modules API.
  const getModulesList = async () => {
    try {
      setLoadModuleList(true);
      const response = await MainService.getModules();
      setModulesList(response?.data?.data);
      setLoadModuleList(false);
    } catch (error) {
      setLoadModuleList(false);
      console.log(`Error ${error}`);
    }
  };

  // Get contacts API.
  const getContacts = useCallback(async () => {
    try {
      const response = await MainService.getCompanies(5, 1, "");
      setCompaniesList(response?.data?.data?.data);
    } catch (error) {
      console.log(`Error ${error}`);
    }
  }, []);

  // Get Specific Task by Id API.
  const getSpecificTask = useCallback(async () => {
    try {
      //console.log("get spec task-----------------");
      setLoadSpecificTask(true);
      const response = await MainService.getSpecificTask(taskToUpdate);
      setSingleTaskData(response?.data?.data);
      setCheckedItems(
        response?.data?.data?.guests != null
          ? response?.data?.data?.guests
          : checkedItems
      );
      setCheckedFollowers(
        response?.data?.data?.followers !== null
          ? response?.data?.data?.followers
          : checkedFollowers
      );
      setLoadSpecificTask(false);
    } catch (error) {
      console.log(`Error ${error}`);
      setLoadSpecificTask(false);
    }
  }, [taskToUpdate]);

  useEffect(() => {
    if (
      selectedStartDate != "" &&
      selectedStartTime != "" &&
      selectedEndTime != "" &&
      selectedEndDate != ""
    ) {
      form.setFieldsValue({
        startDate: dayjs(selectedStartDate, "YYYY-MM-DD"),
        startTime: dayjs(selectedStartTime, "HH:mm"),
        endTime: dayjs(selectedEndTime, "HH:mm"),
        endDate: dayjs(selectedEndDate, "YYYY-MM-DD"),
      });
    }
  }, [
    selectedStartDate,
    selectedStartTime,
    selectedEndTime,
    selectedEndDate,
    form,
  ]);

  // Owners list.
  const ownersOptionsInSelect =
    ownersList &&
    ownersList
      .sort((a, b) => a.label.localeCompare(b.label))
      .map((element) => ({
        label: (
          <>
            <Avatar
              style={{
                backgroundColor: "#c41d7f",
                marginRight: "10px",
              }}
              size="small"
              icon={element?.label.split("")[0].toUpperCase()}
            />
            {element?.label}
          </>
        ),
        value: element?.id,
      }));

  // Module options.
  const moduleOptions =
    modulesList &&
    modulesList
      .sort((a, b) => a.label.localeCompare(b.label))
      .map((element) => ({
        label: element?.label,
        value: element?.id,
      }));

  // Handle check guests in list.
  const handleCheckedItems = (target, key) => {
    let guestIndex =
      guestsList && guestsList.findIndex((element) => element?.id == key);
    if (target) {
      if (guestIndex > -1) {
        setCheckedItems([...checkedItems, guestsList[guestIndex]]);
      }
    } else {
      let newArr =
        checkedItems && checkedItems.filter((element, i) => element?.id != key);
      setCheckedItems(newArr);
    }
  };

  // Handle uncheck guests in list.
  const uncheckGuest = (key) => {
    let newArr =
      checkedItems && checkedItems.filter((element, i) => element?.id !== key);
    setCheckedItems(newArr);
  };

  // Handle check followers in list.
  const handleCheckedFollowers = (target, key) => {
    let guestIndex =
      guestsList && guestsList.findIndex((element) => element?.id == key);
    if (target) {
      if (guestIndex > -1) {
        setCheckedFollowers([...checkedFollowers, guestsList[guestIndex]]);
      }
    } else {
      let newArr =
        checkedFollowers &&
        checkedFollowers.filter((element, i) => element?.id != key);
      setCheckedFollowers(newArr);
    }
  };

  // Handle uncheck followers in list.
  const uncheckFollower = (key) => {
    let newArr =
      checkedFollowers &&
      checkedFollowers.filter((element, i) => element?.id !== key);
    setCheckedFollowers(newArr);
  };

  // Guests list component.
  const guestsDropdownContent = () => (
    <div
      id="scrollableDiv"
      style={{
        overflow: "auto",
      }}>
      <InfiniteScroll
        dataLength={guestsList && guestsList.length}
        hasMore={hasMoreGuests}
        next={() => {
          setGuestsListPage(guestsListPage + 1);
        }}
        height={400}
        scrollableTarget="scrollableDiv"
        loader={
          <Spin
            indicator={
              <Skeleton
                avatar
                paragraph={{
                  rows: 1,
                }}
                active
              />
            }
          />
        }
        endMessage={<Divider plain>{t("tasks.endListIndicator")}</Divider>}>
        {/* It is all, nothing more 🤐 */}
        <List
          size="medium"
          className="membersList"
          header={
            <Input
              placeholder={t("tasks.listSearchPlaceholder")}
              prefix={<SearchOutlined />}
              onChange={(e) => setGuestsSearchQuery(e.target.value)}
              allowClear
            />
          }
          dataSource={guestsList && guestsList}
          renderItem={(item) => (
            <List.Item key={item?.id}>
              <Checkbox
                value={item?.id}
                onChange={(e) => {
                  handleCheckedItems(e.target.checked, e.target.value);
                }}
                defaultChecked={
                  checkedItems &&
                  checkedItems.length > 0 &&
                  checkedItems.map((el) => el?.id).includes(item?.id.toString())
                }>
                <Avatar
                  icon={
                    item?.name == null
                      ? "?"
                      : item?.name.split("")[0].toUpperCase()
                  }
                  style={{ backgroundColor: "#87d068", marginRight: "10px" }}
                />
                {item?.name}
              </Checkbox>
            </List.Item>
          )}
        />
      </InfiniteScroll>
    </div>
  );

  // Followers list component.
  const followersDropdownContent = () => (
    <div
      id="scrollableDiv"
      style={{
        overflow: "auto",
      }}>
      <InfiniteScroll
        dataLength={ownersList && ownersList.length}
        hasMore={hasMoreGuests}
        next={() => {
          setGuestsListPage(guestsListPage + 1);
        }}
        height={400}
        scrollableTarget="scrollableDiv"
        inverse={false}
        loader={
          <Spin
            indicator={
              <Skeleton
                avatar
                paragraph={{
                  rows: 1,
                }}
                active
              />
            }
          />
        }
        endMessage={<Divider plain>{t("tasks.endListIndicator")}</Divider>}>
        {/* It is all, nothing more 🤐 */}
        <List
          size="medium"
          className="membersList"
          header={
            <Input
              placeholder={t("tasks.listSearchPlaceholder")}
              prefix={<SearchOutlined />}
              onChange={(e) => setGuestsSearchQuery(e.target.value)}
              allowClear
            />
          }
          dataSource={guestsList && guestsList}
          renderItem={(item) => (
            <List.Item key={item?.id}>
              <Checkbox
                value={item?.id}
                onChange={(e) => {
                  handleCheckedFollowers(e.target.checked, e.target.value);
                }}
                defaultChecked={
                  checkedFollowers &&
                  checkedFollowers.length > 0 &&
                  checkedFollowers
                    .map((el) => el?.id)
                    .includes(item?.id.toString())
                }>
                <Avatar
                  icon={
                    item?.name == null
                      ? "?"
                      : item?.name.split("")[0].toUpperCase()
                  }
                  style={{ backgroundColor: "#87d068", marginRight: "10px" }}
                />
                {item?.name}
              </Checkbox>
            </List.Item>
          )}
        />
      </InfiniteScroll>
    </div>
  );

  // Get specific guest name from list to be displayed as avatar.
  const getGuestNameForAvatar = (guestId) => {
    let guestIndex =
      guestsList && guestsList.findIndex((element) => element?.id == guestId);
    if (guestIndex > -1) {
      return guestsList[guestIndex]?.name;
    }
  };

  // Reset drawer form.
  const resetDrawerForm = () => {
    setSingleTaskData({});
    setCheckedItems([]);
    setCheckedFollowers([]);
    setTaskToUpdate(null);
    form.resetFields();
    form.setFieldsValue({});
    setSelectedStartDate("");
    setSelectedEndDate("");
    setSelectedStartTime("");
    setSelectedEndTime("");
  };

  const getListVisio = () => {
    // mainService.getVisioList()
    //     .then((res) => {
    //         console.log('VISIO--------', res.data.data)
    //     })
    //     .catch((err) => {
    //         console.log(err)
    //     })
  };

  useEffect(() => {
    // getListVisio()
    // dispatch(getVisio(numberOfPage))
  }, [dispatch, numberOfPage]);

  useEffect(() => {
    if (propsTitle !== "") {
      form.setFieldValue({
        title: propsTitle,
      });
    }
  }, [form, propsTitle]);

  useEffect(() => {
    if (openTaskDrawer == true) {
      getOwners();
    }
  }, [getOwners, ownersSearchQuery, openTaskDrawer]);

  useEffect(() => {
    if (openTaskDrawer == true) {
      getGuests();
    }
  }, [getGuests, guestsListPage, guestsSearchQuery, openTaskDrawer]);

  useEffect(() => {
    if (openTaskDrawer === true) {
      getContacts();
      getModulesList();
    }
  }, [openTaskDrawer]);

  useEffect(() => {
    if (taskToUpdate !== null) {
      getSpecificTask();
    }
  }, [taskToUpdate, getSpecificTask]);

  useEffect(() => {
    if (
      taskToUpdate != null &&
      openTaskDrawer == true &&
      Object.keys(singleTaskData).length != 0
    ) {
      console.log(singleTaskData);
      form.setFieldsValue({
        taskType: 33,
        title: singleTaskData?.label,
        priority: singleTaskData?.priority,
        module:
          singleTaskData?.module_id != null
            ? singleTaskData?.module_id.map((el) => Number(el))
            : [],
        startDate:
          singleTaskData?.start_date &&
          dayjs(singleTaskData?.start_date, "YYYY-MM-DD"),
        startTime:
          singleTaskData?.start_time &&
          dayjs(singleTaskData?.start_time, "HH:mm"),
        endDate:
          singleTaskData?.end_date &&
          dayjs(singleTaskData?.end_date, "YYYY-MM-DD"),
        endTime:
          singleTaskData?.end_time && dayjs(singleTaskData?.end_time, "HH:mm"),
        reminder:
          singleTaskData?.Reminder && singleTaskData?.Reminder.split(" ")[0],
        addonAfter:
          singleTaskData?.Reminder && singleTaskData?.Reminder.split(" ")[1],
        owner: singleTaskData?.owner_id?.id,
        guests: checkedItems,
        followers: checkedFollowers,
        description: singleTaskData?.description,
        note: singleTaskData?.note,
        upload: singleTaskData?.upload,
      });
      setAddOnsValues({ ...addOnsValues, note: singleTaskData?.note });
      setFiles(singleTaskData?.upload);
    }
  }, [taskToUpdate, openTaskDrawer, singleTaskData]);

  let currentDate = new Date();
  // let datetime = currentDate.getDate() + "/"
  //     + (currentDate.getMonth() + 1) + "/"
  //     + currentDate.getFullYear() + "-"
  //     + currentDate.getHours() + ":"
  //     + currentDate.getMinutes() + ":"
  //     + currentDate.getSeconds();

  const options = {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
  };
  let datetime = currentDate
    .toLocaleDateString("fr-FR", options)
    .replace(" ", "-");

  let secretCode = Math.random().toString(36).substring(2, 7);

  const createNewVisio = () => {
    // mainService.createVisio({
    //     "title": "testSoumaya",
    //     "password": "123456"
    // }).then((res) => {
    //     console.log(res.data.data)
    //     setNewVisio(res.data.data)
    // }).catch((err) => {
    //     console.log(err)
    // })
    // dispatch(createVisio({
    //     "title": `Visio-${datetime}`,
    //     "password": secretCode
    // }))
  };

  useEffect(() => {
    getTasksTypes();
  }, []);

  function handleClick() {
    setOpenDrawer(true);
    createNewVisio();
    if (error500 === 500) setOpenDrawer(false);
  }

  function redirectToMeet() {
    if (newVisioReduxUrl) window.open(newVisioReduxUrl, "_blank");
    else window.open("https://visio.unified.test", "_blank");
  }

  const MenuProps = {
    items: [
      { label: "Start now", key: "start_now" },
      { label: "Plan a task", key: "plan_task" },
    ],
    onClick: (e) => {
      if (e.key === "start_now") redirectToMeet();
      else if (e.key === "plan_task") {
        //setOpenDrawer(false);
        setOpenTaskDrawer(true);
      }
    },
  };

  // const onChange = (checked) => {
  //     console.log(`switch to ${checked}`);
  //     checked === true && setOpenTaskDrawer(true)
  //     checked === false && setOpenTaskDrawer(false)
  // };

  useEffect(() => {
    planToTaskCheck === true && setOpenTaskDrawer(true);
    planToTaskCheck === false && setOpenTaskDrawer(false);
  }, [setOpenTaskDrawer, planToTaskCheck]);

  const updateExistingVisio = () => {
    let urlencoded = new URLSearchParams();
    urlencoded.append(
      "title",
      visioToUpdate == null ? newVisioTitle : oneVisioTitle
    );
    urlencoded.append("password", secretCodeVisio);
    visioToUpdate == null
      ? dispatch(updateVisio(newVisioId, urlencoded))
      : dispatch(updateVisio(oneVisioId, urlencoded));
  };

  return (
    <>
      <div style={{ margin: 8 }}>
        <Space direction="vertical" style={{ width: "100%" }}>
          <Header
            active={"5"}
            handleAdd={handleClick}
            btnText={t("visio.addVisio")}
            // disabled='true'
          />
          <TableVisio
            //columns={columns}
            //showColumns
            dataTable={listVisio}
            isLoading={loadingVisio}
            total={totalVisio}
            pageLimit={pageLimitVisio}
            setNumberOfPage={setNumberOfPage}
            numberOfPage={numberOfPage}
            setPageLimit={10}
            setTaskToUpdate={setTaskToUpdate}
            setVisioToUpdate={setVisioToUpdate}
            setOpenDrawer={setOpenDrawer}
            setPlanToTaskCheck={setPlanToTaskCheck}
          />

          {/* <TableView
                        columns={columns}
                        //showColumns
                        dataTable={listVisio}
                        isLoading={loadingVisio}
                        total={totalVisio}
                        pageLimit={pageLimitVisio}
                        setNumberOfPage={setNumberOfPage}
                        setPageLimit={10}
                    /> */}
        </Space>

        <Drawer
          title={
            planToTaskCheck === false
              ? t("visio.createNewVisio")
              : taskToUpdate !== null
              ? "Update task"
              : t("visio.visioToTask")
          }
          footer={
            <Space
              direction="horizontal"
              size="middle"
              style={{ padding: "0.250rem" }}>
              <Button
                htmlType="reset"
                onClick={() => {
                  setOpenDrawer(false);
                  setOpenTaskDrawer(false);
                  form.resetFields();
                  setPlanToTaskCheck(false);
                  setSingleTaskData({});
                  setTaskToUpdate(null);
                  //   setShowGuests(false);
                  //   setShowDescription(false);
                  //   setCountGuests(0);
                  //   resetDrawerForm();
                }}>
                {t("fields_management.drawerCloseBtn")}
              </Button>

              {/* {openTaskDrawer === false && <Dropdown.Button
                                type="primary"
                                //loading={loadingSubmit}
                                trigger={["click"]}
                                placement="topLeft"
                                icon={<UpOutlined />}
                                menu={MenuProps}

                                onClick={() => {
                                    updateExistingVisio()
                                }}
                            >
                                Create
                            </Dropdown.Button>} */}

              {/* {
                                openTaskDrawer === true && */}
              <Button
                type="primary"
                form="form"
                htmlType="submit"
                loading={createAndUpdateTaskLoading}
                icon={<FiSave />}
                disabled={openTaskDrawer === false ? true : false}>
                {t("fields_management.drawerOkBtn")}
              </Button>
              {/* }

                             {
                                openTaskDrawer === false &&
                                <Button
                                    type="primary"
                                    onClick={() =>
                                        updateExistingVisio()
                                    }
                                >
                                    {t("fields_management.drawerOkBtn")}
                                </Button>
                            } */}

              <Button
                type="primary"
                onClick={() => console.log("start")}
                icon={<FiArrowUpRight />}
                // icon={<MdStart />}
              >
                {"Start"}
              </Button>
              <Button
                type="primary"
                // onClick={copyInfo}
                icon={<FiCopy />}>
                Copy
              </Button>
            </Space>
          }
          size="large"
          placement="right"
          onClose={() => {}}
          closeIcon={
            <CloseOutlined
              onClick={() => {
                setOpenDrawer(false);
                setOpenTaskDrawer(false);
                form.resetFields();
                setPlanToTaskCheck(false);
                setSingleTaskData({});
                setTaskToUpdate(null);
              }}
            />
          }
          open={openDrawer}>
          <InstantVisio
            newVisio={newVisio}
            setSecretCodeVisio={setSecretCodeVisio}
            visioToUpdate={visioToUpdate}
          />
          {taskToUpdate === null ? (
            <>
              <Divider />
              Plan to task:{" "}
              <Switch
                checked={planToTaskCheck}
                onChange={(c) => setPlanToTaskCheck(c)}
              />
            </>
          ) : (
            ""
          )}
          {openTaskDrawer === true && (
            <>
              <Divider />

              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{
                  opacity: openTaskDrawer === true ? 1 : 0,

                  y: openTaskDrawer === true ? 0 : -20,

                  transition: { duration: 0.2, ease: "easeInOut" },
                }}>
                <CreateTaskForm
                  form={form}
                  onFinish={onFinish}
                  loadSpecificTask={loadSpecificTask}
                  prefillTaskLabel={prefillTaskLabel}
                  tasksTypes={tasksTypes}
                  singleTaskData={singleTaskData}
                  selectedStartDate={selectedStartDate}
                  setSelectedStartDate={setSelectedStartDate}
                  setSelectedStartTime={setSelectedStartTime}
                  selectedEndTime={selectedEndTime}
                  setSelectedEndTime={setSelectedEndTime}
                  selectedEndDate={selectedEndDate}
                  selectedStartTime={selectedStartTime}
                  setSelectedEndDate={setSelectedEndDate}
                  ownersOptionsInSelect={ownersOptionsInSelect}
                  setOwnersSearchQuery={setOwnersSearchQuery}
                  loadOwners={loadOwners}
                  moduleOptions={moduleOptions}
                  loadModuleList={loadModuleList}
                  checkedItems={checkedItems}
                  guestsDropdownContent={guestsDropdownContent}
                  setGuestsSearchQuery={setGuestsSearchQuery}
                  getGuestNameForAvatar={getGuestNameForAvatar}
                  uncheckGuest={uncheckGuest}
                  checkedFollowers={checkedFollowers}
                  followersDropdownContent={followersDropdownContent}
                  uncheckFollower={uncheckFollower}
                  // setQuillDescription={setQuillDescription}
                  // setQuillNote={setQuillNote}
                  source="visio"
                  idType={idType}
                  setIdType={setIdType}
                  addOnsValues={addOnsValues}
                  setAddOnsValues={setAddOnsValues}
                  selectedTaskType={selectedTaskType}
                  setSelectedTaskType={setSelectedTaskType}
                  files={files}
                  setFiles={setFiles}
                  filesIds={filesIds}
                  setFilesIds={setFilesIds}
                  taskToUpdate={taskToUpdate}
                />
              </motion.div>
            </>
          )}{" "}
        </Drawer>

        {/* <Drawer
                    title="Plan a task"
                    footer={
                        <Space direction="horizontal" size="middle" style={{ padding: "0.250rem" }}>
                            <Button
                                htmlType="reset"
                                onClick={() => {
                                    setOpenTaskDrawer(false);
                                    setShowGuests(false);
                                    setShowDescription(false);
                                    setCountGuests(0);
                                    resetDrawerForm();
                                }}
                            >
                                {t("fields_management.drawerCloseBtn")}
                            </Button>
                            <Button
                                type="primary"
                                form="form"
                                htmlType="submit"
                                loading={createAndUpdateTaskLoading}
                            >
                                {t("fields_management.drawerOkBtn")}
                            </Button>

                        </Space>
                    }

                    onClose={() => { }}
                    size="large"
                    closeIcon={
                        <CloseOutlined
                            onClick={() => {
                                setOpenTaskDrawer(false);
                            }}
                        />
                    }
                    open={openTaskDrawer}
                >
                    <CreateTaskForm
                        form={form}
                        onFinish={onFinish}
                        loadSpecificTask={loadSpecificTask}
                        prefillTaskLabel={prefillTaskLabel}
                        tasksTypes={tasksTypes}
                        singleTaskData={singleTaskData}
                        disableNextDates={disableNextDates}
                        setSelectedStartDate={setSelectedStartDate}
                        setSelectedStartTime={setSelectedStartTime}
                        disableNextHours={disableNextHours}
                        setSelectedEndTime={setSelectedEndTime}
                        disablePrevHours={disablePrevHours}
                        disablePrevMinutes={disablePrevMinutes}
                        disablePreviousDates={disablePreviousDates}
                        setSelectedEndDate={setSelectedEndDate}
                        ownersOptionsInSelect={ownersOptionsInSelect}
                        setOwnersSearchQuery={setOwnersSearchQuery}
                        loadOwners={loadOwners}
                        moduleOptions={moduleOptions}
                        loadModuleList={loadModuleList}
                        checkedItems={checkedItems}
                        guestsDropdownContent={guestsDropdownContent}
                        setGuestsSearchQuery={setGuestsSearchQuery}
                        getGuestNameForAvatar={getGuestNameForAvatar}
                        uncheckGuest={uncheckGuest}
                        checkedFollowers={checkedFollowers}
                        followersDropdownContent={followersDropdownContent}
                        uncheckFollower={uncheckFollower}
                        source="visio"
                        idType={idType}
                        setIdType={setIdType}
                    />
                </Drawer> */}
      </div>
    </>
  );
};

export default VisioList;
