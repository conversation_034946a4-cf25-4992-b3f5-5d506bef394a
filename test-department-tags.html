<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Department Tags Overflow Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
        }
        .test-case {
            background: white;
            border: 1px solid #d9d9d9;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
        }
        .test-title {
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }
        .department-container {
            display: flex;
            align-items: center;
            gap: 4px;
            min-height: 22px;
            max-width: 250px;
            border: 1px dashed #ccc;
            padding: 8px;
            border-radius: 4px;
        }
        .tag {
            background: #f0f8ff;
            color: #1890ff;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            border: 1px solid #91d5ff;
            white-space: nowrap;
            margin: 0;
        }
        .tag.more {
            background: #f0f0f0;
            color: #666;
            border-color: #d9d9d9;
            cursor: pointer;
        }
        .tag.more:hover {
            background: #e6e6e6;
        }
        .popover {
            position: absolute;
            background: white;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            padding: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            z-index: 1000;
            max-width: 200px;
            display: none;
        }
        .popover.show {
            display: block;
        }
        .popover-title {
            font-weight: bold;
            margin-bottom: 8px;
        }
        .popover-content {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>Department Tags Overflow Test</h2>
        
        <div class="test-case">
            <div class="test-title">Cas 1: Tous les tags visibles (largeur suffisante)</div>
            <div class="department-container">
                <span class="tag">Sales</span>
                <span class="tag">Marketing</span>
                <span class="tag">Support</span>
            </div>
        </div>
        
        <div class="test-case">
            <div class="test-title">Cas 2: Overflow avec tag "+2" (largeur limitée)</div>
            <div class="department-container">
                <span class="tag">Sales</span>
                <span class="tag">Marketing</span>
                <span class="tag more" onmouseover="showPopover(this)" onmouseout="hidePopover()">+3</span>
            </div>
        </div>
        
        <div class="test-case">
            <div class="test-title">Cas 3: Beaucoup de départements avec overflow</div>
            <div class="department-container">
                <span class="tag">Development</span>
                <span class="tag more" onmouseover="showPopover(this)" onmouseout="hidePopover()">+5</span>
            </div>
        </div>
        
        <div class="test-case">
            <div class="test-title">Cas 4: Un seul département très long</div>
            <div class="department-container">
                <span class="tag">Customer Success Management</span>
            </div>
        </div>
        
        <div class="popover" id="popover">
            <div class="popover-title">Départements</div>
            <div class="popover-content">
                <span class="tag">Support</span>
                <span class="tag">Technical</span>
                <span class="tag">Quality Assurance</span>
                <span class="tag">DevOps</span>
                <span class="tag">Business Intelligence</span>
            </div>
        </div>
        
        <div style="margin-top: 20px; color: #666; font-size: 14px;">
            <h3>Fonctionnalités implémentées :</h3>
            <ul>
                <li>✅ Calcul automatique de l'espace disponible</li>
                <li>✅ Affichage des tags visibles selon la largeur</li>
                <li>✅ Tag "+X" pour les éléments cachés</li>
                <li>✅ Popover au survol du tag "+X"</li>
                <li>✅ Hauteur constante pour tous les éléments</li>
                <li>✅ Pas de retour à la ligne</li>
            </ul>
        </div>
    </div>

    <script>
        function showPopover(element) {
            const popover = document.getElementById('popover');
            const rect = element.getBoundingClientRect();
            
            popover.style.left = rect.left + 'px';
            popover.style.top = (rect.bottom + 5) + 'px';
            popover.classList.add('show');
        }
        
        function hidePopover() {
            const popover = document.getElementById('popover');
            popover.classList.remove('show');
        }
    </script>
</body>
</html>
