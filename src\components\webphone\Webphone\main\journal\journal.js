import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import { Divider, Empty, Skeleton, Spin, Typography } from "antd";
import { getLogs } from "../../../../../new-redux/actions/voip.actions/getLogs";
import { useTranslation } from "react-i18next";
import { seenCallsOrVoicesOrGroups } from "../../../../../pages/voip/services/services";
import { RESET_MISSED_CALL } from "../../../../../new-redux/constants";
import { handleLogsDataNew } from "../../../../../pages/voip/helpers/helpersFunc";
import { RenderLogWebPhone } from "../../components/DisplayCallerInfo";
import "../../../../../pages/voip/index.css";
import { isGuestConnected } from "utils/role";
//
const loaderTemplate = (len = 2) => (
  <div className="ml-2 mt-2">
    {Array.from({ length: len }, (_, index) => (
      <Skeleton
        key={index}
        avatar
        paragraph={{
          rows: 0,
        }}
        active
      />
    ))}
  </div>
);

const Journal = ({
  SegmentedValue,
  scrollableDivRef,
  setUpWebphone,
  handleActionWebPhone,
}) => {
  // const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const location = useLocation();
  const isGuest = isGuestConnected();

  const user = useSelector((state) => state.user.user);
  const posteVoip = `${user?.extension}`;
  const journalTab = useSelector((state) => state.voip.logs);
  const loading = useSelector((state) => state.voip.loading);
  const { nbrMissedCalls } = useSelector(({ voip }) => voip);

  const [data, setData] = useState([]);
  //
  // fetech log
  useEffect(() => {
    if (!journalTab.length) {
      dispatch(getLogs(true));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (SegmentedValue === "missed") {
      const missedCalls = journalTab?.filter(
        (call) => call?.dst === posteVoip && call?.disposition !== "ANSWERED"
      );
      setData(handleLogsDataNew(missedCalls, user, t, "webPhone"));
    } else setData(handleLogsDataNew(journalTab, user, t, "webPhone"));
  }, [SegmentedValue, journalTab, posteVoip, t, user]);

  useEffect(() => {
    seenCallsOrVoicesOrGroups("missed_call");
  }, []);

  useEffect(() => {
    nbrMissedCalls && dispatch({ type: RESET_MISSED_CALL });
  }, [dispatch, nbrMissedCalls]);

  const handleNavigate = () => {
    if (SegmentedValue === "missed")
      navigate("/telephony/callLog", {
        state: { filter: ["incoming_missed", "incoming_failed"] },
      });
    else navigate("/telephony/callLog");

    setTimeout(() => {
      setUpWebphone(false);
    }, 500);
  };
  //
  const handleDisplayElementInfo = (name, info) => {
    handleActionWebPhone("display_info", {
      name: name,
      id: info?.id,
      familyId: info?.familyId,
    });
  };
  //
  const handleAddItem = (familyId, phoneNbr) => {
    handleActionWebPhone("create_element", { familyId, number: phoneNbr });
  };

  return loading && journalTab?.length === 0 ? (
    <div className="mt-4 h-[17.5rem]">{loaderTemplate(4)}</div>
  ) : !loading && journalTab?.length === 0 ? (
    <div className="flex h-[292px] justify-center p-4 text-sm font-semibold">
      <Empty
        image={Empty.PRESENTED_IMAGE_SIMPLE}
        description={t("voip.noCallLogFound")}
      />
    </div>
  ) : (
    <Spin spinning={loading}>
      <div
        ref={scrollableDivRef}
        className="mt-2 h-[288px] space-y-1 overflow-y-auto px-0.5 "
      >
        <RenderLogWebPhone
          log={data}
          t={t}
          currentUser={user}
          handleDisplayElementInfo={handleDisplayElementInfo}
          handleAddItem={handleAddItem}
          isGuest={isGuest}
        />
        {data?.length && location.pathname !== "/telephony/callLog" ? (
          <div>
            <Divider plain>
              <Typography.Link
                style={{ fontSize: 12 }}
                onClick={handleNavigate}
              >
                {t("voip.moreHistory")}
              </Typography.Link>
            </Divider>
          </div>
        ) : (
          <div className="h-05" />
        )}
      </div>
    </Spin>
  );
};
export default Journal;
