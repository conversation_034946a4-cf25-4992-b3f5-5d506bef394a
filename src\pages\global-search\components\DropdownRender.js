// import { memo } from "react";
// import { Divider, Empty, List } from "antd";
// import InfiniteScroll from "react-infinite-scroll-component";
// import { loaderTemplate } from "pages/voip/directory/Directory";
// import { useTranslation } from "react-i18next";
// import FilterGlobalSearch from "./FilterGlobalSearch";
// import { URL_ENV } from "index";
// import { InfoCircleTwoTone } from "@ant-design/icons";
// import RenderSearchItems from "./render-search-items";
// import "../index.css";

// const DropdownRender = ({
//   data,
//   total,
//   setPage,
//   selectedFilter,
//   setSelectedFilter,
//   handleClickOnItem,
//   onlineUser,
//   handleJoinVisioMeeting,
// }) => {
//   //
//   const [t] = useTranslation("common");

//   //
//   const imgBaseUrl =
//     URL_ENV?.REACT_APP_BASE_URL + URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL;
//   //
//   console.log({ data });
//   //
//   const loadMoreData = () => {
//     const timer = setTimeout(() => {
//       setPage((p) => p + 1);
//       clearTimeout(timer);
//     }, 300);
//   };
//   //
//   return (
//     <>
//       <div className="relative flex w-full justify-between  py-3  pl-3 shadow-md">
//         {(!!data.length || !!selectedFilter.length) && (
//           <FilterGlobalSearch
//             selectedFilter={selectedFilter}
//             setSelectedFilter={setSelectedFilter}
//             setPage={setPage}
//           />
//         )}
//         {!!data.length && (
//           <div className="w-3/5">
//             <Divider
//               orientation="right"
//               style={{
//                 margin: "0px" /*padding: "0 6rem"*/,
//               }}
//             >
//               {`1-${data?.length} of ${
//                 data.length > total ? data.length : total
//               } items found`}
//             </Divider>
//           </div>
//         )}
//       </div>
//       <div
//         id="scrollableDiv-global-search"
//         className="dropDown-global-search relative max-h-96 overflow-y-auto py-0.5"
//       >
//         <InfiniteScroll
//           style={{ overflow: "unset" }}
//           dataLength={data.length}
//           next={loadMoreData}
//           hasMore={data.length < total}
//           loader={loaderTemplate}
//           scrollableTarget="scrollableDiv-global-search"
//         >
//           <div className="global-search-list ">
//             <List
//               // className="membersList cursor-pointer "
//               className="global-search-list cursor-pointer "
//               itemLayout="vertical"
//               dataSource={data}
//               locale={{
//                 emptyText: (
//                   <div className="flex justify-center p-4 text-sm font-semibold">
//                     <Empty
//                       image={Empty.PRESENTED_IMAGE_SIMPLE}
//                       description={
//                         "No results found for your search. Please try different keywords."
//                       }
//                     />
//                   </div>
//                 ),
//               }}
//               renderItem={(item, index) => (
//                 <RenderSearchItems
//                   item={item}
//                   t={t}
//                   imgBaseUrl={imgBaseUrl}
//                   handleClickOnItem={handleClickOnItem}
//                   onlineUser={onlineUser}
//                   handleJoinVisioMeeting={handleJoinVisioMeeting}
//                 />
//               )}
//             />
//           </div>
//         </InfiniteScroll>
//       </div>
//       {!!data.length && (
//         <div className="flex space-x-2 bg-neutral-100	 px-4 py-2 shadow-md">
//           <InfoCircleTwoTone style={{ fontSize: 16 }} />
//           <p className="font-semibold	">
//             If you click on an item, it will open here!
//           </p>
//         </div>
//       )}
//     </>
//   );
// };

// export default memo(DropdownRender);
