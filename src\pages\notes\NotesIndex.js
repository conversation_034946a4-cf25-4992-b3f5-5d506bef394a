import React, { useEffect, useState } from "react";
import ExternalNotes from "./ExternalNotes";
import EditorNote from "../components/TipTapEditor/EditorNote";
import TopBar from "./TopBar";
import EmptyPlace from "./EmptyPlace";
import { useSelector } from "react-redux";
import { <PERSON><PERSON>, <PERSON><PERSON>, message } from "antd";
import { useDispatch } from "react-redux";
import {
  removeIsNewNote,
  removeSelectedNote,
  saveSelfNoteSuccess,
  setNotesNotificationsCount,
  setNotesNotificationsList,
  setSelectedNote,
  triggerSaveNote,
} from "../../new-redux/actions/selfnotes.actions/selfnotes";
import useKeyDown from "../../custom-hooks/useKeyDown";
import { SaveOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import NoteAvatar from "pages/components/DetailsProfile/Activities/Notes/NoteAvatar";
import { useLocation } from "react-router-dom";
import MainService from "services/main.service";

function NotesIndex() {
  const [selectedNote, setClickedNote] = useState(null);
  const dispatch = useDispatch();
  const { t } = useTranslation("common");
  const selfNoteIsSaved = useSelector(
    (state) => state.selfNotesReducer.selfNoteIsSaved
  );
  const selfNoteErrorSaving = useSelector(
    (state) => state.selfNotesReducer.selfNoteErrorSaving
  );

  const selfNoteSelected = useSelector(
    (state) => state?.selfNotesReducer?.selectedNote
  );

  const notesRedux = useSelector((state) => state?.selfNotesReducer?.slefNotes);

  useEffect(() => {
    if (selfNoteSelected == null) {
      setClickedNote(null);
    }
  }, [selfNoteSelected]);

  useEffect(() => {
    MainService.setNotesRead()
      .then((res) => {
    
      })
      .catch((err) => {
        console.log("setNotesRead", err);
      });

    dispatch(setNotesNotificationsCount(0));
    dispatch(setNotesNotificationsList(null));
  }, []);

  const location = useLocation();

  const [displayBy, setDisplayBy] = useState("all");

  const updateDisplayBy = (value) => {
    /**
     *   <Space direction="vertical">
          <Radio value={0}>{t("selfNotes.allNotes")}</Radio>
          <Radio value={1}>{t("selfNotes.myNotes")}</Radio>
          <Radio value={2}>{t("selfNotes.sharedNotes")}</Radio>
          <Radio value={3}>{t("selfNotes.personalNotes")}</Radio>
          <Radio value={4}>{t("selfNotes.sharedWithMe")}</Radio>
        </Space>
     */

    switch (value) {
      case 0:
        setDisplayBy("all");
        break;
      case 1:
        setDisplayBy("my");
        break;
      case 2:
        setDisplayBy("shared");
        break;
      case 3:
        setDisplayBy("personal");
        break;
      case 4:
        setDisplayBy("sharedWithMe");
        break;
      default:
        setDisplayBy("all");
        break;
    }
  };

  // useEffect(() => {
  //   console.log("location state", location.state?.id);

  //   if (location.state?.id) {
  //     const note = notesRedux?.find((note) => note._id == location.state.id);
  //     console.log("note is changing", note);
  //     if (note) {
  //       console.log("note is here");
  //       dispatch(setSelectedNote(note));
  //       setClickedNote(note);
  //     } else {
  //       console.log("not receiveing state", location.state?.id);
  //     }
  //   }
  // }, [location]);

  const users = useSelector((state) => state.chat.userList);

  const currentUser = useSelector((state) => state.user.user);

  const selfNotes = useSelector((state) => state.selfNotesReducer.selfNotes);

  // const populateLocker = (lockerUuid, lockerId) => {
  //   // console.log("lockerUuid", lockerUuid);
  //   // console.log("lockerId", lockerId);

  //   if (lockerId == currentUser.id) {
  //     return {
  //       avatar: currentUser.avatar,
  //       name: currentUser.label,
  //       isYou: true,
  //     };
  //   }

  //   //from users find the uuid
  //   const locker = users.find((user) => user.uuid == lockerUuid);

  //   console.log("locker", locker);

  //   return {
  //     avatar: locker?.image,
  //     name: locker?.name,
  //     isYou: false,
  //   };
  // };

  useKeyDown(27, false, "keydown", () => {
    console.log("clickedNote", selectedNote);
    if (selectedNote?.isNew) {
      dispatch(removeIsNewNote());
    }
    setClickedNote(null);
    dispatch(removeSelectedNote());
  });
  // useEffect(() => {
  //   if (selfNoteIsSaved) {
  //     message.success("Note saved successfully");

  //     dispatch(saveSelfNoteSuccess());
  //   } else if (selfNoteErrorSaving) {
  //     message.error("Error saving note");
  //   }
  // }, [dispatch, selfNoteErrorSaving, selfNoteIsSaved]);

  // useEffect(() => {
  //   let time;
  //   const container = document.getElementById("container");
  //   if (container) {
  //     clearTimeout(time);

  //     time = setTimeout(() => {
  //       container.scrollTo({
  //         top: 0,
  //         behavior: "smooth",
  //       });
  //     }, 150);
  //   }
  //   return () => {
  //     clearTimeout(time);
  //   };
  // }, [selectedNote?._id]);

  useEffect(() => {
    const container = document.getElementById("container");
    if (container) {
      container.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    }
  }, [selectedNote?._id]);

  // useEffect(() => {
  //   console.log("self notes changed", selectedNote);
  // }, [selfNotes]);

  const [searchKeyWord, setSearchKeyWord] = useState("");

  const showDisplayBy = () => {
    const formatDisplayBy = (value) => {
      return (
        <div className="mx-2 mt-2 flex items-center text-sm font-semibold">
          <p>{value}</p>
        </div>
      );
    };

    switch (displayBy) {
      case "all":
        return formatDisplayBy(t("selfNotes.allNotes"));
      case "my":
        return formatDisplayBy(t("selfNotes.myNotes"));
      case "shared":
        return formatDisplayBy(t("selfNotes.sharedNotes"));
      case "personal":
        return formatDisplayBy(t("selfNotes.personalNotes"));
      case "sharedWithMe":
        return formatDisplayBy(t("selfNotes.sharedWithMe"));
      default:
        return formatDisplayBy(t("selfNotes.allNotes"));
    }
  };

  return (
    <div className="flex h-full items-center justify-between overflow-hidden">
      {/* Side bar */}
      <div className="flex h-full w-[410px] flex-col gap-y-1 bg-slate-50  ">
        {/* {showDisplayBy()} */}

        <TopBar
          selectedNote={selectedNote}
          setClickedNote={setClickedNote}
          searchKeyWord={searchKeyWord}
          setSearchKeyWord={setSearchKeyWord}
          displayBy={displayBy}
          showDisplayBy={showDisplayBy}
          updateDisplayBy={updateDisplayBy}
        />

        <ExternalNotes
          isExternal={true}
          selectedNote={selectedNote}
          setClickedNote={setClickedNote}
          searchKeyWord={searchKeyWord}
          displayBy={displayBy}
        />
      </div>
      {/* note */}

      <div className="relative h-full w-full ">
        {selectedNote ? (
          <div id="container" className=" flex h-full flex-col overflow-y-auto">
            {/* <div className="rounded-none border-0 bg-white">
              <Alert
                className="  top-0  m-1"
                type="info"
                showIcon
                message={
                  <p className="text-sm">{t("selfNotes.autoSaveEnabled")}</p>
                }

                // action={
                //   <Button
                //     type="link"
                //     shape="circle"
                //     size="large"
                //     onClick={() => {
                //       dispatch(triggerSaveNote());
                //     }}
                //     icon={<SaveOutlined />}
                //   >
                //     {t("wiki.Save")}
                //   </Button>
                // }
              />
            </div> */}

            {/* {selectedNote?.permission == 0 && (
              <Alert
                className="sticky top-0 z-50 mx-1 mt-1"
                type="warning"
                showIcon
                message={<p className="text-sm">{t("selfNotes.readOnly")}</p>}
              />
            )} */}
            {/* 
            {selectedNote?.is_locked && (
              <Alert
                className="sticky top-0 z-50 mx-1 mt-1"
                type="warning"
                showIcon
                message={
                  <p className="text-sm">
                    {t("selfNotes.noteLockedBy")}
                    <NoteAvatar
                      avatar={
                        populateLocker(
                          selectedNote.locker_uuid,
                          selectedNote.locker_id
                        ).avatar
                      }
                      name={
                        populateLocker(
                          selectedNote.locker_uuid,
                          selectedNote.locker_id
                        ).name
                      }
                      size="small"
                      // isYou={
                      //   populateLocker(
                      //     selectedNote.locker_uuid,
                      //     selectedNote.locker_id
                      //   ).isYou
                      // }
                    />
                    {
                      populateLocker(
                        selectedNote.locker_uuid,
                        selectedNote.locker_id
                      ).name
                    }{" "}
                    {populateLocker(
                      selectedNote.locker_uuid,
                      selectedNote.locker_id
                    ).isYou ? (
                      <>{t("selfNotes.youAreTheLocker")}</>
                    ) : (
                      <>{t("selfNotes.youAreNotTheLocker")}</>
                    )}
                  </p>
                }
              />
            )} */}
            <EditorNote
              selectedNote={selectedNote}
              setClickedNote={setClickedNote}
              cantEdit={selectedNote?.permission == 0}
            />
          </div>
        ) : (
          <EmptyPlace />
        )}
      </div>
    </div>
  );
}

export default NotesIndex;
