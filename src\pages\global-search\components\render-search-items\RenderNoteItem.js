import { memo } from "react";
import { Ava<PERSON>, Divider, List, Space } from "antd";
import { HiOutlinePencilAlt } from "react-icons/hi";
import { renderHighlight, renderTitle } from ".";
import { humanDate } from "pages/voip/helpers/helpersFunc";
import DisplayAvatar from "pages/voip/components/DisplayAvatar";

const RenderNoteItem = ({ item, t, imgBaseUrl, handleClickOnItem }) => {
  //
  const { id, content, owner, sharedWith, created_at, updated_at, highlight } =
    item;
  //
  return (
    <List.Item
      className="custom-list-item-global-search"
      style={{
        alignItems: "center",
      }}
      key={id}
      onClick={() => handleClickOnItem(`/notes`, id)}
    >
      <List.Item.Meta
        avatar={
          <DisplayAvatar
            size={44}
            icon={<HiOutlinePencilAlt style={{ fontSize: 23 }} />}
          />
        }
        title={renderTitle(content, "Note")}
        description={
          <div className="flex flex-col space-y-0.5">
            <Space size={2} split={<Divider type="vertical" />}>
              <p>{`${t("contacts.createdAt")} : ${humanDate(
                created_at,
                t,
                "table"
              )}`}</p>
              {!!updated_at && (
                <p>{`${t("contacts.updatedAt")} : ${humanDate(
                  updated_at,
                  t,
                  "table"
                )}`}</p>
              )}
            </Space>
            <Space size={2} split={<Divider type="vertical" />}>
              <Space size={2}>
                <p>{t("globalSearch.owner")}</p>
                <DisplayAvatar
                  cursor="help"
                  tooltip={true}
                  size={20}
                  name={owner?.label}
                  urlImg={!!owner?.avatar && `${imgBaseUrl}${owner?.avatar}`}
                />
              </Space>
              {!!sharedWith?.length && (
                <Space size={2}>
                  <p>
                    {t("tasks.followers", {
                      s: sharedWith?.length === 1 ? "" : "s",
                    })}
                  </p>
                  <Avatar.Group
                    maxCount={10}
                    maxStyle={{
                      backgroundColor: "rgb(30, 64, 175)",
                      color: "rgb(219, 234, 254)",
                      width: 20,
                      height: 20,
                      fontWeight: 600,
                      cursor: "pointer",
                      display: "flex",
                    }}
                    // max={{
                    //   count: 3,
                    //   style: {
                    //     backgroundColor: "rgb(30, 64, 175)",
                    //     color: "rgb(219, 234, 254)",
                    //     width: 20,
                    //     height: 20,
                    //     fontWeight: 600,
                    //     cursor: "pointer",
                    //     display: "flex",
                    //   },
                    // }}
                  >
                    {sharedWith?.map((item, index) => {
                      return (
                        <DisplayAvatar
                          key={index}
                          cursor="help"
                          tooltip={true}
                          size={20}
                          name={item?.label}
                          urlImg={
                            !!item?.avatar && `${imgBaseUrl}${item?.avatar}`
                          }
                        />
                      );
                    })}
                  </Avatar.Group>
                </Space>
              )}
            </Space>
            {renderHighlight(highlight)}
          </div>
        }
      />
    </List.Item>
  );
};

export default memo(RenderNoteItem);
