import React, { useEffect, useMemo, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import {
  getDataDashboard,
  setQueueInDashboard,
  setSelectedMail,
} from "../../new-redux/actions/dashboard.actions";
import {
  Card,
  Col,
  Row,
  Space,
  Tooltip,
  Typography,
  Statistic,
  Select,
  Tabs,
  DatePicker,
} from "antd";
import { useTranslation } from "react-i18next";
import { MdOutlineMarkEmailUnread, MdPhoneMissed } from "react-icons/md";
import { useLocation, useNavigate } from "react-router-dom";
import { getName } from "../layouts/chat/utils/ConversationUtils";
import { HiOutlineChatBubbleBottomCenterText } from "react-icons/hi2";
import { AvatarChat } from "../../components/Chat";
import {
  ArrowRightOutlined,
  CalendarOutlined,
  DashboardOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons";
import CardTask from "./components/CardTask";
import ItemHeader from "../components/DetailsProfile/ItemHeader";
import {
  CalendarSearchIcon,
  Mail,
  Phone,
  PhoneIncoming,
  PhoneMissed,
  Smartphone,
  Speech,
} from "lucide-react";
import { FcCallback } from "react-icons/fc";

import {
  AtSymbolIcon,
  ChatBubbleBottomCenterTextIcon,
  ChatBubbleLeftRightIcon,
  EnvelopeIcon,
  PhoneArrowDownLeftIcon,
} from "@heroicons/react/24/outline";
import { PhoneArrowUpRightIcon } from "@heroicons/react/20/solid";
import CountUp from "react-countup";
import dayjs from "dayjs";
import { TbMailForward } from "react-icons/tb";
import { URL_ENV } from "index";
import { generateAxios } from "services/axiosInstance";
import { SET_RMC } from "new-redux/constants";
import { toastNotification } from "components/ToastNotification";
import ChoiceIcons from "pages/components/ChoiceIcons";
import { rangePresets } from "pages/voip/helpers/helpersFunc";
import Meta from "antd/es/card/Meta";
import { familyIcons } from "pages/components/DetailsProfile/ViewSphere2";
import DashboardVoip from "components/itemsDashboard/DashboardVoip";
import CardVoip from "./components/CardVoip";
import CardQueue from "./components/CardQueue";
// import ListCardTask from "pages/components/ListCardTask";
const Home = ({ start, end, setStart, setEnd }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { pathname } = useLocation();
  const { user } = useSelector((state) => state.user);
  const {
    unreadMsgOneToOne,
    unreadMessagesInGroups,
    missedUnreturnedCalls,
    unreadArchivedMessages,
    stages,
    iconsTasks,
    loading,
    unreadEmail,
    totalEmails,
    missedTodayCall,
    outgoingTodayCall,
    receivedTodayCall,
    totalunreadMessagesTags,
    totalQueues,
    channelsRmc,
    depsRmc,
    statsTasks,
    tasks,
    allQueues,
    totalFamilies,
  } = useSelector((state) => state.dashboardRealTime);

  const [selectedMail, setSelecteMail] = useState(
    totalEmails && totalEmails?.length > 0 ? totalEmails[0]?.accountId : null
  );
  const [selectedDep, setSelecteDep] = useState("");
  const [selectedQueue, setSelectedQueue] = useState(
    totalQueues && totalQueues.length > 0 ? totalQueues[0].queue_num : ""
  );
  const [unreadReceivedEmailByAccount, setUnreadReceivedEmailByAccount] =
    useState(
      totalEmails && totalEmails?.length > 0
        ? totalEmails[0]?.nb_email_received_unread
        : null
    );

  const [receivedEmailByAccount, setReceivedEmailByAccount] = useState(
    totalEmails && totalEmails?.length > 0
      ? totalEmails[0]?.nb_email_received
      : null
  );
  const [totalSentEmailByAccount, setTotalSentEmailByAccount] = useState(
    totalEmails && totalEmails?.length > 0
      ? totalEmails[0]?.nb_email_sent
      : null
  );

  // useEffect(() => {
  //   if (allQueues && Array.isArray(allQueues) && allQueues.length > 0) {
  //     if (!selectedQueue)
  //       setSelectedQueue({
  //         ...totalQueues[0],
  //         value: totalQueues[0].queue_num,
  //         label: totalQueues[0].queue_label,
  //       });
  //     else {
  //       setSelectedQueue((prev) =>
  //         totalQueues?.find((el) => el.queue_num === prev?.queue_num)
  //       );
  //     }
  //   }
  // }, [totalQueues]);
  useEffect(() => {
    if (totalEmails && Array.isArray(totalEmails) && totalEmails.length > 0) {
      if (selectedMail) {
        setReceivedEmailByAccount(
          totalEmails?.find((el) => el.accountId === selectedMail)
            ?.nb_email_received
        );
        setUnreadReceivedEmailByAccount(
          totalEmails?.find((el) => el.accountId === selectedMail)
            ?.nb_email_received_unread
        );
        setTotalSentEmailByAccount(
          totalEmails?.find((el) => el.accountId === selectedMail)
            ?.nb_email_sent
        );
      } else {
        setReceivedEmailByAccount(totalEmails[0]?.nb_email_received);
        setSelecteMail(totalEmails[0]?.accountId);
        dispatch(setSelectedMail(totalEmails[0]?.accountId));
        setUnreadReceivedEmailByAccount(
          totalEmails?.find((el) => el.accountId === totalEmails[0]?.accountId)
            ?.nb_email_received_unread
        );
        setTotalSentEmailByAccount(
          totalEmails?.find((el) => el.accountId === totalEmails[0]?.accountId)
            ?.nb_email_sent
        );
      }
    }
  }, [totalEmails, selectedMail]);

  // useEffect(() => {
  //   if (user?.location) {
  //     if (user?.location.date_format) {
  //       setStart(dayjs().format(user?.location?.date_format));
  //       setEnd(dayjs().format(user?.location?.date_format));
  //     } else {
  //       setStart(dayjs().format("YYYY-MM-DD"));
  //       setEnd(dayjs().format("YYYY-MM-DD"));
  //     }
  //   }
  // }, [user]);
  // const [dataAccounts, setDataAccounts] = useState([]);
  useEffect(() => {
    if (start && end) {
      dispatch(
        getDataDashboard({
          start,
          end,
          departement_id: selectedDep,
          queue_num: selectedQueue,
        })
      );
    }
  }, [dispatch, start, end]);

  const getStatsRmc = async (value, start, end) => {
    const jour = new Date().getDate();
    const mois = (new Date().getMonth() + 1).toString().padStart(2, "0"); // Notez que les mois commencent à partir de 0 (janvier = 0)
    const annee = new Date().getFullYear();

    const dateRmc = jour + "-" + mois + "-" + annee;
    dispatch({
      type: SET_RMC,
      payload: channelsRmc.map((el) => ({ ...el, count: "-" })),
    });
    try {
      const { data } = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).get(
        `/dashboard/rmcByDepartment?date_start=${start}&date_end=${end}&department_id=${
          value || ""
        }`
      );
      const updatedChannels = channelsRmc.map((el) => {
        const apiItem = data?.find((item) => item?.channel === el?.channel);
        if (apiItem) {
          return { ...el, count: apiItem.count };
        }
        if (!apiItem) {
          return { ...el, count: 0 };
        }
        return el;
      });
      dispatch({
        type: SET_RMC,
        payload: updatedChannels,
      });
    } catch (err) {
      toastNotification("error", t("toasts.somethingWrong"));
    }
  };

  const [t] = useTranslation("common");
  const formatter = (value) => <CountUp end={value} separator="," />;

  function disabledDate(current) {
    // Ne peut pas sélectionner les jours après aujourd'hui
    return current && current > dayjs().endOf("day");
  }
  return (
    <Row gutter={[8, 8]}>
      <Col xl={12} xxl={12} md={24}>
        <CardVoip />
      </Col>
      {totalQueues && Array.isArray(totalQueues) && totalQueues.length > 0 ? (
        <Col xl={12} xxl={12} md={24}>
          <CardQueue start={start} end={end} />
        </Col>
      ) : null}
      <Col
        xl={
          user &&
          user?.access &&
          user?.access?.rmc === "1" &&
          Array.isArray(user?.accounts_email) &&
          user?.accounts_email?.length > 0 &&
          Array.isArray(totalQueues) &&
          totalQueues.length > 0
            ? 12
            : user &&
              user?.access &&
              user?.access?.rmc === "1" &&
              Array.isArray(user?.accounts_email) &&
              user?.accounts_email?.length > 0 &&
              Array.isArray(totalQueues) &&
              totalQueues.length < 1
            ? 12
            : user &&
              user?.access &&
              user?.access?.rmc === "1" &&
              Array.isArray(user?.accounts_email) &&
              user?.accounts_email?.length < 1
            ? 12
            : user &&
              user?.access &&
              !user?.access?.rmc === "1" &&
              Array.isArray(user?.accounts_email) &&
              user?.accounts_email?.length > 0
            ? 12
            : 12
        }
        xxl={
          user &&
          user?.access &&
          user?.access?.rmc === "1" &&
          Array.isArray(user?.accounts_email) &&
          user?.accounts_email?.length > 0 &&
          Array.isArray(totalQueues) &&
          totalQueues.length > 0
            ? 12
            : user &&
              user?.access &&
              user?.access?.rmc === "1" &&
              Array.isArray(user?.accounts_email) &&
              user?.accounts_email?.length > 0 &&
              Array.isArray(totalQueues) &&
              totalQueues.length < 1
            ? 12
            : user &&
              user?.access &&
              user?.access?.rmc === "1" &&
              Array.isArray(user?.accounts_email) &&
              user?.accounts_email?.length < 1
            ? 12
            : user &&
              user?.access &&
              !user?.access?.rmc === "1" &&
              Array.isArray(user?.accounts_email) &&
              user?.accounts_email?.length > 0
            ? 12
            : 12
        }
        // lg={user && user?.access && user?.access["rmc"] !== "1" ? 24 : 12}
        lg={
          Array.isArray(user?.accounts_email) &&
          user?.accounts_email?.length < 1 &&
          user?.access?.rmc === "1"
            ? 12
            : 24
        }
        md={24}
      >
        <Card
          title={
            <span>
              {" "}
              {t("dashboard.unreadDiscussions")}{" "}
              <Tooltip title={t("dashboard.statsMsgs")}>
                <InfoCircleOutlined />{" "}
              </Tooltip>
            </span>
          }
          size=""
          className="h-full bg-white shadow-sm"
          extra={
            unreadMessagesInGroups + unreadMsgOneToOne > 0 ? (
              <Typography.Link onClick={() => navigate("/chat")}>
                <ArrowRightOutlined />
              </Typography.Link>
            ) : null
          }
        >
          <Row gutter={24} className="">
            <Col span={6}>
              <Statistic
                formatter={
                  typeof unreadMsgOneToOne === "number" ? formatter : null
                }
                title={
                  <Space>
                    {t(`dashboard.unreadmsgOtoO`, {
                      plural: unreadMsgOneToOne > 1 ? "s" : "",
                      pluriel: unreadMsgOneToOne > 1 ? "x" : "",
                    })}
                    {/* <Tooltip title={t("dashboard.independantNumber")}>
                    <InfoCircleOutlined style={{ fontSize: "14px" }} />
                  </Tooltip> */}
                  </Space>
                }
                value={unreadMsgOneToOne}
                prefix={<ChatBubbleLeftRightIcon className="h-6 w-6" />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                formatter={
                  typeof unreadMessagesInGroups === "number" ? formatter : null
                }
                title={
                  <Space>
                    {t(`dashboard.unreadmsgtoG`, {
                      plural: unreadMessagesInGroups > 1 ? "s" : "",
                      pluriel: unreadMessagesInGroups > 1 ? "x" : "",
                    })}
                    {/* <Tooltip title={t("dashboard.independantNumber")}>
                    <InfoCircleOutlined style={{ fontSize: "14px" }} />
                  </Tooltip> */}
                  </Space>
                }
                value={unreadMessagesInGroups}
                prefix={<ChatBubbleBottomCenterTextIcon className="h-6 w-6" />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                formatter={
                  typeof unreadArchivedMessages === "number" ? formatter : null
                }
                title={
                  <Space>
                    {t(`dashboard.unreadArchivedMessages`, {
                      plural: unreadArchivedMessages > 1 ? "s" : "",
                      pluriel: unreadArchivedMessages > 1 ? "x" : "",
                    })}
                    {/* <Tooltip title={t("dashboard.independantNumber")}>
                    <InfoCircleOutlined style={{ fontSize: "14px" }} />
                  </Tooltip> */}
                  </Space>
                }
                value={unreadArchivedMessages}
                prefix={<ChatBubbleBottomCenterTextIcon className="h-6 w-6" />}
              />
            </Col>
            {/* {console.log(totalunreadMessagesTags)} */}
            <Col span={6}>
              <Statistic
                formatter={
                  typeof totalunreadMessagesTags === "number" ? formatter : null
                }
                title={
                  <Space>
                    {t(`dashboard.unreadMention`, {
                      plural: totalunreadMessagesTags > 1 ? "s" : "",
                      pluriel: totalunreadMessagesTags > 1 ? "x" : "",
                    })}

                    {/* <Tooltip title={t("dashboard.independantNumber")}>
                    <InfoCircleOutlined style={{ fontSize: "14px" }} />
                  </Tooltip> */}
                  </Space>
                }
                value={totalunreadMessagesTags}
                prefix={<AtSymbolIcon className="h-6 w-6" />}
              />
            </Col>
          </Row>
        </Card>
      </Col>
      {user?.access?.rmc === "1" ? (
        <Col
          xl={
            user &&
            user?.access &&
            Array.isArray(user?.accounts_email) &&
            user?.accounts_email?.length > 0 &&
            Array.isArray(totalQueues) &&
            totalQueues?.length > 0
              ? 12
              : user &&
                user?.access &&
                Array.isArray(user?.accounts_email) &&
                user?.accounts_email?.length < 1 &&
                Array.isArray(totalQueues) &&
                totalQueues?.length < 1
              ? 12
              : user &&
                user?.access &&
                Array.isArray(user?.accounts_email) &&
                user?.accounts_email?.length < 1 &&
                Array.isArray(totalQueues) &&
                totalQueues?.length > 0
              ? 12
              : user &&
                user?.access &&
                Array.isArray(user?.accounts_email) &&
                user?.accounts_email?.length > 0
              ? 12
              : 10
          }
          xxl={
            user &&
            user?.access &&
            Array.isArray(user?.accounts_email) &&
            user?.accounts_email?.length > 0 &&
            Array.isArray(totalQueues) &&
            totalQueues?.length > 0
              ? 12
              : user &&
                user?.access &&
                Array.isArray(user?.accounts_email) &&
                user?.accounts_email?.length < 1 &&
                Array.isArray(totalQueues) &&
                totalQueues?.length < 1
              ? 12
              : user &&
                user?.access &&
                Array.isArray(user?.accounts_email) &&
                user?.accounts_email?.length < 1 &&
                Array.isArray(totalQueues) &&
                totalQueues?.length > 0
              ? 12
              : user &&
                user?.access &&
                Array.isArray(user?.accounts_email) &&
                user?.accounts_email?.length > 0
              ? 12
              : 10
          }
          lg={12}
          md={24}
        >
          <Card
            title={
              <div className="flex items-center justify-between">
                <span>{t("dashboard.socialMedia")}</span>{" "}
                <Select
                  showSearch
                  // placeholder="Select a person"
                  optionFilterProp="children"
                  popupMatchSelectWidth={false}
                  filterOption={(input, option) =>
                    (option?.label2 ?? "")
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  options={[
                    {
                      value: "",
                      label2: t("helpDesk.all"),
                      label: (
                        <div className="flex items-center justify-between">
                          <span>{t("helpDesk.all")}</span>
                          <Tooltip title={t("dashboard.relativeFigures")}>
                            <InfoCircleOutlined />
                          </Tooltip>
                        </div>
                      ),
                    },
                    ...depsRmc.map((dep) => ({
                      value: dep.department_id,
                      label: dep.name,
                      label2: dep.name,
                      color: dep.color,
                    })),
                  ]}
                  style={{ minWidth: 100 }}
                  value={selectedDep}
                  onChange={(value, values) => {
                    setSelecteDep(value);
                    getStatsRmc(value, start, end);
                    //     const { data } = await generateAxios(
                    //       URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
                    //     ).get(
                    //       `/dashboard/rmcByDepartment?date_start=${dayjs(
                    //         payload.start,
                    //         "DD/MM/YYYY"
                    //       ).format("DD/MM/YYYY")}&date_end=${dayjs(
                    //         payload.end,
                    //         "DD/MM/YYYY"
                    //       ).format("DD/MM/YYYY")}&department_id=${
                    //         payload?.departement_id || ""
                    //       }`
                    //     );
                    //     dispatch({
                    //       type: SET_RMC,
                    //       payload: data,
                    //     });
                  }}
                />{" "}
              </div>
            }
            size=""
            className="h-full bg-white shadow-sm"
            extra={
              <Typography.Link
                className="pl-2"
                onClick={() => navigate("/rmc")}
              >
                <ArrowRightOutlined />
              </Typography.Link>
            }
          >
            <Row gutter={24} className="">
              {
                channelsRmc?.map((el, i) => (
                  <Col flex={1} key={`channelsRmc_Col_${i}`}>
                    <Statistic
                      className={`${
                        typeof channelsRmc[0].count === "number"
                          ? ""
                          : "moyen_ringing_calls"
                      } ${el.channel === "Messenger" ? "messengerHome" : ""}`}
                      formatter={formatter}
                      title={t(`dashboard.${el?.channel}`)}
                      value={el.count}
                      suffix={
                        typeof channelsRmc[0].count === "number" ? "" : el.count
                      }
                      prefix={el.icon}
                    />
                  </Col>
                ))
                // <Col span={8}>
                //   <Statistic
                //     formatter={
                //       typeof newMsgsRmcOfDay === "number" ? formatter : null
                //     }
                //     title={t(`dashboard.newMessages`, {
                //       plural: newMsgsRmcOfDay > 1 ? "s" : "",
                //       pluriel: newMsgsRmcOfDay > 1 ? "x" : "",
                //     })}
                //     value={newMsgsRmcOfDay}
                //     prefix={
                //       <ChatBubbleLeftEllipsisIcon className="h-6 w-6" />
                //     }
                //   />
                // </Col>
                // <Col span={8}>
                //   <Statistic
                //     formatter={
                //       typeof totalConversationRmc === "number"
                //         ? formatter
                //         : null
                //     }
                //     title={t(`dashboard.newConversations`, {
                //       plural: totalConversationRmc > 1 ? "s" : "",
                //       pluriel: totalConversationRmc > 1 ? "x" : "",
                //     })}
                //     value={totalConversationRmc}
                //     prefix={<AtSymbolIcon className="h-6 w-6" />}
                //   />
                // </Col>
                // <Col span={8}>
                //   <Statistic
                //     formatter={
                //       typeof visitorsRmc === "number" ? formatter : null
                //     }
                //     title={t(`dashboard.visitor`, {
                //       plural: visitorsRmc > 1 ? "s" : "",
                //       pluriel: visitorsRmc > 1 ? "x" : "",
                //     })}
                //     value={visitorsRmc}
                //     prefix={<UsersIcon className="h-6 w-6" />}
                //   />
                // </Col>
              }
            </Row>
          </Card>
        </Col>
      ) : (
        ""
      )}
      {Array.isArray(user?.accounts_email) &&
      user?.accounts_email?.length > 0 &&
      typeof unreadEmail === "number" ? (
        <Col
          xxl={
            user &&
            user?.access &&
            user?.access?.rmc === "1" &&
            Array.isArray(user?.accounts_email) &&
            Array.isArray(totalQueues) &&
            totalQueues?.length > 0
              ? 10
              : 12
          }
          xl={
            user &&
            user?.access &&
            user?.access?.rmc === "1" &&
            Array.isArray(user?.accounts_email) &&
            Array.isArray(totalQueues) &&
            totalQueues?.length > 0
              ? 10
              : 12
          }
          lg={user && user?.access && !user?.access?.rmc === "1" ? 24 : 12}
          md={24}
        >
          <Card
            title={
              <div className="flex items-center justify-between gap-2 pr-2">
                <span>Emails</span>
                <span className="truncate">
                  <Select
                    showSearch
                    defaultValue={totalEmails[0]?.accountId}
                    placeholder="Select a person"
                    optionFilterProp="children"
                    popupMatchSelectWidth={false}
                    options={totalEmails.map((el) => ({
                      value: el?.accountId,
                      label: el?.email,
                      nb_email_received_unread: el?.nb_email_received_unread,
                      nb_email_sent: el?.nb_email_sent,
                    }))}
                    filterOption={(input, option) =>
                      (option?.label ?? "")
                        .toLowerCase()
                        .includes(input.toLowerCase())
                    }
                    value={selectedMail}
                    onChange={(value, values) => {
                      dispatch(setSelectedMail(value));
                      setSelecteMail(value);
                      dispatch(setSelectedMail(value));
                      setUnreadReceivedEmailByAccount(
                        values?.nb_email_received_unread
                      );
                      setReceivedEmailByAccount(values?.nb_email_received);
                      setTotalSentEmailByAccount(values?.nb_email_sent);
                    }}
                  />
                </span>
              </div>
            }
            size=""
            className="h-full bg-white shadow-sm"
            extra={
              <Typography.Link
                onClick={() =>
                  navigate(
                    `/mailing/${
                      user?.accounts_email &&
                      user?.accounts_email?.length > 0 &&
                      user?.accounts_email?.find(
                        (el) => el.primary_account == 1
                      )
                        ? user?.accounts_email?.find(
                            (el) => el.primary_account == 1
                          )?.id
                        : user?.accounts_email?.length &&
                          user?.accounts_email.length > 0
                        ? user?.accounts_email[0].id
                        : 0
                    }/inbox`
                  )
                }
              >
                <ArrowRightOutlined />
              </Typography.Link>
            }
          >
            <Row gutter={24} className="">
              <Col flex="auto">
                <Statistic
                  formatter={
                    typeof receivedEmailByAccount === "number"
                      ? formatter
                      : null
                  }
                  title={
                    <Space>
                      {t(`dashboard.receivedCalls`, {
                        plural: receivedEmailByAccount > 1 ? "s" : "",
                        pluriel: receivedEmailByAccount > 1 ? "x" : "",
                      })}
                    </Space>
                  }
                  value={receivedEmailByAccount}
                  prefix={<EnvelopeIcon className="h-6 w-6" />}
                />
              </Col>

              <Col flex="auto">
                <Statistic
                  formatter={
                    typeof unreadReceivedEmailByAccount === "number"
                      ? formatter
                      : null
                  }
                  title={
                    <span>
                      {t(`dashboard.unreadEmailReceived`, {
                        plural: unreadReceivedEmailByAccount > 1 ? "s" : "",
                        pluriel: unreadReceivedEmailByAccount > 1 ? "x" : "",
                      })}{" "}
                      <Tooltip title={t("dashboard.unreadPerSession")}>
                        <InfoCircleOutlined />
                      </Tooltip>
                    </span>
                  }
                  value={unreadReceivedEmailByAccount}
                  // suffix={`/${receivedEmailByAccount}`}
                  prefix={<MdOutlineMarkEmailUnread className="h-6 w-6" />}
                />
              </Col>

              <Col flex="auto">
                <Statistic
                  formatter={
                    typeof totalSentEmailByAccount === "number"
                      ? formatter
                      : null
                  }
                  title={t(`dashboard.totalEmailSent`, {
                    plural: totalSentEmailByAccount > 1 ? "s" : "",
                    pluriel: totalSentEmailByAccount > 1 ? "x" : "",
                  })}
                  value={totalSentEmailByAccount}
                  prefix={<TbMailForward className="h-6 w-6" />}
                />
              </Col>
            </Row>
          </Card>
        </Col>
      ) : Array.isArray(user?.accounts_email) &&
        user?.accounts_email?.length > 0 &&
        typeof unreadEmail !== "number" ? (
        <Col
          xxl={
            user &&
            user?.access &&
            user?.access?.rmc === "1" &&
            Array.isArray(user?.accounts_email) &&
            Array.isArray(totalQueues) &&
            totalQueues?.length > 0
              ? 6
              : 12
          }
          xl={
            user &&
            user?.access &&
            user?.access?.rmc === "1" &&
            Array.isArray(user?.accounts_email) &&
            Array.isArray(totalQueues) &&
            totalQueues?.length > 0
              ? 6
              : 12
          }
          lg={user && user?.access && !user?.access?.rmc === "1" ? 24 : 12}
          md={24}
        >
          <Card
            title={
              <div className="flex items-center justify-between pr-2">
                <span>Emails</span>
              </div>
            }
            size=""
            className="h-full bg-white shadow-sm"
            extra={
              unreadEmail > 0 ? (
                <Typography.Link
                  onClick={() =>
                    navigate(
                      `/mailing/${
                        user?.accounts_email &&
                        user?.accounts_email?.length > 0 &&
                        user?.accounts_email?.find(
                          (el) => el.primary_account == 1
                        )
                          ? user?.accounts_email?.find(
                              (el) => el.primary_account == 1
                            )?.id
                          : user?.accounts_email?.length &&
                            user?.accounts_email.length > 0
                          ? user?.accounts_email[0].id
                          : 0
                      }/inbox`
                    )
                  }
                >
                  <ArrowRightOutlined />
                </Typography.Link>
              ) : null
            }
          >
            <Row gutter={24} className="">
              {/* <Col span={8}>
              <Statistic
                formatter={
                  typeof unreadEmail === "number" ? formatter : null
                }
                title={
                  <Space>
                    {t(`dashboard.unreadEmail`, {
                      plural: unreadEmail > 1 ? "s" : "",
                      pluriel: unreadEmail > 1 ? "x" : "",
                    })}
                    <Tooltip title={t("dashboard.independantNumber")}>
                      <InfoCircleOutlined
                        style={{ fontSize: "14px" }}
                      />
                    </Tooltip>
                  </Space>
                }
                value={unreadEmail}
                prefix={<EnvelopeIcon className="h-6 w-6" />}
              />
            </Col> */}

              <Col span={12}>
                <Statistic
                  className="moyen_ringing_calls"
                  formatter={
                    typeof unreadReceivedEmailByAccount === "number"
                      ? formatter
                      : null
                  }
                  title={
                    <span>
                      {t(`dashboard.unreadEmailReceived`, {
                        plural: unreadReceivedEmailByAccount > 1 ? "s" : "",
                        pluriel: unreadReceivedEmailByAccount > 1 ? "x" : "",
                      })}{" "}
                      <Tooltip title={t("dashboard.unreadPerSession")}>
                        <InfoCircleOutlined />
                      </Tooltip>
                    </span>
                  }
                  suffix={`-/ -`}
                  prefix={<MdOutlineMarkEmailUnread className="h-6 w-6" />}
                />
              </Col>

              <Col span={12}>
                <Statistic
                  className="moyen_ringing_calls"
                  formatter={
                    typeof totalSentEmailByAccount === "number"
                      ? formatter
                      : null
                  }
                  title={t(`dashboard.totalEmailSent`, {
                    plural: totalSentEmailByAccount > 1 ? "s" : "",
                    pluriel: totalSentEmailByAccount > 1 ? "x" : "",
                  })}
                  suffix={`-`}
                  prefix={<TbMailForward className="h-6 w-6" />}
                />
              </Col>
            </Row>
          </Card>
        </Col>
      ) : (
        ""
      )}
      {statsTasks?.length > 0 ? (
        <Col
          xl={
            user &&
            user?.access &&
            user?.access?.rmc === "1" &&
            Array.isArray(user?.accounts_email) &&
            user?.accounts_email?.length > 0 &&
            Array.isArray(totalQueues) &&
            totalQueues?.length > 0
              ? 14
              : user &&
                user?.access &&
                !user?.access?.rmc === "1" &&
                Array.isArray(user?.accounts_email) &&
                user?.accounts_email?.length > 0 &&
                Array.isArray(totalQueues) &&
                totalQueues?.length > 0
              ? 24
              : user &&
                user?.access &&
                user?.access?.rmc === "1" &&
                Array.isArray(user?.accounts_email) &&
                user?.accounts_email?.length > 0 &&
                !Array.isArray(totalQueues) &&
                !totalQueues?.length > 0
              ? 24
              : user &&
                user?.access &&
                !user?.access?.rmc === "1" &&
                Array.isArray(user?.accounts_email) &&
                user?.accounts_email?.length < 1 &&
                Array.isArray(totalQueues) &&
                totalQueues?.length > 0
              ? 12
              : 24
          }
          xxl={
            user &&
            user?.access &&
            user?.access?.rmc === "1" &&
            Array.isArray(user?.accounts_email) &&
            user?.accounts_email?.length > 0 &&
            Array.isArray(totalQueues) &&
            totalQueues?.length > 0
              ? 14
              : user &&
                user?.access &&
                !user?.access?.rmc === "1" &&
                Array.isArray(user?.accounts_email) &&
                user?.accounts_email?.length > 0 &&
                Array.isArray(totalQueues) &&
                totalQueues?.length > 0
              ? 24
              : user &&
                user?.access &&
                user?.access?.rmc === "1" &&
                Array.isArray(user?.accounts_email) &&
                user?.accounts_email?.length > 0 &&
                !Array.isArray(totalQueues) &&
                !totalQueues?.length > 0
              ? 24
              : user &&
                user?.access &&
                !user?.access?.rmc === "1" &&
                Array.isArray(user?.accounts_email) &&
                user?.accounts_email?.length < 1 &&
                Array.isArray(totalQueues) &&
                totalQueues?.length > 0
              ? 12
              : 24
          }
          md={24}
        >
          <Card
            title={t("menu1.tasks")}
            size=""
            className="h-full bg-white shadow-sm"
            extra={
              // outgoingTodayCall + missedTodayCall + receivedTodayCall >
              // 0 ? (
              <Typography.Link onClick={() => navigate("/tasks")}>
                <ArrowRightOutlined />
              </Typography.Link>
              // ) : null
            }
          >
            <Row
              gutter={24}
              className="ml-0 mr-0 flex-nowrap overflow-auto"
              style={{ marginRight: 0, marginLeft: 0 }}
            >
              {statsTasks.map((stat, i) => (
                <Col flex="auto" key={`statsTasks_Col_${i}`}>
                  <Statistic
                    formatter={
                      typeof stat.count === "number" ? formatter : null
                    }
                    title={
                      <Tooltip
                        title={stat?.label?.length > 12 ? stat?.label : ""}
                      >
                        <div
                          style={{
                            width: "max-content",
                            whiteSpace: "nowrap",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                          }}
                          className=""
                        >
                          {stat?.label?.length > 12
                            ? `${stat?.label?.slice(0, 12)}...`
                            : stat?.label}
                        </div>
                      </Tooltip>
                    }
                    value={stat.count}
                    valueStyle={{
                      color: stat.color,
                    }}
                    prefix={<ChoiceIcons icon={stat.icons} />}
                  />
                </Col>
              ))}
            </Row>
          </Card>
        </Col>
      ) : user?.task_types ? (
        <Col
          xl={
            user &&
            user?.access &&
            user?.access?.rmc === "1" &&
            Array.isArray(user?.accounts_email) &&
            user?.accounts_email?.length > 0 &&
            Array.isArray(totalQueues) &&
            totalQueues?.length > 0
              ? 14
              : user &&
                user?.access &&
                !user?.access?.rmc === "1" &&
                Array.isArray(user?.accounts_email) &&
                user?.accounts_email?.length > 0 &&
                Array.isArray(totalQueues) &&
                totalQueues?.length > 0
              ? 24
              : user &&
                user?.access &&
                user?.access?.rmc === "1" &&
                Array.isArray(user?.accounts_email) &&
                user?.accounts_email?.length > 0 &&
                !Array.isArray(totalQueues) &&
                !totalQueues?.length > 0
              ? 24
              : user &&
                user?.access &&
                !user?.access?.rmc === "1" &&
                Array.isArray(user?.accounts_email) &&
                user?.accounts_email?.length < 1 &&
                Array.isArray(totalQueues) &&
                totalQueues?.length > 0
              ? 12
              : 24
          }
          xxl={
            user &&
            user?.access &&
            user?.access?.rmc === "1" &&
            Array.isArray(user?.accounts_email) &&
            user?.accounts_email?.length > 0 &&
            Array.isArray(totalQueues) &&
            totalQueues?.length > 0
              ? 14
              : user &&
                user?.access &&
                !user?.access?.rmc === "1" &&
                Array.isArray(user?.accounts_email) &&
                user?.accounts_email?.length > 0 &&
                Array.isArray(totalQueues) &&
                totalQueues?.length > 0
              ? 24
              : user &&
                user?.access &&
                user?.access?.rmc === "1" &&
                Array.isArray(user?.accounts_email) &&
                user?.accounts_email?.length > 0 &&
                !Array.isArray(totalQueues) &&
                !totalQueues?.length > 0
              ? 24
              : user &&
                user?.access &&
                !user?.access?.rmc === "1" &&
                Array.isArray(user?.accounts_email) &&
                user?.accounts_email?.length < 1 &&
                Array.isArray(totalQueues) &&
                totalQueues?.length > 0
              ? 12
              : 24
          }
          md={24}
        >
          <Card
            title={t("menu1.tasks")}
            size=""
            className="h-full bg-white shadow-sm"
            extra={
              // outgoingTodayCall + missedTodayCall + receivedTodayCall >
              // 0 ? (
              <Typography.Link onClick={() => navigate("/tasks")}>
                <ArrowRightOutlined />
              </Typography.Link>
              // ) : null
            }
          >
            <Row gutter={24} className="">
              {user?.task_types.map((stat, i) => (
                <Col flex="auto" key={`task_types_Col_${i}`}>
                  <Statistic
                    formatter={
                      typeof stat.count === "number" ? formatter : null
                    }
                    title={stat.label}
                    value={"-"}
                    valueStyle={{
                      color: stat.color,
                    }}
                    prefix={<ChoiceIcons icon={stat.icons} />}
                  />
                </Col>
              ))}
            </Row>
          </Card>
        </Col>
      ) : null}
      <Col>
        <Card
          title={
            <div>
              {" "}
              {t("menu2.family")}{" "}
              {/* <Tooltip
                    title={
                      t("dashboard.createdByYou") +
                      " / " +
                      t("dashboard.totalElements")
                    }
                  >
                    <InfoCircleOutlined />
                  </Tooltip> */}
            </div>
          }
          size=""
          className="h-full bg-white shadow-sm"
          // extra={
          //   // outgoingTodayCall + missedTodayCall + receivedTodayCall >
          //   // 0 ? (
          //   <Typography.Link onClick={() => navigate("/tasks")}>
          //     <ArrowRightOutlined />
          //   </Typography.Link>
          //   // ) : null
          // }
        >
          <Row gutter={[8, 8]} className="">
            {familyIcons(t).map((item, i) => (
              <Col flex="none" key={`familyIcons_Col_${i}`}>
                <Card
                  style={{
                    minWidth: 203,
                    // maxWidth: 205,
                    // width: 150,
                  }}

                  // actions={[
                  //   <SettingOutlined key="setting" />,
                  //   <EditOutlined key="edit" />,
                  //   <EllipsisOutlined key="ellipsis" />,
                  // ]}
                >
                  <Meta
                    avatar={item?.icon}
                    title={item?.label}
                    description={
                      <div className="flex flex-col ">
                        <span className="font-medium">
                          {totalFamilies?.length > 1
                            ? totalFamilies.find(
                                (el) => el.family_id === item.key
                              )?.total_elements
                            : "-"}{" "}
                          {t("dashboard.totalElements", {
                            plural:
                              totalFamilies.find(
                                (el) => el.family_id === item.key
                              )?.total_elements > 1
                                ? "s"
                                : "",
                          })}{" "}
                        </span>
                        <span className="font-medium">
                          {totalFamilies?.length > 1
                            ? totalFamilies.find(
                                (el) => el.family_id === item.key
                              )?.created_by_you
                            : "-"}{" "}
                          {t("dashboard.createdByYou", {
                            plural:
                              totalFamilies.find(
                                (el) => el.family_id === item.key
                              )?.created_by_you > 1
                                ? "s"
                                : "",
                          })}{" "}
                        </span>
                      </div>
                    }
                  />
                </Card>
              </Col>
            ))}
          </Row>
        </Card>
      </Col>
      <CardTask
        // tasks={tasks}
        iconsTasks={iconsTasks}
        stages={stages}
        setTasks={() => {}}
        loading={loading}
      />
    </Row>
  );
};

export default Home;
