import { store } from "new-redux/store";

export const roles = ["Admin", "SuperAdmin"];

export const guestAllowedActions = ["5", "6", "moreInfo"];

// const connectedUserRoleInChat = async () =>
//   await store.getState().chat.currentUser?.role;
// const connectedUserRoleInSphere = async () =>
//   await store.getState().user.user?.role;

// export const isGuestConnected = (userRoleInChat, userRoleInSphere) => {
//   /* const userRoleInChat = await connectedUserRoleInChat();
//   const userRoleInSphere = await connectedUserRoleInSphere(); */
//   const guestRoles = new Set([2, "guest"]);
//   return [userRoleInChat, userRoleInSphere].some((role) =>
//     guestRoles.has(role)
//   );
// };

export const isGuestConnected = (userRoleInChat, userRoleInSphere) => {
  const chatRole = userRoleInChat ?? store.getState().chat.currentUser?.role;
  const sphereRole = userRoleInSphere ?? store.getState().user.user?.role;
  const guestRoles = new Set([2, "guest"]);
  // console.log(
  //   { chatRole, sphereRole },
  //   [chatRole, sphereRole].some((role) => guestRoles.has(role))
  // );
  return [chatRole, sphereRole].some((role) => guestRoles.has(role));
};
