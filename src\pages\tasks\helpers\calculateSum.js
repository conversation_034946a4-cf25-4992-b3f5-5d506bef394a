import dayjs from "dayjs";

//Images extensions array
export const calculateSum = (array) => {
  return (
    array && array.reduce((total, item) => total + item?.messages_count, 0)
  );
};

//Images extensions array
export const getValueByKey = (array, key) => {
  let specificObject = Array.isArray(array)
    ? array?.find((object) => object?.hasOwnProperty(key))
    : null;
  return specificObject ? specificObject : null;
};

//Images extensions array
export const EXTENSIONS_ARRAY = [
  "jpg",
  "jpeg",
  "png",
  "gif",
  "bmp",
  "tiff",
  "tif",
  "jfif",
  "avif",
  "webp",
  "svg",
  "gif",
];

//Format dates to server format.
export const formatDates = (user, date, time) => {
  let formattedDate = dayjs(
    `${date} ${time}`,
    `${user?.location?.date_format} ${user?.location?.time_format}`
  ).toISOString();
  return formattedDate;
};

// Helper function to get the priority color
export const getPriorityColor = (priority) => {
  const priorityColors = {
    low: "#bfbfbf",
    medium: "#69b1ff",
    high: "#ffc53d",
    urgent: "#f5222d",
  };
  return priorityColors[priority] || null;
};
