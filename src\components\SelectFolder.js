import React, { useCallback, useEffect, useState } from "react";
import MainService from "../services/main.service";
import { Select } from "antd";
import { useTranslation } from "react-i18next";
import { isGuestConnected } from "utils/role";

const SelectFolder = ({
  setIdFolder,
  idFolder,
  dataFolders,
  setDataFolders,
  isDisabled,
}) => {
  //
  const [t] = useTranslation("common");
  //
  const [loading, setLoading] = useState(false);

  const getFolders = useCallback(async () => {
    // if (!isModalOpen) return;
    setLoading(true);
    try {
      let response = await MainService.getFoldersTicketHelpdesk();

      if (response?.status === 200) {
        setDataFolders([
          ...(!isGuestConnected()
            ? [
                {
                  label: t("voip.all"),
                  value: "",
                },
              ]
            : []),
          ...response.data.data.map((item) => ({
            label: item.label,
            value: item.id,
          })),
        ]);
        isGuestConnected() && setIdFolder(response.data.data?.[0]?.id);
      }
    } catch (err) {
      console.log(err);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    getFolders();
  }, [getFolders]);

  const handleSearch = (option, input) => {
    return input?.label?.toLowerCase()?.includes(option?.toLowerCase());
  };

  return (
    <div>
      <Select
        style={{ width: "7rem" }}
        popupMatchSelectWidth={false}
        showSearch
        value={dataFolders?.length > 0 && idFolder}
        loading={loading}
        placeholder="Select folder"
        optionFilterProp={["label"]}
        filterOption={handleSearch}
        onChange={(e) => setIdFolder(e)}
        options={dataFolders}
        disabled={isDisabled}
      />
    </div>
  );
};

export default SelectFolder;
