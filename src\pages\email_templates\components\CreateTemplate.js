import { useState } from "react";
import { <PERSON>, Drawer, But<PERSON> } from "antd";
import { CloseOutlined } from "@ant-design/icons";
//
import Editor from "./template_editor/Editor";
import { useWindowSize } from "../../clients&users/components/WindowSize";

const CreateTemplate = ({ open, setOpen }) => {
  const windowSize = useWindowSize();
  //

  const handleCancelClick = () => {
    setOpen(false);
  };
  //
  const DrawerProps = {
    closeIcon: <CloseOutlined onClick={handleCancelClick} />,
    /////////
    footer: (
      <Space direction="horizontal" style={{ padding: "0.250rem" }}>
        <Button onClick={handleCancelClick}>Cancel</Button>
        <Button
          type="primary"
          //   onClick={() => {
          //     setMethodSubmit("create");
          //     form.submit();
          //   }}
        >
          Create
        </Button>
      </Space>
    ),
  };
  //
  return (
    <Drawer
      title={`Create New Email Templates`}
      placement="right"
      width={"690px"}
      open={open}
      closeIcon={DrawerProps.closeIcon}
      footer={DrawerProps.footer}
    >
      <Editor />
    </Drawer>
  );
};

export default CreateTemplate;
