import React, { useCallback, useEffect, useState } from "react";
import Inbox from "./folders/inbox";
import Outbox from "./folders/outbox";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { <PERSON><PERSON>, <PERSON><PERSON>, Result } from "antd";

import Trash from "./folders/trash";
import { useTranslation } from "react-i18next";

import DetailsMessage from "./detailEmail/DetailsMessage";
import MainService from "../../../services/main.service";
import Starred from "../mailing/folders/starred";

import Header from "./header";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import {
  resetMailNotifcationByMail,
  setFilterEmail,
  setSelectedAccount,
  updateEmailsStats,
} from "../../../new-redux/actions/mail.actions";

import Spam from "./folders/spam";
import Archive from "./folders/archive";
import { setOpenConfigMail } from "new-redux/actions/menu.actions/menu";
import DrawerEmail from "pages/mailing/DrawerEmail";
import Brouillon from "./folders/draft";
import Important from "./folders/important";
import { decrementNumberMailsInLabel } from "./helpers/helpers";
import { toastNotification } from "components/ToastNotification";

const Mailing = () => {
  const navigate = useNavigate();
  const [t] = useTranslation("common");
  // const [modalOpen, setModalOpen] = useState(false);
  const [detailsMail, setDetailsMail] = useState([]);
  const [detailsMeta, setDetailsMeta] = useState([]);
  const [loadingDetails, setLoadingDetails] = useState(false);

  const location = useLocation();
  const { user } = useSelector((state) => state.user);
  const { id, accountId } = useParams();

  const [refresh, setRefresh] = useState(false);
  const [hasSameNameAndPassword, setHasSameNameAndPassword] = useState(true);

  const [configEmail, setConfigEmail] = useState({});
  const [page, setPage] = useState(0);
  const dispatch = useDispatch();

  const {
    dataAccounts,
    loadingAccount,
    openModalEmail,
    numberEmailThread,
    refreshMail,
    statsMail,
  } = useSelector((state) => state.mailReducer);
  const { invalidConfigMail } = useSelector((state) => state.menu);

  const url_string = window.location.href;
  const url = new URL(url_string);
  const folderMailing = url.pathname.split("/")[3];

  const getDetailsMessageInbox = useCallback(
    async (idDetails) => {
      var formData = new FormData();
      formData.append("id", idDetails ? idDetails : id);
      formData.append("accountId", accountId);
      formData.append("folderMailing", folderMailing);
      formData.append("limit", "5");
      if (numberEmailThread <= 5) {
        formData.append("first_id_email", "");
        formData.append("last_id_email", "");
      }
      if (numberEmailThread > 5) {
        formData.append("nbrEmails", numberEmailThread);
      }

      try {
        setLoadingDetails(true);
        setDetailsMail([]);
        let response;

        if (numberEmailThread > 5) {
          response = await MainService.getDetailsInboxFirstLast(formData);
        } else {
          response = await MainService.getDetailsInbox(1, formData);
        }

        if (response.status === 200) {
          checkAccessRoleAccount(response, navigate, t);
          // setDetailsMail((prev) => [...prev, ...response?.data?.data]);
          // setDetailsMeta(response?.data?.meta);
          setDetailsMail(response?.data);
          setLoadingDetails(false);

          const data = response.data?.data;
          if (data?.at(-1)?.old_status === 0) {
            dispatch(
              updateEmailsStats({
                id: accountId,
                type: "substract",
                typeEmail: folderMailing,
              })
            );

            // decrement labels
            dispatch(
              decrementNumberMailsInLabel(
                accountId,
                data?.at(-1)?.labelEmail?.map((label) => label.id) || [],
                statsMail
              )
            );
          }
        }
      } catch (error) {
        setLoadingDetails(false);
        console.log(error);
      }
    },
    // [id, accountId, page, dispatch]
    [id, accountId, dispatch]
  );

  const [path, setPath] = useState(location.pathname);

  useEffect(() => {
    if (id) {
      getDetailsMessageInbox();
    }
  }, [id, getDetailsMessageInbox]);

  const findUsedAccount = useCallback(() => {
    const primaryAcountID =
      dataAccounts?.find((item) => item.selected == 1)?.value ??
      dataAccounts[0]?.value;
    dispatch(setSelectedAccount(primaryAcountID));
    return primaryAcountID;
  }, [loadingAccount, dispatch]);

  useEffect(() => {
    if (dataAccounts?.length > 0 && accountId !== 0) {
      findUsedAccount();

      dispatch(resetMailNotifcationByMail(accountId));
    }
  }, [findUsedAccount, accountId]);

  useEffect(() => {
    switch (location.pathname) {
      case `/mailing/${accountId}/sent`:
        setPath("sent");
        break;
      case `/mailing/${accountId}/inbox`:
        setPath("inbox");
        break;
      case `/mailing/${accountId}/drafts`:
        setPath("drafts");
        break;
      case `/mailing/${accountId}/trash`:
        setPath("trash");
        break;
      case `/mailing/${accountId}/starred`:
        setPath("starred");
        break;
      case `/mailing/${accountId}/spam`:
        setPath("spam");
        break;
      default:
        setPath(location.pathname.split("/")[3]);
        break;
    }
  }, [location.pathname, accountId, id]);

  //
  useEffect(() => {
    setRefresh((p) => !p);
  }, [refreshMail]);

  // const NumberEmailUnread = useCallback(async () => {
  //   if (usedAccount?.value === undefined) return;
  //   try {
  //     const response = await MainService.numberEmailUnread(usedAccount?.value);
  //     if (response.status === 200) {
  //       setNumberEmailUnread(response.data);
  //     }
  //   } catch (error) {
  //     console.log(error);
  //   }
  // }, [usedAccount?.value]);
  // useEffect(() => {
  //   NumberEmailUnread();
  // }, [usedAccount?.value]);

  useEffect(() => {
    // dispatch(setOpenModalEmail(false));
    dispatch(setFilterEmail(false));
  }, []);

  return (
    <>
      {user?.accounts_email?.length === 0 && !loadingAccount ? (
        <>
          <div className="flex h-full w-full items-center justify-center">
            <Result
              status="error"
              title={t("mailing.titleconfigureEmail")}
              subTitle={t("mailing.configureEmail")}
              extra={[
                <Button
                  type="primary"
                  key="console"
                  onClick={() => dispatch(setOpenConfigMail(true))}
                >
                  {t("mailing.configureEmailBtn")}
                </Button>,
              ]}
            ></Result>
          </div>
          <DrawerEmail
            configEmail={configEmail}
            setConfigEmail={setConfigEmail}
            setHasSameNameAndPassword={setHasSameNameAndPassword}
            hasSameNameAndPassword={hasSameNameAndPassword}
            inbox={true}
          />
        </>
      ) : (
        <>
          {invalidConfigMail.length > 0 &&
          invalidConfigMail.includes(Number(accountId)) ? (
            <Alert
              message="Warning"
              description="veuillez vérifier la configuration de votre email"
              type="warning"
              showIcon
            />
          ) : null}
          {location.pathname !== `/mailing/${accountId}/${path}/${id}` ? (
            <Header setRefresh={setRefresh} dataAccounts={dataAccounts} />
          ) : null}

          {location.pathname === `/mailing/${accountId}/sent` ? (
            <Outbox
              // key={`outbox-${accountId}`}
              setDetailsMail={setDetailsMail}
              dataAccounts={dataAccounts}
              refresh={refresh}
            />
          ) : location.pathname === `/mailing/${accountId}/inbox` ? (
            <Inbox
              key={`inbox-${accountId}`}
              setDetailsMail={setDetailsMail}
              dataAccounts={dataAccounts}
              refresh={refresh}
            />
          ) : location.pathname === `/mailing/${accountId}/drafts` ? (
            <Brouillon
              setDetailsMail={setDetailsMail}
              dataAccounts={dataAccounts}
              refresh={refresh}
              setRefresh={setRefresh}
            />
          ) : location.pathname === `/mailing/${accountId}/trash` ? (
            <Trash
              setDetailsMail={setDetailsMail}
              dataAccounts={dataAccounts}
              refresh={refresh}
              setRefresh={setRefresh}
            />
          ) : location.pathname === `/mailing/${accountId}/starred` ? (
            <Starred
              setDetailsMail={setDetailsMail}
              dataAccounts={dataAccounts}
              refresh={refresh}
              setRefresh={setRefresh}
            />
          ) : location.pathname === `/mailing/${accountId}/important` ? (
            <Important
              // key={`important-${accountId}`}
              setDetailsMail={setDetailsMail}
              dataAccounts={dataAccounts}
              refresh={refresh}
              setRefresh={setRefresh}
            />
          ) : location.pathname === `/mailing/${accountId}/spam` ? (
            <Spam
              // key={`important-${accountId}`}
              setDetailsMail={setDetailsMail}
              dataAccounts={dataAccounts}
              refresh={refresh}
              setRefresh={setRefresh}
            />
          ) : location.pathname === `/mailing/${accountId}/archive` ? (
            <Archive
              // key={`important-${accountId}`}
              setDetailsMail={setDetailsMail}
              dataAccounts={dataAccounts}
              refresh={refresh}
              setRefresh={setRefresh}
            />
          ) : location.pathname === `/mailing/${accountId}/${path}/${id}` ? (
            <DetailsMessage
              messageDetails={detailsMail}
              getDetailsMessageInbox={getDetailsMessageInbox}
              ListAccounts={dataAccounts}
              detailsMail={detailsMail}
              setDetailsMail={setDetailsMail}
              page={page}
              setPage={setPage}
              detailsMeta={detailsMeta}
              // setModalOpen={setModalOpen}
              // openEditor={openEditor}
              // setopenEditor={setopenEditor}
              // openTasksDrawer={openTasksDrawer}
              // setOpenTasksDrawer={setOpenTasksDrawer}
              loadingDetails={loadingDetails}
            />
          ) : null}
        </>
      )}
      {/* </Row> */}
    </>
  );
};

export const checkAccessRoleAccount = (response, navigate, t) => {
  if (response?.data?.message === 403 && !response?.data?.data.length) {
    navigate("/unauthorized");
    toastNotification(
      "error",
      t("mailing.noAccessThisBox"),
      "topRight",
      5,
      null,
      1,
      "noAccessThisBox"
    );
  }
};

export default Mailing;
