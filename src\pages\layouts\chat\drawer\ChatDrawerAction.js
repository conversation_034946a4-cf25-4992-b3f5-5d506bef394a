import {
  Button,
  List,
  Skeleton,
  Input,
  Tooltip,
  Space,
  Typography,
} from "antd";
import { useDispatch, useSelector } from "react-redux";
import {
  removeSavedMessage,
  removeStarredMessage,
  setOpenDrawer,
  setPinnedIsFetched,
  setSearchList,
  setStarredIsFetched,
} from "new-redux/actions/chat.actions";
import { useTranslation } from "react-i18next";
import { ChatBodyItem } from "../conversation/body/item";
import {
  Suspense,
  lazy,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import {
  goToMessage,
  pinnedMessages,
  searchMessages,
  starredMessages,
} from "new-redux/services/chat.services";
import {
  CloseOutlined,
  CompressOutlined,
  ExpandOutlined,
  PushpinFilled,
  PushpinOutlined,
  ReloadOutlined,
  RetweetOutlined,
  RightOutlined,
  SearchOutlined,
  StarFilled,
  StarOutlined,
} from "@ant-design/icons";
import { setSearchMessageTerm } from "new-redux/actions/chat.actions/Input";
import { useActionMessage } from "../hooks/useActionMessage";
import { motion } from "framer-motion";
import { BiCodeAlt } from "react-icons/bi";
import { Loader } from "components/Chat";
import { moment_timezone } from "App";
import { lazyRetry } from "utils/lazyRetry";
import { FiSearch } from "react-icons/fi";
import { drawerTypeArray, safeText } from "../utils/ConversationUtils";
const WebHookRender = lazy(() =>
  lazyRetry(() => import("./webhook-page"), "WebHookRender")
);
const DetailForwardMessage = lazy(() =>
  lazyRetry(
    () => import("components/Chat/MessageRender/Other/DetailForwardMessage"),
    "DetailForwardMessage"
  )
);

const { Search } = Input;

const ChatDrawerAction = ({ source }) => {
  const {
    openDrawer,
    starredList,
    pinnedList,
    searchList,
    searchMessageTerm,
    pinnedListFetched,
    starredListFetched,
    currentUser,
  } = useSelector((state) => state.chat);
  const selectedConversation = useSelector(
    (state) => state.ChatRealTime.selectedConversation
  );

  const dispatch = useDispatch();
  const [t] = useTranslation("common");
  let prevMessage = null;
  const [actionType, setActionType] = useState("");
  const [isBig, setIsBig] = useState(false);
  const [position, setPosition] = useState("relative");

  const [currentPage, setCurrentPage] = useState(1);
  const [loadingMore, setLoadingMore] = useState(false);
  const [loading, setLoading] = useState(true);
  const [hasMore, setHasMore] = useState(false);
  const [error, setError] = useState(false);
  const [totalSearch, setTotalSearch] = useState(0);

  const observe = useRef();
  const searchRef = useRef(null);

  const lastElement = useCallback(
    (node) => {
      if (loadingMore) return;
      if (observe.current) observe.current.disconnect();
      observe.current = new IntersectionObserver(
        (e) => {
          if (e[0].isIntersecting && hasMore) {
            if (openDrawer?.type === "starred") {
              dispatch(
                starredMessages({
                  ...selectedConversation,
                  setHasMore,
                  setLoadingMore,
                  currentPage: currentPage + 1,
                  setLoading,
                })
              );
            } else if (openDrawer?.type === "pinned") {
              dispatch(
                pinnedMessages({
                  ...selectedConversation,
                  setHasMore,
                  setLoadingMore,
                  currentPage: currentPage + 1,
                  setLoading,
                })
              );
            } else if (openDrawer?.type === "search") {
              dispatch(
                searchMessages({
                  ...selectedConversation,
                  setHasMore,
                  setLoadingMore,
                  currentPage: currentPage + 1,
                  keyword: searchMessageTerm?.value,
                  setLoading,
                  setError,
                })
              );
            }
            setCurrentPage((c) => c + 1);
          }
        },
        { threshold: 0.7 }
      );
      if (node) observe.current.observe(node);
    },
    [
      loadingMore,
      hasMore,
      openDrawer?.type,
      dispatch,
      selectedConversation,
      currentPage,
      searchMessageTerm?.value,
    ]
  );

  const {
    mutate: handleActionMessage,
    isLoading,
    data: ReturnData,
  } = useActionMessage(actionType);

  const GoToComponent = ({ item }) => {
    return (
      <div
        className={`
        
        absolute right-0 top-0 z-10  flex w-auto cursor-pointer items-center justify-start space-x-2
    
    rounded-full bg-transparent  px-2     py-0.5 group-hover:flex
     ${
       openDrawer?.type && openDrawer?.type !== "search"
         ? "   border-0.5 group-hover:border-solid group-hover:border-gray-200 "
         : ""
     } 
        `}>
        <Space className="hidden group-hover:flex  " size={4}>
          {openDrawer?.type === "starred" ? (
            <Tooltip title={t("chat.action.unstar")}>
              <Button
                disabled={isLoading && ReturnData?.action?.includes("favorite")}
                type="text"
                size="small"
                className="text-yellow-500"
                onClick={() => {
                  handleActionMessage({
                    message_id: item._id,
                    params: null,
                    type_conversation: item.room_id ? "room" : "user",

                    type_action: "remove_favorite",
                  });
                  setActionType("remove_favorite");

                  dispatch(removeStarredMessage(item));
                }}
                icon={<StarFilled />}></Button>
            </Tooltip>
          ) : openDrawer?.type === "pinned" ? (
            item.important === currentUser?._id && (
              <Tooltip title={t("chat.action.unpin")}>
                <Button
                  disabled={
                    isLoading && ReturnData?.message?.includes("important")
                  }
                  type="text"
                  className="text-yellow-500 "
                  size="small"
                  onClick={() => {
                    if (item.important === currentUser?._id) {
                      handleActionMessage({
                        message_id: item._id,
                        params: null,
                        type_conversation: item.room_id ? "room" : "user",
                        type_action: "remove_saved",
                      });
                      setActionType("remove_saved");
                      dispatch(removeSavedMessage(item));
                    }
                  }}
                  icon={
                    <PushpinFilled style={{ fontSize: "100%" }} />
                  }></Button>
              </Tooltip>
            )
          ) : null}
        </Space>

        <Button
          className="group"
          type="text"
          size="small"
          onClick={async () => {
            dispatch(
              await goToMessage({
                id_search: item?._id,
                type: selectedConversation?.type,
              })
            );
          }}>
          <span className="group-hover flex items-center gap-x-3 text-sm text-zinc-400">
            {t("chat.goto")}
            <RightOutlined className="ml-1 mt-1 h-3 w-3" />
          </span>
        </Button>
      </div>
    );
  };
  const onSearch = (value) => {
    setCurrentPage(1);
    value = safeText(value.trim());
    //    value = specialCharacters(value.trim());
    // return;
    // add new value
    // if (
    //   searchMessageTerm.value === last_Search_key.current.trim() &&
    //   !searchMessageTerm.forced
    // )
    //   return;
    //    dispatch(setSearchList([]));

    if (value === "") {
      dispatch(
        setSearchMessageTerm({
          value: "",
          forced: false,
        })
      );
      setError(false);
    } else {
      dispatch(
        searchMessages({
          ...selectedConversation,
          setHasMore,
          setLoadingMore,
          currentPage: 1,
          keyword: value,
          setLoading,
          setTotalSearch,
          setError,
        })
      );
    }
  };

  const skl = () =>
    Array.from(
      { length: Math.floor(document.body.clientHeight / 3) },
      (_, i) => i + 1
    ).map((item) => (
      <div className="flex  items-center px-1" key={`sklt_${item}`}>
        <Skeleton
          avatar
          paragraph={{
            rows: Math.floor(
              (Math.random() + 1) *
                Array.from({ length: 3 }, (i) => i + 1).length
            ),
          }}
          active
        />
      </div>
    ));

  useEffect(() => {
    (async () => {
      if (openDrawer?.type === "starred" && !starredListFetched) {
        await dispatch(
          starredMessages({
            ...selectedConversation,
            setHasMore,
            setLoadingMore,
            currentPage,
            setLoading,
            setError,
          })
        );
        dispatch(setStarredIsFetched(true));
      } else {
        setLoading(false);
      }

      if (openDrawer?.type === "pinned" && !pinnedListFetched) {
        await dispatch(
          pinnedMessages({
            ...selectedConversation,
            setHasMore,
            setLoadingMore,
            currentPage,
            setLoading,
            setError,
          })
        );
        dispatch(setPinnedIsFetched(true));
      } else {
        setLoading(false);
      }
    })();
  }, [
    currentPage,
    dispatch,

    openDrawer?.type,
    pinnedListFetched,
    selectedConversation,
    starredListFetched,
  ]);

  // useEffect(() => {
  //   searchRef.current?.setSelectionRange(
  //     document.getElementById("search_drawer_id")?.value?.length,
  //     document.getElementById("search_drawer_id")?.value?.length
  //   );
  //   const time = setTimeout(() => {
  //     searchRef.current?.focus();
  //   }, 200);
  //   return () => {
  //     clearTimeout(time);
  //   };
  // }, [searchMessageTerm?.focus]);

  useEffect(() => {
    searchMessageTerm?.forced && onSearch(searchMessageTerm?.value);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchMessageTerm?.value, searchMessageTerm?.forced]);
  return (
    <>
      {drawerTypeArray.includes(openDrawer?.type) && (
        <motion.div
          animate={{
            width: isBig ? 800 : 384,
            zIndex: 100,
            right: 0,
            position,
          }}
          initial={{
            position: "relative",
          }}
          transition={{
            duration: isBig ? 0.5 : 0.1,
          }}
          style={{
            height: position === "fixed" ? "calc(100% - 57px)" : "100%",
          }}
          className={`  flex flex-col  justify-between overflow-hidden bg-slate-50 pb-1 pt-3  drop-shadow-2xl `}>
          <div
            className=" flex w-full  items-center justify-between px-2 py-3"
            style={{
              boxShadow: "0 1px 2px 0 rgb(0 0 0 / 0.05)",
            }}>
            <h2 className="m-0 ml-3 text-lg font-bold text-slate-700">
              {openDrawer?.type === "starred" ? (
                <div className="flex items-center space-x-1">
                  <StarOutlined />
                  <span>{t("chat.starredMessages")} </span>
                </div>
              ) : openDrawer?.type === "pinned" ? (
                <div className="flex items-center space-x-1">
                  <PushpinOutlined />
                  <span> {t("chat.pinnedMessages")}</span>
                </div>
              ) : openDrawer?.type === "search" ? (
                <div className="flex items-center space-x-1">
                  <SearchOutlined />
                  <span>{t("chat.searchSide.searchMessages")}</span>
                </div>
              ) : openDrawer?.type === "webhook" ? (
                <div className="flex items-center space-x-1">
                  <BiCodeAlt style={{ fontSize: "18px" }} />
                  <span>{t("chat.bot.dev")}</span>
                </div>
              ) : openDrawer?.type === "forward" ? (
                <div className="flex items-center space-x-1">
                  <RetweetOutlined style={{ fontSize: "18px" }} />
                  <span>{t("chat.forward.text_message_forwarded_title")}</span>
                </div>
              ) : (
                ""
              )}
            </h2>

            <div className="space-x-2">
              {source !== "external" && (
                <Button
                  onClick={() => {
                    let time;
                    setIsBig((p) => !p);
                    clearTimeout(time);
                    time = setTimeout(
                      () => {
                        setPosition((p) =>
                          p === "relative" ? "fixed" : "relative"
                        );
                        clearTimeout(time);
                      },
                      isBig ? 200 : 0
                    );
                  }}
                  type="text"
                  shape="circle"
                  size="small"
                  icon={!isBig ? <ExpandOutlined /> : <CompressOutlined />}
                />
              )}
              <Button
                onClick={() => {
                  dispatch(setOpenDrawer({ type: "" }));
                  openDrawer?.type === "search" &&
                    dispatch(
                      setSearchMessageTerm({
                        value: "",
                        forced: false,
                      })
                    );
                }}
                type="text"
                shape="circle"
                size="small"
                icon={<CloseOutlined />}
              />
            </div>
          </div>
          <div className="flex h-full flex-col space-y-1 overflow-y-auto px-4">
            {openDrawer?.type === "search" && (
              <>
                <Search
                  onFocus={() => {
                    document.getElementById("editor-input")?.blur();
                    const time = setTimeout(() => {
                      // dispatch(
                      //   setSearchMessageTerm({
                      //     value: searchMessageTerm?.value,
                      //     forced: false,
                      //     focus: Math.floor(Math.random() * 10000 + 1),
                      //   })
                      // );
                      clearTimeout(time);
                    }, 1);
                  }}
                  loading={loading}
                  enterButton={<FiSearch className="text-white" />}
                  id="search_drawer_id"
                  placeholder={t("chat.searchSide.searchMessages")}
                  onSearch={onSearch}
                  onChange={(e) => {
                    if (e.target.value.trim() === "") {
                      dispatch(setSearchList({ list: [], page: 1 }));
                      dispatch(
                        setSearchMessageTerm({
                          value: "",
                          forced: false,
                        })
                      );
                    } else
                      dispatch(
                        setSearchMessageTerm({
                          value: e.target.value,
                          forced: false,
                        })
                      );
                  }}
                  style={{
                    width: "100%",
                    marginTop: 16,
                  }}
                  value={searchMessageTerm?.value}
                  autoFocus
                  ref={searchRef}
                  allowClear
                />
                {totalSearch > 0 && searchList.length > 0 ? (
                  <p
                    dangerouslySetInnerHTML={{
                      __html: t("chat.searchSide.searchResults", {
                        count: totalSearch,
                      }),
                    }}
                    className="text-sm  text-gray-400 "></p>
                ) : (
                  <div className="text-xs  text-gray-400 ">
                    {t("chat.searchSide.searchTips")}
                  </div>
                )}
              </>
            )}
            {openDrawer?.type === "webhook" ? (
              <Suspense
                fallback={
                  <div className=" flex h-full  w-full items-center justify-center">
                    <Loader size={24} />
                  </div>
                }>
                <WebHookRender />
              </Suspense>
            ) : openDrawer?.type === "forward" ? (
              <Suspense
                fallback={
                  <div className=" flex h-full  w-full items-center justify-center">
                    <Loader size={24} />
                  </div>
                }>
                <DetailForwardMessage />
              </Suspense>
            ) : loading ? (
              skl()
            ) : error ? (
              <Space size={10} className="my-2 flex h-full flex-col ">
                <Typography.Text type="danger">
                  {error?.message}
                </Typography.Text>
                <Tooltip title={t("chat.reload")}>
                  <Button
                    loading={loading}
                    danger
                    type="primary"
                    onClick={() =>
                      openDrawer?.type === "starred"
                        ? dispatch(
                            starredMessages({
                              ...selectedConversation,
                              setHasMore,
                              setLoadingMore,
                              currentPage,
                              setLoading,
                              setError,
                            })
                          )
                        : openDrawer?.type === "pinned"
                        ? dispatch(
                            pinnedMessages({
                              ...selectedConversation,
                              setHasMore,
                              setLoadingMore,
                              currentPage,
                              setLoading,
                              setError,
                            })
                          )
                        : openDrawer?.type === "search"
                        ? dispatch(
                            searchMessages({
                              ...selectedConversation,
                              setLoadingMore,
                              setTotalSearch,
                              setError,
                              setHasMore,
                              currentPage: 1,
                              keyword: searchMessageTerm?.value,
                              setLoading,
                            })
                          )
                        : null
                    }
                    icon={<ReloadOutlined />}>
                    {t("chat.reload")}
                  </Button>
                </Tooltip>
              </Space>
            ) : (
              <>
                <List
                  size="small"
                  className="messagesList flex h-full w-full flex-1 flex-col overflow-y-auto"
                  dataSource={
                    openDrawer?.type === "starred"
                      ? starredList
                      : openDrawer?.type === "pinned"
                      ? pinnedList
                      : openDrawer?.type === "search"
                      ? searchList
                      : []
                  }
                  rowKey={(item) => item?._id}
                  renderItem={(item, index) => {
                    const sameDay = prevMessage
                      ? moment_timezone(item?.created_at).isSame(
                          moment_timezone(prevMessage.created_at),
                          "day"
                        )
                      : false;

                    prevMessage = item;
                    return (
                      <div className="flex items-center">
                        <ChatBodyItem
                          source={openDrawer?.type}
                          ref={
                            (openDrawer?.type === "starred"
                              ? starredList
                              : openDrawer?.type === "pinned"
                              ? pinnedList
                              : openDrawer?.type === "search"
                              ? searchList
                              : []
                            ).length -
                              1 ===
                            index
                              ? lastElement
                              : null
                          }
                          scrollToBottom={() => {}}
                          hideAvatar={false}
                          display_header_icon={false}
                          sameDay={sameDay}
                          item={item}
                          index={
                            (openDrawer?.type === "starred"
                              ? starredList
                              : openDrawer?.type === "pinned"
                              ? pinnedList
                              : openDrawer?.type === "search"
                              ? searchList
                              : []
                            )?.length -
                            1 -
                            index
                          }
                          data={
                            openDrawer?.type === "starred"
                              ? starredList
                              : openDrawer?.type === "pinned"
                              ? pinnedList
                              : openDrawer?.type === "search"
                              ? searchList
                              : []
                          }
                          tabopenDropDown={{}}
                          setTabOpenDropDown={() => {}}
                          GoToComponent={<GoToComponent item={item} />}
                          from={false}
                          isReply={false}
                        />
                      </div>
                    );
                  }}
                />

                {loadingMore && (
                  <Skeleton
                    avatar
                    paragraph={{
                      rows: 0,
                    }}
                    size="small"
                  />
                )}
              </>
            )}
          </div>
        </motion.div>
      )}
    </>
  );
};
export default ChatDrawerAction;
