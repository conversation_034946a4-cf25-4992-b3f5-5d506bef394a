import { Tabs } from "antd";
import { useEffect } from "react";

export default function TabsDetails({ items, onChange, keyTab, setKey }) {
  // const [key, setKey] = useState("1");
  return (
    <div className="px-4 pt-4">
      <Tabs
        onChange={(key) => setKey(key)}
        // activeKey={keyTab}
        defaultActiveKey={keyTab}
        items={items}
        type="card"
      />
    </div>
  );
}
