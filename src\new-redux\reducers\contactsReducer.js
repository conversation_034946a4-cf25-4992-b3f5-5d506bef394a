import {
  SET_CONTACT_HEADER_INFO,
  SET_CONTACT_DATA,
  SET_CONTACT_INFO_FROM_DRAWER,
  SET_CONTACT_SORT_TABLE,
} from "../constants";

const initialState = {
  contactHeaderInfo: {},
  contactsData: {},
  backward: { state: false, pathName: "" },
  userEvent: null,
  familyEvent: null,
  sortTable: {},
};

const contacts = (state = initialState, action) => {
  const { type, payload } = action;
  switch (type) {
    case "SET_USER_EVENT":
      return {
        ...state,
        userEvent: payload,
      };
    case "RESET_USER_EVENT":
      return {
        ...state,
        userEvent: null,
      };
    case SET_CONTACT_HEADER_INFO:
      return {
        ...state,
        contactHeaderInfo: payload,
      };
    case "RESET_CONTACT_HEADER_INFO":
      return {
        ...state,
        contactHeaderInfo: {},
      };
    case SET_CONTACT_DATA:
      return {
        ...state,
        contactsData: payload,
      };

    case "SET_BACKWARD_STATE":
      return {
        ...state,
        backward: {
          ...state?.backward,
          ...payload,
        },
      };
    case "SET_FAMILY_EVENT":
      return {
        ...state,
        familyEvent: payload,
      };
    case "RESET_FAMILY_EVENT":
      return {
        ...state,
        familyEvent: null,
      };
    case SET_CONTACT_SORT_TABLE:
      const { familyId, info } = payload;
      return {
        ...state,
        sortTable: { ...state.sortTable, [familyId]: info },
      };
    default:
      return state;
  }
};

export default contacts;
