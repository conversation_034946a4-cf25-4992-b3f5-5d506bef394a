/**
 * @name UsersList
 *
 * @description `UsersList` component is responsible for displaying the list of all users (in drawer and modal).
 *
 * @param {Array} usersList The list of all users (members of the family 1,2,4)
 * @param {String} source The source, from where, the component is being called
 * @param {String} searchQuery
 * @param {Array} checkedItems
 * @param {Function} setFollowersSearchQuery
 * @param {Function} displayFollowersList
 * @param {Function} setCheckedItems
 * @param {Function} handleCheckedItems Handle check users from the list.
 * @param {Boolean} loading Show/hide loader indicator.
 * @param {Function} dispatchAddMembers Trigger add guests/owner API in activity.
 * @param {Function} dispatchRemoveMembers Trigger remove guests/owner API from activity.
 * @param {String} defaultOwner The id of the default owner.
 *
 * @returns {JSX.Element} Dropdown with the options of calling or sending a message to user.
 */

import { useState, useRef, useEffect, useCallback, useMemo } from "react";
import { InfoCircleOutlined, SearchOutlined } from "@ant-design/icons";
import { Checkbox, Divider, Input, List, Segmented, Spin, Tooltip } from "antd";
import { useTranslation } from "react-i18next";
import InfiniteScroll from "react-infinite-scroll-component";

import { AvatarChat } from "components/Chat";
import { URL_ENV } from "index";
import { getName } from "pages/layouts/chat/utils/ConversationUtils";
import { EXTENSIONS_ARRAY } from "./helpers/calculateSum";
import { HighlightSearchW } from "pages/voip/components";
import { useSelector } from "react-redux";

const UsersList = ({
  usersList,
  searchQuery,
  setSearchQuery,
  lastPage,
  currentPage,
  setCurrentPage,
  checkedItems,
  handleCheckedItems,
  loading,
  setCheckedItems,
  filterParticipantsValue = 0,
  dispatchAddMembers = () => {},
  dispatchRemoveMembers = () => {},
  setSelectedFamilyMembers = () => {},
  setGuestsList = () => {},
  setFilterParticipantsValue = () => {},
  totalEntities,
  from = "",
}) => {
  const [yPosition, setYPosition] = useState(false);
  const scrollListRef = useRef(null);
  const usersInputRef = useRef(null);
  const [t] = useTranslation("common");
  const { user } = useSelector((state) => state.user);
  // Guests types array (all, organisation, contact, user, lead)
  const segmentedOptions = useMemo(
    () => [
      { value: 0, label: t("helpDesk.all") },
      {
        value: 1,
        label:
          user.tenant === "spheredev2" || user.tenant === "taoufikhospitals"
            ? t("tasks.medicalStaff")
            : t("tasks.orgFamily"),
      },
      {
        value: 2,
        label:
          user.tenant === "spheredev2" || user.tenant === "taoufikhospitals"
            ? t("tasks.patients")
            : t("tasks.contactFamily"),
      },
      { value: 4, label: t("tasks.userFamily") },
      { value: 9, label: t("menu1.leads") },
    ],
    [t]
  );

  // Display family name under the list element.
  const displayFamilyName = useCallback(
    (familyName) => {
      switch (familyName) {
        case "User":
          return t("tasks.userFamily");
        case "Organisation":
          return t("tasks.orgFamily");
        case "Contact":
          return t("tasks.contactFamily");
        case "Leads":
          return t("contacts.lead");
        default:
          return null;
      }
    },
    [t]
  );

  // Handle scroll to top
  useEffect(() => {
    if (yPosition && currentPage > 1) {
      scrollListRef.current?.scrollTo({ top: 0, behavior: "smooth" });
      setYPosition(false);
    }
  }, [currentPage, yPosition]);

  // Handle list filters
  const handleChangeFilter = useCallback(
    (value) => {
      setYPosition(true);
      setGuestsList([]);
      setSelectedFamilyMembers(() => {
        const newSelection =
          value === 0
            ? [1, 2, 4, 9]
            : [1, 2, 4, 9].filter((el) => el === value);
        setCurrentPage(1);
        return newSelection;
      });
    },
    [setCurrentPage, setGuestsList, setSelectedFamilyMembers]
  );

  // Handle search change.
  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  // Handle select/deselect users from the list
  const handleCheckboxChange = useCallback(
    (e, item) => {
      usersInputRef.current.focus({ cursor: "all" });
      handleCheckedItems(
        usersList,
        e.target.value,
        e.target.checked,
        checkedItems,
        setCheckedItems
      );
      if (e.target.checked) {
        dispatchAddMembers(2, e.target.value);
      } else {
        dispatchRemoveMembers(2, e.target.value);
      }
    },
    [
      handleCheckedItems,
      usersList,
      checkedItems,
      setCheckedItems,
      dispatchAddMembers,
      dispatchRemoveMembers,
    ]
  );

  // Handle segmented change.
  const handlsegmentedChange = (value) => {
    handleChangeFilter(value);
    setFilterParticipantsValue(value);
  };

  // Delay incrementing next page.
  const handleIncrementNextPage = () => {
    const timer = setTimeout(() => {
      setCurrentPage((prev) => prev + 1);
      clearTimeout(timer);
    }, 300);
  };

  return (
    <>
      {/* Search input */}
      <Input
        placeholder={t("activities.search")}
        prefix={<SearchOutlined />}
        onChange={handleSearchChange}
        allowClear
        value={searchQuery}
        autoFocus
        style={{ marginBottom: "5px" }}
        ref={usersInputRef}
        suffix={
          <Tooltip title={t("tasks.listSearchPlaceholder")}>
            <InfoCircleOutlined style={{ color: "#1890ff" }} />
          </Tooltip>
        }
      />
      {/* Filter by segmented */}
      <div className="flex w-full justify-center py-3">
        <Segmented
          value={filterParticipantsValue}
          // size={from === "columnTable" ? "small" : "middle"}
          options={segmentedOptions}
          onChange={handlsegmentedChange}
        />
      </div>
      {/* List pagination indicator */}

      <Divider
        plain
        style={{ visibility: usersList?.length > 0 ? "visible" : "hidden" }}
      >
        {`1-${usersList?.length} ${t("mailing.of")} ${
          usersList?.length > totalEntities?.all
            ? usersList?.length
            : totalEntities?.all
        }`}
      </Divider>

      {/* Scrollable users list */}
      <div
        id="scrollableParticipantsDiv"
        style={{
          overflow: "auto",
          width: from === "columnTable" ? 350 : 400,
          height: from === "columnTable" ? 150 : 400,
        }}
      >
        <Spin spinning={loading}>
          <InfiniteScroll
            id="scrollableUsersList"
            scrollThreshold="91%"
            dataLength={usersList?.length || 0}
            hasMore={usersList?.length < totalEntities?.all}
            next={handleIncrementNextPage}
            height={from === "columnTable" ? 150 : 400}
            scrollableTarget="scrollableParticipantsDiv"
            endMessage={
              usersList?.length > 0 && (
                <Divider plain>{t("tasks.endListIndicator")}</Divider>
              )
            }
          >
            <List
              size="list"
              className="membersList"
              dataSource={usersList}
              renderItem={(item, i) => {
                const avatarUrl = `${URL_ENV?.REACT_APP_BASE_URL}${URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL}${item?.avatar}`;
                const isAvatarImage = EXTENSIONS_ARRAY.includes(
                  item?.avatar?.split(".")?.pop()
                );
                return (
                  <List.Item
                    key={item?.id}
                    ref={i === 0 ? scrollListRef : null}
                  >
                    <Checkbox
                      value={item?.id}
                      onChange={(e) => handleCheckboxChange(e, item)}
                      checked={checkedItems?.some(
                        (el) => el?.id?.toString() === item?.id?.toString()
                      )}
                      style={{ width: "100%" }}
                    >
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          width: "100%",
                        }}
                      >
                        <AvatarChat
                          fontSize="0.7rem"
                          className="mx-1.5"
                          size={32}
                          height={14}
                          width={14}
                          url={avatarUrl}
                          hasImage={isAvatarImage}
                          name={getName(item?.label, "avatar")}
                          type="user"
                        />
                        <div>
                          <p>
                            {HighlightSearchW(
                              getName(item?.label, "name"),
                              searchQuery
                            )}
                          </p>
                          <span className="text-[rgb(0,0,0,0.45)]">
                            {displayFamilyName(item?.family_name)}
                          </span>
                        </div>
                      </div>
                    </Checkbox>
                  </List.Item>
                );
              }}
            />
          </InfiniteScroll>
        </Spin>
      </div>
    </>
  );
};

export default UsersList;
