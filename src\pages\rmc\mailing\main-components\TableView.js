import { memo, useMemo, useRef } from "react";
import { <PERSON><PERSON>, Card, Empty, Table } from "antd";
import { useTranslation } from "react-i18next";
import { useWindowSize } from "pages/clients&users/components/WindowSize";
import { useDispatch } from "react-redux";
import { setNumberEmailThread } from "new-redux/actions/mail.actions";
import { useNavigate } from "react-router-dom";
import { renderHighlight } from "pages/global-search/components/render-search-items";
import { conditionActions } from "./utils";
import "./index.css";

const TableView = ({
  columns,
  dataSource,
  selectedRowKeys,
  setSelectedRowKeys,
  loading,
  usedAccount,
  user,
  labelId,
  expandedRows,
  trackTableParams,
}) => {
  //
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const windowSize = useWindowSize();
  const scrollTopEventRef = useRef(null);
  //
  //
  // const statsMail = useSelector((state) => state.mailReducer.statsMail);
  //
  const toggleSelection = (record) => {
    const key = record.key;
    const newSelectedRowKeys = selectedRowKeys.includes(key)
      ? selectedRowKeys.filter((k) => k !== key)
      : [...selectedRowKeys, key];
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };
  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
    getCheckboxProps: (record) => ({
      disabled: !conditionActions(record, usedAccount, user),
    }),
    columnWidth: 25,
    renderCell: (checked, record, index, originNode) => (
      <div className="relative">
        <Button
          type="text"
          size="large"
          shape="circle"
          icon={originNode}
          onClick={(e) => {
            e.stopPropagation();
            toggleSelection(record);
          }}
          disabled={!conditionActions(record, usedAccount, user)}
          style={{
            position: "absolute",
            transform: "translate(-45%, -18%)",
          }}
        />
      </div>
    ),
  };
  //
  const scrollConfig = useMemo(
    () => ({
      x: "100%",
      y: windowSize?.height - 185,
    }),
    [windowSize?.height]
  );
  //
  // console.log({ selectedRowKeys });
  //
  return (
    <Card
      title={false}
      classNames={{ body: "mailing-table-view" }}
      styles={{ body: { padding: 0 } }}
    >
      <Table
        key={`${usedAccount?.value}-${labelId}`}
        size="small"
        loading={loading}
        showHeader={false}
        columns={columns}
        dataSource={dataSource}
        rowSelection={rowSelection}
        rowClassName={(record) => `
          hover:z-10
          cursor-pointer
          hover:shadow-sm
          hover:scale-y-105 
          hover:shadow-slate-500	
          ${record.seen ? "bg-[#f6f8fc]" : ""}
          ${selectedRowKeys.includes(record.key) ? "bg-[#c2dbff]" : ""}
          `}
        onScroll={(e) => (scrollTopEventRef.current = e.target.scrollTop)}
        onRow={(record, rowIndex) => {
          return {
            onClick: (e) => {
              const target = e.target.closest(".ant-checkbox-wrapper");
              if (target) return;
              dispatch(setNumberEmailThread(record.nbr));
              const path = window.location.pathname;
              const endPath = path.split("/").pop();
              if (isNaN(endPath)) {
                navigate(
                  `/mailing/${usedAccount?.value}/${endPath}/${record.key}`,
                  {
                    state: trackTableParams(scrollTopEventRef.current),
                  }
                );
              } else {
                navigate(
                  `/mailing/${usedAccount?.value}/${record?.source}/${record.key}`,
                  {
                    state: trackTableParams(scrollTopEventRef.current),
                  }
                );
              }
            },
          };
        }}
        expandable={
          !!expandedRows.length
            ? {
                expandedRowKeys: expandedRows,
                expandedRowRender: (record) =>
                  !!expandedRows.length ? (
                    <div
                      className="pb-3"
                      style={{
                        paddingLeft: !!Number(usedAccount.shared) ? 140 : 110,
                      }}
                    >
                      {renderHighlight(record.highlight, t, "email")}
                    </div>
                  ) : null,
                rowExpandable: (record) => !!record.highlight,
                showExpandColumn: false,
                // expandedRowClassName: (record) =>
                //   record.seen ? "bg-[#f6f8fc]" : "bg-white",
              }
            : {}
        }
        scroll={scrollConfig}
        locale={{
          emptyText: loading /*|| isDataExist*/ ? (
            <div className=" flex h-64 flex-row items-center justify-center space-x-2 text-xl">
              {t("contacts.loadingDataTable")}
              <span className="animate-bounce">...</span>
            </div>
          ) : (
            <Empty />
          ),
        }}
        pagination={false}
      />
    </Card>
  );
};

// export const decrementNumberMails =
//   (accountId, labelId, statsMail) => (dispatch) => {
//     if (!accountId || !labelId || !statsMail) {
//       return;
//     }

//     const accountStats = statsMail[accountId];
//     if (
//       !accountStats ||
//       !accountStats.labels ||
//       typeof accountStats.labels[labelId] !== "number"
//     ) {
//       return;
//     }

//     const currentCount = accountStats.labels[labelId];

//     if (currentCount <= 0) {
//       return;
//     }

//     const newCount = currentCount - 1;

//     const newStatsMail = {
//       ...statsMail,
//       [accountId]: {
//         ...accountStats,
//         labels: {
//           ...accountStats.labels,
//           [labelId]: newCount,
//         },
//       },
//     };

//     dispatch({
//       type: "SET_EMAILS_STATS",
//       payload: newStatsMail,
//     });
//   };

export default memo(TableView);
