import { Space, Tooltip, Typography } from "antd";
import Highlighter from "react-highlight-words";

export const HighlightSearchW = (text, searchWorld) => (
  <Highlighter
    highlightStyle={{
      backgroundColor: "rgb(253,226,147)",
      padding: 0,
    }}
    searchWords={[searchWorld]}
    autoEscape={true}
    textToHighlight={text ?? ""}
  />
);
//
export const TooltipPhone = (arrayOfPhoneNum, search, call, t) => (
  <Space direction="vertical">
    {arrayOfPhoneNum?.map((number) => (
      <Typography.Paragraph
        copyable={{
          tooltips: false,
          text: `${number}`?.replace(/\(|\)| /g, "")?.replace("+", "00"),
        }}
        // style={{ color: "white" }}
      >
        <Tooltip title={t("voip.clickToCall")}>
          <Typography.Text
            strong
            style={{ cursor: "pointer" }}
            onClick={() => call(`${number}`)}
          >
            {HighlightSearchW(number, search >= 3 ? search : "")}
          </Typography.Text>
        </Tooltip>
      </Typography.Paragraph>
    ))}
  </Space>
);
