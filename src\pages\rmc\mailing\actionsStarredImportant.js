import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import React, { useState } from "react";
import { AiFillStar, AiOutlineStar } from "react-icons/ai";
import {
  MdLabelImportantOutline,
  MdOutlineLabelImportant,
} from "react-icons/md";
import { StarredMessage, importantMessage } from "./services/ActionsApi";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";

const ActionsStarredImportant = ({
  record,
  //   clickStar,
  //   setClickStar,
  //   clickImportant,
  //   setClickImportant,
  getMails,
  usedAccount,
}) => {
  const [t] = useTranslation("common");
  const { user } = useSelector(({ user }) => user);
  const [clickStar, setClickStar] = useState(false);
  const [clickImportant, setClickImportant] = useState(false);

  const conditionActions = (record, condition) => {
    if (
      record?.owner?.owner &&
      record?.transfert &&
      record?.owner?.owner === user?.id &&
      record?.transfert?.account_id === String(usedAccount?.value)
    ) {
      condition = true;
    } else if (
      !record?.owner?.owner &&
      record?.transfert &&
      record?.transfert?.account_id === String(usedAccount?.value)
    ) {
      condition = true;
    } else if (
      record?.owner?.owner &&
      !record?.transfert &&
      record?.owner?.owner === user?.id &&
      record?.owner?.user_id === user?.id
    ) {
      condition = true;
    } else if (!record?.owner?.owner && !record?.transfert) {
      condition = true;
    } else if (
      record?.owner?.owner &&
      !record?.transfert &&
      record?.owner?.owner === user?.id &&
      record?.owner?.user_id !== user?.id
    ) {
      return true;
    } else {
      // verifier
      condition = false;
    }
    return condition;
  };

  const StarredMail = async (id) => {
    try {
      const response = await StarredMessage(id, setClickStar, t);
      response && getMails();
    } catch (error) {
      console.log("error: " + error);
    }
  };

  const ImportantMail = async (id, message) => {
    try {
      const response = await importantMessage({
        id,
        message,
        setClickImportant,
        t,
      });
      response && getMails();
    } catch (error) {
      console.log("error: " + error);
    }
  };

  return (
    <div className="flex items-center">
      <Tooltip
        title={
          record.starred === 0
            ? t("mailing.markfavorite")
            : t("mailing.marknonfavorite")
        }
        placement="bottom"
      >
        <Button
          disabled={clickStar || !conditionActions(record)}
          shape="circle"
          type="text"
          onClick={(e) => {
            e.stopPropagation();
            StarredMail(record.key);
          }}
          icon={
            record.starred === 0 ? (
              <AiOutlineStar
                style={{
                  color: conditionActions(record) ? "#C8C9C8" : "##F0F0F0",
                  height: "35px",
                  width: "20px",
                  // marginTop: "4px",
                  cursor: "pointer",
                }}
              />
            ) : (
              <AiFillStar
                style={{
                  color: conditionActions(record) ? "#fadb14" : "##F0F0F0",
                  height: "35px",
                  width: "20px",
                  // marginTop: "4px",
                  cursor: "pointer",
                }}
              />
            )
          }
        />
      </Tooltip>

      <Tooltip
        title={
          record.important === 0
            ? t("mailing.markImportant")
            : t("mailing.marknotImportant")
        }
        placement="bottom"
      >
        <Button
          disabled={clickImportant || !conditionActions(record)}
          type="text"
          shape="circle"
          icon={
            record.important === 0 ? (
              <MdLabelImportantOutline
                onClick={(e) => {
                  e.stopPropagation();
                  ImportantMail(record.key, t("mailing.emailImportant"));
                }}
                style={{
                  color: conditionActions(record) ? "#C8C9C8" : "##F0F0F0",
                  height: "35px",
                  width: "20px",
                  // marginTop: "4px",
                  cursor: "pointer",
                }}
              />
            ) : (
              <MdOutlineLabelImportant
                onClick={(e) => {
                  e.stopPropagation();
                  ImportantMail(record.key, t("mailing.emailnotImportant"));
                }}
                style={{
                  color: conditionActions(record) ? "#7a7a7a" : "##F0F0F0",
                  height: "35px",
                  width: "20px",
                  // marginTop: "4px",
                  cursor: "pointer",
                }}
              />
            )
          }
        />
      </Tooltip>
    </div>
  );
};

export default ActionsStarredImportant;
