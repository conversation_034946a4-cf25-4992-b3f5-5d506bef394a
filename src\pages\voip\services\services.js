import { URL_ENV } from "index";
import { getLogs } from "../../../new-redux/actions/voip.actions/getLogs";
import { getVoice } from "../../../new-redux/actions/voip.actions/getVoice";
import {
  SET_NBR_VOICES_CALLS_QUEUES,
  SET_NEW_MISSED_CALL,
  SET_NEW_MISSED_QUEUE_GROUP,
  SET_NEW_VOICE_MESSAGING,
} from "../../../new-redux/constants";
import { generateAxios } from "../../../services/axiosInstance";
import axios from "axios";

const CancelToken = axios.CancelToken;
let cancel;

// const URL_ENV = window.URL_ENV || {};
// console.log(URL_ENV);
// const baseURL = URL_ENV?.REACT_APP_BASE_URL+process.env.REACT_APP_SUFFIX_API;

export const fetchColleagues = async (page, limit, search, id) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(
    `/colleagues?page=${page || ""}&limit=${limit || ""}&key=${
      search || ""
    }&id=${id || ""}`
  );
//
export const fetchPhoneBook = async (
  id,
  page,
  limit,
  search,
  type,
  family,
  sources,
  section
  // sort  &sort=${sort || ""}
) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(
    `/phonebooknew?page=${page || ""}&limit=${limit || ""}&key=${
      search || ""
    }&type=${type || ""}&family_id=${family || ""}&channel=${
      sources || ""
    }&id=${id || ""}&section=${section || ""}`
  );
//
export const fetchContactsTypes = async () =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(`/config-phonebook`);
//
export const getCallsLogs = async (
  limit,
  page,
  search,
  start_date,
  end_date,
  filter,
  relationId
) => {
  // If there's an ongoing request, cancel it
  if (cancel) {
    cancel("Cancelling the previous request");
  }

  const url = `/get-calls?page=${page || ""}&limit=${limit || ""}&key=${
    search || ""
  }&start_date=${start_date || ""}&end_date=${end_date || ""}${
    relationId ? `&id_relation=${relationId}` : ""
  }`;

  var filterUrl = null;
  if (filter?.length) {
    filterUrl = filter?.map((item) => `filter[]=${item}`).join("&");
  }

  return await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(`${url}${filterUrl ? "&" + filterUrl : ""}`, {
    // Add the cancel token to the request configuration
    cancelToken: new CancelToken(function executor(c) {
      cancel = c;
    }),
  });
};
//
export const exportCallsLog = async (
  search,
  start_date,
  end_date,
  filter,
  isGroupLog
) => {
  const CancelToken = axios.CancelToken;
  let cancel;

  // Define the cancel token right away
  const source = CancelToken.source();

  const url = `/${
    isGroupLog ? "export-queue" : "export-journal"
  }?key=${encodeURIComponent(search || "")}&start_date=${encodeURIComponent(
    start_date || ""
  )}&end_date=${encodeURIComponent(end_date || "")}`;

  let filterUrl = "";
  if (filter && filter.length) {
    filterUrl = filter
      .map((item) => `filter[]=${encodeURIComponent(item)}`)
      .join("&");
  }

  const config = {
    cancelToken: source.token,
    responseType: "blob",
  };

  try {
    const response = await generateAxios(
      URL_ENV.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
    ).get(`${url}${filterUrl ? "&" + filterUrl : ""}`, config);

    return response;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log("Request canceled", error.message);
    } else {
      // Handle other errors
      throw error;
    }
  }
};
//
export const getVoicesMailLog = async (
  limit,
  page,
  search,
  start_date,
  end_date,
  filter
) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(
    `/get-voice-mail?page=${page || ""}&limit=${limit || ""}&search=${
      search || ""
    }
  &start_date=${start_date || ""}&end_date=${end_date || ""}&filter=${
      filter || ""
    }`
  );
//
export const getGroupsAndQueues = async (
  limit,
  page,
  search,
  start_date,
  end_date,
  filter,
  selectedGroup
) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(
    `/calls-queue-group?page=${page || ""}&limit=${limit || ""}&key=${
      search || ""
    }&start_date=${start_date || ""}&end_date=${end_date || ""}${
      selectedGroup !== "all" ? `&${selectedGroup}` : ""
    }${
      filter.length
        ? "&" + filter?.map((item) => `filter[]=${item}`).join("&")
        : ""
    }`
  );
//
export const getKpiCall = async (source, startDate, endDate) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(
    `/dashboard/${source}?start_date=${startDate || ""}&end_date=${
      endDate || ""
    }`
  );
//
export const getConfigQueuesAndGroups = async () =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(`/queue-group-connected-user`);
//
export const getFirstCallDate = async (type) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(`/first-call-date?type=${type}`);

export const changeStateVoiceMsg = async (poste, msg_id) =>
  await generateAxios(URL_ENV?.REACT_APP_VOIP_URL).get(
    `/SetConsulteMessageVocallIPBXV3API?compte=${
      URL_ENV?.REACT_APP_VOIP_CLIENT ?? "client"
    }&poste=${poste}&id=${msg_id}`
  );
//
export const getTags = async () => {
  const { data } = await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(`/tag-call/config`);

  return data?.map((tag) => ({
    id: tag?.typetask ? `${tag?.id}-${tag?.typetask?.id}` : `${tag?.id}`,
    label: tag?.label,
    color: tag?.color,
    icon: tag?.icon,
    taskType: tag?.typetask ? true : false,
  }));
};

export const createTagsActivity = async (formData) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).post(`/tag-call/new`, formData);

export const updateTagsActivity = async (tagsId, formData) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).post(`/tag-call/update/${tagsId}`, formData);

export const deleteTags = async (formData) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).post(`/tag-call/delete`, formData);

export const seenCallsOrVoicesOrGroups = async (title) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(`/notifications/${title}`);

export const getNewNotifVoip = async (dispatch) => {
  const {
    data: { missed_call, voice_mail, queue_group },
  } = await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(
    `/notifications/count?titles=[missed_call,voice_mail,missed_call_queue_group]`
  );

  dispatch({
    type: SET_NBR_VOICES_CALLS_QUEUES,
    payload: {
      nbr_missed_voices: voice_mail,
      nbr_missed_calls: missed_call,
      nbr_missed_queues: queue_group,
    },
  });
  if (missed_call) {
    dispatch(getLogs(true));
  }
  if (voice_mail) {
    dispatch(getVoice());
  }
};
//
export const affectations = async (family_id, contact_id, search, signal) => {
  const encodedSearch = encodeURIComponent(search);
  return await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(
    `/affectation/config/${family_id}?contact_id=${contact_id}&key=${encodedSearch}`,
    { signal }
  );
};
//
export const postAffectations = async (formData) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).post(`/affectation`, formData);
//
export const deleteAffectation = async (id) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).delete(`/affectation/delete/${id}`);
//
export const getLog360 = async (id, limit, page) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(`/get-calls-360?id_relation=${id}&limit=${limit}&page=${page}`);
//
export const getGroups = async () =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(`/queue-group-listing`);
//
export const getConfigIPBX = async () =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(`config-ipbx`);
//
export const setConfigIPBX = async (formData) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).post(`config-ipbx`, formData);
//
export const generateEventMercure = async (formData) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).post(`wp/notify`, formData);
//
export const getLivePanelData = async () =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(`get-live-pannel`);
