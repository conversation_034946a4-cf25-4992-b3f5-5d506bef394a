import { useEffect, useState } from "react";
import { Form, Image, Upload } from "antd";
import { useTranslation } from "react-i18next";
//
const getBase64 = (file) =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
//
const ImgField = ({
  fieldId,
  label,
  TooltipDescription,
  description,
  required,
  value,
  reset,
  readOnly,
}) => {
  const [t] = useTranslation("common");

  const [fileList, setFileList] = useState(value || []);

  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState("");

  const handleChange = ({ fileList }) => setFileList([...fileList]);

  useEffect(() => {
    if (reset) setFileList([]);
    return () => {
      setFileList([]);
    };
  }, [reset]);

  const handlePreview = async (file) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj);
    }
    setPreviewImage(file.url || file.preview);
    setPreviewOpen(true);
  };

  return (
    <>
      <Form.Item
        key={fieldId}
        label={`${label} (Max 1)`}
        tooltip={TooltipDescription(description)}
        name={fieldId}
        // initialValue={value || []}
        rules={[
          {
            required: required,
            message: t("contacts.fieldXRequired", { x: label }),
          },
        ]}
      >
        <Upload
          accept="image/*"
          listType="picture-circle"
          disabled={readOnly}
          onPreview={handlePreview}
          beforeUpload={() => false}
          fileList={fileList}
          onChange={handleChange}
        >
          {fileList?.length === 0 && `+ ${t("contacts.upload")}`}
        </Upload>
      </Form.Item>
      <div className="hidden">
        <Image
          preview={{
            visible: previewOpen,
            onVisibleChange: (visible) => setPreviewOpen(visible),
            src: previewImage,
          }}
        />
      </div>
    </>
  );
};

export default ImgField;
