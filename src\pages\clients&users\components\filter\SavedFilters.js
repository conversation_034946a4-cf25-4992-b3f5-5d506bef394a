import { memo, useCallback, useEffect, useState } from "react";
import { Button, Input, Select, Space, Tooltip } from "antd";
import { DownOutlined, LoadingOutlined, SaveOutlined } from "@ant-design/icons";
import { saveFilterFamily } from "pages/clients&users/services/services";
import { toastNotification } from "components/ToastNotification";
import { useTranslation } from "react-i18next";

const SavedFilters = ({
  filters,
  setFilters,
  setDynamicFilter,
  dynamicFilter,
  savedFilters,
  isOnlySelect,
  familyId,
  setConfig,
  applyFilter,
}) => {
  //
  const [t] = useTranslation("common");
  //
  const [openSaveFilter, setOpenSaveFilter] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState(null);
  const [loadSelect, setLoadSelect] = useState(false);
  const [loadSaveFilter, setLoadSaveFilter] = useState(false);
  const [filterName, setFilterName] = useState("");
  //
  // console.log({ filters, savedFilters });
  //
  const handleSelectFilter = (selectedFilter) => {
    setSelectedFilter(selectedFilter);
    if (selectedFilter) {
      const findFilter = savedFilters.find((filter) => {
        const key = Object.keys(filter)?.[0];
        return key === selectedFilter;
      });
      if (findFilter) {
        setDynamicFilter(findFilter[selectedFilter]);
        setFilters(findFilter[selectedFilter]);
      }
    } else {
      setDynamicFilter([]);
      setFilters([]);
    }
  };
  //
  const selectFilter = (
    <Select
      style={{ width: "17.5rem" }}
      showSearch
      allowClear
      placeholder="Sélectionner un filtre sauvegardé"
      options={
        savedFilters?.map((f) => {
          const filterName = Object.keys(f)?.[0];
          if (!filterName) return null;
          return {
            label: filterName.replaceAll("_", " "),
            value: filterName,
          };
        }) ?? []
      }
      suffixIcon={loadSelect ? <LoadingOutlined /> : <DownOutlined />}
      disabled={loadSelect}
      value={selectedFilter}
      onChange={handleSelectFilter}
    />
  );
  //
  // Track if the "filters.length" change, so set openSaveFilter to false
  useEffect(() => {
    setOpenSaveFilter(false);
    setSelectedFilter(null);
  }, [filters]);
  //
  // check if the current filter is already exists in the saved filter
  const checkIfFilterExist = useCallback(() => {
    setLoadSelect(true);
    if (!savedFilters.length) {
      setLoadSelect(false);
      return;
    }
    const newFilter = filters.map(({ id, ...rest }) => rest);
    const newSavedFilters = savedFilters.map((obj) =>
      Object.entries(obj).reduce((acc, [key, value]) => {
        acc[key] = value.map(({ id, ...rest }) => rest);
        return acc;
      }, {})
    );
    newSavedFilters.forEach((filter) => {
      for (const [key, value] of Object.entries(filter)) {
        // const
        if (JSON.stringify(value) === JSON.stringify(newFilter)) {
          setSelectedFilter(key);
        }
      }
    });
    setLoadSelect(false);
  }, [filters, savedFilters]);

  useEffect(() => {
    checkIfFilterExist();
  }, [checkIfFilterExist]);
  //
  const saveFilter = async (isSaveAndApply = false) => {
    try {
      setLoadSaveFilter(true);
      const formData = new FormData();
      const name = filterName.replaceAll(" ", "_");
      const newFilter = { [name]: filters };
      formData.append("family_id", familyId);
      formData.append("type", "manual");
      formData.append(
        "filter",
        JSON.stringify([newFilter, ...(savedFilters ?? [])])
      );
      await saveFilterFamily(formData);
      setConfig((prev) => ({
        ...prev,
        filters: [newFilter, ...(savedFilters ?? [])],
      }));
      if (isSaveAndApply) applyFilter();
      setFilterName("");
      setOpenSaveFilter(false);
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? err.message : err);
    } finally {
      setLoadSaveFilter(false);
    }
  };
  //
  return savedFilters?.length && isOnlySelect ? (
    <div className="pr-4">{selectFilter}</div>
  ) : filters.length && openSaveFilter ? (
    <Space.Compact style={{ paddingRight: "2rem" }}>
      <Input
        showCount
        maxLength={100}
        style={{ width: "100%" }}
        placeholder="Saisir le nom de ce filtre"
        onChange={(e) => setFilterName(e.target.value)}
        value={filterName}
      />
      <Tooltip
        title={
          JSON.stringify(filters) !== JSON.stringify(dynamicFilter) &&
          "Sauvgarder & Appliquer"
        }
      >
        <Button
          type="primary"
          icon={<SaveOutlined />}
          onClick={() => saveFilter(true)}
          loading={loadSaveFilter}
        />
      </Tooltip>
    </Space.Compact>
  ) : filters.length ? (
    <div className="flex justify-between pr-8">
      {!selectedFilter && (
        <Button
          type="link"
          onClick={() => setOpenSaveFilter(true)}
        >{`Sauvegarder ce filter (${filters.length})`}</Button>
      )}
      {!!savedFilters?.length && selectFilter}
    </div>
  ) : null;
};

export default memo(SavedFilters);
