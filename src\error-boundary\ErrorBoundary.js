/**
 * ErrorBoundary component that catches errors in its children components and displays an error message.
 * @returns React component
 */

import React from "react";
import i18next from "i18next";
import { Button, Result } from "antd";
import { ArrowLeftOutlined } from "@ant-design/icons";

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, message: "" };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error) {
    this.setState({ ...this.state, message: error.message });
  }
  render() {
    const { hasError } = this.state;
    if (hasError) {
      return (
        <Result
          status="500"
          title="500"
          subTitle={i18next.t("common:toasts.errorFetchApi")}
          extra={
            process.env.REACT_APP_BRANCH.includes("dev") ? (
              <div className="text-2xl text-red-500 ">
                <pre>
                  <code> ERROR IS : {JSON.stringify(this.state.message)} </code>
                </pre>
              </div>
            ) : (
              <a target="_self" href="/">
                <Button
                  onClick={() => (window.location.href = "/")}
                  type="link"
                  className="flex w-full items-center justify-center px-10 py-5"
                  icon={<ArrowLeftOutlined />}
                >
                  {i18next.t("common:BackToHome")}
                </Button>
              </a>
            )
          }
        />
      );
    }

    return this.props.children;
  }
}
export default ErrorBoundary;
