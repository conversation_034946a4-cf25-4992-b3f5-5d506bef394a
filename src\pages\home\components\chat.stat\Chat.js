import { Card, Col, Row, Select, Tooltip } from "antd";
import { URL_ENV } from "index";
import { useState, useEffect } from "react";
import { generateAxios } from "services/axiosInstance";
import {
  Chart2Bars,
  DonutChart2,
  <PERSON><PERSON>hart,
  <PERSON><PERSON>ar<PERSON><PERSON>,
  Pie<PERSON>hart,
  PieChartWithPercent,
} from "../ChartsDashboard";
import { useTranslation } from "react-i18next";
import { InfoCircleOutlined } from "@ant-design/icons";
import { useSelector } from "react-redux";
import CardStat from "../CardStat";

const DashboardChat = ({ start, end }) => {
  const [messagesByType, setMessageByType] = useState({});
  const [messagesFrequencyPerPeriod, setMessagesFrequencyPerPeriod] = useState(
    {}
  );
  const [
    messagesModuleFrequencyPerPeriod,
    setMessagesModuleFrequencyPerPeriod,
  ] = useState([]);
  const [typesByComunication, setTypesByComunication] = useState({});
  const [typesByDevice, setTypesByDevice] = useState({});
  const [topActiveConv, setTopActiveConv] = useState({});
  const [topSentMsg, setTopSentMsg] = useState({});
  const [selectedTypeConv, setSelectedTypeConv] = useState(0);
  const [ticketsIntegrations, setTicketsIntegrations] = useState({});

  const { i18n } = useTranslation("common");
  const [t] = useTranslation("common");
  const user = useSelector(({ user }) => user?.user);
  useEffect(() => {
    const fetchMessageByType = async () => {
      try {
        const response = await generateAxios(
          URL_ENV?.REACT_APP_OAUTH_CHAT_API +
            "/" +
            process.env.REACT_APP_SUFFIX_API
        ).get(
          `/message-by-type?start_date=${start}&end_date=${end}&lang=${i18n.language}&format_date=${user?.location?.date_format}`
        );
        setMessageByType(response?.data);
      } catch (error) {
        console.error(
          "Erreur lors de la récupération des appels totaux :",
          error
        );
      }
    };

    fetchMessageByType();
  }, [start, end, i18n.language, user?.location?.date_format]);
  useEffect(() => {
    const fetchMessagesFrequencyPerPeriod = async () => {
      try {
        const response = await generateAxios(
          URL_ENV?.REACT_APP_OAUTH_CHAT_API +
            "/" +
            process.env.REACT_APP_SUFFIX_API
        ).get(
          `/message-frequency-by-period?start_date=${start}&end_date=${end}&lang=${i18n.language}&format_date=${user?.location?.date_format}`
        );
        setMessagesFrequencyPerPeriod({
          series: response?.data?.series,
          categories: response?.data?.months,
          name: response?.data?.name,
        });
      } catch (error) {
        console.error(
          "Erreur lors de la récupération des appels totaux :",
          error
        );
      }
    };

    fetchMessagesFrequencyPerPeriod();
  }, [start, end, i18n.language, user?.location?.date_format]);
  useEffect(() => {
    const fetchMessagesModuleFrequencyPerPeriod = async () => {
      try {
        const response = await generateAxios(
          URL_ENV?.REACT_APP_OAUTH_CHAT_API +
            "/" +
            process.env.REACT_APP_SUFFIX_API
        ).get(
          `/message-module-frequency-by-period?start_date=${start}&end_date=${end}&lang=${i18n.language}&format_date=${user?.location?.date_format}`
        );
        // console.log({
        //   categories: response?.data?.data.map((el) => el.type),
        //   series: response?.data?.data,
        // });

        setMessagesModuleFrequencyPerPeriod(response?.data);
      } catch (error) {
        console.error(
          "Erreur lors de la récupération des appels totaux :",
          error
        );
        // setMessagesFrequencyPerPeriod({
        //   series: response?.data?.data,
        //   categories: response?.data?.months,
        //   name: response?.data?.name,
        // });
      }
    };

    fetchMessagesModuleFrequencyPerPeriod();
  }, [start, end, i18n.language, user?.location?.date_format]);
  useEffect(() => {
    const fetchTypeByComunication = async () => {
      try {
        const response = await generateAxios(
          URL_ENV?.REACT_APP_OAUTH_CHAT_API +
            "/" +
            process.env.REACT_APP_SUFFIX_API
        ).get(
          `/type-communication?start_date=${start}&end_date=${end}&lang=${i18n.language}&format_date=${user?.location?.date_format}`
        );
        // console.log({
        //   categories: response?.data?.data.map((el) => el.type),https://sphere.cmk.biz/tasks
        //   series: response?.data?.data,
        // });
        setTypesByComunication(response.data);
        // setMessagesModuleFrequencyPerPeriod(response?.data);
      } catch (error) {
        console.error(
          "Erreur lors de la récupération des appels totaux :",
          error
        );
        // setMessagesFrequencyPerPeriod({
        //   series: response?.data?.data,
        //   categories: response?.data?.months,
        //   name: response?.data?.name,
        // });
      }
    };

    fetchTypeByComunication();
  }, [start, end, i18n.language, user?.location?.date_format]);
  useEffect(() => {
    const fetchTypeByDevice = async () => {
      try {
        const response = await generateAxios(
          URL_ENV?.REACT_APP_OAUTH_CHAT_API +
            "/" +
            process.env.REACT_APP_SUFFIX_API
        ).get(
          `/type-device-communication?start_date=${start}&end_date=${end}&lang=${i18n.language}&format_date=${user?.location?.date_format}`
        );
        // console.log({
        //   categories: response?.data?.data.map((el) => el.type),
        //   series: response?.data?.data,
        // });
        setTypesByDevice(response.data);
        // setMessagesModuleFrequencyPerPeriod(response?.data);
      } catch (error) {
        console.error(
          "Erreur lors de la récupération des appels totaux :",
          error
        );
        // setMessagesFrequencyPerPeriod({
        //   series: response?.data?.data,
        //   categories: response?.data?.months,
        //   name: response?.data?.name,
        // });
      }
    };

    fetchTypeByDevice();
  }, [start, end, i18n.language, user?.location?.date_format]);
  useEffect(() => {
    const fetchTopActiveConv = async () => {
      try {
        const response = await generateAxios(
          URL_ENV?.REACT_APP_OAUTH_CHAT_API +
            "/" +
            process.env.REACT_APP_SUFFIX_API
        ).get(
          `/top-active-conversations?start_date=${start}&end_date=${end}&lang=${i18n.language}&type=${selectedTypeConv}&format_date=${user?.location?.date_format}`
        );
        // console.log({
        //   categories: response?.data?.data.map((el) => el.type),
        //   series: response?.data?.data,
        // });
        setTopActiveConv(response.data);
        // setMessagesModuleFrequencyPerPeriod(response?.data);
      } catch (error) {
        console.error(
          "Erreur lors de la récupération des appels totaux :",
          error
        );
        // setMessagesFrequencyPerPeriod({
        //   series: response?.data?.data,
        //   categories: response?.data?.months,
        //   name: response?.data?.name,
        // });
      }
    };

    fetchTopActiveConv();
  }, [
    start,
    end,
    i18n.language,
    selectedTypeConv,
    user?.location?.date_format,
  ]);
  useEffect(() => {
    const fetchTopActiveConv = async () => {
      try {
        const response = await generateAxios(
          URL_ENV?.REACT_APP_OAUTH_CHAT_API +
            "/" +
            process.env.REACT_APP_SUFFIX_API
        ).get(
          `/ticket-frequency-by-period?start_date=${start}&end_date=${end}&lang=${i18n.language}&type=${selectedTypeConv}&format_date=${user?.location?.date_format}`
        );
        // console.log({
        //   categories: response?.data?.data.map((el) => el.type),
        //   series: response?.data?.data,
        // });
        setTicketsIntegrations(response.data);
        // setMessagesModuleFrequencyPerPeriod(response?.data);
      } catch (error) {
        console.error(
          "Erreur lors de la récupération des appels totaux :",
          error
        );
        // setMessagesFrequencyPerPeriod({
        //   series: response?.data?.data,
        //   categories: response?.data?.months,
        //   name: response?.data?.name,
        // });
      }
    };

    fetchTopActiveConv();
  }, [
    start,
    end,
    i18n.language,
    selectedTypeConv,
    user?.location?.date_format,
  ]);

  // useEffect(() => {
  //   const fetchTopSentMsgs = async () => {
  //     try {
  //       const response = await generateAxios(
  //         URL_ENV?.REACT_APP_OAUTH_CHAT_API +
  //           "/" +
  //           process.env.REACT_APP_SUFFIX_API
  //       ).get(
  //         `/top-sent-messages?start_date=${start}&end_date=${end}&lang=${i18n.language}`
  //       );
  //       // console.log({
  //       //   categories: response?.data?.data.map((el) => el.type),
  //       //   series: response?.data?.data,
  //       // });
  //       setTopSentMsg(response.data);
  //       console.log(response.data);
  //       // setMessagesModuleFrequencyPerPeriod(response?.data);
  //     } catch (error) {
  //       console.error(
  //         "Erreur lors de la récupération des appels totaux :",
  //         error
  //       );
  //       // setMessagesFrequencyPerPeriod({
  //       //   series: response?.data?.data,
  //       //   categories: response?.data?.months,
  //       //   name: response?.data?.name,
  //       // });
  //     }
  //   };

  //   fetchTopSentMsgs();
  // }, [start, end, i18n.language]);
  return (
    <Row gutter={[16, 16]}>
      {/* Task Status Radial Chart */}
      <Col className="gutter-row" span={8}>
        <CardStat title={messagesByType?.name}>
          <DonutChart2
            data={messagesByType?.data}
            total={messagesByType?.total || 0}
            name={""}
          />
        </CardStat>
      </Col>
      <Col className="gutter-row" span={8}>
        <CardStat title={typesByDevice?.name}>
          <PieChartWithPercent data={{ ...typesByDevice, name: "" }} />
        </CardStat>
      </Col>
      <Col className="gutter-row" span={8}>
        <CardStat title={typesByComunication?.name}>
          <DonutChart2
            data={typesByComunication?.data}
            total={typesByComunication?.total || 0}
            name=""
          />
        </CardStat>
      </Col>
      <Col className="gutter-row" span={12}>
        <CardStat
          title={messagesFrequencyPerPeriod?.name}
          subtitle={t("common:dashboard.independantNumber")}
        >
          <EvolutionChart
            data={{ ...messagesFrequencyPerPeriod, name: "" }}
            isExistDate={true}
          />
        </CardStat>
      </Col>
      <Col className="gutter-row" span={12}>
        <CardStat title={messagesModuleFrequencyPerPeriod?.name}>
          <OneBarChart
            data={{ ...messagesModuleFrequencyPerPeriod, name: "" }}
          />
        </CardStat>
      </Col>
      <Col className="gutter-row" span={12}>
        <CardStat
          title={topActiveConv?.name}
          extra={
            <Select
              popupMatchSelectWidth={false}
              showSearch
              filterOption={(input, option) =>
                (option?.label ?? "")
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
              onChange={(value) => setSelectedTypeConv(value)}
              value={selectedTypeConv}
              options={[
                { value: 0, label: t("chat.members") },
                { value: 1, label: t("chat.groups") },
              ]}
            />
          }
        >
          <OneBarChart data={topActiveConv} hasSelect={true} />
        </CardStat>
      </Col>

      <Col className="gutter-row" span={12}>
        <CardStat
          title={ticketsIntegrations?.name}
          subtitle={t("common:dashboard.independantNumber")}
        >
          <EvolutionChart
            data={{
              ...ticketsIntegrations,
              categories: ticketsIntegrations?.months,
              name: "",
            }}
            isExistDate={true}
          />
        </CardStat>
      </Col>
      {/* <Col className="gutter-row" span={12}>
        <OneBarChart data={topSentMsg} />
      </Col> */}
    </Row>
  );
};

export default DashboardChat;
