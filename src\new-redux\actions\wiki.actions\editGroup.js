import {
  EDIT_GROUP_WIKI_SUCCESS,
  EDIT_GROUP_WIKI_ERROR,
  IS_LOADING_WIKI,
} from "../../constants";
import MainService from "../../../services/main.service";
import { useTranslation } from "react-i18next";

import { toastNotification } from "../../../components/ToastNotification";

export const updateGroup =
  (groupId, group, setOpenAdd, setEditingKey, t) => async (dispatch) => {
    try {
      dispatch({ type: IS_LOADING_WIKI });
      const response = await MainService.updateGroup(groupId, group);
      dispatch({
        type: EDIT_GROUP_WIKI_SUCCESS,
        payload: { response: response?.data?.data, id: groupId },
      });
      setOpenAdd(false);
      setEditingKey("");

      toastNotification(
        "success",
        group.label_fr + " updated successfully!",
        "topRight"
      );
    } catch (error) {
      dispatch({
        type: EDIT_GROUP_WIKI_ERROR,
        payload: error,
      });
      console.log(error.response.status);
      if (error?.response?.status === 422) {
        toastNotification(
          "error",
          error?.response?.data?.errors[0],
          "topRight"
        );
      } else toastNotification("error", t("toasts.somethingWrong"), "topRight");
    }
  };
//.newGroup, label_en: group.newGroupEn, description_fr: group.descriptionFr, description_en: group.descriptionEn
