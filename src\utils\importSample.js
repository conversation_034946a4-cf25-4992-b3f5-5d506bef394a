export const getSelectedFamilyText = (familyId, t) => {
  switch (familyId) {
    case 1:
      return t("import.organisation");
    case 2:
      return t("import.contact");
    case 3:
      return t("import.deal");
    case 4:
      return t("import.user");
    case 5:
      return t("import.product");
    case 6:
      return t("import.helpdesk");
    case 7:
      return t("import.project");
    case 8:
      return t("import.booking");
    case 9:
      return t("import.leads");
    default:
      return "";
  }
};
