import { useRef, useState } from "react";
import { Segmented } from "antd";
import { useTranslation } from "react-i18next";
import Journal from "./journal";
import { useSelector } from "react-redux";

const CallLogs = ({ setUpWebphone, handleActionWebPhone }) => {
  const [t] = useTranslation("common");

  const scrollableDivRef = useRef(null);
  const journalTab = useSelector((state) => state.voip.logs);
  const [value, setValue] = useState("all");

  // console.log({ value });
  const onChangeSegmented = (value) => {
    setValue(value);
    if (scrollableDivRef.current) {
      scrollableDivRef.current.scrollTo(0, 0);
    }
  };

  return (
    <>
      <div className="z-6 text-center	">
        <Segmented
          disabled={!journalTab?.length}
          options={[
            {
              label: t("voip.all"),
              value: "all",
            },
            {
              label: t("voip.missed"),
              value: "missed",
            },
          ]}
          value={value}
          onChange={onChangeSegmented}
          // size="small"
        />
      </div>
      <Journal
        SegmentedValue={value}
        scrollableDivRef={scrollableDivRef}
        setUpWebphone={setUpWebphone}
        handleActionWebPhone={handleActionWebPhone}
      />
    </>
  );
};
export default CallLogs;
