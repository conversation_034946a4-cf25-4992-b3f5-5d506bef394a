/**
 * @name CheckList
 *
 * @description CheckList component that displays a list of items with checkboxes and editable content.
 *
 * @param {Number} headerHeight - The height of the header.
 * @param {String} from - The source of the checklist.
 * @param {Boolean} openModal - Open the modal for creating or updating a list item.
 * @param {Object} contactInfo - Information about the contact.
 * @param {Function} setOpenModal - Function to set the state of the modal.
 * @param {Function} setKpi - Function to set the key performance indicator.
 *
 * @returns A component that displays a checklist with interactive features like drag and drop, editing, and deleting items.
 */

//React and 3rd party libraries.
import { useCallback, useEffect, useState } from "react";
import {
  Button,
  Checkbox,
  Collapse,
  Divider,
  Empty,
  Form,
  Input,
  Modal,
  Popconfirm,
  Space,
  Spin,
  Switch,
  Tooltip,
  Typography,
  theme,
} from "antd";
import {
  CaretRightOutlined,
  DeleteOutlined,
  EditOutlined,
  HolderOutlined,
  LoadingOutlined,
  MinusCircleOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import { DndContext } from "@dnd-kit/core";
import {
  SortableContext,
  useSortable,
  arrayMove,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import moment from "moment";

//Common imports.
import MainService from "../../../services/main.service";
import { GenericButton } from "../GenericButton";
import InfiniteScroll from "react-infinite-scroll-component";
import { useWindowSize } from "pages/clients&users/components/WindowSize";
import { URL_ENV } from "index";
import { AvatarChat } from "components/Chat";
import { EXTENSIONS_ARRAY } from "pages/tasks/helpers/calculateSum";
import { getName } from "pages/layouts/chat/utils/ConversationUtils";

const CheckList = ({
  openModal,
  setOpenModal,
  setKpi,
  headerHeight,
  from,
  contactInfo,
  setTodoListRecords = () => {},
  setCountChanges = () => {},
}) => {
  const [checklistData, setChecklistData] = useState([]);
  const [originalChecklistData, setOriginalChecklistData] = useState([]);
  const [itemToUpdateData, setItemToUpdataData] = useState({});
  const [fields, setFields] = useState([]);
  const [checklistKey, setChecklistKey] = useState(null);
  const [checklistItemKey, setChecklistItemKey] = useState(null);
  const [todoListPage, setTodoListPage] = useState(1);
  const [todoListLastPage, setTodoListLastPage] = useState(1);
  //Handle loading when different manipulations occurs.
  const [isLoading, setIsLoading] = useState({
    createItem: false,
    getAllItems: false,
    getSingleItem: false,
    deleteItem: false,
    deleteList: false,
    updateListItem: false,
    checkItem: false,
    updateRank: false,
  });

  const [t] = useTranslation("common");
  const [checkListFormInModal] = Form.useForm();
  const [checkListForm] = Form.useForm();
  const windowSize = useWindowSize();
  const user = useSelector((state) => state?.user?.user);

  //Create new checklist
  const createList = async (payload) => {
    try {
      setIsLoading({ ...isLoading, createItem: true });
      const response = await MainService.createChecklist(
        from === "activities" ? 0 : contactInfo?.family_id,
        contactInfo?.id,
        payload
      );
      setChecklistData([response?.data?.data, ...checklistData]);
      setOriginalChecklistData((prev) => [response?.data?.data, ...prev]);
      setOpenModal(false);
      setKpi((prev) =>
        prev.map((el) =>
          el.title === "Todolist" ? { ...el, value: el.value + 1 } : el
        )
      );
      setIsLoading({ ...isLoading, createItem: false });
      setCountChanges((prev) => prev + 1);
      checkListFormInModal.resetFields();
    } catch (error) {
      console.log(`Error ${error}`);
      setIsLoading({ ...isLoading, createItem: false });
    }
  };

  //Update an existing checklist
  const updateList = async (payload, idList) => {
    try {
      setIsLoading({
        ...isLoading,
        createItem: idList ? false : true,
        updateListItem: idList ? true : false,
      });
      const response = await MainService.updateChecklist(
        from === "activities" ? 0 : contactInfo?.family_id,
        contactInfo?.id,
        idList ? idList : itemToUpdateData?._id,
        payload
      );
      setChecklistData(
        checklistData &&
          checklistData.map((data) =>
            data?._id === response?.data?.data?._id
              ? response?.data?.data
              : data
          )
      );
      setOriginalChecklistData((prev) =>
        prev?.map((data) =>
          data?._id === response?.data?.data?._id ? response?.data?.data : data
        )
      );
      setIsLoading({ ...isLoading, createItem: false, updateListItem: false });
      setChecklistKey(null);
      setOpenModal(false);
      if (fields && fields?.length > 0) {
        setFields([]);
      }
      checkListFormInModal.resetFields();
    } catch (error) {
      console.log(`Error ${error}`);
      setIsLoading({ ...isLoading, createItem: false, updateListItem: false });
    }
  };

  //Retrieve checklists
  const getChecklists = useCallback(async () => {
    try {
      setIsLoading({ ...isLoading, getAllItems: true });
      const response = await MainService.getChecklists(
        from === "activities" ? 0 : contactInfo?.family_id,
        contactInfo?.id,
        todoListPage
      );
      const newData = response?.data?.data || [];
      const updatedChecklistData =
        todoListPage > 1 ? [...checklistData, ...newData] : newData;
      setChecklistData(updatedChecklistData);
      setOriginalChecklistData(updatedChecklistData);
      setTodoListRecords((prev) => ({
        ...prev,
        total: countUncheckedItems(response?.data?.data)?.totalFieldTodoItems,
        done: countUncheckedItems(response?.data?.data)?.uncheckedCount,
      }));
      setTodoListLastPage(response?.data?.meta?.last_page);
      setIsLoading({ ...isLoading, getAllItems: false });
    } catch (error) {
      console.log(`Error ${error}`);
      setIsLoading({ ...isLoading, getAllItems: false });
    }
  }, [contactInfo?.id, todoListPage]);

  useEffect(() => {
    getChecklists();
  }, [getChecklists]);

  //Remove checklist item
  const removeChecklistItem = async (listID, elementID) => {
    try {
      setIsLoading({ ...isLoading, deleteItem: true });
      await MainService.deleteChecklistItem(listID, elementID);
      setChecklistData(
        checklistData.map((data) =>
          data?._id === listID
            ? {
                ...data,
                field_todo: data?.field_todo.filter(
                  (element) => element?.id !== elementID
                ),
              }
            : data
        )
      );
      setOriginalChecklistData((prev) =>
        prev?.map((data) =>
          data?._id === listID
            ? {
                ...data,
                field_todo: data?.field_todo.filter(
                  (element) => element?.id !== elementID
                ),
              }
            : data
        )
      );
      setIsLoading({ ...isLoading, deleteItem: false });
    } catch (error) {
      console.log(`Error ${error}`);
      setIsLoading({ ...isLoading, deleteItem: false });
    }
  };

  //Remove a checklist
  const removeChecklist = async (listID) => {
    try {
      setIsLoading({ ...isLoading, deleteList: true });
      let params = from === "activities" ? "0/" + 1 : "null/" + 1;
      await MainService.deleteChecklist(listID, params);
      setChecklistData(checklistData.filter((data) => data?._id !== listID));
      setOriginalChecklistData((prev) =>
        prev?.filter((data) => data?._id !== listID)
      );
      setKpi((prev) =>
        prev.map((el) =>
          el.title === "Todolist" ? { ...el, value: el.value - 1 } : el
        )
      );
      setCountChanges((prev) => prev + 1);
      setIsLoading({ ...isLoading, deleteList: false });
    } catch (error) {
      console.log(`Error ${error}`);
      setIsLoading({ ...isLoading, deleteList: false });
    }
  };

  //Trigger create/update on finish form.
  const onFinish = (values) => {
    let createPayload = {
      label_todo: values?.listLabel,
      data: values?.list
        ?.map((item) => ({ ...item, checked: item?.listCheckbox ? 1 : 0 }))
        ?.map(({ listCheckbox, ...rest }) => ({ ...rest })),
    };
    let updatePayload = {
      label_todo: values?.listLabel,
      data: values?.list
        ?.map((item) => ({
          ...item,
          checked: item?.listCheckbox ? 1 : 0,
          id: item?.id ? item?.id : 0,
          rank: item?.rank ? item?.rank : null,
        }))
        ?.map(({ listCheckbox, ...rest }) => ({ ...rest })),
    };
    if (Object.keys(itemToUpdateData)?.length > 0) {
      updateList(updatePayload);
    } else {
      createList(createPayload);
    }
  };

  //Check an item inside a checklist (mark it as completed).
  const checkItem = async (idList, idItem, payload) => {
    try {
      setIsLoading({ ...isLoading, checkItem: true });
      const response = await MainService.checkItem(idList, idItem, payload);
      setChecklistData(
        checklistData &&
          checklistData.map((data) =>
            data?._id === idList ? response?.data?.data : data
          )
      );
      setOriginalChecklistData((prev) =>
        prev?.map((data) =>
          data?._id === idList ? response?.data?.data : data
        )
      );
      setTodoListRecords((prev) => ({
        ...prev,
        done: prev?.done + (payload?.checked === 1 ? 1 : -1),
      }));
      setIsLoading({ ...isLoading, checkItem: false });
    } catch (error) {
      console.log(`Error ${error}`);
      setIsLoading({ ...isLoading, checkItem: false });
    }
  };

  //Change rank of items inside the checklist.
  const updateRank = async (idList, payload) => {
    try {
      const response = await MainService.updateChecklistRank(idList, payload);
      setIsLoading({ ...isLoading, updateRank: false });
      return response;
    } catch (error) {
      console.log(`Error ${error}`);
      setIsLoading({ ...isLoading, updateRank: false });
    }
  };

  //Count of completed items inside a checklist.
  const getCheckedItems = (itemsArray) => {
    let total =
      itemsArray && itemsArray.filter((item) => Number(item?.checked) === 1);
    return total?.length;
  };

  //Count the sum of completed and incompleted items inside all lists.
  const countUncheckedItems = (data) => {
    return data.reduce(
      (acc, item) => {
        // Count the number of field_todo items with checked === 1 for each item
        const uncheckedCount = item.field_todo.filter(
          (subtask) => subtask.checked === 1
        ).length;
        // Total number of items in field_todo
        const totalFieldTodoItems = item.field_todo.length;

        // Add the counts to the accumulator
        acc.uncheckedCount += uncheckedCount;
        acc.totalFieldTodoItems += totalFieldTodoItems;
        // Return the updated accumulator
        return acc;
      },
      { uncheckedCount: 0, totalFieldTodoItems: 0 }
    );
  };

  //Handle update checklist modal values.
  useEffect(() => {
    if (Object.keys(itemToUpdateData)?.length > 0) {
      checkListFormInModal.setFieldsValue({
        listLabel: itemToUpdateData?.label_todo,
        list: itemToUpdateData?.field_todo?.map((el) => ({
          listCheckbox: Number(el?.checked),
          content: el?.content,
          id: el?.id,
          rank: el?.rank,
        })),
      });
    }
  }, [checkListFormInModal, itemToUpdateData]);

  //Update items rank after drang ends.
  const handleDragEnd = async ({ active, over }, id) => {
    //Prevent update if drag and drop in the same spot.
    if (active.id === over.id) {
      return;
    } else {
      setIsLoading({ ...isLoading, updateRank: true });

      //Update the view
      let targetItem =
        checklistData &&
        checklistData.find((checklist) => checklist?._id === id);
      const oldIndex = targetItem?.field_todo.findIndex(
        (task) => task.id === active.id
      );
      const newIndex = targetItem?.field_todo.findIndex(
        (task) => task.id === over.id
      );
      let newData = arrayMove(targetItem?.field_todo, oldIndex, newIndex);
      let apiData = newData.map((el, i) => ({ id: el?.id, rank: i + 1 }));
      let payload = { data: apiData };

      //Trigger the update rank api
      const { data } = await updateRank(id, payload);
      if (data?.success) {
        setChecklistData(
          checklistData &&
            checklistData.map((item) =>
              item?._id === targetItem?._id
                ? { ...item, field_todo: newData }
                : item
            )
        );

        setOriginalChecklistData((prev) =>
          prev?.map((item) =>
            item?._id === targetItem?._id
              ? { ...item, field_todo: newData }
              : item
          )
        );
      }
      setIsLoading({ ...isLoading, updateRank: false });
    }
  };

  let stringToAdd = "";

  //Handle change of the switch button (filter done/undone).
  const handleSwitchChange = (checked, itemId) => {
    // Filter data based on the switch toggle
    const itemOriginal = originalChecklistData?.find(
      (item) => item?._id === itemId
    );
    const updatedData = checklistData?.map((item) => {
      if (item._id === itemId) {
        return {
          ...item,
          field_todo: checked
            ? item.field_todo?.filter((todo) => todo.checked === 1) // Show only checked items
            : itemOriginal?.field_todo, // Show all items when unchecked
        };
      }
      return item;
    });

    setChecklistData(updatedData);
  };

  //Check if a completed item exists in a list.
  const checkedItemExists = (array) => {
    return array?.some((el) => Number(el?.checked) === 1);
  };

  /**
   * Functional component that represents a list item with sortable functionality.
   * @param {object} element - The element object representing the list item.
   * @param {object} item - The item object.
   * @param {object} props - Additional props passed to the component.
   * @returns JSX element representing a list item with various interactive features.
   */
  const ListItem = ({
    element,
    item,
    checkItem,
    setChecklistItemKey,
    removeChecklistItem,
    checklistItemKey,
    isLoading,
    ...props
  }) => {
    // Destructure the sortable hooks for drag-and-drop functionality
    const {
      attributes,
      listeners,
      setNodeRef,
      transform,
      transition,
      setActivatorNodeRef,
      isDragging,
    } = useSortable({ id: element?.id });
    const [t] = useTranslation("common");

    // Compute the style for the list item, adjusting for dragging state
    const style = {
      ...props.style,
      transform: CSS?.Transform.toString(
        transform && {
          ...transform,
          scaleY: 1,
        }
      ),
      transition,
      ...(isDragging
        ? {
            position: "relative",
            zIndex: 9999,
          }
        : {}),
    };

    // String that will be used when editing the content of the item
    let newString = "";

    return (
      <div ref={setNodeRef} style={style} {...attributes}>
        <Space key={element?.id} style={{ display: "flex" }} align="baseline">
          {/* Draggable handle */}
          <HolderOutlined
            ref={setActivatorNodeRef}
            style={{
              touchAction: "none",
              cursor: "move",
            }}
            {...listeners}
          />
          {/* Hidden form items for id and checked state */}
          <Form.Item name={[element?.id, "id"]} initialValue={element?.id} />
          <Form.Item name={[element?.id, "checked"]} valuePropName="checked">
            <Checkbox
              defaultChecked={Number(element?.checked) === 1 ? true : false}
              id={Number(element?.id)}
              onChange={async (e) => {
                let payload = {
                  checked: e?.target?.checked ? 1 : 0,
                  content: element?.content,
                };
                checkItem(item?._id, element?.id, payload);
              }}
            />
          </Form.Item>
          {/* Editable content */}
          <Form.Item
            name={[element?.id, "content"]}
            initialValue={element?.content}
            rules={[{ required: true, message: "This field is required" }]}
            style={{ marginLeft: "10px", alignSelf: "center" }}
          >
            <Typography.Text
              editable={{
                text: element?.content,
                triggerType: "text",
                onChange: (key) => {
                  newString = key ?? element?.content;
                  setChecklistItemKey(element?.id);
                },
                onEnd: () => {
                  checkItem(item?._id, element?.id, {
                    content: newString.trim(),
                    checked: Number(element?.checked),
                  });
                },
              }}
              style={{
                fontWeight: Number(element?.checked) === 1 && "800",
              }}
            >
              {element?.content}
            </Typography.Text>
          </Form.Item>

          {/* Delete item button */}
          <div
            onClick={(e) => {
              e && e?.preventDefault();
              handleStopPropagation(e);
            }}
          >
            {isLoading?.updateListItem && checklistItemKey === element?.id ? (
              <LoadingOutlined />
            ) : (
              <Popconfirm
                title={t("fields_management.optsTableConfirmPopup")}
                okText={t("fields_management.popupConfirmYesBtn")}
                cancelText={t("fields_management.popupConfirmNoBtn")}
                onConfirm={() => removeChecklistItem(item?._id, element?.id)}
                okButtonProps={{ loading: isLoading?.deleteItem }}
              >
                <Button
                  icon={<DeleteOutlined />}
                  danger
                  type="text"
                  shape="circle"
                />
              </Popconfirm>
            )}
          </div>
        </Space>
      </div>
    );
  };

  /**
   * Get items for checklist based on checklist data and style.
   * @param {Object} style - The style to apply to the items.
   * @returns An array of objects representing checklist items with key, label, style, children, and extra properties.
   */
  const getItems = (style) => {
    // Ensure checklistData is available before proceeding
    if (!checklistData) return [];
    return checklistData?.map((item, i) => {
      const { _id, label_todo, field_todo } = item;
      const completedItems = field_todo ? getCheckedItems(field_todo) : 0;
      const itemsArrayLength =
        originalChecklistData?.find((el) => el?._id === _id)?.field_todo
          ?.length ?? 0;
      return {
        key: _id,
        label: (
          <div className="flex flex-col">
            <Form.Item name="label" initialValue={label_todo}>
              {label_todo}
            </Form.Item>
            {field_todo?.length > 0 ? (
              <div className="flex w-full flex-row items-center">
                <Typography.Text type="secondary">
                  {t("todolist.completed", {
                    done: completedItems,
                    total: itemsArrayLength,
                  })}
                </Typography.Text>
                {checkedItemExists(field_todo) && from === "activities" ? (
                  <div onClick={handleStopPropagation} className="ml-2">
                    <Switch
                      size="small"
                      checkedChildren={t("tasks.done")}
                      unCheckedChildren={t("helpDesk.all")}
                      onChange={(checked, event) => {
                        handleSwitchChange(checked, _id);
                      }}
                    />
                  </div>
                ) : null}
              </div>
            ) : (
              <Typography.Text type="secondary">
                {t("todolist.noListItems")}
              </Typography.Text>
            )}
          </div>
        ),
        style,
        children: (
          <DndContext onDragEnd={(event) => handleDragEnd(event, _id)}>
            <Form
              form={checkListForm}
              id="todolist-form"
              validateTrigger="onChange"
              name={`form-${_id}`}
              className={`form-${_id}`}
            >
              <Form.Item
                name="todoId"
                initialValue={_id}
                style={{ display: "none" }}
              />
              <SortableContext
                strategy={verticalListSortingStrategy}
                items={field_todo?.map((el) => el?.id)?.flat() || []}
              >
                {field_todo?.map((element, i) => (
                  <ListItem
                    element={element}
                    item={item}
                    key={i}
                    checkItem={checkItem}
                    setChecklistItemKey={setChecklistItemKey}
                    removeChecklistItem={removeChecklistItem}
                    checklistItemKey={checklistItemKey}
                    isLoading={isLoading}
                  />
                ))}
              </SortableContext>

              {/* Render additional fields if current item is the checklistKey */}
              {_id === checklistKey &&
                fields?.map(({ name, key }) => (
                  <Space
                    key={key}
                    style={{ display: "flex", marginBottom: 8 }}
                    align="baseline"
                  >
                    <Form.Item
                      name={[key, "content"]}
                      rules={[
                        { required: true, message: "This field is required" },
                      ]}
                    >
                      <Typography.Text
                        editable={{
                          editing: _id === checklistKey,
                          triggerType: "text",
                          onChange: (key) => {
                            stringToAdd = key;
                          },
                          onEnd: () => {
                            if (stringToAdd.trim() !== "") {
                              updateList(
                                {
                                  data: [
                                    {
                                      id: 0,
                                      content: stringToAdd.trim(),
                                      checked: 0,
                                    },
                                  ],
                                },
                                _id
                              );
                            }
                          },
                          icon:
                            isLoading?.updateListItem &&
                            _id === checklistKey ? (
                              <LoadingOutlined />
                            ) : null,
                        }}
                      ></Typography.Text>
                    </Form.Item>
                    <MinusCircleOutlined
                      onClick={() => {
                        setFields(fields.filter((field) => field?.key !== key));
                        setChecklistKey(null);
                        checkListForm.resetFields();
                      }}
                    />
                  </Space>
                ))}
              <Space style={{ marginLeft: "15px" }}>
                <Form.Item>
                  <Button
                    type="link"
                    onClick={() => {
                      setFields([...fields, { key: fields.length, name: "" }]);
                      setChecklistKey(_id);
                    }}
                    disabled={checklistKey !== null}
                    block
                    icon={<PlusOutlined />}
                  >
                    {t("todolist.newListItem")}
                  </Button>
                </Form.Item>
              </Space>
            </Form>
          </DndContext>
        ),
        extra: (
          <div
            onClick={handleStopPropagation}
            className="flex w-full flex-row items-center"
          >
            <Button
              icon={<EditOutlined />}
              type="text"
              shape="circle"
              onClick={() => {
                setOpenModal(true);
                setItemToUpdataData(originalChecklistData[i]);
              }}
            />
            <Popconfirm
              title={t("fields_management.optsTableConfirmPopup")}
              okText={t("fields_management.popupConfirmYesBtn")}
              cancelText={t("fields_management.popupConfirmNoBtn")}
              onConfirm={() => removeChecklist(_id)}
              autoAdjustOverflow
              placement="left"
            >
              <Button
                icon={<DeleteOutlined />}
                danger
                type="text"
                shape="circle"
              />
            </Popconfirm>
            <Tooltip
              title={`${t("contacts.createdBy")} ${item?.user?.label_data} ${t(
                "tasks.drawerHeaderTwo"
              )} ${moment(item?.created_at).format(
                `${user?.location?.date_format} ${user?.location?.time_format}`
              )}`}
            >
              <div>
                <AvatarChat
                  className={"mx-1.5 flex items-center justify-center"}
                  fontSize={"0.875rem"}
                  height={"32px"}
                  width={"32px"}
                  url={`${
                    URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                  }${item?.user?.avatar}`}
                  hasImage={EXTENSIONS_ARRAY?.includes(
                    item?.user?.avatar?.split(".")?.pop()
                  )}
                  name={getName(item?.user?.label_data, "avatar")}
                  type="user"
                />
              </div>
            </Tooltip>
          </div>
        ),
      };
    });
  };

  //Semantic DOM style. Refer to https://ant.design/components/collapse#itemtype
  const { token } = theme.useToken();
  const panelStyle = {
    marginBottom: 24,
    background: from === "activities" ? "#fff" : token.colorFillAlter,
    borderRadius: token.borderRadiusLG,
    border: "none",
  };

  // Increment page on scroll.
  const handleIncrementNextPage = () => setTodoListPage(todoListPage + 1);

  // You can manage some keys here if necessary.
  const handleStopPropagation = (event) => {
    event.stopPropagation();
  };

  return (
    <div onKeyDown={handleStopPropagation}>
      <Spin
        spinning={
          isLoading?.getAllItems ||
          isLoading?.deleteList ||
          isLoading?.updateRank
        }
      >
        <div>
          <div
            style={{ display: "flex", width: "100%", justifyContent: "end" }}
          >
            {/* Add new list button */}
            <GenericButton
              onClick={() => {
                setOpenModal(true);
              }}
              type={from === "activities" ? "link" : "primary"}
              disabled={false}
              text={t("todolist.createList")}
              style={{ marginBottom: "10px" }}
            />
          </div>
          {/* Display list of checklists */}
          {checklistData?.length > 0 ? (
            <InfiniteScroll
              id="scrollableUsersList"
              scrollThreshold="91%"
              dataLength={checklistData?.length || 0}
              hasMore={todoListLastPage > todoListPage}
              next={handleIncrementNextPage}
              style={{
                overflowY: "auto",
                // paddingBottom: "10px",
              }}
              height={
                headerHeight
                  ? from === "viewSphere" || from === "directory"
                    ? `calc(100vh -  100px)`
                    : from === "activities"
                    ? windowSize?.height - 250
                    : "100%"
                  : "100%"
              }
              scrollableTarget="scrollableDiv"
              // endMessage={
              //   <Divider style={{ marign: "8px 0" }} plain>
              //     {t("todolist.noMore")}
              //   </Divider>
              // }
            >
              <Collapse
                bordered={false}
                expandIcon={({ isActive }) => (
                  <CaretRightOutlined rotate={isActive ? 90 : 0} />
                )}
                items={getItems(panelStyle)}
              />
            </InfiniteScroll>
          ) : (
            <Empty />
          )}
        </div>
        {/* Add new list modal */}
        <Modal
          open={openModal}
          title={
            Object.keys(itemToUpdateData)?.length > 0
              ? t("todolist.updateListTitleModal", {
                  listLabel: itemToUpdateData?.label_todo,
                })
              : t("todolist.createListTitleModal")
          }
          onCancel={() => {
            setOpenModal(false);
            checkListFormInModal.resetFields();
            setItemToUpdataData({});
          }}
          footer={
            <>
              <Button
                onClick={() => {
                  setOpenModal(false);
                  checkListFormInModal.resetFields();
                  setItemToUpdataData({});
                }}
              >
                {t("localisation.cancel")}
              </Button>
              <Button
                type="primary"
                form="list-form"
                htmlType="submit"
                loading={isLoading?.createItem}
              >
                {t("wiki.Confirm")}
              </Button>
            </>
          }
          afterOpenChange={(open) => {
            if (!open) {
              checkListFormInModal.resetFields();
              setItemToUpdataData({});
              setFields([]);
            }
          }}
        >
          <Form
            id="list-form"
            name="dynamic_form_nest_item"
            form={checkListFormInModal}
            onFinish={onFinish}
            style={{
              maxWidth: 600,
            }}
            autoComplete="off"
            layout="vertical"
          >
            <Form.Item
              label={t("todolist.listLabel")}
              name="listLabel"
              rules={[
                { required: true, message: t("todolist.listLabelError") },
              ]}
            >
              <Input />
            </Form.Item>
            <Form.List name="list">
              {(fields, { add, remove }) => (
                <>
                  {fields.map(({ key, name, ...restField }) => (
                    <Space
                      key={key}
                      style={{
                        display: "flex",
                        marginBottom: 8,
                        width: "100%",
                      }}
                      align="baseline"
                    >
                      <Form.Item
                        {...restField}
                        name={[name, "listCheckbox"]}
                        valuePropName="checked"
                      >
                        <Checkbox />
                      </Form.Item>
                      <Form.Item
                        {...restField}
                        name={[name, "content"]}
                        rules={[
                          {
                            required: true,
                            message: t("todolist.listItemError"),
                          },
                        ]}
                      >
                        <Input placeholder={t("todolist.newListItem")} />
                      </Form.Item>
                      <MinusCircleOutlined onClick={() => remove(name)} />
                    </Space>
                  ))}
                  <Form.Item>
                    <Button
                      type="dashed"
                      onClick={() => add()}
                      block
                      icon={<PlusOutlined />}
                    >
                      {t("todolist.newListItem")}
                    </Button>
                  </Form.Item>
                </>
              )}
            </Form.List>
          </Form>
        </Modal>
      </Spin>
    </div>
  );
};

export default CheckList;
