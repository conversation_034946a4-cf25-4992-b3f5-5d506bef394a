import { DownOutlined, PlusOutlined, TeamOutlined } from "@ant-design/icons";
import { Button, Dropdown, Space } from "antd";
import { Refs_IDs } from "components/tour/tourConfig";
import { ADD_NEW_ELEMENT_FAMILY } from "new-redux/constants";
import { CgUserlane } from "react-icons/cg";
import { HiOutlineBuildingOffice } from "react-icons/hi2";

export const GenericButton = ({
  type,
  icon,
  onClick,
  text,
  disabled,
  style,
  shape = "default",
}) => {
  return (
    <Button
      ref={Refs_IDs.families_create_new_element}
      type={`${type}`}
      icon={!icon ? <PlusOutlined /> : icon}
      onClick={onClick}
      disabled={disabled}
      style={style}
      shape={shape}
    >
      {text}
    </Button>
  );
};

export const CreationElementDropDownButton = ({ t, dispatch }) => {
  //
  const items = [
    {
      label: t("contacts.company"),
      key: "1",
      icon: <HiOutlineBuildingOffice style={{ fontSize: 15 }} />,
    },
    {
      label: t("contacts.contact"),
      key: "2",
      icon: <TeamOutlined style={{ fontSize: 15 }} />,
    },
    {
      label: t("contacts.lead"),
      key: "9",
      icon: <CgUserlane style={{ fontSize: 15 }} />,
    },
  ];

  const menuProps = {
    items,
    onClick: ({ key }) =>
      dispatch({
        type: ADD_NEW_ELEMENT_FAMILY,
        payload: Number(key),
      }),
  };
  //
  return (
    <Dropdown placement="bottomRight" trigger={["click"]} menu={menuProps}>
      <Button
        ref={Refs_IDs.logs_directory_create_new_element}
        type="primary"
        icon={<PlusOutlined />}
      >
        <Space>
          {t("form.create")}
          <DownOutlined />
        </Space>
      </Button>
    </Dropdown>
  );
};
