import { memo, useEffect, useRef } from "react";
import { Divider, Empty, List, Modal, Input, Button, Spin } from "antd";
import { InfoCircleTwoTone } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { URL_ENV } from "index";
import { FiSearch } from "react-icons/fi";
import { loaderOptionTemplate } from "pages/layouts/webphone/call";
import InfiniteScroll from "react-infinite-scroll-component";
import FilterGlobalSearch from "./FilterGlobalSearch";
import RenderSearchItems from "./render-search-items";
import "../index.css";

const CONTAINER_HEIGHT = 384;

const SearchModel = ({
  inputRef,
  isOpen,
  setIsOpen,
  data,
  total,
  setPage,
  selectedFilter,
  setSelectedFilter,
  handleClickOnItem,
  onlineUser,
  handleJoinVisioMeeting,
  handleSearchChange,
  isLoading,
  search,
  clearState,
}) => {
  //
  const [t] = useTranslation("common");
  const scrollRef = useRef(null);

  useEffect(() => {
    if (!isOpen) return;
    let timer;
    if (inputRef.current && isOpen) {
      timer = setTimeout(() => {
        inputRef.current.focus({
          cursor: "all",
        });
      }, 150);
    }
    return () => clearTimeout(timer);
  }, [inputRef, isOpen]);
  //
  const imgBaseUrl =
    URL_ENV?.REACT_APP_BASE_URL + URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL;
  //
  //
  const handleScroll = (e) => {
    if (isLoading) return;
    const { scrollTop, clientHeight, scrollHeight } = e.currentTarget;
    if (
      Math.abs(scrollHeight - scrollTop - CONTAINER_HEIGHT) <= 1 &&
      data.length < total &&
      !isLoading
    ) {
      setPage((p) => p + 1);
    }
  };
  //
  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTo({ top: 0, behavior: "smooth" });
    }
  }, [search.length, selectedFilter]);
  //
  return (
    <Modal
      zIndex={1075}
      className="custom-search-modal"
      title={
        <div className="relative pb-0.5 pt-2">
          <Input
            allowClear
            onChange={handleSearchChange}
            value={search}
            ref={inputRef}
            placeholder={t("globalSearch.infoOfSearchFunc")}
            variant="borderless"
            prefix={
              <FiSearch className="text-slate-400" style={{ fontSize: 16 }} />
            }
          />
          <div className="relative flex w-full justify-between  py-3  pl-3 shadow-md">
            <FilterGlobalSearch
              selectedFilter={selectedFilter}
              setSelectedFilter={setSelectedFilter}
              setPage={setPage}
              t={t}
            />

            {!!data.length && (
              <div className="w-3/5">
                <Divider
                  orientation="right"
                  style={{
                    margin: "6px 0 0 0",
                  }}
                >
                  {!search.length
                    ? t("globalSearch.lastItemsFound", {
                        total: data.length,
                      })
                    : t("globalSearch.resultsFound", {
                        dataLen: data.length,
                        total: total,
                      })}
                </Divider>
              </div>
            )}
          </div>
        </div>
      }
      width={650}
      open={isOpen}
      onCancel={() => {
        setIsOpen(false);
        clearState();
      }}
      closeIcon={false}
      footer={
        !!data.length && (
          <div className="flex space-x-2 rounded-b-lg	bg-neutral-100 px-4 py-2 shadow-md">
            <InfoCircleTwoTone style={{ fontSize: 16 }} />
            <p className="font-semibold	">{t("globalSearch.infoAboutClick")}</p>
          </div>
        )
      }
    >
      <div
        ref={scrollRef}
        id="scrollableDiv-global-search"
        className="dropDown-global-search relative overflow-y-auto  py-0.5"
        style={{ height: CONTAINER_HEIGHT }}
        onScroll={handleScroll}
      >
        <InfiniteScroll
          style={{ overflow: "unset" }}
          dataLength={data.length}
          // next={() => console.log("hehehehehe")}
          hasMore={data.length < total}
          loader={loaderOptionTemplate(1)}
          scrollableTarget="scrollableDiv-global-search"
        >
          <div className="global-search-list ">
            <Spin spinning={isLoading && !!data.length}>
              <List
                // className="membersList cursor-pointer "
                className="global-search-list cursor-pointer "
                itemLayout="vertical"
                dataSource={data}
                locale={{
                  emptyText: (
                    <div className="relative h-72">
                      {isLoading ? (
                        loaderOptionTemplate(6, "")
                      ) : (
                        <div className="flex h-full  items-center justify-center p-4 text-sm font-semibold">
                          <Empty
                            image={Empty.PRESENTED_IMAGE_SIMPLE}
                            description={
                              <div className="flex flex-col items-center justify-center space-y-2">
                                <p>{t("globalSearch.noResultsFound")}</p>
                                {!!selectedFilter.length && (
                                  <div className="flex w-full justify-center">
                                    <Button
                                      onClick={() => setSelectedFilter([])}
                                    >
                                      {t("globalSearch.clearFilter")}
                                    </Button>
                                  </div>
                                )}
                              </div>
                            }
                          />
                        </div>
                      )}
                    </div>
                  ),
                }}
                renderItem={(item, index) => (
                  <RenderSearchItems
                    key={index}
                    item={item}
                    t={t}
                    imgBaseUrl={imgBaseUrl}
                    handleClickOnItem={handleClickOnItem}
                    onlineUser={onlineUser}
                    handleJoinVisioMeeting={handleJoinVisioMeeting}
                  />
                )}
              />
            </Spin>
          </div>
        </InfiniteScroll>
      </div>
    </Modal>
  );
};

export default memo(SearchModel);
