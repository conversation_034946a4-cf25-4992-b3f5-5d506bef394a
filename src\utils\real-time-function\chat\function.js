/* eslint-disable no-fallthrough */
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Mo<PERSON>, notification, Space } from "antd";
import { moment_timezone } from "App";
import {
  setAssetsToList,
  setDocumentToList,
  setImageToList,
  addLinksToList,
  addNotification,
  addThreadToList,
  resetStateOtherUser,
  searchMsgInListConversations,
  setActionInReply,
  setChatSelectedConversation,
  setChatSelectedParticipants,
  setDeleteMessageInChatMembers,
  setNewMessageChatMemebers,
  setOpenDrawer,
  setUpdateMessageInChatMembers,
  updateChatUserToList,
  setLinksUpdateOrRemoveList,
  setNumberUnreadMsg,
  setMembreChatParticipantsDate,
  substractNotification,
  AddGroupToMembersGroupChat,
} from "../../../new-redux/actions/chat.actions";
import { store } from "../../../new-redux/store";
import { updateMessages } from "../../../pages/layouts/chat/utils/rqUpdate";
import {
  getUserFromMsg,
  getName,
  getOrGenerateTabId,
  getUrlsFromMessage,
  imageExtensions,
  simpleMessageTypes,
} from "../../../pages/layouts/chat/utils/ConversationUtils";
import {
  ADD_ARCHIVE_CONVERSATION,
  LEAVE_PARTICIPANT,
  REMOVE_ARCHIVE_CONVERSATION,
  SET_USER_INFOS,
  SET_MESSAGE_READ_CHAT,
  UPDATE_CURRENT_USER_STATUS_PRESENCE,
  UPDATE_ROOM_BY_ID_CHAT,
} from "../../../new-redux/constants";
import { toastNotification } from "../../../components/ToastNotification";
import { MessageOutlined, RightCircleOutlined } from "@ant-design/icons";
import { AvatarChat } from "../../../components/Chat";
import { BiTask } from "react-icons/bi";
import { changeFavicon } from "../../../pages/layouts/layout/menu1";
import { setMessageReadChat } from "../../../new-redux/services/chat.services";
import { updateParticipantsList } from "../../../pages/layouts/chat/utils/infoRoom";
import { HiOutlineVideoCamera } from "react-icons/hi";
import { setVisoParams } from "../../../new-redux/actions/visio.actions/createVisio";
import {
  setEventMercure,
  setOnlineUser,
  setVisioEventId,
} from "../../../new-redux/actions/chat.actions/realTime";
import { setTypingUser } from "../../../new-redux/actions/chat.actions/Input";
import i18next from "i18next";
import { URL_ENV, queryClient } from "index";
import { setOpenTaskRoomDrawer } from "new-redux/actions/tasks.actions/handleTaskRoomDrawer";
import {
  setNotificationDescription,
  setTaskNotificationAction,
} from "new-redux/actions/tasks.actions/realTime";
import { setStatsChat } from "new-redux/actions/dashboard.actions";
import { EXTENSIONS_ARRAY } from "pages/tasks/helpers/calculateSum";
import { navigateUrlAccordingToModuleId } from "utils/navigateUrlAccordingToModuleId";
import { openDrawerChat } from "new-redux/actions/voip.actions/handleDrawerChat";
import { truncateString } from "pages/voip/helpers/helpersFunc";
const soundNotif = "/sounds/notif.wav";
const getSelectedConversation = async () =>
  await store.getState().ChatRealTime.selectedConversation;
const getMembersGroupsChat = async () =>
  await store.getState().chat.membersGroupsChat;
const getCurrentUserAuth = async () => await store.getState().user.user;
const getCurrentUser = async () => await store.getState().chat.currentUser;
const getMembersGroupsArchived = async () =>
  await store.getState().chat.archivedList;

const getThreadList = async () => await store.getState().chat.threadList;
const getOpnedDrawer = async () => await store.getState().chat.openDrawer;
const getParticipantsList = async () =>
  await store.getState().chat.selectedParticipants;
const getVisioParams = async () => await store.getState().visio.visioParams;

const getTotalNotification = async () =>
  await store.getState().chat.totalNotificationChat;
const getStateSeach = async () => await store.getState()?.chat?.searchMsgState;
const getModuleRoom = async () => await store.getState()?.TasksRealTime;
const getContactInfo = async () =>
  await store.getState()?.contacts?.contactHeaderInfo;
const getContactInfoFromDrawer = async () =>
  await store.getState()?.vue360?.contactInfoFromDrawer;
const isOpenView360InDrawer = async () =>
  await store.getState()?.vue360?.openView360InDrawer;

const isLoadingData = async (id, type) => {
  const discussion = await queryClient.getQueryState(["getMessages", id, type]);
  if (!discussion) return false;
  return discussion?.status === "loading" && discussion?.fetchStatus !== "idle";
};

const goToFunction = async (type, localState, navigate, openHere) => {
  const notChat = ["comment"].includes(type);

  const moduleInfo = await getModuleRoom();

  console.log("moduleInfo", moduleInfo);

  if (notChat) {
    navigate(navigateUrlAccordingToModuleId(moduleInfo?.relationType), {
      state: { roomId: moduleInfo?.relationId },
    });
  } else {
    const dispatchResetStateOtherUser = () =>
      store.dispatch(
        resetStateOtherUser({
          forced: true,
          keepDrawerOpened: false,
          item: notChat
            ? null
            : {
                _id:
                  type === "room" ? localState?.room_id : localState?.sender_id,
                type,
                participants: true,
              },
        })
      );
    if (!!openHere) {
      if (moduleInfo?.openTaskRoomDrawer) {
        store.dispatch(setOpenTaskRoomDrawer(false));
      }
      dispatchResetStateOtherUser();
      let timer = setTimeout(async () => {
        await store.dispatch(
          openDrawerChat(
            null,
            type === "room" ? localState?.room_id : localState?.sender_id,
            "popoverChat"
          )
        );
        clearTimeout(timer);
      }, 1);
    } else {
      navigate("/chat");
      let timer = setTimeout(() => {
        dispatchResetStateOtherUser();
        clearTimeout(timer);
      }, 1);
    }
  }

  if (notChat) {
    console.log("notChat", notChat);
    store.dispatch(
      setChatSelectedConversation({
        selectedConversation: {
          name: localState?.room,
          description: "",
          image: "",
          admin_id: localState?.admin_id,
          bot: null,
          id: localState?.room_id,
          last_message_date: null,
          type: "room",
          source: notChat ? "task" : "chat",

          mode: type,
          muted_status: false,
          conversationId: "",
          external: false,
        },
      })
    );
    const time = setTimeout(() => {
      store.dispatch(setOpenTaskRoomDrawer(true));
      clearTimeout(time);
    }, 100);
  }
  notification.destroy("toast-new-message");
};

let permission = "Notification" in window ? Notification.permission : null;

const playAudio = async () => {
  try {
    let audio = new Audio(soundNotif);
    await audio.play();
  } catch (error) {}
};
const showIndicator = async (notificationContent) => {
  try {
    const oldTitle =
      document.title.split(") ")?.length > 1
        ? document.title.split(") ")[1]
        : document.title;

    if (notificationContent && notificationContent.source === "chat") {
      document.title = i18next.t("common:chat.notification.titleNewMessage");

      const time = setTimeout(async () => {
        const newTitle =
          document.title.split(") ")?.length > 1
            ? document.title.split(") ")[1]
            : document.title;

        const totalNotificationChat = await getTotalNotification();
        if (totalNotificationChat?.numberConversationChat > 0)
          changeFavicon(
            process.env.PUBLIC_URL + "/images/favicon_notification.ico"
          );
        else changeFavicon(process.env.PUBLIC_URL + "/favicon.ico");
        document.title =
          (totalNotificationChat?.numberConversationChat > 0
            ? "(" + totalNotificationChat?.numberConversationChat + ") "
            : "") +
          (newTitle !== i18next.t("common:chat.notification.titleNewMessage")
            ? newTitle
            : oldTitle);
        clearTimeout(time);
      }, 5000);
    }
  } catch (error) {}
};
const showBodyNotification = (type, notificationContent) => {
  let message = "";
  switch (type) {
    case "room":
      message =
        i18next.t("common:chat.notification.publicMessage") +
        " [" +
        (notificationContent.bot &&
        notificationContent.type === "message_from_bot"
          ? getName(notificationContent.bot?.name, "name")
          : getName(notificationContent.sender.name, "name")) +
        "] " +
        i18next.t("common:chat.groupe") +
        " - [" +
        notificationContent.room +
        "]";
      break;
    case "user":
      message =
        i18next.t("common:chat.notification.privateMessage") +
        " [" +
        getName(notificationContent.sender.name, "name") +
        "] ";
      break;
    case "task":
      message = i18next.t("common:tasks.mercureReminderDesktopNotif", {
        taskLabel: notificationContent?.message?.message[0],
        time: notificationContent?.message?.message[1],
      });
      break;
    case "call":
      message =
        i18next.t("common:webphone.notifReceiveCallTitle") +
        (getName(notificationContent.sender.name, "name") ?? "") +
        " " +
        notificationContent.sender?.number;
      break;
    case "webphone":
      message = notificationContent.title;
      break;
    default:
      message = "";
      break;
  }
  return message;
};
const displayAvatarOnNotification = (avatar, name = "", isBot = false) => {
  return (
    <AvatarChat
      className="mr-1 "
      size={32}
      height={8}
      width={8}
      hasImage={EXTENSIONS_ARRAY?.includes(avatar?.split(".")?.pop())}
      url={`${
        (isBot
          ? URL_ENV?.REACT_APP_OAUTH_CHAT_API +
            process.env.REACT_APP_SUFFIX_CHAT_GROUP_STORAGE
          : URL_ENV?.REACT_APP_BASE_URL +
            URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL) + avatar
      }`}
      type={isBot ? "bot" : "user"}
      name={getName(name, "avatar") ?? avatar}
    />
  );
};

const showNotification = async ({ type, notificationContent, navigate }) => {
  if (notificationContent && Object.values(notificationContent).length > 0) {
    const localState = notificationContent;
    let notificationDesktop = null;
    const title = "SPHERE";
    const icon =
      notificationContent.default_image ||
      (notificationContent.bot &&
      notificationContent.type === "message_from_bot"
        ? URL_ENV?.REACT_APP_OAUTH_CHAT_API +
          process.env.REACT_APP_SUFFIX_CHAT_GROUP_STORAGE +
          notificationContent.bot?.logo
        : (!["room", "user"].includes(type) && notificationContent.image) ||
          notificationContent.sender?.image
        ? URL_ENV?.REACT_APP_BASE_URL +
          URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
          notificationContent.sender?.image
        : process.env.PUBLIC_URL + "/avatar/avatar.png");
    const body = showBodyNotification(type, notificationContent);
    notificationDesktop?.close();
    notification.destroy("toast-new-message");

    notificationDesktop = new Notification(title, {
      body,
      icon,
      tag: notificationContent?.id,
    });
    notificationDesktop.onclick = () => {
      notificationDesktop?.close();
      window.parent.focus();

      notificationContent = null;

      const time = setTimeout(() => {
        if (["room", "user"].includes(type)) {
          navigate("/chat");
          goToFunction(type, localState, navigate);
        } else navigate();

        clearTimeout(time);
      }, 100);
    };
    notificationContent = null;
  }
};

const requestAndShowPermission = async ({
  type,
  notificationContent,

  navigate,
}) => {
  Notification.requestPermission(async function (permission) {
    if (permission === "granted") {
      showNotification({
        type,
        notificationContent,

        navigate,
      });
    } else {
      notificationContent = null;
    }
  });
};
const manageNotificationOutput = async ({
  type,
  notificationContent,
  navigate,
}) => {
  const url = new URL(window.location.href);

  if (document.visibilityState === "hidden") {
    if (permission === "granted") {
      showNotification({
        type,
        notificationContent,

        navigate,
      });
    } else if (permission === "default" || permission === "denied") {
      requestAndShowPermission({
        type,
        notificationContent,

        navigate,
      });
    }
  } else if (
    ["room", "user"].includes(type) &&
    notificationContent &&
    document.visibilityState === "visible"
  ) {
    let selectedConversation = await getSelectedConversation();

    let localState = notificationContent;

    if (
      type === "user" &&
      !(
        localState?.sender_id === selectedConversation?.id &&
        localState?.room_id !== null &&
        !selectedConversation?.external &&
        selectedConversation?.type === "user"
      )
    ) {
      const goToLocalState = localState;

      if (url.pathname !== "/chat")
        toastNotification(
          "open",
          <div className="flex w-full flex-col pl-2   text-sm ">
            <span className=" mt-0.5 ">
              {i18next.t("common:chat.notification.privateMessage")}
              <strong> [{getName(localState?.sender?.name, "name")}]</strong>
            </span>{" "}
            {localState.source === "chat" && (
              <Space split={<Divider type="vertical" />}>
                <Button
                  block
                  size="small"
                  type="link"
                  className=" ml-auto flex w-full items-center justify-center  space-x-1 "
                  shape="circle"
                  onClick={() => goToFunction("user", goToLocalState, navigate)}
                >
                  <span>{i18next.t("common:chat.goto")}</span>
                  <RightCircleOutlined
                    style={{
                      fontSize: "1rem",
                    }}
                  />
                </Button>
                <Button
                  block
                  size="small"
                  type="link"
                  className=" ml-auto flex w-full items-center justify-center  space-x-1 "
                  shape="circle"
                  onClick={() =>
                    goToFunction("user", goToLocalState, navigate, true)
                  }
                >
                  <span>{i18next.t("common:chat.openHere")}</span>
                  <RightCircleOutlined
                    rotate={90}
                    style={{
                      fontSize: "1rem",
                    }}
                  />
                </Button>
              </Space>
            )}
          </div>,
          "topRight",
          7,
          displayAvatarOnNotification(
            localState?.sender?.image,
            localState?.sender?.name
          ),
          3,
          "toast-new-message",

          {
            padding: "20px  24px 20px 24px",
            margin: "10px 0px",
          }
        );
      notificationContent = null;
      localState = null;
    } else if (
      type === "room" &&
      !(
        localState?.room_id === selectedConversation?.id &&
        selectedConversation?.type === "room" &&
        !selectedConversation?.external &&
        localState?.receiver_id !== null
      )
    ) {
      const goToLocalState = localState;
      const moduleInfo = await getModuleRoom();
      const contactInfo = await getContactInfo();
      const openView360InDrawer = await isOpenView360InDrawer();
      const contactInfoFromDrawer = await getContactInfoFromDrawer();
      if (url.pathname !== "/chat" || localState.source === "no_chat") {
        toastNotification(
          "open",
          <div className="flex h-auto w-full  items-center  pl-2 ">
            <div
              className={`
            
            gap-x-0.5  text-sm`}
            >
              <div
                className={`${
                  localState.source === "chat"
                    ? "flex whitespace-nowrap"
                    : " block"
                }  items-center   `}
              >
                {localState.source === "chat"
                  ? i18next.t("common:chat.notification.publicMessage")
                  : i18next.t("common:chat.notification.commentTask") +
                    i18next
                      .t("common:chat.notification.sendBy")
                      .toLocaleLowerCase()}
                {localState.source === "chat" && <kbd>{"["}</kbd>}
                <span className=" max-w-[100px] truncate font-bold first-letter:capitalize">
                  {getName(
                    localState.bot && localState.type === "message_from_bot"
                      ? localState.bot?.name
                      : localState?.sender?.name,
                    "name"
                  )}
                </span>
                {localState.source === "chat" && <kbd>{"]"}</kbd>}
              </div>

              <span className="   max-w-[200px] truncate">
                {(localState.source === "chat"
                  ? i18next.t("common:chat.groupe")
                  : navigateUrlAccordingToModuleId(moduleInfo?.relationType) !==
                    "/tasks"
                  ? i18next.t("common:chat.notification.inModule", {
                      moduleName: navigateUrlAccordingToModuleId(
                        moduleInfo?.relationType
                      ).replace("/", ""),
                    })
                  : i18next.t("common:chat.notification.inTask")) + " "}{" "}
                <strong>
                  {localState.source === "chat" && <kbd>{"["}</kbd>}
                  {truncateString(localState?.room, 26)}
                  {localState.source === "chat" && <kbd>{"]"}</kbd>}
                </strong>
              </span>
              {/* {localState.source === "chat" && ( */}

              {contactInfo?.id !== moduleInfo?.relationId &&
              contactInfoFromDrawer?.id !== moduleInfo?.relationId ? (
                <Space split={<Divider type="vertical" />}>
                  <Button
                    size="small"
                    type="link"
                    className={`ml-auto flex w-full items-center space-x-1 justify-${
                      localState.source === "chat" ? "end" : "center"
                    }`}
                    shape="circle"
                    onClick={() => {
                      if (goToLocalState.source === "chat") {
                        goToFunction("room", goToLocalState, navigate);
                      } else if (goToLocalState.source === "no_chat") {
                        goToFunction("comment", goToLocalState, navigate);
                      }
                    }}
                  >
                    <span>{i18next.t("common:chat.goto")}</span>
                    <RightCircleOutlined
                      style={{
                        fontSize: "1rem",
                      }}
                    />
                  </Button>

                  {goToLocalState.source === "chat" && (
                    <Button
                      size="small"
                      type="link"
                      className={`ml-auto flex w-full items-center space-x-1 justify-${
                        localState.source === "chat" ? "end" : "center"
                      }`}
                      shape="circle"
                      onClick={() =>
                        goToFunction("room", goToLocalState, navigate, true)
                      }
                    >
                      <span>{i18next.t("common:chat.openHere")}</span>
                      <RightCircleOutlined
                        rotate={90}
                        style={{
                          fontSize: "1rem",
                        }}
                      />
                    </Button>
                  )}
                </Space>
              ) : null}
              {/* )} */}
            </div>
          </div>,

          "topRight",
          7,
          localState.source === "no_chat" ? (
            navigateUrlAccordingToModuleId(moduleInfo?.relationType) !==
            "/tasks" ? (
              <MessageOutlined className="mt-2 text-3xl text-[#4096ff]" />
            ) : (
              <BiTask className="mt-2 text-3xl text-rose-400" />
            )
          ) : (
            displayAvatarOnNotification(
              localState.bot && localState.type === "message_from_bot"
                ? localState.bot?.logo
                : localState?.sender?.image,

              localState.bot && localState.type === "message_from_bot"
                ? localState.bot?.name
                : localState?.sender?.name,

              localState.bot && localState.type === "message_from_bot"
            )
          ),
          3,
          "toast-new-message",

          {
            padding: "20px  24px 20px 24px",
            margin: "10px 0px",
          }
        );
      }
      notificationContent = null;
      localState = null;
    }
  }
};

/**
 *
 * @param { string}type
 * type : user | room| task|...
 * @param {object} notificationContent
 *notificationContent: in case no chat :
 *notificationContent= {
  sender:{
 * id:1,
 * image:"path", name: "abc",
 * ...}
 * }
 * @param {function} navigate
 * navigate: function to navigate to the type
 */
const checkShowNotification = async ({
  type = "",
  notificationContent = null,
  navigate = () => {},
}) => {
  //
  const currentUser = await getCurrentUser();

  if (currentUser?.online === "busy") {
    notificationContent = null;
    return;
  } else {
    let condition = false;
    showIndicator(notificationContent);
    if (
      !notificationContent ||
      Object.values(notificationContent)?.length === 0
    )
      return;
    if (
      ["room", "user"].includes(type) &&
      parseInt(currentUser?.config?.sound_notification) === 1
    )
      playAudio();

    if (type === "user")
      condition = [1, 2].includes(parseInt(currentUser?.config?.notification));
    else if (type === "room")
      condition =
        (parseInt(currentUser?.config?.notification) === 2 &&
          notificationContent?.tags &&
          (notificationContent?.tags.split(",").includes(currentUser?._id) ||
            notificationContent?.tags === "0")) ||
        parseInt(currentUser?.config?.notification) === 1;
    else if (!["room", "user"].includes(type)) condition = true;
    if (condition)
      manageNotificationOutput({
        type,
        notificationContent,

        navigate,
      });
    else notificationContent = null;
  }
};

const makeReadFunction = async (message, from = "", type) => {
  const currentUser = await getCurrentUser();
  updateMessages(
    null,
    type === "user" ? "make_read" : "make_read_room",
    null,
    type === "user"
      ? from === "event"
        ? message.data.receiver_id
        : message.message.sender_id
      : message?.room_id,
    type,
    type === "user"
      ? null
      : from === "event"
      ? message?.user_id
      : message?.message?.sender_id
  );
  if (from === "event") {
    const discussion_id =
      type === "room"
        ? message.user_id === currentUser?._id
          ? message?.room_id
          : null
        : message?.data?.receiver_id === currentUser?._id
        ? message?.data?.sender_id
        : message?.data?.receiver_id;
    if (
      discussion_id &&
      (currentUser?._id ===
        (type === "room" ? message?.user_id : message?.data?.receiver_id) ||
        type === "user")
    ) {
      store.dispatch(
        substractNotification({
          number: 1,
          discussion_id: message.conversation_id,
        })
      );

      store.dispatch({
        type: SET_MESSAGE_READ_CHAT,
        payload: {
          idRead: discussion_id,
          type,
        },
      });
    }
  } else if (from === "new_msg") {
    const membersGroupsChat = await getMembersGroupsChat();
    const archivedList = await getMembersGroupsArchived();
    const isArchivedDiscussion = Array.isArray(message.archive)
      ? message.archive.includes(currentUser?._id)
      : false;
    const arrayToCompare = !isArchivedDiscussion
      ? [...membersGroupsChat]
      : [...archivedList];
    store.dispatch(
      setMessageReadChat({
        item: arrayToCompare.find(
          (item) => item?._id === message?.conversation_id
        ),
        type,
        source: message.source,
        _id: type === "user" ? message.message.sender_id : message?.room?._id,
        errorText: i18next.t("common:toasts.errorFetchApi"),
      })
    );
  }
};
const newMessage = async ({ message, type, notificationContent, navigate }) => {
  const selectedConversation = await getSelectedConversation();
  const threadList = await getThreadList();
  const stateSearch = await getStateSeach();
  const currentUser = await getCurrentUser();
  const sender = getUserFromMsg(message.message.sender_id);
  const receiver = getUserFromMsg(message.message.receiver_id);
  let discussionFounded;
  const isLoading = await isLoadingData(
    type === "room"
      ? message.room._id
      : message.message.sender_id === currentUser?._id
      ? message.message.receiver_id
      : message.message.sender_id,
    type
  );

  const isArchivedDiscussion = Array.isArray(message.archive)
    ? message.archive.includes(currentUser?._id)
    : false;

  const isMutedConversation = Array.isArray(message.muted)
    ? message.muted.includes(currentUser?._id)
    : false;
  if (isLoading) {
    queryClient.cancelQueries({
      queryKey: [
        "getMessages",
        type === "room"
          ? message.room._id
          : message.message.sender_id === currentUser?._id
          ? message.message.receiver_id
          : message.message.sender_id,
        type,
      ],
      exact: true,
    });
    queryClient.refetchQueries({
      queryKey: [
        "getMessages",
        type === "room"
          ? message.room._id
          : message.message.sender_id === currentUser?._id
          ? message.message.receiver_id
          : message.message.sender_id,
        type,
      ],
      exact: true,
    });
  } else {
    const membersGroupsChat = await getMembersGroupsChat();
    const archivedList = await getMembersGroupsArchived();
    const arrayToChange = !isArchivedDiscussion
      ? [...membersGroupsChat]
      : [...archivedList];
    discussionFounded = arrayToChange.find(
      (item) => message.conversation_id === item._id
    );
    updateMessages(
      message.message,
      type === "room"
        ? "new_message_event"
        : message?.message?.type === "poll_message"
        ? "new_message_poll"
        : "new_message",
      null,
      type === "room"
        ? message.room._id
        : message.message.sender_id === currentUser?._id
        ? message.message.receiver_id
        : message.message.sender_id,
      type,
      message.message?.sender_id,
      discussionFounded ? false : true
    );
    if (message.message.type.includes("replay"))
      updateMessages(
        message.message,
        "new_message_replay",
        message.message?.parent_id,
        type === "room" ? message.room._id : message.message.sender_id,
        type,
        null,
        discussionFounded ? false : true
      );
  }
  const isSameConversation =
    message.source === "no_chat"
      ? message.room._id === selectedConversation?.id
      : message?.conversation_id === selectedConversation?.conversationId;

  // first Two condition in case new message from bot or other user
  // and the conversation is not the same conversation or is opened only for info (drawer not the main conversation)
  // and the document is not focused
  // then increment the number of unread message
  const shouldIncrement =
    (message.message.type === "message_from_bot" ||
      message.message.sender_id !== currentUser?._id) &&
    ((isSameConversation && selectedConversation?.external) ||
      !isSameConversation ||
      !document.hasFocus());

  if (message.source !== "no_chat") {
    if (
      stateSearch?.id &&
      stateSearch?.discussion_id === message?.conversation_id
    )
      store.dispatch(
        searchMsgInListConversations({
          count_new_message: 1,
        })
      );
    isSameConversation &&
      store.dispatch(
        setNumberUnreadMsg({
          id:
            type === "room"
              ? message.room._id
              : message.message.sender_id === currentUser?._id
              ? message.message.receiver_id
              : message.message.sender_id,
          increment: true,
        })
      );

    store.dispatch(
      setNewMessageChatMemebers({
        conversation_id: message.conversation_id,
        archive: isArchivedDiscussion,
        type,
        data: {
          ...message.message,

          unread: 1,
        },
        discussion_id:
          type === "room"
            ? message.room._id
            : message.message.sender_id === currentUser?._id
            ? message.message.receiver_id
            : message.message.sender_id,
        user:
          type === "user"
            ? message.message.sender_id === currentUser?._id
              ? receiver
              : sender
            : {
                admin_id: message.room?.admin_id,
                description: message.room?.description,
                id: message?.room?._id,
                image: message.room?.image,
                name: message.room?.name,
              },
        sender,
        bot: message.message.bot ?? null,

        increment: shouldIncrement,
        discussionFounded: discussionFounded ? true : false,
      })
    );

    /** ----------------------------------> section badge notification  <---------------------------------- **/

    if (!isArchivedDiscussion && shouldIncrement)
      store.dispatch(
        addNotification({
          number: 1,
          discussion_id: message.conversation_id,
        })
      );
  } else {
    // Here add the notifs related to messages inside activities rooms.
    store.dispatch(setTaskNotificationAction("activity_room"));
    store.dispatch(
      setNotificationDescription({
        initiator: message?.room?.name,
        action: message?.room?._id,
      })
    );
  }

  notificationContent = {
    ...message.message,
    sender,
    room: message?.room?.name,
    conversationId: message.conversation_id,
    receiver: getUserFromMsg(message.message.receiver_id, message.message),
    archive: message.archive,
    source: message.source ?? "chat",
    conversation_id: message.conversation_id,
  };

  /** ----------------------------------> section notification  <---------------------------------- **/

  if (
    isSameConversation &&
    !selectedConversation?.external &&
    ((message.message.sender_id === currentUser?._id &&
      message.message.type === "message_from_bot") ||
      message.message.sender_id !== currentUser?._id) &&
    (message.source === "no_chat" ? true : document.hasFocus())
  ) {
    makeReadFunction(message, "new_msg", selectedConversation?.type);
    notificationContent = null;
  } else {
    if (
      (message.message.type !== "message_from_bot" &&
        message.message.sender_id === currentUser?._id) ||
      isMutedConversation ||
      isArchivedDiscussion
    ) {
      notificationContent = null;
    }
    if (notificationContent && Object.values(notificationContent).length > 0) {
      checkShowNotification({
        type: notificationContent?.room_id
          ? "room"
          : notificationContent?.receiver_id
          ? "user"
          : "",
        notificationContent,
        navigate,
      });
    }
  }

  /** ----------------------------------> section replyy  <---------------------------------- **/

  const parent_id = threadList.find((item) => !item.type.includes("replay"));

  if (
    message.message.type.includes("replay") &&
    isSameConversation &&
    message.message.parent_id === parent_id?._id
  )
    store.dispatch(addThreadToList(message.message));

  /** ----------------------------------> open drawer section <---------------------------------- **/
  const openDrawer = await getOpnedDrawer();
  if (isSameConversation && openDrawer.type === "info") {
    if (message.message.file && message.message.file?.length > 0) {
      message.message.file.forEach((element) => {
        if (imageExtensions.includes(element.type.split("/")[1])) {
          store.dispatch(setAssetsToList({ images: 1, documents: 0 }));
          store.dispatch(setImageToList({ element, type: "add" }));
        } else {
          store.dispatch(setDocumentToList({ element, type: "add" }));
          store.dispatch(setAssetsToList({ images: 0, documents: 1 }));
        }
      });
    }

    const urlsMatch = getUrlsFromMessage(message.message.message);
    if (urlsMatch.length > 0) {
      store.dispatch(
        addLinksToList(
          urlsMatch.map((item) => ({
            url: item,
            message_id: message.message_id,
            user: sender?.name,
            created_at: moment_timezone(new Date()).format(),
          }))
        )
      );
      store.dispatch(
        setAssetsToList({
          links: urlsMatch.length,
        })
      );
    }
  }
  /**-----------------------------------> On cancel visio <------------------------------------------- */
  if (message?.message?.type === "message_visio_conf") {
    store.dispatch(setVisioEventId(message?.message?._id));
  }
  //update stats chat in dashboard
  store.dispatch(setStatsChat(message));
};
const forwardMessageDetails = async (message) => {
  const selectedConversation = await getSelectedConversation();

  updateMessages(
    message.forward_data,
    "forward_message",
    message.forward_message_id,
    selectedConversation?.id,
    selectedConversation?.type,
    null
  );
};
const reactMessage = async (message) => {
  const currentUser = await getCurrentUser();
  const isLoading = await isLoadingData(
    message.type_event?.includes("room")
      ? message.room_id
      : currentUser?._id === message.user_id
      ? message.receiver_id
      : message.user_id,
    message.type_event?.includes("room") ? "room" : "user"
  );
  let typeEvent =
    message.type_reaction === "add"
      ? "add_react"
      : message.type_reaction === "remove"
      ? "remove_react"
      : "";
  if (isLoading) {
    queryClient.cancelQueries({
      queryKey: [
        "getMessages",
        message.type_event?.includes("room")
          ? message.room_id
          : currentUser?._id === message.user_id
          ? message.receiver_id
          : message.user_id,
        message.type_event?.includes("room") ? "room" : "user",
      ],
      exact: true,
    });
    queryClient.refetchQueries({
      queryKey: [
        "getMessages",
        message.type_event?.includes("room")
          ? message.room_id
          : currentUser?._id === message.user_id
          ? message.receiver_id
          : message.user_id,
        message.type_event?.includes("room") ? "room" : "user",
      ],
      exact: true,
    });
  } else {
    updateMessages(
      parseInt(message.reaction.reaction),
      typeEvent,
      message.reaction.message_id,
      message.type_event?.includes("room")
        ? message.room_id
        : currentUser?._id === message.user_id
        ? message.receiver_id
        : message.user_id,
      message.type_event?.includes("room") ? "room" : "user",
      message.user_id
    );
    if (
      message.type_reaction === "add"
        ? message.message.parent_id
        : message.parent_id
    )
      updateMessages(
        {
          reaction: parseInt(message.reaction.reaction),
          message_replies_id: message.reaction.message_id,
        },
        message.type_reaction === "add"
          ? "add_react_replies"
          : "remove_react_replies",
        message.type_reaction === "add"
          ? message.message.parent_id
          : message.parent_id,
        message.type_event?.includes("room")
          ? message.room_id
          : currentUser?._id === message.user_id
          ? message.receiver_id
          : message.user_id,
        message.type_event?.includes("room") ? "room" : "user",
        message.user_id
      );
  }
  if (message.source !== "no_chat") {
    const sender = getUserFromMsg(message?.sender_id);
    store.dispatch(
      setUpdateMessageInChatMembers({
        data: message.message?.deleted_at
          ? null
          : {
              _id:
                typeEvent === "remove_react"
                  ? message.message_id
                  : message.message._id,
              type: message.message?.type,
              message: message.message?.message,
            },

        last_message_date:
          typeEvent === "remove_react"
            ? message.message.created_at
            : moment_timezone(new Date()).format(),
        reaction:
          typeEvent === "remove_react"
            ? "delete_react"
            : parseInt(message.reaction.reaction),
        type: message.type_event?.includes("room") ? "room" : "user",
        discussion_id: message.type_event?.includes("room")
          ? message.room_id
          : currentUser?._id === message.user_id
          ? message.receiver_id
          : message.user_id,
        sender: typeEvent === "add_react" ? message.message.user : sender,
      })
    );
  }
  const openDrawer = await getOpnedDrawer();

  if (openDrawer.type === "thread")
    store.dispatch(
      setActionInReply({
        newMessage: parseInt(message.reaction.reaction),
        type: typeEvent,
        message_id: message.reaction.message_id,
        sender_id: message.user_id,
      })
    );
};
const deleteMessage = async (message) => {
  const currentUser = await getCurrentUser();
  const isLoading = await isLoadingData(
    message.type_event?.includes("room") ? message.room_id : message.sender_id,
    message.type_event?.includes("room") ? "room" : "user"
  );

  if (isLoading) {
    queryClient.cancelQueries({
      queryKey: [
        "getMessages",
        message.type_event?.includes("room")
          ? message.room_id
          : message.sender_id === currentUser?._id
          ? message.message.receiver_id
          : message.sender_id,
        message.type_event?.includes("room") ? "room" : "user",
      ],
      exact: true,
    });
    queryClient.refetchQueries({
      queryKey: [
        "getMessages",
        message.type_event?.includes("room")
          ? message.room_id
          : message.sender_id === currentUser?._id
          ? message.message.receiver_id
          : message.sender_id,
        message.type_event?.includes("room") ? "room" : "user",
      ],
      exact: true,
    });
  } else {
    updateMessages(
      message.poll ? message : null,
      "deleted",
      message.message_id,
      message.type_event?.includes("room")
        ? message.room_id
        : message.sender_id === currentUser?._id
        ? message.message.receiver_id
        : message.sender_id,
      message.type_event?.includes("room") ? "room" : "user",
      message.sender_id
    );
    if (message.parent_id)
      updateMessages(
        message.message_id,
        "deleted_replies",
        message.parent_id,
        message.type_event?.includes("room")
          ? message.room_id
          : message.sender_id === currentUser?._id
          ? message.message.receiver_id
          : message.sender_id,
        message.type_event?.includes("room") ? "room" : "user",
        message.sender_id
      );
  }
  if (message.source !== "no_chat")
    store.dispatch(
      setDeleteMessageInChatMembers({
        sender: getUserFromMsg(message.sender_id),

        discussion_id: message.type_event?.includes("room")
          ? message.room_id
          : message.sender_id === currentUser?._id
          ? !message.message
            ? message.receiver_id
            : message.message.receiver_id
          : message.sender_id,
        _id: message.message_id,
      })
    );
  const openDrawer = await getOpnedDrawer();

  if (openDrawer.type === "thread")
    store.dispatch(
      setActionInReply({
        newMessage: null,
        type: "deleted",
        message_id: message.message_id,
        sender_id: message.sender_id,
      })
    );
  if (openDrawer.type === "info") {
    const selectedConversation = await getSelectedConversation();
    const type = message.room_id ? "room" : "user";
    const isSameConversation =
      selectedConversation &&
      type === selectedConversation?.type &&
      selectedConversation?.id ===
        (selectedConversation?.type === "user"
          ? message.sender_id
          : message.room_id);
    if (message.message.file.length > 0 && isSameConversation) {
      message.message.file.forEach((element) => {
        if (imageExtensions.includes(element.type.split("/")[1])) {
          store.dispatch(setAssetsToList({ images: -1 }));
          store.dispatch(setImageToList({ element, type: "delete" }));
        } else {
          store.dispatch(setDocumentToList({ element, type: "delete" }));
          store.dispatch(setAssetsToList({ documents: -1 }));
        }
      });
      if (simpleMessageTypes.includes(message.message.type)) {
        store.dispatch(
          setLinksUpdateOrRemoveList({
            type: "delete",
            message_id: message.message_id,
          })
        );
      }
    }
  }
};
const editMessage = async (message, type) => {
  const openDrawer = await getOpnedDrawer();
  const currentUser = await getCurrentUser();
  const isLoading = await isLoadingData(
    type === "room"
      ? message.message.room_id
      : currentUser?._id === message.message.sender_id
      ? message.message.receiver_id
      : message.message.sender_id,
    type
  );
  if (message.source !== "no_chat")
    store.dispatch(
      setUpdateMessageInChatMembers({
        data: {
          _id: message.message_id,
          type: message.message.type,
          message: message.message.message,
          tags: message.message.tags,
        },
        last_message_date: message.message.updated_at,

        reaction: null,
        type,
        discussion_id: message.type_event?.includes("room")
          ? message.message.room_id
          : currentUser?._id === message.message.sender_id
          ? message.message.receiver_id
          : message.message.sender_id,
        sender: getUserFromMsg(message.message.sender_id),
      })
    );
  if (isLoading) {
    queryClient.cancelQueries({
      queryKey: [
        "getMessages",
        type === "room"
          ? message.message.room_id
          : currentUser?._id === message.message.sender_id
          ? message.message.receiver_id
          : message.message.sender_id,
        type,
      ],
      exact: true,
    });
    queryClient.refetchQueries({
      queryKey: [
        "getMessages",
        type === "room"
          ? message.message.room_id
          : currentUser?._id === message.message.sender_id
          ? message.message.receiver_id
          : message.message.sender_id,
        type,
      ],
      exact: true,
    });
  } else {
    updateMessages(
      message.message,
      "update",
      message.message_id,
      type === "room"
        ? message.message.room_id
        : currentUser?._id === message.message.sender_id
        ? message.message.receiver_id
        : message.message.sender_id,
      type,

      null
    );

    if (message.message.main_message)
      updateMessages(
        message.message,
        "update_replies",
        message.message?.main_message?._id,
        type === "room"
          ? message.message.room_id
          : currentUser?._id === message.message.sender_id
          ? message.message.receiver_id
          : message.message.sender_id,
        type,
        null
      );
  }

  if (openDrawer.type === "thread")
    store.dispatch(
      setActionInReply({
        newMessage: message.message,
        type: "update",
        message_id: message.message_id,
        sender_id: message.message.sender_id,
      })
    );
  if (openDrawer?.type === "info") {
    const selectedConversation = await getSelectedConversation();
    const type = message.message.room_id ? "room" : "user";

    const isSameConversation =
      selectedConversation &&
      !selectedConversation?.external &&
      type === selectedConversation?.type &&
      selectedConversation?.id ===
        (selectedConversation?.type === "user"
          ? message.message.sender_id
          : message.message.room_id);
    const urls = getUrlsFromMessage(message.message?.message);
    if (urls.length > 0 && isSameConversation) {
      store.dispatch(
        setLinksUpdateOrRemoveList({
          list: urls.map((item) => ({
            url: item,
            message_id: message.message_id,
            user: currentUser?.name,
            created_at: moment_timezone(new Date()).format(),
          })),
          type: "update",
          message_id: message.message_id,
        })
      );
    }
  }
};
const updateRoom = async (message) => {
  let selectedConversation = await getSelectedConversation();
  const isLoading = await isLoadingData(message.room._id, "room");
  if (
    selectedConversation?.id === message.room._id &&
    selectedConversation?.type === "room"
  )
    store.dispatch(
      setChatSelectedConversation({
        selectedConversation: {
          ...selectedConversation,
          name: message.room.name,
          description: message.room?.description,
          image: message.room?.image,
          admin_id: message.room?.admin_id,
          bot: message.room.bot,
          id: message.room?._id,
          type: "room",
        },
      })
    );
  if (isLoading) {
    queryClient.cancelQueries({
      queryKey: ["getMessages", message.room._id, "room"],
      exact: true,
    });
    queryClient.refetchQueries({
      queryKey: ["getMessages", message.room._id, "room"],
      exact: true,
    });
  } else {
    // display msg in conversation
    updateMessages(
      {
        ...message.message_system,
        unread_room: [message.room?.admin_id],
      },
      "new_message_event",
      null,
      message.room._id,
      "room",
      message.room?.admin_id,
      message.conversation_id
    );
  }
  updateParticipantsList(message.room.participants, message.room._id, "room");
  store.dispatch(
    setChatSelectedParticipants({
      selectedParticipants: message.room.participants,
    })
  );
  store.dispatch({
    type: UPDATE_ROOM_BY_ID_CHAT,
    payload: message.room,
  });
};
const updateParticipants = async (message, type) => {
  const currentUser = await getCurrentUser();

  if (
    message.room?.admin_id === currentUser?._id &&
    getOrGenerateTabId() === message.tab_id
  ) {
    return;
  }

  let selectedConversation = await getSelectedConversation();
  const isLoading = await isLoadingData(message.room_id, "room");
  const isSameConversation =
    selectedConversation?.id === message.room_id &&
    selectedConversation?.type === "room";
  switch (type) {
    case "add":
      if (message.room?.predefined === 1) {
        if (
          message.message_system.message
            ?.toString()
            ?.split(",")
            ?.includes(currentUser?._id)
        )
          store.dispatch(
            AddGroupToMembersGroupChat({
              ...message.room,
              conversation_id: message.conversation_id,
            })
          );
        else
          store.dispatch(
            setMembreChatParticipantsDate({
              id: message?.room_id,
              conversationId: message?.conversation_id,
            })
          );
      }

      if (
        selectedConversation?.id === message.room?._id &&
        selectedConversation?.type === "room"
      )
        store.dispatch(
          setChatSelectedParticipants({
            selectedParticipants: message?.room?.participants,
          })
        );
      updateParticipantsList(
        message?.room?.participants,
        message?.room_id,
        "room"
      );
      //participants
      break;

    case "remove":
      if (isSameConversation) {
        if (message.message_system.message === currentUser?._id) {
          store.dispatch(setOpenDrawer({ type: "" }));
          store.dispatch(
            setChatSelectedConversation({ selectedConversation: null })
          );
          store.dispatch(
            setChatSelectedParticipants({
              selectedParticipants: [],
            })
          );

          queryClient.removeQueries({
            queryKey: ["getMessages", message.room_id, "room"],
            throwOnError: true,
            cancelRefetch: true,
          });
          queryClient.removeQueries({
            queryKey: ["INFO_CANAL", message.room_id, "room"],
            throwOnError: true,
            cancelRefetch: true,
          });
          Modal.destroyAll();
          Modal.warning({
            title: i18next.t(
              "common:chat.message_system.removed_auth_user_modal"
            ),
          });
        } else {
          store.dispatch(
            setChatSelectedParticipants({
              selectedParticipants: message.room.participants.filter(
                (p) => p._id !== message.message_system.message
              ),
            })
          );
        }
      }
      if (
        message.message_system.message === currentUser?._id &&
        message.room?.predefined === 1
      ) {
        // notification section includes bellow
        store.dispatch({
          type: LEAVE_PARTICIPANT,
          payload: { room_id: message.room_id },
        });
      }
      updateParticipantsList(
        message.room.participants.filter(
          (p) => p._id !== message.message_system.message
        ),
        message.room_id,
        "room"
      );

      break;

    case "leave_user_room":
      if (message.user_id === currentUser?._id) {
        // notification section includes bellow

        store.dispatch({
          type: LEAVE_PARTICIPANT,
          payload: { room_id: message.room_id },
        });
        queryClient.removeQueries({
          queryKey: ["getMessages", message.room_id, "room"],
          throwOnError: true,
          cancelRefetch: true,
        });
        queryClient.removeQueries({
          queryKey: ["INFO_CANAL", message.room_id, "room"],
          throwOnError: true,
          cancelRefetch: true,
        });
        store.dispatch(
          substractNotification({
            number: 1,
            discussion_id: message.conversation_id,
          })
        );
        return;
      } else {
        if (isSameConversation) {
          if (message.user_id === currentUser?._id) {
            store.dispatch(
              setChatSelectedConversation({ selectedConversation: null })
            );
          }

          store.dispatch(
            setChatSelectedParticipants({
              selectedParticipants: message.room.participants.filter(
                (p) => p._id !== message.user_id
              ),
            })
          );
        }
        updateParticipantsList(
          message.room.participants.filter((p) => p._id !== message.user_id),
          message.room?._id,
          "room"
        );

        if (
          message.room.admin_id === currentUser?._id &&
          message.room?.predefined === 1
        )
          store.dispatch({
            type: UPDATE_ROOM_BY_ID_CHAT,
            payload: message.room,
          });
      }

      break;
    default:
      break;
  }
  if (isLoading) {
    queryClient.cancelQueries({
      queryKey: ["getMessages", message.room_id, "room"],
      exact: true,
    });
    queryClient.refetchQueries({
      queryKey: ["getMessages", message.room_id, "room"],
      exact: true,
    });
  } else
    updateMessages(
      {
        ...message.message_system,
        unread_room: [message.room?.admin_id],
      },
      "new_message_event",
      null,
      message.room_id,
      "room",
      message.room?.admin_id,
      message.conversation_id
    );
  store.dispatch(
    setMembreChatParticipantsDate({
      id: message?.room_id,
      conversationId: message?.conversation_id,
    })
  );
};

const updateUserList = async (newUser) => {
  const selectedParticipants = await getParticipantsList();
  const selectedConversation = await getSelectedConversation();
  const currentUser = await getCurrentUser();
  const user = await getCurrentUserAuth();
  if (selectedParticipants?.find((p) => p._id === newUser._id))
    queryClient.invalidateQueries({
      queryKey: [
        "INFO_CANAL",
        selectedConversation?.id,
        selectedConversation?.type,
      ],
    });
  if (selectedConversation?.id === newUser._id)
    store.dispatch(
      setChatSelectedConversation({
        selectedConversation: {
          ...selectedConversation,
          name: newUser?.name,
          post_number: newUser?.post_number,
          image: newUser?.image,
        },
      })
    );
  if (newUser._id === currentUser?._id)
    store.dispatch({
      type: SET_USER_INFOS,
      payload: {
        ...user,
        email: newUser.email,
        label: newUser.name,
        avatar: newUser.image,
      },
    });

  store.dispatch(updateChatUserToList(newUser));
};
const archiveRoomFunction = async (message) => {
  let selectedConversation = await getSelectedConversation();
  const currentUser = await getCurrentUser();

  if (
    message.room_id === selectedConversation?.id &&
    selectedConversation?.type === "room" &&
    selectedConversation?.admin_id !== currentUser?._id
  ) {
    Modal.destroyAll();
    Modal.warning({
      title: i18next.t("common:chat.message_system.groupe_deleted"),
    });
    store.dispatch(setOpenDrawer({ type: "" }));
    queryClient.removeQueries({
      queryKey: ["getMessages", message.room_id, "room"],
      throwOnError: true,
      cancelRefetch: true,
    });
    queryClient.removeQueries({
      queryKey: ["INFO_CANAL", message.room_id, "room"],
      throwOnError: true,
      cancelRefetch: true,
    });
    store.dispatch(
      setChatSelectedConversation({
        selectedConversation: null,
      })
    );
  }
  store.dispatch({
    type: LEAVE_PARTICIPANT,
    payload: { room_id: message.room_id, all: true },
  });
};
const makeMessageImportant = async (message) => {
  const currentUser = await getCurrentUser();

  const isLoading = await isLoadingData(
    message.room_id ??
      (message.user_id === currentUser?._id
        ? message.receiver_id
        : message.user_id),
    message.room_id ? "room" : "user"
  );
  if (isLoading) {
    queryClient.cancelQueries({
      queryKey: [
        "getMessages",
        message.room_id ??
          (message.sender_id === currentUser?._id
            ? message.receiver_id
            : message.sender_id),
        message.room_id ? "room" : "user",
      ],
      exact: true,
    });
    queryClient.refetchQueries({
      queryKey: [
        "getMessages",
        message.room_id ??
          (message.sender_id === currentUser?._id
            ? message.receiver_id
            : message.sender_id),
        message.room_id ? "room" : "user",
      ],
      exact: true,
    });
  } else {
    updateMessages(
      null,
      message.type_event?.includes("add") ? "add_saved_E" : "remove_saved_E",
      message.message_id,
      message.room_id
        ? message.room_id
        : message.sender_id === currentUser?._id
        ? message.receiver_id
        : message.sender_id,
      message.room_id ? "room" : "user",

      message.user_id
    );
  }
};
const makeMessageFavorite = async (message) => {
  const currentUser = await getCurrentUser();
  const isLoading = await isLoadingData(
    message.room_id ??
      (message.sender_id === currentUser?._id
        ? message.receiver_id
        : message.sender_id),
    message.room_id ? "room" : "user"
  );
  if (isLoading) {
    queryClient.cancelQueries({
      queryKey: [
        "getMessages",
        message.room_id ??
          (message.sender_id === currentUser?._id
            ? message.receiver_id
            : message.sender_id),
        message.room_id ? "room" : "user",
      ],
      exact: true,
    });
    queryClient.refetchQueries({
      queryKey: [
        "getMessages",
        message.room_id ??
          (message.sender_id === currentUser?._id
            ? message.receiver_id
            : message.sender_id),
        message.room_id ? "room" : "user",
      ],
      exact: true,
    });
  } else
    updateMessages(
      null,
      message.type_event?.includes("add") ? "add_favorite" : "remove_favorite",
      message.message_id,
      message.room_id ??
        (message.sender_id === currentUser?._id
          ? message.receiver_id
          : message.sender_id),
      message.room_id ? "room" : "user",
      currentUser?._id
    );
};
const archiveDiscussion = async (message) => {
  const type =
    message.conversation?.contact &&
    Object.values(message.conversation?.contact)?.length > 0
      ? "user"
      : "room";
  store.dispatch({
    type: ADD_ARCHIVE_CONVERSATION,
    payload: {
      data: message.conversation,
      item:
        type === "user"
          ? message.conversation?.contact
          : message.conversation?.room,
    },
  });
  if (message.conversation.total_unread > 0)
    store.dispatch(
      substractNotification({
        number: message.conversation.total_unread,
        discussion_id: message.conversation?._id,
      })
    );
};

const unArchiveDiscussion = async (message) => {
  const type =
    message.conversation?.contact &&
    Object.values(message.conversation?.contact)?.length > 0
      ? "user"
      : "room";

  store.dispatch({
    type: REMOVE_ARCHIVE_CONVERSATION,
    payload: {
      data: message.conversation,
      item:
        type === "user"
          ? message.conversation?.contact
          : message.conversation?.room,
    },
  });

  if (message.conversation.total_unread > 0)
    store.dispatch(
      addNotification({
        number: message.conversation.total_unread,
        discussion_id: message.conversation_id,
      })
    );
};

const getVisio = async (message) => {
  const visio = await getVisioParams();
  if (visio.name === message.room) {
    toastNotification(
      "success",
      i18next.t("common:visio.moderatorJoin"),
      "topRight",
      7,
      <HiOutlineVideoCamera
        className="text-green-500"
        style={{ fontSize: "21px" }}
      />
    );
    store.dispatch(setVisoParams({ moderator: true }));
  }
};
const closeEventMerucre = async () => {
  try {
    const eventMercure = await store?.getState()?.ChatRealTime?.eventMercure;

    eventMercure?.close();
  } catch {
    return;
  } finally {
    Promise.all([
      store.dispatch(setEventMercure(null)),
      store.dispatch({
        type: UPDATE_CURRENT_USER_STATUS_PRESENCE,
        payload: "offline",
      }),
      store.dispatch(setTypingUser([])),
      store.dispatch(setOnlineUser({})),
    ]);
  }
};
export {
  makeReadFunction,
  newMessage,
  reactMessage,
  deleteMessage,
  editMessage,
  updateRoom,
  updateParticipants,
  updateUserList,
  archiveRoomFunction,
  makeMessageImportant,
  makeMessageFavorite,
  archiveDiscussion,
  unArchiveDiscussion,
  getVisio,
  closeEventMerucre,
  forwardMessageDetails,
  checkShowNotification,
};
