import { Image } from "antd";
import React, { useEffect, useState } from "react";
import { checkContentType } from "../../../pages/layouts/chat/utils/ConversationUtils";
import Audio from "../audio/Audio";
import File from "./Media/File";
import ImageRender from "./Media/Image";
import { URL_ENV } from "index";

function RenderFile({ message, source, GoToComponent }) {
  const [filesList, setFilesList] = useState([]);
  const [imagesList, setImagesList] = useState([]);
  const [videoList, setVideoList] = useState([]);
  const [audioList, setAudioList] = useState([]);

  useEffect(() => {
    let response = checkContentType(message.file);
    if (response === "mixed") {
      const images = [];
      const videos = [];
      const audios = [];
      const files = [];
      message.file.forEach((item) => {
        if (item.type.startsWith("image")) {
          images.push(item);
        } else if (item.type.startsWith("video")) {
          videos.push(item);
        } else if (item.type.startsWith("audio")) {
          audios.push(item);
        } else {
          files.push(item);
        }
      });
      setImagesList(images);
      setVideoList(videos);
      setAudioList(audios);
      setFilesList(files);
    } else if (response === "image") {
      setImagesList(message.file);
    } else if (response === "video") {
      setVideoList(message.file);
    } else if (response === "audio") {
      setAudioList(message.file);
    } else setFilesList(message.file);
  }, [message]);

  return (
    <div className="message">
      {message.file_id && (
        <div className="m-1 flex flex-col">
          {videoList &&
            videoList.length > 0 &&
            videoList.map((file, index) => (
              <video
                id={"videoID-" + index}
                key={file._id}
                src={`${
                  URL_ENV?.REACT_APP_OAUTH_CHAT_API +
                  process.env.REACT_APP_SUFFIX_CHAT_GROUP_STORAGE
                }${file.path}`}
                controls
                preload="metadata"
                height={320}
                className={`mr-2 w-full max-w-lg  cursor-pointer`}>
                Your browser does not support the video tag.
              </video>
            ))}
          {imagesList && imagesList.length > 0 && (
            <div
              className={`inline-block items-center ${
                GoToComponent ? "my-2" : ""
              } space-x-2`}>
              <Image.PreviewGroup>
                {imagesList &&
                  imagesList.map((f, i) => (
                    <ImageRender
                      array={imagesList}
                      key={`Image_${i}`}
                      file={f}
                      index={i}
                    />
                  ))}
              </Image.PreviewGroup>
            </div>
          )}
          {audioList &&
            audioList.length > 0 &&
            audioList.map((f, i) => (
              <Audio source={source} key={`audio-${i}`} url={f.path} file={f} />
            ))}
          {filesList &&
            filesList.length > 0 &&
            filesList.map((f, i) => (
              <div key={`file-${i}`} className="mt-1">
                <File file={f} index={i} GoToComponent={GoToComponent} />
              </div>
            ))}
        </div>
      )}
    </div>
  );
}

export default RenderFile;
