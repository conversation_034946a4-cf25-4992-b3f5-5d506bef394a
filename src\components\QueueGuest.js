import React, { useEffect, useRef } from "react";
import { Badge, Form, Input, Select, Space } from "antd";
import { useState } from "react";
import { generateAxios } from "../services/axiosInstance";
import { useTranslation } from "react-i18next";
import { colors } from "./Colors";
import { toastNotification } from "./ToastNotification";
import Header from "./configurationHelpDesk/Header";
import ColumnColors from "./ColumnColors";
import NewTableDraggable from "./NewTableDraggable";
import { useDispatch, useSelector } from "react-redux";
import { setSearch } from "../new-redux/actions/menu.actions/menu";
import LabelTable from "./LabelTable";
import BottomButtonAddRow from "./BottomButtonAddRow";
import { SubmitKeyPress } from "../utils/SubmitKeyPress";
import { URL_ENV } from "index";
import { getConfigQueuesAndGroups } from "pages/voip/services/services";
import i18n from "translations/i18n";

const QueueGuest = () => {
  const [form] = Form.useForm();
  const [count, setCount] = useState(0);
  const [data, setData] = useState([]);
  const [editingKey, setEditingKey] = useState("");
  const [id, setId] = useState(null);
  const [loading, setLoading] = useState(true);
  const isEditing = (record) => record.key === editingKey;
  const [t] = useTranslation("common");
  const [color, setColor] = useState("");
  const inputRefs = useRef(null);
  const [companies, setCompanies] = useState([]);
  const [numbersSda, setNumbersSda] = useState([]);

  const [selectedItems, setSelectedItems] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [oldPage, setOldPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  const dispatch = useDispatch();
  const { search } = useSelector((state) => state.form);
  const filteredOptions = companies.filter((o) => !selectedItems.includes(o));

  useEffect(() => {
    if (inputRefs?.current) inputRefs?.current.focus();
  }, [data.length, id]);

  const handleKeyPress = (event) => {
    SubmitKeyPress(event, form);
  };
  const onFinishFailed = (values) => {
    console.log(values);
  };

  const EditableCell = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    const inputNode =
      inputType === "companies" ? (
        <Select
          placeholder={t("guestQueue.selectQueue")}
          options={numbersSda}
          optionFilterProp={"label" || "title"}
          showSearch
        />
      ) : (
        <Input
          ref={dataIndex === "name_en" ? inputRefs : null}
          onKeyPress={handleKeyPress}
          placeholder={`${t(`guestQueue.${dataIndex}`)}`}
        />
      );
    return (
      <td {...restProps}>
        {editing ? (
          <Form.Item
            name={dataIndex.toLowerCase()}
            style={{
              margin: 0,
            }}
            rules={[
              {
                required: true,
                message: `${t(`guestQueue.${dataIndex}`)} ${t(
                  "table.header.isrequired"
                )}`,
              },
            ]}
          >
            {inputNode}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };
  const edit = (record) => {
    if (record) {
      form.setFieldsValue({
        name_fr: record.name_fr,
        name_en: record.name_en,
        queue_number: record.queue_number,
      });
      setId(record.id);
      setColor(record.color);
    } else {
      form.setFieldsValue({
        name_fr: "",
        name_en: "",
        queue_number: undefined,
      });
    }
    setEditingKey(record.key);
  };
  const cancel = (record) => {
    setEditingKey("");
    setColor("");
    setId(null);
    if (!record.id) {
      setData(data.filter((item) => item.key !== record.key));
    }
  };
  const save = async () => {
    setLoading(true);
    if (id) {
      try {
        const row = await form.validateFields();
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post(`/queue-guest-display/${id}`, row);
        setEditingKey("");
        setData(
          data.map((el) =>
            el.id === res.data.id
              ? {
                  ...res.data,
                  key: res.data.id,
                }
              : el
          )
        );
        form.setFieldsValue({
          name_fr: "",
          name_en: "",
          queue_number: undefined,
        });
        setColor("");
        setLoading(false);
        toastNotification(
          "success",
          i18n.language === "en"
            ? row.name_en + t("toasts.edit")
            : row.name_fr + t("toasts.edit"),
          "topRight"
        );
      } catch (errInfo) {
        setLoading(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    } else {
      try {
        const row = await form.validateFields();
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post("/queue-guest-display", row);
        setEditingKey("");
        setData([
          ...data.filter((el) => el.id),
          {
            ...res.data.data,
            key: res.data.data.id,
            // companies_ids: row.companies?.join(","),

            // companies_ids: res.data.data.companies
            //   .map((el) => el.social_reason)
            //   .join(","),
          },
        ]);
        form.setFieldsValue({
          name_fr: "",
          name_en: "",
          queue_number: undefined,
        });
        setLoading(false);
        toastNotification(
          "success",
          i18n.language === "en"
            ? row.name_en + t("toasts.created")
            : row.name_fr + t("toasts.created"),
          "topRight"
        );
      } catch (errInfo) {
        setLoading(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    }
  };

  useEffect(() => {
    const getNumbersSda = async () => {
      const res = await getConfigQueuesAndGroups();
      setNumbersSda(
        Object.entries(res.data.queues).map(([key, value]) => ({
          label: key,
          title: key,
          options: [{ value: value.number, label: value.number }],
        }))
      );
    };
    const getDepartments = async () => {
      setLoading(true);
      try {
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get("/queue-guest-display");
        if (res.data.data.length > 0) {
          setCount(Math.max(...data.map((el) => el.id)));
        }
        if (res.status === 200) {
          getNumbersSda();
          setData(
            res.data.data.map((el, i) => ({ ...el, key: el.id, rank: i + 1 }))
          );
          setLoading(false);
        }
      } catch (err) {
        setLoading(false);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    };
    // if (!search && data.length == 0)
    getDepartments();
    // return () => dispatch(setSearch(""));
  }, []);
  useEffect(() => {
    return () => dispatch(setSearch(""));
  }, []);
  const columns = [
    {
      title: t("guestQueue.name_en"),
      dataIndex: "name_en",
      key: "name_en",
      editable: true,
      sorter: (a, b) => a.name_en.localeCompare(b.name_en),
      //   render: (_, record) => {
      //     return (
      //       <LabelTable
      //         record={{ ...record, label: record.name_en }}
      //         editingKey={editingKey}
      //         edit={edit}
      //       />
      //     );
      //   },
    },
    {
      title: t("guestQueue.name_fr"),
      dataIndex: "name_fr",
      key: "name_fr",
      editable: true,
      sorter: (a, b) => a.name_fr.localeCompare(b.name_fr),
      //   render: (_, record) => {
      //     return (
      //       <LabelTable
      //         record={{ ...record, label: record.name_en }}
      //         editingKey={editingKey}
      //         edit={edit}
      //       />
      //     );
      //   },
    },
    {
      title: t("guestQueue.queue_number"),
      dataIndex: "queue_number",
      key: "queue_number",

      editable: true,
      //   render: (_, { companies }) => (
      //     <div>
      //       {companies?.map((el) => (
      //         <div key={el.id}>{el.label}</div>
      //       ))}
      //     </div>
      //   ),
    },
  ];
  const handleAdd = () => {
    setId(null);
    const ids = data.map((object) => {
      return object.id;
    });
    const newData = {
      key: Math.max(...ids) + 1,
      label: `  `,
      color: undefined,
      companies: [],
      disabled: true,
    };
    setCurrentPage(Math.ceil([...data, newData].length / pageSize));
    setOldPage(Math.ceil([...data, newData].length / pageSize));

    setData([...data, newData]);
    form.setFieldsValue({
      name_fr: "",
      name_en: "",
      queue_number: undefined,
    });
    setEditingKey(Math.max(...ids) + 1);
    setCount(Math.max(...ids) + 1);
  };
  const onRow = () => {};
  const filteredData = data.filter((item) => {
    return (
      item?.name_fr?.toLowerCase()?.includes(search.toLowerCase()) ||
      item?.name_en?.toLowerCase()?.includes(search.toLowerCase()) ||
      String(item?.queue_number)?.includes(search.toLowerCase())
    );
  });
  return (
    <Space direction="vertical" style={{ width: "100%" }}>
      <Header
        active={"2"}
        editingKey={editingKey}
        handleAdd={handleAdd}
        btnText={t("general.addQueueGuest")}
        disabled={loading ? true : editingKey ? true : search ? true : false}
        data={filteredData}
        api="departments"
      />

      <NewTableDraggable
        columns={columns}
        setLoading={setLoading}
        isEditing={isEditing}
        data={filteredData}
        setData={setData}
        loading={loading}
        save={save}
        edit={edit}
        EditableCell={EditableCell}
        onFinishFailed={onFinishFailed}
        cancel={cancel}
        form={form}
        apiRank="/rank-queue-guest-display"
        editingKey={editingKey}
        api="queue-guest-display"
        onRow={onRow}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        pageSize={pageSize}
        setPageSize={setPageSize}
      />

      <BottomButtonAddRow
        editingKey={editingKey}
        data={filteredData}
        text={t("general.addQueueGuest")}
        handleAdd={handleAdd}
        loading={loading}
        search={search || ""}
      />
    </Space>
  );
};
export default QueueGuest;
