/**
 * @param path: path of the route
 * @param key: key
 * @param accessTo : to valid the private module access
 * @param onlyChild: to tell the function which one has Parent (Private route) or note,
 * @param element : the compoenent,
 * @param name: to set the key when mapping the same role of key in the array
 * @param element2: if it has many component in route
 *    */

import { lazy } from "react";
import { lazyRetry } from "../utils/lazyRetry";
const mailingPaths = [
  // "/mailing/:accountId/inbox",
  // "/mailing/:accountId/sent",
  // "/mailing/:accountId/drafts",
  // "/mailing/:accountId/starred",
  // "/mailing/:accountId/important",
  // "/mailing/:accountId/trash",
  // "/mailing/:accountId/spam",
  // "/mailing/:accountId/archive",
  "/mailing/:accountId/:boite/:id",
  // "/mailing/:accountId/label/:labelId/:id",
];
const mailingLabelsPaths = [
  "/mailing/:accountId/inbox",
  "/mailing/:accountId/sent",
  "/mailing/:accountId/drafts",
  "/mailing/:accountId/starred",
  "/mailing/:accountId/important",
  "/mailing/:accountId/trash",
  "/mailing/:accountId/spam",
  "/mailing/:accountId/archive",
  "/mailing/:accountId/label/:labelId",
];

const clientsAndUsersPaths = [
  { path: "/contacts", accessTo: "contact" },
  { path: "/companies", accessTo: "companies" },
  { path: "/settings/products", accessTo: "admin" },
  { path: "/deals", accessTo: "deals" },
  { path: "/tickets", accessTo: "ticket" },
  { path: "/settings/users", accessTo: "admin" },
  { path: "/settings/guests", accessTo: "admin" },
  { path: "/projects", accessTo: "projects" },
  { path: "/booking", accessTo: "booking" },
  { path: "/leads", accessTo: "leads" },
  { path: "/invoices", accessTo: "invoices" },
  { path: "/transactions", accessTo: "transactions" },
];

const contactsDetails = [
  { path: "/contacts/:id", accessTo: "contact" },
  { path: "/companies/:id", accessTo: "companies" },
  { path: "/settings/products/:id", accessTo: "admin" },
  { path: "/deals/:id", accessTo: "deals" },
  { path: "/tickets/:id", accessTo: "ticket" },
  { path: "/settings/users", accessTo: "admin" },
  { path: "/settings/guests", accessTo: "admin" },
  { path: "/projects/:id", accessTo: "projects" },
  { path: "/booking/:id", accessTo: "booking" },
  { path: "/leads/:id", accessTo: "leads" },
];
const contactsDetails2 = [
  { path: "/contacts/v2/:id", accessTo: "contact" },
  { path: "/companies/v2/:id", accessTo: "companies" },
  { path: "/settings/products/v2/:id", accessTo: "admin" },
  { path: "/deals/v2/:id", accessTo: "deals" },
  { path: "/tickets/v2/:id", accessTo: "ticket" },
  { path: "/settings/users/v2/:id", accessTo: "admin" },
  { path: "/settings/guests/v2/:id", accessTo: "admin" },
  { path: "/projects/v2/:id", accessTo: "projects" },
  { path: "/booking/v2/:id", accessTo: "booking" },
  { path: "/leads/v2/:id", accessTo: "leads" },
  { path: "/invoices/v2/:id", accessTo: "invoices" },
  { path: "/transactions/v2/:id", accessTo: "transactions" },
];
const wikiPaths = ["wiki", "wiki/:wikiId/:groupId", "wiki/:wikiId"];
const activitiesPath = ["/tasks", "/tasks/:id"];

export const routes = [
  {
    key: "dashboard",
    accessTo: "default",

    path: "/dashboard",
    onlyChild: true,
    element: lazy(() =>
      lazyRetry(() => import("../pages/home/<USER>"), "Home")
    ),
    name: "home",
  },

  // -------------------------> chat <-------------------------

  {
    accessTo: "chat",
    path: "/chat",
    onlyChild: true,

    element: lazy(() =>
      lazyRetry(() => import("../pages/layouts/chat"), "ChatContainer")
    ),
    name: "chat",
  },
  // -------------------------> logs <-------------------------

  {
    accessTo: "logs",
    onlyChild: true,

    path: "/telephony/*",
    element: lazy(() =>
      lazyRetry(() => import("../pages/voip/Logs"), "VoipLogs")
    ),

    name: "log_voip",
  },
  ///////////////////////////
  // {
  //   path: "/telephony",
  //   name: "telephony_all",
  //   children: [
  //     {
  //       accessTo: "phonebook",
  //       // onlyChild: true,

  //       path: "directory",
  //       element: lazy(() =>
  //         lazyRetry(
  //           () => import("../pages/voip/directory/Directory"),
  //           "directory"
  //         )
  //       ),

  //       name: "directory",
  //     },
  //     {
  //       accessTo: "logs",
  //       // onlyChild: true,

  //       path: "callLog",
  //       element: lazy(() =>
  //         lazyRetry(() => import("../pages/voip/call_logs/CallLogs"), "callLog")
  //       ),

  //       name: "callLog",
  //     },
  //     {
  //       accessTo: "logs",
  //       // onlyChild: true,

  //       path: "voicemail",
  //       element: lazy(() =>
  //         lazyRetry(
  //           () => import("../pages/voip/voice_messaging/VoiceMessaging"),
  //           "voicemail"
  //         )
  //       ),

  //       name: "voicemail",
  //     },
  //     {
  //       accessTo: "logs",
  //       // onlyChild: true,

  //       path: "groupsLog",
  //       element: lazy(() =>
  //         lazyRetry(
  //           () => import("../pages/voip/groups_queues/GroupsAndQueues"),
  //           "groupsLog"
  //         )
  //       ),

  //       name: "groupsLog",
  //     },
  //     {
  //       accessTo: "logs",
  //       // onlyChild: true,

  //       path: "livePanel",
  //       element: lazy(() =>
  //         lazyRetry(
  //           () => import("../pages/voip/live_panel/LivePanel"),
  //           "livePanel"
  //         )
  //       ),

  //       name: "livePanel",
  //     },
  //   ],
  // },
  // -------------------------> visio <-------------------------

  {
    accessTo: "visio",
    path: "/visio",
    onlyChild: true,

    element: lazy(() =>
      lazyRetry(() => import("../components/NewListVisio"), "NewListVisio")
    ),

    name: "visio",
  },
  // -------------------------> notes <-------------------------

  {
    accessTo: "notes",
    path: "/notes",
    onlyChild: true,

    element: lazy(() =>
      lazyRetry(() => import("../pages/notes/NotesIndex"), "NotesIndex")
    ),

    name: "notes",
  },
  // -------------------------> mailing <-------------------------

  ...mailingPaths.map((item, index) => ({
    key: index,
    onlyChild: true,
    path: item,
    accessTo: "email",
    name: "mailing_" + index,
    element: lazy(() =>
      lazyRetry(() => import("../pages/rmc/mailing/mailing"), "Mailing")
    ),
  })),
  ...mailingLabelsPaths.map((item, index) => ({
    key: index,
    onlyChild: true,
    path: item,
    accessTo: "email",
    name: "mailing_labels" + index,
    element: lazy(() =>
      lazyRetry(
        () => import("../pages/rmc/mailing/main-components/MailRouting"),
        "Mailing_labels"
      )
    ),
  })),
  // -------------------------> task <-------------------------

  ...activitiesPath.map((path, index) => ({
    accessTo: "activities",
    path: path,
    onlyChild: true,

    element: lazy(() =>
      lazyRetry(() => import("../pages/tasks/TasksWrapper"), "TasksWrapper")
    ),

    name: "task_" + index,
  })),
  // -------------------------> directory <-------------------------
  // {
  //   path: "directory",
  //   accessTo: "phonebook",
  //   name: "directory_1",
  //   onlyChild: true,
  //   element: lazy(() =>
  //     lazyRetry(() => import("../pages/voip/directory/Directory"), "Directory")
  //   ),
  // },
  // -------------------------> family <-------------------------

  ...clientsAndUsersPaths.map((item, index) => ({
    accessTo: item.accessTo,
    onlyChild: true,

    path: item.path,
    element: lazy(() =>
      lazyRetry(
        () => import("../pages/clients&users/FamilyRouting"),
        "FamilyRouting"
      )
    ),
    name: "routing_" + index,
  })),
  // -------------------------> family detail <-------------------------

  ...contactsDetails.map((item, index) => ({
    onlyChild: true,
    accessTo: item.accessTo,
    path: item.path,
    element: lazy(() =>
      lazyRetry(
        () => import("../pages/components/DetailsProfile/LayoutDetails"),
        "LayoutDetails"
      )
    ),

    name: "layout_details_" + index,
  })),
  ...contactsDetails2.map((item, index) => ({
    onlyChild: true,
    accessTo: item.accessTo,
    path: item.path,
    element: lazy(() =>
      lazyRetry(
        () => import("../pages/components/DetailsProfile/ViewSphere2"),
        "LayoutDetails"
      )
    ),

    name: "layout_details_" + index,
  })),
  // ------------------------->  RMC <-------------------------

  {
    onlyChild: true,

    accessTo: "rmc",
    path: "/rmc",
    element: lazy(() => lazyRetry(() => import("../pages/rmc/rmc"), "Rmc")),

    name: "RMC",
  },

  {
    path: "/stats",

    name: "stat_all",
    children: [
      {
        accessTo: "under_test",
        path: "general",

        element: lazy(() =>
          lazyRetry(() => import("../pages/stat/general"), "GeneralStat")
        ),

        name: "stat_general",
      },
      {
        accessTo: "rmc",
        path: "rmc",
        element: lazy(() =>
          lazyRetry(() => import("../pages/stat/stats"), "RmcStats")
        ),
        name: "stat_rmc",
      },
      {
        accessTo: "families",
        path: "families",
        element: lazy(() =>
          lazyRetry(() => import("../pages/home/<USER>"), "Families")
        ),
        name: "families",
      },
    ],
  },
  // ------------------  onlyoffice ------------------
  {
    accessTo: "default",
    path: "/editor",
    element: lazy(() =>
      lazyRetry(() => import("pages/onlyOffice/DocumentEditor"), "editor")
    ),
    onlyChild: true,
    name: "editor",
  },
  // ------------------>  Porfile <------------------

  {
    path: "/profile",
    name: "profile_all",
    children: [
      {
        accessTo: "default",
        path: "general",
        element: lazy(() =>
          lazyRetry(
            () => import("../pages/profile/ProfileDetail"),
            "ProfileDetail"
          )
        ),
        name: "profile_general",
      },
      {
        accessTo: "default",
        path: "security",
        element: lazy(() =>
          lazyRetry(() => import("../pages/profile/Security"), "Security")
        ),
        name: "profile_security",
      },
      {
        accessTo: "default",
        path: "signature",
        element: lazy(() =>
          lazyRetry(() => import("../pages/profile/Signature"), "Security")
        ),
        name: "signature",
      },
      {
        accessTo: "default",
        path: "localization",
        element: lazy(() =>
          lazyRetry(
            () => import("../pages/settings/LocalisationSettings"),
            "LocalisationSettings"
          )
        ),

        name: "profile_localization",
      },
      {
        accessTo: "default",
        path: "notification",
        element: lazy(() =>
          lazyRetry(
            () => import("../pages/profile/NotificationsManagement"),
            "Notification_managaer"
          )
        ),

        name: "profile_notification",
      },
      {
        accessTo: "default",
        path: "accessToken",
        element: lazy(() =>
          lazyRetry(
            () => import("../pages/profile/AccessToken"),
            "Access_token"
          )
        ),

        name: "Access_token",
      },
      {
        accessTo: "default",
        path: "infoTenant",
        element: lazy(() =>
          lazyRetry(() => import("../pages/profile/InfoTenant"), "InfoTenant")
        ),

        name: "InfoTenant",
      },
      {
        accessTo: "default",
        path: "allNotifications",

        element: lazy(() =>
          lazyRetry(
            () => import("../pages/notifications/notifications"),
            "notifications_ALL"
          )
        ),

        name: "allNotifications",
      },
    ],
  },
  {
    accessTo: "default",
    path: "/profile/appMobile",
    element: lazy(() =>
      lazyRetry(() => import("pages/components/AppMobile"), "editor")
    ),
    onlyChild: true,
    name: "appMobile",
  },
  {
    accessTo: "default",
    path: "/profile/policies",
    element: lazy(() =>
      lazyRetry(() => import("pages/profile/Policies"), "editor")
    ),
    onlyChild: true,
    name: "policies",
  },

  //---------------------->settings<----------------------
  {
    path: "/settings",
    name: "settings_all",
    children: [
      {
        path: "general",
        name: "settings_general",
        //---------------------->settings general<----------------------
        children: [
          {
            accessTo: "admin",
            path: "departments",
            element: lazy(() =>
              lazyRetry(
                () => import("../components/Departments"),
                "Departments"
              )
            ),

            element2: lazy(() =>
              lazyRetry(() => import("../pages/settings/General"), "General")
            ),
            name: "settings_general_departments",
          },

          {
            accessTo: "admin",
            path: "countries",
            element: lazy(() =>
              lazyRetry(
                () => import("../components/TableCountries"),
                "TableCountries"
              )
            ),
            element2: lazy(() =>
              lazyRetry(() => import("../pages/settings/General"), "General")
            ),
            name: "settings_general_countries",
          },
          {
            accessTo: "admin",
            path: "services",
            element: lazy(() =>
              lazyRetry(() => import("../components/Services"), "Services")
            ),
            element2: lazy(() =>
              lazyRetry(() => import("../pages/settings/General"), "General")
            ),
            name: "settings_general_services",
          },
          {
            accessTo: "admin",
            path: "companies",
            element: lazy(() =>
              lazyRetry(
                () => import("../pages/settings/TableCompanies"),
                "TableCompanies"
              )
            ),
            element2: lazy(() =>
              lazyRetry(() => import("../pages/settings/General"), "General")
            ),
            name: "settings_general_companies",
          },
          {
            accessTo: "admin",
            path: "channels",
            element: lazy(() =>
              lazyRetry(
                () => import("../components/ListChannels"),
                "TableChannels"
              )
            ),
            element2: lazy(() =>
              lazyRetry(() => import("../pages/settings/General"), "General")
            ),
            name: "settings_general_channels",
          },
          {
            accessTo: "admin",
            path: "companies/:id",
            element: lazy(() =>
              lazyRetry(
                () => import("../components/ConfigCompanies"),
                "ConfigCompanies"
              )
            ),

            name: "settings_general_companies_id",
          },
          {
            accessTo: "admin",
            path: "companies/:id/banks",
            element: lazy(() =>
              lazyRetry(
                () => import("../components/BanksCompanies"),
                "BanksCompanies"
              )
            ),

            name: "settings_general_companies_id_banks",
          },
          {
            accessTo: "admin",
            path: "companies/:id/tax",
            element: lazy(() =>
              lazyRetry(
                () => import("../components/TaxCompanies"),
                "TaxCompanies"
              )
            ),

            name: "settings_general_companies_id_banks",
          },
          {
            accessTo: "admin",
            path: "companies/:id/signature",
            element: lazy(() =>
              lazyRetry(
                () => import("../components/SigantureCompany"),
                "SignatureCompany"
              )
            ),

            name: "settings_general_companies_id_signature",
          },
          {
            accessTo: "admin",
            path: "guestQueue",
            element: lazy(() =>
              lazyRetry(() => import("../components/QueueGuest"), "QueueGuest")
            ),

            element2: lazy(() =>
              lazyRetry(() => import("../pages/settings/General"), "General")
            ),
            name: "settings_general_guestQueue",
          },
        ],
      },

      {
        accessTo: "admin",
        path: "fields/:id",
        name: "settings_fields_id",
        element: lazy(() =>
          lazyRetry(
            () => import("../components/FieldSettings/FieldsContainer"),
            "FieldsContainer"
          )
        ),
      },
      // ---------------------> settings activity <---------------------
      {
        path: "activity",
        name: "settings_activity",
        //---------------------->settings general<----------------------
        children: [
          {
            accessTo: "activities",
            path: "types",
            element: lazy(() =>
              lazyRetry(
                () => import("../pages/settings/Activities"),
                "Activities"
              )
            ),
            element2: lazy(() =>
              lazyRetry(
                () => import("../pages/settings/TabActivities"),
                "TabActivities"
              )
            ),

            name: "settings_activity_type",
          },
          {
            accessTo: "activities",
            path: "pipelines",
            element: lazy(() =>
              lazyRetry(
                () => import("../components/ActivityPipelines"),
                "ActivityPipelines"
              )
            ),
            element2: lazy(() =>
              lazyRetry(
                () => import("../pages/settings/TabActivities"),
                "TabActivities"
              )
            ),

            name: "settings_activity_pipeline",
          },
        ],
      },

      {
        accessTo: "default",
        path: "tags",
        name: "settings_tags",
        element: lazy(() =>
          lazyRetry(() => import("../pages/settings/Tags"), "Tags")
        ),
      },
      //---------------------->settings mail<----------------------

      {
        accessTo: "email",
        path: "emailAccounts",
        name: "settings_email",
        element: lazy(() =>
          lazyRetry(
            () => import("../pages/settings/ConfigEmail"),
            "ConfigEmail"
          )
        ),
      },
      //---------------------->settings mail<----------------------

      {
        accessTo: "email",
        path: "emailTemplates",
        name: "templates_email",
        element: lazy(() =>
          lazyRetry(
            () => import("../pages/settings/EmailTemplates"),
            "EmailTemplates"
          )
        ),
      },
      //---------------------->settings helpdesk<----------------------

      {
        path: "helpDesk",
        name: "settings_helpDesk",
        children: [
          // {
          //   accessTo: "ticket",
          //   path: "folders",

          //   element: lazy(() => lazyRetry(() => import("../components/Folders"), "Folders")),

          //   element2: lazy(() => lazyRetry(() => import("../pages/settings/HelpDesk"), "HelpDesk")),
          //   name: "settings_helpDesk_folder",
          // },
          {
            accessTo: "admin",
            path: "Subjects",
            element: lazy(() =>
              lazyRetry(() => import("../components/Subjects"), "Subjects")
            ),
            element2: lazy(() =>
              lazyRetry(() => import("../pages/settings/HelpDesk"), "HelpDesk")
            ),

            name: "settings_helpDesk_Subjects",
          },
          {
            accessTo: "admin",
            path: "Levels",
            element: lazy(() =>
              lazyRetry(() => import("../components/Levels"), "Levels")
            ),
            element2: lazy(() =>
              lazyRetry(() => import("../pages/settings/HelpDesk"), "HelpDesk")
            ),

            name: "settings_helpDesk_Level",
          },
          {
            accessTo: "admin",

            path: "severities",
            element: lazy(() =>
              lazyRetry(() => import("../components/Severities"), "Severities")
            ),
            element2: lazy(() =>
              lazyRetry(() => import("../pages/settings/HelpDesk"), "HelpDesk")
            ),

            name: "settings_helpDesk_severities",
          },
          {
            accessTo: "admin",
            path: "SLA",
            element: lazy(() =>
              lazyRetry(
                () => import("../components/SlaHelpDesk"),
                "SlaHelpDesk"
              )
            ),
            element2: lazy(() =>
              lazyRetry(() => import("../pages/settings/HelpDesk"), "HelpDesk")
            ),

            name: "settings_helpDesk_SLA",
          },
        ],
      },
      //---------------------->settings pipline<----------------------
      {
        accessTo: "admin",
        path: "pipeline/:id",
        name: "settings_pipline_family",
        element: lazy(() =>
          lazyRetry(
            () => import("../pages/settings/PipelineStages"),
            "PipelineStages"
          )
        ),
      },

      //---------------------->settings sales<----------------------
      {
        accessTo: "deals",
        path: "sales",
        name: "settings_sales",
        element: lazy(() =>
          lazyRetry(() => import("../pages/settings/Sales"), "Sales")
        ),
      },

      {
        accessTo: "deals",
        path: "sales/currencies",
        name: "settings_sales_currencies",
        element2: lazy(() =>
          lazyRetry(() => import("../pages/settings/Sales"), "Sales")
        ),
        element: lazy(() =>
          lazyRetry(
            () => import("../components/TableCurrencies"),
            "TableCurrencies"
          )
        ),
      },
      //---------------------->settings import<----------------------
      {
        accessTo: "import",
        path: "import",
        name: "settings_import",
        element: lazy(() =>
          lazyRetry(() => import("../pages/import/import"), "import")
        ),
      },
      {
        accessTo: "admin",
        path: "rmc",
        name: "settings_rmc",
        element: lazy(() =>
          lazyRetry(() => import("../pages/rmc/settings"), "RMC")
        ),
      },
      //---------------------->settings checklist<----------------------
      {
        accessTo: "under_test",
        path: "checklist",
        name: "settings_checklist",
        element: lazy(() =>
          lazyRetry(() => import("../pages/settings/CheckLists"), "CheckLists")
        ),
      },
      //---------------------->settings integrations<----------------------
      {
        accessTo: "admin",
        path: "integrations",
        name: "settings_integrations",
        element: lazy(() =>
          lazyRetry(
            () => import("../pages/settings/integrations/index"),
            "integrations"
          )
        ),
      },
      //---------------------->settings liveChat<----------------------
      {
        accessTo: "rmc",
        path: "liveChat",
        name: "settings_liveChat",
        element: lazy(() =>
          lazyRetry(
            () => import("../pages/settings/LiveChatConfig"),
            "liveChat"
          )
        ),
      },

      //---------------------->settings triggers<----------------------
      {
        accessTo: "under_test",
        path: "triggers",
        name: "settings_triggers",
        element: lazy(() =>
          lazyRetry(
            () => import("../pages/settings/TriggersRules"),
            "TriggersRules"
          )
        ),
      },
      //---------------------->settings unavailability<----------------------

      {
        accessTo: "under_test",
        path: "unavailability",
        name: "settings_unavailability",
        element: lazy(() =>
          lazyRetry(
            () => import("../pages/settings/Unavailability"),
            "Unavailability"
          )
        ),
      },
      //---------------------->settings contacts-types<----------------------
      {
        accessTo: "contact",
        path: "contacts-types",
        name: "settings_contacts-types",
        element: lazy(() =>
          lazyRetry(
            () =>
              import("../pages/clients&users/utility-component/TypesContacts"),
            "TypesContacts"
          )
        ),
      },
      //---------------------->settings products<----------------------
      {
        path: "products",
        name: "settings_products_general",
        children: [
          {
            accessTo: "admin",
            path: "unity",
            name: "settings_products_unity",
            element: lazy(() =>
              lazyRetry(
                () => import("../components/SettingsProducts/Unity"),
                "Unity"
              )
            ),
            element2: lazy(() =>
              lazyRetry(() => import("../pages/settings/Products"), "Products")
            ),
          },
          {
            accessTo: "admin",
            path: "products",
            name: "settings_products",
            element: lazy(() =>
              lazyRetry(
                () => import("../components/SettingsProducts/Unity"),
                "Unity"
              )
            ),
            // element2: lazy(() =>
            //   lazyRetry(() => import("../pages/settings/Products"), "Products")
            // ),
          },
          {
            accessTo: "admin",
            path: "family",
            name: "settings_products_family",
            element: lazy(() =>
              lazyRetry(
                () => import("../components/SettingsProducts/FamilyProduct"),
                "FamilyProduct"
              )
            ),
            element2: lazy(() =>
              lazyRetry(() => import("../pages/settings/Products"), "Products")
            ),
          },
          {
            accessTo: "admin",
            path: "type",
            name: "settings_products_types",
            element: lazy(() =>
              lazyRetry(
                () => import("../components/SettingsProducts/TypesProduct"),
                "TypesProduct"
              )
            ),
            element2: lazy(() =>
              lazyRetry(() => import("../pages/settings/Products"), "Products")
            ),
          },
          {
            accessTo: "admin",
            path: "discount",
            name: "settings_products_discount",
            element: lazy(() =>
              lazyRetry(
                () => import("../components/SettingsProducts/DiscountProduct"),
                "DiscountProduct"
              )
            ),
            element2: lazy(() =>
              lazyRetry(() => import("../pages/settings/Products"), "Products")
            ),
          },
        ],
      },
      //---------------------->settings users<----------------------

      {
        path: "users",
        name: "settings_users_general",
        children: [
          {
            accessTo: "default",
            path: ":id",
            name: "settings_users_general_id",
            element: lazy(() =>
              lazyRetry(
                () =>
                  import("../pages/components/DetailsProfile/LayoutDetails"),
                "LayoutDetails"
              )
            ),
          },
          {
            accessTo: "default",
            path: "role",
            name: "settings_users_role",
            element: lazy(() =>
              lazyRetry(() => import("../components/RolesUsers"), "RolesUsers")
            ),
            element2: lazy(() =>
              lazyRetry(() => import("../pages/settings/Users"), "Users")
            ),
          },
          {
            accessTo: "default",
            path: "team",
            name: "settings_users_team",
            element: lazy(() =>
              lazyRetry(() => import("../components/TeamsUsers"), "TeamsUsers")
            ),
            element2: lazy(() =>
              lazyRetry(() => import("../pages/settings/Users"), "Users")
            ),
          },
        ],
      },
      // --------------------> settings wiki <---------------------
      ...wikiPaths.map((path) => ({
        accessTo: "default",
        key: "settings_wiki_" + path,
        path: path,
        name: "settings_wiki_" + path,
        element: lazy(() =>
          lazyRetry(
            () => import("../pages/wiki/GlobalWrapper"),
            "GlobalWrapper"
          )
        ),
      })),
      // --------------------> settings logs <---------------------

      {
        accessTo: "default",
        path: "log-action",
        name: "settings_logs",
        element: lazy(() =>
          lazyRetry(() => import("../components/LogActions"), "LogActions")
        ),
      },
      {
        accessTo: "folders",
        path: "folders",
        name: "settings_helpDesk_folder",
        element: lazy(() =>
          lazyRetry(() => import("../components/Folders"), "Folders")
        ),
      },

      // --------------------> settings tour <---------------------
      {
        accessTo: "default",
        path: "tour",
        name: "settings_tour",
        element: lazy(() =>
          lazyRetry(() => import("../pages/settings/Tour/Tour"), "Tour")
        ),
      },
    ],
  },
  //---------------------->settings contacts<----------------------

  {
    path: "contact",
    name: "contact_general",
    children: [
      {
        accessTo: "contact",
        path: "companies-types",
        name: "contact_general_companies-types",
        element: lazy(() =>
          lazyRetry(
            () =>
              import("../pages/clients&users/utility-component/TypesCompanies"),
            "TypesCompanies"
          )
        ),
      },
      {
        accessTo: "contact",
        path: "prospects",
        name: "contact_general_prospects",
        element: lazy(() =>
          lazyRetry(() => import("../pages/rmc/prospects"), "Prospects")
        ),
      },
    ],
  },

  //---------------------->Inbox<----------------------
  {
    accessTo: "default",
    path: "interactions",
    element: lazy(() =>
      lazyRetry(() => import("../pages/inbox/index"), "Interactions")
    ),

    name: "interactions",
  },

  // -------------------------> drive <-------------------------

  {
    accessTo: "drive",
    path: "/drive",
    onlyChild: true,

    element: lazy(() =>
      lazyRetry(() => import("../pages/drive/drive.component"), "drive")
    ),

    name: "drive",
  },

  //---------------------->other route<----------------------
];
