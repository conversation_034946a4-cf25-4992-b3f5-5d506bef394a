import { Card, Typography } from "antd";
import React from "react";

const CardStat = ({ item }) => {
  return (
    <div className="grow ">
      <Card
        bordered={true}
        styles={{
          body: {
            padding: "5px",
          },
        }}
      >
        {/* <Statistic title={item.title + " " + item.value} /> */}
        <div className="flex flex-nowrap justify-between gap-1">
          <Typography.Text type="secondary" className="whitespace-nowrap	">
            {item?.title}
          </Typography.Text>
          <Typography.Text className="whitespace-nowrap	">
            {item?.value}
          </Typography.Text>
        </div>
      </Card>
    </div>
  );
};

export default CardStat;
