import { toastNotification } from "components/ToastNotification";
import { decryptC } from "components/webphone/Webphone/PhoneCpt";
import { SET_IP_ADDRESS } from "new-redux/constants";
import MainService from "services/main.service";

let prevController = null;

export const getIpAndDeclareIt =
  (posteVoip, param, currentIP) => async (dispatch) => {
    try {
      if (prevController) {
        prevController.abort();
        console.log("Previous request aborted");
      }

      const controller = new AbortController();
      const { signal } = controller;
      prevController = controller;

      const param5 = decryptC(param?.param_5 || "");

      if (param5 === "false") {
        throw new Error(
          `Cannot process getIpAndDeclareIt: param_5 is set to "false".`
        );
      }

      const resp = await MainService.ipPublicApiIPBX(posteVoip, { signal });
      const lastKnownIP = resp.data?.ip_public || null;

      if (!lastKnownIP) {
        toastNotification(
          "error",
          "Cannot retrieve public IP from IPBX server.",
          "topRight",
          5
        );
        return;
      }

      if (lastKnownIP === currentIP) {
        console.warn("IP address is already up-to-date.");
        return;
      }

      await MainService.ipDecApiIPBX(posteVoip, lastKnownIP, { signal });

      dispatch({
        type: SET_IP_ADDRESS,
        payload: lastKnownIP,
      });
      // console.log(`IP successfully declared: ${lastKnownIP}`);
    } catch (err) {
      if (err.name === "CanceledError") {
        console.log("Request was aborted.");
      } else {
        console.error(`Error in getIpAndDeclareIt: ${err.message}`);
        toastNotification(
          "error",
          `Error declaring IP: ${err.message}`,
          "topRight",
          5
        );
      }
    }
  };
