import React, { useEffect, useRef } from "react";
import { Button, Form, Input, Space } from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useLocation } from "react-router-dom";
import GroupColors from "../../../components/GroupColors";
import { toastNotification } from "../../../components/ToastNotification";
import ColumnColors from "../../../components/ColumnColors";
import { generateAxios } from "../../../services/axiosInstance";
import { colors } from "../../../components/Colors";
import Header from "../../../components/configurationHelpDesk/Header";
import { useDispatch, useSelector } from "react-redux";
import { PlusCircleOutlined } from "@ant-design/icons";
import NewTableDraggable from "../../../components/NewTableDraggable";
import { setSearch } from "../../../new-redux/actions/menu.actions/menu";
import LabelTable from "../../../components/LabelTable";
import BottomButtonAddRow from "../../../components/BottomButtonAddRow";
import { SubmitKeyPress } from "../../../utils/SubmitKeyPress";
import { URL_ENV } from "index";

const TypesCompanies = () => {
  const [form] = Form.useForm();
  const [count, setCount] = useState(0);
  const [data, setData] = useState([]);
  const [editingKey, setEditingKey] = useState("");
  const [id, setId] = useState(null);
  const [loading, setLoading] = useState(true);
  const isEditing = (record) => record.key === editingKey;
  const [t] = useTranslation("common");
  const [color, setColor] = useState("");
  const dispatch = useDispatch();
  const { search } = useSelector((state) => state.form);
  const [currentPage, setCurrentPage] = useState(1);
  const [oldPage, setOldPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  const inputRefs = useRef([]);
  useEffect(() => {
    inputRefs.current.forEach((input) => {
      input?.focus();
    });
  }, [data.length, id]);
  // useEffect(() => {
  //   if (editingKey && oldPage !== currentPage) {
  //     setData((prev) => prev.filter((el) => el.id));
  //     setEditingKey("");
  //   }
  // }, [currentPage, editingKey, oldPage]);
  const handleKeyPress = (event) => {
    SubmitKeyPress(event, form);
  };
  const onFinishFailed = (values) => {
    console.log(values);
  };

  const EditableCell = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    const inputNode =
      inputType === "radio" ? (
        <GroupColors color={color} setColor={setColor} />
      ) : (
        <Input
          ref={(el) => (inputRefs.current[index] = el)}
          onKeyPress={handleKeyPress}
          placeholder={t("activities.name")}
        />
      );
    return (
      <td {...restProps}>
        {editing ? (
          <Form.Item
            name={dataIndex.toLowerCase()}
            style={{
              margin: 0,
            }}
            rules={[
              {
                required: dataIndex === "label" ? true : false,
                message: `${t("tags.name")} ${t("table.header.isrequired")}`,
              },
            ]}>
            {inputNode}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };
  const edit = (record) => {
    if (record) {
      form.setFieldsValue({
        label: record.label,
        color: record.color,
      });
      setId(record.id);
      setColor(record.color);
    } else {
      form.setFieldsValue({
        label: "",
        color: "",
      });
    }
    setEditingKey(record.key);
  };
  const cancel = (record) => {
    setEditingKey("");
    setColor("");
    setId(null);
    if (!record.id) {
      setData(data.filter((item) => item.key !== record.key));
    }
  };
  const save = async (key) => {
    setLoading(true);
    if (id) {
      try {
        const row = await form.validateFields();
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post(`type-contacts/update/${id}`, {
          ...row,
          color,
        });
        setEditingKey("");
        setData(
          data.map((el) =>
            el.id === res.data.data.id
              ? {
                  ...res.data.data,
                  key: res.data.data.id,
                }
              : el
          )
        );
        form.setFieldsValue({
          label: "",
          color: "",
        });
        setColor("");
        setLoading(false);
        toastNotification("success", row.label + t("toasts.edit"), "topRight");
      } catch (errInfo) {
        setLoading(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    } else {
      try {
        const row = await form.validateFields();
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post("type-contacts", {
          ...row,
          color,
        });
        setEditingKey("");
        setData([
          ...data.filter((el) => el.id),
          { ...res.data.data, key: res.data.data.id },
        ]);
        form.setFieldsValue({
          label: "",
          color: "",
        });
        setColor("");
        setLoading(false);
        toastNotification(
          "success",
          row.label + t("toasts.created"),
          "topRight"
        );
      } catch (errInfo) {
        setLoading(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    }
  };

  useEffect(() => {
    const getTypesContacts = async () => {
      setLoading(true);
      try {
        const {
          data: { data },
        } = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get("/type-contacts");
        setData(data.map((el, i) => ({ ...el, key: el.id, rank: i + 1 })));
        if (data.length > 0) {
          setCount(Math.max(...data.map((el) => el.id)));
        }
        setLoading(false);
      } catch (err) {
        setLoading(false);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    };
    getTypesContacts();

    return () => dispatch(setSearch(""));
  }, []);
  const handleClick = (event) => {
    event.stopPropagation();
  };

  const columns = [
    {
      title: t("activities.name"),
      dataIndex: "label",
      key: "label",
      editable: true,
      sorter: (a, b) => a.label.localeCompare(b.label),
      render: (_, record) => {
        return (
          <LabelTable record={record} editingKey={editingKey} edit={edit} />
        );
      },
    },

    {
      title: t("activities.color"),
      dataIndex: "color",
      key: "color",
      editable: true,
      render: (_, { color }) => <ColumnColors color={color} colors={colors} />,
    },
  ];

  const onRow = () => {};
  const handleAdd = () => {
    const ids = data.map((object) => {
      return object.id;
    });
    setId(null);

    const newData = {
      key: Math.max(...ids) + 1,
      label: "",
      color: "",
      disabled: true,
    };
    setCurrentPage(Math.ceil([...data, newData].length / pageSize));
    setOldPage(Math.ceil([...data, newData].length / pageSize));
    setData([...data, newData]);
    form.setFieldsValue({
      label: "",
      color: "",
    });
    setEditingKey(Math.max(...ids) + 1);
    setCount(Math.max(...ids) + 1);
  };
  const filteredData = data.filter((item) => {
    return item.label?.toLowerCase().includes(search.toLowerCase());
  });
  return (
    <Space direction="vertical" style={{ width: "100%" }}>
      <div className="pt-4">
        <Header
          active={"4"}
          editingKey={editingKey}
          handleAdd={handleAdd}
          btnText={t("helpDesk.addType")}
          disabled={loading ? true : editingKey ? true : search ? true : false}
        />
      </div>
      <NewTableDraggable
        columns={columns}
        setLoading={setLoading}
        isEditing={isEditing}
        data={filteredData}
        setData={setData}
        loading={loading}
        save={save}
        edit={edit}
        EditableCell={EditableCell}
        onFinishFailed={onFinishFailed}
        cancel={cancel}
        form={form}
        apiRank="/rank-type-contacts"
        editingKey={editingKey}
        api="type-contacts"
        onRow={onRow}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        pageSize={pageSize}
        setPageSize={setPageSize}
      />
      <BottomButtonAddRow
        editingKey={editingKey}
        data={filteredData}
        text={t("helpDesk.addType")}
        handleAdd={handleAdd}
        loading={loading}
        search={search || ""}
      />
    </Space>
  );
};
export default TypesCompanies;
