diff --git a/node_modules/rc-virtual-list/es/ScrollBar.js b/node_modules/rc-virtual-list/es/ScrollBar.js
index 9ba1ebf..cfcf0db 100644
--- a/node_modules/rc-virtual-list/es/ScrollBar.js
+++ b/node_modules/rc-virtual-list/es/ScrollBar.js
@@ -45,13 +45,13 @@ var ScrollBar = /*#__PURE__*/React.forwardRef(function (props, ref) {
     visible = _React$useState8[0],
     setVisible = _React$useState8[1];
   var visibleTimeoutRef = React.useRef();
-  var delayHidden = function delayHidden() {
-    clearTimeout(visibleTimeoutRef.current);
-    setVisible(true);
-    visibleTimeoutRef.current = setTimeout(function () {
-      setVisible(false);
-    }, 3000);
-  };
+  // var delayHidden = function delayHidden() {
+  //   clearTimeout(visibleTimeoutRef.current);
+  //   setVisible(true);
+  //   visibleTimeoutRef.current = setTimeout(function () {
+  //     setVisible(false);
+  //   }, 3000);
+  // };
 
   // ======================== Range =========================
   var enableScrollRange = scrollRange - containerSize || 0;
@@ -163,14 +163,14 @@ var ScrollBar = /*#__PURE__*/React.forwardRef(function (props, ref) {
       };
     }
   }, [dragging]);
-  React.useEffect(function () {
-    delayHidden();
-  }, [scrollOffset]);
+  // React.useEffect(function () {
+  //  delayHidden();
+  // }, [scrollOffset]);
 
   // ====================== Imperative ======================
   React.useImperativeHandle(ref, function () {
     return {
-      delayHidden: delayHidden
+      delayHidden: ()=>{}
     };
   });
 
@@ -223,7 +223,7 @@ var ScrollBar = /*#__PURE__*/React.forwardRef(function (props, ref) {
     className: classNames(scrollbarPrefixCls, _defineProperty(_defineProperty(_defineProperty({}, "".concat(scrollbarPrefixCls, "-horizontal"), horizontal), "".concat(scrollbarPrefixCls, "-vertical"), !horizontal), "".concat(scrollbarPrefixCls, "-visible"), visible)),
     style: _objectSpread(_objectSpread({}, containerStyle), style),
     onMouseDown: onContainerMouseDown,
-    onMouseMove: delayHidden
+    onMouseMove: ()=>{}
   }, /*#__PURE__*/React.createElement("div", {
     ref: thumbRef,
     className: classNames("".concat(scrollbarPrefixCls, "-thumb"), _defineProperty({}, "".concat(scrollbarPrefixCls, "-thumb-moving"), dragging)),
