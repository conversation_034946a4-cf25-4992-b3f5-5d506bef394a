import {
  DELETE_GROUP_WIKI_SUCCESS,
  DELETE_GROUP_WIKI_ERROR,
} from "../../constants";
import MainService from "../../../services/main.service";
import { toastNotification } from "../../../components/ToastNotification";

export const DeleteGroupWiki = (groupId, t) => async (dispatch) => {
  try {
    //dispatch({ type: IS_LOADING });
    const response = await MainService.removeGroup(groupId);

    dispatch({
      type: DELETE_GROUP_WIKI_SUCCESS,
      payload: groupId,
    });
    toastNotification("success", "wiki" + t("toasts.deleted"), "topRight");
  } catch (error) {
    dispatch({
      type: DELETE_GROUP_WIKI_ERROR,
      payload: error,
    });
  }
};
