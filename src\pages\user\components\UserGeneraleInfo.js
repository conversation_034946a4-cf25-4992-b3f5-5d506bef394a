import { useState, useEffect } from "react";
//
import { Divider, Input, Select } from "antd";

const UserGeneraleInfo = () => {
    const [firstName, setFirstName] = useState("Hassine")
    const [updateFirstName, setUpdateFirstName] = useState(false)
    //
    const [role, setRole] = useState({label: "Super Admin", value: "super_admin"})
    const [updateRole, setUpdateRole] = useState(false)
    //
  return (
    <>
      <div className="flex flex-col space-y-4 p-6">
        <div className="grid grid-cols-3 text-sm font-medium ">
          <p className="text-slate-600">First Name</p>
          {updateFirstName ? (
            <Input
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
            />
          ) : (
            <p className="font-semibold">{firstName}</p>
          )}
          <div className="flex justify-end">
            {updateFirstName ? (
              <a onClick={() => setUpdateFirstName(false)}>Submit</a>
            ) : (
              <a onClick={() => setUpdateFirstName(true)}>Update</a>
            )}
          </div>
        </div>
        <Divider />
        <div className="grid grid-cols-3 text-sm font-medium ">
          <p className="text-slate-600">Last Name</p>

          <p className="font-semibold">Turki Basla</p>

          <div className="flex justify-end">
            <a>Update</a>
          </div>
        </div>
        <Divider />
        <div className="grid grid-cols-3 text-sm font-medium ">
          <p className="text-slate-600">E-Mail</p>

          <p className="font-semibold"><EMAIL></p>

          <div className="flex justify-end">
            <a>Update</a>
          </div>
        </div>
        <Divider />
        <div className="grid grid-cols-3 text-sm font-medium ">
          <p className="text-slate-600">Phone Number</p>

          <p className="font-semibold">99 391 220</p>

          <div className="flex justify-end">
            <a>Update</a>
          </div>
        </div>
        <Divider />
        <div className="grid grid-cols-3 text-sm font-medium ">
          <p className="text-slate-600">Extension</p>

          <p className="font-semibold">208</p>

          <div className="flex justify-end">
            <a>Update</a>
          </div>
        </div>
        <Divider />
        <div className="grid grid-cols-3 text-sm font-medium ">
          <p className="text-slate-600">Job Title</p>

          <p className="font-semibold">Front-End Developer</p>

          <div className="flex justify-end">
            <a>Update</a>
          </div>
        </div>
      </div>

      <div style={{ backgroundColor: "#f5f5f5", height: "1rem" }} />

      <div className="flex flex-col space-y-4 p-6">
        <div className="grid grid-cols-3 text-sm font-medium ">
          <p className="text-slate-600">Role</p>

          {updateRole ? (
            <Select
              showSearch
              placeholder="Select a person"
              optionFilterProp="children"
              value={role?.value}
              onChange={(_, option) =>
                setRole({ label: option?.label, value: option?.value })
              }
              filterOption={(input, option) =>
                (option?.label ?? "")
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
              options={[
                {
                  label: "Super Admin",
                  value: "super_admin",
                },
                {
                  label: "Support",
                  value: "support",
                },
                {
                  label: "Commercial",
                  value: "commercial",
                },
                {
                  label: "Manager Commercial",
                  value: "manager_commercial",
                },
                {
                  label: "Agent",
                  value: "agent",
                },
                {
                  label: "Manager",
                  value: "manager",
                },
                {
                  label: "Client",
                  value: "client",
                },
              ]}
            />
          ) : (
            <p className="font-semibold">{role?.label}</p>
          )}

          <div className="flex justify-end">
            {updateRole ? (
              <a onClick={() => setUpdateRole(false)}>submit</a>
            ) : (
              <a onClick={() => setUpdateRole(true)}>Update</a>
            )}
          </div>
        </div>
        <Divider />
        <div className="grid grid-cols-3 text-sm font-medium ">
          <p className="text-slate-600">Department</p>

          <p className="font-semibold">Dev</p>

          <div className="flex justify-end">
            <a>Update</a>
          </div>
        </div>
        <Divider />
        <div className="grid grid-cols-3 text-sm font-medium ">
          <p className="text-slate-600">Services</p>

          <p className="font-semibold">Service</p>

          <div className="flex justify-end">
            <a>Update</a>
          </div>
        </div>
        <Divider />
        <div className="grid grid-cols-3 text-sm font-medium ">
          <p className="text-slate-600">Teams</p>

          <p className="font-semibold">Default - Comunik pro</p>

          <div className="flex justify-end">
            <a>Update</a>
          </div>
        </div>
        {/* <Divider /> */}
      </div>
    </>
  );
};

export default UserGeneraleInfo;
