import { JitsiMeeting } from "@jitsi/react-sdk";
import React, {
  useCallback,
  useEffect,
  useRef,
  useState,
  useMemo,
} from "react";
import { useTranslation } from "react-i18next";
import {
  CloseOutlined,
  DownOutlined,
  LoginOutlined,
  VerticalAlignBottomOutlined,
  VerticalAlignTopOutlined,
} from "@ant-design/icons";
import { Button, Dropdown, Space, Spin, Typography } from "antd";
import { useDispatch, useSelector } from "react-redux";

import "./visio.css";
import {
  joinMeet,
  resizeVisio,
  setVisoParams,
  toggleVisio,
} from "../../../../new-redux/actions/visio.actions/createVisio";
import { useNavigate } from "react-router-dom";
import { toastNotification } from "../../../../components/ToastNotification";
import { URL_ENV } from "../../../..";
import { LogoutLink } from "pages/layouts/chat/utils/ConversationUtils";
import useTimer from "components/Timer";
import { handleReload } from "pages/tasks/helpers/handleCheck";

function VisioComponent({ name_room }) {
  const { user } = useSelector((state) => state.user);
  const userImg =
    user?.avatar &&
    `${URL_ENV?.REACT_APP_BASE_URL + URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL}${
      user?.avatar?.split("/")[user?.avatar.split("/").length - 1]
    }`;
  const { size, visioParams, isOpen } = useSelector((state) => state.visio);

  const navigate = useNavigate();
  const apiRef = useRef();
  const timer = useTimer(null, visioParams?.visioStartDate, false);
  const { i18n, t } = useTranslation("common");
  const [isTop, setTop] = useState(false);
  const [isLeft, setIsLeft] = useState(false);

  const grantedAccess = useMemo(() => {
    return [visioParams?.creatorId, visioParams?.owner_id, ...(visioParams?.participants || [])];
  }, [visioParams?.creatorId, visioParams?.owner_id, visioParams?.participants]);
  const dispatch = useDispatch();
  const handleJitsiIframeMeduim = useCallback(
    (iframeRef) => {
      iframeRef.style.height =
        visioParams?.moderator || grantedAccess.includes(user?.id)
          ? !visioParams?.external
            ? "85.5vh"
            : "90vh"
          : "0";
      iframeRef.style.width = "100%";
      iframeRef.style.backgroundColor = "black";

      iframeRef.style.visibility =
        visioParams?.moderator || !visioParams?.external ? "visible" : "hidden";
    },
    [visioParams?.moderator, visioParams?.owner_id, user?.id, grantedAccess]
  );
  const handleJitsiIFrameSmall = useCallback(
    (iframeRef) => {
      iframeRef.style.paddingTop = "0px";
      iframeRef.style.height =
        visioParams?.moderator || grantedAccess.includes(user?.id)
          ? "35vh"
          : "0vh";
      iframeRef.style.width =
        visioParams?.moderator || grantedAccess.includes(user?.id)
          ? "35vh"
          : "0vh";
      iframeRef.style.visibility =
        visioParams?.moderator || grantedAccess.includes(user?.id)
          ? "visible"
          : "hidden";

      iframeRef.style.backgroundColor = "black";
    },
    [user?.id, visioParams?.moderator, grantedAccess]
  );
  const handleJitsiIFrameLarge = useCallback(
    (iframeRef) => {
      iframeRef.style.height =
        visioParams?.moderator || grantedAccess.includes(user?.id)
          ? "100vh"
          : "0";
      iframeRef.style.width = "100%";
      iframeRef.style.backgroundColor = "black";

      iframeRef.style.visibility =
        visioParams?.moderator || !visioParams?.external ? "visible" : "hidden";
    },
    [visioParams?.moderator, user?.id, visioParams?.external, grantedAccess]
  );
  const handleApiReady = (apiObj) => {
    apiRef.current = apiObj;
    if (!apiRef.current) return;

    apiRef.current.on("videoConferenceJoined", (e) => {
      if (grantedAccess.includes(user?.id))
        apiRef.current.executeCommand("grantModerator", e.id);
      dispatch(
        joinMeet({
          external: visioParams?.external,
          roomName: e.roomName,
          user_id: e.id,
          user_name: e.displayName,
          errorText: t("toasts.errorFetchApi"),
        })
      );
    });

    apiRef.current.on("videoConferenceLeft", (e) => {});
    apiRef.current.on("participantRoleChanged", function (event) {
      if (event.role === "moderator") {
        apiRef.current.executeCommand("toggleLobby", true);
      }
    });
  };

  const handleReadyToClose = () => {
    !visioParams?.external &&
      toastNotification(
        "success",
        t("chat.header.visio.visio-leave"),
        "topRight",
        5
      );

    dispatch(toggleVisio(false));
    dispatch(
      setVisoParams({
        moderator: false,
        owner_id: "",
        token: "",
        name: "",
        id_visio: null,
        creatorId: "",
        visioStartDate: null,
      })
    );
    apiRef.current?.executeCommand("hangup");
  };
  useEffect(() => {
    const iFrameObject = document.querySelector('[id*="jitsiMeeting-"]');

    if (iFrameObject) {
      if (size === "large" || visioParams?.external)
        handleJitsiIFrameLarge(iFrameObject);
      else if (size === "medium") handleJitsiIframeMeduim(iFrameObject);
      else if (size === "small") handleJitsiIFrameSmall(iFrameObject);
    }
    if (localStorage.getItem("lastHref")) localStorage.removeItem("lastHref");
  }, [
    visioParams?.external,
    handleJitsiIframeMeduim,
    handleJitsiIFrameLarge,
    handleJitsiIFrameSmall,
    dispatch,
    size,
    navigate,
    user?.id,
    visioParams?.moderator,
  ]);

  // join meet while in wating rooom
  useEffect(() => {
    if (
      (visioParams?.external || !grantedAccess.includes(user?.id)) &&
      visioParams.name
    ) {
      dispatch(
        joinMeet({
          external: visioParams?.external,
          roomName: visioParams.name,
          user_id: null,
          user_name: user.name,
          errorText: t("toasts.errorFetchApi"),
        })
      );
    }
  }, [
    visioParams?.external,
    dispatch,
    t,
    user?.id,
    user.name,
    visioParams.name,
    grantedAccess,
  ]);
  useEffect(() => {
    document.documentElement.style.setProperty(
      "--hauteur",
      !visioParams?.external && size === "medium"
        ? "90vh"
        : size === "large"
        ? "100vh"
        : "97vh"
    );
    document.documentElement.style.setProperty(
      "--largeur",
      !visioParams?.external && size === "medium"
        ? "calc(100% - 360px)"
        : "100%"
    );
  }, [visioParams?.moderator, size, visioParams?.external, visioParams?.token]);
  const togglePosition = () => {
    setTop((p) => !p);
  };

  // const openFullscreen = () => {
  //   let elem = document.getElementById("visio-component");
  //   if (elem.requestFullscreen) {
  //     elem.requestFullscreen();
  //   } else if (elem.webkitRequestFullscreen) {
  //     /* Safari */
  //     elem.webkitRequestFullscreen();
  //   } else if (elem.msRequestFullscreen) {
  //     /* IE11 */
  //     elem.msRequestFullscreen();
  //   }
  //   dispatch(resizeVisio(""));
  // };

  const items = [
    {
      key: "1",
      label: (
        <span>
          30<span className="text-xs">%</span>
        </span>
      ),
      onClick: () => dispatch(resizeVisio("small")),
    },
    {
      key: "2",
      label: (
        <span>
          70<span className="text-xs">%</span>
        </span>
      ),
      onClick: () => dispatch(resizeVisio("medium")),
    },
    {
      key: "3",
      label: (
        <span>
          100<span className="text-xs">%</span>
        </span>
      ),
      onClick: () => dispatch(resizeVisio("large")),
    },
  ];

  console.warn({ timer });

  if (
    (!visioParams?.external && !isOpen) ||
    (user && user?.access && user?.access["visio"] === "0")
  ) {
    return null;
  }
  if (!isOpen && visioParams?.external)
    return (
      <div className="flex h-screen items-center justify-center bg-gray-300/80 ">
        <p className="text-xl md:text-3xl">
          {visioParams?.room_status === "closed" ? (
            <div className="flex flex-col">
              <div>
                {t("visio.roomClosed")} <span className="ml-1">🕐</span>{" "}
              </div>
              {timer !== "00:00" ? (
                <div>
                  <span className="text-lg">
                    {t("visio.meetStartsIn")} {timer}
                  </span>
                </div>
              ) : null}
              <div className="mt-4 flex justify-center">
                <Button
                  type="primary"
                  onClick={handleReload}
                  disabled={timer !== "00:00"}
                >
                  {t("visio.participateMeet")}
                </Button>
              </div>
            </div>
          ) : visioParams?.room_status == "expired" ? (
            <>
              {t("visio.roomExpired")} <span className="ml-1">⏰</span>
            </>
          ) : (
            <>
              {t("chat.header.visio.visio-leave")}{" "}
              <span className="ml-1">👋</span>
            </>
          )}
        </p>
      </div>
    );
  return (
    <div
      onClick={() => {
        if (size === "medium" && !visioParams?.external) {
          dispatch(resizeVisio("small"));
        }
      }}
      id="visio-component"
      className={` ${
        size !== "small"
          ? `${
              !visioParams?.external
                ? size === "large"
                  ? "inset-x-0 "
                  : "inset-x-7"
                : "inset-x-2"
            } fixed  inset-y-0 flex flex-row-reverse `
          : `minimized ${isTop ? "top-0" : "bottom-[2%]"} ${
              isLeft ? "left-20" : ""
            }`
      } 
      ${
        size === "large"
          ? "left-16 z-[1007]"
          : size !== ""
          ? "z-[1006]"
          : "z-[1005]"
      }
       flex flex-1   ${
         !visioParams?.external
           ? size === "large"
             ? "p-0"
             : "py-10 pl-12"
           : " py-2"
       } `}
    >
      <div
        className={`visio-box   flex flex-col ${
          size === "large" ? " rounded-none" : "rounded-2xl"
        }     transition-all delay-75 duration-100  ${
          size === "large"
            ? "left-16 z-[1007]"
            : size === "small"
            ? ` minimized z-[1006] ${isTop ? "top-[2%]" : "bottom-[2%]"} ${
                isLeft ? "left-20" : ""
              }`
            : "z-[1006]"
        }`}
      >
        <header
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
          }}
          className={`flex  justify-between ${
            size === "large" ? "" : "rounded-t-2xl"
          } bg-black p-2.5`}
        >
          <Typography.Title
            ellipsis
            level={4}
            style={{
              color: "white",
            }}
          >
            {" "}
            {`${size !== "small" ? `${t("visio.room_name")} :` : ""} ${
              visioParams.visio_name
            }`}
            {/* {t("visio.room_name")}: {visioParams.visio_name} */}
          </Typography.Title>

          <div className="flex flex-1  items-center justify-end space-x-1.5">
            {!visioParams?.external ? (
              <>
                {size === "small" && (
                  <Space size={3}>
                    <Button
                      type="default"
                      size="small"
                      className="p-0.5"
                      shape="circle"
                      icon={
                        <VerticalAlignTopOutlined
                          style={{ fontSize: 15 }}
                          rotate={isLeft ? 90 : 270}
                        />
                      }
                      onClick={() => setIsLeft((p) => !p)}
                    />
                    <Button
                      type="default"
                      size="small"
                      className="p-0.5"
                      shape="circle"
                      icon={
                        !isTop ? (
                          <VerticalAlignTopOutlined style={{ fontSize: 15 }} />
                        ) : (
                          <VerticalAlignBottomOutlined
                            style={{ fontSize: 15 }}
                          />
                        )
                      }
                      onClick={togglePosition}
                    />
                  </Space>
                )}
                <Dropdown
                  placement="bottomLeft"
                  className="z-[2005]"
                  rootClassName="z-[2001]"
                  menu={{
                    items,
                    selectable: true,
                    className: "space-y-0.5",
                    selectedKeys: [
                      size === "small" ? "1" : size === "medium" ? "2" : "3",
                    ],
                  }}
                >
                  <Button
                    type="default"
                    size="small"
                    // className={`   p-0.5`}
                    shape="round"
                  >
                    <Space size={2}>
                      <span>
                        {size === "small"
                          ? "30"
                          : size === "medium"
                          ? "70"
                          : "100"}
                        <span className="text-xs">%</span>
                      </span>
                      <DownOutlined style={{ fontSize: 12 }} />
                    </Space>
                  </Button>
                </Dropdown>

                <Button
                  size="small"
                  type="default"
                  shape="circle"
                  className="p-0.5"
                  icon={<CloseOutlined />}
                  onClick={handleReadyToClose}
                />
              </>
            ) : (
              <Button
                shape="round"
                onClick={() => {
                  const urlLast = new URL(URL_ENV?.REACT_APP_DOMAIN);
                  urlLast.searchParams.set(
                    "room_visio_name",
                    name_room ?? visioParams?.name
                  );
                  localStorage.setItem("lastHref", urlLast.toString());
                  window.location.href = LogoutLink(true);
                }}
                icon={<LoginOutlined />}
                className=" flex cursor-pointer items-center justify-center p-2  "
              >
                <Typography.Title level={5}>
                  {t("login.login")}
                </Typography.Title>
              </Button>
            )}
          </div>
        </header>
        {/* ||
        visioParams?.creatorId !== user?.id */}
        {!grantedAccess.includes(user?.id) && !visioParams?.moderator && (
          <div
            onClick={(e) => e.stopPropagation()}
            className={`${
              size === "small" ? "flex-col " : "flex-col md:flex-row"
            } flex h-full  w-full items-center justify-center space-x-6 space-y-2 bg-gray-700 text-white`}
          >
            <Spin size="large" />
            <p
              className={`
            ${size === "small" ? "text-xl" : "text-3xl"} text-center
            `}
            >
              {t("visio.waitModerator")}
            </p>
          </div>
        )}

        {visioParams.token && (
          <JitsiMeeting
            domain={URL_ENV?.REACT_APP_VISIO_DOMAIN}
            roomName={name_room ?? visioParams?.name}
            configOverwrite={{
              toolbarButtons: [
                "camera",
                "chat",
                "closedcaptions",
                "desktop",
                "embedmeeting",
                "etherpad",

                "filmstrip",
                "fullscreen",
                "hangup",
                "highlight",
                "livestreaming",
                "microphone",
                "noisesuppression",
                "raisehand",
                "recording",
                "security",
                "select-background",
                "settings",
                "invite",
                "shareaudio",
                "sharedvideo",
                "shortcuts",
                "tileview",
                "toggle-camera",
                "videoquality",
                "whiteboard",
              ],
              hiddenPremeetingButtons: !visioParams?.external ? ["invite"] : [],

              startWithAudioMuted: false,
              startScreenSharing: false,
              enableEmailInStats: true,
              startWithVideoMuted: true,
              lobby: {
                autoKnock: false,
                enableChat: true,
              },
              securityUi: {
                hideLobbyButton: false,
              },
            }}
            userInfo={
              visioParams?.external
                ? null
                : user?.id && {
                    displayName: user?.name,
                    email: user?.email,
                    avatar: userImg,
                  }
            }
            gravatar={{ disabled: true }}
            jwt={visioParams.token}
            interfaceConfigOverwrite={{
              SHARING_FEATURES: [""],
              lobby: { autoKnock: true, enableChat: false },
              CLOSE_PAGE_GUEST_HINT: true,
              filmStripOnly: false,
              DISABLE_JOIN_LEAVE_NOTIFICATIONS: true,
            }}
            spinner={() => (
              <div className="flex h-full items-center justify-center bg-black">
                <Spin size="large" />;
              </div>
            )}
            lang={i18n.language}
            onApiReady={(externalApi) => handleApiReady(externalApi)}
            onReadyToClose={handleReadyToClose}
          />
        )}
      </div>
    </div>
  );
}

export default VisioComponent;
