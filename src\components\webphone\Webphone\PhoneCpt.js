import React, {
  useState,
  useEffect,
  useCallback,
  lazy,
  Suspense,
  memo,
  useRef,
} from "react";
import { useDispatch, useSelector } from "react-redux";
import { toastNotification } from "../../ToastNotification.js";

import { getInternalForward } from "../../../new-redux/actions/voip.actions/getInternalForward.js";
import MainService from "../../../services/main.service.js";
import useNetwork from "../../../custom-hooks/useNetwork.js";
import SIP, { Session } from "sip.js";

import {
  //  start_video_call,
  stop_call,
  updateCallStatus,
} from "../../../new-redux/actions/voip.actions/callIPBX.js";
import { Card, Badge, Button, Drawer, Result, Skeleton, Spin } from "antd";
import {
  CloseOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
} from "@ant-design/icons";

import { useTranslation } from "react-i18next";
import { getTags } from "../../../pages/voip/services/services.js";
import { webPhoneActions } from "./components/webPhoneActions.js";
import { uuid } from "../../../pages/layouts/chat/utils/ConversationUtils.js";
import { useSendMessage } from "../../../pages/layouts/chat/hooks/useSendMessage.js";
import { URL_ENV } from "index.js";
import { store } from "new-redux/store.js";
import {
  RESET_LAST_CALL_LOG,
  SET_FORWARDING_CALL,
  SET_RECEIVER_INFO,
} from "new-redux/constants/index.js";
// import { getUser } from "new-redux/actions/voip.actions/getUser.js";

import TagContent from "../../../pages/voip/components/tag_column/TagContent.js";
import { lazyRetry } from "utils/lazyRetry.js";
import { LoaderDrawer } from "pages/layouts/chat/index.js";
import { openDrawerChat } from "new-redux/actions/voip.actions/handleDrawerChat.js";
import FormCreate from "pages/clients&users/components/FormCreate.js";
import { useNavigate } from "react-router-dom";
import { generateUrlToView360 } from "pages/voip/helpers/helpersFunc.js";
import Drawer360 from "pages/voip/components/Drawer360.js";
import { checkShowNotification } from "utils/real-time-function/chat/function.js";
import CreateTask from "pages/voip/components/CreateTask.js";
import DisplayElementInfo from "pages/voip/components/DisplayElementInfo.js";
import { getIpAndDeclareIt } from "new-redux/actions/voip.actions/declareAndGetIP.js";
import "./index.css";
import { HiOutlineVideoCamera, HiPhone } from "react-icons/hi2";
import { handleConfEvents } from "./components/handleConfEvents.js";
import { Refs_IDs } from "components/tour/tourConfig.js";

const Main = lazy(() => lazyRetry(() => import("./main/main.js"), "Main"));
// const Dash = lazy(() =>
//   lazyRetry(() => import("./webphoneDown/Dash.js"), "Dash")
// );

const Callpage = lazy(() =>
  lazyRetry(() => import("./callPage/Callpage.js"), "Callpage")
);
const CallPageDown = lazy(() =>
  lazyRetry(() => import("./callPage/callPageDown.js"), "CallPageDown")
);

var Phone = {
  config: {},
};
var oldNumber;
var notifAppel = 0;
var notifAppelFromWB = 0;
const incoming = new Audio("/sounds/ring.ogg");
incoming.loop = true;
const secondIncoming = new Audio("/sounds/second_ring.ogg");
secondIncoming.loop = true;
const outgoing = new Audio("/sounds/ringback.ogg");
outgoing.loop = true;

const fin = new Audio("/sounds/callend.ogg");
var waitingForConferenceCall = false;
var conferenceTargetId = false;
var tab = [];
var allSession = [];

//decrypt param webphone : cesar 3
export function decryptC(text, shift = 3) {
  let result = "";

  const textLength = text.length;

  for (let i = 0; i < textLength; i++) {
    let char = text[i];

    if (/[a-zA-Z]/.test(char)) {
      let caseValue = /[A-Z]/.test(char) ? 65 : 97;

      result += String.fromCharCode(
        ((((char.charCodeAt(0) - shift - caseValue + 260) % 26) + 26) % 26) +
          caseValue
      );
    } else if (/\d/.test(char)) {
      result += String.fromCharCode(
        ((((char.charCodeAt(0) - shift - 48 + 260) % 10) + 10) % 10) + 48
      );
    } else {
      result += char;
    }
  }

  return result;
}

// const getCurrentTime = () => {
//   const now = new Date();
//   const hours = String(now.getHours()).padStart(2, "0");
//   const minutes = String(now.getMinutes()).padStart(2, "0");
//   const seconds = String(now.getSeconds()).padStart(2, "0");
//   return `${hours}:${minutes}:${seconds}`;
// };

function mix(MultiAudioTrackStream) {
  var audioContext = null;
  try {
    window.AudioContext = window.AudioContext || window.webkitAudioContext;
    audioContext = new AudioContext();
  } catch (e) {
    console.error("AudioContext() not available, cannot record");
    return MultiAudioTrackStream;
  }
  var mixedAudioStream = audioContext.createMediaStreamDestination();
  MultiAudioTrackStream.getAudioTracks().forEach(function (audioTrack) {
    var srcStream = new MediaStream();
    srcStream.addTrack(audioTrack);
    var streamSourceNode = audioContext.createMediaStreamSource(srcStream);
    streamSourceNode.connect(mixedAudioStream);
  });
  return mixedAudioStream.stream;
}

function PhoneCpt({ cmkPhone, status, stopReconnectTone }) {
  const dispatch = useDispatch();
  const navigateTo = useNavigate();
  const isConf = useRef(false);
  const confInfo = useRef(null);
  const forwardingCall = useRef(null);
  const { isOnline } = useNetwork();
  // const { isServerReachable } = useNetworkStatus();
  const eventMercure = useSelector((state) => state.ChatRealTime.eventMercure);
  forwardingCall.current = useSelector(
    (state) => state.voipBlackList.forwardingCall
  );
  confInfo.current = useSelector((state) => state.voipBlackList.confInfo);

  const [t] = useTranslation("common");

  // save all  calls
  const [session, setSession] = useState([]);
  // the active call
  const [activecall, setActivecall] = useState(null);
  // holdcall
  const [holdCall, setHoldCall] = useState("#7d7d7d85");
  // muteCall
  const [muteCall, setMuteCall] = useState("#7d7d7d85");
  const [backHome, setBackHome] = useState("");
  const [upWebphone, setUpWebphone] = useState(false);
  const [webphoneDown, setWebphoneDown] = useState(false);
  const [navigate, setNavigate] = useState("history");
  const [openTag, setOpenTag] = useState(false);
  const [drawerTagHeight, setDrawerTagHeight] = useState(45);

  const [allTags, setAllTags] = useState([]);
  const [openChatDrawer, setOpenChatDrawer] = useState(false);
  const [isKeyPadUp, setIsKeyPadUp] = useState(false);

  const [openTask, setOpenTask] = useState(false);
  const [elementDetails, setElementDetails] = useState({});
  const [openDrawerInfo, setOpenDrawerInfo] = useState(false);

  const [isCreateDrawerOpen, setIsCreateDrawerOpen] = useState(false);
  const [familyCreationInfo, setFamilyCreationInfo] = useState(null);
  const [isCreationDone, setIsCreationDone] = useState(false);
  const [isSiderLayoutDrawerOpen, setIsSiderLayoutDrawerOpen] = useState(false);

  // const [formTask] = Form.useForm();
  const posteVoip = useSelector((state) => state.user.user?.extension);
  const outputDeviceId = useSelector((state) => state.voip.outputDeviceId);

  const param = useSelector((state) => state.voip.param);
  const internalforward = useSelector((state) => state.voip.internalforward);
  const callIPBX = useSelector((state) => state.voipBlackList.callIPBX);
  const currentIP = useSelector((state) => state.voip.addressIp);
  // const cmkPhone = useSelector((state) => state.voipBlackList.cmkPhone);
  // const callVideoIPBX = useSelector(
  //   (state) => state.voipBlackList.callVideoIPBX
  // );
  const colleagues = useSelector((state) => state.voip.collegues);
  // const journalTab = useSelector((state) => state.voip.logs);
  const receiverInfo = useSelector((state) => state.voipBlackList.receiverInfo);
  const elementInfo = useSelector(
    (state) => state?.contacts?.contactHeaderInfo
  );
  // const loadingJournalTab = useSelector((state) => state.voip.loading);
  const lastCallLog = useSelector((state) => state.voip.lastCallLog);

  const { nbrVoiceMessaging, nbrMissedCalls } = useSelector(({ voip }) => voip);
  const { userList } = useSelector((state) => state.chat);

  const extraTokenHeader = [
    `X-Custom-Header: ${decryptC(param?.param_9 ?? "")}`,
  ];

  // console.log({ session, activecall });

  const fetchTags = useCallback(async () => {
    try {
      const tags = await getTags();
      setAllTags(tags);
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? err.message : err);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    fetchTags();
  }, [fetchTags]);

  const onClose = () => {
    setOpenTag(false);
    dispatch({ type: RESET_LAST_CALL_LOG });
  };
  //
  // handle the open and close of the chat drawer
  const handleOpenChatDrawer = (uuid) => {
    dispatch(openDrawerChat(uuid));
    // handleMsgClick(dispatch, setOpenChatDrawer, userList, {
    //   uuid: uuid,
    // });
  };

  const { mutate: handleActionMessage } = useSendMessage("new_message");
  useEffect(() => {
    !openTag && setDrawerTagHeight(45);
  }, [openTag]);

  async function sendMessageAndHungUP(session, call, message) {
    let time;
    // TODO : GET user from api
    const receiver = userList.find(
      (item) => Number(item.post_number) === Number(call)
    );
    clearTimeout(time);

    hungUp(session);
    time = setTimeout(() => {
      !!receiver &&
        handleActionMessage({
          params: {
            selectedConversation: receiver,
            message: message?.trim(),
            file: [],
            taggedPerson: "",
            from: false,
            main_message: null,
            webPhone: true,
          },
          type_conversation: "user",
          type_action: "new_message",
        });
    }, 1000);
  }

  const declareIP = useCallback(async () => {
    if (isOnline && eventMercure?.readyState === 1)
      dispatch(getIpAndDeclareIt(posteVoip, param, currentIP));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [eventMercure?.readyState, isOnline]);

  useEffect(() => {
    declareIP();
  }, [declareIP]);

  const checkMicrophonePermission = async () => {
    const constraints = {
      audio: true,
      video: false,
    };
    try {
      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      if (stream) {
        stream.getTracks().forEach(function (track) {
          track.stop();
        });
        return true;
      }
    } catch (error) {
      return false;
    }
  };

  const fetchParam = useCallback(() => {
    if (decryptC(param?.param_5 || "") === "true") {
      dispatch(getInternalForward(posteVoip));
    } else document.documentElement.style.setProperty("--heightIPBX", "205px");
  }, [posteVoip, param?.param_5, dispatch]);

  // const fetchLogs = useCallback(() => {
  //   dispatch(getLogs());
  // }, []);

  useEffect(() => {
    if (posteVoip !== undefined) fetchParam();
  }, [fetchParam, posteVoip]);

  //
  const setTheReceiverInfo = (resp, s, receiverNumber) => {
    const receiverInfoPayload = {
      name: resp.data.length === 0 ? null : resp.data[0]?.name ?? "",
      image:
        resp.data.length === 0
          ? null
          : resp.data[0]?.image?.[0]
          ? `${
              URL_ENV?.REACT_APP_BASE_URL + URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
            }${resp.data[0]?.image?.[0]}`
          : null,
      extension: resp.data[0]?.extension,
      id: resp.data[0]?.id,
      uid: resp.data[0]?.uid ?? null,
      phones: resp.data[0]?.phone?.length ? resp.data[0]?.phone : null,
      number: s ? s.remoteIdentity?.uri?.user : receiverNumber,
      email: resp.data[0]?.email ? [resp.data[0]?.email] : null,
      type: resp.data[0]?.type,
      source: resp.data[0]?.source,
      family_id: resp.data[0]?.family_id,
    };
    dispatch({
      type: SET_RECEIVER_INFO,
      payload: receiverInfoPayload,
    });
    return receiverInfoPayload;
  };
  //

  function pauseAudio(sound, playFin = false, withouCondition = false) {
    if (session.length <= 2 || withouCondition) {
      sound.pause();
      sound.src = "";
    }
    if (playFin) fin.play();
  }

  function specialCharacters(number) {
    number = String(number);
    // if (number.charAt(0) === "+") {
    //   number = "00" + number.substr(1);
    // }
    return number
      .replace(/ /g, "")
      .replace(/[`~!@$%^&()_|\-=?;:'",.<>\{\}\[\]\\\/]/gi, "")
      .replace(/[a-z]/gi, "");
  }

  // merge session (conference)
  function sipMergeSessions(sessionId, targetSessionId) {
    var current = sessionId;
    var target = targetSessionId;
    var currentRtpReceivers =
      current.mediaHandler.peerConnection.getReceivers();
    var currentRtpSenders = current.mediaHandler.peerConnection.getSenders();
    var targetRtpReceivers = target.mediaHandler.peerConnection.getReceivers();
    var targetRtpSenders = target.mediaHandler.peerConnection.getSenders();

    var targetOutputStream = new MediaStream();
    var currentOutputStream = new MediaStream();
    currentRtpReceivers.forEach(function (RTPReceiver) {
      if (RTPReceiver.track && RTPReceiver.track.kind == "audio") {
        targetOutputStream.addTrack(RTPReceiver.track);
      }
    });

    targetRtpReceivers.forEach(function (RTPReceiver) {
      if (RTPReceiver.track && RTPReceiver.track.kind == "audio") {
        currentOutputStream.addTrack(RTPReceiver.track);
      }
    });

    targetRtpSenders.forEach(function (RTPSender) {
      if (RTPSender.track && RTPSender.track.kind == "audio") {
        target.data.AudioSourceTrack = RTPSender.track;
        targetOutputStream.addTrack(RTPSender.track);
        var mixedAudioTracks = mix(targetOutputStream).getAudioTracks()[0];
        mixedAudioTracks.isMixedTrack = true;
        RTPSender.replaceTrack(mixedAudioTracks);
      }
    });

    currentRtpSenders.forEach(function (RTPSender) {
      if (RTPSender.track && RTPSender.track.kind == "audio") {
        current.data.AudioSourceTrack = RTPSender.track;
        currentOutputStream.addTrack(RTPSender.track);
        var mixedAudioTracks = mix(currentOutputStream).getAudioTracks()[0];
        mixedAudioTracks.isMixedTrack = true;
        RTPSender.replaceTrack(mixedAudioTracks);
      }
    });
  }

  const cancelEchecAppel = () => {
    dispatch(stop_call());
    //dispatch(stop_video_call());
  };
  //create conf
  const ConferenceCall = async (number) => {
    try {
      if (number !== "" && number !== posteVoip) {
        setBackHome("");
        waitingForConferenceCall = true;
        conferenceTargetId = activecall;
        let resp;
        try {
          resp = await MainService.numberCallApiIPBX(specialCharacters(number));
        } catch (e) {
          resp = { data: [] };
        }
        var s = cmkPhone?.invite(number, {
          extraHeaders: extraTokenHeader,
          media: {
            stream: Phone.Stream,
            constraints: { audio: true, video: false },
            render: {
              remote: new Audio(),
            },
            RTCConstraints: { optional: [{ DtlsSrtpKeyAgreement: "true" }] },
          },
        });
        s.established = false;
        s.conf = "inviter";
        const receiverInfo = setTheReceiverInfo(resp, s);
        s.receiverInfo = receiverInfo;

        setActivecall((prev) => {
          prev.conf = s;
          return prev;
        });
        s.direction = "outgoing";
        s.statusAppel = "ringing";
        s.uuid = uuid();

        pushToSession(s);
        s.on("progress", function (e) {
          s.established = true;
          // add api to tell the other receiver there are a conf call
          outgoing.src = "/sounds/ringback.ogg";
          outgoing.play();
        });

        s.on("accepted", function (e) {
          pauseAudio(outgoing, false, true);

          s.statusAppel = "accepted";
          s.date = new Date().getTime();
          if (waitingForConferenceCall) {
            sipMergeSessions(s, conferenceTargetId);
            waitingForConferenceCall = false;
            conferenceTargetId = false;
          }
          isConf.current = true;
        });
        s.on("bye", function (e) {
          pauseAudio(outgoing, true, true);

          filterSession(s, true);

          dispatch(stop_call());
        });
        s.on("rejected", function (e) {
          pauseAudio(outgoing, false, false);

          filterSession(s, true);

          dispatch(stop_call());
        });
        s.on("cancel", function (e) {
          try {
            pauseAudio(outgoing, true, true);

            filterSession(s, true);

            dispatch(stop_call());
          } catch (error) {
            return;
          }
        });
        s.on("failed", function (e) {
          try {
            pauseAudio(outgoing, true, true);

            filterSession(s, true);

            dispatch(stop_call());

            //dispatch(stop_video_call());
          } catch (error) {
            dispatch(stop_call());

            return;
          }
        });
      }
    } catch (error) {}
  };
  //
  // hold session
  const holdSession = (session) => {
    if (session?.isOnHold().local === true) {
      session?.unhold();
      session.statusAppel = "answered";
      setHoldCall("#7d7d7d85");
    } else {
      session?.hold();
      session.statusAppel = "holding";
      setHoldCall("white");
    }
  };
  // To make conf When user is already in two calls
  const mergeCallsToConference = async () => {
    // console.log({ activecall, session, s });
    if (activecall && session.length > 1) {
      var target = activecall;
      var newSession = allSession.find((s) => s.uuid !== target.uuid);
      newSession.conf = "inviter";
      setActivecall((prev) => {
        prev.conf = newSession;
        return prev;
      });
      newSession.direction = "outgoing";
      newSession.statusAppel = "accepted";
      newSession.unhold();
      // holdSession(newSession);

      var newSessions = [target, newSession];

      sipMergeSessions(newSession, activecall);
      allSession = [...newSessions];
      setSession(newSessions);
      isConf.current = true;
    }
  };
  //
  //refer call
  const transfertCall = async (target, item) => {
    try {
      if (target !== "" && target !== posteVoip) {
        const formData = new FormData();
        formData.append("src", posteVoip);
        formData.append("dst", activecall?.remoteIdentity?.uri?.user);
        formData.append("src_forwarding", posteVoip);
        formData.append("dst_forwarding", target);
        formData.append("dst_forwarding_name", item.name);
        formData.append("dst_forwarding_image", item.image);
        const url = new URL(URL_ENV.REACT_APP_VOIP_URL);

        let referredBy = new SIP.URI("sip", posteVoip, url.hostname, 5060);

        activecall?.refer(target, {
          extraHeaders: [
            `Referred-By: <${referredBy?.toString()}>`,
            ...extraTokenHeader,
          ],
        });
        filterSession(activecall);

        MainService.blindForwardingIPBX(formData);
      }
    } catch (error) {
      console.log("error", error);
    }
  };
  //
  // Transfer attended
  const attendedTransfer = (target, destSession) => {
    try {
      // console.log({ activecall, session, allSession, target, dest });
      session?.[0].refer(destSession);
      allSession.forEach((s) => hungUp(s));
      setActivecall(null);
      allSession = [];
      setSession([]);
      // if (target !== "") {
      //   target.refer(dest);
      //   const time = setTimeout(() => {
      //     dispatch({
      //       type: SET_FORWARDING_CALL,
      //       payload: null,
      //     });
      //     dispatch({
      //       type: SET_RECEIVER_INFO,
      //       payload: null,
      //     });
      //     clearTimeout(time);
      //   }, 1);
      // }
    } catch (error) {
      console.log(error);
      return;
    }
  };
  //

  //mute session
  const muteSession = (session) => {
    if (session.ismute === "yes") {
      session.unmute();
      session.ismute = "no";
      setMuteCall("#7d7d7d85");
    } else {
      session.mute();
      session.ismute = "yes";
      setMuteCall("white");
    }
  };
  //hungup call
  const hungUp = (session) => {
    if (!session || typeof session !== "object") {
      console.log("Invalid session:", session);
      return;
    }

    try {
      session.selfCancel = false;
      if (session.statusAppel !== "ringing") {
        session.bye && session.bye();
      } else if (typeof session.reject === "function") {
        session.reject();
      } else if (typeof session.cancel === "function") {
        session.cancel();
      }
    } catch (error) {
      console.log("error", error);
      if (error.code === 2) {
        dispatch(stop_call());
        pauseAudio(incoming, false);
        filterSession(session);
      } else if (activecall?.conf) {
        setActivecall((prev) => {
          prev.conf = null;
          return prev;
        });
        filterSession(session);
      }
    }
  };

  //up audio call
  const upBtn = async (session) => {
    try {
      const isMicAcitve = await checkMicrophonePermission();

      if (!isMicAcitve) {
        toastNotification(
          "error",
          t("webphone.mustAllowMicrophonePermission"),
          "topLeft",
          0
        );
      } else {
        makeSessionHold();

        session.date = new Date().getTime();

        await session.accept({
          media: {
            constraints: { audio: true, video: false },
            render: {
              remote: new Audio(),
            },
            RTCConstraints: { optional: [{ DtlsSrtpKeyAgreement: "true" }] },
          },
        });
        session.statusAppel = "accepted";
      }
    } catch (error) {
      console.log({ error });
      //  filterSession(session);
    }
  };
  //up video call
  const upBtnVideo = async (session) => {
    document.getElementById("remoteVideo").style.display = "block";
    document.getElementById("localVideo").style.display = "block";

    makeSessionHold();

    await session.accept({
      media: {
        constraints: { audio: true, video: true },
        render: {
          remote: document.getElementById("remoteVideo"),
          local: document.getElementById("localVideo"),
        },
        RTCConstraints: { optional: [{ DtlsSrtpKeyAgreement: "true" }] },
      },
    });
    session.statusAppel = "accepted";

    const time = setTimeout(() => {
      session.hold();
      session.unhold();
      clearTimeout(time);
    }, 1000);

    session.date = new Date().getTime();
  };
  //send dtmf
  const sendDTMF = (session, digit) => {
    if (session) {
      session.dtmf(digit);
    }
  };
  //change session (2 calss or +)
  const changeSession = (session) => {
    if (activecall !== "") {
      activecall.hold();
      activecall.statusAppel = "holding";
      session.unhold();
      session.statusAppel = "answered";
      setActivecall(session);
    }
  };
  const closeBackHome = (e) => {
    setBackHome("");
  };
  const openBackHome = (e) => {
    setBackHome("newCall");
  };
  const openBackHomeConference = (e) => {
    setBackHome("conference");
  };
  /**
   * 
   * filterSession : filtrer la session de la liste
   * "s": session pour la filtrer de la liste
   * "extra": reset conf call
  
   * @param {any} s
   */
  const filterSession = useCallback(
    (s, extra = false) => {
      let sessionArray = [...allSession];

      if (extra) {
        sessionArray = sessionArray.map((item) => {
          if (item.conf) {
            item.conf = null;
          }
          return item;
        });
      }

      sessionArray = sessionArray.filter((item) => item?.uuid !== s?.uuid);
      if (
        document.getElementById("remoteVideo") &&
        document.getElementById("localVideo")
      ) {
        document.getElementById("remoteVideo").style.display = "none";
        document.getElementById("localVideo").style.display = "none";
      }

      // if (extra) {
      if (sessionArray.length !== 0) {
        if (sessionArray.length === 2) {
          setActivecall(sessionArray.at(0));
        } else setActivecall(sessionArray.at(-1));
      } else {
        setActivecall(null);
      }

      if (!sessionArray.find((item) => item.statusAppel === "accepted"))
        setWebphoneDown(false);
      allSession = [...sessionArray];
      setSession([...sessionArray]);
      if (s?.receiverInfo?.id === forwardingCall.current?.dst_id) {
        dispatch({
          type: SET_FORWARDING_CALL,
          payload: null,
        });
      }
      if (sessionArray.length === 0) {
        stopReconnectTone();
        const time = setTimeout(() => {
          dispatch({
            type: SET_FORWARDING_CALL,
            payload: null,
          });
          dispatch({
            type: SET_RECEIVER_INFO,
            payload: null,
          });
          return () => clearTimeout(time);
        }, 1);
      }
    },
    [dispatch]
  );

  const pushToSession = useCallback((s) => {
    let newArray = [...allSession];
    const existSession = newArray?.find(
      (element) =>
        element?.remoteIdentity?.uri?.user === s?.remoteIdentity?.uri?.user
    );

    if (existSession) return;
    newArray.push(s);
    allSession = [...newArray];
    setSession(newArray);
    if (allSession.length > 1) setWebphoneDown(false);
  }, []);

  const updateSession = useCallback((s) => {
    let array = [...allSession];
    array = array.map((item) => (item.uuid === s.uuid ? s : item));
    setSession(array);
    allSession = [...array];
  }, []);

  const makeSessionHold = useCallback(() => {
    if (allSession.length <= 1) return;
    let array = [...allSession];

    array = array.map((item) => {
      if (item.statusAppel === "accepted") {
        item.statusAppel = "holding";
        item.hold();
      }
      return item;
    });
    allSession = [...array];
    setSession([...array]);
  }, []);

  //  make video  call
  // const callClickVideo = async (number) => {
  //   try {
  //     const isMicAcitve = await checkMicrophonePermission();

  //     if (!isMicAcitve) {
  //       toastNotification(
  //         "error",
  //         t("webphone.mustAllowMicrophonePermission"),
  //         "topLeft",
  //         0
  //       );
  //       dispatch(start_video_call(""));
  //     } else {
  //       if (number !== "" && number !== posteVoip) {
  //         var s = cmkPhone?.invite(specialCharacters(number), {
  //           media: {
  //             stream: Phone.Stream,
  //             constraints: { audio: true, video: true },
  //             render: {
  //               remote: document.getElementById("remoteVideo"),
  //               local: document.getElementById("localVideo"),
  //             },
  //             RTCConstraints: { optional: [{ DtlsSrtpKeyAgreement: "true" }] },
  //           },
  //         });
  //         const resp = await MainService.numberCallApiIPBX(
  //           specialCharacters(number)
  //         );
  //         resp.data.length === 0
  //           ? (s.nameCall = "")
  //           : (s.nameCall = resp.data[0]?.name ?? "");
  //         resp.data.length === 0
  //           ? (s.imageCall = "")
  //           : (s.imageCall = resp.data[0]?.image ?? "");
  //         resp.data.length === 0
  //           ? (s.uuidCall = null)
  //           : (s.uuidCall = resp.data[0]?.uid ?? null);
  //         s.type = "video";
  //         s.conf = null;
  //         s.direction = "outgoing";
  //         s.ismute = "no";
  //         s.statusAppel = "ringing";
  //         s.uuid = uuid();
  //         pushToSession(s);

  //         s.on("progress", function (e) {
  //           outgoing.src = "/sounds/ringback.ogg";

  //           // outgoing.play();
  //           document.getElementById("remoteVideo").style.display = "block";
  //           document.getElementById("localVideo").style.display = "block";
  //         });
  //         s.on("accepted", function (e) {
  //           pauseAudio(outgoing, false, true);
  //           makeSessionHold();

  //           s.statusAppel = "accepted";

  //           s.date = new Date().getTime();
  //           setActivecall(s);
  //         });
  //         s.on("bye", function (e) {
  //           pauseAudio(outgoing, true, true);
  //           filterSession(s);

  //           //dispatch(stop_video_call());
  //         });
  //         s.on("rejected", function (e) {
  //           pauseAudio(outgoing, false, true);

  //           filterSession(s);

  //           //dispatch(stop_video_call());
  //         });
  //         s.on("cancel", function (e) {
  //           try {
  //             pauseAudio(outgoing, true, true);
  //             filterSession(s);

  //             //dispatch(stop_video_call());
  //           } catch (error) {
  //             return;
  //           }
  //         });

  //         s.on("failed", function (e) {
  //           try {
  //             pauseAudio(outgoing, false, true);

  //             filterSession(s);

  //             dispatch(stop_call());

  //             //dispatch(stop_video_call());
  //           } catch (error) {
  //             return;
  //           }
  //         });
  //       }
  //     }
  //   } catch (error) {}
  //   //clicktocall video
  // };
  useEffect(() => {
    const receiverCall = async (s) => {
      if (!isOnline || !cmkPhone) return;
      if (!session?.length)
        dispatch({
          type: SET_FORWARDING_CALL,
          payload: null,
        });
      if (session.length > 1 || !!confInfo.current) return;
      try {
        const existSession = allSession?.find(
          (element) =>
            element?.remoteIdentity?.uri?.user === s?.remoteIdentity?.uri?.user
        );
        // close drawer tag
        if (!existSession) {
          setOpenTag(false);
          setUpWebphone(true);

          const qualificationAfterCall = await store.getState().voip
            ?.internalforward?.qualification;

          s.direction = "incoming";
          s.statusAppel = "ringing";
          s.loading = true;
          s.established = true;
          onClose();

          if (
            s.mediaHandler.peerConnection.remoteDescription.sdp.indexOf(
              "video"
            ) === -1
          ) {
            s.type = "audio";
          } else {
            s.type = "video";
          }

          s.ismute = "no";
          s.conf = null;
          const sessionID = uuid();
          s.uuid = sessionID;
          try {
            if (outputDeviceId) {
              incoming.setSinkId(outputDeviceId);
              secondIncoming.setSinkId(outputDeviceId);
            }

            if (!session.length || session?.[0]?.statusAppel === "ringing") {
              incoming.src = "/sounds/ring.ogg";
              incoming.play();
            } else {
              secondIncoming.src = "/sounds/second_ring.ogg";
              secondIncoming.play();
            }
          } catch (e) {
            console.log(e);
          }

          pushToSession(s);

          s.on("accepted", function (e) {
            pauseAudio(incoming);
            pauseAudio(secondIncoming);

            s.statusAppel = "accepted";
            s.date = new Date().getTime();
            setActivecall(s);
          });
          s.on("bye", function (e) {
            if (qualificationAfterCall === "1") {
              setDrawerTagHeight(435);
              setOpenTag(true);
            } else if (qualificationAfterCall === "2") {
              setOpenTag(true);
            }

            pauseAudio(incoming, true);
            pauseAudio(secondIncoming, true);

            // if (s?.conf) {
            //   s?.conf?.conf = null; //s.conf.conf = null;
            // }

            filterSession(s, true);

            dispatch(stop_call());
            //dispatch(stop_video_call());
          });
          s.on("rejected", function (e) {
            notifAppelFromWB++;

            pauseAudio(incoming, false);
            pauseAudio(secondIncoming, false);

            // const conf = session?.find((sess) => session.conf?.conf);

            filterSession(s, true);

            dispatch(stop_call());

            //dispatch(stop_video_call());
          });

          s.on("cancel", function (e) {
            try {
              pauseAudio(incoming, true);
              pauseAudio(secondIncoming, true);

              filterSession(s, true);

              dispatch(stop_call());

              //dispatch(stop_video_call());
            } catch (error) {
              return;
            }
          });
          s.on("failed", function (e) {
            try {
              pauseAudio(incoming, false);
              pauseAudio(secondIncoming, false);

              filterSession(s, true);

              dispatch(stop_call());

              //dispatch(stop_video_call());
            } catch (error) {
              return;
            }
          });

          if (s.remoteIdentity?.uri?.user === "sphere_visio") {
            dispatch({
              type: SET_RECEIVER_INFO,
              payload: {
                name: "Sphere Visio",
                image: <HiOutlineVideoCamera />,
                extension: "sphere_visio",
              },
            });
            s.receiverInfo = {
              name: "Sphere Visio",
              image: <HiOutlineVideoCamera />,
              extension: "sphere_visio",
            };
            s.loading = false;
            updateSession(s);
            checkShowNotification({
              type: "call",
              conversation: null,
              navigate: () => {},
              notificationContent: {
                sender: {
                  image: <HiOutlineVideoCamera />,
                  name: "Sphere Visio",
                  // number: s.remoteIdentity?.uri?.user,
                },
              },
            });
          } else {
            MainService.numberCallApiIPBX(
              specialCharacters(s.remoteIdentity?.uri?.user)
            )
              .then((resp) => {
                const receiverInfo = setTheReceiverInfo(resp, s);
                s.receiverInfo = receiverInfo;
                s.loading = false;
                updateSession(s);

                checkShowNotification({
                  type: "call",
                  conversation: null,
                  navigate: () => {},
                  notificationContent: {
                    sender: {
                      image: receiverInfo?.image,
                      name: receiverInfo?.name,
                      number: s.remoteIdentity?.uri?.user,
                    },
                  },
                });
              })
              .catch((e) => {
                // a voir ?
                s.loading = false;
                updateSession(s);

                console.log("error", e);
              });
          }
        } else {
          dispatch(stop_call());
          //dispatch(stop_video_call());

          //  s.reject();
        }
      } catch (error) {
        console.log("error", error);
      }
    };

    cmkPhone?.on("invite", receiverCall);
    return () => {
      cmkPhone?.removeListener("invite", receiverCall);
    };
  }, [cmkPhone, outputDeviceId, session, isOnline]);

  // console.log({ session, activecall, allSession, confInfo });

  useEffect(() => {
    const callClick = async () => {
      if (!session?.length)
        dispatch({
          type: SET_FORWARDING_CALL,
          payload: null,
        });
      try {
        const isMicAcitve = await checkMicrophonePermission();

        if (!isMicAcitve) {
          toastNotification(
            "error",
            t("webphone.mustAllowMicrophonePermission"),
            "topLeft",
            0
          );
          dispatch(stop_call());
          return;
        } else {
          if (oldNumber === callIPBX?.post_numberR) return;

          setBackHome("");
          let resp;
          try {
            resp = await MainService.numberCallApiIPBX(
              specialCharacters(callIPBX?.post_numberR)
            );
          } catch (e) {
            resp = { data: [] };
          }

          let extraHeaders = [];
          const payload = { ...callIPBX };
          delete payload?.post_numberR;
          for (const key in payload) {
            if (payload[key])
              extraHeaders.push(
                "X-SPHERE-" +
                  key.toUpperCase().replace("_", "-") +
                  ": " +
                  payload[key]
              );
          }

          // console.log({ extraHeaders, payload });
          extraHeaders.push(...extraTokenHeader);

          const s = await cmkPhone?.invite(
            specialCharacters(callIPBX?.post_numberR),
            {
              extraHeaders,

              media: {
                // stream: Phone.Stream,
                constraints: { audio: true, video: false },
                render: {
                  remote: new Audio(),
                },
                RTCConstraints: {
                  optional: [{ DtlsSrtpKeyAgreement: "true" }],
                },
              },
            }
          );
          const qualificationAfterCall = await store.getState().voip
            ?.internalforward?.qualification;
          const sessionID = uuid();
          const receiverInfo = setTheReceiverInfo(resp, s);
          s.receiverInfo = receiverInfo;

          s.conf = null;
          s.direction = "outgoing";
          s.selfCancel = true;
          s.ismute = "no";
          s.statusAppel = "ringing";
          s.uuid = sessionID;
          s.established = false;
          oldNumber = callIPBX?.post_numberR;
          pushToSession(s);

          onClose();

          s.on("progress", function (e) {
            s.established = true;

            if (
              e.status_code === 183 &&
              e.body &&
              s.hasOffer &&
              !s.dialog &&
              (!e.hasHeader("require") ||
                e.getHeader("require")?.indexOf("100rel") === -1)
            ) {
              if (s.mediaHandler.hasDescription(e)) {
                s.createDialog(e, "UAC");
              }

              s.hasAnswer = true;

              s.dialog.pracked.push(e.getHeader("rseq"));
              s.status = Session.C.STATUS_EARLY_MEDIA;
              try {
                s.mediaHandler.setDescription(
                  e,

                  s.mediaHandler.mediaHint.constraints,
                  s.modifiers
                );
                pauseAudio(outgoing, false, true);
              } catch (reason) {
                console.error(reason);
                s.terminate({
                  status_code: 488,
                  reason_phrase: "Bad Media Description",
                });
              }
            } else {
              if (e.status_code === 183 || e.status_code === 180) {
                outgoing.src = "/sounds/ringback.ogg";
                outgoing.play();
              }
            }
            dispatch(updateCallStatus({ disableCall: false }));
            updateSession(s);
          });
          s.on("accepted", function (e) {
            makeSessionHold();
            pauseAudio(outgoing, false, true);
            // condition added in case queue call is accepted (ringing + accepted)
            if (!e) return;

            s.date = new Date().getTime();

            s.statusAppel = "accepted";
            s.established = true;
            setActivecall(s);
          });

          s.on("bye", function () {
            oldNumber = undefined;

            if (qualificationAfterCall === "1") {
              setDrawerTagHeight(435);
              setOpenTag(true);
            } else if (qualificationAfterCall === "2") {
              setOpenTag(true);
            }
            // if (
            //   (window.BackendURLCmk =
            //     window.location.href.split("/")[4] === "logs")
            // ) {
            //   fetchLogs();
            // }

            pauseAudio(outgoing, true, true);

            if (s.conf) {
              s.conf.conf = null;
            }
            dispatch(stop_call());

            filterSession(s);
          });
          s.on("rejected", function () {
            try {
              oldNumber = undefined;

              pauseAudio(outgoing, false, true);
              dispatch(stop_call());
              filterSession(s);
            } catch (error) {}
          });
          s.on("cancel", function () {
            try {
              oldNumber = undefined;

              pauseAudio(outgoing, true, true);
              dispatch(stop_call());

              filterSession(s);
            } catch {
              return;
            }
          });
          s.on("failed", function () {
            try {
              oldNumber = undefined;

              pauseAudio(outgoing, false, true);
              dispatch(stop_call());

              filterSession(s);

              //change session (last call)

              //  //dispatch(stop_video_call());
            } catch {
              return;
            }
          });
        }
      } catch (e) {
        dispatch(stop_call());

        return;
      }
    };

    if (
      cmkPhone &&
      callIPBX?.post_numberR &&
      Number(callIPBX?.post_numberR) !== Number(posteVoip) &&
      Number(callIPBX?.post_numberR) !== Number(receiverInfo?.number) &&
      status === "success" &&
      session.length < 2 &&
      !confInfo.current
    ) {
      callClick();
      setUpWebphone(true);
    } else {
      dispatch(stop_call());
      //dispatch(stop_video_call());
    }
  }, [callIPBX?.post_numberR, status, cmkPhone]);
  // useEffect(() => {
  //   if (callVideoIPBX) {
  //     callClickVideo(callVideoIPBX);

  //     setUpWebphone(true);
  //   }
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [callVideoIPBX]);
  // useEffect(() => {
  //   if (upWebphone && !colleagues.length) dispatch(getUser());
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [upWebphone]);

  useEffect(() => {
    navigate !== "keyPad" && isKeyPadUp && setIsKeyPadUp(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [navigate]);
  //
  const handleDrawerSiderLayout = (action, method) => {
    switch (action) {
      case "open":
        dispatch({
          type: "SET_CONTACT_HEADER_INFO",
          payload: receiverInfo,
        });
        if (method === "navigate")
          navigateTo(
            generateUrlToView360(
              receiverInfo?.family_id,
              receiverInfo?.id,
              "v2"
            )
          );
        else setIsSiderLayoutDrawerOpen(true);
        break;
      case "close":
        setIsSiderLayoutDrawerOpen(false);
        break;
      default:
        break;
    }
  };
  //
  const handleActionWebPhone = (action, params) => {
    // console.log(action, params);
    switch (action) {
      case "create_element":
        setFamilyCreationInfo({
          familyId: params?.familyId,
          number: params?.number,
        });
        setIsCreateDrawerOpen(true);
        break;
      case "create_task":
        setOpenTask(true);
        break;
      case "display_info":
        setElementDetails({
          label: params?.name,
          id: params?.id,
          familyId: params?.familyId,
        });
        setOpenDrawerInfo(true);
        break;
      default:
        break;
    }
  };
  //
  //trak conf to send event to participants members
  // var activecall1 = activecall;
  // var session1 = session;
  // console.log({ isConf, activecall1, session1 });
  useEffect(() => {
    if (isConf.current && session.length > 1 && activecall?.conf) {
      handleConfEvents(activecall, "start");
    } else if (isConf.current && session.length < 2 && !activecall?.conf) {
      handleConfEvents(activecall, "end");
      isConf.current = false;
    }
  }, [activecall, session, isConf.current]);
  // //
  return (
    <>
      <div className="phone" style={{ width: "100%", borderRadius: "30px" }}>
        <div
          className={` absolute bottom-5  left-20 flex flex-col-reverse	overflow-hidden  rounded-md`}
          style={{
            filter: "drop-shadow(4px 8px 14px #b6b6b6)",
            width: upWebphone ? "20rem" : 60,
            // height: upWebphone ? 538 : 55,
          }}
        >
          <video
            key={"remoteVideo"}
            id="remoteVideo"
            style={{
              position: "fixed",
              height: "89%",
              width: "100%",
              zIndex: "3",
              objectFit: "cover",
              borderRadius: "1rem",
              top: "0px",
              display: "none",
            }}
          ></video>
          <video
            key={"localVideo"}
            id="localVideo"
            style={{
              position: "absolute",
              zIndex: "4",
              right: "5px",
              width: "39%",
              borderRadius: "10px",
              // top:"20px"
              top: "0px",
              display: "none",
            }}
            muted
          ></video>
          <div
            className="relative mt-5"
            ref={Refs_IDs.phonecpt}
            style={{ width: "45px", zIndex: "6" }}
          >
            <Badge
              count={nbrVoiceMessaging + nbrMissedCalls}
              overflowCount={10}
            >
              <Button
                style={{ width: "2.75rem", height: "2.75rem" }}
                onClick={() => {
                  // if (isGuestConnected()) return;
                  if (session.length === 0 || !upWebphone) {
                    if (upWebphone) {
                      setUpWebphone(false);
                      setOpenTag(false);
                    } else {
                      setUpWebphone(true);
                    }
                  } else {
                    webphoneDown
                      ? setWebphoneDown(false)
                      : setWebphoneDown(true);
                  }
                }}
                type="primary"
                icon={
                  session.length === 0 ? (
                    upWebphone ? (
                      <CloseOutlined style={{ fontSize: "20px" }} />
                    ) : (
                      <HiPhone
                        style={{
                          fontSize: 22,
                        }}
                      />
                    )
                  ) : webphoneDown ? (
                    <FullscreenOutlined style={{ fontSize: 24 }} />
                  ) : (
                    <FullscreenExitOutlined style={{ fontSize: 24 }} />
                  )
                }
              />
            </Badge>

            {internalforward?.forwarding === "1" ? (
              <div
                className="absolute h-5 w-5 rounded-full  bg-black text-white"
                style={{ top: "-10px" }}
              >
                <svg
                  className="h-3 w-3"
                  aria-hidden="true"
                  focusable="false"
                  data-prefix="fas"
                  data-icon="random"
                  role="img"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 512 512"
                  data-fa-i2svg=""
                  style={{ margin: "4px" }}
                >
                  <path
                    fill="currentColor"
                    d="M504.971 359.029c9.373 9.373 9.373 24.569 0 33.941l-80 79.984c-15.01 15.01-40.971 4.49-40.971-16.971V416h-58.785a12.004 12.004 0 0 1-8.773-3.812l-70.556-75.596 53.333-57.143L352 336h32v-39.981c0-21.438 25.943-31.998 40.971-16.971l80 79.981zM12 176h84l52.781 56.551 53.333-57.143-70.556-75.596A11.999 11.999 0 0 0 122.785 96H12c-6.627 0-12 5.373-12 12v56c0 6.627 5.373 12 12 12zm372 0v39.984c0 21.46 25.961 31.98 40.971 16.971l80-79.984c9.373-9.373 9.373-24.569 0-33.941l-80-79.981C409.943 24.021 384 34.582 384 56.019V96h-58.785a12.004 12.004 0 0 0-8.773 3.812L96 336H12c-6.627 0-12 5.373-12 12v56c0 6.627 5.373 12 12 12h110.785c3.326 0 6.503-1.381 8.773-3.812L352 176h32z"
                  ></path>
                </svg>
              </div>
            ) : (
              ""
            )}
          </div>
          {upWebphone ? (
            <Card
              className={`webPhone-card`}
              style={{
                zIndex: session.length !== 0 ? "1" : "6",
                padding: 0,
              }}
              title={
                <div className="ml-1.5 flex flex-row space-x-1">
                  <Badge
                    styles={{
                      indicator: {
                        height: 7,
                        width: 7,
                      },
                    }}
                    status={status}
                  />
                  <span style={{ fontSize: "14px" }}>{posteVoip}</span>
                </div>
              }
              styles={{
                body: { padding: 0 },
                // header: { backgroundColor: "#F1F5F9" },
              }}
              size="small"
              bordered={false}
              extra={
                session.length !== 0
                  ? [
                      <Button
                        key={"button-webphone-down"}
                        type="text"
                        // size="small"
                        // style={{ color: "#94a3b8" }}
                        shape="circle"
                        icon={
                          webphoneDown ? (
                            <FullscreenOutlined style={{ fontSize: 18 }} />
                          ) : (
                            <FullscreenExitOutlined style={{ fontSize: 18 }} />
                          )
                        }
                        onClick={() =>
                          webphoneDown
                            ? setWebphoneDown(false)
                            : setWebphoneDown(true)
                        }
                      />,
                    ]
                  : [
                      <Button
                        key={"button-webphone-close"}
                        type="text"
                        size="small"
                        onClick={() => setUpWebphone(false)}
                        icon={<CloseOutlined />}
                      />,
                    ]
              }
              actions={
                session.length === 0
                  ? webPhoneActions(
                      navigate,
                      setNavigate,
                      nbrMissedCalls,
                      nbrVoiceMessaging,
                      t,
                      isKeyPadUp,
                      setIsKeyPadUp
                    )
                  : []
              }
            >
              {param?.param_1 === undefined ? (
                <Result
                  key={"card-result"}
                  style={{ height: 375 }}
                  status="error"
                  title={t("toasts.error")}
                  subTitle={t("toasts.somethingWrong")}
                />
              ) : (
                <>
                  <Drawer
                    key={"drawer-card"}
                    title={
                      <div className="flex flex-row justify-between space-x-1">
                        {lastCallLog && !session?.length ? (
                          <div className="relative flex w-full flex-row items-center space-x-1 text-sm">
                            <p className="whitespace-nowrap font-semibold text-black">
                              {t("voip.qualifyTheCall")}
                            </p>
                            <p className="w-[0px] flex-1 truncate font-semibold text-slate-500">
                              {lastCallLog
                                ? `${posteVoip}` === lastCallLog.dst
                                  ? lastCallLog.src_name?.replaceAll(
                                      "_",
                                      " "
                                    ) || lastCallLog.src
                                  : lastCallLog.dst_name?.replaceAll(
                                      "_",
                                      " "
                                    ) || lastCallLog.dst
                                : ""}
                            </p>
                          </div>
                        ) : (
                          <Skeleton.Button
                            style={{ width: "8rem", height: 19 }}
                            active
                            size="small"
                          />
                        )}

                        {drawerTagHeight === 45 ? (
                          <div className="flex flex-row space-x-0.5">
                            <Button size="small" onClick={() => onClose()}>
                              {!lastCallLog ? t("voip.cancel") : t("voip.no")}
                            </Button>
                            <Button
                              disabled={!lastCallLog}
                              size="small"
                              type="primary"
                              onClick={() => setDrawerTagHeight(435)}
                            >
                              {t("voip.yes")}
                            </Button>
                          </div>
                        ) : (
                          <Button
                            size="small"
                            type="text"
                            icon={<CloseOutlined />}
                            onClick={() => onClose()}
                          />
                        )}
                      </div>
                    }
                    closeIcon={false}
                    placement={drawerTagHeight === 45 ? "top" : "bottom"}
                    onClose={onClose}
                    open={openTag && !session?.length}
                    getContainer={false}
                    styles={{
                      body: {
                        overflowY: "hidden",
                        display: drawerTagHeight === 45 && "none",
                        padding: "10px 5px",
                      },
                      header: { padding: "10px 5px" },
                    }}
                    // headerStyle={{
                    //   padding: "10px 5px",
                    // }}
                    height={drawerTagHeight}
                  >
                    {drawerTagHeight !== 45 && (
                      <Spin spinning={!lastCallLog}>
                        <TagContent
                          id={lastCallLog?._id}
                          data={allTags}
                          setOpen={setOpenTag}
                          source={"webPhone"}
                        />
                      </Spin>
                    )}
                  </Drawer>

                  {session.length === 0 ? (
                    <Suspense
                      fallback={
                        <div className="scroll_bar relative flex h-[23rem] items-center justify-center bg-gray-50">
                          <Spin size="large" />
                        </div>
                      }
                    >
                      <Main
                        key={"main-card"}
                        cancelEchecAppel={cancelEchecAppel}
                        tab={tab}
                        notifAppel={notifAppel}
                        notifAppelFromWB={notifAppelFromWB}
                        upWebphone={upWebphone}
                        navigate={navigate}
                        handleOpenMgsDrawer={handleOpenChatDrawer}
                        isOpenDrawerChat={openChatDrawer}
                        isKeyPadUp={isKeyPadUp}
                        setIsKeyPadUp={setIsKeyPadUp}
                        setUpWebphone={setUpWebphone}
                        handleActionWebPhone={handleActionWebPhone}
                      />
                    </Suspense>
                  ) : // : (
                  //   <Suspense
                  //     fallback={
                  //       <div className="scroll_bar relative flex h-[23rem] items-center justify-center bg-gray-50">
                  //         <Spin size="large" />
                  //       </div>
                  //     }>
                  //     <Dash
                  //       key={"dash-card"}
                  //       notifAppel={notifAppel}
                  //       notifAppelFromWB={notifAppelFromWB}
                  //     />
                  //   </Suspense>
                  // )
                  !webphoneDown ? (
                    <Suspense
                      fallback={
                        <div className="scroll_bar relative flex h-[23rem] items-center justify-center bg-gray-50">
                          <Spin size="large" />
                        </div>
                      }
                    >
                      <Callpage
                        key={"call-page-card"}
                        setWebphoneDown={setWebphoneDown}
                        holdSession={holdSession}
                        muteSession={muteSession}
                        activecall={activecall}
                        setActivecall={setActivecall}
                        hungUp={hungUp}
                        upBtn={upBtn}
                        session={session}
                        setSession={setSession}
                        changeSession={changeSession}
                        holdCall={holdCall}
                        muteCall={muteCall}
                        ConferenceCall={ConferenceCall}
                        sipMergeSessions={sipMergeSessions}
                        upBtnVideo={upBtnVideo}
                        transfertCall={transfertCall}
                        tab={tab}
                        backHomeVariable={backHome}
                        openBackHome={openBackHome}
                        closeBackHome={closeBackHome}
                        openBackHomeConference={openBackHomeConference}
                        sendDTMF={sendDTMF}
                        webphoneDown={webphoneDown}
                        sendMessageAndHungUP={sendMessageAndHungUP}
                        handleOpenMgsDrawer={handleOpenChatDrawer}
                        isOpenDrawerChat={openChatDrawer}
                        isCreateDrawerOpen={isCreateDrawerOpen}
                        setIsCreateDrawerOpen={setIsCreateDrawerOpen}
                        setFamilyCreationInfo={setFamilyCreationInfo}
                        isCreationDone={isCreationDone}
                        setTheReceiverInfo={setTheReceiverInfo}
                        handleDrawerSiderLayout={handleDrawerSiderLayout}
                        handleActionWebPhone={handleActionWebPhone}
                        attendedTransfer={attendedTransfer}
                        mergeCallsToConference={mergeCallsToConference}
                      />
                    </Suspense>
                  ) : (
                    <Suspense
                      fallback={
                        <div className="scroll_bar relative flex h-[23rem] items-center justify-center bg-gray-50">
                          <Spin size="large" />
                        </div>
                      }
                    >
                      <CallPageDown
                        key={"call-page-down-card"}
                        activecall={activecall}
                        holdSession={holdSession}
                        hungUp={hungUp}
                        upBtn={upBtn}
                        session={session}
                        changeSession={changeSession}
                        muteSession={muteSession}
                        tab={tab}
                      />
                    </Suspense>
                  )}
                </>
              )}
            </Card>
          ) : null}
        </div>
      </div>

      {isCreateDrawerOpen && familyCreationInfo?.familyId && (
        <Suspense fallback={<LoaderDrawer />}>
          <FormCreate
            open={isCreateDrawerOpen}
            setOpen={setIsCreateDrawerOpen}
            familyId={familyCreationInfo?.familyId}
            nbrPhoneFromVoip={familyCreationInfo?.number}
            setCatchChange={setIsCreationDone}
          />
        </Suspense>
      )}

      <Suspense fallback={<LoaderDrawer />}>
        <Drawer360
          isOpen={isSiderLayoutDrawerOpen}
          elementInfo={elementInfo}
          handleDrawerSiderLayout={handleDrawerSiderLayout}
        />
      </Suspense>
      {openTask && (
        <Suspense fallback={<LoaderDrawer />}>
          <CreateTask
            open={openTask}
            setOpen={setOpenTask}
            mask={false}
            source={"call"}
          />
        </Suspense>
      )}
      <Suspense fallback={<LoaderDrawer />}>
        <DisplayElementInfo
          open={openDrawerInfo}
          setOpen={setOpenDrawerInfo}
          elementDetails={elementDetails}
        />
      </Suspense>
    </>
  );
}

export default memo(PhoneCpt);
