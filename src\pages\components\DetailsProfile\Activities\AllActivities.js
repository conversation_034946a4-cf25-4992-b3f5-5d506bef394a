import { InfoCircleFilled } from "@ant-design/icons";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Col,
  List,
  Row,
  Segmented,
  Skeleton,
  Space,
  Spin,
  Tooltip,
  Typography,
} from "antd";
import {
  Activity,
  CalendarCheck,
  CheckSquare,
  ChevronDown,
  ChevronRight,
  File,
  Home,
  InfoIcon,
  Mail,
  MessageCircle,
  PictureInPicture,
  Presentation,
  StickyNote,
} from "lucide-react";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { AnimatePresence, motion } from "framer-motion";
import { useEffect } from "react";
import ListActivity from "./ListActivity";
import CheckList from "../CheckList.js";
import Mails from "./Mails";

const AllActivities = ({
  selectedValue,
  setSelectedValue,
  load,
  setLoad,
  tasks360,
}) => {
  const [t] = useTranslation("common");
  const [isVisible, setIsVisible] = useState(true);
  const [isVisible2, setIsVisible2] = useState(true);
  const [choiceCard, setChoiceCard] = useState({
    card1: true,
    card2: false,
    card3: false,
    card4: false,
    color: "#EFF6FF",
  });
  //console.log(tasks360);
  const [paddingHover, setPaddingHover] = useState({
    transition: "all 0.4s ease-in-out",
    delay: "0.3s",
    padding: "0.5em",
  });
  const toggleVisibility = () => {
    setIsVisible(!isVisible);
  };
  useEffect(() => {
    if (load) {
      setTimeout(() => {
        setLoad(false);
      }, 1000);
    }
  }, [load, setLoad]);
  const data = [
    {
      title: "Ant Design Title 1",
      desc: "Lorem Ipsum is simply dummy text of the printing and typesetting industry. ",
    },
    {
      title: "Ant Design Title 2",
      desc: "Lorem Ipsum is simply dummy text of the printing and typesetting industry. ",
    },
    {
      title: "Ant Design Title 3",
      desc: "Lorem Ipsum is simply dummy text of the printing and typesetting industry. ",
    },
  ];
  const dataCards = [
    {
      title: "Title 1",
    },
    {
      title: "Title 2",
    },
    {
      title: "Title 3",
    },
    {
      title: "Title 4",
    },
  ];

  const switchViewsOnSelectTab = (value) => {
    switch (value) {
      case "9":
        return <CheckList />;

      default:
        break;
    }
  };

  return (
    <Space
      direction="vertical"
      size="middle"
      style={{
        display: "flex",
      }}
    >
      {" "}
      <Segmented
        // className=" text-md segmentedActivities bg-[#F8FAFC]   "
        defaultValue={"1"}
        block
        options={[
          {
            label: (
              <div className=" -mb-1 flex flex-col items-center justify-center pt-1">
                <Activity />
                <span className="-mt-1">
                  {t("layout_profile_details.activities")}
                </span>
              </div>
            ),
            value: "1",
          },
          {
            label: (
              <div className=" -mb-1 flex flex-col items-center justify-center pt-1">
                <StickyNote />
                <span className="-mt-1">Notes</span>
              </div>
            ),
            value: "2",
          },
          {
            label: (
              <div className=" -mb-1 flex flex-col items-center justify-center pt-1">
                <Mail />
                <span className="-mt-1">Email</span>
              </div>
            ),
            value: "3",
          },
          {
            label: (
              <div className=" -mb-1 flex flex-col items-center justify-center pt-1">
                <MessageCircle />
                <span className="-mt-1">SMS</span>
              </div>
            ),
            value: "4",
          },
          {
            label: (
              <div className=" -mb-1 flex flex-col items-center justify-center pt-0.5">
                <PictureInPicture />
                <span className="-mt-1">
                  {t("layout_profile_details.callTask")}
                </span>
              </div>
            ),
            value: "5",
          },
          {
            label: (
              <div className=" -mb-1 flex flex-col items-center justify-center pt-0.5">
                <Presentation />
                <span className="-mt-1">
                  {t("layout_profile_details.meetingTask")}
                </span>
              </div>
            ),
            value: "6",
          },
          {
            label: (
              <div className=" -mb-1 flex flex-col items-center justify-center pt-1">
                <CalendarCheck />
                <span className="-mt-1">
                  {" "}
                  {t("layout_profile_details.allTasks")}
                </span>
              </div>
            ),
            value: "7",
          },
          {
            label: (
              <div className=" -mb-1 flex flex-col items-center justify-center pt-1">
                <File />
                <span className="-mt-1">
                  {" "}
                  {t("layout_profile_details.files")}
                </span>
              </div>
            ),
            value: "8",
          },
          {
            label: (
              <div className=" -mb-1 flex flex-col items-center justify-center pt-1">
                <CheckSquare />
                <span className="-mt-1"> Lists</span>
              </div>
            ),
            value: "9",
          },
        ]}
        onChange={(e) => {
          setLoad(true);
          setSelectedValue(e);
          setChoiceCard((prev) => ({
            card1: true,
            card2: false,
            card3: false,
            card4: false,
          }));
        }}
        value={selectedValue}
      />
      <Mails />
      {/* <AnimatePresence>
        {selectedValue !== "1" && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Row gutter={16} className="container">
              <Col span={6} className="cardActivities">
                <Card
                  className={`relative flex cursor-pointer   hover:border-blue-500 hover:bg-blue-100 ${
                    choiceCard.card1
                      ? "choiced border-blue-500 bg-blue-100"
                      : "notchoiced"
                  }`}
                  onClick={() => {
                    setLoad(true);
                    setChoiceCard((prev) => ({
                      card1: true,
                      card2: false,
                      card3: false,
                      card4: false,
                      color: "#EFF6FF",
                    }));
                  }}
                >
                  <span
                    style={{
                      fontSize: "22px",
                      fontWeight: "bold",
                      padding: "0 10px",
                    }}
                  >
                    {" "}
                    All
                  </span>
                  <span
                    style={{
                      fontSize: "25px",
                      position: "absolute",
                      top: "50%",
                      transform: "translateY(-50%)",
                      right: "26px",
                      fontWeight: "bold",
                      color: "#afafaf",
                    }}
                  >
                    10
                  </span>
                </Card>
              </Col>
              <Col span={6} className="cardActivities">
                <Card
                  className={`flex cursor-pointer text-center hover:border-yellow-500 hover:bg-orange-100 ${
                    choiceCard.card2
                      ? "choiced border-yellow-500 bg-orange-100"
                      : "notchoiced"
                  }`}
                  onClick={() => {
                    setLoad(true);
                    setChoiceCard((prev) => ({
                      card1: false,
                      card2: true,
                      card3: false,
                      card4: false,
                      color: "#ffedd5",
                    }));
                  }}
                >
                  <span
                    style={{
                      fontSize: "22px",
                      fontWeight: "bold",
                      padding: "0 10px",
                    }}
                  >
                    {" "}
                    Upcoming
                  </span>
                  <span
                    style={{
                      fontSize: "25px",
                      position: "absolute",
                      top: "50%",
                      transform: "translateY(-50%)",
                      right: "26px",
                      fontWeight: "bold",
                      color: "#afafaf",
                    }}
                  >
                    3
                  </span>
                </Card>
              </Col>
              <Col span={6} className="cardActivities">
                <Card
                  className={`flex cursor-pointer text-center  hover:border-red-500 hover:bg-red-100 ${
                    choiceCard.card3
                      ? "choiced border-red-500 bg-red-100"
                      : "notchoiced"
                  }`}
                  onClick={() => {
                    setLoad(true);
                    setChoiceCard((prev) => ({
                      card1: false,
                      card2: false,
                      card3: true,
                      card4: false,
                      color: "#fee2e2",
                    }));
                  }}
                >
                  <span
                    style={{
                      fontSize: "22px",
                      fontWeight: "bold",
                      padding: "0 10px",
                    }}
                  >
                    {" "}
                    Overdue
                  </span>
                  <span
                    style={{
                      fontSize: "25px",
                      position: "absolute",
                      top: "50%",
                      transform: "translateY(-50%)",
                      right: "26px",
                      fontWeight: "bold",
                      color: "#afafaf",
                    }}
                  >
                    3
                  </span>
                </Card>
              </Col>

              <Col span={6} className="cardActivities">
                <Card
                  className={`flex cursor-pointer text-center  hover:border-green-500 hover:bg-green-100 ${
                    choiceCard.card4
                      ? "choiced border-green-500 bg-green-100"
                      : "notchoiced"
                  }`}
                  onClick={() => {
                    setLoad(true);
                    setChoiceCard((prev) => ({
                      card1: false,
                      card2: false,
                      card3: false,
                      card4: true,
                      color: "#dcfce7",
                    }));
                  }}
                >
                  <span
                    style={{
                      fontSize: "22px",
                      fontWeight: "bold",
                      padding: "0 10px",
                    }}
                  >
                    {" "}
                    Completed
                  </span>
                  <span
                    style={{
                      fontSize: "25px",
                      position: "absolute",
                      top: "50%",
                      transform: "translateY(-50%)",
                      right: "26px",
                      fontWeight: "bold",
                      color: "#afafaf",
                    }}
                  >
                    4
                  </span>
                </Card>
              </Col>
            </Row>
          </motion.div>
        )}
      </AnimatePresence> */}
      <>
        {/* <div className="">
          <div className="flex w-full items-center justify-between rounded-lg bg-[#5D6B79] p-2">
            <Space>
              <Typography.Title level={3} style={{ color: "white" }}>
                {t("layout_profile_details.upcoming")}
              </Typography.Title>
              <Tooltip title="test">
                <InfoIcon
                  className="cursor-help "
                  style={{ color: "white" }}
                  size={16}
                />
              </Tooltip>
            </Space>
            <Button
              type="text"
              shape="circle"
              className="cursor-n-resize text-white"
              icon={
                <ChevronRight
                  size={15}
                  className={`${
                    isVisible ? "rotate-90" : "rotate-0"
                  } text-white`}
                />
              }
              onClick={toggleVisibility}
            />
          </div>
          <AnimatePresence>
            {selectedValue !== "1" && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: "auto", opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Row gutter={16} className="container">
                  <Col span={6} className="cardActivities">
                    <Card
                      className={`relative flex cursor-pointer   hover:border-blue-500 hover:bg-blue-100 ${
                        choiceCard.card1 ? "choiced border-blue-500 bg-blue-100" : "notchoiced"
                      }`}
                      onClick={() => {
                        setLoad(true);
                        setChoiceCard((prev) => ({
                          card1: true,
                          card2: false,
                          card3: false,
                          card4: false,
                          color: "#EFF6FF",
                        }));
                      }}
                    >
                      <span
                        style={{
                          fontSize: "22px",
                          fontWeight: "bold",
                          padding: "0 10px",
                        }}
                      >
                        {" "}
                        All
                      </span>
                      <span
                        style={{
                          fontSize: "25px",
                          position: "absolute",
                          top: "50%",
                          transform: "translateY(-50%)",
                          right: "26px",
                          fontWeight: "bold",
                          color: "#afafaf",
                        }}
                      >
                        10
                      </span>
                    </Card>
                  </Col>
                  <Col span={6} className="cardActivities">
                    <Card
                      className={`flex cursor-pointer text-center hover:border-yellow-500 hover:bg-orange-100 ${
                        choiceCard.card2 ? "choiced border-yellow-500 bg-orange-100" : "notchoiced"
                      }`}
                      onClick={() => {
                        setLoad(true);
                        setChoiceCard((prev) => ({
                          card1: false,
                          card2: true,
                          card3: false,
                          card4: false,
                          color: "#ffedd5",
                        }));
                      }}
                    >
                      <span
                        style={{
                          fontSize: "22px",
                          fontWeight: "bold",
                          padding: "0 10px",
                        }}
                      >
                        {" "}
                        Upcoming
                      </span>
                      <span
                        style={{
                          fontSize: "25px",
                          position: "absolute",
                          top: "50%",
                          transform: "translateY(-50%)",
                          right: "26px",
                          fontWeight: "bold",
                          color: "#afafaf",
                        }}
                      >
                        3
                      </span>
                    </Card>
                  </Col>
                  <Col span={6} className="cardActivities">
                    <Card
                      className={`flex cursor-pointer text-center  hover:border-red-500 hover:bg-red-100 ${
                        choiceCard.card3 ? "choiced border-red-500 bg-red-100" : "notchoiced"
                      }`}
                      onClick={() => {
                        setLoad(true);
                        setChoiceCard((prev) => ({
                          card1: false,
                          card2: false,
                          card3: true,
                          card4: false,
                          color: "#fee2e2",
                        }));
                      }}
                    >
                      <span
                        style={{
                          fontSize: "22px",
                          fontWeight: "bold",
                          padding: "0 10px",
                        }}
                      >
                        {" "}
                        Overdue
                      </span>
                      <span
                        style={{
                          fontSize: "25px",
                          position: "absolute",
                          top: "50%",
                          transform: "translateY(-50%)",
                          right: "26px",
                          fontWeight: "bold",
                          color: "#afafaf",
                        }}
                      >
                        3
                      </span>
                    </Card>
                  </Col>

                  <Col span={6} className="cardActivities">
                    <Card
                      className={`flex cursor-pointer text-center  hover:border-green-500 hover:bg-green-100 ${
                        choiceCard.card4 ? "choiced border-green-500 bg-green-100" : "notchoiced"
                      }`}
                      onClick={() => {
                        setLoad(true);
                        setChoiceCard((prev) => ({
                          card1: false,
                          card2: false,
                          card3: false,
                          card4: true,
                          color: "#dcfce7",
                        }));
                      }}
                    >
                      <span
                        style={{
                          fontSize: "22px",
                          fontWeight: "bold",
                          padding: "0 10px",
                        }}
                      >
                        {" "}
                        Completed
                      </span>
                      <span
                        style={{
                          fontSize: "25px",
                          position: "absolute",
                          top: "50%",
                          transform: "translateY(-50%)",
                          right: "26px",
                          fontWeight: "bold",
                          color: "#afafaf",
                        }}
                      >
                        4
                      </span>
                    </Card>
                  </Col>
                </Row>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
        <div className="-mb-3 flex w-full items-center justify-between rounded-lg bg-[#5D6B79] p-2">
          <Space>
            <Typography.Title level={3} style={{ color: "white" }}>
              October 2023
            </Typography.Title>
            <Tooltip title="test">
              <InfoIcon
                className="cursor-help"
                style={{ color: "white" }}
                size={16}
              />{" "}
            </Tooltip>
          </Space>
          <Button
            type="text"
            shape="circle"
            className="cursor-n-resize text-white"
            icon={
              <ChevronRight
                size={15}
                className={`${
                  isVisible2 ? "rotate-90" : "rotate-0"
                } text-white`}
              />
            }
            onClick={() => setIsVisible2(!isVisible2)}
          />
        </div> */}
        {/* <AnimatePresence>
          {isVisible2 && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <List
                itemLayout="horizontal"
                // header={
                //   <div className="flex w-full items-center justify-between">
                //     <Space>
                //       <Typography.Title level={3}>
                //         {t("layout_profile_details.upcoming")}
                //       </Typography.Title>
                //       <Tooltip title="test">
                //         <InfoCircleFilled className="cursor-help" />
                //       </Tooltip>
                //     </Space>
                //     <Button
                //       type="text"
                //       shape="circle"
                //       icon={<ChevronDown size={15} />}
                //       onClick={toggleVisibility}
                //     />
                //   </div>
                // }
                dataSource={tasks360[0].tasks}
                renderItem={(item, index) => (
                  <ListActivity
                    item={item}
                    load={load}
                    choiceCard={choiceCard}
                    index={index}
                  />
                )}
              </AnimatePresence>
            </div>
            <div className="-mb-3 flex w-full items-center justify-between rounded-lg bg-[#5D6B79] p-2">
              <Space>
                <Typography.Title level={3} style={{ color: "white" }}>
                  October 2023
                </Typography.Title>
                <Tooltip title="test">
                  <InfoIcon className="cursor-help" style={{ color: "white" }} size={16} />{" "}
                </Tooltip>
              </Space>
              <Button
                type="text"
                shape="circle"
                className="cursor-n-resize text-white"
                icon={
                  <ChevronRight
                    size={15}
                    className={`${isVisible2 ? "rotate-90" : "rotate-0"} text-white`}
                  />
                }
                onClick={() => setIsVisible2(!isVisible2)}
              />
            </motion.div>
          )}
        </AnimatePresence> */}
      </>
    </Space>
  );
};

export default AllActivities;
