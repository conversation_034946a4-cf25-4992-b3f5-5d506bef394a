import {
  Badge,
  Button,
  Descriptions,
  Divider,
  Empty,
  Image,
  Input,
  Popover,
  Skeleton,
  Tooltip,
} from "antd";
import Text from "antd/es/typography/Text";
import Title from "antd/es/typography/Title";

import { PlusOutlined, RightOutlined, SendOutlined } from "@ant-design/icons";
import { File, ImageContainer, LinkPreview } from "components/Chat";
import { toastNotification } from "components/ToastNotification";
import {
  setDocumentsFiltredListChatInfo,
  setDocumentsListChatInfo,
  setLinksFiltredListChatInfo,
  setLinksListChatInfo,
  setMediasListChatInfo,
} from "new-redux/actions/chat.actions";
import {
  setOpenEditor,
  setOpenModalEmail,
} from "new-redux/actions/mail.actions";
import { callApi, goToMessage } from "new-redux/services/chat.services";
import {
  Suspense,
  lazy,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import { useTranslation } from "react-i18next";
import { FiPhoneForwarded, FiSearch } from "react-icons/fi";
import { useDispatch, useSelector } from "react-redux";
import MainService from "services/main.service";
import { lazyRetry } from "utils/lazyRetry";
import {
  getName,
  getOrGenerateTabId,
  TAB_LIST_HEIGHT,
} from "../../utils/ConversationUtils";
import Members from "./members-list";
const MembersAdd = lazy(() =>
  lazyRetry(() => import("../../sidebar/header/members_add"), "MembersAdd")
);

export const MediasChildren = () => {
  const { images } = useSelector((state) => state.chat);
  const selectedConversation = useSelector(
    (state) => state.ChatRealTime.selectedConversation
  );

  const dispatch = useDispatch();
  const [currentPage, setCurrentPage] = useState(1);
  const [loadingMore, setLoadingMore] = useState(false);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(false);
  const [t] = useTranslation("common");

  const observe = useRef();
  const lastElement = useCallback(
    (node) => {
      if (loadingMore) return;
      if (observe.current) observe.current.disconnect();
      observe.current = new IntersectionObserver(
        (e) => {
          if (e[0].isIntersecting && hasMore) {
            setCurrentPage((c) => c + 1);
          }
        },
        { threshold: 0.7 }
      );
      if (node) observe.current.observe(node);
    },
    [loadingMore, hasMore]
  );

  const skl = () => (
    <div className="flex h-full w-full flex-col gap-3 overflow-y-auto">
      {Array.from(
        { length: Math.floor(document.body.clientHeight / 3) },
        (_, i) => i + 1
      ).map((item) => (
        <Skeleton.Input key={`sklt_${item}`} active style={{ width: "100%" }} />
      ))}
    </div>
  );

  const loadMoreDataImages = async (signal) => {
    try {
      if (!selectedConversation?.conversationId) return;
      if (currentPage > 1) {
        setLoadingMore(true);
      } else {
        setLoading(true);
      }
      const medias = await MainService.getMediasDiscussion(
        selectedConversation?.conversationId,
        currentPage,
        selectedConversation?.type,
        signal
      );
      setHasMore(currentPage < medias.data.meta.last_page);
      setLoadingMore(false);
      dispatch(setMediasListChatInfo([...images, ...medias.data.data]));
      setLoading(false);
    } catch (error) {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  useEffect(() => {
    const abortController = new AbortController();
    loadMoreDataImages(abortController.signal);
    return () => {
      abortController.abort();
    };
  }, [currentPage]);

  return (
    <div
      style={{
        maxHeight: `calc(100vh - ${TAB_LIST_HEIGHT})`,
        cursor: loadingMore || loading ? "wait" : "auto",
      }}
      className=" overflow-auto p-0"
    >
      {loading ? (
        skl()
      ) : images?.length === 0 ? (
        <Empty description={t("chat.info.noMedia")} />
      ) : (
        <ul className="flex w-full flex-col items-center  px-0.5 ">
          <Image.PreviewGroup>
            {images?.map((img, index) => (
              <div
                className="h-full w-full"
                key={`file_${img?._id}_${index}`}
                ref={images.length - 1 === index ? lastElement : null}
              >
                <Suspense
                  fallback={Array.from(
                    { length: images.length ?? 10 },
                    (_, i) => i + 1
                  ).map((item) => (
                    <Skeleton.Input key={`file_key_${item}`} block active />
                  ))}
                >
                  <ImageContainer
                    key={img._id}
                    array={images}
                    img={img}
                    index={index}
                    GoToComponent={
                      <Tooltip title={t("chat.goto")}>
                        <Button
                          onClick={async () =>
                            dispatch(
                              await goToMessage({
                                id_search: img.message_id,
                                type: selectedConversation?.type,
                              })
                            )
                          }
                          icon={<RightOutlined />}
                          type="link"
                        />
                      </Tooltip>
                    }
                  />
                </Suspense>
              </div>
            ))}
          </Image.PreviewGroup>
        </ul>
      )}
      {loadingMore && (
        <Skeleton.Image className="h-32 w-40 py-2" active size="small" />
      )}
    </div>
  );
};

export const DocumentsChildren = () => {
  const { documents, documentsFiltred } = useSelector((state) => state.chat);
  const selectedConversation = useSelector(
    (state) => state.ChatRealTime.selectedConversation
  );
  const searchRef = useRef(null);

  const dispatch = useDispatch();
  const [currentPage, setCurrentPage] = useState(1);
  const [loadingMore, setLoadingMore] = useState(false);
  const [loading, setLoading] = useState("");
  const [hasMore, setHasMore] = useState(false);
  const [numberSearchTotal, setNumberSearchTotal] = useState("default");
  const [t] = useTranslation("common");

  const observe = useRef();
  const lastElement = useCallback(
    (node) => {
      if (loadingMore || numberSearchTotal !== "default") return;
      if (observe.current) observe.current.disconnect();
      observe.current = new IntersectionObserver(
        (e) => {
          if (e[0].isIntersecting && hasMore) {
            setCurrentPage((c) => c + 1);
          }
        },
        { threshold: 0.7 }
      );
      if (node) observe.current.observe(node);
    },
    [loadingMore, numberSearchTotal, hasMore]
  );

  const skl = () => (
    <div className="flex h-full w-full flex-col gap-3 overflow-y-auto">
      {Array.from(
        { length: Math.floor(document.body.clientHeight / 3) },
        (_, i) => i + 1
      ).map((item) => (
        <Skeleton.Input key={`sklt_${item}`} active style={{ width: "100%" }} />
      ))}
    </div>
  );

  const loadDatFile = async (signal) => {
    if (!selectedConversation?.conversationId) return;

    try {
      if (currentPage > 1) {
        setLoadingMore(true);
      } else {
        setLoading("default");
      }
      const filesData = await MainService.getFilesDiscussion(
        selectedConversation?.conversationId,
        currentPage,
        selectedConversation?.type,
        signal
      );

      dispatch(
        setDocumentsListChatInfo([...documents, ...filesData.data.data])
      );
      setHasMore(currentPage < filesData.data.meta.last_page);
    } catch (error) {
    } finally {
      setLoadingMore(false);
      setLoading("");
    }
  };
  const cancelSearch = () => {
    dispatch(setDocumentsFiltredListChatInfo(documentsFiltred));
    setNumberSearchTotal("default");
  };
  const searchDocument = async (value) => {
    const abortController = new AbortController();

    try {
      setLoading("search");
      if (value === "") {
        cancelSearch();

        return;
      }
      const formData = new FormData();
      formData.append("keyword", value);
      formData.append("conversation_id", selectedConversation?.conversationId);
      const filesData = await MainService.searchFileDiscussion(
        formData,
        selectedConversation?.type,
        abortController.signal
      );
      dispatch(setDocumentsFiltredListChatInfo(filesData?.data?.files));
      setNumberSearchTotal(filesData?.data.results);
      setLoading("");
      setLoadingMore(false);
    } catch (error) {
    } finally {
      setLoading("");
      setLoadingMore(false);
    }

    return () => {
      abortController.abort();
    };
  };

  useEffect(() => {
    const abortController = new AbortController();

    loadDatFile(abortController.signal);
    return () => {
      abortController.abort();
    };
  }, [currentPage]);

  return (
    <div className="h-full overflow-hidden py-3">
      <Input.Search
        ref={searchRef}
        onClick={() => {
          let time;
          document.getElementById("editor-input")?.blur();
          clearTimeout(time);

          time = setTimeout(() => {
            searchRef.current?.focus();
            clearTimeout(time);
          }, 1);
        }}
        loading={loading === "search"}
        onSearch={(value) => {
          searchDocument(value.trimStart().replace(/\s{1,} /g, " "));
        }}
        enterButton
        size="middle"
        placeholder={t("chat.searchSide.searchDocument")}
        prefix={<FiSearch className="text-slate-500" />}
        // value={search}
        onChange={(e) => {
          if (e.target.value === "") cancelSearch();
        }}
        className=" w-full flex-1"
        allowClear
      />
      <div className="my-1.5  ml-1 w-full flex-1">
        {numberSearchTotal > 0 && numberSearchTotal !== "default" ? (
          <p
            dangerouslySetInnerHTML={{
              __html: t("chat.searchSide.searchResults", {
                count: numberSearchTotal,
              }),
            }}
            className="text-sm  text-gray-400 "
          ></p>
        ) : (
          <div className="text-xs  text-gray-400 ">
            {t("chat.searchSide.searchTips")}
          </div>
        )}
      </div>

      <div
        style={{
          maxHeight: `calc(100vh - ${TAB_LIST_HEIGHT})`,
          overflow: "auto",
          cursor: loadingMore || loading ? "wait" : "auto",
        }}
        className="relative flex flex-col  "
      >
        {loading === "default" ? (
          skl()
        ) : documents?.length === 0 ? (
          <Empty description={t("chat.info.noDocuments")} />
        ) : (
          <div className="h-full w-full  flex-wrap px-1">
            {documents?.map((file, index) => (
              <div
                className="my-1"
                key={`file_${file?._id}_${index}`}
                ref={documents.length - 1 === index ? lastElement : null}
              >
                <Suspense
                  fallback={Array.from(
                    { length: documents.length ?? 10 },
                    (_, i) => i + 1
                  ).map((item) => (
                    <Skeleton.Input key={`file_key_${item}`} block active />
                  ))}
                >
                  <File
                    GoToComponent={
                      <Tooltip title={t("chat.goto")}>
                        <Button
                          onClick={async () =>
                            dispatch(
                              await goToMessage({
                                id_search: file.message_id,
                                type: selectedConversation?.type,
                              })
                            )
                          }
                          icon={<RightOutlined />}
                          type="link"
                        />
                      </Tooltip>
                    }
                    fromInfosTab={true}
                    file={file}
                    index={index}
                  />
                </Suspense>
              </div>
            ))}
          </div>
        )}

        {loadingMore && (
          <Skeleton.Input className="py-3" active block size="small" />
        )}
      </div>
    </div>
  );
};

export const LinksChildren = () => {
  const { links, linksFiltred } = useSelector((state) => state.chat);
  const selectedConversation = useSelector(
    (state) => state.ChatRealTime.selectedConversation
  );
  const searchRef = useRef(null);

  const dispatch = useDispatch();
  const [currentPage, setCurrentPage] = useState(1);
  const [loadingMore, setLoadingMore] = useState(false);
  const [loading, setLoading] = useState("");
  const [hasMore, setHasMore] = useState(false);
  const [numberSearchTotal, setNumberSearchTotal] = useState("default");
  const [t] = useTranslation("common");

  const observe = useRef();
  const lastElement = useCallback(
    (node) => {
      if (loadingMore || numberSearchTotal !== "default") return;

      if (observe.current) observe.current.disconnect();
      observe.current = new IntersectionObserver(
        (e) => {
          if (e[0].isIntersecting && hasMore) {
            setCurrentPage((c) => c + 1);
          }
        },
        { threshold: 0.7 }
      );
      if (node) observe.current.observe(node);
    },
    [loadingMore, numberSearchTotal, hasMore]
  );

  const skl = () => (
    <div className="flex h-full w-full flex-col gap-3 overflow-y-auto">
      {Array.from(
        { length: Math.floor(document.body.clientHeight / 3) },
        (_, i) => i + 1
      ).map((item) => (
        <Skeleton.Input key={`sklt_${item}`} active style={{ width: "100%" }} />
      ))}
    </div>
  );

  const loadDataLinks = async (signal) => {
    if (!selectedConversation?.conversationId) return;

    try {
      if (currentPage > 1) {
        setLoadingMore(true);
      } else {
        setLoading("default");
      }
      const filesData = await MainService.getLinksDiscussion(
        selectedConversation?.conversationId,
        currentPage,
        selectedConversation?.type,
        signal
      );

      dispatch(
        setLinksListChatInfo(
          currentPage === 1
            ? filesData.data.data
            : [...links, ...filesData.data.data]
        )
      );
      setHasMore(currentPage < filesData.data.meta.last_page);
    } catch (error) {
    } finally {
      setLoading("");
      setLoadingMore(false);
    }
  };
  const cancelSearch = () => {
    dispatch(setLinksFiltredListChatInfo(linksFiltred));
    setNumberSearchTotal("default");
  };
  const searchLinks = async (value) => {
    const abortController = new AbortController();

    try {
      setLoading("search");
      if (value === "") {
        cancelSearch();

        return;
      }
      const formData = new FormData();
      formData.append("keyword", value);
      formData.append("conversation_id", selectedConversation?.conversationId);
      const linksData = await MainService.searchLinksDiscussion(
        formData,
        selectedConversation?.type,
        abortController.signal
      );
      dispatch(setLinksFiltredListChatInfo(linksData?.data?.urls));
      setNumberSearchTotal(linksData?.data.results);
    } catch (error) {
    } finally {
      setLoading("");
      setLoadingMore(false);
    }

    return () => {
      abortController.abort();
    };
  };

  useEffect(() => {
    const abortController = new AbortController();

    loadDataLinks(abortController.signal);
    return () => {
      abortController.abort();
    };
  }, [currentPage]);

  return (
    <div className="h-full overflow-hidden py-3">
      <Input.Search
        ref={searchRef}
        onClick={() => {
          let time;
          document.getElementById("editor-input")?.blur();
          clearTimeout(time);

          time = setTimeout(() => {
            searchRef.current?.focus();
            clearTimeout(time);
          }, 1);
        }}
        loading={loading === "search"}
        onSearch={(value) => {
          searchLinks(value.trimStart().replace(/\s{1,} /g, " "));
        }}
        enterButton
        size="middle"
        placeholder={t("chat.searchSide.searchLinks")}
        prefix={<FiSearch className="text-slate-500" />}
        onChange={(e) => {
          if (e.target.value === "") cancelSearch();
        }}
        className=" w-full flex-1"
        allowClear
      />
      <div className="my-1.5  ml-1 w-full flex-1">
        {numberSearchTotal > 0 && numberSearchTotal !== "default" ? (
          <p
            dangerouslySetInnerHTML={{
              __html: t("chat.searchSide.searchResults", {
                count: numberSearchTotal,
              }),
            }}
            className="text-sm  text-gray-400 "
          ></p>
        ) : (
          <div className="text-xs  text-gray-400 ">
            {t("chat.searchSide.searchTips")}
          </div>
        )}
      </div>

      <div
        style={{
          maxHeight: `calC(100vh - ${TAB_LIST_HEIGHT})`,
          overflow: "auto",
          cursor: loadingMore || loading ? "wait" : "auto",
        }}
        className="relative flex flex-col  "
      >
        {loading === "default" ? (
          skl()
        ) : links?.length === 0 ? (
          <Empty description={t("chat.info.noLinks")} />
        ) : (
          <div className="h-full w-full  flex-wrap px-1">
            {links.map((link, index) => (
              <div
                className="my-1"
                key={`link_${link?._id}_${index}`}
                ref={links.length - 1 === index ? lastElement : null}
              >
                <Suspense
                  fallback={Array.from(
                    { length: link.length ?? 10 },
                    (_, i) => i + 1
                  ).map((item) => (
                    <div className="flex h-full w-full flex-col gap-3 overflow-y-auto">
                      <Skeleton.Input key={`links_key_${item}`} block active />
                    </div>
                  ))}
                >
                  <LinkPreview
                    item={link}
                    GoToComponent={
                      <Tooltip title={t("chat.goto")}>
                        <Button
                          onClick={async () =>
                            dispatch(
                              await goToMessage({
                                id_search: link.message_id,
                                type: selectedConversation?.type,
                              })
                            )
                          }
                          block
                          icon={<RightOutlined />}
                          type="link"
                        />
                      </Tooltip>
                    }
                    fromInfosTab={true}
                  />
                </Suspense>
              </div>
            ))}
          </div>
        )}

        {loadingMore && (
          <Skeleton.Input size="large" block active className="my-2 py-2" />
        )}
      </div>
    </div>
  );
};

export const MembersChildren = ({
  listAllUsers,
  setAllListUsers,
  selectedConversation,
  selectedParticipants,
  currentUser,
  updateRoom,
  openId,
}) => {
  const [t] = useTranslation("common");
  const [search, setSearch] = useState("");
  const [searchMembersInInfo, setSearchMemberInfo] = useState("");

  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const [errorListUser, setErrorListUser] = useState(false);
  const handleCancel = () => {
    setAllListUsers((prev) => prev.map((el) => ({ ...el, selected: false })));
    setLoading(false);
    setErrorListUser(false);
    setOpen(false);
    setSearch("");
  };
  const handleOpenChange = (newOpen) => {
    setOpen(newOpen);
    if (!newOpen) {
      handleCancel();
    }
  };
  const finish = async (params) => {
    let time;
    if (params.users_ids.length > 0) {
      setLoading(true);

      try {
        setErrorListUser(false);

        const response = await MainService.InviteUsersToRoom({
          ...params,
          tab_id: getOrGenerateTabId(),
        });
        const users = listAllUsers
          .filter((el) => el.selected)
          .map((objet) => {
            const { selected, ...autresAttributs } = objet;
            return autresAttributs;
          });
        setOpen(false);

        setLoading(false);
        updateRoom(
          users,
          "inviteMembersTogroup",
          response?.data?.message_system_id
        );

        toastNotification(
          "success",
          t("chat.message_system.successAddMembers"),
          "topRight"
        );
        setSearch("");
        time = setTimeout(() => {
          setAllListUsers((prev) =>
            prev.map((el) => ({ ...el, selected: false }))
          );
          clearTimeout(time);
        }, 1000);
      } catch (err) {
        setLoading(false);
        toastNotification(
          "error",
          t("chat.error_message.failedAddMembers"),
          "topRight"
        );
      }
    } else {
      setErrorListUser(true);
    }
  };
  const setMembers = (e) => {
    setSearchMemberInfo(e.target.value.trimStart().replace(/\s{1,} /g, " "));
  };
  return (
    <div>
      <div className="mb-1 flex justify-between">
        <Title level={5}>
          <Badge className="mr-0.5">{selectedParticipants.length}</Badge>
          {t("chat.members")}
        </Title>
        <Popover
          content={
            <div className="flex flex-col space-y-2">
              <Input
                size="middle"
                placeholder={t("chat.searchSide.searchMembersToAdd")}
                prefix={<FiSearch className="text-slate-500" />}
                value={search}
                onChange={(e) =>
                  setSearch(e.target.value.trimStart().replace(/\s{1,} /g, " "))
                }
                className="w-full flex-1"
                allowClear
              />
              <Suspense fallback={<></>}>
                <MembersAdd
                  listAllUsers={listAllUsers}
                  setAllListUsers={setAllListUsers}
                  search={search}
                  add={true}
                />
              </Suspense>{" "}
              {errorListUser &&
              listAllUsers.filter((el) => el.selected).length === 0 ? (
                <div className="ant-form-item-explain-error text-[#ff4d4f]">
                  {t(t("chat.forward.error_select_user"))}
                </div>
              ) : (
                ""
              )}{" "}
              <Divider />
              <div className="flex justify-end space-x-2">
                <Button disabled={loading} onClick={handleCancel}>
                  {t("activities.cancel")}
                </Button>

                <Button
                  type="primary"
                  loading={loading}
                  onClick={() =>
                    finish({
                      users_ids: listAllUsers
                        .filter((el) => el.selected)
                        .map((el) => el?._id)
                        .join(","),
                      room_id: selectedConversation?.id,
                    })
                  }
                >
                  Ok
                </Button>
              </div>{" "}
            </div>
          }
          // title="Manage members"
          trigger="click"
          placement="topRight"
          open={open}
          onOpenChange={handleOpenChange}
        >
          {selectedConversation?.admin_id === currentUser?._id &&
          listAllUsers.length > 0 ? (
            <Button
              type="link"
              size="small"
              shape="circle"
              icon={<PlusOutlined />}
              onClick={() => setOpen(true)}
              // disabled={openId}
            >
              {t("chat.add")}
            </Button>
          ) : (
            ""
          )}
        </Popover>
      </div>
      <div className="mb-2 flex w-full items-center justify-between space-x-2">
        <Input
          onChange={setMembers}
          size="middle"
          style={{
            width: "100%",
          }}
          placeholder={t("chat.searchSide.searchMembers")}
          prefix={<FiSearch className="text-slate-500" />}
          value={searchMembersInInfo}
          allowClear
        />
      </div>
      <Members
        searchMembersInInfo={searchMembersInInfo}
        updateRoom={updateRoom}
        openId={openId}
      />
    </div>
  );
};

export const InfosChildren = ({ selectedConversation }) => {
  const [t] = useTranslation("common");
  const [clicked, setClicked] = useState(false);
  const { user } = useSelector((state) => state.user);

  const dispatch = useDispatch();
  return (
    <>
      <div className="px-4">
        <Descriptions
          layout="horizontal"
          title={t("chat.userInfos")}
          size="small"
          column={{
            xxl: 1,
            xl: 1,
            lg: 1,
            md: 1,
            sm: 1,
            xs: 1,
          }}
        >
          <Descriptions.Item label={t("chat.username")}>
            <Text
              copyable={{
                text: getName(selectedConversation?.name, "name"),
              }}
            >
              {" "}
              {getName(selectedConversation?.name, "name")}
            </Text>
          </Descriptions.Item>
          <Descriptions.Item label={t("chat.email")}>
            <Text
              copyable={{
                text: selectedConversation?.email,
              }}
            >
              {" "}
              {selectedConversation?.email}{" "}
            </Text>
            <Tooltip title={t("mailing.newMail")}>
              <Button
                size="small"
                type="link"
                disabled={user?.access["email"] === "0"}
                onClick={() => {
                  if (user?.access && user?.access["email"] === "1") {
                    dispatch(setOpenModalEmail(true));

                    const time = setTimeout(() => {
                      dispatch(setOpenEditor({ state: false, type: "" }));
                      clearTimeout(time);
                    }, 1);
                  }
                }}
                icon={<SendOutlined />}
              />
            </Tooltip>
          </Descriptions.Item>
          <Descriptions.Item
            contentStyle={{
              display: "flex",
              alignItems: "center",
            }}
            label={t("chat.telephone")}
          >
            <Text
              copyable={{
                text: selectedConversation?.post_number,
              }}
            >
              {" "}
              {selectedConversation?.post_number}{" "}
            </Text>
            <Tooltip title={t("chat.action.call")}>
              <Button
                className="p-0"
                type="link"
                disabled={clicked || !user?.extension}
                onClick={() => {
                  dispatch(
                    callApi({
                      setClicked: setClicked,
                      post_numberR: selectedConversation?.post_number,
                      errorText: t("toasts.errorFetchApi"),
                    })
                  );
                }}
                icon={<FiPhoneForwarded />}
              />
            </Tooltip>
          </Descriptions.Item>
        </Descriptions>
      </div>
    </>
  );
};
