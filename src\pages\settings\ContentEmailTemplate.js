import React, {
  useState,
  useEffect,
  useRef,
  useMemo,
  useLayoutEffect,
} from "react";
import {
  Button,
  Form,
  Input,
  Switch,
  Tabs,
  Badge,
  Spin,
  Typography,
  message,
  Segmented,
  Divider,
  Card,
  Collapse,
  Row,
  Col,
  Tag,
  Tooltip,
  Dropdown,
  Space,
  ConfigProvider,
  Modal,
  Select,
} from "antd";
import ReactQuill, { Quill } from "react-quill";
import ImageResize from "quill-image-resize-module-react";
import "react-quill/dist/quill.snow.css";

import { useTranslation } from "react-i18next";
// import quillEmoji from 'react-quill-emoji'
// import 'react-quill-emoji/dist/quill-emoji.css'
import MainService from "../../services/main.service";
import { toastNotification } from "../../components/ToastNotification";
import {
  DownOutlined,
  EyeInvisibleOutlined,
  EyeOutlined,
  InfoCircleOutlined,
  RestOutlined,
  SettingOutlined,
  SyncOutlined,
} from "@ant-design/icons";
import Confirm from "../../components/GenericModal";
import quillTable from "quill-table";
import "react-quill/dist/quill.snow.css";
// import "react-quill/dist/quill.bubble.css";
import quillEmoji from "react-quill-emoji";
import "react-quill-emoji/dist/quill-emoji.css";
import { URL_ENV } from "index";
import { displayRightIcon } from "utils/displayIcon";
import { useSelector } from "react-redux";
import parse from "html-react-parser";
import { debounce } from "lodash";
import { useNavigate } from "react-router-dom";
import { showNameOrg } from "new-redux/actions/configCompanies.actions/configCompaniesAction";
import { useDispatch } from "react-redux";
import ChoiceIcons from "pages/components/ChoiceIcons";
import EmailTemplateEditor from "./EmailTemplateEditor";
import EmailTemplateEditor2 from "./EmailTemplateEditor2";

Quill.register(quillTable.TableCell);
Quill.register(quillTable.TableRow);
Quill.register(quillTable.Table);
Quill.register(quillTable.Contain);
Quill.register("modules/table", quillTable.TableModule);
const maxRows = 8;
const maxCols = 5;

const tableOptions = [];
for (let r = 1; r <= maxRows; r++) {
  for (let c = 1; c <= maxCols; c++) {
    tableOptions.push("newtable_" + r + "_" + c);
  }
}

const ContentEmailTemplate = ({
  selectedNode,
  folders,
  setFolders,
  setSelectedNode,
  selectedGroup,
  setDisabled,
  disabled,
  fields,
  signature,
  senderSms,
  setDisabledButton,
  disabledBtn,
  setFields,
  tags,
}) => {
  const [t] = useTranslation("common");
  const [form] = Form.useForm();
  const { families } = useSelector((state) => state.families);
  const [loading, setLoading] = useState(false);
  const [loadingStatus, setLoadingStatus] = useState(false);
  const [loadingBtn, setLoadingBtn] = useState(false);
  const [showReset, setShowReset] = useState(false);
  const [showResetBodySms, setShowResetBodySms] = useState(false);
  const [open, setOpen] = useState(false);
  const [typeReset, setTypeReset] = useState("");

  const [showBtn, setShowBtn] = useState(false);
  const [RangeIndex, setRangeIndex] = useState(null);
  const [screenfull, setScreenFull] = useState(false);
  const [selectedField, setSelectedField] = useState("");
  const [isBlur, setIsBlur] = useState({ text: "", field: "" });
  const [ecrit, setEcrit] = useState("");
  const [posCursor, setPosCursor] = useState(0);
  const [modalText, setModalText] = useState("");
  const [modalTitle, setModalTitle] = useState("");
  const [mount, setMount] = useState(false);

  const [messageApi, contextHolder] = message.useMessage();
  const bodysms = useRef(null);
  const subjectMailRef = useRef(null);
  const quillRef = useRef(null);
  const { types } = useSelector((state) => state.types);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  Quill.register("modules/imageResize", ImageResize);
  Quill.register(
    {
      "formats/emoji": quillEmoji.EmojiBlot,
      "modules/emoji-toolbar": quillEmoji.ToolbarEmoji,
      "modules/emoji-textarea": quillEmoji.TextAreaEmoji,
      "modules/emoji-shortname": quillEmoji.ShortNameEmoji,
    },
    true
  );
  const Parchment = Quill.import("parchment");

  let config = { scope: Parchment.Scope.BLOCK };
  let SpanBlockClass = new Parchment.Attributor.Class(
    "span-block",
    "p",
    config
  );
  Quill.register(SpanBlockClass, true);
  // Quill.register('modules/imageResize', ImageResize)
  // console.log('ImageResize', ImageResize)
  // Quill.register(
  //   {
  //     'formats/emoji': quillEmoji.EmojiBlot,
  //     'modules/emoji-toolbar': quillEmoji.ToolbarEmoji,
  //     'modules/emoji-textarea': quillEmoji.TextAreaEmoji,
  //     'modules/emoji-shortname': quillEmoji.ShortNameEmoji,
  //   },
  //   true,
  // )

  const handleOk = async () => {
    try {
      let bodymail = selectedNode?.bodymail;
      let bodysms = selectedNode?.bodysms;

      if (typeReset === "bodyMail") {
        bodymail = selectedNode?.bodymail_system;
      } else if (typeReset === "bodySms") {
        bodysms = selectedNode?.bodysms_system;
      }
      setLoading(true);
      const res = await MainService.updateTemplateEmail(
        selectedNode.id,
        {
          ...form.getFieldsValue(),
          folder_id: selectedNode.folder_id,
          family_id: selectedGroup,
          bodymail,
          bodysms,
          // seeder: selectedNode.seeder,
        },
        config
      );
      if (typeReset === "bodyMail") {
        form.setFieldsValue({
          bodymail: selectedNode?.bodymail_system,
        });
      } else if (typeReset === "bodySms") {
        form.setFieldsValue({
          bodysms: selectedNode?.bodysms_system,
        });
      }
      const updatedTreeData = folders.map((node) => {
        node.children.map((doc) => {
          if (doc.id == res.data.message.id) {
            return (
              (doc.title = res.data.message.title),
              (doc.bodysms = res.data.message.bodysms),
              (doc.subject_sms = res.data.message.subject_sms),
              (doc.bodymail = res.data.message.bodymail),
              (doc.subject = res.data.message.subject),
              (doc.bodymail_system = res.data.message.bodymail_system)
            );
          }
        });

        return node;
      });
      setFolders(updatedTreeData);
      setSelectedNode(res.data.message);
      // if (typeReset === "bodyMail") {
      //   form.setFieldsValue({ bodymail: selectedNode?.bodymail_system });
      // } else if (typeReset === "bodySms") {
      //   form.setFieldsValue({ bodysms: selectedNode?.bodysms_system });
      // }

      // const updatedTreeData = folders.map((node) => {
      //   node.children.map((doc) => {
      //     if (doc.id == res.data.message.id) {
      //       return res.data.message;
      //     }
      //   });

      //   return node;
      // });
      // setFolders(updatedTreeData);
      setLoading(false);
      setOpen(false);
      setDisabled(true);

      if (typeReset === "bodyMail") {
        setShowReset(false);
        if (
          selectedNode?.bodysms_system?.trimEnd() ===
          res?.data?.message?.bodysms?.trimEnd()
        ) {
          setShowResetBodySms(false);
        }
        if (
          selectedNode.bodysms_system?.trimEnd() !==
          res?.data?.message?.bodysms?.trimEnd()
        ) {
          setShowResetBodySms(true);
        }
      } else if (typeReset === "bodySms") {
        setShowResetBodySms(false);
        if (
          res?.data?.message?.bodymail?.trimEnd() ===
          selectedNode.bodymail_system?.trimEnd()
        ) {
          setShowReset(false);
        }
        if (
          selectedNode.bodymail_system?.trimEnd() !==
          res?.data?.message?.bodymail?.trimEnd()
        ) {
          setShowReset(true);
        }
      }
      setTypeReset("");
      setTimeout(() => {
        setDisabled(true);
      }, 100);
      toastNotification(
        "success",
        t(`emailTemplates.template`) +
          '"' +
          form.getFieldValue().title +
          '"' +
          t(`wiki.PageEditedSuccessfully`),
        "topRight"
      );
      setDisabled(true);
    } catch (err) {
      setLoading(false);
      toastNotification("error", t("toasts.somethingWrong"), "topRight");
    }

    // setModalText('The modal will be closed after two seconds');
    // setConfirmLoading(true);
    // setTimeout(() => {
    //   setOpen(false);
    //   setConfirmLoading(false);
    // }, 2000);
  };
  const handleCancel = () => {
    console.log("Clicked cancel button");
    setOpen(false);
  };

  const handleKeyPressBodySms = (event) => {
    // Empêcher la saisie des caractères spéciaux en vérifiant le code de la touche pressée
    const charCode = event.which || event.keyCode;
    if (
      charCode === 33 ||
      (charCode >= 35 && charCode <= 47) ||
      (charCode >= 58 && charCode <= 126)
    ) {
      event.preventDefault();
    }
  };

  const imageHandler = () => {
    if (quillRef?.current != null) {
      const editor = quillRef.current.getEditor();

      const input = document.createElement("input");
      input.setAttribute("type", "file");
      input.setAttribute("accept", "image/*");
      input.click();
      input.onchange = async function () {
        const file = input.files[0];

        const formData = new FormData();
        formData.append("upload", file);
        formData.append("file_name", file?.name);

        MainService.uploadFile360(formData).then((res) => {
          const range = editor.getSelection();
          const link = `${
            URL_ENV?.REACT_APP_BASE_URL
            // process.env.REACT_APP_SUFFIX_AUTH_IMAGE_FILE
          }${res.data.message.path}`;

          // this part the image is inserted
          // by 'image' option below, you just have to put src(link) of img here.
          editor.insertEmbed(range.index, "image", link);
          form.getFieldsValue()?.title && setDisabled(false);
          // resolve(res.data.data);
        });
      }.bind(this);
    }
  };
  const modules = useMemo(
    () => ({
      table: true, /// disable table module
      tableUI: false,
      toolbar: {
        container: [
          [{ header: [1, 2, 3, 4, 5, 6, false] }],
          [{ font: [] }],
          [{ size: [] }],
          [{ color: [] }, { background: [] }],
          ["bold", "italic", "strike", "blockquote", "underline"],
          [
            { list: "ordered" },
            { list: "bullet" },
            { indent: "-1" },
            { indent: "+1" },
          ],
          [
            "link",
            "image",
            "video",
            { table: tableOptions },

            // { table: "append-row" },
            // { table: "append-col" },
          ],
          // ["deleteTable"],

          // ['emoji'],
          [{ align: [] }],
          ["clean"],
          ["code-block"],
          ["omega"],
        ],
        handlers: {
          image: imageHandler,
          omega: () => {
            screenfull ? setScreenFull(false) : setScreenFull(true);
          },
          deleteTable: (e, v) => {
            var tableaux = document.querySelectorAll("table");

            // Ajoutez un gestionnaire d'événements de clic à chaque tableau
            tableaux.forEach(function (tableau) {
              tableau.addEventListener("click", function (event) {
                // Récupérez tout le HTML du tableau sélectionné en utilisant outerHTML
                var htmlTableau = event.target.outerHTML;

                // Faites ce que vous voulez avec le HTML du tableau sélectionné
              });
            });
          },
        },
      },

      // imageUploader : {
      //   upload: file => {
      //     return new Promise((resolve, reject) => {
      //       const formData = new FormData();
      //       formData.append("upload", file);
      //       MainService.uploadImageWiki(formData)
      //         .then((res) => {
      //           console.log(res.data.message.path);
      //           resolve(`${URL_ENV?.REACT_APP_BASE_URL+process.env.REACT_APP_SUFFIX_BASE_URL_FILE_PATH}public/storage/uploads-wiki/${res.data.message.fileName}`)
      //           // resolve(res.data.data);
      //         })
      //         .catch((err) => {
      //           reject("Upload failed");
      //           console.log(err);
      //         });
      //     });
      //   }
      // }

      // 'emoji-toolbar': true,
      // 'emoji-textarea': true,
      // 'emoji-shortname': true,
    }),
    [screenfull]
  );
  const formats = [
    "header",
    "mention",
    "bold",
    "italic",
    "underline",
    "strike",
    "blockquote",
    "code-block",
    "color",
    "table",
    "font",
    "list",
    "bullet",
    "indent",
    "link",
    "image",
    "video",
    // "mention"
  ];
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === "Escape") {
        setScreenFull(false);
        // Ajoutez votre logique ici
      }
    };

    // Attache le gestionnaire d'événements à l'élément racine du composant
    document.addEventListener("keydown", handleKeyDown);

    // Nettoie le gestionnaire d'événements lorsque le composant est démonté
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, []);
  useLayoutEffect(() => {
    if (selectedNode.id == 7) {
      setFields((prev) => [
        ...prev?.filter(
          (el) => el.label !== t("emailTemplates.authentication")
        ),
        {
          label: t("emailTemplates.authentication"),
          fields: [
            { label: "token_url", alias: t("emailTemplates.recoveryLink") },
            // { label: "VM_DATE", alias: "VM_DATE" },
            // { label: "VM_DUR", alias: "VM_DUR" },
          ],
        },
      ]);
    } else if (selectedNode.id == 1) {
      setFields((prev) => [
        ...prev?.filter(
          (el) => el.label !== t("emailTemplates.authentication")
        ),
        {
          label: t("emailTemplates.authentication"),
          fields: [
            { label: "link", alias: t("emailTemplates.acceptInvi") },
            // { label: "VM_DATE", alias: "VM_DATE" },
            // { label: "VM_DUR", alias: "VM_DUR" },
          ],
        },
      ]);
    } else if (selectedNode.id == 6) {
      setFields((prev) => [
        ...prev?.filter(
          (el) => el.label !== t("emailTemplates.authentication")
        ),
        {
          label: t("emailTemplates.authentication"),
          fields: [
            { label: "full_name", alias: t("emailTemplates.fullName") },
            { label: "otp", alias: t("emailTemplates.otp") },
            { label: "name_app", alias: t("emailTemplates.nameApp") },
            // { label: "VM_DATE", alias: "VM_DATE" },
            // { label: "VM_DUR", alias: "VM_DUR" },
          ],
        },
      ]);
    } else {
      setFields((prev) => [
        ...prev?.filter(
          (el) => el.label !== t("emailTemplates.authentication")
        ),
      ]);
    }
    let word = /<\/table>(?!.*<\/table>)/g;

    form.setFieldsValue({
      // slugUrlValue: generateSlug(selectedNode?.title_fr),
      title: selectedNode?.title,
      bodysms: selectedNode?.bodysms,
      subject_sms: senderSms || selectedNode?.subject_sms,
      bodymail:
        selectedNode?.bodymail?.replace(word, "</table></br>") || "<p><br></p>",
      subject: selectedNode?.subject,
      bodymail_system:
        selectedNode?.bodymail_system?.replace(word, "</table></br>") ||
        "<p></br></p>",
      tags: Array.isArray(selectedNode?.tags)
        ? selectedNode?.tags.map((el) => el)
        : [],

      // pageNameFr: selectedNode?.title_fr,
      // pageNameEn: selectedNode?.title_en,
      // pageUrl: selectedNode?.name,
      // contentFr: selectedNode?.content_fr,
      // contentEn: selectedNode?.content_en,
      status: selectedNode?.status,
      //folderName: selectedNode?.folder_title,
      // folderName: selectedNode?.folder_title,
      //.replace(/\s/g, ''),
    });
  }, [form, selectedNode.id, senderSms]);
  useEffect(() => {
    document.querySelector(".ql-tooltip").style.opacity = 0;
    const adjustTooltipPosition = () => {
      const tooltipElement = document.querySelector(".ql-tooltip");
      if (tooltipElement && !tooltipElement.classList.contains("ql-hidden")) {
        const left = parseFloat(tooltipElement.style.left);
        if (left < 0) {
          tooltipElement.style.left = "0px";
          setTimeout(() => {
            tooltipElement.style.opacity = 1;
          }, 100);
        } else {
          tooltipElement.style.opacity = 1;
        }
      }
    };

    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === "childList") {
          adjustTooltipPosition();
        }
      });
    });

    observer.observe(document.body, { childList: true, subtree: true });

    return () => {
      observer.disconnect();
    };
  }, []);
  useEffect(() => {
    let bodymail_system = selectedNode?.bodymail_system || "<p><br></p>";
    setDisabled(true);
    setIsBlur({ text: "", field: "" });
    setEcrit("<p></p>");
    setTimeout(() => {
      setDisabledButton(true);

      if (bodymail_system?.trimEnd() === selectedNode.bodymail?.trimEnd()) {
        setShowReset(false);
      }
      if (
        selectedNode.bodysms_system?.trimEnd() ===
        selectedNode.bodysms?.trimEnd()
      ) {
        setShowResetBodySms(false);
      }
      if (
        selectedNode.bodysms_system?.trimEnd() !==
        selectedNode.bodysms?.trimEnd()
      ) {
        setShowResetBodySms(true);
      }
      if (bodymail_system?.trimEnd() !== selectedNode.bodymail?.trimEnd()) {
        setShowReset(true);
      }
      setDisabled(true);

      setShowBtn(true);
    }, 50);

    if (bodymail_system?.trimEnd() === selectedNode.bodymail?.trimEnd()) {
      setShowReset(false);
    }
    if (
      selectedNode.bodysms_system?.trimEnd() === selectedNode.bodysms?.trimEnd()
    ) {
      setShowResetBodySms(false);
    }
    if (
      selectedNode.bodysms_system?.trimEnd() !== selectedNode.bodysms?.trimEnd()
    ) {
      setShowResetBodySms(true);
    }
    if (bodymail_system?.trimEnd() !== selectedNode.bodymail?.trimEnd()) {
      setShowReset(true);
    }
    form.setFieldsValue({
      bodysms: selectedNode?.bodysms,
    });
    setMount(false);

    return () => {
      setShowBtn(false);
      setMount(false);
    };
  }, [selectedNode.id]);
  const onFinish = async (value) => {
    try {
      setLoading(true);

      const formData = new URLSearchParams();
      let word = /<\/table>(?!.*<\/table>)$/g;
      formData.append(
        "bodymail",
        `<html><body>${value?.bodymail
          ?.replace("table table_id", 'table border="1" table_id')
          ?.replace(word, "</table></br>")}</body></html>`
      );
      formData.append("folder_id", selectedNode.folder_id);
      formData.append("family_id", selectedGroup);
      formData.append("title", value?.title);
      formData.append("bodysms", value?.bodysms || "");
      formData.append("subject_sms", value?.subject_sms || "");
      formData.append("subject", value?.subject || "");
      formData.append("status", value?.status);

      if (value.tags && Array.isArray(value.tags) && value.tags.length > 0)
        value.tags.forEach((tagId) => {
          formData.append("tag_id[]", tagId);
        });

      // formData.append('seeder', selectedNode.seeder);
      // Append other properties if needed

      const res = await MainService.updateTemplateEmail(
        selectedNode.id,
        formData,
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      );

      const updatedTreeData = folders.map((node) => {
        node.children.map((doc) => {
          if (doc.id == res.data.message.id) {
            doc.title = res.data.message.title;
            doc.bodysms = res.data.message.bodysms;
            doc.subject_sms = res.data.message.subject_sms;
            doc.bodymail = res.data.message.bodymail;
            doc.subject = res.data.message.subject;
            doc.bodymail_system = res.data.message.bodymail_system;
            doc.tags =
              res.data.message.tags &&
              Array.isArray(res.data.message.tags) &&
              res.data.message.tags.map((el) => el.tag_id);
          }
        });

        return node;
      });

      setFolders(updatedTreeData);
      setLoading(false);
      setDisabled(true);
      setOpen(false);
      setTypeReset("");

      toastNotification(
        "success",
        t(`emailTemplates.template`) +
          '"' +
          form.getFieldValue().title +
          '"' +
          t(`wiki.PageEditedSuccessfully`),
        "topRight"
      );
    } catch (err) {
      setLoading(false);
      toastNotification("error", t("toasts.somethingWrong"), "topRight");
    }
  };
  const deleteTemplateEmail = (id, folderKey) => {
    // setLoading(true);
    MainService.deleteTemplateEmail(id)
      .then((res) => {
        const newTreeData2 = [...folders];
        newTreeData2.map((e) => {
          if (e.key == folderKey) {
            return (e.children = e.children.filter((el) => el.id !== id));
          }
        });
        setFolders(newTreeData2);
        if (selectedNode.id === id) setSelectedNode("");
        // setLoading(false);
      })
      .catch((err) => {
        // setLoading(false);
        console.log(err);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      });
  };

  const resetBodyMail = async () => {
    // setLoading(true);
    try {
      // const res = await MainService.resetBodyMailInTemplateEmail(
      //   selectedNode?.id
      // );
      form.setFieldsValue({ bodymail: selectedNode?.bodymail_system });
      // const updatedTreeData = folders.map((node) => {
      //   node.children.map((doc) => {
      //     if (doc.id == selectedNode.id) {
      //       return (
      //         (doc.title = res.data.message.title),
      //         (doc.bodysms = res.data.message.bodysms),
      //         (doc.subject_sms = res.data.message.subject_sms),
      //         (doc.bodymail = res.data.message.bodymail),
      //         (doc.subject = res.data.message.subject)
      //       );
      //     }
      //   });

      //   return node;
      // });
      // setFolders(updatedTreeData);
      setShowReset(false);
      setDisabled(false);
      setTimeout(() => {
        setDisabledButton(true);
        setShowReset(false);
      }, 50);

      // setLoading(false);
    } catch (err) {
      setLoading(false);

      toastNotification("error", t("toasts.somethingWrong"), "topRight");
    }
  };
  const resetBodySms = async () => {
    try {
      // const res = await MainService.resetBodyMailInTemplateEmail(
      //   selectedNode?.id
      // );
      form.setFieldsValue({ bodysms: selectedNode?.bodysms_system });
      // const updatedTreeData = folders.map((node) => {
      //   node.children.map((doc) => {
      //     if (doc.id == selectedNode.id) {
      //       return (
      //         (doc.title = res.data.message.title),
      //         (doc.bodysms = res.data.message.bodysms),
      //         (doc.subject_sms = res.data.message.subject_sms),
      //         (doc.bodymail = res.data.message.bodymail),
      //         (doc.subject = res.data.message.subject)
      //       );
      //     }
      //   });

      //   return node;
      // });
      // setFolders(updatedTreeData);
      setShowResetBodySms(false);
      setDisabled(false);
      setDisabledButton(true);
    } catch (err) {
      setLoading(false);

      toastNotification("error", t("toasts.somethingWrong"), "topRight");
    }
  };
  const onFinishFailed = (value) => {
    console.log(value);
  };
  const handleChange = (html, delta, source, editor) => {
    const regex = /<\/table>\s*$/;
    if (regex.test(html)) {
      form.setFieldsValue({
        bodymail: html + "<p><br><p></br>",
      });
    }
    let bodymail_system = selectedNode?.bodymail_system || "<p><br></p>";
    if (
      selectedNode.bodymail?.replace(/<p><br><\/p>$/, "") !==
        html?.replace(/<p><br><\/p>$/, "") &&
      !showReset
    ) {
      setTimeout(() => {
        setDisabled(false);
      }, -50);
    }
    if (html) {
      if (mount) {
        setTimeout(() => {
          setDisabled(false);
        }, -50);
      }
      // form.setFieldsValue({ bodymail: html });

      // console.log(selectedNode.bodymail, "-------", html);

      if (bodymail_system?.replace(/\n/g, "") !== html?.replace(/\n/g, "")) {
        setShowReset(true);
      } else if (
        bodymail_system?.replace(/\n/g, "") === html?.replace(/\n/g, "")
      ) {
        setShowReset(false);
      }
      if (!mount)
        setTimeout(() => {
          setMount(true);
        }, -50);
    }
  };
  const handleCursorPositionBodySms = () => {
    if (bodysms.current) {
      const textarea = bodysms.current.resizableTextArea.textArea;
      const startPos = textarea.selectionStart;
      const endPos = textarea.selectionEnd;
      setPosCursor(textarea.selectionEnd);

      // Si startPos === endPos, le curseur est simplement positionné (pas de sélection)
    }
  };
  const handleCursorPositionSubjectMail = () => {
    if (bodysms.current) {
      const input = subjectMailRef.current.input;
      const startPos = input.selectionStart;
      const endPos = input.selectionEnd;
      setPosCursor(input.selectionEnd);
      console.log("Position de départ:", startPos);
      console.log("Position de fin:", endPos);

      // Si startPos === endPos, le curseur est simplement positionné (pas de sélection)
    }
  };

  const onSelectField = (field) => {
    const fieldName = isBlur?.field;
    const regex = /<([A-Z][A-Z0-9]*)\b[^>]*>(.*?)<\/\1>/i;
    if ((!regex.test(ecrit) || ecrit === "") && regex !== "<p></p>") {
      if (
        (fieldName === "bodysms" &&
          (isBlur?.text + `{{${field}}}`).length > 160) ||
        (fieldName === "subject_sms" &&
          (isBlur?.text + `{{${field}}}`).length > 9)
      ) {
        messageApi.open({
          type: "warning",
          content: t("emailTemplates.limitCaracter"),
        });
        // setDisabledButton(true);
      } else {
        // if (fieldName === "bodysms") {
        form.setFieldsValue({
          [fieldName]:
            isBlur.text.substring(0, posCursor) +
            `{{${field}}}` +
            isBlur.text.substring(posCursor, isBlur.text.length),
        });
        if (fieldName === "bodysms") {
          setTimeout(() => {
            const textarea = bodysms.current?.resizableTextArea?.textArea;
            if (textarea) {
              textarea.focus();
              textarea.setSelectionRange(
                posCursor + field?.length + 4,
                posCursor + field?.length + 4
              );
            }
          }, 0);
        } else
          setTimeout(() => {
            const input = subjectMailRef.current.input;
            if (input) {
              input.focus();
              input.setSelectionRange(
                posCursor + field?.length + 4,
                posCursor + field?.length + 4
              );
            }
          }, 0);
        setDisabled(false);
        fieldName === "bodysms" && setShowResetBodySms(true);
        // setPosCursor(0);
      }
      //   else{
      //     form.setFieldsValue({ [fieldName]: isBlur.text + `{{${field}}}` });
      //   setDisabled(false);
      //   if (fieldName) form.getFieldInstance(fieldName)?.focus();
      // }
    }

    // bodysms.current.focus();
    if (ecrit === "<p></p>") {
      let inputElement;
      let divElement = document.querySelector("div.ql-tooltip.ql-editing");
      let quilElement = document.querySelector(".ql-container");
      let hiddenElement = document.querySelector(".ql-hidden");

      if (divElement) {
        setDisabledButton(false);
        inputElement = divElement.querySelector("input");

        // Vérifiez si l'élément existe
        if (inputElement) {
          // Définissez le texte à insérer
          var texte = "{{" + field + "}}";

          // Modifiez la valeur de l'élément input avec le texte souhaité
          inputElement.value = texte;
        }
      }
      if (hiddenElement) {
        const quillInstance = quillRef.current.getEditor();
        const range = quillInstance.getSelection();
        const lastCursorPosition = range ? range.index : 0;
        quillInstance.insertText(
          lastCursorPosition,
          "{{" + field + "}}",
          // "bold",
          true
        );
      }
    }
    setDisabled(false);

    // setSelectedField(field);
  };
  const onChangeSelection = (range) => {
    setEcrit("<p></p>");
    typeof range?.index === "number" && setDisabledButton(false);
  };
  const debounceOnChangeSelection = debounce(onChangeSelection, -1);

  const items = fields.map((field, index) => ({
    key: index + 1,
    label: (
      <div className="flex justify-between">
        <span>{field?.label}</span>
        {(field?.isField || field?.isCompany) && (
          <Tooltip
            title={
              field?.isField
                ? t("fields_management.moduleFieldRedirectBtn", {
                    moduleName: t("menu2.fields"),
                  })
                : t("emailTemplates.goToCompany", {
                    label: signature?.label,
                  })
            }
          >
            <Button
              type="link"
              className="flex"
              onClick={() =>
                field?.isField
                  ? navigate(
                      `/settings/fields/${
                        families.find((el) => el.id === selectedGroup)?.label
                      }`
                    )
                  : navigate(`/settings/general/companies/${signature.id}`)
              }
            >
              <SettingOutlined />
            </Button>
          </Tooltip>
        )}
      </div>
    ),

    children: (
      <div
        className="grid grid-cols-1 justify-between gap-1"
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        {field.fields.map((el) => (
          <Tooltip
            title={
              <div>
                <div>
                  {t("fields_management.field_label")}: {el.label}
                </div>
                {el.description ? (
                  <div>Description: {el.description}</div>
                ) : null}
              </div>
            }
          >
            <Button
              loading={loadingBtn}
              onClick={(e) => onSelectField(el.label)}
              disabled={disabledBtn}
              icon={displayRightIcon(
                types &&
                  types.find((element) => element?.id == el?.field_type_id)
                    ?.fieldType,
                2,
                4
              )}
              className="field-tempEmail flex items-center justify-start"
            >
              <span className="truncate">{el.alias}</span>
            </Button>
          </Tooltip>
        ))}
      </div>
    ),
  }));

  const handleMenuClick = (e) => {
    setLoadingStatus(true);
    MainService.updateTemplateEmail(
      selectedNode.id,
      {
        ...form.getFieldsValue(),
        folder_id: selectedNode.folder_id,
        family_id: selectedGroup,
        status: e.key,
        subject_sms:
          form.getFieldsValue()?.subject_sms || selectedNode?.subject_sms,
        bodymail: form.getFieldsValue()?.bodymail || selectedNode?.bodymail,
        subject: form.getFieldsValue()?.subject || selectedNode?.subject,
      },
      config
    )
      .then((res) => {
        const updatedTreeData = folders.map((node) => {
          node.children.map((doc) => {
            if (doc.id == res.data.message.id) {
              return (
                (doc.status = e.key),
                (doc.title = res.data.message.title),
                (doc.bodysms = res.data.message.bodysms),
                (doc.subject_sms = res.data.message.subject_sms),
                (doc.bodymail = res.data.message.bodymail),
                (doc.subject = res.data.message.subject),
                (doc.bodymail_system = res.data.message.bodymail_system)
              );
            }
          });

          return node;
        });
        setFolders(updatedTreeData);

        setSelectedNode({
          ...selectedNode,
          ...form.getFieldsValue(),
          status: e.key,
        });
        setLoadingStatus(false);
        setDisabled(true);
        toastNotification(
          "success",
          t(`emailTemplates.template`) +
            '"' +
            selectedNode.title +
            '"' +
            t(`wiki.PageEditedSuccessfully`),
          "topRight"
        );
      })
      .catch((err) => {
        setLoadingStatus(false);

        //setLoading(false)
        console.log(err);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      });
  };

  const items2 = [
    {
      label: (
        <div>
          <EyeOutlined /> &nbsp;{t("wiki.Published")}
        </div>
      ),
      value: "1",
      key: "1",
    },
    {
      label: (
        <div>
          <EyeInvisibleOutlined /> &nbsp;{t("wiki.Draft")}
        </div>
      ),
      value: "0",
      key: "0",
    },
  ];
  const menuProps = {
    items: items2.filter((el) => el.value != selectedNode.status),
    onClick: handleMenuClick,
  };
  const addTable = () => {
    const newTable = {
      rows: [
        { cells: ["Colonne 1", "Colonne 2"] },
        { cells: ["Ligne 1", "Ligne 2"] },
      ],
    };
    form.setFieldsValue({ bodymail: newTable });
  };
  return (
    <div>
      {contextHolder}

      <Spin spinning={open ? false : loadingStatus || loading} size="medium">
        <Form
          name="formItemPath"
          layout="vertical"
          onFinish={onFinish}
          onFinishFailed={onFinishFailed}
          //   validateMessages={validateMessages}
          form={form}
          // id="form"
          loading={loading}
          onValuesChange={(t, r) => {
            if (!r.title) {
              setDisabled(true);
            } else if (
              // r.bodymail !== selectedNode?.bodymail ||
              r.bodysms !== selectedNode?.bodysms ||
              r.subject !== selectedNode.subject ||
              r.subject_sms !== (senderSms || selectedNode.subject_sms) ||
              r.title !== selectedNode.title
            ) {
              setDisabled(false);
            }
          }}
        >
          <div
            className="sticky top-0 z-50 flex justify-between bg-white"
            // style={{ backgroundColor: "white", zIndex: 99 }}
          >
            <div></div>

            <div className="flex ">
              <div className="pr-2">
                {selectedNode?.seeder == 1 ? (
                  <Form.Item
                    name="status"
                    // className="relative ml-0"
                    valuePropName="checked"
                  >
                    <Badge
                      status="success"
                      text={
                        <span className=" text-[#52c41a]">
                          {t("wiki.Published")}
                        </span>
                      }
                    />
                  </Form.Item>
                ) : (
                  <Form.Item
                    name="status"
                    // className="relative ml-0"
                    valuePropName="checked"
                  >
                    <ConfigProvider
                      theme={{
                        token: {
                          // Seed Token
                          colorPrimary:
                            selectedNode.status == 1 ? "#3CC3A5" : "#DF4F37",
                          // borderRadius: 2,

                          // Alias Token
                        },
                      }}
                    >
                      <Dropdown menu={menuProps} trigger={["click"]}>
                        <Button
                          type="primary"
                          // type="dashed"
                          // className={`${
                          //   selectedNode.status == 1
                          //     ? "bg-green-600 text-white hover:bg-green-700"
                          //     : "bg-orange-500 text-white hover:bg-orange-600"
                          // }`}
                          icon={
                            selectedNode.status == 1 ? (
                              <EyeOutlined />
                            ) : (
                              <EyeInvisibleOutlined />
                            )
                          }
                        >
                          <Space>
                            {selectedNode.status == 1
                              ? t("wiki.Published")
                              : t("wiki.Draft")}
                            <DownOutlined />
                          </Space>
                        </Button>
                      </Dropdown>
                    </ConfigProvider>
                  </Form.Item>
                )}
              </div>
              <div className="pr-2">
                <Button
                  disabled={disabled}
                  // loading={loading}
                  type="primary"
                  htmlType="submit"
                  onClick={(e) => e.stopPropagation()}
                >
                  {t(`wiki.Save`)}
                </Button>
              </div>
              <div>
                <Button
                  danger
                  type="primary"
                  disabled={selectedNode?.seeder == 1}
                  onClick={(e) => {
                    e.stopPropagation();
                    Confirm(
                      `${t(`wiki.Delete`)} "${selectedNode?.title}" `,
                      t(`wiki.Confirm`),
                      <RestOutlined style={{ color: "red" }} />,
                      function func() {
                        return MainService.deleteTemplateEmail(selectedNode?.id)
                          .then((res) => {
                            const newTreeData2 = [...folders];
                            newTreeData2.map((e) => {
                              if (e.key == selectedNode?.folder_id) {
                                return (e.children = e.children.filter(
                                  (el) => el.id !== selectedNode?.id
                                ));
                              }
                            });
                            setFolders(newTreeData2);
                            if (selectedNode.id === selectedNode?.id)
                              setSelectedNode("");
                            // setLoading(false);
                          })
                          .catch((err) => {
                            // setLoading(false);
                            if (err.response.status === 422) {
                              toastNotification(
                                "error",
                                t("emailTemplates.notDeletePrimaryTemplate"),
                                "topRight"
                              );
                            } else
                              toastNotification(
                                "error",
                                t("toasts.somethingWrong"),
                                "topRight"
                              );
                          });
                      },
                      true
                    );
                  }}
                >
                  {t(`wiki.Delete`)}
                </Button>
              </div>
            </div>
          </div>
          {/* <Row gutter={16}>
            <Col md={24} lg={17}>
              <div className="flex justify-end ">
                <div className="pr-2">
                  <Form.Item
                    name="status"
                    // className="relative ml-0"
                    valuePropName="checked"
                  >
                    <Dropdown menu={menuProps} trigger={["click"]}>
                      <Button type="dashed">
                        <Space>
                          {selectedNode.status == 1
                            ? t("wiki.Published")
                            : t("wiki.Draft")}
                          <DownOutlined />
                        </Space>
                      </Button>
                    </Dropdown>
                  </Form.Item>
                </div>
                <div className="pr-2">
                  <Button
                    disabled={disabled}
                    loading={loading}
                    type="primary"
                    htmlType="submit"
                  >
                    {t(`wiki.Save`)}
                  </Button>
                </div>
                <div>
                  <Button
                    danger
                    type="primary"
                    onClick={() => {
                      Confirm(
                        `${t(`wiki.Delete`)} "${selectedNode?.title}" `,
                        t(`wiki.Confirm`),
                        <RestOutlined style={{ color: "red" }} />,
                        function func() {
                          return MainService.deleteTemplateEmail(
                            selectedNode?.id
                          )
                            .then((res) => {
                              const newTreeData2 = [...folders];
                              newTreeData2.map((e) => {
                                if (e.key == selectedNode?.folder_id) {
                                  return (e.children = e.children.filter(
                                    (el) => el.id !== selectedNode?.id
                                  ));
                                }
                              });
                              setFolders(newTreeData2);
                              if (selectedNode.id === selectedNode?.id)
                                setSelectedNode("");
                              // setLoading(false);
                            })
                            .catch((err) => {
                              // setLoading(false);
                              if (err.response.status === 422) {
                                toastNotification(
                                  "error",
                                  t("emailTemplates.notDeletePrimaryTemplate"),
                                  "topRight"
                                );
                              } else
                                toastNotification(
                                  "error",
                                  t("toasts.somethingWrong"),
                                  "topRight"
                                );
                            });
                        },
                        true
                      );
                    }}
                  >
                    {t(`wiki.Delete`)}
                  </Button>
                </div>
              </div>
            </Col>
            <Col md={0} lg={7}>
              <Typography.Title level={5}>
                {t("fields_management.field_type_label")}
              </Typography.Title>
            </Col>
          </Row> */}
          <Row gutter={16}>
            <Col
              md={24}
              lg={fields.length > 0 ? 17 : 24}
              style={{
                height: "calc(100vh - 125px)",
                width: "100%",
              }}
              className=" overflow-y-auto"
            >
              {selectedNode?.seeder == 1 ? (
                <Form.Item label={t("fields_management.field_label")}>
                  <Input value={selectedNode?.alias} disabled={true} />
                </Form.Item>
              ) : null}
              <Form.Item
                label={t("table.header.title")}
                name="title"
                rules={[
                  {
                    required: true,
                    // message: "Please input your username!",
                  },
                ]}
              >
                <Input
                  onFocus={() => {
                    setIsBlur({ text: "", field: "" });
                  }}
                  onClick={(e) => {
                    setIsBlur({ text: "", field: "" });
                    // e.key.trim() !== "" &&
                    setEcrit("");
                    setDisabledButton(true);
                  }}
                />
              </Form.Item>
              {selectedNode.seeder != 1 ? (
                <>
                  <Divider orientation="left">{t("menu2.tags")} </Divider>
                  <Form.Item
                    label={t("voip.qualification")}
                    name="tags"
                    // rules={[
                    //   {
                    //     required: true,
                    //     // message: t("emailTemplates.plsSelect"),
                    //   },
                    // ]}
                  >
                    <Select
                      mode="multiple"
                      allowClear
                      style={{
                        width: "100%",
                      }}
                      placeholder={t("emailTemplates.plsSelect")}
                      onChange={() => setDisabled(false)}
                      filterOption={(input, option) =>
                        (Array.isArray(tags)
                          ? tags
                              .find((el) => el.id === option?.value)
                              ?.label?.toLowerCase() ?? ""
                          : ""
                        ).includes(input.toLowerCase())
                      }
                      options={tags.map((el) => ({
                        label: (
                          <div
                            className={`flex items-center space-x-2`}
                            style={{ color: el.color }}
                          >
                            <ChoiceIcons icon={el.icon} />{" "}
                            <span>{el.label}</span>
                          </div>
                        ),
                        value: el.id,
                      }))}
                    />
                  </Form.Item>
                </>
              ) : null}

              {selectedGroup !== "voip" && (
                <>
                  <Divider orientation="left">Sms </Divider>
                  <Form.Item
                    className="senderSmsTemplateEmail"
                    label={
                      <div className="flex w-full items-center justify-between">
                        <div>
                          {t("emailTemplates.smsHeader")}{" "}
                          <Tooltip
                            title={t("companies.infoSenderSms", {
                              label: signature?.label,
                            })}
                            overlayInnerStyle={{
                              width: "100%",
                              minWidth: "320px",
                            }}
                          >
                            <InfoCircleOutlined />
                          </Tooltip>
                        </div>
                        <Tooltip
                          title={t("emailTemplates.goToSettings", {
                            label: signature?.label,
                          })}
                          overlayInnerStyle={{
                            width: "100%",
                            minWidth: "250px",
                          }}
                        >
                          <Button
                            type="link"
                            onClick={() => {
                              navigate(
                                `/settings/general/companies/${signature?.id}/signature`
                              );
                              dispatch(showNameOrg(signature.label));
                            }}
                          >
                            <SettingOutlined />
                          </Button>
                        </Tooltip>
                      </div>
                    }
                    name="subject_sms"
                  >
                    <Input
                      id="smsHeader"
                      // maxLength={9}
                      onBlur={(e) =>
                        setIsBlur({
                          text: e.target.value,
                          field: "subject_sms",
                        })
                      }
                      onFocus={() => setSelectedField("")}
                      onKeyPress={(e) => {
                        // e.key.trim() !== "" &&
                        setEcrit(e.target.value);
                      }}
                      disabled={true}
                      onClick={() => {
                        setEcrit("");
                        setDisabledButton(true);
                      }}
                    />
                  </Form.Item>
                  <Form.Item
                    className="senderSmsTemplateEmail"
                    label={
                      <div className="flex w-full items-center justify-between gap-3">
                        <span>{t("emailTemplates.smsContent")}</span>

                        <Button
                          className={`${
                            selectedNode?.seeder == 1 &&
                            showResetBodySms &&
                            showBtn
                              ? "visible "
                              : "invisible"
                          }`}
                          icon={<SyncOutlined />}
                          onClick={() => {
                            setModalText(t("emailTemplates.resetBodySms"));
                            setModalTitle(
                              t("emailTemplates.titleResetBodySms")
                            );
                            setTypeReset("bodySms");
                            setOpen(true);
                          }}
                        >
                          {t("localisation.reset")}
                        </Button>
                      </div>
                    }
                    name="bodysms"
                  >
                    <Input.TextArea
                      ref={bodysms}
                      onSelect={handleCursorPositionBodySms}
                      showCount
                      id="bodySms"
                      maxLength={160}
                      style={{
                        height: 80,
                        resize: "none",
                      }}
                      onBlur={(e) =>
                        setIsBlur({ text: e.target.value, field: "bodysms" })
                      }
                      onChange={(e) => {
                        e.target.value?.replace(/[^\w\s.,!?{}]+/g, "") !==
                        selectedNode?.bodysms_system
                          ? setShowResetBodySms(true)
                          : setShowResetBodySms(false);
                        form.setFieldsValue({
                          bodysms: e.target.value?.replace(
                            /[^\w\s.,!?{}]+/g,
                            ""
                          ),
                        });
                      }}
                      onClick={() => {
                        setDisabledButton(false);
                        setEcrit("");
                      }}
                      onFocus={() => setSelectedField("")}
                    />
                  </Form.Item>
                </>
              )}
              <Divider orientation="left">Email </Divider>
              <Form.Item
                className="senderSmsTemplateEmail"
                // name={["company", "sender_name"]}

                label={
                  <div className="flex w-full items-center justify-between">
                    <span>
                      {t("companies.senderName")}{" "}
                      <Tooltip
                        title={t("companies.infoSenderEmail", {
                          label: signature?.label,
                        })}
                        overlayInnerStyle={{
                          width: "100%",
                          minWidth: "400px",
                        }}
                      >
                        <InfoCircleOutlined />
                      </Tooltip>
                    </span>
                    <Tooltip
                      title={t("emailTemplates.goToSettings", {
                        label: signature?.label,
                      })}
                      overlayInnerStyle={{
                        width: "100%",
                        minWidth: "250px",
                      }}
                    >
                      <Button
                        type="link"
                        onClick={() => {
                          navigate(
                            `/settings/general/companies/${signature?.id}/signature`
                          );
                          dispatch(showNameOrg(signature.label));
                        }}
                      >
                        <SettingOutlined />
                      </Button>
                    </Tooltip>
                  </div>
                }
                // initialValue={company?.sender_name || ""}
                rules={[
                  {
                    required: true,
                    message: `${t("companies.senderName")} ${t(
                      "table.header.isrequired"
                    )}`,
                  },
                ]}
              >
                <Input value={signature?.sender_name} disabled />
              </Form.Item>
              <Form.Item label={t("emailTemplates.subjectMail")} name="subject">
                <Input
                  onBlur={(e) =>
                    setIsBlur({ text: e.target.value, field: "subject" })
                  }
                  onFocus={(e) => {
                    setSelectedField("");
                    setEcrit(e.target.value);
                  }}
                  ref={subjectMailRef}
                  id="subjectMail"
                  onKeyPress={(e) => {
                    // e.key.trim() !== "" &&
                    setEcrit(e.target.value);
                  }}
                  onSelect={handleCursorPositionSubjectMail}
                  onClick={() => {
                    setDisabledButton(false);
                    setEcrit("");
                  }}
                />
              </Form.Item>
              <EmailTemplateEditor2 />
              <Form.Item
                className="senderSmsTemplateEmail"
                label={
                  <div className="flex w-full items-center justify-between gap-3">
                    <span>{t("emailTemplates.bodymail")}</span>

                    <Button
                      className={`${
                        selectedNode?.seeder == 1 && showReset && showBtn
                          ? "visible"
                          : "invisible"
                      }`}
                      icon={<SyncOutlined />}
                      onClick={() => {
                        setModalText(t("emailTemplates.resetBodyMail"));
                        setModalTitle(t("emailTemplates.titleResetBodyMail"));
                        setTypeReset("bodyMail");
                        setOpen(true);
                      }}
                      cancelText={t("form.cancel")}
                    >
                      {t("localisation.reset")}
                    </Button>
                  </div>
                }
                name="bodymail"
                // initialValue="<p><br></p>"
                id="quilTemp"
              >
                {/* <EmailTemplateEditor /> */}

                <ReactQuill
                  theme="snow"
                  ref={quillRef}
                  bounds="#editor"
                  // value={contentFr}
                  modules={modules}
                  // formats={formats}
                  className="editorTemplate"
                  style={{
                    position: screenfull ? "fixed" : "static",
                    height: screenfull ? "auto" : "100%",
                    minHeight: "200px",
                    zIndex: screenfull ? 1000 : 0,
                    top: 0,
                    left: 0,
                    bottom: 0,
                    right: 0,
                    overflow: "auto",
                    // maxHeight: "100vh",
                    background: screenfull ? "white" : "transparent",
                  }}
                  onChange={handleChange}
                  // onChangeSelection={(range, t, v) => {
                  //   if (document.getElementsByTagName("table")[0]) {
                  //     document
                  //       .getElementsByTagName("table")[0]
                  //       .addEventListener("click", (e) => {
                  //         if (
                  //           v
                  //             .getHTML()
                  //             .includes(
                  //               `<p><span style="color: rgb(230, 0, 0);">x</span></p>${
                  //                 e.target.closest("table").outerHTML
                  //               }`
                  //             )
                  //         ) {
                  //         } else {
                  //           form.setFieldsValue({
                  //             bodymail: v
                  //               .getHTML()
                  //               .replace(
                  //                 e.target.closest("table").outerHTML,
                  //                 `<p><span style="color: rgb(230, 0, 0);">x</span></p>${
                  //                   e.target.closest("table").outerHTML
                  //                 }`
                  //               ),
                  //           });
                  //         }
                  //       });
                  //   }

                  //   // debounceOnChangeSelection(range);
                  // }}
                  onBlur={(range) => {
                    setIsBlur({ text: "", field: "bodymail" });
                    if (quillRef.current) {
                      // quillInstance.insertText(
                      //   range.index,
                      //   "{{ttt}}",
                      //   "bold",
                      //   true
                      // );
                      setRangeIndex(range.index);
                    }
                  }}
                  onKeyPress={(e) => {
                    // e.key.trim() !== "" &&
                    // setShowReset(true);
                    setEcrit("<p></p>");
                    setDisabledButton(false);

                    setDisabled(false);
                  }}
                  onFocus={() => {
                    setEcrit("<p></p>");
                  }}
                />
                {/* <QuilWithTable /> */}
                {/* <BodyMailTemplate form={form} selectedPage={selectedNode} /> */}
              </Form.Item>
              {selectedNode?.seeder == 1 &&
              signature?.signature_system_email ? (
                <Form.Item
                  className="senderSmsTemplateEmail"
                  label={
                    <div className="flex w-full items-center justify-between">
                      <span>
                        Signature{" "}
                        <Tooltip
                          title={t("companies.infoSignSms", {
                            label: signature?.label,
                          })}
                          overlayInnerStyle={{
                            width: "100%",
                            minWidth: "400px",
                          }}
                        >
                          <InfoCircleOutlined />
                        </Tooltip>
                      </span>
                      <Tooltip
                        title={t("emailTemplates.goToSettings", {
                          label: signature?.label,
                        })}
                        overlayInnerStyle={{
                          width: "100%",
                          minWidth: "250px",
                        }}
                      >
                        <Button
                          type="link"
                          onClick={() => {
                            navigate(
                              `/settings/general/companies/${signature?.id}/signature`
                            );
                            dispatch(showNameOrg(signature.label));
                          }}
                        >
                          <SettingOutlined />
                        </Button>
                      </Tooltip>
                    </div>
                  }
                >
                  <div className=" min-h-[30px]  rounded-md border  border-dashed border-gray-300  p-1 text-start">
                    {parse(signature?.signature_system_email)}
                  </div>
                </Form.Item>
              ) : null}
            </Col>

            <Col
              md={fields.length > 0 ? 24 : 0}
              lg={fields.length > 0 ? 7 : 0}
              className="space-y-2"
            >
              {/* <Tabs
                defaultActiveKey="1"
                type="card"
                items={[
                  {
                    key: "1",
                    label: t("menu2.fields"),

                    children: (
                      <>
                        <Typography.Title level={5}>
                          {t("fields_management.field_type_label")}
                        </Typography.Title>
                        <div
                          className=" max-h-[calc(100vh-200px)] w-full gap-4  overflow-auto pb-2"
                          onClick={(e) => {
                            e.stopPropagation();
                          }}
                        >
                          <Collapse items={items} defaultActiveKey={[1]} />
                        </div>
                      </>
                    ),
                  },
                  {
                    key: "2",
                    label: t("companies.principalCompany"),
                    children: (
                      <Space direction="vertical" style={{ width: "100%" }}>
                        <Button className="w-full" >
                          {t("companies.socialreason")}
                        </Button>
                        <Button className="w-full">
                          {t("companies.adress")}
                        </Button>
                        <Button className="w-full">
                          {t("companies.adress")}
                        </Button>
                        <Button className="w-full">{t("tags.icon")} </Button>
                        <Button className="w-full">
                          {t("companies.city")}
                        </Button>
                        <Button className="w-full">
                          {t("companies.postalCode")}
                        </Button>
                        <Button className="w-full">
                          {t("companies.country")}
                        </Button>
                        <Button className="w-full">
                          {t("companies.webSite")}
                        </Button>
                        <Button className="w-full"> Email </Button>
                      </Space>
                    ),
                  },
                ]}
                onChange={() => {}}
              /> */}
              <Typography.Title level={5}>
                {t("fields_management.field_type_label")}
              </Typography.Title>
              <div
                className=" max-h-[calc(100vh-155px)] w-full gap-4  overflow-auto pb-2"
                onClick={(e) => {
                  e.stopPropagation();
                }}
              >
                <Collapse items={items} defaultActiveKey={[1]} />
              </div>
            </Col>
          </Row>
        </Form>
      </Spin>
      <Modal
        title={modalTitle}
        open={open}
        onOk={handleOk}
        confirmLoading={loading}
        onCancel={handleCancel}
      >
        <p>{modalText}</p>
      </Modal>
      {/* <div className="mt-5 min-h-[400px]">
        <Editorr />
      </div> */}
    </div>
  );
};

export default ContentEmailTemplate;
