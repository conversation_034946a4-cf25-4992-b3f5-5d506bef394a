import { useEffect, useState } from "react";
import {
  CloseOutlined,
  DeleteOutlined,
  LogoutOutlined,
  QuestionCircleOutlined,
} from "@ant-design/icons";
import {
  Typography,
  Button,
  Tabs,
  Popconfirm,
  message,
  Tooltip,
  Spin,
  Skeleton,
} from "antd";
import {
  setAssetsCount,
  setChatSelectedConversation,
  setChatSelectedParticipants,
  setDataConversation,
  setDocumentsListChatInfo,
  setLinksListChatInfo,
  setMediasListChatInfo,
  setMembreChatParticipantsDate,
  setOpenDrawer,
  setOpenQuitGroupModal,
} from "new-redux/actions/chat.actions";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import {
  DocumentsChildren,
  InfosChildren,
  LinksChildren,
  MediasChildren,
  MembersChildren,
} from ".";
import { AvatarChat } from "components/Chat";
import {
  accepetedExtentionImage,
  formatAssetsCount,
  getName,
  objectNewMessage,
} from "../../utils/ConversationUtils";
import { updateMessages } from "../../utils/rqUpdate";
import {
  archiveRoom,
  leaveParticpant,
  updateRoomInfosChat,
} from "new-redux/services/chat.services";
import { toastNotification } from "components/ToastNotification";
import { updateParticipantsList } from "../../utils/infoRoom";
import MainService from "services/main.service";
import useGetInfoDiscussion from "../../hooks/useGetInfoDiscussion";
import { URL_ENV } from "index";
import { isGuestConnected } from "utils/role";
const { Paragraph } = Typography;

const loadingState = {
  default: "default",
  name: "name",
  description: "description",
  image: "image",
};
function ChatInfo() {
  const [loading, setLoading] = useState(loadingState.default);

  const { userList } = useSelector((state) => state.chat);
  const [listAllUsers, setAllListUsers] = useState(
    userList.map((el) => ({ ...el, selected: false }))
  );

  const dispatch = useDispatch();
  const { status, fetchStatus } = useGetInfoDiscussion();
  const [t] = useTranslation("common");
  let roomNewTitle = "";
  let roomNewDescription = "";
  const [defaultDescription, setDefaultDescription] = useState("Description");
  const { currentUser, assetsCount, selectedParticipants } = useSelector(
    (state) => state.chat
  );
  const selectedConversation = useSelector(
    (state) => state.ChatRealTime.selectedConversation
  );
  const sphereUserRole = useSelector((state) => state?.user?.user?.role);
  //const [isPending, startTransition] = useTransition();
  const [tabIndex, setTabIndex] = useState("1");

  const closeChatInfoSection = () => {
    if (selectedConversation?.external) {
      dispatch(
        setChatSelectedConversation({
          selectedConversation: null,
        })
      );
    }
    dispatch(setOpenDrawer({ type: "" }));
  };

  const [open, setOpen] = useState(false);
  const [openDelete, setOpenDelete] = useState(false);

  // const [openPopConfirmAdmin, setOpenConfirmAdmin] = useState(false);
  const [loadingLeaveParticipant, setLoadingLeaveParticipant] = useState(false);
  const [loadingDelete, setLoadingDelete] = useState(false);

  const handleLeaveParticipant = async () => {
    setLoadingLeaveParticipant(true);

    try {
      setLoadingLeaveParticipant(false);
      await dispatch(
        leaveParticpant({
          room_id: selectedConversation?.id,
        })
      );
      dispatch(setOpenDrawer({ type: "" }));

      dispatch(
        setChatSelectedConversation({
          selectedConversation: null,
        })
      );

      toastNotification(
        "success",
        t("chat.message_system.leaveSuccessGroup"),
        "topRight"
      );
    } catch (err) {
      toastNotification(
        "error",
        t("chat.message_system.successAddMembers"),
        "topRight"
      );
    }
  };
  const handleCancelLeaveParticipant = () => {
    currentUser?._id !== selectedConversation?.admin_id
      ? setOpen(false)
      : dispatch(setOpenQuitGroupModal(false));
  };

  const updateRoomFunction = async (data, type, external) => {
    let type_message = "";
    let message_id;
    let isError = false;
    try {
      if (["name", "description", "image"].includes(type)) {
        setLoading(loadingState[type]);
        const response = await dispatch(
          updateRoomInfosChat({
            id: selectedConversation?.id,

            name: type === "name" ? data : selectedConversation?.name,
            description:
              type === "description" ? data : selectedConversation?.description,
            image: type === "image" ? data : selectedConversation?.image,
          })
        );
        message_id = response.message_system_id;
        setDefaultDescription(response?.room?.description ? "" : "Description");
        setLoading(loadingState.default);

        type_message = "message_system_update_room";
      } else {
        switch (type) {
          case "inviteMembersTogroup":
            message_id = external;
            dispatch(
              setChatSelectedParticipants({
                selectedParticipants: [...selectedParticipants, ...data],
              })
            );
            updateParticipantsList(
              [...selectedParticipants, ...data],
              selectedConversation?.id,
              "room"
            );

            dispatch(
              setMembreChatParticipantsDate({
                id: selectedConversation?.id,
                conversationId: selectedConversation?.conversationId,
              })
            );
            type_message = "message_system_add_user_room";

            break;
          case "removeMembersTogroup":
            message_id = external;
            dispatch(
              setChatSelectedParticipants({
                selectedParticipants: selectedParticipants.filter(
                  (el) => el._id !== data
                ),
              })
            );
            updateParticipantsList(
              selectedParticipants.filter((el) => el._id !== data),
              selectedConversation?.id,
              "room"
            );

            dispatch(
              setMembreChatParticipantsDate({
                id: selectedConversation?.id,
                conversationId: selectedConversation?.conversationId,
              })
            );
            type_message = "message_system_remove_user_room";

            break;
          case "admin":
            message_id = external;
            type_message = "message_system_update_room";
            break;
          default:
            break;
        }
      }
    } catch (e) {
      isError = true;
      console.log(e);
      toastNotification("error", t("toasts.errorFetchApi"), "topRight");
      setLoading(loadingState.default);
    }

    //create object of sending msg
    if (isError) return;
    let new_message = objectNewMessage(
      selectedConversation?.type,
      message_id,
      currentUser,
      selectedConversation,
      [],
      type_message === "message_system_add_user_room"
        ? data?.map((item) => item._id).toString()
        : data?.toString(),
      [],
      false,
      type_message,
      0
    );

    // display msg in conversation
    updateMessages(
      new_message,
      "new_message_event",
      null,
      selectedConversation?.id,
      selectedConversation?.type,
      selectedConversation?.admin_id,
      selectedConversation?.conversationId
    );
  };

  const onChangeTab = (key) => {
    //startTransition(() => {
    setTabIndex(key);
    //  });
  };

  let items = [
    {
      key: "2",
      label: (
        <span className="text-[12px]">
          {t("chat.medias")} (
          {assetsCount.images !== undefined
            ? formatAssetsCount(assetsCount.images)
            : "--"}
          )
        </span>
      ),
      children: <MediasChildren />,
    },
    {
      key: "3",
      label: (
        <span className="text-[12px]">
          {t("chat.documents")} (
          {assetsCount.documents !== undefined
            ? formatAssetsCount(assetsCount.documents)
            : "--"}
          )
        </span>
      ),
      children: <DocumentsChildren />,
    },
    {
      key: "4",
      label: (
        <span className="text-[12px]">
          {t("chat.links")} (
          {assetsCount.links !== undefined
            ? formatAssetsCount(assetsCount.links)
            : "--"}
          )
        </span>
      ),
      children: <LinksChildren />,
    },
  ];

  if (selectedConversation?.type === "room") {
    items.unshift({
      key: "1",
      label: <span className="text-xs">{t("chat.members")}</span>,
      children: (
        <MembersChildren
          selectedParticipants={selectedParticipants}
          selectedConversation={selectedConversation}
          listAllUsers={listAllUsers
            .filter(
              (obj1) =>
                !selectedParticipants.some((obj2) => obj2._id === obj1._id)
            )
            .map((obj) => ({ ...obj }))}
          setAllListUsers={setAllListUsers}
          currentUser={currentUser}
          search=""
          updateRoom={updateRoomFunction}
        />
      ),
    });
  }

  if (selectedConversation && selectedConversation?.type === "user") {
    items.unshift({
      key: "1",
      label: <span className="text-xs"> {t("chat.infos")}</span>,
      children: <InfosChildren selectedConversation={selectedConversation} />,
    });
  }

  useEffect(() => {
    let mount = true;
    const getRoomMediaCount = async () => {
      dispatch(setMediasListChatInfo([]));
      dispatch(setDocumentsListChatInfo([]));
      dispatch(setLinksListChatInfo([]));
      try {
        const totalCount = await MainService.getTotalMediaFiles(
          selectedConversation?.conversationId,
          selectedConversation?.type
        );

        dispatch(
          setAssetsCount({
            images: totalCount.data.total_medias,
            documents: totalCount.data.total_files,
            links: totalCount.data.total_urls,
          })
        );
      } catch (error) {
        console.log(error);
      }
    };
    if (selectedConversation?.conversationId && mount) getRoomMediaCount();
    return () => {
      mount = false;
    };
  }, [
    selectedConversation?.conversationId,
    selectedConversation?.type,
    dispatch,
  ]);

  const onSelectFile = (e) => {
    const file = e.target.files[0];
    const isImage = file.type.split("/")[0] === "image";
    if (!isImage) {
      message.error(`${file.name} ${t("import.isNotImage")} !`);
      return;
    }
    if (file.size > 150000) {
      message.error(
        `${file.name} ${t("chat.errSizeImage", { size: "150 KB" })}`
      );
      return;
    }
    const extentionImage = file.type?.split("/").pop().toUpperCase();

    const acceptedType = accepetedExtentionImage.includes(extentionImage);
    if (!acceptedType) {
      message.error(
        t("chat.file.error_extention", {
          extention: accepetedExtentionImage.join(", "),
        })
      );
      return;
    }
    updateRoomFunction(file, "image");
  };

  return (
    <>
      <div className="absolute left-2 top-2">
        <Button
          onClick={closeChatInfoSection}
          type="text"
          shape="circle"
          size="small"
          icon={<CloseOutlined />}
        ></Button>
      </div>
      <div
        onBlur={() =>
          defaultDescription === "" && setDefaultDescription("Description")
        }
        className="mt-6 flex flex-col items-center justify-center space-y-1 px-4"
      >
        <div className="relative mb-2">
          {loading === loadingState.image ? (
            <div className="p-10">
              <Spin size="large" />
            </div>
          ) : (
            <>
              {selectedConversation?.admin_id === currentUser?._id &&
                selectedConversation?.image && (
                  <Button
                    className="absolute -right-2 top-0 z-50 block h-8 w-8 opacity-70"
                    shape="circle"
                    icon={<DeleteOutlined />}
                    onClick={() => updateRoomFunction(null, "image")}
                  />
                )}
              <Tooltip
                title={
                  selectedConversation &&
                  selectedConversation?.type === "room" &&
                  selectedConversation?.admin_id === currentUser?._id
                    ? t("chat.room.chooseImage")
                    : ""
                }
                placement="right"
              >
                <label
                  className={`${
                    selectedConversation &&
                    selectedConversation?.admin_id === currentUser?._id
                      ? "cursor-pointer"
                      : ""
                  }
                    avatar group relative flex  items-center justify-center`}
                >
                  <AvatarChat
                    isPublic={
                      selectedConversation?.type === "room" &&
                      [2, 5, 6].includes(
                        Number(selectedConversation?.predefined)
                      )
                    }
                    roomPro={selectedConversation?.predefined}
                    fromInfo={true}
                    type={selectedConversation?.type}
                    className={
                      selectedConversation &&
                      selectedConversation?.type === "room" &&
                      selectedConversation?.admin_id === currentUser?._id
                        ? "group-hover:opacity-30"
                        : ""
                    }
                    hasImage={selectedConversation?.image}
                    fontSize="3rem"
                    size={160}
                    height={40}
                    width={40}
                    url={
                      (selectedConversation?.type === "room"
                        ? URL_ENV?.REACT_APP_OAUTH_CHAT_API +
                          process.env.REACT_APP_SUFFIX_CHAT_GROUP_STORAGE
                        : URL_ENV?.REACT_APP_BASE_URL +
                          URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL) +
                      selectedConversation?.image
                    }
                    name={getName(selectedConversation?.name, "avatar")}
                  />

                  {selectedConversation &&
                    selectedConversation?.admin_id === currentUser?._id && (
                      <>
                        <input
                          type="file"
                          onChange={onSelectFile}
                          className="hidden"
                        ></input>
                      </>
                    )}
                </label>
              </Tooltip>
            </>
          )}
        </div>
        {loading === loadingState.name ? (
          <Spin />
        ) : (
          <Typography.Title
            editable={
              selectedConversation &&
              selectedConversation?.admin_id === currentUser?._id
                ? {
                    onChange: (key) => (roomNewTitle = key),
                    maxLength: 50,
                    onEnd: () =>
                      roomNewTitle.trim() !== ""
                        ? updateRoomFunction(roomNewTitle, "name")
                        : false,
                  }
                : false
            }
            level={3}
            style={{
              margin: 0,
            }}
            className="text-center"
          >
            {selectedConversation &&
              selectedConversation?.name
                ?.split("_")
                ?.map((e) => e.charAt(0).toUpperCase() + e.slice(1))
                ?.join(" ")}
          </Typography.Title>
        )}
        {loading === loadingState.description ? (
          <Spin />
        ) : (
          selectedConversation?.type === "room" && (
            <Paragraph
              className="text-slate-600"
              editable={
                selectedConversation?.admin_id === currentUser?._id
                  ? {
                      onChange: (key) => (roomNewDescription = key),
                      maxLength: 200,
                      onStart: () => setDefaultDescription(""),
                      onEnd: () =>
                        updateRoomFunction(roomNewDescription, "description"),
                      // roomNewDescription.trim() !== ""
                      //   ?
                      //   : false,
                    }
                  : false
              }
            >
              {typeof selectedConversation?.description === "undefined" ||
              !selectedConversation?.description ? (
                <span className="text-slate-400">{defaultDescription}</span>
              ) : (
                selectedConversation?.description
              )}
            </Paragraph>
          )
        )}
      </div>
      <div className="mt-2  space-x-1 px-2">
        {status === "loading" && fetchStatus !== "idle" ? (
          <div className="flex h-full w-full flex-col items-center">
            <div className="flex w-full items-center justify-between space-x-2 ">
              {Array.from({ length: 4 }, (_, i) => (
                <Skeleton.Input key={"skeleton" + i} size="small" active />
              ))}
            </div>
            <Spin className="mt-20" />
          </div>
        ) : (
          <Tabs
            activeKey={tabIndex}
            centered
            items={items}
            onChange={onChangeTab}
          />
        )}
      </div>
      {selectedConversation &&
        selectedConversation?.type === "room" &&
        selectedConversation?.predefined !== 2 &&
        tabIndex === "1" && (
          <div
            className="absolute bottom-2
           flex w-full items-center justify-center space-x-2"
          >
            <div className="text-center">
              {currentUser?._id !== selectedConversation?.admin_id ? (
                !isGuestConnected(currentUser?.role, sphereUserRole) && (
                  <Popconfirm
                    title={t("chat.room.confirmAbandonment")}
                    style={{ width: "100%", height: "100%" }}
                    description={t("chat.room.quitGroup")}
                    popupVisible={open}
                    onConfirm={handleLeaveParticipant}
                    okButtonProps={{
                      loading: loadingLeaveParticipant,
                      danger: true,
                    }}
                    icon={<QuestionCircleOutlined style={{ color: "red" }} />}
                    onCancel={handleCancelLeaveParticipant}
                  >
                    <Button size="small" danger onClick={() => setOpen(true)}>
                      {t("chat.quitGroup")}
                    </Button>
                  </Popconfirm>
                )
              ) : (
                <Button
                  size="small"
                  type="primary"
                  danger
                  onClick={async () => {
                    if (selectedConversation?.predefined === 2) return;

                    await dispatch(setDataConversation(selectedConversation));

                    dispatch(setOpenQuitGroupModal(true));
                  }}
                  icon={<LogoutOutlined />}
                >
                  {t("chat.quitGroup")}
                </Button>
              )}
            </div>
            {selectedConversation &&
              selectedConversation?.predefined !== 2 &&
              currentUser?._id === selectedConversation?.admin_id && (
                <div className="text-center">
                  <Popconfirm
                    title={t("chat.room.confirmDelete")}
                    style={{ width: "100%", height: "100%" }}
                    description={t("chat.room.deleteGroup")}
                    placement="topLeft"
                    popupVisible={openDelete}
                    onConfirm={async () => {
                      if (selectedConversation?.predefined === 2) return;

                      setLoadingDelete(true);
                      await dispatch(archiveRoom(selectedConversation?.id));

                      setLoadingDelete(false);
                      dispatch(setOpenDrawer({ type: "" }));

                      toastNotification(
                        "success",
                        t("chat.message_system.successDeleteGroup"),
                        "topRight"
                      );
                    }}
                    okButtonProps={{
                      loading: loadingDelete,
                      danger: true,
                    }}
                    icon={<QuestionCircleOutlined style={{ color: "red" }} />}
                    onCancel={handleCancelLeaveParticipant}
                  >
                    <Button
                      size="small"
                      icon={<DeleteOutlined />}
                      danger
                      onClick={() => setOpenDelete(true)}
                    >
                      {t("chat.deleteGroup")}
                    </Button>
                  </Popconfirm>
                </div>
              )}
          </div>
        )}
    </>
  );
}
export default ChatInfo;
