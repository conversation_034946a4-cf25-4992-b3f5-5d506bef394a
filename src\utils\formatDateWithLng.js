import moment from "moment";
import "moment/min/locales";

/**
 *
 * @param {*} rawDate  date returned from the server
 * @param {*} localeLanguage Selected language by the current user
 * @returns date formatted based on the choice of language (french format/english format)
 */

export const dateLocale = (rawDate, localeLanguage) => {
  return moment(rawDate).locale(localeLanguage).format("ll");
};

export const multipleDateLocale = (rawDate, localeLanguage) => {
  return moment(rawDate).locale(localeLanguage).format("lll");
};
