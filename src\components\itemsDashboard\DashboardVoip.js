import React, { useEffect, useState } from "react";
import { Row, Col, Select, Card, Skeleton, Statistic } from "antd";
import HighchartsReact from "highcharts-react-official";
import Highcharts from "highcharts";
import HighchartsMore from "highcharts/highcharts-more";
import Highcharts3D from "highcharts/highcharts-3d";
import ItemSeries from "highcharts/modules/item-series";
import { generateAxios } from "services/axiosInstance";
import { URL_ENV } from "index";
import {
  DonutChart2,
  EvolutionChart,
  OneBarChart,
  PieChartWithPercent,
  StatsChart,
  TrophiesChart,
} from "pages/home/<USER>/ChartsDashboard";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { getKpiCall } from "pages/voip/services/services";
import moment from "moment";
import TableVoipStats from "./TableVoipStats";
import { LucideIcon } from "pages/home/<USER>/ticket.stat/ticket";
import CardStat from "pages/home/<USER>/CardStat";
import { useDispatch } from "react-redux";
import { getNamesQueues } from "new-redux/actions/dashboard.actions";
import {
  HiPhone,
  HiPhoneIncoming,
  HiPhoneOutgoing,
  HiPhoneMissedCall,
} from "react-icons/hi";
import { MdPhoneMissed } from "react-icons/md";

// Initialisation de modules Highcharts
HighchartsMore(Highcharts);
Highcharts3D(Highcharts);
ItemSeries(Highcharts);

// Fonction utilitaire pour capitaliser le dernier mot d'une clé
const getLastWordCapitalized = (key) => {
  const words = key.split("_");
  const lastWord = words[words.length - 1];
  return lastWord.charAt(0).toUpperCase() + lastWord.slice(1);
};

// Composant du graphique Bundestag
const PhoneIcon = ({ iconName, size = 24, color = "currentColor" }) => {
  console.log(iconName);

  switch (iconName) {
    case "HiPhone":
      return <HiPhone size={size} color={color} />;
    case "HiPhoneIncoming":
      return <HiPhoneIncoming size={size} color={color} />;
    case "MdPhoneMissed":
      return <MdPhoneMissed size={size} color={color} />;
    case "HiPhoneOutgoing":
      return <HiPhoneOutgoing size={size} color={color} />;
    case "HiPhoneMissedCall":
      return <HiPhoneMissedCall size={size} color={color} />;
    default:
      return <HiPhone size={size} color={color} />;
  }
};

// Composant principal Dashboard
const DashboardVoip = ({ start, end, from = "" }) => {
  const [totalCalls, setTotalCalls] = useState({
    data: [],
    name: "",
    total: null,
  });

  const [inbounds, setInbounds] = useState({
    data: [],
    name: "",
    total: null,
  });
  const [outbounds, setOutbounds] = useState({
    data: [],
    name: "",
    total: null,
  });
  const [statsSda, setstatsSda] = useState({});
  const { i18n } = useTranslation("common");
  const [t] = useTranslation("common");

  const { totalQueues, allQueues } = useSelector(
    (state) => state.dashboardRealTime
  );
  const [queueEvolution, setQueueEvolution] = useState({});
  const [callsTags, setCallsTags] = useState({});
  const [callsModule, setCallsModule] = useState({});
  const [queueStat, setQueueStat] = useState({});
  const [numbersSda, setNumbersSda] = useState([]);

  const [selectedQueue, setSelectedQueue] = useState(
    Array.isArray(totalQueues) && totalQueues.length > 0
      ? totalQueues[0].queue_num
      : ""
  );
  const [selectedSda, setSelectedSda] = useState("");
  const [callKpi, setCallKpi] = useState([]);
  const [loadCallKpi, setLoadCallKpi] = useState(false);
  const [evolutionSda, setEvolutionSda] = useState({});

  const { user } = useSelector((state) => state.user);
  const dispatch = useDispatch();
  useEffect(() => {
    if (allQueues.length === 0) {
      dispatch(getNamesQueues(start, end));
    }
  }, [dispatch, allQueues, start, end]);
  useEffect(() => {
    if (
      !selectedQueue &&
      Array.isArray(totalQueues) &&
      totalQueues.length > 0
    ) {
      setSelectedQueue(totalQueues[0].queue_num);
    } else if (
      !selectedQueue &&
      Array.isArray(allQueues) &&
      allQueues.length > 0
    ) {
      setSelectedQueue(allQueues[0].value);
    }
  }, [totalQueues, selectedQueue, allQueues]);
  useEffect(() => {
    const fetchKpi = async () => {
      setLoadCallKpi(true);
      try {
        const formatWithTime = "YYYY-MM-DD HH:mm"; // Backend format with time
        const formatWithoutTime = "YYYY-MM-DD"; // Backend format without time

        const userDateFormat = user?.location?.date_format || formatWithoutTime;
        const userTimeFormat = user?.location?.time_format || "HH:mm";
        const combinedFormat = `${userDateFormat} ${userTimeFormat}`;
        const { data } = await getKpiCall(
          "call",
          moment(start, combinedFormat).format("YYYY-MM-DD HH:mm"),
          moment(end, combinedFormat).format("YYYY-MM-DD HH:mm")
        );
        setLoadCallKpi(false);
        setCallKpi(data.data);
      } catch (err) {
        setLoadCallKpi(false);
      }
    };

    fetchKpi();
  }, [start, end]);

  // Fetch des données pour les appels totaux
  useEffect(() => {
    const fetchTotalCalls = async () => {
      try {
        const response = await generateAxios(
          `${URL_ENV.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
        ).get(
          `/users-voip-kpi?start_date=${start}&end_date=${end}&total_calls=1&language=${i18n.language}`
        );
        const data = response.data;
        setTotalCalls(data);
      } catch (error) {
        console.error(
          "Erreur lors de la récupération des appels totaux :",
          error
        );
      }
    };

    fetchTotalCalls();
  }, [start, end, i18n.language]);

  // Fetch des données pour les appels entrants
  useEffect(() => {
    const fetchInbounds = async () => {
      try {
        const response = await generateAxios(
          `${URL_ENV.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
        ).get(
          `/users-voip-kpi?start_date=${start}&end_date=${end}&inbound=1&language=${i18n.language}`
        );

        setInbounds(response.data || {});
      } catch (error) {
        console.error(
          "Erreur lors de la récupération des appels entrants :",
          error
        );
      }
    };

    fetchInbounds();
  }, [start, end, i18n.language]);

  // Fetch des données pour les appels sortants
  useEffect(() => {
    const fetchOutbounds = async () => {
      try {
        const response = await generateAxios(
          `${URL_ENV.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
        ).get(
          `/users-voip-kpi?start_date=${start}&end_date=${end}&outbound=1&language=${i18n.language}`
        );

        setOutbounds(response.data || {});
      } catch (error) {
        console.error(
          "Erreur lors de la récupération des appels sortants :",
          error
        );
      }
    };

    fetchOutbounds();
  }, [start, end, i18n.language]);
  useEffect(() => {
    const fetchQueueEvolution = async () => {
      try {
        const response = await generateAxios(
          `${URL_ENV.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
        ).get(
          `/queue-evolution?start_date=${start}&end_date=${end}&outbound=1&language=${i18n.language}`
        );
        setQueueEvolution({
          name: response?.data?.name,
          categories: response?.data?.data?.categories,
          series: response?.data?.data?.series,
        });
      } catch (error) {
        console.error(
          "Erreur lors de la récupération des appels sortants :",
          error
        );
      }
    };

    fetchQueueEvolution();
  }, [start, end, i18n.language]);
  useEffect(() => {
    const fetchCallTags = async () => {
      try {
        const response = await generateAxios(
          `${URL_ENV.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
        ).get(
          `/call-tags?start_date=${start}&end_date=${end}&language=${i18n.language}`
        );
        setCallsTags(response?.data);
      } catch (error) {
        console.error(
          "Erreur lors de la récupération des appels sortants :",
          error
        );
      }
    };

    fetchCallTags();
  }, [start, end, i18n.language]);
  useEffect(() => {
    const fetchCallModule = async () => {
      try {
        const response = await generateAxios(
          `${URL_ENV.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
        ).get(
          `/calls-module?start_date=${start}&end_date=${end}&language=${i18n.language}`
        );

        setCallsModule({
          data: response?.data?.data,
          name: response?.data?.name,
          total: "",
        });
      } catch (error) {
        console.error(
          "Erreur lors de la récupération des appels sortants :",
          error
        );
      }
    };

    fetchCallModule();
  }, [start, end, i18n.language]);
  useEffect(() => {
    const fetchQueueStat = async () => {
      try {
        const response = await generateAxios(
          `${URL_ENV.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
        ).get(
          `/queue-stat?start_date=${start}&end_date=${end}&language=${i18n.language}&queue_num=${selectedQueue}`
        );
        setQueueStat(response.data);
      } catch (error) {
        console.error(
          "Erreur lors de la récupération des appels sortants :",
          error
        );
      }
    };

    fetchQueueStat();
  }, [start, end, i18n.language, selectedQueue]);
  useEffect(() => {
    const fetchSdaStat = async () => {
      let res;
      try {
        if (!selectedSda) {
          res = await generateAxios(
            `${URL_ENV.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
          ).get(`/list-sda`);
          setNumbersSda(
            Object.values(res.data).map((el) => ({ value: el, label: el }))
          );
          setSelectedSda(Object.values(res.data)[0]);
        }
        if (selectedSda) {
          const response = await generateAxios(
            `${URL_ENV.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
          ).get(
            `/sda-stat?start_date=${start}&end_date=${end}&language=${i18n.language}&category=${selectedSda}`
          );
          setstatsSda(response.data);
        }
      } catch (error) {
        console.error(
          "Erreur lors de la récupération des appels sortants :",
          error
        );
      }
    };

    fetchSdaStat();
  }, [start, end, i18n.language, selectedSda]);
  useEffect(() => {
    const fetchSdaEvolution = async () => {
      try {
        const response = await generateAxios(
          `${URL_ENV.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
        ).get(
          `/sda-evoltion?start_date=${start}&end_date=${end}&language=${i18n.language}`
        );
        setEvolutionSda({ name: response.data?.name, ...response.data?.data });
      } catch (error) {
        console.error(
          "Erreur lors de la récupération des appels sortants :",
          error
        );
      }
    };

    fetchSdaEvolution();
  }, [start, end, i18n.language]);

  // useEffect(() => {
  //   const getNumbersSda = async () => {
  //     const res = await generateAxios(
  //       `${URL_ENV.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
  //     ).get(`/queue-group-connected-user`);
  //   };
  //   getNumbersSda();
  // }, []);
  return (
    <>
      <Row gutter={(from = "drawer" ? [4, 4] : [16, 16])}>
        {!loadCallKpi
          ? callKpi.map((el, i) => (
              <Col style={{ flex: "0 0 20%" }} key={`cardsTickets_Col_${i}`}>
                <Card
                  bordered={true}
                  // style={{ background: el.color }}
                >
                  <Statistic
                    title={
                      <div className="flex items-center justify-between">
                        <span className="font-semibold">
                          {t(`voip.${el.name}`)}
                        </span>
                        <PhoneIcon iconName={el?.icon} color={el?.color} />
                      </div>
                    }
                    value={el?.value}
                    precision={0}
                    valueStyle={{
                      color: el?.color,
                    }}
                    // prefix={<ArrowUpOutlined />}
                    // suffix="%"
                  />
                </Card>
              </Col>
            ))
          : Array.from({ length: 5 }, () => null).map((_, index) => (
              <Col
                style={{ flex: "0 0 20%" }}
                key={`load_cardsTickets_Col_${index}`}
              >
                <div className="skeletonSelectDepartments grow">
                  <Skeleton.Input
                    key={index}
                    active
                    style={{ width: "100%", height: "84px" }}
                  />
                </div>
              </Col>
            ))}
      </Row>
      <br />
      <Row gutter={[16, 16]}>
        <Col span={from === "drawer" ? 12 : 10}>
          <CardStat title={totalCalls?.name}>
            <DonutChart2
              data={totalCalls?.data}
              total={totalCalls?.total}
              name=""
            />
          </CardStat>
        </Col>
        <Col span={from === "drawer" ? 12 : 7}>
          <CardStat title={inbounds?.name}>
            <DonutChart2
              data={inbounds?.data}
              total={inbounds?.total}
              name=""
            />
          </CardStat>
        </Col>
        <Col span={from === "drawer" ? 12 : 7}>
          <CardStat title={outbounds?.name}>
            <DonutChart2
              data={outbounds?.data}
              total={outbounds?.total}
              name=""
            />
          </CardStat>
        </Col>
        {allQueues && Array.isArray(allQueues) && allQueues.length > 0 ? (
          <Col className="gutter-row" span={from === "drawer" ? 12 : 12}>
            <CardStat title={queueEvolution?.name}>
              <EvolutionChart data={{ ...queueEvolution, name: "" }} />
            </CardStat>
          </Col>
        ) : null}

        <Col className="gutter-row" span={from === "drawer" ? 12 : 12}>
          <CardStat title={callsModule?.name}>
            <OneBarChart data={{ ...callsModule, name: "" }} />
          </CardStat>
        </Col>
        {allQueues.length > 0 && (
          <Col className="gutter-row" span={12}>
            <CardStat
              title={queueStat?.name}
              extra={
                <Select
                  popupMatchSelectWidth={false}
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? "")
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  onChange={(value) => setSelectedQueue(value)}
                  value={selectedQueue}
                  options={allQueues}
                />
              }
            >
              <StatsChart data={{ ...queueStat, name: "" }} />
            </CardStat>
          </Col>
        )}
        {numbersSda?.length > 0 && selectedSda && (
          <Col className="gutter-row" span={12}>
            <CardStat
              style={{ padding: 0, borderRadius: 0 }}
              title={statsSda?.name}
              extra={
                <Select
                  popupMatchSelectWidth={false}
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? "")
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  onChange={(value) => setSelectedSda(value)}
                  value={selectedSda}
                  options={numbersSda}
                />
              }
            >
              <TrophiesChart data={{ ...statsSda, name: "" }} />
            </CardStat>
          </Col>
        )}

        {/* <Col span={24}>
          <TableSdaStats start={start} end={end} />
        </Col> */}
        {numbersSda?.length > 0 && (
          <Col className="gutter-row" span={12}>
            <CardStat title={evolutionSda?.name}>
              <EvolutionChart data={{ ...evolutionSda, name: "" }} />
            </CardStat>
          </Col>
        )}
        <Col className="gutter-row" span={from === "drawer" ? 12 : 12}>
          <CardStat title={callsTags?.name}>
            <PieChartWithPercent
              isExistDate={false}
              hasSelected={true}
              data={{
                ...callsTags,

                data: callsTags?.data?.map((item) => {
                  return {
                    name: item[0],
                    y: item[1],
                  };
                }),
                name: "",
              }}
              // total={statsEmailProcRate?.data
              //   ?.map((el) => Number(el.y))
              //   .reduce((x, y) => x + y, 0)}
              // name={statsEmailProcRate?.name}
            />
          </CardStat>
        </Col>
        <Col span={24}>
          <TableVoipStats start={start} end={end} />
        </Col>
        {/* <Col span={8}>
        <PieChart inbounds={inbounds} />
      </Col>
      <Col span={8}>
        <BundestagChart outbounds={outbounds} />
      </Col> */}
      </Row>
    </>
  );
};

export default DashboardVoip;
