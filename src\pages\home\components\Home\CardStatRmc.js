import { Col, Row, Select, Tooltip } from "antd";
import React, { useMemo } from "react";
import CardWithGraph from "../CardWithGraph";
import { GoTo } from "pages/home/<USER>";
import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { Refs_IDs } from "components/tour/tourConfig";
import { InfoCircleOutlined } from "@ant-design/icons";
import { toastNotification } from "components/ToastNotification";
import { useDispatch } from "react-redux";
import { SET_RMC } from "new-redux/constants";
import MainService from "services/main.service";
import dayjs from "dayjs";
import { setSelectedDepRmc } from "new-redux/actions/dashboard.actions";

const CardStatRmc = ({ start, end }) => {
  const [t] = useTranslation("common");
  const navigate = useNavigate();
  const { channelsRmc, depsRmc, selectedDepRmc, loading } = useSelector(
    (state) => state.dashboardRealTime
  );
  const { user } = useSelector((state) => state.user);
  const dispatch = useDispatch();
  const getStatsRmc = async (value, start, end) => {
    dispatch({
      type: SET_RMC,
      payload: channelsRmc?.map((el) => ({ ...el, count: "-" })),
    });
    try {
      const { data } = await MainService.getStatsRmc({
        start: dayjs(start, user?.location?.date_format).format("DD.MM.YYYY"),
        end: dayjs(end, user?.location?.date_format).format("DD.MM.YYYY"),
        department_id: value,
      });
      const updatedChannels = channelsRmc?.map((el) => {
        const apiItem = data?.find((item) => item?.channel === el?.channel);
        if (apiItem) {
          return { ...el, count: apiItem.count };
        }
        if (!apiItem) {
          return { ...el, count: 0 };
        }
        return el;
      });
      dispatch({
        type: SET_RMC,
        payload: updatedChannels,
      });
    } catch (err) {
      toastNotification("error", t("toasts.somethingWrong"));
    }
  };
  const dataRmc = useMemo(
    () => ({
      data: channelsRmc.map((item) => ({
        name: t(`dashboard.${item?.channel}`),
        y: Number(item.count),
        color: item.color,
      })),
      name: "",
    }),
    [channelsRmc, t]
  );
  return (
    <Row gutter={[8, 8]}>
      <Col span={24}>
        <CardWithGraph
          title={
            <div className="flex items-center justify-between ">
              <span>
                {t("dashboard.socialMedia")} &nbsp;
                <Select
                  ref={Refs_IDs.select_social_media_dashboard}
                  showSearch
                  optionFilterProp="children"
                  popupMatchSelectWidth={false}
                  filterOption={(input, option) =>
                    (option?.label2 ?? "")
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  options={[
                    {
                      value: "all",
                      label2: t("helpDesk.all"),
                      label: (
                        <div className="flex items-center justify-between">
                          <span>{t("helpDesk.all")}</span>
                          <Tooltip title={t("dashboard.relativeFigures")}>
                            <InfoCircleOutlined />
                          </Tooltip>
                        </div>
                      ),
                    },
                    ...depsRmc?.map((dep) => ({
                      value: Number(dep.department_id),
                      label: dep.name,
                      label2: dep.name,
                      color: dep.color,
                    })),
                  ]}
                  style={{ minWidth: 100, width: "min-content" }}
                  value={selectedDepRmc}
                  onChange={(value, values) => {
                    // setSelecteDep(value);
                    dispatch(setSelectedDepRmc(value));
                    getStatsRmc(value, start, end);
                  }}
                />{" "}
              </span>
              <GoTo
                to={"6"}
                title={t("dashboard.socialMedia")}
                navigate={navigate}
                t={t}
                user={user}
              />
            </div>
          }
          stats={channelsRmc
            .slice()
            .sort((a, b) => b.count - a.count)
            .map((el) => ({
              icon: el.icon,
              name: t(`dashboard.${el?.channel}`),
              number: el.count,
            }))}
          chartData={!loading ? dataRmc : { data: [], name: "" }}
        />
      </Col>
    </Row>
  );
};

export default CardStatRmc;
