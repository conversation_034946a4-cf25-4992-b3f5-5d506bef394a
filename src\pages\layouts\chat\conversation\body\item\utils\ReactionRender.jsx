import { Badge, Space, Tooltip } from "antd";
import {
  react_emojis,
  getName,
  regroupUsersByReactionId,
  getUserFromMsg,
} from "pages/layouts/chat/utils/ConversationUtils";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";

const ReactionRender = ({
  item,
  handleActionMessage,
  setActionType,
  source,
}) => {
  const { t } = useTranslation("common");
  const { currentUser } = useSelector((state) => state.chat);
  const selectedConversation = useSelector(
    (state) => state.ChatRealTime.selectedConversation
  );

  return (
    !item.deleted_at &&
    Array.isArray(item.reactions) &&
    item.reactions &&
    item.reactions.length > 0 && (
      <Space size={12} className="flex  pt-1 ">
        {regroupUsersByReactionId(item.reactions)
          .sort((a, b) => a.reaction - b.reaction)
          .map((reaction, i1) => {
            let reactionNameSet = new Set();
            if (reaction.user_id?.length > 0 && reaction.user_id) {
              reaction.user_id.forEach((rec) => {
                if (typeof rec !== "object")
                  reactionNameSet.add(
                    rec === currentUser?._id
                      ? getName(currentUser?.name, "name")
                      : selectedConversation?.type === "user"
                      ? getName(selectedConversation?.name, "name")
                      : getName(getUserFromMsg(rec)?.name, "name")
                  );
              });
            } else {
              reactionNameSet.add(
                reaction.user_id === currentUser?._id
                  ? getName(currentUser?.name, "name")
                  : selectedConversation?.type === "user"
                  ? getName(selectedConversation?.name, "name")
                  : getName(getUserFromMsg(reaction.user_id)?.name, "name")
              );
            }
            return (
              <Tooltip
                key={"reactions_key" + i1}
                title={
                  <div className="flex flex-col items-center">
                    <div className="capitalize" key={i1}>
                      {[...reactionNameSet]?.slice(0, 10)?.join(", ")}
                    </div>
                    {[...reactionNameSet]?.length - 10 > 0 && (
                      <div key={i1}>
                        {t("chat.react.other_user", {
                          number: reactionNameSet?.size - 10,
                        })}{" "}
                      </div>
                    )}
                    {reactionNameSet?.has(
                      getName(currentUser?.name, "name")
                    ) && <span>({t("chat.react.message_to_delete")})</span>}
                  </div>
                }
                className="space-x-0.5"
              >
                <span
                  onClick={() => {
                    if (
                      (reaction?.user_id?.length &&
                        reaction?.user_id?.includes(currentUser?._id)) ||
                      reaction?.user_id === currentUser?._id
                    ) {
                      handleActionMessage({
                        message_id: item?._id,
                        params: {
                          source,
                          main_message_id: item.main_message?._id,
                          params: reaction.reaction,
                        },
                        type_conversation: selectedConversation?.type,
                        type_action: "remove_react",
                      });
                      setActionType("remove_react");
                    } else {
                      handleActionMessage({
                        message_id: item._id,
                        params: {
                          main_message_id: item.main_message?._id,
                          params: reaction.reaction,
                        },
                        type_conversation: selectedConversation?.type,
                        type_action: "add_react",
                      });
                      setActionType("add_react");
                    }
                  }}
                  className="my-1 flex cursor-pointer items-center justify-center rounded-full bg-[#e6f1fe] px-1 py-1   ring-1"
                >
                  <img
                    src={
                      process.env.PUBLIC_URL +
                      react_emojis[reaction.reaction]?.image
                    }
                    loading="lazy"
                    className="h-4 w-4 cursor-pointer object-cover "
                    alt={react_emojis[reaction.reaction]?.name}
                  />
                  <Badge
                    count={reaction.count}
                    size="small"
                    style={{
                      fontSize: "12px",
                      color: "#108ee9",
                      boxShadow: "none",
                      backgroundColor: "#e6f1fe",
                    }}
                  />
                </span>
              </Tooltip>
            );
          })}
      </Space>
    )
  );
};

export default ReactionRender;
