import React, { useEffect, useRef } from "react";
import { Form, Input, Button, Select, Space } from "antd";
import { useState } from "react";
import { generateAxios } from "../services/axiosInstance";
import { useTranslation } from "react-i18next";
import { PlusCircleOutlined } from "@ant-design/icons";
import { toastNotification } from "./ToastNotification";
import Header from "./configurationHelpDesk/Header";
import NewTableDraggable from "./NewTableDraggable";
import { useSelector, useDispatch } from "react-redux";
import { setSearch } from "../new-redux/actions/menu.actions/menu";
import LabelTable from "./LabelTable";
import BottomButtonAddRow from "./BottomButtonAddRow";
import { SubmitKeyPress } from "../utils/SubmitKeyPress";
import { URL_ENV } from "index";

const Services = () => {
  const [form] = Form.useForm();
  const [count, setCount] = useState(0);
  const [departments, setDepartments] = useState([]);
  const [data, setData] = useState([]);
  const [allData, setAllData] = useState([]);

  const [editingKey, setEditingKey] = useState("");
  const [id, setId] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [oldPage, setOldPage] = useState(1);
  const [loading, setLoading] = useState(true);
  const [pageSize, setPageSize] = useState(20);
  const [sorter, setSorter] = useState({
    field: null,
    order: null,
  });
  const [filter, setFilter] = useState({});
  const isEditing = (record) => record.key === editingKey;
  const [t] = useTranslation("common");
  const inputRefs = useRef([]);
  const dispatch = useDispatch();
  const { search } = useSelector((state) => state.form);

  useEffect(() => {
    inputRefs.current.forEach((input) => {
      input?.focus();
    });
  }, [data.length, id]);

  useEffect(() => {
    setData((prev) =>
      allData.filter((object) => {
        if (filter.departement_id && filter.departement_id.length > 0) {
          return filter.departement_id.includes(
            object.departement_id.toString()
          );
        } else {
          return object;
        }
      })
    );
  }, [sorter, filter, allData]);

  const onFinishFailed = (values) => {
    console.log(values);
  };
  const handleKeyPress = (event) => {
    SubmitKeyPress(event, form);
  };
  const handleTableChange = (pagination, filters, sorter) => {
    setSorter({
      field: sorter.field,
      order: sorter.order,
    });
    setFilter(filters);
  };

  // useEffect(() => {
  //   if (editingKey && oldPage !== currentPage) {
  //     setData((prev) => prev.filter((el) => el.id));
  //     setEditingKey("");'
  //   }
  // }, [currentPage, editingKey, oldPage]);
  const EditableCell = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    const inputNode =
      inputType === "select" ? (
        <Select
          placeholder={t("services.selectdepartment")}
          options={departments}
          showSearch
          filterOption={(input, option) =>
            (option?.label?.toLowerCase() ?? "").includes(input.toLowerCase())
          }
        />
      ) : (
        <Input
          ref={(el) => (inputRefs.current[index] = el)}
          onKeyPress={handleKeyPress}
          placeholder={t("activities.name")}
        />
      );
    return (
      <td {...restProps}>
        {editing ? (
          <Form.Item
            name={dataIndex.toLowerCase()}
            style={{
              margin: 0,
            }}
            rules={[
              {
                required: true,
                message: `${t(`services.${dataIndex}`)} ${t(
                  "table.header.isrequired"
                )}`,
              },
            ]}>
            {inputNode}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };
  const edit = (record) => {
    if (record) {
      form.setFieldsValue({
        label: record.label,
        departement_id: record.departement_id,
      });
      setId(record.id);
    } else {
      form.setFieldsValue({
        label: "",
        departement_id: null,
      });
    }
    setEditingKey(record.key);
  };
  const cancel = (record) => {
    setEditingKey("");
    setId(null);
    if (!record.id) {
      setData(data.filter((item) => item.key !== record.key));
    }
  };
  const save = async (key) => {
    setLoading(true);
    if (id) {
      try {
        const row = await form.validateFields();
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post(`/services/update/${id}`, row);
        setEditingKey("");
        setData(
          data.map((el) =>
            el.id === res.data.data.id
              ? {
                  ...res.data.data,
                  key: res.data.data.id,
                  departement_id: Number(res.data.data.departement_id),
                }
              : el
          )
        );
        setAllData(
          data.map((el) =>
            el.id === res.data.data.id
              ? {
                  ...res.data.data,
                  key: res.data.data.id,
                  departement_id: Number(res.data.data.departement_id),
                }
              : el
          )
        );
        form.setFieldsValue({
          label: "",
          departement_id: null,
        });
        setLoading(false);
        toastNotification("success", row.label + t("toasts.edit"), "topRight");
      } catch (errInfo) {
        setLoading(false);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    } else {
      try {
        const row = await form.validateFields();
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post("/services", row);
        setEditingKey("");
        setData([
          ...data.filter((el) => el.id),
          { ...res.data.data, key: res.data.data.id },
        ]);
        setAllData([
          ...data.filter((el) => el.id),
          { ...res.data.data, key: res.data.data.id },
        ]);
        form.setFieldsValue({
          label: "",
          departement_id: null,
        });
        setLoading(false);
        toastNotification("success", row.label + t("toasts.edit"), "topRight");
      } catch (errInfo) {
        setLoading(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    }
  };
  useEffect(() => {
    const getServices = async () => {
      setLoading(true);
      try {
        const {
          data: { data },
        } = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get("/services");
        if (data) {
          const res = await generateAxios(
            URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
          ).get("/departments");
          setDepartments(
            res.data.data.map((el) => ({
              label: el.label,
              value: el.id,
              color: el.color,
            }))
          );
          setData(data.map((el, i) => ({ ...el, key: el.id, rank: i + 1 })));
          setAllData(data.map((el, i) => ({ ...el, key: el.id, rank: i + 1 })));

          if (data.length > 0) {
            setCount(Math.max(...data.map((el) => el.id)));
          }
          setLoading(false);
        }
      } catch (err) {
        setLoading(false);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    };
    getServices();
    return () => dispatch(setSearch(""));
  }, []);

  const handleClick = (event) => {
    event.stopPropagation();
  };

  const columns = [
    {
      title: t("activities.name"),
      dataIndex: "label",
      key: "label",
      editable: true,
      sorter: (a, b) => a.label.localeCompare(b.label),
      render: (_, record) => {
        return (
          <LabelTable record={record} editingKey={editingKey} edit={edit} />
        );
      },
    },
    {
      title: t("table.header.department"),
      dataIndex: "departement_id",
      key: "departement_id",
      editable: true,
      filters: departments.map((el) => ({
        text: el.label,
        value: el.value.toString(),
      })),

      onFilter: (value, record) => record.departement_id == value,

      sorter: (a, b) =>
        departments
          .find((el) => el.value === a.departement_id)
          ?.label.localeCompare(
            departments.find((el) => el.value === b.departement_id)?.label
          ),

      render: (_, record) => (
        <>
          {record.departement_id ? (
            <span
              style={{
                color: departments.find(
                  (el) => el.value === record.departement_id
                )?.color,
              }}>
              {
                departments.find((el) => el.value === record.departement_id)
                  ?.label
              }{" "}
            </span>
          ) : (
            ""
          )}
        </>
      ),
    },
  ];
  // const mergedColumns = columns.map((col) => {
  //   if (!col.editable) {
  //     return col;
  //   }
  //   return {
  //     ...col,
  //     onCell: (record, index) => ({
  //       record,
  //       inputType: col.dataIndex === "departement_id" ? "select" : "input",
  //       dataIndex: col.dataIndex,
  //       title: col.title,
  //       editing: isEditing(record),
  //       index,
  //     }),
  //   };
  // });
  const handleAdd = () => {
    const ids = data.map((object) => {
      return object.id;
    });
    setId(null);
    const newData = {
      key: Math.max(...ids) + 1,
      label: `  `,
      departement_id: null,
      disabled: true,
    };
    setCurrentPage(Math.ceil([...data, newData].length / pageSize));
    setOldPage(Math.ceil([...data, newData].length / pageSize));
    setData([...data, newData]);
    form.setFieldsValue({
      label: "",
      departement_id: null,
    });
    setEditingKey(Math.max(...ids) + 1);
    setCount(Math.max(...ids) + 1);
  };
  const onRow = () => {};
  const filteredData = data.filter((item) => {
    return (
      item.label.toLowerCase().includes(search.toLowerCase()) ||
      departments
        .find((el) => el.value === item.departement_id)
        ?.label.toLowerCase()
        .includes(search.toLowerCase())
    );
  });
  return (
    <Space direction="vertical" style={{ width: "100%" }}>
      <Header
        active={"3"}
        editingKey={editingKey}
        handleAdd={handleAdd}
        btnText={t("helpDesk.addService")}
        disabled={loading ? true : editingKey ? true : search ? true : false}
        api={"services"}
      />

      <NewTableDraggable
        columns={columns}
        setLoading={setLoading}
        isEditing={isEditing}
        data={filteredData}
        setData={setData}
        loading={loading}
        save={save}
        edit={edit}
        EditableCell={EditableCell}
        onFinishFailed={onFinishFailed}
        cancel={cancel}
        form={form}
        apiRank="change-service-rank"
        editingKey={editingKey}
        api="services"
        onRow={onRow}
        setAllData={setAllData}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        pageSize={pageSize}
        setPageSize={setPageSize}
        handleTableChange={handleTableChange}
      />

      <BottomButtonAddRow
        editingKey={editingKey}
        data={filteredData}
        text={t("helpDesk.addService")}
        handleAdd={handleAdd}
        loading={loading}
        search={
          search ||
          (filter.departement_id && filter.departement_id.length > 0) ||
          ""
        }
      />
    </Space>
  );
};
export default Services;
