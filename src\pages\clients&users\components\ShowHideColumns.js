import { memo, useCallback, useEffect, useMemo, useState } from "react";
import {
  Popover,
  Checkbox,
  Button,
  Card,
  Badge,
  Collapse,
  Space,
  Divider,
} from "antd";
import {
  CaretRightOutlined,
  DownOutlined,
  MenuOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { storeColumnsOrder, storeConfigColumns } from "../services/services";
import { toastNotification } from "../../../components/ToastNotification";
import { Refs_IDs } from "components/tour/tourConfig";
import {
  DndContext,
  closestCenter,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  SortableContext,
  useSortable,
  arrayMove,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { useWindowSize } from "./WindowSize";
//
//
const ShowHideColumns = ({ showColumns, setShowColumns, familyId }) => {
  //
  const [t] = useTranslation("common");
  const windowSize = useWindowSize();

  const [openPopover, setOpenPopover] = useState(null);

  const [updateShowColumn, setUpdateShowColumn] = useState(false);
  const [updateOrderColumn, setUpdateOrderColumn] = useState(false);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5, // Require 5px movement before dragging starts
      },
    })
  );

  // Handle drag end for column reordering
  const handleDragEnd = useCallback(
    (event) => {
      const { active, over } = event;

      if (!over || active.id === over.id) return;

      // Extract group and column names from the IDs
      const [activeGroup] = active.id.split("-");
      const [overGroup] = over.id.split("-");

      // Only allow reordering within the same group
      if (activeGroup !== overGroup) return;

      setUpdateOrderColumn(true);

      setShowColumns((prev) => {
        const newShowColumns = { ...prev };
        const group = activeGroup;
        const oldIndex = newShowColumns.groups[group].allColumns.indexOf(
          active.id.split("-")[1]
        );
        const newIndex = newShowColumns.groups[group].allColumns.indexOf(
          over.id.split("-")[1]
        );

        // Reorder the columns in the group
        newShowColumns.groups[group].allColumns = arrayMove(
          newShowColumns.groups[group].allColumns,
          oldIndex,
          newIndex
        );

        // Also reorder showColumns if needed to maintain consistency
        // if (
        //   newShowColumns.groups[group].showColumns.includes(
        //     active.id.split("-")[1]
        //   )
        // ) {
        //   newShowColumns.groups[group].showColumns = arrayMove(
        //     newShowColumns.groups[group].showColumns,
        //     oldIndex,
        //     newIndex
        //   );
        // }

        return newShowColumns;
      });
    },
    [setShowColumns]
  );

  const saveConfigCol = useCallback(() => {
    try {
      const formData = new FormData();
      formData.append("family_id", familyId);
      for (const col in showColumns.colConfig) {
        const value = showColumns.colConfig[col];
        // do not append Timestamps fields
        if (typeof value.id === "number")
          formData.append(`field[${value.id}]`, value.show);
      }
      storeConfigColumns(formData);
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? `error: ${err.message}` : { err });
    } finally {
      setUpdateShowColumn(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [openPopover]);

  const saveColOrder = useCallback(() => {
    try {
      const formData = new FormData();
      const { colConfig, groups } = showColumns;
      for (const group in groups) {
        if (group !== "Timestamps") {
          const { rank, allColumns } = groups[group];
          allColumns.forEach((col, i) => {
            const colId = colConfig[col]?.id;
            const newRank = i + (rank === 1 ? 2 : 1);
            formData.append(`field[${colId}]`, newRank);
          });
        }
      }
      storeColumnsOrder(formData, familyId);
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? `error: ${err.message}` : { err });
    } finally {
      setUpdateOrderColumn(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [openPopover]);

  useEffect(() => {
    if (!openPopover) {
      if (updateShowColumn) saveConfigCol();
      else if (updateOrderColumn) saveColOrder();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [saveConfigCol, saveColOrder]);

  const { groups, mainColumn, totalColumns, totalShowColumns } = showColumns;
  //
  // Handle the "Show All" checkbox
  const toggleAllColumnsVisibility = useCallback(
    (event) => {
      setUpdateShowColumn(true);
      const newShowColumns = { ...showColumns };
      const newColConfig = {};
      if (event.target.checked) {
        newShowColumns.totalShowColumns = newShowColumns.totalColumns;
        Object.values(newShowColumns.groups).forEach((group) => {
          group.showColumnsNumber = group.columnsNumber;
          group.showColumns = group.allColumns;
        });
        for (const col in newShowColumns.colConfig) {
          const value = newShowColumns.colConfig[col];
          newColConfig[col] = { ...value, show: 1 };
        }
        newShowColumns.colConfig = newColConfig;
      } else {
        newShowColumns.totalShowColumns = 1;
        Object.values(newShowColumns.groups).forEach((group) => {
          group.showColumnsNumber = 0;
          group.showColumns = [];
        });
        for (const col in newShowColumns.colConfig) {
          const value = newShowColumns.colConfig[col];
          newColConfig[col] = { ...value, show: 0 };
        }
        newShowColumns.colConfig = newColConfig;
      }
      newShowColumns.colConfig[newShowColumns.mainColumn] = {
        ...newShowColumns.colConfig[newShowColumns.mainColumn],
        show: 1,
      };
      setShowColumns(newShowColumns);
    },
    [setShowColumns, showColumns]
  );
  //
  // Handle the checkboxes for each group
  const handleShowGroup = useCallback(
    (event, group) => {
      setUpdateShowColumn(true);
      const newShowColumns = { ...showColumns };
      const newColConfig = { ...showColumns.colConfig };
      if (event.target.checked) {
        newShowColumns.totalShowColumns +=
          newShowColumns.groups[group].columnsNumber -
          newShowColumns.groups[group].showColumnsNumber;
        newShowColumns.groups[group].showColumnsNumber =
          newShowColumns.groups[group].columnsNumber;
        newShowColumns.groups[group].showColumns =
          newShowColumns.groups[group].allColumns;
        newShowColumns.groups[group].allColumns.forEach((col) => {
          newColConfig[col] = { ...newColConfig[col], show: 1 };
        });
        newShowColumns.colConfig = newColConfig;
      } else {
        newShowColumns.totalShowColumns -=
          newShowColumns.groups[group].columnsNumber;
        newShowColumns.groups[group].showColumnsNumber = 0;
        newShowColumns.groups[group].showColumns = [];
        newShowColumns.groups[group].allColumns.forEach((col) => {
          newColConfig[col] = { ...newColConfig[col], show: 0 };
        });
        newShowColumns.colConfig = newColConfig;
      }
      setShowColumns(newShowColumns);
      // setUpdate(update + 1);
    },
    [setShowColumns, showColumns]
  );
  //
  // Handle the checkboxes for each column
  const handleShowColumn = useCallback(
    (event, group, column) => {
      setUpdateShowColumn(true);
      const newShowColumns = { ...showColumns };
      const newColConfig = { ...showColumns.colConfig };
      if (event.target.checked) {
        newShowColumns.totalShowColumns++;
        newShowColumns.groups[group].showColumnsNumber++;
        newShowColumns.groups[group].showColumns = [
          ...newShowColumns.groups[group].showColumns,
          column,
        ];
        newColConfig[column] = { ...newColConfig[column], show: 1 };
        newShowColumns.colConfig = newColConfig;
      } else {
        newShowColumns.totalShowColumns--;
        newShowColumns.groups[group].showColumnsNumber--;
        newShowColumns.groups[group].showColumns = newShowColumns.groups[
          group
        ].showColumns.filter((col) => col !== column);
        newColConfig[column] = { ...newColConfig[column], show: 0 };
        newShowColumns.colConfig = newColConfig;
      }
      setShowColumns(newShowColumns);
      // setUpdate(update + 1);
    },
    [setShowColumns, showColumns]
  );

  //
  // console.log({ showColumns });
  //
  const content = useMemo(
    () =>
      !!Object.keys(showColumns).length && (
        <div className="relative w-96 space-y-6 p-1">
          {/********************* Manage Show/Hide all Columns ************************/}
          <div className="flex flex-row justify-between p-3 font-semibold">
            <Checkbox
              onChange={toggleAllColumnsVisibility}
              indeterminate={
                totalShowColumns > 1 && totalColumns > totalShowColumns
              }
              checked={totalColumns === totalShowColumns}
            >
              <p> {t("contacts.showAll")}</p>
            </Checkbox>
            <p>{`${totalShowColumns} ${t("voip.of")} ${totalColumns} ${t(
              "contacts.columns"
            )}`}</p>
          </div>

          <div
            className="space-y-6 overflow-y-auto overflow-x-hidden p-3"
            style={{ maxHeight: windowSize.height / 1.6 }}
          >
            {/********************* Main Column ************************/}
            <Card>
              <Checkbox disabled checked>
                <p className="max-w-80 truncate font-sans text-sm">
                  {mainColumn}
                </p>
              </Checkbox>
            </Card>
            {/********************* Groups & Columns ************************/}
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              {Object.keys(groups).map((group, index) => (
                <Collapse
                  key={index}
                  collapsible={
                    groups[group].allColumns?.length ? "icon" : "disabled"
                  }
                  defaultActiveKey={index === 0 ? [`${group}_${index}`] : []}
                  expandIcon={({ isActive }) => (
                    <CaretRightOutlined
                      style={{ fontSize: 14 }}
                      rotate={isActive ? 90 : 0}
                    />
                  )}
                  items={[
                    {
                      key: `${group}_${index}`,
                      label: (
                        <Checkbox
                          key={group}
                          onChange={(e) => handleShowGroup(e, group)}
                          indeterminate={
                            groups[group]?.showColumnsNumber !== 0 &&
                            groups[group]?.columnsNumber >
                              groups[group]?.showColumnsNumber
                          }
                          checked={
                            groups[group]?.columnsNumber ===
                            groups[group]?.showColumnsNumber
                          }
                          disabled={!groups[group].allColumns?.length}
                        >
                          <p className="max-w-48 truncate font-semibold uppercase text-slate-500">
                            {group}
                          </p>
                        </Checkbox>
                      ),
                      extra: (
                        <span className="font-semibold text-slate-500">{`${
                          groups[group]?.showColumnsNumber
                        } ${t("voip.of")} ${
                          groups[group]?.columnsNumber
                        }`}</span>
                      ),
                      children: (
                        <SortableContext
                          items={showColumns.groups[group].allColumns.map(
                            (col) => `${group}-${col}`
                          )}
                          strategy={verticalListSortingStrategy}
                        >
                          <Space
                            direction="vertical"
                            split={<Divider style={{ margin: 0 }} />}
                            style={{ width: "20rem" }}
                          >
                            {showColumns.groups[group]?.allColumns?.map(
                              (col, i) => (
                                <SortableItem
                                  key={`${group}-${col}`}
                                  id={`${group}-${col}`}
                                  group={group}
                                  col={col}
                                  handleShowColumn={handleShowColumn}
                                  groups={showColumns.groups}
                                />
                              )
                            )}
                          </Space>
                        </SortableContext>
                      ),
                    },
                  ]}
                />
              ))}
            </DndContext>
          </div>
        </div>
      ),
    [
      groups,
      handleDragEnd,
      handleShowColumn,
      handleShowGroup,
      mainColumn,
      sensors,
      showColumns,
      t,
      toggleAllColumnsVisibility,
      totalColumns,
      totalShowColumns,
      windowSize.height,
    ]
  );

  return (
    <Popover
      content={content}
      placement="bottomRight"
      arrow={false}
      trigger={["click"]}
      overlayInnerStyle={{ padding: 0 }}
      onOpenChange={(open) => setOpenPopover(open)}
    >
      <Badge dot={totalColumns > totalShowColumns}>
        <Button ref={Refs_IDs.families_columns_manipulation} icon={columns3Cog}>
          <DownOutlined style={{ fontSize: 15 }} />
        </Button>
      </Badge>
    </Popover>
  );
};
//
const SortableItem = ({ id, group, col, handleShowColumn, groups }) => {
  const isTimestampGroup = group === "Timestamps";
  const isDisabled = isTimestampGroup || groups[group]?.allColumns?.length < 2;
  //
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: `${group}-${col}`,
    disabled: isDisabled,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div ref={setNodeRef} style={style} className="flex items-center space-x-3">
      <MenuOutlined
        {...attributes}
        {...listeners}
        style={{
          fontSize: 14,
          cursor: isDisabled ? "not-allowed" : "move",
          color: isDisabled ? "#ccc" : "",
        }}
      />
      <Checkbox
        onChange={(e) => handleShowColumn(e, group, col)}
        checked={groups[group]?.showColumns?.includes(col)}
      >
        <p className="max-w-64 truncate text-sm">{col}</p>
      </Checkbox>
    </div>
  );
};
//
export const columns3Cog = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="18"
    height="18"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="1.75"
    strokeLinecap="round"
    strokeLinejoin="round"
    className="lucide lucide-columns3-cog-icon lucide-columns-3-cog"
  >
    <path d="M10.5 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v5.5" />
    <path d="m14.3 19.6 1-.4" />
    <path d="M15 3v7.5" />
    <path d="m15.2 16.9-.9-.3" />
    <path d="m16.6 21.7.3-.9" />
    <path d="m16.8 15.3-.4-1" />
    <path d="m19.1 15.2.3-.9" />
    <path d="m19.6 21.7-.4-1" />
    <path d="m20.7 16.8 1-.4" />
    <path d="m21.7 19.4-.9-.3" />
    <path d="M9 3v18" />
    <circle cx="18" cy="18" r="3" />
  </svg>
);
//
export default memo(ShowHideColumns);
