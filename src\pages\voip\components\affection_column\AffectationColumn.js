import { useState } from "react";
import { useSelector } from "react-redux";
import { But<PERSON>, Divider, Popover, Space, Tag, Tooltip } from "antd";
import AffectationContent from "./AffectationContent";
import { HiOutlineBuildingOffice } from "react-icons/hi2";
import {
  CloseOutlined,
  LoadingOutlined,
  TeamOutlined,
} from "@ant-design/icons";
import { AiOutlineShoppingCart } from "react-icons/ai";
import { LuPalmtree } from "react-icons/lu";
import { CgUserlane } from "react-icons/cg";
import { deleteAffectation } from "../../services/services";
import { toastNotification } from "../../../../components/ToastNotification";
import { useDispatch } from "react-redux";
import { CiUser } from "react-icons/ci";
import { Blocks, HeartHandshake } from "lucide-react";
import TicketIconSphere from "components/icons/TicketIconSphere";
import { URL_ENV } from "index";
import DisplayAvatar from "../DisplayAvatar";
import { renderIcon } from "pages/clients&users/components/RenderColumnsTable";
import {
  generateUrlToView360,
  truncateString,
} from "pages/voip/helpers/helpersFunc";
import { familyWithAvatar } from "pages/rmc/mailing/main-components/columnsTable";
import { getFamilyNameById } from "pages/clients&users/FamilyRouting";
import { useNavigate } from "react-router-dom";

//

//
const AffectationColumn = ({
  affectation,
  id,
  setDataSource,
  t,
  access,
  user,
  canUserCreateOrUpdate,
  infoMetaTable,
  setFamilyToAdd,
  setOpenDrawerCreate,
  setExternalSource,
  handleDisplayElementInfo,
}) => {
  //
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const logs = useSelector(({ voip }) => voip.logs);
  //
  const UpdateOnlyDataSource = infoMetaTable
    ? infoMetaTable?.page * infoMetaTable?.limit > logs.length
    : true;

  //
  const [loadingDelete, setLoadingDelete] = useState(false);
  const [openPopCon, setOpenPopCon] = useState(false);

  //
  const familiesName = {
    1: t("contacts.company"),
    2: t("contacts.contact"),
    3: t("menu1.deals"),
    4: t("contacts.user"),
    5: t("contacts.product"),
    6: t("contacts.ticket"),
    7: t("contacts.project"),
    8: t("contacts.booking"),
    9: t("contacts.leads"),
  };
  //
  const families = {
    1: {
      label: t("contacts.company"),
      icon: <HiOutlineBuildingOffice style={{ fontSize: 16 }} />,
    },
    2: {
      label: t("contacts.contact"),
      icon: <TeamOutlined style={{ fontSize: 15 }} />,
    },
    3: {
      label: "Deals",
      icon: <HeartHandshake size={18} style={{ marginTop: 4 }} />,
    },
    4: {
      label: t("contacts.user"),
      icon: <CiUser style={{ fontSize: 17, marginTop: 3 }} />,
    },
    5: {
      label: t("contacts.product"),
      icon: <AiOutlineShoppingCart style={{ fontSize: 16, marginTop: 2 }} />,
    },
    6: {
      label: t("contacts.ticket"),
      icon: <TicketIconSphere size={19} style={{ marginTop: 2 }} />,
    },
    7: {
      label: t("contacts.project"),
      icon: <Blocks size={17} />,
    },
    8: {
      label: t("contacts.booking"),
      icon: <LuPalmtree style={{ fontSize: 16 }} />,
    },
    9: {
      label: t("contacts.leads"),
      icon: <CgUserlane style={{ fontSize: 15, marginTop: 3 }} />,
    },
  };
  //
  const AffectationContents = (
    <div className="flex items-center justify-center">
      <AffectationContent
        elementId={id}
        source={"call"}
        setData={setDataSource}
        t={t}
        access={access}
        user={user}
        UpdateOnlyDataSource={UpdateOnlyDataSource}
        setFamilyToAdd={setFamilyToAdd}
        setOpenDrawerCreate={setOpenDrawerCreate}
        setExternalSource={setExternalSource}
        setOpenPopCon={setOpenPopCon}
      />
    </div>
  );
  //
  const handleDelete = async () => {
    if (!affectation.id) {
      toastNotification(
        "error",
        "Cannot delete this affectation, please contact our support",
        "topRight"
      );
      return;
    }
    try {
      setLoadingDelete(true);
      const { status } = await deleteAffectation(affectation?.id);
      if (status === 200) {
        if (UpdateOnlyDataSource)
          setDataSource((prev) =>
            prev.map((item) =>
              item._id === id ? { ...item, affectation: null } : item
            )
          );
        else {
          const newLogs = logs.map((call) => {
            if (call._id === id) {
              return {
                ...call,
                affectation: null,
              };
            }
            return call;
          });
          dispatch({
            type: "GET_LOG_SUCCESS",
            payload: { data: newLogs },
          });
        }
      }
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? err.message : err);
    } finally {
      setLoadingDelete(false);
    }
  };
  //
  const shouldDisableAffectation =
    access.companies !== "1" &&
    access.contact !== "1" &&
    access.leads !== "1" &&
    access.deals !== "1" &&
    access.ticket !== "1" &&
    access.projects !== "1" &&
    access.products !== "1" &&
    access.booking !== "1";
  //
  if (!affectation?.affect_label)
    return (
      <Popover
        content={AffectationContents}
        title={t("voip.assignCallTo")}
        open={openPopCon}
        onOpenChange={(open) => setOpenPopCon(open)}
        trigger={["click"]}
        placement="bottomLeft"
        arrow={false}
      >
        <Button
          type="dashed"
          size="small"
          disabled={shouldDisableAffectation || canUserCreateOrUpdate === false}
        >
          {t("voip.assignment")}
        </Button>
      </Popover>
    );
  //
  const tooltipContent = (
    <div className="space-y-0.5">
      <div className="flex flex-nowrap space-x-1 whitespace-nowrap">
        <span>{t("mailing.affectTo")}</span>
        <span className="font-semibold">
          {`${affectation.affect_label} ${
            familyWithAvatar[affectation.affected_family_id]
              ? `(${getFamilyNameById(
                  t,
                  Number(affectation.affected_family_id)
                )})`
              : ""
          }`}
        </span>
      </div>

      <div className="flex justify-end">
        <Space size={3} split={<Divider type="vertical" />}>
          <Button
            key="more_info"
            size="small"
            type="link"
            style={{ paddingLeft: 0, paddingRight: 0 }}
            onClick={() => {
              handleDisplayElementInfo(affectation.affect_label, {
                id: affectation.affect_to,
                familyId: affectation.affected_family_id,
              });
              document.body.click();
            }}
          >
            {t("voip.moreInfo")}
          </Button>
          <Button
            key="view_sphere"
            size="small"
            type="link"
            style={{ paddingLeft: 0, paddingRight: 0 }}
            onClick={() =>
              navigate(
                generateUrlToView360(
                  affectation.affected_family_id,
                  affectation.affect_to,
                  "v2"
                )
              )
            }
          >
            {t("voip.view360")}
          </Button>
        </Space>
      </div>
    </div>
  );
  //
  return (
    <div
      key={affectation._id}
      className="inline-flex cursor-pointer items-center space-x-1.5 rounded-md bg-gray-500/10 px-1 py-0.5 text-xs font-semibold  text-gray-600 "
    >
      <Tooltip
        key="update_identification"
        arrow={false}
        overlayInnerStyle={{ width: "fit-content" }}
        title={tooltipContent}
        placement="topRight"
      >
        <div className="flex  items-center space-x-1">
          <DisplayAvatar
            size={22}
            name={affectation.affect_label}
            urlImg={
              affectation.avatar &&
              `${
                URL_ENV.REACT_APP_BASE_URL + URL_ENV.REACT_APP_SUFFIX_AVATAR_URL
              }${affectation.avatar}`
            }
            icon={renderIcon(Number(affectation.affected_family_id), 16)}
          />
          <span>{truncateString(affectation.affect_label, 25)}</span>
        </div>
      </Tooltip>
      {canUserCreateOrUpdate !== false && (
        <Tooltip key="delete_identification" title={t("voip.delete")}>
          <Button
            size="small"
            type="text"
            loading={loadingDelete}
            icon={<CloseOutlined />}
            // style={{ width: 20, height: 18 }}
            onClick={handleDelete}
          />
        </Tooltip>
      )}
    </div>
  );
};

export default AffectationColumn;
