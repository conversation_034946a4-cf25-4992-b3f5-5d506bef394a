import React, { useEffect, useRef } from "react";
import { Form, Input, Button, Space } from "antd";
import { useState } from "react";
import axiosInstance from "../services/axiosInstance";
import { useTranslation } from "react-i18next";
import {
  CaretRightOutlined,
  PlusCircleOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import { useParams } from "react-router-dom";
import { toastNotification } from "./ToastNotification";
import NewTableDraggable from "./NewTableDraggable";
import LabelTable from "./LabelTable";
import { SubmitKeyPress } from "../utils/SubmitKeyPress";

const Trigger = ({
  setEditingKey,
  editingKey,
  setIdTrigger,
  trigger_id,
  setEditingKeyRules,
  setDataRules,
}) => {
  const [form] = Form.useForm();
  const [count, setCount] = useState(0);
  const [rank, setRank] = useState(null);
  const { family } = useParams();
  const [data, setData] = useState([]);
  const [id, setId] = useState(null);
  const [selectedRowKey, setSelectedRowKey] = useState("");
  const [loading, setLoading] = useState(false);
  const isEditing = (record) => record.key === editingKey;
  const [t] = useTranslation("common");
  const [dataSaved, setDataSaved] = useState([]);

  const inputRefs = useRef([]);

  //   useEffect(() => {
  //     const getPipelines = async () => {
  //       setLoading(true);
  //       try {
  //         const {
  //           data: { data },
  //         } = await axiosInstance.get(
  //           `/pipelines/pipelines-by-family/${
  //             items.find((el) => el.item === family)?.key || 1
  //           }`
  //         );
  //         setData(data.map((el, i) => ({ ...el, key: el.id, rank: i + 1 })));
  //         setDataSaved(data);
  //         if (data.length > 0) {
  //           setCount(Math.max(...data.map((el) => el.id)));
  //           setIdTrigger(data[0].id);
  //           setSelectedRowKey(data[0].id);
  //         }
  //         setLoading(false);
  //       } catch (err) {
  //         setLoading(false);
  //         toastNotification("error", t("toasts.somethingWrong"), "topRight");

  //         console.log(err);
  //       }
  //     };
  //     getPipelines();
  //   }, [family]);
  useEffect(() => {
    inputRefs.current.forEach((input) => {
      input?.focus();
    });
  }, [data.length, id]);

  const onFinishFailed = (values) => {
    console.log(values);
  };
  const handleKeyPress = (event) => {
    SubmitKeyPress(event, form);
  };
  const handleClick = (event) => {
    event.stopPropagation();
  };
  const EditableCell = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    const inputNode = (
      <Input
        ref={(el) => (inputRefs.current[0] = el)}
        onKeyPress={handleKeyPress}
        placeholder="Trigger name"
        onClick={handleClick}
      />
    );

    return (
      <td {...restProps}>
        {editing ? (
          <Form.Item
            name={dataIndex}
            style={{
              margin: 0,
            }}
            rules={[
              {
                required: true,
                message: `Trigger Name is required`,
              },
            ]}
          >
            {inputNode}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };
  const edit = (record) => {
    if (record) {
      form.setFieldsValue({
        label: record.label,
        rank: record.rank,
      });
      setRank(record.rank);
      setId(record.id);
    } else {
      form.setFieldsValue({
        label: "",
      });
    }
    setEditingKey(record.key);
  };
  const cancel = (record) => {
    setEditingKey("");
    setId(null);
    if (!record.id) {
      setData(data.filter((item) => item.key !== record.key));
    }
  };

  const columns = [
    {
      title: t("activities.name"),
      dataIndex: "label",
      key: "label",
      editable: true,
      sorter: (a, b) => a.label.localeCompare(b.label),
      render: (_, record) => (
        <div className="flex justify-between">
          <LabelTable record={record} editingKey={editingKey} edit={edit} />

          {record.id == trigger_id ? (
            <CaretRightOutlined className="ml-1" />
          ) : (
            ""
          )}
        </div>
      ),
    },
  ];
  const mergedColumns = columns.map((col, index) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record) => ({
        record,
        inputType: col.dataIndex === "input",
        dataIndex: col.dataIndex,
        title: col.title,
        editing: isEditing(record),
        index,
      }),
    };
  });
  const handleAdd = () => {
    setId(null);
    const newData = {
      key: count + 1,
      label: `  `,
      color: "",
      disabled: true,
    };

    setData([...data, newData]);
    form.setFieldsValue({
      label: "",
    });
    setEditingKey(count + 1);
    setCount(count + 1);
  };
  const onSelectChange = (selectedRowKeys) => {
    setSelectedRowKey(selectedRowKeys[0]);
    if (selectedRowKeys !== trigger_id) {
      setEditingKeyRules("");
      setIdTrigger(selectedRowKeys[0]);
    }
  };
  const save = () => {};
  const rowSelection = {
    type: "radio",

    selectedRowKeys: [selectedRowKey],

    onChange: onSelectChange,
    getCheckboxProps: (record) => ({
      style: { display: !record.id ? "none" : "flex" }, // Masque la case à cocher

      // Column configuration not to be checked
    }),
  };
  const rowClassName = (record) => {
    return selectedRowKey == record.key ? "selected-row" : "";
  };
  const onRow = (record, rowIndex) => {
    return {
      onClick: () => {
        if (record.id) {
          setSelectedRowKey(record.key);
          onSelectChange([record.key]);
        }
      },
    };
  };
  return (
    <Space direction="vertical" style={{ width: "100%" }}>
      <Space direction="vertical" style={{ width: "100%" }}>
        <div className="flex items-center justify-between pt-3">
          <span className="ml-2 mr-2 rounded	 px-2.5 py-0.5 text-base font-medium text-[#2253d5] dark:bg-blue-200 dark:text-blue-800">
            {/* {t(`pipeline.pipelines${family}`)} */}
          </span>
          <Button
            type="primary"
            shape="circle"
            icon={<PlusOutlined />}
            onClick={handleAdd}
            disabled={loading ? true : editingKey ? true : false}
          />
        </div>
      </Space>

      <NewTableDraggable
        columns={columns}
        setLoading={setLoading}
        isEditing={isEditing}
        data={data}
        setData={setData}
        loading={loading}
        save={save}
        edit={edit}
        EditableCell={EditableCell}
        onFinishFailed={onFinishFailed}
        cancel={cancel}
        form={form}
        apiRank="/pipelines/updateRank"
        editingKey={editingKey}
        api="pipelines"
        btnText={t("helpDesk.addFolder")}
        pagination={false}
        setDataStage={setDataRules}
        pipeline_id={trigger_id}
        onRow={onRow}
        rowSelection={rowSelection}
        rowClassName={rowClassName}
      />

      <div style={{ width: "0" }}>
        <Button
          icon={<PlusCircleOutlined />}
          onClick={handleAdd}
          type="link"
          block
          disabled={loading ? true : editingKey ? true : false}
        >
          {/* {t("companies.addPipeline")} */}
        </Button>
      </div>
    </Space>
  );
};
export default Trigger;
