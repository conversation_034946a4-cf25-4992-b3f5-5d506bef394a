import { Popover, Typography } from "antd";
import { humanDate } from "pages/voip/helpers/helpersFunc";

const PopoverTask = ({
  children,
  t,
  setIdCall,
  setTaskId,
  setOpenTask,
  idCall,
  task,
}) => {
  return (
    <Popover
      style={{ cursor: "default" }}
      key={"task" + task.id}
      title={t("voip.taskInfo")}
      content={
        <div className="flex flex-col">
          <div className="flex flex-row justify-between space-x-1">
            <span className="font-semibold text-slate-500">
              {t("table.startDate")}
            </span>
            <div>
              <span className="font-semibold">
                {humanDate(`${task.start_date} ${task.start_time}`, t, "table")}
              </span>
            </div>
          </div>
          <div className="flex flex-row justify-between space-x-1">
            <span className="font-semibold text-slate-500">
              {t("table.endDate")}
            </span>
            <div>
              <span className="font-semibold">
                {humanDate(`${task.end_date} ${task.end_time}`, t, "table")}
              </span>
            </div>
          </div>
          {/* <Typography.Link
            onClick={(e) => {
              e.preventDefault();
              // setIdCall(idCall);
              setTaskId(task.id);

              setOpenTask(true);
            }}
            className="mt-3 flex justify-end font-semibold"
          >
            {t("voip.moreInfo")}
          </Typography.Link> */}
        </div>
      }
    >
      {children}
    </Popover>
  );
};

export default PopoverTask;
