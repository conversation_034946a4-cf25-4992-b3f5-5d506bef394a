import React, { useEffect, useState } from "react";
import { Input, Space, message } from "antd";
import MainService from "../../../../../services/main.service";
import { FiSearch } from "react-icons/fi";
import { useTranslation } from "react-i18next";

const { Search } = Input;

function NoteSearch({
  elementId,
  searchResultNote,
  setSearchResultNote,
  setSearchLoading,
  isExternal,
}) {
  const onSearch = (value, _e, info) => {
    console.log("value", value);
    console.log("info", info);
    console.log("e", _e);
    if (value) {
      searchFunction(value);
    } else {
      if (info?.source == "clear") {
        console.log("here");
        setSearchResultNote(null);
      } else {
        setSearchResultNote(null);
      }
    }
  };
  const [t] = useTranslation("common");

  const searchFunction = (value) => {
    let data = {
      search: value,
    };

    setSearchLoading(true);

    MainService.searchNotes360(data, elementId, isExternal)
      .then((response) => {
        console.log(response?.data?.data);
        if (response?.data?.data?.length > 0) {
          let notesResultSearch = response?.data?.data;

          //
          let res = [];
          notesResultSearch.forEach((el) => {
            res.push({
              ...el.note,
              permission: el.permission,
            });
          });

          console.log("res", res);

          setSearchResultNote(res);
        } else {
          message.warning("No results for the searched keyword");
        }
      })
      .catch((error) => {
        console.log(error);
      })
      .finally(() => {
        setSearchLoading(false);
      });
  };

  // useEffect(() => {
  //   if (!isSearching) {
  //     setBeforeSearchData(notes);
  //   }
  //   console.log("notes length", notes.length);
  // }, [notes]);

  return (
    <>
      <Search
        placeholder={t("notes360.searchNotes")}
        onSearch={onSearch}
        onChange={(e) => {
          if (e.target.value == "") {
            setSearchResultNote(null);
          }
        }}
        allowClear
        style={{
          width: "100%",
          marginTop: "1rem",
          padding: "0.5rem",
        }}
      />
    </>
  );
}

export default NoteSearch;
