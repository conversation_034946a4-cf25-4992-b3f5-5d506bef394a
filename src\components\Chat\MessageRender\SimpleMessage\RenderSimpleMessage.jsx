import { Tooltip, Typography } from "antd";
import {
  convertToPlain,
  fileMessageReplyType,
  forwardMessageTypes,
  getUserFromMsg,
  getName,
  imageMessageReplyType,
  safeText,
  simpleMessageReplyTypes,
  simpleMessageTypes,
  addHttpIfNeeded,
} from "../../../../pages/layouts/chat/utils/ConversationUtils";
import { useTranslation } from "react-i18next";
import "./renderSimpleMessage.css";
import { useSelector } from "react-redux";
import { DeleteOutlined } from "@ant-design/icons";
import { moment_timezone } from "../../../../App";
import {
  EMAIL_REGEX,
  EMOJI_REGEX,
  HAPPY__REGEX,
  HASHTAG_REGEX,
  LINK_REGEX,
  PRE_CODE_REGEX,
  SPECIAL_CHAR_REGEX,
  SPLIT_ITEM_REGEX,
  PHONE_NUMBER_REGEX,
  DATE_FORMAT_REGEX,
} from "../../../../utils/regex";
import { useEffect, useState } from "react";
//REGEX
//const acceptedAarray = ["no_chat", "main"];
const { Text, Paragraph } = Typography;
const mustTruncate = (element) =>
  element?.offsetHeight < element?.scrollHeight ||
  element?.offsetWidth < element?.scrollWidth;

const RenderSimpleMessage = ({ message, onItemSelect, source }) => {
  const { t } = useTranslation("common");
  const { searchMessageTerm, currentUser } = useSelector((state) => state.chat);
  const [showFullContent, setShowFullContent] = useState(false);
  const [showButton, setShowButton] = useState(false);

  const currentUserValue = (element) => {
    const user_id = element.getAttribute("userid");
    return user_id === currentUser?._id
      ? "ME"
      : Number(user_id) === 0
      ? "ALL"
      : "NO";
  };
  const createMarkup = (messageProps) => {
    try {
      let text = safeText(messageProps);

      if (PRE_CODE_REGEX.test(text)) {
        return { __html: text };
      }

      if (source === "search" && searchMessageTerm?.value.trim() !== "") {
        const escapedSearchTerm = searchMessageTerm?.value
          ?.trim()
          ?.replace(SPECIAL_CHAR_REGEX, "\\$&");
        const regexSearch = new RegExp(escapedSearchTerm, "gmi");
        let parent = document.createElement("div");
        parent.innerHTML = text;

        if (!parent.classList.contains("message")) {
          // Déplace le contenu texte dans l'élément parent
          const textNode = document.createTextNode(parent.textContent);
          parent.innerHTML = ""; // Efface le contenu actuel de l'élément parent
          parent.appendChild(textNode);
        }
        text = parent.innerText?.replace(regexSearch, (match) => {
          return `<span class='bg-yellow-500'>${match}</span>`;
        });
      }

      const words = text.split(SPLIT_ITEM_REGEX);

      const parsedWords = words.map((word) => {
        if (word === "<p>" || source === "search") {
          return word;
        }
        //
        if (DATE_FORMAT_REGEX.test(word))
          word = word?.replace(DATE_FORMAT_REGEX, (match) => {
            return (
              '<span  aria-label="date" title="date" date="' +
              match +
              '" class=" inline-block " >' +
              match +
              "</span>"
            );
          });
        // Handle links
        else if (
          LINK_REGEX.test(convertToPlain(word)) &&
          !EMAIL_REGEX.test(convertToPlain(word))
        ) {
          word = convertToPlain(word);
          word = word.replace(LINK_REGEX, (link) => {
            return `<a target="_blank"  aria-label="link" href="${addHttpIfNeeded(
              convertToPlain(link)
            )}">${convertToPlain(link)}</a>`;
          });
        } else {
          word = word?.replace(
            HASHTAG_REGEX,
            (hash) =>
              `<span
              title="${convertToPlain(hash)}"
              aria-label="hashtag">${convertToPlain(hash)}</span>`
          );
          word = word?.replace(EMAIL_REGEX, `<a href="mailto:$1">$1</a>`);

          word = word?.replace(EMOJI_REGEX, (match) => {
            return (
              '<span aria-label="emoji" class="text-[20px] inline-block justify-start items-start " >' +
              match +
              "</span>"
            );
          });
          word = word?.replace(PHONE_NUMBER_REGEX, (match) => {
            return (
              '<span  aria-label="phone-number" title="call" number="' +
              match +
              '" class=" inline-block " >' +
              match +
              "</span>"
            );
          });

          if (!HASHTAG_REGEX.test(word))
            word = word?.replace(
              HAPPY__REGEX,

              (match) =>
                '<span aria-label="birthday" class="cursor-pointer   font-semibold text-xl   bg-gradient-to-r      from-purple-400 to-red-400  text-transparent bg-clip-text " >' +
                match.trim().toLocaleUpperCase() +
                "</span>"
            );
        }

        return word;
      });
      const elementMentions = document.querySelectorAll(
        '[data-type="mention"]'
      );

      if (elementMentions) {
        elementMentions.forEach((element) => {
          element.setAttribute(
            "current-user",
            source === "no_chat" ? "NO" : currentUserValue(element)
          );
        });
      }

      return { __html: parsedWords.join(" ") };
    } catch (error) {}
  };
  useEffect(() => {
    const checkTruncation = () => {
      const paragraphElement = document.getElementById(
        `message-content-${source}${message._id}`
      );
      if (!paragraphElement) return;
      setShowButton(mustTruncate(paragraphElement));
    };
    if (source === "reply") {
      const timeoutId = setTimeout(checkTruncation, 500);
      return () => clearTimeout(timeoutId);
    } else {
      checkTruncation();
    }
  }, [message._id, source]);
  return (
    <div
      className={
        source === "main" && simpleMessageReplyTypes.includes(message.type)
          ? "response"
          : ""
      }
    >
      {source === "main" &&
        (((simpleMessageReplyTypes.includes(message.type) ||
          fileMessageReplyType.includes(message.type) ||
          imageMessageReplyType.includes(message.type)) &&
          !message.main_message) ||
          (convertToPlain(message?.main_message?.message).trim().length > 0 &&
            simpleMessageTypes.includes(message.main_message?.type))) &&
        !forwardMessageTypes.includes(message.type) && (
          <div className=" flex  flex-nowrap items-start justify-start space-x-1 ">
            <Text
              type="secondary"
              className=" flex items-start space-x-2   whitespace-nowrap "
            >
              {" "}
              {t("chat.reply.message")}{" "}
              {message?.main_message && (
                <p className=" mx-1 max-w-[200px]  truncate italic">
                  {t("chat.reply.sentBy") +
                    " " +
                    getName(
                      message?.main_message?.type === "message_from_bot"
                        ? message?.main_message?.bot.name
                        : getUserFromMsg(message.main_message?.sender_id)?.name,
                      "name"
                    )}
                </p>
              )}{" "}
              :{" "}
            </Text>

            {!message?.main_message ? (
              <Text type="secondary">
                <DeleteOutlined className="ml-0 mr-0.5" />
                {t("chat.delete.messaageDeleted")}
              </Text>
            ) : (
              <Paragraph
                onClick={() => onItemSelect(message)}
                ellipsis={{ rows: 1, expandable: false }}
                className=" mb-0  max-w-lg cursor-pointer text-[#1677ff] hover:opacity-70"
              >
                {convertToPlain(message?.main_message?.message)}
              </Paragraph>
            )}
          </div>
        )}
      <div className=" block">
        <p
          id={"message-content-" + source + message._id}
          style={{
            wordBreak: "break-word",
          }}
          className={`message py-1 ${
            !showFullContent ? "line-clamp-[10]" : " line-clamp-none"
          }`}
          dangerouslySetInnerHTML={createMarkup(message?.message)}
        />

        {showButton && (
          <Text
            className="hober:text-blue-400 cursor-pointer text-blue-500 hover:underline "
            onClick={() => setShowFullContent((p) => !p)}
          >
            {showFullContent ? t("chat.less") : t("chat.more")}
          </Text>
        )}
      </div>
      <div className="flex items-center gap-x-1">
        {message.edit != 0 && (
          <Tooltip
            overlayClassName={
              currentUser?._id === message?.sender_id ? "" : "hidden"
            }
            placement="right"
            title={
              t("chat.edit.text_message_old") +
              (convertToPlain(message?.edit?.old_messages?.at?.(-1)).length > 0
                ? convertToPlain(message?.edit?.old_messages?.at?.(-1))
                : "(" + t("vue360.empty") + ")") +
              "'. "
            }
          >
            <Text type="secondary">
              ({t("chat.edit.text_message_update_at")}{" "}
              {moment_timezone(message.edit.edit_at).format(
                `Do MMMM, [${t("chat.edit.at")}]  LT`
              )}
              )
            </Text>
          </Tooltip>
        )}
      </div>
    </div>
  );
};

export default RenderSimpleMessage;
