import { useCallback, useEffect, useRef, useState } from "react";
import { useLocation } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { useWindowSize } from "pages/clients&users/components/WindowSize";
import { getNode } from "./tourConfig";
import { CustomTour } from "./CustomTour";
import { useTranslation } from "react-i18next";
import { message, Spin } from "antd";
import { toastNotification } from "components/ToastNotification";
import { SET_TOURS_ACCESS } from "new-redux/constants";
import { setOpenTourInProfile } from "new-redux/actions/menu.actions/menu";
import MainService from "services/main.service";
import i18n from "translations/i18n";

const TourWrapper = () => {
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const windowSize = useWindowSize();
  const isOpenTour = useRef(null);
  const location = useLocation();
  const pathname = location.pathname;
  //
  isOpenTour.current = useSelector((state) => state.menu.isOpenTour);
  const toursAccess = useSelector(
    (state) => state.user.user?.toursAccess || {}
  );
  //
  const pathHaveTour =
    Boolean(Number(toursAccess[pathname.replace("/", "")])) ||
    (pathname.includes("/v2/") && Boolean(Number(toursAccess.viewSphere)))
      ? true
      : false;
  //
  const [tourSteps, setTourSteps] = useState({});
  const [shouldFetchTour, setShouldFetchTour] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  //
  //
  const handleCloseTour = async () => {
    dispatch(
      updateToursConfig("close", toursAccess, pathname.replace("/", ""), t)
    );
  };
  //
  useEffect(() => {
    if (/*pathHaveTour === 0 || */ !pathHaveTour) return;
    setShouldFetchTour(true);
  }, [pathHaveTour, pathname, t]);
  //
  //
  const getTour = useCallback(async () => {
    if (!shouldFetchTour) return;

    try {
      setIsLoading(true);
      const formData = new FormData();
      formData.append(
        "path",
        pathname.includes("/v2/") ? "viewSphere" : pathname.replace("/", "")
      );
      const {
        data: { data, success },
      } = await MainService.getTourSteps(
        // pathname.includes("/v2/") ? "viewSphere" : pathname.replace("/", ""),
        formData,
        i18n.language
      );
      if (!success) return;

      let steps = data.steps
        .map((step) => {
          const node = getNode(step.selector);
          if (!node) return null;

          return {
            title: step.title,
            placement: step.placement,
            cover: step.cover ? (
              <img
                alt={step.selector}
                style={{
                  maxHeight: windowSize.height / 2,
                  maxWidth: windowSize.width / 2,
                  objectFit: "scale-down",
                }}
                src={`${process.env.REACT_APP_URL_TENANT}storage/${step.cover}`}
              />
            ) : step.video ? (
              <video width="100%" height="360" controls>
                <source
                  src={`${process.env.REACT_APP_URL_TENANT}storage/${step.video}`}
                  type="video/mp4"
                />
              </video>
            ) : null,
            description: (
              <div dangerouslySetInnerHTML={{ __html: step.description }} />
            ),
            target: () => node,
          };
        })
        .filter((s) => !!s?.target());

      const intro = {
        title: data.name,
        cover: data.video ? (
          <video width="100%" height="360" controls>
            <source
              src={`${process.env.REACT_APP_URL_TENANT}storage/${data.video}`}
              type="video/mp4"
            />
          </video>
        ) : data.cover ? (
          <img
            alt={data.selector}
            style={{
              maxHeight: windowSize.height / 2,
              maxWidth: windowSize.width / 2,
              objectFit: "scale-down",
            }}
            src={`${process.env.REACT_APP_URL_TENANT}storage/${data.cover}`}
          />
        ) : null,
        description: (
          <div dangerouslySetInnerHTML={{ __html: data.description }} />
        ),
      };

      steps = [...(Boolean(data.is_intro) ? [intro] : []), ...steps];

      console.log({ steps });

      setTourSteps((prev) => {
        // const existingSteps = prev[pathname] || [];
        // if (JSON.stringify(existingSteps) === JSON.stringify(steps)) {
        //   return prev;
        // }
        return {
          ...prev,
          [pathname]: steps,
        };
      });
    } catch (err) {
      toastNotification("error", t("tours.fetchError"), "topRight");
      console.error("API returned failure", err);
    } finally {
      setShouldFetchTour(false);
      setIsLoading(false);
      dispatch(setOpenTourInProfile(false));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shouldFetchTour /*, pathname, t, windowSize*/]);

  useEffect(() => {
    let isMounted = true;
    const fetchData = async () => {
      if (isMounted && pathHaveTour) {
        await getTour();
      }
    };

    const timer = setTimeout(
      fetchData,
      isOpenTour.current ? 100 : pathname.includes("/v2/") ? 6000 : 2000
    );

    return () => {
      isMounted = false;
      clearTimeout(timer);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [getTour, pathname]);
  //

  return (
    <>
      {shouldFetchTour /*&& !tourSteps[pathname]*/ && (
        <Spin spinning={isLoading} tip={t("tours.loadingSteps")} fullscreen />
      )}

      {!!pathHaveTour && (
        <CustomTour
          t={t}
          open={!!pathHaveTour && !shouldFetchTour}
          steps={tourSteps[pathname] || []}
          onClose={handleCloseTour}
        />
      )}
    </>
  );
};
//
export const updateToursConfig =
  (method, toursAccess, pathname, t) => async (dispatch) => {
    // check if this path name already have a tour
    pathname = pathname.includes("/v2/") ? "viewSphere" : pathname;
    if (toursAccess[pathname] === undefined) {
      message.warning(t("tours.noGuide"));
      return;
    }
    //
    const tourPayload = toursAccess;
    tourPayload[pathname] = method === "close" ? 0 : 1;

    dispatch({ type: SET_TOURS_ACCESS, payload: tourPayload });

    const formData = {
      tours: tourPayload,
    };
    try {
      const { data } = await MainService.updateToursAccess(formData);
      console.log("✅ Updated tours:", data);
    } catch (err) {
      console.error("Cannot update tours access:", err);
    } finally {
      // dispatch(setOpenTourInProfile(method === "close" ? false : true));
    }
  };
//
export default TourWrapper;
