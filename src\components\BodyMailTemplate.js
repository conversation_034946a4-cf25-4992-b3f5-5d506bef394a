import React, { useEffect, useState } from "react";
import ReactQuill, { Quill } from "react-quill";
import ImageResize from "quill-image-resize-module-react";
import "react-quill/dist/quill.snow.css";
import quillEmoji from "react-quill-emoji";
import "react-quill-emoji/dist/quill-emoji.css";

const modules = {
  toolbar: {
    container: [["bold", "italic"], ["emoji"]],
  },
  "emoji-toolbar": true,
  "emoji-textarea": true,
  "emoji-shortname": true,
};

export default function BodyMailTemplate({ form, selectedPage }) {
  Quill.register("modules/imageResize", ImageResize);
  Quill.register(
    {
      "formats/emoji": quillEmoji.EmojiBlot,
      "modules/emoji-toolbar": quillEmoji.ToolbarEmoji,
      "modules/emoji-textarea": quillEmoji.TextAreaEmoji,
      "modules/emoji-shortname": quillEmoji.ShortNameEmoji,
    },
    true
  );

  const customBoldHandler = () => {
    console.log("clicked...");
  };
  const [value, setValue] = useState("");
  useEffect(() => {
    if (selectedPage.bodymail) {
      setValue(selectedPage?.bodymail);
    } else setValue("");
  }, [selectedPage]);
  useEffect(() => {
    if (value) form.setFieldsValue({ bodymail: value });
  }, [form, value]);
  return (
    <ReactQuill
      style={{ height: "200px" }}
      className="templateEmail"
      modules={{
        toolbar: {
          container: [
            [
              { header: "1" },
              { header: "2" },
              { header: [3, 4, 5, 6] },
              { font: [] },
            ],
            [{ size: [] }],
            [{ color: [] }, { background: [] }],
            ["bold", "italic", "underline", "strike", "blockquote"],
            [{ align: [] }],
            [{ list: "ordered" }, { list: "bullet" }],
            ["link", "image", "video"],
            ["emoji"],
            ["clean"],
            ["code-block"],
          ],
        },
        "emoji-toolbar": true,
        // "emoji-textarea": true,
        "emoji-shortname": true,
        imageResize: {
          parchment: Quill.import("parchment"),
          modules: ["Resize", "DisplaySize"],
          displayStyles: {
            width: "200px",
            height: "50px",
          },
          handleStyles: {
            width: "200px",
            height: "50px",
            // other camelCase styles for size display
          },
        },
      }}
      theme="snow"
      value={value}
      onChange={setValue}
    />
  );
}
