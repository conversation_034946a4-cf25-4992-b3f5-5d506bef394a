import { combineReducers } from "redux";

import fields from "./fieldsReducer";
import families from "./familiesReducer";
import types from "./typesReducer";
import menu from "./menuReducer";
import helpers from "./helpersReducer";

import form from "./formReducer";
import user from "./UserReducer";
import table from "./tableReducer";
import wiki from "./wikiReducer";
import configCompanies from "./configCompaniesReducer";
import chat from "./chatReducer";
import visio from "./visioReducer";
import ChatRealTime from "./chatRealTime";
import voip from "./voipReducer";
import TasksRealTime from "./tasksRealTime";
import contacts from "./contactsReducer";
import dashboardRealTime from "./dashboardRealTime";
import visioList from "./visioListReducer";
import voipBlackList from "./voip_blackList";
import notes from "./notesReducer";
import importReducer from "./importReducer";
import vue360 from "./vue360";
import files from "./filesReducer";
import rmc from "./rmcReducer";
import mailReducer from "./MailReducer";
import selfNotesReducer from "./selfNotesReducer";
import drive from "./driveReducer";
const rootReducer = combineReducers({
  fields,
  families,
  types,
  // token,
  menu,
  helpers,
  ChatRealTime,
  form,
  user,
  table,
  wiki,
  configCompanies,
  chat,
  visio,
  voip,
  TasksRealTime,
  contacts,
  dashboardRealTime,
  visioList,
  voipBlackList,
  notes,
  importReducer,
  vue360,
  files,
  rmc,
  mailReducer,
  selfNotesReducer,
  drive,
});

export default rootReducer;
