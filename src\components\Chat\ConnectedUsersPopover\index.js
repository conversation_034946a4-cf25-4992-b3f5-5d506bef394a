import { Input, List, Popover } from "antd";
import { useState } from "react";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { ConnectedUsersListItem } from "./listItem";
import { FiSearch } from "react-icons/fi";
import { getName } from "../../../pages/layouts/chat/utils/ConversationUtils";

export const ConnectedUsersPopover = ({ children, source }) => {
  const [search, setSearch] = useState("");
  const { selectedParticipants } = useSelector((state) => state.chat);
  const onlineUser = useSelector((state) => state.ChatRealTime.onlineUser);
  const [t] = useTranslation("common");

  return selectedParticipants ? (
    selectedParticipants.length > 0 && (
      <Popover
        content={
          <div className="flex w-80 flex-col space-y-2">
            <Input
              size="middle"
              placeholder={t("chat.searchSide.searchMembers")}
              prefix={<FiSearch className="text-slate-500" />}
              value={search}
              onChange={(e) =>
                setSearch(e.target.value.trimStart().replace(/\s{1,} /g, " "))
              }
              className="w-full flex-1"
              allowClear
            />
            <List
              className="membersList max-h-80 overflow-auto"
              dataSource={selectedParticipants?.filter(
                (el) =>
                  getName(el.name, "name")
                    ?.toLowerCase()
                    ?.includes(search.toLowerCase()) &&
                  onlineUser[el.uuid] === "online"
              )}
              renderItem={(item) => (
                <ConnectedUsersListItem item={item} source={source} />
              )}
            />
          </div>
        }
        trigger="click">
        {children}
      </Popover>
    )
  ) : (
    <></>
  );
};
