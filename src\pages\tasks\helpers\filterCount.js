// This helper functionis used to determine whether there is an active filter already or not. (advanced filter mainly)
export const getRelevantFiltersCount = (filters, excludedKeys) => {
  return (
    filters?.filter(
      (filter) =>
        !Object.keys(filter)?.some((key) => excludedKeys?.includes(key))
    ).length || 0
  );
};

export const rolesList = (t) => {
  return [
    {
      label: t("tasks.creatorRole"),
      value: 0,
    },
    {
      label: t("tasks.OwnerRole"),
      value: 1,
    },
    {
      label:
        t("tasks.MemberRole").charAt(0).toUpperCase() +
        t("tasks.MemberRole").slice(1),
      value: 2,
    },
    {
      label: t("tasks.followerRole"),
      value: 3,
    },
  ];
};
