import {
  CREATE_VISIO_SUCCESS,
  CREATE_VISIO_ERROR,
  GET_VISIO_SUCCESS,
  GET_VISIO_ERROR,
  IS_LOADING_VISIO,
  DELETE_VISIO_ERROR,
  DELETE_VISIO_SUCCESS,
  UPDATE_VISIO_SUCCESS,
  UPDATE_VISIO_ERROR,
  GET_ONE_VISIO_SUCCESS,
  GET_ONE_VISIO_ERROR,
  RESET_VISIO_STATE,
  //  MAKE_VISIO_FLOAT,
  TOGGLE_VISIO,
  TOGGLE_SIZE_VISIO,
  SET_VISIO_PARAMS,
} from "../constants";
const initialState = {
  visioList: [],
  new: "",
  errors: {},
  isLoading: false,
  total: "",
  pageLimit: "",
  isDeleted: false,
  isAdded: false,
  oneVisio: "",
  isOpen: false,
  size: "medium",
  visioParams: {
    visio_name: "",
    external: false,
    join_room: false,
    moderator: false,
    name: "",
    token: "",
    owner_id: "",
    id_visio: null,
    creatorId: "",
    visioStartDate: null,
  },
};

const visio = (state = initialState, action) => {
  const { type, payload } = action;
  switch (type) {
    case IS_LOADING_VISIO:
      return {
        ...state,
        isLoading: true,
      };
    case GET_VISIO_SUCCESS:
      let visios = [];

      payload.data.map((v) => {
        const date = new Date(v.created_at);
        const options = {
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
        };
        const outputDate = date
          .toLocaleDateString("fr-FR", options)
          .replace(",", "");
        visios.push({ ...v, key: v.id, startDate: outputDate });
      });
      return {
        ...state,
        visioList: visios,
        //new: "",
        isLoading: false,
        total: payload.total,
        pageLimit: payload.per_page,
        isDeleted: false,
        isAdded: false,
        oneVisio: "",
      };
    case CREATE_VISIO_SUCCESS:
      let visiosAdd = state.visioList;
      visiosAdd.unshift(payload);
      return {
        ...state,
        visioList: visiosAdd,
        new: payload,
        isLoading: false,
        isDeleted: false,
        isAdded: true,
      };
    case DELETE_VISIO_SUCCESS:
      return {
        ...state,
        isLoading: false,
        visioList: state?.visioList.filter(
          (element) => element?.id !== payload
        ),
        isDeleted: true,
        isAdded: false,
      };
    case UPDATE_VISIO_SUCCESS:
      return {
        ...state,
      };
    case GET_ONE_VISIO_SUCCESS:
      return {
        ...state,
        isLoading: false,
        isDeleted: false,
        isAdded: false,
        oneVisio: payload,
      };

    case CREATE_VISIO_ERROR:
    case GET_VISIO_ERROR:
    case DELETE_VISIO_ERROR:
    case UPDATE_VISIO_ERROR:
    case GET_ONE_VISIO_ERROR:
      return {
        ...state,
        new: "",
        errors: payload,
        isLoading: false,
        isDeleted: false,
        isAdded: false,
      };
    // case MAKE_VISIO_FLOAT: {
    //   return {
    //     ...state,
    //     isFloat: payload,
    //   };
    // }
    case TOGGLE_VISIO: {
      return {
        ...state,
        isOpen: payload,
        size: "medium",
      };
    }
    case TOGGLE_SIZE_VISIO: {
      return {
        ...state,
        size: payload,
      };
    }
    case SET_VISIO_PARAMS: {
      return {
        ...state,
        visioParams: {
          external:
            payload.external === undefined
              ? state.visioParams.external
              : payload.external,
          moderator:
            payload.moderator === undefined
              ? state.visioParams.moderator
              : payload.moderator,
          join_room:
            payload.token === undefined
              ? state.visioParams.join_room
              : payload.join_room,
          token:
            payload.token === undefined
              ? state.visioParams.token
              : payload.token,
          name:
            payload.name === undefined ? state.visioParams.name : payload.name,
          visio_name:
            payload.visio_name === undefined
              ? state.visioParams.visio_name
              : payload.visio_name,
          owner_id:
            payload.owner_id === undefined
              ? state.visioParams.owner_id
              : payload.owner_id,
          creatorId:
            payload.creatorId === undefined
              ? state.visioParams.creatorId
              : payload.creatorId,
          visioStartDate:
            payload.visioStartDate === undefined
              ? state.visioParams.visioStartDate
              : payload.visioStartDate,
          id_visio:
            payload?.id_visio === undefined
              ? state?.visioParams?.id_visio
              : payload?.id_visio,
          room_status:
            payload?.room_status === undefined
              ? state?.visioParams?.room_status
              : payload?.room_status,
          participants:
            payload?.participants == undefined
              ? state?.visioParams?.participants
              : payload?.participants,
        },
      };
    }

    case RESET_VISIO_STATE: {
      return initialState;
    }
    default:
      return state;
  }
};

export default visio;
