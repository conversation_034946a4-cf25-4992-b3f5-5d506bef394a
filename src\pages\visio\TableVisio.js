import {
  Button,
  Divider,
  Form,
  message,
  Radio,
  Table,
  Tooltip,
  Typography,
} from "antd";
import { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>Eye, FiEyeOff } from "react-icons/fi";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { HiOutlineCalendar } from "react-icons/hi2";

import DropDownCrud from "../../components/DropDownCrud";
import { deleteVisio } from "../../new-redux/actions/visio.actions/deleteVisio";
import { getVisio } from "../../new-redux/actions/visio.actions/getVisio";
import MainService from "../../services/main.service";
import { TableLoader } from "../clients&users/components/SkeletonLoader";
import { getOneVisio } from "../../new-redux/actions/visio.actions/getOneVisio";
import { useWindowSize } from "../clients&users/components/WindowSize";

const TableVisio = ({
  dataTable,
  isLoading,
  total,
  pageLimit,
  setNumberOfPage,
  numberOfPage,
  setPageLimit,
  setTaskToUpdate,
  setVisioToUpdate,
  setOpenDrawer,
  setPlanToTaskCheck,
}) => {
  const windowSize = useWindowSize();

  const staticData = [
    {
      id: 1,
      title: "VisioToTask",
      password: "1234",
      meeting_url: "visio123",
      startDate: "05/05/2023 12:54:33",
      isTask: 1,
      taskId: 172,
    },
    {
      id: 2,
      title: "visiobis",
      password: "5678",
      meeting_url: "visio456",
      startDate: "03/05/2023 12:24:30",
      isTask: 0,
    },
    {
      id: 3,
      title: "VisioTask2",
      password: "9101112",
      meeting_url: "visio789",
      startDate: "05/05/2023 19:46:13",
      isTask: 1,
      taskId: 173,
    },
  ];

  const staticData2 =
    dataTable &&
    dataTable.map((element, i) => ({
      element: element,
      id: element?.id,
      key: element?.id,
      title: element?.title,
      password: element?.password,
      meeting_url: "https://sphere-dev.comunikcrm.info/visio/" + element?.id,
      startDate: element?.startDate,
      isTask: element?.is_task,
      taskId: element?.id_task && element?.id_task,
    }));

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [clickedIndex, setClickedIndex] = useState(null);

  const [src, setSrc] = useState(dataTable);
  const [editingKey, setEditingKey] = useState("");
  const [data, setData] = useState([]);
  const [form] = Form.useForm();

  const isEditing = (record) => record.key === editingKey;

  const dispatch = useDispatch();
  const navigate = useNavigate();

  // const blurPassword = (password) => {
  //     return password.replace(/./g, '*');
  // };
  // const unblurPassword = (password, isHovered) => {
  //     return isHovered ? password : blurPassword(password);
  // };

  // const onRow = (record, index) => {
  //     return {
  //         onMouseEnter: () => {
  //             setHoveredIndex(index);
  //         },
  //         onMouseLeave: () => {
  //             setHoveredIndex(null);
  //         },
  //     };
  // };

  const onPasswordClick = (value) => {
    navigator.clipboard.writeText(value).then(() => {
      message.success(`Secret code copied to clipboard!`);
    });
  };

  const onLinkClick = (value) => {
    navigator.clipboard.writeText(value).then(() => {
      message.success(`Link copied to clipboard!`);
    });
  };

  const togglePasswordVisibility = (currentIndex) => {
    if (clickedIndex === currentIndex) {
      setShowPassword(!showPassword);
    } else {
      setShowPassword(true);
    }
    setClickedIndex(currentIndex);
  };

  const edit = (record) => {
    setVisioToUpdate(record.id);
    setOpenDrawer(true);
    dispatch(getOneVisio(record.id));
    if (record.isTask === true) {
      console.log("visio is a task");
      setTaskToUpdate(record.taskId);
      setPlanToTaskCheck(true);
    } else if (record.isTask === false) {
      console.log("visio is not a task");
      setPlanToTaskCheck(false);
      //setEditingKey(record.key);
    }
  };

  const handleDelete = async (key) => {
    try {
      console.log(key);
      await dispatch(deleteVisio(key));
    } catch (error) {
      console.log("erreur", error);
    }
    // MainService.deleteVisio(key)
  };

  const cancel = (record) => {
    setEditingKey("");

    //setId(null);
    // if (!record.id) {
    //   setData(data.filter((item) => item.key !== record.key));
    // }
  };

  useEffect(() => {
    setData(dataTable);
  }, [dataTable]);

  const columns = [
    {
      title: "Title",
      dataIndex: "element",
      width: "200px",
      render: (record, element) => (
        <div className="flex justify-between">
          <a className="truncate">
            <Typography.Link
              onClick={() => {
                edit(element);
              }}>
              {element.title}
            </Typography.Link>
          </a>
          {/* <div
                        // onClick={handleClick}
                        className="flex justify-start items-center"
                    >
                        <DropDownCrud
                            record={record}
                            edit={edit}
                            handleDelete={handleDelete}
                            form={form}
                            isEditing={isEditing}
                            cancel={cancel}
                            data={data}
                            setData={setData}
                            editingKey={editingKey}
                            api='visio'
                            source='visio'
                        />
                    </div> */}
        </div>
      ),
    },
    {
      //title: 'Actions',
      dataIndex: "actions",
      width: "50px",
      render: (_, record) => {
        return (
          <div
            // onClick={handleClick}
            className="flex items-center justify-start">
            <DropDownCrud
              record={record}
              edit={edit}
              handleDelete={handleDelete}
              form={form}
              isEditing={isEditing}
              cancel={cancel}
              data={data}
              setData={setData}
              editingKey={editingKey}
              api="visio"
              source="visio"
            />
          </div>
          // )
        );
      },
    },
    {
      //isTask
      dataIndex: "isTask",
      width: "35px",
      render: (text) => (
        <a onClick={() => navigate(`/tasks`)}>
          {text === true ? <HiOutlineCalendar /> : ""}
        </a>
      ),
    },
    {
      title: "Secret Code",
      dataIndex: "password",
      width: "150px",
      render: (value, record, index) => (
        <div className="flex justify-between">
          {showPassword && clickedIndex === index
            ? value
            : "*".repeat(value.length)}
          <div>
            {showPassword && clickedIndex === index ? (
              <FiEyeOff
                onClick={() => togglePasswordVisibility(index)}
                style={{ cursor: "pointer", marginRight: "5px" }}
                className="h-3 w-3"
              />
            ) : (
              <FiEye
                onClick={() => togglePasswordVisibility(index)}
                style={{ cursor: "pointer", marginRight: "5px" }}
                className="h-3 w-3"
              />
            )}
            <FiCopy
              onClick={() => onPasswordClick(value)}
              style={{ cursor: "pointer" }}
              className="h-3 w-3"
            />
          </div>
        </div>
      ),
    },
    {
      title: "Link",
      dataIndex: "meeting_url",
      ellipsis: true,
      width: 600,
      render: (value, index) => (
        <div className="flex justify-between">
          <a target="_blank" href={value} className="truncate">
            {value}
          </a>
          <div>
            <FiCopy
              onClick={() => onLinkClick(value)}
              style={{ cursor: "pointer" }}
              className="h-3 w-3"
            />
          </div>
        </div>
      ),
    },
    {
      title: "Date",
      dataIndex: "startDate",
      width: 200,
      render: (startDate) => <div className="truncate">{startDate}</div>,
    },
  ];

  const start = () => {
    setLoading(true);
    setTimeout(() => {
      setSelectedRowKeys([]);
      setLoading(false);
    }, 1000);
  };
  const onSelectChange = (newSelectedRowKeys) => {
    console.log("selectedRowKeys changed: ", newSelectedRowKeys);
    setSelectedRowKeys(newSelectedRowKeys);
  };
  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  const hasSelected = selectedRowKeys.length > 0;

  // const listVisio = useSelector((state) => state.visio.visioList);

  // const newVisio = useSelector((state) => state.visio.new)

  const isDeleted = useSelector((state) => state.visio.isDeleted);
  const isAdded = useSelector((state) => state.visio.isAdded);

  // useEffect(() => {
  //     newVisio && dataTable.push(newVisio)
  // }, [newVisio, dataTable])

  useEffect(() => {
    isDeleted === true && dispatch(getVisio(numberOfPage));
  }, [isDeleted, dispatch, numberOfPage]);

  useEffect(() => {
    isAdded === true && dispatch(getVisio(numberOfPage));
  }, [isAdded, dispatch, numberOfPage]);

  return (
    <div>
      <div
        style={{
          marginBottom: 16,
        }}>
        {/* <Button type="primary" onClick={start} disabled={!hasSelected} loading={loading}>
                    Reload
                </Button> */}
        <span
          style={{
            marginLeft: 8,
          }}>
          {hasSelected ? `Selected ${selectedRowKeys.length} items` : ""}
        </span>
      </div>

      {/* <Table
                rowSelection={rowSelection}
                columns={columns}
                dataSource={dataTable}
            /> */}

      {columns?.length ? (
        <Table
          columns={columns}
          //dataSource={dataTable}
          dataSource={staticData}
          //rowSelection={rowSelection}
          //onRow={onRow}
          size={"small"}
          loading={isLoading}
          sticky={true}
          scroll={{
            // x: showColumns?.totalShowColumns * 220,
            y: windowSize?.height - 340,
          }}
          // pagination={{
          //   showTotal: (total, range) =>
          //     `${range[0]}-${range[1]} of ${total} items`,
          //   showSizeChanger: true,
          //   showQuickJumper: true,
          //   total: total,
          //   pageSize: pageLimit,
          //   onChange: (page) => setNumberOfPage(page),
          //   onShowSizeChange: (current, size) => {
          //     setPageLimit(size);
          //   },
          //   pageSizeOptions: [10],
          //   size: "medium",
          // }}
        />
      ) : (
        <TableLoader colNum={6} rowNum={5} />
      )}
    </div>
  );
};
export default TableVisio;
