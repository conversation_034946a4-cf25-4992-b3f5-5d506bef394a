import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Tooltip } from "antd";
import { HiOutlineBuildingOffice } from "react-icons/hi2";
import {
  CloseOutlined,
  LoadingOutlined,
  TeamOutlined,
} from "@ant-design/icons";
import { LuPalmtree } from "react-icons/lu";
import { useEffect, useMemo, useState } from "react";
import { toastNotification } from "../../../components/ToastNotification";
import MainService from "../../../services/main.service";
import IdentifContent from "./identifContent";
import FormCreate from "pages/clients&users/components/FormCreate";
import { useSelector } from "react-redux";
import { CgUserlane } from "react-icons/cg";
import { setRefreshMailInbox } from "new-redux/actions/mail.actions";
import { useDispatch } from "react-redux";
import { RiBallPenLine } from "react-icons/ri";
import { AiOutlineShoppingCart } from "react-icons/ai";
import { CiUser } from "react-icons/ci";
import { Blocks, HeartHandshake } from "lucide-react";
import TicketIconSphere from "components/icons/TicketIconSphere";
//

//
const Identification = ({
  id,
  setData,
  t,
  user,
  access,
  fromEmail,
  idEmail,
  identification,
  dataMailInbox,
  fromName,
  transfert,
  owner,
  type,
  setDetailsMail,
  getDetailsMessageInbox,
  idThread,
  openAction,
  getDetailsThreadsCollapse,
  pageDetailsThread,
}) => {
  const [loadingDelete, setLoadingDelete] = useState(false);
  const [openPopCon, setOpenPopCon] = useState(false);
  const [openSelect2, setOpenSelect2] = useState(false);
  const [idModule, setIdModule] = useState(null);
  const [mailingProps, setMailingProps] = useState({
    label: "",
    email: "",
    idEmail: "",
    id: "",
  });
  const [familyId, setFamilyId] = useState(null);
  const [openForm, setOpenForm] = useState(false);

  const { dataAccounts } = useSelector((state) => state.mailReducer);
  const dispatch = useDispatch();
  const usedAccount = useMemo(
    () => dataAccounts?.find((item) => item.selected === true),
    [dataAccounts]
  );

  const familiesName = {
    1: t("contacts.company"),
    2: t("contacts.contact"),
    4: t("contacts.user"),
    9: t("contacts.leads"),
  };
  const families = {
    1: {
      label: t("contacts.company"),
      icon: <HiOutlineBuildingOffice style={{ fontSize: 16 }} />,
    },
    2: {
      label: t("contacts.contact"),
      icon: <TeamOutlined style={{ fontSize: 15 }} />,
    },
    3: {
      label: "Deals",
      icon: <HeartHandshake size={18} style={{ marginTop: 4 }} />,
    },
    4: {
      label: t("contacts.user"),
      icon: <CiUser style={{ fontSize: 17, marginTop: 3 }} />,
    },
    5: {
      label: t("contacts.product"),
      icon: <AiOutlineShoppingCart style={{ fontSize: 16, marginTop: 2 }} />,
    },
    6: {
      label: t("contacts.ticket"),
      icon: <TicketIconSphere size={19} style={{ marginTop: 2 }} />,
    },
    7: {
      label: t("contacts.project"),
      icon: <Blocks size={17} />,
    },
    8: {
      label: t("contacts.booking"),
      icon: <LuPalmtree style={{ fontSize: 16 }} />,
    },
    9: {
      label: t("contacts.leads"),
      icon: <CgUserlane style={{ fontSize: 15, marginTop: 3 }} />,
    },
  };

  const IdentifContents = (
    <div className="flex items-center justify-center">
      <IdentifContent
        openSelect2={openSelect2}
        setOpenSelect2={setOpenSelect2}
        elementId={id}
        source={"email"}
        setData={setData}
        t={t}
        access={access}
        user={user}
        fromEmail={fromEmail}
        dataMailInbox={dataMailInbox}
        idEmail={idEmail}
        setOpenPopCon={setOpenPopCon}
        idModule={idModule}
        setIdModule={setIdModule}
        type={type}
        setDetailsMail={setDetailsMail}
        getDetailsMessageInbox={getDetailsMessageInbox}
        getDetailsThreadsCollapse={getDetailsThreadsCollapse}
        idThread={idThread}
        pageDetailsThread={pageDetailsThread}
        identification={identification}
        // setOpenSelect={setOpenSelect}
      />
    </div>
  );

  const handlePopoverToggle = () => {
    setOpenPopCon(!openPopCon); // Toggle the visibility
  };

  const handleDelete = async () => {
    var formData = new FormData();

    formData.append("identificationId", identification?.identificationId);
    formData.append("emailId", idEmail);
    formData.append("type", "email");
    formData.append("account_id", usedAccount?.value);
    for (let i = 0; i < usedAccount?.departmentId?.length; i++) {
      formData.append("departement_id[]", usedAccount?.departmentId[i]);
    }
    try {
      setLoadingDelete(true);
      const { status } = await MainService.deleteIdentification(formData);
      if (status === 200) {
        if (type === "inbox") {
          setData((prev) =>
            prev.map((item) => {
              return item?.id === id ? { ...item, identification: null } : item;
            })
          );
        } else {
          // setDetailsMail((p) => {
          //   let detail = Object.assign({}, p);
          //   detail.data[detail?.data?.length - 1].identification = [];
          //   return detail;
          // });
          getDetailsMessageInbox();
          getDetailsThreadsCollapse(5 * pageDetailsThread, false, 1);
        }

        toastNotification(
          "success",
          t("mailing.identifDeletedSuccess"),
          "topRight",
          3
        );
        dispatch(setRefreshMailInbox(true));
      }
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? err.message : err);
    } finally {
      setLoadingDelete(false);
    }
  };

  const shouldDisableIdenfitication =
    access.companies !== "1" && access.contact !== "1" && access.leads !== "1";

  const disabledPopverIdentif =
    !(
      transfert?.account_id &&
      transfert?.account_id !== String(usedAccount?.value)
    ) && !(owner?.owner && owner?.owner != user.id);

  const OpenDrawer = () => {
    if (idModule === 1) {
      setMailingProps({
        label: fromName?.length > 0 ? fromName : fromEmail,
        email: fromEmail,
        idEmail: idEmail,
        source: "email",
      });
      setFamilyId(1);
      setOpenForm(true);
    } else if (idModule === 2) {
      setMailingProps({
        label: fromName?.length > 0 ? fromName : fromEmail,
        email: fromEmail,
        idEmail: idEmail,
        source: "email",
      });

      setFamilyId(2);
      setOpenForm(true);
    } else if (idModule === 9) {
      setMailingProps({
        label: fromName?.length > 0 ? fromName : fromEmail,
        email: fromEmail,
        idEmail: idEmail,
        source: "email",
      });

      setFamilyId(9);
      setOpenForm(true);
    }
    setOpenPopCon(false);
  };

  const identificationTitle = (
    <div className="">
      <div className="flex flex-row items-center  justify-between">
        <span>{t("mailing.identifier")}</span>
        {idModule ? <p>{t("voip.or")}</p> : null}
        {idModule ? (
          <div>
            <Button
              size="small"
              type="primary"
              onClick={() => {
                OpenDrawer();
              }}
            >
              Create{" "}
              {idModule === 1 ? "Company" : idModule === 2 ? "Contact" : "Lead"}
            </Button>
          </div>
        ) : null}
      </div>
      <Divider className="my-2" />
    </div>
  );

  const handleOpenChange = (open) => {
    if (disabledPopverIdentif) {
      if (!open) {
        setOpenSelect2(false);
      }
      setOpenPopCon(open);
    }
  };

  useEffect(() => {
    if (openAction) {
      setOpenPopCon(openAction);
    }
  }, [openAction]);

  //
  // return identification?.label_data && type === "inbox" ? (
  return identification?.label_data ? (
    type === "dropdown" ? (
      t("mailing.identify")
    ) : (
      <Popover
        content={IdentifContents}
        title={identificationTitle}
        open={openPopCon}
        onOpenChange={handleOpenChange}
        trigger={["click"]}
        placement="bottomLeft"
        arrow={false}
      >
        <Tag
          style={{
            maxWidth: type === "details" ? "140px" : "100%",
            position: "relative",
          }}
        >
          <>
            <div className="flex flex-row items-center space-x-1.5">
              <span className="cursor-help">
                <Tooltip title={familiesName[identification?.family_id]}>
                  {families[identification?.family_id]?.icon}
                </Tooltip>
              </span>
              <Tooltip title={`Identifié à ${identification?.label_data}`}>
                <p className="truncate text-xs font-semibold">
                  {`${identification?.label_data}`}
                </p>
              </Tooltip>
              {loadingDelete ? (
                <Button type="link" size="small" icon={<LoadingOutlined />} />
              ) : !(
                  transfert?.account_id &&
                  transfert?.account_id !== String(usedAccount?.value)
                ) && !(owner?.owner && owner?.owner != user.id) ? (
                <Tooltip title={t("voip.delete")}>
                  <Button
                    type="text"
                    size="small"
                    icon={<CloseOutlined />}
                    onClick={handleDelete}
                  />
                </Tooltip>
              ) : null}
            </div>
          </>
        </Tag>
      </Popover>
    )
  ) : identification?.label_data && type === "details" ? (
    <Tooltip title={`cet email est identifié à ${identification?.label_data}`}>
      <RiBallPenLine
        style={{
          fontSize: "22px",
          color: "#6d6d6d",
          cursor: "default",
        }}
        onClick={(e) => e.stopPropagation()}
      />
    </Tooltip>
  ) : (transfert?.account_id && transfert?.account_id != usedAccount?.value) ||
    (owner?.owner && owner?.owner != user.id) ? (
    <Tooltip
      title={
        transfert?.account_id &&
        transfert?.account_id !== String(usedAccount?.value)
          ? t("mailing.Tooltip.TransferedNoIdentif")
          : owner?.owner && owner?.owner != user.id
          ? t("mailing.Tooltip.AssignNoIdentif")
          : transfert?.account_id && owner?.owner
          ? t("mailing.Tooltip.TransferedAndAssigned")
          : `Affecter cet email à un élément de module`
      }
    >
      {type === "dropdown" ? (
        t("mailing.identify")
      ) : (
        <Button size="small" type="dashed" disabled={true}>
          Identifier l'email
        </Button>
      )}
    </Tooltip>
  ) : (
    <>
      <Popover
        content={IdentifContents}
        title={identificationTitle}
        open={openPopCon}
        onOpenChange={(open) => {
          if (!open) {
            setOpenSelect2(false);
          }
          setOpenPopCon(open);
        }}
        trigger={["click"]}
        placement="bottomLeft"
        arrow={false}
      >
        <span onClick={handlePopoverToggle}>
          {type === "dropdown" ? (
            t("mailing.identify")
          ) : (
            <Tooltip title={t("mailing.identifyEmail")}>
              <Button
                size="small"
                type="dashed"
                disabled={shouldDisableIdenfitication}
              >
                {t("mailing.identify")}
              </Button>
            </Tooltip>
          )}
        </span>
      </Popover>

      <FormCreate
        open={openForm}
        setOpen={setOpenForm}
        familyId={familyId}
        mailingProps={mailingProps}
      />
    </>
  );
};

export default Identification;
