import React from "react";

const LiveChatIcon = ({ 
  primaryColor = "#94a3b8",  // Default blue (original color)
  secondaryColor = "#FFFFFF", // Default white (for lines)
  size = 18,
  ...props 
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 600 600"
    style={{ enableBackground: "new 0 0 600 600" }}
    {...props}
  >
    <g id="XMLID_1849_">
      <path
        id="XMLID_1854_"
        fill={primaryColor}
        d="M300,0C134.3,0,0,134.3,0,300c0,63,19.4,121.5,52.6,169.8L5.2,600H300c165.7,0,300-134.3,300-300C600,134.3,465.7,0,300,0z"
      />
      <g id="XMLID_1850_">
        <path
          id="XMLID_1853_"
          fill={secondaryColor}
          d="M141.5,197.2c4-10.9,14.3-18.2,26-18.2h245.7l-8,22.1c-4,10.9-14.3,18.2-26,18.2H133.4L141.5,197.2z"
        />
        <path
          id="XMLID_1852_"
          fill={secondaryColor}
          d="M405.1,383.5c-4,10.9-14.3,18.2-26,18.2H133.4l8-22.1c4-10.9,14.3-18.2,26-18.2h245.7L405.1,383.5z"
        />
        <path
          id="XMLID_1851_"
          fill={secondaryColor}
          d="M509.2,292.3c-4,10.9-14.3,18.2-26,18.2H133.4l8-22.1c4-10.9,14.3-18.2,26-18.2h349.8L509.2,292.3z"
        />
      </g>
    </g>
  </svg>
);

export default LiveChatIcon;