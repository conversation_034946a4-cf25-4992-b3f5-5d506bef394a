import { Skeleton } from "antd";
import React from "react";

const SkeletonThreecards = ({ loading }) => {
  return (
    <div className="flex w-full justify-between gap-4">
      <div className="flex w-full items-center space-x-3">
        <div className="w-full">
          <Skeleton.Button active={loading} block={loading} size="small" />

          <br />
          <br />

          <Skeleton.Button active={loading} block={loading} size="small" />
        </div>
        <div>
          <Skeleton.Avatar
            active={loading}
            block={loading}
            shape={"button"}
            size="default"
            style={{ height: "48px", width: "48px" }}
          />
        </div>
      </div>
      <div className="flex w-full items-center space-x-3">
        <div className="w-full">
          <Skeleton.Button active={loading} block={loading} size="small" />

          <br />
          <br />

          <Skeleton.Button active={loading} block={loading} size="small" />
        </div>
        <div>
          <Skeleton.Avatar
            active={loading}
            block={loading}
            shape={"button"}
            size="default"
            style={{ height: "48px", width: "48px" }}
          />
        </div>
      </div>
      <div className="flex w-full items-center space-x-3">
        <div className="w-full">
          <Skeleton.Button active={loading} block={loading} size="small" />

          <br />
          <br />

          <Skeleton.Button active={loading} block={loading} size="small" />
        </div>
        <div>
          <Skeleton.Avatar
            active={loading}
            block={loading}
            shape={"button"}
            size="default"
            style={{ height: "48px", width: "48px" }}
          />
        </div>
      </div>
    </div>
  );
};

export default SkeletonThreecards;
