import { useCallback } from "react";
import { useTranslation } from "react-i18next";
import { useQuery } from "@tanstack/react-query";
import { useLocation } from "react-router-dom";

import { STALE_TIME_CACHE } from "..";
import MainService from "services/main.service";
import { toastNotification } from "components/ToastNotification";
import { useSelector } from "react-redux";

function useInfoRoomInModules(
  elementId,
  canCreateRoom,
  roomName,
  source = "",
  formData // mailing
) {
  const location = useLocation();
  const { t } = useTranslation("common");
  const { activeActivity360, activeTab360 } = useSelector(
    (state) => state?.vue360
  );
  const { contactHeaderInfo } = useSelector((state) => state?.contacts);
  const { openView360InDrawer } = useSelector((state) => state?.vue360);
  const pathnameIncludes = (paths) =>
    paths.some((path) => location?.pathname?.includes(path));

  const handleSource = () => {
    const result =
      (pathnameIncludes(["tasks", "visio", "dashboard"]) ||
        activeTab360 === 3) &&
      (pathnameIncludes(["notes"])
        ? Object.keys(contactHeaderInfo)?.length === 0 ||
          activeActivity360 === "1"
        : true)
        ? "tasks"
        : openView360InDrawer || contactHeaderInfo?.id
        ? "tasks"
        : "family";
    return result;
  };

  const getInfoDiscussion = useCallback(
    (signal) => {
      const promise = new Promise(async (resolve, reject) => {
        try {
          if (!elementId) return reject(new Error("no_id"));
          if (canCreateRoom === 0) return;
          // const response = await MainService.createRoomTask(
          //   handleSource(),
          //   elementId
          // );
          let response;
          // if (handleSource() !== "notes") {
          if (pathnameIncludes(["mailing"])) {
            response = await MainService.createRoomMailing(formData);
          } else if (source !== "notes") {
            response = await MainService.createRoomTask(
              source || handleSource(),
              elementId
            );
          } else {
            response = await MainService.createRoomNote(elementId, roomName);
          }
          return resolve(response?.data);
        } catch (error) {
          console.log(error.response.status);
          if (error.name === "CanceledError") return;
          if (error.response.status !== 422) {
            toastNotification(
              "error",
              t("toasts.errorFetchApi") + "",
              "topRight"
            );
          }
          reject(error);
        }
      });
      return promise;
    },
    [elementId, t, canCreateRoom, source]
  );

  const { data, isFetched, status, fetchStatus } = useQuery({
    queryFn: async ({ signal }) => await getInfoDiscussion(signal),
    retry: 0,
    queryKey: ["INFO_CHAT_MODULE", elementId],
    enabled: elementId && canCreateRoom === 1 ? true : false,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    staleTime: STALE_TIME_CACHE,
    cacheTime: 0,
  });
  return { data, isFetched, status, fetchStatus };
}

export default useInfoRoomInModules;
