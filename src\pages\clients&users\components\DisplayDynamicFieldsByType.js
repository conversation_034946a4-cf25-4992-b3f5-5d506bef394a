import {
  Checkbox,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Radio,
  Select,
  Space,
  TimePicker,
  Badge,
  Rate,
} from "antd";
import { FiMail } from "react-icons/fi";
import { useTranslation } from "react-i18next";
import { colors } from "../../../components/Colors";
import {
  CurrencyField,
  PhoneFields,
  FileField,
  ImgField,
  FieldAutoComplete,
} from "./special_fields";
import dayjs from "dayjs";
import { QuestionCircleOutlined } from "@ant-design/icons";
import emojiRegex from "emoji-regex";
import AlbumField from "./special_fields/AlbumField";
import { URL_ENV } from "index";
import DisplayAvatar from "pages/voip/components/DisplayAvatar";
import { renderIcon } from "./RenderColumnsTable";
import ChoiceIcons from "pages/components/ChoiceIcons";

// import { useLocation } from "react-router-dom";

//
const DisplayDynamicFieldsByType = ({

  form,
  config,
  fieldType,
  fieldId,
  label,
  value,
  familyId,
  fieldModule,
  isModule,
  options,
  required,
  description,
  placeholder,
  setSubmitIsDisable,
  reset,
  readOnly,
  isMultiple,
  family_id,
  isViewOnly=false,
}) => {
  //
  // familyId && console.log(fieldId, label);
  //
  const [t] = useTranslation("common");
  // const location = useLocation();
  //
  const dateFormat = config?.date_format;
  const timeFormat = config?.time_format;
  //
  const regexEmoji = emojiRegex();
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  const numberRegex = /^\d+$/;
  const urlRegex = /^(https?|chrome):\/\/[^\s$.?#].[^\s]*$/;
  const passwordRegex = /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[a-zA-Z]).{10,}$/;
  //
  // const [passwordId, setPasswordId] = useState(undefined);

  /// this useEffect to catch the field id of field password
  // useEffect(() => {
  //   fieldType === "password" && setPasswordId(fieldId);
  // }, [fieldId, fieldType]);
  //
  // const handleGeneratePassword = () => {
  //   setSubmitIsDisable(false);
  //   const newPassword = generatePassword(10);
  //   form.setFieldsValue({
  //     [passwordId]: newPassword,
  //     confirmPassword: newPassword,
  //   });
  // };
  // const getBase64 = (file) =>
  //   new Promise((resolve, reject) => {
  //     const reader = new FileReader();
  //     reader.readAsDataURL(file);
  //     reader.onload = () => resolve(reader.result);
  //     reader.onerror = (error) => reject(error);
  //   });
  //
  const TooltipDescription = (description) => {
    if (description) {
      return {
        title: description,
        icon: (
          <QuestionCircleOutlined
            style={{ color: "#1d4ed8", fontSize: "15px" }}
          />
        ),
      };
    }
    return null;
  };
  //
  const baseUrlAvatar =
    URL_ENV?.REACT_APP_BASE_URL + URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL;
  //
  const menuOptions = familyId
    ? config?.list_values_family?.[familyId]
    : isModule
    ? config?.module_values?.[isModule]?.filter(
        (item) =>
          (item?.status === 1 || !item?.status) &&
          (item?.hidden === 0 || !item?.hidden)
      )
    : options;

  const RenderOptions = ({ option }) => {
    //
    const isAvatar = !!familyId && (
      <DisplayAvatar
        name={option?.label}
        size={20}
        icon={renderIcon(familyId, 14)}
        urlImg={option?.avatar && `${baseUrlAvatar}${option?.avatar}`}
      />
    );
    //
    // const isModule = !!isModule  &&
    //
    return (
      <div
        style={{ color: option?.color ?? "" }}
        className="flex items-center space-x-1"
      >
        {isAvatar ? (
          isAvatar
        ) : option?.icon ? (
          <ChoiceIcons
            icon={option?.icon}
            fontSize={14}
            height={null}
            top={0}
          />
        ) : null}
        <span>{option?.label}</span>
      </div>
    );
  };

  switch (fieldType) {
    case "radio":
      return (
        <Form.Item
          key={fieldId}
          label={label}
          tooltip={TooltipDescription(description)}
          name={fieldId}
          initialValue={value || undefined}
          rules={[
            {
              required: required,
              message: t("contacts.fieldXRequired", { x: label }),
            },
          ]}
        >
          <Radio.Group>
            <Space direction="vertical">
              {menuOptions?.map(
                (option) =>
                  option?.label &&
                  option?.id && (
                    <Radio
                      disabled={readOnly && true}
                      key={option?.id}
                      value={option?.id}
                    >
                      <RenderOptions option={option} />
                    </Radio>
                  )
              )}
            </Space>
          </Radio.Group>
        </Form.Item>
      );
    ////////////////////////////////////////////////////////////////
    case "text":
    case "ip address":
      return (
        <Form.Item
          key={fieldId}
          label={label}
          tooltip={TooltipDescription(description)}
          name={fieldId}
          initialValue={value || undefined}
          rules={[
            {
              required: required,
              message: t("contacts.fieldXRequired", { x: label }),
            },
            {
              validator: (_, value) => {
                if (value && regexEmoji?.test(value)) {
                  return Promise.reject(
                    new Error(t("contacts.noEmojisAllowed"))
                  );
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <Input
            placeholder={
              family_id === 6 && readOnly
                ? t("contacts.fieldFilledAuto")
                : placeholder
            }
            readOnly={readOnly && true}
          />
        </Form.Item>
      );
    ////////////////////////////////////////////////////////////////
    case "checkbox":
      return (
        <Form.Item
          key={fieldId}
          label={label}
          tooltip={TooltipDescription(description)}
          name={fieldId}
          initialValue={value || undefined}
          rules={[
            {
              required: required,
              message: t("contacts.fieldXRequired", { x: label }),
            },
          ]}
        >
          <Checkbox.Group disabled={readOnly && true}>
            <Space direction="vertical">
              {menuOptions?.map(
                (option) =>
                  option?.label &&
                  option?.id && (
                    <Checkbox key={option?.id} value={option?.id}>
                      <RenderOptions option={option} />
                    </Checkbox>
                  )
              )}
            </Space>
          </Checkbox.Group>
        </Form.Item>
      );
    ////////////////////////////////////////////////////////////////
    case "select":
      // const valueExists = menuOptions?.some((option) => option.id === value);
      // !valueExists && form.setFieldValue(fieldId, undefined);
      // console.log({ value, valueExists });
      return (
        <Form.Item
          key={fieldId}
          label={label}
          tooltip={TooltipDescription(description)}
          name={fieldId}
          initialValue={value || undefined}
          rules={[
            {
              required: required,
              message: t("contacts.fieldXRequired", { x: label }),
            },
          ]}
        >
          <Select
            disabled={readOnly && true}
            style={{ width: "100%" }}
            showSearch
            allowClear
            optionFilterProp="children"
            filterOption={(input, option) =>
              option?.label?.toLowerCase()?.includes(input?.toLowerCase())
            }
            placeholder={placeholder}
          >
            {menuOptions?.map(
              (option) =>
                option?.label &&
                option?.id && (
                  <Select.Option
                    key={option?.id}
                    value={option?.id}
                    label={option?.label}
                  >
                    <RenderOptions option={option} />
                  </Select.Option>
                )
            )}
          </Select>
        </Form.Item>
      );
    ////////////////////////////////////////////////////////////////
    case "multiselect":
      // const validValues = Array.isArray(value)
      //   ? value.filter((val) =>
      //       menuOptions?.some((option) => option.id === val)
      //     )
      //   : [];
      // !validValues.length && form.setFieldValue(fieldId, undefined);
      return (
        <Form.Item
          key={fieldId}
          label={label}
          tooltip={TooltipDescription(description)}
          name={fieldId}
          initialValue={value || undefined}
          rules={[
            {
              required: required,
              message: t("contacts.fieldXRequired", { x: label }),
            },
          ]}
        >
          <Select
            disabled={readOnly && true}
            mode="multiple"
            style={{ width: "100%" }}
            showSearch
            allowClear
            optionFilterProp="children"
            maxTagCount="responsive"
            filterOption={(input, option) =>
              option.label.toLowerCase().includes(input.toLowerCase())
            }
            placeholder={placeholder}
          >
            {menuOptions?.map(
              (option) =>
                option?.label &&
                option?.id && (
                  <Select.Option
                    key={option?.id}
                    value={option?.id}
                    label={option?.label}
                  >
                    <RenderOptions option={option} />
                  </Select.Option>
                )
            )}
          </Select>
        </Form.Item>
      );
    ////////////////////////////////////////////////////////////////
    case "rate":
      return (
        <Form.Item
          key={fieldId}
          label={label}
          tooltip={TooltipDescription(description)}
          name={fieldId}
          initialValue={Number(value) || undefined}
          rules={[
            {
              required: required,
              message: t("contacts.fieldXRequired", { x: label }),
            },
          ]}
        >
          <Rate allowHalf disabled={readOnly && true} />
        </Form.Item>
      );
    ////////////////////////////////////////////////////////////////
    case "email":
      return (
        <Form.Item
          key={fieldId}
          label={label}
          tooltip={TooltipDescription(description)}
          name={fieldId}
          initialValue={value || undefined}
          rules={[
            {
              required: required,
              message: t("contacts.fieldXRequired", { x: label }),
            },
            {
              type: "email",
              message: t("contacts.enterValidEmail"),
            },
          ]}
        >
          <Input
            prefix={<FiMail className="mr-1 h-5 w-5" />}
            autoComplete="off"
            placeholder={placeholder}
            readOnly={readOnly && true}
          />
        </Form.Item>
      );
    ////////////////////////////////////////////////////////////////
    case "date_time":
      return (
        <Form.Item
          key={fieldId}
          label={label}
          tooltip={TooltipDescription(description)}
          name={fieldId}
          initialValue={
            value ? dayjs(value, `${dateFormat} ${timeFormat}`) : undefined
          }
          rules={[
            {
              required: required,
              message: t("contacts.fieldXRequired", { x: label }),
            },
          ]}
        >
          <DatePicker
            disabled={readOnly && true}
            allowClear
            use12Hours={timeFormat === "h:mm a"}
            format={`${dateFormat} ${timeFormat}`}
            showTime
          />
        </Form.Item>
      );
    ////////////////////////////////////////////////////////////////
    case "number":
      return (
        <Form.Item
          key={fieldId}
          label={label}
          tooltip={TooltipDescription(description)}
          name={fieldId}
          initialValue={value || undefined}
          rules={[
            {
              required: required,
              message: t("contacts.fieldXRequired", { x: label }),
            },
            {
              pattern: numberRegex,
              message: t("contacts.onlyNum"),
            },
          ]}
        >
          <InputNumber
            disabled={readOnly && true}
            style={{
              width: "100%",
            }}
          />
        </Form.Item>
      );
    ////////////////////////////////////////////////////////////////
    case "time":
      return (
        <Form.Item
          key={fieldId}
          label={label}
          tooltip={TooltipDescription(description)}
          name={fieldId}
          initialValue={value ? dayjs(value, `${timeFormat}`) : undefined}
          rules={[
            {
              required: required,
              message: t("contacts.fieldXRequired", { x: label }),
            },
          ]}
        >
          <TimePicker
            allowClear
            use12Hours={timeFormat === "h:mm a"}
            format={timeFormat}
            disabled={readOnly && true}
          />
        </Form.Item>
      );
    ////////////////////////////////////////////////////////////////
    case "date":
      // console.log({ value, dateFormat });
      return (
        <Form.Item
          key={fieldId}
          label={label}
          tooltip={TooltipDescription(description)}
          name={fieldId}
          initialValue={value ? dayjs(value, `${dateFormat}`) : undefined}
          rules={[
            {
              required: required,
              message: t("contacts.fieldXRequired", { x: label }),
            },
          ]}
        >
          <DatePicker
            allowClear
            format={dateFormat}
            disabled={readOnly && true}
          />
        </Form.Item>
      );
    ////////////////////////////////////////////////////////////////
    case "textarea":
      return (
        <Form.Item
          key={fieldId}
          label={label}
          tooltip={TooltipDescription(description)}
          name={fieldId}
          initialValue={value || undefined}
          rules={[
            {
              required: required,
              message: t("contacts.fieldXRequired", { x: label }),
            },
          ]}
        >
          <Input.TextArea
            readOnly={readOnly && true}
            autoComplete="off"
            showCount
            rows={4}
            maxLength={1000}
            style={{
              width: "100%",
              marginBottom: "1rem",
            }}
            placeholder={placeholder}
          />
        </Form.Item>
      );
    ////////////////////////////////////////////////////////////////
    case "link":
      return (
        <Form.Item
          key={fieldId}
          label={label}
          tooltip={TooltipDescription(description)}
          name={fieldId}
          initialValue={value || undefined}
          rules={[
            {
              required: required,
              message: t("contacts.fieldXRequired", { x: label }),
            },
            {
              pattern: urlRegex,
              message: t("contacts.validUrl"),
            },
          ]}
        >
          <Input
            readOnly={readOnly && true}
            // autoComplete="off"
            // addonBefore="https://"
            placeholder={placeholder || "https://www.exemple.com"}
          />
        </Form.Item>
      );
    ////////////////////////////////////////////////////////////////
    case "range":
      return (
        <Form.Item
          key={fieldId}
          label={label}
          tooltip={TooltipDescription(description)}
          name={fieldId}
          initialValue={
            value
              ? [
                  dayjs(value[0], `${dateFormat}`),
                  dayjs(value[1], `${dateFormat}`),
                ]
              : undefined
          }
          rules={[
            {
              required: required,
              message: t("contacts.fieldXRequired", { x: label }),
            },
          ]}
        >
          <DatePicker.RangePicker
            allowClear
            format={dateFormat}
            disabled={readOnly && true}
          />
        </Form.Item>
      );
    // /////////////////////////////////////////////////////////////
    case "color":
      return (
        <Form.Item
          key={fieldId}
          label={label}
          tooltip={TooltipDescription(description)}
          name={fieldId}
          initialValue={value || undefined}
          rules={[
            {
              required: required,
              message: t("contacts.fieldXRequired", { x: label }),
            },
          ]}
        >
          <Select
            disabled={readOnly && true}
            style={{
              width: "100%",
            }}
            allowClear
            placeholder={placeholder}
            // disabled={checkUniqueValues(uniqueValue, action, value)}
            optionFilterProp="children"
            options={colors?.map((color, i) => ({
              label: (
                <Space key={i}>
                  <Badge color={color.value} /> {t(`colors.${color.label}`)}
                </Space>
              ),
              value: color.value,
            }))}
          ></Select>
        </Form.Item>
      );
    // /////////////////////////////////////////////////////////////
    case "country":
      ////
      const _options =
        config?.countries?.map((country) => ({
          value: country.id,
          label_en: country.name_en,
          label_fr: country.name_fr,
          toDisplay: (
            <Space key={country?.id}>
              <span>{country?.flag}</span>
              <span>
                {localStorage.getItem("language") === "fr"
                  ? country.name_fr
                  : country.name_en}
              </span>
            </Space>
          ),
        })) ?? [];
      ////
      return (
        <Form.Item
          key={fieldId}
          label={label}
          tooltip={TooltipDescription(description)}
          name={fieldId}
          initialValue={Number(value) || undefined}
          rules={[
            {
              required: required,
              message: t("contacts.fieldXRequired", { x: label }),
            },
          ]}
        >
          <Select
            disabled={readOnly && true}
            mode={isMultiple && "multiple"}
            // disabled={checkUniqueValues(uniqueValue, action, value)}
            maxTagCount="responsive"
            style={{ width: "100%" }}
            allowClear
            showSearch
            optionFilterProp="label"
            filterOption={(input, option) =>
              option?.label_en.toLowerCase().includes(input.toLowerCase()) ||
              option?.label_fr.toLowerCase().includes(input.toLowerCase())
            }
            placeholder={placeholder}
          >
            {_options.map((option, i) => (
              <Select.Option
                key={i}
                value={option?.value}
                label_en={option?.label_en}
                label_fr={option?.label_fr}
              >
                {option?.toDisplay}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      );
    ////////////////////////////////////////////////////////////////
    case "image":
      return (
        <ImgField
          fieldId={fieldId}
          label={label}
          TooltipDescription={TooltipDescription}
          description={description}
          required={required}
          readOnly={readOnly}
          // getBase64={getBase64}
          value={value}
          reset={reset}
          isView={isViewOnly}
        />
      );
    ////////////////////////////////////////////////////////////////
    case "album":
      return (
        <AlbumField
          fieldId={fieldId}
          label={label}
          TooltipDescription={TooltipDescription}
          description={description}
          required={required}
          readOnly={readOnly}
          form={form}
          value={value}
        />
      );
    ////////////////////////////////////////////////////////////////
    case "file":
      return (
        <FileField
          fieldId={fieldId}
          label={label}
          TooltipDescription={TooltipDescription}
          description={description}
          required={required}
          readOnly={readOnly}
          form={form}
          value={value}
        />
      );
    ////////////////////////////////////////////////////////////////
    case "monetary":
      return (
        <Form.Item
          key={fieldId}
          label={label}
          tooltip={TooltipDescription(description)}
          name={fieldId}
          // initialValue={value || undefined}
          rules={[
            {
              required: required,
              message: t("contacts.fieldXRequired", { x: label }),
            },
          ]}
        >
          <CurrencyField
            // uniqueValue={checkUniqueValues(uniqueValue, action, value)}
            fieldValue={value || undefined}
            options={config?.countries}
            form={form}
            fieldId={fieldId}
            setIsDisable={setSubmitIsDisable}
            readOnly={readOnly}
          />
        </Form.Item>
      );
    ////////////////////////////////////////////////////////////////
    case "phone":
      return (
        <Form.Item
          key={fieldId}
          label={label}
          tooltip={TooltipDescription(description)}
          name={fieldId}
          // initialValue={value || undefined}
          rules={[
            {
              required: required,
              message: t("contacts.fieldXRequired", { x: label }),
            },
          ]}
        >
          <PhoneFields
            // onChange={onChange}
            // uniqueValue={checkUniqueValues(uniqueValue, action, value)}
            fieldValue={value}
            options={config?.countries}
            defaultOption={
              config?.default_dial_code && config?.default_dial_code
            }
            form={form}
            fieldId={fieldId}
            setIsDisable={setSubmitIsDisable}
            readOnly={readOnly}
          />
        </Form.Item>
      );
    ////////////////////////////////////////////////////////////////
    case "autocomplete":
      return (
        <Form.Item
          key={fieldId}
          label={label}
          tooltip={TooltipDescription(description)}
          name={fieldId}
          // initialValue={value || undefined}
          rules={[
            {
              required: required,
              message: t("contacts.fieldXRequired", { x: label }),
            },
          ]}
        >
          <FieldAutoComplete
            fieldValue={value}
            fieldId={fieldId}
            familyId={familyId}
            fieldModule={fieldModule}
            form={form}
            setIsDisable={setSubmitIsDisable}
            RenderOptions={RenderOptions}
          />
        </Form.Item>
      );
    ////////////////////////////////////////////////////////////////
    default:
      return <></>;
  }
};

export default DisplayDynamicFieldsByType;
//
// case "password":
//   return (
//     <Card className="mb-4">
//       <Form.Item
//         key={fieldId}
//         label={label}
//         tooltip={TooltipDescription(description)}
//         name={fieldId}
//         // initialValue={value || undefined}
//         rules={[
//           {
//             required: required,
//             message: t("contacts.fieldXRequired", { x: label }),
//           },
//           {
//             pattern: passwordRegex,
//             message: t("contacts.min10chat"),
//           },
//         ]}
//       >
//         <Input.Password />
//       </Form.Item>
//       <Form.Item
//         key={"confirmPassword"}
//         label="Confirm Password"
//         name="confirmPassword"
//         dependencies={["password"]}
//         hasFeedback
//         rules={[
//           {
//             required: required,
//             message: `The 'Password' field is required`,
//           },
//           {
//             pattern: passwordRegex,
//             message: `Minimum ten (10) characters, at least one uppercase letter, one lowercase letter and one number!`,
//           },
//           ({ getFieldValue }) => ({
//             validator(_, value) {
//               if (!value || getFieldValue(fieldId) === value) {
//                 return Promise.resolve();
//               }
//               return Promise.reject(
//                 new Error(
//                   "The two passwords that you entered do not match!"
//                 )
//               );
//             },
//           }),
//         ]}
//       >
//         <Input.Password />
//       </Form.Item>
//       <Space>
//         <Button
//           onClick={handleGeneratePassword}
//           type="primary"
//           icon={<FiKey className="mr-1" />}
//         >
//           Generate Password
//         </Button>
//         <Button
//           onClick={() => {
//             navigator.clipboard.writeText(
//               form.getFieldValue()["confirmPassword"]
//             );
//             message.open({
//               type: "success",
//               content: "Password copied",
//             });
//           }}
//           icon={<FiCopy className="mr-1" />}
//         >
//           Copy Password
//         </Button>
//       </Space>
//     </Card>
//   );
