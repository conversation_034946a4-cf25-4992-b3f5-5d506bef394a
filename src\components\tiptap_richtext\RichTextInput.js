import React, {
  useState,
  useRef,
  useEffect,
  useLayoutEffect,
  lazy,
  Suspense,
} from "react";
import { Extension, isActive, textInputRule } from "@tiptap/core";
import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Underline from "@tiptap/extension-underline";
import Image from "@tiptap/extension-image";
import Mention from "@tiptap/extension-mention";
//import Link from "@tiptap/extension-link";
import Placeholder from "@tiptap/extension-placeholder";
import Heading from "@tiptap/extension-heading";
import { Color } from "@tiptap/extension-color";
import TextStyle from "@tiptap/extension-text-style";
import Highlight from "@tiptap/extension-highlight";
import TaskList from "@tiptap/extension-task-list";
import TaskItem from "@tiptap/extension-task-item";
import { Button, Modal, Tooltip, Dropdown, Divider } from "antd";
import {
  SmileOutlined,
  FontColorsOutlined,
  CaretRightOutlined,
  CloseOutlined,
  PauseOutlined,
  PlayCircleOutlined,
} from "@ant-design/icons";
import { useSelector } from "react-redux";

// import renderItems from "./renderItems.js";
// import getSuggestionItems from "./items.js";
// import Commands from "./commands.js";
import suggestion from "./mentionSuggestion.js";
import useOnClickOutside from "../../pages/layouts/chat/hooks/useClickOutside.js";
import VideoRecorder from "./VideoRecorder.js";
import { useTranslation } from "react-i18next";
import { store } from "../../new-redux/store.js";
import { displayEditorIcon } from "./displayIcon.js";
import { GithubPicker } from "react-color";
import CodeBlock from "@tiptap/extension-code-block";
import { Loader } from "../Chat/index.js";
import data from "@emoji-mart/data";
import { convertToPlain } from "../../pages/layouts/chat/utils/ConversationUtils.js";
import GeneratorText from "pages/layouts/chat/conversation/input/GeneratorText/generator-text.jsx";
const Picker = lazy(() => import("@emoji-mart/react"));

const MenuBar = ({
  editor,
  isListModeActive,
  setIsListModeActive,
  source,
  t,
}) => {
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [showHighlightPicker, setShowHighlightPicker] = useState(false);

  if (!editor) {
    return null;
  }

  let items = [
    {
      key: 1,
      label: <p>Normal</p>,
      onClick: (e) => editor.chain().focus().toggleHeading({ level: 4 }).run(),
      className: editor.isActive("heading", { level: 4 }) ? "is_active" : "",
    },
    {
      key: 2,
      label: <h2>Medium</h2>,
      onClick: (e) => editor.chain().focus().toggleHeading({ level: 2 }).run(),
      className: editor.isActive("heading", { level: 2 }) ? "is_active" : "",
    },
    {
      key: 3,
      label: <h1>Large</h1>,
      onClick: (e) => editor.chain().focus().toggleHeading({ level: 1 }).run(),
      className: editor.isActive("heading", { level: 1 }) ? "is_active" : "",
    },
    {
      key: 4,
      name: "checkboxes_list",
      onClick: () => editor.chain().focus().toggleTaskList().run(),
      className: editor.isActive("taskList") ? "is-active" : "",
    },
  ];

  return (
    <div
      className={`${
        source != "notes" ? "menuBar" : "menuBarSourceNotes"
      } w-full shadow-md  `}
    >
      <section className="relative">
        {/* <Tooltip title="Color">


          <button
            onClick={() => editor.chain().focus().setColor('#958DF1').run()}
            className={editor.isActive('textStyle', { color: '#958DF1' }) ? 'is-active bg-red-500 h-5 w-5' : 'h-5 w-5 bg-red-500'}
          >

          </button>
          <button
            onClick={() => editor.chain().focus().setColor('#F98181').run()}
            className={editor.isActive('textStyle', { color: '#F98181' }) ? 'is-active' : ''}
          >
            red
          </button>
          <button
            onClick={() => editor.chain().focus().setColor('#FBBC88').run()}
            className={editor.isActive('textStyle', { color: '#FBBC88' }) ? 'is-active' : ''}
          >
            orange
          </button>
          <button
            onClick={() => editor.chain().focus().setColor('#FAF594').run()}
            className={editor.isActive('textStyle', { color: '#FAF594' }) ? 'is-active' : ''}
          >
            yellow
          </button>
          <button
            onClick={() => editor.chain().focus().setColor('#70CFF8').run()}
            className={editor.isActive('textStyle', { color: '#70CFF8' }) ? 'is-active' : ''}
          >
            blue
          </button>
          <button
            onClick={() => editor.chain().focus().setColor('#94FADB').run()}
            className={editor.isActive('textStyle', { color: '#94FADB' }) ? 'is-active' : ''}
          >
            teal
          </button>
          <button
            onClick={() => editor.chain().focus().setColor('#B9F18D').run()}
            className={editor.isActive('textStyle', { color: '#B9F18D' }) ? 'is-active' : ''}
          >
            green
          </button>
          <button onClick={() => editor.chain().focus().unsetColor().run()}>unsetColor</button>

        </Tooltip> */}
        <Tooltip title="Bold">
          <Button
            onClick={() => editor.chain().focus().toggleBold().run()}
            className={editor.isActive("bold") ? "is_active" : ""}
            type="text"
            icon={displayEditorIcon("bold", "18px")}
            shape="circle"
          />
        </Tooltip>
        <Tooltip title="Italic">
          <Button
            onClick={() => editor.chain().focus().toggleItalic().run()}
            className={editor.isActive("italic") ? "is_active" : ""}
            type="text"
            icon={displayEditorIcon("italic", "18px")}
            shape="circle"
          />
        </Tooltip>
        {!["chat_reply", "no_chat"].includes(source) && (
          <>
            <Tooltip title="Underline">
              <Button
                onClick={() => editor.chain().focus().toggleUnderline().run()}
                className={editor.isActive("underline") ? "is_active" : ""}
                type="text"
                icon={displayEditorIcon("underline", "18px")}
                shape="circle"
              />
            </Tooltip>
            <Tooltip title="Linethrough">
              <Button
                onClick={() => editor.chain().focus().toggleStrike().run()}
                className={editor.isActive("strike") ? "is_active" : ""}
                type="text"
                icon={displayEditorIcon("strike", "18px")}
                shape="circle"
              />
            </Tooltip>
          </>
        )}
      </section>

      <section className="relative">
        {!["chat_reply"].includes(source) && (
          <>
            <Divider type="vertical" />

            <Tooltip title="Color">
              <Button
                // onClick={() => editor.chain().focus().toggleBold().run()}
                // className={editor.isActive("bold") ? "is_active" : ""}
                onClick={() => setShowColorPicker(!showColorPicker)}
                type="text"
                icon={displayEditorIcon("color", "18px")}
                shape="circle"
              />
            </Tooltip>
            {showColorPicker && (
              <div className="absolute bottom-14">
                <GithubPicker
                  triangle="hide"
                  colors={[
                    "#000000",
                    "#d61125",
                    "#0d9c00",
                    "#0000ff",
                    "#ffff00",
                    "#FFFFFF",
                  ]}
                  width="165px"
                  onChangeComplete={(color) =>
                    editor.chain().focus().setColor(color.hex).run()
                  }
                />
              </div>
            )}
          </>
        )}
        {!["chat_reply"].includes(source) && (
          <>
            <Tooltip title="Highlight">
              <Button
                // onClick={() => editor.chain().focus().toggleBold().run()}
                // className={editor.isActive("bold") ? "is_active" : ""}
                onClick={() => setShowHighlightPicker(!showHighlightPicker)}
                type="text"
                icon={displayEditorIcon("highlight", "18px")}
                shape="circle"
              />
            </Tooltip>
            {showHighlightPicker && (
              <div className="absolute bottom-14">
                <GithubPicker
                  triangle="hide"
                  colors={[
                    "#000000",
                    "#d61125",
                    "#0d9c00",
                    "#0000ff",
                    "#ffff00",
                    "#FFFFFF",
                  ]}
                  width="165px"
                  onChangeComplete={(color) => {
                    if (color.hex !== "#FFFFFF")
                      editor
                        .chain()
                        .focus()
                        .toggleHighlight({ color: color.hex })
                        .run();
                    else editor.chain().focus().unsetHighlight().run();
                  }}
                />
              </div>
            )}
          </>
        )}
      </section>

      <Divider type="vertical" />

      <section>
        <Dropdown
          menu={{
            items,
          }}
        >
          <Button shape="circle" type="text">
            {displayEditorIcon("size", "18px")}
          </Button>
        </Dropdown>
      </section>

      {/* <Button
          onClick={(e) => editor.chain().focus().toggleHeading({ level: 2 }).run()}
          className={editor.isActive("heading", { level: 2 }) ? "is_active" : ""}
          type="text"
          icon={<FaHeading className="heading3" />}
          shape="circle"
          size="small"
        />
        <Button
          onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
          className={editor.isActive("heading", { level: 3 }) ? "is_active" : ""}
          type="text"
          icon={<FaHeading />}
          shape="circle"
          size="small"
        /> */}

      <Divider type="vertical" />

      <section>
        <Tooltip title="Unordered list">
          <Button
            onClick={() => {
              setIsListModeActive(!isListModeActive);
              editor.chain().focus().toggleBulletList().run();
            }}
            className={editor.isActive("bulletList") ? "is_active" : ""}
            type="text"
            icon={displayEditorIcon("unorderedList", "18px")}
            shape="circle"
          />
        </Tooltip>
        <Tooltip title="Ordered list">
          <Button
            onClick={() => {
              setIsListModeActive(!isListModeActive);
              editor.chain().focus().toggleOrderedList().run();
            }}
            className={editor.isActive("orderedList") ? "is_active" : ""}
            type="text"
            icon={displayEditorIcon("orderedList", "18px")}
            shape="circle"
          />
        </Tooltip>
        {/* <Tooltip title="Checklist">
          <Button
            onClick={() => {
              setIsListModeActive(!isListModeActive);
              editor.chain().focus().toggleTaskList().run();
            }}
            className={editor.isActive("taskList") ? "is_active" : ""}
            type="text"
            icon={displayEditorIcon("checklist", "18px")}
            shape="circle"
          />
        </Tooltip> */}
      </section>

      {!["chat_reply", "no_chat"].includes(source) && (
        <section>
          <Divider type="vertical" />

          <Tooltip title="Code">
            <Button
              onClick={() => editor.chain().focus().toggleCodeBlock().run()}
              type="text"
              icon={displayEditorIcon("code", "18px")}
              shape="circle"
            />
          </Tooltip>
          <Divider type="vertical" />
          <Tooltip title="Undo">
            <Button
              onClick={() => editor.chain().focus().undo().run()}
              type="text"
              icon={displayEditorIcon("undo", "18px")}
              shape="circle"
            />
          </Tooltip>
          <Tooltip title="Redo">
            <Button
              onClick={(e) => editor.chain().focus().redo().run()}
              type="text"
              icon={displayEditorIcon("redo", "18px")}
              shape="circle"
            />
          </Tooltip>
          {source === "tasks" && (
            <Tooltip title={t("tasks.checklistTooltip")}>
              <Button
                onClick={() => editor.chain().focus().toggleTaskList().run()}
                className={editor.isActive("taskList") ? "is_active" : ""}
                type="text"
                icon={displayEditorIcon("checkboxes_list", "18px")}
                shape="circle"
              />
            </Tooltip>
          )}
        </section>
      )}
      {/* </div> */}
    </div>
  );
};

const mimeType = "video/webm;codecs=vp8,opus";

const SmilieReplacer = Extension.create({
  name: "smilieReplacer",

  addInputRules() {
    return [
      textInputRule({ find: /-___-$/, replace: "😑 " }),
      textInputRule({ find: /:'-\)$/, replace: "😂 " }),
      textInputRule({ find: /':-\)$/, replace: "😅 " }),
      textInputRule({ find: /':-D $/, replace: "😅 " }),
      textInputRule({ find: />:-\)$/, replace: "😆 " }),
      textInputRule({ find: /-__-$/, replace: "😑 " }),
      textInputRule({ find: /':-\($/, replace: "😓 " }),
      textInputRule({ find: /:'-\($/, replace: "😢 " }),
      textInputRule({ find: />:-\($/, replace: "😠 " }),
      textInputRule({ find: /O:-\)$/, replace: "😇 " }),
      textInputRule({ find: /0:-3$/, replace: "😇 " }),
      textInputRule({ find: /0:-\)$/, replace: "😇 " }),
      textInputRule({ find: /0;\^\)$/, replace: "😇 " }),
      textInputRule({ find: /O;-\)$/, replace: "😇 " }),
      textInputRule({ find: /0;-\)$/, replace: "😇 " }),
      textInputRule({ find: /O:-3$/, replace: "😇 " }),
      textInputRule({ find: /:'\)$/, replace: "😂 " }),
      textInputRule({ find: /:-D$/, replace: "😃 " }),
      textInputRule({ find: /':\)$/, replace: "😅 " }),
      textInputRule({ find: /'=\)$/, replace: "😅 " }),
      textInputRule({ find: /':D$/, replace: "😅 " }),
      textInputRule({ find: /'=D$/, replace: "😅 " }),
      textInputRule({ find: />:\)$/, replace: "😆 " }),
      textInputRule({ find: />;\)$/, replace: "😆 " }),
      textInputRule({ find: />=\)$/, replace: "😆 " }),
      textInputRule({ find: /;-\)$/, replace: "😉 " }),
      textInputRule({ find: /\*-\)$/, replace: "😉 " }),
      textInputRule({ find: /;-\]$/, replace: "😉 " }),
      textInputRule({ find: /;\^\)$/, replace: "😉 " }),
      textInputRule({ find: /B-\)$/, replace: "😎 " }),
      textInputRule({ find: /8-\)$/, replace: "😎 " }),
      textInputRule({ find: /B-D$/, replace: "😎 " }),
      textInputRule({ find: /8-D$/, replace: "😎 " }),
      textInputRule({ find: /:-\*$/, replace: "😘 " }),
      textInputRule({ find: /:\^\*$/, replace: "😘 " }),
      textInputRule({ find: /:-\)$/, replace: "🙂 " }),
      textInputRule({ find: /-_-$/, replace: "😑 " }),
      textInputRule({ find: /:-X$/, replace: "😶 " }),
      textInputRule({ find: /:-#$/, replace: "😶 " }),
      textInputRule({ find: /:-x$/, replace: "😶 " }),
      textInputRule({ find: />.<$/, replace: "😣 " }),
      textInputRule({ find: /:-O$/, replace: "😮 " }),
      textInputRule({ find: /:-o$/, replace: "😮 " }),
      textInputRule({ find: /O_O$/, replace: "😮 " }),
      textInputRule({ find: />:O$/, replace: "😮 " }),
      textInputRule({ find: /:-P$/, replace: "😛 " }),
      textInputRule({ find: /:-p$/, replace: "😛 " }),
      textInputRule({ find: /:-Þ$/, replace: "😛 " }),
      textInputRule({ find: /:-þ$/, replace: "😛 " }),
      textInputRule({ find: /:-b$/, replace: "😛 " }),
      textInputRule({ find: />:P$/, replace: "😜 " }),
      textInputRule({ find: /X-P$/, replace: "😜 " }),
      textInputRule({ find: /x-p$/, replace: "😜 " }),
      textInputRule({ find: /':\($/, replace: "😓 " }),
      textInputRule({ find: /'=\($/, replace: "😓 " }),
      textInputRule({ find: />:\\$/, replace: "😕 " }),
      textInputRule({ find: />:\/$/, replace: "😕 " }),
      textInputRule({ find: /:-\/$/, replace: "😕 " }),
      textInputRule({ find: /:-.$/, replace: "😕 " }),
      textInputRule({ find: />:\[$/, replace: "😞 " }),
      textInputRule({ find: /:-\($/, replace: "😞 " }),
      textInputRule({ find: /:-\[$/, replace: "😞 " }),
      textInputRule({ find: /:'\($/, replace: "😢 " }),
      textInputRule({ find: /;-\($/, replace: "😢 " }),
      textInputRule({ find: /#-\)$/, replace: "😵 " }),
      textInputRule({ find: /%-\)$/, replace: "😵 " }),
      textInputRule({ find: /X-\)$/, replace: "😵 " }),
      textInputRule({ find: />:\($/, replace: "😠 " }),
      textInputRule({ find: /0:3$/, replace: "😇 " }),
      textInputRule({ find: /0:\)$/, replace: "😇 " }),
      textInputRule({ find: /O:\)$/, replace: "😇 " }),
      textInputRule({ find: /O=\)$/, replace: "😇 " }),
      textInputRule({ find: /O:3$/, replace: "😇 " }),
      textInputRule({ find: /<\/3$/, replace: "💔 " }),
      textInputRule({ find: /:D$/, replace: "😃 " }),
      textInputRule({ find: /=D$/, replace: "😃 " }),
      textInputRule({ find: /;\)$/, replace: "😉 " }),
      textInputRule({ find: /\*\)$/, replace: "😉 " }),
      textInputRule({ find: /;\]$/, replace: "😉 " }),
      textInputRule({ find: /;D$/, replace: "😉 " }),
      textInputRule({ find: /B\)$/, replace: "😎 " }),
      textInputRule({ find: /8\)$/, replace: "😎 " }),
      textInputRule({ find: /:\*$/, replace: "😘 " }),
      textInputRule({ find: /=\*$/, replace: "😘 " }),
      textInputRule({ find: /:\)$/, replace: "🙂 " }),
      textInputRule({ find: /=\]$/, replace: "🙂 " }),
      textInputRule({ find: /=\)$/, replace: "🙂 " }),
      textInputRule({ find: /:\]$/, replace: "🙂 " }),
      textInputRule({ find: /:X$/, replace: "😶 " }),
      textInputRule({ find: /:#$/, replace: "😶 " }),
      textInputRule({ find: /=X$/, replace: "😶 " }),
      textInputRule({ find: /=x$/, replace: "😶 " }),
      textInputRule({ find: /:x$/, replace: "😶 " }),
      textInputRule({ find: /=#$/, replace: "😶 " }),
      textInputRule({ find: /:O$/, replace: "😮 " }),
      textInputRule({ find: /:o$/, replace: "😮 " }),
      textInputRule({ find: /:P$/, replace: "😛 " }),
      textInputRule({ find: /=P$/, replace: "😛 " }),
      textInputRule({ find: /:p$/, replace: "😛  " }),
      textInputRule({ find: /=p$/, replace: "😛 " }),
      textInputRule({ find: /:Þ$/, replace: "😛 " }),
      textInputRule({ find: /:þ$/, replace: "😛 " }),
      textInputRule({ find: /:b$/, replace: "😛 " }),
      textInputRule({ find: /d:$/, replace: "😛 " }),
      textInputRule({ find: /:-\/$/, replace: "😕 " }),
      textInputRule({ find: /:\\$/, replace: "😕 " }),
      textInputRule({ find: /=\/$/, replace: "😕 " }),
      textInputRule({ find: /=\\$/, replace: "😕 " }),
      textInputRule({ find: /:L$/, replace: "😕 " }),
      textInputRule({ find: /=L$/, replace: "😕 " }),
      textInputRule({ find: /:\($/, replace: "😞 " }),
      textInputRule({ find: /:\[$/, replace: "😞 " }),
      textInputRule({ find: /=\($/, replace: "😞 " }),
      textInputRule({ find: /;\($/, replace: "😢 " }),
      textInputRule({ find: /D:$/, replace: "😨 " }),
      textInputRule({ find: /:\$$/, replace: "😳 " }),
      textInputRule({ find: /=\$$/, replace: "😳 " }),
      textInputRule({ find: /#\)$/, replace: "😵 " }),
      textInputRule({ find: /%\)$/, replace: "😵 " }),
      textInputRule({ find: /X\)$/, replace: "😵 " }),
      textInputRule({ find: /:@$/, replace: "😠 " }),
      textInputRule({ find: /<3$/, replace: "❤️ " }),
      textInputRule({ find: /\/shrug$/, replace: "¯\\_(ツ)_/¯" }),
    ];
  },
});

const RichTextInput = ({
  source,
  setContent,
  content,
  FileComponent,
  AudioInput,
  uploadFile,
  onBlurInput,
  filesToUpload,
  watchShowInputMenuBar,
  onKeyPress,
  onKeyUp,
  isMessageSent,
  setEditorContent,
  editorContent,
  showAudioInput,
  updatewithenterKey,
  showEmojiInEditor = true,
  editable = true,
  saveContent = () => {},
  maxWidth,
}) => {
  const [showEmojis, setShowEmojis] = useState(false);
  const [selectedEmoji, setSelectedEmoji] = useState(null);
  const [showMenuBar, setShowMenuBar] = useState(
    source.includes("note") ? true : false
  );
  const [isListModeActive, setIsListModeActive] = useState(false);
  const [recordVideoPanel, setRecordVideoPanel] = useState(false);
  // const [editorContent, setEditorContent] = useState(null);
  const [editorIsDropTarget, setEditorIsDropTarget] = useState(false);
  const [permission, setPermission] = useState(false);
  const [recordingStatus, setRecordingStatus] = useState("inactive");
  const [stream, setStream] = useState(null);
  const [recordedVideo, setRecordedVideo] = useState(null);
  const [videoChunks, setVideoChunks] = useState([]);

  const { selectedConversation } = useSelector((state) => state.ChatRealTime);
  const currentUser = useSelector((state) => state.chat.currentUser);

  const isActiveToolsIA = currentUser?.integrations.some(
    (el) => el.type === 2 && el.is_active === 1
  );
  const textInputRef = useRef(null);
  const emojiRef = useRef(null);
  const filesCounterRef = useRef(null);
  const liveVideoFeed = useRef(null);
  const mediaRecorder = useRef(null);

  const disableEnter = Extension.create({
    addKeyboardShortcuts(event) {
      return {
        Enter: () => {
          if (!source.includes("note")) {
            return true;
          }
        },
        // Overwrite the behavior from tiptap with the default behavior on pressing Shift-Enter.
        "Shift-Enter": () =>
          this.editor.commands.first(({ commands }) => [
            () => commands.newlineInCode(),
            () => commands.splitListItem("listItem"), // This line added
            () => commands.createParagraphNear(),
            () => commands.liftEmptyBlock(),
            () => commands.splitBlock(),
          ]),
      };
    },
  });
  const { t } = useTranslation("common");

  const editor = useEditor({
    editorProps: {
      attributes: {
        class:
          source === "chat_update"
            ? "ProseMirror-chat-update-message"
            : editorIsDropTarget
            ? "Prosemirror-dropzone"
            : source === "tasks"
            ? "ProseMirror-textarea"
            : source === "notes"
            ? "ProseMirror-notes"
            : source === "note_update"
            ? "ProseMirror-note-update"
            : "ProseMirror",
      },
      transformPastedHTML: (html) => {
        return html.replace(/<img.*?>/g, ""); // remove any images copied any pasted as HTML
      },
    },
    extensions: [
      Color,
      TextStyle,
      Highlight.configure({ multicolor: true }),
      StarterKit,
      Underline,
      disableEnter,
      Heading.configure({
        levels: [1, 2, 4],
      }),
      Image.configure({
        inline: false,
        allowBase64: false,
        HTMLAttributes: {
          class: "image-from-chat",
        },
      }),
      SmilieReplacer,
      // Paragraph.configure({
      //   HTMLAttributes: { class: "paragraph" },
      // }),

      selectedConversation?.type !== "user" &&
        Mention.extend({
          addAttributes() {
            return {
              ...this.parent(),
              userId: {
                default: "",
              },
            };
          },
        }).configure({
          HTMLAttributes: { class: "mention" },
          suggestion,
        }),
      // Link.configure({
      //   // protocols: ["ftp", "mailto"],
      //   openOnClick: true,
      //   target: "_blank",
      //   validate: (href) =>
      //     /^https?:\/\//.test(href) ? "" : "https://" + href,
      // }),

      // Commands.configure({
      //   suggestion: {
      //     items: getSuggestionItems,
      //     render: renderItems,
      //   },
      // }),
      Placeholder.configure({
        placeholder: "Aa...",
      }),
      CodeBlock.configure({
        languageClassPrefix: "language-",
      }),
      ...(source === "tasks" ? [TaskList, TaskItem] : []),
    ],
    content: source && source.includes("chat") ? content : editorContent,
    onUpdate(event) {
      if (source && source?.includes("chat")) {
        if (
          event?.transaction?.steps[0]?.slice?.content?.content[0]?.type
            ?.name === "image" &&
          filesCounterRef.current < 5
        ) {
          fetch(event?.transaction?.doc?.content?.content[0]?.attrs?.src)
            .then((res) => res.blob())
            .then((blob) => {
              let file = new File([blob], "image-" + new Date().toISOString(), {
                type: blob?.type,
              });
              uploadFile(file);
            })
            .catch((error) => {
              console.log(`on update file error ${error}`);
            })
            .finally(() => (filesCounterRef.current += 1));
        } else {
          // From chat
          setContent(editor && editor.getHTML(editorContent));
        }
      } else {
        // From components
        source &&
          !source?.includes("chat") &&
          setEditorContent(editor && editor.getHTML());
      }
    },
    onBlur: (e) => {
      editor && editor.commands?.blur();
      if (source && source?.includes("chat") && onBlurInput) {
        onBlurInput(editor.getHTML());
        // editor && editor.commands?.blur();
        // content !== "" ||
      } else if (source === "tasks") {
        let htmlData = editor.getHTML();
        if (htmlData !== editorContent) {
          saveContent(htmlData);
        }
      }
    },
    onSelectionUpdate: ({ editor }) => {
      const { empty, from, to } = editor.view.state.selection;

      if (!source.includes("note")) {
        const isAlreadySelected =
          !empty && from === to && from === editor.state.selection.from;
        setShowMenuBar(!empty && from !== to && !isAlreadySelected);
      }
    },
    editable,
  });
  const addImage = (data) => {
    const { files } = data;
    uploadFile && uploadFile(files);
    if (files && files.length > 0 && filesCounterRef.current < 5) {
      for (const file of Array.from(files)) {
        const [mime] = file.type.split("/");

        filesCounterRef.current += Array.from(files).length;
        if (source && !source?.includes("chat")) {
          if (mime === "image") {
            const url = URL.createObjectURL(file);
            // console.log("IMAGE URL  " + url);
            editor?.chain().focus().setImage({ src: url }).run();
          }
        }
      }
    }
  };

  const addEmoji = (emojiObject) => {
    let symbol = emojiObject?.unified.split("-");
    let codesArray = [];
    symbol.forEach((element) => codesArray.push("0x" + element));
    let emoji = String.fromCodePoint(...codesArray);
    setSelectedEmoji(emoji);
  };

  const getCameraPermission = async () => {
    setRecordedVideo(null);
    try {
      const videoConstraints = {
        audio: false,
        video: true,
      };

      const audioContraints = {
        audio: true,
      };

      // create audio and video streams separately
      const audioStream = await navigator.mediaDevices.getUserMedia(
        audioContraints
      );
      const videoStream = await navigator.mediaDevices.getUserMedia(
        videoConstraints
      );

      setPermission(true);

      //combine both audio and video streams
      const combineStream = new MediaStream([
        ...videoStream.getVideoTracks(),
        ...audioStream.getAudioTracks(),
      ]);

      setStream(combineStream);

      //set videostream to live feed player
      liveVideoFeed.current.srcObject = videoStream;
      startRecording(combineStream);
    } catch (error) {
      console.log("getCameraPermission error", error);
    }
  };

  const startRecording = async (streamParam) => {
    setRecordingStatus("recording");

    const media = new MediaRecorder(streamParam, { mimeType });

    mediaRecorder.current = media;
    mediaRecorder.current.start();

    let localVideoChunks = [];

    mediaRecorder.current.ondataavailable = (event) => {
      if (typeof event?.data === undefined) return;
      if (event?.data?.size === 0) return;

      localVideoChunks.push(event?.data);
    };

    setVideoChunks(localVideoChunks);
  };

  const stopRecording = async () => {
    setPermission(false);
    setRecordingStatus("inactive");
    mediaRecorder?.current.stop();

    mediaRecorder.current.onstop = () => {
      const videoBlob = new Blob(videoChunks, { type: mimeType });
      const videoUrl = URL.createObjectURL(videoBlob);

      setRecordedVideo(videoUrl);

      setVideoChunks([]);
    };
  };

  const resetRecordVideo = () => {
    setRecordVideoPanel(false);
    setRecordingStatus("inactive");
    setPermission(false);
    setRecordedVideo(null);
    setVideoChunks([]);
    mediaRecorder.current = null;
    if (liveVideoFeed.current !== null) {
      liveVideoFeed.current.srcObject = null;
    }
  };

  useOnClickOutside(emojiRef, showEmojis, () => {
    setShowEmojis(false);
  });

  useLayoutEffect(() => {
    const focusElement = () => {
      source !== "tasks" && editor && editor.commands?.focus("end");
    };

    if (source !== "tasks") {
      editor?.commands.clearContent();
      editor && editor.commands?.focus("end");
    }
    window.addEventListener("focus", focusElement, false);
    return () => window.removeEventListener("focus", focusElement, false);
  }, [editor, selectedConversation?.id, source]);

  useLayoutEffect(() => {
    if (source && source.includes("chat")) {
      const getLastMessage = async () => {
        const lastMessage = await store.getState().chat.lastMessage;
        const lastMessageValue =
          lastMessage.find(
            (item) =>
              item.conversationId ===
              `${selectedConversation?.id}_${selectedConversation?.type}_${
                source.includes("chat_reply") ? "chat_reply" : source
              }`
          )?.value ?? "";
        return lastMessageValue;
      };

      (async () => {
        let response = await getLastMessage();
        if (convertToPlain(response).length > 0) setContent(response);

        //  if (response.length > 0) {
        source && source !== "chat_update" && editor?.commands.clearContent();
        editor?.commands.insertContent(response);
        //}
      })();
    }

    // else if (source === "tasks") {
    //   editor?.commands.insertContent(editorContent);
    // }
  }, [
    selectedConversation?.id,
    editor,
    selectedConversation?.type,
    // showMenuBar,
    // lastMessage,
    source,
  ]);

  useEffect(() => {
    let time;
    if (source === "chat_update" || source === "note_update") {
      time = setTimeout(() => editor?.commands.insertContent(content));
    }
    return () => clearTimeout(time);
  }, [source, content, editor]);

  useEffect(() => {
    if (isMessageSent) {
      editor && editor.commands.clearContent();
    }
  }, [isMessageSent, editor]);

  useEffect(() => {
    if (source && source.includes("chat")) {
      watchShowInputMenuBar && watchShowInputMenuBar(showMenuBar);
    }
  }, [showMenuBar, source]);

  useEffect(() => {
    editor && editor.commands.insertContent(selectedEmoji);
    setSelectedEmoji(null);
  }, [editor, selectedEmoji]);

  useEffect(() => {
    if (source && source?.includes("chat") && filesToUpload) {
      filesCounterRef.current = filesToUpload.length;
    }
  }, [source, filesToUpload]);

  return (
    <>
      <div className="relative">
        {showEmojis && (
          <div
            ref={emojiRef}
            className={`absolute right-0 ${
              source && source?.includes("note") ? "z-50" : "z-10"
            } ${
              source && (source?.includes("chat") || source?.includes("note"))
                ? "bottom-10"
                : "bottom-28"
            }`}
          >
            <Suspense fallback={<Loader size={30} />}>
              <Picker data={data} emojiSize={25} onEmojiSelect={addEmoji} />
            </Suspense>
          </div>
        )}
        <div
          className={`textEditor`}
          style={{
            maxWidth: !!maxWidth
              ? maxWidth
              : source === "tasks"
              ? "680px"
              : source === "chat_reply_big"
              ? "740px"
              : source === "chat_reply"
              ? "320px"
              : source.includes("note")
              ? "100%"
              : showAudioInput
              ? "0"
              : `calc(100vw - 530px)`,
          }}
          ref={textInputRef}
        >
          {showMenuBar && source !== "RMC" && (
            <MenuBar
              source={source}
              editor={editor}
              isListModeActive={isListModeActive}
              setIsListModeActive={setIsListModeActive}
              t={t}
            />
          )}
          <EditorContent
            id="editor-input"
            data-id-editor={"editor-" + source}
            tabIndex={1}
            editor={editor}
            onPaste={(e) => {
              e.preventDefault();
              e.stopPropagation();

              source.includes("chat") && addImage(e.clipboardData);
            }}
            onDrop={(e) => {
              e.preventDefault();
              e.stopPropagation();
              addImage(e.dataTransfer);
            }}
            onKeyPress={onKeyPress}
            onKeyUp={onKeyUp}
            onKeyDown={updatewithenterKey}
            onDragEnter={() => setEditorIsDropTarget(true)}
            onDragLeave={() => setEditorIsDropTarget(false)}
          />
          <div
            id="action-menu"
            className={`absolute bottom-1.5 right-2 z-50 flex justify-end space-x-1 rounded bg-white p-0.5 ${
              source === "chat_update" && "mr-2"
            }`}
          >
            {isActiveToolsIA ? (
              <GeneratorText editor={editor} source={source} />
            ) : null}
            {!source.includes("note") && (
              <Tooltip title={t("chat.action.rich_text")}>
                <Button
                  className={`${showMenuBar ? "is_active" : ""}`}
                  type="text"
                  icon={<FontColorsOutlined className="w-full" />}
                  shape="circle"
                  size="small"
                  onClick={() => setShowMenuBar(!showMenuBar)}
                />
              </Tooltip>
            )}

            <> {FileComponent} </>

            <>{AudioInput}</>
            {showEmojiInEditor && (
              <Tooltip title={t("chat.action.emoji")}>
                <Button
                  onClick={() => setShowEmojis(true)}
                  className={showEmojis ? "is_active" : ""}
                  type="text"
                  icon={<SmileOutlined />}
                  shape="circle"
                  size="small"
                />
              </Tooltip>
            )}

            {/* {source && source !== "chat_update" && (
              <Button
                className={recordVideoPanel ? "is_active" : ""}
                onClick={() => setRecordVideoPanel(true)}
                type="text"
                icon={<VideoCameraOutlined />}
                shape="circle"
                size="small"
              />
            )} */}
          </div>
        </div>
      </div>
      {/* <Modal
        title="Record video message"
        onCancel={null}
        open={recordVideoPanel}
        closeIcon={<CloseOutlined onClick={resetRecordVideo} />}
        footer={
          <>
            {recordedVideo ? (
              <Button
                type="primary"
                icon={<CaretRightOutlined />}
                // onClick={() => console.log("recorded video", videoChunks)}
              >
                Confirm
              </Button>
            ) : !permission ? (
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                onClick={getCameraPermission}
              >
                Record
              </Button>
            ) : (
              <Button
                type="primary"
                icon={<PauseOutlined />}
                onClick={stopRecording}
              >
                Pause
              </Button>
            )}
            <Button type="default" onClick={resetRecordVideo}>
              Cancel
            </Button>
          </>
        }
        afterOpenChange={(open) => {
          if (open === false) {
            resetRecordVideo();
          }
        }}
      >
        <VideoRecorder
          permission={permission}
          recordingStatus={recordingStatus}
          recordedVideo={recordedVideo}
          getCameraPermission={getCameraPermission}
          startRecording={startRecording}
          stopRecording={stopRecording}
          liveVideoFeed={liveVideoFeed}
        />
      </Modal> */}
    </>
  );
};

export default RichTextInput;
