import { Alert, List, Switch } from "antd";
import { toastNotification } from "components/ToastNotification";
import { setAccountData } from "new-redux/actions/mail.actions";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import MainService from "services/main.service";

const MailingNotification = () => {
  const { dataAccounts } = useSelector((state) => state.mailReducer);
  const { user } = useSelector((state) => state.user);
  const { t } = useTranslation("common");
  const [loadingChangeStatus, setLoadingChangeStatus] = useState(false);
  const sharedAccounts = dataAccounts.some((item) => item.shared === "1");
  const dispatch = useDispatch();

  const changeStatus = async (id, label, status) => {
    setLoadingChangeStatus(true);
    try {
      const response = await MainService.updateNotifcationMailing({
        account_id: id,
        notification_status: status ? 1 : 0,
      });
      dispatch(
        setAccountData(
          dataAccounts.map((el) =>
            el.value === id
              ? {
                  ...el,
                  notification_status: response?.data?.notification_status
                    ? 1
                    : 0,
                }
              : el
          )
        )
      );
      setLoadingChangeStatus(false);

      toastNotification(
        "success",
        status
          ? t("profilemenu.notificationEnabledStatus") + " " + label
          : t("profilemenu.notificationDisabledStatus") + " " + label,
        "topRight"
      );
    } catch (err) {
      setLoadingChangeStatus(false);
      toastNotification(
        "error",
        label + t("toasts.somethingWrong"),
        "topRight"
      );
      console.log(err);
    }
  };

  return (
    <div className="my-2">
      {/* {sharedAccounts ? (
        <Alert
          type="info"
          message={t("profilemenu.notificationMailing")}
          showIcon
        />
      ) : null} */}

      <List
        itemLayout="horizontal"
        dataSource={dataAccounts}
        renderItem={(item, index) => (
          <List.Item
            actions={[
              <Switch
                // disabled={item.creator !== user.id}
                loading={loadingChangeStatus}
                checked={item.notification_status === 1}
                onChange={(e) => changeStatus(item.value, item.label, e)}
              />,
            ]}
          >
            <List.Item.Meta
              //   avatar={<Avatar src={`https://api.dicebear.com/7.x/miniavs/svg?seed=${index}`} />}
              title={item.label}
              //   description="Ant Design, a design language for background applications, is refined by Ant UED Team"
            />
          </List.Item>
        )}
      />
    </div>
  );
};

export default MailingNotification;
