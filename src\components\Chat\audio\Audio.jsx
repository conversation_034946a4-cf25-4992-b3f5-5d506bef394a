import { useEffect, useRef, useState } from "react";
import {
  CaretRightOutlined,
  DownloadOutlined,
  PaperClipOutlined,
  PauseOutlined,
} from "@ant-design/icons";
import { Button, Tooltip, Typography } from "antd";
import { useTranslation } from "react-i18next";

import "./Audio.css";
import { URL_ENV } from "index";
import { handleDownloadFile } from "pages/layouts/chat/utils/ConversationUtils";
const getTime = (d) => {
  d = Number(d);
  var m = Math.floor(d / 60);
  var s = Math.floor(d % 60);
  var mDisplay = m > 0 ? (m < 10 ? "0" + m + ":" : m + ":") : "00:";
  var sDisplay = s > 0 ? (s < 10 ? "0" + s : s) : "00";
  return mDisplay + sDisplay;
};

const acceptedSource = ["main", ""];
function Audio({ url, file, isFullURL = false, source = "" }) {
  const { t } = useTranslation("common");

  const audioRef = useRef(null);
  const checkWidthRef = useRef(null);

  const [isPlay, setIsPlay] = useState(false);
  const [error, setError] = useState(false);
  const [loadingDownload, setLoading] = useState(false);
  const [duration, setDuration] = useState({
    progress: 0,
    progressNUmber: 0,
    length: 0,
  });

  useEffect(() => {
    const auddioRefCopy = audioRef.current;

    const handleEndAudio = () => {
      setIsPlay(false);
      setDuration((c) => ({
        ...c,
        progressNUmber: 0,
        progress: auddioRefCopy?.duration,
      }));
    };
    auddioRefCopy.addEventListener("ended", handleEndAudio);
    return () => {
      auddioRefCopy?.removeEventListener("ended", handleEndAudio);
    };
  }, [url]);

  function playAudio() {
    setIsPlay((c) => !c);

    try {
      setError(false);
      if (isPlay) {
        audioRef.current?.pause();
      } else {
        audioRef.current?.play();
      }
    } catch (e) {
      setIsPlay(false);
      setError(true);
    }
  }

  const seekTo = () => {
    audioRef.current.currentTime =
      (checkWidthRef.current.value / 100) *
      (isNaN(duration.length) ? 1 : duration.length);
  };

  function onTimeUpdate() {
    const duree = parseInt(audioRef.current?.duration);
    const currentTime = audioRef.current?.currentTime;

    setDuration({
      progress: currentTime,
      progressNUmber: (currentTime / duree) * 100,
      length: duree,
    });
  }

  return (
    <div
      className={`${
        !acceptedSource.includes(source) ? "mt-1.5" : ""
      } flex h-full  w-full  max-w-lg flex-col space-y-1`}>
      <audio
        ref={audioRef}
        src={
          !isFullURL
            ? URL_ENV?.REACT_APP_OAUTH_CHAT_API +
              process.env.REACT_APP_SUFFIX_CHAT_GROUP_STORAGE +
              url
            : url
        }
        onError={() => setError(true)}
        onLoadedMetadata={(e) =>
          setDuration((c) => ({
            ...c,
            progress: e?.nativeEvent?.target?.duration,
          }))
        }
        preload="metadata"
        id="audioInput"
        onTimeUpdate={onTimeUpdate}
      />
      <div
        className={`relative flex h-auto flex-col justify-start ${
          file ? " rounded-2xl" : "rounded-3xl"
        }
      ${isFullURL ? "w-full" : "w-1/3"}
      bg-blue-100  px-2  md:w-full`}>
        <div className="flex items-center justify-center p-1">
          <div
            className={`  flex ${
              file ? " flex-col" : "flex-row  items-center"
            } w-full    `}>
            {file && (
              <div className="ml-2  flex flex-1 items-center justify-between space-x-1 text-base  text-black/50   ">
                <div className="flex items-center gap-x-1 ">
                  <PaperClipOutlined className="text-lg" />

                  <Typography.Text type="secondary">
                    {file.file_name}
                  </Typography.Text>
                  {acceptedSource.includes(source) && (
                    <Typography.Text type="secondary">
                      ({file.size})
                    </Typography.Text>
                  )}
                </div>

                <Tooltip
                  title={t("import.downloadFileWithTitle", {
                    filename: file.file_name,
                  })}>
                  <Button
                    disabled={error}
                    loading={loadingDownload}
                    onClick={(e) => handleDownloadFile(e, file, setLoading)}
                    size="large"
                    type="link"
                    shape="circle"
                    icon={<DownloadOutlined />}
                  />{" "}
                </Tooltip>
              </div>
            )}
            <div
              className={`my-1  flex w-full items-center justify-between space-x-1`}>
              <Button
                disabled={error}
                shape="circle"
                type="primary"
                size="small"
                loading={isPlay && !duration.progress}
                icon={
                  !duration.progress ? (
                    <CaretRightOutlined />
                  ) : isPlay ? (
                    <PauseOutlined />
                  ) : (
                    <CaretRightOutlined />
                  )
                }
                onClick={playAudio}
              />
              <div className="text-md  text-blue-700">
                {!duration.progress ||
                isNaN(duration.progress) ||
                duration.progress === Infinity
                  ? null
                  : getTime(duration.progress)}
              </div>

              <input
                disabled={!duration.length || isNaN(duration.length)}
                ref={checkWidthRef}
                type="range"
                min="0"
                max="100"
                value={duration.progressNUmber || 0}
                onChange={seekTo}
                className="seek_slider w-full flex-1 bg-blue-100"
              />
            </div>
          </div>
        </div>
        {error && (
          <span className=" m-0 ml-1  text-red-500">
            {t("chat.audio_message.errorCharge")}
          </span>
        )}
      </div>
    </div>
  );
}

export default Audio;
