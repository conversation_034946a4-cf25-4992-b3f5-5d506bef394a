import {
  <PERSON><PERSON><PERSON>Outlined,
  PlusOutlined,
  DragOutlined,
  MenuOutlined,
} from "@ant-design/icons";
import { <PERSON><PERSON>, Card, Col, Row, Tooltip } from "antd";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";

import { useDispatch } from "react-redux";
import {
  addTaskInDashboard,
  getDataDashboard,
  getLeads_contacts,
  getStatsChat,
  getStatsTasks,
  getStatsTickets,
  RemoveTaskInDashboard,
  setQueueInDashboard,
  setResetData,
  setStatsDeals,
  setTasksInDashboard,
  UpdateTasksInDashboard,
} from "new-redux/actions/dashboard.actions";

import { SET_STATS_TASKS_DASHBOARD } from "new-redux/constants";

import i18n from "translations/i18n";
import EmptyPage from "components/EmptyPage";
import TasksRoom from "pages/tasks/tasksRoom";
import CreateTask from "pages/voip/components/CreateTask";
import { setTask360 } from "new-redux/actions/chat.actions/Input";
import CardQueue2 from "./CardQueue2";

import ListTasks2 from "./components/ListTasks2";
import { useNavigate } from "react-router-dom";
import { roles } from "utils/role";
import CardStatVoip from "./components/Home/CardStatVoip";
import CardStatChat from "./components/Home/CardStatChat";
import CardStatRmc from "./components/Home/CardStatRmc";
import CardStatDeal from "./components/Home/CardStatDeal";
import CardStatTicket from "./components/Home/CardStatTicket";
import CardStatActivitiesTypes from "./components/Home/CardStatActivitiesTypes";
import CardStatEmail from "./components/Home/CardStatEmail";
import CardStatFamilies from "./components/Home/CardStatFamilies";

// Imports pour dnd-kit
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragOverlay,
  rectIntersection,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  rectSortingStrategy,
  horizontalListSortingStrategy,
} from "@dnd-kit/sortable";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import CardStatConvertedLeadsToContacts from "./components/Home/CardStatConvertedLeadsToContacts";
import CardStatConvertedContactsToGuests from "./components/Home/CardStatConvertedContactsToGuests";
import DashboardLoadingSkeleton from "./components/Home/LoadDashboard";
// Composant Card draggable avec bouton drag
const DraggableCard = ({ id, children, isDragEnabled = true, ...props }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div ref={setNodeRef} style={style}>
      <Card
        {...props}
        styles={{
          header: { padding: 0, height: "auto" },
          body: { padding: 0 },
        }}
        style={{
          ...props.style,
          border: isDragging ? "2px dashed #1890ff" : "0",
          position: "relative",
          padding: 0,
        }}
      >
        <div
          style={{
            display: "flex",
            alignItems: "center",
            gap: "8px",
            zIndex: 555,
            position: id === "tasks" ? "-moz-initial" : "absolute",
            padding: id === "tasks" ? "6px 5px" : 0,
            top: 9,
            left: 5,
          }}
        >
          {props.extra}
          {isDragEnabled && (
            <Button
              {...attributes}
              {...listeners}
              type="text"
              size="small"
              shape="circle"
              icon={<MenuOutlined />}
              style={{
                cursor: "grab",
              }}
              title="Glisser pour déplacer"
            />
          )}
        </div>
        {children}
      </Card>
    </div>
  );
};

// Composant pour la ligne activities/tasks avec gestion du drag améliorée
const DraggableRowCard = ({
  id,
  children,
  isRow = false,
  rowId,
  isDragEnabled = true,
  ...props
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: isRow ? rowId : id,
    data: {
      type: isRow ? "row" : "card",
      rowId: isRow ? rowId : undefined,
      cardId: isRow ? undefined : id,
    },
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  if (isRow) {
    // Pour toute la ligne - drag vertical uniquement
    return (
      <div
        ref={setNodeRef}
        style={style}
        className={`row-container ${isDragging ? "dragging-row" : ""} `}
      >
        <Button
          className="absolute top-2 "
          {...attributes}
          {...listeners}
          type="text"
          size="small"
          shape="circle"
          icon={<MenuOutlined />}
          style={{
            cursor: "grab",
          }}
          title="Glisser pour déplacer"
        />
        {children}
      </div>
    );
  }

  // Pour les cartes individuelles dans la ligne - drag horizontal uniquement
  return (
    <div ref={setNodeRef} style={style}>
      <Card
        {...props}
        styles={{
          header: { padding: 0, height: "auto" },
          body: { padding: 0 },
        }}
        style={{
          ...props.style,
          border: isDragging ? "2px dashed #1890ff" : "1px solid #d9d9d9",
          position: "relative",
          padding: 0,
        }}
      >
        <div
          style={{
            display: "flex",
            alignItems: "center",
            gap: "8px",
            padding: "4px",
          }}
        >
          {props.extra}
          {isDragEnabled && (
            <Button
              {...attributes}
              {...listeners}
              type="text"
              size="small"
              icon={<MenuOutlined />}
              style={{
                cursor: "grab",
                // color: "#999",
              }}
              title="Glisser pour déplacer"
            />
          )}
        </div>
        {children}
      </Card>
    </div>
  );
};

// Composant pour une colonne déplaçable
const DraggableCol = ({ id, children, span }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    position: "relative",
    zIndex: isDragging ? 999 : 1,
  };

  return (
    <Col span={span} ref={setNodeRef} style={style}>
      <div
        className={`draggable-col-handle absolute ${
          id === "tasks-cols-left" ? "top-1.5" : "top-2.5"
        } left-2.5  z-50 `}
      >
        <Button
          {...attributes}
          {...listeners}
          type="text"
          size="small"
          shape="circle"
          icon={<MenuOutlined />}
          style={{
            cursor: "grab",
          }}
          title="Glisser pour déplacer"
        />
      </div>
      {children}
    </Col>
  );
};

export const GoTo = ({ title, to, navigate, t, user }) => {
  const handleClick = () => {
    navigate("/stats/families", { state: to });
  };

  return (
    <>
      {roles.includes(user?.role) ? (
        <Tooltip
          title={`${t("chat.goto")} ${t(
            "menu1.stats"
          )?.toLowerCase()} ${title?.toLowerCase()}`}
        >
          <Button
            type="link"
            icon={<ArrowRightOutlined />}
            onClick={handleClick}
            aria-label={`Go to ${title}`}
          />
        </Tooltip>
      ) : null}
    </>
  );
};

export const backgroundImagecard = "linear-gradient(to right, #F4F8F9,#F8F2F9)";
export const gridStyle = {
  width: "50%",
  padding: "13px",
  marginBottom: "1px",
  background: "white",
};
export const stylesCard = {
  header: { padding: "5px 6px 5px 40px", minHeight: "auto" },
  body: { padding: "11px 7px 6px 8px" },
};

const Home4 = ({ start, end }) => {
  const { user } = useSelector((state) => state.user);
  const [load, setLoad] = useState(user?.id ? false : true);
  const [t] = useTranslation("common");
  const navigate = useNavigate();
  const {
    totalQueues,
    channelsRmc,
    statsTasks,
    tasks,
    selectedDepRmc,
    statsTickets,
  } = useSelector((state) => state.dashboardRealTime);
  const { openTaskRoomDrawer } = useSelector((state) => state?.TasksRealTime);
  const [selectedQueue, setSelectedQueue] = useState(
    totalQueues && Array.isArray(totalQueues) && totalQueues.length > 0
      ? totalQueues[0].queue_num
      : ""
  );
  const dispatch = useDispatch();

  const [roomActivityId, setRoomActivityId] = useState(null);
  const [openTaskAdvanced, setOpenTaskAdvanced] = useState(false);

  // État pour gérer l'ordre des cartes
  const [cardOrder, setCardOrder] = useState(() => {
    try {
      const savedCardOrder = localStorage.getItem("dashboard_card_order");
      return savedCardOrder
        ? JSON.parse(savedCardOrder)
        : [
            // "voip",
            // "queue",
            // "chat",
            // "rmc",
            // "ticket",
            // "deal",
            // "activities-tasks-row",
            // "email",
            // "families",
          ];
    } catch (error) {
      console.error("Error loading card order from localStorage:", error);
      return [
        // "voip",
        // "chat",
        // "rmc",
        // "ticket",
        // "deal",
        // "activities-tasks-row",
        // "email",
        // "families",
      ];
    }
  });
  const [rowOrder, setRowOrder] = useState(() => {
    try {
      const savedRowOrder = localStorage.getItem("dashboard_row_order");
      return savedRowOrder
        ? JSON.parse(savedRowOrder)
        : ["activities", "tasks"];
    } catch (error) {
      console.error("Error loading row order from localStorage:", error);
      return ["activities", "tasks"];
    }
  });
  const [activeId, setActiveId] = useState(null);
  const [dragDirection, setDragDirection] = useState(null); // 'vertical' ou 'horizontal'
  const [tasksColsOrder, setTasksColsOrder] = useState(() => {
    try {
      const savedTasksColsOrder = localStorage.getItem(
        "dashboard_tasks_cols_order"
      );
      return savedTasksColsOrder
        ? JSON.parse(savedTasksColsOrder)
        : ["tasks-cols-left", "tasks-cols-right"];
    } catch (error) {
      console.error(
        "Error loading tasks columns order from localStorage:",
        error
      );
      return ["tasks-cols-left", "tasks-cols-right"];
    }
  });
  const [now, setNow] = useState("");
  // const { isSleeping, lastWakeTime } = useWakeUpDetection({
  //   sleepThreshold: 5000, // 5 secondes
  //   onWakeUp: (wakeTime) => {
  //     if (now) {
  //       const dateFormat = user.location.date_format || "YYYY-MM-DD";
  //       const currentDate = dayjs(now, dateFormat);
  //       const yourDate = dayjs(end, dateFormat); // Remplacez yourDateToCompare par votre date à comparer
  //       console.log(currentDate, "---", yourDate);
  //       // Calculer la différence en jours
  //       const diffInDays = Math.abs(currentDate.diff(yourDate, "day"));
  //       console.log(diffInDays);
  //       // Vérifier si la différence est inférieure à 1 jour
  //       if (diffInDays === 1) {
  //         console.log(dayjs(end, dateFormat).add(1, "day").format(dateFormat));
  //         // localStorage.setItem(
  //         //   "dateDashboard",
  //         //   JSON.stringify({
  //         //     startDate: dayjs(start, dateFormat).add(1, "day"),
  //         //     endDate: dayjs(end, dateFormat).add(1, "day"),
  //         //   })
  //         // );
  //         // La date est à moins d'un jour de la date d'aujourd'hui
  //         console.log("La date est à moins d'un jour de la date d'aujourd'hui");
  //       }
  //     }

  //     console.log("🌅 PC réveillé à:", wakeTime.toLocaleTimeString());
  //     // Ici tu peux rafraîchir des données, reconnecter WebSocket, etc.
  //   },
  //   onSleep: () => {
  //     const dateFormat = user.location.date_format || "YYYY-MM-DD";
  //     const currentDate = dayjs().format(dateFormat);
  //     setNow(currentDate);
  //     console.log("😴 PC en veille détecté");
  //     // Ici tu peux sauvegarder des données, fermer des connexions, etc.
  //   },
  // });
  // Configuration des capteurs pour le drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const collisionDetectionStrategy = (args) => {
    const { active } = args;
    const activeData = active.data.current;

    // Pour les cartes activities/tasks dans la ligne
    if (
      activeData?.type === "card" &&
      (active.id === "activities" || active.id === "tasks")
    ) {
      setDragDirection("vertical"); // Forcer vertical pour ces cartes
      return closestCenter(args); // Permettre collision avec toutes les cartes
    }

    // Reste du code existant...
    if (activeData?.type === "row") {
      setDragDirection("vertical");
      return rectIntersection(args);
    }

    setDragDirection("vertical");
    return closestCenter(args);
  };

  // Initialisation de l'ordre des cartes
  useEffect(() => {
    const savedCardOrder = localStorage.getItem("dashboard_card_order");
    const cards = [];

    // Ajouter les cartes selon les permissions utilisateur
    cards.push("voip");

    if (totalQueues && Array.isArray(totalQueues) && totalQueues.length > 0) {
      cards.push("queue");
    }

    if (user?.access?.["chat"] === "1") {
      cards.push("chat");
    }

    if (
      (roles.includes(user?.role) || user?.access?.rmc === "1") &&
      channelsRmc.length > 0
    ) {
      cards.push("rmc");
    }
    if (user?.access?.["leads"] === "1") {
      cards.push("leads");
    }
    if (user?.access?.ticket === "1") {
      cards.push("ticket");
    }
    if (user?.access?.contact === "1") {
      cards.push("contacts");
    }
    if (user?.access?.deals === "1") {
      cards.push("deal");
    }

    // Ajouter la ligne spéciale comme un seul élément
    cards.push("activities-tasks-row");

    if (
      Array.isArray(user?.accounts_email) &&
      user?.accounts_email?.length > 0
    ) {
      cards.push("email");
    }

    cards.push("families");
    if (savedCardOrder && JSON.parse(savedCardOrder).length > 0) {
      return;
    } else setCardOrder(cards);
  }, [user, totalQueues, channelsRmc]);

  // Gestion du début du drag
  const handleDragStart = (event) => {
    setActiveId(event.active.id);
    setDragDirection(null);
  };

  // Fonction pour obtenir le composant de carte selon l'ID
  const getCardComponent = (cardId, extraProps = {}) => {
    switch (cardId) {
      case "voip":
        return <CardStatVoip key="voip" {...extraProps} />;

      case "queue":
        return totalQueues &&
          Array.isArray(totalQueues) &&
          totalQueues.length > 0 ? (
          <CardQueue2
            key="queue"
            start={start}
            end={end}
            selectedQueue={selectedQueue}
            setSelectedQueue={setSelectedQueue}
            backgroundImagecard={backgroundImagecard}
            {...extraProps}
          />
        ) : null;

      case "chat":
        return user?.access?.["chat"] === "1" ? (
          <CardStatChat key="chat" {...extraProps} />
        ) : null;

      case "rmc":
        return (roles.includes(user?.role) || user?.access?.rmc === "1") &&
          channelsRmc.length > 0 ? (
          <CardStatRmc key="rmc" start={start} end={end} {...extraProps} />
        ) : null;

      case "ticket":
        return user?.access?.ticket === "1" ? (
          <CardStatTicket
            key="ticket"
            data={statsTickets?.gauge}
            cardsTickets={statsTickets?.cards}
            {...extraProps}
          />
        ) : null;

      case "deal":
        return user?.access?.deals === "1" ? (
          <CardStatDeal key="deal" {...extraProps} />
        ) : null;

      // case "activities":
      //   return (
      //     <div
      //       key="activities"
      //       style={{ backgroundImage: backgroundImagecard }}
      //     >
      //       <CardStatActivitiesTypes {...extraProps} />
      //     </div>
      //   );

      case "tasks":
        return (
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={(event) => {
              const { active, over } = event;
              if (active.id !== over?.id) {
                setTasksColsOrder((prev) => {
                  const newOrder = arrayMove(
                    prev,
                    prev.indexOf(active.id),
                    prev.indexOf(over.id)
                  );
                  // Sauvegarder immédiatement
                  localStorage.setItem(
                    "dashboard_tasks_cols_order",
                    JSON.stringify(newOrder)
                  );
                  return newOrder;
                });
              }
            }}
          >
            <SortableContext
              items={tasksColsOrder}
              strategy={horizontalListSortingStrategy}
            >
              <Row gutter={[8, 8]}>
                {tasksColsOrder.map((colId) =>
                  colId === "tasks-cols-left" ? (
                    <DraggableCol
                      key="tasks-cols-left"
                      id="tasks-cols-left"
                      span={8}
                    >
                      <CardStatActivitiesTypes {...extraProps} />
                    </DraggableCol>
                  ) : (
                    <DraggableCol
                      key="tasks-cols-right"
                      id="tasks-cols-right"
                      span={16}
                    >
                      <Card
                        style={{
                          backgroundImage: backgroundImagecard,
                          padding: 0,
                        }}
                        styles={{
                          header: {
                            padding: "4px 6px 4px 40px",
                            minHeight: "auto",
                          },
                          body: { padding: 6 },
                        }}
                        title={
                          <div className="flex items-center justify-between">
                            {t("menu1.tasks")}{" "}
                            <GoTo
                              to={"3"}
                              title={t("menu1.tasks")}
                              navigate={navigate}
                              t={t}
                              user={user}
                            />
                          </div>
                        }
                        extra={
                          Array.isArray(tasks) && tasks.length > 1 ? (
                            <Button
                              size="small"
                              onClick={() => setOpenTaskAdvanced(true)}
                              type="primary"
                              icon={<PlusOutlined />}
                            >
                              {t("tasks.addQuickTask")}
                            </Button>
                          ) : null
                        }
                      >
                        {Array.isArray(tasks) && tasks.length > 0 ? (
                          <div className="relative">
                            <ListTasks2
                              list={tasks}
                              setRoomActivityId={setRoomActivityId}
                              roomActivityId={roomActivityId}
                              onDeleteTask={onDeleteTask}
                              onUpdateTask={onUpdateTask}
                            />
                            {tasks.length === 0 ? (
                              <div className="absolute bottom-0 left-0 right-0 flex justify-center">
                                <EmptyPage
                                  heroTitle={
                                    <span>
                                      {t("tasks.startCreateActivities")}{" "}
                                      {t("tasks.activities")}
                                    </span>
                                  }
                                  mainBtnTitle={t("tasks.addQuickTask")}
                                  handleMainBtnClick={() =>
                                    setOpenTaskAdvanced(true)
                                  }
                                />
                              </div>
                            ) : null}
                          </div>
                        ) : (
                          <div className="h-[300px]">
                            <EmptyPage
                              heroTitle={
                                <span>
                                  {/* {t("tasks.startCreateActivities")}{" "}
                                  {t("dashboard.activities")} */}
                                </span>
                              }
                              mainBtnTitle={t("tasks.addQuickTask")}
                              handleMainBtnClick={() =>
                                setOpenTaskAdvanced(true)
                              }
                            />{" "}
                          </div>
                        )}
                      </Card>
                    </DraggableCol>
                  )
                )}
              </Row>
            </SortableContext>
          </DndContext>
        );

      case "email":
        return Array.isArray(user?.accounts_email) &&
          user?.accounts_email?.length > 0 ? (
          <CardStatEmail key="email" {...extraProps} />
        ) : null;

      case "leads":
        return <CardStatConvertedLeadsToContacts key="leads" />;
      case "contacts":
        return <CardStatConvertedContactsToGuests key="leads" />;
      case "families":
        return (
          <CardStatFamilies
            key="families"
            start={start}
            end={end}
            {...extraProps}
          />
        );

      default:
        return null;
    }
  };

  // Fonction pour déterminer la taille des colonnes
  // Modifier la fonction getColSpan pour gérer les cartes sorties de la ligne
  const getColSpan = (cardId) => {
    // Si la carte est DANS la ligne activities-tasks, utiliser les tailles spécifiques
    if (rowOrder.includes(cardId)) {
      if (cardId === "activities") return 6;
      if (cardId === "tasks") return 24;
    }
    // Si la carte est HORS de la ligne (position indépendante)
    // Appliquer la règle : si > 12 = toute la ligne, sinon = 12
    if (cardId === "activities" && !rowOrder.includes(cardId)) {
      return 12; // 6 < 12, donc taille standard
    }

    if (cardId === "tasks" && !rowOrder.includes(cardId)) {
      return 24; // 18 > 12, donc toute la ligne
    }

    // Autres cartes - taille standard
    switch (cardId) {
      case "email":
        return user?.access?.rmc === "1" && channelsRmc.length > 0 ? 12 : 12;
      default:
        return 12;
    }
  };

  // // Sauvegarder l'ordre des cartes dans localStorage à chaque changement
  // useEffect(() => {
  //   if (cardOrder.length > 0) {
  //     localStorage.setItem("dashboard_card_order", JSON.stringify(cardOrder));
  //   }
  // }, [cardOrder]);

  // Sauvegarder l'ordre des lignes
  useEffect(() => {
    if (rowOrder.length > 0) {
      localStorage.setItem("dashboard_row_order", JSON.stringify(rowOrder));
    }
  }, [rowOrder]);

  // Sauvegarder l'ordre des colonnes de tâches
  useEffect(() => {
    localStorage.setItem(
      "dashboard_tasks_cols_order",
      JSON.stringify(tasksColsOrder)
    );
  }, [tasksColsOrder]);

  // Modifiez la fonction handleDragEnd pour gérer tous les cas
  const handleDragEnd = (event) => {
    const { active, over } = event;

    if (!over) {
      setActiveId(null);
      setDragDirection(null);
      return;
    }

    const activeData = active.data.current;

    // Si on déplace une ligne entière
    if (activeData?.type === "row") {
      if (
        (active.id === "activities" || active.id === "tasks") &&
        cardOrder.includes(over.id)
      ) {
        const draggedCard = active.id;

        // Retirer de rowOrder
        setRowOrder((prev) => {
          const newRow = prev.filter((item) => item !== draggedCard);

          // Ajouter à cardOrder à la position over
          setCardOrder((prevCard) => {
            let newCardOrder = prevCard.filter((item) => item !== draggedCard);
            const overIndex = newCardOrder.indexOf(over.id);
            newCardOrder.splice(overIndex, 0, draggedCard);

            // Si rowOrder devient vide ou 1 élément, supprimer la ligne
            if (newRow.length <= 1) {
              newCardOrder = newCardOrder.filter(
                (item) => item !== "activities-tasks-row"
              );
              if (newRow.length === 1) {
                newCardOrder.splice(overIndex + 1, 0, newRow[0]);
              }
            }

            // Sauvegarder immédiatement
            localStorage.setItem(
              "dashboard_card_order",
              JSON.stringify(newCardOrder)
            );
            return newCardOrder;
          });

          // Sauvegarder immédiatement
          localStorage.setItem(
            "dashboard_row_order",
            JSON.stringify(newRow.length > 1 ? newRow : [])
          );
          return newRow.length > 1 ? newRow : [];
        });
      } else if (active.id !== over.id && cardOrder.includes(over.id)) {
        setCardOrder((items) => {
          const newOrder = arrayMove(
            items,
            items.indexOf(active.id),
            items.indexOf(over.id)
          );
          // Sauvegarder immédiatement
          localStorage.setItem(
            "dashboard_card_order",
            JSON.stringify(newOrder)
          );
          return newOrder;
        });
      }
    }
    // Si on déplace une carte activities/tasks
    else if (activeData?.type === "card" && rowOrder.includes(active.id)) {
      const draggedCard = active.id;

      // Drag horizontal - réorganiser dans la ligne
      if (dragDirection === "horizontal" && rowOrder.includes(over.id)) {
        setRowOrder((items) => {
          const newOrder = arrayMove(
            items,
            items.indexOf(active.id),
            items.indexOf(over.id)
          );
          // Sauvegarder immédiatement
          localStorage.setItem("dashboard_row_order", JSON.stringify(newOrder));
          return newOrder;
        });
      }
      // Drag vertical - sortir de la ligne
      else if (dragDirection === "vertical" && cardOrder.includes(over.id)) {
        // Retirer la carte de rowOrder
        setRowOrder((prevRowOrder) => {
          const newRowOrder = prevRowOrder.filter(
            (item) => item !== draggedCard
          );

          // Ajouter la carte à cardOrder à la position désirée
          setCardOrder((prevCardOrder) => {
            // Retirer la carte si elle existe déjà
            let workingOrder = prevCardOrder.filter(
              (item) => item !== draggedCard
            );

            // Trouver l'index de la carte de destination
            const overIndex = workingOrder.indexOf(over.id);

            if (overIndex !== -1) {
              // Insérer la carte à la position désirée
              workingOrder.splice(overIndex, 0, draggedCard);
            } else {
              // Si la position n'est pas trouvée, ajouter à la fin
              workingOrder.push(draggedCard);
            }

            // Gérer la ligne activities-tasks
            if (newRowOrder.length <= 1) {
              // Si la ligne a 1 carte ou moins, la supprimer et ajouter la carte restante
              workingOrder = workingOrder.filter(
                (item) => item !== "activities-tasks-row"
              );
              if (newRowOrder.length === 1) {
                // Ajouter la carte restante après la carte draggée
                const draggedIndex = workingOrder.indexOf(draggedCard);
                workingOrder.splice(draggedIndex + 1, 0, newRowOrder[0]);
              }
            }

            // Sauvegarder immédiatement
            localStorage.setItem(
              "dashboard_card_order",
              JSON.stringify(workingOrder)
            );
            return workingOrder;
          });

          // Sauvegarder immédiatement
          localStorage.setItem(
            "dashboard_row_order",
            JSON.stringify(newRowOrder.length > 1 ? newRowOrder : [])
          );
          return newRowOrder.length > 1 ? newRowOrder : [];
        });
      }
    }
    // Drag d'une carte normale
    else {
      if (active.id !== over.id && cardOrder.includes(over.id)) {
        setCardOrder((items) => {
          const newOrder = arrayMove(
            items,
            items.indexOf(active.id),
            items.indexOf(over.id)
          );
          // Sauvegarder immédiatement
          localStorage.setItem(
            "dashboard_card_order",
            JSON.stringify(newOrder)
          );
          return newOrder;
        });
      }
    }

    setActiveId(null);
    setDragDirection(null);
  };

  // Rendu des cartes avec drag and drop
  const renderCards = () => {
    const cards = [];

    cardOrder.forEach((cardId) => {
      // Cas spécial pour la ligne activities/tasks
      if (cardId === "activities-tasks-row" && rowOrder.length > 0) {
        cards.push(
          <Col span={24} key="activities-tasks-row">
            <DraggableRowCard
              id="activities-tasks-row"
              isRow={true}
              rowId="activities-tasks-row"
              style={{
                position: "relative",
                paddingLeft: "32px",
                background: "white",
                borderRadius: "8px",
                // border: "1px solid #d9d9d9",
                padding: "16px 16px 16px 32px",
                marginBottom: "8px",
              }}
            >
              <SortableContext
                items={rowOrder}
                strategy={horizontalListSortingStrategy}
              >
                <Row gutter={[8, 8]}>
                  {rowOrder.map((rowCardId) => {
                    const component = getCardComponent(rowCardId);
                    if (!component) return null;

                    const span = rowCardId === "activities" ? 6 : 24;

                    return (
                      <Col span={span} key={rowCardId}>
                        <DraggableRowCard
                          id={rowCardId}
                          rowId="activities-tasks-row"
                          style={{
                            // border: "1px solid #e8e8e8",
                            borderRadius: "6px",
                            background: "white",
                          }}
                        >
                          {component}
                        </DraggableRowCard>
                      </Col>
                    );
                  })}
                </Row>
              </SortableContext>
            </DraggableRowCard>
          </Col>
        );
        return;
      }

      // Cartes normales ET cartes activities/tasks qui sont sorties de la ligne
      const component = getCardComponent(cardId);
      if (!component) return;

      // Déterminer la largeur appropriée pour chaque carte
      const span = getColSpan(cardId);

      cards.push(
        <Col span={span} key={cardId}>
          <DraggableCard id={cardId}>{component}</DraggableCard>
        </Col>
      );
    });

    return cards;
  };

  // Reste du code identique...
  useEffect(() => {
    if (start && end) {
      dispatch(
        getDataDashboard({
          start,
          end,
          departement_id: selectedDepRmc === "all" ? "" : selectedDepRmc,
          queue_num: selectedQueue,
        })
      );
    }
  }, [dispatch, start, end]);
  useEffect(() => {
    if (start && end)
      dispatch(getStatsTickets({ start, end, language: i18n.language }));
  }, [dispatch, start, end]);

  // Pour setQueueInDashboard
  useEffect(() => {
    if (start && end)
      dispatch(
        setQueueInDashboard({
          startDate: start,
          endDate: end,
          queue_num: selectedQueue || "",
          setLoad: totalQueues.length === 0 ? setLoad : () => {},
        })
      );
  }, [dispatch, start, end, selectedQueue]);

  // Pour setTasksInDashboard
  useEffect(() => {
    if (start && end)
      dispatch(setTasksInDashboard({ startDate: start, endDate: end }));
  }, [dispatch, start, end]);

  // Pour getStatsTasks
  useEffect(() => {
    if (start && end)
      dispatch(getStatsTasks({ startDate: start, endDate: end }));
  }, [dispatch, start, end]);

  // Pour getStatsChat
  useEffect(() => {
    if (start && end) dispatch(getStatsChat());
  }, [dispatch]);

  // Pour setStatsDeals
  useEffect(() => {
    if (start && end)
      dispatch(setStatsDeals({ startDate: start, endDate: end }));
  }, [dispatch, start, end]);
  useEffect(() => {
    if (start && end) dispatch(getLeads_contacts({ start, end }));
  }, [dispatch, start, end]);

  useEffect(() => {
    return () => {
      dispatch(setResetData());
    };
  }, []);

  const { task360 } = useSelector((state) => state.form);

  useEffect(() => {
    if (task360 && Object.keys(task360).length > 0) {
      addTask(task360);

      setTimeout(() => {
        dispatch(setTask360({}));
      }, 100);
    }
  }, [task360]);

  const addTask = (task) => {
    dispatch(addTaskInDashboard(task));
    dispatch({
      type: SET_STATS_TASKS_DASHBOARD,
      payload: statsTasks.map((el) =>
        el.id === task360?.tasks_type_id ? { ...el, count: el.count + 1 } : el
      ),
    });
  };

  const onDeleteTask = (task) => {
    dispatch(RemoveTaskInDashboard(task?.id));
    dispatch({
      type: SET_STATS_TASKS_DASHBOARD,
      payload: statsTasks.map((el) =>
        el.id === task?.tasks_type_id ? { ...el, count: el.count - 1 } : el
      ),
    });
  };

  const onUpdateTask = (task) => {
    dispatch(UpdateTasksInDashboard(task));
  };

  return (
    <div className="px-2 pb-12">
      <style jsx>{`
        .row-container {
          position: relative;
        }
        .row-container.dragging-row {
          z-index: 1000;
        }
        .row-drag-handle:hover {
          background: #e6f7ff;
          border-color: #1890ff;
          color: #1890ff;
        }
        .row-drag-handle:active {
          cursor: grabbing !important;
        }
        .ant-card-extra {
          display: flex;
          align-items: center;
        }
      `}</style>
      {!load ? (
        <>
          <DndContext
            sensors={sensors}
            collisionDetection={collisionDetectionStrategy}
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
          >
            <SortableContext
              items={[...cardOrder, "activities", "tasks"]} // Ajouter explicitement activities et tasks
              strategy={rectSortingStrategy}
            >
              <Row gutter={[8, 8]} className="pb-2">
                {renderCards()}
              </Row>
            </SortableContext>

            <DragOverlay>
              {activeId ? (
                <Card
                  style={{
                    transform: "rotate(3deg)",
                    boxShadow: "0 15px 35px rgba(0,0,0,0.2)",
                    border: "2px solid #1890ff",
                    minWidth:
                      activeId === "activities-tasks-row"
                        ? "100%"
                        : (activeId === "activities" || activeId === "tasks") &&
                          !rowOrder.includes(activeId)
                        ? "100%"
                        : "auto",
                    background: "white",
                  }}
                >
                  <div style={{ opacity: 0.9 }}>
                    {activeId === "activities-tasks-row" ? (
                      <Row gutter={[8, 8]}>
                        <Col span={8}>{getCardComponent("activities")}</Col>
                        <Col span={16}>{getCardComponent("tasks")}</Col>
                      </Row>
                    ) : (
                      getCardComponent(activeId)
                    )}
                  </div>
                </Card>
              ) : null}
            </DragOverlay>
          </DndContext>
          {/* <CardStatConvertedLeadsToContacts />
          <CardStatConvertedContactsToGuests /> */}
          {openTaskRoomDrawer ? (
            <TasksRoom key={roomActivityId} elementId={roomActivityId} />
          ) : null}
          <CreateTask
            open={openTaskAdvanced}
            setOpen={setOpenTaskAdvanced}
            mask={true}
            listVisio={false}
            fromVue360={false}
          />
        </>
      ) : (
        <DashboardLoadingSkeleton />
      )}
    </div>
  );
};

export default Home4;
