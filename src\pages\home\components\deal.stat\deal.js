import { URL_ENV } from "index";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { generateAxios } from "services/axiosInstance";
import { DonutChartWithDrillDown } from "../ChartsDashboard";

const DashboardDeals = ({ start, end }) => {
  const [statsByFamily, setStatsByFamily] = useState({});
  const { i18n } = useTranslation("common");

  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await generateAxios(
          `${URL_ENV?.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
        ).post(`/get_stat_family`, {
          date_start: start,
          date_end: end,
          lang: i18n.language,
          family_id: 3,
        });

        setStatsByFamily({
          ...res.data.data,
          drilldown: {
            series: Object.keys(res.data.data.drilldown).map((pipeline) => ({
              id: pipeline,
              data: res.data.data.drilldown[pipeline].data, // Drilldown data
            })),
          },
        });
      } catch (err) {}
    };
    fetchData();
  }, [start, end, i18n.language]);
  return (
    <div>
      <DonutChartWithDrillDown data={statsByFamily} />{" "}
    </div>
  );
};

export default DashboardDeals;
