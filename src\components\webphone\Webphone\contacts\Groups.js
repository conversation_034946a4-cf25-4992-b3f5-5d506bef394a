import { Fragment, useEffect, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import useActionCall from "../../../../pages/voip/helpers/ActionCall";
import { getGroups } from "../../../../pages/voip/services/services";
import { toastNotification } from "../../../ToastNotification";
import { Button, Divider, Empty, Skeleton, Space, Tooltip } from "antd";
import { RenderMembers } from "../../../../pages/voip/groups/Groups";
import { PhoneOutlined } from "@ant-design/icons";
import DisplayAvatar from "../../../../pages/voip/components/DisplayAvatar";
import { HighlightSearchW } from "pages/voip/components";
import { URL_ENV } from "index";
import { ImUsers } from "react-icons/im";
import { FaUsers } from "react-icons/fa";

const Groups = ({ search }) => {
  //
  const [t] = useTranslation("common");
  const call = useActionCall();
  const { user } = useSelector(({ user }) => user);
  const poste = `${user?.extension}`;
  //
  const [dataSource, setDataSource] = useState([]);
  const [isLoadingData, setIsLoadingData] = useState(true);
  //
  // console.log({ dataSource });
  //
  const fetchGroups = async () => {
    try {
      setIsLoadingData(true);
      const { data } = await getGroups();
      setDataSource(
        data?.map((group) => ({
          ...group,
          extension: `${group.extension}`,
          name: group?.name?.replaceAll("_", " "),
          // name: "BU Comunik VoiP Commercial BU Comunik VoiP",
          members: group?.members?.map((member) => ({
            ...member,
            image: member?.image ? buildImageUrl(member.image) : null,
          })),
        }))
      );
    } catch (err) {
      toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? err.message : err);
    } finally {
      setIsLoadingData(false);
    }
  };

  useEffect(() => {
    fetchGroups();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  //
  function buildImageUrl(imageName) {
    const baseUrl = URL_ENV?.REACT_APP_BASE_URL ?? "default_base_url";
    const suffixUrl =
      URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL ?? "default_suffix_url";
    return `${baseUrl}${suffixUrl}${imageName}`;
  }
  //
  const modifiedSearch = useMemo(
    () =>
      (/^\+?[0-9]+$/.test(search)
        ? search.replace("+", "00")
        : search
      )?.toLowerCase(),
    [search]
  );
  //
  return (
    <div
      id="scrollableDiv"
      style={{
        height: "18rem",
        overflow: "auto",
        padding: "4px 4px",
      }}
    >
      {isLoadingData ? (
        <div className="mt-4 px-1">
          <Skeleton active avatar paragraph={{ rows: 1 }} />
          <Skeleton active avatar paragraph={{ rows: 1 }} />
          <Skeleton active avatar paragraph={{ rows: 1 }} />
        </div>
      ) : !isLoadingData && dataSource?.length === 0 ? (
        <div className="flex justify-center p-4 text-sm font-semibold">
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={"No groups Found"}
          />
        </div>
      ) : (
        dataSource
          ?.filter(
            (element) =>
              element?.name?.toLowerCase()?.includes(modifiedSearch) ||
              element?.extension?.toLowerCase()?.includes(modifiedSearch)
          )
          ?.map((group, i) => (
            <Fragment key={i}>
              <div
                key={i}
                className=" flex flex-row  justify-between rounded-md  p-0.5 "
              >
                <div className="flex flex-row justify-between">
                  <Space>
                    <DisplayAvatar
                      size={40}
                      name={group?.name}
                      icon={
                        group?.queue ? (
                          <ImUsers />
                        ) : group?.group ? (
                          <FaUsers />
                        ) : null
                      }
                    />
                    <div className="w-[210px]">
                      <p className=" truncate font-semibold leading-5">
                        {group?.name?.length > 30 ? (
                          <Tooltip title={group?.name}>
                            {group?.name?.slice(0, 30)}...
                          </Tooltip>
                        ) : (
                          HighlightSearchW(group?.name, search)
                        )}
                      </p>
                      <Space>
                        <p
                          style={{ fontSize: 14 }}
                          className="truncate leading-5 text-slate-500 "
                        >
                          {HighlightSearchW(group?.extension, search)}
                        </p>
                        {/* <div className="grid items-center"> */}
                        <RenderMembers
                          key={i}
                          members={group?.members}
                          call={call}
                          poste={poste}
                          maxCount={5}
                          avatarSize={20}
                          popoverTrigger="hover"
                        />
                        {/* </div> */}
                      </Space>
                    </div>
                  </Space>
                </div>
                <div className="grid items-center">
                  <Tooltip
                    title={group?.is_member ? t("voip.alreadyMember") : null}
                  >
                    <Button
                      type="link"
                      disabled={group?.is_member}
                      onClick={() =>
                        !group?.is_member && call(group?.extension)
                      }
                      icon={
                        <PhoneOutlined rotate={100} style={{ fontSize: 18 }} />
                      }
                    />
                  </Tooltip>
                </div>
              </div>
              <Divider style={{ margin: "4px 0" }} />
            </Fragment>
          ))
      )}
    </div>
  );
};

export default Groups;
