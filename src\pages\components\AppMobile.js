import React, { useEffect, useState } from "react";
import headerImage from "../../assets/headerAppMobile.jpg";
import QrCodeIos from "../../assets/comunik_ios_qr.webp";
import QrCodeAndroid from "../../assets/comunik_android_qr.webp";

import appStore from "../../assets/appStore.svg";
import playStore from "../../assets/playStore.svg";
import logoComunikUnified from "../../assets/logoComunikUnified.png";
import { useTranslation } from "react-i18next";
import {
  Avatar,
  Button,
  Card,
  Col,
  Divider,
  Image,
  Row,
  Segmented,
  Skeleton,
  Space,
  Tabs,
  Tooltip,
  Typography,
} from "antd";
import { useSelector } from "react-redux";
import { URL_ENV } from "index";
import {
  AppleOutlined,
  InfoCircleOutlined,
  LoginOutlined,
  QrcodeOutlined,
  ReloadOutlined,
} from "@ant-design/icons";
import { generateAxios } from "services/axiosInstance";
import { Play } from "lucide-react";
const circleStyle = {
  height: "20px",
  width: "20px",
  backgroundColor: "#60a5fa",
  borderRadius: "50%",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  color: "white",
};
const AppMobile = () => {
  const [t] = useTranslation("common");
  const { user } = useSelector((state) => state.user);
  const [qRCodeImage, setQRCodeImage] = useState("");
  const [loading, setLoading] = useState(false);
  const [store, setStore] = useState("1");

  const onChange = (key) => {
    if (key === "2" && !qRCodeImage) {
      getQrcode();
    }
  };

  const getQrcode = async () => {
    setLoading(true);
    try {
      const response = await generateAxios(
        process.env.REACT_APP_DEFAULT_AUTH_DOMAIN +
          process.env.REACT_APP_SUFFIX_API
      ).get(`/mobile/generate-qr-code`, { responseType: "blob" });
      // Créez une URL locale pour le Blob
      const imageUrl = URL.createObjectURL(response.data);

      setQRCodeImage(imageUrl);
      setLoading(false);
    } catch (e) {
      setLoading(false);
    }
  };
  const items = [
    {
      key: "1",
      label: t("profilemenu.loginEmailPassword"),
      icon: <LoginOutlined />,
      children: (
        <Row gutter={[16, 16]} className="mt-2">
          <Col className="gutter-row" span={9}>
            <div className="flex flex-col gap-y-2">
              <div className="flex items-center ">
                <span style={circleStyle}>1</span>
                <span className="pl-1 text-xs font-medium">
                  {t("profilemenu.scanCode")}
                </span>
              </div>
              <div>
                <Segmented
                  shape="round"
                  onChange={setStore}
                  value={store}
                  options={[
                    {
                      value: "1",
                      icon: <Play size={14} className="relative top-0.5" />,
                      label: "Google Play",
                    },
                    { value: "2", icon: <AppleOutlined />, label: "App Store" },
                  ]}
                />
              </div>
              <div className="flex items-center  ">
                {/* <Divider type="vertical" style={{ minHeight: "40px" }} /> */}
                <div className="flex flex-col gap-y-3  pl-3">
                  {store === "1" ? (
                    <div className="flex items-center gap-x-2">
                      <Image src={QrCodeAndroid} width={110} />
                      <a
                        href="https://play.google.com/store/apps/details?id=db.unified.chat_voip&hl=fr"
                        target="_blank"
                        rel="noreferrer"
                      >
                        <img width={110} src={playStore} alt="" />
                      </a>
                    </div>
                  ) : (
                    <div className="flex items-center gap-x-2">
                      <Image src={QrCodeIos} width={110} />
                      <a
                        href="https://apps.apple.com/tn/app/comunik-unified/id6739617673?l=fr-FR"
                        target="_blank"
                        rel="noreferrer"
                      >
                        <img width={110} src={appStore} alt="" />
                      </a>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </Col>
          <Col className="gutter-row" span={6}>
            <div className="flex flex-col gap-y-2">
              <div className="flex items-center ">
                <span style={circleStyle}>2</span>
                <span className="pl-1  font-medium">
                  {t("profilemenu.EnterTenancy")}
                </span>
              </div>
              <div className="appMobile-input-container">
                <label className="appMobile-input-label">
                  {t("dashboard.tenancy")}
                </label>
                <input
                  type="text"
                  className="appMobile-input-field"
                  value={URL_ENV?.REACT_APP_DOMAIN?.replace(/^https?:\/\//, "")}
                  readOnly
                />
              </div>
            </div>
          </Col>
          <Col className="gutter-row " span={9}>
            <div className="flex flex-col gap-y-2">
              <div className="flex items-center ">
                <span style={circleStyle}>3</span>
                <span className="pl-1  font-medium">
                  {t("profilemenu.fillFieldsAppMobile")}
                </span>
              </div>
              <div className="appMobile-input-container">
                <label className="appMobile-input-label">Email</label>
                <input
                  type="text"
                  className="appMobile-input-field"
                  value={user?.email}
                  readOnly
                />
              </div>
              <div className="appMobile-input-container">
                <label className="appMobile-input-label">
                  {t("emailAccounts.password")}{" "}
                  <Tooltip title={t("profilemenu.usePassword")}>
                    <InfoCircleOutlined />
                  </Tooltip>
                </label>
                <input
                  type="password"
                  className="appMobile-input-field"
                  value=""
                  readOnly
                />
              </div>
            </div>
          </Col>
        </Row>
      ),
    },
    {
      key: "2",
      label: t("profilemenu.loginqrCode"),
      icon: <QrcodeOutlined />,
      children: (
        <div className=" mt-2 flex items-center">
          <div className="flex flex-col gap-y-2">
            <div className="flex items-center ">
              <span className="pl-1 text-xs font-medium">
                {t("profilemenu.scanCodeAndConnect")}
              </span>
            </div>
            {qRCodeImage ? (
              <Space>
                {loading ? (
                  <Skeleton.Image
                    active={loading}
                    style={{ width: 110, height: 110 }}
                  />
                ) : (
                  <Image width={110} src={qRCodeImage} alt="QR Code" />
                )}
                {qRCodeImage ? (
                  <Tooltip
                    title={t("profilemenu.ReceiveQrCode")}
                    placement="right"
                  >
                    <Button
                      icon={<ReloadOutlined />}
                      onClick={getQrcode}
                      loading={loading}
                    />
                  </Tooltip>
                ) : null}
              </Space>
            ) : (
              <Skeleton.Image
                active={loading}
                style={{ width: 110, height: 110 }}
              />
            )}
          </div>
        </div>
      ),
    },
  ];

  return (
    <div className="jus flex w-full flex-col items-center py-3">
      <Card
        style={{
          width: 1200,
          //   width: "100%",
        }} // hoverable
        cover={
          <Image
            alt="example"
            src={headerImage}
            width={1200}
            height={250}
            preview={false}
            loading="lazy"
          />
        }
      >
        <div className="flex items-center gap-x-2">
          <Avatar size={50} src={logoComunikUnified} alt="" />
          <span>
            <Typography.Title level={3}>Comunik unified</Typography.Title>

            <Typography.Text>
              {t("profilemenu.paragraphAppMobile")}
            </Typography.Text>
          </span>
        </div>
        <Divider style={{ margin: "16px 0 0 0" }} />
        <div className="pt-[5px]">
          <Tabs
            tabPosition="left"
            defaultActiveKey="1"
            items={items}
            onChange={onChange}
          />
        </div>
      </Card>
    </div>
  );
};

export default AppMobile;
