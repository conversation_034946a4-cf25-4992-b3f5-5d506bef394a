import { memo, useMemo, useRef, useState } from "react";
import { Badge, Button, Popover, Select, Spin, Tooltip } from "antd";
import { CloseCircleOutlined, LoadingOutlined } from "@ant-design/icons";
import { toastNotification } from "components/ToastNotification";
import { URL_ENV } from "index";
import DisplayAvatar from "pages/voip/components/DisplayAvatar";
import MainService from "services/main.service";
import { handleDataUsersToAssign } from "../helpers/helpers";

export const AssignMail = memo(
  ({
    owner,
    usedAccount,
    user,
    onlineUser,
    id,
    t,
    setData,
    transfer,
    dataUsers,
  }) => {
    //
    const selectRef = useRef();
    //
    const [loading, setLoading] = useState(false);
    const [isHovered, setIsHovered] = useState(false);
    //
    const isDispatcher = useMemo(
      () => usedAccount?.dispatcheur?.includes(user.id),
      [usedAccount?.dispatcheur, user.id]
    );
    const isFromDepartment = useMemo(
      () => usedAccount?.departmentId?.includes(user?.department_id),
      [usedAccount?.departmentId, user?.department_id]
    );
    //
    const formattingSharedAccountUser = useMemo(
      () => handleDataUsersToAssign(dataUsers, user, onlineUser, t),
      [dataUsers, user, onlineUser, t]
    );
    //
    const handleHovering = (isHover) => {
      if (isHover) {
        if (
          owner.owner === user?.id ||
          (owner.user_id === user?.id && isDispatcher) ||
          isDispatcher
        ) {
          setIsHovered(true);
        }
      } else {
        isHovered && setIsHovered(false);
      }
    };
    //
    const deleteAssign = async () => {
      try {
        setLoading(true);
        const formData = new FormData();
        formData.append("email_id", id);
        formData.append("user_id", owner?.user_id);
        formData.append("owner", owner?.owner);
        formData.append("account_id", usedAccount.value);
        usedAccount?.departmentId?.length &&
          usedAccount?.departmentId?.forEach((e) =>
            formData.append("departement_id[]", e)
          );
        await MainService.deleteAssignEmail(formData);
        setData((prev) =>
          prev.map((mail) =>
            mail.id === id
              ? {
                  ...mail,
                  owner: [],
                }
              : mail
          )
        );
      } catch (err) {
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
        throw new Error(err?.message ? `error: ${err.message}` : { err });
      } finally {
        setLoading(false);
      }
    };
    //
    const handleAssignUser = async (userId) => {
      try {
        setLoading(true);

        const formData = new FormData();
        formData.append("id_email", id);
        formData.append("owner", userId);
        formData.append("account_id", usedAccount.value);
        for (let i = 0; i < usedAccount.departmentId.length; i++) {
          formData.append("departement_id[]", usedAccount.departmentId[i]);
        }
        for (let i = 0; i < usedAccount?.dispatcheur?.length; i++) {
          formData.append("dispatcheur[]", usedAccount.dispatcheur[i]);
        }
        const {
          data: { data },
        } = await MainService.assignEmail(formData);
        const resp = data?.[0];
        const newOwner = { ...resp, avatar: resp?.avatar?.path };
        setData((prev) =>
          prev.map((mail) =>
            mail.id === id
              ? {
                  ...mail,
                  owner: newOwner,
                }
              : mail
          )
        );
      } catch (err) {
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
        throw new Error(err?.message ? `error: ${err.message}` : { err });
      } finally {
        setLoading(false);
      }
    };
    //
    if (transfer) {
      return (
        <div
          onClick={(e) => e.stopPropagation()}
          className="flex items-center justify-center"
        >
          <Tooltip title={t("mailing.Tooltip.TransferedNoAssign")}>
            <Button size="small" type="dashed" disabled={true}>
              {isDispatcher ? t("mailing.Assign") : t("mailing.assignToMe")}
            </Button>
          </Tooltip>
        </div>
      );
    }

    if (!isFromDepartment) {
      return (
        <div
          onClick={(e) => e.stopPropagation()}
          className="flex items-center justify-center"
        >
          {owner ? (
            <Badge
              dot
              offset={[-3, 23]}
              color={
                onlineUser[owner?.uuid] === "away"
                  ? "orange"
                  : onlineUser[owner?.uuid] === "busy"
                  ? "red"
                  : onlineUser[owner?.uuid] === "online"
                  ? "green"
                  : "#a6a6a6"
              }
              styles={{
                indicator: { height: 7, width: 7 },
              }}
            >
              <DisplayAvatar
                size={26}
                tooltip={owner.owner === user?.id ? "me" : true}
                cursor="help"
                name={owner?.label_data}
                urlImg={
                  owner?.avatar &&
                  `${
                    URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                  }${owner?.avatar}`
                }
              />
            </Badge>
          ) : (
            <Tooltip title={t("mailing.Tooltip.cannotAssignNotFromDepartment")}>
              <Button size="small" type="dashed" disabled={true}>
                {isDispatcher ? t("mailing.delete") : t("mailing.Assign")}
              </Button>
            </Tooltip>
          )}
        </div>
      );
    }

    if (isDispatcher) {
      //
      const dispatcherPopover = (
        <Select
          ref={selectRef}
          showSearch
          style={{
            width: 240,
          }}
          defaultOpen={true}
          optionFilterProp={["searchOption"]}
          filterOption={(input, option) =>
            (option?.searchOption ?? "")
              .toLowerCase()
              .includes(input.toLowerCase())
          }
          options={formattingSharedAccountUser}
          onSelect={(value) => handleAssignUser(value)}
        />
      );
      //
      return (
        <div
          onClick={(e) => e.stopPropagation()}
          className="flex items-center justify-center"
        >
          {loading ? (
            <Spin indicator={<LoadingOutlined spin />} />
          ) : !owner ? (
            <Popover
              arrow={false}
              title={null}
              trigger="click"
              placement="bottom"
              content={dispatcherPopover}
              onOpenChange={(visible) => {
                if (visible) {
                  setTimeout(() => {
                    selectRef.current?.focus();
                  }, 50);
                }
              }}
              overlayInnerStyle={{ padding: 6 }}
            >
              <Button size="small" type="dashed">
                {t("mailing.Assign")}
              </Button>
            </Popover>
          ) : (
            <div
              className="flex items-center justify-center"
              onMouseEnter={() => handleHovering(true)}
              onMouseLeave={() => handleHovering(false)}
            >
              <Popover
                arrow={false}
                title={null}
                trigger="click"
                placement="bottom"
                content={dispatcherPopover}
                onOpenChange={(visible) => {
                  if (visible) {
                    setTimeout(() => {
                      selectRef.current?.focus();
                    }, 50);
                  }
                }}
                overlayInnerStyle={{ padding: 6 }}
              >
                <Badge
                  count={
                    isHovered ? (
                      <Button
                        size="small"
                        type="link"
                        shape="circle"
                        danger
                        onClick={deleteAssign}
                        icon={<CloseCircleOutlined style={{ fontSize: 15 }} />}
                      />
                    ) : (
                      0
                    )
                  }
                >
                  <Badge
                    dot
                    offset={[-3, 23]}
                    color={
                      onlineUser[owner?.uuid] === "away"
                        ? "orange"
                        : onlineUser[owner?.uuid] === "busy"
                        ? "red"
                        : onlineUser[owner?.uuid] === "online"
                        ? "green"
                        : "#a6a6a6"
                    }
                    styles={{
                      indicator: { height: 7, width: 7 },
                    }}
                  >
                    <DisplayAvatar
                      size={26}
                      tooltip={owner.owner === user?.id ? "me" : true}
                      cursor="pointer"
                      name={owner?.label_data}
                      urlImg={
                        owner?.avatar &&
                        `${
                          URL_ENV?.REACT_APP_BASE_URL +
                          URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                        }${owner?.avatar}`
                      }
                    />
                  </Badge>
                </Badge>
              </Popover>
            </div>
          )}
        </div>
      );
    } else {
      //
      const nonDispatcherPopover =
        owner?.owner === user?.id ? null : (
          <Button
            // type="dashed"
            size="small"
            onClick={() => handleAssignUser(user?.id)}
          >
            {t("mailing.takeOver")}
          </Button>
        );
      //
      return (
        <div
          onClick={(e) => e.stopPropagation()}
          className="flex items-center justify-center"
        >
          {loading ? (
            <Spin indicator={<LoadingOutlined spin />} />
          ) : !owner ? (
            <Button
              type="dashed"
              size="small"
              onClick={() => handleAssignUser(user?.id)}
            >
              {t("mailing.assignToMe")}
            </Button>
          ) : (
            <div
              className="flex items-center justify-center"
              onMouseEnter={() => handleHovering(true)}
              onMouseLeave={() => handleHovering(false)}
            >
              <Popover
                title={null}
                arrow={false}
                trigger="click"
                placement="bottom"
                content={nonDispatcherPopover}
                overlayInnerStyle={{ padding: 6 }}
              >
                <Badge
                  key="delete"
                  count={
                    isHovered ? (
                      <Button
                        size="small"
                        type="link"
                        shape="circle"
                        danger
                        onClick={deleteAssign}
                        icon={<CloseCircleOutlined style={{ fontSize: 15 }} />}
                      />
                    ) : (
                      0
                    )
                  }
                >
                  <Badge
                    key="status"
                    dot
                    offset={[-3, 23]}
                    color={
                      onlineUser[owner?.uuid] === "away"
                        ? "orange"
                        : onlineUser[owner?.uuid] === "busy"
                        ? "red"
                        : onlineUser[owner?.uuid] === "online"
                        ? "green"
                        : "#a6a6a6"
                    }
                    styles={{
                      indicator: { height: 7, width: 7 },
                    }}
                  >
                    <DisplayAvatar
                      size={26}
                      tooltip={
                        owner.owner === user?.id || owner.user_id === user?.id
                          ? t("voip.me")
                          : true
                      }
                      cursor={owner?.owner === user?.id ? "default" : "pointer"}
                      name={owner?.label_data}
                      urlImg={
                        owner?.avatar &&
                        `${
                          URL_ENV?.REACT_APP_BASE_URL +
                          URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                        }${owner?.avatar}`
                      }
                    />
                  </Badge>
                </Badge>
              </Popover>
            </div>
          )}
        </div>
      );
    }
  }
);
