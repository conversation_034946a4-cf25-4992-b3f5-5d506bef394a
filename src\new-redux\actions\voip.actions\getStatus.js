import { GET_STATUS_SUCCESS, GET_STATUS_ERROR, CHANGE_STATUS } from "../../constants";
import MainService from "../../../services/main.service";
export const getStatus = (poste) => async (dispatch) => {
    try {

        const response = await MainService.statusApiIPBX(poste);
        dispatch({
            type: GET_STATUS_SUCCESS,
            payload: response?.data.collegues,
        });
    } catch (error) {
        dispatch({
            type: GET_STATUS_ERROR,
            payload: error,
        });
    }
};

export const changeStatusVoip = (status, poste) => {
    return {
        type: CHANGE_STATUS,
        payload: [status, poste],
    };
};


