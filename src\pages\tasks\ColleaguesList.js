/**
 * @name ColleaguesList
 *
 * @description `ColleaguesList` component is responsible for displaying the list of colleagues (in drawer and modal).
 *
 * @param {Array} usersList The list of users/colleagues (members of the family 4)
 * @param {String} source The source, from where, the component is being called
 * @param {String} searchQuery
 * @param {Array} checkedItems
 * @param {Function} setFollowersSearchQuery
 * @param {Function} displayFollowersList
 * @param {Function} setCheckedItems
 * @param {Function} handleCheckedItems Handle check users from the list.
 * @param {Boolean} loading Show/hide loader indicator.
 * @param {Function} dispatchAddMembers Trigger add guests/owner API in activity.
 * @param {Function} dispatchRemoveMembers Trigger remove guests/owner API from activity.
 * @param {String} defaultOwner The id of the default owner.
 *
 * @returns {JSX.Element} Dropdown with the options of calling or sending a message to user.
 */

import { forwardRef, useRef, useCallback } from "react";
import {
  InfoCircleOutlined,
  MailOutlined,
  PhoneOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { Checkbox, Divider, Input, List, Spin, Tag, Tooltip } from "antd";
import { useTranslation } from "react-i18next";

import { AvatarChat } from "components/Chat";
import { URL_ENV } from "index";
import { getName } from "pages/layouts/chat/utils/ConversationUtils";
import { EXTENSIONS_ARRAY } from "./helpers/calculateSum";
import InfiniteScroll from "react-infinite-scroll-component";
import { HighlightSearchW } from "pages/voip/components";

const ColleaguesList = forwardRef(
  (
    {
      usersList,
      source,
      searchQuery,
      checkedItems,
      setFollowersSearchQuery,
      setCheckedItems,
      handleCheckedItems,
      loading,
      dispatchAddMembers = () => {},
      dispatchRemoveMembers = () => {},
      defaultOwner = null,
      totalEntities,
      setCurrentPage,
      from = "",
    },
    ref
  ) => {
    const colleaguesInputRef = useRef(null);
    const scrollListRef = useRef(null);
    const [t] = useTranslation("common");

    // Handle checkbox item click
    const handleClickOnItem = useCallback(
      (e) => {
        colleaguesInputRef.current.focus({ cursor: "all" });
        const { value, checked } = e.target;
        handleCheckedItems(
          usersList,
          value,
          checked,
          checkedItems,
          setCheckedItems
        );
        if (checked) {
          dispatchAddMembers(3, value);
        } else {
          dispatchRemoveMembers(3, value);
        }
      },
      [
        colleaguesInputRef,
        handleCheckedItems,
        usersList,
        checkedItems,
        setCheckedItems,
        dispatchAddMembers,
        dispatchRemoveMembers,
      ]
    );

    // Handle search input changes.
    const handleSearchInputChange = (e) => {
      setFollowersSearchQuery(e.target.value);
    };

    // Handle increment page number.
    const handleIncrementNextPage = () => setCurrentPage((prev) => prev + 1);
    console.log(defaultOwner);
    return (
      <>
        {/* Search input */}
        <Input
          placeholder={t("activities.search")}
          prefix={<SearchOutlined />}
          onChange={handleSearchInputChange}
          value={searchQuery}
          allowClear
          style={{ marginBottom: "5px" }}
          ref={colleaguesInputRef}
          suffix={
            <Tooltip title={t("tasks.listSearchPlaceholder")}>
              <InfoCircleOutlined style={{ color: "#1890ff" }} />
            </Tooltip>
          }
        />
        {/* List pagination indicator */}
        {usersList?.length > 0 && (
          <Divider plain>
            {`1-${usersList?.length} ${t("mailing.of")} ${
              usersList?.length > totalEntities?.colleagues
                ? usersList?.length
                : totalEntities?.colleagues
            }`}
          </Divider>
        )}
        {/* Scrollable users list */}
        <div
          id="scrollableFollowersDiv"
          style={{
            overflow: "auto",
            width: from === "columnTable" ? 350 : 400,
            maxHeight: from === "columnTable" ? 150 : 400,
            padding: "0",
          }}
          ref={ref}
        >
          <Spin spinning={loading}>
            <InfiniteScroll
              id="scrollableFollowersList"
              scrollThreshold="91%"
              dataLength={usersList?.length || 0}
              hasMore={usersList?.length < totalEntities?.colleagues}
              next={handleIncrementNextPage}
              height={from === "columnTable" ? 150 : 400}
              scrollableTarget="scrollableFollowersDiv"
              endMessage={
                usersList?.length > 0 && (
                  <Divider plain>{t("tasks.endListIndicator")}</Divider>
                )
              }
            >
              <List
                className="membersList"
                dataSource={usersList}
                renderItem={(item, index) => {
                  const avatarUrl = `${URL_ENV?.REACT_APP_BASE_URL}${URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL}${item?.avatar}`;
                  const isAvatarImage = EXTENSIONS_ARRAY.includes(
                    item?.avatar?.split(".")?.pop()
                  );

                  if (source === "followersList") {
                    const isChecked = checkedItems?.some(
                      (el) => el?.id?.toString() === item?.id?.toString()
                    );
                    return (
                      <List.Item
                        key={item?.id}
                        id={`listItem-${index}`}
                        ref={index === 0 ? scrollListRef : null}
                      >
                        <Checkbox
                          value={item?.id}
                          onChange={handleClickOnItem}
                          checked={isChecked}
                          style={{ width: "100%" }}
                        >
                          <div
                            style={{
                              display: "flex",
                              alignItems: "center",
                              width: "100%",
                            }}
                          >
                            <AvatarChat
                              fontSize="0.7rem"
                              className="mx-1.5"
                              size={32}
                              height={14}
                              width={14}
                              url={avatarUrl}
                              hasImage={isAvatarImage}
                              name={getName(item?.label, "avatar")}
                              type="user"
                            />
                            <div>
                              {HighlightSearchW(item?.label, searchQuery)}
                              <p className="text-[12px] text-[#475569]">
                                {item?.email && (
                                  <span>
                                    <MailOutlined
                                      style={{ marginRight: "3px" }}
                                    />
                                    {HighlightSearchW(item?.email, searchQuery)}
                                  </span>
                                )}
                                {item?.extension && (
                                  <span className="ml-2">
                                    <PhoneOutlined
                                      style={{ marginRight: "3px" }}
                                    />
                                    {HighlightSearchW(
                                      `${item?.extension}`,
                                      searchQuery
                                    )}
                                  </span>
                                )}
                              </p>
                            </div>
                          </div>
                        </Checkbox>
                      </List.Item>
                    );
                  } else {
                    return (
                      <List.Item
                        key={item?.id}
                        id={`listItem-${index}`}
                        style={{
                          cursor: "pointer",
                        }}
                        onClick={() => dispatchAddMembers(1, item?.id)}
                        ref={index === 0 ? scrollListRef : null}
                        extra={
                          defaultOwner === item?.id ? (
                            <Tag bordered={false} color="magenta">
                              {t("tasks.owner")}
                            </Tag>
                          ) : null
                        }
                      >
                        <div
                          style={{
                            display: "flex",
                            alignItems: "center",
                            width: "100%",
                          }}
                        >
                          <AvatarChat
                            fontSize="0.7rem"
                            className="mx-1.5"
                            size={32}
                            height={14}
                            width={14}
                            url={avatarUrl}
                            hasImage={isAvatarImage}
                            name={getName(item?.label, "avatar")}
                            type="user"
                          />
                          <p>
                            {HighlightSearchW(item?.label, searchQuery)}
                            <p className="text-[12px] text-[#475569]">
                              {item?.email && (
                                <span>
                                  <MailOutlined
                                    style={{ marginRight: "3px" }}
                                  />
                                  {HighlightSearchW(item?.email, searchQuery)}
                                </span>
                              )}
                              {item?.extension && (
                                <span className="ml-2">
                                  <PhoneOutlined
                                    style={{ marginRight: "3px" }}
                                  />
                                  {HighlightSearchW(
                                    `${item?.extension}`,
                                    searchQuery
                                  )}
                                </span>
                              )}
                            </p>
                          </p>
                        </div>
                      </List.Item>
                    );
                  }
                }}
              />
            </InfiniteScroll>
          </Spin>
        </div>
      </>
    );
  }
);

export default ColleaguesList;
