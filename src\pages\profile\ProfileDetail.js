import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import Header from "./components/profile-details-components/Header";
import FormUpdate from "../clients&users/components/FormUpdate";
import { useTranslation } from "react-i18next";
import { Descriptions, Skeleton, Tabs, Typography } from "antd";
import { useWindowSize } from "../clients&users/components/WindowSize";

import {
  getDataProfileDetails,
  getFieldsToCreate,
} from "../clients&users/services/services";
import { toastNotification } from "../../components/ToastNotification";
import useActionCall from "pages/voip/helpers/ActionCall";
import RenderDescriptionDetails from "pages/clients&users/components/RenderDescriptionDetails";

const ProfileDetail = () => {
  const [t] = useTranslation("common");
  const call = useActionCall();
  const { user } = useSelector((state) => state.user);
  const windowSize = useWindowSize();

  const [dataToDisplay, setDataToDisplay] = useState([]);
  const [activeTab, setActiveTab] = useState(null);

  const [isError, setIsError] = useState(false);

  const [openDrawerUpdate, setOpenDrawerUpdate] = useState(false);
  const [catchUpdate, setCatchUpdate] = useState(false);
  //
  // console.log({ dataToDisplay });
  //
  const getAllFieldsAndData = async () => {
    try {
      const fields = await getFieldsToCreate(4, "read", null, user?.id);
      const fieldsValues = await getDataProfileDetails();
      const labels = fields?.data?.data;
      const values = fieldsValues?.data;
      const result = [];
      labels?.forEach((group) => {
        result.push({
          id: group?.id,
          groupeName: group?.label,
          fields: group?.fields
            ?.filter(
              (el) =>
                el.field_type !== "password" &&
                el.label !== "uuid" &&
                !el.hidden
            )
            .map((el) => ({
              id: el?.id,
              label: el?.alias,
              value: values[el?.id] || "-",
              type: el?.field_type,
            })),
        });
      });
      setActiveTab(result[0]?.id);
      setDataToDisplay(result);
      setIsError(false);
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      setIsError(true);
      throw new Error(err?.message ? err.message : err);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  };

  useEffect(() => {
    if (user?.id) {
      setDataToDisplay([]);
      getAllFieldsAndData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user?.id, catchUpdate]);
  //

  return (
    <div className="relative p-2">
      <div className="space-y-4">
        <Header setOpenDrawerUpdate={setOpenDrawerUpdate} t={t} />
        <Skeleton
          // className="mt-12"
          loading={!dataToDisplay?.length && !isError}
          active
        >
          <Tabs
            type="card"
            activeKey={activeTab}
            onChange={(key) => setActiveTab(Number(key))}
            style={{ padding: "0 0 0 0.5rem" }}
            items={dataToDisplay?.map((group) => {
              return {
                label: group?.groupeName,
                key: group?.id,
                children: (
                  <div
                    className="overflow-content overflow-x-hidden"
                    style={{ maxHeight: `${windowSize.height - 340}px` }}
                  >
                    <Descriptions
                      colon={false}
                      column={4}
                      key={group?.id}
                      contentStyle={{ marginTop: 6, marginBottom: 12 }}
                    >
                      {group?.fields?.map((field) => (
                        <Descriptions.Item
                          key={field?.id}
                          label={
                            <Typography.Paragraph
                              style={{
                                textTransform: "uppercase",
                                marginRight: 12,
                                color: "rgb(100, 116, 139)",
                              }}
                              ellipsis={{
                                rows: 2,
                                expandable: true,
                                defaultExpanded: false,
                                symbol: (
                                  <span className="capitalize">
                                    {t("contacts.more")}
                                  </span>
                                ),
                              }}
                            >
                              {field.label}
                            </Typography.Paragraph>
                          }
                        >
                          {field?.value === "-" ? (
                            "-"
                          ) : (
                            <RenderDescriptionDetails
                              type={field.type}
                              value={field.value}
                              t={t}
                              call={call}
                              isProfile={true}
                            />
                          )}
                        </Descriptions.Item>
                      ))}
                    </Descriptions>
                  </div>
                ),
              };
            })}
          />
        </Skeleton>
      </div>

      <FormUpdate
        open={openDrawerUpdate}
        setOpen={setOpenDrawerUpdate}
        elementDetails={{
          id: user?.id,
          label: user?.label?.replaceAll("_", " "),
        }}
        familyId={4}
        setCatchChange={setCatchUpdate}
        catchChange={catchUpdate}
        profile={true}
      />
    </div>
  );
};

export default ProfileDetail;
