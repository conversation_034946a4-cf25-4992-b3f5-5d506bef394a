import { createRef } from "react";
//
export const Refs_IDs = {
  // logs
  logs_input_search: "#logs_input_search",
  logs_filter_icon: "#logs_filter_icon",
  logs_range_date_picker: createRef(),
  logs_dropDown_actions: createRef(),
  logs_statistics: createRef(),
  logs_export_calls_log: createRef(),
  logs_directory_create_new_element: createRef(),
  logs_directory_filter_icon: createRef(),
  logs_directory_dropDown_actions: createRef(),
  // families
  families_input_search: createRef(),
  families_filter_icon: createRef(),
  families_sort_icon: createRef(),
  families_select_pipeline: createRef(),
  families_select_display_view: createRef(),
  families_import_dropDown: createRef(),
  families_create_new_element: createRef(),
  families_statistics: createRef(),
  families_export_data: createRef(),
  families_columns_manipulation: createRef(),
  families_dropDown_actions: createRef(),
  //dashboard
  select_date_dashboard: createRef(),
  logo_dashboard: createRef(),
  globalSearch: createRef(),
  select_social_media_dashboard: createRef(),
  select_email_dashboard: createRef(),
  phonecpt: createRef(),
  iconProfile: createRef(),
  //visio
  CreateTask: createRef(),
  ChoiceDate: createRef(),
  Search: createRef(),
  MoreTask: createRef(),
  PriorityTask: createRef(),
  PipelineTask: createRef(),
  Notif: createRef(),
  Owner: createRef(),
  Guest: createRef(),
  Follower: createRef(),
  Participate: createRef(),
  //tasks
  addQuickTask: createRef(),
  searchActivity: createRef(),
  choiceDateTask: createRef(),
  selectPipelineTask: createRef(),
  choiceViewTask: createRef(),
  statsTasks: createRef(),
  filterTasks: createRef(),
  meModeTasks: createRef(),
  notifsTasks: createRef(),
  displayColumnsTasks: createRef(),
  exportTasks: createRef(),
  moreOptionsTask: createRef(),
  choicePriorityTask: createRef(),
  updateOwners: createRef(),
  updateGuests: createRef(),
  updateFollowers: createRef(),
  idActivity: createRef(),
  //chat
  savedMessage: createRef(),
  displayChat: createRef(),
  sortByChat: createRef(),
  addColleagueOrGroupInChat: createRef(),
  searchMsgsChat: createRef(),
  audioCallChat: createRef(),
  visioCallChat: createRef(),
  //viewSphere
  selectPipelineStageInviewSphere: createRef(),
  actionsHeaderViewSphere: createRef(),
  btnMoreHeaderViewSphere: createRef(),
  raccourciChatViewSphere: "#raccourciChatViewSphere",
  raccourciRmcViewSphere: "#raccourciRmcViewSphere",
  commentsViewSphere: "#commentsViewSphere",
  filesViewSphere: "#filesViewSphere",
  addAssociationViewSphere: createRef(),
};
//
export function getNode(key) {
  const val = Refs_IDs[key];
  if (!val) return null;

  // is it a React ref?
  if (typeof val === "object" && "current" in val) {
    return val.current;
  }

  // otherwise treat it as an ID
  return document.getElementById(val);
}
//

export const PathnamesTour = {
  "/logs": true,
  "/contacts": true,
};
