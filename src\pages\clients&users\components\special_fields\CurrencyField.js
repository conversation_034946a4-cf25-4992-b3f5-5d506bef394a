import { useState, useEffect, useMemo } from "react";
import { InputNumber, Select } from "antd";

const { Option } = Select;

const CurrencyField = ({
  fieldValue,
  options,
  form,
  fieldId,
  uniqueValue,
  setIsDisable,
  readOnly,
}) => {
  const [amount, setAmount] = useState(fieldValue?.[1]);
  const [currency, setCurrency] = useState(fieldValue?.[0]);
  //
  const [currencyOptions, setCurrencyOptions] = useState([]);

  const defaultOption = useMemo(
    () => currencyOptions?.[0]?.currency,
    [currencyOptions]
  );

  useEffect(() => {
    if (amount || currency)
      form.setFieldValue(fieldId, [currency || defaultOption, amount]);
    if (amount !== fieldValue?.[1] || currency !== fieldValue?.[0]) {
      form.setFields([
        {
          name: fieldId,
          touched: true,
        },
      ]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [amount, currency]);

  const handleCurrencyChange = (value) => {
    setCurrency(value);
    setIsDisable(false);
  };

  const renderCurrencyOption = (option, i) => (
    <Option
      key={i}
      value={option?.currency}
      nameEn={option?.name_en}
      nameFr={option?.name_fr}
      symbol={option?.currency_symbol}
    >
      {`${option?.currency} (${option?.currency_symbol})`}
    </Option>
  );

  const handleCurrencyOptions = () => {
    const result = [];
    for (let i = 0; i < options.length; i++) {
      const option = options[i];
      if (option?.primary) {
        result.unshift(option);
        break;
      }
      if (option?.selected_currencies) result.push(option);
    }
    setCurrencyOptions(result);
  };

  const handleNumberChange = (value) => {
    setAmount(value);
    setIsDisable(false);
  };

  useEffect(() => {
    handleCurrencyOptions();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <InputNumber
      disabled={uniqueValue || readOnly}
      addonBefore={
        <Select
          defaultActiveFirstOption
          disabled={readOnly && true}
          allowClear
          showSearch
          style={{ width: "7rem" }}
          value={currency || defaultOption}
          onChange={handleCurrencyChange}
          optionFilterProp={["nameEn", "nameFr", "value", "symbol"]}
          filterOption={(input, option) =>
            option?.nameEn.toLowerCase().includes(input.toLowerCase()) ||
            option?.nameFr.toLowerCase().includes(input.toLowerCase()) ||
            option?.value.toLowerCase().includes(input.toLowerCase()) ||
            option?.symbol.toLowerCase().includes(input.toLowerCase())
          }
        >
          {currencyOptions?.map(renderCurrencyOption)}
        </Select>
      }
      placeholder={`0.000`}
      style={{ width: "100%" }}
      value={amount}
      formatter={currencyFormatter}
      parser={currencyParser}
      onChange={handleNumberChange}
      controls={false}
    />
  );
};
//
export const currencyFormatter = (value) =>
  `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
//
export const currencyParser = (value) => {
  const truncatedValue = value
    .replace(/[^0-9.-]+/g, "")
    .replace(/\$\s?|(,*)/g, "")
    .replace(/(\.\d{3})\d+/g, "$1");

  const numericValue = parseFloat(truncatedValue);

  return isNaN(numericValue) ? "" : numericValue.toFixed(3);
};

export default CurrencyField;
