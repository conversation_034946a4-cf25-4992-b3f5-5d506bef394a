/* eslint-disable jsx-a11y/anchor-is-valid */
import {
  BookOutlined,
  DownOutlined,
  FilterOutlined,
  UsergroupAddOutlined,
} from "@ant-design/icons";
import {
  Badge,
  Button,
  Checkbox,
  Divider,
  Popover,
  Radio,
  Space,
  Tooltip,
  Typography,
} from "antd";
import { Suspense, lazy, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import {
  setDisplayMembersRoomsPreview,
  setOpenSideBarDrawer,
} from "new-redux/actions/chat.actions";
import { setConfigChatUser } from "new-redux/services/chat.services";
import { lazyRetry } from "utils/lazyRetry";
import { Loader } from "components/Chat";
import { FilterSidebarType } from "new-redux/reducers/chatReducer";
import { Refs_IDs } from "components/tour/tourConfig";
const AddDiscussion = lazy(() => lazyRetry(() => import("./AddDiscussion")));

const { Text } = Typography;
const SortAddBar = ({ source = "chat" }) => {
  const dispatch = useDispatch();

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [openPopOver, setOpenPopover] = useState(false);
  const [openPopOverGroupMembersFilter, setOpenPopOverGroupMembersFilter] =
    useState(false);
  const [isHovered, setIsHovred] = useState(false);
  const [t] = useTranslation("common");
  const {
    activeFiltersDiscussion,
    sidebarDrawer,
    currentUser,
    searchChatSideBar,
  } = useSelector((state) => state.chat);

  const onChangeMessagePreview = (e) => {
    dispatch(
      setConfigChatUser({
        sort_message: currentUser?.config?.sort_message,
        hidden_message: e.target.checked ? 0 : 1,
        sound_notification: currentUser?.config?.sound_notification,
        notification: currentUser?.config?.notification,
      })
    );
  };

  const onChangeSortBy = (e) => {
    dispatch(
      setConfigChatUser({
        sort_message: e.target.value,
        hidden_message: currentUser?.config?.hidden_message,
        sound_notification: currentUser?.config?.sound_notification,
        notification: currentUser?.config?.notification,
      })
    );
  };

  const onChangeDisplay = (e) => {
    dispatch(setDisplayMembersRoomsPreview(e.target.value));
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const showModal = () => {
    setIsModalOpen(true);
  };

  const content = (
    <div className="flex flex-col space-y-2">
      <Text type="secondary">{t("chat.sortBy")}</Text>
      <Radio.Group
        onChange={onChangeSortBy}
        value={
          !isNaN(currentUser?.config?.sort_message)
            ? currentUser?.config?.sort_message
            : 0
        }
      >
        <Space direction="vertical">
          <Radio value={1}>{t("chat.AZ")}</Radio>
          <Radio value={0}>{t("chat.recents")}</Radio>
          <Radio value={2}>{t("chat.UnReadMessage")}</Radio>
        </Space>
      </Radio.Group>
      <Divider />
      <Checkbox
        onChange={onChangeMessagePreview}
        checked={currentUser?.config?.hidden_message === 0}
      >
        {t("chat.messagePreview")}
      </Checkbox>
    </div>
  );

  const contentGroupMembers = (
    <div className="flex flex-col space-y-2">
      <Text type="secondary">{t("chat.display")}</Text>
      <Space direction="vertical">
        <Checkbox
          value={FilterSidebarType.member}
          onChange={onChangeDisplay}
          checked={activeFiltersDiscussion?.includes(FilterSidebarType.member)}
        >
          {t("chat.members")}
        </Checkbox>
        <Checkbox
          value={FilterSidebarType.room}
          onChange={onChangeDisplay}
          checked={activeFiltersDiscussion?.includes(FilterSidebarType.room)}
        >
          {t("chat.groups")}
        </Checkbox>
        <Checkbox
          value={FilterSidebarType.guest}
          onChange={onChangeDisplay}
          checked={activeFiltersDiscussion?.includes(FilterSidebarType.guest)}
        >
          {t("chat.guests")}
        </Checkbox>
      </Space>
    </div>
  );
  const handleOpenChange = (newOpen) => {
    setOpenPopover(newOpen);
  };

  const handleOpenChangeMembersGroupFilter = (newOpen) => {
    setOpenPopOverGroupMembersFilter(newOpen);
    setIsHovred(false);
  };

  return (
    <>
      {/* <div className="flex items-center justify-center  space-x-2">
        <Title level={4}>{t("menu1.chat")} </Title>
      </div> */}

      {source === "chat" ? (
        <Space>
          <Tooltip title={t("chat.listSavedMessage")}>
            <Button
              type="text"
              shape="circle"
              size="small"
              icon={<BookOutlined />}
              onClick={() => {
                dispatch(setOpenSideBarDrawer(true));
                setOpenPopover(false);
              }}
              ref={Refs_IDs.savedMessage}
            ></Button>
          </Tooltip>
          <Popover
            open={openPopOverGroupMembersFilter}
            onOpenChange={handleOpenChangeMembersGroupFilter}
            content={contentGroupMembers}
            placement="bottomLeft"
            trigger="click"
          >
            <Tooltip
              open={isHovered}
              onOpenChange={(e) => {
                setIsHovred(e);
              }}
              title={t("chat.display")}
            >
              <Badge
                dot={
                  activeFiltersDiscussion?.length <
                  Object.values(FilterSidebarType).length
                }
              >
                <Button
                  disabled={searchChatSideBar !== ""}
                  type="text"
                  shape="circle"
                  size="small"
                  icon={<FilterOutlined />}
                  ref={Refs_IDs.displayChat}
                />
              </Badge>
            </Tooltip>
          </Popover>
          <Popover
            open={openPopOver}
            onOpenChange={handleOpenChange}
            content={content}
            placement="bottomLeft"
            trigger="click"
          >
            <Tooltip title={t("chat.sortBy")}>
              <Button
                disabled={searchChatSideBar !== ""}
                type="text"
                shape="circle"
                size="small"
                icon={<DownOutlined />}
                ref={Refs_IDs.sortByChat}
              />
            </Tooltip>
          </Popover>
          <Suspense
            fallback={
              <div className="fixed inset-0 z-[9999] flex h-full w-full   items-center justify-center bg-black/5 ">
                <Loader size="2rem" />
              </div>
            }
          >
            {isModalOpen && (
              <AddDiscussion
                key={"addDiscussion" + isModalOpen}
                open={isModalOpen}
                handleCancel={handleCancel}
              />
            )}
          </Suspense>
          {sidebarDrawer === "chat" && (
            <Tooltip title={t("chat.addColleague")}>
              <Button
                type="link"
                shape="circle"
                size="small"
                icon={
                  <UsergroupAddOutlined
                    style={{
                      // color: "#404040",
                      fontSize: 16,
                    }}
                  />
                }
                onClick={showModal}
                ref={Refs_IDs.addColleagueOrGroupInChat}
              />
            </Tooltip>
          )}
        </Space>
      ) : null}
    </>
  );
};
export default SortAddBar;
