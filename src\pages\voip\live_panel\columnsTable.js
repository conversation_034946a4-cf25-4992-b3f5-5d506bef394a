import { memo, useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Tag, Tooltip } from "antd";
import { HighlightSearchW } from "../components";
import {
  InfoCircleOutlined,
  MessageFilled,
  MessageOutlined,
  PhoneOutlined,
} from "@ant-design/icons";
import { HiPhone } from "react-icons/hi2";
import { FaFax, FaHeadphones, FaLaptop, FaMobileAlt } from "react-icons/fa";
import { BsFillMegaphoneFill } from "react-icons/bs";
import { HiPhoneIncoming, HiPhoneOutgoing } from "react-icons/hi";
import { URL_ENV } from "index";
import DisplayAvatar from "../components/DisplayAvatar";
import { useTranslation } from "react-i18next";

export const useColumnsLivePanel = ({
  search,
  usersStatus,
  availabilityFilter,
  statusComFilter,
  handleOpenDisplayInfo,
  handleOpenChatDrawer,
  call,
}) => {
  //
  const [t] = useTranslation("common");
  //
  return [
    {
      title: t("livePanel.users"),
      dataIndex: "user_info",
      key: "user_info",
      fixed: "left",
      width: 250,
      filteredValue: availabilityFilter,
      filters: [
        {
          text: t("livePanel.online"),
          value: "online",
        },
        {
          text: t("livePanel.busy"),
          value: "busy",
        },
        {
          text: t("livePanel.away"),
          value: "away",
        },
        {
          text: t("livePanel.offline"),
          value: "offline",
        },
      ],
      onFilter: (value, record) => {
        const status = usersStatus[record.user_info.uid] || "offline";
        return status === value;
      },
      sorter: (a, b) => a.user_info?.label?.localeCompare(b.user_info?.label),
      render: ({ label, avatar, extension, uid, id, family_id }, record) => {
        const status = record.webphone?.status;
        const userStatus = usersStatus[uid];
        return (
          <div className="flex w-[235px] items-center justify-between">
            <div className="mr-1 min-w-0 flex-1">
              <div className="flex items-center space-x-1">
                <div className="_avatar_">
                  <Badge
                    dot
                    offset={[-3, 22]}
                    color={
                      userStatus === "away"
                        ? "orange"
                        : userStatus === "busy"
                        ? "red"
                        : userStatus === "online"
                        ? "green"
                        : "#a6a6a6"
                    }
                    styles={{
                      indicator: { height: 7, width: 7 },
                    }}
                  >
                    <DisplayAvatar
                      size={26}
                      name={label}
                      urlImg={
                        avatar &&
                        `${
                          URL_ENV?.REACT_APP_BASE_URL +
                          URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                        }${avatar}`
                      }
                    />
                  </Badge>
                </div>
                <span className="ml-1 truncate font-semibold">
                  {HighlightSearchW(label?.replaceAll("_", " "), search)}
                </span>
                <span className="ml-1 text-slate-500">
                  ({HighlightSearchW(extension, search)})
                </span>
              </div>
            </div>
            {label !== t("voip.me") && (
              <div className="flex-none items-center">
                {status === "free" ? (
                  <Button
                    key="call"
                    size="small"
                    type="link"
                    icon={
                      <PhoneOutlined
                        style={{ fontSize: 16, transform: "rotate(100deg)" }}
                      />
                    }
                    onClick={() => call(extension, id, family_id)}
                  />
                ) : null}
                {uid && userStatus ? (
                  <Button
                    key="chat"
                    size="small"
                    type="link"
                    icon={<MessageOutlined style={{ fontSize: 14 }} />}
                    onClick={() => handleOpenChatDrawer(uid)}
                  />
                ) : null}
                {/* <Button
                  size="small"
                  type="link"
                  icon={<InfoCircleOutlined style={{ fontSize: 14 }} />}
                  onClick={() =>
                    handleOpenDisplayInfo({
                      ...infoCall,
                      familyId: infoCall.family_id,
                    })
                  }
                /> */}
              </div>
            )}
          </div>
        );
      },
    },
    // {
    //   title: t("livePanel.availability"),
    //   dataIndex: "user_info",
    //   key: "availability",
    //   fixed: "left",
    //   width: 140,
    //   filteredValue: availabilityFilter,
    //   filters: [
    //     {
    //       text: t("livePanel.online"),
    //       value: "online",
    //     },
    //     {
    //       text: t("livePanel.busy"),
    //       value: "busy",
    //     },
    //     {
    //       text: t("livePanel.away"),
    //       value: "away",
    //     },
    //     {
    //       text: t("livePanel.offline"),
    //       value: "offline",
    //     },
    //   ],
    //   onFilter: (value, record) => {
    //     const status = usersStatus[record.user_info.uid] || "offline";
    //     return status === value;
    //   },
    //   render: ({ uid }) => {
    //     const status = usersStatus[uid];
    //     return (
    //       <Tag
    //         bordered={false}
    //         color={
    //           status === "online"
    //             ? "success"
    //             : status === "busy"
    //             ? "error"
    //             : status === "away"
    //             ? "warning"
    //             : ""
    //         }
    //         style={{ fontWeight: 600 }}
    //       >
    //         {status === "online"
    //           ? t("livePanel.online")
    //           : status === "busy"
    //           ? t("livePanel.busy")
    //           : status === "away"
    //           ? t("livePanel.away")
    //           : t("livePanel.offline")}
    //       </Tag>
    //     );
    //   },
    // },
    {
      title: t("livePanel.statusCall"),
      dataIndex: "webphone",
      key: "statusCom",
      fixed: "left",
      width: 170,
      filteredValue: statusComFilter,
      filters: [
        { text: t("livePanel.onCall"), value: "inCall" },
        { text: t("livePanel.ringing"), value: "ringing" },
        { text: t("livePanel.onPause"), value: "onHold" },
        { text: t("livePanel.free"), value: "free" },
      ],
      onFilter: (value, record) => {
        const status = record.webphone?.status;
        // const userStatus = usersStatus[record.user_info.uid];
        return (
          status === value
          // ||
          // (value === "free" &&
          //   userStatus &&
          //   status !== "inCall" &&
          //   status !== "ringing" &&
          //   status !== "onHold")
        );
      },
      render: ({ status }, record) => {
        // console.log(status);
        // const userStatus = usersStatus[record.user_info.uid];
        const effectiveStatus = status; /*|| userStatus;*/
        if (!effectiveStatus) return null;
        //
        const getTagColor = (status) => {
          switch (status) {
            case "inCall":
              return "success";
            case "ringing":
              return "processing";
            case "onHold":
              return "warning";
            default:
              return "";
          }
        };
        //
        const getStatusLabel = (status) => {
          switch (status) {
            case "inCall":
              return t("livePanel.onCall");
            case "ringing":
              return t("livePanel.ringing");
            case "onHold":
              return t("livePanel.onPause");
            default:
              return t("livePanel.free");
          }
        };

        const tagColor = getTagColor(status);
        const label = getStatusLabel(effectiveStatus);

        return (
          <div className="flex items-center space-x-1.5">
            {!!record?.infoCall?.source && (
              <SourceToDisplay source={record?.infoCall?.source} t={t} />
            )}
            {record.infoCall?.direction && (
              <IconToDisplay direction={record.infoCall.direction} />
            )}
            <Tag bordered={false} style={{ fontWeight: 600 }} color={tagColor}>
              <div className="flex items-center space-x-1">
                {status !== "free" && status && (
                  <span className="relative mb-[1px] flex h-1.5 w-1.5">
                    <span
                      className={`absolute inline-flex h-full w-full animate-ping rounded-full ${
                        status === "inCall"
                          ? "bg-green-400"
                          : status === "ringing"
                          ? "bg-blue-400"
                          : "bg-yellow-400"
                      } opacity-75`}
                    />
                    <span
                      className={`relative inline-flex h-1.5 w-1.5 rounded-full ${
                        status === "inCall"
                          ? "bg-green-500"
                          : status === "ringing"
                          ? "bg-blue-500"
                          : "bg-yellow-500"
                      }`}
                    />
                  </span>
                )}
                <span>{label}</span>
              </div>
            </Tag>
            {status === "onHold" && record.webphone?.onHoldTime && (
              <div className="ml-1.5">
                <Timer startTime={record.webphone.onHoldTime} />
              </div>
            )}
          </div>
        );
      },
    },
    {
      title: t("livePanel.onCallWith"),
      dataIndex: "infoCall",
      key: "comWith",
      fixed: "left",
      width: 250,
      defaultSortOrder: "descend",
      sorter: (a, b) => {
        const aVal = a.infoCall ? a.infoCall.label || a.infoCall.number : "";
        const bVal = b.infoCall ? b.infoCall.label || b.infoCall.number : "";
        return String(aVal).localeCompare(String(bVal));
      },
      render: (infoCall) => {
        if (!infoCall) return null;
        return (
          <div className="flex w-[235px] items-center justify-between">
            <div className="mr-1 min-w-0 flex-1">
              <div className="flex items-center space-x-1">
                <div className="_avatar_">
                  <DisplayAvatar
                    size={26}
                    name={infoCall.label}
                    urlImg={
                      infoCall.avatar &&
                      `${URL_ENV.REACT_APP_BASE_URL}${URL_ENV.REACT_APP_SUFFIX_AVATAR_URL}${infoCall.avatar}`
                    }
                  />
                </div>
                <span className="ml-1 truncate font-semibold">
                  {HighlightSearchW(infoCall.label || infoCall.number, search)}
                  {/* Hassine Ben Ali Ben Hassine Turki Basla */}
                </span>
                {!!infoCall.label && (
                  <span className="ml-1 text-slate-500">
                    ({HighlightSearchW(infoCall.number, search)})
                  </span>
                )}
              </div>
            </div>
            {infoCall.id && infoCall.family_id && (
              <div className="flex-none">
                <Button
                  size="small"
                  type="link"
                  icon={<InfoCircleOutlined style={{ fontSize: 14 }} />}
                  onClick={() =>
                    handleOpenDisplayInfo({
                      ...infoCall,
                      familyId: infoCall.family_id,
                    })
                  }
                />
              </div>
            )}
          </div>
        );
      },
    },
    {
      title: t("livePanel.callTime"),
      dataIndex: "infoCall",
      key: "callStartTime",
      align: "center",
      fixed: "left",
      width: 100,
      sorter: (a, b) => {
        const startA = a.infoCall?.startTime ? Number(a.infoCall.startTime) : 0;
        const startB = b.infoCall?.startTime ? Number(b.infoCall.startTime) : 0;
        return startA - startB;
      },
      render: (infoCall) =>
        !!infoCall?.startTime && <Timer startTime={infoCall?.startTime} />,
    },
    {
      title: t("livePanel.incomingCalls"),
      children: [
        {
          title: "Total",
          dataIndex: ["daily_stat", "inBound"],
          key: "inBound",
          width: 80,
          align: "right",
          sorter: (a, b) => a.daily_stat.inBound - b.daily_stat.inBound,
        },
        {
          title: t("livePanel.answered"),
          dataIndex: ["daily_stat", "answered"],
          key: "answered",
          width: 105,
          align: "right",
          sorter: (a, b) => a.daily_stat.answered - b.daily_stat.answered,
        },
        {
          title: t("livePanel.notAnswered"),
          dataIndex: ["daily_stat", "unAnswered"],
          key: "unAnswered",
          width: 145,
          align: "right",
          sorter: (a, b) => a.daily_stat.unAnswered - b.daily_stat.unAnswered,
        },
        {
          title: "%" + t("livePanel.lost"),
          dataIndex: ["daily_stat", "unansweredPct"],
          key: "unansweredPct",
          width: 100,
          align: "right",
          sorter: (a, b) =>
            a.daily_stat.unansweredPct - b.daily_stat.unansweredPct,
          render: (percentageUnAnswered) => (
            <span className="text-slate-500">{percentageUnAnswered}%</span>
          ),
        },
        {
          title: t("livePanel.recalled"),
          dataIndex: ["daily_stat", "returned"],
          key: "returned",
          width: 105,
          align: "right",
          sorter: (a, b) => a.daily_stat.returned - b.daily_stat.returned,
        },
      ],
    },
    {
      title: t("livePanel.outgoingCalls"),
      children: [
        {
          title: "Total",
          dataIndex: ["daily_stat", "outBound"],
          key: "outBound",
          width: 80,
          align: "right",
          sorter: (a, b) => a.daily_stat.outBound - b.daily_stat.outBound,
        },
        {
          title: t("livePanel.answered"),
          dataIndex: ["daily_stat", "outAnswered"],
          key: "outAnswered",
          width: 100,
          align: "right",
          sorter: (a, b) => a.daily_stat.outAnswered - b.daily_stat.outAnswered,
        },
        {
          title: t("livePanel.notAnswered"),
          dataIndex: ["daily_stat", "outUnAnswered"],
          key: "outUnAnswered",
          width: 145,
          align: "right",
          sorter: (a, b) =>
            a.daily_stat.outUnAnswered - b.daily_stat.outUnAnswered,
        },
      ],
    },
  ];
  // {
  //   title: "Actions",
  //   dataIndex: "actions",
  //   key: "actions",
  //   fixed: "left",
  //   width: 100,
  //   onCell: (record, rowIndex) => ({
  //     style: {
  //       backgroundColor: "#fafafa",
  //     },
  //   }),
  //   render: (_, record) => <DisplayActions record={record} />,
  // },
};
//
const IconToDisplay = memo(({ direction }) =>
  direction === "outBound" ? (
    <HiPhoneOutgoing className="fill-blue-600 " style={{ fontSize: 14 }} />
  ) : direction === "inBound" ? (
    <HiPhoneIncoming className="fill-green-600 " style={{ fontSize: 14 }} />
  ) : null
);
//
const SourceToDisplay = memo(({ source, t }) => (
  <Tooltip
    title={
      source === "mobile"
        ? t("livePanel.appMobile")
        : source === "deskPhone"
        ? t("livePanel.deskPhone")
        : t("livePanel.webPhone")
    }
  >
    {source === "mobile" ? (
      <FaMobileAlt style={{ fontSize: 14, cursor: "help" }} />
    ) : source === "deskPhone" ? (
      <FaFax style={{ fontSize: 14, cursor: "help" }} />
    ) : (
      <FaLaptop style={{ fontSize: 14, cursor: "help" }} />
    )}
  </Tooltip>
));
//
export const DisplayActions = ({ record }) => {
  //
  //
  return (
    // record.user_info.availability === "online" && (
    <div className="flex items-center space-x-0">
      <Tooltip key="call" title="Appeler">
        <Button
          size="small"
          type="link"
          icon={<HiPhone style={{ fontSize: 13 }} />}
          disabled={
            record.user_info.availability !== "online" ||
            record?.statusCom === "in a call" ||
            record?.statusCom === "ringing"
          }
        />
      </Tooltip>

      <Tooltip key="chat" title="chat">
        <Button
          size="small"
          type="link"
          icon={<MessageFilled style={{ fontSize: 13 }} />}
          disabled={!record.user_info?.uid}
        />
      </Tooltip>

      <Tooltip key="listen" title="Écouter">
        <Button
          size="small"
          type="link"
          icon={<FaHeadphones style={{ fontSize: 14 }} />}
          disabled={record?.statusCom !== "in a call"}
        />
      </Tooltip>

      <Tooltip key="whisper" title="Chuchoter">
        <Button
          size="small"
          type="link"
          icon={<BsFillMegaphoneFill style={{ fontSize: 13 }} />}
          disabled={record?.statusCom !== "in a call"}
        />
      </Tooltip>
    </div>
  );
  //   );
};
//
export const Timer = memo(({ startTime }) => {
  //
  const [elapsedTime, setElapsedTime] = useState(0);

  useEffect(() => {
    const timer = setInterval(() => {
      setElapsedTime(Math.floor(Date.now() / 1000) - startTime);
    }, 1000);

    return () => clearInterval(timer);
  }, [startTime]);

  //
  const formatTime = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    //
    if (!hours && !minutes && !secs) return null;
    if (hours > 0) {
      return `${String(hours).padStart(2, "0")}:${String(minutes).padStart(
        2,
        "0"
      )}:${String(secs).padStart(2, "0")}`;
    }
    return `${String(minutes).padStart(2, "0")}:${String(secs).padStart(
      2,
      "0"
    )}`;
  };

  return <div>{formatTime(elapsedTime)}</div>;
});
