import { memo, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { Button, Divider, Input, Space, Table, Tag, Tooltip } from "antd";
import { FiSearch } from "react-icons/fi";
import { CloseOutlined, UploadOutlined } from "@ant-design/icons";
import { debounce } from "lodash";
import { useWindowSize } from "pages/clients&users/components/WindowSize";
import { getLivePanelData } from "../services/services";
import { toastNotification } from "components/ToastNotification";
import { useColumnsLivePanel } from "./columnsTable";
import { useMercurePanelLive } from "./useMercurePanelLive";
import { openDrawerChat } from "new-redux/actions/voip.actions/handleDrawerChat";
import KpiLivePanel from "./KpiLivePanel";
import DisplayElementInfo from "../components/DisplayElementInfo";
import useActionCall from "../helpers/ActionCall";
import "./index.css";

const LivePanel = () => {
  //
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const call = useActionCall();
  const windowSize = useWindowSize();
  const onlineUserRef = useRef({});
  //
  //
  const currentUser = useSelector(({ user }) => user?.user);
  const eventMercure = useSelector((state) => state.ChatRealTime.eventMercure);
  const onlineUser = useSelector(
    (state) => state.ChatRealTime?.onlineUser || {}
  );
  const usersStatus = useMemo(() => {
    if (eventMercure?.readyState === 1) {
      onlineUserRef.current = onlineUser;
      return onlineUser;
    } else return onlineUserRef.current;
  }, [eventMercure?.readyState, onlineUser]);
  //
  //   const [fetchedUsers, setFetchedUsers] = useState([]);
  const [dataSourceTable, setDataSourceTable] = useState([]);
  const [loadingTable, setLoadingTable] = useState(false);
  const [search, setSearch] = useState("");
  const [availabilityFilter, setAvailabilityFilter] = useState(null);
  const [statusComFilter, setStatusComFilter] = useState(null);
  const [selectedStats, setSelectedStats] = useState("inBound");
  const [openDisplayInfo, setOpenDisplayInfo] = useState(false);
  const [elementInfo, setElementInfo] = useState({});
  //
  const fetchLivePanelData = useCallback(async () => {
    try {
      setLoadingTable(true);
      const { data } = await getLivePanelData();
      if (!data?.length) return;
      const transformData = data
        //   .filter((user) => user.user_info.id !== currentUser.id)
        .filter((user) => !!user.user_info.extension)
        .map((user) => {
          const { user_info, daily_stat, webphone, called_info, caller_info } =
            user;
          let infoCall = null;
          const webStatus = webphone.com_statut?.webphone?.status;
          if (
            webStatus === "RingCall" ||
            webStatus === "RingingCall" ||
            webStatus === "Up" ||
            webStatus === "OnHoldCall"
          ) {
            const startTime =
              webStatus === "Up" || webStatus === "OnHoldCall"
                ? Number(webphone.com_statut?.webphone?.start_time)
                : null;
            const source = user.mobile_device
              ? "mobile"
              : user.deskphone_device
              ? "deskPhone"
              : "webPhone";
            infoCall =
              called_info.id === user_info.id
                ? {
                    ...caller_info,
                    extension: `${caller_info.extension}`,
                    number: webphone.com_statut?.webphone?.caller,
                    direction: "inBound",
                    startTime: startTime,
                    source,
                    label:
                      caller_info.id === currentUser.id
                        ? t("voip.me")
                        : caller_info.label,
                  }
                : {
                    ...called_info,
                    extension: `${called_info.extension}`,
                    number: webphone.com_statut?.webphone?.called,
                    direction: "outBound",
                    startTime: startTime,
                    source,
                    label:
                      called_info.id === currentUser.id
                        ? t("voip.me")
                        : called_info.label,
                  };
          }
          return {
            key: user_info.id,
            ...user,
            user_info: {
              ...user_info,
              label:
                user_info.id === currentUser.id
                  ? t("voip.me")
                  : user_info.label,
              extension: `${user_info.extension}`,
              // availability: usersStatus[user_info.uid] || null,
            },
            daily_stat: {
              inBound: daily_stat.in_bound,
              answered: daily_stat.received,
              unAnswered: daily_stat.missed,
              returned: daily_stat.returned_calls,
              outBound: daily_stat.out_bound,
              outAnswered: daily_stat.outbound_received,
              outUnAnswered: daily_stat.outbound_missed,
              unansweredPct: getUnansweredPercentage(
                daily_stat.in_bound,
                daily_stat.missed
              ),
            },
            webphone: {
              ...webphone,
              status:
                webStatus === "RingCall" || webStatus === "RingingCall"
                  ? "ringing"
                  : webStatus === "Up"
                  ? "inCall"
                  : webStatus === "OnHoldCall"
                  ? "onHold"
                  : webStatus === "NOT_INUSE" ||
                    webStatus === "UNAVAILABLE" ||
                    !!onlineUserRef.current[user_info.uid]
                  ? "free"
                  : null,
            },
            infoCall,
          };
        });
      setDataSourceTable(transformData);
    } catch (err) {
      toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message || err);
    } finally {
      setLoadingTable(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    fetchLivePanelData();
  }, [fetchLivePanelData]);
  //
  const eventData = useMercurePanelLive({ fetchLivePanelData });
  // Handle Mercure events to update the data source table.
  const handleEventMercure = useCallback(() => {
    if (!eventData || dataSourceTable.length === 0) return;

    const { type_event, data: eventDetails } = eventData;
    const { user_info, daily_stat, webphone, called_info, caller_info } =
      eventDetails;

    const source = eventDetails.mobile_device
      ? "mobile"
      : eventDetails.deskphone_device
      ? "deskPhone"
      : "webPhone";

    const infoCall =
      called_info.id === user_info.id
        ? {
            ...caller_info,
            extension: `${caller_info.extension}`,
            number: webphone.com_statut?.caller,
            direction: "inBound",
            source,
            label:
              caller_info.id === currentUser.id
                ? t("voip.me")
                : caller_info.label,
          }
        : {
            ...called_info,
            extension: `${called_info.extension}`,
            number: webphone.com_statut?.called,
            direction: "outBound",
            source,
            label:
              called_info.id === currentUser.id
                ? t("voip.me")
                : called_info.label,
          };

    setDataSourceTable((prev) =>
      prev.map((user) => {
        if (user.user_info.id !== user_info.id) return user;

        if (type_event === "RingCall" || type_event === "RingingCall") {
          return updateUserData(user, {
            webphone: { ...webphone, status: "ringing" },
            infoCall,
            eventDetails,
            daily_stat,
          });
        } else if (type_event === "InProgressCall") {
          return updateUserData(user, {
            webphone: { ...webphone, status: "inCall" },
            eventDetails,
            infoCall: {
              ...infoCall,
              startTime: Math.floor(Date.now() / 1000),
            },
            daily_stat,
          });
        } else if (type_event === "OnHoldCall") {
          return updateUserData(user, {
            webphone: {
              ...webphone,
              status: "onHold",
              onHoldTime: Math.floor(Date.now() / 1000),
            },
            eventDetails,
            ...(user?.infoCall || infoCall),
            daily_stat,
          });
        } else if (type_event === "UnHoldCall") {
          return updateUserData(user, {
            webphone: { ...webphone, status: "inCall" },
            ...(user?.infoCall || infoCall),
            daily_stat,
          });
        } else if (
          type_event === "change_status" &&
          (webphone.status === "NOT_INUSE" || webphone.status === "UNAVAILABLE")
        ) {
          return updateUserData(user, {
            webphone: { ...webphone, status: "free" },
            infoCall: null,
            daily_stat,
          });
        }
        return user;
      })
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [eventData]);

  useEffect(() => {
    handleEventMercure();
  }, [handleEventMercure]);
  //
  const handleOpenDisplayInfo = (element) => {
    if (!element?.id || !element?.familyId) return;
    setElementInfo(element);
    setOpenDisplayInfo(true);
  };
  //
  const handleOpenChatDrawer = (uid) => {
    dispatch(openDrawerChat(uid));
  };
  //
  const columnsTable = useColumnsLivePanel({
    search,
    usersStatus,
    availabilityFilter,
    statusComFilter,
    handleOpenDisplayInfo,
    handleOpenChatDrawer,
    call,
  });
  //
  // console.log({ dataSourceTable });
  // console.log(eventData);
  //
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedSearch = useCallback(
    debounce((nextValue) => {
      setSearch(nextValue.trim());
    }, 300),
    []
  );

  // Memoize the filtered dataSource to prevent unnecessary re-renders
  const filteredDataSource = useMemo(() => {
    return dataSourceTable.filter(
      (item) =>
        item.user_info.label?.toLowerCase().includes(search.toLowerCase()) ||
        item.user_info.extension?.toLowerCase().includes(search.toLowerCase())
    );
  }, [dataSourceTable, search]);

  //
  if (window.location.pathname !== "/telephony/livePanel") return null;
  //
  return (
    <div className="space-y-3">
      <KpiLivePanel
        dataSource={dataSourceTable}
        usersStatus={usersStatus}
        loading={loadingTable}
        availabilityFilter={availabilityFilter}
        setAvailabilityFilter={setAvailabilityFilter}
        statusComFilter={statusComFilter}
        setStatusComFilter={setStatusComFilter}
        selectedStats={selectedStats}
        setSelectedStats={setSelectedStats}
      />
      <div className="flex justify-between px-3">
        <div className="flex space-x-4">
          <Input
            allowClear
            style={{ width: "16rem" }}
            placeholder={t("livePanel.searchPLive")}
            prefix={<FiSearch className="text-slate-400" />}
            disabled={!search && !dataSourceTable.length}
            onChange={(e) => debouncedSearch(e.target.value)}
          />
          <DisplayFilters
            availabilityFilter={availabilityFilter}
            statusComFilter={statusComFilter}
            setAvailabilityFilter={setAvailabilityFilter}
            setStatusComFilter={setStatusComFilter}
            t={t}
          />
        </div>
        <Tooltip title="Exporter">
          <Button
            disabled={
              !dataSourceTable.length || process.env.REACT_APP_BRANCH === "prod"
            }
            icon={<UploadOutlined />}
            onClick={() => fetchLivePanelData()}
          />
        </Tooltip>
      </div>
      <div className="table-view-livePanel">
        <Table
          // virtual
          bordered
          size="small"
          columns={columnsTable}
          dataSource={filteredDataSource}
          loading={loadingTable}
          scroll={{
            x: "max-content",
            y: windowSize?.height - 470,
          }}
          pagination={false}
          locale={{
            emptyText: loadingTable ? (
              <div className=" flex h-64 flex-row items-center justify-center space-x-2 text-xl">
                {t("contacts.loadingDataTable")}
                <span className="animate-bounce">...</span>
              </div>
            ) : (
              ""
            ),
          }}
          onChange={(pagination, filters, sorter) => {
            const { availability, statusCom } = filters;
            setAvailabilityFilter(availability);
            setStatusComFilter(statusCom);
          }}
        />
      </div>
      <DisplayElementInfo
        open={openDisplayInfo}
        setOpen={setOpenDisplayInfo}
        elementDetails={elementInfo}
      />
    </div>
  );
};
//
//
const getUnansweredPercentage = (inBound, missed) => {
  if (!inBound) {
    return "0";
  }
  const percentage = (missed / inBound) * 100;
  return parseFloat(percentage.toFixed(2)).toString();
};
//
// Map API response data to the table's data source format.
const mapLivePanelData = (data, usersStatus, currentUser) =>
  data
    .filter((user) => user.user_info.id !== currentUser.id)
    .map((user) => {
      const {
        user_info,
        daily_stat,
        postes_statut: { webphone },
      } = user;
      return {
        ...user,
        key: user_info.id,
        user_info: {
          ...user_info,
          extension: `${user_info.extension}`,
        },
        daily_stat: {
          inBound: daily_stat.in_bound,
          outBound: daily_stat.out_bound,
          answered: daily_stat.received,
          unAnswered: daily_stat.missed,
          unansweredPct: getUnansweredPercentage(
            daily_stat.in_bound,
            daily_stat.missed
          ),
        },
        webphone: {
          ...webphone,
          status: webphone.status.includes("Ring")
            ? "ringing"
            : webphone.status === "Up"
            ? "inCall"
            : webphone.status === "NOT_INUSE" || !!usersStatus[user_info.uid]
            ? "free"
            : null,
        },
      };
    });
//
// Helper to update a specific user's data based on Mercure events.
const updateUserData = (user, updatedProps) => ({
  ...user,
  ...updatedProps,
  daily_stat: {
    inBound: updatedProps.daily_stat?.in_bound ?? user.daily_stat.inBound,
    answered: updatedProps.daily_stat?.received ?? user.daily_stat.answered,
    unAnswered: updatedProps.daily_stat?.missed ?? user.daily_stat.unAnswered,
    returned:
      updatedProps.daily_stat.returned_calls ?? user.daily_stat.returned,
    outBound: updatedProps.daily_stat?.out_bound ?? user.daily_stat.outBound,
    outAnswered:
      updatedProps.daily_stat?.outbound_received ?? user.daily_stat.outAnswered,
    outUnAnswered:
      updatedProps.daily_stat?.outbound_missed ?? user.daily_stat.outUnAnswered,
    unansweredPct: getUnansweredPercentage(
      updatedProps.daily_stat?.in_bound ?? user.daily_stat.inBound,
      updatedProps.daily_stat?.missed ?? user.daily_stat.unAnswered
    ),
  },
});
//
const FILTER_MAPPINGS = (t) => ({
  availability: {
    online: { label: t("livePanel.online"), color: "success" },
    offline: { label: t("livePanel.offline"), color: "" },
    busy: { label: t("livePanel.busy"), color: "error" },
    away: { label: t("livePanel.away"), color: "warning" },
  },
  statusCom: {
    inCall: { label: t("livePanel.onCall"), color: "success" },
    free: { label: t("livePanel.free"), color: "" },
    ringing: { label: t("livePanel.ringing"), color: "processing" },
    onHold: { label: t("livePanel.onPause"), color: "warning" },
  },
});

const CustomTag = ({ filterName, color, onRemove }) => (
  <Tag bordered={false} color={color} style={{ padding: 0 }}>
    <div className="relative inline-flex items-center space-x-1 rounded pl-1.5 text-xs font-semibold">
      <span>{filterName}</span>
      <Button
        size="small"
        type="text"
        icon={<CloseOutlined />}
        onClick={onRemove}
      />
    </div>
  </Tag>
);

const DisplayFilters = memo(
  ({
    availabilityFilter,
    statusComFilter,
    setAvailabilityFilter,
    setStatusComFilter,
    t,
  }) => {
    //
    const removeFilter = useCallback((setState, key) => {
      setState((prev) => prev.filter((item) => item !== key));
    }, []);
    //
    const renderFilters = useCallback(
      (filterKeys, mapping, setFilterState) => (
        <Space size="small">
          {filterKeys.map((key) => {
            const { label, color } = mapping[key] || {};
            return (
              <CustomTag
                key={key}
                filterName={label}
                color={color}
                onRemove={() => removeFilter(setFilterState, key)}
              />
            );
          })}
        </Space>
      ),
      [removeFilter]
    );

    //
    if (!availabilityFilter?.length && !statusComFilter?.length) return null;
    //
    return (
      <Space split={<Divider type="vertical" style={{ height: "1.2rem" }} />}>
        {availabilityFilter?.length
          ? renderFilters(
              availabilityFilter,
              FILTER_MAPPINGS(t).availability,
              setAvailabilityFilter
            )
          : null}
        {statusComFilter?.length
          ? renderFilters(
              statusComFilter,
              FILTER_MAPPINGS(t).statusCom,
              setStatusComFilter
            )
          : null}
      </Space>
    );
  }
);
//
export default LivePanel;
