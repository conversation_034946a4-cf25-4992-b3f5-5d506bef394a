import { Fragment } from "react";
import { useSelector } from "react-redux";
import { Avatar, Skeleton, Tooltip } from "antd";
import {
  CheckCircleOutlined,
  ExclamationCircleFilled,
  FieldTimeOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";

import { AvatarChat } from "components/Chat";
import {
  getName,
  systemMessageTypesGroups,
} from "pages/layouts/chat/utils/ConversationUtils";
import useGetInfoDiscussion from "pages/layouts/chat/hooks/useGetInfoDiscussion";
import { URL_ENV } from "index";

const RenderSeen = ({ item, index_item, data, source }) => {
  const { currentUser, selectedParticipants } = useSelector(
    (state) => state.chat
  );
  const { user } = useSelector((state) => state.user);

  const selectedConversation = useSelector(
    (state) => state.ChatRealTime.selectedConversation
  );

  const { status, data: dataInfo, fetchStatus } = useGetInfoDiscussion();

  const { t } = useTranslation("common");
  const markedSeenFunctionRoom = () => {
    let selectedParticipantsChat =
      source !== "no_chat"
        ? [...(dataInfo?.data?.room_info?.participants || [])]
        : [...selectedParticipants];

    let array = [];

    array = selectedParticipantsChat?.filter(
      (p) =>
        (item.unread_room?.includes(p._id) && p._id !== currentUser?._id) ||
        (p._id === item.sender_id &&
          item.type !== "message_from_bot" &&
          item.sender_id !== currentUser?._id &&
          index_item === 0)
    );

    if (status === "loading" && fetchStatus !== "idle")
      return item.unread_room
        .slice(0, 4)
        .map((_, index) => <Skeleton.Avatar size={16} active key={index} />);
    if (item.unread === 2)
      return <FieldTimeOutlined className="text-gray-600" />;
    else if (item.unread === "error" && source === "main") {
      return (
        <Tooltip
          placement="topLeft"
          title={t("chat.error_message.message_send")}>
          <ExclamationCircleFilled className="animate-bounce text-lg text-[#ff4d4f] " />
        </Tooltip>
      );
    } else if (
      systemMessageTypesGroups.includes(item.type) &&
      index_item === 0
    ) {
      return (
        <Tooltip
          placement="topLeft"
          title={getName(selectedConversation?.name, "name")}>
          <>
            <AvatarChat
              isPublic={selectedConversation?.predefined === 2}
              className={
                selectedConversation?.predefined === 2
                  ? ""
                  : "mb-0.5 ring-1 ring-blue-600"
              }
              fontSize="0.4rem"
              type="room"
              hasImage={selectedConversation?.image}
              height={5}
              width={5}
              size={20}
              url={
                URL_ENV?.REACT_APP_OAUTH_CHAT_API +
                process.env.REACT_APP_SUFFIX_CHAT_GROUP_STORAGE +
                selectedConversation?.image
              }
              name={getName(selectedConversation?.name, "avatar")}
            />
          </>
        </Tooltip>
      );
    } else if (
      selectedParticipantsChat?.length > 0 &&
      array?.length === 0 &&
      index_item === 0
    ) {
      if (item.type === "message_from_bot")
        return (
          <Tooltip placement="topLeft" title={getName(item.bot?.name, "name")}>
            <AvatarChat
              className={item.bot?.logo ? "" : "mt-1"}
              fontSize="0.4rem"
              type="user"
              hasImage={item.bot?.logo}
              height={5}
              width={5}
              size={20}
              url={
                URL_ENV?.REACT_APP_OAUTH_CHAT_API +
                process.env.REACT_APP_SUFFIX_CHAT_GROUP_STORAGE +
                item?.bot?.logo
              }
              name={getName(item?.bot?.name, "avatar")}
            />
          </Tooltip>
        );
      else if (item.sender_id === currentUser?._id)
        return <CheckCircleOutlined className="text-gray-600" />;
    } else if (array && array.length > 0) {
      return (
        <Avatar.Group
          className="flex flex-row items-center text-xs"
          maxCount={5}
          maxPopoverTrigger="hover"
          maxPopoverPlacement="top"
          maxStyle={{
            cursor: "pointer",
            backgroundColor: "#dbeafe",

            color: "#1e40af",
            height: 25,
            fontSize: "13px",
            width: 25,
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            alignSelf: "center",
            marginTop: 3,
          }}>
          {array.map((e, k1) => (
            <Tooltip
              placement="topLeft"
              title={getName(e.name, "name")}
              key={`react_${k1}`}>
              <Fragment key={`react_${k1}`}>
                <AvatarChat
                  className={e.image ? "" : "mt-1"}
                  fontSize="0.4rem"
                  type="user"
                  hasImage={e.image}
                  height={5}
                  width={5}
                  size={20}
                  url={
                    URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                    (e._id === currentUser?._id ? user?.avatar : e?.image)
                  }
                  name={getName(e?.name, "avatar")}
                />
              </Fragment>
            </Tooltip>
          ))}
        </Avatar.Group>
      );
    }
  };
  const markedSeenFunction = () => {
    const indexArray = data
      ? data.findLastIndex(
          (e) => e.sender_id !== currentUser?._id || e.unread === 0
        )
      : undefined;

    // return (
    //   <Avatar.Group
    //     maxCount={5}
    //     maxStyle={{
    //       cursor: "pointer",
    //       backgroundColor: "#dbeafe",
    //       color: "#1e40af",
    //       height: 25,
    //       fontSize: "13px",
    //       width: 25,

    //       marginTop: 3,
    //     }}>
    //     <Avatar
    //       size={22}
    //       src="https://api.dicebear.com/7.x/miniavs/svg?seed=2"
    //     />
    //     <Avatar
    //       size={22}
    //       src="https://api.dicebear.com/7.x/miniavs/svg?seed=2"
    //     />
    //     <Avatar
    //       size={22}
    //       src="https://api.dicebear.com/7.x/miniavs/svg?seed=2"
    //     />
    //     <Avatar
    //       size={22}
    //       src="https://api.dicebear.com/7.x/miniavs/svg?seed=2"
    //     />
    //     <Avatar
    //       size={22}
    //       src="https://api.dicebear.com/7.x/miniavs/svg?seed=2"
    //     />
    //     <Avatar
    //       size={22}
    //       src="https://api.dicebear.com/7.x/miniavs/svg?seed=2"
    //     />
    //     <Avatar
    //       size={22}
    //       style={{
    //         backgroundColor: "#f56a00",
    //       }}>
    //       K
    //     </Avatar>
    //     <Tooltip title="Ant User" placement="top">
    //       <Avatar
    //         size={22}
    //         src="https://api.dicebear.com/7.x/miniavs/svg?seed=2"
    //         style={{
    //           backgroundColor: "#87d068",
    //         }}
    //       />
    //     </Tooltip>
    //     <Avatar
    //       size={22}
    //       style={{
    //         backgroundColor: "#1677ff",
    //       }}
    //       src="https://api.dicebear.com/7.x/miniavs/svg?seed=2"
    //     />
    //   </Avatar.Group>
    // );
    if (index_item === 0) {
      return (
        <>
          {item.sender_id !== currentUser?._id || item.unread === 0 ? (
            <Tooltip
              placement="topLeft"
              title={getName(selectedConversation?.name, "name")}>
              <>
                {" "}
                <AvatarChat
                  className={selectedConversation?.image ? "" : "mt-1"}
                  fontSize="0.4rem"
                  type="user"
                  key={`react_${selectedConversation?.id}`}
                  hasImage={selectedConversation?.image}
                  height={5}
                  width={5}
                  size={18}
                  url={
                    URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                    selectedConversation?.image
                  }
                  name={getName(selectedConversation?.name, "avatar")}
                />
              </>
            </Tooltip>
          ) : item.unread === 2 ? (
            <FieldTimeOutlined className="text-gray-600" />
          ) : item.unread === "error" ? (
            <Tooltip
              placement="topLeft"
              title={t("chat.error_message.message_send")}>
              <ExclamationCircleFilled className="animate-bounce text-lg text-[#ff4d4f] " />
            </Tooltip>
          ) : (
            <CheckCircleOutlined className="text-gray-600" />
          )}
        </>
      );
    } else if (index_item > 0) {
      return (
        <div className="ml-auto self-end">
          {item.unread === 2 ? (
            <FieldTimeOutlined className="text-gray-600" />
          ) : indexArray && data.length - 1 - indexArray === index_item ? (
            <Tooltip
              placement="topLeft"
              title={getName(selectedConversation?.name, "name")}>
              <>
                <AvatarChat
                  className={selectedConversation?.image ? "" : "mt-1"}
                  fontSize="0.4rem"
                  type="user"
                  key={`react_${selectedConversation?.id}`}
                  hasImage={selectedConversation?.image}
                  height={5}
                  width={5}
                  size={18}
                  url={
                    URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                    selectedConversation?.image
                  }
                  name={getName(selectedConversation?.name, "avatar")}
                />
              </>
            </Tooltip>
          ) : (
            <></>
          )}
        </div>
      );
    } else return <></>;
  };
  return (
    <div className="flex items-center ">
      {selectedConversation?.type === "user"
        ? markedSeenFunction()
        : selectedConversation?.type === "room"
        ? markedSeenFunctionRoom()
        : null}
    </div>
  );
};

export default RenderSeen;
