import dayjs from "dayjs";
import moment from "moment";
import "moment/locale/fr";
import {
  HiPhoneIncoming,
  HiPhoneMissedCall,
  HiPhoneOutgoing,
} from "react-icons/hi";
import { MdPhoneMissed } from "react-icons/md";
import { resetStateOtherUser } from "../../../new-redux/actions/chat.actions";
import { toastNotification } from "../../../components/ToastNotification";
import { getUserListChat } from "../../../new-redux/services/chat.services";
import { URL_ENV } from "index";
import { Badge, Tooltip } from "antd";
import { FaCheckCircle, FaTimesCircle } from "react-icons/fa";
import { moment_timezone } from "App";
import { store } from "new-redux/store";
import momentTimezone from "moment-timezone";

export const billSecToHumanReadable = (billSec, source) => {
  if (!billSec || billSec === "0") return "";

  let totalSeconds = 0;
  let hours = 0;
  let minutes = 0;
  let seconds = 0;

  if (!billSec.includes(":")) {
    totalSeconds = parseInt(billSec, 10);
    seconds = totalSeconds;
  } else {
    const units = billSec.split(":").map((unit) => parseInt(unit, 10) || 0);
    if (units.length === 3) {
      [hours, minutes, seconds] = units;
    } else if (units.length === 2) {
      [minutes, seconds] = units;
    } else if (units.length === 1) {
      [seconds] = units;
    }
    totalSeconds = hours * 3600 + minutes * 60 + seconds;
  }

  if (source === "webPhoneVoiceCall" && totalSeconds > 60) {
    const totalMinutes = Math.floor(totalSeconds / 60);

    if (totalSeconds % 60 === 0) {
      return `${totalMinutes} min`;
    } else {
      return (
        <span>
          {totalMinutes}
          <sup>+</sup> min
        </span>
      );
    }
  }
  if (!billSec.includes(":")) return `${billSec} sec`;

  const formattedHours = hours ? `${hours} h ` : "";
  const formattedMinutes = minutes ? `${minutes} min ` : "";
  const formattedSeconds = seconds ? `${seconds} sec` : "";

  return `${formattedHours}${formattedMinutes}${formattedSeconds}`.trim();
};

//
export const getFirst2Chart = (name) => {
  return name
    ?.split(" ")
    .slice(0, 2)
    .map((word) => word.charAt(0).toUpperCase())
    .join("");
};
//
export const disabledTime = (current) => {
  const now = moment();
  if (current && current.isSame(now, "day")) {
    return {
      disabledHours: () => [...Array(24).keys()].slice(now.hours() + 1),
      disabledMinutes: () => [...Array(60).keys()].slice(now.minutes() + 1),
      // disabledSeconds: () => [...Array(60).keys()],
    };
  }
  return null;
};
//
export const disabledDate = (dateToStart, current) => {
  const today = dayjs().endOf("day");
  const specificDate = dayjs(dateToStart).startOf("day");
  return current.isAfter(today) || current.isBefore(specificDate);
};
//
export const humanDate = (callDate, t, source, hideTimeForLastYear = false) => {
  const formats = [
    "YYYY-MM-DD HH:mm",
    "YYYY-MM-DD hh:mm A",
    "DD-MM-YYYY HH:mm",
    "DD-MM-YYYY hh:mm A",
    "MM-DD-YYYY HH:mm",
    "MM-DD-YYYY hh:mm A",
    "DD.MM.YYYY HH:mm",
    "DD.MM.YYYY hh:mm A",
    "DD/MM/YYYY HH:mm",
    "DD/MM/YYYY hh:mm A",
    "MM.DD.YYYY HH:mm",
    "MM.DD.YYYY hh:mm A",
    "MM/DD/YYYY HH:mm",
    "MM/DD/YYYY hh:mm A",
    moment.ISO_8601,
  ];

  let date = moment(callDate, formats, true);

  if (!date.isValid()) {
    return callDate;
  }
  let now = moment();
  let startOfWeek = moment().startOf("week");
  let endOfWeek = moment().endOf("week");
  let currentYear = moment().format("YYYY");
  let lastYear = moment().subtract(1, "year").format("YYYY");

  if (date.isSame(now, "day")) {
    return source === "table"
      ? `${t("voip.today")} ${t("voip.at")} ${date.format("LT")}`
      : date.format("LT");
  } else if (date.isBetween(startOfWeek, endOfWeek)) {
    return date.calendar(now, {
      lastDay: `[${t("voip.yesterday_at")}] LT`,
      lastWeek: `dddd [${t("voip.at")}] LT`,
      sameElse: function (now) {
        if (this.year() === now.year()) {
          return `ddd D MMM [${t("voip.at")}] LT`;
        } else {
          return `ddd D MMM YYYY [${t("voip.at")}] LT`;
        }
      },
    });
  } else {
    let dayMonth;
    if (date.format("YYYY") === currentYear) {
      dayMonth = date.format("ddd D MMM");
    } else if (date.format("YYYY") === lastYear && hideTimeForLastYear) {
      dayMonth = date.format("ddd D MMM YYYY");
      return dayMonth; // Return only the date without time
    } else {
      dayMonth = date.format("ddd D MMM YYYY");
    }
    let time = date.format("LT");
    return `${dayMonth} ${t("voip.at")} ${time}`;
  }
};

export const noteDate = (
  callDate,
  t,
  dateConfig,
  dateTimeConfig,
  dateTimeZoneConfig
) => {
  try {
    // Validate the timezone
    const validTimeZone = momentTimezone.tz.zone(dateTimeZoneConfig)
      ? dateTimeZoneConfig
      : "UTC";

    // Parse the date from ISO format, assume it's UTC
    const utcDate = momentTimezone.utc(callDate);

    // Convert UTC date to the user's timezone
    let localDate = utcDate.clone().tz(validTimeZone);

    // Validate the converted date
    if (!localDate.isValid()) {
      console.warn("Invalid date or timezone:", callDate, validTimeZone);
      return callDate; // Return raw input if invalid
    }

    // Check if one-hour adjustment is needed
    const adjustedDate = localDate.clone().add(1, "hour");
    if (adjustedDate.isValid() && adjustedDate.hour() !== localDate.hour()) {
      localDate = adjustedDate; // Apply the adjustment if discrepancy detected
    }

    // Get the current date in the user's timezone
    const now = momentTimezone().tz(validTimeZone);

    // If the date is today, return only the time
    if (localDate.isSame(now, "day")) {
      return localDate.format(dateTimeConfig);
    }

    // Return formatted date and time for other days
    return `${localDate.format(dateConfig)} ${localDate.format(
      dateTimeConfig
    )}`;
  } catch (error) {
    console.error("Error processing date:", error);
    return callDate; // Fallback in case of unexpected errors
  }
};

//
export function formatDateComparison(date, t) {
  const currentDate = moment(date);
  const startOfWeek = moment().startOf("week");
  const endOfWeek = moment().endOf("week");

  if (currentDate.isSame(moment(), "day")) {
    return t("voip.today");
  }

  if (currentDate.isSame(moment().subtract(1, "days"), "day")) {
    return t("voip.yesterday");
  }

  if (currentDate.isBetween(startOfWeek, endOfWeek, "day", "[]")) {
    return currentDate.format("dddd");
  }

  return currentDate.format("dddd, DD-MM-YYYY");
}
//
export function formatDateComparisonWebPhone(date, t) {
  const currentDate = moment(date);
  const startOfWeek = moment().startOf("week");
  const endOfWeek = moment().endOf("week");
  const currentYear = moment().year();

  if (currentDate.isSame(moment(), "day")) {
    return t("voip.today");
  }

  if (currentDate.isSame(moment().subtract(1, "days"), "day")) {
    return t("voip.yesterday");
  }

  if (currentDate.isBetween(startOfWeek, endOfWeek, "day", "[]")) {
    return currentDate.format("ddd");
  }

  if (currentDate.year() === currentYear) {
    return currentDate.format("ddd D MMM");
  } else {
    return currentDate.format("ddd D MMM YYYY");
  }
}
//
export function formatDateComparisonTableFamily(date) {
  const currentDate = moment(date);
  if (!currentDate.isValid()) {
    return date;
  }

  const currentYear = moment().year();
  if (currentDate.year() === currentYear) {
    return currentDate.format("ddd D MMM");
  } else {
    return currentDate.format("ddd D MMM YYYY");
  }
}
//
export const rangePresets = (t) => [
  {
    label: t("voip.yesterday"),
    value: [
      dayjs().startOf("day").add(-1, "d"),
      dayjs().endOf("day").add(-1, "d"),
    ],
  },
  {
    label: t("voip.today"),
    value: [dayjs().startOf("day"), dayjs().endOf("day")],
  },
  {
    label: t("voip.today&yesterday"),
    value: [dayjs().startOf("day").add(-1, "d"), dayjs().endOf("day")],
  },
  {
    label: t("voip.currentWeek"),
    value: [dayjs().startOf("week"), dayjs().endOf("day")],
  },
  {
    label: t("voip.currentMonth"),
    value: [dayjs().startOf("month"), dayjs().endOf("day")],
  },
  {
    label: t("voip.last7days"),
    value: [dayjs().subtract(7, "day").startOf("day"), dayjs().endOf("day")],
  },
  {
    label: t("voip.last14days"),
    value: [dayjs().subtract(14, "day").startOf("day"), dayjs().endOf("day")],
  },
  {
    label: t("voip.last30days"),
    value: [dayjs().subtract(30, "day").startOf("day"), dayjs().endOf("day")],
  },
  {
    label: t("voip.last90days"),
    value: [dayjs().subtract(90, "day").startOf("day"), dayjs().endOf("day")],
  },
];
//
export const handleDateTimeRange = (
  dateString,
  setState,
  isTimeDisplayed,
  userConfigDate
) => {
  let start, end;

  const formatWithTime = "YYYY-MM-DD HH:mm"; // Backend format with time
  const formatWithoutTime = "YYYY-MM-DD"; // Backend format without time

  const userDateFormat = userConfigDate?.date_format || formatWithoutTime;
  const userTimeFormat = userConfigDate?.time_format || "HH:mm";
  const combinedFormat = `${userDateFormat} ${userTimeFormat}`;

  if (isTimeDisplayed) {
    start = moment(dateString[0], combinedFormat).format(formatWithTime);
    end = moment(dateString[1], combinedFormat).format(formatWithTime);
  } else {
    start = moment(dateString[0], userDateFormat)
      .startOf("day")
      .format(formatWithTime);
    end = moment(dateString[1], userDateFormat)
      .endOf("day")
      .format(formatWithTime);
  }

  setState([
    start === "Invalid date" ? null : start,
    end === "Invalid date" ? null : end,
  ]);
};
//
export const formatDatePickerRange = (showTime, configDate) => {
  const dateFormat = configDate?.date_format || "YYYY-MM-DD";
  const timeFormat = configDate?.time_format || "HH:mm";
  if (showTime) return `${dateFormat} ${timeFormat}`;
  else return dateFormat;
};
//
function isNumber(s) {
  return !isNaN(parseFloat(s)) && isFinite(s);
}
//
export const extractPhoneNum = (phoneNum) => {
  const result = [];
  const len = phoneNum?.length;
  if (!len || !Array.isArray(phoneNum)) return result;
  phoneNum?.forEach((num) => {
    if (Array.isArray(num)) {
      let len2 = num.length;
      let [dial, number] = num;
      if (len2 === 2) {
        if (isNumber(dial) && isNumber(number)) {
          result.push(`(${dial}) ${number}`);
          return;
        }
        if (!isNumber(dial) && isNumber(number)) {
          result.push(`${number}`);
          return;
        }
      }
      if (len2 === 1 && isNumber(dial)) {
        result.push(`${dial}`);
        return;
      }
    }
    if (typeof num === "string" && isNumber(num)) {
      result.push(num);
      return;
    }
  });
  return result;
};
//
export const transformDataToCreateTags = (arr) => {
  let obj = {};

  arr.forEach((item) => {
    let splitItem = item.split("-");

    if (obj[splitItem[0]]) {
      obj[splitItem[0]].push(splitItem[1]);
    } else {
      obj[splitItem[0]] = [splitItem[1]];
    }
  });

  let output = Object.entries(obj)
    .map(([key, values]) => `${key}=>[${values.join(", ")}]`)
    .join("*");
  return output;
};
//
export const adjustPhoneNumber = (phoneNumber, prefix) => {
  if (!phoneNumber) return null;
  if (!prefix) return phoneNumber;

  const newPrefix = prefix.replace("+", "00");

  const prefixLength = newPrefix.length;

  if (phoneNumber.substring(0, prefixLength) === newPrefix)
    return phoneNumber.substring(prefixLength);
  else return phoneNumber;
};
// handle Call Log
const DISPOSITION_ANSWERED = "ANSWERED";
const DISPOSITION_NO_ANSWER = "NO ANSWER";
const DISPOSITION_BUSY = "BUSY";
const DISPOSITION_FAILED = "FAILED";
const DISPOSITION_CONGESTION = "CONGESTION";
const DISPOSITION_IN_PROGRESS = "IN_PROGRESS";
const CALL_MADE = "voip.callMade";
const CALL_RECEIVED = "voip.callReceived";

const createIcon = (className, IconComponent, isInProcess) =>
  isInProcess ? (
    <Badge
      offset={[3, 0]}
      count={
        <Badge
          status="processing"
          styles={{
            indicator: {
              height: 7,
              width: 7,
            },
          }}
        />
      }
    >
      <IconComponent className={className} />
    </Badge>
  ) : (
    <IconComponent className={className} />
  );

export const dispositionInfo = (item, isMe, t, source) => {
  let baseKey = isMe ? `${CALL_MADE}` : `${CALL_RECEIVED}`;
  let icon, tooltipMsg, disposition;

  switch (item.disposition) {
    case DISPOSITION_ANSWERED:
      tooltipMsg = `${t(`${baseKey}Answered`)} ${billSecToHumanReadable(
        item.ringingsec
      )}`;
      icon = isMe
        ? createIcon(
            `h-[${source === "webPhone" ? 17 : 18}px] w-[${
              source === "webPhone" ? 17 : 18
            }px] cursor-help fill-blue-600`,
            HiPhoneOutgoing
          )
        : createIcon(
            `h-[${source === "webPhone" ? 17 : 18}px] w-[${
              source === "webPhone" ? 17 : 18
            }px] cursor-help fill-green-600`,
            HiPhoneIncoming
          );
      break;
    case DISPOSITION_NO_ANSWER:
    case DISPOSITION_BUSY:
      tooltipMsg = `${t(`${baseKey}NotAnswered`)} ${billSecToHumanReadable(
        item.ringingsec
      )}`;
      if (!isMe) {
        icon = createIcon(
          `h-[${source === "webPhone" ? 17 : 18}px] w-[${
            source === "webPhone" ? 17 : 18
          }px] cursor-help fill-red-500`,
          MdPhoneMissed
        );
        disposition = "missed";
      } else {
        icon = createIcon(
          `h-[${source === "webPhone" ? 17 : 18}px] w-[${
            source === "webPhone" ? 17 : 18
          }px] cursor-help fill-gray-400`,
          HiPhoneOutgoing
        );
      }
      break;
    case DISPOSITION_FAILED:
    case DISPOSITION_CONGESTION:
      tooltipMsg = t(`${baseKey}Failed`);
      if (!isMe) {
        icon = createIcon(
          `h-[${source === "webPhone" ? 17 : 18}px] w-[${
            source === "webPhone" ? 17 : 18
          }px] cursor-help fill-red-500`,
          HiPhoneMissedCall
        );
        disposition = "missed";
      } else {
        icon = createIcon(
          `h-[${source === "webPhone" ? 17 : 18}px] w-[${
            source === "webPhone" ? 17 : 18
          }px] cursor-help fill-gray-400`,
          HiPhoneMissedCall
        );
      }
      break;
    case DISPOSITION_IN_PROGRESS:
      tooltipMsg = t("voip.inProcess");
      disposition = "IN_PROGRESS";
      icon = isMe
        ? createIcon(
            `h-[${source === "webPhone" ? 17 : 18}px] w-[${
              source === "webPhone" ? 17 : 18
            }px] cursor-help fill-blue-600`,
            HiPhoneOutgoing
          )
        : createIcon(
            `h-[${source === "webPhone" ? 17 : 18}px] w-[${
              source === "webPhone" ? 17 : 18
            }px] cursor-help fill-green-600`,
            HiPhoneIncoming,
            true
          );
      break;
    default:
      tooltipMsg = "";
      icon = null;
  }
  return { tooltipMsg, icon, disposition };
};

const handleForwardingInfo = (item, prefix) => {
  if (item.type_appel === "31") {
    return {
      isForwarding: true,
      forwardingInfo: {
        id: item.dst_forwarding_id || null,
        name: item.dst_forwarding_name?.replaceAll("_", " ") || null,
        familyId: item.dst_forwarding_family || null,
        image:
          item?.dst_forwarding_image &&
          `${
            URL_ENV?.REACT_APP_BASE_URL + URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
          }${item?.dst_forwarding_image}`,
        number: adjustPhoneNumber(item.dst_forwarding, prefix),
      },
    };
  }
  return {};
};

const handleQueueInfo = (item) => {
  if (item.type_appel === "2") {
    return {
      isQueue: true,
      isQueueTreated: item.etat_queue === "1" ? true : false,
      queueInfo: {
        name: item.queue_name || null,
        image: null,
        number: item.queue_num || null,
      },
    };
  }
  return {};
};

const handleGroupInfo = (item) => {
  if (item.type_appel === "7") {
    return {
      isGroup: true,
      isGroupTreated: item?.disposition === "ANSWERED" ? true : false,
      groupInfo: {
        name: item.group_name || null,
        image: null,
        number: item.group_num || null,
      },
    };
  }
  return {};
};

const handleCallStatus = (item, t) => {
  const check =
    item?.disposition === "NO ANSWER" ||
    item?.disposition === "BUSY" ||
    item?.disposition === "FAILED";
  if (check) {
    const status = item?.call_status;
    const date = item?.returned_date
      ? humanDate(item?.returned_date, t, "table")
      : null;
    switch (status) {
      case "returned today":
        return {
          checkedIcon: (
            <Tooltip
              title={
                <div className="flex flex-col ">
                  <span>{t("voip.recalled_today_answered")}</span>
                  <span className="font-semibold">{date ?? null}</span>
                </div>
              }
              placement="topRight"
            >
              <FaCheckCircle
                className="text-green-700"
                style={{ fontSize: 15, cursor: "help" }}
              />
            </Tooltip>
          ),
        };
      case "returned":
        return {
          checkedIcon: (
            <Tooltip
              title={
                <div className="flex flex-col ">
                  <span>{t("voip.recalled_answered")}</span>
                  <span className="font-semibold">{date ?? null}</span>
                </div>
              }
              placement="topRight"
            >
              <FaCheckCircle
                className="text-green-400"
                style={{ fontSize: 15, cursor: "help" }}
              />
            </Tooltip>
          ),
        };
      case "not returned":
        return {
          checkedIcon: (
            <Tooltip title={`${t("voip.not_recalled")}`} placement="topRight">
              <FaTimesCircle
                className={"text-red-500"}
                style={{ fontSize: 15, cursor: "help" }}
              />
            </Tooltip>
          ),
        };
      case "returned unreachable":
        return {
          checkedIcon: (
            <Tooltip
              title={
                <div className="flex flex-col ">
                  <span>{t("voip.recalled_not_answered")}</span>
                  <span className="font-semibold">{date ?? null}</span>
                </div>
              }
              placement="topRight"
            >
              <FaCheckCircle
                className="text-orange-400"
                style={{ fontSize: 15, cursor: "help" }}
              />
            </Tooltip>
          ),
        };
      default:
        return {};
    }
  } else return {};
};

const handleInternalForwarding = (item, poste) => {
  if (
    (item.forwarding_type === "internalForwarding" ||
      item.forwarding_type === "BlindForwarding") &&
    item.src_forwarding.length
  ) {
    return {
      isInternalForwarding: item.forwarding_type === "internalForwarding",
      isInternalTransfer: item.forwarding_type === "BlindForwarding",
      internalForwardingInfo: [
        ...(poste === `${item.src}`
          ? [
              ...item.src_forwarding.map((element) => ({
                id: element.src_forwarding_id,
                uuid: element.src_forwarding_uuid,
                name: element?.src_forwarding_name?.replaceAll("_", " "),
                familyId: element.src_forwarding_family,
                image:
                  element.src_forwarding_image &&
                  `${
                    URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                  }${element.src_forwarding_image}`,
                number: element.src_forwarding_num,
              })),
              {
                uuid: item.dst_uuid,
                id: item.dst_id,
                familyId: item.dst_family,
                number: item.dst,
                image:
                  item.dst_image &&
                  `${
                    URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                  }${item.dst_image}`,
                name: item.dst_name?.replaceAll("_", " "),
              },
            ]
          : [
              {
                uuid: item.src_uuid,
                id: item.src_id,
                familyId: item.src_family,
                number: item.src,
                image:
                  item.src_image &&
                  `${
                    URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                  }${item.src_image}`,
                name: item.src_name?.replaceAll("_", " "),
              },
              ...item.src_forwarding.map((element) => ({
                id: element.src_forwarding_id,
                uuid: element.src_forwarding_uuid,
                name: element?.src_forwarding_name?.replaceAll("_", " "),
                familyId: element.src_forwarding_family,
                image:
                  element.src_forwarding_image &&
                  `${
                    URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                  }${element.src_forwarding_image}`,
                number: element.src_forwarding_num,
              })),
              // {
              //   uuid: item.src_uuid,
              //   id: item.src_id,
              //   familyId: item.src_family,
              //   number: item.src,
              //   image:
              //     item.src_image &&
              //     `${
              //       URL_ENV?.REACT_APP_BASE_URL +
              //       URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
              //     }${item.src_image}`,
              //   name: item.src_name?.replaceAll("_", " "),
              // },
              {
                uuid: item.dst_uuid,
                id: item.dst_id,
                familyId: item.dst_family,
                number: item.dst,
                image:
                  item.dst_image &&
                  `${
                    URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                  }${item.dst_image}`,
                name: item.dst_name?.replaceAll("_", " "),
              },
            ]),
      ],

      // internalForwardingInfo: [
      //   ...(poste === `${item.src}` || item?.blind_forwarding_type === 0
      //     ? // || item?.blind_forwarding_type === 1
      //       []
      //     : [
      //         {
      //           uuid: item.src_uuid,
      //           id: item.src_id,
      //           familyId: item.src_family,
      //           number: item.src,
      //           image:
      //             item.src_image &&
      //             `${
      //               URL_ENV?.REACT_APP_BASE_URL +
      //               URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
      //             }${item.src_image}`,
      //           name: item.src_name?.replaceAll("_", " "),
      //         },
      //       ]),
      //   ...item.src_forwarding.map((element) => ({
      //     id: element.src_forwarding_id,
      //     uuid: element.src_forwarding_uuid,
      //     name: element?.src_forwarding_name?.replaceAll("_", " "),
      //     familyId: element.src_forwarding_family,
      //     image:
      //       element.src_forwarding_image &&
      //       `${
      //         URL_ENV?.REACT_APP_BASE_URL +
      //         URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
      //       }${element.src_forwarding_image}`,
      //     number: element.src_forwarding_num,
      //   })),
      //   {
      //     uuid: item.dst_uuid,
      //     id: item.dst_id,
      //     familyId: item.dst_family,
      //     number: item.dst,
      //     image:
      //       item.dst_image &&
      //       `${
      //         URL_ENV?.REACT_APP_BASE_URL +
      //         URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
      //       }${item.dst_image}`,
      //     name: item.dst_name?.replaceAll("_", " "),
      //   },
      // ],
    };
  } else {
  }
};

export const handleLogsDataNew = (data, user, t, source) => {
  if (!Array.isArray(data) || !data.length) return [];
  const poste = `${user.extension}` ?? null;
  const userId = `${user.id}` ?? null;
  const prefix = user.location.dial_code ?? null;
  let lastDisplayDate = null;
  return data.map((item) => {
    const { tooltipMsg, icon, disposition } = dispositionInfo(
      item,
      userId === item.src_id,
      t,
      source
    );
    const forwardingInfo = handleForwardingInfo(item, prefix);
    const queueInfo = handleQueueInfo(item);
    const groupInfo = handleGroupInfo(item);
    const internalForwarding = handleInternalForwarding(item, poste);
    const callStatus = source === "table" ? handleCallStatus(item, t) : null;
    //
    //
    let dividerDate = formatDateComparisonWebPhone(item.calldate_start, t);
    dividerDate = dividerDate?.includes(",")
      ? dividerDate.split(",")[1]?.trim()
      : dividerDate;

    if (lastDisplayDate !== dividerDate) {
      lastDisplayDate = dividerDate;
    } else {
      dividerDate = false;
    }
    //
    let log = {
      voice_mail: item?.voice_mail
        ? {
            ...item?.voice_mail,
            audio: item?.voice_mail?.chemain_fichier_audio,
            state: item?.voice_mail?.etat,
            duration:
              item?.voice_mail?.duration === "00:00:00"
                ? null
                : item?.voice_mail?.duration,
          }
        : null,
      key: item._id,
      _id: item._id,
      id: item.id,
      id_appel: item.id_appel,
      callSense: item.src_id === userId ? "outgoing" : "incoming",
      humanDate: humanDate(item.calldate_start, t),
      date: item.calldate_start,
      ...(source === "webPhone"
        ? {
            dividerDate: dividerDate,
            time: moment(item?.calldate_start).format("LT"),
          }
        : {
            affectation: item.affectation,
            tags: item.tags,
            tasks: item.tasks,
          }),
      audioRecording: item.id_enregistrement || null,
      callDuration:
        item.disposition === DISPOSITION_ANSWERED ? item.billsec : null,
      tooltipMsg,
      icon,
      ...(disposition ? { disposition } : {}),
      ...(item.type_appel === "7" && item.src === poste
        ? {
            callInfo: {
              uuid: item?.amaflags_uuid,
              id: item?.amaflags_id,
              familyId: item?.amaflags_family,
              number: item?.amaflags,
              image: item?.amaflags_image
                ? `${
                    URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                  }${item?.amaflags_image}`
                : null,
              name: item?.amaflags_name?.replaceAll("_", " "),
            },
          }
        : {
            callInfo: {
              uuid: item.src_id === userId ? item.dst_uuid : item.src_uuid,
              id: item.src_id === userId ? item.dst_id : item.src_id,
              familyId:
                item.src_id === userId ? item.dst_family : item.src_family,
              number: item.src_id === userId ? item.dst : item.src,
              image:
                (item.src_id === userId ? item.dst_image : item.src_image) &&
                `${
                  URL_ENV?.REACT_APP_BASE_URL +
                  URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                }${item.src_id === userId ? item.dst_image : item.src_image}`,
              name: (item.src_id === userId
                ? item.dst_name
                : item.src_name
              )?.replaceAll("_", " "),
            },
          }),
      ...forwardingInfo,
      ...queueInfo,
      ...groupInfo,
      ...internalForwarding,
      ...(item.src !== poste && source === "table" ? callStatus : {}),
    };

    // Handle case when the colleague call you from his/her poste on your personnel phone number
    if (item.type_appel === "0" && poste !== item.src) {
      log.isOnTheNum = true;
      log.onTheNum = item.dst;
    }
    if (item.type_appel === "3") {
      log.conf = true;
    }
    log.firstTime = item.first_time ? true : false;

    return log;
  });
};
//
export const handleLogGroupsQueues = (data, user, t) => {
  if (!Array.isArray(data) || !data.length) return [];

  const baseUrlImg =
    URL_ENV?.REACT_APP_BASE_URL + URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL;

  return data.map((item, i) => {
    const { tooltipMsg, icon, disposition } = dispositionInfo(
      item,
      user?.id === item?.src_id,
      t
    );

    const createImageUrl = (image) => (image ? `${baseUrlImg}${image}` : null);
    const cleanerName = (name) => (name ? name.replaceAll("_", " ") : null);
    const isTreated = (item) => {
      if (item.disposition === "IN_PROGRESS") return true;
      // case queue
      if (item.type_appel === "2") {
        if (item.etat_queue === "1") return true;
        else return false;
      }
      // case group
      else if (item.type_appel === "7") {
        if (item.disposition === "ANSWERED") return true;
        else return false;
      }
    };

    const isReturned = (item, t) => {
      const check =
        item?.disposition === "NO ANSWER" ||
        item?.disposition === "BUSY" ||
        item?.disposition === "FAILED";
      if (check && item.returned_by) {
        const statusAndIcon = () => {
          let status, icon;
          if (item?.call_status === "returned today") {
            status = t("voip.recalled_today_answered");
            icon = (
              <FaCheckCircle
                className="text-green-700"
                style={{ fontSize: 15, cursor: "help" }}
              />
            );
          } else {
            status = t("voip.recalled_answered");
            icon = (
              <FaCheckCircle
                className="text-green-400"
                style={{ fontSize: 15, cursor: "help" }}
              />
            );
          }
          return { status, icon };
        };
        const date = item?.returned_date
          ? humanDate(item?.returned_date, t, "table")
          : null;
        return {
          returned: {
            status: statusAndIcon()?.status,
            date: date,
            id: item.returned_by_id,
            familyId: item.returned_by_family,
            uuid: item.returned_by_uuid,
            name: cleanerName(item.returned_by_name),
            image: createImageUrl(item.returned_by_image),
            number: item.returned_by,
            audio: item.returned_recording,
            displayIcon: statusAndIcon().icon,
          },
        };
      } else return {};
    };

    return {
      key: i,
      _id: item._id,
      id_appel: item.id_appel,
      affectation: item.affectation,
      tags: item.tags,
      isGroup: item.type_appel === "7" || item.type === "GROUP",
      isQueue: item.type_appel === "2" || item.type === "QUEUE",
      isConf: item.type_appel === "3",
      groupInfo: {
        name: item.queue_name
          ? item.queue_name
          : item.group_name
          ? item.group_name
          : item.dst_name,
        number: item.queue_num
          ? item.queue_num
          : item.group_num
          ? item.group_num
          : item.dst,
        image: createImageUrl(item.queue_image || item.group_image),
      },
      src: {
        id: item.src_id,
        uuid: item.src_uuid,
        familyId: item.src_family,
        name: cleanerName(item.src_name),
        number: item.src,
        image: createImageUrl(item.src_image),
      },
      dst: {
        id: item.dst_id,
        uuid: item.dst_uuid,
        familyId: item.dst_family,
        name: cleanerName(item.dst_name),
        number: item.dst,
        image: createImageUrl(item.dst_image),
      },
      isTreatedByMe:
        item.dst_id === user?.id || item.returned_by_id === user?.id,
      humanDate: humanDate(item.calldate_start, t),
      date: item.calldate_start,
      waitingTime: billSecToHumanReadable(item.ringingsec),
      audioRecording: item.id_enregistrement || null,
      callDuration:
        item.disposition === DISPOSITION_ANSWERED ? item.billsec : null,
      tooltipMsg,
      icon,
      disposition,
      isTreated: isTreated(item),
      ...isReturned(item, t),
      isMeTheSrc: item.src_id === user?.id,
    };
  });
};

//
export const CenterContents = ({ vertical, horizontal, children }) => (
  <div
    key={children}
    style={{
      display: "flex",
      alignItems: vertical,
      justifyContent: horizontal,
      height: "2.5rem",
    }}
  >
    {children}
  </div>
);
//
export const handlePageSizeOptions = (total, limit = 150) => {
  const pageSize = [20, 50, 100, 150].filter(
    (size) => size <= total && size <= limit
  );
  const maxAllowedSize = Math.min(total, limit);

  if (
    maxAllowedSize > (pageSize[pageSize.length - 1] || 0) &&
    !pageSize.includes(maxAllowedSize)
  ) {
    pageSize.push(maxAllowedSize);
  }
  return pageSize;
};
//
export const handleMsgClick = async (dispatch, setOpenChat, userList, obj) => {
  // console.log({ setOpenChat, userList, obj });
  if (!userList?.length) dispatch(getUserListChat());
  const userInfo = userList?.find(
    (user) =>
      (user?.uuid && user?.uuid === obj?.uuid) ||
      Number(user?.post_number) === Number(obj?.post_number)
  );
  if (!userInfo) {
    toastNotification("error", "Cannot find this user", "topRight");
    return;
  }

  dispatch(
    resetStateOtherUser({
      forced: true,
      keepDrawerOpened: false,
      item: {
        _id: userInfo?._id,
        type: "user",
        participants: true,
      },
    })
  );

  setOpenChat(true);
};

export const areArraysEqual = (arr1, arr2) => {
  if (arr1?.length !== arr2?.length) {
    return false;
  }

  let map = {};

  for (let i = 0; i < arr1.length; i++) {
    // If the item is already in the map, increment its count, otherwise set its count to 1.
    map[arr1[i]] = (map[arr1[i]] || 0) + 1;
  }

  for (let i = 0; i < arr2.length; i++) {
    // If the item is not in the map, then the arrays are not the same.
    if (!map[arr2[i]]) {
      return false;
    }
    // Decrement the count of the item in the map.
    map[arr2[i]] -= 1;
  }

  // Check if all counts in the map are 0, meaning both arrays have the same items.
  for (let key in map) {
    if (map[key] !== 0) {
      return false;
    }
  }

  return true;
};
//
export const truncateString = (str, maxChars, displayTooltip = false) => {
  if (!str || typeof str !== "string") return "";
  const isTruncated = str.length > (maxChars || str.length);
  const truncatedText = isTruncated ? str.slice(0, maxChars) + "..." : str;
  if (isTruncated && displayTooltip) {
    return (
      <Tooltip title={str}>
        <span className="cursor-help">{truncatedText}</span>
      </Tooltip>
    );
  }
  return truncatedText;
};
//
export const truncateFileName = (fileName, maxChars) => {
  if (fileName.length <= maxChars) {
    return fileName;
  }

  const extIndex = fileName.lastIndexOf(".");
  const hasExtension = extIndex !== -1 && extIndex !== 0;

  let baseName, extension;

  if (hasExtension) {
    baseName = fileName.substring(0, extIndex);
    extension = fileName.substring(extIndex);
  } else {
    baseName = fileName;
    extension = "";
  }

  const maxBaseNameLength = maxChars - extension.length;

  if (maxBaseNameLength <= 0) {
    return fileName.substring(0, maxChars);
  }

  if (baseName.length <= maxBaseNameLength) {
    return baseName + extension;
  }

  const ellipsis = "..";
  const maxBaseNameWithEllipsis = maxBaseNameLength - ellipsis.length;

  if (maxBaseNameWithEllipsis <= 0) {
    return baseName.substring(0, maxBaseNameLength) + extension;
  }

  return baseName.substring(0, maxBaseNameWithEllipsis) + ellipsis + extension;
};
//
export const getColumnWidth = (tableRef, index) => {
  const columns = tableRef?.current?.querySelectorAll(".ant-table-cell");
  const columnWidth = columns?.[index]?.offsetWidth;
  return columnWidth;
};
//
export const suggestLog = (log, userPoste, t) => {
  const countMap = new Map();

  log.forEach((item) => {
    const keyToCheck = item.src === userPoste ? "dst" : "src";
    const key = item[keyToCheck];
    if (!countMap.has(key)) {
      countMap.set(key, {
        count: 0,
        item: {
          date: item[`${keyToCheck}_family`]
            ? null
            : humanDate(item?.calldate_start, t),
          family_id: item[`${keyToCheck}_family`] || null,
          id: item[`${keyToCheck}_id`] || null,
          baseImg: item[`${keyToCheck}_image`]
            ? item[`${keyToCheck}_image`]
            : null,
          image: item[`${keyToCheck}_image`]
            ? `${
                URL_ENV?.REACT_APP_BASE_URL +
                URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
              }${item[`${keyToCheck}_image`]}`
            : null,
          name: item[`${keyToCheck}_name`]?.replaceAll("_", " ") || null,
          phone: {
            displayNum: item[`${keyToCheck}`],
            callNum: item[`${keyToCheck}`],
            copyNum: item[`${keyToCheck}`],
          },
          uuid: item[`${keyToCheck}_uuid`] || null,
          extension: null,
        },
      });
    }
    countMap.get(key).count++;
  });

  const resultArray = Array.from(countMap.values()).map(({ count, item }) => {
    return { ...item, repeated: count };
  });

  resultArray.sort((a, b) => b.repeated - a.repeated);

  return resultArray;
};

export const generateUrlToView360 = (familyId, id, version) => {
  let basePath;
  const castFamilyId = Number(familyId) || null;
  switch (castFamilyId) {
    case 1:
      basePath = `/companies`;
      break;
    case 2:
      basePath = `/contacts`;
      break;
    case 3:
      basePath = `/deals`;
      break;
    case 4:
      basePath = `/settings/users`;
      break;
    case 5:
      basePath = `/setting/products`;
      break;
    case 6:
      basePath = `/tickets`;
      break;
    case 7:
      basePath = `/projects`;
      break;
    case 8:
      basePath = `/booking`;
      break;
    case 9:
      basePath = `/leads`;
      break;
    case 11:
      basePath = `/invoices`;
      break;
    case 12:
      basePath = `/transaction`;
      break;
    default:
      return null;
  }

  if (version === "v2") {
    basePath += `/v2`;
  }

  return `${basePath}/${id}`;
};

export const checkIfPathOnView360 = (path) => {
  const validPaths = [
    "/companies",
    "/contacts",
    "/deals",
    "/leads",
    "/tickets",
    "/settings/users",
    "/booking",
    "/projects",
    "/settings/products",
    "/invoices",
    "/transaction",
  ];
  const pathRegex = new RegExp(
    `^(${validPaths.join("|")})(/v2)?/([0-9a-fA-F]{24})$`
  );
  return pathRegex.test(path);
};
//
export const actionCallFromRmc = async (number, date, actionCall) => {
  const currentDate = Date.now();
  const dateTimeZone = moment_timezone(+date).valueOf();
  const difference = currentDate - dateTimeZone;
  const isAlreadyOnCall = await store.getState()?.voipBlackList?.receiverInfo;
  if (difference < 15000 && !isAlreadyOnCall) {
    actionCall(number);
  }
};

export const findNextZIndex = () => {
  try {
    const openDrawers = document.querySelectorAll(".ant-drawer-open");

    if (openDrawers.length === 0) {
      return 1000;
    }

    let highestZIndex = 0;

    openDrawers.forEach((drawer) => {
      const zIndex = window.getComputedStyle(drawer).zIndex;

      if (parseInt(zIndex) > highestZIndex) {
        highestZIndex = parseInt(zIndex);
      }
    });

    return Math.min(1039, highestZIndex + 1);
  } catch (error) {
    // console.error("An error occurred while calculating the z-index:", error);
    return 100;
  }
};
//
export const convertHtmlToText = (html) => {
  const tempElement = document.createElement("div");
  tempElement.innerHTML = html;
  return tempElement.innerText;
};
//
export const hexToRgba = (hex, opacity) => {
  const defaultColor = `rgba(107, 114, 128, ${opacity})`;
  if (hex === null) {
    return defaultColor;
  }

  hex = hex.replace("#", "");

  const hexRegex = /^[0-9A-Fa-f]{6}$/;
  if (!hexRegex.test(hex)) {
    return defaultColor;
  }

  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};
