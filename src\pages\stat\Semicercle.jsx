import React from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";

const Semicercle = ({ pivotData }) => {
  if (
    !pivotData ||
    !pivotData.rowKeys ||
    !pivotData.colKeys ||
    !pivotData.getAggregator
  ) {
    return <p className="text-center text-red-600">No Data</p>;
  }
  const rowKeys = pivotData.rowKeys;
  const colKeys = pivotData.colKeys;
  if (rowKeys.length === 0 && colKeys.length === 0) {
    return <p className="text-center text-red-600">No Data</p>;
  }
  const pieData = (rowKeys.length > 0 ? rowKeys : colKeys)
    .map((entry) => {
      const label = entry.join(" - ") || "Total";
      const otherAxis = rowKeys.length > 0 ? colKeys : rowKeys;

      const sum =
        otherAxis.length > 0
          ? otherAxis.reduce((acc, val) => {
              const r = rowKeys.length > 0 ? entry : val;
              const c = rowKeys.length > 0 ? val : entry;
              return acc + (pivotData.getAggregator(r, c)?.value() || 0);
            }, 0)
          : pivotData.getAggregator(entry, [])?.value() ||
            pivotData.getAggregator([], entry)?.value() ||
            0;
      return { name: label, y: sum };
    })
    .filter((item) => item.y > 0);
  if (pieData.length === 0) {
    return <p className="text-center text-red-600">No Data</p>;
  }
  const options = {
    chart: {
      plotBackgroundColor: null,
      plotBorderWidth: 0,
      plotShadow: false,
    },
    title: {
      text: `
            ${
              pivotData.props.rows.length ? pivotData.props.rows.join(" ") : ""
            }</br>
            ${
              pivotData.props.cols.length
                ? "" + pivotData.props.cols.join(" ")
                : ""
            }`,
      align: "center",
      verticalAlign: "middle",
      y: 60,
      style: {
        fontSize: "1.1em",
      },
    },
    tooltip: {
      pointFormat: "{series.name}: <b>{point.percentage:.1f}%</b>",
    },
    accessibility: {
      point: {
        valueSuffix: "%",
      },
    },
    plotOptions: {
      pie: {
        dataLabels: {
          enabled: true,
          distance: -50,
          style: {
            fontWeight: "bold",
            color: "white",
          },
        },
        startAngle: -90,
        endAngle: 90,
        center: ["50%", "75%"],
        size: "110%",
      },
    },
    credits: {
      enabled: false,
    },
    dataLabels: {
      enabled: true,
    },
    series: [
      {
        type: "pie",
        name: "Browser share",
        innerSize: "50%",
        name: "Share",
        data: pieData,
      },
    ],
  };

  return (
    <div
      style={{ width: "100%", height: "100%" }}
      className="highcharts-figure"
    >
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
};
export default Semicercle;
