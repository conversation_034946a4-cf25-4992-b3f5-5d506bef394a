import { Suspense, lazy, useEffect, useRef, useState } from "react";
import "./../../../../pages/layouts/webphone/index.css";
import { Spin } from "antd";
import { lazyRetry } from "utils/lazyRetry";

const Contacts = lazy(() =>
  lazyRetry(() => import("../contacts/contacts"), "Contacts")
);
const Voicecall = lazy(() =>
  lazyRetry(() => import("../voicecall/VoiceCall"), "Voicecall")
);
const Settings = lazy(() =>
  lazyRetry(() => import("./settings/settings"), "Settings")
);
const CallLogs = lazy(() =>
  lazyRetry(() => import("./journal/callLogs"), "CallLogs")
);
const Call = lazy(() =>
  lazyRetry(() => import("../../../../pages/layouts/webphone/call"), "Call")
);
const KeyPad = lazy(() =>
  lazyRetry(() => import("../../../../pages/layouts/webphone/KeyPad"), "KeyPad")
);
const LoaderSection = () => (
  <div className="flex  h-[23rem] items-center justify-center ">
    <Spin />
  </div>
);
function Main({
  navigate,
  isKeyPadUp,
  setIsKeyPadUp,
  setUpWebphone,
  handleActionWebPhone,
}) {
  //
  const inputRef = useRef(null);
  //
  const [number, setNumber] = useState("");
  const [isCallInputDisabled, setIsCallInputDisabled] = useState(false);

  useEffect(() => {
    isCallInputDisabled && setIsCallInputDisabled(false);
    if (inputRef.current) {
      inputRef.current.focus();
    }
    setNumber("");
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [navigate]);

  return (
    <div className={`scroll_bar relative h-[23rem]`}>
      {navigate === "history" ? (
        <Suspense fallback={<LoaderSection />}>
          <Call
            key={"history-call"}
            setSearch={setNumber}
            search={number}
            navigate={navigate}
            inputRef={inputRef}
          />
        </Suspense>
      ) : null}

      <div className="mt-2">
        {navigate === "team" ? (
          <Suspense fallback={<LoaderSection />}>
            <Contacts
              key={"team"}
              setIsCallInputDisabled={setIsCallInputDisabled}
              handleActionWebPhone={handleActionWebPhone}
            />
          </Suspense>
        ) : navigate === "history" ? (
          <Suspense fallback={<LoaderSection />}>
            <CallLogs
              key={"history-callLogs"}
              setUpWebphone={setUpWebphone}
              handleActionWebPhone={handleActionWebPhone}
            />
          </Suspense>
        ) : navigate === "voice" ? (
          <Suspense fallback={<LoaderSection />}>
            <Voicecall key={"voice"} />
          </Suspense>
        ) : navigate === "settings" ? (
          <Suspense fallback={<LoaderSection />}>
            <Settings key={"settings"} />
          </Suspense>
        ) : navigate === "keyPad" ? (
          <Suspense fallback={<LoaderSection />}>
            <KeyPad
              key={"keyPad"}
              setSearch={setNumber}
              search={number}
              isKeyPadUp={isKeyPadUp}
              setIsKeyPadUp={setIsKeyPadUp}
            />
          </Suspense>
        ) : null}
      </div>
    </div>
  );
}

export default Main;
