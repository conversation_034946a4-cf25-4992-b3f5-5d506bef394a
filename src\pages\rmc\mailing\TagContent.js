import { useEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useLocation } from "react-router-dom";
import {
  Button,
  DatePicker,
  Divider,
  Form,
  Select,
  TimePicker,
  Input,
  Space,
  Popconfirm,
} from "antd";
import { useTranslation } from "react-i18next";
import moment from "moment";

import dayjs from "dayjs";

import { toastNotification } from "../../../components/ToastNotification";
import { areArraysEqual, truncateString } from "../../voip/helpers/helpersFunc";
import ChoiceIcons from "../../components/ChoiceIcons";
import {
  createTagsActivity,
  updateTagsActivity,
} from "../../voip/services/services";
import MainService from "services/main.service";
import { setRefreshMailInbox } from "new-redux/actions/mail.actions";

const { Option } = Select;

/**
 * 
 * @param {*} param0 
* tags =>  for create: null & required for update [tag, tag ....] 
  id => "required": call id "_id"....
  info => not "required", can be null
  data => "required", Array of tags you can get this by "getTags" in services 
  setDataSource => setState if you need to get the return from server after submitting create/update and getting status 200
  setCatchChange => setState if you need to catch the change when status is 200 after submitting for create or update
  setOpen => state to close what you open (drawer, modal, etc) asshole 
 * 
 */

const TagContent = ({
  tags,
  id,
  info,
  data,
  setDataSource,
  setOpen,
  source,
  type,
  setDetailsMail,
  idThread,
  getDetailsMessageInbox,
  getDetailsThreadsCollapse,
  pageDetailsThread,
}) => {
  const dispatch = useDispatch();
  const { logs } = useSelector(({ voip }) => voip);
  const { user } = useSelector(({ user }) => user);
  const { dataAccounts } = useSelector((state) => state.mailReducer);
  const access = user.access || {};
  const location = useLocation();
  //
  const [t] = useTranslation("common");
  const [form] = Form.useForm();
  const [loadingSubmit, setLoadingSubmit] = useState(false);
  const [selectedOptions, setSelectedOptions] = useState([]);
  const [note, setNote] = useState("");
  const [dateTimeTrack, setDateTimeTrack] = useState([]);
  const [disabledUpdate, setDisabledUpdate] = useState(true);
  const usedAccount = useMemo(
    () => dataAccounts?.find((item) => item.selected === true),
    [dataAccounts]
  );

  const updateTags = !!tags?.tags?.tags
    ? tags?.tags?.tags?.map(
        (tag) => `${tag?.id}${tag?.parent_id ? `-${tag?.parent_id}` : ""}`
      ) || []
    : tags?.tags?.map(
        (tag) => `${tag?.id}${tag?.parent_id ? `-${tag?.parent_id}` : ""}`
      ) || [];
  const datePickersToShow = selectedOptions.filter((option) =>
    option.value.includes("-")
  );
  //
  //reset All fields if id call change
  useEffect(() => {
    form.resetFields();
    setSelectedOptions([]);
    setNote("");
  }, [id]);
  // reset form when user clears the selections & track update button if is disabled
  useEffect(() => {
    !selectedOptions?.length && !updateTags?.length && form.resetFields();
    if (!updateTags?.length) {
      setDisabledUpdate(false);
      return;
    }

    const selectedValues = form?.getFieldValue(`${id}tags`);
    const areSame = areArraysEqual(selectedValues, updateTags);
    if (areSame && form?.getFieldValue(`${id}note`) === (tags?.note || "")) {
      setDisabledUpdate(true);
    } else setDisabledUpdate(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedOptions, note]);
  //
  const updateDateTimeTrack = (tag, newDate, newTime) => {
    setDateTimeTrack((prevState) => {
      const existingEntry = prevState.find((entry) => entry.tag === tag);

      if (!existingEntry && newDate) {
        return [...prevState, { tag, date: newDate, time: newTime }];
      }

      if (existingEntry) {
        if (newDate === null) {
          return prevState.filter((entry) => entry.tag !== tag);
        }

        return prevState.map((entry) =>
          entry.tag === tag
            ? {
                ...entry,
                date: newDate !== undefined ? newDate : entry.date,
                time: newTime !== undefined ? newTime : entry.time,
              }
            : entry
        );
      }
      return prevState;
    });
  };
  //

  const onFinish = async (values) => {
    try {
      // console.log({ values });
      setLoadingSubmit(true);

      const formData = new FormData();
      const hasTags = tags?.tags?.length > 0;
      const name = info?.name ? info?.name : info?.number;

      if (!hasTags) {
        formData.append(
          "id",
          type === "inbox" || type === "dropdown" ? id : idThread
        );
      }

      values?.[`${id}tags`]?.forEach((tag) => {
        const date = values?.[`${tag}date`]
          ? dayjs(values?.[`${tag}date`])?.format("YYYY-MM-DD")
          : "";
        const time = values?.[`${tag}time`]
          ? dayjs(values?.[`${tag}time`])?.format("HH:mm")
          : "";

        formData.append(`tags[${tag}]`, `${date || ""} ${time || ""}`);
      });

      formData.append("note", values?.[`${id}note`]);
      formData.append("type", "email");
      formData.append("account_id", usedAccount?.value);
      for (let i = 0; i < usedAccount.departmentId.length; i++) {
        formData.append("departement_id[]", usedAccount.departmentId[i]);
      }

      let response;
      if (updateTags?.length) {
        response = await updateTagsActivity(tags?._id, formData);
      } else {
        response = await createTagsActivity(formData);
      }

      if (type === "dropdown") {
        setDataSource &&
          setDataSource((prevState) =>
            prevState?.map((item) => {
              return item.id === id
                ? { ...item, tags: response?.data?.data }
                : item;
            })
          );
      } else if (type === "details") {
        getDetailsMessageInbox();
        getDetailsThreadsCollapse(5 * pageDetailsThread, false, 1);
        // setDetailsMail((p) => {
        //   let detail = Object.assign({}, p);
        //   detail.data = detail.data.map((item) => {
        //     return item.id === idThread
        //       ? { ...item, tags: response?.data?.data.tags }
        //       : item;
        //   });
        //   return detail;
        // });
      }

      dispatch(setRefreshMailInbox(true));
      form.resetFields();
      setLoadingSubmit(false);
      setOpen(false);

      const successText = hasTags
        ? t("mailing.successUpdate")
        : t("mailing.successQualif");

      toastNotification("success", successText, "topRight");
      // When update tag, no need for "id" attribute
    } catch (err) {
      if (err?.response?.status !== 401 && err?.response?.status !== 422) {
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
      if (err?.response?.status === 422) {
        toastNotification(
          "error",
          `You don't have permission to update this tag`,
          "topRight"
        );
      }

      setLoadingSubmit(false);
      setOpen(false);
      throw new Error(err?.message || err);
    }
  };
  //
  const confirmDeleteTags = async (tagsId) => {
    var formData = new FormData();
    formData.append("id", tagsId);
    formData.append("type", "email");
    formData.append("account_id", usedAccount?.value);
    for (let i = 0; i < usedAccount?.departmentId?.length; i++) {
      formData.append("departement_id[]", usedAccount?.departmentId[i]);
    }
    return new Promise((resolve, reject) => {
      MainService.deleteTags(formData)
        .then(() => {
          if (type === "inbox") {
            setDataSource((prevState) =>
              prevState?.map((item) =>
                item.id === id ? { ...item, tags: null } : item
              )
            );
          } else {
            getDetailsMessageInbox();
            getDetailsThreadsCollapse(5 * pageDetailsThread, false, 1);
            // setDetailsMail((p) => {
            //   let detail = Object.assign({}, p);
            //   detail.data = detail.data.map((item) => {
            //     return item.id === idThread ? { ...item, tags: {} } : item;
            //   });
            //   return detail;
            // });
          }

          toastNotification("success", t("voip.tagDelete"), "topRight");

          resolve(); // You can resolve with any value you like, or no value
        })
        .catch((err) => {
          err?.response?.status !== 401 &&
            toastNotification("error", t("toasts.somethingWrong"), "topRight");

          reject(); // Same here, reject with or without value
        });
    });
  };
  //
  const disabledDate = (current) =>
    current && current < moment().subtract(1, "days").endOf("day");

  const disablePastTime = (formId) => {
    const selectedDate = form.getFieldValue(formId);
    return () => {
      const current = dayjs();
      const selected = dayjs(selectedDate);

      if (selected && selected.isSame(current, "day")) {
        const currentHour = current.hour();
        const currentMinute = current.minute();

        return {
          disabledHours: () => Array.from(new Array(currentHour), (v, i) => i),
          disabledMinutes: (selectedHour) =>
            selectedHour === currentHour
              ? Array.from(new Array(currentMinute), (v, i) => i)
              : [],
        };
      }

      // If the selected date is not today, don't disable any hours or minutes
      return {};
    };
  };
  //
  const DividerComponent = ({ label, color }) => (
    <Divider plain orientation="left" style={{ margin: ".15rem" }}>
      <p className="truncate" style={{ color: color }}>
        {truncateString(label, 20)}
      </p>
    </Divider>
  ); //
  return (
    <div>
      <div
        style={{
          width: source === "webPhone" ? "17.5rem" : "16rem",
          // height: source === "webPhone" ? "16.5rem" : "13.5rem",
        }}
        className="flex flex-col space-y-4 overflow-y-auto  "
      >
        <Form
          form={form}
          onFinish={onFinish}
          scrollToFirstError={true}
          layout="vertical"
          autoComplete="off"
          style={{
            width: "95%",
          }}
        >
          <Form.Item
            key={id}
            label={`${t("voip.qualification")}`}
            name={`${id}tags`}
            initialValue={updateTags}
            rules={[
              {
                required: true,
                message: `${t("voip.tagIsRequired", {
                  x: `${t("voip.qualification")}`,
                })}`,
              },
            ]}
          >
            <Select
              placeholder={t("voip.selectQualify")}
              allowClear
              showSearch
              popupMatchSelectWidth={false}
              mode="multiple"
              maxTagCount="responsive"
              optionLabelProp="label"
              filterOption={(input, option) =>
                option?.label.toLowerCase().includes(input.toLowerCase())
              }
              onChange={(values, options) => setSelectedOptions(options)}
            >
              {data?.map((item) => (
                <Option
                  key={item?.id}
                  value={item?.id}
                  label={item?.label}
                  color={item?.color}
                  icon={item?.icon}
                >
                  <Space>
                    <ChoiceIcons icon={item?.icon} />
                    <span style={{ color: item?.color }}>{item?.label}</span>
                  </Space>
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            key={`${id}note`}
            label="Note"
            name={`${id}note`}
            initialValue={tags?.note || ""}
          >
            <Input.TextArea
              showCount
              allowClear
              rows={2}
              maxLength={400}
              placeholder={t("voip.writeNote")}
              onChange={(e) => setNote(e?.target?.value)}
            />
          </Form.Item>
          {!updateTags?.length && access?.activities === "1" ? (
            <div className="mt-8 flex flex-col space-y-3">
              {datePickersToShow?.map((option) => (
                <div
                  key={`${id}-${option?.value}`}
                  className="flex flex-col space-y-0"
                >
                  <DividerComponent
                    label={option?.label}
                    color={option?.color}
                  />
                  <div className="flex items-end space-x-1">
                    <Form.Item
                      key={`${id}-${option?.value}-date`}
                      label="Date"
                      name={`${option?.value}date`}
                    >
                      <DatePicker
                        placeholder="Select Date"
                        disabledDate={disabledDate}
                        onChange={(date) => {
                          const formattedDate =
                            date?.format("YYYY-MM-DD") || null;
                          updateDateTimeTrack(
                            option?.value,
                            formattedDate,
                            undefined
                          );
                        }}
                      />
                    </Form.Item>
                    <Form.Item
                      key={`${id}-${option?.value}-time`}
                      label="time"
                      name={`${option?.value}time`}
                    >
                      <TimePicker
                        // defaultValue={dayjs()}
                        placeholder="Select Time"
                        format={"HH:mm"}
                        minuteStep={5}
                        onChange={(timeValue) => {
                          const formattedTime =
                            timeValue?.format("HH:mm") || null;
                          updateDateTimeTrack(
                            option?.value,
                            undefined,
                            formattedTime
                          );
                        }}
                        disabledTime={disablePastTime(`${option?.value}date`)}
                      />
                    </Form.Item>
                  </div>
                </div>
              ))}
            </div>
          ) : null}
        </Form>
      </div>
      <div className="flex flex-row justify-between px-2 py-4">
        {updateTags?.length ? (
          <Popconfirm
            title="Delete"
            description={`${t("voip.areYouSureToDelete")} ${
              tags?.tags?.length > 1 ? t("voip.thoseTags") : t("voip.thisTag")
            }?`}
            onConfirm={() => confirmDeleteTags(tags?._id)}
            // onCancel={cancel}
            okText="Yes"
            cancelText="Cancel"
          >
            <Button size="small" type="primary" danger>
              {t("contacts.delete")}
            </Button>
          </Popconfirm>
        ) : (
          <div />
        )}
        {datePickersToShow?.length && dateTimeTrack?.length ? (
          <Button
            size="small"
            type="primary"
            loading={loadingSubmit}
            onClick={() => form.submit()}
          >
            {t("voip.save_createTask", {
              nbr: dateTimeTrack?.length > 0 ? dateTimeTrack?.length : "",
              s: dateTimeTrack?.length > 1 ? "s" : "",
            })}
          </Button>
        ) : (
          <Button
            size="small"
            type="primary"
            loading={loadingSubmit}
            onClick={() => form.submit()}
            disabled={disabledUpdate}
          >
            {t("voip.save")}
          </Button>
        )}
      </div>
    </div>
  );
};

export default TagContent;
