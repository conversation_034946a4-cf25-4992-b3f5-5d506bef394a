import { useEffect, useMemo, useState } from "react";
import { Form, Image, Upload } from "antd";
import { useTranslation } from "react-i18next";
//
const getBase64 = (file) =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
//

const AlbumField = ({
  fieldId,
  label,
  TooltipDescription,
  description,
  required,
  value,
  readOnly,
}) => {
  //
  const [t] = useTranslation("common");
  //
  const [albumList, setAlbumList] = useState(value || []);

  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewAlbum, setPreviewAlbum] = useState([]);
  const [currentImg, setCurrentImg] = useState(0);

  const handleChange = ({ fileList }) => setAlbumList([...fileList]);
  //

  const handlePreview = async (file) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj);
    }
    return file.url || file.preview;
  };
  //
  useEffect(() => {
    if (!albumList?.length) return;

    const loadPreviews = async () => {
      const previews = await Promise.all(
        albumList.map((img) => handlePreview(img))
      );
      setPreviewAlbum(previews);
    };

    loadPreviews();
  }, [albumList]);
  //
  const showImg = useMemo(
    () => (
      <div className="hidden">
        {!previewAlbum.length ? null : (
          <Image.PreviewGroup
            preview={{
              onChange: (current, prev) => setCurrentImg(current),
              onVisibleChange: (visible) => setPreviewOpen(visible),
              visible: previewOpen,
              current: currentImg,
            }}
          >
            {previewAlbum.map((img, i) => (
              <Image key={i} src={img} />
            ))}
          </Image.PreviewGroup>
        )}
      </div>
    ),
    [currentImg, previewAlbum, previewOpen]
  );
  //
  const handlePreviewAlbum = (file) => {
    const idToFind = file?.uid;
    const index = albumList.findIndex((obj) => obj.uid === idToFind);
    // console.log({ idToFind, index });
    setCurrentImg(index || 0);
    setPreviewOpen(true);
  };
  // console.log({ albumList, previewAlbum, previewOpen });
  //
  return (
    <>
      <Form.Item
        key={fieldId}
        label={label}
        tooltip={TooltipDescription(description)}
        name={fieldId}
        // initialValue={form.getFieldValue(fieldId) || []}
        rules={[
          {
            required: required,
            message: t("contacts.fieldXRequired", { x: label }),
          },
        ]}
      >
        <Upload
          accept="image/*"
          listType="picture-card"
          disabled={readOnly}
          multiple={true}
          beforeUpload={() => false}
          onPreview={handlePreviewAlbum}
          onChange={handleChange}
          fileList={albumList}
        >
          {t("contacts.upload")}
        </Upload>
      </Form.Item>

      {showImg}
    </>
  );
};

export default AlbumField;
