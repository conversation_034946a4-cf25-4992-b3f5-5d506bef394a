import { useLayoutEffect, useState } from "react";
import debounce from "lodash/debounce";

export function useWindowSize() {
  const [size, setSize] = useState([0, 0]);

  useLayoutEffect(() => {
    function updateSize() {
      setSize([window.innerWidth, window.innerHeight]);
    }

    const debouncedUpdateSize = debounce(updateSize, 200);

    window.addEventListener("resize", debouncedUpdateSize);
    updateSize();
    return () => window.removeEventListener("resize", debouncedUpdateSize);
  }, []);

  return size;
}
