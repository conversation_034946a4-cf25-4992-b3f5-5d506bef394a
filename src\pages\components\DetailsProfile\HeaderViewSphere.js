import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Divider,
  Dropdown,
  Layout,
  message,
  Popconfirm,
  Rate,
  Row,
  Select,
  Skeleton,
  Space,
  Tag,
  Tooltip,
  Typography,
} from "antd";
import DisplayAvatar from "pages/voip/components/DisplayAvatar";
import { useTranslation } from "react-i18next";
import { URL_ENV } from "index";
import { useDispatch } from "react-redux";
import MainService from "services/main.service";
import { useEffect, useRef, useState } from "react";
import { setUpdateElementSuccessfully } from "new-redux/actions/form.actions/form";
import { useSelector } from "react-redux";
import ActionsList from "pages/clients&users/components/contacts-details-component/layout/components/ActionsList";
import { useNavigate, useParams } from "react-router-dom";
import {
  ArrowLeftOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  CopyOutlined,
  DeleteOutlined,
  DownOutlined,
  FrownOutlined,
  InfoCircleOutlined,
  LoadingOutlined,
  MoreOutlined,
  QuestionCircleOutlined,
  SisternodeOutlined,
  SmileOutlined,
  TeamOutlined,
} from "@ant-design/icons";
import { toastNotification } from "components/ToastNotification";
import { FiCopy, FiEdit, FiFolderPlus, FiUserPlus } from "react-icons/fi";
import { AvatarChat } from "components/Chat";
import { getName } from "pages/layouts/chat/utils/ConversationUtils";
import ChoiceIcons from "../ChoiceIcons";
import useActionCall from "pages/voip/helpers/ActionCall";
import { CgDetailsLess, CgDetailsMore } from "react-icons/cg";
import { motion } from "framer-motion";
import { SET_CONTACT_INFO_FROM_DRAWER } from "new-redux/constants";
import {
  removeLastIdFromViewSphere,
  setNewInteraction,
} from "new-redux/actions/vue360.actions/vue360";
import HeaderDirectoryViewSphere from "./HeaderDirectoryViewSphere";
import ActionsComponent from "pages/tasks/ActionsComponent";
import { convertContactToGuest } from "pages/clients&users/helpers";
import { familyIcons, SuccessWon } from "./ViewSphere2";
import { deleteElements } from "pages/clients&users/services/services";
import { getFamilyNameById } from "pages/clients&users/FamilyRouting";
import { MdReplay } from "react-icons/md";
import { isGuestConnected, roles } from "utils/role";
import { Refs_IDs } from "components/tour/tourConfig";
import { HiOutlinePhone } from "react-icons/hi2";
import CustomSteps from "./CustomSteps";

const { Header } = Layout;
const copyIcon = (text) => (
  <Typography.Paragraph
    copyable={{
      text: text,
      icon: [
        <FiCopy className="relative  cursor-pointer text-[12px] text-blue-600 hover:text-blue-500" />,
      ],
    }}
  />
);
const Reopen = ({
  handleChange,
  dataSteps,
  openView360InDrawer,
  dispatch,
  contactInfo,
  loadStage,
  selectedTags,
}) => {
  const [t] = useTranslation("common");

  function getObjectBeforeId(data, id) {
    let index = data.findIndex((item) => item.id === id);

    while (index > 0) {
      const previousItem = data[index - 1];
      if (previousItem.final !== true) {
        return previousItem;
      }
      index--;
    }

    return null;
  }

  const handleClick = async () => {
    await handleChange(
      getObjectBeforeId(dataSteps, selectedTags?.id),
      "reopen"
    );

    if (openView360InDrawer) {
      dispatch({
        type: SET_CONTACT_INFO_FROM_DRAWER,
        payload: {
          ...contactInfo,
          closing_reason: null,
        },
      });
    } else {
      dispatch(
        {
          type: "SET_CONTACT_HEADER_INFO",
          payload: {
            ...contactInfo,
            closing_reason: null,
          },
        },
        "reopen"
      );
    }
  };

  return (
    <Popconfirm
      title={t("vue360.reopen") + " " + contactInfo?.name}
      placement="bottomLeft"
      // overlayStyle={{ paddingRight: "8px" }}
      description={t("vue360.reopen?", { name: contactInfo?.name })}
      // open={open}
      onConfirm={handleClick}
      okButtonProps={{
        loading: loadStage,
      }}
      // onCancel={handleCancel}
    >
      <Tooltip
        title={
          contactInfo?.family_id === 6
            ? t("vue360.reopen") + " " + t("vue360.theTicket")
            : t("vue360.reopen") + " " + contactInfo?.name
        }
        placement="topLeft"
      >
        <Button
          id="reopen-stage-view-sphere"
          size="small"
          style={{ height: "30px", width: "34px" }}
          disabled={loadStage}
          icon={<MdReplay />}
        >
          {/* {t("vue360.reopen")} */}
        </Button>
      </Tooltip>
    </Popconfirm>
  );
};
const HeaderViewSphere = ({
  from,
  loading,
  dataSteps,
  contactInfo,
  reference,
  selectedTags,
  setOpenEmailModal,
  isUpdate,
  setSelectedKey,
  setOpenModalCheckList,
  setOpenDrawerUpdate,
  setOpenFields,
  setOpenPipeline,
  setHeaderHeight,
  channel,
  contactType,
  detailsInfo,
  setReasons,
  setSelectedTags,
  setWindowHeight,
  headerHeight,
  windowHeight,
  source,
  setSelectedStage,
  selectedStage,
  openChat,
  setSelectedReasonType,
  setActionDeal,
  actionDeal,
  loadFinalStage,
  openView360InDrawer,
  setIsFinalStage,
  setRefreshInfo,
  selectedKey,
}) => {
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const headerInfoRef = useRef(null);
  const ActionsInfoRef = useRef(null);

  const headerRef = useRef(null);
  const call = useActionCall();

  const [loadStage, setLoadStage] = useState(false);
  const { dropAction } = useSelector((state) => state?.form);
  const [loadConvertToCtc, setLoadConvertToCtc] = useState(false);
  const [loadCancelTicket, setLoadCancelTicket] = useState(false);
  const [widthHeaderRefInfo, setWidthHeaderRefInfo] = useState(0);
  const [widthActionsRefInfo, setWidthActionsRefInfo] = useState(0);
  const { user } = useSelector((state) => state?.user);
  const [expanded, setExpanded] = useState(false);
  const { prevIds, openChatInViewSphere, activeTab360 } = useSelector(
    (state) => state?.vue360
  );
  const [open, setOpen] = useState(false);
  const [invited, setInvited] = useState(false);

  useEffect(() => {
    if (invited) {
      if (openView360InDrawer) {
        dispatch({
          type: SET_CONTACT_INFO_FROM_DRAWER,
          payload: {
            ...contactInfo,
            guest: true,
          },
        });
      } else
        dispatch({
          type: "SET_CONTACT_HEADER_INFO",
          payload: {
            ...contactInfo,
            guest: true,
          },
        });
      setInvited(false);
    }
  }, [invited, contactInfo, openView360InDrawer]);

  const handleCancel = () => {
    setOpen(false);
  };
  const params = useParams();

  useEffect(() => {
    if (!loading && headerRef.current) {
      const updateHeight = () => {
        setHeaderHeight(headerRef.current.clientHeight);
      };

      updateHeight();
      //   headerRef.current.addEventListener("transitionend", updateHeight);

      //   return () => {
      //     headerRef.current.removeEventListener("transitionend", updateHeight);
      //   };
    }
    if (!loading && ActionsInfoRef.current) {
      const updateWidth = () => {
        setWidthActionsRefInfo(ActionsInfoRef.current.clientWidth);
      };

      updateWidth();
      //   headerRef.current.addEventListener("transitionend", updateHeight);

      //   return () => {
      //     headerRef.current.removeEventListener("transitionend", updateHeight);
      //   };
    }
    if (!loading && headerInfoRef.current) {
      const updateWidth = () => {
        setWidthHeaderRefInfo(headerInfoRef.current.clientWidth);
      };

      updateWidth();
      //   headerRef.current.addEventListener("transitionend", updateHeight);

      //   return () => {
      //     headerRef.current.removeEventListener("transitionend", updateHeight);
      //   };
    }
  }, [loading, loadStage, loadFinalStage]);
  // Update element's stage.

  useEffect(() => {
    if (dropAction && dropAction?.closingReasons) {
      setReasons(dropAction?.closingReasons);
    }
  }, [dropAction]);

  const updateElementStage = async (id, payload, newStageId) => {
    try {
      const response = await MainService.updateElementStage(
        id,
        payload,
        newStageId
      );
      return response;
    } catch (error) {
      console.log(`Error ${error}`);
      message.error(error, 3);
    }
  };
  const cancelticket = async () => {
    setLoadCancelTicket(true);
    try {
      const formData = new FormData();
      formData.append("ids[]", contactInfo?.id);
      formData.append("family_id", contactInfo?.family_id);
      const { status, data } = await deleteElements(formData);
      if (status === 200) {
        toastNotification(
          "success",
          <>
            {`${t("contacts.the")} ${getFamilyNameById(
              t,
              contactInfo?.family_id
            )} `}
            <Typography.Text strong>{contactInfo?.name}</Typography.Text>{" "}
            {t("contacts.successDelete")}
          </>,
          "topRight",
          5
        );
        navigate("/tickets");
        setLoadCancelTicket(false);
      }
    } catch (err) {
      setLoadCancelTicket(false);
    }
  };
  const handleChange = async (tag, status = "open") => {
    setLoadStage(true);
    try {
      // Update new stage
      const { data } = await updateElementStage(
        contactInfo.family_id,
        {
          new_stage_id: tag?.id,
          id_element: contactInfo?.id,
        },
        tag?.id,
        dispatch
      );
      setSelectedStage(tag?.id);

      if (data?.success && data?.required_fields === 1) {
        setIsFinalStage(true);
        // It will open the update element drawer in case of a required fields
        dispatch(
          setUpdateElementSuccessfully({
            ...dropAction,
            destination: tag?.id,
          })
        );
        setOpenDrawerUpdate(true);
      } else if (data?.is_final === 1 && data?.required_fields === 0) {
        // Reasons modal will popup
        dispatch(
          setUpdateElementSuccessfully({
            ...dropAction,
            destination: tag?.id,
            elementId: contactInfo?.id,
          })
        );
        if (status === "reopen") {
          if (openView360InDrawer) {
            dispatch({
              type: SET_CONTACT_INFO_FROM_DRAWER,
              payload: {
                ...contactInfo,
                closing_reason: null,
              },
            });
          } else
            dispatch({
              type: "SET_CONTACT_HEADER_INFO",
              payload: {
                ...contactInfo,
                closing_reason: null,
              },
            });
        }

        setReasons(data?.data);
        // message.success(t("toasts.stageChanged"), 3);
      } else {
        // if (status === "reopen") {
        if (openView360InDrawer) {
          dispatch({
            type: SET_CONTACT_INFO_FROM_DRAWER,
            payload: {
              ...contactInfo,
              closing_reason: null,
            },
          });
        } else
          dispatch({
            type: "SET_CONTACT_HEADER_INFO",
            payload: {
              ...contactInfo,
              closing_reason: null,
            },
          });
        contactInfo?.family_id == 6 && tag.final && tag.default && SuccessWon();
        // }

        setSelectedTags(tag);

        selectedKey === "4" && setRefreshInfo(true);
        if (activeTab360 == 3)
          dispatch(setNewInteraction({ type: "updateWithoutMercure" }));
        if ((!tag.final && tag.default) || status === "reopen") {
          message.success(
            t("vue360.reopenSuccess", {
              name: contactInfo?.name || contactInfo?.reference,
            }),
            3
          );
        } else if (
          contactInfo?.family_id == 6 &&
          tag.final &&
          tag.default &&
          tag.resolved == 0
        ) {
          message.success(
            t("vue360.closeSuccess", {
              name: contactInfo?.name || contactInfo?.reference,
            }),
            3
          );
        } else message.success(t("chat.message_system.deal_updated_stage"), 3);
      }
      setLoadStage(false);
    } catch (err) {
      console.log(err);
    }
  };

  const handleDropdownClick = (e) => {
    const key = e?.key;
    switch (key) {
      case "edit":
        contactInfo?.id && setOpenDrawerUpdate(true);
        break;
      case "fields":
        setOpenFields(true);
        break;
      case "pipeline":
        setOpenPipeline(true);
        break;
      default:
        message.info("This feature is not ready yet!");
        break;
    }
  };
  const DropdownProps = {
    items: [
      {
        label: t("contacts.edit"),
        key: "edit",
        icon: <FiEdit className="h-4 w-4 text-slate-500" />,
        disabled: !contactInfo?.shared,
      },
      roles?.includes(user?.role) && {
        label: t("menu2.fields"),
        key: "fields",
        icon: <FiFolderPlus className="h-4 w-4 text-slate-500" />,
      },
      roles.includes(user?.role) &&
        contactInfo.pipeline && {
          label: t("menu2.pipeline"),
          key: "pipeline",
          icon: (
            <SisternodeOutlined
              style={{ fontSize: "14px" }}
              className="text-slate-500"
            />
          ),
        },

      // {
      //   label: "Clone",
      //   icon: <BookCopy className="w-[16px] text-slate-500" />,

      //   key: "3",
      // },
      // {
      //   label: "Archived",
      //   icon: <Archive className="w-[16px] text-slate-500" />,
      //   key: "4",
      // },
      // {
      //   type: "divider",
      // },
      // {
      //   label: t("contacts.delete"),
      //   key: "1",
      //   icon: <FiTrash className="h-4 w-4 " />,
      //   danger: true,
      // },
    ],
    onClick: (e) => handleDropdownClick(e),
  };
  const convertToCtc = async () => {
    setLoadConvertToCtc(true);
    try {
      const res = await MainService.convertToContact(contactInfo.id);
      navigate(`/contacts/v2/${res.data.id}`);
      setLoadConvertToCtc(false);
      toastNotification(
        "success",
        contactInfo?.family_id === 6
          ? contactInfo?.subject_helpdesk
          : contactInfo?.name + t("vue360.convertedToContact"),
        "topRight"
      );
    } catch (err) {
      setLoadConvertToCtc(false);
      toastNotification("error", t("toasts.somethingWrong"), "topRight");
    }
  };
  // useEffect(() => {
  //   const particleContainer = document.getElementById("header-container");
  //   if (
  //     contactInfo?.closing_reason?.reason_type == 1 &&
  //     particleContainer &&
  //     actionDeal === 1
  //   ) {
  //     // Trigger the particle animation
  //     party.confetti(particleContainer, {
  //       count: party.variation.range(1000, 50000),
  //       speed: party.variation.range(0, 300),
  //     });
  //   }
  // }, [dispatch, contactInfo, actionDeal]);

  return (
    <div ref={headerRef} id="header-container">
      {from === "directory" ? (
        <Header
          style={{ height: "100%", padding: "8px" }}
          className="bg-gradient-to-t from-blue-50 to-white"
        >
          <Row>
            <HeaderDirectoryViewSphere
              dataSteps={dataSteps}
              contactInfo={contactInfo}
              openView360InDrawer={openView360InDrawer}
              reference={reference}
              selectedTags={selectedTags}
              loadStage={loadStage}
              handleChange={handleChange}
              setOpenEmailModal={setOpenEmailModal}
              channel={channel}
              isUpdate={isUpdate}
              setSelectedKey={setSelectedKey}
              setOpenModalCheckList={setOpenModalCheckList}
              DropdownProps={DropdownProps}
              openChatInViewSphere={openChatInViewSphere}
              contactType={contactType}
              detailsInfo={detailsInfo}
              headerInfoRef={headerInfoRef}
            />
          </Row>
        </Header>
      ) : (
        <Header
          style={{
            height: "100%",
            padding: "8px",

            //   background: "linear-gradient(135deg, #2b6cb00d, #c5e0ff)",
          }}
          className="bg-gradient-to-t from-blue-50 to-white"
          // className=" bg-slate-50"
        >
          <Row>
            <div className="flex h-full w-full flex-col overflow-x-auto overflow-y-hidden bg-gradient-to-t from-blue-50 to-white">
              <div className="flex w-full  items-center justify-between  gap-y-2 lg:space-x-3 ">
                {/* {contactInfo?.family_id == "1" ||
                  contactInfo?.family_id == "2" ||
                  contactInfo?.family_id == "4" ||
                  contactInfo?.family_id == "9" ? (
                    <div className="">
                      <DisplayAvatar
                        size={40}
                        urlImg={contactInfo?.image}
                        name={contactInfo?.name}
                      />
                    </div>
                  ) : null} */}

                <div
                  className={`flex w-full flex-col ${
                    dataSteps?.length > 0 ? "gap-0" : "gap-3"
                  } `}
                >
                  <div className="flex items-center gap-x-1 lg:gap-x-3 xl:gap-x-5">
                    <div className="flex  grow items-center">
                      <span className=" inline-flex grow items-center gap-1.5 ">
                        <span className="relative inline-flex items-center gap-[2px]">
                          {openView360InDrawer && prevIds.length > 1 ? (
                            <Button
                              onClick={() => {
                                dispatch({
                                  type: SET_CONTACT_INFO_FROM_DRAWER,
                                  payload: {
                                    id: prevIds.slice(-2)[0],
                                  },
                                });

                                dispatch(
                                  setNewInteraction({
                                    type: "updateElementFromDrawer",
                                  })
                                );
                                dispatch(
                                  removeLastIdFromViewSphere(contactInfo?.id)
                                );
                              }}
                              icon={<ArrowLeftOutlined />}
                              shape="circle"
                              type="text"
                            />
                          ) : !openView360InDrawer && params?.id ? (
                            <Button
                              id="btn-back-view-sphere"
                              onClick={() =>
                                typeof window.location.pathname?.split(
                                  "/"
                                )[1] === "string"
                                  ? contactInfo?.family_id == 4
                                    ? navigate("/settings/users")
                                    : contactInfo?.family_id == 5
                                    ? navigate("/settings/products")
                                    : navigate(
                                        "/" +
                                          window.location.pathname.split("/")[1]
                                      )
                                  : navigate(-1)
                              }
                              icon={<ArrowLeftOutlined />}
                              shape="circle"
                              type="text"
                            />
                          ) : null}
                          {contactInfo?.family_id == "1" ||
                          contactInfo?.family_id == "2" ||
                          contactInfo?.family_id == "4" ||
                          contactInfo?.family_id == "9" ? (
                            <ActionsComponent
                              elementValue={{
                                uuid: contactInfo?.uid,
                                extension: contactInfo?.extension,
                                id: contactInfo?.id,
                                family_id: contactInfo?.family_id,
                              }}
                              openChat={openChat}
                            >
                              <DisplayAvatar
                                background="rgb(219, 234, 254)"
                                size={65}
                                cursor={
                                  contactInfo?.uid || contactInfo?.extension
                                    ? "pointer"
                                    : ""
                                }
                                icon={
                                  contactInfo?.image
                                    ? null
                                    : familyIcons(t, 30).find(
                                        (el) =>
                                          el.key === contactInfo?.family_id
                                      )?.icon
                                }
                                urlImg={
                                  contactInfo?.image
                                    ? URL_ENV?.REACT_APP_BASE_URL +
                                      URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                                      contactInfo?.image
                                    : null
                                }
                                name={contactInfo?.name}
                              />
                            </ActionsComponent>
                          ) : (
                            <DisplayAvatar
                              background="rgb(219, 234, 254)"
                              size={65}
                              icon={
                                familyIcons(t, 30).find(
                                  (el) => el.key === contactInfo?.family_id
                                )?.icon
                              }
                              urlImg={null}
                              name={contactInfo?.name}
                            />
                          )}
                        </span>

                        {/* <div
                            // className="lg:min-w[150px] flex flex-col truncate lg:max-w-[200px] xl:min-w-[200px] xl:max-w-[350px]"
                            className={`flex w-full grow flex-col gap-y-1 truncate  `}
                            // style={{
                            //   maxWidth: `calc((${
                            //     openView360InDrawer ? "1080px" : "100vw"
                            //   } - ${
                            //     from === "directory"
                            //       ? 750
                            //       : widthHeaderRefInfo +
                            //         widthActionsRefInfo +
                            //         215
                            //   }px)`,
                            //   minWidth: 90,
                            // }}
                          > */}
                        <div className={`flex w-full flex-col gap-y-1 pl-2`}>
                          <div className="flex items-center justify-between  gap-x-4">
                            <span className={`flex  items-center gap-x-4 `}>
                              <span
                                style={{
                                  maxWidth: `calc(100vw - ${
                                    230 + widthHeaderRefInfo
                                  }px)`,
                                }}
                                className={` truncate text-xl font-semibold`}
                              >
                                {contactInfo.closing_reason?.reason_type ==
                                1 ? (
                                  <Alert
                                    className="alertDeal"
                                    message={
                                      <div className="inline-flex items-center gap-x-4">
                                        <span
                                          style={{ marginBottom: 0 }}
                                          className="text-xl text-green-600"
                                        >
                                          {contactInfo?.name}
                                        </span>
                                      </div>
                                    }
                                    type="success"
                                    style={{ padding: 4, margin: 0 }}
                                    showIcon
                                  />
                                ) : contactInfo.closing_reason?.reason_type ==
                                  0 ? (
                                  <Alert
                                    className="alertDeal"
                                    message={
                                      <div className="inline-flex items-center gap-x-4 text-xl">
                                        <span
                                          style={{ marginBottom: 0 }}
                                          className="text-red-600"
                                        >
                                          {contactInfo?.name}
                                        </span>
                                      </div>
                                    }
                                    type="error"
                                    style={{ padding: 4, margin: 0 }}
                                    showIcon
                                  />
                                ) : contactInfo?.family_id === 6 ? (
                                  contactInfo?.subject_helpdesk
                                ) : (
                                  contactInfo?.name
                                )}

                                {contactInfo?.family_id === 2 &&
                                  contactInfo?.guest &&
                                  " (" +
                                    t("visio.guest", { plural: "" }) +
                                    ") "}
                              </span>
                            </span>

                            <div
                              className="flex items-center gap-x-1  pr-[2px]"
                              ref={headerInfoRef}
                            >
                              {!isGuestConnected() &&
                                contactInfo?.family_id == 9 &&
                                !contactInfo.converted && (
                                  <Popconfirm
                                    title=""
                                    description={t("vue360.convertLeadToCtc")}
                                    open={open}
                                    onConfirm={convertToCtc}
                                    okButtonProps={{
                                      loading: loadConvertToCtc,
                                    }}
                                    onCancel={handleCancel}
                                  >
                                    <Button
                                      id="btn-convert-to-contact-header-view-sphere"
                                      size="small"
                                      style={{ height: "30px" }}
                                      onClick={() => setOpen(true)}
                                      icon={<TeamOutlined />}
                                    >
                                      {t("mailing.Contact")}
                                    </Button>
                                  </Popconfirm>
                                )}
                              {!isGuestConnected() &&
                                contactInfo?.family_id == 2 &&
                                !contactInfo?.guest && (
                                  <Button
                                    id="invite-contact-button-view-sphere"
                                    size="small"
                                    style={{ height: "30px" }}
                                    onClick={() =>
                                      convertContactToGuest(
                                        t,
                                        contactInfo?.id,
                                        contactInfo?.name,
                                        setInvited,
                                        true,
                                        user?.role
                                      )
                                    }
                                    icon={<FiUserPlus />}
                                  >
                                    {t("import.inviteUsers")}
                                  </Button>
                                )}
                              {!isGuestConnected() &&
                                contactInfo?.family_id == 6 &&
                                contactInfo?.pipeline && (
                                  <>
                                    {selectedTags?.resolved == 1 ? (
                                      <Popconfirm
                                        title={
                                          t("vue360.closedTicket") +
                                          " " +
                                          contactInfo?.reference
                                        }
                                        description={t(
                                          "vue360.closeThisTicket?"
                                        )}
                                        overlayStyle={{
                                          paddingRight: "8px",
                                        }}
                                        // open={open}
                                        onConfirm={async () => {
                                          await handleChange(
                                            dataSteps.find(
                                              (el) =>
                                                el.default &&
                                                el.final &&
                                                el.resolved == 0
                                            )
                                          );
                                        }}
                                        okButtonProps={{
                                          loading: loadStage,
                                        }}
                                        icon={
                                          <CheckCircleOutlined
                                            style={{ color: "green" }}
                                          />
                                        }
                                        // onCancel={handleCancel}
                                      >
                                        <Button
                                          id="close-ticket-view-sphere"
                                          type="primary"
                                          size="small"
                                          style={{
                                            backgroundColor: "#74bc83",
                                            borderColor: "#74bc83",
                                            height: 30,
                                          }}
                                          className="hover:opacity-80"
                                          // onClick={async () => {
                                          //   if (
                                          //     selectedTags?.final === true &&
                                          //     selectedTags?.default === true
                                          //   ) {
                                          //     await handleChange(dataSteps[0]);
                                          //   } else {
                                          //     await handleChange(
                                          //       dataSteps.find(
                                          //         (el) => el.default == 1 && el.final
                                          //       )
                                          //     );
                                          //   }
                                          // }}
                                          // loading={loadStage}

                                          icon={<CheckCircleOutlined />}
                                        >
                                          {t("vue360.closedTicket")}
                                        </Button>
                                      </Popconfirm>
                                    ) : null}
                                    {selectedTags.default &&
                                      selectedTags.final &&
                                      selectedTags.resolved == 0 && (
                                        <Popconfirm
                                          title={
                                            t("vue360.reopen") +
                                            " " +
                                            contactInfo?.reference
                                          }
                                          description={t(
                                            "vue360.reopenThisTicket?"
                                          )}
                                          overlayStyle={{
                                            paddingRight: "8px",
                                          }}
                                          // open={open}
                                          onConfirm={async () => {
                                            await handleChange(dataSteps[0]);
                                          }}
                                          okButtonProps={{
                                            loading: loadStage,
                                          }}

                                          // onCancel={handleCancel}
                                        >
                                          <Tooltip
                                            title={
                                              t("vue360.reopen") +
                                              " " +
                                              t(
                                                "vue360.theTicket"
                                              )?.toLowerCase()
                                            }
                                            placement="topLeft"
                                          >
                                            <Button
                                              style={{
                                                height: 30,
                                                width: 34,
                                              }}
                                              id="reopen-stage-ticket-view-sphere"
                                              size="small"
                                              icon={
                                                <MdReplay
                                                  style={{
                                                    fontSize: 15,
                                                    position: "relative",
                                                    top: "3px",
                                                  }}
                                                />
                                              }
                                            >
                                              {/* {t("vue360.reopen")} */}
                                              {/* {" "}
                                                {t("vue360.theTicket")} */}
                                            </Button>
                                          </Tooltip>
                                        </Popconfirm>
                                      )}
                                  </>
                                )}

                              {/* {!isGuestConnected() &&
                                  user?.id === contactInfo?.owner?.id &&
                                  contactInfo?.family_id == 6 && (
                                    <Popconfirm
                                      title={
                                        t("emailAccounts.cancel") +
                                        " " +
                                        contactInfo?.reference
                                      }
                                      overlayStyle={{
                                        paddingRight: "10px",
                                      }}
                                      description={t(
                                        "vue360.cancelThisTicket?"
                                      )}
                                      // open={open}
                                      icon={
                                        <QuestionCircleOutlined
                                          style={{ color: "#dc2626" }}
                                        />
                                      }
                                      onConfirm={cancelticket}
                                      okButtonProps={{
                                        loading: loadCancelTicket,
                                      }}
                                      // onCancel={handleCancel}
                                    >
                                      
                                      <Button
                                        type="primary"
                                        size="small"
                                        danger
                                        style={{
                                          height: "30px",
                                          marginRight: "1px",
                                        }}
                                        icon={<DeleteOutlined />}
                                      >
                                        {t("form.cancel")}
                                        
                                      </Button>
                                    </Popconfirm>
                                  )} */}

                              {!isGuestConnected() &&
                              contactInfo?.family_id == 3 ? (
                                <>
                                  {contactInfo.closing_reason?.reason_type ==
                                    "1" ||
                                  contactInfo.closing_reason?.reason_type ==
                                    "0" ? (
                                    <span className="flex items-center space-x-1">
                                      {typeof Number(
                                        contactInfo.closing_reason?.reason_type
                                      ) === "number" ? (
                                        <Tooltip
                                          placement="left"
                                          title={
                                            <span className="inline-flex items-center gap-x-1   ">
                                              <span className="italic">
                                                {t("sales.reason")}
                                              </span>
                                              :{/* <BadgeHelp size={16} /> */}
                                              <span className="font-semibold underline">
                                                {
                                                  contactInfo?.closing_reason
                                                    ?.label
                                                }
                                              </span>
                                            </span>
                                          }
                                        >
                                          <Tag
                                            color={
                                              contactInfo.closing_reason
                                                ?.reason_type == 1
                                                ? "green"
                                                : "red"
                                            }
                                            style={{
                                              height: 30,
                                              marginInlineEnd: 0,
                                            }}
                                            className="flex items-center text-sm font-semibold"
                                            icon={
                                              contactInfo.closing_reason
                                                ?.reason_type == 1 ? (
                                                <SmileOutlined />
                                              ) : (
                                                <FrownOutlined />
                                              )
                                            }
                                          >
                                            {contactInfo.closing_reason
                                              ?.reason_type == 1
                                              ? t("sales.successReason")
                                              : t("sales.failReason")}{" "}
                                            <InfoCircleOutlined />
                                          </Tag>
                                        </Tooltip>
                                      ) : null}
                                      <Reopen
                                        handleChange={handleChange}
                                        dataSteps={dataSteps}
                                        openView360InDrawer={
                                          openView360InDrawer
                                        }
                                        dispatch={dispatch}
                                        SET_CONTACT_INFO_FROM_DRAWER={
                                          SET_CONTACT_INFO_FROM_DRAWER
                                        }
                                        contactInfo={contactInfo}
                                        loadStage={loadStage}
                                        selectedTags={selectedTags}
                                      />
                                    </span>
                                  ) : null}
                                  {contactInfo.closing_reason?.reason_type ==
                                  null ? (
                                    <>
                                      <Button
                                        id="btn-success-deal-view-sphere"
                                        type="primary"
                                        size="small"
                                        style={{
                                          backgroundColor: "#74bc83",
                                          borderColor: "#74bc83",
                                          height: 30,
                                        }}
                                        className="hover:opacity-80"
                                        loading={actionDeal === 1 && loadStage}
                                        disabled={actionDeal === 0 && loadStage}
                                        onClick={async () => {
                                          setActionDeal(1);
                                          await handleChange(
                                            dataSteps.find(
                                              (el) =>
                                                el.default == 1 && el.final
                                            )
                                          );
                                          setSelectedReasonType(1);
                                        }}
                                        icon={<CheckCircleOutlined />}
                                      >
                                        {t("sales.successReason")}
                                      </Button>
                                      <Button
                                        id="btn-fail-deal-view-sphere"
                                        type="primary"
                                        size="small"
                                        style={{
                                          backgroundColor: "#E78168",
                                          borderColor: "#E78168",
                                          height: 30,
                                        }}
                                        className="hover:opacity-80"
                                        onClick={async () => {
                                          setActionDeal(0);
                                          await handleChange(
                                            dataSteps.find(
                                              (el) =>
                                                el.default == 1 && el.final
                                            )
                                          );
                                          setSelectedReasonType(0);
                                        }}
                                        disabled={actionDeal === 1 && loadStage}
                                        loading={actionDeal === 0 && loadStage}
                                        icon={<CloseCircleOutlined />}
                                      >
                                        {t("sales.failReason")}
                                      </Button>
                                    </>
                                  ) : null}
                                </>
                              ) : null}
                              {/* {contactInfo.closing_reason?.reason_type ==
                                1 ? (
                                  <>
                                    <Tag
                                      color={"#74bc83"}
                                      icon={
                                        <CheckCircleOutlined
                                          style={{ fontSize: 12 }}
                                        />
                                      }
                                      style={{
                                        height: 32,
                                      }}
                                      className="truncate"
                                    >
                                      <span className="text-xs font-semibold">
                                        {t("sales.successReason")}
                                      </span>
                                      <Tooltip
                                        title={
                                          contactInfo?.closing_reason?.label
                                            .length > 39
                                            ? contactInfo?.closing_reason?.label
                                            : ""
                                        }
                                      >
                                        <div className="2xl: -mt-1 max-w-[200px] truncate text-xs">
                                          {contactInfo?.closing_reason?.label}
                                        </div>
                                      </Tooltip>
                                    </Tag>
                                    
                                  </>
                                ) : contactInfo.closing_reason?.reason_type ==
                                  0 ? (
                                  <>
                                    <Tag
                                      color={"#E78168"}
                                      icon={
                                        <CloseCircleOutlined
                                          style={{ fontSize: 12 }}
                                        />
                                      }
                                      className="truncate"
                                    >
                                      <span className="text-xs font-semibold">
                                        {t("sales.failReason")}
                                      </span>
                                      <div className="-mt-1 max-w-[122px] truncate text-xs">
                                        {contactInfo?.closing_reason?.label}
                                      </div>
                                    </Tag>
                                    <Reopen
                                      handleChange={handleChange}
                                      dataSteps={dataSteps}
                                      openView360InDrawer={openView360InDrawer}
                                      dispatch={dispatch}
                                      SET_CONTACT_INFO_FROM_DRAWER={
                                        SET_CONTACT_INFO_FROM_DRAWER
                                      }
                                      contactInfo={contactInfo}
                                      loadStage={loadStage}
                                      selectedTags={selectedTags}
                                    />
                                  </>
                                ) : null} */}
                            </div>

                            {/* {contactInfo?.type ? (
                              <span
                                style={{
                                  color: contactType?.color,
                                }}
                              >
                                {contactInfo?.type}
                              </span>
                            ) : null} */}
                            {/* <Tag color="geekblue">
                                {" "}
                                {reference ? "# " + reference : null}
                              </Tag> */}
                          </div>
                          <div className="flex items-center justify-between">
                            <div className="flex  items-center gap-5">
                              {reference ? (
                                <div className="flex flex-col gap-1">
                                  <span className="truncate text-xs font-semibold text-gray-400 ">
                                    {t("vue360.reference")}
                                  </span>
                                  <span className="flex gap-1 truncate text-sm font-semibold">
                                    {reference ? "# " + reference : null}
                                  </span>
                                </div>
                              ) : null}

                              {contactInfo?.owner?.id &&
                              contactInfo?.owner?.name ? (
                                <div className="flex flex-col gap-1">
                                  <span className="truncate text-xs font-semibold text-gray-400 ">
                                    {t("tasks.owner")}
                                  </span>
                                  <span className="flex gap-1 truncate text-sm font-semibold">
                                    {/* {contactInfo?.owner?.avatar && ( */}
                                    <ActionsComponent
                                      elementValue={{
                                        uuid: contactInfo?.owner?.uid,
                                        extension:
                                          contactInfo?.owner?.extension,
                                        id: contactInfo?.owner?.id,
                                        family_id: 4,
                                      }}
                                      openChat={openChat}
                                    >
                                      <AvatarChat
                                        // fontSize="0.875rem"
                                        url={
                                          URL_ENV?.REACT_APP_BASE_URL +
                                          URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                                          contactInfo?.owner?.avatar
                                        }
                                        fontSize={12}
                                        type="user"
                                        size={22}
                                        height={10}
                                        width={10}
                                        name={getName(
                                          contactInfo?.owner?.name,
                                          "avatar"
                                        )}
                                        hasImage={
                                          contactInfo?.owner?.avatar &&
                                          contactInfo?.owner?.avatar !==
                                            "/storage/uploads/"
                                        }
                                      />
                                    </ActionsComponent>
                                    <span className="flex self-center">
                                      {/* )} */} {contactInfo?.owner?.name}
                                    </span>
                                  </span>
                                </div>
                              ) : null}
                              {contactInfo?.type ? (
                                <div className="flex flex-col gap-1">
                                  <span
                                    className={`  truncate text-xs font-semibold text-gray-400 `}
                                  >
                                    {contactInfo?.type ? "Type" : null}
                                  </span>
                                  <span
                                    className={`  truncate text-sm font-semibold `}
                                  >
                                    {contactInfo?.type ? (
                                      <span
                                        style={{
                                          color: contactType?.color,
                                        }}
                                      >
                                        {contactInfo?.type}
                                      </span>
                                    ) : null}
                                  </span>
                                </div>
                              ) : null}
                              {channel?.id ? (
                                <div className="flex flex-col gap-1">
                                  <span className="flex  items-center">
                                    <span
                                      className={`  truncate text-xs font-semibold text-gray-400 `}
                                    >
                                      {t("vue360.channel")}
                                    </span>
                                  </span>
                                  <span className="flex  items-center">
                                    <span
                                      className={`  truncate text-sm font-semibold `}
                                    >
                                      <span style={{ color: channel?.color }}>
                                        <ChoiceIcons icon={channel?.icon} />{" "}
                                        {channel?.label}{" "}
                                      </span>
                                    </span>
                                  </span>
                                </div>
                              ) : null}

                              {Object.keys(detailsInfo).length > 0 &&
                                Object.entries(detailsInfo).map(
                                  ([key, { type, value }]) => (
                                    <div
                                      className="flex flex-col gap-1"
                                      key={key}
                                    >
                                      <span
                                        // type="secondary"

                                        className="whitespace-nowrap text-xs font-semibold text-gray-400"
                                      >
                                        {key}{" "}
                                        {Array.isArray(value) &&
                                        value.length > 2
                                          ? "(" + value.length + ")"
                                          : ""}
                                      </span>
                                      <span className="flex gap-1 truncate text-sm font-semibold">
                                        {type === "checkbox" ||
                                        type === "select" ||
                                        type === "radio" ||
                                        type === "multiselect" ||
                                        type === "autocomplete" ? (
                                          <span className="max-w-[220px] space-x-1 truncate text-sm font-semibold">
                                            {Array.isArray(value) ? (
                                              value?.map((item, index) => (
                                                <span key={index}>
                                                  {item}
                                                  {index !== value.length - 1 &&
                                                    ","}
                                                </span>
                                              ))
                                            ) : (
                                              <span
                                                className={`text-sm font-semibold`}
                                              >
                                                {value}{" "}
                                              </span>
                                            )}
                                          </span>
                                        ) : type === "phone" ? (
                                          <Typography.Text>{`(${value?.[0]}) ${value?.[1]}`}</Typography.Text>
                                        ) : type === "image" ? (
                                          <AvatarChat
                                            url={
                                              URL_ENV?.REACT_APP_BASE_URL +
                                              URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                                              value
                                            }
                                            fontSize={12}
                                            type="user"
                                            size={22}
                                            height={10}
                                            width={10}
                                            name={getName(
                                              contactInfo?.owner?.name,
                                              "avatar"
                                            )}
                                            hasImage={
                                              value &&
                                              value !== "/storage/uploads/"
                                            }
                                          />
                                        ) : type === "file" ? (
                                          <div className="flex flex-col space-y-1">
                                            {value?.map((file, i) => (
                                              <a
                                                key={i}
                                                className="truncate"
                                                style={{
                                                  width: `14rem`,
                                                }}
                                                href={`${
                                                  URL_ENV?.REACT_APP_BASE_URL +
                                                  process.env
                                                    .REACT_APP_SUFFIX_AVATAR_URL
                                                }${file}`}
                                                target="_blank"
                                                rel="noreferrer"
                                              >
                                                {file}
                                              </a>
                                            ))}
                                          </div>
                                        ) : type === "rate" ? (
                                          <Rate
                                            allowHalf
                                            disabled
                                            defaultValue={Number(value)}
                                          />
                                        ) : (
                                          // <Tooltip title={key}>
                                          <span className="text-sm font-semibold">
                                            {Array.isArray(value)
                                              ? value.map((el) => el + " ")
                                              : typeof value === "string"
                                              ? value
                                              : null}
                                          </span>
                                          // </Tooltip>
                                        )}
                                        {type === "phone" ? (
                                          <span className="m-0 mt-[1px] inline-flex items-center space-x-1 p-0">
                                            {copyIcon(
                                              `${value?.[0]?.replace(
                                                "+",
                                                "00"
                                              )}${value?.[1]}`
                                            )}
                                            <Tooltip title={t("voip.call")}>
                                              <HiOutlinePhone
                                                rotate={100}
                                                className="relative top-[1px] cursor-pointer text-[13px] text-blue-600 hover:text-blue-500"
                                                onClick={() =>
                                                  call(
                                                    `${value?.[0]?.replace(
                                                      "+",
                                                      "00"
                                                    )}${value?.[1]}`,
                                                    contactInfo?.id,
                                                    contactInfo?.family_id
                                                  )
                                                }
                                              />
                                            </Tooltip>
                                          </span>
                                        ) : (
                                          ""
                                        )}
                                        {type === "extension" ? (
                                          <Space size={4}>
                                            {value} {copyIcon(value)}
                                            {user.id !== contactInfo?.id ? (
                                              <Tooltip title={"Call"}>
                                                <HiOutlinePhone
                                                  rotate={100}
                                                  className="relative top-[1px] cursor-pointer text-[13px] text-blue-600 hover:text-blue-500"
                                                  onClick={() =>
                                                    call(
                                                      `${value?.[0]?.replace(
                                                        "+",
                                                        "00"
                                                      )}${value?.[1]}`,
                                                      contactInfo?.id,
                                                      contactInfo?.family_id
                                                    )
                                                  }
                                                />
                                              </Tooltip>
                                            ) : null}
                                          </Space>
                                        ) : type === "email" ? (
                                          <span className="mt-[1px]">
                                            {copyIcon(`${value}`)}
                                          </span>
                                        ) : (
                                          ""
                                        )}
                                      </span>
                                    </div>
                                  )
                                )}

                              {selectedTags?.id ? (
                                <div
                                  className="flex flex-col gap-1 "
                                  ref={
                                    Refs_IDs?.selectPipelineStageInviewSphere
                                  }
                                >
                                  <span className="inline-flex items-center text-gray-400">
                                    <span className="truncate text-xs font-semibold ">
                                      Pipeline
                                      {"  "}-
                                    </span>
                                    <span className="ml-[0.5] pl-0.5 text-xs">
                                      {t("pipeline.stage")}
                                      {/* {`
                                      ${t("pipeline.stage")}
                                      
                                      ${
                                        dataSteps
                                          .map((el) => el.id)
                                          .indexOf(selectedTags?.id) + 1
                                      }/${dataSteps?.length - 1}`} */}
                                    </span>
                                  </span>
                                  <Select
                                    suffixIcon={
                                      loadStage ? (
                                        <LoadingOutlined
                                          style={{
                                            color: "#000",
                                            fontSize: "10px",
                                          }}
                                        />
                                      ) : (
                                        <DownOutlined
                                          style={{
                                            color: "#000",
                                            fontSize: "10px",
                                            pointerEvents: "none",
                                          }}
                                        />
                                      )
                                    }
                                    bordered={false}
                                    size="small"
                                    loading={loadStage}
                                    style={{
                                      padding: "0",
                                      height: "20px",
                                      // width: "100%",
                                      // minWidth: "200px",
                                      // maxWidth: "300px",
                                      // background: "#FCFDFF",
                                      background: "transparent",
                                    }}
                                    className="selectViewSphere w-full min-w-[150px] lg:max-w-[300px] xl:max-w-[320px]"
                                    popupMatchSelectWidth={false}
                                    placeholder={t("tasks.choose")}
                                    onChange={(e, value) =>
                                      isGuestConnected()
                                        ? () => {}
                                        : handleChange(value)
                                    }
                                    //   bordered={false}
                                    //   onChange={(e) => {           v
                                    //     UpdateTaskStage(
                                    //       {
                                    //         "task_id[]": props?.id,
                                    //         new_stage_id: e,
                                    //       },
                                    //       "singleUpdate"
                                    //     );
                                    //   }}
                                    value={selectedTags?.id}
                                    optionLabelProp="label2"
                                    options={dataSteps
                                      .slice(0, dataSteps.length - 1)
                                      .map((el) => ({
                                        ...el,
                                        label: (
                                          <span className="viewSphereSelect flex">
                                            <Badge
                                              color={el.color}
                                              text={
                                                el.percent ? (
                                                  <>
                                                    {el.label +
                                                      " (" +
                                                      el.percent +
                                                      "%)"}
                                                  </>
                                                ) : (
                                                  el.label
                                                )
                                              }
                                            />
                                          </span>
                                        ),
                                        label2: (
                                          <span className="viewSphereSelect flex text-sm font-semibold">
                                            {
                                              dataSteps[dataSteps.length - 1]
                                                ?.pipeline?.label
                                            }
                                            {"  "}-{"  "}
                                            <Badge
                                              className="pl-1"
                                              color={el.color}
                                              text={
                                                el.percent ? (
                                                  <>
                                                    {el.label +
                                                      " (" +
                                                      el.percent +
                                                      "%) "}
                                                  </>
                                                ) : (
                                                  el.label
                                                )
                                              }
                                            />
                                          </span>
                                        ),

                                        value: el.id,
                                        disabled: isGuestConnected(),
                                      }))}
                                    //   popupMatchSelectWidth={false}
                                  />
                                </div>
                              ) : null}
                              {selectedTags?.id ? (
                                <div className="flex flex-col gap-1 ">
                                  <span className="flex  items-center">
                                    <span
                                      className={` invisible truncate text-xs font-semibold text-gray-400 `}
                                    >
                                      {t("vue360.channel")}
                                    </span>
                                  </span>
                                  <span className="flex  items-center">
                                    <span
                                      className={`  truncate text-sm font-semibold `}
                                      id="show-pipeline/stage-view-sphere"
                                    >
                                      <span>
                                        {!expanded ? (
                                          <Button
                                            type="text"
                                            shape="circle"
                                            size="small"
                                            // style={{
                                            //   width: "30px",
                                            //   height: "26px",
                                            // }}
                                            icon={<CgDetailsMore />}
                                            onClick={() => {
                                              setExpanded(true);
                                              setTimeout(() => {
                                                setHeaderHeight(
                                                  // contactInfo.closing_reason
                                                  //   ?.reason_type == 0
                                                  //   ? headerHeight + 106
                                                  //   : contactInfo
                                                  //       .closing_reason
                                                  //       ?.reason_type == 1
                                                  //   ? headerHeight + 106
                                                  //   :
                                                  headerHeight + 103
                                                );
                                                setWindowHeight(
                                                  windowHeight - 103
                                                );
                                              }, 50);
                                            }}
                                          />
                                        ) : (
                                          <Button
                                            type="text"
                                            shape="circle"
                                            size="small"
                                            icon={<CgDetailsLess />}
                                            onClick={() => {
                                              setExpanded(false);
                                              setTimeout(() => {
                                                setHeaderHeight(
                                                  // contactInfo.closing_reason
                                                  //   ?.reason_type == 0
                                                  //   ? 122
                                                  //   : contactInfo
                                                  //       .closing_reason
                                                  //       ?.reason_type == 1
                                                  //   ? 122
                                                  //   :
                                                  96
                                                );
                                                setWindowHeight(
                                                  windowHeight + 130
                                                );
                                              }, 50);
                                            }}
                                          />
                                        )}
                                      </span>
                                    </span>
                                  </span>
                                </div>
                              ) : null}
                            </div>
                            {!isGuestConnected() && (
                              <div className="inline-flex">
                                {contactInfo?.family_id && (
                                  <span
                                    className="inline-flex items-center justify-end space-x-1"
                                    ref={
                                      !isGuestConnected()
                                        ? ActionsInfoRef
                                        : null
                                    }
                                  >
                                    <span
                                      ref={Refs_IDs.actionsHeaderViewSphere}
                                    >
                                      <ActionsList
                                        {...contactInfo}
                                        setReceiver={() => {}}
                                        setOpenEmailModal={setOpenEmailModal}
                                        elementID={contactInfo?.id}
                                        module={contactInfo?.family_id}
                                        source={""}
                                        isElementUpdate={isUpdate}
                                        isAssociationUpdate={false}
                                        setSelectedKey={setSelectedKey}
                                        setOpenModalCheckList={
                                          setOpenModalCheckList
                                        }
                                        from="viewSphere"
                                      />
                                    </span>
                                    <Dropdown
                                      menu={DropdownProps}
                                      disabled={
                                        openView360InDrawer &&
                                        openChatInViewSphere
                                      }
                                    >
                                      <Button
                                        ref={Refs_IDs.actionsHeaderViewSphere}
                                        size="small"
                                        icon={
                                          <MoreOutlined className="font-semibold" />
                                        }
                                        style={{
                                          height: "30px",
                                          width: "34px",
                                        }}
                                        // onClick={() => setOpenModalMessage(true)}
                                      />
                                    </Dropdown>
                                  </span>
                                )}
                              </div>
                            )}
                            {isGuestConnected() &&
                              contactInfo?.family_id == 6 &&
                              contactInfo?.pipeline && (
                                // selectedTags?.final !== true &&
                                // selectedTags?.default !== true &&

                                <div
                                  className="mt-3 flex items-center justify-end gap-2 pr-4"
                                  // ref={
                                  //   isGuestConnected() ? headerInfoRef : null
                                  // }
                                >
                                  {selectedTags?.resolved == 1 ? (
                                    <Popconfirm
                                      title={
                                        t("vue360.closedTicket") +
                                        " " +
                                        contactInfo?.reference
                                      }
                                      description={t("vue360.closeThisTicket?")}
                                      overlayStyle={{ paddingRight: "8px" }}
                                      // open={open}
                                      onConfirm={async () => {
                                        await handleChange(
                                          dataSteps.find(
                                            (el) =>
                                              el.default &&
                                              el.final &&
                                              el.resolved == 0
                                          )
                                        );
                                      }}
                                      okButtonProps={{ loading: loadStage }}
                                      icon={
                                        <CheckCircleOutlined
                                          style={{ color: "green" }}
                                        />
                                      }
                                      // onCancel={handleCancel}
                                    >
                                      <Button
                                        id="close-ticket-view-sphere"
                                        type="primary"
                                        size="small"
                                        style={{
                                          backgroundColor: "#74bc83",
                                          borderColor: "#74bc83",
                                          height: 30,
                                        }}
                                        className="hover:opacity-80"
                                        // onClick={async () => {
                                        //   if (
                                        //     selectedTags?.final === true &&
                                        //     selectedTags?.default === true
                                        //   ) {
                                        //     await handleChange(dataSteps[0]);
                                        //   } else {
                                        //     await handleChange(
                                        //       dataSteps.find(
                                        //         (el) => el.default == 1 && el.final
                                        //       )
                                        //     );
                                        //   }
                                        // }}
                                        // loading={loadStage}

                                        icon={<CheckCircleOutlined />}
                                      >
                                        {t("vue360.closedTicket")}
                                        {/* {" "}
                                          {t("vue360.theTicket")} */}
                                      </Button>
                                    </Popconfirm>
                                  ) : null}
                                  {selectedTags.default &&
                                    selectedTags.final &&
                                    selectedTags.resolved == 0 && (
                                      <Popconfirm
                                        title={
                                          t("vue360.reopen") +
                                          contactInfo?.reference
                                        }
                                        description={t(
                                          "vue360.reopenThisTicket?"
                                        )}
                                        overlayStyle={{ paddingRight: "8px" }}
                                        // open={open}
                                        onConfirm={async () => {
                                          await handleChange(dataSteps[0]);
                                        }}
                                        okButtonProps={{ loading: loadStage }}

                                        // onCancel={handleCancel}
                                      >
                                        <Button
                                          id="reopen-stage-ticket-view-sphere"
                                          style={{
                                            height: 30,
                                          }}
                                          size="small"
                                          icon={
                                            <MdReplay
                                              style={{
                                                fontSize: 15,
                                                position: "relative",
                                                top: "3px",
                                              }}
                                            />
                                          }
                                        >
                                          {t("vue360.reopen")}
                                          {/* {" "}
                                            {t("vue360.theTicket")} */}
                                        </Button>
                                      </Popconfirm>
                                    )}
                                  {user?.id === contactInfo?.owner?.id &&
                                    contactInfo?.family_id == 6 && (
                                      <Popconfirm
                                        title={
                                          t("emailAccounts.cancel") +
                                          " " +
                                          contactInfo?.reference
                                        }
                                        overlayStyle={{ paddingRight: "8px" }}
                                        description={t(
                                          "vue360.cancelThisTicket?"
                                        )}
                                        // open={open}
                                        icon={
                                          <QuestionCircleOutlined
                                            style={{ color: "red" }}
                                          />
                                        }
                                        onConfirm={cancelticket}
                                        okButtonProps={{
                                          loading: loadCancelTicket,
                                        }}
                                        // onCancel={handleCancel}
                                      >
                                        <Button
                                          id="cancel-ticket-view-sphere"
                                          type="primary"
                                          style={{
                                            height: "30px",
                                          }}
                                          size="small"
                                          danger
                                          icon={
                                            <DeleteOutlined
                                            // style={{ fontSize: 15 }}
                                            />
                                          }
                                        >
                                          {t("form.cancel")}
                                          {/* {" "}
                                            {t("vue360.theTicket")} */}
                                        </Button>
                                      </Popconfirm>
                                    )}
                                </div>
                              )}
                          </div>
                        </div>
                        {/* </div> */}
                      </span>
                    </div>
                  </div>
                </div>

                {/* {contactType?.id ? (
                                        <Tag
                                          color={contactType?.color}
                                          // style={{
                                          //   fontSize: "12px",
                                          //   padding: "2px 8px",
                                          // }}
                                        >
                                          {contactType?.label}
                                        </Tag>
                                      ) : null} */}
              </div>
              {from !== "directory" ? (
                dataSteps?.length > 0 ? (
                  <>
                    <motion.div
                      initial={false}
                      animate={{
                        height: expanded ? "" : 0,
                        // visibility: expanded ? "visible" : "hidden",
                      }}
                      transition={{ duration: 0.5, ease: "easeInOut" }}
                      //   className="flex justify-center"
                    >
                      <Divider style={{ margin: "12px 0" }} />
                      <div className="flex ">
                        <div
                          className={`inline-flex h-full max-w-[calc((${
                            openView360InDrawer ? "1080px" : "100vw"
                          }-77px)] items-center justify-between gap-x-2 pl-6 pr-1`}
                        >
                          <div className="flex items-center ">
                            <span className=" h-[76px] w-full">
                              {/* <div className="flex  "> */}
                              <div className="flex w-full flex-col gap-1 ">
                                <div className="flex items-center justify-start">
                                  <div
                                    className={`flex w-full max-w-[calc((${
                                      openView360InDrawer ? "1080px" : "100vw"
                                    }-77px)] items-center gap-2`}
                                  >
                                    <span className="text-sm font-bold">
                                      {
                                        dataSteps[dataSteps.length - 1]
                                          ?.pipeline?.label
                                      }{" "}
                                      -{" "}
                                      <span
                                        style={{
                                          color: selectedTags?.color,
                                        }}
                                        className={`inline-flex max-w-[calc((${
                                          openView360InDrawer
                                            ? "1080px"
                                            : "100vw"
                                        }-462px)] truncate text-sm font-semibold`}
                                      >
                                        {typeof selectedTags?.label === "string"
                                          ? typeof selectedTags?.percent ===
                                            "number"
                                            ? selectedTags?.label +
                                              " (" +
                                              selectedTags?.percent +
                                              "%)"
                                            : selectedTags?.label
                                          : typeof selectedTags?.label?.props
                                              ?.children?.props?.text ===
                                            "string"
                                          ? selectedTags?.label?.props?.children
                                              ?.props?.text
                                          : selectedTags?.label?.props?.children
                                              ?.props?.text?.props?.children +
                                            " "}
                                        {/* {typeof selectedTags?.percent ===
                                            "number"
                                              ? " (" +
                                                selectedTags?.percent +
                                                "%)"
                                              : null} */}
                                      </span>
                                    </span>
                                    <Divider type="vertical" />
                                    <Typography.Text
                                      level={5}
                                      // mark
                                      style={{ margin: 0 }}
                                    >
                                      <span className="w-[100px] text-xs font-semibold">{`${t(
                                        "fields_management.groupStageLabel"
                                      )} ${
                                        dataSteps
                                          .map((el) => el.id)
                                          .indexOf(selectedTags?.id) + 1
                                      }/${dataSteps?.length - 1}`}</span>
                                    </Typography.Text>

                                    {!isGuestConnected() &&
                                    roles.includes(user.role) ? (
                                      <>
                                        <Divider type="vertical" />

                                        <Button
                                          icon={<SisternodeOutlined />}
                                          type="link"
                                          size="small"
                                          onClick={() => setOpenPipeline(true)}
                                        >
                                          {t("tasks.openAttachment") +
                                            " " +
                                            t("menu2.pipeline")}
                                        </Button>
                                      </>
                                    ) : null}
                                  </div>
                                </div>
                                <div
                                  className=" flex w-full  items-center gap-1  overflow-x-hidden overflow-y-hidden  pt-1 text-center hover:overflow-x-auto"
                                  style={{
                                    width:
                                      source === "task"
                                        ? `calc((${
                                            openView360InDrawer
                                              ? "1080px"
                                              : "100vw"
                                          } - 172px)`
                                        : "auto",
                                  }}
                                >
                                  <div
                                    className="flex gap-5  pb-2"
                                    style={{
                                      maxWidth:
                                        from === "directory"
                                          ? `calc((${
                                              openView360InDrawer
                                                ? "1080px"
                                                : "100vw"
                                            } - 525px)`
                                          : `calc((${
                                              openView360InDrawer
                                                ? "1150px"
                                                : "100vw"
                                            } - 115px)`,
                                      width: "-webkit-fill-available",
                                    }}
                                  >
                                    <CustomSteps
                                      dataSteps={dataSteps
                                        .slice(0, -1)
                                        .map((el, i) => ({ ...el, index: i }))}
                                      handleChange={handleChange}
                                      selectedTags={selectedTags}
                                      loadStage={loadStage}
                                    />
                                    {/* <>
                                    {dataSteps.slice(0, -1).map((tag, i) => {
                                      const isSelected =
                                        tag.id === selectedTags?.id;
                                      const isDisabled =
                                        isGuestConnected() || isSelected;

                                      return (
                                        <div
                                          key={tag.id}
                                          className={`relative flex grow flex-col gap-y-1`}
                                        >
                                          <span
                                            style={{
                                              color:
                                                i >
                                                dataSteps
                                                  .map((el) => el?.id)
                                                  .lastIndexOf(selectedTags?.id)
                                                  ? "#94a3b8"
                                                  : "#6b7280",
                                            }}
                                            className="flex text-xs font-semibold"
                                          >
                                            <Badge
                                              style={{ width: "max-content" }}
                                              color={tag.color}
                                              text={
                                                <span
                                                  style={{
                                                    width: "max-content",
                                                  }}
                                                >
                                                  {tag.label}
                                                </span>
                                              }
                                            />
                                          </span>
                                          <div className="flex grow">
                                            <Tag.CheckableTag
                                              key={tag.id}
                                              checked={isSelected}
                                              onChange={
                                                isDisabled
                                                  ? () => {}
                                                  : () =>
                                                      handleChange(
                                                        tag,
                                                        !isSelected
                                                      )
                                              }
                                              size="large"
                                              disabled={true}
                                              className={`grow ${
                                                isDisabled
                                                  ? "cursor-not-allowed"
                                                  : ""
                                              }`}
                                              style={{
                                                background:
                                                  i >
                                                  dataSteps
                                                    .map((el) => el.id)
                                                    .lastIndexOf(
                                                      selectedTags?.id
                                                    )
                                                    ? "#e2e8f0"
                                                    : "#60a5fa",
                                                margin: 0,
                                                height: "4px",
                                              }}
                                            >
                                              <span className="text-white">
                                                <div className="w-20 py-[1px] text-center">
                                               
                                                </div>
                                              </span>
                                            </Tag.CheckableTag>
                                          </div>
                                        </div>
                                      );
                                    })}
                                    </> */}
                                  </div>
                                </div>
                              </div>
                              {/* </div> */}
                            </span>
                          </div>

                          {/* <div className="w-[155px]">
                          <Card style={{ width: "100%" }}>
                            <span
                              className="text-md inline-flex w-full flex-col px-2 font-bold "
                              style={{
                                color: dataSteps?.find(
                                  (el) => el.label === selectedTags
                                )?.color,
                              }}
                            >
                              <span className="max-w-[86px] grow truncate">
                                {selectedTags + " "}
                              </span>
                              <span>
                                {typeof dataSteps?.find(
                                  (el) => el.label === selectedTags
                                )?.percent === "number" ? (
                                  <span>
                                    {" "}
                                    <PercentageOutlined />
                                    {
                                      dataSteps?.find(
                                        (el) => el.label === selectedTags
                                      )?.percent
                                    }
                                  </span>
                                ) : null}
                              </span>
                            </span>
                          </Card>
                        </div> */}
                        </div>
                      </div>
                    </motion.div>
                  </>
                ) : (
                  ""
                )
              ) : (
                ""
              )}
            </div>
          </Row>
          {/* <Space>
            <div className="demo-logo" />
            <Avatar
              style={{
                backgroundColor: "#87d068",
              }}
              icon={<UserOutlined />}
            />
          </Space> */}
        </Header>
      )}
    </div>
  );
};

export default HeaderViewSphere;
