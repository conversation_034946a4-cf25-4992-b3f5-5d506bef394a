import React from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";

const DualAxes = ({ pivotData }) => {
  if (
    !pivotData ||
    !pivotData.rowKeys ||
    !pivotData.colKeys ||
    !pivotData.getAggregator
  ) {
    return <p className="text-center text-red-600">No Data</p>;
  }

  const rowKeys = pivotData.rowKeys;
  const colKeys = pivotData.colKeys;

  if (rowKeys.length === 0 || colKeys.length === 0) {
    return <p className="text-center text-red-600">No Data</p>;
  }

  const categories = rowKeys.map((row) => row.join(" - "));
  const series = colKeys.map((col, index) => ({
    name: col.join(" - "),
    type: index % 2 === 0 ? "column" : "spline",
    yAxis: index % 2 === 0 ? 1 : 0,
    data: rowKeys.map((row) => pivotData.getAggregator(row, col)?.value() || 0),
  }));

  const options = {
    chart: {
      zooming: {
        type: "xy",
      },
    },
    title: {
      text: `📊
            ${pivotData.props.rows.length ? pivotData.props.rows.join(" ") : ""}
            ${
              pivotData.props.cols.length
                ? " : " + pivotData.props.cols.join(" ")
                : ""
            }`,
      align: "left",
      dispalay: "block",
      fontFamily: "Arial, sans-serif",
      style: {
        fontWeight: "normal",
        fontSize: "20px",
        fontFamily: "Inter, sans-serif",
      },
    },
    xAxis: {
      categories,
      crosshair: true,
    },
    yAxis: [
      {
        title: {
          text: null,
          style: { color: Highcharts.getOptions().colors?.[1] || "#000" },
        },
        text: null,
        labels: {
          style: { color: Highcharts.getOptions().colors?.[1] || "#000" },
        },
      },
      {
        title: {
          style: { color: Highcharts.getOptions().colors?.[0] || "#000" },
          text: null,
        },
        labels: {
          style: { color: Highcharts.getOptions().colors?.[0] || "#000" },
        },
        opposite: true,
      },
    ],
    tooltip: {
      shared: true,
    },
    legend: {
      align: "left",
      verticalAlign: "top",
      backgroundColor:
        Highcharts.defaultOptions.legend?.backgroundColor ||
        "rgba(255,255,255,0.25)",
    },
    credits: {
      enabled: false,
    },
    series,
  };

  return (
    <div
      style={{ height: "100%", width: "100%" }}
      className="highcharts-figure"
    >
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
};

export default DualAxes;
