import {
  ACTIVITIES_MESSAGES,
  FILTER_READ_ACTIVITIES_MESSAGES,
  OPEN_TASK_ROOM_DRAWER,
} from "../../constants";

export const setOpenTaskRoomDrawer = (payload) => ({
  type: OPEN_TASK_ROOM_DRAWER,
  payload,
});
export const setActivitiesMessages = (payload) => ({
  type: ACTIVITIES_MESSAGES,
  payload,
});
export const filterReadMessage = (payload) => ({
  type: FILTER_READ_ACTIVITIES_MESSAGES,
  payload,
});
