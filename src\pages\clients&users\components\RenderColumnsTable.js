import {
  Tag,
  Tooltip,
  Button,
  Image,
  Popover,
  Avatar,
  Rate,
  Space,
} from "antd";
//
import { <PERSON><PERSON><PERSON>, FiPhoneForwarded } from "react-icons/fi";
import { useTranslation } from "react-i18next";
import { URL_ENV } from "index";
import {
  formatDateComparisonTableFamily,
  humanDate,
  truncateFileName,
  truncateString,
} from "pages/voip/helpers/helpersFunc";
import useActionCall from "pages/voip/helpers/ActionCall";
import ChoiceIcons from "pages/components/ChoiceIcons";
import { colors } from "components/Colors";
import DisplayAvatar from "pages/voip/components/DisplayAvatar";
import { AiOutlineShoppingCart } from "react-icons/ai";
import { DollarOutlined } from "@ant-design/icons";
import { LuPalmtree } from "react-icons/lu";
import { useState } from "react";
import { Blocks, HeartHandshake } from "lucide-react";
import TicketIconSphere from "components/icons/TicketIconSphere";
//
const textStyle = "text-slate-800";
const maxChars = {
  multipleOptions: 21,
  singleOption: 28,
};
const { multipleOptions, singleOption } = maxChars;
//
//
const RenderColumnsTable = ({ values, type, relatedType, relatedOptions }) => {
  //
  // !!relatedType && console.log(type, values, relatedType, { relatedOptions });
  const { t } = useTranslation("common");
  const actionCall = useActionCall();
  //
  if (values === null || values === undefined) return null;
  switch (type) {
    case "radio":
    case "select":
      if (!values) return null;
      if (!!relatedType)
        return relatedType === "family_module" ? (
          <CustomTag
            content={relatedOptions?.label}
            maxChars={singleOption}
            avatar={
              <DisplayAvatar
                name={values}
                size={20}
                icon={renderIcon(relatedOptions?.family_id, 14)}
                urlImg={
                  !!relatedOptions?.avatar &&
                  URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                    relatedOptions?.avatar
                }
              />
            }
          />
        ) : relatedOptions?.isPipeline && !!relatedOptions?.stageLabel ? (
          <div className="flex items-center">
            <CustomTag content={relatedOptions?.pipelineLabel} maxChars={12} />
            <CustomTag
              content={relatedOptions?.stageLabel}
              color={relatedOptions?.color}
              maxChars={14}
            />
          </div>
        ) : (
          <CustomTag
            content={relatedOptions?.label}
            icon={relatedOptions?.icon}
            color={relatedOptions?.color}
            maxChars={!!relatedOptions?.icon ? singleOption : 30}
          />
        );
      return <CustomTag content={values} />;
    case "rate":
      return (
        !!values && <Rate disabled allowHalf defaultValue={Number(values)} />
      );
    case "color":
      return !!values && <CustomTag content={values} color={values} t={t} />;
    case "checkbox":
    case "multiselect":
      if (!Array.isArray(values) || values.length === 0) return null;

      const validValues = values.filter(
        (item) =>
          (typeof item === "string" && item.trim() !== "") ||
          (typeof item === "object" && Object.keys(item).length > 0)
      );
      if (validValues.length === 0) return null;

      const renderTag = (item, index) => {
        const isRelated = !!relatedType;
        const label = isRelated ? item?.label : item;
        const key = item?.id || index;

        const avatar =
          isRelated && relatedType === "family_module" ? (
            <DisplayAvatar
              name={label}
              size={20}
              icon={renderIcon(item?.family_id, 14)}
              urlImg={
                item?.avatar &&
                `${URL_ENV?.REACT_APP_BASE_URL}${URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL}${item?.avatar}`
              }
            />
          ) : null;

        return (
          <CustomTag
            key={key}
            avatar={avatar}
            content={label}
            icon={item?.icon}
            color={item?.color}
            maxChars={multipleOptions}
          />
        );
      };

      const items = relatedType ? relatedOptions : values;
      const renderTags = items.map(renderTag);

      if (values.length === 1) {
        return renderTags[0];
      }

      const [firstTag, ...restTags] = renderTags;

      return (
        <div className="flex items-center ">
          {firstTag}
          <DisplayPopover
            content={<Space direction="vertical">{restTags}</Space>}
            nbr={restTags.length}
          />
        </div>
      );
    case "link":
      return (
        !!values && (
          <div className="group flex flex-row items-center justify-between">
            <Tooltip title={t("contacts.openInNewTab")} placement="top">
              <a
                className={`${textStyle} truncate underline decoration-dashed underline-offset-4`}
                href={`${values}`}
                target="_blank"
                rel="noreferrer"
              >
                {values}
              </a>
            </Tooltip>
            <div className="hidden group-hover:block">
              <Tooltip title={t("chat.action.copy")} placement="top">
                <Button
                  onClick={() => {
                    navigator.clipboard.writeText(values);
                    // message.open({
                    //   type: "success",
                    //   content: "E-mail copied",
                    // });
                  }}
                  type="link"
                  size="small"
                  icon={<FiCopy style={{ fontSize: 16 }} />}
                />
              </Tooltip>
            </div>
          </div>
        )
      );
    case "monetary":
      return (
        !!(values?.length && values?.[1]) && (
          <span className={textStyle}>
            {`(${values[0]}) ${Number(values[1])
              .toFixed(3)
              .replace(/\d(?=(\d{3})+\.)/g, "$&,")}`}
          </span>
        )
      );
    case "email":
      return (
        !!values && (
          <div className="group flex flex-row  items-center justify-between">
            <span
              className={`${textStyle} truncate underline decoration-dashed underline-offset-4`}
            >
              {values}
            </span>
            <div className="hidden group-hover:block">
              {/* <Space size={1}> */}
              <Tooltip title={t("chat.action.copy")} placement="top">
                <Button
                  onClick={() => {
                    navigator.clipboard.writeText(values);
                    // message.open({
                    //   type: "success",
                    //   content: "E-mail copied",
                    // });
                  }}
                  type="link"
                  size="small"
                  icon={<FiCopy style={{ fontSize: 16 }} />}
                />
              </Tooltip>
              {/* <Tooltip title="Send E-Mail" placement="top">
                    <Button
                      onClick={() => {
                        message.error("This Action Is Under Dev 🤌", [5]);
                        // setReceiverMail(values);
                        // setOpenModalMessage(true);
                        // message.open({
                        //   type: "warning",
                        //   content: "This Action Is Under Dev!",
                        // });
                      }}
                      type="link"
                      icon={<FiSend className="h-4 w-4" />}
                    ></Button>
                  </Tooltip> */}
              {/* </Space> */}
            </div>
          </div>
        )
      );
    case "phone":
      return (
        !!(values?.length && values?.[1]) && (
          <div className="group flex flex-row items-center justify-between ">
            <span
              className={`${textStyle} underline decoration-dashed underline-offset-4`}
            >
              {`(${values[0]}) ${values[1]}`}
            </span>
            <div className=" hidden group-hover:block">
              <Space size={3}>
                <Tooltip title={t("chat.action.copy")} placement="top">
                  <Button
                    onClick={() => {
                      navigator.clipboard.writeText(
                        values
                          ?.join("")
                          ?.replace(/\(|\)| /g, "")
                          ?.replace("+", "00")
                      );
                      // message.open({
                      //   type: "success",
                      //   content: "Phone number copied",
                      // });
                    }}
                    type="link"
                    size="small"
                    icon={<FiCopy style={{ fontSize: 16 }} />}
                  />
                </Tooltip>
                <Tooltip title={t("voip.call")} placement="top">
                  <Button
                    onClick={() => {
                      actionCall(`${values}`);
                    }}
                    type="link"
                    size="small"
                    icon={<FiPhoneForwarded style={{ fontSize: 16 }} />}
                  />
                </Tooltip>
              </Space>
            </div>
          </div>
        )
      );
    case "extension":
      return (
        !!values && (
          <div className="group flex flex-row items-center justify-between">
            <span
              className={`${textStyle} truncate underline decoration-dashed underline-offset-4`}
            >
              {values}
            </span>
            <div className="hidden group-hover:block">
              <Space size={3}>
                <Tooltip title={t("chat.action.copy")} placement="top">
                  <Button
                    size="small"
                    onClick={() => {
                      navigator.clipboard.writeText(values);
                      // message.open({
                      //   type: "success",
                      //   content: "Phone number copied",
                      // });
                    }}
                    type="link"
                    icon={<FiCopy style={{ fontSize: 16 }} />}
                  />
                </Tooltip>
                <Tooltip title={t("voip.call")} placement="top">
                  <Button
                    size="small"
                    onClick={() => actionCall(`${values}`)}
                    type="link"
                    icon={<FiPhoneForwarded style={{ fontSize: 16 }} />}
                  />
                </Tooltip>
              </Space>
            </div>
          </div>
        )
      );
    case "image":
      return (
        !!values && (
          <Image
            height={28}
            src={`${
              URL_ENV?.REACT_APP_BASE_URL + URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
            }${values.path}`}
          />
        )
      );
    case "album":
      return <RenderAlbum values={values} />;
    case "file":
      return <RenderFiles values={values} t={t} />;
    case "date_time":
      return <span className={textStyle}>{humanDate(values, t, "table")}</span>;
    case "date":
      return (
        <span className={textStyle}>
          {formatDateComparisonTableFamily(values)}
        </span>
      );
    case "range":
      return (
        !!values?.length && (
          <span className={textStyle}>{`${formatDateComparisonTableFamily(
            values[0]
          )} -> ${formatDateComparisonTableFamily(values[1])}`}</span>
        )
      );
    case "country":
      if (values) {
        const language =
          localStorage.getItem("language") === "fr" ? "fr" : "en";
        const getCountryName = (option) =>
          truncateString(
            language === "fr" ? option?.name_fr : option?.name_en,
            multipleOptions,
            true
          );
        const CountryTagContent = ({ flag, name }) => (
          <div className="flex items-center space-x-1">
            <span className="text-base">{flag}</span>
            <span className={textStyle}>{name}</span>
          </div>
        );
        if (Array.isArray(values) && values.length > 0) {
          const countryTags = relatedOptions.map((option, i) => (
            <CustomTag
              isCountry={true}
              key={option.id || i}
              maxChars={multipleOptions}
              content={
                <CountryTagContent
                  flag={option?.flag}
                  name={getCountryName(option)}
                />
              }
            />
          ));

          if (values.length === 1) {
            return countryTags[0];
          } else {
            const [firstTag, ...restTags] = countryTags;
            return (
              <div className="flex items-center ">
                {firstTag}
                <DisplayPopover
                  content={<Space direction="vertical">{restTags}</Space>}
                  nbr={restTags.length}
                />
              </div>
            );
          }
        } else if (typeof values === "string") {
          const option = relatedOptions;
          return (
            <CustomTag
              isCountry={true}
              content={
                <CountryTagContent
                  flag={option?.flag}
                  name={truncateString(
                    language === "fr" ? option?.name_fr : option?.name_en,
                    singleOption,
                    true
                  )}
                />
              }
            />
          );
        }
      }
      return null;
    case "ip address":
      return (
        !!values && (
          <div className="group flex flex-row  items-center justify-between">
            <span className={`${textStyle} truncate`}>{values}</span>
            <div className="hidden items-center group-hover:block">
              <Tooltip title={t("chat.action.copy")} placement="top">
                <Button
                  onClick={() => {
                    navigator.clipboard.writeText(values);
                  }}
                  type="link"
                  size="small"
                  icon={<FiCopy style={{ fontSize: 16 }} />}
                />
              </Tooltip>
            </div>
          </div>
        )
      );
    case "autocomplete":
      if (!values) return null;

      const isRelated = !!relatedType;
      const isSingleValue = typeof values === "string";
      const isArrayValue = Array.isArray(values) && values.length > 0;

      const getAvatar = (item) => {
        if (isRelated && relatedType === "family_module") {
          return (
            <DisplayAvatar
              name={item?.name || item?.label || item}
              size={20}
              icon={item?.family_id && renderIcon(item?.family_id, 14)}
              urlImg={
                item?.avatar &&
                `${URL_ENV.REACT_APP_BASE_URL}${URL_ENV.REACT_APP_SUFFIX_AVATAR_URL}${item.avatar}`
              }
            />
          );
        }
        return null;
      };

      const renderSingleTag = (item) => {
        const label = isRelated ? item?.label || item : item;
        return (
          <CustomTag
            content={label}
            avatar={getAvatar(item)}
            icon={isRelated ? item?.icon : null}
            color={isRelated ? item?.color : null}
          />
        );
      };

      const renderMultipleTags = (items) => {
        const renderTag = (item, index) => {
          const label = isRelated ? item?.label || item : item;
          const key = item?.id || index;

          return (
            <CustomTag
              key={key}
              content={label}
              avatar={getAvatar(item)}
              icon={isRelated ? item?.icon : null}
              color={isRelated ? item?.color : null}
              maxChars={multipleOptions}
            />
          );
        };

        const renderTags = items.map(renderTag);

        if (renderTags.length === 1) {
          return renderTags[0];
        }

        const [firstTag, ...restTags] = renderTags;

        return (
          <div className="flex items-center ">
            {firstTag}
            <DisplayPopover
              content={<Space direction="vertical">{restTags}</Space>}
              nbr={restTags.length}
            />
          </div>
        );
      };

      if (isSingleValue) {
        const item = isRelated ? relatedOptions || {} : values;
        return renderSingleTag(item);
      }

      if (isArrayValue) {
        const items = isRelated ? relatedOptions || [] : values;
        return renderMultipleTags(items);
      }

      return null;

    default:
      return (
        <span className={`${textStyle} truncate`}>
          {truncateString(values, singleOption, true)}
        </span>
      );
  }
};

export const CustomTag = ({
  content,
  color,
  icon,
  avatar,
  t,
  isCountry = false,
  maxChars = singleOption,
}) => {
  let colorName = "default";
  let displayContent = content;
  if (color) {
    const isColorExist = colors.find((c) => c.value === color);
    if (isColorExist) {
      colorName = isColorExist?.tagColor;
      if (content === color) {
        displayContent = t(`colors.${isColorExist?.label}`);
      }
    }
  }
  // return (
  //   <span className="inline-flex items-center rounded-md bg-black-50 px-2 py-1 text-xs text-black-700">
  //     Badge
  //   </span>
  // );
  return (
    <Tag
      bordered={false}
      color={colorName}
      style={{ paddingLeft: 2, paddingRight: 2 }}
      // icon={!!icon && <ChoiceIcons icon={icon} />}
      // style={{ borderRadius: "30%", padding: "0 10px" }}
    >
      <div className="flex  items-center justify-center space-x-1 	">
        {!!icon ? (
          <ChoiceIcons icon={icon} fontSize={14} height={null} top={0} />
        ) : !!avatar ? (
          avatar
        ) : null}
        {isCountry ? (
          <span className="text-[13px]">{displayContent}</span>
        ) : (
          <span className="text-[13px]">
            {truncateString(displayContent, maxChars, true)}
          </span>
        )}
      </div>
    </Tag>
  );
};
//
export const DisplayPopover = ({ content, nbr }) => {
  // if (!(data && Array.isArray(data) && data.length)) return null
  return (
    <Popover content={content} placement="bottomRight" arrow={false}>
      <Tag bordered={false} style={{ paddingLeft: 3, paddingRight: 3 }}>
        <span className={`${textStyle} cursor-help font-semibold`}>+{nbr}</span>
      </Tag>
    </Popover>
  );
};
//
export const renderIcon = (family_id, fontSize) =>
  family_id === 3 ? (
    <HeartHandshake size={fontSize} />
  ) : family_id === 5 ? (
    <AiOutlineShoppingCart style={{ fontSize: fontSize }} />
  ) : family_id === 6 ? (
    <TicketIconSphere size={fontSize} />
  ) : family_id === 7 ? (
    <Blocks size={fontSize} />
  ) : family_id === 8 ? (
    <LuPalmtree style={{ fontSize: fontSize }} />
  ) : family_id === 11 ? (
    <DollarOutlined style={{ fontSize: fontSize }} />
  ) : null;
//
export const RenderAlbum = ({ values, size = 24, maxCount = 3 }) => {
  //
  const [previewVisible, setPreviewVisible] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  if (!values?.length) return null;

  // Construct the image URLs
  const imageUrls = values.map(
    (img) =>
      `${URL_ENV?.REACT_APP_BASE_URL + URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL}${
        img?.path ? img?.path : img
      }`
  );

  return (
    <>
      <Avatar.Group
        size={size}
        shape="square"
        maxCount={maxCount}
        maxPopoverTrigger="click"
        maxStyle={{
          backgroundColor: "rgb(28, 64, 175)",
          color: "rgb(219, 234, 254)",
          fontWeight: 600,
          cursor: "pointer",
          display: "flex",
        }}
        // max={{
        //   count: 3,
        //   popover: "click",
        //   style: {
        //     backgroundColor: "rgb(28, 64, 175)",
        //     color: "rgb(219, 234, 254)",
        //     fontWeight: 600,
        //     cursor: "pointer",
        //     display: "flex",
        //   },
        // }}
      >
        {imageUrls.map((url, i) => (
          <Avatar
            key={i}
            size={size}
            src={url}
            onClick={() => {
              setCurrentImageIndex(i);
              setPreviewVisible(true);
            }}
            className="mx-3 cursor-pointer"
          />
        ))}
      </Avatar.Group>

      <div className="hidden">
        <Image.PreviewGroup
          preview={{
            visible: previewVisible,
            onVisibleChange: (vis) => setPreviewVisible(vis),
            onChange: (current, prev) => setCurrentImageIndex(current),
            current: currentImageIndex,
          }}
        >
          {imageUrls.map((url, i) => (
            <Image key={i} src={url} />
          ))}
        </Image.PreviewGroup>
      </div>
    </>
  );
};
//
export const RenderFiles = ({ values, t }) => {
  if (!values || !values.length) return null;

  values = Array.isArray(values) ? values : [values];

  const baseUrl =
    URL_ENV?.REACT_APP_BASE_URL + URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL;

  const firstFile = values[0];
  const firstFileName = firstFile?.file_name || firstFile;
  const firstFileUrl = `${baseUrl}${firstFile?.path || firstFile}`;

  if (values.length === 1) {
    return (
      <Tooltip title={t("contacts.openInNewTab")} placement="top">
        <a
          className={`${textStyle} truncate underline decoration-dashed underline-offset-4`}
          href={firstFileUrl}
          target="_blank"
          rel="noreferrer"
        >
          {truncateFileName(firstFileName, 30)}
        </a>
      </Tooltip>
    );
  } else if (values.length > 1) {
    const [firstFile, ...restFiles] = values;
    return (
      <div className="flex items-center space-x-1.5">
        <Tooltip title={t("contacts.openInNewTab")} placement="top">
          <a
            className={`${textStyle} truncate underline decoration-dashed underline-offset-4`}
            href={firstFileUrl}
            target="_blank"
            rel="noreferrer"
          >
            {truncateFileName(firstFileName, 24)}
          </a>
        </Tooltip>
        <Popover
          content={
            <Space direction="vertical">
              {restFiles.map((file, index) => {
                const fileName = file?.file_name || file;
                const fileUrl = `${baseUrl}${file?.path || file}`;
                return (
                  <a
                    key={index}
                    className={`${textStyle} truncate underline decoration-dashed underline-offset-4`}
                    href={fileUrl}
                    target="_blank"
                    rel="noreferrer"
                  >
                    {truncateFileName(fileName, 30)}
                  </a>
                );
              })}
            </Space>
          }
          placement="bottomRight"
          arrow={false}
        >
          <Tag bordered={false} style={{ paddingLeft: 3, paddingRight: 3 }}>
            <span className={`${textStyle} cursor-help font-semibold`}>
              +{restFiles?.length}
            </span>
          </Tag>
        </Popover>
      </div>
    );
  }
  return null;
};

export default RenderColumnsTable;
