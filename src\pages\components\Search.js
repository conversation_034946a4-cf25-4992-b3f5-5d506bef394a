import React, { forwardRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { FiSearch } from "react-icons/fi";
import { useDispatch, useSelector } from "react-redux";
import { Badge, Button, Checkbox, Dropdown, Tag, Tooltip, Input } from "antd";
import { BsCommand } from "react-icons/bs";
import { InfoCircleOutlined, MoreOutlined } from "@ant-design/icons";

import { setSearch } from "../../new-redux/actions/menu.actions/menu";
import { setNewInteraction } from "new-redux/actions/vue360.actions/vue360";

const SearchInTable = forwardRef(
  (
    {
      placeholder = "table.search",
      showSearchShortcut,
      searchParams,
      setSearchParams,
      source,
      disabled = false,
      setPageNumber = () => {},
    },
    ref
  ) => {
    const [hideTag, setHideTag] = useState(false);
    const [openParamsDropdown, setOpenParamsDropdown] = useState(false);

    const dispatch = useDispatch();
    const [t] = useTranslation("common");
    const { search } = useSelector((state) => state.form);

    const handleCheckChange = (key) => {
      let itemIdx = searchParams && searchParams.findIndex((el) => el === key);
      if (itemIdx > -1) {
        setSearchParams(searchParams.filter((e) => e !== key));
      } else {
        setSearchParams([...searchParams, key]);
      }
    };

    const items = [
      {
        key: "options",
        type: "group",
        label: t("tasks.searchOptions"),
        children: [
          {
            key: "label",
            label: (
              <Checkbox onChange={() => handleCheckChange("label")}>
                {t("tasks.searchByLabel")}
              </Checkbox>
            ),
          },
          {
            key: "name",
            label: (
              <Checkbox onChange={() => handleCheckChange("name")}>
                {t("tasks.searchByName")}
              </Checkbox>
            ),
          },
        ],
      },
    ];

    const handleOpenDropdown = (open) => {
      setOpenParamsDropdown(open);
    };

    const handleChange = (e) => {
      const { value } = e?.target;
      dispatch(setSearch(value));
    };

    return source === "activity" ? (
      <Input
        onFocusCapture={() => setHideTag(true)}
        onBlurCapture={() => setHideTag(false)}
        prefix={<FiSearch className="text-slate-400" />}
        onChange={(e) => {
          handleChange(e);
          setTimeout(() => setPageNumber(1), 500);
        }}
        placeholder={placeholder}
        style={{ width: "auto", height: "32px" }}
        value={search}
        allowClear
        ref={ref}
        suffix={
          <>
            <Tooltip title={t("toasts.searchInfo")}>
              <InfoCircleOutlined style={{ color: "#1890ff" }} />
            </Tooltip>
            <Dropdown
              trigger={["click"]}
              menu={{
                items,
              }}
              arrow
              open={openParamsDropdown}
              onOpenChange={handleOpenDropdown}
            >
              <Button
                type="text"
                shape="circle"
                size="small"
                icon={
                  <Badge dot={searchParams && searchParams?.length > 0}>
                    <MoreOutlined />
                  </Badge>
                }
                onClick={(e) => e?.stopPropagation()}
              />
            </Dropdown>
          </>
        }
      />
    ) : (
      <Input
        onFocusCapture={() => setHideTag(true)}
        onBlurCapture={() => setHideTag(false)}
        prefix={<FiSearch className="text-slate-400" />}
        disabled={disabled}
        onChange={(e) => {
          setPageNumber(1);
          dispatch(setSearch(e.target.value));
          if (source === "viewSphere") {
            dispatch(setNewInteraction({ type: "search" }));
          }
        }}
        placeholder={t(placeholder)}
        style={{ width: "280px" }}
        value={search}
        allowClear
        ref={ref}
        suffix={
          showSearchShortcut && !hideTag ? (
            navigator.userAgent.indexOf("Macintosh") !== -1 ? (
              <Tag
                bordered={false}
                style={{
                  color: "rgb(156 163 175)",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                }}
              >
                <BsCommand /> + <span>K</span>
              </Tag>
            ) : (
              <Tag bordered={false} style={{ color: "rgb(156 163 175)" }}>
                Ctrl + K
              </Tag>
            )
          ) : null
        }
      />
    );
  }
);

export default SearchInTable;
