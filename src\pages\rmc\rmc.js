import { useState } from "react";
import { LoadingAnimation } from "../components/loader";
import { useSelector, useDispatch } from "react-redux";
import { useLocation } from "react-router-dom";
import { URL_ENV } from "index";
import ShareVisio from "pages/layouts/visio/components/share-visio";
import { setOPenVisioRmc, setVisioRmcElementId } from "new-redux/actions/rmc.actions";

const Rmc = () => {
  const [hide, setHide] = useState(false);
  const token = localStorage.getItem("accessToken");
  const openVisioRmc = useSelector((state) => state?.rmc?.openVisioRmc);
  const location = useLocation();
  const dispatch = useDispatch();
  return (
    <div>
      {hide === false ? <LoadingAnimation /> : <></>}
      <iframe
        src={
          location.state
            ? `${URL_ENV?.REACT_APP_RMC_URL}?Token=${token}&conversation=${location.state}`
            : `${URL_ENV?.REACT_APP_RMC_URL}?Token=${token}`
        }
        title="chat"
        display="block"
        width="100%"
        // height= {`${deviceHeight}px -120px`}
        sendbox="allow-same-origin allow-popups"
        allowfullscreen="true"
        style={{ height: "calc(100vh - 70px)", border: "none" }}
        allowtransparency="true"
        onLoad={() => setHide(true)}
      ></iframe>
      {openVisioRmc && (
        <ShareVisio
          open={openVisioRmc}
          onClose={() => {
            dispatch(setVisioRmcElementId(null));
            dispatch(setOPenVisioRmc(false));
          }}
        />
      )}
      {/* emaild=${user?.email}&access_Token=azertyuikxnbzgxyuojklnzjbkgziulxjk23456789IUJHBVF&conversation=${location.state} */}
      {/* https://rmcdemo.comunikcrm.info/comuniksocialdev/admin.php?emaild=${user?.email}&access_Token=azertyuikxnbzgxyuojklnzjbkgziulxjk23456789IUJHBVF */}
    </div>
  );
};
export default Rmc;
