import { URL_ENV } from "index";
import { SET_CONTACT_HEADER_INFO } from "../../../new-redux/constants";

const getContactDataAndDispatch = (
  familyId,
  label,
  record,
  data,
  dispatch,
  pathName,
  navigate
) => {
  // console.log({ label });
  const result = {
    id: record?.key,
    family_id: familyId,
    name: label?.replaceAll("_", " "),
    image: null,
    phones: [],
    type: null,
    email: [],
  };
  for (const item in record) {
    const value = record[item];
    const fieldType = value?.fieldType;
    switch (fieldType) {
      case "image":
        if (!result["image"])
          result["image"] = `${
            URL_ENV?.REACT_APP_BASE_URL +
            URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
          }${value?.["value"]?.path}`;
        break;
      case "email":
        // if (!result["email"]) result["email"] = value?.["value"];
        if (value?.["value"]) result["email"].push(value?.["value"]);
        break;
      case "phone":
        if (Array.isArray(value?.["value"]))
          result["phones"]?.push(value?.["value"]);
        else
          value?.["value"] &&
            result["phones"]?.push(["+216", value?.["value"]]);
        break;
      default:
        break;
    }
  }
  let contactTypeId = null;
  let contactSourceId = null;
  data?.[0]?.fields?.forEach((item) => {
    if (item?.label === "contact_type") {
      contactTypeId = item?.id;
      return;
    }
    if (item?.label === "creation_source") {
      contactSourceId = item?.id;
      return;
    }
  });
  result["type"] = record?.[contactTypeId]?.value;
  result["source"] = record?.[contactSourceId]?.value;

  // console.log({ record, data, result });

  dispatch({
    type: SET_CONTACT_HEADER_INFO,
    payload: result || {},
  });

  dispatch({
    type: "SET_BACKWARD_STATE",
    payload: { pathName: pathName },
  });
  navigate(`${pathName}/v2/${record?.key}`);
};

export default getContactDataAndDispatch;
