// import './styles.scss';
import React, { useCallback, useEffect, useRef, useState } from "react";
import { Color } from "@tiptap/extension-color";
import { common, createLowlight } from "lowlight";
import CodeBlockLowlight from "@tiptap/extension-code-block-lowlight";
import ListItem from "@tiptap/extension-list-item";
import TextStyle from "@tiptap/extension-text-style";
import Table from "@tiptap/extension-table";
import TableCell from "@tiptap/extension-table-cell";
import TableHeader from "@tiptap/extension-table-header";
import TableRow from "@tiptap/extension-table-row";
import Underline from "@tiptap/extension-underline";
import Document from "@tiptap/extension-document";
import Gapcursor from "@tiptap/extension-gapcursor";
import Paragraph from "@tiptap/extension-paragraph";
import Text from "@tiptap/extension-text";
import Image from "@tiptap/extension-image";
import Highlight from "@tiptap/extension-highlight";
import TextAlign from "@tiptap/extension-text-align";
import { EditorContent, useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import "tippy.js/animations/shift-toward-subtle.css";
import Link from "@tiptap/extension-link";
import ImageResize from "tiptap-extension-resize-image";
import "./CutsomTable.css";

import { Icons } from "./Icons";

import Extensions, { TipTapEditorExtensions } from "./extensions";
import Props from "./props";
import { Button, Select, message } from "antd";
import "./Styles.css";
import { TipTapEditorProps } from "./TipTapEditorProps";
import BubbleMenu from "@tiptap/extension-bubble-menu";
import { NodeTypeDropdown } from "./NodeTypeDropdown";
import { useTranslation } from "react-i18next";
import { EditorBubbleMenu } from "./bubbleMenu";
import MainService from "../../../services/main.service.js";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import {
  modifySelectedNoteContent,
  modifySelfNote,
  removeSelectedNote,
  saveNewNoteAfterPost,
  saveSelfNoteError,
  saveSelfNoteSuccess,
  setNoteLocked,
  setNoteUnlocked,
  setSelectedNote,
  triggerSaveNote,
} from "../../../new-redux/actions/selfnotes.actions/selfnotes.js";
import { useLocation } from "react-router-dom";
import SuperchargedTableExtensions from "./Table/supercharged-table-kit";
import { EditorState, TextSelection } from "prosemirror-state";
import TaskList from "@tiptap/extension-task-list";
import TaskItem from "@tiptap/extension-task-item";
import FloatingToolbar from "./FloatingToolbar";
import { extractTitle } from "pages/notes/utils";

const useDebounce = (callback, delay) => {
  const timeoutRef = useRef(null);

  const debouncedFunction = (...args) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  };

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return debouncedFunction;
};

const MenuBarIcon = ({ editor }) => [
  {
    id: 1,
    name: "bold",
    icon: Icons.bold,
    onClick: () => editor.chain().focus().toggleBold().run(),
    disable: !editor.can().chain().focus().toggleBold().run(),
    isActive: editor.isActive("bold") ? "is-active text-green-700" : "",
    hover: false,
    split: false,
  },
  {
    id: 2,
    name: "italic",
    icon: Icons.italic,
    onClick: () => editor.chain().focus().toggleItalic().run(),
    disable: !editor.can().chain().focus().toggleItalic().run(),
    isActive: editor.isActive("italic") ? "is-active text-green-700" : "",
    hover: false,
    split: false,
  },
  {
    id: 21,
    name: "underline",
    icon: Icons.underline,
    onClick: () => editor.chain().focus().toggleUnderline().run(),
    disable: false,
    isActive: editor.isActive("underline") ? "is-active text-green-700" : "",
    hover: false,
    split: false,
  },
  {
    id: 3,
    name: "strike",
    icon: Icons.strikethrough,
    onClick: () => editor.chain().focus().toggleStrike().run(),
    disable: !editor.can().chain().focus().toggleStrike().run(),
    isActive: editor.isActive("strike") ? "is-active text-green-700" : "",
    hover: false,
    split: false,
  },
  {
    id: 4,
    name: "code",
    icon: Icons.code,
    onClick: () => editor.chain().focus().toggleCode().run(),
    disable: !editor.can().chain().focus().toggleCode().run(),
    isActive: editor.isActive("code") ? "is-active text-green-700" : "",
    hover: false,
    split: true,
  },
  {
    id: 5,

    items: [
      {
        name: "paragraph",
        icon: Icons.paragraph,
        onClick: () => editor.chain().focus().setParagraph().run(),
        disable: false,
        isActive: editor.isActive("paragraph")
          ? "is-active text-green-700"
          : "",
        hover: false,
        split: true,
      },
      {
        name: "heading1",
        icon: Icons.h1,
        onClick: () => editor.chain().focus().toggleHeading({ level: 1 }).run(),
        disable: false,
        isActive: editor.isActive("heading", { level: 1 })
          ? "is-active text-green-700"
          : "",
        hover: false,
        split: false,
      },
      {
        name: "heading2",
        icon: Icons.h2,
        onClick: () => editor.chain().focus().toggleHeading({ level: 2 }).run(),
        disable: false,
        isActive: editor.isActive("heading", { level: 2 })
          ? "is-active text-green-700"
          : "",
        hover: false,
        split: false,
      },
      {
        name: "heading3",
        icon: Icons.h3,
        onClick: () => editor.chain().focus().toggleHeading({ level: 3 }).run(),
        disable: false,
        isActive: editor.isActive("heading", { level: 3 })
          ? "is-active text-green-700"
          : "",
        hover: false,
        split: false,
      },
      {
        name: "heading4",
        icon: Icons.h4,
        onClick: () => editor.chain().focus().toggleHeading({ level: 4 }).run(),
        disable: false,
        isActive: editor.isActive("heading", { level: 4 })
          ? "is-active text-green-700"
          : "",
        hover: false,
        split: false,
      },
      {
        name: "heading5",
        icon: Icons.h5,
        onClick: () => editor.chain().focus().toggleHeading({ level: 5 }).run(),
        disable: false,
        isActive: editor.isActive("heading", { level: 5 })
          ? "is-active text-green-700"
          : "",
        hover: false,
        split: false,
      },
      {
        name: "bullet list",
        icon: Icons.ul,
        onClick: () => editor.chain().focus().toggleBulletList().run(),
        disable: false,
        isActive: editor.isActive("bulletList")
          ? "is-active text-green-700 list-disc"
          : "",
        hover: false,
        split: false,
      },
      {
        name: "ordered list",
        icon: Icons.ol,
        onClick: () => editor.chain().focus().toggleOrderedList().run(),
        disable: false,
        isActive: editor.isActive("orderedList")
          ? "is-active text-green-700 list-decimal"
          : "",
        hover: false,
        split: false,
      },
      {
        name: "checkboxes list",
        icon: Icons.taskList,
        onClick: () => editor.chain().focus().toggleTaskList().run(),
        disable: false,
        isActive: editor.isActive("taskList") ? "is-active text-green-700" : "",
        hover: false,
        split: false,
      },
    ],
    isSelect: true,
  },
  {
    id: 16,
    items: [
      {
        name: "align left",
        icon: Icons.alignLeft,
        onClick: () => editor.chain().focus().setTextAlign("left").run(),
        disable: false,
        isActive: editor.isActive({ textAlign: "left" }) ? "is-active" : "",
        hover: false,
        split: false,
      },
      {
        name: "align center",
        icon: Icons.alignCenter,
        onClick: () => editor.chain().focus().setTextAlign("center").run(),
        disable: false,
        isActive: editor.isActive({ textAlign: "center" })
          ? "is-active text-green-700 text-center"
          : "",
        hover: false,
        split: false,
      },
      {
        name: "align right",
        icon: Icons.alignRight,
        onClick: () => editor.chain().focus().setTextAlign("right").run(),
        disable: false,
        isActive: editor.isActive({ textAlign: "right" }) ? "is-active" : "",
        hover: false,
        split: false,
      },
      {
        name: "align justify",
        icon: Icons.alignJustify,
        onClick: () => editor.chain().focus().setTextAlign("justify").run(),
        disable: false,
        isActive: editor.isActive({ textAlign: "justify" }) ? "is-active" : "",
        hover: false,
        split: true,
      },
    ],

    isSelect: true,
  },

  {
    id: 20,
    name: "highlight",
    icon: Icons.bg,
    onClick: () => editor.chain().focus().toggleHighlight().run(),
    disable: false,
    isActive: editor.isActive("highlight") ? "is-active text-green-700" : "",
    hover: false,
    split: false,
  },
  {
    id: 10,
    name: "code block",
    icon: Icons.codeblock,
    onClick: () => editor.chain().focus().toggleCodeBlock().run(),
    disable: false,
    isActive: editor.isActive("codeBlock") ? "is-active text-green-700" : "",
    hover: false,
    split: false,
  },
  {
    id: 11,
    name: "blockquote",
    icon: Icons.blockquote,
    onClick: () => editor.chain().focus().toggleBlockquote().run(),
    disable: false,
    isActive: editor.isActive("blockquote") ? "is-active text-green-700" : "",
    hover: false,
    split: false,
  },
  {
    id: 12,
    name: "table",
    icon: Icons.table,
    onClick: () =>
      editor
        .chain()
        .focus()
        .insertTable({ rows: 3, cols: 3, withHeaderRow: true })
        .run(),
    disable: false,
    isActive: editor.isActive("table") ? "is-active text-green-700" : "",
    hover: true,
    split: true,
  },
  {
    id: 30,
    name: "undo",
    icon: Icons.undo,
    onClick: () => editor.chain().focus().undo().run(),
    disable: !editor.can().undo(),
    isActive: editor.isActive("table") ? "is-active text-green-700" : "",
    hover: false,
    split: false,
  },
  {
    id: 31,
    name: "redo",
    icon: Icons.redo,
    onClick: () => editor.chain().focus().redo().run(),
    disable: !editor.can().redo(),
    isActive: editor.isActive("table") ? "is-active text-green-700" : "",
    hover: false,
    split: true,
  },
];

export function MenuBar({ editor, setImageURL }) {
  const [open, setOpen] = useState(true);
  const fileInputRef = useRef(null);

  if (!editor) {
    return null;
  }
  const MenuBarIconValue = MenuBarIcon({ editor });

  const handleIconClick = () => {
    fileInputRef.current?.click();
  };
  const handleChangeHeading = (value) => {
   // console.log("value got", value);
    if (value === "paragraph") {
      editor.chain().focus().setParagraph().run();
    } else if (value === "heading1") {
      editor.chain().focus().toggleHeading({ level: 1 }).run();
    } else if (value === "heading2") {
      editor.chain().focus().toggleHeading({ level: 2 }).run();
    } else if (value === "heading3") {
      editor.chain().focus().toggleHeading({ level: 3 }).run();
    } else if (value === "heading4") {
      editor.chain().focus().toggleHeading({ level: 4 }).run();
    } else if (value === "heading5") {
      editor.chain().focus().toggleHeading({ level: 5 }).run();
    } else if (value === "align left") {
      editor.chain().focus().setTextAlign("left").run();
    } else if (value === "align center") {
      editor.chain().focus().setTextAlign("center").run();
    } else if (value === "align right") {
      editor.chain().focus().setTextAlign("right").run();
    } else if (value === "align justify") {
      editor.chain().focus().setTextAlign("justify").run();
    } else if (value === "ordered list") {
      editor.chain().focus().toggleOrderedList().run();
    } else if (value === "bullet list") {
      editor.chain().focus().toggleBulletList().run();
    } else if (value === "checkboxes list") {
      console.log("toggleTaskList");
      editor.chain().focus().toggleTaskList().run();
    }
  };
  return (
    <div className="flex w-full flex-wrap items-center gap-1 bg-white p-2  text-black">
      <input
        type="color"
        onInput={(event) =>
          editor.chain().focus().setColor(event.target.value).run()
        }
        value={editor.getAttributes("textStyle").color}
      />
      {MenuBarIconValue.map((item) =>
        item.hover ? (
          <>
            {item?.isSelect ? (
              <Select
                // defaultValue={item.items[0]}
                size="small"
                style={{
                  width: 120,
                }}
                onChange={handleChangeHeading}
                options={item.items.map((el) => ({
                  label: (
                    <div className="flex items-center space-x-1">
                      <el.icon size={16} />
                      <span>{el.name}</span>
                    </div>
                  ),
                  value: el.name,
                }))}
              />
            ) : (
              <Button
                key={item.id}
                // onClick={item.onClick}
                size="small"
                disabled={item.disable}
                className={`${
                  item.disable ? "cursor-not-allowed" : "cursor-pointer"
                } flex items-center`}
                type="text"
              >
                <item.icon size={16} />
              </Button>
            )}
            {item.split && (
              <div className="mx-1 flex h-6 w-[1px] bg-gray-100" />
            )}
            {/* {TableMenu({ editor }).map((menuItem) => (
                  <MenubarItem key={menuItem.id} onClick={menuItem.action}>
                    {menuItem.name}
                  </MenubarItem>
                ))} */}
          </>
        ) : (
          <>
            {item?.isSelect ? (
              <Select
                defaultValue={item.items[0].name}
                size="small"
                style={{
                  width: "50px",
                }}
                onChange={(value) => handleChangeHeading(value)}
                options={item.items.map((el) => ({
                  label: (
                    <div className="flex items-center space-x-1">
                      <el.icon size={16} />
                    </div>
                  ),
                  value: el.name,
                }))}
              />
            ) : (
              <div className="flex h-full items-center gap-1">
                <Button
                  key={item.id}
                  size="small"
                  onClick={item.onClick}
                  disabled={item.disable}
                  className={`${
                    item.disable
                      ? "cursor-not-allowed p-1"
                      : "hover: cursor-pointer p-1 hover:bg-gray-100"
                  } + ${item.isActive ? item.isActive : ""} flex items-center`}
                >
                  <item.icon size={16} />
                </Button>
                {/* {item.split && (
              <div className="mx-1 w-[1px] flex bg-gray-100 h-6" />
            )} */}
              </div>
            )}
          </>
        )
      )}
      {/* <div className="cursor-pointer hover:bg-gray-100 hover: p-1">
        <input
          type="file"
          onChange={handleImageChange}
          ref={fileInputRef}
          className="hidden"
        />
        <Icons.image onClick={handleIconClick} />
      </div> */}
    </div>
  );
}

function EditorNote(props) {
  const {
    editorText,
    contentSignature,
    source = "",
    selectedNote,
    setClickedNote,
    cantEdit,
    signature,
    setHasSelectedNode = () => {},
  } = props;

  const selectedSelfNote = useSelector(
    (state) => state.selfNotesReducer.selectedNote
  );
  // console.log("selectedNote", selectedNote);
  const lowlight = createLowlight(common);
  const editorRef = useRef();
  const [cursorPosition, setCursorPosition] = React.useState({});
  const previousPosition = useRef({});
  const [imageURL, setImageURL] = useState(null);
  const [showMenuBar, setShowMenuBar] = useState(null);
  const [messageApi, contextHolder] = message.useMessage();
  const [t] = useTranslation("common");
  const { pathname } = useLocation();
  const dispatch = useDispatch();

  const handleImageChange = (file) => {
    let filesize = (file.size / 1024 / 1024).toFixed(4);
    if (!file.type.includes("image")) {
      messageApi.open({
        type: "error",
        content: t("chat.bot.acceptedOnlyImageType"),
      });
    }
    if (file && file.type.includes("image") && filesize < 10) {
      const reader = new FileReader();
      reader.onloadend = () => {
        if (typeof reader.result === "string") {
          setImageURL(reader.result);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const currentUser = useSelector((state) => state.user.user);

  const saveNote = useSelector((state) => state.selfNotesReducer.triggerSave);
  const [isSaving, setIsSaving] = useState(false);

  const [updatesQueue, setUpdatesQueue] = useState([]);

  const testText = `<div data-type="rootblock"><p>ffdfdfdsfds</p></div><div data-type="rootblock"><p>dfdfdfdf</p></div><div data-type="rootblock"><h1>fdfdsfdsfdsfdsf</h1></div><div data-type="rootblock"><p><strong>fdsfdsfdsfdfdsfdfdf</strong></p></div>`;
  let editor;

  // console.log("selectedNote", selectedNote);

  //   const [editor, setEditor] = useState(null);

  //   const [editable, setEditable] = useState(false);

  //   useEffect(() => {
  //     if (
  //       selectedSelfNote?.is_locked &&
  //       selectedSelfNote?.locker_id == currentUser.id
  //     ) {
  //       setEditable(true);
  //     } else {
  //       setEditable(false);
  //     }
  //   }, [selectedSelfNote, currentUser]);

  const [editorKey, setEditorKey] = useState(0);

  useEffect(() => {
    setEditorKey(editorKey + 1);
  }, [selectedSelfNote?.is_locked, selectedSelfNote?.locker_id]);

  // useEffect(() => {
  //   if (selectedSelfNote?.isNew) {
  //     editor?.setEditable(true);
  //   } else if (currentUser?.id == selectedSelfNote?.user) {
  //     editor?.setEditable(true);
  //   } else {
  //     editor?.setEditable(false);
  //   }
  // }, [selectedSelfNote]);
  editor = useEditor({
    // editable:
    // selectedNote?.shared_with?.length > 0
    //   ? selectedSelfNote?.is_locked &&
    //     selectedSelfNote?.locker_id == currentUser.id
    //     ? true
    //     : false
    //   : true,
    // content: selectedSelfNote?.content,
    // editable: selectedSelfNote?.isNew
    //   ? true
    //   : currentUser?.id == selectedSelfNote?.user,
    // editable: selectedSelfNote?.permission == 1 ? true : false,
    // editable: true,
    key: editorKey,
    editorProps: {
      handleDrop: function (view, event, slice, moved) {
        if (
          !moved &&
          event.dataTransfer &&
          event.dataTransfer.files &&
          event.dataTransfer.files[0]
        ) {
          // if dropping external files
          let file = event.dataTransfer.files[0]; // the dropped file
          handleImageChange(file);

          return true; // handled
        }
        return false; // not handled use default behaviour
      },
      // handleDOMEvents: {
      //   focus: (view, event) => {
      //     // Prevent unwanted scrolling when focusing
      //     event.preventDefault();
      //     return false;
      //   },
      // },
      // handleScrollToSelection: false,
    },
    extensions: [
      ...TipTapEditorExtensions,
      ImageResize,
      Color.configure({ types: [TextStyle.name, ListItem.name] }),
      TextStyle.configure({ types: [ListItem.name] }),
      // StarterKit,
      // StarterKit.configure({
      //   bulletList: {
      //     keepMarks: true,
      //     keepAttributes: false, // TODO : Making this as `false` becase marks are not preserved when I try to preserve attrs, awaiting a bit of help
      //   },
      //   orderedList: {
      //     keepMarks: true,
      //     keepAttributes: false, // TODO : Making this as `false` becase marks are not preserved when I try to preserve attrs, awaiting a bit of help
      //   },
      // }),
      TextAlign.configure({
        types: ["heading", "paragraph"],
      }),
      Image.configure({
        inline: true,
        allowBase64: true,
      }),
      Underline.configure({
        HTMLAttributes: {
          class: "my-custom-class",
        },
      }),
      // CodeBlockLowlight.configure({
      //   lowlight,
      // }),
      Highlight.configure({
        multicolor: true,
      }),
      Link,
      // Document,
      Paragraph,
      Text,
      Table.configure({
        HTMLAttributes: {
          class: "note-tiptap-table",
        },
        resizable: true,
        allowTableNodeSelection: true,
      }),
      TableRow,
      TableHeader,
      TableCell.extend({
        addKeyboardShortcuts() {
          return {
            ArrowUp: ({ editor }) => {
              const { state } = editor;
              const { $head } = state.selection;

              if ($head.parent.type.name === "tableCell") {
                const posAbove = state.doc.resolve($head.before()).before();
                if (posAbove) {
                  editor.commands.setTextSelection(posAbove);
                }
                return true;
              }
              return false;
            },
            ArrowDown: ({ editor }) => {
              const { state } = editor;
              const { $head } = state.selection;

              if ($head.parent.type.name === "tableCell") {
                const posBelow = state.doc.resolve($head.after()).after();
                if (posBelow) {
                  editor.commands.setTextSelection(posBelow);
                }
                return true;
              }
              return false;
            },
            Enter: ({ editor }) => {
              const { state, view } = editor;
              const { $head } = state.selection;

              if ($head.parent.type.name === "tableCell") {
                // Insert a new line in the current cell
                const position = $head.pos + 1;
                const transaction = state.tr.insertText("\n", position);

                view.dispatch(transaction);
                view.focus(); // Ensure focus remains inside the editor
                return true;
              }
              return false;
            },
          };
        },
      }),
      Gapcursor,
      TaskList.extend({
        group: "block",
        content: "taskItem+",
      }),
      TaskItem.extend({
        content: "paragraph block*",
        defining: true,
      }),
    ],
    // .filter((extension) => {
    //   if (selectedNote?.is_locked) {
    //     return ![ImageResize.name, Image.name, Link.name].includes(
    //       extension.name
    //     );
    //   } else {
    //     return true;
    //   }
    // })
    selectedSelfNote,

    onBlur({ editor, event }) {
      // setClickedNote({ ...selectedNote, content: editor?.getHTML() });
      source === "signature" && setClickedNote(editor?.getHTML());

      // The editor isn’t focused anymore.
    },
    onUpdate: ({ editor }) => {
      editorRef.current = editor;
      //resotre the cursor position

      const { view } = editor;
      const { state, dispatch } = view;

      const selection = state.selection;

      // console.log("selection", selection);

      dispatch(state.tr.setSelection(selection));
    },
    onSelectionUpdate({ editor }) {
      const { empty, from, to } = editor.view.state.selection;
      if (pathname === "/profile/signature") {
        if (
          editor?.getHTML() == '<div data-type="rootblock"><p></p></div>' ||
          editor?.getHTML() === ""
          // || editor?.getHTML()===signature?.value
        ) {
          // setDisabled(true)
          setHasSelectedNode(false);
        } else {
          // setDisabled(false);
          setHasSelectedNode(true);
        }
      }
    },
  });

  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === "Enter" && editor.isActive("tableCell")) {
        event.preventDefault();

        // Insert a new paragraph inside the cell
        editor.chain().focus().insertContent("<p></p>").run();

        // Prevent scrolling
        event.stopPropagation();
      }
    };

    document.addEventListener("keydown", handleKeyDown);

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [editor]);

  useEffect(() => {
    if (
      selectedSelfNote?.permission == 1 ||
      selectedSelfNote?.isNew ||
      selectedSelfNote?.user == currentUser?.id
    ) {
      editor?.setEditable(true);
      // editor?.enable();
    } else {
      // editor?.disable();
      editor?.setEditable(false);
    }
  }, [selectedSelfNote, editor]);

  // console.log("editor", editor);

  const getCursorPosition = () => {
    if (!editor) return { from: 0, to: 0 };
    const { from, to } = editor.state.selection;
    return { from, to };
  };

  //TODO: fix this tomorrow

  // useEffect(() => {
  //   if (editorRef.current) {
  //     editorRef.current.setEditable(
  //       selectedSelfNote?.is_locked &&
  //         selectedSelfNote?.locker_id == currentUser.id
  //     );
  //   }
  //   if (selectedNote?.is_locked && selectedSelfNote?.locker_id)
  //   editor?.setEditable(
  //     selectedNote?.shared_with?.length > 0
  //       ? selectedSelfNote?.is_locked &&
  //         selectedSelfNote?.locker_id == currentUser.id
  //         ? true
  //         : false
  //       : true
  //   );
  //   console.log(editor?.setEditable);
  // }, [selectedSelfNote?.is_locked, selectedSelfNote?.locker_id]);

  // const workerRef = useRef(null);

  // useEffect(() => {
  //   workerRef.current = new Worker(new URL("./saveWorker.js", import.meta.url));

  //   workerRef.current.onmessage = function (e) {
  //     const { type, payload } = e.data;

  //     switch (type) {
  //       case "SAVE_NOTE":
  //         handleSaveNoteInWorker(payload.noteId, payload.data);
  //         break;

  //       default:
  //         break;
  //     }
  //   };

  //   return () => {
  //     workerRef.current.terminate();
  //   };
  // }, []);

  const isFirstChange = useRef(true);

  const savingStatus = useRef(true);

  const handleSaveNote = useCallback(
    (content) => {
      console.log("initial render");
      console.log("content in the callback", content);

      const title = extractTitle(content);
      const oldTitle = extractTitle(selectedSelfNote?.content);

      console.log("title", title);
      console.log("old title", extractTitle(selectedSelfNote?.content));

      if (!content) return;

      if (!editor || isSaving) return;

      const data = {
        // content: editor.getHTML(),
        content: content,
        permission: 1,
      };

      if (title != extractTitle(selectedSelfNote?.content)) {
        data.room_name = title;
      }

      const { from, to } = getCursorPosition();
      console.log(`Cursor from position: ${from}, to position: ${to}`);

      setCursorPosition({ from, to });

      setIsSaving(true);

      // if (selectedSelfNote?.isNew) {
      //   handleCreateNote(content);
      // } else {

      // }

      MainService.updateNote360(selectedSelfNote._id, data)
        .then((response) => {
          //after the update i want to set the selection to the previous position
          dispatch(modifySelfNote(response.data.data));
          dispatch(saveSelfNoteSuccess());

          console.log(
            "response",
            selectedSelfNote?._id == response.data.data._id
          );

          if (selectedSelfNote?._id == response.data.data._id) {
            // dispatch(setSelectedNote(response.data.data));
            console.log("id matched with remote id");
            console.log("response", response.data.data._id);
            console.log("selectedNote", selectedSelfNote?._id);
            // dispatch(modifySelectedNoteContent(response.data.data.content));
          }
          // const tr = editor.state.tr.setSelection(
          //   TextSelection.create(editor.state.doc, 10, 10)
          // );
          // editor.view.dispatch(tr);
        })
        .catch((error) => {
          console.error("Failed to save note:", error);
          dispatch(saveSelfNoteError());
        })
        .finally(() => {
          setIsSaving(false);
          dispatch(triggerSaveNote());
          savingStatus.current = false;
          // isFirstChange.current = true;
        });
      // savingStatus.current = true;
    },
    [dispatch, selectedSelfNote?._id, isSaving, editor]
  );

  const debouncedSave = useDebounce((content) => {
    handleSaveNote(content);
  }, 600);

  const debounceCreateNote = useDebounce((content) => {
    handleCreateNote(content);
  }, 600);

  useEffect(() => {
    if (!editor) return;

    const handleChange = () => {
      // console.log("editor changed", isFirstChange.current);
      // if (isFirstChange.current) {
      //   MainService.lockNote(selectedNote._id).then((response) => {
      //     console.log("lock note", response);
      //     isFirstChange.current = false;
      //   });
      // }

      const { from, to } = getCursorPosition();

      previousPosition.current = { from, to };

      setCursorPosition({ from, to });

      if (!isSaving) {
        let content = editor.getHTML();
        console.log("content", content);
        if (selectedSelfNote?.isNew) {
          debounceCreateNote(content);
        } else {
          debouncedSave(content);
        }
      }
    };

    // Register a listener for editor content change
    editor.on("update", handleChange);

    return () => {
      editor.off("update", handleChange);
    };
  }, [debouncedSave, isSaving, editor, selectedSelfNote?._id]);

  // const restoreCursorPosition = () => {
  //   if (!editor) return;

  //   const { from, to } = previousPosition.current;

  //   const tr = editor.state.tr.setSelection(
  //     TextSelection.create(editor.state.doc, from, to)
  //   );

  //   editor.view.dispatch(tr);
  // };

  // useEffect(() => {
  //   restoreCursorPosition();
  // }, [cursorPosition]);

  const restoreCursorPosition = () => {
    if (!editor) return;

    const { from, to } = previousPosition.current;
    const docLength = editor.state.doc.content.size;

    // Check if the position exists in the document
    const validFrom = Math.min(from, docLength);
    const validTo = Math.min(to, docLength);

    const tr = editor.state.tr.setSelection(
      TextSelection.create(editor.state.doc, validFrom, validTo)
    );

    editor.view.dispatch(tr);
  };

  // useEffect(() => {
  //   restoreCursorPosition();
  // }, [cursorPosition]);

  // const lockNote = () => {
  //   MainService.lockNote(selectedSelfNote?._id)
  //     .then((response) => {
  //       // console.log("lock note 01", response);
  //       isFirstChange.current = false;

  //       dispatch(
  //         setNoteLocked({
  //           noteId: selectedSelfNote?._id,
  //           lockerId: response.data.data.locker_id,
  //           lockerUuid: response.data.data.is_locked,
  //         })
  //       );

  //       dispatch(setSelectedNote(response.data.data));
  //     })
  //     .catch((error) => {
  //       console.error("Failed to lock note:", error);
  //     });
  // };

  // useEffect(() => {
  //   // console.log("Trigger lock");
  //   lockNote();

  //   return () => {
  //     // console.log("Component unmounting");
  //     MainService.unlockNote(selectedSelfNote?._id)
  //       .then((response) => {
  //         // console.log("unlock note 01", response);
  //         dispatch(setNoteUnlocked(selectedSelfNote?._id));
  //         // dispatch(removeSelectedNote());
  //       })
  //       .catch((error) => {
  //         console.error("Failed to unlock note:", error);
  //       });
  //   };
  // }, [selectedSelfNote?._id]);

  //   useEffect(() => {
  //     setTimeout(() => {
  //       lockNote();
  //     }, 1000);
  //   }, [selectedSelfNote?.is_locked]);

  const handleCreateNote = useCallback((content) => {
    let formData = new FormData();
    // formData.append("content", editor?.getHTML());
    formData.append("content", content);

    console.log("selectedNote", selectedSelfNote);

    MainService.createNote360(formData)
      .then((response) => {
        // console.log(response);

        dispatch(
          saveNewNoteAfterPost({
            localId: selectedSelfNote._id,
            newNote: response?.data?.data[0]?.note,
          })
        );
        // setClickedNote(
        //   response?.data?.data[0]?.note
        //     ? response?.data?.data[0]?.note
        //     : selectedSelfNote
        // );

        // dispatch(
        //   setSelectedNote(
        //     response?.data?.data[0]?.note
        //       ? response?.data?.data[0]?.note
        //       : selectedSelfNote
        //   )
        // );

        console.log("selectedNote", selectedSelfNote);

        // if (selectedSelfNote?.isNew && selectedSelfNote?._id?.length < 10) {
        //   dispatch(setSelectedNote(response?.data?.data[0]?.note));
        // }

        dispatch(saveSelfNoteSuccess());

        return response;
      })
      .then((response) => {})
      .catch((error) => {
        console.log(error);
        dispatch(saveSelfNoteError());
      })
      .finally(() => {
        dispatch(triggerSaveNote());
      });
  });

  // useEffect(() => {
  //   // if (saveNote && source !== "signature") {
  //   if (selectedSelfNote?.isNew === true) {
  //     handleCreateNote();
  //     // console.log("selectedNote", selectedSelfNote);
  //   } else {
  //     handleSaveNote();
  //     // console.log("saveNote", saveNote);
  //     // console.log("selectedNote", selectedSelfNote);
  //   }
  //   // }
  // }, [saveNote, source]);

  const handleSelectionUpdate = () => {
    const { selection } = editor.state;
    const { from, to } = selection;
    if (from !== to) {
      const startPosition = editor.view.coordsAtPos(from);
      const endPosition = editor.view.coordsAtPos(to);

      // setCursorPosition(editor.view.dom.getBoundingClientRect());
    }
  };

  const getSelectionCoordinates = (selection) => {
    const { from, to } = selection;

    const startPosition = editor.view.coordsAtPos(from);
    const endPosition = editor.view.coordsAtPos(to);

    const top = startPosition.top;
    const left = startPosition.left;
    const right = endPosition.right;
    const bottom = endPosition.bottom;
    setCursorPosition(top);
  };
  const handleDoubleClick = (event) => {
    const selection = editor.state.selection;
    const { from, to, empty } = selection;
    getSelectionCoordinates(selection);
    // setCursorPosition(anchor)

    // Vérifier si la sélection est un double-clic sur du texte
    const isAlreadySelected =
      !empty && from === to && from === editor.state.selection.from;
    setShowMenuBar(!empty && from !== to && !isAlreadySelected);
  };
  const handleSelectionChange = () => {
    const selection = editor.state.selection;
    // Vérifier si la sélection est vide (déselection)
    if (selection.empty) {
      // La déselection est détectée
      setShowMenuBar(false);
    }
  };
  const onScroll = async (e) => {
    if (e?.currentTarget?.scrollTop) {
    }
  };

  function resetEditorContent(editor, content) {
    editor?.commands?.setContent(content);
    const newEditorState = EditorState.create({
      doc: editor.state.doc,
      plugins: editor.state.plugins,
      schema: editor.state.schema,
    });
    editor.view.updateState(newEditorState);
  }

  useEffect(() => {
    if (editor && imageURL) {
      editor.commands.setImage({
        src: imageURL,
      });
    }
  }, [imageURL]);

  useEffect(() => {
    if (editor) {
      if (selectedSelfNote?.content)
        // editor?.commands?.setContent(selectedSelfNote?.content);
        resetEditorContent(editor, selectedSelfNote?.content);
      // editor?.commands?.clearHistory();
      // else editor?.chain().focus().insertContent("").run();
    }
  }, [selectedSelfNote?.content, editor, selectedSelfNote?._id]);

  const selfNotes = useSelector((state) => state.selfNotesReducer.selfNotes);

  const [showBubbleMenu, setShowBubbleMenu] = useState(false);

  useEffect(() => {
    if (
      selectedSelfNote?.permission == 1 ||
      selectedSelfNote?.isNew ||
      selectedSelfNote?.user == currentUser?.id
    ) {
      setShowBubbleMenu(true);
    } else {
      setShowBubbleMenu(false);
    }
  }, [selectedSelfNote]);

  return (
    <div
      className="temp mt-12 pb-12"
      style={{
        width: "100%", // Full width
        height: "100vh", // Full viewport height
        overflow: "hidden",
      }}
    >
      {/* <div className={showMenuBar?`visible`:`invisible`} style={{position:"fixed",top:Math.ceil((cursorPosition))-50,zIndex:500000,overflow:"hidden"}}>
        <MenuBar editor={editor} setImageURL={setImageURL} />
        
        </div> */}

      {contextHolder}

      <EditorContent
        editor={editor}
        style={{
          position: "absolute", // Absolute positioning inside the parent
          top: 0, // Align to the top
          left: 0, // Align to the left
          right: 0, // Stretch to the right
          bottom: 0, // Stretch to the bottom

          overflowY: "auto", // Enable vertical scrolling for long content
          // Optional padding for better readability
          boxSizing: "border-box", // Include padding in dimensions
          // marginTop: cantEdit ? "6rem" : "3rem",
          marginTop: "1rem",
        }}
        className="templateEmail noteSpeEditor"
        initialContent={"Test"}
      />

      {editor && <FloatingToolbar editor={editor} />}
      {/*  ? (
        <EditorBubbleMenu editor={editor} />
      ) : ' */}
      <EditorBubbleMenu
        editor={editor}
        isAllowedToWrite={
          selectedSelfNote?.permission == 1 ||
          selectedSelfNote?.isNew ||
          selectedSelfNote?.user == currentUser?.id
        }
      />
    </div>
  );
}

export default EditorNote;
