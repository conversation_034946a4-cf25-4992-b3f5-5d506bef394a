import { decodeJWT, getName } from "pages/layouts/chat/utils/ConversationUtils";
import { store } from "../../new-redux/store";
import { generateAxios } from "../../services/axiosInstance";
import { URL_ENV } from "index";
import MainService from "services/main.service";
import axios from "axios";
import { setTypeUserLiveChat } from "new-redux/actions/chat.actions/Input";
import { setOpenTourInProfile } from "new-redux/actions/menu.actions/menu";

function decryptValue(encrypted) {
  // same api key as backend project in .env file
  const SECRET_KEY_LOCAL = URL_ENV?.REACT_APP_SECRET_KEY;
  // Décodez depuis Base64
  const decodedEncrypted = atob(encrypted);

  const keyLength = SECRET_KEY_LOCAL.length;
  let decrypted = "";

  for (let i = 0; i < decodedEncrypted.length; i++) {
    decrypted += String.fromCharCode(
      decodedEncrypted.charCodeAt(i) ^
        SECRET_KEY_LOCAL.charCodeAt(i % keyLength)
    );
  }

  // Supprimez le rembourrage
  const paddingLength = decrypted.charCodeAt(decrypted.length - 1);
  decrypted = decrypted.slice(0, -paddingLength);
  return decrypted;
}
export const getUserDetails = async (userId) => {
  return await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(`/get-fields-values/${4}?id=${userId}`);
};

export const getUserChatWithComunik = async () => {
  const { user } = await store.getState().user;

  try {
    if (user?.id) {
      const data = new FormData();
      data.append("token", "c1adab37bcb62f2a7f99b675c2ad6c0fdc50b51e");
      data.append("function", "add-user-external");
      data.append("first_name", getName(user?.label, "name"));
      data.append("last_name", user?.tenant);
      data.append("email", user?.email);
      data.append("user_type", "user");
      data.append("status", "1");
      data.append(
        "extra",
        JSON.stringify({
          phone: [user?.phone, "Phone"],
          guest_type: [user?.role, "guest_type"],
          tenant: [user?.tenant, "tenant"],
          uid: [user?.id, "uid"],
        })
      );
      if (user?.avatar)
        data.append(
          "profile_image",
          URL_ENV?.REACT_APP_BASE_URL +
            URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
            user?.avatar
        );
      const response = await axios.post(
        "https://rmcdemo.comunikcrm.info/comuniksocial/include/api.php",
        data
      );
      store.dispatch(
        setTypeUserLiveChat(
          Array.isArray(response?.data?.response)
            ? response?.data?.response[0]
            : response?.data?.response
        )
      );
    }
  } catch (error) {
    console.error("Error adding user guest:", error);
  }
};

export const fetchProfile = async (dispatch, setUserInfos) => {
  let user;
  try {
    user = await store.getState()?.user?.user;
    const response = await MainService.getProfile();
    const {
      data: { data: toursAccess },
    } = await MainService.getToursAccess();
    const decodedToken = decodeJWT(response.data.message.token_access);
    const host = decodedToken.host;
    const urlToCompare = new URL(URL_ENV?.REACT_APP_DOMAIN);

    const keys = Object.keys(decodedToken).filter(
      (key) => key !== "host" && key !== "exp"
    );
    let access = {};
    keys.forEach((key) => {
      access[key] = (
        process.env.REACT_APP_BRANCH === "devLocal"
          ? true
          : host === urlToCompare.host
      )
        ? decryptValue(decodedToken[key]).split("_")[1]
        : "0";
    });

    const finalObject = { ...response.data.message, toursAccess };
    finalObject.access = access;

    delete finalObject.token_access;
    dispatch(setUserInfos(finalObject));
  } catch (err) {
    dispatch(setUserInfos(user?.id ? user : {}));
  }
};

export const updatePassword = async (payload) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).post(`change-password`, payload);

export const sendForgotPasswordEmail = async (payload) =>
  await generateAxios(
    URL_ENV?.REACT_APP_OAUTH2_URL + process.env.REACT_APP_SUFFIX_API
  ).post(`password/forgot`, payload);
export const deleteCookie = (name) => {
  document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
};

export const logoutRmc = async () => {
  try {
    let data = new FormData();
    data.append("token", "c1adab37bcb62f2a7f99b675c2ad6c0fdc50b51e");
    data.append("function", "logout");

    const response = await axios.post(
      "https://rmcbackdev1.sphere.tn:4543/include/ajax.php",
      data
    );
    deleteCookie("sb-login");
    deleteCookie("sb-rmc");
    window.SBF.reset();
  } catch (err) {}
};
export const closeTour = async (setOpenTour, pathname, dispatch) => {
  setOpenTour(false);

  await axios.post(
    `${
      process.env.REACT_APP_URL_TENANT + process.env.REACT_APP_SUFFIX_API
    }tours/update-by-selector/${pathname}`,
    { status: 0 }
  );
  dispatch(setOpenTourInProfile(false));
};
