import { LoadingOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Spin } from "antd";
import React, { useState } from "react";
import { isGuestConnected } from "utils/role";

const CustomSteps = ({ dataSteps, handleChange, selectedTags, loadStage }) => {
  const [current, setCurrent] = useState(0);

  return (
    <div
      className="flex h-8 
      w-full 
      max-w-[calc(100vw-150px)]
      bg-white"
      //   style={{ outline: "1px solid #d1d5db" }}
    >
      {dataSteps.map((step, index) => {
        const isSelected = step.id === selectedTags?.id;
        const isDisabled = isGuestConnected() || isSelected;
        return (
          <div
            key={index}
            onClick={
              isDisabled
                ? () => {}
                : () => {
                    setCurrent(index);
                    handleChange(step, !isSelected);
                  }
            }
            className={`
            relative flex min-w-32 flex-1  items-center justify-center
            text-sm font-semibold transition-all duration-300
            ${current === index ? "text-white" : "text-gray-600"} ${
              isSelected ? "cursor-not-allowed" : "cursor-pointer"
            }
          `}
            //   style={{
            //     zIndex: steps.length - index 0,
            //   }}
          >
            {/* SVG pour la forme avec bordure sélective */}
            <svg
              className="absolute inset-0 h-full w-full transition-all duration-300"
              viewBox="0 0 100 48"
              preserveAspectRatio="none"
              style={{
                background:
                  selectedTags?.rank > index + 1 ? "#E6F4FF" : "white",
                borderTop:
                  selectedTags?.rank >= index + 1
                    ? "1px solid #d1d5db"
                    : "1px solid #d1d5db",
                borderBottom:
                  selectedTags?.rank >= index + 1
                    ? "1px solid  #d1d5db"
                    : "1px solid #d1d5db",
                borderLeft: index === 0 ? "1px solid #d1d5db" : "none",
              }}
            >
              {/* Forme de base sans stroke */}
              <path
                d={
                  index === 0
                    ? "M 0 0 L 85 0 L 100 24 L 85 48 L 0 48 Z"
                    : index === dataSteps.length - 1
                    ? "M 0 0 L 100 0 L 100 48 L 0 48 Z"
                    : "M 0 0 L 85 0 L 100 24 L 85 48 L 0 48 Z"
                }
                fill={selectedTags?.rank >= index + 1 ? "#E6F4FF" : "white"}
                className="transition-all duration-300"
              />

              {/* Bordures extérieures uniquement */}

              {/* Bordure de droite (seulement pour le dernier step) */}
              {index === dataSteps.length - 1 && (
                <line
                  x1="100"
                  y1="0"
                  x2="100"
                  y2="48"
                  stroke="#d1d5db"
                  strokeWidth="1"
                />
              )}

              {/* Bordures diagonales pour les flèches (sauf dernier step) */}
              {index !== dataSteps.length - 1 && (
                <>
                  {/* Diagonale haute droite */}
                  <line
                    x1="85"
                    y1="0"
                    x2="100"
                    y2="24"
                    stroke="#d1d5db"
                    strokeWidth="1"
                  />
                  {/* Diagonale basse droite */}
                  <line
                    x1="100"
                    y1="24"
                    x2="85"
                    y2="48"
                    stroke="#d1d5db"
                    strokeWidth="1"
                  />
                </>
              )}
            </svg>

            {/* Texte */}
            <span className="relative z-20 flex w-max items-center gap-x-2 px-1">
              <Badge color={step.color} text={step.label} />

              <span
                style={{
                  visibility:
                    loadStage && index === current ? "visible" : "hidden",
                }}
              >
                <Spin
                  className="text-gray-500"
                  indicator={<LoadingOutlined spin />}
                  size="small"
                />
              </span>
            </span>
          </div>
        );
      })}
    </div>
  );
};

export default CustomSteps;
