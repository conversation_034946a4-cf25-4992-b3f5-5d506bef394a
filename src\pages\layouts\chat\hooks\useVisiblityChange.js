import { useEffect } from "react";
export var visibilityChange = "";
var hidden = undefined;
if (typeof document.hidden !== "undefined") {
  // Opera 12.10 and Firefox 18 and later support
  hidden = "hidden";
  visibilityChange = "visibilitychange";
} else if (typeof document.msHidden !== "undefined") {
  hidden = "msHidden";
  visibilityChange = "msvisibilitychange";
} else if (typeof document.webkitHidden !== "undefined") {
  hidden = "webkitHidden";
  visibilityChange = "webkitvisibilitychange";
}
function useVisiblityChange({ callback }) {
  useEffect(() => {
    if (
      typeof document.addEventListener === "undefined" ||
      hidden === undefined
    ) {
      return;
    } else {
      document.addEventListener(visibilityChange, callback, false);
    }
    return () => {
      document.removeEventListener(visibilityChange, callback, false);
    };
  }, []);
}

export default useVisiblityChange;
