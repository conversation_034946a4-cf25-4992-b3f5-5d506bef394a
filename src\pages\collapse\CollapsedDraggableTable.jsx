import React, {
  useCallback,
  useRef,
  useState,
  useEffect,
  useMemo,
  useLayoutEffect,
} from "react";
import {
  Tag,
  Table,
  Button,
  message,
  Switch,
  Form,
  Input,
  Tooltip,
  Typography,
  Popconfirm,
  Alert,
  Col,
  Dropdown,
  Space,
  Popover,
  Select,
  Collapse,
  Skeleton,
  Spin,
  Empty,
} from "antd";
import { DndContext, closestCenter } from "@dnd-kit/core";
import Confirm from "../../components/GenericModal";
import {
  arrayMove,
  SortableContext,
  verticalListSortingStrategy,
  useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { useSelector, useDispatch } from "react-redux";
import { useLocation, useParams } from "react-router-dom";
import {
  LoadingOutlined,
  PlusOutlined,
  FileTextFilled,
  HolderOutlined,
  InfoCircleOutlined,
  MoreOutlined,
  EllipsisOutlined,
  EditOutlined,
  DeleteOutlined,
  RestOutlined,
  FolderOpenOutlined,
  FolderOutlined,
  <PERSON>sAltOutlined,
  ShrinkOutlined,
  EyeOutlined,
  RightOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import Highlighter from "react-highlight-words";
import { some, debounce, template } from "lodash";
import { FiSearch } from "react-icons/fi";
import {
  RESET_FIELD_STATE,
  SET_CONTACT_INFO_FROM_DRAWER,
} from "../../new-redux/constants";
import { updateFieldParameter } from "../../new-redux/actions/fields.actions/updateFieldParameter";
import { updateAlias } from "../../new-redux/actions/fields.actions/updateAlias";
import MainService from "../../services/main.service";
import { toastNotification } from "../../components/ToastNotification";
import { DeleteSpecificField } from "../../new-redux/actions/fields.actions/deleteSpecificField";
import { displayRightIcon } from "../../utils/displayIcon";
import { createNewGroup } from "../../new-redux/actions/fields.actions/createNewGroup";
import { updateGroup } from "../../new-redux/actions/fields.actions/updateGroup";
import { updateFieldsRank } from "../../new-redux/actions/fields.actions/updateFieldRank";
import { DeleteSpecificGroup } from "../../new-redux/actions/fields.actions/deleteSpecificGroup";
import {
  handleDrawerTitle,
  setOpenFieldDrawer,
} from "../../new-redux/actions/fields.actions/fieldDrawer";
import { useWindowSize } from "pages/clients&users/components/WindowSize";
import DropDownCrud from "components/DropDownCrud";
import SearchInTable from "pages/components/Search";
import { familyIcons } from "pages/components/DetailsProfile/ViewSphere2";
import ActionsHeaderTable from "../../components/ActionsHeaderTable";
import { updateGroupsRank } from "../../new-redux/actions/fields.actions/updateGroupsRank";
import FormCreate from "../clients&users/components/FormCreate";
// Draggable row in fields table
const TableRow = ({ children, ...props }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    setActivatorNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: props["data-row-key"],
  });

  // Custom styles when drag is active.
  const style = {
    ...props.style,
    transform: CSS.Transform.toString(
      transform && {
        ...transform,
        scaleY: 1,
      }
    ),
    transition,
    ...(isDragging
      ? {
          position: "relative",
          zIndex: 9999,
        }
      : {}),
  };
  return (
    <tr {...props} ref={setNodeRef} style={style} {...attributes}>
      {React.Children.map(children, (child) => {
        if (child.key === "sort") {
          return React.cloneElement(child, {
            children: (
              <HolderOutlined
                ref={setActivatorNodeRef}
                style={{
                  touchAction: "none",
                  cursor: isDragging ? "grabbing" : "grab",
                  padding: 0,
                }}
                {...listeners}
              />
            ),
          });
        }
        return child;
      })}
    </tr>
  );
};

// Sortable Collapse Item Component
const SortableCollapseItem = ({
  id,
  item,
  searchTerm,
  expandAll,
  ref,
  selectedGroupId,
  ...props
}) => {
  const [isOpen, setIsOpen] = useState(id == 1 ? true : false);
  let selectedKey = "";
  if (item.idGroup === selectedGroupId) selectedKey = String(item.key);
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  useEffect(() => {
    expandAll && setIsOpen(false);
  }, [expandAll]);

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    ...(isDragging
      ? {
          position: "relative",
          opacity: 0.7,
          backgroundColor: "#f0f0f0",
          zIndex: 999,
          height: "50px",
        }
      : {}),
  };

  return (
    <div className="w-full" style={style} {...attributes} ref={setNodeRef}>
      <Collapse
        ref={ref}
        activeKey={
          isDragging ? [] : searchTerm || expandAll ? [item.key] : undefined
        }
        accordion
        expandIcon={({ isActive }) => {
          setIsOpen(isActive);
          return <RightOutlined rotate={isActive ? 90 : 0} />;
        }}
        defaultActiveKey={[selectedGroupId ? selectedKey : "1"]}
        className=" w-full rounded-[4px] drop-shadow-md [&_.ant-collapse-content-box]:!p-[0px]  [&_.ant-collapse-header]:!rounded-[4px]  [&_.ant-collapse-header]:bg-white hover:[&_.ant-collapse-header]:bg-gray-100 [&_.ant-collapse-item]:!rounded-none"
        items={[{ ...item, label: item.label(listeners, isOpen || expandAll) }]}
      />
    </div>
  );
};
//Draggable Table main component.
const CollapsedDraggableTable = ({
  setUpdateFieldProps,
  groupList,
  setGroupList,
  setSelectedGroupId,
  setEditingKey,
  editingKey,
  selectedGroupId,
  setSelectedRowKey,
  selectedRowKey,
  modulesList,
  source = "",
  openDrawerFromStage,
  familyId = null,
  setIsUpdateFromVue360 = () => {},
}) => {
  const { user } = useSelector((state) => state.user);
  // Extract fields data from redux store
  const {
    fields,
    updateFieldParamSuccess,
    isLoading,
    updateFieldParamError,
    isFieldRemoved,
    updateFieldAliasSuccess,
    updateFieldAliasLoading,
    isGroupCreated,
    isGroupDeleted,
    isCreateGroupLoading,
    deletingGroupLoading,
    isGroupUpdated,
    updateGroupLoading,
    isDeletingFieldLoading,
    isUpdateFieldLoading,
    isCreateFieldLoading,
    isGroupRankUpdated,
    isRankFieldUpdated,
    updateFieldRankLoading,
    errors,
    updateGroupRankLoading,
  } = useSelector((state) => state?.fields);
  const { activeMenu360 } = useSelector((state) => state?.vue360);
  const { contactHeaderInfo: contactInfo } = useSelector(
    (state) => state?.contacts
  );
  const { contactInfoFromDrawer, openView360InDrawer } = useSelector(
    (state) => state?.vue360
  );
  const { pathname } = useLocation();
  // Local states.
  const [datas, setDatas] = useState([]);
  const [rowsCount, setRowsCount] = useState(0);
  const [searchGrpQuery, setSearchGrpQuery] = useState("");
  const [loadChangeGroupRank, setLoadChangeGroupRank] = useState(false);
  const [loadPipelines, setLoadPipelines] = useState(false);
  const [groupIdToUpdate, setGroupIdToUpdate] = useState(null);
  const [editingAliasKey, setEditingAliasKey] = useState("");
  const [familyProducts, setFamilyProducts] = useState([]);
  const [pipelines, setPipelines] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [loadingSwitchId, setLoadingSwitchId] = useState(null);
  const [expandAll, setExpandAll] = useState(false);
  // Instances declaration.
  const GroupInputRefs = useRef([]);
  const deleteBtnRef = useRef();
  const dispatch = useDispatch();
  const { id } = useParams();
  const [form] = Form.useForm();
  const [groupForm] = Form.useForm();
  const { types } = useSelector((state) => state.types);
  const { families } = useSelector((state) => state.families);
  const { search } = useSelector((state) => state.form);
  const [t] = useTranslation("common");
  const windowSize = useWindowSize();
  let arrayRanksRef = [];
  let updateRankFunctionOccurence = 0;

  const [fieldsGroupData, setGroupFields] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [openFormCreate, setOpenFormCreate] = useState(false);

  useEffect(() => {
    return () => {
      setSearchTerm("");
      setDatas([]);
      setExpandAll(false);
      setGroupFields([]);
    };
  }, [id]);
  // This is used to enable edit group label editable on the table.
  const isEditing = (record) => record.key == editingKey;

  // Add new group
  const onFinish = (values) => {
    saveNewGroup(values);
  };

  const onFinishFailed = (errors) => {};

  // Handle edit field function. (open edit drawer)
  const editFieldHandler = (record) => {
    dispatch(
      handleDrawerTitle(
        t("fields_management.update_field_drawer-title", {
          fieldLabel: record?.alias,
        })
      )
    );
    dispatch(setOpenFieldDrawer(true));
    setUpdateFieldProps(record);
  };

  // Handle delete field function.
  const handleDeleteField = (id) => {
    dispatch(DeleteSpecificField(id));
    if (activeMenu360 === "4") {
      setIsUpdateFromVue360(true);
    }
  };

  // Restrict the event.
  const handleClick = (event) => {
    event?.stopPropagation();
  };

  // Reset 'contactInfo' reducer when navigating to fields paths.
  useLayoutEffect(() => {
    if (pathname.includes("/settings/fields")) {
      dispatch({
        type: "RESET_CONTACT_HEADER_INFO",
      });
      dispatch({
        type: SET_CONTACT_INFO_FROM_DRAWER,
        payload: {},
      });
    }
  }, []);

  //Retrieve pipelines and stages.
  const getPipelines = useCallback(async () => {
    let response;

    try {
      setLoadPipelines(true);

      if (familyId) {
        response = await MainService.getPipelinesByFamily(familyId);
      } else {
        const familyId = contactInfo?.family_id
          ? contactInfo?.family_id
          : families && families.find((el) => el?.label === id)?.id;
        response = await MainService.getPipelinesByFamily(familyId);
      }
      setPipelines(
        response?.data?.data.map((pipeline) => ({
          label: pipeline?.label,
          value: pipeline?.pipeline_key,
          uid: pipeline?.id,
          options:
            pipeline?.stages &&
            pipeline?.stages.map((stage) => ({
              label: stage?.label,
              value: stage?.id,
            })),
        }))
      );
      setLoadPipelines(false);
    } catch (error) {
      setLoadPipelines(false);
      console.log(`Error ${error}`);
    }
  }, [id, familyId, contactInfo?.family_id]);

  useEffect(() => {
    getPipelines();
  }, [getPipelines]);

  //open drawer from stage
  useEffect(() => {
    if (source === "drawer" && !openDrawerFromStage) {
      setEditingKey("");
      setSelectedRowKey("");
      setSelectedGroupId(null);
    }
  }, [source, openDrawerFromStage]);

  //Update the config switches on table.
  const updateSwitches = (parameter, id) => {
    // Set the ID of the switch being interacted with
    setLoadingSwitchId(id);
    dispatch(updateFieldParameter(parameter, id)).finally(() =>
      setLoadingSwitchId(null)
    );
    // Reset the loading state after the operation
  };

  // Fields table columns.
  const columns = [
    {
      key: "sort",
      width: 15,
      className: "remove_border_right_tr mr-0",
    },
    {
      title: "Alias",
      dataIndex: "alias",
      key: "alias",
      align: "left",
      ellipsis: true,
      width: 180,
      editable: true,
      render: (record) => {
        let newString = record?.alias;

        return (
          <div className="flex justify-between">
            <div className="flex flex-row items-center">
              <Typography.Text
                title={record?.alias}
                className="truncate text-ellipsis "
                style={{
                  cursor: "pointer",
                  maxWidth:
                    !pathname?.includes("settings/fields") && contactInfo?.id
                      ? "130px"
                      : "130px",
                  marginLeft:
                    !pathname?.includes("settings/fields") && contactInfo?.id
                      ? "15px"
                      : "25px",
                }}
                editable={{
                  text: record?.alias,
                  triggerType: "text",
                  onChange: (key) => {
                    newString = key;
                    setEditingAliasKey(record?.id);
                  },
                  onEnd: () => {
                    let formData = new FormData();
                    formData.append("alias", newString?.trim());
                    if (newString?.trim() !== "") {
                      dispatch(updateAlias({ fieldId: record?.id, formData }));
                      if (activeMenu360 === "4") {
                        setIsUpdateFromVue360(true);
                      }
                    }
                  },
                  icon:
                    updateFieldAliasLoading && editingAliasKey == record?.id ? (
                      <LoadingOutlined />
                    ) : null,
                }}
              >
                {record.default === 1 && (
                  <Tooltip title={t("emailTemplates.system")}>
                    <Tag color="#2db7f5" className="self-center">
                      S
                    </Tag>
                  </Tooltip>
                )}
                <Highlighter
                  highlightStyle={{
                    backgroundColor: "#ffc069",
                    padding: 0,
                  }}
                  searchWords={[searchTerm]}
                  autoEscape
                  textToHighlight={
                    record?.alias ? record?.alias?.toString() : ""
                  }
                />
              </Typography.Text>

              {record?.description ? (
                <Tooltip
                  title={
                    <>
                      <span className="text-xs italic">Description</span>
                      <p>{record?.description}</p>
                    </>
                  }
                >
                  <FileTextFilled
                    style={{ marginLeft: "5px", color: "#ff9c6e" }}
                  />
                </Tooltip>
              ) : null}
            </div>
            <div className="opacity-0 group-hover:opacity-100">
              <DropDownCrud
                record={record}
                edit={editFieldHandler}
                handleDelete={handleDeleteField}
                form={form}
                cancel={() => {}}
                isEditing={isEditing}
                data={datas}
                setData={setDatas}
                editingKey={editingKey}
                api="fields-management"
                source="draggableTable"
                confirmLoading={false}
                fieldType={"text"}
                updateOptionField={1}
              />
            </div>
          </div>
        );
      },
    },
    {
      title: t("fields_management.display"),
      dataIndex: "alias",
      key: "alias",
      align: "center",
      width: 100,
      render: (props) => {
        if (props?.field_display?.length == 0) {
          return "_";
        }
        return (
          <>
            {props?.field_display?.slice(0, 1).map((tag, index) => (
              <Tag
                color="default"
                className="rounded-[10px] font-semibold"
                key={index}
              >
                {tag.label?.replaceAll("_", " ")}
              </Tag>
            ))}
            <Popover
              content={
                <div className="flex flex-col gap-1">
                  {props?.field_display?.slice(1).map((tag, index) => (
                    <Tag
                      className="rounded-[10px] font-semibold"
                      color="default"
                      key={index}
                    >
                      {tag.label?.replaceAll("_", " ")}
                    </Tag>
                  ))}
                </div>
              }
            >
              {props?.field_display?.length > 1 && (
                <Tag color="#1d4ed8" className=" rounded-[10px] font-semibold ">
                  + {props?.field_display?.length - 1} ...
                </Tag>
              )}
            </Popover>
          </>
        );
      },
    },
    {
      title: t("table.header.type"),
      dataIndex: "type",
      key: "type",
      align: "center",
      width: 80,
      render: (props) => {
        return (
          <Popover
            className="w-[100%] "
            key={props?.id}
            content={
              <Tag
                color="processing"
                bordered={false}
                className="mr-0"
                icon={displayRightIcon(
                  types &&
                    types.find(
                      (element) =>
                        Number(element?.id) === Number(props?.field_type_id)
                    )?.fieldType,
                  4,
                  4
                )}
              >
                {types &&
                  types.find(
                    (element) =>
                      Number(element?.id) === Number(props?.field_type_id)
                  )?.fieldType}
              </Tag>
            }
            autoAdjustOverflow
            trigger={["hover"]}
          >
            <span className=" text-2xl text-[#0a0a0a]">
              {displayRightIcon(
                types &&
                  types.find(
                    (element) =>
                      Number(element?.id) === Number(props?.field_type_id)
                  )?.fieldType,
                4,
                4
              )}
            </span>
          </Popover>
        );
      },
    },
    {
      title: t("fields_management.moduleName"),
      dataIndex: "actions",
      key: "actions",
      align: "center",
      width: 80,
      render: (props) => {
        if (modulesList && modulesList.length == 0) {
          return "_";
        }
        return (
          <p>
            {(modulesList &&
              modulesList.find(
                (element) =>
                  Number(element?.id) ===
                  Number(props?.field_module || props?.field_module_id)
              )?.label) ||
              "_"}
          </p>
        );
      },
    },
    {
      title: t("fields_management.displayedType"),
      dataIndex: "actions",
      key: "actions",
      align: "center",
      width: 80,
      render: (props) => {
        if (types && types.length == 0) {
          return "_";
        }
        return (
          <p className=" flex items-center gap-1">
            {displayRightIcon(
              types &&
                types.find(
                  (element) =>
                    Number(element?.id) === Number(props?.module_type)
                )?.fieldType,
              4,
              4
            )}
            {(types &&
              types.find(
                (element) => Number(element?.id) === Number(props?.module_type)
              )?.fieldType) ||
              "_"}
          </p>
        );
      },
    },
    {
      title: t("table.header.hidden"),
      dataIndex: "visible",
      key: "visible",
      align: "center",
      width: 80,
      render: (props) => {
        return (
          <Switch
            key={props?.id}
            size="small"
            onChange={() => updateSwitches("hidden", props?.id)}
            defaultChecked={props?.hidden}
            loading={loadingSwitchId === props?.id}
            checked={props?.hidden}
          />
        );
      },
    },
    {
      title: t("table.header.uniqueValue"),
      dataIndex: "unique",
      key: "unique",
      align: "center",
      width: 80,
      render: (props) => (
        <Switch
          key={props?.id}
          size="small"
          onChange={() => updateSwitches("uniqueValue", props?.id)}
          defaultChecked={props?.uniqueValue}
          loading={loadingSwitchId === props?.id}
          checked={props?.uniqueValue}
        />
      ),
    },
    {
      title: t("table.header.required"),
      dataIndex: "required",
      key: "required",
      align: "center",
      width: 80,
      render: (props) => (
        <Switch
          key={props?.id}
          size="small"
          onChange={() => updateSwitches("required", props?.id)}
          defaultChecked={props?.required}
          loading={loadingSwitchId === props?.id}
          checked={props?.required}
        />
      ),
    },
    {
      title: t("fields_management.readOnly"),
      dataIndex: "readOnly",
      key: "readOnly",
      align: "center",
      width: 80,
      render: (props) => (
        <Switch
          key={props?.id}
          size="small"
          onChange={() => updateSwitches("read_only", props?.id)}
          defaultChecked={props?.read_only}
          loading={loadingSwitchId === props?.id}
          checked={props?.read_only}
        />
      ),
    },
    {
      title: t("fields_management.showToGuest"),
      dataIndex: "guest",
      key: "guest",
      align: "center",
      width: 80,
      render: (props) => (
        <Switch
          key={props?.id}
          size="small"
          onChange={() => updateSwitches("guest", props?.id)}
          defaultChecked={props?.guest}
          loading={loadingSwitchId === props?.id}
          checked={props?.guest}
        />
      ),
    },
  ];

  // Handle open drawer function.
  const openDrawer = () => {
    dispatch(
      handleDrawerTitle(t("fields_management.create_field_drawer-title"))
    );
    dispatch(setOpenFieldDrawer(true));
  };

  // Handle refresh dataSource side effect.
  useEffect(() => {
    const existId = contactInfo?.family_id
      ? familyIcons(t).find((el) => el.key === contactInfo?.family_id)?.pathname
      : id;
    setGroupList(fields?.groups);

    setGroupFields(() =>
      fields?.groups?.map((group, i) => ({
        ...group,
        key: i + 1,
        groupName: group,
        product_type: existId === "Product" ? group : undefined,
        product_modules: existId === "Product" ? group : undefined,
        stage: existId !== "Product" ? group : undefined,
        fields: group.fields.map((item) => ({
          id: item?.id,
          key: item?.id,
          rank: item?.rank,
          title: item,
          alias: item,
          associateGroup: item?.field_group_id,
          visible: item,
          unique: item,
          required: item,
          readOnly: item,
          guest: item,
          type: item,
          actions: item,
        })),
      }))
    );
  }, [groupList, fields, id]);

  // Show a notification on update parameter (change switch button).
  useEffect(() => {
    if (updateFieldParamSuccess === true) {
      message.success(t("toasts.fieldParamSuccess"), 4.5);
      // Reset redux state after 3 seconds.
      let timer = setTimeout(() => {
        dispatch({ type: RESET_FIELD_STATE });
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [dispatch, updateFieldParamSuccess]);

  // Fix double click on remove icon.
  useEffect(() => {
    if (deleteBtnRef && deleteBtnRef.current) {
      deleteBtnRef.current.focus();
    }
  });

  // Show a notification on update alias.
  useEffect(() => {
    if (updateFieldAliasSuccess === true) {
      message.success(t("fields_management.updateAliasSuccess"), 4.5);
      setEditingKey("");
      // Reset redux state after 3 seconds.
      let timer = setTimeout(() => {
        dispatch({ type: RESET_FIELD_STATE });
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [dispatch, updateFieldAliasSuccess]);

  // Display a toast notification whenever a field is removed.
  useEffect(() => {
    if (isFieldRemoved === true) {
      toastNotification("success", t("toasts.fieldDeleted"), "bottomRight", 3);
      // Reset redux state after 3 seconds.
      let timer = setTimeout(() => {
        dispatch({ type: RESET_FIELD_STATE });
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [dispatch, isFieldRemoved]);

  // Display a toast notification whenever a group is removed.
  useEffect(() => {
    if (isGroupDeleted === true) {
      toastNotification("success", t("toasts.groupDeleted"), "bottomRight", 3);
      setSelectedRowKey(selectedGroupId);
      // Reset redux state after 3 seconds.
      let timer = setTimeout(() => {
        dispatch({ type: RESET_FIELD_STATE });
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [dispatch, isGroupDeleted]);

  // Display a toast notification whenever a group ranking is changed.
  useEffect(() => {
    if (isGroupRankUpdated === true) {
      message.success(t("toasts.rankChanged"), 3);
      let timer = setTimeout(() => {
        dispatch({ type: RESET_FIELD_STATE });
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [dispatch, isGroupRankUpdated]);

  // Show a notification if an error occurs on update parameter (change switch button).
  useEffect(() => {
    if (
      updateFieldParamError === true &&
      errors?.response?.data?.message ===
        "This field already contains duplicate data."
    ) {
      toastNotification(
        "error",
        t("toasts.duplicatedDataError"),
        "bottomRight",
        4.5
      );
      // Reset redux state after 3 seconds.
      let timer = setTimeout(() => {
        dispatch({ type: RESET_FIELD_STATE });
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [dispatch, updateFieldParamError]);

  // Submit form on press "ENTER" key.
  const handleKeyPress = (event) => {
    if (event.key === "Enter") {
      groupForm.submit();
    }
  };

  // Set focus on group input.
  useEffect(() => {
    GroupInputRefs.current.forEach((input) => {
      input?.focus();
    });
  }, [groupList, id, contactInfo?.family_id]);

  //Retrieve family products elements.
  const getFamilyProducts = async () => {
    try {
      const response = await MainService.getFamilyProducts();
      setFamilyProducts(response?.data?.data);
    } catch (error) {
      console.log(`Error ${error}`);
    }
  };

  useEffect(() => getFamilyProducts(), []);

  // Remove new row from the view.
  const cancelNewRow = (record) => {
    setEditingKey("");
    record.edit &&
      setGroupFields((prev) => prev.map((el) => ({ ...el, edit: false })));
    record.add && setGroupFields((prev) => prev.filter((el) => !el.add));
    setGroupIdToUpdate(null);
    groupForm.setFieldsValue({
      groupName: "",
      stage: undefined,
      product_modules: undefined,
      product_type: undefined,
    });
  };

  // Edit group
  const editGroupField = (record) => {
    if (record) {
      if (id !== "Product") {
        groupForm.setFieldsValue({
          groupName: record?.groupName?.label,
          stage:
            Number(record?.stage_id) ||
            Number(record?.stage?.stage?.id) ||
            null,
        });
      } else if (id === "Product") {
        groupForm.setFieldsValue({
          groupName: record?.groupName?.label,
          product_modules:
            record?.product_type?.relatif_module?.id ||
            record?.product_type?.relatif_module_id,
          product_type:
            record?.product_modules?.product?.id ||
            record?.product_modules?.product_id,
        });
      }
    } else {
      groupForm.setFieldsValue({
        groupName: "",
        stage: null,
      });
    }

    setGroupFields((prev) =>
      prev.map((item) => {
        item.id === record.id ? (item.edit = true) : (item.edit = false);
        return item;
      })
    );
    setGroupIdToUpdate(record?.id);
  };

  // Handle Save/Update group (dispatch redux store functions).
  const saveNewGroup = async (values) => {
    let familyId = contactInfo?.family_id
      ? contactInfo?.family_id
      : families && families.find((element) => element?.label == id)?.id;
    const existId = contactInfo?.family_id
      ? familyIcons(t).find((el) => el.key === contactInfo?.family_id)?.pathname
      : id;
    let createGroupPayload =
      existId === "Product"
        ? {
            family_id: familyId,
            label: values?.groupName,
            family_product_id: values?.product_type,
            relatif_module_id: values?.product_modules,
          }
        : {
            family_id: familyId,
            label: values?.groupName,
            stage_id: values?.stage,
          };

    let updateGroupPayload =
      existId === "Product"
        ? {
            label: values?.groupName,
            family_product_id:
              typeof values?.product_type === "undefined"
                ? ""
                : values?.product_type,
            relatif_module_id:
              typeof values?.product_modules === "undefined"
                ? ""
                : values?.product_modules,
          }
        : {
            label: values?.groupName,
            stage_id: values?.stage,
          };

    if (groupIdToUpdate === null) {
      dispatch(createNewGroup(createGroupPayload));
    } else {
      dispatch(updateGroup(groupIdToUpdate, updateGroupPayload));
    }
    setGroupIdToUpdate(null);
  };

  // Show notification on add new Group + reset inputs + deactivate disable state.
  useEffect(() => {
    if (isGroupCreated === true) {
      toastNotification(
        "success",
        t("toasts.groupCreated"),
        "bottomRight",
        "4.5"
      );
      setEditingKey("");
      form.resetFields();
      // Reset redux store after 3 seconds.
      let timer = setTimeout(() => {
        dispatch({ type: RESET_FIELD_STATE });
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [dispatch, isGroupCreated]);

  // Show notification on add new Group + reset inputs + deactivate disable state.
  useEffect(() => {
    if (isGroupUpdated === true) {
      toastNotification(
        "success",
        t("toasts.groupUpdated"),
        "bottomRight",
        "4.5"
      );
      setEditingKey("");
      form.resetFields();
      setGroupIdToUpdate(null);
      // Reset redux store after 3 seconds.
      let timer = setTimeout(() => {
        dispatch({ type: RESET_FIELD_STATE });
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [dispatch, isGroupUpdated]);

  // Show notification on successfully change a field's rank.
  useEffect(() => {
    if (isRankFieldUpdated === true) {
      message.success(t("toasts.rankChanged"), 3);
      // Reset redux store after 3 seconds.
      let timer = setTimeout(() => {
        dispatch({ type: RESET_FIELD_STATE });
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [dispatch, isRankFieldUpdated]);

  const openAddNewGroup = () => {
    groupForm.setFieldsValue({
      groupName: "",
      stage: undefined,
      product_modules: undefined,
      product_type: undefined,
    });
    setGroupFields((prev) => [...prev, { add: true, fields: [] }]);
    setTimeout(() => {
      const parents = document.querySelector(".ant-layout-content");
      const drawerTarget = document.querySelector(".ant-drawer-body");
      if (parents) {
        parents.scrollTo({ top: parents.scrollHeight, behavior: "smooth" });
      }
      if (drawerTarget) {
        drawerTarget.scrollTo({
          top: drawerTarget.scrollHeight,
          behavior: "smooth",
        });
      }
    }, 20);
  };

  // Handle drag and drop (refer to https://docs.dndkit.com/api-documentation/context-provider#event-handlers)
  const onDragEnd = ({ active, over }) => {
    if (active?.id != over?.id) {
      setGroupFields((prev) =>
        prev.map((item) => {
          if (some(item.fields, ["key", active?.id])) {
            const groupId = item?.id;
            const target = item.fields;
            if (
              target[target?.findIndex((i) => i?.id == active?.id)]
                ?.reference == 1 ||
              target[target?.findIndex((i) => i?.id == over?.id)]?.reference ==
                1
            ) {
              return item;
            } else {
              updateRank(
                arrayMove(
                  target,
                  target?.findIndex((i) => i?.key == active?.id),
                  target?.findIndex((i) => i?.key == over?.id)
                ),
                groupId
              );
              return {
                ...item,
                fields: arrayMove(
                  target,
                  target?.findIndex((i) => i?.key == active?.id),
                  target?.findIndex((i) => i?.key == over?.id)
                ),
              };
            }
          } else {
            return item;
          }
        })
      );
    }
  };

  // Update rank api call.
  const updateRank = async (array, groupId) => {
    var formData = new FormData();
    array &&
      array.forEach((item, i) => {
        if (item) {
          let key = `rank[${item?.key}]`;
          let value = i + 1;
          formData.append(key, value);
        }
      });
    let familyId =
      families && families.filter((element) => element?.label == id)[0]?.id;

    let payload = {
      familyId: pathname.includes("settings/fields")
        ? familyId
        : openView360InDrawer
        ? contactInfoFromDrawer?.family_id
        : contactInfo?.family_id
        ? contactInfo?.family_id
        : "",
      groupId: groupId,
      formData,
    };
    updateRankFunctionOccurence += 1;
    try {
      dispatch(updateFieldsRank(payload));
    } catch (error) {
      console.log(`Error ${error}`);
    }
  };

  const StageComponent = (record) => {
    let correspondingPipeline =
      pipelines &&
      pipelines?.find(
        (pipeline) =>
          Number(pipeline?.uid) === Number(record?.stage?.pipeline_id) ||
          Number(pipeline?.uid) === Number(record?.pipeline?.id)
      );

    let stage =
      pipelines &&
      pipelines.map(
        (el) =>
          el?.options.find(
            (stage) =>
              Number(stage?.value) ===
              Number(record?.stage?.stage?.id || record?.stage?.stage_id)
          )?.label
      );
    return (
      record?.system !== 1 &&
      (stage?.filter((el) => el !== undefined)[0] ? (
        <div
          className="flex cursor-pointer items-start hover:underline"
          onClick={(e) => {
            e?.stopPropagation();
            editGroupField(record);
          }}
        >
          <Tooltip title={t("fields_management.aliasTooltip")}>
            <span>{`${correspondingPipeline?.label}/${
              stage?.filter((el) => el !== undefined)[0]
            }`}</span>
          </Tooltip>
        </div>
      ) : (
        <Typography.Link
          onClick={(e) => {
            e?.stopPropagation();
            editGroupField(record);
          }}
        >
          {t("fields_management.addPipeline")}
        </Typography.Link>
      ))
    );
  };

  const ProductId = (record) => {
    const typeName =
      record?.product?.label ||
      familyProducts.find((el) => el.id == record?.product_id)?.label;
    const ModulName =
      record?.relatif_module?.label ||
      families.find((el) => el.id == record?.relatif_module_id)?.label;

    return (
      <>
        <div
          className="flex cursor-pointer items-center hover:underline"
          onClick={(e) => {
            editGroupField(record);
            handleClick(e);
          }}
        >
          <Tooltip title="Click to update">
            <span>{typeName}</span>
          </Tooltip>
        </div>
        <div className="flex items-center justify-between">
          <span>{ModulName}</span>
        </div>
      </>
    );
  };

  const filteredData = useCallback(() => {
    if (!searchTerm.length) return fieldsGroupData;
    return fieldsGroupData
      ?.map((group) => {
        if (group.add || group.edit) return group;

        const filteredFields = group?.fields?.filter(
          (field) =>
            field?.label?.toLowerCase()?.includes(searchTerm.toLowerCase()) ||
            field?.alias?.alias
              ?.toLowerCase()
              ?.includes(searchTerm.toLowerCase())
        );

        // Return the group with filtered fields if any field matches the search term
        if (
          filteredFields.length > 0 ||
          group?.label?.toLowerCase()?.includes(searchTerm.toLowerCase())
        ) {
          return {
            ...group,
            fields: filteredFields,
          };
        }

        return null; // Exclude groups that don't match the search term
      })
      .filter((group) => group !== null);
  }, [fieldsGroupData, id, searchTerm]);

  function DropdownItems(record) {
    return [
      {
        label: (
          <span
            onClick={(event) => {
              event.stopPropagation();
              setSelectedGroupId(record.id);
              editGroupField(record);
            }}
            className="cursor-pointer font-semibold"
          >
            {t("fields_management.update_Group")}
          </span>
        ),
        key: "1",
        icon: (
          <EditOutlined
            onClick={(event) => {
              event.stopPropagation();
              setSelectedGroupId(record.id);
              editGroupField(record);
            }}
          />
        ),
        disabled:
          record?.default == 1 ||
          record?.system == 1 ||
          record?.editable == 0 ||
          record?.is_used == 1 ||
          (filteredData() || [])?.some((item) => item.add || item.edit),
      },
      {
        label: (
          <span
            onClick={(event) => {
              event.stopPropagation();
              setSelectedGroupId(record.id);
              Confirm(
                ` ${t("table.delete")} "${record.label?.replace(
                  /<\/?[^>]+(>|$)/g,
                  ""
                )}" !`,
                "Confirm",
                <RestOutlined style={{ color: "red" }} />,

                () => {
                  setSelectedGroupId(record.id);
                  dispatch(DeleteSpecificGroup(record.id));
                },
                true
              );
            }}
            className="cursor-pointer font-semibold"
          >
            {" "}
            {t("fields_management.delete_group")}
          </span>
        ),
        key: "2",
        danger: true,
        icon: (
          <DeleteOutlined
            onClick={(event) => {
              event.stopPropagation();
              Confirm(
                ` ${t("table.delete")} "${record.label?.replace(
                  /<\/?[^>]+(>|$)/g,
                  ""
                )}" !`,
                "Confirm",
                <RestOutlined style={{ color: "red" }} />,

                () => {
                  dispatch(DeleteSpecificGroup(record.id));
                  setGroupFields((prev) =>
                    prev.filter((item) => item.id !== record.id)
                  );
                },
                true
              );
            }}
          />
        ),
        disabled:
          record?.default == 1 ||
          record?.system == 1 ||
          record?.editable == 0 ||
          record?.is_used == 1 ||
          (filteredData() || [])?.some((item) => item.add || item.edit),
      },
    ];
  }
  let items = filteredData()?.map((group, i) => ({
    key: i + 1,
    idGroup: group.id,
    label: (props, isOpen) =>
      (group.edit && updateGroupLoading) ||
      (group.add && isCreateGroupLoading) ? (
        <Space>
          <Skeleton.Input active={true} />
          <Skeleton.Input active={true} />
          <Skeleton.Input active={true} />
        </Space>
      ) : (
        <div className="flex items-center gap-3">
          {!group.add &&
          (user?.role === "SuperAdmin" || user?.role === "Admin") &&
          group?.system !== 1 ? (
            <Tooltip title={t("fields_management.reorder")}>
              <Button
                {...props}
                type="text"
                size="small"
                icon={<HolderOutlined />}
                style={{ cursor: "move" }}
              />
            </Tooltip>
          ) : (
            <Button
              disabled={true}
              type="text"
              size="small"
              icon={<HolderOutlined />}
              style={{ cursor: "not-allowed" }}
            />
          )}
          {!isOpen ? (
            <FolderOutlined style={{ fontSize: "16px" }} />
          ) : (
            <FolderOpenOutlined style={{ fontSize: "16px" }} />
          )}
          {group.edit || group.add ? (
            <div className={"flex w-full items-center"}>
              <Form
                form={groupForm}
                name="basic"
                className="flex w-full items-center justify-between "
                onFinish={(values) => {
                  saveNewGroup(values);
                }}
                onFinishFailed={onFinishFailed}
                autoComplete="off"
              >
                <Form.Item
                  label="group name"
                  name="groupName"
                  className="mb-0"
                  rules={[
                    {
                      required: true,
                      message: `Group Name ${t("table.header.isrequired")}`,
                    },
                  ]}
                >
                  <Input
                    onClick={(e) => e?.stopPropagation()}
                    className="w-48"
                    ref={(el) => (GroupInputRefs.current[group.rank] = el)}
                    onKeyPress={handleKeyPress}
                    placeholder={t("fields_management.groupNamePlaceholder")}
                  />
                </Form.Item>

                {id === "Product" ? (
                  <>
                    <Form.Item
                      label="Type"
                      className="mb-0"
                      name="product_type"
                    >
                      <Select
                        placeholder="Select Type"
                        showSearch
                        onClick={(e) => e?.stopPropagation()}
                        options={
                          familyProducts &&
                          familyProducts.map((product, i) => ({
                            label: product?.label,
                            value: product?.id,
                          }))
                        }
                        optionFilterProp="children"
                        filterOption={(input, option) =>
                          option?.label
                            .toLowerCase()
                            .includes(input.toLowerCase())
                        }
                        allowClear
                        popupMatchSelectWidth={false}
                      />
                    </Form.Item>
                    <Form.Item
                      label="Module"
                      className="mb-0"
                      name="product_modules"
                    >
                      <Select
                        placeholder="Select Module"
                        showSearch
                        onClick={(e) => e?.stopPropagation()}
                        options={
                          families &&
                          families.map((family) => ({
                            label: family?.label,
                            value: family?.id,
                          }))
                        }
                        optionFilterProp="children"
                        filterOption={(input, option) =>
                          option?.label
                            .toLowerCase()
                            .includes(input.toLowerCase())
                        }
                        allowClear
                        popupMatchSelectWidth={false}
                      />
                    </Form.Item>
                  </>
                ) : (
                  <Form.Item label="stage" className="mb-0" name="stage">
                    <Select
                      allowClear
                      onClick={(e) => e?.stopPropagation()}
                      options={pipelines && pipelines}
                      style={{ width: "200px" }}
                      placeholder={t("fields_management.groupStagePlaceholder")}
                    />
                  </Form.Item>
                )}

                <ActionsHeaderTable
                  record={group}
                  form={groupForm}
                  cancel={(value) => {
                    cancelNewRow(value);
                  }}
                  isEditing={() => true}
                  //updateOptionField={Object.keys(updateFieldProps)?.length}
                  edit={(value) => {
                    //  console.log(value);
                  }}
                  data={filteredData()}
                  setData={setGroupFields}
                  //editingKey={props?.default == 1 ? "1" : ""}
                  api="fields-groups"
                />
              </Form>
            </div>
          ) : (
            <div className="flex w-full items-center justify-between gap-2">
              <div className="flex items-center gap-4">
                <span className="flex w-[120px] items-center gap-1 font-semibold">
                  {group?.system === 1 && (
                    <Tooltip title={t("emailTemplates.system")}>
                      <Tag color="#2db7f5" className="self-center">
                        S
                      </Tag>
                    </Tooltip>
                  )}
                  <Highlighter
                    highlightStyle={{
                      backgroundColor: "#ffc069",
                      padding: 0,
                    }}
                    searchWords={[searchTerm]}
                    autoEscape
                    textToHighlight={group.label ? group.label.toString() : ""}
                  />
                </span>
                <div className="flex  flex-wrap items-start text-xs  ">
                  <Tooltip title={t("fields_management.requiredFields")}>
                    <Tag
                      size="small"
                      className="rounded-[10px] font-semibold"
                    >{` ${t("fields_management.required")} : ${
                      group.fields.filter(
                        (item) => item?.required?.required == 1
                      ).length
                    }	`}</Tag>
                  </Tooltip>
                  <Tooltip title={t("fields_management.uniqueFields")}>
                    <Tag
                      size="small"
                      className="rounded-[10px] font-semibold"
                    >{`${t("fields_management.uniqueValue")}  :  ${
                      group.fields.filter(
                        (item) => item?.required?.uniqueValue == 1
                      ).length
                    }	`}</Tag>
                  </Tooltip>
                </div>
              </div>
              {group?.system !== 1 &&
                (id === "Product" ? ProductId(group) : StageComponent(group))}
              <div onClick={(e) => e?.stopPropagation()}>
                <Tooltip
                  title={t("fields_management.create_field_drawer-title")}
                >
                  <Button
                    icon={<PlusOutlined />}
                    size="small"
                    type="text"
                    disabled={(filteredData() || [])?.some(
                      (item) => item.add || item.edit
                    )}
                    onClick={(event) => {
                      event.stopPropagation();
                      setSelectedGroupId(group?.id);
                      openDrawer();
                    }}
                    className="font-size-20 cursor-pointer"
                  />
                </Tooltip>
                <Dropdown
                  menu={{ items: DropdownItems(group) }}
                  trigger={["click"]}
                  onClick={(event) => {
                    event.stopPropagation();
                  }}
                  className="z-999"
                >
                  <Button
                    icon={<MoreOutlined />}
                    size="small"
                    type="text"
                    onClick={(event) => {
                      event.stopPropagation();
                    }}
                    disabled={editingKey ? true : false}
                    className="font-size-20 cursor-pointer"
                  />
                </Dropdown>
              </div>
            </div>
          )}
        </div>
      ),

    children: group.fields.length ? (
      <div className="flex flex-col items-start gap-2">
        <Space direction="vertical" style={{ width: "100%" }}>
          <Form
            form={form}
            id="alias-form"
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
          >
            <DndContext onDragEnd={onDragEnd}>
              <SortableContext
                items={
                  group.fields
                    ? group.fields?.map((element) => element?.key)
                    : [1, 2, 3]
                }
                strategy={verticalListSortingStrategy}
              >
                <Table
                  components={{
                    body: {
                      row: group.fields && group.fields?.length > 0 && TableRow,
                    },
                  }}
                  rowKey="key"
                  columns={columns}
                  dataSource={group.fields}
                  loading={
                    isDeletingFieldLoading ||
                    isUpdateFieldLoading ||
                    isCreateFieldLoading ||
                    updateFieldRankLoading
                  }
                  onRow={(_, index) => {
                    const attr = {
                      index,
                    };
                    return attr;
                  }}
                  size={"small"}
                  scroll={{
                    y: windowSize?.height - 300,
                  }}
                  pagination={false}
                  emp
                  rowClassName={() => "group"}
                />
              </SortableContext>
            </DndContext>
          </Form>
        </Space>
      </div>
    ) : (
      <div className="flex h-full w-full items-center gap-3 p-3">
        <Empty description={false} />
        <div className="flex flex-col items-start gap-2">
          <h3>{t("fields_management.emptyFields")}</h3>
          <span>{t("fields_management.emptyFieldText")}</span>
          <Button
            icon={<PlusOutlined />}
            type="primary"
            size="middle"
            disabled={(filteredData() || [])?.some(
              (item) => item.add || item.edit
            )}
            onClick={(event) => {
              event.stopPropagation();
              setSelectedGroupId(group?.id);
              openDrawer();
            }}
          >
            {t("fields_management.addField")}
          </Button>
        </div>
      </div>
    ),
  }));

  const LoadingCllapseComponent = () => {
    const loadingItems = [1, 2, 3].map((_, index) => ({
      key: index,
      label: (
        <Skeleton
          title={false}
          active
          paragraph={{ rows: 1 }}
          className="w-full"
        />
      ),
      children: <Skeleton active />,
    }));
    return loadingItems.map((item) => (
      <Collapse
        className="  w-full rounded-[4px] drop-shadow-md  [&_.ant-collapse-header]:!rounded-[4px]  [&_.ant-collapse-header]:bg-white hover:[&_.ant-collapse-header]:bg-gray-100 [&_.ant-collapse-item]:!rounded-none"
        key={item.key}
        items={[item]}
      />
    ));
  };

  const onDragGroup = ({ active, over }) => {
    if (active.id !== over.id && active.id !== 1 && over.id !== 1) {
      try {
        const oldIndex = filteredData().findIndex(
          (item) => item?.key == active.id
        );
        const newIndex = filteredData().findIndex(
          (item) => item?.key == over.id
        );
        const newArray = arrayMove(filteredData(), oldIndex, newIndex);
        setGroupFields(newArray);
        const formData = new FormData();
        newArray &&
          newArray.forEach((item, i) =>
            formData.append(`rank[${item?.id}]`, i + 1)
          );
        dispatch(updateGroupsRank(formData));
      } catch (error) {}
    }
  };

  const debouncedSearch = useCallback(
    debounce((term) => {
      setSearchTerm(term);
    }, 300),
    []
  );

  const handleSearch = (e) => {
    const term = e?.target?.value?.toString();
    debouncedSearch(term); // Call the debounced function
  };

  return (
    <div className="flex w-full flex-col gap-3 pb-5 pl-5 pr-5">
      {/* Fields Groups Table*/}
      <div className="flex w-full flex-col items-start gap-1">
        <div className="flex w-full items-center justify-between gap-2">
          <Typography.Title
            level={4}
            style={{
              marginRight: "8px",
              color: "#2253d5",
              paddingTop: "2px",
              paddingBottom: "2px",
              paddingRight: "10px",
              marginBottom: "10px",
            }}
          >
            {t("fields_management.fieldsGroupsTitle")}
          </Typography.Title>

          <Button
            icon={<PlusOutlined />}
            type="primary"
            onClick={() => {
              openAddNewGroup();
            }}
            disabled={(filteredData() || [])?.some(
              (item) => item.add || item.edit
            )}
          >
            {t("fields_management.addGroup")}
          </Button>
        </div>
        <Alert
          showIcon
          message={
            <p className="font-semibold text-gray-500">
              {t("fields_management.fieldsInfo")}
            </p>
          }
          type="info"
        />
      </div>
      {isLoading ? (
        <div className="flex w-full flex-col items-start gap-2 ">
          <div className="flex  w-full items-center justify-between"></div>
          <LoadingCllapseComponent />
        </div>
      ) : (
        <Spin
          className="h-full w-full"
          spinning={updateGroupRankLoading || deletingGroupLoading}
        >
          <div className="flex w-full flex-col items-start gap-2 ">
            <div className="flex w-full items-center justify-between">
              <Input
                prefix={<FiSearch className="text-slate-400" />}
                onChange={handleSearch}
                placeholder={t("fields_management.searchGrpPlaceholder")}
                style={{ width: "300px" }}
                allowClear
              />
              <div className="flex items-center gap-2">
                <Tooltip
                  title={
                    !expandAll
                      ? t("fields_management.expandAll")
                      : t("fields_management.collapseAll")
                  }
                >
                  <Button
                    icon={
                      !expandAll ? <ArrowsAltOutlined /> : <ShrinkOutlined />
                    }
                    onClick={() => {
                      setExpandAll((value) => !value);
                    }}
                    type={expandAll ? "primary" : "default"}
                  />
                </Tooltip>
                <Tooltip title={t("fields_management.view")}>
                  <Button
                    icon={<EyeOutlined />}
                    onClick={() => {
                      setOpenFormCreate(true);
                    }}
                  />
                </Tooltip>
              </div>
            </div>
       
            {items?.length > 0 && (
              <DndContext
                collisionDetection={closestCenter}
                onDragEnd={onDragGroup}
              >
                <SortableContext
                  items={items?.map((item) => item.key)}
                  strategy={verticalListSortingStrategy}
                >
                  {items?.map((item, index) => (
                    <SortableCollapseItem
                      searchTerm={searchTerm}
                      key={item.key}
                      id={item.key}
                      item={item}
                      expandAll={expandAll}
                      selectedGroupId={selectedGroupId}
                    />
                  ))}
                </SortableContext>
              </DndContext>
            )}
          </div>
        </Spin>
      )}
      <FormCreate
        isViewOnly={true}
        open={openFormCreate}
        setOpen={setOpenFormCreate}
        familyId={
          familyId || contactInfo?.family_id
            ? contactInfo?.family_id
            : families && families.find((el) => el?.label === id)?.id
        }
      />
    </div>
  );
};

export default CollapsedDraggableTable;
