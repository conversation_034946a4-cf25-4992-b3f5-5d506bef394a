import {
  CheckCircleOutlined,
  CheckOutlined,
  CloseCircleOutlined,
  CloseOutlined,
  InfoCircleOutlined,
  WarningOutlined,
} from "@ant-design/icons";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Checkbox,
  ConfigProvider,
  Divider,
  Drawer,
  Form,
  Input,
  InputNumber,
  Popover,
  Radio,
  Select,
  Skeleton,
  Space,
  Switch,
  Tooltip,
  Typography,
} from "antd";
import React, { useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { toastNotification } from "../../components/ToastNotification";
import {
  setInavlidConfigMail,
  setOpenConfigMail,
} from "../../new-redux/actions/menu.actions/menu";
import { useTranslation } from "react-i18next";
import { generateAxios } from "../../services/axiosInstance";
import {
  setAccountData,
  setFetchedAccount,
} from "../../new-redux/actions/mail.actions";
import { URL_ENV } from "index";
import ReactQuill, { Quill } from "react-quill";
import ImageResize from "quill-image-resize-module-react";
import "react-quill/dist/quill.snow.css";
// import "react-quill/dist/quill.bubble.css";
import quillEmoji from "react-quill-emoji";
import "react-quill-emoji/dist/quill-emoji.css";
import { SET_USER_INFOS } from "new-redux/constants";
import i18n from "translations/i18n";
import MainService from "services/main.service";
import { setPipelinesTicket } from "new-redux/actions/dashboard.actions";
const ATTRIBUTES = ["alt", "height", "width", "style"];
Quill.register("modules/imageResize", ImageResize);
Quill.register(
  {
    "formats/emoji": quillEmoji.EmojiBlot,
    "modules/emoji-toolbar": quillEmoji.ToolbarEmoji,
    "modules/emoji-textarea": quillEmoji.TextAreaEmoji,
    "modules/emoji-shortname": quillEmoji.ShortNameEmoji,
  },
  true
);
const modules = {
  toolbar: [
    [{ header: [1, 2, 3, 4, 5, 6, false] }],
    ["bold", "italic", "underline", "strike", "blockquote"],
    [{ size: [] }],
    [{ font: [] }],
    [{ align: ["right", "center", "justify"] }],
    [{ list: "ordered" }, { list: "bullet" }],
    ["link", "image"],
    [{ color: ["red", "#785412"] }],
    [{ background: ["red", "#785412"] }],
  ],
  imageResize: {
    parchment: Quill.import("parchment"),
    modules: ["Resize", "DisplaySize"],
  },
};
const msgFr = `<strong>Pour la configuration d'une boite Gmail et obtenir le Mot de passe, suivez ces étapes:</strong>
<ul>
<li>1. Connectez-vous à votre compte Gmail.</li>
<li>2. Cliquez sur l'icône de votre profil située dans le coin supérieur droit de l'écran.</li>
<li>3. Sélectionnez <strong>Gérer votre compte Google</strong>.</li>
<li>4. Dans la barre de recherche, tapez <strong>mot de passe des applications</strong>.</li>
<li>5. Cliquez sur <strong>Mot de passe des applications</strong> dans les résultats de recherche.</li>
<li>6. Entrez le nom de votre application dans le champ <strong>Nom de l'application</strong>.</li>
<li>7. Cliquez sur <strong>Créer</strong>.</li>
<li>8. <strong>Le mot de passe de l'application s'affichera</strong>. Copiez-le et collez-le dans la configuration de votre compte.</li>
</ul>
<span class="text-[15px]">&#9755;</span><span class="underline">Remarques:</span>
Les mots de passe des applications sont différents de votre mot de passe Gmail. Ne les partagez pas avec d'autres personnes.`;
const msgEn = `<strong>To set up a Gmail account and obtain the password, follow these steps:</strong>
<ul>
<li>1. Sign in to your Gmail account.</li>
<li>2. Click on your profile icon located in the top right corner of the screen.</li>
<li>3. Select <strong>Manage your Google Account</strong>.</li>
<li>4. In the search bar, type <strong>app passwords</strong>.</li>
<li>5. Click on <strong>App passwords</strong> in the search results.</li>
<li>6. Enter the name of your application in the <strong>App name</strong> field.</li>
<li>7. Click on <strong>Create</strong>.</li>
<li>8. <strong>The app password will be displayed</strong>. Copy it and paste it into your account configuration.</li>
</ul>
<span class="text-[15px]">&#9755;</span><span class="underline">Notes:</span>
App passwords are different from your Gmail password. Do not share them with anyone else.
`;

const formats = [
  "header",
  "bold",
  "italic",
  "underline",
  "strike",
  "blockquote",
  "list",
  "bullet",
  "link",
  "color",
  "image",
  "background",
  "align",
  "size",
  "font",
  ...ATTRIBUTES,
];
const TicketSwitch = ({
  mailToTicket,
  setMailToTicket,
  getPipelines,
  form,
}) => (
  <Switch
    size="small"
    style={{ marginLeft: "4px" }}
    checked={mailToTicket}
    onChange={(e) => {
      setMailToTicket(e);
      form.setFieldsValue({ is_convert_to_ticket: e ? 1 : 0 });
      if (e) {
        getPipelines();
      }
    }}
  />
);
const DrawerEmail = ({
  inbox = false,
  configEmail = {},
  setConfigEmail = () => {},
  labelId,
  setLabelId = () => {},
  setData = {},
  primary_account,
  hasSameNameAndPassword,
  setHasSameNameAndPassword = () => {},
  loadingDrawer,
}) => {
  const [form] = Form.useForm();
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isTest, setIsTest] = useState(false);
  const [autoAffectation, setAutoAffectation] = useState(false);
  const [autoUnhandledEmails, setAutoUnhandledEmails] = useState(false);
  const [passedTest, setPassedTest] = useState("initial");
  const [departments, setDepartments] = useState([]);
  const [loadDepartemnts, setLoadDepartments] = useState(false);
  const [isGetDepartemnts, setIsGetDepartments] = useState(false);
  const [disabledDepartment, setDisabledDepartment] = useState(false);
  const [subjects, setSubjects] = useState([]);
  const [selectedSubjects, setSelectedSubjects] = useState([]);
  const [values, setValues] = useState({});
  const [mailToTicket, setMailToTicket] = useState(false);
  const [loadPipeline, setLoadPipeline] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [rapport, setRapport] = useState("");
  const { invalidConfigMail } = useSelector((state) => state.menu);
  const { dataAccounts } = useSelector((state) => state.mailReducer);
  const { pipelinesTicket } = useSelector((state) => state.dashboardRealTime);
  const buttonSubmit = useRef();
  const dispatch = useDispatch();
  const { openConfigMail } = useSelector((state) => state.form);
  const { user } = useSelector((state) => state.user);

  const hanldeRefSubmit = () => {
    buttonSubmit.current.click();
  };
  const [t] = useTranslation("common");

  const submitTest = async () => {
    setLoading(true);

    try {
      await form.validateFields();
      let allData = form.getFieldsValue();
      if (allData.password === "********") {
        delete allData.password;
        delete allData.password_smtp;
      } else
        allData = hasSameNameAndPassword
          ? {
              ...form.getFieldsValue(),
              username_smtp: form.getFieldsValue()?.username,
              password_smtp: form.getFieldsValue().password,
            }
          : form.getFieldsValue();

      let response;
      if (labelId) {
        response = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        )
          .post(`tester-config/${labelId}`, {
            ...allData,
            paramEdit: 1,
            pipeline_id:
              allData.is_convert_to_ticket === 0 ? "" : allData.pipeline_id,
            is_auto_assigned:
              values.is_convert_to_ticket === 0 ? 0 : autoAffectation ? 1 : 0,
            auto_assign_existing_tickets:
              values.is_convert_to_ticket === 0
                ? 0
                : autoUnhandledEmails
                ? 1
                : 0,
            default_department_id:
              values.is_convert_to_ticket === 0
                ? ""
                : values.default_department_id,
          })
          .then((res) => {
            setPassedTest("success");
            setRapport(res?.data?.message);
            setLoading(false);
          })
          .catch((e) => {
            setPassedTest("error");
            if (e.response.status === 402) {
            }
          })
          .catch((e) => {
            setPassedTest("error");
            if (e.response.status === 402) {
              console.log(e.response.data.error);
              setRapport(e?.response?.data?.error?.replace(/\.\s*/g, ".<br>"));

              toastNotification(
                "error",
                t("toasts.customError", {
                  error: t("emailAccounts.failedVerification"),
                }),
                "topRight"
              );
            }
            // toastNotification(
            //   "error",
            //   t("toasts.customError", { error: e?.response?.data?.error }),
            //   "topRight"
            // );
          });
      } else {
        response = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        )
          .post(`tester-config`, {
            ...allData,
            pipeline_id:
              allData.is_convert_to_ticket === 0 ? "" : allData.pipeline_id,
            is_auto_assigned:
              values.is_convert_to_ticket === 0 ? 0 : autoAffectation ? 1 : 0,
            auto_assign_existing_tickets:
              values.is_convert_to_ticket === 0
                ? 0
                : autoUnhandledEmails
                ? 1
                : 0,
            default_department_id:
              values.is_convert_to_ticket === 0
                ? ""
                : values.default_department_id,
          })
          .catch((e) => {
            setPassedTest("error");
            if (e.response.status === 402) {
              console.log(e.response.data.error);
              setRapport(e?.response?.data?.error?.split("\n").join("<br>"));
              toastNotification(
                "error",
                t("toasts.customError", {
                  error: t("emailAccounts.failedVerification"),
                }),
                "topRight"
              );
            }
            // toastNotification(
            //   "error",
            //   t("toasts.customError", { error: e?.response?.data?.error }),
            //   "topRight"
            // );
          });
      }
      setLoading(false);

      if (response?.data?.success) {
        setRapport("");
        setPassedTest("success");
        toastNotification("success", t("toasts.connectionSuccess"), "topRight");
      }
    } catch (err) {
      // setPassedTest("error");
      // alert("3");

      setLoading(false);
    }
  };

  const onClose = () => {
    dispatch(setOpenConfigMail(false));
    form.resetFields();
    form.setFieldsValue({ account_type: "Imap", email: "" });
    setMailToTicket(false);
    setLabelId(null);
    setHasSameNameAndPassword(true);
    setConfigEmail({});
    setIsTest(false);
    setLoading(false);
    setConfirmLoading(false);
    setPassedTest("initial");
    setRapport("");
    setDisabledDepartment(false);
    setValues({});
    setAutoAffectation(false);
    setAutoUnhandledEmails(false);
  };

  const onFinish = async (values) => {
    setIsSubmitted(true);
    let allData = values;
    if (allData.password === "********") {
      delete allData.password;
      delete allData.password_smtp;
    } else
      allData = hasSameNameAndPassword
        ? {
            ...values,
            username_smtp: values.username,
            password_smtp: values.password,
          }
        : values;
    if (labelId) {
      try {
        setConfirmLoading(true);
        await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post(
          `/config-mails/update/${labelId}
            `,

          {
            ...allData,
            primary_account,
            default: true,
            pipeline_id:
              values.is_convert_to_ticket === 0 ? "" : values.pipeline_id,
            is_auto_assigned:
              values.is_convert_to_ticket === 0 ? 0 : autoAffectation ? 1 : 0,
            auto_assign_existing_tickets:
              values.is_convert_to_ticket === 0
                ? 0
                : autoUnhandledEmails
                ? 1
                : 0,
            default_department_id:
              values.is_convert_to_ticket === 0
                ? ""
                : values.default_department_id,
          }
        );
        const {
          data: { data: alldata },
        } = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get(`/config-mails`);

        setData(alldata);
        // setData((prev) =>
        //   data.map((el) =>
        //     invalidConfigMail.some((em) => em === el.id)
        //       ? { ...el, status: 1 }
        //       : el
        //   )
        // );

        if (invalidConfigMail.some((el) => el === values.id)) {
          dispatch(
            setInavlidConfigMail(
              invalidConfigMail.filter((el) => el !== values.id)
            )
          );
        }

        onClose();
        setConfirmLoading(false);

        toastNotification(
          "success",
          values.email + t("toasts.edit"),
          "topRight"
        );
      } catch (err) {
        console.log(err);
        setConfirmLoading(false);
        if (!inbox && err.response.status === 422)
          toastNotification(
            "error",
            `${err.response.data.errors[0]}`,
            "topRight"
          );
        else toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    } else {
      try {
        setConfirmLoading(true);
        const { data } = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post(`/sync-email`, {
          ...allData,
          primary_account: 0,
          pipeline_id:
            values.is_convert_to_ticket === 0 ? "" : values.pipeline_id,
          is_auto_assigned:
            values.is_convert_to_ticket === 0 ? 0 : autoAffectation ? 1 : 0,
          default_department_id:
            values.is_convert_to_ticket === 0
              ? ""
              : values.default_department_id,
        });
        if (!inbox) {
          const {
            data: { data: alldata },
          } = await generateAxios(
            URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
          ).get(`/config-mails`);

          setData(alldata);
        }
        // setMsgAlert(`${contact.label} has affected to new owner`)
        // setSuccessAlert(true)
        dispatch(
          setAccountData([
            ...dataAccounts,
            {
              label: data?.email,
              value: data?.id,
              creator: data?.user_id,
              primary: data?.primary_account,
              shared: data?.shared,
              sync: data?.sync,
              selected: data?.selected,
              departmentId: data?.departement_id,
              dispatcheur: data?.dispatcheur,
            },
          ])
        );

        dispatch({
          type: SET_USER_INFOS,
          payload: {
            ...user,
            accounts_email: [...user.accounts_email, data],
          },
        });

        dispatch(setFetchedAccount(false));
        onClose();
        // if (inbox) {
        //   navigate(`/mailing/${data.id}/inbox`);
        // }
        setConfirmLoading(false);

        toastNotification(
          "success",
          values.email + t("toasts.created"),
          "topRight"
        );
      } catch (err) {
        console.log(err);
        setConfirmLoading(false);
        if (!inbox && err.response.status === 422)
          toastNotification(
            "error",
            `${err.response.data.message}`,
            "topRight"
          );
        else toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    }
  };
  const onFinishFailed = (values) => {
    setIsSubmitted(true);
  };
  const getDepartments = async () => {
    setLoadDepartments(true);
    try {
      const res = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).get("/departments");
      setDepartments(
        res.data.data.map((el) => ({
          label: el.label,
          value: el.id,
          color: el.color,
        }))
      );
      setIsGetDepartments(true);
      setLoadDepartments(false);
    } catch (err) {
      setLoadDepartments(false);
      toastNotification("error", t("toasts.somethingWrong"), "topRight");

      console.log(err);
    }
  };
  const getPipelines = async () => {
    setLoadPipeline(true);

    try {
      await getDepartments();
      if (pipelinesTicket.length === 0) {
        const res = await MainService.getPipelinesByFamily(6);
        dispatch(
          setPipelinesTicket(
            res.data.data.map((el) => ({
              value: el.id,
              label: el.label,
            }))
          )
        );
      }
      const {
        data: { data },
      } = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).get("/ticket-subjects");

      const groupedByDepartment = data
        .filter((el) => el.id_department)
        .reduce((acc, subject) => {
          const option = {
            label: subject.label,
            value: subject.id,
          };
          const deptId = subject.department.id;
          const existingDept = acc.find((item) => item.value === deptId);

          if (existingDept) {
            if (!existingDept.options.some((opt) => opt.value === subject.id)) {
              existingDept.options.push(option);
            }
          } else {
            acc.push({
              label: subject.department.label,
              value: deptId,
              title: "manager",
              options: [option],
            });
          }

          return acc;
        }, []);

      setSubjects(groupedByDepartment);

      // setSubjects(data.filter(el=>el.id_department))
      setLoadPipeline(false);
    } catch (err) {
      setLoadPipeline(false);
      console.log(err);
    }
  };
  const optionsAccountType = [
    { label: "Imap", value: "Imap" },
    // { label: "Gmail", value: "Gmail", disabled: true },
    // { label: "Outlook", value: "Outlook", disabled: true },
  ];
  const optionsEncryption = [
    { label: "SSL", value: "ssl" },
    { label: "TLS", value: "tls" },
    { label: "STARTTLS", value: "starttls" },
  ];
  const onChangeCheckbox = (e) => {
    if (e.target.checked) {
      form.setFieldsValue({ departement_ids: "ALL" });
      setDisabledDepartment(true);
    } else {
      form.setFieldsValue({
        departement_ids:
          configEmail.departement_ids && configEmail.departement_ids.length > 0
            ? configEmail.departement_ids
            : [],
      });
      setDisabledDepartment(false);
    }
  };
  useEffect(() => {
    if (Object.keys(configEmail).length !== 0) {
      setValues(configEmail);
      form?.setFieldsValue({
        ...configEmail,
        // create_contact: configEmail.create_contact == 1 ? true : false,
        departement_ids: configEmail.departement_ids || [],
      });
      setAutoAffectation(configEmail?.is_auto_assigned === 1 ? true : false);
      setAutoUnhandledEmails(
        configEmail?.auto_assign_existing_tickets === 1 ? true : false
      );
      setSelectedSubjects(configEmail?.subject_ids || []);
      if (configEmail?.is_convert_to_ticket === 1) {
        getPipelines();
        setMailToTicket(true);
      }
      if (configEmail?.type == "1") getDepartments();
      else if (configEmail?.departement_ids === "ALL")
        setDisabledDepartment(true);
    }
  }, [form, configEmail]);
  const handleChangeValues = async (changedValues, allValues) => {
    setPassedTest("initial");
    setRapport("");

    if (changedValues.type == "0") {
      setValues(allValues);
      // form.setFieldsValue({ departement_ids: [] });
      // setDisabledDepartment(false);
    }
    if (changedValues.type == "1") {
      // form.setFieldsValue({
      //   departement_ids:
      //     configEmail.departement_ids && configEmail.departement_ids.length > 0
      //       ? configEmail.departement_ids
      //       : [],
      // });
      setValues(allValues);
      if (!isGetDepartemnts) {
        getDepartments();
      }
    }
  };
  function handleInputNumberKeyDown(e) {
    if (e.key === "-" && !e.target.value) {
      e.preventDefault();
    }
    if (e.key === "e") {
      e.preventDefault();
    }
    if (
      isNaN(parseInt(e.key)) &&
      ![
        "Tab",
        "Backspace",
        "Delete",
        "ArrowLeft",
        "ArrowRight",
        "Enter",
        ".",
      ].includes(e.key)
    ) {
      e.preventDefault();
    }
  }
  return (
    <Drawer
      title={
        labelId
          ? t("emailAccounts.updateEmailAccount")
          : t("emailAccounts.createEmailAccount")
      }
      placement="right"
      onClose={onClose}
      open={openConfigMail}
      size="large"
      footer={
        <div className="flex w-full items-center justify-between gap-4">
          {passedTest === "success" ? (
            <div>
              <CheckCircleOutlined style={{ color: "#16a34a" }} />{" "}
              <span className="text-xs font-semibold text-green-600">
                {t("emailAccounts.successVerification")}
              </span>
            </div>
          ) : passedTest === "error" ? (
            <div>
              <CloseCircleOutlined style={{ color: "#dc2626" }} />{" "}
              <span className="text-xs font-semibold text-red-600">
                {t("emailAccounts.failedVerification")}
              </span>{" "}
              {/* <Popover content={rapport} trigger="click">
                <Typography.Link style={{ textDecorationLine: "underline" }}>
                  Rapport
                </Typography.Link>
              </Popover> */}
            </div>
          ) : (
            <div className="flex items-center gap-x-1">
              <InfoCircleOutlined style={{ color: "#FF4D4F" }} />{" "}
              <span className="text-[11px] font-medium">
                {t("emailAccounts.infoTest")}
              </span>{" "}
            </div>
          )}

          <Space align="end">
            <ConfigProvider
              theme={{
                token: {
                  colorPrimary: "#3CC3A5",
                },
              }}
            >
              {passedTest === "success" ? null : (
                <Button
                  type="primary"
                  onClick={async () => {
                    setIsTest(true);
                    await submitTest();
                  }}
                  loading={loading}
                >
                  {" "}
                  {t("emailAccounts.check")}
                </Button>
              )}
            </ConfigProvider>{" "}
            {/* <Button onClick={onClose}> {t("emailAccounts.cancel")}</Button>{" "} */}
            <Button
              type="primary"
              onClick={() => {
                setIsTest(false);
                hanldeRefSubmit();
              }}
              loading={confirmLoading}
              disabled={passedTest !== "success"}
            >
              {labelId ? t("form.save") : t("form.create")}
            </Button>
            {/* {!labelId ? (
              <Tooltip title={t("emailAccounts.infoTest")}>
                <InfoCircleOutlined className="text-md	hover:cursor-help" />
              </Tooltip>
            ) : null} */}
          </Space>
        </div>
      }
    >
      {/* <div className="pb-2">
        <Alert
          message={
            <div>
              {i18n.language === "en"
                ? msgEn
                    ?.split("\n")
                    ?.map((el, i) =>
                      isShowMore ? (
                        <p dangerouslySetInnerHTML={{ __html: el }}></p>
                      ) : i < 3 ? (
                        <p dangerouslySetInnerHTML={{ __html: el }}></p>
                      ) : (
                        ""
                      )
                    )
                : msgFr
                    ?.split("\n")
                    ?.map((el, i) =>
                      isShowMore ? (
                        <p dangerouslySetInnerHTML={{ __html: el }}></p>
                      ) : i < 3 ? (
                        <p dangerouslySetInnerHTML={{ __html: el }}></p>
                      ) : (
                        ""
                      )
                    )}
              <Button type="link" onClick={() => setIsShowMore(!isShowMore)}>
                {isShowMore
                  ? t("tasks.elementShowLessBtn") + "."
                  : t("tasks.showMoreBtn") + "..."}
              </Button>
            </div>
          }
          type="info"
          // showIcon
        />
      </div> */}
      <Form
        form={form}
        // ref={formRef}
        name="basic"
        labelCol={{
          span: 24,
        }}
        wrapperCol={{
          span: 24,
        }}
        onFinish={onFinish}
        onFinishFailed={onFinishFailed}
        autoComplete="off"
        scrollToFirstError
        layout="vertical"
        initialValues={{
          account_type: configEmail?.account_type,
          email: configEmail?.email,

          type: "0",
        }}
        onValuesChange={handleChangeValues}
      >
        {loadingDrawer ? (
          <Skeleton />
        ) : (
          <>
            <Form.Item
              name="account_type"
              label={t("emailAccounts.accountType")}
              rules={[
                {
                  required: true,
                  message: `${t("emailAccounts.accountType")} ${t(
                    "table.header.isrequired"
                  )}`,
                },
              ]}
              initialValue={"Imap"}
            >
              <Select
                disabled
                options={optionsAccountType}
                placeholder={t("emailAccounts.selectAccountType")}
              />
            </Form.Item>
            <Form.Item
              name="sync_email"
              label={t("emailAccounts.syncEmailsFrom")}
              initialValue="0"
            >
              <Radio.Group>
                {/* <Radio value="Now">{t("emailAccounts.now")}</Radio> */}
                <Radio value="0">{t("emailAccounts.now")}</Radio>
                <Radio value="1">{t("emailAccounts.1weekago")}</Radio>
                {/* <Radio value="6 Months ago">{t("emailAccounts.6monthago")}</Radio> */}
              </Radio.Group>
            </Form.Item>
            <Form.Item
              name="type"
              label={t("emailAccounts.account")}
              rules={[
                {
                  required: true,
                  message: `${t("emailAccounts.account")} ${t(
                    "table.header.isrequired"
                  )}`,
                },
              ]}
            >
              <Radio.Group>
                <Radio
                  value="1"
                  disabled={
                    user.role === "Admin" || user.role === "SuperAdmin"
                      ? false
                      : true
                  }
                >
                  {t("emailAccounts.shared")}
                </Radio>
                <Radio value="0">{t("emailAccounts.personal")}</Radio>
              </Radio.Group>
            </Form.Item>

            {values?.type == "1" ? (
              <>
                <Form.Item
                  name="processing_time"
                  label={t("mailing.processingTime")}
                  rules={[
                    {
                      required: values?.type == "1" ? true : false,
                      message: ` ${
                        t("mailing.processingTime") +
                        " " +
                        t("table.header.isrequired")
                      }`,
                    },
                  ]}
                  // initialValue={configEmail?.processing_time}
                >
                  <InputNumber
                    style={{ width: "100%" }}
                    onKeyDown={handleInputNumberKeyDown}
                    addonAfter={t("helpDesk.hours")}
                  />
                </Form.Item>
                <div className="flex w-full items-center gap-2">
                  <Form.Item
                    style={{ width: "100%" }}
                    name="departement_ids"
                    label={t("table.header.departments")}
                    rules={[
                      {
                        required: true,
                        message: `departments ${t("table.header.isrequired")}`,
                      },
                    ]}
                    // validateTrigger="onSubmit"

                    //initialValue={configEmail ? configEmail.email : ""}
                  >
                    {loadDepartemnts ? (
                      <div className="skeletonSelectDepartments">
                        <Skeleton.Input
                          active={loadDepartemnts}
                          size="default"
                          style={{ width: "100%", display: "inline-flex" }}
                        />
                      </div>
                    ) : (
                      <Select
                        placeholder={t("services.selectdepartment")}
                        options={departments}
                        disabled={disabledDepartment}
                        mode="multiple"
                        maxTagCount="responsive"
                        showSearch
                        filterOption={(input, option) =>
                          (option?.label?.toLowerCase() ?? "").includes(
                            input.toLowerCase()
                          )
                        }
                        style={{ width: "100%" }}
                      />
                    )}
                  </Form.Item>
                  <div>
                    <div className="invisible">all</div>
                    <Checkbox
                      onChange={onChangeCheckbox}
                      defaultChecked={values?.departement_ids === "ALL"}
                      // style={{ marginTop: "6px" }}
                    >
                      {t("helpDesk.all")}
                    </Checkbox>{" "}
                  </div>
                </div>
              </>
            ) : null}

            <Form.Item
              name="email"
              label="Email"
              rules={[
                {
                  required: true,
                  message: `Email ${t("table.header.isrequired")}`,
                },
                {
                  type: "email",
                  message: t("emailAccounts.errEmail"),
                },
              ]}
              // validateTrigger="onSubmit"

              //initialValue={configEmail ? configEmail.email : ""}
            >
              <Input disabled={labelId} />
            </Form.Item>
            <Form.Item name="create_contact">
              <Card
                size="small"
                styles={{
                  body: { padding: 0 },
                }}
                title={
                  <div className="flex items-center gap-x-2">
                    <Typography.Title level={5}>
                      {t("emailAccounts.createContactRecord")}
                    </Typography.Title>
                    <Switch
                      size="small"
                      style={{ marginLeft: "4px" }}
                      defaultChecked={
                        form.getFieldValue("create_contact") == 1 ? true : false
                      }
                      onChange={(e) =>
                        form.setFieldsValue({ create_contact: e ? 1 : 0 })
                      }
                    />
                  </div>
                }
                // variant="borderless"
                // style={{ width: 300 }}
              />
            </Form.Item>

            <Form.Item
              name="is_convert_to_ticket"
              initialValue={form.getFieldsValue().is_convert_to_ticket || 0}
            >
              <Card
                size="small"
                styles={{
                  body: {
                    backgroundColor: !mailToTicket && "white",
                    padding: !mailToTicket ? 0 : 10,
                  },
                }}
                title={
                  <div className="flex items-center gap-x-2">
                    <Typography.Title level={5}>
                      {t("emailAccounts.convertMailToTicket")}
                    </Typography.Title>
                    <TicketSwitch
                      mailToTicket={mailToTicket}
                      setMailToTicket={setMailToTicket}
                      getPipelines={getPipelines}
                      form={form}
                    />{" "}
                  </div>
                }
                // variant="borderless"
                // style={{ width: 300 }}
              >
                {mailToTicket ? (
                  loadPipeline ? (
                    <div className="flex flex-col gap-y-3">
                      <div className="skeletonSelectDepartments">
                        <Skeleton.Input
                          active={loadDepartemnts}
                          size="default"
                          style={{ width: "100%", display: "inline-flex" }}
                        />
                      </div>
                      <div className="skeletonSelectDepartments">
                        <Skeleton.Input
                          active={loadDepartemnts}
                          size="default"
                          style={{ width: "100%", display: "inline-flex" }}
                        />
                      </div>
                      <div className="skeletonSelectDepartments">
                        <Skeleton.Input
                          active={loadDepartemnts}
                          size="default"
                          style={{ width: "100%", display: "inline-flex" }}
                        />
                      </div>
                    </div>
                  ) : (
                    <>
                      <Form.Item
                        name="pipeline_id"
                        label={t("pipeline.pipelineName")}
                        rules={[
                          {
                            required: true,
                            message: `${t("pipeline.pipelineName")} ${t(
                              "table.header.isrequired"
                            )}`,
                          },
                        ]}
                        initialValue={form.getFieldsValue().pipeline_id}
                      >
                        <Select
                          options={pipelinesTicket}
                          placeholder={t("emailTemplates.plsSelect")}
                          showSearch
                          allowClear
                          optionFilterProp="label"
                          filterSort={(optionA, optionB) => {
                            var _a, _b;
                            return (
                              (_a =
                                optionA === null || optionA === void 0
                                  ? void 0
                                  : optionA.label) !== null && _a !== void 0
                                ? _a
                                : ""
                            )
                              .toLowerCase()
                              .localeCompare(
                                ((_b =
                                  optionB === null || optionB === void 0
                                    ? void 0
                                    : optionB.label) !== null && _b !== void 0
                                  ? _b
                                  : ""
                                ).toLowerCase()
                              );
                          }}
                        />
                      </Form.Item>
                      <Form.Item
                        name="default_department_id"
                        label={
                          <div className="">
                            {t("emailAccounts.defaultDepartment")} &nbsp;{" "}
                            <Tooltip
                              title={t("emailAccounts.defaultDepartmentInfo")}
                            >
                              <InfoCircleOutlined />
                            </Tooltip>
                          </div>
                        }
                        rules={[
                          {
                            required: true,
                            message: "Please select a department",
                          },
                        ]}

                        // initialValue={form.getFieldsValue().pipeline_id}
                      >
                        <Select
                          placeholder={t("services.selectdepartment")}
                          options={departments}
                          disabled={disabledDepartment}
                          maxTagCount="responsive"
                          showSearch
                          filterOption={(input, option) =>
                            (option?.label?.toLowerCase() ?? "").includes(
                              input.toLowerCase()
                            )
                          }
                          style={{ width: "100%" }}
                        />
                      </Form.Item>
                      {subjects.length > 0 ? (
                        <Form.Item
                          name="subject_ids"
                          label={t("helpDesk.subject")}

                          // initialValue={form.getFieldsValue().pipeline_id}
                        >
                          <Select
                            mode="multiple"
                            showSearch
                            placeholder={t("emailTemplates.plsSelect")}
                            allowClear
                            onChange={(value) => {
                              setSelectedSubjects(value);
                              value.length < 1 && setAutoAffectation(false);
                              setAutoUnhandledEmails(false);
                            }}
                            optionFilterProp="children"
                            filterOption={(input, option) => {
                              // Handle both OptGroup children and direct options
                              const text = (
                                option.label ||
                                option.children?.props?.children ||
                                option.children ||
                                ""
                              )
                                .toString()
                                .toLowerCase();
                              return text.includes(input.toLowerCase());
                            }}
                            filterSort={(optionA, optionB) => {
                              const getLabel = (option) => {
                                if (option.label) return option.label;
                                if (option.children?.props?.children)
                                  return option.children.props.children;
                                if (option.children) return option.children;
                                return "";
                              };

                              const labelA = getLabel(optionA)
                                .toString()
                                .toLowerCase();
                              const labelB = getLabel(optionB)
                                .toString()
                                .toLowerCase();

                              return labelA.localeCompare(labelB);
                            }}
                          >
                            {subjects.map((group) => (
                              <Select.OptGroup
                                key={`group-${group.value}`}
                                label={group.label}
                              >
                                {group.options.map((option) => (
                                  <Select.Option
                                    key={`opt-${option.value}`}
                                    value={option.value}
                                    label={option.label} // Important for filtering
                                  >
                                    {option.label}
                                  </Select.Option>
                                ))}
                              </Select.OptGroup>
                            ))}
                          </Select>
                        </Form.Item>
                      ) : null}

                      {selectedSubjects.length > 0 ? (
                        <div className="mt-4">
                          <div className="mb-2 mt-4 flex items-center gap-x-2">
                            <span className="pb-1 font-medium text-slate-500">
                              {t("emailAccounts.autoAffectAgent")}
                            </span>{" "}
                            <Switch
                              size="small"
                              value={autoAffectation}
                              onChange={(value) => setAutoAffectation(value)}
                            />
                          </div>
                          <div className="mb-2 mt-4 flex items-center gap-x-2">
                            <span className="pb-1 font-medium text-slate-500">
                              {t("emailAccounts.automaticUnhandledEmails")}
                            </span>{" "}
                            <Switch
                              size="small"
                              value={autoUnhandledEmails}
                              onChange={(value) =>
                                setAutoUnhandledEmails(value)
                              }
                            />
                          </div>
                        </div>
                      ) : null}
                    </>
                  )
                ) : null}
              </Card>
            </Form.Item>

            {/* <Form.Item
          name="password"
          label={t("emailAccounts.password")}
          rules={[
            {
              required: true,
              message: `${t("emailAccounts.password")} ${t(
                "table.header.isrequired"
              )}`,
            },
          ]}
        >
          <Input.Password autoComplete="off" />
        </Form.Item>
        <Form.Item name="username" label={t("emailAccounts.userName")}>
          <Input autoComplete="off" />
        </Form.Item> */}
            <Divider orientation="left">
              {" "}
              <Typography.Title level={5}>
                {t("emailAccounts.incomingMail")}
              </Typography.Title>
            </Divider>
            <Form.Item
              name="server_imap"
              label={t("emailAccounts.server")}
              rules={[
                {
                  required: true,
                  message: `${t("emailAccounts.server")} ${t(
                    "table.header.isrequired"
                  )}`,
                },
              ]}
            >
              <Input placeholder="imap.example.tn" />
            </Form.Item>
            <Form.Item
              label="port"
              name="port_imap"
              rules={[
                {
                  required: true,
                  message: `Port IMAP ${t("table.header.isrequired")}`,
                },
              ]}
              style={{
                display: "inline-block",
                width: "calc(50% - 8px)",
              }}
              initialValue={"993"}
            >
              <InputNumber style={{ width: "100%" }} />
            </Form.Item>
            <Form.Item
              label="Encryption"
              name="encryption_imap"
              style={{
                display: "inline-block",
                width: "calc(50% - 8px)",
                margin: "0 0 0 16px ",
              }}
              initialValue={optionsEncryption[0].label}
            >
              <Select options={optionsEncryption} allowClear />
            </Form.Item>
            <Form.Item
              name="username"
              rules={[
                {
                  required: true,
                },
              ]}
              label={t("emailAccounts.userName")}
              style={{
                display: "inline-block",
                width: "calc(50% - 8px)",
              }}
            >
              <Input disabled={labelId} autoComplete="off" />
            </Form.Item>
            <Form.Item
              name="password"
              label={
                <span>
                  {" "}
                  <span>{t("emailAccounts.password")}</span>{" "}
                  <Popover
                    content={
                      i18n.language === "en"
                        ? msgEn
                            ?.split("\n")
                            ?.map((el, i) => (
                              <p dangerouslySetInnerHTML={{ __html: el }}></p>
                            ))
                        : msgFr
                            ?.split("\n")
                            ?.map((el, i) => (
                              <p dangerouslySetInnerHTML={{ __html: el }}></p>
                            ))
                    }
                  >
                    <InfoCircleOutlined />
                  </Popover>
                </span>
              }
              rules={[
                {
                  required: !labelId ? true : false,
                  message: `${t("emailAccounts.password")} ${t(
                    "table.header.isrequired"
                  )}`,
                },
              ]}
              style={{
                display: "inline-block",
                width: "calc(50% - 8px)",
                margin: "0 0 0 16px ",
              }}
              // initialValue={labelId ? "********" : ""}
            >
              <Input.Password
                autoComplete="off"
                placeholder={labelId ? "********" : ""}
              />
            </Form.Item>
            <Divider orientation="left">
              <Typography.Title level={5}>
                {t("emailAccounts.outgoingMail")}{" "}
              </Typography.Title>
            </Divider>

            <Form.Item
              name="server_smtp"
              label={t("emailAccounts.server")}
              rules={[
                {
                  required: true,

                  message: `${t("emailAccounts.server")} ${t(
                    "table.header.isrequired"
                  )}`,
                },
              ]}
            >
              <Input placeholder="smtp.example.tn" />
            </Form.Item>
            <Form.Item
              label="port"
              name="port_smtp"
              rules={[
                {
                  required: true,
                  message: `Port SMTP ${t("table.header.isrequired")}`,
                },
              ]}
              style={{
                display: "inline-block",
                width: "calc(50% - 8px)",
              }}
              initialValue={"465"}
            >
              <InputNumber style={{ width: "100%" }} />
            </Form.Item>
            <Form.Item
              label="Encryption"
              name="encryption_smtp"
              style={{
                display: "inline-block",
                width: "calc(50% - 8px)",
                margin: "0 0 0 16px ",
              }}
              initialValue={optionsEncryption[0].label}
            >
              <Select options={optionsEncryption} allowClear />
            </Form.Item>

            <Form.Item>
              <Card
                size="small"
                styles={{
                  body: { padding: 0 },
                }}
                title={
                  <div className="flex items-center gap-x-2">
                    <span>{t("emailAccounts.useSameNamePassword")}</span>{" "}
                    <Switch
                      size="small"
                      checked={hasSameNameAndPassword}
                      onChange={(e) => setHasSameNameAndPassword(e)}
                    />
                  </div>
                }
              />
            </Form.Item>
            {!hasSameNameAndPassword ? (
              <>
                <Form.Item
                  name="username_smtp"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                  label={t("emailAccounts.userName")}
                  style={{
                    display: "inline-block",
                    width: "calc(50% - 8px)",
                  }}
                >
                  <Input autoComplete="off" />
                </Form.Item>
                <Form.Item
                  name="password_smtp"
                  label={t("emailAccounts.password")}
                  rules={[
                    {
                      required: !labelId ? true : false,
                      message: `${t("emailAccounts.password")} ${t(
                        "table.header.isrequired"
                      )}`,
                    },
                  ]}
                  style={{
                    display: "inline-block",
                    width: "calc(50% - 8px)",
                    margin: "0 0 0 16px ",
                  }}
                >
                  <Input.Password autoComplete="off" />
                </Form.Item>
              </>
            ) : (
              ""
            )}
            {values?.type == "1" ? (
              <>
                <Divider orientation="left">
                  {" "}
                  <Typography.Title level={5}>Signature</Typography.Title>
                </Divider>
                {/* <Form.Item
                  name="label"
                  label="Label"
                  // name="server_imap"
                  // label={t("emailAccounts.server")}
                  rules={[
                    {
                      required: true,
                      message: `Label ${t("table.header.isrequired")}`,
                    },
                  ]}
                >
                  <Input placeholder="label" />
                </Form.Item> */}

                <Form.Item
                  label={t("emailAccounts.defaultSignature")}
                  name="value"
                  rules={[
                    {
                      required: true,
                      message: `${t("emailAccounts.defaultSignature")} ${t(
                        "table.header.isrequired"
                      )}`,
                    },
                  ]}
                  initialValue={"<p><br/><p>"}

                  // initialValue="<p><br></p>"
                >
                  <ReactQuill
                    theme="snow"
                    modules={modules}
                    formats={formats}
                    // value={contentFr}

                    // onKeyPress={(e) => {
                    //   // e.key.trim() !== "" &&
                    //   setDisabled(false);
                    // }}
                  />
                  {/* <BodyMailTemplate form={form} selectedPage={selectedNode} /> */}
                </Form.Item>
              </>
            ) : null}
            {/* <span className="px-1"> {t("emailAccounts.allownon-secure")}</span>
        <Switch size="small" /> */}

            <Button
              className="hidden"
              ref={buttonSubmit}
              type="primary"
              htmlType="submit"
              // disabled={!passedTest}
            >
              Submit
            </Button>
          </>
        )}
      </Form>
    </Drawer>
  );
};

export default DrawerEmail;
