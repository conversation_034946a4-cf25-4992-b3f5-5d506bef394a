import { useCallback, useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  searchMsgInListConversations,
  stopSearchMsg,
} from "new-redux/actions/chat.actions";
import { paginatationSearchMsg } from "new-redux/services/chat.services";
import { store } from "new-redux/store";

function useCustomScroll({
  isFetchingNextPage = false,
  inViewLastElement = false,
  inViewFirstElement = false,
  inViewFirstElementInList = false,
  hasNextPage = false,
  inViewFooter = false,
  fetchNextPage = () => {},
  handleReadMsg = () => {},
  source = "",
}) {
  const { searchMsgState, searchMsg, scrollToBottomState } = useSelector(
    (state) => state.chat
  );
  const selectedConversation = useSelector(
    (state) => state.ChatRealTime.selectedConversation
  );
  const timeoutRef = useRef({
    timeout1: null,
    timeout2: null,
    timeout3: null,
  });

  const [pagePrevious, setPagePrevious] = useState(0);
  const [pageNext, setPageNext] = useState(0);

  const dispatch = useDispatch();

  const stopSearchUI = useCallback(() => {
    if (!searchMsgState.id) return;
    // Clear any previous timeouts
    Object.values(timeoutRef.current).forEach(clearTimeout);
    timeoutRef.current.timeout1 = null;
    timeoutRef.current.timeout2 = null;
    timeoutRef.current.timeout3 = null;

    const list = document.getElementById("parentContainerID");

    if (!list) return;
    list.setAttribute("style", "height: 100%; overflow-y: hidden;");
    list.removeAttribute("id");
  }, [searchMsgState?.id]);

  const loadMore = useCallback(async () => {
    const searchMsgState = await store.getState().chat.searchMsgState;
    const canScroll =
      (hasNextPage && !searchMsgState.id && !searchMsgState.loading) ||
      (searchMsgState.id &&
        !searchMsgState.loading &&
        !searchMsgState.virtual &&
        searchMsgState.hasMorePrevious);

    if (isFetchingNextPage || searchMsgState.miniLoadingPrevious) return;

    if (canScroll) {
      if (searchMsgState.id && !searchMsgState.loading) {
        dispatch(
          paginatationSearchMsg({
            last_id: searchMsgState.last_id,
            type: "previous",
            receiver_id: selectedConversation?.id,
            page: pagePrevious + 1,
            type_discussion: selectedConversation?.type,
          })
        );
        setPagePrevious((c) => c + 1);
      } else {
        fetchNextPage();
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    isFetchingNextPage,

    hasNextPage,
    dispatch,
    selectedConversation?.id,
    selectedConversation?.type,
    pagePrevious,
  ]);
  const scrollToBottomFunction = useCallback(() => {
    try {
      const list = document.getElementsByClassName("messagesList");
      if (list.length > 0) {
        Array.from(list).forEach((element) => {
          element.scrollTo(0, list.scrollHeight);
        });
      }

      stopSearchUI();
    } catch (error) {
      return null;
    }
  }, [stopSearchUI]);
  useEffect(() => {
    if (inViewLastElement) loadMore();
    if (inViewFooter) stopSearchUI();
  }, [inViewLastElement, inViewFooter, loadMore, stopSearchUI]);

  const scrollToElement = useCallback(
    async (type) => {
      try {
        // Interval check for the element
        const interval = setInterval(() => {
          const idToScroll =
            type === "next"
              ? searchMsg.slice(-15)[0]?._id
              : type === "previous"
              ? searchMsg.slice(0, 15)[10]?._id
              : searchMsgState?.virtual;

          const element = document?.querySelector(`[data-id="${idToScroll}"]`);

          if (element) {
            clearInterval(interval); // Stop checking once element is found
            let parentContainer = element.parentElement;

            parentContainer.style.overflowY = "auto";
            parentContainer.style.height =
              parseInt(
                document.getElementById("containerChatRef").clientHeight - 8,
                10
              ) + "px";

            parentContainer.setAttribute("id", "parentContainerID");

            element.scrollIntoView({
              behavior: "auto",
              block: "center",
              inline: "center",
            });
          }

          // Clear search state after 15 seconds
          timeoutRef.current.timeout2 = setTimeout(() => {
            dispatch(
              searchMsgInListConversations({
                virtual: null,
              })
            );
          }, 5000);
          // }
        }, 100);

        // Fallback if element never loads
        timeoutRef.current.timeout3 = setTimeout(() => {
          clearInterval(interval); // Stop checking
        }, 15000);
      } catch (error) {
        return null;
      }
    },
    [dispatch, searchMsg, searchMsgState?.virtual]
  );
  //SCROLL TO pagination

  useEffect(() => {
    if (
      isFetchingNextPage ||
      searchMsgState.miniLoadingPrevious ||
      searchMsgState.miniLoadingNext
    ) {
      window.dispatchEvent(new Event("resize"));
      // if (searchMsgState.id && !searchMsgState.loading) {
      //   stopSearchUI();
      //   dispatch(
      //     searchMsgInListConversations({
      //       virtual: null,
      //     })
      //   );
      // }

      return;
    }
    if (searchMsgState.id && !searchMsgState.loading && searchMsg.length > 46) {
      if (
        !searchMsgState.miniLoadingNext &&
        searchMsgState.orientation === "next"
      ) {
        scrollToElement("next");
      } else if (
        !searchMsgState.miniLoadingPrevious &&
        searchMsgState.orientation === "previous"
      ) {
        scrollToElement("previous");
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    searchMsg,
    isFetchingNextPage,
    searchMsgState.id,
    searchMsgState.orientation,
    searchMsgState.miniLoadingNext,
    searchMsgState.miniLoadingPrevious,
  ]);
  //SCROLL TO element after search

  useEffect(() => {
    if (searchMsgState.loading) {
      stopSearchUI();
      setPageNext(0);
      setPagePrevious(0);
    } else if (
      !searchMsgState.loading &&
      searchMsgState.virtual &&
      searchMsgState?.id
    ) {
      scrollToElement();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dispatch, searchMsgState.loading, searchMsgState?.virtual]);

  // SCROLL TO BOTTOM
  useEffect(() => {
    if (searchMsgState.miniLoadingNext || searchMsgState.virtual) return;
    else if (
      inViewFirstElement &&
      searchMsgState.hasMoreNext &&
      searchMsgState.first_id
    ) {
      dispatch(
        paginatationSearchMsg({
          last_id: searchMsgState.first_id,
          type: "next",
          receiver_id: selectedConversation?.id,
          page: pageNext + 1,
          type_discussion: selectedConversation?.type,
        })
      );
      setPageNext((c) => c + 1);
    }
  }, [
    dispatch,
    inViewFirstElement,
    pageNext,
    searchMsgState.virtual,
    searchMsgState.first_id,
    searchMsgState.hasMoreNext,
    searchMsgState.miniLoadingNext,
    selectedConversation?.id,
    selectedConversation?.type,
  ]);

  // INITIAL SCROLL TO BOTTOM / READ MSG
  useEffect(() => {
    if (inViewFirstElementInList) {
      if (searchMsgState?.id) {
        scrollToBottomFunction();
        dispatch(stopSearchMsg(false));
      }
      if (source === "main") handleReadMsg();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    handleReadMsg,
    inViewFirstElementInList,
    scrollToBottomState,
    searchMsgState?.id,
    source,
  ]);

  return { loadMore, scrollToBottomFunction };
}

export default useCustomScroll;
