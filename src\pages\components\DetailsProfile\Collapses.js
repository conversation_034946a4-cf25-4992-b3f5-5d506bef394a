import {
  ArrowRightOutlined,
  CaretRightOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import React from "react";
import { Button, Collapse, Space, Tooltip, Typography, theme } from "antd";
import ItemHeader from "./ItemHeader";
import { Mail, Phone } from "lucide-react";
import { useTranslation } from "react-i18next";
const text = `
  A dog is a type of domesticated animal.
  Known for its loyalty and faithfulness,
  it can be found as a welcome guest in many households across the world.
`;

const Collapses = () => {
  const { token } = theme.useToken();
  const [t] = useTranslation("common");
  const getItems = (panelStyle) => [
    {
      key: "1",
      label: "Information",
      children: (
        <>
          <ItemHeader
            text="<EMAIL>"
            icon={<Mail size={15} />}
            title="Email"
            colorIcon="text-blue-500"
          />
          <ItemHeader
            text="9900000"
            icon={<Phone size={15} />}
            title={t("chat.phone")}
            colorIcon="text-blue-500"
          />
          <div className="flex items-center justify-end">
            <Typography.Link>
              View more <ArrowRightOutlined />
            </Typography.Link>
          </div>
        </>
      ),
      style: panelStyle,
    },
    {
      key: "2",
      label: (
        <div className="flex  justify-between">
          <span>Deals</span>
          <Tooltip title="Add Deal">
            <Button
              icon={<PlusOutlined />}
              type="text"
              shape="circle"
              size="small"
              onClick={(e) => e.stopPropagation()}
            />
          </Tooltip>

          {/* <PlusOutlined /> */}
        </div>
      ),
      children: <p>{text}</p>,
      style: panelStyle,
    },
    {
      key: "3",
      label: (
        <div className="flex  justify-between">
          <span>Tickets</span>
          <Tooltip title="Add ticket">
            <Button
              icon={<PlusOutlined />}
              type="text"
              shape="circle"
              size="small"
              onClick={(e) => e.stopPropagation()}
            />
          </Tooltip>
          {/* <PlusOutlined /> */}
        </div>
      ),
      children: <p>{text}</p>,
      style: panelStyle,
    },
  ];
  const panelStyle = {
    marginBottom: 24,
    background: token.colorFillAlter,
    borderRadius: token.borderRadiusLG,
    border: "none",
  };
  return (
    <Collapse
      bordered={false}
      defaultActiveKey={["1"]}
      expandIcon={({ isActive }) => (
        <CaretRightOutlined rotate={isActive ? 90 : 0} />
      )}
      style={{
        background: token.colorBgContainer,
      }}
      items={getItems(panelStyle)}
    />
  );
};
export default Collapses;
