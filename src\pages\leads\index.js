import React from "react";
import {
  <PERSON>,
  Button,
  Dropdown,
  Avatar,
  Tooltip,
  Tag,
  Segmented,
  Col,
  Row,
  Statistic,
  Select,
  Input,
  DatePicker,
} from "antd";
import {
  PlusOutlined,
  LikeOutlined,
  MoreOutlined,
  UserOutlined,
  PaperClipOutlined,
  CommentOutlined,
  PhoneOutlined,
  CalendarOutlined,
  DeleteOutlined,
  A<PERSON>toreOutlined,
  BarsOutlined,
} from "@ant-design/icons";
const handleChange = (value) => {
  console.log(`selected ${value}`);
};

const { RangePicker } = DatePicker;
const { Search } = Input;
const onSearch = (value) => console.log(value);

const items = [
  {
    key: "1",
    label: (
      <a
        target="_blank"
        rel="noopener noreferrer"
        href="https://www.antgroup.com"
      >
        Create activity
      </a>
    ),
  },
  {
    key: "2",
    label: (
      <a
        target="_blank"
        rel="noopener noreferrer"
        href="https://www.aliyun.com"
      >
        Preview
      </a>
    ),
  },
  {
    key: "3",
    label: (
      <a
        target="_blank"
        rel="noopener noreferrer"
        href="https://www.luohanacademy.com"
      >
        Edit
      </a>
    ),
  },
  {
    type: "divider",
  },
  {
    key: "4",
    danger: true,
    icon: <DeleteOutlined />,
    label: (
      <a
        target="_blank"
        rel="noopener noreferrer"
        href="https://www.luohanacademy.com"
      >
        Delete
      </a>
    ),
  },
];
const Kanban = () => (
  <div className="h-screen bg-white p-6">
    <div className="2xl:max mb-3 flex max-w-5xl items-center justify-between">
      <Segmented
        options={[
          {
            label: "List",
            value: "List",
            icon: <BarsOutlined />,
          },
          {
            label: "Kanban",
            value: "Kanban",
            icon: <AppstoreOutlined />,
          },
        ]}
      />
      <div className="flex space-x-3">
        <Select
          defaultValue="Default Pipeline"
          style={{
            width: 200,
          }}
          onChange={handleChange}
          options={[
            {
              value: "jack",
              label: "Jack",
            },
            {
              value: "lucy",
              label: "Lucy",
            },
            {
              value: "disabled",
              disabled: true,
              label: "Disabled",
            },
            {
              value: "Yiminghe",
              label: "yiminghe",
            },
          ]}
        />
        {/* <Button type="primary" icon={<PlusOutlined />}>
          Create Lead
        </Button> */}
      </div>
    </div>
    <div className="mb-3 flex hidden max-w-5xl items-center space-x-3">
      <Search
        placeholder="input search text"
        style={{
          width: 300,
        }}
        onSearch={onSearch}
        enterButton
      />

      <Row gutter={16}>
        <Col span={12}>
          <Statistic title="Feedback" value={1128} prefix={<LikeOutlined />} />
        </Col>
        <Col span={12}>
          <Statistic title="Unmerged" value={93} suffix="/ 100" />
        </Col>
      </Row>

      <RangePicker />
    </div>

    <div className="mx-auto h-full w-full overflow-scroll">
      <div className="block h-full space-x-4 overflow-x-scroll whitespace-nowrap">
        <div className="inline-flex h-full w-80 flex-col bg-gray-50 p-3 align-top overflow-y-hidden">
          <div className="mb-3 flex items-center justify-between">
            <div className="flex-1 text-center">
              <span className="block text-base font-semibold text-gray-900">
                Qualified To Buy
              </span>
              <span className="text-sm text-gray-500">$9,194.01 - 3 deals</span>
            </div>
            <Button type="link" icon={<PlusOutlined />}></Button>
          </div>
          <div className="space-y-3">
            <Card bordered={false}>
              <div className="grid grid-cols-6 gap-3">
                <div className="col-span-5 flex flex-col">
                  <a href="#" className="truncate font-medium">
                    #1- Schroeder-Abbott Schroeder-Abbott
                  </a>
                  <div className="mb-1 flex items-center justify-start space-x-4">
                    <span className="font-semibold text-gray-700">
                      $2,075.00
                    </span>
                    <a
                      href="#"
                      className="inline-flex border-0 border-b border-dashed text-xs font-medium text-gray-500"
                    >
                      1 Activity
                    </a>
                  </div>
                  <div className="flex space-x-2">
                    <Tooltip placement="top" title="Created date">
                      <span className="text-xs text-gray-500">2 hours ago</span>
                    </Tooltip>
                  </div>
                  <div className="mt-1 flex items-center space-x-1">
                    <Avatar size={24} icon={<UserOutlined />} />
                    <span className="text-xs text-gray-500">Owner name</span>
                  </div>
                </div>
                <div className="col-span-1 flex flex-col items-end gap-y-2">
                  <div className="flex">
                    <Dropdown menu={{ items }}>
                      <a onClick={(e) => e.preventDefault()}>
                        <MoreOutlined />
                      </a>
                    </Dropdown>
                  </div>

                  <Tooltip
                    placement="right"
                    title="1 commentaire"
                    className="flex items-center"
                  >
                    <span className="mr-1 text-xs font-medium text-gray-400">
                      1
                    </span>

                    <CommentOutlined className="text-gray-400" />
                  </Tooltip>
                  <Tooltip
                    placement="right"
                    title="1 appel en absence"
                    className="flex items-center"
                  >
                    <span className="mr-1 text-xs font-medium text-gray-400">
                      1
                    </span>
                    <PhoneOutlined className="text-gray-400" />
                  </Tooltip>
                  <Tooltip
                    placement="right"
                    title="3 fichiers"
                    className="flex items-center"
                  >
                    <span className="mr-1 text-xs font-medium text-gray-400">
                      3
                    </span>
                    <PaperClipOutlined className="text-gray-400" />
                  </Tooltip>
                </div>
              </div>
            </Card>
            <Card bordered={false}>
              <div className="grid grid-cols-6 gap-3">
                <div className="col-span-5 flex flex-col">
                  <a href="#" className="truncate font-medium">
                    #1- Schroeder-Abbott Schroeder-Abbott
                  </a>
                  <div className="mb-1 flex items-center justify-start space-x-4">
                    <span className="font-semibold text-gray-700">
                      $2,075.00
                    </span>
                    <a
                      href="#"
                      className="inline-flex border-0 border-b border-dashed text-xs font-medium text-gray-500"
                    >
                      1 Activity
                    </a>
                  </div>
                  <div className="flex space-x-2">
                    <Tooltip placement="top" title="Created date">
                      <span className="text-xs text-gray-500">2 hours ago</span>
                    </Tooltip>
                  </div>
                  <div className="mt-1 flex items-center space-x-1">
                    <Avatar size={24} icon={<UserOutlined />} />
                    <span className="text-xs text-gray-500">Owner name</span>
                  </div>
                </div>
                <div className="col-span-1 flex flex-col items-end gap-y-2">
                  <div className="flex">
                    <Dropdown menu={{ items }}>
                      <a onClick={(e) => e.preventDefault()}>
                        <MoreOutlined />
                      </a>
                    </Dropdown>
                  </div>

                  <Tooltip
                    placement="right"
                    title="1 commentaire"
                    className="flex items-center"
                  >
                    <span className="mr-1 text-xs font-medium text-gray-400">
                      1
                    </span>

                    <CommentOutlined className="text-gray-400" />
                  </Tooltip>
                  <Tooltip
                    placement="right"
                    title="1 appel en absence"
                    className="flex items-center"
                  >
                    <span className="mr-1 text-xs font-medium text-gray-400">
                      1
                    </span>
                    <PhoneOutlined className="text-gray-400" />
                  </Tooltip>
                  <Tooltip
                    placement="right"
                    title="3 fichiers"
                    className="flex items-center"
                  >
                    <span className="mr-1 text-xs font-medium text-gray-400">
                      3
                    </span>
                    <PaperClipOutlined className="text-gray-400" />
                  </Tooltip>
                </div>
              </div>
            </Card>
          </div>
        </div>
        <div className="inline-flex h-full w-80 flex-col rounded bg-gray-50 p-3 align-top overflow-y-hidden">
          <div className="mb-3 flex items-center justify-between">
            <div className="flex-1 text-center">
              <span className="block text-base font-semibold text-gray-900">
                Contact Made
              </span>
              <span className="text-sm text-gray-500">
                $50,647.00 - 9 deals
              </span>
            </div>
            <Button type="link" icon={<PlusOutlined />}></Button>
          </div>

          <div className="space-y-3">
            <Card bordered={false}>
              <div className="grid grid-cols-6 gap-3">
                <div className="col-span-5 flex flex-col">
                  <a href="#" className="truncate font-medium">
                    #1- Schroeder-Abbott Schroeder-Abbott
                  </a>
                  <div className="mb-1 flex items-center justify-start space-x-4">
                    <span className="font-semibold text-gray-700">
                      $2,075.00
                    </span>
                    <a
                      href="#"
                      className="inline-flex border-0 border-b border-dashed text-xs font-medium text-gray-500"
                    >
                      1 Activity
                    </a>
                  </div>
                  <div className="flex space-x-2">
                    <Tooltip placement="top" title="Created date">
                      <span className="text-xs text-gray-500">2 hours ago</span>
                    </Tooltip>
                  </div>
                  <Tooltip title="Owner name" className="mt-1">
                    <Avatar src="https://zos.alipayobjects.com/rmsportal/ODTLcjxAfvqbxHnVXCYX.png" />
                  </Tooltip>
                </div>
                <div className="col-span-1 flex flex-col items-end gap-y-2">
                  <div className="flex">
                    <Dropdown menu={{ items }}>
                      <a onClick={(e) => e.preventDefault()}>
                        <MoreOutlined />
                      </a>
                    </Dropdown>
                  </div>

                  <Tooltip
                    placement="right"
                    title="3 fichiers"
                    className="flex items-center"
                  >
                    <span className="mr-1 text-xs font-medium text-gray-400">
                      3
                    </span>
                    <PaperClipOutlined className="text-gray-400" />
                  </Tooltip>
                </div>
              </div>
            </Card>
            <Card bordered={true}>
              <div className="grid grid-cols-6 gap-3">
                <div className="col-span-5 flex flex-col">
                  <a href="#" className="truncate font-medium">
                    #1- Schroeder-Abbott Schroeder-Abbott
                  </a>
                  <div className="mb-1 flex items-center justify-start space-x-4">
                    <span className="font-semibold text-gray-700">
                      $2,075.00
                    </span>
                    <a
                      href="#"
                      className="inline-flex border-0 border-b border-dashed text-xs font-medium text-gray-500"
                    >
                      1 Activity
                    </a>
                  </div>
                  <div className="flex space-x-2">
                    <Tooltip placement="top" title="Created date">
                      <span className="text-xs text-gray-500">2 hours ago</span>
                    </Tooltip>
                    <Tooltip placement="top" title="Expiration date">
                      <Tag color="red" className="space-x-1 text-xs">
                        <CalendarOutlined />
                        <span>13 Mars 2023</span>
                      </Tag>
                    </Tooltip>
                  </div>
                  <div className="mt-1 flex items-center space-x-1">
                    <Avatar size={24} icon={<UserOutlined />} />
                    <span className="text-xs text-gray-500">Owner name</span>
                  </div>
                </div>
                <div className="col-span-1 flex flex-col items-end gap-y-2">
                  <div className="flex">
                    <Dropdown menu={{ items }}>
                      <a onClick={(e) => e.preventDefault()}>
                        <MoreOutlined />
                      </a>
                    </Dropdown>
                  </div>

                  <Tooltip
                    placement="right"
                    title="1 commentaire"
                    className="flex items-center"
                  >
                    <span className="mr-1 text-xs font-medium text-gray-400">
                      1
                    </span>

                    <CommentOutlined className="text-gray-400" />
                  </Tooltip>

                  <Tooltip
                    placement="right"
                    title="3 fichiers"
                    className="flex items-center"
                  >
                    <span className="mr-1 text-xs font-medium text-gray-400">
                      3
                    </span>
                    <PaperClipOutlined className="text-gray-400" />
                  </Tooltip>
                </div>
              </div>
            </Card>
          </div>
        </div>
        <div className="inline-flex h-full w-80 flex-col rounded bg-blue-50 p-3 align-top overflow-y-hidden"></div>
        <div className="inline-flex h-full w-80 flex-col rounded bg-red-50 p-3 align-top overflow-y-hidden"></div>
        <div className="inline-flex h-full w-80 flex-col rounded bg-yellow-50 p-3 align-top overflow-y-hidden"></div>
        <div className="inline-flex h-full w-80 flex-col rounded bg-blue-50 p-3 align-top overflow-y-hidden"></div>
        <div className="inline-flex h-full w-80 flex-col rounded bg-purple-50 p-3 align-top overflow-y-hidden">
          <Card title="Card title" bordered={false}>
            Card content
          </Card>
        </div>
      </div>
    </div>
  </div>
);
export default Kanban;
