import {
  CaretRightOutlined,
  ClockCircleOutlined,
  InfoCircleFilled,
  InfoCircleOutlined,
} from "@ant-design/icons";
import {
  Avatar,
  Card,
  Col,
  Collapse,
  Empty,
  List,
  Row,
  Space,
  Statistic,
  Tooltip,
  Typography,
} from "antd";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import RecentInteractions from "./RecentInteractions";
import { generateAxios } from "../../../../services/axiosInstance";
import Icon from "@ant-design/icons/lib/components/AntdIcon";
import MainService from "../../../../services/main.service";
import { useLocation, useParams } from "react-router-dom";
import { useSelector } from "react-redux";
import { setNewInteraction } from "new-redux/actions/vue360.actions/vue360";
import { useDispatch } from "react-redux";

const Overview = ({
  tasksTypes,
  addTab,
  dataSteps,
  isUpdate,
  setIsUpdate = () => {},
  module = "",
  source = "",
  headerHeight,
  from = "",
  contactInfo,
}) => {
  const [t] = useTranslation("common");
  const { search } = useSelector((state) => state.form);
  const [recentInteractions, setRecentInteractions] = useState([]);
  const [recentInteractionsBeforeChanged, setRecentInteractionsBeforeChanged] =
    useState([]);
  const [isMount, setIsMount] = useState(false);
  const [loadingRecentInteractions, setLoadingRecentInteracations] =
    useState(false);
  const [page, setPage] = useState(1);
  const [lastPage, setLastPage] = useState(1);
  const [kpi, setKpi] = useState([]);

  const [debounceSearch, setDebounceSearch] = useState("");
  const [listFilter, setFilter] = useState({ selected: [] });
  const [start, setStart] = useState("");
  const [end, setEnd] = useState("");
  const dispatch = useDispatch();
  const { activeTab360, newInteraction } = useSelector(
    (state) => state?.vue360
  );

  useEffect(() => {
    let timer;
    timer = setTimeout(() => {
      setDebounceSearch(search);
    }, 300);

    return () => clearTimeout(timer);
  }, [search]);
  // useEffect(() => {
  //   const getKpi = async () => {
  //     try {
  //       const {
  //         data: { data },
  //       } = await MainService.getKpiOverview360ByElement(
  //         elementID ? elementID : params.id
  //       );
  //       // Object.entries(data[0]).map(([key, value]) =>
  //       //   setKpi((prev) => [...prev, { title: key, value }])
  //       // );
  //       setKpi(
  //         Object.entries(data[0]).map(([key, value]) => ({ title: key, value }))
  //       );
  //     } catch (err) {
  //       console.log(err);
  //     }
  //   };
  //   if (activeTab360 === 3) getKpi();
  // }, [params.id, activeTab360, elementID]);
  useEffect(() => {
    // Fonction pour obtenir un icône aléatoire

    // Fonction pour obtenir un icône aléatoire parmi une liste

    const getLogs = async () => {
      setLoadingRecentInteracations(true);
      try {
        const { data } = await MainService.getOverview360({
          family_id: module ? module : contactInfo.family_id,
          element_id: contactInfo?.id,
          page: page,
          search: debounceSearch,
          filter:
            listFilter.selected?.length > 0
              ? listFilter.selected.join(",")
              : "",
          start,
          end,
        });
        setRecentInteractionsBeforeChanged(data.data);
        // Création d'un objet Map pour regrouper les éléments par date
        const groupedData = new Map();

        // Parcours du tableau de données
        data.data.forEach((item) => {
          const date = item.date.split(" ")[0]; // Extraction de la date sans l'heure
          if (groupedData.has(date)) {
            groupedData.get(date).push(item);
          } else {
            groupedData.set(date, [item]);
          }
        });

        // Conversion du Map en tableau de groupes
        const groupedArray = Array.from(groupedData, ([date, items]) => ({
          date,
          items,
        }));
        setRecentInteractions(groupedArray);

        setPage(data.meta.current_page);
        setLastPage(data.meta.last_page);
        //.sort((a,b)=>b.selected-a.selected));
        setLoadingRecentInteracations(false);
        setIsUpdate(false);
        setIsMount(true);
        if (newInteraction.type === "updateWithoutMercure") {
          dispatch(setNewInteraction({ type: "" }));
        }
      } catch (err) {
        setLoadingRecentInteracations(false);
      }
    };
    if (
      activeTab360 === 3 &&
      contactInfo?.id &&
      (newInteraction.type?.length > 0 || isUpdate || !isMount)
    ) {
      getLogs();
    }
    // return () => {
    //   if (activeTab360 === 3) {
    //     setPage(1);
    //     setLastPage(1);
    //   }
    // };
  }, [
    activeTab360,
    debounceSearch,
    listFilter,
    start,
    end,
    isUpdate,
    // elementID,
    // module,
    contactInfo?.id,
    newInteraction.type,
  ]);

  const panelStyle = {
    marginBottom: 24,
    background: "rgb(248 250 252)",
    borderRadius: 8,
    border: "none",
  };

  const getItems = (panelStyle) => [
    {
      key: "1",
      label: t("layout_profile_details.recentInteractions"),

      children:
        recentInteractions.length === 0 ? (
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        ) : (
          <RecentInteractions list={recentInteractions} addTab={addTab} />
        ),
      style: panelStyle,
    },
  ];
  return (
    <div>
      <Space
        direction="vertical"
        size="middle"
        style={{
          display: "flex",
        }}
      >
        {source !== "viewSphere2" ? (
          <div class="flex w-full flex-nowrap  justify-between gap-2 overflow-auto pb-1">
            {kpi.map((item, i) => (
              <div className="grow " key={i}>
                <Card bordered={true}>
                  {/* <Statistic title={item.title + " " + item.value} /> */}
                  <div className="flex flex-nowrap justify-between gap-1">
                    <Typography.Text
                      type="secondary"
                      className="whitespace-nowrap	"
                    >
                      {item.title}
                    </Typography.Text>
                    <Typography.Text className="whitespace-nowrap	">
                      {item.value}
                    </Typography.Text>
                  </div>
                </Card>
              </div>
            ))}
          </div>
        ) : null}
        {/* <Collapse
          bordered={false}
          defaultActiveKey={["1"]}
          expandIcon={({ isActive }) => (
            <CaretRightOutlined rotate={isActive ? 90 : 0} />
          )}
          items={getItems(panelStyle)}
        /> */}
        {/* <div
          style={{
            height: "calc(100vh - 275px)",
            overflow: "auto",
            marginRight: "-20px",
            paddingRight: "16px",
          }}
        > */}
        <RecentInteractions
          headerHeight={headerHeight}
          list={recentInteractions}
          loading={loadingRecentInteractions}
          page={page}
          lastPage={lastPage}
          setPage={setPage}
          setList={setRecentInteractions}
          tasksTypes={tasksTypes}
          addTab={addTab}
          listFilter={listFilter}
          setFilter={setFilter}
          setStart={setStart}
          setEnd={setEnd}
          listBeforeChanged={recentInteractionsBeforeChanged}
          setListBeforeChanged={setRecentInteractionsBeforeChanged}
          dataSteps={dataSteps}
          from={from}
          contactInfo={contactInfo}
        />
        {/* </div> */}
      </Space>
    </div>
  );
};

export default Overview;
