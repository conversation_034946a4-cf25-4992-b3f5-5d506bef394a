import {
  OPEN_TASK_DRAWER,
  OPEN_TASK_ROOM_DRAWER,
  GET_SELECTED_VIEW_IN_TASK,
  IS_USER_NOTIFIED,
  NEW_INCOMING_TASK_NOTIFICATION,
  ADD_TASK_NOTIFICATION_ACTION_TYPE,
  TASK_NOTIFICATION_PAYLOAD,
  SET_MSG_TASK_DRAWER,
  TASK_NOTIFICATION_DESCRIPTION,
  TOTAL_TASKS_NOTIFICATIONS,
  TASKS_FILTERS,
  ACTIVITIES_MESSAGES,
  FILTER_READ_ACTIVITIES_MESSAGES,
  HAND<PERSON>_ACTIVE_FILTERS,
  SET_REMINDERS,
  ADD_REMINDER,
  IS_OVERVIEW_MODAL_OPEN,
  HANDLE_SELECTED_PIPELINE,
  HANDLE_RELATION_ID,
  HANDLE_RELATION_TYPE,
  SAVE_PREFERENCES,
  SET_MSG_TASK_ID_DRAWER,
} from "../constants";

const preferredView = localStorage.getItem("tasks-view");

const initialState = {
  openTaskDrawer: false,
  msgTask: "",
  msgTaskId: "",
  openTaskRoomDrawer: false,
  selectedViewInTask: preferredView,
  isUserNotified: false,
  newIncomingNotification: false,
  taskNotifActionType: null,
  taskNotifPayload: null,
  taskNotifDescription: null,
  totalNotificationNumber: 0,
  tasksFilters: null,
  activitiesMessages: [],
  activeFilters: [],
  remindersList: {},
  isOverviewActive: false,
  pipelinePreference: {},
  relationId: null,
  relationType: null,
  userPreferences: [],
};
const TasksRealTime = (state = initialState, action) => {
  const { payload, type } = action;
  switch (type) {
    case OPEN_TASK_DRAWER:
      return { ...state, openTaskDrawer: payload };

    case OPEN_TASK_ROOM_DRAWER:
      return { ...state, openTaskRoomDrawer: payload };

    case GET_SELECTED_VIEW_IN_TASK:
      return { ...state, selectedViewInTask: payload };

    case IS_USER_NOTIFIED:
      return { ...state, isUserNotified: payload };

    case NEW_INCOMING_TASK_NOTIFICATION:
      return { ...state, newIncomingNotification: payload };

    case ADD_TASK_NOTIFICATION_ACTION_TYPE:
      return { ...state, taskNotifActionType: payload };

    case TASK_NOTIFICATION_PAYLOAD:
      return { ...state, taskNotifPayload: payload };

    case TASK_NOTIFICATION_DESCRIPTION:
      return { ...state, taskNotifDescription: payload };

    case TOTAL_TASKS_NOTIFICATIONS:
      return { ...state, totalNotificationNumber: payload };

    case SET_MSG_TASK_DRAWER:
      return { ...state, msgTask: payload };
    case SET_MSG_TASK_ID_DRAWER:
      return { ...state, msgTaskId: payload };

    case TASKS_FILTERS:
      return { ...state, tasksFilters: payload };

    case ACTIVITIES_MESSAGES:
      return { ...state, activitiesMessages: payload };

    case HANDLE_ACTIVE_FILTERS:
      return { ...state, activeFilters: payload };

    case SET_REMINDERS:
      return {
        ...state,
        remindersList: payload,
      };

    case ADD_REMINDER:
      return {
        ...state,
        remindersList: payload,
      };

    case IS_OVERVIEW_MODAL_OPEN:
      return {
        ...state,
        isOverviewActive: payload,
      };

    case HANDLE_SELECTED_PIPELINE:
      return {
        ...state,
        pipelinePreference: payload,
      };

    case HANDLE_RELATION_ID:
      return {
        ...state,
        relationId: payload,
      };

    case HANDLE_RELATION_TYPE:
      return {
        ...state,
        relationType: payload,
      };

    case FILTER_READ_ACTIVITIES_MESSAGES:
      return {
        ...state,
        activitiesMessages: {
          ...state?.activitiesMessages,
          unread_msg_room: state?.activitiesMessages?.unread_msg_room?.filter(
            (el) => el?.room_id !== payload
          ),
        },
      };
    case SAVE_PREFERENCES:
      return {
        ...state,
        userPreferences: payload,
      };

    default:
      return state;
  }
};

export default TasksRealTime;
