import React, { useState } from "react";
import { Disclosure } from "@headlessui/react";
import { ArrowPathIcon } from "@heroicons/react/24/outline";
import { useSelector } from "react-redux";
import { SquaresPlusIcon, FolderOpenIcon } from "@heroicons/react/24/outline";

function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}

const SideMenu = ({
  subNavigations,
  selectedSubMenu,
  setSelectedSubMenu,
  setShowDetailsView,
  // families,
  setFamilyId,
  clickedSidemenuItem,
  setClickedSidemenuItem,
  // loadFamily,
}) => {
  const [revealContent, setRevealContent] = useState(false);

  const { families, isSuccess, isLoading } = useSelector(
    (state) => state.families
  );

  // console.log("families store", isSuccess, isLoading);

  // console.log("selectedSubMenu", selectedSubMenu);

  return (
    <aside className="px-2 py-6 sm:px-6 lg:col-span-3 lg:px-0 lg:py-0">
      <nav className="space-y-1">
        {/* {subNavigations.map((item) => (
          <> */}
        {/* {console.log("item", item?.key)} */}
        <p
          key="fields"
          className={classNames(
            clickedSidemenuItem === "fields"
              ? "bg-gray-50 text-orange-600 hover:bg-white"
              : "text-gray-900 hover:bg-gray-50 hover:text-gray-900",
            "group flex w-full cursor-pointer items-center rounded-md px-3 py-2 text-sm font-medium"
          )}
          aria-current={clickedSidemenuItem === "fields" ? "page" : undefined}
          onClick={() => {
            setClickedSidemenuItem("fields");
            setRevealContent(!revealContent);
          }}
        >
          <SquaresPlusIcon
            className={classNames(
              clickedSidemenuItem === "fields"
                ? "text-orange-500"
                : "text-gray-400 group-hover:text-gray-500",
              "-ml-1 mr-3 h-6 w-6 flex-shrink-0"
            )}
            aria-hidden="true"
          />
          <span className="truncate">Fields</span>
          {/* {clickedSidemenuItem === "fields" ? ( */}
          <svg
            className={classNames(
              revealContent ? "rotate-90 text-gray-400" : "text-gray-300",
              "ml-3 h-5 w-5 flex-shrink-0 transform transition-colors duration-150 ease-in-out group-hover:text-gray-400"
            )}
            viewBox="0 0 20 20"
            aria-hidden="true"
          >
            <path d="M6 6L14 10L6 14V6Z" fill="currentColor" />
          </svg>
          {/* ) : null} */}
        </p>
        {revealContent && isLoading ? (
          <ArrowPathIcon className="flex h-6 w-6 animate-spin items-center justify-center text-gray-500" />
        ) : revealContent && families?.data ? (
          families?.data.map((child) => (
            <p
              key={child.id}
              className={classNames(
                selectedSubMenu === child.label
                  ? "bg-gray-50 text-orange-600 hover:bg-white"
                  : "text-gray-500 hover:bg-gray-50 hover:text-gray-700",
                "group flex cursor-pointer items-center rounded-md px-3 py-2 text-sm font-medium"
              )}
              onClick={() => {
                if (selectedSubMenu !== child.label) {
                  setShowDetailsView(false);
                }
                setSelectedSubMenu(child.label);
                setClickedSidemenuItem("fields");
                setFamilyId(child.id);
              }}
            >
              <span className="truncate">{child.label}</span>
            </p>
          ))
        ) : null}
        <p
          key="deals"
          className={classNames(
            clickedSidemenuItem === "deals"
              ? "bg-gray-50 text-orange-600 hover:bg-white"
              : "text-gray-900 hover:bg-gray-50 hover:text-gray-900",
            "group flex w-full cursor-pointer items-center rounded-md px-3 py-2 text-sm font-medium"
          )}
          aria-current={clickedSidemenuItem === "deals" ? "page" : undefined}
          onClick={() => {
            setClickedSidemenuItem("deals");
            setFamilyId(null);
            setSelectedSubMenu("");
          }}
        >
          <FolderOpenIcon
            className={classNames(
              clickedSidemenuItem === "deals"
                ? "text-orange-500"
                : "text-gray-400 group-hover:text-gray-500",
              "-ml-1 mr-3 h-6 w-6 flex-shrink-0"
            )}
            aria-hidden="true"
          />
          <span className="truncate">Deals</span>
        </p>
        {/* {revealContent && isLoading ? (
              <ArrowPathIcon className="flex justify-center items-center h-6 w-6 animate-spin text-gray-500" />
            ) : clickedSidemenuItem === "fields" && families?.data ? (
              families?.data.map((child) => (
                <p
                  key={child.id}
                  className={classNames(
                    selectedSubMenu === child.label
                      ? "bg-gray-50 text-orange-600 hover:bg-white"
                      : "text-gray-500 hover:text-gray-700 hover:bg-gray-50",
                    "group rounded-md px-3 py-2 flex items-center text-sm font-medium cursor-pointer"
                  )}
                  onClick={() => {
                    if (selectedSubMenu !== child.label) {
                      setShowDetailsView(false);
                    }
                    setSelectedSubMenu(child.label);
                    setFamilyId(child.id);
                  }}
                >
                  <span className="truncate">{child.label}</span>
                </p>
              ))
            ) : null} */}
        {/* </>
        ))} */}
      </nav>
    </aside>
  );
};

export default SideMenu;
