import React, { useEffect, useRef } from "react";
import { Form, Space, Tag, Switch, Table } from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { toastNotification } from "./ToastNotification";
import { useDispatch, useSelector } from "react-redux";
import SearchInTable from "../pages/components/Search";
import { setSearch } from "../new-redux/actions/menu.actions/menu";
import { generateAxios } from "../services/axiosInstance";
import FilterTable from "./FilterTable";
import { URL_ENV } from "index";
import MainService from "services/main.service";
const TableCurrencies = () => {
  const [form] = Form.useForm();
  const [loadSwitchUsed, setLoadSwitchUsed] = useState(false);
  const [switchId, setSwitchId] = useState("");
  const [data, setData] = useState([]);
  const [allData, setAllData] = useState([]);
  const [openFilterTable, setOpenFilterTable] = useState(false);
  const [editingKey, setEditingKey] = useState("");
  const [loading, setLoading] = useState(true);
  const isEditing = (record) => record.key === editingKey;
  const [t] = useTranslation("common");
  const { search } = useSelector((state) => state.form);
  const dispatch = useDispatch();
  const [loadSwitchPrimary, setLoadSwitchPrimary] = useState(false);
  const maxHeight = `calc(100vh - 300px)`;
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
  });
  const [sorter, setSorter] = useState({
    field: null,
    order: null,
  });
  const [filter, setFilter] = useState({});
  const sortData = (data, sortField, sortOrder) => {
    // Trier les données en fonction de la colonne sélectionnée par l'utilisateur
    let compareFn;
    if (sortField === "selected_currencies" || sortField === "primary")
      compareFn = (a, b) =>
        sortOrder === "ascend"
          ? a[sortField] - b[sortField]
          : sortOrder === "descend"
          ? b[sortField] - a[sortField]
          : data;
    else
      compareFn = (a, b) =>
        sortOrder === "ascend"
          ? a[sortField].localeCompare(b[sortField])
          : sortOrder === "descend"
          ? b[sortField].localeCompare(a[sortField])
          : data;
    return data.sort(compareFn);
  };

  useEffect(() => {
    const getCurrencies = async () => {
      setLoading(true);
      try {
        const { data } = await MainService.getCurrencies();
        setAllData(
          data.message.sort(
            (a, b) => b.selected_currencies - a.selected_currencies
          )
        );
        setData(
          [
            ...new Map(
              data.message.map((item) => [item["currency_name"], item])
            ).values(),
          ].filter((object) => {
            if (
              filter.selected_currencies &&
              filter.selected_currencies.length > 0
            ) {
              return filter.selected_currencies.includes(
                object.selected_currencies?.toString()
              );
            } else {
              return object;
            }
          })
        );

        //.sort((a,b)=>b.selected-a.selected));
        setLoading(false);
      } catch (err) {
        setLoading(false);
      }
    };
    getCurrencies();

    return () => dispatch(setSearch(""));
  }, []);

  useEffect(() => {
    setData(
      sortData(
        allData.filter((object) => {
          if (filter.selected && filter.selected.length > 0) {
            return filter.selected.includes(
              object.selected_currencies.toString()
            );
          } else {
            return object;
          }
        }),
        sorter.field,
        sorter.order
      )
    );
  }, [sorter, filter, allData]);

  const handleTableChange = (pagination, filters, sorter) => {
    setPagination(pagination);
    setSorter({
      field: sorter.field,
      order: sorter.order,
    });
    setFilter(filters);
  };

  let columns = [
    {
      title: t("table.header.name"),
      dataIndex: "currency_name",
      key: "currency_name",
      sorter: true,
    },
    {
      title: "Code",
      dataIndex: "currency",
      key: "currency",
      sorter: true,
    },
    {
      title: t("sales.symbol"),
      dataIndex: "currency_symbol",
      key: "currency_symbol",
    },
    {
      title: t("sales.defaultcurrency"),
      dataIndex: "primary",
      key: "primary",
      sorter: true,

      render: (_, reccord) => (
        <Switch
          loading={reccord.id == switchId ? loadSwitchPrimary : ""}
          disabled={reccord.primary == 1}
          size="small"
          defaultChecked={reccord.primary == 1 ? true : false}
          checked={reccord.primary == 1 ? true : false}
          onChange={(checked, event) => changePrimary(checked, event, reccord)}
        />
      ),
    },
    {
      title: t("sales.used"),
      dataIndex: "selected_currencies",
      key: "selected_currencies",
      // filters: [
      //   {
      //     text: t(`sales.used`),
      //     value: "1",
      //   },
      //   {
      //     text: t(`sales.unused`),
      //     value: "0",
      //   },
      // ],
      // onFilter: (value, record) => record.selected_currencies == value,
      sorter: true,
      render: (_, reccord) => (
        <Switch
          loading={reccord.id == switchId ? loadSwitchUsed : ""}
          disabled={reccord.primary == 1}
          size="small"
          defaultChecked={reccord?.selected_currencies == 1 ? true : false}
          checked={reccord?.selected_currencies == 1 ? true : false}
          onChange={(checked, event) => changeUsed(checked, event, reccord)}
        />
      ),
    },
  ];
  const sortedData = sortData(data, sorter.field, sorter.order);
  const paginatedData = sortedData.slice(
    (pagination.current - 1) * pagination.pageSize,
    pagination.current * pagination.pageSize
  );
  const filteredData = data
    .sort((a, b) => b.selected_currencies - a.selected_currencies)
    .filter((item) => {
      return (
        item.currency_name.toLowerCase().includes(search.toLowerCase()) ||
        item.currency.toLowerCase().includes(search.toLowerCase()) ||
        item.currency_symbol.toLowerCase().includes(search.toLowerCase())
      );
    });
  const changePrimary = async (checked, event, reccord) => {
    setSwitchId(reccord.id);
    setLoadSwitchPrimary(true);
    if (checked) {
      let formData = new FormData();
      formData.append("primary", 1);
      try {
        const {
          data: { data },
        } = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post(
          `countries_primary/update/${reccord.id}
      `,

          formData
        );
        setData((previous) =>
          previous.map((el) =>
            el.id == reccord.id ? data : { ...el, primary: 0 }
          )
        );
        setAllData((previous) =>
          previous.map((el) =>
            el.id == reccord.id ? data : { ...el, primary: 0 }
          )
        );
        setLoadSwitchPrimary(false);
        setSwitchId("");
        toastNotification(
          "success",
          reccord.currency_name + t("toasts.primaryCurrency"),
          "topRight"
        );
      } catch (err) {
        setSwitchId("");
        setData(
          data.map((el) =>
            el.id == reccord.id ? { ...el, status: 0 } : { ...el, primary: 0 }
          )
        );
        setAllData((prev) =>
          prev.map((el) =>
            el.id == reccord.id ? { ...el, status: 0 } : { ...el, primary: 0 }
          )
        );

        setLoadSwitchPrimary(false);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    } else {
      let formData = new FormData();
      formData.append("primary", 0);
      try {
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post(
          `curencies-primary/update/${reccord.id}
        `,

          formData
        );
        setData(
          data.map((el) =>
            el.id == reccord.id ? res.data.data : { ...el, primary: 0 }
          )
        );
        setAllData(
          data.map((el) =>
            el.id == reccord.id ? res.data.data : { ...el, primary: 0 }
          )
        );
        // setData(data);
        setLoadSwitchPrimary(false);
        setSwitchId("");

        toastNotification("success", t("toasts.updated"), "topRight");
      } catch (err) {
        setLoadSwitchPrimary(false);
        setSwitchId("");
        toastNotification("error", ` failed`, "topRight");
      }
    }
  };

  const changeUsed = async (checked, event, reccord) => {
    setSwitchId(reccord.id);
    setLoadSwitchUsed(true);
    if (checked) {
      let formData = new FormData();
      formData.append("selected_currencies", 1);
      try {
        const {
          data: { data },
        } = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post(
          `currencies_selected/update/${reccord.id}
      `,

          formData
        );
        setData((previous) =>
          previous.map((el) => (el.id == reccord.id ? data : el))
        );
        setAllData((previous) =>
          previous.map((el) => (el.id == reccord.id ? data : el))
        );
        // setData(data);
        setLoadSwitchUsed(false);
        setSwitchId("");
        toastNotification(
          "success",
          reccord.currency_name + t("toasts.used"),
          "topRight"
        );
      } catch (err) {
        setSwitchId("");

        setLoadSwitchUsed(false);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    } else {
      let formData = new FormData();
      formData.append("selected_currencies", 0);
      try {
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post(
          `currencies_selected/update/${reccord.id}
        `,

          formData
        );
        setData(data.map((el) => (el.id == reccord.id ? res.data.data : el)));
        setAllData((prev) =>
          prev.map((el) => (el.id == reccord.id ? res.data.data : el))
        );
        // setData(data);
        setLoadSwitchUsed(false);
        setSwitchId("");

        toastNotification(
          "success",
          reccord.currency_name + t("toasts.notUsed"),
          "topRight"
        );
      } catch (err) {
        setLoadSwitchUsed(false);
        setSwitchId("");

        // if (err.response.status === 422)
        //   toastNotification(
        //     "error",
        //     `${err.response.data.errors[0]}`,
        //     "topRight"
        //   );
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    }
  };

  return (
    <Space direction="vertical" style={{ width: "100%" }}>
      <div className="flex w-full px-4">
        <SearchInTable />
        <FilterTable
          setFilter={setFilter}
          selectedFilter={filter?.selected}
          filters={[
            {
              text: t(`sales.used`),
              value: "1",
            },
            {
              text: t(`sales.unused`),
              value: "0",
            },
          ]}
          t={t}
          openFilterTable={openFilterTable}
          setOpenFilterTable={setOpenFilterTable}
        />
      </div>
      {/* <GenericTableAddRow
        height={maxHeight}
        columns={columns}
        setLoading={setLoading}
        isEditing={isEditing}
        data={paginatedData}
        onChange={handleTableChange}
        setData={setData}
        loading={loading}
        save={save}
        edit={edit}
        EditableCell={EditableCell}
        onFinishFailed={onFinishFailed}
        cancel={cancel}
        form={form}
        apiRank="/countries"
        editingKey={editingKey}
        api="countries"
      /> */}
      <Table
        scroll={{ y: maxHeight }}
        size={"small"}
        // dataSource={filteredData.slice(
        //   (pagination.current - 1) * pagination.pageSize,
        //   pagination.current * pagination.pageSize
        // )}

        dataSource={filteredData}
        columns={columns}
        pagination={{ ...pagination, total: filteredData.length }}
        loading={loading}
        onChange={handleTableChange}
      />
    </Space>
  );
};
export default TableCurrencies;
