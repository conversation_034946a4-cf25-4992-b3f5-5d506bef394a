import { useCallback, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { callApi } from "../../../new-redux/services/chat.services";

function stripPrefix(str, search) {
  return str.startsWith(search) ? str.slice(search.length) : str;
}

function normalizeNumber(raw) {
  // remove parentheses, spaces, hyphens, etc.
  let cleaned = raw.replace(/[^\d+]/g, "");
  // convert "+<digits>" to "00<digits>"
  if (cleaned.startsWith("+")) {
    cleaned = "00" + cleaned.slice(1);
  }
  return cleaned;
}
const useActionCall = () => {
  //
  const dispatch = useDispatch();

  const user = useSelector((state) => state.user.user);
  const dialCode = user.location?.dial_code ?? "";
  // const reformDial = dialCode ? dialCode.replace("+", "00") : "";
  const reformDial = useMemo(
    () => (dialCode.startsWith("+") ? "00" + dialCode.slice(1) : dialCode),
    [dialCode]
  );

  return useCallback(
    (dest, id = null, family_id = null) => {
      // const number = `${dest}`?.replace(dialCode, "")
      const withoutDial = stripPrefix(String(dest), reformDial);
      // id : id (user, contact, company, ...) that you want to call if exist
      // family_id: family_id of the (user, contact, company, ...) you want to call if exist
      // console.log(dist, id, family_id);
      const post_numberR = normalizeNumber(withoutDial);
      // if (action === "video")
      //   dispatch(
      //     callVideo({
      //       post_numberR: distNumber,
      //       // errorText: t("toasts.errorFetchApi"),
      //     })
      //   );
      // else
      dispatch(
        callApi({
          post_numberR,
          id: id,
          family_id: family_id,
        })
      );
    },
    [dispatch, reformDial]
  );
};

export default useActionCall;
