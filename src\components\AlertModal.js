import { Fragment, useRef } from "react";
import { Dialog, Transition } from "@headlessui/react";
import { ExclamationTriangleIcon } from "@heroicons/react/24/outline";
import { ArrowPathIcon, XMarkIcon } from "@heroicons/react/24/outline";
import { useDispatch, useSelector } from "react-redux";

import { removeSpecificOption, reset } from "../redux/fieldsSlice";

const AlertModal = ({
  openDeleteModal,
  setOpenDeleteModal,
  source,
  deleteOptionId,
  setSourceComponent,
  disableDeleteBtn,
  setDisableDeleteBtn,
}) => {
  const cancelButtonRef = useRef(null);
  const dispatch = useDispatch();
  const { isLoading } = useSelector((state) => state.fields);

  const handleRemoveSpecificOption = async (e) => {
    e && e.preventDefault();

    // if (source === "field_table" && deleteOptionId !== null) {
    //   dispatch(removeSpecificField(deleteOptionId));
    // }

    if (source !== "field_table" && deleteOptionId !== null) {
      dispatch(removeSpecificOption(deleteOptionId));
    }
  };

  // useEffect(() => {
  //   let timer = setTimeout(() => {
  //     dispatch(reset());
  //     setOpenDeleteModal(false);
  //   }, 3000);
  //   return () => clearTimeout(timer);
  // }, [dispatch, isSuccess, setOpenDeleteModal]);

  // console.log("btn", disableDeleteBtn);

  return (
    <Transition.Root show={openDeleteModal} as={Fragment}>
      <Dialog
        as="div"
        className="relative z-10"
        initialFocus={cancelButtonRef}
        onClose={() => {}}
      >
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pt-5 pb-4 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                {/*  */}
                <div className="absolute top-0 right-0 hidden pt-4 pr-4 sm:block">
                  <button
                    type="button"
                    className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                    onClick={() => {
                      setOpenDeleteModal(false);
                      setSourceComponent(null);
                      dispatch(reset());
                      setDisableDeleteBtn(false);
                    }}
                  >
                    <span className="sr-only">Close</span>
                    <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                  </button>
                </div>
                {/*  */}
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                    <ExclamationTriangleIcon
                      className="h-6 w-6 text-red-600"
                      aria-hidden="true"
                    />
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <Dialog.Title
                      as="h3"
                      className="text-lg font-medium leading-6 text-gray-900"
                    >
                      Are you sure you want to perform this action?
                    </Dialog.Title>
                    {/* {isError ? (
                      <div className="mt-2">
                        <Alert type="error" alertMessage="Something went wrong" />
                      </div>
                    ) : isOptDeleted || isSuccess ? (
                      <Alert
                        type="success"
                        alertMessage={`${
                          source === "field_table" ? "Field" : "Option"
                        } was deleted successfully`}
                      />
                    ) : null} */}
                  </div>
                </div>
                <div className="mt-5 sm:mt-4 sm:ml-10 sm:flex sm:pl-4 float-right">
                  <button
                    disabled={disableDeleteBtn}
                    type="button"
                    className={`inline-flex w-full ${
                      disableDeleteBtn
                        ? "cursor-not-allowed opacity-50"
                        : "cursor-pointer  hover:bg-red-700"
                    } justify-center mr-2 rounded-md border border-transparent bg-red-600 px-4 py-2 text-base font-medium text-white shadow-sm  focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm`}
                    onClick={handleRemoveSpecificOption}
                  >
                    {isLoading ? (
                      <ArrowPathIcon className="flex justify-center items-center h-6 w-6 animate-spin text-white" />
                    ) : (
                      "Confirm"
                    )}
                  </button>
                  <button
                    type="button"
                    className="mt-3 inline-flex w-full   justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:mt-0 sm:w-auto sm:text-sm"
                    onClick={() => {
                      setOpenDeleteModal(false);
                      setSourceComponent(null);
                      dispatch(reset());
                      setDisableDeleteBtn(false);
                    }}
                    ref={cancelButtonRef}
                  >
                    Cancel
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
};

export default AlertModal;
