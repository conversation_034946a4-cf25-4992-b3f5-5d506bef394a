import {
  Button,
  Divider,
  Dropdown,
  Input,
  Popover,
  Select,
  Skeleton,
  Space,
  Switch,
  Table,
  Tag,
  Tooltip,
  Typography,
} from "antd";

import React, { useEffect, useState } from "react";
import Confirm from "./GenericModal";
import "./table.css";
import { toastNotification } from "./ToastNotification";
import {
  CheckCircleOutlined,
  DeleteOutlined,
  EditOutlined,
  ExclamationCircleOutlined,
  RestOutlined,
  WarningOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { generateAxios } from "../services/axiosInstance";
import { Ban, CheckCircle, CheckCircle2 } from "lucide-react";
import { SET_USER_INFOS } from "../new-redux/constants";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import {
  setAccountData,
  setFetchedAccount,
} from "../new-redux/actions/mail.actions";
import {
  setInavlidConfigMail,
  setOpenConfigMail,
} from "../new-redux/actions/menu.actions/menu";
import { URL_ENV } from "index";
import { AvatarChat } from "./Chat";
import { getName } from "pages/layouts/chat/utils/ConversationUtils";
import DispatcherMail from "./DispatcherMail";
import { EXTENSIONS_ARRAY } from "pages/tasks/helpers/calculateSum";
import ActionsComponent from "pages/tasks/ActionsComponent";
import { TagWithHiddenCount } from "./TagWithHiddenCount";

const TableConfigMail = ({
  data,
  setData,
  loading,
  setLabelId,
  toggle,
  setToggle,
  count,
  setCount,
  setLoading,
}) => {
  const [t] = useTranslation("common");
  const { dataAccounts } = useSelector((state) => state.mailReducer);

  const [loadPrimary, setLoadPrimary] = useState(false);
  const [loadSync, setLoadSync] = useState(false);
  const [render, setRender] = useState(0);

  const [currentPage, setCurrentPage] = useState(1);

  const [pageSize, setPageSize] = useState(20);
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.user);
  const { invalidConfigMail } = useSelector((state) => state.menu);

  useEffect(() => {
    if (invalidConfigMail.length > 0)
      setData((prev) =>
        prev.map((el) =>
          invalidConfigMail.includes(el.email) ? { ...el, status: 0 } : el
        )
      );
  }, [invalidConfigMail]);

  const columns = [
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
      width: 300,
      fixed: "left",
      render: (_, record) => (
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {user?.id !== record.user_id ? (
              <div>{record.email}</div>
            ) : (
              <div
                className="cursor-pointer font-medium text-blue-600 hover:underline dark:text-blue-500"
                onClick={() => {
                  setLabelId(record.id);
                  setCount(count + 1);
                  dispatch(setOpenConfigMail(true));
                }}
              >
                {record.email}
              </div>
            )}
            {/* {record?.status == 0 ? (
              <WarningOutlined
                onClick={() => warning(record.id)}
                className="animate-blink  text-red-500"
              />
            ) : (
              ""
            )} */}
          </div>
          <DropdownOption reccord={record} />
        </div>
      ),
    },
    {
      title: t("tasks.creatorRole"),
      dataIndex: "creator",
      key: "creator",
      width: 90,
      render: (_, record) => (
        <ActionsComponent
          elementValue={{
            ...record?.creator,
            label: record?.creator?.label_data,
            id: record?.creator?.user_id,
          }}
        >
          <AvatarChat
            fontSize={"0.875rem"}
            className={"mx-1.5 flex items-center justify-center"}
            height={"32px"}
            width={"32px"}
            url={`${
              URL_ENV?.REACT_APP_BASE_URL + URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
            }${record?.creator?.avatar}`}
            hasImage={EXTENSIONS_ARRAY?.includes(
              record?.creator?.avatar?.split(".")?.pop()
            )}
            name={getName(record?.creator?.label_data, "avatar")}
            type="user"
          />
        </ActionsComponent>
      ),
    },

    {
      title: t("emailAccounts.check"),
      dataIndex: "status",
      key: "status",
      width: "80px",
      render: (_, { status, id, email }) =>
        status == 1 ? (
          <Button
            icon={<CheckCircle2 color="green" />}
            shape="circle"
            type="text"
            className="buttonIconCheck cursor-pointer"
            onClick={() => warning(id)}
          />
        ) : (
          <Tooltip
            title={t("mailing.invalidConfig", {
              email: "",
            })}
          >
            <Button
              icon={<Ban />}
              shape="circle"
              danger
              className="buttonIconCheck cursor-pointer"
              type="text"
              onClick={() => warning(id, email)}
            />
          </Tooltip>
        ),
    },
    {
      title: t("emailAccounts.account"),
      dataIndex: "type",
      width: "120px",
      key: "type",
      render: (_, { type }) => (
        <Tag color={type === "1" ? "#108ee9" : ""}>
          {type === "1"
            ? t(`emailAccounts.shared`)
            : t(`emailAccounts.personal`)}
        </Tag>
      ),
    },
    {
      title: t("emailAccounts.mailToTicket"),
      dataIndex: "type",
      width: "120px",
      key: "type",
      render: (_, { is_convert_to_ticket }) => (
        <Switch
          size="small"
          checked={is_convert_to_ticket === 1 ? true : false}
          disabled={true}
        />
      ),
    },

    {
      title: t("emailAccounts.disableSync"),
      width: "150px",

      filters: [
        {
          text: t(`emailAccounts.synchronous`),
          value: "1",
        },
        {
          text: t(`emailAccounts.asynchronous`),
          value: "0",
        },
      ],
      onFilter: (value, record) => record.sync_disabled == value,
      render: ({ ...reccord }, { sync_disabled }) => (
        <Switch
          size="small"
          checked={sync_disabled === 1 ? true : false}
          defaultChecked={sync_disabled === 1 ? true : false}
          onChange={(checked, event) =>
            changeDisableSync(checked, event, reccord)
          }
          loading={reccord.id === toggle ? loadSync : false}
          disabled={user?.id !== reccord.user_id}
        />
      ),
    },
    {
      title: t("emailAccounts.primaryAccount"),
      width: "150px",
      sorter: (a, b) => b.primary_account - a.primary_account,
      render: ({ ...reccord }) => (
        <div className="ml-1">
          <Switch
            size="small"
            disabled={reccord.primary_account === 1}
            checked={reccord.primary_account === 1 ? true : false}
            defaultChecked={reccord.primary_account === 1 ? true : false}
            onChange={(checked, event) =>
              changePrimaryAccount(checked, event, reccord)
            }
            loading={reccord.id === toggle ? loadPrimary : false}
          />
        </div>
      ),
    },
    {
      title: t("table.header.departments"),
      width: "150px",
      render: (_, { departement_labels }) => (
        <div className="space-x-1 space-y-1">
          <TagWithHiddenCount
            tags={departement_labels}
            name={t("table.header.departments")}
          />
        </div>
      ),
    },
    {
      title: t("mailing.dispatcher"),
      dataIndex: "dispatcher",
      key: "dispatcher",
      width: "250px",
      render: (
        _,
        { id, type, departement_ids, dispatcheur, user_id, processing_time }
      ) =>
        type === "1" ? (
          <DispatcherMail
            id={id}
            setData={setData}
            dispatcheur={dispatcheur}
            departement_ids={departement_ids}
            setLoading={setLoading}
            loading={loading}
            type={type}
            creatorId={user_id}
            processing_time={processing_time}
          />
        ) : null,
    },
    {
      title: t("emailAccounts.accountType"),
      dataIndex: "account_type",
      width: "150px",

      key: "account_type",
      sorter: (a, b) => a.account_type - b.account_type,
    },

    {
      title: t("mailing.processingTime"),
      width: "160px",
      render: (_, { processing_time }) =>
        processing_time ? (
          <Tag>{processing_time + " " + t("helpDesk.hours")}</Tag>
        ) : null,
    },
  ];

  const getRowClassName = (record, index) => {
    if (index === 0 && render > 0) {
      return "animated-row";
    }
    return "";
  };

  const warning = async (id, email) => {
    setLoading(true);
    try {
      const res = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).post(`tester-config/${id}`);
      if (res.data.message === "Success") {
        setData((prev) =>
          prev.map((el) => (id == el.id ? { ...el, status: 1 } : el))
        );
        if (invalidConfigMail.some((el) => el === id)) {
          dispatch(
            setInavlidConfigMail(invalidConfigMail.filter((el) => el !== id))
          );
        }
        setLoading(false);

        toastNotification(
          "success",
          t("mailing.successTest"),

          "topRight"
        );
      } else {
        setData((prev) =>
          prev.map((el) => (id == el.id ? { ...el, status: 0 } : el))
        );
        setLoading(false);
      }
    } catch (err) {
      console.log(err);
      setData((prev) =>
        prev.map((el) => (id == el.id ? { ...el, status: 0 } : el))
      );
      setLoading(false);

      toastNotification("error", err?.response?.data?.error, "topRight");
    }
  };
  const deleteTask = async (id, label) => {
    try {
      const { data } = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).delete(`/config-mails/${id}`);

      setData((prev) => prev.filter((el) => el.id !== id));
      dispatch(setAccountData(dataAccounts.filter((el) => el.value !== id)));

      dispatch({
        type: SET_USER_INFOS,
        payload: {
          ...user,
          accounts_email: user.accounts_email.filter((el) => el.id !== id),
        },
      });
      toastNotification(
        "success",
        `${label}  ${t("toasts.deleted")}`,
        "topRight"
      );
    } catch (err) {
      toastNotification("error", t("toasts.somethingWrong"), "topRight");
    }
  };
  const changePrimaryAccount = async (checked, event, reccord) => {
    setLoadPrimary(true);
    let payload;
    if (checked) {
      payload = {
        primary_account: 1,
        type: reccord?.type,
        departement_ids: reccord?.departement_ids,
        processing_time: reccord?.processing_time,
      };
      try {
        setToggle(reccord.id);
        const {
          data: { data },
        } = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post(
          `/config-mails/update/${reccord.id}
      `,
          payload
        );
        const newData = data.map((el, i) => ({
          rank: i + 1,
          ...el,
          key: el.id,
        }));
        const oldRank = reccord.rank;
        const newRank = checked ? 1 : data.length;
        const fromIndex = oldRank - 1;
        const toIndex = newRank - 1;
        setRender(count + 1);
        newData.splice(toIndex, 0, newData.splice(fromIndex, 1)[0]);
        setData(data);
        dispatch({
          type: SET_USER_INFOS,
          payload: {
            ...user,
            accounts_email: data,
          },
        });
        dispatch(setFetchedAccount(false));
        // setData((previous) =>
        //   previous.map((el, i) => ({ rank: i + 1, ...el }))
        // );

        // setData((prev) =>
        //   prev
        //     .map((el) =>
        //       el.id === reccord.id
        //         ? { ...el, primary_account: 1 }
        //         : { ...el, primary_account: 0 }
        //     )
        //     // .sort((a, b) => {
        //     //   return b.primary_account - a.primary_account;
        //     // })
        // );

        // setMsgAlert(`${contact.label} has affected to new owner`)
        // setSuccessAlert(true)

        setLoadPrimary(false);

        toastNotification(
          "success",
          reccord.email + t("toasts.primaryMail"),
          "topRight"
        );
      } catch (err) {
        setLoadPrimary(false);

        if (err?.response?.status === 422)
          toastNotification(
            "error",
            `${err.response.data.errors[0]}`,
            "topRight"
          );
        else toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    } else {
      setLoadPrimary(true);

      setToggle(null);
      try {
        payload = {
          primary_account: 0,
          type: reccord?.type,
          departement_ids: reccord?.departement_ids,
          processing_time: reccord?.processing_time,
        };
        const {
          data: { data },
        } = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post(
          `/config-mails/update/${reccord.id}
        `,
          payload
        );
        dispatch({
          type: SET_USER_INFOS,
          payload: {
            ...user,
            accounts_email: data,
          },
        });
        // setData(data)

        // setMsgAlert(`${contact.label} has affected to new owner`)
        // setSuccessAlert(true)

        toastNotification("success", t("toasts.updated"), "topRight");
        setLoadPrimary(false);
      } catch (err) {
        setLoadPrimary(false);

        console.log(err);
        if (err?.response?.status === 422)
          toastNotification(
            "error",
            `${err.response.data.errors[0]}`,
            "topRight"
          );
        else toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    }
  };
  const changeDisableSync = async (checked, event, reccord) => {
    setLoadSync(true);
    setToggle(reccord.id);
    let payload;
    if (checked) {
      reccord.type === "1"
        ? (payload = {
            sync_disabled: 1,
            type: reccord?.type,
            departement_ids: reccord?.departement_ids,
            processing_time: reccord?.processing_time,
          })
        : (payload = { sync_disabled: 1, type: reccord?.type });

      try {
        const {
          data: { data },
        } = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post(
          `/config-mails/update/${reccord.id}`,

          payload
        );
        setData(
          data.map((el, i) => ({ rank: i + 1, ...el, key: el.id }))
          // .sort((a, b) => {
          //   return b.primary_account - a.primary_account;
          // })
        );
        dispatch({
          type: SET_USER_INFOS,
          payload: {
            ...user,
            accounts_email: data,
          },
        });
        setLoadSync(false);
        toastNotification(
          "success",
          reccord.email + t("toasts.sync"),
          "topRight"
        );
      } catch (err) {
        setLoadSync(false);
        if (err?.response?.status === 422)
          toastNotification(
            "error",
            `${err.response.data.errors[0]}`,
            "topRight"
          );
        else toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    } else {
      setLoadSync(true);
      try {
        reccord.type === "1"
          ? (payload = {
              sync_disabled: 0,
              type: reccord?.type,
              departement_ids: reccord?.departement_ids,
              processing_time: reccord?.processing_time,
            })
          : (payload = { sync_disabled: 0, type: reccord?.type });
        const {
          data: { data },
        } = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post(
          `/config-mails/update/${reccord.id}`,

          payload
        );
        setData(
          data.map((el, i) => ({ rank: i + 1, ...el, key: el.id }))
          // .sort((a, b) => {
          //   return b.primary_account - a.primary_account;
          // })
        );
        dispatch({
          type: SET_USER_INFOS,
          payload: {
            ...user,
            accounts_email: data,
          },
        });
        setLoadSync(false);
        toastNotification(
          "success",
          reccord.email + t("toasts.notSync"),
          "topRight"
        );
      } catch (err) {
        setLoadSync(false);
        if (err?.response?.status === 422)
          toastNotification(
            "error",
            `${err.response.data.errors[0]}`,
            "topRight"
          );
        else toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    }
  };
  const handleChangePage = (page, pageSize) => {
    setCurrentPage(page);
    setPageSize(pageSize);
  };

  const handleChangePageSize = (current, size) => {
    setCurrentPage(1);
    setPageSize(size);
  };
  const DropdownOption = (reccord) => {
    const items = [
      {
        label: t("table.edit"),
        key: "1",
        icon: <EditOutlined />,
        disabled:
          reccord.reccord.default == 1 || user?.id !== reccord?.reccord?.user_id
            ? true
            : false,
      },
      {
        label: t("table.delete"),
        danger: true,
        key: "2",
        icon: <DeleteOutlined />,
        disabled:
          // reccord.reccord.primary_account == 1 ||
          reccord.reccord.user_id !== user.id ? true : false,
      },
    ];
    return (
      <div className="r-8">
        <Dropdown
          trigger={["click"]}
          placement="bottomLeft"
          arrow
          menu={{
            items,
            onClick: (e) => {
              if (e.key === "1") {
                setLabelId(reccord.reccord.id);
                setCount(count + 1);
                dispatch(setOpenConfigMail(true));
              }
              if (e.key === "2") {
                Confirm(
                  `Delete "${reccord.reccord.email}" `,
                  "Confirm",
                  <RestOutlined style={{ color: "red" }} />,
                  function func() {
                    return deleteTask(
                      reccord.reccord.id,
                      reccord.reccord.email
                    );
                  },
                  true
                );
              }
            },
          }}
        >
          <svg
            className="h-4 w-4 cursor-pointer text-gray-400 hover:text-gray-700"
            aria-hidden="true"
            fill="currentColor"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"></path>
          </svg>
        </Dropdown>
      </div>
    );
  };

  return (
    <>
      <Table
        rowKey="key"
        // rowClassName={getRowClassName}
        columns={columns}
        dataSource={data}
        loading={loading}
        size={"small"}
        scroll={{
          x: 1500,
        }}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          total: data && data.length,
          onChange: handleChangePage,
          onShowSizeChange: handleChangePageSize,
          showSizeChanger: false,
          pageSizeOptions: ["10", "20", "50", "100"],
          hideOnSinglePage: data.length < 11 && true,
        }}
      />
    </>
  );
};
export default TableConfigMail;
