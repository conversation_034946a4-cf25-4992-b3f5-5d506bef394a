import { Card, Col, Row, Statistic } from "antd";
import React from "react";
import { Gauge<PERSON><PERSON> } from "../ChartsDashboard";
import {
  backgroundImagecard,
  GoTo,
  gridStyle,
  stylesCard,
} from "pages/home/<USER>";
import { LucideIcon } from "../ticket.stat/ticket";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import CountUp from "react-countup";
import { useSelector } from "react-redux";

const CardStatTicket = ({ data, cardsTickets }) => {
  const navigate = useNavigate();
  const [t] = useTranslation("common");
  const formatter = (value) => <CountUp end={value} separator="," />;
  const { user } = useSelector((state) => state.user);

  return (
    <Card
      style={{ backgroundImage: backgroundImagecard }}
      styles={{
        ...stylesCard,
      }}
      title={
        <div className="flex items-center justify-between pr-2">
          Tickets &nbsp;
          <GoTo
            to={"8"}
            title={"Tickets"}
            navigate={navigate}
            t={t}
            user={user}
          />
        </div>
      }
    >
      <Row>
        <Col span={12}>
          <Card style={{ background: backgroundImagecard, border: 0 }}>
            {cardsTickets.map((el, i) => (
              <Card.Grid
                hoverable={false}
                style={{
                  ...gridStyle,
                  borderStartStartRadius: i === 0 ? 6 : 0,
                  borderEndStartRadius: i === 2 ? 6 : 0,
                }}
                key={`card-ticket-${el.id || i}`}
              >
                <Statistic
                  formatter={typeof el.value === "number" ? formatter : null}
                  title={el.name}
                  value={el.value}
                  valueStyle={{
                    color: el.color,
                  }}
                  prefix={
                    <LucideIcon
                      iconName={el?.icon}
                      color={el?.color}
                      size={20}
                    />
                  }
                />
              </Card.Grid>
            ))}
          </Card>
        </Col>
        <Col span={12}>
          <Card

          // styles={{
          //   header: { padding: "4px 6px 4px 10px", minHeight: "auto" },
          // }}
          // title={"Tickets " + t("vue360.resolved").toLowerCase()}
          >
            <Card.Grid
              hoverable={false}
              style={{ width: "100%", height: 174, padding: 8 }}
              className="cardGraphDashboard"
            >
              <GaugeChart
                data={{
                  total: data?.data?.total,
                  used_storage: data?.data?.used,
                  title: data?.data?.name,
                  unit: data?.data?.name,
                }}
                title=""
                height={170}
                name={data?.name}
                inverseColor={true}
                size={"170%"}
              />
            </Card.Grid>
          </Card>
        </Col>
      </Row>
    </Card>
  );
};

export default CardStatTicket;
