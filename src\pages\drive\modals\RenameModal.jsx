import React from "react";
import { Modal, Form, Input, Button } from "antd";
import { FolderFilled, FileTextFilled } from "@ant-design/icons";
import { useTranslation } from "react-i18next";

const RenameModal = ({
  isVisible,
  onCancel,
  onSubmit,
  itemToRename,
  form,
  loading = false
}) => {
  const [t] = useTranslation("common");

  return (
    <Modal
      title={t("drive.renameModal.title", { name: itemToRename?.name })}
      open={isVisible}
      onCancel={onCancel}
      footer={null}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={onSubmit}
      >
        <Form.Item
          name="name"
          label={t("drive.renameModal.name")}
          rules={[
            { required: true, message: t("drive.renameModal.required") },
            { min: 2, message: t("drive.renameModal.minText") },
            { max: 45, message: t("drive.renameModal.maxText") },
          ]}
        >
          <Input
            placeholder={t("drive.renameModal.placeholder")}
            autoFocus
            suffix={itemToRename?.type === "folder" ? <FolderFilled /> : <FileTextFilled />}
          />
        </Form.Item>

        <div className="flex justify-end space-x-2">
          <Button onClick={onCancel}>
            {t("drive.renameModal.cancel")}
          </Button>
          <Button 
            type="primary" 
            htmlType="submit" 
            loading={loading}
          >
            {t("drive.renameModal.save")}
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

export default RenameModal; 