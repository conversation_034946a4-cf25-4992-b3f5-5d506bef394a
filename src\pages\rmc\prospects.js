import { useState } from "react";
import { LoadingAnimation } from "../components/loader";
import { useSelector } from "react-redux";
import { URL_ENV } from "index";

const Prospects = () => {
  const [hide, setHide] = useState(false);
  const { user } = useSelector((state) => state.user);
  const token = localStorage.getItem("accessToken");

  return (
    <div>
      {hide === false ? <LoadingAnimation /> : <></>}

      <iframe
        src={`${URL_ENV?.REACT_APP_RMC_URL}?Token=${token}&area=users`}
        title="chat"
        display="block"
        width="100%"
        // height= {`${deviceHeight}px -120px`}
        sendbox="allow-same-origin allow-popups"
        allowfullscreen="true"
        style={{ height: "calc(100vh - 70px)", border: "none" }}
        allowtransparency="true"
        onLoad={() => setHide(true)}></iframe>
    </div>
  );
};
export default Prospects;
