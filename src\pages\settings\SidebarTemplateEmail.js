import React, { useCallback, useEffect, useRef, useState } from "react";
// import "./styles.css";
import {
  Button,
  Input,
  Tree,
  DirectoryTree,
  Modal,
  Dropdown,
  Menu,
  Space,
  Popconfirm,
  Typography,
  Tooltip,
  Spin,
  Empty,
  Form,
  message,
  Tag,
  Select,
} from "antd";
import { GenericButton } from "../components/GenericButton";
import {
  EditOutlined,
  PlusOutlined,
  DeleteOutlined,
  RestOutlined,
  DownOutlined,
  FolderOutlined,
  FileOutlined,
} from "@ant-design/icons";
import { useParams } from "react-router-dom";
import MainService from "../../services/main.service";
import { toastNotification } from "../../components/ToastNotification";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import Confirm from "../../components/GenericModal";
import { FiEdit, FiMoreVertical, FiTrash } from "react-icons/fi";
import { AiOutlineFileAdd } from "react-icons/ai";
import {
  DragFoldersOrdering,
  changeRankOnDragAndDropChild,
  handleDragAndDropFromChildToFolder,
  isChild,
  isFolder,
  sortFolders,
} from "../../pages/wiki/sideBarDndUtils";
import { setFamilyId } from "new-redux/actions/menu.actions/menu";
import { useDispatch } from "react-redux";

const SidebarTemplateEmail = ({
  selectedGroup,
  selectedNode,
  setSelectedNode,
  expandedKeys,
  setExpandedKeys,
  type,
  value,
  folders,
  setFolders,
  nodeName,
  setNodeName,
  selectedFolder,
  setSelectedFolder,
  loading,
  setLoading,
  delete1,
  setDisabled,
  fields,
  setFields,
  setSignature,
  fieldsCompany,
  modules,
}) => {
  const [t] = useTranslation("common");
  const { user } = useSelector((state) => state.user);
  const [form] = Form.useForm();
  const { families } = useSelector((state) => state.families);

  const [newTitle, setNewTitle] = useState("");
  const [newPage, setNewPage] = useState("");
  const [visible, setVisible] = useState(false);
  const [visibleAdd, setVisibleAdd] = useState(false);
  const [visibleAddPage, setVisibleAddPage] = useState(false);
  const [visibleDelete, setVisibleDelete] = useState(false);
  const [visibleDeletePage, setVisibleDeletePage] = useState(false);
  const [nodeNameEn, setNodeNameEn] = useState("");
  const [nodeKey, setNodeKey] = useState("");
  const [pageKey, setPageKey] = useState("");
  const [pageToDelete, setPageToDelete] = useState("");
  const [selectedKeys, setSelectedKeys] = useState("");
  const dispatch = useDispatch();
  const validateMessages = {
    required: "'${name}' is required!",
  };

  function limitTitleFr(data, maxLength) {
    if (data && data.length > maxLength) {
      return (data = data.slice(0, maxLength) + "...");
    } else return data;
  }
  useEffect(() => {
    setSelectedKeys("");
    setSelectedNode(null);
  }, [selectedGroup]);
  const onChangeFamily = (value, tt) => {
    dispatch(
      setFamilyId({
        familyId: value,
        typeFamily: tt.type,
      })
    );
    // setFamily_id(value);
  };
  const filterOption = (input, option) =>
    (option?.label ?? "").toLowerCase().includes(input.toLowerCase());
  const getFolders = () => {
    setLoading(true);
    MainService.getFoldersEmails(selectedGroup, type)
      .then((res) => {
        MainService.getGroupsByFamily(selectedGroup)
          .then((rest) => {
            // getConfigCompanies()

            selectedGroup === "Voip"
              ? setFields([
                  {
                    label: t("menu2.messaging"),
                    fields: [
                      {
                        label: "caller_num",
                        alias: t("emailTemplates.callerNumber"),
                      },
                      { label: "origtime", alias: "Date" },
                      { label: "boite", alias: t("emailTemplates.postNumber") },
                      {
                        label: "duration ",
                        alias: t("emailTemplates.duration"),
                      },
                      // { label: "VM_DATE", alias: "VM_DATE" },
                      // { label: "VM_DUR", alias: "VM_DUR" },
                    ],
                  },
                  fieldsCompany,
                ])
              : selectedGroup === "Visio" || selectedGroup === "Activity"
              ? setFields([
                  {
                    label:
                      selectedGroup === "Visio" ? "Visio" : t("menu1.task"),
                    fields: [
                      { label: "label", alias: t("tasks.tableLabel") },
                      { label: "department_id", alias: t("import.department") },
                      { label: "start_date", alias: t("table.startDate") },
                      { label: "start_time", alias: t("tasks.startTime") },
                      { label: "end_date", alias: t("tasks.tableEndDate") },
                      { label: "end_time", alias: t("tasks.endTime") },
                      { label: "guests", alias: t("tasks.guestsListTitle") },
                      {
                        label: "followers",
                        alias: t("tasks.followersListTitle"),
                      },
                      {
                        label: "exam_id",
                        alias:
                          user.tenant === "spheredev2" ||
                          user.tenant === "taoufikhospitals"
                            ? t("tasks.exams")
                            : t("tasks.products"),
                      },

                      { label: "description ", alias: "Description" },
                      { label: "note", alias: "Note" },
                      { label: "priority ", alias: t("tasks.priority") },
                      { label: "location ", alias: "Location" },
                    ],
                  },
                  fieldsCompany,
                ])
              : Object.keys(fieldsCompany).length > 0
              ? setFields([
                  ...rest.data.data.map((el) => ({ ...el, isField: true })),
                  fieldsCompany,
                ])
              : setFields(rest.data.data);
            let f = [];
            res.data.data.map((doc) => {
              f.push({ ...doc, selectable: false, title: doc.label });
              setExpandedKeys((prevStateArray) => [...prevStateArray, doc.key]);

              f.forEach((item) => {
                item?.children?.forEach((child) => {
                  child.title = child.title || child.subject;
                  // child.icon = <FileOutlined
                  //   className='inline'
                  // />;
                });
              });
            });

            // f = sortFolders(f);

            setFolders(limitTitleFr(f, 13));
            setFolders(f);

            setLoading(false);
          })
          .catch((err) => {
            console.log(err);
            setLoading(false);

            toastNotification("error", t("toasts.somethingWrong"), "topRight");
            //setLoading(false)
          });
      })
      .catch((err) => {
        console.log(err);
        setLoading(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
        //setLoading(false)
      });
  };
  const generateSlug = (str) => {
    const slug = str
      .toLowerCase()
      .trim()
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .replace(/[^a-z0-9]+/g, "_")
      .replace(/(^-|-$)+/g, "");
    return slug;
  };

  const onFinishAdd = () => {
    setLoading(true);
    setVisibleAdd(true);
    let formData = new FormData();
    formData.append("label", newTitle);
    // formData.append(type == 1 ? "family_id" : "family_id", selectedGroup);
    type == 1
      ? formData.append("family_id", selectedGroup)
      : formData.append("family_id", null);
    formData.append("module_system", selectedGroup);

    formData.append("status", 1);
    formData.append("hidden", 1);
    formData.append("field_module_id", 13);

    MainService.addFoldersEmails(formData, selectedGroup, type)
      .then((res) => {
        const newTreeData = [...folders];
        setNodeKey(res?.data?.data[0].key);
        setExpandedKeys([...expandedKeys, res?.data?.data[0].key]);
        newTreeData.push({
          key: res?.data?.data[0].key,
          label: form.getFieldValue().newTitle,
          title: form.getFieldValue().newTitle,
          selected: true,
          selectable: false,
          children: res?.data?.data[0].children,
          status: "0",
        });
        setFolders(newTreeData);
        const parentIndex = newTreeData.findIndex(
          (node) => node.key === res?.data?.data[0].key
        );
        setSelectedNode({
          ...newTreeData[parentIndex].children[0],
          status: "0",
        });
        setSelectedKeys([newTreeData[parentIndex].children[0].key]);
        setVisibleAdd(false);

        setLoading(false);
        setDisabled(true);
      })
      .catch((err) => {
        setLoading(false);
        if (err.response.status === 422) {
          toastNotification(
            "error",
            t(`emailTemplates.${err.response.data.message}`),
            "topRight"
          );
        } else
          toastNotification("error", t("toasts.somethingWrong"), "topRight");
      });
  };
  const updateFolder = async () => {
    let updatedTreeData = [];
    setLoading(true);
    MainService.updateFolderEmail(nodeKey, {
      label: form.getFieldValue().nodeName,
      family_id: selectedGroup,
      field_module_id: 13,
    })
      .then((res) => {
        updatedTreeData = folders.map((node) => {
          if (node.key == nodeKey) {
            return {
              ...node,
              title: form.getFieldValue().nodeName,
            };
          }
          return node;
        });

        const parentIndex = updatedTreeData.findIndex(
          (node) => node.key === nodeKey
        );

        //         updatedTreeData[parentIndex].children = [
        //         ...updatedTreeData[parentIndex].children,
        //           {

        //     slug_url: generateSlug(`a_${form.getFieldValue().nodeName}_a`),
        //           },
        // ]

        // updatedTreeData[parentIndex].children.map((e) => {
        //   return (e.slug_url = generateSlug(
        //     `${value}_${form.getFieldValue().nodeName}_${e.title_fr}`,
        //     (e.folder_title = form.getFieldValue().nodeName)
        //   ));
        // });

        //setFolders(limitTitleFr(updatedTreeData, 13))
        setFolders(updatedTreeData);
        setLoading(false);
      })
      .catch((err) => {
        console.log(err);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      });
  };

  const deleteFolder = async () => {
    setLoading(true);
    MainService.deleteFolder(nodeKey)
      .then((res) => {
        let dataa = folders;

        setFolders(dataa.filter((e) => e.key !== nodeKey));
        setSelectedNode("");
        //return folders
        setLoading(false);
      })
      .catch((err) => {
        console.log(err);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      });
  };

  const deletePage = (pageKey) => {
    setLoading(true);
    const newTreeData2 = [...folders];

    MainService.deletePage(pageKey)
      .then((res) => {
        newTreeData2.map((e) => {
          if (
            e.key == selectedFolder.folder_id
              ? selectedFolder.folder_id
              : selectedFolder.id
          ) {
            return (e.children = e.children.filter((el) => el.id !== pageKey));
          }
        });

        //setFolders(limitTitleFr(newTreeData2, 13))
        setFolders(newTreeData2);

        if (selectedNode.id === pageKey) setSelectedNode("");

        setLoading(false);
      })
      .catch((err) => {
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
        console.log(err);
      });
  };

  const handleOk = () => {
    updateFolder();
    setVisible(false);
  };

  useEffect(() => {
    if (Object.keys(fieldsCompany).length > 0) getFolders(selectedGroup || 4);
  }, [selectedGroup, value, fieldsCompany]);

  const showModal = (nodeKey, nodeName, nodeNameEn) => {
    setVisible(true);
    setNodeKey(nodeKey);
    setNodeName(nodeName);
  };

  const handleCancel = () => {
    setVisible(false);
  };

  const onCancelAdd = () => {
    setVisibleAdd(false);
  };
  const onCancelAddPage = () => {
    setVisibleAddPage(false);
  };
  const onOkAdd = () => {
    onFinishAdd();
  };
  const onOkAddPage = () => {
    onFinish();
  };

  const onCancelDelete = () => {
    setVisibleDelete(false);
  };
  const onOkDelete = () => {
    deleteFolder();
    setVisibleDelete(false);
  };

  const onCancelDeletePage = () => {
    setVisibleDeletePage(false);
  };
  const onOkDeletePage = () => {
    setVisibleDeletePage(false);
    deletePage(pageKey);
  };

  const onInputChangeAdd = (e) => {
    setNewTitle(e.target.value);
  };

  const onInputChangeAddPage = (e) => {
    setNewPage(e.target.value);
  };

  const onSelect = (selectedKeys, event) => {
    setSelectedKeys(selectedKeys);
    setSelectedNode({
      ...event.node,
      bodymail: event.node?.bodymail || "<p><br></p>",
    });
    setDisabled(true);

    // intervalId = setTimeout(() => {
    //   setDisabled(true);
    // }, -100);
    // setTimeout(() => {
    //   setDisabled(true);
    // }, 100);

    // if (event.node.props.data.children) {
    // }

    //else setSelectedNode(event.node.props.data.children[0])
    //console.log(event.node.props)
  };
  // const onExpand = (expandedKeys) => {
  //   setExpandedKeys(expandedKeys)
  // }
  const onExpand = (expandedKeys, { expanded, node }) => {
    setExpandedKeys(
      expanded
        ? expandedKeys.concat(node.props.eventKey)
        : expandedKeys.filter((key) => key !== node.props.eventKey)
    );
  };

  const addParentNode = () => {
    setVisibleAdd(true);
  };

  // const addChildNode = (parentNode) => {
  //   //console.log('parentNode', parentNode.key)
  //   const newTreeData = [...folders]
  //   const parentIndex = newTreeData.findIndex(
  //     (node) => node.key === parentNode.key,
  //   )

  //   newTreeData[parentIndex].children = [
  //     ...parentNode.children,
  //     {
  //       name: `Page ${newTreeData[parentIndex].children.length}`,
  //       title_fr: `Page ${newTreeData[parentIndex].children.length}`,
  //       key: `${parentNode.key}-${newTreeData[parentIndex].children.length}`,
  //       //slugUrl: `/${parentNode.title}/Page ${newTreeData[parentIndex].children.length}`,
  //       slugUrl: parentNode.title,
  //     },
  //   ]
  //   setFolders(newTreeData)
  // }

  const addChildNode = (parentNode) => {
    setVisibleAddPage(true);
    setSelectedFolder(parentNode);
  };
  const onFinish = () => {
    setVisibleAddPage(true);
    const newTreeData = [...folders];
    const parentIndex = newTreeData.findIndex(
      (node) => node.key === selectedFolder.key
    );

    setLoading(true);
    MainService.addTemplateEmail({
      title: form.getFieldValue().newPage,
      folder_id: selectedFolder.key,
      family_id: selectedGroup,
      primary: 0,
      shared: 0,
      status: 0,
    })

      .then((res) => {
        newTreeData[parentIndex].children = [
          ...selectedFolder.children,
          {
            id: res.data.message.id,
            name: newPage,
            title: form.getFieldValue().newPage,

            key: `${res.data.message.folder_id}-${res.data.message.id}`,
            slug_url: generateSlug(
              `${value}_${selectedFolder.title}_${newPage}`
            ),
            folder_id: res.data.message.folder_id,
            status: "0",

            //slugUrl: `/${parentNode.title}/Page ${newTreeData[parentIndex].children.length}`,
            //slug_url: parentNode.title,
          },
        ];
        const childIndex = newTreeData[parentIndex].children.findIndex(
          (node) => node.id == res.data.message.id
        );

        //setFolders(limitTitleFr(newTreeData, 13))
        setFolders(newTreeData);

        form.resetFields();
        setVisibleAddPage(false);
        setSelectedNode({
          ...newTreeData[parentIndex].children[childIndex],
          status: "0",
        });
        console.log(res.data);
        setSelectedKeys([
          res.data.message.folder_id + "-" + res.data.message.id,
        ]);

        // onSelect('select')

        setLoading(false);
        setDisabled(true);
      })

      .catch((err) => {
        setLoading(false);
        console.log(err.response.data.errors);
        if (
          err?.response?.data?.errors[0] ===
          "The title fr has already been taken."
        )
          toastNotification("error", t(`wiki.PageAlreadyExists`), "topRight");
      });
  };
  useEffect(() => {
    form.setFieldsValue({
      nodeName: nodeName,
      nodeNameEn: nodeNameEn,

      //.replace(/\s/g, ''),
    });
  }, [nodeName, nodeNameEn]);

  const renderTitle = (node) => {
    const items = [
      {
        label: t(`wiki.Edit`),
        key: "2",
        icon: <FiEdit className="h-4 w-4 text-slate-400" />,
      },
      {
        label: t(`wiki.Delete`),
        key: "3",
        danger: true,
        disabled:
          node.seeder == 1 || (node.children && node.children.length > 0),
        icon: <FiTrash className="h-4 w-4" />,
      },
    ];

    const onClick = ({ key }) => {
      if (key == 2) {
        showModal(node.key, node.title);
      } else if (key == 3) {
        Confirm(
          `${t(`wiki.Delete`)} "${node.title}" `,
          t(`wiki.Confirm`),
          <RestOutlined style={{ color: "red" }} />,
          function func() {
            return MainService.deleteFolderEmails(node.key)
              .then((res) => {
                setFolders((prev) => prev.filter((e) => e.key !== node.key));
                if (node?.key == selectedNode?.folder_id) setSelectedNode("");
                //return folders
                setLoading(false);
              })
              .catch((err) => {
                console.log(err);
                toastNotification(
                  "error",
                  t("toasts.somethingWrong"),
                  "topRight"
                );
              });
          },
          true
        );
      }
    };

    return (
      <span>
        {node.children && (
          <div>
            {node.seeder == 1 ? (
              <Tooltip title={t("emailTemplates.system")}>
                <Tag color="#2db7f5" className="self-center">
                  S
                </Tag>
              </Tooltip>
            ) : null}
            <span>{limitTitleFr(node.title, 25)} </span>{" "}
            <div style={{ float: "right" }} className="flex">
              <Tooltip
                title={
                  node?.seeder == 1
                    ? t("emailTemplates.cannotAddPage")
                    : t(`wiki.AddPage`)
                }
              >
                <Button
                  size="small"
                  type="link"
                  shape="circle"
                  onClick={(event) => {
                    event.stopPropagation();
                    addChildNode(node);
                    form.resetFields();
                  }}
                  style={{ marginRight: 3 }}
                  disabled={node?.seeder == 1}
                >
                  <PlusOutlined className="h-4 w-4 cursor-pointer text-gray-400 hover:text-gray-700" />
                </Button>
              </Tooltip>

              {/* &nbsp;
              <Tooltip title={t(`wiki.EditFolder`)}>
                <a
                  onClick={(event) => {
                            event.stopPropagation()
                            event.preventDefault()
                            showModal(node.key, node.title_fr, node.title_en)
                  }}
                  style={{marginRight:3}}
                >
                  <EditOutlined />
                </a>
              </Tooltip>
              &nbsp;
              <Tooltip title={t(`wiki.DeleteFolder`)}>
                <a
                  // onClick={(event) => {
                  //           event.stopPropagation()
                  //           event.preventDefault()
                  //           showModalDelete(node.key, node.title_fr)
                  //         }}
                  onClick={(event) => {
                 event.stopPropagation()
                 event.preventDefault()
                    Confirm(
                      `${t(`wiki.Delete`)} "${node.title_fr
                      }" `,
                      t(`wiki.Confirm`),
                      <RestOutlined style={{ color: "red" }} />,
                      function func() {
                        return (MainService.deleteFolder(node.key)
      .then((res) => {
        let dataa = folders
       
        
        setFolders(dataa.filter(e => e.key !== node.key))
        //return folders
        setLoading(false)
      })
      .catch((err) => {
        console.log(err)
      }))
                      },
                      true,
                    )     
            }}

                >
                  <DeleteOutlined />
                </a>
              </Tooltip> */}
              <Dropdown
                trigger={["click"]}
                placement="bottomLeft"
                menu={{
                  items,
                  onClick,
                }}
              >
                <Button
                  size="small"
                  type="link"
                  shape="circle"
                  onClick={(event) => {
                    event.preventDefault();
                    event.stopPropagation();
                  }}
                >
                  <FiMoreVertical className="mt-1 h-4 w-4 cursor-pointer text-gray-400 hover:text-gray-700" />
                </Button>
              </Dropdown>
            </div>
          </div>
        )}
        {!node.children && (
          <div className="group relative">
            <div className="hover:gray-300">
              {/* <FileOutlined /> */}
              &nbsp; &nbsp;
              {node?.seeder == 1 ? (
                <Tooltip title={t("emailTemplates.system")}>
                  <Tag color="#2db7f5" className="self-center">
                    S
                  </Tag>
                </Tooltip>
              ) : null}
              {limitTitleFr(node.title, 25)}&nbsp;
            </div>
            <div className="absolute -top-1 right-0 opacity-0 transition-opacity group-hover:opacity-100">
              <Tooltip
                title={
                  node?.seeder == 1
                    ? t("emailTemplates.cannotDeleteSystem")
                    : ""
                }
                placement="right"
              >
                <Button
                  disabled={node?.seeder == 1 ? true : false}
                  danger
                  type="link"
                  icon={<DeleteOutlined />}
                  onClick={(event) => {
                    event.stopPropagation();
                    event.preventDefault();
                    Confirm(
                      `${t(`wiki.Delete`)} "${node.title}" `,
                      t(`wiki.Confirm`),
                      <RestOutlined style={{ color: "red" }} />,
                      function func() {
                        return MainService.deleteTemplateEmail(node.id)
                          .then((res) => {
                            const newTreeData2 = [...folders];
                            newTreeData2.map((e) => {
                              if (e.key == node.folder_id) {
                                return (e.children = e.children.filter(
                                  (el) => el.id !== node.id
                                ));
                              }
                            });
                            setFolders(newTreeData2);
                            if (selectedNode?.id === node.id)
                              setSelectedNode("");
                          })
                          .catch((err) => {
                            console.log(err);
                            // if (err.response.status === 422) {
                            //   toastNotification(
                            //     "error",
                            //     t("emailTemplates.notDeletePrimaryTemplate"),
                            //     "topRight"
                            //   );
                            // } else
                            //   toastNotification(
                            //     "error",
                            //     t("toasts.somethingWrong"),
                            //     "topRight"
                            //   );
                          });
                      },
                      true
                    );
                  }}
                ></Button>
              </Tooltip>
            </div>
          </div>
        )}
      </span>
    );
  };

  // const renderTreeNodes = (folders) =>
  //   folders.map((item) => {
  //     if (item.page_id) {
  //       return (
  //         <TreeNode title={item.label} key={item.id}>
  //           {renderTreeNodes(item.page_id)}
  //         </TreeNode>
  //       )
  //     }
  //     return <TreeNode title={item.label} key={item.id} />
  //   })

  const handleKeyPressModif = (event) => {
    if (event.key === "Enter") {
      //form.submit();
      handleOk();
    }
  };

  const handleKeyPressAdd = (event) => {
    if (event.key === "Enter") {
      //setVisibleAdd(false)
      //form.submit();
      onOkAdd();
    }
  };

  const handleKeyPressAddPage = (event) => {
    if (event.key === "Enter") {
      //form.submit();
      onOkAddPage();
    }
  };

  const [dragNode, setDragNode] = useState(null);

  const onDragEnter = (info) => {
    // console.log("node",info.node);
    // console.log(info);
    setDragNode(info.node);
    // expandedKeys, set it when controlled is needed
    // setExpandedKeys(info.expandedKeys)
    //conditions: drag only works from folder to folder or to change page or child rank
    //if the drag and drop is between folders
  };

  const onDrop = (info) => {
    // console.log("INFOS ON DROP", info);
    // console.log("folders", folders);

    // console.log("INFO", info);

    const dropKey = info.node.key;
    const dragKey = info.dragNode.key;
    const dropPos = info.node.pos.split("-");
    const dropPosition =
      info.dropPosition - Number(dropPos[dropPos.length - 1]);
    const dragPos = info.dragNode.pos.split("-");
    const dragPosition =
      info.dragPosition - Number(dragPos[dragPos.length - 1]);

    const dropGaps = {
      dragOverGapBottom: info.node.dragOverGapBottom,
      dragOverGapTop: info.node.dragOverGapTop,
    };

    // console.log("DROP GAPS: ", dropGaps);

    // console.log("DROP INFOS", info);
    // console.log(
    //   "Drop position ",
    //   dropPosition,
    //   ", Drag positon ",
    //   dragPosition
    // );
    // console.log("DROP POS", dropPos);
    // console.log("DROP POSITION", dropPosition);
    // console.log("DROP KEY", dropKey);
    // console.log("DRAG KEY", dragKey);
    //same object
    if (dragKey == dropKey) return;
    //from folder to folder
    if (isFolder(dragKey.toString()) && isFolder(dropKey.toString())) {
      setLoading(true);
      let { newTreeData, jsonToReturn } = DragFoldersOrdering(
        dragKey,
        dropKey,
        dropPosition,
        folders
      );
      MainService.updateRankFoldersEmails(jsonToReturn)
        .then((res) => {
          console.log(res);
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
      setFolders(newTreeData);
    }
    //from folder to page
    if (isFolder(dragKey.toString()) && isChild(dropKey.toString())) return;
    //from page to folder
    if (isChild(dragKey.toString()) && isFolder(dropKey.toString())) {
      // console.log("Drag is a child and drop is a folder");
      if (info?.dragNode?.status == 0) {
        return;
      }

      const dragKeyParent = dragKey.split("-")[0];
      const dragKeyParentChildren = folders.find(
        (folder) => folder.key == dragKeyParent
      ).children.length;

      if (dragKeyParentChildren <= 1) {
        return;
      } else {
        // setLoading(true);

        let { newTreeData, dataToSend } = handleDragAndDropFromChildToFolder(
          dragKey,
          dropKey,
          info,
          dropPosition,
          folders
        );
        let formData = new FormData();
        formData.append(
          "folder_id",
          info.node.folder_id || dataToSend.folders[0].folder_id
        );
        formData.append(`rank[${info.dragNode.id}]`, info.dropPosition);
        MainService.EmailsUpdateFolderPagesRanks(dataToSend)
          .then((res) => {
            // console.log(res);
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setLoading(false);
          });

        setFolders(newTreeData);
        // MainService.wikiUpdateFolderPagesRanks(dataToSend)
        //   .then((res) => {
        //     console.log(res);
        //   })
        //   .catch((err) => {
        //     console.log(err);
        //   })
        //   .finally(() => {
        //     setLoading(false);
        //   });

        setFolders(newTreeData);
      }
    }

    if (isChild(dragKey.toString()) && isChild(dropKey.toString())) {
      if (info?.dragNode?.primary == 1) {
        return;
      }

      if (!info.dropToGap) return;
      const dragKeyParent = dragKey.split("-")[0];
      const dragKeyParentChildren = folders.find(
        (folder) => folder.key == dragKeyParent
      ).children.length;
      if (dragKeyParentChildren <= 1) {
        return;
      } else {
        //search for dragKey parent

        //number of children inside dragKey parent
        // handleDragAndDrop(dragKey, dropKey, info, dropPosition);
        let { newTreeData, dataToSend } = changeRankOnDragAndDropChild(
          dragKey,
          dropKey,
          info,
          dropPosition,
          dropGaps,
          folders
        );
        setLoading(true);
        let formData = new FormData();
        formData.append("folder_id", info.node.folder_id);
        formData.append(`rank[${info.dragNode.id}]`, info.dropPosition + 1);

        MainService.EmailsUpdateFolderPagesRanks(dataToSend)
          .then((res) => {})
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setLoading(false);
          });

        setFolders(newTreeData);
      }
    }

    //i want it to prevent making a folder a child of a page
    //if the drop key is a page then return

    // handleDragAndDrop(dragKey, dropKey, info, dropPosition);

    // if(isFolder(dragKey.toString()) && isFolder(dropKey.toString())) {
    //   setFolders(swapFoldersRanks(dragKey, dropKey));
    //   return;
    // }
  };
  return (
    <>
      {selectedGroup && (
        <Spin spinning={loading} size="medium">
          <div
            className="sticky top-0"
            style={{ backgroundColor: "white", zIndex: 99 }}
          >
            <Form.Item label={t("tasks.selectFamily")}>
              <Select
                showSearch
                defaultValue={4}
                placeholder={t("tasks.selectFamily")}
                optionFilterProp="children"
                onChange={onChangeFamily}
                style={{ minWidth: 100, maxWidth: 300 }}
                filterOption={filterOption}
                options={[
                  ...families.map((fam) => ({ ...fam, type: 1 })),
                  ...modules,
                ].map((el) => ({
                  value: el.id,
                  label: el.label,
                  type: el.type,
                }))}
              />
            </Form.Item>
            {selectedGroup ? (
              <GenericButton
                type="primary"
                icon={<PlusOutlined />}
                text={t(`wiki.AddFolder`)}
                //disabled={!selectedGroup || loading}
                onClick={(event) => {
                  event.stopPropagation();
                  form.resetFields();
                  addParentNode();
                }}
              />
            ) : null}
          </div>
          <br></br>
          <br></br>

          {folders.length > 0 ? (
            <>
              <div className="flex">
                <div style={{ height: "100%", width: "100%" }}>
                  <Tree
                    draggable
                    onDragEnter={onDragEnter}
                    onDrop={onDrop}
                    style={{
                      width: "300px",
                      height: "full",
                    }}
                    selectedKeys={selectedKeys}
                    defaultExpandAll
                    showLine={true}
                    defaultExpandParent
                    expandedKeys={expandedKeys}
                    onSelect={onSelect}
                    onExpand={onExpand}
                    treeData={folders}
                    titleRender={renderTitle}
                    showIcon={false}
                    blockNode
                  ></Tree>
                </div>
                <div></div>
              </div>
            </>
          ) : (
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          )}
        </Spin>
      )}
      <Modal
        title={t(`wiki.EditFolder`)}
        open={visible}
        onCancel={handleCancel}
        footer={[
          <div className="space-x-2">
            <Button key="back" onClick={handleCancel}>
              {t(`wiki.Cancel`)}
            </Button>
            <Button
              disabled={form.getFieldValue().nodeName === ""}
              key="submit"
              type="primary"
              loading={loading}
              onClick={handleOk}
            >
              {t(`form.save`)}
            </Button>
          </div>,
        ]}
        className="my-modal"
      >
        {/* <Input
              value={nodeName}
              onChange={(e) => setNodeName(e.target.value)}
              //onKeyPress={handleKeyPressModif}
        />
        <Input
              value={nodeNameEn}
              onChange={(e) => setNodeNameEn(e.target.value)}
              //onKeyPress={handleKeyPressModif}
            /> */}

        <Form
          name="formItemPath"
          layout="vertical"
          onFinish={onFinishAdd}
          validateMessages={validateMessages}
          form={form}
          id="form"
          //onKeyPress={handleKeyPressAdd}
        >
          <Form.Item
            name="nodeName"
            label={t(`wiki.Title`)}
            rules={[
              {
                required: true,
                message: t(`wiki.TitleRequired`),
              },
            ]}
          >
            <Input onChange={(e) => setNodeName(e.target.value)} autoFocus />
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        title={t(`wiki.AddAFolder`)}
        open={visibleAdd}
        onCancel={onCancelAdd}
        // onOk={onOkAdd}
        htmlType="submit"
        footer={[
          <Button key="back" onClick={onCancelAdd}>
            {t(`wiki.Cancel`)}
          </Button>,
          <Button
            disabled={
              form.getFieldValue().newTitle === undefined ||
              form.getFieldValue().newTitle === ""
            }
            key="submit"
            type="primary"
            //disabled={loading}
            loading={loading}
            onClick={onOkAdd}
          >
            {t(`form.create`)}
          </Button>,
        ]}
        className="my-modal"
      >
        <Form
          name="formItemPath"
          layout="vertical"
          onFinish={onFinishAdd}
          validateMessages={validateMessages}
          form={form}
          id="form"
          //onKeyPress={handleKeyPressAdd}
        >
          <Form.Item
            name="newTitle"
            label={t(`wiki.Title`)}
            rules={[
              {
                required: true,
                message: t(`wiki.TitleRequired`),
              },
            ]}
          >
            <Input onChange={onInputChangeAdd} autoFocus />
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        title={
          <span>
            Êtes-vous sûr(e) de vouloir supprimer le dossier <b>{nodeName}</b>
          </span>
        }
        open={visibleDelete}
        onCancel={onCancelDelete}
        // onOk={onOkDelete}
        footer={[
          <Button key="back" onClick={onCancelDelete}>
            Annuler
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={loading}
            onClick={onOkDelete}
          >
            Valider
          </Button>,
        ]}
        className="my-modal"
      ></Modal>
      <Modal
        title={
          <span>
            {t(`wiki.AddPageToFolder`)}
            <b>{selectedFolder.title_fr}</b>
          </span>
        }
        open={visibleAddPage}
        onCancel={onCancelAddPage}
        // onOk={onOkAddPage}
        htmlType="submit"
        footer={[
          <Button key="back" onClick={onCancelAddPage}>
            {t(`wiki.Cancel`)}
          </Button>,
          <Button
            disabled={
              form.getFieldValue().newPage === undefined ||
              form.getFieldValue().newPage === ""
            }
            key="submit"
            type="primary"
            loading={loading}
            onClick={onOkAddPage}
          >
            {t(`form.create`)}
          </Button>,
        ]}
        className="my-modal"
      >
        <Form
          name="formItemPath"
          layout="vertical"
          onFinish={onFinish}
          // onFinishFailed={onFinishFailed}
          validateMessages={validateMessages}
          form={form}
          id="form"
        >
          <Form.Item
            name="newPage"
            label={t(`wiki.TitlePage`)}
            rules={[
              {
                required: true,
                message: t(`wiki.TitleRequired`),
              },
            ]}
          >
            <Input
              onChange={onInputChangeAddPage}
              autoFocus
              //onKeyPress={handleKeyPressAddPage}
            />
          </Form.Item>
        </Form>
      </Modal>
      <Modal
        title={
          <span>
            Êtes-vous sûr(e) de vouloir supprimer la page <b>{pageToDelete}</b>{" "}
            qui appartient au dossier <b>{nodeName}</b>?
          </span>
        }
        open={visibleDeletePage}
        onCancel={onCancelDeletePage}
        // onOk={onOkDeletePage}
        footer={[
          <Button key="back" onClick={onCancelDeletePage}>
            Annuler
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={loading}
            onClick={onOkDeletePage}
          >
            Valider
          </Button>,
        ]}
        className="my-modal"
      ></Modal>
    </>
  );
};

export default SidebarTemplateEmail;
