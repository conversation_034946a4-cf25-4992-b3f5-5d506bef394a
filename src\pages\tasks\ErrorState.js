import React from "react";
import { Button, Typography } from "antd";
import { RedoOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";

const ErrorState = ({ onRetry }) => {
  const [t] = useTranslation("common");

  return (
    <div className="flex w-full flex-col">
      <Typography.Text type="danger" style={{ margin: "5px auto" }}>
        {t("tasks.loadActivitiesError")}
      </Typography.Text>
      <div className="flex justify-center">
        <Button
          danger
          type="primary"
          onClick={onRetry}
          icon={<RedoOutlined />}
          style={{ position: "relative", width: "auto" }}
        >
          {t("tasks.tryAgain")}
        </Button>
      </div>
    </div>
  );
};

export default ErrorState;
