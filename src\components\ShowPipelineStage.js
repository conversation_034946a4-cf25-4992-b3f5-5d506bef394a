import { Col, Form, Row } from "antd";
import React, { useEffect, useState } from "react";
import Pipeline from "./Pipeline";
import Stage from "./Stage";
import { useLocation } from "react-router-dom";
import { SET_CONTACT_INFO_FROM_DRAWER } from "new-redux/constants";
import { useDispatch } from "react-redux";

const ShowPipelineStage = ({ items = [], from = "", keyTab = "" }) => {
  const [form] = Form.useForm();
  const [editingKey, setEditingKey] = useState("");
  const [editingKeyStage, setEditingKeyStage] = useState("");
  const [dataStage, setDataStage] = useState([]);
  const [pipeline_id, setIdPipeline] = useState("");
  const [disabledAddStage, setDisabledAddStage] = useState(false);
  const [dataPipelines, setDataPipelines] = useState([]);
  const [id, setId] = useState(null);
  const [count, setCount] = useState(0);

  const [loadingPipeline, setLoadingPipeline] = useState(true);
  const dispatch = useDispatch();
  const { pathname } = useLocation();
  useEffect(() => {
    setIdPipeline("");
    setEditingKey("");
    setEditingKeyStage("");
    if (
      pathname?.includes("/settings/activity") ||
      pathname?.includes("/settings/pipeline")
    ) {
      dispatch({
        type: "RESET_CONTACT_HEADER_INFO",
      });
      dispatch({
        type: SET_CONTACT_INFO_FROM_DRAWER,
        payload: {},
      });
    }
  }, [keyTab]);

  const handleAdd = () => {
    setId(null);
    const newData = {
      key: count + 1,
      label: `  `,
      color: "",
      department: [],
      disabled: true,
    };

    setDataPipelines([...dataPipelines, newData]);
    form.setFieldsValue({
      label: "",
      department: [],
    });
    setEditingKey(count + 1);
    setCount(count + 1);
  };

  return (
    <Row gutter={8}>
      <Col
        xs={24}
        sm={24}
        md={24}
        lg={10}
        style={{
          overflowY: "hidden",
          minHeight: "350px",
        }}
      >
        <Pipeline
          setEditingKey={setEditingKey}
          editingKey={editingKey}
          setEditingKeyStage={setEditingKeyStage}
          editingKeyStage={editingKeyStage}
          setIdPipeline={setIdPipeline}
          pipeline_id={pipeline_id}
          items={items}
          dataStage={dataStage}
          setDataStage={setDataStage}
          loading={loadingPipeline}
          setLoading={setLoadingPipeline}
          setDisabledAddStage={setDisabledAddStage}
          data={dataPipelines}
          setData={setDataPipelines}
          keyTab={keyTab}
          handleAdd={handleAdd}
          id={id}
          setId={setId}
          count={count}
          setCount={setCount}
          form={form}
          from={from}
        />
      </Col>
      <Col
        xs={24}
        sm={24}
        md={24}
        lg={14}
        style={{
          paddingRight: "8px",
          minHeight: "350px",
        }}
      >
        <Stage
          setEditingKey={setEditingKeyStage}
          editingKey={editingKeyStage}
          pipeline_id={pipeline_id}
          data={dataStage}
          setData={setDataStage}
          loadingPipeline={loadingPipeline}
          disabledAddStage={disabledAddStage}
          dataPipelines={dataPipelines}
          handleAddPipeline={handleAdd}
          editingKeyPipeline={editingKey}
        />
      </Col>
    </Row>
  );
};

export default ShowPipelineStage;
