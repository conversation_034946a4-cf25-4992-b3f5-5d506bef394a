import axios from "axios";
import { toastNotification } from "../components/ToastNotification";
import i18next from "i18next";
import { store } from "../new-redux/store";
import { logoutOut } from "../new-redux/actions/user.actions/getUser";
import { URL_ENV } from "index";

let refreshPromise = null;
const clearPromise = () => (refreshPromise = null);
let logoutOccurence = 0;

// Cache for axios instances
const axiosInstancesCache = new Map();

async function refreshToken() {
  if (
    localStorage.getItem("refreshToken") !== null &&
    localStorage.getItem("refreshToken") !== "null" &&
    localStorage.getItem("refreshToken") !== undefined &&
    localStorage.getItem("refreshToken") !== "undefined"
  ) {
    const response = await axios
      .post(
        URL_ENV?.REACT_APP_OAUTH2_URL +
          process.env.REACT_APP_SUFFIX_API +
          "refresh-token",
        {
          refresh_token: localStorage.getItem("refreshToken"),
        }
      )
      .catch(async (e) => {
        if (logoutOccurence === 0) {
          await store.dispatch(logoutOut());
          logoutOccurence = logoutOccurence + 1;
        }
      });
    localStorage.setItem("accessToken", response?.data?.token?.access_token);
    localStorage.setItem("refreshToken", response?.data?.token?.refresh_token);
    return response?.data?.token?.access_token;
  }
}

export const generateAxios = (baseUrl) => {
  // Return cached instance if available
  if (axiosInstancesCache.has(baseUrl)) {
    return axiosInstancesCache.get(baseUrl);
  }

  // Create new instance if not in cache
  const axiosInstance = axios.create({
    baseURL: baseUrl,
    headers: {
      "Content-type": "multipart/form-data;",
    },
  });

  axiosInstance.interceptors.response.use(undefined, async (error) => {
    const config = error?.config;
    if (
      error?.response?.status === 401 &&
      !config._retry
      //  error?.response?.data?.includes("Unauthenticated") &&
      //  error.config.url !== "/v1/logout"
    ) {
      config._retry = true;

      if (!refreshPromise) {
        refreshPromise = refreshToken().finally(clearPromise);
      }

      try {
        if (
          localStorage.getItem("refreshToken") !== null &&
          localStorage.getItem("refreshToken") !== "null" &&
          localStorage.getItem("refreshToken") !== undefined &&
          localStorage.getItem("refreshToken") !== "undefined"
        ) {
          const token = await refreshPromise;
          if (token) {
            config.headers.authorization = `Bearer ${token}`;
            return axiosInstance(config);
          } else {
            if (logoutOccurence === 0) {
              logoutOccurence = logoutOccurence + 1;
              await store.dispatch(logoutOut());

              return Promise.reject();
            }
          }
        } else {
          // Logging out the user by removing all the tokens from local
          await store.dispatch(logoutOut());

          return Promise.reject();
        }
      } catch (error) {
        toastNotification(
          "error",
          i18next.t("common:toasts.reconnect"),
          "topRight",
          3,
          undefined,
          1,
          "error_401"
        );
        // Logging out the user by removing all the tokens from local
        if (logoutOccurence === 0) {
          logoutOccurence = logoutOccurence + 1;
          await store.dispatch(logoutOut());
        }

        return Promise.reject(error);
      }
    } else return Promise.reject(error);
  });

  axiosInstance.interceptors.request.use(async (config) => {
    const accessToken = localStorage.getItem("accessToken");
    if (
      accessToken !== null &&
      accessToken !== "null" &&
      accessToken !== undefined &&
      accessToken !== "undefined"
    ) {
      config.headers.Authorization = `Bearer ${accessToken}`;
    }

    return config;
  });

  // Cache the instance
  axiosInstancesCache.set(baseUrl, axiosInstance);

  return axiosInstance;
};

// Add a utility function to clear the cache if needed
export const clearAxiosCache = () => {
  axiosInstancesCache.clear();
};