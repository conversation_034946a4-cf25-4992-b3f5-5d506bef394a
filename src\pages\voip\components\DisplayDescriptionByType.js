import { Button, Image, Rate, Space, Tag, Tooltip, Typography } from "antd";
import { extractPhoneNum } from "../helpers/helpersFunc";
import { FiCopy, FiPhoneForwarded } from "react-icons/fi";
import { URL_ENV } from "index";

//

export const copyIcon = (text) => (
  <Typography.Paragraph
    copyable={{
      text: text,
      icon: [
        <FiCopy
          style={{
            color: "rgb(22, 119, 255)",
            marginTop: "2px",
            fontSize: "15px",
          }}
        />,
      ],
    }}
  />
);
//
export const displayDescriptionValue = (data, t, call, familyId, contactId) => {
  //
  const { value, type } = data;

  const pathName = window.location.pathname;
  const isProfile = pathName === "/profile/general";

  switch (type) {
    case "image":
      return (
        <Image
          width={50}
          src={`${
            URL_ENV?.REACT_APP_BASE_URL + URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
          }${value.path}`}
          style={{
            borderRadius: "50%",
            width: 50,
            height: 50,
          }}
        />
      );
    case "album":
      return (
        <Image.PreviewGroup>
          {value.length > 0 &&
            value?.map((item, i) => (
              <Image
                key={i}
                width={50}
                src={`${
                  URL_ENV?.REACT_APP_BASE_URL +
                  URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                }${item.path}`}
              />
            ))}
        </Image.PreviewGroup>
      );
    case "file":
      // console.log(value);
      return (
        <div className="flex flex-col">
          {value?.map((item, i) => (
            <a
              key={i}
              className="truncate"
              href={`${
                URL_ENV?.REACT_APP_BASE_URL +
                URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
              }${item.path}`}
              target="_blank"
              rel="noreferrer"
            >
              {item.file_name}
            </a>
          ))}
        </div>
      );
    case "phone":
      const numberToDisplay = extractPhoneNum(value);
      const number = numberToDisplay
        ?.join()
        ?.replaceAll(",", "")
        ?.replace("+", "00");
      return value ? (
        <Space>
          <span>
            {numberToDisplay?.length === 2
              ? `(${numberToDisplay[0]}) ${numberToDisplay[1]}`
              : numberToDisplay}
          </span>
          {copyIcon(number)}
          <Tooltip title={t("voip.call")}>
            <Button
              onClick={() => call(number, contactId, familyId)}
              icon={<FiPhoneForwarded style={{ fontSize: "15px" }} />}
              type="link"
              size="small"
            />
          </Tooltip>
        </Space>
      ) : (
        ""
      );
    case "email":
      return value ? (
        <Space>
          {value}
          {copyIcon(value)}
          {/* {setReceiverEmail && setOpenEmailModal ? (
            <Tooltip title={t("voip.clickToSendMail")}>
              <Button
                onClick={() => {
                  setReceiverEmail(value);
                  dispatch(setOpenModalEmail(true));
                  setOpenEmailModal(true);
                  setTimeout(() => {
                    dispatch(setOpenEditor({ state: false, type: "" }));
                  }, 1);
                }}
                icon={<SendOutlined style={{ fontSize: "15px" }} />}
                type="link"
                size="small"
              />
            </Tooltip>
          ) : null} */}
        </Space>
      ) : (
        ""
      );
    case "checkbox":
    case "multiselect":
      return (
        Array.isArray(value) &&
        value?.length > 0 && (
          <Space wrap size={[1, 2]}>
            {value?.map((item, i) => (
              // <p key={i}>{item}</p>
              // `${i !== 0 ? ` - ` : ""} ${item}`
              <Tag key={i}>{item}</Tag>
            ))}
          </Space>
        )
      );
    case "radio":
    case "select":
      return <p>{value}</p>;
    case "rate":
      return <Rate disabled allowHalf defaultValue={Number(value)} />;
    case "color":
      return <Tag color={value}>{value}</Tag>;
    case "range":
      return (
        <span className="ellipsis-text">{`${value[0]} -> ${value[1]}`}</span>
      );
    case "link":
      return (
        <>
          <Tooltip title="Open in new tab" placement="top">
            <a href={`${value}`} target="_blank" rel="noreferrer">
              {value}
            </a>
          </Tooltip>
        </>
      );
    case "extension":
      return (
        <Space>
          <span>{value}</span>
          {!isProfile && (
            <Tooltip title={t("voip.call")}>
              <Button
                onClick={() => call(value, contactId, familyId)}
                icon={<FiPhoneForwarded style={{ fontSize: "15px" }} />}
                type="link"
                size="small"
              />
            </Tooltip>
          )}
        </Space>
      );

    case "monetary":
      return <span>{`(${value?.[0]}) ${value?.[1]}`}</span>;

    default:
      return value;
  }
};
