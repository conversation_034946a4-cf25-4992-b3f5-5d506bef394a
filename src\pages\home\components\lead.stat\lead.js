import { Card, Col, Row, Select, Spin, Typography } from "antd";
import { toastNotification } from "components/ToastNotification";
import {
  setPipelinesLead,
  setSelectedPipelineInLead,
} from "new-redux/actions/dashboard.actions";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import MainService from "services/main.service";
import { isGuestConnected } from "utils/role";
import {
  DonutChartWithDrillDown,
  PieChartWithLegend,
} from "../ChartsDashboard";
import {
  getStatByFamily,
  getStatFamilyByField,
} from "pages/home/<USER>";
import CardStat from "../CardStat";

const DashboardLead = ({ start, end, from = "" }) => {
  const dispatch = useDispatch();
  const [t] = useTranslation("common");
  const { i18n } = useTranslation("common");

  const { selectedPipelineInLead: selectedPipeline, pipelinesLead: pipelines } =
    useSelector((state) => state.dashboardRealTime);
  const [loading, setLoading] = useState(false);
  const [leadBySource, setLeadBySource] = useState({});
  const [leadByType, setLeadByType] = useState({});
  const [statsByFamily, setStatsByFamily] = useState({});

  const getStatFamilyByFieldInDeal = async (field) => {
    try {
      const result = await getStatFamilyByField(
        start,
        end,
        9,
        field,
        selectedPipeline
      );
      return result;
    } catch (error) {
      console.error("Error in getStatFamilyByFieldInDeal:", error);
      throw error;
    }
  };
  useEffect(() => {
    const getPipelines = async () => {
      setLoading(true);
      try {
        const response = await MainService.getPipelinesByFamily(9);

        dispatch(
          setPipelinesLead(
            response?.data?.data.map((el) => ({
              value: el.id,
              label: el.label,
            }))
          )
        );
        if (!selectedPipeline?.value) {
          dispatch(
            setSelectedPipelineInLead({
              label: response?.data?.data[0]?.label,
              value: response?.data?.data[0]?.id,
            })
          );
        }
        setLoading(false);
      } catch (error) {
        console.log(`Error ${error}`);
        setLoading(false);
        toastNotification("error", t("toasts.somethingWrong"));
      }
    };
    if (!selectedPipeline?.value) getPipelines();
  }, [selectedPipeline?.value, t]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await getStatFamilyByFieldInDeal(90);
        setLeadBySource(res.data?.data);
      } catch (err) {
        console.log(err);
      }
    };
    if (selectedPipeline?.value && !isGuestConnected()) {
      fetchData();
    }
  }, [start, end, i18n.language, selectedPipeline.value]);
  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await getStatFamilyByFieldInDeal(91);
        setLeadByType(res.data?.data);
      } catch (err) {
        console.log(err);
      }
    };
    if (selectedPipeline?.value && !isGuestConnected()) {
      fetchData();
    }
  }, [start, end, i18n.language, selectedPipeline.value]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await getStatByFamily(
          start,
          end,
          9,
          selectedPipeline?.value
        );
        setStatsByFamily({
          ...res.data.data,
          drilldown: {
            series: Object.keys(res.data.data.drilldown).map((pipeline) => ({
              id: pipeline,
              data: res.data.data.drilldown[pipeline].data, // Drilldown data
            })),
          },
        });
      } catch (err) {
        console.log(err);
      }
    };
    if (selectedPipeline?.value && !isGuestConnected()) {
      fetchData();
    }
  }, [start, end, i18n.language, selectedPipeline.value]);

  return (
    <Spin spinning={loading}>
      <Card
        style={{
          // background: "#F8FAFC",
          border: 0,
        }}
        styles={{ body: { padding: "6px 0px" } }}
        title={
          <div className="mb-3 flex max-w-[350px] flex-col  justify-start gap-y-0.5">
            <Typography.Text>
              {t("emailTemplates.plsSelect")} pipeline
            </Typography.Text>
            <Select
              showSearch
              popupMatchSelectWidth={false}
              filterOption={(input, option) =>
                (option?.label ?? "")
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
              style={{ minWidth: 250 }}
              onChange={(value, data) => {
                dispatch(
                  setSelectedPipelineInLead({
                    label: data.label,
                    value: data.value,
                  })
                );
              }}
              value={selectedPipeline?.value}
              options={pipelines}
            />
          </div>
        }
      >
        <div
          style={{
            height: `calc(100vh - ${
              from.includes("drawer") ? "164" : "255"
            }px)`,
            overflowY: "auto",
            overflowX: "hidden",
          }}
        >
          <Row gutter={[16, 16]}>
            <Col span={from === "drawer" ? 12 : 8}>
              <CardStat
                title={t("dashboard.moduleBySource", {
                  name: t("menu1.leads"),
                })}
              >
                <PieChartWithLegend
                  data={{
                    ...leadBySource,
                    name: "",
                  }}
                  withArrow={true}
                />
              </CardStat>
            </Col>
            <Col span={from === "drawer" ? 12 : 8}>
              <CardStat
                title={t("dashboard.moduleByType", {
                  name: t("menu1.leads"),
                })}
              >
                <PieChartWithLegend
                  data={{
                    ...leadByType,
                    name: "",
                  }}
                  withArrow={true}
                />
              </CardStat>
            </Col>
            <Col span={from === "drawer" ? 12 : 8}>
              <CardStat
                title={statsByFamily?.name}
                subtitle={
                  statsByFamily?.parent_name !== undefined &&
                  statsByFamily?.series?.find(
                    (el) => el.name === selectedPipeline.label
                  )?.y > 0
                    ? statsByFamily?.parent_name +
                      " " +
                      statsByFamily?.series?.find(
                        (el) => el.name === selectedPipeline.label
                      )?.y
                    : ""
                }
              >
                <DonutChartWithDrillDown
                  data={{ ...statsByFamily, name: "" }}
                  subtitle={t("dashboard.independantFilter", {
                    name: "pipeline",
                  })}
                  selected={selectedPipeline.label}
                />
              </CardStat>
            </Col>
          </Row>
        </div>
      </Card>
    </Spin>
  );
};

export default DashboardLead;
