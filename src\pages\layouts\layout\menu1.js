import {
  useEffect,
  useMemo,
  useCallback,
  lazy,
  Suspense,
  useState,
  useRef,
} from "react";
import { DollarOutlined, InboxOutlined, TeamOutlined } from "@ant-design/icons";
import {
  MdKeyboardDoubleArrowUp,
  MdKeyboardDoubleArrowDown,
} from "react-icons/md";
import { Badge, Button, Image, Menu } from "antd";
import { useTranslation } from "react-i18next";

import {
  HiOutlineChatBubbleBottomCenterText,
  HiOutlinePhone,
  HiOutlineUserGroup,
  HiOutlineVideoCamera,
  HiOutlineShare,
  HiOutlineCog8Tooth,
  HiOutlineChartBar,
  HiOutlineEnvelope,
  HiOutlineCalendar,
  HiOutlineBuildingOffice,
  HiDevicePhoneMobile,
} from "react-icons/hi2";
import { NavLink } from "react-router-dom";
import { useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { setPathName } from "../../../new-redux/actions/form.actions/form";

import $ from "jquery";
import { memo } from "react";
import {
  isOverviewModalOpen,
  setNewIncomingTaskNotification,
} from "../../../new-redux/actions/tasks.actions/realTime";
import { useInView } from "react-intersection-observer";
import { LuPalmtree } from "react-icons/lu";
import { CgUserlane } from "react-icons/cg";
import { lazyRetry } from "../../../utils/lazyRetry";
import {
  setAccountData,
  setFetchedAccount,
  setHasNewMessage,
  setLoadingAccount,
  setOpenModalEmail,
  setSearchEmail,
} from "../../../new-redux/actions/mail.actions";
import { HiOutlinePencilAlt } from "react-icons/hi";

import { SideBarChatPopover } from "../chat/sidebar/SideBarChatPopover";
import { GrTransaction } from "react-icons/gr";
import { setNotifRmc } from "new-redux/actions/rmc.actions";
import { isGuestConnected, roles } from "utils/role";
import { setOpenTaskDrawer } from "new-redux/actions/tasks.actions/handleTaskDrawer";
import { setOpenTaskRoomDrawer } from "new-redux/actions/tasks.actions/handleTaskRoomDrawer";
import { setOpenFieldDrawer } from "new-redux/actions/fields.actions/fieldDrawer";
import { getLabelNameById } from "pages/rmc/mailing/helpers/helpers";
import { Blocks, HeartHandshake, HardDrive } from "lucide-react";
import QrCode from "../../../assets/qrcodeappmobile.webp";
import TicketIconSphere from "components/icons/TicketIconSphere";
import MainService from "services/main.service";
import { setSelectedAccountInEmail } from "new-redux/actions/dashboard.actions";
//
//
//
const ChatWithColleague = lazy(() =>
  lazyRetry(
    () => import("../../../pages/voip/components/ChatWithColleague.js"),
    "ChatWithColleague"
  )
);
const FormCreate = lazy(() =>
  lazyRetry(
    () => import("../../clients&users/components/FormCreate"),
    "FormCreate"
  )
);

const CreateTask = lazy(
  () => import("../../voip/components/CreateTask"),
  "CreateTask"
);

export function getItem(
  label,
  key,
  icon,
  children,
  type,
  disabled = false,
  theme = "light"
) {
  return {
    key,
    icon,
    children,
    label,
    type,
    disabled,
    theme,
  };
}
export function changeFavicon(path_ico) {
  const head = document.head || document.getElementsByTagName("head")[0];
  const oldFavicon = document.getElementById("favicon");

  if (oldFavicon) {
    head.removeChild(oldFavicon);
  }

  const newFavicon = document.createElement("link");
  newFavicon.id = "favicon";
  newFavicon.rel = "icon";
  newFavicon.href = path_ico;

  head.appendChild(newFavicon);
}

export let menuItems = [];

const guestAllowedModules = ["chat", "visio", "notes", "tasks", "tickets"];

const Menu1 = () => {
  const [isGuest, setIsGuest] = useState(false);
  const [openTour, setOpenTour] = useState(false);
  const { ref: topScroll, inView: inViewTop } = useInView({
    threshold: 0.7,
  });
  const { ref: bottomScrollRef, inView: inViewBottom } = useInView({
    threshold: 0.7,
  });
  const dispatch = useDispatch();
  const refChat = useRef(null);
  const refVoip = useRef(null);
  const [t] = useTranslation("common");
  const { pathname } = useLocation();
  const openDrawerChatWithColleague = useSelector(
    ({ voip }) => voip.openDrawerChat
  );
  const rmc = useSelector((state) => state.rmc);
  const totalNotificationChat = useSelector(
    (state) => state.chat.totalNotificationChat
  );
  const { newIncomingNotification, totalNotificationNumber, remindersList } =
    useSelector((state) => state?.TasksRealTime);
  const { nbrMissedCalls, nbrVoiceMessaging, nbrMissedQueueGroup } =
    useSelector(({ voip }) => voip);
  const { user } = useSelector((state) => state.user);
  const chatUser = useSelector((state) => state.chat?.currentUser?.role);
  const { notificationCount, countReminders } = useSelector(
    (state) => state.visioList
  );
  const notesNotificationsCount = useSelector(
    (state) => state?.selfNotesReducer?.notificationsCount
  );
  const { notificationMailCount, dataAccounts } = useSelector(
    (state) => state.mailReducer
  );
  const usedAccount = useMemo(() => {
    return dataAccounts?.find((item) => item.selected) || dataAccounts?.[0];
  }, [dataAccounts.length]);

  const getTitle = useCallback(
    (title) => {
      document.title =
        (totalNotificationChat?.numberConversationChat > 0
          ? "(" + totalNotificationChat?.numberConversationChat + ") "
          : "") + title;
    },
    [totalNotificationChat?.numberConversationChat]
  );
  useEffect(() => {
    const timer = setTimeout(() => {
      setOpenTour(true);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  /////// Hassine Basla: just added this chedden yed 7atta ta7dher l'interface jdida
  const getAccounts = async () => {
    dispatch(setLoadingAccount(true));
    try {
      const response = await MainService.getAccounts();
      let notifs = await MainService.getNotifcationMailingByUser();
      if (response?.status === 200) {
        let data = response?.data?.map((item) => ({
          label: item.email,
          value: item.id,
          primary: item.primary_account,
          shared: item.shared,
          sync: item.sync,
          creator: item.creator,
          selected: !!item.selected,
          departmentId: item.departement_id,
          dispatcheur: item.dispatcheur,
          alias: item.alias ?? [],
          labels: item.labels,
          default_signature: item.default_signature,
          notification_status: notifs?.data?.find(
            (el) => el.account_id === item.id
          )?.notification_status,
        }));
        dispatch(setAccountData(data));
        Array.isArray(response?.data) &&
          response?.data.filter((el) => el.shared === "1").length > 0 &&
          dispatch(
            setSelectedAccountInEmail(
              response?.data.filter((el) => el.shared === "1")[0]?.id
            )
          );
      }
    } catch (err) {
      dispatch(setLoadingAccount(false));
      dispatch(setFetchedAccount(false));
      console.log(err);
    }
  };
  //////

  const directory = ["colleague", "phonebook", "groups"];
  const contact_array = ["companies", "contact", "leads"];
  const divider2 = [
    "rmc",
    "deals",
    "ticket",
    "projects",
    "booking",
    "products",
  ];
  const divider3 = ["rmc"];
  menuItems = useMemo(
    () => [
      user?.access?.["chat"] === "1" &&
        getItem(
          // t("menu1.chat"),
          "",
          "chat",
          <span className={`${openTour ? "py-0.5" : "py-0"}`} ref={refChat}>
            <SideBarChatPopover t={t}>
              <NavLink
                ref={topScroll}
                to="/chat"
                onClick={() => {
                  dispatch(setPathName("/chat"));
                  getTitle(t("menu1.chat"));
                }}
              >
                <HiOutlineChatBubbleBottomCenterText
                  style={{ fontSize: "21px", marginTop: 8 }}
                />

                <Badge
                  size="small"
                  overflowCount={10}
                  style={{
                    maxWidth: "20px",
                    display: "flex",
                    justifyContent: "center",
                    //  alignItems: "center",
                  }}
                  count={totalNotificationChat?.numberConversationChat}
                  offset={[-5, -22]}
                  // offset={[-4, -16]}
                ></Badge>
              </NavLink>
            </SideBarChatPopover>
          </span>
        ),

      user?.access?.["logs"] === "1" &&
        getItem(
          t("menu2.logs"),
          "telephony",
          <NavLink
            to="/telephony/callLog"
            ref={user?.access?.["chat"] !== "1" ? topScroll : null}
            className={"navigation-icon"}
            onClick={() => {
              dispatch(setPathName("/logs"));
              getTitle(`${t("menu1.voip")} |` + t("menu2.logs"));
            }}
          >
            <HiOutlinePhone style={{ fontSize: "21px" }} />
            <Badge
              size="small"
              overflowCount={10}
              style={{
                maxWidth: "20px",
                display: "flex",
                justifyContent: "center",
                //  alignItems: "center",
              }}
              count={nbrMissedCalls + nbrVoiceMessaging + nbrMissedQueueGroup}
              offset={[-4, -16]}
            ></Badge>
          </NavLink>
        ),

      user?.access?.["visio"] === "1" &&
        getItem(
          t("menu1.visio"),
          "visio",
          <NavLink
            to="/visio"
            className={"navigation-icon"}
            onClick={() => {
              dispatch(setPathName("/visio"));
              getTitle(t("menu1.visio"));
            }}
          >
            <HiOutlineVideoCamera style={{ fontSize: "21px" }} />
            {Number(notificationCount) + Number(countReminders) ? (
              <Badge
                size="small"
                overflowCount={10}
                style={{
                  maxWidth: "20px",
                  display: "flex",
                  justifyContent: "center",
                }}
                dot={Number(notificationCount) + Number(countReminders)}
                offset={[-4, -10]}
              />
            ) : null}
          </NavLink>
        ),

      user?.access?.["notes"] === "1" &&
        getItem(
          t("menu1.notes"),
          "notes",
          <NavLink
            to="/notes"
            className={"navigation-icon"}
            onClick={() => {
              dispatch(setPathName("/notes"));
              getTitle(t("menu1.notes"));
            }}
          >
            <HiOutlinePencilAlt style={{ fontSize: "21px" }} />
            {/* <Badge
              size="small"
              overflowCount={10}
              style={{
                maxWidth: "20px",
                display: "flex",
                justifyContent: "center",
                //  alignItems: "center",
              }}
              count={notificationCount}
              offset={[-4, -16]}
            /> */}
            {Number(notesNotificationsCount) > 0 ? (
              <Badge
                size="small"
                overflowCount={10}
                style={{
                  maxWidth: "20px",
                  display: "flex",
                  justifyContent: "center",
                  //  alignItems: "center",
                }}
                dot={true}
                offset={[-4, -10]}
              />
            ) : null}
          </NavLink>
        ),

      user?.access?.["email"] === "1" &&
        getItem(
          t("menu1.mailing"),
          "mailing",
          <NavLink
            to={"/mailing"}
            className={"navigation-icon"}
            onClick={() => {
              dispatch(setHasNewMessage(0));
              if (usedAccount?.value) {
                dispatch(setPathName(`/mailing/${usedAccount?.value}/inbox`));
              } else {
                dispatch(setPathName(`/mailing`));
              }
              dispatch(setOpenModalEmail(false));
              $("title").text(
                (totalNotificationChat?.numberConversationChat > 0
                  ? "(" + totalNotificationChat?.numberConversationChat + ") "
                  : "") + t("menu1.mailing")
              );
              getAccounts();
            }}
          >
            <HiOutlineEnvelope style={{ fontSize: "21px" }} />
            <Badge
              size="small"
              overflowCount={10}
              style={{
                maxWidth: "20px",
                display: "flex",
                justifyContent: "center",
                //  alignItems: "center",
              }}
              dot={notificationMailCount?.hasUnread}
              offset={[-4, -10]}
            ></Badge>
          </NavLink>
        ),

      user?.access?.["activities"] === "1" &&
        getItem(
          t("menu1.tasks"),
          "tasks",
          <NavLink
            to="/tasks"
            className={"navigation-icon"}
            onClick={() => {
              dispatch(setPathName("/tasks"));
              dispatch(setNewIncomingTaskNotification(false));

              getTitle(t("menu1.tasks"));
            }}
          >
            <HiOutlineCalendar style={{ fontSize: "21px" }} />
            <Badge
              dot={totalNotificationNumber + remindersList?.meta?.total > 0}
              size="small"
              overflowCount={10}
              style={{
                maxWidth: "20px",
                display: "flex",
                justifyContent: "center",
              }}
              offset={[-4, -10]}
              count={totalNotificationNumber}
            ></Badge>
          </NavLink>
        ),
      user?.access?.["drive"] === "1" &&
        getItem(
          t("menu1.drive"),
          "drive",
          <NavLink
            to="/drive"
            className={"navigation-icon"}
            onClick={() => {
              dispatch(setPathName("/drive"));

              getTitle(t("menu1.drive"));
            }}
          >
            <HardDrive size={22} />
          </NavLink>
        ),
      // directory.find((item) => user?.access?.[item] === "1") &&
      //   getItem(
      //     <span className="text-xs font-semibold ">{t("voip.directory")}</span>,
      //     "directory",
      //     <NavLink
      //       to="/directory"
      //       className={"navigation-icon"}
      //       onClick={() => {
      //         dispatch(setPathName("/directory"));
      //         dispatch(setNewIncomingTaskNotification(false));

      //         getTitle(t("voip.directory"));
      //       }}
      //     >
      //       <FiBookOpen style={{ fontSize: "21px" }} />
      //     </NavLink>
      //   ),

      contact_array.find((item) => user?.access?.[item] === "1") &&
        getItem(
          t("menu1.contacts"),
          "contacts_1",
          <NavLink className={"navigation-icon"}>
            <span className="flex flex-1 items-center">
              <HiOutlineUserGroup style={{ fontSize: "22px" }} />
            </span>
          </NavLink>,
          [
            user?.access?.["companies"] === "1" &&
              getItem(
                <span className="text-xs font-semibold ">
                  {user.tenant === "spheredev2" ||
                  user.tenant === "taoufikhospitals"
                    ? t("tasks.medicalStaff")
                    : t("menu1.companies")}
                </span>,
                "companies",
                <NavLink
                  to="/companies"
                  className={"navigation-icon"}
                  onClick={() => {
                    dispatch(setPathName("/companies"));

                    getTitle(
                      t("menu1.contacts") + " | " + t("menu1.companies")
                    );
                  }}
                >
                  <HiOutlineBuildingOffice style={{ fontSize: "16px" }} />
                </NavLink>
              ),

            user?.access?.["contact"] === "1" &&
              getItem(
                <span className="text-xs font-semibold ">
                  {user.tenant === "spheredev2" ||
                  user.tenant === "taoufikhospitals"
                    ? t("tasks.patients")
                    : t("menu1.contacts")}
                </span>,
                "contacts",
                <NavLink
                  to="/contacts"
                  className={"navigation-icon"}
                  onClick={() => {
                    dispatch(setPathName("/contacts"));

                    getTitle(t("menu1.contacts") + " | " + t("menu1.contacts"));
                  }}
                >
                  <TeamOutlined style={{ fontSize: "16px" }} />
                </NavLink>
              ),

            user?.access?.["leads"] === "1" &&
              getItem(
                <span className="text-xs font-semibold ">
                  {t("menu1.leads")}
                </span>,

                "leads",
                <NavLink
                  to="/leads"
                  className={"navigation-icon"}
                  onClick={() => {
                    dispatch(setPathName("/leads"));
                    getTitle(t("menu1.contacts") + " | " + t("menu1.leads"));
                  }}
                >
                  <CgUserlane style={{ fontSize: "16px" }} />
                </NavLink>
              ),
          ]
        ),

      divider2.find((item) => user?.access?.[item] === "1") && {
        type: "divider",
      },
      process.env.REACT_APP_BRANCH.includes("dev") &&
        getItem(
          t("menu1.interactions"),
          "interactions",
          <NavLink
            to="/interactions"
            className={"navigation-icon"}
            onClick={() => {
              dispatch(setPathName("/interactions"));
              getTitle(t("menu1.interactions"));
            }}
          >
            <InboxOutlined style={{ fontSize: "21px" }} />
          </NavLink>
        ),
      user?.access?.["rmc"] === "1" || user?.rmc_access === "OUI"
        ? user?.email === "<EMAIL>"
          ? getItem(
              t("menu1.rmc"),
              "rmc",
              <NavLink className={"navigation-icon"}>
                <HiOutlineShare style={{ fontSize: "21px" }} />
              </NavLink>
            )
          : getItem(
              t("menu1.rmc"),
              "rmc",
              <NavLink
                to="/rmc"
                className={"navigation-icon"}
                onClick={() => {
                  dispatch(setPathName("/rmc"));
                  dispatch(setNotifRmc(false));
                  getTitle(t("menu1.rmc"));
                }}
              >
                <HiOutlineShare style={{ fontSize: "21px" }} />
                <Badge
                  size="small"
                  overflowCount={10}
                  style={{
                    maxWidth: "20px",
                    display: "flex",
                    justifyContent: "center",
                    // alignItems: "center",
                  }}
                  dot={rmc.notif}
                  offset={[-4, -10]}
                />
              </NavLink>
            )
        : "",

      user?.access?.["deals"] === "1" &&
        getItem(
          user?.tenant === "spheredev2" || user.tenant === "taoufikhospitals"
            ? t("menu2.folders")
            : t("menu1.deals"),
          "deals",
          <NavLink
            to="/deals"
            className={"navigation-icon"}
            onClick={() => {
              dispatch(setPathName("/deals"));

              getTitle(t("menu1.deals"));
            }}
          >
            <HeartHandshake size={22} />
          </NavLink>
        ),

      user?.access?.["ticket"] === "1" &&
        getItem(
          t("menu2.ticket"),
          "tickets",
          <NavLink
            to="/tickets"
            className={"navigation-icon"}
            onClick={() => {
              dispatch(setPathName("/tickets"));

              getTitle(t("menu2.ticket"));
            }}
          >
            <TicketIconSphere size={26} />
          </NavLink>
        ),

      user?.access?.["projects"] === "1" &&
        getItem(
          t("menu1.projects"),
          "projects",
          <NavLink
            to="/projects"
            className={"navigation-icon"}
            onClick={() => {
              dispatch(setPathName("/projects"));

              getTitle(t("menu1.projects"));
            }}
          >
            <Blocks size={22} />
          </NavLink>
        ),

      // user?.access?.["products"] === "1" &&
      //   getItem(
      //     user.tenant === "spheredev2" || user.tenant === "taoufikhospitals"
      //       ? t("tasks.exams")
      //       : t("menu2.products"),
      //     "products",
      //     <NavLink
      //       to="/settings/products"
      //       className={"navigation-icon"}
      //       onClick={() => {
      //         dispatch(setPathName("/settings/products"));

      //         getTitle(t("menu2.products"));
      //       }}
      //     >
      //       <AiOutlineShoppingCart style={{ fontSize: "21px" }} />
      //     </NavLink>
      //   ),

      process.env.REACT_APP_BRANCH !== "prod" &&
        user?.access?.["invoices"] === "1" &&
        getItem(
          t("menu2.invoices"),
          "invoices",
          <NavLink
            to="/invoices"
            className={"navigation-icon"}
            onClick={() => {
              dispatch(setPathName("/invoices"));

              getTitle(t("menu2.invoice"));
            }}
          >
            <DollarOutlined style={{ fontSize: "23px" }} />
          </NavLink>
        ),

      process.env.REACT_APP_BRANCH !== "prod" &&
        user?.access?.["transactions"] === "1" &&
        getItem(
          "Transactions",
          "transactions",
          <NavLink
            to="/transactions"
            className={"navigation-icon"}
            onClick={() => {
              dispatch(setPathName("/transactions"));

              getTitle("Transactions");
            }}
          >
            <GrTransaction style={{ fontSize: 22 }} />
          </NavLink>
        ),

      user?.access?.["booking"] === "1" &&
        getItem(
          t("menu1.booking"),
          "booking",
          <NavLink
            to="/booking"
            className={"navigation-icon"}
            onClick={() => {
              dispatch(setPathName("/booking"));

              getTitle(t("menu1.booking"));
            }}
          >
            <LuPalmtree style={{ fontSize: "23px" }} />
          </NavLink>
        ),
      divider3.find((item) => user?.access?.[item] === "1") && {
        type: "divider",
      },

      // process.env.REACT_APP_BRANCH !== "prod" &&
      getItem(
        t("menu1.stats"),
        "stats",
        <NavLink
          to="/stats/families"
          className={"navigation-icon"}
          onClick={() => {
            dispatch(setPathName("/stats/families"));

            getTitle(t("menu1.stats"));
          }}
        >
          <HiOutlineChartBar style={{ fontSize: "21px" }} />
        </NavLink>
      ),
      {
        type: "divider",
      },
      getItem(
        <div className="flex flex-col space-y-0.5">
          <Image src={QrCode} width={162} />
          <span className="text-gray-200">{t("menu1.scanMobileApp")}</span>
        </div>,
        "appMobile",
        <NavLink
          to="profile/appMobile"
          className={"navigation-icon active"}
          onClick={() => {
            dispatch(setPathName("profile/appMobile"));

            getTitle(t("menu1.appMobile"));
          }}
        >
          <HiDevicePhoneMobile
            style={{ fontSize: "21px", position: "absolute", top: 11 }}
          />
        </NavLink>
      ),
      getItem(
        t("menu1.settings"),
        "settings",
        <NavLink
          id="settingId"
          ref={bottomScrollRef ?? null}
          to={
            roles.includes(user?.role)
              ? "/settings/general/companies"
              : "/settings/tags"
          }
          className={"navigation-icon flex items-center"}
          onClick={() => {
            dispatch(
              setPathName(
                roles.includes(user?.role)
                  ? "/settings/general/companies"
                  : "/settings/tags"
              )
            );

            getTitle(t("menu1.settings"));
          }}
        >
          <HiOutlineCog8Tooth style={{ fontSize: "21px", marginTop: 10 }} />
        </NavLink>
      ),
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      dispatch,
      t,
      pathname,
      totalNotificationChat?.numberConversationChat,
      notesNotificationsCount,
      user?.email,
      newIncomingNotification,
      totalNotificationNumber,
      remindersList?.meta?.total,
      nbrMissedCalls,
      nbrVoiceMessaging,
      nbrMissedQueueGroup,
      notificationCount,
      countReminders,
      notificationMailCount?.hasUnread,
      rmc.notif,
      usedAccount?.id,
    ]
  );

  // useEffect(() => {
  //   const getAccounts = async () => {
  //     dispatch(setLoadingAccount(true));
  //     try {
  //       const response = await MainService.getAccounts();
  //       if (response?.status === 200) {
  //         let data = response?.data?.data.map((item) => ({
  //           label: item.email,
  //           value: item.id,
  //           primary: item.primary_account,
  //           shared: item.shared,
  //           sync: item.sync,

  //           selected: item.selected,
  //           departmentId: item.departement_id,
  //           dispatcheur: item.dispatcheur,
  //         }));
  //         dispatch(setAccountData(data));
  //       }
  //     } catch (err) {
  //       dispatch(setLoadingAccount(false));
  //       dispatch(setFetchedAccount(false));
  //       console.log(err);
  //     }
  //   };

  //   if (pathname.includes("dashboard") && !fetchedAccount) {
  //     getAccounts();
  //   }
  // }, [fetchedAccount, pathname, dispatch]);

  useEffect(() => {
    let pageTitle = null;
    switch (pathname.split("/")[1]) {
      case "dashboard":
        pageTitle = "Sphere - " + t("menu1.dashboard");
        break;
      case "chat":
        pageTitle = `${t("menu1.chat")}`;

        break;
      case "logs":
        pageTitle = `${t("menu1.voip")} | ${t("menu2.logs")}`;
        break;
      case "companies":
        pageTitle = `${t("menu1.contacts")} | ${t("menu1.companies")} `;
        break;
      case "contacts":
        pageTitle = `${t("menu1.contacts")} | ${t("menu1.contacts")}`;
        break;
      case "leads":
        pageTitle = `${t("menu1.contacts")} | ${t("menu1.leads")} `;
        break;
      case "directory":
        pageTitle = t("voip.directory");
        // if (pathname.split("/")[2] === "colleagues") {
        //   pageTitle = t("voip.directory") + ` |  ${t("menu2.colleagues")}`;
        // } else if (pathname.split("/")[2] === "groups") {
        //   pageTitle = t("voip.directory") + ` | ${t("menu2.groups")}`;
        // } else if (pathname.split("/")[2] === "phone-book") {
        //   pageTitle = t("voip.directory") + ` | ${t("menu2.phoneBook")}`;
        // }
        break;
      case "settings":
        if (pathname.split("/")[2] === "general") {
          pageTitle = `${t("menu1.settings")} | ${t("menu2.general")}`;
        } else if (pathname.split("/")[2] === "fields") {
          pageTitle = `${t("menu1.settings")} | ${t("menu2.fields")}`;
        } else if (pathname.split("/")[2] === "activity") {
          pageTitle = `${t("menu1.settings")} | ${t("menu2.activity")}`;
        } else if (pathname.split("/")[2] === "tags") {
          pageTitle = `${t("menu1.settings")} | ${t("menu2.tags")}`;
        } else if (pathname.split("/")[2] === "emailAccounts") {
          pageTitle = `${t("menu1.settings")} | ${t("menu2.emailaccounts")}`;
        } else if (pathname.split("/")[2] === "rmc") {
          pageTitle = `${t("menu1.settings")} | ${t("menu2.rmcSettings")}`;
        } else if (pathname.split("/")[2] === "users") {
          pageTitle = `${t("menu1.settings")} | ${t("menu2.users")}`;
        } else if (pathname.split("/")[2] === "guests") {
          pageTitle = `${t("menu1.settings")} | ${t("users.guests")}`;
        } else if (pathname.split("/")[2] === "helpDesk") {
          pageTitle = `${t("menu1.settings")} | ${t(
            "menu2.helpdeskWithoutFields"
          )}`;
        } else if (pathname.split("/")[2] === "pipeline") {
          pageTitle = `${t("menu1.settings")} | ${t("menu2.pipeline")}`;
        } else if (pathname.split("/")[2] === "wiki") {
          pageTitle = `${t("menu1.settings")} | ${t("menu2.wiki")}`;
        } else if (pathname.split("/")[2] === "sales") {
          pageTitle = `${t("menu1.settings")} | ${t("menu2.sales")}`;
        } else if (pathname.split("/")[2] === "import") {
          pageTitle = `${t("menu1.settings")} | ${t("menu2.import")}`;
        } else if (pathname.split("/")[2] === "checklist") {
          pageTitle = `${t("menu1.settings")} | ${t("menu2.checklist")}`;
        } else if (pathname.split("/")[2] === "triggers") {
          pageTitle = `${t("menu1.settings")} | ${t("menu2.triggers")}`;
        } else if (pathname.split("/")[2] === "log-action") {
          pageTitle = `${t("menu1.settings")} | ${t("menu2.log-action")}`;
        } else if (pathname.split("/")[2] === "unavailability") {
          pageTitle = `${t("menu1.settings")} | ${t("menu2.unavailability")}`;
        } else if (pathname.split("/")[2] === "contacts-types") {
          pageTitle = `${t("menu1.settings")} | ${t("menu2.contacts-types")}`;
        } else if (pathname.split("/")[2] === "products") {
          pageTitle = `${t("menu1.settings")} | ${t("menu2.products")}`;
        } else if (pathname.split("/")[2] === "emailTemplates") {
          pageTitle = `${t("menu1.settings")} | ${t("menu2.emailTemplates")}`;
        } else if (pathname.split("/")[2] === "folders") {
          pageTitle = `${t("menu1.settings")} | ${t("menu2.folders")}`;
        } else if (pathname.split("/")[2] === "tour") {
          pageTitle = `${t("menu1.settings")} | ${t("menu2.tour")}`;
        } else if (pathname.split("/")[2] === "integrations") {
          pageTitle = `${t("menu1.settings")} | ${t(
            "integrations.integrations"
          )}`;
        } else if (pathname.split("/")[2] === "liveChat") {
          pageTitle = `${t("menu1.settings")} | Live chat`;
        }
        break;
      case "profile":
        if (pathname.split("/")[2] === "general") {
          pageTitle = `${t("menu1.profile")} | ${t("menu2.general_info")}`;
        } else if (pathname.split("/")[2] === "security") {
          pageTitle = `${t("menu1.profile")} | ${t("menu2.security")}`;
        } else if (pathname.split("/")[2] === "localization") {
          pageTitle = `${t("menu1.profile")} | ${t("menu2.localization")}`;
        } else if (pathname.split("/")[2] === "notification") {
          pageTitle = `${t("menu1.profile")} | ${t("menu1.notifications")}`;
        } else if (pathname.split("/")[2] === "signature") {
          pageTitle = `${t("menu1.profile")} | Signature email`;
        } else if (pathname.split("/")[2] === "accessToken") {
          pageTitle = `${t("menu1.profile")} | ${t("profilemenu.accessToken")}`;
        } else if (pathname.split("/")[2] === "policies") {
          pageTitle = `${t("menu1.profile")} | ${t("menu2.policies")}`;
        } else if (pathname.split("/")[2] === "infoTenant") {
          pageTitle = `${t("menu1.profile")} | ${t("profilemenu.infoTenant")}`;
        } else if (pathname.split("/")[2] === "appMobile") {
          pageTitle = `${t("menu1.profile")} | ${t("menu1.appMobile")}`;
        } else if (pathname.split("/")[2] === "logs") {
          pageTitle = `${t("menu1.profile")} | ${t("menu2.allNotifications")}`;
        }
        break;

      case "mailing":
        if (pathname.split("/")[3] === "inbox") {
          pageTitle = `${t("menu1.mailing")} | ${t("mailing.inbox")}`;
        } else if (pathname.split("/")[3] === "sent") {
          pageTitle = `${t("menu1.mailing")} | ${t("mailing.sent")}`;
        } else if (pathname.split("/")[3] === "drafts") {
          pageTitle = `${t("menu1.mailing")} | ${t("mailing.draft")}`;
        } else if (pathname.split("/")[3] === "starred") {
          pageTitle = `${t("menu1.mailing")} | ${t("mailing.starred")}`;
        } else if (pathname.split("/")[3] === "important") {
          pageTitle = `${t("menu1.mailing")} | ${"Important"}`;
        } else if (pathname.split("/")[3] === "trash") {
          pageTitle = `${t("menu1.mailing")} | ${t("mailing.trash")}`;
        } else if (pathname.split("/")[3] === "spam") {
          pageTitle = `${t("menu1.mailing")} | ${t("mailing.spam")}`;
        } else if (pathname.split("/")[3] === "archive") {
          pageTitle = `${t("menu1.mailing")} | ${t("mailing.archive")}`;
        } else if (pathname.split("/")[3] === "label") {
          const labelName = getLabelNameById(
            usedAccount,
            dataAccounts,
            pathname,
            t
          );
          pageTitle = `${t("menu1.mailing")} ${
            labelName ? `| ${labelName}` : ""
          }`;
        }
        break;
      case "stats":
        if (pathname.split("/")[2] === "general") {
          pageTitle = `${t("menu1.stats")} | ${t("menu2.general")}`;
        } else if (pathname.split("/")[2] === "rmc") {
          pageTitle = `${t("menu1.stats")} | ${t("menu2.statsRmc")}`;
        } else if (pathname.split("/")[2] === "families") {
          pageTitle = `${t("menu1.stats")} | ${t("menu2.family")}`;
        }
        break;

      case "visio-home":
        pageTitle = t("menu1.visio");
        break;
      case "unauthorized":
        pageTitle = t("unauthorized");
        break;
      case "drive":
        pageTitle = `${t("menu1.drive")}`;

        break;
      case "logout":
        pageTitle = t("logout");
        break;

      default:
        pageTitle =
          menuItems.find(
            (item) =>
              item?.key ===
              (pathname.split("/")[pathname.split("/").length - 1] ||
                pathname.split("/")[1])
          )?.label ||
          pathname.split("/")[1].charAt(0).toUpperCase() +
            pathname.split("/")[1].slice(1);

        break;
    }
    if (totalNotificationChat?.numberConversationChat > 0)
      changeFavicon(
        process.env.PUBLIC_URL + "/images/favicon_notification.ico"
      );
    else changeFavicon(process.env.PUBLIC_URL + "/images/favicon.ico");
    getTitle(pageTitle);
  }, [totalNotificationChat?.numberConversationChat, getTitle, pathname, t]);

  useEffect(() => {
    let isMounted = true;

    const checkGuestStatus = async () => {
      try {
        const guestStatus = await isGuestConnected(chatUser, user?.role);
        if (isMounted) {
          setIsGuest(guestStatus);
        }
      } catch (error) {
        if (isMounted) {
          console.error("Error determining guest status:", error);
        }
      }
    };

    checkGuestStatus();

    return () => {
      isMounted = false;
    };
  }, [chatUser, user?.role]);

  const scrollTo = (direction) => {
    switch (direction) {
      case "top":
        {
          const navigationIcons = document.querySelector(".navigation-icons");
          if (navigationIcons)
            navigationIcons.scrollTo({
              top: 0,
              behavior: "auto",
            });
        }

        break;
      case "bottom":
        {
          const navigationIcons = document.querySelector("#settingId");
          if (navigationIcons)
            navigationIcons.scrollIntoView({
              behavior: "auto",
            });
        }
        break;

      default:
        break;
    }
  };

  const resetModuleState = useCallback(
    (key) => {
      switch (key) {
        case "chat":
          break;
        case "mailing":
          dispatch(setSearchEmail(""));
          break;
        case "tasks":
          dispatch(setOpenTaskDrawer(false));
          localStorage.removeItem("redirect-activity-id");
          dispatch(isOverviewModalOpen(false));
          dispatch(setOpenTaskRoomDrawer(false));
          break;
        case "visio":
          dispatch(isOverviewModalOpen(false));
          dispatch(setOpenTaskRoomDrawer(false));
          break;
        case "settings":
          dispatch(setOpenFieldDrawer(false));
          break;
        default:
          dispatch(setOpenTaskDrawer(false));
          break;
      }
    },
    [dispatch]
  );

  return (
    <>
      {pathname !== "/editor" && (
        <Button
          style={{
            display: inViewTop ? "none" : "block",
          }}
          className={`${inViewTop ? "" : " mx-auto my-2 block "}  `}
          icon={
            <MdKeyboardDoubleArrowUp style={{ fontSize: 24, color: "white" }} />
          }
          type="text"
          onClick={() => scrollTo("top")}
        />
      )}

      {!user || Object.values(user).length === 0 ? (
        Array.from(Array(10).keys()).map((item, index) => (
          <div
            key={`menu_item_${index}`}
            className=" mx-1.5 my-2 h-10 w-[50px] animate-pulse rounded-lg bg-slate-800 text-center "
          />
        ))
      ) : (
        <Menu
          className="navigation-icons flex-1 scroll-m-0 overflow-y-auto scrollbar-none  scrollbar-track-slate-700"
          mode="inline"
          theme="dark"
          onClick={(e) => {
            // reset module state when changing the menu
            const path =
              pathname.split("/")[1] === "directory"
                ? pathname.split("/")[pathname.split("/").length - 1]
                : pathname.split("/")[1];
            if (e.key !== path) resetModuleState(path);
            if (e?.key === "tasks")
              dispatch({ type: "RESET_CONTACT_HEADER_INFO" });
          }}
          items={
            isGuest
              ? menuItems?.filter((el) =>
                  guestAllowedModules?.includes(el?.key)
                )
              : pathname.split("/")[1] === "editor"
              ? null
              : menuItems
          }
          selectedKeys={[
            pathname.split("/")[1] === "directory" ||
            pathname.split("/")[2] === "appMobile"
              ? pathname.split("/")[pathname.split("/").length - 1]
              : pathname.split("/")[1],
          ]}
        />
      )}

      {!isGuest && pathname !== "/editor" && (
        <Button
          style={{
            display: inViewBottom ? "none" : "block",
          }}
          className={`${inViewBottom ? "" : " mx-auto my-2 block "}  `}
          icon={
            <MdKeyboardDoubleArrowDown
              style={{ fontSize: 24, color: "white" }}
            />
          }
          type="text"
          onClick={() => scrollTo("bottom")}
        />
      )}
      {pathname === "/rmc" && (
        <Suspense fallback={<></>}>
          <FormCreate
            familyId={rmc?.family_id}
            open={rmc?.openCreateForm}
            rmcData={rmc?.createFormData}
          />
        </Suspense>
      )}
      {pathname === "/rmc" && (
        <Suspense fallback={<></>}>
          <CreateTask open={rmc?.openCreateTask} />
        </Suspense>
      )}

      {openDrawerChatWithColleague && (
        <Suspense fallback={<></>}>
          <ChatWithColleague />
        </Suspense>
      )}

      {/* {process.env.REACT_APP_BRANCH.includes("dev") &&
      !openModalChangeLang &&
      pathname === "/dashboard" ? (
        <Tour
          placement="right"
          open={openTour}
          zIndex={9999}
          onClose={() => setOpenTour(false)}
          steps={[
            {
              title: `Bonjour ${user?.label}, voici une présentation de ${t(
                "menu1.chat"
              )}`,
              description:
                "Ce module vous permet d'échanger en temps réel avec vos collègues et de collaborer au sein de groupes spécifiques",
              cover: (
                <img
                  alt="tour.png"
                  src="https://chatbackcomunik.cmk.biz:4543/storage/tenants/comunik/uploads/files/w6rYz2ScflCcT6RtspPHMCfWTGBuZ2LphAE5tvRQ.png"
                />
              ),
              target: () => refChat.current,
            },
            {
              title: `Voici une présentation de ${t("menu1.voip")}`,
              cover: (
                <img
                  alt="tour.png"
                  src="https://chatbackcomunik.cmk.biz:4543/storage/tenants/comunik/uploads/files/CJYxJUsgtsnokfJdIlFQZsbok29eJKjLCZmm85YH.png"
                />
              ),
              description:
                "Le module Téléphonie permet de passer des appels audio, de chatter en temps réel et de consulter l'ensemble de vos contacts avec une vue détaillée pour chaque contact. Il facilite ainsi une communication fluide et renforce la collaboration au sein de votre équipe.",
              target: () => document.getElementById("phonecpt"),
            },
            {
              title: t("globalSearch.globalSearchAuto"),

              description:
                "Le module Recherche Globale permet aux utilisateurs de trouver rapidement des informations à travers toute l'application, y compris des emails, des activités , des contacts ,etc.... Cette fonctionnalité améliore l'efficacité et facilite l'accès aux données essentielles en un instant.",
              target: () => document.getElementById("globalSearch"),
            },
          ]}
        />
      ) : null} */}
    </>
  );
};
export default memo(Menu1);
