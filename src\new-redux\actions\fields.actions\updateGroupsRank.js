import {
  UPDATE_GROUP_RANK_SUCCESS,
  UPDATE_GROUP_RANK_ERROR,
  UPDATE_GROUP_RANK_LOADING,
} from "../../constants";
import MainService from "../../../services/main.service";

export const updateGroupsRank = (payload) => async (dispatch) => {
  try {
    dispatch({ type: UPDATE_GROUP_RANK_LOADING });
    const response = await MainService.updateGroupRanking(payload);
    dispatch({
      type: UPDATE_GROUP_RANK_SUCCESS,
      payload: response?.data,
    });
  } catch (error) {
    if (error?.status !== 401) {
      dispatch({
        type: UPDATE_GROUP_RANK_ERROR,
        payload: error,
      });
    }
  }
};
