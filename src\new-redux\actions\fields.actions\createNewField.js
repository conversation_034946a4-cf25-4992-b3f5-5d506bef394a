import {
  CREATE_NEW_FIELD_SUCCESS,
  CREATE_NEW_FIELD_ERROR,
  CREATE_FIELD_LOADING,
} from "../../constants";
import MainService from "../../../services/main.service";

export const createNewField = (payload) => async (dispatch) => {
  try {
    dispatch({ type: CREATE_FIELD_LOADING });
    const response = await MainService.handleCreateNewField(payload);
    dispatch({
      type: CREATE_NEW_FIELD_SUCCESS,
      payload: response?.data?.data,
    });
  } catch (error) {
    if (error?.status !== 401) {
      dispatch({
        type: CREATE_NEW_FIELD_ERROR,
        payload: error,
      });
    }
  }
};
