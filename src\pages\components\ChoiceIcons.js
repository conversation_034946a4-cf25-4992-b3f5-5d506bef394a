import React from "react";
import {
 
  RestOutlined,
 
  BellOutlined,

  MessageOutlined,

  WarningOutlined,
  PlusOutlined,
 
  UploadOutlined,
  
  WhatsAppOutlined,
  InstagramOutlined,
  FacebookOutlined,
} from "@ant-design/icons";
import {
  AtSymbolIcon,
  TruckIcon,

} from "@heroicons/react/24/outline";

import {
  MdAssuredWorkload,
  MdDeleteOutline,
  MdOutlineAccessTime,
  MdOutlineCalendarToday,
  MdOutlineCameraEnhance,
 
  MdOutlineClose,

  MdOutlineCoPresent,
  MdOutlineComment,
  MdOutlineCreditCard,
  MdOutlineDirectionsCar,
  MdOutlineEdit,
  MdOutlineFolder,
  MdOutlineInbox,
  
  MdOutlineMail,
  MdOutlineMenuBook,
 
  MdOutlinePhoneIphone,
  MdOutlinePublic,
  MdOutlineRingVolume,
  MdOutlineSettings,
  MdOutlineVerified,
  MdOutlineVideocam,
  MdPersonOutline,
} from "react-icons/md";
import {
  HiOutlineVideoCamera,
  HiOutlineWrenchScrewdriver,
} from "react-icons/hi2";
import { Hospital, Stethoscope } from "lucide-react";

const ChoiceIcons = ({
  icon,
  fontSize,
  height = "auto",
  top,
  width,
  color = "",
}) => {
  if (icon === "DeleteOutlined") {
    return <MdDeleteOutline style={{ fontSize: fontSize, height, color }} />;
  } else if (icon === "EditOutlined") {
    return <MdOutlineEdit style={{ fontSize: fontSize, height, color }} />;
  } else if (icon === "RestOutlined") {
    return <RestOutlined />;
  } else if (icon === "BankOutlined") {
    return <MdAssuredWorkload style={{ fontSize: fontSize, height, color }} />;
  } else if (icon === "BellOutlined") {
    return <BellOutlined />;
  } else if (icon === "CalendarOutlined") {
    return (
      <MdOutlineCalendarToday style={{ fontSize: fontSize, height, color }} />
    );
  } else if (icon === "CameraOutlined") {
    return (
      <MdOutlineCameraEnhance style={{ fontSize: fontSize, height, color }} />
    );
  } else if (icon === "CarOutlined") {
    return (
      <MdOutlineDirectionsCar style={{ fontSize: fontSize, height, color }} />
    );
  } else if (icon === "CloseOutlined") {
    return <MdOutlineClose style={{ fontSize: fontSize, height, color }} />;
  } else if (icon === "CreditCardOutlined") {
    return (
      <MdOutlineCreditCard style={{ fontSize: fontSize, height, color }} />
    );
  } else if (icon === "FieldTimeOutlined") {
    return (
      <MdOutlineAccessTime style={{ fontSize: fontSize, height, color }} />
    );
  } else if (icon === "FolderOutlined") {
    return <MdOutlineFolder style={{ fontSize: fontSize, height, color }} />;
  } else if (icon === "InboxOutlined") {
    return <MdOutlineInbox style={{ fontSize: fontSize, height, color }} />;
  } else if (icon === "MailOutlined") {
    return <MdOutlineMail style={{ fontSize: fontSize, height, color }} />;
  } else if (icon === "MessageOutlined") {
    return <MessageOutlined style={{ fontSize: fontSize, height, color }} />;
  } else if (icon === "CheckCircleOutlined") {
    return <MdOutlineVerified style={{ fontSize: fontSize, height, color }} />;
  } else if (icon === "MobileOutlined") {
    return (
      <MdOutlinePhoneIphone style={{ fontSize: fontSize, height, color }} />
    );
  } else if (icon === "PhoneOutlined") {
    return (
      <MdOutlineRingVolume style={{ fontSize: fontSize, height, color }} />
    );
  } else if (icon === "ProfileOutlined") {
    return <MdOutlineCoPresent style={{ fontSize: fontSize, height, color }} />;
  } else if (icon === "ReadOutlined") {
    return <MdOutlineMenuBook style={{ fontSize: fontSize, height, color }} />;
  } else if (icon === "SettingOutlined") {
    return <MdOutlineSettings style={{ fontSize: fontSize, height, color }} />;
  } else if (icon === "PlusOutlined") {
    return <PlusOutlined />;
  } else if (icon === "VideoCameraOutlined") {
    return <MdOutlineVideocam style={{ fontSize: fontSize, height, color }} />;
  } else if (icon === "UserOutlined") {
    return <MdPersonOutline style={{ fontSize: fontSize, height, color }} />;
  } else if (icon === "WebOutlined") {
    return <MdOutlinePublic style={{ fontSize: fontSize, height, color }} />;
  } else if (icon === "UploadOutlined") {
    return <UploadOutlined style={{ fontSize: fontSize, height, color }} />;
  } else if (icon === "SocialOutlined") {
    return <MdOutlineComment style={{ fontSize: fontSize, height, color }} />;
  } else if (icon === "AtSymbolIcon") {
    return (
      <AtSymbolIcon
        className="relative "
        style={{
          fontSize: fontSize,
          height,
          color,
          top: typeof top === "number" ? top : "4px",
          width: width || "16px",
        }}
      />
    );
  } else if (icon === "TruckIcon") {
    return (
      <TruckIcon
        className=" relative"
        style={{
          fontSize: fontSize,
          height,
          color,
          top: top || "3px",
          width: width || "16px",
        }}
      />
    );
  } else if (icon === "WrenchScrewdriverIcon") {
    return (
      <HiOutlineWrenchScrewdriver
        className="text-[16px] "
        style={{ fontSize: fontSize, height, width, color }}
      />
    );
  } else if (icon === "WarningOutlined") {
    return <WarningOutlined style={{ fontSize: fontSize, height, color }} />;
  } else if (icon === "WhatsAppOutlined") {
    return <WhatsAppOutlined style={{ fontSize: fontSize, height, color }} />;
  } else if (icon === "InstagramOutlined") {
    return <InstagramOutlined style={{ fontSize: fontSize, height, color }} />;
  } else if (icon === "FacebookOutlined") {
    return <FacebookOutlined style={{ fontSize: fontSize, height, color }} />;
  } else if (icon === "visio") {
    return (
      <HiOutlineVideoCamera style={{ fontSize: fontSize, height, color }} />
    );
  } else if (icon === "MessengerOutlined") {
    return (
      <span className="anticon anticon-whats-app">
        <svg
          width="15"
          height="17"
          viewBox="0 -1 13 13"
          xmlns="http://www.w3.org/2000/svg"
          focusable={false}
          data-icon="instagram"
          fill="currentColor"
          aria-hidden="true"
          style={{ fontSize: fontSize, height, color }}
        >
          <path
            d="M6.5 0C2.9305 0 0 2.71177 0 6.07107C0 7.86104 0.850586 9.45098 2.16667 10.5584V13L4.57031 11.8287C5.1818 12.0081 5.8208 12.1421 6.5 12.1421C10.0695 12.1421 13 9.43036 13 6.07107C13 2.71177 10.0695 0 6.5 0ZM6.5 1.05584C9.51091 1.05584 11.9167 3.30774 11.9167 6.07107C11.9167 8.83439 9.51091 11.0863 6.5 11.0863C5.85254 11.0863 5.2347 10.9667 4.65495 10.7728L4.4349 10.7069L3.25 11.2843V10.0964L3.04687 9.93147C1.84505 9.00761 1.08333 7.62389 1.08333 6.07107C1.08333 3.30774 3.48909 1.05584 6.5 1.05584ZM5.89062 4.40482L2.6237 7.7703L5.55208 6.18655L7.10937 7.8198L10.3424 4.40482L7.48177 5.97208L5.89062 4.40482Z"
            // fill="black"
          />
        </svg>
      </span>
    );
  } else if (icon === "Consultation") {
    return <Stethoscope style={{ height, color }} size={fontSize || 16} />;
  } else if (icon === "Exam") {
    return (
      <Hospital
        style={{
          height,
          color,
        }}
        size={fontSize || 16}
      />
    );
  } else return <></>;
};

export default ChoiceIcons;
