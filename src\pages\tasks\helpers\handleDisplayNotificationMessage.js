import { getName } from "pages/layouts/chat/utils/ConversationUtils";

export const formatNotificationMessage = (
  t,
  user,
  actionsArray,
  isSameUser = false,
  userObject = {}
) => {
  if (actionsArray && actionsArray?.length === 2) {
    if (actionsArray[0] === "Creation") {
      return (
        <div
          dangerouslySetInnerHTML={
            isSameUser
              ? {
                  __html: t("notifications.currentUserCreatesActivity"),
                }
              : {
                  __html: t("tasks.taskToastMsgwithOptions", {
                    user: isSameUser ? t("toasts.currentUserPronoun") : user,
                    taskLabel: actionsArray[1].replace(/"|'/g, ""),
                  }),
                }
          }
        ></div>
      );
    } else if (actionsArray[0] === "Update") {
      return (
        <div
          dangerouslySetInnerHTML={
            isSameUser
              ? {
                  __html: t("notifications.currentUserUpdatesActivity"),
                }
              : {
                  __html: t("tasks.updateTaskToastMsgwithOptions", {
                    user,
                    taskLabel: actionsArray[1].replace(/"|'/g, ""),
                  }),
                }
          }
        ></div>
      );
    } else if (actionsArray[0] === "Deletion") {
      return (
        <div
          dangerouslySetInnerHTML={
            isSameUser
              ? {
                  __html: t("notifications.currentUserDeletesActivity"),
                }
              : {
                  __html: t("tasks.deleteTaskToastMsg", {
                    user,
                    taskLabel: actionsArray[1].replace(/"|'/g, ""),
                  }),
                }
          }
        ></div>
      );
    } else if (actionsArray[0] === "Update_dates") {
      return (
        <div
          dangerouslySetInnerHTML={
            isSameUser
              ? {
                  __html: t("notifications.currentUserUpdatesDateActivity"),
                }
              : {
                  __html: t("tasks.updateTaskToastMsgwithOptions", {
                    user,
                    taskLabel: actionsArray[1].replace(/"|'/g, ""),
                  }),
                }
          }
        ></div>
      );
    } else if (actionsArray[0] === "Update_label") {
      return (
        <div
          dangerouslySetInnerHTML={
            isSameUser
              ? {
                  __html: t("notifications.currentUserUpdatesLabel", {
                    newLabel: actionsArray[1].replace(/"|'/g, ""),
                  }),
                }
              : {
                  __html: t("toasts.updateActivityLabel", {
                    user: getName(user, "name"),
                    newLabel: actionsArray[1].replace(/"|'/g, ""),
                  }),
                }
          }
        ></div>
      );
    } else if (actionsArray[0] === "Update_task_files_remove") {
      return (
        <div
          dangerouslySetInnerHTML={
            isSameUser
              ? {
                  __html: t("notifications.currentUserRemovesFiles"),
                }
              : {
                  __html: t("toasts.removeFilesInActivity", {
                    user: getName(user, "name"),
                    label: actionsArray[1].replace(/"|'/g, ""),
                  }),
                }
          }
        ></div>
      );
    } else if (actionsArray[0] === "Update_task_files_add") {
      return (
        <div
          dangerouslySetInnerHTML={
            isSameUser
              ? {
                  __html: t("notifications.currentUserAddsFiles"),
                }
              : {
                  __html: t("toasts.addFilesInActivity", {
                    user: getName(user, "name"),
                    label: actionsArray[1].replace(/"|'/g, ""),
                  }),
                }
          }
        ></div>
      );
    } else if (actionsArray[0] === "Update_description") {
      return (
        <div
          dangerouslySetInnerHTML={
            isSameUser
              ? {
                  __html: t("notifications.currentUserUpdatesDescription"),
                }
              : {
                  __html: t("toasts.updateTaskNoteNotification", {
                    user: getName(user, "name"),
                    noteDescription: "description",
                    label: actionsArray[1].replace(/"|'/g, ""),
                  }),
                }
          }
        ></div>
      );
    } else if (actionsArray[0] === "Update_note") {
      return (
        <div
          dangerouslySetInnerHTML={
            isSameUser
              ? {
                  __html: t("notifications.currentUserUpdatesNote"),
                }
              : {
                  __html: t("toasts.updateTaskNoteNotification", {
                    user: getName(user, "name"),
                    noteDescription: "description",
                    label: actionsArray[1].replace(/"|'/g, ""),
                  }),
                }
          }
        ></div>
      );
    }
  } else if (actionsArray && actionsArray?.length === 3) {
    if (actionsArray[0] === "New_member") {
      return (
        <div
          dangerouslySetInnerHTML={
            isSameUser
              ? {
                  __html: t("notifications.currentUserAddsNewMemberActivity", {
                    newMember: actionsArray[2].replace(/"|'/g, ""),
                  }),
                }
              : {
                  __html: t("tasks.taskToastMsgwithOptions", {
                    user,
                    taskLabel: actionsArray[1].replace(/"|'/g, ""),
                  }),
                }
          }
        ></div>
      );
    } else if (actionsArray[0] === "Update_task_files") {
      return (
        <div
          dangerouslySetInnerHTML={
            actionsArray[1] === "add"
              ? {
                  __html: t("toasts.addFilesInActivity", {
                    user: getName(user, "name"),
                    label: actionsArray[1].replace(/"|'/g, ""),
                  }),
                }
              : {
                  __html: t("toasts.removeFilesInActivity", {
                    user: getName(user, "name"),
                    label: actionsArray[1].replace(/"|'/g, ""),
                  }),
                }
          }
        ></div>
      );
    } else if (actionsArray[0] === "Remove_member") {
      return (
        <div
          dangerouslySetInnerHTML={
            isSameUser
              ? {
                  __html: t("notifications.currentUserRemovesMember", {
                    removedUser: getName(
                      actionsArray[2].replace(/"|'/g, ""),
                      "name"
                    ),
                    activityLabel: actionsArray[1].replace(/"|'/g, ""),
                  }),
                }
              : {
                  __html: t("notifications.userRemovesMember", {
                    user,
                    removedUser: getName(
                      actionsArray[2].replace(/"|'/g, ""),
                      "name"
                    ),
                    activityLabel: actionsArray[1].replace(/"|'/g, ""),
                  }),
                }
          }
        ></div>
      );
    } else if (actionsArray[0] === "Update_items_list:") {
      return (
        <div
          dangerouslySetInnerHTML={
            isSameUser
              ? {
                  __html: t("toasts.meUpdateTaskListNotification", {
                    user,
                    labelList: actionsArray[2].replace(/"|'/g, ""),
                    label: actionsArray[1].replace(/"|'/g, ""),
                  }),
                }
              : {
                  __html: t("toasts.updateTaskListNotification", {
                    user,
                    labelList: actionsArray[2].replace(/"|'/g, ""),
                    label: actionsArray[1].replace(/"|'/g, ""),
                  }),
                }
          }
        ></div>
      );
    } else if (actionsArray[0] === "delete_list:") {
      return (
        <div
          dangerouslySetInnerHTML={
            isSameUser
              ? {
                  __html: t("toasts.meDeleteTaskListNotification", {
                    user,
                    label: actionsArray[1].replace(/"|'/g, ""),
                    labelList: actionsArray[2].replace(/"|'/g, ""),
                  }),
                }
              : {
                  __html: t("toasts.deleteTaskListNotification", {
                    user,
                    label: actionsArray[1].replace(/"|'/g, ""),
                    labelList: actionsArray[2].replace(/"|'/g, ""),
                  }),
                }
          }
        ></div>
      );
    } else if (actionsArray[0] === "Add_new_list:") {
      return (
        <div
          dangerouslySetInnerHTML={
            isSameUser
              ? {
                  __html: t("toasts.meCreateTaskListNotification", {
                    user,
                    labelList: actionsArray[2].replace(/"|'/g, ""),
                    label: actionsArray[1].replace(/"|'/g, ""),
                  }),
                }
              : {
                  __html: t("toasts.createTaskListNotification", {
                    user,
                    labelList: actionsArray[2].replace(/"|'/g, ""),
                    label: actionsArray[1].replace(/"|'/g, ""),
                  }),
                }
          }
        ></div>
      );
    } else if (actionsArray[0] === "update_rank_list:") {
      return (
        <div
          dangerouslySetInnerHTML={
            isSameUser
              ? {
                  __html: t("toasts.meUpdateRankTaskListNotification", {
                    user,
                    labelList: actionsArray[2].replace(/"|'/g, ""),
                    label: actionsArray[1].replace(/"|'/g, ""),
                  }),
                }
              : {
                  __html: t("toasts.updateRankaskListNotification", {
                    user,
                    labelList: actionsArray[2].replace(/"|'/g, ""),
                    label: actionsArray[1].replace(/"|'/g, ""),
                  }),
                }
          }
        ></div>
      );
    }
  } else if (actionsArray && actionsArray?.length === 4) {
    if (actionsArray[0] === "Update_priority") {
      return (
        <div
          dangerouslySetInnerHTML={
            isSameUser
              ? {
                  __html: t("notifications.currentUserUpdatesPriority", {
                    oldPriority:
                      actionsArray[2] === '""' || actionsArray[2] === null
                        ? t("tasks.noPriority")
                        : actionsArray[2].replace(/"|'/g, "") ===
                          "low".replace(/"|'/g, "")
                        ? t("tasks.lowPriority")
                        : actionsArray[2].replace(/"|'/g, "") ===
                          "medium".replace(/"|'/g, "")
                        ? t("tasks.mediumPriority")
                        : actionsArray[2].replace(/"|'/g, "") ===
                          "high".replace(/"|'/g, "")
                        ? t("tasks.highPriority")
                        : actionsArray[2].replace(/"|'/g, "") ===
                            "urgent".replace(/"|'/g, "") &&
                          t("tasks.urgentPriority"),
                    newPriority:
                      actionsArray[3].replace(/"|'/g, "") ===
                      "low".replace(/"|'/g, "")
                        ? t("tasks.lowPriority")
                        : actionsArray[3].replace(/"|'/g, "") ===
                          "medium".replace(/"|'/g, "")
                        ? t("tasks.mediumPriority")
                        : actionsArray[3].replace(/"|'/g, "") ===
                          "high".replace(/"|'/g, "")
                        ? t("tasks.highPriority")
                        : actionsArray[3].replace(/"|'/g, "") ===
                            "urgent".replace(/"|'/g, "") &&
                          t("tasks.urgentPriority"),
                  }),
                }
              : {
                  __html: t("tasks.updateTaskPriorityToastMsgwithOptions", {
                    user,
                    taskLabel: actionsArray[1].replace(/"|'/g, ""),
                    from:
                      actionsArray[2] === '""' || actionsArray[2] === null
                        ? t("tasks.noPriority")
                        : actionsArray[2].replace(/"|'/g, "") ===
                          "low".replace(/"|'/g, "")
                        ? t("tasks.lowPriority")
                        : actionsArray[2].replace(/"|'/g, "") ===
                          "medium".replace(/"|'/g, "")
                        ? t("tasks.mediumPriority")
                        : actionsArray[2].replace(/"|'/g, "") ===
                          "high".replace(/"|'/g, "")
                        ? t("tasks.highPriority")
                        : actionsArray[2].replace(/"|'/g, "") ===
                            "urgent".replace(/"|'/g, "") &&
                          t("tasks.urgentPriority"),
                    to:
                      actionsArray[3].replace(/"|'/g, "") ===
                      "low".replace(/"|'/g, "")
                        ? t("tasks.lowPriority")
                        : actionsArray[3].replace(/"|'/g, "") ===
                          "medium".replace(/"|'/g, "")
                        ? t("tasks.mediumPriority")
                        : actionsArray[3].replace(/"|'/g, "") ===
                          "high".replace(/"|'/g, "")
                        ? t("tasks.highPriority")
                        : actionsArray[3].replace(/"|'/g, "") ===
                            "urgent".replace(/"|'/g, "") &&
                          t("tasks.urgentPriority"),
                  }),
                }
          }
        ></div>
      );
    } else if (actionsArray[0] === "Update_stage") {
      return (
        <div
          dangerouslySetInnerHTML={{
            __html: t("tasks.updateTaskStageToastMsgwithOptions", {
              user,
              taskLabel: actionsArray[1]?.replace(/"|'/g, ""),
              from: actionsArray[2]?.replace(/"|'/g, ""),
              to: actionsArray[3]?.replace(/"|'/g, ""),
            }),
          }}
        ></div>
      );
    } else if (actionsArray[0] === "Update_task_type") {
      return (
        <div
          dangerouslySetInnerHTML={
            isSameUser
              ? {
                  __html: t("notifications.currentUserUpdatesType", {
                    oldType: actionsArray[2].replace(/"|'/g, ""),
                    newType: actionsArray[3].replace(/"|'/g, ""),
                  }),
                }
              : {
                  __html: t("toasts.updateActivityType", {
                    user: getName(user, "name"),
                    label: actionsArray[1].replace(/"|'/g, ""),
                    oldType: actionsArray[2].replace(/"|'/g, ""),
                    newType: actionsArray[3].replace(/"|'/g, ""),
                  }),
                }
          }
        ></div>
      );
    } else if (actionsArray[0] === "Update_family_element") {
      return (
        <div
          dangerouslySetInnerHTML={
            isSameUser
              ? {
                  __html: t("notifications.currentUserUpdatesElement", {
                    element: actionsArray[3].replace(/"|'/g, ""),
                    module: actionsArray[2].replace(/"|'/g, ""),
                  }),
                }
              : {
                  __html: t("toasts.updateActivityElement", {
                    user: getName(user, "name"),
                    label: actionsArray[1].replace(/"|'/g, ""),
                    element: actionsArray[3].replace(/"|'/g, ""),
                    module: actionsArray[2].replace(/"|'/g, ""),
                  }),
                }
          }
        ></div>
      );
    } else if (actionsArray[0] === "Update_dates") {
      return (
        <div
          dangerouslySetInnerHTML={
            isSameUser
              ? {
                  __html: t("notifications.currentUserUpdatesDateActivity", {
                    oldDate: actionsArray[2].replace(/"|'/g, ""),
                    newDate: actionsArray[3].replace(/"|'/g, ""),
                  }),
                }
              : {
                  __html: t("toasts.updateTaskDatesNotification", {
                    user: getName(user, "name"),
                    label: actionsArray[1].replace(/"|'/g, ""),
                  }),
                }
          }
        ></div>
      );
    } else if (actionsArray[0] === "Update_task_reminder") {
      return (
        <div
          dangerouslySetInnerHTML={
            isSameUser
              ? {
                  __html: t("notifications.currentUserUpdatesReminder"),
                }
              : {
                  __html: t("toasts.updateReminder", {
                    user: getName(user, "name"),
                    label: actionsArray[1].replace(/"|'/g, ""),
                  }),
                }
          }
        ></div>
      );
    } else if (actionsArray[0] === "New_member") {
      return (
        <div
          dangerouslySetInnerHTML={
            isSameUser
              ? {
                  __html: t("notifications.currentUserAddsMemberWithRole", {
                    addedUser: getName(
                      actionsArray[2].replace(/"|'/g, ""),
                      "name"
                    ),
                    role: dispatchRole(actionsArray[3].replace(/"|'/g, ""), t),
                    activityLabel: actionsArray[1].replace(/"|'/g, ""),
                  }),
                }
              : {
                  __html: t("notifications.userAddsMemberWithRole", {
                    user: getName(user, "name"),
                    addedUser: getName(
                      actionsArray[2].replace(/"|'/g, ""),
                      "name"
                    ),
                    role: dispatchRole(actionsArray[3].replace(/"|'/g, ""), t),
                    activityLabel: actionsArray[1].replace(/"|'/g, ""),
                  }),
                }
          }
        />
      );
    } else if (actionsArray[0] === "Remove_member") {
      return (
        <div
          dangerouslySetInnerHTML={
            isSameUser
              ? {
                  __html: t("notifications.currentUserRemovesMemberWithRole", {
                    removedUser: getName(
                      actionsArray[2].replace(/"|'/g, ""),
                      "name"
                    ),
                    role: dispatchRole(actionsArray[3].replace(/"|'/g, ""), t),
                    activityLabel: actionsArray[1].replace(/"|'/g, ""),
                  }),
                }
              : {
                  __html: t("notifications.userRemovesMemberWithRole", {
                    user: getName(user, "name"),
                    removedUser: getName(
                      actionsArray[2].replace(/"|'/g, ""),
                      "name"
                    ),
                    role: dispatchRole(actionsArray[3].replace(/"|'/g, ""), t),
                    activityLabel: actionsArray[1].replace(/"|'/g, ""),
                  }),
                }
          }
        />
      );
    } else if (actionsArray[0] === "delete_item_list:") {
      return (
        <div
          dangerouslySetInnerHTML={
            isSameUser
              ? {
                  __html: t("toasts.meDeleteItemInTaskListNotification", {
                    user,
                    label: actionsArray[1].replace(/"|'/g, ""),
                    labelList: actionsArray[2].replace(/"|'/g, ""),
                    labelItem: actionsArray[3].replace(/"|'/g, ""),
                  }),
                }
              : {
                  __html: t("toasts.deleteTaskListNotification", {
                    user,
                    label: actionsArray[1].replace(/"|'/g, ""),
                    labelList: actionsArray[2].replace(/"|'/g, ""),
                    labelItem: actionsArray[3].replace(/"|'/g, ""),
                  }),
                }
          }
        ></div>
      );
    }
  } else if (actionsArray && actionsArray?.length === 5) {
    if (
      (actionsArray[0] === "Update_label_list:",
      "--",
      actionsArray[1].replace(/"|'/g, ""))
    ) {
      return (
        <div
          dangerouslySetInnerHTML={
            isSameUser
              ? {
                  __html: t("toasts.meUpdateNameTaskListNotification", {
                    user,
                    label: actionsArray[1].replace(/"|'/g, ""),
                    oldName: actionsArray[3].replace(/"|'/g, ""),
                    newName: actionsArray[2].replace(/"|'/g, ""),
                  }),
                }
              : {
                  __html: t("toasts.updateNameTaskListNotification", {
                    user,
                    label: actionsArray[1].replace(/"|'/g, ""),
                    oldName: actionsArray[3].replace(/"|'/g, ""),
                    newName: actionsArray[2].replace(/"|'/g, ""),
                  }),
                }
          }
        ></div>
      );
    }
  }
};

/* const sanitizeInput = (input) => input.replace(/"|'/g, "");
const getPriorityTranslation = (priority, t) => {
  const priorityMap = {
    low: t("tasks.lowPriority"),
    medium: t("tasks.mediumPriority"),
    high: t("tasks.highPriority"),
    urgent: t("tasks.urgentPriority"),
    default: t("tasks.noPriority"),
  };
  return priorityMap[priority] || priorityMap.default;
};

const getNotificationMessage = (key, variables = {}, t) => {
  return { __html: t(key, variables) };
};

const getUserName = (t, user, isSameUser) =>
  isSameUser ? t("toasts.currentUserPronoun") : getName(user, "name");

export const formatNotificationMessage = (
  t,
  user,
  actionsArray,
  isSameUser = false,
  userObject = {}
) => {
  if (!actionsArray || actionsArray.length < 2) return null;

  const actionType = actionsArray[0];
  const actionValue = sanitizeInput(actionsArray[1]);
  const actionThirdValue = actionsArray[2] && sanitizeInput(actionsArray[2]);
  const actionFourthValue = actionsArray[3] && sanitizeInput(actionsArray[3]);

  const variables = {
    user: getUserName(t, user, isSameUser),
    taskLabel: actionValue,
    oldPriority: actionThirdValue
      ? getPriorityTranslation(actionThirdValue, t)
      : undefined,
    newPriority: actionFourthValue
      ? getPriorityTranslation(actionFourthValue, t)
      : undefined,
    oldType: actionThirdValue,
    newType: actionFourthValue,
    removedUser: actionThirdValue
      ? getName(actionThirdValue, "name")
      : undefined,
    addedUser: actionThirdValue ? getName(actionThirdValue, "name") : undefined,
    role: actionFourthValue ? dispatchRole(actionFourthValue, t) : undefined,
    element: actionFourthValue,
    module: actionThirdValue,
    oldDate: actionThirdValue,
    newDate: actionFourthValue,
    noteDescription:
      actionValue === "Update_description" || actionValue === "Update_note"
        ? "description"
        : undefined,
  };

  const actionMapping = {
    Creation: {
      key: isSameUser
        ? "notifications.currentUserCreatesActivity"
        : "tasks.taskToastMsgwithOptions",
      variables,
    },
    Update: {
      key: isSameUser
        ? "notifications.currentUserUpdatesActivity"
        : "tasks.updateTaskToastMsgwithOptions",
      variables,
    },
    Deletion: {
      key: isSameUser
        ? "notifications.currentUserDeletesActivity"
        : "tasks.deleteTaskToastMsg",
      variables,
    },
    Update_dates: {
      key: isSameUser
        ? "notifications.currentUserUpdatesDateActivity"
        : "tasks.updateTaskToastMsgwithOptions",
      variables,
    },
    Update_label: {
      key: isSameUser
        ? "notifications.currentUserUpdatesLabel"
        : "toasts.updateActivityLabel",
      variables,
    },
    Update_task_files_remove: {
      key: isSameUser
        ? "notifications.currentUserRemovesFiles"
        : "toasts.removeFilesInActivity",
      variables,
    },
    Update_task_files_add: {
      key: isSameUser
        ? "notifications.currentUserAddsFiles"
        : "toasts.addFilesInActivity",
      variables,
    },
    Update_priority: {
      key: isSameUser
        ? "notifications.currentUserUpdatesPriority"
        : "tasks.updateTaskPriorityToastMsgwithOptions",
      variables: {
        ...variables,
        oldPriority: getPriorityTranslation(actionThirdValue, t),
        newPriority: getPriorityTranslation(actionFourthValue, t),
      },
    },
    New_member: {
      key: isSameUser
        ? "notifications.currentUserAddsNewMemberActivity"
        : "tasks.taskToastMsgwithOptions",
      variables,
    },
    Remove_member: {
      key: isSameUser
        ? "notifications.currentUserRemovesMember"
        : "notifications.userRemovesMember",
      variables,
    },
    Update_task_type: {
      key: isSameUser
        ? "notifications.currentUserUpdatesType"
        : "toasts.updateActivityType",
      variables,
    },
    Update_stage: {
      key: "tasks.updateTaskStageToastMsgwithOptions",
      variables,
    },
    Update_family_element: {
      key: isSameUser
        ? "notifications.currentUserUpdatesElement"
        : "toasts.updateActivityElement",
      variables,
    },
    Update_task_reminder: {
      key: isSameUser
        ? "notifications.currentUserUpdatesReminder"
        : "toasts.updateReminder",
      variables,
    },
  };

  const action = actionMapping[actionType];

  if (action) {
    return (
      <div
        dangerouslySetInnerHTML={getNotificationMessage(
          action.key,
          action.variables,
          t
        )}
      ></div>
    );
  }

  return null;
}; */

const dispatchRole = (role, t) => {
  if (role === "follower") {
    return t("tasks.followerRole");
  } else if (role === "guest") {
    return t("tasks.MemberRole");
  } else if (role === "owner") {
    return t("tasks.OwnerRole");
  }
};
