import { memo, useRef } from "react";
import { message, Upload } from "antd";
import {
  FileExcelOutlined,
  FileImageOutlined,
  FilePdfOutlined,
  FileTextOutlined,
  FileWordOutlined,
} from "@ant-design/icons";

const DragFileField = ({
  t,
  className = "",
  listType = "picture",
  multiple = true,
  onChange,
  fileList = [],
  itemRender,
  beforeUpload,
  allowedExtensions = [
    "pdf",
    "docx",
    "xlsx",
    "pptx",
    "txt",
    "csv",
    "jpg",
    "jpeg",
    "png",
    "svg",
    "zip",
    "ovpn",
    "mp3",
    "wav",
  ],
}) => {
  //
  const { Dragger } = Upload;
  const disallowedFiles = useRef([]);
  //
  const handleBeforeUpload = (file, fileList) => {
    // const fileName = file?.name
    const fileExt = file?.name?.split(".")?.pop()?.toLowerCase();
    const isAllowed = allowedExtensions?.includes(fileExt);

    if (!isAllowed) disallowedFiles.current.push(fileExt);

    if (
      fileList.indexOf(file) === fileList.length - 1 &&
      disallowedFiles.current.length > 0
    ) {
      const uniqueExtensions = [...new Set(disallowedFiles.current)];
      message.open({
        duration: 4,
        type: "error",
        content: (
          <span
            dangerouslySetInnerHTML={{
              __html: t("mailing.notAllowedExt", {
                ext: uniqueExtensions.join(", "),
              }),
            }}
          />
        ),
      });

      disallowedFiles.current = [];
    }

    if (isAllowed) {
      onChange(file);
      return isAllowed;
    } else return Upload.LIST_IGNORE;
  };
  //
  const draggerProps = {
    listType,
    multiple,
    onChange,
    fileList,
    allowedExtensions,
    className,
    ...(itemRender ? itemRender : {}),
    beforeUpload: beforeUpload ?? handleBeforeUpload,
  };
  //

  //
  return (
    <Dragger {...draggerProps}>
      <p className="ant-upload-drag-icon">
        <FileWordOutlined style={{ fontSize: "1.3rem" }} />
        <FileExcelOutlined style={{ fontSize: "1.8rem" }} />
        <FileImageOutlined style={{ fontSize: "2.5rem" }} />
        <FilePdfOutlined style={{ fontSize: "1.8rem" }} />
        <FileTextOutlined style={{ fontSize: "1.3rem" }} />
      </p>
      <p className="ant-upload-text">{t("tasks.uploadFile")}</p>
      <p className="ant-upload-hint">
        {allowedExtensions?.map((ext) => `.${ext}`).join(" ")}
      </p>
    </Dragger>
  );
};

export default memo(DragFileField);
