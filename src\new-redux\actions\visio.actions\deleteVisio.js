import { IS_LOADING_VISIO, DELETE_VISIO_SUCCESS, DELETE_VISIO_ERROR } from "../../constants";
import MainService from "../../../services/main.service";
import { toastNotification } from "../../../components/ToastNotification";

export const deleteVisio = (visioId) => async (dispatch) => {
    try {
        dispatch({ type: IS_LOADING_VISIO });
        const response = await MainService.deleteVisio(visioId);
        dispatch({
            type: DELETE_VISIO_SUCCESS,
            payload: visioId,
        });
    } catch (error) {
        dispatch({
            type: DELETE_VISIO_ERROR,
            payload: error,
        });
        toastNotification(
            "error",
            "Something went wrong, please try again!",
            "topRight"
        );
    }
};
