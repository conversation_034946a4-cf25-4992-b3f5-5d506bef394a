import React, { useEffect, useRef, useState } from "react";
import {
  Modal,
  Form,
  Input,
  Button,
  Avatar,
  Select,
  Divider,
  Space,
  Cascader,
  Spin,
  Skeleton,
} from "antd";
import { useSelector, useDispatch } from "react-redux";
import {
  setDetailsOpenIntegration,
  setOpenModalTicketGlpi,
} from "new-redux/actions/chat.actions/Input";
import { useTranslation } from "react-i18next";
import { generateAxios } from "services/axiosInstance";
import { URL_ENV } from "index";
import {
  setMsgTask,
  setMsgTaskId,
} from "new-redux/actions/tasks.actions/handleTaskDrawer";
import { toastNotification } from "components/ToastNotification";
import { PlusOutlined } from "@ant-design/icons";
import { updateMessages } from "../chat/utils/rqUpdate";
import { getOrGenerateTabId } from "../chat/utils/ConversationUtils";
let index = 0;
const ModalTicketGLPI = ({ msgTask }) => {
  const { openModalTicketGlpi, detailsOpenIntegration } = useSelector(
    (state) => state.form
  );
  const { msgTaskId } = useSelector((state) => state?.TasksRealTime);
  const { selectedConversation } = useSelector((state) => state.ChatRealTime);

  const [form] = Form.useForm();
  const [loadingGeneral, setLoadingGeneral] = useState(false);
  const [loading, setLoading] = useState(false);
  const [errorOptionsListClickup, setErrorOptionsListClickup] = useState(false);
  const [actionsGlpi, setActionsGlpi] = useState({});

  const [isContentEmpty, setIsContentEmpty] = useState(true);
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const inputRef = useRef(null);
  const inputAddTagRef = useRef(null);

  const [items, setItems] = useState([]);
  const [name, setName] = useState("");
  const [optionsCLickUp, setOptionsCLickUp] = useState([]);
  const [loadingClickUp, setLoadingClickUp] = useState(false);
  const [openFolderClickup, setOpenFolderClickup] = useState(false);

  useEffect(() => {
    if (openModalTicketGlpi) {
      inputRef.current?.focus();
    }
  }, [openModalTicketGlpi]);

  useEffect(() => {
    const getTeamsClickUp = async () => {
      setLoadingClickUp(true);
      try {
        const res = await generateAxios(
          `${URL_ENV?.REACT_APP_OAUTH_CHAT_API}/${process.env.REACT_APP_SUFFIX_API}`
        ).get("/clickup/teams");
        setOptionsCLickUp(
          res.data.map((el) => ({ value: el.id, label: el.name, children: [] }))
        );
        if (res.data.length === 0) {
          setIsContentEmpty(true);
          setErrorOptionsListClickup(true);
        }
        setLoadingClickUp(false);
      } catch (err) {
        setLoadingClickUp(false);
      }
    };
    const getActionsGlpi = async () => {
      setLoadingGeneral(true);
      try {
        const res = await generateAxios(
          `${URL_ENV?.REACT_APP_OAUTH_CHAT_API}/${process.env.REACT_APP_SUFFIX_API}`
        ).get("/glpi/get-ticket-requests");
        delete res.data.success;

        setActionsGlpi(res.data);
        setLoadingGeneral(false);
      } catch (err) {
        setLoadingGeneral(false);
        toastNotification(
          "error",
          err?.response?.data?.message || "Error",
          "topRight"
        );
      }
    };
    if (detailsOpenIntegration?.app_name?.toLowerCase() === "clickup") {
      getTeamsClickUp();
    } else if (detailsOpenIntegration?.app_name?.toLowerCase() === "glpi") {
      getActionsGlpi();
    }
  }, [detailsOpenIntegration.app_name]);
  const getSecondTeamsClickUp = async (itemClickUp) => {
    setLoadingClickUp(true);
    try {
      const res = await generateAxios(
        `${URL_ENV?.REACT_APP_OAUTH_CHAT_API}/${process.env.REACT_APP_SUFFIX_API}`
      ).get(`/clickup/spaces/${itemClickUp[0]}`);
      setOptionsCLickUp((prev) =>
        prev.map((el) =>
          el.value === itemClickUp[0]
            ? {
                ...el,
                children: res.data.map((el) => ({
                  value: el.id,
                  label: el.name,
                })),
              }
            : el
        )
      );
      if (res.data.length === 0) {
        setErrorOptionsListClickup(true);
        isContentEmpty(true);
      }
      setLoadingClickUp(false);
    } catch (err) {
      console.log(err);
      toastNotification(
        "error",
        err?.response?.data?.message || "Error",
        "topRight"
      );
      setLoadingClickUp(false);
    }
  };
  const getThirdTeamsClickUp = async (itemClickUp) => {
    setLoadingClickUp(true);
    try {
      const res = await generateAxios(
        `${URL_ENV?.REACT_APP_OAUTH_CHAT_API}/${process.env.REACT_APP_SUFFIX_API}`
      ).get(`/clickup/folders/${itemClickUp[1]}`);
      setOptionsCLickUp((prev) =>
        prev.map((el) =>
          el.value === itemClickUp[0]
            ? {
                ...el,
                children: el.children.map((el) =>
                  el.value === itemClickUp[1]
                    ? {
                        ...el,
                        children: res.data.map((el) => ({
                          value: el.id,
                          label: el.name,
                        })),
                      }
                    : el
                ),
              }
            : el
        )
      );
      if (res.data.length === 0) {
        setErrorOptionsListClickup(true);
        isContentEmpty(true);
      }
      setLoadingClickUp(false);
    } catch (err) {
      setLoadingClickUp(false);
    }
  };
  const getFourthTeamsClickUp = async (itemClickUp) => {
    setLoadingClickUp(true);

    try {
      const res = await generateAxios(
        `${URL_ENV?.REACT_APP_OAUTH_CHAT_API}/${process.env.REACT_APP_SUFFIX_API}`
      ).get(`/clickup/lists/${itemClickUp[2]}`);

      // Mettez à jour les options en fonction de la réponse obtenue
      setOptionsCLickUp((prev) =>
        prev.map((el) =>
          el.value === itemClickUp[0]
            ? {
                ...el,
                children: el.children.map((child) =>
                  child.value === itemClickUp[1]
                    ? {
                        ...child,
                        children: child.children.map((grandChild) =>
                          grandChild.value === itemClickUp[2]
                            ? {
                                ...grandChild,
                                children: res.data.map((item) => ({
                                  value: item.id,
                                  label: item.name,
                                })),
                              }
                            : grandChild
                        ),
                      }
                    : child
                ),
              }
            : el
        )
      );
      setErrorOptionsListClickup(false);

      if (res.data.length === 0) {
        setIsContentEmpty(true);
        setOpenFolderClickup(false);
        setErrorOptionsListClickup(false);
      }
      setLoadingClickUp(false);
    } catch (err) {
      setLoadingClickUp(false);
    }
  };
  const changeFolderClickUP = (itemClickUp) => {
    if (
      detailsOpenIntegration?.app_name?.toLowerCase() === "clickup" &&
      Array.isArray(itemClickUp) &&
      itemClickUp?.length === 1
    ) {
      getSecondTeamsClickUp(itemClickUp);
    } else if (
      detailsOpenIntegration?.app_name?.toLowerCase() === "clickup" &&
      Array.isArray(itemClickUp) &&
      itemClickUp?.length === 2
    ) {
      getThirdTeamsClickUp(itemClickUp);
    } else if (
      detailsOpenIntegration?.app_name?.toLowerCase() === "clickup" &&
      Array.isArray(itemClickUp) &&
      itemClickUp?.length === 3
    ) {
      getFourthTeamsClickUp(itemClickUp);
    } else {
      setOpenFolderClickup(false);
    }
  };

  const handleCancel = () => {
    dispatch(setOpenModalTicketGlpi(false));
    dispatch(setDetailsOpenIntegration({}));
    dispatch(setMsgTaskId(""));
  };
  const payloadTicket = (values) => {
    if (
      detailsOpenIntegration.fromRmc &&
      detailsOpenIntegration.app_name?.toUpperCase() === "GLPI"
    ) {
      return { ...values, tab_id: getOrGenerateTabId() };
    } else
      return { ...values, tab_id: getOrGenerateTabId(), message_id: msgTaskId };
  };
  const handleOk = async () => {
    setLoading(true);
    try {
      let values = await form.validateFields();
      if (Array.isArray(values.list_id) && values.list_id.length === 4) {
        values = { ...values, list_id: values.list_id[3] };
      }

      const response = await generateAxios(
        `${URL_ENV?.REACT_APP_OAUTH_CHAT_API}/${process.env.REACT_APP_SUFFIX_API}`
      ).post(
        `${detailsOpenIntegration?.app_name.toLowerCase()}/create-ticket`,
        payloadTicket(values)
      );
      updateMessages(
        response.data.result, //integrations
        "new_integration",
        response.data.message_id, //msgid
        selectedConversation?.id,
        selectedConversation?.type,

        null
      );
      dispatch(setMsgTask(""));
      toastNotification(
        "success",
        t("vue360.theTicket") +
          " " +
          detailsOpenIntegration?.app_name +
          t("toasts.created"),
        "topRight"
      );
      handleCancel(); // Ferme le modal après le succès
    } catch (error) {
      if (detailsOpenIntegration?.app_name.toLowerCase() === "github")
        toastNotification(
          "error",
          error?.response?.data?.error?.message,
          "topRight"
        );
      else toastNotification("error", error?.response?.data?.error, "topRight");
      // if (error?.response?.status === 400) {
      //   toastNotification("error", error?.response?.data?.error, "topRight");
      // } else toastNotification("error", t("toasts.somethingWrong"), "topRight");
    } finally {
      setLoading(false);
    }
  };

  const convertHtmlToText = (html) => {
    const tempElement = document.createElement("div");
    tempElement.innerHTML = html;
    return tempElement.innerText;
  };

  const onValuesChange = (changedValues, allvalues) => {
    const allValuesAreFilled = Object.values(allvalues).every(
      (value) => value !== "" && value !== undefined && value !== null
    );
    if (detailsOpenIntegration?.app_name.toLowerCase() === "glpi") {
      if (
        !allvalues.name ||
        !allvalues?.contents ||
        !allvalues?.categories_id ||
        typeof allvalues?.entities_id !== "number"
      ) {
        setIsContentEmpty(true);
      } else {
        setIsContentEmpty(false);
      }
    } else if (allvalues["list_id"]?.length < 4) {
      setIsContentEmpty(true);
    } else setIsContentEmpty(!allValuesAreFilled);
  };

  const onNameChange = (event) => {
    setName(event.target.value);
  };
  const addtagToGithub = (e) => {
    e.preventDefault();
    setItems([...items, name || `New item ${index++}`]);
    setName("");
    setTimeout(() => {
      inputAddTagRef.current?.focus();
    }, 0);
  };
  const renderIntegrationFields = () => {
    const appName = detailsOpenIntegration?.app_name?.toLowerCase();

    if (appName === "clickup") {
      return (
        <Form.Item
          label={t("tasks.listView")}
          name="list_id"
          rules={[
            {
              required: true,
              message: `${t("integrations.folderId")} ${t(
                "table.header.isrequired"
              )}`,
            },
          ]}
        >
          <Cascader
            options={optionsCLickUp}
            onDropdownVisibleChange={(open) =>
              open && setOpenFolderClickup(true)
            }
            status={errorOptionsListClickup ? "error" : false}
            open={openFolderClickup}
            onChange={changeFolderClickUP}
            placeholder={t("emailTemplates.plsSelect")}
            loading={loadingClickUp}
          />
        </Form.Item>
      );
    } else if (appName === "github") {
      return (
        <>
          <Form.Item
            label={t("tasks.owner")}
            name="owner"
            rules={[
              {
                required: true,
                message: `${t("tasks.owner")} ${t("table.header.isrequired")}`,
              },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label={t("integrations.repository")}
            name="repo"
            rules={[
              {
                required: true,
                message: `${t("integrations.repository")} ${t(
                  "table.header.isrequired"
                )}`,
              },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label={t("menu2.tags")}
            name="labels"
            rules={[
              {
                required: true,
                message: `${t("menu2.tags")} ${t("table.header.isrequired")}`,
              },
            ]}
          >
            <Select
              placeholder={t("emailTemplates.plsSelect")}
              mode="multiple"
              onDropdownVisibleChange={(open) =>
                open &&
                setTimeout(() => {
                  inputAddTagRef.current.focus();
                }, 300)
              }
              style={{ width: "100%" }}
              dropdownRender={(menu) => (
                <>
                  {menu}
                  <Divider style={{ margin: "8px 0" }} />
                  <div className="flex w-full items-center justify-between gap-x-2">
                    <Input
                      ref={inputAddTagRef}
                      value={name}
                      onChange={onNameChange}
                      onKeyDown={(e) => {
                        e.stopPropagation();
                        if (e.key === "Enter" && e.target.value) {
                          addtagToGithub(e);
                        }
                      }}
                    />
                    <Button
                      icon={<PlusOutlined />}
                      type="primary"
                      disabled={!name}
                      onClick={addtagToGithub}
                    >
                      {t("tags.createTag")}
                    </Button>
                  </div>
                </>
              )}
              options={items.map((item) => ({ label: item, value: item }))}
            />
          </Form.Item>
        </>
      );
    } else if (appName === "glpi") {
      return loadingGeneral ? (
        <>
          <Form.Item label={t("integrations.categories_id")}>
            <div className="skeletonSelectDepartments grow">
              <Skeleton.Input
                style={{
                  width: "100%",
                  minWidth: 150,
                  display: "inline-flex",
                }}
                active
              />
            </div>
          </Form.Item>

          <Form.Item label={t("integrations.urgency")}>
            <div className="skeletonSelectDepartments grow">
              <Skeleton.Input
                style={{
                  width: "100%",
                  minWidth: 150,
                  display: "inline-flex",
                }}
                active
              />
            </div>
          </Form.Item>
          <Form.Item label={t("integrations.priority")}>
            <div className="skeletonSelectDepartments grow">
              <Skeleton.Input
                style={{
                  width: "100%",
                  minWidth: 150,
                  display: "inline-flex",
                }}
                active
              />
            </div>
          </Form.Item>
        </>
      ) : (
        Object.keys(actionsGlpi).map((key, i) => (
          <Form.Item
            key={i}
            name={key}
            label={t(`integrations.${key}`)}
            rules={[
              {
                required:
                  key === "categories_id" || key === "entities_id"
                    ? true
                    : false,
                message: `${t(`integrations.${key}`)} ${t(
                  "table.header.isrequired"
                )}`,
              },
            ]}
          >
            <Select
              style={{ width: "100%" }}
              placeholder={`${t("import.shareBtnError")}`}
            >
              {actionsGlpi[key].map((item) => (
                <Select.Option key={item.id} value={item.id}>
                  {item.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        ))
      );
    }
    return null;
  };
  return (
    <Modal
      title={
        <span className="flex items-center gap-x-1">
          <span>{t("contacts.btnCreateTicket")}</span>
          <span>{detailsOpenIntegration?.app_name?.toUpperCase()}</span>
          <Avatar src={detailsOpenIntegration?.logo} size={16} />
        </span>
      }
      open={openModalTicketGlpi}
      onOk={handleOk}
      confirmLoading={loading}
      styles={{ footer: { textAlign: "end" } }}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          {t("form.cancel")}
        </Button>,
        <Button
          type="primary"
          loading={loading}
          onClick={handleOk}
          disabled={isContentEmpty}
        >
          {t("form.create")}
        </Button>,
      ]}
    >
      <Form
        form={form}
        layout="vertical"
        name="myForm"
        onValuesChange={onValuesChange}
      >
        {/* "ticketTitleIn":"Titre du ticket dans ",
        "descriptionTicketIn":"Description du ticket dans" */}
        {loadingGeneral ? (
          <>
            <Form.Item
              label={
                t("integrations.descriptionTicketIn") +
                detailsOpenIntegration?.app_name?.toUpperCase()
              }
              rules={[
                {
                  required: true,
                  message: `${t("wiki.Content")} ${t(
                    "table.header.isrequired"
                  )}`,
                },
              ]}
              initialValue={convertHtmlToText(msgTask)}
            >
              <div className="skeletonSelectDepartments grow">
                <Skeleton.Input
                  style={{
                    width: "100%",
                    minWidth: 150,
                    display: "inline-flex",
                  }}
                  active
                />
              </div>
            </Form.Item>
            <Form.Item
              label={
                t("integrations.descriptionTicketIn") +
                detailsOpenIntegration?.app_name?.toUpperCase()
              }
              rules={[
                {
                  required: true,
                  message: `${t("wiki.Content")} ${t(
                    "table.header.isrequired"
                  )}`,
                },
              ]}
              initialValue={convertHtmlToText(msgTask)}
            >
              <div className="skeletonSelectDepartments grow">
                <Skeleton.Input
                  style={{
                    width: "100%",
                    height: 74,
                    minWidth: 150,
                    display: "inline-flex",
                  }}
                  active
                />
              </div>
            </Form.Item>
          </>
        ) : (
          <>
            <Form.Item
              label={
                t("integrations.ticketTitleIn") +
                detailsOpenIntegration?.app_name?.toUpperCase()
              }
              name="name"
              rules={[
                {
                  required: true,
                  message: `${t("activities.label")} ${t(
                    "table.header.isrequired"
                  )}`,
                },
              ]}
            >
              <Input ref={inputRef} />
            </Form.Item>

            <Form.Item
              label={
                t("integrations.descriptionTicketIn") +
                detailsOpenIntegration?.app_name?.toUpperCase()
              }
              name="contents"
              rules={[
                {
                  required: true,
                  message: `${t("wiki.Content")} ${t(
                    "table.header.isrequired"
                  )}`,
                },
              ]}
              initialValue={convertHtmlToText(msgTask)}
            >
              <Input.TextArea
                rows={3}
                onKeyDown={(e) => {
                  // Ne rien faire pour la touche "Entrée" pour permettre la nouvelle ligne
                  if (e.key === "Enter") {
                    // alert("ok ")
                    e.stopPropagation();
                  }
                }}
              />
            </Form.Item>
          </>
        )}
        {renderIntegrationFields()}
      </Form>
    </Modal>
  );
};

export default ModalTicketGLPI;
