import { useState, useEffect, useCallback, useRef, memo } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import useActionCall from "../../../../pages/voip/helpers/ActionCall";
import {
  Divider,
  Popover,
  Tooltip,
  Empty,
  Button,
  Space,
  Typography,
  Spin,
  Skeleton,
  List,
  Dropdown,
} from "antd";
import {
  GlobalOutlined,
  InfoCircleOutlined,
  MessageOutlined,
  PhoneOutlined,
} from "@ant-design/icons";
import { FiCopy, FiMoreVertical } from "react-icons/fi";
import { HighlightSearchW } from "../../../../pages/voip/components";
import "./index.css";
import InfiniteScroll from "react-infinite-scroll-component";
import { toastNotification } from "../../../ToastNotification";
import { useNavigate } from "react-router-dom";
import { fetchPhoneBook } from "../../../../pages/voip/services/services";
import DisplayAvatar from "../../../../pages/voip/components/DisplayAvatar";
import { URL_ENV } from "index";
import { debounce } from "lodash";
import { openDrawerChat } from "new-redux/actions/voip.actions/handleDrawerChat";
import { Tb360View } from "react-icons/tb";
import { generateUrlToView360 } from "pages/voip/helpers/helpersFunc";
import { isGuestConnected } from "utils/role";
import VirtualList from "rc-virtual-list";
import { useInView } from "react-intersection-observer";

const loaderTemplate = (len = 2,ref) => (
  <div className="ml-2 mt-2" ref={ref}>
    {Array.from({ length: len }, (_, index) => (
      <Skeleton
        key={index}
        avatar
        paragraph={{
          rows: 0,
        }}
        active
      />
    ))}
  </div>
);

const Collegues = ({ search, handleActionWebPhone }) => {
  //
  const {ref, inView}=useInView()
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const call = useActionCall();
  const scrollableDivRef = useRef(null);
  const isGuest = isGuestConnected();

  const dialCode = useSelector(
    ({ user: { user } }) => user?.location?.dial_code
  );
  //
  const [shouldFetchData, setShouldFetchData] = useState(true);
  const [dataSource, setDataSource] = useState([]);
  const [totalData, setTotalData] = useState(0);
  const [page, setPage] = useState(1);
  const limit = 20;
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [popoverOpen, setPopoverOpen] = useState(null);
  const hasNextPage = totalData > dataSource.length
  // const [expandedKeys, setExpandedKeys] = useState([]);
  //
  // console.log({ dataSource });
  //
  useEffect(() => {
    setShouldFetchData(true);
  }, [page, search]);

  useEffect(() => {
    setPage(1);
    setIsLoadingData(true);
    if (scrollableDivRef.current) {
      scrollableDivRef.current.scrollTo(0, 0);
    }
  }, [search]);
  //
  const fetchData = useCallback(async () => {
    if (!shouldFetchData) return;
    try {
      setIsLoadingData(true);
      const modifiedSearch = /^\+?[0-9]+$/.test(search)
        ? search.replace("+", "00")
        : search;
      const {
        data: {
          data,
          meta: { total },
        },
      } = await fetchPhoneBook(
        null,
        page,
        limit,
        modifiedSearch,
        null,
        null,
        null,
        "colleagues"
      );
      setTotalData(Number(total));
      const handleColleagues =
        data.map((item) => ({
          ...item,
          image:
            item?.image &&
            `${
              URL_ENV?.REACT_APP_BASE_URL + URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
            }${item.image}`,
          name: item?.name?.replaceAll("_", " "),
          phones: item.phones?.map(([prefix, num]) => ({
            displayNum: `(${prefix}) ${num}`,
            // callNum: `${
            //   dialCode === prefix ? num : prefix?.replace("+", "00")
            // }${num}`,
            callNum:
              dialCode === prefix
                ? `${num}`
                : `${prefix?.replace("+", "00")}${num}`,
          })),
        })) || [];
      page === 1
        ? setDataSource(handleColleagues)
        : setDataSource((prevData) => [...prevData, ...handleColleagues]);
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? err.message : err);
    } finally {
      setIsLoadingData(false);
      setShouldFetchData(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shouldFetchData]);
  //
  useEffect(() => {
    fetchData();
  }, [fetchData]);
  //
  const loadMoreData = () => {
    setPage((p) => p + 1);
  };
  //

 
  //
  //

  useEffect(() => {
    if (!isLoadingData && hasNextPage && inView) {

      loadMoreData();
    }
  }, [inView, hasNextPage]);

  return (
    <Spin spinning={isLoadingData}>
      <div
     
        className="colleague-collapse  p-0 px-1 py-0.5"
      >
        {dataSource?.length ? (
          
            <div className="voip-list ">
              <List
                className={`membersList cursor-pointer`}
                itemLayout="vertical"
              
                locale={{
                  emptyText: isLoadingData ? (
                    <div className=" flex h-64 flex-row items-center justify-center space-x-2 text-xl">
                      {t("contacts.loadingDataTable")}
                      <span className="animate-bounce">...</span>
                    </div>
                  ) : (
                    ""
                  ),
                }}>
               <VirtualList
                data={dataSource}
              height={288}
                itemHeight={60}
                itemKey={"id"}
        
                style={{ padding: "4px 0" }}
              >{(item, index) => (
                  <>
                    <List.Item
                      style={{
                        alignItems: "center",
                      }}
                      key={item?.id}
                    >
              
                      <div className="flex flex-row items-center justify-between space-x-1">
                        <ConditionalPopover
                          item={item}
                          t={t}
                          call={call}
                          open={popoverOpen}
                          setOpen={setPopoverOpen}
                        >
                          <Tooltip title={t("voip.call")}>
                            <List.Item.Meta
                              onClick={() =>
                                item?.extension &&
                                !item?.phones?.length &&
                                call(item.extension, item?.id, 4)
                              }
                              avatar={
                                <DisplayAvatar
                                  name={item.name}
                                  urlImg={item.image}
                                  size={38}
                                />
                              }
                              title={
                                <p className="truncate font-semibold">
                                  {HighlightSearchW(item?.name, search)}
                                </p>
                              }
                              description={
                                <Space
                                  size={0}
                                  split={<Divider type="vertical" />}
                                >
                                  {item?.extension && (
                                    <p style={{ fontSize: 13 }}>
                                      {item.extension}
                                    </p>
                                  )}
                                  {item?.department && (
                                    <p
                                      className="max-w-48 truncate"
                                      style={{ fontSize: 13 }}
                                    >
                                      {item.department}
                                    </p>
                                  )}
                                </Space>
                              }
                            />
                          </Tooltip>
                        </ConditionalPopover>
                        <DropDownAction
                          item={item}
                          call={call}
                          t={t}
                          dispatch={dispatch}
                          navigate={navigate}
                          handleActionWebPhone={handleActionWebPhone}
                          isGuest={isGuest}
                        />
                      </div>
                    </List.Item>
                    {index < dataSource?.length - 1 && (
                      <Divider style={{ margin: "0.1rem" }} />
                    )}
                     {index == dataSource.length - 1 && hasNextPage && (
                      loaderTemplate(2,ref)
                    )}
                  </>
                )}
                </VirtualList>
                </List>
            </div>

       
     
        ) : !dataSource.length && isLoadingData ? (
          loaderTemplate(4)
        ) : (
          <div className="flex justify-center p-4 text-sm font-semibold">
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={"No colleagues Found"}
            />
          </div>
        )}
      </div>
    </Spin>
  );
};

export const copyIcon = (text) => (
  <div className="pl-1">
    <Typography.Paragraph
      copyable={{
        text: text,
        icon: [
          <FiCopy
            style={{
              color: "rgb(22, 119, 255)",
              marginTop: 3,
              fontSize: 16,
            }}
          />,
        ],
      }}
    />
  </div>
);

const ConditionalPopover = memo(
  ({ children, item, call, t, open, setOpen }) => {
    //
    if (item?.phones?.length) {
      const { id, name, extension, phones } = item;
      const firstName = name ? name.split(" ")?.[0] : "";
      return (
        <Popover
          open={open === id}
          onOpenChange={(state) => (state ? setOpen(id) : setOpen(null))}
          title={t("voip.callOn", { name: firstName })}
          content={
            <div className="space-y-1">
              {extension && (
                <div
                  key={extension}
                  className="ml-1 flex flex-row justify-between space-x-1"
                >
                  {extension}
                  <Space size={3}>
                    <Button
                      type="link"
                      size="small"
                      onClick={() => {
                        call(extension, id, 4);
                      }}
                      icon={
                        <PhoneOutlined rotate={100} style={{ fontSize: 17 }} />
                      }
                    />
                    {copyIcon(extension)}
                  </Space>
                </div>
              )}
              {phones?.map((phone, i) => (
                <div
                  key={i}
                  className="ml-1 flex flex-row justify-between space-x-1"
                >
                  {phone?.displayNum}
                  <Space size={3}>
                    <Button
                      type="link"
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        call(phone?.callNum, id, 4);
                      }}
                      icon={
                        <PhoneOutlined rotate={100} style={{ fontSize: 16 }} />
                      }
                    />
                    {copyIcon(phone?.callNum)}
                  </Space>
                </div>
              ))}
            </div>
          }
          trigger="click"
          arrow={false}
          placement="bottom"
        >
          {children}
        </Popover>
      );
    } else return children;
  }
);

const DropDownAction = memo(
  ({ item, call, t, dispatch, navigate, handleActionWebPhone, isGuest }) => {
    //
    const { id, name, extension, email, uuid, phones } = item;
    const family_id = 4;
    //
    const menuDropdown = () => {
      const items = [];
      //
      const pushItem = (key, icon, label, onClick, disabled, children) => {
        items.push({
          key,
          icon,
          label,
          onClick,
          disabled,
          children,
        });
      };
      //
      if (extension && phones?.length) {
        pushItem(
          extension + id,
          <PhoneOutlined
            className="text-slate-500"
            rotate={100}
            style={{ fontSize: 14 }}
          />,
          t("voip.call"),
          null,
          null,
          [
            {
              key: extension,
              label: (
                <div className="flex flex-row justify-between space-x-1.5">
                  {extension}
                  {copyIcon(extension)}
                </div>
              ),
              onClick: () => call(`${extension}`, id, family_id),
            },
            ...phones.map(({ callNum, copyNum, displayNum }) => ({
              key: displayNum,
              label: (
                <div className="flex flex-row justify-between space-x-2">
                  {displayNum}
                  {copyIcon(copyNum)}
                </div>
              ),
              onClick: () => call(callNum, id, family_id),
            })),
          ]
        );
      } else
        pushItem(
          extension + id,
          <PhoneOutlined
            className="text-slate-500"
            rotate={100}
            style={{ fontSize: 14 }}
          />,
          // <div className="flex flex-row justify-between space-x-2">
          //   {`${t("voip.call")} ${extension}`}
          //   {copyIcon(extension)}
          // </div>,
          t("voip.call"),
          () => call(extension, id, family_id)
        );
      if (uuid) {
        items.push({ type: "divider" });
        uuid &&
          pushItem(
            `chat-${uuid}`,
            <MessageOutlined
              className="text-slate-500"
              style={{ fontSize: 14 }}
            />,
            // <p className="max-w-[10rem] truncate">
            //   {`${t("voip.chatWith")} ${name?.split(" ")?.[0]}`}
            // </p>,
            t("voip.chat"),
            () => dispatch(openDrawerChat(uuid))
          );
      }

      if (id && family_id) {
        items.push({ type: "divider" });
        pushItem(
          "more-info",
          <InfoCircleOutlined
            className="text-slate-500"
            style={{ fontSize: 14 }}
          />,
          // `${t("voip.moreInfoWith")} ${name?.split(" ")?.[0]}`,
          t("voip.moreInfo"),
          () =>
            handleActionWebPhone("display_info", {
              name: name,
              id: id,
              familyId: family_id,
            }),
          isGuest
        );
        pushItem(
          "vue-360",
          <GlobalOutlined
            className="text-slate-500"
            style={{ fontSize: 14 }}
          />,

          // <p className="max-w-[10rem] truncate">
          //   {`${t("voip.view360")} ${name?.split(" ")?.[0]}`}
          // </p>,
          t("voip.view360"),
          () =>
            // console.log("hahahhahahahahhaahha");
            // console.log({ item });
            // dispatch({
            //   type: "SET_CONTACT_HEADER_INFO",
            //   payload: {
            //     ...item,
            //     family_id,
            //     phones: phones.length
            //       ? phones.map((phone) => [phone.displayNum])
            //       : [],
            //     email: email ? [email] : [],
            //   },
            // });
            navigate(generateUrlToView360(family_id, id, "v2")),
          isGuest
        );
      }

      return { items };
    };
    //
    return (
      <Dropdown
        trigger={["click"]}
        placement="bottomRight"
        menu={menuDropdown()}
      >
        <FiMoreVertical className="h-[18px] w-[18px] cursor-pointer text-gray-400 hover:text-gray-700" />
      </Dropdown>
    );
  }
);

// const collapseItems = useMemo(() => {
//   if (!dataSource?.length) return;
//   return (
//     dataSource.map((item) => ({
//       key: item.id,
//       label: <RenderLabelItem {...item} search={search} />,
//       children: (
//         <RenderChildItem
//           {...item}
//           isOpenDrawerChat={isOpenDrawerChat}
//           handleOpenMgsDrawer={handleOpenMgsDrawer}
//           call={call}
//           t={t}
//           location={location}
//         />
//       ),
//       extra: (
//         <Button
//           onClick={() => call(item?.extension, item?.id, 4)}
//           disabled={!item?.extension}
//           size="small"
//           type="link"
//           shape="circle"
//           icon={<PhoneOutlined rotate={100} style={{ fontSize: 18 }} />}
//         />
//       ),
//       showArrow: false,
//       style: {
//         background: "rgb(248 250 252)",
//         marginBottom: 0,
//         padding: expandedKeys.includes(`${item.id}`) && "0 8px",
//       },
//     })) || []
//   );
//   // eslint-disable-next-line react-hooks/exhaustive-deps
// }, [dataSource, expandedKeys, location, isOpenDrawerChat]);

// function RenderLabelItem({ name, image, extension, search }) {
//   //
//   const RenderAvatar = () => (
//     <DisplayAvatar urlImg={image} name={name} size={38} />
//   );
//   //
//   return (
//     <div className="flex flex-row space-x-2">
//       <RenderAvatar />
//       <div className="w-[12rem]">
//         <p className="truncate font-semibold leading-5">
//           {HighlightSearchW(name, search)}
//         </p>

//         <p className={"leading-4 text-slate-500"}>
//           {HighlightSearchW(`${extension}`, search)}
//         </p>
//       </div>
//     </div>
//   );
// }
// //
// function RenderChildItem({
//   id,
//   uuid,
//   email,
//   phones,
//   isOpenDrawerChat,
//   handleOpenMgsDrawer,
//   call,
//   t,
//   location,
// }) {
//   //
//   const copyIcon = (text) => (
//     <Typography.Paragraph
//       copyable={{
//         text: text,
//         icon: [
//           <FiCopy
//             style={{
//               color: "rgb(22, 119, 255)",
//               // marginTop: 2,
//               fontSize: 15,
//             }}
//           />,
//         ],
//       }}
//     />
//   );
//   //
//   const mobileContents = phones?.length ? (
//     <div className="flex flex-col space-y-2">
//       {phones.map(({ displayNum, callNum }, i) => (
//         <div className="flex flex-row justify-between space-x-2" key={i}>
//           <Tooltip title={t("voip.call")}>
//             <Typography.Link onClick={() => call(callNum, id, 4)}>
//               {displayNum}
//             </Typography.Link>
//           </Tooltip>

//           {copyIcon(callNum)}
//         </div>
//       ))}
//     </div>
//   ) : (
//     ""
//   );

//   return (
//     <div id={`panel-${id}`} key={id} className="flex justify-center">
//       <Space size={10} split={<Divider type="vertical" />}>
//         <Tooltip title="Visio">
//           <Button
//             type="link"
//             size="small"
//             shape="circle"
//             icon={<VideoCameraOutlined style={{ fontSize: 16 }} />}
//             onClick={() => message.warning("This Feature is not ready yet!")}
//           />
//         </Tooltip>
//         {/* <Tooltip title={phones?.length ? "Mobile" : ""}> */}
//         <Popover placement="right" content={mobileContents}>
//           <Button
//             type="link"
//             size="small"
//             shape="circle"
//             icon={<MobileOutlined style={{ fontSize: 16 }} />}
//             disabled={!phones?.length}
//           />
//         </Popover>
//         {/* </Tooltip> */}
//         <Tooltip title={!email ? "" : "Send Mail"}>
//           <Button
//             type="link"
//             size="small"
//             shape="circle"
//             icon={<MailOutlined style={{ fontSize: 16 }} />}
//             disabled={!email}
//             onClick={() => message.warning("This Feature is not ready yet!")}
//           />
//         </Tooltip>
//         <Tooltip
//           title={
//             !isOpenDrawerChat && uuid && !location.pathname === "/chat"
//               ? "Chat"
//               : ""
//           }
//         >
//           <Button
//             type="link"
//             size="small"
//             shape="circle"
//             icon={<MessageOutlined style={{ fontSize: 16 }} />}
//             // disabled={
//             //   isOpenDrawerChat || !uuid || location.pathname === "/chat"
//             // }
//             onClick={() => handleOpenMgsDrawer(uuid)}
//           />
//         </Tooltip>
//         <Tooltip title="History">
//           <Button
//             type="link"
//             size="small"
//             shape="circle"
//             icon={<HistoryOutlined style={{ fontSize: 16 }} />}
//             onClick={() => message.warning("This Feature is not ready yet!")}
//           />
//         </Tooltip>
//       </Space>
//     </div>
//   );
// }
//
export default Collegues;
