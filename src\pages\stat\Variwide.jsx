import React from "react";
import Highcharts from "highcharts";
import VariwideModule from "highcharts/modules/variwide";
import HighchartsReact from "highcharts-react-official";

if (typeof VariwideModule === "function") {
  VariwideModule(Highcharts);
}

const Variwide = ({ pivotData }) => {
  if (
    !pivotData ||
    !pivotData.rowKeys ||
    !pivotData.colKeys ||
    !pivotData.getAggregator
  ) {
    return <p className="text-center text-red-600">No Data</p>;
  }

  const rowKeys = pivotData.rowKeys;
  const colKeys = pivotData.colKeys;

  if (rowKeys.length === 0 && colKeys.length === 0) {
    return <p className="text-center text-red-600">No Data</p>;
  }

  const isRow = rowKeys.length > 0;
  const baseKeys = isRow ? rowKeys : colKeys;
  const seriesKeys = isRow ? colKeys : rowKeys;

  const categories = baseKeys.map((key) => key.join(" - "));

  const series =
    seriesKeys.length > 0
      ? seriesKeys.map((seriesKey) => ({
          name: seriesKey.join(" - ") || "Total",
          data: baseKeys.map((baseKey) => {
            const r = isRow ? baseKey : seriesKey;
            const c = isRow ? seriesKey : baseKey;
            const value = pivotData.getAggregator(r, c)?.value() || 0;
            return {
              y: value,
              z: value,
            };
          }),
        }))
      : [
          {
            name: "Total",
            data: baseKeys.map((baseKey) => {
              const r = isRow ? baseKey : [];
              const c = isRow ? [] : baseKey;
              const value = pivotData.getAggregator(r, c)?.value() || 0;
              return {
                y: value,
                z: value,
              };
            }),
          },
        ];

  const options = {
    chart: {
      type: "variwide",
    },
    title: {
      text: `📊
            ${pivotData.props.rows.length ? pivotData.props.rows.join(" ") : ""}
            ${
              pivotData.props.cols.length
                ? " : " + pivotData.props.cols.join(" ")
                : ""
            }`,
      align: "left",
      dispalay: "block",
      fontFamily: "Arial, sans-serif",
      style: {
        fontWeight: "normal",
        fontSize: "20px",
        fontFamily: "Inter, sans-serif",
      },
    },
    xAxis: {
      categories: categories,
      gridLineWidth: 0,
      lineWidth: 1,
    },
    tooltip: {
      shared: true,
      shadow: true,
      pointFormat: "Value: <b>{point.y}</b><br>Width (z): <b>{point.z}</b><br>",
    },
    plotOptions: {
      series: {
        dataLabels: {
          enabled: true,
          format: "{point.y:.0f}",
        },
        borderRadius: 3,
        colorByPoint: true,
        pointPadding: 0,
        groupPadding: 0,
      },
    },
    caption: {
      text: "Column widths are proportional to z values.",
    },
    legend: {
      align: "left",
      verticalAlign: "top",
      backgroundColor:
        Highcharts.defaultOptions.legend?.backgroundColor ||
        "rgba(255,255,255,0.25)",
    },
    credits: {
      enabled: false,
    },
    series: series,
  };

  return (
    <div
      style={{ height: "100%", width: "100%" }}
      className="highcharts-figure"
    >
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
};

export default Variwide;
