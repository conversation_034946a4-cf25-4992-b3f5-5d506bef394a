.custom-dropdown-global-search {
  transform: translateX(-50%) !important;
  left: 50% !important;
  padding: 0;
  z-index: 1050 !important;
}

.custom-list-item-global-search .ant-list-item-action {
  display: flex;
  justify-content: flex-end;
  margin-top: auto;
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.custom-list-item-global-search mark {
  background-color: rgb(253,226,147);
  color: inherit;
}

.global-search-list .ant-list-vertical .ant-list-item .ant-list-item-meta {
  margin-block-end: 0px !important;
}

.global-search-list .ant-list .ant-list-item {
  @apply p-1.5;
  @apply hover:bg-slate-100;
}

.global-search-list .ant-list-item-meta-description {
  @apply truncate text-xs;
  @apply font-medium text-slate-700;
}

.global-search-list .ant-list-item-meta-avatar {
  margin-inline-end: 10px !important;
}

.global-search-list .ant-list-empty-text {
  padding: 0 !important;
}

.dropDown-global-search::-webkit-scrollbar {
  width: 8px;
  /* height: 10px; */
}

.dropDown-global-search::-webkit-scrollbar-thumb {
   -webkit-border-radius: 10px;
  background: #e5e5e5;
  border-radius: 2px;
}

.dropDown-global-search::-webkit-scrollbar-track {
  background-color: white;
  -webkit-border-radius: 10px;
  border-radius: 2px;
}

.dropDown-global-search::-webkit-scrollbar-thumb:hover {
  background-color: #eaeaea;
}

.custom-search-modal.ant-modal .ant-modal-content {
  padding: 0 !important;
}
.custom-search-modal.ant-modal .ant-modal-footer {
  margin-top: 0 !important;
}
.custom-search-modal.ant-modal .ant-modal-header {
  margin-bottom: 0 !important;
}
