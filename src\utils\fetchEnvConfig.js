import { clearAllExcept } from "new-redux/actions/user.actions/getUser";
import { LogoutLink } from "pages/layouts/chat/utils/ConversationUtils";

export const fetchEnvConfig = async () => {
  let result = null;
  const url = new URL(window.location.href);
  const accessToken =
    url.searchParams.get("access_token") ?? localStorage.getItem("accessToken");
  const nameOfURL =
    "URL_ENV_LOCAL_" + (accessToken ? "" : "GENERAL_") + url.hostname;
  if (localStorage.getItem(nameOfURL)) {
    result = JSON.parse(localStorage.getItem(nameOfURL));
  } else {
    let headers = {};
    clearAllExcept();
    try {
      if (accessToken) {
        headers = {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
          "cache-control": "no-cache",
        };
      }
      const response = await fetch(
        `${
          process.env.REACT_APP_DEFAULT_AUTH_DOMAIN +
          process.env.REACT_APP_SUFFIX_API
        }env-front`,
        {
          method: "GET",
          headers,
        }
      );
      if (!response.ok) {
        throw new Error("Network response was not ok");
      }
      const res = await response.json();
      localStorage.setItem(nameOfURL, JSON.stringify(res));
      result = res;
    } catch (error) {
      console.log("Fetch error ", error);
      return LogoutLink();
    }
  }
  return result;
};
