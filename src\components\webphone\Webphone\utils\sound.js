var contextClass =
  window.AudioContext ||
  window.webkitAudioContext ||
  window.mozAudioContext ||
  window.oAudioContext ||
  window.msAudioContext;

if (contextClass) {
  // Web Audio API is available.
  var context = new contextClass();
}

let oscillator1, oscillator2;

const dialTone = (freq1, freq2) => {
  oscillator1 = context.createOscillator();
  oscillator1.type = "sine";
  oscillator1.frequency.value = freq1;
  let gainNode1 = context.createGain
    ? context.createGain()
    : context.createGainNode();
  oscillator1.connect(gainNode1);
  gainNode1.connect(context.destination);
  gainNode1.gain.value = 0.1;
  oscillator1.start ? oscillator1.start(0) : oscillator1.noteOn(0);

  oscillator2 = context.createOscillator();
  oscillator2.type = "sine";
  oscillator2.frequency.value = freq2;
  let gainNode2 = context.createGain
    ? context.createGain()
    : context.createGainNode();
  oscillator2.connect(gainNode2);
  gainNode2.connect(context.destination);
  gainNode2.gain.value = 0.1;
  oscillator2.start ? oscillator2.start(0) : oscillator2.noteOn(0);
};

export const playToneDTMF = (freq1, freq2) => {
  if (typeof oscillator1 != "undefined") oscillator1.disconnect();
  if (typeof oscillator2 != "undefined") oscillator2.disconnect();
  dialTone(freq1, freq2);
};

export const stopToneDTMF = () => {
  if (typeof oscillator1 != "undefined") oscillator1.disconnect();
  if (typeof oscillator2 != "undefined") oscillator2.disconnect();
};
