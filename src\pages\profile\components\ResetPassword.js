/* eslint-disable jsx-a11y/anchor-is-valid */
import { useEffect, useState } from "react";
import { Button, Form, Input, Modal, Tooltip, notification } from "antd";
import { sendForgotPasswordEmail, updatePassword } from "../services";
import { toastNotification } from "../../../components/ToastNotification";
import NotifUpdatePwd from "./NotifUpdatePwd";

const ResetPassword = ({ open, setOpen, userEmail, t }) => {
  const [form] = Form.useForm();

  const [timer, setTimer] = useState(null);
  const [resendText, setResendText] = useState(t("profile.forgotPwd"));

  const [loading, setLoading] = useState(false);

  const handleCancel = () => {
    form.resetFields();
    setOpen(false);
  };

  const onFinish = async (values) => {
    try {
      setLoading(true);
      const formData = new FormData();
      formData.append("old_password", values?.oldPassword);
      formData.append("new_password", values?.newPassword);
      const { status } = await updatePassword(formData);
      if (status === 200) {
        const key = `open${Date.now()}`;

        const closeNotification = () => {
          notification.close(key);
        };

        notification.open({
          message: t("profile.pwdUpdateSuccess"),
          type: "success",
          key,
          duration: 0,
          description: (
            <NotifUpdatePwd
              password={values?.newPassword}
              closeNotification={closeNotification}
              t={t}
            />
          ),
        });
        handleCancel();
        setLoading(false);
      }
    } catch (err) {
      const {
        response: { status },
        message,
      } = err;
      if (status === 422)
        form.setFields([
          {
            name: "oldPassword",
            errors: [t("profile.theOldPwdIncorrect")],
          },
        ]);
      else toastNotification("error", t("toasts.somethingWrong"), "topRight");
      setLoading(false);
      throw new Error(message || err);
    }
  };

  const footer = (
    <div className="mt-4 flex justify-end space-x-8">
      <Button onClick={handleCancel} disabled={loading}>
        {t("profile.cancel")}
      </Button>
      <Button type="primary" onClick={() => form.submit()} loading={loading}>
        {t("profile.confirm")}
      </Button>
    </div>
  );

  const handleForgotPassword = async () => {
    try {
      const formData = new FormData();
      formData.append("email", userEmail);
      const { status } = await sendForgotPasswordEmail(formData);
      if (status === 200) {
        setTimer(30);
        setResendText(t("profile.resendInXSec", { x: 30 }));
        toastNotification(
          "success",
          <span>
            {t("profile.anEmailHasBeenSent")} <strong>{userEmail}</strong>
          </span>,
          "topRight",
          5
        );
      }
    } catch (err) {
      toastNotification("error", t("toasts.somethingWrong"), "topRight");

      throw new Error(err?.message || err);
    }
  };

  useEffect(() => {
    let timerInterval;
    if (timer > 0) {
      timerInterval = setInterval(() => {
        setTimer((prevTimer) => {
          const newTimer = prevTimer - 1;
          setResendText(t("profile.resendInXSec", { x: newTimer }));
          return newTimer;
        });
      }, 1000);
    } else if (timer === 0) {
      clearInterval(timerInterval);
      setResendText(t("profile.forgotPwd"));
    }

    return () => {
      clearInterval(timerInterval);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [timer]);

  return (
    <Modal
      title={t("profile.editPwd")}
      open={open}
      onCancel={() => setOpen(false)}
      footer={footer}
      closeIcon={<></>}
    >
      <Form
        layout="vertical"
        form={form}
        onFinish={onFinish}
        autoComplete="off"
      >
        <Form.Item
          label={
            <span>
              {t("profile.oldPwd")}{" "}
              {timer ? (
                <span
                  style={{
                    color: "grey",
                    cursor: "not-allowed",
                  }}
                >
                  ({resendText})
                </span>
              ) : (
                <Tooltip title={t("profile.msgForgotPwd")}>
                  <a
                    onClick={handleForgotPassword}
                    style={{
                      // color: "blue",
                      textDecoration: "underline",
                      cursor: "pointer",
                    }}
                  >
                    ({resendText})
                  </a>
                </Tooltip>
              )}
            </span>
          }
          name="oldPassword"
          rules={[
            {
              required: true,
              message: t("profile.fieldRequired"),
            },
          ]}
        >
          <Input.Password />
        </Form.Item>

        <Form.Item
          label={t("profile.newPwd")}
          name="newPassword"
          rules={[
            {
              required: true,
              message: "This field is required",
            },
            {
              pattern: new RegExp(".{8,}"),
              message: `Minimum eight (8) characters!`,
            },
          ]}
        >
          <Input.Password />
        </Form.Item>
        <Form.Item
          label={t("profile.confirmNewPwd")}
          name="confirmNewPassword"
          rules={[
            {
              required: true,
              message: "This field is required",
            },
            {
              pattern: new RegExp(".{8,}"),
              message: `Minimum eight (8) characters!`,
            },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue("newPassword") === value) {
                  return Promise.resolve();
                }
                return Promise.reject(
                  new Error("The two passwords that you entered do not match!")
                );
              },
            }),
          ]}
        >
          <Input.Password />
        </Form.Item>
      </Form>
    </Modal>
  );

  //
  // const [form] = Form.useForm();
  // const passwordRegex = /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[a-zA-Z]).{10,}$/;
  // //
  // const [password, setPassword] = useState(null);
  // //
  // const handleGeneratePassword = () => {
  //   const newPassword = generatePassword();
  //   setPassword(newPassword);
  //   form.setFieldsValue({
  //     new_password: newPassword,
  //     confirm_new_password: newPassword,
  //   });
  // };
  // //
  // const onFinish = (values) => {
  //   console.log(values);
  // };
  // //
  // const onFinishFailed = (errorInfo) => {
  //   // console.log(errorInfo, errorInfo.errorFields[0].name)
  //   form.scrollToField(errorInfo.errorFields[0].name);
  //   toastNotification(
  //     "error",
  //     "Form validation failed, check all fields",
  //     "topRight"
  //   );
  // };
  // //
  // const handleReset = () => {
  //   form.resetFields();
  //   setPassword(null);
  // }
  // //
  // return (
  //   <Form
  //     form={form}
  //     layout={"vertical"}
  //     onFinish={onFinish}
  //     onFinishFailed={onFinishFailed}
  //     autoComplete="off"
  //     scrollToFirstError
  //     style={{
  //       maxWidth: 350,
  //     }}
  //   >
  //     {!userID  && (
  //       <Form.Item
  //         label="Old Password"
  //         name="old_password"
  //         rules={[
  //           {
  //             required: true,
  //             message: `The 'Old Password' field is required!`,
  //           },
  //         ]}
  //       >
  //         <Input.Password />
  //       </Form.Item>
  //     )}
  //     <Form.Item
  //       label="New Password"
  //       name="new_password"
  //       rules={[
  //         {
  //           required: true,
  //           message: `The 'New Password' field is required!`,
  //         },
  //         {
  //           pattern: passwordRegex,
  //           message:
  //             "Minimum ten (10) characters, at least one uppercase letter, one lowercase letter and one number!",
  //         },
  //       ]}
  //     >
  //       <Input.Group compact>
  //         <Input.Password

  //           value={password}
  //           onChange={(e) => setPassword(e.target.value)}
  //         />

  //         <Tooltip placement="top" title="Copy">
  //           <Button
  //             onClick={() => {
  //               navigator.clipboard.writeText(password);
  //               message.open({
  //                 type: "success",
  //                 content: "Password copied",
  //               });
  //             }}
  //             icon={<CopyOutlined className="text-slate-500" />}
  //           />
  //         </Tooltip>
  //         <Tooltip placement="topRight" title="Generate Password">
  //           <Button
  //             type="primary"
  //             onClick={handleGeneratePassword}
  //             icon={<UnlockOutlined />}
  //           />
  //         </Tooltip>
  //       </Input.Group>
  //     </Form.Item>
  //     <Form.Item
  //       label="Confirm New Password"
  //       name="confirm_new_password"
  //       rules={[
  //         {
  //           required: true,
  //           message: `The 'Confirm New Password' field is required!`,
  //         },
  //         {
  //           pattern: passwordRegex,
  //           message:
  //             "Minimum ten (10) characters, at least one uppercase letter, one lowercase letter and one number!",
  //         },
  //       ]}
  //     >
  //       <Input.Password />
  //     </Form.Item>
  //     <Form.Item>
  //       <div className="flex flex-row space-x-2">
  //         <Button type="primary" htmlType="submit">
  //           Submit
  //         </Button>
  //         <Button htmlType="button" onClick={handleReset}>
  //           Reset
  //         </Button>
  //         {userID && <Button
  //         danger
  //         onClick={() => {handleReset(); setOpen(false)}}
  //         >
  //           Cancel
  //           </Button>}
  //       </div>
  //     </Form.Item>
  //   </Form>
  // );
};

export default ResetPassword;
//
