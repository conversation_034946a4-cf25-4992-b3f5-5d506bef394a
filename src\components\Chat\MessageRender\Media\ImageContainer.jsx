import { Button, Skeleton, Tooltip } from "antd";
import { Ava<PERSON><PERSON><PERSON>, ImageRender } from "components/Chat";
import React, { Suspense, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { Link } from "react-router-dom";
import { URL_ENV } from "index";
import {
  getName,
  handleDownloadFile,
} from "pages/layouts/chat/utils/ConversationUtils";
import { moment_timezone } from "App";
import { HiOutlineCalendar } from "react-icons/hi2";
import { DownloadOutlined } from "@ant-design/icons";

const ImageContainer = ({ img, index, GoToComponent, array }) => {
  const { t } = useTranslation("common");
  const [loadingDownload, setLoading] = useState(false);
  const currentUser = useSelector((state) => state.chat.currentUser);
  return (
    <div
      className={`
    flex ${
      GoToComponent ? "my-2.5" : ""
    } file  w-full  items-center justify-between
    `}
    >
      <div className="w-full">
        <div
          className={`flex  flex-col`}
          style={{
            whiteSpace: "nowrap",
            overflow: "hidden",
            textOverflow: "ellipsis",
          }}
        >
          <div className="flex w-full flex-col space-y-2 text-sm  ">
            <div className="flex w-full items-center justify-between">
              <Link className="flex w-full items-center space-x-1">
                <Suspense
                  fallback={Array.from({ length: 6 }, (_, i) => i + 1).map(
                    (item) => (
                      <Skeleton.Image
                        className="h-32 w-40"
                        key={`image_key_${item}`}
                        active
                      />
                    )
                  )}
                >
                  <ImageRender
                    fromInfo={!!GoToComponent}
                    array={array}
                    file={img}
                    width={40}
                    index={index}
                    height={40}
                  />
                </Suspense>

                <div className="flex flex-col space-y-1">
                  <span
                    className={`whitespace-normal font-medium  ${
                      GoToComponent ? " line-clamp-2 truncate text-xs" : ""
                    }`}
                  >
                    {img.file_name}
                  </span>
                  <span className="text-xs font-medium ">{img.size}</span>
                </div>
              </Link>

              {GoToComponent && <>{GoToComponent}</>}
            </div>

            <div className="flex w-full items-center  justify-between text-xs font-medium text-black">
              <div className="flex items-center space-x-3">
                {img?.user_info && GoToComponent && (
                  <div className="flex items-center space-x-1">
                    <AvatarChat
                      isPublic={false}
                      type="user"
                      fontBold="font-semibold"
                      fontSize="10px"
                      hasImage={img?.user_info?.image}
                      height={6}
                      width={6}
                      size={24}
                      url={
                        img?.user_info?._id === currentUser?._id
                          ? URL_ENV?.REACT_APP_BASE_URL +
                            URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                            img?.user_info?.image
                          : URL_ENV?.REACT_APP_BASE_URL +
                            URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                            img?.user_info?.image
                      }
                      name={getName(img?.user_info?.name, "avatar")}
                    />
                    <Tooltip title={img?.user_info?.name}>
                      <span className=" max-w-md truncate whitespace-normal">
                        {getName(img?.user_info?.name, "name")}
                      </span>
                    </Tooltip>
                  </div>
                )}

                <div className="flex items-center space-x-1">
                  <HiOutlineCalendar className="text-sm font-medium" />
                  <span>
                    {moment_timezone(img.created_at).format(
                      " DD MMM YYYY HH:mm"
                    )}
                  </span>
                </div>
              </div>
              <div>
                <Tooltip
                  title={t("import.downloadFileWithTitle", {
                    filename: img.file_name,
                  })}
                >
                  <Button
                    loading={loadingDownload}
                    onClick={(e) => handleDownloadFile(e, img, setLoading)}
                    shape={GoToComponent && "  circle"}
                    icon={<DownloadOutlined />}
                    size="small"
                  >
                    {GoToComponent ? "" : t("import.download")}
                  </Button>
                </Tooltip>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ImageContainer;
