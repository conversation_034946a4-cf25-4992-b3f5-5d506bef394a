import { GET_TYPES_SUCCESS, GET_TYPES_ERROR, RESET_STATE } from "../constants";

const initialState = {
  types: [],
  errors: {},
  isLoading: false,
};

const types = (state = initialState, action) => {
  const { type, payload } = action;
  switch (type) {
    case GET_TYPES_SUCCESS:
      return {
        ...state,
        isLoading: false,
        types: payload,
      };

    case GET_TYPES_ERROR:
      return {
        ...state,
        isLoading: false,
        errors: payload,
      };
    case RESET_STATE: {
      return initialState;
    }
    default:
      return state;
  }
};

export default types;
