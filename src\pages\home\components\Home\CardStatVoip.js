import { Card, Col, Row, Statistic } from "antd";
import {
  backgroundImagecard,
  GoTo,
  gridStyle,
  stylesCard,
} from "pages/home/<USER>";
import React, { useMemo } from "react";
import {
  HiPhoneIncoming,
  HiPhoneMissedCall,
  HiPhoneOutgoing,
} from "react-icons/hi";
import { MdPhoneMissed } from "react-icons/md";
import { PieChartWithLegend } from "../ChartsDashboard";
import { useSelector } from "react-redux";
import CountUp from "react-countup";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import EmptyPage from "components/EmptyPage";

const CardStatVoip = () => {
  const {
    missedUnreturnedCalls,
    missedTodayCall,
    outgoingTodayCall,
    receivedTodayCall,
    loading,
  } = useSelector((state) => state.dashboardRealTime);
  const [t] = useTranslation("common");
  const { user } = useSelector((state) => state.user);
  const formatter = (value) => <CountUp end={value} separator="," />;
  const dataLogs = useMemo(() => {
    const data = [
      {
        name: t(`dashboard.receivedCalls`, {
          plural: receivedTodayCall > 1 ? "s" : "",
          pluriel: receivedTodayCall > 1 ? "x" : "",
        }),
        y: Number(receivedTodayCall),
        color: "#3f8600",
        // color: lightenColor("#3f8600", 0.7),
      },
      {
        name: t(`dashboard.missedCalls`, {
          plural: missedTodayCall > 1 ? "s" : "",
          pluriel: missedTodayCall > 1 ? "x" : "",
        }),
        y: Number(missedTodayCall),
        color: "#cf1322",
        // color: lightenColor("#cf1322", 0.7),
      },
      {
        name: t(`dashboard.outgoingCalls`, {
          plural: outgoingTodayCall > 1 ? "s" : "",
          pluriel: outgoingTodayCall > 1 ? "x" : "",
        }),
        y: Number(outgoingTodayCall),
        color: "#3b82f6",
        // color: lightenColor("#3b82f6", 0.7),
      },
      {
        name: t(`voip.not_recalled`, {
          plural: missedUnreturnedCalls > 1 ? "s" : "",
          pluriel: missedUnreturnedCalls > 1 ? "x" : "",
        }),
        y: Number(missedUnreturnedCalls),
        color: "#dc2626",
      },
    ];

    return {
      data,
      name: "",
    };
  }, [
    receivedTodayCall,
    missedTodayCall,
    outgoingTodayCall,
    missedUnreturnedCalls,
    t,
  ]);
  const navigate = useNavigate();
  return (
    <Card
      style={{ backgroundImage: backgroundImagecard }}
      styles={{ ...stylesCard }}
      title={
        <div className="flex items-center justify-between  pr-2">
          <span>{t("menu2.logs")}</span>&nbsp;
          <GoTo
            to={"2"}
            title={t("menu2.logs")}
            navigate={navigate}
            t={t}
            user={user}
          />
        </div>
      }
    >
      <Row>
        <Col span={12} style={{ paddingLeft: 0, paddingRight: 0 }}>
          <Card style={{ backgroundImage: backgroundImagecard, border: 0 }}>
            <Card.Grid
              hoverable={false}
              style={{ ...gridStyle, borderStartStartRadius: 6 }}
            >
              <Statistic
                formatter={
                  typeof receivedTodayCall === "number" ? formatter : null
                }
                title={t(`dashboard.receivedCalls`, {
                  plural: receivedTodayCall > 1 ? "s" : "",
                  pluriel: receivedTodayCall > 1 ? "x" : "",
                })}
                value={receivedTodayCall}
                valueStyle={{
                  color: "#3f8600",
                }}
                prefix={
                  <HiPhoneIncoming className="h-[22px] w-[22px] fill-green-600 " />
                }
              />
            </Card.Grid>
            <Card.Grid hoverable={false} style={gridStyle}>
              <Statistic
                formatter={
                  typeof missedTodayCall === "number" ? formatter : null
                }
                title={t(`dashboard.missedCalls`, {
                  plural: missedTodayCall > 1 ? "s" : "",
                  pluriel: missedTodayCall > 1 ? "x" : "",
                })}
                value={missedTodayCall}
                valueStyle={{
                  color: "#cf1322",
                }}
                prefix={
                  <MdPhoneMissed className="h-[22px] w-[22px] text-[#EF4444] " />
                }
              />
            </Card.Grid>

            <Card.Grid
              hoverable={false}
              style={{ ...gridStyle, borderEndStartRadius: 6 }}
            >
              <Statistic
                formatter={
                  typeof outgoingTodayCall === "number" ? formatter : null
                }
                title={t(`dashboard.outgoingCalls`, {
                  plural: outgoingTodayCall > 1 ? "s" : "",
                  pluriel: outgoingTodayCall > 1 ? "x" : "",
                })}
                value={outgoingTodayCall}
                valueStyle={{
                  color: "#3b82f6",
                }}
                prefix={
                  <HiPhoneOutgoing className=" h-[22px] w-[22px] fill-blue-500" />
                }
              />
            </Card.Grid>
            <Card.Grid hoverable={false} style={gridStyle}>
              <Statistic
                formatter={
                  typeof missedUnreturnedCalls === "number" ? formatter : null
                }
                title={t(`voip.not_recalled`, {
                  plural: missedUnreturnedCalls > 1 ? "s" : "",
                  pluriel: missedUnreturnedCalls > 1 ? "x" : "",
                })}
                value={missedUnreturnedCalls}
                valueStyle={{
                  color: "#dc2626",
                }}
                prefix={
                  <HiPhoneMissedCall className=" h-[22px] w-[22px] fill-red-600" />
                }
              />
            </Card.Grid>
          </Card>
        </Col>
        <Col
          span={12}
          style={{
            paddingLeft: 0,
            width: "100%",
            height: "100%",
            borderTopRightRadius: 6,
            borderBottomRightRadius: 6,
            background: "white",
            borderLeft: "1px solid #f0f0f0",
          }}
        >
          {dataLogs?.data
            ?.map((el) => el.y)
            ?.reduce(
              (accumulator, currentValue) => accumulator + currentValue,
              0
            ) > 0 ? (
            <PieChartWithLegend
              height={173}
              data={!loading ? dataLogs : { data: [], name: "" }}
              alignLegend={{
                align: "right",
                verticalAlign: "middle",
                layout: "vertical",
                enabled: false,
              }}
              exporting={false}
            />
          ) : (
            <div className="h-[173px]">
              <EmptyPage />
            </div>
          )}
        </Col>
      </Row>
    </Card>
  );
};

export default CardStatVoip;
