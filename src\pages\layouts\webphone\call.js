import { useState, useEffect, useCallback, useMemo } from "react";
import { useSelector } from "react-redux";
import { useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import {
  EnterOutlined,
  InfoCircleTwoTone,
  LoadingOutlined,
  MessageOutlined,
} from "@ant-design/icons";
import {
  Button,
  Space,
  AutoComplete,
  Typography,
  Tooltip,
  Empty,
  Input,
  Skeleton,
} from "antd";
import MainService from "../../../services/main.service";
import { toastNotification } from "../../../components/ToastNotification";
import { debounce } from "lodash";
import { humanDate, suggestLog } from "../../voip/helpers/helpersFunc";
import { HighlightSearchW } from "../../voip/components";
import { FiCopy, FiSearch } from "react-icons/fi";
import useActionCall from "../../voip/helpers/ActionCall";
import "./index.css";
import DisplayAvatar from "../../voip/components/DisplayAvatar";
import { URL_ENV } from "index";
import { useDispatch } from "react-redux";
import { openDrawerChat } from "new-redux/actions/voip.actions/handleDrawerChat";
import { isGuestConnected } from "utils/role";

export const loaderOptionTemplate = (rowNumber = 4, className) => (
  <div className={className ? className : "ml-2 mt-2"}>
    {Array.from({ length: rowNumber }, (_, index) => (
      <Skeleton
        key={index}
        avatar
        paragraph={{
          rows: 0,
        }}
        active
      />
    ))}
  </div>
);

const Call = ({ search, setSearch, inputRef }) => {
  //
  const [t] = useTranslation("common");
  const actionCall = useActionCall();
  const dispatch = useDispatch();
  // const inputRef = useRef(null);
  const location = useLocation();
  const isGuest = isGuestConnected();
  const user = useSelector((state) => state.user.user);
  const userPoste = `${user?.extension}`;
  const log = useSelector((state) => state.voip.logs);
  const dialCode = useSelector(
    ({ user: { user } }) => user?.location?.dial_code
  );
  //
  const [searchOptions, setSearchOptions] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [displayValue, setDisplayValue] = useState("");
  const [selectedFromDropdown, setSelectedFromDropdown] = useState(false);
  // const [search, setSearch] = useState("");
  //
  const handleOpenMgsDrawer = (uuid) => {
    dispatch(openDrawerChat(uuid));
  };
  //
  const fetchSearchOptions = useCallback(async () => {
    // if (isGuest) return;
    try {
      setIsLoading(true);
      if (!search?.length) return;
      const modifiedSearch = /^\+?[0-9]+$/.test(search)
        ? search.replace("+", "00")
        : search;
      const { data } = await MainService.searchByNumOrName({
        key: modifiedSearch,
      });
      if (!data?.length) {
        setSearchOptions(
          Number(modifiedSearch)
            ? [
                {
                  key: "key_notInContacts",
                  label: "Pas dans vos contacts",
                  values: [
                    {
                      extension: null,
                      family_id: null,
                      group: 0,
                      id: "unknown",
                      phone: {
                        callNum: modifiedSearch,
                        copyNum: modifiedSearch,
                        displayNum: modifiedSearch,
                      },
                      is_member: 0,
                      queue: 0,
                    },
                  ],
                },
              ]
            : []
        );
        return;
      }
      const transformedOptions = transformData(data, search, dialCode, t);
      setSearchOptions(transformedOptions);
    } catch (err) {
      if (err?.response?.status !== 401) {
        setSearchOptions([]);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
      throw new Error(err?.message ? err.message : err);
    } finally {
      setIsLoading(false);
    }
  }, [dialCode, /*isGuest,*/ search, t]);

  useEffect(() => {
    fetchSearchOptions();
  }, [fetchSearchOptions]);
  //
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedSearch = useCallback(
    debounce((nextValue) => setSearch(nextValue), 300),
    []
  );

  const handleSearchChange = (e) => {
    const inputValue = e.target.value;

    if (/^[0-9\s]*$/.test(inputValue)) {
      const searchText = inputValue.replace(/\s+/g, "");
      setDisplayValue(searchText);
      debouncedSearch(searchText.trim());
    } else {
      setDisplayValue(inputValue);
      debouncedSearch(inputValue.trim());
    }
  };

  //
  const suggestLogOptions = useMemo(
    () => suggestLog(log, userPoste, t),
    [log, t, userPoste]
  );
  //
  const options = useMemo(() => {
    if (!search.length && (!suggestLogOptions || !suggestLogOptions.length))
      return [];
    else if (!search.length) {
      return [
        {
          label: (
            <span className="text-xs font-semibold">{t("voip.suggested")}</span>
          ),
          options: suggestLogOptions.map((item) =>
            renderItem(item, search, handleOpenMgsDrawer, actionCall, t)
          ),
        },
      ];
    } else {
      return searchOptions.map((item, i) => ({
        label: <span className="text-xs font-semibold	">{item.label}</span>,
        options: item.values
          .slice(0, 10)
          .map((value) =>
            renderItem(value, search, handleOpenMgsDrawer, actionCall, t)
          ),
      }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    // actionCall,
    // handleOpenMgsDrawer,
    location,
    search,
    searchOptions,
    suggestLogOptions,
    t,
  ]);
  //
  // console.log({ isOpenDrawerChat });
  //
  return (
    <div className="autoComplete-input-call mt-2 flex justify-center">
      <AutoComplete
        // popupClassName="bg-slate-50"
        popupClassName="autoComplete-input-call pr-0 pb-2 rounded-t-sm"
        style={{
          width: "94%",
          cursor: "default",
        }}
        // open={false}
        notFoundContent={
          isLoading ? (
            loaderOptionTemplate(4)
          ) : (
            <div className="flex justify-center p-4 text-sm font-semibold">
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description={"No Contacts Found"}
              />
            </div>
          )
        }
        options={options}
        value={displayValue}
        onSelect={(value, option) => {
          // console.log({ value, option });
          if (option?.value) {
            Number(search) && setSelectedFromDropdown(true);
            actionCall(option.value, option?.id, option?.family_id);
          }
        }}
      >
        <Input
          ref={inputRef}
          style={{ width: "100%" }}
          onChange={handleSearchChange}
          onPressEnter={() => {
            let timer = setTimeout(() => {
              if (Number(search) && !selectedFromDropdown && !isGuest)
                actionCall(search);
              if (selectedFromDropdown) setSelectedFromDropdown(false);
              return () => clearTimeout(timer);
            }, 100);
          }}
          prefix={
            <FiSearch className="text-slate-400" style={{ fontSize: 16 }} />
          }
          suffix={
            <div className="flex items-center space-x-2">
              {Number(search) && !selectedFromDropdown && !isGuest ? (
                <EnterOutlined
                  style={{
                    fontSize: 15,
                  }}
                />
              ) : null}
              {isLoading ? (
                <LoadingOutlined
                  style={{
                    fontSize: 16,
                    color: "rgb(22, 119, 255)",
                  }}
                />
              ) : null}
              <Tooltip title={t("voip.infoInputWebPhone")}>
                <InfoCircleTwoTone
                  style={{
                    fontSize: 14,
                    cursor: "help",
                  }}
                />
              </Tooltip>
            </div>
          }
          placeholder={t("voip.inputMessage")}
        />
      </AutoComplete>
    </div>
  );
};
//
const copyIcon = (text) => (
  <Typography.Paragraph
    copyable={{
      text: text,
      icon: [
        <FiCopy
          style={{
            color: "rgb(22, 119, 255)",
            marginTop: "4px",
            fontSize: "16px",
          }}
        />,
      ],
    }}
  />
);
//
function generateUniqueID() {
  const timestamp = new Date().getTime();
  const randomSegment = Math.random().toString(36).substring(2, 15);
  const uniqueID = `key_${timestamp}_${randomSegment}`;
  return uniqueID;
}
//
export function renderItem(item, search, handleOpenChat, actionCall, t) {
  //
  const { image, name, date, extension, phone, uuid, id, family_id } = item;
  //
  return {
    key: generateUniqueID(),
    value: extension || phone?.callNum,
    label: (
      <div key={item} className="relative flex w-full flex-row ">
        <div
          onClick={() => actionCall(extension || phone?.callNum, id, family_id)}
          className="relative  flex w-9/12 flex-row items-center space-x-2.5"
        >
          <div>
            <DisplayAvatar urlImg={image} name={name} size={35} />
          </div>
          <div className="w-10/12">
            <Tooltip title={t("voip.call")}>
              <p className="truncate font-semibold leading-5">
                {HighlightSearchW(name || phone?.displayNum, search)}
              </p>
              {name ? (
                <p className="leading-4 text-slate-500">
                  {HighlightSearchW(extension || phone?.displayNum, search)}
                </p>
              ) : date ? (
                <p className="leading-4 text-slate-500">{date}</p>
              ) : null}
            </Tooltip>
          </div>
        </div>

        <div className="absolute right-0 top-1 flex flex-row items-center space-x-1">
          {uuid && (
            <Button
              onClick={(e) => {
                e.stopPropagation();
                handleOpenChat(uuid);
              }}
              type="link"
              size="small"
              icon={<MessageOutlined style={{ fontSize: 14 }} />}
            />
          )}
          {copyIcon(extension || phone?.copyNum)}
        </div>
      </div>
    ),
  };
}
//
export function transformData(data, search, dialCode, t) {
  const familyMap = {
    1: "companies",
    2: "contacts",
    9: "leads",
    4: "colleagues",
  };

  const obj = {
    colleagues: [],
    contacts: [],
    companies: [],
    leads: [],
    groups_queues: [],
    notInContacts: [],
  };

  const baseURL = URL_ENV?.REACT_APP_BASE_URL || "";
  const avatarSuffix = URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL || "";

  const formatName = (name) => name?.replace(/_/g, " ");

  const formatImage = (image) =>
    image ? `${baseURL}${avatarSuffix}${image}` : undefined;

  const pushToObj = (key, item) => {
    obj[key].push(item);
  };

  data.forEach((item) => {
    const familyId = item.family_id;
    const commonFields = {
      ...item,
      name: formatName(item.name),
      baseImg: item.image,
      image: formatImage(item.image),
    };

    if (familyId === 4) {
      pushToObj("colleagues", {
        ...commonFields,
        extension: item.extension ? `${item.extension}` : undefined,
        phone: formattingPhoneNumber(item.phone, search, dialCode),
      });
    } else if (familyId && familyMap[familyId]) {
      pushToObj(familyMap[familyId], {
        ...commonFields,
        phone: formattingPhoneNumber(item.phone, search, dialCode),
      });
    } else if (item.group || item.queue) {
      pushToObj("groups_queues", item);
    } else {
      pushToObj("notInContacts", {
        ...commonFields,
        date: item.calldate_start
          ? humanDate(item.calldate_start, t)
          : undefined,
        phone: formattingPhoneNumber(item.phone, search, dialCode),
        extension: item.extension ? `${item.extension}` : undefined,
      });
    }
  });

  const sections = [
    {
      key: "key_colleagues",
      label: t("voip.colleagues"),
      values: obj.colleagues,
    },
    { key: "key_companies", label: t("voip.companies"), values: obj.companies },
    { key: "key_contacts", label: t("voip.contacts"), values: obj.contacts },
    { key: "key_leads", label: t("contacts.leads"), values: obj.leads },
    {
      key: "key_groups_queues",
      label: t("voip.groups_queues"),
      values: obj.groups_queues,
    },
    {
      key: "key_notInContacts",
      label: t("voip.notInContacts"),
      values: obj.notInContacts,
    },
  ];

  return sections.filter((section) => section.values.length > 0);
}

//
export function formattingPhoneNumber(phone, search, dialCode) {
  //
  const formatPhone = (phone) => {
    if (!Array.isArray(phone) && typeof phone === "string")
      return { displayNum: phone, callNum: phone, copyNum: phone };
    const hasDialCode =
      phone?.[0] === dialCode &&
      search?.[0] !== "+" &&
      search?.slice(0, 2) !== "00";
    const displayNum = hasDialCode
      ? phone?.[1]
      : `(${phone?.[0]}) ${phone?.[1]}`;
    const callNum = `${hasDialCode ? "" : phone?.[0]?.replace("+", "00")}${
      phone?.[1]
    }`;
    const copyNum = `${phone[0] || ""}${phone[1]}`;

    return { displayNum, callNum, copyNum };
  };

  return phone?.length ? formatPhone(phone) : null;
}

export default Call;
