import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  PlusOutlined,
  SettingFilled,
  SyncOutlined,
  WifiOutlined,
} from "@ant-design/icons";
import { HiOutlineArrowDownTray, HiOutlineCalendar } from "react-icons/hi2";
import {
  Layout,
  ConfigProvider,
  theme,
  Divider,
  Dropdown,
  Button,
  Tooltip,
  Alert,
  Typography,
  Skeleton,
  Tag,
  Empty,
  Select,
} from "antd";
import React, { Suspense, useEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { NavLink, Navigate, useLocation, useNavigate } from "react-router-dom";
import Menu1 from "./layout/menu1";
import Menu2 from "./layout/menu2";
import { useTranslation } from "react-i18next";

import {
  CreationElementDropDownButton,
  GenericButton,
} from "../components/GenericButton";
import {
  setOpenImportDrawer,
  setCreateForm,
} from "../../new-redux/actions/form.actions/form";
import { setOpenTaskDrawer } from "../../new-redux/actions/tasks.actions/handleTaskDrawer";
import "./layout.css";
import useNetwork from "../../custom-hooks/useNetwork";
import { BsWifiOff } from "react-icons/bs";
import { VISIO_URL_REDIRECT_REGEX } from "../../utils/regex";
import { resizeVisio } from "../../new-redux/actions/visio.actions/createVisio";
import AvatarLayout from "./components/avatar-layout";
import {
  setPage,
  setSearchEmail,
  setSearchPage,
  setSelectedAccount,
} from "new-redux/actions/mail.actions";
import { AvatarChat } from "components/Chat";
import { LATENCE_ADDING_TIME, getName } from "./chat/utils/ConversationUtils";
import { AiOutlineSetting, AiOutlineVideoCameraAdd } from "react-icons/ai";
import MainService from "services/main.service";
import { SET_USER_INFOS } from "new-redux/constants";
import GlobalSearch from "pages/global-search/GlobalSearch";
import { handleReload } from "pages/tasks/helpers/handleCheck";
import SortAddBar from "./chat/sidebar/header/SortAddBar";
import { setCollpaseMenu } from "new-redux/actions/menu.actions/menu";
import { isGuestConnected } from "utils/role";
import { setOpenTaskRoomDrawer } from "new-redux/actions/tasks.actions/handleTaskRoomDrawer";
import { setOpenFieldDrawer } from "new-redux/actions/fields.actions/fieldDrawer";
import StatsDrawer from "pages/components/StatsDrawer";
import { getLabelNameById } from "pages/rmc/mailing/helpers/helpers";
import { truncateString } from "pages/voip/helpers/helpersFunc";
import { Refs_IDs } from "components/tour/tourConfig";
import CreateItemsDropdown from "pages/drive/createItemsDropdown";
const { Header, Sider, Content } = Layout;

const LayoutFull = ({ children }) => {
  const redirectActivityId = localStorage.getItem("redirect-activity-id");

  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const location = useLocation();
  const navigate = useNavigate();
  const { nameOrg } = useSelector((state) => state.configCompanies);
  const { parentItem } = useSelector((state) => state.drive);
  const { selectedFamily } = useSelector((state) => state.families);
  const families = useSelector((state) => state?.families?.families);
  const { isOpen, size, visioParams } = useSelector((state) => state.visio);
  const { selectedViewInTask } = useSelector((state) => state?.TasksRealTime);
  const { totalNotificationChat, latence, currentUser } = useSelector(
    (state) => state.chat
  );
  const { user } = useSelector((state) => state.user);
  const { isMenuCollapsed } = useSelector((state) => state?.menu);
  const { dataAccounts, loadingAccount, synchroType } = useSelector(
    (state) => state.mailReducer
  );

  const { isOnline } = useNetwork();
  const {
    token: { colorBgContainer },
  } = theme.useToken();

  const usedAccount = useMemo(() => {
    return dataAccounts?.find((item) => item.selected) || dataAccounts?.[0];
  }, [dataAccounts]);

  // console.warn("dataAccountlayout", dataAccounts);
  // console.warn("usedAccountlayout", usedAccount);
  const handleBlurVisio = () => {
    if (
      (size === "medium" || size === "large") &&
      isOpen &&
      !visioParams?.external
    ) {
      dispatch(resizeVisio("small"));
    }
  };

  const updateSelectedEmail = async (id) => {
    var formData = new FormData();
    formData.append("accountId", id);
    try {
      dispatch(setSelectedAccount(id));
      dispatch(setSearchEmail(""));
      const response = await MainService.updateSelectedEmail(formData);
      if (response.status === 200) {
        const accounts_email = user?.accounts_email?.map((item) =>
          item.id === id ? { ...item, primary_account: 1 } : item
        );
        dispatch({
          type: SET_USER_INFOS,
          payload: {
            ...user,
            accounts_email: accounts_email,
          },
        });
      }
    } catch (error) {
      console.log(error);
    }
  };

  const getUrlFromPath = (url) => {
    switch (true) {
      case url.includes("companies"):
        if (location.pathname === `/settings/general/companies`) {
          return t(`menu2.companiesOnly`);
        } else if (location.pathname === "/contacts/companies") {
          return t(`menu2.companiesOnly`);
        }
        break;
      case url.includes("new"):
      case /\d+/.test(url):
        if (location.pathname === `/settings/general/companies/${url}`) {
          return (
            t(`menu2.companies`, { company: nameOrg || "companies" }) +
            "/Informations"
          );
        }
        break;
      case url.includes("banks"):
        if (
          /\/settings\/general\/companies\/\d+\/banks/.test(location.pathname)
        ) {
          return (
            t(`menu2.companies`, { company: nameOrg || "companies" }) + "/Banks"
          );
        }
        break;
      case url.includes("tax"):
        if (
          /\/settings\/general\/companies\/\d+\/tax/.test(location.pathname)
        ) {
          return (
            t(`menu2.companies`, { company: nameOrg || "companies" }) + "/Tax"
          );
        }
        break;
      case url.includes("signature"):
        if (
          /\/settings\/general\/companies\/\d+\/signature/.test(
            location.pathname
          )
        ) {
          return (
            t(`menu2.companies`, { company: nameOrg || "companies" }) +
            "/signature"
          );
        }
        break;
      case url.includes("important"):
        return "Important";
      case url.includes("import"):
        // if (/\/import/.test(location.pathname)) {
        //   return `${t("menu1.import")} ${getSelectedFamilyText(
        //     Number(selectedFamily),
        //     t
        //   )}`;
        // }
        if (/\/import/.test(location.pathname)) {
          return `${t("menu1.import")} ${
            selectedFamily
              ? families?.find(
                  (el) => Number(el?.id) === Number(selectedFamily)
                )?.label
              : ""
          }`;
        }
        break;
      case /\/wiki\/.*/.test(location.pathname):
        if (/\/wiki\/docs/.test(location.pathname)) {
          return t(`menu2.wiki-docs`);
        }

        break;
      case location.pathname.includes("pipeline"):
        if (/\/settings\/pipeline\/\w+/.test(location.pathname)) {
          return t(`menu2.pipeline`) + "/" + url;
        }
        break;
      default:
        return t(`menu2.${url}`);
    }
  };

  const menuOtherOptions = [
    {
      label: t("menu1.import"),
      icon: <HiOutlineArrowDownTray />,
      onClick: () => dispatch(setOpenImportDrawer(true)),
    },
  ];

  const canShowMenu2 = useMemo(() => {
    const menu2Exclude = [
      "companies",
      "contacts",
      // "*",
      // "products",
      "deals",
      "tickets",
      "projects",
      "leads",
      // "directory",
      "notifications",
      "notes",
      "invoices",
      "transactions",
      "editor",
      "telephony",
      "logs",
      "drive",
    ];
    return (
      !/\/chat/.test(location.pathname) &&
      !/\/dashboard/.test(location.pathname) &&
      location.pathname !== "/interactions" &&
      location.pathname !== "/profile/appMobile" &&
      location.pathname !== "/stats/families" &&
      location.pathname !== "/rmc" &&
      !/\/logout/.test(location.pathname) &&
      !/\/home-visio/.test(location.pathname) &&
      !/\/invitation-user/.test(location.pathname) &&
      !/\/tasks/.test(location.pathname) &&
      !/\/visio/.test(location.pathname) &&
      !/\/unauthorized/.test(location.pathname) &&
      !/\/booking/.test(location.pathname) &&
      !location.pathname.match(/^\/settings\/users\/([a-fA-F0-9]{24})$/) &&
      !location.pathname.match(/^\/settings\/guests\/v2\/([a-fA-F0-9]{24})$/) &&
      !location.pathname.match(/^\/settings\/users\/v2\/([a-fA-F0-9]{24})$/) &&
      !location.pathname.match(
        /^\/settings\/products\/v2\/([a-fA-F0-9]{24})$/
      ) &&
      !menu2Exclude.includes(location.pathname.split("/")[1]) &&
      !location.pathname.match(VISIO_URL_REDIRECT_REGEX)
      // &&
      // document.title !== t("404")
    );
  }, [t, location.pathname]);
  const hideHeader = useMemo(
    () =>
      ///\/chat/.test(location.pathname) ||
      /\/rmc/.test(location.pathname) ||
      /\/logout/.test(location.pathname) ||
      /\/home-visio/.test(location.pathname) ||
      /\/invitation-user/.test(location.pathname) ||
      /\/unauthorized/.test(location.pathname) ||
      /\/settings\/rmc/.test(location.pathname) ||
      // /\/stats/.test(location.pathname) ||
      /\/contacts\/prospects/.test(location.pathname) ||
      document.title === t("404") ||
      location.pathname.match(VISIO_URL_REDIRECT_REGEX),
    [location.pathname, t]
  );
  const canAddMargin = useMemo(
    () =>
      /\/chat/.test(location.pathname) ||
      location.pathname === "/rmc" ||
      location.pathname === "/drive" ||
      /\/dashboard/.test(location.pathname) ||
      location.pathname === "/interactions" ||
      location.pathname === "/profile/appMobile" ||
      location.pathname === "/stats/families" ||
      /\/tasks/.test(location.pathname) ||
      /\/dashboard/.test(location.pathname) ||
      /\/dashboard/.test(location.pathname) ||
      location.pathname === "/visio" ||
      /\/booking/.test(location.pathname) ||
      location.pathname.match(/^\/settings\/users\/([a-fA-F0-9]{24})$/) ||
      location.pathname.match(/^\/settings\/guests\/v2\/([a-fA-F0-9]{24})$/) ||
      location.pathname.match(/^\/settings\/users\/v2\/([a-fA-F0-9]{24})$/) ||
      location.pathname.match(
        /^\/settings\/products\/v2\/([a-fA-F0-9]{24})$/
      ) ||
      location.pathname.split("/")[1] === "companies" ||
      location.pathname.split("/")[1] === "contacts" ||
      location.pathname.split("/")[1] === "deals" ||
      location.pathname.split("/")[1] === "tickets" ||
      location.pathname.split("/")[1] === "projects" ||
      location.pathname.split("/")[1] === "leads" ||
      // location.pathname.split("/")[1] === "directory" ||
      location.pathname.split("/")[1] === "editor" ||
      // location.pathname.split("/")[2] === "phone-book" ||
      // location.pathname.split("/")[2] === "colleagues" ||
      // location.pathname.split("/")[2] === "groups" ||
      location.pathname.split("/")[1] === "notes" ||
      location.pathname.split("/")[1] === "invoices" ||
      location.pathname.split("/")[1] === "transactions" ||
      location.pathname.split("/")[1] === "telephony" ||
      (/\/unauthorized/.test(location.pathname) &&
        !/\/logout/.test(location.pathname) &&
        document.title !== t("404") &&
        !location.pathname.includes("/visio-home/") &&
        !/\/invitation-user/.test(location.pathname)),
    [location.pathname, t]
  );
  const canAddCollapsedButton = useMemo(
    () =>
      !location.pathname.match(/^\/settings\/users\/([a-fA-F0-9]{24})$/) &&
      !location.pathname.match(/^\/settings\/users\/([a-fA-F0-9]{24})$/) &&
      !location.pathname.match(/^\/settings\/guests\/v2\/([a-fA-F0-9]{24})$/) &&
      !location.pathname.match(/^\/settings\/users\/v2\/([a-fA-F0-9]{24})$/) &&
      !location.pathname.match(
        /^\/settings\/products\/v2\/([a-fA-F0-9]{24})$/
      ) &&
      !/\/tasks/.test(location.pathname) &&
      location.pathname !== "/interactions" &&
      location.pathname !== "/profile/appMobile" &&
      location.pathname !== "/stats/families" &&
      !/\/chat/.test(location.pathname) &&
      !/\/dashboard/.test(location.pathname) &&
      !/\/visio/.test(location.pathname) &&
      !/\/deals/.test(location.pathname) &&
      !/\/tickets/.test(location.pathname) &&
      !/\/projects/.test(location.pathname) &&
      location.pathname.split("/")[1] !== "companies" &&
      location.pathname.split("/")[1] !== "drive" &&
      !/\/contacts/.test(location.pathname) &&
      !/\/invoices/.test(location.pathname) &&
      !/\/transactions/.test(location.pathname) &&
      !/\/booking/.test(location.pathname) &&
      !/\/leads/.test(location.pathname) &&
      // !/\/directory/.test(location.pathname) &&
      !/\/editor/.test(location.pathname) &&
      !/\/telephony/.test(location.pathname) &&
      // !/\/colleagues/.test(location.pathname) &&
      // !/\/phone-book/.test(location.pathname) &&
      (!/\/groups/.test(location.pathname) ||
        /\/wiki/.test(location.pathname)) &&
      !/\/unauthorized/.test(location.pathname) &&
      !/\/notes/.test(location.pathname),
    [location.pathname]
  );
  const publicUrl = useMemo(
    () =>
      !location.pathname.match(VISIO_URL_REDIRECT_REGEX) &&
      !location.pathname.match(/\/logout/) &&
      !location.pathname.match(/\/invitation-user/),
    [location.pathname]
  );

  if (location.pathname === "/") {
    return <Navigate to="/dashboard" />;
  } else if (location.pathname === "/profile")
    return <Navigate to="/profile/general" />;
  else if (location.pathname === "/stats") {
    return <Navigate to="/stats/families" />;
  } else if (location.pathname === "/settings")
    return <Navigate to="/settings/general/companies" />;
  else if (location.pathname === "/settings/general")
    return <Navigate to="/settings/general/companies" />;
  else if (location.pathname === "/settings/activity")
    return <Navigate to="/settings/activity/types" />;
  else if (location.pathname === "/settings/helpDesk")
    return <Navigate to="/settings/helpDesk/severities" />;
  else if (location.pathname === "/mailing") {
    return <Navigate to={`/mailing/${usedAccount?.value ?? 0}/inbox`} />;
  }
  const renderEmptyComponent = () => {
    return <Empty />;
  };

  return (
    <ConfigProvider
      theme={{
        algorithm: theme.defaultAlgorithm,
        token: {
          colorPrimary: "#1d4ed8",
          fontSize: 13,
        },
      }}
      renderEmpty={renderEmptyComponent}
    >
      <Layout>
        {isOnline &&
          latence > LATENCE_ADDING_TIME + LATENCE_ADDING_TIME / 2 && (
            <Alert
              className="absolute inset-x-0 z-[1010] ml-[70px] mr-2 mt-1 flex items-center justify-center p-2"
              description={
                <div>
                  {t("toasts.networkStatusPoorDescription")}
                  {location.pathname !== "/editor" && (
                    <Typography.Link className="text-sm" onClick={handleReload}>
                      {t("chat.link")}
                    </Typography.Link>
                  )}
                </div>
              }
              type="warning"
              showIcon
              closable
            />
          )}

        <Sider
          onClick={handleBlurVisio}
          width="50"
          className={`flex flex-col   ${publicUrl ? "visible" : "  invisible"}`}
          theme="dark"
          collapsed={true}
          style={{
            overflow: "hidden",
            height: "100vh",
            position: "fixed",
            zIndex: isOpen && size !== "small" ? 1050 : 1029,
            left: 0,
            top: 0,
            bottom: 0,
          }}
        >
          <div className="relative pb-4 pt-3 text-center">
            <NavLink
              to={location.pathname === "/editor" ? "#" : "/dashboard"}
              onClick={(e) => {
                if (location.pathname === "/editor") {
                  e && e.preventDefault();
                  return;
                }
                dispatch(setOpenTaskDrawer(false));
                dispatch(setOpenTaskRoomDrawer(false));
                dispatch(setOpenFieldDrawer(false));
                if (redirectActivityId) {
                  localStorage.removeItem("redirect-activity-id");
                }
                document.title =
                  (totalNotificationChat?.numberConversationChat > 0
                    ? "(" + totalNotificationChat?.numberConversationChat + ") "
                    : "") + t("menu1.dashboard");
              }}
            >
              <Tooltip
                placement="bottomRight"
                title={
                  isOnline
                    ? latence <= LATENCE_ADDING_TIME * 2
                      ? t("toasts.networkStatusGood")
                      : latence > LATENCE_ADDING_TIME * 2 &&
                        latence < LATENCE_ADDING_TIME * 7
                      ? t("toasts.networkStatusModerate")
                      : t("toasts.networkStatusPoor")
                    : t("toasts.networkStatusOffline")
                }
              >
                <span className=" absolute -top-1 right-1   text-lg">
                  {isOnline ? (
                    <WifiOutlined
                      className={`  text-${
                        latence <= LATENCE_ADDING_TIME * 2
                          ? "green-500"
                          : latence > LATENCE_ADDING_TIME * 2 &&
                            latence < LATENCE_ADDING_TIME * 7
                          ? "yellow-500"
                          : "red-500"
                      } `}
                    />
                  ) : (
                    <BsWifiOff className="  text-gray-500" />
                  )}
                </span>
              </Tooltip>

              <img
                ref={Refs_IDs.logo_dashboard}
                src="/images/logo_comunik.png"
                onError={(e) =>
                  //   e.target.src="/images/logo_comunik.png"
                  (e.target.onerror = null)
                }
                width="34px"
                alt="soc_logo"
              />
            </NavLink>
          </div>
          <Menu1 />
          {location.pathname !== "/editor" && <AvatarLayout />}
        </Sider>

        {canShowMenu2 &&
          ((user?.accounts_email?.length > 0 &&
            location.pathname.split("/")[1] === "mailing") ||
            location.pathname.split("/")[1] !== "mailing") && (
            <Sider
              className={` menu2-${location.pathname.split("/")[1]} 
           sticky top-0  transition-all  delay-100   ease-in-out`}
              trigger={null}
              collapsible
              theme="light"
              collapsed={isMenuCollapsed}
              style={{
                marginLeft: 64,
                overflow: "auto",
                height: "100vh",
                minWidth: "300px !important",
                maxWidth: "500px",
              }}
            >
              {/* {location.pathname.split("/")[1] === "mailing" && user?.accounts_email?.length > 0 ? ( */}
              {location.pathname.split("/")[1] === "mailing" &&
              user?.accounts_email?.length > 0 ? (
                <div className=" px-2 py-4">
                  {loadingAccount ? (
                    <Skeleton.Input active block />
                  ) : (
                    <Select
                      value={{
                        key: usedAccount?.value,
                        label: (
                          <div className="flex items-center space-x-1">
                            <AvatarChat
                              size={24}
                              width={7}
                              className="flex-none"
                              fontSize={11}
                              height={7}
                              hasImage={false}
                              backgroundColor="#f56a00"
                              colorTextAvatar="#FFF"
                              name={getName(usedAccount?.label, "avatar")}
                              type="user"
                            />
                            <span className="mx-1 max-w-[200px] flex-1 truncate text-[14px] font-semibold leading-5 text-gray-500">
                              {usedAccount?.label}
                            </span>
                          </div>
                        ),
                      }}
                      labelInValue
                      optionLabelProp="label"
                      onChange={(e) => {
                        navigate("/mailing/" + e.value + "/inbox");
                        dispatch(setSearchEmail(""));
                        updateSelectedEmail(e.value);
                        dispatch(setPage(1));
                        dispatch(setSearchPage(1));
                      }}
                      options={dataAccounts.map((item) => ({
                        value: item.value,
                        label: (
                          <Tooltip
                            placement="right"
                            title={isMenuCollapsed ? item.label : null}
                          >
                            <div className="flex items-center space-x-1">
                              <AvatarChat
                                size={24}
                                width={6}
                                fontSize={10}
                                height={6}
                                backgroundColor="#f56a00"
                                colorTextAvatar="#FFF"
                                hasImage={false}
                                name={getName(item.label, "avatar")}
                                type="user"
                              />
                              {!isMenuCollapsed ? (
                                <p className="max-w-[200px] truncate">
                                  {item.label}
                                </p>
                              ) : null}
                            </div>
                          </Tooltip>
                        ),
                      }))}
                      style={{
                        width: "100%", // Set the desired width
                      }}
                      popupMatchSelectWidth={false}
                    />
                    // <Dropdown
                    //   trigger={["click"]}
                    //   arrow
                    //   menu={{
                    //     defaultSelectedKeys: [usedAccount?.value + ""],
                    //     selectable: true,
                    //     items: dataAccounts.map((item) => ({
                    //       style: { margin: "2px" },

                    //       key: item.value,
                    //       label: (
                    //         <p className="  max-w-[200px] truncate">
                    //           {item.label}
                    //         </p>
                    //       ),
                    //       icon: (
                    //         <AvatarChat
                    //           size={24}
                    //           width={6}
                    //           fontSize={10}
                    //           height={6}
                    //           backgroundColor="#f56a00"
                    //           colorTextAvatar="#FFF"
                    //           hasImage={false}
                    //           name={getName(item.label, "avatar")}
                    //           type="user"
                    //         />
                    //       ),
                    //     })),

                    //     onClick: (e) => {
                    //       navigate("/mailing/" + e.key + "/inbox");
                    //       updateSelectedEmail(e.key);
                    //       dispatch(setPage(1));
                    //       dispatch(setSearchPage(1));
                    //     },
                    //   }}
                    // >
                    //   <div className=" flex w-full cursor-pointer items-center  justify-start space-x-1 ">
                    //     <AvatarChat
                    //       size={36}
                    //       width={7}
                    //       className=" flex-none"
                    //       fontSize={15}
                    //       height={7}
                    //       hasImage={false}
                    //       backgroundColor="#f56a00"
                    //       colorTextAvatar="#FFF"
                    //       name={getName(usedAccount?.label, "avatar")}
                    //       type="user"
                    //     />
                    //     <span
                    //       className="mx-1 max-w-[200px] flex-1 truncate  text-[14px] font-semibold leading-5
                    //  text-gray-500"
                    //     >
                    //       {usedAccount?.label}{" "}
                    //     </span>
                    //     <DownOutlined />
                    //   </div>
                    // </Dropdown>
                  )}
                </div>
              ) : (
                <>
                  {" "}
                  <h1
                    className={`flex h-[51px] items-center   text-lg font-medium text-slate-600 ${
                      isMenuCollapsed
                        ? "justify-center"
                        : "max-w-48 truncate px-6"
                    }`}
                  >
                    {isMenuCollapsed ? (
                      <SettingFilled />
                    ) : t(
                        `menu1.${location.pathname?.split("/")[1]}`
                      )?.includes("menu1") ? (
                      ""
                    ) : (
                      t(`menu1.${location.pathname?.split("/")[1]}`)
                    )}
                  </h1>
                  <Divider className="my-1"></Divider>
                </>
              )}

              <Menu2 collapsed={isMenuCollapsed} />
            </Sider>
          )}
        <Layout
          onClick={handleBlurVisio}
          className="site-layout"
          style={
            canAddMargin
              ? {
                  marginLeft: 64,
                  overflow: "auto",
                  height: "100vh",
                }
              : { height: "100vh" }
          }
        >
          {location.pathname.split("/")[1] !== "editor" && (
            <Header
              className="sticky top-0 flex  items-center   shadow"
              style={
                (hideHeader && location.pathname.split("/")[1] !== "mailing") ||
                (user?.accounts_email?.length === 0 &&
                  location.pathname.split("/")[1] === "mailing")
                  ? { display: "none" }
                  : {
                      padding: 0,
                      background: colorBgContainer,
                      zIndex: "11",
                    }
              }
            >
              <div
                className={`${
                  location.pathname === "/chat" ? "bg-slate-100" : ""
                } flex w-full flex-row items-center ${
                  isGuestConnected(currentUser?.role, user?.role)
                    ? "relative h-full"
                    : ""
                } pl-1 pr-4`}
              >
                <div className="basis-1/3">
                  <section className="flex items-center justify-start">
                    {canAddCollapsedButton && (
                      <Tooltip
                        title={
                          isMenuCollapsed
                            ? t("menu2.expandMenu")
                            : t("menu2.hideMenu")
                        }
                      >
                        {React.createElement(
                          isMenuCollapsed
                            ? MenuUnfoldOutlined
                            : MenuFoldOutlined,
                          {
                            className: "trigger",
                            // onClick: () => setCollapsed((prev) => !prev),
                            onClick: () =>
                              dispatch(setCollpaseMenu(!isMenuCollapsed)),
                          }
                        )}
                      </Tooltip>
                    )}
                    <h2
                      className={`mb-0  px-2 ${
                        location.pathname.includes("/settings/emailTemplates")
                          ? "text-sm"
                          : "text-lg"
                      } font-medium text-slate-600`}
                    >
                      {location.pathname.includes("/drive")
                        ? t("menu1.drive")
                        : ""}
                      {location.pathname === "/dashboard"
                        ? t("menu2.dashboard")
                        : ""}
                      {location.pathname === "/stats/families"
                        ? t("menu1.stats")
                        : ""}
                      {location.pathname === "/interactions"
                        ? t("menu1.interactions")
                        : ""}
                      {location.pathname.includes("/settings/liveChat")
                        ? "Live chat"
                        : ""}
                      {location.pathname === "/interactions"
                        ? t("profile.mobielApp")
                        : ""}
                      {/* {location.pathname === "/directory"
                        ? t("voip.directory")
                        : ""} */}
                      {location.pathname.includes("/settings/emailTemplates")
                        ? t("menu2.emailTemplates")
                        : ""}
                      {location.pathname.includes("/profile/signature")
                        ? "Signature email"
                        : ""}
                      {location.pathname.includes("/profile/accessToken")
                        ? t("profilemenu.accessToken")
                        : ""}
                      {location.pathname.includes("/profile/infoTenant")
                        ? t("profilemenu.infoTenant")
                        : ""}

                      {location.pathname === "/profile/allNotifications"
                        ? t("menu2.allNotifications")
                        : ""}
                      {location.pathname.includes("/wiki/groups")
                        ? `Wiki/Groups`
                        : ""}
                      {location.pathname.includes("/wiki/groupes")
                        ? "Wiki/Groupes"
                        : ""}
                      {location.pathname.includes("/wiki/catalogs")
                        ? "Wiki/Catalogs"
                        : ""}
                      {location.pathname.includes("/wiki/catalogues")
                        ? "Wiki/Catalogues "
                        : ""}
                      {location.pathname.includes("activity/pipelines")
                        ? "Pipelines"
                        : ""}
                      {location.pathname === `/settings/general/guestQueue`
                        ? t("menu2.guestQueue")
                        : ""}
                      {/* {location.pathname?.includes("/contacts-types")
                        ? t("menu2.typesContacts")
                        : ""} */}
                      {location.pathname === "/contacts"
                        ? t("menu2.contacts")
                        : ""}
                      {location.pathname?.includes("/invoices")
                        ? t("menu2.invoices")
                        : ""}
                      {location.pathname?.includes("/transactions")
                        ? "Transactions"
                        : ""}
                      {location.pathname?.includes("/settings/users/")
                        ? t("contacts.user")
                        : ""}
                      {location.pathname?.includes("/settings/guests")
                        ? t("users.guests")
                        : ""}
                      {location.pathname === "/leads" ? t("menu1.leads") : ""}
                      {location.pathname === "/companies" ||
                      /^\/companies\/(\w+)$/g.test(location.pathname)
                        ? t("menu1.companies")
                        : ""}
                      {location.pathname?.includes("/deals")
                        ? t("menu1.deals")
                        : ""}
                      {location.pathname?.includes("/tickets")
                        ? t("menu1.tickets")
                        : ""}
                      {location.pathname?.includes("/projects")
                        ? t("menu1.projects")
                        : ""}
                      {location.pathname?.includes("/settings/products/v2")
                        ? t("menu1.products")
                        : ""}
                      {location.pathname === "/booking"
                        ? t("menu1.booking")
                        : ""}
                      {location.pathname?.split("/")?.[1] === "telephony"
                        ? t("menu2.logs")
                        : ""}
                      {location.pathname?.includes("/notes")
                        ? t("menu1.notes")
                        : ""}
                      {location.pathname?.includes("/chat")
                        ? t("menu1.chat")
                        : ""}
                      {!location.pathname?.includes("/tasks") &&
                      location.pathname.split("/").length > 2
                        ? getUrlFromPath(
                            location.pathname
                              .split("/")
                              [
                                location.pathname.split("/").length - 1
                              ].toLowerCase()
                          )?.includes("menu2")
                          ? ""
                          : getUrlFromPath(
                              location.pathname
                                .split("/")
                                [
                                  location.pathname.split("/").length - 1
                                ].toLowerCase()
                            )
                        : ""}
                      {location.pathname?.includes("/tasks")
                        ? t("tasks.menu2Header", {
                            selectedView:
                              selectedViewInTask === "Kanban"
                                ? "Kanban"
                                : selectedViewInTask === "Calendar"
                                ? t("tasks.calendarViewTooltip")
                                : t("tasks.tableViewTooltip"),
                          })
                        : ""}
                      {location.pathname?.includes("/visio") ? (
                        <span className="">{t("menu1.visio")}</span>
                      ) : (
                        ""
                      )}
                      {location.pathname?.includes("/mailing") &&
                      location.pathname?.includes("/label")
                        ? truncateString(
                            getLabelNameById(
                              usedAccount,
                              dataAccounts,
                              location.pathname,
                              t
                            ),
                            25
                          )
                        : ""}
                    </h2>
                    {location.pathname?.includes("/chat") &&
                      !isGuestConnected(currentUser?.role, user?.role) && (
                        /* ml-28 to make the icon in the same level as the sidebar  */
                        <div className="fixed left-72 flex items-center justify-end">
                          <SortAddBar source="chat" />
                        </div>
                      )}
                  </section>
                </div>

                {/* {!isGuestConnected(currentUser?.role, user?.role) && ( */}
                <div className="basis-1/3">
                  <div className="flex items-center justify-center">
                    <GlobalSearch />
                  </div>
                </div>
                {/* )} */}

                <div className="basis-1/3">
                  <div className="flex items-center justify-end">
                    <div>
                      {location.pathname.includes("/drive") && (
                        <CreateItemsDropdown parent={parentItem} />
                      )}
                      {(location.pathname === "/contacts" ||
                        location.pathname === "/companies" ||
                        location.pathname === "/deals" ||
                        location.pathname === "/settings/users" ||
                        location.pathname === "/settings/guests" ||
                        location.pathname === "/settings/products" ||
                        location.pathname === "/tickets" ||
                        location.pathname === "/leads" ||
                        location.pathname === "/invoices" ||
                        location.pathname === "/transactions" ||
                        location.pathname === "/projects") &&
                        !isGuestConnected() && (
                          <Dropdown
                            menu={{
                              items: menuOtherOptions,
                            }}
                            className="mr-6"
                          >
                            <Button
                              ref={Refs_IDs.families_import_dropDown}
                              type="text"
                            >
                              ...
                            </Button>
                          </Dropdown>
                        )}
                      {location.pathname === "/contacts" && (
                        <GenericButton
                          type="primary"
                          icon={<PlusOutlined />}
                          onClick={() => dispatch(setCreateForm(true))}
                          text={t("contacts.btnCreateContact")}
                        />
                      )}
                      {location.pathname === "/companies" && (
                        <GenericButton
                          type="primary"
                          icon={<PlusOutlined />}
                          onClick={
                            () => dispatch(setCreateForm(true)) /*message.open({
                        type: "warning",
                        content: "This Feature is under Dev!",
                      })*/
                          }
                          text={t("contacts.btnCreateCompany")}
                        />
                      )}
                      {location.pathname === "/settings/products" && (
                        <GenericButton
                          type="primary"
                          icon={<PlusOutlined />}
                          onClick={() => dispatch(setCreateForm(true))}
                          text={t("contacts.btnCreateProduct")}
                        />
                      )}
                      {location.pathname === "/deals" && (
                        <GenericButton
                          type="primary"
                          icon={<PlusOutlined />}
                          onClick={() => dispatch(setCreateForm(true))}
                          text={t("contacts.btnCreateDeal")}
                        />
                      )}
                      {location.pathname === "/tickets" && (
                        <GenericButton
                          type="primary"
                          icon={<PlusOutlined />}
                          onClick={() => dispatch(setCreateForm(true))}
                          text={t("contacts.btnCreateTicket")}
                        />
                      )}
                      {location.pathname === "/projects" && (
                        <GenericButton
                          type="primary"
                          icon={<PlusOutlined />}
                          onClick={() => dispatch(setCreateForm(true))}
                          text={t("contacts.btnCreateProject")}
                        />
                      )}
                      {location.pathname === "/booking" && (
                        <GenericButton
                          type="primary"
                          icon={<PlusOutlined />}
                          onClick={() => dispatch(setCreateForm(true))}
                          text={t("contacts.btnCreateBooking")}
                        />
                      )}
                      {location.pathname === "/leads" && (
                        <GenericButton
                          type="primary"
                          icon={<PlusOutlined />}
                          onClick={() => dispatch(setCreateForm(true))}
                          text={t("contacts.btnCreateLead")}
                        />
                      )}
                      {location.pathname === "/invoices" && (
                        <GenericButton
                          type="primary"
                          icon={<PlusOutlined />}
                          onClick={() => dispatch(setCreateForm(true))}
                          text={t("contacts.btnCreateInvoice")}
                        />
                      )}
                      {location.pathname === "/transactions" && (
                        <GenericButton
                          type="primary"
                          icon={<PlusOutlined />}
                          onClick={() => dispatch(setCreateForm(true))}
                          text={t("contacts.btnCreateTransaction")}
                        />
                      )}
                      {location.pathname === "/settings/users" && (
                        <GenericButton
                          type="primary"
                          icon={<PlusOutlined />}
                          onClick={() => dispatch(setCreateForm(true))}
                          text={t("contacts.btnCreateUser")}
                        />
                      )}
                      {location.pathname === "/tasks" &&
                        !isGuestConnected(currentUser?.role, user?.role) && (
                          <Button
                            type="primary"
                            ref={Refs_IDs.addQuickTask}
                            icon={<PlusOutlined />}
                            onClick={() => dispatch(setOpenTaskDrawer(true))}
                            id={
                              location.pathname === "/tasks"
                                ? "addQuickTask"
                                : "field-drawer-btn"
                            }
                          >
                            {location.pathname === "/tasks"
                              ? t("tasks.addQuickTask")
                              : t("fields_management.field_drawer_btn")}
                          </Button>
                        )}
                      {location.pathname === "/visio" &&
                        !isGuestConnected(currentUser?.role, user?.role) && (
                          <div className="flex items-center gap-1">
                            <Button
                              className="invisible"
                              disabled={isOpen}
                              type="primary"
                              icon={<AiOutlineVideoCameraAdd />}
                            >
                              {t("chat.header.visio.createVideoConferance")}
                            </Button>
                            <Button
                              className="invisible"
                              icon={
                                <HiOutlineCalendar
                                  style={{ fontSize: "100%" }}
                                />
                              }
                            >
                              {t("chat.header.createVideoLater")}
                            </Button>
                          </div>
                        )}
                      {
                        // location.pathname === "/directory"
                        location.pathname === "/telephony/directory" && (
                          <CreationElementDropDownButton
                            t={t}
                            dispatch={dispatch}
                          />
                        )
                      }
                    </div>
                    {/\/mailing/.test(location.pathname) && usedAccount && (
                      <div className="flex items-center font-semibold">
                        {synchroType === "start" ? (
                          <Tag icon={<SyncOutlined spin />} color="processing">
                            processing
                          </Tag>
                        ) : // : synchroType === "fail" ? (
                        //   <Tag color="error">error</Tag>
                        // )
                        null}
                        {usedAccount?.sync === 1 ? (
                          <Tag color="success">{t("mailing.Syncronized")}</Tag>
                        ) : (
                          <Tag color="red">{t("mailing.NotSyncronized")}</Tag>
                        )}
                        <StatsDrawer familyId={"email"} />
                        <Tooltip placement="top" title="Configuration email">
                          <Button
                            type="text"
                            danger
                            icon={
                              <AiOutlineSetting
                                style={{
                                  height: "20px",
                                  width: "20px",
                                  marginTop: "3px",
                                  marginRight: "4px",
                                  cursor: "pointer",
                                }}
                              />
                            }
                            onClick={() => {
                              navigate("settings/emailAccounts");
                            }}
                          />
                        </Tooltip>
                      </div>
                    )}
                    {/\/emailTemplates/.test(location.pathname) && (
                      <div>
                        {/* <Form.Item label={t("tasks.selectFamily")}> */}

                        {/* </Form.Item> */}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </Header>
          )}
          <Content
            className="overflow-auto"
            style={{
              padding: 0,

              background: colorBgContainer,
            }}
          >
            {children}
          </Content>
        </Layout>
      </Layout>
    </ConfigProvider>
  );
};
export default LayoutFull;
