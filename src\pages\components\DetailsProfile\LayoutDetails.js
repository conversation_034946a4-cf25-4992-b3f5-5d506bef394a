import { useEffect, useState } from "react";
import {
  Bad<PERSON>,
  Card,
  Layout,
  Skeleton,
  Steps,
  Tabs,
  Typography,
  theme,
} from "antd";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import SiderLayout from "../../clients&users/components/contacts-details-component/layout/SiderLayout";
import TimeLine from "./Timeline";
import ProfileDetails from "../../clients&users/components/contacts-details-component/ProfileDetails";
import Overview from "./Activities/Overview";
import ChatRmc from "./ChatRmc";
import {
  setActiveActivity360,
  setActiveTab360,
} from "../../../new-redux/actions/vue360.actions/vue360";
import { useDispatch } from "react-redux";
import MainService from "../../../services/main.service";
import CallLogs360 from "../../voip/call_logs/CallLogs360";
import Notes from "./Activities/Notes";
import Mails from "./Activities/Mails";
import Files from "./Activities/Files";
import Cart from "../../cart";
import { useParams } from "react-router-dom";
import { setSearch } from "../../../new-redux/actions/menu.actions/menu";
import { URL_ENV } from "index";

const { Content, Sider } = Layout;

const LayoutDetails = ({
  elementID = null,
  module = null,
  source,
  isMinimized,
  handleMinimized = () => {},
}) => {
  //
  const [t] = useTranslation("common");
  const params = useParams();
  const dispatch = useDispatch();
  const contactInfo = useSelector(
    (state) => state?.contacts?.contactHeaderInfo
  );
  const { user } = useSelector((state) => state?.user);
  const { activeTab360 } = useSelector((state) => state?.vue360);
  const token = localStorage.getItem("accessToken");
  const [tasksTypes, setTasksTypes] = useState([]);
  const [loadTasks, setLoadTasks] = useState(false);
  const [pipelines, setPipelines] = useState([]);
  const [tabsItems, setTabsItems] = useState([]);
  const [currentStep, setCurrentStep] = useState(5);
  const [loadStep, setLoadStep] = useState(false);
  const [countTasks, setCountTasks] = useState({});
  const [openModalCheckList, setOpenModalCheckList] = useState(false);
  const [dataSteps, setDataSteps] = useState([]);

  const setDefaultTabsItems = () => {
    setTabsItems([
      {
        label: t("layout_profile_details.overview"),
        key: 3,
        children: (
          <Overview
            tasksTypes={tasksTypes}
            addTab={addTab}
            dataSteps={dataSteps}
            elementID={elementID}
            module={module}
          />
        ),
        closable: false,
      },
      {
        label: t("layout_profile_details.activities"),
        key: 1,
        children: (
          <TimeLine
            tasksTypes={tasksTypes}
            setTasksTypes={setTasksTypes}
            setCountTasks={setCountTasks}
            countTasks={countTasks}
            setOpenModal={setOpenModalCheckList}
            openModal={openModalCheckList}
          />
        ),
        closable: false,
      },
      {
        label: "Info général",
        key: 2,
        children: (
          <ProfileDetails
            familyId={module ? module : contactInfo?.family_id}
            contactId={
              elementID
                ? elementID
                : contactInfo?.id
                ? contactInfo.id
                : params?.id
            }
            numberOfColumns={3}
          />
        ),
        closable: false,
      },
      {
        label: t("menu1.callLog"),
        key: 4,
        children: (
          <CallLogs360
            contactId={elementID ? elementID : contactInfo?.id}
            isStepsDisplayed={dataSteps?.length}
          />
        ),
        closable: false,
      },
      {
        label: "Exchanges",
        key: 9,
        children: "Échanges entre collègues",
        closable: false,
      },
      {
        label: "Discussions",
        key: 5,
        children: <ChatRmc dataSteps={dataSteps} />,
        closable: false,
      },
      {
        label: "Notes",
        key: 6,
        children: <Notes />,
        closable: false,
      },
      {
        label: "Email",
        key: 7,
        children: <Mails />,
        closable: false,
      },

      {
        label: t("layout_profile_details.files"),
        key: 8,
        children: <Files />,
        closable: false,
      },
      {
        label: t("layout_profile_details.cart"),
        key: 10,
        children: <Cart />,
        closable: false,
      },
    ]);
  };

  useEffect(() => {
    const getTasksTypes = async () => {
      setLoadTasks(true);
      try {
        const response = await MainService.getTasksTypes();
        setTasksTypes(response?.data?.data?.tasks_type);
        setLoadTasks(false);
      } catch (error) {
        setLoadTasks(false);

        console.log(`Error ${error}`);
      }
    };
    // const getPipelines = async () => {
    //   try {
    //     const response = await MainService.getPipelinesByFamily("7");
    //     let systemPipeline = response?.data?.data.find(
    //       (pipeline) => pipeline?.system === 1
    //     )?.id;
    //     setPipelines(response?.data?.data);
    //   } catch (error) {
    //     console.log(`Error ${error}`);
    //   }
    // };
    const getStagesSteps = async () => {
      setLoadStep(true);
      try {
        const response = await MainService.getStages(
          elementID ? elementID : params.id
        );
        const currentIndex = response.data.data.findIndex(
          (item) => item.currentstage === 1
        );

        setDataSteps(
          response.data.data.map((item, index) => {
            if (index < currentIndex) {
              return { ...item, status: "finish" };
            } else if (index > currentIndex) {
              return { ...item, status: "wait" };
            } else {
              return { ...item, status: "process" };
            }
          })
        );
        setLoadStep(false);
      } catch (error) {
        setLoadStep(false);

        console.log(`Error ${error}`);
      }
    };
    const getTasksCount = async () => {
      try {
        const res = await MainService.getTasks360Count({
          id: params.id,
          types: "",
        });

        setCountTasks(res?.data);
      } catch (err) {}
    };
    // getTasksCount();

    getTasksTypes();
    // getPipelines();
    getStagesSteps();
    // return () => {
    //   dispatch({
    //     type: "RESET_CONTACT_HEADER_INFO",
    //   });
    // };
  }, [params.id, elementID]);

  useEffect(() => {
    setDefaultTabsItems();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    tasksTypes,
    dataSteps.length,
    contactInfo.family_id,
    contactInfo.id,
    openModalCheckList,
  ]);
  useEffect(() => {
    source === "voip" || source === "webPhone"
      ? dispatch(setActiveTab360(2))
      : dispatch(setActiveTab360(3));
    dispatch(setActiveActivity360("1"));
  }, [source]);

  const onChangeTabs = (key) => {
    // setActiveTab(key);
    dispatch(setSearch(""));
    dispatch(setActiveTab360(Number(key)));
  };
  //
  const onChangeStep = (value) => {
    setCurrentStep(value);
  };
  const addTab = (tabKey, children) => {
    setTabsItems((prev) => {
      const isExist = prev?.find((tab) => tab.label === tabKey);
      if (isExist) {
        dispatch(setActiveTab360(tabKey));
        return prev;
      } else {
        dispatch(setActiveTab360(tabKey));
        return [
          ...prev,
          {
            label: tabKey,
            children: children,
            key: tabKey,
          },
        ];
      }
    });
  };

  const removeTab = (tabKey) => {
    const newPanes = tabsItems.filter((tab) => tab.key !== tabKey);
    setTabsItems(newPanes);
    dispatch(setActiveTab360(3));
  };

  const onEditTabs = (tabKey, action) => {
    if (action === "remove") {
      removeTab(tabKey);
    }
  };
  //
  const {
    token: { colorBgContainer },
  } = theme.useToken();
  //
  return (
    <Layout>
      <Sider
        width="360px"
        style={{ background: colorBgContainer }}
        className="sider-360"
      >
        <SiderLayout
          addTab={addTab}
          elementID={elementID}
          module={module}
          source={source}
          setOpenModalCheckList={setOpenModalCheckList}
          handleMinimized={handleMinimized}
        />
      </Sider>
      {!isMinimized && (
        <Layout
          style={{
            padding: "4px 24px ",
            background: "white",
          }}
        >
          {!loadStep ? (
            dataSteps.length > 0 ? (
              <Card
                title={
                  <div>
                    Pipeline: {dataSteps[dataSteps.length - 1].pipeline.label}{" "}
                  </div>
                }
                className="bg-slate-50"
              >
                <Steps
                  type="navigation"
                  size="small"
                  current={dataSteps.findIndex(
                    (item) => item.currentstage === 1
                  )}
                  onChange={onChangeStep}
                  className="site-navigation-steps"
                  items={dataSteps.slice(0, -1).map((el) => ({
                    title: (
                      <Typography.Text>
                        <Badge color={el.color} /> {el.label}{" "}
                        {el.percent ? "(" + el.percent + "% )" : null}
                      </Typography.Text>
                    ),
                    status: el.status,
                  }))}
                />
              </Card>
            ) : null
          ) : (
            <Card
              style={{
                marginTop: 16,
              }}
              title={
                <Skeleton
                  paragraph={{
                    rows: 0,
                  }}
                />
              }
              bordered={true}
            >
              {" "}
              <Skeleton
                paragraph={{
                  rows: 0,
                }}
                style={{ display: "flex", width: "100%" }}
              />
            </Card>
          )}
          <Content
            style={{
              padding: " 24",
              margin: "10px 0",
              minHeight: 280,
              background: "white",
            }}
          >
            <Tabs
              // defaultActiveKey={3}
              hideAdd
              onChange={onChangeTabs}
              activeKey={activeTab360}
              type="editable-card"
              items={tabsItems}
              onEdit={onEditTabs}
              className="tabs360-general"
            />
          </Content>
        </Layout>
      )}
      {user.rmc_access === "OUI" ? (
        <iframe
          src={`${URL_ENV?.REACT_APP_RMC_URL}?Token=${token}`}
          title="chat"
          display="block"
          width="100%"
          // height= {`${deviceHeight}px -120px`}
          sendbox="allow-same-origin allow-popups"
          allowfullscreen="true"
          style={{
            display: "none",
            border: "none",
          }}
          allowtransparency="true"
          // onLoad={() => setHide(true)}
        ></iframe>
      ) : null}
    </Layout>
  );
};
export default LayoutDetails;
