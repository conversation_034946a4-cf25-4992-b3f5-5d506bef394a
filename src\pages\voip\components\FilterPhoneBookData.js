import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Checkbox,
  Popover,
  Space,
  Typography,
} from "antd";
import { FilterTwoTone } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import ChoiceIcons from "pages/components/ChoiceIcons";
import { Refs_IDs } from "components/tour/tourConfig";

const FilterPhoneBookData = ({
  allTypes,
  allSources,
  setTypes,
  types,
  setFamilyId,
  setSources,
  sources,
  familyId,
  isDisabled,
}) => {
  const [t] = useTranslation("common");
  //
  const [open, setOpen] = useState(false);
  const [checkedFamily, setCheckedFamily] = useState([]);
  const [checkedTypes, setCheckedTypes] = useState([]);
  const [checkedSource, setCheckedSources] = useState([]);
  //
  const handleApply = () => {
    setFamilyId(checkedFamily);
    setTypes(checkedTypes);
    setSources(checkedSource);
    setO<PERSON>(false);
  };
  //
  const handleReset = () => {
    familyId?.length && setFamilyId([]);
    setCheckedFamily([]);
    types?.length && setTypes([]);
    setCheckedTypes([]);
    sources.length && setSources([]);
    setCheckedSources([]);
  };

  const contents = (
    <div className="flex flex-col space-y-4">
      <Space style={{ alignItems: "flex-start" }}>
        <Space key="module" direction="vertical">
          <Card
            size="small"
            type="inner"
            styles={{ body: { backgroundColor: "white" } }}
            classNames={{
              body: "p-2 overflow-y-auto max-h-56	",
            }}
            title={
              <span className="font-sans text-sm uppercase">
                {/* {t("voip.family")} */}
                Modules
              </span>
            }
          >
            <Checkbox.Group
              style={{
                display: "flex",
                flexDirection: "column",
              }}
              value={checkedFamily}
              onChange={(check) => setCheckedFamily(check)}
            >
              <Checkbox key={1} value={"1"} style={{ paddingBottom: ".25rem" }}>
                <Typography.Text strong>{t("voip.companies")}</Typography.Text>
              </Checkbox>
              <Checkbox key={2} value={"2"} style={{ paddingBottom: ".25rem" }}>
                <Typography.Text strong>{t("voip.contacts")}</Typography.Text>
              </Checkbox>
              <Checkbox key={9} value={"9"} style={{ paddingBottom: ".25rem" }}>
                <Typography.Text strong>{t("contacts.leads")}</Typography.Text>
              </Checkbox>
              {/* <Checkbox value={"4"} style={{ paddingBottom: ".25rem" }}>
                <Typography.Text strong>{t("voip.colleagues")}</Typography.Text>
              </Checkbox>
              <Checkbox value={"group"} style={{ paddingBottom: ".25rem" }}>
                <Typography.Text strong>{t("voip.groups")}</Typography.Text>
              </Checkbox>
              <Checkbox value={"queue"} style={{ paddingBottom: ".25rem" }}>
                <Typography.Text strong>{t("voip.queues")}</Typography.Text>
              </Checkbox> */}
            </Checkbox.Group>
          </Card>
        </Space>
        {allTypes?.length > 0 && (
          <Space key="types" direction="vertical">
            <Card
              size="small"
              type="inner"
              styles={{ body: { backgroundColor: "white" } }}
              classNames={{
                body: "p-2 overflow-y-auto max-h-56	",
              }}
              title={<span className="font-sans text-sm uppercase">Types</span>}
            >
              <Checkbox.Group
                style={{ display: "flex", flexDirection: "column" }}
                value={checkedTypes}
                onChange={(check) => setCheckedTypes(check)}
                // disabled={
                //   checkedFamily?.includes("4") && checkedFamily?.length === 1
                // }
              >
                {allTypes?.map((type, i) => (
                  <Checkbox
                    key={i}
                    value={type?.id}
                    style={{ paddingBottom: ".25rem" }}
                  >
                    <Badge
                      color={type.color || "white"}
                      text={<strong>{type.label}</strong>}
                    />
                  </Checkbox>
                ))}
              </Checkbox.Group>
            </Card>
          </Space>
        )}
        {allSources?.length > 0 && (
          <Space key="sources" direction="vertical">
            <Card
              size="small"
              type="inner"
              styles={{ body: { backgroundColor: "white" } }}
              classNames={{
                body: "p-2 overflow-y-auto max-h-56	",
              }}
              title={
                <span className="font-sans text-sm uppercase">Sources</span>
              }
            >
              <Checkbox.Group
                style={{ display: "flex", flexDirection: "column" }}
                value={checkedSource}
                onChange={(check) => setCheckedSources(check)}
                // disabled={
                //   checkedFamily?.includes("4") && checkedFamily?.length === 1
                // }
              >
                {allSources?.map((source, i) => (
                  <Checkbox
                    key={i}
                    value={source?.id}
                    style={{ paddingBottom: ".25rem" }}
                  >
                    <Space size={3}>
                      <ChoiceIcons icon={source.icon} fontSize={14} />
                      <Typography.Text strong>{source?.label}</Typography.Text>
                    </Space>
                  </Checkbox>
                ))}
              </Checkbox.Group>
            </Card>
          </Space>
        )}
      </Space>
      <div className="flex justify-end">
        <div className="flex flex-row space-x-2">
          <Button
            size="small"
            disabled={!familyId?.length && !types?.length && !sources.length}
            type="link"
            onClick={handleReset}
          >
            {t("import.reset")}
          </Button>
          <Button
            size="small"
            disabled={
              !familyId?.length &&
              !checkedFamily.length &&
              !types?.length &&
              !checkedTypes.length &&
              !sources.length &&
              !checkedSource.length
            }
            type="primary"
            onClick={handleApply}
          >
            {t("voip.apply")}
          </Button>
        </div>
      </div>
    </div>
  );

  return (
    <Popover
      content={contents}
      open={open}
      onOpenChange={(open) => setOpen(open)}
      // placement="bottomRight"
      trigger={"click"}
    >
      <Button
        ref={Refs_IDs.logs_directory_filter_icon}
        onClick={() => setOpen(!open)}
        disabled={isDisabled}
        type="text"
        shape="circle"
        icon={
          <Badge dot={familyId.length || types.length || sources.length}>
            <FilterTwoTone />
          </Badge>
        }
      />
    </Popover>
  );
};

export default FilterPhoneBookData;
