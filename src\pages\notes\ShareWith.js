import { CloseCircleFilled, LoadingOutlined } from "@ant-design/icons";
import {
  Avatar,
  Button,
  Checkbox,
  Input,
  List,
  Modal,
  Spin,
  Tooltip,
} from "antd";
import NoteAvatar from "pages/components/DetailsProfile/Activities/Notes/NoteAvatar";
import React, { useEffect } from "react";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { getName } from "pages/layouts/chat/utils/ConversationUtils";
import { URL_ENV } from "index";
import { AvatarChat } from "components/Chat";

function ShareWith({ open, setOpen, shareLoading, item, shareFunction }) {
  //   const voipCollegues = useSelector((state) => state.voip.collegues);
  const chatCollegues = useSelector((state) => state.chat.userList);

  const { t } = useTranslation("common");

  const [finalCollegues, setFinalCollegues] = React.useState([]);

  const [selectedCollegues, setSelectedCollegues] = React.useState([]);

  const [unshareWith, setUnshareWith] = React.useState([]);

  const [searchKeyWord, setSearchKeyWord] = React.useState("");

  useEffect(() => {
    //match the collegues from voip and chat with uuid
    // let temp = [];
    // let tempChatCollegues = chatCollegues;

    // voipCollegues.forEach((voipCollegue) => {
    //   let chatCollegue = tempChatCollegues.find(
    //     (chatCollegue) => chatCollegue.uuid === voipCollegue.uuid
    //   );
    //   if (chatCollegue) {
    //     temp.push({ ...chatCollegue, mongoId: voipCollegue.id });
    //   }
    // });
    // setFinalCollegues(temp);

    // console.log("finalCollegues temp", temp);

    let tempSelected = [];

    if (item?.shared_with) {
      //it contains mongo ids of the collegues
      item.shared_with.forEach((sharedWith) => {
        console.log("sharedWith", sharedWith);
        let collegue = chatCollegues.find(
          (collegue) => collegue.uuid == sharedWith.uuid
        );
        if (collegue) {
          tempSelected.push(collegue);
        }
      });

      console.log("temp", tempSelected);

      setSelectedCollegues(tempSelected);
    }
  }, [chatCollegues]);

  const selectAll = () => {
    setSelectedCollegues(chatCollegues);
  };

  const deselectAll = () => {
    setSelectedCollegues([]);
  };

  useEffect(() => {
    //TODO populate initial selectedCollegues
  }, [finalCollegues, item]);

  //TODO: shareFunction
  const share = () => {
    let sharedWith = selectedCollegues?.map((collegue) => collegue.uuid);

    shareFunction(item._id, {
      shared_with: sharedWith,
    });
  };

  //search item.shared_with users in chatCollegues
  const defaultItemSharedWith = () => {
    let temp = [];
    item.shared_with.forEach((sharedWith) => {
      let collegue = chatCollegues.find(
        (collegue) => collegue.uuid === sharedWith.uuid
      );
      if (collegue) {
        temp.push(collegue);
      }
    });
    return temp;
  };

  return (
    <Modal
      title={t("selfNotes.shareTitle")}
      // style={{ width: "700px" }}
      visible={open}
      //   onOk={() => {
      //     share();
      //   }}
      onCancel={() => {
        setOpen(false);
      }}
      //   okText="Share"
      //   cancelText="Cancel"
      //   confirmLoading={shareLoading}
      //   disableOk={selectedCollegues.length === 0}
      footer={null}
      width={700}
    >
      {
        <Avatar.Group
          //   maxCount={7}
          className="mb-0.5 ml-4 flex space-x-1 overflow-x-auto py-2"
        >
          {selectedCollegues.map((collegue) => (
            <Tooltip
              title={collegue.name}
              key={collegue.uuid}
              placement="bottom"
            >
              <div
                style={{
                  position: "relative",
                  cursor: "pointer",
                }}
              >
                <CloseCircleFilled
                  style={{
                    position: "absolute",
                    right: 0,
                    top: "0px",
                    cursor: "pointer",
                    zIndex: 20,
                    color: "red",
                    fontSize: "15px",
                  }}
                  onClick={() =>
                    setSelectedCollegues(
                      selectedCollegues.filter(
                        (selectedCollegue) =>
                          selectedCollegue.uuid !== collegue.uuid
                      )
                    )
                  }
                />
                {/* <NoteAvatar
                  avatar={collegue.image}
                  name={collegue.name}
                  size={38}
                /> */}
                <AvatarChat
                  url={
                    URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                    collegue.image
                  }
                  type="user"
                  size={38}
                  height={10}
                  width={10}
                  name={getName(collegue.name, "avatar")}
                  hasImage={collegue.image}
                />
              </div>
            </Tooltip>
          ))}
        </Avatar.Group>
      }
      {/* <div className="flex items-center justify-between">
        <Button type="link" onClick={selectAll}>
          Select all
        </Button>
        <Button type="link" onClick={deselectAll}>
          Deselect all
        </Button>
      </div> */}

      <Input
        placeholder={t("selfNotes.searchForColleaguesToShare")}
        value={searchKeyWord}
        onChange={(e) => setSearchKeyWord(e.target.value)}
        style={{ marginBottom: 10 }}
      />
      <div className="my-2 flex items-center justify-end">
        <Checkbox
          onChange={(e) => {
            if (e.target.checked) {
              setSelectedCollegues(chatCollegues);
            } else {
              //set them to item.shared_with default
              setSelectedCollegues([]);
            }
          }}
          checked={selectedCollegues.length === chatCollegues.length}
        >
          {t("selfNotes.selectAll")}
        </Checkbox>
      </div>

      <div
        style={{
          maxHeight: 200,
          overflow: "auto",
          padding: "0",
        }}
        className="mb-6"
      >
        <List
          itemLayout="horizontal"
          dataSource={
            chatCollegues.filter(
              (collegue) =>
                collegue.name
                  .toLowerCase()
                  .includes(searchKeyWord.toLowerCase()) ||
                collegue.email
                  .toLowerCase()
                  .includes(searchKeyWord.toLowerCase())
            )
            // .filter(
            //   (collegue) =>
            //     !selectedCollegues.find(
            //       (selectedCollegue) =>
            //         selectedCollegue.mongoId === collegue.mongoId
            //     )
            // )
          }
          key={(item) => item.uuid}
          renderItem={(item) => (
            <List.Item
              style={{
                padding: "4px 10px",
                borderBottom: "1px solid #f0f0f0",
                cursor: "pointer",
                // backgroundColor: selectedCollegues.find(
                //   (selectedCollegue) =>
                //     selectedCollegue.mongoId === item.mongoId
                // )
                //   ? "#f0f0f0"
                //   : "white",
              }}
              onClick={() => {
                console.log("checked item", item);
                if (
                  selectedCollegues.find(
                    (collegue) => collegue.uuid === item.uuid
                  )
                ) {
                  setSelectedCollegues(
                    selectedCollegues.filter(
                      (collegue) => collegue.uuid !== item.uuid
                    )
                  );
                } else {
                  setSelectedCollegues([...selectedCollegues, item]);
                }
              }}
            >
              <List.Item.Meta
                title={
                  <div className="flex items-center">
                    {/* <NoteAvatar
                      avatar={item.image}
                      name={item.name}
                      size={38}
                    /> */}
                    <AvatarChat
                      url={
                        URL_ENV?.REACT_APP_BASE_URL +
                        URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                        item.image
                      }
                      type="user"
                      size={38}
                      height={10}
                      width={10}
                      name={getName(item.name, "avatar")}
                      hasImage={item.image}
                    />
                    <div
                      className="ml-2
                        flex-col
                    "
                    >
                      <div>{item.name}</div>
                      <div className="text-muted text-xs text-gray-500">
                        {item.email}
                      </div>
                    </div>
                  </div>
                }
              />

              <Checkbox
                onChange={(e) => {
                  console.log("checked item", item);
                  if (e.target.checked) {
                    setSelectedCollegues([...selectedCollegues, item]);
                  } else {
                    setSelectedCollegues(
                      selectedCollegues.filter(
                        (collegue) => collegue.uuid !== item.uuid
                      )
                    );
                  }
                }}
                checked={selectedCollegues.find(
                  (selectedCollegue) => selectedCollegue.uuid === item.uuid
                )}
              />
            </List.Item>
          )}
        />
      </div>
      <p
        className="text-xs text-gray-500"
        style={{
          textAlign: "justify",
          padding: "10px",
        }}
      >
        {t("selfNotes.sharingInfo")}
      </p>
      <div className="mt-2 flex items-center justify-end space-x-2">
        <Button
          type="default"
          primary
          onClick={() => {
            setOpen(false);
          }}
        >
          {t("selfNotes.cancelShare")}
        </Button>

        <Button
          type="primary"
          // disabled={shareLoading}
          loading={shareLoading}
          onClick={() => {
            share();
          }}
        >
          {/* {shareLoading ? (
            <Spin
              indicator={<LoadingOutlined spin />}
              size="small"
              //color="blue"
            />
          ) : (
            <>{t("selfNotes.saveShare")}</>
          )} */}
          <>{t("selfNotes.saveShare")}</>
        </Button>
      </div>
    </Modal>
  );
}

export default ShareWith;
