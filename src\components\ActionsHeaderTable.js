import {
  CloseOutlined,
  DeleteOutlined,
  EditOutlined,
  RestOutlined,
  SaveOutlined,
} from "@ant-design/icons";
import { Popconfirm, Space, Tooltip, Button } from "antd";
import React, { useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import Confirm from "./GenericModal";
import {
  FiEdit3,
  FiSave,
  FiTrash2,
  FiX,
  FiXCircle,
  FiXSquare,
} from "react-icons/fi";
import moment from "moment";

const ActionsHeaderTable = React.forwardRef((props, ref) => {
  const {
    record,
    edit,
    handleDelete,
    form,
    cancel,
    isEditing,
    data,
    setData,
    editingKey,
    api,
    source,
    confirmLoading,
    fieldType,
    updateOptionField,
    setEditingRecord,
  } = props;
  const editable = isEditing(record);
  const [t] = useTranslation("common");
  const handleDisableDeleteBtn = (array, fieldType, rec) => {
    if (fieldType !== "radio" && updateOptionField == 0 && array.length <= 1) {
      return true;
    } else if (updateOptionField != 0) {
      if (
        array.map((element) => element?.id).filter((el) => el != undefined)
          .length <= 1 &&
        rec?.key == undefined
      ) {
        return true;
      }
    }
    if (fieldType == "radio") {
      if (updateOptionField == 0 && array.length <= 2) {
        return true;
      } else if (
        array.map((element) => element?.id).filter((el) => el != undefined)
          .length <= 2 &&
        rec?.key == undefined
      ) {
        return true;
      }
    }
    return false;
  };
  return (
    <>
      {editable ? (
        <div>
          <div className="flex">
            <Popconfirm
              title={t("fields_management.cancelAlertMsg")}
              onConfirm={(e) => {
                e.stopPropagation();
                cancel(record);
              }}
            >
              <Button
                type="text"
                size="small"
                shape="circle"
                danger
                icon={<FiX className="text-slate-400" />}
                onClick={(e) => {
                  e.stopPropagation();
                }}
              />
            </Popconfirm>
            <Tooltip title={t("form.save")}>
              <Button
                id="buttonSaveTable"
                type="text"
                size="small"
                ref={ref}
                shape="circle"
                icon={<FiSave className="text-green-600" />}
                onClick={(e) => {
                  e.stopPropagation();
                  form.submit();
                }}
              />
            </Tooltip>
          </div>
        </div>
      ) : (
        <div className="mt-[2px] flex">
          <Button
            type="text"
            size="small"
            shape="circle"
            danger
            icon={<FiTrash2 />}
            disabled={
              api === "task-types"
                ? record?.count > 0 ||
                  record.default === 1 ||
                  (api === "stages" && record.can_update_rank === 0)
                : record?.is_used == 1 ||
                  record?.default == 1 ||
                  record?.seeder == 1 ||
                  record?.default == 2 ||
                  record?.system == 1 ||
                  record?.editable == 0 ||
                  (api === "indisponibilite" &&
                    record.start_date < moment().format("YYYY-MM-DD")) ||
                  (source === "fieldOptions" &&
                    handleDisableDeleteBtn(data, fieldType, record))
                ? true
                : false
            }
            onClick={(e) => {
              e.stopPropagation();

              record?.id
                ? Confirm(
                    ` ${t("table.delete")} "${
                      record.name?.replace(/<\/?[^>]+(>|$)/g, "") ||
                      record.label_fr?.replace(/<\/?[^>]+(>|$)/g, "") ||
                      record.label?.replace(/<\/?[^>]+(>|$)/g, "") ||
                      record.Bank?.replace(/<\/?[^>]+(>|$)/g, "") ||
                      record.title?.replace(/<\/?[^>]+(>|$)/g, "") ||
                      record?.listElementValue?.replace(
                        /<\/?[^>]+(>|$)/g,
                        ""
                      ) ||
                      t(`toasts.${api}`)
                    }" !`,
                    "Confirm",
                    <RestOutlined style={{ color: "red" }} />,
                    function func() {
                      return handleDelete(
                        record.id == undefined
                          ? record.key
                          : source == "draggableTable"
                          ? {
                              fieldId: record.id.toString(),
                              groupId: record?.field_group_id,
                            }
                          : record.id.toString() || record.key
                      );
                    },
                    true,
                    api === "departments" ||
                      api === "type-family-products" ||
                      api === "pipelines" ||
                      (api === "steps" &&
                        data[data.indexOf(record) + 1]?.confirmation ===
                          "true") ? (
                      <span className="text-xs">
                        {t(`beforeDeleted.${api}`)}
                      </span>
                    ) : (
                      ""
                    )
                  )
                : setData(data.filter((item) => item.key !== record.key));
            }}
          />
          {source !== "fieldOptions" && (
            // && api !== "external-tokens"
            <Button
              type="link"
              size="small"
              shape="circle"
              icon={<FiEdit3 />}
              disabled={
                // (api !== "task-types" &&
                //   api !== "stages" &&
                //   api !== "pipelines" &&
                //   api !== "fields-management") ||
                // (api !== "stages" &&
                //   api !== "channels" &&
                //   api !== "severities" &&
                //   record?.system == 1) ||
                // record?.default == 2 ||
                // api === "fields-groups"
                (api === "stages" && record.can_update_rank === 0) ||
                (api === "indisponibilite" &&
                  record.start_date < moment().format("YYYY-MM-DD")) ||
                (api === "fields-groups" && record?.system === 1) ||
                !data.every((objet) => "id" in objet)
                  ? true
                  : false
              }
              onClick={(e) => {
                e.stopPropagation();
                edit(record);
                setEditingRecord(form.getFieldValue());
                if (api !== "fields-groups")
                  setTimeout(() => {
                    if (ref?.current) {
                      ref.current.disabled = true;
                      ref.current.querySelector("svg").style.color =
                        "#00000040";
                    }
                  }, 0);
                // if (api !== "fields-groups")
                //   setTimeout(() => {
                //     let myButton = document.getElementById("buttonSaveTable");

                //     var svgElement = myButton.querySelector("svg");

                //     myButton.disabled = true;
                //     svgElement.style.color = "#00000040";
                //   }, 0);
              }}
            />
          )}
        </div>
      )}
    </>
  );
});

export default ActionsHeaderTable;
