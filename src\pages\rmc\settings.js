import { useState } from "react";
import { LoadingAnimation } from "../components/loader";
import { useSelector } from "react-redux";
import { URL_ENV } from "index";
import Unauthorized from "pages/403/Unauthrorized";
import { roles } from "utils/role";

const RmcSettings = () => {
  const [hide, setHide] = useState(false);
  const { user } = useSelector((state) => state.user);
  const token = localStorage.getItem("accessToken");

  return (
    <div>
      {user?.access["rmc"] === "1" && roles.includes(user?.role) ? (
        <>
          {hide === false ? <LoadingAnimation /> : <></>}
          <iframe
            src={`${URL_ENV?.REACT_APP_RMC_URL}?Token=${token}&area=settings`}
            title="chat"
            display="block"
            width="100%"
            // height= {`${deviceHeight}px -120px`}
            sendbox="allow-same-origin allow-popups"
            allowfullscreen="true"
            style={{ height: "calc(100vh - 70px)", border: "none" }}
            allowtransparency="true"
            onLoad={() => setHide(true)}
          ></iframe>
        </>
      ) : (
        <Unauthorized />
      )}
    </div>
  );
};
export default RmcSettings;
