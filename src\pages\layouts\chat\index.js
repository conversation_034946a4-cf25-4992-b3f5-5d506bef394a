import ChatSidebar from "./sidebar/sidebar";
import { useDispatch, useSelector } from "react-redux";
import { lazy, memo, Suspense, useLayoutEffect } from "react";
import {
  resetStateOtherUser,
  setChatSelectedConversation,
  setChatSelectedParticipants,
  setSearchChatSidebar,
} from "new-redux/actions/chat.actions";
import { useTranslation } from "react-i18next";
import { Alert, Skeleton, Typography } from "antd";
import { Loader } from "components/Chat";
import { lazyRetry } from "utils/lazyRetry";
import { motion } from "framer-motion";
import useKeyDown from "custom-hooks/useKeyDown";
import { drawerTypeArray } from "./utils/ConversationUtils";
import MainService from "services/main.service";
import { redirectToChat } from "new-redux/services/chat.services";

const SavedMessage = lazy(() =>
  lazyRetry(() => import("./drawer/SavedMessage"), "SavedMessage")
);

const PollsRender = lazy(() =>
  lazyRetry(() => import("./drawer/PollsRender"), "PollsRender")
);
const ChatDrawerAction = lazy(() =>
  lazyRetry(() => import("./drawer/ChatDrawerAction"), "ChatDrawerAction")
);

const ChatInfo = lazy(() =>
  lazyRetry(() => import("./drawer/info/info"), "ChatInfo")
);

const Header = lazy(() =>
  lazyRetry(() => import("./conversation/header/Header"), "Header")
);
const ChatConversations = lazy(() =>
  lazyRetry(() => import("./conversation/body"), "ChatConversations")
);
const InputChat = lazy(() =>
  lazyRetry(() => import("./conversation/input"), "InputChat")
);

const ChatDrawerThread = lazy(() =>
  lazyRetry(() => import("./drawer/thread"), "ChatDrawerThread")
);

export const STALE_TIME_CACHE = 1000 * 60 * 15;
const LoadFunction = () =>
  Array.from({ length: 3 }, (_, i) => i + 1).map((item) => (
    <div className="flex flex-col  items-center px-1 py-3" key={`sklt_${item}`}>
      <Skeleton
        avatar
        paragraph={{
          rows: Math.floor(
            (Math.random() + 1) * Array.from({ length: 4 }, (i) => i + 1).length
          ),
        }}
        active
      />
    </div>
  ));

export const LoaderDrawer = () => (
  <motion.div
    animate={{
      zIndex: 100,
      width: -200,
    }}
    initial={{
      width: 0,
    }}
    transition={{
      duration: 0.5,
    }}
    className={` relative flex h-full  flex-col items-center justify-center overflow-x-hidden  bg-slate-50   pb-1 pt-3 drop-shadow-2xl  `}
  >
    <Loader size={36} />
  </motion.div>
);
const ChatContainer = () => {
  const {
    openDrawer,
    sidebarDrawer,
    currentUser,
    userIsTyping,
    openSideBarDrawer,
  } = useSelector((state) => state.chat);
  const selectedConversation = useSelector(
    (state) => state.ChatRealTime.selectedConversation
  );

  const dispatch = useDispatch();
  const [t] = useTranslation("common");

  useKeyDown(27, false, "keydown", () => {
    if (openDrawer?.type) {
      dispatch(
        resetStateOtherUser({
          forced: true,
          keepDrawerOpened: false,
          item: null,
        })
      );
    } else {
      userIsTyping === 1 &&
        MainService.stopTyping(
          selectedConversation?.id,
          selectedConversation?.type
        );
      dispatch(setChatSelectedParticipants({ selectedParticipants: [] }));

      dispatch(
        setChatSelectedConversation({
          selectedConversation: null,
        })
      );
    }
  });
  useLayoutEffect(() => {
    const clearFunction = () => {
      dispatch(
        resetStateOtherUser({
          forced: false,
          keepDrawerOpened: false,
          item: null,
        })
      );
      const getRedirectChatLink = localStorage.getItem("redirectChatLink");
      if (getRedirectChatLink) {
        const urlLast = new URL(getRedirectChatLink);
        dispatch(redirectToChat(urlLast.searchParams.get("email")));
      } else
        dispatch(
          setChatSelectedConversation({
            selectedConversation: null,
          })
        );
    };
    clearFunction();

    return () => {
      const url = new URL(window.location.href);
      if (url.pathname !== "/chat") {
        clearFunction();
        // Added this line to reset the chat search. Without it, after navigating to another module and hover in icon chat, the chat list preview still shows filtered results even though the search input is cleared.
        // Hassine Basla
        dispatch(setSearchChatSidebar(""));
      }
    };
  }, [dispatch, sidebarDrawer]);
  if (currentUser?.status === 2)
    return (
      <div className="flex h-full  w-full items-center justify-center ">
        <Alert
          message={
            <div className="flex  items-center  justify-center space-x-2 px-2 py-5">
              <Typography.Title level={4} type="danger">
                {t("chat.message_system.DeniedAccessChat") + "..."}
              </Typography.Title>
            </div>
          }
          type="error"
        />
      </div>
    );
  return (
    <div
      id="chatContainer"
      className="flex h-full w-full justify-between overflow-hidden"
    >
      <div className="relative z-10   w-[360px] bg-slate-50 pb-2">
        <ChatSidebar source="chat" />
      </div>

      {selectedConversation?.id && !selectedConversation?.external ? (
        <div className="flex flex-1 flex-col justify-between">
          <Suspense
            fallback={
              <Skeleton
                active
                className=" w-full p-2  shadow-sm"
                avatar
                paragraph={{ rows: 1 }}
              />
            }
          >
            <Header
              key={`${selectedConversation?.id}_${selectedConversation?.type}`}
            />
          </Suspense>
          <div id="containerChat" className=" flex-1 overflow-hidden pb-1 pl-4">
            <Suspense fallback={<LoadFunction />}>
              <ChatConversations
                key={`${selectedConversation?.id}_${selectedConversation?.type}`}
              />
            </Suspense>
          </div>

          <Suspense fallback={<Skeleton.Input active block />}>
            <InputChat
              key={`${selectedConversation?.id}_${selectedConversation?.type}`}
            />
          </Suspense>
        </div>
      ) : (
        <div className="flex h-full flex-1   items-center justify-center space-x-2">
          <blockquote className="flex  flex-col space-y-2 text-[#46B7CF]">
            <q className="text-5xl ">{t("chat.emptyPlaceholder")}</q>
            <p className="text-right">-- Socrates</p>
          </blockquote>
        </div>
      )}
      {openDrawer.type === "info" && (
        <div className={`relative w-96  bg-slate-50`}>
          <Suspense fallback={<LoaderDrawer />}>
            <ChatInfo key={selectedConversation?.conversationId} />
          </Suspense>
        </div>
      )}
      {drawerTypeArray.includes(openDrawer?.type) && (
        <Suspense fallback={<LoaderDrawer />}>
          <ChatDrawerAction
            key={selectedConversation?.id + "_" + selectedConversation?.type}
          />
        </Suspense>
      )}

      {openDrawer?.type === "thread" && (
        <Suspense fallback={<LoaderDrawer />}>
          <ChatDrawerThread />
        </Suspense>
      )}
      {openSideBarDrawer && (
        <Suspense fallback={<></>}>
          <SavedMessage />
        </Suspense>
      )}
      {openDrawer.type?.includes("polls_") && (
        <Suspense fallback={<LoaderDrawer />}>
          <PollsRender />
        </Suspense>
      )}
    </div>
  );
};

export default memo(ChatContainer);
