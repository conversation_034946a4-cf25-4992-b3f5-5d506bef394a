import { Alert, Form, Select, Space, Spin } from "antd";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { toastNotification } from "../../components/ToastNotification";
import moment from "moment-timezone";
import { FormFooter } from "../components/FormFooter";
import { generateAxios } from "../../services/axiosInstance";
import { AllCountries } from "../../components/AllCountries";
import { BsXLg } from "react-icons/bs";
import { useDispatch } from "react-redux";
import { SET_USER_INFOS } from "../../new-redux/constants";
import { useSelector } from "react-redux";
import { URL_ENV } from "index";
import { lang } from "translations/lang";
import MainService from "services/main.service";
import { setDateDashboard } from "new-redux/actions/dashboard.actions";
import dayjs from "dayjs";
export const optionsDateFormat = (t) => {
  return [
    {
      value: "DD-MM-YYYY",
      label: "d-m-y [18-01-2023]",
    },
    {
      value: "MM-DD-YYYY",
      label: "m-d-y [01-18-2023]",
    },
    {
      value: "MM.DD.YYYY",
      label: "m.d.y [01.18.2023]",
    },
    {
      value: "DD.MM.YYYY",
      label: "d.m.y [18.01.2023]",
    },
    {
      value: "DD/MM/YYYY",
      label: "d/m/y [18/01/2023]",
    },
    {
      value: "MM/DD/YYYY",
      label: "m/d/y [01/18/2023]",
    },
    // {
    //   value: "MMMM DD,YYYY",
    //   label: `F j,Y [${t("localisation.wednesday")} 18,2023]`,
    // },

    // {
    //   value: "DD MMMM,YYYY",
    //   label: `j F,Y [18 ${t("localisation.january")},2023]`,
    // },
    // {
    //   value: "dddd, MMMM DD,YYYY",
    //   label: `l, F j, Y [${t("localisation.wednesday")}, ${t(
    //     "localisation.january"
    //   )} 18, 2023]`,
    // },
    // {
    //   value: "ddd, MMMM DD,YYYY",
    //   label: `D, F j, Y [${t("localisation.wednesday").slice(0, 3)}, ${t(
    //     "localisation.january"
    //   )} 18, 2023]`,
    // },
  ];
};
const LocalisationSettings = () => {
  const dispatch = useDispatch();
  const [listLocalisations, setListLocalisations] = useState({});
  const [loadListLocalisations, setLoadListLocalisations] = useState(null);
  const [isUpdated, setIsUpdated] = useState(false);
  const [countries, setCountries] = useState([]);
  const [loadButton, setLoadButton] = useState(false);
  const [disabled, setDisabled] = useState(true);
  const [t, i18n] = useTranslation("common");
  const [form] = Form.useForm();
  const { user } = useSelector((state) => state.user);
  const { startDate, endDate } = useSelector(
    (state) => state.dashboardRealTime
  );
  useEffect(() => {
    const getLocalisation = async () => {
      setLoadListLocalisations(true);
      try {
        const [result, countries] = await Promise.all([
          generateAxios(
            URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
          ).get("/get-localization"),
          generateAxios(
            URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
          ).get("countries"),
          MainService.getCurrencies(),
        ]);

        form.setFieldsValue({
          dial_code: result?.data?.data?.dial_code,
        });
        setCountries(countries?.data?.data);

        setListLocalisations(result.data.data);
        setLoadListLocalisations(false);
      } catch (err) {
        setLoadListLocalisations(false);

        console.log(err);
      }
    };
    getLocalisation();
  }, []);

  useEffect(() => {
    if (isUpdated) setDisabled(false);
    else setDisabled(true);
  }, [isUpdated]);
  const onFinish = async (values) => {
    setDisabled(true);
    setLoadButton(true);

    try {
      const { data } = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).post("localizations/update", { ...values });
      setLoadButton(false);
      setDefaultLanguage(values.default_language);

      dispatch({
        type: SET_USER_INFOS,
        payload: {
          ...user,
          location: data.data,
        },
      });
      if (startDate && endDate) {
        dispatch(
          setDateDashboard({
            startDate: dayjs(startDate, user?.location?.date_format).format(
              data.data.date_format
            ),
            endDate: dayjs(endDate, user?.location?.date_format).format(
              data.data.date_format
            ),
          })
        );
      }
      setListLocalisations(data.data);

      toastNotification(
        "success",
        `${t("menu2.localisation")} ${t("toasts.updated")}`,
        "topRight"
      );
    } catch (err) {
      setLoadButton(false);
      setDisabled(false);

      toastNotification("error", `error`, "topRight");
      console.log(err);
    }
  };
  const onFinishFailed = async () => {};

  const optionsTimeFormat = [
    {
      value: "HH:mm",
      label: `24 ${t("tasks.hours")}`,
    },
    {
      value: "h:mm a",
      label: `12 ${t("tasks.hours")}`,
    },
  ];
  const optionsWeekStarted = [
    {
      value: "monday",
      label: t("localisation.monday"),
    },
    {
      value: "saturday",
      label: t("localisation.saturday"),
    },
    {
      value: "sunday",
      label: t("localisation.sunday"),
    },
  ];

  const timezoneList = moment.tz.names().map((tz) => {
    const offset = moment.tz(tz).format("Z");
    return { name: tz, offset };
  });

  const changeToEnglish = () => {
    i18n.changeLanguage("en");
    localStorage.setItem("language", "en");
  };

  const changeToFrench = () => {
    i18n.changeLanguage("fr");
    localStorage.setItem("language", "fr");
  };

  const setDefaultLanguage = (lang) => {
    if (lang === "en") {
      changeToEnglish();
    } else if (lang === "fr") {
      changeToFrench();
    }
  };
  // console.log(timezoneList);

  const onValuesChange = (changedValues, allValues) => {
    // let myButton = document.getElementById("buttonSaveTable");
    let commonAttributes = {};
    // let svgElement = myButton.querySelector("svg");

    // Obtenir la liste des clés de l'objet plus petit
    let smallerKeys = Object.keys(allValues);

    // Filtrer les attributs de l'objet plus grand en fonction de la liste des clés
    listLocalisations &&
      smallerKeys.forEach(function (key) {
        if (key in listLocalisations) {
          commonAttributes[key] = listLocalisations[key];
        }
      });

    let trimmedJsonObject = Object.keys(allValues).reduce((acc, key) => {
      if (typeof allValues[key] === "string") {
        acc[key] = allValues[key].trim();
      } else {
        acc[key] = allValues[key] || null;
      }
      return acc;
    }, {});

    // if (
    //   JSON.stringify(commonAttributes) !== JSON.stringify(trimmedJsonObject)
    // ) {
    //   setDisabled(false);
    // } else {
    //   setDisabled(true);
    // }
    setDisabled(false);
  };
  return (
    <>
      {loadListLocalisations === false ? (
        <Space direction="vertical">
          {!listLocalisations ? (
            <div className="px-6 py-3">
              <Alert message={t("localisation.noConfig")} type="error" />
            </div>
          ) : (
            ""
          )}
          <div
            className={`flex items-center justify-start px-6 ${
              listLocalisations ? "py-4" : ""
            }`}
          >
            <Form
              form={form}
              name="basic"
              labelCol={{
                span: 24,
              }}
              wrapperCol={{
                span: 24,
              }}
              initialValues={{
                remember: true,
              }}
              onFinish={onFinish}
              onFinishFailed={onFinishFailed}
              autoComplete="off"
              scrollToFirstError
              layout="vertical"
              onValuesChange={(changedValues, allValues) =>
                onValuesChange(changedValues, allValues)
              }
            >
              <Form.Item
                label={t("localisation.dateFormat")}
                name="date_format"
                initialValue={
                  optionsDateFormat(t)?.find(
                    (el) => el.value === listLocalisations?.date_format
                  )?.value || null
                  //  optionsDateFormat(t)[0].value
                }
                rules={[
                  {
                    required: true,
                  },
                ]}
              >
                <Select
                  popupMatchSelectWidth={false}
                  placeholder="Select your Date Format"
                  options={optionsDateFormat(t)}
                />
              </Form.Item>
              <Form.Item
                label={t("localisation.timeFormat")}
                name="time_format"
                // shouldUpdate={(prevValues, curValues) =>
                //   setIsUpdated(
                //     JSON.stringify(prevValues) !== JSON.stringify(curValues)
                //   )
                // }
                initialValue={
                  optionsTimeFormat.find(
                    (el) => el.value === listLocalisations?.time_format
                  )?.value || null
                  // optionsTimeFormat[0]?.value
                }
                rules={[
                  {
                    required: true,
                  },
                ]}
              >
                <Select
                  popupMatchSelectWidth={false}
                  placeholder="Select your time Format"
                  // style={{
                  //   width: "300px",
                  // }}
                  options={optionsTimeFormat}
                />
              </Form.Item>

              <Form.Item
                label={t("localisation.weekStartsOn")}
                name="week_started"
                // shouldUpdate={(prevValues, curValues) =>
                //   setIsUpdated(
                //     JSON.stringify(prevValues) !== JSON.stringify(curValues)
                //   )
                // }
                placeholder="Select your time Format"
                initialValue={
                  optionsWeekStarted.find(
                    (el) => el.value === listLocalisations?.week_started
                  )?.value || null
                  // optionsWeekStarted[0]?.value
                }
                rules={[
                  {
                    required: true,
                  },
                ]}
              >
                <Select
                  popupMatchSelectWidth={false}
                  placeholder="Select your week starts on"
                  // style={{
                  //   width: "300px",
                  // }}
                  options={optionsWeekStarted}
                />
              </Form.Item>
              <Form.Item
                label={t("localisation.defaultLanguage")}
                name="default_language"
                // shouldUpdate={(prevValues, curValues) =>
                //   setIsUpdated(
                //     JSON.stringify(prevValues) !== JSON.stringify(curValues)
                //   )
                // }
                initialValue={
                  i18n.language
                  // optionsDefaultLanguage.find(
                  //   (el) => el.value === listLocalisations?.default_language
                  // )?.value || optionsDefaultLanguage[1]?.value
                }
                rules={[
                  {
                    required: true,
                  },
                ]}
              >
                <Select
                  popupMatchSelectWidth={false}
                  placeholder="Select your default Language"
                  options={lang}
                />
              </Form.Item>

              <Form.Item
                label={t("localisation.defaultTimezone")}
                // shouldUpdate={(prevValues, curValues) =>
                //   setIsUpdated(
                //     JSON.stringify(prevValues) !== JSON.stringify(curValues)
                //   )
                // }
                name="default_timezone"
                rules={[
                  {
                    required: true,
                  },
                ]}
                initialValue={
                  listLocalisations?.default_timezone ||
                  // timezoneList.find((el) => el.name === localTimeZone)?.name +
                  //   " " +
                  //   timezoneList.find((el) => el.name === localTimeZone)
                  //     ?.offset ||
                  null
                }
              >
                <Select
                  popupMatchSelectWidth={false}
                  showSearch
                  placeholder="Select Timezone"
                >
                  {timezoneList.map((tz) => (
                    <Select.Option
                      key={tz.name}
                      value={tz.name + " " + tz.offset}
                    >
                      {tz.name} ({tz.offset})
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                label={t("localisation.dialCode")}
                // shouldUpdate={(prevValues, curValues) =>
                //   setIsUpdated(
                //     JSON.stringify(prevValues) !== JSON.stringify(curValues)
                //   )
                // }
                name="dial_code"
                rules={[
                  {
                    required: true,
                  },
                ]}
              >
                <Select
                  popupMatchSelectWidth={false}
                  allowClear
                  showSearch
                  optionFilterProp={["nameEn", "nameFr", "value"]}
                  filterOption={(input, option) =>
                    option?.nameEn
                      .toLowerCase()
                      .includes(input.toLowerCase()) ||
                    option?.nameFr
                      .toLowerCase()
                      .includes(input.toLowerCase()) ||
                    option?.value.toLowerCase().includes(input.toLowerCase())
                  }
                  placeholder="dial_code"
                >
                  {countries.map((el, i) => (
                    <Select.Option
                      key={i}
                      value={el?.dial_code}
                      nameEn={el?.name_en}
                      nameFr={el?.name_fr}
                    >
                      {el.flag} {el.dial_code}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>

              <FormFooter
                onClickCancel={() => form.setFieldsValue(listLocalisations)}
                loadButton={loadButton}
                submitDisabled={disabled}
              />
            </Form>
          </div>
        </Space>
      ) : (
        <div className="flex h-[300px] w-[397px] items-center justify-center">
          <Spin />
        </div>
      )}
    </>
  );
};

export default LocalisationSettings;
