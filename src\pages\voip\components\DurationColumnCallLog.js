import { useEffect, useRef, useState } from "react";
import { Badge, Button, Popover, Tooltip } from "antd";
import { billSecToHumanReadable } from "../helpers/helpersFunc";
import { PlayCircleOutlined } from "@ant-design/icons";
import { FaVoicemail } from "react-icons/fa";

const DurationColumnCallLog = ({
  audioRecording,
  state,
  actionSeen,
  id,
  idCall,
  duration,
  source,
  t,
}) => {
  const audioRef = useRef(null);
  const [popoverVisible, setPopoverVisible] = useState(false);

  // When the popover is visible and the audio element is mounted,
  // then load the source and play the audio.
  useEffect(() => {
    if (popoverVisible && audioRecording && audioRef.current) {
      audioRef.current.src = audioRecording;
      // play() returns a promise - you can catch errors if needed.
      audioRef.current.play().catch((err) => {
        console.error("Audio playback failed:", err);
      });
    }
  }, [popoverVisible, audioRecording]);

  // When the user clicks the play button, just mark the item as seen (if needed)
  // and show the popover.
  const handlePlayClick = () => {
    if (state === "Nouveau" && actionSeen) {
      actionSeen(id, idCall || null);
    }
    setPopoverVisible(true);
  };

  // Handle popover visibility change. When closed, pause and reset the audio.
  const handlePopoverVisibleChange = (visible) => {
    setPopoverVisible(visible);
    if (!visible && audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
    }
  };

  // If there's no duration but the state is new, mark as seen.
  if (!duration && state === "Nouveau" && actionSeen) {
    actionSeen(id, idCall || null);
  }

  // Define what the Popover displays.
  const popoverContent = (
    <audio ref={audioRef} controls style={{ width: "20rem" }} />
  );

  return duration && audioRecording ? (
    <div
      className="DurationColumn relative w-full"
      key={`${id}-${duration}-${audioRecording}`}
    >
      <div className="flex items-center space-x-0.5">
        {audioRecording && (
          <Popover
            content={popoverContent}
            title={null}
            // arrow={false}
            trigger="click"
            placement="bottomLeft"
            open={popoverVisible}
            onOpenChange={handlePopoverVisibleChange}
            destroyTooltipOnHide
            // style={{ padding: 0 }}
          >
            <Button
              type="link"
              shape="circle"
              icon={<PlayCircleOutlined style={{ fontSize: 16 }} />}
              onClick={handlePlayClick}
            />
          </Popover>
        )}
        <div className="flex items-center space-x-2">
          {state === "Nouveau" && <Badge status="processing" />}
          <p>
            {source === "messaging"
              ? duration
              : billSecToHumanReadable(duration)}
          </p>
          {source === "callLog" && (
            <Tooltip title={t("voip.afterCallForwardingToVoicemail")}>
              <FaVoicemail
                style={{
                  fontSize: 20,
                  marginTop: -1,
                  color: "#1677FF",
                  cursor: "help",
                }}
              />
            </Tooltip>
          )}
        </div>
      </div>
    </div>
  ) : (
    <p className="font-semibold">{`${t("voip.emptyContent")} (0 sec)`}</p>
  );
};

export default DurationColumnCallLog;
