import i18n from "i18next";
import common_fr from "./fr/common.json";
import common_en from "./en/common.json";
import { initReactI18next } from "react-i18next";
import LanguageDetector from "i18next-browser-languagedetector";

const storedLang = localStorage.getItem("language")?.toLowerCase();
const normalizedLang = storedLang?.startsWith("en") ? "en" : "fr";
i18n
  .use(initReactI18next)
  .use(LanguageDetector)
  .init({
    interpolation: { escapeValue: false },
    lng: normalizedLang,
    fallbackLng: ["fr"],
    resources: {
      en: {
        common: common_en, // 'common' is our custom namespace
      },
      fr: {
        common: common_fr,
      },
    },
  });
export default i18n;
