import { memo, useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import {
  Button,
  Checkbox,
  Divider,
  Form,
  Input,
  Modal,
  Select,
  Skeleton,
  Space,
  Spin,
} from "antd";
import { getConfigLabel, postConfigLabel } from "../services/services";
import { toastNotification } from "components/ToastNotification";
import { MinusCircleOutlined, PlusOutlined } from "@ant-design/icons";
import { debounce } from "lodash";
import MainService from "services/main.service";

export const ModalConfigLabel = memo(
  ({ isOpen, setIsOpen, infoLabel, setInfoLabel, usedAccount }) => {
    //
    const [t] = useTranslation("common");
    const [form] = Form.useForm();
    //
    const [loading, setLoading] = useState(false);
    const [options, setOptions] = useState([]);
    const [loadingOptions, setLoadingOptions] = useState(false);
    const [selectedOptions, setSelectedOptions] = useState([]);
    const [subjects, setSubjects] = useState([{ key: Date.now(), value: "" }]);
    const [loadingSubmit, setLoadingSubmit] = useState(false);
    const [showInInbox, setShowInInbox] = useState(true);
    //
    // console.log({ options, selectedOptions });
    //
    const fetchConfigLabel = useCallback(async () => {
      if (!infoLabel?.id || !isOpen) return;
      try {
        setLoading(true);
        const {
          data: { data },
        } = await getConfigLabel(infoLabel?.id);
        // console.log({ data });
        if (!data) {
          resetState();
          return;
        }
        const { address_sender, subject, show_inbox } = data;
        if (address_sender.length) {
          setOptions(
            address_sender.map((address) => ({
              label: address,
              value: address,
            }))
          );
          setSelectedOptions(address_sender);
          form.setFieldValue("sender", address_sender);
        }
        subject?.length &&
          setSubjects([
            ...subject.map((subject, i) => ({
              key: Date.now() + i,
              value: subject,
            })),
          ]);
        setShowInInbox(!!show_inbox);
        // console.log({ response });
      } catch (err) {
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
        throw new Error(err?.message ? `error: ${err.message}` : { err });
      } finally {
        setLoading(false);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isOpen]);

    useEffect(() => {
      fetchConfigLabel();
    }, [fetchConfigLabel]);
    //
    const getOptions = async (search) => {
      if (!search || !search?.length) {
        setOptions([]);
        return;
      }
      setLoadingOptions(true);
      var formData = new FormData();
      formData.append("search", search.toLowerCase());
      formData.append("accountId", usedAccount?.value);

      try {
        const {
          data: { address },
        } = await MainService.searchEmail(formData);
        if (address.length)
          setOptions(
            address.map((item) => ({
              label: item,
              value: item,
            }))
          );
        else if (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(search))
          setOptions([
            {
              label: search,
              value: search,
            },
          ]);
        else setOptions([]);
      } catch (err) {
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
        throw new Error(err?.message ? `error: ${err.message}` : { err });
      } finally {
        setLoadingOptions(false);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
    const debouncedSearch = useCallback(
      debounce((nextValue) => getOptions(nextValue), 500),
      []
    );
    //
    const addSubject = () => {
      setSubjects((prevSubjects) => [
        ...prevSubjects,
        { key: Date.now(), value: "" },
      ]);
    };
    const removeSubject = (keyToRemove) => {
      setSubjects((prevSubjects) =>
        prevSubjects.filter((subject) => subject.key !== keyToRemove)
      );
    };
    //
    const submitConfig = async (values) => {
      if (!selectedOptions?.length && !subjects[0]?.value) {
        toastNotification(
          "warning",
          t("mailing.atLeastAddressOrSubject"),
          "topRight",
          5
        );
        return;
      }
      try {
        setLoadingSubmit(true);
        const formData = new FormData();
        formData.append("label_id", infoLabel?.id);
        formData.append("account_id", usedAccount?.value);
        formData.append("show_inbox", showInInbox ? 1 : 0);
        selectedOptions.forEach((option) =>
          formData.append("address_sender[]", option)
        );
        subjects.forEach(
          (subject) =>
            !!subject.value && formData.append("subject[]", subject.value)
        );
        await postConfigLabel(formData);
        setIsOpen(false);
        resetState();
        toastNotification("success", t("toasts.savedSuccessfully"), "topRight");
      } catch (err) {
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
        throw new Error(err?.message ? `error: ${err.message}` : { err });
      } finally {
        setLoadingSubmit(false);
      }
    };
    //
    const modalProps = {
      footer: !loading && (
        <>
          <Divider />
          <div className="flex items-center justify-end">
            <Space>
              <Button disabled={loadingSubmit} onClick={() => setIsOpen(false)}>
                {t("voip.cancel")}
              </Button>
              <Button
                type="primary"
                loading={loadingSubmit}
                onClick={() => form.submit()}
              >
                {t("voip.save")}
              </Button>
            </Space>
          </div>
        </>
      ),
    };
    //
    const resetState = () => {
      setLoading(false);
      setOptions([]);
      setLoadingOptions(false);
      setSelectedOptions([]);
      setSubjects([{ key: Date.now(), value: "" }]);
      setLoadingSubmit(false);
      setShowInInbox(true);
      form.resetFields();
      form.resetFields();
    };
    //
    return (
      <Modal
        title={`Configuration: ${infoLabel?.label}`}
        open={isOpen}
        onChange={(open) => !open && resetState()}
        onCancel={() => {
          if (!loadingSubmit) {
            setIsOpen(false);
            resetState();
          }
        }}
        {...modalProps}
      >
        {loading ? (
          <Skeleton active />
        ) : (
          <Form
            form={form}
            name="basic"
            layout={"vertical"}
            style={{ width: "100%" }}
            colon={false}
            onFinish={submitConfig}
          >
            <Form.Item
              name="sender"
              label={t("mailing.senderAddress")}
              initialValue={selectedOptions}
            >
              <Select
                key="sender"
                allowClear
                showSearch
                mode="multiple"
                notFoundContent=""
                // maxTagCount="responsive"
                style={{
                  width: "100%",
                  marginLeft: 8,
                }}
                options={options}
                onChange={(newValue) => {
                  setOptions([]);
                  setSelectedOptions(newValue);
                }}
                onSearch={debouncedSearch}
                suffixIcon={loadingOptions && <Spin size="small" />}
              />
            </Form.Item>

            <Form.Item label={t("mailing.subjectEmail")} name="subjects">
              <div
                className={`space-y-5 ${
                  subjects.length > 3 ? "max-h-80 overflow-y-auto" : ""
                }`}
              >
                {subjects.map((subject, index) => (
                  <div key={subject.key} className="flex items-end space-x-1">
                    <Input.TextArea
                      allowClear
                      showCount
                      maxLength={100}
                      rows={2}
                      placeholder="Enter a subject..."
                      style={{
                        width: "100%",
                        marginLeft: 8,
                        resize: "none",
                      }}
                      value={subject.value}
                      onChange={(e) =>
                        setSubjects((prevSubjects) =>
                          prevSubjects.map((s) =>
                            s.key === subject.key
                              ? { ...s, value: e.target.value }
                              : s
                          )
                        )
                      }
                    />
                    {index === 0 ? (
                      <Button
                        type="link"
                        size="small"
                        shape="circle"
                        onClick={addSubject}
                        // disabled={!subject.value}
                        icon={<PlusOutlined style={{ fontSize: 16 }} />}
                      />
                    ) : (
                      <Button
                        type="link"
                        size="small"
                        shape="circle"
                        onClick={() => removeSubject(subject.key)}
                        danger
                        icon={<MinusCircleOutlined style={{ fontSize: 16 }} />}
                      />
                    )}
                  </div>
                ))}
              </div>
            </Form.Item>

            <Form.Item name="showInInbox">
              <Checkbox
                checked={showInInbox}
                onChange={({ target: { checked } }) => setShowInInbox(checked)}
                style={{ marginLeft: 8, marginTop: 8 }}
              >
                {t("mailing.showInOriginalBox")}
              </Checkbox>
            </Form.Item>

            {/* <Form.Item
              name="applyTo"
              label="Apply to"
              initialValue="future"
              required
            >
              <Radio.Group
                options={[
                  {
                    label: "Only new emails",
                    value: "future",
                  },
                  {
                    label: "Existing and new emails",
                    value: "existing",
                    disabled: true,
                  },
                  {
                    label: "Only existing emails",
                    value: "past",
                    disabled: true,
                  },
                ]}
                style={{
                  display: "flex",
                  flexDirection: "column",
                  gap: 8,
                  marginLeft: 8,
                }}
              />
            </Form.Item> */}
          </Form>
        )}
      </Modal>
    );
  }
);
//
export const validateEmails = (_, value) => {
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

  if (!value || value.length === 0) {
    return Promise.reject(new Error("At least one email is required"));
  }

  const invalidEmails = value.filter((email) => !emailRegex.test(email));
  if (invalidEmails.length > 0) {
    return Promise.reject(
      new Error(`Invalid email(s): ${invalidEmails.join(", ")}`)
    );
  }

  return Promise.resolve();
};
//
export const labelColors = [
  // Primary Colors
  { label: "Red", value: "#F5222D" },
  { label: "Blue", value: "#1890ff" },
  { label: "Yellow", value: "#FADB14" },

  // Secondary Colors
  { label: "Green", value: "#52C41A" },
  { label: "Orange", value: "#f59e0b" },
  { label: "Purple", value: "#722ED1" },

  // Neutral Colors
  { label: "Black", value: "#000" },
  //  { label: "White", value: "#FFFFFF" },
  { label: "Gray", value: "#9ca3af" },

  // Tertiary & Other Important Colors
  { label: "Cyan", value: "#13C2C2" },
  { label: "Lime", value: "#A0D911" },
  { label: "Teal", value: "#008080" },
  // { label: "Olive", value: "#808000" },
  // { label: "Maroon", value: "#800000" },
  // { label: "Navy", value: "#000080" },
  { label: "Indigo", value: "#4B0082" },
  { label: "Turquoise", value: "#40E0D0" },
  // { label: "Gold", value: "#FFD700" },
  { label: "Gold", value: "#FAAD14" },
  // { label: "Silver", value: "#C0C0C0" },
  // { label: "Brown", value: "#A52A2A" },

  // Light & Soft Colors
  // { label: "Light Gray", value: "#D3D3D3" },
  // { label: "Dark Gray", value: "#A9A9A9" },
  // { label: "Sky Blue", value: "#87CEEB" },
  { label: "Pink", value: "#EB2F96" },
  // { label: "Deep Pink", value: "#FF1493" },
  { label: "Light Blue", value: "#ADD8E6" },
  { label: "Light Green", value: "#90EE90" },
  { label: "Lavender", value: "#E6E6FA" },
  { label: "Beige", value: "#F5F5DC" },
  // { label: "Coral", value: "#FF7F50" },
  { label: "Violet", value: "#EE82EE" },
];
