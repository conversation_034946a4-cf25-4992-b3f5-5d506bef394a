import { useState, useEffect, useRef, useMemo, memo } from "react";
import { useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import {
  Table,
  Input,
  Tooltip,
  DatePicker,
  Switch,
  Button,
  Space,
  Empty,
  Skeleton,
  Divider,
} from "antd";
import { FiSearch } from "react-icons/fi";
import { useTranslation } from "react-i18next";
import {
  changeStateVoiceMsg,
  getCallsLogs,
  getFirstCallDate,
  getKpiCall,
  getTags,
  seenCallsOrVoicesOrGroups,
} from "../services/services";
import { useWindowSize } from "../../clients&users/components/WindowSize";
import { toastNotification } from "../../../components/ToastNotification";
import useActionCall from "../helpers/ActionCall";
import {
  CenterContents,
  disabledDate,
  formatDatePickerRange,
  handleDateTimeRange,
  handleLogsDataNew,
  handlePageSizeOptions,
  humanDate,
  rangePresets,
} from "../helpers/helpersFunc";
import CallerColumn from "../components/CallerColumn";
import AffectationColumn from "../components/affection_column/AffectationColumn";
import DurationColumnCallLog from "../components/DurationColumnCallLog";
import TagColumn from "../components/tag_column/TagColumn";
import dayjs from "dayjs";
import { InfoCircleTwoTone } from "@ant-design/icons";
import debounce from "lodash/debounce";
import FormCreate from "../../clients&users/components/FormCreate";
import DisplayElementInfo from "../components/DisplayElementInfo";
import CreateTask from "../components/CreateTask";
import FilterCallLogs from "../components/FilterCallLogs";
import { useDispatch } from "react-redux";
import { RESET_MISSED_CALL } from "../../../new-redux/constants";
import ExportButton from "../components/ExportButton";
import CardStat from "pages/components/CardStat";
import StatsDrawer from "pages/components/StatsDrawer";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Refs_IDs } from "components/tour/tourConfig";
import "../../clients&users/index.css";

// moment.locale("fr");

const CallLogs = ({ relationId }) => {
  // Hooks
  const navigate = useNavigate();
  const call = useActionCall();
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const location = useLocation();
  const tableRef = useRef(null);
  const windowSize = useWindowSize();
  const queryClient = useQueryClient();
  const componentMounted = useRef(true);

  // Redux state
  const user = useSelector(({ user }) => user?.user);
  const access = user.access || {};
  const poste = `${user?.extension}`;

  const nbrMissedCalls = useSelector(({ voip }) => voip?.nbrMissedCalls);
  const callLogs = useSelector(({ voip }) => voip?.logs);
  // Local state - reduced to only what's necessary
  const [displaySearch, setDisplaySearch] = useState("");
  const [search, setSearch] = useState("");
  const [dateRange, setDateRange] = useState([null, null]);
  const [filterCalls, setFilterCalls] = useState([]);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(20);
  const [showTime, setShowTime] = useState(null);
  const [elementDetails, setElementDetails] = useState({});
  const [familyToAdd, setFamilyToAdd] = useState(null);
  const [nbrPhoneToAdd, setNbrPhoneToAdd] = useState(null);
  const [openDrawerCreate, setOpenDrawerCreate] = useState(false);
  const [openDrawerInfo, setOpenDrawerInfo] = useState(false);
  const [externalSource, setExternalSource] = useState({});
  const [openTask, setOpenTask] = useState(false);
  const [idCallForTask, setIdCallForTask] = useState("");
  const [taskId, setTaskId] = useState(null);

  // Check if there's a filter from location state
  useEffect(() => {
    const paramsFilterCalls = location?.state?.filter;
    if (paramsFilterCalls) {
      setFilterCalls(paramsFilterCalls);
      navigate(location.pathname, { state: {}, replace: true });
    }
  }, [location, navigate]);

  // Reset page when filters change
  useEffect(() => {
    setPage(1);
  }, [search, filterCalls, dateRange]);

  // React Query for call logs
  const {
    data: callLogsData,
    isLoading: callLogsLoading,
    isRefetching,
    isError: callLogsError,
  } = useQuery(
    [["callLogs"], limit, page, search, dateRange, filterCalls, relationId],
    async () => {
      try {
        const {
          data: {
            data,
            meta: { total },
          },
        } = await getCallsLogs(
          limit,
          page,
          search,
          dateRange?.[0],
          dateRange?.[1],
          filterCalls,
          relationId || null
        );

        const result = handleLogsDataNew(data, user, t, "table");
        return { data: result, total, isEmpty: data.length === 0 };
      } catch (err) {
        if (err?.response?.status === 401 || err?.code === "ERR_CANCELED") {
          throw new Error("Unauthorized");
        } else {
          toastNotification("error", t("toasts.somethingWrong"), "topRight");
          throw new Error(err?.message ? err.message : err);
        }
      }
    },
    {
      keepPreviousData: true,
      staleTime: 30000, // 30 seconds
      refetchOnWindowFocus: false,
    }
  );

  useEffect(() => {
    queryClient.refetchQueries({ queryKey: ["callKpi", dateRange] });
    queryClient.refetchQueries({
      queryKey: [
        ["callLogs"],
        limit,
        page,
        search,
        dateRange,
        filterCalls,
        relationId,
      ],
      type: "active",
    });
  }, [callLogs]);
  // React Query for first call date (only fetched once)
  const { data: firstCallDate } = useQuery(
    ["firstCallDate"],
    async () => {
      const {
        data: { first_call_date },
      } = await getFirstCallDate("calls");
      return first_call_date;
    },
    {
      staleTime: 10000,
      refetchOnWindowFocus: false,
    }
  );

  // React Query for tags (only fetched once)
  const { data: allTags } = useQuery(
    ["callTags"],
    async () => {
      const tags = await getTags();
      return tags;
    },
    {
      enabled: !!callLogsData?.data?.length,
      staleTime: 30000,
      refetchOnWindowFocus: false,
    }
  );

  // React Query for KPI data
  const { data: callKpi, isLoading: callKpiLoading } = useQuery(
    ["callKpi", dateRange],
    async () => {
      const [start, end] = dateRange;
      const { data } = await getKpiCall("call", start, end);
      return data;
    },
    {
      refetchOnWindowFocus: false,
      keepPreviousData: true,
    }
  );
  // Handle calls seen when unmount or leave the interface
  useEffect(() => {
    seenCallsOrVoicesOrGroups("missed_call");
    return () => {
      componentMounted.current = false;
      seenCallsOrVoicesOrGroups("missed_call");

      // Clean up any pending debounce
      debouncedSearch.cancel();
    };
  }, []);

  // Reset missed call notification
  useEffect(() => {
    if (page === 1 && nbrMissedCalls) {
      const timer = setTimeout(() => {
        dispatch({ type: RESET_MISSED_CALL });
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [nbrMissedCalls, page, dispatch]);

  // Debounced search
  const debouncedSearch = useMemo(
    () => debounce((nextValue) => setSearch(nextValue), 1000),
    []
  );

  const handleSearch = (event) => {
    const inputValue = event?.target?.value;
    setDisplaySearch(inputValue);
    const cleanInputValue = inputValue.trim();
    if (cleanInputValue.length > 2) debouncedSearch(cleanInputValue);
    else debouncedSearch("");
  };

  const handleShowTotal = (total, range) => (
    <span>{`${range[0]}-${range[1]} of ${total} `}</span>
  );

  const handleSwitchChange = (checked) => {
    if (checked) {
      setShowTime({
        format: "HH:mm",
        defaultValue: [dayjs("00:00", "HH:mm"), dayjs("23:59", "HH:mm")],
      });
    } else {
      setShowTime(null);
    }
  };

  const handleDisplayElementInfo = (name, info) => {
    setElementDetails({
      label: name,
      id: info?.id,
      familyId: info?.familyId,
    });
    setOpenDrawerInfo(true);
  };

  const handleAddItem = (familyId, phoneNbr) => {
    setFamilyToAdd(familyId);
    setNbrPhoneToAdd(phoneNbr);
    setOpenDrawerCreate(true);
  };
  const setDataSource = (updateFunction) => {
    queryClient.setQueryData(
      [["callLogs"], limit, page, search, dateRange, filterCalls, relationId],
      (oldData) => {
        console.log(oldData, updateFunction(oldData.data));

        return {
          ...oldData,
          data: updateFunction(oldData.data),
        };
      }
    );
  };
  const playAudio = async (audio_id, call_id) => {
    if (!componentMounted.current) return;

    try {
      const resp = await changeStateVoiceMsg(poste, audio_id);
      if (resp?.status === 200) {
        // Update data in the query cache instead of managing local state f blaset dataSource
        queryClient.setQueryData(
          [
            ["callLogs"],
            limit,
            page,
            search,
            dateRange,
            filterCalls,
            relationId,
          ],
          (oldData) => {
            if (!oldData) return oldData;

            const newData = {
              ...oldData,
              data: oldData.data.map((log) =>
                log?._id === call_id
                  ? {
                      ...log,
                      voice_mail: { ...log?.voice_mail, state: "Consulté" },
                    }
                  : log
              ),
            };
            return newData;
          }
        );
      }
    } catch (err) {
      if (!componentMounted.current) return;

      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      console.error(err);
    }
  };

  const columns = useMemo(
    () => [
      {
        dataIndex: "icon",
        key: "icon",
        width: "50px",
        fixed: "left",
        render: (icon, { tooltipMsg }, index) => (
          <CenterContents key={index} vertical={"center"} horizontal={"center"}>
            <Tooltip key={index} title={tooltipMsg} placement="topLeft">
              {icon}
            </Tooltip>
          </CenterContents>
        ),
      },
      {
        title: t("table.header.caller"),
        dataIndex: "name",
        key: "name",
        fixed: "left",
        render: (name, record, index) => (
          <CallerColumn
            t={t}
            call={call}
            key={index}
            index={index}
            name={name}
            record={record}
            searchText={search}
            handleDisplayElementInfo={handleDisplayElementInfo}
            handleAddItem={handleAddItem}
            currentUser={user}
            dispatch={dispatch}
            navigate={navigate}
          />
        ),
      },
      {
        title: "Date",
        dataIndex: "humanDate",
        key: "humanDate",
        width: "14%",
        fixed: "left",
        render: (humanDate, record, index) => (
          <CenterContents key={index} vertical={"center"}>
            <span
              key={index}
              className={record.disposition === "missed" ? "text-red-500" : ""}
            >
              {humanDate}
            </span>
          </CenterContents>
        ),
      },
      {
        title: t("table.header.duration"),
        dataIndex: "callDuration",
        key: "callDuration",
        width: "15%",
        render: (
          _,
          { callDuration, audioRecording, _id, voice_mail },
          index
        ) => (
          <CenterContents key={index} vertical={"center"}>
            {callDuration ? (
              <DurationColumnCallLog
                key={index}
                audioRecording={audioRecording}
                duration={callDuration}
                t={t}
              />
            ) : voice_mail ? (
              <DurationColumnCallLog
                key={index}
                audioRecording={voice_mail?.audio}
                state={voice_mail?.state}
                id={voice_mail?.id}
                idCall={_id}
                actionSeen={playAudio}
                source="callLog"
                duration={voice_mail?.duration}
                t={t}
              />
            ) : null}
          </CenterContents>
        ),
      },
      {
        title: "Qualification",
        dataIndex: "tags",
        key: "tags",
        className: "py-0",
        width: "20%",
        render: (tags, { _id, callInfo: { name, number }, tasks }, index) => (
          <TagColumn
            key={index}
            tags={tags}
            tasks={tasks}
            id={_id}
            setDataSource={setDataSource}
            info={{ name: name, number: number }}
            data={allTags}
            setOpenTask={setOpenTask}
            setIdCall={setIdCallForTask}
            setTaskId={setTaskId}
            infoMetaTable={{ page: page, limit: limit }}
          />
        ),
      },
      {
        title: "Affectation",
        dataIndex: "affectation",
        key: "affectation",
        width: "20%",
        render: (affectation, { _id }, index) => (
          <AffectationColumn
            key={index}
            id={_id}
            affectation={affectation}
            t={t}
            setDataSource={setDataSource}
            access={access}
            user={user}
            infoMetaTable={{ page: page, limit: limit }}
            setFamilyToAdd={setFamilyToAdd}
            setOpenDrawerCreate={setOpenDrawerCreate}
            setExternalSource={setExternalSource}
            handleDisplayElementInfo={handleDisplayElementInfo}
          />
        ),
      },
    ],
    [
      t,
      search,
      user,
      call,
      dispatch,
      handleDisplayElementInfo,
      handleAddItem,
      allTags,
      playAudio,
      setDataSource,
      setOpenTask,
      setIdCallForTask,
      setTaskId,
      page,
      limit,
      access,
      setFamilyToAdd,
      setOpenDrawerCreate,
      setExternalSource,
    ]
  );

  // Memoize the data source from the query
  const memoizedDataSource = useMemo(
    () => callLogsData?.data || [],
    [callLogsData?.data]
  );

  const isLoading = callLogsLoading || callKpiLoading;
  const hasData = !!callLogsData?.data?.length;
  const totalLogs = callLogsData?.total || 0;
  //
  if (window.location.pathname !== "/telephony/callLog") return null;

  //

  return (
    <div className="relative w-full space-y-3 pt-0">
      <div className="flex w-full items-end px-2">
        <div className="flex basis-[30%] items-center space-x-2">
          <div id={Refs_IDs.logs_input_search}>
            <Input
              style={{ maxWidth: 300 }}
              disabled={!hasData && !search?.length}
              allowClear
              placeholder={t("voip.log_search")}
              value={displaySearch}
              onChange={handleSearch}
              prefix={<FiSearch className="h-4 w-4 text-slate-400" />}
              suffix={
                <Tooltip title={t("voip.search3chart")}>
                  <InfoCircleTwoTone
                    style={{
                      fontSize: 14,
                      cursor: "help",
                    }}
                  />
                </Tooltip>
              }
            />
          </div>
          <div id={Refs_IDs.logs_filter_icon}>
            <FilterCallLogs
              setFilterState={setFilterCalls}
              filterState={filterCalls}
              t={t}
              disabled={!hasData && !filterCalls.length}
              headerParamsFilterCalls={location?.state?.filter}
            />
          </div>
          {filterCalls?.length ? (
            <Button size="small" type="link" onClick={() => setFilterCalls([])}>
              {t("tags.reset")}
            </Button>
          ) : null}
        </div>

        <div className="flex basis-[40%] items-center justify-center">
          <DisplayKpi
            callKpi={callKpi}
            dateRange={dateRange}
            t={t}
            isLoading={callKpiLoading}
          />
        </div>

        <div className="flex basis-[30%] items-center justify-end space-x-2">
          <div ref={Refs_IDs.logs_range_date_picker}>
            <DatePicker.RangePicker
              disabled={!hasData && !dateRange[0] && !dateRange[1]}
              placement="bottomRight"
              presets={rangePresets(t)}
              onChange={(date, dateString) =>
                handleDateTimeRange(
                  dateString,
                  setDateRange,
                  showTime !== null,
                  user?.location
                )
              }
              format={formatDatePickerRange(showTime, user?.location)}
              showTime={showTime}
              renderExtraFooter={() => (
                <div className="ml-2">
                  <span>{t("voip.displayTime")}: </span>
                  <Switch size="small" onChange={handleSwitchChange} />
                </div>
              )}
              allowClear
              disabledDate={(current) => disabledDate(firstCallDate, current)}
            />
          </div>
          <div ref={Refs_IDs.logs_statistics}>
            <StatsDrawer familyId={"voip"} />
          </div>
          <div ref={Refs_IDs.logs_export_calls_log}>
            <ExportButton
              search={search}
              startDate={dateRange[0]}
              endDate={dateRange[1]}
              filter={filterCalls}
              disabled={!hasData}
            />
          </div>
        </div>
      </div>

      <div className="table-view">
        <Table
          ref={tableRef}
          columns={columns}
          dataSource={memoizedDataSource}
          loading={isLoading || isRefetching}
          size="small"
          pagination={
            totalLogs <= 10
              ? false
              : {
                  showTotal: (total, range) => handleShowTotal(total, range),
                  showSizeChanger: true,
                  showQuickJumper: true,
                  total: totalLogs,
                  pageSize: limit,
                  current: page,
                  onChange: (page) => setPage(page),
                  onShowSizeChange: (current, size) => setLimit(size),
                  pageSizeOptions:
                    totalLogs && handlePageSizeOptions(totalLogs),
                  size: "small",
                }
          }
          locale={{
            emptyText: isLoading ? (
              <div className="flex h-64 flex-row items-center justify-center space-x-2 text-xl">
                {t("contacts.loadingDataTable")}
                <span className="animate-bounce">...</span>
              </div>
            ) : (
              <Empty />
            ),
          }}
          scroll={{
            y: windowSize?.height - 300,
            x: 1200,
          }}
        />
      </div>

      <FormCreate
        open={openDrawerCreate}
        setOpen={setOpenDrawerCreate}
        familyId={familyToAdd}
        nbrPhoneFromVoip={nbrPhoneToAdd}
        externalSource={externalSource}
        setExternalSource={setExternalSource}
      />
      <DisplayElementInfo
        open={openDrawerInfo}
        setOpen={setOpenDrawerInfo}
        elementDetails={elementDetails}
      />
      <CreateTask
        open={openTask}
        setOpen={setOpenTask}
        mask={false}
        idCall={idCallForTask}
        source={"call"}
        taskId={taskId}
        setTaskId={setTaskId}
      />
      {/* <TourWrapper /> */}
    </div>
  );
};

const DisplayKpi = memo(({ callKpi, dateRange, t, isLoading }) => {
  const hasDateRange = dateRange?.[0] && dateRange?.[1];
  const dateLabel = hasDateRange
    ? `${humanDate(dateRange[0], t, "table")} - ${humanDate(
        dateRange[1],
        t,
        "table"
      )}`
    : t("voip.today");

  const cardData = [
    {
      key: "total_today",
      title: t("voip.total_today"),
      value: callKpi?.total_today,
    },
    {
      key: "outgoing_today_call",
      title: t("voip.outgoing_today_call"),
      value: callKpi?.outgoing_today_call,
    },
    {
      key: "received_today_call",
      title: t("voip.received_today_call"),
      value: callKpi?.received_today_call,
    },
    {
      key: "missed_today_call",
      title: t("voip.missed_today_call"),
      value: callKpi?.missed_today_call,
    },
    {
      key: "missed_calls_not_returned_today",
      title: t("voip.missed_calls_not_returned_today"),
      value: callKpi?.missed_calls_not_returned_today,
    },
  ];

  return (
    <div>
      <Divider plain style={{ margin: "0 0 4px 0" }}>
        {dateLabel}
      </Divider>

      <Space wrap size={10}>
        {!isLoading && callKpi
          ? cardData.map((item) => <CardStat key={item.key} item={item} />)
          : Array.from({ length: 5 }).map((_, index) => (
              <Skeleton.Button key={index} active />
            ))}
      </Space>
    </div>
  );
});

export default CallLogs;
