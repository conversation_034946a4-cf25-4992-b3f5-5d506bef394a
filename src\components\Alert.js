import React from "react";
import {
  XCircleIcon,
  CheckCircleIcon,
  XMarkIcon,
} from "@heroicons/react/20/solid";

export const Alert = ({
  type,
  alertMessage,
  setSuccessAlert,
  setFailAlert,
}) => {
  return (
    // <div className="rounded-md bg-red-50 p-4">
    //   <div className="flex">
    //     <div className="flex-shrink-0">
    //       {type === "error" ? (
    //         <XCircleIcon className="h-5 w-5 text-red-400" aria-hidden="true" />
    //       ) : (
    //         <CheckCircleIcon className="h-5 w-5 text-green-400" aria-hidden="true" />
    //       )}
    //     </div>
    //     <div className="ml-3">
    //       <h3 className={`text-sm font-medium text-${type === "error" ? "red" : "green"}-800`}>
    //         {alertMessage}
    //       </h3>
    //       <div className={`mt-2 text-sm text-${type === "error" ? "red" : "green"}-700`}>
    //       </div>
    //     </div>
    //   </div>
    // </div>
    // ***************************************
    <div
      className={`rounded-md bg-${type === "error" ? "red" : "green"}-50 p-4`}
    >
      <div className="flex">
        <div className="flex-shrink-0">
          {/* <CheckCircleIcon className="h-5 w-5 text-green-400" aria-hidden="true" /> */}
          {type === "error" ? (
            <XCircleIcon className="h-5 w-5 text-red-400" aria-hidden="true" />
          ) : (
            <CheckCircleIcon
              className="h-5 w-5 text-green-400"
              aria-hidden="true"
            />
          )}
        </div>
        <div className="ml-3">
          <h3
            className={`text-sm font-medium text-${
              type === "error" ? "red" : "green"
            }-800`}
          >
            {alertMessage}
          </h3>
        </div>
        <div className="ml-auto pl-3">
          <div className="-mx-1.5 -my-1.5">
            <button
              type="button"
              className={`inline-flex rounded-md bg-${
                type === "error" ? "red" : "green"
              }-50 p-1.5 text-${
                type === "error" ? "red" : "green"
              }-500 hover:bg-${
                type === "error" ? "red" : "green"
              }-100 focus:outline-none focus:ring-2 focus:ring-${
                type === "error" ? "red" : "green"
              }-600 focus:ring-offset-2 focus:ring-offset-${
                type === "error" ? "red" : "green"
              }-50`}
            >
              {/* <span className="sr-only">Dismiss</span> */}
              <XMarkIcon
                className="h-5 w-5"
                aria-hidden="true"
                onClick={() =>
                  type === "error"
                    ? setFailAlert(false)
                    : setSuccessAlert(false)
                }
              />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
