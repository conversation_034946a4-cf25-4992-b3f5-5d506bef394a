import { memo } from "react";
import { Badge, Divider, List, Space, Tag } from "antd";
import { HiOutlineEnvelope } from "react-icons/hi2";
import { renderHighlight, renderTitle } from ".";
import { humanDate } from "pages/voip/helpers/helpersFunc";
import { SendOutlined } from "@ant-design/icons";
import DisplayAvatar from "pages/voip/components/DisplayAvatar";

const RenderEmailItem = ({ item, t, handleClickOnItem }) => {
  //
  const { id, label, date, from, to, account_id, folder, seen, highlight } =
    item;

  const firstTo = to?.[0];
  const restTo = to?.length - 1;
  //
  const renderFolder = (folder) => (
    <Tag
      bordered={false}
      style={{ fontSize: 12, paddingLeft: 2, paddingRight: 2 }}
      // icon={
      //   folder === "inbox" ? (
      //     <MailOutlined />
      //   ) : folder === "sent" ? (
      //     <SendOutlined />
      //   ) : folder === "drafts" ? (
      //     <SnippetsOutlined />
      //   ) : folder === "starred" ? (
      //     <StarOutlined />
      //   ) : folder === "important" ? (
      //     <MdLabelImportantOutline />
      //   ) : folder === "archive" ? (
      //     <IoArchiveOutline />
      //   ) : folder === "trash" ? (
      //     <DeleteOutlined />
      //   ) : folder === "spam" ? (
      //     <RiErrorWarningLine />
      //   ) : null
      // }
    >
      {folder}
    </Tag>
  );
  //
  return (
    <List.Item
      className="custom-list-item-global-search"
      key={id}
      style={{
        alignItems: "center",
      }}
      onClick={() =>
        handleClickOnItem(`/mailing/${account_id}/${folder}/${id}`)
      }
    >
      <List.Item.Meta
        avatar={
          <Badge dot={!seen} offset={[-6, 3]}>
            <DisplayAvatar
              size={44}
              icon={<HiOutlineEnvelope style={{ fontSize: 23 }} />}
            />
          </Badge>
        }
        title={renderTitle(label, "Email")}
        description={
          <div className="flex flex-col space-y-1">
            <Space size={1} split={<Divider type="vertical" />}>
              {renderFolder(folder)}
              <p>{humanDate(date, t, "table")}</p>
              {!seen && (
                <Tag
                  bordered={false}
                  color="error"
                  style={{ fontSize: 11, paddingLeft: 2, paddingRight: 2 }}
                >
                  {t("globalSearch.unread")}
                </Tag>
              )}
            </Space>
            <div className="relative flex w-full space-x-0.5">
              {!!from.address && (
                <Tag
                  bordered={false}
                  style={{
                    fontSize: 12,
                    maxWidth: "50%",
                    paddingLeft: 2,
                    paddingRight: 2,
                  }}
                >
                  <p
                    className=" truncate"
                    dangerouslySetInnerHTML={{
                      __html: from.name
                        ? `${from.name} <${from.address}>`
                        : `${from.address}`,
                    }}
                  />
                </Tag>
              )}
              {!!to?.length && <SendOutlined style={{ marginRight: 2 }} />}
              {!!to?.length && (
                <>
                  <Tag
                    bordered={false}
                    style={{
                      fontSize: 12,
                      maxWidth: "40%",
                      paddingLeft: 2,
                      paddingRight: 2,
                    }}
                  >
                    <p
                      className=" truncate"
                      dangerouslySetInnerHTML={{
                        __html: firstTo.name
                          ? `${firstTo.name} <${firstTo.address}>`
                          : `${firstTo.address}`,
                      }}
                    />
                  </Tag>
                  {restTo > 0 && (
                    <Tag
                      bordered={false}
                      style={{ fontSize: 12, paddingLeft: 2, paddingRight: 2 }}
                    >{`+ ${restTo}`}</Tag>
                  )}
                </>
              )}
            </div>
            {renderHighlight(highlight, t)}
          </div>
        }
      />
    </List.Item>
  );
};

export default memo(RenderEmailItem);
