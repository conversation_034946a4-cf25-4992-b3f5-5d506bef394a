import {
  CalendarOutlined,
  CheckCircleOutlined,
  CopyOutlined,
  DeleteOutlined,
  DownloadOutlined,
  EditOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  PushpinFilled,
  PushpinOutlined,
  RetweetOutlined,
  RollbackOutlined,
  SmileOutlined,
  StarFilled,
  StarOutlined,
} from "@ant-design/icons";
import {
  Divider,
  Dropdown,
  Space,
  Tooltip,
  Button,
  message,
  Avatar,
} from "antd";
import React, { useMemo, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import {
  addSavedMessage,
  addStarredMessage,
  removeSavedMessage,
  removeStarredMessage,
} from "new-redux/actions/chat.actions";
import {
  setMsgTask,
  setMsgTaskId,
  setOpenTaskDrawer,
} from "new-redux/actions/tasks.actions/handleTaskDrawer";
import {
  audioMessageTypes,
  convertToPlain,
  forwardMessageTypes,
  handleDownloadFile,
  react_emojis,
  simpleMessageTypes,
} from "pages/layouts/chat/utils/ConversationUtils";
import { isGuestConnected } from "utils/role";
import {
  setDetailsOpenIntegration,
  setOpenModalTicketGlpi,
} from "new-redux/actions/chat.actions/Input";
import { convertHtmlToText } from "pages/voip/helpers/helpersFunc";
import { HeartHandshake } from "lucide-react";
import TicketIconSphere from "components/icons/TicketIconSphere";

const OptionItem = ({
  source,
  handleActionMessage,
  item,
  loading,
  data,
  setActionType,
  toggleAction,
  setTabOpenDropDown,
  tabopenDropDown,
  searchReplies,
  setOpenFormCreate = () => {},
  setInfoFormCreate = () => {},
}) => {
  const [loadingAudio, setLoading] = useState(false);

  const array_reaction = useMemo(
    () =>
      item.reactions?.map((item) => ({
        reaction: item.reaction,
        user_id: item.user_id,
      })) || [],
    [item]
  );
  const { t } = useTranslation("common");
  const currentUser = useSelector((state) => state.chat.currentUser);
  const { user } = useSelector((state) => state.user);
  const openDrawerChat = useSelector((state) => state.voip?.openDrawerChat);
  const selectedConversation = useSelector(
    (state) => state.ChatRealTime.selectedConversation
  );
  const ref = useRef(null);
  const dispatch = useDispatch();
  const [copied, setCopied] = useState(false);
  const integrationsItems =
    Array.isArray(currentUser?.integrations) &&
    currentUser?.integrations.length > 0
      ? [
          {
            key: "integrations",
            disabled: !simpleMessageTypes.includes(item.type),
            show:
              !openDrawerChat &&
              user?.access &&
              !isGuestConnected(currentUser?.role, user?.role)
                ? "1"
                : "0",
            label: t("integrations.integrations"),
            children: currentUser?.integrations
              ?.filter((el) => el.type === 1)
              ?.map((el) => ({
                value: el?.app_name,
                label: (
                  <span className="flex items-center gap-x-1">
                    <span>{el.app_name}</span>
                  </span>
                ),

                icon: <Avatar src={el?.logo} size={16} />,
                disabled:
                  !simpleMessageTypes.includes(item.type) ||
                  item?.integrations?.some(
                    (integration) => integration?.app_id === el._id
                  ),
                show:
                  !openDrawerChat &&
                  user?.access &&
                  user?.access["activities"] === "1" &&
                  !isGuestConnected(currentUser?.role, user?.role)
                    ? "1"
                    : "0",
                onClick: () => {
                  dispatch(setOpenModalTicketGlpi(true));
                  dispatch(setDetailsOpenIntegration(el));

                  dispatch(setMsgTask(item.message));
                  dispatch(setMsgTaskId(item._id));
                  setTabOpenDropDown({ state: false, selected: null });
                },
              })),
          },
        ]
      : [];

  const createElement = [
    {
      key: "create-element",
      label: t("form.create"),
      show:
        !openDrawerChat &&
        !isGuestConnected(currentUser?.role, user?.role) &&
        (user?.access["activities"] === "1" || user?.access["ticket"] === "1")
          ? "1"
          : "0",
      children: [
        {
          key: "task",
          label: t("menu1.task"),
          icon: <CalendarOutlined style={{ fontSize: "100%" }} />,
          disabled: !simpleMessageTypes.includes(item.type),
          show:
            !openDrawerChat &&
            user?.access &&
            user?.access["activities"] === "1" &&
            !isGuestConnected(currentUser?.role, user?.role)
              ? "1"
              : "0",
          onClick: () => {
            dispatch(setOpenTaskDrawer(true));
            dispatch(setMsgTask(item.message));
            setTabOpenDropDown({ state: false, selected: null });
          },
        },
        {
          key: "ticket",
          label: t("contacts.ticket"),
          icon: <TicketIconSphere size={16} />,
          disabled: !simpleMessageTypes.includes(item.type),
          show:
            !openDrawerChat &&
            user?.access &&
            user?.access["ticket"] === "1" &&
            !isGuestConnected(currentUser?.role, user?.role)
              ? "1"
              : "0",
          onClick: () => {
            setInfoFormCreate({
              msgId: item._id,
              msg: convertHtmlToText(item.message),
              familyId: 6,
            });
            setOpenFormCreate(true);
          },
        },
      ],
    },
  ];

  const itemsSubMenu = [
    {
      key: "00",
      label: copied ? t("chat.action.copied") : t("chat.action.copy"),
      icon: copied ? (
        <CheckCircleOutlined style={{ fontSize: "100%" }} />
      ) : (
        <CopyOutlined style={{ fontSize: "100%" }} />
      ),
      onClick: () => {
        const blobHtml = new Blob([item.message], { type: "text/html" });
        const blobText = new Blob([convertToPlain(item.message)], {
          type: "text/plain",
        });
        const data = [
          new ClipboardItem({
            "text/plain": blobText,
            "text/html": blobHtml,
          }),
        ];

        navigator.clipboard.write(data).then(() => {
          setCopied(true);
          message.success(t("chat.action.copied"));
        });
      },
      show:
        simpleMessageTypes.includes(item.type) &&
        item.type !== "message_from_bot"
          ? "1"
          : "0",
    },
    {
      key: "01",
      label: t("chat.action.forward"),
      icon: <RetweetOutlined style={{ fontSize: "100%" }} />,
      onClick: () => {
        toggleAction(true, "forward", item);
        setTabOpenDropDown({ state: false, selected: null });
      },
      show:
        isGuestConnected(currentUser?.role, user?.role) ||
        (process.env.REACT_APP_BRANCH === "prod" &&
          item.type === "message_from_bot")
          ? "0"
          : "1",
    },
    {
      key: "02",
      label:
        item.important === "" ? t("chat.action.pin") : t("chat.action.unpin"),

      icon:
        item.important === "" ? (
          <PushpinOutlined style={{ fontSize: "100%" }} />
        ) : (
          <PushpinFilled style={{ fontSize: "100%" }} />
        ),

      onClick: () => {
        if (item.important === "") {
          handleActionMessage({
            message_id: item._id,
            params: null,
            type_conversation: item.room_id ? "room" : "user",
            type_action: "add_saved",
          });

          dispatch(
            addSavedMessage({
              ...item,
              room_info: selectedConversation?.type === "room" && {
                _id: selectedConversation?.id,
                ...selectedConversation,
              },
              important: currentUser?._id,
            })
          );
          setActionType("add_Saved");
        } else if (item.important === currentUser?._id) {
          handleActionMessage({
            message_id: item._id,
            params: null,
            type_conversation: item.room_id ? "room" : "user",
            type_action: "remove_saved",
          });
          setActionType("remove_saved");
          dispatch(removeSavedMessage({ ...item, important: "" }));
        } else return;
      },

      show:
        item.important !== "" && item.important !== currentUser?._id
          ? "0"
          : "1",
    },

    // {
    //   key: "03",
    //   label: t("chat.action.task"),
    //   icon: <CalendarOutlined style={{ fontSize: "100%" }} />,
    //   disabled: !simpleMessageTypes.includes(item.type),
    //   show:
    //     !openDrawerChat &&
    //     user?.access &&
    //     user?.access["activities"] === "1" &&
    //     !isGuestConnected(currentUser?.role, user?.role)
    //       ? "1"
    //       : "0",
    //   onClick: () => {
    //     dispatch(setOpenTaskDrawer(true));
    //     dispatch(setMsgTask(item.message));

    //     setTabOpenDropDown({ state: false, selected: null });
    //   },
    // },
    {
      type: "divider",
      show:
        Array.isArray(currentUser?.integrations) &&
        currentUser?.integrations.length > 0 &&
        !openDrawerChat &&
        user?.access &&
        !isGuestConnected(currentUser?.role, user?.role)
          ? "1"
          : "0",
    },
    ...createElement,
    ...integrationsItems,
    {
      key: "04",
      label: t("import.download"),
      icon: <DownloadOutlined style={{ fontSize: "100%" }} />,
      disabled: loadingAudio,
      show: audioMessageTypes.includes(item.type) ? "1" : "0",
      onClick: () => {
        handleDownloadFile(null, { path: item.voice }, setLoading);
      },
    },
    {
      type: "divider",
      show:
        item.sender_id === currentUser?._id && item.type !== "message_from_bot"
          ? "1"
          : "0",
    },
    {
      key: "11",
      label: t("chat.delete.deleteMember"),
      icon: <DeleteOutlined style={{ fontSize: "100%" }} />,

      danger: true,
      disabled: loading && data?.action?.includes("delete"),
      onClick: () => {
        toggleAction(true, "delete");
        setTabOpenDropDown({ state: false, selected: null });
      },
      show:
        item.sender_id === currentUser?._id && item.type !== "message_from_bot"
          ? "1"
          : "0",
    },
  ];

  return (
    <div
      className="  absolute -top-5 right-14 z-10 block rounded-full bg-white px-1 py-0.5 shadow-md"
      ref={ref}
      key={"option_" + item?._id}
      id={tabopenDropDown?.selected === item._id ? "list-item-2" : ""}
    >
      <Space size={source === "no_chat" ? 10 : 4}>
        {source !== "no_chat" && (
          <>
            <Tooltip
              title={
                item.favoris?.length > 0 &&
                item.favoris.includes(currentUser?._id)
                  ? t("chat.action.unstar")
                  : t("chat.action.star")
              }
            >
              <Button
                disabled={loading && data?.action?.includes("favorite")}
                type="text"
                size="small"
                shape="circle"
                onClick={() => {
                  handleActionMessage({
                    message_id: item._id,
                    params: null,
                    type_conversation: item.room_id ? "room" : "user",

                    type_action:
                      item.favoris?.length > 0 &&
                      item.favoris.includes(currentUser?._id)
                        ? "remove_favorite"
                        : "add_favorite",
                  });
                  setActionType(
                    item.favoris?.length > 0 &&
                      item.favoris.includes(currentUser?._id)
                      ? "remove_favorite"
                      : "add_favorite"
                  );
                  if (
                    item.favoris?.length > 0 &&
                    item.favoris.includes(currentUser?._id)
                  )
                    dispatch(removeStarredMessage(item));
                  else
                    dispatch(
                      addStarredMessage({
                        ...item,
                        room_info: selectedConversation?.type === "room" && {
                          _id: selectedConversation?.id,
                          ...selectedConversation,
                        },
                      })
                    );
                }}
                className={`
              ${
                item.favoris?.length > 0 &&
                item.favoris.includes(currentUser?._id)
                  ? "text-yellow-500"
                  : "text-slate-500"
              }
            `}
                icon={
                  item.favoris?.length > 0 &&
                  item.favoris.includes(currentUser?._id) ? (
                    <StarFilled />
                  ) : (
                    <StarOutlined />
                  )
                }
              />
            </Tooltip>

            <Tooltip title={t("chat.action.reply")}>
              <Button
                disabled={item.type?.includes("replay") && !item.main_message}
                type="text"
                size="small"
                shape="circle"
                className="text-slate-500"
                icon={<RollbackOutlined />}
                onClick={() => {
                  searchReplies(item);
                }}
              />
            </Tooltip>
          </>
        )}

        <Tooltip title={t("chat.action.react")}>
          <Dropdown
            trigger={["click"]}
            destroyPopupOnHide
            placement="bottomRight"
            arrow={{
              pointAtCenter: true,
            }}
            autoFocus
            autoAdjustOverflow
            menu={{
              items: Array.from(
                { length: react_emojis.length },
                (_, i) => i
              ).map((reaction) => ({
                key: reaction,

                label: (
                  <Tooltip title={react_emojis[reaction]?.name}>
                    <img
                      src={
                        process.env.PUBLIC_URL + react_emojis[reaction]?.image
                      }
                      loading="lazy"
                      alt={react_emojis[reaction]?.name}
                      className={`${
                        array_reaction.find(
                          (item) =>
                            item.reaction === reaction &&
                            item.user_id === currentUser?._id
                        )
                          ? "cursor-not-allowed opacity-50 "
                          : "transform cursor-pointer delay-75 duration-75 hover:scale-125"
                      } h-7 w-7 object-fill `}
                    />
                  </Tooltip>
                ),
                disabled: array_reaction.find(
                  (item) =>
                    item.reaction === reaction &&
                    item.user_id === currentUser?._id
                ),
              })),

              className: "flex items-center  justify-center space-x-0.5",
              selectable: true,
              selectedKeys: array_reaction,

              onClick: (e) => {
                handleActionMessage({
                  message_id: item._id,
                  params: {
                    source,
                    main_message_id: item.main_message?._id,
                    params: Number(e.key),
                  },
                  type_conversation: item.room_id ? "room" : "user",
                  type_action: "add_react",
                });
                setActionType("add_react");
              },
            }}
          >
            <Button
              type="text"
              size="small"
              disabled={loading}
              shape="circle"
              className="text-slate-500"
              icon={<SmileOutlined />}
            />
          </Dropdown>
        </Tooltip>
        {item.sender_id === currentUser?._id &&
          !forwardMessageTypes.includes(item.type) &&
          item.type !== "message_from_bot" &&
          !audioMessageTypes.includes(item.type) && (
            <Tooltip title={t("chat.action.edit")}>
              <Button
                onClick={() => {
                  toggleAction(true, "edit", item);
                  setTabOpenDropDown({ state: false, selected: null });
                }}
                // disabled
                type="text"
                size="small"
                shape="circle"
                icon={<EditOutlined />}
              />
            </Tooltip>
          )}
        {source === "no_chat" && item.sender_id === currentUser?._id ? (
          <Tooltip title={t("chat.delete.deleteMember")}>
            <Button
              disabled={loading && data?.action?.includes("delete")}
              type="text"
              size="small"
              shape="circle"
              danger
              className="text-slate-500"
              icon={<DeleteOutlined style={{ fontSize: "100%" }} />}
              onClick={() => {
                toggleAction(true, "delete");
              }}
            />
          </Tooltip>
        ) : (
          source !== "no_chat" && (
            <>
              <Divider type="vertical" />
              <Dropdown
                trigger={["click"]}
                getPopupContainer={(triggerNode) => triggerNode.parentNode}
                autoFocus
                destroyPopupOnHide
                open={tabopenDropDown?.state}
                onOpenChange={(e) => {
                  setTabOpenDropDown({
                    state: e,
                    selected: e ? item._id : null,
                  });
                  // setOpen(e);
                }}
                placement="bottomRight"
                arrow={{
                  pointAtCenter: true,
                }}
                autoAdjustOverflow
                menu={{
                  items: itemsSubMenu.filter((item) => item.show === "1"),

                  className: "flex flex-col justify-start w-full",
                }}
              >
                <Button
                  type="text"
                  size="small"
                  shape="circle"
                  className="text-slate-500"
                  icon={<EllipsisOutlined />}
                />
              </Dropdown>
            </>
          )
        )}
      </Space>
    </div>
  );
};

export default OptionItem;
