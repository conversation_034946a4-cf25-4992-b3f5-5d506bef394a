import React, { useState } from "react";

import Content from "./Content";
import SelectProduct from "./Select";
import Sidebar from "./Sidebar";
import { useWindowSize } from "../clients&users/components/WindowSize";
import MainService from "../../services/main.service";
import { toastNotification } from "../../components/ToastNotification";
import { useTranslation } from "react-i18next";
import { Spin } from "antd";

const Wrapper = ({
  selectedGroupWiki,
  setSelectedGroupWiki,
  selectedGroup,
  setSelectedGroup,
  selectedFolder,
  setSelectedFolder,
  isVisited,
  setIsVisited,
  folders,
  setFolders,
  selectedNode,
  setSelectedNode,
  expandedKeys,
  setExpandedKeys,
  nodeName,
  setNodeName,
  loading,
  setLoading,
  binders,
  setBinders,
  disabled,
  setDisabled,
  loadGroupsWithClasseur,
}) => {
  const [t] = useTranslation("common");
  const windowSize = useWindowSize();
  const [selectedKeys, setSelectedKeys] = useState([selectedNode?.key]);

  const sideBarSize = windowSize?.height - 200;
  //const [selected, setSelected] = useState(null)

  const [value, setValue] = useState("");
  // const [selectedFolder, setSelectedFolder] = useState("");

  const delete1 = (pageKey, folderKey) => {
    setLoading(true);
    MainService.deletePage(pageKey)
      .then((res) => {
        const newTreeData2 = [...folders];
        newTreeData2.map((e) => {
          if (e.key == folderKey) {
            return (e.children = e.children.filter((el) => el.id !== pageKey));
          }
        });
        setFolders(newTreeData2);
        if (selectedNode.id === pageKey) setSelectedNode("");
        setLoading(false);
      })
      .catch((err) => {
        setLoading(false);
        console.log(err);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      });
  };
  return (
    <div className="mx-4">
      {loadGroupsWithClasseur ? (
        <div className="pl-10">
          <Spin />
        </div>
      ) : (
        <div className="flex overflow-hidden">
          <div>
            <div style={{ width: 300, marginBottom: 20 }}>
              <SelectProduct
                setSelectedGroup={setSelectedGroup}
                selectedGroup={selectedGroup}
                setSelectedNode={setSelectedNode}
                selectedNode={selectedNode}
                expandedKeys={expandedKeys}
                setExpandedKeys={setExpandedKeys}
                setSelectedGroupWiki={setSelectedGroupWiki}
                value={value}
                setValue={setValue}
                isVisited={isVisited}
                setIsVisited={setIsVisited}
                binders={binders}
                setBinders={setBinders}
                setSelectedKeys={setSelectedKeys}
                loadGroupsWithClasseur={loadGroupsWithClasseur}
                selectedGroupWiki={selectedGroupWiki}
              />
            </div>
            <div
              style={{ height: sideBarSize, width: 310 }}
              className="overflow-auto"
            >
              <Sidebar
                setSelectedGroup={setSelectedGroup}
                selectedGroup={selectedGroup}
                setSelectedNode={setSelectedNode}
                selectedNode={selectedNode}
                expandedKeys={expandedKeys}
                setExpandedKeys={setExpandedKeys}
                value={value}
                folders={folders}
                setFolders={setFolders}
                nodeName={nodeName}
                setNodeName={setNodeName}
                selectedFolder={selectedFolder}
                setSelectedFolder={setSelectedFolder}
                loading={loading}
                setLoading={setLoading}
                delete1={delete1}
                isVisited={isVisited}
                setIsVisited={setIsVisited}
                selectedKeys={selectedKeys}
                setSelectedKeys={setSelectedKeys}
                binders={binders}
                setDisabled={setDisabled}
              />
            </div>
          </div>
          <div
            style={{ height: "calc(100vh - 130px)", marginTop: 0 }}
            className="flex-1 overflow-y-auto"
          >
            {selectedNode && (
              <Content
                selectedNode={selectedNode}
                folders={folders}
                setFolders={setFolders}
                nameGroup={value}
                nodeName={nodeName}
                delete1={delete1}
                setSelectedNode={setSelectedNode}
                disabled={disabled}
                setDisabled={setDisabled}
              />
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default Wrapper;
