import { DollarOutlined } from "@ant-design/icons";
import { Badge, Divider, List, Rate, Space, Tag } from "antd";
import {
  generateUrlToView360,
  humanDate,
} from "pages/voip/helpers/helpersFunc";
import { AiOutlineShoppingCart } from "react-icons/ai";
import { LuPalmtree } from "react-icons/lu";
import { removeTagsFromText, renderHighlight, renderTitle } from ".";
import { getFamilyNameById } from "pages/clients&users/FamilyRouting";
import { memo } from "react";
import { Blocks, HeartHandshake } from "lucide-react";
import TicketIconSphere from "components/icons/TicketIconSphere";
import DisplayAvatar from "pages/voip/components/DisplayAvatar";
import { colors } from "components/Colors";

const renderPipAndStage = (pipeline, stage) => (
  <Space size={1} split={<span className="font-semibold">--</span>}>
    {!!pipeline && (
      <Tag
        bordered={false}
        style={{ fontSize: 12, paddingLeft: 2, paddingRight: 2 }}
      >
        {pipeline}
      </Tag>
    )}
    {!!stage?.label && (
      <Tag
        bordered={false}
        color={
          colors.find((c) => c.value === stage?.color)?.tagColor ?? stage?.color
        }
        style={{ fontSize: 12, paddingLeft: 2, paddingRight: 2 }}
      >
        {stage?.label}
      </Tag>
    )}
  </Space>
);

const RenderFamilyItem = ({
  item,
  t,
  imgBaseUrl,
  handleClickOnItem,
  onlineUser,
}) => {
  //

  //
  const { id, family_id, label, avatar, created_at, owner, stage, highlight } =
    item;
  //
  const uuid = item?.uid;
  const role = item?.role;
  const department = item?.department;
  const type = item?.type;
  const source = item?.source;
  const pipeline = item?.pipeline;
  const amount = item?.amount?.length
    ? `(${item.amount[0]}) ${Number(item.amount[1])
        .toFixed(item.amount[0] === "TND" ? 3 : 2)
        .replace(/\d(?=(\d{3})+\.)/g, "$&,")}`
    : null;
  const score = item?.score;
  const isOverdue = item?.is_overdue;
  //
  const familyName = getFamilyNameById(t, family_id);
  //
  const urlImg =
    [1, 2, 4, 9].includes(family_id) && avatar
      ? `${imgBaseUrl}${avatar}`
      : null;

  const icon =
    family_id === 3 ? (
      <HeartHandshake size={22} />
    ) : family_id === 5 ? (
      <AiOutlineShoppingCart style={{ fontSize: 21 }} />
    ) : family_id === 6 ? (
      <TicketIconSphere size={24} />
    ) : family_id === 7 ? (
      <Blocks size={22} />
    ) : family_id === 8 ? (
      <LuPalmtree style={{ fontSize: 21 }} />
    ) : family_id === 11 ? (
      <DollarOutlined style={{ fontSize: 21 }} />
    ) : null;
  //
  return (
    <List.Item
      className="custom-list-item-global-search"
      key={id}
      style={{
        alignItems: "center",
      }}
      onClick={() =>
        handleClickOnItem(generateUrlToView360(family_id, id, "v2"))
      }
    >
      <List.Item.Meta
        avatar={
          <Badge
            offset={[-4, 38]}
            color={
              onlineUser?.[uuid] === "online"
                ? "green"
                : onlineUser?.[uuid] === "busy"
                ? "red"
                : onlineUser?.[uuid] === "away"
                ? "orange"
                : "#a6a6a6"
            }
            dot={family_id === 4}
          >
            <DisplayAvatar
              name={removeTagsFromText(label)}
              urlImg={urlImg}
              size={44}
              icon={icon}
            />
          </Badge>
        }
        title={renderTitle(label, familyName)}
        description={
          <div className="flex flex-col space-y-0.5">
            <Space size={2} split={<Divider type="vertical" />}>
              {!!department && (
                <Tag
                  bordered={false}
                  style={{ fontSize: 12, paddingLeft: 2, paddingRight: 2 }}
                >
                  {department}
                </Tag>
              )}
              {!!role && (
                <Tag
                  bordered={false}
                  style={{ fontSize: 12, paddingLeft: 2, paddingRight: 2 }}
                >
                  {role}
                </Tag>
              )}
              {!!type && (
                <Tag
                  bordered={false}
                  style={{ fontSize: 12, paddingLeft: 2, paddingRight: 2 }}
                >
                  {type}
                </Tag>
              )}
              {/* {!!pipeline && (
                <Tag
                  bordered={false}
                  style={{ fontSize: 12, paddingLeft: 2, paddingRight: 2 }}
                >
                  {pipeline}
                </Tag>
              )} */}
              {renderPipAndStage(pipeline, stage)}
              {!!source && (
                <Tag
                  bordered={false}
                  style={{ fontSize: 12, paddingLeft: 2, paddingRight: 2 }}
                >
                  {source}
                </Tag>
              )}
              {!!amount && (
                <Tag
                  bordered={false}
                  style={{ fontSize: 12, paddingLeft: 2, paddingRight: 2 }}
                >
                  {amount}
                </Tag>
              )}
              {!!score && (
                <Rate
                  style={{ fontSize: 14 }}
                  allowHalf
                  disabled
                  defaultValue={score}
                />
              )}
            </Space>

            <Space size={2} split={<Divider type="vertical" />}>
              <Space size={3}>
                <p>
                  {family_id === 4
                    ? t("globalSearch.createdBy")
                    : t("globalSearch.owner")}
                </p>
                <DisplayAvatar
                  cursor="help"
                  tooltip={true}
                  size={20}
                  name={owner?.label}
                  urlImg={!!owner?.avatar && `${imgBaseUrl}${owner?.avatar}`}
                />
              </Space>
              <Space size={3}>
                <p>{t("globalSearch.created")}:</p>
                <p>{humanDate(created_at, t, "table")}</p>
              </Space>
              {isOverdue && (
                <Tag
                  bordered={false}
                  color="error"
                  style={{ fontSize: 12, paddingLeft: 2, paddingRight: 2 }}
                >
                  {t("globalSearch.overdue")}
                </Tag>
              )}
            </Space>
            {renderHighlight(highlight)}
          </div>
        }
      />
    </List.Item>
  );
};

export default memo(RenderFamilyItem);
