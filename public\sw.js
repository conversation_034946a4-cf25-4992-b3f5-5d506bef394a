// web-worker.js

const CACHE_NAME = "version-1";

// eslint-disable-next-line no-restricted-globals
self.addEventListener("install", (event) => {});
// call active event
// eslint-disable-next-line no-restricted-globals
self.addEventListener("activate", async (event) => {
  // const urlBase64ToUint8Array = (base64String) => {
  //   const padding = "=".repeat((4 - (base64String.length % 4)) % 4);
  //   // eslint-disable-next-line no-useless-escape
  //   const base64 = (base64String + padding)
  //     .replace(/\-/g, "+")
  //     .replace(/_/g, "/");

  //   const rawData = atob(base64);
  //   const outputArray = new Uint8Array(rawData.length);

  //   // eslint-disable-next-line no-plusplus
  //   for (let i = 0; i < rawData.length; ++i) {
  //     outputArray[i] = rawData.charCodeAt(i);
  //   }
  //   console.log(outputArray);
  //   return outputArray;
  // };

  // eslint-disable-next-line no-restricted-globals
  // const subcription = await self.registration.pushManager.subscribe({
  //   userVisibleOnly: true,
  //   applicationServerKey: urlBase64ToUint8Array(
  //     "BKhoGpSOdaqJMdvS33-97YIUxBBfsHYLfrGQHSNqJr6DxQW_5JG0nRQn9RAa4E-uLM0sCVBoGQ10HpsUWXrEf8o"
  //   ),
  // });

  // const a = {
  //   publicKey:
  //     "BKhoGpSOdaqJMdvS33-97YIUxBBfsHYLfrGQHSNqJr6DxQW_5JG0nRQn9RAa4E-uLM0sCVBoGQ10HpsUWXrEf8o",
  //   privateKey: "-1JNcImqDe9ghFPxJLTGd6jMCgLPJJF7DBpBEynE0c4",
  // };
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        // eslint-disable-next-line array-callback-return
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            // eslint-disable-next-line no-restricted-globals
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
});

// eslint-disable-next-line no-restricted-globals
self.addEventListener("fetch", (event) => {
  const requestUrl = new URL(event.request.url);
  const excludedURLs = [
    "/comunik_ipbx/Api/WSsphere",
    "comunikcrm.info:4543/api/",
    "/api/",
    "/.well-known",
  ];
  const isExcluded = excludedURLs.some((excludedPath) =>
    requestUrl.pathname.includes(excludedPath)
  );

  if (!isExcluded) {
    event.respondWith(
      fetch(event.request)
        .then((response) => {
          // clone response stream
          const responseClone = response.clone();
          caches.open(CACHE_NAME).then((cache) => {
            cache.put(event.request, responseClone).catch((e) => {
              return;
              // throw new Error(` ${e.message}`);
            });
          });
          return response;
        })
        .catch(() => caches.match(event.request).then((response) => response))
    );
  }
});
