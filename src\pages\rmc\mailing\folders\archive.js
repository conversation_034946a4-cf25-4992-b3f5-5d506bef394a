import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Badge } from "antd";

import { useCallback, useEffect, useMemo, useState } from "react";

import { DeleteOutlined } from "@ant-design/icons";

import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import MarkDown from "../Markdown";
import MainService from "../../../../services/main.service";
import FormCreate from "../../../clients&users/components/FormCreate";
import CreateTask from "../../../voip/components/CreateTask";
import useDebounce from "../../../components/UseDebounce/UseDebounce";
import dayjs from "dayjs";
import {
  ReadUnreadMessages,
  SeenMessage,
  TrashListMessages,
} from "../services/ActionsApi";

import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import {
  setNumberEmailThread,
  setPage,
  setPageSize,
} from "new-redux/actions/mail.actions";
import ActionsStarredImportant from "../actionsStarredImportant";
import { formatDateForDisplay } from "pages/tasks/helpers/formatDateToDisplay";
import DropdownActionsTable from "../dropdownActionsTable";
import Log from "../components/Log";
import ModalDeleteEmail from "../components/ModalDeleteEmail";
import "../mailing.css";
import { checkAccessRoleAccount } from "../mailing";

const Archive = ({ dataAccounts, setDetailsMail, refresh }) => {
  const [openForm, setOpenForm] = useState(false);
  const [familyId, setFamilyId] = useState(null);
  const [openTask, setOpenTask] = useState(false);

  const [dataMailArchive, setDataMailArchive] = useState([]);
  const [metaMailArchive, setMetaMailArchive] = useState({});
  const [loading, setLoading] = useState({ state: false, type: null });
  const [openModal, setOpenModal] = useState(false);
  const [EmailId, setEmailId] = useState("");
  const [clickArchive, setClickArchive] = useState(false);
  const [typeDelete, setTypeDelete] = useState("");
  const [error, setError] = useState(false);
  const [thirdid, setThirdId] = useState("");
  const [openLogDrawer, setOpenLogDrawer] = useState(null);
  const { page, pageSize, searchEmail } = useSelector(
    (state) => state.mailReducer
  );
  const [t] = useTranslation("common");
  const navigate = useNavigate();
  const usedAccount = useMemo(
    () => dataAccounts?.find((item) => item?.selected),
    [dataAccounts]
  );
  const debouncedSearchValue = useDebounce(searchEmail, 500);
  const dispatch = useDispatch();
  const isToday = require("dayjs/plugin/isToday");
  const { user } = useSelector(({ user }) => user);
  dayjs.extend(isToday);

  const getMailsArchive = useCallback(async () => {
    // if (Object.values(metaMailImportant).length > 0 && !refresh) return;
    setLoading({ state: true, type: "mails" });

    let response = "";
    try {
      if (debouncedSearchValue.length > 0) {
        response = await MainService.searchMailsArchive(
          usedAccount?.value,
          debouncedSearchValue,
          page,
          pageSize
        );
      } else {
        response = await MainService.getArchiveEmails(
          usedAccount?.value,
          page,
          pageSize
        );
      }
      if (response?.status === 200) {
        checkAccessRoleAccount(response, navigate, t);

        setDataMailArchive(response.data.data);
        setMetaMailArchive(response.data.meta);
        setError(false);
      }
    } catch (err) {
      setError(true);
      console.log(err);
    } finally {
      setLoading(false);
    }
  }, [
    usedAccount?.value,
    debouncedSearchValue,
    page,
    metaMailArchive?.currentPage,
    refresh,
    pageSize,
  ]);

  const dataSourceArchive = dataMailArchive.map((item, i) => ({
    key: item.id,
    from: { name: item.from.name, nbr: item?.nbr, address: item.from.address },
    // from: item,
    // to: item.to,
    subject: item.subject,
    body: item.body,
    date: item.date,
    seen: item.seen,
    starred: item.starred,
    third_id: item.third_id,
    important: item.important,
    exist: item.exist,
    nbr: item.nbr,
  }));

  const columns = [
    {
      dataIndex: "starred",
      width: "90px",
      fixed: "left",
      render: (_, record) => (
        <>
          <ActionsStarredImportant
            record={record}
            getMails={getMailsArchive}
            usedAccount={usedAccount}
          />
        </>
      ),
    },
    {
      title: t("mailing.Inbox.To"),
      dataIndex: "from",
      // width: "130px",
      fixed: "left",
      render: (text, record) => {
        return (
          <div
            className={`relative flex cursor-pointer items-center ${
              record.seen === 0 ? " font-bold" : ""
            } `}
          >
            <Tooltip
              placement="topLeft"
              title={text?.name?.length > 0 ? text.name : text.address}
            >
              <p
                className="  ml-1 max-w-[80%] cursor-pointer truncate"
                // style={{ width: "80%" }}
              >
                {text?.name?.length > 0 ? text.name : text.address}
              </p>
            </Tooltip>

            <div className="action-mail">
              <Badge
                style={{
                  backgroundColor: "transparent",
                  border: "none",
                  outline: "none",
                  margin: 0,
                  color: "gray",
                  fontSize: 12,
                  fontWeight: record?.seen === 0 ? "bold" : "normal",
                }}
                count={text?.nbr}
              ></Badge>
              <DropdownActionsTable
                record={record}
                setThirdId={setThirdId}
                setOpenLogDrawer={setOpenLogDrawer}
                t={t}
                conditionActions={false}
                usedAccount={usedAccount}
                dataMailOutbox={dataMailArchive}
                setDataMailOutbox={setDataMailArchive}
                user={user}
                // access={access}
                // dataTags={dataTags}
                setOpenTask={setOpenTask}
                setEmailId={setEmailId}
                setOpenModal={setOpenModal}
                setTypeDelete={setTypeDelete}
                getMailsInbox={getMailsArchive}
                clickArchive={clickArchive}
                setClickArchive={setClickArchive}
                type="archive"
              />
            </div>
          </div>
        );
      },
    },
    {
      title: t("mailing.Inbox.subject"),
      dataIndex: "subject",
      // width: "150px",
      render: (text, record) => {
        return (
          <div style={{ cursor: "pointer" }}>
            <span
              style={{
                fontWeight: record.seen === 0 ? "bold" : "",
                cursor: "pointer",
              }}
            >
              {/* {text?.length > 30 ? (
                <p>{text?.toString()?.substring(0, 30)}...</p>
              ) : (
                text
              )} */}
              {text?.length > 30 ? (
                // <MarkDown>{text.toString()?.substring(0, 30)}...</MarkDown>
                <span
                  dangerouslySetInnerHTML={{
                    __html: text.toString()?.substring(0, 30) + "...",
                  }}
                />
              ) : (
                // <MarkDown>{text}</MarkDown>
                <span dangerouslySetInnerHTML={{ __html: text }} />
              )}
            </span>
          </div>
        );
      },
    },
    {
      title: t("mailing.Inbox.message"),
      dataIndex: "body",
      // width: "300px",
      ellipsis: true,
      render: (text, record) => {
        return (
          <div style={{ cursor: "pointer" }}>
            {/* <MarkDown>{text}</MarkDown> */}
            <span dangerouslySetInnerHTML={{ __html: text }} />
          </div>
        );
      },
    },
    {
      title: t("mailing.Inbox.date"),
      dataIndex: "date",
      // width: "120px",
      render: (dateTime, record) => (
        <span
          style={{
            fontWeight: record.seen === 0 ? "bold" : "",
            cursor: "pointer",
          }}
        >
          {formatDateForDisplay(
            record.date,
            `${user?.location?.date_format} ${user?.location?.time_format}`,
            user,
            t
          )}
        </span>
      ),
    },
  ];

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  const onSelectChange = (newSelectedRowKeys) => {
    //console.log("selectedRowKeys changed: ", newSelectedRowKeys);
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const toggleSelection = (record) => {
    const key = record.key;
    const newSelectedRowKeys = selectedRowKeys.includes(key)
      ? selectedRowKeys.filter((k) => k !== key)
      : [...selectedRowKeys, key];
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
    renderCell: (checked, record, index, originNode) => (
      <div className="relative">
        <Button
          style={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
          }}
          size="large"
          type="text"
          shape="circle"
          onClick={(e) => {
            e.stopPropagation();
            toggleSelection(record);
          }}
        >
          {originNode}
        </Button>
      </div>
    ),
  };
  const hasSelected = selectedRowKeys.length > 0;

  const handleMarkAsReadUnread = async (status, selectedRowKeys) => {
    const response = await ReadUnreadMessages({
      usedAccount,
      status,
      selectedRowKeys,
      setSelectedRowKeys,
      t,
      dispatch,
    });

    if (response && debouncedSearchValue?.length === 0) getMailsArchive();
  };
  const DeleteMail = async () => {
    setLoading({ state: true, type: "delete" });
    if (typeDelete === "multiple") {
      const response = await TrashListMessages({
        usedAccount,
        id: EmailId,
        selectedRowKeys,
        setSelectedRowKeys,
        setOpenModal,
        folder: "archive",
        typeDelete,
        setLoading,
        t,
      });
      if (response) getMailsArchive();
    }
  };

  useEffect(() => {
    if (dataAccounts?.length > 0) {
      getMailsArchive();
    }
  }, [getMailsArchive]);

  return (
    <>
      {hasSelected ? (
        <div className="mb-[8px] ml-[20px] flex items-center space-x-3">
          <Button
            type="primary"
            danger
            icon={<DeleteOutlined />}
            onClick={() => {
              setOpenModal(true);
              setTypeDelete("multiple");
            }}
          >
            {t("mailing.DeleteButton")} ({selectedRowKeys.length}{" "}
            {selectedRowKeys.length > 1 ? "emails" : "email"})
          </Button>
          {usedAccount?.shared == 0 ? (
            <>
              <Button
                type="default"
                onClick={() => handleMarkAsReadUnread(1, selectedRowKeys)}
              >
                {t("mailing.markRead")} ({selectedRowKeys.length})
              </Button>

              <Button
                type="default"
                onClick={() => handleMarkAsReadUnread(0, selectedRowKeys)}
              >
                {t("mailing.markUnread")} ({selectedRowKeys.length})
              </Button>
            </>
          ) : null}
        </div>
      ) : null}

      <FormCreate open={openForm} setOpen={setOpenForm} familyId={familyId} />

      <CreateTask
        open={openTask}
        setOpen={setOpenTask}
        mask={false}
        source="mailing"
      />

      <ModalDeleteEmail
        t={t}
        openModal={openModal}
        setOpenModal={setOpenModal}
        loading={loading}
        DeleteMail={DeleteMail}
        typeDelete={typeDelete}
        selectedRowKeysLength={selectedRowKeys.length}
      />

      {openLogDrawer !== null ? (
        <Log
          openLogDrawer={openLogDrawer}
          setOpenLogDrawer={setOpenLogDrawer}
          setLoading={setLoading}
          thirdid={thirdid}
        />
      ) : null}

      <Table
        className="mailing-custom-row"
        loading={loading.state && loading.type === "mails"}
        rowSelection={rowSelection}
        columns={columns}
        dataSource={dataSourceArchive}
        showSizeChanger={false}
        pagination={{
          current: page,
          pageSize: pageSize,
          pageSizeOptions: ["10", "20", "30"],
          total: metaMailArchive.total === 0 ? 1 : metaMailArchive.total,
          showSizeChanger: true,
          onChange: (page, pageSize) => {
            dispatch(setPage(page));
            dispatch(setPageSize(pageSize));
          },
          showTotal: (total, range) =>
            `${range[0]}-${range[1]} of ${total} items`,
        }}
        onRow={(record) => {
          return {
            onClick: () => {
              setDetailsMail([]);
              dispatch(setNumberEmailThread(record.nbr));
              navigate(`/mailing/${usedAccount?.value}/archive/${record.key}`);
            },
          };
        }}
        scroll={{ y: "calc(100vh - 250px)" }}
        locale={{
          emptyText: (
            <p className={error ? "mt-4 text-red-600" : "mt-4 text-[#898282]"}>
              {error
                ? t("toasts.errorFetchApi")
                : loading.state && loading.type === "mails"
                ? "Loading ..."
                : t("mailing.noData")}
            </p>
          ),
        }}
        rowClassName={(_, index) =>
          `${index === 5 ? "" : "clickable-row"} group`
        }
        size="small"
      />
    </>
  );
};

export default Archive;
