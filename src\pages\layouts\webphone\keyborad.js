import { <PERSON><PERSON>, Popover, Col, <PERSON> } from "antd";

import { TbBackslash } from "react-icons/tb";
import { IoKeypadSharp } from "react-icons/io5";
const style = {
  background: "#F1F5F9",
  padding: "0",
};

export const KeyPad = ({ setNumber, setDisplayValue, setSearch }) => {
  const keyPad = [
    { number: "1", key: "" },
    { number: "2", key: "ABC" },
    { number: "3", key: "DEF" },
    { number: "4", key: "GHI" },
    { number: "5", key: "JKL" },
    { number: "6", key: "MNO" },
    { number: "7", key: "PQRS" },
    { number: "8", key: "TUV" },
    { number: "9", key: "WXYZ" },
    { number: "*", key: "" },
    { number: "0", key: "+" },
    { number: "#", key: "" },
  ];

  return (
    <div
      className="w-[21rem] rounded-b-md  px-6 pb-1.5 pt-2"
      style={{ backgroundColor: "rgb(241, 245, 249)" }}
    >
      <Row gutter={[6, 2]}>
        {keyPad?.map(({ number, key }) => (
          <Col key={number} className="gutter-row" span={8}>
            <div style={style} key={number}>
              <Button
                key={number}
                size="large"
                block="true"
                className={"flex items-center justify-center"}
                onClick={() => {
                  setDisplayValue((prevNum) => prevNum + number);
                  setNumber((prevNum) => prevNum + number);
                  setSearch((prev) => prev + number);
                }}
              >
                <div className="grid items-center">
                  <span
                    style={{
                      marginTop: key && "5px",
                      fontSize: number === "*" && 16,
                    }}
                    className=" font-semibold leading-4"
                  >
                    {number}
                  </span>
                  <span
                    style={{ fontSize: 10 }}
                    className="leading-4 	 text-slate-400 "
                  >
                    {key}
                  </span>
                </div>
              </Button>
            </div>
          </Col>
        ))}
      </Row>
    </div>
  );
};
const Keyboard = ({ setOpen, open, isDisabled, autoCompleteRef }) => (
  <div className="z-50">
    <Button
      // type="text
      disabled={isDisabled}
      size="small"
      style={{ height: "32px", width: "29px" }}
      onClick={() => {
        setOpen(!open);
        autoCompleteRef.current.focus();
      }}
      icon={
        <>
          <IoKeypadSharp
            style={{
              fontSize: "17px",
              marginTop: "4px",
              color: "rgb(148 163 184)",
            }}
          />
          {open ? (
            <TbBackslash
              style={{
                fontSize: "24px",
                color: "rgb(100 116 139)",
                position: "absolute",
                left: 1,
              }}
            />
          ) : (
            ""
          )}
        </>
      }
    />
    {/* </Popover> */}
  </div>
);
export default Keyboard;
