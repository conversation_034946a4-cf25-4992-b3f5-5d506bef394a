

.callButton{
  animation: playBtn 1.6s ease infinite;
}

.userImage {
  height: 90px;
  width: 90px;
  border-radius: 100%;
  background-color: #eeeaea;
  margin-bottom: 10px;
  margin: auto;
}
.userImageIncoming {
  height: 90px;
  width: 90px;
  border-radius: 100%;
  background-color: #eeeaea;
  margin-bottom: 10px;
  margin: auto;
  animation: play 1.6s ease infinite;
}
.callPage {
  background-color: #111827;
  height: 100%;
  border-radius:1rem ;
}

#callButtons {
  display: flex;
  flex-direction: column;

  justify-content: center;
  margin: auto;
  border: 0;
  z-index: 0;
  height: 100%;
  width: 100%;
}

.callLine {
  margin: 10px auto;
}
.callLineIncoming {
  margin: -99px auto 212px auto;
}
.callButtonsVideo {
  justify-content: space-around;
  margin: auto;

  z-index: 0;

  background-color: #60606057;
  position: absolute;
  bottom: 0;
  width: 100%;
  border-radius: 21px 21px 0 0;
}

.digitCall {
  height: 55px;
  width: 55px;
  margin: 0 auto 0 !important;
  color: white;
  /* font-weight: 300; */

  display: flex;
  cursor: pointer;
  float: left;
  outline: none;
  border-radius: 8px;
  border: 0;
  font-weight: bold;
  background-color: rgb(255 255 255 / 0%);
}
.digitCallVideo {
  margin: 0 auto 0 !important;
  color: white;
  /* font-weight: 300; */

  display: flex;
  cursor: pointer;
  float: left;
  outline: none;
  border-radius: 100%;
  border: 0;
  font-weight: bold;
  background-color: rgb(255 255 255 / 25%);
  width:50px;
  height:50px;
}

.hungup {
  color: white;
  background: red;
  border-radius: 100%;
  height: 60px;
  width: 60px;
  border: 0;
  color: white;
  cursor: pointer;
}

.hungupVideo {
  color: white;
  background: linear-gradient(to top, #b00606, #f73016);
  border-radius: 100%;
  height: 50px;
  width: 50px;
  color: white;
  cursor: pointer;
  margin-left: 14px;
}

.upBtn,
.upBtnVideo {
  color: white;
  background: #22c55e;
  border-radius: 100%;
  height: 60px;
  width: 60px;
  border: 0;
  color: white;
  cursor: pointer;
  font-size: large;
  font-weight: 500;
}

.controlBtn {
  display: flex;
  justify-content: space-around;
}
.callNumber {
  color: white;
  font-size: larger;
  font-weight: 500;
  -webkit-animation: moving 5s infinite;
  animation: moving 7s infinite;
  text-align: center;
  width: 100%;
  margin: 7px 0 0 0;
}

#watch {
  text-align: center;
}

/* .callNumber{
    color: white;
    font-size: x-large;
    -webkit-animation: moving 5s infinite;
    animation: moving 7s infinite;
  }


@keyframes moving {
    from {transform: translateX(-100px);color:white;}
    to {transform: translateX(150px);color:transparent;}
}



  .call-animation {
 
    width: 100px;
    height: 100px;
    position: relative;
    margin: 0 auto;
    border-radius: 100%;
    border: solid 5px transparent;
    animation: play 1.6s ease infinite;
    -webkit-backface-visibility: hidden;
    -moz-backface-visibility: hidden;
    -ms-backface-visibility: hidden;
    backface-visibility: hidden;
  
  } */

  .call-animation  {
 
    border-radius: 100%;
    /* background-color: #0c0404; */
    margin: auto;
    animation: playDown 1.6s ease infinite;
  }
.img-circle {
  /* width: 135px;
        height: 135px;
        border-radius: 100%;
        position: absolute;
        left: 0px;
        top: 0px; */
  width: 114px;
  height: 114px;

  position: absolute;
  left: 7px;
  top: 0px;
}

@keyframes playDown {
  0% {
    transform: scale(1);
  }
  15% {
    box-shadow: 0 0 0 5px rgba(14, 1, 1, 0.4);
  }
  25% {
    box-shadow: 0 0 0 10px rgba(63, 40, 40, 0.4),
      0 0 0 20px rgba(78, 65, 65, 0.2);
  }
  25% {
    box-shadow: 0 0 0 15px rgba(87, 79, 79, 0.4),
      0 0 0 30px rgba(92, 90, 90, 0.2);
  }
}


@keyframes play {
  0% {
    transform: scale(1);
  }
  15% {
    box-shadow: 0 0 0 5px rgba(255, 255, 255, 0.4);
  }
  25% {
    box-shadow: 0 0 0 10px rgba(255, 255, 255, 0.4),
      0 0 0 20px rgba(255, 255, 255, 0.2);
  }
  25% {
    box-shadow: 0 0 0 15px rgba(255, 255, 255, 0.4),
      0 0 0 30px rgba(255, 255, 255, 0.2);
  }
}

@keyframes playBtn {
  0% {
    transform: scale(1);
  }
  15% {
    box-shadow: 0 0 0 5px rgba(200, 155, 236, 0.4);
  }
  25% {
    box-shadow: 0 0 0 10px rgba(156, 86, 236, 0.4),
      0 0 0 20px rgba(183, 43, 211, 0.2);
  }
  25% {
    box-shadow: 0 0 0 15px rgba(132, 88, 161, 0.4),
      0 0 0 30px rgba(126, 59, 134, 0.2);
  }
}

.holdConf {
  height: 25px;
  width: 25px;
  border-radius: 100%;
  background-color: bray;
  background-color: #bababa;
  position: absolute;
  top: -13px;
}
.hungupConf {
  height: 25px;
  width: 25px;
  border-radius: 100%;

  background-color: #c91111;
  position: absolute;
  bottom: 23px;
}
#numDisplayDtmf {
  text-align: center;
  border: 0;
  outline: none;
  background-color: transparent;

  color: white;
}
