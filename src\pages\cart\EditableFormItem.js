import { Typography, Form } from "antd";


const Dummy = props => {
 
  return (
    props.value? props.name !== "productDescription" ?<div >{props.value}</div>:<Typography.Text type="secondary">{props.value}</Typography.Text> :<></>
)};

const EditableFormItem = (props) => {
  const { editing,name,label } = props;

  return <Form.Item  labe={label} name={name}>{editing ? props.children : <Dummy name={name[1]} />}</Form.Item>;
};

export default EditableFormItem;
