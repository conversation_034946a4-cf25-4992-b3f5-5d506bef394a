import { useState, useCallback, useRef } from "react";
import { Input, Segmented } from "antd";
import Annuaire from "./annuaire";
import Collegues from "./collegues";
import { useTranslation } from "react-i18next";
import Groups from "./Groups";
import { FiSearch } from "react-icons/fi";
import { debounce } from "lodash";
import { isGuestConnected } from "utils/role";

function Contacts({ handleActionWebPhone }) {
  const [t] = useTranslation("common");
  const inputRef = useRef(null);
  const isGuest = isGuestConnected();

  //
  const [contactsPage, setContactsPage] = useState(t("voip.colleagues"));
  const [displaySearchText, setDisplaySearchText] = useState("");
  const [search, setSearch] = useState("");
  //
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedSearch = useCallback(
    debounce((nextValue) => setSearch(nextValue), 300),
    []
  );

  const handleOnChange = (e) => {
    const inputValue = e?.target?.value;
    if (/^[0-9\s]*$/.test(inputValue)) {
      const searchText = inputValue.replace(/\s+/g, "");
      setDisplaySearchText(searchText);
      debouncedSearch(searchText.trim());
    } else debouncedSearch(inputValue?.trim());
    setDisplaySearchText(inputValue);
  };

  // const clearSearch = () => {
  //   setDisplaySearchText("");
  //   setSearch("");
  // };

  return (
    <div className="contacts relative h-[23rem]">
      <div className=" relative px-2	text-center">
        <Segmented
          block
          options={[
            {
              label: isGuest ? t("menu1.contacts") : t("voip.colleagues"),
              value: t("voip.colleagues"),
              // disabled: isGuest,
            },
            {
              label: isGuest ? t("voip.colleagues") : t("menu1.contacts"),
              value: t("menu1.contacts"),
              disabled: isGuest,
            },
            {
              label: t("voip.groups"),
              value: t("voip.groups"),
              // disabled: isGuest,
            },
          ]}
          value={contactsPage}
          onChange={(value) => {
            // clearSearch();
            setContactsPage(value);
            inputRef?.current?.focus();
          }}
          style={{ marginBottom: "0.5rem" }}
        />
      </div>
      <div className="mb-2 flex justify-center">
        <Input
          autoFocus
          ref={inputRef}
          allowClear
          value={displaySearchText}
          onChange={handleOnChange}
          prefix={
            <FiSearch className="text-slate-400" style={{ fontSize: 16 }} />
          }
          // placeholder={`${t("voip.search")} ${contactsPage}`}
          placeholder={`${t("voip.searchDirectory")}`}
          style={{
            width: "94%",
          }}
        />
      </div>
      {contactsPage === t("menu1.contacts") ? (
        <Annuaire search={search} handleActionWebPhone={handleActionWebPhone} />
      ) : contactsPage === t("voip.colleagues") ? (
        <Collegues
          search={search}
          handleActionWebPhone={handleActionWebPhone}
        />
      ) : (
        <Groups search={search} />
      )}
    </div>
  );
}

export default Contacts;
