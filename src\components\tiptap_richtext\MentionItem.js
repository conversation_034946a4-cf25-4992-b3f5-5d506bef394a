import React, { useRef, useEffect } from "react";

const MentionItem = ({ isActive, className, style, ...props }) => {
  const ref = useRef(null);

  useEffect(() => {
    if (isActive) {
      ref.current?.scrollIntoView({ block: "nearest" });
    }
  }, [isActive]);

  return (
    <div
      ref={ref}
      className={"mentionsItem" + (className ? ` ${className}` : "")}
      style={{ backgroundColor: isActive ? "lightgray" : undefined, ...style }}
      {...props}
    />
  );
};

export default MentionItem;
