import { ArrowLeftOutlined } from "@ant-design/icons";
import { Button } from "antd";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";

const GoBack = () => {
  const { t } = useTranslation("common");

  return (
    <Link to="/login">
      <Button
        type="link"
        className="flex w-full items-center justify-center px-10 py-5"
        icon={<ArrowLeftOutlined />}>
        {t("login.back")}
      </Button>
    </Link>
  );
};

export default GoBack;
