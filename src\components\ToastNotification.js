import throttle from "lodash.throttle";

let toastApi = null;

/**
 * Called by NotificationProvider once to give us the hook-based API.
 */
export function __setToastApi__(api) {
  toastApi = api;
}

/**
 * Normalize any user-passed style so we only end up with
 * string|number values and no numeric or nested keys.
 */
function normalizeStyle(style) {
  if (!style || typeof style !== "object" || Array.isArray(style)) {
    return {};
  }

  return Object.entries(style).reduce((acc, [prop, val]) => {
    if (typeof val === "string" || typeof val === "number") {
      acc[prop] = val;
    }
    return acc;
  }, {});
}

/**
 * The core “fire a toast” function.
 */
function fireToast(cfg) {
  if (!toastApi) {
    console.warn("[toastNotification] API not initialized yet");
    return;
  }

  // start with the caller’s style (normalized)
  const flatStyle = normalizeStyle(cfg.style);

  // inject our zIndex if on /tasks
  if (window.location.pathname.startsWith("/tasks")) {
    flatStyle.zIndex = 9999;
  }

  // build a collapse-friendly key
  const collapseKey =
    cfg.key ||
    `${cfg.type}-${typeof cfg.message === "string" ? cfg.message : ""}`;

  toastApi[cfg.type]({
    message: cfg.message,
    placement: cfg.placement || "topRight",
    duration: cfg.duration || 5,
    icon: cfg.icon,
    key: collapseKey,
    style: flatStyle,
    // closeIcon: null,
    btn: null,
    className: "lean-notification",
  });
}

/**
 * Throttle + defer: max 1 toast per 100ms, each inside rAF.
 */
export const throttledFire = throttle(
  (cfg) => requestAnimationFrame(() => fireToast(cfg)),
  100,
  { leading: true, trailing: true }
);

/**
 * The API your 825 call-sites use, unchanged.
 */
export function toastNotification(
  notifType,
  message,
  placement,
  duration = 3,
  icon,
  maxCount, // ignored here
  key,
  style
) {
  throttledFire({
    type: notifType,
    message,
    placement,
    duration,
    icon,
    key,
    style,
  });
}

/*****************OLD VERSION*********************** */
// import { notification } from "antd";
// import throttle from "lodash/throttle";

// // import { store } from "new-redux/store";

// // const activitiesNotificationsStatus = async () =>
// //   await store.getState().TasksRealTime.isUserNotified;

// // const status = await activitiesNotificationsStatus();
// const location = window.location.pathname;

// export const toastNotification = (
//   notifType,
//   message,
//   placement,
//   duration = 3,
//   icon,
//   maxCount,
//   key,
//   style
// ) => {
//   const combinedStyles = {
//     ...style,
//     zIndex:
//       location?.split("/")[1] === "tasks" ? { zIndex: "1 !important" } : null,
//   };
//   notification.config({
//     maxCount: maxCount ?? 5,
//     getContainer: () => document.body,
//   });
//   notification[notifType]({
//     message,
//     key,
//     placement,
//     duration,
//     icon,
//     style: combinedStyles,
//   });
// };
