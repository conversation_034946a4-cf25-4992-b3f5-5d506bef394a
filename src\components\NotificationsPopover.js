import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Checkbox,
  Divider,
  List,
  Popover,
  Skeleton,
  Space,
  Tabs,
  <PERSON>lt<PERSON>,
  Typography,
} from "antd";
import InfiniteScroll from "react-infinite-scroll-component";
import dayjs from "dayjs";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import relativeTime from "dayjs/plugin/relativeTime";

import {
  getName,
  isTheDay,
} from "../pages/layouts/chat/utils/ConversationUtils";
import {
  CarryOutOutlined,
  CommentOutlined,
  FieldTimeOutlined,
  MoreOutlined,
} from "@ant-design/icons";
import {
  changeListNotifs,
  setNotificationList,
  setRemindersList,
} from "../new-redux/actions/visio.actions/visio";
import { URL_ENV } from "index";
import { AvatarChat } from "./Chat";
import { useSelector } from "react-redux";
import { moment_timezone } from "App";
import { formatNotificationMessage } from "pages/tasks/helpers/handleDisplayNotificationMessage";
import { calculateSum } from "pages/tasks/helpers/calculateSum";
import { MdOutlineVideocam } from "react-icons/md";
import MainService from "services/main.service";
import { setRemindersArray } from "new-redux/actions/tasks.actions/realTime";
import { EXTENSIONS_ARRAY } from "pages/tasks/helpers/calculateSum";

dayjs.extend(relativeTime);

const NotificationsPopover = ({
  notificationsList,
  markNotificationAsRead,
  setTaskToUpdate,
  notificationsPage,
  setNotificationsPage,
  lastNotificationsPage,
  source,
  from = "",
  setActivityLabel,
  markAllNotifsAsRead,
  setFilterNotifications,
  setNotifications = () => {},
  loadNotifs,
  filterNotifications,
  setOpen = () => {},
  setOpenActivity360,
  setShowNotificationsMenu,
  setShowRemindersMenu,
  markAllAsReadLoading = false,
  reminders,
}) => {
  const [disableMakeAllReadBtn, setDisableMakeAllReadBtn] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [loadReminder, setLoadReminder] = useState(false);
  const language = localStorage.getItem("language");
  const dispatch = useDispatch();
  const [t, i18n] = useTranslation("common");
  const { totalNotificationNumber, activitiesMessages, remindersList } =
    useSelector((state) => state?.TasksRealTime);
  const { remindersListVisio, countReminders, notificationCount } = useSelector(
    (state) => state?.visioList
  );
  const paginateRemindersVisio = async (page) => {
    setLoadReminder(true);
    try {
      const res = await MainService.getNumberOfNotifications(page);
      if (from === "listVisio") {
        dispatch(
          setRemindersList({
            ...res.data.reminders_visio,
            meta: res.data.reminders_visio?.meta,
            data: [
              ...remindersListVisio?.data,
              ...res.data.reminders_visio?.data,
            ],
          })
        );
      } else
        dispatch(
          setRemindersArray({
            ...res.data.reminders,
            data: [...remindersList?.data, ...res.data.reminders?.data],
          })
        );
      setLoadReminder(false);
    } catch (err) {
      setLoadReminder(false);
    }
  };

  const { loadNotifsVisio } = useSelector((state) => state?.visioList);
  const content = (
    <Space direction="vertical">
      {activeTab === 0 ? (
        <Checkbox
          onChange={(e) => {
            let { checked } = e?.target;
            setNotificationsPage(1);
            setFilterNotifications(checked ? 1 : 0);
            if (from === "listVisio") {
              dispatch(changeListNotifs([]));
              dispatch(
                setNotificationList({
                  page: 1,
                  t,
                  unread: checked ? 1 : 0,
                })
              );
            }
          }}
        >
          {t("tasks.unreadNotifs")}
        </Checkbox>
      ) : null}
      <Button
        type="link"
        onClick={() => {
          markAllNotifsAsRead(
            activeTab === 0
              ? ""
              : activeTab === 3 && from === "listVisio"
              ? "visio"
              : activeTab === 3 && from !== "listVisio"
              ? "task"
              : ""
          );
        }}
        // disabled={totalNotificationNumber === 0}
        loading={markAllAsReadLoading}
      >
        {t("tasks.markAllRead")}
      </Button>
    </Space>
  );
  return (
    <>
      <div className="flex items-center justify-between pb-4">
        <Typography.Title level={4}>Notifications</Typography.Title>
        {/* {!disableMakeAllReadBtn && (
            <Checkbox
              onChange={(e) => {
                let { checked } = e?.target;
                setNotificationsPage(1);
                setFilterNotifications(checked ? 1 : 0);
                if (from === "listVisio") {
                  dispatch(changeListNotifs([]));
                  dispatch(
                    setNotificationList({
                      page: 1,
                      t,
                      unread: checked ? 1 : 0,
                    })
                  );
                }
              }}
            >
              {t("tasks.unreadNotifs")}
            </Checkbox>
          )} */}
      </div>
      <Tabs
        // tabBarExtraContent={
        //   !disableMakeAllReadBtn && (
        //     <Button
        //       type="link"
        // onClick={markAllNotifsAsRead}
        // disabled={totalNotificationNumber === 0}
        // loading={markAllAsReadLoading}
        //     >
        //       {t("tasks.markAllRead")}
        //     </Button>
        //   )
        // }
        onChange={(activeKey) => {
          setActiveTab(activeKey);
          if (activeKey === 0) {
            setDisableMakeAllReadBtn(false);
            setNotificationsPage(1);
            setNotifications([]);
            if (from === "listVisio") {
              dispatch(changeListNotifs([]));
              dispatch(
                setNotificationList({
                  page: 1,
                  t,
                  unread: activeKey,
                })
              );
            }
            setFilterNotifications(0);
          } else {
            setDisableMakeAllReadBtn(true);
            setFilterNotifications(null);
          }
        }}
        defaultActiveKey={filterNotifications}
        items={[
          {
            key: 0,
            icon:
              from === "listVisio" ? (
                <MdOutlineVideocam />
              ) : (
                <CarryOutOutlined />
              ),
            label: (
              <>
                <Typography.Text>
                  {from === "listVisio" ? "Visio" : t("menu1.tasks")}
                </Typography.Text>
                <Badge
                  count={
                    from === "listVisio"
                      ? Number(notificationCount) > 0
                        ? Number(notificationCount)
                        : 0
                      : Number(totalNotificationNumber) > 0
                      ? Number(totalNotificationNumber)
                      : 0
                  }
                  size="small"
                  className="ml-1"
                />
                <Popover content={content} trigger={["click"]}>
                  <Badge dot={filterNotifications > 0} offset={[-3, 6]}>
                    <Button
                      icon={<MoreOutlined />}
                      type="text"
                      size="small"
                      shape="circle"
                      style={{ marginLeft: "8px" }}
                    />
                  </Badge>
                </Popover>
              </>
            ),
            children: (
              <div
                id="scrollableDiv"
                style={{
                  maxHeight: 400,
                  overflow: "auto",
                }}
              >
                <InfiniteScroll
                  dataLength={notificationsList && notificationsList.length}
                  hasMore={notificationsPage < lastNotificationsPage}
                  next={() => {
                    from === "listVisio"
                      ? dispatch(
                          setNotificationList({
                            page: notificationsPage + 1,
                            t,
                            unread: filterNotifications,
                          })
                        )
                      : setNotificationsPage(notificationsPage + 1);
                  }}
                  loader={
                    <Skeleton
                      style={{ width: 400 }}
                      avatar
                      paragraph={{
                        rows: 1,
                      }}
                      active
                    />
                  }
                  height={
                    source === "notifications" &&
                    notificationsList &&
                    notificationsList?.length >= 10
                      ? 400
                      : "auto"
                  }
                  endMessage={
                    source === "notifications" &&
                    notificationsList &&
                    notificationsList?.length > 0 && (
                      <Divider plain>{t("tasks.noMoreNotifs")}</Divider>
                    )
                  }
                  scrollableTarget="scrollableDiv"
                >
                  <List
                    style={{ width: 400 }}
                    dataSource={
                      notificationsList &&
                      notificationsList.filter((el) => el !== null)
                    }
                    size="small"
                    loading={
                      from === "listVisio" ? loadNotifsVisio : loadNotifs
                    }
                    renderItem={(item, index) => (
                      <>
                        <div className="sticky top-0 z-10 bg-white">
                          {!moment_timezone(
                            item?.created_at?.split(" ")[0]
                          ).isSame(
                            moment_timezone(
                              notificationsList[index - 1]?.created_at?.split(
                                " "
                              )[0]
                            )
                          ) && (
                            <p className="bg-white font-medium uppercase">
                              {isTheDay(
                                new Date(item?.created_at?.split(" ")[0]),
                                "Y",
                                new Date()
                              ) ||
                              moment_timezone(
                                item?.created_at?.split(" ")[0]
                              ).isSame(moment_timezone(), "day")
                                ? moment_timezone(item?.created_at).calendar(
                                    null,
                                    {
                                      lastDay: () =>
                                        i18n.language === "fr"
                                          ? "[Hier]"
                                          : "[Yesterday]",
                                      sameDay: () =>
                                        i18n.language === "fr"
                                          ? "[Aujourd'hui]"
                                          : "[Today]",
                                    }
                                  )
                                : moment_timezone(item?.created_at).format(
                                    "ll"
                                  )}
                              {/* </Divider> */}
                            </p>
                          )}
                        </div>
                        <List.Item
                          onClick={() => {
                            setTaskToUpdate(item?.id_data);
                            setOpenActivity360(true);
                            setShowNotificationsMenu(false);

                            if (item?.read === 0) {
                              markNotificationAsRead("notification", {
                                log_id: item?.logId ? item?.logId : item?.id,
                              });
                            }
                          }}
                          key={item?.data_id}
                          className={`notification-list-item my-1 cursor-pointer ${
                            item?.read === 1
                              ? "bg-[#fff] hover:bg-[#bae0ff]"
                              : "bg-[#e6f4ff] hover:bg-[#bae0ff]"
                          } rounded-md transition-colors duration-300`}
                        >
                          <List.Item.Meta
                            style={{
                              padding: "2px",
                              borderRadius: "6px",
                            }}
                            avatar={
                              <div className="flex flex-row items-center">
                                {item?.read === 0 && (
                                  <Badge dot status="processing" />
                                )}
                                <Tooltip
                                  title={getName(item?.user_2?.label, "name")}
                                  placement="top"
                                >
                                  <AvatarChat
                                    fontSize={"0.875rem"}
                                    className={
                                      "mx-1.5 flex items-center justify-center"
                                    }
                                    height={"32px"}
                                    width={"32px"}
                                    url={`${
                                      URL_ENV?.REACT_APP_BASE_URL +
                                      URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                                    }${item?.avatar || item?.user_2?.avatar}`}
                                    hasImage={
                                      item?.avatar
                                        ? EXTENSIONS_ARRAY?.includes(
                                            item?.avatar?.split(".")?.pop()
                                          )
                                        : EXTENSIONS_ARRAY?.includes(
                                            item?.user_2?.avatar
                                              ?.split(".")
                                              ?.pop()
                                          )
                                    }
                                    name={getName(
                                      item?.label || item?.user_2?.label,
                                      "avatar"
                                    )}
                                    type="user"
                                  />
                                </Tooltip>
                              </div>
                            }
                            title={
                              <>
                                <Typography.Text
                                  strong={item?.read === 0 ? true : false}
                                >
                                  {getName(item?.user, "name")}
                                </Typography.Text>
                                <Typography.Text
                                  type="secondary"
                                  className="float-right"
                                >
                                  {dayjs()
                                    ?.locale(language)
                                    ?.to(dayjs(item?.created_at))}
                                </Typography.Text>
                              </>
                            }
                            description={
                              <Typography.Text>
                                {formatNotificationMessage(
                                  t,
                                  getName(
                                    item?.user_2?.label
                                      ? item?.user_2?.label
                                      : item?.user,
                                    "name"
                                  ),
                                  item?.action
                                )}
                              </Typography.Text>
                            }
                          />
                        </List.Item>
                      </>
                    )}
                  />
                </InfiniteScroll>
              </div>
            ),
          },
          from !== "listVisio" && {
            key: 2,
            icon: <CommentOutlined />,

            label: (
              <>
                <Typography.Text>{t("tasks.commentsTab")}</Typography.Text>
                <Badge
                  count={
                    activitiesMessages &&
                    calculateSum(activitiesMessages?.unread_msg_room)
                  }
                  size="small"
                  className="ml-1"
                />
              </>
            ),
            children: (
              <div
                id="scrollableDiv"
                style={{
                  maxHeight: 400,
                  overflow: "auto",
                }}
              >
                <InfiniteScroll
                  dataLength={
                    activitiesMessages &&
                    calculateSum(activitiesMessages?.unread_msg_room)
                  }
                  // hasMore={notificationsPage < lastNotificationsPage}
                  // next={() => {
                  //   from === "listVisio"
                  //     ? dispatch(
                  //         setNotificationList({
                  //           page: notificationsPage + 1,
                  //           t,
                  //           unread: filterNotifications,
                  //         })
                  //       )
                  //     : setNotificationsPage(notificationsPage + 1);
                  // }}
                  loader={
                    <Skeleton
                      style={{ width: 400 }}
                      avatar
                      paragraph={{
                        rows: 1,
                      }}
                      active
                    />
                  }
                  height={
                    source === "notifications" &&
                    activitiesMessages?.unread_msg_room &&
                    calculateSum(activitiesMessages?.unread_msg_room)?.length >=
                      10
                      ? 400
                      : "auto"
                  }
                  endMessage={
                    activitiesMessages?.unread_msg_room &&
                    calculateSum(activitiesMessages?.unread_msg_room) > 0 && (
                      <Divider plain>{t("tasks.noMoreComments")}</Divider>
                    )
                  }
                  scrollableTarget="scrollableDiv"
                >
                  <List
                    style={{ width: 400 }}
                    dataSource={
                      activitiesMessages?.unread_msg_room &&
                      activitiesMessages?.unread_msg_room
                    }
                    size="small"
                    loading={false}
                    renderItem={(item) => (
                      <List.Item
                        onClick={async () => {
                          setTaskToUpdate(item?.room_relation_id);
                          setOpenActivity360(true);
                          setShowNotificationsMenu(false);
                        }}
                        // key={item?.data_id}
                        className={`notification-list-item my-1 cursor-pointer rounded-md bg-[#e6f4ff] transition-colors duration-300 hover:bg-[#bae0ff]`}
                        extra={<Badge count={item?.messages_count} />}
                      >
                        <List.Item.Meta
                          style={{
                            padding: "2px",
                            borderRadius: "6px",
                          }}
                          avatar={
                            <div className="flex flex-row items-center">
                              <Badge dot status="processing" />
                              <Tooltip
                                title={getName(item?.room_name, "name")}
                                placement="top"
                              >
                                <AvatarChat
                                  fontSize={"0.875rem"}
                                  className={
                                    "mx-1.5 flex items-center justify-center"
                                  }
                                  height={"32px"}
                                  width={"32px"}
                                  hasImage={false}
                                  name={getName(item?.room_name, "avatar")}
                                  type="room"
                                />
                              </Tooltip>
                            </div>
                          }
                          title={
                            <>
                              <Typography.Text strong>
                                {item?.room_name}
                              </Typography.Text>
                            </>
                          }
                          description={
                            <Typography.Text type="secondary">
                              {t("tasks.newComment", {
                                messageCount: item?.messages_count,
                                s: item?.messages_count > 1 ? "s" : "",
                              })}
                            </Typography.Text>
                          }
                        />
                      </List.Item>
                    )}
                  />
                </InfiniteScroll>
              </div>
            ),
          },
          {
            key: 3,
            icon: <FieldTimeOutlined />,
            label: (
              <>
                <Typography.Text>{t("tasks.reminderTooltip")}</Typography.Text>
                <Badge
                  count={
                    from !== "listVisio"
                      ? Number(remindersList?.meta?.total) //total reminders in task
                      : Number(countReminders) //total reminders in visio
                  }
                  size="small"
                  className="ml-1"
                />
                <Popover content={content} trigger={["click"]}>
                  <Badge offset={[-3, 6]}>
                    <Button
                      icon={<MoreOutlined />}
                      type="text"
                      size="small"
                      shape="circle"
                      style={{ marginLeft: "8px" }}
                    />
                  </Badge>
                </Popover>
              </>
            ),
            children: (
              <div
                id="scrollableDiv"
                style={{
                  maxHeight: 400,
                  overflow: "auto",
                }}
              >
                <InfiniteScroll
                  dataLength={
                    from === "listVisio"
                      ? remindersListVisio && remindersListVisio?.data
                        ? remindersListVisio?.data?.length
                        : remindersListVisio?.length
                      : remindersList && remindersList?.data
                      ? remindersList?.data?.length
                      : remindersList?.length
                  }
                  hasMore={
                    from === "listVisio"
                      ? remindersListVisio?.meta?.current_page <
                        remindersListVisio?.meta?.last_page
                      : remindersList?.meta?.current_page <
                        remindersList?.meta?.last_page
                  }
                  next={() => {
                    from === "listVisio"
                      ? paginateRemindersVisio(
                          remindersListVisio?.meta?.current_page + 1
                        )
                      : paginateRemindersVisio(
                          remindersList?.meta?.current_page + 1
                        );
                  }}
                  loader={
                    <Skeleton
                      style={{ width: 400 }}
                      avatar
                      paragraph={{
                        rows: 1,
                      }}
                      active
                    />
                  }
                  height={
                    source === "notifications"
                      ? from !== "listVisio" &&
                        remindersList?.data &&
                        remindersList?.data?.length >= 5
                        ? 400
                        : from === "listVisio" && countReminders >= 5
                        ? 400
                        : "auto"
                      : "auto"
                  }
                  endMessage={
                    source === "notifications" && from === "listVisio"
                      ? countReminders > 0
                      : remindersList?.data &&
                        remindersList?.data?.length > 0 && (
                          <Divider plain>{t("tasks.noMoreReminders")}</Divider>
                        )
                  }
                  scrollableTarget="scrollableDiv"
                >
                  <List
                    style={{ width: 400 }}
                    // dataSource={reminders && reminders.filter((el) => el !== null)}
                    dataSource={
                      from === "listVisio"
                        ? remindersListVisio?.data
                        : remindersList?.data
                    }
                    size="small"
                    loading={from === "listVisio" ? loadReminder : loadNotifs}
                    renderItem={(item) => (
                      <List.Item
                        onClick={() => {
                          setTaskToUpdate(item?.id_data ?? item?.id);
                          setOpenActivity360(true);
                          setShowNotificationsMenu(false);

                          markNotificationAsRead("reminder", {
                            log_id: item?.logId ?? item?.id,
                          });
                        }}
                        key={item?.logId}
                        className={`notification-list-item my-1 cursor-pointer ${
                          item?.read === 1
                            ? "bg-[#fff] hover:bg-[#bae0ff]"
                            : "bg-[#e6f4ff] hover:bg-[#bae0ff]"
                        } rounded-md transition-colors duration-300`}
                      >
                        <List.Item.Meta
                          style={{
                            padding: "2px",
                            borderRadius: "6px",
                          }}
                          avatar={
                            <div className="flex flex-row items-center">
                              {<Badge dot status="processing" />}
                              <Avatar
                                icon={
                                  <FieldTimeOutlined
                                    style={{ color: "#374151" }}
                                  />
                                }
                                style={{
                                  backgroundColor: "#ffec3d",
                                  marginLeft: "6px",
                                }}
                              />
                            </div>
                          }
                          title={t("tasks.reminderTitle")}
                          description={
                            item?.has_occured === 0 ? (
                              <div
                                dangerouslySetInnerHTML={{
                                  __html: t("tasks.mercureReminder", {
                                    taskLabel: item?.action
                                      ? item?.action?.[0]
                                      : item?.message?.[0],
                                    time: item?.action
                                      ? item?.action?.[1]
                                      : item?.message?.[1],
                                  }),
                                }}
                              ></div>
                            ) : (
                              <div
                                dangerouslySetInnerHTML={{
                                  __html: t("tasks.occurredActivityReminder", {
                                    activityLabel: item?.action?.[1],
                                    startDate: item?.action?.[2],
                                    reminderDuration: item?.action?.[3],
                                  }),
                                }}
                              ></div>
                            )
                          }
                        />
                      </List.Item>
                    )}
                  />
                </InfiniteScroll>
              </div>
            ),
          },
        ]}
      />
    </>
  );
};

export default NotificationsPopover;
