import { Card, Col, Row } from "antd";
import { URL_ENV } from "index";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { generateAxios } from "services/axiosInstance";
import { DonutChart2, OneBar<PERSON>hart } from "../ChartsDashboard";
import CardStat from "../CardStat";

const DashboardNotes = ({ start, end }) => {
  const [notesAssignedToModules, setNotesAssignedToModules] = useState({});
  const [myNotes, setMyNotes] = useState({});
  const [myNotes2, setMyNotes2] = useState({});

  const { i18n } = useTranslation("common");

  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await generateAxios(
          `${URL_ENV?.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
        ).get(
          `/notes-modules-stats?language=${i18n.language}&start_date=${start}&end_date=${end}`
        );
        setNotesAssignedToModules(res.data);
      } catch (err) {}
    };
    fetchData();
  }, [i18n.language, start, end]);
  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await generateAxios(
          `${URL_ENV?.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
        ).get(
          `/notes-stats-1?language=${i18n.language}&start_date=${start}&end_date=${end}`
        );
        setMyNotes(res.data);
      } catch (err) {}
    };
    fetchData();
  }, [i18n.language, start, end]);
  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await generateAxios(
          `${URL_ENV?.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
        ).get(
          `/notes-stats-2?language=${i18n.language}&start_date=${start}&end_date=${end}`
        );
        setMyNotes2(res.data);
      } catch (err) {}
    };
    fetchData();
  }, [i18n.language, start, end]);
  function getColorForIndex(index) {
    const colors = ["#F39C12", "#9B59B6", "#2ECC71", "#3498DB"]; // Exemple de couleurs
    return colors[index]; // Assigne une couleur différente à chaque élément
  }

  return (
    <Row gutter={[16, 16]}>
      <Col className="gutter-row" span={10}>
        <CardStat title={notesAssignedToModules?.name}>
          <DonutChart2
            data={notesAssignedToModules?.data}
            total={notesAssignedToModules?.total}
            name={""}
          />
        </CardStat>
      </Col>
      <Col className="gutter-row" span={7}>
        <CardStat title={myNotes?.name}>
          <DonutChart2
            data={myNotes?.data}
            total={myNotes?.total}
            name={""}
            // isExistDate={false}
          />
        </CardStat>
      </Col>
      <Col className="gutter-row" span={7}>
        <CardStat title={myNotes2?.name}>
          <DonutChart2
            data={myNotes2?.data?.map((el, i) => ({
              ...el,
              color: getColorForIndex(i),
            }))}
            total={myNotes2?.total}
            name={""}
          />
        </CardStat>
      </Col>
    </Row>
  );
};

export default DashboardNotes;
