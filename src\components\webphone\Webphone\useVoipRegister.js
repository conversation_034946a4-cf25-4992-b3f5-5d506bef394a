import { useRef, useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { UA } from "sip.js";
import { decryptC } from "./PhoneCpt";
import { URL_ENV } from "index";
import useNetwork from "custom-hooks/useNetwork";
// import useFocusWindow from "pages/layouts/chat/hooks/useFocusWindow";
import { checkShowNotification } from "utils/real-time-function/chat/function";
import { toastNotification } from "components/ToastNotification";
import MainService from "services/main.service";
import { notification } from "antd";
// import useNetworkStatus from "custom-hooks/useNetworkStatus";
//
const reconnectAudio = new Audio("/sounds/connecting_voip.ogg");
reconnectAudio.loop = true;

const startReconnectTone = () => {
  reconnectAudio
    .play()
    .catch((err) => console.error("Audio play failed:", err));
};
const stopReconnectTone = () => {
  if (reconnectAudio.paused) return;
  reconnectAudio.pause();
  reconnectAudio.currentTime = 0;
};

export const getCurrentTime = () => {
  const now = new Date();
  const hours = String(now.getHours()).padStart(2, "0");
  const minutes = String(now.getMinutes()).padStart(2, "0");
  const seconds = String(now.getSeconds()).padStart(2, "0");
  return `${hours}:${minutes}:${seconds}`;
};

const useVoipRegister = (params, currentUser) => {
  //
  const { isOnline } = useNetwork();
  //
  const eventMercure = useSelector((state) => state.ChatRealTime.eventMercure);
  // const receiverInfo = useSelector((state) => state.voipBlackList.receiverInfo);
  const currentIP = useSelector((state) => state.voip.addressIp);
  //
  const MAX_RECONNECTION_ATTEMPTS = 5;
  const MAX_QUICK_RETRY = 3;
  const RECONNECTION_DELAY = 5000;
  const phoneStatusRef = useRef("processing");
  const uaInstance = useRef(null);
  const uaEventHandlers = useRef({}); // Store event handlers for later removal
  const lastKnownIP = useRef(currentIP);
  const isReconnecting = useRef(false); // to prevent overlapping reconnection attempts
  const attempts = useRef(0);
  const isStopping = useRef(false);
  const reconnectionDelayTimer = useRef(null);
  //
  // const [uaInstance, setUaInstance] = useState(null);
  const [phoneStatus, setPhoneStatus] = useState("processing");
  const [isServerReachableState, setIsServerReachableState] = useState(false);
  //
  const isCallActive = () => {
    if (!uaInstance.current) return false;

    // return (
    //   uaInstance.current &&
    //   Object.keys(uaInstance.current.sessions || {}).length > 0
    // );

    const sessions = uaInstance.current.sessions || {};

    return Object.values(sessions).some((session) => {
      return session.statusAppel === "accepted";
    });
  };
  // console.log(
  //   uaInstance.current,
  //   uaInstance.current?.registerContext?.contact?.split(".")?.[0]
  // );
  //
  const updatePhoneStatus = (status) => {
    if (status !== phoneStatusRef.current) {
      console.warn(
        "[[VOIP_REGISTER]] useVoipRegister__________ status:",
        status,
        `at: ${getCurrentTime()}`
      );
      if (status === "success") notification.destroy("notification-webphone");
      phoneStatusRef.current = status;
      setPhoneStatus(status);
    }
  };
  //
  // console.log({ uaInstance, uaEventHandlers });
  useEffect(() => {
    if (!lastKnownIP.current && currentIP) {
      lastKnownIP.current = currentIP;
    }
  }, [currentIP]);
  //
  // Show WebPhone Error Notification
  const showWebPhoneErrorNotification = (timeout, cause) => {
    if (window.location.pathname === "/logout") return;
    const timer = setTimeout(() => {
      if (
        (uaInstance.current && uaInstance.current?.isRegistered()) ||
        phoneStatusRef.current === "success" ||
        phoneStatusRef.current === "processing" ||
        isReconnecting.current
      )
        return;
      else {
        toastNotification(
          "error",
          "Le WebPhone est déconnecté!",
          "bottomLeft",
          5,
          undefined,
          1,
          "notification-webphone"
        );

        checkShowNotification({
          type: "webphone",
          conversation: null,
          navigate: () => {},
          notificationContent: {
            default_image:
              process.env.PUBLIC_URL + "/images/logo192-comunik.png",
            title: "Le WebPhone est déconnecté!",
          },
        });
      }
      clearTimeout(timer);
    }, timeout || 10000);
  };
  //
  // Attach event listeners to UA
  const attachUAEventListeners = (ua) => {
    // Define and store event handlers
    uaEventHandlers.current = {
      connecting: (args) => {
        console.warn(
          "[[VOIP_REGISTER]] WebSocket attempting to connect..." +
            args.attempts,
          `at: ${getCurrentTime()}`
        );
        updatePhoneStatus("processing");
      },
      connected: () => {
        console.warn(
          "[[VOIP_REGISTER]] WebSocket connected",
          `at: ${getCurrentTime()}`
        );
        // updatePhoneStatus("success");
        attempts.current = 0;
      },
      registered: () => {
        console.warn(
          "[[VOIP_REGISTER]] SIP UA registered",
          `at: ${getCurrentTime()}`
        );
        updatePhoneStatus("success");
        attempts.current = 0;
      },
      disconnected: () => {
        console.warn(
          "[[VOIP_REGISTER]] WebSocket disconnected",
          `at: ${getCurrentTime()}`
        );
        updatePhoneStatus("default");
        attemptReconnection();
      },
      unregistered: async (response, cause) => {
        console.warn(
          "[[VOIP_REGISTER]] SIP UA unregistered. Cause:",
          cause,
          {
            response,
          },
          `at: ${getCurrentTime()}`
        );
        updatePhoneStatus("default");
        await initializeUA();
      },
      registrationFailed: async (response, cause) => {
        console.error(
          "[[VOIP_REGISTER]] SIP registration failed! Cause:",
          cause,
          { response },
          `at: ${getCurrentTime()}`
        );
        updatePhoneStatus("default");
        await initializeUA();
      },
    };
    // Attach event listeners
    for (const [eventName, handler] of Object.entries(
      uaEventHandlers.current
    )) {
      ua.on(eventName, handler);
    }
  };
  //
  // Detach event listeners from UA
  const detachUAEventListeners = (ua) => {
    if (uaEventHandlers.current) {
      for (const [eventName, handler] of Object.entries(
        uaEventHandlers.current
      )) {
        ua.removeListener(eventName, handler);
      }
      uaEventHandlers.current = {};
    }
  };
  //
  // Stop and clean up UA instance
  const stopUaInstance = async () => {
    if (isStopping.current) {
      console.warn(
        "[[VOIP_REGISTER]] stopUaInstance is already running.",
        `at: ${getCurrentTime()}`
      );
      return;
    }
    isStopping.current = true;

    if (uaInstance.current) {
      try {
        console.warn(
          "[[VOIP_REGISTER]] stopUaInstance is processing.....",
          `at: ${getCurrentTime()}`
        );
        detachUAEventListeners(uaInstance.current);
        await uaInstance.current.unregister({ all: true });
        uaInstance.current.stop();
        updatePhoneStatus("default");
        if (uaInstance.current?.transport) {
          uaInstance.current.transport?.disconnect();
        }
        uaInstance.current = null;
      } catch (error) {
        console.error(
          "[[VOIP_REGISTER]] Error stopping UA instance:",
          error,
          `at: ${getCurrentTime()}`
        );
      } finally {
        stopReconnectTone();
      }
    }
    isStopping.current = false;
  };
  //
  const initializeUA = async () => {
    if (!params?.param_1 || !currentUser?.extension) {
      console.warn(
        "[[VOIP_REGISTER]] param_1 is missing, aborting registration.",
        `at: ${getCurrentTime()}`
      );
      return;
    }
    if (isReconnecting.current || !isOnline || !isServerReachableState) {
      return;
    }

    if (uaInstance.current) {
      await stopUaInstance();
    }
    isReconnecting.current = true;

    const name = decryptC(params.param_1);
    const pwd = decryptC(params.param_2);
    const url = new URL(URL_ENV.REACT_APP_VOIP_URL);

    const sipConfig = {
      password: pwd,
      displayName: name,
      uri: `sip:${name}@${url.hostname}`,
      wsServers: `wss://${url.hostname}:8089/ws`,
      keepAliveInterval: 1,
      autostart: false,
      traceSip: true,
      log: { level: 0 },
      hackWssInTransport: true,
      wsServerMaxReconnection: 5,
      wsServerReconnectionTimeout: 4,
      connectionRecoveryMaxInterval: 30,
      // connectionTimeout: 1,
      registerExpires: 120,
      // registerExpires: 10, // just for testing !!!!!!!!!!!!!
      noAnswerTimeout: 60,
      allowLegacyNotifications: true,
      media: {
        video: { codec: "VP8" },
        audio: { codec: "opus" },
      },
    };

    try {
      const ua = new UA(sipConfig);
      uaInstance.current = ua;
      attachUAEventListeners(ua);
      ua.start();
    } catch (error) {
      console.error(
        "[[VOIP_REGISTER]] Error initializing UA:",
        error,
        `at: ${getCurrentTime()}`
      );
      updatePhoneStatus("default");
    } finally {
      isReconnecting.current = false;
    }
  };
  //
  const attemptReconnection = () => {
    if (attempts.current >= MAX_RECONNECTION_ATTEMPTS) {
      console.error(
        "[[VOIP_REGISTER]] Maximum reconnection attempts reached.",
        { MAX_RECONNECTION_ATTEMPTS },
        `at: ${getCurrentTime()}`
      );
      showWebPhoneErrorNotification(1);
      updatePhoneStatus("warning");
      return;
    }

    // If we have a UA instance, try to reconnect it up to 3 times before full re-init
    if (uaInstance.current && attempts.current < MAX_QUICK_RETRY) {
      attempts.current += 1;
      updatePhoneStatus("processing");
      setTimeout(async () => {
        console.warn(
          `[[VOIP_REGISTER]] Quick attempt (${attempts.current}) to re-register existing UA...`,
          `at: ${getCurrentTime()}`
        );

        // Try to re-register or connect the existing UA instance directly
        try {
          uaInstance.current.register();
        } catch (error) {
          console.error(
            "[[VOIP_REGISTER]] Error on quick re-register attempt:",
            error
          );
        }

        // Check if it got registered
        setTimeout(() => {
          if (uaInstance.current && uaInstance.current.isRegistered()) {
            console.warn(
              "[[VOIP_REGISTER]] UA re-registered without full restart"
            );
            attempts.current = 0;
          } else {
            // If still not registered, attempt again or eventually do a full re-init
            attemptReconnection();
          }
        }, 2000); // Wait a short time to see if registration succeeds
      }, RECONNECTION_DELAY);
    } else {
      // After 3 attempts, do the full restart process
      attempts.current += 1;
      updatePhoneStatus("processing");
      setTimeout(async () => {
        console.warn(
          `[[VOIP_REGISTER]] Attempting full reinitialization (${
            attempts.current - MAX_QUICK_RETRY
          })...`,
          `at: ${getCurrentTime()}`
        );

        // await stopUaInstance();
        await initializeUA();

        if (uaInstance.current && uaInstance.current.isRegistered()) {
          attempts.current = 0;
        } else {
          attemptReconnection();
        }
      }, RECONNECTION_DELAY * attempts.current);
    }
  };
  //
  // reachability changes in isOnline and eventMercure.readyState
  useEffect(() => {
    let abortController = new AbortController();

    const checkReachability = async () => {
      if (!isOnline || !params || !currentUser?.extension) {
        setIsServerReachableState(false);
        return;
      }
      if (eventMercure?.readyState === 1) {
        setIsServerReachableState(true);
      } else {
        try {
          console.warn(
            "[[VOIP_REGISTER]] Trying to ping the server",
            `at: ${getCurrentTime()}`
          );
          await MainService.pingServer(abortController.signal);
          setIsServerReachableState(true);
        } catch (error) {
          if (error?.message === "canceled") return;
          const status = error?.response?.status;
          if (status === 429 || status === 404) {
            setIsServerReachableState(true);
          } else {
            console.error(
              "[[VOIP_REGISTER]] The server is not reachable:",
              `at: ${getCurrentTime()}`
            );
            setIsServerReachableState(false);
          }
        }
      }
    };

    checkReachability();

    return () => {
      abortController.abort();
    };
  }, [isOnline, eventMercure?.readyState]);
  //
  // Handle network && IP address when change
  useEffect(() => {
    const handleOnline = async () => {
      if (!isServerReachableState || !isOnline) {
        if (uaInstance.current && isCallActive()) {
          console.warn(
            "[[VOIP_REGISTER]] Network lost while in call. Waiting 60 seconds before stopping UA...",
            `at: ${getCurrentTime()}`
          );
          startReconnectTone();
          if (reconnectionDelayTimer.current) {
            clearTimeout(reconnectionDelayTimer.current);
          }
          reconnectionDelayTimer.current = setTimeout(async () => {
            if (!isOnline) {
              console.warn(
                "[[VOIP_REGISTER]] 60 seconds elapsed with no network recovery. Stopping UA...",
                `at: ${getCurrentTime()}`
              );
              await stopUaInstance();
              showWebPhoneErrorNotification(1);
            }
          }, 30000);
        } else {
          // No active call; stop immediately.
          await stopUaInstance();
          showWebPhoneErrorNotification(1);
        }
      } else {
        // Network and server are reachable.
        stopReconnectTone();
        if (reconnectionDelayTimer.current) {
          clearTimeout(reconnectionDelayTimer.current);
          reconnectionDelayTimer.current = null;
        }
        if (currentIP && lastKnownIP.current !== currentIP) {
          console.warn(
            `[[VOIP_REGISTER]] IP changed from ${lastKnownIP.current} to ${currentIP}, re-registering UA...`,
            `at: ${getCurrentTime()}`
          );
          await stopUaInstance();
          lastKnownIP.current = currentIP;
          await initializeUA();
        } else if (!uaInstance.current || !uaInstance.current.isRegistered()) {
          await initializeUA();
        }
      }
    };

    handleOnline();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOnline, isServerReachableState, currentIP, params]);

  //
  // Clean up on component unmount
  useEffect(() => {
    return async () => {
      try {
        if (uaInstance.current) await stopUaInstance();
      } finally {
        updatePhoneStatus("default");
        uaInstance.current = null;
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  //
  //
  return {
    phone: uaInstance.current,
    status: phoneStatus,
    isRegistered: () => uaInstance.current?.isRegistered(),
    stopReconnectTone: () => stopReconnectTone(),
  };
};

export default useVoipRegister;
