import { CheckCircleFilled, CheckCircleOutlined } from "@ant-design/icons";
import { moment_timezone } from "App";
import {
  Avatar,
  Button,
  Checkbox,
  Divider,
  Form,
  Progress,
  Tooltip,
  Typography,
} from "antd";
import { setPollsVote } from "new-redux/actions/chat.actions";
import React, { useEffect, useState } from "react";
import { BiPoll } from "react-icons/bi";
import { useSelector } from "react-redux";
import { motion } from "framer-motion";

import ClassNames from "../ClassNames";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { RxDotFilled } from "react-icons/rx";
import AvatarChat from "components/Chat/Avatar/AvatarChat";
import { URL_ENV } from "index";
import { getName } from "pages/layouts/chat/utils/ConversationUtils";
import { useActionMessage } from "pages/layouts/chat/hooks/useActionMessage";

const { Text, Title } = Typography;

function PollsMessage({ item, source, isBig }) {
  const { pollsRender, currentUser, selectedParticipants } = useSelector(
    (state) => state.chat
  );
  const selectedConversation = useSelector(
    (state) => state.ChatRealTime.selectedConversation
  );
  const { user } = useSelector((state) => state.user);

  const [showResultPolls, setShowResultPolls] = useState(
    item?.poll?.end_date &&
      moment_timezone().isAfter(moment_timezone(item?.poll?.end_date))
  );

  const {
    mutate: handleActionMessage,
    isSuccess: isMessageSuccess,
    isError,
  } = useActionMessage("add_vote");
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const { t } = useTranslation("common");

  useEffect(() => {
    let timeout;
    if (isError) {
      if (
        item?.poll?.options
          ?.filter((option) => option?.users_id?.includes(currentUser?._id))
          .map((option) => option._id).length === 0
      )
        form.resetFields();
    }
    if (
      item?.poll?.options &&
      moment_timezone(item?.poll?.end_date).isAfter(moment_timezone())
    ) {
      form.setFieldValue(
        "check_multiple",
        item?.poll?.options
          ?.filter((option) => option?.users_id?.includes(currentUser?._id))
          .map((option) => option._id)
      );
      form.setFieldValue(
        "check_once",
        item?.poll?.options
          ?.filter((option) => option?.users_id?.includes(currentUser?._id))
          .map((option) => option._id)
      );
    }
    if (pollsRender)
      timeout = setTimeout(() => {
        dispatch(setPollsVote(null));
        clearTimeout(timeout);
      }, 1000);

    return () => clearTimeout(timeout);
  }, [
    dispatch,
    pollsRender,
    isError,
    isMessageSuccess,
    currentUser?._id,
    form,
    item?.poll?.options,
    item?.poll?.end_date,
  ]);

  const showAvatar = (array) => {
    try {
      const voted_array = selectedParticipants?.filter((p) =>
        array?.includes(p._id)
      );
      return voted_array.map((e, k1) => (
        <Tooltip
          placement="topLeft"
          title={getName(e.name, "name")}
          key={`voted_${k1}`}>
          <React.Fragment key={`voted_${k1}`}>
            <AvatarChat
              fontSize="0.4rem"
              type="user"
              hasImage={e.image}
              height={4}
              width={4}
              size={16}
              url={
                e._id === currentUser?._id
                  ? URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                    user?.avatar
                  : URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                    e?.image
              }
              name={getName(e?.name, "avatar")}
            />
          </React.Fragment>
        </Tooltip>
      ));
    } catch (error) {
      return <></>;
    }
  };
  const validForm = (value_params) => {
    form
      .validateFields()
      .then((values) => {
        if (item?.poll?.multi_answer === 0)
          form.setFieldValue("check_once", values.check_once.slice(-1));

        if (
          (item?.poll?.multi_answer === 1 &&
            values.check_multiple.length === 0) ||
          (item?.poll?.multi_answer === 0 &&
            (!values.check_once || values.check_once?.length === 0))
        )
          setShowResultPolls(false);
        handleActionMessage({
          message_id: item?._id,
          params: {
            isMultiple: item?.poll?.multi_answer === 1,
            oldPoll: item?.poll,
            oldValue: value_params,
            value:
              item?.poll?.multi_answer === 0
                ? values.check_once.slice(-1)
                : values.check_multiple,
          },
          type_conversation: selectedConversation?.type,
          type_action: "add_vote",
        });
      })
      .catch(() => {});
  };

  return (
    <div className="flex h-full w-11/12 flex-col  items-start overflow-y-auto p-1 ">
      <div className="mb-1 flex  items-center ">
        <BiPoll className=" text-yellow-400" size={22} />

        <p
          style={{
            wordBreak: "break-word",
          }}
          className=" message font-semibold first-letter:capitalize ">
          {item?.poll?.question}
        </p>
      </div>

      <div className="mt-1 w-full rounded-md  p-2.5 ring-2  ring-gray-200">
        {(!item?.poll?.end_date ||
          (item?.poll?.end_date &&
            moment_timezone().isBefore(
              moment_timezone(item?.poll?.end_date)
            ))) && (
          <>
            <div className="mb-0.5 flex items-center justify-start space-x-1">
              {item?.poll?.multi_answer === 1 ? (
                <>
                  <CheckCircleFilled className="z-0 -mr-2" />
                  <CheckCircleOutlined className="z-10" />
                </>
              ) : (
                <CheckCircleFilled />
              )}
              <Text type="secondary">
                {" "}
                {item?.poll?.multi_answer === 1
                  ? t("chat.polls.multiple_choice")
                  : t("chat.polls.single_choice")}{" "}
              </Text>
            </div>

            <div className="flex w-11/12 flex-col ">
              <Form
                form={form}
                name={"vote" + item?.poll?._id + source}
                onFinish={validForm}
                initialValues={{
                  check_once: item?.poll?.options
                    ?.filter((option) =>
                      option?.users_id?.includes(currentUser?._id)
                    )
                    .map((option) => option._id),
                  check_multiple: item?.poll?.options
                    ?.filter((option) =>
                      option?.users_id?.includes(currentUser?._id)
                    )
                    .map((option) => option._id),
                }}>
                {item?.poll?.multi_answer === 1 ? (
                  <Form.Item name="check_multiple">
                    <Checkbox.Group
                      value={item?.poll?.options?.map((option) => option._id)}
                      className="flex flex-col space-y-2">
                      {item?.poll?.options.map((option) => {
                        return (
                          <Checkbox
                            onChange={(e) => {
                              validForm(e.target.value);
                            }}
                            key={option._id}
                            defaultChecked={option?.users_id?.includes(
                              currentUser?._id
                            )}
                            checked={option?.users_id?.includes(
                              currentUser?._id
                            )}
                            value={option._id}>
                            {option.option}
                          </Checkbox>
                        );
                      })}
                    </Checkbox.Group>
                  </Form.Item>
                ) : (
                  <Form.Item name="check_once">
                    <Checkbox.Group
                      value={
                        item?.poll?.options?.map((option) => option._id) ?? []
                      }
                      className="flex flex-col space-y-2">
                      {item?.poll?.options?.map((option) => {
                        return (
                          <Checkbox
                            onChange={(e) => {
                              validForm(e.target.value);
                            }}
                            key={option._id}
                            checked={option?.users_id?.includes(
                              currentUser?._id
                            )}
                            value={option._id}>
                            {option.option}
                          </Checkbox>
                        );
                      })}
                    </Checkbox.Group>
                  </Form.Item>
                )}
              </Form>
            </div>
            <Divider className="my-2" />

            <div
              className={ClassNames(
                !isBig ? "flex-col" : "flex-row items-center ",
                "flex w-full justify-between  border-gray-200"
              )}>
              {/* {item?.poll?.user_has_answer && ( */}
              <div
                className={ClassNames(
                  " flex w-full flex-1 items-center border-gray-200    text-center"
                )}>
                <Text type="secondary">
                  {" "}
                  {item?.poll?.total_vote +
                    " " +
                    t("chat.polls.voted") +
                    " (" +
                    item?.poll?.users_voted_ids?.length +
                    " users)"}
                </Text>

                {item?.poll?.end_date && (
                  <>
                    {(isBig || source === "main") && (
                      <RxDotFilled className="text-[#808080]" />
                    )}
                    <Text type="secondary">
                      {t("chat.polls.expire") + " "}
                      {moment_timezone(item?.poll?.end_date).fromNow()}{" "}
                    </Text>
                  </>
                )}
              </div>
              {/* )} */}

              <Button
                className="flex w-full flex-1 items-center justify-center"
                //disabled={!item?.poll?.user_has_answer}
                onClick={() => setShowResultPolls((p) => !p)}
                type="link">
                {!showResultPolls
                  ? t("chat.polls.show_result")
                  : t("chat.polls.hide_result")}
              </Button>
            </div>
          </>
        )}
        {(showResultPolls ||
          (item?.poll?.end_date &&
            moment_timezone().isAfter(
              moment_timezone(item?.poll?.end_date)
            ))) && (
          <motion.div
            initial={{
              height: moment_timezone().isAfter(
                moment_timezone(item?.poll?.end_date)
              )
                ? "auto"
                : 0,
              maxHeight: 200,
              opacity: moment_timezone().isAfter(
                moment_timezone(item?.poll?.end_date)
              )
                ? 1
                : 0,
            }}
            animate={
              moment_timezone().isAfter(moment_timezone(item?.poll?.end_date))
                ? undefined
                : {
                    height: "auto",
                    maxHeight: 200,
                    overflow: "auto",
                    opacity: 1,
                  }
            }
            style={{ overflow: "hidden" }}
            transition={{ duration: 0.5 }}
            className=" overflow-y-auto">
            {moment_timezone().isAfter(
              moment_timezone(item?.poll?.end_date)
            ) && (
              <Text type="secondary">
                {t("chat.polls.finished") +
                  moment_timezone(item?.poll?.end_date).fromNow()}
              </Text>
            )}
            {item?.poll?.options?.map((option, key) => (
              <div
                key={`poll_option_${key}`}
                className="flex w-full items-center space-x-2">
                <Text className="w-1/12">
                  {Math.floor(
                    item?.poll?.total_vote > 0
                      ? (option.vote / item?.poll?.total_vote) * 100
                      : "0"
                  ).toFixed(0) + "%"}

                  {" (" + option.vote + ")"}
                </Text>
                <div className="flex w-5/6 flex-col">
                  <div className="flex w-full items-center justify-between">
                    <Title level={5}>{option.option} </Title>
                    {item?.poll?.private === 0 && (
                      <Avatar.Group
                        size="large"
                        className="flex flex-row items-center"
                        maxCount={5}
                        maxPopoverTrigger="hover"
                        maxPopoverPlacement="top"
                        maxStyle={{
                          cursor: "pointer",
                          backgroundColor: "#dbeafe",
                          color: "#1e40af",
                          height: 16,
                          width: 16,
                          display: "flex",
                          flexDirection: "row",
                          alignItems: "center",
                          alignSelf: "center",
                          marginTop: 3,
                        }}>
                        {showAvatar(option.users_id)}
                      </Avatar.Group>
                    )}
                  </div>

                  <Progress
                    className="w-full flex-1"
                    showInfo={false}
                    strokeColor="#1890ff"
                    percent={
                      item?.poll?.total_vote > 0
                        ? (option.vote / item?.poll?.total_vote) * 100
                        : 0
                    }
                    size="small"
                  />
                </div>
              </div>
            ))}
          </motion.div>
        )}
      </div>
    </div>
  );
}

export default PollsMessage;
