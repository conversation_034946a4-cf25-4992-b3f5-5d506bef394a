import {
  CloseOutlined,
  CompressOutlined,
  ExpandOutlined,
  PlusCircleOutlined,
} from "@ant-design/icons";
import { Button, Space, Tabs } from "antd";
import React, { useEffect, useLayoutEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import DrawerList from "components/Chat/Drawer/DrawerList";
import { setOpenDrawer } from "new-redux/actions/chat.actions";
import { getAllPolls, getPolls } from "new-redux/services/chat.services";
import { BiPoll } from "react-icons/bi";
import { CreatePollsComponent } from "components/Chat";
import { motion } from "framer-motion";
import { isGuestConnected } from "utils/role";

function PollsRender() {
  const [t] = useTranslation("common");
  const dispatch = useDispatch();

  const {
    openDrawer,
    allPollList,
    pollList,
    pollListFetched,
    allPollListFetched,
    currentUser,
  } = useSelector((state) => state.chat);
  const sphereUserRole = useSelector((state) => state.user?.user?.role);

  // starred state
  const [currentPagePolls, setCurrentPagePolls] = useState(1);
  const [loadingMorePolls, setLoadingMorePolls] = useState(false);
  const [loadingPolls, setLoadingPolls] = useState(false);
  const [hasMorePolls, setHasMorePolls] = useState(false);
  const [errorPolls, setErrorPolls] = useState(false);
  // AllPolls state
  const [currentPageAllPolls, setCurrentPageAllPolls] = useState(1);
  const [loadingMoreAllPolls, setLoadingMoreAllPolls] = useState(false);
  const [loadingAllPolls, setLoadingAllPolls] = useState(false);
  const [hasMoreAllPolls, setHasMoreAllPolls] = useState(false);
  const [errorAllPolls, setErrorAllPolls] = useState(false);
  const [isOpenPolls, setOpenPolls] = useState(false);
  const [isBig, setIsBig] = useState(false);
  const [position, setPosition] = useState("relative");

  // commun state
  const [tabSelected, setTabSelected] = useState("");

  useEffect(() => {
    if (!tabSelected) return;

    if (!allPollListFetched && tabSelected === "1") {
      dispatch(
        getAllPolls({
          setHasMore: setHasMoreAllPolls,
          setLoadingMore: setLoadingMoreAllPolls,
          currentPage: 1,
          item: openDrawer.external,

          setLoading: setLoadingAllPolls,
          setError: setErrorAllPolls,
        })
      );
    } else if (!pollListFetched && tabSelected === "2") {
      dispatch(
        getPolls({
          setLoadingMore: setLoadingMorePolls,
          setHasMore: setHasMorePolls,
          currentPage: 1,
          item: openDrawer.external,

          setLoading: setLoadingPolls,
          setError: setErrorPolls,
        })
      );
    }
  }, [
    allPollListFetched,
    pollListFetched,
    dispatch,
    tabSelected,
    openDrawer.external,
  ]);
  const items = [
    {
      key: "1",
      label: <span className="text-base">{t("chat.polls.all")}</span>,
      children: (
        <DrawerList
          isBig={isBig}
          source="polls_1"
          tab="1"
          hasGoTo={false}
          error={errorAllPolls}
          data={allPollList}
          loading={loadingAllPolls}
          loadingMore={loadingMoreAllPolls}
          hasMore={hasMoreAllPolls}
          page={currentPageAllPolls}
          reloadFunction={() =>
            dispatch(
              getAllPolls({
                setHasMore: setHasMoreAllPolls,
                setLoadingMore: setLoadingMoreAllPolls,
                currentPage: 1,
                item: openDrawer.external,

                setLoading: setLoadingAllPolls,
                setError: setErrorAllPolls,
              })
            )
          }
          loadMoreFunction={(page) => {
            dispatch(
              getAllPolls({
                setHasMore: setHasMoreAllPolls,
                setLoadingMore: setLoadingMoreAllPolls,
                currentPage: page,
                item: openDrawer.external,

                setLoading: setLoadingAllPolls,
                setError: setErrorAllPolls,
              })
            );
            setCurrentPageAllPolls((p) => p + 1);
          }}
        />
      ),
    },
    {
      key: "2",
      label: <span className="text-base"> {t("chat.polls.incomplete")} </span>,
      children: (
        <DrawerList
          isBig={isBig}
          source="polls_2"
          tab="2"
          hasGoTo={false}
          error={errorPolls}
          data={pollList}
          loading={loadingPolls}
          loadingMore={loadingMorePolls}
          hasMore={hasMorePolls}
          page={currentPagePolls}
          reloadFunction={() =>
            dispatch(
              getPolls({
                setLoadingMore: setLoadingMorePolls,
                setHasMore: setHasMorePolls,
                currentPage: 1,
                item: openDrawer.external,

                setLoading: setLoadingPolls,
                setError: setErrorPolls,
              })
            )
          }
          loadMoreFunction={(page) => {
            dispatch(
              getPolls({
                setHasMore: setHasMorePolls,
                setLoadingMore: setLoadingMorePolls,
                currentPage: page,
                item: openDrawer.external,
                setLoading: setLoadingPolls,
                setError: setErrorPolls,
              })
            );
            setCurrentPagePolls((p) => p + 1);
          }}
        />
      ),
    },
  ];
  useLayoutEffect(() => {
    if (openDrawer.type.includes("polls_"))
      setTabSelected(openDrawer.type?.split("polls_")[1] === "1" ? "1" : "2");
  }, [openDrawer.type]);

  return (
    <motion.div
      animate={{
        width: isBig ? 800 : 384,
        zIndex: 100,
        right: 0,
        position,
      }}
      initial={{
        position: "relative",
      }}
      transition={{
        duration: isBig ? 0.5 : 0.1,
      }}
      style={{
        height: position === "fixed" ? "calc(100% - 57px)" : "100%",
      }}
      className={`  flex flex-col  justify-between overflow-hidden bg-slate-50 pb-1 pt-3  drop-shadow-2xl `}
    >
      <div
        className=" flex items-center justify-between px-2 py-3"
        style={{
          boxShadow: "0 1px 2px 0 rgb(0 0 0 / 0.05)",
        }}
      >
        <h2 className="m-0 ml-3 flex items-center space-x-1 text-lg font-bold text-slate-700">
          <BiPoll />{" "}
          <span className="text-sm">{t("chat.action.list_polls")}</span>
        </h2>
        <Space>
          <Button
            disabled={isGuestConnected(currentUser?.role, sphereUserRole)}
            onClick={() => setOpenPolls(true)}
            type="primary"
            icon={<PlusCircleOutlined />}
          >
            <motion.span
              animate={{
                width: isBig ? 95 : 30,
              }}
              transition={{
                duration: isBig ? 0.5 : 0.1,
              }}
              className="text-xs"
            >
              {t(isBig ? "chat.polls.title_modal" : "chat.create")}
            </motion.span>
          </Button>
          <Button
            onClick={() => {
              let time;
              setIsBig((p) => !p);

              time = setTimeout(
                () => {
                  setPosition((p) => (p === "relative" ? "fixed" : "relative"));
                  clearTimeout(time);
                },
                isBig ? 200 : 0
              );
            }}
            type="text"
            shape="circle"
            size="small"
            icon={!isBig ? <ExpandOutlined /> : <CompressOutlined />}
          />
          <Button
            onClick={() => {
              dispatch(setOpenDrawer({ type: "" }));
            }}
            type="text"
            shape="circle"
            size="small"
            icon={<CloseOutlined />}
          ></Button>
        </Space>
      </div>
      <div className="flex h-full flex-col space-y-1 overflow-y-auto px-4">
        <Tabs
          className="drawer h-full w-full overflow-hidden"
          centered
          defaultActiveKey={
            openDrawer.type?.split("polls_")[1] === "1" ? "1" : "2"
          }
          items={items}
          onChange={(key) => setTabSelected(key)}
        />
      </div>
      {isOpenPolls && (
        <CreatePollsComponent open={isOpenPolls} setOpen={setOpenPolls} />
      )}
    </motion.div>
  );
}

export default PollsRender;
