import React from 'react';
import { Modal, Button, Input } from 'antd';

const HtmlModal = ({ isOpen, onClose, htmlInput, setHtmlInput, onInsert }) => {
  return (
    <Modal
      title="Insérer du HTML personnalisé"
      open={isOpen}
      onCancel={onClose}
      footer={[
        <Button
          key="cancel"
          onClick={onClose}
        >
          Annuler
        </Button>,
        <Button key="insert" type="primary" onClick={onInsert}>
          Insérer HTML
        </Button>,
      ]}
      width={600}
      centered
    >
      <Input.TextArea
        value={htmlInput}
        onChange={(e) => setHtmlInput(e.target.value)}
        rows={8}
        placeholder="Collez votre code HTML ici..."
        style={{ resize: "none" }}
      />
    </Modal>
  );
};

export default HtmlModal;