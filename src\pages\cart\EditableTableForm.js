import { useState } from "react";
import {
  Button,
  Form,
  Space,
  Select,
  Modal,
  InputNumber,
  Typography,
  Divider,
  Input,
} from "antd";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import EditableCartTable from "./EditableCartTable";
import { moment_timezone } from "App";
import { PlusOutlined } from "@ant-design/icons";
import { HighlightSearchW } from "pages/voip/components";
import { doesCartDetailsExist } from "./utils/utilsFunctions";

const EditableTableForm = (props) => {
  const [numberOfRows, setNumberOfRows] = useState(0);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const {
    cartDetails,
    currenciesList,
    setSelectedCurrency,
    productsList,
    discountsList,
    form,
    onFinish,
  } = props;
  const [t] = useTranslation("common");
  const [editingIndex, setEditingIndex] = useState([]);
  const [addingIndex, setaddingIndex] = useState([]);
  const userInfo = useSelector((state) => state?.user);
  const elementName = useSelector(
    (state) => state?.contacts?.contactHeaderInfo?.name
  );
  const [createForm] = Form.useForm();
  const mockData = {
    products: [
      {
        product: "Laptop",
        productDescription:
          "15-inch lightweight laptop with Intel i7 processor and 16GB RAM.",
        qty: 2,
        unitPrice: 1000.0,
        discount: 100.0,
        tax: 180.0,
        amount: 2080.0,
      },
      {
        product: "Mouse",
        productDescription: "Wireless optical mouse with ergonomic design.",
        qty: 5,
        unitPrice: 20.0,
        discount: 0.0,
        tax: 10.0,
        amount: 110.0,
      },
      {
        product: "Keyboard",
        productDescription:
          "Mechanical keyboard with RGB backlight and tactile switches.",
        qty: 3,
        unitPrice: 50.0,
        discount: 15.0,
        tax: 27.0,
        amount: 162.0,
      },
      {
        product: "Monitor",
        productDescription:
          "27-inch 4K UHD monitor with HDR support and adjustable stand.",
        qty: 1,
        unitPrice: 300.0,
        discount: 30.0,
        tax: 54.0,
        amount: 324.0,
      },
    ],
  };
  const [data, setData] = useState(mockData);
  const openCreatModal = () => {
    setIsModalOpen(true);
  };
  const InfoItem = ({ label, value }) => (
    <div className="flex flex-row items-center">
      <label className="text-[#8c8c8c]">{label}: </label>
      <p className="ml-1">{value}</p>
    </div>
  );

  const getCurrencySymbol = () => {
    let currId = form.getFieldValue("selectedCurrency");
    let selectedCurr = currenciesList?.find((item) => item?.value === currId);
    return selectedCurr ? selectedCurr?.symbol : currenciesList[0]?.symbol;
  };

  const handleSearchInCurrencySelect = (input, option) => {
    let labelLower = (option?.label || "").toLowerCase().normalize();
    let currencyLower = (option?.currencyName || "").toLowerCase().normalize();
    let currencySymbol = option?.symbol || "";
    let inputLower = input.toLowerCase().normalize();
    return (
      labelLower.includes(inputLower) ||
      currencyLower.includes(inputLower) ||
      currencySymbol.includes(inputLower)
    );
  };
  const calculateSubtotal = (items) => {
    return items
      .reduce((acc, item) => {
        const lineTotal = item?.qty * item?.unitPrice - item?.discount;
        return acc + lineTotal;
      }, 0)
      .toFixed(2); // Format avec 2 décimales
  };

  return (
    <>
      <Form
        form={form}
        name="dynamic_form_item"
        id="cart-form"
        onValuesChange={(changedValues, allValues) => {
          // setNumberOfRows(allValues?.tableRows?.length);
        }}
        onFinish={(value) => {
          setaddingIndex([]);
          setEditingIndex([]);
          setData((preve) => ({ ...preve, products: [...value.products] }));
        }}
        initialValues={data}
        autoComplete="off"
      >
        <div className="flex w-full flex-row items-start justify-between">
          <div className="flex flex-col pb-8">
            <div>
              <Typography.Title level={4} underline>
                {!doesCartDetailsExist(cartDetails)
                  ? t("cart.createCartTitle", {
                      associatedElement: elementName,
                    })
                  : t("cart.UpdateCartTitle")}
              </Typography.Title>
              {/* {numberOfRows > 0 ? (
                <span>{`(${numberOfRows} Produit)`}</span>
              ) : null} */}
            </div>
            {doesCartDetailsExist(cartDetails) && (
              <Typography.Text type="secondary">
                (Created At:{" "}
                {moment_timezone(cartDetails?.created_at).calendar(null, {
                  sameDay: () => {
                    return `[${t("tasks.today")}] ${
                      userInfo?.user?.location?.time_format
                    }`;
                  },
                  sameElse: () => {
                    return `${userInfo?.user?.location?.date_format}`;
                  },
                })}
                )
              </Typography.Text>
            )}
          </div>
          <Form.Item name="selectedCurrency">
            <Select
              options={currenciesList}
              style={{ width: "100px" }}
              showSearch
              optionLabelProp="label"
              filterOption={(input, option) =>
                handleSearchInCurrencySelect(input, option)
              }
              onChange={(value, option) => {
                setSelectedCurrency(option);
              }}
            />
          </Form.Item>
        </div>
        {data?.products.length > 0 ? (
          <section className="flex items-center justify-center pb-3">
            <InfoItem
              label={t("cart.products", {
                plural: data?.products.length > 1 ? "s" : "",
              })}
              value={data?.products.length}
            />
            <Divider type="vertical" />
            <InfoItem
              label={t("cart.subtotal")}
              value={`${
                form.getFieldValue("products")
                  ? calculateSubtotal(form.getFieldValue("products"))
                  : 0
              }`}
            />
            <Divider type="vertical" />
            <InfoItem label="TVA" value="19%" />
            <Divider type="vertical" />
            <InfoItem
              label={t("cart.total")}
              value={
                form.getFieldValue("products")
                  ? form
                      .getFieldValue("products")
                      .reduce((sum, item) => sum + item?.amount, 0)
                  : "-"
              }
            />
          </section>
        ) : null}
        <Form.List name="products">
          {(products, { add, remove }, { errors }) => {
            return (
              <>
                <EditableCartTable
                  remove={remove}
                  add={add}
                  form={form}
                  rowElement={products}
                  productsList={productsList}
                  discountsList={discountsList}
                  openCreatModal={openCreatModal}
                  setEditingIndex={setEditingIndex}
                  editingIndex={editingIndex}
                  addingIndex={addingIndex}
                  setaddingIndex={setaddingIndex}
                />
                <Form.ErrorList errors={errors} />
              </>
            );
          }}
        </Form.List>

        <div className="right-0 block">
          <div className="float-right space-x-3 ">
            {/* <Button danger>
                {doesCartDetailsExist(cartDetails)
                  ? t("table.delete")
                  : t("localisation.cancel")}
              </Button>*/}
            {(!!addingIndex.length || !!editingIndex.length) && (
              <Button type="primary" htmlType="submit" form="cart-form">
                {t("fields_management.drawerOkBtn")}
              </Button>
            )}
          </div>
        </div>
      </Form>
      <Modal
        title={t("cart.addNewProductBtn")}
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        onOk={() => createForm.submit()}
        //confirmLoading={btnLoading}
      >
        <Form
          form={createForm}
          name="layout-multiple-horizontal"
          autoComplete="off"
          layout="vertical"
          onFinish={onFinish}
        >
          <Space direction="vertical" style={{ width: "100%" }}>
            <Form.Item
              layout="vertical"
              label={t("import.product")}
              name={"product"}
              rules={[{ required: true }]}
            >
              <Select
                placeholder={t("cart.selectProduct")}
                options={productsList}
              />
            </Form.Item>
            <Form.Item name={"productDescription"}>
              <Input.TextArea placeholder={t("unavailability.description")} />
            </Form.Item>
            <Space direction="horizontal" style={{ width: "100%" }}>
              <Form.Item
                layout="vertical"
                label={t("cart.quantity")}
                name={"qty"}
                rules={[{ required: true }]}
              >
                <InputNumber
                  placeholder={t("cart.quantity")}
                  style={{ width: "100%" }}
                  // onKeyPress={preventTypingNonNumeric}
                />
              </Form.Item>
              <Form.Item label={" "} name={"unit"}>
                <Input placeholder={t("cart.unit")} />
              </Form.Item>
            </Space>
            <Form.Item
              layout="vertical"
              label={t("cart.unitPrice")}
              name={"unitPrice"}
              rules={[{ required: true }]}
            >
              <InputNumber
                placeholder={t("cart.unitPrice")}
                style={{ width: "100%" }}
                //onKeyPress={preventTypingNonNumeric}
              />
            </Form.Item>
            <Form.Item
              layout="vertical"
              label={t("cart.discount")}
              name={"discount"}
            >
              <Select
                placeholder={t("cart.discount")}
                style={{ width: "100%" }}
                options={discountsList}
              />
            </Form.Item>
            <Form.Item layout="vertical" label={t("cart.tax")} name={"tax"}>
              <InputNumber
                placeholder={t("cart.tax")}
                style={{ width: "100%" }}
                //  onKeyPress={preventTypingNonNumeric}
              />
            </Form.Item>
          </Space>
        </Form>
      </Modal>
    </>
  );
};

export default EditableTableForm;
