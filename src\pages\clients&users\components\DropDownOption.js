import { useMemo } from "react";
import { useTranslation } from "react-i18next";
//
import { Dropdown } from "antd";
import {
  FiMoreVertical,
  FiEdit,
  FiTrash,
  FiUserPlus,
  FiLink2,
  FiInfo,
} from "react-icons/fi";
import { isGuestConnected, roles } from "../../../utils/role";
import { MdBlockFlipped, MdReplay } from "react-icons/md";
import { ImUnlocked } from "react-icons/im";
import {
  CheckCircleOutlined,
  GlobalOutlined,
  MessageOutlined,
  SwapOutlined,
  SyncOutlined,
  TrademarkOutlined,
} from "@ant-design/icons";
import { RiUserReceivedLine } from "react-icons/ri";
import { TbExchange } from "react-icons/tb";
import { RxReset } from "react-icons/rx";
import { Refs_IDs } from "components/tour/tourConfig";

const DropDownOption = ({
  index,
  elementID,
  elementLabel,
  handleDropDownOptions,
  role,
  familyId,
  etat,
  linkInvitation,
  relation_id,
  canOpenRoom,
  pipelineStage,
  resolvedStage,
  guest,
  folderId,
}) => {
  const [t] = useTranslation("common");
  // const location = useLocation();
  // console.log(process.env.REACT_APP_BRANCH);
  const condition = !roles?.includes(role) && familyId === 4;
  const isGuest = isGuestConnected();
  //
  const items = useMemo(
    () =>
      isGuest && familyId === 6
        ? [
            {
              label: t("voip.moreInfo"),
              key: "moreInfo",
              icon: <FiInfo className="h-4 w-4 text-slate-400" />,
              disabled: condition,
            },
            {
              label: t("menu1.chat"),
              key: "discussion",
              icon: (
                <MessageOutlined
                  className=" text-slate-400"
                  style={{ fontSize: 15 }}
                />
              ),
              disabled: canOpenRoom === 0,
            },
            ...(pipelineStage?.isFinalStage && !resolvedStage
              ? [
                  {
                    label: t("helpDesk.reopenTicket"),
                    key: "reOpen",
                    icon: (
                      <MdReplay
                        className=" text-slate-400"
                        style={{ fontSize: 15 }}
                      />
                    ),
                  },
                ]
              : []),
            {
              label: t("mailing.close"),
              key: "close",
              icon: (
                <CheckCircleOutlined
                  className=" text-slate-400"
                  style={{ fontSize: 15 }}
                />
              ),
              disabled: !resolvedStage,
            },
          ]
        : [
            {
              label: t("contacts.edit"),
              key: "edit",
              icon: <FiEdit className="h-4 w-4 text-slate-400" />,
              disabled: condition,
            },
            ...(familyId === 2
              ? [
                  {
                    label: t("contacts.invite"),
                    key: "inviteGuest",
                    icon: <FiUserPlus className="h-4 w-4 text-slate-400" />,
                    disabled: guest,
                  },
                ]
              : []),
            {
              label: t("voip.moreInfo"),
              key: "moreInfo",
              icon: <FiInfo className="h-4 w-4 text-slate-400" />,

              disabled: condition,
            },
            ...(familyId === 9
              ? [
                  {
                    label: t("mailing.Contact"),
                    key: "convertToContact",
                    icon: (
                      <SyncOutlined
                        className=" text-slate-400"
                        style={{ fontSize: 15 }}
                      />
                    ),
                  },
                ]
              : []),
            ...(familyId !== 4
              ? [
                  {
                    label: t("menu1.chat"),
                    key: "discussion",
                    icon: (
                      <MessageOutlined
                        className=" text-slate-400"
                        style={{ fontSize: 15 }}
                      />
                    ),
                    disabled: canOpenRoom === 0,
                  },
                ]
              : []),
            ...(!relation_id
              ? [
                  {
                    label: `${t("voip.view360")}`,
                    key: "view360-2",
                    icon: (
                      <GlobalOutlined
                        style={{ fontSize: 16 }}
                        className="text-slate-400"
                      />
                    ),
                  },
                ]
              : []),
            ...(familyId === 4
              ? [
                  {
                    label:
                      etat === 2
                        ? t("contacts.re-invited")
                        : t("contacts.invite"),
                    key: "invite",
                    icon: <FiUserPlus className="h-4 w-4 text-slate-400" />,
                    disabled: etat === 4 || etat === 3,
                  },
                  etat === 2 && {
                    label: t("contacts.linkInvitation"),
                    key: "copyInvitation",
                    icon: <FiLink2 className="h-4 w-4 text-slate-400" />,
                    disabled: !linkInvitation,
                  },
                  // ...(etat === 4
                  //   ? [
                  {
                    label: t("contacts.unblock"),
                    // danger: true,
                    key: "unblock",
                    icon: (
                      <ImUnlocked
                        className="h-4 w-4 text-slate-400"
                        // style={{ fontSize: "17px" }}
                      />
                    ),
                    disabled: etat !== 4,
                  },
                  //   ]
                  // : []),
                  { type: "divider" },
                  // ...(etat !== 4 || etat === 2
                  //   ? []
                  //   : [
                  {
                    label: t("contacts.block"),
                    danger: true,
                    key: "block",
                    icon: <MdBlockFlipped className="h-4 w-4 " />,
                    disabled: etat === 1 || etat === 4,
                  },
                  // ]),
                ]
              : []),
            ...(familyId === 6
              ? [
                  {
                    label: t("helpDesk.move"),
                    key: "deplacerByOne",
                    icon: (
                      <TbExchange
                        className="text-slate-400"
                        style={{ fontSize: "16px" }}
                      />
                    ),
                  },
                  {
                    label: t("helpDesk.transfer"),
                    key: "transfer",
                    icon: (
                      <SwapOutlined
                        className="text-slate-400"
                        style={{ fontSize: "16px" }}
                      />
                    ),
                  },
                  {
                    label: t("helpDesk.assign"),
                    key: "assign",
                    icon: (
                      <RiUserReceivedLine className="h-4 w-4 text-slate-400" />
                    ),
                  },
                  {
                    label: t("helpDesk.relaunch"),
                    key: "launch",
                    icon: (
                      <TrademarkOutlined
                        className="text-slate-400"
                        style={{ fontSize: "16px" }}
                      />
                    ),
                  },
                  {
                    label: t("helpDesk.handOver"),
                    key: "remettre",
                    disabled: folderId === 1,
                    icon: (
                      <RxReset
                        className="text-slate-400"
                        style={{ fontSize: "16px" }}
                      />
                    ),
                  },
                ]
              : []),
            ...(pipelineStage?.isFinalStage
              ? [
                  {
                    label: t("helpDesk.reopenTicket"),
                    key: "reOpen",
                    icon: (
                      <MdReplay
                        className=" text-slate-400"
                        style={{ fontSize: 15 }}
                      />
                    ),
                  },
                ]
              : []),

            {
              label: t("contacts.delete"),
              danger: true,
              key: "delete",
              icon: <FiTrash className="h-4 w-4" />,
              disabled: condition || folderId === 3,
            },
          ],
    [
      isGuest,
      familyId,
      t,
      condition,
      canOpenRoom,
      relation_id,
      etat,
      linkInvitation,
      pipelineStage,
    ]
  );
  //
  return (
    <div
      ref={index === 0 ? Refs_IDs.families_dropDown_actions : null}
      className="r-8"
    >
      <Dropdown
        overlayStyle={{ minWidth: items?.length > 2 ? "7rem" : "" }}
        trigger={["click"]}
        placement="bottomRight"
        arrow={false}
        menu={{
          items,
          onClick: (e) =>
            handleDropDownOptions(
              e?.key,
              elementID,
              elementLabel,
              pipelineStage?.pipelineId
            ),
        }}
      >
        <FiMoreVertical className="h-4 w-4 cursor-pointer text-gray-400 hover:text-gray-700" />
      </Dropdown>
    </div>
  );
};

export default DropDownOption;
