import {
  CheckOutlined,
  ClockCircleOutlined,
  CommentOutlined,
  CopyOutlined,
  DeleteOutlined,
  EditOutlined,
  InfoCircleOutlined,
  PlayCircleOutlined,
  RestOutlined,
} from "@ant-design/icons";
import { <PERSON><PERSON>, Button, Divider, Dropdown, Tooltip, Typography } from "antd";
import { Mail } from "lucide-react";
import React, { memo, useState } from "react";
import { useTranslation } from "react-i18next";
import { getTokenRoom } from "../../new-redux/actions/visio.actions/createVisio";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import {
  getDetailsMeet,
  setCountNotificationVisio,
  setDetailsMeet,
  setDetailsMeetExternal,
  setHistory,
  setKeyMeet,
  setLater,
  setListMeet,
  setNow,
} from "../../new-redux/actions/visio.actions/visio";
import ListMeet from "./ListMeet";
import moment from "moment";
import Confirm from "../../components/GenericModal";
import { setOpenTaskRoomDrawer } from "../../new-redux/actions/tasks.actions/handleTaskRoomDrawer";
import MainService from "../../services/main.service";
import TasksRoom from "../tasks/tasksRoom";
import { toastNotification } from "../../components/ToastNotification";
import { FiMoreVertical } from "react-icons/fi";

const ItemVisio = memo(
  ({
    title,
    colorText,
    time = "",
    start_date,
    id,
    room,
    newMeet = false,
    setIdTask,
    owner_id,
    guests,
    creator,
    followers,
    setExterneUpdate,
    setOpenDrawerMsg,
    dataExternal,
    setData,
  }) => {
    const [isCopied, setIsCopied] = useState(false);
    const [showCopy, setShowCopy] = useState(false);
    const [open, setOpen] = useState(false);
    const { isOpen } = useSelector((state) => state.visio);
    const { notificationCount } = useSelector((state) => state.visioList);

    const {
      keyMeet,
      now,
      later,
      listMeet,
      countToday,
      countUpComing,
      countHistory,
    } = useSelector((state) => state.visioList);
    const { user } = useSelector((state) => state.user);
    const dispatch = useDispatch();
    const [t] = useTranslation("common");
    // console.log(keys, id);
    const handleClick = () => {
      dispatch(setKeyMeet(id));
      // dispatch(getDetailsMeet({ keyMeet: id, t }));
      dispatch(setDetailsMeet(dataExternal));
      if (newMeet) {
        const currentDate = moment().format("YYYY-MM-DD");
        const startDate = moment(start_date, "YYYY-MM-DD");
        dispatch(
          setListMeet(
            listMeet.map((el) =>
              el.id === id ? { ...el, newMeet: false } : el
            )
          )
        );
        if (start_date === currentDate && now > 0) {
          dispatch(setNow({ now: now - 1, countToday: countToday }));
        }

        if (startDate.isAfter(currentDate, "day") && later > 0) {
          dispatch(
            setLater({ later: later - 1, countUpComing: countUpComing })
          );
        }
      }
      if (dataExternal.read == 0) {
        if (dataExternal.idLog)
          MainService.markNotificationAsRead({ log_id: dataExternal.idLog });
        else
          MainService.markNotificationAsRead({
            action: dataExternal.action[0],
            task_id: dataExternal.id,
          });

        dispatch(
          setListMeet(
            listMeet.map((el) =>
              el.id === dataExternal.id ? { ...el, read: 1 } : el
            )
          )
        );
        if (notificationCount > 0)
          dispatch(setCountNotificationVisio(notificationCount - 1));
      }
    };

    return (
      <div
        className=" flex-grow cursor-pointer   space-y-1 2xl:flex  2xl:items-center 2xl:space-x-4"
        onClick={handleClick}>
        {/* <div>{icon}</div> */}
        <div className="flex w-full flex-col">
          <div className="flex w-full  items-center justify-between space-x-1">
            <Typography.Title
              level={5}
              className={` w-full ${colorText}`}
              ellipsis={true}>
              {title}
            </Typography.Title>
            <div>{dataExternal.read == 0 ? <Badge color={"blue"} /> : ""}</div>
          </div>

          <Typography.Text
            className={`whitespace-normal text-xs  ${colorText}`}
            ellipsis={true}
            type="secondary">
            {time}
          </Typography.Text>
        </div>
      </div>
    );
  }
);

export default ItemVisio;
