import React, { useEffect, useState } from "react";
import { Card, Col, Row, Select } from "antd";
import {
  TaskStatusRadial<PERSON>hart,
  Chart2Bars,
  BrowserMarketShare<PERSON>hart,
  <PERSON><PERSON>hart,
  <PERSON>ut<PERSON>hart2,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  <PERSON><PERSON>hartWithLegend,
} from "../ChartsDashboard";
import { generateAxios } from "services/axiosInstance";
import { URL_ENV } from "index";
import CardStat from "../CardStat";

// Centralisation des endpoints pour éviter les erreurs
const KPI_ENDPOINTS = {
  OVERDUE_COMPLETED: "/kpi-tasks/overdue-completed",
  PIPELINE_STAGE: "/kpi-tasks/pipeline-stage",
  TYPES: "/kpi-tasks/types",
  GUESTS_TYPE: "/kpi-tasks/guests-type",
  TYPES_OVERDUE_COMPLETED: "/kpi-tasks/types-overdue-completed",
  TYPES_PRIORITY: "/kpi-tasks/types-priority",
  EVOLUTION_VISIO: "/kpi-tasks/evolution-visio",
};

// Fonction utilitaire pour l'appel d'API
const fetchApiData = async (endpoint, body, setStateCallback) => {
  try {
    const res = await generateAxios(
      `${URL_ENV?.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
    ).post(endpoint, body);
    setStateCallback(res.data);
  } catch (err) {
    console.error(`Error fetching data from ${endpoint}:`, err);
  }
};

const DashboardTasks = ({ start, end, from = "" }) => {
  // État pour chaque appel API
  const [dataTasks, setDataTasks] = useState({});
  const [data, setData] = useState({ categories: [], data: [], name: "" });
  const [types, setTypes] = useState([]);
  const [totalTypes, setTotalTypes] = useState(0);
  const [name, setName] = useState("");
  const [categoriesTasks, setCategoriesTasks] = useState([]);
  const [categoriesTypesTasks, setCategoriesTypesTasks] = useState([]);
  const [tasksPriorities, setTasksPriorities] = useState([]);
  const [pipelines, setPipelines] = useState([]);
  const [selectedPipeline, setSelectedPipeline] = useState("");
  const [dataEvolutionVisio, setDataEvolutionVisio] = useState({});

  const [parentName, setParentName] = useState("");
  const [childName, setChildName] = useState("");

  // Fetch overdue-completed data
  useEffect(() => {
    fetchApiData(KPI_ENDPOINTS.OVERDUE_COMPLETED, { start, end }, (data) => {
      setDataTasks(data);
      // setParentName(data.parent_name);
      // setChildName(data.child_name);
    });
  }, [start, end]);

  // Fetch pipeline-stage data
  useEffect(() => {
    const getPipelines = async () => {
      try {
        let pipelines = [];

        // Récupérer les pipelines uniquement si "selectedPipeline" est vide
        if (!selectedPipeline) {
          const res = await generateAxios(
            URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
          ).get(`/pipelines/pipelines-by-module-system/task`);
          pipelines = res.data.data;

          // Mettre à jour l'état des pipelines
          setPipelines(
            pipelines.map((el) => ({ value: el.id, label: el.label }))
          );

          // Sélectionner automatiquement le premier pipeline si aucun n'est sélectionné
          if (pipelines.length > 0) {
            setSelectedPipeline(pipelines[0].id);
          }
        }

        // Déterminer quel pipeline utiliser pour appeler l'API
        const pipelineId = selectedPipeline;

        if (pipelineId) {
          // Appeler l'API pour récupérer les données du pipeline
          await fetchApiData(
            `${KPI_ENDPOINTS.PIPELINE_STAGE}/${pipelineId}`,
            { start, end },
            setData
          );
        }
      } catch (err) {
        console.error(
          "Erreur lors de la récupération des pipelines ou des données :",
          err
        );
      }
    };

    getPipelines();
  }, [start, end, selectedPipeline]);

  // Fetch types data
  useEffect(() => {
    fetchApiData(KPI_ENDPOINTS.TYPES, { start, end }, (data) => {
      setTypes(data.data);
      setTotalTypes(data.total);
      setName(data.name);
    });
  }, [start, end]);

  // Fetch guests-type data
  useEffect(() => {
    fetchApiData(KPI_ENDPOINTS.GUESTS_TYPE, { start, end }, setCategoriesTasks);
  }, [start, end]);

  // Fetch types-overdue-completed data
  useEffect(() => {
    fetchApiData(
      KPI_ENDPOINTS.TYPES_OVERDUE_COMPLETED,
      { start, end },
      setCategoriesTypesTasks
    );
  }, [start, end]);

  // Fetch types-priority data
  useEffect(() => {
    fetchApiData(
      KPI_ENDPOINTS.TYPES_PRIORITY,
      { start, end },
      setTasksPriorities
    );
  }, [start, end]);
  useEffect(() => {
    fetchApiData(
      KPI_ENDPOINTS.EVOLUTION_VISIO,
      { start, end },
      setDataEvolutionVisio
    );
  }, [start, end]);
  return (
    <Row gutter={[16, 16]}>
      {/* Task Status Radial Chart */}
      <Col className="gutter-row" span={from === "drawer" ? 12 : 6}>
        <CardStat title={dataTasks?.name}>
          <DonutChart2
            data={dataTasks?.data}
            total={dataTasks?.total}
            name=""
          />
        </CardStat>
      </Col>
      <Col className="gutter-row" span={from === "drawer" ? 12 : 7}>
        <CardStat title={categoriesTasks?.name}>
          <PieChartWithLegend
            data={{ ...categoriesTasks, name: "" }}
            total={totalTypes}
            name=""
          />
        </CardStat>
      </Col>

      {/* Chart 2 Bars */}
      <Col className="gutter-row" span={from === "drawer" ? 12 : 11}>
        <CardStat title={categoriesTypesTasks?.name}>
          <Chart2Bars data={{ ...categoriesTypesTasks, name: "" }} />
        </CardStat>
      </Col>

      {/* Browser Market Share Chart */}
      <Col
        className="gutter-row"
        span={from === "drawer" ? 12 : 10}
        style={{ background: "white", padding: 0 }}
      >
        <CardStat
          title={data?.name}
          extra={
            <Select
              popupMatchSelectWidth={false}
              showSearch
              filterOption={(input, option) =>
                (option?.label ?? "")
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
              onChange={(value) => setSelectedPipeline(value)}
              value={selectedPipeline}
              options={pipelines}
            />
          }
        >
          <BrowserMarketShareChart
            hasSelect={false}
            allData={{ ...data, name: "" }}
            parentName={parentName}
            childName={childName}
          />
        </CardStat>
      </Col>

      {/* Pie Chart */}

      {/* Donut Chart */}
      <Col className="gutter-row" span={from === "drawer" ? 12 : 14}>
        <CardStat title={name}>
          <DonutChart2 data={types} total={totalTypes} name={""} />
        </CardStat>
      </Col>

      {/* Trophies Chart */}
      <Col className="gutter-row" span={from === "drawer" ? 12 : 12}>
        <CardStat title={tasksPriorities?.name}>
          <TrophiesChart data={{ ...tasksPriorities, name: "" }} />
        </CardStat>
      </Col>
      <Col className="gutter-row" span={from === "drawer" ? 12 : 12}>
        <CardStat title={dataEvolutionVisio?.name}>
          <EvolutionChart data={{ ...dataEvolutionVisio, name: "" }} />
        </CardStat>
      </Col>
    </Row>
  );
};

export default DashboardTasks;
