import React, { useState } from "react";
import ModalConfirm from "./ModalConfirm";
import { useTranslation } from "react-i18next";

const ModalArchive = ({
  title,
  content,
  okText,
  openModal,
  setOpenModal,
  action,
}) => {
  const [loading, setLoading] = useState(false);
  const [t] = useTranslation("common");

  const handleOk = async () => {
    setLoading(true);
    await action();
    setLoading(false);
    setOpenModal(false);
  };

  const handleCancel = () => {
    setOpenModal(false);
  };

  return (
    <ModalConfirm
      open={openModal}
      title={title}
      content={
        <div className="w-full">
          {content}
        </div>
      }
      dangerMode={true}
      okText={okText}
      cancelText={t("form.cancel")}
      onOk={handleOk}
      onCancel={handleCancel}
      loading={loading}
    />
  );
};

export default ModalArchive;
