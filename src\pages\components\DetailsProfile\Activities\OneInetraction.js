import { AnimatePresence } from "framer-motion";
import React, { useRef, useState } from "react";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { isImagePath } from "./ChangesInteractions";
import { Button, Image, Typography } from "antd";
import { URL_ENV } from "index";

const OneInetraction = ({ index, keyValue, value }) => {
  const [expanded, setExpanded] = useState(false);
  const [t] = useTranslation("common");
  const liRef = useRef(null);
  const textRef = useRef(null);
  const isHtml = (str) => {
    const doc = new DOMParser().parseFromString(str, "text/html");
    return Array.from(doc.body.childNodes).some((node) => node.nodeType === 1);
  };

  const renderContent = (textWithHtml) => {
    if (isHtml(textWithHtml)) {
      return <span dangerouslySetInnerHTML={{ __html: textWithHtml }} />;
    } else {
      return textWithHtml;
    }
  };

  return (
    <li
      key={`${value?.join("")}-${index}-${keyValue}`}
      ref={liRef}
      className={`flex  items-center overflow-hidden ${
        !expanded ? "gap-x-1 whitespace-nowrap" : "whitespace-normal"
      } `}
      style={{ width: `calc(100vw - ${expanded ? 500 : 480}px ` }}
    >
      <span
        className={`w-max ${!expanded ? "truncate" : ""} leading-[33px]`}
        ref={textRef}
      >
        <span className="mr-1 text-sm font-semibold">{keyValue} : </span>
        {t("vue360.from").toUpperCase()}{" "}
        {typeof value[0] === "string" && value[0]?.length > 0 ? (
          <>
            <span className="mx-1 items-center rounded-md bg-gray-100 p-1 font-medium">
              <AnimatePresence>
                <motion.span
                  initial={{ opacity: 0, height: 0 }}
                  animate={{
                    opacity: 1,
                    height: "auto",
                    transition: { duration: 0.2 },
                    fontSize: "12px",
                    fontWeight: 500,
                  }}
                  exit={{
                    opacity: 0,
                    height: 0,
                    transition: { duration: 0.2 },
                  }}
                >
                  {isImagePath(value[0]) ? (
                    <Image
                      src={
                        URL_ENV?.REACT_APP_BASE_URL +
                        URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                        value[0]
                      }
                      style={{ width: 35, height: 35 }}
                    />
                  ) : (
                    renderContent(
                      // expanded[keyValue]
                      //   ? value[0]
                      //   :
                      value[0]
                    )
                  )}
                  {/* {!isImagePath(value[0]) &&
                  value[0].length > 33 &&
                  // !expanded[keyValue] &&
                  "..."} */}
                </motion.span>
              </AnimatePresence>
            </span>
          </>
        ) : (
          <span className="mx-1 items-center rounded-md bg-gray-100 p-1 font-medium">
            {t("vue360.empty")}{" "}
          </span>
        )}
        {t("vue360.to").toUpperCase()}{" "}
        {typeof value[1] === "string" && value[1]?.length > 0 ? (
          <>
            <span className="ml-1 items-center rounded-md bg-sky-100 p-1 font-medium">
              <AnimatePresence>
                <motion.span
                  initial={{ opacity: 0, height: 0 }}
                  animate={{
                    opacity: 1,
                    height: "auto",
                    transition: { duration: 0.2 },
                    fontSize: "12px",
                    fontWeight: 500,
                  }}
                  exit={{
                    opacity: 0,
                    height: 0,
                    transition: { duration: 0.2 },
                  }}
                >
                  {isImagePath(value[1]) ? (
                    <Image
                      src={
                        URL_ENV?.REACT_APP_BASE_URL +
                        URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                        value[1]
                      }
                      style={{ width: 35, height: 35 }}
                    />
                  ) : (
                    renderContent(
                      // expanded[key]
                      //   ? value[1]
                      //   :
                      value[1]
                    )
                  )}
                </motion.span>
              </AnimatePresence>
            </span>
          </>
        ) : (
          <span className="ml-1 items-center rounded-md bg-sky-100 p-1 font-medium">
            {t("vue360.empty")}
          </span>
        )}
        {typeof liRef?.current?.clientWidth === "number" &&
          typeof textRef?.current?.clientWidth === "number" &&
          liRef?.current?.clientWidth - textRef?.current?.clientWidth < 151 &&
          expanded && (
            <Typography.Text
              className="ml-1 cursor-pointer text-gray-400 hover:text-gray-500"
              onClick={() => setExpanded(!expanded)}
            >
              {expanded ? t("vue360.showLess") : t("vue360.showMore")}
            </Typography.Text>
          )}
      </span>
      {typeof liRef?.current?.clientWidth === "number" &&
        typeof textRef?.current?.clientWidth === "number" &&
        liRef?.current?.clientWidth - textRef?.current?.clientWidth < 151 &&
        !expanded && (
          <Typography.Text
            className="cursor-pointer text-gray-400  hover:text-gray-500"
            onClick={() => setExpanded(!expanded)}
          >
            {expanded ? t("vue360.showLess") : t("vue360.showMore")}
          </Typography.Text>
        )}
    </li>
  );
};

export default OneInetraction;
