{"languageEnglish": "English", "languageFrench": "French", "404Subtitle": "Sorry, the page you visited does not exist.", "404": "Not Found", "unauthorized": "Unauthorized", "logout": "Logout", "unauthorizedSubtitle": "Sorry, you are not authorized to access this page.", "BackToHome": "Back Home", "supportContact": "If the problem persists, please contact  sphere's support.", "updateElement": "Update", "errorOccurred": "An error has occurred", "menu1": {"dashboard": "Home", "chat": "Discussions", "voip": "VoIP", "callLog": "Call Log", "company_directory": "Company Directory", "visio": "Visio", "notes": "Notes", "rmc": "Multi-canal", "contacts": "Contacts", "settings": "Setting", "notifications": "Notifications", "stats": "Stats", "mailing": "Email", "tasks": "Activities", "import": "Import", "profile": "Profile", "products": "Products", "deals": "Deals", "tickets": "Tickets", "projects": "Projects", "family": "{{ family }}", "pre-meet": "Pre-Meet", "booking": "Bookings", "leads": "Leads", "companies": "Organizations", "task": "Activity", "email": "Email", "invoices": "Invoices", "interactions": "Interactions", "appMobile": "Mobile application", "manage": "Manage {{module}} ", "my_charts": "My stats", "scanMobileApp": "Scan to get Comunik unified", "drive": "Drive"}, "menu2": {"general": "General", "fields": "Fields", "organisation": "Fields/Organisation", "contact": "Fields/Contact", "deal": "Fields/Deal", "deals": "Pipeline/Deals", "leads": "Fields/Leads", "booking": "Fields/Booking", "localisation": "Localization", "activities": "Activities", "rmcSettings": "RMC Settings", "inboxsettings": "Inbox Settings", "outboxsettings": "Outbox Settings", "contacts": "Contacts", "contacts-types": "Contact types", "companies": "General/{{company}}", "guestQueue": "General/Guest queue", "companiesOnly": "Organizations", "colleagues": "Colleagues", "groups": "Groups", "prospects": "Prospects", "logs": "Telephony", "messaging": "Voicemail", "phoneBook": "Phone Book", "phone-book": "Phone Book", "statsRmc": "Stats RMC", "tags": "Tags", "activity": "Activities", "importOrganismes": "Companies", "importContacts": "Contacts", "user": "Fields/User", "users": "Users", "user-view": "User View", "manageaccount": "Manage Account", "emailaccounts": "Email Accounts", "inbox": "Inbox", "sent": "<PERSON><PERSON>", "drafts": "Drafts", "starred": "Starred", "trash": "Trash", "pipeline": "Pipelines", "product": "Fields/Product", "products": "Products", "helpdesk": "Fields/HelpDesk", "helpdeskWithoutFields": "Helpdesk", "departments": "General/Departments", "services": "General/Services", "channels": "General/Channels", "folders": "Folders", "wiki": "Wiki", "ticket": "Tickets", "tickets": "Pipeline/Tickets", "severities": "Ticket/Severities", "severity": "Severities", "projects": "Pipeline/Projects", "project": "Projects", "general_info": "General", "security": "Security", "localization": "Localization", "family": "Families", "sales": "Transactions", "role": "Users/Roles", "team": "Users/Teams", "import": "Import", "sla": "Ticket/SLA", "levels": "Ticket/Level", "subjects": "Ticket/Subject", "wiki-docs": "Wiki/Documents", "wiki-config": "Wiki/Groups", "usersWithoutFields": "Users", "pipelineDeals": "Pipeline/Deals", "type": "Types", "unity": "Unity", "types": "Types", "typesContacts": "Contact types", "typesCompanies": "Company types ", "countries": "General/Countries", "currencies": "Sales/Currencies", "tasks": "Activities", "stats-task": "Activity", "stats-chat": "Cha<PERSON>", "stats-voip": "Voip", "checklist": "Checklist", "visio": "Visio", "triggers": "Triggers", "allNotifications": "All Notifications", "notificationsManagement": "Chat notification management", "all": "All Notifications", "management": "Chat notification management", "log-action": "Logs", "tour": "Tour", "dashboard": "Home", "unavailability": "Unavailability", "notification": "Notifications management", "emailTemplates": "Templates", "discount": "Discount", "invoices": "Invoices", "expandMenu": "Expand Menu", "hideMenu": "<PERSON><PERSON>", "integrations": "Integrations", "transactions": "Transactions", "policies": "Policies"}, "table": {"search": "Enter a character to search.", "search3": "Enter at least 3 characters...", "startDate": "Start Date", "endDate": "End Date", "createContact": "Create Contact", "edit": "Edit", "delete": "Delete", "primaryFieldTooltip": "Primary Field", "header": {"type": "Type", "caller": "Caller", "when": "When", "duration": "Duration", "contacts": "Contacts", "name": "Name", "selectRangeDate": "Select Range Date", "description": "Description", "uploadImage": "Upload Image", "pricingAmount": "Pricing Amount", "source": "Source", "rank": "Rank", "title": "Title", "hidden": "Hidden", "uniqueValue": "Unique", "required": "Required", "isrequired": "is required !", "actions": "Actions", "department": "Department", "departments": "Departments", "percentage": "Win (%)", "currency": "<PERSON><PERSON><PERSON><PERSON>", "flag": "Flag", "timezone": "Timezone", "prefix": "Prefix", "region": "Continent", "country": "Country", "extra": "Extra configuration"}}, "localisation": {"timeFormat": "Time Format", "dateFormat": "Date Format", "weekStartsOn": "Week Starts On", "defaultLanguage": "Default Language", "defaultTimezone": "Default Timezone", "submit": "Submit", "sumbit2": "Submit", "reset": "Reset", "cancel": "Cancel", "monday": "Monday", "sunday": "Sunday", "saturday": "Saturday", "french": "French", "english": "English", "dialCode": "Dial code", "wednesday": "Wednesday", "january": "January", "noConfig": "There is no location configuration available.", "titleChangeLangue": "Change Language", "contentChangeLangue": "Your default language is currently set to <strong>{{lang}}.</strong> Would you like to switch it, as specified in the authentication application?"}, "sales": {"addReasonToconclude": "Add reason to conclude", "addReasonforLoss": "Add reason for Loss", "ReasonforLoss": "Reason for Loss", "ReasonToconclude": "Reason to conclude", "reason": "Reason", "defaultcurrency": "<PERSON><PERSON><PERSON>", "used": "Used", "unused": "Unused", "currencies": "Currencies", "symbol": "Symbol", "successReason": "Won", "failReason": "Lost", "selectReasonTitle": "Reason for the action on this deal", "failStatus": "Lost", "successStatus": "Won", "normalStatus": "In Progress"}, "activities": {"createActivity": "Add activity type", "id": "Id", "label": "Name", "icons": "Icon", "search": "Search", "reset": "Reset", "filter": "Filter", "close": "Close", "createActivityType": "Add activity type", "editActivityType": "Edit activity type", "name": "Name", "icon": "Icon", "color": "Color", "iconTaked": "The icon has been taked", "oK": "OK", "cancel": "Cancel", "typefamilyproduct_id": "Type", "companies": "Companies", "activitiesTypes": "Types", "activitiesPipelines": "Pipelines"}, "emailAccounts": {"accountType": "Account Type", "account": "Category", "primaryAccount": "Primary", "disableSync": "Sync", "synchronous": "Synchronous", "asynchronous": "Asynchronous", "syncEmailsFrom": "Sync emails from", "createContactRecord": "Create Lead record if record does not exist", "password": "Password", "userName": "Username", "createEmailAccount": "Create <PERSON><PERSON> Account", "updateEmailAccount": "Update Email Account", "incomingMail": "Incoming Mail (IMAP)", "server": "Hostname", "port": "Port", "encryption": "Encryption", "outgoingMail": "Outgoing Mail (SMTP)", "allownon-secure": "Allow non-secure certificate", "shared": "Shared", "personal": "Personal", "errEmail": "The entered value is not valid Email!", "oK": "OK", "cancel": "Cancel", "connectSharedAccount": "Connect Shared Account", "connectpersonnalEmailAccount": "Connect personnal email account", "connectAccount": "Create <PERSON><PERSON> Account", "now": "Today", "notSync": "No synchronization", "1monthago": "1 Month ago", "1weekago": "1 Week ago", "3monthago": "3 Month ago", "6monthago": "6 Month ago", "selectAccountType": "Select Account type", "defaultSignature": "The default signature", "infoTest": "Before sending the configuration data, you need to click on 'Check'", "unassignedMail": "Total unassigned mail", "myChargeMail": "Number of mail in my charge", "ProcessedAndClosedMail": "Number of mail in my charge and processed (processed and closed)", "progressMail": "Number of mail in my charge and in progress", "processedMail": "Number of mail in my charge to be processed (new)", "out-of-timeMails": "Number of out-of-time mails", "displayIndicators": "Config display indicators on banner", "tooltipBanner": "These Key Performance Indicators (KPIs) will be displayed in a shared email inbox.", "convertMailToTicket": "Convert received mail to ticket", "mailToTicket": "Mail to ticket", "successVerification": "The verification has been successfully completed", "failedVerification": "The configuration failed. Please check your settings", "check": "Check", "defaultDepartment": "Default department", "autoAffectAgent": "Automatic assignment of agents upon receiving emails", "defaultDepartmentInfo": "This department is selected by default when no subject is chosen or when the subject does not include a department", "useSameNamePassword": "Use the username and password of Incoming Mail (IMAP) for Outgoing Mail (SMTP)"}, "tags": {"createTag": "Add Tag", "id": "Id", "label": "Label", "search": "Search", "reset": "Reset", "filter": "Filter", "close": "Close", "editTag": "Edit Tag", "name": "Name", "icon": "Icon", "color": "Color", "withAtypeOfActivity": "With a type of activity", "withOutTypeOfActivity": "without type of activity", "typeTask": "Type of activity triggered", "ListOfCategories": "Categories", "oK": "OK", "cancel": "Cancel", "selectcolor": "Select color", "selecttypeactivity": "Select type of activity", "selectcategories": "Select modules", "selectUsers": "Select users", "selecticon": "Select icon", "typetask_id": "activity type", "users": "Users", "Families": "Families", "selectFamily": "Select family"}, "channels": {"label": "Name", "icon": "Icon"}, "page": {"underConstruction": "This page is under construction."}, "toasts": {"fieldCreated": "Field was successfully created", "groupCreated": "Group was successfully created", "groupUpdated": "Group was successfully updated", "fieldUpdated": "Field was successfully updated", "usedField": "This field cannot be deleted! It's already used!", "fieldDeleted": "Field was successfully deleted", "taskDeleted": "Activity was successfully deleted", "groupDeleted": "Group was successfully deleted", "optionValueDeleted": "Option value was successfully deleted", "taskUpdated": "Activity was successfully updated", "somethingWrong": "Something Went Wrong, Please Try Again", "deleted": " deleted successfully ", "created": " created successfully ", "updated": "updated successfully", "edit": " edited successfully ", "error": "Error", "rankChanged": "Rank Changed", "stageChanged": "Stage Changed", "rankFailed": "Rank Failed", "fieldParamSuccess": "Field parameter was updated successfully", "success": "Success", "companyDeleted": "Company Successfully Deleted", "companiesDeleted": "Companies Successfully Deleted", "contactCreated": "Contact Successfully Created", "contactUpdated": "Contact Successfully Updated", "contactDeleted": "Contact Successfully Deleted", "contactsDeleted": "Contacts Successfully Deleted", "formFailed": "Form validation failed, check all fields", "recordUpdated": "Record successfully updated", "ownerUpdated": "{{label}} has been affected to a new owner", "networkError": "Network Error", "networkErrorDescription": "You are currently offline.", "networkErrorSendMessage": "You can't send message while you are offline.", "re-connection": "Re-connection in progress...", "mercureConnectionDescription": "try to reconnect,  please wait... ", "errorFetchApi": "Something Went Wrong, Please Try Again", "errorRoomNotFound": "Room Visio Not Found", "associationDone": "Association is Done", "associationFailed": "Association Failed", "dissociationDone": "Dissociation is Done", "dissociationFailed": "Dissociation Failed", "deleteFailed": "Delete Failed", "contactAlreadyAssociated": "This contact is already associated", "actionFailed": "Action Failed", "customError": "{{error}}", "callForwardingActive": "Active Call forwarding", "module used you cannot deleted": "Module used you cannot deleted !", "primaryCurrency": " has become the default currency", "used": " is used", "notUsed": " is no longer used", "primaryCompany": " has become the primary company", "primaryMail": " has become the primary mail", "sync": " has become synchronized", "notSync": " is no longer synchronized", "connectionSuccess": "Connection established successfully", "existingFieldLabelError": "Field label already exists", "networkStatusGood": "Network is good.", "networkStatusModerate": "Network is moderate.", "networkStatusPoor": "Network is poor.", "networkStatusPoorDescription": "Your network is poor, please check your connection. Please refresh the page by clicking ", "networkStatusOffline": "You are currently offline.", "duplicatedDataError": "Duplicated Data Found!!", "duplicatedGrpNameError": "Group name already exists", "reconnect": "Login session timeout please reconnect", "addTaskNotification": "<strong>{{user}}</strong> added you as <strong>{{role}}</strong> in new activity <strong>{{label}}</strong>", "updateTaskNotification": "<strong>{{user}}</strong> has updated the activity <strong>{{label}}</strong>", "updateRoleTaskNotification": "<strong>{{user}}</strong> has updated your role to <strong>{{role}}</strong> in the activity <strong>{{label}}</strong>", "deleteTaskNotification": "<strong>{{user}}</strong> deleted an activity.", "updateTaskpriorityNotification": "<strong>{{user}}</strong> updated the priority of the activity <strong>{{label}}</strong>", "updateTaskListNotification": "<strong>{{user}}</strong> has updated the list <strong>{{labelList}}</strong> of the activity <strong>{{label}}</strong>", "meUpdateTaskListNotification": "<strong>You</strong> have updated the list <strong>{{labelList}}</strong> of the activity <strong>{{label}}</strong>", "updateNameTaskListNotification": "<strong>{{user}}</strong> has renamed a list from <strong>{{oldName}} to {{newName}}</strong> for the activity <strong>{{label}}</strong>", "meUpdateNameTaskListNotification": "<strong>You</strong> have renamed a list from <strong>{{oldName}} to {{newName}}</strong> for the activity <strong>{{label}}</strong>", "createTaskListNotification": "<strong>{{user}}</strong> has created a new list <strong>{{labelList}}</strong> in the activity <strong>{{label}}</strong>", "meCreateTaskListNotification": "<strong>You</strong> have created a new list <strong>{{labelList}}</strong> in the activity <strong>{{label}}</strong>", "deleteTaskListNotification": "<strong>{{user}}</strong> has deleted the list <strong>{{labelList}}</strong> of the activity <strong>{{label}}</strong>", "meDeleteTaskListNotification": "<strong>You</strong> have deleted the list <strong>{{labelList}}</strong> from the activity <strong>{{label}}</strong>", "deleteItemInTaskListNotification": "<strong>{{user}}</strong> has deleted the item <strong>{{labelItem}}</strong> from the list <strong>{{labelList}}</strong> of the activity <strong>{{label}}</strong>", "meDeleteItemInTaskListNotification": "<strong>You</strong> have deleted the item <strong>{{labelItem}}</strong> from the list <strong>{{labelList}}</strong> in the activity <strong>{{label}}</strong>", "updateRankaskListNotification": "<strong>{{user}}</strong> have updated the ranks of the items in the list <strong>{{labelList}}</strong> of the activity <strong>{{label}}</strong>", "meUpdateRankTaskListNotification": "<strong>You</strong> has updated the ranks of the items in the list <strong>{{labelList}}</strong> of the activity <strong>{{label}}</strong>", "taskReminderNotification": "<strong>{{task<PERSON><PERSON><PERSON>}}</strong> activity reminder", "updateTaskDatesNotification": "<strong>{{user}}</strong> has changed the schedule of the activity <strong>{{label}}</strong>", "updateTaskStageNotification": "<strong>{{user}}</strong> has changed the stage of the activity <strong>{{label}}</strong>", "updateTaskNoteNotification": "<strong>{{user}}</strong> updated a <strong>{{noteDescription}}</strong> in the activity <strong>{{label}}</strong>", "statusChanged": "Status changed successfully", "mercureReminder": "Activity {{<PERSON><PERSON><PERSON><PERSON>}} starts in {{time}}", "error_status": "You can't call this number, please check the number and try again", "messageNotSent": "the message is not sent", "indisponibilite": "Unavailability", "discounts": "Discount", "taxes": "Tax", "cannotDeleteStage": "Stage used you cannot deleted !", "searchInfo": "Search by any column", "groupe-wikis": "Wiki group", "updateActivityLabel": "<strong>{{user}}</strong> has updated the label to <strong>{{newLabel}}</strong>", "updateActivityType": "<strong>{{user}}</strong> has updated the type of <strong>{{label}}</strong> from <strong>{{oldType}}</strong> to <strong>{{newType}}</strong>", "updateActivityElement": "<strong>{{user}}</strong> associated the element <strong>{{element}}</strong> from the module <strong>{{module}}</strong> to activity <strong>{{label}}</strong>", "newEmail": "You have received a new email from <strong>{{from}}</strong> in the <strong>{{to}}</strong> inbox", "updateReminder": "<strong>{{user}}</strong> has updated the reminder of the activity <strong>{{label}}</strong>", "addFilesInActivity": "<strong>{{user}}</strong> has added attachments in the activity <strong>{{label}}</strong>", "removeFilesInActivity": "<strong>{{user}}</strong> has removed an attachment from the activity <strong>{{label}}</strong>", "driveItemShared": "<strong>{{user}}</strong> shared a {{itemType}} <strong>{{itemName}}</strong> with you", "driveItemUnshared": "<strong>{{user}}</strong> unshared a {{itemType}} <strong>{{itemName}}</strong> from you", "driveItemTrashed": "<strong>{{user}}</strong> moved {{itemType}} <strong>{{itemName}}</strong> to trash", "driveItemMoved": "<strong>{{user}}</strong> moved {{itemType}} <strong>{{itemName}}</strong> to a different location", "driveItemRenamed": "<strong>{{user}}</strong> renamed {{itemType}} <strong>{{itemName}}</strong>", "reminderTooltip": "<p>If you leave this field empty, you won't be reminded before the beginning of this activity</p><strong>P.S: This field accepts only numbers</strong>", "currentUserPronoun": "You", "external-tokens": "the access token", "labelExist": "the {{label}} already exists", "queue-guest-display": "The queue"}, "fields_management": {"field_drawer_btn": "Create Field", "create_field_drawer-title": "Create New Field", "update_field_drawer-title": "Update the field [{{field<PERSON><PERSON><PERSON>}}]", "field_type_label": "Please select a Field Type", "field_label": "Internal Name", "field_alias": "Label", "hidden": "Hidden", "uniqueValue": "Unique Value", "required": "Required", "opt_table_btn": "Add New Option", "opt_value": "Internal Name", "opt_label": "Label", "optsTableConfirmPopup": "Sure to delete?", "popupConfirmYesBtn": "Yes", "popupConfirmNoBtn": "No", "optsTableTooltip": "There must be at least one option!", "drawerOkBtn": "Save", "drawerCloseBtn": "Close", "fieldTypeError": "Please pick a field type!", "fieldLabelError": "Field label is required!", "noOptsError": "You must fill all the options inputs!", "optValueError": "Option value is required!", "optLabelError": "Label value is required!", "moduleFieldRedirectBtn": "Go to {{moduleName}} settings", "fieldsGroupsTitle": "Fields Groups", "fieldsTitle": "Fields", "groupNameCol": "Group Name", "cancelAlertMsg": "Sure to cancel?", "cancelText": "Cancel", "okText": "Ok", "associatedGroup": "Associated Group", "selectModulePlaceholder": "Select Module", "moduleType": "Select Display Type", "selectGroupErrorMsg": "Associate a group to this field!", "selectModuleErrorMsg": "Module is required!", "searchBtn": "Search", "resetBtn": "Reset", "SearchPlaceholder": "Search in groups and fields", "selectedFieldType": "Selected Field Type", "aliasTooltip": "Click on alias to update", "redirectToCountries": "Go To Countries Settings", "redirectBtnHelpLine": "Click here to consult '{{dept}}' settings..", "country": "Countries", "optTooltip": "Click to edit", "placeholderField": "Placeholder", "fieldDescription": "Field Description", "multiselectInfo": "Multiselect field allows to select multiple options at once", "display": "Views", "dateFormatLabel": "Select Date Format", "timeFormatLabel": "Select Time Format", "dateFormatPlaceholder": "Select Date Format", "dateFormatInfo": "From here you can select the date format", "displayModelInfo": "Select the views where this field will be displayed. The limit per view type is shown on the right.", "groupStageLabel": "STAGE", "groupStagePlaceholder": "Select pipeline/stage", "groupNamePlaceholder": "Group Name", "addPipeline": "Add pipeline/stage", "readOnly": "Read Only", "multiple": "Multiple Selection", "multipleInfo": "This option allows multiple selection of 'country'.", "changeParamsError": "You can't modify properties as hidden and required simultaneously.", "moduleName": "<PERSON><PERSON><PERSON>", "displayedType": "Display", "addField": "Field", "addGroup": "Group", "searchFieldPlaceholder": "Search Field", "searchGrpPlaceholder": "Search in groups and fields", "fieldDesc": "Field Description", "exceedDisplayError": "You have exceeded the allowed limit for the <strong>{{views}}</strong> view type", "externalUserParam": "Show this field to external users (guests)", "showToGuest": "Show to guests", "externalUserParamInfo": "On check this option, the value of this field will be shown to your external users (guests).", "showInGuestForm": "Show in Guest Form", "optionsTable": "Options Table", "updateAliasSuccess": "Alias updated successfully", "deleteSystemViewError": "Unable to modify the system display views.", "hiddenAndRequiredError": "Unable to store properties as hidden and required simultaneously.", "reorder": "Reorder", "update_Group": "Update Group", "delete_group": "Delete Group", "companies": "Company", "contact": "Contact", "leads": "Lead", "lead": "Lead", "deal": "Deal", "ticket": "Ticket", "helpdesk": "Ticket", "project": "Project", "product": "Product", "booking": "Booking", "user": "User", "transaction": "Transaction", "invoices": "Invoices", "organisation": "Organisation", "expandAll": "Expand All", "collapseAll": "Collapse All", "fieldsInfo": "Improve the quality of your data by choosing where certain fields appear and by highlighting some as important. This includes custom fields that help you tailor data to your business needs.", "view": "View field form", "requiredFields": "Required field in this group", "uniqueFields": "Unique field in this group", "emptyFields": "No fields yet in this group.", "emptyFieldText": "You can create a field in this group from our suggestions of popular fields."}, "mailing": {"newMsg": "New message", "accept": "Accept", "confirm": "confirm", "newMail": "Send an e-mail", "DeleteMail": "Are you sure you want to delete the email", "DeleteMails": "Are you sure you want to delete", "ArchiveMail": "Are you sure you want to archive the email?", "UnarchiveMail": "Are you sure you want to unarchive the email?", "TransferMail": "Are you sure you want to move the email?", "TransferPopConfirm": "Move email", "Delete": "Delete Email", "DeleteButton": "Delete", "ClearTrashButton": "Delete forever", "Syncronized": "Syncronized", "NotSyncronized": "Not Syncronized", "Primary": "Primary", "Secondary": "Secondary", "shared": "Shared", "private": "Private", "searchAndSelect": "Search and select a {{type}} mailbox", "Personal": "Personal", "ConfigureMail": "Please configure your email", "DeleteDefinitive": "Messages that have been in Trash more than 30 days will be automatically deleted", "inbox": "Inbox", "sent": "<PERSON><PERSON>", "draft": "Draft", "starred": "Starred", "trash": "Trash", "spam": "Spam", "archive": "Archive", "unarchive": "Unarchive", "archiveEmail": "Archive Email", "unarchiveMail": "Unarchive Email", "search": "Search", "Reply": "Reply", "ReplyAll": "Reply all", "Forward": "Forward", "Actions": "Actions", "Contact": "Convert to contact", "EditContact": "Edit contact", "Deal": "Create a deal", "Activity": "Create an activity", "Organisation": "Create an organisation", "Ticket": "Create a ticket", "ErrorMsg": "Please input the message", "ErrorObject": "Please input the subject", "ErrorMail": "Please select at least one email", "ValidMail": "Please enter a valid email", "empty": "empty", "Close": "Close", "invalidConfig": "The configuration of the email {{email}} is invalid.", "successTest": "Your email address is valid.", "noData": "No Data", "Historique": "History", "AssigntransferredError": "You are unable to perform actions on this assignment because it has been transferred to another account", "AssignDispatcherError": "You cannot perform this action because you don't have the dispatcher role", "AssignContactAdmin": "Please contact your administrator for dispatcher configuration", "of": "of", "qualify": "Qualify", "Affect": "Affect", "Assign": "Assign", "identify": "Identify", "move": "Move", "moved": "Moved", "filterTitle": "Advanced filter", "yes": "YES", "no": "NO", "filterReset": "Reset filter", "Default": "<PERSON><PERSON><PERSON>", "ThanksToAssign": "Please assign to", "loading": "loading", "notAssigned": "NOT ASSIGNED", "InProgress": "In Progress", "PROCESS": "PROCESS", "Assigned": "ASSIGNED", "AssignTo": "Assigned to", "outOfTime": "OUT OF DEADLINE ", "successUpdate": "The qualification has been successfully modified", "successQualif": "The email is successfully qualified", "thanksAssign": "Please assign to", "processEmail": "Email processing time", "expiredEmail": "Processing time expired", "depassed": "is outdated", "markRead": "<PERSON> as read", "markUnread": "<PERSON> as unread", "read": "marked as read", "notRead": "Marked as unread", "markSpam": "<PERSON> as spam", "markfavorite": "mark as starred", "marknonfavorite": "mark as not starred", "markImportant": "mark as important", "marknotImportant": "mark as not important", "emailImportant": "Your email has been marked as important", "emailnotImportant": "Your email has been marked as not important", "emailRead": "Your email has been marked as Read", "emailnotRead": "Your email has been marked as unread", "backReception": "Back to inbox", "emailArchived": "Your email has been archived", "searchemailPrivate": "Search private email", "searchemailShared": "Search shared email", "affectedSuccess": "affected with success", "identifiedSuccess": "Your email has been successfully identified", "identifDeletedSuccess": "Identification successfully removed", "emailMoved": "the email is moved", "movedSuccess": "the email was successfully moved", "Emaildeleted": "Your email has been deleted", "Emailsdeleted": "emails have been deleted", "EmailAssigned": "Your email has been assigned to", "modifiedSuccess": "modified with success", "signed": "Signed by", "ShowMore": "Show more", "Inbox": {"other": "Other inbox", "sender": "Sender name", "To": " To", "subject": "Subject", "message": "Message", "date": "Date"}, "NewMsg": {"Cancel": "Cancel", "send": "Send", "received": "Received", "sent": "<PERSON><PERSON>", "from": "From", "To": "To", "object": "Object", "message": "Message", "newMsg": "New message", "placeholderMail": "Search or enter address email", "file": "Attach files", "photo": "Insert a photo", "emoji": "Insert an emoji"}, "optLabelError": "Label value is required!", "primaryFieldTooltip": "This field is primary", "titleconfigureEmail": " No mailbox configured", "configureEmailBtn": "Configure a mailbox", "configureEmail": "It seems that you are not a member of a shared box and that you have not set up any e-mails on your account.", "selectUsers": "Select users", "dispatcher": "Di<PERSON>atcher", "emailTransfer": "this email was forwarded by", "from": "from", "to": "to", "in": "in", "new": "New", "inProgress": "In progress", "processed": "Processed", "closed": "Closed", "close": "Close", "qualifier": "qualify the email", "identifyEmail": "Identify email to a module", "searchPrivate": "Search private email", "searchShared": "Search shared email", "moveTo": "Move to", "Tooltip": {"important": "<PERSON>quer comme important", "starred": "Marquer comme favori", "supprimer": "<PERSON><PERSON><PERSON><PERSON>", "Log": "History", "AssignTransferedNoIdentif": "You cannot identify the email because it is assigned and moved", "AssignTransferedNoIdentifNoAffect": "You cannot affect the email because it is assigned and moved", "AssignTransferedNoIdentifNoQualif": "You cannot qualify the email because it is assigned and moved", "AssignTransferedNoIdentifNoTransfer": "You cannot move the email because it is assigned and moved", "AssignNoIdentif": "You cannot identify the email because it is assigned", "AssignNoAffect": "You cannot affect the email because it is assigned", "AssignNoQualif": "You cannot qualify the email because it is assigned", "AssignNoTransfer": "You cannot move the email because it is assigned", "TransferedNoIdentif": "You cannot identify the email because it is moved", "TransferedNoAffect": "You cannot affect the email because it is moved", "TransferedQualif": "You cannot qualify the email because it is moved", "TransferedNoAssign": "This email has been moved and cannot be assigned", "TransferedAndAssigned": "<PERSON><PERSON> is assigned", "Affecter": "Affect this email to a module element", "Assigner": "Assign this email", "Move": "Move the email to another mailbox", "cannotAssignNotFromDepartment": "Assignment not allowed: You are not part of the department that manages this shared mailbox"}, "yourEmail": "your email has been", "processingTime": "Processing time", "all": "All", "MyMode": "mode me", "RelativeTo": "Relative to", "filter": "Filter", "identifier": "Identify", "convertTo": "Convert to", "Qualifier": "Qualify", "affecter": "Affect", "qualifierà": "Qualify to", "affecterà": "Affect to", "sender": "Sender", "user": "User", "detailed": "Open detail view", "itemSelected": "items selected", "messageSucces": "Your message has been sent successfully", "openOtherMessages": "open other messages", "addTemplate": "ADD TEMPLATE", "actionTemplate": "This action adds the contents of the selected template.", "confirmAction": "Please confirm this action?", "emailTemplate": "email template", "Attachments": "Attachments", "uploadFile": "Upload file", "changeSignature": " Your signature will be inculded in the email body. If you want to change it,", "clickHere": "Click here", "selectReceiver": "Select the receiver's email address to get the template.", "noSubject": "No subject", "Date": "Date", "Lieu": "Place", "Qui": "Who", "AssignedTo": "Assigned to", "qualifiedTo": "Qualified to", "Affected": "Affected", "chronoYes": "chrono :yes", "restaureEmail": "restore email", "synchroStart": "Synchronization in progress", "synchroFailed": "synchronization failed", "synchroSuccess": "synchronization completed successfully", "EmptyTrash": "Vider la corbeille", "ConfirmClearTrashTitle": "Confirm message deletion", "ConfirmClearTrashMessage": "Are you sure you want to permanently delete all messages from Trash? This action is irreversible and messages cannot be recovered", "successClearTrash": "All messages have been deleted", "forever": "forever", "assigned": "Assigned", "None": "Aucune", "notAllowedExt": "The <strong>.{{ext}}</strong> file type is not supported.", "includeAttachments": "Include original attachments", "labels": "Labels", "notSpam": "Not spam", "makeItNotSpam": "Make it non-spam", "labelAs": "Label as", "removeLabelFromMail": "Remove this label from this mail?", "movedBy": "Moved by", "assignToMe": "Assign to me", "takeOver": "Take over", "noAccessThisBox": "You do not have access to this mailbox.", "moveToast": "<strong>{{owner}}</strong> moved an email from <strong>{{from}}</strong> to <strong>{{to}}</strong> [\"{{subject}}\"]", "emailAttachment": "Contains <strong>{{nbr}}</strong> attachment(s)", "containsAttch": "Conatins attachment(s)", "expireProcessing": "The processing time for the email [{{subject}}] from <strong>{{address}}</strong> has expired. Assigned to <strong>{{user}}</strong> (Started: {{startDate}}).", "assignEmail": "Email [{{subject}}] from <strong>{{sender}}</strong> in <strong>{{account}}</strong> box has been assigned to <strong>{{owner}}</strong> by <strong>{{user}}</strong>.", "selfAssignEmail": "Email [{{subject}}] from <strong>{{sender}}</strong> in <strong>{{account}}</strong> box has been self-assigned by <strong>{{user}}</strong>.", "deleteAssignEmail": "Email [{{subject}}] from <strong>{{sender}}</strong> in <strong>{{account}}</strong> box has been unassigned from <strong>{{owner}}</strong> by <strong>{{user}}</strong>.", "selfDeleteAssignEmail": "Email [{{subject}}] from <strong>{{sender}}</strong> in <strong>{{account}}</strong> box has been self-unassigned by <strong>{{user}}</strong>.", "affectEmail": "Email [{{subject}}] from <strong>{{sender}}</strong> in <strong>{{account}}</strong> box has been affected to {{family}}: <strong>{{element}}</strong> by <strong>{{user}}</strong>.", "autoAffectEmail": "Email [{{subject}}] from <strong>{{sender}}</strong> in <strong>{{account}}</strong> box has been affected to {{family}}: <strong>{{element}}</strong> automatically.", "updateStateEmail": "Email [{{subject}}] from <strong>{{sender}}</strong> has changed state from <strong>{{lastState}}</strong> to <strong>{{newState}}</strong> by <strong>{{user}}</strong>.", "qualificationEmail": "Email [{{subject}}] from <strong>{{sender}}</strong> has been qualified with tag(s) '<strong>{{tags}}</strong>' by <strong>{{user}}</strong>.", "deleteQualificationEmail": "The qualification of email [{{subject}}] from <strong>{{sender}}</strong> has been deleted by <strong>{{user}}</strong>.", "identificationEmail": "Email [{{subject}}] from <strong>{{sender}}</strong> has been identified as {{family}}: <strong>{{element}}</strong> by <strong>{{user}}</strong>.", "deleteIdentificationEmail": "The identification for email [{{subject}}] from <strong>{{sender}}</strong> as {{family}}: <strong>{{element}}</strong> has been deleted by <strong>{{user}}</strong>.", "identifiedAs": "Identified as", "affectTo": "Linked to", "clickToEdit": "Click to edit", "start": "Start", "end": "End", "outOfDeadline": "Out of deadline", "bouncedMail": "Undelivered email", "affectThisMail": "Affect this email", "updateAffectation": "Updated the affectation", "updateIdentification": "Update the identification", "noMailings": "No template", "manageMailings": "Manage templates", "selectMailing": "Select a template", "insertMailing": "Insert template", "append": "Append Mailing", "clearAndInsert": "Clear and Insert template", "insertPrompt": "How would you like to insert this template?", "small": "Small", "normal": "Normal", "large": "Large", "huge": "<PERSON>ge", "sendingMail": "Sending mail...", "updateQualification": "Update qualification", "noEmailConfigured": {"title": "No email account configured", "subTitle": "It looks like you haven’t set up any mailboxes yet.\nTo start sending and receiving emails, please add an account.", "cta": "Add email account"}, "emailConfig": {"errorTitle": "Email configuration error", "errorDesc": "We couldn’t connect to your email account. Please verify your settings and try again."}, "dateRange": "Date range", "senderAddress": "Sender address", "subjectEmail": "Email subject", "showInOriginalBox": "Also display in the original inbox", "atLeastAddressOrSubject": "Please select at least one email address or subject!", "status": "Status", "chatMail": "Discuss this email", "roomAlreadyExist": "The chat room already exists"}, "tasks": {"tablePagination": "{{range}} of {{totalItems}} activities", "addQuickTask": "Add Quick Task", "deleteBtn": "Delete ({{s}} record{{plural}})", "tableLabel": "LABEL", "tableType": "TYPE", "tablePriority": "PRIORITY", "startDate": "START DATE", "tableEndDate": "DUE DATE", "tableOwner": "OWNER", "tableGuests": "PARTICIPANTS", "titlePlaceholder": "Title", "typePlaceholder": "Activity Type", "priorityPlaceholder": "Priority", "startDatePlaceholder": "Start Date", "endDatePlaceholder": "Due Date", "startTimePlaceholder": "Start  time", "endTimePlaceholder": "End time", "ownerPlaceholder": "Select an Owner", "guestsPlaceholder": "Select participant", "createTaskTitle": "Create New Activity", "updateTaskTitle": "Update Activity [{{label}}]", "updateVisioTitle": "Update Visio [{{label}}]", "type": "Activity Type", "title": "Title", "priority": "Priority", "dateTime": "Select Date and Time", "reminder": "Reminder", "owner": "Owner", "guests": "Participant{{s}}", "followers": "Follower{{s}}", "addGuestBtn": "Add participants", "addFollowerBtn": "Add Followers", "descriptionHelp": "The description is visible to all participants.", "noteHelp": "Notes are private and visible only to colleagues.", "upload": "Upload", "uploadRulesInfo": "Upload up to 6 files, with a total size limit of 15 MB", "uploadBtn": "Click or drag file to this area to upload", "weeks": "Weeks", "hours": "Hours", "days": "Days", "reminderSelect": "Select", "typeError": "Activity type is required!", "titleError": "Title field is required!", "priorityError": "Please select priority status!", "ownerError": "Please select an owner!", "tasksSidemenuTooltip": "Activities", "toastsTask": "Activity", "updatePriorityMsg": "Priority was updated successfully", "activitytypes": "Activities by type", "timeRangeStart": "from", "timeRangeEnd": "to", "addGuestsBtn": "participants", "addFollowersBtn": "Add Followers", "addGuestsBtnInCard": "Add Participants", "urgentPriority": "<PERSON><PERSON>", "mediumPriority": "Medium", "lowPriority": "Low", "highPriority": "High", "deleteTaskModal": "Delete selected Task{{plural}}?", "guestsListTitle": "participants", "followersListTitle": "Followers", "followersListTitleInfo": "(Only colleagues)", "listSearchPlaceholder": "Search by name, Email Address, Post Number, Phone Number", "ascendSort": "Click to sort ascending", "descendSort": "Click to sort descending", "cancelSort": "Click to cancel sorting", "associateModule": "Associate a module", "selectModulePlaceholder": "Select Module(s)", "endListIndicator": "No More", "drawerHeaderOne": "Activity created by", "drawerHeaderVisioOne": "Visio was created by", "drawerHeaderTwo": "on", "visioErr": "Please try again later", "startTime": "Start Time", "endTime": "End Time", "showTime": "Show Time", "correspondingStage": "Corresponding Stage", "correspondingStagePlaceholder": "Stage", "setPriority": "Set Priority", "attachments": "Attachment{{s}}", "addTaskInKanban": "Create New {{component}}", "AddNewStage": "Add New {{component}}", "stageLabel": "Stage Label", "WinProbability": "Win Probability", "color": "Color", "stageLabelError": "Stage label is required", "taskToastMsgwithOptions": "<strong>{{user}}</strong> added you in a new Activity <strong>{{taskLabel}}</strong>", "updateTaskToastMsgwithOptions": "<strong>{{user}}</strong> has updated the Activity <strong>{{taskLabel}}</strong>", "deleteTaskToastMsg": "<strong>{{user}}</strong> has deleted the Activity <strong>{{taskLabel}}</strong>", "updateTaskPriorityToastMsgwithOptions": "<strong>{{user}}</strong> has updated <strong>{{taskLabel}}</strong>'s priority from <strong>{{from}}</strong> to <strong>{{to}}</strong>", "updateTaskStageToastMsgwithOptions": "<strong>{{user}}</strong> has updated <strong>{{taskLabel}}</strong>'s stage from <strong>{{from}}</strong> to <strong>{{to}}</strong>", "noPriority": "no priority", "mercureReminder": "The Activity <strong>{{task<PERSON><PERSON><PERSON>}}</strong> starts in <strong>{{time}}</strong>", "mercureReminderDesktopNotif": "The Activity [{{<PERSON><PERSON><PERSON><PERSON>}}] starts in [{{time}}]", "reminderTooltip": "Reminders", "calendarViewTooltip": "Calendar", "tableViewTooltip": "Table", "listViewTooltip": "List", "noMoreNotifs": "No More Notifications", "noMoreReminders": "No More Reminders", "noMoreComments": "No More Comments", "createChatRoom": "Create Chat Room", "openChatRoom": "Open Chat Room", "menu2Header": "Activities: {{selected<PERSON>iew}} View", "reminderBeforeStart": "Reminder before start date", "reminderBeforeDue": "Reminder before due date", "reminderInfo": "On check this option, you will be reminded before the due of this activity", "moveToStage": "Move to new Stage", "reminderTitle": "This is an Activity Reminder", "overdueTaskInfo": "This activity is not in the final stages by its scheduled or expected deadline", "taskDeletedToast": "Task deleted successfully", "joinVisio": "Join Video Meeting NOW", "inviteMembersParam": "Send notification E-mails to selected external participants", "invitecolleaguesParam": "Send system message to selected colleagues", "uploadFile": "Click or drag file to this area to upload", "uploadFileHint": "Images, files formats are supported.", "today": "Today at", "choose": "<PERSON><PERSON>", "addActivity": "Activity was added successfully", "notAuthorizedToSeeActivity": "You are no longer participating in this activity.", "selectFamily": "Select Module", "selectFamilyPlaceholder": "Select Module", "selectFamilyElement": "Related Element", "selectFamilyElementPlaceholder": "Select Module element", "deleteAllBtn": "Delete All", "locationPlaceholder": "Location of the meet", "creatorRole": "Creator", "OwnerRole": "Owner", "MemberRole": "participant", "followerRole": "Follower", "selectRolePlaceholder": "Select your role", "generalTab": "Activity", "detailsTab": "Details", "sendNotifs": "Notification settings", "msgSystemNotif": "A system message will be sent to all selected colleagues", "emailSystemNotif": "A system email will be sent to all selected participants on add or update of an activity", "requiredElementOnSelectModule": "You must select an element from the selected module", "requiredModuleOnSelectElement": "You must select the module to which the selected element belongs", "elementNotBelongingToSelectedModule": "The selected element does not belong to the selected module", "filterTitle": "Advanced Filter", "clearFilter": "Clear Filter", "clearBtn": "Cancel", "filterResult": "Results: ", "loadActivitiesError": "An error occurred while loading your activities", "tryAgain": "Try Again", "calendarTryAgain": "An error occurred, Try Again", "searchOptions": "Search in:", "searchByLabel": "activity Label", "searchByName": "member name", "aucun": "No one", "addDate": "Add Date", "userFamily": "Colleague", "orgFamily": "Organization", "contactFamily": "Contact", "fileExtensionError": "File extension not supported!", "associatedModule": "Associated Module", "creator": "Planner", "activityId": "Activity ID", "showCols": "{{displayed}} of {{total}}", "show/hide": "Show/Hide Columns", "activityDeleted": "This activity was deleted", "unathorizedActivity": "You are not authorized to access activity details.", "userRemovedFromActivity": "You are no longer able to participate in this activity.", "noResult": "No Result", "activityStartsNow": "The Activity <strong>{{activity<PERSON><PERSON><PERSON>}}</strong> Starts Now", "activityEndsNow": "The Activity <strong>{{<PERSON><PERSON><PERSON><PERSON>}}</strong> Ends {{endTime}}", "moduleElement": "Click to see {{element<PERSON><PERSON><PERSON>}}'s details", "view360": "Overview", "copyLink": "Copy Activity Link", "configureLocalizationNotif": "Date & time format missing!<br /> Please configure your settings", "GoToLocalization": "Go to Settings", "noFollower": "No Followers", "noGuest": "No participant", "noChat": "Chat Not Available", "noChatInfo": "There must be at least 2 different colleagues to activate the chat", "deleteStage": "Delete <strong>{{label}}</strong>", "moreInfo": "More Options", "markAllRead": "Mark all as read", "unread": "Unread", "kpiVisioLabel": "Visio", "kpiCreatedLabel": "Created by me", "kpiIsOverdueLabel": "Overdue", "kpiIsOverdueVisio": "Overdue visio", "kpiInvitedLabel": "you're invited", "KpiChatSource": "Chat source", "logFeedTitle": "History Feed", "logFeedEndOfList": "No More", "historyBtn": "History", "allPipelines": "All Pipelines", "reminderErrorMsg": "Field 'reminder' cannot be empty", "commentsTab": "Comments", "unreadNotifs": "Unread Notifications", "elementShowMoreBtn": "Show {{totalHidden}} More", "showMoreBtn": "Show More", "elementShowLessBtn": "Show Less", "newComment": "{{messageCount}} New Comment{{s}}", "dateTimeError": "The start date-time cannot be after the due date-time.", "filterFromToday": "Starting from today <span class='bg-blue-100 text-black underline decoration-blue-500 underline-offset-1'>{{today}}</span>", "startFrom": "Starting from {{today}}", "occurredActivityReminder": "This reminder for <strong>{{activityLabel}}</strong> scheduled <strong>{{reminderDuration}}</strong> before. (started at <strong>{{startDate}}</strong>)", "filterInfo": "The filter parameters are automatically saved.", "creatorInfoTip": "on {{date}} at {{time}}", "rename": "<PERSON><PERSON>", "startingFromToday": "From Today", "monthView": "Month", "weekView": "Week", "dayView": "Day", "listView": "List", "TodayView": "Today", "openAttachment": "Open", "loadRoomError": "An error occurred, please try again later", "noDescriptionNote": "No description/Note", "goToSettings": "Go to pipelines settings", "assignment": "Assignment", "visioInProgress": "Visio Conference in progress...", "meMode": "Me mode", "filterConditionInfo": "This option allows you to combine filters. Choose 'OR' to match any condition, or 'AND' to match all conditions. At least two filters must be active to use this.", "filterConditionLabel": "Select how the filters are combined:", "and": "AND", "or": "OR", "startCreateActivities": "Start creating", "activities": "Activities", "recordings": "Recordings", "selectModuleInfo": "Select a module to associate with the activity. The module defines the category or context for the activity.", "selectModuleElementInfo": "Choose an element from the selected module to associate with the activity. Elements represent specific items within the module.", "checklistTooltip": "Task(s)", "addPipelineError": "You must add a pipeline first", "subtasksTitle": "Lists", "done": "Done", "fileSize": "KB", "selectTags": "Select tags", "doctors": "Doctors", "exams": "Speciality / Examen", "products": "Families / products", "selectDoctor": "Select a doctor", "selectExam": "Select an examen", "clinics": "Hospitals", "selectClinic": "Select a hospital", "medicalStaff": "Medical staff", "patients": "Patients", "dropZone": "Drop here to move", "dropHere": "Drop here to move to {{stage}}"}, "form": {"save": "Save", "cancel": "Cancel", "submit": "Submit", "create": "Create"}, "beforeDeleted": {"departments": "If you remove this department, all these services will be removed", "type-family-products": "If you delete this type of product, all these families will be deleted.", "pipelines": "If you delete this pipeline, all these stages will be deleted.", "steps": "Please note: steps that depend on this step are automatically dependent on the previous step."}, "helpDesk": {"Levels": "Levels", "addChannel": "Add a channel", "addDep": "Add a department", "addService": "Add a service", "addSla": "Add a SLA", "addFolder": "Add a folder", "addSeverity": "Add a severity", "channels": "Channels", "folder": "Folders", "hours": "Hours", "minutes": "Minutes", "days": "Days", "state": "State", "addType": "Add a contact type", "actif": "Active", "noActif": "Inactive", "status": "Status", "customers": "Contacts", "all": "All", "listcontacts": "List of contacts", "listproduits": "List of products", "listproducts": "List of products", "listteams": "List of teams", "listorganisations": "List of organisations", "clients": "Customers", "produits": "Products", "teams": "Teams", "selectproduits": "Select products", "selectclients": "Select customers", "selectteams": "Select teams", "selectorganisations": "Select organisations", "selectcontacts": "Select contacts", "allproduits": "All products", "allclients": "All contacts", "allteams": "All teams", "createsla": "Create SLA", "updatesla": "Update SLA", "usedothersla": "exists in another sla", "assignedTo": "Assigned to", "changeName": "Change only the name", "subject": "Subjects", "addSubject": "Add subject", "addLevel": "Add level", "contacts": "Contacts", "products": "Products", "organisations": "Organisations", "activeTemplateEmail": "Please assign the necessary model and ensure that it is activated", "successRemis": "Successfully submitted.", "successRelaunched.": "Successfully relaunched.", "assign": "Assign me", "move": "Move", "transfer": "Transfer", "relaunch": "Relaunch", "handOver": "Hand over", "ticket": "{{single}}ticket{{plural}}", "merge": "<PERSON><PERSON>", "mergeTicketBlock": "Merge ticket into block", "transfertTicketBlock": "Transfer tickets into block", "coverTicket": "Are you sure you can cover this ticket?", "SureRelaunchTicket": "Are you sure you want to relaunch this ticket?", "listLevels": "List of levels", "cateringAgent": "Catering agent", "reasonTransfer": "Reason for transfer", "InterventionCarriedOut": "Intervention carried out", "sureHandTicket": "Are you sure you want to hand in this ticket ?", "moveToTrash": "Move to trash", "slaRespected": "SLA respected", "slaDepassed": "SLA depassed", "closeTicket": "Close", "reopenTicket": "Reopen", "closeTicketConfirmMessage": "Are you sure to close this ticket?", "reopenTicketConfirmMessage": "Are you sure to reopen this ticket?", "cancelNoAuth": "You are not allowed to cancel this ticket"}, "users": {"addRole": "Add a role", "addTeam": "Add a team", "users": "Users", "teams": "Teams", "roles": "Roles", "guests": "Guests"}, "familyProduct": {"addFamily": "Add a family", "addType": "Add a product type", "addUnity": "Add a unity", "addCompany": "Add a company", "addDiscount": "Add a discount", "addTax": "Add a tax", "discount": "the discount", "rate": "Rate"}, "import": {"uploadSample": "UPLOAD", "uploadSampleDescription": "Spreadsheet uploading guide", "mapColumns": "MAP COLUMNS", "mapColumnsDescription": "Map columns with fields", "configuration": "CONFIGURATION", "configurationDescription": "Spreadsheet import config", "import": "IMPORT", "importDescription": "Start the import process", "date": "DATE", "fileName": "FILE NAME", "ok": "IMPORTED", "duplicates": "DUPLICATES", "errors": "ERRORS", "actions": "ACTIONS", "delete": "Delete", "upload": "Click or Drop to Upload the Spreadsheet", "delimiter": "Delimiter", "filePreview": "File Preview", "submit": "Submit", "selectFamily": "Select a Family", "spreadsheetColumns": "Spreadsheet Columns", "forceOption": "Force Option (Overriding duplicated fields with new values)", "extraFieldOption": "Extra Fields Option (Adding new Fields and merging them with the old import)", "fileProcessed": "Your file has been processed", "uploadAnother": "upload another file ?", "history": "History", "family": "Family", "selectMapping": "Select a mapped field", "choosePk": "Please choose a primary key", "selectPk": "Select a primary key", "uploadedSuccess": "uploaded successfully.", "uploadFailed": "upload failed.", "jobLaunched": "Job successfully launched.", "jobFailed": "Job launching failed.", "nameOfColumnExist": "the first line of your file contains the column names", "reset": "Reset", "inputFile": "Please input your File", "chooseFamily": "Please choose a family", "unkownColumnName": "Column {{index}}", "continue": "Continue", "standardOption": "Standard Option (Standard import without forcing duplicates and extra fields)", "restoreMappingFailed": "Error on restore this mapping: {{error}}", "duplicatesNotification": "There are some duplicates in the file. do you want to force the upload ? if yes please choose a primary key :", "next": "Next", "previous": "Previous", "titleOfFileColumns": "Columns's title of your file", "familyFields": "Family Fields", "uploadHint": ".csv, .xls, .xlsx formats supported.", "uploadWarning": "Your file will be automatically converted to .csv.", "downloadFileWithTitle": "Download {{filename}}", "uploadSuccessHint": "History ID : {{historyId}} / Imported : {{nbr_ok}} / Skipped : {{nbr_doublons}} / Errors : {{nbr_error}} / Family : {{family}}", "uploadRows": "Number of Rows", "uploadColumns": "Number of Columns", "uploadSize": "Upload Size", "uploadFileName": "File Name", "archived": "ARCHIVED", "deleted": "DELETED", "deleteAndArchiveRecordsNotification": "Do you want to delete or archive this data ?", "archiveAction": "Archive", "deleteAction": "Delete", "deleteAndArchiveAction": "Delete / Archive", "isNotImage": "is not a image", "deleteArchiveSummary": "History ID: {{historyId}} / Linked Families: {{families}} / For Archive : {{archived}} / For Delete : {{deleted}}", "fileProcessedWithWarnings": "Your file has been processed but with issues", "fileInfos": "File Infos", "download": "Download", "downloadSample": "Download Sample", "deleteRecordsNotification": "Do you want to delete this data ?", "deleteDescription": "This record in the history table with History ID {{historyId}} will be deleted permanently.", "status": "STATUS", "completed": "Completed", "mapping": "Mapping", "downloadSampleDescription": "In case of list if you find separations with 'OR' in the sample file, you must therefore choose a value from this list. in case of range if you find separations with ',' in the sample file, you must put an interval of two elements.", "notSupportedFormat": "file format not supported.", "uploadFileFamily": "Family", "organisation": "Organisation", "contact": "Contact", "deal": "Deal", "user": "User", "project": "Project", "booking": "Booking", "leads": "Leads", "product": "Product", "helpdesk": "Helpdesk", "mapLists": "MAP LISTS", "importDetails": "IMPORT DETAILS", "spreadsheetLists": "Spreadsheet Lists", "titleOfFileLists": "Title of file lists", "lists": "Lists", "selectMappingList": "Select a mapped list", "selectMappingListFamily": "Start Typing to display family values", "logs": "Logs", "logsEmpty": "Empty", "addType": "Add {{title}}", "company": "Company", "department": "Department", "service": "Service", "role": "Role", "startImport": "Start Import", "downloadFileAgain": "Download The Spreadsheet", "mappingColumn": "Columns Mapping", "mappingList": "Lists Mapping", "empty": "Empty Value", "requiredFields": "Required <PERSON>", "sharedWith": "Please select a shared department", "uniqueFields": "Unique Fields", "cancelImport": "Cancel Import", "importingFile": "This File is being imported", "mappingListNotice": "Select columns to map a specific list, empty selection will be ignored", "dualCode": "Dial Code", "phoneCode": "Phone", "monetaryCurrency": "<PERSON><PERSON><PERSON><PERSON>", "monetaryValue": "Value", "importWarningText": "An import is in progress. Would you like to cancel it and start a new import?", "importWarningTitle": "Import warning", "importWarningContinueBtn": "Continue Import", "importWarningCancelBtn": "Cancel Import", "nextStepBtn": "Next Step", "cancelBtn": "Cancel and Start Again", "shareWith": "Share With", "shareWithInfo": "Indicate the confidentiality option for sharing your imported contacts: If you choose 'Only Me', the contacts are displayed for you only. If you choose 'Departments', you must select which departments are authorized to access the contacts, with the option of making any necessary changes.", "onlyMe": "Only Me", "others": "Departments", "selectDeptsError": "Please select at least a department", "shareBtnError": "Please select an option", "selectFamilyError": "Please select a family", "inviteImports": "Send Invitations E-mails to the imported users", "sendInvitaionsToast": "An invitation E-mail was sent to all imported users", "autocompletePlaceholder": "Type value of the list", "inviteUsers": "Invite", "inviteUsersTooltip": "{{invitedNumber}} user(s) out of {{importedNumber}} imported user(s) were already invited", "dialCode": "Force Missing Dial Code Values", "dialCodeInfoTooltip": "The selected option will be forced for missed values in {{value}} column in your imported file", "currency": "Force Missing Currency Values", "currencyInfoTooltip": "The selected option will be forced for missed values in {{value}} column in your imported file", "accurateAutoMap": "Accurate Auto-selection", "autoMapToVerify": "Verfiy this Auto-selection", "selectCurrencyPlaceholder": "Select your Currency", "selectDialPlaceholder": "Select your Dial Code", "selectFamilyPlaceholder": "Select Corresponding Module", "monetary": "<PERSON><PERSON><PERSON><PERSON>", "dialCodeLabel": "Phone Dial Code", "importDone": "Import was launched successfully. You'll be notified once the import is done.", "importFinished": "Job has finished successfully. Please review the history table for feedback.", "startAnother": "Import Another File", "importHasStarted": "Import has started. You'll be notified once the job is done", "cancelImportTitle": "Cancel Import", "cancelImportDesc": "if you cancel the import now, you can continue it anytime from this step. All you have to do is to click 'continue' from history table.", "continueImportBtn": "Continue Import", "selectAll": "Select All", "selectedDateFormat": "Selected date format: "}, "companies": {"socialreason": "Social reason", "adress": "Address", "city": "City", "postalCode": "Postal code", "country": "Country", "phone": "Phone", "website": "WebSite", "banks": "Banks", "email": "Email", "taxId": "Tax Identification Number", "vatCode": "VAT code", "tradeRegister": "Trade Register", "capital": "Capital", "addbank": "Add a Bank", "infoFis": "Tax information", "infoGeneral": "General information", "accessTableCompanies": "Access to the table of companies", "principalCompany": "principal company", "addPipeline": "Add a pipeline", "addStage": "Add a stage", "addcompany": "Add a company", "selectcompanies": "Select companies", "systemEmail": "Automatic EMAIL address (System)", "senderName": "Sender name", "shippingEmail/Sms": "Sending settings (EMAIL/SMS)", "infoSenderEmail": "The value of Sender (Email) is taken from  General/Companies/{{label}}/(EMAIL/SMS delivery section)", "infoSenderSms": "The value of Sender (SMS) is taken from  General/Companies/{{label}}/(EMAIL/SMS delivery section)", "infoSignSms": "The signature (EMAIL) is taken from General/Companies/{{label}}/(EMAIL/SMS delivery section)", "tax_identification": "Tax Identification Number", "tva_code": "VAT code", "trade_register": "Trade Register", "code_postal": "Postal code", "logo": "Logo", "icon": "Icon", "label": "Social reason", "company": "Company", "searchInfoInModuleInTable": "Search across all columns", "searchInfoInModuleInKanban": "Search by any label", "kanbanSettingsInfo": "Go to <strong>{{module<PERSON>abel}}</strong>' pipelines settings", "agency": "Agency", "countries": "Countries", "currency": "<PERSON><PERSON><PERSON><PERSON>", "bank": "Bank", "correctNumberPhone": "Please enter a valid phone number"}, "general": {"companies": "Companies", "channels": "Channels", "countries": "Countries", "queueGuest": "Guest queue", "addQueueGuest": "Add a guest queue"}, "guestQueue": {"name_en": "Name in EN", "name_fr": "Name in FR", "queue_number": "Queue", "selectQueue": "Select queue"}, "services": {"label": "Name", "departement_id": "Department", "selectdepartment": "Select a department"}, "colors": {"Black": "Black", "Red": "Red", "Dark Red": "Dark Red", "Orange": "Orange", "Dark Orange": "Dark Orange", "Yellow": "Yellow", "Green": "Green", "Dark Green": "Dark Green", "Emerald": "Emerald", "Teal": "<PERSON><PERSON>", "Cyan": "<PERSON><PERSON>", "Light Blue": "Light Blue", "Blue": "Blue", "Dark Blue": "Dark Blue", "BlueViolet": "<PERSON><PERSON><PERSON><PERSON>", "Indigo": "Indigo", "Purple": "Purple", "Violet": "Violet", "Fuchsia": "Fuchsia", "Pink": "Pink", "Rose": "<PERSON>", "Gray": "<PERSON>", "Dark Gray": "Dark Gray", "Slate": "Slate", "Brown": "<PERSON>", "Amber": "Amber", "Lime": "Lime", "Sky": "Sky"}, "wiki": {"Docs": "Docs", "Groups": "Groups", "SelectGroup": "Select a group...", "AddFolder": "Add folder", "AddBinder": "Add a catalog", "EditBinder": "Edit a catalog", "binder": "Catalogs", "AddPage": "Add a page", "EditFolder": "Edit folder", "DeleteFolder": "Delete folder", "AddAFolder": "Add a folder", "TitleFolderFr": "Title in FR", "TitleFolderEn": "Title in EN", "Cancel": "Close", "Ok": "Save", "AddPageToFolder": "Add a page to the folder: ", "TitlePageFr": "Title of the page in FR", "TitlePageEn": "Title of the page in EN", "Delete": "Delete ", "Edit": "Edit", "Confirm": "Confirm", "Save": "Save", "SlugUrl": "Slug Url", "French": "French", "English": "English", "TitlePage": "Tilte of the page", "Content": "Content", "SlugUrlCopied": "Slug URL copied", "AddGroup": "Add group", "Image": "Image", "UploadImage": "Upload image", "sorting": "sort", "AddAGroup": "Add a Group", "EditGroup": "Edit Group", "Title": "Title", "Description": "Description", "FolderAlreadyExists": "This folder already exists!", "PageAlreadyExists": "This page already exists!", "NoGroupSelected": "No group selected", "TitleRequired": "The title is required!", "PageEditedSuccessfully": " edited successfully!", "Published": "Published", "Draft": "Draft", "ImageRequired": "The image is required!", "The label fr has already been taken.": "The label has already been taken.", "uploadContent": "Upload the content", "ErrDownload": "Error downloading document", "metaDescription": "Meta Description"}, "pipeline": {"pipelinestickets": "Pipelines tickets", "pipelinesdeals": "Pipelines deals", "pipelinesprojects": "Pipelines projects", "stagestickets": "Stages tickets", "stagesdeals": "Stages deals", "stagesprojects": "Stages projects", "pipelineName": "Pipeline Name", "stageName": "Stage Name", "finalStage": "Final", "errFinalStage": "The pipeline must include at least one final stage.", "isDefault": "You cannot update this stage", "chooseAnotherPipeline": "You can choose another type of pipeline.", "createPipelineFirst": "Create a pipeline first", "stage": "Stage", "addStageToGrouName": "Add this stage to a group name", "resolved": "Resolved"}, "chat": {"reload": "Reload", "loading": "Loading", "more": "More...", "less": "Less.", "loadPreviousMessage": "Load previous messages", "groupe": "Group", "groupe_public": "Public group", "groupe_guest": "Group with guests", "addGroup": "Add a group", "nameGroup": "Name Group", "selectAll": "Select all", "uploadDragImage": "Click or drag image to this area to upload", "errSizeImage": "must smaller than {{size}}!", "addColleague": "Add a conversation", "filter": "Filter by", "empty_discussion": "Start a new discussion", "startconversation": "Start a new discussion", "retrieveFileDataError": "An error occurred while retrieving the file. Please try again later.", "viewFileWithTitle": "Open file <u>{{fileName}}</u>", "forward": {"text_message_forwarded_at": " on ", "text_message_forwarded": "Forwarded", "text_message_forwarded_title": "Forward history", "previewMsg": " Message forwarded: ", "forwardTo": "Your message has been forwarded by : ", "forwardToOther": "The message sent by <i class=' font-semibold '>{{sent_name}}</i> has been forwarded by : ", "title_modal": " Forward a message", "content_modal_message": "Are you sure you want to forward this message?", "you_forward_message": "You forwarded a message sent by ", "you_forward_your_message": "You forwarded your message", "you_received_forwarded_message": " forward you a message sent by ", "room_received_forwarded_message": " forward a message sent by ", "to": "to", "other_room": "an other discussion", "placeholder_select1_forward": " Click to select the recipient", "placeholder_select2_forward": " Choose the name of the recipient", "message_sidebar": "Forward", "error_select_user": "Please choose one or more users"}, "delete": {"deleteMember": "Delete", "messaageDeleted": "Message  deleted", "delete_member": "Member was successfully deleted", "title_modal": "Delete a message", "content_modal_message": "Are you sure that you want  delete this message?", "delete_error_sent_message_title": "Definitive deletion of a message", "delete_error_sent_message_description": "Are you sure to delete this message?", "delete_error_member": "Are you sure to delete the member ?"}, "polls": {"poll": "poll", "title_modal": "Create poll", "question": "Question", "question_placeholder": " Enter your question", "control_question": "Question is required", "option_placeholder": "Enter your option", "add_option": "Add option", "Options": "Options", "allow_multiple_answers": "Allow multiple answers", "multiple_choice": " Multiple choice", "single_choice": " Single choice", "Yes": "Yes", "No": "No", "all": "All", "incomplete": "In progress", "public_tooltip": "Allows you to view the list of survey respondents and their answers.", "expire_At": "Expiration date ", "finished": "Finished, ", "private": "Show participants", "unique_option": "Options must be unique ", "min_option": " You must add at least 2 options", "option_not_empty": "Option cannot be empty", "success": "Poll successfully created.", "voted": "voted", "max_option": " You can add only 5 options", "show_result": " Show result", "hide_result": " Hide result", "limit_time": " No limit time", "limit_time_tooltip": "By activating this option, the survey will be definitively closed on the configured date.", "you_voted": " voted in : ", "other_voted": " voted in : ", "expire": "Expires", "list": "List of all polls", "incomplete_list": "List of uncompleted surveys", "polls": "Polls", "validation": {"time_out": "The poll has expired"}}, "reply": {"message": "replied to the message ", "sentBy": "Sent by", "sidebar": "replied : ", "close": "Close the thread first"}, "edit": {"text_message_old": "Old message : '", "text_message_update_at": "edit on", "at": "at"}, "react": {"message_to_delete": "Click to delete the reaction", "other_user": "and {{number}} other(s) "}, "audio_message": {"errorCharge": "Error while loading the audio message", "title_modal": "Audio message", "stop": "Stop", "permission_message": "Please accept the permission to use your microphone", "description": "Send a voice message in only one minute, click on {{stop}} to get the message", "submit_directory": "Submit without check the audio ", "accessMic": "Please allow access to the microphone to use this feature."}, "file": {"error_taile": "File must be smaller than 50MB!", "max_file": "You can select only 5 files, by default the system takes the first 5!", "error_extention": "The only extensions allowed are the following: {{extention}}"}, "action": {"deviceMobile": "From Mobile", "deviceDesktop": "From Web", "delete": "Delete", "forward": "Forward", "resend": "Resend", "pin": "<PERSON>n", "pinned": "pinned", "By": " by ", "pinned_info": "pinned (for all members)", "unpin": "Unpin", "react": "React", "edit": "Edit", "reply": "Reply", "emoji": "<PERSON><PERSON><PERSON>", "generateText": "Rewrite with AI", "messageEmptyErrorAi": "Cannot improve the message because there is no content.", "rich_text": "Editor", "upload": "Upload File", "call": "Call", "call_audio": "Audio call ", "call_video": "Video call", "create_conferance": "Create a video meeting", "star": "Star", "unstar": "Unstar", "starred": "Starred ", "starred_info": "Starred (only for you)", "list_starred": "List of starred messages", "list_polls": "List of polls", "list_pinned": "List of pinned messages", "sendMessage": "Discuss", "designateAdmin": "Confirm the designation", "newAdmin": "New admin", "option": "Option", "copy": "Copy", "copied": "<PERSON>pied", "task": "Create a task"}, "info": {"noMedia": "No media Found", "noLinks": "No links Found", "noDocuments": "No documents Found"}, "searchSide": {"all": "All", "conversations": "Discussions", "users": "Users", "messages": "Messages", "new_user": "Please enter your message to {{name}} ...", "searchIcon": "Search", "searchTitle": " Which search input to choose?", "searchGroupUser": "Search for a group or colleague ", "searchConversation": "Conversation search", "navigation": "Navigation", "search": "Search...", "searchMembers": "Search members", "searchMessages": "Search a message", "searchTips": "Enter the word to search then click on \"ENTER\"", "searchResults": "<span class='font-semibold text-black'>{{count}}</span> result(s).", "cancelFilter": " Cancel the filter", "searchMembersToAdd": "Search members to add", "searchGroup": "Search a group", "searchEmpty": "Cancel the filter and search again", "searchMember": "Search a member", "searchDocument": "Search a document", "searchLinks": "Search a link"}, "message_system": {"draft": "Draft", "update_general_group": "has made a change to the channel information.", "leave_group": "has left the group", "remove_membre": "removed", "removed_auth_user": "removed you from the channel", "removed_auth_user_modal": "You have been removed from the group by the administrator.", "groupe_deleted": "The group has been deleted by administrator.", "add_membre": "added", "add_auth_user_membre": "added you to the channel", "successAddMembers": "The addition of member(s) is successfully completed.", "successChangeAdmin": "Change of admin successfully.", "leaveSuccessGroup": "You have successfully left the group.", "successChangeQuitAdmin": "Administrator change successfully completed. You have left the group as a user.", "designateAdmin": "The designation of a new admin is done.", "newMessage": "New message", "SyncNewMessage": "Changes have been made to this channel...", "DeniedAccessChat": "You can't access to the chat, please contact the administrator", "error_fetch_new_messages": "Error while getting new messages", "successDeleteGroup": "Group successfully deleted.", "missed_call": "Missed voice call ", "outgoing_call": "Outgoing voice call ", "duration": "with a duration of ", "incoming_call": "Incoming voice call ", "statusChanged": "The status has been changed successfully.", "task_created": "created a task", "task_updated_stage": "updated the stage of the task", "task_created_auth": "have created a task", "task_updated_stage_auth": " have updated the stage of the task", "selectReasonInDeal": "Please select a reason to close this deal", "deal_updated_stage": "Stage updated", "visio-created": " create a video meeting", "visio-created-auth": " have created a video meeting"}, "notification": {"privateMessage": "Private message from ", "commentTask": "New comment", "inTask": " in the task ", "inModule": " in module {{moduleName}} ", "publicMessage": "Message from", "titleNewMessage": "You have a new message", "sendBy": " sent by : "}, "header": {"members": "members", "member": "member", "share_video_call": "Share meeting link", "share": " Share invitation link", "copyLink": "Copy the invitation link", "shareWithEmail": "Share the invitation by email", "send": "Copy the invitation link", "participate": " Participate in meeting", "createVideoLater": "Plan a video meeting", "visio": {"invitation": "You're invited to join a video conference created by", "created_by": "created by", "timeToStart": "due {{time1}} at {{time2}}", "invitation-link": "Click the link below to join:", "roomName": "Room name", "join": "Join the video meeting", "title": "Invitation to a video meeting from ", "title2": "Invitation to a video meeting", "description": "invited you to join a videoconference on Sphere.", "visio-leave": "Thank you for taking part in the video meeting. See you soon!", "user-leave": "{{name}} has left the video meeting", "user-join": "{{name}} has joined the video meeting", "callVideoSIP": "Call video SIP", "createVideoConferance": "Create a video meeting", "createInstantVideoConferance": "Start an instant meeting", "validationHint": " Press Enter to validate the name", "full-screen": "Full screen"}, "createVideoCallDescription": "Save this link and share it with the people you'd like to meet. Make sure to keep a copy for future use as well.", "createVideoCallError": " An error occurred while creating the video call, please try again later.", "createVideoCallWait": "Wait for <timer> {{timer}} </timer>  for the video to load automatically, or click the link to access it instantly."}, "room": {"error_send_message403": "You are not allowed to send messages in this channel", "description_under_title_room": " This is the beginning of the history of the channel", "description_under_title_user": "This is the beginning of the history of the private exhibition with", "confirmAbandonment": "Confirm abandonment", "quitGroup": "Are you sure you want to leave this group ?", "confirmDelete": "Confirm Delete Group", "deleteGroup": "Are you sure you want to delete this group ?", "chooseImage": "Choose an image"}, "message_type": {"file": "file", "image": "image", "voice": "voice", "message_from_bot": "Message from bot {{name}}", "message_visio_conf": "{{name}} organise a video meeting", "message_deleted": "Message deleted", "message_deleted_room": "{{name}} has deleted the message"}, "external_drawer": {"image": "{{name}} sent an image", "file": "{{name}} sent a file", "voice": "{{name}} sent a voice message", "mixed_image": "{{name}} sent an image and a text", "mixed_file": "{{name}} sent a file and a text"}, "error_message": {"message_send": "Message not sent. ", "re-send_action": "Click here to resend the message or delete it.", "re-send": "First, resend or delete the wrong message.", "chooseUsers": "Select one or more users !", "failedAddMembers": "Failed to add member(s).", "delete_error_member": "Are you sure you want to delete this member?", "selectNewAdmin": "Select the new admin", "designateAdmin": "Are you sure you want to designate this member as an admin?", "min3chart": "The name must be at least 3 characters"}, "bot": {"code": "Code", "description": "You can use the code below to integrate the Send Messages as Notification API into your app.", "dev": "Webhook", "create": " Create a bot", "edit": "Edit a bot", "active": "Active", "deactive": "Deactive", "activate_bot": "Activate the bot", "deactivate_bot": "Deactivate the bot", "activate_bot_description": "Are you sure you want to activate this bot?", "deactivate_bot_description": "Are you sure you want to deactivate this bot? The API token will be <b>lost</b> and the API will be <b>stopped.</b>", "name": "Name ", "namePlaceholder": "Enter the name of the bot", "image": "Logo", "deleteImage": " Delete image", "acceptedOnlyImageType": " Only image files are accepted (png, jpg, jpeg)", "uploadImage": "Click or drag file to this area to upload", "requiredName": " The name of the bot is required", "minName": "The name of the bot must be at least 3 characters", "requiredLogo": " The logo of the bot is required", "file_min_size": "The file size must be smaller than 150 Ko", "successCreateBot": "The bot has been successfully created.", "successUpdateBot": "The bot has been successfully updated.", "apiSendSuccess": "The request has been successfully sent.", "successStatusBot": "The status of the bot has been successfully updated.", "apiTokenNotFound": "The token is not found", "empty": "No bot found", "selectLang": "Select the language", "copy": "Copy", "copied": "<PERSON>pied", "testApi": "Send test request", "detail": {"paragraph": "Webhooks are an easy way to get automatic messages and data sent to a group using the magic of generated scripts.", "list1": "Click on «Create a bot»", "list2": "Fill in the information on the creation form and validate.", "list3": "Copy the generated integration script and place it in your third-party application script"}}, "typing": "is typing...", "placeholderInput": "Write your message...", "others": "and others", "userstyping": "are typing...", "respond": "responded: ", "audio": "audio message", "deleted": "message deleted", "informations": "Informations", "mute": "Mute", "quitGroup": "Quit Group", "valid&quit": "Validate and quit", "removeFromList": "Remove From List", "messagePreview": "Message Preview", "sortBy": "Sort By", "AZ": "A-Z", "recents": "Recents", "UnReadMessage": " Unread Messages", "display": "Display By", "members": "Members", "groups": "Groups", "guests": "Guests", "chat_group": "group", "chat_member": "member", "ok": "OK", "add": "Add", "create": "Create", "deleteGroup": "Delete Group", "connected": "Connected", "userInfos": "User Infos", "username": "Username", "email": "Email", "telephone": "Extension", "stopSearch": "Return to original discussion", "itsAll": "It is all, nothing more 🤐", "medias": "Medias", "documents": "Documents", "links": "Links", "link": "link", "infos": "Infos", "emptyPlaceholder": "Speak so I can see you.", "create_group_by_auth": "You have created this group  ", "create_group": "You have been added to this group  ", "new": "New", "decrypt": "Decrypting messages", "admin": "Admin", "reacted": "reacted", "onMessage": "on : ", "starredMessages": "Starred Messages", "pinnedMessages": "Pinned Messages", "goto": "Go To", "openHere": "Open here", "you": "You", "name": "Name", "thread": "<PERSON><PERSON><PERSON>", "back": "Back", "archiveList": "Archived", "successfullyArchived": "Discussion successfully archived", "successfullyRestored": "Discussion successfully restored", "archiveModalTitle": "Confirm archiving this discussion", "archiveModalBody": "Are you sure that you want to archive this discussion ?", "validate&archive": "Validate and archive", "archiveRestoreModalTitle": "Confirm restoring this discussion", "archiveRestoreModalBody": "Are you sure that you want to restore this discussion ?", "validate&restore": "Validate and restore", "restoreDiscussion": "Rest<PERSON>", "archiveDiscussion": "Archive", "unmute": "Unmute", "muteModalTitle": "Confirm muting this discussion", "muteModalBody": "Are you sure that you want to mute this discussion ?", "unmuteModalTitle": "Confirm unmuting this discussion", "unmuteModalBody": "Are you sure that you want to unmute this discussion ?", "validate": "validate", "listStarredGlobal": " Starred ", "listPinnedGlobal": " Pinned ", "listSavedMessage": " Saved messages", "all": "ALL"}, "visio": {"waitModerator": "Please wait for the moderator to join the meeting...", "moderatorJoin": "The moderator has joined the meeting", "searchInfo": "Search by any column (except Start Date/End Date) and the number of Key Performance Indicators (KPIs) is not dependent on your specific search.", "room_name": "Room name", "addVisio": "Create Visio", "createNewVisio": "Create a new Visio", "visioToTask": "Transform into task", "plan": "Plan", "start": "Start", "url": "Url", "password": "Secret code", "participate": "Participate", "participateMeet": "Join now", "loginInformation": "Joining info", "linkMeeting": "Meeting link", "organizer": "Organizer", "guest": "Guest{{plural}}", "waiting": "waiting", "yes": "yes", "myMeetings": "My meetings", "editInCalendar": "Edit the meeting", "deleteMeeting": "Delete the meeting", "newMeeting": "Start a meeting", "today": "Today", "upComing": "Upcoming", "history": "History", "refresh": "Refresh", "endDate": "Ends", "startDate": "Starts", "colleague": "Colleague{{plural}}", "contact": "Contact{{plural}}", "company": "Company{{plural}}", "textCreator": "Task was created", "creator": "Creator", "searchGuests&followers": "Search for guests or followers", "noVisioToday": "No visioConf scheduled for today", "noVisioLater": "No visioConf scheduled for upComing", "noVisioHistory": "No visioConf scheduled for history", "linkClicked": "The link has been copied", "this_week": "This week", "upcoming": "Upcoming", "tomorrow": "Tomorrow", "followVisioLinkClipboard": "To join the videoconference, follow this link", "followVisioInPhoneClipboardPartOne": "To enter by phone, dial", "followVisioInPhoneClipboardPartTwo": "and enter this code", "morePhoneClipboard": "Here are some other phone numbers", "callOn": "Call on", "infoCnx": "Via Phone", "infoCnxTitle": "Your connection details", "visioInfoText": "Send this link to the people you wish to invite to the meeting. Be sure to save their contact information so you can reuse it later.", "canceledVisio": "Canceled", "startInstantMeeting": "Start instant meeting", "visioMeet": "video meetings", "recordedDownloadTitle": "Recorded video", "recordedVideoTitle": "Recorded", "roomClosed": "This meeting hasn't started yet.", "roomExpired": "This meeting is over. See you soon.", "meetStartsIn": "The meeting starts in ", "CreateVisioBtn": "Create Visio"}, "checklist": {"label": "Name", "tasktype_id": "Activity Type", "duration": "Duration", "listChecklist": "Checklist list", "stepsChecklist": "Checklist Step"}, "login": {"login": " <PERSON><PERSON>", "username": "Username", "Title1": "Welcome ", "usernameRequired": "Please input your username!", "usernameEmail": "The input is not valid E-mail!", "password": "Password", "passwordRequired": "Please input your password!", "submit": "Submit", "back": "Back", "reset_password": {"verifToken": " Token verification ...", "invitationExpired": "Your invitation has expired, please contact the administrator.", "resend_code": " You can resend the code after", "passwordLength": "The password must contain at least 8 characters", "wrongLink": "Sorry, the link you followed is invalid or has expired. Please try again.", "button": "Confirm Password", "passwordMatch": "Passwords do not match", "passwordPattern": "Password format is incorrect.", "passwordRequired": " Password is required", "minLength": "Password must be at least 8 characters.", "uppercase": "Password must include at least one uppercase letter.", "number": "Password must include at least one number.", "specialChar": "Password must include at least one special character.", "title": "Password Creation", "subtitle": "Choose a secure password to protect your account. Make sure it's unique and difficult to guess.", "password": "New password", "confirmPassword": "Confirm new password", "success_password_Set": " Your password has been successfully saved."}}, "notifications": {"senddesktopnotifications": "Send desktop notifications:", "forallactivities": "For all activities", "onlyforpersonalmentionsandmessages": "Only for personal mentions and messages", "never": "Never", "notificationsound": "Notification sound:", "on": "On", "off": "Off", "notificationUpdated": "Notification Successfully Updated", "currentUserCreatesActivity": "You created this activity", "currentUserUpdatesActivity": "You updated this activity", "currentUserDeletesActivity": "You deleted this activity", "currentUserUpdatesDateActivity": "You updated the schedule of this activity", "currentUserUpdatesLabel": "You updated the label of this activity to <strong>{{newLabel}}</strong>", "currentUserAddsFiles": "You added files to this activity", "currentUserRemovesFiles": "You removed files from this activity", "currentUserAddsNewMemberActivity": "You added <strong>{{newM<PERSON>ber}}</strong> in this activity", "currentUserUpdatesPriority": "You updated the priority of this activity from <strong>{{oldPriority}}</strong> to <strong>{{newPriority}}</strong>", "currentUserUpdatesStage": "You updated the stage of this activity from <strong>{{oldStage}}</strong> to <strong>{{newStage}}</strong>", "currentUserUpdatesType": "You updated the type of this activity from <strong>{{oldType}}</strong> to <strong>{{newType}}</strong>", "currentUserUpdatesElement": "You assigned this activity to the element <strong>{{element}}</strong> from the module <strong>{{module}}</strong>", "currentUserupdatesActivity": "You updated the dates of this activity", "currentUserUpdatesReminder": "You updated the reminder of this activity", "currentUserUpdatesDescription": "You updated the description of this activity", "currentUserUpdatesNote": "You updated the note of this activity", "imagesInKanban": "Image{{s}}", "currentUserRemovesMember": "You removed <strong>{{removedUser}}</strong> from the activity <strong>{{activityLabel}}</strong>", "userRemovesMember": "<strong>{{user}}</strong> removed <strong>{{removedUser}}</strong> from the activity <strong>{{activityLabel}}</strong>", "currentUserAddsMemberWithRole": "You added <strong>{{addedUser}}</strong> as {{role}} in the activity <strong>{{activityLabel}}</strong>", "currentUserRemovesMemberWithRole": "You removed <strong>{{removedUser}}</strong> ({{role}}) from the activity <strong>{{activityLabel}}</strong>", "userAddsMemberWithRole": "<strong>{{user}}</strong> added <strong>{{addedUser}}</strong> as {{role}} in the activity <strong>{{activityLabel}}</strong>", "userRemovesMemberWithRole": "<strong>{{user}}</strong> removed <strong>{{removedUser}}</strong> ({{role}}) from the activity <strong>{{activityLabel}}</strong>"}, "webphone": {"errorGetData": "An error occurred while retrieving the parameter data for your webPhone!", "notifReceiveCallTitle": "Incoming call from: ", "reconnect": "Reconnecting...", "error": "Webphone disconnected. Attempts to reconnect launched...", "mustAllowMicrophonePermission": "You must allow microphone permission."}, "unavailability": {"start_date": "Start date", "end_date": "End Date", "nature": "Nature", "description": "Description", "Leave": "Leave", "Authorization": "Authorization", "Other": "Other", "otherDesc": "For example: Travel, Meeting, Lunch break ...", "Youarenotallowedtorequestoffourhoursofauthorizationspermonth": "You are not allowed to request  of four hours of authorizations per month", "checkthetimeslotforunavailability.": "Check the time slot for unavailability.", "Theminimumdurationofleaveis4hours,pleaseverifytheselectedtimeslot.": "The  minimum  duration  of  leave  is  4  hours,  please  verify  the  selected  time  slot.", "Youhaveexceedtheallowednumberofdays!": "You have  exceed the allowed number of days !"}, "profilemenu": {"profile": "My profile", "support": "Support", "documentation": "Documentation", "logout": "Logout", "signedAs": "Signed as: ", "Tenancy": "Tenancy: ", "status": "Status", "offline": "Offline", "online": "Online", "away": "Away", "busy": "Busy", "custom": "Custom", "accessToken": "Access Token", "addAccessToken": "Add a access token", "expirationDate": "Expiration date", "lastUsed": "Last used", "lastViewAccessToken": "Here is your new personal access token. This is the only time it will be shown so don't lose it! You may now use this token to make API requests.", "pat": "Personal Access Token", "expiredDate": "The expiration date has expired", "infoTenant": "Tenancy information", "chatStorage": "Chat storage", "available": "Available", "storageUsage": "Storage usage", "appMobile": "Application mobile", "paragraphAppMobile": "A secure communication solution that integrates advanced messaging and professional telephony to facilitate team collaboration with comprehensive features and a smooth user experience.", "scanCode": "Scan QR code to download the application", "fillFieldsAppMobile": "Use your credentials to log in", "aboutTheApplication": "About the application", "usePassword": "Please use your password to log in.", "EnterTenancy": "Enter tenancy URL", "loginqrCode": "Login with QR code", "loginEmailPassword": "Login with email and password", "generateCodeQr": "Generate QR code", "scanCodeAndConnect": "Scan to connect to the app", "ReceiveQrCode": "Click here to receive a new QR code", "notificationMailing": "Notifications are managed by the creators of the email accounts", "notificationEnabledStatus": "Notifications are enabled on", "notificationDisabledStatus": "Notifications are disabled on"}, "dashboard": {"welcome": "Welcome", "unreadmsgOtoO": " Private{{plural}}", "unreadArchivedMessages": "Archived", "unreadEmail": " Unread email", "unreadmsgtoG": " Groups", "missedCalls": " Missed", "receivedCalls": " Received", "outgoingCalls": " Outgoing", "visitor": " New visitor{{plural}}", "newConversations": " New conversation{{plural}}", "newMessages": " New message{{plural}}", "todoToday": "To do today", "goToModuleTask": "View all activities", "youhave": "You have", "postNumber": "Extension", "phoneNumber": "Phone number", "checkTasks": "Check your current activities and upcoming activities here", "checkTasksDesc": "Check the activities are you currently doing and other upcoming activities", "viewCalendar": "View calendar", "goToCalls": "Go to call log", "unreturnedMissedCalls": " Unreturned", "unreadMention": "Mentioned", "independantNumber": "The displayed numbers do not depend on the date filter.", "independantFilter": "The displayed numbers do not depend on the {{name}} filter.", "unreadEmailReceived": "Unread", "unreadEmailSent": "Unread sent", "totalEmailSent": "<PERSON><PERSON>", "statsCall": "The figures relate to the call", "statsMsgs": "The figures relate to the messages", "unreadPerSession": "Unread per session", "socialMedia": "Social networking", "wa": "WhatsApp", "fb": "<PERSON>", "ig": "Instagram", "chat": "Live Chat", "relativeFigures": "Figures pertain to all conversations regardless of department assignment.", "queue": "Queue", "lostNotRecalled": "Missing recalled", "recalled": "Called Back", "notRecalled": "Not called back", "answered": "Answered", "unreadDiscussions": "Unread discussions", "createdByYou": "Created by me", "totalElements": "Element{{plural}}", "percentage": "Percentage", "sharedAccounts": "Only the shared boxes", "tenancy": "Tenancy", "ticket_by_stage": "Number of tickets by stage", "ticket_by_perimiter": "Number of tickets by perimiter", "ticket_by_type": "Number of tickets by type", "ticket_by_priority": "Number of tickets by priority", "ticket_by_channel": "Number of tickets by channel", "ticket_by_product": "Number of tickets by product", "moduleBySource": "Number of {{name}} by source", "moduleByType": "Number of {{name}} by type", "exportCsv": "Export as CSV", "accountEmail": "an email account", "nextTasksTypes": "Next activities types", "prevTasksTypes": "Previous activities types", "numberElementsPerModule": "Number of elements per module", "activities": "activities", "leadsNotConverted": "Leads not converted to contacts", "Contact_non_guest": "Contacts not invited", "successfulConversions": "Successful conversions", "successfulInvitations": "Successful invitations"}, "voip": {"unknown": "Unknown", "callMade": "Call made, ring", "callFailed": "Call failed", "callReceived": "Call received, ring", "missedCall": "Missed call, ring", "failedCall": "Call failed", "callMadeAnswered": "Call made answered, rang", "callMadeNotAnswered": "Call made not answered, rang", "callMadeFailed": "Failed call made", "callReceivedAnswered": "Call received answered, rang", "callReceivedNotAnswered": "Missed call, rang", "callReceivedFailed": "Failed call received", "addCompany": "Add company", "addContact": "Add contact", "addLead": "Add Lead", "onTheNum": "on the N°", "transferred": "referred to", "issued": "Outgoing", "receipts": "Incoming", "missed": "Missed", "outgoing_missed": "Not answered", "failed": "Failed", "answered": "Answered", "busy": "Busy", "callMissed": "Missed call", "apply": "Apply", "displayTime": "Display time", "yesterday": "Yesterday", "today": "Today", "today&yesterday": "Today and yesterday", "currentWeek": "Current week", "currentMonth": "Current month", "last7days": "Last 7 days", "last14days": "Last 14 days", "last30days": "Last 30 days", "last90days": "Last 90 days", "audioRecording": "Audio Recording", "all": "All", "unread": "Unread", "filterCallByType": "Filter Call By Type", "search3chart": "Enter at least 3 characters to search...", "log_search": "Name, poste or number phone", "group_queue_search": "Search by name or number", "colleagueNum": "Number of colleagues", "moreInfo": "More Info", "moreInfoWith": "More info about", "clickToDisplayInfo": "Click on the item to display information.", "clickToSendMail": "Click to Send E-Mail", "clickToCall": "Click to call", "contactNum": "Number of contacts", "of": "of", "call": "Call", "chat": "Cha<PERSON>", "chatWith": "Chat with", "videoCall": "Video call", "family": "Family", "companies": "Organizations", "contacts": "Contacts", "colleagues": "Colleagues", "groups_queues": "Groups & Queue", "notInContacts": "Not in your contacts", "tagDelete": "Tags deleted successfully", "editTag": "Edit qualification", "areYouSureToDelete": "Are you sure to delete", "thoseTags": "those tags", "thisTag": "this tag", "tagIsRequired": "The field '{{x}}' is required!", "qualification": "Qualification", "writeNote": "Write your note...", "save": "Save", "addTag": "Add tag", "processing": "Processing", "inputMessage": "Search colleagues & contacts & log", "renvoi-vers-poste": "Internal extension", "renvoi-vers-numero": "Phone number", "renvoi-vers-boite": "Voice mail", "renvoi-sonneries": "Ringtones", "renvoi-vers": "To", "renvoi-vers-boite-message": "To your voicemail", "renvoi-title": "Call forwarding", "renvoi-button": "Confirm", "renvoi-appel": "Call forwarding", "ringing-output-device": "Ringing output device", "renvoi-appel-message": "No referral", "renvoi-title-page": "Settings", "renvoi-conditionnel": "Conditional referral", "searchLogs": "Search by name or number...", "qualifyTheCall": "Qualify this call", "or": "Or", "createTask": "Create Activity", "updateQualify": "Update the call Qualification", "successUpdateQualify": "Call qualification with <strong>{{name}}</strong> is successfully updated", "successCreateQualify": "Call qualification with <strong>{{name}}</strong> is successfully created", "save_createTask": "Save & Create {{nbr}} task{{s}}", "audioCall": "Audio call", "VideoCall": "Video call", "with": "with", "sonnerie": "Ring", "pauseAppel": "Call paused", "coursAppel": "Calling", "connectingInProgress": "Connecting in progress", "switchTo": "Switch to", "conf": "Audio conference", "nouvelAppel": "New call", "pauseBut": "Hold", "playBut": "Resume", "silence": "Silence", "transfert": "Transfer", "conférence": "Conference", "taskCall": "Create Activity", "qualif": "Qualify this call", "recalled_today_answered": "Called back in the same day and answered", "recalled_answered": "Called back and answered", "recalled_not_answered": "Called back and no answer", "not_recalled": "Not recalled", "yesterday_at": "Yesterday at", "at": "at", "moreHistory": "Click to see more details", "directory": "Directory", "numPhoneNotFoundRmc": "This contact has no phone number", "add": "Add", "addToModule": "Create new", "invalidPhoneFormat": "Invalid phone format! please enter a valid phone format.", "openChat": "You cannot open more than one discussion", "alreadyInChat": "You are already in chat!", "call_qualification": "Call qualification", "ask_before": "Ask before", "never": "Never", "always": "Always", "internalExtension": "Internal extension", "yourPhoneNum": "My phone number", "yourVoicemail": "My voicemail", "colleague": "Colleague", "beep": "<PERSON><PERSON>", "beeps": "<PERSON><PERSON>", "yes": "Yes", "no": "No", "userDoNotHavePhoneNum": "You don't have a phone number, you need to add one in order to choose this option. To achieve this you need to navigate to your profile and add your number phone", "after": "After", "to": "To", "delete": "Delete", "cancel": "Cancel", "edit": "Edit", "hintQualifSetting": "This is a form that appears when you've completed the call, allowing you to qualify the call and perform tasks.", "followUpAction": "Follow-up action", "suggested": "Suggested", "search": "Search", "fieldRequired": "This field is required", "selectQualify": "Select qualification", "assignCallTo": "Affect this call", "selectModule": "Select module", "search_select": "Search and select", "assignment": "Affect this call", "searchGroup": "Search by group name or number", "alreadyMember": "You are already a member of this group", "logAndSearch": "Log & Search", "dialPad": "DialPad", "hideDialPad": "<PERSON>de", "treatedBy": "Treated by", "from": "From", "emptyContent": "Empty content", "phoneNumbers": "Phone numbers", "treated": "Treated", "groupsOrQueues": "Groups / Queues", "waitingTime": "Waiting", "noAnswer": "No answer", "recalledBy": "Recalled by", "queues": "Queues", "callGroup": "Call group", "myPhoneNumLog": "My tel. no.", "hisPhoneNumLog": "His/her tel. no.", "afterCallForwarding": "Following a referral", "afterCallTransfer": "Following a transfer", "afterCallForwardingToVoicemail": "Following a referral to voicemail", "forwarding": "Forwarding", "transfer": "Transfer", "me": "Me", "group": "Call group", "groups": "Call groups", "queue": "Queue", "iAmMember": "I am member", "view360": "View sphere", "sort": "Sort", "alphabetically": "In alphabetical order", "byModules": "By modules", "firstTime": "First time", "exportLog": "Export log", "fullScreen": "Full screen view", "alreadyInView360": "You are already in an interface that includes the sphere view. Would you like to leave this page to open a different sphere view instead?", "selectGroupOrQueue": "Select a group or queue", "missed_today_call": "Missed", "total_today": "Total", "received_today_call": "Incoming", "outgoing_today_call": "Outgoing", "missed_calls_not_returned_today": "Missed not returned", "answered_calls": "Answered calls", "no_answered_calls": "Unanswered calls", "moyen_ringing_calls": "Average waiting time", "moyen_treatement_calls": "Average call duration", "DMS": "AWT", "DMC": "ACD", "associate": "Associate", "disassociate": "Disassociate", "associateTo": "Associate to", "removeAssociation": "Delete association", "closeCurrentDrawerMsg": "Please close the open panel so you can open the detailed record of <strong>{{name}}</strong>", "closeCurrentDrawerToCreate": "Please close the open panel to be able to open the create panel.", "callOn": "Call {{name}} on", "searchDirectory": "Search by name or number", "successUpdateQualifySetting": "Qualification setting updated successfully.", "successUpdateOutputDeviceSetting": "Output device setting updated successfully.", "successDeleteForwarding": "Call forwarding deleted successfully.", "successUpdateForwarding": "Call forwarding updated successfully.", "taskInfo": "Activity information", "infoInputWebPhone": "The number of displayed items is limited. For a comprehensive search, please consult the full directory.", "notTheOwner": "You are not the creator of this element, so you cannot delete it.", "referral": "Referral", "updateTask": "Update Activity", "noteTagInfo": "This note is linked to this call qualification, and it will be displayed in call log", "qualifNote": "Qualification note", "noteTaskInfo": "This note will be linked to the note for activity <strong>{{taskName}}</strong>", "qualificationInfo": "Selecting a qualification with a red dot will log a call qualification and create an activity named after the qualification with the chosen due date.", "remove": "Remove", "qualifContainsTask": "This qualification contains a task", "minimizeDrawerChat": "Minimize [Esc]", "msgWhenDisplayChatSidebarIsOpen": "You can't display discussion list because you're already in a view that includes the chat or the modal window for the new e-mail is open!", "errorRetrievingLogData": "An error occurred while retrieving the call log data!", "blind": "Blind", "attended": "Attended", "backTo": "Back to", "confirmTransfer": "Confirm transfer", "onlyMe": "Only Me", "departments": "Departments", "sharedWith": "Shared with", "errorSharedWith": "Please select at least one department to share with!", "selectDepartments": "Select departments", "noCallLogFound": "No call history. You haven't made or received any calls.", "noVoicemailFound": "No voicemail history. You haven't received voicemails.", "sendMsgWhenRinging": "Send a message", "createNew": "Create new", "infoCallInProcess": "<strong>{{member}}</strong> is currently handling the call from <strong>{{srcSource}}</strong> on <strong>{{dstSource}}</strong>", "callInProcess": "Call in process", "inProcess": "In process", "livePanel": "Live panel"}, "profile": {"forgotPwd": "Forgot password", "pwdUpdateSuccess": "Password updated successfully", "yourNewPwd": "Your new password", "thisNotifWillDisappear": "This notification will disappear in {{x}} seconds.", "theOldPwdIncorrect": "The old password is incorrect", "resendInXSec": "Resend in {{x}} seconds", "anEmailHasBeenSent": "An email has been successfully sent to your inbox at", "editPwd": "Edit Password", "oldPwd": "Old Password", "msgForgotPwd": "Clicking here will send a URL to your email to redirect you to an interface where you can change your password.", "fieldRequired": "This field is required", "confirmNewPwd": "Confirm New Password", "min8chart": "Minimum eight (8) characters!", "passwordMatch": "The two passwords that you entered do not match!", "confirm": "Confirm", "cancel": "Cancel", "newPwd": "New password", "profile": "Profile", "password": "Password", "edit": "Edit", "rmcAccess": "RMC Access", "sharedEmail": "Shared Email account(s)", "personalEmail": "Personal Email account(s)"}, "contacts": {"cannotDelete": "You cannot make this action because <strong>{{label}}</strong> is already used in <strong>{{errMsg}}</strong>!", "edit": "Edit", "delete": "Delete", "successDelete": "Deleted Successfully", "the": "the", "notAllowed": "You are not allowed to make this action!", "successCreate": "created successfully", "errExistingMail": "Email already taken", "createAndAddAnother": "Create & Add another", "createGoDetail": "Create & Go to view sphere", "createAndInvite": "Create & Invite", "createAndInviteAndAddAnother": "Create & Invite & Add another", "createAndInviteAndGoDetail": "Create & Invite & Go to view sphere", "cancel": "Cancel", "create": "Create", "goToXSetting": "Go to {{x}} fields settings", "fieldSetting": "<PERSON> Setting", "createNewX": "New {{x}}", "successUpdate": "The {{x}} {{y}} has been updated successfully", "saveAndGoDetail": "Save & Go to sphere view ", "save": "Save", "validated": "Validated (optional)", "upload": "Upload", "enterFileName": "Enter file name...(optional)", "fieldXRequired": "The [{{x}}] field is required!", "enterValidEmail": "Please enter a valid email address!", "onlyNum": "This field only accept number!", "validUrl": "Please enter a valid url!", "min10chat": "Minimum ten (10) characters, at least one uppercase letter, one lowercase letter and one number!", "goBack": "Go back", "showAll": "Show all", "column": "Column", "columns": "Columns", "showColumns": "Show columns", "company": "Organization", "contact": "Contact", "user": "Colleague", "guest": "guest", "product": "Product", "ticket": "Ticket", "project": "Project", "companies": "Organizations", "contacts": "Contacts", "collegues": "Colleagues", "users": "Users", "products": "Products", "tickets": "Tickets", "projects": "Projects", "booking": "Booking", "bookings": "Bookings", "leads": "Leads", "lead": "Lead", "invoices": "Invoices", "invoice": "Invoice", "invite": "Invite", "block": "Block", "unblock": "Unblock", "errorDelete": "You cannot make this action because", "alreadyUsed": "already used in", "those": "those", "is": "is", "are": "are", "deleteConfirmMsg": "Are you sure you want to perform this action?", "successSendInvite": "Invitation sent successfully! Email receipt could be delayed.", "successSendInvitations": "Invitations sent successfully! Emails may arrive with delay.", "blockUser": "{{x}} is successfully blocked !", "blockUsers": "{{x}} users are successfully blocked !", "unblockUser": "{{x}} is successfully unblocked !", "unblockUsers": "{{x}} users are successfully unblocked !", "created": "Created", "active": "Active", "invited": "Invite", "re-invited": "Re-invite", "blocked": "Blocked", "unblocked": "Unblocked", "createdAt": "Created at", "updatedAt": "Updated at", "createdBy": "Created by", "noEmojisAllowed": "Emojis are not allowed in this field!", "linkInvitation": "Invitation link", "linkExpired": "This invitation link has expired. Please re-invite the user to generate a new valid link.", "errorMercure": "Error: [<strong>{{error}}</strong>] while processing your action. Please try again or contact support if the problem persists.", "successMercure": "Success! {{element}} [<strong>{{name}}</strong>] has been successfully <strong>{{action}}</strong> by <strong>{{admin_name}}</strong>.", "linkInviteError": "Something Went Wrong while processing your action. Please try again or contact support if the problem persists.", "thisWindowWillClose": "This window will close automatically in <strong>{{timer}}</strong> seconds.", "fieldFilledAuto": "This field will be filled automatically", "deleteAssociation": "Are you sure to delete this association?", "users_unblocked": "unblocked", "users_blocked": "blocked", "send_invitation": "invited", "user_accept_invitation": "{{element}} [<strong>{{name}}</strong>] has accepted the invitation.", "loadingDataTable": "Loading data, please wait", "total_elements": "Total", "created_by_you": "Created by me", "associated_to_you": "Associated to me", "total_active": "Total active", "notPermissionToDelete": "You do not have permission to delete this/those element(s), (You're not the creator or an admin!)", "btnCreateCompany": "Create organization", "btnCreateContact": "Create contact", "btnCreateDeal": "Create deal", "btnCreateUser": "Create user", "btnCreateProduct": "Create product", "btnCreateTicket": "Create ticket", "btnCreateProject": "Create project", "btnCreateBooking": "Create booking", "btnCreateLead": "Create lead", "btnCreateInvoice": "Create invoice", "btnCreateTransaction": "Create Transaction", "createdFileEvent": "<strong>{{creator_name}}</strong> has uploaded a file in the <strong>{{family_name}}</strong> <strong>{{element_name}}</strong>", "createdCommentEvent": "<strong>{{creator_name}}</strong> has commented on the <strong>{{family_name}}</strong> <strong>{{element_name}}</strong>", "updatedCommentEvent": "<strong>{{creator_name}}</strong> has updated a comment on the <strong>{{family_name}}</strong> <strong>{{element_name}}</strong>", "openInNewTab": "Open in new tab", "createdElement": "<strong>{{user}}</strong> created a new {{family}} [<strong>{{elementName}}</strong>]", "updatedElement": "<strong>{{user}}</strong> updated the {{family}} [<strong>{{elementName}}</strong>]", "deletedElement": "<strong>{{user}}</strong> deleted the {{family}} <strong>{{elementName}}</strong>", "deletedElements": "<strong>{{user}}</strong> deleted <strong>{{nbr}}</strong> <strong>{{family}}</strong>", "errLimitLicense": "You have reached the license limit. Please upgrade to add or unblock more users.", "notifProccessBeingProcess": "Your action is currently being processed. This may take a little while. You will be notified once the process is complete.", "selectFolderDisabled": "You cannot select a folder while filters are active. Please clear the filters to proceed.", "selectPipelineDisabled": "Action disabled as long as filters are applied.Please deactivate them to continue.", "selectKanbanViewDisabled": "You cannot select a Kanban view while filters are active. Please clear the filters to proceed.", "triggerDesc": "Click to sort descending", "triggerAsc": "Click to sort ascending", "cancelSort": "Click to cancel sorting", "inviteUserProcess1": "Your action is currently being processed. This may take a little while.", "inviteUserProcess2": "You will be notified once the process is complete.", "inviteContacts": "Inviting {{name}} to be a guest", "emailTakenError": "The email has already been taken", "enterEmailTitle": "This contact does not have an email address.", "inviteNotAllowed": "You do not have permission to make this action!", "viewMore": "View more", "more": "More", "sendEmail": "Send email"}, "layout_profile_details": {"overview": "Overview", "activities": "Activities", "upcomingEvents": "Upcoming events", "recentInteractions": "Interactions", "callTask": "Call activity", "meetingTask": "Meeting activity", "allTasks": "All activities", "files": "Files", "upcoming": "Upcoming", "cart": "<PERSON><PERSON>"}, "vue360": {"createTask": "created this task at ", "delete": "Delete", "deType": "of type", "ascreatorandowner": "as creator and owner.", "asGuest": "as guest", "and": "and", "asFollowers": "as followers", "noInteractions": "No interaction", "isOwner": "the owner.", "you": "You", "or": "or", "all": "All", "upcoming": "Upcoming", "overdue": "Overdue", "completed": "Completed", "are": "are", "is": "is", "titleCreation": "Create", "titleUpdate": "Update ", "actionCreate": "has created a  ", "actionUpdate": "has updated  ", "actionDelete": "has deleted  ", "call": " Call", "Product": " Product", "Deal": " Deal", "Helpdesk": " Ticket", "helpdesk": " Ticket", "ticket": " Ticket", "Ticket": " Ticket", "email": " Email", "Project": " Project", "Booking": " Booking", "Leads": " Lead", "leads": " Lead", "Contact": " Contact", "voip": " Call", "task": " Activity", "Organisation": " Company", "User": " User", "actionUpdateFrom": "has updated ", "from": "from", "to": "to", "by": "by", "noChat": "No chat", "ANSWERED": "Answered", "FAILED": "Failed", "BUSY": "Busy", "NO ANSWER": "No Answer", "CONGESTION": "Congestion", "actionCall": "called", "actionCallMe": "called you", "null": "", "empty": "empty", "withDuration": "with duration", "numberModification": "{{number}} modification{{plural}} made", "show": "Show ", "hide": "<PERSON>de ", "families": "Families", "actionsDelete": "Delete", "actionsCreate": "Create", "actionsUpdate": "Update", "cardFilterTask": "Activities", "cardFilterCall": "Calls", "transfer": "has transferred", "relaunch": "has relaunched", "move": "has moved", "assign": "has taken the", "affectation": "has affected", "qualification": "has qualified", "merge": "has merged", "return": "has revert the", "toTheDefaultFolder": " to the default folder", "update_qualification": "has updated the qualification of", "delete_qualification": "has deleted the qualification of", "update_affectation": "has updated the affectation of", "delete_affectation": "has deleted the affectation of", "note": "Note", "Email": " Email", "association": "has associated", "delete_association": "deleted the association with", "copyLink": "Copy Link", "subject": "Subject", "level": "Level", "severity": "Severity", "folders": "Folder", "slas": "Sla", "associateWithElement": "Association with an element (user, deal, etc.)", "informationSheet": "Informations", "generalInteraction": "Interactions", "channel": "Channel", "cannotAccessChat": "You do not have access to access the chat.", "multipleRelations": "You can establish mutiple associations", "oneRelation": "You can only establish one association", "noInfoGeneral": "You need to select a field and click on <PERSON>, then choose 360 View in the display mode.", "todoList": "Todo list", "convertedToContact": " has been converted into a contact", "noAssociationsAvailable": "No associations available", "history": "History", "replaceBy": "Would you like to replace {{oldValue}} with {{newValue}} ?", "noEmail": "No email address", "NotAuthTodeleteAssoc": "Click here to select {{module}} in the association and dissociate {{label}}", "roomChat": "Room conversation ", "privateChat": "Private conversation with ", "readMore": "Read more", "readLess": "Read less", "convertLeadToCtc": "Would you like to convert this lead into a contact?", "reopen": " Reopen", "closedTicket": "Close", "reference": "Reference", "closeThisTicket?": "Are you sure you want to close this ticket?", "reopenThisTicket?": "Are you sure you want to reopen this ticket?", "reopen?": "Are you sure you want to reopen {{name}}?", "cancelThisTicket?": "Are you sure you want to cancel this ticket?", "cancelThis?": "Are you sure you want to cancel {{name}}?", "reopenSuccess": "{{name}} has been successfully reopened", "closeSuccess": "{{name}} has been successfully closed", "theTicket": "The ticket", "listOfInteractions": "List of interactions", "resolved": "Resolved", "Transaction": " Transaction", "Invoices": " Invoice", "showMore": "Show more", "showLess": "Show less", "comment": "Comment", "toAddress": "to", "withSubject": "with the subject", "user": "User", "minimize": "Minimize", "maximize": "Maximize", "Emails": "Emails", "Meeting": "Meeting", "Visio": "Visio", "Notes": "Notes", "Comments": "Comments", "Todolist": "Lists", "Files": "Files", "Appelsdirects": "Direct Calls"}, "emailTemplates": {"TitlePage": "Title of the page", "smsHeader": "SMS header", "smsContent": "SMS content", "subjectMail": "Subject mail", "bodymail": "Content mail", "notDeletePrimaryTemplate": "The default template cannot be deleted.", "folder label must be unique.": "Folder label must be unique.", "system": "System", "template": "The template ", "limitCaracter": "You have exceeded the character limit.", "cannotDeleteSystem": "You cannot delete the system template.", "resetBodyMail": "Do you want to confirm the reset of the email body?", "titleResetBodyMail": "Confirmation of Email Body Reset", "resetBodySms": "Do you want to confirm the reset of the SMS body?", "titleResetBodySms": "Confirmation of SMS Body Reset", "goToSettings": "Go to General/Companies/{{label}}/(EMAIL/SMS delivery section)", "recoveryLink": "Link for forgotten password", "otp": "Security Code", "nameApp": "Company name", "fullName": "Full name", "authentication": "Authentication", "callerNumber": "Caller number", "postNumber": "Post number", "duration": "Duration", "cannotAddPage": "You cannot add a page to a system template", "goToCompany": "Go to the {{label}} information", "plsSelect": "Please select", "acceptInvi": "Accept invitation"}, "files360": {"labelErr": "Please input your label !", "labelPlaceHolder": "File label", "fileCannotBeUploadedToSecurityReasons": "The file cannot be uploaded for security reasons.", "fileErrorWhileUploading": "An error occurred while uploading the file.", "file": "File", "fileErr": "Please upload your file !", "filePlaceHolder": "Click or drag file to this area to upload", "uploadSubmit": "Submit", "uploadReset": "Cancel", "uploadSearch": "Search files by name...", "fileMaxSizeMsg": "File size must be less than 10MB !", "fileName": "File name", "fileUploadedAt": "Uploaded at", "fileUpatedAt": "Updated at", "fileCreator": "Creator", "modifyFile": "Modify file", "deleteFile": "Delete file", "deleteFileDescription": "Are you sure you want to delete this file ?", "deleteFileTitle": "Delete the file", "onOk": "Yes", "onCancel": "No", "viewFile": "View", "downloadFile": "Download", "updateLabel": "File {{filename}} label updated successfully", "deleteFileSuccess": "File {{filename}} deleted successfully", "updateFile": "File {{filename}} updated successfully", "uploadFile": "File {{filename}} uploaded successfully"}, "notes360": {"createNote": "Create a comment", "public": "Public", "internal": "Internal", "visibility": "Visibility", "editNote": "Edit this comment", "deleteNote": "Delete this comment", "deleteTheNote": "Delete this comment", "ensureDeleteMessage": "Are you sure you want to delete this comment?", "okDelete": "Yes", "cancelDelete": "No", "saveEdit": "Save", "noNotesFound": "No comments found", "searchNotes": "Search for comments ...", "noteCreatedSuccess": "Comment created successfully", "noteDeletedSuccess": "Comment deleted successfully"}, "signature": {"unregistred": "This signature has never been saved. Please press the 'Save' button to store the signature.", "addSignature": "Add a new email signature.", "listSignature": "List of email signatures", "newSignature": "new email signature", "defaultSignature": "Set as the default signature", "labelUsed": "This name is already used.", "defaultRequired": "At least one signature is marked as default", "exportpdf": "Export pdf"}, "build": {"start": "The wiki publication is in progress", "end": "The wiki has been successfully generated and published online.", "fail": "The generation and publication of the wiki has failed."}, "modules": {"companies": "Company", "contacts": "Contact", "leads": "Lead", "deals": "Deal", "tickets": "Ticket", "projects": "Project", "products": "Product", "bookings": "Booking"}, "todolist": {"createList": "Create List", "createListTitleModal": "Create New List", "updateListTitleModal": "Update List [{{list<PERSON>abel}}]", "listLabel": "List label", "newListItem": "New list item", "completed": "{{done}} out of {{total}} are completed", "listLabelError": "Please add a label to your list", "listItemError": "Please add an item", "noListItems": "There's no items in this list", "noMore": "No More"}, "selfNotes": {"addNote": "Add new note", "searchNotes": "Search notes ...", "sharingSuccess": "Note sharing successful", "sharingError": "Note sharing failed", "deleteSuccess": "Note deleted successfully", "deleteError": "Note deletion failed", "deleteNote": "Delete note", "deleteNoteMessage": "Are you sure you want to delete this note?", "autoSaveEnabled": "Your note will be saved automatically.", "noteLockedBy": "This note is locked by", "youAreTheLocker": "(You). ", "youAreNotTheLocker": ". You can't edit it, and the content you will write will not be considered.", "shareTitle": "Share note with collegues", "searchForColleaguesToShare": "Search collegues...", "saveShare": "Save", "cancelShare": "Cancel", "delete": "Delete", "shareWith": "Share with", "sharingInfo": "The note will be shared with the selected members in read-only mode.", "selectAll": "Select all", "edit": "Edit", "archiver": "Archive", "deleteNoteRichText": "You are about to delete this note. Are you sure you want to proceed?", "readOnly": "Read-only access.", "quote": "Taking notes is like doing scales in literature.", "observer": "Observer", "owner": "Owner", "discussion": "Discussion", "download": "Download", "allNotes": "All Notes", "myNotes": "My Notes", "sharedNotes": "Shared by me", "personalNotes": "Personal Notes", "sharedWithMe": "Shared with me", "associate": "Affect", "affectNote": "Affect note", "affectationSaved": "Assignment saved successfully.", "affectationSavedError": "Error during assignment.", "family": "Associated Module", "element": "Related Element", "selectFamily": "Select a module", "selectElement": "Select an element", "familyRequired": "Module field is required.", "elementRequired": "Element field is required.", "cancel": "Cancel", "informations": "Informations", "title": "Title", "createdAt": "Created At", "lastUpdateAt": "Last Update At", "sharedWith": "Shared With", "notShared": "This note is not shared with anyone.", "notAssociated": "This note is not associated with any module.", "deleteAffectation": "Delete affectation", "affectationDeleted": "Affectation deleted successfully.", "note_has_discussion": "This note includes a discussion", "note_is_affected": "This note has been assigned", "noteListPagination": "{{range}} of {{totalItems}} notes"}, "globalSearch": {"globalSearchAuto": "Global Search...", "infoOfSearchFunc": "Search for emails, activities, contacts, colleagues, deals, projects...", "other": "Other", "resultsFound": "1-{{dataLen}} of {{total}} items found", "infoAboutClick": "If you click on an item, it will open!", "noResultsFound": "No results found! Try using different keywords.", "clearFilter": "Clear Filter", "unread": "Unread", "createdBy": "Created by", "created": "Created", "owner": "Owner", "visioInProgress": "In progress", "joinVisio": "Participate", "overdue": "Overdue", "lastItemsFound": "Last {{total}} items found", "emailBody": "E-mail content", "shared": "Shared"}, "filterFamily": {"where": "Where", "and": "And", "or": "Or", "isEqual": "is equal to", "isNotEqual": "is not equal to", "isLike": "contains", "isNotLike": "does not contain", "isBlank": "is blank", "isNotBlank": "is not blank", "is": "is", "isNot": "is not", "containsAnyOf": "contains any of", "doesNotContainAnyOf": "does not contain any of", "containsAllOf": "contains all of", "doesNotContainAllOf": "does not contain all of", "isGreaterThan": "is greater than", "isLessThan": "is less than", "isGreaterThanOrEqual": "is greater than or equal to", "isLessThanOrEqual": "is less than or equal to", "isAfter": "is after", "isBefore": "is before", "isOnOrAfter": "is on or after", "isOnOrBefore": "is on or before", "isWithin": "is within", "filenameContains": "filename contains", "filenameDoesNotContain": "filename does not contain", "noFilterAdded": "No filter added", "addFilter": "Add filter", "resetFilters": "Reset filters", "apply": "Apply", "filter": "Filter", "allStages": "All stages", "sort": "Sort", "selectField": "Select a field...", "selectOrder": "Select the order...", "ascendant": "Ascendant", "descendant": "Descendant"}, "integrations": {"listOfIntegrations": "List of integrations", "integrations": "Integrations", "folderId": "Folder Id", "repository": "Repository", "ticketId": "Ticket ID", "resetConfirmation": "Reset Confirmation", "ticketTitleIn": "Ticket Title in  ", "descriptionTicketIn": "Ticket Description in ", "categories_id": "Categories", "urgency": "Urgency", "priority": "Priority", "choose": "Please choose ", "quitChat": "Leave chat", "goToViewTicket": "Are you sure you want to access the detailed view of this ticket ?", "entities_id": "Entities", "iaTools": "IA Tools", "emptyIntegration": "Click on an integration to display the settings to configure"}, "cart": {"createCartTitle": "Associated Cart", "UpdateCartTitle": "Cart details", "addNewProductBtn": "Add new product", "quantity": "Quantity", "unitPrice": "Unit price", "unit": "Unit", "discount": "Discount", "tax": "Tax", "amount": "Amount", "total": "Total", "subtotal": "Subtotal", "selectProduct": "Product", "selectDiscount": "Select Discount", "emptyCart": "Empty Cart", "startAddingProducts": "Start Adding Products", "products": "Product{{plural}}"}, "tours": {"skip": "<PERSON><PERSON>", "key": "Key", "name": "Name", "description": "Description", "add": "Add Tour", "edit": "Edit", "delete": "Delete", "create": "Create Tour", "actions": "Actions", "deleteConfirm": "Are you sure you want to delete this tour?", "successCreated": "Tour created successfully", "successUpdated": "Tour updated successfully", "successDeleted": "Tour deleted successfully", "errorDeleting": "Error deleting tour", "errorGeneric": "An error occurred", "errorLoading": "Error loading tours", "key_required": "Key is required", "name_required": "Name is required", "description_required": "Description is required", "noGuide": "This page has no guide configured.", "fetchError": "Could not load the tour steps. Please try again later.", "loadingSteps": "Loading tour steps, please wait…", "steps": {"manage_steps": "Manage steps", "add": "Add step", "edit": "Edit step", "delete": "Delete", "deleted": "Step deleted", "updated": "Step updated", "created": "Step created", "error_fetch": "Error fetching steps", "error_save": "Error saving step", "error_delete": "Error deleting step", "error_reorder": "Error reordering", "reordered": "Steps reordered", "title": "Title", "description": "Description", "selector": "Selector", "actions": "Actions", "upload": "Cover image", "drag_text": "Click or drag file to upload", "drag_hint": "Only one image file is allowed.", "confirm_delete": "Are you sure to delete this step?", "invalid_image_type": "Only JPEG, PNG, GIF or WEBP images are allowed.", "too_large": "Image must be smaller than 2MB.", "no_image": "No image"}}, "livePanel": {"searchPLive": "Search by name or number", "online": "Online", "offline": "Offline", "busy": "Busy", "away": "Away", "onCall": "On Call", "free": "Free", "ringing": "Ringing", "onPause": "On Pause", "users": "Users", "availability": "Availability", "statusCall": "Call Status", "onCallWith": "On Call With", "callTime": "Call Time", "incomingCalls": "Incoming Calls", "answered": "Answered", "notAnswered": "Not Answered", "lost": "Lost", "recalled": "Recalled", "outgoingCalls": "Outgoing Calls", "appMobile": "Mobile App", "deskPhone": "Desk Phone (IP)", "webPhone": "Web Phone", "statusCalls": "Call Statuses", "todayNbrCalls": "Number of Calls Today", "inbound": "Inbound", "outbound": "Outbound"}, "drive": {"failedToLoadItems": "Failed to load drive items. Please try again later.", "folders": "Folders", "filesInFolder": "Files in Folder", "selectFolder": "Select Folder", "selectFolderToViewFiles": "Select a folder from the tree to view its files", "noFilesInFolder": "No files in this folder", "extension": "Extension", "folder": "Folder", "path": "Path", "basicInformation": "Basic Information", "ownerInformation": "Owner Information", "dateInformation": "Date Information", "technicalInformation": "Technical Information", "locationInformation": "Location Information", "New": "New", "createFolder": "Create Folder", "createFile": "Create File", "rename": "<PERSON><PERSON>", "delete": "Delete", "upload": "Upload", "download": "Download", "view": "View", "share": "Share", "details": "Details", "edit": "Edit", "name": "Name", "type": "Type", "size": "Size", "createdAt": "Created At", "updatedAt": "Updated At", "actions": "Actions", "owner": "Owner", "createNew": "Create New", "uploadFile": "Upload File", "myDrive": "My Drive", "noFilesFound": "No files or folders found", "shareFeatureComingSoon": "Share feature coming soon", "itemDeletedSuccessfully": "Item deleted successfully", "failedToDeleteItem": "Failed to delete item. Please try again.", "itemRenamedSuccessfully": "Item renamed successfully", "failedToRenameItem": "Failed to rename item. Please try again.", "youCanOnlyUploadToFolders": "You can only upload to folders", "loadingData": "Loading data", "fileUploadedSuccessfully": "File uploaded successfully", "failedToUploadFile": "Failed to upload file. Please try again.", "folderCreatedSuccessfully": "Folder created successfully", "failedToCreateFolder": "Failed to create folder. Please try again.", "uploading": "Uploading", "processing": "Processing", "cancelUpload": "Cancel Upload", "uploadCanceled": "Upload canceled", "fileDownloadedSuccessfully": "File downloaded successfully", "failedToDownloadFile": "Failed to download file. Please try again.", "canOnlyDownloadFiles": "You can only download files, not folders", "createFolderModal": {"title": "Create New Folder", "folderName": "Folder Name", "placeholder": "Enter folder name", "required": "Please enter a folder name", "tooLong": "Name is too long", "cancel": "Cancel", "create": "Create", "tooShort": "Name is too short"}, "shareModal": {"removeSuccess": "User removed successfully", "sharedWith": "Shared ", "shareSuccess": "Share successful", "selectPermissionRequired": "Select a permission level", "selectUserRequired": "Select at least one user to share with", "noSharedUsers": "This item is not shared with anyone", "confirmRemoveUser": "Are you sure you want to remove this user?", "yes": "Yes", "no": "No", "title": "Share", "selectUsers": "Select users to share with", "selectUsersPlaceholder": "Search and select users...", "permissions": "Permissions", "permissionsPlaceholder": "Select permission level", "viewer": "Viewer", "editor": "Editor", "viewerDescription": "Can view and download", "editorDescription": "Can view, download, edit and delete", "fileDetails": "File Details", "folderDetails": "Folder Details", "owner": "Owner", "size": "Size", "extension": "Extension", "type": "Type", "createdAt": "Created At", "cancel": "Cancel", "share": "Share", "noUsersSelected": "Please select at least one user to share with", "noPermissionSelected": "Please select a permission level"}, "deleteConfirmation": {"deleteFolder": "Delete Folder", "deleteFile": "Delete File", "areYouSure": "Are you sure you want to delete", "folderWarning": "This action will permanently delete the folder and all its contents.", "fileWarning": "This action cannot be undone.", "cancel": "Cancel", "delete": "Delete"}, "renameModal": {"title": "Rename {{name}}", "name": "Name", "required": "Please enter a name.", "minText": "Name must be at least 2 characters.", "maxText": "Name must be less than 30 characters.", "placeholder": "Enter new name", "cancel": "Cancel", "save": "Save"}, "dropHere": "Drop here", "canOnlyDropIntoFolders": "You can only drop items into folders", "cannotDropIntoItself": "Cannot drop item into itself", "itemMovedSuccessfully": "{{itemName}} moved to {{folderName}} successfully", "dragDropFiles": "Drag & drop files here", "dragDropDescription": "Support for multiple files upload", "clickToUpload": "Click to upload", "or": "or", "selectedFiles": "Selected Files", "clearAll": "Clear All", "uploadFiles": "Upload", "noFilesSelected": "No files selected", "removeFile": "Remove file", "dropFilesHere": "Drop files here to upload", "releaseToUpload": "Release to upload files", "filesUploadedSuccessfully": "Files uploaded successfully", "failedToUploadFiles": "Failed to upload files", "file": "file", "files": "files", "completed": "completed", "open": "Open", "failedToOpenFile": "Failed to open file", "addFolder": "Add Folder", "newFolder": "New Folder", "enterFolderName": "Enter folder name", "folderDeletedSuccessfully": "Folder deleted successfully", "failedToDeleteFolder": "Failed to delete folder", "folderRenamedSuccessfully": "Folder renamed successfully", "failedToRenameFolder": "Failed to rename folder", "confirmDeleteFolder": "Are you sure you want to delete this folder? All contents will be permanently deleted.", "confirmDeleteFolderTitle": "Delete Folder", "loading": "Loading...", "failedToLoadSharedUsers": "Failed to load shared users", "notShared": "Not shared with anyone", "sharedWith": "Shared with", "sharedItem": "This item is shared", "andMoreUsers": "and {{count}} more...", "searchFiles": "Search files and folders...", "noResultsFound": "No results found for \"{{search}}\"", "searchResultsCount": "Found {{count}} result{{plural}} for \"{{search}}\"", "clearSearch": "Clear", "suggestedImages": "AI-Powered Image Results", "aiImageSearchDescription": "Images found using AI-powered semantic search", "clearingSearchToNavigate": "Clearing search to navigate to folder...", "scrollDownForMore": "Scroll down to see more results in the main list", "viewAllImages": "View all {{count}} images", "itemNotFound": "Item not found", "errorLoadingItemDetails": "Error loading item details", "failedToLoadDetails": "Failed to Load Details", "storage": "Storage"}, "tour": {"next": "Next", "prev": "Previous", "finish": "Finish"}, "liveChat": {"installationIn": "installation in", "recomandedForOptimalLoading": "Recommended for optimal loading", "pasteTheCodeBeforeThe": "Paste the code before the tag", "validAlternative": "Valid alternative", "testUrl": "Test URL"}, "stat": {"load_model": "Load model", "no_data": "No data available", "show_graph": "Show graph", "save_chart": "Save chart", "saveAsModel": "Save as model", "title_is_required": "Title is required", "myCharts": "My charts", "delete_selected": "Delete selected", "search_by_chart_title": "Search by chart title", "no_charts": "No charts available", "enter_title": "Enter a title", "delete": "Delete", "update_chart": "Update chart", "choose_model": "Save model", "add_chart": "Add chart", "edit_chart": "Edit chart", "stop_refresh": "Stop auto-refresh", "start_refresh": "Start auto-refresh", "addNewTable": "Add new tab", "noChartGenerated": "No chart generated", "SelectAFamilyToLoadAvailableDataModels": "Select a family to load available data models", "ChooseAModelTemplateForQuickSetup": "You can choose a data model for quick setup", "option3": "AI assistant to automatically generate visualizations", "ReadyToVisualize": "Ready to visualize", "ConfigureYourSettingsAndCreateStunningDataVisualizations": "Configure your settings and create stunning data visualizations", "ChooseYourDataFamilyCategory": "Choose your data family category", "QuickGuide": "Quick guide", "public": "Public", "private": "Private", "Refresh_Layout": "Refresh", "ChartActions": "Chart actions", "ConfigurationPanel": "Configuration panel", "createNewTab": "Create new tab", "TotalCharts": "Total charts", "of": "of", "edit": "Edit tab", "charts": "charts", "Clear": "Clear", "Created": "Created", "help_message": "I'm here to help you create professional visualizations and analyze your data effectively.", "create_chartAssistant": "Create a professional chart with advanced metrics.", "click_to_start": "Click to start", "Showing": "Showing", "professional_support": "Professional support", "ai_assistant": "AI assistant", "Cancel": "Cancel", "select_preconfigured_template": "Select a preconfigured template", "deleted_successfully": "Chart(s) deleted successfully.", "delete_error": "An error occurred while deleting the chart(s).", "tab_deleted_successfully": "Tab deleted successfully.", "tab_delete_error": "An error occurred while deleting the tab.", "TabName": "Tab name", "AfficherLaConfiguration": "Show configuration", "RéduireLaConfiguration": "Hide configuration", "Début": "Start", "Fin": "End", "AnullerLaModification": "Cancel modification", "Auto-refreshActive": "Auto-refresh active", "chartRendererNotAvailable": "Chart renderer not available", "Auto-refreshInactive": "Auto-refresh inactive", "genereleghraphique": "générer le graphique", "editing_message": "Editing message"}}