import { useState } from "react";
import SelectStages from "./SelectStages";

const SelectPipelinesStages = ({
  record,
  stages,
  iconsTasks,
  setSelectedStage,
  selectedStage,
  setList = () => {},
  setCountTasks = () => {},
  listFilter,
  source = "",
  size = "default",
}) => {
  const [pipeline, setPipeline] = useState("");

  return (
    <SelectStages
      stages={stages}
      record={record}
      iconsTasks={iconsTasks}
      setSelectedStage={setSelectedStage}
      selectedStage={selectedStage}
      setList={setList}
      setPipeline={setPipeline}
      setCountTasks={setCountTasks}
      listFilter={listFilter}
      source={source}
      size={size}
    />
  );
};

export default SelectPipelinesStages;
