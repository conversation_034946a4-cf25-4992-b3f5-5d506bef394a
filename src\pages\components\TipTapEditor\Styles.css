.templateEmail .textEditor {
  width: 100%; /* Set the desired width for the container */
  overflow-wrap: break-word; /* Enable word wrapping */
}

/* TitTap Editor Design */
.templateEmail .ProseMirror {
  padding: 10px;
  background: transparent;
  border:0;
  /* border: 0px solid #d9d9d9; */
  height: 100%;
  max-height:100%;

}
.templateEmail .ProseMirror-textarea {
  padding: 10px;
  background: white;
  height: 100%;
  border: 0px solid #d9d9d9;


}
.templateEmail .ProseMirror:focus {
  border-style: none;
  outline: none;
}
.templateEmail .ProseMirror .is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #adb5bd;
  pointer-events: none;
  height: 0;
}
.templateEmail .ProseMirror .is-empty::before {
  content: attr(data-placeholder);
  float: left;
  color: #adb5bd;
  pointer-events: none;
  height: 0;
}
/* .templateEmail .tippy-box{
  background: white !important;
  border:1px solid #e5e7eb;
  overflow-y: hidden !important;
} */
.temp #tippy-1{
  z-index: 0 !important;

}
.temp .tippy-box{
  /* display: flex; */
  width: 100% !important;
  max-width: 100% !important;
  overflow: hidden !important;
}


 .ediorNotionLike table {
  border-collapse: collapse;
  table-layout: fixed;
  width: 100%;
  margin: 0;
  overflow-x: hidden;
}

.ediorNotionLike table td,
.ediorNotionLike table th {
  min-width: 1em;
  border: 2px solid #ced4da;
  padding: 3px 5px;
  vertical-align: top;
  box-sizing: border-box;
  position: relative;
}

.ediorNotionLike table td > *,
.ediorNotionLike table th > * {
  margin-bottom: 0;
}

.ediorNotionLike table tr div {
  width: 100%;
}

.ediorNotionLike table th {
  font-weight: bold;
  text-align: left;
  background-color: #f1f3f5;
}

.ediorNotionLike table .selectedCell:after {
  z-index: 2;
  position: absolute;
  content: "";
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: rgba(200, 200, 255, 0.4);
  pointer-events: none;
}

.ediorNotionLike table .column-resize-handle {
  position: absolute;
  right: -2px;
  top: 0;
  bottom: -2px;
  width: 4px;
  background-color: #adf;
  pointer-events: none;
}

.ediorNotionLike table p {
  margin: 0;
}

.d-block-button {
  @apply bg-gray-200 hover:bg-gray-300 cursor-grab text-lg py-1 opacity-0 transition duration-200 ease-in-out text-black h-fit rounded flex justify-center items-center;
} 

ul[data-type="taskList"] {
  list-style: none;
  margin-left: 0;
  padding: 0;
}

ul[data-type="taskList"] li {
  align-items: flex-start;
  display: flex;
}

ul[data-type="taskList"] li > label {
  flex: 0 0 auto;
  margin-right: 0.5rem;
  user-select: none;
}

ul[data-type="taskList"] li > div {
  flex: 1 1 auto;
}

ul[data-type="taskList"] input[type="checkbox"] {
  cursor: pointer;
}

ul[data-type="taskList"] ul[data-type="taskList"] {
  margin: 0;
}


/* .tableWrapper {
  padding: 1rem 0;
  overflow-x: auto;
} */