import {
  UPDATE_FIELD_OPTIONS_RANK_SUCCESS,
  UPDATE_FIELD_OPTIONS_RANK_ERROR,
  UPDATE_FIELD_OPTIONS_RANK_LOADING,
} from "../../constants";
import MainService from "../../../services/main.service";

export const updateFieldOptionRank = (payload) => async (dispatch) => {
  try {
    dispatch({ type: UPDATE_FIELD_OPTIONS_RANK_LOADING });
    const response = await MainService.changeFieldOptionsRank(payload);
    dispatch({
      type: UPDATE_FIELD_OPTIONS_RANK_SUCCESS,
      payload: { response, fieldId: payload?.fieldId },
    });
  } catch (error) {
    if (error?.status !== 401) {
      dispatch({
        type: UPDATE_FIELD_OPTIONS_RANK_ERROR,
        payload: error,
      });
    }
  }
};
