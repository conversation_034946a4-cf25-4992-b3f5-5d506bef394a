import { memo } from "react";
import RenderEmailItem from "./RenderEmailItem";
import RenderFamilyItem from "./RenderFamilyItem";
import RenderTaskItem from "./RenderTaskItem";
import RenderNoteItem from "./RenderNoteItem";
import RenderDriveItem from "./RenderDriveItem";

//
export const renderTitle = (title, category) => (
  <div className="flex flex-row items-center space-x-1 font-semibold	">
    <p
      className="mr-0.5 truncate"
      dangerouslySetInnerHTML={{ __html: title }}
    />

    <p>({category})</p>
  </div>
);

export const renderHighlight = (highlight, t, source) => {
  if (
    highlight &&
    typeof highlight === "object" &&
    !Array.isArray(highlight) &&
    Object.keys(highlight).length > 0
  ) {
    const highlightKey = Object.keys(highlight)?.[0];
    const highlightValue = highlight[highlightKey];
    const displayHighlightLey =
      highlightKey === "body" ? t("globalSearch.emailBody") : highlightKey;

    return (
      <div className="flex space-x-1">
        <p className=" text-xs" style={{ whiteSpace: "nowrap" }}>
          {displayHighlightLey}:
        </p>
        <span
          className="overflow-hidden text-xs italic text-slate-500"
          dangerouslySetInnerHTML={{
            __html: `${highlightValue}`,
          }}
        />
      </div>
    );
  } else return null;
};

export const removeTagsFromText = (text) => {
  const regex = /(<([^>]+)>)/gi;
  if (text === null || text === "") return text;
  else return text.replace(regex, "");
};
//
const RenderSearchItems = ({
  item,
  t,
  imgBaseUrl,
  handleClickOnItem,
  onlineUser,
  handleJoinVisioMeeting,
}) => {
  //

  //
  switch (item.category) {
    case "email":
      return (
        <RenderEmailItem
          t={t}
          item={item}
          handleClickOnItem={handleClickOnItem}
        />
      );
    case "task":
      return (
        <RenderTaskItem
          t={t}
          item={item}
          imgBaseUrl={imgBaseUrl}
          handleClickOnItem={handleClickOnItem}
          handleJoinVisioMeeting={handleJoinVisioMeeting}
        />
      );
    case "note":
      return (
        <RenderNoteItem
          t={t}
          item={item}
          imgBaseUrl={imgBaseUrl}
          handleClickOnItem={handleClickOnItem}
        />
      );
    case "familyValue":
      return (
        <RenderFamilyItem
          t={t}
          item={item}
          imgBaseUrl={imgBaseUrl}
          onlineUser={onlineUser}
          handleClickOnItem={handleClickOnItem}
        />
      );
    case "drive":
      return (
        <RenderDriveItem
          t={t}
          item={item}
          imgBaseUrl={imgBaseUrl}
          handleClickOnItem={handleClickOnItem}
        />
      );
    default:
      return <></>;
  }
};

export default memo(RenderSearchItems);
