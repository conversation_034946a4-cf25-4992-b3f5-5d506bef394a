import React, { useEffect, useRef, useState } from "react";
import {
  Form,
  Input,
  Space,
  Button,
  Modal,
  Badge,
  Tabs,
  message,
  Upload,
  Image,
  Typography,
} from "antd";
import "./styles.css";
import {
  ExportOutlined,
  SyncOutlined,
  UploadOutlined,
} from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { useLocation } from "react-router-dom";
import ReactQuill, { Quill } from "react-quill";
import ImageResize from "quill-image-resize-module-react";
import "react-quill/dist/quill.snow.css";

import { toastNotification } from "../../components/ToastNotification";
import NewTableDraggable from "../../components/NewTableDraggable";
import Header from "../../components/configurationHelpDesk/Header";
// import ActionsHeaderTable from './ActionsHeaderTable'
import { createGroupWiki } from "../../new-redux/actions/wiki.actions/createGroup";
import { updateGroup } from "../../new-redux/actions/wiki.actions/editGroup";
import { generateAxios } from "../../services/axiosInstance";
import { setSearch } from "../../new-redux/actions/menu.actions/menu";
import { URL_ENV } from "index";
import axios from "axios";
import ButtonExport from "./ButtonExport";

const { TabPane } = Tabs;

const Binder = ({
  setGroupsWithBinders,
  setSelectedGroupWiki,
  selectedGroupWiki,
}) => {
  const [t] = useTranslation("common");

  const { groupWiki, isLoading } = useSelector((state) => state.wiki);

  const [form] = Form.useForm();
  const [count, setCount] = useState(0);
  const location = useLocation();
  const [data, setData] = useState(groupWiki);
  const [editingKey, setEditingKey] = useState("");
  const [id, setId] = useState(null);
  const [record, setRecord] = useState({});
  const [disabled, setDisabled] = useState(true);
  const [loadingExport, setLoadingExport] = useState(false);

  const [loading, setLoading] = useState("");
  const [loadingModal, setLoadingModal] = useState("");

  const isEditing = (record) => record.key === editingKey;
  const [titleModalGroup, setTitleModalGroup] = useState("");
  const [listBinder, setListBinder] = useState([]);

  const [name, setName] = useState("");
  const [newGroup, setNewGroup] = useState("");
  const [descriptionFr, setDescriptionFr] = useState("");
  const [newGroupEn, setNewGroupEn] = useState("");
  const [descriptionEn, setDescriptionEn] = useState("");
  const [newGroupEdit, setNewGroupEdit] = useState("");
  const [descriptionFrEdit, setDescriptionFrEdit] = useState("");
  const [newGroupEnEdit, setNewGroupEnEdit] = useState("");
  const [descriptionEnEdit, setDescriptionEnEdit] = useState("");

  const [image, setImage] = useState([]);
  const [imageNew, setImageNew] = useState([]);

  const [openAdd, setOpenAdd] = useState(false);
  const [openEdit, setOpenEdit] = useState(false);

  const [logo, setLogo] = useState([]);
  const [fileList, setFileList] = useState([]);
  const { search } = useSelector((state) => state.form);

  const props = {
    beforeUpload: (file) => {
      const isPNG = file.type.split("/")[0] === "image";
      if (!isPNG) {
        message.error(`${file.name} ${t("import.isNotImage")} !`);
        return Upload.LIST_IGNORE;
      }
      return false;
    },
    onChange: (info) => {
      console.log(info.fileList);
    },
  };

  const validateMessages = {
    required: "'${name}' is required!",
  };

  const modules = {
    toolbar: [
      [{ header: [1, 2, 3, 4, 5, 6, false] }],
      [{ font: [] }],
      [{ size: [] }],
      [{ color: [] }, { background: [] }],
      ["bold", "italic", "strike", "blockquote"],
      [
        { list: "ordered" },
        { list: "bullet" },
        { indent: "-1" },
        { indent: "-1" },
      ],
      // ['emoji'],
      ["clean"],
    ],
    // 'emoji-toolbar': true,
    // 'emoji-textarea': true,
    // 'emoji-shortname': true,
  };

  const dispatch = useDispatch();

  useEffect(() => {
    const getBinder = async () => {
      setLoading(true);
      try {
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get("classeur-wikis");
        setListBinder(res.data.data.map((el) => ({ ...el, key: el.id })));
        setLoading(false);
      } catch (err) {
        setLoading(false);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    };
    getBinder();
    return () => dispatch(setSearch(""));
  }, []);

  const handleCancel = () => {
    setEditingKey("");
    setOpenAdd(false);
    form.resetFields();
    setLogo([]);
  };
  const handleCancelEdit = () => {
    setEditingKey("");
    setOpenEdit(false);
    form.resetFields();
    setLogo([]);
  };

  const inputRefs = useRef([]);
  useEffect(() => {
    inputRefs.current.forEach((input) => {
      input?.focus();
    });
  }, [data.length, id]);
  const onFinish = async (values) => {};
  const onFinishFailed = (values) => {
    console.log(values);
  };
  const handleExport = async (fileUrl) => {
    setLoadingExport(true);
    try {
      const response = await axios.get(fileUrl, { responseType: "blob" });
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "document.pdf");
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      setLoadingExport(true);
    } catch (error) {
      console.error("Erreur lors du téléchargement du document", error);
    }
  };
  const edit = (record) => {
    setDisabled(true);
    setLogo(
      record?.image?.length
        ? [
            {
              uid: record.id,
              name: record.image[0].fileName,
              status: "done",
              url: `${
                URL_ENV?.REACT_APP_BASE_URL +
                process.env.REACT_APP_SUFFIX_AUTH_IMAGE_FILE
              }uploads-wiki/${record.image[0].fileName}`,
            },
          ]
        : []
    );
    setRecord(record);
    setId(record.id);
    setTitleModalGroup(t(`wiki.EditBinder`));
    setEditingKey(record.key);
    form.setFieldsValue({
      newGroup: record.label_fr,
      newGroupEn: record.label_en,
      descriptionFr: record.description_fr,
      descriptionEn: record.description_en,
      image: record?.image,
      icon: record.icon,
    });
    setOpenAdd(true);
  };

  const cancel = (record) => {
    setEditingKey("");

    setId(null);
    if (!record.id) {
      setData(data.filter((item) => item.key !== record.key));
    }
  };

  const save = async (key) => {
    setLoadingModal(true);
    if (id) {
      try {
        if (!form.getFieldValue().newGroup)
          toastNotification("error", t("wiki.TitleRequired"), "topRight");
        else if (logo[0].length === 0)
          toastNotification("error", t("wiki.ImageRequired"), "topRight");
        else {
          const res = await generateAxios(
            URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
          ).post(`classeur-wikis/update/${id}`, {
            label_fr: form.getFieldValue().newGroup,
            label_en: form.getFieldValue().newGroupEn,
            description_fr: form.getFieldValue().descriptionFr,
            description_en: form.getFieldValue().descriptionEn,
            image: logo[0]?.originFileObj,
          });
          setListBinder((prev) =>
            prev.map((el) =>
              el.id === id ? { ...res.data.data, key: res.data.data.id } : el
            )
          );
          setGroupsWithBinders((prev) =>
            prev.map((el) =>
              el.value === id ? { ...el, label: res.data.data.label_fr } : el
            )
          );
        }
        setLoadingModal(false);
        setOpenAdd(false);
        setEditingKey("");
        form.resetFields();
        setLogo([]);
        toastNotification(
          "success",
          key.newGroup + " " + t("toasts.edit"),
          "topRight"
        );

        // form.resetFields()
        // setLogo([])
      } catch (errInfo) {
        setLoadingModal(false);

        //setLoading(false);
        console.log(errInfo);
        if (errInfo?.response?.status === 409) {
          toastNotification(
            "error",
            t(`wiki.${errInfo?.response?.data?.errors[0]}`),
            "topRight"
          );
        } else
          toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    } else {
      try {
        //setOpenAdd(true)
        // if (!logo[0]?.originFileObj) {
        //   toastNotification(
        //   "error",
        //   t(`wiki.ImageRequired`),
        //   "topRight"
        // );
        // }

        if (!form.getFieldValue().newGroup)
          toastNotification("error", t("wiki.TitleRequired"), "topRight");
        else if (!logo[0]?.originFileObj)
          toastNotification("error", t("wiki.ImageRequired"), "topRight");
        else {
          const res = await generateAxios(
            URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
          ).post("classeur-wikis", {
            label_fr: form.getFieldValue().newGroup,
            label_en: form.getFieldValue().newGroupEn,
            description_fr: form.getFieldValue().descriptionFr,
            description_en: form.getFieldValue().descriptionEn,
            image: logo[0]?.originFileObj,
          });
          setListBinder((prev) => [
            ...prev,
            { ...res.data.data, key: res.data.data.id },
          ]);

          setGroupsWithBinders((prev) => [
            ...prev,
            {
              value: res.data.data.id,
              label: res.data.data.label_fr,
              options: [],
            },
          ]);
          setEditingKey("");
          setLoadingModal(false);
          setOpenAdd(false);
          toastNotification(
            "success",
            key.newGroup + " " + t("toasts.created"),
            "topRight"
          );
          form.resetFields();
          setLogo([]);
        }
      } catch (errInfo) {
        //setLoading(false);
        console.log(errInfo);

        setLoadingModal(false);

        if (errInfo?.response?.status === 409) {
          toastNotification(
            "error",
            t(`wiki.${errInfo?.response?.data?.errors[0]}`),
            "topRight"
          );
        } else
          toastNotification("error", t("toasts.somethingWrong"), "topRight");
        console.log("Validate Failed:", errInfo);
      }
    }
  };

  const handleKeyPress = (event) => {
    if (event.key === "Enter") {
      form.submit();
    }
  };
  const exportPdf = async (id) => {
    window.open(
      `http://51.255.8.13:5000/api/generate-pdf/${id}`,
      "_blank",
      "noreferrer"
    );
    // setLoading(true);
    // try {
    //   const res = await axios.get(
    //     `http://51.255.8.13:5000/api/generate-pdf/${id}`
    //   );
    //   setLoading(false);
    // } catch (err) {
    //   toastNotification("error", t("toasts.somethingWrong"), "topRight");

    //   setLoading(false);
    // }
  };

  const columns = [
    {
      title: t(`wiki.TitleFolderFr`),
      dataIndex: "product",
      key: "product",
      //editable: true,
      sorter: (a, b) => a.label_fr.localeCompare(b.label_fr),
      //width: '47.5%',
      render: (_, props) => (
        <div className="flex justify-between">
          <Typography.Link
            onClick={() => {
              edit(props);
            }}
          >
            {props.label_fr}
          </Typography.Link>
        </div>
      ),
    },
    {
      title: t(`wiki.TitleFolderEn`),
      dataIndex: "label_en",
      key: "label_en",
      //editable: true,
      sorter: (a, b) => a?.label_en?.localeCompare(b?.label_en),
      // width: '47.5%'
    },

    {
      title: t(`wiki.Image`),
      dataIndex: "logo",
      key: "logo",
      render: (_, logo) =>
        logo?.image?.length && (
          <Image
            src={
              logo?.image?.length &&
              `${
                URL_ENV?.REACT_APP_BASE_URL +
                process.env.REACT_APP_SUFFIX_AUTH_IMAGE_FILE
              }${logo?.image[0]?.path}`
            }
            width={30}
          />
        ),
      //width: '5%'
      //editable: true,
    },
    {
      // title: t(`export pdf`),
      render: (_, { id }) => (
        <>
          <ButtonExport
            fileUrl={`${URL_ENV?.REACT_APP_WIKIURL}/api/generate-pdf-classeur/${id}`}
            id={id}
          />
        </>
      ),

      //editable: true,
      // width: '47.5%'
    },
    // {
    //   // title: t(`export pdf`),
    //   render: (_, props) => (
    //     <Button icon={<ExportOutlined />}>export pdf</Button>
    //   ),

    //   //editable: true,
    //   // width: '47.5%'
    // },
    // {
    //   // title: t(`export pdf`),
    //   render: (_, props) => <Button icon={<SyncOutlined />}>Build</Button>,

    //   //editable: true,
    //   // width: '47.5%'
    // },
  ];

  const handleAdd = () => {
    setLogo([]);
    const ids = data.map((object) => {
      return object.id;
    });
    setId(null);
    setTitleModalGroup(t(`wiki.AddBinder`));

    setEditingKey(Math.max(...ids) + 1);
    setCount(Math.max(...ids) + 1);
    setOpenAdd(true);
  };

  const onRow = () => {};
  const onValuesChange = (changedValues, allValues) => {
    // let myButton = document.getElementById("buttonSaveTable");
    let commonAttributes = {};
    let test = {
      newGroup: record.label_fr,
      descriptionEn: record.description_en || "<p><br></p>",
      descriptionFr: record.description_fr || "<p><br></p>",
      image: record.image,
      newGroupEn: record.label_en,
      classeur_id: record.classeur_id,
    };

    // let svgElement = myButton.querySelector("svg");

    // Obtenir la liste des clés de l'objet plus petit
    let newValues = {
      ...allValues,
      descriptionEn:
        allValues.descriptionEn || record.description_en || "<p><br></p>",
      descriptionFr:
        allValues.descriptionFr || record.description_fr || "<p><br></p>",
      newGroupEn: allValues.newGroupEn || record.label_en,
    };
    let smallerKeys = Object.keys(newValues);

    // Filtrer les attributs de l'objet plus grand en fonction de la liste des clés
    test &&
      smallerKeys.forEach(function (key) {
        if (key in test) {
          commonAttributes[key] = test[key] || "";
        }
      });

    let trimmedJsonObject = Object.keys(newValues).reduce((acc, key) => {
      if (typeof newValues[key] === "string") {
        acc[key] = newValues[key].trim();
      } else {
        acc[key] = newValues[key] || "";
      }
      return acc;
    }, {});
    if (
      JSON.stringify(commonAttributes) !== JSON.stringify(trimmedJsonObject)
    ) {
      setDisabled(false);
    } else {
      setDisabled(true);
    }
  };

  const dataTable =
    // data
    //  &&
    // data
    //   .map((element, i) => ({
    //     id: element?.id,
    //     product: element,
    //     logo: element?.logo,
    //     label_fr: element?.label_fr,
    //     label_en: element?.label_en,
    //     description_fr: element?.description_fr,
    //     description_en: element?.description_en,
    //     image: element?.image,
    //     key: element?.key,
    //     value: element?.value,
    //     default: element?.default,
    //   }))
    listBinder.filter((item) => {
      return (
        item?.label_fr?.toLowerCase()?.includes(search?.toLowerCase()) ||
        item?.label_en?.toLowerCase()?.includes(search?.toLowerCase())
      );
    });
  return (
    <>
      <Space direction="vertical" style={{ width: "100%" }}>
        <Header
          active={"5"}
          editingKey={editingKey}
          handleAdd={handleAdd}
          btnText={t(`wiki.AddBinder`)}
        />

        <NewTableDraggable
          columns={columns}
          setLoading={setLoading}
          isEditing={isEditing}
          data={dataTable}
          setData={setListBinder}
          loading={loading}
          save={save}
          edit={edit}
          //EditableCell={EditableCell}
          onFinishFailed={onFinishFailed}
          cancel={cancel}
          form={form}
          apiRank="/rank-classeur"
          editingKey={editingKey}
          api="classeur-wikis"
          onRow={onRow}
          setGroupsWithBinders={setGroupsWithBinders}
          setSelectedGroupWiki={setSelectedGroupWiki}
          selectedGroupWiki={selectedGroupWiki}
        />
      </Space>
      <Modal
        title={titleModalGroup}
        open={openAdd}
        onCancel={handleCancel}
        footer={[
          <Button key="back" onClick={handleCancel}>
            {t(`wiki.Cancel`)}
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={() => form.submit()}
            loading={loadingModal}
            disabled={
              titleModalGroup === t(`wiki.EditBinder`) ? disabled : false
            }
            // disabled={
            //   form.getFieldValue().newGroup === undefined ||
            //   form.getFieldValue().newGroup === ""
            // }
          >
            {t(`wiki.Ok`)}
          </Button>,
        ]}
        className="my-modal"
      >
        <Form
          name="formItemPath"
          layout="vertical"
          onFinish={save}
          onValuesChange={onValuesChange}
          // onFinishFailed={onFinishFailed}
          validateMessages={validateMessages}
          form={form}
          id="form"
        >
          <Form.Item
          //name="content"
          >
            {/* <Tabs defaultActiveKey="1" items={items} onChange={onChange} /> */}

            <Tabs>
              <TabPane
                tab={
                  <div className="mx-4 flex pb-0.5">
                    <span>{t(`wiki.French`)}</span>
                    <Badge />
                  </div>
                }
                className="TabsBody"
              >
                <Form.Item
                  name="newGroup"
                  label={t(`wiki.Title`)}
                  rules={[
                    {
                      required: true,
                      message: t(`wiki.TitleRequired`),
                    },
                  ]}
                  //validateFirst="parallel"
                >
                  <Input
                  //style={{ width: '100%' }}
                  // onChange={(e) => setNewGroup(e.target.value)}
                  //onChange={handleInputChange}
                  // let arr = e.target.value.split(' ')
                  // let text = arr.join('_')

                  // setSlugUrlValue(
                  //   `${
                  //     form.getFieldValue().folderName
                  //   }_${text}`.toLowerCase(),
                  // )
                  //setPageNameFr(e.target.value)
                  />
                </Form.Item>
                <Form.Item
                  name="descriptionFr"
                  label={t(`wiki.Description`)}
                  // style={{
                  //   display: 'inline-block',
                  //   width: 'calc(50% - 8px)',
                  //   margin: '0 8px ',
                  // }}
                  initialValue={"<p><br></p>"}
                >
                  {/* <ReactQuill
                    theme="snow"
                    value={descriptionFr}
                    modules={modules}
                    //style={{ height: '380px' }}
                    onChange={handleChangeFr}
                  /> */}
                  <ReactQuill theme="snow" />
                </Form.Item>
              </TabPane>
              <TabPane
                tab={
                  <div className="mx-4 flex pb-0.5">
                    <span>{t(`wiki.English`)}</span>
                    <Badge />
                  </div>
                }
                className=""
                key="wiki-config"
              >
                <Form.Item name="newGroupEn" label={t(`wiki.Title`)}>
                  <Input
                    style={{ width: "100%" }}
                    // onChange={(e) => setNewGroupEn(e.target.value)}
                  />
                </Form.Item>
                <Form.Item
                  name="descriptionEn"
                  label={t(`wiki.Description`)}
                  initialValue={"<p><br></p>"}
                >
                  <ReactQuill theme="snow" />
                </Form.Item>
              </TabPane>
            </Tabs>
          </Form.Item>

          <Form.Item
            name="image"
            label={t(`wiki.Image`)}
            rules={[
              {
                required: true,
                message: t("wiki.ImageRequired"),
              },
            ]}
            className=""
          >
            <Upload
              {...props}
              accept="image/*"
              listType="picture"
              onChange={(e) => {
                setLogo(e.fileList);
                e.fileList.length < 1 && form.setFieldsValue({ image: "" });
              }}
              fileList={logo}
              multiple={false}
              className="upload-list-inline uploadGroupWiki "
            >
              {logo.length < 1 && (
                <Button icon={<UploadOutlined />}>
                  {t(`wiki.UploadImage`)}
                </Button>
              )}
            </Upload>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};
export default Binder;
