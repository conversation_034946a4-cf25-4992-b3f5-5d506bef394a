import {
  ADD_ROW_FAMILY,
  ADD_ROW_TYPE,
  ADD_ROW_UNITY,
  ADD_ROW_TYPE_CONTACT,
  ADD_ROW_TYPE_ACTIVITY,
  ADD_ROW_TAG,
  IS_DELETE_ROWS,
} from "../../constants";
export const addRowinTableFamily = (payload) => ({
  type: ADD_ROW_FAMILY,
  payload: payload,
});

export const addRowinTableType = (payload) => ({
  type: ADD_ROW_TYPE,
  payload: payload,
});
export const addRowinTableUnity = (payload) => ({
  type: ADD_ROW_UNITY,
  payload: payload,
});
export const addRowinTableTypeContact = (payload) => ({
  type: ADD_ROW_TYPE_CONTACT,
  payload: payload,
});

export const addRowinTableTypeActivity = (payload) => ({
  type: ADD_ROW_TYPE_ACTIVITY,
  payload: payload,
});
export const addRowinTableTag = (payload) => ({
  type: ADD_ROW_TAG,
  payload: payload,
});
export const changeIsDeleteRows = (payload) => ({
  type: IS_DELETE_ROWS,
  payload: payload,
});
