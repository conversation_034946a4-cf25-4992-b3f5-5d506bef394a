import React, { useRef, useState } from "react";
import ActionsStarredImportant from "./actionsStarredImportant";
import {
  <PERSON><PERSON>,
  Badge,
  But<PERSON>,
  Divider,
  Popconfirm,
  Popover,
  Tag,
  Tooltip,
} from "antd";
import { BiTransferAlt } from "react-icons/bi";
import Chrono from "./chrono";
import { URL_ENV } from "index";
import { TbChecks } from "react-icons/tb";

import { useDispatch } from "react-redux";
import StateEmail from "./stateEmail";
import MarkDown from "./Markdown";
import Identification from "./identification";
import { useSelector } from "react-redux";

import Affectation from "./Affectation";
import Qualification from "./Qualification";
import Assign from "./Assign";
import moment from "moment";
import { formatDateForDisplay } from "pages/tasks/helpers/formatDateToDisplay";
import { FaCalendar } from "react-icons/fa";
import PopoverTask from "./components/popoverTask";
import DropdownActionsTable from "./dropdownActionsTable";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import {
  generateUrlToView360,
  truncateString,
} from "pages/voip/helpers/helpersFunc";
import {
  ArrowDownOutlined,
  ArrowUpOutlined,
  CloseOutlined,
  QuestionCircleOutlined,
} from "@ant-design/icons";
import { MdLabel } from "react-icons/md";
import { deleteLabelFromMail } from "./services/ActionsApi";
import { MailX } from "lucide-react";

const useColumnInbox = ({
  searchEmail,
  getMailsInbox,
  // getMailsSearchInbox,
  usedAccount,
  setThirdId,
  setOpenLogDrawer,
  t,
  setEmailId,
  setOpenModal,
  setTypeDelete,
  dataMailInbox,
  setDataMailInbox,
  setOpenTask,
  conditionActions,
  setIsHovered,
  isHovered,
  dataTags,
  setMailingProps,
  setFamilyId,
  setTaskId,
  setOpenForm,
}) => {
  const [clickArchive, setClickArchive] = useState(false);

  const { user } = useSelector(({ user }) => user);
  const access = user.access || {};
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const widthRef = useRef(null);
  // const [columnWidth, setColumnWidth] = useState(0);
  // const [screenWidth, setScreenWidth] = useState(window.innerWidth);
  const { i18n } = useTranslation("common");
  const specialCharacters = (query) => {
    try {
      if (!query) return "";
      return query.replace(/[`~!$%^&*|+\/,-=?;:'"<>]/gi, "");
    } catch (e) {
      return query;
    }
  };

  // console.log({ dataMailInbox });

  const contentSender = (record, item, type) => {
    let idUrl = "";
    if (type === "single") {
      idUrl = record.identification?.id;
    } else {
      idUrl = item?.identification?.id;
    }

    return (
      <>
        <div className="flex items-center space-x-4">
          <div className="w-[40px]">
            {type === "multiple" && item?.identification?.avatar ? (
              <Avatar
                style={{
                  marginRight: "10px",
                  marginBottom: "4px",
                }}
                size={40}
                src={
                  <img
                    src={`${
                      URL_ENV?.REACT_APP_BASE_URL +
                      URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                    }${item?.identification?.avatar}`}
                    alt="name"
                  />
                }
              />
            ) : type === "single" && record?.identification?.avatar ? (
              <Avatar
                style={{
                  marginRight: "10px",
                  marginBottom: "4px",
                }}
                size={40}
                src={
                  <img
                    src={`${
                      URL_ENV?.REACT_APP_BASE_URL +
                      URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                    }${record?.identification?.avatar}`}
                    alt="name"
                  />
                }
              />
            ) : (
              <Avatar
                style={{
                  backgroundColor: "#f56a00",
                }}
                size={40}
              >
                <p
                  style={{
                    backgroundColor: "#f56a00",
                    fontSize: "20px",
                    fontWeight: "400",
                  }}
                >
                  {item?.name
                    ? item?.name
                        ?.replace(/[^A-Za-z]/g, "")
                        ?.charAt(0)
                        ?.toUpperCase() || "?"
                    : item?.address
                        ?.replace(/[^A-Za-z]/g, "")
                        ?.charAt(0)
                        ?.toUpperCase() || "?"}
                </p>
              </Avatar>
            )}
          </div>

          <div className="w-full">
            <div className="flex justify-between">
              <p className=" truncate text-[16px] font-semibold">
                {type === "multiple"
                  ? item?.identification?.label_data ??
                    item?.from?.name ??
                    item?.from?.address?.split("@")[0].replace(/\./g, " ")
                  : record?.identification?.label_data ??
                    record?.from?.name ??
                    record?.from?.address?.split("@")[0].replace(/\./g, " ")}
              </p>
              {/* <IoPersonAddOutline
                style={{
                  fontSize: "18px",
                  color: "#635c5c",
                  cursor: "pointer",
                }}
              /> */}
            </div>
            <p className=" truncate text-[13px] ">{item?.address}</p>
          </div>
        </div>
        {idUrl ? (
          <>
            <Divider plain className="my-2 bg-gray-300" />
            <div className="flex items-center">
              <Button
                type="link"
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(
                    generateUrlToView360(
                      type === "single"
                        ? record?.identification?.family_id
                        : item?.identification?.family_id,
                      type === "single"
                        ? record?.identification?.id
                        : item?.identification?.id,
                      "v2"
                    )
                  );
                }}
              >
                {" "}
                {t("mailing.detailed")}
              </Button>
            </div>
          </>
        ) : null}
      </>
    );
  };

  const markSender = (text) => {
    if (text?.address?.includes("<mark>")) return text?.address;
    else return text?.name ?? text?.address;
  };

  const screenWidth = window.innerWidth;

  const columns = [
    {
      dataIndex: "starred",
      // width: "40px",
      width: "90px",
      fixed: "left",
      onCell: () => ({
        onClick: (event) => {
          event.stopPropagation(); // Prevent navigation on click
        },
      }),
      render: (_, record) => (
        <div className="mr-2 flex items-center ">
          <ActionsStarredImportant
            record={record}
            getMails={
              // searchEmail?.length > 0 ? getMailsSearchInbox :
              getMailsInbox
            }
            usedAccount={usedAccount}
          />
          {usedAccount?.shared == 1 && (record.box == 0 || record.box == 2) ? (
            <div onClick={(e) => e.stopPropagation()}>
              <StateEmail
                idEmail={record?.key}
                usedAccount={usedAccount}
                state={record.state}
                getMails={
                  // searchEmail?.length > 0 ? getMailsSearchInbox :
                  getMailsInbox
                }
                type="inbox"
                disabled={conditionActions(record) ? false : true}
              />
            </div>
          ) : null}
        </div>
      ),
    },

    {
      title: t("mailing.sender"),
      dataIndex: "from",
      // width: usedAccount?.shared == 0 ? "250px" : "320px",
      // width: "25%",
      fixed: "left",
      render: (text, record) => {
        return (
          <div className=" relative flex items-center justify-between">
            <div className="flex items-center space-x-0.5">
              {/* <div className="mr-2 flex items-center">
                {usedAccount?.shared == 1 ? (
                  <div onClick={(e) => e.stopPropagation()}>
                    <StateEmail
                      idEmail={record?.key}
                      usedAccount={usedAccount}
                      state={record.state}
                      getMails={
                        searchEmail?.length > 0
                          ? getMailsSearchInbox
                          : getMailsInbox
                      }
                      type="inbox"
                      disabled={conditionActions(record) ? false : true}
                    />
                  </div>
                ) : null}
              </div> */}

              <div className="mr-2 cursor-help items-center">
                <Tooltip
                  title={
                    record.box === 0 || record.box === 2
                      ? t("mailing.NewMsg.received")
                      : t("mailing.NewMsg.sent")
                  }
                >
                  {record.box === 0 || record.box === 2 ? (
                    <ArrowDownOutlined
                      style={{ color: "green", fontSize: 14 }}
                    />
                  ) : (
                    <ArrowUpOutlined style={{ color: "blue", fontSize: 14 }} />
                  )}
                </Tooltip>
              </div>

              <div className="ml-2 w-[30px]">
                {record?.identification?.avatar ? (
                  <Avatar
                    style={{
                      marginRight: "10px",
                      marginBottom: "4px",
                    }}
                    size={30}
                    src={`${
                      URL_ENV?.REACT_APP_BASE_URL +
                      URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                    }${record?.identification?.avatar}`}
                  />
                ) : (
                  <Avatar
                    style={{
                      backgroundColor: "#f56a00",
                    }}
                    size={30}
                  >
                    {record.from?.name
                      ? record.from?.name
                          ?.replace(/<[^>]*>/g, "")
                          ?.replace(/[^A-Za-z]/g, "")
                          ?.charAt(0)
                          ?.toUpperCase() || "?"
                      : record.from?.address
                          ?.replace(/<[^>]*>/g, "")
                          ?.replace(/[^A-Za-z]/g, "")
                          ?.charAt(0)
                          ?.toUpperCase() || "?"}
                  </Avatar>
                )}
              </div>
              <div>
                <div className="flex max-w-[190px] items-center truncate">
                  {record?.fromThread?.length > 0 ? (
                    record?.fromThread.map((item, i) => {
                      return (
                        <Popover
                          key={`popover-${i}`}
                          content={contentSender(record, item, "multiple")}
                          overlayInnerStyle={{
                            width: "auto",
                          }}
                          // trigger="hover"
                        >
                          <p
                            key={i}
                            className="ml-3 max-w-[120px] truncate"
                            style={{
                              marginLeft: "4px",
                              cursor: "pointer",
                              fontWeight: record.seen === 0 ? "bold" : "",
                            }}
                            // onMouseEnter={() => handleMouseEnter(record, i)}
                            // onMouseLeave={handleMouseLeave}
                          >
                            {/* {item?.name ?? item?.address?.split("@")[0]} */}
                            <span
                              dangerouslySetInnerHTML={{
                                __html: searchEmail.length
                                  ? markSender(text)
                                  : item?.name ?? item?.address?.split("@")[0],
                              }}
                            />

                            {i < record?.fromThread?.length - 1 && ", "}
                          </p>
                        </Popover>
                      );
                    })
                  ) : (
                    <Popover
                      content={contentSender(record, text, "single")}
                      overlayInnerStyle={{ width: "auto" }}
                    >
                      <p
                        className="ml-3 max-w-[170px] truncate"
                        style={{
                          marginLeft: "4px",
                          cursor: "pointer",
                          fontWeight: record.seen === 0 ? "bold" : "",
                        }}
                      >
                        <span
                          dangerouslySetInnerHTML={{
                            __html: searchEmail.length
                              ? markSender(text)
                              : text?.name ?? text?.address?.split("@")[0],
                          }}
                        />
                        {/* {specialCharacters(
                          text?.name ?? text?.address?.split("@")[0]
                        )} */}
                      </p>
                    </Popover>
                  )}
                </div>
              </div>
            </div>

            <div className="action-mail space-x-0.5">
              {" "}
              {record?.transfert?.account_id ? (
                <Tooltip
                  // title={`Cet email a été déplacé par ${record?.transfert?.email_source} à ${record?.transfert?.email} `}
                  title={
                    t("mailing.emailTransfer") +
                    ` ${record?.transfert?.label_data_owner}  ${
                      record?.transfert?.email_source ? t("mailing.from") : ` `
                    }   ${record?.transfert?.email_source ?? ""} ` +
                    t("mailing.to") +
                    ` ${record?.transfert?.email}  ${
                      moment(record?.transfert?.date_transfert)
                        .locale(i18n.language)
                        .format("LLLL")
                        ? t("mailing.in")
                        : ``
                    }  ${
                      moment(record?.transfert?.date_transfert)
                        .locale(i18n.language)
                        .format("LLLL") ?? ""
                    }`
                  }
                >
                  <BiTransferAlt className="h-4 w-4 text-[#4f60e2]" />
                </Tooltip>
              ) : !record?.chrono_now &&
                !record?.transfert?.account_id ? null : record?.chrono_now &&
                record?.transfert?.account_id ? null : null}
              {record?.status_processing_duration !== null ? (
                <Chrono
                  chronoNow={record?.chrono_now}
                  expireTime={record?.expire_Processing_Time}
                  processDuration={record?.status_processing_duration}
                />
              ) : !record?.chrono_now &&
                record?.transfert?.account_id ? null : null}
              {record.isBounced && (
                <Tooltip title={t("mailing.bouncedMail")}>
                  <MailX
                    size={16}
                    strokeWidth={2}
                    className="cursor-help text-yellow-500"
                  />
                </Tooltip>
              )}
              {text?.nbr > 1 ? (
                <Badge
                  style={{
                    backgroundColor: "transparent",
                    border: "none",
                    outline: "none",
                    margin: 0,
                    color: "gray",
                    fontSize: 12,
                    fontWeight: record?.seen === 0 ? "bold" : "normal",
                  }}
                  count={text?.nbr}
                ></Badge>
              ) : null}
              <div className=" ml-1 ">
                <DropdownActionsTable
                  record={record}
                  setThirdId={setThirdId}
                  setOpenLogDrawer={setOpenLogDrawer}
                  t={t}
                  // setIdentif={setIdentif}
                  conditionActions={conditionActions}
                  usedAccount={usedAccount}
                  dataMailInbox={dataMailInbox}
                  setDataMailInbox={setDataMailInbox}
                  user={user}
                  access={access}
                  dataTags={dataTags}
                  setOpenTask={setOpenTask}
                  setEmailId={setEmailId}
                  setOpenModal={setOpenModal}
                  setTypeDelete={setTypeDelete}
                  clickArchive={clickArchive}
                  getMailsInbox={getMailsInbox}
                  setClickArchive={setClickArchive}
                  type="inbox"
                  setMailingProps={setMailingProps}
                  setOpenForm={setOpenForm}
                />
              </div>
              <div>{record.new ? <Tag color="processing">New</Tag> : ""}</div>
            </div>
          </div>
        );
      },
    },

    usedAccount?.shared == 0
      ? {
          title: t("mailing.Inbox.subject"),
          dataIndex: "subject",
          // width: "100px",
          ellipsis: true,
          render: (text, record) => {
            return (
              <div
                style={{
                  cursor: "pointer",
                  fontWeight: record.seen === 0 ? "bold" : "",
                }}
              >
                <div className="max-w-[180px]">
                  {/* <MarkDown>{text}</MarkDown> */}
                  <span dangerouslySetInnerHTML={{ __html: text }} />
                </div>
              </div>
            );
          },
        }
      : null,

    usedAccount?.shared == 1
      ? {
          title: t("mailing.assigned"),
          dataIndex: "owner",
          // width: "10%",
          width: "95px",
          key: "owner",
          align: "center",
          render: (text, record) => (
            <div
              onMouseEnter={() => setIsHovered(record.key)}
              onMouseLeave={() => setIsHovered(false)}
              onClick={(e) => {
                e.stopPropagation();
              }}
            >
              <Assign
                key={record.key}
                idEmail={record.key}
                owner={record?.owner}
                setDataMail={setDataMailInbox}
                dataMailInbox={dataMailInbox}
                type="inbox"
                transfert={record?.transfert}
                circleDelete={true}
                isHovered={isHovered}
                setIsHovered={setIsHovered}
              />
            </div>
          ),
        }
      : null,

    {
      title:
        usedAccount?.shared == 1
          ? t("mailing.Inbox.subject")
          : t("mailing.Inbox.message"),
      // width: screenWidth > 1520 ? "150px" : "70px",
      ellipsis: true,
      dataIndex: usedAccount?.shared == 1 ? "subject" : "body",

      render: (text, record) => {
        return (
          <div
            className="flex cursor-pointer items-center justify-between "
            ref={widthRef}
          >
            <div className="">
              <div className="flex items-center space-x-1">
                {usedAccount?.shared == 1 &&
                  (record?.userId?.length > 0 ? (
                    <Tooltip
                      title={record.userId?.map((item, index) => (
                        <React.Fragment key={item?.label_data + "_" + index}>
                          {item.avatar?.length > 0 ? (
                            <Tooltip
                              key={
                                item?.label_data +
                                "_" +
                                item?.avatar +
                                "_" +
                                index
                              }
                              title={item?.label_data}
                              placement="bottom"
                            >
                              <Avatar
                                style={{
                                  cursor: "pointer",
                                  // marginBottom: "4px",
                                  marginRight: "4px",
                                }}
                                size={28}
                                height={8}
                                width={8}
                                src={
                                  <img
                                    src={`${
                                      URL_ENV?.REACT_APP_BASE_URL +
                                      URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                                    }${item?.avatar}`}
                                    alt="A"
                                  />
                                }
                              />
                            </Tooltip>
                          ) : (
                            <Tooltip
                              title={item?.label_data}
                              placement="bottom"
                            >
                              <Avatar
                                style={{
                                  cursor: "pointer",
                                  marginRight: "4px",
                                  backgroundColor: "#c41d7f",
                                  // marginBottom: "4px",
                                }}
                                size={28}
                                height={8}
                                width={8}
                              >
                                {item?.label_data
                                  ?.replace(/[^A-Za-z]/g, "")
                                  ?.charAt(0)
                                  ?.toUpperCase() ||
                                  "?" ||
                                  "?"}
                              </Avatar>
                            </Tooltip>
                          )}
                        </React.Fragment>
                      ))}
                      color="#dedfe5"
                    >
                      <TbChecks className="h-4 w-4 text-[#41b5e0]" />
                    </Tooltip>
                  ) : null)}
                {/* <div
                  className={`max-w-[350px] overflow-hidden text-ellipsis whitespace-nowrap 
  group-hover:max-w-[400px] lg:max-w-[300px] 2xl:max-w-[470px] 3xl:max-w-[650px]`}
                > */}
                {/* <div className="max-w-[350px]lg:max-w-[300px] truncate 2xl:max-w-[470px] 3xl:max-w-[650px]"> */}

                <p
                  className={`block overflow-hidden text-ellipsis whitespace-nowrap`}
                  style={{
                    fontWeight: record?.seen === 0 ? "bold" : "",
                    cursor: "pointer",
                    maxWidth: `${Math.max(window.innerWidth * 0.3, 150)}px`, // Adjusts based on screen size (30% of screen width, minimum 150px)
                  }}
                >
                  <span dangerouslySetInnerHTML={{ __html: text }} />
                  {/* <MarkDown>
                  </MarkDown> */}
                  {/* {text} */}
                </p>
                {/* </div> */}
              </div>

              <div
                className=" flex items-center space-x-2"
                onClick={(e) => e.stopPropagation()}
              >
                {!!record?.labelEmail?.length &&
                  record.labelEmail.map((label) => (
                    <div
                      key={label.id}
                      className={`inline-flex items-center space-x-0.5 rounded-md bg-gray-50 px-1.5 py-0.5 text-xs font-medium text-gray-600 ring-1 ring-inset ring-gray-500/10`}
                    >
                      <MdLabel
                        style={{
                          fontSize: 18,
                          color:
                            usedAccount.labels?.find((l) => l.id === label.id)
                              ?.color || "rgb(107 114 128)",
                        }}
                      />
                      <span className="font-semibold">
                        {truncateString(label?.label, 15, true)}
                      </span>

                      <Popconfirm
                        title={t("mailing.removeLabelFromMail")}
                        okText={t("voip.yes")}
                        cancelText={t("voip.no")}
                        onConfirm={() =>
                          dispatch(
                            deleteLabelFromMail(
                              usedAccount.value,
                              label.id,
                              record.third_id
                            )
                          )
                        }
                        icon={
                          <QuestionCircleOutlined
                            style={{
                              color: "red",
                            }}
                          />
                        }
                      >
                        <Button
                          size="small"
                          shape="circle"
                          type="text"
                          icon={<CloseOutlined />}
                        />
                      </Popconfirm>
                    </div>
                  ))}
                {record.identification?.label_data ||
                (record?.nbr > 1 &&
                  // record?.fromThread[0]?.identification?.label_data
                  record.fromThread?.find(
                    (thread) => thread.identification?.label_data
                  )) ? (
                  <Identification
                    id={record.key}
                    fromName={record.from.name}
                    fromEmail={record.from.address}
                    idEmail={record.key}
                    identification={
                      // record?.nbr > 1 && record?.fromThread?.length === 1
                      //   ? record.fromThread[0].identification
                      //   : record.identification
                      record?.nbr > 1
                        ? record?.fromThread?.filter(
                            (item) => item?.address === record?.from?.address
                          )[0]?.identification
                        : record?.identification
                    }
                    dataMailInbox={dataMailInbox}
                    setData={setDataMailInbox}
                    usedAccount={usedAccount}
                    t={t}
                    user={user}
                    access={access}
                    owner={record?.owner}
                    transfert={record?.transfert}
                    type="inbox"
                  />
                ) : null}
                {record.affectation?.affect_label ? (
                  <Affectation
                    type="inbox"
                    id={record.key}
                    owner={record?.owner}
                    fromName={record.from.name}
                    fromEmail={record.from.address}
                    idEmail={record.key}
                    transfert={record?.transfert}
                    affectation={record.affectation}
                    setDataSource={setDataMailInbox}
                    usedAccount={usedAccount}
                    t={t}
                    user={user}
                    access={access}
                  />
                ) : null}

                {record.tags?.tags?.length > 0 ? (
                  <Qualification
                    tags={record.tags}
                    id={record.key}
                    info={{
                      name: record?.qualification?.label,
                      number: record?.qualification?.note,
                    }}
                    owner={record?.owner}
                    transfert={record?.transfert}
                    data={dataTags}
                    setDataSource={setDataMailInbox}
                    setOpenTask={setOpenTask}
                    usedAccount={usedAccount}
                    type="inbox"

                    // setIdCall={setIdCallForTask}
                  />
                ) : null}

                {record?.tasks?.label ? (
                  <PopoverTask
                    t={t}
                    // setIdCall={setIdCall}
                    setTaskId={setTaskId}
                    setOpenTask={setOpenTask}
                    idCall={record.key}
                    task={record.tasks}
                  >
                    <FaCalendar
                      style={{
                        color: "red",
                        cursor: "help",
                        fontSize: 16,
                      }}
                    />
                  </PopoverTask>
                ) : null}
              </div>
            </div>
          </div>
        );
      },
    },

    {
      dataIndex: "Date",
      title: "Date",
      // width: "125px",
      render: (_, record) => (
        <div
          className=""
          style={{
            fontWeight: record.seen === 0 ? "bold" : "",
            cursor: "pointer",
          }}
        >
          {formatDateForDisplay(
            record.date,
            `${user?.location?.date_format} ${user?.location?.time_format}`,
            user,
            t
          )}
        </div>
      ),
    },
  ];

  return columns;
};

export default useColumnInbox;
