import React, { useEffect, useState } from "react";
import "./chatWithComunik.css"; // Import the CSS for styling
import { useSelector } from "react-redux";
import { useLocation } from "react-router-dom";

const ChatWithComunik = () => {
  const { typeUserLiveChat } = useSelector((state) => state.form);

  useEffect(() => {
    if (typeUserLiveChat === "Agent-account") return;
    if (!typeUserLiveChat?.id_conversation || !typeUserLiveChat?.user_id)
      return;

    let socketIoScript;
    let rmcScript;

    //     socketIoScript.crossOrigin = "anonymous";

    //     socketIoScript.onload = () => {
    //       socketLoaded = true;
    //       resolve();
    //     };

    //     socketIoScript.onerror = (err) => {
    //       console.error("Failed to load Socket.IO", err);
    //       reject(err);
    //     };

    //     document.body.appendChild(socketIoScript);
    //   });
    // };

    const loadChatBot = () => {
      rmcScript = document.createElement("script");
      rmcScript.src = "https://rmcdemo.comunikcrm.info/comuniksocial/bot.js";
      rmcScript.async = true;
      rmcScript.defer = true;

      // Configuration des attributs data
      rmcScript.setAttribute("data-user-id", typeUserLiveChat.user_id);
      rmcScript.setAttribute(
        "data-conversation-id",
        typeUserLiveChat.id_conversation
      );
      rmcScript.setAttribute("data-position", "bottom-right");

      rmcScript.onerror = () => {
        console.error("Failed to load chat bot script");
      };

      document.body.appendChild(rmcScript);
    };

    // Load scripts sequentially
    const loadScripts = async () => {
      try {
        // await loadSocketIO();
        loadChatBot();
      } catch (error) {
        console.error("Script loading failed:", error);
      }
    };

    loadScripts();

    return () => {
      // Cleanup
      if (socketIoScript && document.body.contains(socketIoScript)) {
        document.body.removeChild(socketIoScript);
      }
      if (rmcScript && document.body.contains(rmcScript)) {
        document.body.removeChild(rmcScript);
      }
    };
  }, [typeUserLiveChat]);

  // Chargement du script principal une fois jQuery chargé
  // useEffect(() => {
  //   if (!scriptsLoaded.jquery) return;

  //   const style = document.createElement("style");

  //   style.innerHTML = `
  // .sb-chat .sb-popup-message{
  // display: none !important;}
  //   .sb-chat-btn {
  //     top: ${
  //       contactInfo?.id ||
  //       pathname.includes("v2") ||
  //       pathname.includes("directory")
  //         ? "30% !important"
  //         : "50% !important"
  //     };
  //     right:${
  //       contactInfo?.id ||
  //       pathname.includes("v2") ||
  //       pathname.includes("directory")
  //         ? "23px !important"
  //         : "20px !important"
  //     };
  //     width: 35px !important;
  //     height: 35px !important;
  //     box-shadow: 0 2px 6px rgb(0 0 0 / 0%), 0 3px 32px rgb(0 0 0 / 0%) !important;
  //   }
  //   .sb-chat-btn img {
  //     width: 35px !important;
  //     height: 35px !important;
  //   }
  // `;
  //   document.head.appendChild(style);
  //   let mainScript;
  //   if (!mount) {
  //     mainScript = document.createElement("script");
  //     mainScript.id = "sbinit";
  //     mainScript.src =
  //       "https://rmcdemo.comunikcrm.info/comuniksocial/js/main.js";
  //     mainScript.async = true;
  //     mainScript.onload = () => {
  //       setScriptsLoaded((prev) => ({ ...prev, sbMain: true }));
  //     };

  //     document.body.appendChild(mainScript);
  //   }
  //   setMount(true);
  //   return () => {
  //     if (document.head.contains(style)) {
  //       document.head.removeChild(style);
  //     }
  //     // Nettoyage au démontage du composant
  //     if (document.body.contains(mainScript) && !mount) {
  //       document.body.removeChild(mainScript);
  //     }
  //   };
  // }, [scriptsLoaded.jquery, contactInfo?.id]);

  // useEffect(() => {
  //   if (
  //     !scriptsLoaded.jquery ||
  //     !scriptsLoaded.sbMain ||
  //     typeUserLiveChat === "Agent-account"
  //   )
  //     return;

  //   const sbCheckInterval = setInterval(() => {
  //     if (window.SBF && window.SBChat) {
  //       clearInterval(sbCheckInterval);
  //       setSbInitialized(true);
  //       if (window.jQuery && user.email) {
  //         if (typeUserLiveChat === "duplicate-email") {
  //           window.SBF.loginnew(user.email, () => {
  //             window.SBChat.initChat();
  //             // window.SBChat.open();
  //           });
  //         } else if (Array.isArray(typeUserLiveChat)) {
  //           window.SBF.login(
  //             "",
  //             "",
  //             typeUserLiveChat[0].id,
  //             typeUserLiveChat[0].token,
  //             function () {
  //               window.SBChat.initChat();
  //               // window.SBChat.open();
  //             }
  //           );

  //           /*   window.SBF.loginCookie(typeUserLiveChat[1]);
  //           window.SBF.activeUser(new window.SBUser(typeUserLiveChat[0]));
  //           window.SBChat.initChat();*/
  //         } else if (typeUserLiveChat === "Agent-account") {
  //           window.SBChat.initChat();
  //         }

  //         // window.jQuery(document).on("SBReady", function () {
  //         //   window.SBF.getActiveUser(true, () => {
  //         //     const activeUser = window.SBF.activeUser();
  //         //     console.log("activeuser", activeUser);
  //         //     if (activeUser && activeUser.email !== user.email) {
  //         //       //   window.SBF.reset();
  //         //     }
  //         //   });
  //         //   window.SBF.loginnew(user.email, () => {
  //         //     window.SBChat.initChat();
  //         //     window.SBChat.open();
  //         //   });
  //         //   // window.SBF.loginCookie(response[1]);
  //         //   // window.SBF.activeUser(new SBUser(response[0]));
  //         //   // window.SBChat.initChat();
  //         //   /*window.SBF.loginnew(user.email, function () {
  //         //     window.SBChat.initChat();
  //         //     window.SBChat.open();
  //         //   });*/
  //         //   // if (sessionStorage.getItem("pageReloaded")) {
  //         //   //   window.SBF.loginnew(user.email, function () {
  //         //   //     window.SBChat.initChat();
  //         //   //     window.SBChat.open();
  //         //   //   });
  //         //   //   return;
  //         //   // }
  //         //   // window.SBF.loginnew(user.email, function () {
  //         //   //   window.SBChat.initChat();
  //         //   //   sessionStorage.setItem("pageReloaded", "true");
  //         //   //    setTimeout(function () {
  //         //   //      window.location.reload();
  //         //   //    }, 500);
  //         //   // });
  //         // });
  //       }
  //     }
  //   }, 100);

  //   return () => {
  //     clearInterval(sbCheckInterval);
  //   };
  // }, [
  //   scriptsLoaded.jquery,
  //   scriptsLoaded.sbMain,
  //   user.email,
  //   typeUserLiveChat,
  // ]);

  // const handleChatClick = () => {
  //   if (sbInitialized && window.SBChat) {
  //     window.SBChat.initChat();
  //     window.SBChat.open();
  //   } else {
  //     console.log("Le chat n'est pas encore initialisé");
  //   }
  // };
  // const getCookieValue = (name) => {
  //   const cookies = document.cookie.split(";");
  //   // console.log(cookies);
  //   for (let i = 0; i < cookies.length; i++) {
  //     const cookie = cookies[i].trim();
  //     if (cookie.startsWith(name + "=")) {
  //       console.log(cookie.startsWith(name + "="));
  //       return cookie.substring(name.length + 1);
  //     }
  //   }
  //   return "";
  // };

  return <div />;
};

export default ChatWithComunik;
