/* audio control */
/* :root {
    --color-audio-webPhone: rgb(248 250 252);
} */

.voicemail-webPhone audio::-webkit-media-controls-play-button,
.voicemail-webPhone audio::-webkit-media-controls-panel {
    background-color: var(--color-audio-webPhone);
    color: var(--color-audio-webPhone);
}

.voicemail-webPhone audio:hover::-webkit-media-controls-play-button,
.voicemail-webPhone audio:hover::-webkit-media-controls-panel {
    background-color: var(--color-audio-webPhone);
    color: var(--color-audio-webPhone);
}

.voicemail-webPhone .audio-controls {
    /* height: 2.2rem; */
    /* max-width: 300px; */
    margin-bottom: 0;
  }
/* .voicemail-webPhone .ant-collapse-ghost .ant-collapse-content-box {
    padding-block: 6 !important
}   */

.custom-collapse-panel-webPhone-voiceMail .ant-collapse-content-box {
    padding: 0 10px 8px 10px !important;
}