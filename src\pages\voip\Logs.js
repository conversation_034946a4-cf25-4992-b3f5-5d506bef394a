import { useEffect } from "react";
import { useSelector } from "react-redux";
import { Badge, Tabs } from "antd";
import { useTranslation } from "react-i18next";
import {
  Navigate,
  Route,
  Routes,
  useLocation,
  useNavigate,
} from "react-router-dom";
import { roles } from "utils/role";
import { useQueryClient } from "@tanstack/react-query";
import VoiceMessaging from "./voice_messaging/VoiceMessaging.js";
import GroupsAndQueues from "./groups_queues/GroupsAndQueues.js";
import CallLogs from "./call_logs/CallLogs";
import LivePanel from "./live_panel/LivePanel";
import Directory from "./directory/Directory";
import { URL_ENV } from "index.js";

const Logs = () => {
  //
  const [t] = useTranslation("common");
  const navigate = useNavigate();
  const location = useLocation();
  const pathname = location.pathname;
  //
  const queryClient = useQueryClient();
  const user = useSelector((state) => state.user.user);
  const { nbrMissedCalls, nbrVoiceMessaging, nbrMissedQueueGroup } =
    useSelector(({ voip }) => voip);

  //

  const handleChangeTab = (key) => {
    navigate(`${key}`);
  };

  useEffect(() => {
    if (pathname === "/telephony" || pathname === "/telephony/") {
      navigate("/telephony/callLog", { replace: true });
    }
    return () => {
      queryClient.removeQueries(["firstCallDate"], { exact: true });
      queryClient.removeQueries(["callTags"], { exact: false });
      queryClient.removeQueries(["callKpi"], { exact: false });
      queryClient.removeQueries([["callLogs"]], { exact: false });
      // dispatch({ type: SET_SELECTED_TAB, payload: "call_logs" });
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const isValidPath =
    pathname.startsWith("/telephony") &&
    [
      "",
      "/",
      "/callLog",
      "/voicemail",
      "/groupsLog",
      "/livePanel",
      "/directory",
    ].some((suffix) => pathname === `/telephony${suffix}`);

  if (!isValidPath) {
    return <Navigate to="/unauthorized" state={{ from: pathname }} replace />;
  } else if (
    pathname === "/telephony/livePanel" &&
    process.env.REACT_APP_BRANCH !== "devLocal" &&
    (!roles.includes(user?.role) ||
      (URL_ENV.REACT_APP_TENANT_NAME !== "spheredev1" &&
        URL_ENV.REACT_APP_TENANT_NAME !== "comunik"))
  ) {
    return <Navigate to="/unauthorized" state={{ from: pathname }} replace />;
  }

  return (
    <div className="mt-2 p-1">
      <Tabs
        activeKey={pathname}
        size="large"
        type="card"
        onChange={handleChangeTab}
        items={[
          {
            label: t("voip.directory"),
            key: "/telephony/directory",
          },
          {
            label: (
              <div className="flex flex-row">
                {t("menu1.callLog")}
                <Badge
                  size="small"
                  overflowCount={10}
                  count={nbrMissedCalls}
                  offset={[0, -3]}
                />
              </div>
            ),
            key: "/telephony/callLog",
          },
          {
            label: (
              <div className="flex flex-row">
                {t("menu2.messaging")}
                <Badge
                  size="small"
                  overflowCount={10}
                  count={nbrVoiceMessaging}
                  offset={[0, -3]}
                />
              </div>
            ),
            key: "/telephony/voicemail",
          },
          {
            label: (
              <div className="flex flex-row">
                {t("voip.groups_queues")}
                <Badge
                  size="small"
                  overflowCount={10}
                  count={nbrMissedQueueGroup}
                  offset={[0, -3]}
                />
              </div>
            ),
            key: "/telephony/groupsLog",
          },
          ...(roles.includes(user.role) &&
          (process.env.REACT_APP_BRANCH === "devLocal" ||
            URL_ENV.REACT_APP_TENANT_NAME === "spheredev1" ||
            URL_ENV.REACT_APP_TENANT_NAME === "comunik")
            ? [
                {
                  label: `${t("voip.livePanel")} (${t("livePanel.users")})`,
                  key: "/telephony/livePanel",
                },
              ]
            : []),
        ]}
      />
      <Routes>
        <Route path="callLog" element={<CallLogs />} />
        <Route path="voicemail" element={<VoiceMessaging />} />
        <Route path="groupsLog" element={<GroupsAndQueues />} />
        <Route path="livePanel" element={<LivePanel />} />
        <Route path="directory" element={<Directory />} />
      </Routes>
    </div>
  );
};

export default Logs;
