import {
  convertT<PERSON><PERSON><PERSON>,
  sortD<PERSON><PERSON>ist,
  uniq<PERSON><PERSON><PERSON><PERSON>,
  uuid,
} from "../../pages/layouts/chat/utils/ConversationUtils";
import {
  SET_CHAT_SELECTED_PARTICIPANTS,
  SET_CHAT_USER_LIST,
  SET_SEARCH_CHAT_SIDEBAR,
  SET_FILTER_CHAT_PREVIEW,
  SET_CHAT_MEMBERS_GROUPS,
  CREATE_GROUP_CHAT,
  SET_INPUT_VALUE,
  SET_DELETE_MESSAGE_CHAT_MEMBERS,
  SET_UPDATE_MESSAGE_CHAT_MEMEBERS,
  SET_NEW_MESSAGE_CHAT_MEMEBERS,
  SET_USER_INFO_CHAT,
  SET_MESSAGE_READ_CHAT,
  SET_MESSAGE_UNDO_READ_CHAT,
  SET_CONFIG_USER_CHAT,
  SET_NEW_ERROR_MESSAGE,
  FILTER_ERROR_MESSAGE,
  // SET_NEW_WAITING_MESSAGE,
  // FILTER_WAITING_MESSAGE,
  SCROLL_TO_BOTTOM,
  UPDATE_ROOM_BY_ID_CHAT,
  SET_MEMBRE_CHAT_PARTICIPANTS_DATE,
  SEARCH_MESSAGE_IN_LIST_CONVERSATION,
  SEARCH_MESSAGE_GET_DATA,
  LEAVE_PARTICIPANT,
  SET_MEDIAS_LIST,
  SET_DOCUMENTS_LIST,
  SET_LINKS_LIST,
  SET_LINKS_FILTRED_LIST,
  SET_NOTIFICATION_NUMBER,
  ADD_NOTIFICATION_NUMBER,
  SUBSTRACT_NOTIFICATION_NUMBER,
  SET_LATENCE_TIMEOUT,
  SET_STARRED_LIST,
  SET_PINNED_LIST,
  ADD_STARRED_MESSAGE,
  REMOVE_STARRED_MESSAGE,
  ADD_SAVED_MESSAGE,
  REMOVE_SAVED_MESSAGE,
  SET_NUMBER_UNREAD_MSG,
  SET_SYNC_NEW_MSG,
  SET_SEARCH_LIST,
  SET_ASSETS_COUNT,
  SET_IMAGE_TO_LIST,
  SET_DOCUMENT_TO_LIST,
  SET_ASSETS_TO_LIST,
  SET_SEARCH_MESSAGE_TERM,
  SET_PINNED_FETCHED,
  SET_STARRED_FETCHED,
  SET_THREAD_LIST,
  ADD_THREAD_TO_LIST,
  SET_OPEN_DRAWER,
  SET_MODAL_QUIT_GROUP,
  SET_MENTION_STATE,
  SET_ACTION_IN_SEARCH_STATUS,
  SET_ACTION_IN_REPLY,
  ADD_ARCHIVE_CONVERSATION,
  REMOVE_ARCHIVE_CONVERSATION,
  GET_ARCHIVE_CONVERSATIONS,
  SET_SIDEBAR_DRAWER,
  SET_ARCHIVED_LIST_IDS,
  ADD_UNREAD_MESSAGE_ARCHIVED,
  ADD_CHAT_USER_LIST,
  REMOVE_CHAT_USER_LIST,
  UPDATE_CHAT_USER_LIST,
  UPDATE_CURRENT_USER_STATUS_BLOCKED,
  UPDATE_CURRENT_USER_STATUS_PRESENCE,
  RESET_STATE,
  ADD_TO_MUTE_LIST,
  REMOVE_FROM_MUTE_LIST,
  RESET_STATE_OTHER_USER,
  UPDATE_THREAD_TO_LIST,
  ADD_UUID_TO_MESSAGE,
  DELETE_UUID_TO_MESSAGE,
  SET_ALL_STARRED_LIST,
  SET_ALL_PINNED_LIST,
  SET_DATA_CONVERSATION,
  SET_POLL_LIST,
  ADD_POLL_LIST,
  SET_ALL_POLL_LIST,
  ADD_ALL_POLL_LIST,
  REMOVE_POLL_LIST,
  REMOVE_ALL__POLL_LIST,
  UPDATE_ALL_POLL_LIST,
  UPDATE_POLL_LIST,
  SET_OPEN_SIDE_BAR_DRAWER,
  SET_POLLS_VOTE,
  SET_LOADING_SIDE_BAR,
  SET_DOCUMENTS_FILTRED_LIST,
  SET_ERROR_MEMBERS_GROUPS_CHAT,
  ADD_LINKS_TO_LIST,
  SET_LINKS_UPDATE_REMOVE_LIST,
  SET_EXTERNAL_ITEM_CHAT,
  HANDLE_TYPING_USER,
} from "../constants";
import { getCurrentItem } from "new-redux/actions/chat.actions";
//import { moment_timezone } from "App";
export const LoadingSideBarStatus = {
  idle: "IDLE",
  soft: "SOFT",
  hard: "HARD",
  load: "LOAD",
  loadMore: "LOAD_MORE",
};
export const FilterSidebarType = {
  room: "room",
  member: "member",
  guest: "guest",
};

const setTags = (tags, oldTagStatus = 0, userId) => {
  // If tags are undefined, null, or the old status is 2, return the oldTagStatus immediately
  if (typeof tags === "undefined" || tags === null || oldTagStatus === 2) {
    return oldTagStatus;
  }

  // If tags are "0", return 1
  if (tags === "0") {
    return 1;
  }

  // If tags include the userId, return 2
  if (tags?.includes(String(userId))) {
    return 2;
  }

  // Otherwise, return 0
  return 0;
};

const initialState = {
  // list of particiapns
  selectedParticipants: [],
  // object of current user in chat
  currentUser: null,
  // list of all users
  userList: [],
  // waiting msg uuid after generating object of msg
  msgUUID: [],
  // to indicate if im in mention phase or not
  mentionState: false,
  // state to handle input bar
  searchChatSideBar: "",
  // state to indicate the bar from where new msg start
  numberUnreadMsg: {
    discussion_id: null,
    number: 0,
  },
  // object of msg in case reconnection
  syncNewMsg: null,
  // to filter list of memebers
  activeFiltersDiscussion: [...Object.values(FilterSidebarType)],
  // list of members
  membersGroupsChat: [],
  // loading indicator
  loadingSideBar: LoadingSideBarStatus.idle,
  // array: include list of input in drafts
  lastMessage: [],
  // array: include list of msg not sent
  errorMessages: [],
  // in case api get conversation throwing error
  errorChatMember: false,
  //waitingMessages: [],
  // to force scroll down every time switch the conversation
  scrollToBottomState: null,
  // object contain in case of search is made in
  searchMsgState: {
    discussion_id: null,

    count_new_message: 0,
    loading: false,
    miniLoadingPrevious: false,
    hasMorePrevious: true,
    hasMoreNext: true,
    miniLoadingNext: false,
    // the real id of msg
    id: null,
    // this is to indicate the style where i search of
    virtual: null,
    // it may be top or bottom
    orientation: "",

    last_id: null,
    first_id: null,
  },
  // array: include list of msg in search phase
  searchMsg: [],
  // state to count the last unread msg
  totalNotificationChat: {
    numberConversationChat: 0,
    total: 0,
    conversations: [],
  },
  // external object for no_chat
  externalChatItem: {
    _id: null,
    total_unread: 0,
    last_message: {
      unread: 1,
    },
    tag_status: false,
  },
  // array in details conversation  describe list of images
  images: [],
  // array in details conversation  describe list of documents
  documents: [],
  documentsFiltred: [],
  // array in details conversation  describe list of links
  links: [],
  linksFiltred: [],
  // value indicate the ping between client and sphere
  latence: null,
  // to indicate which drawer is opened  from right
  openDrawer: { type: "", external: null },
  // to open drawer from the left
  openSideBarDrawer: false,
  // array : list of starred msg
  starredList: [],
  // array : list of pinned msg
  pinnedList: [],
  // array : list of search msg
  searchList: [],
  // array : list of thread msg in reply
  threadList: [],
  // boolean : status of loading api
  threadLoading: false,
  // object: in details conversation : indciate number of files, documents, links...
  assetsCount: {
    images: undefined,
    documents: undefined,
    links: undefined,
  },
  // object: in case search is active (value: the input search; force to the search without click into enter)
  searchMessageTerm: { value: "", forced: false },
  //boolean:  indication if the api has been fetched or not
  pinnedListFetched: false,
  starredListFetched: false,
  // boolean: open drawer quit group
  openModalQuitGroup: false,
  // array: archived msg
  archivedList: [],
  // string: if chat's sidebar is chat/ archive
  sidebarDrawer: "chat",
  // ids of discussions is archived
  archivedListIds: [],
  // indicator of archived msg
  count_archived_msg: 0,
  // array: list of  all starred
  allStarredList: [],
  // array: list of all pinned
  allPinnedList: [],
  dataConversation: null,
  // array: list of all polls
  allPollList: [],
  pollList: [],
  allPollListFetched: false,
  pollListFetched: false,
  // number: force to render
  pollsRender: null,
  // set the status of typing user
  userIsTyping: 0,
};

const chat = (state = initialState, action) => {
  const { type, payload } = action;
  switch (type) {
    case SET_CHAT_SELECTED_PARTICIPANTS:
      return {
        ...state,
        selectedParticipants: payload.selectedParticipants,
      };
    case SET_CHAT_USER_LIST:
      return {
        ...state,
        userList: payload,
      };
    case ADD_CHAT_USER_LIST:
      return {
        ...state,
        userList:
          payload.type === "one"
            ? [...state.userList, payload.data]
            : [...state.userList, ...payload.data],
        selectedParticipants:
          payload.type === "one"
            ? [...state.selectedParticipants, payload.data]
            : state.selectedParticipants,
      };
    case REMOVE_CHAT_USER_LIST:
      return {
        ...state,
        userList: state.userList.filter((user) => user._id !== payload),
        selectedParticipants: state.selectedParticipants.filter(
          (user) => user._id !== payload
        ),
      };
    case UPDATE_CHAT_USER_LIST:
      return {
        ...state,
        userList: state.userList.map((user) =>
          user._id === payload._id ? payload : user
        ),
        selectedParticipants: state.selectedParticipants?.map((user) =>
          user._id === payload._id ? payload : user
        ),
      };

    // to show bar : new msg from index (number of new msg)
    case SET_NUMBER_UNREAD_MSG:
      return {
        ...state,
        numberUnreadMsg: {
          discussion_id: payload.id,
          number: payload.increment
            ? state.numberUnreadMsg.number + 1
            : payload.number,
        },
      };
    // in case of reconnection : to show pop : it seems you have new msg
    case SET_SYNC_NEW_MSG:
      return {
        ...state,
        syncNewMsg: payload,
      };

    // in sidebar  :  the search bar input
    case SET_SEARCH_CHAT_SIDEBAR:
      return {
        ...state,
        searchChatSideBar: String(payload)?.trim(),
      };
    // in sidebar  :  the filter by
    case SET_FILTER_CHAT_PREVIEW:
      let newValue = [...state.activeFiltersDiscussion];
      // to initialize the array (send all the values)
      if (Array.isArray(payload)) {
        newValue = [...payload];
      } else if (newValue.includes(payload)) {
        newValue = newValue.filter((filter) => filter !== payload);
      } else {
        newValue.push(payload);
      }
      return {
        ...state,
        activeFiltersDiscussion: newValue,
      };

    // in sidebar  :  the list of members and groups : array reference.
    case SET_CHAT_MEMBERS_GROUPS:
      return {
        ...state,
        membersGroupsChat: sortDataList(
          payload.list,
          state.currentUser?.config?.sort_message,
          "last_message_date"
        ),
      };

    // in sidebar  : in case of error in api
    case SET_ERROR_MEMBERS_GROUPS_CHAT: {
      return {
        ...state,
        errorChatMember: payload,
      };
    }

    // make message read

    case SET_MESSAGE_READ_CHAT: {
      // check if the conversation is archived
      const isArchiveConversation = state.archivedListIds.find(
        (item) =>
          item.room_id === payload.idRead || item.receiver_id === payload.idRead
      );

      const arrayToChange = !isArchiveConversation
        ? [...state.membersGroupsChat]
        : [...state.archivedList];

      const itemArchived = state.archivedList.find(
        (disc) =>
          (payload.type === "room" ? disc?.room?._id : disc?.contact?._id) ===
            payload.idRead &&
          ((payload.type === "room" && disc.room !== null) ||
            (payload.type === "user" && disc.contact !== null))
      );
      const newDataNotFiltred = arrayToChange.map((e) => {
        if (
          (payload.type === "room" ? e?.room?._id : e?.contact?._id) ===
            payload.idRead &&
          ((payload.type === "room" && e.room !== null) ||
            (payload.type === "user" && e.contact !== null))
        ) {
          return {
            ...e,
            total_unread: 0,
            tag_status: false,

            last_message: !e.last_message
              ? null
              : {
                  ...e.last_message,
                  unread: 0,
                },
          };
        }

        return e;
      });

      return {
        ...state,

        count_archived_msg: isArchiveConversation
          ? state.count_archived_msg - itemArchived?.total_unread
          : state.count_archived_msg,
        membersGroupsChat: sortDataList(
          !isArchiveConversation
            ? [...newDataNotFiltred]
            : [...state.membersGroupsChat],
          state.currentUser?.config?.sort_message,
          "last_message_date"
        ),

        archivedList: sortDataList(
          isArchiveConversation
            ? [...newDataNotFiltred]
            : [...state.archivedList],
          state.currentUser?.config?.sort_message,
          "last_message_date"
        ),
      };
    }
    case SET_EXTERNAL_ITEM_CHAT: {
      return {
        ...state,
        externalChatItem: payload,
      };
    }
    case SET_MESSAGE_UNDO_READ_CHAT: {
      const isArchiveConversation = state.archivedListIds.find(
        (item) => item.room_id === payload.id || item.receiver_id === payload.id
      );

      const arrayToChange = !isArchiveConversation
        ? [...state.membersGroupsChat]
        : [...state.archivedList];
      const newData = arrayToChange.map((e) => {
        if (
          (payload.type === "room" ? e?.room?._id : e?.contact?._id) ===
            payload.id &&
          ((payload.type === "room" && e.room !== null) ||
            (payload.type === "user" && e.contact !== null))
        ) {
          return {
            ...e,
            total_unread: payload.item?.total_unread,
            tag_status: payload.item?.tag_status,

            last_message: !e.last_message
              ? null
              : {
                  ...e.last_message,
                  unread: payload.item?.last_message?.unread,
                },
          };
        }

        return e;
      });
      return {
        ...state,
        count_archived_msg: isArchiveConversation
          ? state.count_archived_msg + payload.item?.total_unread
          : state.count_archived_msg,
        membersGroupsChat: sortDataList(
          !isArchiveConversation ? [...newData] : [...state.membersGroupsChat],
          state.currentUser?.config?.sort_message,
          "last_message_date"
        ),
        archivedList: sortDataList(
          isArchiveConversation ? [...newData] : [...state.archivedList],
          state.currentUser?.config?.sort_message,
          "last_message_date"
        ),
      };
    }
    case SET_LOADING_SIDE_BAR:
      const arrayToCompare =
        state.sidebarDrawer === "chat"
          ? [...state.membersGroupsChat]
          : [...state.archivedList];
      return {
        ...state,
        loadingSideBar:
          payload === LoadingSideBarStatus.load
            ? arrayToCompare?.length > 0
              ? LoadingSideBarStatus.soft
              : LoadingSideBarStatus.hard
            : payload,
      };

    case SET_MEMBRE_CHAT_PARTICIPANTS_DATE: {
      const isArchiveConversation = state.archivedListIds.find(
        (item) => item.room_id === payload.id || item.receiver_id === payload.id
      );

      const arrayToChange = !isArchiveConversation
        ? [...state.membersGroupsChat]
        : [...state.archivedList];

      const newData = arrayToChange.map((item) =>
        item._id === payload.conversationId
          ? { ...item, last_message_date: new Date().toISOString() }
          : item
      );
      return {
        ...state,

        membersGroupsChat: sortDataList(
          !isArchiveConversation ? [...newData] : [...state.membersGroupsChat],
          state.currentUser?.config?.sort_message,
          "last_message_date"
        ),

        archivedList: sortDataList(
          isArchiveConversation ? [...newData] : [...state.archivedList],
          state.currentUser?.config?.sort_message,
          "last_message_date"
        ),
      };
    }
    case CREATE_GROUP_CHAT:
      return {
        ...state,

        membersGroupsChat: sortDataList(
          [
            {
              _id: payload.conversation_id,

              contact: null,
              sender: null,
              bot: null,
              room: {
                _id: payload._id,
                name: payload.name,
                image: payload.image,
                description: payload.description,
                admin_id: payload.admin_id,
              },
              reaction: null,
              last_message: null,
              last_message_date: new Date(payload.created_at).toISOString(),
              total_unread: 0,
              tag_status: false,
            },
            ...state.membersGroupsChat,
          ],
          state.currentUser?.config?.sort_message,
          "last_message_date"
        ),
      };
    case SET_INPUT_VALUE: {
      const { conversationId, type, path, value } = payload;
      let newMsgs = [...state.lastMessage] ?? [];

      const msgFound = newMsgs.find(
        (item) =>
          item?.conversationId ===
          `${conversationId}_${type}_${
            path?.includes("chat_reply") ? "chat_reply" : path
          }`
      );
      if (msgFound && convertToPlain(value)?.length === 0) {
        newMsgs = newMsgs.filter(
          (item) =>
            item.conversationId !==
            `${conversationId}_${type}_${
              path.includes("chat_reply") ? "chat_reply" : path
            }`
        );
      } else if (msgFound && convertToPlain(value)?.length > 0)
        newMsgs = newMsgs.map((item) =>
          item.conversationId === `${conversationId}_${type}_${path}`
            ? {
                conversationId: `${conversationId}_${type}_${path}`,
                value,
              }
            : item
        );
      else
        newMsgs = [
          ...newMsgs,
          {
            conversationId: `${conversationId}_${type}_${
              state.openDrawer.type === "thread" ? "chat_reply" : path
            }`,

            value,
          },
        ];

      newMsgs = uniqByKey([...newMsgs], "conversationId");
      return {
        ...state,
        lastMessage: [...newMsgs],
      };
    }
    case SET_MENTION_STATE: {
      return {
        ...state,
        mentionState: payload,
      };
    }
    case SET_NOTIFICATION_NUMBER: {
      const { number, conversations, total } = payload;

      return {
        ...state,
        totalNotificationChat: {
          numberConversationChat: number,
          total: total,
          conversations: conversations || [],
        },
      };
    }
    case ADD_NOTIFICATION_NUMBER: {
      const { discussion_id, number } = payload;

      const canAdd = ![...state.totalNotificationChat.conversations].includes(
        discussion_id
      );

      return {
        ...state,
        totalNotificationChat: {
          numberConversationChat: canAdd
            ? state.totalNotificationChat.numberConversationChat + 1
            : state.totalNotificationChat.numberConversationChat,
          total: state.totalNotificationChat.total + number,
          conversations: canAdd
            ? [
                ...new Set([
                  ...state.totalNotificationChat.conversations,
                  discussion_id,
                ]),
              ]
            : state.totalNotificationChat.conversations,
        },
      };
    }
    case SUBSTRACT_NOTIFICATION_NUMBER: {
      const { discussion_id, number } = payload;

      const canRemove = [...state.totalNotificationChat.conversations].includes(
        discussion_id
      );

      return {
        ...state,
        totalNotificationChat: {
          numberConversationChat: canRemove
            ? state.totalNotificationChat.numberConversationChat > 0
              ? state.totalNotificationChat.numberConversationChat - 1
              : 0
            : state.totalNotificationChat.numberConversationChat,
          total: canRemove
            ? state.totalNotificationChat.total > 0
              ? state.totalNotificationChat.total - number > 0
                ? state.totalNotificationChat.total - number
                : 0
              : 0
            : state.totalNotificationChat.total,
          conversations: canRemove
            ? [...state.totalNotificationChat.conversations].filter(
                (item) => item !== discussion_id
              )
            : state.totalNotificationChat.conversations,
        },
      };
    }
    case SET_DELETE_MESSAGE_CHAT_MEMBERS: {
      const isArchiveConversation = state.archivedListIds.find(
        (item) =>
          item.room_id === payload.discussion_id ||
          item.receiver_id === payload.discussion_id
      );
      const arrayToChange = !isArchiveConversation
        ? [...state.membersGroupsChat]
        : [...state.archivedList];

      const newData = arrayToChange.map((item) => {
        if (getCurrentItem(item)?._id === payload.discussion_id) {
          if (item.last_message?._id !== payload._id) return item;
          else
            return {
              ...item,
              sender: payload.sender,

              reaction: null,

              last_message: null,
              last_message_date: new Date().toISOString(),
            };
        }
        return item;
      });
      return {
        ...state,

        membersGroupsChat: sortDataList(
          !isArchiveConversation ? [...newData] : [...state.membersGroupsChat],
          state.currentUser?.config?.sort_message,
          "last_message_date"
        ),

        archivedList: sortDataList(
          isArchiveConversation ? [...newData] : [...state.archivedList],
          state.currentUser?.config?.sort_message,
          "last_message_date"
        ),
        starredList: state.starredList.filter((e) => e._id !== payload._id),
        allStarredList: state.allStarredList.filter(
          (e) => e._id !== payload._id
        ),
        allPinnedList: state.allPinnedList.filter((e) => e._id !== payload._id),

        pinnedList: state.pinnedList.filter((e) => e._id !== payload._id),
      };
    }
    case SET_UPDATE_MESSAGE_CHAT_MEMEBERS: {
      const newReaction =
        typeof payload.reaction === "string"
          ? payload.reaction
          : typeof payload.reaction === "number"
          ? Number(payload.reaction)
          : null;
      const isArchiveConversation = state.archivedListIds.find(
        (item) =>
          item.room_id === payload.discussion_id ||
          item.receiver_id === payload.discussion_id
      );

      const arrayToChange = !isArchiveConversation
        ? [...state.membersGroupsChat]
        : [...state.archivedList];

      const newData = arrayToChange.map((item) => {
        if (
          (payload.type === "room" ? item?.room?._id : item?.contact?._id) ===
            action.payload.discussion_id &&
          ((payload.type === "room" && item.room !== null) ||
            (payload.type === "user" && item.contact !== null))
        ) {
          if (
            payload.unread === "error" ||
            ((newReaction || newReaction === 0) && !isNaN(newReaction)) ||
            newReaction === "delete_react"
          )
            return {
              ...item,

              sender: {
                _id: payload.sender?._id,
                name: payload.sender?.name,
                email: payload.sender?.email,
                post_number: payload.sender?.post_number,
                image: payload.sender?.image,
                uuid: payload.sender.uuid,
              },

              reaction:
                newReaction !== "delete_react"
                  ? newReaction === null || newReaction === undefined
                    ? null
                    : newReaction
                  : null,
              last_message:
                payload.data?.deleted_at || payload.data === null
                  ? null
                  : {
                      ...item.last_message,
                      _id: payload.data._id,
                      type: payload.data.type,
                      message: payload.data.message,
                      unread:
                        payload.unread === "error"
                          ? "error"
                          : item.last_message?.unread,
                    },
              last_message_date:
                newReaction === "delete_react"
                  ? new Date(payload?.last_message_date).toISOString()
                  : new Date().toISOString(),
              tag_status: setTags(
                payload?.data?.tags,
                item.tag_status,
                state.currentUser?._id
              ),
            };
          else if (
            item.last_message?._id === payload.data._id &&
            payload.unread !== "error"
          )
            return {
              ...item,

              sender: {
                _id: payload.sender?._id,
                name: payload.sender?.name,
                email: payload.sender?.email,
                post_number: payload.sender?.post_number,
                image: payload.sender?.image,
                uuid: payload.sender.uuid,
              },

              reaction:
                newReaction !== "delete_react"
                  ? newReaction === null || newReaction === undefined
                    ? null
                    : newReaction
                  : null,
              last_message:
                payload.data?.deleted_at || payload.data === null
                  ? null
                  : {
                      ...item.last_message,
                      _id: payload.data?._id,
                      type: payload.data?.type,
                      message: payload.data?.message,
                    },
              last_message_date:
                newReaction === "delete_react"
                  ? new Date(payload?.last_message_date).toISOString()
                  : new Date().toISOString(),
              tag_status: setTags(
                payload?.data?.tags,
                item.tag_status,
                state.currentUser?._id
              ),
            };
        }
        return item;
      });

      return {
        ...state,

        membersGroupsChat: sortDataList(
          !isArchiveConversation ? [...newData] : [...state.membersGroupsChat],
          state.currentUser?.config?.sort_message,
          "last_message_date"
        ),

        archivedList: sortDataList(
          isArchiveConversation ? [...newData] : [...state.archivedList],
          state.currentUser?.config?.sort_message,
          "last_message_date"
        ),
        starredList:
          payload.data?.favoris?.length > 0
            ? state.starredList.map((e) => {
                if (e._id === payload.data._id) {
                  e = payload.data;
                }
                return e;
              })
            : state.starredList,
        allStarredList:
          payload.data?.favoris?.length > 0
            ? state.allStarredList.map((e) => {
                if (e._id === payload.data._id) {
                  e = payload.data;
                }
                return e;
              })
            : state.allStarredList,
        allPinnedList:
          payload.data?.important > 0
            ? state.allPinnedList.map((e) => {
                if (e._id === payload.data._id) {
                  e = payload.data;
                }
                return e;
              })
            : state.allPinnedList,

        pinnedList:
          payload.data?.important > 0
            ? state.pinnedList.map((e) => {
                if (e._id === payload.data._id) {
                  e = payload.data;
                }
                return e;
              })
            : state.pinnedList,
      };
    }
    case SET_NEW_MESSAGE_CHAT_MEMEBERS: {
      const isArchiveConversation =
        payload?.archive ??
        state.archivedListIds.find(
          (item) =>
            item.room_id === payload.discussion_id ||
            item.receiver_id === payload.discussion_id
        );

      const arrayToChange = !isArchiveConversation
        ? [...state.membersGroupsChat]
        : [...state.archivedList];
      const discussionFounded =
        typeof payload.discussionFounded === "boolean"
          ? payload.discussionFounded
          : arrayToChange.find(
              (item) =>
                (payload.type === "room"
                  ? item?.room?._id
                  : item?.contact?._id) === payload.discussion_id
            );

      const _id = payload.conversation_id || uuid();
      if (!discussionFounded) {
        return {
          ...state,

          membersGroupsChat: sortDataList(
            !isArchiveConversation
              ? [
                  ...state.membersGroupsChat,
                  {
                    _id,

                    contact:
                      payload.type === "user"
                        ? {
                            _id: payload.discussion_id,
                            name: payload.user.name,
                            email: payload.user.email,
                            post_number: payload.user.post_number,
                            image: payload.user.image,
                            uuid: payload.user.uuid,
                          }
                        : null,
                    room:
                      payload.type === "room"
                        ? {
                            _id: payload.discussion_id,
                            name: payload.user.name,
                            description: payload.user.description,
                            image: payload.user.image,
                            admin_id: payload.user.admin_id,
                          }
                        : null,
                    sender: payload.sender,
                    // ? {
                    //     _id: payload.user?._id,
                    //     image: payload.user.image,
                    //     name: payload.user.name,
                    //   }
                    // : null,
                    reaction: null,

                    bot: payload.bot ?? null,

                    last_message: payload.data
                      ? {
                          _id: payload.data._id,
                          type: payload.data.type,
                          message: payload.data.message,
                          unread:
                            payload.unread === "error"
                              ? "error"
                              : payload.data?.unread === 2
                              ? 2
                              : 1,
                          tags: null,
                        }
                      : null,
                    last_message_date: new Date().toISOString(),
                    total_unread: payload.increment ? 1 : 0,
                    tag_status: setTags(
                      payload?.data?.tags,
                      undefined,
                      state.currentUser?._id
                    ),
                  },
                ]
              : [...state.membersGroupsChat],
            state.currentUser?.config?.sort_message,
            "last_message_date"
          ),

          archivedList: sortDataList(
            isArchiveConversation
              ? [
                  ...state.archivedList,
                  {
                    _id,

                    contact:
                      payload.type === "user"
                        ? {
                            _id: payload.discussion_id,
                            name: payload.user.name,
                            email: payload.user.email,
                            post_number: payload.user.post_number,
                            image: payload.user.image,
                            uuid: payload.user.uuid,
                          }
                        : null,
                    room:
                      payload.type === "room"
                        ? {
                            _id: payload.discussion_id,
                            name: payload.user.name,
                            description: payload.user.description,
                            image: payload.user.image,
                            admin_id: payload.user.admin_id,
                          }
                        : null,
                    sender: payload.sender
                      ? {
                          admin_id: payload.user.admin_id,
                          description: payload.user.description,
                          _id: payload.discussion_id,
                          image: payload.user.image,
                          name: payload.user.name,
                          uuid: payload.user.uuid,
                        }
                      : null,
                    reaction: null,
                    bot: payload.bot ?? null,

                    last_message: payload.data
                      ? {
                          _id: payload.data._id,
                          type: payload.data.type,
                          message: payload.data.message,
                          unread:
                            payload.unread === "error"
                              ? "error"
                              : payload.data.unread === 2
                              ? 2
                              : 1,
                          tags: null,
                        }
                      : null,
                    last_message_date: new Date().toISOString(),
                    total_unread: payload.increment ? 1 : 0,
                    tag_status: setTags(
                      payload?.data?.tags,
                      undefined,
                      state.currentUser?._id
                    ),
                  },
                ]
              : [...state.archivedList],
            state.currentUser?.config?.sort_message,
            "last_message_date"
          ),
        };
      } else {
        const newData = arrayToChange.map((item) => {
          if (
            (payload.type === "room" ? item?.room?._id : item?.contact?._id) ===
              payload.discussion_id &&
            ((payload.type === "room" && item.room !== null) ||
              (payload.type === "user" && item.contact !== null))
          ) {
            return {
              ...item,
              _id: payload.conversation_id || item._id,

              sender: {
                _id: payload?.sender?._id,
                name: payload?.sender?.name,
                email: payload?.sender?.email,
                post_number: payload?.sender?.post_number,
                image: payload?.sender?.image,
                uuid: payload.sender.uuid,
              },
              bot: payload.bot ?? item.bot,

              last_message: payload.data?.deleted_at
                ? null
                : typeof payload.data === "string"
                ? {
                    ...item.last_message,
                    _id: payload.data,
                    unread: 1,
                  }
                : typeof payload.data === "object"
                ? {
                    _id: payload.data._id,
                    type: payload.data.type,
                    message: payload.data.message,
                    unread:
                      payload.unread === "error"
                        ? "error"
                        : payload.data.unread === 2
                        ? 2
                        : payload.data.unread ?? 1,
                    tags: null,
                  }
                : null,
              reaction:
                payload.reaction !== "delete_react"
                  ? payload.reaction ?? null
                  : null,

              total_unread: payload.increment ? item.total_unread + 1 : 0,
              last_message_date: payload.data?.created_at
                ? new Date(payload.data?.created_at).toISOString()
                : new Date().toISOString(),

              tag_status: setTags(
                payload?.data?.tags,
                item.tag_status,
                state.currentUser?._id
              ),
            };
          }
          return item;
        });
        return {
          ...state,
          count_archived_msg: payload?.archive
            ? state.count_archived_msg + 1
            : state.count_archived_msg,
          membersGroupsChat: sortDataList(
            !isArchiveConversation
              ? [...newData]
              : [...state.membersGroupsChat],
            state.currentUser?.config?.sort_message,
            "last_message_date"
          ),

          archivedList: sortDataList(
            isArchiveConversation ? [...newData] : [...state.archivedList],
            state.currentUser?.config?.sort_message,
            "last_message_date"
          ),
        };
      }
    }
    case SET_USER_INFO_CHAT: {
      return {
        ...state,
        currentUser: {
          ...payload,
          config: {
            sort_message: parseInt(payload?.config?.sort_message || 0, 10),
            hidden_message: parseInt(payload?.config?.hidden_message || 0, 10),
            notification: parseInt(payload?.config?.notification || 1, 10),
            sound_notification: parseInt(
              payload?.config?.sound_notification || 1,
              10
            ),
          },
        },
      };
    }

    case UPDATE_CURRENT_USER_STATUS_BLOCKED: {
      return {
        ...state,
        currentUser: {
          ...state.currentUser,
          status: 2,
        },
      };
    }
    case UPDATE_CURRENT_USER_STATUS_PRESENCE: {
      return {
        ...state,
        currentUser: {
          ...state.currentUser,
          online: payload,
        },
      };
    }

    case SET_CONFIG_USER_CHAT: {
      return {
        ...state,
        currentUser: {
          ...state.currentUser,
          config: {
            ...state.currentUser.config,
            sort_message: parseInt(payload.sort_message ?? 0, 10),
            hidden_message: parseInt(payload.hidden_message ?? 0, 10),
            notification: parseInt(payload.notification ?? 1, 10),
            sound_notification: parseInt(payload.sound_notification ?? 1, 10),
          },
        },
      };
    }
    case SET_NEW_ERROR_MESSAGE: {
      return {
        ...state,
        errorMessages: [...state.errorMessages, action.payload].sort(
          (a, b) => b.message?.created_at - a.message?.created_at
        ),
      };
    }
    // case SET_NEW_WAITING_MESSAGE: {
    //   return {
    //     ...state,
    //     waitingMessages: !state.waitingMessages.some(
    //       (element) => element.message._id === payload.message._id
    //     )
    //       ? [...state.waitingMessages, payload].sort(
    //           (a, b) => b.message?.created_at - a.message?.created_at
    //         )
    //       : [...state.waitingMessages].sort(
    //           (a, b) => b.message?.created_at - a.message?.created_at
    //         ),
    //   };
    // }
    // case FILTER_WAITING_MESSAGE: {
    //   return {
    //     ...state,
    //     waitingMessages:
    //       state.waitingMessages && state.waitingMessages.length > 0
    //         ? state.waitingMessages
    //             .filter((item) => item.message._id !== payload)
    //             .sort((a, b) => b.message?.created_at - a.message?.created_at)
    //         : [],
    //   };
    // }

    case FILTER_ERROR_MESSAGE: {
      return {
        ...state,
        errorMessages:
          state.errorMessages && state.errorMessages.length > 0
            ? state.errorMessages
                .filter((item) => item.message._id !== payload)
                .sort((a, b) => b.message?.created_at - a.message?.created_at)
            : [],
      };
    }

    case SCROLL_TO_BOTTOM: {
      return {
        ...state,
        scrollToBottomState: payload,
      };
    }
    case SEARCH_MESSAGE_IN_LIST_CONVERSATION: {
      return {
        ...state,

        searchMsgState: {
          loading:
            payload.loading === undefined
              ? state.searchMsgState.loading
              : payload.loading,
          count_new_message:
            payload.count_new_message === undefined
              ? state.searchMsgState.count_new_message
              : payload.count_new_message === 0
              ? 0
              : state.searchMsgState.count_new_message +
                payload.count_new_message,

          discussion_id:
            payload.discussion_id === undefined
              ? state.searchMsgState.discussion_id
              : payload.discussion_id,
          miniLoadingPrevious:
            payload.miniLoadingPrevious === undefined
              ? state.searchMsgState.miniLoadingPrevious
              : payload.miniLoadingPrevious,
          hasMorePrevious:
            payload.hasMorePrevious === undefined
              ? state.searchMsgState.hasMorePrevious
              : payload.hasMorePrevious,
          hasMoreNext:
            payload.hasMoreNext === undefined
              ? state.searchMsgState.hasMoreNext
              : payload.hasMoreNext,

          miniLoadingNext:
            payload.miniLoadingNext === undefined
              ? state.searchMsgState.miniLoadingNext
              : payload.miniLoadingNext,
          id: payload.id === undefined ? state.searchMsgState.id : payload.id,
          virtual:
            payload.virtual === undefined
              ? state.searchMsgState.virtual
              : payload.virtual,

          orientation:
            payload.orientation === undefined
              ? state.searchMsgState.orientation
              : payload.orientation,
          last_id:
            payload.last_id === undefined
              ? state.searchMsgState.last_id
              : payload.last_id,
          first_id:
            payload.first_id === undefined
              ? state.searchMsgState.first_id
              : payload.first_id,
        },
      };
    }
    case SEARCH_MESSAGE_GET_DATA: {
      let newData = [...state.searchMsg];
      let data =
        action.payload.type === "previous"
          ? action.payload.data.reverse()
          : action.payload.data;

      newData.splice(
        action.payload.type === "previous" ? 0 : newData.length,
        0,
        ...data
      );
      return {
        ...state,
        searchMsg:
          action.payload.type === "normal"
            ? action.payload.data
            : action.payload.type === "empty"
            ? []
            : newData,
      };
    }
    case SET_ACTION_IN_SEARCH_STATUS: {
      let newData = state.searchMsg.map((item) => {
        if (item._id === payload.message_id) {
          switch (payload.type) {
            case "add_react":
              return {
                ...item,
                reactions: [
                  ...item.reactions,
                  {
                    _id: uuid(),
                    user_id: payload.sender_id,
                    reaction: payload.newMessage,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString(),
                  },
                ].sort((a, b) => a.reaction - b.reaction),
              };
            case "remove_react": {
              return {
                ...item,
                reactions:
                  item.reactions &&
                  item.reactions
                    ?.filter(
                      (item) =>
                        !(
                          item.user_id === payload.sender_id &&
                          item.reaction === payload.newMessage
                        )
                    )
                    .sort((a, b) => a.reaction - b.reaction),
              };
            }
            case "add_favorite": {
              return {
                ...item,
                favoris: !item.favoris
                  ? [payload.sender_id]
                  : item.favoris && !item.favoris?.includes(payload.sender_id)
                  ? [...item.favoris, payload.sender_id]
                  : null,
              };
            }
            case "remove_favorite": {
              return {
                ...item,
                favoris:
                  item.favoris && item.favoris.length > 1
                    ? item.favoris.filter(
                        (favoris) => favoris === payload.sender_id
                      )
                    : null,
              };
            }
            case "deleted": {
              return {
                ...item,
                message: "Message deleted",
                deleted_at: new Date().toISOString(),
              };
            }
            case "add_saved": {
              return {
                ...item,
                important: payload.sender_id,
              };
            }
            case "remove_saved": {
              return {
                ...item,
                important: "",
              };
            }

            case "update": {
              return {
                ...item,
                message: payload.newMessage.message,
                edit: payload.newMessage.edit,
              };
            }
            default:
              break;
          }
        }
        return item;
      });

      return {
        ...state,
        searchMsg: newData,
      };
    }
    case SET_ACTION_IN_REPLY: {
      let newData = [...state.threadList];
      if (payload.type === "deleted")
        newData = newData.filter((item) => item._id !== payload.message_id);

      newData = newData.map((item) => {
        if (item._id === payload.message_id) {
          switch (payload.type) {
            case "add_react":
              return {
                ...item,
                reactions: [
                  ...item.reactions,
                  {
                    _id: uuid(),
                    user_id: payload.sender_id,
                    reaction: payload.newMessage,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString(),
                  },
                ].sort((a, b) => a.reaction - b.reaction),
              };
            case "remove_react": {
              return {
                ...item,
                reactions:
                  item.reactions &&
                  item.reactions
                    ?.filter(
                      (item) =>
                        !(
                          item.user_id === payload.sender_id &&
                          item.reaction === payload.newMessage
                        )
                    )
                    .sort((a, b) => a.reaction - b.reaction),
              };
            }

            case "update": {
              return {
                ...item,
                message: payload.newMessage.message,
                edit: payload.newMessage.edit,
              };
            }
            default:
              break;
          }
        }
        return item;
      });

      return {
        ...state,
        threadList: newData,
      };
    }

    case LEAVE_PARTICIPANT: {
      // payload: room_id, all
      // all means that the group will be removed from the list
      const item = state.membersGroupsChat.find(
        (el) => el.room?._id === payload.room_id
      );
      let totalNotification = state.totalNotificationChat;
      if (item) {
        totalNotification = {
          numberConversationChat:
            state.totalNotificationChat.numberConversationChat > 0
              ? state.totalNotificationChat.numberConversationChat - 1
              : 0,

          total:
            state.totalNotificationChat.total > 0
              ? state.totalNotificationChat.total - item.total_unread > 0
                ? state.totalNotificationChat.total - item.total_unread
                : 0
              : 0,

          conversations: [...state.totalNotificationChat.conversations].filter(
            (el) => el !== item._id
          ),
        };
      }

      return {
        ...state,
        totalNotificationChat: totalNotification,

        selectedParticipants: payload.all
          ? []
          : state.selectedParticipants.filter(
              (el) => el._id !== state.currentUser?._id
            ),
        membersGroupsChat: state.membersGroupsChat.filter(
          (el) => getCurrentItem(el)?._id !== payload.room_id
        ),

        archivedList: state.archivedList.filter(
          (el) => getCurrentItem(el)?._id !== payload.room_id
        ),
      };
    }
    case UPDATE_ROOM_BY_ID_CHAT: {
      const isArchiveConversation = state.archivedListIds.find(
        (item) => item.room_id === payload._id
      );
      const arrayToChange = !isArchiveConversation
        ? [...state.membersGroupsChat]
        : [...state.archivedList];
      const newData = arrayToChange.map((item) => {
        if (item?.room?._id === payload._id) {
          return {
            ...item,
            room: {
              ...item?.room,
              name: payload.name,
              description: payload.description,
              image: payload.image,
              admin_id: payload.admin_id,
            },
          };
        } else {
          return item;
        }
      });

      return {
        ...state,
        membersGroupsChat: sortDataList(
          !isArchiveConversation ? [...newData] : [...state.membersGroupsChat],
          state.currentUser?.config?.sort_message,
          "last_message_date"
        ),
        archivedList: sortDataList(
          isArchiveConversation ? [...newData] : [...state.archivedList],
          state.currentUser?.config?.sort_message,
          "last_message_date"
        ),
      };
    }
    case SET_MEDIAS_LIST: {
      return {
        ...state,
        images: action.payload,
      };
    }
    case SET_DOCUMENTS_LIST: {
      return {
        ...state,
        documents: action.payload,
        documentsFiltred: action.payload,
      };
    }

    case SET_DOCUMENTS_FILTRED_LIST: {
      return {
        ...state,
        documents: action.payload,
      };
    }

    case SET_LINKS_LIST: {
      return {
        ...state,
        links: payload,
        linksFiltred: payload,
      };
    }

    case SET_LINKS_FILTRED_LIST: {
      return {
        ...state,
        links: payload,
      };
    }

    // case "SET_DISABLE_CONNEXION": {
    //   return {
    //     ...state,
    //     logConnexion: payload,
    //   };
    // }
    case SET_LATENCE_TIMEOUT: {
      return {
        ...state,
        latence: payload,
      };
    }
    case SET_OPEN_DRAWER: {
      return {
        ...state,
        openDrawer: payload,
        threadLoading: payload.type === "thread",
        threadList: !payload.type ? [] : [...state.threadList],
      };
    }
    case SET_OPEN_SIDE_BAR_DRAWER: {
      return {
        ...state,
        openSideBarDrawer: payload,
      };
    }

    case SET_STARRED_LIST: {
      let newArray = [...state.starredList, ...payload];

      newArray = uniqByKey(newArray, "_id");

      return {
        ...state,
        starredList: newArray,
      };
    }
    case SET_PINNED_LIST: {
      let newArray = [...state.pinnedList, ...payload];

      newArray = uniqByKey(newArray, "_id");

      return {
        ...state,
        pinnedList: newArray,
      };
    }

    case SET_SEARCH_LIST: {
      return {
        ...state,
        searchList:
          payload?.list?.length === 0
            ? []
            : uniqByKey(
                payload.page === 1
                  ? [...payload.list]
                  : [...state.searchList, ...payload.list],
                "_id"
              ),
      };
    }

    case ADD_STARRED_MESSAGE: {
      return {
        ...state,
        allStarredList: sortDataList(
          [payload, ...state.allStarredList],
          0,
          "created_at"
        ),
        starredList: sortDataList(
          [payload, ...state.starredList],
          0,
          "created_at"
        ),
      };
    }
    case REMOVE_STARRED_MESSAGE: {
      return {
        ...state,
        starredList: sortDataList(
          state.starredList.filter((item) => item._id !== payload._id),
          0,
          "created_at"
        ),
        allStarredList: sortDataList(
          state.allStarredList.filter((item) => item._id !== payload._id),
          0,
          "created_at"
        ),
      };
    }
    case ADD_SAVED_MESSAGE: {
      return {
        ...state,
        pinnedList: sortDataList(
          [payload, ...state.pinnedList],
          0,
          "created_at"
        ),
        allPinnedList: sortDataList(
          [payload, ...state.allPinnedList],
          0,
          "created_at"
        ),
      };
    }
    case REMOVE_SAVED_MESSAGE: {
      return {
        ...state,
        pinnedList: sortDataList(
          state.pinnedList.filter((item) => item._id !== payload._id),
          0,
          "created_at"
        ),

        allPinnedList: sortDataList(
          state.allPinnedList.filter((item) => item._id !== payload._id),
          0,
          "created_at"
        ),
      };
    }
    case ADD_POLL_LIST: {
      return {
        ...state,
        pollList: uniqByKey([payload, ...state.pollList], "_id"),
      };
    }
    case ADD_ALL_POLL_LIST: {
      return {
        ...state,
        allPollList: uniqByKey([payload, ...state.allPollList], "_id"),
      };
    }
    case SET_POLLS_VOTE: {
      return {
        ...state,
        pollsRender: action.payload,
      };
    }

    case UPDATE_POLL_LIST: {
      return {
        ...state,
        pollList: state.pollList.map((item) =>
          item._id === payload._id ? payload : item
        ),
      };
    }
    case UPDATE_ALL_POLL_LIST: {
      return {
        ...state,
        allPollList: state.allPollList.map((item) =>
          item._id === payload._id ? payload : item
        ),
      };
    }

    case REMOVE_POLL_LIST: {
      return {
        ...state,
        pollList: state.pollList.filter((item) => item._id !== payload),
      };
    }
    case REMOVE_ALL__POLL_LIST: {
      return {
        ...state,
        allPollList: state.allPollList.filter((item) => item._id !== payload),
      };
    }

    case SET_ASSETS_COUNT:
      return {
        ...state,
        assetsCount: {
          images: payload.images,
          documents: payload.documents,
          links: payload.links,
        },
      };

    case SET_IMAGE_TO_LIST: {
      return {
        ...state,
        images:
          payload.type === "add"
            ? !state.images.some((image) => image._id === payload.element._id)
              ? [payload.element, ...state.images]
              : [...state.images]
            : state.images.filter((image) => image._id !== payload.element._id),
      };
    }
    case SET_DOCUMENT_TO_LIST: {
      const { element, type } = payload;

      return {
        ...state,
        documents:
          type === "add"
            ? !state.documents.some((doc) => doc._id === element._id)
              ? [element, ...state.documents]
              : [...state.documents]
            : state.documents.filter((doc) => doc._id !== element._id),

        documentsFiltred:
          type === "add"
            ? !state.documentsFiltred.some((doc) => doc._id === element._id)
              ? [element, ...state.documentsFiltred]
              : [...state.documentsFiltred]
            : state.documentsFiltred.filter((doc) => doc._id !== element._id),
      };
    }
    case ADD_LINKS_TO_LIST: {
      return {
        ...state,
        links: [...payload, ...state.links],

        linksFiltred: [...payload, ...state.linksFiltred],
      };
    }
    case SET_LINKS_UPDATE_REMOVE_LIST: {
      const { type, list, message_id } = payload;
      let array = [...state.linksFiltred];
      let countLinks = state.assetsCount.links;
      const oldLinksArray =
        array.filter((item) => item.message_id === message_id) || [];

      if (type === "update") {
        array = array.filter((item) => item.message_id !== message_id);
        array = [...list, ...array];
        countLinks = countLinks + (list.length - oldLinksArray.length);
      } else {
        array = array.filter((item) => item.message_id !== message_id);
        countLinks = countLinks - oldLinksArray.length;
      }

      return {
        ...state,
        links: array,

        linksFiltred: array,
        assetsCount: {
          ...state.assetsCount,
          links: countLinks,
        },
      };
    }

    case SET_ASSETS_TO_LIST: {
      return {
        ...state,
        assetsCount: {
          images:
            payload.images !== undefined
              ? state.assetsCount.images + payload.images
              : state.assetsCount.images,
          documents:
            payload.documents !== undefined
              ? state.assetsCount.documents + payload.documents
              : state.assetsCount.documents,
          links:
            payload.links !== undefined
              ? state.assetsCount.links + payload.links
              : state.assetsCount.links,
        },
      };
    }
    case SET_SEARCH_MESSAGE_TERM: {
      return {
        ...state,
        searchMessageTerm: payload,
      };
    }
    case SET_PINNED_FETCHED: {
      return {
        ...state,
        pinnedListFetched: payload,
        pinnedList: [...state.pinnedList],
      };
    }
    case SET_STARRED_FETCHED: {
      return {
        ...state,
        starredListFetched: payload,
        starredList: [...state.starredList],
      };
    }

    case SET_THREAD_LIST: {
      return {
        ...state,
        threadList: payload,
        threadLoading: false,
      };
    }
    case ADD_THREAD_TO_LIST: {
      // let newArray = [...state.threadList, payload];
      // newArray = newArray.filter((obj, index, array) => {
      //   return index === 0 || obj._id !== array[index - 1]._id;
      // });

      return {
        ...state,
        threadList: uniqByKey([...state.threadList, payload], "_id"),
      };
    }
    case UPDATE_THREAD_TO_LIST: {
      return {
        ...state,
        threadList: state.threadList.map((item) =>
          item._id === payload.id_old ? payload.newMsg : item
        ),
      };
    }

    case SET_MODAL_QUIT_GROUP: {
      return {
        ...state,
        openModalQuitGroup: payload,
      };
    }
    case ADD_ARCHIVE_CONVERSATION: {
      return {
        ...state,
        archivedList: [...state.archivedList, payload.data],
        membersGroupsChat: state.membersGroupsChat.filter(
          (e) => e._id !== payload.data._id
        ),

        count_archived_msg:
          state.count_archived_msg + payload.data.total_unread,
        archivedListIds: [
          ...state.archivedListIds,

          {
            room_id: getCurrentItem(payload.item)?.admin_id
              ? getCurrentItem(payload.item)?._id
              : null,
            receiver_id: getCurrentItem(payload.item)?.admin_id
              ? null
              : getCurrentItem(payload.item)?._id,
          },
        ],
      };
    }
    case REMOVE_ARCHIVE_CONVERSATION: {
      const newArray = state.archivedListIds.filter((e) => {
        if (getCurrentItem(payload.item)?.admin_id) {
          return e.room_id !== getCurrentItem(payload.item)?._id;
        } else {
          return e.receiver_id !== getCurrentItem(payload.item)?._id;
        }
      });
      return {
        ...state,
        archivedList: sortDataList(
          state.archivedList.filter((el) => el._id !== payload.data._id),
          state.currentUser?.config?.sort_message,
          "last_message_date"
        ),
        count_archived_msg:
          state.count_archived_msg > 0
            ? state.count_archived_msg - payload.data.total_unread
            : 0,

        membersGroupsChat: sortDataList(
          [...state.membersGroupsChat, payload.data],
          state.currentUser?.config?.sort_message,
          "last_message_date"
        ),
        archivedListIds: [...newArray],
      };
    }
    case GET_ARCHIVE_CONVERSATIONS: {
      return {
        ...state,
        archivedList: sortDataList(
          payload,
          state.currentUser?.config?.sort_message,
          "last_message_date"
        ),
      };
    }
    case SET_SIDEBAR_DRAWER: {
      return {
        ...state,
        sidebarDrawer: payload,
      };
    }

    case SET_ARCHIVED_LIST_IDS: {
      return {
        ...state,
        archivedListIds: payload.list,
        count_archived_msg: payload.count_archived_msg,
      };
    }
    case ADD_UNREAD_MESSAGE_ARCHIVED: {
      return {
        ...state,
        count_archived_msg: state.count_archived_msg + payload,
      };
    }

    case ADD_TO_MUTE_LIST: {
      const newData = state.membersGroupsChat.map((e) => {
        if (e._id === payload?._id) e.muted_status = true;
        return e;
      });
      return {
        ...state,

        membersGroupsChat: sortDataList(
          [...newData],
          state.currentUser?.config?.sort_message,
          "last_message_date"
        ),
      };
    }
    case REMOVE_FROM_MUTE_LIST: {
      const newData = state.membersGroupsChat.map((e) => {
        if (e._id === payload?._id) e.muted_status = false;
        return e;
      });

      return {
        ...state,

        membersGroupsChat: sortDataList(
          [...newData],
          state.currentUser?.config?.sort_message,
          "last_message_date"
        ),
      };
    }
    case ADD_UUID_TO_MESSAGE: {
      return {
        ...state,
        msgUUID: [...state.msgUUID, payload],
      };
    }
    case DELETE_UUID_TO_MESSAGE: {
      return {
        ...state,
        msgUUID: payload
          ? state.msgUUID.filter((item) => item.uuid !== payload)
          : [...state.msgUUID],
      };
    }
    case RESET_STATE_OTHER_USER: {
      return {
        ...state,
        userIsTyping: 0,

        selectedParticipants: [],
        msgUUID: [],
        mentionState: false,
        numberUnreadMsg: {
          discussion_id: null,
          number: 0,
        },
        syncNewMsg: null,

        scrollToBottomState: null,

        searchMsg: [],
        images: [],
        documents: [],
        documentsFiltred: [],
        links: [],
        linksFiltred: [],
        latence: null,
        openDrawer: payload ? state.openDrawer : { type: "", external: null },
        //openSideBarDrawer: false,
        starredList: [],
        pinnedList: [],
        searchList: [],
        threadList: [],
        threadLoading: false,
        assetsCount: {
          images: undefined,
          documents: undefined,
          links: undefined,
        },
        searchMessageTerm: { value: "", forced: false },
        openModalQuitGroup: false,
        dataConversation: null,
        pollsRender: null,
        starredListFetched: false,
        pinnedListFetched: false,
        allPollListFetched: false,
        pollListFetched: false,

        //msgUUID: [],
      };
    }
    case SET_ALL_STARRED_LIST: {
      let newArray =
        payload.page === 1
          ? [...payload.data]
          : [...state.allStarredList, ...payload.data];
      newArray = uniqByKey(newArray, "_id");
      return {
        ...state,
        allStarredList: newArray,
      };
    }
    case SET_ALL_PINNED_LIST: {
      let newArray =
        payload.page === 1
          ? [...payload.data]
          : [...state.allPinnedList, ...payload.data];
      newArray = uniqByKey(newArray, "_id");
      return {
        ...state,
        allPinnedList: newArray,
      };
    }

    case SET_ALL_POLL_LIST: {
      let newArray =
        payload.page === 1
          ? [...payload.data]
          : [...state.allPollList, ...payload.data];
      newArray = uniqByKey(newArray, "_id");
      return {
        ...state,
        allPollListFetched: true,
        allPollList: newArray,
      };
    }
    case SET_POLL_LIST: {
      let newArray =
        payload.page === 1
          ? [...payload.data]
          : [...state.pollList, ...payload.data];
      newArray = uniqByKey(newArray, "_id");
      return {
        ...state,
        pollListFetched: true,

        pollList: newArray,
      };
    }

    case SET_DATA_CONVERSATION: {
      return {
        ...state,
        dataConversation: payload,
      };
    }

    case HANDLE_TYPING_USER: {
      return {
        ...state,
        userIsTyping: payload,
      };
    }
    case RESET_STATE: {
      return initialState;
    }
    default:
      return state;
  }
};

export default chat;
