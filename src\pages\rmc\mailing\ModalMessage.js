import {
  But<PERSON>,
  Modal,
  Tooltip,
  Input,
  Form,
  Select,
  Col,
  Row,
  Dropdown,
  Typography,
  Divider,
} from "antd";
import { CloseCircleOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import React, { useRef, useEffect, useState, useMemo } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { toastNotification } from "../../../components/ToastNotification";
import Editor from "./editor/Editor";
import MainService from "../../../services/main.service";
import { useSelector } from "react-redux";
import {
  setOpenEditor,
  setOpenModalEmail,
  setRefreshMailVueSphere,
} from "../../../new-redux/actions/mail.actions";
import { useDispatch } from "react-redux";
import DOMPurify from "dompurify";
import parse from "html-react-parser";
import FormItem from "antd/es/form/FormItem";
import useDebounce from "pages/components/UseDebounce/UseDebounce";
import { URL_ENV } from "index";
import { setDataConversation } from "new-redux/actions/chat.actions";
import "./mailing.css";
import { AiOutlineSetting } from "react-icons/ai";
import DragFileField from "pages/clients&users/components/special_fields/DragFileField";
import { trackDraftData } from "./helpers/trackDraftMail";
import { RESET_DRAFT } from "new-redux/constants";
import { useWindowSize } from "pages/clients&users/components/WindowSize";
const { Text, Link } = Typography;
const ModalMessage = ({
  title,
  setRefresh,
  sender,
  receiver,
  receivers,
  defaultObject,
  defaultMessage,
  setOpen,
  setSelectedKey = () => {},
  contactId,
  familyId,
}) => {
  const [senderMail, setSenderMail] = useState();
  const [receiverMail, setReceiverMail] = useState([]);
  const [CcMail, setCcMail] = useState([]);
  const [CciMail, setCciMail] = useState([]);
  const [subject, setSubject] = useState(defaultObject || "");
  const [fileList, setFileList] = useState([]);
  const [value, setValue] = useState(defaultMessage ?? "");
  const [loading, setLoading] = useState(false);
  const [messageError, setMessageError] = useState("");
  const [addLineCc, setAddLineCc] = useState(false);
  const [addLineCci, setAddLineCci] = useState(false);
  const [showFields, setShowFields] = useState(false);
  const [TimeInput, setTimeInput] = useState(false);
  const Receivers = receivers ? receivers : [];
  const [loadingSearchEmail, setLoadingSearchEmail] = useState(false);
  const [dataSearchEmail, setDataSearchEmail] = useState([]);
  const [dataFolder, setDataFolder] = useState([]);
  const [dataFolderFiltred, setDataFolderFiltred] = useState([]);
  const [dataSignature, setDataSignature] = useState([]);

  const [signature, setSignature] = useState("");
  const [showModalVerifTemplate, setShowModalVerifTemplate] = useState(false);
  const [templateResponse, setTemplateResponse] = useState("");
  const quillRef = useRef(null);
  const [t] = useTranslation("common");
  const { accountId } = useParams();
  const editorRef = useRef(null);
  const [form] = Form.useForm();

  const { dataAccounts, openModalEmail, draft } = useSelector(
    (state) => state.mailReducer
  );

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const location = useLocation();
  const windowSize = useWindowSize();
  const usedAccount = useMemo(
    () => dataAccounts?.find((item) => item.selected === true),
    [dataAccounts]
  );
  const [searchEmailSelect, setSearchEmailSelect] = useState("");
  const DebounceSearchEmail = useDebounce(searchEmailSelect, 500);

  const validateEmailsRequired = (rule, value, callback) => {
    // Email regex pattern
    const regex =
      /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    if (!value || value.length === 0) {
      callback(t("mailing.ErrorMail"));
    } else {
      for (let i = 0; i < value.length; i++) {
        if (!regex.test(value[i])) {
          callback(t("mailing.ValidMail"));
          return;
        }
      }

      callback();
    }
  };

  const validateEmailsCC = (rule, value, callback) => {
    if (!value || value.length === 0) {
      callback();
      return;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    for (const email of value) {
      if (!emailRegex.test(email)) {
        callback("Please enter valid email addresses");
        return;
      }
    }

    callback();
  };

  const insertText = () => {
    const editor = editorRef.current.editor;
    editor.insertContent("Text inserted via button click");
  };

  const onFinish = (values) => {
    if (value.length === 0) {
      setMessageError(t("mailing.ErrorMsg"));
    } else {
      PostMail();
    }
  };

  const onFinishFailed = (errorInfo) => {
    console.log("Failed:", errorInfo);
  };

  const getFolderByFamily = async () => {
    try {
      const response = await MainService.folderByFamily();
      if (response.status === 200) {
        let array = [...response.data.data];
        array = array
          .filter((item) => Object.values(item)[0].length > 0)
          .map((item) => ({
            label: Object.keys(item)[0],
            options: Object.values(item)[0].map((el) => ({
              label: el.title,
              value: el.template_id + "/" + el.family_id,
            })),
            // searchOption: element?.label_data,
          }));
        array = [{ label: "Aucune", value: "" }, ...array];
        setDataFolder(array);
        setDataFolderFiltred(array);
      }
    } catch (error) {
      console.log("error", error);
    }
  };

  const getTemplate = async (dataFolderByFamily) => {
    var formData = new FormData();
    formData.append("template_id", dataFolderByFamily.split("/")[0]);
    formData.append("email", receiverMail[0]);
    formData.append("family_id", dataFolderByFamily.split("/")[1]);

    try {
      const response = await MainService.getTemplate(formData);
      if (response.status === 200) {
        setShowModalVerifTemplate(true);
        setTemplateResponse(response.data);
        // setValue(p=>p.concat(response.data));
      }
    } catch (error) {
      toastNotification(
        "error",
        error?.response?.data
          ? error?.response?.data?.message
          : t("toasts.errorFetchApi"),
        "topRight",
        4
      );
    }
  };

  const handleAddTemplate = () => {
    setValue((p) => p.concat(templateResponse));
    form.setFieldValue("message", value.concat(templateResponse));
    setShowModalVerifTemplate(false);
  };

  // const getDefaultSignature = async (e) => {
  //   try {
  //     const response = await MainService.getDefaultSignature(
  //       e?.toString().length > 0 ? e : accountId ? accountId : sender
  //     );
  //     if (response.status === 200) {
  //       setSignature(response?.data.data ?? "");
  //     }
  //   } catch (error) {
  //     console.log("error", error);
  //   }
  // };

  const getSignature = async (e) => {
    try {
      const response = await MainService.getSignature(
        e?.toString()?.length > 0 ? e : accountId ? accountId : sender
      );
      if (response.status === 200) {
        setDataSignature(
          response?.data?.data?.map((item) => ({
            label: item?.label,
            value: item?.value,
          }))
        );
        !signature &&
          setSignature(
            response.data.data?.find((item) => item?.default === true)?.value ??
              response?.data.data?.[0]?.value
          );
      }
    } catch (error) {
      console.log("error", error);
    }
  };

  //Api search email in input TO , CC , BCC
  const searchEmail = async () => {
    setLoadingSearchEmail(true);
    var formData = new FormData();
    formData.append("search", DebounceSearchEmail);
    formData.append("accountId", usedAccount?.value);

    try {
      const response = await MainService.searchEmail(formData);
      if (response.status === 200) {
        setDataSearchEmail(
          response.data.address.map((item) => ({
            label: item,
            value: item,
          }))
        );
        setLoadingSearchEmail(false);
      }
    } catch (error) {
      setLoadingSearchEmail(false);
    }
  };

  function createMarkup(text, type) {
    let sanitizedHtml = DOMPurify.sanitize(text, {
      ALLOWED_TAGS: [],
      ALLOWED_ATTR: [],
      USE_PROFILES: { html: true },
    });
    // regex to get the img tags
    const imgTags = sanitizedHtml.match(/<img[^>]*>/g);
    if (imgTags) {
      imgTags.forEach((imgTag) => {
        // regex to get the img alt attribute

        const alt = imgTag.match(/alt="(.*?)"/);
        if (alt && alt[1]) {
          const altText = alt[1];
          // regex to get the img src attribute

          const srcMatch = imgTag.match(/src="(.*?)"/);
          if (srcMatch && srcMatch[1]) {
            if (type === "sent") {
              const src = srcMatch[1].replace(/[^/]*$/, altText);
              sanitizedHtml = sanitizedHtml.replace(srcMatch[1], src);
            } else {
              const src = srcMatch[1].replace(
                /[^/]*$/,
                URL_ENV?.REACT_APP_BASE_URL + "storage/attachments/" + altText
              );
              sanitizedHtml = sanitizedHtml.replace(srcMatch[1], src);
            }
          }
        }
      });
    }
    if (type === "sent") {
      return sanitizedHtml;
    } else return { __html: sanitizedHtml };
  }

  // API send email
  const PostMail = async () => {
    setLoading(true);
    let i,
      j,
      k = [0, 0, 0];

    var formData = new FormData();
    const from = !senderMail
      ? usedAccount.label
      : senderMail?.accountLabel
      ? senderMail?.accountLabel
      : senderMail;
    formData.append("from", from);
    formData.append(
      "account_id",
      // senderMail === "" ? usedAccount?.value : senderMail
      senderMail?.accountId
        ? senderMail.accountId
        : senderMail?.value
        ? senderMail.value
        : senderMail
        ? senderMail
        : usedAccount?.value
    );
    formData.append("subject", subject);
    formData.append("paramLog", 0);
    if (contactId) {
      formData.append("id_element", contactId);
      formData.append("family_id", familyId);
    }

    formData.append("body", value.concat(signature || ""));
    fileList.forEach((file) =>
      formData.append("attachments[]", file.originFileObj)
    );

    for (i in receiverMail) formData.append("receiver[]", receiverMail[i]);

    for (j in CcMail) formData.append("cc[]", CcMail[j]);

    for (k in CciMail) formData.append("cci[]", CciMail[k]);

    try {
      const url = new URL(window.location.href);
      const response = await MainService.PostMail(formData);
      if (response.status === 200) {
        dispatch({ type: RESET_DRAFT, payload: "newMsg" });
        form.resetFields();
        form.resetFields();
        dispatch(setOpenModalEmail(false));
        setOpen && setOpen(false);
        dispatch(setRefreshMailVueSphere(true));
        if (
          /\/mailing\/(\d+)\/sent/.test(url.pathname) ||
          (/\/mailing\/(\d+)\/inbox/.test(url.pathname) &&
            (receiverMail.includes(usedAccount?.label) ||
              CcMail.includes(usedAccount?.label) ||
              CciMail.includes(usedAccount?.label)))
        )
          setRefresh && setRefresh(true);
        onCancel(false);
        toastNotification(
          "success",
          "Votre email a été envoyé avec succés",
          "topRight",
          3
        );
      }
    } catch (error) {
      setLoading(false);
      toastNotification(
        "error",
        error?.response?.data
          ? error?.response?.data?.message
          : t("toasts.errorFetchApi"),
        "topRight",
        4
      );

      console.log("err", error);
    } finally {
      i = 0;
      j = 0;
      k = 0;
    }
  };

  const handleChange = ({ fileList }) => setFileList([...fileList]);

  useEffect(() => {
    if (DebounceSearchEmail.length > 0) searchEmail();
  }, [DebounceSearchEmail]);

  useEffect(() => {
    if (receiverMail.length > 0) getFolderByFamily();
  }, [receiverMail]);

  const onCancel = (saveDraft = true) => {
    saveDraft && trackDraft();

    setReceiverMail([]);
    setCcMail([]);
    setCciMail([]);
    setSubject("");
    setValue("");
    setAddLineCc(false);
    setAddLineCci(false);
    setTimeInput(false);
    dispatch(setOpenModalEmail(false));
    setSelectedKey("");
    // cancel conversation
    dispatch(setDataConversation(null));
    setOpen && setOpen(false);
  };

  // useEffect(() => {
  //   if (dataAccounts.length > 0) getDefaultSignature();
  // }, []);

  useEffect(() => {
    dispatch(setOpenEditor({ state: false, type: "" }));
    if (receiver) setReceiverMail((p) => [...p, receiver]);
    if (dataAccounts.length > 0) getSignature();

    //render editor after 1ms
    const time = setTimeout(() => {
      setTimeInput(true);
    }, 1);
    return () => {
      clearTimeout(time);
    };
  }, [receiver]);

  useEffect(() => {
    form.setFieldsValue({
      object: defaultObject ?? "",
    });
  }, [form, defaultObject]);

  useEffect(() => {
    if (openModalEmail && quillRef.current) {
      const editor = quillRef.current.getEditor();
      if (editor) {
        editor.focus(); // Focus the editor when modal opens
      }
    }
  }, [openModalEmail]);

  const items = [
    {
      key: "options",
      type: "group",

      label: "Pick your Signature",
      children: [
        usedAccount?.shared === 0
          ? {
              label: t("mailing.none"),
              value: "",
              style: { margin: "2px" },
              key: "",
            }
          : null,
        ...dataSignature?.map((el) => ({
          label: el?.label,
          value: el?.value,
          key: el?.value,
          style: { margin: "2px" },
        })),
      ],
    },
  ];

  const onSearchTemplate = (e) => {
    if (e.length === 0) setDataFolder(dataFolderFiltred);
    else {
      let array = [...dataFolderFiltred];
      array = array
        .map((item) => ({
          ...item,
          options: item?.options?.filter((el) =>
            el?.label?.toLowerCase()?.includes(e.toLowerCase())
          ),
        }))
        .filter((item) => item?.options?.length > 0);
      setDataFolder(array);
    }
  };
  //
  const trackDraft = () => {
    const from = !senderMail
      ? usedAccount?.label
      : senderMail?.accountLabel
      ? senderMail?.accountLabel
      : senderMail;
    dispatch(
      trackDraftData(
        "newMsg",
        from,
        receiverMail,
        CcMail,
        CciMail,
        subject,
        value,
        fileList,
        signature
      )
    );
  };
  // Initialize  draft if existing
  useEffect(() => {
    if (draft.newMsg && location.pathname.split("/")?.[1] === "mailing") {
      const {
        // from,
        receiver,
        cc,
        cci,
        subject,
        body,
        attachments,
        signature,
      } = draft.newMsg;
      if (receiver.length) {
        setReceiverMail(receiver);
        form.setFieldValue("Receiver", receiver);
      }
      if (cc.length) {
        setCcMail(cc);
        setAddLineCc(true);
      }
      if (cci.length) {
        setCciMail(cci);
        cci.length && setAddLineCci(true);
      }
      if (subject) {
        setSubject(subject);
        form.setFieldValue("object", subject);
      }
      if (!!body) {
        setValue(body);
        form.setFieldValue("message", body);
      }
      !!attachments.length && setFileList(attachments);
      !!signature && setSignature(signature);
    }
    return () => {
      setTimeInput(false);
    };
  }, []);

  const handleValueChange = (value) => {
    setValue(value);
    form.setFieldValue("message", value);
  };

  return (
    <Modal
      title={t("mailing.NewMsg.newMsg")}
      open={openModalEmail}
      centered
      onCancel={onCancel}
      // maskClosable={false}
      styles={{
        body: {
          overflowY: "auto",
          maxHeight: "calc(100vh - 20rem)",
          paddingRight: "1rem",
        },
      }}
      width={900}
      footer={[
        <>
          <Divider />

          <div className="flex items-center justify-end space-x-2">
            <Button onClick={onCancel}>{t("mailing.NewMsg.Cancel")}</Button>
            <Button
              htmlType="submit"
              key="submit"
              form="send-email-form"
              disabled={loading}
              loading={loading}
              type="primary"
            >
              {t("mailing.NewMsg.send")}
            </Button>
          </div>
        </>,
      ]}
    >
      {dataAccounts && dataAccounts?.length === 0 ? (
        // location.pathname === `/mailing`
        <>
          <blockquote className="mt-5 flex flex-col space-y-2 text-[#db4646]">
            <q className="text-2xl">Veuillez configurer votre email</q>
          </blockquote>
        </>
      ) : (
        <>
          <Modal
            open={showModalVerifTemplate}
            title={t("mailing.addTemplate")}
            onCancel={() => {
              setShowModalVerifTemplate(false);
              setSelectedKey("");
            }}
            footer={[
              <div className=" flex items-center justify-end space-x-2">
                <Button
                  key="back"
                  onClick={() => setShowModalVerifTemplate(false)}
                >
                  {t("mailing.NewMsg.Cancel")}
                </Button>
                <Button key="submit" type="primary" onClick={handleAddTemplate}>
                  {t("mailing.accept")}
                </Button>
              </div>,
            ]}
          >
            <p>
              {t("mailing.actionTemplate")}
              {t("mailing.confirmAction")}
            </p>
          </Modal>

          <Form
            name="form"
            layout="horizontal"
            form={form}
            labelCol={{
              span: 3,
            }}
            id="send-email-form"
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
            colon={false}
            labelWrap
          >
            <Form.Item
              label={t("mailing.NewMsg.from")}
              // initialValue={
              //   sender
              //     ? sender
              //     : usedAccount?.value && parseInt(usedAccount?.value)
              // }
              initialValue={usedAccount?.value && usedAccount?.value}
              name="sender"
            >
              <Select
                onChange={(value, option) => {
                  // console.log({ value, option });
                  setSenderMail(option);
                  // getDefaultSignature(value);
                  getSignature(option?.accountId || option?.value);
                  setSignature(option?.signature || null);
                }}
                options={refactorDataAccounts(dataAccounts)}
                // value={senderMail ? senderMail : usedAccount?.value}
              />
            </Form.Item>

            <Row style={{ marginLeft: 15 }}>
              <Col span={21}>
                <Form.Item
                  label={t("mailing.NewMsg.To")}
                  name="Receiver"
                  rules={[
                    { required: true, validator: validateEmailsRequired },
                  ]}
                  initialValue={receiver ? [receiver] : receiverMail}
                >
                  <Select
                    loading={loadingSearchEmail}
                    mode="tags"
                    notFoundContent=""
                    placeholder={t("mailing.NewMsg.placeholderMail")}
                    value={receiver ? [receiver] : receiverMail}
                    options={
                      dataSearchEmail.length > 0 ? dataSearchEmail : Receivers
                    }
                    open={!!dataSearchEmail.length}
                    onChange={(e) => {
                      setReceiverMail(e);
                      setDataSearchEmail([]);
                      if (e.length === 0) {
                        form.setFieldValue("Template", "");
                      }
                    }}
                    onSearch={(e) => setSearchEmailSelect(e)}
                  />
                </Form.Item>
              </Col>
              <Col span={1} style={{ marginTop: "3px", marginLeft: "7px" }}>
                <span
                  className="cursor-pointer "
                  onClick={() => {
                    setCcMail([]);
                    form.setFieldsValue({
                      cc: [],
                    });
                    setAddLineCc(!addLineCc);
                  }}
                >
                  Cc
                </span>
              </Col>
              <Col span={1} style={{ marginTop: "3px" }}>
                <span
                  className="cursor-pointer "
                  onClick={() => {
                    setCciMail([]);
                    form.setFieldsValue({
                      cci: [],
                    });
                    setAddLineCci(!addLineCci);
                  }}
                >
                  Cci
                </span>
              </Col>
            </Row>

            {addLineCc ? (
              <>
                <Row style={{ marginLeft: "7px" }}>
                  <Col span={22}>
                    <Form.Item
                      label="Cc"
                      name="cc"
                      rules={[{ required: false, validator: validateEmailsCC }]}
                      initialValue={CcMail}
                    >
                      <Select
                        mode="tags"
                        notFoundContent=""
                        placeholder={t("mailing.NewMsg.placeholderMail")}
                        onChange={(e) => {
                          setCcMail(e);
                          setDataSearchEmail([]);
                        }}
                        options={
                          dataSearchEmail.length > 0 ? dataSearchEmail : []
                        }
                        onSearch={(e) => setSearchEmailSelect(e)}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={2}>
                    <Button
                      type="link"
                      shape="circle"
                      icon={<CloseCircleOutlined />}
                      onClick={() => {
                        setAddLineCc(!addLineCc);
                        setCcMail([]);
                      }}
                    ></Button>
                  </Col>
                </Row>
              </>
            ) : null}
            {addLineCci ? (
              <>
                <Row style={{ marginLeft: "7px" }}>
                  <Col span={22}>
                    <Form.Item
                      label="Cci"
                      name="cci"
                      rules={[{ validator: validateEmailsCC }]}
                      initialValue={CciMail}
                    >
                      <Select
                        mode="tags"
                        notFoundContent=""
                        placeholder={t("mailing.NewMsg.placeholderMail")}
                        onChange={(e) => {
                          setCciMail(e);
                          setDataSearchEmail([]);
                        }}
                        options={
                          dataSearchEmail.length > 0 ? dataSearchEmail : []
                        }
                        onSearch={(e) => setSearchEmailSelect(e)}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={2}>
                    <Button
                      type="link"
                      shape="circle"
                      icon={<CloseCircleOutlined />}
                      onClick={() => {
                        setCciMail([]);
                        setAddLineCci(!addLineCci);
                      }}
                    ></Button>
                  </Col>
                </Row>
              </>
            ) : null}

            <Form.Item
              label={t("mailing.NewMsg.object")}
              name="object"
              required={true}
              rules={[{ required: true, message: t("mailing.ErrorObject") }]}
              initialValue={subject}
            >
              <Input
                // value={subject}
                onChange={(e) => setSubject(e.target.value)}
              />
            </Form.Item>

            <Row style={{ marginLeft: 10 }}>
              <Col span={22}>
                <Form.Item
                  name="Template"
                  tooltip={{
                    title: t("mailing.selectReceiver"),
                  }}
                  label={t("menu2.emailTemplates")}
                >
                  <Select
                    showSearch
                    //  filterOption={filterOption}
                    filterOption={false}
                    defaultActiveFirstOption={false}
                    suffixIcon={null}
                    notFoundContent={null}
                    optionFilterProp="children"
                    disabled={receiverMail.length === 0}
                    onSearch={onSearchTemplate}
                    autoFocus
                    onChange={(e) => {
                      if (e.length > 0) getTemplate(e);
                    }}
                    defaultValue={{ label: "Aucune", value: "" }}
                    options={dataFolder || [{ label: "Aucune", value: "" }]}
                    // style={{ width: "180px" }}
                  />
                </Form.Item>
              </Col>
              <Col span={1} style={{ marginTop: "3px", marginLeft: "7px" }}>
                <Tooltip placement="top" title={t("menu2.emailTemplates")}>
                  <Button
                    type="text"
                    danger
                    icon={
                      <AiOutlineSetting
                        style={{
                          height: "20px",
                          width: "20px",
                          cursor: "pointer",
                        }}
                      />
                    }
                    onClick={() => {
                      navigate("/settings/emailTemplates");
                    }}
                  />
                </Tooltip>
              </Col>
            </Row>
            <Form.Item
              label={t("mailing.NewMsg.message")}
              name="message"
              rules={[{ required: true, message: t("mailing.ErrorMsg") }]}
            >
              {TimeInput ? (
                <div
                  className="quill_editor_modal_mailing"
                  style={{
                    "--editor-modal-mailing-max-height": `${
                      windowSize.height / 3
                    }px`,
                  }}
                >
                  <Editor
                    showFields={showFields}
                    setShowFields={setShowFields}
                    valueEditor={value}
                    setValueEditor={handleValueChange}
                    quillRef={quillRef}
                  />
                </div>
              ) : null}
            </Form.Item>
            {messageError.length > 0 ? (
              <p className="ml-16 text-red-600">{t("mailing.ErrorMsg")}</p>
            ) : null}

            <Form.Item label={t("mailing.Attachments")} name="uploadFile">
              <Tooltip title={t("mailing.NewMsg.file")}>
                <DragFileField
                  t={t}
                  onChange={handleChange}
                  fileList={[...fileList]}
                  className="upload-list-inline"
                />
              </Tooltip>
            </Form.Item>
            {signature && (
              <>
                <Divider className="my-1 py-1" />
                <FormItem label="Signature">
                  <div className="min-h-[30px]  rounded-md border  border-dashed border-gray-300  p-1 text-start">
                    {parse(signature)}
                  </div>
                </FormItem>
              </>
            )}

            <div className=" flex flex-nowrap items-center gap-x-1 text-xs">
              <Text type="secondary" className="">
                {t("mailing.changeSignature")}
              </Text>

              <Dropdown
                disabled={dataSignature.length === 0}
                trigger={["click"]}
                // overlayClassName="w-80 space-y-1"
                placement="topRight"
                menu={{
                  selectable: true,
                  selectedKeys: signature,
                  items,

                  onClick: (e) => {
                    setSignature(e.key);
                  },
                }}
              >
                <Link>{t("mailing.clickHere")}</Link>
              </Dropdown>
            </div>
          </Form>
        </>
      )}
    </Modal>
  );
};

export const refactorDataAccounts = (dataAccounts) => {
  if (!dataAccounts?.length) return [];
  const result = [];
  dataAccounts.forEach(({ alias, label, value, default_signature }) => {
    if (alias.length) {
      alias.forEach((item) => {
        result.push({
          label: (
            <p>
              {item.alias}{" "}
              <span className="text-slate-500">{`<${label}>`}</span>
            </p>
          ),
          // value: item.alias,
          value: item.id,
          accountId: value,
          accountLabel: item.alias,
          signature: item?.Signature,
        });
      });
    }
    result.push({
      label: label,
      value: value,
      signature: default_signature?.value,
      accountLabel: label,
      // accountId: value,
    });
  });
  // console.log({ result });
  return result;
};

export default ModalMessage;
