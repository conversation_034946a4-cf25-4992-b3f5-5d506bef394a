import DOMPurify from "dompurify";
import { URL_ENV } from "index";
import { clearAllExcept } from "new-redux/actions/user.actions/getUser";
import { store } from "new-redux/store";
import { LINK_TO_SENT_REGEX, UUID_REGEX } from "utils/regex";
import orderBy from "lodash/orderBy";

const findOcc = (arr, key1, key2) => {
  let arr2 = [];
  let arrayUser = [];

  arr.forEach((x) => {
    // Checking if there is any object in arr2
    // which contains the key value
    if (
      arr2.some((val) => {
        return val[key1] === x[key1];
      })
    ) {
      // If yes! then increase the occurrence by 1
      arr2.forEach((k) => {
        if (k[key1] === x[key1]) {
          arrayUser.push(k[key2], x[key2]);

          k["count"] = k["count"] + 1;
          k[key2] = [...new Set(arrayUser)];
        }
      });
    } else {
      let a = {};

      // arrayUser.push(x[key2] )
      a[key1] = x[key1];
      a["count"] = 1;
      a[key2] = x[key2];
      arr2.push(a);
    }
  });
  console.log("THIS IS THE REACTION ARRAY", arr2);
  return arr2;
};

const regroupUsersByReactionId = (recs) => {
  const usersByReactionId = {};

  for (const rec of recs) {
    const { reaction, user_id } = rec;

    if (!usersByReactionId.hasOwnProperty(reaction)) {
      usersByReactionId[reaction] = {
        reaction,
        count: 0,
        user_id: [],
      };
    }

    usersByReactionId[reaction].count++;
    usersByReactionId[reaction].user_id.push(user_id);
  }

  return Object.values(usersByReactionId);
};

const react_emojis = [
  {
    name: "like",
    image: "/emoji/like.svg",
  },
  {
    name: "love",
    image: "/emoji/love.svg",
  },
  // {
  //   name: "bravo",
  //   image: "/emoji/angry.svg",
  // },
  // {
  //   name: "insightful",
  //   image: "/emoji/insightful.svg",
  // },
  {
    name: "haha",
    image: "/emoji/haha.svg",
  },
  {
    name: "sad",
    image: "/emoji/sad.svg",
  },
  {
    name: "wow",
    image: "/emoji/wow.svg",
  },
  {
    name: "angry",
    image: "/emoji/angry.svg",
  },
];
function getName(name = "", type = "") {
  let nameString = "";

  switch (type) {
    case "avatar": {
      if (name) {
        if (name.includes("@")) {
          // Extract the first two letters before '@'
          const emailPrefix = name
            .split("@")[0]
            .replace(/[`~!#$%^&*()_|+\-=?;:'",.<>\{\}\[\]\\\/]/gi, "");
          return emailPrefix.length > 2
            ? emailPrefix.slice(0, 2).toUpperCase()
            : emailPrefix.toUpperCase();
        } else {
          const words = name.split(/\s|\_/);
          words.forEach((word) => {
            if (word.match(/^[a-zA-Z\u00C0-\u017F\-0-9]+$/)) {
              nameString += word[0].toUpperCase();
            }
          });

          return nameString.length > 2 ? nameString.slice(0, 2) : nameString;
        }
      }
      break;
    }
    case "name": {
      if (name && typeof name === "string") {
        let nameString = "";
        nameString = name.replaceAll("_", " ");
        nameString = nameString[0].toLocaleUpperCase() + nameString.slice(1);
        return nameString.trim();
      }
      break;
    }

    default:
      return;
  }
}
const getUserFromMsg = (sender_id = 0, elem = null) => {
  const currentUser = store.getState().chat.currentUser;
  const selectedParticipants = store.getState().chat.selectedParticipants;
  // Check if the sender_id is a UUID / exp : in case activity
  const isUUID = UUID_REGEX.test(sender_id);
  const key = isUUID ? "uuid" : "_id";
  const userInUserList = selectedParticipants.find(
    (user) => user[key] === sender_id
  );

  let item =
    elem?.sender ??
    (currentUser?.[key] === sender_id ? currentUser : userInUserList);

  if (!item) {
    const userList = store.getState().chat.userList;
    item = userList.find((user) => user[key] === sender_id);
  }
  return item;
};

function normFile(e) {
  if (Array.isArray(e)) {
    return e;
  }
  return e?.fileList;
}
function isTheDay(date, type, compareDate) {
  if (type === "Y") compareDate.setDate(compareDate.getDate() - 1);

  if (
    compareDate.getFullYear() === date.getFullYear() &&
    compareDate.getMonth() === date.getMonth() &&
    compareDate.getDate() === date.getDate()
  ) {
    return true;
  }

  return false;
}

const uuid = (mask = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx") => {
  return `${mask}`.replace(/[xy]/g, function (c) {
    let r = (Math.random() * 16) | 0,
      v = c === "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
};
function generateCodeSnippets(botId, apiToken) {
  const baseUrl =
    URL_ENV?.REACT_APP_OAUTH_CHAT_API + "/" + process.env.REACT_APP_SUFFIX_API;

  const phpCurlCode = `
  <p>
  <?php <br>$curl = curl_init();<br><br>curl_setopt_array($curl, array(<br> CURLOPT_URL =&gt; '${baseUrl}send-message-bot',<br> CURLOPT_RETURNTRANSFER =&gt; true,<br> CURLOPT_ENCODING =&gt; '',<br> CURLOPT_MAXREDIRS =&gt; 10,<br> CURLOPT_TIMEOUT =&gt; 0,<br> CURLOPT_FOLLOWLOCATION =&gt; true,<br> CURLOPT_HTTP_VERSION =&gt; CURL_HTTP_VERSION_1_1,<br> CURLOPT_CUSTOMREQUEST =&gt; 'POST',<br> CURLOPT_POSTFIELDS =&gt; array('bot_id' =&gt; '${botId}','message' =&gt; 'test message'),<br> CURLOPT_HTTPHEADER =&gt; array(<br> 'Authorization: ${apiToken}'<br> ),<br>));<br><br>$response = curl_exec($curl);<br><br>curl_close($curl);<br>echo $response;<br><br>
</p>
    
    `;

  const curlCode = `
  curl --location '${baseUrl}send-message-bot' \<br>--header 'Authorization: ${apiToken}' \<br>--form 'bot_id="${botId}"' \<br>--form 'message="test message"'
    `;

  const javascriptFetchCode = `
  var myHeaders = new Headers();<br>myHeaders.append("Authorization", "${apiToken}");<br>
  <br>var formdata = new FormData();<br>formdata.append("bot_id", "${botId}");<br>formdata.append("message", "test message");<br>
  <br>
  var requestOptions = {<br> method: 'POST',<br> headers: myHeaders,<br> body: formdata,<br> redirect: 'follow'<br>};<br><br>
  fetch("${baseUrl}send-message-bot", requestOptions)<br> .then(response =&gt; response.text())<br> .then(result =&gt; console.log(result))<br> .catch(error =&gt; console.log('error', error));

    `;

  const pythonRequestsCode = `
  <p>import requests</p>
  <p>url = "${baseUrl}send-message-bot"</p>
  <p>payload = {'bot_id': '${botId}',</p>
  <p>'message': 'test message'}</p>
  <p>headers = {</p>
  <p> 'Authorization': '${apiToken}'</p>
  <p>}</p>
  <p>response = requests.request("POST", url, headers=headers, data=payload, files=files)</p>
  <p>print(response.text)</p><br> 
    `;

  return {
    PHPcURL: phpCurlCode,
    cURL: curlCode,
    JavaScriptFetch: javascriptFetchCode,
    PythonRequests: pythonRequestsCode,
  };
}

const imageExtensions = [
  "jpg",
  "jpeg",
  "png",
  "gif",
  "bmp",
  "tif",
  "tiff",
  "raw",
  "svg",
  "icon",
  "webp",
];

const videoExtensions = [
  "mp4",
  "avi",
  "mov",
  "mpg",
  "mpeg",
  "webm",
  "webp",
  "quicktime",
];
const audioExtentions = [
  "mp3",
  "wav",
  "aiff",
  "flac",
  "aac",
  "wma",
  "ogg",
  "mpeg",
];
const simpleMessageTypes = [
  "poll_message",
  "message",
  "forward_message",
  "forward_message_room",
  "mixed_image",
  "forward_mixed_image",
  "forward_mixed_image_room",
  "mixed_file",
  "forward_mixed_file",
  "forward_mixed_file_room",
  "message_from_bot",
  "replay_mixed_image",
  "replay_mixed_file",
  "replay",
];
const forwardMessageTypes = [
  "forward_message",
  "forward_mixed_image",
  "forward_mixed_file",
  "forward_message_room",
  "forward_mixed_image_room",
  "forward_mixed_file_room",
  "forward_file",
  "forward_image",
  "forward_voice",
  "forward_file_room",
  "forward_image_room",
  "forward_voice_room",
];
const fileMessageTypes = [
  "file",
  "forward_file",
  "forward_file_room",

  "mixed_file",
  "forward_mixed_file",
  "forward_mixed_file_room",
  "file",
  "replay_file",
  "replay_mixed_file",
];
const fileMessageReplyType = [
  "file",
  "replay_file",
  "replay_mixed_file",
  "replay_voice",
  "replay_voice_room",
];

const imageMessageTypes = [
  "image",
  "forward_image",
  "forward_image_room",
  "mixed_image",
  "forward_mixed_image",
  "forward_mixed_image_room",
  "replay_image",
  "replay_mixed_image",
];

const audioMessageTypes = [
  "voice",
  "forward_voice",
  "forward_voice_room",
  "replay_voice",
  "replay_voice_room",
];
const systemMessageTypes = [
  "message_missed_call",
  "message_received_call",
  "message_task",
  "message_visio_conf",
  "message_system_add_user_room",
  "message_system_remove_user_room",
  "message_system_update_room",
  "message_system_leave_user_room",
];

const systemMessageTypesGroups = [
  "message_system_add_user_room",
  "message_system_remove_user_room",
  "message_system_update_room",
  "message_system_leave_user_room",
];
const simpleMessageReplyTypes = [
  "replay_mixed_image",
  "replay_mixed_file",
  "replay",
];
const imageMessageReplyType = ["image", "replay_image", "replay_mixed_image"];
const urlChatComponent = [
  "/chat",
  "/logs",
  "/directory/colleagues",
  "/directory/phone-book",
  // "/voip/messaging",
];
const drawerTypeArray = ["starred", "pinned", "forward", "webhook", "search"];

const NUMBER_CHAR_MAX = 700;
function checkContentType(arr) {
  try {
    let containsImage = false;
    let containsVideo = false;
    let containsAudio = false;
    let containsFile = false;
    if (arr && Array(arr)) {
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].type.startsWith("image")) {
          containsImage = true;
        } else if (arr[i].type.startsWith("audio")) {
          containsAudio = true;
        } else if (arr[i].type.startsWith("video")) {
          containsVideo = true;
        } else {
          containsFile = true;
        }

        // if both containsImage and containsVideo and containsAudio are true, we have a mix of file types
        const typesCount = [
          containsImage,
          containsAudio,
          containsVideo,
          containsFile,
        ].filter(Boolean).length;

        if (typesCount > 1) {
          return "mixed";
        }
      }
    }

    if (containsImage) return "image";
    if (containsVideo) return "video";
    if (containsAudio) return "audio";
    if (containsFile) return "file";

    return null;
  } catch (error) {
    console.log(error);
  }
}

async function stopStream() {
  try {
    await navigator?.mediaDevices?.getUserMedia(
      { audio: true },

      function (stream) {
        stream.getTracks().forEach(function (track) {
          track.stop();
        });
      },
      function (error) {
        console.log("getUserMedia() error", error);
      }
    );
  } catch (error) {}
}
function getMessageType(msg, files, from) {
  let hasMessage = msg !== null && msg !== undefined && msg !== "";
  let hasImage =
    files !== null && files !== undefined && files.length > 0
      ? files.every((file) => file.type.startsWith("image"))
      : false;
  let hasMixed =
    (hasMessage && files !== null && files !== undefined && files.length > 0) ||
    (hasImage && msg !== null && msg !== undefined && msg !== "");
  let hasReplay = from;

  if (hasReplay) {
    if (hasMixed) {
      if (hasImage) {
        return "replay_mixed_image";
      } else {
        return "replay_mixed_file";
      }
    } else if (hasImage) {
      return "replay_image";
    } else if (hasMessage) {
      return "replay";
    } else {
      return "replay_file";
    }
  } else {
    if (hasMixed) {
      if (hasImage) {
        return "mixed_image";
      } else {
        return "mixed_file";
      }
    } else if (hasMessage) {
      return "message";
    } else if (hasImage) {
      return "image";
    } else {
      return "file";
    }
  }
}

const objectNewMessage = (
  type,
  _id,
  user_sender,
  user_receiver,
  file,
  message,
  tags,
  from,
  type_message,
  default_unread,
  main_message = null,
  poll = null
) => ({
  // indicate is local message
  locale: true,
  admin_id: type === "room" ? user_receiver.admin_id : null,
  bot: type === "room" ? user_receiver.bot : null,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  deleted_at: null,
  edit: 0,
  favoris: [],
  file:
    file && file.length > 0
      ? file.map((f) => ({
          _id: f._id,
          file_name: f.file_name,
          path: f.path,
          size: f.size,
          type: f.type,
          thumbnail_url: f.thumbnail_url,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }))
      : null,

  file_id: file.length > 0 ? [file.map((file) => file._id).join(",")] : [],
  _id,
  main_message,
  message: message?.trim() ?? null,
  parent_id: main_message ? main_message?._id : null,
  // valid only for private / public disc (0: private, 1: public)
  private: "0",

  reactions: [],
  important: "",

  poll:
    type_message === "poll_message" && poll
      ? {
          created_at: new Date().toISOString(),
          id: poll?._id,
          multi_answer: poll?.multi_answer,
          options: poll?.options,
          question: poll?.question,
          updated_at: new Date().toISOString(),
        }
      : null,
  receiver_id: type === "user" ? user_receiver?.id : undefined,
  receiver_uuid: type === "user" ? user_receiver?.uuid : undefined,
  replies: [],
  room_id: type === "room" ? user_receiver?.id : undefined,
  room_info: type === "room" ? user_receiver : null,
  sender: {
    email: user_sender?.email,
    _id: user_sender?._id,
    image: user_sender?.image,
    name: user_sender?.name,
    post_number: user_sender?.post_number,
    role: "user",
    status: 1,
    uuid: user_sender?.uuid,
  },
  sender_id: user_sender?._id,
  sender_uuid: user_sender?.uuid,
  tags: type === "room" ? tags ?? "" : undefined,
  type: type_message ?? getMessageType(message, file, from),
  // unread: 0 for read, 1 for unread, 2 for waiting and error: is error message
  unread: default_unread ?? 2,
  unread_room: [],

  voice: null,
});

const createSideBarMessage = ({ id, message, contact, room, type }) => {
  const {
    id: messageId,
    created_at,
    message: msg,
    type: messageType,
    updated_at,
    unread,
    sender,
    reaction,
  } = message;
  const { _id: userId, name, image, post_number } = contact;
  const { _id: roomId, name: roomName, image: roomImage } = room;

  return {
    id,
    last_message_date: updated_at,
    last_message: {
      _id: messageId,
      message: msg,
      created_at,
      unread,
      type: messageType,
    },
    reaction,
    messageType,
    sender,
    contact:
      type === "room"
        ? null
        : {
            _id: userId,
            name,
            post_number,
            image,
          },
    room:
      type === "user"
        ? null
        : {
            _id: roomId,
            name: roomName,
            image: roomImage,
          },
    type,
  };
};
const targetTags = [
  "html",
  "a",
  "abbr",
  "address",
  "area",
  "article",
  "aside",
  "b",
  "blockquote",
  "body",
  "br",
  "caption",
  "cite",
  "code",
  "col",
  "colgroup",
  "data",
  "datalist",
  "dd",
  "del",
  "details",
  "dfn",
  "div",
  "dl",
  "dt",
  "em",
  "fieldset",
  "figcaption",
  "figure",
  "footer",
  "h1",
  "h2",
  "h3",
  "h4",
  "h5",
  "h6",
  "head",
  "header",
  "hr",
  "i",
  "label",
  "legend",
  "li",
  "main",
  "mark",
  "nav",
  "object",
  "ol",
  "p",
  "param",
  "pre",
  "q",
  "rp",
  "rt",
  "svg",
  "s",
  "section",
  "small",
  "span",
  "strong",
  "sub",
  "summary",
  "sup",
  "table",
  "tbody",
  "td",
  "tfoot",
  "th",
  "thead",
  "time",
  "title",
  "tr",
  "u",
  "ul",

  // "br",
  // "p",
  // "pre",
  // "code",
  // "span",
  // "strong",
  // "em",
  // "u",
  // "s",
  // "h1",
  // "h2",
  // "h3",
  // "ul",
  // "ol",
  // "li",
  // "div",
  // "table",
  // "td",
  // "tr",
  // "mark",
  // "a",
];
const configDOM = {
  ALLOWED_TAGS: targetTags,
  ALLOWED_ATTR: [
    "style",
    "width",
    "aria-label",
    "userid",
    "current-user",
    "data-type",
    "class",
  ],
};
const safeText = (text) => DOMPurify.sanitize(text, configDOM);
function convertToPlain(html, addReturn = false) {
  let newHtml = safeText(html);

  // Create a new div element
  var tempDivElement = document.createElement("div");
  if (addReturn) newHtml = newHtml.replace(/<\/p>/g, "\n</p>");
  tempDivElement.innerHTML = newHtml;

  const mentionSpans = tempDivElement.querySelectorAll(
    'span[data-type="mention"]'
  );
  mentionSpans.forEach((span) => {
    span.innerText += " ";
  });

  tempDivElement.innerHTML = tempDivElement.innerHTML
    .replace(/(<[^>]*>)\s+/g, "$1")
    .replace(/&nbsp;/g, " ");
  const text = tempDivElement.textContent || tempDivElement.innerText || "";

  let newMessage = insertSpaceBeforeTagOpening(text.trim());

  newMessage = insertSpaceAfterTag(newMessage);
  newMessage = newMessage === "<p></p>" ? "" : newMessage;
  // Retrieve the text property of the element
  return newMessage;
}

const getOrGenerateTabId = () => {
  const keySize = 256 / 8; // 256 bits
  const randomBytes = new Uint8Array(keySize);
  crypto.getRandomValues(randomBytes);
  const secret_key =
    sessionStorage.getItem("tab_id") ??
    Array.from(randomBytes, (byte) => byte.toString(32).padStart(2, "0")).join(
      ""
    );
  if (!sessionStorage.getItem("tab_id"))
    sessionStorage.setItem("tab_id", secret_key);

  return secret_key;
};

function insertSpaceBeforeTagOpening(message) {
  const regex = new RegExp(
    `<(?!\\/?(${targetTags.join("|")})(>| [^>]*>))`,
    "g"
  );
  return message.replace(regex, "< ");
}

function insertSpaceAfterTag(message) {
  const targetTag = [
    "strong",
    "s",
    "em",
    "u",
    "span",
    "h1",
    "h2",
    "h3",
    "h4",
    "h5",
    "h6",
  ];
  const regex = new RegExp(`<(\\/?)(${targetTag.join("|")})(>| [^>]*>)`, "g");
  return message.replace(regex, (match, p1, p2, p3) => {
    if (p1 === "/") {
      return `</${p2}>&nbsp;`;
    } else {
      return match;
    }
  });
}
function enleverAccents(str) {
  try {
    return str?.normalize("NFD")?.replace(/[\u0300-\u036f]/g, "");
  } catch (e) {
    return str;
  }
}
function getUrlsFromMessage(message) {
  let url = [];
  let newMessagePlainText = convertToPlain(message, true);
  if (newMessagePlainText.length === 0) return url;
  newMessagePlainText = newMessagePlainText.replace("\n", " ");
  const urlsMatch = newMessagePlainText.match(LINK_TO_SENT_REGEX);
  url = [...new Set(urlsMatch)];
  return url;
}
const addHttpIfNeeded = (url = "") => {
  const trimmedUrl = url.trim();

  if (!trimmedUrl) return "";

  return /^https?:\/\//i.test(trimmedUrl)
    ? trimmedUrl
    : `https://${trimmedUrl}`;
};
const accepetedExtentionImage = ["JPG", "PNG", "JPEG", "SVG+XML", "WEBP"];

const LogoutLink = (noSuffix) => {
  let url = URL_ENV?.REACT_APP_REDIRECT_URL_AUTH;
  if (!url) {
    const urlHref = new URL(window.location.href);

    const token = decodeJWT(
      localStorage.getItem("accessToken") ??
        urlHref.searchParams.get("access_token")
    );
    url = "https://" + token?.url_auth_front;
  }
  const isUrlVerified = !url ? false : url?.indexOf("https://") !== -1;
  if (isUrlVerified) {
    clearAllExcept();
    return (window.location.href = url + (noSuffix ? "" : "/logout"));
  } else {
    console.error("Error while redirecting to logout!");
    return (window.location.href = process.env?.REACT_APP_AUTH_URL + "/logout");
  }
};

const isRoom = (item) => item.room !== null && item.contact === null;
const isGuestRoom = (item) =>
  isRoom(item) && Number(item.room?.predefined) === 5;
const isPublicRoom = (item) =>
  isRoom(item) && [2, 5, 6].includes(Number(item.room?.predefined));
const specialCharacters = (query) => {
  try {
    if (!query) return "";
    return query.replace(/[`~!$%^&*|+\/,-=?;:'"<>]/gi, "");
  } catch (e) {
    return query;
  }
};

const handleDownloadFile = (e, file, cb) => {
  if (e) e.preventDefault();
  cb && cb(true);
  if (file) {
    fetch(
      URL_ENV?.REACT_APP_OAUTH_CHAT_API +
        process.env.REACT_APP_SUFFIX_CHAT_GROUP_STORAGE +
        file?.path
    )
      .then((response) => response.blob())
      .then((blob) => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.style.display = "none";
        a.href = url;
        a.download = file.file_name;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
      })
      .catch(() => alert("Une erreur s'est produites"))
      .finally(() => cb && cb(false));
  }
};
const uniqByKey = (array, key) => {
  return [...new Map(array.map((item) => [item[key], item])).values()];
};
// get current item : user/ group/ bot
const getCurrentItem = (item) => {
  if (item.contact !== null) {
    return item.contact;
  } else if (item.room !== null) {
    return item.room;
  } else if (item.bot !== null) {
    return item.bot;
  }
};
const sortDataList = (list = [], sort_message = 0, date = "") => {
  // applied when search in side bar is active (fake id for user has been given to avoid conflict with room id)
  const listUser = list.filter((item) => typeof item.type_item === "string");
  // list of data with id number (room or user)
  let filteredData = list.filter(
    (item) => typeof item.type_item === "undefined"
  );
  // sort data list
  if (sort_message === 1)
    filteredData = orderBy(
      filteredData,
      [(e) => getCurrentItem(e)?.name?.toLowerCase()],
      ["asc"]
    );
  else if (sort_message === 0)
    filteredData = orderBy(
      filteredData,
      [(e) => new Date(e && e[date])],
      ["desc"]
    );
  else if (sort_message === 2)
    filteredData = orderBy(filteredData, [(e) => e.total_unread], ["desc"]);
  let finalArray = [...filteredData];

  if (listUser.length > 0)
    finalArray = [...filteredData, ...listUser].sort((a, b) => {
      if (
        typeof a.type_item === "string" &&
        typeof b.type_item === "undefined"
      ) {
        return 1;
      } else if (
        typeof a.type_item === "undefined" &&
        typeof b.type_item === "string"
      ) {
        return -1;
      } else return 0;
    });
  return uniqByKey(finalArray, "_id");
};
const LATENCE_ADDING_TIME = 3000;

const formatAssetsCount = (count) => {
  const formattedCount = new Intl.NumberFormat().format(count);
  return count > 99 ? "+99" : formattedCount;
};
const decodeJWT = (jwt) => {
  if (!jwt) return null;
  const base64Url = jwt.split(".")[1];
  const base64 = base64Url.replace(/-/g, "+").replace(/_/g, "/"); // Replace URL-safe characters
  const jsonPayload = decodeURIComponent(
    atob(base64)
      ?.split("")
      ?.map((c) => `%${("00" + c.charCodeAt(0).toString(16)).slice(-2)}`)
      ?.join("")
  );
  return JSON.parse(jsonPayload);
};
// const getSelectedFilter = (data) => {
//   const conditions = [
//     { key: "conversations", value: data?.conversations > 0 },
//     { key: "users", value: data.users > 0 },
//     { key: "messages", value: data.messages > 0 },
//   ];

//   // Check if all conditions are true
//   const allFull = conditions.every((condition) => condition.value);

//   // Check how many are full
//   const activeFilters = conditions.filter((condition) => condition.value);

//   // Determine the filter result
//   let filter;
//   if (allFull) {
//     filter = "all";
//   } else if (activeFilters.length > 0) {
//     filter = activeFilters[0].key;
//   } else {
//     filter = "none";
//   }
//   return filter;
// };
const TAB_LIST_HEIGHT = "460px";

export {
  decodeJWT,
  // getSelectedFilter,
  formatAssetsCount,
  LATENCE_ADDING_TIME,
  sortDataList,
  getCurrentItem,
  uniqByKey,
  specialCharacters,
  isRoom,
  isPublicRoom,
  isGuestRoom,
  LogoutLink,
  enleverAccents,
  safeText,
  convertToPlain,
  getOrGenerateTabId,
  targetTags,
  insertSpaceBeforeTagOpening,
  insertSpaceAfterTag,
  findOcc,
  stopStream,
  react_emojis,
  getName,
  normFile,
  isTheDay,
  uuid,
  checkContentType,
  fileMessageTypes,
  simpleMessageTypes,
  forwardMessageTypes,
  imageMessageTypes,
  audioMessageTypes,
  systemMessageTypes,
  systemMessageTypesGroups,
  fileMessageReplyType,
  simpleMessageReplyTypes,
  imageMessageReplyType,
  imageExtensions,
  videoExtensions,
  audioExtentions,
  accepetedExtentionImage,
  getMessageType,
  objectNewMessage,
  regroupUsersByReactionId,
  generateCodeSnippets,
  createSideBarMessage,
  getUrlsFromMessage,
  urlChatComponent,
  NUMBER_CHAR_MAX,
  addHttpIfNeeded,
  drawerTypeArray,
  getUserFromMsg,
  handleDownloadFile,
  TAB_LIST_HEIGHT,
};
