import { useInfiniteQuery } from "@tanstack/react-query";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";

import MainService from "services/main.service";
import { toastNotification } from "components/ToastNotification";
import { useCallback } from "react";
import { STALE_TIME_CACHE } from "..";
import { getStatsChat } from "new-redux/actions/dashboard.actions";
import { useDispatch } from "react-redux";
import { useLocation } from "react-router-dom";

function useGetMessage() {
  const { t } = useTranslation("common");
  const dispatch = useDispatch();
  const { pathname } = useLocation();
  const selectedConversation = useSelector(
    (state) => state.ChatRealTime.selectedConversation
  );
  const searchMsgState = useSelector((state) => state.chat.searchMsgState);

  const fetchMessages = useCallback(
    async (
      signal,
      pageParam = 1,
      discussion_id,
      discussion_type,
      externalType
    ) => {
      return new Promise(async (resolve, reject) => {
        try {
          const response = await MainService.getDiscussion(
            discussion_id,
            pageParam,
            discussion_type,
            externalType,
            signal
          );
          if (response?.data?.success) {
            resolve(response.data);
            pathname === "dashboard" && dispatch(getStatsChat());
          } else reject(new Error(response?.data?.message));
        } catch (e) {
          reject(e);
        }
      }).catch((e) => {
        if (e && e.name === "AbortError") {
          signal.abort();
          return;
        }
        throw e;
      });
    },
    []
  );
  const {
    fetchStatus,
    status,
    data: getMessagesData,
    fetchNextPage,
    refetch,
    isRefetching,
    isFetchingNextPage,
    isError,
    error,
    hasNextPage,
  } = useInfiniteQuery({
    enabled:
      selectedConversation?.source &&
      (selectedConversation?.source !== "chat" ||
        selectedConversation?.conversationId) &&
      !searchMsgState.id &&
      !searchMsgState.loading &&
      selectedConversation?.id &&
      !selectedConversation?.external
        ? true
        : false,
    queryKey: [
      "getMessages",
      selectedConversation?.id,
      selectedConversation?.type,
    ],

    queryFn: ({ signal, pageParam }) =>
      fetchMessages(
        signal,
        pageParam,
        selectedConversation?.source === "chat"
          ? selectedConversation?.conversationId
          : selectedConversation?.id,
        selectedConversation?.type,
        selectedConversation?.source !== "chat"
      ),
    getNextPageParam: (pageParams, allPages) => {
      try {
        if (typeof pageParams !== "object") return undefined;
        if (pageParams && !Object.keys(pageParams).includes("meta"))
          return undefined;
        return pageParams.meta.current_page >= pageParams.meta.last_page
          ? undefined
          : allPages.length + 1;
      } catch (error) {
        if (error && error.name === "CanceledError") {
          return;
        }
      }
    },
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    staleTime: STALE_TIME_CACHE,
    retry: 2,
    retryOnMount: false,
    retryDelay: 500,
    // cacheTime: 0,

    onError: (e) => {
      if (e.name !== "CanceledError")
        toastNotification("error", t("toasts.errorFetchApi"), "topRight");
    },
  });

  return {
    fetchStatus,
    status,
    getMessagesData,
    fetchNextPage,
    refetch,
    isRefetching,
    isFetchingNextPage,
    isError,
    error,
    hasNextPage,
  };
}

export default useGetMessage;
