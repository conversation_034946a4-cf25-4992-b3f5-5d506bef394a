import React from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
const BarStacked = ({ pivotData }) => {
  if (
    !pivotData ||
    !pivotData.rowKeys ||
    !pivotData.colKeys ||
    !pivotData.getAggregator
  ) {
    return <p className="text-center text-red-600">No Data</p>;
  }
  const rowKeys = pivotData.rowKeys;
  const colKeys = pivotData.colKeys;
  if (rowKeys.length === 0 && colKeys.length === 0) {
    return <p className="text-center text-red-600">No Data</p>;
  }
  const isRow = rowKeys.length > 0;
  const baseKeys = isRow ? rowKeys : colKeys;
  const seriesKeys = isRow ? colKeys : rowKeys;
  const categories = baseKeys.map((key) => key.join(" - "));
  const series =
    seriesKeys.length > 0
      ? seriesKeys.map((seriesKey) => ({
          name: seriesKey.join(" - ") || "Total",
          data: baseKeys.map((baseKey) => {
            const r = isRow ? baseKey : seriesKey;
            const c = isRow ? seriesKey : baseKey;
            const value = pivotData.getAggregator(r, c)?.value() || 0;
            return {
              y: value,
              z: value,
            };
          }),
        }))
      : [
          {
            name: "Total",
            data: baseKeys.map((baseKey) => {
              const r = isRow ? baseKey : [];
              const c = isRow ? [] : baseKey;
              const value = pivotData.getAggregator(r, c)?.value() || 0;
              return {
                y: value,
                z: value,
              };
            }),
          },
        ];

  const options = {
    chart: {
      type: "bar",
    },
    title: {
      text: `📊
            ${pivotData.props.rows.length ? pivotData.props.rows.join(" ") : ""}
            ${
              pivotData.props.cols.length
                ? " : " + pivotData.props.cols.join(" ")
                : ""
            }`,
      align: "left",
      dispalay: "block",
      fontFamily: "Arial, sans-serif",
      style: {
        fontWeight: "normal",
        fontSize: "20px",
        fontFamily: "Inter, sans-serif",
      },
    },
    xAxis: {
      categories: categories,
    },
    yAxis: {
      min: 0,
    },
    legend: {
      reversed: true,
    },
    plotOptions: {
      series: {
        stacking: "normal",
        dataLabels: {
          enabled: true,
          formatter: function () {
            return this.y === 0 ? null : this.y;
          },
        },
      },
    },
    credits: {
      enabled: false,
    },
    series: series,
  };
  return (
    <div
      style={{ height: "100%", width: "100%" }}
      className="highcharts-figure"
    >
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
};

export default BarStacked;
