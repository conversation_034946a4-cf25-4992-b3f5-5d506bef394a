import {
  GET_PARAM_SUCCESS,
  GET_PARAM_ERROR,
  SET_CMKPHONE_INSTANCE,
} from "../../constants";
import MainService from "../../../services/main.service";
import { toastNotification } from "components/ToastNotification";
import i18next from "i18next";
import { Button, notification } from "antd";
import { ReloadOutlined } from "@ant-design/icons";
export const getParamIPBX = (poste) => async (dispatch) => {
  try {
    const abortController = new AbortController();
    setTimeout(() => {
      abortController.abort();
    }, 20000);

    const response = await MainService.paramApiIPBX(
      poste,
      abortController.signal
    );

    if (response.status === 200) {
      dispatch({
        type: GET_PARAM_SUCCESS,
        payload: response?.data || null,
      });
      notification.destroy("notification-webphone-error");
    } else throw new Error(response.data);
  } catch (error) {
    dispatch({
      type: SET_CMKPHONE_INSTANCE,
      payload: {
        status: "warning",
        phone: null,
      },
    });
    toastNotification(
      "error",
      <div className="flex w-full flex-col items-center justify-center ">
        <span>{i18next.t("common:webphone.errorGetData")}</span>
        <Button
          type="primary"
          danger
          onClick={() => getParamIPBX(poste)(dispatch)}
          className="mt-2 w-32"
          icon={<ReloadOutlined />}
        >
          {i18next.t("common:chat.reload")}
        </Button>
      </div>,
      "topLeft",
      0,
      null,
      1,
      "notification-webphone-error"
    );
    // error diffrent then timeout error
    if (error?.message === "ERR_CONNECTION_TIMED_OUT") return;
    dispatch({
      type: GET_PARAM_ERROR,
      payload: null,
    });
  }
};
