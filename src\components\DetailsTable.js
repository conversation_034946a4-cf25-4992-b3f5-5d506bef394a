import React, { useState, useEffect, useCallback } from "react";
import {
  TrashIcon,
  CheckIcon,
  PencilIcon,
  XMarkIcon,
  FolderPlusIcon,
  PlusCircleIcon,
  PlusIcon,
} from "@heroicons/react/24/outline";
import { useSelector, useDispatch } from "react-redux";
import { Spin, Popconfirm } from "antd";

import {
  updateSpecificOptionFieldValue,
  createNewOption,
  reset,
  removeSpecificOption,
} from "../redux/fieldsSlice";
import TooltipPopover from "./Tooltip";
import "./style.css";
import AlertModal from "./AlertModal";
import { toastNotification } from "./ToastNotification";
import { Alert } from "./Alert";

const DetailsTable = ({
  setUpdateFieldsOptions,
  updateFieldProps,
  newFieldOptionsHandler,
  tableRows,
  setTableRows,
  getFieldsListOptions,
  newOptionArray,
  handleAddNewClick,
  handleRemoveNewClick,
  errors,
}) => {
  const [clickedToUpdate, setClickedToUpdate] = useState(null);
  const [optionError, setOptionError] = useState(null);
  const [edit, setEdit] = useState(false);
  const [deleteOptionId, setDeleteOptionId] = useState(null);
  const [openAlertDeleteModal, setOpenAlertDeleteModal] = useState(false);
  const [deleteLoader, setDeleteLoader] = useState(false);
  const [successAlert, setSuccessAlert] = useState(false);
  const [failAlert, setFailAlert] = useState(false);
  // const [tableRows, setTableRows] = useState(null);
  const [showAlert, setShowAlert] = useState(false);
  const [disableDeleteBtn, setDisableDeleteBtn] = useState(false);
  const [source, setSource] = useState("");

  const { isLoading, isSuccess, isOptDeleted, isError } = useSelector(
    (state) => state.fields
  );
  const dispatch = useDispatch();

  // const getFieldsListOptions = useCallback(async () => {
  //   try {
  //     const response = await MainService.getSpecificField(updateFieldProps?.id);
  //     console.log("getFieldsListOptions res", response);
  //     setTableRows(response?.data?.field_list_value);
  //   } catch (error) {
  //     console.log(`Error ${error}`);
  //   }
  // }, [updateFieldProps?.id]);

  const updateOptionValue = async (e) => {
    e && e.preventDefault();

    if (
      tableRows.find((element) => element?.listElementValue === "")
        ?.listElementValue === ""
    ) {
      setOptionError("This field can't be empty!");
    } else setOptionError("");

    // console.log("update");

    var formData = new FormData();
    formData.append(
      "listElementValue",
      tableRows &&
        tableRows.filter((element) => element?.id === clickedToUpdate)[0]
          ?.listElementValue
    );
    let payload = { clickedToUpdate, formData };
    // console.log(
    //   "data",
    //   payload,
    //   "errrr",
    //   tableRows.find((element) => element?.listElementValue === "")
    // );
    if (
      tableRows.find((element) => element?.listElementValue === "")
        ?.listElementValue !== ""
    ) {
      dispatch(updateSpecificOptionFieldValue(payload));
    }
  };

  const addNewOption = async (e) => {
    e && e.preventDefault();

    // console.log("new");
    // console.log(
    //   "errrr",
    //   optionError,
    //   "listElementValue",
    //   tableRows && tableRows.filter((element) => element?.id === "0")[0]?.listElementValue
    // );

    if (
      tableRows.find((element) => element?.listElementValue === "")
        ?.listElementValue === ""
    ) {
      setOptionError("This field can't be empty!");
    } else setOptionError("");
    // console.log(
    //   "errrrrrr",
    //   optionError
    // "find",
    // tableRows.find((element) => element?.listElementValue === "")?.listElementValue === ""
    // );
    var formData = new FormData();
    formData.append(
      "listElementValue",
      tableRows &&
        tableRows.filter((element) => element?.id === "0")[0]?.listElementValue
    );
    formData.append("field_id", updateFieldProps?.id);
    formData.append("hidden", 0);
    formData.append("deleted", 0);
    formData.append("defaultValue", 0);
    if (
      tableRows.find((element) => element?.listElementValue === "")
        ?.listElementValue !== ""
    ) {
      dispatch(createNewOption(formData));
    }
  };

  // useEffect(() => {
  //   if (isOptDeleted) {
  //     setTableRows(updateFieldsOptions);
  //     dispatch(reset());
  //   }
  // }, [isOptDeleted, tableRows, updateFieldsOptions, dispatch, source]);

  const handleAddClick = (e) => {
    e && e.preventDefault();
    setTableRows([
      {
        id: "0",
        listElementValue: "",
        hidden: "0",
        deleted: "0",
        defaultValue: "0",
      },
      ...tableRows,
    ]);
  };

  const handleRemoveClick = (index) => {
    let list = [...tableRows];
    let selectedItem = list.findIndex((element) => element?.id === "0");
    selectedItem !== -1 ? list.splice(index, 1) : setEdit(false);
    setTableRows(list);
  };

  const handleNewInputChange = (e, index) => {
    let newFormValues = [...tableRows];
    // let newFormValues = [...updateFieldsOptions];
    tableRows.forEach((item, i) => (newFormValues[i] = { ...item }));
    newFormValues[index][e.target.name] = e.target.value;
    setTableRows(newFormValues);
  };

  const disableRemoveIcon = (array) => {
    if (array && array.length <= 2) {
      return true;
    } else return false;
  };

  useEffect(() => {
    getFieldsListOptions();
  }, [getFieldsListOptions]);

  useEffect(() => {
    if (isOptDeleted === true) {
      getFieldsListOptions();
      toastNotification(
        "success",
        "Option value was deleted successfully",
        "bottomRight",
        "4.5"
      );
      setEdit(false);
      setSource("");
    }
  }, [isOptDeleted, getFieldsListOptions]);

  // useEffect(() => {
  //   if (isSuccess === true) {
  //     getFieldsListOptions();
  //     setEdit(false);
  //     setSource("");
  //   }
  // }, [isSuccess, getFieldsListOptions]);

  useEffect(() => {
    if (isSuccess === true) {
      let timer = setTimeout(() => {
        dispatch(reset());
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [dispatch, isSuccess]);

  // console.log("row", tableRows.filter((element) => element?.id === "0")[0]?.listElementValue);
  // console.log("newOptionArray", errors);

  return (
    <>
      <div className="px-4 sm:px-6 lg:px-8">
        <div className="mt-8 flex flex-col">
          <div className="-my-2 overflow-x-auto sm:-mx-14 lg:-mx-14">
            <div className="inline-block min-w-full py-2 align-middle md:px-5 lg:px-7">
              {/* Alert */}
              {isSuccess ? (
                <Alert
                  type="success"
                  alertMessage="Option values list Updated successfully"
                />
              ) : isError ? (
                <Alert type="error" alertMessage="Something went wrong!" />
              ) : // : isOptDeleted ? (
              //   <Alert type="success" alertMessage="Option deleted successfully" />
              // )
              null}
              {showAlert && tableRows && tableRows.length <= 2 ? (
                <div className="rounded-md bg-yellow-50 p-4 mb-5">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <XMarkIcon
                        className="h-5 w-5 text-gray-400 hover:text-gray-600 cursor-pointer"
                        aria-hidden="true"
                        onClick={() => setShowAlert(false)}
                      />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-yellow-800">
                        Attention needed
                      </h3>
                      <div className="mt-2 text-sm text-yellow-700">
                        <p>You can't have less than 2 options!</p>
                      </div>
                    </div>
                  </div>
                </div>
              ) : null}
              {/* End alert */}
              <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg mt-5">
                <table className="min-w-full divide-y divide-gray-300">
                  <thead className="bg-gray-50">
                    <tr>
                      {/* <th
                        scope="col"
                        className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"
                      >
                        ID
                      </th> */}
                      <th
                        scope="col"
                        className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"
                      >
                        Displayed Option Value
                      </th>
                      <th
                        scope="col"
                        className="relative py-3.5 pl-3 pr-4 sm:pr-6 float-right"
                      >
                        {/* <span className="sr-only">Actions</span> */}
                        <button
                          type="button"
                          onClick={() => {
                            Object.keys(updateFieldProps).length === 0
                              ? handleAddNewClick()
                              : handleAddClick();
                            setSource("newOption");
                          }}
                          className="inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-2.5 py-1.5 text-sm leading-4 text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                        >
                          <PlusIcon
                            className="-ml-0.5 mr-2 h-4 w-4"
                            aria-hidden="true"
                          />
                          Add New Option
                        </button>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 bg-white">
                    {isLoading ? (
                      <Spin />
                    ) : tableRows ? (
                      tableRows.map((listItem, i) => (
                        <tr key={i}>
                          <td className="whitespace-nowrap pl-4 text-sm">
                            <div className="flex items-center">
                              {(edit && clickedToUpdate === listItem?.id) ||
                              listItem?.id === "0" ? (
                                <div className="flex flex-col">
                                  <input
                                    name="listElementValue"
                                    type="text"
                                    id={`option${i}`}
                                    className="block w-auto rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                    value={listItem?.listElementValue}
                                    // onChange={(e) => setListItemField(e.target.value)}
                                    onChange={(e) => handleNewInputChange(e, i)}
                                  />
                                  {optionError !== null ? (
                                    <p className="text-red-500 transition-all duration-300 ease-in-out relative mb-0">
                                      {optionError}
                                    </p>
                                  ) : null}
                                </div>
                              ) : (
                                <div className="font-medium text-gray-900">
                                  {listItem.listElementValue}
                                </div>
                              )}
                            </div>
                          </td>
                          <td className="relative flex float-right whitespace-nowrap py-4 text-center text-sm font-medium sm:pr-6">
                            {(edit && clickedToUpdate === listItem?.id) ||
                            listItem?.id === "0" ? (
                              <>
                                <p className="text-indigo-600 hover:text-indigo-900 cursor-pointer flex">
                                  <TooltipPopover
                                    text="Confirm"
                                    position="topRight"
                                  >
                                    <CheckIcon
                                      className="h-5 w-5 text-green-300 hover:text-green-500"
                                      onClick={() => {
                                        source === "newOption"
                                          ? addNewOption()
                                          : updateOptionValue();
                                      }}
                                    />
                                  </TooltipPopover>
                                </p>
                                <p className="text-indigo-600 hover:text-indigo-900 cursor-pointer flex">
                                  <TooltipPopover
                                    text="Cancel"
                                    position="bottom"
                                  >
                                    <XMarkIcon
                                      className="h-5 w-5 text-gray-300 hover:text-gray-500"
                                      onClick={() => {
                                        setEdit(false);
                                        setClickedToUpdate("");
                                        setOptionError(null);
                                        handleRemoveClick(i);
                                        setSource("");
                                      }}
                                    />
                                  </TooltipPopover>
                                </p>
                              </>
                            ) : (
                              <>
                                <p
                                  onClick={() => {
                                    setEdit(true);
                                    setClickedToUpdate(listItem?.id);
                                  }}
                                  className="text-indigo-600 hover:text-indigo-900 cursor-pointer"
                                >
                                  <TooltipPopover
                                    text="Click here to update option"
                                    position="topRight"
                                  >
                                    <PencilIcon className="h-5 w-5 text-yellow-300 hover:text-yellow-500" />
                                  </TooltipPopover>
                                </p>
                                <p className="ml-2 text-indigo-600 hover:text-indigo-900 cursor-pointer">
                                  {/* <TooltipPopover text="Remove" position="topLeft"> */}
                                  <Popconfirm
                                    title="Are you sure you want to perform this action?"
                                    okText="Confirm"
                                    cancelText="Cancel"
                                    arrowPointAtCenter={true}
                                    autoAdjustOverflow={false}
                                    onConfirm={() =>
                                      dispatch(
                                        removeSpecificOption(deleteOptionId)
                                      )
                                    }
                                  >
                                    <TrashIcon
                                      className="h-5 w-5 text-red-300 hover:text-red-500"
                                      onClick={() => {
                                        setDeleteOptionId(listItem?.id);
                                        // disableRemoveIcon(tableRows)
                                        //   ? setShowAlert(true)
                                        //   : setOpenAlertDeleteModal(true);
                                        // console.log("wtf");
                                      }}
                                    />
                                  </Popconfirm>
                                  {/* </TooltipPopover> */}
                                </p>
                              </>
                            )}
                          </td>
                        </tr>
                      ))
                    ) : // IN CASE THE USER ADDS A NEW MULTI_OPTIONS FIELD.
                    Object.keys(updateFieldProps).length === 0 ? (
                      newOptionArray.map((listItem, i) => (
                        <tr key={i}>
                          <td className="whitespace-nowrap pl-4 text-sm">
                            <div className="flex items-center">
                              <div className="flex flex-col">
                                <input
                                  name="listElementValue"
                                  type="text"
                                  id={`option${i}`}
                                  className="block w-auto rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                  // value={listItem?.listElementValue}
                                  onChange={(e) => newFieldOptionsHandler(e, i)}
                                />
                                {errors?.fieldOpts ? (
                                  <p className="text-red-500 transition-all duration-300 ease-in-out relative mb-0">
                                    {errors?.fieldOpts}
                                  </p>
                                ) : null}
                              </div>
                            </div>
                          </td>
                          <td className="relative flex float-right whitespace-nowrap py-4 text-center text-sm font-medium sm:pr-6">
                            <p className="text-indigo-600 hover:text-indigo-900 cursor-pointer flex">
                              <TooltipPopover text="Cancel" position="bottom">
                                <XMarkIcon
                                  className="h-5 w-5 text-gray-300 hover:text-gray-500"
                                  onClick={() => {
                                    setOptionError(null);
                                    newOptionArray.length > 2 &&
                                      handleRemoveNewClick(i);
                                    setSource("");
                                  }}
                                />
                              </TooltipPopover>
                            </p>
                          </td>
                        </tr>
                      ))
                    ) : (
                      tableRows == null && (
                        <tr>
                          <td className="whitespace-nowrap py-2 pl-4 text-sm">
                            <div className="flex items-center">
                              <div className="">
                                <div className="font-medium text-gray-900">
                                  No Options
                                </div>
                              </div>
                            </div>
                          </td>
                        </tr>
                      )
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
      <AlertModal
        openDeleteModal={openAlertDeleteModal}
        setOpenDeleteModal={setOpenAlertDeleteModal}
        loading={deleteLoader}
        failAlert={failAlert}
        successAlert={successAlert}
        deleteOptionId={deleteOptionId}
        disableDeleteBtn={disableDeleteBtn}
        setDisableDeleteBtn={setDisableDeleteBtn}
      />
    </>
  );
};

export default DetailsTable;
