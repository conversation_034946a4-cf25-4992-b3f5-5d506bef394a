import { Input, message, Upload, Form, Avatar, Tooltip, Switch } from "antd";
import React, { Suspense, lazy, useEffect, useMemo, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { lazyRetry } from "utils/lazyRetry";
import { useAnimation, motion } from "framer-motion";
import { useSelector, useDispatch } from "react-redux";
import { CloseCircleFilled, PlusOutlined } from "@ant-design/icons";
import {
  accepetedExtentionImage,
  getName,
  getOrGenerateTabId,
  normFile,
} from "../../utils/ConversationUtils";
import { AvatarChat, Loader } from "components/Chat";
import { FiSearch } from "react-icons/fi";
import {
  AddGroupToMembersGroupChat,
  resetStateOtherUser,
  setChatSelectedConversation,
  setChatSelectedParticipants,
} from "new-redux/actions/chat.actions";
import { updateParticipantsList } from "../../utils/infoRoom";
import { toastNotification } from "components/ToastNotification";
import ModalConfirm from "components/Chat/Modal/ModalConfirm";
import MainService from "services/main.service";
import { URL_ENV } from "index";
const MembersAdd = lazy(() => lazyRetry(() => import("./members_add")));
const { TextArea } = Input;

function AddDiscussion({ handleCancel, open }) {
  const { t } = useTranslation("common");
  const userList = useSelector((state) => state.chat.userList);
  const currentUser = useSelector((state) => state.chat.currentUser);

  const [listAllUsers, setAllListUsers] = useState([]);
  const [noImage, setNoImageState] = useState(true);
  const [createGroup, setCreateGroup] = useState({
    checked: false,
    disabled: false,
  });
  const [search, setSearch] = useState("");
  const [loading, setLoading] = useState(false);

  const [form] = Form.useForm();
  const controls = useAnimation();
  const controls2 = useAnimation();
  const dispatch = useDispatch();

  const handleOk = () => {
    form.submit();
  };
  const inputRef = useRef(null);

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);
  const onFinish = async (values) => {
    const firstActions = [];
    const lastActions = [];
    let time;
    try {
      //
      if (!createGroup.checked) {
        const user = values.users_ids[0];
        if (user) {
          firstActions.push(
            resetStateOtherUser({
              forced: true,
              keepDrawerOpened: false,
              item: {
                _id: user?._id,
                type: "user",
              },
            })
          );
          firstActions.push(
            setChatSelectedParticipants({
              selectedParticipants: [
                {
                  email: user?.email,
                  name: user?.name,
                  description: null,
                  image: user?.image,
                  uuid: user?.uuid,

                  admin_id: null,
                  bot: null,
                  _id: user?._id,
                  post_number: user?.post_number,
                  type: "user",
                },
                currentUser,
              ],
            })
          );
          clearTimeout(time);
          time = setTimeout(() => {
            handleCancel();
          }, 2);
        }
      } else {
        setLoading(true);
        let formData = new FormData();
        values.users_ids = values.users_ids.map((el) => el._id).join(",");

        for (const key in values) {
          formData.append(
            key,
            Array.isArray(values[key])
              ? values[key][0]?.originFileObj
              : values[key]
          );
          if (
            (Array.isArray(values[key]) && values[key].length === 0) ||
            values[key] === "" ||
            values[key] === undefined
          ) {
            formData.delete(key);
          }
        }

        formData.append("tab_id", getOrGenerateTabId());
        const res = await MainService.createRoom(formData);

        if (res.status === 200) {
          setLoading(false);
          firstActions.push (resetStateOtherUser({
            forced: true,
            keepDrawerOpened: false,
            item:null,
          }))
          firstActions.push(
            AddGroupToMembersGroupChat({
              ...res.data.room,
              conversation_id: res.data.conversation_id,
            })
          );
          updateParticipantsList(
            res.data.room?.participants,
            res.data.room?._id,
            "room"
          );
          firstActions.push(
            setChatSelectedParticipants({
              selectedParticipants: res.data.room.participants,
            })
          );
          
          firstActions.push(
            setChatSelectedConversation({
              selectedConversation: {
                name: res.data.room.name,
                description: res.data.room.description,
                image: res.data.room.image,
                admin_id: res.data.room.admin_id,
                bot: res.data.room.bot,
                id: res.data.room?._id,
                predefined: res.data.room.predefined,
                type: "room",
              source:"chat",

                created_at: res.data.room.created_at,
                conversationId: res.data.conversation_id,
              },
            })
          );
          handleCancel();
        }
      }
    } catch (err) {
      console.log(err);
      setLoading(false);
      toastNotification("error", t("toasts.errorFetchApi"), "topRight");
    } finally {
      let time;
      clearTimeout(time);

      time = setTimeout(async () => {
        await Promise.all(firstActions.map((action) => dispatch(action)));
        await Promise.all(lastActions.map((action) => dispatch(action)));
        clearTimeout(time);
      }, 1);
    }
  };

  useEffect(() => {
    setSearch("");
    setCreateGroup({ checked: false, disabled: false });
    setLoading(false);
    if (open) {
      form?.resetFields();
      setNoImageState(true);
      setAllListUsers(userList.map((el) => ({ ...el, selected: false })));
    }
  }, [open]);
  const filterList = useMemo(
    () => (open ? listAllUsers.filter((el) => el.selected) ?? [] : []),
    [listAllUsers, open]
  );
  useEffect(() => {
    if (!open) return;
    form.setFieldsValue({
      users_ids: filterList,
    });
    if (filterList.length > 1) {
      controls.start({ height: "auto", opacity: 1 });
      controls2.start({ height: "auto", opacity: 1 });

      setCreateGroup({ checked: true, disabled: true });
    } else if (filterList.length === 1 && !createGroup.checked) {
      controls.start({ height: 0, opacity: 0 });
      controls2.start({ height: "auto", opacity: 1 });
      setCreateGroup({ checked: false, disabled: false });
    } else if (filterList.length <= 1 && createGroup.checked) {
      controls.start({ height: "auto", opacity: 1 });
      setCreateGroup({ checked: true, disabled: false });

      controls2.start({
        height: filterList.length === 1 ? "auto" : 0,
        opacity: filterList.length === 1 ? 1 : 0,
      });
    } else {
      setCreateGroup({ checked: false, disabled: false });

      form.resetFields();
      controls.start({ height: 0, opacity: 0 });
      controls2.start({ height: 0, opacity: 0 });
    }
  }, [filterList, createGroup.checked]);

  const deselectUser = (user) => {
    let array = [...listAllUsers];
    array = array.map((el) => {
      if (el._id === user._id) el.selected = false;
      return el;
    });
    setAllListUsers([...array]);
  };
  const handlePreview = (file) => {
    const reader = new FileReader();
    reader.readAsDataURL(file.originFileObj);

    reader.onloadend = () => {
      const base64String = reader.result;
      // Ouvrir l'image dans une nouvelle fenêtre
      const imageData = atob(base64String.split(",")[1]);
      const arrayBuffer = new ArrayBuffer(imageData.length);
      const uintArray = new Uint8Array(arrayBuffer);

      for (let i = 0; i < imageData.length; i++) {
        uintArray[i] = imageData.charCodeAt(i);
      }

      const blob = new Blob([arrayBuffer], { type: "image/png" });
      const imageUrl = URL.createObjectURL(blob);

      window.open(imageUrl);
    };
  };
  const props = {
    beforeUpload: (file) => {
      const isImage = file.type.split("/")[0] === "image";
      if (!isImage) {
        message.error(`${file.name} ${t("import.isNotImage")} !`);
        return Upload.LIST_IGNORE;
      }
      if (file.size > 150000) {
        message.error(
          `${file.name} ${t("chat.errSizeImage", { size: "150 KB" })}`
        );
        return Upload.LIST_IGNORE;
      }
      const extentionImage = file.type?.split("/").pop().toUpperCase();
      const acceptedType = accepetedExtentionImage.includes(extentionImage);
      if (!acceptedType) {
        message.error(
          t("chat.file.error_extention", {
            extention: accepetedExtentionImage.join(", "),
          })
        );
        return Upload.LIST_IGNORE;
      }

      return false;
    },

    multiple: false,
    accept: "image/*",
    onChange: (info) => {
      setNoImageState(info.file.status === "removed");
    },
    onPreview: handlePreview,
    name: "avatar",
    listType: "picture-circle",
    className: "avatar-uploader",
  };
  const onClose = () => {
    form?.resetFields();

    handleCancel();
  };

  return (
    <ModalConfirm
      title={t("chat.addColleague")}
      wrapClassName="modal-addGroup"
      open={open}
      onCancel={onClose}
      onOk={handleOk}
      loading={loading}
      cancelText={t("form.cancel")}
      okText={t("chat.startconversation")}
      width={700}
      content={
        <Form
          name="add-colleague"
          layout="vertical"
          onFinish={onFinish}
          form={form}>
          <div className="flex items-start justify-between space-x-6 pr-5">
            <div className="flex-1">
              <Form.Item>
                {filterList?.length > 0 && (
                  <div className="mb-2 overflow-hidden">
                    <Avatar.Group className="w-[650px] space-x-1 overflow-auto py-5">
                      {filterList?.map((el) => (
                        <Tooltip placement="top" title={el.name} key={el._id}>
                          <div className="relative">
                            <CloseCircleFilled
                              style={{
                                position: "absolute",
                                right: 0,
                                top: "-10px",
                                cursor: "pointer",
                                zIndex: 20,
                                color: "red",
                                fontSize: "15px",
                              }}
                              onClick={() => deselectUser(el)}
                            />
                            <AvatarChat
                              url={
                                URL_ENV?.REACT_APP_BASE_URL +
                                URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                                el.image
                              }
                              type="user"
                              size={38}
                              height={10}
                              width={10}
                              name={getName(el.name, "avatar")}
                              hasImage={el.image}
                            />
                          </div>
                        </Tooltip>
                      ))}
                    </Avatar.Group>
                  </div>
                )}

                <div className="w-full">
                  <Form.Item>
                    <Input
                      size="middle"
                      placeholder={t("chat.searchSide.searchMembersToAdd")}
                      prefix={<FiSearch className="text-slate-500" />}
                      value={search}
                      onChange={(e) =>
                        setSearch(
                          e.target.value.trimStart().replace(/\s{1,} /g, " ")
                        )
                      }
                      className="w-full flex-1"
                      ref={inputRef}
                      allowClear
                    />
                  </Form.Item>
                </div>

                <Form.Item
                  name="users_ids"
                  rules={[
                    {
                      required: true,
                      message: t("chat.error_message.chooseUsers"),
                    },
                  ]}>
                  <Suspense
                    fallback={
                      <div className="flex h-full w-full items-center justify-center">
                        <Loader size={24} />
                      </div>
                    }>
                    <MembersAdd
                      listAllUsers={listAllUsers}
                      setAllListUsers={setAllListUsers}
                      search={search}
                    />
                  </Suspense>
                </Form.Item>
              </Form.Item>

              <motion.div transition={{ duration: 0.5 }}>
                <Form.Item>
                  <Switch
                    size="small"
                    checked={createGroup.checked}
                    onChange={(checked) =>
                      setCreateGroup((prev) => ({
                        ...prev,
                        checked: checked,
                      }))
                    }
                    disabled={createGroup.disabled}
                  />{" "}
                  {t("chat.addGroup")}{" "}
                </Form.Item>
              </motion.div>
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={controls}
                style={{ overflow: "hidden" }}
                transition={{ duration: 0.5 }}
                className="flex items-center space-x-4 py-1">
                <div className="flex-1">
                  <Form.Item
                    label={t("table.header.name")}
                    name="name"
                    rules={[
                      {
                        required: createGroup.checked ? true : false,
                        message: `${t("table.header.name")}  ${t(
                          "table.header.isrequired"
                        )}`,
                      },
                      {
                        min: 3,
                        message: t("chat.error_message.min3chart"),
                      },
                    ]}>
                    <Input
                      placeholder={t("table.header.name")}
                      maxLength={50}
                      showCount
                    />
                  </Form.Item>
                  <Form.Item name="description" label="Description">
                    <TextArea
                      rows={3}
                      showCount
                      maxLength={200}
                      placeholder="Description"
                    />
                  </Form.Item>
                </div>
                <Form.Item
                  valuePropName="fileList"
                  getValueFromEvent={normFile}
                  name="image">
                  <Upload {...props}>
                    {noImage && (
                      <div className="flex flex-col">
                        <div className=" text-sm">
                          <PlusOutlined />
                          <p>{t("table.header.uploadImage")}</p>
                        </div>
                      </div>
                    )}
                  </Upload>
                </Form.Item>
              </motion.div>
            </div>
          </div>
        </Form>
      }
    />
  );
}

export default AddDiscussion;
