import { Card, Checkbox } from "antd";
import React, { useState } from "react";
import { useEffect } from "react";

const CardFilter = ({
  filter,
  setFilter,
  setCheckedListFilter,
  checkedListFilter,
  checkedListOk,
  openFilterTable,
}) => {
  const [checkedList, setCheckedList] = useState([]);
  const checkAll = filter.data.length === checkedList.length;
  const indeterminate =
    checkedList.length > 0 && checkedList.length < filter.data.length;

  const onChange = (checkedValues) => {
    setCheckedList(checkedValues);

    setCheckedListFilter((prevData) => {
      let newData = [...prevData]; // Créer une nouvelle copie de la liste existante

      if (newData.length > 0) {
        const name = filter?.name;
        let index = newData.findIndex((objet) => name in objet);

        if (index !== -1) {
          newData[index][name] = checkedValues; // Mettre à jour l'attribut existant
        } else {
          newData.push({ [name]: checkedValues }); // Ajouter un nouvel objet avec l'attribut et les valeurs
        }
      } else {
        newData.push({ [filter.name]: checkedValues }); // Ajouter le premier objet à la liste
      }

      return newData;
    });

    // setFilter((prev) => ({
    //   selected: [...new Set([...prev.selected, ...checkedValues])],
    // }));
  };
  const onCheckAllChange = (e) => {
    setCheckedList(e.target.checked ? filter.data.map((el) => el.value) : []);
    const selected = e.target.checked ? filter.data.map((el) => el.value) : [];

    setCheckedListFilter((prevData) => {
      let newData = [...prevData]; // Créer une nouvelle copie de la liste existante

      if (newData.length > 0) {
        const name = filter?.name;
        let index = newData.findIndex((objet) => name in objet);

        if (index !== -1) {
          newData[index][name] = e.target.checked
            ? filter.data.map((el) => el.value)
            : []; // Mettre à jour l'attribut existant
        } else {
          newData.push({
            [name]: e.target.checked ? filter.data.map((el) => el.value) : [],
          }); // Ajouter un nouvel objet avec l'attribut et les valeurs
        }
      } else {
        newData.push({
          [filter.name]: e.target.checked
            ? filter.data.map((el) => el.value)
            : [],
        }); // Ajouter le premier objet à la liste
      }

      return newData;
    });
    // setCheckedListFilter((prev) =>
    //   e.target.checked
    //     ? [...prev, ...filter.data.map((el) => el.value)]
    //     : [...prev]
    // );

    // setFilter((prev) => ({
    //   selected: [...new Set([...prev.selected, ...selected])],
    // }));
  };

  return (
    <Card
      type="inner"
      title={
        <div className="space-x-1">
          <Checkbox
            indeterminate={indeterminate}
            onChange={onCheckAllChange}
            checked={checkAll}
          />{" "}
          <span>{filter.name}</span>
        </div>
      }
      bordered={true}
      headStyle={{ padding: "0 10px" }}
    >
      <Checkbox.Group
        options={filter.data.map((el) => ({
          label: el.text,
          value: el.value,
        }))}
        onChange={onChange}
        value={checkedList}
        className="flex flex-col space-y-2"
      />
    </Card>
  );
};

export default CardFilter;
