import { ArrowRightOutlined } from "@ant-design/icons";
import {
  PhoneArrowDownLeftIcon,
  PhoneArrowUpRightIcon,
} from "@heroicons/react/24/outline";
import { Card, Col, Row, Statistic, Typography } from "antd";
import React from "react";
import CountUp from "react-countup";
import { useTranslation } from "react-i18next";
import { FcCallback } from "react-icons/fc";
import { MdPhoneMissed } from "react-icons/md";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

const CardVoip = () => {
  const navigate = useNavigate();
  const {
    missedUnreturnedCalls,
    missedTodayCall,
    outgoingTodayCall,
    receivedTodayCall,
  } = useSelector((state) => state.dashboardRealTime);
  const formatter = (value) => <CountUp end={value} separator="," />;

  const [t] = useTranslation("common");

  return (
    <Card
      title={t("menu1.voip")}
      size=""
      className="h-full bg-white shadow-sm"
      extra={
        // outgoingTodayCall + missedTodayCall + receivedTodayCall >
        // 0 ? (
        <Typography.Link onClick={() => navigate("/logs")}>
          <ArrowRightOutlined />
        </Typography.Link>
        // ) : null
      }
    >
      <Row gutter={24} className="">
        <Col span={6}>
          <Statistic
            formatter={typeof receivedTodayCall === "number" ? formatter : null}
            title={t(`dashboard.receivedCalls`, {
              plural: receivedTodayCall > 1 ? "s" : "",
              pluriel: receivedTodayCall > 1 ? "x" : "",
            })}
            value={receivedTodayCall}
            valueStyle={{
              color: "#3f8600",
            }}
            prefix={<PhoneArrowDownLeftIcon className="h-5 w-5" />}
          />
        </Col>
        <Col span={6}>
          <Statistic
            formatter={typeof missedTodayCall === "number" ? formatter : null}
            title={t(`dashboard.missedCalls`, {
              plural: missedTodayCall > 1 ? "s" : "",
              pluriel: missedTodayCall > 1 ? "x" : "",
            })}
            value={missedTodayCall}
            valueStyle={{
              color: "#cf1322",
            }}
            prefix={
              <MdPhoneMissed className="h-[22px] w-[22px] text-xl text-[#EF4444] " />
            }
          />
        </Col>
        <Col span={6}>
          <Statistic
            formatter={typeof outgoingTodayCall === "number" ? formatter : null}
            title={t(`dashboard.outgoingCalls`, {
              plural: outgoingTodayCall > 1 ? "s" : "",
              pluriel: outgoingTodayCall > 1 ? "x" : "",
            })}
            value={outgoingTodayCall}
            valueStyle={{
              color: "#1677ff",
            }}
            prefix={<PhoneArrowUpRightIcon className="h-5 w-5" />}
          />
        </Col>

        <Col span={6}>
          <Statistic
            formatter={
              typeof missedUnreturnedCalls === "number" ? formatter : null
            }
            title={t(`dashboard.unreturnedMissedCalls`, {
              plural: missedUnreturnedCalls > 1 ? "s" : "",
              pluriel: missedUnreturnedCalls > 1 ? "x" : "",
            })}
            value={missedUnreturnedCalls}
            valueStyle={{
              color: "black",
            }}
            prefix={<FcCallback className="h-[22px] w-[22px]" />}
          />
        </Col>
      </Row>
    </Card>
  );
};

export default CardVoip;
