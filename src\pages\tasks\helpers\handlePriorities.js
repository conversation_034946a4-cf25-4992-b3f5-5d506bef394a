import { Badge, Tag } from "antd";
import i18next from "i18next";

// Styles of priority item in select.
const prioritySelectStyles = {
  display: "flex",
  flexDirection: "row",
  alignItems: "center",
};

// List of priorities
export const prioritiesList = () => {
  return [
    {
      key: "low",
      value: "low",
      label: (
        <Badge
          style={prioritySelectStyles}
          color="#bfbfbf"
          text={
            <div className="text-[#bfbfbf]">
              {i18next.t("common:tasks.lowPriority")}
            </div>
          }
        />
      ),
      title: i18next.t("common:tasks.lowPriority"),
    },
    {
      key: "medium",
      value: "medium",
      label: (
        <Badge
          style={prioritySelectStyles}
          color="#69b1ff"
          text={
            <div className="text-[#69b1ff]">
              {i18next.t("common:tasks.mediumPriority")}
            </div>
          }
        />
      ),
      title: i18next.t("common:tasks.mediumPriority"),
    },
    {
      key: "high",
      value: "high",
      label: (
        <Badge
          style={prioritySelectStyles}
          color="#ffc53d"
          text={
            <div className="text-[#ffc53d]">
              {i18next.t("common:tasks.highPriority")}
            </div>
          }
        />
      ),
      title: i18next.t("common:tasks.highPriority"),
    },
    {
      key: "urgent",
      value: "urgent",
      label: (
        <Badge
          style={prioritySelectStyles}
          color="#f5222d"
          text={
            <div className="text-[#f5222d]">
              {i18next.t("common:tasks.urgentPriority")}
            </div>
          }
        />
      ),
      title: i18next.t("common:tasks.urgentPriority"),
    },
  ];
};

// List of priorities in select
export const prioritiesListInFilter = () => {
  return [
    {
      key: "low",
      value: "low",
      label: <Tag color="#bfbfbf">{i18next.t("common:tasks.lowPriority")}</Tag>,
    },
    {
      key: "medium",
      value: "medium",
      label: (
        <Tag color="#69b1ff">{i18next.t("common:tasks.mediumPriority")}</Tag>
      ),
    },
    {
      key: "high",
      value: "high",
      label: (
        <Tag color="#ffc53d">{i18next.t("common:tasks.highPriority")}</Tag>
      ),
    },
    {
      key: "urgent",
      value: "urgent",
      label: (
        <Tag color="#f5222d">{i18next.t("common:tasks.urgentPriority")}</Tag>
      ),
    },
  ];
};

// Format and translate priority on hover
export const handlePriorityLabelOnHover = (label) => {
  if (label === "low") {
    return i18next.t("common:tasks.lowPriority");
  } else if (label === "medium") {
    return i18next.t("common:tasks.mediumPriority");
  } else if (label === "high") {
    return i18next.t("common:tasks.highPriority");
  } else if (label === "urgent") {
    return i18next.t("common:tasks.urgentPriority");
  }
};

// Display color based on priority.
export const displayPriorityColor = (state) => {
  if (state === "low") {
    return "#bfbfbf";
  } else if (state === "medium") {
    return "#69b1ff";
  } else if (state === "high") {
    return "#ffc53d";
  } else if (state === "urgent") {
    return "#f5222d";
  } else return null;
};
