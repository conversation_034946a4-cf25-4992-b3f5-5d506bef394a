import { useEffect, useMemo, useRef, useState } from "react";
import { AutoComplete, Avatar, Button, Form, Select, Space } from "antd";
import { HiOutlineBuildingOffice, HiOutlineUserGroup } from "react-icons/hi2";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-icons/cg";
import { CloseCircleFilled, LoadingOutlined } from "@ant-design/icons";
import { AiOutlineShoppingCart } from "react-icons/ai";
import { LuPalmtree } from "react-icons/lu";

import {
  affectations,
  postAffectations,
} from "../../../pages/voip/services/services";
import { useSelector } from "react-redux";
import { toastNotification } from "components/ToastNotification";
import { FiUsers } from "react-icons/fi";
import { setRefreshMailInbox } from "new-redux/actions/mail.actions";
import { useDispatch } from "react-redux";
import { URL_ENV } from "index";
import { Blocks, HeartHandshake } from "lucide-react";
import TicketIconSphere from "components/icons/TicketIconSphere";

const AffectationContent = ({
  source,
  elementId,
  setData,
  t,
  access,
  user,
  setOpenPopCon,
  type,
  setDetailsMail,
  setIdModule,
  affectation,
}) => {
  //
  const [form] = Form.useForm();
  const autoCompleteRef = useRef(null);

  const [selectedFamily, setSelectedFamily] = useState(
    isNaN(Number(affectation.affected_family_id))
      ? null
      : Number(affectation.affected_family_id)
  );
  const [autoCompleteOptions, setAutoCompleteOptions] = useState([]);
  const [loadingOptions, setLoadingOptions] = useState(false);
  // const [autoCompleteLabel, setAutoCompleteLabel] = useState("");
  const [autoCompleteValue, setAutoCompleteValue] = useState(null);
  const [saveIsDisabled, setSaveIsDisabled] = useState(true);
  const [saveIsLoading, setSaveIsLoading] = useState(false);

  const { dataAccounts } = useSelector((state) => state.mailReducer);
  const dispatch = useDispatch();
  const usedAccount = useMemo(
    () => dataAccounts?.find((item) => item.selected === true),
    [dataAccounts]
  );

  useEffect(() => {
    if (!selectedFamily) {
      setAutoCompleteValue(null);
      setAutoCompleteOptions([]);
      setSaveIsDisabled(true);
    }
  }, [selectedFamily]);
  //
  const familiesOption = [
    ...(access?.companies === "1"
      ? [
          {
            label: (
              <Space>
                <HiOutlineBuildingOffice
                  style={{ fontSize: "16px", marginTop: "5px" }}
                />
                <p>{t("menu1.companies")}</p>
              </Space>
            ),
            value: 1,
          },
        ]
      : []),
    ...(access?.contact === "1"
      ? [
          {
            label: (
              <Space>
                <HiOutlineUserGroup
                  style={{ fontSize: "16px", marginTop: "5px" }}
                />
                <p>{t("menu1.contacts")}</p>
              </Space>
            ),
            value: 2,
          },
        ]
      : []),
    ...(access?.leads === "1"
      ? [
          {
            label: (
              <Space>
                <CgUserlane style={{ fontSize: "16px", marginTop: "5px" }} />
                <p>{t("menu1.leads")}</p>
              </Space>
            ),
            value: 9,
          },
        ]
      : []),
    ...(access?.deals === "1"
      ? [
          {
            label: (
              <Space>
                <HeartHandshake size={18} style={{ marginTop: 7 }} />
                <p>{t("menu1.deals")}</p>
              </Space>
            ),
            value: 3,
          },
        ]
      : []),
    ...(access?.colleague === "1"
      ? [
          {
            label: (
              <Space>
                <FiUsers style={{ fontSize: "17px", marginTop: "5px" }} />
                <p>{t("contacts.collegues")}</p>
              </Space>
            ),
            value: 4,
          },
        ]
      : []),
    ...(access?.ticket === "1"
      ? [
          {
            label: (
              <Space>
                <TicketIconSphere size={19} style={{ marginTop: 5 }} />
                <p>{t("menu1.tickets")}</p>
              </Space>
            ),
            value: 6,
          },
        ]
      : []),
    ...(access?.projects === "1"
      ? [
          {
            label: (
              <Space>
                <Blocks size={17} style={{ marginTop: 7 }} />
                <p>{t("menu1.projects")}</p>
              </Space>
            ),
            value: 7,
          },
        ]
      : []),
    ...(access?.products === "1"
      ? [
          {
            label: (
              <Space>
                <AiOutlineShoppingCart
                  style={{ fontSize: "16px", marginTop: "5px" }}
                />
                <p>{t("menu1.products")}</p>
              </Space>
            ),
            value: 5,
          },
        ]
      : []),
    ...(access?.booking === "1"
      ? [
          {
            label: (
              <Space>
                <LuPalmtree style={{ fontSize: "16px", marginTop: "5px" }} />
                <p>{t("menu1.booking")}</p>
              </Space>
            ),
            value: 8,
          },
        ]
      : []),
  ];
  //
  const families = {
    1: t("contacts.company"),
    2: t("contacts.contact"),
    3: "Deals",
    4: t("contacts.collegues"),
    5: t("contacts.product"),
    6: t("contacts.ticket"),
    7: t("contacts.project"),
    8: t("contacts.booking"),
    9: t("contacts.leads"),
  };
  //
  const fetchOptions = async (searchText, familyID) => {
    // if (searchText?.length < 0) return;
    try {
      setLoadingOptions(true);
      const {
        data: { data },
      } = await affectations(
        familyID || selectedFamily,
        user?.id,
        searchText ?? ""
      );
      const fetchedOptions = data?.map((element) => ({
        // label: `${e.label}${e.reference ? ` - ${e.reference}` : ""}`,
        // value: `${e.label}${e.reference ? ` - ${e.reference}` : ""}`,
        // key: e._id,

        label: (
          <div className="flex">
            {element?.avatar?.length > 0 ? (
              <Avatar
                style={{
                  // backgroundColor: "#c41d7f",
                  marginRight: "10px",
                  marginBottom: "4px",
                }}
                size={22}
                src={
                  <img
                    src={`${
                      URL_ENV?.REACT_APP_BASE_URL +
                      URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                    }${element?.avatar}`}
                    alt="name"
                  />
                }
              />
            ) : (
              <Avatar
                style={{
                  backgroundColor: "#c41d7f",
                  marginRight: "10px",
                  marginBottom: "4px",
                }}
                size={22}
              >
                {element?.label?.charAt(0)?.toUpperCase()}
              </Avatar>
            )}
            <div className="w-[75%] ">
              {element.label}
              {element.reference ? ` - ${element.reference}` : ""}
            </div>
          </div>
        ),
        value: `${element.label}${
          element.reference ? ` - ${element.reference}` : ""
        }`,
        key: element._id,
        searchOption: element?.label,
      }));
      const defaultSelected = fetchedOptions?.find(
        (item) => item.key === affectation.affect_to
      );
      setAutoCompleteOptions(fetchedOptions);
      setAutoCompleteValue(defaultSelected);
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? err.message : err);
    } finally {
      setLoadingOptions(false);
    }
  };

  const onChange = (text) => {
    if (text?.length < autoCompleteValue?.length) setSaveIsDisabled(true);
    // setAutoCompleteLabel(text);
  };
  //
  const onSelect = (data, option) => {
    // setAutoCompleteLabel(option.label);
    setAutoCompleteValue(option);
    setSaveIsDisabled(false);
  };
  //
  const saveAffectation = async () => {
    try {
      setSaveIsLoading(true);
      const formData = new FormData();
      formData.append("element_id", elementId);
      formData.append("affect_to", autoCompleteValue.key);
      formData.append("affected_family_id", selectedFamily);
      formData.append("type", source);
      formData.append("account_id", usedAccount?.value);
      for (let i = 0; i < usedAccount?.departmentId?.length; i++) {
        formData.append("departement_id[]", usedAccount?.departmentId[i]);
      }
      const response = await postAffectations(formData);
      if (response?.status === 200) {
        if (type === "inbox" || type === "dropdown") {
          dispatch(setRefreshMailInbox(true));
          setData &&
            setData((prev) =>
              prev?.map((item) =>
                item?._id === elementId || item?.id === elementId
                  ? { ...item, affectation: response.data.data }
                  : item
              )
            );
        } else {
          setDetailsMail((p) => {
            let detail = Object.assign({}, p);
            detail.data[detail?.data?.length - 1].affectation =
              response.data.data;
            return detail;
          });
        }
        toastNotification(
          "success",
          t("mailing.affectedSuccess"),
          "topRight",
          3
        );
        setOpenPopCon(false);
      }
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? err.message : err);
    } finally {
      setSaveIsLoading(false);
    }
  };

  useEffect(() => {
    if (selectedFamily) {
      fetchOptions();
    } else {
    }
  }, [selectedFamily]);

  useEffect(() => {
    form.setFieldsValue({
      module: selectedFamily ?? null,
      select: autoCompleteValue ?? null,
    });
  }, [form, selectedFamily, autoCompleteValue]);
  return (
    <div className="w-64 space-y-6">
      <Form form={form} layout="vertical">
        <Form.Item
          label={t("voip.selectModule")}
          name="module"
          rules={[
            {
              required: true,
              message: "",
            },
          ]}
        >
          <Select
            style={{ width: "100%" }}
            allowClear
            placeholder={t("voip.selectModule")}
            options={familiesOption}
            value={selectedFamily}
            onChange={async (value) => {
              setIdModule(value);
              setSelectedFamily(value);
              // setAutoCompleteLabel("");
              setAutoCompleteOptions([]);
              form.resetFields([t("voip.search_select")]);
              // await fetchOptions("", value);
              if (autoCompleteRef.current) {
                autoCompleteRef.current.focus();
              }
            }}
          />
        </Form.Item>

        <Form.Item
          label={
            selectedFamily ? (
              `${t("voip.search_select")} ${families?.[selectedFamily]}`
            ) : (
              <span className=" text-white"> </span>
            )
          }
          name="select"
          rules={[
            {
              required: selectedFamily,
              message: "",
            },
          ]}
        >
          <AutoComplete
            ref={autoCompleteRef}
            defaultOpen={true}
            disabled={!selectedFamily}
            // value={!selectedFamily ? "" : autoCompleteLabel}
            value={autoCompleteValue}
            options={autoCompleteOptions}
            style={{
              width: "100%",
            }}
            onSelect={onSelect}
            onSearch={(text) => fetchOptions(text)}
            allowClear={{
              clearIcon: loadingOptions ? (
                <LoadingOutlined style={{ fontSize: 12, color: "#1677ff" }} />
              ) : (
                <CloseCircleFilled />
              ),
            }}
            onChange={onChange}
          />
        </Form.Item>
      </Form>
      <div className="flex flex-row justify-end pt-8">
        <Button
          loading={saveIsLoading}
          onClick={saveAffectation}
          disabled={saveIsDisabled}
          size="small"
          type="primary"
        >
          {t("voip.save")}
        </Button>
      </div>
    </div>
  );
};

export default AffectationContent;
