import React, { useCallback, useEffect, useState } from "react";
import "./styles.css";
import {
  Button,
  Input,
  Tree,
  DirectoryTree,
  Modal,
  Dropdown,
  Menu,
  Space,
  Popconfirm,
  Typography,
  Tooltip,
  Spin,
  Empty,
  Form,
  message,
} from "antd";
import { GenericButton } from "../components/GenericButton";
import {
  EditOutlined,
  PlusOutlined,
  DeleteOutlined,
  RestOutlined,
  DownOutlined,
  FolderOutlined,
  FileOutlined,
} from "@ant-design/icons";
import { useParams } from "react-router-dom";
import Content from "./Content";
import MainService from "../../services/main.service";
import { toastNotification } from "../../components/ToastNotification";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import Confirm from "../../components/GenericModal";
import { FiEdit, FiMoreVertical, FiTrash } from "react-icons/fi";
import { AiOutlineFileAdd } from "react-icons/ai";
import {
  DragFoldersOrdering,
  changeRankOnDragAndDropChild,
  handleDragAndDropFromChildToFolder,
  isChild,
  isFolder,
  sortFolders,
} from "./sideBarDndUtils";

const Sidebar = ({
  selectedGroup,
  selectedNode,
  setSelectedNode,
  expandedKeys,
  setExpandedKeys,
  value,
  folders,
  setFolders,
  nodeName,
  setNodeName,
  selectedFolder,
  setSelectedFolder,
  loading,
  setLoading,
  delete1,
  selectedKeys,
  setSelectedKeys,
  binders,
  setDisabled,
}) => {
  const [t] = useTranslation("common");
  const { groupId } = useParams();
  const { groupWiki } = useSelector((state) => state.wiki);
  const [form] = Form.useForm();

  const [newTitle, setNewTitle] = useState("");
  const [newTitleEn, setNewTitleEn] = useState("");
  const [newPage, setNewPage] = useState("");
  const [newPageEn, setNewPageEn] = useState("");
  const [visible, setVisible] = useState(false);
  const [visibleAdd, setVisibleAdd] = useState(false);
  const [visibleAddPage, setVisibleAddPage] = useState(false);
  const [visibleDelete, setVisibleDelete] = useState(false);
  const [visibleDeletePage, setVisibleDeletePage] = useState(false);
  const [nodeNameEn, setNodeNameEn] = useState("");
  const [nodeKey, setNodeKey] = useState("");
  const [pageKey, setPageKey] = useState("");
  const [pageToDelete, setPageToDelete] = useState("");
  const [showDelete, setShowDelete] = useState(false);
  const [editableStr, setEditableStr] = useState("");

  const validateMessages = {
    required: "'${name}' is required!",
  };

  function limitTitleFr(data, maxLength) {
    if (data && data.length > maxLength) {
      return (data = data.slice(0, maxLength) + "...");
    } else return data;
  }

  const generateSlug = (str) => {
    const slug = str
      .toLowerCase()
      .trim()
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .replace(/[^a-z0-9]+/g, "_")
      .replace(/(^-|-$)+/g, "");
    return slug;
  };

  const onFinishAdd = () => {
    setLoading(true);
    setVisibleAdd(true);
    MainService.createFolder({
      title_fr: form.getFieldValue().newTitle,
      title_en: form.getFieldValue().newTitleEn,
      label: form.getFieldValue().newTitle,
      product_id: selectedGroup,
      slug_url: generateSlug(
        `${value}_${form.getFieldValue().newTitle}_pagepardefaut`
      ),
      status: 0,
    })
      .then((res) => {
        const newTreeData = [...folders];

        setNodeKey(res?.data?.data.key);

        setExpandedKeys([...expandedKeys, res?.data?.data.key]);

        newTreeData.push({
          key: res?.data?.data.key,
          label: form.getFieldValue().newTitle,
          title: form.getFieldValue().newTitle,
          title_fr: form.getFieldValue().newTitle,
          title_en: form.getFieldValue().newTitleEn,
          selected: true,
          selectable: false,
          children: [
            {
              key: res.data.data.children[0].key,
              id: res.data.data.children[0].id,
              title: res.data.data.children[0].title_fr,
              title_fr: res.data.data.children[0].title_fr,
              title_en: res.data.data.children[0].title_en,
              slug_url: generateSlug(
                `${value}_${form.getFieldValue().newTitle}_pagepardefaut`
              ),
              content_fr: res.data.data.children[0].content_fr,
              content_en: res.data.data.children[0].content_en,
              folder_title: form.getFieldValue().newTitle,
              status: res.data.data.children[0].status,
              folder_id: res?.data?.data.key,
            },
          ],
        });

        setFolders(newTreeData);
        const parentIndex = newTreeData.findIndex(
          (node) => node.key === res?.data?.data.key
        );
        setVisibleAdd(false);
        setLoading(false);

        setSelectedNode({ ...newTreeData[parentIndex].children[0], status: 0 });
        setSelectedKeys([newTreeData[parentIndex].children[0].key]);
        setDisabled(true);
      })
      .catch((err) => {
        setLoading(false);
        console.log(err);
        if (
          err.response.data.errors[0] === "The title fr has already been taken."
        )
          toastNotification("error", t(`wiki.FolderAlreadyExists`), "topRight");
        else toastNotification("error", t("toasts.somethingWrong"), "topRight");
      });
  };
  const updateFolder = async () => {
    let updatedTreeData = [];
    setLoading(true);
    MainService.updateFolder(nodeKey, {
      title_fr: form.getFieldValue().nodeName,
      title_en: form.getFieldValue().nodeNameEn,
      product_id: selectedGroup,
    })
      .then((res) => {
        updatedTreeData = folders.map((node) => {
          if (node.key == nodeKey) {
            return {
              ...node,
              title: form.getFieldValue().nodeName,
              title_fr: form.getFieldValue().nodeName,
              title_en: form.getFieldValue().nodeNameEn,
            };
          }
          return node;
        });

        const parentIndex = updatedTreeData.findIndex(
          (node) => node.key === nodeKey
        );

        //         updatedTreeData[parentIndex].children = [
        //         ...updatedTreeData[parentIndex].children,
        //           {

        //     slug_url: generateSlug(`a_${form.getFieldValue().nodeName}_a`),
        //           },
        // ]

        updatedTreeData[parentIndex].children.map((e) => {
          return (e.slug_url = generateSlug(
            `${value}_${form.getFieldValue().nodeName}_${e.title_fr}`,
            (e.folder_title = form.getFieldValue().nodeName)
          ));
        });

        //setFolders(limitTitleFr(updatedTreeData, 13))
        setFolders(updatedTreeData);
        setLoading(false);
        setDisabled(true);
      })
      .catch((err) => {
        console.log(err);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      });
  };

  const deleteFolder = async () => {
    setLoading(true);
    MainService.deleteFolder(nodeKey)
      .then((res) => {
        let dataa = folders;

        setFolders(dataa.filter((e) => e.key !== nodeKey));
        setSelectedNode("");
        //return folders
        setLoading(false);
      })
      .catch((err) => {
        console.log(err);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      });
  };

  const deletePage = (pageKey) => {
    setLoading(true);
    const newTreeData2 = [...folders];

    MainService.deletePage(pageKey)
      .then((res) => {
        newTreeData2.map((e) => {
          if (
            e.key == selectedFolder.folder_id
              ? selectedFolder.folder_id
              : selectedFolder.id
          ) {
            return (e.children = e.children.filter((el) => el.id !== pageKey));
          }
        });

        //setFolders(limitTitleFr(newTreeData2, 13))
        setFolders(newTreeData2);

        if (selectedNode.id === pageKey) setSelectedNode("");

        setLoading(false);
      })
      .catch((err) => {
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
        console.log(err);
      });
  };

  const delete2 = (pageKey, folderKey) => {
    MainService.deletePage(pageKey)
      .then((res) => {
        const newTreeData2 = [...folders];
        newTreeData2.map((e) => {
          if (e.key == folderKey) {
            return (e.children = e.children.filter((el) => el.id !== pageKey));
          }
        });
        //setFolders(limitTitleFr(newTreeData2, 13))
        setFolders(newTreeData2);
        if (selectedNode.id === pageKey) setSelectedNode("");
        setLoading(false);
      })
      .catch((err) => {
        console.log(err);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      });
  };

  const handleOk = () => {
    updateFolder();
    setVisible(false);
  };

  const showModal = (nodeKey, nodeName, nodeNameEn) => {
    setVisible(true);
    setNodeKey(nodeKey);
    setNodeName(nodeName);
    setNodeNameEn(nodeNameEn);
  };

  const handleCancel = () => {
    setVisible(false);
  };

  const onCancelAdd = () => {
    setVisibleAdd(false);
  };
  const onCancelAddPage = () => {
    setVisibleAddPage(false);
  };
  const onOkAdd = () => {
    onFinishAdd();
  };
  const onOkAddPage = () => {
    onFinish();
  };
  const showModalDelete = (nodeKey, nodeName) => {
    setVisibleDelete(true);
    setNodeKey(nodeKey);
    setNodeName(nodeName);
  };

  const showModalDeletePage = (parentNode, pageId, pageTitle) => {
    setVisibleDeletePage(true);
    setPageKey(pageId);
    setSelectedFolder(parentNode);
    setPageToDelete(pageTitle);
  };

  const onCancelDelete = () => {
    setVisibleDelete(false);
  };
  const onOkDelete = () => {
    deleteFolder();
    setVisibleDelete(false);
  };

  const onCancelDeletePage = () => {
    setVisibleDeletePage(false);
  };
  const onOkDeletePage = () => {
    setVisibleDeletePage(false);
    deletePage(pageKey);
  };

  const onInputChangeAdd = (e) => {
    setNewTitle(e.target.value);
  };
  const onInputChangeAddEn = (e) => {
    setNewTitleEn(e.target.value);
  };
  const onInputChangeAddPage = (e) => {
    setNewPage(e.target.value);
  };
  const onInputChangeAddPageEn = (e) => {
    setNewPageEn(e.target.value);
  };
  const onSelect = (selectedKeys, event) => {
    setSelectedKeys(selectedKeys);
    setSelectedNode(event.node);
    setDisabled(true);

    // if (event.node.props.data.children) {
    // }

    //else setSelectedNode(event.node.props.data.children[0])
    //console.log(event.node.props)
  };
  // const onExpand = (expandedKeys) => {
  //   setExpandedKeys(expandedKeys)
  // }
  const onExpand = (expandedKeys, { expanded, node }) => {
    setExpandedKeys(
      expanded
        ? expandedKeys.concat(node.props.eventKey)
        : expandedKeys.filter((key) => key !== node.props.eventKey)
    );
  };

  const addParentNode = () => {
    setVisibleAdd(true);
  };

  // const addChildNode = (parentNode) => {
  //   //console.log('parentNode', parentNode.key)
  //   const newTreeData = [...folders]
  //   const parentIndex = newTreeData.findIndex(
  //     (node) => node.key === parentNode.key,
  //   )

  //   newTreeData[parentIndex].children = [
  //     ...parentNode.children,
  //     {
  //       name: `Page ${newTreeData[parentIndex].children.length}`,
  //       title_fr: `Page ${newTreeData[parentIndex].children.length}`,
  //       key: `${parentNode.key}-${newTreeData[parentIndex].children.length}`,
  //       //slugUrl: `/${parentNode.title}/Page ${newTreeData[parentIndex].children.length}`,
  //       slugUrl: parentNode.title,
  //     },
  //   ]
  //   setFolders(newTreeData)
  // }

  const addChildNode = (parentNode) => {
    setVisibleAddPage(true);
    setSelectedFolder(parentNode);
  };

  const onFinish = () => {
    setVisibleAddPage(true);
    const newTreeData = [...folders];
    const parentIndex = newTreeData.findIndex(
      (node) => node.key === selectedFolder.key
    );

    setLoading(true);
    MainService.addPage({
      title: form.getFieldValue().newPage,
      title_fr: form.getFieldValue().newPage,
      title_en: form.getFieldValue().newPageEn,
      folder_id: selectedFolder.key,
      slug_url: generateSlug(`${value}_${selectedFolder.title}_${newPage}`),
      content_fr: "<p><br></p>",
      content_en: "<p><br></p>",
      status: 0,
    })

      .then((res) => {
        newTreeData[parentIndex].children = [
          ...selectedFolder.children,
          {
            id: res.data.data.id,
            name: newPage,
            title: form.getFieldValue().newPage,
            title_fr: form.getFieldValue().newPage,
            title_en: form.getFieldValue().newPageEn,
            key: `${res.data.data.folder_id}-${res.data.data.id}`,
            slug_url: generateSlug(
              `${value}_${selectedFolder.title}_${newPage}`
            ),
            folder_id: res.data.data.folder_id,
            folder_title: res.data.data.folder_title,
            content_fr: "<p><br></p>",
            content_en: "<p><br></p>",

            //slugUrl: `/${parentNode.title}/Page ${newTreeData[parentIndex].children.length}`,
            //slug_url: parentNode.title,
          },
        ];
        const childIndex = newTreeData[parentIndex].children.findIndex(
          (node) => node.id == res.data.data.id
        );

        //setFolders(limitTitleFr(newTreeData, 13))
        setFolders(newTreeData);

        form.resetFields();
        setVisibleAddPage(false);
        setSelectedNode({
          ...newTreeData[parentIndex].children[childIndex],
          status: 0,
        });

        setSelectedKeys([res.data.data.key]);

        // onSelect('select')

        setLoading(false);
      })

      .catch((err) => {
        setLoading(false);
        console.log(err.response.data.errors);
        if (
          err?.response?.data?.errors[0] ===
          "The title fr has already been taken."
        )
          toastNotification("error", t(`wiki.PageAlreadyExists`), "topRight");
      });
  };
  useEffect(() => {
    form.setFieldsValue({
      nodeName: nodeName,
      nodeNameEn: nodeNameEn,

      //.replace(/\s/g, ''),
    });
  }, [nodeName, nodeNameEn]);

  const renderTitle = (node) => {
    const items = [
      {
        label: t(`wiki.Edit`),
        key: "2",
        icon: <FiEdit className="h-4 w-4 text-slate-400" />,
      },
      {
        label: t(`wiki.Delete`),
        key: "3",
        danger: true,
        icon: <FiTrash className="h-4 w-4" />,
      },
    ];

    const onClick = ({ key }) => {
      if (key == 2) {
        showModal(node.key, node.title_fr, node.title_en);
      } else if (key == 3) {
        Confirm(
          `${t(`wiki.Delete`)} "${node.title_fr}" `,
          t(`wiki.Confirm`),
          <RestOutlined style={{ color: "red" }} />,
          function func() {
            return MainService.deleteFolder(node.key)
              .then((res) => {
                let dataa = folders;

                setFolders(dataa.filter((e) => e.key !== node.key));
                if (node.key == selectedNode.folder_id) setSelectedNode("");
                //return folders
                setLoading(false);
              })
              .catch((err) => {
                console.log(err);
                toastNotification(
                  "error",
                  t("toasts.somethingWrong"),
                  "topRight"
                );
              });
          },
          true
        );
      }
    };

    return (
      <span>
        {node.children && (
          <div>
            {limitTitleFr(node.title_fr, 25)}
            <div style={{ float: "right" }} className="flex">
              <Tooltip title={t(`wiki.AddPage`)}>
                <a
                  onClick={(event) => {
                    event.stopPropagation();
                    addChildNode(node);
                    form.resetFields();
                  }}
                  style={{ marginRight: 3 }}
                >
                  <PlusOutlined className="h-4 w-4 cursor-pointer text-gray-400 hover:text-gray-700" />
                </a>
              </Tooltip>

              {/* &nbsp;
              <Tooltip title={t(`wiki.EditFolder`)}>
                <a
                  onClick={(event) => {
                            event.stopPropagation()
                            event.preventDefault()
                            showModal(node.key, node.title_fr, node.title_en)
                  }}
                  style={{marginRight:3}}
                >
                  <EditOutlined />
                </a>
              </Tooltip>
              &nbsp;
              <Tooltip title={t(`wiki.DeleteFolder`)}>
                <a
                  // onClick={(event) => {
                  //           event.stopPropagation()
                  //           event.preventDefault()
                  //           showModalDelete(node.key, node.title_fr)
                  //         }}
                  onClick={(event) => {
                 event.stopPropagation()
                 event.preventDefault()
                    Confirm(
                      `${t(`wiki.Delete`)} "${node.title_fr
                      }" `,
                      t(`wiki.Confirm`),
                      <RestOutlined style={{ color: "red" }} />,
                      function func() {
                        return (MainService.deleteFolder(node.key)
      .then((res) => {
        let dataa = folders
       
        
        setFolders(dataa.filter(e => e.key !== node.key))
        //return folders
        setLoading(false)
      })
      .catch((err) => {
        console.log(err)
      }))
                      },
                      true,
                    )     
            }}

                >
                  <DeleteOutlined />
                </a>
              </Tooltip> */}
              <Dropdown
                trigger={["click"]}
                placement="bottomLeft"
                menu={{
                  items,
                  onClick,
                }}
              >
                <a
                  onClick={(event) => {
                    event.preventDefault();
                    event.stopPropagation();
                  }}
                >
                  <FiMoreVertical className="mt-1 h-4 w-4 cursor-pointer text-gray-400 hover:text-gray-700" />
                </a>
              </Dropdown>
            </div>
          </div>
        )}
        {!node.children && (
          <div className="group relative ">
            <div className="flex items-center justify-between">
              <div className="hover:gray-300">
                {/* <FileOutlined /> */}
                &nbsp;
                {limitTitleFr(node.title_fr, 30)}
              </div>
              <Button
                danger
                type="link"
                className="opacity-0 transition-opacity group-hover:opacity-100"
                icon={<DeleteOutlined />}
                onClick={(event) => {
                  event.stopPropagation();
                  event.preventDefault();
                  Confirm(
                    `${t(`wiki.Delete`)} "${node.title_fr}" `,
                    t(`wiki.Confirm`),
                    <RestOutlined style={{ color: "red" }} />,
                    function func() {
                      return delete1(node.id, node.folder_id);
                    },
                    true
                  );
                }}
              />
            </div>
          </div>
        )}
      </span>
    );
  };

  // const renderTreeNodes = (folders) =>
  //   folders.map((item) => {
  //     if (item.page_id) {
  //       return (
  //         <TreeNode title={item.label} key={item.id}>
  //           {renderTreeNodes(item.page_id)}
  //         </TreeNode>
  //       )
  //     }
  //     return <TreeNode title={item.label} key={item.id} />
  //   })

  const handleKeyPressModif = (event) => {
    if (event.key === "Enter") {
      //form.submit();
      handleOk();
    }
  };

  const handleKeyPressAdd = (event) => {
    if (event.key === "Enter") {
      //setVisibleAdd(false)
      //form.submit();
      onOkAdd();
    }
  };

  const handleKeyPressAddPage = (event) => {
    if (event.key === "Enter") {
      //form.submit();
      onOkAddPage();
    }
  };

  const [dragNode, setDragNode] = useState(null);

  const onDragEnter = (info) => {
    // console.log("node",info.node);
    setDragNode(info.node);
    // expandedKeys, set it when controlled is needed
    // setExpandedKeys(info.expandedKeys)
    //conditions: drag only works from folder to folder or to change page or child rank
    //if the drag and drop is between folders
  };

  const onDrop = (info) => {
    // console.log("INFOS ON DROP", info);
    // console.log("folders", folders);

    const dropKey = info.node.key;
    const dragKey = info.dragNode.key;
    const dropPos = info.node.pos.split("-");
    const dropPosition =
      info.dropPosition - Number(dropPos[dropPos.length - 1]);
    const dragPos = info.dragNode.pos.split("-");
    const dragPosition =
      info.dragPosition - Number(dragPos[dragPos.length - 1]);

    const dropGaps = {
      dragOverGapBottom: info.node.dragOverGapBottom,
      dragOverGapTop: info.node.dragOverGapTop,
    };

    // console.log("DROP INFOS", info);

    // console.log("DROP POS", dropPos);
    // console.log("DROP POSITION", dropPosition);
    // console.log("DROP KEY", dropKey);
    // console.log("DRAG KEY", dragKey);
    //same object
    if (dragKey == dropKey) return;
    //from folder to folder
    if (isFolder(dragKey.toString()) && isFolder(dropKey.toString())) {
      setLoading(true);
      let { newTreeData, jsonToReturn } = DragFoldersOrdering(
        dragKey,
        dropKey,
        dropPosition,
        folders
      );
      MainService.wikiUpdateFoldersRanksInsideGroup(jsonToReturn)
        .then((res) => {
          console.log(res);
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
      setFolders(newTreeData);
    }
    //from folder to page
    if (isFolder(dragKey.toString()) && isChild(dropKey.toString())) return;
    //from page to folder
    if (isChild(dragKey.toString()) && isFolder(dropKey.toString())) {
      console.log("Drag is a child and drop is a folder");

      const dragKeyParent = dragKey.split("-")[0];
      const dragKeyParentChildren = folders.find(
        (folder) => folder.key == dragKeyParent
      ).children.length;

      if (dragKeyParentChildren <= 1) {
        return;
      } else {
        setLoading(true);

        let { newTreeData, dataToSend } = handleDragAndDropFromChildToFolder(
          dragKey,
          dropKey,
          info,
          dropPosition,
          folders
        );

        MainService.wikiUpdateFolderPagesRanks(dataToSend)
          .then((res) => {
            console.log(res);
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setLoading(false);
          });

        setFolders(newTreeData);
      }
    }

    if (isChild(dragKey.toString()) && isChild(dropKey.toString())) {
      if (!info.dropToGap) return;
      const dragKeyParent = dragKey.split("-")[0];
      const dragKeyParentChildren = folders.find(
        (folder) => folder.key == dragKeyParent
      ).children.length;
      if (dragKeyParentChildren <= 1) {
        return;
      } else {
        //search for dragKey parent

        //number of children inside dragKey parent
        // handleDragAndDrop(dragKey, dropKey, info, dropPosition);
        let { newTreeData, dataToSend } = changeRankOnDragAndDropChild(
          dragKey,
          dropKey,
          info,
          dropPosition,
          dropGaps,
          folders
        );
        setLoading(true);
        MainService.wikiUpdateFolderPagesRanks(dataToSend)
          .then((res) => {
            console.log(res);
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setLoading(false);
          });

        setFolders(newTreeData);
      }
    }

    //i want it to prevent making a folder a child of a page
    //if the drop key is a page then return

    // handleDragAndDrop(dragKey, dropKey, info, dropPosition);

    // if(isFolder(dragKey.toString()) && isFolder(dropKey.toString())) {
    //   setFolders(swapFoldersRanks(dragKey, dropKey));
    //   return;
    // }
  };
  return (
    <>
      {selectedGroup && (
        <Spin spinning={loading} size="medium">
          <div
            className="sticky top-0"
            style={{ backgroundColor: "white", zIndex: 99 }}
          >
            <GenericButton
              type="primary"
              icon={<PlusOutlined />}
              text={t(`wiki.AddFolder`)}
              //disabled={!selectedGroup || loading}
              onClick={(event) => {
                event.stopPropagation();
                form.resetFields();
                addParentNode();
              }}
            />
          </div>
          <br></br>
          <br></br>

          {folders.length > 0 ? (
            <>
              <div className="flex">
                <div style={{ height: "100%", width: "100%" }}>
                  <Tree
                    draggable
                    onDragEnter={onDragEnter}
                    onDrop={onDrop}
                    style={{
                      width: "300px",
                      height: "full",
                    }}
                    selectedKeys={selectedKeys}
                    defaultExpandAll
                    showLine={true}
                    defaultExpandParent
                    expandedKeys={expandedKeys}
                    onSelect={onSelect}
                    onExpand={onExpand}
                    treeData={folders}
                    titleRender={renderTitle}
                    showIcon={false}
                    blockNode
                  ></Tree>
                </div>
                <div></div>
              </div>
            </>
          ) : (
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          )}
        </Spin>
      )}
      <Modal
        title={t(`wiki.EditFolder`)}
        open={visible}
        onCancel={handleCancel}
        footer={[
          <div className="space-x-2">
            <Button key="back" onClick={handleCancel}>
              {t(`wiki.Cancel`)}
            </Button>
            <Button
              disabled={form.getFieldValue().nodeName === ""}
              key="submit"
              type="primary"
              loading={loading}
              onClick={handleOk}
            >
              {t(`form.save`)}
            </Button>
          </div>,
        ]}
        className="my-modal"
      >
        {/* <Input
              value={nodeName}
              onChange={(e) => setNodeName(e.target.value)}
              //onKeyPress={handleKeyPressModif}
        />
        <Input
              value={nodeNameEn}
              onChange={(e) => setNodeNameEn(e.target.value)}
              //onKeyPress={handleKeyPressModif}
            /> */}

        <Form
          name="formItemPath"
          layout="vertical"
          onFinish={onFinishAdd}
          validateMessages={validateMessages}
          form={form}
          id="form"
          //onKeyPress={handleKeyPressAdd}
        >
          <Form.Item
            name="nodeName"
            label={t(`wiki.TitleFolderFr`)}
            rules={[
              {
                required: true,
                message: t(`wiki.TitleRequired`),
              },
            ]}
          >
            <Input onChange={(e) => setNodeName(e.target.value)} />
          </Form.Item>

          <Form.Item name="nodeNameEn" label={t(`wiki.TitleFolderEn`)}>
            <Input onChange={(e) => setNodeNameEn(e.target.value)} />
          </Form.Item>
        </Form>
      </Modal>
      <Modal
        title={t(`wiki.AddAFolder`)}
        open={visibleAdd}
        onCancel={onCancelAdd}
        // onOk={onOkAdd}
        // htmlType="submit"
        footer={[
          <Button key="back" onClick={onCancelAdd}>
            {t(`wiki.Cancel`)}
          </Button>,
          <Button
            disabled={
              form.getFieldValue().newTitle === undefined ||
              form.getFieldValue().newTitle === ""
            }
            // key="submit"
            type="primary"
            //disabled={loading}
            loading={loading}
            onClick={onFinishAdd}
          >
            {t(`form.create`)}
          </Button>,
        ]}
        className="my-modal"
      >
        <Form
          name="formItemPath"
          layout="vertical"
          onFinish={onFinishAdd}
          validateMessages={validateMessages}
          form={form}
          id="form"
          //onKeyPress={handleKeyPressAdd}
        >
          <Form.Item
            name="newTitle"
            label={t(`wiki.TitleFolderFr`)}
            rules={[
              {
                required: true,
                message: t(`wiki.TitleRequired`),
              },
            ]}
          >
            <Input onChange={onInputChangeAdd} />
          </Form.Item>

          <Form.Item
            name="newTitleEn"
            label={t(`wiki.TitleFolderEn`)}
            // rules={[
            //   {
            //     required: true,
            //     message: t(`wiki.TitleRequired`),
            //   },
            // ]}
          >
            <Input onChange={onInputChangeAddEn} />
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        title={
          <span>
            Êtes-vous sûr(e) de vouloir supprimer le dossier <b>{nodeName}</b>
          </span>
        }
        open={visibleDelete}
        onCancel={onCancelDelete}
        // onOk={onOkDelete}
        footer={[
          <Button key="back" onClick={onCancelDelete}>
            Annuler
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={loading}
            onClick={onOkDelete}
          >
            Valider
          </Button>,
        ]}
        className="my-modal"
      ></Modal>
      <Modal
        title={
          <span>
            {t(`wiki.AddPageToFolder`)}
            <b>{selectedFolder.title_fr}</b>
          </span>
        }
        open={visibleAddPage}
        onCancel={onCancelAddPage}
        // onOk={onOkAddPage}
        htmlType="submit"
        footer={[
          <Button key="back" onClick={onCancelAddPage}>
            {t(`wiki.Cancel`)}
          </Button>,
          <Button
            disabled={
              form.getFieldValue().newPage === undefined ||
              form.getFieldValue().newPage === ""
            }
            key="submit"
            type="primary"
            loading={loading}
            onClick={onOkAddPage}
          >
            {t(`form.create`)}
          </Button>,
        ]}
        className="my-modal"
      >
        <Form
          name="formItemPath"
          layout="vertical"
          onFinish={onFinish}
          // onFinishFailed={onFinishFailed}
          validateMessages={validateMessages}
          form={form}
          id="form"
        >
          <Form.Item
            name="newPage"
            label={t(`wiki.TitlePageFr`)}
            rules={[
              {
                required: true,
                message: t(`wiki.TitleRequired`),
              },
            ]}
          >
            <Input
              onChange={onInputChangeAddPage}
              //onKeyPress={handleKeyPressAddPage}
            />
          </Form.Item>
          <Form.Item
            name="newPageEn"
            label={t(`wiki.TitlePageEn`)}
            // rules={[
            //       {
            //         required: true,
            //         message: 'The page name in english is required!',
            //       },
            //     ]}
          >
            <Input
              onChange={onInputChangeAddPageEn}
              //onKeyPress={handleKeyPressAddPage}
            />
          </Form.Item>
        </Form>
      </Modal>
      <Modal
        title={
          <span>
            Êtes-vous sûr(e) de vouloir supprimer la page <b>{pageToDelete}</b>{" "}
            qui appartient au dossier <b>{nodeName}</b>?
          </span>
        }
        open={visibleDeletePage}
        onCancel={onCancelDeletePage}
        // onOk={onOkDeletePage}
        footer={[
          <Button key="back" onClick={onCancelDeletePage}>
            Annuler
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={loading}
            onClick={onOkDeletePage}
          >
            Valider
          </Button>,
        ]}
        className="my-modal"
      ></Modal>
    </>
  );
};

export default Sidebar;
