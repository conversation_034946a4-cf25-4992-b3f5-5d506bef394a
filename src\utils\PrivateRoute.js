import { useSelector } from "react-redux";
import { Navigate, Outlet, useLocation } from "react-router-dom";

import useLocalStorage from "../custom-hooks/useLocalStorage";
import { roles } from "./role";
import { URL_ENV } from "..";
import { LogoutLink } from "pages/layouts/chat/utils/ConversationUtils";

const PrivateRoute = ({ accessTo }) => {
  const [token] = useLocalStorage(
    "accessToken",
    localStorage.getItem("accessToken")
  );
  const { user } = useSelector((state) => state.user);
  const { pathname } = useLocation();

  if (!token) {
    LogoutLink();
    return <></>;
  }

  const url = new URL(window.location.href);

  const isChatPath = url.pathname.includes("chat");
  const hasAccess = user?.access?.[accessTo] === "1";

  const canAccess =
    roles.includes(user?.role) ||
    accessTo === "default" ||
    hasAccess ||
    (url.pathname === "/rmc" && user?.rmc_access === "OUI") ||
    (accessTo === "under_test" && URL_ENV?.REACT_APP_BRANCH !== "prod");

  if (isChatPath && !hasAccess) {
    return (
      <Navigate to="/unauthorized" state={{ from: url.pathname }} replace />
    );
  }

  return canAccess ? (
    <Outlet />
  ) : (
    <Navigate to="/unauthorized" state={{ from: url.pathname }} replace />
  );
};

export default PrivateRoute;
