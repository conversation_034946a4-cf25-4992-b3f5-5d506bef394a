import { <PERSON><PERSON>, Switch, Table } from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useWindowSize } from "custom-hooks/useWindowSize";
import { SphereDrawer } from "components/Chat/Drawer";
import ChatNotification from "./components/notification-management/chat-notification";
import {
  HiOutlineArrowDownTray,
  HiOutlineCalendar,
  HiOutlineChartBar,
  HiOutlineChatBubbleBottomCenterText,
  HiOutlineCog8Tooth,
  HiOutlineEnvelope,
  HiOutlinePhone,
  HiOutlineShare,
  HiOutlineUserCircle,
  HiOutlineUserGroup,
  HiOutlineVideoCamera,
} from "react-icons/hi2";
import { DollarOutlined, InboxOutlined } from "@ant-design/icons";
import { FiBell, FiBookOpen } from "react-icons/fi";
import { HiOutlinePencilAlt } from "react-icons/hi";
import { AiOutlineShoppingCart } from "react-icons/ai";
import { Lu<PERSON>almtree } from "react-icons/lu";
import { Cg<PERSON><PERSON>lane } from "react-icons/cg";
import { Blocks, HeartHandshake } from "lucide-react";
import TicketIconSphere from "components/icons/TicketIconSphere";
import MailingNotification from "./components/notification-management/mailing-notification";

const NotificationsManagement = () => {
  const [_, height] = useWindowSize();
  const [t] = useTranslation("common");
  const [openDrawer, setOpenDrawer] = useState("");

  const modules = [
    {
      key: "chat",
      label: t("menu1.chat"),
      icon: (
        <HiOutlineChatBubbleBottomCenterText style={{ fontSize: "21px" }} />
      ),
    },
    // {
    //   key: "voip",
    //   label: t("menu1.voip"),
    //   icon: <HiOutlinePhone style={{ fontSize: "21px" }} />,
    // },

    // {
    //   key: "callLog",
    //   label: t("menu2.logs"),
    //   icon: <HiOutlinePhone style={{ fontSize: "21px" }} />,
    // },
    // {
    //   key: "company_directory",
    //   label: t("voip.directory"),
    //   icon: <FiBookOpen style={{ fontSize: "21px" }} />,
    // },
    // {
    //   key: "visio",
    //   label: t("menu1.visio"),
    //   icon: <HiOutlineVideoCamera style={{ fontSize: "21px" }} />,
    // },
    // {
    //   key: "notes",
    //   label: t("menu1.notes"),
    //   icon: <HiOutlinePencilAlt style={{ fontSize: "21px" }} />,
    // },
    // {
    //   key: "rmc",
    //   label: t("menu1.rmc"),
    //   icon: <HiOutlineShare style={{ fontSize: "21px" }} />,
    // },
    // {
    //   key: "contacts",
    //   label: t("menu1.contacts"),
    //   icon: <HiOutlineUserGroup style={{ fontSize: "21px" }} />,
    // },

    // {
    //   key: "settings",
    //   label: t("menu1.settings"),
    //   icon: <HiOutlineCog8Tooth style={{ fontSize: "21px" }} />,
    // },
    // {
    //   key: "notifications",
    //   label: t("menu1.notifications"),
    //   icon: <FiBell style={{ fontSize: "21px" }} />,
    // },
    // {
    //   key: "stats",
    //   label: t("menu1.stats"),
    //   icon: <HiOutlineChartBar style={{ fontSize: "21px" }} />,
    // },
    {
      key: "mailing",
      label: t("menu1.mailing"),
      icon: <HiOutlineEnvelope style={{ fontSize: "21px" }} />,
    },
    // {
    //   key: "tasks",
    //   label: t("menu1.tasks"),
    //   icon: <HiOutlineCalendar style={{ fontSize: "21px" }} />,
    // },
    // {
    //   key: "import",
    //   label: t("menu1.import"),
    //   icon: <HiOutlineArrowDownTray style={{ fontSize: "21px" }} />,
    // },
    // {
    //   key: "profile",
    //   label: t("menu1.profile"),
    //   icon: <HiOutlineUserCircle style={{ fontSize: "21px" }} />,
    // },
    // {
    //   key: "products",
    //   label: t("menu1.products"),
    //   icon: <AiOutlineShoppingCart style={{ fontSize: "21px" }} />,
    // },
    // {
    //   key: "deals",
    //   label: t("menu1.deals"),
    //   icon: <HeartHandshake size={22} />,
    // },
    // {
    //   key: "tickets",
    //   label: t("menu1.tickets"),
    //   icon: <TicketIconSphere size={24} />,
    // },

    // {
    //   key: "projects",
    //   label: t("menu1.projects"),
    //   icon: <Blocks size={22} />,
    // },
    // {
    //   key: "booking",
    //   label: t("menu1.booking"),
    //   icon: <LuPalmtree style={{ fontSize: "21px" }} />,
    // },
    // {
    //   key: "leads",
    //   label: t("menu1.leads"),
    //   icon: <CgUserlane style={{ fontSize: "21px" }} />,
    // },
    // {
    //   key: "companies",
    //   label: t("menu1.companies"),
    //   icon: <HiOutlineCog8Tooth style={{ fontSize: "21px" }} />,
    // },
    // {
    //   key: "invoices",
    //   label: t("menu1.invoices"),
    //   icon: <DollarOutlined style={{ fontSize: "21px" }} />,
    // },
    // {
    //   key: "interactions",
    //   label: t("menu1.interactions"),
    //   icon: <InboxOutlined style={{ fontSize: "21px" }} />,
    // },
  ];

  const columns = [
    {
      title: t("fields_management.moduleName"),
      dataIndex: "label",
      key: "label1",
      render: (_, record) => (
        <div className="flex items-center justify-between gap-1">
          <div className="flex items-center gap-1">
            {record.icon}
            <span>{record.label}</span>
          </div>

          {["chat", "voip", "mailing"].includes(record.key) && (
            <Button type="link" onClick={() => setOpenDrawer(record.key)}>
              {t("table.header.extra")}
            </Button>
          )}
        </div>
      ),
    },
    {
      title: "Notification (Desktop)",
      key: "desktopNotification",
      render: (_, record) => <Switch disabled />,
      align: "center",
    },
    {
      title: "Notification (Local)",
      key: "localNotification",
      render: (_, record) => <Switch disabled />,
      align: "center",
    },
  ];

  return (
    <div className="mt-5 p-2">
      <SphereDrawer
        mask={true}
        width={450}
        placement="right"
        title={
          <div className="flex items-center gap-x-1 capitalize">
            <HiOutlineCog8Tooth style={{ fontSize: "21px" }} />
            {t("menu1.manage", { module: openDrawer })}
          </div>
        }
        open={openDrawer}
        onClose={() => {
          setOpenDrawer(false);
        }}
      >
        {openDrawer === "chat" ? (
          <ChatNotification />
        ) : openDrawer === "mailing" ? (
          <MailingNotification />
        ) : (
          <p>Voip config</p>
        )}
      </SphereDrawer>
      <Table
        bordered
        scroll={{ x: 750, y: height - 170 }} // height of the header of the layout
        rowKey="key"
        columns={columns}
        dataSource={modules}
        pagination={false}
      />
    </div>
  );
};

export default NotificationsManagement;
