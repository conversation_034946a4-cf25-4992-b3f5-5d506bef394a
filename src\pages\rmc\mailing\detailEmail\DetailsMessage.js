import React, {
  useEffect,
  useState,
  useRef,
  useMemo,
  useCallback,
} from "react";
import {
  Card,
  Space,
  Dropdown,
  Button,
  Row,
  Col,
  Tooltip,
  Form,
  Select,
  Collapse,
  Avatar,
  Skeleton,
  List,
  Divider,
  Typography,
  Modal,
  Input,
  Spin,
  Checkbox,
} from "antd";

import dayjs from "dayjs";
import {
  CloseCircleOutlined,
  ArrowLeftOutlined,
  LikeOutlined,
  MessageOutlined,
  StarOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
} from "@ant-design/icons";
import { useLocation, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";

import { Quill } from "react-quill";
import ImageResize from "quill-image-resize-module-react";
import "react-quill/dist/quill.snow.css";
import quillEmoji from "react-quill-emoji";
import "react-quill-emoji/dist/quill-emoji.css";
import { <PERSON>OutlineHistory, AiOutlineSetting } from "react-icons/ai";

import Editor from "../editor/Editor";

import MainService from "../../../../services/main.service";
import isArray from "lodash/isArray";
import { toastNotification } from "../../../../components/ToastNotification";

import FormCreate from "../../../clients&users/components/FormCreate";
import CreateTask from "../../../voip/components/CreateTask";

import { useSelector } from "react-redux";
import { KEYWORD_MAILING_REGEX } from "../../../../utils/regex";

import {
  setNumberEmailThread,
  setOpenEditor,
} from "../../../../new-redux/actions/mail.actions";
import { useDispatch } from "react-redux";
import { URL_ENV } from "index";

import parse from "html-react-parser";
import FormUpdate from "pages/clients&users/components/FormUpdate";
import Log from "../components/Log";
import Assign from "../Assign";
import Affectation from "../Affectation";
import Identification from "../identification";
import Transfer from "../Transfer";
import Qualification from "../Qualification";
import StateEmail from "../stateEmail";
import useDebounce from "pages/components/UseDebounce/UseDebounce";
import Chrono from "../chrono";
import { formatDateForDisplay } from "pages/tasks/helpers/formatDateToDisplay";
import MarkDown from "../Markdown";
import { BsChevronExpand } from "react-icons/bs";

import EmailBody from "./EmailBody";
import DragFileField from "pages/clients&users/components/special_fields/DragFileField";
import { refactorDataAccounts } from "../ModalMessage";

const DetailsMessage = ({
  messageDetails,
  loadingDetails,
  getDetailsMessageInbox,
  detailsMail,
  setDetailsMail,
  page,
  setPage,
  detailsMeta,
  accountId360,
  type,
  idEmail360,
  setDetailState,
}) => {
  //
  const dispatch = useDispatch();
  const location = useLocation();

  const [loading, setLoading] = useState({ state: false, type: null, key: 0 });
  const [loadingAction, setLoadingAction] = useState({
    state: false,
    type: null,
  });

  const [form] = Form.useForm();

  const { id, accountId } = useParams();
  const [senderMail, setSenderMail] = useState(null);
  const [addLineCc, setAddLineCc] = useState(false);
  const [addLineBcc, setAddLineBcc] = useState(false);
  const [value, setValue] = useState("");
  const [previousBody, setPreviousBody] = useState("");

  const [openForm, setOpenForm] = useState(false);
  const [familyId, setFamilyId] = useState(null);

  const [receiverMail, setReceiverMail] = useState([]);

  const [CcMail, setCcMail] = useState([]);
  const [BccMail, setBCcMail] = useState([]);

  const [showFields, setShowFields] = useState(false);
  const [openTask, setOpenTask] = useState(false);
  const [messageError, setMessageError] = useState("");

  const [fileList, setFileList] = useState([]);

  const [dataSearchEmail, setDataSearchEmail] = useState([]);
  const [loadingSearchEmail, setLoadingSearchEmail] = useState(false);

  const [dataFolder, setDataFolder] = useState([]);
  const [dataSignature, setDataSignature] = useState([]);
  const [signature, setSignature] = useState("");
  const [statutFolder, setStatutFolder] = useState("");
  const [showModalVerifTemplate, setShowModalVerifTemplate] = useState(false);
  const [templateResponse, setTemplateResponse] = useState("");
  const [subject, setSubject] = useState("");
  const { Text, Link } = Typography;
  const [dataTags, setDataTags] = useState([]);
  const [mailingProps, setMailingProps] = useState({
    label: "",
    email: "",
    idEmail: "",
    id: "",
  });
  const [selectedThread, setSelectedThread] = useState({});
  const [openFormUpdate, setOpenFormUpdate] = useState(false);
  const [openLogDrawer, setOpenLogDrawer] = useState(null);
  const [attachments, setAttachments] = useState([]);
  const [searchEmailSelect, setSearchEmailSelect] = useState("");
  const [isHovered, setIsHovered] = useState(false);
  const [detailsCollapseMail, setDetailsCollapseMail] = useState([]);
  const [detailsCollapseMeta, setDetailsCollapseMeta] = useState([]);
  const [loadingCollapseDetails, setLoadingCollapseDetails] = useState(false);
  const [attachFilesExists, setAttachFilesExists] = useState(true);

  const messageDetailsRef = useRef({ data: null, lastData: null });
  const DebounceSearchEmail = useDebounce(searchEmailSelect, 500);
  const { user } = useSelector(({ user }) => user);
  const access = user.access || {};

  const items = [
    {
      key: "options",
      type: "group",

      label: "Pick your Signature",
      children: [
        {
          label: "Aucune",
          value: "",
          style: { margin: "2px" },
          key: "",
        },
        ...dataSignature?.map((el) => ({
          label: el?.label,
          value: el?.value,
          key: el?.value,
          style: { margin: "2px" },
        })),
      ],
    },
  ];

  const conditionActions = (message) => {
    if (
      message?.owner?.owner &&
      message?.transfert &&
      message?.owner?.owner === user?.id &&
      message?.transfert?.account_id === String(usedAccount?.value)
    ) {
      return true;
    } else if (
      !message?.owner?.owner &&
      message?.transfert &&
      message?.transfert?.account_id === String(usedAccount?.value)
    ) {
      return true;
    } else if (
      message?.owner?.owner &&
      !message?.transfert &&
      message?.owner?.owner === user?.id &&
      message?.owner?.user_id === user?.id
    ) {
      return true;
    } else if (!message?.owner?.owner && !message?.transfert) {
      return true;
    } else if (
      message?.owner?.owner &&
      !message?.transfert &&
      message?.owner?.owner === user?.id &&
      message?.owner?.user_id !== user?.id
    ) {
      return true;
    } else {
      // verifier

      return false;
    }
    return false;
  };

  const validateEmailsRequired = (rule, value, callback) => {
    // Email regex pattern
    const regex =
      /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    if (!value || value.length === 0) {
      callback(t("mailing.ErrorMail"));
    } else if (typeof value === "string" && !regex.test(value)) {
      callback(t("mailing.ValidMail"));
      return;
    } else if (isArray(value)) {
      for (let i = 0; i < value.length; i++) {
        if (!regex.test(value[i])) {
          callback(t("mailing.ValidMail"));
          return;
        }
      }
      callback();
    } else {
      return Promise.resolve();
    }
  };

  const validateEmailsCC = (rule, value, callback) => {
    if (!value || value.length === 0) {
      callback();
      return;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    for (const email of value) {
      if (!emailRegex.test(email)) {
        callback("Please enter valid email addresses");
        return;
      }
    }

    callback();
  };

  const ref = useRef(null);
  const navigate = useNavigate();
  const [t] = useTranslation("common");

  const url_string = window.location.href;
  const url = new URL(url_string);
  const folderMailing = url.pathname.split("/")[3];

  const isToday = require("dayjs/plugin/isToday");
  dayjs.extend(isToday);

  Quill.register("modules/imageResize", ImageResize);
  Quill.register(
    {
      "formats/emoji": quillEmoji.EmojiBlot,
      "modules/emoji-toolbar": quillEmoji.ToolbarEmoji,
      "modules/emoji-textarea": quillEmoji.TextAreaEmoji,
      "modules/emoji-shortname": quillEmoji.ShortNameEmoji,
    },
    true
  );

  const { dataAccounts, numberEmailThread, openEditor } = useSelector(
    (state) => state.mailReducer
  );

  const usedAccount = useMemo(
    () => dataAccounts?.find((item) => item.selected === true),
    [dataAccounts]
  );

  // function createMarkup(text, type, shortPath) {
  //   let sanitizedHtml = DOMPurify.sanitize(text, {
  //     ALLOWED_TAGS: [],
  //     ALLOWED_ATTR: [],
  //     USE_PROFILES: { html: true },
  //   });

  //   // regex to get the img tags
  //   const imgTags = sanitizedHtml.match(/<img[^>]*>/g);
  //   if (imgTags) {
  //     imgTags.forEach((imgTag) => {
  //       // regex to get the img alt attribute

  //       const alt = imgTag.match(/alt="(.*?)"/);
  //       if (alt && alt[1]) {
  //         const altText = alt[1];
  //         // regex to get the img src attribute

  //         const srcMatch = imgTag.match(/src="(.*?)"/);
  //         if (srcMatch && srcMatch[1]) {
  //           // if (type === "sent") {
  //           //   const src = srcMatch[1].replace(/[^/]*$/, altText);
  //           //   sanitizedHtml = sanitizedHtml.replace(srcMatch[1], src);
  //           // }
  //           //  else {
  //           const src = srcMatch[1].replace(
  //             /[^/]*$/,
  //             URL_ENV?.REACT_APP_BASE_URL + shortPath + altText
  //           );

  //           sanitizedHtml = sanitizedHtml.replace(srcMatch[1], src);
  //           // }
  //         }
  //       }
  //     });
  //   }
  //   if (type === "sent") {
  //     return sanitizedHtml;
  //   } else return { __html: sanitizedHtml };
  // }

  const updateUrlId = (newId) => {
    const newUrl = `/mailing/${usedAccount?.value}/inbox/${newId}`;
    window.history.replaceState(null, "", newUrl);
  };

  const ReplyMail = async () => {
    setLoadingAction({ state: true, type: "sendReply" });
    var formData = new FormData();

    senderMail?.accountLabel &&
      formData.append("from", senderMail?.accountLabel);
    formData.append("account_id", accountId ?? accountId360);
    formData.append(
      "newMessageReply",
      `<div className='newMessageReply'>${value.concat(signature)}</div>`
    );
    formData.append(
      "previousMessageReply",
      // `<img src="https://miro.medium.com/v2/resize:fit:1200/1*y6C4nSvy2Woe0m7bWEn4BA.png"/>`
      `<div className='previousMessageReply'>${previousBody}</div>`
    );
    formData.append("subject", subject);
    !!selectedThread?.attachments?.length &&
      formData.append("attachFilesExists", attachFilesExists ? 1 : 0);
    formData.append("paramLog", 1);
    formData.append("inReplyTo", "");
    formData.append("id", type === "360" ? idEmail360 : id);
    if (typeof receiverMail === "string") {
      formData.append("receiver[]", receiverMail);
    } else {
      for (let i = 0; i < receiverMail.length; i++) {
        formData.append("receiver[]", receiverMail[i]);
      }
    }

    for (let i = 0; i < CcMail.length; i++) {
      formData.append("cc[]", CcMail[i]);
    }

    for (let i = 0; i < BccMail.length; i++) {
      formData.append("bcc[]", BccMail[i]);
    }

    fileList.map((file) =>
      formData.append("attachments[]", file.originFileObj)
    );
    attachments.forEach((item) =>
      // formData.append(
      //   "attachment_links[]",
      //    item.shortPath
      // )
      formData.append("filename[]", item.filename)
    );

    try {
      const response = await MainService.ReplyMail(formData);
      if (response.status === 200) {
        dispatch(setOpenEditor({ state: false, type: "" }));
        setLoadingAction({ state: false, type: "" });
        getDetailsMessageInbox(response.data.data);
        dispatch(setNumberEmailThread(numberEmailThread + 1));
        // setDetailsCollapseMeta(...detailsCollapseMeta,detailsCollapseMeta.total=detailsCollapseMeta.total + 1);
        if (detailsCollapseMail.length > 0) {
          setDetailsCollapseMeta((prevState) => ({
            ...prevState,
            total: prevState.total + 1,
          }));
        }
        updateUrlId(response.data.data);
        setSenderMail(null);
        // getDetailsThreadsCollapse();
        toastNotification("success", t("mailing.messageSucces"), "topRight", 3);
      }
    } catch (error) {
      setLoadingAction({ state: false, type: "" });
      toastNotification(
        "error",
        error?.response?.data
          ? error?.response?.data?.message
          : t("toasts.errorFetchApi"),
        "topRight",
        4
      );
      console.log("err", error);
    }
  };

  const ForwardMail = async () => {
    setLoadingAction({ state: true, type: "sendForward" });
    var formData = new FormData();

    senderMail?.accountLabel &&
      formData.append("from", senderMail?.accountLabel);
    formData.append("account_id", accountId ?? accountId360);
    // formData.append("subject", "FWD: " + messageDetails?.primary?.subject);
    formData.append("subject", "FWD: " + subject);
    formData.append("previousMessageReply", value);
    !!selectedThread?.attachments?.length &&
      formData.append("attachFilesExists", attachFilesExists ? 1 : 0);

    // formData.append("body", value);
    formData.append("paramLog", 2);
    formData.append("id", type === "360" ? idEmail360 : id);
    // formData.append("attachment_links", URL_ENV?.REACT_APP_BASE_URL + item.shortPath + item.filename);

    attachments.forEach((item) =>
      // formData.append(
      //   "attachment_links[]",
      //    item.shortPath
      // )
      formData.append("filename[]", item.filename)
    );

    for (let i = 0; i < receiverMail.length; i++) {
      formData.append("receiver[]", receiverMail[i]);
    }

    for (let i = 0; i < CcMail.length; i++) {
      formData.append("cc[]", CcMail[i]);
    }

    for (let i = 0; i < BccMail.length; i++) {
      formData.append("cci[]", BccMail[i]);
    }

    fileList.map((file) =>
      formData.append("attachments[]", file.originFileObj)
    );

    try {
      const response = await MainService.ForwardMail(formData);
      if (response.status === 200) {
        dispatch(setOpenEditor({ state: false, type: "" }));
        setLoadingAction({ state: false, type: "" });
        getDetailsMessageInbox();
        setReceiverMail([]);
        setCcMail([]);
        setBCcMail([]);
        setValue("");
        setAddLineCc(false);
        setAddLineBcc(false);
        setSenderMail(null);
        toastNotification("success", t("mailing.messageSucces"), "topRight", 3);
      }
    } catch (error) {
      toastNotification(
        "error",
        error?.response?.data
          ? error?.response?.data?.message
          : t("toasts.errorFetchApi"),
        "topRight",
        4
      );
      setLoadingAction({ state: false, type: "" });
      console.log("err", error);
    }
  };

  const onFinish = (values) => {
    if (value.length === 1) {
      setMessageError(t("mailing.ErrorMsg"));
    } else {
      if (
        openEditor.type === "reply" ||
        openEditor.type === "replyFirstMessage" ||
        openEditor.type === "replyAllFirstMessage" ||
        openEditor.type === "replyAll"
      ) {
        ReplyMail();
      } else if (
        openEditor.type === "Forward" ||
        openEditor.type === "ForwardFirstMessage"
      ) {
        ForwardMail();
      }
    }
  };

  const onFinishFailed = (errorInfo) => {
    console.log("Failed:", errorInfo);
  };

  const handleChange = ({ fileList }) => setFileList([...fileList]);

  const { Panel } = Collapse;

  useEffect(() => {
    if (messageDetails?.data && messageDetails?.data.length > 0) {
      const firstId = messageDetails?.data[0].id;
      const lastId = messageDetails?.data[messageDetails.data.length - 1].id;

      // setMessageDetailsData(firstId);
      // setMessageDetailsLastData(lastId);

      // Update ref
      messageDetailsRef.current = { data: firstId, lastData: lastId };
    }
  }, [messageDetails?.data, numberEmailThread]);

  useEffect(() => {
    if (
      messageDetails?.data &&
      messageDetails?.data.length > 0 &&
      numberEmailThread <= 5
    ) {
      setDetailsCollapseMail({
        ...messageDetails,
        data: messageDetails.data.slice(1, -1),
      });
    }
  }, [messageDetails?.data]);

  const isLoadingRef = useRef(false);

  //
  const getDetailsThreadsCollapse = useCallback(
    async (limit, type, pageActions) => {
      if (isLoadingRef.current) return; // Prevent duplicate fetches
      isLoadingRef.current = true; // Set loading

      var formData = new FormData();
      formData.append(
        "id",
        type === "360" ? idEmail360 : messageDetailsRef.current.lastData
      );
      formData.append("accountId", accountId ?? accountId360);
      formData.append("folderMailing", folderMailing);
      formData.append("limit", limit ?? "5");
      formData.append("first_id_email", messageDetailsRef.current.data); // Use ref for latest value
      formData.append("last_id_email", messageDetailsRef.current.lastData); // Use ref for latest value

      try {
        setLoadingCollapseDetails(true);

        const response = await MainService.getDetailsInbox(
          pageActions ?? page,
          formData
        );

        if (response.status === 200) {
          // const newData = response?.data?.data || [];
          // const existingData = Array.isArray(detailsCollapseMail.data)
          //   ? detailsCollapseMail.data
          //   : [];
          // const uniqueData = [
          //   ...existingData,
          //   ...newData.filter(
          //     (item) => !existingData.some((existing) => existing.id === item.id)
          //   ), // Filter out duplicates
          // ];

          // setDetailsCollapseMail({
          //   data: uniqueData,
          //   links: response?.data?.links,
          //   meta: response?.data?.meta,
          // });
          if (!type) {
            setDetailsCollapseMail({
              data: response?.data?.data,
              links: response?.data?.links,
              meta: response?.data?.meta,
            });
          } else {
            setDetailsCollapseMail((prev) => ({
              data: [
                ...(Array.isArray(prev?.data) ? prev?.data : []),
                ...response?.data?.data,
              ],
              links: response?.data?.links,
              meta: response?.data?.meta,
            }));
          }

          setDetailsCollapseMeta(response?.data?.meta);
          setLoadingCollapseDetails(false);
        }
      } catch (error) {
        setLoadingCollapseDetails(false);
        console.log(error);
      } finally {
        setLoadingCollapseDetails(false);
        isLoadingRef.current = false; // Reset loading state
      }
    },
    [id, accountId, page, dispatch, detailsCollapseMail.data]
  );

  const searchEmail = async () => {
    setLoadingSearchEmail(true);
    var formData = new FormData();
    formData.append("search", DebounceSearchEmail);
    formData.append("accountId", usedAccount?.value);
    try {
      const response = await MainService.searchEmail(formData);
      if (response.status === 200) {
        setDataSearchEmail(
          response.data.address.map((item) => ({
            label: item,
            value: item,
          }))
        );
        setLoadingSearchEmail(false);
      }
    } catch (error) {
      setLoadingSearchEmail(false);
    }
  };

  const getTags = useCallback(async () => {
    try {
      const response = await MainService.getTags();
      if (response?.status === 200) {
        const data =
          response?.data?.map((tag) => ({
            id: tag?.typetask
              ? `${tag?.id}-${tag?.typetask?.id}`
              : `${tag?.id}`,
            label: tag?.label,
            color: tag?.color,
            icon: tag?.icon,
            taskType: tag?.typetask ? true : false,
          })) || [];
        setDataTags(data);
      }
    } catch (error) {
      console.log(error);
    }
  }, []);

  useEffect(() => {
    if (DebounceSearchEmail.length > 0) searchEmail();
  }, [DebounceSearchEmail]);

  useEffect(() => {
    dispatch(setOpenEditor({ state: false, type: "" }));
    getTags();
    return () => {
      dispatch(setOpenEditor({ state: false, type: "" }));
    };
  }, []);

  useEffect(() => {
    if (openEditor && openEditor?.state) {
      setTimeout(() => {
        ref.current?.scrollIntoView({
          block: "end",
          behavior: "smooth",
        });
        setLoading({ state: false, type: "" });
        // setLoadingFirstMessage({ state: false, type: "" });
      }, 600);
    }
  }, [openEditor, loading?.state]);

  const downloadFileAtUrl = (url) => {
    fetch(url)
      .then((response) => response.blob())
      .then((blob) => {
        const blobUrl = window.URL.createObjectURL(new Blob([blob]));
        const fileName = url.split("/").pop();
        const aTag = document.createElement("a");
        aTag.href = blobUrl;
        aTag.setAttribute("download", fileName);
        document.body.appendChild(aTag);
        aTag.click();
        aTag.remove();
      });
  };

  useEffect(() => {
    form.setFieldsValue({
      Receiver: receiverMail,
      cc: CcMail,
      cci: BccMail,
      subject: subject,
    });
  }, [form, receiverMail, openEditor, CcMail, BccMail, subject]);

  const listData = Array.from({
    length: 3,
  }).map((_, i) => ({
    href: "https://ant.design",
    title: `ant design part ${i + 1}`,
    avatar: `https://xsgames.co/randomusers/avatar.php?g=pixel&key=${i}`,
    description:
      "Ant Design, a design language for background applications, is refined by Ant UED Team.",
    content:
      "We supply a series of design principles, practical patterns and high quality design resources (Sketch and Axure), to help people create their product prototypes beautifully and efficiently.",
  }));

  const IconText = ({ icon, text }) => (
    <>
      {React.createElement(icon, {
        style: {
          marginRight: 8,
        },
      })}
      {text}
    </>
  );

  const getFolderByFamily = async () => {
    if (statutFolder === "fetched") return;
    try {
      setStatutFolder("loading");
      const response = await MainService.folderByFamily();
      if (response.status === 200) {
        setStatutFolder("fetched");
        let array = [...response.data.data];
        array = array
          .filter((item) => Object.values(item)[0].length > 0)
          .map((item) => ({
            label: Object.keys(item)[0],
            options: Object.values(item)[0].map((el) => ({
              label: el.title,
              value: el.template_id + "/" + el.family_id,
            })),
          }));
        setDataFolder([{ label: "Aucune", value: "" }, ...array]);
      }
    } catch (error) {
      setStatutFolder("error");
      console.log("error", error);
    }
  };

  const getTemplate = async (dataFolderByFamily) => {
    var formData = new FormData();
    formData.append("template_id", dataFolderByFamily.split("/")[0]);
    formData.append("email", receiverMail[0]);
    formData.append("family_id", dataFolderByFamily.split("/")[1]);

    try {
      const response = await MainService.getTemplate(formData);
      if (response.status === 200) {
        setShowModalVerifTemplate(true);
        setTemplateResponse(response.data);
      }
    } catch (error) {
      toastNotification(
        "error",
        error?.response?.data
          ? error?.response?.data?.message
          : t("toasts.errorFetchApi"),
        "topRight",
        4
      );
    }
  };

  const handleAddTemplate = () => {
    setValue((p) => p.concat(templateResponse));
    setShowModalVerifTemplate(false);
  };

  const getSignature = async () => {
    try {
      const response = await MainService.getSignature(
        accountId ?? accountId360
      );
      if (response.status === 200) {
        setDataSignature(
          response.data.data.map((item) => ({
            label: item.label,
            value: item.value,
          }))
        );

        // setSignature(
        //   response.data.data.filter((item) => item.default === true)[0].value
        // );
      }
    } catch (error) {
      console.log("error", error);
    }
  };

  const getDefaultSignature = async () => {
    try {
      const response = await MainService.getDefaultSignature(
        accountId ?? accountId360
      );
      if (response.status === 200) {
        setSignature(response?.data.data ?? "");
      }
    } catch (error) {
      console.log("error", error);
    }
  };

  // Header de chaque message dans le détail du mail
  const renderHeader = (item, i) => (
    <>
      <Row
        style={{
          display: "flex",
          justifyContent: "space-between",
        }}
      >
        <Row
          style={{
            display: "flex",
            alignItems: "center",
            // width: "78%",
          }}
        >
          <Col>
            <div className="mr-2 cursor-help items-center">
              <Tooltip
                title={
                  item.box === 0 || item.box === 2
                    ? t("mailing.NewMsg.received")
                    : t("mailing.NewMsg.sent")
                }
              >
                {item.box === 0 || item.box === 2 ? (
                  <ArrowDownOutlined style={{ color: "green", fontSize: 14 }} />
                ) : (
                  <ArrowUpOutlined style={{ color: "blue", fontSize: 14 }} />
                )}
              </Tooltip>
            </div>
          </Col>
          <Col>
            {item?.identification?.avatar ? (
              <Avatar
                style={{
                  marginRight: "10px",
                  marginBottom: "4px",
                }}
                size={30}
                src={
                  <img
                    src={`${
                      URL_ENV?.REACT_APP_BASE_URL +
                      URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                    }${item?.identification?.avatar}`}
                    alt="name"
                  />
                }
              />
            ) : (
              <Avatar
                style={{
                  backgroundColor: "#f56a00",
                }}
                size={34}
              >
                {item?.from?.name
                  ? item?.from?.name?.charAt(0)
                  : item?.from?.address?.charAt(0)}
              </Avatar>
            )}
          </Col>
          <Col style={{ marginLeft: "8px" }}>
            <Row>
              <Tooltip
                placement="bottom"
                title={
                  <div>
                    <div className="flex space-x-1.5">
                      <p style={{ fontWeight: "bold" }}>
                        {t("mailing.NewMsg.from")}
                      </p>{" "}
                      <p>{item?.from?.address}</p>
                    </div>

                    <div className="flex space-x-1.5">
                      <p style={{ fontWeight: "bold" }}>
                        {t("mailing.NewMsg.To")}
                      </p>{" "}
                      <div>
                        {item.to?.map((item) => (
                          <p> {item?.address}</p>
                        ))}
                      </div>
                    </div>

                    {item?.cc?.length > 0 ? (
                      <div className="flex space-x-1.5">
                        <p style={{ fontWeight: "bold" }}>Cc</p>{" "}
                        <div>
                          {item.cc.map((item) => (
                            <p>{item?.address}</p>
                          ))}
                        </div>
                      </div>
                    ) : null}

                    {item.cci && item.cci.length > 0 ? (
                      <div className="flex space-x-1.5">
                        <p style={{ fontWeight: "bold" }}>Cci</p>{" "}
                        <div>
                          {item.cci.map((item) => (
                            <p>{item?.address}</p>
                          ))}
                        </div>
                      </div>
                    ) : null}

                    <div className="flex space-x-1.5">
                      <p style={{ fontWeight: "bold" }}>
                        {t("mailing.Inbox.date")}
                      </p>{" "}
                      <p>{item?.date}</p>
                    </div>
                    <div className="flex space-x-1.5">
                      <p style={{ fontWeight: "bold" }}>
                        {t("mailing.signed")}
                      </p>{" "}
                      <p>{item?.from?.address.split("@")[1]}</p>
                    </div>
                  </div>
                }
              >
                <p
                  style={{
                    fontWeight: "bold",
                    marginTop: "5px",
                    fontSize: "14px ",
                    color: "black",
                  }}
                >
                  {item?.from?.name ? item?.from?.name : item?.from?.address}
                </p>
              </Tooltip>
              {usedAccount?.shared == 1 && item.box === 0 && (
                <StateEmail
                  idEmail={item.id}
                  usedAccount={usedAccount}
                  state={item.state}
                  getMails={getDetailsMessageInbox}
                  type="inbox"
                  disabled={conditionActions(item) ? false : true}
                />
              )}
            </Row>
            <p className="text-gray-600">
              {t("mailing.NewMsg.To")}{" "}
              {item?.to?.name ? item.to[0]?.name : item?.to[0]?.address}
            </p>
          </Col>
        </Row>

        <Row>
          <div>
            <div className="mt-[8px] flex items-center space-x-1">
              <p>
                {formatDateForDisplay(
                  item.date,
                  `${user?.location?.date_format} ${user?.location?.time_format}`,
                  user,
                  t,
                  "mailing"
                )}
              </p>
              {item.status_processing_duration !== null ? (
                <Chrono
                  chronoNow={item.chrono_now}
                  expireTime={item.expire_Processing_Time}
                  processDuration={item?.status_processing_duration}
                />
              ) : null}
            </div>
            <div className="flex items-center space-x-0.5">
              {folderMailing === "inbox" ? (
                <div onClick={(e) => e.stopPropagation()}>
                  <Identification
                    id={type === "360" ? idEmail360 : id}
                    // fromName={messageDetails?.data[i]?.from?.name}
                    // fromEmail={messageDetails?.data[i]?.from?.address}
                    fromName={item?.from?.name}
                    fromEmail={item?.from?.address}
                    idEmail={type === "360" ? idEmail360 : id}
                    identification={item?.identification}
                    setDetailsMail={setDetailsMail}
                    // identification={messageDetails?.data[i]?.identification}
                    // setDetailsMail={setDetailsMail}
                    usedAccount={usedAccount}
                    t={t}
                    user={user}
                    access={access}
                    // owner={
                    //   messageDetails?.data[messageDetails?.data?.length - 1]
                    //     ?.owner
                    // }
                    // transfert={messageDetails[i]?.transfert}
                    owner={item.owner}
                    transfert={item?.transfert}
                    getDetailsMessageInbox={getDetailsMessageInbox}
                    getDetailsThreadsCollapse={getDetailsThreadsCollapse}
                    pageDetailsThread={page}
                    type="details"
                    idThread={item.id}
                  />
                </div>
              ) : null}
              {
                <div onClick={(e) => e.stopPropagation()}>
                  <Qualification
                    tags={item?.tags}
                    id={type === "360" ? idEmail360 : id}
                    idThread={item.id}
                    type="details"
                    owner={item?.owner}
                    transfert={item?.transfert}
                    data={dataTags}
                    // setDataSource={setDataMailInbox}
                    // setOpenTask={setOpenTask}
                    usedAccount={usedAccount}
                    getDetailsMessageInbox={getDetailsMessageInbox}
                    getDetailsThreadsCollapse={getDetailsThreadsCollapse}
                    pageDetailsThread={page}
                  />
                </div>
              }
              {folderMailing === "inbox" ? (
                <Transfer
                  idThread={item.id}
                  idEmail={type === "360" ? idEmail360 : id}
                  accountId={usedAccount?.value}
                  dataRow={item}
                  typeFrom="details"
                  setDetailsMail={setDetailsMail}
                />
              ) : null}
            </div>
          </div>
        </Row>
      </Row>
      <Row>
        <span
          style={{
            marginLeft: "42px",
            fontWeight: "500",
            color: "black",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-evenly",
          }}
        >
          {item?.subject ? (
            <div className="flex items-center space-x-1">
              <span>{t("mailing.Inbox.subject") + ":"}</span>
              <MarkDown type="details">{item?.subject}</MarkDown>
            </div>
          ) : (
            t("mailing.noSubject")
          )}
        </span>
      </Row>
    </>
  );

  useEffect(() => {
    getSignature();
    getDefaultSignature();
  }, []);

  useEffect(() => {
    if (receiverMail?.length > 0) getFolderByFamily();
  }, [receiverMail]);

  useEffect(() => {
    if (page > 0) {
      getDetailsThreadsCollapse("5", true);
    }
  }, [getDetailsThreadsCollapse, page]);
  //
  const handleValueChange = (value) => {
    setValue(value);
    form.setFieldValue("message", value);
  };

  return (
    <div>
      <>
        <div className="flex items-center justify-between">
          <div
            className="p-3"
            style={{
              fontWeight: "bold",
              marginTop: "10px",
              marginLeft: "10px",
              display: "flex",
              alignItems: "center",
            }}
          >
            <Tooltip placement="top" title={t("mailing.backReception")}>
              <Button
                type="text"
                icon={<ArrowLeftOutlined />}
                onClick={() => {
                  const url = new URL(window.location.href);
                  dispatch(setOpenEditor({ state: false, type: "" }));
                  if (type === "360") {
                    setDetailState(false);
                  } else {
                    navigate(
                      location.state?.path ??
                        `/mailing/${accountId}/${
                          url.pathname.match(KEYWORD_MAILING_REGEX)?.[1]
                        }`,
                      { state: location.state, history: true }
                    );
                  }
                }}
              ></Button>
            </Tooltip>

            {messageDetails?.data?.length > 0 && (
              <h1
                style={{
                  fontWeight: "bold",
                  marginLeft: "10px",
                  whiteSpace: "normal", // Allows wrapping
                  overflow: "hidden", // Hide any overflow beyond two lines
                }}
              >
                <MarkDown type="title">
                  {messageDetails?.data[messageDetails?.data?.length - 1]
                    ?.subject ?? t("mailing.noSubject")}
                </MarkDown>
              </h1>
            )}
          </div>

          <div className=" mr-[48px] mt-[20px] flex items-center">
            <Tooltip placement="bottom" title={t("mailing.Historique")}>
              <Button
                type="text"
                icon={
                  <AiOutlineHistory
                    style={{ fontSize: "25px", color: "#6d6d6d" }}
                  />
                }
                onClick={() => {
                  setOpenLogDrawer(id);
                }}
              ></Button>
            </Tooltip>
          </div>
        </div>
        {!loadingDetails && (
          <div className="flex items-center justify-between">
            <div className="ml-[20px] flex ">
              {!loadingDetails &&
              usedAccount?.shared == 1 &&
              folderMailing === "inbox" ? (
                <Assign
                  idEmail={type === "360" ? idEmail360 : id}
                  type="details"
                  owner={
                    messageDetails?.data[messageDetails?.data?.length - 1]
                      ?.owner
                  }
                  transfert={
                    messageDetails?.data[messageDetails?.data?.length - 1]
                      ?.transfert
                  }
                  detailsMail={detailsMail}
                  setDetailsMail={setDetailsMail}
                  getDetailsMessageInbox={getDetailsMessageInbox}
                  // circleDelete={true}
                />
              ) : null}
              {!loadingDetails &&
              /*conditionActions(
                messageDetails?.data[messageDetails?.data?.length - 1]
              ) &&*/
              (folderMailing === "inbox" || folderMailing === "sent") ? (
                <div className=" ml-[6px] flex items-center space-x-2">
                  {usedAccount?.shared == 1 ? (
                    <>
                      <Affectation
                        id={type === "360" ? idEmail360 : id}
                        owner={
                          messageDetails?.data[messageDetails?.data?.length - 1]
                            ?.owner
                        }
                        transfert={
                          messageDetails?.data[messageDetails?.data?.length - 1]
                            ?.transfert
                        }
                        affectation={
                          messageDetails?.data[messageDetails?.data?.length - 1]
                            .affectation
                        }
                        type="details"
                        t={t}
                        access={access}
                        user={user}
                        setDetailsMail={setDetailsMail}
                      />
                    </>
                  ) : null}
                </div>
              ) : null}
            </div>
          </div>
        )}

        <br />
        <Space
          direction="vertical"
          size="middle"
          style={{
            display: "flex",
            marginLeft: "10px",
            marginRight: "10px",
          }}
        >
          {messageDetails?.data && (
            <Collapse
              defaultActiveKey={[
                messageDetails?.data[messageDetails?.data.length - 1]?.id,
                // messageDetails?.data[0]?.id,
              ]}
              style={{ overflow: "auto", width: "100%", marginBottom: "5px" }}
            >
              {/* First Reply */}
              <Panel
                showArrow={false}
                header={renderHeader(messageDetails?.data[0], 0)}
                key={messageDetails?.data[0]?.id}
                // collapsible={true}
                // defaultActiveKey={["1"]}
              >
                <EmailBody
                  item={messageDetails?.data[0]}
                  i={0}
                  messageDetails={messageDetails}
                  loadingDetails={loadingDetails}
                  loading={loading}
                  conditionActions={conditionActions}
                  setSubject={setSubject}
                  setReceiverMail={setReceiverMail}
                  setPreviousBody={setPreviousBody}
                  setAddLineBcc={setAddLineBcc}
                  setAddLineCc={setAddLineCc}
                  setBCcMail={setBCcMail}
                  setCcMail={setCcMail}
                  setLoading={setLoading}
                  setValue={setValue}
                  setAttachments={setAttachments}
                  getDetailsMessageInbox={getDetailsMessageInbox}
                  setSelectedThread={setSelectedThread}
                />
              </Panel>

              {/* Hidden Replies - Toggle */}
              {detailsCollapseMail &&
                detailsCollapseMail?.data?.length > 0 &&
                detailsCollapseMail?.data?.map((item, i) => (
                  <>
                    <Panel
                      showArrow={false}
                      header={renderHeader(item, i)}
                      key={item.id}
                    >
                      <EmailBody
                        item={item}
                        i={i}
                        messageDetails={detailsCollapseMail}
                        loadingDetails={loadingDetails}
                        loading={loading}
                        conditionActions={conditionActions}
                        setSubject={setSubject}
                        setReceiverMail={setReceiverMail}
                        setPreviousBody={setPreviousBody}
                        setAddLineBcc={setAddLineBcc}
                        setAddLineCc={setAddLineCc}
                        setBCcMail={setBCcMail}
                        setCcMail={setCcMail}
                        setLoading={setLoading}
                        setValue={setValue}
                        setAttachments={setAttachments}
                        getDetailsMessageInbox={getDetailsMessageInbox}
                        setSelectedThread={setSelectedThread}
                      />
                    </Panel>
                  </>
                ))}

              {((detailsCollapseMail?.length === 0 && numberEmailThread > 5) ||
                (detailsCollapseMail?.data?.length > 0 &&
                  detailsCollapseMeta?.current_page !==
                    detailsCollapseMeta?.last_page &&
                  detailsCollapseMeta?.total - detailsCollapseMeta?.to !==
                    0)) && (
                <div style={{ margin: "-15px 0 0 5px" }}>
                  <Tooltip title={t("mailing.openOtherMessages")}>
                    <Button
                      shape="circle"
                      onMouseEnter={() => setIsHovered(true)}
                      onMouseLeave={() => setIsHovered(false)}
                      onClick={() => {
                        setPage((prev) => prev + 1);
                        setIsHovered(false);
                      }}
                      style={{
                        textAlign: "center",
                        display: "inline-flex",
                        justifyContent: "center",
                        alignItems: "center",
                        padding: "0", // Ensure padding doesn't affect centering
                      }}
                    >
                      {loadingCollapseDetails ? (
                        <Spin
                          size="small"
                          style={{ display: "flex", justifyContent: "center" }}
                        />
                      ) : isHovered ? (
                        <BsChevronExpand />
                      ) : detailsCollapseMeta.length === 0 ? (
                        numberEmailThread - 2
                      ) : detailsCollapseMeta?.current_page !==
                        detailsCollapseMeta?.last_page ? (
                        detailsCollapseMeta?.total - detailsCollapseMeta?.to
                      ) : (
                        detailsCollapseMeta?.total - detailsCollapseMeta?.to
                      )}
                    </Button>
                  </Tooltip>
                </div>
              )}

              {/* Last Reply */}
              {messageDetails?.data?.length > 1 && (
                <Panel
                  showArrow={false}
                  header={renderHeader(
                    messageDetails?.data[messageDetails?.data?.length - 1],
                    messageDetails?.data?.length - 1
                  )}
                  key={
                    messageDetails?.data[messageDetails?.data?.length - 1]?.id
                  }
                  collapsible="disabled"
                >
                  <EmailBody
                    item={
                      messageDetails?.data[messageDetails?.data?.length - 1]
                    }
                    i={messageDetails?.data?.length - 1}
                    messageDetails={messageDetails}
                    loadingDetails={loadingDetails}
                    loading={loading}
                    conditionActions={conditionActions}
                    setSubject={setSubject}
                    setReceiverMail={setReceiverMail}
                    setPreviousBody={setPreviousBody}
                    setAddLineBcc={setAddLineBcc}
                    setAddLineCc={setAddLineCc}
                    setBCcMail={setBCcMail}
                    setCcMail={setCcMail}
                    setLoading={setLoading}
                    setValue={setValue}
                    setAttachments={setAttachments}
                    getDetailsMessageInbox={getDetailsMessageInbox}
                    setSelectedThread={setSelectedThread}
                  />
                </Panel>
              )}
            </Collapse>
          )}
        </Space>

        <Modal
          open={showModalVerifTemplate}
          title={t("mailing.addTemplate")}
          onCancel={() => setShowModalVerifTemplate(false)}
          footer={[
            <div className=" flex  items-center justify-end space-x-2">
              <Button
                key="back"
                onClick={() => setShowModalVerifTemplate(false)}
              >
                {t("mailing.NewMsg.Cancel")}
              </Button>
              <Button key="submit" type="primary" onClick={handleAddTemplate}>
                {t("mailing.accept")}
              </Button>
            </div>,
          ]}
        >
          <p>
            {t("mailing.actionTemplate")}
            {t("mailing.confirmAction")}
          </p>
        </Modal>

        {/* Reply,replyALL,forward message */}
        {openEditor && openEditor?.state ? (
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <Card
              style={{
                width: "100%",
                marginLeft: "10px",
                marginRight: "10px",
                marginTop: "8px",
              }}
            >
              <Form
                name="basic"
                layout="horizontal"
                labelCol={{
                  span: 3,
                }}
                onFinish={onFinish}
                onFinishFailed={onFinishFailed}
                form={form}
                scrollToFirstError={true}
                id="email-form"
                colon={false}
                labelWrap
              >
                {!!usedAccount?.alias?.length && (
                  <Form.Item
                    label={t("mailing.NewMsg.from")}
                    // initialValue={
                    //   sender
                    //     ? sender
                    //     : usedAccount?.value && parseInt(usedAccount?.value)
                    // }
                    initialValue={usedAccount?.value}
                    name="sender"
                  >
                    <Select
                      onChange={(value, option) => {
                        // console.log({ value, option });
                        setSenderMail(option);
                        // getDefaultSignature(value);
                        getSignature(option?.accountId || option?.value);
                        setSignature(option?.signature || null);
                      }}
                      options={refactorDataAccounts([usedAccount])}
                      // value={senderMail ? senderMail : usedAccount?.value}
                    />
                  </Form.Item>
                )}

                <Row style={{ marginLeft: 12 }}>
                  <Col span={21}>
                    <Form.Item
                      label={t("mailing.NewMsg.To")}
                      name="Receiver"
                      rules={[
                        { required: true, validator: validateEmailsRequired },
                      ]}
                    >
                      <Select
                        loading={loadingSearchEmail}
                        mode="tags"
                        notFoundContent=""
                        style={{
                          width: "94%",
                          marginLeft: "4px",
                        }}
                        placeholder={t("mailing.NewMsg.placeholderMail")}
                        value={receiverMail}
                        onChange={(e) => {
                          setReceiverMail(e);
                          setDataSearchEmail([]);
                        }}
                        options={
                          dataSearchEmail.length > 0 ? dataSearchEmail : []
                        }
                        onSearch={(e) => setSearchEmailSelect(e)}
                      />
                    </Form.Item>
                  </Col>

                  {!addLineCc && (
                    <Col
                      span={1}
                      style={{ marginTop: "3px", marginLeft: "7px" }}
                    >
                      <span
                        className="cursor-pointer  hover:underline"
                        onClick={() => setAddLineCc(!addLineCc)}
                      >
                        Cc
                      </span>
                    </Col>
                  )}
                  {!addLineBcc && (
                    <Col span={1} style={{ marginTop: "3px" }}>
                      <span
                        className="cursor-pointer  hover:underline"
                        onClick={() => setAddLineBcc(!addLineBcc)}
                      >
                        Bcc
                      </span>
                    </Col>
                  )}
                </Row>
                {addLineCc || CcMail.length > 0 ? (
                  <>
                    <Row style={{ marginLeft: "7px" }}>
                      <Col span={22}>
                        <Form.Item
                          label="Cc"
                          name="cc"
                          rules={[
                            { required: false, validator: validateEmailsCC },
                          ]}
                        >
                          <Select
                            loading={loadingSearchEmail}
                            mode="tags"
                            value={CcMail}
                            notFoundContent=""
                            placeholder={t("mailing.NewMsg.placeholderMail")}
                            onChange={(e) => {
                              setCcMail(e);
                              setDataSearchEmail([]);
                            }}
                            options={
                              dataSearchEmail.length > 0 ? dataSearchEmail : []
                            }
                            onSearch={(e) => setSearchEmailSelect(e)}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={2}>
                        <Button
                          style={{ color: "red" }}
                          type="link"
                          shape="circle"
                          icon={<CloseCircleOutlined />}
                          onClick={() => {
                            setAddLineCc(!addLineCc);
                            setCcMail([]);
                          }}
                        ></Button>
                      </Col>
                    </Row>
                  </>
                ) : null}
                {addLineBcc || BccMail.length > 0 ? (
                  <>
                    <Row style={{ marginLeft: "7px" }}>
                      <Col span={22}>
                        <Form.Item
                          label="Cci"
                          name="cci"
                          rules={[
                            { required: false, validator: validateEmailsCC },
                          ]}
                        >
                          <Select
                            loading={loadingSearchEmail}
                            mode="tags"
                            notFoundContent=""
                            placeholder={t("mailing.NewMsg.placeholderMail")}
                            value={BccMail}
                            onChange={(e) => {
                              setBCcMail(e);
                              setDataSearchEmail([]);
                            }}
                            options={
                              dataSearchEmail.length > 0 ? dataSearchEmail : []
                            }
                            onSearch={(e) => setSearchEmailSelect(e)}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={2}>
                        <Button
                          type="link"
                          shape="circle"
                          style={{ color: "red" }}
                          icon={<CloseCircleOutlined />}
                          onClick={() => {
                            setAddLineBcc([]);
                            setAddLineBcc(!addLineBcc);
                          }}
                        ></Button>
                      </Col>
                    </Row>
                  </>
                ) : null}

                <Form.Item
                  label={t("mailing.NewMsg.object")}
                  name="subject"
                  required={true}
                  rules={[
                    { required: true, message: t("mailing.ErrorObject") },
                  ]}
                >
                  <Input
                    value={subject}
                    onChange={(e) => setSubject(e.target.value)}
                  />
                </Form.Item>
                <Row style={{ marginLeft: 12 }}>
                  <Col span={22}>
                    <Form.Item
                      name="Template"
                      tooltip={t("mailing.selectReceiver")}
                      label={t("menu2.emailTemplates")}
                    >
                      <Select
                        disabled={receiverMail.length === 0}
                        onChange={(e) => {
                          if (e.length > 0) getTemplate(e);
                        }}
                        defaultValue={{ label: "Aucune", value: "" }}
                        options={dataFolder || [{ label: "Aucune", value: "" }]}
                        // style={{ width: "180px" }}
                      />
                    </Form.Item>
                  </Col>

                  <Col span={1} style={{ marginTop: "2px", marginLeft: "7px" }}>
                    <Tooltip placement="top" title={t("mailing.emailTemplate")}>
                      <Button
                        type="text"
                        danger
                        icon={
                          <AiOutlineSetting
                            style={{
                              height: "20px",
                              width: "20px",
                              cursor: "pointer",
                            }}
                          />
                        }
                        onClick={() => {
                          navigate("/settings/emailTemplates");
                        }}
                      />
                    </Tooltip>
                  </Col>
                </Row>

                {!!selectedThread?.attachments?.length && (
                  <Form.Item
                    name="attachFilesExists"
                    wrapperCol={{ offset: 3 }}
                  >
                    <Checkbox
                      checked={attachFilesExists}
                      onChange={() => setAttachFilesExists((p) => !p)}
                    >
                      {t("mailing.includeAttachments")}
                    </Checkbox>
                  </Form.Item>
                )}
                <Form.Item
                  label={t("mailing.NewMsg.message")}
                  name="message"
                  rules={[{ required: true, message: t("mailing.ErrorMsg") }]}
                >
                  <Editor
                    showFields={showFields}
                    setShowFields={setShowFields}
                    valueEditor={value}
                    setValueEditor={handleValueChange}
                  />
                </Form.Item>
                {messageError.length > 0 ? (
                  <p className="ml-16 text-red-600">{t("mailing.ErrorMsg")}</p>
                ) : null}

                <Form.Item label={t("mailing.Attachments")} name="uploadFile">
                  <Tooltip title={t("mailing.NewMsg.file")}>
                    {/* <Upload
                      beforeUpload={() => false}
                      multiple={true}
                      onChange={handleChange}
                      listType="picture"
                      defaultFileList={[...fileList]}
                      className="upload-list-inline"
                    >
                      <Button className="  px-3" icon={<UploadOutlined />}>
                        {t("mailing.uploadFile")}
                      </Button>
                    </Upload> */}
                    <DragFileField
                      t={t}
                      onChange={handleChange}
                      fileList={[...fileList]}
                      className="upload-list-inline"
                    />
                  </Tooltip>
                </Form.Item>
                {signature && (
                  <>
                    <Divider className="my-1 py-1" />
                    <Form.Item label="Signature">
                      <div className="min-h-[30px]  rounded-md border  border-dashed border-gray-300  p-1 text-start">
                        {/* <p>--</p> */}
                        {parse(signature)}{" "}
                      </div>
                    </Form.Item>
                  </>
                )}
                <div
                  className=" flex flex-nowrap items-center gap-x-1 text-xs "
                  style={{ marginLeft: "58px" }}
                >
                  <Text type="secondary" className="">
                    Your signature will be inculded in the email body. If you
                    want to change it,
                  </Text>

                  <Dropdown
                    disabled={dataSignature.length === 0}
                    trigger={["click"]}
                    // overlayClassName="w-80 space-y-1"
                    placement="topRight"
                    menu={{
                      selectable: true,
                      selectedKeys: signature,
                      items,
                      className: "bg-red-500",

                      onClick: (e) => {
                        setSignature(e.key);
                      },
                    }}
                  >
                    <Link>Click here</Link>
                  </Dropdown>
                </div>

                <Form.Item>
                  <div className=" flex flex-col">
                    <Divider />

                    <div className=" flex w-full items-center justify-end">
                      <div className="flex items-center space-x-2">
                        <Button
                          onClick={() => {
                            dispatch(setOpenEditor({ state: false, type: "" }));
                            setAddLineCc(false);
                            setAddLineBcc(false);
                            setCcMail([]);
                            setBCcMail([]);
                            setAttachFilesExists(true);
                          }}
                        >
                          {t("mailing.NewMsg.Cancel")}
                        </Button>

                        <Button
                          htmlType="submit"
                          key="submit"
                          form="email-form"
                          loading={
                            loadingAction?.state
                            // loading.type === "sendForward" ||
                            // loading.type === "sendReply" ||
                            // loading.type === "sendReplyAll"
                          }
                          type="primary"
                        >
                          {t("mailing.NewMsg.send")}
                        </Button>
                      </div>
                    </div>
                  </div>
                </Form.Item>
              </Form>
            </Card>
          </div>
        ) : null}

        <div ref={ref} />
      </>

      {/* Loading */}

      {loadingDetails && (
        <List
          itemLayout="vertical"
          size="large"
          dataSource={listData}
          renderItem={(item) => (
            <List.Item
              key={item.title}
              actions={
                !loading
                  ? [
                      <IconText
                        icon={StarOutlined}
                        text="156"
                        key="list-vertical-star-o"
                      />,
                      <IconText
                        icon={LikeOutlined}
                        text="156"
                        key="list-vertical-like-o"
                      />,
                      <IconText
                        icon={MessageOutlined}
                        text="2"
                        key="list-vertical-message"
                      />,
                    ]
                  : undefined
              }
              extra={
                !loadingDetails && (
                  <img
                    width={272}
                    alt="logo"
                    src="https://gw.alipayobjects.com/zos/rmsportal/mqaQswcyDLcXyDKnZfES.png"
                  />
                )
              }
            >
              <Skeleton loading={loadingDetails} active avatar>
                <List.Item.Meta
                  avatar={<Avatar src={item.avatar} />}
                  title={<a href={item.href}>{item.title}</a>}
                  description={item.description}
                />
                {item.content}
              </Skeleton>
            </List.Item>
          )}
        />
      )}

      <FormCreate
        open={openForm}
        setOpen={setOpenForm}
        familyId={familyId}
        mailingProps={mailingProps}
      />

      <FormUpdate
        open={openFormUpdate}
        setOpen={setOpenFormUpdate}
        familyId={familyId}
        elementDetails={mailingProps}
      />

      <CreateTask
        open={openTask}
        setOpen={setOpenTask}
        mask={false}
        source="mailing"
        mailingProps={mailingProps}
      />

      {openLogDrawer !== null ? (
        <Log
          openLogDrawer={openLogDrawer}
          setOpenLogDrawer={setOpenLogDrawer}
          thirdid={
            messageDetails.data[messageDetails?.data?.length - 1].third_id
          }
          accountId360={accountId360}
          // setLoading={setLoading}
        />
      ) : null}
    </div>
  );
};

export default DetailsMessage;
