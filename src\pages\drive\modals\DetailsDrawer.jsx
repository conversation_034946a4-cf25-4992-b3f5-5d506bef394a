import React from "react";
import { Drawer, Spin, Tag, Typography, Card, Space, Divider } from "antd";
import { useQuery } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { getItemDrive, getSharedDriveItem } from "../../../services/main.service";
import {
  FolderFilled,
  UserOutlined,
  CalendarOutlined,
  FileOutlined,
  DatabaseOutlined,
  InfoCircleOutlined,
  FileTextOutlined,
  CloudOutlined,
  FolderOpenOutlined,
} from "@ant-design/icons";
import { humanDate } from "../../voip/helpers/helpersFunc";
import DisplayAvatar from "../../voip/components/DisplayAvatar";
import { URL_ENV } from "index";

const { Title, Text } = Typography;

const DetailsDrawer = ({ isVisible, onClose, itemId, getIcon }) => {
  const [t] = useTranslation("common");

  const { data: itemDetails, isLoading, isError } = useQuery({
    queryKey: ["drive-item-details",isVisible, itemId],
    queryFn: () => getItemDrive(itemId),
    enabled: !!itemId && isVisible,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    retry: 1,
  });


  const item = itemDetails?.data;

  const DetailItem = ({ icon, label, value, valueType = "text" }) => (
    <div className="flex items-center justify-between py-3 px-4 hover:bg-gray-50 rounded-lg transition-colors">
      <div className="flex items-center gap-3">
        <div className="text-gray-500 text-lg">{icon}</div>
        <Text className="text-gray-700 font-medium">{label}</Text>
      </div>
      <div className="flex-1 text-right">
        {valueType === "tag" ? (
          <Tag color={value.color || "default"}>{value.text}</Tag>
        ) : valueType === "user" ? (
          <div className="flex items-center justify-end gap-2">
            <DisplayAvatar
              name={value.name}
              size={24}
              urlImg={value.avatar}
            />
            <Text className="font-medium">{value.name}</Text>
          </div>
        ) : valueType === "code" ? (
          <code className="bg-gray-100 px-2 py-1 rounded text-xs text-gray-800">
            {value}
          </code>
        ) : (
          <Text className="text-gray-900 font-medium">{value}</Text>
        )}
      </div>
    </div>
  );

  const SectionCard = ({ title, icon, children }) => (
    <Card className="mb-4 shadow-sm border-0" bodyStyle={{ padding: 0 }}>
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-4 py-3 border-b">
        <div className="flex items-center gap-2">
          <div className="text-blue-600">{icon}</div>
          <Title level={5} className="mb-0 text-gray-800">{title}</Title>
        </div>
      </div>
      <div className="divide-y divide-gray-100">
        {children}
      </div>
    </Card>
  );

  return (
    <Drawer
      title={
        <div className="flex items-center gap-3 py-2">
       
          <div>
            <Title level={4} className="mb-0 text-gray-800">
              {t("drive.details")}
            </Title>
          
          </div>
        </div>
      }
      placement="right"
      onClose={onClose}
      open={isVisible}
      width={520}
      className="details-drawer"
      styles={{
        body: { padding: "24px", backgroundColor: "#fafafa" },
        header: { borderBottom: "1px solid #e5e7eb", backgroundColor: "white" }
      }}
    >
      <Spin spinning={isLoading}>
        {isError ? (
          <div className="text-center py-12">
            <div className="mb-4">
              <InfoCircleOutlined style={{ fontSize: "64px", color: "#ef4444" }} />
            </div>
            <Title level={4} type="danger">
              {t("drive.failedToLoadDetails")}
            </Title>
            <Text type="secondary">
              {t("drive.errorLoadingItemDetails")}
            </Text>
          </div>
        ) : item ? (
          <div className="space-y-6">
            {/* Item Header Card */}
            <Card className="shadow-sm border-0 bg-white">
              <div className="flex items-center gap-4 p-2">
                <div className="p-3 bg-gray-50 rounded-xl">
                  {getIcon(item, "48px")}
                </div>
                <div className="flex-1">
                  <Title level={3} className="mb-1 text-gray-800">
                    {item.name}
                  </Title>
                  <div className="flex items-center gap-3">
                    <Tag 
                      color={item.type === "folder" ? "blue" : "green"}
                      className="rounded-full px-3 py-1"
                    >
                      {item.type === "folder" ? (
                        <><FolderOpenOutlined className="mr-1" />{t("drive.folder")}</>
                      ) : (
                        <><FileTextOutlined className="mr-1" />{t("drive.file")}</>
                      )}
                    </Tag>
                    {item.type === "file" && item.extension && (
                      <Tag color="orange" className="rounded-full px-3 py-1">
                        {item.extension.toUpperCase()}
                      </Tag>
                    )}
                  </div>
                </div>
              </div>
            </Card>

            {/* Basic Information */}
            <SectionCard 
              title={t("drive.basicInformation")} 
              icon={<FileOutlined />}
            >
              <DetailItem
                icon={<FileTextOutlined />}
                label={t("drive.name")}
                value={item.name}
              />
              <DetailItem
                icon={<CloudOutlined />}
                label={t("drive.type")}
                value={{
                  text: item.type === "folder" ? t("drive.folder") : t("drive.file"),
                  color: item.type === "folder" ? "blue" : "green"
                }}
                valueType="tag"
              />
              {item.type === "file" && item.extension && (
                <DetailItem
                  icon={<FileOutlined />}
                  label={t("drive.extension")}
                  value={{
                    text: item.extension.toUpperCase(),
                    color: "orange"
                  }}
                  valueType="tag"
                />
              )}
              {item.type === "file" && item.size && (
                <DetailItem
                  icon={<DatabaseOutlined />}
                  label={t("drive.size")}
                  value={item.size}
                />
              )}
                 <DetailItem
                icon={<UserOutlined />}
                label={t("drive.owner")}
                value={{
                  name: item.created_by?.label,
                  avatar: item.created_by?.avatar &&
                    URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                    item.created_by?.avatar
                }}
                valueType="user"
              />
                <DetailItem
                icon={<CalendarOutlined />}
                label={t("drive.createdAt")}
                value={humanDate(item.created_at, t)}
              />
              <DetailItem
                icon={<CalendarOutlined />}
                label={t("drive.updatedAt")}
                value={humanDate(item.updated_at, t)}
              />
            </SectionCard>

        

          
       
          </div>
        ) : null}
      </Spin>
    </Drawer>
  );
};

export default DetailsDrawer; 