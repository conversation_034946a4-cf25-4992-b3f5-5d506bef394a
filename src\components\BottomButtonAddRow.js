import { PlusCircleOutlined } from "@ant-design/icons";
import { Button } from "antd";
import React from "react";
import { useTranslation } from "react-i18next";

const BottomButtonAddRow = ({
  loading,
  editingKey,
  handleAdd,
  data,
  text,
  search,
  pagination = true,
}) => {
  return (
    // marginTop: Math.ceil(data.length / pageSize) > 1 ? "-52px" : 0
    // <div
    //   style={{
    //     width: "0",
    //     paddingLeft: "15px",
    //     marginTop:
    //       data?.filter((el) => el.id).length > 10 && pagination ? "-52px" : 0,
    //   }}
    // >
    <div className="inline-flex justify-start">
      <Button
        icon={<PlusCircleOutlined />}
        disabled={loading ? true : editingKey ? true : search ? true : false}
        onClick={handleAdd}
        className="cursor-pointer"
        type="link"
        block
      >
        {text}
      </Button>
    </div>
    // </div>
  );
};

export default BottomButtonAddRow;
