import { Fragment, useCallback, useEffect, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { useLocation } from "react-router-dom";
import { useDispatch } from "react-redux";
import { Avatar, Button, Input, Popover, Space, Table, Tooltip } from "antd";
import { FiSearch } from "react-icons/fi";
import { MessageOutlined, PhoneOutlined } from "@ant-design/icons";
import { useWindowSize } from "../../clients&users/components/WindowSize";
import { toastNotification } from "../../../components/ToastNotification";
import { useTranslation } from "react-i18next";
import useActionCall from "../helpers/ActionCall";
import { getGroups } from "../services/services";
import { HighlightSearchW } from "../components";
import DisplayAvatar from "../components/DisplayAvatar";

import { openDrawerChat } from "new-redux/actions/voip.actions/handleDrawerChat";

export const getLettersAvatarGroup = (str) => {
  if (!str) return "";
  const newStr = str.replaceAll("_", " ");
  const words = newStr.split(" ");

  if (words.length === 0) return "";

  if (words.length === 1) {
    return words[0].charAt(0);
  } else {
    return words
      .slice(0, 3)
      .map((word) => word.charAt(0))
      .join("");
  }
};
//
const MemberDetails = ({ member, poste, location, onCall }) => {
  const dispatch = useDispatch();
  const [t] = useTranslation("common");
  const { name, extension, uuid, image, family_id, member_id } = member;
  const displayName = name.replace("_", " ");
  const isCurrentPoste = `${extension}` === `${poste}`;
  const canMessage = uuid && !isCurrentPoste && location.pathname !== "/chat";

  return (
    <Space size={3}>
      <DisplayAvatar name={name} urlImg={image} size={32} />
      <div className="flex flex-col">
        <p className="font-semibold">
          {isCurrentPoste ? t("voip.me") : displayName}
        </p>
        <div className="flex items-center space-x-2">
          <p className="leading-4 text-slate-500">{extension}</p>
          {!isCurrentPoste && (
            <div className="flex items-center">
              <Button
                disabled={!canMessage}
                onClick={() => dispatch(openDrawerChat(uuid))}
                size="small"
                type="link"
                icon={<MessageOutlined style={{ fontSize: 14 }} />}
              />
              <Button
                onClick={() => onCall(extension, member_id, family_id)}
                disabled={isCurrentPoste}
                size="small"
                type="link"
                icon={<PhoneOutlined rotate={100} style={{ fontSize: 14 }} />}
              />
            </div>
          )}
        </div>
      </div>
    </Space>
  );
};
//
export const RenderMembers = ({
  members,
  call,
  poste,

  maxCount = 10,
  avatarSize = 30,
  popoverTrigger = "click",
}) => {
  const location = useLocation();

  return (
    <Avatar.Group
      maxCount={maxCount}
      maxStyle={{
        backgroundColor: "rgb(30, 64, 175)",
        color: "rgb(219, 234, 254)",
        width: avatarSize,
        height: avatarSize,
        // fontWeight: 600,
        cursor: "pointer",
        display: "flex",
      }}
      // max={{
      //   count: maxCount,
      //   popover: popoverTrigger,
      //   style: {
      //     backgroundColor: "rgb(30, 64, 175)",
      //     color: "rgb(219, 234, 254)",
      //     width: avatarSize,
      //     height: avatarSize,
      //     fontWeight: 600,
      //     cursor: "pointer",
      //     display: "flex",
      //   },
      // }}
    >
      {members.map((member, index) => (
        <Popover
          key={index}
          trigger={popoverTrigger}
          content={
            <MemberDetails
              member={member}
              poste={poste}
              onCall={call}
              location={location}
            />
          }
        >
          <Fragment key={index}>
            <DisplayAvatar
              key={index}
              name={member?.name}
              urlImg={member?.image}
              size={avatarSize}
              cursor={"help"}
            />
          </Fragment>
        </Popover>
      ))}
    </Avatar.Group>
  );
};

const Groups = () => {
  //
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const call = useActionCall();
  const tableRef = useRef();
  const windowSize = useWindowSize();
  const { user } = useSelector(({ user }) => user);
  const poste = `${user?.extension}`;
  //
  const [dataSource, setDataSource] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [isLoadingData, setIsLoadingData] = useState(true);

  const [search, setSearch] = useState("");
  const [displaySearch, setDisplaySearch] = useState("");

  //
  //   console.log(dataSource);
  //
  const fetchGroups = useCallback(async () => {
    try {
      setIsLoadingData(true);
      const { data } = await getGroups();
      setDataSource(data.map((item, i) => ({ key: i, ...item })));
    } catch (err) {
      toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? err.message : err);
    } finally {
      setIsLoadingData(false);
    }
  }, [t]);

  useEffect(() => {
    fetchGroups();
  }, [fetchGroups]);
  //
  useEffect(() => {
    if (!search.length) {
      setFilteredData(dataSource);
    } else {
      setIsLoadingData(true);
      const timer = setTimeout(() => {
        setFilteredData(
          dataSource.filter(
            (item) =>
              item?.name?.toLowerCase()?.includes(search) ||
              item?.extension?.toLowerCase()?.includes(search)
          )
        );
        setIsLoadingData(false);
        clearTimeout(timer);
      }, 1000);
    }
  }, [dataSource, search]);
  //
  const handleOpenMgsDrawer = (uuid) => {
    dispatch(openDrawerChat(uuid));
  };
  //
  const columns = [
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      render: (name, { extension, is_member }, index) => (
        <div className="flex flex-row justify-between" key={index}>
          <Space>
            <DisplayAvatar size={40} isGroup={true} name={name} />
            <div className="">
              <p className="font-semibold leading-5">
                {HighlightSearchW(name, search)}
              </p>
              <Tooltip title={t("voip.clickToCall")}>
                <p
                  onClick={() => call(extension)}
                  className="cursor-pointer leading-4 text-slate-500 hover:text-slate-400"
                >
                  {HighlightSearchW(extension, search)}
                </p>
              </Tooltip>
            </div>
          </Space>
          <Tooltip title={is_member ? t("voip.alreadyMember") : null}>
            <Button
              type="link"
              disabled={is_member}
              onClick={() => !is_member && call(extension)}
              icon={<PhoneOutlined rotate={100} style={{ fontSize: 18 }} />}
            />
          </Tooltip>
        </div>
      ),
    },
    {
      title: "Members",
      dataIndex: "members",
      key: "members",
      render: (members, _, index) => (
        <RenderMembers
          key={index}
          members={members}
          call={call}
          poste={poste}
          handleOpenMsg={handleOpenMgsDrawer}
        />
      ),
    },
  ];
  //
  const handleSearch = (event) => {
    const inputValue = event?.target?.value;
    setDisplaySearch(inputValue);
    setSearch(inputValue.trim().toLowerCase());
  };
  //
  return (
    <div className="relative w-full space-y-6 pt-4">
      <div className="flex w-full justify-between px-6">
        {/* <div className="flex flex-row space-x-2"> */}
        <Input
          style={{ width: "300px" }}
          disabled={!dataSource?.length && !search?.length}
          allowClear
          placeholder={t("voip.searchGroup")}
          value={displaySearch}
          onChange={handleSearch}
          prefix={<FiSearch className="h-4 w-4 text-slate-400" />}
          // suffix={
          //   <Tooltip title={t("voip.search3chart")}>
          //     <InfoCircleTwoTone
          //       style={{
          //         fontSize: 14,
          //         cursor: "help",
          //       }}
          //     />
          //   </Tooltip>
          // }
        />

        {/* <div className="flex flex-row space-x-2">
        <FilterCallLogs
          setFilterState={setFilterCalls}
          filterState={filterCalls}
          t={t}
          disabled={!dataSource?.length}
        />
        {filterCalls?.length ? (
          <Button type="link" onClick={() => setFilterCalls([])}>
            {t("tags.reset")}
          </Button>
        ) : (
          ""
        )}
      </div> */}
        {/* </div> */}
      </div>
      <div className="table-view">
        <Table
          ref={tableRef}
          columns={columns}
          dataSource={filteredData}
          loading={isLoadingData}
          size="small"
          //   pagination={
          //     totalLogs <= 10
          //       ? false
          //       : {
          //           showTotal: (total, range) => handleShowTotal(total, range),
          //           showSizeChanger: true,
          //           showQuickJumper: true,
          //           total: totalLogs - 1,
          //           pageSize: limit,
          //           current: page,
          //           onChange: (page) => setPage(page),
          //           onShowSizeChange: (current, size) => setLimit(size),
          //           pageSizeOptions:
          //             totalLogs && handlePageSizeOptions(totalLogs),
          //           size: "small",
          //         }
          //   }
          scroll={{
            y: windowSize?.height - 320,
            // x: 1400,
          }}
        />
      </div>

      {/* <FormCreate
    open={openDrawerCreate}
    setOpen={setOpenDrawerCreate}
    familyId={familyToAdd}
    setCatchChange={setCatchChange}
    nbrPhoneFromVoip={nbrPhoneToAdd}
  />
  <DisplayElementInfo
    open={openDrawerInfo}
    setOpen={setOpenDrawerInfo}
    elementDetails={elementDetails}
  />
  <CreateTask
    open={openTask}
    setOpen={setOpenTask}
    mask={false}
    idCall={idCallForTask}
    source={"call"}
  /> */}
    </div>
  );
};

export default Groups;
