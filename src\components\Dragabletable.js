import React, { useCallback, useRef, useState, useEffect } from "react";
import update from "immutability-helper";
import { useDrag, useDrop } from "react-dnd";
import { PencilSquareIcon, TrashIcon } from "@heroicons/react/24/outline";
import { Tag, Table, Popconfirm, Button, message, Switch } from "antd";
import { useSelector, useDispatch } from "react-redux";
import { useParams } from "react-router-dom";
import {
  StopOutlined,
  FormOutlined,
  DeleteOutlined,
  AlertFilled,
} from "@ant-design/icons";

import MainService from "../services/main.service";
import TooltipPopover from "../components/Tooltip";
import AlertModal from "../components/AlertModal";
import { toastNotification } from "../components/ToastNotification";
// import { reset } from "../redux/fieldsSlice";
import { DeleteSpecificField } from "../new-redux/actions/fields.actions/deleteSpecificField";
import { displayRightIcon } from "../utils/displayIcon";
import { RESET_STATE } from "../new-redux/constants";
import { useTranslation } from "react-i18next";

const type = "DraggableBodyRow";

/**
 *
 * @param {*} param0
 * @returns Fields management table.
 */

const DraggableTable = ({
  setAddNewFieldModal,
  setTitleConfig,
  setUpdateFieldProps,
  searchQuery,
  setVisibility,
  setRequiredField,
  setUniqueValue,
  familyId,
}) => {
  const { data, fields, isSuccess, isLoading, isOptDeleted, isFieldRemoved } =
    useSelector((state) => state.fields);

  console.log("fields", fields);

  const [typesList, setTypesList] = useState();
  const [newPosition, setNewPosition] = useState(null);
  const [oldPosition, setOldPosition] = useState(null);
  const [draggedField, setDraggedField] = useState(null);
  const [deleteModal, setDeleteModal] = useState(false);
  const [fieldToDelete, setFieldToDelete] = useState(null);
  const [sourceComponent, setSourceComponent] = useState(null);
  const [datas, setDatas] = useState(null);
  const [ranksArray, setRanksArray] = useState(null);

  const deleteBtnRef = useRef();
  const dispatch = useDispatch();
  const { id } = useParams();
  const { types } = useSelector((state) => state.types);

  // The drag and drop configuration and implementation.(https://react-dnd.github.io/react-dnd/docs/overview)
  const DraggableBodyRow = ({
    index,
    moveRow,
    className,
    style,
    ...restProps
  }) => {
    const ref = useRef(null);
    const [{ isOver, dropClassName }, drop] = useDrop({
      accept: type,
      collect: (monitor) => {
        const { index: dragIndex } = monitor.getItem() || {};
        if (dragIndex === index) {
          return {};
        }
        return {
          isOver: monitor.isOver(),
          dropClassName:
            dragIndex < index ? " drop-over-downward" : " drop-over-upward",
        };
      },
      drop: (item) => {
        moveRow(item.index, index);
        // console.log("datas", datas, "item", item);
        updateRank();
      },
    });
    const [, drag] = useDrag({
      type,
      item: {
        index,
      },
      collect: (monitor) => ({
        isDragging: monitor.isDragging(),
      }),
    });
    drop(drag(ref));
    return (
      <tr
        ref={ref}
        className={`${className}${isOver ? dropClassName : ""}`}
        style={{
          cursor: "move",
          ...style,
        }}
        {...restProps}
        // onClick={() => console.log("onChange", datas, "rks", ranksArray)}
      />
    );
  };

  const moveRow = useCallback(
    (dragIndex, hoverIndex) => {
      setOldPosition(dragIndex);
      setNewPosition(hoverIndex);
      setDraggedField(datas[dragIndex]?.id);
      const dragRow = datas[dragIndex];
      setDatas(
        update(datas, {
          $splice: [
            [dragIndex, 1],
            [hoverIndex, 0, dragRow],
          ],
        })
      );
    },
    [datas]
  );

  const components = {
    body: {
      row: DraggableBodyRow,
    },
  };

  // Update rank api call.
  const updateRank = useCallback(async () => {
    var formData = new FormData();
    ranksArray &&
      ranksArray.forEach((item, i) => {
        for (var variable in item) {
          console.log("itmmm", item);
          let key = `rank[${item?.id}]`;
          let value = i + 1;
          console.log("rankss", `rank[${item?.id}]`, value);
          formData.append(key, value);
        }
      });

    let payload = {
      id,
      formData,
    };
    console.log("rrrrr", ranksArray);
    // try {
    //   const response = await MainService.changeFieldRank(payload);
    //   message.success("Rank was updated successfully", 3);
    //   console.log("res", response);
    // } catch (error) {
    //   console.log("errr", error);
    //   message.error("Something went wrong!", 3);
    // }
  }, [id, ranksArray]);

  // Get new rank to be sent with the API.
  useEffect(() => {
    // console.log("eeeee1", datas);
    let object = {};
    let newArr =
      datas &&
      datas.map((element, i) => {
        return (object = { id: element?.id, index: i + 1 });
      });
    // console.log("eeeee2", newArr);
    setRanksArray(newArr);
  }, [datas]);

  // Refresh data source whenever changed.
  const handleDataSource = useCallback(() => {
    setDatas(fields?.fields);
  }, [fields?.fields]);

  // Handle refresh dataSource side effect.
  useEffect(() => {
    // if (isSuccess === true || isOptDeleted) {
    //   handleDataSource();
    // } else handleDataSource();
    handleDataSource();
  }, [handleDataSource, isSuccess, isOptDeleted]);

  console.log("datas", datas);

  // The actual data that will be displayed.
  const dataSource =
    datas &&
    datas
      .filter((element) =>
        element?.label.toLowerCase().includes(searchQuery.toLowerCase().trim())
      )
      .map((item, i) => ({
        key: item.id,
        rank: i + 1,
        title: item,
        visible: item,
        unique: item,
        required: item,
        type: item,
        actions: item,
      }));

  const showIcons = (id) => {
    // console.log("id", id);
    switch (id) {
      case 1:
        return "#ffa39e";
      case 2:
        return "#ffbb96";
      case 3:
        return "#ffd591";
      case 4:
        return "#ffe58f";
      case 5:
        return "#fffb8f";
      case 6:
        return "#eaff8f";
      case 7:
        return "#b7eb8f";
      case 8:
        return "#87e8de";
      // /************************** */
      case 9:
        return "#91caff";
      case 10:
        return "#adc6ff";
      case 11:
        return "#d3adf7";
      case 13:
        return "#ffadd2";
      case 14:
        return "#d9d9d9";
      case 15:
        return "#40a9ff";
      case 16:
        return "#612500";
      case 17:
        return "#002329";

      default:
        break;
    }
  };

  useEffect(() => {
    if (deleteBtnRef && deleteBtnRef.current) {
      // console.log("ref", deleteBtnRef, deleteBtnRef.current);
      deleteBtnRef.current.focus();
    }
  });

  const updateFieldStatus = async (toggleName, idx) => {
    try {
      await MainService.updateParameters(toggleName, idx);
      message.success("Field updated successfully", 4.5);
      handleDataSource();
    } catch (error) {
      message.error("Something went wrong!!", 4.5);
    }
  };

  const [t] = useTranslation("common");

  // Antd table columns.
  const columns = [
    {
      title: t("table.header.rank"),
      dataIndex: "rank",
      key: "rank",
    },
    {
      title: t("table.header.title"),
      dataIndex: "title",
      key: "title",
      render: (props) => (
        <Button
          type="link"
          onClick={() => {
            setTitleConfig("Update Field");
            setAddNewFieldModal(true);
            setUpdateFieldProps(props);
            // alert("Under dev to fix some bugs!! thanks");
          }}
        >
          {props.label}
        </Button>
      ),
    },
    {
      title: t("table.header.hidden"),
      dataIndex: "visible",
      key: "visible",
      align: "center",
      render: (props) => (
        <Switch
          size="small"
          onChange={() => updateFieldStatus("hidden", props?.id)}
          defaultChecked={props?.hidden}
        />
      ),
      shouldCellUpdate: (record, prevRecord) => {
        // console.log("record", record, "prevRecord", prevRecord);
        return record.hidden !== prevRecord.hidden;
      },
    },
    {
      title: t("table.header.uniqueValue"),
      dataIndex: "unique",
      key: "unique",
      align: "center",
      render: (props) => (
        <Switch
          size="small"
          onChange={() => updateFieldStatus("uniqueValue", props?.id)}
          defaultChecked={props?.uniqueValue}
        />
      ),
      shouldCellUpdate: (record, prevRecord) => {
        // console.log("record", record, "prevRecord", prevRecord);
        return record.uniqueValue !== prevRecord.uniqueValue;
      },
    },
    {
      title: t("table.header.required"),
      dataIndex: "required",
      key: "required",
      align: "center",
      render: (props) => (
        <Switch
          size="small"
          onChange={() => updateFieldStatus("required", props?.id)}
          defaultChecked={props?.required}
        />
      ),
      shouldCellUpdate: (record, prevRecord) => {
        // console.log("record", record, "prevRecord", prevRecord);
        return record.required !== prevRecord.required;
      },
    },
    {
      title: t("table.header.type"),
      dataIndex: "type",
      key: "type",
      align: "center",
      filters:
        types &&
        types
          .slice()
          .sort((a, b) => a.list - b.list)
          .map((element) => ({
            text:
              // <p className="">
              element?.fieldType,
            // displayRightIcon(element?.fieldType, 4, 4)
            // </p>
            value: element?.id,
          })),
      onFilter: (value, record) =>
        record.actions.field_type_id == value.toString(),
      // record.actions.field_type_id.startsWith(value.toString())
      filterSearch: true,
      render: (props) => (
        <div className="flex">
          <Tag>
            {types &&
              types.find((element) => element?.id == props?.field_type_id)
                ?.fieldType}
          </Tag>
        </div>
      ),
    },
    {
      title: t("table.header.actions"),
      dataIndex: "actions",
      key: "actions",
      align: "center",
      render: (props) => (
        <>
          <div className="float-right">
            <Popconfirm
              title="Are you sure?"
              okText="Confirm"
              cancelText="No"
              trigger={["click"]}
              arrowPointAtCenter={true}
              autoAdjustOverflow={true}
              mouseEnterDelay={0}
              onConfirm={() => dispatch(DeleteSpecificField(fieldToDelete))}
            >
              <DeleteOutlined
                className=" text-red-300 hover:text-red-500 cursor-pointer"
                onClick={() => {
                  setFieldToDelete(props?.id);
                }}
                style={{ fontSize: "16px" }}
                ref={deleteBtnRef}
              />
            </Popconfirm>
            <FormOutlined
              className=" text-gray-400 hover:text-gray-600 cursor-pointer"
              onClick={() => {
                setTitleConfig("Update Field");
                setAddNewFieldModal(true);
                setUpdateFieldProps(props);
              }}
              style={{ fontSize: "16px" }}
            />
          </div>
        </>
      ),
    },
  ];

  // Display a toast notification whenever a field is removed.
  useEffect(() => {
    if (isFieldRemoved === true) {
      toastNotification(
        "success",
        "field was deleted successfully",
        "bottomRight",
        3
      );
      setDeleteModal(false);
      let timer = setTimeout(() => {
        dispatch({ type: RESET_STATE });
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [dispatch, isFieldRemoved]);

  // console.log("dddd", ranksArray);

  // Message to be displayed in the table if data is null.
  let empty = {
    emptyText: (
      <span className="flex justify-center items-center">
        <p className="text-gray-400 text-xl">
          <StopOutlined className="ml-10" />
          No field
        </p>
      </span>
    ),
  };

  return (
    <>
      <Table
        columns={columns}
        dataSource={dataSource}
        components={components}
        onRow={(_, index) => {
          const attr = {
            index,
            moveRow,
          };
          return attr;
        }}
        // pagination={false}
        loading={isLoading}
        locale={empty}
        shouldCellUpdate={(record, prevRecord) =>
          console.log("record", record, "prevRecord", prevRecord)
        }
      />
      {/* <AlertModal
        openDeleteModal={deleteModal}
        setOpenDeleteModal={setDeleteModal}
        deleteOptionId={fieldToDelete}
        source={sourceComponent}
        setSourceComponent={setSourceComponent}
      /> */}
    </>
  );
};

export default DraggableTable;
