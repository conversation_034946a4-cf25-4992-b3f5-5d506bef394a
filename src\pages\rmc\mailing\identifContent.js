import { useCallback, useEffect, useMemo, useState } from "react";
import { Avatar, Button, Form, Select, Space } from "antd";
import { HiOutlineBuildingOffice, HiOutlineUserGroup } from "react-icons/hi2";
import { Cg<PERSON><PERSON>lane } from "react-icons/cg";

import { toastNotification } from "components/ToastNotification";
import MainService from "services/main.service";
import useDebounce from "pages/components/UseDebounce/UseDebounce";
import { useSelector } from "react-redux";
import { setRefreshMailInbox } from "new-redux/actions/mail.actions";
import { useDispatch } from "react-redux";
import { URL_ENV } from "index";
import { FiUsers } from "react-icons/fi";

const IdentifContent = ({
  setData,
  t,
  access,
  fromEmail,
  idEmail,
  setOpenPopCon,
  dataMailInbox,
  idModule,
  setIdModule,
  type,
  setDetailsMail,
  openSelect2,
  setOpenSelect2,
  getDetailsMessageInbox,
  idThread,
  getDetailsThreadsCollapse,
  pageDetailsThread,
  identification,
}) => {
  //
  const [form] = Form.useForm();
  const [dataIdentifSelect, setDataIdentifSelect] = useState({
    status: "idle",
    meta: {},
    data: [],
  });
  const [page, setPage] = useState(1);
  const [id, setId] = useState(identification?.id ?? null);
  const [search, setSearch] = useState("");

  const [loadingStore, setLoadingStore] = useState(false);
  const [loadingSelect, setLoadingSelect] = useState(false);
  const [selectedFamily, setSelectedFamily] = useState(
    identification?.family_id ? identification.family_id : null
  );
  const debouncedSearchValue = useDebounce(search, 500);

  const { dataAccounts } = useSelector((state) => state.mailReducer);
  const dispatch = useDispatch();

  const usedAccount = useMemo(
    () => dataAccounts?.find((item) => item.selected === true),
    [dataAccounts]
  );

  const familiesOption = [
    ...(access?.companies === "1"
      ? [
          {
            label: (
              <Space>
                <HiOutlineBuildingOffice
                  style={{ fontSize: "16px", marginTop: "5px" }}
                />
                <p>{t("contacts.companies")}</p>
              </Space>
            ),
            value: 1,
          },
        ]
      : []),
    ...(access?.contact === "1"
      ? [
          {
            label: (
              <Space>
                <HiOutlineUserGroup
                  style={{ fontSize: "16px", marginTop: "5px" }}
                />
                <p>{t("menu1.contacts")}</p>
              </Space>
            ),
            value: 2,
          },
        ]
      : []),
    ...(access?.colleague === "1"
      ? [
          {
            label: (
              <Space>
                <FiUsers style={{ fontSize: "17px", marginTop: "5px" }} />
                <p>{t("contacts.collegues")}</p>
              </Space>
            ),
            value: 4,
          },
        ]
      : []),
    ...(access?.leads === "1"
      ? [
          {
            label: (
              <Space>
                <CgUserlane style={{ fontSize: "16px", marginTop: "5px" }} />
                <p>{t("menu1.leads")}</p>
              </Space>
            ),
            value: 9,
          },
        ]
      : []),
  ];

  const families = {
    1: t("contacts.company"),
    2: t("contacts.contact"),
    4: t("contacts.collegues"),
    9: t("contacts.leads"),
  };

  // console.log({ dataIdentifSelect, identification });

  const getIdentifSelect = useCallback(
    async (e) => {
      setLoadingSelect(true);
      if (search.length > 0) {
        setPage(1);
      }
      try {
        // setDataIdentifSelect({
        //   status: "loading",
        //   data: [],
        // });

        const response = await MainService.getIdentifSelect(
          e ?? selectedFamily,
          fromEmail,
          page,
          search
        );

        if (response?.status === 200) {
          const isIdentExist =
            page === 1 && !search
              ? response?.data?.data?.find((e) => e?.id === identification?.id)
              : false;
          // console.log({ isIdentExist });
          const resp = [
            ...response?.data?.data,
            ...(isIdentExist ? [] : [isIdentExist]),
          ].filter(Boolean);
          setLoadingSelect(false);
          const newData = resp?.map((item) => ({
            label: (
              <div className="flex items-center">
                {item?.avatar?.length > 0 ? (
                  <Avatar
                    style={{
                      // backgroundColor: "#c41d7f",
                      marginRight: "10px",
                    }}
                    size={22}
                    src={
                      <img
                        src={`${
                          URL_ENV?.REACT_APP_BASE_URL +
                          URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                        }${item?.avatar}`}
                        alt="name"
                      />
                    }
                  />
                ) : (
                  <Avatar
                    style={{
                      backgroundColor: "#c41d7f",
                      marginRight: "10px",
                    }}
                    size={22}
                  >
                    {item?.label?.charAt(0)?.toUpperCase()}
                  </Avatar>
                )}
                <div className="w-[75%] ">{item?.label}</div>
              </div>
            ),
            value: item?.id,
            searchOption: item?.label,
          }));
          if (page > 1) {
            setDataIdentifSelect((prevData) => ({
              status: "success",
              meta: response?.data?.meta,
              data: [...prevData.data, ...newData],
            }));
          } else {
            setDataIdentifSelect({
              status: "success",
              meta: response?.data?.meta,
              data: newData,
              // data: newData.find((e) => e?.value === identification?.id)
              //   ? newData
              //   : [
              //       {
              //         searchOption: identification.label_data,
              //         value: identification,
              //       },
              //       ...newData,
              //     ],
            });
          }
        }
      } catch (error) {
        setLoadingSelect(false);
        setDataIdentifSelect((prevData) => ({
          status: "error",
          meta: [],
          data: [...prevData.data],
        }));
        console.log(error);
      }
    },
    [page, debouncedSearchValue, selectedFamily]
  );

  const searchIdentifSelect = useCallback(
    async (e) => {
      try {
        setDataIdentifSelect({
          status: "loading",
          data: [],
        });
        const response = await MainService.searchIdentifSelect(
          e ?? selectedFamily,
          fromEmail,
          search
        );

        if (response?.status === 200) {
          setDataIdentifSelect({
            status: "success",
            meta: response?.data.meta,
            data: response?.data.data.map((item) => ({
              label: (
                <div className="flex">
                  {item?.avatar?.length > 0 ? (
                    <Avatar
                      style={{
                        // backgroundColor: "#c41d7f",
                        marginRight: "10px",
                        marginBottom: "4px",
                      }}
                      size={22}
                      src={
                        <img
                          src={`${
                            URL_ENV?.REACT_APP_BASE_URL +
                            URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                          }${item?.avatar}`}
                          alt="name"
                        />
                      }
                    />
                  ) : (
                    <Avatar
                      style={{
                        backgroundColor: "#c41d7f",
                        marginRight: "10px",
                        marginBottom: "4px",
                      }}
                      size={22}
                    >
                      {item?.label?.charAt(0)?.toUpperCase()}
                    </Avatar>
                  )}
                  <div className="w-[75%] ">{item?.label}</div>
                </div>
              ),
              value: item?.id,
              searchOption: item?.label,
            })),
          });
        }
      } catch (error) {
        setDataIdentifSelect((prev) => ({
          status: "error",
          data: [...prev.data],
        }));
        console.log(error);
      }
    },
    [debouncedSearchValue]
  );

  const StoreIdentifEmail = async () => {
    setLoadingStore(true);
    var formData = new FormData();

    formData.append(
      "emailId",
      type === "inbox" || type === "dropdown" ? idEmail : idThread
    );
    formData.append("familyId", selectedFamily);
    formData.append("account_id", usedAccount?.value);
    formData.append("id", id);
    for (let i = 0; i < usedAccount?.departmentId?.length; i++) {
      formData.append("departement_id[]", usedAccount?.departmentId[i]);
    }

    try {
      const response = await MainService.storeIdentifEmail(formData);
      if (response.status === 200) {
        setLoadingStore(false);
        setOpenPopCon(false);
        dispatch(setRefreshMailInbox(true));
        if (type === "inbox" || type === "dropdown") {
          setData(
            dataMailInbox.map((item) =>
              item.id === idEmail
                ? { ...item, identification: response?.data.data }
                : item
            )
          );
        } else {
          // setDetailsMail((p) => {
          //   let detail = Object.assign({}, p);
          //   detail.data[detail?.data?.length - 1].identification =
          //     response?.data?.data;
          //   return detail;
          // });
          getDetailsMessageInbox();
          getDetailsThreadsCollapse(5 * pageDetailsThread, false, 1);
        }

        toastNotification(
          "success",
          t("mailing.identifiedSuccess"),
          "topRight",
          3
        );
      }
    } catch (error) {
      setLoadingStore(false);
      toastNotification(
        "error",
        error?.response?.data
          ? error?.response?.data?.message
          : t("toasts.errorFetchApi"),
        "topRight",
        4
      );

      console.log("err", error);
    }
  };

  const handlePopupScroll = (e) => {
    if (
      e.target.scrollTop + e.target.offsetHeight >= e.target.scrollHeight - 1 &&
      page < dataIdentifSelect.meta.last_page
    ) {
      setPage((prevPage) => prevPage + 1);
    }
  };

  useEffect(() => {
    if (selectedFamily) getIdentifSelect();
  }, [getIdentifSelect]);

  useEffect(() => {
    form.setFieldsValue({
      module: selectedFamily ?? null,
      searchSelect: id ?? null,
    });
  }, [form, selectedFamily, id]);

  // useEffect(() => {
  //   if (debouncedSearchValue?.length > 0) searchIdentifSelect();
  // }, [searchIdentifSelect]);

  return (
    <div className="w-64 space-y-6 ">
      <Form form={form} layout="vertical" name="form">
        <Form.Item
          label={t("voip.selectModule")}
          name="module"
          rules={[
            {
              required: true,
              message: "",
            },
          ]}
        >
          <Select
            onDropdownVisibleChange={(e) => {
              if (e) setOpenSelect2(!e);
            }}
            style={{ width: "100%" }}
            allowClear
            options={familiesOption}
            value={selectedFamily}
            placeholder={t("voip.selectModule")}
            onChange={(e) => {
              if (e) {
                setSelectedFamily(e);
                setIdModule(e);
                // getIdentifSelect(e);
              }
              setPage(1);
              setDataIdentifSelect({
                status: "loading",
                data: [],
              });

              setId("");
              form.setFieldValue("searchSelect", "");

              if (e) setOpenSelect2(true);
            }}
          />
        </Form.Item>

        <Form.Item
          label={`${t("voip.search_select")} ${
            families?.[selectedFamily] ?? ""
          }`}
          name="searchSelect"
          rules={[
            {
              required: selectedFamily,
              message: "",
            },
          ]}
        >
          <Select
            defaultActiveFirstOption
            loading={loadingSelect}
            open={openSelect2}
            onDropdownVisibleChange={(e) => setOpenSelect2(e)}
            showSearch
            filterOption={false}
            onPopupScroll={handlePopupScroll}
            style={{ width: "100%" }}
            options={
              loadingSelect
                ? [{ label: "Loading...", value: "loading" }]
                : dataIdentifSelect.data
            }
            value={loadingSelect ? "loading" : id}
            onChange={(e) => {
              setId(e);
              setOpenSelect2(false);
            }}
            onSearch={(e) => setSearch(e)}
          />
        </Form.Item>
      </Form>
      <div className="flex flex-row justify-end pt-8">
        <Button
          loading={loadingStore}
          onClick={() => StoreIdentifEmail()}
          size="small"
          type="primary"
          disabled={!idModule}
        >
          {t("voip.apply")}
        </Button>
      </div>
    </div>
  );
};

export default IdentifContent;
