import {
  Button,
  Checkbox,
  Drawer,
  Form,
  Input,
  InputNumber,
  Radio,
  Select,
  Skeleton,
  Space,
  Spin,
  Table,
} from "antd";
import axios from "axios";
import React, { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { toastNotification } from "./ToastNotification";
import { generateAxios } from "../services/axiosInstance";
import ReactQuill, { Quill } from "react-quill";
import "react-quill/dist/quill.snow.css";
import "react-quill-emoji/dist/quill-emoji.css";
import TextArea from "antd/es/input/TextArea";
import { convertToPlain } from "../pages/layouts/chat/utils/ConversationUtils";
import { URL_ENV } from "index";
import debounce from "lodash/debounce";

const DrawerSla = ({
  setData,
  open,
  setOpen,
  severities,
  labelId,
  setLabelId,
  typeSla,
  usedContacts,
  setUsedContacts,
  setTypeSla,
  confirmLoading,
  setConfirmLoading,
}) => {
  const editorRef = useRef(null);
  const inputRef = useRef(null);
  const [openSel, setOpenSel] = useState(false);
  const [form] = Form.useForm();
  const [sla, setSla] = useState({});
  const [loading, setLoading] = useState(true);
  const [isChecked, setIsChecked] = useState(false);
  const [status, setStatus] = useState(0);
  const [loadtypeInSelect, setLoadTypeInSelect] = useState(false);
  const [page, setPage] = useState(1);
  const [lastPage, setLastPage] = useState(1);
  const formRef = useRef(null);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [selectedValues, setSelectedValues] = useState([]);
  const [options, setOptions] = useState([]);
  const [type, setType] = useState("");
  const [loadType, setLoadType] = useState(true);
  const [customers, setCustomers] = useState([]);
  const [listContact, setListContact] = useState([]);
  const [search, setSearch] = useState("");
  const [fetching, setFetching] = useState(false);

  const buttonSubmit = useRef();
  const [existSeverities, setExistSeverities] = useState([]);
  const radios = [
    { label: "contacts", value: "2" },
    { label: "produits", value: "5" },
    { label: "teams", value: "63" },
    { label: "organisations", value: "1" },
  ];
  useEffect(() => {
    setListContact([]);

    return () => {
      setListContact([]);
    };
  }, []);
  // useEffect(() => {
  //   const getCustomers = async () => {
  //     // setLoadType(true);
  //     try {
  //       const {
  //         data: { data },
  //       } = await generateAxios(URL_ENV?.REACT_APP_BASE_URL+process.env.REACT_APP_SUFFIX_API).get(
  //         `/sla_config?page=${page}&limit=20`
  //       );
  //       setCustomers(data);
  //     } catch (e) {}
  //   };
  //   if (open) getCustomers();
  // }, [page, open]);

  useEffect(() => {
    const getCustomers = async () => {
      // setLoadType(true);
      try {
        setLoadType(true);
        let {
          data: { data },
        } = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get(`/sla_config?page=1&limit=20`);

        if (type == "63") {
          setOptions((prev) => [
            ...data.team["1"].map((el) => ({
              value: el?.id,
              label: el?.label,
              used: el?.used,
            })),
          ]);
          setLastPage(data.team["0"]?.lastPage);
          if (labelId)
            setSelectedValues(
              sla.list_contact
                // .filter((el) => el.used == 1)
                .map((el) => String(el.id))
            );
        }
        if (type == "2") {
          setOptions((prev) => [
            ...data.contact["1"].map((el) => ({
              value: el?.id,
              label: el?.label,
              used: el?.used,
            })),
          ]);
          setLastPage(data.contact["0"]?.lastPage);
          if (labelId)
            setSelectedValues(
              sla.list_contact
                // .filter((el) => el.used == 1)
                .map((el) => el.id)
            );
        }
        if (type == "5") {
          setOptions((prev) => [
            ...data.product["1"].map((el) => ({
              value: el?.id,
              label: el?.label,
              used: el?.used,
            })),
          ]);
          setLastPage(data.product["0"]?.lastPage);
          if (labelId)
            setSelectedValues(
              sla.list_contact
                // .filter((el) => el.used == 1)
                .map((el) => String(el.id))
            );
        }
        if (type == "1") {
          setOptions((prev) => [
            ...data.organisation["1"].map((el) => ({
              value: el?.id,
              label: el?.label,
              used: el?.used,
            })),
          ]);
          setLastPage(data.organisation["0"]?.lastPage);
          if (labelId)
            setSelectedValues(
              sla.list_contact
                // .filter((el) => el.used == 1)
                .map((el) => el.id)
            );
        }
        setLoadType(false);
      } catch (e) {
        setLoadType(false);
      }
    };
    if (type && sla.seeder != 1 && open) getCustomers();
  }, [type, sla, labelId, open]);

  useEffect(() => {
    form.setFieldsValue({ list_contact: selectedValues });
  }, [selectedValues]);
  function handleInputNumberKeyDown(e) {
    if (e.key === "." && e.target.value.includes(".")) {
      e.preventDefault();
    }
    if (e.key === "-" && !e.target.value) {
      e.preventDefault();
    }

    if (e.key === "e") {
      e.preventDefault();
    }
    if (
      isNaN(parseInt(e.key)) &&
      ![
        "Backspace",
        "Delete",
        "ArrowLeft",
        "ArrowRight",
        "Enter",
        "Tab",
      ].includes(e.key)
    ) {
      e.preventDefault();
    }
  }
  const [t] = useTranslation("common");
  const columns = [
    {
      dataIndex: "label",
      key: "label",
      render: (_, record) => (
        <span style={{ color: record.color }}>{record.label}</span>
      ),
    },
    {
      title: t("helpDesk.hours"),
      dataIndex: "hours",
      key: "hours",
      render: (_, record) => (
        <Form.Item
          name={[record?.id, record.label, "hours"]}
          rules={[
            {
              required: true,
              message: `${t("helpDesk.hours")} ${t("table.header.isrequired")}`,
            },
          ]}
        >
          <InputNumber
            onKeyDown={handleInputNumberKeyDown}
            style={{ width: "100%" }}
            min="0"
          />
        </Form.Item>
      ),
    },
    {
      title: t("helpDesk.minutes"),
      dataIndex: "minutes",
      key: "minutes",
      render: (_, { id, label }) => (
        <Form.Item
          name={[id, label, "minutes"]}
          rules={[
            {
              required: true,
              message: `${t("helpDesk.minutes")} ${t(
                "table.header.isrequired"
              )}`,
            },
          ]}
        >
          <InputNumber
            onKeyDown={handleInputNumberKeyDown}
            style={{ width: "100%" }}
            min="0"
            max="59"
          />
        </Form.Item>
      ),
    },
  ];
  useEffect(() => {
    const getOneSla = async () => {
      setLoading(true);
      try {
        const {
          data: { data },
        } = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get(`/slas/${labelId}`);
        setSla(data.sla);
        // form.setFieldsValue({ sla_type: data.sla.sla_type });
        setExistSeverities(
          data.sla.severity.map((el) => ({
            [el?.id]: { [el.label]: { hours: el.hours, minutes: el.minutes } },
          }))
        );
        setStatus(Number(data.sla.status));
        setUsedContacts(
          data.contact_used.filter(
            (objet1) =>
              !data.sla.list_contact.some((objet2) => objet1.id === objet2.id)
          )
        );
        setListContact(data.sla.list_contact);
        // setOptions(data.sla.list_contact);
        setLoading(false);
      } catch (err) {
        setLoading(false);

        if (axios.isCancel(err)) {
          console.log("Requête annulée par l'utilisateur");
        }
      }
    };
    if (labelId && open) {
      getOneSla();
    } else {
      setLoadType(false);
      setLoading(false);

      // form.setFieldsValue({ sla_type: typeSla });

      //    onChangeType({ target: { value: typeSla } });
      const updateUsedContacts = async () => {
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get(
          `/slas
          `
        );
        setUsedContacts(
          res.data.data.contact_used
          // .filter(
          //   (el) =>
          //     el.type_sla ==
          //     res.data.data.sla.find((el) => el.primary_account == 1)?.id
          // )
          // ?.map((el) => el.list)
          // ?.flat()
        );
        setStatus(0);

        setLoading(false);
      };
      if (open) updateUsedContacts();
    }
  }, [labelId, open]);
  useEffect(() => {
    if (labelId) {
      form.setFieldsValue(
        existSeverities.reduce((objetCombine, objet) => {
          for (let key in objet) {
            return {
              ...objetCombine,
              [key]: objet[key],
              label: sla.label,
              sla_type: sla.sla_type,
              description: convertToPlain(sla.description),
              list_contact: sla.list_contact,
            };
          }
        }, {})
      );
      onChangeType({ target: { value: sla.sla_type } });
      if (sla.all_contact) {
        setIsChecked(true);
      } else {
        setIsChecked(false);
      }
    }
  }, [existSeverities, labelId, sla, form]);

  // useEffect(() => {
  //   const listSelected = sla?.list_contact?.map((element1) => {
  //     const element2 = options.find((el) => el.value == element1?.id);
  //     if (element2) {
  //       return element1?.id;
  //     } else {
  //       return element1?.label;
  //     }
  //   });
  //   if (listSelected && listSelected.length > 0)
  //     setSelectedValues((prev) => [...listSelected, ...prev]);
  // }, [loadType, options]);

  const onClose = () => {
    setOpen(false);
    form.resetFields();
    form.setFieldsValue({});
    setLabelId(null);
    setPage(1);
    setLastPage(1);
    setOptions([]);
    setLoadType(true);
    setType(null);
    setIsChecked(false);
    setSla({});
    setSelectedValues([]);
    setStatus(0);
    setListContact([]);
  };
  const onChangeType = async ({ target: { value } }) => {
    // setIsChecked(true);
    setListContact([]);
    setOptions([]);
    setSelectedValues([]);
    setPage(1);
    setLastPage(1);
    setType(value);
    setSearch("");
  };

  // useEffect(()=>{
  //   if(!loadtypeInSelect){
  //   selectRef?.current?.scrollTo(10)
  //   }

  // },[loadtypeInSelect])
  const handleScroll = async (event) => {
    const target = event.target;
    if (
      target.scrollHeight - target.scrollTop === target.clientHeight &&
      page < lastPage
    ) {
      try {
        setLastPage(1);
        setLoadTypeInSelect(true);
        // selectRef?.current?.scrollTo(10)

        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get(`/sla_config?page=${page + 1}&limit=20`);
        if (type == "63") {
          setOptions([
            ...options,
            ...res.data.data.team["1"].map((el) => ({
              value: el?.id,
              label: el?.label,
              used: el?.used,
            })),
          ]);

          setLastPage(res.data.data.team["0"]?.lastPage);
        }
        if (type == "1") {
          setOptions([
            ...options,
            ...res.data.data.organisation["1"].map((el) => ({
              value: el?.id,
              label: el?.label,
              used: el?.used,
            })),
          ]);

          setLastPage(res.data.data.organisation["0"]?.lastPage);
        }
        if (type == "2") {
          setOptions([
            ...options,
            ...res.data.data.contact["1"].map((el) => ({
              value: el?.id,
              label: el?.label,
              used: el?.used,
            })),
          ]);
          setLastPage(res.data.data.contact["0"]?.lastPage);
        }
        if (type == "5") {
          setOptions([
            ...options,
            ...res.data.data.product["1"].map((el) => ({
              value: el?.id,
              label: el?.label,
              used: el?.used,
            })),
          ]);
          setLastPage(res.data.data.product["0"]?.lastPage);
        }

        setLoadTypeInSelect(false);

        setLoadType(false);
      } catch (err) {
        setOptions([]);
        setLoadTypeInSelect(false);

        setLoadType(false);
        console.log(err);
      }

      setPage(page + 1);
    }
    // if (target.scrollTop == 0 && target.clientHeight==256 && page > 1) {
    // // if (target.scrollHeight - target.scrollTop === 0) {

    //     try {
    //       if (type  == "1") {
    //         const {
    //           data: { data },
    //         } = await axiosInstance.get(
    //           `/${radios.find((el) => el.value === type ).label}`
    //         );
    //         setOptions(data.map((el) => ({ value: el.id, label: el.label })));
    //         setLoadType(false);
    //       } else {
    //         setLoadTypeInSelect(true)
    //         const res = await axiosInstance.get(
    //           `/get-elements-data/${type }?page=${page-1}&limit=20`
    //         );

    //         setOptions(
    //           res.data.data.map((el) => ({ value: el.id, label: el.label }))
    //         );
    //         setLoadTypeInSelect(false)

    //       }
    //       // setPage(page - 1);

    //     } catch (err) {
    //       setOptions([]);
    //       setLoadTypeInSelect(false)
    //       console.log(err);
    //     }

    // }

    // if (target.scrollHeight - target.scrollTop === 0) {
    //   setPage(page - 1);
    // }
  };

  const selectRef = useRef(null);

  const onFinish = async (values) => {
    setIsSubmitted(true);
    if (labelId) {
      setConfirmLoading(true);
      try {
        const { sla_type, description, list_contact, label, ...severity } =
          values;
        let severity_ids = Object.keys(severity);
        let label_severities = Object.values(severity)
          .map((el) => Object.keys(el))
          .flat();
        let minutes = Object.values(severity)
          .map((el) => Object.values(el))
          .flat()
          .map((el) => el.minutes);
        let hours = Object.values(severity)
          .map((el) => Object.values(el))
          .flat()
          .map((el) => el.hours);
        const {
          data: { data },
        } = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post(
          `slas/update/${labelId}
          `,

          JSON.stringify({
            label: values.label,
            sla_type: values.sla_type,
            status: sla?.seeder == 1 ? sla.status : status,
            severity_ids,
            label_severities,
            minutes,
            hours,
            all_contact:
              sla?.seeder == 1 && values.sla_type == "2"
                ? "allclients"
                : sla?.seeder == 1 && values.sla_type == "1"
                ? "allteams"
                : sla?.seeder == 1 && values.sla_type == "5"
                ? "allproduits"
                : "",
            // all_contact: isChecked
            //   ? `all${radios.find((el) => el.value === type)?.label}`
            //   : "",
            // list_contact: isChecked
            //   ? ""
            //   : values?.list_contact instanceof Array
            //   ? values?.list_contact.join(",")
            //   : values?.list_contact,
            list_contact: Array.isArray(values?.list_contact)
              ? values?.list_contact.map((el) => el.value || el).join(",")
              : values?.list_contact,
            description,
          }),
          {
            headers: {
              "Content-Type": "application/json",
            },
          }
        );
        setData((prev) => prev.map((el) => (el?.id === data?.id ? data : el)));
        let formData = new FormData();
        formData.append("status", status);
        formData.append(
          "list_contact",
          values?.list_contact instanceof Array
            ? values?.list_contact.join(",")
            : values?.list_contact
        );
        try {
          const {
            data: { data },
          } = await generateAxios(
            URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
          ).post(
            `sla-status/update/${labelId}
      `,

            formData
          );

          const res = await generateAxios(
            URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
          ).get(
            `/slas
            `
          );

          setData(res.data.data.sla);

          setTypeSla(data.sla_type);
        } catch (err) {
          console.log(err);
        }

        setConfirmLoading(false);
        onClose();
        toastNotification(
          "success",
          values.label + t("toasts.edit"),
          "topRight"
        );
        setSelectedValues([]);
      } catch (err) {
        console.log(err);
        setConfirmLoading(false);
        if (err.response.status === 422)
          toastNotification(
            "error",
            `${err.response.data.errors[0]}`,
            "topRight"
          );
        else toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    } else {
      try {
        setConfirmLoading(true);
        const { sla_type, description, list_contact, label, ...severity } =
          values;
        let severity_ids = Object.keys(severity);
        let label_severities = Object.values(severity)
          .map((el) => Object.keys(el))
          .flat();
        let minutes = Object.values(severity)
          .map((el) => Object.values(el))
          .flat()
          .map((el) => el.minutes);
        let hours = Object.values(severity)
          .map((el) => Object.values(el))
          .flat()
          .map((el) => el.hours);
        const {
          data: { data },
        } = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post(
          `slas
          `,

          JSON.stringify({
            label: values.label,
            sla_type: values.sla_type,
            status,
            severity_ids,
            label_severities,
            minutes,
            hours,
            // all_contact: isChecked
            //   ? `all${radios.find((el) => el.value === type)?.label}`
            //   : "",
            // list_contact: isChecked ? "" : values.list_contact?.join(","),
            list_contact:
              values?.list_contact instanceof Array
                ? values?.list_contact.map((el) => el.value).join(",")
                : values?.list_contact,

            description,
          }),
          {
            headers: {
              "Content-Type": "application/json",
            },
          }
        );
        if (status == 0) {
          setData((previous) => [...previous, data]);
        }
        if (status == 1) {
          setData((previous) =>
            [...previous, data].map((el) =>
              el.id == data.id ||
              (el.sla_type == data.sla_type && el.seeder == 1)
                ? { ...el, status: 1 }
                : el.sla_type == data.sla_type
                ? el
                : { ...el, status: 0 }
            )
          );
        }

        setConfirmLoading(false);
        onClose();

        toastNotification(
          "success",
          `${values.label} ` + t("toasts.created"),
          "topRight"
        );
        setSelectedValues([]);
      } catch (err) {
        setConfirmLoading(false);
        if (err.response.status === 422)
          toastNotification(
            "error",
            `${err.response.data.errors[0]}`,
            "topRight"
          );
        else toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    }
  };
  const onFinishFailed = (values) => {
    setIsSubmitted(true);
  };
  const handleSelectAll = (selectAll) => {
    if (selectAll) {
      const values = options.map((option) => option.value);
      setSelectedValues(values);
    } else {
      setSelectedValues([]);
    }
  };

  const handleChange = (values) => {
    // if (values.includes("all")) {
    //   handleSelectAll(true);
    // } else {
    // const filteredValues = values.filter((value) => value !== "all");
    setSelectedValues(values);
    // }
  };

  // const selectAllOption = { label: "Sélectionner tout", value: "all" };
  // const value =
  //   selectedValues.length === options.length ? ["all"] : selectedValues;

  // useEffect(() => {
  //   if (
  //     selectedValues &&
  //     selectedValues.length > 0 &&
  //     selectedValues.length === options.length
  //   ) {
  //     setIsChecked(true);
  //     form.setFieldsValue({ list_contact: [] });
  //     setSelectedValues([]);
  //   }
  // }, [selectedValues, options]);

  // useEffect(() => {
  //   if (listContact)
  //     setOptions((prev) => [
  //       ...listContact?.map((el) => ({
  //         value: el.label,
  //         label: el.id,
  //       })),
  //       ...prev,
  //     ]);
  // }, [listContact]);
  const getOptionsBySearch = async (search) => {
    // setLoadType(true);
    setFetching(true);
    try {
      let {
        data: { data },
      } = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).get(`/sla_config?page=1&limit=20&search=${search}`);

      if (type == "63") {
        setOptions((prev) => [
          ...data.team["1"].map((el) => ({
            value: el?.id,
            label: el?.label,
            used: el?.used,
          })),
        ]);
        setLastPage(data.team["0"]?.lastPage);
        if (labelId)
          setSelectedValues(
            sla.list_contact
              // .filter((el) => el.used == 1)
              .map((el) => String(el.id))
          );
      }
      if (type == "2") {
        setOptions((prev) => [
          ...data.contact["1"].map((el) => ({
            value: el?.id,
            label: el?.label,
            used: el?.used,
          })),
        ]);
        setLastPage(data.contact["0"]?.lastPage);
        if (labelId)
          setSelectedValues(
            sla.list_contact
              // .filter((el) => el.used == 1)
              .map((el) => el.id)
          );
      }
      if (type == "5") {
        setOptions((prev) => [
          ...data.product["1"].map((el) => ({
            value: el?.id,
            label: el?.label,
            used: el?.used,
          })),
        ]);
        setLastPage(data.product["0"]?.lastPage);
        if (labelId)
          setSelectedValues(
            sla.list_contact
              // .filter((el) => el.used == 1)
              .map((el) => String(el.id))
          );
      }
      if (type == "1") {
        setOptions((prev) => [
          ...data.organisation["1"].map((el) => ({
            value: el?.id,
            label: el?.label,
            used: el?.used,
          })),
        ]);
        setLastPage(data.organisation["0"]?.lastPage);
        if (labelId)
          setSelectedValues(
            sla.list_contact
              // .filter((el) => el.used == 1)
              .map((el) => el.id)
          );
      }
      setFetching(false);
    } catch (e) {
      setFetching(false);
    }
  };
  const debouceRequest = debounce((value) => {
    getOptionsBySearch(value);
    setSearch(value);
  }, 300);
  return (
    <>
      <Drawer
        title={`${
          labelId ? t("helpDesk.updatesla") : t("helpDesk.createsla")
        } `}
        placement="right"
        onClose={onClose}
        open={open}
        size="large"
        afterOpenChange={(visible) => {
          if (visible) {
            inputRef?.current?.focus();
          }
        }}
        // style={{ position: 'absolute' }}
        footer={
          <div className="flex justify-start">
            <Space align="end">
              <Button onClick={onClose}> close</Button>{" "}
              <Button
                type="primary"
                onClick={() => form.submit()}
                loading={confirmLoading}
              >
                {t("form.submit")}
              </Button>
            </Space>
          </div>
        }
      >
        {loading ? (
          <>
            <Skeleton />
            <Skeleton />
            <Skeleton />
            <Skeleton />
            <Skeleton />
          </>
        ) : (
          <Form
            form={form}
            ref={formRef}
            name="basic"
            labelCol={{
              span: 24,
            }}
            wrapperCol={{
              span: 24,
            }}
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
            autoComplete="off"
            scrollToFirstError
            layout="vertical"
          >
            <Form.Item
              name="label"
              label={t("tags.name")}
              rules={[
                {
                  required: true,
                  message: `${t("tags.name")} ${t("table.header.isrequired")}`,
                },
              ]}
              defaultValue={sla?.label}
            >
              <Input
                placeholder="Name"
                ref={inputRef}
                disabled={sla.seeder === 1}
              />
            </Form.Item>
            <Form.Item
              label="Type"
              name="sla_type"
              rules={[
                {
                  required: !isChecked ? true : false,
                  message: `Type ${t("table.header.isrequired")}`,
                },
              ]}
            >
              <Radio.Group onChange={onChangeType}>
                {radios.map((el) => (
                  <Radio
                    value={el.value}
                    disabled={labelId ? el.value != sla.sla_type : false}
                    key={el.value}
                  >
                    {t(`${"helpDesk." + el.label}`)}
                  </Radio>
                ))}
              </Radio.Group>
            </Form.Item>
            {loadType === false && type ? (
              <Form.Item
                label={t(
                  `helpDesk.list${
                    radios?.find((el) => el.value === type)?.label
                  }`
                )}
                name="list_contact"
                rules={[
                  {
                    required: sla?.seeder == 1 ? false : true,
                    message: `${t(
                      `helpDesk.list${
                        radios?.find((el) => el.value === type)?.label
                      }`
                    )} ${t("table.header.isrequired")}`,
                  },
                ]}
              >
                <div className="flex items-center space-x-2">
                  {sla?.seeder == 1 ? null : (
                    <Select
                      labelInValue
                      filterOption={false}
                      open={openSel}
                      onDropdownVisibleChange={setOpenSel}
                      ref={selectRef}
                      mode="multiple"
                      maxTagCount="responsive"
                      showSearch={!loadtypeInSelect}
                      placeholder={t(
                        `helpDesk.select${
                          radios?.find((el) => el.value === type)?.label
                        }`
                      )}
                      loading={loadtypeInSelect || fetching}
                      notFoundContent={fetching ? <Spin size="small" /> : null}
                      // defaultValue={sla.list_contact?.map((el) => el.id)}
                      value={selectedValues}
                      onChange={handleChange}
                      onPopupScroll={handleScroll}
                      onSearch={(value) => debouceRequest(value)}
                      // filterOption={
                      //   (input, option) =>
                      //     !loadtypeInSelect && setSearch(input.toLowerCase())
                      //   // option?.children
                      //   //   ?.toLowerCase()
                      //   //   .indexOf(input.toLowerCase()) >= 0
                      // }
                      disabled={sla?.seeder == 1 || isChecked}
                    >
                      {/* <Select.Option
                  key={selectAllOption.value}
                  value={selectAllOption.value}
                >
                  {selectAllOption.label}
                </Select.Option> */}
                      {[
                        ...listContact?.map((el) => ({
                          value: String(el.id),
                          label: el.label,
                        })),
                        ...options.filter(
                          (option) =>
                            !listContact?.find(
                              (el) => el.id === option.value
                              // && el.used === 1
                            )
                        ),
                      ].map((option) => (
                        <Select.Option
                          key={option.value}
                          value={option.value}
                          disabled={
                            listContact?.find((el) => el.id === option.value)
                              ? false
                              : option.used
                          }
                        >
                          {option.label}
                        </Select.Option>
                      ))}

                      {loadtypeInSelect && (
                        <Select.Option>
                          <Spin size="small" />
                        </Select.Option>
                      )}
                    </Select>
                  )}
                  {labelId && sla?.seeder == 1 ? (
                    <Checkbox
                      defaultChecked={true}
                      checked={true}
                      disabled
                      // onChange={(CheckboxChangeEvent) => {
                      //   setIsChecked(CheckboxChangeEvent.target.checked);
                      //   resetSelect(CheckboxChangeEvent.target.checked);
                      // }}
                    >
                      <span className="whitespace-nowrap">
                        {t(`helpDesk.all`) +
                          " " +
                          t(
                            `helpDesk.list${
                              radios?.find((el) => el.value === type)?.label
                            }`
                          )}
                      </span>
                    </Checkbox>
                  ) : (
                    // <Checkbox
                    //   defaultChecked={isChecked}
                    //   checked={isChecked}
                    //   onChange={(CheckboxChangeEvent) => {
                    //     setIsChecked(CheckboxChangeEvent.target.checked);
                    //     // resetSelect(CheckboxChangeEvent.target.checked);
                    //   }}
                    // >
                    //   <span className="whitespace-nowrap">
                    //     {t(
                    //       `helpDesk.all${
                    //         radios.find((el) => el.value === type)?.label
                    //       }`
                    //     )}
                    //   </span>
                    // </Checkbox>
                    ""
                  )}
                </div>
              </Form.Item>
            ) : loadType === true ? (
              <>
                <Skeleton
                  paragraph={{
                    rows: 1,
                  }}
                />
              </>
            ) : (
              ""
            )}

            <Space direction="vertical" style={{ width: "100%" }}>
              <div className="space-x-1">
                <span
                  style={{ fontFamily: "SimSun,sans-serif", color: "#ff4d4f" }}
                >
                  *
                </span>
                <span
                  style={{ color: "rgb(100 116 139 / 1)", fontWeight: 500 }}
                >
                  Severities
                </span>
              </div>
              <Form.Item>
                <Table
                  dataSource={severities}
                  columns={columns}
                  pagination={false}
                  size={"small"}
                  className="my-table"
                  // scroll={{ y: 'auto' }}
                  scroll={{ y: "calc(100vh - 77vh)" }}
                  // scroll={{ y: 'calc(100vh - 300px)' }}
                />
                {/* </div> */}
              </Form.Item>
            </Space>
            {/* <Form.Item name="description" label="Description">
              <ReactQuill
                // value={valueQuill}
                // onChange={setValueQuill}
                ref={editorRef}
              />
            </Form.Item> */}
            <Form.Item name="description" label="Description">
              <TextArea rows={4} />

              {/* <ReactQuill
                // value={valueQuill}
                // onChange={setValueQuill}
                ref={editorRef}
              /> */}
            </Form.Item>
            {sla?.seeder !== 1 ? (
              <Form.Item label="Status">
                <Radio.Group
                  defaultValue={status}
                  value={status}
                  // value={String(status)}
                  onChange={(e) => setStatus(e.target.value)}
                >
                  <Radio value={1}>{t("helpDesk.actif")}</Radio>
                  <Radio value={0}>{t("helpDesk.noActif")}</Radio>
                </Radio.Group>
              </Form.Item>
            ) : (
              ""
            )}
          </Form>
        )}
      </Drawer>
    </>
  );
};

export default DrawerSla;
