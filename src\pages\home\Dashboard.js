import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Tabs } from "antd";
import React, { useEffect, useState, useMemo, useCallback } from "react";
import { AvatarChat } from "components/Chat";
import { URL_ENV } from "index";
import { getName } from "pages/layouts/chat/utils/ConversationUtils";
import ItemHeader from "pages/components/DetailsProfile/ItemHeader";
import dayjs from "dayjs";
import { rangePresets } from "pages/voip/helpers/helpersFunc";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import DashboardVoip from "components/itemsDashboard/DashboardVoip";
import DashboardTasks from "./components/task.stat/Task";
import DashboardEmails from "./components/email.stat/email";
import DashboardChat from "./components/chat.stat/Chat";
import DashboardRmc from "./components/rmc.stat/Rmc";
import DashboardNotes from "./components/note.stat/note";
import DashboardTicket from "./components/ticket.stat/ticket";
import DashboardDeals from "./components/deal.stat/deal";
import { isGuestConnected } from "utils/role";
import { useLocation, useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { setDateDashboard } from "new-redux/actions/dashboard.actions";
import DashboardLead from "./components/lead.stat/lead";
import DashboardContacts from "./components/contact.stat/contact";
import Home4 from "./Home4";
import DashboardTicket2 from "./components/ticket.stat/ticket2";
import { Refs_IDs } from "components/tour/tourConfig";
import GeneralStat from "pages/stat/general";
import { Mail, Phone, Smartphone } from "lucide-react";
import { HomeOutlined } from "@ant-design/icons";

// Fonction utilitaire extraite et mémorisée
export const disabledDate = (current) => {
  return current && current > dayjs().endOf("day");
};

const Dashboard = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [t] = useTranslation("common");

  const [key, setKey] = useState("2");

  // Sélecteurs Redux optimisés
  const { user } = useSelector((state) => state.user);
  const { startDate, endDate } = useSelector(
    (state) => state.dashboardRealTime
  );

  // Mémoisation des valeurs dérivées
  const isGuest = useMemo(() => isGuestConnected(), []);
  const isDevBranch = useMemo(
    () => process.env.REACT_APP_BRANCH?.includes("dev"),
    []
  );
  const isOnMainPath = useMemo(
    () => location.pathname === "/" || location.pathname === "/dashboard",
    [location.pathname]
  );

  // Callbacks mémorisés
  const onTabChange = useCallback((newKey) => {
    setKey(newKey);
  }, []);

  const onDateChange = useCallback(
    (date, dateString) => {
      dispatch(
        setDateDashboard({
          startDate: dateString[0],
          endDate: dateString[1],
        })
      );
    },
    [dispatch]
  );

  // Fonction pour vérifier si un preset est sélectionné
  const isPresetSelected = useCallback(
    (presetValue) => {
      if (!startDate || !endDate) return false;

      const [presetStart, presetEnd] = presetValue;
      const currentStart = dayjs(startDate, user?.location?.date_format);
      const currentEnd = dayjs(endDate, user?.location?.date_format);

      return (
        currentStart.isSame(presetStart, "day") &&
        currentEnd.isSame(presetEnd, "day")
      );
    },
    [startDate, endDate, user?.location?.date_format]
  );

  // Mémoisation des presets
  const datePresets = useMemo(() => {
    const today = dayjs();
    const isMondayOrTuesday = today.day() === 1 || today.day() === 2; // 1 = lundi, 2 = mardi

    return rangePresets(t)
      .filter((preset) => {
        // On garde toutes les options SAUF "currentWeek" si c'est lundi ou mardi
        if (isMondayOrTuesday && preset.label === t("voip.currentWeek")) {
          return false; // On exclut cette option
        }
        return true; // On garde toutes les autres
      })
      .map((preset) => ({
        ...preset,
        label: (
          <span
            style={{
              color: isPresetSelected(preset.value) ? "#1890ff" : "inherit",
            }}
          >
            {preset.label}
          </span>
        ),
      }));
  }, [t, isPresetSelected]);

  // Mémoisation des valeurs par défaut du DatePicker
  const defaultDateValues = useMemo(() => {
    if (!startDate || !endDate) return undefined;
    return [
      dayjs(startDate, user?.location?.date_format),
      dayjs(endDate, user?.location?.date_format),
    ];
  }, [startDate, endDate, user?.location?.date_format]);

  // Mémoisation du composant DatePicker
  const DatePickerComponent = useMemo(
    () =>
      startDate &&
      endDate && (
        <DatePicker.RangePicker
          defaultValue={defaultDateValues}
          allowClear={false}
          style={{ width: "100%" }}
          disabledDate={disabledDate}
          format={user?.location?.date_format}
          presets={datePresets}
          onChange={onDateChange}
        />
      ),
    [defaultDateValues, user?.location?.date_format, datePresets, onDateChange]
  );

  // Composant de contenu pour chaque tab
  const TabContent = useMemo(() => {
    const contentMap = {
      2: <DashboardVoip start={startDate} end={endDate} />,
      3: <DashboardTasks start={startDate} end={endDate} />,
      4: <DashboardEmails start={startDate} end={endDate} />,
      5: <DashboardChat start={startDate} end={endDate} />,
      6: <DashboardRmc start={startDate} end={endDate} />,
      7: <DashboardNotes start={startDate} end={endDate} />,
      8: <DashboardTicket start={startDate} end={endDate} />,
      9: <DashboardDeals start={startDate} end={endDate} />,
      10: <DashboardLead start={startDate} end={endDate} />,
      11: <DashboardContacts start={startDate} end={endDate} />,
      12: <GeneralStat start={startDate} end={endDate} />,
      15: <DashboardTicket2 start={startDate} end={endDate} />,
    };

    const content = contentMap[key];
    if (!content) return null;

    // Wrapper pour le scrolling
    const needsScrollWrapper = [
      "2",
      "3",
      "5",
      "6",
      "7",
      "9",
      "10",
      "11",
      "12",
    ].includes(key);

    return needsScrollWrapper ? (
      <div className="h-[calc(100vh-170px)] overflow-y-auto overflow-x-hidden">
        <div className="pr-2">{content}</div>
      </div>
    ) : (
      content
    );
  }, [key, startDate, endDate]);

  // Mémoisation de la configuration des tabs
  const tabItems = useMemo(() => {
    const items = [];

    if (!isGuest && user?.access?.["logs"] === "1") {
      items.push({ key: "2", label: t("menu2.logs"), children: TabContent });
    }
    if (!isGuest && user?.access?.["activities"] === "1") {
      items.push({ key: "3", label: t("menu1.tasks"), children: TabContent });
    }
    if (!isGuest && user?.access?.["email"] === "1") {
      items.push({ key: "4", label: "Email", children: TabContent });
    }
    if (!isGuest && user?.access?.["chat"] === "1") {
      items.push({ key: "5", label: t("menu1.chat"), children: TabContent });
    }
    if (!isGuest && user?.access?.rmc === "1") {
      items.push({
        key: "6",
        label: t("dashboard.socialMedia"),
        children: TabContent,
      });
    }
    if (!isGuest && user?.access?.["notes"] === "1") {
      items.push({ key: "7", label: "Notes", children: TabContent });
    }
    if (user?.access?.["ticket"] === "1") {
      items.push({ key: "8", label: "Tickets", children: TabContent });
    }
    if (!isGuest && user?.access?.["deals"] === "1") {
      items.push({ key: "9", label: t("menu1.deals"), children: TabContent });
    }
    if (!isGuest && user?.access?.["leads"] === "1") {
      items.push({ key: "10", label: t("menu1.leads"), children: TabContent });
    }
    if (!isGuest && user?.access?.["contact"] === "1") {
      items.push({
        key: "11",
        label: t("menu1.contacts"),
        children: TabContent,
      });
    }
    if (!isGuest && isDevBranch) {
      items.push({ key: "12", label: "Stats avancées", children: TabContent });
      items.push({ key: "15", label: "Tickets2", children: TabContent });
    }

    return items;
  }, [isGuest, user?.access, t, TabContent, isDevBranch]);

  // Mémoisation des informations utilisateur
  const userInfo = useMemo(() => {
    if (!user) return null;

    return {
      name: getName(user.label, "name"),
      avatar: user.avatar,
      email: user.email,
      extension: user.extension,
      phone: user.phone,
      tenant: user.tenant,
      avatarUrl: user.avatar
        ? `${URL_ENV?.REACT_APP_BASE_URL}${URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL}${user.avatar}`
        : null,
    };
  }, [user]);

  // Effects optimisés
  useEffect(() => {
    if (location.state) {
      setKey(location.state);
    } else if (isGuest) {
      setKey("8");
    } else {
      setKey("2");
    }
  }, [user?.role, location.state, isGuest]);

  useEffect(() => {
    if (location.pathname === "/") {
      navigate("/dashboard");
    }
  }, [navigate, location.pathname]);

  useEffect(() => {
    if (user?.location && !startDate && !endDate) {
      const dateFormat = user.location.date_format || "YYYY-MM-DD";
      const currentDate = dayjs().format(dateFormat);

      dispatch(
        setDateDashboard({
          startDate: currentDate,
          endDate: currentDate,
        })
      );
    }
  }, [user, startDate, endDate, dispatch]);

  // Rendu conditionnel optimisé
  if (isGuest) {
    return (
      <div className="h-[calc(100vh-57px)] space-y-6">
        <div className="flex flex-col">
          <div
            ref={Refs_IDs.select_date_dashboard}
            className="m-3 flex w-max self-end"
          >
            {DatePickerComponent}
          </div>
          <DashboardTicket start={startDate} end={endDate} from="guest" />
        </div>
      </div>
    );
  }

  if (isOnMainPath) {
    return (
      <div className="h-[calc(100vh-57px)] space-y-6">
        <div className="px-6 pt-6">
          <div className="flex items-center gap-x-3">
            <AvatarChat
              fontSize={userInfo?.avatar ? "2rem" : "1.5rem"}
              type="user"
              url={userInfo?.avatarUrl}
              size={60}
              name={getName(user?.label, "avatar")}
              hasImage={!!userInfo?.avatar}
            />
            <div className="flex w-full items-center justify-between">
              <Space direction="vertical" size={8}>
                <span className="text-xl font-bold">
                  {t("dashboard.welcome")}, {userInfo?.name}
                </span>
                <Space size="small" className="text-slate-600">
                  {userInfo?.email && (
                    <ItemHeader
                      text={userInfo.email}
                      icon={<Mail size={15} />}
                      title="Email"
                      colorIcon="text-blue-500"
                      colorText="text-slate-600"
                    />
                  )}
                  <ItemHeader
                    text={userInfo?.extension}
                    icon={<Phone size={15} />}
                    title={t("dashboard.postNumber")}
                    colorIcon="text-blue-500"
                    colorText="text-slate-600"
                  />
                  {userInfo?.phone &&
                    Array.isArray(userInfo.phone) &&
                    userInfo.phone.length > 1 &&
                    userInfo.phone.every((el) => el != null) && (
                      <ItemHeader
                        text={userInfo.phone[0] + userInfo.phone[1]}
                        icon={<Smartphone size={15} />}
                        title={t("dashboard.phoneNumber")}
                        colorIcon="text-blue-500"
                        colorText="text-black"
                      />
                    )}
                  {userInfo?.tenant && !isGuest && (
                    <ItemHeader
                      text={URL_ENV?.REACT_APP_TENANT_ALIAS}
                      icon={<HomeOutlined />}
                      title={t("dashboard.tenancy")}
                      colorIcon="text-blue-500"
                      colorText="text-slate-600"
                      noIconCopy={true}
                    />
                  )}
                </Space>
              </Space>
              <div ref={Refs_IDs.select_date_dashboard}>
                {DatePickerComponent}
              </div>
            </div>
          </div>
        </div>

        <div className="m-2">
          <div className="h-[calc(100vh-170px)] overflow-y-auto overflow-x-hidden">
            <Home4 start={startDate} end={endDate} />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-[calc(100vh-57px)] space-y-6">
      <div className="px-4">
        <div className="flex justify-end place-self-end pt-4">
          {DatePickerComponent}
        </div>
        {user?.role && (
          <Tabs
            activeKey={key}
            items={tabItems}
            onChange={onTabChange}
            destroyInactiveTabPane={true}
          />
        )}
      </div>
    </div>
  );
};

export default Dashboard;
