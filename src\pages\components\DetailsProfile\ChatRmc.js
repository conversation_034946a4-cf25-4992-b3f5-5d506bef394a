import {
  InstagramOutlined,
  LoadingOutlined,
  MessageOutlined,
  WhatsAppOutlined,
} from "@ant-design/icons";
import { Empty, Select, Spin, Tabs, Typography } from "antd";
import { URL_ENV } from "index";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { LiaFacebookMessenger } from "react-icons/lia";
import { useSelector } from "react-redux";
import { useLocation, useParams } from "react-router-dom";
import { generateAxios } from "services/axiosInstance";
import ChatRmcWithoutInput from "./chatRmcWithoutInput";
export const getIconRmc = (source) => {
  switch (source) {
    case "RMC_LiveChat":
      return (
        <span
          style={{ color: "#0078FF" }}
          className="flex items-center gap-x-1"
        >
          <span>{source?.split("_")[1]}</span>
          <MessageOutlined />
        </span>
      );
    case "RMC_Whatsapp":
      return (
        <span
          style={{ color: "#25D366" }}
          className="flex items-center gap-x-1"
        >
          <span>{source?.split("_")[1]}</span>{" "}
          <WhatsAppOutlined style={{ color: "#25D366" }} />
        </span>
      );
    case "RMC_Messenger":
      return (
        <span
          style={{ color: "#0084FF" }}
          className="flex items-center gap-x-1"
        >
          <span>{source?.split("_")[1]}</span>
          <svg
            width="13"
            height="16"
            viewBox="0 -2 13 13"
            xmlns="http://www.w3.org/2000/svg"
            focusable={false}
            data-icon="messenger"
            fill="currentColor"
            aria-hidden="true"
          >
            <path d="M6.5 0C2.9305 0 0 2.71177 0 6.07107C0 7.86104 0.850586 9.45098 2.16667 10.5584V13L4.57031 11.8287C5.1818 12.0081 5.8208 12.1421 6.5 12.1421C10.0695 12.1421 13 9.43036 13 6.07107C13 2.71177 10.0695 0 6.5 0ZM6.5 1.05584C9.51091 1.05584 11.9167 3.30774 11.9167 6.07107C11.9167 8.83439 9.51091 11.0863 6.5 11.0863C5.85254 11.0863 5.2347 10.9667 4.65495 10.7728L4.4349 10.7069L3.25 11.2843V10.0964L3.04687 9.93147C1.84505 9.00761 1.08333 7.62389 1.08333 6.07107C1.08333 3.30774 3.48909 1.05584 6.5 1.05584ZM5.89062 4.40482L2.6237 7.7703L5.55208 6.18655L7.10937 7.8198L10.3424 4.40482L7.48177 5.97208L5.89062 4.40482Z" />
          </svg>
        </span>
      );
    case "RMC_Instagram":
      return (
        <span
          style={{ color: "#E4405F" }}
          className="flex items-center gap-x-1"
        >
          <span>{source?.split("_")[1]}</span>
          <InstagramOutlined />
        </span>
      );
    default:
      return null;
  }
};
const ChatRmc = ({
  dataSteps,
  from,
  headerHeight,
  contactInfo,
  source,
  listConv,
}) => {
  const [loading, setLoading] = useState(false);
  const [loadChangeConv, setLoadChangeConv] = useState(false);
  const [selectedConv, setSelectedConv] = useState("");
  const [firstRender, setFirstRender] = useState(true);
  const { activeTab360 } = useSelector((state) => state?.vue360);
  const [t] = useTranslation("common");

  const location = useLocation();
  const { user } = useSelector((state) => state?.user);
  useEffect(() => {
    const getConversation = async () => {
      setLoadChangeConv(true);
      setLoading(true);
      try {
        if (Array.isArray(listConv) & (listConv.length > 0)) {
          setSelectedConv(
            listConv.find((el) => el.conversation_id)?.conversation_id
          );
        }
        setLoading(false);
        setTimeout(() => {
          setLoadChangeConv(false);
          setFirstRender(false);
        }, 1000);
      } catch (err) {
        setLoading(false);

        console.log(err);
      }
    };
    if (activeTab360 === 5 || source === "popover") getConversation();
  }, [activeTab360, contactInfo?.id, source, listConv]);
  return (
    <>
      {user.rmc_access === "OUI" ? (
        <div>
          {!loading && listConv.length > 1 && selectedConv ? (
            <Tabs
              tabPosition={"left"}
              items={listConv.map((el) => {
                return {
                  label: el.source,
                  key: el.conversation_id,
                  children: (
                    <>
                      {selectedConv ? (
                        <>
                          {loadChangeConv && (
                            <div
                              style={{
                                height: `calc(100vh - ${
                                  from === "viewSphere"
                                    ? "300px"
                                    : dataSteps.length > 0
                                    ? "268px"
                                    : "146px"
                                })`,
                                width: "100%",
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                              }}
                            >
                              <Spin
                                indicator={
                                  <LoadingOutlined
                                    style={{
                                      fontSize: 24,
                                    }}
                                    spin
                                  />
                                }
                              />
                            </div>
                          )}
                          <div
                            className={`${
                              loadChangeConv ? "h-0 w-0" : "h-full"
                            }`}
                          >
                            <iframe
                              src={`${URL_ENV?.REACT_APP_RMC_URL}?conversation=${selectedConv}&vue=360`}
                              title="chat"
                              display="block"
                              width="100%"
                              // height= {`${deviceHeight}px -120px`}
                              sendbox="allow-same-origin allow-popups"
                              allowFullScreen="true"
                              style={{
                                height: `calc(100vh - ${
                                  source === "popover"
                                    ? "70px"
                                    : from === "viewSphere"
                                    ? "300px"
                                    : dataSteps.length > 0
                                    ? "268px"
                                    : "146px"
                                })`,
                                border: "none",
                              }}
                              allowtransparency="true"
                              // onLoad={() => setHide(true)}
                            ></iframe>
                          </div>
                        </>
                      ) : (
                        <div className="mt-5">
                          <Typography.Title
                            level={3}
                            style={{ textAlign: "center" }}
                          >
                            {t("vue360.noChat")}
                          </Typography.Title>
                        </div>
                      )}
                    </>
                  ),

                  icon:
                    el.source === "RMC_LiveChat" ? (
                      <MessageOutlined />
                    ) : el.source === "RMC_Whatsapp" ? (
                      <WhatsAppOutlined />
                    ) : el.source === "RMC_Messenger" ? (
                      <span className="anticon anticon-whats-app">
                        <svg
                          width="13"
                          height="16"
                          viewBox="0 -2 13 13"
                          xmlns="http://www.w3.org/2000/svg"
                          focusable={false}
                          data-icon="instagram"
                          fill="currentColor"
                          aria-hidden="true"
                        >
                          <path
                            d="M6.5 0C2.9305 0 0 2.71177 0 6.07107C0 7.86104 0.850586 9.45098 2.16667 10.5584V13L4.57031 11.8287C5.1818 12.0081 5.8208 12.1421 6.5 12.1421C10.0695 12.1421 13 9.43036 13 6.07107C13 2.71177 10.0695 0 6.5 0ZM6.5 1.05584C9.51091 1.05584 11.9167 3.30774 11.9167 6.07107C11.9167 8.83439 9.51091 11.0863 6.5 11.0863C5.85254 11.0863 5.2347 10.9667 4.65495 10.7728L4.4349 10.7069L3.25 11.2843V10.0964L3.04687 9.93147C1.84505 9.00761 1.08333 7.62389 1.08333 6.07107C1.08333 3.30774 3.48909 1.05584 6.5 1.05584ZM5.89062 4.40482L2.6237 7.7703L5.55208 6.18655L7.10937 7.8198L10.3424 4.40482L7.48177 5.97208L5.89062 4.40482Z"
                            // fill="black"
                          />
                        </svg>
                      </span>
                    ) : el.source === "RMC_Instagram" ? (
                      <InstagramOutlined />
                    ) : null,
                };
              })}
              onChange={(key) => {
                setLoadChangeConv(true);
                setSelectedConv(key);
                setTimeout(() => {
                  setLoadChangeConv(false);
                }, 2000);
              }}
            />
          ) : !loading && listConv.length === 1 && selectedConv ? (
            <iframe
              src={`${URL_ENV?.REACT_APP_RMC_URL}?conversation=${selectedConv}&vue=360`}
              title="chat"
              display="block"
              width="100%"
              // height= {`${deviceHeight}px -120px`}
              sendbox="allow-same-origin allow-popups"
              allowFullScreen="true"
              style={{
                height: `calc(100vh - ${
                  source === "popover"
                    ? "34px"
                    : from === "viewSphere"
                    ? headerHeight + 141 + "px"
                    : dataSteps.length > 0
                    ? "268px"
                    : "146px"
                })`,
                border: "none",
              }}
              allowtransparency="true"
              // onLoad={() => setHide(true)}
            ></iframe>
          ) : !loading && listConv.length === 0 ? (
            <div className="mt-5">
              <Empty />
            </div>
          ) : !loading && listConv.length === 1 && !selectedConv ? (
            <div className="mt-5">
              <Empty />
            </div>
          ) : (
            <div
              style={{
                height: `calc(100vh - ${
                  from === "viewSphere"
                    ? headerHeight + 141 + "px"
                    : dataSteps.length > 0
                    ? "268px"
                    : "146px"
                })`,
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              {" "}
              <Spin
                indicator={
                  <LoadingOutlined
                    style={{
                      fontSize: 24,
                    }}
                    spin
                  />
                }
              />
            </div>
          )}
        </div>
      ) : user.rmc_access !== "OUI" &&
        !loading &&
        listConv.length > 1 &&
        selectedConv ? (
        <div>
          <Tabs
            tabPosition={"left"}
            items={listConv.map((el) => {
              return {
                label: el.source,
                key: el.id,
                children: (
                  <>
                    {selectedConv ? (
                      <>
                        {loadChangeConv && (
                          <div
                            style={{
                              height: `calc(100vh - ${
                                from === "viewSphere"
                                  ? headerHeight + 141 + "px"
                                  : dataSteps.length > 0
                                  ? "268px"
                                  : "146px"
                              })`,
                              width: "100%",
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                            }}
                          >
                            <Spin
                              indicator={
                                <LoadingOutlined
                                  style={{
                                    fontSize: 24,
                                  }}
                                  spin
                                />
                              }
                            />
                          </div>
                        )}

                        <ChatRmcWithoutInput
                          idLead={el?.id}
                          dataSteps={dataSteps}
                          setLoadChangeConv={setLoadChangeConv}
                          guest={false}
                          from={from}
                          headerHeight={headerHeight}
                          contactInfo={contactInfo}
                        />
                      </>
                    ) : (
                      <div className="mt-5">
                        <Empty />
                      </div>
                    )}
                  </>
                ),

                icon:
                  el.source === "RMC_LiveChat" ? (
                    <MessageOutlined />
                  ) : el.source === "RMC_Whatsapp" ? (
                    <WhatsAppOutlined />
                  ) : el.source === "RMC_Messenger" ? (
                    <svg
                      width="13"
                      height="16"
                      viewBox="0 -2 13 13"
                      xmlns="http://www.w3.org/2000/svg"
                      focusable={false}
                      data-icon="instagram"
                      fill="currentColor"
                      aria-hidden="true"
                    >
                      <path
                        d="M6.5 0C2.9305 0 0 2.71177 0 6.07107C0 7.86104 0.850586 9.45098 2.16667 10.5584V13L4.57031 11.8287C5.1818 12.0081 5.8208 12.1421 6.5 12.1421C10.0695 12.1421 13 9.43036 13 6.07107C13 2.71177 10.0695 0 6.5 0ZM6.5 1.05584C9.51091 1.05584 11.9167 3.30774 11.9167 6.07107C11.9167 8.83439 9.51091 11.0863 6.5 11.0863C5.85254 11.0863 5.2347 10.9667 4.65495 10.7728L4.4349 10.7069L3.25 11.2843V10.0964L3.04687 9.93147C1.84505 9.00761 1.08333 7.62389 1.08333 6.07107C1.08333 3.30774 3.48909 1.05584 6.5 1.05584ZM5.89062 4.40482L2.6237 7.7703L5.55208 6.18655L7.10937 7.8198L10.3424 4.40482L7.48177 5.97208L5.89062 4.40482Z"
                        // fill="black"
                      />
                    </svg>
                  ) : el.source === "RMC_Instagram" ? (
                    <InstagramOutlined />
                  ) : null,
              };
            })}
            style={{
              height: `calc(100vh - ${dataSteps.length > 0 ? "300" : "175"}px)`,
            }}
            onChange={(key) => {
              // setLoadChangeConv(true);
              setSelectedConv(key);
              // setTimeout(() => {
              //   setLoadChangeConv(false);
              // }, 2000);
            }}
          />
        </div>
      ) : user.rmc_access !== "OUI" &&
        !loading &&
        listConv.length === 1 &&
        selectedConv ? (
        <ChatRmcWithoutInput
          dataSteps={dataSteps}
          guest={true}
          from={from}
          headerHeight={headerHeight}
          contactInfo={contactInfo}
        />
      ) : user.rmc_access !== "OUI" && !loading && listConv.length === 0 ? (
        <div className="mt-5">
          <Empty />
        </div>
      ) : null}
    </>
  );
};

export default ChatRmc;
