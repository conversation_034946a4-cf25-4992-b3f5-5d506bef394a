import { memo, useCallback, useEffect, useMemo, useState } from "react";
import { toastNotification } from "components/ToastNotification";
import { Divider, Modal, Segmented, Select, Spin } from "antd";
import { useTranslation } from "react-i18next";
import MainService from "services/main.service";

const ModalMoveMail = ({
  open,
  setOpen,
  record,
  usedAccount,
  setDataSource,
}) => {
  //
  const [t] = useTranslation("common");
  //
  const [type, setType] = useState("1");
  const [dataAccounts, setDataAccounts] = useState([]);
  const [loadingData, setLoadingData] = useState(false);
  const [openSelect, setOpenSelect] = useState(false);

  //   console.log({ dataAccounts });
  //
  const handleCancel = useCallback(() => {
    setOpen({});
    setType("1");
  }, [setOpen]);
  //
  const getMailAccounts = useCallback(async () => {
    if (!open) return;
    try {
      setLoadingData(true);
      const {
        data: { data },
      } = await MainService.getUserstransferred(type);
      const result = [];
      if (type === "1") {
        for (const group in data) {
          const accounts = data[group] || [];
          result.push({
            label: <span className="font-semibold">{group}</span>,
            title: group,
            options: accounts.map(({ accountId, departement_id, email }) => ({
              label: email,
              value: accountId + departement_id + email,
              accountId,
              departmentId: departement_id,
            })),
          });
        }
      } else {
        result.push(
          ...data.map(({ accountId, userId, email }) => ({
            label: email,
            value: userId,
            accountId,
          }))
        );
      }
      setDataAccounts(result);
    } catch (err) {
      toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? `error: ${err.message}` : { err });
    } finally {
      setLoadingData(false);
      setOpenSelect(true);
    }
  }, [open, t, type]);

  useEffect(() => {
    getMailAccounts();
  }, [getMailAccounts]);
  //
  const selectAccount = useCallback(
    async (value, option) => {
      try {
        setLoadingData(true);
        setOpenSelect(false);
        const formData = new FormData();
        formData.append("type_dispatch", type);
        formData.append("emailTransfert", option.label);
        usedAccount.departmentId?.map((e) =>
          formData.append("departement_id[]", e)
        );
        formData.append("account_id", option.accountId);
        formData.append("account_id_source", usedAccount.value);
        formData.append("email_id", record.id);
        const {
          data: { transferred_to },
        } = await MainService.transferEmail(formData);
        setDataSource((prev) =>
          prev.map((item) => {
            if (record.id === item.id)
              return { ...item, transfert: transferred_to };
            return item;
          })
        );
        handleCancel();
        toastNotification("success", t("mailing.movedSuccess"), "topRight", 3);
      } catch (err) {
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
        setOpenSelect(true);
        throw new Error(err?.message ? `error: ${err.message}` : { err });
      } finally {
        setLoadingData(false);
      }
    },
    [record, setDataSource, t, type, usedAccount, handleCancel]
  );
  //
  //
  const modalProps = useMemo(
    () => ({
      title: (
        <div className="relative flex flex-grow flex-col">
          <div className="space-y-1">
            <p className="text-base font-semibold">
              {t("mailing.Tooltip.Move")}
            </p>
            <div className="flex space-x-1 text-sm">
              <p className="whitespace-nowrap font-semibold">
                {t("emailTemplates.subjectMail")}:
              </p>
              <p className="truncate">{record?.subject}</p>
            </div>
          </div>
          <Divider style={{ margin: "5px 0 0 0" }} />
        </div>
      ),
      open: open,
      confirmLoading: "",
      onCancel: handleCancel,
      maskClosable: false,
      footer: null,
    }),
    [record, handleCancel, open, t]
  );
  //
  if (!open || !record) return null;
  //
  return (
    <Modal {...modalProps}>
      <div className="relative w-full space-y-3">
        <Segmented
          block
          value={type}
          onChange={setType}
          options={[
            {
              label: t("mailing.shared"),
              value: "1",
            },
            {
              label: t("mailing.private"),
              value: "0",
            },
          ]}
          disabled={loadingData}
        />
        <Select
          showSearch
          style={{ width: "100%" }}
          optionFilterProp="label"
          placeholder={
            <span
              dangerouslySetInnerHTML={{
                __html: t("mailing.searchAndSelect", {
                  type:
                    type === "1" ? t("mailing.shared") : t("mailing.private"),
                }),
              }}
            />
          }
          suffixIcon={<Spin spinning={loadingData} size="small" />}
          disabled={loadingData}
          options={dataAccounts}
          open={openSelect}
          onSelect={selectAccount}
        />
      </div>
    </Modal>
  );
};

export default memo(ModalMoveMail);
