import {
  GET_LOG_SUCCESS,
  IS_LOADING_LOG,
  GET_PARAM_SUCCESS,
  GET_PARAM_ERROR,
  GET_USER_SUCCESS,
  GET_USER_ERROR,
  GET_VOICE_SUCCESS,
  GET_VOICE_ERROR,
  IS_LOADING_VOICE,
  GET_INTERNALCALLFORWARD_SUCCESS,
  GET_INTE<PERSON><PERSON><PERSON>LFORWARD_ERROR,
  SET_INTE<PERSON><PERSON><PERSON><PERSON>ORWARD_SUCCESS,
  SET_DEVICE_RINGING_OUTPUT,
  SET_INTERNALCALLFORWARD_ERROR,
  GET_STATUS_SUCCESS,
  GET_STATUS_ERROR,
  RESET_STATE,
  CHANGE_STATUS,
  GET_NOTIF_SUCCESS,
  GET_NOTIF_ERROR,
  GET_PHONE_BOOK,
  SET_NBR_VOICES_CALLS_QUEUES,
  SET_NEW_MISSED_CALL,
  SET_NEW_MISSED_QUEUE_GROUP,
  SET_NEW_VOICE_MESSAGING,
  RESET_MISSED_CALL,
  RESET_VOICE_MESSAGING,
  RESET_VOICE_MESSAGING_AND_MISSED_CALL_AND_QUEUE,
  SET_RETURNED_QUEUE_MISSED_CALL,
  SET_NEW_LOG_GROUPS_QUEUES,
  RESET_LOG_GROUPS_QUEUES,
  RESET_MISSED_QUEUE_GROUP,
  SET_NEW_TAG_GROUPS_QUEUES,
  RESET_TAG_GROUPS_QUEUES,
  SET_NEW_CALL_LOG,
  SET_LAST_CALL_LOG,
  RESET_LAST_CALL_LOG,
  OPEN_DRAWER_CHAT,
  CLOSE_DRAWER_CHAT,
  SET_CALL_FIRST_TIME,
  SET_IP_ADDRESS,
  SET_LOADING_TRUE,
  SET_LOADING_FALSE,
  SET_NEW_LOG_GROUPS_QUEUES_IN_PROCESS,
} from "../constants";
const initialState = {
  notifMissedCall: [],
  statusVoip: [],
  makeInternalforward: [],
  internalforward: [],
  voice: [],
  logs: [],
  newLogGroupsQueues: [],
  newTagOrAffectationGroupsQueues: [],
  callsInProcess: [],
  returnedQueueMissedCall: false,
  totalLogs: 0,
  param: null,
  loadingParm: false,
  collegues: [],
  loading: false,
  phoneBook: [],
  nbrMissedCalls: 0,
  nbrVoiceMessaging: 0,
  nbrMissedQueueGroup: 0,
  lastCallLog: null,
  openDrawerChat: false,
  selectedExternalConv: null,
  outputDeviceId: null,
  addressIp: null,
};

const voip = (state = initialState, action) => {
  const { type, payload } = action;
  switch (type) {
    case SET_IP_ADDRESS:
      return {
        ...state,
        addressIp: payload,
      };
    // case SET_NEW_CALL_LOG:
    case SET_RETURNED_QUEUE_MISSED_CALL:
      return {
        ...state,
        returnedQueueMissedCall: payload,
      };
    case SET_NEW_LOG_GROUPS_QUEUES:
      return {
        ...state,
        newLogGroupsQueues: [...payload, ...state.newLogGroupsQueues],
        // newLogGroupsQueues: [
        //   ...Array.from(
        //     new Map(
        //       [...payload, ...state.newLogGroupsQueues].map((item) => [
        //         item.id_appel,
        //         item,
        //       ])
        //     ).values()
        //   ),
        // ],
      };
    case SET_NEW_LOG_GROUPS_QUEUES_IN_PROCESS:
      return {
        ...state,
        newLogGroupsQueues: payload?.isQueue
          ? [payload.data, ...state.newLogGroupsQueues]
          : [],
        callsInProcess: [payload.data, ...state.callsInProcess.slice(0, 50)],
      };
    case RESET_LOG_GROUPS_QUEUES:
      return {
        ...state,
        newLogGroupsQueues: [],
      };

    case SET_NEW_TAG_GROUPS_QUEUES:
      return {
        ...state,
        newTagOrAffectationGroupsQueues: [
          ...state.newTagOrAffectationGroupsQueues,
          ...payload,
        ],
      };
    case RESET_TAG_GROUPS_QUEUES:
      return {
        ...state,
        newTagOrAffectationGroupsQueues: [],
      };
    case IS_LOADING_LOG:
      return {
        ...state,
        loading: !state.loading,
      };
    case IS_LOADING_VOICE:
      return {
        ...state,
        loading: payload,
      };
    case GET_LOG_SUCCESS:
      return {
        ...state,
        logs: payload?.data || [],
        totalLogs: payload?.total
          ? payload?.total
          : payload?.data?.length
          ? payload.data.length
          : initialState?.total,
        loading: false,
      };
    case GET_PARAM_SUCCESS:
      return {
        ...state,
        param: payload,
      };
    case GET_PARAM_ERROR:
      return {
        ...state,
        param: payload,
      };
    case GET_USER_SUCCESS:
      return {
        ...state,
        collegues: payload,
      };
    case GET_USER_ERROR:
      return {
        ...state,
        collegues: payload,
      };
    case GET_VOICE_SUCCESS:
      return {
        ...state,
        voice: payload,
        loading: false,
      };
    case GET_VOICE_ERROR:
      return {
        ...state,
        voice: [],
        loading: false,
      };

    case GET_INTERNALCALLFORWARD_SUCCESS:
      return {
        ...state,
        internalforward: payload,
      };

    case GET_INTERNALCALLFORWARD_ERROR:
      return {
        ...state,
        internalforward: payload,
      };

    case SET_INTERNALCALLFORWARD_SUCCESS:
      return {
        ...state,
        internalforward: payload,
      };

    case SET_INTERNALCALLFORWARD_ERROR:
      return {
        ...state,
        makeInternalforward: payload,
      };
    case SET_DEVICE_RINGING_OUTPUT:
      return {
        ...state,
        outputDeviceId: payload,
      };

    case GET_STATUS_SUCCESS:
      return {
        ...state,
        statusVoip: payload.map((item) => ({
          poste: item.poste,
          status: item.etat,
        })),
      };

    case CHANGE_STATUS:
      return {
        ...state,
        statusVoip: state.statusVoip.map((item) =>
          item.poste === payload[1] ? { ...item, status: payload[0] } : item
        ),
      };

    case GET_STATUS_ERROR:
      return {
        ...state,
        statusVoip: [],
      };

    case GET_NOTIF_SUCCESS:
      return {
        ...state,
        notifMissedCall: payload,
      };

    case GET_NOTIF_ERROR:
      return {
        ...state,
        notifMissedCall: [],
      };

    case GET_PHONE_BOOK:
      return {
        ...state,
        phoneBook: payload,
      };

    case SET_LAST_CALL_LOG:
      return {
        ...state,
        lastCallLog: payload,
      };

    case RESET_LAST_CALL_LOG:
      return {
        ...state,
        lastCallLog: null,
      };

    case SET_NEW_CALL_LOG:
      return {
        ...state,
        logs: [...payload, ...state.logs.slice(0, 100)],
        loading: false,
      };

    case SET_NEW_MISSED_CALL: {
      return {
        ...state,
        nbrMissedCalls: state?.nbrMissedCalls + payload?.nbr,
      };
    }

    case SET_CALL_FIRST_TIME: {
      if (!payload || !payload._id || !state.logs.length) return;
      const callId = payload?._id;
      const updatedCallLog = state.logs.map((call) =>
        call?._id !== callId ? call : payload
      );
      return {
        ...state,
        logs: updatedCallLog,
      };
    }

    case SET_NEW_MISSED_QUEUE_GROUP: {
      return {
        ...state,
        nbrMissedQueueGroup: state?.nbrMissedQueueGroup + payload?.nbr,
      };
    }

    case RESET_MISSED_CALL: {
      return {
        ...state,
        nbrMissedCalls: 0,
      };
    }

    case RESET_MISSED_QUEUE_GROUP: {
      return {
        ...state,
        nbrMissedQueueGroup: 0,
      };
    }

    case SET_NEW_VOICE_MESSAGING: {
      const updatedState = {
        ...state,
        nbrVoiceMessaging:
          state.nbrVoiceMessaging + (Number(payload?.nbr) || 0),
      };
      if (payload?.data) {
        const data = payload.data[0];
        updatedState.voice = [data, ...state.voice];
        updatedState.logs = state.logs.length
          ? state.logs.map((log) => {
              return log.id_appel === data.id_appel
                ? { ...log, voice_mail: data }
                : log;
            })
          : state.logs;
      }
      return updatedState;
    }

    case RESET_VOICE_MESSAGING: {
      return {
        ...state,
        nbrVoiceMessaging: 0,
      };
    }

    case SET_NBR_VOICES_CALLS_QUEUES:
      return {
        ...state,
        nbrVoiceMessaging: payload?.nbr_missed_voices || 0,
        nbrMissedCalls: payload?.nbr_missed_calls || 0,
        nbrMissedQueueGroup: payload?.nbr_missed_queues || 0,
      };

    case RESET_VOICE_MESSAGING_AND_MISSED_CALL_AND_QUEUE: {
      return {
        ...state,
        nbrVoiceMessaging: 0,
        nbrMissedCalls: 0,
        nbrMissedQueueGroup: 0,
      };
    }

    case OPEN_DRAWER_CHAT:
      return {
        ...state,
        selectedExternalConv: payload || null,
        openDrawerChat: true,
      };

    case CLOSE_DRAWER_CHAT:
      return {
        ...state,
        selectedExternalConv: null,
        openDrawerChat: false,
      };

    case SET_LOADING_TRUE:
      return {
        ...state,
        loading: true,
      };

    case SET_LOADING_FALSE:
      return {
        ...state,
        loading: false,
      };

    case RESET_STATE: {
      return initialState;
    }

    default:
      return state;
  }
};

export default voip;
