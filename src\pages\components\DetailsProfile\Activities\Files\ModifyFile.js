import { Form, Upload, Space, Button, message } from "antd";
import React, { useState } from "react";
import { renderFileSize } from "./utlis";
import { FileTextOutlined, InboxOutlined } from "@ant-design/icons";
import MainService from "../../../../../services/main.service";
import { useDispatch } from "react-redux";
import { modifyFile360Name } from "../../../../../new-redux/actions/files.actions/files";
import { useTranslation } from "react-i18next";
import { toastNotification } from "components/ToastNotification";

function ModifyFile({ fileToModify, setFileToModify }) {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const [t] = useTranslation("common");

  const [fileToUpload, setFileToUpload] = useState(null); // [
  const [uploading, setUploading] = useState(false);

  const normFile = (e) => {
   // console.log("Upload event:", e);
    if (e?.file?.size > 10485760) {
      message.error(t("files360.fileMaxSizeMsg"));
      return null;
    }

    if (Array.isArray(e)) {
      return e;
    }
    return e?.fileList;
  };

  const onFinish = (values) => {
    setUploading(true);
    //console.log("Received values of form: ", values);

    let formData = new FormData();
    // formData.append("file_label", values.label);
    formData.append("file_name", values.dragger[0].name);
    formData.append("upload", values.dragger[0].originFileObj);

    // console.log("FORMDATA", fileToModify?._id["$oid"]);

    MainService.updateFile360(fileToModify?._id, formData)
      .then((response) => {
        //console.log(response?.data?.message?.collection);
        let collection = response?.data?.message?.collection;
        collection.creator = collection?.creator[0];
        dispatch(modifyFile360Name(collection));
        // message.success("File modified successfully");
        toastNotification(
          "success",
          t("files360.updateFile", {
            filename: fileToModify.file_label,
          })
        );
        setFileToUpload(null);
        setFileToModify(null);
      })
      .catch((error) => {
        console.log(error);
      })
      .finally(() => {
        setUploading(false);
      });
  };

  return (
    <div className="mt-4">
      <hr
        style={{
          color: "#E5E7EB",
        }}
      />

      <Form layout="vertical" form={form} onFinish={onFinish}>
        <Form.Item label={t("files360.modifyFile")}>
          <Form.Item
            name="dragger"
            valuePropName="fileList"
            getValueFromEvent={normFile}
            noStyle
            rules={
              [
                //   {
                //     required: true,
                //     message: "Please upload your file!",
                //   },
                //   {
                //     validator: (_, value) =>
                //       value?.fileList?.length <= 1
                //         ? Promise.resolve()
                //         : Promise.reject(new Error("You can only upload one file")),
                //   },
                //   {
                //     validator: (_, value) =>
                //       //file size must be less than 10MB
                //       value?.file?.size <= 10485760
                //         ? Promise.resolve(
                //             console.log("FILE on validator", value?.file),
                //             //remove the error message
                //             form.setFields([
                //               {
                //                 name: "dragger",
                //                 errors: [],
                //               },
                //             ])
                //           )
                //         : Promise.reject(
                //             new Error("File size must be less than 10MB")
                //           ),
                //   },
              ]
            }
          >
            <Upload.Dragger
              name="files"
              onChange={(info) => {
                //file size must be less than 10MB
                console.log("INFOS", info);
                if (info?.file?.size <= 10485760) {
                  setFileToUpload(info?.file);
                }
              }}
              accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.jpg,.png,.zip"
              onPreview={(file) => {
                console.log("FILE", file);
              }}
              showUploadList={false}
              multiple={false}
            >
              {fileToUpload ? (
                <div className="flex flex-col items-center justify-center">
                  <FileTextOutlined className="text-4xl" />
                  <p className="text-md">{fileToUpload.name}</p>
                  <p className="text-md">{renderFileSize(fileToUpload.size)}</p>
                </div>
              ) : (
                <>
                  <p className="ant-upload-drag-icon">
                    <InboxOutlined />
                  </p>
                  <p className="ant-upload-text">
                    {t("files360.filePlaceHolder")}
                  </p>
                </>
              )}
            </Upload.Dragger>
          </Form.Item>
        </Form.Item>

        <Form.Item
          //   wrapperCol={{
          //     span: 12,
          //     offset: 6,
          //   }}
          className="text-right"
        >
          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={uploading}
              disabled={fileToUpload != null ? false : true}
            >
              {t("files360.uploadSubmit")}
            </Button>
            <Button
              htmlType="reset"
              onClick={() => {
                // form.resetFields();
                setFileToUpload(null);
                form.setFieldsValue({ dragger: [] });
              }}
              disabled={uploading}
            >
              {t("files360.uploadReset")}
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </div>
  );
}

export default ModifyFile;
