import React from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";

const LineChart = ({ pivotData }) => {
  if (
    !pivotData ||
    !pivotData.rowKeys ||
    !pivotData.colKeys ||
    !pivotData.getAggregator
  ) {
    return <p className="text-center text-red-600">No Data</p>;
  }

  const rowKeys = pivotData.rowKeys;
  const colKeys = pivotData.colKeys;

  if (rowKeys.length === 0 && colKeys.length === 0) {
    return <p className="text-center text-red-600">No Data</p>;
  }

  const isRow = rowKeys.length > 0;
  const baseKeys = isRow ? rowKeys : colKeys;
  const seriesKeys = isRow ? colKeys : rowKeys;

  const categories = baseKeys.map((key) => key.join(" - ") || "Total");

  const series =
    seriesKeys.length > 0
      ? seriesKeys.map((seriesKey) => ({
          name: seriesKey.join(" - ") || "Total",
          data: baseKeys.map((baseKey) => {
            const r = isRow ? baseKey : seriesKey;
            const c = isRow ? seriesKey : baseKey;
            return pivotData.getAggregator(r, c)?.value() || 0;
          }),
        }))
      : [
          {
            name: "Total",
            data: baseKeys.map((baseKey) => {
              const r = isRow ? baseKey : [];
              const c = isRow ? [] : baseKey;
              return pivotData.getAggregator(r, c)?.value() || 0;
            }),
          },
        ];

  const options = {
    chart: {
      type: "line",
    },
    title: {
      text: `📊
            ${pivotData.props.rows.length ? pivotData.props.rows.join(" ") : ""}
            ${
              pivotData.props.cols.length
                ? " : " + pivotData.props.cols.join(" ")
                : ""
            }`,
      align: "left",
      dispalay: "block",
      fontFamily: "Arial, sans-serif",
      style: {
        fontWeight: "normal",
        fontSize: "20px",
        fontFamily: "Inter, sans-serif",
      },
    },
    xAxis: {
      categories,
    },
    legend: {
      layout: "vertical",
      align: "right",
      verticalAlign: "middle",
    },
    series,
    responsive: {
      rules: [
        {
          condition: {
            maxWidth: 500,
          },
          chartOptions: {
            legend: {
              layout: "horizontal",
              align: "center",
              verticalAlign: "bottom",
            },
          },
        },
      ],
    },
    plotOptions: {
      line: {
        dataLabels: {
          enabled: true,
        },
        enableMouseTracking: false,
      },
    },
    credits: {
      enabled: false,
    },
  };

  return (
    <div
      id="container"
      style={{ width: "100%", height: "100%" }}
      className="highcharts-figure"
    >
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
};

export default LineChart;
