import React from "react";
import { HiOutlineVideoCamera } from "react-icons/hi2";
import { moment_timezone } from "App";
import { Divider, Tooltip, Typography } from "antd";
import { useTranslation } from "react-i18next";
import "./meetEvent.css";

const MeetEvent = ({ dataMeet }) => {
  const { Text } = Typography;
  const { t } = useTranslation("common");
  return (
    <div>
      <div className="my-2 w-[98%] rounded-md border-solid border-[#F2F2F2] p-3">
        <div className="flex flex-row justify-between">
          <div className="flex items-center  ">
            <div className="event-date-badge">
              <div className="event-month">MAY</div>
              <div className="event-day">22</div>
              <div className="event-weekday">Tues</div>
            </div>
            <div className="ml-[15px] flex flex-col">
              <div className="flex items-center space-x-4">
                <a target="_blank" href={dataMeet.meetLink} rel="noreferrer">
                  {dataMeet.title}
                </a>

                <Text italic type="secondary">
                  {t("chat.header.visio.timeToStart", {
                    time1: moment_timezone(dataMeet.start).fromNow(),
                    time2: moment_timezone(dataMeet.end).format("HH:mm"),
                  })}
                </Text>
              </div>
              <Text italic type="secondary">
                {t("chat.header.visio.created_by") + " " + dataMeet.organizer}
              </Text>
            </div>
          </div>
        </div>
        <>
          <Divider className="my-2" />
          <div className="flex items-center justify-between space-x-0.5 ">
            <div className="flex flex-col justify-between ">
              <div className="ml-[10px] flex flex-row space-x-[20px]">
                <Text italic type="secondary">
                  {t("mailing.Date")}
                </Text>
                <div className="ml-0.5 flex items-center">
                  <p className="font-medium">{dataMeet.start}</p>
                </div>
              </div>
              <div className="ml-[10px] flex flex-row space-x-[20px]">
                <Text italic type="secondary">
                  {t("mailing.Lieu")}
                </Text>
                <p className="font-medium">{dataMeet.location}</p>
              </div>
              {dataMeet.attendees.length > 0 ? (
                <div className="ml-[10px] flex flex-row space-x-[20px]">
                  <Text italic type="secondary">
                    {t("mailing.Qui")}
                  </Text>
                  <div className="ml-0.5 flex items-center truncate">
                    {/* <Tooltip
                      title={dataMeet.attendees
                        .map((item) => item.slice(7))
                        .join(", ")}
                    > */}
                    <p className="m truncate ">
                      {dataMeet.attendees.map((item) => item).join(", ")}
                    </p>
                    {/* </Tooltip> */}
                  </div>
                </div>
              ) : null}
            </div>
          </div>
        </>
      </div>
    </div>
  );
};

export default MeetEvent;
