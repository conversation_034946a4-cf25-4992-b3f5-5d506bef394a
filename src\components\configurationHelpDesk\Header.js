import { ExportOutlined, PlusOutlined } from "@ant-design/icons";
import { Button, Space, Tooltip } from "antd";
import { GenericButton } from "../../pages/components/GenericButton";
import SearchInTable from "../../pages/components/Search";
import HelpDesk from "../../pages/settings/HelpDesk";
import { useSelector } from "react-redux";
import { useLocation } from "react-router-dom";
import { generateAxios } from "../../services/axiosInstance";
import { useEffect, useRef, useState } from "react";
import FilterTable from "../FilterTable";
import { useTranslation } from "react-i18next";
import { URL_ENV } from "index";

const Header = ({
  active,
  handleAdd,
  editingKey,
  btnText,
  disabled,
  shape,
  data = [],
  api = "",
  tasks = [],
  setFilter = () => {},
  filters = [],
  count = { all: 1 },
  selectedFilter = [],
}) => {
  const { isDeleteRows } = useSelector((state) => state.table);
  const { search } = useSelector((state) => state.form);
  const [loading, setLoading] = useState(false);
  const { pathname } = useLocation();
  const divRef = useRef(null);
  const [divWidth, setDivWidth] = useState(null);
  useEffect(() => {
    const updateDivWidth = () => {
      if (divRef.current) {
        const width = divRef.current.offsetWidth;
        setDivWidth(width);
      }
    };

    window.addEventListener("resize", updateDivWidth);
    updateDivWidth();

    return () => {
      window.removeEventListener("resize", updateDivWidth);
    };
  }, []);
  const [t] = useTranslation("common");
  const [openFilterTable, setOpenFilterTable] = useState(false);
  function convertToCSV(data) {
    const headers = Object.keys(data[0]);
    const rows = data.map((item) => {
      const companies = item.companies
        ? item.companies.map((c) => c.label).join(",")
        : "";
      return headers.map((header) =>
        header === "companies" ? companies : item[header]
      );
    });
    return [headers.join(","), ...rows.map((row) => row.join(","))].join("\n");
  }
  function downloadCSV(data) {
    const csv = convertToCSV(data);
    const url = window.URL.createObjectURL(new Blob([csv]));
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", "donnees.csv");
    document.body.appendChild(link);
    link.click();
  }
  const handleDownload = async () => {
    setLoading(true);
    try {
      const response = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).get(`${api}?export=1&search=${search}`, { responseType: "blob" });
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", api + ".xlsx");
      document.body.appendChild(link);
      link.click();
      setLoading(false);
    } catch (error) {
      setLoading(false);
      console.log(error);
    }
  };
  return (
    <div>
      {/* <HelpDesk active={active} /> */}
      <div
        className={`flex w-full justify-between pr-4 transition-all duration-200 ease-in ${
          !pathname.includes("pipeline") && isDeleteRows ? "pl-[220px]" : "pl-4"
        }  `}
      >
        {api === "accessToken" ? null : count?.all > 0 &&
          api !== "taxes" &&
          api !== "discounts" ? (
          <SearchInTable />
        ) : (
          <SearchInTable disabled={true} />
        )}
        {(api === "tasks-360" || api === "pipelines") &&
          ((count?.all > 0 && count?.types == null) ||
            count?.types != null) && (
            <div
              className="pl-1"
              // className={`${
              //   disabled && count?.types === null ? "invisible" : "visible"
              // }`}
            >
              <FilterTable
                setFilter={setFilter}
                filters={filters}
                t={t}
                openFilterTable={openFilterTable}
                setOpenFilterTable={setOpenFilterTable}
                disabled={disabled}
                selectedFilter={selectedFilter}
                tasks={tasks}
                widthTooltip={divWidth}
              />
            </div>
          )}

        {/* <div ref={divRef} className="w-full"></div> */}
        <div className="ml-auto flex space-x-2">
          {/* {api === "departments" ||
          api === "services" ||
          api === "channels" ||
          api === "groupe-wikis" ||
          api === "companies" ? (
            <Button
              icon={<ExportOutlined />}
              onClick={() =>
                // downloadCSV(
                //   data.map(
                //     ({
                //       rank,
                //       created_at,
                //       updated_at,
                //       deleted_at,
                //       key,
                //       field_module_id,
                //       ...rest
                //     }) => rest
                //   )
                // )
                handleDownload()
              }
              loading={loading}
            >
              Export data
            </Button>
          ) : (
            ""
          )} */}
          <div className="flex space-x-2">
            {" "}
            <Tooltip title={btnText} placement="left">
              <span>
                <GenericButton
                  onClick={handleAdd}
                  type="primary"
                  disabled={disabled}
                  // text={btnText}
                  shape="circle"
                />
              </span>
            </Tooltip>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Header;
