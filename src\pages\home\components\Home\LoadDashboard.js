import React, { useState, useEffect } from "react";

const DashboardSkeletonCard = ({
  variant = "chart",
  title,
  hasIcon = true,
}) => {
  return (
    <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
      {/* Content based on variant */}
      {variant === "chart" && (
        <div className="space-y-4">
          {/* Stats row */}
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="h-6 w-12 animate-pulse rounded bg-blue-200"></div>
                <div className="h-5 w-8 animate-pulse rounded bg-gray-300"></div>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="h-6 w-12 animate-pulse rounded bg-red-200"></div>
                <div className="h-5 w-8 animate-pulse rounded bg-gray-300"></div>
              </div>
            </div>
          </div>
          {/* Pie chart placeholder */}
          <div className="flex justify-between  gap-x-2 pt-1">
            <div className="w-full flex-1 justify-between px-2 first:flex ">
              <div className="flex h-auto flex-col content-around justify-between">
                <div className="flex items-center space-x-2">
                  <div className="h-4 w-16 animate-pulse rounded bg-gray-300"></div>
                  <div className="h-3 w-12 animate-pulse rounded bg-gray-200"></div>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="h-4 w-16 animate-pulse rounded bg-gray-300"></div>
                  <div className="h-3 w-12 animate-pulse rounded bg-gray-200"></div>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="h-4 w-16 animate-pulse rounded bg-gray-300"></div>
                  <div className="h-3 w-12 animate-pulse rounded bg-gray-200"></div>
                </div>
              </div>
              <div className="flex h-auto flex-col content-around justify-between">
                <div className="flex items-center space-x-2">
                  <div className="h-4 w-16 animate-pulse rounded bg-gray-300"></div>
                  <div className="h-3 w-12 animate-pulse rounded bg-gray-200"></div>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="h-4 w-16 animate-pulse rounded bg-gray-300"></div>
                  <div className="h-3 w-12 animate-pulse rounded bg-gray-200"></div>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="h-4 w-16 animate-pulse rounded bg-gray-300"></div>
                  <div className="h-3 w-12 animate-pulse rounded bg-gray-200"></div>
                </div>
              </div>
            </div>
            <div className="align-center border-1 flex flex-1 justify-center ">
              <div className="relative h-36 w-36  animate-pulse rounded-full bg-gray-200 px-5">
                <div className="absolute inset-4 rounded-full bg-white"></div>
              </div>
            </div>
          </div>
        </div>
      )}

      {variant === "social" && (
        <div className="space-y-3">
          {/* Social media items */}
          {[
            { color: "bg-green-500", count: "71" },
            { color: "bg-blue-500", count: "43" },
            { color: "bg-blue-600", count: "5" },
            { color: "bg-pink-500", count: "1" },
          ].map((item, i) => (
            <div key={i} className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div
                  className={`h-6 w-6 rounded ${item.color} animate-pulse opacity-60`}
                ></div>
                <div className="h-4 w-20 animate-pulse rounded bg-gray-300"></div>
              </div>
              <div className="h-5 w-8 animate-pulse rounded bg-gray-300"></div>
            </div>
          ))}
          {/* Pie chart */}
          <div className="mt-4 flex justify-center">
            <div className="h-24 w-24 animate-pulse rounded-full bg-gray-200"></div>
          </div>
        </div>
      )}

      {variant === "donut" && (
        <div className="space-y-4">
          {/* Center donut chart */}
          <div className="flex justify-center">
            <div className="relative h-40 w-40">
              <div className="h-40 w-40 animate-pulse rounded-full bg-gray-200"></div>
              <div className="absolute inset-8 flex items-center justify-center rounded-full bg-white">
                <div className="text-center">
                  <div className="mb-1 h-6 w-12 animate-pulse rounded bg-gray-300"></div>
                  <div className="h-3 w-16 animate-pulse rounded bg-gray-200"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {variant === "stats" && (
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="h-3 w-12 animate-pulse rounded bg-gray-200"></div>
              <div className="h-6 w-16 animate-pulse rounded bg-blue-300"></div>
            </div>
            <div className="space-y-2">
              <div className="h-3 w-16 animate-pulse rounded bg-gray-200"></div>
              <div className="h-6 w-20 animate-pulse rounded bg-green-300"></div>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="h-3 w-14 animate-pulse rounded bg-gray-200"></div>
              <div className="h-6 w-12 animate-pulse rounded bg-gray-300"></div>
            </div>
            <div className="space-y-2">
              <div className="h-3 w-10 animate-pulse rounded bg-gray-200"></div>
              <div className="h-5 w-14 animate-pulse rounded bg-gray-300"></div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

const ActivityListSkeleton = () => (
  <div className="rounded-lg border border-gray-200 bg-white shadow-sm">
    {/* Header */}
    <div className="flex items-center justify-between border-b border-gray-100 p-4">
      <div className="flex items-center space-x-2">
        <div className="h-4 w-4 animate-pulse rounded bg-gray-300"></div>
        <div className="h-4 w-24 animate-pulse rounded bg-gray-300"></div>
        <div className="h-5 w-8 animate-pulse rounded-full bg-green-200"></div>
      </div>
      <div className="h-6 w-6 animate-pulse rounded bg-gray-200"></div>
    </div>

    {/* Activity type stats */}
    <div className="space-y-3 p-4">
      {[
        { icon: "bg-yellow-500", count: "34" },
        { icon: "bg-green-500", count: "17" },
        { icon: "bg-blue-500", count: "1" },
        { icon: "bg-blue-600", count: "0" },
        { icon: "bg-blue-400", count: "0" },
        { icon: "bg-purple-500", count: "0" },
        { icon: "bg-red-500", count: "0" },
      ].map((item, i) => (
        <div key={i} className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div
              className={`h-4 w-4 rounded ${item.icon} animate-pulse opacity-60`}
            ></div>
            <div className="h-4 w-16 animate-pulse rounded bg-gray-300"></div>
          </div>
          <div className="h-4 w-6 animate-pulse rounded bg-gray-300"></div>
        </div>
      ))}
    </div>
  </div>
);

const RecentActivitiesSkeleton = () => (
  <div className="rounded-lg border border-gray-200 bg-white shadow-sm">
    {/* Header */}
    <div className="flex items-center justify-between border-b border-gray-100 p-4">
      <div className="h-4 w-20 animate-pulse rounded bg-gray-300"></div>
      <div className="h-8 w-32 animate-pulse rounded bg-blue-200"></div>
    </div>

    {/* Activity items */}
    <div className="space-y-2 px-2 pb-1 pt-1.5">
      {[...Array(4)].map((_, i) => (
        <div key={i} className="flex items-start space-x-3">
          <div className="h-8 w-8 animate-pulse rounded-full bg-blue-500 opacity-60"></div>
          <div className="flex-1 space-y-2">
            <div className="flex items-center space-x-2">
              <div className="h-4 w-12 animate-pulse rounded bg-gray-300"></div>
              <div className="h-3 w-24 animate-pulse rounded bg-gray-200"></div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-1">
                <div className="h-3 w-16 animate-pulse rounded bg-gray-200"></div>
                <div className="h-5 w-5 animate-pulse rounded-full bg-gray-200"></div>
              </div>
              <div className="flex items-center space-x-1">
                <div className="h-3 w-20 animate-pulse rounded bg-gray-200"></div>
                <div className="h-5 w-5 animate-pulse rounded-full bg-gray-200"></div>
              </div>
              <div className="flex items-center space-x-1">
                <div className="h-3 w-12 animate-pulse rounded bg-gray-200"></div>
                <div className="h-5 w-5 animate-pulse rounded-full bg-gray-200"></div>
              </div>
            </div>
          </div>
          <div className="h-3 w-24 animate-pulse rounded bg-gray-200"></div>
        </div>
      ))}
    </div>
  </div>
);

const ChartSkeleton = ({ type = "bar" }) => (
  <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
    {/* Header */}
    <div className="mb-4 flex items-center justify-between">
      <div className="h-4 w-32 animate-pulse rounded bg-gray-300"></div>
      <div className="h-6 w-6 animate-pulse rounded bg-gray-200"></div>
    </div>

    {type === "bar" && (
      <div className="space-y-4">
        {/* Legend */}
        <div className="flex justify-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className="h-3 w-3 animate-pulse rounded bg-green-500"></div>
            <div className="h-3 w-12 animate-pulse rounded bg-gray-200"></div>
          </div>
          <div className="flex items-center space-x-2">
            <div className="h-3 w-3 animate-pulse rounded bg-red-500"></div>
            <div className="h-3 w-16 animate-pulse rounded bg-gray-200"></div>
          </div>
          <div className="flex items-center space-x-2">
            <div className="h-3 w-3 animate-pulse rounded bg-blue-500"></div>
            <div className="h-3 w-14 animate-pulse rounded bg-gray-200"></div>
          </div>
        </div>

        {/* Bar chart */}
        <div className="flex h-48 items-end justify-evenly space-x-4">
          <div className="space-y-2 text-center">
            <div className="h-32 w-12 animate-pulse rounded-t bg-green-300"></div>
            <div className="h-3 w-12 animate-pulse rounded bg-gray-200"></div>
          </div>
          <div className="space-y-2 text-center">
            <div className="h-32 w-12 animate-pulse rounded-t bg-red-300"></div>
            <div className="h-3 w-12 animate-pulse rounded bg-gray-200"></div>
          </div>
          <div className="space-y-2 text-center">
            <div className="h-20 w-12 animate-pulse rounded-t bg-blue-300"></div>
            <div className="h-3 w-12 animate-pulse rounded bg-gray-200"></div>
          </div>
        </div>
      </div>
    )}

    {type === "line" && (
      <div className="space-y-4">
        {/* Legend */}
        <div className="flex justify-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className="h-3 w-3 animate-pulse rounded bg-blue-500"></div>
            <div className="h-3 w-16 animate-pulse rounded bg-gray-200"></div>
          </div>
          <div className="flex items-center space-x-2">
            <div className="h-3 w-3 animate-pulse rounded bg-purple-500"></div>
            <div className="h-3 w-20 animate-pulse rounded bg-gray-200"></div>
          </div>
        </div>

        {/* Line chart bars */}
        <div className="flex h-32 items-end justify-between px-4">
          {[...Array(9)].map((_, i) => (
            <div key={i} className="flex flex-col items-center space-y-1">
              <div
                className="w-8 animate-pulse rounded-t bg-blue-300"
                style={{ height: `${20 + Math.random() * 80}px` }}
              ></div>
              <div className="h-2 w-8 animate-pulse rounded bg-gray-200"></div>
            </div>
          ))}
        </div>
      </div>
    )}
  </div>
);

export default function DashboardLoadingSkeleton() {
  return (
    <div className="min-h-screen bg-gray-50 p-4">
      {/* Header */}

      {/* Main Grid */}
      <div className="grid-col-2 grid gap-6">
        {/* Left Column */}
        <div className="space-y-6">
          {/* Discussions and Social Networks Row */}
          <div className="grid grid-cols-2 gap-4">
            <DashboardSkeletonCard variant="chart" />
            <DashboardSkeletonCard variant="chart" />
          </div>
        </div>
        <div className="grid grid-cols-4 gap-4">
          <div className="col-span-1">
            <ActivityListSkeleton />
          </div>
          <div className="col-span-3">
            <RecentActivitiesSkeleton />
          </div>
        </div>
        <div className="grid grid-cols-2 gap-4">
          <ChartSkeleton type="bar" />
          <ChartSkeleton type="line" />
        </div>
      </div>
    </div>
  );
}
