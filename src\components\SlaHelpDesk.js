import { Form, Space } from "antd";
import axios from "axios";
import { type } from "jquery";
import { useEffect, useMemo, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { setSearch } from "../new-redux/actions/menu.actions/menu";
import DrawerEmail from "../pages/mailing/DrawerEmail";
import { generateAxios } from "../services/axiosInstance";
import Header from "./configurationHelpDesk/Header";
import DrawerSla from "./DrawerSla";
import TableConfigMail from "./TableConfigMail";
import TableSla from "./TableSla";
import { toastNotification } from "./ToastNotification";
import { URL_ENV } from "index";

const SlaHelpDesk = () => {
  const [toggle, setToggle] = useState(null);
  const [loading, setLoading] = useState(true);
  const [labelId, setLabelId] = useState(null);
  const [primaryAccount, setPrimaryAccount] = useState(null);
  const [open, setOpen] = useState(false);
  const [count, setCount] = useState(0);
  const [usedContacts, setUsedContacts] = useState([]);
  const [data, setData] = useState([]);
  const [configEmail, setConfigEmail] = useState({});
  const [debounceValue, setDebounceValue] = useState("");
  const [severities, setSeverities] = useState([]);
  const buttonSubmit = useRef();
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const [loadSwitch, setLoadSwitch] = useState(false);
  const [switchId, setSwitchId] = useState("");
  const formRef = useRef(null);
  const [typeSla, setType] = useState("");
  const [confirmLoading, setConfirmLoading] = useState(false);

  const { search } = useSelector((state) => state.form);

  const onClose = () => {
    formRef.current.resetFields();
  };
  // useEffect(() => {
  //   const timer = setTimeout(() => setDebounceValue(search), 300);

  //   return () => {
  //     clearTimeout(timer);
  //   };
  // }, [search]);
  useEffect(() => {
    function generateRandomColor() {
      let maxVal = 0xffffff; // 16777215
      let randomNumber = Math.random() * maxVal;
      randomNumber = Math.floor(randomNumber);
      randomNumber = randomNumber.toString(16);
      let randColor = randomNumber.padStart(6, 0);
      return `#${randColor.toUpperCase()}`;
    }

    const getData = async () => {
      setLoading(true);
      try {
        const {
          data: { data },
        } = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get(
          `/slas
          `
        );
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get(
          `/severities
          `
        );

        setData((prev) =>
          data.sla.map((el) => ({
            ...el,
            list_contact: el.list_contact.map((em) => ({
              ...em,
              color: em.color ? em.color : generateRandomColor(),
            })),
          }))
        );
        setSeverities(res.data.data);
        setType(data?.sla.find((el) => el.status == 1)?.sla_type);
        // setUsedContacts({
        //   ...data
        //     .filter((el) => el?.list_contact?.length > 0)
        //     .map((el) => ({ [el.id]: el.list_contact }))
        //     ?.flat(),
        // });
        // setUsedContacts(
        //   data.contact_used
        //     .filter(
        //       (el) =>
        //         el.typeSla == data.sla.find((el) => el.primary_account == 1)?.id
        //     )
        //     ?.map((el) => el.list)
        //     ?.flat()
        // );
        setUsedContacts(data.contact_used);
        setLoading(false);
        setToggle(data.sla.find((el) => el.primary_account === 1)?.id);
      } catch (err) {
        setLoading(false);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    };

    getData();

    return () => {
      dispatch(setSearch(""));
    };
  }, []);

  // useEffect(() => {
  //   const getDataBeforeUpdate = async () => {
  //     try {
  //       const {
  //         data: { data },
  //       } = await generateAxios(URL_ENV?.REACT_APP_BASE_URL+process.env.REACT_APP_SUFFIX_API).get(
  //         `/slas/${labelId}
  //         `
  //       );
  //       setConfigEmail(data);
  //       setPrimaryAccount(data.primary_account);
  //       setCount(0);
  //     } catch (err) {
  //       toastNotification("error", t("toasts.somethingWrong"), "topRight");
  //     }
  //   };

  //   if (labelId && count > 0) {
  //     getDataBeforeUpdate();
  //   }
  // }, [labelId, dispatch, count]);

  const filteredData = data.filter((item) => {
    let isMatch = false;

    item.list_contact &&
      item.list_contact.length > 0 &&
      item.list_contact?.forEach((el) => {
        if (el.label.toLowerCase().includes(search.toLowerCase()))
          isMatch = true;
      });

    return (
      item.label.toLowerCase().includes(search.toLowerCase()) ||
      t(`helpDesk.${item.all_contact}`)
        ?.toLowerCase()
        .includes(search.toLowerCase()) ||
      isMatch
    );
  });

  return (
    <Space direction="vertical" style={{ width: "100%" }}>
      <Header
        active={"3"}
        editingKey={loadSwitch}
        handleAdd={() => setOpen(true)}
        btnText={t("helpDesk.addSla")}
        disabled={loading ? true : false}
      />
      <Space direction="vertical" style={{ width: "100%" }}>
        <TableSla
          data={filteredData}
          setData={setData}
          loading={loading}
          setLoading={setLoading}
          onClose={onClose}
          setLabelId={setLabelId}
          setCount={setCount}
          count={count}
          toggle={toggle}
          setToggle={setToggle}
          setOpen={setOpen}
          loadSwitch={loadSwitch}
          setLoadSwitch={setLoadSwitch}
          switchId={switchId}
          setSwitchId={setSwitchId}
          severities={severities}
          setTypeSla={setType}
          confirmLoading={confirmLoading}
        />
      </Space>
      {/* {typeSla && ( */}
      <DrawerSla
        usedContacts={usedContacts}
        setUsedContacts={setUsedContacts}
        typeSla={typeSla}
        open={open}
        setOpen={setOpen}
        severities={severities}
        labelId={labelId}
        setLabelId={setLabelId}
        setData={setData}
        setTypeSla={setType}
        confirmLoading={confirmLoading}
        setConfirmLoading={setConfirmLoading}
      />
      {/* )} */}
    </Space>
  );
};

export default SlaHelpDesk;
