import { mergeAttributes, Node } from "@tiptap/core";
import { NodeSelection } from "prosemirror-state";
import React, { useEffect, useRef } from "react";

const isScrollable = (ele) => {
  const hasScrollableContent = ele.scrollHeight > ele.clientHeight;

  const overflowYStyle = window.getComputedStyle(ele).overflowY;
  const isOverflowHidden = overflowYStyle.indexOf("hidden") !== -1;

  return hasScrollableContent && !isOverflowHidden;
};

const getScrollableParent = (ele) => {
  return !ele || ele === document.body
    ? document.body
    : isScrollable(ele)
    ? ele
    : getScrollableParent(ele.parentNode);
};

const getElementWithAttributes = (name, attrs, events) => {
  const el = document.createElement(name);

  if (!el) throw new Error(`Element with name ${name} can't be created.`);

  if (attrs) {
    Object.entries(attrs).forEach(([key, val]) => el.setAttribute(key, val));
  }

  if (events) {
    Object.entries(events).forEach(([key, val]) =>
      el.addEventListener(key, val)
    );
  }

  return el;
};

export const TableRow = Node.create({
  name: "tableRow",

  addOptions() {
    return {
      HTMLAttributes: {},
    };
  },

  content: "(tableCell | tableHeader)*",

  tableRole: "row",

  parseHTML() {
    return [{ tag: "tr" }];
  },

  renderHTML({ HTMLAttributes }) {
    return [
      "tr",
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes),
      0,
    ];
  },

  addNodeView() {
    return ({ editor, HTMLAttributes, getPos }) => {
      const TableRowComponent = () => {
        const pos = useRef(() => getPos());
        const scrollableParent = useRef(
          getScrollableParent(editor.options.element)
        );
        const isCursorInsideControlSectionRef = useRef(false);

        const actions = {
          deleteRow: () => {
            editor.chain().deleteNode("tableRow").focus().run();
          },
          selectRow: () => {
            const from = pos.current();
            const resolvedFrom = editor.state.doc.resolve(from);
            const nodeSel = new NodeSelection(resolvedFrom);
            editor.view.dispatch(editor.state.tr.setSelection(nodeSel));
          },
        };

        const setCursorInsideControlSection = () => {
          isCursorInsideControlSectionRef.current = true;
        };

        const setCursorOutsideControlSection = () => {
          isCursorInsideControlSectionRef.current = false;
        };

        const controlSection = useRef(
          getElementWithAttributes(
            "section",
            {
              class:
                "absolute hidden flex items-center w-2 bg-gray-200 z-50 cursor-pointer border-1 border-indigo-600 rounded-l opacity-25 hover:opacity-100",
              contenteditable: "false",
            },
            {
              click: (e) => {
                if (e) e.stopPropagation();
                actions.selectRow();
              },
              mouseenter: () => {
                setCursorInsideControlSection();
              },
              mouseover: () => {
                setCursorInsideControlSection();
              },
              mouseleave: () => {
                setCursorOutsideControlSection();
                hideControls();
              },
            }
          )
        );

        const deleteButton = useRef(
          getElementWithAttributes(
            "button",
            {
              class:
                "text-sm px-1 absolute -translate-x-[125%] hover:active:-translate-x-[125%] mr-2",
            },
            {
              click: (e) => {
                if (e) e.stopPropagation();
                actions.deleteRow();
              },
            }
          )
        );

        const showControls = () => {
          repositionControlsCenter();
          controlSection.current.classList.remove("hidden");
        };

        const hideControls = () => {
          setTimeout(() => {
            if (isCursorInsideControlSectionRef.current) return;
            controlSection.current.classList.add("hidden");
          }, 100);
        };

        const tableRow = useRef(
          getElementWithAttributes(
            "tr",
            { ...HTMLAttributes },
            {
              mouseenter: showControls,
              mouseover: showControls,
              mouseleave: hideControls,
            }
          )
        );

        deleteButton.current.textContent = "x";
        controlSection.current.append(deleteButton.current);
        document.body.append(controlSection.current);

        let rectBefore = "";

        const repositionControlsCenter = () => {
          setTimeout(() => {
            const rowCoords = tableRow.current.getBoundingClientRect();
            const stringifiedRowCoords = JSON.stringify(rowCoords);

            if (rectBefore === stringifiedRowCoords) return;

            controlSection.current.style.top = `${
              rowCoords.top + document.documentElement.scrollTop
            }px`;
            controlSection.current.style.left = `${
              rowCoords.x + document.documentElement.scrollLeft - 8
            }px`;
            controlSection.current.style.height = `${rowCoords.height + 1}px`;

            rectBefore = stringifiedRowCoords;
          });
        };

        useEffect(() => {
          setTimeout(() => {
            repositionControlsCenter();
          }, 100);

          editor.on("selectionUpdate", repositionControlsCenter);
          editor.on("update", repositionControlsCenter);
          scrollableParent.current?.addEventListener(
            "scroll",
            repositionControlsCenter
          );
          document.addEventListener("scroll", repositionControlsCenter);

          return () => {
            controlSection.current.remove();
            editor.off("selectionUpdate", repositionControlsCenter);
            editor.off("update", repositionControlsCenter);
            scrollableParent.current?.removeEventListener(
              "scroll",
              repositionControlsCenter
            );
            document.removeEventListener("scroll", repositionControlsCenter);
          };
        }, [editor]);

        return {
          dom: tableRow.current,
          contentDOM: tableRow.current,
          destroy: () => {},
        };
      };

      return TableRowComponent;
    };
  },
});
