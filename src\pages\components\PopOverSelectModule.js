import { But<PERSON>, <PERSON><PERSON><PERSON>, Form, Popover, Select } from "antd";
import React, { useRef } from "react";
import { useTranslation } from "react-i18next";
import { useEffect } from "react";
import MainService from "services/main.service";
import { useSelector } from "react-redux";
import { getElementByFamily } from "pages/clients&users/services/services";
import FormCreate from "pages/clients&users/components/FormCreate";
import { familyIcons } from "./DetailsProfile/ViewSphere2";

const PopOverSelectModule = ({
  onFinish,
  action = "",
  ButtonPopOver,
  width = 300,
  form,
  loadingSubmit,
  setOpenPopOver,
  openPopOver,
}) => {
  const [t] = useTranslation("common");
  const [selectedModule, setSelectedModule] = React.useState({});
  const [searchSelect, setSearchSelect] = React.useState("");
  const [openFormCreate, setOpenFormCreate] = React.useState(false);

  const [open, setOpen] = React.useState(false);
  const [dataModule, setDataModule] = React.useState([]);
  const contactInfo = useSelector(
    (state) => state?.contacts?.contactHeaderInfo
  );
  const popoverRef = useRef();

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (popoverRef.current && !popoverRef.current.contains(event.target)) {
        setOpenPopOver(false);
      }
    };

    document.addEventListener("click", handleClickOutside);

    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);
  useEffect(() => {
    const getData = async () => {
      try {
        const res = await getElementByFamily(
          selectedModule?.key,
          contactInfo?.id,
          ""
        );
        setDataModule(
          res.data.data.map((el) => ({
            value: el.id,
            label: el.label,
            disabled: el.is_used,
          }))
        );
        setOpen(true);
      } catch (err) {
        console.log(err);
      }
    };
    if (selectedModule.key && contactInfo.id) getData();
  }, [selectedModule.key, contactInfo.id]);

  const onChangeModule = (value, values) => {
    if (!value) {
      setSelectedModule({});
      setDataModule([]);
      form.setFieldsValue({ searchSelect: null });
      setOpen(false);
    } else {
      form.setFieldsValue({ searchSelect: null });
      setSelectedModule(values);
    }
  };
  const onChangePopOverOpen = (open) => {
    if (open) {
      setOpenPopOver(open);
    }
    if (!openPopOver) {
      setOpen(false);
      setOpenPopOver(open);
      form.setFieldsValue({ searchSelect: null, module: null });
      setSelectedModule({});
      setDataModule([]);
    }
  };
  const content = (
    <Form
      form={form}
      layout="vertical"
      name="form"
      onFinish={onFinish}
      onValuesChange={(value, all) => setSearchSelect(all.searchSelect)}
    >
      <Form.Item
        label={t("voip.selectModule")}
        name="module"
        rules={[
          {
            required: true,
            message: "",
          },
        ]}
      >
        <Select
          style={{ width: "100%" }}
          allowClear
          options={familyIcons(t).map((el) => ({
            ...el,
            label: (
              <div>
                {el.icon} {el.label}
              </div>
            ),
          }))}
          onChange={onChangeModule}
          placeholder={t("voip.selectModule")}
        />
      </Form.Item>

      <Form.Item
        label={`${t("voip.search_select")} ${
          selectedModule?.key &&
          Array.isArray(selectedModule?.label?.props?.children)
            ? selectedModule?.label?.props?.children[2]
            : ""
        }`}
        rules={[
          {
            required: selectedModule?.key ? true : false,
            message: "",
          },
        ]}
        name="searchSelect"
      >
        <Select
          defaultActiveFirstOption
          disabled={!selectedModule?.key}
          optionFilterProp={["label", "searchOption"]}
          showSearch
          open={open}
          onDropdownVisibleChange={(e) => setOpen(e)}
          options={dataModule}
          filterOption={(input, option) =>
            (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
          }
        />
      </Form.Item>
      <Form.Item
        wrapperCol={{
          offset: 8,
          span: 16,
        }}
      >
        <div className="flex justify-end">
          <Button
            type="primary"
            htmlType="submit"
            size="small"
            disabled={!selectedModule?.key || !searchSelect}
            loading={loadingSubmit}
          >
            {t("wiki.Ok")}
          </Button>
        </div>
      </Form.Item>
    </Form>
  );
  const identificationTitle = (
    <div className="">
      <div className="flex flex-row items-center  justify-between">
        <span>{action}</span>
        {selectedModule?.key ? <p>{t("voip.or")}</p> : null}
        {selectedModule?.key ? (
          <div>
            <Button
              size="small"
              type="primary"
              onClick={() => {
                setOpenFormCreate(true);
              }}
            >
              {t("form.create")}{" "}
              {Array.isArray(selectedModule?.label?.props?.children) &&
                selectedModule?.label?.props?.children[2]}
            </Button>
          </div>
        ) : null}
      </div>
      <Divider className="my-2" />
    </div>
  );
  return (
    <>
      <div ref={popoverRef}>
        <Popover
          content={content}
          trigger={["click"]}
          title={identificationTitle}
          overlayStyle={{ width }}
          open={openPopOver}
          onOpenChange={onChangePopOverOpen}
        >
          {ButtonPopOver}
        </Popover>
      </div>
      <FormCreate
        open={openFormCreate}
        setOpen={setOpenFormCreate}
        familyId={selectedModule?.key}
        idRelation={contactInfo?.id}
        setCatchChange={false}
      />
    </>
  );
};

export default PopOverSelectModule;
