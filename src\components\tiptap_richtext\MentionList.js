import { forwardRef, useState, useEffect, useImperative<PERSON>andle } from "react";
import { Tag, Typography } from "antd";
import { useSelector } from "react-redux";

import "./RichTextInput.css";
import { AvatarChat, Loader } from "../Chat";
import { getName } from "../../pages/layouts/chat/utils/ConversationUtils";
import { useTranslation } from "react-i18next";
import { URL_ENV } from "index";
import useGetInfoDiscussion from "pages/layouts/chat/hooks/useGetInfoDiscussion";

export const MentionList = forwardRef((props, ref) => {
  const { status, fetchStatus } = useGetInfoDiscussion();

  const [selectedIndex, setSelectedIndex] = useState(0);
  const { t } = useTranslation("common");

  const { selectedConversation } = useSelector((state) => state.ChatRealTime);

  const selectItem = (index) => {
    let item = props.items[index];
    if (item) {
      props.command({
        id: item?._id === 0 ? t("chat.all") : item?.name,
        userId: item?._id,
      });
    }
  };

  const upHandler = () => {
    if (typeof props.items === "string") return;
    setSelectedIndex((selectedIndex + props.items.length - 1) % props.items.length);
  };

  const downHandler = () => {
    if (typeof props.items === "string") return;

    setSelectedIndex((selectedIndex + 1) % props.items.length);
  };

  const enterHandler = () => {
    selectItem(selectedIndex);
  };

  useEffect(() => setSelectedIndex(0), [props.items]);

  useImperativeHandle(ref, () => ({
    onKeyDown: ({ event }) => {
      if (event.key === "ArrowUp") {
        upHandler();
        return true;
      }

      if (event.key === "ArrowDown") {
        downHandler();
        return true;
      }

      if (event.key === "Enter") {
        enterHandler();
        return true;
      }

      return false;
    },
  }));
  if (selectedConversation?.type === "user") return <></>;

  return (
    selectedConversation?.type === "room" && (
      <div className="items">
        {status === "loading" && fetchStatus !== "idle" ? (
          <Tag
            type="link"
            className="item m-0.5 flex w-40 flex-1 items-center justify-start space-x-1"
          >
            <Typography.Text type="secondary" className="truncate whitespace-normal text-xs">
              <span>{t("chat.loading")} ...</span>
              <Loader />
            </Typography.Text>
          </Tag>
        ) : props.items?.length ? (
          props.items.map((item, index) => (
            <Tag
              type="link"
              icon={
                <AvatarChat
                  fontSize="0.875rem"
                  size={36}
                  width={7}
                  height={7}
                  backgroundColor={"#cde2fe"}
                  url={
                    (item.type
                      ? URL_ENV?.REACT_APP_OAUTH_CHAT_API +
                        process.env.REACT_APP_SUFFIX_CHAT_GROUP_STORAGE
                      : URL_ENV?.REACT_APP_BASE_URL + URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL) +
                    item?.image
                  }
                  hasImage={item?.image}
                  name={getName(item.name, "avatar")}
                  type={item.type ? "room" : "user"}
                />
              }
              className={`item ${
                index === selectedIndex ? "is-selected" : ""
              } m-0.5 flex w-40 flex-1 items-center justify-start space-x-1  `}
              key={index}
              onClick={() => selectItem(index)}
              id={item?._id}
            >
              <Typography.Text className="truncate whitespace-normal text-xs">
                {getName(item?.name, "name")}
              </Typography.Text>
            </Tag>
          ))
        ) : (
          <div className="item">No result</div>
        )}
      </div>
    )
  );
});
