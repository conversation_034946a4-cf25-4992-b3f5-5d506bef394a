import {
  List,
  Skeleton,
  Space,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>loat<PERSON>utton,
  ConfigProvider,
} from "antd";
import React, {
  memo,
  Suspense,
  useCallback,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from "react";

import {
  DownOutlined,
  MessageOutlined,
  ReloadOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";

import ChatBodyItem from "./item";
import { AvatarChat, Loader } from "components/Chat";
import { useDispatch, useSelector } from "react-redux";
import { setMessageReadChat } from "new-redux/services/chat.services";
import { stopSearchMsg } from "new-redux/actions/chat.actions";
import { scrollToBottom } from "new-redux/actions/chat.actions/Input";
import { setOpenTaskDrawer } from "new-redux/actions/tasks.actions/handleTaskDrawer";
import { useInView } from "react-intersection-observer";
import { moment_timezone } from "App";
import useCustomScroll from "../../hooks/useCustomScroll";
import useGetMessage from "../../hooks/useGetMessage";
import {
  getName,
  systemMessageTypes,
  systemMessageTypesGroups,
  uniqByKey,
} from "../../utils/ConversationUtils";
import { updateMessages } from "../../utils/rqUpdate";
//import useVisiblityChange from "../../hooks/useVisiblityChange";
import { URL_ENV } from "index";
import useOnClickMessage from "../../hooks/useOnClickMessage";
import useFocusWindow from "../../hooks/useFocusWindow";
import { store } from "new-redux/store";
import { LoadingSideBarStatus } from "new-redux/reducers/chatReducer";
import FormCreate from "pages/clients&users/components/FormCreate";

const { Text, Title, Paragraph } = Typography;

function ChatConversations({ source = "main" }) {
  let prevMessage = null;
  const [tabopenDropDown, setTabOpenDropDown] = useState({
    state: false,
    selected: null,
  });
  const [openFormCreate, setOpenFormCreate] = useState(false);
  const [infoFormCreate, setInfoFormCreate] = useState({});

  const { t } = useTranslation("common");

  const {
    numberUnreadMsg,
    errorMessages,
    scrollToBottomState,
    searchMsgState,
    searchMsg,
    openDrawer,
    loadingSideBar,
    sidebarDrawer,
    archivedList,
    membersGroupsChat,
    externalChatItem,
    currentUser,
  } = useSelector((state) => state.chat);

  const selectedConversation = useSelector(
    (state) => state.ChatRealTime.selectedConversation
  );
  const errorReadMsgRef = useRef({
    err1: "",
    err2: null,
  });

  const dispatch = useDispatch();

  const item_of_compare = useMemo(
    () =>
      source === "no_chat"
        ? externalChatItem
        : (sidebarDrawer === "chat"
            ? [...membersGroupsChat]
            : [...archivedList]
          ).find((item) => selectedConversation?.conversationId === item._id),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      source,
      externalChatItem?._id,
      sidebarDrawer,
      membersGroupsChat,
      archivedList,
      selectedConversation?.conversationId,
    ]
  );

  const {
    fetchStatus,
    status,
    getMessagesData,
    fetchNextPage,
    refetch,
    isRefetching,
    isFetchingNextPage,
    isError,
    error,
    hasNextPage,
  } = useGetMessage();
  const FilterdMessages = useMemo(() => {
    let newArray =
      getMessagesData?.pages[0] === undefined
        ? []
        : getMessagesData?.pages
            ?.map((page) => page.data)
            .reduce((accumulator, currentValue) => {
              return accumulator.concat(currentValue);
            }, [])
            .reverse();
    // let uniqueArray = newArray.filter((obj, index, array) => {
    //   return index === 0 || obj._id !== array[index - 1]._id;
    // });

    return uniqByKey(newArray, "_id");
    //  return uniqueArray;
  }, [getMessagesData]);
  const handleReadMsg = useCallback(async () => {
    // seen if msg is bot or sender not the auth or reaction with the auth
    /** ----------------------------------> get last msg unread <---------------------------------- **/

    // retrieve the last message that is not a system message
    const getLastMessageToControll = systemMessageTypesGroups?.includes(
      getMessagesData?.pages?.[0]?.data?.[0]?.type
    )
      ? FilterdMessages?.findLast(
          (item) => !systemMessageTypes.includes(item.type)
        )
      : getMessagesData?.pages?.[0]?.data?.[0];
    // check if the last message is unread (the sender not the auth or the message is from the bot and last message unread)
    const isLastMsgUnread =
      (getMessagesData?.pages?.[0]?.data?.[0]?.sender_id !== currentUser?._id ||
        getMessagesData?.pages?.[0]?.data?.[0]?.type === "message_from_bot") &&
      (selectedConversation?.type === "room"
        ? getLastMessageToControll &&
          !getLastMessageToControll?.unread_room?.includes(currentUser?._id)
        : getMessagesData?.pages?.[0]?.data?.[0]?.unread === 1);

    /** ----------------------------------> get unread message from sidebar  <---------------------------------- **/
    const membersGroupsChatLocal = await store.getState().chat
      .membersGroupsChat;

    const archivedListLocal = await store.getState().chat.archivedList;
    const selectedConversationLocal = await store.getState().ChatRealTime
      .selectedConversation;
    const itemToCompare =
      source === "no_chat"
        ? externalChatItem
        : (sidebarDrawer === "chat"
            ? [...membersGroupsChatLocal]
            : [...archivedListLocal]
          ).find(
            (item) => selectedConversationLocal?.conversationId === item._id
          );

    const isLastMsgUnreadFromSidebar =
      itemToCompare &&
      itemToCompare?.total_unread > 0 &&
      (itemToCompare?.last_message?.type === "message_from_bot" ||
        (itemToCompare?.reaction === null &&
          itemToCompare?.sender?._id !== currentUser?._id));

    /** ----------------------------------> condition of last message <---------------------------------- **/
    const canMakeReadMsg = isLastMsgUnreadFromSidebar || isLastMsgUnread;
    if (
      loadingSideBar === LoadingSideBarStatus.idle &&
      canMakeReadMsg &&
      (source === "no_chat" ? true : document.hasFocus()) &&
      errorReadMsgRef.current.err1 !== errorReadMsgRef.current.err2
    ) {
      //
      try {
        await dispatch(
          setMessageReadChat({
            item: itemToCompare,
            type: selectedConversationLocal?.type,
            _id: selectedConversationLocal?.id,
            source,
            errorText: t("toasts.errorFetchApi"),
          })
        );
        errorReadMsgRef.current = {
          err1: "",
          err2: null,
        };
      } catch (e) {
        errorReadMsgRef.current = {
          err1: e.response?.data?.message,
          err2: errorReadMsgRef.current.err1,
        };
      }
    }
  }, [
    source,
    selectedConversation?.type,
    getMessagesData?.pages,
    externalChatItem,
    sidebarDrawer,
    loadingSideBar,
    currentUser?._id,
    dispatch,
    t,
  ]);

  const { ref: lastElement, inView: inViewLastElement } = useInView({
    threshold: 0.2,
  });
  const { ref: firstElement, inView: inViewFirstElement } = useInView({
    threshold: 0.2,
  });
  const { ref: firstElementInList, inView: inViewFirstElementInList } =
    useInView({
      threshold: 0.1,
    });
  const { ref: footerElement, inView: inViewFooter } = useInView({
    threshold: 0.1,
  });
  const { loadMore, scrollToBottomFunction } = useCustomScroll({
    isFetchingNextPage,
    inViewLastElement,
    inViewFirstElement,
    inViewFirstElementInList,
    hasNextPage,
    fetchNextPage,
    handleReadMsg,
    source,
    inViewFooter,
  });

  useEffect(() => {
    let timeout;
    if (errorMessages.length > 0) {
      errorMessages.forEach((element) => {
        if (
          element.discussion_id === selectedConversation?.id &&
          element.type_conversation === selectedConversation?.type &&
          getMessagesData?.pages[0]?.data[0]?._id !== element.message?._id
        ) {
          !getMessagesData?.pages[0]?.data.some(
            (item) => item?._id === element?.message._id
          ) &&
            updateMessages(
              {
                ...element.message,
                created_at: moment_timezone(new Date()).format(),
                updated_at: moment_timezone(new Date()).format(),
              },
              "new_message",
              null,
              selectedConversation?.id,
              selectedConversation?.type,
              null,
              selectedConversation?.conversationId
            );
        }
      });
    }

    return () => clearTimeout(timeout);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    numberUnreadMsg?.number,
    errorMessages,
    selectedConversation?.id,
    getMessagesData?.pages,
    dispatch,
    selectedConversation?.type,
  ]);
  useLayoutEffect(() => {
    scrollToBottomFunction();
    setTabOpenDropDown({
      state: false,
      selected: null,
    });

    return () => {
      const url = new URL(window.location.href);

      url.pathname === "/chat" && dispatch(setOpenTaskDrawer(false));
    };
  }, [
    selectedConversation?.id,
    selectedConversation?.type,
    dispatch,
    scrollToBottomState,
  ]);

  useFocusWindow({ callback: handleReadMsg });
  // in case open chat from source no_chat
  useEffect(() => {
    if (source === "no_chat") {
      handleReadMsg();
    }
  }, [handleReadMsg, source]);
  // useVisiblityChange({ callback: handleReadMsg });

  useOnClickMessage();
  const skl = () =>
    Array.from(
      {
        length: Math.floor(
          document.getElementById("containerChatRef")?.clientHeight / 30
        ),
      },
      (_, i) => i + 1
    ).map((item) => (
      <div className="flex flex-col  items-center px-1" key={`sklt_${item}`}>
        <Skeleton
          avatar
          paragraph={{
            rows: Math.floor(
              (Math.random() + 1) *
                Array.from({ length: 3 }, (i) => i + 1).length
            ),
          }}
          active
        />
      </div>
    ));

  const renderFooter = () => (
    <div ref={footerElement} className={` ml-10  mt-32   flex flex-col`}>
      <div className=" flex h-14 w-14 items-center justify-center">
        <AvatarChat
          isPublic={
            selectedConversation?.type === "room" &&
            selectedConversation?.predefined === 2
          }
          fontBold="font-semibold"
          type={selectedConversation?.type}
          hasImage={selectedConversation?.image}
          height={12}
          width={12}
          size={52}
          url={
            selectedConversation?.type === "room"
              ? URL_ENV?.REACT_APP_OAUTH_CHAT_API +
                process.env.REACT_APP_SUFFIX_CHAT_GROUP_STORAGE +
                selectedConversation?.image
              : URL_ENV?.REACT_APP_BASE_URL +
                URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                selectedConversation?.image
          }
          name={getName(selectedConversation?.name, "avatar")}
        />
      </div>

      <Title level={3} className="font-medium first-letter:capitalize">
        {getName(selectedConversation?.name, "name")}
      </Title>

      <p className="mt-2  text-xl font-semibold capitalize"></p>
      <Paragraph type="secondary">
        {selectedConversation?.type === "room"
          ? t("chat.room.description_under_title_room")
          : t("chat.room.description_under_title_user")}
        <b className="ml-1 first-letter:capitalize">
          {getName(selectedConversation?.name, "name")}.
        </b>{" "}
        <br />
      </Paragraph>
    </div>
  );
  const customizeRenderEmpty = () => (
    <div className="flex items-center  justify-center space-x-1 text-center text-xl">
      <MessageOutlined />
      <p className=" text-gray-400">{t("chat.empty_discussion")} </p>
    </div>
  );

  // BLOC ENTER TAG/ mention

  return (
    <div
      id="containerChatRef"
      style={{
        cursor:
          searchMsgState.loading ||
          isRefetching ||
          (status === "loading" && fetchStatus !== "idle")
            ? "wait"
            : "default",
      }}
      className="my-1 flex  h-full w-full flex-col justify-center overflow-y-hidden"
    >
      {isError ? (
        <Space size={3} className="flex flex-col">
          <Text type="danger">{error?.message}</Text>
          <Tooltip title={t("chat.reload")}>
            <Button
              loading={isRefetching}
              danger
              type="primary"
              onClick={refetch}
              icon={<ReloadOutlined />}
            >
              {t("chat.reload")}
            </Button>
          </Tooltip>
        </Space>
      ) : searchMsgState.loading ||
        isRefetching ||
        (status === "loading" && fetchStatus !== "idle") ? (
        skl()
      ) : (
        <ConfigProvider renderEmpty={customizeRenderEmpty}>
          <List
            id="messagesList"
            className="messagesList relative flex  h-full w-full flex-col-reverse overflow-y-auto"
            size="large"
            footer={
              ((!hasNextPage &&
                !searchMsgState.id &&
                !searchMsgState.loading) ||
                (searchMsgState.id &&
                  !searchMsgState.hasMorePrevious &&
                  !searchMsgState.loading)) &&
              renderFooter()
            }
            loadMore={
              (isFetchingNextPage && !searchMsgState.id) ||
              (searchMsgState.id && searchMsgState.miniLoadingPrevious) ? (
                <div
                  className={`flex w-full transition duration-150 ${
                    searchMsgState.virtual ? "hidden" : ""
                  } items-center  justify-center  space-x-1  py-1.5`}
                >
                  <Loader size={25} />
                  <Text> {t("chat.loading")} ...</Text>
                </div>
              ) : null
            }
            dataSource={searchMsgState?.id ? searchMsg : FilterdMessages ?? []}
            rowKey={(item) => item?._id}
            renderItem={(item, index) => {
              const isSameUser =
                prevMessage &&
                prevMessage._id !== item._id &&
                prevMessage.type !== "message_from_bot" &&
                item.type !== "message_from_bot" &&
                prevMessage.sender_id === item.sender_id;

              const isLessThanOneMinute =
                prevMessage &&
                prevMessage._id !== item._id &&
                moment_timezone(item.created_at).diff(
                  moment_timezone(prevMessage.created_at),
                  "minutes"
                ) < 1;
              const isMessageSystem =
                prevMessage &&
                prevMessage._id !== item._id &&
                systemMessageTypes.includes(prevMessage.type) ===
                  systemMessageTypes.includes(item.type);
              const hideAvatar = !prevMessage
                ? false
                : isSameUser && isLessThanOneMinute && isMessageSystem;
              const sameDay =
                prevMessage &&
                moment_timezone(item?.created_at).isSame(
                  moment_timezone(prevMessage.created_at),
                  "day"
                );
              const arrayToCompareWith =
                searchMsgState.id && !searchMsgState.loading
                  ? searchMsg
                  : FilterdMessages;
              prevMessage = item;

              return (
                <div data-id={item?._id} className={`h-auto  w-full`}>
                  {arrayToCompareWith.length ===
                    arrayToCompareWith.length - index &&
                    !(
                      (!hasNextPage &&
                        !searchMsgState.id &&
                        !searchMsgState.loading) ||
                      (searchMsgState.id &&
                        !searchMsgState.hasMorePrevious &&
                        !searchMsgState.loading)
                    ) &&
                    !(
                      isFetchingNextPage || searchMsgState.miniLoadingPrevious
                    ) && (
                      <Button
                        ref={lastElement}
                        type="link"
                        onClick={loadMore}
                        className={`flex w-full items-center justify-center space-x-1 text-xs transition `}
                        loading={
                          (isFetchingNextPage && !searchMsgState.id) ||
                          (searchMsgState.id &&
                            searchMsgState.miniLoadingPrevious)
                        }
                      >
                        {t("chat.loadPreviousMessage")}
                      </Button>
                    )}
                  <Suspense fallback={skl()}>
                    <ChatBodyItem
                      source={source}
                      scrollToBottom={scrollToBottomFunction}
                      item={item}
                      ref={
                        !searchMsgState.id &&
                        !searchMsgState.loading &&
                        FilterdMessages?.length - 1 - index === 0
                          ? firstElementInList
                          : searchMsgState.id &&
                            !searchMsgState.loading &&
                            searchMsg?.length - 1 - index === 0
                          ? item._id ===
                            (FilterdMessages.at(-1)?._id ||
                              item_of_compare?.last_message?._id)
                            ? firstElementInList
                            : firstElement
                          : null
                      }
                      hideAvatar={hideAvatar}
                      sameDay={sameDay}
                      index={arrayToCompareWith?.length - 1 - index}
                      data={arrayToCompareWith}
                      tabopenDropDown={tabopenDropDown}
                      setTabOpenDropDown={setTabOpenDropDown}
                      setOpenFormCreate={setOpenFormCreate}
                      setInfoFormCreate={setInfoFormCreate}
                    />
                  </Suspense>

                  {searchMsgState.id &&
                    !searchMsgState.loading &&
                    searchMsg?.length - 1 - index === 0 &&
                    searchMsgState.miniLoadingNext && (
                      <div
                        className={` flex w-full  ${
                          searchMsgState.virtual ? "hidden" : ""
                        } items-center  justify-center  space-x-1  py-1.5 `}
                      >
                        <Loader size={25} />
                        <Text> {t("chat.loading")} ...</Text>
                      </div>
                    )}
                </div>
              );
            }}
          />
        </ConfigProvider>
      )}

      {searchMsgState.id && !searchMsgState.loading && (
        <FloatButton
          badge={{ count: searchMsgState.count_new_message, overflowCount: 10 }}
          type="primary"
          shape="circle"
          tooltip={t("chat.stopSearch")}
          className={` my-32  h-12 ${
            openDrawer.type ? "mr-96 " : "mr-12"
          } w-12 `}
          icon={<DownOutlined size={24} />}
          onClick={() => {
            dispatch(stopSearchMsg(true));
            dispatch(scrollToBottom(Math.floor(Math.random() * 1000000 + 1)));
          }}
        />
      )}
      {openFormCreate && (
        <FormCreate
          open={openFormCreate}
          setOpen={setOpenFormCreate}
          familyId={infoFormCreate.familyId}
          externalSource={{
            source: "chat",
            id: infoFormCreate.msgId,
            msg: infoFormCreate.msg,
            familyId: infoFormCreate.familyId,
            callInProcess: false,
          }}
        />
      )}
    </div>
  );
}

export default memo(ChatConversations);
