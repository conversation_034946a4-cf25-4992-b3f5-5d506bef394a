import { <PERSON><PERSON>, Space, Typography } from "antd";
import { <PERSON><PERSON><PERSON>, FiPhoneForwarded } from "react-icons/fi";
import useActionCall from "../helpers/ActionCall";
import DisplayAvatar from "./DisplayAvatar";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { HiOutlineVideoCamera } from "react-icons/hi2";

const RenderImg = ({ img, name, callNum }) => (
  <DisplayAvatar
    urlImg={img}
    name={name}
    icon={callNum === "sphere_visio" && <HiOutlineVideoCamera />}
    size={40}
  />
);

const RenderInfo = ({ name, callNum, colWidth, isMe, t }) => {
  const baseStyle = "font-semibold truncate";

  return (
    <div /*style={{ maxWidth: `${colWidth - 120}px` }}*/>
      <p className={baseStyle}>{isMe ? t("voip.me") : name ? name : callN<PERSON>}</p>
      {!!name && <p>{callNum}</p>}
    </div>
  );
};

const CallerColumn360 = (props) => {
  //
  const call = useActionCall();
  const [t] = useTranslation("common");
  const user = useSelector(({ user }) => user?.user);
  //
  const col = props?.col;
  const colWidth = props?.colWidth;
  const callId = props?.id;
  const id = col === "caller" ? props?.callerId : props?.calledId;
  const familyId =
    col === "caller" ? props?.callerFamilyId : props?.calledFamilyId;
  const name = col === "caller" ? props?.caller : props?.called;
  const callNum = col === "caller" ? props?.callerNum : props?.calledNum;
  const img = col === "caller" ? props?.callerImg : props?.calledImg;
  const isMe =
    col === "caller"
      ? props?.callerId === user?.id
      : props?.calledId === user?.id;

  //
  // console.log(col, colWidth);
  //
  const copyIcon = (text) => (
    <div className="pl-1">
      <Typography.Paragraph
        copyable={{
          text: text,
          icon: [
            <FiCopy
              style={{
                color: "rgb(22, 119, 255)",
                marginTop: "6px",
                fontSize: "15px",
              }}
            />,
          ],
        }}
      />
    </div>
  );
  //
  return (
    <div className="group flex flex-row items-center justify-between">
      <div className="flex flex-row items-center space-x-2">
        <RenderImg img={img} name={name} callNum={callNum} />
        <RenderInfo
          callNum={callNum}
          name={name}
          colWidth={colWidth}
          isMe={isMe}
          t={t}
        />
      </div>
      {!isMe && callNum !== "sphere_visio" && (
        <div className="hidden group-hover:block">
          <Space size={2}>
            {copyIcon(callNum)}
            <Button
              onClick={() => call(callNum, id, familyId)}
              icon={<FiPhoneForwarded style={{ fontSize: "15px" }} />}
              type="link"
            />
          </Space>
        </div>
      )}
    </div>
  );
};

export default CallerColumn360;
