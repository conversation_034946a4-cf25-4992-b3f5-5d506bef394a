// import React, { useState, useRef, useEffect } from "react";
// import AudioAnalyser from "react-audio-analyser";
// import { useDispatch, useSelector } from "react-redux";
// import WaveSurfer from "wavesurfer.js";
// import "./style.css";
// import {
//   AudioOutlined,
//   PauseOutlined,
//   PlayCircleOutlined,
// } from "@ant-design/icons";
// import { BiStopCircle } from "react-icons/bi";

// const AudioInput2 = ({ open, close }) => {
//   const [status, setStatus] = useState("");
//   const [audioSrc, setAudioSrc] = useState("");
//   const [audioType, setAudioType] = useState("audio/webm");
//   const [recordingTime, setRecordingTime] = useState(0);
//   const [intervalId, setIntervalId] = useState(null);
//   const [recordedBlob, setRecordedBlob] = useState(null);
//   const [isPlaying, setIsPlaying] = useState(false);

//   const [fullTime, setFullTime] = useState(0);
//   const wavesurferRef = useRef(null);
//   const audioRef = useRef(null);
//   const { selectedConversation } = useSelector((state) => state.ChatRealTime);
//   const dispatch = useDispatch();
//   useEffect(() => {
//     // Initialiser le waveform de Wavesurfer lors du montage du composant
//     wavesurferRef.current = WaveSurfer.create({
//       container: "#waveform",
//       waveColor: "gray",
//       progressColor: "gray",
//       height: 40,
//       // barHeight: 2,
//       // container: "#waveform",
//       // waveColor: "gray",
//       // progressColor: "gray",
//       // barWidth: 3,
//       // barHeight: 2,
//       // height: 50,
//       // barMinHeight: 1,
//       // responsive: true,
//       // autoCenterImmediately: true,
//       // scrollParent: true,
//       // barGap: 3,
//       // plugins: [
//       //   WaveSurferTimeline.create({
//       //     container: timelineRef.current,
//       //     formatTimeCallback: formatTimeCallback,
//       //     timeInterval: timeInterval,
//       //     primaryLabelInterval: primaryLabelInterval,
//       //     secondaryLabelInterval: secondaryLabelInterval,
//       //     primaryColor: "blue",
//       //     secondaryColor: "red",
//       //     primaryFontColor: "blue",
//       //     secondaryFontColor: "red",
//       //   }),
//       // ],
//       // normalize: true,
//     });
//     wavesurferRef.current.on("ready", () => {
//       // Faites quelque chose lorsque l'enregistrement est terminé
//     });
//     wavesurferRef.current.on("pause", () => {
//       // Faites quelque chose lorsque l'enregistrement est terminé
//     });
//     wavesurferRef.current.on("play", () => {
//       handlePlayback();
//       // Faites quelque chose lorsque l'enregistrement est terminé
//     });

//     return () => {
//       // Nettoyer Wavesurfer lors du démontage du composant
//       wavesurferRef.current.destroy();
//     };
//   }, []);

//   useEffect(() => {
//     wavesurferRef.current.on("finish", () => {
//       // Faites quelque chose lorsque l'enregistrement est terminé
//       clearInterval(intervalId);
//       setIsPlaying(false);
//       wavesurferRef.current.seekTo(0);
//       setRecordingTime(0);

//       // setTimeout(() => {
//       //   clearInterval(intervalId);
//       // }, Math.floor(wavesurferRef.current.getDuration() * 1000));

//       // Effectuez les actions nécessaires pour revenir à 0
//       // ...
//     });
//     wavesurferRef.current.on("seek", (progress) => {
//       // Récupérez la position en seconde où vous avez cliqué
//       const duration = wavesurferRef.current.getDuration();
//       console.log(duration);
//       const clickedTime = progress * duration;
//       setRecordingTime(Math.floor(Number(clickedTime)));
//       clearInterval(intervalId);
//       // startRecordingTime();

//       // startRecordingTime();
//     });
//   }, [intervalId]);

//   const startRecordingTime = () => {
//     clearInterval(intervalId);

//     const timerId = setInterval(() => {
//       setRecordingTime((prevTime) => prevTime + 1);
//     }, 1000);

//     setIntervalId(timerId);
//   };

//   useEffect(() => {
//     if (open && !recordedBlob) {
//       controlAudio("recording");
//     }
//     if (!open && !recordedBlob) {
//       setRecordingTime(0);
//     }
//   }, [open, recordedBlob]);
//   //   useEffect(() => {

//   //     return ()=>{
//   //       handleDeleteRecording();
//   //     }
//   //   }, [])

//   const controlAudio = (status) => {
//     setStatus(status);
//   };

//   const changeScheme = (e) => {
//     setAudioType(e.target.value);
//   };
//   const handlePlayback = () => {
//     clearInterval(intervalId);
//     setIsPlaying(true);
//     // wavesurferRef.current.play(); // Démarrer la lecture avec Wavesurfer
//     // audioRef.current.addEventListener("timeupdate", handleTimeUpdate);
//     setRecordingTime(Math.floor(wavesurferRef.current.getCurrentTime()));
//     startRecordingTime();
//   };

//   const handlePause = () => {
//     clearInterval(intervalId);

//     setIsPlaying(false);

//     wavesurferRef.current.pause();
//     // setRecordingTime((prevTime) => prevTime);
//   };
//   const audioProps = {
//     audioType,
//     // audioOptions: {sampleRate: 30000}, // 设置输出音频采样率
//     status,
//     audioSrc,
//     backgroundColor: "#f3f4f600",
//     strokeColor: "black",
//     width: 250,
//     height: 50,
//     timeslice: 1000, // timeslice（https://developer.mozilla.org/en-US/docs/Web/API/MediaRecorder/start#Parameters）
//     audioType: "audio/mp3",
//     startCallback: (e) => {
//       console.log("succ start", e);
//       startRecordingTime();
//     },
//     pauseCallback: (e) => {
//       clearInterval(intervalId);

//       console.log("succ pause", e);
//     },
//     stopCallback: (e) => {
//       wavesurferRef.current.load(window.URL.createObjectURL(e));
//       setFullTime(recordingTime);

//       setRecordedBlob(e);
//       // setAudioSrc(window.URL.createObjectURL(e));
//       audioRef.current.src = window.URL.createObjectURL(e); // Assigner l'URL au lecteur audio

//       setRecordingTime(0);
//       clearInterval(intervalId);
//       console.log("succ stop", e);
//     },
//     onRecordCallback: (e) => {
//       console.log("recording", e);
//     },
//     errorCallback: (err) => {
//       console.log("error", err);
//     },
//   };

//   function formatTime(seconds) {
//     const hours = Math.floor(seconds / 3600);
//     const minutes = Math.floor((seconds % 3600) / 60);
//     const remainingSeconds = seconds % 60;

//     const formattedHours = String(hours).padStart(2, "0");
//     const formattedMinutes = String(minutes).padStart(2, "0");
//     const formattedSeconds = String(remainingSeconds).padStart(2, "0");

//     return `${formattedMinutes}:${formattedSeconds}`;
//   }
//   console.log(wavesurferRef?.current?.getDuration());
//   return (
//     <div>
//       <div className="flex items-center justify-end space-x-2">
//         <div
//           className=" items-center space-x-4 bg-white px-4 "
//           style={{
//             display: status !== "inactive" && !recordedBlob ? "flex" : "none",
//           }}
//         >
//           <span>{formatTime(recordingTime)}</span>

//           <AudioAnalyser {...audioProps}>
//             <div className="btn-box flex items-center space-x-2">
//               <div className="text-red-500">
//                 <AudioOutlined
//                   className="animate-blink"
//                   style={{ fontSize: "19px" }}
//                 />
//               </div>
//               <BiStopCircle
//                 className="ml-7 cursor-pointer text-red-600 hover:opacity-70"
//                 style={{ fontSize: "20px" }}
//                 onClick={() => controlAudio("inactive")}
//               />
//               {status !== "recording" && (
//                 <PlayCircleOutlined
//                   onClick={() => controlAudio("recording")}
//                   className="hover:text-green-800"
//                   style={{ fontSize: "18px" }}
//                 />
//               )}
//               {status === "recording" && (
//                 <PauseOutlined
//                   onClick={() => controlAudio("paused")}
//                   className="hover:text-blue-600"
//                   style={{ fontSize: "18px" }}
//                 />
//               )}
//             </div>
//           </AudioAnalyser>

//           <div className="playback-container" style={{ display: "none" }}>
//             <audio ref={audioRef}></audio>
//           </div>
//         </div>
//         <div
//           className=" items-center space-x-4 bg-white px-4 "
//           style={{
//             display: status === "inactive" && recordedBlob ? "flex" : "none",
//           }}
//         >
//           <div className="btn-box flex items-center space-x-2">
//             <BiStopCircle
//               className="ml-7 cursor-pointer text-red-600 hover:opacity-70"
//               style={{ fontSize: "20px" }}
//               onClick={() => controlAudio("inactive")}
//             />
//             {!isPlaying ? (
//               <PlayCircleOutlined
//                 onClick={() => {
//                   clearInterval(intervalId);
//                   wavesurferRef.current.play();
//                 }}
//                 className="hover:text-green-800"
//                 style={{ fontSize: "18px" }}
//               />
//             ) : (
//               <PauseOutlined
//                 className="hover:text-blue-600"
//                 style={{ fontSize: "18px" }}
//                 onClick={handlePause}
//               />
//             )}
//           </div>
//           <div
//             id="waveform"
//             className="waveform"
//           // style={{
//           //   display: recordedBlob ? "block" : "none",
//           //   // width: recordedBlob && !isRecording ? "auto" : "0",
//           // }}
//           ></div>
//           <span>
//             {formatTime(recordingTime)} / {formatTime(fullTime)}
//           </span>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default AudioInput2;
