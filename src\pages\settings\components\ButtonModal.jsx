import React, { useEffect } from "react";
import { Modal, Form, Input, Slider, Button, ColorPicker } from "antd";

export const initialButtonConfig = {
  text: "",
  url: "",
  bgColor: "#1890ff",
  textColor: "#ffffff",
  padding: "12px 24px",
  borderRadius: "5px",
};

const ButtonModal = ({
  isOpen,
  onClose,
  buttonConfig,
  setButtonConfig,
  onInsert,
}) => {
  const [form] = Form.useForm();

  // Synchroniser les valeurs du formulaire avec buttonConfig
  useEffect(() => {
    form.setFieldsValue(buttonConfig);
  }, [buttonConfig, form]);

  const resetButtonModal = () => {
    setButtonConfig(initialButtonConfig);
    form.resetFields();
    onClose();
  };
  const hexString = (colorHex) =>
    typeof colorHex === "string"
      ? colorHex
      : colorHex === null || colorHex === void 0
      ? void 0
      : colorHex.toHexString();

  const handleFormChange = (changedValues, allValues) => {
    // Mettre à jour buttonConfig quand le formulaire change

    setButtonConfig({
      ...buttonConfig,
      ...changedValues,
      bgColor: hexString(allValues.bgColor),
      textColor: hexString(allValues.textColor),
    });
  };
  console.log(buttonConfig);
  const handleSliderChange = (value) => {
    const newConfig = { ...buttonConfig, borderRadius: `${value}px` };
    setButtonConfig(newConfig);
    form.setFieldsValue({ borderRadius: `${value}px` });
  };
  console.log(buttonConfig);
  return (
    <Modal
      title="Créer un bouton"
      open={isOpen}
      onCancel={resetButtonModal}
      footer={[
        <Button key="cancel" onClick={resetButtonModal}>
          Annuler
        </Button>,
        <Button
          key="submit"
          type="primary"
          onClick={() => {
            const res = form.submit();
            resetButtonModal();
          }}
        >
          Insérer le bouton
        </Button>,
      ]}
      width={500}
      centered
    >
      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleFormChange}
        initialValues={buttonConfig}
        onFinish={onInsert}
      >
        <Form.Item
          label="Texte du bouton"
          name="text"
          rules={[
            {
              required: true,
              message: "Veuillez saisir le texte du bouton",
            },
          ]}
        >
          <Input placeholder="Cliquez ici" />
        </Form.Item>

        <Form.Item
          label="URL de destination"
          name="url"
          rules={[{ required: true, message: "Veuillez saisir une URL" }]}
        >
          <Input placeholder="https://exemple.com" />
        </Form.Item>

        <div className="grid grid-cols-2 gap-3">
          <Form.Item label="Couleur de fond" name="bgColor">
            <ColorPicker
              format={"hex"}
              showText
              onChange={(value) =>
                form.setFieldsValue({ bgColor: hexString(value) })
              }
            />
          </Form.Item>

          <Form.Item label="Couleur du texte" name="textColor">
            <ColorPicker
              showText
              onChange={(value) =>
                form.setFieldsValue({ textColor: hexString(value) })
              }
            />
          </Form.Item>
        </div>

        <Form.Item label="Espacement interne (padding)" name="padding">
          <Input placeholder="12px 24px" />
        </Form.Item>

        <Form.Item label="Bordure arrondie" name="borderRadius">
          <Input placeholder="5px" />
        </Form.Item>

        <Form.Item label="Ajustement bordure">
          <Slider
            min={0}
            max={20}
            value={parseInt(buttonConfig.borderRadius) || 0}
            onChange={handleSliderChange}
          />
        </Form.Item>

        {/* Aperçu du bouton */}
        <Form.Item label="Aperçu">
          <div className="rounded border p-4 text-center">
            <a
              href="#"
              onClick={(e) => e.preventDefault()}
              style={{
                display: "inline-block",
                backgroundColor: buttonConfig.bgColor,
                color: buttonConfig.textColor,
                padding: buttonConfig.padding || "10px 20px",
                textDecoration: "none",
                borderRadius: buttonConfig.borderRadius || "5px",
                fontWeight: "bold",
              }}
            >
              {buttonConfig.text || "Texte du bouton"}
            </a>
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ButtonModal;
