import { LinkOutlined } from "@ant-design/icons";
import { Tooltip, Typography } from "antd";

import { moment_timezone } from "App";
import AvatarChat from "components/Chat/Avatar/AvatarChat";
import { URL_ENV } from "index";
import {
  addHttpIfNeeded,
  getName,
} from "pages/layouts/chat/utils/ConversationUtils";
import { HiOutlineCalendar } from "react-icons/hi2";
import { useSelector } from "react-redux";

const { Link } = Typography;

const LinkPreview = ({ item, GoToComponent, fromInfosTab = false }) => {
  const currentUser = useSelector((state) => state.chat.currentUser);
  return (
    <div
      className={`flex ${
        GoToComponent ? "my-2.5" : ""
      } file w-full  items-center justify-between`}
    >
      <Link href={addHttpIfNeeded(item.url)} target="_blank">
        <div
          className={`flex  w-full ${fromInfosTab ? "flex-col" : "flex-row"}`}
          style={{
            whiteSpace: "nowrap",
            overflow: "hidden",
            textOverflow: "ellipsis",
          }}
        >
          <div className="flex w-full flex-col space-y-1  text-xs">
            <div className="flex items-center">
              <LinkOutlined className="mr-1" />
              <Tooltip title={item.url}>
                <span
                  className={`mt-0  truncate whitespace-normal ${
                    GoToComponent ? "text-xs" : ""
                  }
                ${fromInfosTab ? "line-clamp-2" : ""}
                `}
                >
                  {item.url}
                </span>
              </Tooltip>
            </div>
            <div className="flex items-center space-x-3 font-medium text-black">
              {item?.user && GoToComponent && (
                <span className="  max-w-md truncate whitespace-normal text-xs">
                  <AvatarChat
                    isPublic={false}
                    type="user"
                    fontBold="font-semibold"
                    fontSize="10px"
                    hasImage={item?.user_info?.image}
                    height={6}
                    width={6}
                    size={24}
                    url={
                      item?.user_info?._id === currentUser?._id
                        ? URL_ENV?.REACT_APP_BASE_URL +
                          URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                          item?.user_info?.image
                        : URL_ENV?.REACT_APP_BASE_URL +
                          URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                          item?.user_info?.image
                    }
                    name={getName(item?.user_info?.name, "avatar")}
                  />
                  <Tooltip title={item?.user_info?.name}>
                    <span className=" max-w-md truncate whitespace-normal">
                      {getName(item?.user_info?.name, "name")}
                    </span>
                  </Tooltip>
                </span>
              )}
              {fromInfosTab && (
                <div className="flex items-center space-x-1">
                  <HiOutlineCalendar className="text-sm font-medium" />
                  <span>
                    {moment_timezone(item.created_at).format(
                      " DD MMM YYYY HH:mm"
                    )}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      </Link>
      <div className="flex h-full">
        {fromInfosTab && GoToComponent && <>{GoToComponent}</>}
      </div>
    </div>
  );
};

export default LinkPreview;
