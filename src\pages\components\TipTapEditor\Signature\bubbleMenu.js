import { BubbleMenu } from "@tiptap/react";
import { useRef, useState } from "react";
import {
  BoldIcon,
  ItalicIcon,
  UnderlineIcon,
  StrikethroughIcon,
  CodeIcon,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Unlink,
  Link,
} from "lucide-react";
import { NodeSelector } from "./NodeSelector";
import { Button, Form, Popover } from "antd";

import { ColorSelector } from "../ColorSelector";
import { AddLinkBox } from "../AddLinkBox";
import { BiUnlink } from "react-icons/bi";
import { BsLink45Deg } from "react-icons/bs";
import useOnClickOutside from "../../../layouts/chat/hooks/useClickOutside";

export const EditorBubbleMenu = (props) => {
  const items = [
    {
      name: "bold",
      isActive: () => props?.editor?.isActive("bold"),
      command: () => props?.editor?.chain().focus().toggleBold().run(),
      icon: BoldIcon,
    },
    {
      name: "italic",
      isActive: () => props?.editor?.isActive("italic"),
      command: () => props?.editor?.chain().focus().toggleItalic().run(),
      icon: ItalicIcon,
    },
    {
      name: "underline",
      isActive: () => props?.editor?.isActive("underline"),
      command: () => props?.editor?.chain().focus().toggleUnderline().run(),
      icon: UnderlineIcon,
    },
    {
      name: "strike",
      isActive: () => props?.editor?.isActive("strike"),
      command: () => props?.editor?.chain().focus().toggleStrike().run(),
      icon: StrikethroughIcon,
    },
    {
      name: "code",
      isActive: () => props?.editor?.isActive("code"),
      command: () => props?.editor?.chain().focus().toggleCode().run(),
      icon: CodeIcon,
    },
    {
      name: "Align left",
      icon: AlignLeft,

      command: () => props.editor.chain().focus().setTextAlign("left").run(),
      isActive: () => props.editor?.isActive({ textAlign: "left" }),
    },
    {
      name: "Align center",
      icon: AlignCenter,
      command: () => props.editor.chain().focus().setTextAlign("center").run(),
      isActive: () => props.editor?.isActive({ textAlign: "center" }),
    },
    {
      name: "Align right",
      icon: AlignRight,
      command: () => props.editor.chain().focus().setTextAlign("right").run(),
      isActive: () => props.editor?.isActive({ textAlign: "right" }),
    },
    // {
    //   name: "align justify",
    //   icon: AlignJustify,
    //   onClick: () => props.editor.chain().focus().setTextAlign("justify").run(),
    //   isActive: () => props.editor?.isActive({ textAlign: "justify" }),
    // },
  ];

  const bubbleMenuProps = {
    ...props,
    shouldShow: ({ editor }) => {
      // don't show if image is selected
      if (editor.isActive("image")) {
        return false;
      }
      return editor.view.state.selection.content().size > 0;
    },
    tippyOptions: {
      moveTransition: "transform 0.15s ease-out",
      onHidden: () => {
        setIsNodeSelectorOpen(false);
        setIsColorSelectorOpen(false);
      },
    },
  };

  const [isNodeSelectorOpen, setIsNodeSelectorOpen] = useState(false);
  const [isColorSelectorOpen, setIsColorSelectorOpen] = useState(false);
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm();
  const menuRef = useRef(null);
  useOnClickOutside(menuRef, undefined, () => {
    setOpen(false);
  });
  return (
    <BubbleMenu
      ref={menuRef}
      {...bubbleMenuProps}
      className="flex items-center overflow-hidden rounded border border-stone-200 bg-white shadow-xl"
    >
      <NodeSelector
        editor={props?.editor}
        isOpen={isNodeSelectorOpen}
        setIsOpen={() => {
          setIsNodeSelectorOpen(!isNodeSelectorOpen);
          setIsColorSelectorOpen(false);
        }}
      />

      {items.map((item, index) => (
        <Button
          type="text"
          key={index}
          onClick={item.command}
          className="p-2 text-stone-600 hover:bg-stone-100 active:bg-stone-200"
        >
          <item.icon
            className={`h-4 w-4 ${item.isActive() ? "text-blue-500" : ""}`}
          />
        </Button>
      ))}
      <Popover
        placement="bottom"
        // title={"test"}
        trigger="click"
        onOpenChange={(e) => setOpen(e)}
        open={open}
        content={
          <AddLinkBox editor={props.editor} setOpen={setOpen} form={form} />
        }
        arrow={false}
      >
        {props.editor?.isActive("link") ? (
          <Button
            type="text"
            className="p-2"
            title="Remove link"
            onClick={() => props.editor.chain().focus().unsetLink().run()}
          >
            <Unlink className="h-4 w-4" />
          </Button>
        ) : (
          <Button type="text" className="e-200 p-2" title="add a link">
            <Link className="h-4 w-4" />
          </Button>
        )}{" "}
      </Popover>
      <ColorSelector
        editor={props?.editor}
        isOpen={isColorSelectorOpen}
        setIsOpen={() => {
          setIsColorSelectorOpen(!isColorSelectorOpen);
          setIsNodeSelectorOpen(false);
        }}
      />
    </BubbleMenu>
  );
};
