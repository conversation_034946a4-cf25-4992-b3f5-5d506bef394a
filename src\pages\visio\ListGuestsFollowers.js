import { Badge, Dropdown, List, Tabs, Typography } from "antd";
import React, { useState } from "react";
import { AvatarChat } from "../../components/Chat";
import { getName } from "../layouts/chat/utils/ConversationUtils";
import { FiCopy, FiMoreVertical } from "react-icons/fi";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { MessageOutlined, PhoneOutlined } from "@ant-design/icons";
import { handleMsgClick } from "../voip/helpers/helpersFunc";
import useActionCall from "../voip/helpers/ActionCall";
import { useDispatch } from "react-redux";
import ChatWithUser from "../voip/components/ChatWithUser";
import { URL_ENV } from "index";

const ListGuestsFollowers = ({ guests, followers }) => {
  const onlineUser = useSelector((state) => state.ChatRealTime.onlineUser);
  const { userList } = useSelector((state) => state.chat);
  const { user } = useSelector((state) => state.user);

  const [openMsgDrawer, setOpenMsgDrawer] = useState(false);
  const dispatch = useDispatch();

  const [t] = useTranslation("common");
  const call = useActionCall();
  const menuDropdown = (item) => {
    const items = [];
    const copyIcon = (text) => (
      <Typography.Paragraph
        copyable={{
          text: text,
          icon: [
            <FiCopy
              style={{
                color: "rgb(22, 119, 255)",
                marginTop: "2px",
                fontSize: "15px",
              }}
            />,
          ],
        }}
      />
    );

    const pushItem = (label, key, icon, onClick, disabled, children) => {
      items.push({
        label,
        key,
        icon,
        onClick,
        disabled,
        children,
      });
    };
    // console.log(item.phones.map((el) => el.filter((item) => item !== null)));

    if (item?.extension) {
      pushItem(
        <div className="flex flex-row justify-between">
          {`${t("voip.audioCall")} ${item.extension}`}{" "}
          {copyIcon(item.extension)}
        </div>,
        "audio-call-extension",
        <PhoneOutlined rotate={100} style={{ fontSize: "14px" }} />,
        () => call(`${item.extension}`)
      );
    }

    if (
      item?.phones?.map((phone) => phone.filter((value) => value !== null))
        .length === 1
    ) {
      const numberToDisplay = `${item?.phones?.[0]?.[0]} ${item?.phones?.[0]?.[1]}`;
      const numberToCall = numberToDisplay
        ?.replace("+", "00")
        ?.replace(" ", "");
      pushItem(
        <div className="flex flex-row justify-between">
          {`${"Call"} ${numberToDisplay}`}
          {copyIcon(numberToCall)}
        </div>,
        "audio-call-phone",
        <PhoneOutlined rotate={100} style={{ fontSize: "14px" }} />,
        () => call(`${numberToCall}`)
      );
    }
    if (
      item?.phones?.map((phone) => phone.filter((value) => value !== null))
        .length > 1
    ) {
      const children = [];
      item?.phones?.forEach((phone, i) => {
        const numberToDisplay = `${phone[0]} ${phone[1]}`;
        const numberToCall = numberToDisplay
          ?.replace("+", "00")
          ?.replace(" ", "");
        children?.push({
          key: `call${numberToCall}`,
          label: (
            <div className="flex flex-row justify-between">
              {`${numberToDisplay}`}
              {copyIcon(numberToCall)}
            </div>
          ),
          onClick: () => call(`${numberToCall}`),
        });
      });

      pushItem(
        `${"Call"}`,
        "audio-call-phone",
        <PhoneOutlined rotate={100} style={{ fontSize: "14px" }} />,

        null,
        null,
        children
      );
    }

    if (item?.uuid) {
      const firstName = item?.label?.split(" ")?.[0];
      const shortName = `${firstName?.slice(0, 7)}${
        firstName?.length > 7 ? "..." : ""
      }`;
      pushItem(
        `Chat ${t("voip.with")} ${shortName}`,
        "chat",
        <MessageOutlined style={{ fontSize: "14px" }} />,
        () =>
          handleMsgClick(dispatch, setOpenMsgDrawer, userList, {
            post_number: item.extension,
          }),
        openMsgDrawer
      );
    }

    // items.push({ type: "divider" });

    // pushItem(
    //   t("voip.moreInfo"),
    //   "more-info",
    //   <InfoCircleOutlined style={{ fontSize: "14px" }} />,
    //   () => setColleagueInfo(item)
    // );

    return { items };
  };
  const contacts = [
    {
      key: 1,
      label: (
        <>
          {t("visio.guest", {
            plural: guests?.length > 1 ? "s" : "",
          })}{" "}
          {guests?.length > 0
            ? // <Badge count={guests?.length} color="blue" />
              "(" + guests?.length + ")"
            : ""}
        </>
      ),
      children: (
        <div
          id="scrollableDiv "
          style={{
            // maxHeight: 350,
            // minHeight: 150,
            height: "calc(100vh - 200px)",
            overflow: "auto",
            padding: "0",
          }}
          className="scrollableDiv"
        >
          <List
            className="membersList "
            dataSource={guests}
            renderItem={(el) => (
              <div className="flex items-center justify-between">
                <Typography.Paragraph className="space-x-1 px-4 py-1">
                  <div className="flex items-center space-x-1">
                    <Badge
                      dot
                      color={
                        onlineUser[el.id] === "away"
                          ? "orange"
                          : onlineUser[el.id] === "busy"
                          ? "red"
                          : onlineUser[el.id] === "online"
                          ? "green"
                          : "#a6a6a6"
                      }
                      offset={[-5, 30]}
                    >
                      <AvatarChat
                        fontSize="0.875rem"
                        url={
                          URL_ENV?.REACT_APP_BASE_URL +
                          URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                          el?.avatar
                        }
                        type="user"
                        size={38}
                        height={10}
                        width={10}
                        name={getName(el?.label, "avatar")}
                        hasImage={el?.avatar}
                      />
                    </Badge>{" "}
                    <div>
                      <Typography.Text strong>
                        {getName(el?.label, "name")}
                      </Typography.Text>
                      <Typography.Paragraph>
                        {el.family_id === 1
                          ? t("visio.company", {
                              plural: "",
                            })
                          : el.family_id === 2
                          ? t("visio.contact", {
                              plural: "",
                            })
                          : el.family_id === 4
                          ? t("visio.colleague", {
                              plural: "",
                            })
                          : ""}
                      </Typography.Paragraph>
                    </div>
                  </div>
                </Typography.Paragraph>
                {user?.id !== el.id ? (
                  el.extension ||
                  el?.phones
                    ?.map((phone) => phone.filter((value) => value !== null))
                    .flat().length > 0 ? (
                    <Dropdown
                      trigger={["click"]}
                      placement="bottomRight"
                      menu={menuDropdown(el)}
                      getPopupContainer={(triggerNode) =>
                        triggerNode.parentNode
                      }
                    >
                      <FiMoreVertical className="h-4 w-4 cursor-pointer text-gray-400 hover:text-gray-700" />
                    </Dropdown>
                  ) : (
                    ""
                  )
                ) : (
                  ""
                )}
              </div>
            )}
          />
        </div>
      ),
    },

    {
      key: 4,
      label: (
        <>
          {t("tasks.followers", {
            s: followers?.length > 1 ? "s" : "",
          })}{" "}
          {followers?.length > 0
            ? // <Badge count={followers.length} color="blue" />
              "(" + followers.length + ")"
            : ""}
        </>
      ),
      children: (
        <div
          id="scrollableDiv"
          style={{
            // maxHeight: 350,
            // minHeight: 150,
            height: "calc(100vh - 200px)",
            overflow: "auto",
            padding: "0",
          }}
          className="scrollableDiv"
        >
          <List
            className="membersList"
            dataSource={followers}
            renderItem={(el) => (
              <div className="flex items-center justify-between">
                <Typography.Paragraph className="space-x-1 px-4 py-1">
                  <Badge
                    dot
                    color={
                      onlineUser[el?.idUser] === "away"
                        ? "orange"
                        : onlineUser[el?.idUser] === "busy"
                        ? "red"
                        : onlineUser[el?.idUser] === "online"
                        ? "green"
                        : "#a6a6a6"
                    }
                    offset={[-5, 30]}
                  >
                    <AvatarChat
                      fontSize="0.875rem"
                      url={
                        URL_ENV?.REACT_APP_BASE_URL +
                        URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                        el?.avatar
                      }
                      type="user"
                      size={38}
                      height={10}
                      width={10}
                      name={getName(el?.label, "avatar")}
                      hasImage={el?.avatar}
                    />
                  </Badge>{" "}
                  {el?.label}
                </Typography.Paragraph>
                {el.extension ||
                el?.phones
                  ?.map((phone) => phone.filter((value) => value !== null))
                  .flat().length > 0 ? (
                  <Dropdown
                    trigger={["click"]}
                    placement="bottomRight"
                    menu={menuDropdown(el)}
                    getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  >
                    <FiMoreVertical className="h-4 w-4 cursor-pointer text-gray-400 hover:text-gray-700" />
                  </Dropdown>
                ) : (
                  ""
                )}
              </div>
            )}
          />
        </div>
      ),
    },
  ];
  return (
    <>
      {" "}
      <Tabs defaultActiveKey="1" items={contacts} className="mt-[3px]" />
      {openMsgDrawer ? (
        <ChatWithUser open={openMsgDrawer} setOpen={setOpenMsgDrawer} />
      ) : (
        ""
      )}
    </>
  );
};

export default ListGuestsFollowers;
