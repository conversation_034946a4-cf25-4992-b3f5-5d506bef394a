import React from "react";
import { Avatar } from "antd";
import { URL_ENV } from "index";

function NoteAvatar({ avatar, name, size }) {
  // console.log("avatar", avatar);
  // console.log("name", name);
  // console.log("size", size);

  const [avatarError, setAvatarError] = React.useState(false);

  return (
    <>
      {[
        "jpg",
        "jpeg",
        "png",
        "gif",
        "bmp",
        "tiff",
        "tif",
        "jfif",
        "avif",
        "webp",
      ].some((ext) => avatar?.toLowerCase().endsWith(ext)) && !avatarError ? (
        <Avatar
          size={!size ? "large" : size}
          src={
            URL_ENV?.REACT_APP_BASE_URL +
            URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
            avatar
          }
          //on error want to show the text avatar
          onError={() => {
            setAvatarError(true);
          }}
        />
      ) : (
        <Avatar
          size={!size ? "large" : size}
          style={{
            backgroundColor: "#f56a00",
            // verticalAlign: "middle",
          }}
          icon={
            <span style={{ textTransform: "uppercase" }} className="text-sm">
              {name?.charAt(0)}
            </span>
          }
        />
      )}
    </>
  );
}

export default NoteAvatar;
