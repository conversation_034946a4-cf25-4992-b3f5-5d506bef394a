import React, { memo, useEffect, useRef, useState } from "react";
import { DollarOutlined, TeamOutlined } from "@ant-design/icons";
import { Button, Drawer, Form, Image, Layout, Spin, Tour, message } from "antd";
import { HiOutlineBuildingOffice } from "react-icons/hi2";
import { useTranslation } from "react-i18next";

import MainService from "services/main.service";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import {
  getElementSystemDetails,
  getGeneralInfo360,
  getKPI,
} from "pages/clients&users/services/services";
import { useDispatch } from "react-redux";
import { URL_ENV } from "index";
import { useSelector } from "react-redux";
import {
  resetPrevIdsFromViewSphere,
  setActiveActivity360,
  setActiveMenu360,
  setActiveTab360,
  setChatInViewSPhereFromDrawer,
  setNbrChatInViewSPhere,
  setNewInteraction,
  setOpenChatInViewSPhere,
  setOpenView360InDrawer,
} from "new-redux/actions/vue360.actions/vue360";
import { generateAxios } from "services/axiosInstance";

import { FiUsers } from "react-icons/fi";
import { CgUserlane } from "react-icons/cg";
import { AiOutlineShoppingCart } from "react-icons/ai";
import { LuPalmtree } from "react-icons/lu";
import { setUpdateElementSuccessfully } from "new-redux/actions/form.actions/form";

import ViewSphere from "./ViewSphere";
import { SET_CONTACT_INFO_FROM_DRAWER } from "new-redux/constants";

import ContentViewSphere from "./ContentViewSphere";
import HeaderViewSphere from "./HeaderViewSphere";
import { getFamilyIdByName } from "pages/clients&users/FamilyRouting";
import NotFoundPage from "pages/404";
import party from "party-js";
import { isGuestConnected } from "utils/role";
import { Blocks, HandCoins, HeartHandshake } from "lucide-react";
import { closeTour } from "pages/profile/services";
import axios from "axios";
import i18n from "translations/i18n";
import TicketIconSphere from "components/icons/TicketIconSphere";
import LoadingViewSphere from "./LoadingViewSphere";

export const pathViewSphere = (familyId, id) => {
  switch (familyId) {
    case 1:
      return `/companies/v2/${id}`;
    case 2:
      return `/contacts/v2/${id}`;
    case 3:
      return `/deals/v2/${id}`;
    case 4:
      return `/settings/users/v2/${id}` || `/settings/guests/v2/${id}`;
    case 5:
      return `/settings/products/v2/${id}`;
    case 6:
      return `/tickets/v2/${id}`;
    case 7:
      return `/projects/v2/${id}`;
    case 8:
      return `/booking/v2/${id}`;
    case 9:
      return `/leads/v2/${id}`;
    case 11:
      return `/invoices/v2/${id}`;
    case 12:
      return `/transactions/v2/${id}`;
    default:
      break;
  }
};
export const SuccessWon = () => {
  const particleContainer = document.getElementById("header-container");
  if (particleContainer) {
    party.confetti(particleContainer, {
      count: party.variation.range(500, 1000), // Plus de confettis
      size: party.variation.range(0.5, 1.5), // Taille des confettis
      duration: party.variation.range(3000, 5000), // Durée de l'animation
      spread: party.variation.range(60, 120), // Répartition des confettis
    });
  } else {
    console.error("Element with ID 'header-container' not found.");
  }
};
export const familyIcons = (t, size = 14) => {
  return [
    {
      value: 1,
      label: t("contacts.companies"),
      icon: <HiOutlineBuildingOffice />,
      key: 1,
      pathname: "Organisation",
    },
    {
      value: 2,
      label: t("contacts.contacts"),
      icon: <TeamOutlined />,
      key: 2,
      pathname: "Contact",
    },

    {
      value: 3,
      label: t("modules.deals"),
      icon: <HeartHandshake size={size} />,
      key: 3,
      pathname: "Deal",
    },
    {
      value: 4,
      label: t("contacts.users"),
      icon: <FiUsers />,
      key: 4,
      pathname: "User",
    },

    {
      value: 5,
      label: t("contacts.products"),
      icon: <AiOutlineShoppingCart />,
      key: 5,
      pathname: "Product",
    },
    {
      value: 6,
      label: "Tickets",
      icon: <TicketIconSphere size={size} />,
      key: 6,
      pathname: "Helpdesk",
    },

    {
      value: 7,
      label: t("contacts.projects"),
      icon: <Blocks size={size} />,
      key: 7,
      pathname: "Project",
    },
    {
      value: 8,
      label: t("contacts.bookings"),
      icon: <LuPalmtree />,
      key: 8,
      pathname: "Booking",
    },
    {
      value: 9,
      label: t("contacts.leads"),
      icon: <CgUserlane />,
      key: 9,
      pathname: "Leads",
    },
    {
      value: 11,
      label: t("menu2.invoices"),
      icon: <DollarOutlined />,
      key: 11,
      pathname: "Invoices",
    },
    {
      value: 12,
      label: t("menu2.transactions"),
      icon: <HandCoins />,
      key: 12,
      pathname: "Transaction",
    },
  ];
};

const ViewSphere2 = ({ from = "viewSphere", elementId = "", source = "" }) => {
  const [tasksTypes, setTasksTypes] = useState([]);
  const [selectedTags, setSelectedTags] = useState({});
  // const [selectedKey, setSelectedKey] = useState(
  //   localStorage.getItem("selectedKeyViewSphere") || "1"
  // );
  const [selectedKey, setSelectedKey] = useState("1");
  const [selectedKeySideBar, setSelectedKeySideBar] = useState(null);
  const [openTour, setOpenTour] = useState(false);
  const [openTask, setOpenTask] = useState(false);
  const [canCreateRoom, setCanCreateRoom] = useState(0);
  const [listConv, setListConv] = useState([]);
  const [disabledRelation, setDisabledRelation] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [dataSteps, setDataSteps] = useState([]);
  const [loading, setLoading] = useState(true);
  const [loadFinalStage, setLoadFinalStage] = useState(false);
  const [openEmailModal, setOpenEmailModal] = useState(false);
  const [mountChat, setMountchat] = useState(false);
  const [countTasks, setCountTasks] = useState({});
  const [kpi, setKpi] = useState([]);
  const [relations, setRelations] = useState([]);
  const [generalInfo, setGeneralInfo] = useState({});
  const [detailsInfo, setDetailsInfo] = useState({});
  const [openFields, setOpenFields] = useState(false);
  const [openPipeline, setOpenPipeline] = useState(false);
  const [openDrawerUpdate, setOpenDrawerUpdate] = useState(false);
  const [isUpdate, setIsUpdate] = useState(false);
  const [channel, setChannel] = useState(null);
  const [reference, setReference] = useState(null);
  const [openModalCheckList, setOpenModalCheckList] = useState(false);
  const [show404, setShow404] = useState(false);
  const [selectedReasonType, setSelectedReasonType] = useState(null);
  const [reasons, setReasons] = useState([]);
  const [selectedStage, setSelectedStage] = useState(null);
  const [collapsed, setCollapsed] = useState(
    from === "directory" ? true : false
  );
  const [widthHeaderRefInfo, setWidthHeaderRefInfo] = useState(0);
  const [contactType, setContactType] = useState(null);
  const [mountRelation, setMountRelation] = useState(false);
  const [actionDeal, setActionDeal] = useState(null);
  const [isFinalStage, setIsFinalStage] = useState(false);
  const [steps, setSteps] = useState([]);
  const token = localStorage.getItem("accessToken");
  const { user } = useSelector((state) => state.user);
  const [headerHeight, setHeaderHeight] = useState(84);
  const [windowHeight, setWindowHeight] = useState(window.innerHeight - 150);
  const [refreshInfo, setRefreshInfo] = useState(true);
  const params = useParams();
  const [t] = useTranslation("common");
  const navigate = useNavigate();
  const [finalStageForm] = Form.useForm();
  const headerInfoRef = useRef(null);
  const { contactHeaderInfo: contactInfo } = useSelector(
    (state) => state?.contacts
  );
  const { isOpenTour } = useSelector((state) => state.menu);

  const location = useLocation();
  const {
    activeTab360,
    newInteraction,
    contactInfoFromDrawer,
    openView360InDrawer,
  } = useSelector((state) => state?.vue360);
  const headerRef = useRef(null);
  const closeTourViewSphere = async () => {
    closeTour(setOpenTour, "viewSphere", dispatch);
  };
  const dispatch = useDispatch();
  useEffect(() => {
    function extractName(url) {
      const regex = /\/([a-zA-Z]+)\/v2/;
      const match = url.match(regex);
      return match ? match[1] : null;
    }
    const getALLdata = async () => {
      setLoading(true);
      try {
        // setIsUpdate(true);
        const { data: fetchElementInfo } = await getElementSystemDetails(
          elementId || params?.id
        );
        if (
          // location.pathname === "/directory"
          location.pathname === "/telephony/directory" ||
          location.pathname === "/tasks" ||
          location.pathname === "/visio" ||
          location.pathname === "/notes" ||
          getFamilyIdByName[extractName(location.pathname).toLowerCase()] ===
            fetchElementInfo?.family_id
        ) {
          const [
            // { data: stats_chat },
            { data: tasks_type },
            // { data: generalInfos },
            { data: detailsInfo },
            { data: kpiInfo },
          ] = await Promise.all([
            MainService.getTasksTypes(),
            // getGeneralInfo360("general_info", elementId || params?.id),
            getGeneralInfo360("header-info", elementId || params?.id),
            MainService.getKpiOverview360ByElement(elementId || params?.id),
          ]);
          setCanCreateRoom(fetchElementInfo?.access_discussion);
          // setGeneralInfo(generalInfos?.field_value);
          setDetailsInfo(detailsInfo?.field_value);
          if (!isGuestConnected()) {
            const { data } = await getKPI(
              fetchElementInfo?.family_id,
              fetchElementInfo?.id
            );

            setRelations(
              data?.map((kpi) => ({
                ...kpi,
                children: kpi?.child?.length
                  ? kpi?.child?.slice(0, 3)?.map((child) => ({
                      label: child?.label_data,
                      id: child?.id,
                      pipeline: child?.pipeline_label,
                      stage: child?.stage_label,
                    }))
                  : [],
              }))
            );
          }

          setKpi(
            Object.entries(kpiInfo.data[0]).map(([key, value]) => ({
              title: key,
              value,
            }))
          );

          if (fetchElementInfo.source) {
            const channels = await generateAxios(
              URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
            ).get("/channels");
            setChannel(
              channels?.data?.data.find(
                (el) => el?.label === fetchElementInfo.source
              )
            );
          }
          if (fetchElementInfo.type) {
            const ContactTypes = await generateAxios(
              URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
            ).get("/type-contacts");
            setContactType(
              ContactTypes?.data?.data?.find(
                (el) => el?.label === fetchElementInfo.type
              )
            );
          }

          setReference(fetchElementInfo?.reference);

          if (fetchElementInfo.family_id && !isGuestConnected()) {
            const res = await generateAxios(
              URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
            ).get(`get-relation-family/${fetchElementInfo?.family_id}`);
            const convertedData = Object.entries(res.data).map(
              ([key, label]) => {
                return Number(key);
              }
            );
            if (user?.rmc_access === "OUI" && !isGuestConnected()) {
              const convRmc = await MainService.getConvRmc360(
                fetchElementInfo?.id
              );

              setListConv(convRmc.data.data.filter((el) => el.conversation_id));
            }
            setDisabledRelation(convertedData?.length === 0);
          }
          const getUnreadMsgRoomInViewSphere = async () => {
            try {
              const res = await MainService.getUnreadMsgRoomInViewSphere({
                relation_id: elementId || params.id,
              });
              dispatch(
                setNbrChatInViewSPhere({
                  number: res?.data?.total_message_room_count,
                  relation_id: elementId || params.id,
                  id: res?.data?.room_id,
                })
              );
            } catch (err) {
              console.log(err);
            }
          };
          if (fetchElementInfo.access_discussion === 1)
            getUnreadMsgRoomInViewSphere();
          await getStages(fetchElementInfo, false);
          setTasksTypes(tasks_type?.data?.tasks_type);
          setIsUpdate(false);
          dispatch({
            type: "SET_CONTACT_HEADER_INFO",
            payload: { ...contactInfo, ...fetchElementInfo } || {},
          });
          setLoading(false);
          dispatch(setNewInteraction({ type: "" }));
          setIsUpdate(false);
        } else {
          setLoading(false);
          setShow404(true);
        }
      } catch (err) {
        console.log(err);
        if (err?.response?.data?.message === "Data not found") {
          setShow404(true);
        }
        setLoading(false);
      }
    };
    if (
      ((elementId && !params.id) || (params.id && !elementId)) &&
      (newInteraction?.type === "updateElement" || isUpdate)
    ) {
      getALLdata();
    }
  }, [newInteraction.type, elementId, params.id, isUpdate]);

  useEffect(() => {
    if (
      contactInfo?.id &&
      newInteraction?.type === "updateStage"
      // &&
      // user.id !== newInteraction?.user
    ) {
      getStages(contactInfo);
    }
  }, [contactInfo?.id, newInteraction?.type]);

  useEffect(() => {
    if (openView360InDrawer) {
      dispatch(
        setChatInViewSPhereFromDrawer({
          id: "",
          number: 0,
          relation_id: "",
        })
      );
    } else {
      if (activeTab360 == 9 || selectedKeySideBar === "Chat") {
        dispatch(setOpenChatInViewSPhere(true));

        // else {
        //   dispatch(
        //     setNbrChatInViewSPhere({
        //       id: "",
        //       number: 0,
        //       relation_id: "",
        //     })
        //   );
        // }
      } else {
        dispatch(setOpenChatInViewSPhere(false));
      }
    }
  }, [activeTab360, selectedKeySideBar, dispatch, openView360InDrawer]);

  // useEffect(() => {
  //   setIsUpdate(true);
  // }, [params.id]);
  useEffect(() => {
    if (!loading && headerRef.current) {
      const updateHeight = () => {
        setHeaderHeight(headerRef.current.clientHeight);
      };

      updateHeight();
      //   headerRef.current.addEventListener("transitionend", updateHeight);

      //   return () => {
      //     headerRef.current.removeEventListener("transitionend", updateHeight);
      //   };
    }
    if (!loading && headerInfoRef.current) {
      const updateWidth = () => {
        setWidthHeaderRefInfo(headerInfoRef.current.clientWidth);
      };

      updateWidth();
      //   headerRef.current.addEventListener("transitionend", updateHeight);

      //   return () => {
      //     headerRef.current.removeEventListener("transitionend", updateHeight);
      //   };
    }
  }, [loading]);
  useEffect(() => {
    if (!openTask) {
      setSelectedKeySideBar(null);
    }
  }, [openTask]);
  useEffect(() => {
    const getTasksCount = async () => {
      try {
        const res = await MainService.getTasks360Count({
          id: elementId || params.id,
          types: "",
        });

        setCountTasks(res?.data);
      } catch (err) {}
    };

    getTasksCount();
    setSelectedKey("1");
    // setSelectedKey(localStorage.getItem("selectedKeyViewSphere") || "1");
    // dispatch(
    //   setActiveTab360(
    //     Number(localStorage.getItem("activeTabInteractionsViewSphere"))
    //   )
    // );
    dispatch(setActiveMenu360("1"));
    return () => {
      dispatch(setActiveTab360(null));
      // selectedKey != 2
      //   ? dispatch(setActiveTab360(null))
      //   : dispatch(
      //       setActiveTab360(
      //         Number(localStorage.getItem("activeTabInteractionsViewSphere"))
      //       )
      //     );
      dispatch(setActiveActivity360(""));
      setRelations([]);
      setGeneralInfo({});
      dispatch(setNewInteraction({ type: "updateElement" }));
      setMountRelation(false);
      dispatch(resetPrevIdsFromViewSphere());
      dispatch(setUpdateElementSuccessfully(null));
      dispatch({
        type: "RESET_CONTACT_HEADER_INFO",
      });
      dispatch(setOpenView360InDrawer(false));
      dispatch({
        type: SET_CONTACT_INFO_FROM_DRAWER,
        payload: {},
      });
      dispatch(
        setNbrChatInViewSPhere({
          number: 0,
          relation_id: "",
          id: "",
        })
      );
      // dispatch(resetPrevIdsFromViewSphere());
      dispatch(setOpenChatInViewSPhere(false));
    };
  }, [dispatch, elementId]);

  useEffect(() => {
    const getAccessDisussion = async (contactInfo) => {
      try {
        const { data: element } = await getElementSystemDetails(
          contactInfo?.id
        );
        dispatch({
          type: "SET_CONTACT_HEADER_INFO",
          payload: {
            ...contactInfo,
            access_discussion: element.access_discussion,
          },
        });
      } catch (err) {}
    };
    const fetchKPI = async () => {
      try {
        setMountRelation(false);
        dispatch(setNewInteraction({ type: "" }));
        const { data } = await getKPI(
          contactInfo?.family_id,
          params?.id || contactInfo?.id
        );
        if (
          // contactInfo?.owner?.id !== user.id &&
          (data
            .find((el) => el.family_id === 4)
            .child.some((el) => el.id === contactInfo?.owner?.id) &&
            data.find((el) => el.family_id === 4).number < 2) ||
          data.find((el) => el.family_id === 4)?.child?.length === 0
        ) {
          getAccessDisussion(contactInfo);
        }
        if (user?.rmc_access === "OUI") {
          const convRmc = await MainService.getConvRmc360(contactInfo?.id);
          setListConv(convRmc.data.data.filter((el) => el.conversation_id));
        }
        setRelations(
          data?.map((kpi) => ({
            ...kpi,
            children: kpi?.child?.length
              ? kpi?.child?.slice(0, 3)?.map((child) => ({
                  label: child?.label_data,
                  id: child?.id,
                  pipeline: child?.pipeline_label,
                  stage: child?.stage_label,
                }))
              : [],
          }))
        );
      } catch (err) {}
    };
    if (
      contactInfo.family_id &&
      (params?.id || contactInfo?.id) &&
      (mountRelation || newInteraction.type === "associateElement")
    ) {
      fetchKPI();
    }
  }, [
    dispatch,
    contactInfo?.id,
    contactInfo?.family_id,
    params?.id,
    newInteraction.type,
    mountRelation,
  ]);

  const getStages = async (element = {}, fetchElemetInfo = true) => {
    try {
      const res = await MainService.getStages(element.id);
      const currentIndex = res.data.data.findIndex(
        (item) => item.currentstage === 1
      );
      if (element?.id && fetchElemetInfo) {
        const { data: fetchElementInfo } = await getElementSystemDetails(
          elementId || params?.id
        );
        dispatch({
          type: "SET_CONTACT_HEADER_INFO",
          payload: { ...element, ...fetchElementInfo } || {},
        });
      }
      if (newInteraction?.type === "updateStage") {
        setTimeout(() => {
          dispatch(setNewInteraction({ type: "" }));
        }, 1000);
      }

      if (res.data.data.length > 0) {
        const dataSteps = res.data.data.map((item, index) => {
          const status =
            index < currentIndex
              ? "finish"
              : index > currentIndex
              ? "wait"
              : "process";
          return { ...item, status };
        });

        setDataSteps(dataSteps);
        const selectedTag = res.data.data.find((el) => el.currentstage === 1);
        setSelectedTags(selectedTag);
        selectedKey === "4" &&
          newInteraction?.type !== "updateStage" &&
          setRefreshInfo(true);

        if (
          isUpdate &&
          selectedTag?.final &&
          selectedTag?.default &&
          selectedTag?.resolved === 0 &&
          isFinalStage &&
          contactInfo?.family_id == 6
        ) {
          setLoading(false);
          SuccessWon();
          setIsFinalStage(false);
          message.success(
            t("vue360.closeSuccess", {
              name: contactInfo?.name || contactInfo?.reference,
            }),
            3
          );
        }
      } else {
        setWindowHeight(window.innerHeight - 135);
        setHeaderHeight(84);
      }
    } catch (error) {
      // Gérer les erreurs ici
    }
  };

  useEffect(() => {
    if (
      newInteraction?.type !== "updateElement" ||
      newInteraction?.type !== "updateStage"
    ) {
      // setIsUpdate(true);
    }
    return () => {
      dispatch({
        type: "RESET_CONTACT_HEADER_INFO",
      });
      dispatch(setActiveTab360(null));
      dispatch(setUpdateElementSuccessfully(null));
    };
  }, [dispatch]);

  const handleDropdownClick = (e) => {
    const key = e?.key;
    switch (key) {
      case "edit":
        contactInfo?.id && setOpenDrawerUpdate(true);
        break;
      case "fields":
        setOpenFields(true);
        break;
      case "pipeline":
        setOpenPipeline(true);
        break;
      default:
        message.info("This feature is not ready yet!");
        break;
    }
  };

  // Handle submit reasons form (after choosing reasons).
  const handleSumitReasonsForm = async () => {
    setLoadFinalStage(true);
    try {
      // setLoadUpdateInFinalStages(true);
      let selectedReasons = finalStageForm.getFieldsValue();
      let formData = new FormData();
      formData.append("new_stage_id", selectedStage);
      formData.append("id_element", params?.id || contactInfo?.id);
      formData.append("id_reason", selectedReasons?.reasons);
      if (typeof actionDeal === "number")
        formData.append("reason_type", actionDeal);
      else formData.append("reason_type", selectedReasons?.reasonType);
      let response = await MainService.updateElementToFinalStage(
        contactInfo.family_id,
        formData
      );
      if (response?.status === 200) {
        await getStages(contactInfo);
        if (actionDeal === 1) {
          // Trigger the particle animation
          SuccessWon();
        }
        if (actionDeal !== 1) {
          message.success(t("chat.message_system.deal_updated_stage"), 3);
        }

        finalStageForm.resetFields();
        dispatch(setUpdateElementSuccessfully(null));
        setReasons([]);
        setSelectedReasonType(null);
        setSelectedStage(null);
        if (activeTab360 == 3)
          dispatch(setNewInteraction({ type: "updateWithoutMercure" }));
        setActionDeal(null);
      }
    } catch (error) {
      console.log(`Error ${error}`);
      message.error("Something went wrong", 3);
    } finally {
      setLoadFinalStage(false);
    }
  };

  return (
    <div id="view-container">
      <Layout>
        {/* <Spin
          mask={true}
          spinning={mountRelation && selectedKey == 4 ? false : loading}
        > */}
        {show404 ? (
          <div className="height-screen bg-white">
            <NotFoundPage />
          </div>
        ) : (
          <>
            {loading ? (
              <LoadingViewSphere />
            ) : (
              <div>
                <HeaderViewSphere
                  openView360InDrawer={false}
                  key={contactInfo?.id}
                  from={from}
                  loading={loading}
                  dataSteps={dataSteps}
                  contactInfo={contactInfo}
                  reference={reference}
                  selectedTags={selectedTags}
                  setOpenEmailModal={setOpenEmailModal}
                  isUpdate={isUpdate}
                  setRefreshInfo={setRefreshInfo}
                  setSelectedKey={setSelectedKey}
                  setOpenModalCheckList={setOpenModalCheckList}
                  setOpenDrawerUpdate={setOpenDrawerUpdate}
                  setOpenFields={setOpenFields}
                  setOpenPipeline={setOpenPipeline}
                  setHeaderHeight={setHeaderHeight}
                  channel={channel}
                  contactType={contactType}
                  detailsInfo={detailsInfo}
                  setReasons={openView360InDrawer ? () => {} : setReasons}
                  setSelectedTags={setSelectedTags}
                  setWindowHeight={setWindowHeight}
                  headerHeight={
                    location.pathname === "/telephony/directory"
                      ? headerHeight + 71
                      : headerHeight
                  }
                  windowHeight={windowHeight}
                  source={source}
                  setSelectedStage={setSelectedStage}
                  selectedStage={selectedStage}
                  setSelectedReasonType={setSelectedReasonType}
                  setActionDeal={setActionDeal}
                  actionDeal={actionDeal}
                  loadFinalStage={loadFinalStage}
                  setLoading={setLoading}
                  setIsFinalStage={setIsFinalStage}
                  selectedKey={selectedKey}
                />
              </div>
            )}

            {/* <div className="h-1 bg-white"></div> */}
            <ContentViewSphere
              key={contactInfo?.id}
              selectedKey={selectedKey}
              setSelectedKey={setSelectedKey}
              setSelectedItem={setSelectedItem}
              relations={relations}
              collapsed={collapsed}
              contactInfo={contactInfo}
              setRelations={setRelations}
              generalInfo={generalInfo}
              loading={loading}
              headerHeight={
                location.pathname === "/telephony/directory"
                  ? headerHeight + 71
                  : headerHeight
              }
              from={from}
              windowHeight={windowHeight}
              kpi={kpi}
              openFields={openFields}
              setOpenFields={setOpenFields}
              isUpdate={isUpdate}
              setIsUpdate={setIsUpdate}
              dataSteps={dataSteps}
              listConv={listConv}
              tasksTypes={tasksTypes}
              setSelectedKeySideBar={setSelectedKeySideBar}
              countTasks={countTasks}
              setTasksTypes={setTasksTypes}
              setCountTasks={setCountTasks}
              setKpi={setKpi}
              disabledRelation={disabledRelation}
              selectedItem={selectedItem}
              selectedKeySideBar={selectedKeySideBar}
              mountChat={mountChat}
              setMountchat={setMountchat}
              openTask={openTask}
              setOpenTask={setOpenTask}
              setCollapsed={setCollapsed}
              openDrawerUpdate={openDrawerUpdate}
              setOpenDrawerUpdate={setOpenDrawerUpdate}
              setOpenPipeline={setOpenPipeline}
              openPipeline={openPipeline}
              handleSumitReasonsForm={handleSumitReasonsForm}
              reasons={reasons}
              setReasons={setReasons}
              selectedReasonType={selectedReasonType}
              setSelectedReasonType={setSelectedReasonType}
              selectedStage={selectedStage}
              setSelectedStage={setSelectedStage}
              finalStageForm={finalStageForm}
              setListConv={setListConv}
              actionDeal={actionDeal}
              setActionDeal={setActionDeal}
              loadFinalStage={loadFinalStage}
              openView360InDrawer={false}
              selectedTags={selectedTags}
              refreshInfo={refreshInfo}
              setRefreshInfo={setRefreshInfo}
              // className="menuViewSphere"
            />
          </>
        )}
        {/* </Spin> */}
        {user.rmc_access === "OUI" ? (
          <iframe
            src={`${URL_ENV?.REACT_APP_RMC_URL}?Token=${token}`}
            title="chat"
            display="block"
            width="100%"
            // height= {`${deviceHeight}px -120px`}
            sendbox="allow-same-origin allow-popups"
            allowFullscreen="true"
            style={{
              display: "none",
              border: "none",
            }}
            allowtransparency="true"
            // onLoad={() => setHide(true)}
          ></iframe>
        ) : null}
        {/* <Tooltip
          title={!open ? "Open the list of modules" : ""}
          // id="showMeViewSphere"
          placement="left"
        > */}
        {/* <FloatButton.Group
          open={open}
          trigger="click"
          style={{
            right: 15,
            bottom: 25,
          }}
          // onOpenChange={(e) => setOpen(!open)}
          onClick={() => setOpen(!open)}
          icon={
            <div>
              <ArrowsAltOutlined />
              <Badge
                dot={
                  selectedKey === "5" ||
                  selectedKey === "6" ||
                  (selectedKey === "2" && activeTab360 === 5)
                }
                id="showMeViewSphere"
                className="absolute -top-[10px] "
              />
            </div>
          }
        >
          <Tooltip title="Notes" placement="left">
            <FloatButton
              icon={<FileTextOutlined />}
              // style={{ width: "60px" }}
              className="flex justify-end"
              type={selectedKey === "5" ? "primary" : "default"}
              onClick={() => {
                setSelectedKey("5");
                // setIdModule(4);
              }}
            />
          </Tooltip>
          <Tooltip title={t("layout_profile_details.files")} placement="left">
            <FloatButton
              type={selectedKey === "6" ? "primary" : "default"}
              icon={<FileAddOutlined />}
              onClick={() => {
                setSelectedKey("6");
                // setIdModule(1);
              }}
            />
          </Tooltip>
          <Tooltip title="Email" placement="left">
            <FloatButton
              icon={<MailOutlined />}
              type={openModalEmail ? "primary" : "default"}
              onClick={() => {
                dispatch(setOpenModalEmail(true));
              }}
            />
          </Tooltip>
          <Tooltip title="RMC" placement="left">
            <FloatButton
              icon={<HiOutlineShare />}
              type={
                selectedKey === "2" && activeTab360 === 5 ? "primary" : "default"
              }
              onClick={() => {
                setSelectedKey("2");
                dispatch(setActiveTab360(5));
              }}
            />
          </Tooltip>
        </FloatButton.Group> */}

        <Drawer
          width={1080}
          key={contactInfoFromDrawer?.id}
          title={
            <div className="flex items-center justify-between gap-2">
              {
                familyIcons(t)?.find(
                  (el) => el.key === contactInfoFromDrawer?.family_id
                )?.label
              }
              <Button
                type="link"
                onClick={() =>
                  navigate(
                    pathViewSphere(
                      contactInfoFromDrawer?.family_id,
                      contactInfoFromDrawer?.id
                    )
                  )
                }
              >
                {contactInfoFromDrawer?.name || contactInfoFromDrawer?.reference
                  ? t("chat.goto")
                  : null}{" "}
                {contactInfoFromDrawer?.name ||
                  contactInfoFromDrawer?.reference}
              </Button>
            </div>
          }
          onClose={() => {
            dispatch(setNewInteraction({ type: "" }));
            dispatch(setOpenView360InDrawer(false));
            dispatch({
              type: SET_CONTACT_INFO_FROM_DRAWER,
              payload: {},
            });
            dispatch(resetPrevIdsFromViewSphere());
          }}
          open={openView360InDrawer}
          styles={{
            body: {
              overflowY: "hidden",
              padding: 0,
            },
          }}
        >
          <ViewSphere elementId={contactInfoFromDrawer?.id} />
        </Drawer>

        {/* <Drawer
        width={1080}
        onClose={() => {
          setOpenPipeline(false);
        }}
        open={openPipeline}
        styles={{
          body: {
            padding: 0,
          },
        }}
        title={t("menu2.pipeline")}
      >
        <ShowPipelineStage items={items} keyTab={contactInfo?.family_id} />
      </Drawer> */}
      </Layout>
      {/* {!loading &&
      process.env.REACT_APP_BRANCH.includes("dev") &&
      contactInfo?.id === params?.id ? (
        <Tour
          placement="right"
          open={openTour}
          zIndex={9999}
          onClose={closeTourViewSphere}
          steps={steps}
          actionsRender={(originNode, { current, total }) => (
            <>
              {current !== total - 1 && (
                <Button size="small" onClick={closeTourViewSphere}>
                  Skip
                </Button>
              )}
              {originNode}
            </>
          )}
        />
      ) :  
      null} */}
    </div>
  );
};
export default memo(ViewSphere2);
