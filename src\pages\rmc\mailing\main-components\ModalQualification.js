import { memo, useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Form, Button, Divider, Modal, Select, Spin, Input } from "antd";
import { toastNotification } from "components/ToastNotification";
import MainService from "services/main.service";
import ChoiceIcons from "pages/components/ChoiceIcons";
import {
  createTagsActivity,
  updateTagsActivity,
} from "pages/voip/services/services";

//
const { Option } = Select;

const handleApiError = (err, t) => {
  if (err.name !== "CanceledError" && err?.response?.status !== 401) {
    toastNotification("error", t("toasts.somethingWrong"), "topRight");
  }
};
//
const ModalQualification = ({
  open,
  setOpen,
  record,
  usedAccount,
  setDataSource,
}) => {
  //
  const [t] = useTranslation("common");
  const [form] = Form.useForm();
  //
  const tag = record.tags;
  const tags = tag?.tags?.map((e) => e.id) ?? [];
  const note = tag?.note ?? "";

  //
  const [qualifyOptions, setQualifyOptions] = useState([]);
  const [loadingSubmit, setLoadingSubmit] = useState(false);
  const [state, setState] = useState({
    tags,
    note,
  });
  //
  const fetchQualificationOptions = useCallback(async () => {
    try {
      const { data } = await MainService.getTags();
      setQualifyOptions(
        data?.map((item) => ({
          ...item,
          value: item.id,
        }))
      );
    } catch (err) {
      handleApiError(err, t);
    }
  }, [t]);

  useEffect(() => {
    fetchQualificationOptions();
  }, [fetchQualificationOptions]);
  //
  const handleCancel = () => {
    setOpen({});
    form.resetFields();
    form.resetFields();
  };
  //
  const onFinish = async (values) => {
    setLoadingSubmit(true);
    try {
      const formData = new FormData();
      const hasTags = tags?.tags?.length > 0;
      !hasTags && formData.append("id", record.key);
      formData.append("type", "email");
      formData.append("account_id", usedAccount?.value);
      usedAccount.departmentId?.forEach((e) =>
        formData.append("departement_id[]", e)
      );
      !!values?.note && formData.append("note", values.note);
      values.qualification.forEach((tag) =>
        formData.append(`tags[${tag}]`, ``)
      );
      let response;
      if (hasTags) {
        response = await updateTagsActivity(tags?._id, formData);
      } else {
        response = await createTagsActivity(formData);
      }
      response = response?.data?.data?.tags;
      setDataSource((prev) =>
        prev.map((item) => {
          if (item.key === record.key)
            return {
              ...item,
              tags: response,
            };
          return item;
        })
      );
      const successText = hasTags
        ? t("mailing.successUpdate")
        : t("mailing.successQualif");

      toastNotification("success", successText, "topRight");
      handleCancel();
    } catch (err) {
      handleApiError(err, t);
    } finally {
      setLoadingSubmit(false);
    }
  };
  //
  const modalTitle = (
    <div className="relative flex flex-grow flex-col">
      <div className="space-y-1">
        <p className="text-base font-semibold">
          {tags?.tags?.length
            ? t("mailing.updateQualification")
            : "Qualification"}
        </p>
        <div className="flex space-x-1 text-sm">
          <p className="whitespace-nowrap font-semibold">
            {t("emailTemplates.subjectMail")}:
          </p>
          <p className="truncate">{record?.subject}</p>
        </div>
      </div>
      <Divider style={{ margin: "5px 0 0 0" }} />
    </div>
  );
  //
  //
  return (
    <Modal
      width={450}
      title={modalTitle}
      open={open}
      onCancel={handleCancel}
      maskClosable={false}
      footer={[
        <Button
          key="save-button"
          type="primary"
          loading={loadingSubmit}
          onClick={() => form.submit()}
          disabled={
            state.note === note &&
            JSON.stringify(state.tags) === JSON.stringify(tags)
          }
        >
          {t("voip.save")}
        </Button>,
      ]}
    >
      <Form form={form} layout="vertical" onFinish={onFinish}>
        <Form.Item
          label="Qualification"
          name="qualification"
          rules={[
            {
              required: true,
            },
          ]}
          initialValue={tags}
        >
          <Select
            placeholder={t("voip.selectQualify")}
            allowClear
            showSearch
            mode="multiple"
            maxTagCount="responsive"
            optionLabelProp="label"
            filterOption={(input, option) =>
              option?.label.toLowerCase().includes(input.toLowerCase())
            }
            suffixIcon={<Spin spinning={!qualifyOptions.length} size="small" />}
            onChange={(value) => setState((prev) => ({ ...prev, tags: value }))}
          >
            {qualifyOptions?.map((option) => (
              <Option
                key={option?.id}
                value={option?.value}
                label={option?.label}
                color={option?.color}
                icon={option?.icon}
              >
                <div className="flex items-center space-x-2">
                  <ChoiceIcons icon={option?.icon} />
                  <span style={{ color: option?.color }}>{option.label}</span>
                </div>
              </Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item label={t("voip.qualifNote")} name="note" initialValue={note}>
          <Input.TextArea
            showCount
            allowClear
            rows={3}
            autoSize={{
              minRows: 3,
              maxRows: 6,
            }}
            maxLength={500}
            placeholder={t("voip.writeNote")}
            style={{ marginBottom: "1.5rem" }}
            onChange={(e) =>
              setState((prev) => ({ ...prev, note: e?.target?.value }))
            }
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default memo(ModalQualification);
