import { Avatar, List, Skeleton, Space } from "antd";
import React, { useState } from "react";
import { LazyLoadImage } from "react-lazy-load-image-component";

const ListActivity = ({ load, choiceCard, item, index }) => {
  const [paddingHover, setPaddingHover] = useState({
    transition: "all 0.4s ease-in-out",
    delay: "0.3s",
    padding: "0.5em",
  });
  return (
    <List.Item
      className={` relative my-6 ml-5 scale-[0.99] rounded-md   hover:cursor-text hover:transition-all hover:delay-300 hover:ease-in-out`}
      onMouseOver={() =>
        setPaddingHover({
          transition: "all 0.4s ease-in-out",
          delay: "0.3s",
          padding: "0.3em",
        })
      }
      onMouseLeave={() =>
        setPaddingHover({
          transition: "all 0.4s ease-in-out",
          delay: "0.3s",
          padding: "0.5em",
        })
      }
      style={{
        background: load ? "" : "#f1f5f9",
        // #3a5065
      }}
    >
      <Skeleton
        loading={load}
        active
        avatar
        size="small"
        paragraph={{ rows: 0 }}
      >
        <Space>
          <div className="absolute -top-5 left-[12%] rounded-md bg-white px-3 py-2 font-semibold">
            {item.title}
          </div>
          <Avatar
            style={{
              marginLeft: "-3em",
              ...paddingHover,
              background:
                "linear-gradient(-89deg, #3282b866 0 70%, #bbe1fa7a  0% 100%)",
              width: "50px",
              height: "50px",
              // width: "6vmin",
              // height: "6vmin",
            }}
            src={
              <LazyLoadImage
                loading="lazy"
                id="target-image"
                effect={"blur"}
                className={`  h-auto w-full  object-contain  `}
                placeholderSrc={`https://xsgames.co/randomusers/avatar.php?g=pixel&key=${index}`}
                style={{
                  aspectRatio: "1",
                }}
                src={`https://xsgames.co/randomusers/avatar.php?g=pixel&key=${index}`}
                alt={"item_avatar"}
              />
            }
            shape="square"
            className="hover:scale-[0.99] hover:transition-all hover:delay-300 hover:ease-in-out"
          />
          <div className="px-2">{item.desc}</div>
        </Space>
        <div className="pr-2">16 october 2023</div>
      </Skeleton>
    </List.Item>
  );
};

export default ListActivity;
