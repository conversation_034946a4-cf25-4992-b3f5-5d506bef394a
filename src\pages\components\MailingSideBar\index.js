import {
  DeleteOutlined,
  EditOutlined,
  MailOutlined,
  SendOutlined,
  SnippetsOutlined,
  StarOutlined,
} from "@ant-design/icons";
import { Button, Col, Row, Select, Menu } from "antd";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { MdLabelImportantOutline } from "react-icons/md";
import { useSelector } from "react-redux";
import { NavLink } from "react-router-dom";
import { useNavigate } from "react-router-dom";
import {
  setSearchEmail,
  setSelectedAccount,
} from "../../../new-redux/actions/mail.actions";
import { useDispatch } from "react-redux";

export const MailingSideBar = (props) => {
  const [t] = useTranslation("common");
  const navigate = useNavigate();
  const [selectedLink, setSelectedLink] = useState("");
  const dispatch = useDispatch();
  const { loadingAccount, dataAccounts } = useSelector(
    (state) => state.mailReducer
  );

  const usedAccount = dataAccounts?.find((item) => item.selected === true);

  const MailingTypes = [
    {
      name: t("mailing.inbox"),
      link: `inbox`,
      icon: <MailOutlined />,
    },
    {
      name: t("mailing.sent"),
      link: `sent`,
      icon: <SendOutlined />,
    },
    {
      name: t("mailing.draft"),
      link: `drafts`,
      icon: <SnippetsOutlined />,
    },
    {
      name: t("mailing.starred"),
      link: `starred`,
      icon: <StarOutlined />,
    },
    {
      name: "Important",
      link: `important`,
      icon: <MdLabelImportantOutline />,
    },
    {
      name: t("mailing.trash"),
      link: `trash`,
      icon: <DeleteOutlined />,
    },
  ];

  const getTokenFromUrl = () => {
    const url_string = window.location.href;
    var url = new URL(url_string);

    setSelectedLink(url.pathname.split("/")[3]);
  };

  useEffect(() => {
    getTokenFromUrl();
  }, []);

  return (
    <Col span={4} style={{ backgroundColor: "#F8FAFC" }}>
      <Menu>
        <Row style={{ marginTop: "12px" }}>
          <Select
            loading={loadingAccount}
            value={
              loadingAccount && dataAccounts.length === 0
                ? "loading ..."
                : dataAccounts.length > 0
                ? dataAccounts.find((item) => item.selected === true)
                : t("mailing.empty")
            }
            style={{ width: "100%" }}
            onChange={(e) => {
              dispatch(setSelectedAccount(e));
              navigate(`/mailing/${e}/inbox`);
              dispatch(setSearchEmail(""));

              setSelectedLink("inbox");
            }}
            options={props.ListAccounts}
          />
        </Row>
        <Row
          style={{
            marginTop: "12px",
          }}
        >
          <Button
            style={{ width: "100%" }}
            type="primary"
            onClick={() => {
              props.setModalOpen(true);
              props.setopenEditor({ state: false });
            }}
            icon={<EditOutlined />}
          >
            <span className="hidden  ">{t("mailing.newMsg")}</span>
          </Button>
        </Row>

        {MailingTypes.map((item) => (
          <NavLink to={`/mailing/${usedAccount?.value ?? 0}/` + item.link}>
            <Row style={{ width: "100%", marginTop: "1px" }}>
              <Button
                className={`flex items-center justify-start ${
                  selectedLink === item.link ? "bg-gray-300" : ""
                }`}
                block
                type="text"
                onClick={() => {
                  props.setPath(item.link);
                  setSelectedLink(item.link);
                  props.setopenEditor({ state: false, type: "" });
                }}
                icon={item.icon}
              >
                <p className="hidden lg:flex">{item.name}</p>
              </Button>
            </Row>
          </NavLink>
        ))}
      </Menu>
    </Col>
  );
};
