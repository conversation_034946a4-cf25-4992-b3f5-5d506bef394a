import { toastNotification } from "components/ToastNotification";
import { CLOSE_DRAWER_CHAT, OPEN_DRAWER_CHAT } from "new-redux/constants";

export const openDrawerChat = (uuid, _id, source) => async (dispatch) => {
  if (!uuid && source !== "popoverChat") {
    toastNotification("error", "Cannot find this user", "topRight");
    return;
  }
  dispatch({
    type: OPEN_DRAWER_CHAT,
    payload: { source, uuid, _id },
  });
};

export const closeDrawerChat = () => async (dispatch) => {
  dispatch({
    type: CLOSE_DRAWER_CHAT,
  });
};
