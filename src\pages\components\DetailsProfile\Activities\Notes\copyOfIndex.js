import React, { useEffect, useState, lazy, Suspense, useRef } from "react";
import {
  Button,
  Card,
  Space,
  Avatar,
  Tooltip,
  Typography,
  Spin,
  Modal,
  Popconfirm,
  message,
  Empty,
} from "antd";
import { DeleteOutlined, EditOutlined } from "@ant-design/icons";
import RichTextInput from "../../../../../components/tiptap_richtext/RichTextInput";
import MainService from "../../../../../services/main.service";
import { useSelector } from "react-redux";
import moment from "moment";
import { getName } from "../../../../layouts/chat/utils/ConversationUtils";
import InfiniteScroll from "react-infinite-scroll-component";
import TestInfiniteScroll from "./TestInfiniteScroll";
import NoteAvatar from "./NoteAvatar";
import NoteSearch from "./NoteSearch";

function CopyNotes() {
  const [content, setContent] = useState("");
  const [notes, setNotes] = useState([]);
  const [onFetch, setOnFetch] = useState(false); // [1
  const [onSaveLoading, setOnSaveLoading] = useState(false); // [1
  const contactInfo = useSelector(
    (state) => state?.contacts?.contactHeaderInfo
  );

  const bottom = useRef(null);

  const [nextPage, setNextPage] = useState(1); // [1
  const [hasMore, setHasMore] = useState(true); // [1

  const [onUpdateLoading, setOnUpdateLoading] = useState(false); // [1

  const [person, setPerson] = useState({}); // [1

  const [noteToEditId, setNoteToEditId] = useState(null);
  const [noteToEditContent, setNoteToEditContent] = useState("");

  const toggleNoteEdit = (note) => {
    if (noteToEditId == note._id) {
      setNoteToEditId(null);
      setNoteToEditContent("");
    } else {
      setNoteToEditId(note._id);
      setNoteToEditContent(note.content);
    }
  };

  useEffect(() => {
   // console.log(content);
  }, [content]);

  const noteCreate = () => {
    setOnSaveLoading(true);

    let data = {
      content: content,
    };
    let familyId = contactInfo?.family_id;
    let elementId = contactInfo?.id;
    MainService.createNote360(data, familyId, elementId)
      .then((response) => {
        console.log(response);
        setNotes([response?.data?.data, ...notes]);
        setContent("");
        message.success("Note created successfully");
      })
      .catch((error) => {
        console.log(error);
      })
      .finally(() => {
        setOnSaveLoading(false);
      });
  };

  const noteDelete = (id) => {
    MainService.deleteNote360(id)
      .then((response) => {
        //console.log(response);
        setNotes(notes.filter((note) => note._id !== id));
        message.success("Note deleted successfully");
      })
      .catch((error) => {
        console.log(error);
      });
  };

  const updateNote = (id) => {
    setOnUpdateLoading(true);
    let data = {
      content: noteToEditContent,
    };
    MainService.updateNote360(id, data)
      .then((response) => {
       // console.log(response);
        setNotes(
          notes.map((note) => {
            if (note._id == id) {
              return response?.data?.data;
            }
            return note;
          })
        );
        setNoteToEditId(null);
        setNoteToEditContent("");
      })
      .catch((error) => {
        console.log(error);
      })
      .finally(() => {
        setOnUpdateLoading(false);
      });
  };

  const getNotes = async () => {
    setOnFetch(true);
    await MainService.getNotes360(
      contactInfo?.family_id,
      contactInfo?.id,
      1,
      10
    )
      .then((response) => {
       // console.log(response);
        setNotes(response?.data?.data?.notes?.data);
        setPerson({
          name: response?.data?.data?.label_data,
          avatar: response?.data?.data?.avatar,
        });
        return response?.data?.data?.notes?.next_page_url;
      })
      .then((nextPageUrl) => {
        if (nextPageUrl) {
          console.log("HAS MORE");
          setNextPage((prevPage) => {
           // console.log("prevPage + 1 : ", prevPage + 1);
            return prevPage + 1;
          });
        }
      })
      .catch((error) => {
        console.log(error);
      })
      .finally(() => {
        setOnFetch(false);
      });
  };

  const getNotesPages = async (pageToGet) => {
    console.log("FIRED GET NOTES PAGE");

    console.log("nextPage on getNotesPages", nextPage);
    await MainService.getNotes360(
      contactInfo?.family_id,
      contactInfo?.id,
      pageToGet,
      10
    )
      .then((response) => {
        console.log(response);
        // setNotes([...notes, ...response?.data?.data?.notes?.data]);
        //merge new data with old dasta and remove duplicate
        setNotes((prev) => {
          let newNotes = [...prev, ...response?.data?.data?.notes?.data];
          let uniqueNotes = newNotes.filter(
            (item, index) =>
              newNotes.findIndex((item2) => item2._id === item._id) === index
          );
          return uniqueNotes;
        });

        return response?.data?.data?.notes?.next_page_url;
      })
      .then((nextPageUrl) => {
        if (nextPageUrl) {
          console.log("HAS MORE");
          setNextPage((prevPage) => {
            console.log("prevPage + 1 : ", prevPage + 1);
            return prevPage + 1;
          });
        }
      })
      .catch((error) => {
        console.log(error);
      })
      .finally(() => {
        setOnFetch(false);
      });
  };

  useEffect(() => {
    getNotes();
  }, []);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          getNotesPages(nextPage);
        }
      },
      { threshold: 1 }
    );

    observer.observe(bottom.current);

    return () => {
      observer.disconnect();
    };
  }, [bottom]);

  return (
    <>
      <div className="w-full flex-col items-end">
        <Typography.Title
          level={5}
          style={{
            marginBottom: "10px",
          }}
        >
          Note
        </Typography.Title>
        <RichTextInput
          source="notes"
          content={content}
          setContent={(e) => {
            if (!e) setContent("");
            setContent(e?.trim());
          }}
          setEditorContent={(e) => {
            if (!e) setContent("");
            setContent(e?.trim());
          }}
        />
        <Button
          type="primary"
          style={{ marginTop: "10px" }}
          onClick={content ? noteCreate : null}
          loading={onSaveLoading}
        >
          Create Note
        </Button>
      </div>
      <NoteSearch elementId={contactInfo?.id} />

      {onFetch ? (
        <div className="flex h-64 items-center justify-center">
          <Spin />
        </div>
      ) : (
        <div className="w-full">
          {notes && notes?.length > 0 ? (
            <>
              {notes &&
                notes.map((note, index) => (
                  <Card
                    key={note?._id}
                    style={{ marginTop: "10px", width: "100%" }}
                  >
                    <Space direction="vertical" style={{ width: "100%" }}>
                      <div className="flex w-full items-center justify-between">
                        <div className="flex space-x-2">
                          <NoteAvatar
                            name={getName(person?.name, "avatar")}
                            avatar={person?.avatar}
                          />

                          <div className="flex-col">
                            <div className="text-sm font-semibold">
                              {getName(person.name, "name")}
                            </div>
                            <div className="font-thick text-xs">
                              {moment(note.created_at).format(
                                "DD/MM/YYYY HH:mm"
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="flex space-x-1">
                          <div className="">
                            <Tooltip placement="top" title="Edit">
                              <Button
                                type="text"
                                icon={<EditOutlined />}
                                onClick={() => toggleNoteEdit(note)}
                              />
                            </Tooltip>
                          </div>

                          <div className="">
                            <Tooltip placement="top" title="Delete">
                              <Popconfirm
                                title="Delete the note"
                                description="Are you sure to delete this note?"
                                onConfirm={() => noteDelete(note._id)}
                                // onCancel={cancel}
                                okText="Yes"
                                cancelText="No"
                              >
                                <Button
                                  type="text"
                                  icon={<DeleteOutlined />}
                                  // onClick={() => noteDelete(note._id)}
                                  // onClick={showDeleteConfirm}
                                />
                              </Popconfirm>
                            </Tooltip>
                          </div>
                        </div>
                      </div>

                      {noteToEditId == note._id && noteToEditContent != "" ? (
                        <div className="w-full flex-col ">
                          <RichTextInput
                            source="note_update"
                            content={note.content}
                            editorContent={noteToEditContent}
                            setContent={(e) => {
                              if (!e) setNoteToEditContent("");
                              setNoteToEditContent(e?.trim());
                            }}
                            setEditorContent={(e) => {
                              if (!e) setNoteToEditContent("");
                              setNoteToEditContent(e?.trim());
                            }}
                          />
                          <Button
                            type="primary"
                            style={{ marginTop: "10px" }}
                            onClick={() => {
                              if (noteToEditContent) updateNote(note._id);
                            }}
                            loading={onUpdateLoading}
                          >
                            Save
                          </Button>
                        </div>
                      ) : (
                        <div
                          className="w-full px-4"
                          dangerouslySetInnerHTML={{ __html: note.content }}
                        />
                      )}
                    </Space>
                  </Card>
                ))}
            </>
          ) : (
            <Empty
              style={{
                marginTop: "10px",
              }}
              description={<span>No notes</span>}
            />
          )}
          <div
            ref={bottom}
            className="flex h-5 w-5 items-center justify-center bg-blue-600"
          />
        </div>
      )}
    </>
  );
}

export default CopyNotes;
