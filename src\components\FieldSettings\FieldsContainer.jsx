/**
 * @name FieldsContainer
 *
 * @description Fields manager and select module wrapper.
 *
 * @param {String} familyId The id of the family from drawer.
 * @param {Function} setIsUpdateFromVue360 Re-trigger the get fields api on add/update field from viewsphere.
 *
 * @returns {JSX.Element} Fields manager container.
 */

// React and 3rd party libraries imports.
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
// Common imports.
import { clearSearchInput } from "../../new-redux/actions/form.actions/form";
import FieldsSettingArea from "./FieldsSettingArea";
import { GenericTabs } from "../../pages/components/Tabs";
import { displayFamiliesIcons } from "../../utils/displayIcon";
import { DESTROY_FIELD_STATE } from "../../new-redux/constants";

function FieldsContainer({ familyId = "", setIsUpdateFromVue360 = () => {} }) {
  const [page, setPage] = useState(1);
  const [defaultActiveTabKey, setDefaultActiveKey] = useState(null);
  const [typeFilterId, setTypeFilterId] = useState("");
  const [editingKey, setEditingKey] = useState("");
const [t] = useTranslation("common");
  const { families } = useSelector((state) => state?.families);
  const dispatch = useDispatch();
  const location = useLocation();
  const navigate = useNavigate();
  const { id } = useParams();

  
  // Families tab elements
  const items =
    families &&
    families.map((element) => ({
      key: element?.id,
      icon:displayFamiliesIcons(element?.label,4,
        4),
      label:     t(`fields_management.${element?.label.toLowerCase()}`).includes("fields_management") ? element?.label :t(`fields_management.${element?.label.toLowerCase()}`),
    }));

  // Handle change tab
  const onChange = () => {
    setPage(1);
    setTypeFilterId("");
  };

  // Display the selected family label.
  const familyLabelFromKey = (key) => {
    let index = families && families.findIndex((element) => element?.id == key);
    if (index > -1) {
      return families[index]?.label;
    }
  };

  // Set default (pre-selected) family id.
  useEffect(() => {
    let splittedLocationArray = location?.pathname.split("/");
    if (familyId) {
      setDefaultActiveKey(familyId);
    }
    if (
      splittedLocationArray[1] === "settings" &&
      splittedLocationArray[2] === "fields"
    ) {
      let index =
        families &&
        families.findIndex(
          (element) => element?.label == splittedLocationArray[3]
        );
      if (index > -1) {
        setDefaultActiveKey(families[index]?.id);
      }
    }
  }, [location, dispatch,id, families, familyId]);

  useEffect(() => {
    return ()=>{
 dispatch({type:DESTROY_FIELD_STATE})
      
    }
  }, []);

  return (
    <>
      <div className="px-4 pt-4">
        {/* Main tab header */}
        {!familyId ? (
          <GenericTabs
        
            items={items}
            onChange={onChange}
            defaultActiveKey={defaultActiveTabKey}
            activeKey={defaultActiveTabKey}
            onClick={(key) => {
              setEditingKey("");
          
              
              navigate(`/settings/fields/${familyLabelFromKey(key)}`);
            }}
          />
        ) : null}
      </div>
      {/* Fields wrapper */}
      <FieldsSettingArea
      key={familyId}
        page={page}
        typeFilterId={typeFilterId}
        editingKey={editingKey}
        setEditingKey={setEditingKey}
        familyIdFromDrawer={familyId}
        setIsUpdateFromVue360={setIsUpdateFromVue360}
      />
    </>
  );
}

export default FieldsContainer;
