import {
  CheckOutlined,
  DeleteOutlined,
  EditOutlined,
  <PERSON>Outlined,
  MoreOutlined,
  RestOutlined,
  <PERSON>CameraOutlined,
  EyeOutlined,
  MessageOutlined,
} from "@ant-design/icons";
import { Button, Dropdown, Typography } from "antd";
import { useTranslation } from "react-i18next";
import { FiCopy } from "react-icons/fi";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";

import MainService from "../services/main.service";
import { getTokenRoom } from "../new-redux/actions/visio.actions/createVisio";
import Confirm from "./GenericModal";
import { URL_ENV } from "index";
import {
  setKpiDateVisio,
  setKpiVisio,
} from "new-redux/actions/visio.actions/visio";
import { displayStatKey } from "pages/tasks/KpiGrid";
import { closeDrawerChat } from "new-redux/actions/voip.actions/handleDrawerChat";

const DropdownTask = ({
  props,
  handleDelete,
  editTask,
  handleOpenActivityIn360,
  source,
  from = "",
  openChat = () => {},
  selectedCalendarView = null,
  setOptionTask = () => {},
}) => {
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const { tabKey } = useSelector((state) => state.visioList);
  const { user } = useSelector((state) => state.user);
  const items = [
    from !== "Kanban" && {
      label: t("wiki.Edit"),
      key: "1",
      icon: <EditOutlined style={{ fontSize: "13px" }} />,
      danger: false,
      disabled: props?.can_update_task === 0,
    },
    from !== "Kanban" &&
      source !== "viewSphere" && {
        label: t("tasks.view360"),
        key: "6",
        icon: <EyeOutlined style={{ fontSize: "13px" }} />,
        danger: false,
      },
    3 === props?.tasks_type_id &&
    (props?.guests?.some((el) => el.id === user.id) ||
      props?.followers?.some((el) => el.id === user.id) ||
      props?.owner_id?.id === user.id ||
      props?.creator?.id === user.id)
      ? {
          label: t("visio.participate"),
          key: "2",
          icon: <VideoCameraOutlined style={{ fontSize: "13px" }} />,
        }
      : null,

    from !== "Kanban" &&
      user?.access?.["chat"] === "1" && {
        label: t("menu1.chat"),
        key: "3",
        icon: <MessageOutlined style={{ fontSize: "13px" }} />,
        // disabled: !props?.can_create_room || openDrawerChat,
        disabled: props?.can_create_room === 0,
      },
    3 === props?.tasks_type_id
      ? {
          label: (
            <Typography.Paragraph
              copyable={{
                text: `${URL_ENV?.REACT_APP_DOMAIN}/?room_visio_name=${props?.location}`,

                icon: [
                  <div>
                    {" "}
                    {t("chat.header.copyLink")} <FiCopy />
                  </div>,
                  <div className="space-x-1">
                    <Typography.Text className=" text-[#000000e0]">
                      {t("visio.linkClicked")}
                    </Typography.Text>
                    <CheckOutlined className="text-green-600" />{" "}
                  </div>,
                ],
              }}
              // onClick={(e, t) => console.log(e, t)}
            >
              {/* {t("chat.header.copyLink")} */}
            </Typography.Paragraph>
          ),
          key: "5",
          icon: <LinkOutlined style={{ fontSize: "13px" }} />,
        }
      : null,
    {
      type: "divider",
    },
    {
      label: (
        <Typography.Paragraph
          copyable={{
            text: `${URL_ENV?.REACT_APP_DOMAIN}/tasks/${props?.id}`,

            icon: [
              <div>
                {" "}
                {t("tasks.copyLink")} <FiCopy />
              </div>,
              <div className="space-x-1">
                <Typography.Text className=" text-[#000000e0]">
                  {t("visio.linkClicked")}
                </Typography.Text>
                <CheckOutlined className="text-green-600" />{" "}
              </div>,
            ],
          }}
          // onClick={(e, t) => console.log(e, t)}
        >
          {/* {t("chat.header.copyLink")} */}
        </Typography.Paragraph>
      ),
      key: "7",
      icon: <LinkOutlined style={{ fontSize: "13px" }} />,
    },
    {
      label: t("wiki.Delete"),
      key: "4",
      icon: <DeleteOutlined style={{ fontSize: "13px" }} />,
      danger: true,
      disabled: props?.can_update_task === 0,
    },
  ];

  return (
    <Dropdown
      menu={{
        items,
        onClick: (e) => {
          setOptionTask(e.key);
          if (e.key === "1") {
            editTask(props);
          }

          if (e.key === "2") {
            dispatch(
              getTokenRoom({
                room: props.location,
                errorText1: t("toasts.errorFetchApi"),
                errorText2: t("toasts.errorRoomNotFound"),
              })
            );
          }
          if (e?.key === "3") {
            dispatch(closeDrawerChat());
            openChat(props.id);
          } else if (e.key === "4") {
            Confirm(
              `Delete "${props.label}" `,
              "Confirm",
              <RestOutlined style={{ color: "red" }} />,
              async function func() {
                await handleDelete([props.id], props.start_date);
                if (window.location.pathname.includes("visio")) {
                  let kpi = { data: [] };
                  let kpiDate = { data: [] };
                  kpiDate = await MainService.getKpiDateVisio();
                  kpi = await MainService.getKpiVisio(tabKey);

                  dispatch(
                    setKpiVisio(
                      kpi &&
                        kpi.data && [
                          ...Object.entries(kpi.data).map(([key, value]) => {
                            return {
                              title: displayStatKey(t, key, ""),
                              value: value,
                              tr: false,
                            };
                          }),
                        ]
                    )
                  );
                  dispatch(
                    setKpiDateVisio(
                      kpiDate &&
                        kpiDate.data &&
                        Object.entries(kpiDate.data).map(([key, value]) => {
                          return { title: key, value: value, tr: true };
                        })
                    )
                  );
                }
              },
              true
            );
          }
          if (e?.key === "6") {
            handleOpenActivityIn360(props);
          }
        },
      }}
      trigger={["click"]}
      overlayStyle={{ zIndex: "99999" }}
    >
      <Button
        icon={<MoreOutlined style={{ fontSize: "13px" }} />}
        onClick={(e) => e && e?.stopPropagation()}
        type="text"
        shape="circle"
        size="small"
        style={{
          padding: 0,
          color:
            source === "activity-calendar" &&
            selectedCalendarView !== "listWeek"
              ? "#fff"
              : "#000",
        }}
      />
    </Dropdown>
  );
};
export default DropdownTask;
