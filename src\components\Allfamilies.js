import { DollarOutlined, FormOutlined } from "@ant-design/icons";
import { <PERSON>s, <PERSON>Handshake } from "lucide-react";
import { AiOutlineShoppingCart } from "react-icons/ai";
import { CgUserlane } from "react-icons/cg";
import { FiUsers } from "react-icons/fi";
import { GrTransaction } from "react-icons/gr";
import {
  HiMail,
  HiOutlineCalendar,
  HiOutlinePhone,
  HiOutlineTicket,
  HiOutlineUserGroup,
  HiPhoneMissedCall,
} from "react-icons/hi";
import {
  HiMiniPhoneArrowDownLeft,
  HiOutlineBuildingOffice,
  HiPhone,
  HiPhoneArrowUpRight,
} from "react-icons/hi2";
import { LuPalmtree } from "react-icons/lu";
import { MdOutlinePhoneLocked } from "react-icons/md";
import { TbPhoneCalling } from "react-icons/tb";
import TicketIconSphere from "./icons/TicketIconSphere";

export const Allfamilies = [
  {
    id: 1,
    label: "Organisation",
    prefix: "ORG_",
    last: 0,
    active: 0,
    created_at: null,
    updated_at: null,
    deleted_at: null,
    icon: <HiOutlineBuildingOffice style={{ fontSize: "18px" }} />,
  },
  {
    id: 2,
    label: "Contact",
    prefix: "CTCT_",
    last: 2,
    active: 0,
    created_at: null,
    updated_at: "2023-11-22T14:00:12.000000Z",
    deleted_at: null,
    icon: <HiOutlineUserGroup style={{ fontSize: "18px" }} />,
  },
  {
    id: 3,
    label: "Deal",
    prefix: "DEAL_",
    last: 14,
    active: 0,
    created_at: null,
    updated_at: "2023-11-23T08:13:13.000000Z",
    deleted_at: null,
    icon: <HeartHandshake size={18} />,
  },
  {
    id: 4,
    label: "User",
    prefix: "USER_",
    last: 0,
    active: 0,
    created_at: null,
    updated_at: null,
    deleted_at: null,
    icon: <FiUsers style={{ fontSize: "18px" }} />,
  },
  {
    id: 5,
    label: "Product",
    prefix: "PRDCT_",
    last: 0,
    active: 0,
    created_at: null,
    updated_at: null,
    deleted_at: null,
    icon: <AiOutlineShoppingCart style={{ fontSize: "18px" }} />,
  },
  {
    id: 6,
    label: "Helpdesk",
    prefix: "TCK_",
    last: 8,
    active: 0,
    created_at: null,
    updated_at: "2023-11-27T15:30:44.000000Z",
    deleted_at: null,
    icon: <TicketIconSphere size={18} />,
  },
  {
    id: 7,
    label: "Project",
    prefix: "PRJCT_",
    last: 2,
    active: 0,
    created_at: null,
    updated_at: "2023-11-21T17:15:18.000000Z",
    deleted_at: null,
    icon: <Blocks size={18} />,
  },
  {
    id: 8,
    label: "Booking",
    prefix: "BOOK_",
    last: 4,
    active: 0,
    created_at: null,
    updated_at: "2023-11-22T16:14:36.000000Z",
    deleted_at: null,
    icon: <LuPalmtree style={{ fontSize: "18px" }} />,
  },
  {
    id: 9,
    label: "LEADS",
    prefix: "LEAD_",
    last: 0,
    active: 1,
    created_at: null,
    updated_at: null,
    deleted_at: null,
    icon: <CgUserlane style={{ fontSize: "18px" }} />,
  },
  {
    id: 11,
    label: "Invoices",
    prefix: "Invoices",
    last: 0,
    active: 1,
    created_at: null,
    updated_at: null,
    deleted_at: null,
    icon: <DollarOutlined style={{ fontSize: 18 }} />,
  },
  {
    id: 12,
    label: "Transaction",
    prefix: "Transaction_",
    last: 0,
    active: 1,
    created_at: null,
    updated_at: null,
    deleted_at: null,
    icon: <GrTransaction style={{ fontSize: 18 }} />,
  },

  {
    id: "voip",

    icon: <HiOutlinePhone style={{ fontSize: "18px" }} />,
  },
  {
    id: "call",

    icon: <HiOutlinePhone style={{ fontSize: "18px" }} />,
  },

  {
    id: "ANSWERED",

    icon: (
      <HiMiniPhoneArrowDownLeft style={{ fontSize: "18px", color: "green" }} />
    ),
  },
  {
    id: "FAILED",

    icon: <HiPhoneMissedCall style={{ fontSize: "18px", color: "red" }} />,
  },
  {
    id: "BUSY",

    icon: (
      <TbPhoneCalling style={{ fontSize: "18px", color: "rgb(249 115 22)" }} />
    ),
  },

  {
    id: "CONGESTION",

    icon: <MdOutlinePhoneLocked style={{ fontSize: "18px" }} />,
  },
  {
    id: "NO ANSWER",
    icon: (
      <HiPhoneArrowUpRight
        style={{ fontSize: "18px", color: "rgb(156 163 175)" }}
      />
    ),
  },
  {
    id: "task",

    icon: <HiOutlineCalendar style={{ fontSize: "18px" }} />,
  },
  {
    id: "email",

    icon: <HiMail style={{ fontSize: "18px" }} />,
  },
  {
    id: "note",
    icon: <FormOutlined style={{ fontSize: "18px" }} />,
  },
];
