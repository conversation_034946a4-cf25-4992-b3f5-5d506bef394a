import React, { useEffect, useRef } from "react";
import {
  Form,
  Typography,
  Input,
  Button,
  Select,
  Badge,
  Space,
  Tag,
} from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { CaretRightOutlined, PlusCircleOutlined } from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";
import ChoiceIcons from "../pages/components/ChoiceIcons";
import ColumnColors from "./ColumnColors";
import NewTableDraggable from "./NewTableDraggable";
import { setSearch } from "../new-redux/actions/menu.actions/menu";
import { toastNotification } from "./ToastNotification";
import { allIcons } from "./Icons";
import { generateAxios } from "../services/axiosInstance";
import LabelTable from "./LabelTable";
import BottomButtonAddRow from "./BottomButtonAddRow";
import Header from "./configurationHelpDesk/Header";
import { SubmitKeyPress } from "../utils/SubmitKeyPress";
import { URL_ENV } from "index";

const CheckList = ({
  checkListId,
  setCheckListId,
  editingKey,
  setEditingKey,
}) => {
  const [form] = Form.useForm();
  const [debounceValue, setDebounceValue] = useState("");
  const [dataform, setDataForm] = useState({});
  const [data, setData] = useState([]);
  const [allData, setAllData] = useState([]);

  // const [editingKey, setEditingKey] = useState("");
  const [id, setId] = useState(null);
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modules, setModules] = useState([]);
  const isEditing = (record) => record.key === editingKey;
  const [t] = useTranslation("common");
  const [selectedItems, setSelectedItems] = useState([]);
  const [selectedRowKey, setSelectedRowKey] = useState("");
  const categories = ["Contacts", "Deals", "Companies", "call"];
  const filteredOptions = modules.filter(
    (o) => !selectedItems.includes(o.label)
  );
  const [sorter, setSorter] = useState({
    field: null,
    order: null,
  });
  const [filter, setFilter] = useState({});
  const dispatch = useDispatch();
  const { search } = useSelector((state) => state.form);

  // useEffect(() => {
  //   const timer = setTimeout(() => setDebounceValue(search), 100);

  //   return () => {
  //     clearTimeout(timer);
  //   };
  // }, [search]);
  const handleClick = (event) => {
    event.stopPropagation();
  };
  const inputRefs = useRef([]);
  const handleTableChange = (pagination, filters, sorter) => {
    setSorter({
      field: sorter.field,
      order: sorter.order,
    });
    setFilter(filters);
  };

  useEffect(() => {
    const getModules = async () => {
      const {
        data: { data },
      } = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).get("/field-modules");
      setModules(data);
    };

    getModules();
    return () => {
      dispatch(setSearch(""));
    };
  }, []);
  useEffect(() => {
    setData(
      allData.filter((object) => {
        if (filter.family_id && filter.family_id.length > 0) {
          return filter.family_id.includes(object.family_id.toString());
        } else {
          return object;
        }
      })
    );
  }, [sorter, filter, allData]);

  useEffect(() => {
    inputRefs.current.forEach((input) => {
      input?.focus();
    });
  }, [id, data.length]);

  const onFinishFailed = (values) => {
   // console.log(values);
  };
  const handleKeyPress = (event) => {
    SubmitKeyPress(event, form);
  };
  const EditableCell = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    const inputNode =
      inputType === "select" ? (
        <Select
          placeholder={t("tags.selecticon")}
          showSearch
          options={allIcons.map((el) => ({
            label: (
              <Space className="px-1" key={el.value}>
                <Typography.Text type="secondary">{el.label}</Typography.Text>
                {el.value.replaceAll("Outlined", "")}{" "}
              </Space>
            ),
            value: el.value,
          }))}
          optionFilterProp="children"
          filterOption={(input, option) =>
            (option?.value.toLowerCase() ?? "").includes(input.toLowerCase())
          }
          filterSort={(optionA, optionB) =>
            (optionA?.value ?? "")
              .toLowerCase()
              .localeCompare((optionB?.value ?? "").toLowerCase())
          }
          allowClear
        />
      ) : inputType === "tasks" ? (
        <Select
          placeholder={t("tags.selecttypeactivity")}
          options={tasks.map((el) => ({
            value: el.id,
            label: (
              <div className="space-x-2">
                {" "}
                <ChoiceIcons icon={el.icons} /> <span>{el.label}</span>{" "}
              </div>
            ),
          }))}
          allowClear
        />
      ) : inputType === "categories" ? (
        <Select
          placeholder={t("tags.selectcategories")}
          // placeholder="Inserted are removed"
          options={
            filteredOptions.length > 0 &&
            filteredOptions.map((item) => ({
              value: item.id,
              label: item.label,
            }))
          }
          allowClear
        />
      ) : (
        <Input
          // ref={(el) => (inputRefs.current[0] = el)}
          autoFocus
          onKeyPress={handleKeyPress}
          placeholder={t("activities.name")}
        />
      );
    return (
      <td {...restProps}>
        {editing ? (
          <Form.Item
            name={dataIndex.toLowerCase()}
            style={{
              margin: 0,
            }}
            rules={[
              {
                required:
                  dataIndex === "tasktype_id" || dataIndex === "label"
                    ? true
                    : false,
                message: `${t(`checklist.${dataIndex}`)} ${t(
                  "table.header.isrequired"
                )}`,
              },
            ]}
          >
            {inputNode}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };
  const edit = (record) => {
    if (record) {
      form.setFieldsValue({
        label: record.label,
        family_id: record.family_id,
        tasktype_id: record.tasktype_id,
      });
      setDataForm({
        label: record.label,
        family_id: record.family_id,
        tasktype_id: record.tasktype_id,
      });
      setId(record.id);
    } else {
      form.setFieldsValue({
        label: "",
        family_id: null,
        tasktype_id: null,
      });
    }
    setEditingKey(record.key);
  };
  const cancel = (record) => {
    setEditingKey("");
    setId(null);
    if (!record.id) {
      setData(data.filter((item) => item.key !== record.key));
    }
  };
  const save = async (key) => {
    setLoading(true);
    if (id) {
      try {
        const row = await form.validateFields();
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post(`/checklists/update/${id}`, {
          ...row,
          family_id: row.family_id ? row.family_id : "",
        });
        setEditingKey("");
        setData((previous) =>
          previous.map((el) =>
            el.id === res.data.data.id
              ? {
                  ...res.data.data,
                  key: res.data.data.id,
                }
              : el
          )
        );
        form.setFieldsValue({
          label: "",
          family_id: null,
          tasktype_id: null,
        });
        setDataForm({});
        setLoading(false);
        toastNotification("success", row.label + t("toasts.edit"), "topRight");
      } catch (errInfo) {
        setLoading(false);
        console.log(errInfo);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    } else {
      try {
        const row = await form.validateFields();

        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post("/checklists", {
          ...row,
        });
        setEditingKey("");
        setData((prev) => [
          ...prev.filter((el) => el.id),
          { ...res.data.data, id: res.data.data.id, key: res.data.data.id },
        ]);

        form.setFieldsValue({
          label: "",
          family_id: null,
          tasktype_id: null,
        });
        setDataForm({});

        setLoading(false);
        toastNotification(
          "success",
          row.label + t("toasts.created"),
          "topRight"
        );
      } catch (errInfo) {
        setLoading(false);
        console.log(errInfo);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    }
  };
  // useEffect(() => {
  //   const getTasks = async () => {
  //     const tasks = await axiosInstance.get(`/task-types`);
  //     setTasks(tasks.data.data.tasks_type);
  //     setLoading(false);
  //   };
  //   getTasks();
  // }, []);
  useEffect(() => {
    const getData = async () => {
      setLoading(true);
      try {
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get(`/checklists`);

        // console.log(tags.data.data);

        if (res.status === 200) {
          const tasks = await generateAxios(
            URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
          ).get(`/task-types`);
          setData(
            res.data.data.map((el, i) => ({
              ...el,
              key: el.id,
              rank: i + 1,
            }))
          );
          setTasks(tasks?.data?.data?.tasks_type);
          setCheckListId(res?.data?.data[0]?.id);
          setSelectedRowKey(res?.data?.data[0]?.id);
          setLoading(false);
        }
      } catch (err) {
        console.log(err);
        setLoading(false);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    };
    const getActivities = async () => {
      setLoading(true);
      setEditingKey("");
      try {
        const {
          data: { data },
        } = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post(`/qualifications-search?label=${debounceValue}`);
        setData(
          data.original.results.map((el, i) => ({
            ...el,
            key: el.id,
          }))
        );
        setLoading(false);
      } catch (e) {
        console.log(e);

        setLoading(false);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    };
    if (!debounceValue) getData();
    else getActivities();
  }, [debounceValue]);

  const columns = [
    {
      title: t("activities.name"),
      dataIndex: "label",
      key: "label",
      editable: true,
      sorter: (a, b) => a.label.localeCompare(b.label),
      render: (_, record) => {
        return (
          <LabelTable record={record} editingKey={editingKey} edit={edit} />
        );
      },
    },
    {
      title: t("tags.typeTask"),
      dataIndex: "tasktype_id",
      key: "tasktype_id",
      editable: true,
      sorter: (a, b) => a.typetask?.label.localeCompare(b.typetask?.label),
      render: (_, record) => (
        // typetask?.icons ? (
        //   <Space wrap style={{ color: typetask?.color }}>
        //     <ChoiceIcons icon={typetask?.icons} />

        //     <div dangerouslySetInnerHTML={{ __html: typetask.label }} />
        //   </Space>
        // ) : (
        //   ""
        // ),

        <div
          className="flex cursor-pointer items-center space-x-2 hover:underline"
          style={{
            color: tasks.find((el) => el.id === record.tasktype_id)?.color,
          }}
          onClick={(e) => {
            edit(record);
            handleClick(e);
          }}
        >
          {" "}
          <ChoiceIcons
            icon={tasks.find((el) => el.id === record.tasktype_id)?.icons}
          />{" "}
          <span>{tasks.find((el) => el.id === record.tasktype_id)?.label}</span>
        </div>
      ),
    },
    {
      title: "Module",
      dataIndex: "family_id",
      key: "family_id",
      editable: true,
      width: "200px",
      filters: modules.map((el) => ({
        text: el?.label,
        value: el?.id.toString(),
      })),

      onFilter: (value, record) => record.family_id == value,

      render: (_, { family_id, id }) => (
        <div className="flex items-center justify-between">
          <span>{modules.find((el) => el.id === family_id)?.label}</span>{" "}
          <span>
            {id == checkListId ? <CaretRightOutlined className="ml-1" /> : ""}
          </span>
        </div>
      ),
    },
  ];
  const handleAdd = () => {
    const ids = data.map((object) => {
      return object.id;
    });
    setId(null);

    const newData = {
      key: Math.max(...ids) + 1,
      label: "",
      family_id: null,
      tasktype_id: null,
      disabled: true,
    };

    setData([...data, newData]);
    form.setFieldsValue({
      label: "",
      family_id: null,
      tasktype_id: null,
    });
    setEditingKey(Math.max(...ids) + 1);
  };
  const onSelectChange = (selectedRowKeys) => {
    setSelectedRowKey(selectedRowKeys[0]);
    if (selectedRowKeys !== checkListId) {
      // setEditingKeyStage("");
      setCheckListId(selectedRowKeys[0]);
    }
  };

  const rowSelection = {
    type: "radio",

    selectedRowKeys: [selectedRowKey],

    onChange: onSelectChange,
    getCheckboxProps: (record) => ({
      style: {
        display: !record.id ? "none" : "flex",
        paddingTop: editingKey ? "6px" : 0,
      }, // Masque la case à cocher

      // Column configuration not to be checked
    }),
  };
  const rowClassName = (record) => {
    if (record["key"] === editingKey) {
      return "editingRow selected-row";
    }
    if (selectedRowKey == record.key) {
      return "selected-row";
    }

    return "";
  };
  const onRow = (record, rowIndex) => {
    return {
      onClick: () => {
        if (record.id) {
          setSelectedRowKey(record.key);
          onSelectChange([record.key]);
        }
      },
    };
  };
  const filteredData = data.filter((item) => {
    return (
      item.label?.toLowerCase().includes(search.toLowerCase()) ||
      tasks
        .find((el) => el.id === item.tasktype_id)
        ?.label.toLowerCase()
        .includes(search.toLowerCase()) ||
      modules
        .find((el) => el.id === item.family_id)
        ?.label.toLowerCase()
        .includes(search.toLowerCase())
    );
  });
  console.log(tasks);
  return (
    <Space direction="vertical" style={{ width: "100%" }}>
      <div className="ml-2 mr-2 mt-4	rounded px-2.5 py-0.5 text-base font-medium text-[#2253d5] dark:bg-blue-200 dark:text-blue-800">
        {t(`checklist.listChecklist`)}
      </div>
      <Header
        editingKey={editingKey}
        handleAdd={handleAdd}
        btnText={""}
        disabled={loading ? true : editingKey ? true : search ? true : false}
        shape={"circle"}
      />
      {/* <div className="pt-4">
        <Header
          active={"4"}
          editingKey={editingKey}
          handleAdd={handleAdd}
          btnText="Add a checkList"
          disabled={loading ? true : editingKey ? true : false}
        />
      </div> */}

      <NewTableDraggable
        columns={columns}
        setLoading={setLoading}
        isEditing={isEditing}
        data={filteredData}
        setData={setData}
        loading={loading}
        save={save}
        edit={edit}
        EditableCell={EditableCell}
        onFinishFailed={onFinishFailed}
        cancel={cancel}
        form={form}
        apiRank="rank-checklists"
        editingKey={editingKey}
        api="checklists"
        onRow={onRow}
        rowSelection={rowSelection}
        rowClassName={rowClassName}
        // handleTableChange={handleTableChange}
      />

      <BottomButtonAddRow
        editingKey={editingKey}
        data={data}
        text={"Add a checkList"}
        handleAdd={handleAdd}
        loading={loading}
        search={(filter.family_id && filter.family_id.length > 0) || ""}
        pagination={false}
      />
    </Space>
  );
};
export default CheckList;
