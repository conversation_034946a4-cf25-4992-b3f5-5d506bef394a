/* eslint-disable jsx-a11y/no-noninteractive-tabindex */
/* eslint-disable jsx-a11y/label-has-associated-control */
import React, { useEffect, useRef, useState } from "react";
import { NodeViewContent, NodeViewWrapper, NodeViewProps } from "@tiptap/react";
import { Editor } from "@tiptap/core";
import Tippy from "@tippyjs/react";
import "./Styles.css";

import { ChevronDown } from "lucide-react";
import { Button } from "antd";

const cellButtonsConfig = [
  {
    name: "Add row above",
    action: (editor) => editor.chain().focus().addRowBefore().run(),
    iconClass: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M22 14a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v7h2v-2h4v2h2v-2h4v2h2v-2h4v2h2zM4 14h4v3H4zm6 0h4v3h-4zm10 0v3h-4v-3zm-9-4h2V7h3V5h-3V2h-2v3H8v2h3z"
        ></path>
      </svg>
    ),
  },
  {
    name: "Add row below",
    action: (editor) => editor.chain().focus().addRowAfter().run(),
    iconClass: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M22 10a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V3h2v2h4V3h2v2h4V3h2v2h4V3h2zM4 10h4V7H4zm6 0h4V7h-4zm10 0V7h-4v3zm-9 4h2v3h3v2h-3v3h-2v-3H8v-2h3z"
        ></path>
      </svg>
    ),
  },
  {
    name: "Add column before",
    action: (editor) => editor.chain().focus().addColumnBefore().run(),
    iconClass: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M13 2a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h9V2zm7 8v4h-7v-4zm0 6v4h-7v-4zm0-12v4h-7V4zM9 11H6V8H4v3H1v2h3v3h2v-3h3z"
        ></path>
      </svg>
    ),
  },
  {
    name: "Add column after",
    action: (editor) => editor.chain().focus().addColumnAfter().run(),
    iconClass: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M11 2a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H2V2zm-7 8v4h7v-4zm0 6v4h7v-4zM4 4v4h7V4zm11 7h3V8h2v3h3v2h-3v3h-2v-3h-3z"
        ></path>
      </svg>
    ),
  },
  {
    name: "Remove row",
    action: (editor) => editor.chain().focus().deleteRow().run(),
    iconClass: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M9.41 13L12 15.59L14.59 13L16 14.41L13.41 17L16 19.59L14.59 21L12 18.41L9.41 21L8 19.59L10.59 17L8 14.41zM22 9a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2zM4 9h4V6H4zm6 0h4V6h-4zm6 0h4V6h-4z"
        ></path>
      </svg>
    ),
  },
  {
    name: "Remove col",
    action: (editor) => editor.chain().focus().deleteColumn().run(),
    iconClass: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M9.41 13L12 15.59L14.59 13L16 14.41L13.41 17L16 19.59L14.59 21L12 18.41L9.41 21L8 19.59L10.59 17L8 14.41zM22 9a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2zM4 9h4V6H4zm6 0h4V6h-4zm6 0h4V6h-4z"
        ></path>
      </svg>
    ),
  },
  {
    name: "Toggle header row",
    action: (editor) => editor.chain().focus().toggleHeaderRow().run(),
    iconClass: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M22 14a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-4a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2zM4 14h4v-4H4zm6 0h4v-4h-4zm6 0h4v-4h-4z"
        ></path>
      </svg>
    ),
  },
  {
    name: "Toggle header column",
    action: (editor) => editor.chain().focus().toggleHeaderColumn().run(),
    iconClass: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M8 2h8a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H8a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2m0 8v4h8v-4zm0 6v4h8v-4zM8 4v4h8V4z"
        ></path>
      </svg>
    ),
  },
  {
    name: "Toggle header cell",
    action: (editor) => editor.chain().focus().toggleHeaderCell().run(),
    iconClass: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M21 19a1 1 0 0 1-1 1h-1v-2h2zm-6 1v-2h2v2zm-4 0v-2h2v2zm-4 0v-2h2v2zm-3 0a1 1 0 0 1-1-1v-1h2v2zM19 4H5a2 2 0 0 0-2 2v2h18V6c0-1.11-.89-2-2-2M5 14H3v2h2zm0-4H3v2h2zm16 0h-2v2h2zm0 4h-2v2h2zm-10 2v-2h2v2zm0-4v-2h2v2z"
        ></path>
      </svg>
    ),
  },
  {
    name: "Remove table",
    action: (editor) => editor.chain().focus().deleteTable().run(),
    iconClass: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M9.41 13L12 15.59L14.59 13L16 14.41L13.41 17L16 19.59L14.59 21L12 18.41L9.41 21L8 19.59L10.59 17L8 14.41zM22 9a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2zM4 9h4V6H4zm6 0h4V6h-4zm6 0h4V6h-4z"
        ></path>
      </svg>
    ),
  },
];

const TableCellNodeView = ({ node, getPos, selected, editor }) => {
  const [isCurrentCellActive, setIsCurrentCellActive] = useState(false);

  const tableCellOptionsButtonRef = useRef(null);

  const calculateActiveSateOfCurrentCell = () => {
    const { from, to } = editor.state.selection;

    const nodeFrom = getPos();
    const nodeTo = nodeFrom + node.nodeSize;

    setIsCurrentCellActive(nodeFrom <= from && to <= nodeTo);
  };

  useEffect(() => {
    editor.on("selectionUpdate", calculateActiveSateOfCurrentCell);

    setTimeout(calculateActiveSateOfCurrentCell, 100);

    return () => {
      editor.off("selectionUpdate", calculateActiveSateOfCurrentCell);
    };
  });

  const gimmeDropdownStyles = () => {
    let top = tableCellOptionsButtonRef.current?.clientTop;
    if (top) top += 5;

    let left = tableCellOptionsButtonRef.current?.clientLeft;
    if (left) left += 5;

    return {
      top: `${top}px`,
      left: `${left}px`,
    };
  };

  return (
    <NodeViewWrapper>
      <NodeViewContent as="span" className="flex whitespace-nowrap" />

      {(isCurrentCellActive || selected) && (
        <Tippy
          appendTo={document.body}
          trigger="click"
          interactive
          animation="shift-toward-subtle"
          placement="right-start"
          content={
            <div
              tabIndex={0}
              className="fixed z-[1000] h-56 w-56 space-y-1 overflow-auto rounded-md bg-white p-2 shadow"
              // style={gimmeDropdownStyles()}
            >
              {cellButtonsConfig.map((btn) => (
                <Button
                  className="flex w-full items-center justify-start space-x-2"
                  type="text"
                  onClick={() => btn.action(editor)}
                  key={btn.name}
                >
                  <span>{btn.iconClass}</span>
                  <span className="font-semibold">{btn.name}</span>
                </Button>
              ))}
            </div>
          }
        >
          <label
            tabIndex={0}
            // className="trigger-button"
            contentEditable={false}
          >
            <Button size="small" icon={<ChevronDown size={20} />} />
          </label>
        </Tippy>
      )}
    </NodeViewWrapper>
  );
};

export default TableCellNodeView;
