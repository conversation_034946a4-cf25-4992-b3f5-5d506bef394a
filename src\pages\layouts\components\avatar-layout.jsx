import { Badge, Dropdown } from "antd";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { RiWifiOffLine } from "react-icons/ri";
import { HiOutlineStatusOnline } from "react-icons/hi";
import { AiOutlineClockCircle, AiOutlineMinusCircle } from "react-icons/ai";
import {
  HiDevicePhoneMobile,
  HiOutlineArrowLeftOnRectangle,
  HiOutlineDocumentText,
  HiOutlineLifebuoy,
  HiOutlineUserCircle,
} from "react-icons/hi2";

import { generateAxios } from "../../../services/axiosInstance";
import { toastNotification } from "../../../components/ToastNotification";
import { UPDATE_CURRENT_USER_STATUS_PRESENCE } from "../../../new-redux/constants";

import { logoutOut } from "../../../new-redux/actions/user.actions/getUser";
import { AvatarChat } from "../../../components/Chat";
import { getName } from "../chat/utils/ConversationUtils";
import { URL_ENV } from "index";
import { setOpenTaskDrawer } from "new-redux/actions/tasks.actions/handleTaskDrawer";
import { setOpenTaskRoomDrawer } from "new-redux/actions/tasks.actions/handleTaskRoomDrawer";
import { setOpenFieldDrawer } from "new-redux/actions/fields.actions/fieldDrawer";
import { Milestone } from "lucide-react";
import { Refs_IDs } from "components/tour/tourConfig";
import { updateToursConfig } from "components/tour/TourWrapper";
import { setOpenTourInProfile } from "new-redux/actions/menu.actions/menu";

function AvatarLayout() {
  const redirectActivityId = localStorage.getItem("redirect-activity-id");
  const currentUser = useSelector((state) => state.chat.currentUser);
  const { user } = useSelector((state) => state.user);
  // const { isOpenTour } = useSelector((state) => state.menu);
  const toursAccess = useSelector(
    (state) => state.user.user?.toursAccess || {}
  );

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { t } = useTranslation("common");
  const { pathname } = useLocation();

  // const displayTour = async () => {
  //   // const res = await axios.post(
  //   //   `${
  //   //     process.env.REACT_APP_URL_TENANT + process.env.REACT_APP_SUFFIX_API
  //   //   }tours/update-by-selector${
  //   //     pathname.includes("v2") ? "/viewSphere" : pathname
  //   //   }`,
  //   //   { status: 1 }
  //   // );
  //   // if (res.data.success)
  //   dispatch(setOpenTourInProfile(true));
  // };
  const items = [
    {
      key: "0",
      label: (
        <div className="profile space-y-1">
          {!!URL_ENV?.REACT_APP_TENANT_ALIAS && (
            <p className=" text-slate-500">
              {/* <small className="font-semibold">
                {t("profilemenu.Tenancy")}
              </small> */}
              {URL_ENV?.REACT_APP_TENANT_ALIAS}
            </p>
          )}
          <p>{user?.email}</p>
        </div>
      ),
    },
    {
      type: "divider",
    },
    {
      className: "item-dropDown",
      key: "1",
      label:
        t("profilemenu.status") +
        " (" +
        (currentUser?.online === "offline"
          ? t("profilemenu.offline")
          : currentUser?.online === "away"
          ? t("profilemenu.away")
          : currentUser?.online === "busy"
          ? t("profilemenu.busy")
          : t("profilemenu.online")) +
        ")",

      icon:
        currentUser?.online === "offline" ? (
          <RiWifiOffLine
            style={{
              fontSize: "21px",
            }}
            className=" text-[#a6a6a6]"
          />
        ) : currentUser?.online === "away" ? (
          <AiOutlineClockCircle
            style={{
              fontSize: "21px",
            }}
            className="  text-orange-400"
          />
        ) : currentUser?.online === "busy" ? (
          <AiOutlineMinusCircle
            style={{
              fontSize: "21px",
            }}
            className=" text-red-700"
          />
        ) : (
          <HiOutlineStatusOnline
            style={{
              fontSize: "21px",
            }}
            className=" text-green-700"
          />
        ),
      children: [
        {
          key: "1-1",
          label: (
            <p className="profile removeCSS ">{t("profilemenu.online")}</p>
          ),

          onClick: () => setUserStatus("online", true),
          icon: <HiOutlineStatusOnline className="h-5 w-5 text-green-700" />,
        },
        {
          key: "1-2",
          label: <p className="profile removeCSS">{t("profilemenu.away")}</p>,
          onClick: () => setUserStatus("away", true),

          icon: <AiOutlineClockCircle className="h-5 w-5 text-orange-400" />,
        },
        {
          key: "1-3",
          label: <p className="profile removeCSS ">{t("profilemenu.busy")}</p>,
          icon: <AiOutlineMinusCircle className="h-5 w-5 text-red-700" />,
          onClick: () => setUserStatus("busy", true),
        },
      ],
    },
    {
      key: "2",
      label: <p className="profile removeCSS">{t("profilemenu.profile")}</p>,
      onClick: () => {
        navigate("/profile/general");
        dispatch(setOpenTaskDrawer(false));
        dispatch(setOpenFieldDrawer(false));
        dispatch(setOpenTaskRoomDrawer(false));
        if (redirectActivityId) {
          localStorage.removeItem("redirect-activity-id");
        }
      },
      icon: <HiOutlineUserCircle className="h-5 w-5 text-slate-400" />,
    },
    {
      key: "appMobile",
      label: <p className="profile removeCSS">{t("profilemenu.appMobile")}</p>,
      onClick: () => {
        navigate("/profile/appMobile");
      },
      icon: <HiDevicePhoneMobile className="h-5 w-5 text-slate-400" />,
    },
    {
      key: "3",
      label: t("profilemenu.support"),
      icon: <HiOutlineLifebuoy className="h-5 w-5 text-slate-400" />,

      disabled: true,
    },

    {
      key: "tour",
      // disbaled: Boolean(toursAccess?.[pathname.replace("/", "")]),
      label: t("menu2.tour"),
      onClick: () => {
        // displayTour();
        dispatch(setOpenTourInProfile(true));
        dispatch(
          updateToursConfig("open", toursAccess, pathname.replace("/", ""), t)
        );
      },

      icon: <Milestone size={16} color="#94a3b8" />,
    },

    {
      key: "4",
      label: (
        <a
          className="removeCSS"
          href={URL_ENV?.REACT_APP_WIKIURL + "/comunikkit"}
          // href="https://docs.comunikcrm.com/comunikunified"
          target="_blank"
          rel="noopener noreferrer"
        >
          {t("profilemenu.documentation")}
        </a>
      ),

      icon: <HiOutlineDocumentText className="h-5 w-5 text-slate-400" />,
    },
    {
      type: "divider",
    },
    {
      key: "5",
      label: <p className="profile">{t("profilemenu.logout")}</p>,
      onClick: () => dispatch(logoutOut(navigate)),
      icon: (
        <HiOutlineArrowLeftOnRectangle className="h-5 w-5 text-slate-400" />
      ),
    },
    // {
    //   key: "6",
    //   label: <p className="profile">Logout rmc</p>,
    //   onClick: () => logoutRmc(),
    //   icon: (
    //     <HiOutlineArrowLeftOnRectangle className="h-5 w-5 text-slate-400" />
    //   ),
    // },
  ];

  const setUserStatus = async (status, fetchAPI) => {
    let statusCode = null;
    const oldOnlineState = currentUser?.online;
    switch (status) {
      case "online":
        statusCode = 0;

        break;
      case "away":
        statusCode = 1;
        break;
      case "busy":
        statusCode = 2;
        break;
      default:
        break;
    }

    let response = null;
    if (fetchAPI) {
      try {
        response = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).put(
          `statues`,
          {
            status_user: statusCode,
          },
          {
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
            },
          }
        );
      } catch (error) {
        toastNotification("error", t("toasts.errorFetchApi"), "topRight");
        dispatch({
          type: UPDATE_CURRENT_USER_STATUS_PRESENCE,
          payload: oldOnlineState,
        });
      }
    }
    dispatch({
      type: UPDATE_CURRENT_USER_STATUS_PRESENCE,
      payload: status,
    });

    if (fetchAPI && response?.status === 200)
      toastNotification(
        "success",
        t("chat.message_system.statusChanged"),
        "topRight"
      );
    else toastNotification("success", t("toasts.statusChanged"), "topRight");
  };

  //   const onClickStatus = ({ key }) => {
  //     if (key === "5-4") {
  //       setOpenUnavailabilityModal(true);
  //     }
  //   };

  //   const handleOkUnavailability = async () => {
  //     setOpenUnavailabilityModal(false);
  //     const res = await generateAxios(URL_ENV?.REACT_APP_BASE_URL+process.env.REACT_APP_SUFFIX_API).get(
  //       `/indisponibilite`
  //     );
  //     if (res.data.success) {
  //       const custom = res.data?.data.find((e) =>
  //         moment_timezone().isBetween(
  //           moment_timezone(e.start_date),
  //           moment_timezone(e.end_date)
  //         )
  //       );
  //       if (custom) setUserStatus("away", false);
  //     }
  //   };
  //   const handleCancelUnavailability = () => {
  //     setOpenUnavailabilityModal(false);
  //   };
  return (
    <div className="flex shrink-0  items-center justify-center py-2.5">
      <Dropdown
        menu={{
          items,
          //  onClick: onClickStatus,
          selectedKeys: [
            `1-${
              currentUser?.online === "online"
                ? "1"
                : currentUser?.online === "away"
                ? "2"
                : "3"
            }`,
          ],
        }}
        overlayClassName="profileDropdown"
        className=""
        trigger={["click"]}
        placement="topLeft"
        arrow={{
          pointAtCenter: true,
        }}
      >
        <Badge
          offset={[-7, 4]}
          dot
          color={
            currentUser?.online === "offline"
              ? "#a6a6a6"
              : currentUser?.online === "away"
              ? "orange"
              : currentUser?.online === "busy"
              ? "red"
              : "green"
          }
        >
          <div className="cursor-pointer" ref={Refs_IDs.iconProfile}>
            <AvatarChat
              size={40}
              height={10}
              width={10}
              hasImage={user?.avatar}
              url={
                URL_ENV?.REACT_APP_BASE_URL +
                URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                user?.avatar
              }
              type="user"
              name={getName(user?.label, "avatar")}
            />
          </div>
        </Badge>
      </Dropdown>

      {/* <Modal
        title={t("menu2.unavailability")}
        open={openUnavailabilityModal}
        onOk={handleOkUnavailability}
        onCancel={handleCancelUnavailability}
        width={700}>
        <Unavailability />
      </Modal> */}
    </div>
  );
}

export default AvatarLayout;
