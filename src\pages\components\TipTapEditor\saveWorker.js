const { default: MainService } = require("services/main.service");

self.onmessage = async function (e) {
  const { type, payload } = e.data;

  switch (type) {
    case "SAVE_NOTE":
      saveNote(payload.noteId, payload.data);
      break;

    case "FETCH_UPDATED_NOTES":
      fetchAndSendUpdatedNotes();
      break;

    default:
      break;
  }
};

async function saveNote(noteId, data) {
  try {
    const updatedNote = await MainService.updateNote360(noteId, data);
    const updatedNoteData = updatedNote.data.data;
    self.postMessage({ type: "SAVE_NOTE_SUCCESS", payload: updatedNoteData });
  } catch (error) {
    self.postMessage({
      type: "SAVE_NOTE_ERROR",
      payload: { error: error.message },
    });
  }
}

async function fetchAndSendUpdatedNotes() {
  try {
    const updatedNotes = await MainService.fetchNotes();
    self.postMessage({ type: "UPDATE_SELF_NOTES", payload: { updatedNotes } });
  } catch (error) {
    console.error("Failed to fetch updated notes:", error);
  }
}
