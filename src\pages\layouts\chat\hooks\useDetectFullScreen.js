import { useEffect } from "react";

function useDetectFullScreen({ callback }) {
  useEffect(() => {
    const fullScreenEvents = [
      "fullscreenchange",
      "webkitfullscreenchange",
      "mozfullscreenchange",
      "msfullscreenchange",
    ];

    const eventListener = () => {
      const isFullScreen =
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement;

      callback(isFullScreen);
    };

    for (const eventName of fullScreenEvents) {
      if (document.addEventListener) {
        document.addEventListener(eventName, eventListener, false);
      }
    }

    return () => {
      for (const eventName of fullScreenEvents) {
        if (document.removeEventListener) {
          document.removeEventListener(eventName, eventListener, false);
        }
      }
    };
  }, [callback]);
}

export default useDetectFullScreen;
