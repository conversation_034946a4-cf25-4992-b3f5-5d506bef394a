import { Space, Typography } from "antd";
import { allIcons } from "./Icons";

const GroupIcons = () => {
  return (
    <Select
      placeholder={title}
      showSearch
      style={{
        width: 250,
      }}
      options={allIcons.map((el) => ({
        label: (
          <Space className="px-1">
            <Typography.Text type="secondary">{el.label}</Typography.Text>
            {el.value.replaceAll("Outlined", "")}{" "}
          </Space>
        ),
        value: el.value,
      }))}
      optionFilterProp="children"
      filterOption={(input, option) =>
        (option?.value.toLowerCase() ?? "").includes(input.toLowerCase())
      }
      filterSort={(optionA, optionB) =>
        (optionA?.value ?? "")
          .toLowerCase()
          .localeCompare((optionB?.value ?? "").toLowerCase())
      }
      allowClear
    />
  );
};

export default GroupIcons;
