import { Tooltip } from "antd";
import { useTranslation } from "react-i18next";
import { SisternodeOutlined } from "@ant-design/icons";

const SubtasksIndicator = ({ subtasksNumber, position = "absolute" }) => {
  const [t] = useTranslation("common");
  return (
    <Tooltip title={`${subtasksNumber} ${t("tasks.subtasksTitle")}`}>
      <SisternodeOutlined
        style={{
          fontSize: position === "absolute" ? "10px" : undefined,
          position,
          left: position === "absolute" ? "-16px" : undefined,
          color:
            position === "absolute" ? "oklch(0.448 0.119 151.328)" : undefined,
        }}
      />
    </Tooltip>
  );
};

export default SubtasksIndicator;
