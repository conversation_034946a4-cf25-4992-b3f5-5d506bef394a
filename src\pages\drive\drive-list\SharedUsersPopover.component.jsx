import React, { useState } from "react";
import { Popover, Spin, Tag } from "antd";
import { UserOutlined, ShareAltOutlined } from "@ant-design/icons";
import { useQuery } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { getSharedDriveItem } from "../../../services/main.service";
import DisplayAvatar from "../../../pages/voip/components/DisplayAvatar";
import { URL_ENV } from "../../../index";

const SharedUsersPopover = ({ itemId, itemName, children }) => {
  const [t] = useTranslation("common");
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);

  const { data: sharedData, isLoading, isError, refetch } = useQuery({
    queryKey: ["shared-drive-item", itemId],
    queryFn: () => getSharedDriveItem(itemId),
    enabled: isPopoverOpen, 
    refetchOnWindowFocus: false,
    staleTime: 5 * 60 * 1000, 
  });

  const sharedUsers = sharedData?.data || [];
  const hasSharedUsers = sharedUsers.length > 0;

  const renderPopoverContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center p-6 min-h-[80px]">
          <Spin size="small" />
          <span className="ml-3 text-sm text-gray-600">{t("drive.loading")}</span>
        </div>
      );
    }

    if (isError) {
      return (
        <div className="p-4 text-red-500 text-sm flex items-center gap-2">
          <UserOutlined className="text-red-400" />
          <span>{t("drive.failedToLoadSharedUsers")}</span>
        </div>
      );
    }

    if (!hasSharedUsers) {
      return (
        <div className="p-4 text-gray-500 flex items-center gap-2 text-sm">
          <UserOutlined className="text-gray-400" />
          <span>{t("drive.notShared")}</span>
        </div>
      );
    }

    return (
      <div className="w-80 max-w-sm">
        <div className="mb-3 font-medium text-gray-700 flex items-center gap-2 text-sm">
        
          <span>{t("drive.sharedWith")}</span>
        </div>
        <div 
          className="space-y-2 max-h-48 overflow-y-auto pr-1"
          style={{
            scrollbarWidth: 'thin',
            scrollbarColor: '#d1d5db transparent'
          }}
        >
          {sharedUsers.map((shareItem) => (
            <div key={shareItem.id} className="flex items-center justify-between gap-2 p-2 hover:bg-gray-50 rounded-md transition-colors">
              <div className="flex items-center gap-2 flex-1 min-w-0">
                <DisplayAvatar
                  name={shareItem.user.label}
                  size={24}
                  urlImg={
                    shareItem.user.avatar &&
                    URL_ENV?.REACT_APP_BASE_URL +
                      URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                      shareItem.user.avatar
                  }
                />
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium truncate text-gray-900">
                    {shareItem.user.label}
                  </div>
                  {shareItem.user.email && (
                    <div className="text-xs text-gray-500 truncate">
                      {shareItem.user.email}
                    </div>
                  )}
                </div>
              </div>
              <Tag 
                size="small" 
                color={shareItem.permission_level === 'editor' ? 'blue' : 'green'}
                className="text-xs px-2 py-1 rounded-full border-0"
              >
                {shareItem.permission_level}
              </Tag>
            </div>
          ))}
        </div>
    
      </div>
    );
  };

  return (
    <Popover
      content={renderPopoverContent()}
      title={
        <div className="flex items-center gap-2 text-gray-800 pb-3 mb-1 relative">
          <ShareAltOutlined className="text-blue-500" />
          <span className="truncate max-w-85 font-medium">{itemName}</span>
    
          <div className="absolute bottom-0 left-0 right-0 h-px bg-gray-300"></div>
        </div>
      }
      trigger="hover"
      placement="top"
      mouseEnterDelay={0.5}
      mouseLeaveDelay={0.1}
      onOpenChange={(visible) => {
        setIsPopoverOpen(visible);
        if (visible) {
          refetch();
        }
      }}
      overlayStyle={{ 
        width: 'auto',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
      }}
      overlayInnerStyle={{
        padding: '12px',
        borderRadius: '8px'
      }}
    >
      {children}
    </Popover>
  );
};

export default SharedUsersPopover; 