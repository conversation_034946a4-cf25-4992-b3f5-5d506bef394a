import { SET_VOICE_SUCCESS, SET_VOICE_ERROR } from "../../constants";
import MainService from "../../../services/main.service";

export const setVoice = (poste,id) => async (dispatch) => {
  try {
    // dispatch({ type: IS_LOADING_VOICE });
    const response = await MainService.consultVoiceApiIPBX(poste,id);
    dispatch({
      type: SET_VOICE_SUCCESS,
      payload: response?.data,
    });
  } catch (error) {
    dispatch({
      type: SET_VOICE_ERROR,
      payload: error,
    });
  }
};
