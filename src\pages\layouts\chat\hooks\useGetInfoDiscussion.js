import { useCallback } from "react";
import { useSelector } from "react-redux";
import MainService from "services/main.service";
import { toastNotification } from "components/ToastNotification";
import { useTranslation } from "react-i18next";
import { useQuery } from "@tanstack/react-query";
import { STALE_TIME_CACHE } from "..";

function useGetInfoDiscussion() {
  const { t } = useTranslation("common");
  const selectedConversation = useSelector(
    (state) => state.ChatRealTime.selectedConversation
  );
  const { openDrawer } = useSelector((state) => state.chat);
  const getInfoDiscussion = useCallback(
    (id, signal) => {
      const promise = new Promise(async (resolve, reject) => {
        try {
          if (!id) return; //reject(new Error("no_id"));
          const formData = new FormData();
          formData.append("conversation_id", id);
          const response = await MainService.getDiscussionInfo(
            formData,
            selectedConversation?.type,
            signal
          );
          return resolve(response?.data);
        } catch (error) {
          if (error.name === "CanceledError") return;
          toastNotification("error", t("toasts.errorFetchApi"), "topRight");

          reject(error);
        }
      });
      return promise;
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [selectedConversation?.type, selectedConversation?.conversationId, t]
  );
  const { data, isFetched, status, fetchStatus } = useQuery({
    queryFn: async ({ signal }) => {
      if (selectedConversation?.id)
        return await getInfoDiscussion(
          selectedConversation?.source === "chat"
            ? selectedConversation?.conversationId
            : selectedConversation?.id,
          signal
        );
    },
    retry: 0,
    queryKey: [
      "INFO_CANAL",
      selectedConversation?.id,
      selectedConversation?.type,
    ],
    /* handle when ever the api fetch : 
    -if source not chat =>true
    -if source chat must have conversationId and other condition  
    - if the room info is opened and type is room and external => true
    */
    enabled:
      selectedConversation?.conversationId &&
      selectedConversation?.source === "chat" &&
      ((selectedConversation?.id && !selectedConversation?.external) ||
        (selectedConversation?.id &&
          selectedConversation?.external &&
          selectedConversation?.type === "room" &&
          openDrawer?.type === "info"))
        ? true
        : false,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    staleTime: STALE_TIME_CACHE,
  });
  return { data, isFetched, status, fetchStatus };
}

export default useGetInfoDiscussion;
