import { useState, useEffect } from "react";
import { Alert, Form } from "antd";
import { useParams } from "react-router-dom";

import { formatJsonPayload, formatDataObject } from "./utils/utilsFunctions";
import MainService from "services/main.service";
import "./cart.css";
import EditableTableForm from "./EditableTableForm";

const Cart = () => {
  const [form] = Form.useForm();
  const params = useParams();

  const [productsList, setProductsList] = useState([]);
  const [discountsList, setDiscountsList] = useState([]);
  const [currenciesList, setCurrenciesList] = useState([]);
  const [selectedCurrency, setSelectedCurrency] = useState(null);
  const [cartDetails, setCartDetails] = useState({});
  const [cartTotal, setCartTotal] = useState(0);
  const [newProductName, setNewProductName] = useState("");
  const [loadCartDetails, setLoadCartDetails] = useState(false);

  const onNameChange = (event) => {
    setNewProductName(event.target.value);
  };

  const resetAllFields = () => {
    // form.resetFields();
    /* setTableRows([
      {
        id: uniqueRowID,
        product: null,
        qty: 0,
        unitPrice: 0,
        discount: 0,
        amount: 0,
        tax: 0,
      },
    ]); */
  };

  const onFinish = (values) => {

    
    let payload = formatJsonPayload(
      params?.id,
      selectedCurrency?.value,
      cartTotal,
      values,
      newProductName
    );

    createCart(payload);
  };

  const createCart = async (payload) => {
    try {
      const response = await MainService.createCartInDeal(payload);
      if (response?.data?.success) {
        getElementCart();
      }
    } catch (error) {
      console.log(`Error ${error}`);
    }
  };

  const getProducts = async () => {
    try {
      const response = await MainService.getProducts();
      setProductsList(
        response?.data?.data.map((product) => ({
          label: product?.label_data,
          value: product?.id,
        }))
      );
    } catch (error) {
      console.log(`Error ${error}`);
    }
  };

  const getDiscounts = async () => {
    try {
      const response = await MainService.getDiscounts();
      setDiscountsList(
        response?.data?.data
          .filter((el) => el?.status === 1)
          .map((discount) => ({
            label: `${discount?.rate}%`,
            value: discount?.rate,
          }))
      );
    } catch (error) {
      console.log(`Error ${error}`);
    }
  };

  const getCurrency = async () => {
    try {
      const response = await MainService.getUsedCurrencies();
      setCurrenciesList(
        response?.data?.data?.map((item) => formatDataObject(item))
      );
      form.setFieldsValue({
        selectedCurrency: response?.data?.data[0]?.id,
      });
      setSelectedCurrency(response?.data?.data[0]?.id);
    } catch (error) {
      console.log(`Error ${error}`);
    }
  };

  const getElementCart = async () => {
    try {
      setLoadCartDetails(true);
      const response = await MainService.getCartByElement(params?.id);
      if (response?.data?.data) {
        setCartDetails(response?.data?.data);
      }
      setLoadCartDetails(false);
    } catch (error) {
      console.log(`Error ${error}`);
      setLoadCartDetails(false);
    }
  };

  const deleteProductFromCart = async (deleteID) => {
    try {
      const response = await MainService.deleteProductFromCart(deleteID);
    } catch (error) {
      console.log(`Error ${error}`);
    }
  };

  const deleteCart = async () => {
    try {
      const response = await MainService.deleteCart(cartDetails?.id);
      if (response?.data?.success) {
        getElementCart();
      }
    } catch (error) {
      console.log(`Error ${error}`);
    }
  };

  useEffect(() => {
    getProducts();
    getDiscounts();
    getCurrency();
    getElementCart();
  }, []);

  return (
    <>
      {/* <Alert message="This feature is under dev" type="warning" showIcon /> */}
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          flexDirection: "column",
        }}
      >
        <EditableTableForm
          currenciesList={currenciesList}
          setSelectedCurrency={setSelectedCurrency}
          cartDetails={cartDetails}
          productsList={productsList}
          discountsList={discountsList}
          onFinish={onFinish}
          form={form}
        />
      </div>
    </>
  );
};

export default Cart;
