import { useCallback, useEffect, useState } from "react";
import { AutoComplete, Select } from "antd";
import {
  autoCompleteFamily,
  autoCompleteModule,
} from "../../services/services";
import { toastNotification } from "../../../../components/ToastNotification";
import { useTranslation } from "react-i18next";
import { debounce } from "lodash";

const FieldAutoComplete = ({
  fieldValue,
  fieldId,
  familyId,
  fieldModule,
  form,
  setIsDisable,
  RenderOptions,
}) => {
  const [t] = useTranslation("common");

  const [value, setValue] = useState(null);
  const [options, setOptions] = useState([]);
  const [searchText, setSearchText] = useState("");

  // console.log({ form });

  useEffect(() => {
    if (fieldValue) {
      const option = {
        ...fieldValue,
        value: fieldValue?._id || fieldValue?.id,
      };
      setOptions([option]);
      setValue(fieldValue?._id || fieldValue?.id);
      form.setFieldValue(fieldId, fieldValue?._id || fieldValue?.id);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fieldValue]);

  const onChange = (data) => {
    //console.log({ data });
    setValue(data);
    form.setFieldValue(fieldId, data ? data : undefined);
    setIsDisable(false);
  };

  // const onSelect = (data, option) => {
  //   setValue(option.label);
  //   setIsDisable(false);
  //   form.setFieldValue(fieldId, option.value);
  //   form.setFields([
  //     {
  //       name: fieldId,
  //       touched: true,
  //     },
  //   ]);
  // };

  const fetchOptions = useCallback(async () => {
    if (!searchText?.length) {
      // setOptions([]);
      return;
    }
    try {
      const {
        data: { message },
      } = familyId
        ? await autoCompleteFamily(familyId, searchText)
        : await autoCompleteModule(fieldModule, searchText);
      const fetchedOptions = message?.map((e) => ({
        label: e.label,
        value: e.id,
        ...e,
      }));
      setOptions(fetchedOptions);
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? err.message : err);
    }
  }, [familyId, fieldModule, searchText, t]);

  useEffect(() => {
    fetchOptions();
  }, [fetchOptions]);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const handleOnSearch = useCallback(
    debounce((text) => setSearchText(text), 300),
    []
  );

  return (
    <Select
      allowClear
      showSearch
      defaultActiveFirstOption={false}
      value={value}
      style={{
        width: "100%",
      }}
      filterOption={false}
      notFoundContent={null}
      placeholder="Search to display options..."
      suffixIcon={null}
      onChange={onChange}
      onSearch={handleOnSearch}
      // onSelect={onSelect}
    >
      {options?.map(
        (option, i) =>
          option?.value &&
          option.label && (
            <Select.Option
              key={option?.value}
              value={option?.value}
              label={option?.label}
            >
              <RenderOptions option={option} />
            </Select.Option>
          )
      )}
    </Select>
    // <AutoComplete
    //   value={value}
    //   // options={options?.map((item) => ({
    //   //   label: <RenderOptions option={item} />,
    //   //   value: item.value,
    //   // }))}
    //   style={{
    //     width: "100%",
    //   }}
    //   onSearch={handleOnSearch}
    //   placeholder="Search to display options..."
    //   allowClear
    //   onChange={onChange}
    //   onSelect={onSelect}
    // >
    //   {options?.map(
    //     (option, i) =>
    //       option?.id &&
    //       option.label && (
    //         <AutoComplete.Option
    //           key={option?.id}
    //           value={option?.id}
    //           label={option?.label}
    //         >
    //           <RenderOptions option={option} />
    //         </AutoComplete.Option>
    //       )
    //   )}
    // </AutoComplete>
  );
};

export default FieldAutoComplete;
