import { useEffect, useMemo, useState } from "react";
import ChatSidebar from "./sidebar";
import { Popover } from "antd";
import { useLocation } from "react-router-dom";
import { useSelector } from "react-redux";

export const SideBarChatPopover = ({ children, t }) => {
  //
  const location = useLocation();
  //
  const isOverviewActiveOpen = useSelector(
    (state) => state?.TasksRealTime?.isOverviewActive
  );
  const openTaskRoomDrawer = useSelector(
    (state) => state?.TasksRealTime?.openTaskRoomDrawer
  );
  const openChatInViewSphere = useSelector(
    (state) => state?.vue360?.openChatInViewSphere
  );
  //
  const openModalEmail = useSelector(
    (state) => state.mailReducer.openModalEmail
  );
  //
  const [openPopover, setOpenPopover] = useState(false);
  //
  // console.log({ openPopover });
  const displayChatSidebar = useMemo(
    () =>
      isOverviewActiveOpen ||
      openTaskRoomDrawer ||
      openChatInViewSphere ||
      openModalEmail,
    [
      isOverviewActiveOpen,
      openChatInViewSphere,
      openModalEmail,
      openTaskRoomDrawer,
    ]
  );
  //
  const popoverContent = displayChatSidebar ? (
    <strong>{t("voip.msgWhenDisplayChatSidebarIsOpen")}</strong>
  ) : (
    <div className="bg-slate-50 py-4">
      <ChatSidebar source="external" />
    </div>
  );
  //
  useEffect(() => {
    // when user hover in chat icon then click on it it will false the state so when user leave the chat the popover do not open
    location.pathname === "/chat" && setOpenPopover(false);
  }, [location.pathname]);
  //
  return (
    <Popover
      onOpenChange={(newOpen) =>
        setOpenPopover(location.pathname === "/chat" ? false : newOpen)
      }
      open={openPopover && location.pathname !== "/chat"}
      // open={true}
      placement="right"
      content={popoverContent}
      overlayStyle={{ width: "360px" }}
      overlayInnerStyle={{
        padding: displayChatSidebar ? "" : "0px",
        boxShadow: displayChatSidebar
          ? ""
          : "0 25px 50px -12px rgb(0 0 0 / 0.25)",
        borderRadius: displayChatSidebar ? "" : "0.25rem",
      }}
      mouseEnterDelay={0.7}
    >
      {children}
    </Popover>
  );
};
