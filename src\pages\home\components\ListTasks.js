import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Divider,
  List,
  Skeleton,
  Space,
  Spin,
  Tooltip,
  Typography,
} from "antd";
import React, { memo, useEffect, useState } from "react";
import { useLocation, useParams } from "react-router-dom";
import VirtualList from "rc-virtual-list";
import { Clock } from "lucide-react";
import { FaFlag } from "react-icons/fa";
import { useSelector } from "react-redux";

import moment from "moment";
import {
  ArrowRightOutlined,
  CommentOutlined,
  RestOutlined,
  VideoCameraOutlined,
} from "@ant-design/icons";
import { AvatarChat } from "components/Chat";
import { URL_ENV } from "index";
import { getName } from "pages/layouts/chat/utils/ConversationUtils";
import { moment_timezone } from "App";
import { humanDate } from "pages/voip/helpers/helpersFunc";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import useCompAct360 from "pages/tasks/activityDetails/CompAct360";
import MainService from "services/main.service";
import { toastNotification } from "components/ToastNotification";
import { FiEdit3, FiTrash2 } from "react-icons/fi";
import Confirm from "components/GenericModal";
import { getTokenRoom } from "new-redux/actions/visio.actions/createVisio";
import ChoiceIcons from "pages/components/ChoiceIcons";
import { displayPriorityColor } from "pages/tasks/helpers/handlePriorities";
import SelectPipelinesStages from "components/SelectPipelinesStages";
import Activity360 from "pages/tasks/activityDetails/Activity360";
import { setOpenTaskRoomDrawer } from "new-redux/actions/tasks.actions/handleTaskRoomDrawer";

const Description = ({ item, user, t }) => {
  return (
    <div className="mt-1 text-black">
      {item.creator?.id === item.owner_id?.id ? (
        <span className="flex flex-wrap items-center gap-x-1">
          <span className="font-semibold">
            {item.creator?.id === user.id ? (
              t("vue360.you")
            ) : (
              <Tooltip title={item?.creator?.label}>
                <span>
                  <AvatarChat
                    fontSize="0.875rem"
                    url={
                      URL_ENV?.REACT_APP_BASE_URL +
                      URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                      item?.creator?.avatar
                    }
                    type="user"
                    size={24}
                    height={10}
                    width={10}
                    name={getName(item.creator?.label, "avatar")}
                    hasImage={
                      item?.creator?.avatar &&
                      item?.creator?.avatar !== "/storage/uploads/"
                    }
                  />
                </span>
              </Tooltip>
            )}
          </span>{" "}
          {t("vue360.createTask", {
            avoir: item.creator?.id === user.id ? "avez" : "a",
          })}{" "}
          <span className="font-semibold">
            {" "}
            {moment().isSame(item?.created_at, "day")
              ? moment_timezone(item?.created_at).format("ddd D MMM")
              : ""}{" "}
            {/* {moment_timezone(
                `${item?.created_at}`,
                `${user?.location?.date_format}`
              )} */}
            {humanDate(item.created_at, t)}
          </span>{" "}
          {/* {t("vue360.deType")} {selected(item)?.label}{" "} */}
          {t("vue360.ascreatorandowner")}{" "}
        </span>
      ) : (
        <span>
          <span className="font-semibold">
            {item.creator?.id === user.id ? (
              t("vue360.you")
            ) : (
              <Tooltip title={item?.creator?.label}>
                <span>
                  <AvatarChat
                    fontSize="0.875rem"
                    url={
                      URL_ENV?.REACT_APP_BASE_URL +
                      URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                      item?.creator?.avatar
                    }
                    type="user"
                    size={24}
                    height={10}
                    width={10}
                    name={getName(item.creator?.label, "avatar")}
                    hasImage={
                      item?.creator?.avatar &&
                      item?.creator?.avatar !== "/storage/uploads/"
                    }
                  />
                </span>
              </Tooltip>
            )}
          </span>{" "}
          {t("vue360.createTask", {
            avoir: item.creator?.id === user.id ? "avez" : "a",
          })}{" "}
          <span className="font-semibold">
            {" "}
            {humanDate(item.created_at, t)}
          </span>{" "}
          ,{/*  {selected(item)?.label} {t("vue360.or")}{" "} */}
          <span className="font-semibold">
            {" "}
            {item.owner_id?.id === user.id ? (
              t("vue360.you")
            ) : (
              <Tooltip title={item?.owner_id?.label}>
                <span>
                  <AvatarChat
                    fontSize="0.875rem"
                    url={
                      URL_ENV?.REACT_APP_BASE_URL +
                      URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                      item?.owner_id?.avatar
                    }
                    type="user"
                    size={24}
                    height={10}
                    width={10}
                    name={getName(item.owner_id?.label, "avatar")}
                    hasImage={
                      item?.owner_id?.avatar &&
                      item?.owner_id?.avatar !== "/storage/uploads/"
                    }
                  />
                </span>
              </Tooltip>
            )}{" "}
          </span>{" "}
          {item.owner_id?.id === user.id ? t("vue360.are") : t("vue360.is")}{" "}
          {t("vue360.isOwner")}{" "}
        </span>
      )}
    </div>
  );
};
function TaskLabel({
  item,
  openView360InDrawer,
  setTaskToUpdate,
  setDetailsTask,
  setIdTask,
  setOpenActivity360,
  setOpenChat = () => {},
  setSelectedKeySideBar = () => {},
}) {
  const canUpdate = item?.can_update_task === 1;

  const handleClick = () => {
    setOpenChat(false);
    setSelectedKeySideBar("");
    setTaskToUpdate(item.id);
    setDetailsTask(item);
    setIdTask(item.id);
    setOpenActivity360(true);
  };

  const textStyle = {
    maxWidth: openView360InDrawer ? "325px" : "calc(100vw - 1000px)",
    overflow: "hidden",
    textOverflow: "ellipsis",
    whiteSpace: "nowrap",
    cursor: canUpdate ? "pointer" : "default",
  };

  return canUpdate ? (
    <Typography.Link style={textStyle} onClick={handleClick}>
      {item.label}
    </Typography.Link>
  ) : (
    <span style={textStyle}>{item.label}</span>
  );
}
const AllTasks = ({
  list,
  onUpdateTask = () => {},
  onDeleteTask = () => {},
  initLoading,
  setInitLoading,
  setDetailsTask = () => {},
  setIdTask = () => {},
  page,
  lastPage,
  setPage = () => {},
  time,
  selectedValue,
  total,
  setCountTasks = () => {},
  listFilter,
  setTotal = () => {},
  open,
  setOpen = () => {},
  setRoomActivityId = () => {},
  roomActivityId,
  updateKpi,
  setOpenChat = () => {},
  setSelectedKeySideBar = () => {},
}) => {
  const [loading, setLoading] = useState(false);
  const [selectedStage, setSelectedStage] = useState("");
  const [pipelines, setPipelines] = useState([]);
  const { user } = useSelector((state) => state.user);
  const { openView360InDrawer } = useSelector((state) => state?.vue360);
  const { iconsTasks } = useSelector((state) => state.dashboardRealTime);
  const dispatch = useDispatch();
  const params = useParams();
  const [t] = useTranslation("common");
  const selected = (item) => {
    return iconsTasks?.tasks_type?.find((el) => el.id === item.tasks_type_id);
  };
  const location = useLocation();
  const {
    singleTaskData,
    setSingleTaskData,
    form,
    checkedItems,
    setCheckedItems,
    checkedFollowers,
    setCheckedFollowers,

    loadGuests,
    loadSpecificTask,
    taskToUpdate,
    setTaskToUpdate,
    ownersList,
    guestsList,
    guestsSearchQuery,
    setGuestsSearchQuery,
    setFollowersSearchQuery,
    guestsListPage,
    setGuestsListPage,
    guestsListLastPage,
    loadOwners,
    files,
    setFiles,
    openActivity360,
    setOpenActivity360,
    countChanges,
    setCountChanges,
    setSelectedFamilyMembers,
    setShowCardPopover,
    addOnsValues,
    setAddOnsValues,
  } = useCompAct360();
  const openChat = async (id) => {
    dispatch(setOpenTaskRoomDrawer(true));
    setRoomActivityId(id);
  };
  useEffect(() => {
    if (list.length > 0) {
      const getPipelines = async () => {
        // setLoading(true);
        try {
          const response = await MainService.getPipelinesByFamilyTask();

          setPipelines(response?.data?.data);
          setLoading(false);
        } catch (error) {
          setLoading(false);
          toastNotification("error", t("toasts.somethingWrong"));
          console.log(`Error ${error}`);
        }
      };
      getPipelines();
    }
  }, [list.length]);

  useEffect(() => {
    if (openActivity360 && singleTaskData?.id) {
      onUpdateTask(singleTaskData);
    }
    // if (!openActivity360) {
    //   setSingleTaskData({});
    // }
  }, [openActivity360, singleTaskData]);

  const prioritiesList = [
    {
      value: "low",
      label: t("tasks.lowPriority"),
    },
    {
      value: "medium",
      label: t("tasks.mediumPriority"),
    },
    {
      value: "high",
      label: t("tasks.highPriority"),
    },
    {
      value: "urgent",
      label: t("tasks.urgentPriority"),
    },
  ];

  // const onScroll = async (e) => {
  //   if (
  //     (e.currentTarget.clientHeight + Math.round(e.currentTarget.scrollTop) ===
  //       e.currentTarget.scrollHeight ||
  //       e.currentTarget.clientHeight + Math.round(e.currentTarget.scrollTop) ===
  //         e.currentTarget.scrollHeight - 1) &&
  //     page + 1 <= lastPage
  //   ) {
  //     setPage(page + 1);

  //     setLoading(true);
  //     try {
  //       const res = await MainService.getTasks360({
  //         id: params.id,
  //         type:
  //           selectedValue === "6"
  //             ? 3
  //             : selectedValue === "4"
  //             ? 2
  //             : selectedValue === "5"
  //             ? 1
  //             : "",
  //         time: time,
  //         page: page + 1,
  //       });
  //       onUpdateTask((prev) => [...prev, ...res.data.data]);

  //       setLoading(false);
  //     } catch (err) {
  //       setLoading(false);

  //       toastNotification("error", t("toasts.somethingWrong"), "topRight");
  //     }
  //   }
  // };
  const handleDelete = async (id, item) => {
    try {
      let formData = new FormData();
      formData.append("id[]", id);
      const response = await MainService.deleteSpecificTask(formData);
      if (response?.status === 200) {
        onDeleteTask(item);

        toastNotification("success", t("toasts.taskDeleted"), "bottomRight");
      }
    } catch (error) {
      console.log(`Error ${error}`);
      toastNotification("error", t("toasts.somethingWrong"));
    }
  };
  return (
    <>
      <List>
        <VirtualList
          data={list}
          height={300}
          itemHeight={70}
          //   className="list-activities-360"
          itemKey="listTasks"
          // onScroll={onScroll}
        >
          {(item) => (
            // <div className="w-full pb-1" style={{ minWidth: "max-content" }}>
            //   <Card
            //     style={{
            //       width: "100%",
            //       padding: 0,
            //       background: location.pathname.includes("v3")
            //         ? "#F8FAFC"
            //         : "white",
            //     }}
            //   >
            <List.Item
              // extra={
              //   item?.tasks_type_id === 3 && item?.creator?.id !== user.id ? (
              //     <Button type="link" size="small">
              //       Join Meeting
              //     </Button>
              //   ) : null
              // }
              actions={[
                // item?.guests?.some((el) => el.id === user.id) ||
                // item?.followers?.some((el) => el.id === user.id) ||
                // item?.owner_id?.id === user.id ||
                // item?.creator?.id === user.id ? (
                //   <Button
                //     key="list-loadmore-edit"
                //     onClick={() => {
                //       setDetailsTask(item);
                //       setIdTask(item.id);
                //       setOpen(true);
                //     }}
                //     type="link"
                //     size="small"
                //     shape="circle"
                //   >
                //     <FiEdit3 />
                //   </Button>
                // ) : null,
                item?.can_create_room === 1 && (
                  <Button
                    key="list-loadmore-edit"
                    onClick={() => openChat(item?.id)}
                    type="text"
                    shape="circle"
                    size="small"
                  >
                    <CommentOutlined />
                  </Button>
                ),

                item?.tasks_type_id === 3 ? (
                  <Tooltip title={t("chat.header.visio.join")}>
                    <Button
                      icon={<VideoCameraOutlined />}
                      size="small"
                      shape="circle"
                      type="text"
                      className="relative -top-0.5"
                      onClick={() => {
                        dispatch(
                          getTokenRoom({
                            room: item?.location,
                            errorText1: t("toasts.errorFetchApi"),
                            errorText2: t("toasts.errorRoomNotFound"),
                          })
                        );
                      }}
                    />
                  </Tooltip>
                ) : null,
                item?.owner_id?.id === user.id ||
                item?.creator?.id === user.id ? (
                  <Button
                    key="list-loadmore-edit"
                    onClick={() => {
                      Confirm(
                        `Delete "${item.label}" `,
                        "Confirm",
                        <RestOutlined style={{ color: "red" }} />,
                        function func() {
                          return handleDelete(item.id, item);
                        },
                        true
                      );
                    }}
                    danger
                    size="small"
                    shape="circle"
                    type="text"
                  >
                    <FiTrash2 />
                  </Button>
                ) : null,
                // <a key="list-loadmore-more">more</a>,
              ].filter((el) => el)}
            >
              <Skeleton avatar title={false} loading={item.loading} active>
                <List.Item.Meta
                  style={{
                    display: "flex",
                    alignItems: "center",
                  }}
                  avatar={
                    <Tooltip
                      title={
                        iconsTasks?.tasks_type?.find(
                          (el) => el.id === item.tasks_type_id
                        )?.label
                      }
                    >
                      <Avatar
                        style={{
                          display: "flex",
                          alignItems: "center",
                          background: iconsTasks?.tasks_type?.find(
                            (el) => el.id === selected(item)?.id
                          )?.color,
                        }}
                        gap={0}
                        size="large"
                      >
                        <ChoiceIcons icon={selected(item)?.icons} />
                      </Avatar>
                    </Tooltip>
                  }
                  title={
                    <Space split={<Divider type="vertical" />}>
                      <div className="flex items-center space-x-1 ">
                        {/* <ChoiceIcons icon={selected(item)?.icons} />{" "} */}
                        <TaskLabel
                          item={item}
                          openView360InDrawer={openView360InDrawer}
                          setTaskToUpdate={setTaskToUpdate}
                          setDetailsTask={setDetailsTask}
                          setIdTask={setIdTask}
                          setOpenActivity360={setOpenActivity360}
                          setOpenChat={setOpenChat}
                          setSelectedKeySideBar={setSelectedKeySideBar}
                        />
                      </div>
                      <Tooltip
                        title={
                          <div>
                            <div>
                              {t("mailing.NewMsg.from")}:{" "}
                              {moment_timezone(
                                `${item?.start_date} ${item?.start_time}`,
                                `${user?.location?.date_format} ${user?.location?.time_format}`
                              ).calendar(null, {
                                sameDay: () => {
                                  return `[${t("tasks.today")}] ${
                                    user?.location?.time_format
                                  }`;
                                },
                              })}
                            </div>
                            <div>
                              {t("mailing.NewMsg.To")}:{" "}
                              {moment(
                                `${item?.end_date} ${item?.end_time}`,
                                `${user?.location?.date_format} ${user?.location?.time_format}`
                              ).calendar(null, {
                                sameDay: () => {
                                  return `[${t("tasks.today")}] ${
                                    user?.location?.time_format
                                  }`;
                                },
                              })}
                            </div>
                          </div>
                        }
                      >
                        <div className="flex items-center space-x-1">
                          <Clock size={15} />

                          <span>
                            {moment_timezone(
                              `${item?.start_date} ${item?.start_time}`,
                              `${user?.location?.date_format} ${user?.location?.time_format}`
                            ).calendar(null, {
                              sameDay: () => {
                                return `[${t("tasks.today")}] ${
                                  user?.location?.time_format
                                }`;
                              },
                            })}
                          </span>
                        </div>
                      </Tooltip>
                      {item.priority ? (
                        <Tooltip
                          title={
                            prioritiesList.find(
                              (el) => el.value === item.priority
                            )?.label
                          }
                        >
                          <FaFlag
                            style={{
                              color: displayPriorityColor(item.priority),
                            }}
                          />
                        </Tooltip>
                      ) : null}
                      {/* {item.created_at ? (
                      <Tag>
                        <MdMoreTime /> : {humanDate(item.created_at, t)}
                      </Tag>
                    ) : (
                      ""
                    )} */}
                    </Space>
                  }
                  description={
                    <div className="flex flex-col space-y-1.5">
                      <Description
                        item={item}
                        user={user}
                        t={t}
                        list={list}
                        onUpdateTask={onUpdateTask}
                      />
                      <Space split={<Divider type="vertical" />}>
                        {item?.guests?.length > 0 && (
                          <Space
                            style={{
                              textAlign: "center",
                              width: "max-content",
                            }}
                          >
                            <Typography.Text>
                              {t("visio.guest", {
                                plural: item?.guests?.length > 1 ? "s" : "",
                              })}
                              :
                            </Typography.Text>
                            <Avatar.Group
                              maxCount={3}
                              maxPopoverTrigger="click"
                              maxStyle={{
                                color: "#f56a00",
                                backgroundColor: "#fde3cf",
                                cursor: "pointer",
                              }}
                            >
                              {item?.guests.map((el) => (
                                <Tooltip title={getName(el.label, "name")}>
                                  <span>
                                    <AvatarChat
                                      fontSize="0.72rem"
                                      url={
                                        URL_ENV?.REACT_APP_BASE_URL +
                                        URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                                        el?.avatar
                                      }
                                      type="user"
                                      size={26}
                                      height={6}
                                      width={6}
                                      name={getName(el.label, "avatar")}
                                      hasImage={[
                                        "jpg",
                                        "jpeg",
                                        "png",
                                        "webp",
                                        "gif",
                                        "bmp",
                                        "tiff",
                                        "tif",
                                        "svg",
                                        "avif",
                                        "jfif",
                                      ].includes(el?.avatar?.split(".")?.pop())}
                                    />
                                  </span>
                                </Tooltip>
                              ))}
                            </Avatar.Group>{" "}
                          </Space>
                        )}
                        {item?.followers?.length > 0 && (
                          <Space
                            style={{
                              textAlign: "center",
                              width: "max-content",
                            }}
                          >
                            <Typography.Text>
                              {t("tasks.followers", {
                                s: item?.followers?.length > 1 ? "s" : "",
                              })}
                              :
                            </Typography.Text>
                            <Avatar.Group
                              maxCount={3}
                              maxPopoverTrigger="click"
                              maxStyle={{
                                color: "#f56a00",
                                backgroundColor: "#fde3cf",
                                cursor: "pointer",
                              }}
                            >
                              {item.followers.map((el) => (
                                <Tooltip title={getName(el.label, "name")}>
                                  <span>
                                    <AvatarChat
                                      fontSize="0.72rem"
                                      url={
                                        URL_ENV?.REACT_APP_BASE_URL +
                                        URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                                        el?.avatar
                                      }
                                      type="user"
                                      size={26}
                                      height={6}
                                      width={6}
                                      name={getName(el.label, "avatar")}
                                      hasImage={[
                                        "jpg",
                                        "jpeg",
                                        "png",
                                        "webp",
                                        "gif",
                                        "bmp",
                                        "tiff",
                                        "tif",
                                        "svg",
                                        "avif",
                                        "jfif",
                                      ]?.includes(
                                        el?.avatar?.split(".")?.pop()
                                      )}
                                    />
                                  </span>
                                </Tooltip>
                              ))}
                            </Avatar.Group>{" "}
                          </Space>
                        )}
                        {item.pipeline_label &&
                        (item.owner_id.id === user.id ||
                          item.creator?.id === user.id) ? (
                          <SelectPipelinesStages
                            stages={pipelines}
                            record={item}
                            iconsTasks={iconsTasks?.tasks_types || []}
                            setSelectedStage={setSelectedStage}
                            selectedStage={selectedStage}
                            setList={onUpdateTask}
                            setCountTasks={setCountTasks}
                            listFilter={listFilter}
                            source=""
                            size="small"
                          />
                        ) : null}
                      </Space>
                    </div>
                  }
                />
              </Skeleton>
            </List.Item>
            //   </Card>
            // </div>
          )}
        </VirtualList>
        {loading ? <Spin /> : ""}
      </List>
      {/* {openActivity360 ? ( */}
      {openActivity360 ? (
        <Activity360
          key={taskToUpdate ? taskToUpdate : 1}
          openActivity360={openActivity360}
          setOpenActivity360={setOpenActivity360}
          taskToUpdate={taskToUpdate}
          singleTaskData={singleTaskData}
          setSingleTaskData={setSingleTaskData}
          loadSpecificTask={loadSpecificTask}
          tasksTypes={iconsTasks?.tasks_type || []}
          setTaskToUpdate={setTaskToUpdate}
          pipelines={pipelines}
          guestsList={guestsList}
          checkedItems={checkedItems}
          guestsSearchQuery={guestsSearchQuery}
          setGuestsSearchQuery={setGuestsSearchQuery}
          guestsListPage={guestsListPage}
          setGuestsListPage={setGuestsListPage}
          guestsListLastPage={guestsListLastPage}
          setCheckedItems={setCheckedItems}
          ownersList={ownersList}
          checkedFollowers={checkedFollowers}
          setCheckedFollowers={setCheckedFollowers}
          setFollowersSearchQuery={setFollowersSearchQuery}
          loadOwners={loadOwners}
          loadGuests={loadGuests}
          addOnsValues={addOnsValues}
          setAddOnsValues={setAddOnsValues}
          files={files}
          setFiles={setFiles}
          countChanges={countChanges}
          setCountChanges={setCountChanges}
          setSelectedFamilyMembers={setSelectedFamilyMembers}
          form={form}
          setShowCardPopover={setShowCardPopover}
        />
      ) : null}
      {/* ) : null} */}
    </>
  );
};

export default memo(AllTasks);
