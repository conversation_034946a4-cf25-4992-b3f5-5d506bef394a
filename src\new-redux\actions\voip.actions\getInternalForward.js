import {
  GET_INTERNALCALLFORWARD_SUCCESS,
  GET_INTERNALCALLFORWARD_ERROR,
} from "../../constants";
// import MainService from "../../../services/main.service";
import { getConfigIPBX } from "../../../pages/voip/services/services";

export const getInternalForward = () => async (dispatch) => {
  try {
    // dispatch({ type: IS_LOADING_VOICE });
    const response = await getConfigIPBX();
    dispatch({
      type: GET_INTERNALCALLFORWARD_SUCCESS,
      payload: response?.data?.data || [],
    });
  } catch (error) {
    dispatch({
      type: GET_INTERNALCALLFORWARD_ERROR,
      payload: error,
    });
  }
};
