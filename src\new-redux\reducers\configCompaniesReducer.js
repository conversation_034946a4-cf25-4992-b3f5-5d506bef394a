import { ADD_NAME_ORG, RESET_STATE } from "../constants";

const initialState = {
  nameOrg: "",
};

const ConfigCompanies = (state = initialState, action) => {
  const { type, payload } = action;
  switch (type) {
    case ADD_NAME_ORG:
      return {
        ...state,
        nameOrg: payload,
      };
    case RESET_STATE: {
      return initialState;
    }
    default:
      return state;
  }
};

export default ConfigCompanies;
