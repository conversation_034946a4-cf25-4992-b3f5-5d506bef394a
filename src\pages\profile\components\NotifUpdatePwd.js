import React, { useState, useEffect } from "react";
import { Button, Typography } from "antd";
import { EyeInvisibleOutlined, EyeOutlined } from "@ant-design/icons";
const { Text, Paragraph } = Typography;

const NotifUpdatePwd = ({ password, closeNotification, t }) => {
  const [hidden, setHidden] = useState(true);
  const [seconds, setSeconds] = useState(30);
  const asterisks = "*".repeat(password.length);

  useEffect(() => {
    const timer = setInterval(() => {
      setSeconds((prevSeconds) => {
        if (prevSeconds <= 1) {
          clearInterval(timer);
          closeNotification();
          return 0;
        }
        return prevSeconds - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [closeNotification]);

  return (
    <>
      <Paragraph>
        {t("profile.yourNewPwd")}:{" "}
        <Text strong copyable={{ text: password }}>
          {hidden ? asterisks : password}
        </Text>
        {hidden ? (
          <Button
            onClick={() => setHidden(!hidden)}
            type="link"
            icon={<EyeInvisibleOutlined />}
          />
        ) : (
          <Button
            onClick={() => setHidden(!hidden)}
            type="link"
            icon={<EyeOutlined />}
          />
        )}
        {/* <Button type="link" onClick={() => setHidden(!hidden)}>
          Toggle
        </Button> */}
      </Paragraph>
      <Paragraph>
        {t("profile.thisNotifWillDisappear", { x: seconds })}
      </Paragraph>
    </>
  );
};

export default NotifUpdatePwd;
