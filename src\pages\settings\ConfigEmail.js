import { <PERSON>, Tabs, Tooltip } from "antd";
import { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import Header from "../../components/configurationHelpDesk/Header";
import TableConfigMail from "../../components/TableConfigMail";
import { toastNotification } from "../../components/ToastNotification";
import { setOpenConfigMail } from "../../new-redux/actions/menu.actions/menu";
import { generateAxios } from "../../services/axiosInstance";
import DrawerEmail from "../mailing/DrawerEmail";
import { URL_ENV } from "index";
import BannerIndicatorsConfigMail from "./BannerIndicatorsConfigMail";
import { InfoCircleOutlined } from "@ant-design/icons";
import EmptyPage from "components/EmptyPage";
const ConfigEmail = () => {
  const [toggle, setToggle] = useState(null);
  const [loading, setLoading] = useState(true);
  const [loadingDrawer, setLoadingDrawer] = useState(false);

  const [labelId, setLabelId] = useState(null);
  const [primaryAccount, setPrimaryAccount] = useState(null);

  const [count, setCount] = useState(0);
  const [hasSameNameAndPassword, setHasSameNameAndPassword] = useState(true);

  const [data, setData] = useState([]);
  const [configEmail, setConfigEmail] = useState({});
  const [debounceValue, setDebounceValue] = useState("");
  const [activeKey, setActiveKey] = useState("1");
  const { search } = useSelector((state) => state.form);
  const [t] = useTranslation("common");
  const dispatch = useDispatch();

  const formRef = useRef(null);

  const onClose = () => {
    dispatch(setOpenConfigMail(false));
    formRef.current.resetFields();
  };
  const onChangeTabs = (key) => {
    setActiveKey(key);
  };
  // useEffect(() => {
  //   const timer = setTimeout(() => setDebounceValue(search), 150);

  //   return () => {
  //     clearTimeout(timer);
  //   };
  // }, [search]);
  useEffect(
    () => {
      const getData = async () => {
        setLoading(true);
        try {
          const {
            data: { data },
          } = await generateAxios(
            URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
          ).get(
            `/config-mails
          `
          );
          // setData(
          //   data
          //     .map((el, i) => ({ rank: i + 1, ...el, key: el.id }))
          //     .sort((a, b) => {
          //       return b.primary_account - a.primary_account;
          //     })
          // );
          setData(data);
          // if (!data.some((el) => el.type == 1))
          setLoading(false);
          setToggle(data.find((el) => el.primary_account === 1)?.id);
        } catch (err) {
          setLoading(false);
          toastNotification("error", t("toasts.somethingWrong"), "topRight");
        }
      };
      const getEmails = async () => {
        setLoading(true);
        try {
          const {
            data: { data },
          } = await generateAxios(
            URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
          ).post(`/configMail/search`, {
            label: debounceValue,
          });
          setData(data);
          setLoading(false);
        } catch (e) {
          setLoading(false);
          toastNotification("error", t("toasts.somethingWrong"), "topRight");
        }
      };
      // if (!debounceValue)
      getData();
      // else getEmails();
      return () => setLoading(null);
    },
    [
      //debounceValue
    ]
  );
  useEffect(() => {
    const getDataBeforeUpdate = async () => {
      setLoadingDrawer(true);
      try {
        const {
          data: { data },
        } = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get(
          `/config-mails/${labelId}
          `
        );
        setConfigEmail({
          ...data,
          label: data?.signature?.label,
          value: data?.signature?.value,
        });
        setPrimaryAccount(data.primary_account);
        setHasSameNameAndPassword(
          data.username === data.username_smtp &&
            data.password === data.password_smtp
        );
        setLoadingDrawer(false);

        // dispatch(setOpenConfigMail({ type: "", open: true }));
        setCount(0);
      } catch (err) {
        setLoadingDrawer(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    };

    if (labelId && count > 0) {
      getDataBeforeUpdate();
    }
  }, [labelId, dispatch, count]);
  const filteredData = data.filter((item) => {
    let isMatch = false;
    item.departement_labels?.forEach((el) => {
      if (el.toLowerCase().includes(search.toLowerCase())) isMatch = true;
    });

    return (
      item.email.toLowerCase().includes(search.toLowerCase()) ||
      isMatch ||
      t(`emailAccounts.${item.type_show.toLowerCase()}`)
        ?.toLowerCase()
        ?.includes(search.toLowerCase())
    );
  });
  const items = [
    {
      key: "1",
      label: t("menu2.emailaccounts"),
      children: (
        <>
          <div
            className="pt-4"
            style={{ visibility: data.length > 0 ? "visible" : "hidden" }}
          >
            <Header
              active={"3"}
              editingKey={""}
              handleAdd={() => dispatch(setOpenConfigMail(true))}
              btnText={t("emailAccounts.connectAccount")}
              disabled={loading || data.length === 0 ? true : false}
            />
          </div>
          <Space direction="vertical" style={{ width: "100%" }}>
            {loading || data.length > 0 ? (
              <TableConfigMail
                data={filteredData}
                setData={setData}
                loading={loading}
                setLoading={setLoading}
                onClose={onClose}
                setConfigEmail={setConfigEmail}
                setLabelId={setLabelId}
                setCount={setCount}
                count={count}
                toggle={toggle}
                setToggle={setToggle}
              />
            ) : (
              <EmptyPage
                heroTitle={<span>{t("mailing.titleconfigureEmail")} </span>}
                mainBtnTitle={t("emailAccounts.connectAccount")}
                handleMainBtnClick={() => dispatch(setOpenConfigMail(true))}
              />
            )}
          </Space>

          <DrawerEmail
            configEmail={configEmail}
            setConfigEmail={setConfigEmail}
            labelId={labelId}
            setLabelId={setLabelId}
            setData={setData}
            toggle={toggle}
            primary_account={primaryAccount}
            setHasSameNameAndPassword={setHasSameNameAndPassword}
            hasSameNameAndPassword={hasSameNameAndPassword}
            loadingDrawer={loadingDrawer}
            setLoadingDrawer={setLoadingDrawer}
            inbox={false}
          />
        </>
      ),
    },
    {
      key: "2",
      label: (
        <Space>
          {t("emailAccounts.displayIndicators")}
          <Tooltip title={t("emailAccounts.tooltipBanner")}>
            <InfoCircleOutlined />
          </Tooltip>
        </Space>
      ),
      children: <BannerIndicatorsConfigMail />,
    },
  ];
  return (
    <Space direction="vertical" style={{ width: "100%" }}>
      <div className="px-4 pt-4">
        <Tabs
          type="card"
          activeKey={activeKey}
          items={items}
          onChange={onChangeTabs}
        />
      </div>
    </Space>
  );
};

export default ConfigEmail;
