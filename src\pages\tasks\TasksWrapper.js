/**
 * * @name TasksWrapper
 *
 * @description The activities module allows to track, manage, and organize various activities related to customer interactions and business processes.
 *
 *
 * @param {String} id id of the activity to update.
 * @param {Function} setId Sets "id" state.
 *
 * @returns {JSX.Element} Activites wrapper component (holds all different views).
 */

import {
  useState,
  useEffect,
  lazy,
  Suspense,
  useCallback,
  useRef,
  useLayoutEffect,
  memo,
  useMemo,
} from "react";
import {
  Drawer,
  Form,
  Button,
  Spin,
  Tooltip,
  Popover,
  Badge,
  Typography,
  Checkbox,
  Divider,
  Space,
  Row,
  Segmented,
  Skeleton,
  Tour,
  Image,
} from "antd";
import {
  TableOutlined,
  CalendarOutlined,
  BellOutlined,
  MessageOutlined,
  UploadOutlined,
  LoadingOutlined,
  FilterOutlined,
  ColumnWidthOutlined,
  RightCircleOutlined,
  UserOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import dayjs from "dayjs";
import "react-quill/dist/quill.snow.css";
import { useLocation, useNavigate } from "react-router-dom";
import moment from "moment";
import DOMPurify from "dompurify";
import { MdOutlineTableChart } from "react-icons/md";

import MainService from "../../services/main.service";
import SearchInTable from "../components/Search";
import { toastNotification } from "../../components/ToastNotification";
import "./style.css";
import {
  addReminder,
  isOverviewModalOpen,
  setActiveTasksFilters,
  setPipelinePreference,
  setRemindersArray,
  setSelectedViewInTask,
  setTasksFilters,
  setTotalNotificationsNumber,
} from "../../new-redux/actions/tasks.actions/realTime";
import {
  setMsgTask,
  setOpenTaskDrawer,
} from "../../new-redux/actions/tasks.actions/handleTaskDrawer";
import NavHeader from "../../components/NavHeader";
import NotificationsPopover from "../../components/NotificationsPopover";
import {
  setActivitiesMessages,
  setOpenTaskRoomDrawer,
} from "../../new-redux/actions/tasks.actions/handleTaskRoomDrawer";
import { AvatarChat } from "../../components/Chat";
import { lazyRetry } from "../../utils/lazyRetry";
import { generateAxios } from "../../services/axiosInstance";
import useKeyDown from "../../custom-hooks/useKeyDown";
import { getName } from "../layouts/chat/utils/ConversationUtils";
import useDebounce from "../components/UseDebounce/UseDebounce";
import { URL_ENV } from "index";
import ModuleElementDetails from "./ModuleElementDetails";
import FilterTableCols from "./FilterTableCols";
import { LoaderDrawer } from "pages/layouts/chat";
import KpiGrid from "./KpiGrid";
import FilterTags from "./FilterTags";
import {
  ADD_TASK_NOTIFICATION_ACTION_TYPE,
  SET_CONTACT_INFO_FROM_DRAWER,
} from "new-redux/constants";
import {
  calculateSum,
  EXTENSIONS_ARRAY,
  formatDates,
  getPriorityColor,
  getValueByKey,
} from "./helpers/calculateSum";
import { BsCommand } from "react-icons/bs";
import {
  setCountNotificationVisio,
  setCountReminders,
  setRemindersList,
} from "new-redux/actions/visio.actions/visio";
import { closeDrawerChat } from "new-redux/actions/voip.actions/handleDrawerChat";
import ActionsComponent from "./ActionsComponent";
import { HighlightSearchW } from "pages/voip/components";
import { getRelevantFiltersCount } from "./helpers/filterCount";
import StatsDrawer from "pages/components/StatsDrawer";
import { allColumns } from "./helpers/handleCheck";
import {
  formatDatePayload,
  formatTimePayload,
} from "./helpers/formatDateToDisplay";
import { clearSearchInput } from "new-redux/actions/form.actions/form";
import ChoiceIcons from "pages/components/ChoiceIcons";
import { fetchPhoneBook } from "pages/voip/services/services";
import { Refs_IDs } from "components/tour/tourConfig";

//Dynamic imports.
const TasksRoom = lazy(
  () => lazyRetry(() => import("./tasksRoom")),
  "TasksRoom"
);
const FilterOptions = lazy(
  () => lazyRetry(() => import("./FilterOptions")),
  "FilterOptions"
);
const TasksTableView = lazy(
  () => lazyRetry(() => import("./tasksViews/TasksTableView")),
  "TasksTableView"
);
const CalendarView = lazy(
  () => lazyRetry(() => import("./tasksViews/CalendarView")),
  "CalendarView"
);
const KanbanBoard = lazy(
  () => lazyRetry(() => import("./tasksViews/kanban/KanbanBoard")),
  "KanbanBoard"
);
const TasksListView = lazy(
  () => lazyRetry(() => import("./tasksViews/listView/TasksListView")),
  "TasksListView"
);
const CreateTaskForm = lazy(
  () => lazyRetry(() => import("./CreateTaskForm")),
  "CreateTaskForm"
);
const Activity360 = lazy(
  () => lazyRetry(() => import("./activityDetails/Activity360")),
  "Activity360"
);

const TasksWrapper = memo(function TasksWrapper({ id = "", setId = () => {} }) {
  const { user } = useSelector((state) => state.user);
  const selectedConversation = useSelector(
    (state) => state.ChatRealTime.selectedConversation
  );
  const chat = useSelector((state) => state.chat);
  const usersList = useSelector((state) => state?.chat?.userList);
  const { search } = useSelector((state) => state.form);
  const { remindersListVisio } = useSelector((state) => state.visioList);
  const [t] = useTranslation("common");
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const searchInputRef = useRef();
  const apiCalledRef = useRef(false);
  const debouncedSearchValue = useDebounce(
    DOMPurify.sanitize(search, { ALLOWED_TAGS: [], ALLOWED_ATTR: [] }),
    500
  );
  const redirectActivityId = localStorage.getItem("redirect-activity-id");
  const columnsFilters = JSON.parse(localStorage.getItem("columns-filter"));

  // Instances.
  const [form] = Form.useForm();
  const [filtersForm] = Form.useForm();
  const {
    openTaskDrawer,
    isUserNotified,
    taskNotifPayload,
    taskNotifDescription,
    totalNotificationNumber,
    taskNotifActionType,
    tasksFilters,
    openTaskRoomDrawer,
    msgTask,
    activitiesMessages,
    activeFilters,
    remindersList,
    pipelinePreference,
    userPreference,
  } = useSelector((state) => state?.TasksRealTime);

  // Onl load, get the selected view from localStorage.
  const preferredView = localStorage.getItem("tasks-view");
  const [columnsBackup, setColumnsBackup] = useState([]);
  const [tags, setTags] = useState([]);

  // Local states.
  const [appliedFilters, setAppliedFilters] = useState(activeFilters ?? []);
  const [singleTaskData, setSingleTaskData] = useState({});
  const [checkedItems, setCheckedItems] = useState([]);
  const [checkedFollowers, setCheckedFollowers] = useState([]);
  const [tasksData, setTasksData] = useState([]);
  const [tasksTypes, setTasksTypes] = useState([]);
  const [loadTasks, setLoadTasks] = useState(false);
  const [loadGuests, setLoadGuests] = useState(false);
  const [loadSpecificTask, setLoadSpecificTask] = useState(false);
  const [createAndUpdateTaskLoading, setCreateAndUpdateTaskLoading] =
    useState(false);
  const [taskToUpdate, setTaskToUpdate] = useState(null);
  const [roomActivityId, setRoomActivityId] = useState(null);
  const [ownersList, setOwnersList] = useState([]);
  const [guestsList, setGuestsList] = useState([]);
  const [guestsSearchQuery, setGuestsSearchQuery] = useState("");
  const [followersSearchQuery, setFollowersSearchQuery] = useState("");
  const [doctorsSeacrhQuery, setDoctorsSearchQuery] = useState("");
  const [sorter, setSorter] = useState(() => {
    try {
      const savedSorter = localStorage.getItem("sorter_activity");
      return savedSorter
        ? JSON.parse(savedSorter)
        : { sort_field: "", sort_direction: "" };
    } catch (error) {
      console.error("Failed to parse saved sorter:", error);
      return { sort_field: "", sort_direction: "" };
    }
  });
  const [showTime, setShowTime] = useState(false);
  const [fetchTasks, setFetchTasks] = useState(false);
  const [guestsListPage, setGuestsListPage] = useState(1);
  const [guestsListLastPage, setGuestsListLastPage] = useState(null);
  const [followersListPage, setFollowersListPage] = useState(1);
  const [followersListLastPage, setFollowersListLastPage] = useState(null);
  const [doctorsListLastPage, setDoctorsListLastPage] = useState(1);
  const [examsListLastPage, setExamsListLastPage] = useState(1);
  const [selectedStartDate, setSelectedStartDate] = useState("");
  const [selectedEndDate, setSelectedEndDate] = useState("");
  const [selectedStartTime, setSelectedStartTime] = useState("");
  const [selectedEndTime, setSelectedEndTime] = useState("");
  const [loadOwners, setLoadOwners] = useState(false);
  const [loadDoctors, setLoadDoctors] = useState(false);
  const [loadExams, setLoadExams] = useState(false);
  const [pageNumber, setPageNumber] = useState(1);
  const [limit, setLimit] = useState(15);
  const [total, setTotal] = useState(null);
  const [totalFiltered, setTotalFiltered] = useState(null);
  const [files, setFiles] = useState([]);
  const [events, setEvents] = useState([]);
  const [pipelines, setPipelines] = useState([]);
  const [loadNotifs, setLoadNotifs] = useState(false);
  const [columns, setColumns] = useState([]);
  const [selectedStageId, setSelectedStageId] = useState(null);
  const [loadUpdateTaskStage, setLoadUpdateTaskStage] = useState(false);
  const [stageIdToFilter, setStageIdToFilter] = useState([]);
  const [notifications, setNotifications] = useState([]);
  const [showNotificationsMenu, setShowNotificationsMenu] = useState(false);
  const [notificationsPage, setNotificationsPage] = useState(1);
  const [lastNotificationsPage, setLastNotificationsPage] = useState(null);
  const [deletedTaskIndicator, setDeletedTaskIndicator] = useState(false);
  const [notFoundTaskIndicator, setNotFoundTaskIndicator] = useState(false);
  const [reminders, setReminders] = useState([]);
  const [checkDueDateReminder, setCheckDueDateReminder] = useState(false);
  const [loadingExport, setLoadingExport] = useState(false);
  const [isError, setIsError] = useState(false);
  const [openFilterMenu, setOpenFilterMenu] = useState(false);
  const [getTasksError, setGetTasksError] = useState(false);
  const [updateFilters, setUpdateFilters] = useState(0);
  const [searchParams, setSearchParams] = useState([]);
  const [openElementDetails, setOpenElementDetails] = useState(false);
  const [openActivity360, setOpenActivity360] = useState(false);
  const [activeTabKey, setActiveTabKey] = useState("generalInfoActivities");
  const [activityLabel, setActivityLabel] = useState("");
  const [emailNotification, setEmailNotification] = useState(false);
  const [countChanges, setCountChanges] = useState(0);
  const [filterNotifications, setFilterNotifications] = useState(0);
  const [kpisList, setKpisList] = useState(null);
  const [showCardPopover, setShowCardPopover] = useState({});
  const [selectedPipelineIsActive, setSelectedPipelineIsActive] =
    useState(false);
  const [roomsMessages, setRoomsMessages] = useState([]);
  const [reminderValidator, setReminderValidator] = useState(false);
  const [markAllAsReadLoading, setMarkAllAsReadLoading] = useState(false);
  const [loadKpis, setLoadKpis] = useState(false);
  const [totalUnreadMessages, setTotalUnreadMessages] = useState(0);
  const [meMode, setMeMode] = useState(false);
  const [checkedColumns, setCheckedColumns] = useState(
    columnsFilters ? columnsFilters : allColumns?.map((el) => el?.value)
  );
  const [elementDetails, setElementDetails] = useState({
    id: null,
    module: null,
  });
  const [addOnsValues, setAddOnsValues] = useState({
    description: "",
    note: "",
  });
  const [totalEntities, setTotalEntities] = useState({
    colleagues: 0,
    all: 0,
  });
  const [dateSegmented, setDateSegmented] = useState(
    getValueByKey(activeFilters, "dateType")?.dateType ?? 3
  );
  const [selectedFamilyMembers, setSelectedFamilyMembers] = useState([
    1, 2, 4, 9,
  ]);
  const [dateFilter, setDateFilter] = useState(
    getValueByKey(activeFilters, "filterByDate")
      ? [
          dayjs(
            new Date(
              getValueByKey(activeFilters, "filterByDate")?.filterByDate[0]
            )
          ).format(user?.location?.date_format),
          dayjs(
            new Date(
              getValueByKey(activeFilters, "filterByDate")?.filterByDate[1]
            )
          ).format(user?.location?.date_format),
        ]
      : null
  );
  const [selectedFamily, setSelectedFamily] = useState(
    getValueByKey(appliedFilters, "related_element")?.related_element ?? []
  );
  const [selectedRoles, setSelectedRoles] = useState(
    getValueByKey(appliedFilters, "selectRoles")?.selectRoles ?? [0, 1, 2, 3]
  );
  const [filterTable, setFilterTable] = useState(
    getValueByKey(appliedFilters, "types")?.types ?? ""
  );
  const [selectedTags, setSelectedTags] = useState(
    getValueByKey(appliedFilters, "tags")?.tags ?? ""
  );
  const [switchViews, setSwitchViews] = useState(
    !preferredView ? "Table" : preferredView
  );
  const [selectedUser, setSelectedUser] = useState(
    {
      id: getValueByKey(activeFilters, "user")?.user,
    } || user?.id
  );
  const [pageDoctors, setPageDoctors] = useState(1);
  const [doctors, setDoctors] = useState([]);
  const [selectedPipeline, setSelectedPipeline] = useState(
    pipelinePreference?.isSelectPipelineActive === true
      ? pipelinePreference?.preferredPipeline
      : 0
  );
  const debounceGuestsSearch = useDebounce(guestsSearchQuery, 500);
  const debounceFollowersSearch = useDebounce(followersSearchQuery, 500);
  const debounceDoctorsSearch = useDebounce(doctorsSeacrhQuery, 500);
  const filterCondition = filtersForm.getFieldValue("filterCondition") ?? "and";
  const selectFutureActivities =
    (activeFilters?.some((el) => el?.hasOwnProperty("filterFromTodayOn")) ||
      filtersForm.getFieldValue("filterFromTodayOn")) ??
    false;
  useEffect(() => {
    if (id) {
      setTaskToUpdate(id);
    }
  }, [id]);

  // Open create activity drawer in chat (convert message to task), get the participant(s) based on type of room.
  useEffect(() => {
    if (location.pathname === "/chat") {
      selectedConversation?.type === "user"
        ? setCheckedItems([
            {
              id: selectedConversation?.id,
              label: getName(selectedConversation?.name, "name"),
              avatar:
                selectedConversation?.image ||
                getName(selectedConversation?.name, "name")[0],
            },
          ])
        : selectedConversation?.type === "room"
        ? setCheckedItems(
            chat?.selectedParticipants
              .filter((el) => el.id !== chat?.currentUser?._id && el.uuid)
              .map((el) => ({
                id: el?.id,
                label: getName(el?.name, "name"),
                avatar: el?.image || getName(el?.name, "name")[0],
              }))
          )
        : setCheckedItems([]);
      setAddOnsValues({ description: msgTask });
    }
  }, [location, selectedConversation, msgTask]);

  // Open activity by id on redirect (for example from globalSearch).
  useEffect(() => {
    if (location.pathname.split("/")[2]) {
      setTaskToUpdate(location.pathname.split("/")[2]);
      setOpenActivity360(true);
    }
  }, [location.pathname]);
  // Fill task label  input on select task type function.
  const prefillTaskLabel = (value) => {
    let fieldTitle = form.getFieldValue("title");
    let selectedType = tasksTypes?.find(
      (element) => Number(element?.id) === Number(value)
    );
    let doesLabelExistIntypesList = tasksTypes?.some(
      (element) => element?.label === fieldTitle
    );
    if (!fieldTitle || doesLabelExistIntypesList) {
      form.setFieldsValue({
        title: selectedType?.label,
      });
    }
  };

  // Reset drawer form.
  const resetDrawerForm = () => {
    setSingleTaskData({});
    setCheckedItems([]);
    setCheckedFollowers([]);
    setTaskToUpdate(null);
    form.resetFields();
    form.setFieldsValue({
      startTime: "",
      startDate: "",
      endDate: "",
      endTime: "",
      family: null,
      related_element: null,
    });
    setSelectedStartDate("");
    setSelectedEndDate("");
    setSelectedStartTime("");
    setSelectedEndTime("");
    setAddOnsValues({
      description: "",
      note: "",
    });
    setFiles([]);
    setCheckDueDateReminder(false);
    setIsError(false);
    setGuestsSearchQuery("");
    setFollowersSearchQuery("");
    setReminderValidator(false);
  };

  // Get types API.
  const getTasksTypes = async () => {
    try {
      const response = await MainService.getTasksTypes();
      setTasksTypes(response?.data?.data?.tasks_type);
    } catch (error) {
      console.log(`Error ${error}`);
      toastNotification("error", t("toasts.somethingWrong"));
    }
  };
  const getTags = async () => {
    try {
      const response = await MainService.getTagsTasks();
      setTags(
        response?.data.map((el) => ({
          label: el.label,
          // label: (
          //   <div className="flex items-center gap-x-1">
          //     <span>
          //       <ChoiceIcons icon={el.icon} color={el.color} />
          //     </span>{" "}
          //     <span>{el.label}</span>
          //   </div>
          // ),
          value: el.id,
          icon: <ChoiceIcons icon={el.icon} />,
        }))
      );
    } catch (err) {}
  };
  // Get tasks API.
  const getTasks = useCallback(
    async (param) => {
      // Destructure `filters` for easier access
      const filters = tasksFilters?.filters || {};
      // Derive variables with fallback values using the nullish coalescing operator (??)
      const priority = filters?.priority?.toString() ?? "";
      const activePipeline =
        filters?.pipeline?.toString() ?? stageIdToFilter?.toString();
      const FilteredPipeline = selectedPipeline ?? 0;
      const search_param = param?.toString() ?? searchParams?.toString() ?? "";
      // Simplify date logic handling
      const start = dateFilter
        ? dateFilter[0].toUpperCase()
        : selectFutureActivities
        ? dayjs().startOf("day").format(user?.location?.date_format)
        : "";
      // Set 'end' date based on the future activities flag
      const end =
        !selectFutureActivities && dateFilter
          ? dateFilter[1].toUpperCase()
          : "";
      // Construct the payload with all the necessary values
      const payload = new FormData();
      payload.append("limit", limit);
      payload.append("page", pageNumber);
      payload.append("search", debouncedSearchValue);
      payload.append("stages_ids", activePipeline);
      payload.append("pipeline_id", FilteredPipeline);
      payload.append("priorities", priority);
      payload.append("task_type_id", filterTable ?? "");
      payload.append("user_id", selectedUser?.id?.toString() || user?.id);
      payload.append("roles", selectedRoles?.toString());
      payload.append("start", start);
      payload.append("end", end);
      if (Array.isArray(selectedFamily) && selectedFamily?.length === 2) {
        payload.append("family_id", selectedFamily[0]?.toString());
        payload.append("related_element", selectedFamily[1]?.toString());
      }
      if (Array.isArray(selectedTags) && selectedTags?.length > 0) {
        selectedTags.forEach((id) => {
          payload.append("tags_ids[]", id);
        });
      }
      payload.append("search_param", search_param);
      payload.append("type", dateSegmented === 3 ? "" : dateSegmented);
      payload.append("logic", filterCondition);
      if (sorter?.sort_field) payload.append("sort_field", sorter?.sort_field);
      if (sorter?.sort_direction)
        payload.append("sort_direction", sorter?.sort_direction);

      // Set the loading state
      setLoadTasks(true);
      try {
        const response = await MainService.getTasks(payload);
        setTasksData(response?.data?.data);
        setTotal(response?.data?.meta?.total);
        getTasksError && setGetTasksError(false);
        if (appliedFilters && appliedFilters?.length) {
          setTotalFiltered(response?.data?.meta?.total);
        } else {
          setTotalFiltered(null);
        }
        setFetchTasks(true);
      } catch (error) {
        console.error(`Error ${error}`);
        setGetTasksError(true);
        toastNotification("error", t("toasts.somethingWrong"));
      } finally {
        setLoadTasks(false);
      }
    },
    [
      limit,
      pageNumber,
      stageIdToFilter,
      selectedPipeline,
      tasksFilters?.filters?.priority,
      tasksFilters?.filters?.pipeline,
      filterTable,
      debouncedSearchValue,
      selectedRoles,
      dateFilter,
      selectedFamily,
      isUserNotified,
      selectFutureActivities,
      dateSegmented,
      filterCondition,
      selectedUser?.id,
      selectedTags,
      sorter?.sort_field,
      sorter?.sort_direction,
    ]
  );
  // Retrigger get activities API on change search parameters. (Only when the search is activated)
  useEffect(() => {
    if (search && searchParams) {
      getTasks(searchParams);
    }
    // return () => setTasksData([]);
  }, [searchParams]);

  //After closing the modal overview, re-trigger getTasks api if the user performed any changes
  //to update the table.
  useEffect(() => {
    let isMounted = true;
    if (isMounted) {
      if (switchViews === "Table" && countChanges > 0 && !openActivity360) {
        getTasks();
        setCountChanges(0);
      }
    }
    return () => {
      isMounted = false;
    };
  }, [countChanges, openActivity360, switchViews]);

  // Helper function to construct task object
  const constructTaskObject = (taskData) => ({
    id: taskData?.id,
    tasks_type_id: taskData?.tasks_type_id,
    title: taskData?.label,
    start: formatDates(user, taskData?.start_date, taskData?.start_time),
    end: formatDates(user, taskData?.end_date, taskData?.end_time),
    display: "block",
    overlap: taskData?.owner_id?.id === user?.id || taskData?.creator?.id,
    backgroundColor: getPriorityColor(taskData?.priority),
    borderColor: getPriorityColor(taskData?.priority),
    icon: taskData?.tasks_type_id,
    element_label: taskData?.element_label,
    family_label: taskData?.family_label,
    reminder: taskData?.reminder,
    is_overdue: taskData?.is_overdue,
    is_final_stage: taskData?.is_final_stage,
    is_follower: taskData?.is_follower,
    upload: taskData?.upload,
    stage_id: taskData?.stage_id,
    priority: taskData?.priority,
    files: taskData?.files,
    followers: taskData?.followers,
    owner_id: taskData?.owner_id,
    guests: taskData?.guests,
    label: taskData?.label,
    end_date: taskData?.end_date,
    end_time: taskData?.end_time,
    start_date: taskData?.start_date,
    start_time: taskData?.start_time,
    pipeline_label: taskData?.pipeline_label,
    stage_label: taskData?.stage_label,
  });

  // Helper function to update columns for the Kanban view
  const updateColumnsForKanban = (taskData, updatedTaskId = null) => {
    const targetStage = columns?.find(
      (column) => Number(column?.stage_id) === Number(taskData?.stage_id)
    );
    if (targetStage) {
      const updatedColumn = columns.map((column) => {
        if (column?.stage_id === taskData?.stage_id) {
          // Check if we are updating an existing task
          if (updatedTaskId) {
            return {
              ...column,
              elements: column?.elements?.map((element) =>
                element?.id === updatedTaskId
                  ? { ...element, ...taskData }
                  : element
              ),
            };
          }
          // Add new task if it's not an update
          return {
            ...column,
            elements: column?.elements
              ? [taskData, ...column?.elements]
              : [taskData],
          };
        }
        return column;
      });
      setColumns(updatedColumn);
    }
  };

  // Helper function to update events for the Calendar view
  const updateEventsForCalendar = (taskData, taskId = null) => {
    const newTask = constructTaskObject(taskData);
    if (taskId) {
      // If taskId is provided, we are updating an existing task
      const itemIndex = events?.findIndex((element) => element?.id === taskId);
      if (itemIndex !== -1) {
        const updatedEvents = events?.map((element, i) =>
          i === itemIndex ? { ...element, ...newTask } : element
        );
        setEvents(updatedEvents);
      }
    } else {
      // If taskId is not provided, this is a new task (create)
      setEvents([newTask, ...events]);
    }
    dispatch(setOpenTaskDrawer(false));
  };

  // Error handling function
  const handleCreateTaskError = (error) => {
    setCreateAndUpdateTaskLoading(false);
    if (error?.response?.status === 422) {
      const errorMessages = {
        "The element id field is required when family id is present.": t(
          "tasks.requiredElementOnSelectModule"
        ),
        "The family id field is required when element id is present.": t(
          "tasks.requiredModuleOnSelectElement"
        ),
        "The element does not belong to the family provided!": t(
          "tasks.elementNotBelongingToSelectedModule"
        ),
        "Localization error": (
          <div className="flex w-full flex-col pl-2">
            <p
              className="mt-0.5"
              dangerouslySetInnerHTML={{
                __html: t("tasks.configureLocalizationNotif"),
              }}
            />
            <Button
              type="link"
              className="ml-auto w-full"
              onClick={() => navigate(`/profile/localization`)}
            >
              {t("common:tasks.GoToLocalization")}
              <RightCircleOutlined style={{ fontSize: "1rem" }} />
            </Button>
          </div>
        ),
      };
      const errorMessage =
        errorMessages[error?.response?.data?.errors[0]] ||
        error?.response?.data?.errors[0];
      toastNotification(
        error?.response?.data?.errors[0] === "Localization error"
          ? "warning"
          : "error",
        errorMessage
      );
    } else if (error?.response?.status === 418) {
      toastNotification("error", t("tasks.addPipelineError"));
    } else {
      toastNotification("error", t("toasts.somethingWrong"));
      console.log(`Error ${error}`);
    }
  };

  // Create task API.
  const createTask = async (payload) => {
    try {
      setCreateAndUpdateTaskLoading(true);
      const response = await MainService.createNewTask(payload);
      if (response?.data?.success) {
        setCreateAndUpdateTaskLoading(false);
        dispatch(setOpenTaskDrawer(false));
        toastNotification("success", t("tasks.addActivity"), "bottomRight");
        if (switchViews === "Calendar") {
          updateEventsForCalendar(response?.data?.data);
        } else if (switchViews === "Kanban") {
          updateColumnsForKanban(response?.data?.data);
        } else {
          getTasks();
        }
        setUpdateFilters((prev) => prev + 1);
      }
      resetDrawerForm();
    } catch (error) {
      handleCreateTaskError(error);
    }
  };

  // Update task API.
  const updateTask = async (taskId, payload) => {
    try {
      setCreateAndUpdateTaskLoading(true);
      const response = await MainService.updateTask(taskId, payload);
      if (response?.data?.success) {
        setCreateAndUpdateTaskLoading(false);
        if (switchViews === "Calendar") {
          updateEventsForCalendar(response?.data?.data, taskId);
        } else if (switchViews === "Kanban") {
          updateColumnsForKanban(response?.data?.data, taskId);
        } else {
          getTasks();
          setId("");
          setTaskToUpdate("");
        }
        setFilterTable("");
        dispatch(setOpenTaskDrawer(false));
        resetDrawerForm();
      }
    } catch (error) {
      handleCreateTaskError(error);
    }
  };

  // Trigger after submitting the form and verifying data successfully
  const onFinish = (values) => {
    let formData = new FormData();
    const fieldsValues = form.getFieldsValue(true);
    formData.append("label", values?.title);
    formData.append(
      "priority",
      values?.priority ?? fieldsValues?.priority ?? ""
    );
    formData.append(
      "Reminder",
      values?.reminder && values?.addonAfter
        ? `${values?.reminder} ${values?.addonAfter}`
        : ""
    );
    formData.append(
      "start_date",
      formatDatePayload(values?.startDate, user?.location?.date_format)
    );
    formData.append(
      "start_time",
      formatTimePayload(values?.startTime, user?.location?.time_format)
    );
    formData.append(
      "end_time",
      formatTimePayload(values?.endTime, user?.location?.time_format)
    );
    formData.append(
      "end_date",
      formatDatePayload(values?.endDate, user?.location?.date_format)
    );
    formData.append(
      "owner_id",
      typeof values?.owner === "object" ? values?.owner?.value : values?.owner
    );
    formData.append("code", values?.code || "");
    formData.append("department_id", values?.department_id || "");

    if (values.exam_id) {
      values.exam_id.forEach((id) => {
        formData.append("exam_id[]", id);
      });

      // formData.append("exam_id", values?.exam_id);
    }
    if (Array.isArray(values.tags_ids) && values?.tags_ids?.length > 0) {
      values?.tags_ids?.forEach((el) => formData.append("tags_ids[]", el));
    }
    let result = usersList?.filter((user) =>
      checkedItems?.some((list) => user?.id === list?.id)
    );
    let filteredArray = checkedItems?.filter(
      (el) => typeof el?.id === "string"
    );
    let allTogetherArray = [...result, ...filteredArray];
    location.pathname === "/chat"
      ? allTogetherArray?.forEach((el) => {
          const key = typeof el?.id === "string" ? "id" : "uuid";
          const value = el?.[key];
          formData.append(
            "guests[]",
            JSON.stringify({ [key === "id" ? "mid" : "uid"]: value })
          );
        })
      : checkedItems?.forEach((el) => formData.append("guests[]", el?.id));
    checkedFollowers?.forEach((el) => formData.append("followers[]", el?.id));
    formData.append("tasks_type_id", values?.taskType);
    formData.append("description", addOnsValues?.description ?? "");
    formData.append("note", addOnsValues?.note ?? "");

    formData.append("stage_id", values?.stage ?? "");
    formData.append("reminder_before_end", checkDueDateReminder ? 1 : 0);
    formData.append("send_email", emailNotification ? 1 : 0);
    formData.append(
      "send_message_system",
      values?.notificationsSettings?.includes("systemMessage") ? 1 : 0
    );

    formData.append("location", values?.location ?? "");
    (values?.family || fieldsValues?.family) &&
      formData.append("family_id", values?.family || fieldsValues?.family);
    (values?.relatedElement || fieldsValues?.relatedElement) &&
      formData.append(
        "element_id",
        values?.relatedElement || fieldsValues?.relatedElement
      );

    files?.length > 0 &&
      files?.forEach((file) => formData.append("upload[]", file?.id));

    msgTask !== "" && formData.append("source", 1);

    // Trigger the create or the update API.
    if (Object.keys(singleTaskData).length === 0) {
      createTask(formData);
    } else if (Object.keys(singleTaskData).length > 0 && taskToUpdate) {
      updateTask(taskToUpdate, formData);
    }
    dispatch(setMsgTask(""));
  };

  //Change api getAllPipelines by getPipelinesByFamily with family id 7 (Project)
  const getPipelines = async () => {
    try {
      // setLoadPipelines(true);
      const response = await MainService.getPipelinesByFamilyTask();
      setSelectedPipeline(
        (switchViews === "Kanban" || switchViews === "List") &&
          selectedPipeline === 0 &&
          userPreference?.isSelectPipelineActive === false
          ? response?.data?.data[0]?.id
          : selectedPipeline
      );
      setPipelines(response?.data?.data);
      // setLoadPipelines(false);
    } catch (error) {
      // setLoadPipelines(false);
      toastNotification("error", t("toasts.somethingWrong"));
      console.log(`Error ${error}`);
    }
  };

  // Trigger notifications api.
  const getNotifications = useCallback(async () => {
    try {
      if (filterNotifications !== 2 && filterNotifications !== null) {
        setLoadNotifs(true);
        const response = await MainService.getNotifications(
          notificationsPage,
          "",
          filterNotifications
        );
        if (notificationsPage > 1) {
          setNotifications([...notifications, ...response?.data?.data]);
        } else {
          setNotifications(response?.data?.data);
          setLastNotificationsPage(response?.data?.meta?.last_page);
          // setUnreadNotifications(response?.data?.count);
        }
        setLoadNotifs(false);
      }
    } catch (error) {
      console.log(`Error ${error}`);
      setLoadNotifs(false);
    }
  }, [notificationsPage, filterNotifications]);

  // Mark single notification as read.
  const markNotificationAsRead = async (type, payload) => {
    try {
      const response = await MainService.markNotificationAsRead(payload);
      if (response?.status === 200) {
        const res = await MainService.getNumberOfNotifications();

        if (type === "notification") {
          getNotifications();
          dispatch(setTotalNotificationsNumber(totalNotificationNumber - 1));
          dispatch(setCountNotificationVisio(res.data.visio_count));
        } else if (type === "reminder") {
          let reminder =
            remindersList?.data &&
            remindersList?.data?.find((item) => item?.id === payload?.log_id);
          if (reminder) {
            dispatch(
              setRemindersArray({
                ...remindersList,
                meta: {
                  ...remindersList?.meta,
                  total: remindersList?.meta.total - 1,
                },
                data: remindersList?.data?.filter(
                  (el) => el?.id !== reminder?.id
                ),
              })
            );
            dispatch(
              setCountReminders(res?.data?.reminders_visio?.meta?.total)
            );
            dispatch(setRemindersList(res?.data?.reminders_visio));
            //update remindersListVisio
          }
        }
      }
    } catch (error) {
      console.log(`Error ${error}`);
    }
  };

  // Get Specific Task by Id API.
  const getSpecificTask = useCallback(async () => {
    try {
      setLoadSpecificTask(true);
      const response = await MainService.getSpecificTask(taskToUpdate);
      if (response?.data?.message === "This task was deleted") {
        setDeletedTaskIndicator(true);
      } else if (response?.data?.message === "Task not found") {
        setNotFoundTaskIndicator(true);
      } else {
        setSingleTaskData(response?.data?.data);
        setCheckedItems(
          response?.data?.data?.guests != null
            ? response?.data?.data?.guests
            : checkedItems
        );
        setCheckedFollowers(
          response?.data?.data?.followers !== null
            ? response?.data?.data?.followers
            : checkedFollowers
        );
        setAddOnsValues({
          ...addOnsValues,
          note: response?.data?.data?.note,
          description: response?.data?.data?.description,
        });
        setFiles(response?.data?.data?.upload);
      }
    } catch (error) {
      console.log(`Error ${error}`);
      toastNotification("error", t("toasts.somethingWrong"));
    } finally {
      setLoadSpecificTask(false);
      if (redirectActivityId) {
        localStorage.removeItem("redirect-activity-id");
      }
    }
  }, [taskToUpdate]);
  // On search in owners/followers list, setPages to 1
  useEffect(() => {
    setFollowersListPage(1);
    // const scrollableDiv = document.getElementById("scrollableFollowersDiv");
    // if (scrollableDiv) {
    //   scrollableDiv.scrollTo({ top: 0, behavior: "smooth" });
    // }
  }, [debounceFollowersSearch]);

  // Get owners/followers API.
  const getOwners = useCallback(
    async (signal) => {
      try {
        setLoadOwners(true);
        let formData = new FormData();
        formData.append("family_id", 4);
        formData.append("search", debounceFollowersSearch);
        formData.append("guest", 0);
        const response = await MainService.getFamilyOptions(
          followersListPage,
          200,
          formData
        );
        setOwnersList(
          followersListPage > 1
            ? [...ownersList, ...response?.data?.data]
            : response?.data?.data
        );
        setTotalEntities((prev) => ({
          ...prev,
          colleagues: response?.data?.meta?.total,
        }));
        setFollowersListLastPage(response?.data?.meta?.last_page);
        setLoadOwners(false);
      } catch (error) {
        setLoadOwners(false);
        console.log(`Error ${error}`);
        toastNotification("error", t("toasts.somethingWrong"));
      }
    },
    [debounceFollowersSearch, followersListPage]
  );

  const getDoctors = useCallback(
    async (signal) => {
      try {
        setLoadDoctors(true);
        const response = await fetchPhoneBook(
          "",
          pageDoctors,
          200,
          debounceDoctorsSearch,
          "",
          1
        );
        setDoctors(
          pageDoctors > 1
            ? [...doctors, ...response?.data?.data]
            : response?.data?.data
        );
        setTotalEntities((prev) => ({
          ...prev,
          doctors: response?.data?.meta?.total,
        }));
        setDoctorsListLastPage(response?.data?.meta?.last_page);
        setLoadDoctors(false);
      } catch (error) {
        setLoadDoctors(false);
        console.log(`Error ${error}`);
        toastNotification("error", t("toasts.somethingWrong"));
      }
    },
    [debounceDoctorsSearch, pageDoctors]
  );

  // Get owners/followers on mount.
  useEffect(() => {
    let abort = new AbortController();
    getOwners(abort?.signal);
    return () => abort?.abort();
  }, [getOwners]);

  // useEffect(() => {
  //   let abort = new AbortController();
  //   getDoctors(abort?.signal);
  //   return () => abort?.abort();
  // }, [getDoctors]);

  // On search in guests list, scroll to top and setPages to 1
  useEffect(() => {
    setGuestsListPage(1);
    const scrollableDiv = document.getElementById("scrollableParticipantsDiv");
    if (scrollableDiv) {
      scrollableDiv.scrollTo({ top: 0, behavior: "smooth" });
    }
  }, [debounceGuestsSearch]);

  // Get guests API.
  const getGuests = useCallback(async () => {
    try {
      let formData = new FormData();
      formData.append("family_id", selectedFamilyMembers.toString());
      formData.append("search", debounceGuestsSearch);
      setLoadGuests(true);
      const response = await MainService.getFamilyOptions(
        guestsListPage,
        50,
        formData
      );
      setGuestsList(
        guestsListPage > 1
          ? [...guestsList, ...response?.data?.data]
          : response?.data?.data
      );
      setTotalEntities((prev) => ({
        ...prev,
        all: response?.data?.meta?.total,
      }));
      setGuestsListLastPage(response?.data?.meta?.last_page);
      setLoadGuests(false);
    } catch (error) {
      console.log(`Error ${error}`);
      toastNotification("error", t("toasts.somethingWrong"));
      setLoadGuests(false);
    }
  }, [guestsListPage, debounceGuestsSearch, selectedFamilyMembers]);

  // Get guests on mount.
  useEffect(() => {
    let abort = new AbortController();
    getGuests();
    return () => abort?.abort();
  }, [getGuests, guestsListPage, debounceGuestsSearch]);

  // Get activities room api.
  const getActivitiesRoom = async () => {
    try {
      const response = await MainService.getActivitiesRoomNotifications();
      setRoomsMessages(response?.data?.unread_msg_room);
      setTotalUnreadMessages(response?.data?.total_all_messages_room_count);
      dispatch(setActivitiesMessages(response?.data));
    } catch (error) {
      console.log(`Error ${error}`);
    }
  };

  // Retrieve pipelines/activities room on 1st component mount.
  useEffect(() => {
    getPipelines();
    getActivitiesRoom();
    getTasksTypes();
    getTags();
    dispatch({
      type: SET_CONTACT_INFO_FROM_DRAWER,
      payload: {},
    });
    return () => {
      dispatch(clearSearchInput());
    };
  }, []);

  //Update activity details when the modal/drawer is already open and with the same activity id.
  useEffect(() => {
    if (
      isUserNotified &&
      Object.keys(singleTaskData)?.length > 0 &&
      singleTaskData?.id === taskNotifPayload?.id
    ) {
      getSpecificTask();
    }
  }, [isUserNotified, taskNotifPayload?.id]);

  //Update view in realtime according mercure event.
  useEffect(() => {
    if (
      taskNotifPayload !== null &&
      taskNotifDescription !== null &&
      isUserNotified &&
      taskNotifActionType !== "activity_room" &&
      taskNotifDescription?.action !== "removed" &&
      taskNotifActionType !== "delete_task"
    ) {
      dispatch(setTotalNotificationsNumber(totalNotificationNumber + 1));
      setNotifications([
        {
          id_data: taskNotifDescription?.id,
          user: taskNotifDescription?.initiator?.label,
          action: taskNotifDescription?.action,
          avatar: taskNotifDescription?.initiator?.avatar,
          read: 0,
          logId: taskNotifDescription?.logId,
          created_at: dayjs(taskNotifDescription?.date).format(
            `YYYY-MM-DD HH:mm:ss`
          ),
        },
        ...notifications,
      ]);
      setUpdateFilters((prev) => prev + 1);
    } else if (
      taskNotifActionType === "activity_room" ||
      taskNotifActionType === "delete_task"
    ) {
      getActivitiesRoom();
      dispatch({
        type: ADD_TASK_NOTIFICATION_ACTION_TYPE,
        taskNotifActionType: null,
      });
      setUpdateFilters((prev) => prev + 1);
    }
    if (
      taskNotifDescription?.action &&
      taskNotifDescription?.action === "removed" &&
      (openActivity360 || openTaskDrawer)
    ) {
      dispatch(setOpenTaskDrawer(false));
      setOpenActivity360(false);
      setUpdateFilters((prev) => prev + 1);
    }
  }, [isUserNotified, taskNotifPayload, taskNotifDescription, dispatch, openActivity360, openTaskDrawer, taskNotifActionType]);

  // Prevent trigger getNotifications api on chat.
  useEffect(() => {
    if (location.pathname !== "/chat") getNotifications();
  }, [getNotifications, notificationsPage, location.pathname]);

  //Get activities in Table view.
  useEffect(() => {
    if (switchViews === "Table") getTasks();
    /* return () => {
      setTasksData([]);
    }; */
  }, [getTasks, switchViews]);
  //Add reminder in realtime.
  useEffect(() => {
    if (taskNotifDescription?.taskId && taskNotifActionType === "reminder") {
      dispatch(
        addReminder({
          ...remindersList,
          data: [
            {
              id: taskNotifDescription?.taskId,
              logId: taskNotifDescription?.logId,
              action: taskNotifDescription?.action,
              read: 0,
              has_occured: 0,
            },
            ...remindersList?.data,
          ],
        })
      );
    }
  }, [dispatch, taskNotifDescription]);

  useEffect(() => {
    if (taskToUpdate) {
      getSpecificTask();
    }
  }, [getSpecificTask]);
  // Owners list.
  const ownersOptionsInSelect = ownersList
    ?.sort((a, b) => a?.label?.localeCompare(b?.label))
    ?.map((element) => ({
      name: getName(element?.label, "name"),
      label: (
        <>
          <div className="flex flex-row items-center">
            <AvatarChat
              fontSize="0.875rem"
              className="mr-1.5"
              size={28}
              height={17}
              width={17}
              url={`${
                URL_ENV?.REACT_APP_BASE_URL +
                URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
              }${element?.avatar}`}
              hasImage={EXTENSIONS_ARRAY?.includes(
                element?.avatar?.split(".")?.pop()
              )}
              name={getName(element?.label, "avatar")}
              type="user"
            />
            <p>
              {HighlightSearchW(
                getName(element?.label, "name"),
                followersSearchQuery
              )}
            </p>
          </div>
        </>
      ),
      value: element?.id,
    }));
  const doctorsOptionsInSelect = doctors?.map((element) => ({
    name: getName(element?.name, "name"),
    label: (
      <>
        <div className="flex flex-row items-center">
          <AvatarChat
            fontSize="0.875rem"
            className="mr-1.5"
            size={28}
            height={17}
            width={17}
            url={`${
              URL_ENV?.REACT_APP_BASE_URL + URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
            }${element?.image}`}
            hasImage={EXTENSIONS_ARRAY?.includes(
              element?.avatar?.split(".")?.pop()
            )}
            name={getName(element?.name, "avatar")}
            type="user"
          />
          <p>
            {HighlightSearchW(
              getName(element?.name, "name"),
              followersSearchQuery
            )}
          </p>
        </div>
      </>
    ),
    value: element?.id,
  }));

  // Update selected view in localStorage.
  useLayoutEffect(() => {
    if (preferredView !== switchViews) {
      localStorage.setItem("tasks-view", switchViews);
    }
  }, [switchViews]);

  // Prefill pipelines select in header
  useEffect(() => {
    if (
      switchViews === "Kanban" &&
      pipelines?.length > 0 &&
      userPreference?.isSelectPipelineActive === false
    ) {
      form.setFieldsValue({
        pipelineSelect: pipelines && pipelines[0]?.id,
      });
      setSelectedPipeline(
        pipelines && selectedPipeline === 0
          ? pipelines[0]?.id
          : selectedPipeline
      );
    }
    // if (selectedPipeline === 0) getTasks();
  }, [form, switchViews, pipelines, userPreference]);

  // Handle open advanced filter with shortcut.
  useKeyDown(71, true, "keydown", () => {
    setOpenFilterMenu(!openFilterMenu);
  });

  //Format list of pipelines.
  const pipelinesOptions = useMemo(
    () => pipelines?.map(({ label, id }) => ({ label, value: id })),
    [pipelines]
  );

  // Change Function UpdateTaskStage from KanbanBoard to TasksWrapper to use it in TasksTableView
  const UpdateTaskStage = async (payload, source) => {
    try {
      setLoadTasks(true);
      await MainService.updateTaskStageInKanban(payload);
      getTasks();
      setLoadUpdateTaskStage(false);
    } catch (error) {
      console.log(`Error ${error}`);
      setLoadTasks(false);
      setLoadUpdateTaskStage(false);
    }
  };

  //Handle switch views segmented changes.
  const handleSegmentedChange = (value) => {
    setSwitchViews(value);
    dispatch(setSelectedViewInTask(value));
    setShowCardPopover({});
    if (
      !selectedPipelineIsActive &&
      userPreference?.isSelectPipelineActive === false
    ) {
      setSelectedPipeline(0);
    }
  };

  //Handle pipelines select in activities header.
  const handleSelectChange = (value) => {
    setPageNumber(1);
    setSelectedPipeline(value);
    setSelectedPipelineIsActive(value !== 0);
    if (value !== 0) {
      dispatch(
        setPipelinePreference({
          isSelectPipelineActive: true,
          preferredPipeline: value,
        })
      );
    } else {
      dispatch(setPipelinePreference({}));
    }
  };

  let defaultValues = { selectedPipeline, switchViews };

  // Mark all activities as read api.
  const markAllNotifsAsRead = async (type) => {
    try {
      setMarkAllAsReadLoading(true);
      const response = await MainService.markAllNotificationsAsRead("", type);
      if (response?.status === 200) {
        if (type === "task") {
          dispatch(
            setRemindersArray({
              ...remindersList,
              data: [],
              meta: { current_page: 1, from: 1, last_page: 1, total: 0 },
            })
          );
          dispatch(setCountReminders(0));
          dispatch(
            setRemindersList({
              ...remindersListVisio,
              meta: {
                ...remindersListVisio?.meta,
                current_page: 1,
                last_page: 1,

                total: 0,
              },
              data: [],
            })
          );
        } else if (type === "") {
          dispatch(setTotalNotificationsNumber(0));
          setNotifications(
            notifications &&
              notifications.map((notification) =>
                notification?.read === 0
                  ? { ...notification, read: 1 }
                  : notification
              )
          );
          dispatch(setCountNotificationVisio(0));
        }
      }
      setMarkAllAsReadLoading(false);
    } catch (error) {
      setMarkAllAsReadLoading(false);
      console.log(`Error ${error}`);
    }
  };

  //Export API
  const handleDownload = async () => {
    try {
      setLoadingExport(true);
      let priority = tasksFilters?.filters?.priority
        ? tasksFilters?.filters?.priority?.toString()
        : "";
      let activePipeline =
        tasksFilters?.filters?.pipeline &&
        tasksFilters?.filters?.pipeline !== null
          ? tasksFilters?.filters?.pipeline?.toString()
          : stageIdToFilter?.toString();
      let pp =
        selectedPipeline && selectedPipeline !== null ? selectedPipeline : 0;
      let payload = {
        search: debouncedSearchValue,
        stages_ids: activePipeline,
        pipeline_id: pp,
        priorities: priority,
        task_type_id: filterTable,
        export: 1,
        roles: selectedRoles?.toString(),
        start: dateFilter
          ? dateFilter[0]?.toUpperCase()
          : selectFutureActivities === true
          ? dayjs(dayjs().startOf("day")).format(user?.location?.date_format)
          : "",
        end:
          !selectFutureActivities && dateFilter
            ? dateFilter[1]?.toUpperCase()
            : "",
        family_id: selectedFamily ? selectedFamily : "",
        type: dateSegmented === 3 ? "" : dateSegmented,
        search_param: search,
      };
      const response = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).post(`tasks/get/table`, payload, {
        responseType: "blob",
      });
      const url = window.URL.createObjectURL(new Blob([response?.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "Activities.xlsx");
      document.body.appendChild(link);
      link.click();
      setLoadingExport(false);
    } catch (error) {
      setLoadingExport(false);
      toastNotification("error", t("toasts.somethingWrong"));
      console.log("Export error " + error);
    }
  };

  //Handle deselect roles from select (advanced filter)
  const handleResetRolesSelect = () => {
    setSelectedRoles([]);
    setAppliedFilters(appliedFilters.filter((el) => el !== "selectRoles"));
  };

  // Clear all filters (advanced filter footer).
  const clearFilters = () => {
    setAppliedFilters([]);
    if (followersSearchQuery) {
      setOwnersList([
        ...ownersList,
        { label: user?.label, avatar: user?.avatar, id: user?.id },
      ]);
    }
    setFollowersSearchQuery("");
    filtersForm.resetFields();

    filtersForm.setFieldsValue({
      filterCondition: "and",
      selectRoles: [0, 1, 2, 3],
      user: user?.id,
    });
    setSelectedUser({ id: user.id, name: user.label });
    setAppliedFilters([{ selectRoles: [0, 1, 2, 3], user: user?.id }]);
    setSelectedRoles([0, 1, 2, 3]);
    setFilterTable("");
    setFilterTable("");
    setDateFilter(null);
    setSelectedFamily([]);
    setSelectedTags([]);
    dispatch(setTasksFilters({ ...tasksFilters, filters: { priority: null } }));
    setTotalFiltered(null);
  };
  // Close filter tag.
  const handleCloseTag = (filterParameter) => {
    let param = Object.keys(filterParameter)[0];
    setAppliedFilters(
      [...appliedFilters].filter((el) => el.hasOwnProperty(param) === false)
    );
    switch (param) {
      case "selectRoles":
        setSelectedRoles([]);
        filtersForm.setFieldsValue({
          selectRoles: [0, 1, 2, 3],
        });
        break;
      case "types":
        setFilterTable("");
        filtersForm.setFieldsValue({
          types: "",
        });
        break;
      case "filterByDate":
        filtersForm.setFieldsValue({
          filterByDate: [],
        });
        setDateFilter(null);
        break;
      case "filterByModule":
        filtersForm.setFieldsValue({
          filterByModule: undefined,
        });
        setSelectedFamily(null);
        break;
      case "priorityFilter":
        dispatch(
          setTasksFilters({ ...tasksFilters, filters: { priority: null } })
        );
        filtersForm.setFieldsValue({
          priorityFilter: undefined,
        });
        break;
      case "filterFromTodayOn":
        filtersForm.setFieldsValue({
          filterFromTodayOn: "all",
        });
        break;
      case "userName":
        filtersForm.setFieldsValue({
          user: user?.id,
        });
        setSelectedUser({ id: user?.id, name: user?.label });
        setAppliedFilters((prev) =>
          prev.map((item) => {
            if (item.user !== undefined) {
              return { ...item, user: user?.id };
            }
            return item;
          })
        );
        setMeMode(true);
        break;
      case "module":
        setSelectedFamily([]);
        setAppliedFilters((prev) =>
          prev.map((item) => {
            if (item.related_element !== undefined) {
              return { ...item, related_element: [] };
            }
            return item;
          })
        );
        filtersForm.setFieldsValue({
          related_element: [],
        });

        break;

      default:
        break;
    }
  };

  // Open activity details on click on 'go to' link in notification toast.
  useEffect(() => {
    if (redirectActivityId) {
      setTaskToUpdate(redirectActivityId);
      // dispatch(setOpenTaskDrawer(true));
      setOpenActivity360(true);
    }
  }, [dispatch, redirectActivityId]);

  // Persist show/hide table columns in localstorage.
  useEffect(() => {
    localStorage.setItem("columns-filter", JSON.stringify(checkedColumns));
  }, [checkedColumns]);

  // Re-trigger activities kpi whenever filters changes.
  useEffect(() => {
    const getKpis = async () => {
      try {
        setLoadKpis(true);
        const response = await MainService.activitiesKpi();
        setKpisList(response?.data);
        setLoadKpis(false);
      } catch (error) {
        console.log(`Error ${error}`);
        setLoadKpis(false);
      }
    };
    getKpis();
  }, [updateFilters]);

  // dispatch filters reducer whenever appliedFilters changes
  useEffect(() => {
    dispatch(setActiveTasksFilters(appliedFilters));
  }, [dispatch, appliedFilters]);

  // Hide toast notification when activity overview modal is open.
  useEffect(() => {
    const adjustNotificationZIndex = () => {
      const notifications = document.querySelectorAll(
        ".ant-notification.ant-notification-topRight.ant-notification-stack.ant-notification-stack-expanded"
      );
      notifications.forEach((notif) => {
        notif.style.zIndex = openActivity360 ? "12" : "";
      });
    };
    if (openActivity360) {
      adjustNotificationZIndex();
      // Observe changes to dynamically adjust z-index for new notifications
      const observer = new MutationObserver(adjustNotificationZIndex);
      const targetNode = document.body;
      const config = { childList: true, subtree: true };
      observer.observe(targetNode, config);
      return () => {
        observer.disconnect();
      };
    }
  }, [openActivity360, isUserNotified]);

  // Display notifications count.
  const handleShowNotificationsNumber = () => {
    let remindersSum = 0;
    let sum = 0;
    if (remindersList?.meta) {
      remindersSum = remindersList?.meta?.total; //change remindersList?.length by remindersList?.meta?.total
    } else {
      remindersSum = remindersList?.length;
    }
    sum =
      remindersSum +
      totalNotificationNumber +
      calculateSum(activitiesMessages?.unread_msg_room);
    return sum > 0 ? sum : 0;
  };

  // Dispatch isOverviewModalOpen in redux to enable/disable chat popover.
  useEffect(() => {
    if (openActivity360 === true) {
      dispatch(isOverviewModalOpen(true));
    } else {
      dispatch(isOverviewModalOpen(false));
    }
  }, [openActivity360, dispatch]);

  // Handle click on chat icon on top right of the drawer.
  const handleOpenChatRoomFromDrawer = () => {
    dispatch(closeDrawerChat());
    dispatch(setOpenTaskRoomDrawer(true));
    setRoomActivityId(taskToUpdate);
  };

  // Handle side effects on close the activity drawer.
  const handleOpenCloseDrawerEffect = (open) => {
    if (open === false) {
      dispatch(setOpenTaskDrawer(false));
      resetDrawerForm();
      setActiveTabKey("generalInfoActivities");
      setActivityLabel("");
      setShowCardPopover({});
      if (redirectActivityId) {
        localStorage.removeItem("redirect-activity-id");
      }
    }
    setShowNotificationsMenu(false);
    setNotFoundTaskIndicator(false);
  };

  // Handle close drawer.
  const handleCloseDrawer = (e) => {
    e && e?.stopPropagation();
    dispatch(setOpenTaskDrawer(false));
    setId("");
    setDeletedTaskIndicator(false);
    setNotFoundTaskIndicator(false);
    dispatch(setMsgTask(""));
    setSelectedStageId(null);
  };

  // Handle show/hide table column.
  const handleCheckDisplayedColumns = (e) =>
    setCheckedColumns(
      e?.target?.checked
        ? allColumns.map((el) => el?.value)
        : allColumns.filter((el) => el?.disabled).map((e) => e?.value)
    );

  // Handle change dates segmented.
  const changeTab = (e) => {
    setDateSegmented(e);
    // Check if 'dateType' already exists in the active filters
    let updatedFilters = activeFilters?.map((item) => {
      if (item?.hasOwnProperty("dateType")) {
        // If found, update 'dateType' with the new value
        return { ...item, dateType: e };
      }
      return item;
    });
    // If no 'dateType' was found in the existing filters, add it
    let itemExist = activeFilters?.some((item) =>
      item?.hasOwnProperty("dateType")
    );
    if (!itemExist) {
      updatedFilters = [...updatedFilters, { dateType: e }];
    }
    // Dispatch the updated filters
    dispatch(setActiveTasksFilters(updatedFilters));
  };

  // Handle select meMode from the header.
  const handleSelectMeMode = () => {
    let isMeModeActive = appliedFilters?.find((item) =>
      item?.hasOwnProperty("selectRoles")
    );
    if (isMeModeActive && isMeModeActive?.selectRoles?.some((el) => el === 1)) {
      setSelectedRoles(selectedRoles?.filter((el) => el !== 1));
      setAppliedFilters((prevFilters) => {
        return prevFilters
          ?.map((item) => {
            // Check if the item has 'selectedRoles' and contains role '1'
            if ("selectRoles" in item && item?.selectRoles?.includes(1)) {
              // If 'selectedRoles' has more than just '1', remove '1'
              if (item.selectRoles?.length > 1) {
                return {
                  ...item,
                  selectRoles: item?.selectRoles?.filter((el) => el !== 1),
                };
              }
              // If 'selectedRoles' contains only '1', return null to filter it out
              return null;
            }
            return item; // Return the item unchanged if it doesn't have '1'
          })
          .filter(Boolean); // Remove null entries (objects with only '1')
      });
    } else {
      setSelectedRoles([...selectedRoles, 1]);
      filtersForm.setFieldsValue({ user: user.id });
      setAppliedFilters((prevFilters) => {
        // Check if 'selectRoles' object exists in appliedFilters
        const existingFilter = prevFilters?.find((item) => item?.selectRoles);

        if (existingFilter) {
          // If 'selectRoles' exists, check if it already contains '1'
          if (!existingFilter?.selectRoles?.includes(1)) {
            return prevFilters.map((item) =>
              item?.selectRoles
                ? { ...item, selectRoles: [...item?.selectRoles, 1] } // Add '1' to selectRoles
                : item
            );
          } else {
            return prevFilters; // '1' already exists, no changes
          }
        } else {
          // If no 'selectRoles' object exists, add one
          return [...prevFilters, { selectRoles: [1] }];
        }
      });
    }
    setMeMode(!meMode);
  };

  return (
    <>
      <div className="px-2">
        {/* Header */}
        <Space
          direction="vertical"
          size="small"
          style={{
            display: "flex",
            padding: "20px 13px 0 13px",
          }}
        >
          {/* Stats */}
          <Row>
            {loadKpis ? (
              <div className="flex w-full flex-row justify-between gap-4">
                {Array.from({ length: 5 }).map((_, index) => (
                  <Skeleton.Input key={`skeleton_${index}`} active={true} />
                ))}
              </div>
            ) : (
              <KpiGrid kpisList={kpisList} />
            )}
          </Row>
          {/* Search input + segmented + pipelines + views switch + filter + notifications + export */}
          <Row
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              flexWrap: "nowrap",
              width: "100%",
            }}
          >
            <section className="relative flex w-[100%] grow flex-row items-center">
              <div className="mr-4" ref={Refs_IDs.searchActivity}>
                <SearchInTable
                  ref={searchInputRef}
                  showSearchShortcut={true}
                  searchParams={searchParams}
                  setSearchParams={setSearchParams}
                  setPageNumber={setPageNumber}
                  source="activity"
                  placeholder={t("companies.searchInfoInModuleInTable")}
                />
              </div>
              {switchViews === "Table" && (
                <div className="mr-4">
                  <Segmented
                    ref={Refs_IDs.choiceDateTask}
                    style={{
                      height: "32px",
                    }}
                    options={[
                      {
                        label: t("helpDesk.all"),
                        value: 3,
                      },
                      {
                        label: t("visio.today"),
                        value: 1,
                      },
                      {
                        label: t("visio.upComing"),
                        value: 2,
                      },
                      {
                        label: t("visio.history"),
                        value: 0,
                      },
                    ]}
                    value={dateSegmented}
                    onChange={changeTab}
                  />
                </div>
              )}
              <div className="mt-[0.7rem]">
                <NavHeader
                  view={switchViews}
                  source="tasks"
                  defaultValues={defaultValues}
                  handleSegmentedChange={handleSegmentedChange}
                  SelectOptions={
                    switchViews === "Kanban"
                      ? pipelinesOptions
                      : [
                          { label: t("tasks.allPipelines"), value: 0 },
                          ...pipelinesOptions,
                        ]
                  }
                  segmentedData={[
                    {
                      value: "Table",
                      icon: (
                        <Tooltip title={t("tasks.tableViewTooltip")}>
                          <TableOutlined />
                        </Tooltip>
                      ),
                    },
                    /* {
                      value: "List",
                      icon: (
                        <Tooltip title={t("tasks.listViewTooltip")}>
                          <UnorderedListOutlined />
                        </Tooltip>
                      ),
                    }, */
                    {
                      value: "Kanban",
                      icon: (
                        <Tooltip title="Kanban">
                          <div className="pt-0.5">
                            <MdOutlineTableChart className="text-[15px]" />
                          </div>
                        </Tooltip>
                      ),
                    },
                    {
                      value: "Calendar",
                      icon: (
                        <Tooltip title={t("tasks.calendarViewTooltip")}>
                          <CalendarOutlined />
                        </Tooltip>
                      ),
                    },
                  ]}
                  onSelectChange={handleSelectChange}
                />
              </div>
            </section>
            <div className="ml-1 flex flex-row items-center justify-between">
              <span ref={Refs_IDs.statsTasks}>
                <StatsDrawer familyId={"task"} />
              </span>
              <Tooltip
                trigger={["click", "hover"]}
                title={
                  <div className="flex items-center gap-x-1 ">
                    <span>{t("activities.filter")}</span>
                    {navigator.userAgent.indexOf("Macintosh") !== -1 ? (
                      <div className="flex items-center">
                        <BsCommand /> <span>G</span>
                      </div>
                    ) : (
                      "Ctrl + G"
                    )}
                  </div>
                }
              >
                <Button
                  ref={Refs_IDs.filterTasks}
                  icon={
                    <Badge
                      dot={
                        getRelevantFiltersCount(activeFilters, [
                          "dateType",
                          "filterCondition",
                        ]) > 0
                      }
                    >
                      <FilterOutlined
                        style={{ color: openFilterMenu ? "#fff" : "#000" }}
                      />
                    </Badge>
                  }
                  type={openFilterMenu ? "primary" : "text"}
                  shape="circle"
                  size="large"
                  onClick={() => setOpenFilterMenu(!openFilterMenu)}
                />
              </Tooltip>
              <Tooltip title={t("tasks.meMode")}>
                <Button
                  ref={Refs_IDs.meModeTasks}
                  icon={<UserOutlined />}
                  type={
                    selectedRoles?.some((el) => el === 1) &&
                    selectedUser?.id === user?.id
                      ? "primary"
                      : "text"
                  }
                  shape="circle"
                  size="large"
                  onClick={handleSelectMeMode}
                />
              </Tooltip>
              <Popover
                key={"notifications"}
                placement="bottomLeft"
                autoAdjustOverflow={true}
                destroyTooltipOnHide
                content={
                  <NotificationsPopover
                    notificationsList={notifications}
                    markNotificationAsRead={markNotificationAsRead}
                    setTaskToUpdate={setTaskToUpdate}
                    notificationsPage={notificationsPage}
                    setNotificationsPage={setNotificationsPage}
                    lastNotificationsPage={lastNotificationsPage}
                    setNotifications={setNotifications}
                    markAllNotifsAsRead={markAllNotifsAsRead}
                    source="notifications"
                    setFilterNotifications={setFilterNotifications}
                    filterNotifications={filterNotifications}
                    loadNotifs={loadNotifs}
                    setOpenActivity360={setOpenActivity360}
                    setShowNotificationsMenu={setShowNotificationsMenu}
                    roomsMessages={roomsMessages}
                    totalUnreadMessages={totalUnreadMessages}
                    markAllAsReadLoading={markAllAsReadLoading}
                    reminders={reminders}
                  />
                }
                trigger={["click"]}
                open={showNotificationsMenu}
                onOpenChange={(open) => {
                  setShowNotificationsMenu(open);
                }}
              >
                <div>
                  <Badge
                    count={handleShowNotificationsNumber()}
                    size="small"
                    style={{ top: "10px", right: "10px" }}
                  >
                    <Tooltip title="Notifications">
                      <Button
                        ref={Refs_IDs.notifsTasks}
                        type="text"
                        shape="circle"
                        size="large"
                        icon={<BellOutlined />}
                      />
                    </Tooltip>
                  </Badge>
                </div>
              </Popover>
              <Popover
                content={
                  <FilterTableCols
                    allColumns={allColumns}
                    checkedColumns={checkedColumns}
                    setCheckedColumns={setCheckedColumns}
                  />
                }
                placement="bottomLeft"
                arrow={true}
                trigger={["click"]}
                title={
                  <>
                    <div className="flex flex-row items-center justify-between">
                      <Checkbox
                        onChange={handleCheckDisplayedColumns}
                        indeterminate={
                          checkedColumns.length > 0 &&
                          checkedColumns.length < allColumns.length
                        }
                        checked={checkedColumns.length === allColumns.length}
                      >
                        {t("contacts.showAll")}
                      </Checkbox>
                      <span>
                        {t("tasks.showCols", {
                          displayed: checkedColumns?.length,
                          total: allColumns?.length,
                        })}
                      </span>
                    </div>
                    <Divider />
                  </>
                }
              >
                <Tooltip title={t("tasks.show/hide")}>
                  {switchViews === "Table" && (
                    <Button
                      ref={Refs_IDs.displayColumnsTasks}
                      icon={
                        <Badge
                          dot={checkedColumns.length !== allColumns?.length}
                        >
                          <ColumnWidthOutlined />
                        </Badge>
                      }
                      type="text"
                      shape="circle"
                      size="large"
                    />
                  )}
                </Tooltip>
              </Popover>
              <Tooltip title="Export Activities">
                <Button
                  ref={Refs_IDs.exportTasks}
                  icon={<UploadOutlined />}
                  onClick={handleDownload}
                  loading={loadingExport}
                  type="text"
                  shape="circle"
                  size="large"
                />
              </Tooltip>
            </div>
          </Row>
          {/* Active filter tags */}
          <Row
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              flexWrap: "wrap",
              width: "100%",
            }}
          >
            {appliedFilters?.length > 0 &&
              appliedFilters?.map((filter, i) => {
                return (
                  <FilterTags
                    key={Object.keys(filter)[0]}
                    parameter={filter}
                    handleCloseTag={handleCloseTag}
                    tasksTypes={tasksTypes}
                    tags={tags}
                    setSelectedTags={setSelectedTags}
                    setSelectedRoles={setSelectedRoles}
                    setAppliedFilters={setAppliedFilters}
                    filtersForm={filtersForm}
                  />
                );
              })}
          </Row>
        </Space>
        {/* Rendered view based on selection */}
        {switchViews === "Table" ? (
          <Suspense
            fallback={
              <Spin style={{ display: "flex", justifyContent: "center" }} />
            }
          >
            <TasksTableView
              t={t}
              form={form}
              tasksData={tasksData}
              setTasksData={setTasksData}
              tasksTypes={tasksTypes}
              loadTasks={loadTasks}
              setTaskToUpdate={setTaskToUpdate}
              ownersList={ownersList}
              guestsList={guestsList}
              showTime={showTime}
              setShowTime={setShowTime}
              setPageNumber={setPageNumber}
              setLimit={setLimit}
              total={total}
              setTotal={setTotal}
              pipelines={pipelines}
              UpdateTaskStage={UpdateTaskStage}
              loadUpdateTaskStage={loadUpdateTaskStage}
              setLoadUpdateTaskStage={setLoadUpdateTaskStage}
              stageIdToFilter={stageIdToFilter}
              setStageIdToFilter={setStageIdToFilter}
              selectedPipeline={selectedPipeline}
              getTasks={getTasks}
              getTasksError={getTasksError}
              setOpenElementDetails={setOpenElementDetails}
              setElementDetails={setElementDetails}
              checkedColumns={checkedColumns}
              setOpenActivity360={setOpenActivity360}
              pageNumber={pageNumber}
              setActivityLabel={setActivityLabel}
              setAppliedFilters={setAppliedFilters}
              setRoomActivityId={setRoomActivityId}
              setUpdateFilters={setUpdateFilters}
              appliedFilters={appliedFilters}
              checkedItems={checkedItems}
              guestsSearchQuery={guestsSearchQuery}
              setGuestsSearchQuery={setGuestsSearchQuery}
              guestsListPage={guestsListPage}
              setGuestsListPage={setGuestsListPage}
              guestsListLastPage={guestsListLastPage}
              setCheckedItems={setCheckedItems}
              checkedFollowers={checkedFollowers}
              setCheckedFollowers={setCheckedFollowers}
              setFollowersSearchQuery={setFollowersSearchQuery}
              followersSearchQuery={followersSearchQuery}
              followersListLastPage={followersListLastPage}
              totalEntities={totalEntities}
              setOwnersList={setOwnersList}
              followersListPage={followersListPage}
              setFollowersListPage={setFollowersListPage}
              loadOwners={loadOwners}
              loadGuests={loadGuests}
              setSelectedFamilyMembers={setSelectedFamilyMembers}
              limit={limit}
              setSorter={setSorter}
              sorterActivity={sorter}
            />
          </Suspense>
        ) : switchViews === "Calendar" ? (
          <Suspense
            fallback={
              <Spin style={{ display: "flex", justifyContent: "center" }} />
            }
          >
            <CalendarView
              setSelectedStartDate={setSelectedStartDate}
              setSelectedEndDate={setSelectedEndDate}
              setSelectedStartTime={setSelectedStartTime}
              setSelectedEndTime={setSelectedEndTime}
              setTasksData={setTasksData}
              setTaskToUpdate={setTaskToUpdate}
              filterTable={filterTable}
              events={events}
              setEvents={setEvents}
              tasksTypes={tasksTypes}
              selectedPipeline={selectedPipeline}
              selectedRoles={selectedRoles}
              setActivityLabel={setActivityLabel}
              selectedFamily={selectedFamily}
              setTotal={setTotal}
              setOpenActivity360={setOpenActivity360}
              openActivity360={openActivity360}
              countChanges={countChanges}
              setCountChanges={setCountChanges}
              switchViews={switchViews}
              activeStage={
                tasksFilters?.filters?.pipeline &&
                tasksFilters?.filters?.pipeline !== null
                  ? tasksFilters?.filters?.pipeline.toString()
                  : stageIdToFilter
              }
              activePriority={
                tasksFilters?.filters?.priority
                  ? tasksFilters?.filters?.priority.toString()
                  : ""
              }
              showCardPopover={showCardPopover}
              setShowCardPopover={setShowCardPopover}
              setUpdateFilters={setUpdateFilters}
              filterCondition={filterCondition}
              setRoomActivityId={setRoomActivityId}
              selectedTags={selectedTags}
              selectedUser={selectedUser}
            />
          </Suspense>
        ) : switchViews === "List" ? (
          <Suspense
            fallback={
              <Spin style={{ display: "flex", justifyContent: "center" }} />
            }
          >
            <TasksListView
              lists={columns}
              setLists={setColumns}
              selectedPipeline={selectedPipeline}
              pipelines={pipelines}
              switchViews={switchViews}
              tasksTypes={tasksTypes}
            />
          </Suspense>
        ) : (
          <Suspense
            fallback={
              <Spin style={{ display: "flex", justifyContent: "center" }} />
            }
          >
            <div className="overflow-scroll">
              <KanbanBoard
                columns={columns}
                setColumns={setColumns}
                selectedPipeline={selectedPipeline}
                setSelectedPipeline={setSelectedPipeline}
                setTaskToUpdate={setTaskToUpdate}
                setSelectedStageId={setSelectedStageId}
                tasksTypes={tasksTypes}
                UpdateTaskStage={UpdateTaskStage}
                loadUpdateTaskStage={loadUpdateTaskStage}
                setLoadUpdateTaskStage={setLoadUpdateTaskStage}
                source="Task"
                filterTable={filterTable}
                switchViews={switchViews}
                pipelines={pipelines}
                setPipelines={setPipelines}
                selectedRoles={selectedRoles}
                selectedUser={selectedUser}
                getPipelines={getPipelines}
                selectedFamily={selectedFamily}
                dateFilter={dateFilter}
                setActivityLabel={setActivityLabel}
                setOpenActivity360={setOpenActivity360}
                openActivity360={openActivity360}
                setTotal={setTotal}
                countChanges={countChanges}
                setCountChanges={setCountChanges}
                setOpenElementDetails={setOpenElementDetails}
                setElementDetails={setElementDetails}
                activePriority={
                  tasksFilters?.filters?.priority
                    ? tasksFilters?.filters?.priority.toString()
                    : ""
                }
                setRoomActivityId={setRoomActivityId}
                elementDetails={elementDetails}
                selectFutureActivities={selectFutureActivities}
                setUpdateFilters={setUpdateFilters}
                filterCondition={filterCondition}
                selectedTags={selectedTags}
              />
            </div>
          </Suspense>
        )}
        {/* Create/update activity drawer */}

        <Drawer
          open={openTaskDrawer}
          title={
            !taskToUpdate ? (
              t("tasks.createTaskTitle")
            ) : (
              <Tooltip
                trigger={activityLabel?.length > 76 ? "hover" : []}
                title={activityLabel}
              >
                <div className="line-clamp-1">
                  {activityLabel || singleTaskData?.label}
                </div>
              </Tooltip>
            )
          }
          footer={
            <div className="flex flex-row items-center justify-between">
              <div>
                <Button
                  htmlType="reset"
                  onClick={handleCloseDrawer}
                  style={{ marginRight: "10px" }}
                >
                  {t("fields_management.drawerCloseBtn")}
                </Button>
                <Button
                  type="primary"
                  form="form"
                  htmlType="submit"
                  loading={createAndUpdateTaskLoading}
                  disabled={
                    deletedTaskIndicator ||
                    notFoundTaskIndicator ||
                    singleTaskData?.can_update_task === 0
                  }
                >
                  {t("fields_management.drawerOkBtn")}
                </Button>
              </div>
              {taskToUpdate !== null &&
                !deletedTaskIndicator &&
                !notFoundTaskIndicator && (
                  <div
                    className="text-[13px] text-[#000000A6]"
                    type="secondary"
                    style={{ display: "flex", alignItems: "center" }}
                  >
                    {t("tasks.drawerHeaderOne")}{" "}
                    {loadSpecificTask ? (
                      <LoadingOutlined />
                    ) : (
                      <ActionsComponent elementValue={singleTaskData?.creator}>
                        <AvatarChat
                          className={"mx-1.5 flex items-center justify-center"}
                          fontSize={"0.875rem"}
                          height={"32px"}
                          width={"32px"}
                          url={`${
                            URL_ENV?.REACT_APP_BASE_URL +
                            URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                          }${singleTaskData?.creator?.avatar}`}
                          hasImage={EXTENSIONS_ARRAY?.includes(
                            singleTaskData?.creator?.avatar?.split(".")?.pop()
                          )}
                          name={getName(
                            singleTaskData?.creator?.label,
                            "avatar"
                          )}
                          type="user"
                        />
                      </ActionsComponent>
                    )}{" "}
                    {loadSpecificTask ? (
                      <LoadingOutlined />
                    ) : (
                      <Typography.Text mark>
                        {moment(singleTaskData?.create_at).format(
                          `${user?.location?.date_format} ${user?.location?.time_format}`
                        )}
                      </Typography.Text>
                    )}
                  </div>
                )}
            </div>
          }
          className="tasks-drawer"
          size="large"
          placement="right"
          afterOpenChange={handleOpenCloseDrawerEffect}
          maskClosable={false}
          onClose={handleCloseDrawer}
          extra={
            Object.keys(singleTaskData).length > 0 &&
            singleTaskData?.can_create_room === 1 &&
            user?.access?.["chat"] === "1" && (
              <Tooltip
                title={
                  singleTaskData?.room_id !== 0
                    ? t("tasks.openChatRoom")
                    : t("tasks.createChatRoom")
                }
                overlayStyle={{
                  display:
                    Object.keys(singleTaskData).length > 0 &&
                    singleTaskData?.can_create_room === 0 &&
                    "none",
                }}
              >
                <Button
                  type="text"
                  shape="circle"
                  icon={<MessageOutlined />}
                  onClick={handleOpenChatRoomFromDrawer}
                />
              </Tooltip>
            )
          }
        >
          {/* Drawer component */}
          <Suspense fallback={<Skeleton active />}>
            <CreateTaskForm
              form={form}
              onFinish={onFinish}
              loadSpecificTask={loadSpecificTask}
              setLoadSpecificTask={setLoadSpecificTask}
              prefillTaskLabel={prefillTaskLabel}
              tasksTypes={tasksTypes}
              singleTaskData={singleTaskData}
              selectedStartDate={selectedStartDate}
              setSelectedStartDate={setSelectedStartDate}
              setSelectedStartTime={setSelectedStartTime}
              selectedEndTime={selectedEndTime}
              setSelectedEndTime={setSelectedEndTime}
              selectedEndDate={selectedEndDate}
              selectedStartTime={selectedStartTime}
              setSelectedEndDate={setSelectedEndDate}
              ownersOptionsInSelect={ownersOptionsInSelect}
              loadOwners={loadOwners}
              checkedItems={checkedItems}
              guestsSearchQuery={guestsSearchQuery}
              setGuestsSearchQuery={setGuestsSearchQuery}
              checkedFollowers={checkedFollowers}
              source="task"
              addOnsValues={addOnsValues}
              setAddOnsValues={setAddOnsValues}
              files={files}
              setFiles={setFiles}
              taskToUpdate={taskToUpdate}
              pipelines={pipelines}
              selectedStageId={selectedStageId}
              guestsList={guestsList}
              ownersList={ownersList}
              setCheckedFollowers={setCheckedFollowers}
              setCheckedItems={setCheckedItems}
              guestsListPage={guestsListPage}
              followersListPage={followersListPage}
              setGuestsListPage={setGuestsListPage}
              setFollowersListPage={setFollowersListPage}
              followersSearchQuery={followersSearchQuery}
              setFollowersSearchQuery={setFollowersSearchQuery}
              deletedTaskIndicator={deletedTaskIndicator}
              notFoundTaskIndicator={notFoundTaskIndicator}
              setCheckDueDateReminder={setCheckDueDateReminder}
              guestsListLastPage={guestsListLastPage}
              followersListLastPage={followersListLastPage}
              loadGuests={loadGuests}
              isError={isError}
              setIsError={setIsError}
              activeTabKey={activeTabKey}
              setActiveTabKey={setActiveTabKey}
              setEmailNotification={setEmailNotification}
              setSelectedFamilyMembers={setSelectedFamilyMembers}
              openElementDetails={openElementDetails}
              setGuestsList={setGuestsList}
              setReminderValidator={setReminderValidator}
              reminderValidator={reminderValidator}
              totalEntities={totalEntities}
              setOwnersList={setOwnersList}
            />
          </Suspense>
        </Drawer>
        {/* Advanced filter drawer */}
        <Suspense fallback={<LoaderDrawer />}>
          <FilterOptions
            setTasksFilters={setTasksFilters}
            filtersForm={filtersForm}
            setAppliedFilters={setAppliedFilters}
            setPageNumber={setPageNumber}
            columnsBackup={columnsBackup}
            filterTable={filterTable}
            setFilterTable={setFilterTable}
            setColumns={setColumns}
            setColumnsBackup={setColumnsBackup}
            appliedFilters={appliedFilters}
            tasksTypes={tasksTypes}
            switchViews={switchViews}
            setDateFilter={setDateFilter}
            setSelectedFamily={setSelectedFamily}
            handleResetRolesSelect={handleResetRolesSelect}
            setSelectedRoles={setSelectedRoles}
            openFilterMenu={openFilterMenu}
            setOpenFilterMenu={setOpenFilterMenu}
            clearFilters={clearFilters}
            totalFiltered={totalFiltered}
            setTotalFiltered={setTotalFiltered}
            dateSegmented={dateSegmented}
            selectedFamily={selectedFamily}
            usersList={ownersOptionsInSelect}
            setUsersListPage={setFollowersListPage}
            loadUsers={loadOwners}
            setSearchUsers={setFollowersSearchQuery}
            setSelectedUser={setSelectedUser}
            selectedUser={selectedUser}
            selectedRoles={selectedRoles}
            setMeMode={setMeMode}
            tags={tags}
            setSelectedTags={setSelectedTags}
          />
        </Suspense>
        {/* Associated element viewsphere */}
        <ModuleElementDetails
          key={elementDetails?.id}
          openElementDetails={openElementDetails}
          setOpenElementDetails={setOpenElementDetails}
          setElementDetails={setElementDetails}
          elementDetails={elementDetails}
        />
        {/* Activity overview */}
        {openActivity360 ? (
          <Suspense
            fallback={
              <Spin style={{ display: "flex", justifyContent: "center" }} />
            }
          >
            <Activity360
              key={taskToUpdate ? taskToUpdate : 1}
              openActivity360={openActivity360}
              setOpenActivity360={setOpenActivity360}
              taskToUpdate={taskToUpdate}
              // singleTaskData={useDeepMemo(singleTaskData)}
              singleTaskData={singleTaskData}
              setSingleTaskData={setSingleTaskData}
              loadSpecificTask={loadSpecificTask}
              // tasksTypes={useDeepMemo(tasksTypes)}
              tasksTypes={tasksTypes}
              setTaskToUpdate={setTaskToUpdate}
              /* pipelines={useDeepMemo(pipelines)}
            guestsList={useDeepMemo(guestsList)}
            checkedItems={useDeepMemo(checkedItems)} */
              pipelines={pipelines}
              guestsList={guestsList}
              checkedItems={checkedItems}
              guestsSearchQuery={guestsSearchQuery}
              setGuestsSearchQuery={setGuestsSearchQuery}
              guestsListPage={guestsListPage}
              setGuestsListPage={setGuestsListPage}
              guestsListLastPage={guestsListLastPage}
              setCheckedItems={setCheckedItems}
              /* ownersList={useDeepMemo(ownersList)}
            checkedFollowers={useDeepMemo(checkedFollowers)} */
              ownersList={ownersList}
              checkedFollowers={checkedFollowers}
              setCheckedFollowers={setCheckedFollowers}
              setFollowersSearchQuery={setFollowersSearchQuery}
              loadOwners={loadOwners}
              loadGuests={loadGuests}
              // addOnsValues={useDeepMemo(addOnsValues)}
              addOnsValues={addOnsValues}
              setAddOnsValues={setAddOnsValues}
              // files={useDeepMemo(files)}
              files={files}
              setFiles={setFiles}
              countChanges={countChanges}
              setCountChanges={setCountChanges}
              setSelectedFamilyMembers={setSelectedFamilyMembers}
              // form={useDeepMemo(form)}
              form={form}
              setShowCardPopover={setShowCardPopover}
              deletedTaskIndicator={deletedTaskIndicator}
              setDeletedTaskIndicator={setDeletedTaskIndicator}
              notFoundTaskIndicator={notFoundTaskIndicator}
              setNotFoundTaskIndicator={setNotFoundTaskIndicator}
              followersSearchQuery={followersSearchQuery}
              totalEntities={totalEntities}
              setOwnersList={setOwnersList}
              followersListPage={followersListPage}
              setFollowersListPage={setFollowersListPage}
            />
          </Suspense>
        ) : null}
      </div>
      {/* Activity room drawer */}
      {openTaskRoomDrawer && !openActivity360 ? (
        <Suspense fallback={<LoaderDrawer />}>
          <TasksRoom
            key={roomActivityId}
            elementId={roomActivityId}
            canCreateRoom={1}
            setTaskToUpdate={setTaskToUpdate}
            setOpenActivity360={setOpenActivity360}
          />
        </Suspense>
      ) : (
        <></>
      )}
    </>
  );
});

export default TasksWrapper;
