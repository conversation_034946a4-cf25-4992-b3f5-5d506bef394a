import {
  SET_INPUT_VALUE,
  SET_NEW_ERROR_MESSAGE,
  FILTER_ERROR_MESSAGE,
  SCROLL_TO_BOTTOM,
  SET_TYPING_USER,
  SET_SEARCH_MESSAGE_TERM,
  SET_MENTION_STATE,
  SET_PATH_NAME,
  ADD_UUID_TO_MESSAGE,
  DELETE_UUID_TO_MESSAGE,
  // SET_NEW_WAITING_MESSAGE,
  // FILTER_WAITING_MESSAGE,
  ADD_TASK360,
  CANCEL_TASK_360,
  OPEN_MODAL_TICKET_GLPI,
  DETAILS_OPEN_INTEGRATION,
  SET_TYPE_USER_LIVECHAT,
} from "../../constants";

export const setInputValue = (payload) => (dispatch) => {
  dispatch({ type: SET_INPUT_VALUE, payload });
};

export const setNewErrorMessage = (payload) => (dispatch) => {
  dispatch({ type: SET_NEW_ERROR_MESSAGE, payload });
};
export const filterErrorMessage = (payload) => (dispatch) => {
  dispatch({ type: FILTER_ERROR_MESSAGE, payload });
};

// export const setNewWaitingMessage = (payload) => (dispatch) => {
//   dispatch({ type: SET_NEW_WAITING_MESSAGE, payload });
// };
// export const filterWaitingMessage = (payload) => (dispatch) => {
//   dispatch({ type: FILTER_WAITING_MESSAGE, payload });
// };

export const scrollToBottom = (payload) => (dispatch) => {
  dispatch({ type: SCROLL_TO_BOTTOM, payload });
};

export const setTypingUser = (payload) => (dispatch) => {
  dispatch({ type: SET_TYPING_USER, payload });
};

export const setSearchMessageTerm = (payload) => (dispatch) => {
  dispatch({ type: SET_SEARCH_MESSAGE_TERM, payload });
};
export const setMentionState = (payload) => (dispatch) => {
  dispatch({ type: SET_MENTION_STATE, payload });
};
export const setPathName = (payload) => (dispatch) => {
  dispatch({ type: SET_PATH_NAME, payload });
};
export const addUuidToMessage = (payload) => (dispatch) => {
  dispatch({ type: ADD_UUID_TO_MESSAGE, payload });
};
export const removeUuidToMessage = (payload) => (dispatch) => {
  dispatch({ type: DELETE_UUID_TO_MESSAGE, payload });
};
export const setTask360 = (payload) => (dispatch) => {
  dispatch({ type: ADD_TASK360, payload });
};
export const cancelTask360 = (payload) => (dispatch) => {
  dispatch({ type: CANCEL_TASK_360, payload });
};

export const setOpenModalTicketGlpi = (payload) => (dispatch) => {
  dispatch({ type: OPEN_MODAL_TICKET_GLPI, payload });
};

export const setDetailsOpenIntegration = (payload) => (dispatch) => {
  dispatch({ type: DETAILS_OPEN_INTEGRATION, payload });
};
export const setTypeUserLiveChat = (payload) => (dispatch) => {
  dispatch({ type: SET_TYPE_USER_LIVECHAT, payload });
};
