import {
  PushpinFilled,
  ReloadOutlined,
  RightOutlined,
  StarFilled,
} from "@ant-design/icons";
import { Button, List, Skeleton, Space, Tooltip, Typography } from "antd";
import React, { useCallback, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { Loader } from "..";
import {
  removePollList,
  removeSavedMessage,
  removeStarredMessage,
  resetStateOtherUser,
  searchMsgInListConversations,
} from "../../../new-redux/actions/chat.actions";
import { ChatBodyItem } from "../../../pages/layouts/chat/conversation/body/item";
import { updateMessages } from "../../../pages/layouts/chat/utils/rqUpdate";
import { useActionMessage } from "../../../pages/layouts/chat/hooks/useActionMessage";
import { moment_timezone } from "../../../App";
import { goToMessage } from "new-redux/services/chat.services";

function DrawerList({
  source,
  loading,
  data,
  loadingMore,
  hasMore,
  loadMoreFunction,
  reloadFunction,
  page,
  error,
  hasGoTo,
  tab,
  isBig,
}) {
  const { openDrawer, openSideBarDrawer, searchMsgState, currentUser } =
    useSelector((state) => state.chat);
  const [actionType, setActionType] = useState("");

  const selectedConversation = useSelector(
    (state) => state.ChatRealTime.selectedConversation
  );

  const { t } = useTranslation("common");

  const dispatch = useDispatch();
  let prevMessage = null;
  const observe = useRef(null);
  const [keyGoTo, setKeyGoTo] = useState(null);
  const skl = () =>
    Array.from(
      { length: Math.floor(document.body.clientHeight / 3) },
      (_, i) => i + 1
    ).map((item) => (
      <div className="flex  items-center px-1" key={`sklt_${item}`}>
        <Skeleton
          avatar
          paragraph={{
            rows: Math.floor(
              (Math.random() + 1) *
                Array.from({ length: 3 }, (i) => i + 1).length
            ),
          }}
          active
        />
      </div>
    ));
  const lastElement = useCallback(
    (node) => {
      if (loadingMore) return;
      if (observe.current) observe.current.disconnect();
      observe.current = new IntersectionObserver(
        (e) => {
          if (e[0].isIntersecting && hasMore) {
            loadMoreFunction(page + 1);
          }
        },
        { threshold: 0.7 }
      );
      if (node) observe.current?.observe(node);
    },
    [loadingMore, hasMore, dispatch, page]
  );

  const {
    mutate: handleActionMessage,
    isLoading,
    data: ReturnData,
  } = useActionMessage(actionType);
  const goToComponent = (item) => {
    return (
      <div
        key={item._id}
        className="
       absolute right-0 top-0 z-10 flex w-auto cursor-pointer items-center justify-start space-x-2
      
       rounded-full bg-white    px-2   py-0.5 group-hover:flex
       group-hover:border-solid  group-hover:border-gray-200     ">
        <Space className="hidden group-hover:flex  " size={4}>
          {openSideBarDrawer && tab === "1" ? (
            <Tooltip title={t("chat.action.unstar")}>
              <Button
                disabled={isLoading && ReturnData?.action?.includes("favorite")}
                type="text"
                size="small"
                className="text-yellow-500"
                onClick={() => {
                  handleActionMessage({
                    message_id: item._id,
                    params: null,
                    type_conversation: item?.room_info ? "room" : "user",

                    type_action: "remove_favorite",
                  });
                  setActionType("remove_favorite");

                  dispatch(removeStarredMessage(item));
                }}
                icon={<StarFilled />}></Button>
            </Tooltip>
          ) : openSideBarDrawer && tab === "2" ? (
            <Tooltip
              title={
                item.important !== currentUser?._id
                  ? ""
                  : t("chat.action.unpin")
              }>
              <Button
                disabled={
                  item.important !== currentUser?._id ||
                  (isLoading && ReturnData?.message?.includes("important"))
                }
                type="text"
                className="text-yellow-500 "
                size="small"
                onClick={() => {
                  if (item.important === currentUser?._id) {
                    handleActionMessage({
                      message_id: item._id,
                      params: null,
                      type_conversation: item?.room_info ? "room" : "user",
                      type_action: "remove_saved",
                    });
                    setActionType("remove_saved");
                    dispatch(removeSavedMessage(item));
                  }
                }}
                icon={<PushpinFilled style={{ fontSize: "100%" }} />}></Button>
            </Tooltip>
          ) : null}
        </Space>
        <Button
          loading={searchMsgState.loading && item._id === keyGoTo}
          className="group"
          type="text"
          size="small"
          onClick={() => goToMsg(item)}>
          <span className="group-hover  flex items-center gap-x-3 text-sm text-zinc-400">
            {t("chat.goto")}
            <RightOutlined className="ml-1 mt-1 h-3 w-3" />
          </span>
        </Button>
      </div>
    );
  };

  const goToMsg = useCallback(
    async (item) => {
      setKeyGoTo(item._id);
      dispatch(
        searchMsgInListConversations({
          loading: true,
        })
      );
      dispatch(
        resetStateOtherUser({
          forced: false,
          keepDrawerOpened: false,
          item: {
            _id:
              item?.room_info?._id ||
              item?.room_id ||
              (item?.sender_id === currentUser?._id
                ? item?.receiver_id
                : item?.sender_id),
            type: item?.room_info || item?.room_id ? "room" : "user",
            participants: true,
          },
        })
      );

      dispatch(
        await goToMessage({
          id_search: item?._id,
          type: item?.room_info ? "room" : "user",
        })
      );

      setKeyGoTo(null);
    },
    [currentUser?._id, dispatch]
  );

  return (
    <div
      className={` ${
        loading ? "cursor-wait" : "cursor-default"
      } drawer h-full overflow-y-auto`}>
      {loading ? (
        skl()
      ) : error ? (
        <Space size={3} className="flex flex-col">
          <Typography.Text type="danger">{error?.message}</Typography.Text>
          <Tooltip title={t("chat.reload")}>
            <Button
              loading={loading}
              danger
              type="primary"
              onClick={() => reloadFunction()}
              icon={<ReloadOutlined />}>
              {t("chat.reload")}
            </Button>
          </Tooltip>
        </Space>
      ) : (
        <>
          <List
            size="large"
            className=" flex h-full w-full flex-1 flex-col  overflow-y-auto"
            dataSource={data}
            rowKey={(item) => item?._id}
            renderItem={(item, index) => {
              const sameDay = prevMessage
                ? moment_timezone(item?.created_at).isSame(
                    moment_timezone(prevMessage.created_at),
                    "day"
                  )
                : false;

              prevMessage = item;
              if (
                item?.poll?.end_date &&
                moment_timezone().isAfter(
                  moment_timezone(item?.poll?.end_date)
                ) &&
                openDrawer?.type === "polls_2" &&
                tab === "2" &&
                data.find((polls) => polls.poll?._id === item.poll?._id)
              ) {
                dispatch(removePollList(item._id));
                //   dispatch(addAllPollList(item));
                updateMessages(
                  item,
                  "deleted_poll",
                  item._id,
                  selectedConversation?.id,
                  selectedConversation?.type,
                  currentUser?._id
                );
                return;
              }
              return (
                <div className="mb-2">
                  {data.length - 1 === index ? (
                    <div className=" flex flex-col">
                      <ChatBodyItem
                        isBig={isBig}
                        source={source}
                        ref={lastElement}
                        scrollToBottom={() => {}}
                        hideAvatar={false}
                        display_header_icon={false}
                        sameDay={sameDay}
                        item={item}
                        index={data.length - 1 - index}
                        data={data}
                        tabopenDropDown={{}}
                        setTabOpenDropDown={() => {}}
                        GoToComponent={hasGoTo && goToComponent(item)}
                        from={false}
                        isReply={false}
                      />
                      {loadingMore && (
                        <div className="flex w-full    items-center  justify-center  space-x-1  py-1.5">
                          <Loader size={25} />
                          <Typography.Text>
                            {" "}
                            {t("chat.loading")} ...
                          </Typography.Text>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className=" flex items-center">
                      <ChatBodyItem
                        isBig={isBig}
                        source={source}
                        scrollToBottom={() => {}}
                        hideAvatar={false}
                        display_header_icon={false}
                        sameDay={sameDay}
                        item={item}
                        index={data.length - 1 - index}
                        data={data}
                        tabopenDropDown={{}}
                        setTabOpenDropDown={() => {}}
                        GoToComponent={hasGoTo && goToComponent(item)}
                        from={false}
                        isReply={false}
                      />
                    </div>
                  )}
                </div>
              );
            }}
          />
        </>
      )}
    </div>
  );
}

export default DrawerList;
