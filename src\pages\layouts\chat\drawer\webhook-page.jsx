import { Suspense, useCallback, useEffect, useRef, useState } from "react";
import { Loader, ModalConfirm } from "components/Chat";
import { useTranslation } from "react-i18next";
import {
  Button,
  Empty,
  Form,
  Input,
  Space,
  Tooltip,
  Upload,
  message,
  Typography,
  Select,
  Spin,
  Dropdown,
  Popconfirm,
} from "antd";
import {
  CheckCircleOutlined,
  CloseCircleFilled,
  CopyOutlined,
  EditOutlined,
  InboxOutlined,
  MoreOutlined,
  QuestionCircleOutlined,
  ReloadOutlined,
  RobotOutlined,
  SendOutlined,
} from "@ant-design/icons";
import { useSelector } from "react-redux";
import MainService from "services/main.service";
import { toastNotification } from "components/ToastNotification";
import { useQuery } from "@tanstack/react-query";
import {
  accepetedExtentionImage,
  generateCodeSnippets,
  getName,
  normFile,
} from "../utils/ConversationUtils";
import { updateChatSelectedConversation } from "new-redux/actions/chat.actions";
import { useDispatch } from "react-redux";
import { STALE_TIME_CACHE } from "..";
import { queryClient, URL_ENV } from "index";
const { Text, Paragraph } = Typography;
const { Dragger } = Upload;

const fetchRobot = async (signal, discussion_id) => {
  const promise = await MainService.getBot(discussion_id, signal).then(
    (response) => response.data
  );

  promise.cancel = () => {
    signal.abort();
  };
  return promise;
};

const WebHookRender = () => {
  const { t } = useTranslation("common");
  const dispatch = useDispatch();
  const inputRef = useRef(null);
  const [form] = Form.useForm();
  const [messageApi, contextHolder] = message.useMessage();
  const [isCopied, setCopied] = useState(false);
  const [ProgrammeLang, setProgrammeLang] = useState([]);

  const [loading, setLoading] = useState({
    type: "",
    state: false,
  });
  const [langSelect, setLangSelect] = useState({ name: "", value: "" });
  const [botParams, setBotParams] = useState({ token: "", bot_id: "" });

  const [openModalDropDown, setOpenModalDropDown] = useState({
    dropdownIsOpen: false,
    modalIsOpen: false,
    type: "",
  });

  const selectedConversation = useSelector(
    (state) => state.ChatRealTime.selectedConversation
  );
  const { status, data, refetch, isRefetching, error, isError, isFetched } =
    useQuery({
      enabled: !!selectedConversation?.id,
      queryKey: ["getListRobot", selectedConversation?.id, "room"],
      queryFn: ({ signal }) => fetchRobot(signal, selectedConversation?.id),

      refetchOnWindowFocus: false,
      refetchOnMount: false,
      staleTime: STALE_TIME_CACHE,
      retry: 0,
      onError: (error) => {
        if (error.name === "CanceledError") return;

        toastNotification("error", t("toasts.errorFetchApi"), "topRight");
      },
    });
  useEffect(() => {
    if (isFetched && data?.bot?._id) {
      const codeSnippets = generateCodeSnippets(
        data?.bot?._id,
        data?.bot?.api_token
      );
      setBotParams({
        token: data?.bot?.api_token,
        bot_id: data?.bot?._id,
      });

      setProgrammeLang([
        {
          name: "PHP-cURL",
          value: codeSnippets.PHPcURL,
        },
        {
          name: "cURL",
          value: codeSnippets.cURL,
        },
        {
          name: "JavaScript - Fetch",
          value: codeSnippets.JavaScriptFetch,
        },
        {
          name: "Python-Requests",
          value: codeSnippets.PythonRequests,
        },
      ]);
      setLangSelect({
        name: "PHP-cURL",
        value: codeSnippets.PHPcURL,
      });
    }
  }, [data?.bot?.api_token, data?.bot?._id, isFetched]);

  const toggleModalDropDown = useCallback(
    ({ modalIsOpen, type, dropdownIsOpen }) => {
      let time;
      if (type && type !== "update-status-bot") form.resetFields();
      setOpenModalDropDown((p) => {
        const newState = {
          dropdownIsOpen:
            p.type === "update-status-bot" && type === undefined
              ? true
              : dropdownIsOpen === undefined
              ? p.dropdownIsOpen
              : dropdownIsOpen,
          modalIsOpen: modalIsOpen === undefined ? p.modalIsOpen : modalIsOpen,
          type: type === undefined ? p.type : type,
        };
        return newState;
      });
      clearTimeout(time);

      time = setTimeout(() => {
        inputRef.current?.focus();
        clearTimeout(time);
      }, 100);
    },
    [form]
  );
  const props = {
    listType: "picture",
    className: "avatar-uploader",
    accept: "image/*",
    multiple: false,
    defaultFileList: !data?.bot?.logo
      ? []
      : [
          {
            uid: data?.bot?._id,
            name: "bot-name",
            status: "done",
            url:
              URL_ENV?.REACT_APP_OAUTH_CHAT_API +
              process.env.REACT_APP_SUFFIX_CHAT_GROUP_STORAGE +
              data?.bot?.logo,
            thumbUrl:
              URL_ENV?.REACT_APP_OAUTH_CHAT_API +
              process.env.REACT_APP_SUFFIX_CHAT_GROUP_STORAGE +
              data?.bot?.logo,
          },
        ],
    maxCount: 1,

    beforeUpload: (file) => {
      const isImage = file.type.split("/")[0] === "image";
      if (
        !isImage ||
        !accepetedExtentionImage.includes(file.type.split("/")[1].toUpperCase())
      ) {
        messageApi.error(
          `${file.name} ${t("chat.bot.acceptedOnlyImageType")} !`
        );
        return Upload.LIST_IGNORE;
      }
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        messageApi.error(
          `${file.name} ${t("chat.errSizeImage", { size: "2 MB" })}`
        );
        return Upload.LIST_IGNORE;
      }

      return false;
    },
    onChange: (file) => {
      if (file.file.status === "removed") form.setFieldValue("botImage", "");
      else form.setFieldValue("botImage", file.file);
    },
  };

  const uploadButton = (
    <>
      <p className="ant-upload-drag-icon">
        <InboxOutlined />
      </p>
      <p className="ant-upload-text">{t("chat.bot.uploadImage")}</p>
    </>
  );
  const handleSubmit = useCallback(async () => {
    const formData = new FormData();

    form
      .validateFields()
      .then(async (values) => {
        try {
          setLoading({
            type: openModalDropDown.type,
            state: true,
          });

          formData.append("room_id", selectedConversation?.id);
          formData.append("name", values?.botName);
          formData.append("logo", values.botImage ?? "");
          let response;
          if (openModalDropDown.type === "edit-bot")
            response = await MainService.updateBot(formData, data?.bot?._id);
          else response = await MainService.createBot(formData);
          if (response.status === 200) {
            const codeSnippets = generateCodeSnippets(
              response.data?.bot?._id,
              response.data?.bot?.api_token
            );
            toastNotification(
              "success",
              openModalDropDown.type === "create-bot"
                ? t("chat.bot.successCreateBot")
                : t("chat.bot.successUpdateBot"),
              "topRight"
            );
            queryClient.setQueryData(
              ["getListRobot", response.data.bot?.room_id, "room"],
              (oldData) => {
                if (!oldData) return oldData;

                let newData = oldData;

                return {
                  ...newData,
                  bot: response.data.bot,
                };
              }
            );
            dispatch(
              updateChatSelectedConversation({
                bot: response.data?.bot,
              })
            );

            setProgrammeLang([
              {
                name: "PHP-cURL",
                value: codeSnippets.PHPcURL,
              },
              {
                name: "cURL",
                value: codeSnippets.cURL,
              },
              {
                name: "JavaScript - Fetch",
                value: codeSnippets.JavaScriptFetch,
              },
              {
                name: "Python-Requests",
                value: codeSnippets.PythonRequests,
              },
            ]);
            setLangSelect({
              name: "PHP-cURL",
              value: codeSnippets.PHPcURL,
            });
            setBotParams({
              token: response.data?.bot?.api_token,
              bot_id: response.data?.bot?._id,
            });
          }
        } catch (error) {
          toastNotification("error", t("toasts.errorFetchApi"), "topRight");
        } finally {
          toggleModalDropDown({
            modalIsOpen: false,
            dropdownIsOpen: false,
            type: "",
          });
          setLoading({
            type: "",
            state: false,
          });
        }
      })
      .catch((e) => console.log(e));
  }, [
    form,
    openModalDropDown.type,

    data?.bot?._id,
    t,
    dispatch,
    selectedConversation,
    toggleModalDropDown,
  ]);
  const handleUpdateStatus = useCallback(
    async (status) => {
      try {
        setLoading({
          type: "update-status-bot",
          state: true,
        });
        const formData = new FormData();
        formData.append("bot_id", data?.bot?._id);
        formData.append("status", status ? 0 : 1);
        const response = await MainService.updateStatusBot(formData);
        if (response.status === 200) {
          toastNotification(
            "success",
            t("chat.bot.successStatusBot"),
            "topRight"
          );

          queryClient.setQueryData(
            ["getListRobot", response.data.bot?.room_id, "room"],
            (oldData) => {
              if (!oldData) return oldData;
              let newData = oldData;

              return {
                ...newData,
                bot: response.data.bot,
              };
            }
          );
          dispatch(
            updateChatSelectedConversation({
              bot: response.data.bot,
            })
          );

          let time;
          clearTimeout(time);
          time = setTimeout(() => {
            clearTimeout(time);
          }, 200);
        }
      } catch (error) {
        toastNotification("error", t("toasts.errorFetchApi"), "topRight");
      } finally {
        toggleModalDropDown({
          modalIsOpen: false,
          dropdownIsOpen: false,
          type: "",
        });

        setLoading({
          type: "",
          state: false,
        });
      }
    },
    [data?.bot?._id, dispatch, selectedConversation, t, toggleModalDropDown]
  );
  const testAPI = useCallback(async () => {
    try {
      setLoading({
        type: "test-bot-api",
        state: true,
      });
      var myHeaders = new Headers();
      myHeaders.append("Authorization", botParams.token);
      var formdata = new FormData();
      formdata.append("bot_id", botParams.bot_id);
      formdata.append("message", "test message");
      var requestOptions = {
        method: "POST",
        headers: myHeaders,
        body: formdata,
        redirect: "follow",
      };

      const response = await fetch(
        `${
          URL_ENV?.REACT_APP_OAUTH_CHAT_API +
          "/" +
          process.env.REACT_APP_SUFFIX_API
        }send-message-bot`,
        requestOptions
      ).then((response) => response.json());
      if (response.success) {
        toastNotification("success", t("chat.bot.apiSendSuccess"), "topRight");
        setLoading({
          type: "",
          state: false,
        });
      }
    } catch (e) {
      toastNotification("error", t("toasts.somethingWrong"), "topRight");

      console.log(e);
      return null;
    } finally {
      toggleModalDropDown({
        modalIsOpen: false,
        dropdownIsOpen: false,
        type: "",
      });

      setLoading({
        type: "",
        state: false,
      });
    }
  }, [botParams.bot_id, botParams.token, t]);
  const items = [
    {
      key: "bot_menu_1",
      label: t("chat.bot.testApi"),
      icon: <SendOutlined />,
      onClick: () => testAPI(),
      disabled:
        !botParams?.token ||
        !botParams?.bot_id ||
        (loading.state && loading.type === "test-bot-api"),
    },
    {
      label: t("chat.bot.edit"),
      key: "bot_menu_2",
      icon: <EditOutlined style={{ fontSize: "100%" }} />,
      onClick: () =>
        toggleModalDropDown({
          modalIsOpen: true,
          type: "edit-bot",
          dropdownIsOpen: false,
        }),
    },
    {
      type: "divider",
    },
    {
      key: "bot_menu_3",
      label:
        data?.bot?.status === 1
          ? t("chat.bot.deactivate_bot")
          : t("chat.bot.activate_bot"),
      onClick: (e) => {
        toggleModalDropDown({
          modalIsOpen: false,
          type: "update-status-bot",
          dropdownIsOpen: true,
        });
        e.domEvent.preventDefault();
        e.domEvent.stopPropagation();
      },

      icon: (
        <Popconfirm
          open={openModalDropDown.type === "update-status-bot"}
          onOpenChange={() => {
            toggleModalDropDown({
              modalIsOpen: false,
              type: "",
              dropdownIsOpen: true,
            });
          }}
          title={
            (data?.bot?.status === 1
              ? t("chat.bot.deactivate_bot")
              : t("chat.bot.activate_bot")) +
            " `" +
            data?.bot?.name +
            "` ?"
          }
          description={
            <p
              dangerouslySetInnerHTML={{
                __html:
                  data?.bot?.status === 1
                    ? t("chat.bot.deactivate_bot_description")
                    : t("chat.bot.activate_bot_description"),
              }}
              className=" max-w-sm whitespace-normal break-all"
            ></p>
          }
          onConfirm={(e) => {
            handleUpdateStatus(data?.bot?.status === 1);
            e.stopPropagation();
          }}
          okButtonProps={{
            loading: loading.state && loading.type === "update-status-bot",
            danger: data?.bot?.status === 1,
          }}
          icon={
            <QuestionCircleOutlined
              style={{ color: data?.bot?.status === 0 ? "green" : "red" }}
            />
          }
          onCancel={(e) => {
            toggleModalDropDown({
              modalIsOpen: false,
              type: "",
              dropdownIsOpen: true,
            });

            e.stopPropagation();
          }}
          trigger={["click"]}
        >
          <RobotOutlined style={{ fontSize: "100%" }} />
        </Popconfirm>
      ),
      danger: data?.bot?.status === 1,
    },
  ];
  const handleCopy = useCallback(async () => {
    let time;
    setCopied(true);
    let plainText = document.createElement("p");
    plainText.innerHTML = langSelect?.value;
    navigator.clipboard.writeText(plainText.innerText);
    message.success(t("chat.bot.copied"));
    time = setTimeout(() => {
      setCopied(false);
      clearTimeout(time);
    }, 3000);
  }, [langSelect?.value, t]);
  return (
    <div
      id="container"
      className="  mt-2 flex h-full w-full  flex-col justify-start "
    >
      {contextHolder}

      {status === "loading" ? (
        <div className="flex h-full w-full items-center justify-center">
          <Spin size="large" />
        </div>
      ) : isError ? (
        <Space size={3} className="flex flex-col">
          <Text type="danger">{error?.message}</Text>
          <Tooltip title={t("chat.reload")}>
            <Button
              loading={isRefetching}
              danger
              type="primary"
              onClick={refetch}
              icon={<ReloadOutlined />}
            >
              {t("chat.reload")}
            </Button>
          </Tooltip>
        </Space>
      ) : data?.bot?._id ? (
        <>
          <p className="my-2 max-w-[350px] text-xs text-gray-500/70">
            {t("chat.bot.description")}
          </p>

          <div
            className={` mb-2 flex  w-full flex-col overflow-y-auto rounded-md bg-gray-200  `}
          >
            <div className="flex w-full items-center justify-between px-3">
              <div className="flex items-center justify-start">
                <Select
                  className="my-2 w-48"
                  onChange={(value) =>
                    setLangSelect(
                      ProgrammeLang.find(({ name }) => name === value)
                    )
                  }
                  value={langSelect?.name}
                  options={ProgrammeLang?.map((item) => ({
                    value: item?.name,
                    label: item?.name,
                  }))}
                />
              </div>

              <div className="flex items-center gap-x-1 ">
                <Tooltip
                  title={
                    data?.bot?.status === 1
                      ? t("chat.bot.active")
                      : t("chat.bot.deactive")
                  }
                >
                  {data?.bot?.status === 1 ? (
                    <></>
                  ) : (
                    <CloseCircleFilled className=" text-base text-red-500" />
                  )}
                </Tooltip>
                <Tooltip
                  title={!isCopied ? t("chat.bot.copy") : t("chat.bot.copied")}
                >
                  <Button
                    shape="circle"
                    type="text"
                    className="text-gray-500/70 hover:text-gray-500/90"
                    onClick={handleCopy}
                    icon={
                      !isCopied ? (
                        <CopyOutlined />
                      ) : (
                        <CheckCircleOutlined className=" scale-125" />
                      )
                    }
                  />
                </Tooltip>
                <Dropdown
                  arrow
                  menu={{
                    items,
                  }}
                  trigger={["click"]}
                  onOpenChange={(e) => {
                    toggleModalDropDown({ dropdownIsOpen: e });
                  }}
                  open={openModalDropDown.dropdownIsOpen}
                >
                  <Button
                    icon={<MoreOutlined />}
                    type="text"
                    shape="circle"
                    size="small"
                  />
                </Dropdown>
              </div>
            </div>

            {ProgrammeLang.length === 0 ? (
              <Loader size={20} />
            ) : (
              <p
                dangerouslySetInnerHTML={{ __html: langSelect?.value }}
                className="   my-1 space-y-2  overflow-y-auto  whitespace-normal break-all  p-2 leading-relaxed  "
              ></p>
            )}
          </div>
        </>
      ) : (
        <div className="flex h-full w-full flex-col  items-center  space-y-0.5 py-2">
          <Paragraph type="secondary" className=" gap-y-4 text-sm">
            {t("chat.bot.detail.paragraph")}
            <ul className=" list-inside list-decimal space-y-2 ">
              <li> {t("chat.bot.detail.list1")}</li>
              <li>{t("chat.bot.detail.list2")}</li>
              <li>{t("chat.bot.detail.list3")}</li>
            </ul>
          </Paragraph>

          <Empty
            className=" mt-4 flex w-full flex-col justify-start px-10"
            image="/avatar/bot.png"
            imageStyle={{
              width: "100%",
              height: "10rem",
              mixBlendMode: "multiply",
              opacity: 0.5,
            }}
            description={
              <span className="w-full text-base text-zinc-400">
                {t("chat.bot.empty")}
              </span>
            }
          >
            <Button
              className="mt-1 "
              block
              size="large"
              type="primary"
              onClick={() =>
                toggleModalDropDown({
                  modalIsOpen: true,
                  type: "create-bot",
                  dropdownIsOpen: false,
                })
              }
            >
              {t("chat.bot.create")}
            </Button>{" "}
          </Empty>
          <div></div>
        </div>
      )}

      {openModalDropDown.modalIsOpen && (
        <Suspense
          fallback={
            <div className="fixed inset-0 z-[9999] flex h-full w-full   items-center justify-center bg-black/5 ">
              <Loader size="2rem" />
            </div>
          }
        >
          <ModalConfirm
            title={
              openModalDropDown.type === "create-bot"
                ? t("chat.bot.create")
                : t("chat.bot.edit")
            }
            loading={loading.state && loading.type === openModalDropDown.type}
            content={
              <Form
                form={form}
                name={openModalDropDown.type}
                initialValues={{
                  botName: getName(data?.bot?.name, "name") || "",
                  botImage: data?.bot?.logo
                    ? URL_ENV?.REACT_APP_OAUTH_CHAT_API +
                      process.env.REACT_APP_SUFFIX_CHAT_GROUP_STORAGE +
                      data?.bot?.logo
                    : null,
                }}
                className="w-full "
                layout="horizontal"
                autoComplete="off"
                labelCol={{ flex: "120px" }}
                labelAlign="left"
                wrapperCol={{ flex: 1 }}
              >
                <Form.Item
                  rules={[
                    {
                      validateTrigger: "onSubmit",

                      validator: async (_, name) => {
                        if (!name) {
                          return Promise.reject(
                            new Error(t("chat.bot.requiredName"))
                          );
                        } else if (name.length < 3) {
                          return Promise.reject(
                            new Error(t("chat.bot.minName"))
                          );
                        }
                      },
                    },
                  ]}
                  required
                  label={t("chat.bot.name")}
                  name="botName"
                >
                  <Input
                    size="large"
                    ref={inputRef}
                    autoFocus
                    placeholder={t("chat.bot.namePlaceholder")}
                  />
                </Form.Item>

                <Form.Item
                  className="w-full items-center justify-center"
                  tooltip={
                    <ul className="list-outside list-disc gap-y-2">
                      <li className=" first-letter:capitalize">
                        {t("chat.errSizeImage", { size: "2 MB" })}
                      </li>
                      <li className=" first-letter:capitalize">
                        {t("chat.bot.acceptedOnlyImageType")}
                      </li>
                    </ul>
                  }
                  required={
                    !selectedConversation?.bot?.image ||
                    openModalDropDown.type === "create-bot"
                  }
                  rules={[
                    {
                      validateTrigger: "onSubmit",
                      validator: async (_, file) => {
                        if (
                          selectedConversation?.bot?.image &&
                          openModalDropDown.type !== "create-bot"
                        )
                          return Promise.resolve();
                        if (!file) {
                          return Promise.reject(
                            new Error(t("chat.bot.requiredLogo"))
                          );
                        }
                      },
                    },
                  ]}
                  label={t("chat.bot.image")}
                  name="botImage"
                >
                  <Form.Item
                    noStyle
                    valuePropName="fileList"
                    getValueFromEvent={normFile}
                  >
                    <Dragger {...props}>{uploadButton}</Dragger>
                  </Form.Item>
                </Form.Item>
              </Form>
            }
            open={openModalDropDown.modalIsOpen}
            onCancel={() =>
              toggleModalDropDown({
                modalIsOpen: false,
                dropdownIsOpen: false,
                type: "",
              })
            }
            onOk={handleSubmit}
            cancelText={t("form.cancel")}
            width={600}
            okText={
              openModalDropDown.type === "create-bot"
                ? t("form.create")
                : t("form.save")
            }
            dangerMode={false}
            disabled={false}
          />
        </Suspense>
      )}
    </div>
  );
};

export default WebHookRender;
