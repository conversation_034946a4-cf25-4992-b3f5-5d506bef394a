// Families constants
export const GET_FAMILIES_SUCCESS = "GET_FAMILIES_SUCCESS";
export const GET_SELECTED_FAMILY = "GET_SELECTED_FAMILY";
export const GET_FAMILIES_ERROR = "GET_FAMILIES_ERROR";

// Types constants
export const GET_TYPES_SUCCESS = "GET_TYPES_SUCCESS";
export const GET_TYPES_ERROR = "GET_TYPES_ERROR";

// Reset states
export const RESET_STATE = "RESET_STATE";
export const RESET_FIELD_STATE = "RESET_FIELD_STATE";
export const RESET_STATE_OTHER_USER = "RESET_STATE_OTHER_USER";
export const RESET_VISIO_STATE = "RESET_VISIO_STATE";

// Fields constants
export const IS_LOADING = "IS_LOADING";
export const UPDATE_FIELD_PARAM_LOADING = "UPDATE_FIELD_PARAM_LOADING";
export const GET_FIELDS_SUCCESS = "GET_FIELDS_SUCCESS";
export const GET_SEARCH_FIELDS_SUCCESS = "GET_SEARCH_FIELDS_SUCCESS";
export const GET_FIELDS_ERROR = "GET_FIELDS_ERROR";
export const CREATE_NEW_FIELD_SUCCESS = "CREATE_NEW_FIELD_SUCCESS";
export const CREATE_NEW_FIELD_ERROR = "CREATE_NEW_FIELD_ERROR";
export const DELETE_FIELD_SUCCESS = "DELETE_FIELD_SUCCESS";
export const DELETE_FIELD_LOADING = "DELETE_FIELD_LOADING";
export const DELETE_FIELD_ERROR = "DELETE_FIELD_ERROR";
export const UPDATE_FIELD_SUCCESS = "UPDATE_FIELD_SUCCESS";
export const UPDATE_FIELD_ERROR = "UPDATE_FIELD_ERROR";
export const UPDATE_FIELD_PARAMETER_SUCCESS = "UPDATE_FIELD_PARAMETER_SUCCESS";
export const UPDATE_FIELD_PARAMETER_ERROR = "UPDATE_FIELD_PARAMETER_ERROR";
export const UPDATE_ALIAS_SUCCESS = "UPDATE_ALIAS_SUCCESS";
export const UPDATE_ALIAS_LOADING = "UPDATE_ALIAS_LOADING";
export const UPDATE_ALIAS_ERROR = "UPDATE_ALIAS_ERROR";
export const CREATE_FIELD_LOADING = "CREATE_FIELD_LOADING";
export const UPDATE_FIELD_LOADING = "UPDATE_FIELD_LOADING";
export const CREATE_NEW_GROUP_SUCCESS = "CREATE_NEW_GROUP_SUCCESS";
export const CREATE_NEW_GROUP_ERROR = "CREATE_NEW_GROUP_ERROR";
export const CREATE_GROUP_LOADING = "CREATE_GROUP_LOADING";
export const DELETE_GROUP_LOADING = "DELETE_GROUP_LOADING";
export const DELETE_GROUP_SUCCESS = "DELETE_GROUP_SUCCESS";
export const DELETE_GROUP_ERROR = "DELETE_GROUP_ERROR";
export const UPDATE_GROUP_SUCCESS = "UPDATE_GROUP_SUCCESS";
export const UPDATE_GROUP_ERROR = "UPDATE_GROUP_ERROR";
export const UPDATE_GROUP_LOADING = "UPDATE_GROUP_LOADING";
export const UPDATE_GROUP_RANK_SUCCESS = "UPDATE_GROUP_RANK_SUCCESS";
export const UPDATE_GROUP_RANK_ERROR = "UPDATE_GROUP_RANK_ERROR";
export const UPDATE_GROUP_RANK_LOADING = "UPDATE_GROUP_RANK_LOADING";
export const UPDATE_FIELD_RANK_SUCCESS = "UPDATE_FIELD_RANK_SUCCESS";
export const UPDATE_FIELD_RANK_ERROR = "UPDATE_FIELD_RANK_ERROR";
export const UPDATE_FIELD_RANK_LOADING = "UPDATE_FIELD_RANK_LOADING";
export const UPDATE_FIELD_OPTIONS_RANK_SUCCESS =
  "UPDATE_FIELD_OPTIONS_RANK_SUCCESS";
export const UPDATE_FIELD_OPTIONS_RANK_ERROR =
  "UPDATE_FIELD_OPTIONS_RANK_ERROR";
export const UPDATE_FIELD_OPTIONS_RANK_LOADING =
  "UPDATE_FIELD_OPTIONS_RANK_LOADING";
export const OPEN_FIELD_DRAWER = "OPEN_FIELD_DRAWER";
export const TITLE_CONFIG = "TITLE_CONFIG";
export const DESTROY_FIELD_STATE = "DESTROY_FIELD_STATE";

// Field options management
export const DELETE_FIELD_OPTION_SUCCESS = "DELETE_FIELD_OPTION_SUCCESS";
export const DELETE_FIELD_OPTION_ERROR = "DELETE_FIELD_OPTION_ERROR";
export const DELETE_FIELD_OPTION_LOADING = "DELETE_FIELD_OPTION_LOADING";

//token constants
export const CLEAR_TOKEN = "CLEAR_TOKEN";
export const SET_TOKEN = "SET_TOKEN";
export const SET_REFRESH_TOKEN = "SET_REFRESH_TOKEN";

//selected menu constants
export const SET_MENU_TITLE = "SET_MENU_TITLE";

//open tag
export const SET_OPEN_TAG = "SET_OPEN_TAG";
export const SET_OPEN_ACTIVITY = "SET_OPEN_ACTIVITY";
export const SET_OPEN_CONFIGMAIL = "SET_OPEN_CONFIGMAIL";
export const SET_CURRENT_WINDOW = "SET_CURRENT_WINDOW";
export const SET_SEARCH = "SET_SEARCH";
export const CLEAR_SEARCH = "CLEAR_SEARCH";
export const SET_INVALID_CONFIGMAIL = "SET_INVALID_CONFIGMAIL";
export const SET_END_BUILD = "SET_END_BUILD";

// open update/create form
export const ADD_NEW_ELEMENT_FAMILY = "ADD_NEW_ELEMENT_FAMILY";
export const RESET_ADD_NEW_ELEMENT_FAMILY = "RESET_ADD_NEW_ELEMENT_FAMILY";
export const SET_CREATE_FORM = "SET_CREATE_FORM";
export const SET_OPEN_IMPORT_DRAWER = "SET_OPEN_IMPORT_DRAWER";
export const SET_OPEN_IMPORT_CHILDREN_DRAWER =
  "SET_OPEN_IMPORT_CHILDREN_DRAWER";
export const CANCEL_TASK_360 = "CANCEL_TASK_360";
// set SET_USER_INFOS
export const SET_USER_INFOS = "SET_USER_INFOS";
export const SET_TOURS_ACCESS = "SET_TOURS_ACCESS";

export const OPEN_MODAL_TICKET_GLPI = "OPEN_MODAL_TICKET_GLPI";
export const DETAILS_OPEN_INTEGRATION = "DETAILS_OPEN_INTEGRATION";
export const SET_TYPE_USER_LIVECHAT = "SET_TYPE_USER_LIVECHAT";
//add row in table

export const ADD_ROW_FAMILY = "ADD_ROW_FAMILY";
export const ADD_ROW_TYPE = "ADD_ROW_TYPE";
export const ADD_ROW_UNITY = "ADD_ROW_UNITY";
export const IS_DELETE_ROWS = "IS_DELETE_ROWS";

//Visio
export const CREATE_VISIO_SUCCESS = "CREATE_VISIO_SUCCESS";
export const CREATE_VISIO_ERROR = "CREATE_VISIO_ERROR";
export const GET_VISIO_SUCCESS = "GET_VISIO_SUCCESS";
export const GET_VISIO_ERROR = "GET_VISIO_ERROR";
export const IS_LOADING_VISIO = "IS_LOADING_VISIO";
export const DELETE_VISIO_SUCCESS = "DELETE_VISIO_SUCCESS";
export const DELETE_VISIO_ERROR = "DELETE_VISIO_ERROR";
export const UPDATE_VISIO_SUCCESS = "UPDATE_VISIO_SUCCESS";
export const UPDATE_VISIO_ERROR = "UPDATE_VISIO_ERROR";
export const GET_ONE_VISIO_SUCCESS = "GET_ONE_VISIO_SUCCESS";
export const GET_ONE_VISIO_ERROR = "GET_ONE_VISIO_ERROR";
export const MAKE_VISIO_FLOAT = "MAKE_VISIO_FLOAT";
export const TOGGLE_VISIO = "TOGGLE_VISIO";
export const TOGGLE_SIZE_VISIO = "TOGGLE_SIZE_VISIO";
export const SET_VISIO_PARAMS = "SET_VISIO_PARAMS";
export const MAKE_ALL_NOTIFS_READ = "MAKE_ALL_NOTIFS_READ";
//visioList
export const SET_LOADING_TAB_VISIO = "SET_LOADING_TAB_VISIO";
export const GET_DATA_CHANGE_VISIO = "GET_DATA_CHANGE_VISIO";
export const SET_LOADING_DETAILS_VISIO = "SET_LOADING_DETAILS_VISIO";
export const GET_DETAILS_VISIO = "GET_DETAILS_VISIO";
export const SET_PAGE = "SET_PAGE";
export const SET_LIST_MEET = "SET_LIST_MEET";
export const SET_KEY_MEET = "SET_KEY_MEET";
export const SET_TAB_KEY = "SET_TAB_KEY";
export const SET_DETAILS_MEET = "SET_DETAILS_MEET";
export const SET_NOW = "SET_NOW";
export const SET_LATER = "SET_LATER";
export const SET_LAST_PAGE = "SET_LAST_PAGE";
export const SET_HISTORY_COUNT = "SET_HISTORY_COUNT";
export const SET_LABEL = "SET_LABEL";
export const SET_OPEN_DRAWER_VISIO = "SET_OPEN_DRAWER_VISIO";
export const SET_DETAILS_MEET_EXTERNAL = "SET_DETAILS_MEET_EXTERNAL";
export const SET_NOTIFICATION_COUNT = "SET_NOTIFICATION_COUNT";
export const SET_NOTIFICATION_LIST = "SET_NOTIFICATION_LIST";
export const SET_PAGE_NOTIFICATION_LIST = "SET_PAGE_NOTIFICATION_LIST";
export const SET_LAST_PAGE_NOTIFICATION_LIST =
  "SET_LAST_PAGE_NOTIFICATION_LIST";
export const SET_REMINDERS_LIST = "SET_REMINDERS_LIST";
export const GET_REMINDERS_LIST = "GET_REMINDERS_LIST";
export const SET_COUNT_REMINDERS = "SET_COUNT_REMINDERS";
export const SET_LIMIT = "SET_LIMIT";
export const SET_SEARCH_LIST_VISIO = "SET_SEARCH_LIST_VISIO";
export const SET_KPI = "SET_KPI";
export const SET_KPI_DATE = "SET_KPI_DATE";
export const SET_LOAD_NOTIF = "SET_LOAD_NOTIF";
export const UPDATE_NOTIFICATION_LIST = "UPDATE_NOTIFICATION_LIST";
//Wiki
export const IS_LOADING_WIKI = "IS_LOADING_WIKI";
export const CREATE_GROUP_WIKI_SUCCESS = "CREATE_GROUP_WIKI_SUCCESS";
export const CREATE_GROUP_WIKI_ERROR = "CREATE_GROUP_WIKI_ERROR";
export const GET_GROUP_WIKI_SUCCESS = "GET_GROUP_WIKI_SUCCESS";
export const GET_GROUP_WIKI_ERROR = "GET_GROUP_WIKI_ERROR";
export const DELETE_GROUP_WIKI_SUCCESS = "DELETE_GROUP_WIKI_SUCCESS";
export const DELETE_GROUP_WIKI_ERROR = "DELETE_GROUP_WIKI_ERROR";
export const EDIT_GROUP_WIKI_SUCCESS = "EDIT_GROUP_WIKI_SUCCESS";
export const EDIT_GROUP_WIKI_ERROR = "EDIT_GROUP_WIKI_ERROR";

export const GET_FOLDER_WIKI_SUCCESS = "GET_FOLDER_WIKI_SUCCESS";
export const GET_FOLDER_WIKI_ERROR = "GET_FOLDER_WIKI_ERROR";
export const ADD_ROW_TYPE_CONTACT = "ADD_ROW_TYPE_CONTACT";
export const ADD_ROW_TYPE_ACTIVITY = "ADD_ROW_TYPE_ACTIVITY";
export const ADD_ROW_TAG = "ADD_ROW_TAG";
export const ADD_NAME_ORG = "ADD_NAME_ORG";

export const SET_IMPORT_KEY = "SET_IMPORT_KEY";

//--------------------chat ---------------------
//--------------------chat ---------------------
//--------------------chat ---------------------
// notifications
export const SET_NOTIFICATION_NUMBER = "SET_NOTIFICATION_NUMBER";
export const ADD_NOTIFICATION_NUMBER = "ADD_NOTIFICATION_NUMBER";
export const SUBSTRACT_NOTIFICATION_NUMBER = "SUBSTRACT_NOTIFICATION_NUMBER";
export const SET_NOTIFICATION_CONFIG_USER = "SET_NOTIFICATION_CONFIG_USER";
export const SET_DISPLAY_MEMBERS_PREVIEW = "SET_DISPLAY_MEMBERS_PREVIEW";
export const SET_FILTER_CHAT_PREVIEW = "SET_FILTER_CHAT_PREVIEW";
export const SET_SEARCH_CHAT_SIDEBAR = "SET_SEARCH_CHAT_SIDEBAR";
export const SET_CHAT_MEMBERS_GROUPS = "SET_CHAT_MEMBERS_GROUPS";
export const SET_ERROR_MEMBERS_GROUPS_CHAT = "SET_ERROR_MEMBERS_GROUPS_CHAT";
export const SET_LOADING_SIDE_BAR = "SET_LOADING_SIDE_BAR";
export const SET_NUMBER_UNREAD_MSG = "SET_NUMBER_UNREAD_MSG";
export const SET_SYNC_NEW_MSG = "SET_SYNC_NEW_MSG";

export const SET_CHAT_SELECTED_PARTICIPANTS = "SET_CHAT_SELECTED_PARTICIPANTS";
export const SET_CHAT_SELECTED_CONVERSATION = "SET_CHAT_SELECTED_CONVERSATION";
export const UPDATE__CHAT_SELECTED_CONVERSATION =
  "UPDATE__CHAT_SELECTED_CONVERSATION";
export const SET_CHAT_USER_LIST = "SET_CHAT_USER_LIST";
export const ADD_CHAT_USER_LIST = "ADD_CHAT_USER_LIST";
export const REMOVE_CHAT_USER_LIST = "REMOVE_CHAT_USER_LIST";
export const UPDATE_CHAT_USER_LIST = "UPDATE_CHAT_USER_LIST";

export const UPDATE_CURRENT_USER_STATUS_BLOCKED =
  "UPDATE_CURRENT_USER_STATUS_BLOCKED";
export const UPDATE_CURRENT_USER_STATUS_PRESENCE =
  "UPDATE_CURRENT_USER_STATUS_PRESENCE";

export const SET_USER_INFO_CHAT = "SET_USER_INFO_CHAT";
export const SET_CHAT_INFO_SECTION = "SET_CHAT_INFO_SECTION";
export const CREATE_GROUP_CHAT = "CREATE_GROUP_CHAT";
export const SET_MEMBRE_CHAT_PARTICIPANTS_DATE =
  "SET_MEMBRE_CHAT_PARTICIPANTS_DATE";

export const SEARCH_MESSAGE_IN_LIST_CONVERSATION =
  "SEARCH_MESSAGE_IN_LIST_CONVERSATION";
export const SET_ACTION_IN_SEARCH_STATUS = "SET_ACTION_IN_SEARCH_STATUS";
export const SET_ACTION_IN_REPLY = "SET_ACTION_IN_REPLY";

export const SEARCH_MESSAGE_GET_DATA = "SEARCH_MESSAGE_GET_DATA";
//input
export const SET_INPUT_VALUE = "SET_INPUT_VALUE";
export const SET_NEW_ERROR_MESSAGE = "SET_NEW_ERROR_MESSAGE";
export const SET_NEW_WAITING_MESSAGE = "SET_NEW_WAITING_MESSAGE";
export const FILTER_ERROR_MESSAGE = "FILTER_ERROR_MESSAGE";
export const FILTER_WAITING_MESSAGE = "FILTER_WAITING_MESSAGE";
export const SET_TYPING_USER = "SET_TYPING_USER";
export const SET_PATH_NAME = "SET_PATH_NAME";

// action
export const ADD_STARRED_MESSAGE = "ADD_STARRED_MESSAGE";
export const REMOVE_STARRED_MESSAGE = "REMOVE_STARRED_MESSAGE";

export const ADD_SAVED_MESSAGE = "ADD_SAVED_MESSAGE";
export const REMOVE_SAVED_MESSAGE = "REMOVE_SAVED_MESSAGE";
export const SET_DELETE_MESSAGE_CHAT_MEMBERS =
  "SET_DELETE_MESSAGE_CHAT_MEMBERS";

export const SET_UPDATE_MESSAGE_CHAT_MEMEBERS =
  "SET_UPDATE_MESSAGE_CHAT_MEMEBERS";

export const SET_LATENCE_TIMEOUT = "SET_LATENCE_TIMEOUT";

// Mercure EVENT
export const SET_EVENT_MERCURE = "SET_EVENT_MERCURE";
export const SET_ONLINE_USER = "SET_ONLINE_USER";
export const SET_NEW_UPDATE_ON_MESSAGE = "SET_NEW_UPDATE_ON_MESSAGE";

export const SET_NEW_MESSAGE_CHAT_MEMEBERS = "SET_NEW_MESSAGE_CHAT_MEMEBERS";
export const SET_MESSAGE_READ_CHAT = "SET_MESSAGE_READ_CHAT";
export const SET_MESSAGE_UNDO_READ_CHAT = "SET_MESSAGE_UNDO_READ_CHAT";
export const SET_EXTERNAL_ITEM_CHAT = "SET_EXTERNAL_ITEM_CHAT";

export const SET_CONFIG_USER_CHAT = "SET_CONFIG_USER_CHAT";
export const SCROLL_TO_BOTTOM = "SCROLL_TO_BOTTOM";
export const UPDATE_ROOM_BY_ID_CHAT = "UPDATE_ROOM_BY_ID_CHAT";
export const LEAVE_PARTICIPANT = "LEAVE_PARTICIPANT";
export const SET_MEDIAS_LIST = "SET_MEDIAS_LIST";
export const SET_DOCUMENTS_LIST = "SET_DOCUMENTS_LIST";
export const SET_DOCUMENTS_FILTRED_LIST = "SET_DOCUMENTS_FILTRED_LIST";
export const SET_LINKS_LIST = "SET_LINKS_LIST";
export const SET_LINKS_FILTRED_LIST = "SET_LINKS_FILTRED_LIST";

export const SET_OPEN_DRAWER = "SET_OPEN_DRAWER";
export const SET_OPEN_SIDE_BAR_DRAWER = "SET_OPEN_SIDE_BAR_DRAWER";

export const SET_STARRED_LIST = "SET_STARRED_LIST";
export const SET_PINNED_LIST = "SET_PINNED_LIST";
export const SET_SEARCH_LIST = "SET_SEARCH_LIST";
export const SET_ASSETS_COUNT = "SET_ASSETS_COUNT";
export const SET_IMAGE_TO_LIST = "SET_IMAGE_TO_LIST";
export const SET_DOCUMENT_TO_LIST = "SET_DOCUMENT_TO_LIST";
export const ADD_LINKS_TO_LIST = "ADD_LINKS_TO_LIST";
export const SET_LINKS_UPDATE_REMOVE_LIST = "SET_LINKS_UPDATE_REMOVE_LIST";

export const SET_ASSETS_TO_LIST = "SET_ASSETS_TO_LIST";
export const SET_SEARCH_MESSAGE_TERM = "SET_SEARCH_MESSAGE_TERM";
export const SET_STARRED_FETCHED = "SET_STARRED_FETCHED";
export const SET_PINNED_FETCHED = "SET_PINNED_FETCHED";
export const SET_THREAD_LIST = "SET_THREAD_LIST";
export const ADD_THREAD_TO_LIST = "ADD_THREAD_TO_LIST";
export const UPDATE_THREAD_TO_LIST = "UPDATE_THREAD_TO_LIST";

export const SET_MODAL_QUIT_GROUP = "SET_MODAL_QUIT_GROUP";
export const ADD_ARCHIVE_CONVERSATION = "ADD_ARCHIVE_CONVERSATION";
export const REMOVE_ARCHIVE_CONVERSATION = "REMOVE_ARCHIVE_CONVERSATION";
export const GET_ARCHIVE_CONVERSATIONS = "GET_ARCHIVE_CONVERSATIONS";
export const SET_SIDEBAR_DRAWER = "SET_SIDEBAR_DRAWER";
export const SET_ARCHIVED_LIST_FETCHED = "SET_ARCHIVED_LIST_FETCHED";
export const SET_CHAT_MEMBERS_GROUPS_FETCHED =
  "SET_CHAT_MEMBERS_GROUPS_FETCHED";
export const SET_ARCHIVED_LIST_IDS = "SET_ARCHIVED_LIST_IDS";
export const ADD_TO_MUTE_LIST = "ADD_TO_MUTE_LIST";
export const REMOVE_FROM_MUTE_LIST = "REMOVE_FROM_MUTE_LIST";
export const ADD_UNREAD_MESSAGE_ARCHIVED = "ADD_UNREAD_MESSAGE_ARCHIVED";
export const SET_MENTION_STATE = "SET_MENTION_STATE";
export const ADD_UUID_TO_MESSAGE = "ADD_UUID_TO_MESSAGE";
export const DELETE_UUID_TO_MESSAGE = "DELETE_UUID_TO_MESSAGE";
export const SET_ALL_STARRED_LIST = "SET_ALL_STARRED_LIST";
export const SET_ALL_STARRED_LIST_FETCHED = "SET_ALL_STARRED_LIST_FETCHED";
export const SET_ALL_PINNED_LIST = "SET_ALL_PINNED_LIST";
export const SET_ALL_PINNED_LIST_FETCHED = "SET_ALL_PINNED_LIST_FETCHED";
export const SET_DATA_CONVERSATION = "SET_DATA_CONVERSATION";
export const SET_POLL_LIST_FETCHED = "SET_POLL_LIST_FETCHED";
export const SET_ALL_POLL_LIST_FETCHED = "SET_ALL_POLL_LIST_FETCHED";
export const SET_POLL_LIST = "SET_POLL_LIST";
export const SET_ALL_POLL_LIST = "SET_ALL_POLL_LIST";
export const ADD_POLL_LIST = "ADD_POLL_LIST";
export const REMOVE_POLL_LIST = "REMOVE_POLL_LIST";
export const UPDATE_POLL_LIST = "UPDATE_POLL_LIST";
export const UPDATE_ALL_POLL_LIST = "UPDATE_ALL_POLL_LIST";

export const ADD_ALL_POLL_LIST = "ADD_ALL_POLL_LIST";
export const REMOVE_ALL__POLL_LIST = "REMOVE_ALL__POLL_LIST";
export const SET_POLLS_VOTE = "SET_POLLS_VOTE";
export const HANDLE_TYPING_USER = "HANDLE_TYPING_USER";
export const GET_VISIO_EVENT_ID = "GET_VISIO_EVENT_ID";
//--------------------chat ---------------------
//--------------------chat ---------------------

//--------------------voip ---------------------
export const GET_LOG_SUCCESS = "GET_LOG_SUCCESS";
export const GET_LOG_ERROR = "GET_LOG_ERRORT";
export const IS_LOADING_LOG = "IS_LOADING_LOG";

export const GET_PARAM_SUCCESS = "GET_PARAM_SUCCESS";
export const GET_PARAM_ERROR = "ET_PARAM_ERROR";
export const IS_LOADING_PARAM = "IS_LOADING_PARAM";

export const GET_USER_SUCCESS = "GET_USER_SUCCESS";
export const GET_USER_ERROR = "GET_USER_ERROR";
export const IS_LOADING_USER = "IS_LOADING_USER";

export const GET_VOICE_SUCCESS = "GET_VOICE_SUCCESS";
export const GET_VOICE_ERROR = "GET_VOICE_ERROR";
export const IS_LOADING_VOICE = "IS_LOADING_VOICE";

export const SET_VOICE_SUCCESS = "SET_VOICE_SUCCESS";
export const SET_VOICE_ERROR = "SET_VOICE_ERROR";

export const STOP_CALL = "STOP_CALL";
export const START_CALL = "START_CALL";
export const UPDATE_CALL_IPBX = "UPDATE_CALL_IPBX";

export const STOP_CALL_VIDEO = "STOP_CALL__VIDEO";
export const START_CALL_VIDEO = "START_CALL__VIDEO";

export const GET_STATUS_SUCCESS = "GET_STATUS_SUCCESS";
export const GET_STATUS_ERROR = "GET_STATUS_ERROR";
export const CHANGE_STATUS = "CHANGE_STATUS";

export const GET_NOTIF_SUCCESS = "GET_NOTIF_SUCCESS";
export const GET_NOTIF_ERROR = "GET_NOTIF_ERROR";

export const GET_PHONE_BOOK = "GET_PHONE_BOOK";

export const SET_LOADING_TRUE = "SET_LOADING_TRUE";
export const SET_LOADING_FALSE = "SET_LOADING_FALSE";
export const SET_LAST_CALL_LOG = "SET_LAST_CALL_LOG";
export const RESET_LAST_CALL_LOG = "RESET_LAST_CALL_LOG";
export const SET_NEW_CALL_LOG = "SET_NEW_CALL_LOG";
export const SET_NEW_MISSED_CALL = "SET_NEW_MISSED_CALL";
export const SET_CALL_FIRST_TIME = "SET_CALL_FIRST_TIME";
export const SET_IP_ADDRESS = "SET_IP_ADDRESS";
export const SET_RETURNED_QUEUE_MISSED_CALL = "SET_RETURNED_QUEUE_MISSED_CALL";
export const SET_NBR_VOICES_CALLS_QUEUES = "SET_NBR_VOICES_CALLS_QUEUES";
export const SET_NEW_MISSED_QUEUE_GROUP = "SET_NEW_MISSED_QUEUE_GROUP";
export const SET_NEW_VOICE_MESSAGING = "SET_NEW_VOICE_MESSAGING";
export const SET_FORWARDING_CALL = "SET_FORWARDING_CALL";
export const SET_RECEIVER_INFO = "SET_RECEIVER_INFO";
export const SET_CMKPHONE_INSTANCE = "SET_CMKPHONE_INSTANCE";
export const SET_CONF_INFO = "SET_CONF_INFO";
export const RESET_CONF_INFO = "RESET_CONF_INFO";
export const SET_SENDING_MAIL = "SET_SENDING_MAIL";
export const SET_EMAIL_PROPS = "SET_EMAIL_PROPS";

export const RESET_MISSED_CALL = "RESET_MISSED_CALL";
export const RESET_MISSED_QUEUE_GROUP = "RESET_MISSED_QUEUE_GROUP";
export const RESET_VOICE_MESSAGING = "RESET_VOICE_MESSAGING";
export const RESET_VOICE_MESSAGING_AND_MISSED_CALL_AND_QUEUE =
  "RESET_VOICE_MESSAGING_AND_MISSED_CALL_AND_QUEUE";
export const SET_NEW_LOG_GROUPS_QUEUES = "SET_NEW_LOG_GROUPS_QUEUES";
export const SET_NEW_LOG_GROUPS_QUEUES_IN_PROCESS =
  "SET_NEW_LOG_GROUPS_QUEUES_IN_PROCESS";
export const RESET_LOG_GROUPS_QUEUES = "RESET_LOG_GROUPS_QUEUES";
export const SET_NEW_TAG_GROUPS_QUEUES = "SET_NEW_TAG_GROUPS_QUEUES";
export const RESET_TAG_GROUPS_QUEUES = "RESET_TAG_GROUPS_QUEUES";
export const OPEN_DRAWER_CHAT = "OPEN_DRAWER_CHAT";
export const CLOSE_DRAWER_CHAT = "CLOSE_DRAWER_CHAT";
//--------------------voip ---------------------
export const GET_INTERNALCALLFORWARD_SUCCESS =
  "GET_INTERNALCALLFORWARD_SUCCESS";
export const GET_INTERNALCALLFORWARD_ERROR = "GET_INTERNALCALLFORWARD_ERROR";

export const SET_INTERNALCALLFORWARD_SUCCESS =
  "SET_INTERNALCALLFORWARD_SUCCESS";
export const SET_INTERNALCALLFORWARD_ERROR = "SET_INTERNALCALLFORWARD_ERROR";
export const SET_DEVICE_RINGING_OUTPUT = "SET_DEVICE_RINGING_OUTPUT";

//--------------------tasks---------------------
export const SET_DATE_CONVERSATION_TO_TASK = "SET_DATE_CONVERSATION_TO_TASK";
export const OPEN_TASK_DRAWER = "OPEN_TASK_DRAWER";
export const OPEN_TASK_ROOM_DRAWER = "OPEN_TASK_ROOM_DRAWER";
export const SET_MSG_TASK_DRAWER = "SET_MSG_TASK_DRAWER";
export const SET_MSG_TASK_ID_DRAWER = "SET_MSG_TASK_ID_DRAWER";
export const GET_SELECTED_VIEW_IN_TASK = "GET_SELECTED_VIEW_IN_TASK";
export const IS_USER_NOTIFIED = "IS_USER_NOTIFIED";
export const NEW_INCOMING_TASK_NOTIFICATION = "NEW_INCOMING_TASK_NOTIFICATION";
export const ADD_TASK_NOTIFICATION_ACTION_TYPE =
  "ADD_TASK_NOTIFICATION_ACTION_TYPE";
export const TASK_NOTIFICATION_PAYLOAD = "TASK_NOTIFICATION_PAYLOAD";
export const TASK_NOTIFICATION_DESCRIPTION = "TASK_NOTIFICATION_DESCRIPTION";
export const TOTAL_TASKS_NOTIFICATIONS = "TOTAL_TASKS_NOTIFICATIONS";
export const TASKS_FILTERS = "TASKS_FILTERS";
export const ACTIVITIES_MESSAGES = "ACTIVITIES_MESSAGES";
export const FILTER_READ_ACTIVITIES_MESSAGES =
  "FILTER_READ_ACTIVITIES_MESSAGES";
export const HANDLE_ACTIVE_FILTERS = "HANDLE_ACTIVE_FILTERS";
export const SET_REMINDERS = "SET_REMINDERS";
export const ADD_REMINDER = "ADD_REMINDER";
export const IS_OVERVIEW_MODAL_OPEN = "IS_OVERVIEW_MODAL_OPEN";
export const HANDLE_SELECTED_PIPELINE = "HANDLE_SELECTED_PIPELINE";
export const HANDLE_RELATION_ID = "HANDLE_RELATION_ID";
export const HANDLE_RELATION_TYPE = "HANDLE_RELATION_TYPE";

//--------------------Contacts---------------------//
export const SET_CONTACT_HEADER_INFO = "SET_CONTACT_HEADER_INFO";
export const SET_CONTACT_DATA = "SET_CONTACT_DATA";
export const SET_CONTACT_INFO_FROM_DRAWER = "SET_CONTACT_INFO_FROM_DRAWER";
export const SET_CONTACT_SORT_TABLE = "SET_CONTACT_SORT_TABLE";

//--------------------Dashboard---------------------//

export const SET_UNREAD_MSG_ONE_TO_ONE = "SET_UNREAD_MSG_ONE_TO_ONE";
export const SET_UNREAD_MSG_GROUPS = "SET_UNREAD_MSG_GROUPS";
export const SET_MISSED_CALLS = "SET_MISSED_CALLS";
export const SET_RECEIVED_CALLS = "SET_RECEIVED_CALLS";
export const SET_OUTGOING_CALLS = "SET_OUTGOING_CALLS";
export const SET_VISITORS_RMC = "SET_VISITORS_RMC";
export const SET_NEW_MSGS_RMC = "SET_NEW_MSGS_RMC";
export const SET_TOTAL_CONVERSATION_RMC = "SET_TOTAL_CONVERSATION_RMC";
export const GET_DATA = "GET_DATA";
export const SET_TASKS = "SET_TASKS";
export const SET_LOADING = "SET_LOADING";
export const SET_RMC = "SET_RMC";
export const SET_VOIP_DASHBOARD = "SET_VOIP_DASHBOARD";
export const SET_EMAIL_DASHBOARD = "SET_EMAIL_DASHBOARD";
export const SET_SELECTED_MAIL = "SET_SELECTED_MAIL";
export const GET_STATS_CHAT = "GET_STATS_CHAT";
export const SET_SELECTED_QUEUE = "SET_SELECTED_QUEUE";
export const SET_QUEUE_DASHBOARD = "SET_QUEUE_DASHBOARD";
export const SET_STATS_TASKS_DASHBOARD = "SET_STATS_TASKS_DASHBOARD";
export const SET_DATE_DASHBOARD = "SET_DATE_DASHBOARD";
export const SET_SELECTED_PIPELINE_IN_TICKET =
  "SET_SELECTED_PIPELINE_IN_TICKET";
export const SET_SELECTED_PIPELINE_IN_LEAD = "SET_SELECTED_PIPELINE_IN_LEAD";
export const SET_SELECTED_PIPELINE_IN_CONTACT =
  "SET_SELECTED_PIPELINE_IN_CONTACT";
export const SET_SELECTED_PIPELINE_IN_DEAL = "SET_SELECTED_PIPELINE_IN_DEAL";
export const SET_PIPELINES_TICKET = "SET_PIPELINES_TICKET";
export const SET_PIPELINES_LEAD = "SET_PIPELINES_LEAD";
export const SET_PIPELINES_CONTACT = "SET_PIPELINES_CONTACT";
export const SET_PIPELINES_DEAl = "SET_PIPELINES_DEAl";
export const SET_SELECTED_ACOUNT_IN_EMAIL = "SET_SELECTED_ACOUNT_IN_EMAIL";
export const ADD_TASK_IN_DASHBOARD = "ADD_TASK_IN_DASHBOARD";
export const REMOVE_TASK_IN_DASHBOARD = "REMOVE_TASK_IN_DASHBOARD";
export const UPDATE_TASK_IN_DASHBOARD = "UPDATE_TASK_IN_DASHBOARD";
export const SET_STATS_DEALS = "SET_STATS_DEALS";
export const SET_STATS_LEADS_CONTACTS = "SET_STATS_LEADS_CONTACTS";
export const RESET_DATA = "RESET_DATA";
export const DESTROY_DASHBOARD_STATE = "DESTROY_DASHBOARD_STATE";
export const SET_NAMES_QUEUES = "SET_NAMES_QUEUES";
export const SET_SELECTED_DEP_RMC = "SET_SELECTED_DEP_RMC";
export const SET_STATS_TICKETS = "SET_STATS_TICKETS";
//--------------------vue360---------------------//

export const ADD_TASK360 = "ADD_TASK360";
export const SET_ACTIVE_TAB360 = "SET_ACTIVE_TAB360";
export const SET_ACTIVE_ACTIVITY360 = "SET_ACTIVE_ACTIVITY360";
export const SET_NEW_INTERACTION = "SET_NEW_INTERACTION";
export const SET_OPEN_VIEW360_IN_DRAWER = "SET_OPEN_VIEW360_IN_DRAWER";
export const SET_OPEN_CHAT_VIEW_SPHERE = "SET_OPEN_CHAT_VIEW_SPHERE";
export const SET_NBR_CHAT_VIEW_SPHERE = "SET_NBR_CHAT_VIEW_SPHERE";
export const SET_CHAT_VIEW_SPHERE_FROM_DRAWER =
  "SET_CHAT_VIEW_SPHERE_FROM_DRAWER";
export const SET_PREV_IDS_FROM_DRAWER = "SET_PREV_IDS_FROM_DRAWER";
export const REMOVE_PREV_IDS_FROM_DRAWER = "REMOVE_PREV_IDS_FROM_DRAWER";
export const RESET_PREV_IDS_FROM_DRAWER = "RESET_PREV_IDS_FROM_DRAWER";
export const SET_ACTIVE_MENU_360 = "SET_ACTIVE_MENU_360";
//--------------------notes360---------------------//

export const ADD_NOTE360 = "ADD_NOTE360";
export const REMOVE_NOTE360 = "REMOVE_NOTE360";
export const UPDATE_NOTE360 = "UPDATE_NOTE360";
export const SET_NOTES360 = "SET_NOTES360";
export const ADD_NOTES360_TO_LIST = "ADD_NOTES360_TO_LIST";

//--------------------files360---------------------//
export const ADD_FILE360 = "ADD_FILE360";
export const REMOVE_FILE360 = "REMOVE_FILE360";
export const UPDATE_FILE360 = "UPDATE_FILE360";
export const SET_FILES360 = "SET_FILES360";
export const ADD_FILES360_TO_LIST = "ADD_FILES360_TO_LIST";
export const MODIFY_FILE360_LABEL = "MODIFY_FILE360_LABEL";
export const MODIFY_FILE360_NAME = "MODIFY_FILE360_NAME";
export const SEARCH_FILE360 = "SEARCH_FILE360";
export const RESET_SEARCH_FILE360 = "RESET_SEARCH_FILE360";
export const SET_CURRENT_SELECTED_CONTACT = "SET_CURRENT_SELECTED_CONTACT";
export const UNSET_CURRENT_SELECTED_CONTACT = "UNSET_CURRENT_SELECTED_CONTACT";
export const UPDATE_FILE_FOR_MERCURE = "UPDATE_FILE_FOR_MERCURE";
export const DELETE_FILE_FOR_MERCURE = "DELETE_FILE_FOR_MERCURE";
export const UPDATE_FILE_LABEL_FOR_MERCURE = "UPDATE_FILE_LABEL_FOR_MERCURE";

//--------------import----------------//
export const REALTIME_INVITED_USERS = "REALTIME_INVITED_USERS";
//--------------Mail----------------//
export const SET_ACCOUNT_MAIL = "SET_ACCOUNT_MAIL";
export const SET_ACCOUNT_MAIL_FETCHED = "SET_ACCOUNT_MAIL_FETCHED";
export const SET_LOADING_ACCOUNT = "SET_LOADING_ACCOUNT";
export const SET_SELECTED_ACCOUNT = "SET_SELECTED_ACCOUNT";
export const SET_HAS_NEW_MESSAGE = "SET_HAS_NEW_MESSAGE";

export const SET_EMAILS_STATS = "SET_EMAILS_STATS";
export const UPDATE_EMAILS_STATS = "UPDATE_EMAILS_STATS";
export const SET_OPEN_MODAL_EMAIL = "SET_OPEN_MODAL_EMAIL";
export const RESET_PROPS_MODAL_EMAIL = "RESET_PROPS_MODAL_EMAIL";
export const SET_OPEN_EDITOR = "SET_OPEN_EDITOR";
export const SET_REFRESH_SENT_MAIL = "SET_REFRESH_SENT_MAIL";
export const SET_PAGE_EMAILS = "SET_PAGE_EMAILS";
export const SET_SEARCH_PAGE_EMAILS = "SET_SEARCH_PAGE_EMAILS";
export const SET_FAMILYID = "SET_FAMILYID";
export const SET_REFRESH_TABLE_INBOX = "SET_REFRESH_TABLE_INBOX";
export const SET_PAGE_EMAILS_SIZE = "SET_PAGE_EMAILS_SIZE";
export const SET_SEARCH_EMAIL = "SET_SEARCH_EMAIL";
export const SET_NOTIFICATION_MAILING = "SET_NOTIFICATION_MAILING";
export const UPDATE_NOTIFICATION_MAILING = "UPDATE_NOTIFICATION_MAILING";
export const SET_REFRESH_KPI = "SET_REFRESH_KPI";
export const SET_REFRESH_NUMBER_NOTIFICATION =
  "SET_REFRESH_NUMBER_NOTIFICATION";
export const SET_FILTER_EMAIL = "SET_FILTER_EMAIL";
export const SET_FILTER_ACTIVE = "SET_FILTER_ACTIVE";
export const SET_NUMBER_EMAIL_THREAD = "SET_NUMBER_EMAIL_THREAD";
export const SET_REFRESH_VUE_SPHERE = "SET_REFRESH_VUE_SPHERE";
export const SET_SYNCHRO_TYPE = "SET_SYNCHRO_TYPE";
export const SET_DRAFT = "SET_DRAFT";
export const RESET_DRAFT = "RESET_DRAFT";

//-------------Form------------------//
export const UPDATE_ELEMENT_SUCCESSFULLY = "UPDATE_ELEMENT_SUCCESSFULLY";

//-------------Self Notes------------------//
export const TRIGGER_SAVE_NOTE = "TRIGGER_SAVE_NOTE";
export const TRIGGER_DELETE_NOTE = "TRIGGER_DELETE_NOTE";
export const SAVE_SELF_NOTE_PENDING = "SAVE_SELF_NOTE_PENDING";
export const SAVE_SELF_NOTE_SUCCESS = "SAVE_SELF_NOTE_SUCCESS";
export const SAVE_SELF_NOTE_ERROR = "SAVE_SELF_NOTE_ERROR";
export const SET_SELF_NOTES = "SET_SELF_NOTES";
export const MODIFY_SELF_NOTE = "MODIFY_SELF_NOTE";
export const DELETE_SELF_NOTE = "DELETE_SELF_NOTE";
export const ADD_SELF_NOTE = "ADD_SELF_NOTE";
export const ADD_SELF_NOTES_TO_LIST = "ADD_SELF_NOTES_TO_LIST";
export const SAVE_NEW_NOTE_AFTER_POST = "SAVE_NEW_NOTE_AFTER_POST";
export const REMOVE_NEW_NOTES_ON_UNMOUNT = "REMOVE_NEW_NOTES_ON_UNMOUNT";
export const SET_SELECTED_NOTE = "SET_SELECTED_NOTE";
export const REMOVE_SELECTED_NOTE = "REMOVE_SELECTED_NOTE";
export const MODIFY_SELECTED_NOTE_CONTENT = "MODIFY_SELECTED_NOTE_CONTENT";
export const SET_NOTE_UNLOCKED = "SET_NOTE_UNLOCKED";
export const SET_NOTE_LOCKED = "SET_NOTE_LOCKED";
export const REMOVE_IS_NEW_NOTE = "REMOVE_IS_NEW_NOTE";
export const UPDATE_NOTE_FAMILY_AND_ELEMENT = "UPDATE_NOTE_FAMILY_AND_ELEMENT";
export const SET_NOTES_NOTIFICATIONS_COUNT = "SET_NOTES_NOTIFICATIONS_COUNT";
export const INCREMENT_NOTES_NOTIFICATIONS_COUNT =
  "INCREMENT_NOTES_NOTIFICATIONS_COUNT";
export const DECREMENT_NOTES_NOTIFICATIONS_COUNT =
  "DECREMENT_NOTES_NOTIFICATIONS_COUNT";
export const SET_NOTE_OPEN_ELEMENT_MODAL = "SET_NOTE_OPEN_ELEMENT_MODAL";
export const SET_NOTES_NOTIFICATIONS_LIST = "SET_NOTES_NOTIFICATIONS_LIST";
export const ADD_TO_NOTES_NOTIFICATIONS_LIST =
  "ADD_TO_NOTES_NOTIFICATIONS_LIST";
export const REMOVE_FROM_NOTES_NOTIFICATIONS_LIST =
  "REMOVE_FROM_NOTES_NOTIFICATIONS_LIST";
// ----------- Kanban ------------//
export const SAVE_PREFERENCES = "SAVE_PREFERENCES";
export const SET_SAVE_PENDING = "SET_SAVE_PENDING";
export const UNSET_SAVE_PENDING = "UNSET_SAVE_PENDING";
//----------- rmc ------------//
export const NEW_NOTIF = "NEW_NOTIF";
export const OPEN_VISIO_RMC = "OPEN_VISIO_RMC";
export const VISIO_RMC_ELEMENT_ID = "VISIO_RMC_ELEMENT_ID";

//--------------Menu2-----------------//
export const SET_COLLAPSE_INNER_MENU = "SET_COLLAPSE_INNER_MENU";
export const SET_OPEN_TOUR = "SET_OPEN_TOUR";

//--------------Drive-----------------//
export const SET_DRIVE_PARENT_ITEMS = "SET_DRIVE_PARENT_ITEMS";
export const SET_DRIVE_SELECTED_FOLDER = "SET_DRIVE_SELECTED_FOLDER";
export const SET_DRIVE_BREADCRUMB = "SET_DRIVE_BREADCRUMB";
export const SET_DRIVE_TREE_DATA = "SET_DRIVE_TREE_DATA";
export const UPDATE_DRIVE_TREE_NODE = "UPDATE_DRIVE_TREE_NODE";
export const SET_DRIVE_SEARCH = "SET_DRIVE_SEARCH";
export const SET_DRIVE_SEARCH_INPUT = "SET_DRIVE_SEARCH_INPUT";
export const CLEAR_DRIVE_SEARCH = "CLEAR_DRIVE_SEARCH";
