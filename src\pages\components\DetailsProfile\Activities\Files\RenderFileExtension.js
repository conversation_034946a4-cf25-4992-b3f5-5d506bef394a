import {
  FileExcelOutlined,
  FileImageOutlined,
  FileJpgOutlined,
  FilePdfFilled,
  FilePdfOutlined,
  FilePptFilled,
  FilePptOutlined,
  FileTextOutlined,
  FileWordFilled,
  FileWordOutlined,
  FileZipOutlined,
} from "@ant-design/icons";
import React from "react";

function RenderFileExtension({ fileExtension }) {
  const iconStyle = {
    strokeWidth: 20,
    stoke: "white",
  };

  const iconClassName = "text-5xl text-blue-700";

  switch (fileExtension) {
    case "pdf":
      return <FilePdfOutlined style={iconStyle} className={iconClassName} />;
    //excel extensions
    case "xlsx":
      return <FileExcelOutlined style={iconStyle} className={iconClassName} />;
    //word
    case "xls":
      return <FileExcelOutlined style={iconStyle} className={iconClassName} />;
    //word
    case "doc":
      return <FileWordOutlined style={iconStyle} className={iconClassName} />;
    case "docx":
      return <FileWordOutlined style={iconStyle} className={iconClassName} />;
    case "pptx":
      return <FilePptOutlined style={iconStyle} className={iconClassName} />;
    case "ppt":
      return <FilePptOutlined style={iconStyle} className={iconClassName} />;
    case "jpg":
      return <FileJpgOutlined style={iconStyle} className={iconClassName} />;
    case "png":
      return <FileImageOutlined style={iconStyle} className={iconClassName} />;
    case "zip":
      return <FileZipOutlined style={iconStyle} className={iconClassName} />;
    default:
      return <FileTextOutlined style={iconStyle} className={iconClassName} />;
  }
}

export default RenderFileExtension;
