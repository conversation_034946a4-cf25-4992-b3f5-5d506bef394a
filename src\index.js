import ReactDOM from "react-dom";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-router-dom";
import { Provider } from "react-redux";
import { PersistGate } from "redux-persist/integration/react";
import { I18nextProvider } from "react-i18next";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

import { fetchEnvConfig } from "utils/fetchEnvConfig";
import i18n from "./translations/i18n";
import { store, persistor } from "new-redux/store";

import App from "./App";
import ErrorBoundary from "error-boundary/ErrorBoundary";

import "./index.css";
import "antd/dist/reset.css";

// if (process.env.REACT_APP_BRANCH === "prod") console.log = () => {};
// Hassine: I Add this block of code, so i can log events of P_Live on the branch prod to track data
const realConsoleLog = console.log.bind(console);
if (process.env.REACT_APP_BRANCH === "prod") {
  console.log = (...args) => {
    // Turn all args into one string so we can search for the substring
    const message = args
      .map((arg) =>
        typeof arg === "string" ? arg : JSON.stringify(arg, null, 2)
      )
      .join(" ");

    // Only print if it contains "P_Live"
    if (message.includes("P_Live")) {
      realConsoleLog(...args);
    }
  };
}
//
export const queryClient = new QueryClient();
export const URL_ENV = await fetchEnvConfig();
if (URL_ENV)
  ReactDOM.render(
    <I18nextProvider i18n={i18n}>
      <ErrorBoundary>
        <QueryClientProvider client={queryClient}>
        
            <Provider store={store}>
              <PersistGate loading={null} persistor={persistor}>
                <BrowserRouter>
                  <App />
                </BrowserRouter>
              </PersistGate>
            </Provider>
          </QueryClientProvider>
    
      </ErrorBoundary>
    </I18nextProvider>,
    document.getElementById("root")
  );
