import { memo, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { Quill } from "react-quill";
import { debounce } from "lodash";
import { Button, Divider, Input, Modal, Tooltip, Form, Upload } from "antd";
import {
  BubbleEmail,
  FormItem,
  formRules,
  refactorDataAccounts,
  SelectWithPrefix,
  setEmailFields,
  setSendingMail,
  SignatureDropdown,
  tagRender,
  TemplateDropdown,
  trackDraftData,
  UploadFiles,
  UploadItemRender,
} from "./utils";
import { useWindowSize } from "pages/clients&users/components/WindowSize";
import { CloseOutlined, SendOutlined } from "@ant-design/icons";
import {
  Maximize2,
  Minimize2,
  Minus,
  SendHorizontal,
  Trash2,
} from "lucide-react";
import { toastNotification } from "components/ToastNotification";
import CustomEditor from "./CustomEditor";
import MainService from "services/main.service";
import SignatureBlot from "./SignatureBlot.js";

//
const Delta = Quill.import("delta");

const EmailComposerModal = ({
  open,
  setOpen,
  isReduce,
  setIsReduce,
  isMinimize,
  setIsMinimize,
  dataAccounts,
}) => {
  //
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const location = useLocation();
  const windowSize = useWindowSize();
  const quillRef = useRef(null);
  const relatedElement = useRef(null);
  //
  const draft = useSelector((state) => state.mailReducer.draft);
  const emailProps = useSelector((state) => state.voipBlackList?.emailProps);
  // const propsModalEmail = useSelector(
  //   (state) => state.mailReducer?.propsModalEmail ?? {}
  // );
  //
  const usedAccount = dataAccounts.find((e) => e.selected);
  const senderId = useRef(usedAccount?.value ?? null);
  //
  const [sender, setSender] = useState(usedAccount?.label ?? null);
  const [receiversState, setReceiversState] = useState({
    receivers: [],
    openCci: false,
    openCc: false,
    cci: [],
    cc: [],
  });
  const [receiverOptions, setReceiverOptions] = useState([]);
  const [subject, setSubject] = useState("");
  const [editorContent, setEditorContent] = useState("");
  const [fileList, setFileList] = useState([]);
  // const [loadingSubmit, setLoadingSubmit] = useState(false);
  //
  const { receivers, openCc, cc, openCci, cci } = receiversState;
  const updateReceiversState = (newState) => {
    setReceiversState((prev) => ({ ...prev, ...newState }));
  };
  //
  //
  const fetchReceiverOptions = async (search) => {
    if (!search || !search?.length) {
      setReceiverOptions([]);
      return;
    }
    try {
      const formData = new FormData();
      formData.append("search", search);
      formData.append("accountId", usedAccount?.value);
      const {
        data: { address },
      } = await MainService.searchEmail(formData);
      if (address.length)
        setReceiverOptions(
          address.map((item) => ({
            label: item,
            value: item,
          }))
        );
      else if (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(search))
        setReceiverOptions([
          {
            label: search,
            value: search,
          },
        ]);
      else setReceiverOptions([]);
    } catch (err) {
      toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? `error: ${err.message}` : { err });
    } finally {
    }
  };
  //
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const handleSearchReceiverOptions = useCallback(
    debounce((value) => {
      fetchReceiverOptions(value);
    }, 300),
    []
  );
  //
  const insertSignature = (html) => {
    const editor = quillRef.current.getEditor();

    const [old] = editor.scroll.descendants(SignatureBlot);
    if (old) {
      const idx = editor.getIndex(old);
      editor.deleteText(idx, old.length(), Quill.sources.USER);

      editor.insertEmbed(idx, "signature", html, Quill.sources.USER);
      editor.setSelection(idx + 1, Quill.sources.SILENT);
    } else {
      const range = editor.getSelection(true);
      const pos = range.index;

      editor.insertText(pos, "\n\n", Quill.sources.USER);

      editor.insertEmbed(pos + 2, "signature", html, Quill.sources.USER);

      editor.setSelection(pos + 3, Quill.sources.SILENT);
    }
  };
  //
  const insertTemplate = (temp, method) => {
    const quill = quillRef.current.getEditor();
    quill.focus();

    if (method === "clearAndInsert") {
      const delta = quill.clipboard.convert(`${temp}<p><br></p>`);
      quill.setContents(delta, "silent");
      quill.setSelection(quill.getLength(), 1);
    } else {
      const range = quill.getSelection(true) || {
        index: quill.getLength(),
        length: 0,
      };
      if (range.index > 0) {
        const prevChar = quill.getText(range.index - 1, 1);
        if (prevChar !== "\n") {
          quill.insertText(range.index, "\n", "user");
          range.index++;
        }
      }
      const html = `<p>${temp}</p><p><br></p>`;
      const delta = quill.clipboard.convert(html);

      quill.updateContents(
        new Delta().retain(range.index).concat(delta),
        "user"
      );
      quill.setSelection(range.index + delta.length(), 0);
    }
    quill.focus();
  };
  //
  const trackDraft = () => {
    const [, currentPage] = location.pathname.split("/");
    if (currentPage !== "mailing") return;
    dispatch(
      trackDraftData({
        id: "newMsg",
        sender,
        receivers,
        cc,
        cci,
        subject,
        editorContent,
        fileList,
        senderId: senderId.current,
      })
    );
  };
  //
  const handleClose = (saveDraft = true) => {
    saveDraft && trackDraft();
    form.resetFields();
    form.resetFields();
    relatedElement.current = null;
    setOpen(false);
  };
  //
  //
  const onFinish = async () => {
    dispatch(
      setSendingMail({
        open: true,
        data: {
          sender,
          receivers,
          cc,
          cci,
          subject,
          editorContent,
          fileList,
          senderId: senderId.current,
          contactId: relatedElement.current?.contactId,
          familyId: relatedElement.current?.familyId,
        },
      })
    );
    handleClose();
  };
  //
  // To rack if there's a draft, so fill the states
  useEffect(() => {
    if (emailProps) {
      if (emailProps?.sender) {
        const findEmail = dataAccounts.find(
          (account) => account.label === emailProps.sender
        )?.label;
        const sender = findEmail?.label || usedAccount.label;
        if (findEmail) senderId.current = findEmail.value;
        setSender(sender);
        form.setFieldValue("sender", sender);
      }

      updateReceiversState({
        receivers: emailProps?.receivers || [],
        cc: [],
        cci: [],
        openCc: false,
        openCci: false,
      });
      form.setFieldsValue({
        receivers: emailProps?.receivers || [],
        cc: [],
        cci: [],
      });

      setSubject(emailProps?.subject || "");
      form.setFieldValue("subject", emailProps?.subject || "");

      setEditorContent(emailProps?.message || "");

      dispatch(setEmailFields(null, false));
      setFileList([]);

      if (emailProps.contactId && emailProps.familyId) {
        relatedElement.current = {
          contactId: emailProps.contactId,
          familyId: emailProps.familyId,
        };
      }
    }

    const [, currentPage] = location.pathname.split("/");
    const newMsg = draft?.newMsg;

    if (currentPage !== "mailing" || !newMsg) return;

    const {
      sender,
      receivers = [],
      cc = [],
      cci = [],
      subject,
      editorContent,
      fileList = [],
      senderId: prevSenderId,
    } = newMsg;

    if (sender) {
      setSender(sender);
      form.setFieldValue("sender", sender);
    }

    if (subject) {
      setSubject(subject);
      form.setFieldValue("subject", subject);
    }

    if (editorContent) {
      setEditorContent(editorContent);
    }

    if (fileList.length) {
      setFileList(fileList);
    }

    if (prevSenderId) senderId.current = prevSenderId;

    const hasCc = cc.length > 0;
    const hasCci = cci.length > 0;
    if (receivers.length || hasCc || hasCci) {
      updateReceiversState({
        receivers,
        cc,
        cci,
        openCc: hasCc,
        openCci: hasCci,
      });
      form.setFieldsValue({
        receivers,
        cc,
        cci,
      });
    }

    return () => {
      if (open) setOpen(false);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [emailProps]);
  //
  //
  const quillContainerHeight = useMemo(() => {
    const isCcCciOpen = openCc && openCci ? 80 : openCc || openCci ? 40 : 0;
    const isFileList =
      fileList.length >= 4
        ? 100
        : fileList.length === 3
        ? 75
        : fileList.length === 2
        ? 50
        : fileList.length === 1
        ? 25
        : 0;

    const defaultHeight =
      windowSize.height *
      (windowSize.height <= 600
        ? 0.5
        : windowSize.height <= 700
        ? 0.53
        : windowSize.height <= 800
        ? 0.56
        : windowSize.height <= 900
        ? 0.59
        : 0.62);
    return (defaultHeight - isCcCciOpen - isFileList) * (isReduce ? 0.7 : 1);
  }, [fileList.length, openCc, openCci, windowSize.height, isReduce]);
  //
  // console.log(windowSize.height);
  //
  const modalProps = useMemo(
    () => ({
      centered: !isReduce,
      open: open && !isMinimize,
      width: isReduce ? 820 : windowSize.width * 0.83,
      closable: false,
      style: isReduce
        ? {
            position: "fixed",
            top: "auto",
            bottom: -24,
            right: 0,
            left: "auto",
            margin: 0,
          }
        : { left: 34 },
      styles: {
        content: { padding: 0 },
        header: {
          backgroundColor: "#f2f6fc",
          padding: "8px 12px",
          marginBottom: 0,
        },
        body: { /*height: windowSize.height * 0.8,*/ padding: "2px 6px" },
        footer: { padding: "14px 20px", marginTop: 8 },
      },
    }),
    [isMinimize, isReduce, open, windowSize.width]
  );
  //
  return (
    <>
      <Modal
        {...modalProps}
        title={
          <div className="flex items-center justify-between">
            <span className="text-sm font-semibold  text-[#041e49]">
              {t("mailing.newMsg")}
            </span>
            <div className="flex items-center space-x-1">
              <Button
                type="text"
                size="small"
                onClick={() => setIsMinimize(true)}
                icon={<Minus className="mt-2" size={18} />}
              />
              <Button
                type="text"
                size="small"
                onClick={() => setIsReduce((prev) => !prev)}
                icon={
                  isReduce ? <Maximize2 size={16} /> : <Minimize2 size={16} />
                }
              />
              <Button
                type="text"
                size="small"
                onClick={() => handleClose()}
                icon={<CloseOutlined style={{ fontSize: 14 }} />}
              />
            </div>
          </div>
        }
        footer={
          <div className="relative flex items-center justify-between">
            <div className="flex space-x-3">
              <Button type="primary" onClick={() => form.submit()}>
                <div className="flex items-center space-x-1.5">
                  <SendHorizontal size={14} />
                  <span>{t("mailing.NewMsg.send")}</span>
                </div>
              </Button>
              <div className="flex space-x-1">
                <UploadFiles fileList={fileList} setFileList={setFileList} />
                <TemplateDropdown insertTemplate={insertTemplate} />
                <SignatureDropdown
                  accountId={
                    dataAccounts.find((e) => e?.label === sender)?.value
                  }
                  insertSignature={insertSignature}
                />
              </div>
            </div>
            <Button
              danger
              type="text"
              onClick={() => handleClose(false)}
              icon={<Trash2 size={16} />}
            />
          </div>
        }
      >
        <div className="relative w-full flex-col">
          <Form
            form={form}
            layout="vertical"
            onFinish={onFinish}
            autoComplete="off"
          >
            <FormItem
              key="sender"
              name="sender"
              initialValue={sender}
              autoFocus
            >
              <SelectWithPrefix
                key="sender"
                autoFocus
                placeholder="Underlined"
                size="large"
                variant="Borderless"
                style={{ width: "100%" }}
                // defaultValue={usedAccount?.value}
                options={refactorDataAccounts(dataAccounts)}
                onChange={(value, option) => {
                  setSender(value);
                  senderId.current = option.accountId;
                  // draftRef.current.sender = value;
                }}
                prefix={
                  <span className="text-sm text-slate-500">
                    {t("mailing.NewMsg.from")}
                  </span>
                }
              />
            </FormItem>
            <Divider style={{ margin: 0 }} />
            <FormItem
              key="receivers"
              name="receivers"
              initialValue={receivers}
              rules={formRules(t("mailing.NewMsg.To"), t)}
            >
              <SelectWithPrefix
                key="receivers"
                showSearch
                allowClear
                notFoundContent=""
                mode="multiple"
                size="large"
                // defaultValue={["lucy"]}
                variant="Borderless"
                maxTagCount="responsive"
                style={{ width: "100%" }}
                options={receiverOptions}
                onSearch={handleSearchReceiverOptions}
                onChange={(value) => {
                  setReceiverOptions([]);
                  updateReceiversState({ receivers: [...value] });
                  // draftRef.current.receivers = value;
                }}
                tagRender={tagRender}
                maxTagPlaceholder={(omittedValues) => (
                  <Tooltip
                    styles={{ root: { pointerEvents: "none" } }}
                    title={omittedValues.map(({ label }) => label).join(", ")}
                  >
                    <span className="cursor-help text-sm">{`+ ${omittedValues.length} ...`}</span>
                  </Tooltip>
                )}
                prefix={
                  <span className="w-6 text-sm text-slate-500">
                    {t("mailing.NewMsg.To")}
                  </span>
                }
                suffix={
                  <div className="flex space-x-1.5 text-sm text-slate-500">
                    {!receiversState.openCc && (
                      <span
                        className="cursor-pointer hover:underline"
                        onClick={() => updateReceiversState({ openCc: true })}
                      >
                        Cc
                      </span>
                    )}
                    {!receiversState.openCci && (
                      <span
                        className="cursor-pointer hover:underline"
                        onClick={() => updateReceiversState({ openCci: true })}
                      >
                        Cci
                      </span>
                    )}
                  </div>
                }
              />
            </FormItem>
            <Divider style={{ margin: 0 }} />
            {openCc && (
              <>
                <FormItem key="cc" name="cc">
                  <SelectWithPrefix
                    key="cc"
                    showSearch
                    allowClear
                    notFoundContent=""
                    mode="multiple"
                    size="large"
                    variant="Borderless"
                    maxTagCount="responsive"
                    style={{ width: "100%" }}
                    options={receiverOptions}
                    onSearch={handleSearchReceiverOptions}
                    onChange={(value) => {
                      setReceiverOptions([]);
                      updateReceiversState({ cc: [...value] });
                      // draftRef.current.cc = value;
                    }}
                    tagRender={tagRender}
                    maxTagPlaceholder={(omittedValues) => (
                      <Tooltip
                        styles={{ root: { pointerEvents: "none" } }}
                        title={omittedValues
                          .map(({ label }) => label)
                          .join(", ")}
                      >
                        <span className="cursor-help text-sm">{`+ ${omittedValues.length} ...`}</span>
                      </Tooltip>
                    )}
                    prefix={
                      <span className="w-6 text-sm text-slate-500">Cc</span>
                    }
                    suffix={
                      <Button
                        danger
                        type="text"
                        size="small"
                        icon={<CloseOutlined style={{ fontSize: 14 }} />}
                        onClick={() =>
                          updateReceiversState({ openCc: false, cc: [] })
                        }
                      />
                    }
                  />
                </FormItem>
                <Divider style={{ margin: 0 }} />
              </>
            )}
            {openCci && (
              <>
                <FormItem key="cci" name="cci">
                  <SelectWithPrefix
                    key="cci"
                    showSearch
                    allowClear
                    notFoundContent=""
                    mode="multiple"
                    size="large"
                    variant="Borderless"
                    maxTagCount="responsive"
                    style={{ width: "100%" }}
                    options={receiverOptions}
                    onSearch={handleSearchReceiverOptions}
                    onChange={(value) => {
                      setReceiverOptions([]);
                      updateReceiversState({ cci: [...value] });
                      // draftRef.current.cci = value;
                    }}
                    tagRender={tagRender}
                    maxTagPlaceholder={(omittedValues) => (
                      <Tooltip
                        styles={{ root: { pointerEvents: "none" } }}
                        title={omittedValues
                          .map(({ label }) => label)
                          .join(", ")}
                      >
                        <span className="cursor-help text-sm">{`+ ${omittedValues.length} ...`}</span>
                      </Tooltip>
                    )}
                    prefix={
                      <span className="w-6 text-sm text-slate-500">Cci</span>
                    }
                    suffix={
                      <Button
                        danger
                        size="small"
                        type="text"
                        icon={<CloseOutlined style={{ fontSize: 14 }} />}
                        onClick={() =>
                          updateReceiversState({ openCci: false, cci: [] })
                        }
                      />
                    }
                  />
                </FormItem>
                <Divider style={{ margin: 0 }} />
              </>
            )}
            <FormItem
              key="subject"
              name="subject"
              rules={formRules(t("mailing.Subject"), t)}
              initialValue={subject}
            >
              <Input
                placeholder={t("mailing.Subject")}
                variant="borderless"
                size="large"
                value={subject}
                onChange={(e) => {
                  setSubject(e.target.value);
                  // draftRef.current.subject = e.target.value;
                }}
              />
            </FormItem>
            <Divider style={{ margin: 0 }} />
          </Form>
          <div
            className="quill-editor-modal-mailing relative mt-1"
            style={{
              "--editor-modal-mailing-max-height": `${quillContainerHeight}px`,
              "--ql-size-small": `"${t("mailing.small")}"`,
              "--ql-size-normal": `"${t("mailing.normal")}"`,
              "--ql-size-large": `"${t("mailing.large")}"`,
              "--ql-size-huge": `"${t("mailing.huge")}"`,
            }}
          >
            <CustomEditor
              value={editorContent}
              onChange={(value) => {
                setEditorContent(value);
                // draftRef.current.editorContent = value;
              }}
              ref={quillRef}
            />
          </div>
          <div className="upload-list-mailing relative mb-1 mt-11 max-h-[110px] w-[65%] min-w-[800px] overflow-y-auto px-4">
            <Upload
              fileList={fileList}
              itemRender={UploadItemRender}
              onChange={({ fileList }) => {
                setFileList([...fileList]);
                // draftRef.current.fileList = [...fileList];
              }}
            />
          </div>
        </div>
      </Modal>
      {isMinimize && (
        <BubbleEmail setIsMinimize={setIsMinimize} onClose={handleClose} />
      )}
    </>
  );
};
//

export default memo(EmailComposerModal);
