/**
 * @name KanbanBoard
 *
 * @description A kanban board component with drag and drop functionnality
 *
 * @param {Number} selectedPipeline The id of the selected pipeline from the header's select.
 * @param {Number} familyId The id of the module (contacts:2, deals:3, ...).
 * @param {Number} selectedFamily The id of the selected module.
 * @param {Number} scrollingStageIdInElements The id of the stage where the scroll is currently active.
 * @param {Number} countChanges The number of changes committed on the overview. It will reload the kanban to update it.
 * @param {String} source Variable indicate the source from which the component is being called.
 * @param {String} filterTable Id of task type to filter with.
 * @param {String} switchViews Selected view (kanban, Table, ...).
 * @param {String} searchElement Search query in modules kanban.
 * @param {String} activePriority Active priorities filter.
 * @param {String} activeRoomId The id of active discussion room in module.
 * @param {String} ticketsFolderId The id of active folder in tickets module.
 * @param {String} filterCondition Filters combination (or, and).
 * @param {Boolean} loadDeals Show loader while loading data in modules kanban.
 * @param {Boolean} openActivity360 Show activity overview modal.
 * @param {Boolean} selectFutureActivities Show only activities fron today on (incoming activities).
 * @param {Array} columns Data that will be displayed in kanban board (columns and cards data).
 * @param {Array} tasksTypes Array of all task types.
 * @param {Array} pipelines Array of pipelines.
 * @param {Array} scrollParameterElements Array of stages which have scrolled through.
 * @param {Array} selectedRoles Array of selected roles from filter.
 * @param {Array} dateFilter Array of selected dates range from filter.
 * @param {Function} setColumns Data's setter function.
 * @param {Function} setSelectedStageId Stage id state setter.
 * @param {Function} setTaskToUpdate taskToUpdate state setter. (with the specific id).
 * @param {Function} getKanbanData Get kanban data api call (for modules kanban).
 * @param {Function} setElementDetailToUpdate Sets 'elementDetailToUpdate' state.
 * @param {Function} setOpenDrawerUpdate Sets 'openDrawerUpdate' state.
 * @param {Function} setOpenDrawerCreate Sets 'openDrawerCreate' state.
 * @param {Function} setScrollParameterElements Sets 'scrollParameterElements' state.
 * @param {Function} setScrollingStageIdInElements Sets 'scrollingStageIdInElements' state.
 * @param {Function} getPipelines Get all pipelines api trigger.
 * @param {Function} setOpenActivity360 Sets 'openActivity360' state.
 * @param {Function} setActivityLabel Sets 'activityLabel' state.
 * @param {Function} setTotal Sets 'total' state.
 * @param {Function} setCountChanges Sets 'countChanges' state.
 * @param {Function} setOpenElementDetails Sets 'openElementDetails' state.
 * @param {Function} setElementDetails Sets 'elementDetails' state.
 * @param {Function} setRoomActivityId Sets 'roomActivityId' state.
 * @param {Function} setModalType Sets 'modalType' state.
 * @param {Function} setOpenModal Sets 'openModal' state.
 * @param {Function} setActiveRoomId Sets 'activeRoomId' state.
 * @param {Function} setElementInfo Sets 'elementInfo' state.
 * @param {Function} setOpenDrawerInfo Sets 'openDrawerInfo' state.
 * @param {Function} closeTicketForGuest Close ticket for guest (module tickets).
 * @param {Function} reopenTicketForGuest Reopen ticket for guest (module tickets).
 *
 * @returns {JSX.Element} The rendered kanban board component.
 */

// React imports
import { useCallback, useEffect, useMemo, useState, memo, forwardRef } from "react";

// 3rd-party dependancies imports
import { arrayMove } from "@dnd-kit/sortable";
import { Draggable } from "react-beautiful-dnd";
import { DragDropContext, Droppable } from "react-beautiful-dnd";
import {
  Button,
  Card,
  Col,
  Form,
  Input,
  InputNumber,
  List,
  Modal,
  Radio,
  Skeleton,
  Spin,
  Typography,
  message,
} from "antd";
import { CheckOutlined, PlusOutlined } from "@ant-design/icons";
import { useSelector, useDispatch } from "react-redux";
import InfiniteScroll from "react-infinite-scroll-component";
import { useTranslation } from "react-i18next";
import relativeTime from "dayjs/plugin/relativeTime";
import dayjs from "dayjs";
import PropTypes from "prop-types";
import { useLocation } from "react-router-dom";
import DOMPurify from "dompurify";
import VirtualList from "rc-virtual-list";
import { find } from "lodash";

// Common imports
import GroupColors from "../../../../components/GroupColors";
import MainService from "../../../../services/main.service";
import { toastNotification } from "../../../../components/ToastNotification";
import { setOpenTaskDrawer } from "../../../../new-redux/actions/tasks.actions/handleTaskDrawer";
import { deleteElements } from "../../../clients&users/services/services";
import { useWindowSize } from "../../../clients&users/components/WindowSize";
import TasksRoom from "../../tasksRoom";
import { setUpdateElementSuccessfully } from "../../../../new-redux/actions/form.actions/form";
import { handleReload } from "pages/tasks/helpers/handleCheck";
import { handleTranslateModuleLabel } from "pages/tasks/helpers/handleTranslateModuleLabel";
import EmptyPage from "components/EmptyPage";
import { isGuestConnected } from "utils/role";
import SortableColumn from "./SortableColumn";
import SortableItem from "./SortableItem";
import ErrorState from "pages/tasks/ErrorState";

// Create a forwarded ref version of SortableItem
const ForwardedSortableItem = forwardRef((props, ref) => (
  <SortableItem {...props} forwardedRef={ref} />
));

ForwardedSortableItem.displayName = 'ForwardedSortableItem';

dayjs.extend(relativeTime);

//Show column loader.
const LoadingCard = memo(({ height }) => (
  <Col span={5} style={{ height }}>
    <Card
      bordered
      bodyStyle={{ display: "none" }}
      title={<Skeleton.Input active />}
      className="kanban-header-card"
    />
    <Skeleton active style={{ paddingTop: "8px" }} />
  </Col>
));

const KanbanBoard = memo(function MemoizedKanbanBoard({
  selectedPipeline,
  setTaskToUpdate,
  columns,
  setColumns,
  setSelectedStageId,
  tasksTypes,
  getKanbanData,
  source,
  filterTable,
  switchViews,
  setElementDetailToUpdate,
  setOpenDrawerUpdate,
  loadDeals,
  setOpenDrawerCreate,
  familyId,
  pipelines,
  scrollParameterElements,
  setScrollParameterElements,
  scrollingStageIdInElements,
  setScrollingStageIdInElements,
  selectedRoles,
  getPipelines,
  selectedFamily,
  dateFilter,
  searchElement,
  setOpenActivity360,
  setActivityLabel,
  setTotal,
  openActivity360,
  countChanges,
  setCountChanges,
  setOpenElementDetails,
  setElementDetails,
  activePriority,
  setRoomActivityId,
  selectFutureActivities,
  setModalType,
  setOpenModal,
  setActiveRoomId,
  activeRoomId,
  ticketsFolderId,
  filterCondition,
  setElementInfo,
  setOpenDrawerInfo,
  closeTicketForGuest,
  reopenTicketForGuest,
  selectedUser,
  selectedTags,
}) {
  const [showNewStatusForm, setShowNewStatusForm] = useState(false);
  const [statusColor, setStatusColor] = useState("");
  const [loadStages, setLoadStages] = useState(false);
  const [loadUpdateStagesRank, setLoadUpdateStagesRank] = useState(false);
  const [loadCreateStage, setLoadCreateStage] = useState(false);
  const [loadUpdateTaskStage, setLoadUpdateTaskStage] = useState(false);
  const [selectedReasonType, setSelectedReasonType] = useState(null);
  const [reasons, setReasons] = useState(null);
  const [loadUpdateInFinalStages, setLoadUpdateInFinalStages] = useState(false);
  const [scrollingStageId, setScrollingStageId] = useState(null);
  const [scrollParameter, setScrollParameter] = useState([]);
  const [getActivitiesError, setGetActivitiesError] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);
  
  const [form] = Form.useForm();
  const [finalStageForm] = Form.useForm();
  const [t] = useTranslation("common");
  const {
    isUserNotified,
    selectedViewInTask,
    taskNotifActionType,
    taskNotifPayload,
  } = useSelector((state) => state?.TasksRealTime);
  const { user } = useSelector((state) => state.user);
  const userRole = useSelector((state) => state?.user?.user?.role);
  const { search, dropAction } = useSelector((state) => state?.form);
  const dispatch = useDispatch();
  const location = useLocation();
  const windowSize = useWindowSize();

  // Handle close 'create stage' form side effects.
  const resetData = () => {
    setShowNewStatusForm(false);
    setStatusColor("");
    form.setFieldsValue({
      statusName: "",
      percent: "",
    });
  };

  // Handle add new stage from kanban.
  const addNewStage = async (payload) => {
    try {
      setLoadCreateStage(true);
      await MainService.createNewStage(payload);
      source === "Task" ? getPipelines() : getKanbanData();
      setLoadCreateStage(false);
      resetData();
      toastNotification(
        "success",
        "Stage was created successfully",
        "bottomRight"
      );
    } catch (error) {
      console.log(`Error ${error}`);
      toastNotification("error", t("toasts.somethingWrong"));
      setLoadCreateStage(false);
    }
  };

  // Handle delete stage by id.
  const deleteStage = async (stageId) => {
    try {
      await MainService.deleteStageFromKanban(stageId);
      source === "Task" ? getPipelines() : getKanbanData();
    } catch (error) {
      if (error?.response?.data?.message === "You cannot delete this stage") {
        toastNotification("error", t("toasts.cannotDeleteStage"), "topRight");
      } else {
        console.log(`Error ${error}`);
      }
    }
  };

  // Sum all activities sum on activate advanced filters.
  const sumTotal = (array) => array.reduce((sum, { total }) => sum + total, 0);

  // Get activities api trigger (only activities).
  const getTasks = useCallback(
    async (signal) => {
      try {
        setLoadStages(true);
        let roles = selectedRoles ? selectedRoles?.toString() : "";
        const payload = new FormData();
        payload.append("type_task", filterTable);
        payload.append(
          "search",
          search.length >= 3 && switchViews === "Kanban"
            ? DOMPurify.sanitize(search)
            : ""
        );
        payload.append("roles", roles);
        payload.append(
          "start",
          dateFilter
            ? dateFilter[0].toUpperCase()
            : selectFutureActivities === true
            ? dayjs(dayjs().startOf("day")).format(user?.location?.date_format)
            : ""
        );
        payload.append(
          "end",
          !selectFutureActivities && dateFilter
            ? dateFilter[1].toUpperCase()
            : ""
        );
        payload.append(
          "family_id",
          Array.isArray(selectedFamily) && selectedFamily?.length === 2
            ? selectedFamily[0]
            : ""
        );
        payload.append(
          "related_element",
          Array.isArray(selectedFamily) && selectedFamily?.length === 2
            ? selectedFamily[1]
            : ""
        );
        payload.append("user_id", selectedUser?.id?.toString() || user?.id);
        payload.append("priorities", activePriority);
        payload.append("logic", filterCondition);
        if (Array.isArray(selectedTags) && selectedTags?.length > 0) {
          selectedTags.forEach((id) => {
            payload.append("tags_ids[]", id);
          });
        }
        if (pipelines && pipelines.length > 0) {
          const response = await MainService.getKanbanTasks(
            selectedPipeline === 0
              ? pipelines && pipelines[0]?.id
              : selectedPipeline,
            payload,
            signal
          );

          setColumns(response?.data?.stages);
          setScrollParameter(
            response?.data?.stages.map((stage) => {
              return {
                ...scrollParameter,
                id: stage?.stage_id,
                page: 1,
                lastPage: stage?.last_page,
              };
            })
          );
          setTotal(sumTotal(response?.data?.stages));
        }
        getActivitiesError && setGetActivitiesError(false);
        setLoadUpdateTaskStage(false);
      } catch (error) {
        setGetActivitiesError(true);
        console.log(`Error ${error}`);
        toastNotification("error", t("toasts.somethingWrong"));
      } finally {
        setLoadStages(false);
      }
    },
    [
      filterTable,
      pipelines,
      search,
      selectedPipeline,
      switchViews,
      selectedRoles,
      dateFilter,
      selectedFamily,
      activePriority,
      selectFutureActivities,
      filterCondition,
      selectedUser,
      selectedTags,
    ]
  );
  useEffect(() => {
    return () => {
      dispatch(setUpdateElementSuccessfully(null));
      finalStageForm.resetFields();
    };
  }, [dispatch]);

  // Trigger the get activities function.
  useEffect(() => {
    if (source === "Task" && switchViews === "Kanban") {
      getTasks();
    }
    return () => setColumns([]);
  }, [source, getTasks, selectedPipeline]);

  // Trigger the get activities when start scrolling in stages (works in activities and modules).
  const getTasksOnScroll = async (id, page) => {
    try {
      let formData = new FormData();
      formData.append("stage_id", id);
      formData.append("page", page);

      if (source === "Task") {
        formData.append("type_task", filterTable);
        formData.append("search", search);
      } else {
        formData.append("search", searchElement);
      }

      formData.append("roles", selectedRoles?.toString() || "");

      if (ticketsFolderId) {
        formData.append("tickets_folder_id", ticketsFolderId);
      }
      const response = await MainService.getKanbanTasksOnScroll(
        source === "Task" ? "tasks" : "family",
        formData
      );
      setColumns((prevColumns) => {
        const updatedColumns = [...prevColumns];
        const columnIndex = updatedColumns.findIndex(
          (col) => col?.stage_id === id
        );

        if (columnIndex !== -1) {
          updatedColumns[columnIndex] = {
            ...updatedColumns[columnIndex],
            elements: [
              ...updatedColumns[columnIndex]?.elements,
              ...response?.data?.elements,
            ],
          };
        }

        return updatedColumns;
      });
    } catch (error) {
      console.log(`Error ${error}`);
      toastNotification("error", t("toasts.somethingWrong"));
    }
  };

  // Increment page on scroll until the last page.
  const callApiOnScroll = useMemo(
    () =>
      scrollParameter.map(({ id, page, lastPage }) => {
        return (incrementPage) => {
          if (incrementPage === id && page <= lastPage && page > 1) {
            getTasksOnScroll(id, page);
          }
        };
      }),
    [scrollParameter]
  );

  // Call api on scroll.
  const callApiOnScrollElements = useMemo(
    () =>
      source !== "Task" &&
      scrollParameterElements.map(({ id, page, lastPage }) => {
        return (incrementPage) => {
          if (incrementPage === id && page <= lastPage && page > 1) {
            getTasksOnScroll(id, page);
          }
        };
      }),
    [scrollParameterElements]
  );

  useEffect(() => {
    if (source === "Task") {
      callApiOnScroll?.forEach((apiCall) => apiCall(scrollingStageId));
    } else {
      callApiOnScrollElements?.forEach((apiCall) =>
        apiCall(scrollingStageIdInElements)
      );
    }
  }, [
    scrollingStageId,
    scrollingStageIdInElements,
    callApiOnScroll,
    callApiOnScrollElements,
    source,
  ]);

  // Reload the kanban according the mercure event (this works on kanban).
  useEffect(() => {
    if (
      isUserNotified &&
      selectedViewInTask === "Kanban" &&
      taskNotifPayload !== null &&
      source === "Task"
    ) {
      getTasks();
    }
  }, [
    dispatch,
    isUserNotified,
    selectedViewInTask,
    taskNotifActionType,
    taskNotifPayload,
    columns,
    source,
  ]);

  // Trigger remove task API.
  const deleteTask = async (id) => {
    try {
      setLoadStages(true);
      let formData = new FormData();
      id && formData.append("id[]", id);
      const response = await MainService.deleteSpecificTask(formData);
      if (response?.status === 200) {
        getTasks();
        toastNotification(
          "success",
          t("tasks.taskDeletedToast"),
          "bottomRight"
        );
      }
    } catch (error) {
      console.log(`Error ${error}`);
      toastNotification("error", t("toasts.somethingWrong"));
    }
  };

  // Handle delete element from modules kanban.
  const handleDeleteElement = async (elementId, familyId) => {
    try {
      const formData = new FormData();
      formData.append("ids[]", elementId);
      formData.append("family_id", familyId);
      const response = await deleteElements(formData);
      if (response?.status === 200) {
        getKanbanData();
      }
    } catch (error) {
      console.log(`Error ${error}`);
      toastNotification("error", t("toasts.somethingWrong"));
    }
  };

  // Update stage rank (after drag and drop).
  const updateStagesRank = async (array) => {
    try {
      setLoadUpdateStagesRank(true);
      let formData = new FormData();
      //Add stage id and new rank. `rank[stage_id]: new_rank`
      array?.forEach((item, i) => {
        if (item) {
          let key = `rank[${item?.stage_id}]`;
          let value = i + 1;
          formData.append(key, value);
        }
      });
      await MainService.updateStagesRankInTask(formData);
      message.success(t("toasts.rankChanged"), 3);
      setLoadUpdateStagesRank(false);
    } catch (error) {
      console.log(`Error ${error}`);
      setLoadUpdateStagesRank(false);
      message.error("Something went wrong", 3);
    }
  };

  // Update stage of activity.
  const UpdateTaskStage = async (payload) => {
    try {
      setLoadUpdateTaskStage(true);
      const response = await MainService.updateTaskStageInKanban(payload);
      if (response?.status === 200) {
        getTasks();
      }
    } catch (error) {
      console.log(`Error ${error}`);
      setLoadUpdateTaskStage(false);
      message.error("Something went wrong", 3);
    }
  };

  // Update stage of element.
  const updateElementStage = async (id, payload, newStageId) => {
    try {
      setLoadUpdateStagesRank(true);
      const response = await MainService.updateElementStage(
        id,
        payload,
        newStageId
      );
      setLoadUpdateStagesRank(false);
      return response;
    } catch (error) {
      setLoadUpdateStagesRank(false);
      console.log(`Error ${error}`);
      message.error(error, 3);
    }
  };

  // Update view after drop (in activities kanban).
  const updateViewAfterDrop = (result) => {
    setColumns((prevColumn) => {
      let newStage = prevColumn?.find(
        (column) =>
          Number(column?.stage_id) === Number(result?.destination?.droppableId)
      );

      let draggedElement = prevColumn
        ?.map(
          (column) =>
            Number(column?.stage_id) === Number(result?.source?.droppableId) &&
            column?.elements?.find(
              (item) => item?.element_id === result?.draggableId || item?.id === result?.draggableId
            )
        )
        ?.filter((el) => el !== false)[0];

      setColumns(
        columns
          ?.map((column) =>
            Number(column?.stage_id) === Number(result?.source?.droppableId)
              ? {
                  ...column,
                  elements: column?.elements?.filter(
                    (item) => item?.element_id !== result?.draggableId || item?.id !== result?.draggableId
                  ),
                }
              : column
          )
          ?.map((column) =>
            column?.stage_id === newStage?.stage_id
              ? {
                  ...column,
                  elements:
                    column?.elements && column?.elements?.length > 0
                      ? [...column?.elements, draggedElement]
                      : [draggedElement],
                }
              : column
          )
      );
    });
  };

  // Handle drag end: refer to https://github.com/atlassian/react-beautiful-dnd/blob/master/docs/api/drag-drop-context.md

  const onDragEnd = async (result) => {
    const activeElement = columns
      ?.map((col) =>
        col?.elements?.find((el) => el?.id === result?.draggableId)
      )
      ?.filter((el) => el !== undefined);
     
   
    if (result?.destination === null) {
      return;
    }

    if (
      (result?.type === "task" || result?.type === "element") &&
      result?.source?.droppableId === result?.destination?.droppableId
    ) {
      return;
    }

    if (
      result?.type === "column" &&
      (userRole === "SuperAdmin" || userRole === "Admin")
    ) {
      setColumns((previous) => {
        updateStagesRank(
          arrayMove(columns, result?.source?.index, result?.destination?.index)
        );
        return arrayMove(
          previous,
          result?.source?.index,
          result?.destination?.index
        );
      });
    } else if ((result?.type === "task" && activeElement[0]?.is_follower === 0) || (result?.type === "task" && !!find(activeElement[0].guests,["id",user.id]))) {
      setColumns(() => {
        UpdateTaskStage({
          task_id: [result?.draggableId],
          new_stage_id: result?.destination?.droppableId,
        });

        let columnsArray = [...columns];
        let task =
          columnsArray &&
          columnsArray
            .map((column) =>
              column?.elements.find((item) => item?.id === result?.draggableId)
            )
            .filter((el) => el !== undefined)[0];
        let newStage =
          columnsArray &&
          columnsArray.find(
            (column) =>
              column?.stage_id === Number(result?.destination?.droppableId)
          );

        setColumns(
          columnsArray &&
            columnsArray
              .map((column) =>
                column?.stage_id === Number(result?.source?.droppableId)
                  ? {
                      ...column,
                      elements: column?.elements.filter(
                        (item) => item?.id !== result?.draggableId
                      ),
                    }
                  : column
              )
              .map((column) =>
                column?.stage_id === newStage?.stage_id
                  ? { ...column, elements: [...column?.elements, task] }
                  : column
              )
        );
      });
    } else if (result?.type === "element") {
      let draggedElement =
        columns &&
        columns
          .map(
            (column) =>
              Number(column?.stage_id) ===
                Number(result?.source?.droppableId) &&
              column?.elements.find(
                (item) => item?.element_id === result?.draggableId || item?.id === result?.draggableId
              )
          )
          .filter((el) => el !== false)[0];

      const { data } = await updateElementStage(
        familyId,
        {
          new_stage_id: result?.destination?.droppableId,
          id_element: result?.draggableId,
        },
        result?.destination?.droppableId,
        dispatch
      );

      if (data?.success && data?.required_fields === 1) {
        setElementDetailToUpdate({
          id: result?.draggableId,
          label: draggedElement?.element_info?.label_data,
        });
        dispatch(
          setUpdateElementSuccessfully({
            ...dropAction,
            destination: result?.destination?.droppableId,
          })
        );
        setOpenDrawerUpdate(true);
      } else if (data?.is_final === 1 && data?.required_fields === 0) {
        dispatch(
          setUpdateElementSuccessfully({
            ...dropAction,
            destination: result?.destination?.droppableId,
            elementId: result?.draggableId,
          })
        );
        location?.pathname === "/deals" && setReasons(data?.data);
      } else {
        // When no further action is needed, update view and fetch Kanban data
        updateViewAfterDrop(result);
        getKanbanData();
      }
    }
  };

  // Handle submit reasons form (after choosing reasons).
  const handleSumitReasonsForm = async () => {
    try {
      setLoadUpdateInFinalStages(true);
      let selectedReasons = finalStageForm.getFieldsValue();
      let formData = new FormData();
      formData.append("new_stage_id", Number(dropAction?.destination));
      formData.append("id_element", dropAction?.elementId);
      formData.append("id_reason", selectedReasons?.reasons);
      formData.append("reason_type", selectedReasons?.reasonType);
      let response = await MainService.updateElementToFinalStage(
        familyId,
        formData
      );
      if (response?.status === 200) {
        getKanbanData();
        setReasons(null);
        setLoadUpdateInFinalStages(false);
        message.success(t("chat.message_system.deal_updated_stage"), 3);
        finalStageForm.resetFields();
        dispatch(setUpdateElementSuccessfully(null));
      }
    } catch (error) {
      console.log(`Error ${error}`);
      setLoadUpdateInFinalStages(false);
      message.error("Something went wrong", 3);
    }
  };

  // Handle changes in reasons form.
  const handleChooseReasons = (changedFields, allFields) => {
    if (changedFields[0]?.name === "reasonType") {
      finalStageForm.resetFields("reasons");
    }
  };

  // Callback function that triggers any changes when choosing reasons.
  const handleChangeReasonsItems = (e) => {
    setSelectedReasonType(e?.target?.value);
    finalStageForm.setFieldsValue({
      reasons: undefined,
    });
  };

  // Create new Stage.
  const createNewStage = async (values) => {
    try {
      let formData = new FormData();
      formData.append("label", values?.statusName);
      formData.append("color", statusColor ? statusColor : "#bfbfbf");
      formData.append("percent", values?.winProbability);
      formData.append(
        "pipeline_id",
        selectedPipeline === 0
          ? pipelines && pipelines[0]?.id
          : selectedPipeline
      );
      await addNewStage(formData);
    } catch (error) {
      console.log("Error " + error);
    }
  };

  // Reset create stage form.
  const resetCreateStageForm = () => {
    setShowNewStatusForm(false);
    resetData();
  };

  //Set the reasons array in case a deal is dropped on a closing stage.
  useEffect(() => {
    if (dropAction && dropAction?.closingReasons) {
      setReasons(dropAction?.closingReasons);
    }
  }, [dropAction]);

  // Reload the kanban to update the view (after making any update on the overview).
  useEffect(() => {
    if (switchViews === "Kanban" && countChanges > 0 && !openActivity360) {
      getTasks();
      setCountChanges(0);
    }
  }, [countChanges, openActivity360, switchViews]);

  // Open the create activity/element when the stage is empty.
  const handleMainBtnClick = (column) => {
    if (source === "Task") {
      dispatch(setOpenTaskDrawer(true));
    } else {
      setOpenDrawerCreate(true);
    }
    setSelectedStageId(column?.stage_id);
  };

  //This is useful for the guest user to detect the closing stage.
  const isClosingStage = (stageId) => {
    let pipeline = pipelines?.find(
      (pipeline) => pipeline?.id === selectedPipeline
    );
    let finalStage = pipeline?.stages?.find((stage) => stage?.id === stageId);
    return (
      finalStage?.default && finalStage?.final && finalStage?.resolved === 0
    );
  };

  //This is useful for the guest user to detect the resolved stage.
  const isresolvedStage = (stageId) => {
    let pipeline = pipelines?.find(
      (pipeline) => pipeline?.id === selectedPipeline
    );
    let finalStage = pipeline?.stages?.find((stage) => stage?.id === stageId);
    return finalStage?.resolved === 1;
  };

  /**
   * Handles moving to the next page for a given column in the task or element source.
   * @param {string} column - The column to move to the next page.
   * @returns None
   */
  const handleNextPage = (column) => {
    const targetArray =
      source === "Task" ? scrollParameter : scrollParameterElements;
    const setTargetArray =
      source === "Task" ? setScrollParameter : setScrollParameterElements;
    // Only update if targetArray exists and is not empty
    if (targetArray) {
      setTargetArray(
        targetArray.map((el) =>
          el?.id === column?.stage_id ? { ...el, page: el?.page + 1 } : el
        )
      );
    }
    const setter =
      source === "Task" ? setScrollingStageId : setScrollingStageIdInElements;
    setter(column?.stage_id);
  };

  /**
   * Handles the scrolling behavior for a specific column.
   * @param {Object} column - The column object for which scrolling behavior is handled.
   * @returns None
   */
  const handleColumnScroll = (column) => {
    const setter =
      source === "Task" ? setScrollingStageId : setScrollingStageIdInElements;
    setter(column?.stage_id);
  };

  // Check if more items can be loaded
  const hasMore = (column) =>
    source === "Task"
      ? scrollParameter.find((stage) => stage?.id === column?.stage_id)?.page <
        scrollParameter.find((stage) => stage?.id === column?.stage_id)
          ?.lastPage
      : scrollParameterElements.find((stage) => stage?.id === column?.stage_id)
          ?.page <
        scrollParameterElements.find((stage) => stage?.id === column?.stage_id)
          ?.lastPage;

          const handleScroll = ({ target }, col) => {
            // Trigger `handleNextPage` when scrolled to the bottom
            if (
              target.scrollHeight - target.scrollTop - target.clientHeight < 10 &&
              hasMore(col)
            ) {
              handleNextPage(col);
              // handleColumnScroll(col);
            }
          };
  if (getActivitiesError) {
    return <ErrorState onRetry={handleReload} />;
  }

  



  return (
    <>
      {/* Room drawer */}
      {source !== "Task" && (
        <TasksRoom
          key={activeRoomId}
          elementId={activeRoomId}
          canCreateRoom={1}
          setElementDetailToUpdate={setElementDetailToUpdate}
        />
      )}
      {/* Kanban */}
      <Spin
        className="kanban-spinner"
        spinning={
          (loadUpdateStagesRank ||
            loadUpdateTaskStage ||
            (typeof loadDeals !== "undefined" && loadDeals === true)) &&
          columns?.length > 0
        }
        style={{ margin: "0 !important" }}
      >
        <DragDropContext  onDragEnd={onDragEnd}  
    >
          <Droppable droppableId="board" direction="horizontal"   isDropDisabled={isScrolling} type="column" >
            {(provided) => (
              <div
                style={{
                  userSelect: "none",
                  margin: 0,
                  // paddingTop: "10px",
                  marginLeft: "5px",
                  display: "flex",
                  gap: 20,
                  ...provided?.draggableProps?.style,
                }}
                ref={provided.innerRef}
                {...provided.droppableProps}
                className="overflow-scroll"
              >
                {columns && columns?.length === 0 ? (
                  <>
                    <LoadingCard height={windowSize?.height - 280} />
                    <LoadingCard height={windowSize?.height - 280} />
                  </>
                ) : (
                  columns?.map((column, i) => (
                    <SortableColumn
                      key={column?.stage_id}
                      index={i}
                      source={source}
                      setOpenDrawerCreate={setOpenDrawerCreate}
                      setSelectedStageId={setSelectedStageId}
                      deleteStage={deleteStage}
                      columnData={column}
                      isScrolling={isScrolling}
                    >
                      <Droppable
                        type={source === "Task" ? "task" : "element"}
                        droppableId={column?.stage_id?.toString()}
                        mode="virtual"
                  
                        renderClone={(provided, snapshot, rubric) => {
                       
                          const item = find(column?.elements, [
                            source === "Task" ? "id":"element_id"  ,
                            rubric.draggableId,
                          ]);

                          return (
                            <List.Item
                              key={  source === "Task"
                                ? item?.id
                                : item?.element_id}
                              {...provided.dragHandleProps}
                              ref={provided?.innerRef}
                              {...provided.draggableProps}
                              style={{
                                ...provided?.draggableProps?.style,
                              }}
                            >
                              <ForwardedSortableItem
                                id={
                                  source === "Task"
                                    ? item?.id
                                    : item?.element_id
                                }
                                content={item}
                                tasksList={column?.elements && column?.elements}
                                key={
                                  source === "Task"
                                    ? item?.id
                                    : item?.element_id
                                }
                                index={i}
                                source={source}
                                setTaskToUpdate={setTaskToUpdate}
                                deleteTask={deleteTask}
                                tasksTypes={tasksTypes}
                                setOpenDrawerUpdate={setOpenDrawerUpdate}
                                setElementDetailToUpdate={
                                  setElementDetailToUpdate
                                }
                                handleDeleteElement={handleDeleteElement}
                                setOpenActivity360={setOpenActivity360}
                                setActivityLabel={setActivityLabel}
                                setOpenElementDetails={setOpenElementDetails}
                                setElementDetails={setElementDetails}
                                stageColor={column?.stage_color}
                                setRoomActivityId={setRoomActivityId}
                                setModalType={setModalType}
                                setOpenModal={setOpenModal}
                                setActiveRoomId={setActiveRoomId}
                                setElementInfo={setElementInfo}
                                setOpenDrawerInfo={setOpenDrawerInfo}
                                closeTicketForGuest={closeTicketForGuest}
                                reopenTicketForGuest={reopenTicketForGuest}
                                isFinalStage={isClosingStage(column?.stage_id)}
                                isResolved={isresolvedStage(column?.stage_id)}
                              />
                            </List.Item>
                          );
                        }}
                      >
                        {(dropProvided, dropSnapshot) => {
                          return (
                            <div
                            key={column?.stage_id}
                              id="scrollableDiv"
                              {...dropProvided?.droppableProps}
                              ref={dropProvided?.innerRef}
                              className="kanban-scrollable-list bg-[#fafafa]"
                            >
                              <List className="kanban-list" key={column?.stage_id}>
                                <VirtualList
                      key={column?.stage_id}
                    
                                  data={
                                    column?.elements &&
                                    column?.elements?.map((task) => ({
                                      ...task,
                                      key: task?.id,
                                    }))
                                  }
                                  onMouseEnter={()=>{
                                    setIsScrolling(true)
                                   }}
                                  onMouseLeave={()=>{
                                    setIsScrolling(false)
                                   }}
                             
                                  itemHeight={270}
                                  height={windowSize?.height - 290}
                                  itemKey={(item) =>   source === "Task"
                                    ? item?.id
                                    : item?.element_id}
                                  onScroll={(e) => handleScroll(e, column)}
                                  className="activities-virtual-list p-1"
                                >
                                  {(item,index) => (
                                    <Draggable
                      
                                      index={index}
                                      draggableId={  source === "Task"
                                        ? item?.id
                                        : item?.element_id}
                                      key={  source === "Task"
                                        ? item?.id
                                        : item?.element_id}
                                      isDragDisabled={isGuestConnected()}
                                    >
                                      {(dragProvided, snapshot, reduce) => {
                                        return (
                                          <List.Item
                                            key={  source === "Task"
                                              ? item?.id
                                              : item?.element_id}
                                            {...dragProvided.dragHandleProps}
                                            ref={dragProvided?.innerRef}
                                            {...dragProvided.draggableProps}
                                            style={{
                                              ...dragProvided?.draggableProps?.style,
                                              paddingRight: "6px",
                                       
                                            }}
                                          >
                                            <ForwardedSortableItem
                                              id={
                                                source === "Task"
                                                  ? item?.id
                                                  : item?.element_id
                                              }
                                              content={item}
                                              tasksList={
                                                column?.elements &&
                                                column?.elements
                                              }
                                              key={
                                                source === "Task"
                                                  ? item?.id
                                                  : item?.element_id
                                              }
                                              index={i}
                                              source={source}
                                              setTaskToUpdate={setTaskToUpdate}
                                              deleteTask={deleteTask}
                                              tasksTypes={tasksTypes}
                                              setOpenDrawerUpdate={
                                                setOpenDrawerUpdate
                                              }
                                              setElementDetailToUpdate={
                                                setElementDetailToUpdate
                                              }
                                              handleDeleteElement={
                                                handleDeleteElement
                                              }
                                              setOpenActivity360={
                                                setOpenActivity360
                                              }
                                              setActivityLabel={
                                                setActivityLabel
                                              }
                                              setOpenElementDetails={
                                                setOpenElementDetails
                                              }
                                              setElementDetails={
                                                setElementDetails
                                              }
                                              stageColor={column?.stage_color}
                                              setRoomActivityId={
                                                setRoomActivityId
                                              }
                                              setModalType={setModalType}
                                              setOpenModal={setOpenModal}
                                              setActiveRoomId={setActiveRoomId}
                                              setElementInfo={setElementInfo}
                                              setOpenDrawerInfo={
                                                setOpenDrawerInfo
                                              }
                                              closeTicketForGuest={
                                                closeTicketForGuest
                                              }
                                              reopenTicketForGuest={
                                                reopenTicketForGuest
                                              }
                                              isFinalStage={isClosingStage(
                                                column?.stage_id
                                              )}
                                              isResolved={isresolvedStage(
                                                column?.stage_id
                                              )}
                                            />
                                             
                                          </List.Item>
                                        );
                                      }}
                                      
                                    </Draggable>
                                  )}
                     
                                </VirtualList>
                               
                              </List>

                              {!isGuestConnected() && (
                                <div className="sticky bottom-0 z-[1] flex w-full flex-row items-center justify-between rounded-b-lg border border-gray-200 bg-white px-1 py-1.5">
                                  <Button
                                    type="default"
                                    size="small"
                                    icon={<PlusOutlined />}
                                    onClick={() => handleMainBtnClick(column)}
                                  >
                                    {source === "Task"
                                      ? t("tasks.addQuickTask")
                                      : t("contacts.createNewX", {
                                          x: handleTranslateModuleLabel(
                                            location.pathname.split("/")[1]
                                          ),
                                        })}
                                  </Button>
                                  {column?.total > 0 && (
                                    <span className="text-xs text-[#94a3b8]">{`1 - ${
                                      column?.elements?.length
                                    } ${t("mailing.of")} ${
                                      column?.total
                                    }`}</span>
                                  )}
                                </div>
                              )}
                            </div>
                          );
                        }}
                      </Droppable>
                    </SortableColumn>
                  ))
                )}
                {provided.placeholder}
                {!loadStages && !isGuestConnected() && (
                  <Col span={5}>
                    <Card
                      title={`${t("tasks.AddNewStage", {
                        component: "Stage",
                      })}`}
                      bordered
                      bodyStyle={{ display: "none" }}
                    />
                    {showNewStatusForm ? (
                      <Form
                        onFinish={createNewStage}
                        onFinishFailed={(val) => console.log("error")}
                        layout="vertical"
                        style={{ paddingTop: "10px" }}
                        id="stage-form-in-task"
                      >
                        <Form.Item
                          name="statusName"
                          rules={[
                            {
                              required: true,
                              message: t("tasks.stageLabelError"),
                            },
                          ]}
                          label={t("tasks.stageLabel")}
                        >
                          <Input placeholder={t("tasks.stageLabel")} />
                        </Form.Item>
                        <Form.Item
                          name="winProbability"
                          label={t("tasks.WinProbability")}
                        >
                          <InputNumber
                            placeholder={t("tasks.WinProbability")}
                            addonAfter={"%"}
                            min={0}
                          />
                        </Form.Item>
                        <Form.Item name="color" label={t("tasks.color")}>
                          <GroupColors
                            setColor={setStatusColor}
                            color={statusColor}
                            size="small"
                          />
                        </Form.Item>
                        <Button onClick={resetCreateStageForm} type="link">
                          {t("form.cancel")}
                        </Button>

                        <Button
                          type="primary"
                          htmlType="submit"
                          block
                          icon={<CheckOutlined />}
                          form="stage-form-in-task"
                          loading={loadCreateStage}
                        >
                          {t("fields_management.drawerOkBtn")}
                        </Button>
                      </Form>
                    ) : (
                      <Button
                        type="dashed"
                        onClick={() => setShowNewStatusForm(true)}
                        block
                        icon={<PlusOutlined />}
                        style={{ marginTop: "18px" }}
                      >
                        {`${t("tasks.AddNewStage", {
                          component: "Stage",
                        })}`}
                      </Button>
                    )}
                  </Col>
                )}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      </Spin>
      {/* Reasons modal */}
      <Modal
        open={reasons !== null}
        title={t("sales.selectReasonTitle")}
        onCancel={() => setReasons(null)}
        centered
        footer={() => (
          <div className="left-1/2">
            <Button
              type="primary"
              form="reasons-form"
              htmlType="submit"
              loading={loadUpdateInFinalStages}
            >
              {t("wiki.Confirm")}
            </Button>
          </div>
        )}
      >
        <Form
          form={finalStageForm}
          layout="vertical"
          id="reasons-form"
          onFinish={handleSumitReasonsForm}
          onFieldsChange={handleChooseReasons}
        >
          <Form.Item
            name="reasonType"
            rules={[{ required: true, message: "Ce champs est obligatoire" }]}
          >
            <Radio.Group
              buttonStyle="solid"
              defaultValue={null}
              onChange={handleChangeReasonsItems}
              style={{ paddingTop: "10px", paddingBottom: "10px" }}
            >
              {reasons?.map((item, i) => (
                <Radio.Button value={item?.reason_type} key={item?.reason_type}>
                  {item?.reason_type === 1
                    ? t("sales.successReason")
                    : t("sales.failReason")}
                </Radio.Button>
              ))}
            </Radio.Group>
          </Form.Item>
          {selectedReasonType !== null && (
            <Form.Item
              name="reasons"
              rules={[{ required: true, message: "Ce champs est obligatoire" }]}
            >
              <Radio.Group
                style={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "left",
                }}
                defaultValue={null}
                options={reasons
                  ?.find((el) => el?.reason_type === selectedReasonType)
                  ?.reasons.map((item) => ({
                    value: item?.id,
                    label: item?.label,
                  }))}
              />
            </Form.Item>
          )}
        </Form>
      </Modal>
    </>
  );
});

KanbanBoard.propTypes = {
  selectedPipeline: PropTypes.number,
  setTaskToUpdate: PropTypes.func,
  columns: PropTypes.array,
  setColumns: PropTypes.func,
  setSelectedStageId: PropTypes.func,
  tasksTypes: PropTypes.array,
  loadUpdateTaskStage: PropTypes.bool,
  UpdateTaskStage: PropTypes.func,
};

KanbanBoard.defaultProps = {
  selectedPipeline: 0,
  setTaskToUpdate: () => {},
  columns: [],
  setColumns: () => {},
  setSelectedStageId: () => {},
  tasksTypes: [],
  loadUpdateTaskStage: false,
  UpdateTaskStage: null,
};

export default KanbanBoard;
