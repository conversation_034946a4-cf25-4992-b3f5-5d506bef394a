import React, { useState, useRef } from "react";
import {
  Settings,
  Code,
  Copy,
  Check,
  Download,
  Eye,
  Palette,
  MessageCircle,
  Globe,
  Shield,
  Bell,
  Users,
  Monitor,
  Smartphone,
  Tablet,
  PaletteIcon,
} from "lucide-react";
import { Button, Input, Switch, Tabs } from "antd";
import {
  CheckOutlined,
  CopyOutlined,
  DownloadOutlined,
  CodeOutlined,
  SettingOutlined,
  EyeOutlined,
} from "@ant-design/icons";

import { URL_ENV } from "index";
import { useTranslation } from "react-i18next";

const LiveChatConfig = () => {
  const [activeTab, setActiveTab] = useState("installation");
  const [copied, setCopied] = useState(false);
  const [t] = useTranslation("common");

  const [config, setConfig] = useState({
    chatTitle: "Support Client",
    welcomeMessage: "Bonjour ! Comment pouvons-nous vous aider ?",
    position: "bottom-right",
    theme: "blue",
    language: "fr",
    showOnline: true,
    autoOpen: false,
    soundEnabled: true,
    workingHours: "24/7",
  });
  const url = URL_ENV.REACT_APP_RMC_URL.replace("/admin.php", "");
  const codeRef = useRef(null);

  const installCode = `<!-- Live Chat Installation Code -->
<!-- Scripts requis -->
<script src="${url}/js/min/jquery.min.js"></script>
<!-- Not required if jQuery is already loaded -->
<script id="sbinit" src="${url}/js/main.js"></script>
`;
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(installCode);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error("Erreur lors de la copie:", err);
    }
  };

  const downloadCode = () => {
    const blob = new Blob([installCode], { type: "text/html" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "livechat-install.html";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const tabs = [
    {
      id: "installation",
      label: "Installation",
      icon: <Code className="h-4 w-4" />,
    },
    {
      id: "appearance",
      label: "Apparence",
      icon: <Palette className="h-4 w-4" />,
    },
    {
      id: "behavior",
      label: "Comportement",
      icon: <Settings className="h-4 w-4" />,
    },
    { id: "preview", label: "Aperçu", icon: <Eye className="h-4 w-4" /> },
  ];

  const items = [
    {
      key: "installation",
      label: (
        <span className="flex items-center gap-2">
          <CodeOutlined />
          Installation
        </span>
      ),
    },
    process.env.REACT_APP_BRANCH.includes("dev") && {
      key: "appearance",
      label: (
        <span className="flex items-center gap-2">
          <PaletteIcon size={14} />
          Apparence
        </span>
      ),
    },
    process.env.REACT_APP_BRANCH.includes("dev") && {
      key: "behavior",
      label: (
        <span className="flex items-center gap-2">
          <SettingOutlined />
          Comportement
        </span>
      ),
    },
    process.env.REACT_APP_BRANCH.includes("dev") && {
      key: "preview",
      label: (
        <span className="flex items-center gap-2">
          <EyeOutlined />
          Aperçu
        </span>
      ),
    },
  ];

  const positions = [
    { value: "bottom-right", label: "Bas droite", icon: "↘️" },
    { value: "bottom-left", label: "Bas gauche", icon: "↙️" },
    { value: "top-right", label: "Haut droite", icon: "↗️" },
    { value: "top-left", label: "Haut gauche", icon: "↖️" },
  ];

  const themes = [
    { value: "blue", label: "Bleu", color: "bg-blue-500" },
    { value: "green", label: "Vert", color: "bg-green-500" },
    { value: "purple", label: "Violet", color: "bg-purple-500" },
    { value: "orange", label: "Orange", color: "bg-orange-500" },
    { value: "red", label: "Rouge", color: "bg-red-500" },
  ];
  const openInNewTab = (url) => {
    const newWindow = window.open(url, "_blank", "noopener,noreferrer");
    if (newWindow) newWindow.opener = null;
  };
  return (
    <div className="p-4">
      {/* Header */}
      <Tabs
        // tabPosition="left"
        type="card"
        activeKey={activeTab}
        onChange={setActiveTab}
        items={items}
        className="w-full"
      />
      <div className="mx-auto max-w-7xl  ">
        <div className="grid lg:grid-cols-4">
          {/* Sidebar */}

          {/* <div className="lg:col-span-1">
            <div className="rounded-lg border border-gray-200 bg-white shadow-sm">
              <nav className="p-2">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`my-1 flex w-full items-center space-x-3 rounded-md bg-white px-4 py-3 text-sm font-medium transition-colors hover:cursor-pointer hover:bg-blue-50 ${
                      activeTab === tab.id
                        ? "border-r-2 border-blue-500 bg-blue-50 text-blue-700"
                        : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                    }`}
                  >
                    {tab.icon}
                    <span>{tab.label}</span>
                  </button>
                ))}
              </nav>
            </div>
          </div> */}

          {/* Main Content */}
          <div className="lg:col-span-4">
            <div className="rounded-lg border border-gray-200 bg-white  shadow-sm">
              {/* Installation Tab */}
              {activeTab === "installation" && (
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <h2 className="text-xl font-semibold text-gray-900">
                      Code d'Installation
                    </h2>
                    <div className="flex space-x-3">
                      <Button
                        onClick={copyToClipboard}
                        type="primary"
                        icon={copied ? <CheckOutlined /> : <CopyOutlined />}
                      >
                        <span>
                          {copied
                            ? t("chat.action.copied")
                            : t("chat.action.copy")}
                        </span>
                      </Button>
                      <Button
                        onClick={downloadCode}
                        icon={<DownloadOutlined />}
                      >
                        <span>{t("import.download")}</span>
                      </Button>
                    </div>
                  </div>

                  <div className="rounded-lg border bg-gray-900 p-4">
                    <pre
                      ref={codeRef}
                      className="overflow-x-auto whitespace-pre-wrap font-mono text-sm text-gray-100"
                    >
                      {installCode}
                    </pre>
                  </div>

                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
                      <div className="mb-2 flex items-center space-x-2">
                        <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                        <h3 className="font-medium text-blue-900">
                          {t("liveChat.installationIn")} &lt;head&gt;
                        </h3>
                      </div>
                      <p className="text-sm text-blue-700">
                        {t("liveChat.recomandedForOptimalLoading")}.{" "}
                        {t("liveChat.pasteTheCodeBeforeThe")} &lt;/head&gt;.
                      </p>
                    </div>
                    <div className="rounded-lg border border-green-200 bg-green-50 p-4">
                      <div className="mb-2 flex items-center space-x-2">
                        <div className="h-2 w-2 rounded-full bg-green-500"></div>
                        <h3 className="font-medium text-green-900">
                          {t("liveChat.installationIn")} &lt;footer&gt;
                        </h3>
                      </div>
                      <p className="text-sm text-green-700">
                        {t("liveChat.validAlternative")}.{" "}
                        {t("liveChat.pasteTheCodeBeforeThe")} &lt;/body&gt;.
                      </p>
                    </div>
                  </div>

                  <div className="rounded-lg border border-indigo-200 bg-indigo-50 p-4">
                    <div className="mb-3 flex items-center space-x-2">
                      <div className="h-2 w-2 rounded-full bg-indigo-600"></div>
                      <h3 className="font-medium text-indigo-700">
                        {t("liveChat.testUrl")}
                      </h3>
                    </div>
                    <div className="flex items-center space-x-3">
                      <code className="flex-1 rounded-md bg-white px-3 py-1.5 text-sm text-gray-500">
                        {url}
                        /chat.php
                      </code>
                      <Button onClick={() => openInNewTab(url + "/chat.php")}>
                        {t("emailAccounts.check")}
                      </Button>
                    </div>
                  </div>
                </div>
              )}

              {/* Appearance Tab */}
              {activeTab === "appearance" && (
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold text-gray-900">
                    Personnalisation de l'Apparence
                  </h2>

                  <div className="grid gap-6 md:grid-cols-2">
                    <div>
                      <label className="mb-2 block text-sm font-medium text-gray-700">
                        Titre du Chat
                      </label>
                      <Input
                        type="text"
                        value={config.chatTitle}
                        onChange={(e) =>
                          setConfig({ ...config, chatTitle: e.target.value })
                        }
                        placeholder="Support Client"
                      />
                    </div>

                    <div>
                      <label className="mb-2 block text-sm font-medium text-gray-700">
                        Message de Bienvenue
                      </label>
                      <Input
                        type="text"
                        value={config.welcomeMessage}
                        onChange={(e) =>
                          setConfig({
                            ...config,
                            welcomeMessage: e.target.value,
                          })
                        }
                        placeholder="Comment pouvons-nous vous aider ?"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="mb-3 block text-sm font-medium text-gray-700">
                      Position du Widget
                    </label>
                    <div className="grid grid-cols-2 gap-3 md:grid-cols-4">
                      {positions.map((pos) => (
                        <button
                          key={pos.value}
                          onClick={() =>
                            setConfig({ ...config, position: pos.value })
                          }
                          className={`rounded-lg border-2 p-4 text-center transition-all hover:cursor-pointer ${
                            config.position === pos.value
                              ? "border-blue-500 bg-blue-50 text-blue-700"
                              : "border-gray-200 bg-white text-gray-600 hover:border-gray-300 hover:bg-gray-50"
                          }`}
                        >
                          <div className="mb-2 text-2xl">{pos.icon}</div>
                          <div className="text-sm font-medium">{pos.label}</div>
                        </button>
                      ))}
                    </div>
                  </div>

                  <div>
                    <label className="mb-3 block text-sm font-medium text-gray-700">
                      Thème de Couleur
                    </label>
                    <div className="grid grid-cols-3 gap-3 md:grid-cols-5">
                      {themes.map((theme) => (
                        <button
                          key={theme.value}
                          onClick={() =>
                            setConfig({ ...config, theme: theme.value })
                          }
                          className={`rounded-lg border-2 p-4 text-center transition-all ${
                            config.theme === theme.value
                              ? "border-blue-500 bg-blue-50"
                              : "border-gray-200 bg-white hover:border-gray-300 hover:bg-gray-50"
                          }`}
                        >
                          <div
                            className={`h-8 w-8 ${theme.color} mx-auto mb-2 rounded-md`}
                          ></div>
                          <div className="text-sm font-medium text-gray-700">
                            {theme.label}
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Behavior Tab */}
              {activeTab === "behavior" && (
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold text-gray-900">
                    Comportement du Chat
                  </h2>

                  <div className="grid gap-6 md:grid-cols-2">
                    <div className="rounded-lg border border-gray-200 bg-gray-50 p-6">
                      <div className="mb-4 flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <Users className="h-5 w-5 text-blue-600" />
                          <span className="font-medium text-gray-900">
                            Afficher le statut en ligne
                          </span>
                        </div>
                        <Switch
                          onChange={() =>
                            setConfig({
                              ...config,
                              showOnline: !config.showOnline,
                            })
                          }
                          checked={config.showOnline}
                        />
                      </div>
                      <p className="text-sm text-gray-600">
                        Indique si vos agents sont disponibles
                      </p>
                    </div>

                    <div className="rounded-lg border border-gray-200 bg-gray-50 p-6">
                      <div className="mb-4 flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <Eye className="h-5 w-5 text-blue-600" />
                          <span className="font-medium text-gray-900">
                            Ouverture automatique
                          </span>
                        </div>
                        <Switch
                          onChange={() =>
                            setConfig({ ...config, autoOpen: !config.autoOpen })
                          }
                          checked={config.autoOpen}
                        />
                      </div>
                      <p className="text-sm text-gray-600">
                        Ouvre automatiquement le chat au chargement
                      </p>
                    </div>

                    <div className="rounded-lg border border-gray-200 bg-gray-50 p-6">
                      <div className="mb-4 flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <Bell className="h-5 w-5 text-blue-600" />
                          <span className="font-medium text-gray-900">
                            Notifications sonores
                          </span>
                        </div>
                        <Switch
                          onChange={() =>
                            setConfig({
                              ...config,
                              soundEnabled: !config.soundEnabled,
                            })
                          }
                          checked={config.soundEnabled}
                        />
                      </div>
                      <p className="text-sm text-gray-600">
                        Son lors de nouveaux messages
                      </p>
                    </div>

                    <div className="rounded-lg border border-gray-200 bg-gray-50 p-6">
                      <div className="mb-4 flex items-center space-x-3">
                        <Globe className="h-5 w-5 text-blue-600" />
                        <span className="font-medium text-gray-900">
                          Langue
                        </span>
                      </div>
                      <select
                        value={config.language}
                        onChange={(e) =>
                          setConfig({ ...config, language: e.target.value })
                        }
                        className="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="fr">Français</option>
                        <option value="en">English</option>
                        <option value="ar">العربية</option>
                        <option value="es">Español</option>
                      </select>
                    </div>
                  </div>
                </div>
              )}

              {/* Preview Tab */}
              {activeTab === "preview" && (
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold text-gray-900">
                    Aperçu du Widget
                  </h2>

                  <div className="grid gap-6 lg:grid-cols-3">
                    <div className="text-center">
                      <div className="mb-4 flex items-center justify-center space-x-2">
                        <Monitor className="h-5 w-5 text-blue-600" />
                        <h3 className="font-medium text-gray-900">Desktop</h3>
                      </div>
                      <div className="relative h-40 rounded-lg border border-gray-200 bg-gray-100 p-4">
                        <div
                          className={`absolute ${
                            config.position.includes("right")
                              ? "right-3"
                              : "left-3"
                          } ${
                            config.position.includes("bottom")
                              ? "bottom-3"
                              : "top-3"
                          } flex h-12 w-12 animate-pulse items-center justify-center rounded-full  shadow-lg`}
                          style={{ backgroundColor: config.theme }}
                        >
                          <MessageCircle className="h-6 w-6 text-white" />
                        </div>
                      </div>
                    </div>

                    <div className="text-center">
                      <div className="mb-4 flex items-center justify-center space-x-2">
                        <Tablet className="h-5 w-5 text-blue-600" />
                        <h3 className="font-medium text-gray-900">Tablette</h3>
                      </div>
                      <div className="relative mx-auto h-40 max-w-32 rounded-lg border border-gray-200 bg-gray-100 p-4">
                        <div
                          className={`absolute ${
                            config.position.includes("right")
                              ? "right-2"
                              : "left-2"
                          } ${
                            config.position.includes("bottom")
                              ? "bottom-2"
                              : "top-2"
                          } flex h-10 w-10 animate-pulse items-center justify-center rounded-full  shadow-lg`}
                          style={{ backgroundColor: config.theme }}
                        >
                          <MessageCircle className="h-5 w-5 text-white" />
                        </div>
                      </div>
                    </div>

                    <div className="text-center">
                      <div className="mb-4 flex items-center justify-center space-x-2">
                        <Smartphone className="h-5 w-5 text-blue-600" />
                        <h3 className="font-medium text-gray-900">Mobile</h3>
                      </div>
                      <div className="relative mx-auto h-40 max-w-20 rounded-lg border border-gray-200 bg-gray-100 p-4">
                        <div
                          className={`absolute ${
                            config.position.includes("right")
                              ? "right-1"
                              : "left-1"
                          } ${
                            config.position.includes("bottom")
                              ? "bottom-1"
                              : "top-1"
                          } flex h-8 w-8 animate-pulse items-center justify-center rounded-full  shadow-lg`}
                          style={{ backgroundColor: config.theme }}
                        >
                          <MessageCircle className="h-4 w-4 text-white" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <h3 className="pb-0.5 text-lg font-medium text-gray-900">
                    Configuration Actuelle
                  </h3>
                  <div className="rounded-lg border border-gray-200 bg-gray-50 p-6">
                    <div className="grid gap-4 text-sm md:grid-cols-2">
                      <div className="flex items-center gap-x-2">
                        <span className="text-gray-600">Titre:</span>
                        <span className="font-medium text-gray-900">
                          {config.chatTitle}
                        </span>
                      </div>
                      <div className="flex items-center gap-x-2">
                        <span className="text-gray-600">Position:</span>
                        <span className="font-medium text-gray-900">
                          {config.position}
                        </span>
                      </div>
                      <div className="flex items-center gap-x-2">
                        <span className="text-gray-600">Thème:</span>
                        <span className="font-medium text-gray-900">
                          {config.theme}
                        </span>
                      </div>
                      <div className="flex items-center gap-x-2">
                        <span className="text-gray-600">Langue:</span>
                        <span className="font-medium text-gray-900">
                          {config.language}
                        </span>
                      </div>
                      <div className="flex items-center gap-x-2">
                        <span className="text-gray-600">Statut en ligne:</span>
                        <span
                          className={`font-medium ${
                            config.showOnline
                              ? "text-green-600"
                              : "text-gray-600"
                          }`}
                        >
                          {config.showOnline ? "Activé" : "Désactivé"}
                        </span>
                      </div>
                      <div className="flex items-center gap-x-2">
                        <span className="text-gray-600">Ouverture auto:</span>
                        <span
                          className={`font-medium ${
                            config.autoOpen ? "text-green-600" : "text-gray-600"
                          }`}
                        >
                          {config.autoOpen ? "Activé" : "Désactivé"}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LiveChatConfig;
