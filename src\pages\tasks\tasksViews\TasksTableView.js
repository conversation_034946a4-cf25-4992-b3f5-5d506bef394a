/**
 * @name TasksTableView
 *
 * @description `TasksTableView` component is responsible for displaying the list of activities in a table (This is the main view in the activities module).
 *
 * @param {Array} pipelines List of pipelines.
 * @param {Array} tasksData List of activities.
 * @param {Array} tasksTypes List of activities types.
 * @param {Array} checkedColumns List of displayed table columns.
 * @param {Array} appliedFilters List of active filters.
 * @param {Number} selectedPipeline Selected pipeline (it behaves as a filter).
 * @param {Number} total Total activities retrieved from the db.
 * @param {Number} pageNumber The actual table page number.
 * @param {Number} headerHeight Header's height (this is used in viewsphere).
 * @param {Boolean} loadTasks Shows loader on table while loading activities.
 * @param {Boolean} getTasksError Is true when getTasks returns an error.
 * @param {Boolean} collapsedInViewSphere This is used in viewsphere.
 * @param {Boolean} loadUpdateTaskStage Show loader when updating stage.
 * @param {String} source The source from where the activities table is being called.
 * @param {Function} setRoomActivityId Sets 'roomActivityId' state.
 * @param {Function} setTaskToUpdate Sets 'taskToUpdate' state.
 * @param {Function} setTasksData Sets 'tasksData' state.
 * @param {Function} setActivityLabel Sets 'activityLabel' state.
 * @param {Function} setTotal Sets 'total' state.
 * @param {Function} setOpenActivity360 Sets 'openActivity360' state.
 * @param {Function} setUpdateFilters Sets 'updateFilters' state.
 * @param {Function} setPageNumber Sets 'pageNumber' state.
 * @param {Function} setOpenElementDetails Sets 'openElementDetails' state.
 * @param {Function} setElementDetails Sets 'elementDetails' state.
 * @param {Function} setAppliedFilters Sets 'appliedFilters' state.
 * @param {Function} setLimit Sets 'limit' state.
 * @param {Function} setStageIdToFilter Sets 'stageIdToFilter' state.
 * @param {Function} getTasks Get activities from db api.
 * @param {Function} UpdateTaskStage Update stage api trigger.
 * @param {Function} setOpenDrawerEditTask Sets 'openDrawerEditTask' state.
 * @param {Function} setDetailsTask Sets 'detailsTask' state.
 *
 * @returns {JSX.Element} .
 */

import { useState, useEffect, useMemo, useCallback } from "react";
import {
  Table,
  Button,
  Badge,
  Tooltip,
  Dropdown,
  Select,
  Typography,
  message,
  Space,
  Tag,
} from "antd";
import {
  DeleteOutlined,
  RestOutlined,
  DownOutlined,
  LoadingOutlined,
  CopyOutlined,
  CheckOutlined,
} from "@ant-design/icons";
import { TbFlag3Filled, TbFlag3 } from "react-icons/tb";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import { useTranslation } from "react-i18next";
import "moment/min/locales";
import { useSelector, useDispatch } from "react-redux";
import calendar from "dayjs/plugin/calendar";
import { MdVideoCameraFront } from "react-icons/md";

import { toastNotification } from "../../../components/ToastNotification";
import ChoiceIcons from "../../components/ChoiceIcons";
import "../style.css";
import MainService from "../../../services/main.service";
import Confirm from "../../../components/GenericModal";
import { getName } from "../../layouts/chat/utils/ConversationUtils";
import { setOpenTaskDrawer } from "../../../new-redux/actions/tasks.actions/handleTaskDrawer";
import { updateViewAccordingToNotification } from "../../../utils/updateViewAccordingToNotification";
import {
  setActiveTasksFilters,
  setNotificationPayload,
  setTasksFilters,
} from "../../../new-redux/actions/tasks.actions/realTime";
import AvatarGroup from "../../../components/AvatarGroup";
import DropdownTask from "../../../components/DropdownTask";
import { useWindowSize } from "../../clients&users/components/WindowSize";
import { URL_ENV } from "index";
import { AvatarChat } from "components/Chat";
import getContactDataAndDispatch from "pages/clients&users/helpers/getContactDataAndDispatch";
import { formatDateForDisplay } from "../helpers/formatDateToDisplay";
import {
  displayPriorityColor,
  handlePriorityLabelOnHover,
  prioritiesList,
} from "../helpers/handlePriorities";
import { handleReload } from "../helpers/handleCheck";
import { getValueByKey } from "../helpers/calculateSum";
import { setOpenTaskRoomDrawer } from "new-redux/actions/tasks.actions/handleTaskRoomDrawer";
import { EXTENSIONS_ARRAY } from "../helpers/calculateSum";
import ActionsComponent from "../ActionsComponent";
import { closeDrawerChat } from "new-redux/actions/voip.actions/handleDrawerChat";
import { setChatSelectedConversation } from "new-redux/actions/chat.actions";
import EmptyPage from "components/EmptyPage";
import { isGuestConnected } from "utils/role";
import PopOverMembersTask from "../activityDetails/PopOverMembersTask";
import { familyIcons } from "pages/components/DetailsProfile/ViewSphere2";
import ErrorState from "../ErrorState";
import SubtasksIndicator from "../helpers/SubtasksIndicator";
import { Refs_IDs } from "components/tour/tourConfig";

dayjs.extend(relativeTime);
dayjs.extend(calendar);

const TasksTableView = ({
  tasksData,
  setTasksData,
  tasksTypes,
  loadTasks,
  setTaskToUpdate,
  setPageNumber,
  setLimit,
  total,
  setTotal,
  pipelines,
  UpdateTaskStage,
  loadUpdateTaskStage,
  setStageIdToFilter,
  selectedPipeline,
  getTasks,
  getTasksError,
  setOpenElementDetails,
  setElementDetails,
  checkedColumns,
  setOpenActivity360,
  pageNumber,
  setActivityLabel,
  setAppliedFilters,
  setRoomActivityId,
  setUpdateFilters,
  source = "",
  setOpenDrawerEditTask = () => {},
  setDetailsTask = () => {},
  collapsedInViewSphere = false,
  headerHeight = 0,
  guestsList,
  ownersList,
  setGuestsSearchQuery,
  guestsSearchQuery,
  guestsListLastPage,
  setGuestsListPage,
  loadGuests,
  setCheckedItems,
  setFollowersSearchQuery,
  followersSearchQuery,
  followersListPage,
  setFollowersListPage,
  setSelectedFamilyMembers,
  totalEntities,
  followersListLastPage,
  loadOwners,
  limit,
  checkedItems,
  guestsListPage,
  setSorter,
  sorterActivity,
}) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [loadingUpdateStage, setLoadingUpdateStage] = useState(false);
  const [isCopiedToClipboard, setIsCopiedToClipboard] = useState(false);
  const [filterParticipantsValue, setFilterParticipantsValue] = useState(0);

  const [t] = useTranslation("common");
  const { user } = useSelector((state) => state?.user);
  const {
    isUserNotified,
    selectedViewInTask,
    taskNotifActionType,
    taskNotifPayload,
    tasksFilters,
    activeFilters,
  } = useSelector((state) => state?.TasksRealTime);
  const { openView360InDrawer } = useSelector((state) => state?.vue360);
  const dispatch = useDispatch();
  const windowSize = useWindowSize();
  // const {
  //   checkedItems,
  //   guestsListPage,
  // } = useCompAct360();
  // Selected rows ids.
  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  // Dispatch change priority API.
  const updatePriority = async (id, newPriorityStatus) => {
    try {
      await MainService.updateTaskPriority(id, newPriorityStatus);
      getTasks();
    } catch (error) {
      console.log(`Error ${error}`);
      message.error("Something went wrong!", 4.5);
    }
  };

  //Handle open edit activity drawer.
  const editTask = useCallback(
    ({ label, id, upload, files }) => {
      // Destructure props for easier access
      setActivityLabel(label);
      setTaskToUpdate(id);
      // Conditionally handle drawer opening and task details
      if (source === "viewSphere") {
        setOpenDrawerEditTask(true);
        // Determine the upload value based on its current state
        const taskUpload =
          upload === 0 ? [] : Array.isArray(upload) ? upload : files;
        setDetailsTask((prevState) => ({
          ...prevState,
          ...{ label, id, upload: taskUpload },
        }));
      } else {
        dispatch(setOpenTaskDrawer(true));
      }
    },
    [source, dispatch]
  );

  //Handle open chat drawer.
  const openChat = (id) => {
    dispatch(setOpenTaskRoomDrawer(true));
    setRoomActivityId(id);
  };

  // Handle open activity overview modal.
  const handleOpenActivityIn360 = (props) => {
    dispatch(
      setChatSelectedConversation({
        selectedConversation: null,
      })
    );
    dispatch(closeDrawerChat());
    setTaskToUpdate(props?.id);
    setOpenActivity360(true);
  };

  // Data that will be displayed on table.
  const dataSource = useMemo(() => {
    // Convert tasksTypes to a map for faster lookups
    const tasksTypeMap = tasksTypes?.reduce((acc, el) => {
      acc[Number(el?.id)] = el;
      return acc;
    }, {});
    return tasksData?.map((element) => {
      const activityType = tasksTypeMap[Number(element?.tasks_type_id)];
      return {
        key: element?.id,
        label: element,
        activityType,
        priority: element,
        activityId: element?.id,
        startDate: element,
        dueDate: element,
        pipeline: element,
        owner: element,
        creator: element?.creator,
        guests: element,
        followers: element,
        organization: element,
        contact: element,
        code: element,
        moduleElement: {
          familyLabel: element?.family_label,
          elementLabel: element?.element_label,
          id: element?.element_id,
          familyId: element?.family_id,
        },
      };
    });
  }, [tasksData, tasksTypes]);

  //Handle priority filters on table.
  const handlePriorityFilter = (filterParams) => {
    let array = [];
    let index = activeFilters?.findIndex((el) =>
      el?.hasOwnProperty("priorityFilter")
    );

    if (filterParams !== null && filterParams?.length > 0) {
      if (index > -1) {
        array = activeFilters?.map((el, i) =>
          i === index ? { priorityFilter: filterParams } : el
        );
      } else {
        array = [...activeFilters, { priorityFilter: filterParams }];
      }
    } else {
      array = activeFilters?.filter(
        (el) => !el?.hasOwnProperty("priorityFilter")
      );
    }
    setAppliedFilters(array);
  };

  // Handle filter table by stage.
  const onChange = (pagination, filters, sorter, extra) => {
    if (extra?.action !== "paginate") {
      dispatch(setTasksFilters({ filters, sorter }));
      dispatch(
        setActiveTasksFilters([
          ...activeFilters,
          { priorityFilter: filters?.priority },
        ])
      );
      handlePriorityFilter(filters?.priority);
      let stagesIds = filters?.pipeline?.filter(
        (element) => typeof element !== "string"
      );
      if (stagesIds) {
        setStageIdToFilter(stagesIds);
      } else if (filters?.pipeline === null) {
        setStageIdToFilter([]);
      }
    }
    if (sorter) {
      setSorter({
        sort_field: sorter?.field,
        sort_direction:
          sorter?.order === "ascend"
            ? "ASC"
            : sorter?.order === "descend"
            ? "DESC"
            : "",
      });
      localStorage.setItem(
        "sorter_activity",
        JSON.stringify({
          sort_field: sorter?.field,
          sort_direction:
            sorter?.order === "ascend"
              ? "ASC"
              : sorter?.order === "descend"
              ? "DESC"
              : "",
        })
      );
    } else {
      setSorter({
        sort_field: "",
        sort_direction: "",
      });
      localStorage.setItem(
        "sorter",
        JSON.stringify({
          sort_field: "",
          sort_direction: "",
        })
      );
    }
  };

  //Handle click on femily element on table.
  const handleClickOnElement = (props, record) => {
    getContactDataAndDispatch(
      props?.familyId,
      props?.elementLabel,
      { key: props?.id },
      record,
      dispatch,
      null,
      () => {}
    );
    setElementDetails((prev) => ({
      ...prev,
      id: props?.id,
      module: props?.familyId,
    }));
    setOpenElementDetails(true);
  };
  // Pipelines and stages options array
  const stagesLists = useMemo(() => {
    return pipelines?.map((pipeline) => {
      const stages = pipeline?.stages?.map((stage) => {
        const percent = stage?.percent;
        return {
          label: (
            <>
              <Badge color={stage?.color} style={{ marginRight: "10px" }} />
              {stage?.label}
              {percent && percent !== null && Number(percent) !== 0 && (
                <span className="text-[#8c8c8c]">{`(${percent}%)`}</span>
              )}
            </>
          ),
          value: stage?.id,
        };
      });

      return {
        label: pipeline?.label,
        value: pipeline?.pipeline_key,
        options: stages,
      };
    });
  }, [pipelines]);

  //Copy clicked id on clipboard.
  const copyIdToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      setIsCopiedToClipboard(true);
      setTimeout(() => setIsCopiedToClipboard(false), 2000);
    } catch (err) {
      console.error("Failed to copy: ", err);
    }
  };

  // const handleRemoveMembers = async (role, id) => {
  //   try {
  //     let payload = {
  //       role: role,
  //       member_id: id,
  //     };
  //     const response = await MainService.removeMembers(
  //       singleTaskData?.id,
  //       payload
  //     );
  //     setSingleTaskData(response?.data?.data);
  //     setCountChanges((prev) => prev + 1);
  //   } catch (error) {
  //     console.log(`Error ${error}`);
  //   }
  // };
  // Table's default columns.
  const defaultColumns = [
    {
      title: t("tasks.tableType"),
      dataIndex: "activityType",
      key: "activityType",
      width: 50,
      fixed: "left",
      primary: true,
      render: (props) => (
        <Tooltip title={props?.label}>
          <>
            <ChoiceIcons fontSize="18px" icon={props?.icons} />
          </>
        </Tooltip>
      ),
    },
    {
      title: t("tasks.tableLabel"),
      dataIndex: "label",
      key: "label",
      fixed: "left",
      width: 180,
      primary: true,
      sorter: true,
      sortDirections: ["ascend", "descend"],
      sortOrder:
        sorterActivity?.sort_field === "label"
          ? sorterActivity?.sort_direction === "ASC"
            ? "ascend"
            : sorterActivity?.sort_direction === "DESC"
            ? "descend"
            : ""
          : "",
      render: (props, _, i) => {
        const canUpdate =
          props?.guests?.some((el) => el.id === user.id) ||
          props?.followers?.some((el) => el.id === user.id) ||
          props?.owner_id?.id === user.id ||
          props?.creator?.id === user.id;
        return (
          <div className="flex w-full flex-row items-center">
            {props?.todoList > 0 && (
              <SubtasksIndicator subtasksNumber={props.todoList} />
            )}

            <Typography.Text
              className={`max-w-[180px] overflow-hidden text-ellipsis whitespace-nowrap text-[16px] text-xs ${
                source === "viewSphere" && !canUpdate
                  ? ""
                  : "text-[#1677ff] hover:cursor-pointer hover:text-[#69b1ff]"
              }`}
              ellipsis={
                source === "viewSphere" && !canUpdate
                  ? undefined
                  : { tooltip: true }
              }
              onClick={
                source === "viewSphere" && !canUpdate
                  ? undefined
                  : () => handleOpenActivityIn360(props)
              }
            >
              {props?.label}
            </Typography.Text>

            {props?.visio_in_progress === 1 && (
              <Tooltip title={t("tasks.visioInProgress")}>
                <MdVideoCameraFront className="ml-2 animate-pulse text-sm text-red-500" />
              </Tooltip>
            )}

            <div
              className="ml-auto flex items-center"
              ref={i === 0 ? Refs_IDs.moreOptionsTask : null}
            >
              <DropdownTask
                source={source === "viewSphere" ? "viewSphere" : "activity"}
                props={props}
                handleDelete={deleteTask}
                editTask={editTask}
                handleOpenActivityIn360={handleOpenActivityIn360}
                openChat={openChat}
              />
            </div>
          </div>
        );
      },
    },
    {
      title: t("tasks.tablePriority"),
      dataIndex: "priority",
      key: "priority",
      width: 90,
      align: "center",
      render: (props) => (
        <Dropdown
          menu={{
            items: prioritiesList(),
            onClick: (e) => updatePriority(props?.id, { priority: e?.key }),
            selectedKeys: [props?.priority],
          }}
          trigger={["click"]}
          open={Boolean(props?.can_update_task === 0) ? false : undefined}
        >
          {props?.priority ? (
            <Tooltip title={handlePriorityLabelOnHover(props?.priority)}>
              <TbFlag3Filled
                id={Refs_IDs.choicePriorityTask}
                style={{
                  fontSize: "18px",
                  cursor:
                    props?.can_update_task === 0 ? "not-allowed" : "pointer",
                  color: displayPriorityColor(props?.priority),
                }}
              />
            </Tooltip>
          ) : (
            <Tooltip title={t("tasks.setPriority")}>
              <TbFlag3
                id={Refs_IDs.choicePriorityTask}
                style={{
                  fontSize: "18px",
                  cursor:
                    props?.can_update_task === 0 ? "not-allowed" : "pointer",
                  color: "#bfbfbf",
                }}
              />
            </Tooltip>
          )}
        </Dropdown>
      ),
      filters:
        source === "viewSphere"
          ? false
          : prioritiesList()?.map((el) => ({
              text: (
                <Badge
                  color={displayPriorityColor(el?.value)}
                  text={el?.title}
                />
              ),
              value: el?.value,
            })),
      filteredValue:
        source !== "viewSphere" &&
        activeFilters &&
        getValueByKey(activeFilters, "priorityFilter")?.priorityFilter
          ? getValueByKey(activeFilters, "priorityFilter")?.priorityFilter
          : [],
    },
    {
      title: t("tasks.startDate"),
      dataIndex: "startDate",
      key: "startDate",
      width: 120,
      sorter: true,
      sortDirections: ["ascend", "descend"],
      sortOrder:
        sorterActivity?.sort_field === "startDate"
          ? sorterActivity?.sort_direction === "ASC"
            ? "ascend"
            : sorterActivity?.sort_direction === "DESC"
            ? "descend"
            : ""
          : "",
      render: (props) =>
        props?.start_date !== null ? (
          <Tooltip title={`${props?.start_date} ${props?.start_time}`}>
            {formatDateForDisplay(
              `${props?.start_date} ${props?.start_time}`,
              `${user?.location?.date_format} ${user?.location?.time_format}`,
              user,
              t
            )}
          </Tooltip>
        ) : (
          <Typography.Link
            onClick={() => {
              editTask(props);
            }}
          >
            {t("tasks.addDate")}
          </Typography.Link>
        ),
    },
    {
      title: t("tasks.tableEndDate"),
      dataIndex: "dueDate",
      key: "dueDate",
      width: 120,
      sorter: true,
      sortDirections: ["ascend", "descend"],
      sortOrder:
        sorterActivity?.sort_field === "dueDate"
          ? sorterActivity?.sort_direction === "ASC"
            ? "ascend"
            : sorterActivity?.sort_direction === "DESC"
            ? "descend"
            : ""
          : "",
      render: (props) =>
        props?.end_date != null ? (
          <Tooltip
            title={
              <div className="flex flex-col items-center justify-center">
                <p>{`${props?.end_date} ${props?.end_time}`}</p>
                <p>{props?.is_overdue && t("tasks.overdueTaskInfo")}</p>
              </div>
            }
          >
            <Typography.Text type={props?.is_overdue && "danger"}>
              {formatDateForDisplay(
                `${props?.end_date} ${props?.end_time}`,
                `${user?.location?.date_format} ${user?.location?.time_format}`,
                user,
                t
              )}
            </Typography.Text>
          </Tooltip>
        ) : (
          <Typography.Link
            onClick={() => {
              editTask(props);
            }}
          >
            {t("tasks.addDate")}
          </Typography.Link>
        ),
    },
    ...(source !== "viewSphere"
      ? [
          {
            title: "Pipeline/Stage",
            dataIndex: "pipeline",
            key: "pipeline",
            width: 180,
            render: (props) => {
              return (
                <>
                  <Select
                    ref={Refs_IDs.selectPipelineTask}
                    suffixIcon={
                      <DownOutlined
                        style={{
                          color: "rgba(0,0,0,0.25)",
                          fontSize: "10px",
                          pointerEvents: "none",
                        }}
                      />
                    }
                    style={{
                      width: "100%",
                      padding: 0,
                      backgroundColor: "#0000000a",
                      borderRadius: "6px",
                    }}
                    placeholder={t("tasks.choose")}
                    bordered={false}
                    value={
                      props?.stage_id
                        ? {
                            label: (
                              <div>
                                <span>{props?.pipeline_label}</span> /{" "}
                                <span
                                  style={{
                                    color: pipelines
                                      ?.find(
                                        (el) =>
                                          el.label === props?.pipeline_label
                                      )
                                      ?.stages?.find(
                                        (item) => item.id === props?.stage_id
                                      )?.color,
                                  }}
                                >
                                  {props?.stage_label}
                                </span>{" "}
                              </div>
                            ),
                            value: props?.stage_id,
                          }
                        : null
                    }
                    onChange={(e) => {
                      UpdateTaskStage(
                        {
                          "task_id[]": props?.id,
                          new_stage_id: e,
                        },
                        "singleUpdate"
                      );
                    }}
                    options={stagesLists}
                    popupMatchSelectWidth={false}
                    disabled={
                      source === "viewSphere"
                        ? props?.in_task === 0 ||
                          (props?.in_task === 1 && props?.is_follower === 1)
                        : props?.is_follower === 1
                    }
                  />
                </>
              );
            },
            filteredValue:
              source === "viewSphere"
                ? false
                : tasksFilters &&
                  tasksFilters?.filters &&
                  tasksFilters?.filters?.pipeline &&
                  tasksFilters?.filters?.pipeline.map((el) => `${el}`),
            filters: pipelines?.map((pipeline) => ({
              text: pipeline?.label,
              value: pipeline?.pipeline_key,
              disabled: true,
              disableCheckbox: true,
              children: pipeline?.stages?.map((stage) => ({
                text: stage?.label,
                value: stage?.id,
              })),
            })),
            filterMode: "tree",
          },
        ]
      : [
          {
            title: "Pipeline/Stage",
            dataIndex: "pipeline",
            key: "pipeline",
            width: 180,
            render: (props) => {
              const canUpdate =
                props?.guests?.some((el) => el.id === user.id) ||
                props?.followers?.some((el) => el.id === user.id) ||
                props?.owner_id?.id === user.id ||
                props?.creator?.id === user.id;
              return (
                <>
                  <Select
                    ref={Refs_IDs.selectPipelineTask}
                    suffixIcon={
                      loadUpdateTaskStage ? (
                        <LoadingOutlined
                          style={{ color: "#000", fontSize: "10px" }}
                        />
                      ) : (
                        <DownOutlined
                          style={{
                            color: "#000",
                            fontSize: "10px",
                            pointerEvents: "none",
                          }}
                        />
                      )
                    }
                    style={{
                      width: "100%",
                      padding: 0,
                      backgroundColor: "#0000000a",
                      borderRadius: "6px",
                    }}
                    placeholder={t("tasks.choose")}
                    bordered={false}
                    value={
                      props?.stage_id
                        ? {
                            label: `${props?.pipeline_label} / ${props?.stage_label}`,
                            value: props?.stage_id,
                          }
                        : null
                    }
                    onChange={(e) => {
                      UpdateTaskStage(
                        {
                          "task_id[]": props?.id,
                          new_stage_id: e,
                        },
                        "singleUpdate"
                      );
                    }}
                    options={stagesLists}
                    popupMatchSelectWidth={false}
                    disabled={!canUpdate}
                  />
                </>
              );
            },
          },
        ]),
    {
      title: t("tasks.creatorRole"),
      dataIndex: "creator",
      key: "creator",
      width: 80,
      render: (props) => (
        <ActionsComponent elementValue={props}>
          <AvatarChat
            fontSize={"0.7rem"}
            className={"mx-1.5 flex items-center justify-center"}
            height={"32px"}
            width={"32px"}
            url={`${
              URL_ENV?.REACT_APP_BASE_URL + URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
            }${props?.avatar}`}
            hasImage={EXTENSIONS_ARRAY?.includes(
              props?.avatar?.split(".")?.pop()
            )}
            name={getName(props?.label, "avatar")}
            type="user"
          />
        </ActionsComponent>
      ),
    },
    {
      title: t("tasks.tableOwner"),
      dataIndex: "owner",
      key: "owner",
      width: 100,
      render: (props) => (
        <div className="flex items-center ">
          <ActionsComponent elementValue={props?.owner_id}>
            <AvatarChat
              fontSize={"0.7rem"}
              className="mx-1.5 flex items-center justify-center"
              height={"32px"}
              width={"32px"}
              url={`${
                URL_ENV?.REACT_APP_BASE_URL +
                URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
              }${props?.owner_id?.avatar}`}
              hasImage={EXTENSIONS_ARRAY?.includes(
                props?.owner_id?.avatar?.split(".")?.pop()
              )}
              name={getName(props?.owner_id?.label, "avatar")}
              type="user"
            />
          </ActionsComponent>
          {props?.can_update_task === 1 ? (
            <span ref={Refs_IDs.updateOwners}>
              <PopOverMembersTask
                from="columnTable"
                keyColumn="followers"
                usersList={ownersList}
                searchQuery={followersSearchQuery}
                setSearchQuery={setFollowersSearchQuery}
                lastPage={followersListLastPage}
                currentPage={followersListPage}
                setCurrentPage={setFollowersListPage}
                checkedItems={checkedItems}
                loading={loadOwners}
                setTasksData={setTasksData}
                multipleAddMember={false}
                defaultOwner={props?.owner_id?.id}
                setCheckedItems={setCheckedItems}
                dispatchRemoveMembers={() => {}}
                setSelectedFamilyMembers={setSelectedFamilyMembers}
                setFilterParticipantsValue={setFilterParticipantsValue}
                filterParticipantsValue={filterParticipantsValue}
                totalEntities={totalEntities}
                item={props}
              />
            </span>
          ) : null}
        </div>
      ),
    },
    {
      title: t("tasks.tableGuests"),
      dataIndex: "guests",
      key: "guests",
      width: 150,
      render: (props) => (
        <div className="flex items-center gap-1">
          {props?.guests && props?.guests.length > 0 ? (
            <AvatarGroup
              source="taskTable"
              usersArray={props?.guests}
              uncheckUser={() => {}}
              disableDelete={true}
            />
          ) : null}
          {props?.can_update_task === 1 ? (
            <span ref={Refs_IDs.updateGuests}>
              <PopOverMembersTask
                from="columnTable"
                keyColumn="guests"
                usersList={guestsList}
                searchQuery={guestsSearchQuery}
                setSearchQuery={setGuestsSearchQuery}
                lastPage={guestsListLastPage}
                currentPage={guestsListPage}
                setCurrentPage={setGuestsListPage}
                checkedItems={checkedItems}
                loading={loadGuests}
                setTasksData={setTasksData}
                setCheckedItems={setCheckedItems}
                dispatchRemoveMembers={() => {}}
                setSelectedFamilyMembers={setSelectedFamilyMembers}
                setFilterParticipantsValue={setFilterParticipantsValue}
                filterParticipantsValue={filterParticipantsValue}
                totalEntities={totalEntities}
                item={props}
              />
            </span>
          ) : null}
        </div>
      ),
    },
    {
      title:
        user.tenant === "spheredev2" || user.tenant === "taoufikhospitals"
          ? t("tasks.medicalStaff")
          : t("tasks.orgFamily"),
      dataIndex: "organization",
      key: "organization",
      width: 150,
      render: (props) => {
        const orgs = props?.guests.filter((el) => el.family_id === 1);
        return (
          <div className="flex items-center gap-1">
            {orgs && orgs.length > 0 ? (
              <AvatarGroup
                source="taskTable"
                usersArray={orgs}
                uncheckUser={() => {}}
                disableDelete={true}
              />
            ) : null}{" "}
            {orgs.length === 1 && getName(orgs[0]?.label, "name")}
          </div>
        );
      },
    },
    {
      title:
        user.tenant === "spheredev2" || user.tenant === "taoufikhospitals"
          ? t("tasks.patients")
          : t("tasks.contactFamily"),
      dataIndex: "contact",
      key: "contact",
      width: 150,
      render: (props) => {
        const contacts = props?.guests.filter((el) => el.family_id === 2);
        return (
          <div className="flex items-center gap-1">
            {contacts && contacts.length > 0 ? (
              <AvatarGroup
                source="taskTable"
                usersArray={contacts}
                uncheckUser={() => {}}
                disableDelete={true}
              />
            ) : null}
            {contacts.length === 1 && getName(contacts[0]?.label, "name")}
          </div>
        );
      },
    },
    {
      title: t("tasks.followersListTitle"),
      dataIndex: "followers",
      key: "followers",
      width: 150,
      render: (props) => (
        <div className="flex items-center gap-1">
          {props?.followers && props?.followers.length > 0 ? (
            <AvatarGroup
              source="taskTable"
              usersArray={props?.followers}
              uncheckUser={() => {}}
              disableDelete={true}
            />
          ) : null}
          {props?.can_update_task === 1 ? (
            <span ref={Refs_IDs.updateFollowers}>
              <PopOverMembersTask
                from="columnTable"
                keyColumn="followers"
                usersList={ownersList}
                searchQuery={followersSearchQuery}
                setSearchQuery={setFollowersSearchQuery}
                lastPage={followersListLastPage}
                currentPage={followersListPage}
                setCurrentPage={setFollowersListPage}
                checkedItems={checkedItems}
                loading={loadOwners}
                setTasksData={setTasksData}
                setCheckedItems={setCheckedItems}
                dispatchRemoveMembers={() => {}}
                setSelectedFamilyMembers={setSelectedFamilyMembers}
                setFilterParticipantsValue={setFilterParticipantsValue}
                filterParticipantsValue={filterParticipantsValue}
                totalEntities={totalEntities}
                item={props}
              />
            </span>
          ) : null}
        </div>
      ),
    },
    ...(source !== "viewSphere"
      ? [
          {
            title: t("tasks.assignment"),
            dataIndex: "moduleElement",
            key: "moduleElement",
            width: 130,
            render: (props, record) => {
              return (
                props?.familyLabel &&
                props?.elementLabel && (
                  <Tooltip
                    title={t("tasks.moduleElement", {
                      elementLabel: props?.elementLabel,
                    })}
                  >
                    <Tag
                      color="magenta"
                      className="module-tag-text hover:cursor-pointer"
                      onClick={() => handleClickOnElement(props, record)}
                      style={{
                        height: "25px",
                        display: "flex",
                        alignItems: "center",
                      }}
                      bordered={false}
                      icon={
                        <span className="relative top-[1px] pr-1">
                          {" "}
                          {
                            familyIcons(t).find(
                              (el) => el.key === props?.familyId
                            )?.icon
                          }
                        </span>
                      }
                    >
                      <span className="flex w-[130px] items-center truncate">
                        <span className="truncate">{`${props?.familyLabel}/${props?.elementLabel}`}</span>
                      </span>
                    </Tag>
                  </Tooltip>
                )
              );
            },
          },
        ]
      : []),
    {
      title: "Code",
      dataIndex: "code",
      key: "code",
      width: 150,
      render: (props) => <Typography.Text>{props?.code}</Typography.Text>,
    },
    {
      title: t("tasks.activityId"),
      dataIndex: "activityId",
      key: "activityId",
      width: 150,
      render: (props) => (
        <div
          className="group/item flex flex-row items-center justify-between"
          ref={Refs_IDs.idActivity}
        >
          <Typography.Text ellipsis={{ tooltip: props }}>
            {props}
          </Typography.Text>
          <span
            className="group/icon invisible hover:cursor-pointer group-hover/item:visible"
            onClick={() => copyIdToClipboard(`ActivityID_${props}`)}
          >
            {isCopiedToClipboard ? (
              <CheckOutlined style={{ color: "#73d13d" }} />
            ) : (
              <CopyOutlined style={{ color: "#1890ff" }} />
            )}
          </span>
        </div>
      ),
    },
  ];

  // Row selection keys and change handler.
  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
    getCheckboxProps: (record) => ({
      disabled: record?.label?.can_update_task === 0,
      activityType: record?.tasks_type_id,
    }),
  };

  //Reload table according the mercure event.
  useEffect(() => {
    if (isUserNotified) {
      if (selectedViewInTask === "Table" && taskNotifPayload !== null) {
        return () => {
          updateViewAccordingToNotification(
            taskNotifActionType,
            taskNotifPayload,
            setTasksData,
            tasksData
          );
          dispatch(setNotificationPayload(null));
          if (taskNotifActionType === "create") {
            setTotal(total + 1);
          } else if (taskNotifActionType === "delete_task") {
            setTotal(total - taskNotifPayload && taskNotifPayload?.length);
          }
        };
      }
    }
  }, [
    dispatch,
    isUserNotified,
    selectedViewInTask,
    taskNotifActionType,
    taskNotifPayload,
  ]);

  // Dispatch remove task API. (single or mass deletion)
  const deleteTask = async (ids) => {
    let formData = new FormData();
    ids &&
      ids.forEach((id) => {
        return formData.append("id[]", id);
      });
    try {
      const response = await MainService.deleteSpecificTask(formData);
      if (response?.data?.success) {
        getTasks();
        setSelectedRowKeys([]);
        toastNotification("success", t("toasts.taskDeleted"), "bottomRight");
        setUpdateFilters((prev) => prev + 1);
      }
    } catch (error) {
      console.log(`Error ${error}`);
      toastNotification("error", t("toasts.somethingWrong"));
    }
  };

  //Format pipelines/stages for select.
  const items = useMemo(() => {
    const selectedPipelineObj = pipelines?.find(
      (pipeline) => pipeline?.id === selectedPipeline
    );
    return (
      selectedPipelineObj?.stages.map((el) => ({
        label: el?.label,
        key: el?.id,
        parentId: el?.pipeline_id,
      })) || []
    );
  }, [pipelines, selectedPipeline]);

  // handle update multiple activities stages at once.
  const menuProps = useMemo(
    () => ({
      items,
      onClick: async (stageId) => {
        if (!selectedRowKeys?.length) return;
        try {
          setLoadingUpdateStage(true);
          const formData = new FormData();
          formData.append("new_stage_id", stageId?.key);
          selectedRowKeys.forEach((key) => formData.append("task_id[]", key)); // Keep it as is if this works
          await UpdateTaskStage(formData, "MultipleUpdate");
          message.success(t("toasts.stageChanged"), 3);
          setSelectedRowKeys([]);
        } catch (error) {
          console.error("Error:", error);
          message.error(t("toasts.errorOccurred"));
        } finally {
          setLoadingUpdateStage(false);
        }
      },
    }),
    [selectedRowKeys, t]
  );

  //Show/hide table columns.
  const formatTableData = () => {
    return defaultColumns?.filter((col) => checkedColumns?.includes(col?.key));
  };

  //Handle table's pagination.
  const handleSetPageNumber = (page) => {
    setPageNumber(page);
  };

  //Handle table's limit.
  const handleSetLimit = (current, size) => setLimit(size);

  //Handle table pagination range.
  const handleShowTotalOnPagination = (total, range) =>
    t("tasks.tablePagination", {
      range: `${range[0]}-${range[1]}`,
      totalItems: total,
    });

  return (
    <>
      {/* Show single/mass delete button on top of tasks table */}
      {selectedRowKeys?.length > 0 && (
        <div className="flex flex-row items-center pb-3">
          <Button
            danger
            icon={<DeleteOutlined />}
            type="primary"
            onClick={() =>
              Confirm(
                t("tasks.deleteTaskModal", {
                  plural:
                    selectedRowKeys && selectedRowKeys.length > 1 ? "s" : "",
                }),
                "Confirm",
                <RestOutlined style={{ color: "red" }} />,
                function func() {
                  return deleteTask(selectedRowKeys && selectedRowKeys);
                },
                true
              )
            }
          >
            {t("tasks.deleteBtn", {
              s: selectedRowKeys.length,
              plural: selectedRowKeys.length > 1 ? "s" : "",
            })}
          </Button>
          {selectedPipeline !== 0 && (
            <Dropdown trigger={["click"]} menu={menuProps}>
              <Button
                style={{ marginRight: "30px", marginLeft: "24px" }}
                loading={loadingUpdateStage}
              >
                <Space>
                  {t("tasks.moveToStage")}
                  <DownOutlined />
                </Space>
              </Button>
            </Dropdown>
          )}
        </div>
      )}
      {/* Tasks table */}
      <div className="activities-table px-[12px]">
        {!isGuestConnected() &&
        dataSource?.length === 0 &&
        !loadTasks &&
        activeFilters?.length === 0 ? (
          <EmptyPage
            heroTitle={
              <span>
                {t("tasks.startCreateActivities")}{" "}
                <Typography.Link>{t("tasks.activities")}</Typography.Link>
              </span>
            }
            mainBtnTitle={
              source === "viewSphere" ? "" : t("tasks.addQuickTask")
            }
            handleMainBtnClick={() => dispatch(setOpenTaskDrawer(true))}
          />
        ) : (
          <Table
            style={{
              width:
                source === "viewSphere"
                  ? `calc(${openView360InDrawer ? "1140px" : "100vw"} - ${
                      collapsedInViewSphere ? "243px" : "343px"
                    })`
                  : "100%",
            }}
            rowSelection={source === "viewSphere" ? false : rowSelection}
            columns={
              source === "viewSphere" ? defaultColumns : formatTableData()
            }
            dataSource={dataSource}
            pagination={{
              pageSize: limit,
              total,
              current: pageNumber,
              showTotal: handleShowTotalOnPagination,
              onChange: handleSetPageNumber,
              onShowSizeChange: handleSetLimit,
              pageSizeOptions: [15, 30],
              showSizeChanger: total > 15,
            }}
            rowClassName={() => "task-table-row"}
            loading={loadTasks}
            size="small"
            locale={{
              triggerDesc: t("tasks.descendSort"),
              triggerAsc: t("tasks.ascendSort"),
              cancelSort: t("tasks.cancelSort"),
              emptyText: getTasksError && <ErrorState onRetry={handleReload} />,
            }}
            scroll={{
              y:
                source === "viewSphere"
                  ? `calc(100vh - ${headerHeight + 330}px)`
                  : windowSize?.height -
                    (activeFilters?.length > 0 ? 320 : 300),
              x:
                source === "viewSphere" ? "100%" : checkedColumns?.length * 170,
            }}
            onChange={onChange}
          />
        )}
      </div>
    </>
  );
};

export default TasksTableView;
