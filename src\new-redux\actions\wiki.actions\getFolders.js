import { GET_FOLDER_WIKI_SUCCESS, GET_FOLDER_WIKI_ERROR } from '../../constants'
import MainService from '../../../services/main.service'

export const getFolders = () => async (dispatch) => {
  try {
    const response = await MainService.getFolders()
    dispatch({
      type: GET_FOLDER_WIKI_SUCCESS,
      payload: response?.data?.data,
    })
  } catch (error) {
    dispatch({
      type: GET_FOLDER_WIKI_ERROR,
      payload: error,
    })
  }
}
