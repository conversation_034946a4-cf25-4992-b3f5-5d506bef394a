import React, { forwardRef } from "react";
import { Button } from "antd";

import "./VideoRecordingStyles.css";
import { VideoCameraOutlined } from "@ant-design/icons";

const VideoRecorder = forwardRef(
  (
    {
      permission,
      recordingStatus,
      recordedVideo,
      getCameraPermission,
      startRecording,
      stopRecording,
      liveVideoFeed,
    },
    ref
  ) => {
    // https://github.com/codiini/react-audio-video-recorder/blob/main/src/VideoRecorder.jsx

    return (
      <div>
        <h2>Video Recorder</h2>
        <main>
          <div>
            {/* {!permission ? (
              <Button onClick={getCameraPermission} type="primary" icon={<VideoCameraOutlined />}>
                Get Camera
              </Button>
            ) : null} */}
            {/* {permission && recordingStatus === "inactive" ? (
              <button onClick={startRecording} type="button">
                Start Recording
              </button>
            ) : null} */}
            {/* {recordingStatus === "recording" ? (
              <Button onClick={stopRecording} type="button">
                Stop Recording
              </Button>
            ) : null} */}
          </div>
        </main>
        <div className="video-player">
          {!recordedVideo ? (
            <video
              ref={liveVideoFeed}
              className="live-record"
              controls="seek"
              controlsList="nofullscreen noplay"
              autoPlay
            ></video>
          ) : null}
          {recordedVideo ? (
            <div className="recorded-player">
              <video
                className="recorded"
                src={recordedVideo}
                controls
                controlsList="nofullscreen"
              ></video>
              {/* <a download href={recordedVideo}>
              Download Recording
            </a> */}
            </div>
          ) : null}
        </div>
      </div>
    );
  }
);

export default VideoRecorder;
