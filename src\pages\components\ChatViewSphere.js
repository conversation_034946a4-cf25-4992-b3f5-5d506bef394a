import { useMemo, useState, useEffect, useLayoutEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  Drawer,
  Button,
  Space,
  Skeleton,
  Typography,
  Badge,
  Input,
  Popover,
  List,
  Empty,
} from "antd";
import {
  ArrowLeftOutlined,
  CloseOutlined,
  LoadingOutlined,
  TeamOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { FiBellOff, FiSearch } from "react-icons/fi";
import { useLocation } from "react-router-dom";

import ChatConversations from "../layouts/chat/conversation/body";
import InputChat from "../layouts/chat/conversation/input";
import useInfoRoomInModules from "pages/layouts/chat/hooks/useInfoRoomInModules";
import { closeDrawerChat } from "new-redux/actions/voip.actions/handleDrawerChat";
import {
  resetStateOtherUser,
  setChatSelectedConversation,
  setChatSelectedParticipants,
  setExternalItem,
} from "new-redux/actions/chat.actions";
import { filterReadMessage } from "new-redux/actions/tasks.actions/handleTaskRoomDrawer";
import { AvatarChat } from "components/Chat";
import { URL_ENV } from "index";
import { ConnectedUsersListItem } from "components/Chat/ConnectedUsersPopover/listItem";

import { getName } from "pages/layouts/chat/utils/ConversationUtils";
import { setActiveTab360 } from "new-redux/actions/vue360.actions/vue360";
import { BiMessageRoundedError } from "react-icons/bi";
import { isGuestConnected } from "utils/role";

const ChatViewSphere = ({
  elementId,
  canCreateRoom = 1,
  setElementDetailToUpdate = () => {},
  headerHeight = 0,
  from = "",
  mountChat = false,
  setMountchat = () => {},
  setSelectedConvViewSphere,
  selectedConvViewSphere,
}) => {
  const selectedParticipants = useSelector(
    (state) => state.chat.selectedParticipants
  );

  const { t } = useTranslation("common");
  const dispatch = useDispatch();
  const location = useLocation();
  const [searchParticipant, setSearchParticipant] = useState("");
  const [searchOnlineParticipant, setSearchOnlineParticipant] = useState("");
  const [previousRoom, setPreviousRoom] = useState(null);
  const [previousRoomParticipants, setPreviousRoomParticipants] = useState([]);
  const { status, data, isFetched, fetchStatus } = useInfoRoomInModules(
    location?.state?.roomId ?? elementId,
    canCreateRoom,
    "",
    "family"
  );
  const { openView360InDrawer, activeTab360, openChatInViewSphere } =
    useSelector((state) => state?.vue360);
  const { openDrawerChat } = useSelector((state) => state.voip);
  const chatSelectedConv = useSelector(
    (state) => state?.ChatRealTime?.selectedConversation
  );

  const onlineUsers = useSelector((state) => state?.ChatRealTime?.onlineUser);
  const activitiesMessages = useSelector(
    (state) => state?.TasksRealTime?.activitiesMessages
  );

  const skl = () =>
    Array.from(
      { length: Math.floor(document?.body?.clientHeight / 3) },
      (_, i) => i + 1
    ).map((item) => (
      <div className="flex  items-center px-1" key={`sklt_${item}`}>
        <Skeleton
          avatar
          paragraph={{
            rows: Math.floor(
              (Math.random() + 1) *
                Array.from({ length: 3 }, (i) => i + 1)?.length
            ),
          }}
          active
        />
      </div>
    ));

  const connectedUser = useMemo(() => {
    let users = 0;
    if (status === "success")
      selectedParticipants?.forEach((element) => {
        if (onlineUsers[element?.uuid] === "online") users++;
      });

    return users;
  }, [selectedParticipants, onlineUsers, status]);
  // useEffect(() => {
  //   dispatch(setOpenChatInViewSPhere(true));
  //   return () => {
  //     dispatch(setOpenChatInViewSPhere(false));
  //   };
  // }, [dispatch, activeTab360]);

  useEffect(() => {
    if (status === "success" && isFetched && canCreateRoom == 1 && !mountChat) {
      dispatch(closeDrawerChat());
      dispatch(
        resetStateOtherUser({
          forced: true,
          keepDrawerOpened: false,
          item: null,
        })
      );
      dispatch(
        setChatSelectedParticipants({
          selectedParticipants: data?.data?.room?.participants ?? [],
        })
      );
      dispatch(
        setChatSelectedConversation({
          selectedConversation: {
            name: data?.data?.room?.name,
            description: data?.data?.room?.description,
            image: data?.data?.room?.image,
            admin_id: data?.data?.room?.admin_id,
            bot: null,
            id: data?.data?.room?._id,
            type: "room",
            source: "module",
            muted_status: false,
            conversationId: data?.data?.room?._id,
            external: false,
          },
        })
      );
      setSelectedConvViewSphere({
        name: data?.data?.room?.name,
        description: data?.data?.room?.description,
        image: data?.data?.room?.image,
        admin_id: data?.data?.room?.admin_id,
        bot: null,
        id: data?.data?.room?._id,
        type: "room",
        source: "module",
        muted_status: false,
        conversationId: data?.data?.room?._id,
        external: false,
      });
      setPreviousRoom({
        name: data?.data?.room?.name,
        description: data?.data?.room?.description,
        image: data?.data?.room?.image,
        admin_id: data?.data?.room?.admin_id,
        bot: null,
        id: data?.data?.room?._id,
        type: "room",
        source: "module",
        mode: "members",
        muted_status: false,
        conversationId: data?.data?.room?._id,
        external: false,
      });
      // setMountchat(true);
      // setPreviousRoomParticipants(data?.data?.room?.participants ?? []);
      let activatedRoom =
        activitiesMessages &&
        activitiesMessages?.unread_msg_room?.find(
          (room) => room?.room_id === data?.data?.room?._id
        );
      if (activatedRoom) {
        dispatch(
          setExternalItem({
            _id: data?.data?.room?._id,
            total_unread: activatedRoom?.messages_count,
            last_message: { unread: 1 },
            tag_status: false,
          })
        );
        dispatch(filterReadMessage(data?.data?.room?._id));
      }
    }
  }, [status, isFetched, canCreateRoom, mountChat, openView360InDrawer]);
  useEffect(() => {
    return () => {
      dispatch(setActiveTab360(""));
    };
  }, [dispatch]);
  useEffect(() => {
    let timeoutId;
    if (!openChatInViewSphere) {
      timeoutId = setTimeout(() => {
        dispatch(
          setChatSelectedConversation({
            selectedConversation: null,
          })
        );
      }, 200);
    }

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [openChatInViewSphere]);

  // useEffect(() => {
  //   if (chatSelectedConv?.type === "room") {
  //     console.log(chatSelectedConv, selectedParticipants);
  //     setPreviousRoomParticipants(selectedParticipants);
  //   }
  // }, [selectedParticipants, chatSelectedConv]);

  const returnToPreviousChatRoom = () => {
    dispatch(
      setChatSelectedConversation({
        selectedConversation: {
          ...previousRoom,
        },
      })
    );
    dispatch(
      setChatSelectedParticipants({
        selectedParticipants: previousRoomParticipants,
      })
    );
  };
  const textareaRef = useRef(null);

  const handleKeyDown = (event) => {
    // Vous pouvez gérer certaines touches ici si nécessaire
    if (activeTab360 != 9 && !isGuestConnected() && event.key !== "Enter")
      event.stopPropagation();
  };

  return (
    <div
      className={`chatviewsphere overflow-x-hidden ${
        chatSelectedConv?.type === "user" && activeTab360 == 9 && "fromchat"
      } `}
      ref={activeTab360 == 9 ? null : textareaRef}
      onKeyDown={
        activeTab360 != 9 && !isGuestConnected() ? handleKeyDown : () => {}
      }
    >
      {!openDrawerChat ? (
        <>
          {canCreateRoom == 1 ? (
            status === "loading" ? (
              <>{skl()}</>
            ) : status === "error" ? (
              <div className="relative top-1/3 flex h-auto w-full justify-center">
                <div className="flex flex-col items-center">
                  <p className="text-red-500">{t("tasks.loadRoomError")}</p>
                </div>
              </div>
            ) : (
              <>
                <Space>
                  {chatSelectedConv?.type === "user" ? (
                    <Button
                      icon={<ArrowLeftOutlined />}
                      type="text"
                      shape="circle"
                      onClick={returnToPreviousChatRoom}
                    />
                  ) : null}
                  <Badge
                    offset={[-4, 40]}
                    style={{ width: "8px", height: "8px" }}
                  >
                    <AvatarChat
                      type={chatSelectedConv?.type}
                      fontBold="font-semibold"
                      hasImage={chatSelectedConv?.image}
                      height={12}
                      width={12}
                      size={52}
                      url={
                        chatSelectedConv?.type === "room"
                          ? URL_ENV?.REACT_APP_OAUTH_CHAT_API +
                            process.env.REACT_APP_SUFFIX_CHAT_GROUP_STORAGE +
                            chatSelectedConv?.image
                          : URL_ENV?.REACT_APP_BASE_URL +
                            URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                            chatSelectedConv?.image
                      }
                      name={getName(chatSelectedConv?.name, "avatar")}
                    />
                  </Badge>
                  <div className="flex flex-col">
                    <Typography.Text className="text-lg font-medium text-slate-800 first-letter:capitalize">
                      {getName(chatSelectedConv?.name, "name")}{" "}
                      {chatSelectedConv?.muted_status && (
                        <FiBellOff
                          className="time"
                          style={{ fontSize: "13px" }}
                        />
                      )}{" "}
                    </Typography.Text>
                    {chatSelectedConv?.id &&
                      chatSelectedConv?.type === "room" && (
                        <Space size={12}>
                          {chatSelectedConv?.id && (
                            <Popover
                              content={
                                <div className="flex w-80 flex-col space-y-2">
                                  <Input
                                    size="middle"
                                    placeholder={t(
                                      "chat.searchSide.searchMembers"
                                    )}
                                    prefix={
                                      <FiSearch className="text-slate-500" />
                                    }
                                    value={searchParticipant}
                                    onChange={(e) =>
                                      setSearchParticipant(
                                        e.target.value
                                          .trimStart()
                                          .replace(/\s{1,} /g, " ")
                                      )
                                    }
                                    className="w-full flex-1"
                                    allowClear
                                    autoFocus={true}
                                  />
                                  <List
                                    className="membersList max-h-80 overflow-auto"
                                    dataSource={
                                      selectedParticipants &&
                                      selectedParticipants?.filter((el) =>
                                        getName(el.name, "name")
                                          ?.toLowerCase()
                                          ?.includes(
                                            searchParticipant.toLowerCase()
                                          )
                                      )
                                    }
                                    renderItem={(item) => (
                                      <ConnectedUsersListItem
                                        setPreviousRoomParticipants={
                                          setPreviousRoomParticipants
                                        }
                                        item={item}
                                        source={"chat"}
                                      />
                                    )}
                                  />
                                </div>
                              }
                              trigger="click"
                            >
                              <Button
                                type="text"
                                className="space-x-1 transition duration-300"
                              >
                                <TeamOutlined className="text-gray-400" />
                                <span className="text-gray-400">
                                  {selectedParticipants?.length}
                                </span>
                                <span className="text-gray-400">
                                  {t("chat.header.members")}
                                </span>
                              </Button>
                            </Popover>
                          )}
                          {chatSelectedConv?.id && (
                            <Popover
                              content={
                                <div className="flex w-80 flex-col space-y-2">
                                  <Input
                                    size="middle"
                                    placeholder={t(
                                      "chat.searchSide.searchMembers"
                                    )}
                                    prefix={
                                      <FiSearch className="text-slate-500" />
                                    }
                                    value={searchOnlineParticipant}
                                    onChange={(e) =>
                                      setSearchOnlineParticipant(
                                        e.target.value
                                          .trimStart()
                                          .replace(/\s{1,} /g, " ")
                                      )
                                    }
                                    className="w-full flex-1"
                                    allowClear
                                    autoFocus={true}
                                  />
                                  <List
                                    className="membersList max-h-80 overflow-auto"
                                    dataSource={
                                      selectedParticipants &&
                                      selectedParticipants.filter(
                                        (user) =>
                                          onlineUsers[user?.uuid] === "online"
                                      ) &&
                                      selectedParticipants
                                        ?.filter(
                                          (user) =>
                                            onlineUsers[user?.uuid] === "online"
                                        )
                                        ?.filter((el) =>
                                          getName(el.name, "name")
                                            ?.toLowerCase()
                                            ?.includes(
                                              searchOnlineParticipant.toLowerCase()
                                            )
                                        )
                                    }
                                    renderItem={(item) => (
                                      <ConnectedUsersListItem
                                        setPreviousRoomParticipants={
                                          setPreviousRoomParticipants
                                        }
                                        item={item}
                                        source="chat"
                                      />
                                    )}
                                  />
                                </div>
                              }
                              trigger="click"
                            >
                              <Button
                                className="space-x-1 transition duration-300 hover:cursor-pointer hover:underline"
                                type="text"
                              >
                                <Badge status="success" />
                                <span className="text-gray-400">
                                  {" " + connectedUser}
                                </span>
                                <span className="text-gray-400">
                                  {t("chat.connected")}
                                </span>
                              </Button>
                            </Popover>
                          )}
                        </Space>
                      )}
                  </div>
                </Space>
                <div
                  className="flex flex-1 flex-col justify-between gap-y-1  overflow-y-auto"
                  style={{
                    height: `calc(100vh - ${
                      from === "" ? headerHeight + 210 : headerHeight + 40
                    }px)`,
                  }}
                >
                  {/* <div className=" flex-1 overflow-hidden pl-4"> */}
                  <div
                    id="containerChat"
                    className=" flex-1 overflow-hidden pl-4"
                  >
                    <ChatConversations
                      source={
                        chatSelectedConv?.type === "user" ? "chat" : "no_chat"
                      }
                    />
                  </div>
                  {/* </div> */}
                  <div
                    className={` ${
                      chatSelectedConv?.type === "user" ? "-ml-[22px] " : ""
                    } py-4`}
                  >
                    <InputChat
                      source={
                        chatSelectedConv?.type === "user" ? "chat" : "no_chat"
                      }
                      key={`${chatSelectedConv?.id}_${chatSelectedConv?.type}`}
                    />
                  </div>
                </div>
              </>
            )
          ) : (
            <div className="mt-5 flex justify-center">
              <div className="flex flex-col items-center justify-center">
                <BiMessageRoundedError className="text-[50px]" />
                <p>{t("tasks.noChat")}</p>
              </div>
            </div>
            // <div className="flex items-center justify-center pt-4 text-lg font-semibold">
            //   {t("vue360.cannotAccessChat")}
            // </div>
          )}
        </>
      ) : (
        <div></div>
      )}
    </div>
  );
};

export default ChatViewSphere;
