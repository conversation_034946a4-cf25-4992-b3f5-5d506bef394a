import {
  lazy,
  Suspense,
  useEffect,
  useMemo,
  useCallback,
  useState,
} from "react";
import { Routes, Route, useLocation, Navigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { message, notification, Modal, ConfigProvider } from "antd";
import enUS from "antd/lib/locale/en_US";
import frFR from "antd/lib/locale/fr_FR";

import dayjs from "dayjs";
import TZ from "dayjs/plugin/timezone.js";
import UTC from "dayjs/plugin/utc.js";
import isToday from "dayjs/plugin/isToday";

import customParseFormat from "dayjs/plugin/customParseFormat";
import "dayjs/locale/fr";
import "dayjs/locale/en";

import moment from "moment-timezone";
import "moment/locale/fr";
import "moment/locale/en-gb";

import LayoutFull from "./pages/layouts/layout";
import PrivateRoute from "./utils/PrivateRoute";
import { LoadingAnimation } from "./pages/components/loader";
import ErrorBoundary from "./error-boundary/ErrorBoundary.js";

import { lazyRetry } from "./utils/lazyRetry";
import useAuth from "./custom-hooks/useAuth.js";
import { routes } from "./routes/routes.js";

import {
  CONFIRM_ACCESS_URL_REGEX,
  INVITATION_URL_REGEX,
  VISIO_URL_REDIRECT_REGEX,
  VISIO_URL_REGEX,
} from "./utils/regex.js";

import "./App.css";
import { Loader } from "./components/Chat/index.js";
import Logout from "./pages/logout/index.jsx";
import useNetworkStatus from "custom-hooks/useNetworkStatus";
import {
  setMsgTask,
  setOpenTaskDrawer,
} from "new-redux/actions/tasks.actions/handleTaskDrawer";
import { useDispatch } from "react-redux";
import ModalTicketGLPI from "pages/layouts/components/ModalTicketGLPI";
import ChatWithComunik from "components/chatWithComunik";
import TourWrapper from "components/tour/TourWrapper";
import { NotificationProvider } from "components/NotificationProvider";
import { isGuestConnected } from "utils/role";

const VisioContainer = lazy(() =>
  lazyRetry(
    () => import("./pages/layouts/visio/components/visio-container"),
    "VisioContainer"
  )
);
const ChangeLangModal = lazy(() =>
  lazyRetry(() => import("components/Chat/Modal/ChangeLang"), "ChangeLang")
);

const VisioGuest = lazy(() =>
  lazyRetry(() => import("./pages/layouts/visio/visio-guest"), "VisioGuest")
);
const InvitationUsers = lazy(() =>
  lazyRetry(() => import("./pages/login/invitation-users"), "InvitationUsers")
);
const WebphoneContainer = lazy(() =>
  lazyRetry(() => import("./components/webphone"), "WebphoneContainer")
);
const NotFoundPage = lazy(() =>
  lazyRetry(() => import("./pages/404"), "NotFoundPage")
);
const Unauthorized = lazy(() =>
  lazyRetry(() => import("./pages/403/Unauthrorized"), "NotFoundPage")
);
const EventConnexion = lazy(() =>
  lazyRetry(
    () => import("./utils/real-time-function/chat/EventConnexion"),
    "EventConnexion"
  )
);
const CreateTask = lazy(() =>
  lazyRetry(() => import("pages/voip/components/CreateTask"), "CreateTask")
);
const NewMessageEmail = lazy(
  () =>
    import(
      "./pages/rmc/mailing/main-components/email-composer-modal/NewMessageEmail"
    ),
  "NewMessageEmail"
);
dayjs.extend(UTC);
dayjs.extend(TZ);
dayjs.extend(customParseFormat);
dayjs.extend(isToday);

export let moment_timezone = null;
export let dayjs_timezone = null;
const App = () => {
  const { i18n } = useTranslation("common");
  const [openModalChangeLang, setOpenModalChangeLang] = useState(false);
  const [notificationApi, contextHolderNotification] =
    notification.useNotification();
  const [messageApi, contextHolder] = message.useMessage();
  const { msgTask, openTaskDrawer } = useSelector(
    (state) => state?.TasksRealTime
  );
  const dataAccounts = useSelector((state) => state.mailReducer?.dataAccounts);
  const { openModalTicketGlpi } = useSelector((state) => state?.form);
  const { user } = useSelector((state) => state?.user);
  const dispatch = useDispatch();
  const location = useLocation();
  //general config for moment
  moment.updateLocale(i18n.language === "en" ? "en" : "fr", {
    longDateFormat: {
      LT: user?.location?.time_format === "h:mm a" ? "h:mm A" : "HH:mm",
    },
  });

  moment?.tz?.setDefault(
    user?.location?.default_timezone?.split(" ")[0] ?? moment?.tz?.guess()
  );

  //general config for dayjs
  dayjs.locale(i18n.language === "en" ? "en" : "fr");
  dayjs?.tz?.setDefault(
    user?.location?.default_timezone?.split(" ")[0] ?? dayjs?.tz?.guess()
  );

  moment_timezone = moment;
  dayjs_timezone = dayjs;

  const { isValidToken, isLoading } = useAuth({
    setOpen: setOpenModalChangeLang,
  });

  const { isServerReachable } = useNetworkStatus();

  useEffect(() => {
    return () => {
      messageApi.destroy();
      notificationApi.destroy();
      Modal.destroyAll();
    };
  }, [location.pathname]);

  const renderRoutes = useCallback((array) => {
    return array.map((route) => {
      if (route.children && route.children.length > 0) {
        return (
          <Route key={route.name} path={route.path}>
            {renderRoutes(route.children)};
          </Route>
        );
      } else if (Object.keys(route).includes("onlyChild")) {
        return (
          <Route
            key={route.name}
            element={<PrivateRoute accessTo={route.accessTo} />}
          >
            <Route
              errorElement={<ErrorBoundary />}
              key={route.name}
              path={route.path}
              element={
                <>
                  {route.element2 && <route.element2 />}
                  <route.element />
                </>
              }
            ></Route>
          </Route>
        );
      } else {
        return (
          <Route
            key={route.name}
            element={<PrivateRoute accessTo={route.accessTo} />}
          >
            <Route
              errorElement={<ErrorBoundary />}
              key={route.name}
              path={route.path}
              element={
                <>
                  {route.element2 && <route.element2 />}
                  <route.element />
                </>
              }
            ></Route>
          </Route>
        );
      }
    });
  }, []);
  const LayoutMemoized = useMemo(
    () => (
      <>
        <LayoutFull>
          <Suspense fallback={<LoadingAnimation />}>
            <Routes>
              <Route path="/unauthorized" element={<Unauthorized />} />
              <Route path="/visio-home/:name" element={<VisioGuest />} />
              <Route path="/logout" element={<Logout />} />

              <Route
                path="/invitation-user/:token"
                element={<InvitationUsers />}
              />

              {renderRoutes(routes)}
              <Route path="*" element={<NotFoundPage />} />
            </Routes>
          </Suspense>
        </LayoutFull>
      </>
    ),
    [user?.role]
  );

  const MediaComponenet = useMemo(
    () => (
      <>
        {window.location.pathname !== "/editor" ? (
          <Suspense
            fallback={
              <div className=" absolute bottom-10 left-20">
                <Loader size={24} />
              </div>
            }
          >
            <WebphoneContainer />
          </Suspense>
        ) : null}
        <Suspense fallback={<LoadingAnimation />}>
          <VisioContainer />{" "}
        </Suspense>
      </>
    ),
    []
  );
  const ChatWithComunikComponent = useMemo(() => {
    if (
      // isGuestConnected() &&
      user?.tenant &&
      !user?.tenant?.toLowerCase().includes("comunik")
    ) {
      return (
        <Suspense fallback={<LoadingAnimation />}>
          <ChatWithComunik />
        </Suspense>
      );
    }
  }, [user?.tenant]);

  const TaskComponent = useMemo(
    () =>
      openTaskDrawer &&
      location.pathname === "/chat" && (
        <Suspense
          fallback={
            <div className="fixed inset-0 z-[9999] flex h-full w-full   items-center justify-center bg-black/5 ">
              <Loader size="2rem" />
            </div>
          }
        >
          <CreateTask
            titleLabel={
              msgTask
                ? ""
                : "Visio Conf " + moment_timezone().format("DDMMYYYY HH:mm:ss")
            }
            open={openTaskDrawer}
            source={msgTask ? "" : "visio"}
            setOpen={() => {
              dispatch(setMsgTask(""));
              dispatch(setOpenTaskDrawer(false));
            }}
            mask={false}
            message={msgTask}
          />
        </Suspense>
      ),
    [openTaskDrawer, msgTask, dispatch]
  );
  const TicketGlpiComponent = useMemo(
    () =>
      openModalTicketGlpi && (
        <Suspense
          fallback={
            <div className="fixed inset-0 z-[9999] flex h-full w-full   items-center justify-center bg-black/5 ">
              <Loader size="2rem" />
            </div>
          }
        >
          <ModalTicketGLPI msgTask={msgTask} />
        </Suspense>
      ),
    [openModalTicketGlpi, msgTask, dispatch]
  );
  const urlLast = new URL(window.location.href);

  if (urlLast.toString().match(VISIO_URL_REDIRECT_REGEX)) {
    const match = urlLast.toString().match(VISIO_URL_REGEX);
    if (match) {
      const roomName = urlLast.searchParams.get("room_visio_name");

      return <Navigate to={"/visio-home/" + roomName} />;
    }
  } else if (urlLast.toString().match(INVITATION_URL_REGEX)) {
    const match = urlLast.toString().match(CONFIRM_ACCESS_URL_REGEX);
    if (match) {
      const token = urlLast.searchParams.get("token");

      return <Navigate to={"/invitation-user/" + token} />;
    }
  } else if (isLoading) {
    return <LoadingAnimation />;
  } else if (
    isValidToken &&
    !isLoading &&
    urlLast.toString().match(CONFIRM_ACCESS_URL_REGEX)
  ) {
    return <Navigate to="/dashboard" />;
  }

  return (
    <ConfigProvider locale={i18n.language === "fr" ? frFR : enUS}>
      <NotificationProvider>
        {isValidToken && !isLoading && (
          <Suspense fallback={<></>}>
            <EventConnexion
              isServerReachable={isServerReachable}
              notificationApi={notificationApi}
              messageApi={messageApi}
            />
          </Suspense>
        )}
        {openModalChangeLang && (
          <Suspense
            fallback={
              <div className="flex h-full w-full items-center justify-center">
                <Loader size={24} />
              </div>
            }
          >
            <ChangeLangModal
              open={openModalChangeLang}
              setOpen={setOpenModalChangeLang}
            />
          </Suspense>
        )}
        {dataAccounts.length > 0 && !isGuestConnected() ? (
          <Suspense fallback={<></>}>
            <NewMessageEmail dataAccounts={dataAccounts} />
          </Suspense>
        ) : null}
        {contextHolder}
        {contextHolderNotification}
        {LayoutMemoized}
        {MediaComponenet}
        {ChatWithComunikComponent}
        {TaskComponent}
        {TicketGlpiComponent}
        {/*process.env.REACT_APP_BRANCH !== "prod" &&*/ <TourWrapper />}
      </NotificationProvider>
    </ConfigProvider>
  );
};

export default App;
