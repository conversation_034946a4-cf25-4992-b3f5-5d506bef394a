import React from "react";
import { RadioGroup } from "@headlessui/react";
import { ArrowPathIcon } from "@heroicons/react/24/outline";
import { useSelector } from "react-redux";
import { Radio } from "antd";

import { displayRightIcon } from "../utils/displayIcon";

function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}

const CheckboxGroup = ({ loadTypes, setFieldType, setMem, defaultValue }) => {
  const { types, isSuccess, isLoading } = useSelector((state) => state.types);

  return (
    <div>
      {isLoading ? (
        <ArrowPathIcon className="flex justify-center items-center h-6 w-6 animate-spin text-indigo-500" />
      ) : (
        <Radio.Group
          options={
            types?.data &&
            types?.data.map((element) => ({
              label: (
                <p className="flex justify-between items-center">
                  {element?.fieldType}
                  {displayRightIcon(element?.fieldType, 5, 5)}
                </p>
              ),
              value: element?.id,
            }))
          }
          optionType="button"
          buttonStyle="solid"
          onChange={(e) => {
            setMem(e.target.value);
            setFieldType(e.target.value);
          }}
        />
        // <RadioGroup
        //   onChange={(key) => {
        //     setFieldType(key);
        //     setMem(key);
        //   }}
        //   className="mt-2"
        // >
        //   <RadioGroup.Label className="sr-only"> Choose a memory option </RadioGroup.Label>
        //   <div className="grid grid-cols-3 gap-3 sm:grid-cols-6">
        //     {isSuccess && defaultValue === undefined
        //       ? types?.data.map((option) => (
        //           <RadioGroup.Option
        //             key={option.id}
        //             value={
        //               defaultValue
        //                 ? types?.data &&
        //                   types?.data.find((element) => element?.id == defaultValue?.id)
        //                 : option?.id
        //             }
        //             className={({ active, checked }) =>
        //               classNames(
        //                 active ? "ring-2 ring-offset-2 ring-indigo-500" : "",
        //                 checked
        //                   ? "bg-indigo-600 border-transparent text-white hover:bg-indigo-700"
        //                   : "bg-white border-gray-200 text-gray-900 hover:bg-gray-50",
        //                 "border rounded-md px-2 py-2 flex items-center cursor-pointer justify-center text-xs font-medium uppercase sm:flex-1"
        //               )
        //             }
        //           >
        //             <RadioGroup.Label as="span">
        //               <span className="flex flex-col justify-around items-center">
        //                 {displayRightIcon(option?.fieldType, 5, 5)}
        //                 {option?.fieldType === "radio"
        //                   ? "Unique"
        //                   : option?.fieldType === "checkbox"
        //                   ? "multiple"
        //                   : option?.fieldType}
        //               </span>
        //             </RadioGroup.Label>
        //           </RadioGroup.Option>
        //         ))
        //       : types?.data && defaultValue !== undefined
        //       ? types?.data
        //           .filter((el) => el?.id == defaultValue)
        //           .map((option) => (
        //             <RadioGroup.Option
        //               key={option.id}
        //               value={
        //                 defaultValue
        //                   ? types?.data &&
        //                     types?.data.find((element) => element?.id == defaultValue?.id)
        //                   : option?.id
        //               }
        //               className={({ active, checked }) =>
        //                 classNames(
        //                   active ? "ring-2 ring-offset-2 ring-indigo-500" : "",
        //                   checked
        //                     ? "bg-indigo-600 border-transparent text-white hover:bg-indigo-700"
        //                     : "bg-white border-gray-200 text-gray-900 hover:bg-gray-50",
        //                   "border rounded-md px-2 py-2 flex items-center cursor-pointer justify-center text-xs font-medium uppercase sm:flex-1"
        //                 )
        //               }
        //               disabled={true}
        //             >
        //               <RadioGroup.Label as="span">
        //                 <span className="flex flex-col justify-around items-center">
        //                   {displayRightIcon(option?.fieldType, 5, 5)}
        //                   {option?.fieldType}
        //                 </span>
        //               </RadioGroup.Label>
        //             </RadioGroup.Option>
        //           ))
        //       : null}
        //   </div>
        // </RadioGroup>
      )}
    </div>
  );
};

export default CheckboxGroup;
