import {
  SET_MENU_TITLE,
  SET_OPEN_TAG,
  SET_OPEN_ACTIVITY,
  SET_OPEN_CONFIGMAIL,
  SET_SEARCH,
  SET_INVALID_CONFIGMAIL,
  SET_FAMILYID,
  SET_END_BUILD,
  SET_COLLAPSE_INNER_MENU,
  SET_OPEN_TOUR,
} from "../../constants";

export const setMenu = (payload) => (dispatch) => {
  dispatch({ type: SET_MENU_TITLE, payload: payload });
};
export const setOpenTag = (payload) => ({
  type: SET_OPEN_TAG,
  payload: payload,
});
export const setOpenActivity = (payload) => ({
  type: SET_OPEN_ACTIVITY,
  payload: payload,
});

export const setOpenConfigMail = (payload) => ({
  type: SET_OPEN_CONFIGMAIL,
  payload: payload,
});
export const setEndBuild = (payload) => ({
  type: SET_END_BUILD,
  payload: payload,
});
export const setFamilyId = (payload) => ({
  type: SET_FAMILYID,
  payload: payload,
});
export const setInavlidConfigMail = (payload) => ({
  type: SET_INVALID_CONFIGMAIL,
  payload,
});
export const setOpenTourInProfile = (payload) => ({
  type: SET_OPEN_TOUR,
  payload,
});

export const setSearch = (payload) => ({ type: SET_SEARCH, payload: payload });
export const setCollpaseMenu = (payload) => ({
  type: SET_COLLAPSE_INNER_MENU,
  payload: payload,
});
