import { GET_GROUP_WIKI_SUCCESS, GET_GROUP_WIKI_ERROR } from '../../constants'
import MainService from '../../../services/main.service'

export const getGroups = () => async (dispatch) => {
  try {
    const response = await MainService.getGroups()
    dispatch({
      type: GET_GROUP_WIKI_SUCCESS,
      payload: response?.data?.data,
    })
  } catch (error) {
    dispatch({
      type: GET_GROUP_WIKI_ERROR,
      payload: error,
    })
  }
}
