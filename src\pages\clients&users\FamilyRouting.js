import React, { useEffect, useState } from "react";
import { Navigate, useLocation } from "react-router-dom";
import TableInterface from "./TableInterface";
import Users from "../settings/Users";
import Products from "pages/settings/Products";
import { roles } from "utils/role";
import { useSelector } from "react-redux";

const FamilyRouting = () => {
  const location = useLocation();
  const pathname = location.pathname;

  const { user } = useSelector((state) => state.user);

  const [familyId, setFamilyId] = useState(null);
  //   console.log({familyId})

  useEffect(() => {
    let isMounted = true;
    if (isMounted)
      switch (pathname) {
        case "/companies":
          setFamilyId(1);
          break;
        case "/contacts":
          setFamilyId(2);
          break;
        case "/deals":
          setFamilyId(3);
          break;
        case "/settings/users":
        case "/settings/guests":
          setFamilyId(4);
          break;
        case "/settings/products":
          setFamilyId(5);
          break;
        case "/tickets":
          setFamilyId(6);
          break;
        case "/projects":
          setFamilyId(7);
          break;
        case "/booking":
          setFamilyId(8);
          break;
        case "/leads":
          setFamilyId(9);
          break;
        case "/invoices":
          setFamilyId(11);
          break;
        case "/transactions":
          setFamilyId(12);
          break;
        default:
          break;
      }
    return () => (isMounted = false);
  }, [pathname, familyId]);

  if (pathname === "/settings/users" && !roles.includes(user.role))
    return <Navigate to="/unauthorized" state={{ from: pathname }} replace />;

  return (
    <>
      {familyId === 4 ? <Users /> : familyId === 5 ? <Products /> : null}
      {familyId && <TableInterface familyId={familyId} />}
    </>
  );
};

export default FamilyRouting;

export const getFamilyNameById = (t, id, state) => {
  switch (Number(id)) {
    case 1:
      return state === "plural"
        ? t("contacts.companies")
        : t("contacts.company");
    case 2:
      return state === "plural"
        ? t("contacts.contacts")
        : t("contacts.contact");
    case 3:
      return state === "plural" ? "Deals" : "Deal";
    case 4:
      return state === "plural" ? t("contacts.users") : t("contacts.user");
    case 5:
      return state === "plural"
        ? t("contacts.products")
        : t("contacts.product");
    case 6:
      return state === "plural" ? t("contacts.tickets") : t("contacts.ticket");
    case 7:
      return state === "plural"
        ? t("contacts.projects")
        : t("contacts.project");
    case 8:
      return state === "plural"
        ? t("contacts.bookings")
        : t("contacts.booking");
    case 9:
      return state === "plural" ? t("contacts.leads") : t("contacts.leads");
    case 11:
      return state === "plural"
        ? t("contacts.invoices")
        : t("contacts.invoice");
    case 12:
      return state === "plural" ? "Transactions" : "Transaction";
    default:
      break;
  }
};

export const familyWithAvatar = {
  1: "company",
  2: "contact",
  4: "user",
  9: "leads",
};

export const getSettingPath = (id) => {
  switch (id) {
    case 1:
      return "/settings/fields/Organisation";
    case 2:
      return "/settings/fields/Contact";
    case 3:
      return "/settings/fields/Deal";
    case 4:
      return "/settings/fields/User";
    case 5:
      return "/settings/fields/Product";
    case 6:
      return "/settings/fields/Ticket";
    case 7:
      return "/settings/fields/Project";
    case 8:
      return "/settings/fields/Booking";
    case 9:
      return "/settings/fields/Leads";
    case 11:
      return "settings/fields/Invoices";
    case 12:
      return "/settings/fields/Transaction";
    default:
      break;
  }
};

export const getFamilyIdByName = {
  companies: 1,
  contacts: 2,
  deals: 3,
  users: 4,
  guests: 4,
  products: 5,
  tickets: 6,
  projects: 7,
  booking: 8,
  leads: 9,
  invoices: 11,
  transactions: 12,
};

export const convertPathToFamilyId = (path) => {
  switch (path) {
    case "/companies":
      return 1;

    case "/contacts":
      return 2;

    case "/deals":
      return 3;

    case "/settings/users":
      return 4;

    case "/settings/products":
      return 5;

    case "/tickets":
      return 6;

    case "/projects":
      return 7;

    case "/booking":
      return 8;

    case "/leads":
      return 9;

    case "/invoices":
      return 11;

    case "/transactions":
      return 12;

    default:
      break;
  }
};

// export const matchFamiliesWithPipelineFieldId = {
//   1: 8,
//   2: 18,
//   3: 22,
//   4: 162,
//   5: 160,
//   6: 53,
//   7: 161,
//   8: 87,
//   9: 96,
//   11: 168,
//   // 12:
// };

export const channelSourceId = {
  1: 2,
  2: 13,
  3: 25,
  6: 55,
  8: 88,
  9: 90,
};

export const sortableTypes = {
  text: true,
  email: true,
  rate: true,
  textarea: true,
  date_time: true,
  number: true,
  time: true,
  date: true,
  range: true,
  monetary: true,
  extension: true,
  created_at: true,
  updated_at: true,
};
