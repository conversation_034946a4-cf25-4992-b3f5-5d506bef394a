/**
 * @name CreateTaskForm
 *
 * @description A Form component to create activity
 *              It's used across multiple modules in sphere (activity, visio, journal, chat, 360).
 *
 * @param {number} form The ant design form instance.
 * @param {function} onFinish Triggered when a sub form submits	.
 * @param {boolean} loadSpecificTask Show loader on load task by id (get task by id).
 * @param {function} prefillTaskLabel Fill task label input on select task type.
 * @param {array} tasksTypes array with the task types.
 * @param {Object} singleTaskData Object containing the date of the retrieved task by id.
 * @param {String} selectedStartDate The selected start date from date picker.
 * @param {function} setSelectedStartDate SelectedStartDate settter.
 * @param {function} selectedStartTime The selected start time from time picker.
 * @param {function} setSelectedStartTime SelectedStartTime setter.
 * @param {function} setSelectedEndTime SelectedEndTime setter.
 * @param {String} selectedEndDate The selected end date from date picker.
 * @param {function} selectedEndTime SelectedEndTime setter.
 * @param {String} source Variable indicate the source from which the component is being called.
 * @param {array} ownersOptionsInSelect array with the owners.
 * @param {boolean} loadOwners Show loader on load owners.
 * @param {array} checkedItems array with the checked guests.
 * @param {function} setCheckedItems CheckedItems state setter.
 * @param {function} setGuestsSearchQuery Set search guest query setter.
 * @param {array} checkedFollowers array with the checked followers.
 * @param {function} setCheckedFollowers CheckedFollowers state setter.
 * @param {function} setGuestsSearchQuery Set search guest query setter.
 * @param {String} guestsSearchQuery the search parameter in the search member input.
 * @param {function} setIdType Set the id of the task type.
 * @param {function} object Ask Aymen for this weird props.
 * @param {Object} addOnsValues This object contains the desccription and the note values.
 * @param {function} setAddOnsValues addOnsValues state setter.
 * @param {array} files array with the uploaded files (it works on update new file and on get task by id containing files).
 * @param {function} setFiles Set files state setter.
 * @param {String} taskToUpdate The id of the selected task (to get single task or to delete).
 * @param {array} pipelines Array of pipelines.
 * @param {Number} selectedStageId Selected stage Id.
 * @param {array} guestsList Array of members (familyId 1, 2, 4).
 * @param {array} ownersList Array of followers (familyId 4).
 * @param {Number} guestsListPage The page number of the members list.
 * @param {function} setGuestsListPage guestsListPage state setter.
 * @param {function} setFollowersSearchQuery followersSearchQuery state setter.
 * @param {boolean} deletedTaskIndicator To indicate if the clicked activity was deleted or no. if it was deleted, a friendly
 *                                       interface will tell the user that this task was removed.
 * @param {function} setCheckDueDateReminder checkDueDateReminder state setter.
 * @param {Number} guestsListLastPage the last page number of members list to tell the list component whether to load more or no.
 * @param {String} titleLabel This piece of state is used by ahmed from visio.
 * @param {function} setLoadSpecificTask Show loader on get activity by id state setter.
 *
 * @returns {JSX.Element} The rendered create task form component.
 */

import { useState, useEffect, useRef, useLayoutEffect, useMemo } from "react";
import {
  Form,
  Radio,
  Input,
  Button,
  DatePicker,
  Select,
  Popover,
  Badge,
  Tooltip,
  Skeleton,
  Space,
  TimePicker,
  Row,
  Col,
  Result,
  Checkbox,
  Card,
  Tabs,
  message,
  Upload,
  Tag,
  Divider,
  Spin,
  Cascader,
} from "antd";
import {
  PlusOutlined,
  ArrowRightOutlined,
  LoginOutlined,
  InfoCircleOutlined,
  CloseOutlined,
  WarningOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";
import { useSelector, useDispatch } from "react-redux";
import { useLocation } from "react-router-dom";
import { MdEventBusy, MdVideoCameraFront } from "react-icons/md";

import {
  disablePrevHours,
  disablePrevMinutes,
} from "../../utils/datesValidators";
import ChoiceIcons from "../components/ChoiceIcons";
import RichTextInput from "../../components/tiptap_richtext/RichTextInput";
import MainService from "../../services/main.service";
import UploadFiles from "../../components/UploadFiles";
import { getName } from "../layouts/chat/utils/ConversationUtils";
import AvatarGroup from "../../components/AvatarGroup";
import { URL_ENV } from "index";
import { getTokenRoom } from "../../new-redux/actions/visio.actions/createVisio";
import { AvatarChat } from "../../components/Chat";
import { useWindowSize } from "../clients&users/components/WindowSize";
import UsersList from "./UsersList";
import { handleCheck, uncheckGuest } from "./helpers/handleCheck";
import ColleaguesList from "./ColleaguesList";
import { EXTENSIONS_ARRAY } from "./helpers/calculateSum";
import ShareVisio from "./activityDetails/ShareVisio";
import VisioRecordings from "./activityDetails/VisioRecordings";
import { toastNotification } from "components/ToastNotification";
const { SHOW_CHILD } = Cascader;
const CreateTaskForm = ({
  form,
  onFinish,
  loadSpecificTask,
  prefillTaskLabel,
  tasksTypes,
  singleTaskData,
  selectedStartDate,
  setSelectedStartDate,
  setSelectedStartTime,
  setSelectedEndTime,
  selectedEndDate,
  selectedStartTime,
  setSelectedEndDate,
  ownersOptionsInSelect,
  loadOwners,
  checkedItems,
  setGuestsSearchQuery,
  checkedFollowers,
  source,
  setIdType,
  object,
  setAddOnsValues,
  addOnsValues,
  setFiles,
  files,
  taskToUpdate,
  pipelines,
  selectedStageId,
  guestsList,
  ownersList,
  setCheckedFollowers,
  setCheckedItems,
  guestsSearchQuery,
  guestsListPage,
  setGuestsListPage,
  setFollowersSearchQuery,
  deletedTaskIndicator,
  notFoundTaskIndicator,
  setCheckDueDateReminder,
  guestsListLastPage,
  titleLabel,
  setLoadSpecificTask,
  isError,
  setIsError,
  activeTabKey,
  setActiveTabKey = () => {},
  loadGuests,
  setEmailNotification,
  setSelectedFamilyMembers,
  openElementDetails,
  setGuestsList,
  setReminderValidator,
  reminderValidator,
  fromVue360,
  followersSearchQuery,
  totalEntities,
  setFollowersListPage,
  followersListLastPage,
  followersListPage,
  setOwnersList,
}) => {
  const [currentDate, setCurrentDate] = useState(dayjs());
  const [displayFollowersList, setDisplayFollowersList] = useState(false);
  const [familyElements, setFamilyElements] = useState([]);
  const [loadFamilyElements, setLoadFamilyElements] = useState(false);
  const [selectedTaskId, setSelectedTaskId] = useState(null);
  const [filterParticipantsValue, setFilterParticipantsValue] = useState(0);
  const [ownersListComponentKey, setOwnersListComponentKey] = useState(0);
  const [tags, setTags] = useState([]);
  const [exams, setExams] = useState([]);
  const [departments, setDepartments] = useState([]);
  const [loadingTags, setLoadingTags] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();
  const { user } = useSelector((state) => state?.user);
  const { openTaskDrawer } = useSelector((state) => state?.TasksRealTime);
  const { families } = useSelector((state) => state?.families);
  const newVisioTitle = useSelector((state) => state.visio.new.title);
  const dispatch = useDispatch();
  const location = useLocation();
  const [t] = useTranslation("common");
  const titleInputRef = useRef(null);
  const listHeight = useRef(null);
  const windowSize = useWindowSize();
  const moduleInfo = useSelector((state) => state?.contacts?.contactHeaderInfo);
  const { contactInfoFromDrawer, openView360InDrawer } = useSelector(
    (state) => state?.vue360
  );
  let now = dayjs();

  //The select displayed after (on the right side of) the reminder input.
  const reminderAddon = useMemo(
    () => (
      <Form.Item name="addonAfter" noStyle initialValue="minutes">
        <Select
          style={{ width: 100 }}
          placeholder={t("tasks.reminderSelect")}
          disabled={singleTaskData?.can_update_task === 0}
        >
          {["minutes", "hours", "days", "weeks"].map((value) => (
            <Select.Option key={value} value={value}>
              {value === "minutes" ? "Minutes" : t(`tasks.${value}`)}
            </Select.Option>
          ))}
        </Select>
      </Form.Item>
    ),
    [singleTaskData, t]
  );

  //   Trigger after submitting the form and verifying data failed
  const onFinishFailed = (err) => {
    form.scrollToField("");
  };

  //Detect if there is an error while filling the create form.
  const handleErrors = () => {
    let errorsArray = [];
    let data = form.getFieldsError();
    for (let i = 0; i < data.length; i++) {
      errorsArray.push(data[i]?.errors);
    }
    let nonEmptyArrays = errorsArray?.filter(
      (subArray) => subArray?.length > 0
    );
    setIsError(nonEmptyArrays?.length > 0);
  };

  // Trigger upload-docs API on upload files in create task form.
  const uploadImage = async (options) => {
    const { onSuccess, onError, file, onProgress } = options;
    try {
      const fmData = new FormData();
      // Handle progress bar indicator.
      const config = {
        onUploadProgress: (event) => {
          onProgress({ percent: (event?.loaded / event?.total) * 100 });
        },
      };
      fmData.append("upload[]", file);
      const response = await MainService.uploadFileInTask(fmData, config);
      onSuccess("Ok");
      setFiles([...files, response?.data?.data[0]]);
    } catch (err) {
      console.log("Eroor: ", err);
      onError({ err });
    }
  };

  // Validate file size and extension before upload.
  const beforeUploadValidation = (file) => {
    let allowedExtensions = [
      "pdf",
      "docx",
      "xlsx",
      "pptx",
      "txt",
      "csv",
      "jpg",
      "jpeg",
      "png",
      "svg",
      "zip",
    ];
    // Maximum file size in MB
    //Allow max size 15MB
    const maxFileSize = 15;
    const fileExt = file?.name?.split(".")?.pop()?.toLowerCase();
    const isAllowed = allowedExtensions?.includes(fileExt);
    const isSizeValid = file.size / 1024 / 1024 < maxFileSize;

    if (!isAllowed) {
      messageApi.open({
        duration: 3,
        type: "error",
        content: t("tasks.fileExtensionError"),
        style: {
          marginTop: "60vh",
        },
      });
      return Upload.LIST_IGNORE;
    }
    if (!isSizeValid) {
      messageApi.open({
        duration: 3,
        type: "error",
        content: t("chat.file.error_taile"),
        style: {
          marginTop: "60vh",
        },
      });
      return Upload.LIST_IGNORE;
    }

    return isAllowed && isSizeValid;
  };

  // On remove file trigger delete file api
  const removeUploadedFile = async (fileObject) => {
    let fileIndex =
      files && files.findIndex((file) => file?.fileName === fileObject?.name);

    if (fileIndex > -1) {
      try {
        let formData = new FormData();
        formData.append("id[]", files[fileIndex]?.id);
        setFiles(files.filter((file) => file?.id !== files[fileIndex]?.id));
      } catch (error) {
        console.log(`Error ${error}`);
      }
    }
  };

  useEffect(() => {
    const getData = async () => {
      setLoadSpecificTask(true);

      try {
        const [departmentsResponse, tagsResponse, examsResponse] =
          await Promise.all([
            MainService.getDepartments(),
            MainService.getTagsTasks(),
            MainService.getDataFamilies({ family_id: 5 }),
          ]);

        // Set departments
        const departments = departmentsResponse.data.data.map((el) => ({
          ...el,
          value: el.id,
        }));
        setDepartments(departments);

        // Set tags
        const tags = tagsResponse.data.map((el) => ({
          label: el.label,
          value: el.id,
          icon: <ChoiceIcons icon={el.icon} />,
        }));
        setTags(tags);

        // Set exams
        setExams(examsResponse.data);
      } catch (err) {
        console.log(err);
        toastNotification("error", t("toasts.somethingWrong"));
      } finally {
        if (!taskToUpdate) setLoadSpecificTask(false);
      }
    };

    getData();

    return () => {
      setSelectedTaskId("");
    };
  }, [user?.tenant, taskToUpdate]);

  // Set default values (task-type, owner, label) on open drawer (in create new task).
  useEffect(() => {
    if (Object.keys(singleTaskData)?.length === 0 && taskToUpdate === null) {
      setSelectedTaskId("");
      form.setFieldsValue({
        owner: {
          value: user?.id,
          label: (
            <>
              <AvatarChat
                fontSize="0.875rem"
                className="mb-0.5 mr-1.5 leading-[25px]"
                size={28}
                height={"[25px]"}
                width={"[25px]"}
                url={`${
                  URL_ENV?.REACT_APP_BASE_URL +
                  URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                }${user?.avatar}`}
                hasImage={EXTENSIONS_ARRAY?.includes(
                  user?.avatar && user?.avatar?.split(".")?.pop()
                )}
                name={getName(user?.label, "avatar")}
                type="user"
              />
              {getName(user?.label, "name")}
            </>
          ),
        },
      });
    }
    if (source === "visio") {
      setIdType(3);
      prefillTaskLabel(3);
      form.setFieldsValue({
        taskType: 3,
      });
    } else if (source === "mailing") {
      setIdType(2);
      prefillTaskLabel(2);
      form.setFieldsValue({ taskType: 2 });
    } else if (source === "call") {
      setIdType(4);
      prefillTaskLabel(4);
      form.setFieldsValue({ taskType: 4 });
    }
  }, [openTaskDrawer, singleTaskData, taskToUpdate]);

  // Ask aymen bouzayen for this one.
  useEffect(() => {
    if (source === "mailing") {
      form.setFieldsValue({
        title: object.label,
      });
    }
  }, [form, object]);

  // Select current date/time on open drawer to be used later for default values.
  useEffect(() => {
    setCurrentDate(
      selectedStartDate === ""
        ? now
        : dayjs(selectedStartDate, user?.location?.date_format)
    );
  }, [selectedStartDate]);

  // Prefill the rest of the fields on open drawer to create new task (this is done on two functions to fix
  // a rendering bug).
  useEffect(() => {
    if (Object.keys(singleTaskData).length === 0) {
      form.setFieldsValue({
        reminder: "30",
        addonAfter: "minutes",
      });
    }
  }, [form, openTaskDrawer, currentDate]);

  //Prefill default date/time values on create activity drawer,
  //and add one hour for end date.
  useEffect(() => {
    if (Object.keys(singleTaskData).length === 0) {
      const today = dayjs().format(user?.location?.date_format);
      if (
        dayjs(
          selectedStartDate ? selectedStartDate : now,
          user?.location?.date_format
        ).isSame(
          selectedEndDate
            ? dayjs(selectedEndDate, user?.location?.date_format)
            : dayjs(today, user?.location?.date_format)
        ) ||
        dayjs(
          selectedStartDate ? selectedStartDate : now,
          user?.location?.date_format
        ).isAfter(
          selectedEndDate
            ? dayjs(selectedEndDate, user?.location?.date_format)
            : dayjs(today, user?.location?.date_format)
        )
      ) {
        form.setFieldsValue({
          startDate: dayjs(currentDate, user?.location?.date_format),
          startTime:
            selectedStartTime === ""
              ? dayjs(now)
                  .startOf("hour")
                  .add(Math.ceil(dayjs(now).minute() / 60), "hour")
              : dayjs(selectedStartTime, user?.location?.time_format),
          endDate: handleAddDay(),
          endTime:
            selectedStartTime === ""
              ? dayjs(now)
                  .startOf("hour")
                  .add(Math.ceil(dayjs(now).minute() / 60), "hour")
                  .add(1, "hour")
              : dayjs(
                  form.getFieldValue("startTime"),
                  user?.location?.time_format
                ).add(1, "hour"),
        });
      }
    }
  }, [
    currentDate,
    form,
    selectedStartTime,
    singleTaskData,
    selectedStartDate,
    selectedEndDate,
    user,
  ]);

  //In case of midnight, transition the end date to the next day.
  const handleAddDay = () => {
    let currentTime = dayjs(now)
      .startOf("hour")
      .add(Math.ceil(dayjs(now).minute() / 60), "hour");

    let timeAfterSelection = dayjs(
      form.getFieldValue("startTime"),
      user?.location?.time_format
    )
      .startOf("hour")
      .add(
        Math.ceil(
          dayjs(
            form.getFieldValue("startTime"),
            user?.location?.time_format
          ).minute() / 60
        ),
        "hour"
      );
    if (
      currentTime.hour() === 23 ||
      currentTime.hour() === 0 ||
      timeAfterSelection.hour() === 23 ||
      timeAfterSelection.hour() === 0
    ) {
      return dayjs(currentDate, user?.location?.date_format).add(1, "day");
    } else {
      return dayjs(currentDate, user?.location?.date_format);
    }
  };

  // Set default values on open drawer from visio module.
  useEffect(() => {
    if (source === "visio") {
      form.setFieldsValue({
        tasks_type_id: 3,
        title: titleLabel || newVisioTitle,
      });
    }
  }, [source, form, newVisioTitle]);

  // Set stage field on open drawer.
  useEffect(() => {
    form.setFieldsValue({
      stage:
        selectedStageId === null
          ? pipelines && pipelines[0]?.stages[0]?.id
          : selectedStageId,
    });
  }, [pipelines, form, selectedStageId, openTaskDrawer]);

  // Select options in priorities component styles.
  const prioritySelectStyles = {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  };

  // Disable previous hours in time picker component.
  let disabledHours = disablePrevHours(
    selectedStartDate === ""
      ? dayjs(currentDate, user?.location?.date_format)
      : dayjs(selectedStartDate, user?.location?.date_format),
    selectedEndDate === ""
      ? dayjs(currentDate, user?.location?.date_format)
      : dayjs(selectedEndDate, user?.location?.date_format),
    selectedStartTime === ""
      ? dayjs(currentDate)
          .startOf("hour")
          .add(Math.ceil(dayjs(now).minute() / 60), "hour")
          .format(user?.location?.time_format)
      : selectedStartTime
  );

  // Add focus in 1st input (task label input).
  useEffect(() => {
    let timer = setTimeout(() => {
      Object.keys(singleTaskData)?.length > 0 &&
        titleInputRef?.current?.focus();
    }, 1);
    return () => clearTimeout(timer);
  }, [openTaskDrawer, singleTaskData]);

  // Get family elements by family id.
  const getFamilyElementsById = async (familyId) => {
    try {
      setLoadFamilyElements(true);
      const response = await MainService.getFamilyElement(familyId);
      setFamilyElements(response?.data?.data);
      setLoadFamilyElements(false);
      return response;
    } catch (error) {
      console.log(`Error ${error}`);
      setLoadFamilyElements(false);
    }
  };

  const getData = async () => {
    const element = openView360InDrawer ? contactInfoFromDrawer : moduleInfo;
    if (singleTaskData?.family_id || element) {
      let response = await getFamilyElementsById(
        singleTaskData?.family_id || element?.family_id
      );
      setFamilyElements(response?.data?.data);
      form.setFieldsValue({
        family: singleTaskData?.family_id || element.family_id,
        relatedElement: singleTaskData?.element_id || element.id,
      });
      setLoadSpecificTask(false);
    }
  };

  // Show family and element on update activity.
  useLayoutEffect(() => {
    if (Object.keys(singleTaskData)?.length > 0 && singleTaskData?.element_id) {
      getData();
    }
  }, [form, singleTaskData]);

  // Show/hide delete members/followers button based on some conditions.
  const authorizeManipulateTaskAttendees = (canUpdateTask, items) => {
    if (location?.pathname === "/tasks") {
      if (
        (Object.keys(singleTaskData).length > 0 &&
          canUpdateTask === 1 &&
          items &&
          items?.length > 1) ||
        (items && items?.length > 1)
      ) {
        return true;
      }
    } else if (items && items?.length > 1) {
      return true;
    }
  };

  //Check if slected type is of type meeting.
  const isTypeMeeting = (taskType) => {
    let selectedType = tasksTypes?.find(
      (el) => Number(el?.id) === Number(taskType)
    );
    return selectedType?.label?.toLowerCase() === "meeting";
  };

  // Set Fields values on update. (get activity by ID).
  useEffect(() => {
    if (Object.keys(singleTaskData)?.length > 0 && taskToUpdate) {
      form.resetFields();
      setSelectedStartTime(singleTaskData?.start_time);
      setSelectedStartDate(singleTaskData?.start_date);
      setSelectedTaskId(singleTaskData?.tasks_type_id);
      form.setFieldsValue({
        taskType: singleTaskData?.tasks_type_id,
        title: singleTaskData?.label,
        priority: singleTaskData?.priority,
        module:
          singleTaskData?.module_id != null
            ? singleTaskData?.module_id.map((el) => Number(el))
            : [],
        startDate:
          singleTaskData?.start_date && selectedStartDate === ""
            ? dayjs(singleTaskData?.start_date, user?.location?.date_format)
            : selectedStartDate !== "" &&
              dayjs(selectedStartDate, user?.location?.date_format),
        startTime:
          singleTaskData?.start_time && selectedStartTime === ""
            ? dayjs(
                singleTaskData?.start_time.toLowerCase(),
                user?.location?.time_format
              )
            : selectedStartTime !== "" &&
              dayjs(selectedStartTime, user?.location?.time_format),
        endDate:
          singleTaskData?.end_date && selectedStartDate === ""
            ? dayjs(singleTaskData?.end_date, user?.location?.date_format)
            : selectedStartDate !== "" &&
              dayjs(selectedStartDate, user?.location?.date_format),
        endTime:
          singleTaskData?.end_time && selectedStartTime === ""
            ? dayjs(
                singleTaskData?.end_time.toLowerCase(),
                user?.location?.time_format
              )
            : selectedStartTime !== "" &&
              dayjs(
                form.getFieldValue("startTime"),
                user?.location?.time_format
              ).add(1, "hour"),
        reminder:
          singleTaskData?.Reminder && singleTaskData?.Reminder.split(" ")[0],
        addonAfter:
          singleTaskData?.Reminder && singleTaskData?.Reminder.split(" ")[1],
        stage: singleTaskData?.stage_id,
        stage_id: singleTaskData?.stage_id,
        guests: checkedItems,
        followers: checkedFollowers,
        description: singleTaskData?.description,
        note: singleTaskData?.note,
        upload: singleTaskData?.upload,
        reminderEndDate: singleTaskData?.reminder_before_end,
        notificationsEmails: singleTaskData?.send_email,
        notificationsSettings: [
          singleTaskData?.send_message_system ? "systemMessage" : null,
          singleTaskData?.send_email ? "emailsNotifications" : null,
        ],
        location: singleTaskData?.location,
        tags_ids: singleTaskData?.tags_ids,
        doctor_id: singleTaskData?.doctor_id,
        exam_id: singleTaskData?.exam_id?.map((item) => {
          return [Number(item[0]), item[1]];
        }),
        department_id: singleTaskData?.department_id || undefined,
        code: singleTaskData?.code,
      });
      setCheckDueDateReminder(singleTaskData?.reminder_before_end);
      setAddOnsValues({
        ...addOnsValues,
        note: singleTaskData?.note,
        description: singleTaskData?.description,
      });
      singleTaskData?.upload && setFiles(singleTaskData?.upload);
    }
  }, [form, taskToUpdate, openTaskDrawer, singleTaskData]);
  //
  useEffect(() => {
    if (openTaskDrawer === false) {
      const firstTab = document.querySelector(".general-form-tab");
      const secondTab = document.querySelector(".additional-form-tab");
      if (firstTab) firstTab?.scrollIntoView({ top: 0, behavior: "smooth" });
      if (secondTab) secondTab?.scrollIntoView({ top: 0, behavior: "smooth" });
    } else {
      form.setFieldsValue({
        startDate: dayjs(now, user?.location?.date_format),
        startTime:
          selectedStartTime === ""
            ? dayjs(now)
                .startOf("hour")
                .add(Math.ceil(dayjs(now).minute() / 60), "hour")
            : dayjs(selectedStartTime, user?.location?.time_format),
        endDate: handleAddDay(),
        endTime:
          selectedStartTime === ""
            ? dayjs(now)
                .startOf("hour")
                .add(Math.ceil(dayjs(now).minute() / 60), "hour")
                .add(1, "hour")
            : dayjs(
                form.getFieldValue("startTime"),
                user?.location?.time_format
              ).add(1, "hour"),
      });
    }
  }, [openTaskDrawer, form]);

  //Scroll to top
  const scrollToInitial = () => {
    let time;
    clearTimeout(time);
    time = setTimeout(() => {
      let secondTab = document.getElementById("additional-form-tab");
      secondTab?.scrollIntoView({
        behavior: "auto",
        inline: "start",
        block: "start",
      });
    }, 1);
  };

  //Get families elements.
  useEffect(() => {
    if (fromVue360 && openElementDetails) {
      getData();
    }
  }, [location.pathname, form, openElementDetails, fromVue360]);

  //Format families elements to be displayed in antd select.
  const moduleElementsOptions = useMemo(
    () =>
      familyElements?.length
        ? familyElements.map((el) => ({
            label: el.label_data,
            value: el.id,
          }))
        : [],
    [familyElements]
  );

  // Handle change tab inside the form.
  const handleChangeTab = (active) => {
    setActiveTabKey(active);
    if (active === "additionalInfoActivities") {
      scrollToInitial();
    }
  };

  // Handle open popover containing list of all users (organizations, colleagues, contacts, leads).
  const handleOpenUsersListPopover = (open) => {
    if (!open) {
      setGuestsSearchQuery("");
      setGuestsListPage(1);
      setFilterParticipantsValue(0);
      setSelectedFamilyMembers([1, 2, 4, 9]);
    }
  };

  // Handle open the popover containing the list of colleagues.
  const handleOpenColleaguesListPopover = (open) => {
    setDisplayFollowersList(open);
    if (!open) {
      setFollowersSearchQuery("");
      setFollowersListPage(1);
    }
  };

  // handle scroll in owners list (select scroll).
  const handleOwnersListScroll = (event) => {
    const target = event.target;
    if (
      Math.trunc(target.scrollHeight - target.scrollTop) ===
        target.clientHeight &&
      followersListPage < followersListLastPage
    ) {
      setFollowersListPage(followersListPage + 1);
    }
  };

  //Close popup side effects.
  const handleCloseOwnersPopoup = (open) => {
    if (!open) {
      setFollowersSearchQuery("");
      setFollowersListPage(1);
      setOwnersListComponentKey((prev) => prev + 1);
    }
  };

  const examsOptionsInSelect = exams?.map((item) => ({
    label: item.label,
    value: item.id,
    children: item.items.map((subItem) => ({
      label: subItem.label,
      value: subItem._id,
    })),
  }));

  const filter = (inputValue, path) => {
    return path.some(
      (option) =>
        option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1
    );
  };
  // JSX element
  return (
    <Form
      form={form}
      onFinish={onFinish}
      onFinishFailed={onFinishFailed}
      layout="vertical"
      name="create-task-form"
      id="form"
      scrollToFirstError={true}
      requiredMark={true}
      style={{
        overflow: "none !important",
      }}
      onFieldsChange={handleErrors}
    >
      {contextHolder}
      {/* Show error if error happens on load activity by id or activity was already removed */}
      {loadSpecificTask ? (
        <Skeleton />
      ) : taskToUpdate !== null &&
        Object.keys(singleTaskData).length === 0 &&
        deletedTaskIndicator ? (
        <Result
          icon={<MdEventBusy style={{ fontSize: "72px", color: "red" }} />}
          title={t("tasks.activityDeleted")}
        />
      ) : notFoundTaskIndicator ? (
        <Result
          icon={<MdEventBusy style={{ fontSize: "72px", color: "red" }} />}
          title={t("tasks.userRemovedFromActivity")}
        />
      ) : taskToUpdate !== null &&
        Object.keys(singleTaskData).length === 0 &&
        !deletedTaskIndicator &&
        !notFoundTaskIndicator &&
        source !== "visio" ? (
        <Result status="error" title={t("toasts.somethingWrong")} />
      ) : (
        <>
          <Tabs
            style={{ marginRight: "-23px" }}
            activeKey={activeTabKey}
            tabBarExtraContent={
              Object.keys(singleTaskData)?.length > 0 &&
              Number(singleTaskData?.tasks_type_id) === 3 && (
                <div style={{ paddingRight: "24px" }}>
                  <Button
                    target="_blank"
                    type="primary"
                    icon={<LoginOutlined />}
                    onClick={() => {
                      dispatch(
                        getTokenRoom({
                          room: singleTaskData?.location,
                          errorText1: t("toasts.errorFetchApi"),
                          errorText2: t("toasts.errorRoomNotFound"),
                        })
                      );
                    }}
                  >
                    {/* {t("tasks.joinVisio")} */}
                    {t("chat.header.visio.join")}
                  </Button>
                  {singleTaskData?.visio_in_progress &&
                  singleTaskData?.visio_in_progress === 1 ? (
                    <Tag
                      icon={
                        <MdVideoCameraFront className="-mb-1 mr-1.5 animate-pulse text-sm" />
                      }
                      color="error"
                      style={{ marginLeft: "20px" }}
                      bordered={false}
                    >
                      {t("tasks.visioInProgress")}
                    </Tag>
                  ) : null}
                </div>
              )
            }
            onChange={handleChangeTab}
            items={[
              {
                label: (
                  <p style={{ color: isError ? "red" : "" }}>
                    {t("tasks.generalTab")}{" "}
                    {isError && <WarningOutlined style={{ color: "red" }} />}
                  </p>
                ),
                key: "generalInfoActivities",
                children: (
                  <div
                    style={{
                      overflowY: "auto",
                      padding: "0 1.2rem 1rem 0",
                      maxHeight: `${windowSize.height - 200}px`,
                    }}
                    className="general-form-tab"
                  >
                    <div
                      className={`flex flex-row items-center ${
                        source === "visio" ? "justify-end" : "justify-between"
                      }`}
                    >
                      {/* Select types */}
                      {source !== "visio" ? (
                        <Form.Item
                          name="taskType"
                          label={t("tasks.type")}
                          rules={[
                            { required: true, message: t("tasks.typeError") },
                          ]}
                          defaultValue={
                            source === "visio"
                              ? 3
                              : source === "mailing"
                              ? 3
                              : source === "call"
                              ? 2
                              : null
                          }
                        >
                          <Radio.Group
                            optionType="button"
                            buttonStyle="solid"
                            onChange={(e) => {
                              prefillTaskLabel(e.target.value);
                              setIdType(e.target.value);
                              setSelectedTaskId(e.target.value);
                            }}
                            disabled={
                              Object.keys(singleTaskData).length > 0
                                ? singleTaskData?.can_update_task === 0 ||
                                  Number(singleTaskData?.tasks_type_id) === 3
                                : false
                            }
                          >
                            <Row gutter={[8, 10]}>
                              {tasksTypes.map((el, i) => (
                                <Col
                                  className="gutter-row"
                                  key={`task_type_${i}`}
                                >
                                  <Tooltip title={el?.label}>
                                    <Radio.Button
                                      value={el?.id}
                                      buttonStyle="solid"
                                      disabled={
                                        Object.keys(singleTaskData).length > 0
                                          ? singleTaskData?.can_update_task ===
                                            0
                                          : false
                                      }
                                    >
                                      <ChoiceIcons icon={el?.icons} />
                                    </Radio.Button>
                                  </Tooltip>
                                </Col>
                              ))}
                            </Row>
                          </Radio.Group>
                        </Form.Item>
                      ) : (
                        ""
                      )}
                    </div>
                    {/* Insert title */}
                    <Form.Item
                      label={t("tasks.title")}
                      name="title"
                      rules={[
                        { required: true, message: t("tasks.titleError") },
                      ]}
                    >
                      <Input
                        placeholder={t("tasks.titlePlaceholder")}
                        disabled={singleTaskData?.can_update_task === 0}
                        showCount
                        maxLength={200}
                      />
                    </Form.Item>
                    {/* Select owner */}
                    <Form.Item
                      label={
                        user.tenant === "spheredev2" ||
                        user.tenant === "taoufikhospitals"
                          ? t("tasks.clinics")
                          : t("voip.departments")
                      }
                      rules={[
                        {
                          required:
                            user.tenant === "spheredev2" ||
                            user.tenant === "taoufikhospitals"
                              ? true
                              : false,
                          message: t("tasks.titleError"),
                        },
                      ]}
                      name="department_id"
                    >
                      <Select
                        showSearch
                        style={{
                          width: "100%",
                        }}
                        filterOption={(input, option) =>
                          (option?.label ?? "")
                            .toLowerCase()
                            .includes(input.toLowerCase())
                        }
                        placeholder={
                          user.tenant === "spheredev2" ||
                          user.tenant === "taoufikhospitals"
                            ? t("tasks.selectClinic")
                            : t("services.selectdepartment")
                        }
                        options={departments}
                        allowClear
                      />
                    </Form.Item>
                    {/* {selectedTaskId === 1 ? (
                      // &&
                      // user.tenant==="spheredev2"
                      <Form.Item
                        label={t("tasks.doctors")}
                        name="doctor_id"
                        rules={[
                          { required: true, message: t("tasks.titleError") },
                        ]}
                      >
                        <Select
                          showSearch
                          style={{
                            width: "100%",
                          }}
                          filterOption={false}
                          onSearch={setDoctorsSearchQuery}
                          loading={loadDoctors}
                          notFoundContent={
                            loadDoctors ? <Spin size="small" /> : null
                          }
                          onPopupScroll={handlePopupScrollDoctors}
                          placeholder={t("tasks.selectDoctor")}
                          options={doctors}
                          allowClear
                        />
                      </Form.Item>
                    ) : null} */}
                    {/* {selectedTaskId === 2 && user.tenant === "spheredev2" ? ( */}
                    <Form.Item
                      label={
                        user.tenant === "spheredev2" ||
                        user.tenant === "taoufikhospitals"
                          ? t("tasks.exams")
                          : t("tasks.products")
                      }
                      name="exam_id"
                      // initialValue={undefined}
                      // rules={[
                      //   { required: true, message: t("tasks.titleError") },
                      // ]}
                    >
                      <Cascader
                        style={{ width: "100%" }}
                        options={examsOptionsInSelect}
                        showSearch={{
                          filter,
                        }}
                        placeholder={t("emailTemplates.plsSelect")}
                        // onChange={onChange}
                        multiple
                        maxTagCount="responsive"
                        showCheckedStrategy={SHOW_CHILD}
                      />
                      {/* <Select
                          showSearch
                          style={{
                            width: "100%",
                          }}
                          filterOption={false}
                          onSearch={setExamsSearchQuery}
                          loading={loadExams}
                          notFoundContent={
                            loadExams ? <Spin size="small" /> : null
                          }
                          onPopupScroll={handlePopupScrollExams}
                          placeholder={t("tasks.selectExam")}
                          options={exams}
                          allowClear
                        /> */}
                    </Form.Item>
                    {/* ) : null} */}
                    <Form.Item
                      label={t("tasks.owner")}
                      name="owner"
                      rules={[
                        {
                          required: true,
                          message: t("tasks.ownerError"),
                        },
                      ]}
                      initialValue={
                        Object.keys(singleTaskData)?.length > 0 && {
                          value: singleTaskData?.owner_id?.id,
                          label: (
                            <>
                              <AvatarChat
                                fontSize="0.875rem"
                                className="mr-1.5"
                                size={28}
                                height={17}
                                width={17}
                                url={`${
                                  URL_ENV?.REACT_APP_BASE_URL +
                                  URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                                }${singleTaskData?.owner_id?.avatar}`}
                                hasImage={EXTENSIONS_ARRAY?.includes(
                                  singleTaskData?.owner_id?.avatar
                                    ?.split(".")
                                    ?.pop()
                                )}
                                name={getName(
                                  singleTaskData?.owner_id?.label,
                                  "avatar"
                                )}
                                type="user"
                              />
                              {getName(singleTaskData?.owner_id?.label, "name")}
                            </>
                          ),
                        }
                      }
                    >
                      <Select
                        key={ownersListComponentKey}
                        virtual
                        allowClear
                        placeholder={t("tasks.ownerPlaceholder")}
                        showSearch
                        options={ownersOptionsInSelect}
                        dropdownRender={(menu) => {
                          return (
                            <>
                              <Divider plain>
                                {`1-${ownersOptionsInSelect?.length} ${t(
                                  "mailing.of"
                                )} ${
                                  ownersOptionsInSelect?.length >
                                  totalEntities?.colleagues
                                    ? ownersOptionsInSelect?.length
                                    : totalEntities?.colleagues
                                }`}
                              </Divider>
                              <Spin spinning={loadOwners}>
                                <div id="ownersListInDrawerContainer">
                                  {menu}
                                </div>
                              </Spin>
                            </>
                          );
                        }}
                        onChange={(value, option) =>
                          form.setFieldsValue({
                            owner: value,
                          })
                        }
                        filterOption={false}
                        // suffixIcon={null}
                        loading={loadOwners}
                        disabled={
                          Object.keys(singleTaskData).length > 0 &&
                          singleTaskData?.can_update_task === 0
                        }
                        onPopupScroll={handleOwnersListScroll}
                        onSearch={(value) => setFollowersSearchQuery(value)}
                        onDropdownVisibleChange={handleCloseOwnersPopoup}
                      />
                    </Form.Item>
                    {source !== "visio" && (
                      <Form.Item label={t("menu2.tags")} name="tags_ids">
                        <Select
                          style={{
                            width: "100%",
                          }}
                          loading={loadingTags}
                          mode="multiple"
                          filterOption={(input, option) => {
                            var _a;
                            return (
                              (_a =
                                option === null || option === void 0
                                  ? void 0
                                  : option.label) !== null && _a !== void 0
                                ? _a
                                : ""
                            )
                              .toLowerCase()
                              .includes(input.toLowerCase());
                          }}
                          placeholder={t("tasks.selectTags")}
                          options={tags}
                          optionRender={(option) => (
                            <Space>
                              <span role="img" aria-label={option.data.label}>
                                {option.data.icon}
                              </span>
                              {option.data.label}
                            </Space>
                          )}
                          allowClear
                        />
                      </Form.Item>
                    )}
                    {/* Select pipeline/stage */}
                    <Form.Item
                      label={t("tasks.correspondingStage")}
                      name="stage"
                    >
                      <Select
                        disabled={
                          Object.keys(singleTaskData).length > 0 &&
                          singleTaskData?.can_update_task === 0
                        }
                        style={{
                          width: "100%",
                        }}
                        placeholder={t("tasks.correspondingStagePlaceholder")}
                        options={
                          pipelines &&
                          pipelines.map((pipeline) => ({
                            label: pipeline?.label,
                            value: pipeline?.pipeline_key,
                            options:
                              pipeline?.stages &&
                              pipeline?.stages.map((stage) => ({
                                label: (
                                  <>
                                    <Badge
                                      color={stage?.color}
                                      style={{ marginRight: "10px" }}
                                    />
                                    {stage?.label}{" "}
                                    {stage?.percent && stage?.percent > 0 ? (
                                      <span className="text-[#8c8c8c]">{`(${stage?.percent}%)`}</span>
                                    ) : null}
                                  </>
                                ),
                                value: stage?.id,
                              })),
                          }))
                        }
                        allowClear
                      />
                    </Form.Item>

                    {/* Select participants/followers */}
                    <Space
                      size={16}
                      direction="vertical"
                      style={{
                        width: "100%",
                        paddingTop: "16px",
                        paddingBottom: "16px",
                      }}
                    >
                      {/* Select participants */}
                      <Card
                        styles={{
                          body: {
                            height:
                              checkedItems && checkedItems?.length === 0
                                ? 0
                                : "auto",
                            padding:
                              checkedItems && checkedItems?.length === 0
                                ? 0
                                : "24px",
                          },
                          head: {
                            padding: "0 24px 0 10px",
                          },
                        }}
                        title={
                          <div className="flex flex-row items-center">
                            <Badge
                              count={
                                checkedItems &&
                                checkedItems.length > 0 &&
                                checkedItems.length
                              }
                              style={{
                                boxShadow: "none",
                                fontSize: "12px",
                                background: "transparent",
                                color: "rgb(100 116 139 / 1)",
                              }}
                              showZero={false}
                            />{" "}
                            <p style={{ color: "rgb(100 116 139 / 1)" }}>
                              {checkedItems &&
                                checkedItems.length === 0 &&
                                t("tasks.aucun")}{" "}
                              {t("tasks.guests", {
                                s:
                                  checkedItems && checkedItems.length > 1
                                    ? "s"
                                    : "",
                              })}
                            </p>
                          </div>
                        }
                        extra={
                          <>
                            {authorizeManipulateTaskAttendees(
                              singleTaskData?.can_update_task,
                              checkedItems
                            ) && (
                              <Button
                                type="link"
                                danger
                                icon={<CloseOutlined />}
                                onClick={() => setCheckedItems([])}
                                disabled={
                                  Object.keys(singleTaskData).length > 0 &&
                                  singleTaskData?.can_update_task === 0
                                }
                              >
                                {t("tasks.deleteAllBtn")}
                              </Button>
                            )}
                            <Popover
                              key={"guestsListFromDrawer"}
                              destroyTooltipOnHide={true}
                              content={
                                <UsersList
                                  key={"guestsListFromDrawer"}
                                  usersList={guestsList}
                                  searchQuery={guestsSearchQuery}
                                  setSearchQuery={setGuestsSearchQuery}
                                  lastPage={guestsListLastPage}
                                  currentPage={guestsListPage}
                                  setCurrentPage={setGuestsListPage}
                                  checkedItems={checkedItems}
                                  handleCheckedItems={handleCheck}
                                  loading={loadGuests}
                                  setCheckedItems={setCheckedItems}
                                  setSelectedFamilyMembers={
                                    setSelectedFamilyMembers
                                  }
                                  setGuestsList={setGuestsList}
                                  filterParticipantsValue={
                                    filterParticipantsValue
                                  }
                                  setFilterParticipantsValue={
                                    setFilterParticipantsValue
                                  }
                                  totalEntities={totalEntities}
                                />
                              }
                              showAudioInput={false}
                              autoAdjustOverflow={true}
                              title={t("tasks.guestsListTitle")}
                              trigger={["click"]}
                              onOpenChange={handleOpenUsersListPopover}
                              open={
                                Boolean(singleTaskData?.can_update_task === 0)
                                  ? false
                                  : undefined
                              }
                            >
                              <Button
                                type="link"
                                icon={<PlusOutlined />}
                                disabled={
                                  Object.keys(singleTaskData).length > 0 &&
                                  singleTaskData?.can_update_task === 0
                                }
                              >
                                {t("tasks.addGuestBtn")}
                              </Button>
                            </Popover>
                          </>
                        }
                        bordered={true}
                      >
                        <AvatarGroup
                          usersArray={checkedItems}
                          uncheckUser={uncheckGuest}
                          disableDelete={
                            Object.keys(singleTaskData).length > 0 &&
                            singleTaskData?.can_update_task === 0
                          }
                          setCheckedItems={setCheckedItems}
                        />
                      </Card>
                      {/* Select followers */}
                      <Card
                        bodyStyle={{
                          height:
                            checkedFollowers && checkedFollowers?.length === 0
                              ? 0
                              : "auto",
                          padding:
                            checkedFollowers && checkedFollowers?.length === 0
                              ? 0
                              : "24px",
                        }}
                        headStyle={{
                          padding: "0 24px 0 10px",
                        }}
                        bordered={true}
                        title={
                          <div className="flex flex-row items-center">
                            <Badge
                              count={
                                checkedFollowers &&
                                checkedFollowers.length > 0 &&
                                checkedFollowers.length
                              }
                              style={{
                                boxShadow: "none",
                                fontSize: "12px",
                                background: "transparent",
                                color: "rgb(100 116 139 / 1)",
                              }}
                              showZero={false}
                            />{" "}
                            <p style={{ color: "rgb(100 116 139 / 1)" }}>
                              {checkedFollowers &&
                                checkedFollowers.length === 0 &&
                                t("tasks.aucun")}{" "}
                              {t("tasks.followers", {
                                s:
                                  checkedFollowers &&
                                  checkedFollowers.length > 1
                                    ? "s"
                                    : "",
                              })}
                            </p>
                          </div>
                        }
                        extra={
                          <>
                            {authorizeManipulateTaskAttendees(
                              singleTaskData?.can_update_task,
                              checkedFollowers
                            ) && (
                              <Button
                                type="link"
                                danger
                                icon={<CloseOutlined />}
                                onClick={() => setCheckedFollowers([])}
                                disabled={
                                  Object.keys(singleTaskData).length > 0 &&
                                  singleTaskData?.can_update_task === 0
                                }
                              >
                                {t("tasks.deleteAllBtn")}
                              </Button>
                            )}
                            <Popover
                              key={"followersListFromDrawer"}
                              destroyTooltipOnHide={true}
                              content={
                                <ColleaguesList
                                  key={"followersListFromDrawer"}
                                  source="followersList"
                                  usersList={ownersList}
                                  searchQuery={followersSearchQuery}
                                  displayFollowersList={displayFollowersList}
                                  checkedItems={checkedFollowers}
                                  setCheckedItems={setCheckedFollowers}
                                  handleCheckedItems={handleCheck}
                                  setFollowersSearchQuery={
                                    setFollowersSearchQuery
                                  }
                                  loading={loadOwners}
                                  ref={listHeight}
                                  totalEntities={totalEntities}
                                  setCurrentPage={setFollowersListPage}
                                  currentPage={followersListPage}
                                />
                              }
                              title={
                                <>
                                  {t("tasks.followersListTitle")}
                                  <span className="ml-1 text-xs text-slate-500">
                                    {t("tasks.followersListTitleInfo")}
                                  </span>
                                </>
                              }
                              trigger={["click"]}
                              onOpenChange={handleOpenColleaguesListPopover}
                              open={
                                Boolean(singleTaskData?.can_update_task === 0)
                                  ? false
                                  : undefined
                              }
                              arrow={false}
                            >
                              <Button
                                type="link"
                                icon={<PlusOutlined />}
                                style={{
                                  float: "right",
                                  display: "inline-block",
                                }}
                                disabled={
                                  Object.keys(singleTaskData).length > 0 &&
                                  singleTaskData?.can_update_task === 0
                                }
                              >
                                {t("tasks.addFollowerBtn")}
                              </Button>
                            </Popover>
                          </>
                        }
                      >
                        <AvatarGroup
                          usersArray={checkedFollowers}
                          uncheckUser={uncheckGuest}
                          disableDelete={
                            Object.keys(singleTaskData).length > 0 &&
                            singleTaskData?.can_update_task === 0
                          }
                          setCheckedItems={setCheckedFollowers}
                        />
                      </Card>
                      {/* Send emails as notifications to selections */}
                      {((checkedItems && checkedItems?.length > 0) ||
                        (checkedFollowers && checkedFollowers?.length > 0)) && (
                        <Form.Item
                          name="notificationsSettings"
                          valuePropName="checked"
                          initialValue={singleTaskData?.send_email}
                        >
                          <Checkbox
                            onChange={(e) =>
                              setEmailNotification(e?.target?.checked)
                            }
                            defaultChecked={singleTaskData?.send_email}
                            disabled={
                              Object.keys(singleTaskData).length > 0 &&
                              singleTaskData?.can_update_task === 0
                            }
                          >
                            {t("tasks.inviteMembersParam")}
                          </Checkbox>
                          <Form.Item name="emailSystemNotifTooltip" noStyle>
                            <Tooltip title={t("tasks.emailSystemNotif")}>
                              <InfoCircleOutlined className="text-[rgb(0,0,0,0.45)] hover:cursor-help hover:text-[#1890ff]" />
                            </Tooltip>
                          </Form.Item>
                        </Form.Item>
                      )}
                    </Space>
                    {/* Date/time section */}
                    <Space direction="horizontal" align="center">
                      {/* Start date */}
                      <Col>
                        <Form.Item
                          name="startDate"
                          label={t("tasks.startDatePlaceholder")}
                        >
                          <DatePicker
                            allowClear={false}
                            format={user?.location?.date_format}
                            placeholder={t("tasks.startDatePlaceholder")}
                            onChange={(date) => {
                              setSelectedStartDate(
                                dayjs(date).format(user?.location?.date_format)
                              );
                            }}
                            disabled={
                              Object.keys(singleTaskData).length > 0 &&
                              singleTaskData?.can_update_task === 0
                            }
                          />
                        </Form.Item>
                      </Col>
                      {/* Start time */}
                      <Col>
                        <Form.Item
                          name="startTime"
                          label={t("tasks.startTime")}
                          initialValue={dayjs(now)
                            .startOf("hour")
                            .add(Math.ceil(dayjs(now).minute() / 60), "hour")}
                        >
                          <TimePicker
                            allowClear={false}
                            showNow={false}
                            format={user?.location?.time_format}
                            placeholder={t("tasks.startTimePlaceholder")}
                            onChange={(time) =>
                              setSelectedStartTime(
                                dayjs(time).format(user?.location?.time_format)
                              )
                            }
                            disabled={
                              Object.keys(singleTaskData).length > 0 &&
                              singleTaskData?.can_update_task === 0
                            }
                            minuteStep={15}
                          />
                        </Form.Item>
                      </Col>
                      <ArrowRightOutlined
                        style={{
                          marginTop: "15px",
                          marginRight: "9px",
                        }}
                      />
                      {/* End date */}
                      <Col>
                        <Form.Item
                          name="endDate"
                          label={t("tasks.endDatePlaceholder")}
                        >
                          <DatePicker
                            allowClear={false}
                            format={user?.location?.date_format}
                            placeholder={t("tasks.endDatePlaceholder")}
                            disabledDate={(date) => {
                              return selectedStartDate === ""
                                ? date && date < dayjs().startOf("day")
                                : date <
                                    dayjs(
                                      selectedStartDate,
                                      user?.location?.date_format
                                    ).startOf("day");
                            }}
                            onChange={(date) => {
                              setSelectedEndDate(
                                dayjs(date).format(user?.location?.date_format)
                              );
                            }}
                            disabled={
                              Object.keys(singleTaskData).length > 0 &&
                              singleTaskData?.can_update_task === 0
                            }
                          />
                        </Form.Item>
                      </Col>
                      {/* End time */}
                      <Col>
                        <Form.Item name="endTime" label={t("tasks.endTime")}>
                          <TimePicker
                            showNow={false}
                            format={user?.location?.time_format}
                            placeholder={t("tasks.endTimePlaceholder")}
                            onChange={(time) => {
                              let newTime = dayjs(time).startOf("hour");
                              setSelectedEndTime(
                                dayjs(time).format(user?.location?.time_format)
                              );
                              if (newTime.hour() === 0) {
                                form.setFieldsValue({
                                  endDate: dayjs(
                                    form.getFieldValue("endDate"),
                                    user?.location?.date_format
                                  ).add(1, "day"),
                                });
                              }
                            }}
                            disabledTime={() => {
                              return {
                                disabledHours: () => disabledHours,
                                disabledMinutes: (time) => {
                                  if (
                                    Object.keys(singleTaskData)?.length === 0
                                  ) {
                                    return disablePrevMinutes(
                                      selectedStartDate === ""
                                        ? dayjs(currentDate).format(
                                            user?.location?.date_format
                                          )
                                        : selectedStartDate,
                                      selectedEndDate === ""
                                        ? dayjs(currentDate).format(
                                            user?.location?.date_format
                                          )
                                        : selectedEndDate,
                                      selectedStartTime === ""
                                        ? dayjs(currentDate)
                                            .startOf("hour")
                                            .add(
                                              Math.ceil(
                                                dayjs(now).minute() / 60
                                              ),
                                              "hour"
                                            )
                                            .format(user?.location?.time_format)
                                        : selectedStartTime,
                                      time,
                                      user?.location?.time_format
                                    );
                                  } else if (
                                    Object.keys(singleTaskData)?.length > 0
                                  ) {
                                    return disablePrevMinutes(
                                      selectedStartDate === ""
                                        ? dayjs(currentDate).format(
                                            user?.location?.date_format
                                          )
                                        : selectedStartDate,
                                      selectedEndDate === ""
                                        ? dayjs(currentDate).format(
                                            user?.location?.date_format
                                          )
                                        : selectedEndDate,
                                      selectedStartTime === ""
                                        ? dayjs(currentDate)
                                            .startOf("hour")
                                            .add(
                                              Math.ceil(
                                                dayjs(now).minute() / 60
                                              ),
                                              "hour"
                                            )
                                            .format(user?.location?.time_format)
                                        : selectedStartTime,
                                      time,
                                      user?.location?.time_format
                                    );
                                  }
                                },
                              };
                            }}
                            disabled={
                              Object.keys(singleTaskData).length > 0 &&
                              singleTaskData?.can_update_task === 0
                            }
                            allowClear={false}
                            minuteStep={15}
                          />
                        </Form.Item>
                      </Col>
                    </Space>

                    <Row
                      style={{
                        width: "100%",
                        marginTop: "10px",
                        display: "grid",
                        gridTemplateColumns: "1fr 1fr",
                        gap: "10px",
                      }}
                    >
                      <Tooltip
                        title={
                          <div
                            dangerouslySetInnerHTML={{
                              __html: t("toasts.reminderTooltip"),
                            }}
                          />
                        }
                        open={reminderValidator}
                      >
                        <Form.Item
                          label={t("tasks.reminder")}
                          name="reminder"
                          initialValue="30"
                        >
                          <Input
                            min={1}
                            placeholder="Reminder"
                            addonAfter={reminderAddon}
                            disabled={
                              Object.keys(singleTaskData).length > 0 &&
                              singleTaskData?.can_update_task === 0
                            }
                            onKeyPress={(e) => {
                              const charCode = e.which ? e.which : e.keyCode;
                              if (charCode < 48 || charCode > 57) {
                                e.preventDefault();
                              } else {
                                setReminderValidator(false);
                              }
                            }}
                            onChange={(e) => {
                              if (e?.target?.value.trim() === "") {
                                setReminderValidator(true);
                              }
                            }}
                            onBlur={() => {
                              setReminderValidator(false);
                            }}
                          />
                        </Form.Item>
                      </Tooltip>
                      <Space>
                        <Col>
                          <Row>
                            <Form.Item
                              name="reminderStartDate"
                              valuePropName="checked"
                              initialValue={true}
                            >
                              <Checkbox disabled checked>
                                {t("tasks.reminderBeforeStart")}
                              </Checkbox>
                            </Form.Item>
                          </Row>
                          <Row>
                            <Tooltip
                              title={t("tasks.reminderInfo")}
                              placement="bottom"
                            >
                              <Form.Item
                                name="reminderEndDate"
                                valuePropName="checked"
                                // tooltip={TooltipDescription(t("tasks.reminderInfo"))}
                              >
                                <Checkbox
                                  disabled={
                                    Object.keys(singleTaskData).length > 0 &&
                                    singleTaskData?.can_update_task === 0
                                  }
                                  onChange={(e) =>
                                    setCheckDueDateReminder(e.target.checked)
                                  }
                                >
                                  {t("tasks.reminderBeforeDue")}
                                </Checkbox>
                              </Form.Item>
                            </Tooltip>
                          </Row>
                        </Col>
                      </Space>
                    </Row>
                    {/* Select family */}
                    <Form.Item
                      preserve={false}
                      label={t("tasks.selectFamily")}
                      name="family"
                      tooltip={t("tasks.selectModuleInfo")}
                    >
                      <Select
                        allowClear
                        showSearch
                        optionFilterProp={["label"]}
                        filterOption={(input, option) =>
                          option?.label
                            .toLowerCase()
                            .includes(input.toLowerCase())
                        }
                        disabled={
                          Object.keys(singleTaskData).length > 0 &&
                          singleTaskData?.can_update_task === 0
                        }
                        style={{
                          width: "100%",
                        }}
                        placeholder={t("tasks.selectFamilyPlaceholder")}
                        options={
                          families &&
                          families
                            .map((element) => ({
                              label: element?.label,
                              value: element?.id,
                            }))
                            .sort((a, b) => a?.label.localeCompare(b?.label))
                        }
                        onSelect={(value) => {
                          getFamilyElementsById(value);
                          form.setFieldsValue({
                            relatedElement: null,
                          });
                        }}
                        onClear={() => {
                          setFamilyElements([]);
                          form.resetFields(["relatedElement"]);
                        }}
                      />
                    </Form.Item>
                    {/* Select family element */}
                    <Form.Item
                      preserve={false}
                      label={t("tasks.selectFamilyElement")}
                      name="relatedElement"
                      tooltip={t("tasks.selectModuleElementInfo")}
                      rules={[
                        {
                          required: form.getFieldValue("family") ? true : false,
                          message: t("tasks.requiredElementOnSelectModule"),
                        },
                      ]}
                    >
                      <Select
                        allowClear
                        showSearch={
                          familyElements && familyElements?.length > 0
                        }
                        optionFilterProp={["label"]}
                        filterOption={(input, option) =>
                          option?.label
                            ?.toLowerCase()
                            .includes(input?.toLowerCase())
                        }
                        disabled={
                          Object.keys(singleTaskData).length > 0 &&
                          singleTaskData?.can_update_task === 0
                        }
                        style={{
                          width: "100%",
                        }}
                        // onSearch={handleSearchElementWithDebounce}
                        placeholder={t("tasks.selectFamilyElementPlaceholder")}
                        options={moduleElementsOptions}
                        loading={loadFamilyElements}
                      />
                    </Form.Item>
                    {/* Reminder */}
                  </div>
                ),
              },
              {
                label: t("tasks.detailsTab"),
                key: "additionalInfoActivities",
                children: (
                  <div
                    style={{
                      overflowY: "auto",
                      padding: "0 1.2rem 1rem 0",
                      maxHeight: `${windowSize?.height - 200}px`,
                    }}
                    className="additional-form-tab"
                  >
                    <div id="additional-form-tab" />
                    {/* Location input */}
                    {/* {isTypeMeeting(
                      Object.keys(singleTaskData).length > 0
                        ? singleTaskData?.tasks_type_id
                        : selectedTaskId
                    ) ? (
                      <Form.Item name="location" label="Location">
                        <Input
                          disabled={
                            Object.keys(singleTaskData).length > 0 &&
                            singleTaskData?.can_update_task === 0
                          }
                          placeholder={t("tasks.locationPlaceholder")}
                        />
                      </Form.Item>
                    ) : null} */}
                    <Form.Item
                      preserve={false}
                      label="Code"
                      name="code"
                      initialValue={""}
                    >
                      <Input placeholder="Code" />
                    </Form.Item>
                    {/* Priority Select */}
                    <Form.Item
                      preserve={false}
                      label={t("tasks.priority")}
                      name="priority"
                    >
                      <Select
                        placeholder={t("tasks.priorityPlaceholder")}
                        allowClear
                        disabled={
                          Object.keys(singleTaskData).length > 0 &&
                          singleTaskData?.can_update_task === 0
                        }
                      >
                        <Select.Option value="low">
                          <Badge
                            style={prioritySelectStyles}
                            color="#bfbfbf"
                            text={
                              <div className="text-[#bfbfbf]">
                                {t("tasks.lowPriority")}
                              </div>
                            }
                          />
                        </Select.Option>
                        <Select.Option value="medium">
                          <Badge
                            style={prioritySelectStyles}
                            color="#69b1ff"
                            text={
                              <div className="text-[#69b1ff]">
                                {t("tasks.mediumPriority")}
                              </div>
                            }
                          />
                        </Select.Option>
                        <Select.Option value="high">
                          <Badge
                            style={prioritySelectStyles}
                            color="#ffc53d"
                            text={
                              <div className="text-[#ffc53d]">
                                {t("tasks.highPriority")}
                              </div>
                            }
                          />
                        </Select.Option>
                        <Select.Option value="urgent">
                          <Badge
                            style={prioritySelectStyles}
                            color="#f5222d"
                            text={
                              <div className="text-[#f5222d]">
                                {t("tasks.urgentPriority")}
                              </div>
                            }
                          />
                        </Select.Option>
                      </Select>
                    </Form.Item>

                    {/* Description input */}
                    <Form.Item
                      preserve={false}
                      name="description"
                      label="Description"
                      help={
                        <span className="text-[11px] text-[#999]">
                          {t("tasks.descriptionHelp")}
                        </span>
                      }
                      style={{ paddingBottom: "15px" }}
                      className="activity-description-details"
                    >
                      <span>
                        <RichTextInput
                          showEmojiInEditor={false}
                          source="tasks"
                          // setEditorContent={() => {}}
                          setEditorContent={(e) => {
                            setAddOnsValues({
                              ...addOnsValues,
                              description: e,
                            });
                          }}
                          editorContent={addOnsValues?.description}
                          editable={
                            Object.keys(singleTaskData).length > 0
                              ? singleTaskData?.can_update_task !== 0
                              : true
                          }
                        />
                      </span>
                    </Form.Item>
                    {/* Note input */}
                    <Form.Item
                      preserve={false}
                      label="Note"
                      name="note"
                      help={
                        <span className="text-[11px] text-[#999]">
                          {t("tasks.noteHelp")}
                        </span>
                      }
                      style={{ paddingBottom: "15px" }}
                      className="activity-note-details"
                    >
                      <span>
                        <RichTextInput
                          showEmojiInEditor={false}
                          source="tasks"
                          // setEditorContent={() => {}}
                          setEditorContent={(e) => {
                            setAddOnsValues({ ...addOnsValues, note: e });
                          }}
                          editorContent={addOnsValues?.note}
                          editable={
                            Object.keys(singleTaskData).length > 0
                              ? singleTaskData?.can_update_task !== 0
                              : true
                          }
                        />
                      </span>
                    </Form.Item>
                    {/* Upload attachments */}
                    <Form.Item
                      preserve={false}
                      name="upload"
                      label={
                        <div className="flex flex-col justify-start">
                          {t("tasks.upload")}{" "}
                          <span className="text-[11px] text-[#999]">
                            {t("tasks.uploadRulesInfo")}
                          </span>
                        </div>
                      }
                    >
                      <UploadFiles
                        customRequest={uploadImage}
                        multiple={true}
                        listType="picture"
                        maxCount={6}
                        defaultFileList={
                          Object.keys(singleTaskData).length > 0 &&
                          singleTaskData?.upload !== null &&
                          singleTaskData?.upload.map((el) => ({
                            name: el?.fileName,
                            url: `${URL_ENV?.REACT_APP_BASE_URL}${el?.path}`,
                          }))
                        }
                        className="upload-list-inline"
                        removeUploadedFile={removeUploadedFile}
                        t={t}
                        disabled={
                          Object.keys(singleTaskData).length > 0 &&
                          singleTaskData?.can_update_task === 0
                        }
                        onPreview={(data) => window.open(data?.url, "_blank")}
                        beforeUpload={beforeUploadValidation}
                        allowedFiles={[
                          "pdf",
                          "docx",
                          "xlsx",
                          "pptx",
                          "txt",
                          "csv",
                          "jpg",
                          "jpeg",
                          "png",
                          "gif",
                        ]}
                        directory={false}
                      />
                    </Form.Item>
                  </div>
                ),
              },
              Number(singleTaskData?.tasks_type_id) === 3 &&
                singleTaskData?.recordings?.length > 0 && {
                  label: (
                    <div className="flex items-center">
                      {t("tasks.recordings")}{" "}
                      <Badge
                        count={singleTaskData?.recordings?.length}
                        size="small"
                        color="#faad14"
                        className="ml-2"
                      />
                    </div>
                  ),
                  key: "visioRecordings",
                  children: (
                    <VisioRecordings
                      items={singleTaskData?.recordings}
                      visioLink={singleTaskData?.location}
                    />
                  ),
                },
              Number(singleTaskData?.tasks_type_id) === 3 && {
                label: t("visio.infoCnx"),
                key: "visioParams",
                children: (
                  <ShareVisio
                    item={singleTaskData?.visio}
                    visioLink={singleTaskData?.location}
                  />
                ),
              },
            ]}
          />
        </>
      )}
    </Form>
  );
};

// Default props to minimize types related bugs.
CreateTaskForm.defaultProps = {
  titleLabel: "",
  loadSpecificTask: false,
  prefillTaskLabel: () => {},
  tasksTypes: [],
  singleTaskData: {},
  selectedStartDate: "",
  setSelectedStartDate: () => {},
  setSelectedStartTime: () => {},
  selectedEndTime: "",
  setSelectedEndTime: () => {},
  selectedEndDate: "",
  selectedStartTime: "",
  setSelectedEndDate: () => {},
  ownersOptionsInSelect: [],
  loadOwners: false,
  checkedItems: [],
  setGuestsSearchQuery: () => {},
  checkedFollowers: [],
  source: "",
  setIdType: () => {},
  object: {},
  setAddOnsValues: () => {},
  addOnsValues: "",
  setFiles: () => {},
  files: [],
  taskToUpdate: null,
  pipelines: [],
  selectedStageId: null,
  followersSearchQuery: "",
  guestsList: [],
  ownersList: [],
  setCheckedFollowers: () => {},
  setCheckedItems: () => {},
  guestsSearchQuery: "",
  guestsListPage: null,
  followersListPage: null,
  setGuestsListPage: () => {},
  setFollowersListPage: () => {},
  setFollowersSearchQuery: () => {},
  setCheckDueDateReminder: () => {},
  setSendNotificationEmail: () => {},
  setLoadSpecificTask: () => {},
  openElementDetails: true,
  setSelectedFamilyMembers: () => {},
  setIsError: () => {},
  setReminderValidator: () => {},
};

export default CreateTaskForm;
