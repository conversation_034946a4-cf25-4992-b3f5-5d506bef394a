import React from 'react';
import { <PERSON><PERSON>, But<PERSON>, Slider } from 'antd';
import { Resizable } from 'react-resizable';

const ImageResizeModal = ({
  isVisible,
  onCancel,
  imageSrc,
  imageSize,
  setImageSize,
  originalSize,
  aspectRatio,
  lockAspectRatio,
  setLockAspectRatio,
  onInsert,
  onReset,
  handleResize,
  imageRef,
}) => {
  return (
    <Modal
      title="Redimensionner l'image"
      open={isVisible}
      onCancel={onCancel}
      width={700}
      footer={[
        <Button key="reset" onClick={onReset}>
          Réinitialiser
        </Button>,
        <Button
          key="lock"
          type={lockAspectRatio ? "primary" : "default"}
          onClick={() => setLockAspectRatio(!lockAspectRatio)}
        >
          {lockAspectRatio
            ? "Proportions verrouillées"
            : "Proportions libres"}
        </Button>,
        <Button key="cancel" onClick={onCancel}>
          Annuler
        </Button>,
        <Button key="submit" type="primary" onClick={onInsert}>
          Insérer l'image
        </Button>,
      ]}
    >
      <div className="mb-4">
        <p className="mb-2">
          Redimensionnez l'image en faisant glisser les poignées:
        </p>
        <div className="my-4 flex justify-center">
          {imageSrc && (
            <Resizable
              width={imageSize.width}
              height={imageSize.height}
              onResize={handleResize}
              handle={
                <div
                  className="custom-resize-handle"
                  style={{
                    position: "absolute",
                    width: "10px",
                    height: "10px",
                    background: "#1890ff",
                    borderRadius: "50%",
                    bottom: "-5px",
                    right: "-5px",
                    cursor: "se-resize",
                  }}
                />
              }
            >
              <div
                style={{
                  width: `${imageSize.width}px`,
                  height: `${imageSize.height}px`,
                  position: "relative",
                  overflow: "hidden",
                  border: "1px dashed #d9d9d9",
                }}
              >
                <img
                  ref={imageRef}
                  src={imageSrc}
                  alt="Aperçu"
                  style={{
                    width: "100%",
                    height: "100%",
                    objectFit: "contain",
                  }}
                />
              </div>
            </Resizable>
          )}
        </div>

        <div className="mt-4">
          <p>
            Dimensions: {Math.round(imageSize.width)} ×{" "}
            {Math.round(imageSize.height)} pixels
          </p>
          <p className="text-sm text-gray-500">
            Dimensions originales: {originalSize.width} ×{" "}
            {originalSize.height} pixels
          </p>
        </div>

        <div className="mt-4">
          <p className="mb-2">Largeur:</p>
          <Slider
            min={50}
            max={Math.min(1000, originalSize.width * 2)}
            value={imageSize.width}
            onChange={(value) => {
              if (lockAspectRatio) {
                setImageSize({
                  width: value,
                  height: value / aspectRatio,
                });
              } else {
                setImageSize({
                  ...imageSize,
                  width: value,
                });
              }
            }}
          />
        </div>

        <div className="mt-4">
          <p className="mb-2">Hauteur:</p>
          <Slider
            min={50}
            max={Math.min(800, originalSize.height * 2)}
            value={imageSize.height}
            onChange={(value) => {
              if (lockAspectRatio) {
                setImageSize({
                  width: value * aspectRatio,
                  height: value,
                });
              } else {
                setImageSize({
                  ...imageSize,
                  height: value,
                });
              }
            }}
          />
        </div>
      </div>
    </Modal>
  );
};

export default ImageResizeModal;