import { <PERSON><PERSON>, Card, Col, Row, Typography } from "antd";
import React from "react";
import { PieChartWithLegend } from "./ChartsDashboard";
import { stylesCard } from "../Home4";
import EmptyPage from "components/EmptyPage";

const CardWithGraph = ({ stats, chartData, title }) => {
  const backgroundImagecard = "linear-gradient(to right, #F4F8F9,#F8F2F9)";

  return (
    <Card
      style={{ backgroundImage: backgroundImagecard }}
      title={title}
      styles={{ ...stylesCard }}
    >
      <Row gutter={[4, 4]} className="">
        <Col span={12}>
          <Row gutter={[4, 4]} className="">
            {stats.map((el, i) => (
              <Col span={24} key={i}>
                <div className="flex items-center justify-between rounded-md bg-white px-2 py-1 ">
                  <div className="flex  gap-x-2">
                    <Avatar
                      src={el.icon}
                      // className="bg-gray-100"
                    />
                    <Typography.Text className="self-center text-gray-500">
                      {el.name}
                    </Typography.Text>
                  </div>
                  <span className="text-base font-semibold">{el.number}</span>
                </div>
              </Col>
            ))}
          </Row>
        </Col>
        <Col
          span={12}
          style={{
            width: "100%",
            height: "100%",
            borderTopRightRadius: 6,
            borderBottomRightRadius: 6,
            background: "white",
          }}
        >
          {/* <Card style={{ minWidth: 300, width: "100%" }}>
            <Card.Grid hoverable={false} className="cardGraphDashboard"> */}

          {chartData?.data
            .map((el) => el.y)
            ?.reduce(
              (accumulator, currentValue) => accumulator + currentValue,
              0
            ) > 0 ? (
            <PieChartWithLegend
              height={173}
              data={chartData}
              alignLegend={{
                align: "right",
                verticalAlign: "middle",
                layout: "vertical",
                enabled: false,
              }}
              exporting={false}
            />
          ) : (
            <div className="h-[173px]">
              <EmptyPage />
            </div>
          )}
          {/* </Card.Grid> */}
          {/* </Card> */}
        </Col>
      </Row>
    </Card>
  );
};

export default CardWithGraph;
