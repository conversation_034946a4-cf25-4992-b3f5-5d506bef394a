import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Typo<PERSON>,
} from "antd";
import { ChevronLeft, ChevronRight } from "lucide-react";
import ChoiceIcons from "pages/components/ChoiceIcons";
import { backgroundImagecard } from "pages/home/<USER>";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";

const CardStatActivitiesTypes = () => {
  const [t] = useTranslation("common");
  const { statsTasks, tasks } = useSelector((state) => state.dashboardRealTime);
  const [indexTypesTasks, setIndexTypesTasks] = useState({ start: 0, end: 7 });

  return (
    <Card
      style={{ backgroundImage: backgroundImagecard, height: 353 }}
      title={
        <div className="flex items-center gap-x-1">
          <span>{t("tasks.activitytypes")}</span>{" "}
          <Badge
            count={tasks?.length}
            style={{
              backgroundColor: "#52c41a",
            }}
          />
        </div>
      }
      styles={{
        header: { padding: "4px 6px 4px 40px", minHeight: "auto" },
        body: { padding: 8 },
      }}
      extra={
        <div className="flex items-center gap-x-1">
          <span>
            {indexTypesTasks?.start +
              1 +
              "-" +
              (indexTypesTasks.end >= statsTasks.length
                ? statsTasks.length
                : indexTypesTasks.end) +
              " " +
              t("mailing.of") +
              " " +
              statsTasks?.length}
          </span>
          {statsTasks.length > 7 && (
            <>
              <Tooltip
                title={
                  indexTypesTasks.start === 0
                    ? ""
                    : t("dashboard.prevTasksTypes")
                }
              >
                <Button
                  size="small"
                  icon={<ChevronLeft size={16} />}
                  type="link"
                  disabled={indexTypesTasks.start === 0}
                  // style={{
                  //   visibility: indexTypesTasks.start === 0 ? "hidden" : "visible",
                  // }}
                  onClick={() => {
                    if (indexTypesTasks.start !== 0)
                      setIndexTypesTasks({
                        start: indexTypesTasks.start - 7,
                        end: indexTypesTasks.end - 7,
                      });
                  }}
                />
              </Tooltip>

              <Tooltip
                title={
                  indexTypesTasks.end >= statsTasks.length
                    ? ""
                    : t("dashboard.nextTasksTypes")
                }
              >
                <Button
                  icon={<ChevronRight size={16} />}
                  size="small"
                  type="link"
                  disabled={indexTypesTasks.end >= statsTasks.length}
                  // style={{
                  //   visibility:
                  //     indexTypesTasks.end >= statsTasks.length
                  //       ? "hidden"
                  //       : "visible",
                  // }}
                  onClick={() => {
                    if (indexTypesTasks.end <= statsTasks.length)
                      setIndexTypesTasks({
                        start: indexTypesTasks.start + 7,
                        end: indexTypesTasks.end + 7,
                      });
                  }}
                />
              </Tooltip>
            </>
          )}
        </div>
      }
    >
      <Row gutter={[4, 4]}>
        {statsTasks
          .sort((a, b) => b.count - a.count)
          .slice(indexTypesTasks.start, indexTypesTasks.end)
          .map((stat, i) => (
            <Col span={24} key={i}>
              <div className="flex items-center justify-between rounded-md bg-white px-2 py-2 ">
                <div className="flex items-center gap-x-2">
                  <ChoiceIcons
                    icon={stat.icons}
                    fontSize={"16px"}
                    color={stat?.color || "black"}
                  />
                  <Typography.Text className="text-gray-500">
                    {stat?.label}
                  </Typography.Text>
                </div>
                <span className="text-base font-semibold">{stat.count}</span>
              </div>
            </Col>
          ))}
      </Row>
    </Card>
  );
};

export default CardStatActivitiesTypes;
