import { Card } from "antd";
import { backgroundImagecard, GoTo, stylesCard } from "pages/home/<USER>";
import React, { useMemo } from "react";
import { Chart2Bars } from "../ChartsDashboard";
import { familyIcons } from "pages/components/DetailsProfile/ViewSphere2";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";

const CardStatFamilies = ({ start, end }) => {
  const [t] = useTranslation("common");

  const { totalFamilies } = useSelector((state) => state.dashboardRealTime);
  const { user } = useSelector((state) => state.user);

  return (
    <Card
      style={{ backgroundImage: backgroundImagecard }}
      styles={{
        ...stylesCard,
      }}
      title={
        <span>
          {t("dashboard.numberElementsPerModule")} &nbsp;
          <span className="invisible">
            <GoTo
              to={"2"}
              title={t("dashboard.queue")}
              navigate={() => {}}
              t={t}
              user={user}
            />
          </span>
        </span>
      }
    >
      <Chart2Bars
        data={useMemo(
          () => ({
            categories: totalFamilies.map(
              (el) =>
                familyIcons(t).find((fam) => fam.key === el.family_id)?.label
            ),
            series: [
              {
                name: t("dashboard.totalElements", { plural: "s" }),
                data: totalFamilies.map((el) => el.total_elements),
              },
              {
                name: t("dashboard.createdByYou", { plural: "s" }),
                data: totalFamilies.map((el) => el.created_by_you),
              },
            ],
            name: "",
          }),
          [totalFamilies, start, end]
        )}
        height={290}
      />
    </Card>
  );
};

export default CardStatFamilies;
