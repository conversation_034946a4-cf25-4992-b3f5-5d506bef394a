import { Card, Skeleton } from "antd";
import React from "react";

const LoadingGridViewSohere = () => {
  return (
    <div className="flex-1 bg-white p-6">
      {/* Dashboard */}
      <div className="mb-8">
        <Card className="mb-6">
          <div className="mb-4">
            <Skeleton.Input active size="default" className="mb-4 w-48" />
            <div className="mb-4 grid grid-cols-3 gap-4">
              <Skeleton.Input active size="small" className="w-full" />
              <Skeleton.Input active size="small" className="w-full" />
              <Skeleton.Input active size="small" className="w-full" />
            </div>
          </div>

          {/* Stats Grid */}
          <div className="mb-6 grid grid-cols-4 gap-6">
            {["<PERSON><PERSON><PERSON> directs", "Emails", "Meeting", "Visio"].map(
              (title, index) => (
                <div key={index} className="text-center">
                  <Skeleton.Input
                    active
                    size="small"
                    className="mx-auto mb-2 w-24"
                  />
                  <Skeleton.Input active size="large" className="mx-auto w-8" />
                </div>
              )
            )}
          </div>

          <div className="grid grid-cols-4 gap-6">
            {["Notes", "Comments", "Todolist", "Files"].map((title, index) => (
              <div key={index} className="text-center">
                <Skeleton.Input
                  active
                  size="small"
                  className="mx-auto mb-2 w-20"
                />
                <Skeleton.Input active size="large" className="mx-auto w-8" />
              </div>
            ))}
          </div>
        </Card>
      </div>

      {/* Enterprises Table */}
      <Card className="mb-6">
        <div className="mb-4 flex items-center justify-between">
          <Skeleton.Input active size="default" className="w-32" />
          <Skeleton.Button active size="small" shape="circle" />
        </div>

        {/* Table Header */}
        <div className="mb-4 grid grid-cols-6 gap-4 border-b border-gray-200 pb-3">
          {[
            "NOM COMPLET",
            "PROPRIÉTAIRE",
            "CHAMP",
            "PIPELINE",
            "STAGE",
            "",
          ].map((header, index) => (
            <div key={index} className="flex items-center space-x-2">
              <Skeleton.Input active size="small" className="w-20" />
              {index < 5 && (
                <Skeleton.Button active size="small" shape="circle" />
              )}
            </div>
          ))}
        </div>

        {/* Table Rows */}
        {[...Array(3)].map((_, rowIndex) => (
          <div
            key={rowIndex}
            className="grid grid-cols-6 gap-4 border-b border-gray-100 py-3"
          >
            <div className="flex items-center space-x-3">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-500">
                <Skeleton.Avatar
                  active
                  size="small"
                  shape="circle"
                  className="bg-transparent"
                />
              </div>
              <Skeleton.Input active size="small" className="w-24" />
            </div>
            <div className="flex items-center space-x-2">
              <div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-400">
                <Skeleton.Avatar
                  active
                  size="small"
                  className="bg-transparent"
                />
              </div>
              <Skeleton.Input active size="small" className="w-20" />
            </div>
            <Skeleton.Input active size="small" className="w-24" />
            <Skeleton.Input active size="small" className="w-16" />
            <Skeleton.Button active size="small" className="w-20" />
            <Skeleton.Button active size="small" shape="circle" />
          </div>
        ))}
      </Card>

      {/* Offers Section */}
      <Card>
        <div className="mb-4 flex items-center justify-between">
          <Skeleton.Input active size="default" className="w-24" />
          <Skeleton.Button active size="small" shape="circle" />
        </div>

        <div className="mb-4 grid grid-cols-6 gap-4 border-b border-gray-200 pb-3">
          {[...Array(6)].map((_, index) => (
            <Skeleton.Input key={index} active size="small" className="w-20" />
          ))}
        </div>

        {[...Array(2)].map((_, rowIndex) => (
          <div key={rowIndex} className="grid grid-cols-6 gap-4 py-3">
            <Skeleton.Input active size="small" className="w-32" />
            <div className="flex items-center space-x-2">
              <div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-400">
                <Skeleton.Avatar
                  active
                  size="small"
                  className="bg-transparent"
                />
              </div>
              <Skeleton.Input active size="small" className="w-20" />
            </div>
            <Skeleton.Input active size="small" className="w-24" />
            <Skeleton.Input active size="small" className="w-16" />
            <Skeleton.Button active size="small" className="w-20" />
            <Skeleton.Button active size="small" shape="circle" />
          </div>
        ))}
      </Card>

      {/* Pagination */}
      <div className="mt-6 flex justify-center">
        <div className="flex items-center space-x-2">
          <Skeleton.Button active size="small" shape="circle" />
          <Skeleton.Input active size="small" className="w-16" />
        </div>
      </div>
    </div>
  );
};

export default LoadingGridViewSohere;
