import { useState, useEffect, useCallback, useRef, useMemo } from "react";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { useWindowSize } from "pages/clients&users/components/WindowSize";
import { useDispatch } from "react-redux";
import {
  Divider,
  List,
  Skeleton,
  Input,
  Spin,
  Space,
  Dropdown,
  Typography,
  Badge,
  Segmented,
  Tooltip,
  Modal,
} from "antd";
import {
  InfoCircleOutlined,
  PhoneOutlined,
  MessageOutlined,
  MailOutlined,
  WarningOutlined,
} from "@ant-design/icons";
import { FiCopy, FiEdit, FiTrash } from "react-icons/fi";
import { FiSearch, FiMoreVertical } from "react-icons/fi";
import { fetchContactsTypes, fetchPhoneBook } from "../services/services";
import { toastNotification } from "../../../components/ToastNotification";
import { HighlightSearchW } from "../components/index";
import { uuid } from "../../../pages/layouts/chat/utils/ConversationUtils";
import debounce from "lodash/debounce";
import useActionCall from "../helpers/ActionCall";
import FilterPhoneBookData from "../components/FilterPhoneBookData";
import DisplayAvatar from "../components/DisplayAvatar";
import { URL_ENV } from "index";
import "../index.css";
import { RenderMembers } from "../groups/Groups";
import DisplayDirectoryInfo from "./DisplayDirectoryInfo";
import DisplayElementInfo from "../components/DisplayElementInfo";
import DisplayModuleIconAndText from "../components/DisplayModuleIconAndText";
import ChoiceIcons from "pages/components/ChoiceIcons";
import { openDrawerChat } from "new-redux/actions/voip.actions/handleDrawerChat";
import FormCreate from "pages/clients&users/components/FormCreate";
import { roles } from "utils/role";
import { deleteElements } from "pages/clients&users/services/services";
import { getFamilyNameById } from "pages/clients&users/FamilyRouting";
import FormUpdate from "pages/clients&users/components/FormUpdate";
import { ImUsers } from "react-icons/im";
import { FaUsers } from "react-icons/fa";
import { useInView } from "react-intersection-observer";
import VirtualList from "rc-virtual-list";
import { Refs_IDs } from "components/tour/tourConfig";
import { setEmailFields } from "pages/rmc/mailing/main-components/email-composer-modal/utils";
// Counter for directory instances to prevent multiple instances interfering with each other
let directoryInstanceCounter = 0;

const Directory = () => {
  const { ref, inView } = useInView();
  // Unique instance ID to track this component instance
  const instanceId = useRef(++directoryInstanceCounter);

  // Component mounted ref to prevent state updates after unmount
  const isMounted = useRef(true);

  // Central reference for all timeouts to ensure proper cleanup
  const timeoutRefs = useRef({});

  // References to child components that need cleanup
  const childComponentRefs = useRef({
    infoComponent: null,
    elementInfoModal: null,
    emailModal: null,
    createForm: null,
    updateForm: null,
  });

  // Translation and core hooks
  const [t] = useTranslation("common");
  const call = useActionCall();
  const dispatch = useDispatch();
  const searchRef = useRef(null);
  const windowSize = useWindowSize();
  const { user } = useSelector(({ user }) => user);
  const { openCreateForm, familyId } = useSelector((state) => state?.form);
  const scrollableDivRef = useRef(null);

  // Component state
  const [shouldFetchData, setShouldFetchData] = useState(true);
  const [dataSource, setDataSource] = useState([]);
  const [totalData, setTotalData] = useState(0);
  const [page, setPage] = useState(1);
  const limit = 50;
  const [loading, setLoading] = useState(true);
  const [search, setSearch] = useState("");
  const [displaySearch, setDisplaySearch] = useState("");
  const [dataTypes, setDataTypes] = useState([]);
  const [dataChannels, setDataChannels] = useState([]);
  const [selectedTypes, setSelectedTypes] = useState([]);
  const [selectedFamily, setSelectedFamily] = useState([]);
  const [selectedChannels, setSelectedChannels] = useState([]);
  const [contactInfoToDisplay, setContactInfoToDisplay] = useState(null);
  const [openDrawerInfo, setOpenDrawerInfo] = useState(false);
  const [elementDetails, setElementDetails] = useState({});
  const [typesAndChannels, setTypesAndChannels] = useState({});
  const [selectedSegmented, setSelectedSegmented] = useState("contacts");
  const [catchCreateNewElement, setCatchCreateNewElement] = useState(false);
  const [openDrawerUpdate, setOpenDrawerUpdate] = useState(false);
  const [elementDetailToUpdate, setElementDetailToUpdate] = useState(null);
  const [catchUpdateElement, setCatchUpdateElement] = useState(false);
  const hasNextPage = totalData > dataSource.length;
  // Utility function to safely set state only if component is mounted
  const safeSetState = useCallback((setter, value) => {
    if (isMounted.current) {
      setter(value);
    }
  }, []);

  // Safely clearing timeouts
  const clearAllTimeouts = useCallback(() => {
    Object.values(timeoutRefs.current).forEach(clearTimeout);
    timeoutRefs.current = {};
  }, []);

  // Reset Redux states that might be causing memory leaks
  const resetReduxStates = useCallback(() => {
    // Reset any ViewSphere related states
    dispatch({ type: "RESET_CONTACT_HEADER_INFO" });

    // Reset ViewSphere states
    dispatch({ type: "RESET_VUE360_STATE" });

    // Force reset of any conversations or chat states
    dispatch({ type: "RESET_CONVERSATIONS" });
  }, [dispatch]);

  // Clean up DOM elements that might be detached
  const cleanupDetachedElements = useCallback(() => {
    // Force remove any modals or popovers that might be detached
    const modalMasks = document.querySelectorAll(
      ".ant-modal-mask, .ant-modal-wrap"
    );
    modalMasks.forEach((el) => {
      if (el && el.parentNode) {
        el.parentNode.removeChild(el);
      }
    });

    // Remove any potential popovers
    const popovers = document.querySelectorAll(
      ".ant-popover, .ant-dropdown, .ant-tooltip"
    );
    popovers.forEach((el) => {
      if (el && el.parentNode) {
        el.parentNode.removeChild(el);
      }
    });

    // Clear any ViewSphere2 related elements that might be detached
    const viewSphereElements = document.querySelectorAll("#view-container");
    viewSphereElements.forEach((el) => {
      if (el && el.parentNode) {
        el.parentNode.removeChild(el);
      }
    });
  }, []);

  // Debounced search with proper cleanup
  const debouncedSearch = useCallback(
    debounce((nextValue) => {
      if (isMounted.current) {
        safeSetState(setSearch, nextValue);
      }
    }, 500),
    [safeSetState]
  );

  // Master cleanup function to ensure all resources are released
  useEffect(() => {
    return () => {
      isMounted.current = false;

      clearAllTimeouts();

      if (debouncedSearch && typeof debouncedSearch.cancel === "function") {
        debouncedSearch.cancel();
      }

      resetReduxStates();

      // Force cleanup of detached elements
      cleanupDetachedElements();

      // Reset state to help garbage collection
      setDataSource([]);
      setTypesAndChannels({});
      setContactInfoToDisplay(null);
      setElementDetails({});
      setElementDetailToUpdate(null);

      // Force clear child component references
      Object.keys(childComponentRefs.current).forEach((key) => {
        childComponentRefs.current[key] = null;
      });

      // Remove this instance from counter
      if (directoryInstanceCounter === instanceId.current) {
        directoryInstanceCounter--;
      }

      // Force GC in development mode
      if (process.env.NODE_ENV === "development" && window.gc) {
        try {
          window.gc();
        } catch (e) {
          console.error("Failed to force garbage collection", e);
        }
      }
    };
  }, [clearAllTimeouts, resetReduxStates, cleanupDetachedElements]);

  // Fetch contact types
  useEffect(() => {
    let isSubscribed = true;
    const fetchDataTypes = async () => {
      try {
        const { data } = await fetchContactsTypes();
        if (isSubscribed && isMounted.current) {
          safeSetState(setDataTypes, data.contact_types || []);
          safeSetState(setDataChannels, data.channels || []);

          const types = data.contact_types.reduce((acc, item) => {
            acc[item.label] = item.color;
            return acc;
          }, {});

          const channels = data.channels.reduce((acc, item) => {
            acc[item.label] = item.icon;
            return acc;
          }, {});

          safeSetState(setTypesAndChannels, { channels, types });
        }
      } catch (err) {
        if (
          isSubscribed &&
          isMounted.current &&
          err?.response?.status !== 401
        ) {
          toastNotification("error", t("toasts.somethingWrong"), "topRight");
        }
        console.error("Error fetching contact types:", err?.message || err);
      }
    };

    fetchDataTypes();

    return () => {
      isSubscribed = false;
    };
  }, [safeSetState, t]);

  // Update shouldFetchData when dependencies change
  useEffect(() => {
    if (isMounted.current) {
      safeSetState(setShouldFetchData, true);
    }
  }, [
    page,
    search,
    selectedTypes,
    selectedFamily,
    selectedChannels,
    safeSetState,
  ]);

  // Reset page and scrolling when filters change
  useEffect(() => {
    if (isMounted.current) {
      safeSetState(setPage, 1);
      safeSetState(setLoading, true);
      scrollableDivRef.current?.scrollTo(0, 0);
    }
  }, [search, selectedTypes, selectedFamily, selectedChannels, safeSetState]);

  // Fetch data source with proper cleanup
  const fetchDataSource = useCallback(async () => {
    if (!shouldFetchData || !isMounted.current) return;

    const modifiedSearch = /^\+?[0-9]+$/.test(search)
      ? search.replace("+", "00")
      : search;

    safeSetState(setLoading, true);

    try {
      const {
        data: {
          data,
          meta: { total },
        },
      } = await fetchPhoneBook(
        null,
        page,
        limit,
        modifiedSearch,
        selectedTypes?.join(),
        selectedFamily?.join(),
        selectedChannels?.join(),
        selectedSegmented
      );

      if (isMounted.current) {
        safeSetState(setTotalData, Number(total));

        const processedData = handleDataSource(data, user);

        safeSetState(
          setDataSource,
          page === 1 ? processedData : [...dataSource, ...processedData]
        );
      }
    } catch (err) {
      if (isMounted.current && err?.response?.status !== 401) {
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
      console.error("Error fetching phone book:", err?.message || err);
    } finally {
      if (isMounted.current) {
        safeSetState(setLoading, false);
        safeSetState(setShouldFetchData, false);
        searchRef?.current?.focus();
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shouldFetchData]);

  // Trigger fetch data when shouldFetchData changes
  useEffect(() => {
    fetchDataSource();
  }, [fetchDataSource]);

  // Re-Fetch data when a new element is created
  useEffect(() => {
    if (
      isMounted.current &&
      selectedSegmented === "contacts" &&
      catchCreateNewElement &&
      page === 1
    ) {
      safeSetState(setShouldFetchData, true);
      safeSetState(setCatchCreateNewElement, false);
    }
  }, [catchCreateNewElement, selectedSegmented, page, safeSetState]);

  // Handle search input changes
  const handleSearch = useCallback(
    (event) => {
      const inputValue = event?.target?.value;

      if (!isMounted.current) return;

      if (/^[0-9\s]*$/.test(inputValue)) {
        const searchText = inputValue.replace(/\s+/g, "");
        safeSetState(setDisplaySearch, searchText);
        debouncedSearch(searchText.trim());
      } else {
        safeSetState(setDisplaySearch, inputValue);
        debouncedSearch(inputValue.trim());
      }
    },
    [debouncedSearch, safeSetState]
  );

  // Load more data with proper timeout management
  const loadMoreData = useCallback(() => {
    if (!isMounted.current) return;

    // Clear any existing timeout
    if (timeoutRefs.current.loadMore) {
      clearTimeout(timeoutRefs.current.loadMore);
    }

    // Set new timeout with reference
    timeoutRefs.current.loadMore = setTimeout(() => {
      if (isMounted.current) {
        safeSetState(setPage, (p) => p + 1);
      }
      // Remove reference after execution
      delete timeoutRefs.current.loadMore;
    }, 300);
  }, [safeSetState]);

  // Handle opening message drawer
  const handleOpenMgsDrawer = useCallback(
    (uuid) => {
      dispatch(openDrawerChat(uuid));
    },
    [dispatch]
  );

  // Handle segmented control change
  const handleSegmented = useCallback(
    (value) => {
      if (!isMounted.current) return;

      searchRef?.current?.focus();
      safeSetState(setSelectedSegmented, value);
      safeSetState(setSelectedFamily, []);
      safeSetState(setSelectedChannels, []);
      safeSetState(setSelectedTypes, []);

      switch (value) {
        case t("menu1.contacts"):
          safeSetState(setSelectedFamily, ["1", "2", "9"]);
          break;
        case t("voip.colleagues"):
          safeSetState(setSelectedFamily, ["4"]);
          break;
        case t("voip.groups"):
          safeSetState(setSelectedFamily, ["group", "queue"]);
          break;
        default:
          break;
      }
    },
    [t, safeSetState]
  );

  // Handle keydown for Escape key
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === "Escape" && isMounted.current) {
        safeSetState(setContactInfoToDisplay, null);

        // Clear existing timeout
        if (timeoutRefs.current.resetHeader) {
          clearTimeout(timeoutRefs.current.resetHeader);
        }

        // Set new timeout with reference
        timeoutRefs.current.resetHeader = setTimeout(() => {
          if (isMounted.current) {
            dispatch({ type: "RESET_CONTACT_HEADER_INFO" });
          }
          // Remove reference after execution
          delete timeoutRefs.current.resetHeader;
        }, 1000);
      }
    };

    window.addEventListener("keydown", handleKeyDown);

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
      // Clean up timeout if component unmounts
      if (timeoutRefs.current.resetHeader) {
        clearTimeout(timeoutRefs.current.resetHeader);
      }
    };
  }, [dispatch, safeSetState]);

  // Handle opening drawer update
  const handleOpenDrawerUpdate = useCallback(
    (id, familyId, label) => {
      if (!isMounted.current) return;

      safeSetState(setElementDetailToUpdate, {
        id: id,
        label: label,
        familyId: familyId,
      });
      safeSetState(setOpenDrawerUpdate, true);
    },
    [safeSetState]
  );

  // Handle element update
  const handleElementUpdate = useCallback(async () => {
    if (!catchUpdateElement || !isMounted.current) return;

    try {
      const {
        data: { data },
      } = await fetchPhoneBook(elementDetailToUpdate?.id);

      if (!isMounted.current) return;

      if (contactInfoToDisplay?.id === elementDetailToUpdate?.id) {
        const contactInfo = contactInfoToDisplay;
        safeSetState(setContactInfoToDisplay, null);

        // Clear existing timeout
        if (timeoutRefs.current.updateContact) {
          clearTimeout(timeoutRefs.current.updateContact);
        }

        // Set new timeout with reference
        timeoutRefs.current.updateContact = setTimeout(() => {
          if (isMounted.current) {
            safeSetState(setContactInfoToDisplay, contactInfo);
          }
          // Remove reference after execution
          delete timeoutRefs.current.updateContact;
        }, 50);
      }

      safeSetState(setDataSource, (prevData) =>
        prevData.map((item) =>
          item.id === data.id ? handleDataSource([data], user)?.[0] : item
        )
      );
    } catch (err) {
      console.error("Failed to update element:", err);
    }

    safeSetState(setCatchUpdateElement, false);
  }, [
    catchUpdateElement,
    elementDetailToUpdate?.id,
    contactInfoToDisplay,
    user,
    safeSetState,
  ]);

  useEffect(() => {
    handleElementUpdate();
  }, [handleElementUpdate]);

  // Handle contact deletion with proper modal cleanup
  const handleDeleteContact = useCallback(
    (contactId, familyId, label) => {
      if (!contactId || !isMounted.current) return;

      let modalInstance = null;

      modalInstance = Modal.confirm({
        icon: <WarningOutlined />,
        title: <p className="truncate">{`${t("contacts.delete")} ${label}`}</p>,
        content: t("contacts.deleteConfirmMsg"),
        okText: t("profile.confirm"),
        okType: "danger",
        cancelText: t("profile.cancel"),
        onCancel() {
          // Ensure modal is destroyed
          modalInstance?.destroy();
        },
        onOk: () => {
          return new Promise(async (resolve, reject) => {
            try {
              const formData = new FormData();
              formData.append("ids[]", contactId);
              formData.append("family_id", familyId);
              const { status } = await deleteElements(formData);

              if (!isMounted.current) {
                modalInstance?.destroy();
                return resolve();
              }

              if (status === 200) {
                if (contactInfoToDisplay?.id === contactId) {
                  safeSetState(setContactInfoToDisplay, null);

                  // Clear existing timeout
                  if (timeoutRefs.current.deleteContact) {
                    clearTimeout(timeoutRefs.current.deleteContact);
                  }

                  // Set new timeout with reference
                  timeoutRefs.current.deleteContact = setTimeout(() => {
                    if (isMounted.current) {
                      dispatch({ type: "RESET_CONTACT_HEADER_INFO" });
                    }
                    // Remove reference after execution
                    delete timeoutRefs.current.deleteContact;
                  }, 500);
                }

                const index = dataSource.findIndex(
                  (item) => item.id === contactId
                );

                if (index !== -1) {
                  safeSetState(setDataSource, (prevData) => {
                    const newData = [...prevData];
                    newData.splice(index, 1);
                    return newData;
                  });

                  safeSetState(setTotalData, (prevTotal) => prevTotal - 1);
                }

                toastNotification(
                  "success",
                  <>
                    {`${t("contacts.the")} ${getFamilyNameById(t, familyId)} `}
                    <Typography.Text strong>{label}</Typography.Text>{" "}
                    {t("contacts.successDelete")}
                  </>,
                  "topRight",
                  5
                );

                resolve();
              }
            } catch (err) {
              if (!isMounted.current) {
                modalInstance?.destroy();
                return reject();
              }

              const status = err?.response?.status;
              const errMsg = Object.keys(err?.response?.data?.message || {});

              switch (status) {
                case 401:
                  reject();
                  break;
                case 409:
                  toastNotification(
                    "error",
                    <div>
                      {t("contacts.errorDelete")} <strong>{label}</strong>{" "}
                      {`${t("contacts.is")} `} {t("contacts.alreadyUsed")}{" "}
                      {errMsg?.map((e, i) => (
                        <strong key={e}>
                          {e}
                          {errMsg?.length !== i + 1 && ","}{" "}
                        </strong>
                      ))}
                      !
                    </div>,
                    "topRight",
                    7
                  );
                  reject();
                  break;
                default:
                  toastNotification(
                    "error",
                    t("toasts.somethingWrong"),
                    "topRight"
                  );
                  reject();
              }
            } finally {
              // Always ensure modal is destroyed to prevent memory leaks
              modalInstance?.destroy();
            }
          });
        },
      });

      return modalInstance;
    },
    [dataSource, contactInfoToDisplay, dispatch, t, safeSetState]
  );

  // DirectoryInfo props - wrap handler functions to prevent memory leaks
  const createMemorySafeFunction = useCallback((fn) => {
    return (...args) => {
      if (isMounted.current) {
        return fn(...args);
      }
    };
  }, []);

  // Memoize with proper cleanup
  const directoryInfoProps = useMemo(() => {
    const memorySafeSetOpenDrawerInfo =
      createMemorySafeFunction(setOpenDrawerInfo);
    const memorySafeSetElementDetails =
      createMemorySafeFunction(setElementDetails);

    return {
      contactInfo: contactInfoToDisplay,
      currentUser: user,
      handleOpenMgsDrawer: createMemorySafeFunction(handleOpenMgsDrawer),
      setOpenDrawerInfo: memorySafeSetOpenDrawerInfo,
      setElementDetails: memorySafeSetElementDetails,
      typesAndChannels,
    };
  }, [
    contactInfoToDisplay,
    user,
    handleOpenMgsDrawer,
    typesAndChannels,
    createMemorySafeFunction,
  ]);

  // Create a wrapper for the DisplayDirectoryInfo to allow ref attachment
  const DirectoryInfoWithRef = useCallback(
    (props) => (
      <DisplayDirectoryInfo
        {...props}
        ref={(el) => (childComponentRefs.current.infoComponent = el)}
      />
    ),
    []
  );

  useEffect(() => {
    if (!loading && hasNextPage && inView) {
      loadMoreData();
    }
  }, [inView, hasNextPage]);
  const vertualData = useMemo(
    () => dataSource.map((item) => ({ ...item, vertuialId: uuid() })),
    [dataSource]
  );
  //
  if (window.location.pathname !== "/telephony/directory") return null;
  //
  return (
    <div className="relative flex h-full w-full flex-row space-x-0.5 overflow-hidden ">
      <div className="w-1/5 space-y-2 py-1" style={{ minWidth: 370 }}>
        <div className="px-1">
          <Segmented
            block
            size="middle"
            options={[
              { value: "contacts", label: t("menu1.contacts") },
              { value: "colleagues", label: t("voip.colleagues") },
              { value: "groups", label: t("voip.groups") },
            ]}
            value={selectedSegmented}
            onChange={handleSegmented}
          />
        </div>
        <div className="flex flex-row space-x-1 px-2">
          <Input
            ref={searchRef}
            allowClear
            disabled={(!dataSource.length && !search.length) || loading}
            placeholder={`${t("voip.searchDirectory")}`}
            value={displaySearch}
            onChange={handleSearch}
            prefix={<FiSearch className="h-4 w-4 text-slate-400" />}
          />
          {selectedSegmented === "contacts" ? (
            <FilterPhoneBookData
              allTypes={dataTypes}
              allSources={dataChannels}
              setTypes={setSelectedTypes}
              types={selectedTypes}
              setFamilyId={setSelectedFamily}
              familyId={selectedFamily}
              setSources={setSelectedChannels}
              sources={selectedChannels}
              isDisabled={
                loading ||
                (!dataSource.length &&
                  !selectedFamily.length &&
                  !selectedTypes.length &&
                  !selectedChannels.length)
              }
            />
          ) : null}
        </div>
        {dataSource.length > 0 && (
          <Divider plain>
            {`1-${dataSource.length} of ${totalData} items`}
          </Divider>
        )}
        <div
          className="directory-scroll relative mt-4 py-1"
          id="scrollableDiv-directory"
          style={{
            maxHeight: windowSize?.height - 270,
          }}
        >
          <Spin spinning={loading}>
            <List
              key={selectedSegmented}
              className="voip-list px-0.5 "
              locale={{
                emptyText: loading ? (
                  <div className="flex h-64 flex-row items-center justify-center space-x-2 text-xl">
                    {t("contacts.loadingDataTable")}
                    <span className="animate-bounce">...</span>
                  </div>
                ) : (
                  ""
                ),
              }}
              style={{ padding: "0 4px" }}
            >
              <VirtualList
                data={vertualData}
                height={windowSize?.height - 268}
                // itemHeight={60}
                itemKey={"vertuialId"}
                className="membersList"
                style={{ padding: "4px 0" }}
              >
                {(item, index) => (
                  <div key={index + 1} className=" block">
                    <List.Item
                      className="!mb-[0.25rem] cursor-pointer rounded-md !border-b-0 !px-[0.5rem] !py-[0.25rem] transition-all duration-300 hover:bg-slate-200"
                      style={{
                        alignItems: "center",
                        backgroundColor:
                          item.id === contactInfoToDisplay?.id &&
                          "rgb(226 232 240)",
                      }}
                      key={item?.id}
                    >
                      <div
                        key={item?.id}
                        className="flex w-full flex-row items-center justify-between space-x-1 pr-1"
                      >
                        <List.Item.Meta
                          className=" [&_.ant-list-item-meta-avatar]:!mr-[0.5rem] "
                          onClick={() =>
                            safeSetState(setContactInfoToDisplay, item)
                          }
                          avatar={
                            <DisplayAvatar
                              name={item.name}
                              urlImg={item.image}
                              size={38}
                              icon={
                                item?.queue ? (
                                  <ImUsers />
                                ) : item?.group ? (
                                  <FaUsers />
                                ) : null
                              }
                            />
                          }
                          title={
                            <p className="mb-0 truncate font-semibold text-slate-900">
                              {HighlightSearchW(item?.name, search)}
                            </p>
                          }
                          description={
                            <div className="flex flex-nowrap  text-xs text-slate-500">
                              {renderDescription(
                                item,
                                typesAndChannels,
                                t,
                                true,
                                index,
                                user,
                                handleOpenMgsDrawer,
                                call
                              )}
                            </div>
                          }
                        />
                        <div
                          ref={
                            index === 0
                              ? Refs_IDs.logs_directory_dropDown_actions
                              : null
                          }
                        >
                          <DropDownAction
                            index={index}
                            item={item}
                            t={t}
                            call={call}
                            dispatch={dispatch}
                            setContactInfoToDisplay={setContactInfoToDisplay}
                            handleDeleteContact={handleDeleteContact}
                            handleOpenDrawerUpdate={handleOpenDrawerUpdate}
                          />
                        </div>
                      </div>
                    </List.Item>
                    {index < dataSource.length - 1 && (
                      <Divider style={{ margin: "0.1rem" }} key={index} />
                    )}
                    {index === dataSource.length - 1 && hasNextPage && (
                      <div className="mt-2">{loaderTemplate(ref)}</div>
                    )}
                  </div>
                )}
              </VirtualList>
            </List>
          </Spin>
        </div>
      </div>
      <div className="max-w-calc(100vw-370px) w-full">
        <DirectoryInfoWithRef {...directoryInfoProps} />
      </div>
      {openDrawerInfo && (
        <DisplayElementInfo
          open={openDrawerInfo}
          setOpen={setOpenDrawerInfo}
          elementDetails={elementDetails}
          ref={(el) => (childComponentRefs.current.elementInfoModal = el)}
        />
      )}
      {openCreateForm && (
        <FormCreate
          open={openCreateForm}
          setOpen={() => {}}
          familyId={familyId}
          setCatchChange={setCatchCreateNewElement}
          ref={(el) => (childComponentRefs.current.createForm = el)}
        />
      )}
      {openDrawerUpdate && (
        <FormUpdate
          open={openDrawerUpdate}
          setOpen={setOpenDrawerUpdate}
          elementDetails={elementDetailToUpdate}
          familyId={elementDetailToUpdate?.familyId}
          setCatchChange={setCatchUpdateElement}
        />
      )}
    </div>
  );
};

//
const copyIcon = (text) => (
  <div className="pl-1">
    <Typography.Paragraph
      copyable={{
        text: `${text}`,
        icon: [
          <FiCopy
            style={{
              color: "rgb(22, 119, 255)",
              marginTop: 2,
              fontSize: 15,
            }}
          />,
        ],
      }}
    />
  </div>
);
//
export const DropDownAction = ({
  item,
  call,
  t,
  dispatch,

  setContactInfoToDisplay,

  handleDeleteContact,
  handleOpenDrawerUpdate,
}) => {
  const {
    family_id,
    id,
    extension,
    phone: phones,
    name,
    uuid,
    email,
    group,
    queue,
    is_member,
    canBeDelete,
  } = item;

  const menuDropdown = () => {
    const items = [];

    const pushItem = (
      key,
      icon,
      label,
      onClick,
      disabled,
      children,
      danger
    ) => {
      items.push({
        key,
        icon,
        label,
        onClick,
        disabled,
        children,
        danger,
      });
    };

    if (family_id === 4) {
      if (extension && phones?.length) {
        pushItem(
          extension + id,
          <PhoneOutlined
            className="text-slate-400"
            rotate={100}
            style={{ fontSize: 14 }}
          />,
          t("voip.call"),
          null,
          null,
          [
            {
              key: extension,
              label: (
                <div className="flex flex-row justify-between space-x-1.5">
                  {extension}
                  {copyIcon(extension)}
                </div>
              ),
              onClick: () => call(`${extension}`, id, family_id),
            },
            ...phones.map(({ callNum, copyNum, displayNum }) => ({
              key: displayNum,
              label: (
                <div className="flex flex-row justify-between space-x-2">
                  {displayNum}
                  {copyIcon(copyNum)}
                </div>
              ),
              onClick: () => call(callNum, id, family_id),
            })),
          ]
        );
      } else
        pushItem(
          extension + id,
          <PhoneOutlined
            className=" text-slate-400"
            rotate={100}
            style={{ fontSize: 14 }}
          />,
          <div className="flex flex-row justify-between space-x-2">
            {`${t("voip.call")} ${extension}`}
            {copyIcon(extension)}
          </div>,
          () => call(extension, id, family_id)
        );
    }
    if (family_id && family_id !== 4 && phones.length) {
      if (phones.length === 1) {
        const { callNum, copyNum, displayNum } = phones[0];
        pushItem(
          displayNum + id,
          <PhoneOutlined
            className=" text-slate-400"
            rotate={100}
            style={{ fontSize: 14 }}
          />,
          <div className="flex flex-row justify-between space-x-2">
            {`${t("voip.call")} ${displayNum}`}
            {copyIcon(copyNum)}
          </div>,
          () => call(callNum, id, family_id)
        );
      } else
        pushItem(
          id,
          <PhoneOutlined
            rotate={100}
            className="text-slate-400"
            style={{ fontSize: 14 }}
          />,
          t("voip.call"),
          null,
          null,
          phones.map(({ callNum, copyNum, displayNum }) => ({
            key: displayNum,
            label: (
              <div className="flex flex-row justify-between space-x-2">
                {displayNum}
                {copyIcon(copyNum)}
              </div>
            ),
            onClick: () => call(callNum, id, family_id),
          }))
        );
    }
    if (group || queue) {
      pushItem(
        extension,
        <PhoneOutlined
          className="text-slate-400"
          rotate={100}
          style={{ fontSize: 14 }}
        />,
        <div className="flex flex-row justify-between space-x-2">
          {`${t("voip.call")} ${extension}`}
          {copyIcon(extension)}
        </div>,
        () => call(extension),
        is_member
      );
    }
    if (uuid) {
      items.push({ type: "divider" });
      pushItem(
        `chat-${uuid}`,
        <MessageOutlined
          className=" text-slate-400"
          style={{ fontSize: 14 }}
        />,
        <p className="max-w-[10rem] truncate">{`${t("voip.chatWith")}`}</p>,
        () => dispatch(openDrawerChat(uuid))
      );
    }
    if (email) {
      family_id !== 4 && items.length && items.push({ type: "divider" });
      if (item.length && items?.[items.length - 1]?.type !== "divider")
        items.push({ type: "divider" });
      pushItem(
        `email-${email}`,
        <MailOutlined className="text-slate-400" style={{ fontSize: 14 }} />,
        <p className="max-w-[10rem] truncate">{`Email`}</p>,
        () => {
          dispatch(setEmailFields({ receivers: [email] }));
        }
      );
    }
    if (family_id && family_id !== 4) {
      items.length && items.push({ type: "divider" });
      pushItem(
        `update-${id}`,
        <FiEdit className=" text-slate-400" style={{ fontSize: 14 }} />,
        <p className="max-w-[10rem] truncate">{`${t("contacts.edit")}`}</p>,
        () => handleOpenDrawerUpdate(id, family_id, name)
      );
    }
    if ((id && family_id) || group || queue) {
      items.length && items.push({ type: "divider" });

      pushItem(
        "more-info",
        <InfoCircleOutlined
          className=" text-slate-400"
          style={{ fontSize: 14 }}
        />,
        <p className="max-w-[10rem] truncate">
          {group || queue ? `${t("voip.moreInfo")}` : t("voip.view360")}
        </p>,
        () => setContactInfoToDisplay(item)
      );
      if (family_id && family_id !== 4) {
        pushItem(
          `delete-${id}`,
          <FiTrash className="h-4 w-4" style={{ fontSize: 14 }} />,
          <Tooltip
            placement="right"
            title={!canBeDelete ? t("voip.notTheOwner") : null}
          >
            <p className="max-w-[10rem] truncate">
              {`${t("contacts.delete")}`}
            </p>
          </Tooltip>,
          () => handleDeleteContact(id, family_id, name),
          !canBeDelete,
          null,
          true
        );
      }
    }

    return { items };
  };

  return (
    <Dropdown trigger={["click"]} placement="bottomRight" menu={menuDropdown()}>
      <FiMoreVertical className="h-[18px] w-[18px] cursor-pointer text-gray-400 hover:text-gray-700" />
    </Dropdown>
  );
};

export const renderDescription = (
  item,
  typesAndChannels,
  t,
  shouldRenderMembers,
  index,
  user,
  handleOpenMgsDrawer,
  call
) => {
  const descriptions = [];
  if (item.group || item.queue) {
    if (item.group) {
      descriptions.push(t("voip.group"));
    }
    if (item.queue) {
      descriptions.push(t("voip.queue"));
    }
    if (item.members.length) {
      item.is_member && descriptions.push(`(${t("voip.iAmMember")})`);
      if (!shouldRenderMembers)
        descriptions.push(`${item.members.length} Members`);
      else
        descriptions.push(
          <RenderMembers
            key={index}
            members={item?.members}
            call={call}
            poste={user.extension}
            handleOpenMsg={handleOpenMgsDrawer}
            maxCount={item.is_member ? 2 : 5}
            avatarSize={21}
            popoverTrigger="hover"
          />
        );
    }
  } else if (item.family_id === 4) {
    shouldRenderMembers && descriptions.push(item.extension);
    descriptions.push(<span className="select-all">{item?.email}</span>);
  } else {
    switch (item.family_id) {
      case 1:
        descriptions.push(
          <DisplayModuleIconAndText
            key={index}
            t={t}
            showText={true}
            hideTooltip={true}
            familyId={1}
          />
        );
        break;
      case 2:
        descriptions.push(
          <DisplayModuleIconAndText
            key={index}
            t={t}
            showText={true}
            hideTooltip={true}
            familyId={2}
          />
        );
        break;
      case 9:
        descriptions.push(
          <DisplayModuleIconAndText
            key={index}
            t={t}
            showText={true}
            hideTooltip={true}
            familyId={9}
          />
        );
        break;
      default:
        break;
    }
    item.type &&
      descriptions.push(
        <Badge
          color={typesAndChannels.types?.[item.type] || ""}
          dot
          text={<span className="text-slate-500">{item.type}</span>}
        />
      );
    item.source &&
      descriptions.push(
        <Space size={2}>
          <ChoiceIcons
            icon={typesAndChannels.channels?.[item.source]}
            fontSize={14}
          />
          {item.source}
        </Space>
      );
  }
  return (
    <Space
      size={item.group || item.queue ? 5 : 2}
      split={!(item.group || item.queue) && <Divider type="vertical" />}
    >
      {descriptions.map((element, index) => (
        <span key={index}>{element}</span>
      ))}
    </Space>
  );
};

export const handleDataSource = (data, currentUser) => {
  const dialCode = currentUser.location.dial_code || "";
  const userId = currentUser?._id;
  const userRole = currentUser?.role;

  const baseUrlImg =
    URL_ENV?.REACT_APP_BASE_URL + URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL;

  const createImageUrl = (image) => (image ? `${baseUrlImg}${image}` : null);

  const cleanerName = (name) => (name ? name.replace(/_/g, " ") : null);

  const formattingNumPhones = (phones) => {
    if (!Array.isArray(phones) || !phones.length) return [];
    return phones.map(([prefix, num]) => {
      const copyNum = `${prefix.replace("+", "00")}${num}`;
      const processedPrefix =
        prefix !== dialCode ? prefix.replace("+", "00") : "";
      return {
        displayNum: `(${prefix}) ${num}`,
        callNum: `${processedPrefix}${num}`,
        copyNum: copyNum,
      };
    });
  };

  const processItem = (item) => {
    const baseItem = {
      ...item,
      name: cleanerName(item.name),
      image: createImageUrl(item.image),
      extension: item.extension ? `${item.extension}` : undefined,
      canBeDelete:
        roles?.includes(userRole) || !item.owner_id || userId === item.owner_id
          ? true
          : false,
    };

    if (item.group || item.queue) {
      return {
        ...baseItem,
        id: `${item.extension}-${item.name}`,
        members:
          item.members.map((member) => ({
            ...member,
            name: cleanerName(member.name),
            extension: member.extension ? `${member.extension}` : undefined,
            image: createImageUrl(member.image),
          })) || [],
      };
    } else {
      return {
        ...baseItem,
        phone: formattingNumPhones(item.phone),
      };
    }
  };

  return data ? data.map(processItem) : [];
};
export const loaderTemplate = (ref) => (
  <div className="pl-2 pt-1.5" ref={ref}>
    {Array.from({ length: 2 }, (_, index) => (
      <Skeleton
        key={index}
        avatar
        paragraph={{
          rows: 1,
        }}
        active
      />
    ))}
  </div>
);

export default Directory;
