import React, { useLayoutEffect, useState } from "react";
import { Tabs } from "antd";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { roles } from "utils/role";

const Users = () => {
  const [t] = useTranslation("common");
  const [, setEditingKey] = useState("");
  const [, setEditingKeyStage] = useState("");
  const [keyTab, setKeyTab] = useState("");
  const [, setIdPipeline] = useState("");
  const { pathname } = useLocation();
  const navigate = useNavigate();

  const user = useSelector((state) => state.user.user);

  const items = [
    ...(roles.includes(user.role)
      ? [
          {
            label: <div>{t("users.users")}</div>,
            key: "1",
          },
        ]
      : []),

    {
      label: <div>{t("users.guests")}</div>,
      key: "4",
    },

    {
      label: <div> {t("users.roles")} </div>,
      key: "2",
    },
    process.env.REACT_APP_BRANCH.includes("dev") && {
      label: <div>{t("users.teams")}</div>,
      key: "3",
    },
  ];
  useLayoutEffect(() => {
    if (pathname == "/settings/users") {
      setKeyTab("1");
    } else if (pathname == "/settings/guests") {
      setKeyTab("4");
    } else if (pathname == "/settings/users/role") {
      setKeyTab("2");
    } else if (pathname == "/settings/users/team") {
      setKeyTab("3");
    }

    setIdPipeline("");
    setEditingKey("");
    setEditingKeyStage("");
  }, []);

  const handleTabClick = (key) => {
    // Action à effectuer lorsque l'onglet est cliqué
    if (key === "1") {
      navigate("/settings/users");
    } else if (key === "4") {
      navigate("/settings/guests");
    } else if (key === "2") {
      navigate("/settings/users/role");
    } else if (key === "3") {
      navigate("/settings/users/team");
    }
  };
  return (
    <div className="px-4 pt-4">
      {keyTab ? (
        <Tabs
          onChange={(key) => setKeyTab(key)}
          // activeKey={keyTab}
          defaultActiveKey={keyTab}
          items={items}
          type="card"
          onTabClick={handleTabClick}
        />
      ) : (
        ""
      )}
    </div>
  );
};

export default Users;
