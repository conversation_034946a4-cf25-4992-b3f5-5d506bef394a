import i18next from "i18next";

export const handleTranslateModuleLabel = (rawLabel) => {
  if (rawLabel === "companies") {
    return i18next.t("common:modules.companies");
  } else if (rawLabel === "contacts") {
    return i18next.t("common:modules.contacts");
  } else if (rawLabel === "leads") {
    return i18next.t("common:modules.leads");
  } else if (rawLabel === "deals") {
    return i18next.t("common:modules.deals");
  } else if (rawLabel === "tickets") {
    return i18next.t("common:modules.tickets");
  } else if (rawLabel === "projects") {
    return i18next.t("common:modules.projects");
  } else if (rawLabel === "products") {
    return i18next.t("common:modules.products");
  } else if (rawLabel === "booking") {
    return i18next.t("common:modules.bookings");
  }
};
