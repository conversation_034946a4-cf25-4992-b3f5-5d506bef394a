import { useState, useEffect } from "react";
import { Avatar, Image, Spin, Tooltip } from "antd";
import { LoadingOutlined, QuestionOutlined } from "@ant-design/icons";
import { getFirst2Chart } from "../helpers/helpersFunc";
// import { getLettersAvatarGroup } from "../groups/Groups";
import { useTranslation } from "react-i18next";
import { getFamilyNameById } from "pages/clients&users/FamilyRouting";

const DisplayAvatar = ({
  urlImg,
  name,
  size = 35,
  source,
  cursor,
  icon,
  tooltip = false,
  background = "",
  familyId,
  canPreview = false,
}) => {
  //
  const [t] = useTranslation("common");
  //
  const [imageError, setImageError] = useState(false);
  const [previewOpen, setPreviewOpen] = useState(false);
  //
  useEffect(() => {
    setImageError(false);
  }, [urlImg]);

  const fallbackContent =
    // isGroup && !!name ? (
    //   getLettersAvatarGroup(name)?.toLocaleUpperCase()
    // ) :
    !!name && typeof name === "string" ? (
      getFirst2Chart(name.replaceAll("_", " "))?.toLocaleUpperCase()
    ) : source === "viewSphere" ? (
      <Spin
        indicator={
          <LoadingOutlined
            style={{
              fontSize: size / 2,
            }}
            spin
          />
        }
      />
    ) : (
      <QuestionOutlined style={{ fontSize: size / 1.65, marginTop: 4 }} />
    );

  const classNameAvatar = `font-semibold ${
    cursor === "pointer" || (canPreview && !!urlImg)
      ? "cursor-pointer"
      : cursor === "help"
      ? "cursor-help"
      : "cursor-default"
  }`;

  return (
    <>
      <Tooltip
        title={
          tooltip === "me"
            ? t("voip.me")
            : tooltip
            ? `${name?.replaceAll("_", " ")} ${
                !!familyId ? `(${getFamilyNameById(t, familyId)})` : ""
              }`
            : null
        }
      >
        {icon ? (
          <Avatar
            icon={icon}
            size={size}
            className={classNameAvatar}
            style={{
              backgroundColor: background ? background : "rgb(219, 234, 254)",
              color: background ? "#0c004f" : "rgb(30, 64, 175)",
            }}
          />
        ) : urlImg && !imageError ? (
          <Avatar
            size={size}
            src={urlImg}
            className={classNameAvatar}
            onError={() => {
              setImageError(true);
              return true; // Return true to indicate the error is handled
            }}
            onClick={() => setPreviewOpen(true)}
          />
        ) : (
          <Avatar
            size={size}
            className={classNameAvatar}
            style={{
              backgroundColor: "rgb(219, 234, 254)",
              color: "rgb(30, 64, 175)",
            }}
          >
            <span
              style={{
                fontSize: size < 30 ? size / 2 : "",
              }}
            >
              {fallbackContent}
            </span>
          </Avatar>
        )}
      </Tooltip>

      {canPreview && !!urlImg && (
        <div className="hidden">
          <Image
            preview={{
              visible: previewOpen,
              onVisibleChange: (visible) => setPreviewOpen(visible),
              src: urlImg,
            }}
          />
        </div>
      )}
    </>
  );
};

export default DisplayAvatar;
