import { useState, useEffect, useRef, useCallback } from "react";

export function useWakeUpDetection(options = {}) {
  const {
    sleepThreshold = 10000, // 10 secondes par défaut
    onWakeUp,
    onSleep,
    checkInterval = 2000, // Vérification toutes les 2 secondes
  } = options;

  const [isSleeping, setIsSleeping] = useState(false);
  const [lastWakeTime, setLastWakeTime] = useState(null);
  const lastActiveTime = useRef(Date.now());
  const intervalRef = useRef(null);

  const handleWakeUp = useCallback(() => {
    const now = new Date();
    setIsSleeping(false);
    setLastWakeTime(now);
    onWakeUp?.(now);
  }, [onWakeUp]);

  const handleSleep = useCallback(() => {
    setIsSleeping(true);
    onSleep?.();
  }, [onSleep]);

  useEffect(() => {
    const checkSleepState = () => {
      const now = Date.now();
      const timeSinceLastCheck = now - lastActiveTime.current;

      // Si le temps écoulé dépasse le seuil, c'était probablement en veille
      if (timeSinceLastCheck > sleepThreshold && !isSleeping) {
        handleWakeUp();
      }

      lastActiveTime.current = now;
    };

    // Vérification périodique
    intervalRef.current = setInterval(checkSleepState, checkInterval);

    // Événements de visibilité de la page
    const handleVisibilityChange = () => {
      const now = Date.now();

      if (document.visibilityState === "hidden") {
        handleSleep();
      } else if (document.visibilityState === "visible") {
        const timeDiff = now - lastActiveTime.current;

        // Si longue période d'inactivité, c'était en veille
        if (timeDiff > sleepThreshold) {
          handleWakeUp();
        } else {
          setIsSleeping(false);
        }
      }

      lastActiveTime.current = now;
    };

    // Événements focus/blur de la fenêtre
    const handleFocus = () => {
      const now = Date.now();
      const timeDiff = now - lastActiveTime.current;

      if (timeDiff > sleepThreshold) {
        handleWakeUp();
      }

      lastActiveTime.current = now;
    };

    const handleBlur = () => {
      handleSleep();
    };

    // Ajouter les listeners
    document.addEventListener("visibilitychange", handleVisibilityChange);
    window.addEventListener("focus", handleFocus);
    window.addEventListener("blur", handleBlur);

    return () => {
      clearInterval(intervalRef.current);
      document.removeEventListener("visibilitychange", handleVisibilityChange);
      window.removeEventListener("focus", handleFocus);
      window.removeEventListener("blur", handleBlur);
    };
  }, [sleepThreshold, checkInterval, isSleeping, handleWakeUp, handleSleep]);

  return {
    isSleeping,
    lastWakeTime,
  };
}
