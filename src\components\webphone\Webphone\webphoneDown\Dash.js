import { Badge } from "antd";
function Dash({ notifAppel, notifAppelFromWB }) {
  return (
    <Badge count={notifAppel + notifAppelFromWB}>
      <div style={{ position: "relative", cursor: "grabbing" }}>
        <div className="rounded-full bg-gradient-to-t from-green-700 to-green-500 p-4 shadow-lg shadow-green-700/70">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="currentColor"
            className="h-7 w-7 text-green-50">
            <path
              fill-rule="evenodd"
              d="M1.5 4.5a3 3 0 013-3h1.372c.86 0 1.61.586 1.819 1.42l1.105 4.423a1.875 1.875 0 01-.694 1.955l-1.293.97c-.135.101-.164.249-.126.352a11.285 11.285 0 006.697 6.697c.**************.352-.126l.97-1.293a1.875 1.875 0 011.955-.694l4.423 1.105c.834.209 1.42.959 1.42 1.82V19.5a3 3 0 01-3 3h-2.25C8.552 22.5 1.5 15.448 1.5 6.75V4.5z"
              clip-rule="evenodd"></path>
          </svg>
        </div>
        <div
          style={{
            position: "absolute",
            height: "100%",
            width: "100%",
            backgroundColor: "transparent",
            top: "0",
            left: "0",
            borderRadius: "100%",
          }}></div>
      </div>
    </Badge>
  );
}

export default Dash;
