import {
  CalendarOutlined,
  CommentOutlined,
  DownloadOutlined,
  <PERSON>lip<PERSON>Outlined,
  FireOutlined,
  MailOutlined,
  MobileOutlined,
  PhoneOutlined,
  PlusOutlined,
  ShareAltOutlined,
  SnippetsOutlined,
  StarOutlined,
  UnorderedListOutlined,
  VideoCameraOutlined,
  WarningOutlined,
} from "@ant-design/icons";

import { Button, Dropdown, Menu, Space, Tooltip } from "antd";
import TicketIconSphere from "components/icons/TicketIconSphere";
import {
  Calendar,
  HeartHandshake,
  Mail,
  MessageCircle,
  Phone,
  Plus,
  Repeat2,
  StickyNote,
  Ticket,
} from "lucide-react";
import React from "react";
import { BiPhone } from "react-icons/bi";
import { FaSms } from "react-icons/fa";
import { HiOutlineCalendar } from "react-icons/hi";
import { HiOutlineTicket } from "react-icons/hi2";
import { MdEmail, MdOutlineEmail, MdOutlineSms, MdSms } from "react-icons/md";

const ListButtons = ({ setCurrentItem }) => {
  const itemsPhone = [
    {
      key: "1",
      label: "Action 1",
      icon: <WarningOutlined />,
    },
    {
      key: "2",
      label: "Action 2",
      icon: <MailOutlined />,
    },
    {
      key: "3",
      label: "Action 3",
      icon: <MobileOutlined />,
    },
  ];

  const itemsEmail = [
    {
      key: "1",
      label: "Action 1",
      icon: <WarningOutlined />,
    },
    {
      key: "2",
      label: "Action 2",
      icon: <MailOutlined />,
    },
    {
      key: "3",
      label: "Action 3",
      icon: <MobileOutlined />,
    },
  ];

  const itemsPlus = [
    {
      key: "1",
      label: "Add Deal",
      icon: <HeartHandshake size={14} />,
    },
    {
      key: "2",
      label: "Add Ticket",
      icon: <TicketIconSphere size={14} />,
    },
  ];
  return (
    <div className="flex justify-center">
      <Space>
        <Tooltip title="Call">
          <Dropdown
            menu={{
              items: itemsPhone,
            }}
          >
            <Button icon={<PhoneOutlined />} />
          </Dropdown>
        </Tooltip>

        <Tooltip title="Email">
          <Dropdown
            menu={{
              items: itemsEmail,
            }}
          >
            <Button icon={<MailOutlined />} />
          </Dropdown>
        </Tooltip>

        <Tooltip title="Email">
          <Button icon={<CalendarOutlined />} />
        </Tooltip>
        <Tooltip title="Activities">
          <Button
            icon={<VideoCameraOutlined />}
            onClick={() => setCurrentItem("activities")}
          />
        </Tooltip>

        <Dropdown
          placement="bottomRight"
          menu={{
            items: itemsPlus,
          }}
        >
          <Button icon={<PlusOutlined />} />
        </Dropdown>
        {/* <Space.Compact block>
        <Tooltip title="Call">
          <Dropdown
            placement="bottomRight"
            overlay={
              <Menu
                items={[
                  {
                    key: "1",
                    label: "Action 1",
                    icon: <WarningOutlined />,
                  },
                  {
                    key: "2",
                    label: "Action 2",
                    icon: <MailOutlined />,
                  },
                  {
                    key: "3",
                    label: "Action 3",
                    icon: <MobileOutlined />,
                  },
                ]}
              />
            }
            trigger={["hover"]}
          >
            <Button icon={<Phone className="w-[18px]" />} />
          </Dropdown>
        </Tooltip>
        <Tooltip title="SMS">
          <Button icon={<MessageCircle className="w-[18px]" />} />
        </Tooltip>
        <Tooltip title="Email">
          <Button icon={<Mail className="w-[18px]" />} />
        </Tooltip>
        <Tooltip title="Note">
          <Button icon={<StickyNote className="w-[18px]" />} />
        </Tooltip>

        <Tooltip title="Social">
          <Button icon={<Repeat2 className="w-[18px]" />} />
        </Tooltip>
        <Dropdown
          placement="bottomRight"
          overlay={
            <Menu
              items={[
                {
                  key: "1",
                  label: "Deal",
                  icon: <HeartHandshake className="w-[18px]" />,
                },
                {
                  key: "2",
                  label: "Ticket",
                  icon: <Ticket className="w-[18px]" />,
                },
                {
                  key: "3",
                  label: "Tasks",
                  icon: <Calendar className="w-[18px]" />,
                },
              ]}
            />
          }
          trigger={["hover"]}
        >
          <Button icon={<Plus className="w-[18px]" />} />
        </Dropdown>
      </Space.Compact> */}
      </Space>
    </div>
  );
};

export default ListButtons;
