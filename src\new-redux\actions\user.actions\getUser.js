import { logoutRmc } from "pages/profile/services";
import { RESET_STATE, SET_USER_INFOS } from "../../constants";
import { store } from "../../store";
import { LogoutLink } from "pages/layouts/chat/utils/ConversationUtils";

export const clearAllExcept = () => {
  const language = localStorage.getItem("language");
  const lastHref = localStorage.getItem("lastHref");
  const redirectChatLink = localStorage.getItem("redirectChatLink");
  localStorage.clear();
  if (language) localStorage.setItem("language", language);

  if (lastHref) localStorage.setItem("lastHref", lastHref);

  if (redirectChatLink)
    localStorage.setItem("redirectChatLink", redirectChatLink);
};

export const setUserInfos = (payload) => (dispatch) => {
  dispatch({ type: SET_USER_INFOS, payload });
};

const clearLogout = async (dispatch) => {
  let eventMercure = await store.getState().ChatRealTime.eventMercure;
  eventMercure?.close();
  await dispatch({ type: RESET_STATE });
  clearAllExcept();
  LogoutLink();
};
export const logoutOut =
  (navigate = undefined) =>
  async (dispatch) => {
    try {
      if (navigate) {
        logoutRmc();
        navigate("/logout");
      }
      await clearLogout(dispatch);
    } catch (error) {
      await clearLogout(dispatch);
    }
  };
