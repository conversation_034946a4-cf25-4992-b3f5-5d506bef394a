.editable-cell {
  position: relative;
}

.editable-cell-value-wrap {
  padding: 5px 12px;
  cursor: pointer;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  height: 30px;
}

.editable-row:hover .editable-cell-value-wrap {
  padding: 4px 11px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
}

[data-theme="dark"] .editable-row:hover .editable-cell-value-wrap {
  border: 1px solid #434343;
}

/* tile uploaded pictures */
.upload-list-inline .ant-upload-list-item {
  float: left;
  width: 200px;
  margin-inline-end: 8px;
}

.ant-upload-rtl.upload-list-inline .ant-upload-list-item {
  float: right;
}

/********************************************/

.activities-table .ant-table-tbody {
  padding: 0 !important;
}
.activities-table .ant-table-tbody > tr > td {
  padding: 5px 0 !important;
}

.activities-table .task-table-row {
  vertical-align: middle;
  @apply h-[32px];
}

.activities-table .ant-table-tbody .ant-table-measure-row {
  visibility: hidden !important;
  height: 0 !important;
}

.activities-table .ant-table-thead {
  padding: 0 !important;
}
.activities-table .ant-table-thead > tr > th {
  padding: 5px 7px !important;
}

/********************************************/

.ant-row.ant-row-no-wrap.stages-row {
  height: calc(100vh-150px);
}

.fc .fc-popover {
  max-height: 300px;
  overflow-y: scroll;
}
.ant-drawer-content.tasks-drawer .ant-drawer-wrapper-body .ant-drawer-body {
  overflow: hidden !important;
}

.ant-drawer-content-wrapper .ant-drawer-content.tasks-drawer .ant-drawer-body {
  padding: 0 24px 0 24px !important;
}

.ant-modal.css-dev-only-do-not-override-f8ehde.activity-360-modal-layout
  .ant-modal-content
  .ant-modal-header {
  width: calc(100vw + 48px);
}
.activity-sider-360 {
  background-color: rgb(71 85 105);
}
.activity-sider-360 > .ant-layout-sider-children {
  height: calc(100% + 1rem) !important;
}
.fc-toolbar-title {
  text-transform: capitalize;
}
.fc-errorBtn-button {
  background-color: #ff4d4f !important;
  border-color: #ff4d4f !important;
}
.fc-errorBtn-button:focus {
  background-color: #ff4d4f !important;
  border-color: #ff4d4f !important;
  outline: none !important;
  box-shadow: none !important;
}
.module-tag-text {
  display: inline-block;
  max-width: 100%; /* Set a fixed width or max-width */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.activity-details .ant-descriptions-item-container {
  display: flex;
  flex-direction: row !important;
  align-items: center !important;
}

.activity-description-details div .textEditor {
  max-width: 100% !important;
  margin-right: 10px;
}
.activity-note-details div .textEditor {
  max-width: 100% !important;
  margin-right: 10px;
}
.overdue-task .ant-picker-input input {
  color: red;
}
.ant-picker.css-dev-only-do-not-override-f8ehde.activity-details {
  border: none;
}
.ant-picker.css-dev-only-do-not-override-f8ehde.activity-details.ant-picker-focused {
  box-shadow: none;
}
.ant-descriptions-item.activity-details
  .ant-descriptions-item-container
  .ant-descriptions-item-label:after {
  content: " " !important;
}
.taskItem-card .ant-card-actions {
  height: 40px;
}
.ant-card.ant-card-bordered.ant-card-hoverable.taskItem-card
  .ant-card-actions
  > li {
  border-inline-end: hidden;
}

.ant-list.ant-list-split.kanban-list.css-dev-only-do-not-override-107yv8j li {
  border-block-end: none;
}

.ant-card.ant-card-bordered.kanban-header-card.css-dev-only-do-not-override-107yv8j.ant-card-head {
  border-bottom: none;
  border-radius: 8px 8px 0 0 !important;
}

.ant-card.record-video-card .ant-card-actions > li {
  padding: 0;
}

.activities-table .ant-table-body::-webkit-scrollbar {
  width: 10px;
  height: 5px;
}

.activities-table .ant-table-body::-webkit-scrollbar-thumb {
  -webkit-border-radius: 10px;
  border-radius: 10px;
  background: #eaeaea;
  border-radius: 0px;
}

.activities-table .ant-table-body::-webkit-scrollbar-track {
  background-color: white;
  -webkit-border-radius: 10px;
  border-radius: 10px;
  border-radius: 0px;
}

.activity-sider-360 .ant-layout-header {
  padding: 0;
  background-color: #f8fafc;
}
.activities-virtual-list .rc-virtual-list-holder {
  -ms-overflow-style: none; /* Internet Explorer 10+ */
  scrollbar-width: none; /* Firefox */
}
.activities-virtual-list .rc-virtual-list-holder::-webkit-scrollbar {
  display: none; /* Safari and Chrome */
}
