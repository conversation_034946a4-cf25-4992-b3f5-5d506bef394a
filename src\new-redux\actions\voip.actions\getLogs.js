import {
  GET_LOG_SUCCESS,
  SET_LOADING_TRUE,
  SET_LOADING_FALSE,
} from "../../constants";
import MainService from "../../../services/main.service";
import { toastNotification } from "components/ToastNotification";
import { Button, notification } from "antd";
import i18next from "i18next";
import { ReloadOutlined } from "@ant-design/icons";

export const getLogs =
  (makeItLoading, limit, setLoading = () => {}, displayToast = true) =>
  async (dispatch) => {
    try {
      makeItLoading && dispatch({ type: SET_LOADING_TRUE });
      const response = await MainService.journalApiIPBX(limit || "");
      notification.destroy("notification-webphone-error");
      dispatch({
        type: GET_LOG_SUCCESS,
        payload: response?.data || { data: [] },
      });
    } catch (error) {
      displayToast &&
        toastNotification(
          "error",
          <div className="flex w-full flex-col items-center justify-center ">
            <span>{i18next.t("common:voip.errorRetrievingLogData")}</span>
            <Button
              type="primary"
              danger
              onClick={() =>
                getLogs(makeItLoading, limit, setLoading)(dispatch)
              }
              className="mt-2 w-32"
              icon={<ReloadOutlined />}
            >
              {i18next.t("common:chat.reload")}
            </Button>
          </div>,
          "topLeft",
          3,
          null,
          1,
          "notification-webphone-error"
        );
    } finally {
      if (typeof setLoading === "function") {
        setLoading(false);
      }
      dispatch({ type: SET_LOADING_FALSE });
    }
  };

export const getVoiceFromMissedCall =
  (callsLog, voiceMail) => async (dispatch) => {
    const newVoiceMail = voiceMail?.[0];
    if (!callsLog.length || !newVoiceMail) return;
    const updatedCallsLog = callsLog.map((call) => {
     /* console.log(
        call.id_appel === newVoiceMail.id_appel,
        call.id_appel,
        newVoiceMail.id_appel
      );*/
      if (call.id_appel === newVoiceMail.id_appel) {
        return { ...call, voice_mail: newVoiceMail };
      } else {
        return call;
      }
    });
    dispatch({
      type: GET_LOG_SUCCESS,
      payload: { data: updatedCallsLog },
    });
  };
