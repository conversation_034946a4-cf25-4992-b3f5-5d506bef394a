import { store } from "new-redux/store";
import {
  SET_CHAT_SELECTED_CONVERSATION,
  UPDATE__CHAT_SELECTED_CONVERSATION,
  SET_CHAT_SELECTED_PARTICIPANTS,
  SET_SEARCH_CHAT_SIDEBAR,
  SET_FILTER_CHAT_PREVIEW,
  SET_CHAT_MEMBERS_GROUPS,
  SET_LOADING_SIDE_BAR,
  CREATE_GROUP_CHAT,
  SET_DELETE_MESSAGE_CHAT_MEMBERS,
  SET_UPDATE_MESSAGE_CHAT_MEMEBERS,
  SET_NEW_MESSAGE_CHAT_MEMEBERS,
  SET_MEMBRE_CHAT_PARTICIPANTS_DATE,
  SEARCH_MESSAGE_IN_LIST_CONVERSATION,
  SEARCH_MESSAGE_GET_DATA,
  SET_MEDIAS_LIST,
  SET_DOCUMENTS_LIST,
  ADD_NOTIFICATION_NUMBER,
  ADD_STARRED_MESSAGE,
  REMOVE_STARRED_MESSAGE,
  ADD_SAVED_MESSAGE,
  REMOVE_SAVED_MESSAGE,
  SET_NUMBER_UNREAD_MSG,
  SET_SYNC_NEW_MSG,
  SET_ASSETS_COUNT,
  SET_IMAGE_TO_LIST,
  SET_DOCUMENT_TO_LIST,
  SET_PINNED_LIST,
  SET_STARRED_LIST,
  SET_SEARCH_LIST,
  SET_ASSETS_TO_LIST,
  SET_PINNED_FETCHED,
  SET_STARRED_FETCHED,
  SET_THREAD_LIST,
  ADD_THREAD_TO_LIST,
  SET_OPEN_DRAWER,
  SET_MODAL_QUIT_GROUP,
  SET_ACTION_IN_SEARCH_STATUS,
  SET_SIDEBAR_DRAWER,
  SET_ARCHIVED_LIST_FETCHED,
  SET_CHAT_MEMBERS_GROUPS_FETCHED,
  ADD_CHAT_USER_LIST,
  REMOVE_CHAT_USER_LIST,
  UPDATE_CHAT_USER_LIST,
  UPDATE_CURRENT_USER_STATUS_BLOCKED,
  SET_ACTION_IN_REPLY,
  ADD_TO_MUTE_LIST,
  REMOVE_FROM_MUTE_LIST,
  RESET_STATE_OTHER_USER,
  UPDATE_THREAD_TO_LIST,
  SET_ALL_STARRED_LIST_FETCHED,
  SET_ALL_PINNED_LIST_FETCHED,
  SET_NOTIFICATION_CONFIG_USER,
  SET_DATA_CONVERSATION,
  ADD_POLL_LIST,
  ADD_ALL_POLL_LIST,
  SET_POLLS_VOTE,
  REMOVE_POLL_LIST,
  REMOVE_ALL__POLL_LIST,
  UPDATE_POLL_LIST,
  UPDATE_ALL_POLL_LIST,
  SET_OPEN_SIDE_BAR_DRAWER,
  ADD_UNREAD_MESSAGE_ARCHIVED,
  SET_DOCUMENTS_FILTRED_LIST,
  SET_LINKS_LIST,
  SET_LINKS_FILTRED_LIST,
  ADD_LINKS_TO_LIST,
  SET_LINKS_UPDATE_REMOVE_LIST,
  SET_EXTERNAL_ITEM_CHAT,
  SUBSTRACT_NOTIFICATION_NUMBER,
  HANDLE_TYPING_USER,
} from "../../constants";

export const setChatSelectedConversation = (payload) => (dispatch) => {
  dispatch({ type: SET_CHAT_SELECTED_CONVERSATION, payload });
};
export const updateChatSelectedConversation = (payload) => (dispatch) => {
  dispatch({ type: UPDATE__CHAT_SELECTED_CONVERSATION, payload });
};

export const setChatSelectedParticipants = (payload) => (dispatch) => {
  dispatch({ type: SET_CHAT_SELECTED_PARTICIPANTS, payload });
};

export const setNumberUnreadMsg = (payload) => (dispatch) => {
  dispatch({ type: SET_NUMBER_UNREAD_MSG, payload });
};
export const setSyncNewMsg = (payload) => (dispatch) => {
  dispatch({ type: SET_SYNC_NEW_MSG, payload });
};

export const setSearchChatSidebar = (payload) => (dispatch) => {
  dispatch({ type: SET_SEARCH_CHAT_SIDEBAR, payload });
};

export const setDisplayMembersRoomsPreview = (payload) => (dispatch) => {
  dispatch({ type: SET_FILTER_CHAT_PREVIEW, payload });
};

export const setMembersGroupsChat = (payload) => (dispatch) => {
  dispatch({
    type: SET_CHAT_MEMBERS_GROUPS,
    payload,
  });
};

export const setExternalItem = (payload) => (dispatch) => {
  dispatch({
    type: SET_EXTERNAL_ITEM_CHAT,
    payload,
  });
};

export const setLoadingSideBar = (payload) => (dispatch) => {
  dispatch({
    type: SET_LOADING_SIDE_BAR,
    payload,
  });
};

export const AddGroupToMembersGroupChat = (payload) => (dispatch) => {
  dispatch({ type: CREATE_GROUP_CHAT, payload: payload });
};

export const setDeleteMessageInChatMembers = (payload) => (dispatch) => {
  dispatch({
    type: SET_DELETE_MESSAGE_CHAT_MEMBERS,
    payload,
  });
};
export const setUpdateMessageInChatMembers = (payload) => (dispatch) => {
  dispatch({
    type: SET_UPDATE_MESSAGE_CHAT_MEMEBERS,
    payload,
  });
};
export const setNewMessageChatMemebers = (payload) => (dispatch) => {
  dispatch({
    type: SET_NEW_MESSAGE_CHAT_MEMEBERS,
    payload,
  });
};
export const setMembreChatParticipantsDate = (payload) => (dispatch) => {
  dispatch({ type: SET_MEMBRE_CHAT_PARTICIPANTS_DATE, payload });
};
export const searchMsgInListConversations = (payload) => (dispatch) => {
  dispatch({
    type: SEARCH_MESSAGE_IN_LIST_CONVERSATION,
    payload,
  });
};
export const setActionInSearchStatus = (payload) => (dispatch) => {
  dispatch({ type: SET_ACTION_IN_SEARCH_STATUS, payload });
};
export const setActionInReply = (payload) => (dispatch) => {
  dispatch({ type: SET_ACTION_IN_REPLY, payload });
};

export const serachMessageData = (payload) => (dispatch) => {
  dispatch({
    type: SEARCH_MESSAGE_GET_DATA,
    payload,
  });
};
export const stopSearchMsg = (forced) => (dispatch) => {
  dispatch({
    type: SEARCH_MESSAGE_IN_LIST_CONVERSATION,
    payload: {
      virtual: forced ? null : undefined,
      count_new_message: 0,
      discussion_id: null,
      loading: false,
      miniLoadingPrevious: false,
      hasMorePrevious: true,
      hasMoreNext: true,
      miniLoadingNext: false,
      id: null,
      orientation: "",
      last_id: null,
      first_id: null,
    },
  });
  dispatch({
    type: SEARCH_MESSAGE_GET_DATA,
    payload: { data: [], type: "empty" },
  });
};

export const setMediasListChatInfo = (payload) => (dispatch) => {
  dispatch({
    type: SET_MEDIAS_LIST,
    payload,
  });
};

export const setDocumentsListChatInfo = (payload) => (dispatch) => {
  dispatch({
    type: SET_DOCUMENTS_LIST,
    payload,
  });
};
export const setDocumentsFiltredListChatInfo = (payload) => (dispatch) => {
  dispatch({
    type: SET_DOCUMENTS_FILTRED_LIST,
    payload,
  });
};

export const setLinksListChatInfo = (payload) => (dispatch) => {
  dispatch({
    type: SET_LINKS_LIST,
    payload,
  });
};
export const setLinksFiltredListChatInfo = (payload) => (dispatch) => {
  dispatch({
    type: SET_LINKS_FILTRED_LIST,
    payload,
  });
};

export const setDisableConnexion = (payload) => (dispatch) => {
  dispatch({
    type: "SET_DISABLE_CONNEXION",
    payload,
  });
};
export const setOpenDrawer = (payload) => (dispatch) => {
  dispatch({
    type: SET_OPEN_DRAWER,
    payload,
  });
};
export const setOpenSideBarDrawer = (payload) => (dispatch) => {
  dispatch({
    type: SET_OPEN_SIDE_BAR_DRAWER,
    payload,
  });
};

export const setAssetsCount = (payload) => (dispatch) => {
  dispatch({
    type: SET_ASSETS_COUNT,
    payload,
  });
};

export const addStarredMessage = (payload) => (dispatch) => {
  dispatch({
    type: ADD_STARRED_MESSAGE,
    payload,
  });
};
export const removeStarredMessage = (payload) => (dispatch) => {
  dispatch({
    type: REMOVE_STARRED_MESSAGE,
    payload,
  });
};

export const addSavedMessage = (payload) => (dispatch) => {
  dispatch({
    type: ADD_SAVED_MESSAGE,
    payload,
  });
};
export const removeSavedMessage = (payload) => (dispatch) => {
  dispatch({
    type: REMOVE_SAVED_MESSAGE,
    payload,
  });
};

export const addNotification = (payload) => (dispatch) => {
  dispatch({
    type: ADD_NOTIFICATION_NUMBER,
    payload,
  });
};
export const substractNotification = (payload) => (dispatch) => {
  dispatch({
    type: SUBSTRACT_NOTIFICATION_NUMBER,
    payload,
  });
};

export const setImageToList = (payload) => (dispatch) => {
  dispatch({
    type: SET_IMAGE_TO_LIST,
    payload,
  });
};

export const setDocumentToList = (payload) => (dispatch) => {
  dispatch({
    type: SET_DOCUMENT_TO_LIST,
    payload,
  });
};
export const addLinksToList = (payload) => (dispatch) => {
  dispatch({
    type: ADD_LINKS_TO_LIST,
    payload,
  });
};

export const setLinksUpdateOrRemoveList = (payload) => (dispatch) => {
  dispatch({
    type: SET_LINKS_UPDATE_REMOVE_LIST,
    payload,
  });
};

export const setPinnedList = (payload) => (dispatch) => {
  dispatch({
    type: SET_PINNED_LIST,
    payload,
  });
};

export const setStarredList = (payload) => (dispatch) => {
  dispatch({
    type: SET_STARRED_LIST,
    payload,
  });
};

export const setSearchList = (payload) => (dispatch) => {
  dispatch({
    type: SET_SEARCH_LIST,
    payload,
  });
};

export const setAssetsToList = (payload) => (dispatch) => {
  dispatch({
    type: SET_ASSETS_TO_LIST,
    payload,
  });
};

export const setPinnedIsFetched = (payload) => (dispatch) => {
  dispatch({
    type: SET_PINNED_FETCHED,
    payload,
  });
};

export const setStarredIsFetched = (payload) => (dispatch) => {
  dispatch({
    type: SET_STARRED_FETCHED,
    payload,
  });
};

export const setThreadList = (payload) => (dispatch) => {
  dispatch({
    type: SET_THREAD_LIST,
    payload,
  });
};

export const addThreadToList = (payload) => (dispatch) => {
  dispatch({
    type: ADD_THREAD_TO_LIST,
    payload,
  });
};
export const updateThreadToList = (payload) => (dispatch) => {
  dispatch({
    type: UPDATE_THREAD_TO_LIST,
    payload,
  });
};

export const setOpenQuitGroupModal = (payload) => (dispatch) => {
  dispatch({
    type: SET_MODAL_QUIT_GROUP,
    payload,
  });
};

export const setSidebarDrawer = (payload) => (dispatch) => {
  dispatch({
    type: SET_SIDEBAR_DRAWER,
    payload,
  });
};

export const setArchivedListChatFetched = (payload) => (dispatch) => {
  dispatch({
    type: SET_ARCHIVED_LIST_FETCHED,
    payload,
  });
};

export const setMembersGroupsChatFetched = (payload) => (dispatch) => {
  dispatch({
    type: SET_CHAT_MEMBERS_GROUPS_FETCHED,
    payload,
  });
};
export const addChatUserToList = (payload) => (dispatch) => {
  dispatch({
    type: ADD_CHAT_USER_LIST,
    payload,
  });
};
export const removeChatUserToList = (payload) => (dispatch) => {
  dispatch({
    type: REMOVE_CHAT_USER_LIST,
    payload,
  });
};
export const updateChatUserToList = (payload) => (dispatch) => {
  dispatch({
    type: UPDATE_CHAT_USER_LIST,
    payload,
  });
};

export const updateCurrentUserStatusBlocked = (payload) => (dispatch) => {
  dispatch({
    type: UPDATE_CURRENT_USER_STATUS_BLOCKED,
    payload,
  });
};
export const addToMuteList = (payload) => (dispatch) => {
  dispatch({
    type: ADD_TO_MUTE_LIST,
    payload,
  });
};
export const addUnreadMessageArchived = (payload) => (dispatch) => {
  dispatch({
    type: ADD_UNREAD_MESSAGE_ARCHIVED,
    payload,
  });
};
export const removeFromMuteList = (payload) => (dispatch) => {
  dispatch({
    type: REMOVE_FROM_MUTE_LIST,
    payload,
  });
};

export const getCurrentItem = (item) => {
  if (item?.room) return item.room;
  else if (item?.contact) return item.contact;
  else if (item?.bot) return item.bot;
  else return null;
};
export const resetStateOtherUser =
  (
    payload = {
      keepDrawerOpened: false,
      forced: false,
      item: null,
    }
  ) =>
  async (dispatch) => {
    try {
      dispatch(stopSearchMsg(payload.forced));
      dispatch({
        type: RESET_STATE_OTHER_USER,
        payload: payload.keepDrawerOpened,
      });

      if (payload.item) {
        const membersGroupsChat = await store.getState().chat.membersGroupsChat;
        const archivedList = await store.getState().chat.archivedList;
        const archivedListIds = await store.getState().chat.archivedListIds;
        const sidebarDrawer = await store.getState().chat.sidebarDrawer;

        const isArchivedDiscussion = archivedListIds.find(
          (item) =>
            item[payload?.type === "room" ? "room_id" : "receiver_id"] ===
            payload.item?._id
        );
        if (isArchivedDiscussion && sidebarDrawer === "chat") {
          dispatch(setSidebarDrawer("archive"));
        } else if (!isArchivedDiscussion && sidebarDrawer === "archive") {
          dispatch(setSidebarDrawer("chat"));
        }
        // isCurrentItem in case all the object of the conversation is passed in parasm
        let conversation = null;
        if (payload.item?.isCurrentItem) {
          conversation = {
            ...payload.item,
            _id: payload.item?._idConversation,
          };
        } else {
          conversation = (
            isArchivedDiscussion ? [...archivedList] : [...membersGroupsChat]
          )?.find(
            (el) =>
              (payload.item?.type === "room" ? el.room : el.contact)?._id ===
              payload.item?._id
          );
        }
        let currentItem = null;
        if (conversation) {
          currentItem = getCurrentItem(conversation);
        } else if (payload.item?.type === "user") {
          const userList = await store.getState().chat.userList;
          currentItem = userList.find(
            (el) =>
              (payload.item?._id && el._id === payload.item?._id) ||
              (payload.item?.post_number &&
                el?.post_number === payload.item?.post_number)
          );
        } else {
          return;
        }

        if (conversation?.total_unread > 0)
          dispatch(
            setNumberUnreadMsg({
              id: currentItem?._id,
              number: conversation?.total_unread,
            })
          );

        dispatch(
          setChatSelectedConversation({
            selectedConversation: {
              email: currentItem?.email ?? null,
              name: currentItem?.name,
              description: currentItem?.description ?? null,
              image: currentItem?.image,
              admin_id: currentItem?.admin_id ?? null,
              bot: currentItem?.bot ?? null,
              id: currentItem?._id,
              uuid: currentItem?.uuid ?? null,
              post_number: currentItem?.post_number ?? null,
              role: currentItem?.role ?? null,
              type: conversation?.room ? "room" : "user",
              source: payload.item?.source ?? "chat",
              muted_status: conversation?.muted_status,
              conversationId: conversation?._id,
              external: false,
            },
          })
        );
        if (payload.item.participants && payload.item.type === "user") {
          const currentUser = await store.getState().chat.currentUser;

          dispatch(
            setChatSelectedParticipants({
              selectedParticipants: [
                {
                  email: currentItem?.email,
                  name: currentItem?.name,
                  image: currentItem?.image,
                  _id: currentItem?._id,
                  post_number: currentItem?.post_number,
                  uuid: currentItem?.uuid,
                  type: "user",
                },
                currentUser,
              ],
            })
          );
        }
      }
    } catch (e) {
      console.log(e);
    }
  };

export const setAllStarredFetched = (payload) => (dispatch) => {
  dispatch({
    type: SET_ALL_STARRED_LIST_FETCHED,
    payload,
  });
};
export const setAllPinnedFetched = (payload) => (dispatch) => {
  dispatch({
    type: SET_ALL_PINNED_LIST_FETCHED,
    payload,
  });
};
export const setNotificationConfigChat = (payload) => (dispatch) => {
  dispatch({
    type: SET_NOTIFICATION_CONFIG_USER,
    payload,
  });
};

export const setDataConversation = (payload) => (dispatch) => {
  dispatch({
    type: SET_DATA_CONVERSATION,
    payload,
  });
};

export const addPollList = (payload) => (dispatch) => {
  dispatch({
    type: ADD_POLL_LIST,
    payload,
  });
};
export const addAllPollList = (payload) => (dispatch) => {
  dispatch({
    type: ADD_ALL_POLL_LIST,
    payload,
  });
};
export const setPollsVote = (payload) => (dispatch) => {
  dispatch({
    type: SET_POLLS_VOTE,
    payload,
  });
};
export const removePollList = (payload) => (dispatch) => {
  dispatch({
    type: REMOVE_POLL_LIST,
    payload,
  });
};

export const removeAllPollList = (payload) => (dispatch) => {
  dispatch({
    type: REMOVE_ALL__POLL_LIST,
    payload,
  });
};

export const updatePollList = (payload) => (dispatch) => {
  dispatch({
    type: UPDATE_POLL_LIST,
    payload,
  });
};

export const updateAllPollList = (payload) => (dispatch) => {
  dispatch({
    type: UPDATE_ALL_POLL_LIST,
    payload,
  });
};

export const handleTypingUser = (payload) => (dispatch) => {
  dispatch({
    type: HANDLE_TYPING_USER,
    payload,
  });
};
