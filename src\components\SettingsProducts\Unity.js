import React, { useEffect, useRef } from "react";
import { Form, Input, Button, Space } from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { PlusCircleOutlined } from "@ant-design/icons";
import { toastNotification } from "../ToastNotification";
import Header from "../configurationHelpDesk/Header";
import { useDispatch, useSelector } from "react-redux";
import { addRowinTableUnity } from "../../new-redux/actions/table.actions/table";
import NewTableDraggable from "../NewTableDraggable";
import { setSearch } from "../../new-redux/actions/menu.actions/menu";
import { generateAxios } from "../../services/axiosInstance";
import LabelTable from "../LabelTable";
import BottomButtonAddRow from "../BottomButtonAddRow";
import { SubmitKeyPress } from "../../utils/SubmitKeyPress";
import { URL_ENV } from "index";

const Unity = () => {
  const [form] = Form.useForm();
  const [count, setCount] = useState(0);
  const [data, setData] = useState([]);
  const [editingKey, setEditingKey] = useState("");
  const [id, setId] = useState(null);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [oldPage, setOldPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const isEditing = (record) => record.key === editingKey;
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const { addRowUnity } = useSelector((state) => state.table);
  const { search } = useSelector((state) => state.form);
  const inputRefs = useRef([]);
  useEffect(() => {
    inputRefs.current.forEach((input) => {
      input?.focus();
    });
  }, [data.length, id]);
  // useEffect(() => {
  //   if (editingKey && oldPage !== currentPage) {
  //     setData((prev) => prev.filter((el) => el.id));
  //     setEditingKey("");
  //   }
  // }, [currentPage, editingKey, oldPage]);
  useEffect(() => {
    if (addRowUnity) {
      const ids = data.map((object) => {
        return object.id;
      });
      setId(null);
      const newData = {
        key: Math.max(...ids) + 1,
        label: `  `,
        color: "",
      };

      setData([...data, newData]);
      form.setFieldsValue({
        label: "",
        color: "",
      });
      setEditingKey(Math.max(...ids) + 1);
      setCount(Math.max(...ids) + 1);
    }
  }, [addRowUnity]);
  const onFinishFailed = (values) => {
   // console.log(values);
  };

  const EditableCell = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    const inputNode = (
      <Input
        ref={(el) => (inputRefs.current[index] = el)}
        onKeyPress={handleKeyPress}
        placeholder={t("activities.name")}
      />
    );

    return (
      <td {...restProps}>
        {editing ? (
          <Form.Item
            name={dataIndex.toLowerCase()}
            style={{
              margin: 0,
            }}
            rules={[
              {
                required: true,
                message: `${t("activities.name")} is required!`,
              },
            ]}>
            {inputNode}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };
  const edit = (record) => {
    if (record) {
      form.setFieldsValue({
        label: record.label,
      });
      setId(record.id);
      inputRefs.current[1]?.input.focus();
    } else {
      form.setFieldsValue({
        label: "",
      });
    }
    setEditingKey(record.key);
  };
  const cancel = (record) => {
    setEditingKey("");
    setId(null);
    if (!record.id) {
      setData(data.filter((item) => item.key !== record.key));
    }
    dispatch(addRowinTableUnity(false));
  };
  console.log(data);
  const save = async (key) => {
    setLoading(true);
    if (id) {
      try {
        const row = await form.validateFields();
        const res = await await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post(`/unit-family-products/update/${id}`, row);
        setEditingKey("");
        setData(
          data.map((el) =>
            el.id === res.data.data.id
              ? {
                  ...res.data.data,
                  key: res.data.data.id,
                }
              : el
          )
        );
        form.setFieldsValue({
          label: "",
        });
        setLoading(false);
        toastNotification(
          "success",
          row.label + t("toasts.created"),
          "topRight"
        );
      } catch (errInfo) {
        setLoading(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    } else {
      try {
        const row = await form.validateFields();
        const res = await await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post("/unit-family-products", row);
        setEditingKey("");
        setData([
          ...data.filter((el) => el.id),
          { ...res.data.data, key: res.data.data.id },
        ]);
        form.setFieldsValue({
          label: "",
        });
        setLoading(false);
        toastNotification(
          "success",
          row.label + t("toasts.created"),
          "topRight"
        );
      } catch (errInfo) {
        setLoading(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    }
    dispatch(addRowinTableUnity(false));
  };
  useEffect(() => {
    const getProducts = async () => {
      setLoading(true);
      try {
        const {
          data: { data },
        } = await await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get("/unit-family-products");
        setData(data.map((el, i) => ({ ...el, key: el.id, rank: i + 1 })));
        if (data.length > 0) {
          setCount(Math.max(...data.map((el) => el.id)));
        }
        setLoading(false);
      } catch (err) {
        setLoading(false);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    };
    if (!search && data.length == 0) getProducts();
  }, [search]);

  useEffect(() => {
    return () => dispatch(setSearch(""));
  }, []);

  const handleKeyPress = (event) => {
    SubmitKeyPress(event, form);
  };
  const handleClick = (event) => {
    event.stopPropagation();
  };
  const columns = [
    {
      title: t("activities.name"),
      dataIndex: "label",
      key: "label",
      editable: true,
      sorter: (a, b) => a.label.localeCompare(b.label),
      render: (_, record) => {
        return (
          <LabelTable record={record} editingKey={editingKey} edit={edit} />
        );
      },
    },
  ];
  const mergedColumns = columns.map((col, index) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record) => ({
        record,
        inputType: col.dataIndex === "input",
        dataIndex: col.dataIndex,
        title: col.title,
        editing: isEditing(record),
        index,
      }),
    };
  });
  const handleAdd = () => {
    const ids = data.map((object) => {
      return object.id;
    });
    setId(null);
    const newData = {
      key: Math.max(...ids) + 1,
      label: `  `,
      disabled: true,
    };
    setCurrentPage(Math.ceil([...data, newData].length / pageSize));
    setOldPage(Math.ceil([...data, newData].length / pageSize));
    setData([...data, newData]);
    form.setFieldsValue({
      label: "",
    });
    setEditingKey(Math.max(...ids) + 1);
    setCount(Math.max(...ids) + 1);
  };
  const onRow = () => {};
  const filteredData = data.filter((item) => {
    return item.label?.toLowerCase().includes(search.toLowerCase());
  });
  return (
    <Space direction="vertical" style={{ width: "100%", marginTop: "16px" }}>
      <div className="pt-4">
        <Header
          active={"4"}
          editingKey={editingKey}
          handleAdd={handleAdd}
          btnText={t("familyProduct.addUnity")}
          disabled={loading ? true : editingKey ? true : search ? true : false}
        />
      </div>
      <NewTableDraggable
        columns={columns}
        setLoading={setLoading}
        isEditing={isEditing}
        data={filteredData}
        setData={setData}
        loading={loading}
        save={save}
        edit={edit}
        EditableCell={EditableCell}
        onFinishFailed={onFinishFailed}
        cancel={cancel}
        form={form}
        apiRank="/rank-unit-family"
        editingKey={editingKey}
        api="unit-family-products"
        onRow={onRow}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        pageSize={pageSize}
        setPageSize={setPageSize}
      />
      <BottomButtonAddRow
        editingKey={editingKey}
        data={filteredData}
        text={t("familyProduct.addUnity")}
        handleAdd={handleAdd}
        loading={loading}
        search={search || ""}
      />
    </Space>
  );
};
export default Unity;
