import React, { useState } from "react";
import {
  Smartphone,
  QrCode,
  Download,
  Play,
  Apple,
  Mail,
  Lock,
  Server,
  RefreshCw,
  CheckCircle,
  ArrowRight,
} from "lucide-react";
import { Avatar, Button, Image, Input } from "antd";
import headerImage from "../../assets/headerAppMobile.jpg";
import logoComunikUnified from "../../assets/logoComunikUnified.png";
import QrCodeIos from "../../assets/comunik_ios_qr.webp";
import QrCodeAndroid from "../../assets/comunik_android_qr.webp";
import { generateAxios } from "services/axiosInstance";
import { ReloadOutlined } from "@ant-design/icons";
const ElegantMobileInterface = () => {
  const [activeTab, setActiveTab] = useState("login");
  const [selectedStore, setSelectedStore] = useState("google");
  const [qrCode, setQrCode] = useState(null);
  const [loading, setLoading] = useState(false);
  const [qRCodeImage, setQRCodeImage] = useState("");
  const getQrcode = async () => {
    setLoading(true);
    try {
      const response = await generateAxios(
        process.env.REACT_APP_DEFAULT_AUTH_DOMAIN +
          process.env.REACT_APP_SUFFIX_API
      ).get(`/mobile/generate-qr-code`, { responseType: "blob" });
      // Créez une URL locale pour le Blob
      const imageUrl = URL.createObjectURL(response.data);

      setQRCodeImage(imageUrl);
      setLoading(false);
    } catch (e) {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-[calc(100vh-57px)] overflow-auto bg-slate-50 px-4 py-2">
      <div className="mx-auto max-w-6xl">
        {/* Header Section */}
        {/* <div className="mb-12 text-center">
          <div
            className="mb-6 inline-flex h-20 w-20 items-center justify-center rounded-2xl shadow-lg"
            style={{ backgroundColor: "#018681" }}
          >
            <Smartphone className="h-10 w-10 text-white" />
          </div>
          <h1 className="mb-4 text-4xl font-bold text-gray-900">
            Comunik <span style={{ color: "#018681" }}>Unified</span>
          </h1>
          <p className="mx-auto max-w-2xl text-xl leading-relaxed text-gray-600">
            Connectez-vous facilement à votre application mobile avec notre
            interface intuitive et sécurisée
          </p>
        </div> */}
        {/* Main Card */}
        <div className="overflow-hidden rounded-3xl border border-gray-100 bg-white shadow-md">
          {/* Hero Image Section */}
          <div>
            <div
              className="relative h-[250px] overflow-hidden"
              // style={{
              //   background: `linear-gradient(135deg, #018681, #016b67, #014d4a)`,
              // }}
            >
              <div className="absolute inset-0 flex items-center justify-center">
                <Image
                  alt="example"
                  src={headerImage}
                  width={1200}
                  height={250}
                  preview={false}
                  loading="lazy"
                />
              </div>
              {/* Decorative elements */}
              <div className="absolute left-10 top-10 h-20 w-20 rounded-full bg-white/10"></div>
              <div className="absolute bottom-10 right-10 h-32 w-32 rounded-full bg-white/10"></div>
              <div className="absolute right-20 top-1/2 h-16 w-16 rounded-full bg-white/10"></div>
            </div>
            <div className="mt-6 text-center ">
              <div className="flex justify-center gap-x-2">
                <Avatar
                  shape="square"
                  size={40}
                  src={logoComunikUnified}
                  alt=""
                />{" "}
                <h2 className="mb-4 text-4xl font-bold text-gray-900">
                  Comunik <span style={{ color: "#018681" }}>Unified</span>
                </h2>
              </div>
              <p className="mx-auto mt-2  text-lg leading-relaxed text-gray-600">
                Connectez-vous facilement à votre application mobile avec notre
                interface intuitive et sécurisée
              </p>
              {/* <p className=" mt-2">Disponible sur iOS et Android</p> */}
            </div>
          </div>
          {/* Navigation Tabs */}
          <div className="p-8">
            <div className="mb-8 flex rounded-2xl bg-gray-100 p-2">
              <button
                onClick={() => setActiveTab("login")}
                className={`b-0 flex flex-1 cursor-pointer items-center justify-center rounded-xl bg-transparent px-6 py-3 transition-all duration-300 ${
                  activeTab === "login"
                    ? "bg-white font-semibold shadow-md"
                    : "text-gray-600 hover:text-gray-800"
                }`}
                style={
                  activeTab === "login"
                    ? { color: "#018681", border: 0 }
                    : { border: 0 }
                }
              >
                <Mail className="mr-2 h-5 w-5" />
                Connexion Email/Mot de passe
              </button>
              <button
                onClick={() => {
                  setActiveTab("qr");
                  getQrcode();
                }}
                className={`b-0 flex flex-1 cursor-pointer items-center justify-center rounded-xl bg-transparent px-6 py-3 transition-all duration-300 ${
                  activeTab === "qr"
                    ? "bg-white font-semibold shadow-md"
                    : "text-gray-600 hover:text-gray-800"
                }`}
                style={
                  activeTab === "qr"
                    ? { color: "#018681", border: 0 }
                    : { border: 0 }
                }
              >
                <QrCode className="mr-2 h-5 w-5" />
                Connexion par QR Code
              </button>
            </div>

            {/* Login Tab Content */}
            {activeTab === "login" && (
              <div className="space-y-8">
                <div className="grid grid-cols-3 gap-6">
                  {/* Step 1: Download App */}
                  <div
                    className="rounded-2xl border bg-gradient-to-br from-blue-50 to-indigo-50 p-6"
                    // style={{
                    //   background: "linear-gradient(135deg, #f0fdfc, #e6fffa)",
                    //   borderColor: "#b3e0dc",
                    // }}
                  >
                    <div className="mb-4 flex items-center">
                      <div className="mr-3 flex h-8 w-8 items-center justify-center rounded-full bg-blue-600 text-sm font-semibold text-white">
                        1
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900">
                        Télécharger l'application
                      </h3>
                    </div>

                    <div className="mb-4">
                      <div className="flex rounded-xl border border-gray-200 bg-white p-1">
                        <button
                          onClick={() => setSelectedStore("google")}
                          className={`flex flex-1 items-center justify-center rounded-lg  px-3 py-2 transition-all duration-200 ${
                            selectedStore === "google"
                              ? "bg-green-500 text-white shadow-sm"
                              : "bg-white text-gray-600 hover:text-gray-800"
                          }`}
                          style={{ border: 0 }}
                        >
                          <Play className="mr-2 h-4 w-4" />
                          <span className="text-sm font-medium">
                            Google Play
                          </span>
                        </button>
                        <button
                          onClick={() => setSelectedStore("apple")}
                          className={`flex flex-1 items-center justify-center rounded-lg  px-3 py-2 transition-all duration-200 ${
                            selectedStore === "apple"
                              ? "bg-gray-900 text-white shadow-sm"
                              : "bg-white text-gray-600 hover:text-gray-800"
                          }`}
                          style={{ border: 0 }}
                        >
                          <Apple className="mr-2 h-4 w-4" />
                          <span className="text-sm font-medium">App Store</span>
                        </button>
                      </div>
                    </div>

                    <div className="text-center">
                      {selectedStore === "google" ? (
                        <div className="flex items-center justify-center space-x-2">
                          <Image src={QrCodeAndroid} width={110} />
                          <a
                            href={
                              selectedStore === "google"
                                ? "https://play.google.com/store/apps/details?id=db.unified.chat_voip&hl=fr"
                                : "https://apps.apple.com/tn/app/comunik-unified/id6739617673?l=fr-FR"
                            }
                            target="_blank"
                            rel="noreferrer"
                            className="inline-flex items-center rounded-xl border border-gray-200 bg-white p-4 shadow-sm transition-shadow duration-200 hover:shadow-md"
                          >
                            <div className="flex items-center space-x-2">
                              <Play className="h-6 w-6 text-green-500" />
                              <span className="font-medium text-gray-900">
                                Google Play
                              </span>
                            </div>
                          </a>
                        </div>
                      ) : (
                        <div className="flex items-center justify-center space-x-2">
                          <Image src={QrCodeIos} width={110} />
                          <a
                            href={
                              selectedStore === "google"
                                ? "https://play.google.com/store/apps/details?id=db.unified.chat_voip&hl=fr"
                                : "https://apps.apple.com/tn/app/comunik-unified/id6739617673?l=fr-FR"
                            }
                            target="_blank"
                            rel="noreferrer"
                            className="inline-flex items-center rounded-xl border border-gray-200 bg-white p-4 shadow-sm transition-shadow duration-200 hover:shadow-md"
                          >
                            <div className="flex items-center space-x-2">
                              <Apple className="h-6 w-6 text-gray-900" />
                              <span className="font-medium text-gray-900">
                                App Store
                              </span>
                            </div>
                          </a>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Step 2: Enter Tenancy */}
                  <div
                    className="rounded-2xl border bg-gradient-to-br from-blue-50 to-indigo-50 p-6"
                    // style={{
                    //   background: "linear-gradient(135deg, #f0fdfc, #e6fffa)",
                    //   borderColor: "#b3e0dc",
                    // }}
                  >
                    <div className="mb-4 flex items-center">
                      <div className="mr-3 flex h-8 w-8 items-center justify-center rounded-full bg-blue-600 text-sm font-semibold text-white">
                        2
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900">
                        Saisir le domaine
                      </h3>
                    </div>

                    <div className="space-y-3">
                      <label className="mb-2 block text-sm font-medium text-gray-700">
                        <Server className="mr-2 inline h-4 w-4" />
                        Domaine de votre organisation
                      </label>
                      <div className="relative">
                        <Input
                          type="text"
                          className="w-full rounded-xl border border-gray-200 bg-white px-4 py-3 transition-all duration-200 focus:border-transparent focus:ring-2 focus:ring-purple-500"
                          value="your-domain.com"
                          readOnly
                        />
                        <CheckCircle className="absolute right-3 top-1/2 h-5 w-5 -translate-y-1/2 transform text-green-500" />
                      </div>
                    </div>
                  </div>

                  {/* Step 3: Fill Credentials */}
                  <div
                    className="rounded-2xl border bg-gradient-to-br from-blue-50 to-indigo-50 p-6"
                    // style={{
                    //   background: "linear-gradient(135deg, #f0fdfc, #e6fffa)",
                    //   borderColor: "#b3e0dc",
                    // }}
                  >
                    <div className="mb-4 flex items-center">
                      <div className="mr-3 flex h-8 w-8 items-center justify-center rounded-full bg-blue-600 text-sm font-semibold text-white">
                        3
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900">
                        Identifiants de connexion
                      </h3>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <label className="mb-2 block text-sm font-medium text-gray-700">
                          <Mail className="mr-2 inline h-4 w-4" />
                          Adresse email
                        </label>
                        <Input
                          type="email"
                          className="w-full rounded-xl border border-gray-200 bg-white px-4 py-3"
                          value="<EMAIL>"
                          readOnly
                        />
                      </div>
                      <div>
                        <label className="mb-2 block text-sm font-medium text-gray-700">
                          <Lock className="mr-2 inline h-4 w-4" />
                          Mot de passe
                        </label>
                        <Input
                          type="password"
                          className="w-full rounded-xl border border-gray-200 bg-white px-4 py-3"
                          placeholder="Utilisez votre mot de passe habituel"
                          readOnly
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* QR Code Tab Content */}
            {activeTab === "qr" && (
              <div className=" text-center">
                <div className="mx-auto max-w-md">
                  <div className="rounded-2xl border border-indigo-100 bg-gradient-to-br from-indigo-50 to-purple-50 p-2">
                    <div
                      className="rounded-2xl border pb-4"
                      // style={{
                      //   background: "linear-gradient(135deg, #f0fdfc, #e6fffa)",
                      //   borderColor: "#b3e0dc",
                      // }}
                    >
                      <h3 className="mb-4 text-xl font-semibold text-gray-900">
                        Connexion rapide par QR Code
                      </h3>
                      {/* <p className="mb-6 text-gray-600">
                      Scannez ce code avec votre application mobile pour vous
                      connecter instantanément
                    </p> */}

                      <div className="relative  py-2">
                        <div className="inline-block rounded-2xl border border-gray-200 bg-white p-2 shadow-sm">
                          {qRCodeImage && !loading ? (
                            <div className="inline-block rounded-2xl border border-gray-200 bg-white p-2 shadow-sm">
                              <Image
                                width={110}
                                src={qRCodeImage}
                                alt="QR Code"
                              />
                            </div>
                          ) : (
                            <div className="inline-block rounded-2xl border border-gray-200 bg-white p-2 shadow-sm">
                              <QrCode className="mx-auto h-[110px] w-[110px] text-gray-300" />
                            </div>
                          )}

                          {loading && (
                            <div className="absolute inset-0 flex items-center justify-center rounded-2xl ">
                              <RefreshCw
                                className="h-8 w-8 animate-spin"
                                style={{ color: "#018681" }}
                              />
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="mt-3 space-y-3">
                        <Button
                          onClick={getQrcode}
                          disabled={loading}
                          icon={
                            loading || qRCodeImage ? (
                              <ReloadOutlined
                                className={loading && "animate-spin"}
                              />
                            ) : (
                              <QrCode className="mr-2 h-5 w-5" />
                            )
                          }
                          type="primary"
                          // className="flex w-full items-center justify-center rounded-xl px-4 py-2 font-semibold text-white transition-all duration-200 hover:opacity-90 disabled:cursor-not-allowed disabled:opacity-50"
                          // style={{
                          //   background: `linear-gradient(135deg, #018681, #016b67)`,
                          //   border: 0,
                          // }}
                        >
                          {loading ? (
                            <>Génération...</>
                          ) : (
                            <>
                              {qRCodeImage
                                ? "Régénérer le code"
                                : "Générer le QR Code"}
                            </>
                          )}
                        </Button>

                        {qRCodeImage && (
                          <p className="text-sm text-gray-500">
                            Code valide pour 5 minutes
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
        {/* Footer
        <div className="mt-12 text-center">
          <p className="text-gray-500">
            Besoin d'aide ? Contactez notre{" "}
            <a
              href="#"
              className="font-medium hover:opacity-80"
              style={{ color: "#018681" }}
            >
              support technique
            </a>
          </p>
        </div> */}
      </div>
    </div>
  );
};

export default ElegantMobileInterface;
