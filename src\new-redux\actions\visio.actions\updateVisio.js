import { UPDATE_VISIO_SUCCESS, UPDATE_VISIO_ERROR, IS_LOADING_WIKI } from "../../constants";
import MainService from "../../../services/main.service";

import { toastNotification } from "../../../components/ToastNotification";


export const updateVisio = (visioId, info) => async (dispatch) => {
    try {
        dispatch({ type: IS_LOADING_WIKI });
        const response = await MainService.updateVisio(visioId, info);
        dispatch({
            type: UPDATE_VISIO_SUCCESS,
            payload: visioId,
        });
        toastNotification(
            "success",
            "Visio updated successfully!",
            "topRight"
        );
    } catch (error) {
        dispatch({
            type: UPDATE_VISIO_ERROR,
            payload: error,
        });
        toastNotification(
            "error",
            "Something went wrong, please try again!",
            "topRight"
        );
    }
};
