import { useCallback, useState } from "react";
import { CheckCircleOutlined, CopyOutlined } from "@ant-design/icons";
import { Button, message, Select, Tooltip } from "antd";
import { useTranslation } from "react-i18next";

import { URL_ENV } from "index";
import { convertToPlain } from "pages/layouts/chat/utils/ConversationUtils";

const ShareVisio = ({ item, visioLink }) => {
  const [isCopied, setCopied] = useState(false);

  const { t } = useTranslation("common");

  let url = new URL(URL_ENV?.REACT_APP_DOMAIN);
  url.searchParams.set("room_visio_name", visioLink);

  const advancedTextToCopy = useCallback(() => {
    const phoneNumbers = item?.phone_numbers;
    const pinCode = item?.pin;
    return `
          <p>${t("visio.followVisioLinkClipboard")}: <u>${url}</u></p>
          <p>${t("visio.followVisioInPhoneClipboardPartOne")} <strong>${
      phoneNumbers[0]
    }</strong> ${t("visio.followVisioInPhoneClipboardPartTwo")}: <strong>${pinCode}</strong></p>
          ${
            phoneNumbers?.length > 1
              ? `<p>${t("visio.morePhoneClipboard")}: ${phoneNumbers?.map(
                  (number, index) => `<span key={phoneList-${index}}> ${number}</span>`
                )}</p>`
              : ""
          }
        `;
  }, [t]);
  const handleCopy = useCallback(async () => {
    if (isCopied) return;
    const copiedText = advancedTextToCopy();
    const blobHtml = new Blob([copiedText], { type: "text/html" });
    const blobText = new Blob([convertToPlain(copiedText)], {
      type: "text/plain",
    });
    const data = [
      new ClipboardItem({
        "text/plain": blobText,
        "text/html": blobHtml,
      }),
    ];

    navigator.clipboard.write(data).then(() => {
      setCopied(true);
      message.success(t("chat.bot.copied"));
    });
    setTimeout(() => setCopied(false), 3000);
  }, [isCopied, t]);
  return (
    <>
      <p>{t("visio.visioInfoText")}</p>
      <div className="mr-2 mt-3 flex w-full items-center justify-between rounded-md border border-zinc-600 bg-zinc-200 px-2 py-2">
        <div className="pl-2">
          <label>{t("visio.followVisioLinkClipboard")}: </label>
          <a target="_blank" href={url} rel="noreferrer">
            {`${URL_ENV?.REACT_APP_DOMAIN}/?room_visio_name=${visioLink}`}
          </a>
          <section className="flex flex-row items-center">
            <label>{t("visio.followVisioInPhoneClipboardPartOne")}: </label>{" "}
            <Select
              className="phones-select"
              defaultValue={item?.phone_numbers[0]}
              bordered={false}
              popupMatchSelectWidth={false}
              options={item?.phone_numbers?.map((phone) => ({
                label: phone,
                value: phone,
              }))}
            />
          </section>
          <section className="flex flex-row items-center">
            <label>{t("visio.followVisioInPhoneClipboardPartTwo")}: </label>{" "}
            <p className="ml-1 text-gray-500">{item?.pin}</p>
          </section>
        </div>

        <Tooltip title={!isCopied ? t("chat.bot.copy") : t("chat.bot.copied")}>
          <Button
            shape="circle"
            type="text"
            className=" text-gray-500/70 hover:text-gray-500/90"
            onClick={handleCopy}
            icon={
              !isCopied ? (
                <CopyOutlined className=" scale-110" />
              ) : (
                <CheckCircleOutlined className=" scale-110" />
              )
            }
          />
        </Tooltip>
      </div>
    </>
  );
};

export default ShareVisio;
