import { memo, useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import {
  Badge,
  Button,
  DatePicker,
  Form,
  Popover,
  Radio,
  Select,
  Space,
  Switch,
  Tooltip,
} from "antd";
import { useWatch } from "antd/es/form/Form";
import { RouteOff, SlidersHorizontal } from "lucide-react";
import {
  formatDatePickerRange,
  rangePresets,
} from "pages/voip/helpers/helpersFunc";
import { MdRadioButtonChecked } from "react-icons/md";
import { toastNotification } from "components/ToastNotification";
import dayjs from "dayjs";
import useFamiliesOption from "../components/families";
import MainService from "services/main.service";
import ChoiceIcons from "pages/components/ChoiceIcons";
import { affectations } from "pages/voip/services/services";
import { debounce } from "lodash";
import { tagRender } from "./email-composer-modal/utils";

const { Option } = Select;

const stateToColorMap = {
  new: "rgb(107 114 128)",
  "in-progress": "#49a7e9",
  processed: "#a149e9",
  closed: "#3cca5d",
};

const statusOptions = (t) => [
  {
    text: t("mailing.new"),
    value: "new",
    label: (
      <div className="flex items-center space-x-2">
        <MdRadioButtonChecked
          style={{
            fontSize: 14,
            color: stateToColorMap.new,
          }}
        />
        <span>{t("mailing.new")}</span>
      </div>
    ),
  },
  {
    text: t("mailing.inProgress"),
    value: "in-progress",
    label: (
      <div className="flex items-center space-x-2">
        <MdRadioButtonChecked
          style={{
            fontSize: 14,
            color: stateToColorMap["in-progress"],
          }}
        />
        <span>{t("mailing.inProgress")}</span>
      </div>
    ),
  },
  {
    text: t("mailing.processed"),
    value: "processed",
    label: (
      <div className="flex items-center space-x-2">
        <MdRadioButtonChecked
          style={{
            fontSize: 14,
            color: stateToColorMap.processed,
          }}
        />
        <span>{t("mailing.processed")}</span>
      </div>
    ),
  },
  {
    text: t("mailing.closed"),
    value: "closed",
    label: (
      <div className="flex items-center space-x-2">
        <MdRadioButtonChecked
          style={{
            fontSize: 14,
            color: stateToColorMap.closed,
          }}
        />
        <span>{t("mailing.closed")}</span>
      </div>
    ),
  },
];

const families = (t) => ({
  1: t("contacts.company"),
  2: t("contacts.contact"),
  3: t("import.deal"),
  5: t("contacts.product"),
  6: t("contacts.ticket"),
  7: t("contacts.project"),
  8: t("contacts.booking"),
  9: t("contacts.leads"),
});

const handleApiError = (err, t) => {
  if (err.name !== "CanceledError" && err?.response?.status !== 401) {
    toastNotification("error", t("toasts.somethingWrong"), "topRight");
  }
};

const FilterMail = ({ filter, setFilter, usedAccount, isSharedAccount }) => {
  //
  const [t] = useTranslation("common");
  const [form] = Form.useForm();
  //
  const familiesOptions = useFamiliesOption();
  const user = useSelector((state) => state.user.user);
  //
  const affectationFamilyId = useWatch(["affectation_familyId"], form);
  //
  const [qualifyOptions, setQualifyOptions] = useState([]);
  const [showTime, setShowTime] = useState(null);
  const [affectationOptions, setAffectationOptions] = useState([]);
  const [emailOptions, setEmailOptions] = useState([]);

  //
  const fetchQualificationOptions = useCallback(async () => {
    try {
      const { data } = await MainService.getTags();
      setQualifyOptions(
        data?.map((item) => ({
          ...item,
          value: item.label,
        }))
      );
    } catch (err) {
      handleApiError(err, t);
    }
  }, [t]);

  useEffect(() => {
    fetchQualificationOptions();
  }, [fetchQualificationOptions]);
  //
  const handleSwitchChange = (checked) => {
    if (checked) {
      setShowTime({
        format: "HH:mm",
        defaultValue: [dayjs("00:00", "HH:mm"), dayjs("23:59", "HH:mm")],
      });
    } else {
      setShowTime(null);
    }
  };
  //
  const fetchAffectationOptions = useCallback(
    async (search, familyId) => {
      if (!affectationFamilyId && !familyId) return;
      try {
        const {
          data: { data },
        } = await affectations(
          familyId || affectationFamilyId,
          user?.id,
          search ?? ""
        );
        const fetchedOptions = data?.map((e) => ({
          label: `${e.label}${e.reference ? ` - ${e.reference}` : ""}`,
          value: `${familyId || affectationFamilyId}//${e.label}//${e._id}`,
          key: e._id,
        }));
        setAffectationOptions(fetchedOptions);
      } catch (err) {
        handleApiError(err, t);
      }
    },
    [affectationFamilyId, t, user?.id]
  );

  useEffect(() => {
    fetchAffectationOptions();
  }, [fetchAffectationOptions]);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const handleSearchAffectation = useCallback(
    debounce((search, familyId) => {
      fetchAffectationOptions(search, familyId);
    }, 300),
    []
  );
  //
  const handleChangedFields = (changedFields, allFields) => {
    const changedField = changedFields?.[0] ?? {};
    const fieldName = changedField.name?.[0];
    const fieldValue = changedField.value;
    if (fieldName === "affectation_familyId") {
      form.setFieldValue("affectation", undefined);
      return;
    } else {
      const newFilter = { ...filter };
      if (fieldName === "assigned" && fieldValue === 4) {
        delete newFilter[fieldName];
      } else if (fieldName === "date") {
        const dateFormat = "DD/MM/YYYY";
        const timeFormat = "HH:mm";
        newFilter[fieldName] = [
          dayjs(fieldValue[0]).format(`${dateFormat} ${timeFormat}`),
          dayjs(fieldValue[1]).format(`${dateFormat} ${timeFormat}`),
        ];
      } else if (fieldValue) {
        if (Array.isArray(fieldValue) && !fieldValue.length) {
          delete newFilter[fieldName];
        } else {
          newFilter[fieldName] = fieldValue;
        }
      } else {
        delete newFilter[fieldName];
      }
      setFilter(newFilter);
    }
    // console.log({ changedFields, allFields });
  };
  //
  const handleResetFilter = () => {
    setFilter({});
    form.resetFields();
    isSharedAccount &&
      form.setFieldsValue({
        assigned: 4,
        chrono_filter: 0,
      });
  };
  //
  const fetchReceiverOptions = async (search) => {
    if (!search || !search?.length) {
      setEmailOptions([]);
      return;
    }
    try {
      const formData = new FormData();
      formData.append("search", search);
      formData.append("accountId", usedAccount?.value);
      const {
        data: { address },
      } = await MainService.searchEmail(formData);
      if (address.length)
        setEmailOptions(
          address.map((item) => ({
            label: item,
            value: item,
          }))
        );
      else if (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(search))
        setEmailOptions([
          {
            label: search,
            value: search,
          },
        ]);
      else setEmailOptions([]);
    } catch (err) {
      toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? `error: ${err.message}` : { err });
    } finally {
    }
  };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const handleSearchReceiverOptions = useCallback(
    debounce((value) => {
      fetchReceiverOptions(value);
    }, 300),
    []
  );
  //
  // initialize the filter if exist
  // console.log({ filter });
  useEffect(() => {
    form.resetFields();
    form.resetFields();
    isSharedAccount &&
      form.setFieldsValue({
        assigned: 4,
        chrono_filter: 0,
      });
    if (!Object.keys(filter)?.length) return;
    const newForm = {};
    Object.entries(filter).forEach(([key, value]) => {
      if (key === "affectation" && value) {
        const [familyId, label, id] = value.split("//");
        setAffectationOptions([
          {
            label: label,
            value: value,
            key: id,
          },
        ]);
        newForm["affectation_familyId"] = Number(familyId);
        newForm[key] = value;
      } else if (key === "date") {
        const dateFormat = user?.location?.date_format || "DD/MM/YYYY";
        const timeFormat = user?.location?.time_format || "HH:mm";
        const start = dayjs(value[0], `${dateFormat} ${timeFormat}`);
        const end = dayjs(value[1], `${dateFormat} ${timeFormat}`);
        newForm[key] = [start, end];
      } else {
        newForm[key] = value;
      }
    });
    form.setFieldsValue(newForm);
  }, [filter]);
  //
  const popoverContent = (
    <>
      <Form layout="vertical" form={form} onFieldsChange={handleChangedFields}>
        <Form.Item label={t("mailing.NewMsg.from")} name="sender_address">
          <Select
            key="senders"
            showSearch
            allowClear
            mode="multiple"
            notFoundContent=""
            maxTagCount="responsive"
            style={{ width: "100%" }}
            options={emailOptions}
            onChange={(value) => {
              setEmailOptions([]);
              form.setFieldValue("sender_address", [...value]);
            }}
            onSearch={handleSearchReceiverOptions}
            tagRender={tagRender}
            maxTagPlaceholder={(omittedValues) => (
              <Tooltip
                styles={{ root: { pointerEvents: "none" } }}
                title={omittedValues.map(({ label }) => label).join(", ")}
              >
                <span className="cursor-help text-sm">{`+ ${omittedValues.length} ...`}</span>
              </Tooltip>
            )}
          />
        </Form.Item>

        <Form.Item label={t("mailing.NewMsg.To")} name="recipient_address">
          <Select
            key="receivers"
            showSearch
            allowClear
            mode="multiple"
            notFoundContent=""
            maxTagCount="responsive"
            style={{ width: "100%" }}
            options={emailOptions}
            onChange={(value) => {
              setEmailOptions([]);
              form.setFieldValue("recipient_address", [...value]);
            }}
            onSearch={handleSearchReceiverOptions}
            tagRender={tagRender}
            maxTagPlaceholder={(omittedValues) => (
              <Tooltip
                styles={{ root: { pointerEvents: "none" } }}
                title={omittedValues.map(({ label }) => label).join(", ")}
              >
                <span className="cursor-help text-sm">{`+ ${omittedValues.length} ...`}</span>
              </Tooltip>
            )}
          />
        </Form.Item>

        <Form.Item label={t("mailing.dateRange")} name="date">
          <DatePicker.RangePicker
            allowClear
            placement="bottom"
            presets={rangePresets(t)}
            format={formatDatePickerRange(showTime, user?.location)}
            showTime={showTime}
            renderExtraFooter={() => (
              <div className="ml-2">
                <span>{t("voip.displayTime")}: </span>
                <Switch size="small" onChange={handleSwitchChange} />
              </div>
            )}
            style={{ width: "100%" }}
          />
        </Form.Item>
        <Form.Item label="Affectation">
          <Space.Compact block>
            <Form.Item noStyle name="affectation_familyId">
              <Select
                allowClear
                style={{ width: "80%" }}
                placeholder={t("voip.selectModule")}
                options={familiesOptions}
              />
            </Form.Item>
            <Form.Item noStyle shouldUpdate>
              {({ getFieldValue }) => {
                const affectationFamilyId = getFieldValue(
                  "affectation_familyId"
                );
                return (
                  <Form.Item noStyle name="affectation">
                    <Select
                      allowClear
                      showSearch
                      filterOption={false}
                      style={{ width: "100%" }}
                      placeholder={
                        affectationFamilyId
                          ? `${t("voip.search_select")} ${
                              families(t)?.[affectationFamilyId]
                            }`
                          : t("voip.selectModule")
                      }
                      options={affectationOptions}
                      disabled={!affectationFamilyId}
                      onSearch={(search) =>
                        handleSearchAffectation(search, affectationFamilyId)
                      }
                    />
                  </Form.Item>
                );
              }}
            </Form.Item>
          </Space.Compact>
        </Form.Item>
        <Form.Item label="Qualification" name="qualification">
          <Select
            allowClear
            mode="multiple"
            style={{ width: "100%" }}
            placeholder={t("voip.selectModule")}
          >
            {qualifyOptions?.map((option) => (
              <Option
                key={option?.id}
                value={option?.value}
                label={option?.label}
                color={option?.color}
                icon={option?.icon}
              >
                <div className="flex items-center space-x-2">
                  <ChoiceIcons icon={option?.icon} />
                  <span style={{ color: option?.color }}>{option.label}</span>
                </div>
              </Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item label={t("helpDesk.status")} name="state">
          <Select
            allowClear
            mode="multiple"
            optionFilterProp="text"
            options={statusOptions(t)}
            style={{ width: "100%" }}
            placeholder="Sélectionner statut"
          />
        </Form.Item>
        {isSharedAccount && (
          <>
            <Form.Item label={t("mailing.Assign")} name="assigned">
              <Radio.Group buttonStyle="solid">
                <Radio.Button value={4}>{t("mailing.Default")}</Radio.Button>
                <Radio.Button value={1}>{t("mailing.all")}</Radio.Button>
                <Radio.Button value={2}>{t("mailing.MyMode")}</Radio.Button>
                <Radio.Button value={5}>{t("tasks.aucun")}</Radio.Button>
                {/* <Radio.Button value={3}>{t("mailing.RelativeTo")}</Radio.Button> */}
              </Radio.Group>
            </Form.Item>
            <Form.Item label="Chrono" name="chrono_filter">
              <Radio.Group buttonStyle="solid">
                <Radio.Button value={0}>{t("voip.no")}</Radio.Button>
                <Radio.Button value={1}>{t("voip.yes")}</Radio.Button>
                <Radio.Button value={2}>
                  {t("mailing.outOfDeadline")}
                </Radio.Button>
              </Radio.Group>
            </Form.Item>
          </>
        )}
      </Form>
      <div className="mt-6 flex justify-end">
        <Button
          danger
          disabled={!Object.keys(filter)?.length}
          onClick={handleResetFilter}
          icon={<RouteOff size={14} />}
        >
          {t("mailing.filterReset")}
        </Button>
      </div>
    </>
  );
  //
  return (
    <Popover
      arrow={false}
      placement="bottom"
      trigger={["click"]}
      title={t("mailing.filterTitle")}
      content={popoverContent}
      overlayStyle={{ width: "39rem" }}
    >
      <Badge dot={!!Object.keys(filter)?.length}>
        <Button /*type="primary"*/ icon={<SlidersHorizontal size={18} />} />
      </Badge>
    </Popover>
  );
};

export default memo(FilterMail);
