import { But<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Popconfirm, Popover, Select } from "antd";
import React, { useRef } from "react";
import { useTranslation } from "react-i18next";
import { useEffect } from "react";
import { useSelector } from "react-redux";
import { getElementByFamily } from "pages/clients&users/services/services";
import FormCreate from "pages/clients&users/components/FormCreate";
import { CloseOutlined } from "@ant-design/icons";
import { generateAxios } from "services/axiosInstance";
import { URL_ENV } from "index";
import { getName } from "pages/layouts/chat/utils/ConversationUtils";
import { AvatarChat } from "components/Chat";
import { object } from "prop-types";
import { familyIcons } from "pages/components/DetailsProfile/ViewSphere2";

const PopOverSelectModule = ({
  onFinish,
  action = "",
  ButtonPopOver,
  width = 300,
  form,
  loadingSubmit,
  setOpenPopOver,
  openPopOver,
  module = {},
  relations = [],
  contactInfo,
  openPopConfirm,
  setOpenPopConfirm = () => {},
}) => {
  const [t] = useTranslation("common");
  const [selectedModule, setSelectedModule] = React.useState({});
  const [searchSelect, setSearchSelect] = React.useState("");
  const [selectedItem, setSelectedItem] = React.useState("");

  const [load, setLoad] = React.useState(false);

  // const [openPopConfirm, setOpenPopConfirm] = React.useState(false);
  const [openFormCreate, setOpenFormCreate] = React.useState(false);
  const [existModules, setExistModules] = React.useState([
    1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12,
  ]);
  const [disabled, setDisabled] = React.useState(false);
  const [open, setOpen] = React.useState(false);
  const [dataModule, setDataModule] = React.useState([]);
  const { families } = useSelector((state) => state?.families);
  useEffect(() => {
    if (module?.family_id && openPopOver) {
      setExistModules(
        families
          // .filter((el) => el.type_relation === 0)
          .map((el) => el.id)
      );
      setSelectedModule(
        familyIcons(t).find((el) => el.value === module?.family_id)
      );
      form.setFieldsValue({ module: module?.family_id });
    } else if (!module?.family_id && openPopOver) {
      setDisabled(true);

      const getFamilies = async () => {
        try {
          const res = await generateAxios(
            URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
          ).get(`get-relation-family/${contactInfo?.family_id}`);
          const convertedData = Object.entries(res.data).map(([key, label]) => {
            return Number(key);
          });
          setExistModules(convertedData);
          // setExistModules(
          //   relations
          //     .filter((el) => el.type_relation === 0)
          //     .map((el) => el.family_id)
          // );
          setDisabled(false);
        } catch (err) {
          console.log(err);
        }
      };
      getFamilies();
    }
  }, [module.family_id, openPopOver, contactInfo?.family_id]);
  useEffect(() => {
    const getData = async () => {
      setLoad(true);
      try {
        const res = await getElementByFamily(
          selectedModule?.key,
          contactInfo?.id,
          ""
        );

        setDataModule(
          res.data.data.map((el) => ({
            value: el.id,
            label2: (
              <span>
                {" "}
                {selectedModule?.key == "1" ||
                selectedModule?.key == "2" ||
                selectedModule?.key == "4" ||
                selectedModule?.key == "9" ? (
                  <AvatarChat
                    // fontSize="0.875rem"
                    url={
                      URL_ENV?.REACT_APP_BASE_URL +
                      URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                      el.avatar
                    }
                    type="user"
                    size={15}
                    height={10}
                    width={10}
                    name={el.avatar}
                    hasImage={
                      el.avatar.length > 1 && el.avatar !== "/storage/uploads/"
                    }
                  />
                ) : null}{" "}
                {el.label}{" "}
              </span>
            ),
            label3: el.label,
            label: el.label,
            avatar: el.avatar,
            disabled: el.is_used,
          }))
        );
        setLoad(false);
        setOpen(true);
      } catch (err) {
        setLoad(false);
        console.log(err);
      }
    };
    if (selectedModule.key && contactInfo.id) getData();
  }, [selectedModule.key, contactInfo.id]);
  useEffect(() => {
    return () => {
      if (openPopOver) {
        form.setFieldsValue({ searchSelect: null, module: null });
        setSelectedModule({});
        setDataModule([]);
      }
    };
  }, [openPopOver]);
  const onChangeModule = (value, values) => {
    if (!value) {
      setSelectedModule({});
      setDataModule([]);
      form.setFieldsValue({ searchSelect: null });
      setOpen(false);
    } else {
      form.setFieldsValue({ searchSelect: null });
      setSelectedModule(values);
    }
  };
  const onChangePopOverOpen = (open) => {
    if (openFormCreate) {
      setOpenPopOver(true);
    } else if (open) {
      setOpenPopOver(open);
    } else if (!open) {
      setOpen(false);
      setOpenPopOver(open);
      form.setFieldsValue({ searchSelect: null, module: null });
      setSelectedModule({});
      setDataModule([]);
    }
  };

  const content = (
    <div onClick={(e) => e.stopPropagation()}>
      <Form
        form={form}
        layout="vertical"
        name="form"
        onFinish={onFinish}
        onValuesChange={(value, all) => setSearchSelect(all.searchSelect)}
      >
        <Form.Item
          label={t("voip.selectModule")}
          name="module"
          rules={[
            {
              required: true,
              message: "",
            },
          ]}
        >
          <Select
            style={{ width: "100%" }}
            allowClear
            options={familyIcons(t)
              .filter((el) => existModules.includes(el.value))
              .map((el) => ({
                ...el,
                value: Number(el.key),
                label: (
                  <div>
                    {el.icon} {el.label}
                  </div>
                ),
              }))}
            onChange={onChangeModule}
            placeholder={t("voip.selectModule")}
            disabled={module?.family_id || disabled}
          />
        </Form.Item>

        <Form.Item
          label={`${t("voip.search_select")} ${
            selectedModule?.key &&
            Array.isArray(selectedModule?.label?.props?.children)
              ? selectedModule?.label?.props?.children[2]
              : typeof selectedModule?.label === "string"
              ? selectedModule?.label
              : ""
          }`}
          rules={[
            {
              required: selectedModule?.key ? true : false,
              message: "",
            },
          ]}
          name="searchSelect"
        >
          <Select
            defaultActiveFirstOption
            disabled={!selectedModule?.key}
            optionFilterProp={["label", "searchOption"]}
            optionLabelProp="label2"
            showSearch={load ? false : true}
            loading={load}
            open={load ? false : open}
            onChange={(e, t) => setSelectedItem(t.label3)}
            onDropdownVisibleChange={(e) => {
              if (!load) setOpen(e);
            }}
            options={dataModule.map((el) => ({
              ...el,
              label: (
                <span>
                  {" "}
                  {selectedModule?.key == "1" ||
                  selectedModule?.key == "2" ||
                  selectedModule?.key == "4" ||
                  selectedModule?.key == "9" ? (
                    <AvatarChat
                      // fontSize="0.875rem"
                      url={
                        URL_ENV?.REACT_APP_BASE_URL +
                        URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                        el.avatar
                      }
                      type="user"
                      size={15}
                      height={10}
                      width={10}
                      name={el.avatar}
                      hasImage={
                        el.avatar.length > 1 &&
                        el.avatar !== "/storage/uploads/"
                      }
                    />
                  ) : null}{" "}
                  {el.label}{" "}
                </span>
              ),
            }))}
            filterOption={(input, option) =>
              (option?.label3 ?? "").toLowerCase().includes(input.toLowerCase())
            }
          />
        </Form.Item>
        <Form.Item
          wrapperCol={{
            offset: 8,
            span: 16,
          }}
        >
          <div className="flex justify-end space-x-2">
            <Button
              size="small"
              disabled={openFormCreate}
              onClick={() => setOpenPopOver(false)}
            >
              {t("form.cancel")}
            </Button>
            {(module.fieldType === "select" ||
              module.fieldType === "autocomplete") &&
            module?.number > 0 ? (
              <Popconfirm
                title="Association"
                description={t("vue360.replaceBy", {
                  oldValue:
                    Object.keys(module)?.length > 0 && module?.number > 0
                      ? module?.child[0]?.label_data
                      : relations.find(
                          (el) => el.family_id === selectedModule?.value
                        )?.children[0]?.label,
                  newValue: selectedItem,
                })}
                onConfirm={(e) => {
                  e.stopPropagation();
                  setOpenPopConfirm(true);
                  return form.submit();
                }}
                open={openPopConfirm}
                okButtonProps={{
                  loading: loadingSubmit,
                }}
                onCancel={() => setOpenPopConfirm(false)}
              >
                <Button
                  type="primary"
                  size="small"
                  onClick={() => {
                    setOpenPopConfirm(true);
                  }}
                  disabled={!selectedModule?.key || !searchSelect}
                  loading={loadingSubmit}
                >
                  {t("wiki.Ok")}
                </Button>
              </Popconfirm>
            ) : Object.keys(module).length === 0 &&
              (relations.find((el) => el.family_id === selectedModule?.value)
                ?.fieldType === "select" ||
                relations.find((el) => el.family_id === selectedModule?.value)
                  ?.fieldType === "autocomplete") &&
              relations.find((el) => el.family_id === selectedModule?.value)
                ?.number > 0 ? (
              <Popconfirm
                title="Association"
                description={t("vue360.replaceBy", {
                  oldValue:
                    Object.keys(module)?.length > 0 && module?.number > 0
                      ? module?.child[0]?.label_data
                      : relations.find(
                          (el) => el.family_id === selectedModule?.value
                        )?.children[0]?.label,
                  newValue: selectedItem,
                })}
                onConfirm={(e) => {
                  e.stopPropagation();
                  setOpenPopConfirm(true);
                  return form.submit();
                }}
                open={openPopConfirm}
                okButtonProps={{
                  loading: loadingSubmit,
                }}
                onCancel={() => setOpenPopConfirm(false)}
              >
                <Button
                  type="primary"
                  size="small"
                  onClick={() => {
                    setOpenPopConfirm(true);
                  }}
                  disabled={!selectedModule?.key || !searchSelect}
                  loading={loadingSubmit}
                >
                  {t("wiki.Ok")}
                </Button>
              </Popconfirm>
            ) : (
              <Button
                type="primary"
                size="small"
                htmlType="submit"
                disabled={!selectedModule?.key || !searchSelect}
                loading={loadingSubmit}
              >
                {t("wiki.Ok")}
              </Button>
            )}
          </div>
        </Form.Item>
      </Form>
    </div>
  );
  const identificationTitle = (
    <div onClick={(e) => e.stopPropagation()}>
      <div className="flex flex-row items-center  justify-between">
        <span>{action}</span>
        {selectedModule?.key ? <p>{t("voip.or")}</p> : null}
        {/* {selectedModule?.key ? ( */}
        <Button
          size="small"
          type="primary"
          onClick={() => {
            setOpenFormCreate(true);
            // setTimeout(() => {
            //   setOpenPopOver(false);
            // }, 2000);
          }}
          disabled={!selectedModule?.key}
        >
          {t("form.create")}{" "}
          {Array.isArray(selectedModule?.label?.props?.children)
            ? selectedModule?.label?.props?.children[2]
            : typeof selectedModule?.label === "string"
            ? selectedModule?.label
            : ""}
        </Button>
        {/* ) : null} */}
      </div>
      <Divider className="my-2" />
    </div>
  );
  return (
    <>
      <div onClick={(e) => e.stopPropagation()}>
        <Popover
          content={content}
          trigger={["click"]}
          title={identificationTitle}
          overlayStyle={{ width }}
          open={openPopOver}
          onOpenChange={onChangePopOverOpen}
          arrow={false}
        >
          {ButtonPopOver}
        </Popover>
        <span onClick={(e) => e.stopPropagation()}>
          {selectedModule?.key && (
            <FormCreate
              open={openFormCreate}
              setOpen={setOpenFormCreate}
              familyId={selectedModule?.key}
              idRelation={contactInfo?.id}
              setCatchChange={false}
              source="viewSphere"
            />
          )}
        </span>
      </div>
    </>
  );
};

export default PopOverSelectModule;
