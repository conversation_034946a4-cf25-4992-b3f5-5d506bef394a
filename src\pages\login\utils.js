/**
 * example:
 * token : 
 * {access_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ0ZXN0IiwiZ...",
 * refresh_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ0ZXN0IiwiZ...",
 * expires_in: "3600"
 * }
 * @param {} token
 * example:

 * example:
 * redirectURL :  "httsp://sphere.acp.biz"
 * @param {string} redirectURL

 * @param {boolean} hasReturn
 * lang :  "en|fr"
 * @param {string} lang
 */
export const redirectFunction = (token = {}, redirectURL = "", lang = "en") => {
  const redirectTO = new URL(redirectURL);
  redirectTO.searchParams.append("access_token", token?.access_token);
  redirectTO.searchParams.append("refresh_token", token?.refresh_token);
  redirectTO.searchParams.append("expires_in", token?.expires_in);
  redirectTO.searchParams.append("lang", lang);
  window.location.href = redirectTO;
};
