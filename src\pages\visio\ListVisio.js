// import React, { useCallback, useEffect, useState } from "react";
// import {
//   BellOutlined,
//   CheckOutlined,
//   ClockCircleOutlined,
//   CommentOutlined,
//   CopyOutlined,
//   DeleteOutlined,
//   EditOutlined,
//   ExportOutlined,
//   InfoCircleOutlined,
//   LaptopOutlined,
//   NotificationOutlined,
//   PhoneOutlined,
//   PlayCircleOutlined,
//   RestOutlined,
//   UserOutlined,
//   VideoCameraOutlined,
// } from "@ant-design/icons";
// import {
//   Avatar,
//   Badge,
//   Breadcrumb,
//   Button,
//   Divider,
//   Dropdown,
//   Empty,
//   Input,
//   Layout,
//   List,
//   Menu,
//   Popover,
//   Radio,
//   Skeleton,
//   Space,
//   Tabs,
//   Tooltip,
//   Typography,
//   theme,
// } from "antd";
// import {
//   ArrowUpRightFromCircle,
//   CalendarCheck2,
//   CalendarClockIcon,
//   CalendarRange,
//   InfoIcon,
//   Link,
//   Minus,
//   MoveRight,
//   RefreshCw,
//   Users,
// } from "lucide-react";
// import { useTranslation } from "react-i18next";
// import ItemHeader from "../components/DetailsProfile/ItemHeader";
// import ItemVisio from "./ItemVisio";
// import ShareVisio from "../layouts/visio/components/share-visio";
// import CreateTask from "../voip/components/CreateTask";
// import { toastNotification } from "../../components/ToastNotification";
// import { keys } from "@antv/util";
// import {
//   convertToPlain,
//   getName,
// } from "../layouts/chat/utils/ConversationUtils";
// import UpdateTask from "../tasks/UpdateTask";
// import TasksRoom from "../tasks/tasksRoom";
// import MainService from "../../services/main.service";
// import { getTokenRoom } from "../../new-redux/actions/visio.actions/createVisio";
// import { useSelector, useDispatch } from "react-redux";
// import { FiCopy, FiMoreVertical, FiSearch } from "react-icons/fi";
// import {
//   getDetailsMeet,
//   getRemindersList,
//   onChangeTabVisio,
//   setCountNotificationVisio,
//   setDetailsMeet,
//   setHistory,
//   setKeyMeet,
//   setLastPage,
//   setLater,
//   setListMeet,
//   setNotificationList,
//   setNow,
//   setPage,
//   setRemindersList,
//   setTabKey,
// } from "../../new-redux/actions/visio.actions/visio";
// import useActionCall from "../voip/helpers/ActionCall";
// import { handleMsgClick, humanDate } from "../voip/helpers/helpersFunc";
// import ChatWithUser from "../voip/components/ChatWithUser";
// import Confirm from "../../components/GenericModal";
// import moment from "moment";
// import { HiOutlineCalendar } from "react-icons/hi";
// import ListMeet from "./ListMeet";
// import { setOpenTaskRoomDrawer } from "../../new-redux/actions/tasks.actions/handleTaskRoomDrawer";
// import { setChatSelectedConversation } from "../../new-redux/actions/chat.actions";
// import HeaderVisio from "./HeaderVisio";
// import ListGuestsFollowers from "./ListGuestsFollowers";
// import SelectPipelinesStages from "../../components/SelectPipelinesStages";
// import InfiniteScroll from "react-infinite-scroll-component";
// import NotificationsPopover from "../../components/NotificationsPopover";
// import { URL_ENV } from "index";

// const { Header, Content, Footer, Sider } = Layout;

// const ListVisio = () => {
//   const [t] = useTranslation("common");
//   const [key, setKey] = useState(null);
//   const { userList } = useSelector((state) => state.chat);
//   const [openDrawerMsg, setOpenDrawerMsg] = useState(false);
//   const [taskToUpdate, setTaskToUpdate] = useState(null);
//   const [showNotificationsMenu, setShowNotificationsMenu] = useState(false);

//   const [colleagueInfo, setColleagueInfo] = useState({});
//   const { user } = useSelector((state) => state.user);

//   // const [listMeet, setListMeet] = useState([]);
//   // const [page, setPage] = useState(1);
//   // const [lastPage, setLastPage] = useState(1);
//   const [openQuickVideoCall, setOpenQuickVideoCall] = useState(false);
//   const [openTaskAdvanced, setOpenTaskAdvanced] = useState(false);
//   const [idTask, setIdTask] = useState(null);
//   const [pipelines, setPipelines] = useState([]);
//   const [tasksTypes, setTasksTypes] = useState([]);
//   const [isCopied, setIsCopied] = useState(false);
//   const [showCopy, setShowCopy] = useState(false);
//   const [selectedStage, setSelectedStage] = useState("");
//   // const [notificationList, setNotificationList] = useState([]);
//   const [notificationsPage, setNotificationsPage] = useState(1);
//   const [externeUpdate, setExterneUpdate] = useState(false);
//   const [search, setSearch] = useState("");
//   const [open, setOpen] = useState(false);
//   // const [loadTabs, setLoadTabs] = useState(true);
//   // const [loadDetails, setLoadDetails] = useState(false);
//   const [guestsListPage, setGuestsListPage] = useState(1);
//   const [guestsList, setGuestsList] = useState([]);
//   const [guestsListLastPage, setGuestsListLastPage] = useState(null);
//   const [followersListPage, setFollowersListPage] = useState(1);
//   const [ownersList, setOwnersList] = useState([]);
//   const [followersListLastPage, setFollowersListLastPage] = useState(null);
//   const [guestsSearchQuery, setGuestsSearchQuery] = useState("");
//   const [followersSearchQuery, setFollowersSearchQuery] = useState("");

//   const dispatch = useDispatch();
//   const { isOpen } = useSelector((state) => state.visio);
//   const {
//     listMeet,
//     lastPage,
//     keyMeet,
//     page,
//     loadTabs,
//     loadDetails,
//     detailsMeet,
//     tabKey,
//     now,
//     later,
//     countToday,
//     countUpComing,
//     countHistory,
//     notificationCount,
//     notificationList,
//     pageNotificationList,
//     lastPageNotificationList,
//     remindersList,
//   } = useSelector((state) => state.visioList);

//   async function copyTextToClipboard(text) {
//     if ("clipboard" in navigator) {
//       await navigator.clipboard.writeText(text);
//       setIsCopied(true);
//     } else {
//       return document.execCommand("copy", true, text);
//     }
//   }
//   const handleCopyClick = (copyText) => {
//     // Asynchronously call copyTextToClipboard
//     copyTextToClipboard(copyText)
//       .then(() => {
//         // If successful, update the isCopied state value
//         setIsCopied(true);
//         setTimeout(() => {
//           setIsCopied(false);
//         }, 3000);
//       })
//       .catch((err) => {
//         console.log(err);
//       });
//   };

//   // const onChangeTab = useCallback(
//   //   async (value) => {
//   //     setTabKey(value);
//   //     setPage(1);
//   //     setLoadTabs(true);
//   //     try {
//   //       const res = await generateAxios(URL_ENV?.REACT_APP_BASE_URL+process.env.REACT_APP_SUFFIX_API).get(
//   //         `/get-tasks-visio?type=${value}&page=${1}`
//   //       );
//   //       setListMeet(res.data.data);
//   //       setLastPage(res.data.meta.last_page);
//   //       if (res.data.data.length > 0) setKey(res.data.data[0].id);
//   //       else {
//   //         setDetailsMeet({});
//   //         setKey(null);
//   //       }
//   //       setLoadTabs(false);
//   //     } catch (err) {
//   //       console.log(err);
//   //       setLoadTabs(false);
//   //       setDetailsMeet({});
//   //       toastNotification("error", t("toasts.somethingWrong"), "topRight");
//   //     }
//   //   },
//   //   [
//   //     setTabKey,
//   //     setPage,
//   //     setLoadTabs,
//   //     setListMeet,
//   //     setLastPage,
//   //     setKey,
//   //     setDetailsMeet,
//   //     t,
//   //   ]
//   // );
//   // useEffect(() => {
//   //   setOpenDrawerMsg();
//   // }, [open]);
//   const getNotifications = async () => {
//     try {
//       const response = await MainService.getNotifications(notificationsPage, 3);
//       // if (notificationsPage > 1) {
//       //   setNotifications([...notifications, ...response?.data?.data]);
//       // } else {
//       //   setNotifications(response?.data?.data);
//       //   setLastNotificationsPage(response?.data?.meta?.last_page);
//       //   // setUnreadNotifications(response?.data?.count);
//       // }
//       // setNotificationList((prev) => [...prev, ...response.data.data]);
//       setNotificationsPage(response.data.meta.current_page);
//     } catch (error) {
//       console.log(`Error ${error}`);
//     }
//   };
//   useEffect(() => {
//     const getPipelines = async () => {
//       try {
//         const response = await MainService.getPipelinesByFamily("7");
//         let systemPipeline = response?.data?.data.find(
//           (pipeline) => pipeline?.system === 1
//         )?.id;
//         setPipelines(response?.data?.data);
//       } catch (error) {
//         console.log(`Error ${error}`);
//       }
//     };

//     const getTasksTypes = async () => {
//       try {
//         const response = await MainService.getTasksTypes();
//         setTasksTypes(response?.data?.data?.tasks_type);
//       } catch (error) {
//         console.log(`Error ${error}`);
//       }
//     };

//     // Get guests API.

//     const fetchData = async () => {
//       try {
//         await Promise.all([
//           // onChangeTab(1),
//           dispatch(onChangeTabVisio({ value: 1, keyTab: 1, t, keyMeet: "" })),
//           getPipelines(),
//           getTasksTypes(),
//         ]);
//         // Les réponses de toutes les promesses sont disponibles ici

//         // Faites quelque chose avec les données
//       } catch (error) {
//         // Gérez les erreurs ici
//       }
//     };

//     fetchData();

//     return () => {
//       dispatch(setDetailsMeet({}));
//       dispatch(setKeyMeet([]));
//       dispatch(setKeyMeet(""));
//       dispatch(setPage(1));
//     };
//   }, [dispatch]);
//   useEffect(() => {
//     dispatch(setNotificationList({ page: 1, t }));
//     dispatch(getRemindersList({ t }));
//   }, [dispatch]);

//   const getOwners = useCallback(
//     async (signal) => {
//       try {
//         let formData = new FormData();
//         formData.append("family_id[]", 4);
//         const response = await MainService.getFamilyOptions(
//           "",
//           "",
//           followersSearchQuery,
//           formData,
//           signal
//         );
//         if (followersListPage > 1) {
//           setOwnersList([...ownersList, ...response?.data?.data]);
//         } else {
//           setOwnersList(response?.data?.data);
//         }

//         setFollowersListLastPage(response?.data?.meta?.last_page);
//       } catch (error) {
//         console.log(`Error ${error}`);
//       }
//     },
//     [followersListPage, followersSearchQuery]
//   );
//   useEffect(() => {
//     let abort = new AbortController();
//     if (followersListPage !== undefined) {
//       getOwners(abort?.signal);
//     }
//     return () => abort?.abort();
//   }, [followersListPage, getOwners]);
//   const getGuests = useCallback(
//     async (signal) => {
//       try {
//         //SOUMAYA: REMOVE FAMILY IDS 1 AND 2
//         let formData = new FormData();
//         formData.append("family_id[]", 1);
//         formData.append("family_id[]", 4);
//         formData.append("family_id[]", 2);
//         const response = await MainService.getFamilyOptions(
//           guestsListPage,
//           20,
//           guestsSearchQuery,
//           formData,
//           signal
//         );
//         if (guestsListPage > 1) {
//           setGuestsList([...guestsList, ...response?.data?.data]);
//         } else {
//           setGuestsList(response?.data?.data);
//         }
//         setGuestsListLastPage(response?.data?.meta?.last_page);
//       } catch (error) {
//         console.log(`Error ${error}`);
//       }
//     },
//     [guestsListPage, guestsSearchQuery]
//   );

//   useEffect(() => {
//     let abort = new AbortController();
//     if (guestsListPage !== undefined) {
//       getGuests(abort?.signal);
//     }
//     return () => abort?.abort();
//   }, [getGuests, guestsListPage]);

//   // const getdetailsMeet = async () => {
//   //   setLoadDetails(true);
//   //   try {
//   //     const res = await generateAxios(URL_ENV?.REACT_APP_BASE_URL+process.env.REACT_APP_SUFFIX_API).get(
//   //       `/get-task/${key}`
//   //     );
//   //     setDetailsMeet(res.data.data);
//   //     setLoadDetails(false);
//   //   } catch (err) {
//   //     console.log(err);
//   //     setLoadDetails(false);
//   //     setDetailsMeet([]);

//   //     toastNotification("error", t("toasts.somethingWrong"), "topRight");
//   //   }
//   // };
//   // useEffect(() => {
//   //   if (keyMeet) dispatch(getDetailsMeet({ keyMeet, t }));
//   // }, [keyMeet, t]);
//   const changeTab = (e) => {
//     // getNotifications();
//     dispatch(onChangeTabVisio({ value: e, t, keyMeet: keyMeet }));
//     // if (e === 1) {
//     //   dispatch(setNow(0));
//     // }
//     // if (e == 2) {
//     //   dispatch(setLater(0));
//     // }
//   };
//   const contact = detailsMeet?.guests?.filter(
//     (el) =>
//       el.family_id === 2 &&
//       el.label?.toLowerCase().includes(search?.toLowerCase())
//   );
//   const company = detailsMeet?.guests?.filter(
//     (el) =>
//       el.family_id === 1 &&
//       el.label?.toLowerCase().includes(search?.toLowerCase())
//   );
//   const guests = detailsMeet?.guests
//     ?.filter((el) => el.label?.toLowerCase().includes(search?.toLowerCase()))
//     .map((objet2) => {
//       // Trouver l'objet correspondant dans le tableau1
//       let objet1 = userList.find((obj) => obj.uuid === objet2.uuid);
//       // Vérifier si l'objet existe dans le tableau1
//       if (objet1) {
//         // Utiliser la syntaxe des objets littéraux pour créer un nouvel objet avec l'ID ajouté
//         return {
//           ...objet2,
//           idUser: objet1.id,
//           extension: Number(objet1.post_number),
//         };
//       }

//       return objet2;
//     });

//   const colleague = detailsMeet?.guests
//     ?.filter(
//       (el) =>
//         el.family_id === 4 &&
//         el.label?.toLowerCase().includes(search?.toLowerCase())
//     )
//     .map((objet2) => {
//       // Trouver l'objet correspondant dans le tableau1
//       let objet1 = userList.find((obj) => obj.uuid === objet2.uuid);
//       // Vérifier si l'objet existe dans le tableau1
//       if (objet1) {
//         // Utiliser la syntaxe des objets littéraux pour créer un nouvel objet avec l'ID ajouté
//         return {
//           ...objet2,
//           idUser: objet1.id,
//           extension: Number(objet1.post_number),
//         };
//       }

//       return objet2;
//     });
//   const followers = detailsMeet?.followers
//     ?.filter((el) => el.label?.toLowerCase().includes(search?.toLowerCase()))
//     .map((objet2) => {
//       // Trouver l'objet correspondant dans le tableau1
//       let objet1 = userList.find((obj) => obj.uuid === objet2.uuid);

//       // Vérifier si l'objet existe dans le tableau1
//       if (objet1) {
//         // Utiliser la syntaxe des objets littéraux pour créer un nouvel objet avec l'ID ajouté
//         return {
//           ...objet2,
//           userId: objet1.id,
//           extension: Number(objet1.post_number),
//         };
//       }

//       return objet2;
//     });

//   const handleDelete = async (id, start_date) => {
//     try {
//       let formData = new FormData();
//       formData.append("id[]", id);
//       const response = await MainService.deleteSpecificTask(formData);
//       if (response?.status === 200) {
//         dispatch(setListMeet(listMeet.filter((el) => el.id !== id)));
//         const currentDate = moment().format("YYYY-MM-DD");
//         const startDate = moment(start_date, "YYYY-MM-DD");
//         if (start_date === currentDate) {
//           dispatch(setNow({ now: now, countToday: countToday - 1 }));
//           // dispatch(setLastPage(Math.ceil((countToday - 1) / 10)));
//         }

//         if (startDate.isAfter(currentDate, "day")) {
//           dispatch(
//             setLater({ later: later, countUpComing: countUpComing - 1 })
//           );
//           // dispatch(setLastPage(Math.ceil(countUpComing - 1 / 10)));
//         }

//         if (startDate.isBefore(currentDate, "day")) {
//           dispatch(setHistory({ countHistory: countHistory - 1 }));
//           // dispatch(setLastPage(Math.ceil(countUpComing - 1 / 10)));
//         }
//         dispatch(setDetailsMeet({}));

//         toastNotification("success", t("toasts.taskDeleted"), "bottomRight");
//       }
//     } catch (error) {
//       console.log(`Error ${error}`);
//       toastNotification("error", t("toasts.somethingWrong"));
//     }
//   };
//   const markNotificationAsRead = async (payload) => {
//     try {
//       const response = await MainService.markNotificationAsRead(payload);
//       let itemIndex =
//         notificationList &&
//         notificationList.findIndex((notif) =>
//           notif?.id && notif?.id === payload?.log_id
//             ? payload?.log_id
//             : !notif?.id && notif?.id_data === payload?.task_id
//             ? payload?.task_id
//             : null
//         );
//       let newArray =
//         notificationList &&
//         notificationList.map((notification, i) =>
//           i === itemIndex ? response?.data?.data : notification
//         );
//       console.log(newArray);
//       dispatch(setNotificationList(newArray));
//       // setUnreadNotifications(unreadNotifications - 1);
//       dispatch(setCountNotificationVisio(notificationCount - 1));
//     } catch (error) {
//       console.log(`Error ${error}`);
//     }
//   };
//   const markAllNotifsAsRead = async () => {
//     try {
//       const response = await MainService.markAllNotificationsAsRead();
//       if (response?.status === 200) {
//         // setUnreadNotifications(0);
//         dispatch(setCountNotificationVisio(0));
//         dispatch(
//           setNotificationList(
//             notificationList &&
//               notificationList.map((notification) =>
//                 notification?.read === 0
//                   ? { ...notification, read: 1 }
//                   : notification
//               )
//           )
//         );
//       }
//     } catch (error) {
//       console.log(`Error ${error}`);
//     }
//   };
//   const items = [
//     {
//       key: 1,
//       label: (
//         <>
//           {/* // <Badge count={now} offset={[10, 0]}> */}
//           {t("visio.today")}{" "}
//           {/* {countToday > 0 ? <Badge count={countToday} color="blue" /> : ""} */}
//           {countToday > 0 ? "(" + countToday + ")" : ""}
//           {/* </Badge> */}
//         </>
//       ),
//       children: (
//         <>
//           {loadTabs ? (
//             <Skeleton
//               active
//               shape="square"
//               avatar
//               paragraph={{
//                 rows: 1,
//               }}
//               style={{
//                 width: "100%",
//                 marginTop: "10px",
//               }}
//             />
//           ) : countToday > 0 ? (
//             <ListMeet
//               lastPage={lastPage}
//               listMeet={listMeet}
//               keyMeet={keyMeet}
//               page={page}
//               loadTabs={loadTabs}
//               tabKey={tabKey}
//               t={t}
//               setIdTask={setIdTask}
//               setExterneUpdate={setExterneUpdate}
//               setOpenDrawerMsg={setOpenDrawerMsg}
//               notificationList={notificationList}
//             />
//           ) : (
//             <Typography.Title level={5} style={{ textAlign: "center" }}>
//               {t("visio.noVisioToday")}
//             </Typography.Title>
//           )}
//         </>
//       ),
//     },
//     {
//       key: 2,
//       label: (
//         <>
//           {t("visio.upComing")}{" "}
//           {countUpComing > 0
//             ? "(" + countUpComing + ")"
//             :
//               ""}
//         </>
//       ),
//       children: (
//         <>
//           {countUpComing > 0 ? (
//             <ListMeet
//               lastPage={lastPage}
//               listMeet={listMeet}
//               keyMeet={keyMeet}
//               page={page}
//               loadTabs={loadTabs}
//               tabKey={tabKey}
//               t={t}
//               setIdTask={setIdTask}
//               setExterneUpdate={setExterneUpdate}
//               setOpenDrawerMsg={setOpenDrawerMsg}
//               notificationList={notificationList}
//             />
//           ) : (
//             <Typography.Title level={5} style={{ textAlign: "center" }}>
//               {" "}
//               {t("visio.noVisioLater")}
//             </Typography.Title>
//           )}
//         </>
//       ),
//     },
//     {
//       key: 0,
//       label: (
//         <>
//           {t("visio.history")}

//           {/* {countHistory > 0 ? <Badge count={countHistory} color="blue" /> : ""} */}
//         </>
//       ),
//       children: (
//         <>
//           {countHistory > 0 ? (
//             <ListMeet
//               lastPage={lastPage}
//               listMeet={listMeet}
//               keyMeet={keyMeet}
//               page={page}
//               loadTabs={loadTabs}
//               tabKey={tabKey}
//               t={t}
//               setIdTask={setIdTask}
//               setExterneUpdate={setExterneUpdate}
//               setOpenDrawerMsg={setOpenDrawerMsg}
//               notificationList={notificationList}
//             />
//           ) : (
//             t("visio.noVisioHistory")
//           )}
//         </>
//       ),
//     },
//   ];
//   const openChat = async (id) => {
//     // MainService.createRoomTask({
//     //   task_id: id,
//     // })
//     dispatch(setOpenTaskRoomDrawer(true));

//     const singleTaskData = detailsMeet;
//     // console.log("CONTENT OF THE TASK", content);
//     let usr_ids = ",";
//     let usr_ids_arr = [];

//     let guestsIds =
//       Object.keys(singleTaskData).length > 0 &&
//       singleTaskData?.guests &&
//       Object.keys(singleTaskData).length > 0 &&
//       singleTaskData?.guests.map((guest) => (guest?.uuid ? guest?.uuid : null));
//     let followersIds =
//       Object.keys(singleTaskData).length > 0 &&
//       singleTaskData?.followers &&
//       Object.keys(singleTaskData).length > 0 &&
//       singleTaskData?.followers.map((follower) =>
//         follower?.uuid ? follower?.uuid : null
//       );

//     usr_ids_arr = [
//       ...guestsIds,
//       ...followersIds,
//       singleTaskData?.creator?.id !== singleTaskData?.owner_id?.id
//         ? singleTaskData?.owner_id?.uuid
//         : null,
//     ];

//     //get user_ids from followers and guests if they had uuid
//     singleTaskData?.followers?.forEach((follower) => {
//       if (!Array.isArray(follower?.uuid)) {
//         usr_ids_arr.push(follower?.uuid);
//       }
//     });

//     singleTaskData?.guests?.forEach((guest) => {
//       if (!Array.isArray(guest?.uuid)) {
//         usr_ids_arr.push(guest?.uuid);
//       }
//     });

//     //remove duplicates
//     usr_ids_arr = [...new Set(usr_ids_arr)]
//       .filter((id) => id !== null)
//       .filter((el) => el !== singleTaskData?.creator?.uuid);

//     await MainService.createRoomTask({
//       relation_id: id,
//       name: singleTaskData?.label,
//       description: singleTaskData?.label,
//       admin_id: singleTaskData?.creator?.uuid,
//       users_ids: usr_ids_arr?.toString(),
//     })
//       .then((res) => {
//         if (!res?.data?.message) {
//           MainService.assignRoomToTask(id, {
//             room_id: res?.data?.room?.conversation_id,
//           })
//             .then((res) => {
//               console.log("ASSIGN ROOM TO TASK", res);
//             })
//             .catch((err) => {
//               console.log("ASSIGN ROOM TO TASK ERROR", err);
//             });
//         }

//         dispatch(
//           setChatSelectedConversation({
//             selectedConversation: {
//               name: res?.data?.room?.name,
//               description: res?.data?.room?.description,
//               image: res?.data?.room?.image,
//               admin_id: res?.data?.room?.admin_id,
//               bot: null,
//               id: res?.data?.room?.conversation_id,
//               type: "room",
//               mode: "members",
//               muted_status: false,
//               conversationId: res?.data?.room?.conversation_id,
//               external: false,
//               participants: res?.data?.room?.participants,
//             },
//           })
//         );
//       })
//       .catch((err) => {
//         console.log("ROOM TASK ERROR", err);
//       });
//   };
//   const DropdownOption = ({ data }) => {
//     console.log(data);
//     const items = [
//       {
//         label: t("table.edit"),
//         key: "2",
//         icon: <EditOutlined />,
//       },
//       {
//         label: t("menu1.chat"),
//         key: "3",
//         icon: <CommentOutlined />,
//         disabled:
//           detailsMeet?.guests?.length === 0 &&
//           detailsMeet?.followers?.length === 0 &&
//           detailsMeet?.creator?.id === detailsMeet?.owner_id?.id,
//       },
//       user.id === data.owner_id
//         ? {
//             label: t("table.delete"),
//             danger: true,
//             key: "4",
//             icon: <DeleteOutlined />,
//           }
//         : "",
//     ];

//     return (
//       <div className="r-8">
//         <Dropdown
//           trigger={["click"]}
//           placement="bottomLeft"
//           open={open}
//           onOpenChange={(e) => setOpen(e)}
//           getPopupContainer={(triggerNode) => triggerNode.parentNode}
//           arrow
//           menu={{
//             items,
//             onClick: (e) => {
//               e.domEvent.stopPropagation();
//               setOpen(false);

//               if (e.key === "1") {
//                 dispatch(
//                   getTokenRoom({
//                     room: detailsMeet?.location,
//                     errorText1: t("toasts.errorFetchApi"),
//                     errorText2: t("toasts.errorRoomNotFound"),
//                   })
//                 );
//               }
//               if (e.key === "2") {
//                 setExterneUpdate(false);
//                 setIdTask(data.id);
//               }
//               if (e.key === "3") {
//                 openChat(data.id);
//               }

//               if (e.key === "4") {
//                 Confirm(
//                   `Delete "${data.title}" `,
//                   "Confirm",
//                   <RestOutlined style={{ color: "red" }} />,
//                   function func() {
//                     return handleDelete(data.id, data.start_date);
//                   },
//                   true
//                 );
//               }
//             },
//           }}>
//           <FiMoreVertical className="mt-[2px] h-4 w-4 cursor-pointer text-gray-400 hover:text-gray-700" />
//         </Dropdown>
//       </div>
//     );
//   };
//   return (
//     <Layout>
//       <Sider
//         width="360px"
//         style={{
//           background: "white",
//           padding: "12px 0px",
//           borderRight: "1px solid #e8e8e8",
//           height: "calc(100vh - 57px)",
//           // marginTop: "-20px",
//         }}>
//         <Space direction="vertical" className="px-2">
//           <div>
//             <div className=" flex items-center justify-end ">
//               <Typography.Title level={3}></Typography.Title>

//               <div>
//                 <Badge
//                   count={notificationCount}
//                   size="small"
//                   style={{ top: "10px", right: "10px" }}>
//                   <Tooltip title={"Notifications"}>
//                     <Popover
//                       placement="bottom"
//                       content={
//                         <NotificationsPopover
//                           notificationsList={notificationList}
//                           notificationsPage={pageNotificationList}
//                           setTaskToUpdate={setIdTask}
//                           setNotificationsPage={setNotificationsPage}
//                           lastNotificationsPage={lastPageNotificationList}
//                           markNotificationAsRead={markNotificationAsRead}
//                           source="notifications"
//                           from="listVisio"
//                         />
//                       }
//                       trigger={["click"]}
//                       open={showNotificationsMenu}
//                       onOpenChange={(open) => {
//                         if (!open) {
//                           markAllNotifsAsRead();
//                         }
//                         setShowNotificationsMenu(open);
//                       }}>
//                       <Button
//                         icon={<BellOutlined style={{ fontSize: "15px" }} />}
//                         shape="circle"
//                         type="text"
//                       />
//                     </Popover>
//                   </Tooltip>
//                 </Badge>
//               </div>
//               <div>
//                 <Badge
//                   count={remindersList && remindersList.length}
//                   style={{ top: "10px", right: "10px" }}
//                   size="small">
//                   <Tooltip title={t("tasks.reminderTooltip")}>
//                     <Popover
//                       placement="bottomLeft"
//                       autoAdjustOverflow={true}
//                       destroyTooltipOnHide
//                       content={
//                         <NotificationsPopover
//                           notificationsList={remindersList}
//                           markNotificationAsRead={() => {}}
//                           setTaskToUpdate={setTaskToUpdate}
//                           notificationsPage={notificationsPage}
//                           setNotificationsPage={() => {}}
//                           lastNotificationsPage={1}
//                           setNotifications={""}
//                           source="reminders"
//                         />
//                       }
//                       trigger={["click"]}>
//                       <Button
//                         type="text"
//                         shape="circle"
//                         size="large"
//                         icon={
//                           <ClockCircleOutlined style={{ fontSize: "15px" }} />
//                         }
//                       />
//                     </Popover>
//                   </Tooltip>
//                 </Badge>
//               </div>
//             </div>

//             <div className=" flex flex-col items-center  gap-4">
//               <Button
//                 onClick={() => setOpenQuickVideoCall(true)}
//                 disabled={isOpen}
//                 type="primary"
//                 icon={<PlayCircleOutlined style={{ fontSize: "100%" }} />}>
//                 {/* {t("visio.newMeeting")} */}
//                 {t("chat.header.visio.createVideoConferance")}
//               </Button>
//               <Button
//                 onClick={() => setOpenTaskAdvanced(true)}
//                 icon={<HiOutlineCalendar style={{ fontSize: "100%" }} />}>
//                 {t("chat.header.createVideoLater")}
//               </Button>
//             </div>

//             <br />
//           </div>
//           <div className=" w-full px-2">
//             {/* <Radio.Group
//                 buttonStyle="solid"
//                 options={options}
//                 onChange={onChangeTab}
//                 value={value3}
//                 // optionType="button"
//               /> */}
//             {/* <div className=" flex flex-col items-center  gap-4"> */}
//             <Tabs
//               defaultActiveKey="1"
//               activeKey={tabKey}
//               items={items}
//               onChange={(e) => changeTab(e)}
//             />
//             {/* </div> */}
//           </div>
//         </Space>
//       </Sider>
//       <Layout
//         style={{
//           // padding: "0 24px 24px",

//           background: "white",
//         }}>
//         <Content
//           style={{
//             margin: 0,
//             minHeight: 280,
//             background: "white",
//           }}>
//           {loadDetails ? (
//             <>
//               <Skeleton
//                 active
//                 paragraph={{
//                   rows: 1,
//                 }}
//               />
//               <Divider />
//               <Skeleton
//                 active
//                 paragraph={{
//                   rows: 4,
//                 }}
//               />
//             </>
//           ) : Object.keys(detailsMeet).length > 0 ? (
//             <div className=" flex w-full space-x-3   ">
//               <div className="w-full  p-[12px]">
//                 <HeaderVisio
//                   detailsMeet={detailsMeet}
//                   isOpen={isOpen}
//                   setExterneUpdate={setExterneUpdate}
//                   setIdTask={setIdTask}
//                   handleDelete={handleDelete}
//                 />

//                 <Divider />
//                 {/* <Typography.Title level={3}>
//                       <InfoIcon size={18} /> {t("visio.loginInformation")}
//                     </Typography.Title> */}

//                 <Space direction="vertical" style={{ width: "100%" }}>
//                   {/* <div
//                     // onMouseEnter={() => setShowCopy(true)}
//                     // onMouseLeave={() => setShowCopy(false)}
//                     className="flex items-center "
//                   > */}
//                   <Typography.Title level={4} className="px-4">
//                     {t("visio.linkMeeting")}
//                   </Typography.Title>
//                   <div className="mx-4 inline-flex items-center  rounded-lg bg-sky-50   pr-2 shadow-sm">
//                     <div
//                       className=" p-4"
//                       style={{
//                         boxShadow: "8px 0 6px -6px rgba(0, 0, 0, 0.1)",
//                       }}>
//                       <Link size={18} />
//                     </div>
//                     {/* <Divider
//                           type="vertical"
//                           style={{ color: "#e2e8f0", height: "40px" }}
//                         /> */}

//                     <div className="p-2.5">
//                       {URL_ENV?.REACT_APP_DOMAIN}?room_visio_name=
//                       {detailsMeet?.location}
//                     </div>
//                     <FiCopy
//                       onClick={() =>
//                         handleCopyClick(
//                           URL_ENV?.REACT_APP_DOMAIN +
//                             "?room_visio_name=" +
//                             detailsMeet?.location
//                         )
//                       }
//                       className={`mx-1 text-base  text-gray-600 hover:cursor-pointer hover:opacity-80 ${
//                         isCopied ? "hidden" : "block"
//                       }`}
//                     />

//                     {isCopied && (
//                       <CheckOutlined
//                         className={`mx-1 text-base text-gray-600 hover:opacity-80 `}
//                       />
//                     )}
//                   </div>
//                   {detailsMeet.description ? (
//                     <div>
//                       <Typography.Title level={4} className="px-4">
//                         Description
//                       </Typography.Title>
//                       <Typography.Paragraph className="mx-4 flex min-h-[70px] rounded bg-gray-50 px-2 py-3 shadow">
//                         {detailsMeet.description}
//                       </Typography.Paragraph>
//                     </div>
//                   ) : (
//                     ""
//                   )}
//                   {detailsMeet.note ? (
//                     <div>
//                       <Typography.Title level={4} className="px-4">
//                         Note
//                       </Typography.Title>
//                       <div className="mx-4 flex min-h-[70px]  rounded bg-[#FFFFCC] px-2 py-3 shadow">
//                         <Typography.Paragraph>
//                           {convertToPlain(detailsMeet.note)}
//                         </Typography.Paragraph>
//                       </div>
//                     </div>
//                   ) : (
//                     ""
//                   )}
//                   {detailsMeet.pipeline_label && detailsMeet.stage_label ? (
//                     <div>
//                       <Typography.Title level={4} className="px-4">
//                         Pipeline/Stage
//                       </Typography.Title>

//                       <div className="mx-4 inline-flex   px-2 py-3 ">
//                         {detailsMeet.pipeline_label ? (
//                           <SelectPipelinesStages
//                             stages={pipelines}
//                             record={detailsMeet}
//                             iconsTasks={tasksTypes.iconsTasks}
//                             setSelectedStage={setSelectedStage}
//                             selectedStage={selectedStage}
//                             source="listVisio"
//                           />
//                         ) : null}
//                       </div>
//                     </div>
//                   ) : (
//                     ""
//                   )}

//                   <Typography.Text className="px-4" style={{ float: "right" }}>
//                     {t("tasks.drawerHeaderOne")}{" "}
//                     <Tooltip
//                       title={getName(detailsMeet?.creator?.label, "name")}>
//                       {[
//                         "jpg",
//                         "jpeg",
//                         "png",
//                         "gif",
//                         "bmp",
//                         "tiff",
//                         "tif",
//                         "jfif",
//                         "avif",
//                         "webp",
//                       ].includes(
//                         detailsMeet?.creator?.avatar?.split(".")?.pop()
//                       ) ? (
//                         <Avatar
//                           style={{
//                             backgroundColor: "#fde3cf",
//                             color: "#f56a00",
//                             marginRight: "10px",
//                           }}
//                           src={`${
//                             URL_ENV?.REACT_APP_BASE_URL +
//                             URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
//                           }${detailsMeet?.creator?.avatar}`}
//                           alt={detailsMeet?.creator?.avatar}
//                         />
//                       ) : (
//                         <Avatar
//                           style={{
//                             backgroundColor: "#fde3cf",
//                             color: "#f56a00",
//                             marginRight: "10px",
//                           }}
//                           icon={detailsMeet?.creator?.avatar}
//                           alt={detailsMeet?.creator?.avatar}
//                         />
//                       )}
//                     </Tooltip>
//                     {/* {t("tasks.drawerHeaderTwo")}{" "} */}
//                     <Typography.Text>
//                       {humanDate(detailsMeet?.create_at, t)}
//                     </Typography.Text>
//                   </Typography.Text>{" "}
//                 </Space>
//               </div>

//               <div className="h-[calc(100vh-57px)]  w-80  min-w-[320px] max-w-[320px]  bg-slate-100">
//                 <div className="m-[12px]">
//                   <div className="mt-[39px] w-full">
//                     <Input
//                       size="middle"
//                       placeholder={t("visio.searchGuests&followers")}
//                       prefix={<FiSearch className="text-slate-500" />}
//                       value={search}
//                       onChange={(e) =>
//                         setSearch(
//                           e.target.value.trimStart().replace(/\s{1,} /g, " ")
//                         )
//                       }
//                       className="w-full flex-1"
//                       allowClear
//                     />
//                   </div>
//                   <ListGuestsFollowers guests={guests} followers={followers} />
//                 </div>
//                 {/* <Typography.Text level={4}>
//                     1 {t("visio.yes")}, 1{" "}
//                     {t("visio.waiting", {
//                       plural: "",
//                     })}
//                   </Typography.Text> */}
//               </div>
//             </div>
//           ) : (
//             <div
//               // style={{
//               //   position: "absolute",
//               //   top: "50%",
//               //   left: "50%",
//               //   transform: "translate(-50%, -50%)",
//               // }}$
//               className="mt-10 flex items-center justify-center">
//               <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
//             </div>
//           )}
//         </Content>
//       </Layout>
//       {openQuickVideoCall && (
//         <ShareVisio
//           open={openQuickVideoCall}
//           onClose={() => setOpenQuickVideoCall(false)}
//         />
//       )}
//       {!idTask ? (
//         <CreateTask
//           titleLabel={"Visio Conf " + moment().format("DDMMYYYY HH:mm:ss")}
//           open={openTaskAdvanced}
//           source="visio"
//           setOpen={setOpenTaskAdvanced}
//           mask={true}
//           setListMeet={setListMeet}
//           listVisio={true}
//         />
//       ) : null}
//       {!openTaskAdvanced && idTask ? (
//         <UpdateTask
//           id={idTask}
//           externeUpdate={externeUpdate}
//           setId={setIdTask}
//           data={{
//             ...detailsMeet,
//             upload:
//               detailsMeet.upload == 0
//                 ? null
//                 : Array.isArray(detailsMeet?.upload)
//                 ? detailsMeet?.upload
//                 : detailsMeet?.files,
//           }}
//           tasksTypes={tasksTypes}
//           pipelines={pipelines}
//           ownersList={ownersList}
//           setOwnersList={setOwnersList}
//           guestsList={guestsList}
//           setGuestsList={setGuestsList}
//           guestsListPage={guestsListPage}
//           setGuestsListPage={setGuestsListPage}
//           followersListPage={followersListPage}
//           setFollowersListPage={setFollowersListPage}
//           followersListLastPage={followersListLastPage}
//           setFollowersListLastPage={setFollowersListLastPage}
//           setGuestsListLastPage={setGuestsListLastPage}
//           guestsListLastPage={guestsListLastPage}
//           setFollowersSearchQuery={setFollowersSearchQuery}
//           followersSearchQuery={followersSearchQuery}
//           setGuestsSearchQuery={setGuestsSearchQuery}
//           guestsSearchQuery={guestsSearchQuery}
//           from={showNotificationsMenu ? "home" : ""}
//         />
//       ) : null}

//       {!idTask ? <TasksRoom /> : ""}
//     </Layout>
//   );
// };
// export default ListVisio;
