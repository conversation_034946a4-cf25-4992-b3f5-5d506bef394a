import React from "react";

const EXEIcon = ({ width = "50px", height = "50px", ...props }) => {
  return (
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      width={width} 
      height={height} 
      viewBox="-4 0 64 64"
      {...props}
    >
      <path 
        d="M5.112.025c-2.802 0-5.073 2.272-5.073 5.074v53.841c0 2.803 2.271 5.074 5.073 5.074h45.774c2.801 0 5.074-2.271 5.074-5.074v-38.605l-18.902-20.31h-31.946z" 
        fillRule="evenodd" 
        clipRule="evenodd" 
        fill="#8199AF"
      />

      <g fillRule="evenodd" clipRule="evenodd">
        <path 
          d="M55.961 20.377v1h-12.799s-6.312-1.26-6.129-6.708c0 0 .208 5.708 6.004 5.708h12.924z" 
          fill="#617F9B"
        />
        <path 
          d="M37.059.025v14.561c0 1.656 1.104 5.792 6.104 5.792h12.799l-18.903-20.353z" 
          opacity=".5" 
          fill="#ffffff"
        />
      </g>

      <path 
        d="M17.455 53.919h-6.247c-.595 0-1.081-.486-1.081-1.081v-9.848c0-.594.486-1.08 1.081-1.08h6.247c.361 0 .648.288.648.684 0 .36-.287.648-.648.648h-5.833v3.871h5.708c.359 0 .648.288.648.685 0 .36-.289.648-.648.648h-5.708v4.141h5.833c.361 0 .648.288.648.648.001.396-.287.684-.648.684zm12.098.126c-.217 0-.414-.09-.541-.27l-3.727-4.97-3.746 4.97c-.125.18-.324.27-.539.27-.396 0-.721-.306-.721-.72 0-.144.036-.306.145-.432l3.889-5.131-3.619-4.825c-.09-.126-.145-.271-.145-.415 0-.342.289-.72.721-.72.217 0 .432.108.576.288l3.439 4.627 3.438-4.646c.126-.18.324-.27.54-.27.379 0 .738.306.738.72 0 .145-.035.289-.126.415l-3.618 4.808 3.889 5.149c.09.126.126.27.126.414 0 .396-.324.738-.719.738zm10.78-.126h-6.247c-.595 0-1.081-.486-1.081-1.081v-9.848c0-.594.486-1.08 1.081-1.08h6.247c.36 0 .648.288.648.684 0 .36-.288.648-.648.648h-5.833v3.871h5.707c.36 0 .648.288.648.685 0 .36-.288.648-.648.648h-5.707v4.141h5.833c.36 0 .648.288.648.648 0 .396-.288.684-.648.684z" 
        fill="#ffffff"
      />
    </svg>
  );
};

export default EXEIcon;
