import {
  ADD_ROW_FAMILY,
  ADD_ROW_TAG,
  ADD_ROW_TYPE,
  ADD_ROW_TYPE_ACTIVITY,
  ADD_ROW_TYPE_CONTACT,
  ADD_ROW_UNITY,
  IS_DELETE_ROWS,
  RESET_STATE,
} from "../constants";

const initialState = {
  addRowFamily: false,
  addRowType: false,
  addRowUnity: false,
  addRowTypeContact: false,
  addRowActivity: false,
  addRowTag: false,
  isDeleteRows: false,
};

const form = (state = initialState, action) => {
  const { type, payload } = action;
  switch (type) {
    case ADD_ROW_FAMILY:
      return {
        ...state,
        addRowFamily: payload,
      };
    case ADD_ROW_TYPE:
      return {
        ...state,
        addRowType: payload,
      };
    case ADD_ROW_UNITY:
      return {
        ...state,
        addRowUnity: payload,
      };
    case ADD_ROW_TYPE_CONTACT:
      return {
        ...state,
        addRowTypeContact: payload,
      };
    case ADD_ROW_TYPE_ACTIVITY:
      return {
        ...state,
        addRowActivity: payload,
      };
    case ADD_ROW_TAG:
      return {
        ...state,
        addRowTag: payload,
      };
    case IS_DELETE_ROWS:
      return {
        ...state,
        isDeleteRows: payload,
      };
    case RESET_STATE: {
      return initialState;
    }
    default:
      return state;
  }
};

export default form;
