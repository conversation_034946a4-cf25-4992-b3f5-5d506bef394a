import { Badge, Divider, List, Skeleton, Space, Spin, Switch, Tag } from "antd";
import React, { memo, useEffect, useState } from "react";

import VirtualList from "rc-virtual-list";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import moment from "moment";
import { useTranslation } from "react-i18next";
import { toastNotification } from "../../../../components/ToastNotification";
import Paragraph from "@tiptap/extension-paragraph";
import Typography from "antd/es/typography/Typography";

const ListOfSignature = memo(
  ({
    setListSignature,
    listSignature,
    lastPage,
    page,
    loadingList,
    keySignature,
    setKeySignature,
    setClickedNote,
    setDisabled,
    setFirstRender,
    allSignature,
    setHasSelectedNode,
    setContent,
  }) => {
    const [t] = useTranslation("common");

    const ContainerHeight = 400;
    const [loading, setLoading] = useState(false);
    const dispatch = useDispatch();

    const [windowHeight, setWindowHeight] = useState(window.innerHeight);
    const { dataAccounts } = useSelector((state) => state.mailReducer);

    useEffect(() => {
      const handleResize = () => {
        setWindowHeight(window.innerHeight);
      };

      // Ajoute un écouteur d'événement pour le redimensionnement de la fenêtre
      window.addEventListener("resize", handleResize);

      // Nettoyage de l'écouteur d'événement lors de la suppression du composant
      return () => {
        window.removeEventListener("resize", handleResize);
      };
    }, []);
    const onScroll = async (e) => {
      if (
        e.currentTarget.clientHeight + e.currentTarget.scrollTop ===
          e.currentTarget.scrollHeight &&
        page + 1 <= lastPage
      ) {
        setLoading(true);
        try {
          setLoading(false);
        } catch (err) {
          setLoading(false);

          toastNotification("error", t("toasts.somethingWrong"), "topRight");
        }
      }
    };
    const handleClick = (data) => {};
    // useEffect(() => {
    //   const data = listMeet.map((objet) => {
    //     const objetDeuxiemeTableau = notificationList.find(
    //       (obj) => obj.id_data === objet.id
    //     );

    //     return objetDeuxiemeTableau
    //       ? {
    //           ...objet,
    //           read: objetDeuxiemeTableau.read,
    //           idLog: objetDeuxiemeTableau.id,
    //         }
    //       : objet;
    //   });
    //   dispatch(setListMeet(data));
    // }, [notificationList]);
    return (
      <>
        {/* <Typography.Paragraph>
        {t("visio.myMeetings")}{" "}
        {loadTabs ? (
          ""
        ) : (
          <Badge
            count={
              tabKey === 0
                ? countHistory
                : tabKey === 1
                ? countToday
                : countUpComing
            }
            color="blue"
            size="small"
            className={`${
              tabKey === 0
                ? countHistory > 0
                  ? ""
                  : "hidden"
                : tabKey === 1
                ? countToday > 0
                  ? ""
                  : "hidden"
                : countUpComing > 0
                ? ""
                : "hidden"
            }`}
          />
        )}
      </Typography.Paragraph> */}
        {loadingList ? (
          <Skeleton
            active
            shape="square"
            avatar
            paragraph={{
              rows: 1,
            }}
            style={{
              width: "100%",
              marginTop: "10px",
            }}
          />
        ) : (
          <div className="-mr-[16px] h-[calc(100vh-220px)] overflow-auto">
            {/* <Divider orientation="left" plain>
        Mes réunions
      </Divider> */}

            <List>
              <VirtualList
                data={allSignature}
                // height={windowHeight - 110}
                itemKey="email"
                onScroll={onScroll}
              >
                {(el) => (
                  <List.Item
                    key={el.id}
                    className="mr-2 border-none"
                    onClick={(e) => {
                      // e.preventDefault();
                      setClickedNote(el.value);
                      setContent(el.value);
                      setListSignature(allSignature);
                      setTimeout(() => {
                        setHasSelectedNode(false);
                      }, 1);
                      setTimeout(() => {
                        setKeySignature(el.id);
                      }, 50);
                      setFirstRender(true);
                    }}
                    style={{ borderBlockEnd: "none", padding: "4px" }}
                  >
                    <div
                      className={` flex w-full flex-col    ${
                        keySignature === el.id
                          ? "bg-blue-100"
                          : "hover:bg-blue-50"
                      } cursor-pointer rounded-md py-3  pl-2  pr-1`}
                      // onMouseEnter={() => setShowCopy(true)}
                      // onMouseLeave={() => setShowCopy(false)}
                    >
                      <Typography.Text
                        style={{
                          margin: 0,
                        }}
                        type="secondary"
                      >
                        {
                          dataAccounts?.find(
                            (acc) => acc.value === el.account_id
                          )?.label
                        }
                      </Typography.Text>
                      <div className="flex w-full  items-center  justify-between ">
                        <div className=" flex items-center gap-1">
                          <Typography.Title
                            level={5}
                            style={{
                              margin: 0,
                            }}
                          >
                            {el.label}
                          </Typography.Title>
                        </div>
                        {typeof el.id !== "number" && el?.default ? (
                          <Tag color="#108ee9">
                            {t("sales.defaultcurrency")}
                          </Tag>
                        ) : null}
                      </div>
                      {/* 
                      <Switch
                        size="small"
                        checked={el.default}
                        onChange={(e) =>
                          setListSignature((prev) =>
                            prev.map((li) =>
                              li.id === el.id ? { ...li, default: e } : li
                            )
                          )
                        }
                      /> */}
                    </div>
                  </List.Item>
                )}
              </VirtualList>
              {/* {loading ? <Spin /> : ""} */}
            </List>

            {/* </Flex> */}
          </div>
        )}
      </>
    );
  }
);

export default ListOfSignature;
