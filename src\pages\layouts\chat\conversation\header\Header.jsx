import {
  MailOutlined,
  PhoneOutlined,
  PlayCircleOutlined,
  PushpinOutlined,
  SearchOutlined,
  StarOutlined,
  TeamOutlined,
} from "@ant-design/icons";
import {
  Badge,
  Divider,
  Space,
  Tooltip,
  Typography,
  Button,
  Dropdown,
} from "antd";

import { lazy, Suspense, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { setOpenDrawer, setSearchList } from "new-redux/actions/chat.actions";
import { AvatarChat, DropDownGroupIndex, Loader } from "components/Chat";
import { callApi } from "new-redux/services/chat.services";
import { setSearchMessageTerm } from "new-redux/actions/chat.actions/Input";
import { ConnectedUsersPopover } from "components/Chat/ConnectedUsersPopover";
import { FiBellOff } from "react-icons/fi";
import { BiPoll } from "react-icons/bi";
import { BsCommand } from "react-icons/bs";
import { HiOutlineCalendar } from "react-icons/hi";
import { lazyRetry } from "utils/lazyRetry";
import { getName } from "../../utils/ConversationUtils";
import useGetInfoDiscussion from "../../hooks/useGetInfoDiscussion";
import { URL_ENV } from "index";
import { setOpenTaskDrawer } from "new-redux/actions/tasks.actions/handleTaskDrawer";
import { HiOutlineVideoCamera } from "react-icons/hi2";
import { isGuestConnected } from "utils/role";
import { Refs_IDs } from "components/tour/tourConfig";
import { setEmailFields } from "pages/rmc/mailing/main-components/email-composer-modal/utils";

const { Text } = Typography;

const ShareVisio = lazy(() =>
  lazyRetry(() => import("../../../visio/components/share-visio"), "ShareVisio")
);

const Header = ({ source }) => {
  const { t } = useTranslation("common");
  const dispatch = useDispatch();
  const [clicked, setClicked] = useState(false);
  const openDrawer = useSelector((state) => state.chat?.openDrawer);
  const param = useSelector((state) => state.voip.param);

  const { user } = useSelector((state) => state.user);
  const currentUser = useSelector((state) => state.chat?.currentUser);
  const [openQuickVideoCall, setOpenQuickVideoCall] = useState(false);
  // const [data, setDataInfo] = useState(null);

  const onlineUser = useSelector((state) => state.ChatRealTime.onlineUser);
  const selectedConversation = useSelector(
    (state) => state.ChatRealTime.selectedConversation
  );
  const { status, data } = useGetInfoDiscussion();

  const connectedUser = useMemo(() => {
    let users = 0;
    if (selectedConversation?.type === "room")
      data?.data?.room_info?.participants?.forEach((element) => {
        if (onlineUser[element.uuid] === "online") users++;
      });
    return users;
  }, [
    selectedConversation?.type,
    data?.data?.room_info?.participants,
    onlineUser,
  ]);

  const itemsGroupe = useMemo(
    () => [
      {
        key: "dropdown1",
        label: t("chat.header.visio.createVideoConferance"),
        icon: <PlayCircleOutlined style={{ fontSize: "100%" }} />,
        onClick: () =>
          user &&
          user?.access &&
          user?.access["visio"] === "1" &&
          setOpenQuickVideoCall((p) => !p),
      },
      {
        disabled:
          (user && user?.access && user?.access["activities"] === "0") ||
          source === "external",
        key: "dropdown2",
        label: t("chat.header.createVideoLater"),
        icon: <HiOutlineCalendar style={{ fontSize: "100%" }} />,
        onClick: () => {
          user &&
            user?.access &&
            user?.access["activities"] === "1" &&
            dispatch(setOpenTaskDrawer(true));
        },
      },
    ],
    [t, user]
  );

  return (
    <div
      className={`flex w-full items-center justify-between ${
        source !== "external" && "px-4 py-3 shadow-sm"
      }  `}
    >
      <Space>
        <Badge
          dot={selectedConversation?.type === "user"}
          color={
            onlineUser[selectedConversation?.uuid] === "away"
              ? "orange"
              : onlineUser[selectedConversation?.uuid] === "busy"
              ? "red"
              : onlineUser[selectedConversation?.uuid] === "online"
              ? "green"
              : "#a6a6a6"
          }
          offset={[-4, 40]}
          style={{ width: "8px", height: "8px" }}
        >
          <AvatarChat
            isPublic={
              selectedConversation?.type === "room" &&
              [2, 5, 6].includes(Number(selectedConversation?.predefined))
            }
            roomPro={selectedConversation?.predefined}
            type={selectedConversation?.type}
            fontBold="font-semibold"
            hasImage={selectedConversation?.image}
            height={12}
            width={12}
            size={52}
            url={
              selectedConversation?.type === "room"
                ? URL_ENV?.REACT_APP_OAUTH_CHAT_API +
                  process.env.REACT_APP_SUFFIX_CHAT_GROUP_STORAGE +
                  selectedConversation?.image
                : URL_ENV?.REACT_APP_BASE_URL +
                  URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                  selectedConversation?.image
            }
            name={getName(selectedConversation?.name, "avatar")}
          />
        </Badge>
        <div className="flex flex-col ">
          <div className="flex items-center">
            <Text
              onClick={() =>
                dispatch(
                  setOpenDrawer({
                    type: openDrawer?.type === "info" ? "" : "info",
                  })
                )
              }
              className="px-1   text-lg font-medium text-slate-800 first-letter:capitalize hover:cursor-pointer hover:underline "
            >
              {getName(selectedConversation?.name, "name")}
            </Text>
            {selectedConversation?.muted_status && (
              <FiBellOff className="time ml-0.5" style={{ fontSize: "13px" }} />
            )}
          </div>

          {selectedConversation?.type === "room" ? (
            <Space size={12}>
              {status === "success" && (
                <Button
                  disabled={source === "external"}
                  type="text"
                  className="p-1"
                  onClick={() =>
                    dispatch(
                      setOpenDrawer({
                        type: openDrawer?.type === "info" ? "" : "info",
                      })
                    )
                  }
                >
                  <div
                    style={{ color: "rgba(0, 0, 0, 0.45)" }}
                    className="space-x-1 "
                  >
                    {" "}
                    <TeamOutlined />
                    <span>{data?.data?.room_info?.participants?.length}</span>
                    <span>{t("chat.header.members")}</span>
                  </div>
                </Button>
              )}
              {status === "success" && (
                <ConnectedUsersPopover source="chat">
                  <Button
                    type="text"
                    className="space-x-1 transition duration-300"
                    style={{ color: "rgba(0, 0, 0, 0.45)" }}
                  >
                    <Badge status="success" />
                    <span>{connectedUser}</span>
                    <span>{t("chat.connected")}</span>
                  </Button>
                </ConnectedUsersPopover>
              )}
            </Space>
          ) : (
            <Space size={4}>
              <Tooltip title={t("mailing.newMail")}>
                <Button
                  disabled={user?.access["email"] === "0"}
                  type="text"
                  className="p-1"
                  icon={<MailOutlined className="text-black/40" />}
                  onClick={() => {
                    if (user?.access["email"] === "1") {
                      dispatch(
                        setEmailFields({
                          sender: user.email,
                          receivers: [selectedConversation.email],
                        })
                      );
                    }
                  }}
                >
                  <Text type="secondary  ">{selectedConversation?.email}</Text>
                </Button>
              </Tooltip>
              <Tooltip title={t("chat.action.call")}>
                <Button
                  disabled={clicked || !param}
                  className="p-1"
                  icon={<PhoneOutlined className="text-black/40" />}
                  onClick={() =>
                    dispatch(
                      callApi({
                        setClicked: setClicked,
                        post_numberR: selectedConversation?.post_number,
                        errorText: t("toasts.errorFetchApi"),
                      })
                    )
                  }
                  type="text"
                >
                  <Text type="secondary">
                    {selectedConversation?.post_number}
                  </Text>
                </Button>
              </Tooltip>
            </Space>
          )}
        </div>
      </Space>

      <Space size={4}>
        {selectedConversation?.type === "user" && source !== "external" && (
          <Tooltip title={t("chat.action.call_audio")}>
            <Button
              ref={Refs_IDs.audioCallChat}
              onClick={() =>
                dispatch(
                  callApi({
                    setClicked: setClicked,
                    post_numberR: selectedConversation?.post_number,
                    errorText: t("toasts.errorFetchApi"),
                  })
                )
              }
              loading={clicked}
              disabled={clicked || !param}
              type="text"
              shape="circle"
              className="text-slate-500"
              icon={<PhoneOutlined />}
            />
          </Tooltip>
        )}

        <Tooltip
          placement={selectedConversation?.type === "room" ? "left" : "bottom"}
          title={
            t("chat.action.create_conferance")
            // selectedConversation?.type === "room"
            //   ? t("chat.action.create_conferance")
            //   : t("chat.action.call_video")
          }
        >
          {selectedConversation?.type === "room" ? (
            <Dropdown
              disabled={
                (user &&
                  user?.access &&
                  user?.access["visio"] === "0" &&
                  selectedConversation?.type === "room") ||
                isGuestConnected(currentUser?.role, user?.role)
              }
              arrow={true}
              trigger={["click"]}
              menu={{
                items: itemsGroupe,
              }}
              placement="bottomRight"
            >
              <Button
                ref={Refs_IDs.visioCallChat}
                disabled={
                  user &&
                  user?.access &&
                  user?.access["visio"] === "0" &&
                  selectedConversation?.type === "room"
                }
                icon={
                  <HiOutlineVideoCamera className="text-lg text-slate-500" />
                }
                type="text"
                shape="circle"
                className="text-slate-500"
              />
            </Dropdown>
          ) : (
            <Button
              ref={Refs_IDs.visioCallChat}
              shape="circle"
              type="text"
              onClick={() =>
                user &&
                user?.access &&
                user?.access["visio"] === "1" &&
                setOpenQuickVideoCall((p) => !p)
              }
              disabled={
                user && user?.access && user?.access["activities"] === "0"
              }
              icon={<HiOutlineVideoCamera className="text-lg text-slate-500" />}
            ></Button>
          )}
        </Tooltip>

        <Divider type="vertical" />

        {data?.data?.total_incomplete_poll > 0 && source !== "external" && (
          <Tooltip
            className="flex items-center"
            title={t("chat.polls.incomplete_list")}
          >
            <Badge count={data?.data?.total_incomplete_poll}>
              <Button
                type="text"
                shape="circle"
                className="text-slate-500"
                icon={<BiPoll size={16} className="mt-1" />}
                onClick={() => {
                  dispatch(
                    setOpenDrawer({
                      type: "polls_2",
                      external: {
                        _id: selectedConversation?.conversationId,
                        contact:
                          selectedConversation?.type === "room"
                            ? null
                            : {
                                _id: selectedConversation?.id,
                                name: selectedConversation?.name,
                                email: selectedConversation?.email,
                                post_number: selectedConversation?.post_number,
                                image: selectedConversation?.image,
                              },
                        bot: !selectedConversation?.bot
                          ? null
                          : {
                              _id: selectedConversation?.bot?._id,
                              name: selectedConversation?.bot?.name,
                              logo: selectedConversation?.bot?.logo,
                              status: selectedConversation?.bot?.status,
                            },
                        room:
                          selectedConversation?.type === "user"
                            ? null
                            : {
                                _id: selectedConversation?.id,
                                name: selectedConversation?.name,
                                image: selectedConversation?.image,
                                description: selectedConversation?.description,
                                admin_id: selectedConversation?.admin_id,
                                predefined: selectedConversation?.predefined,
                              },
                        muted_status: selectedConversation?.muted_status,
                      },
                    })
                  );
                  dispatch(setSearchList({ list: [], page: 1 }));
                  dispatch(setSearchMessageTerm({ value: "", forced: false }));
                }}
              />
            </Badge>
          </Tooltip>
        )}
        {data?.data?.total_important > 0 && source !== "external" && (
          <Tooltip title={t("chat.action.list_pinned")}>
            <Badge count={data?.data?.total_important}>
              <Button
                type="text"
                shape="circle"
                className="text-slate-500"
                icon={<PushpinOutlined />}
                onClick={() => {
                  dispatch(setOpenDrawer({ type: "pinned" }));
                  dispatch(setSearchList({ list: [], page: 1 }));
                  dispatch(setSearchMessageTerm({ value: "", forced: false }));
                }}
              />
            </Badge>
          </Tooltip>
        )}
        {data?.data?.total_favoris > 0 && source !== "external" && (
          <Tooltip
            className="flex items-center"
            title={t("chat.action.list_starred")}
          >
            <Badge count={data?.data?.total_favoris}>
              <Button
                type="text"
                shape="circle"
                className="text-slate-500"
                icon={<StarOutlined />}
                onClick={() => {
                  dispatch(setOpenDrawer({ type: "starred" }));
                  dispatch(setSearchList({ list: [], page: 1 }));
                  dispatch(setSearchMessageTerm({ value: "", forced: false }));
                }}
              />
            </Badge>
          </Tooltip>
        )}

        <Tooltip
          title={
            <div className="flex items-center gap-x-1 ">
              <span>{t("chat.searchSide.searchIcon")}</span>
              {navigator.userAgent.indexOf("Macintosh") !== -1 ? (
                <div className="flex items-center">
                  <BsCommand /> <span>G</span>
                </div>
              ) : (
                "Ctrl + G"
              )}
            </div>
          }
        >
          <Button
            style={{ marginRight: source === "external" && "2rem" }}
            onClick={() => {
              dispatch(setOpenDrawer({ type: "search" }));
              dispatch(setSearchList({ list: [], page: 1 }));
              dispatch(setSearchMessageTerm({ value: "", forced: false }));
            }}
            type="text"
            shape="circle"
            className="text-slate-500"
            icon={<SearchOutlined />}
          />
        </Tooltip>

        {source !== "external" && <Divider type="vertical"></Divider>}
        {source !== "external" && (
          <span className="transition-all duration-300 group-hover:block group-hover:px-1">
            <Suspense fallback={<Loader size={20} />}>
              {" "}
              <DropDownGroupIndex
                source="chat"
                item={{
                  _id: selectedConversation?.conversationId,
                  contact:
                    selectedConversation?.type === "room"
                      ? null
                      : {
                          _id: selectedConversation?.id,
                          name: selectedConversation?.name,
                          email: selectedConversation?.email,
                          post_number: selectedConversation?.post_number,
                          image: selectedConversation?.image,
                        },
                  bot: !selectedConversation?.bot
                    ? null
                    : {
                        _id: selectedConversation?.bot?._id,
                        name: selectedConversation?.bot?.name,
                        logo: selectedConversation?.bot?.logo,
                        status: selectedConversation?.bot?.status,
                      },
                  room:
                    selectedConversation?.type === "user"
                      ? null
                      : {
                          _id: selectedConversation?.id,
                          name: selectedConversation?.name,
                          image: selectedConversation?.image,
                          description: selectedConversation?.description,
                          admin_id: selectedConversation?.admin_id,
                          predefined: selectedConversation?.predefined,
                        },
                  muted_status: selectedConversation?.muted_status,
                }}
              />
            </Suspense>
          </span>
        )}
      </Space>

      {openQuickVideoCall && (
        <Suspense
          fallback={
            <div className="fixed left-[50%] top-[50%]">
              <Loader size={20} />
            </div>
          }
        >
          {" "}
          <ShareVisio
            open={openQuickVideoCall}
            onClose={() => setOpenQuickVideoCall(false)}
          />
        </Suspense>
      )}
    </div>
  );
};

export default Header;
