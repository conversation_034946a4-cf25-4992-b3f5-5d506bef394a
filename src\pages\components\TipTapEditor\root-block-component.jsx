import { <PERSON>de<PERSON><PERSON>w<PERSON>ontent, NodeView<PERSON>rapper } from "@tiptap/react";
import { MenuBar } from "./Editorr";
import { <PERSON><PERSON>, Toolt<PERSON> } from "antd";
import { useEffect, useMemo, useRef } from "react";
import useOnClickOutside from "../../layouts/chat/hooks/useClickOutside";
import { RxDragHandleDots2, RxPlus } from "react-icons/rx";
import { useSelector } from "react-redux";

// Define the RootBlockComponent, which will serve as a custom Node View in the Tiptap editor
const RootBlockComponent = ({ node, getPos, editor }) => {
  const testRef = useRef();

  const selectedSelfNote = useSelector(
    (state) => state?.selfNotesReducer?.selectedNote
  );

  const currentUser = useSelector((state) => state?.user?.user);

  // Function to create a new node immediately after the current node
  const createNodeAfter = (e) => {
    // Calculate the position right after the current node
    const pos = getPos() + node.nodeSize;

    // Use the editor's command to insert a new "rootblock" node at the calculated position

    editor
      .chain()
      .insertContentAt(pos, "/")
      .focus(pos + 3) // Focus on the new block (you might need to adjust the position based on your exact requirements)
      .run();
  };

  //  useOnClickOutside(testRef, undefined, () => {
  // alert("ok")  });
  // Render the custom node view
  return (
    <NodeViewWrapper
      as="div"
      className="group relative mx-auto flex w-full gap-2"
    >
      <div className="relative my-1.5  flex w-full items-center">
        {/* Container for buttons that appear on hover */}
        <div
          className="absolute left-5 top-1/2 flex w-12 -translate-x-1/2
          -translate-y-1/2 transform gap-1 opacity-0 transition-opacity duration-300 ease-in-out group-hover:opacity-100"
          aria-label="left-menu"
        >
          {/* Button to add a new node after the current node */}
          {(selectedSelfNote?.permission == 1 ||
            selectedSelfNote?.isNew ||
            selectedSelfNote?.user == currentUser?.id) && (
            <>
              <Button
                shape="circle"
                type="text"
                onClick={createNodeAfter}
                className="flex items-center justify-center"
              >
                <RxPlus className="h-4 w-4" />
              </Button>
              {/* Draggable handle button to allow rearranging nodes */}
              <Button
                shape="circle"
                draggable
                data-drag-handle
                type="text"
                className="flex cursor-grab items-center justify-center"
              >
                <RxDragHandleDots2 className="h-4 w-4" />
              </Button>
            </>
          )}
        </div>
        {/* Area where the node's actual content will be rendered */}
        <NodeViewContent className="ediorNotionLike ml-20 w-full gap-10" />
        {/* test */}
      </div>
    </NodeViewWrapper>
  );
};

export default RootBlockComponent;
