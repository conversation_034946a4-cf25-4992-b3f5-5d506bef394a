/* Extend the clickable area around the checkbox */
.mailing-custom-row mark {
  background-color: yellow;
  color: inherit;
}
.mailing .ant-table-selection-column .ant-checkbox-wrapper {
  position: relative; /* Needed for absolute positioning of the background */
  padding: 4px; /* Increase padding to make the clickable area bigger */
  z-index: 1;
}

/* Rounded gray background on hover */
.mailing .ant-table-selection-column .ant-checkbox-wrapper:hover::before {
  content: ""; /* Pseudo-element to create the background */
  position: absolute;
  top: 50%;
  left: 50%;
  width: 35px; /* Adjust width for the gray background */
  height: 35px; /* Adjust height for the gray background */
  background-color: rgba(226, 224, 224, 0.895);
  border-radius: 50%; /* Rounded corners */
  transform: translate(
    -50%,
    -50%
  ); /* Center the background behind the checkbox */
  z-index: -1; /* Ensure background is behind the checkbox */
  pointer-events: none; /* Make sure the background doesn't interfere with clicks */
}
.small-text {
  font-size: 0.75rem;
}

.custom-card-height .ant-card .ant-card-body {
  padding: 4.5px;
}

.unread {
  background-color: #efefef; /* Light gray for read messages */
  font-weight: bold;
}

.read-row .ant-table-cell-fix-left,
.read-row .ant-table-cell-fix-right {
  background-color: #f2f6fc;
}

.read-row {
  background-color: #f2f6fc;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis {
  text-overflow: ellipsis;
}

.whitespace-nowrap {
  white-space: nowrap;
}

/* .ant-table-wrapper .ant-table.ant-table-small .ant-table-tbody>tr>td, :where(.css-dev-only-do-not-override-107yv8j).ant-table-wrapper .ant-table.ant-table-small tfoot>tr>th, :where(.css-dev-only-do-not-override-107yv8j).ant-table-wrapper .ant-table.ant-table-small tfoot>tr>td{
  padding: 3px 3px;
} */

.mailing.ant-table-wrapper
  .ant-table.ant-table-small
  .ant-table-tbody
  > tr
  > td {
  padding: 6px 6px;
}

.mailing .ant-table .ant-table-thead > tr > th {
  padding: 2px 0 0 6px !important; /* Reduce padding */
  height: 32px !important; /* Fix the height */
  line-height: 1 !important; /* Adjust line height */
  font-size: 12px !important; /* Optional: Reduce font size */
}

/* Remove any extra spacing or margin */
.mailing .ant-table .ant-table-thead {
  margin: 0 !important;
  padding: 0 !important;
  border-bottom: 1px solid #ddd; /* Optional: Add bottom border for clarity */
}


.quill_editor_modal_mailing .ql-container.ql-snow {
  max-height: var(--editor-modal-mailing-max-height);
  overflow-y: auto;
}