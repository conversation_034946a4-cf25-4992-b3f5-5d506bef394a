import React from "react";

const ZipIcon = ({ width = "50px", height = "50px", ...props }) => {
    return (
        <svg 
            xmlns="http://www.w3.org/2000/svg" 
            xmlnsXlink="http://www.w3.org/1999/xlink" 
            fill="#000000" 
            version="1.1" 
            id="Capa_1" 
            width={width} 
            height={height} 
            viewBox="0 0 588.601 588.6" 
            xmlSpace="preserve"
            {...props}
        >
            <g>
                <path d="M359.031,537.78c0.781,0.042,1.551,0.111,2.342,0.111h178.2c20.846,0,37.8-16.96,37.8-37.801V86.994   c0-20.838-16.954-37.8-37.8-37.8h-178.2c-0.786,0-1.561,0.077-2.342,0.113V0L11.228,46.417v494.564L359.031,588.6V537.78z    M359.031,71.036c0.771-0.113,1.54-0.237,2.342-0.237h178.2c8.933,0,16.2,7.27,16.2,16.2v413.103c0,8.928-7.268,16.2-16.2,16.2   h-178.2c-0.796,0-1.571-0.127-2.342-0.242V71.036z M145.257,348.828l-82.927-2.115v-13.483l50.055-76.773v-0.681l-45.467,0.751   v-20.764l77.527-2.012v15.021L94.031,325.83v0.665l51.226,0.807V348.828z M186.97,349.893l-25.784-0.66V233.318l25.784-0.67   V349.893z M285.544,296.325c-9.608,8.839-23.712,12.709-39.962,12.594c-3.573-0.021-6.803-0.228-9.292-0.602v42.831l-26.568-0.675   V233.634c8.208-1.619,19.818-2.979,36.395-3.417c16.999-0.448,29.268,2.483,37.568,8.801c8.003,6.012,13.424,16.116,13.424,28.15   C297.108,279.208,293,289.422,285.544,296.325z"/>
                <path d="M247.554,250.715c-5.545,0.098-9.305,0.696-11.264,1.266v35.363c2.31,0.53,5.168,0.707,9.115,0.696   c14.628-0.021,23.739-7.425,23.739-19.865C269.144,257.006,261.291,250.472,247.554,250.715z"/>
                <rect x="424.043" y="80.038" width="42.366" height="17.474"/>
                <rect x="381.676" y="106.197" width="42.367" height="17.484"/>
                <rect x="424.043" y="134.815" width="42.366" height="17.487"/>
                <rect x="424.043" y="190.057" width="42.366" height="17.479"/>
                <rect x="381.676" y="161.99" width="42.367" height="17.479"/>
                <path d="M423.626,378.707c23.172,0,41.939-18.784,41.939-41.966l-7.736-70.284c0-23.172-11.031-41.958-34.203-41.958   s-34.193,18.786-34.193,41.958l-7.757,70.284C381.676,359.913,400.454,378.707,423.626,378.707z M409.752,301.593h27.774v58.672   h-27.774V301.593z"/>
            </g>
        </svg>
    );
};

export default ZipIcon;