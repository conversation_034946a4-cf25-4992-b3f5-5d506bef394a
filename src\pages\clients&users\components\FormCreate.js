import { memo, useCallback, useEffect, useRef, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { setCreateForm } from "../../../new-redux/actions/form.actions/form";
import { useSelector } from "react-redux";
import {
  Form,
  Drawer,
  Space,
  Button,
  Dropdown,
  Tabs,
  Tooltip,
  Typography,
} from "antd";
import { UpOutlined } from "@ant-design/icons";
//
import {
  getDataToConvert,
  getFieldsToCreate,
  storeNewData,
} from "../services/services";
import { toastNotification } from "../../../components/ToastNotification";
import { FormWithTabsLoader } from "./SkeletonLoader";
import { formattingFieldsForm, formattingDataByType } from "../helpers";
import { useWindowSize } from "./WindowSize";
import DisplayDynamicFieldsByType from "./DisplayDynamicFieldsByType";
import "../index.css";
import { useTranslation } from "react-i18next";
import { getLogs } from "new-redux/actions/voip.actions/getLogs";
import {
  findNextZIndex,
  generateUrlToView360,
} from "pages/voip/helpers/helpersFunc";
import { getFamilyNameById, getSettingPath } from "../FamilyRouting";
import { RESET_ADD_NEW_ELEMENT_FAMILY } from "new-redux/constants";
import { setNewInteraction } from "new-redux/actions/vue360.actions/vue360";
import SharedWithField from "./special_fields/SharedWithField";
import { postAffectations } from "pages/voip/services/services";
import { setRefreshMailInbox } from "new-redux/actions/mail.actions";
import { isGuestConnected } from "utils/role";
const FormCreate = ({
  open,
  setOpen,
  familyId,
  setCatchChange = () => {},
  nbrPhoneFromVoip,
  mailingProps,
  rmcData,
  selectedPipeline,
  idRelation,
  selectedStageId,
  setSelectedStageId = () => {},
  source,
  externalSource = {},
  setExternalSource = () => {},
  isViewOnly,
}) => {
  //
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [t] = useTranslation("common");
  const location = useLocation();
  const windowSize = useWindowSize();
  const [form] = Form.useForm();
  const scrollRef = useRef(null);
  const updatedFields = useRef({});
  //
  const currentUser = useSelector((state) => state.user.user);
  const callsInProcess = useSelector((state) => state.voip.callsInProcess);
  //
  const [fieldsToDisplay, setFieldsToDisplay] = useState([]);
  const [checkIfRequired, setCheckIfRequired] = useState({});
  const [config, setConfig] = useState({});
  const [matchFieldsAndType, setMatchFieldsAndType] = useState({});
  const [matchFieldsIdAndGroupId, setMatchFieldsIdAndGroupId] = useState(null);
  // const [updatedFields, setUpdatedFields] = useState({});
  const [methodSubmit, setMethodSubmit] = useState("");
  const [loadingSubmit, setLoadingSubmit] = useState(false);
  const [submitIsDisable, setSubmitIsDisable] = useState(true);
  //
  const [selectedTab, setSelectedTab] = useState(null);
  const [resetFieldsAfterSubmit, setResetFieldsAfterSubmit] = useState(false);
  //
  // console.log({ externalSource });
  //
  const getAllFieldForm = useCallback(async () => {
    if (!familyId) {
      setOpen(false);
      dispatch({ type: RESET_ADD_NEW_ELEMENT_FAMILY });
      toastNotification("error", "Missing FamilyId", "topRight", 7);
      return;
    }
    try {
      form.resetFields();
      const fields = await getFieldsToCreate(
        familyId,
        "create",
        null,
        null,
        selectedStageId
      );
      if (!fields?.data?.data?.length) {
        toastNotification(
          "warning",
          "Il n'y a pas de champs, veuillez d'abord créer des champs dans la configuration. ",
          "topRight",
          7
        );
        setOpen(false);
        dispatch({ type: RESET_ADD_NEW_ELEMENT_FAMILY });
        return;
      }
      setConfig(fields?.data?.config);
      let fieldsValues = null;
      if (externalSource?.source) {
        fieldsValues = await getDataToConvert(
          externalSource?.familyId,
          externalSource?.id,
          externalSource?.source,
          externalSource?.msg
        );
        fieldsValues = fieldsValues?.data?.data ?? null;
        // console.log({ fieldsValues });
      }
      const { result, matchF_G } = await formattingFieldsForm(
        fields?.data?.data,
        setMatchFieldsAndType,
        setSelectedTab,
        fieldsValues ? fieldsValues : rmcData ? rmcData : null,
        form,
        setCheckIfRequired,
        nbrPhoneFromVoip,
        mailingProps,
        fields?.data?.config,
        null,
        selectedPipeline,
        familyId,
        selectedStageId
      );
      setFieldsToDisplay(result);
      setMatchFieldsIdAndGroupId(matchF_G);
      setResetFieldsAfterSubmit(false);
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      setOpen(false);
      throw new Error(err?.message ? err.message : err);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    familyId,
    form,
    mailingProps,
    nbrPhoneFromVoip,
    rmcData,
    selectedPipeline,
    t,
    selectedStageId,
  ]);

  useEffect(() => {
    open && getAllFieldForm();
  }, [getAllFieldForm, open]);
  //
  const handleCancelClick = (supAction) => {
    // to get the the id of the first Group
    if (matchFieldsIdAndGroupId instanceof Map) {
      var firstKey = matchFieldsIdAndGroupId?.keys()?.next()?.value;
      var firstValue = matchFieldsIdAndGroupId?.get(firstKey);
    }
    // if (supAction === "resetFields") {
    setResetFieldsAfterSubmit(true);
    setSubmitIsDisable(true);
    setSelectedTab(firstValue);
    setFieldsToDisplay([]);
    form.resetFields();
    form.resetFields();
    setSelectedStageId(null);
    // }
    dispatch(setCreateForm(false));
    dispatch({ type: "RMC_RESET_STATE_CREATE_FORM" });
    setOpen && setOpen(false);
  };
  //
  const handleSelectedTab = (activeKey) => {
    setSelectedTab(activeKey);
  };
  //
  const handleChangedFields = (changedFields, allFields) => {
    submitIsDisable && setSubmitIsDisable(false);
    const field_id = changedFields?.[0]?.name?.[0];
    const value = changedFields?.[0]?.value;
    updatedFields.current = { ...updatedFields, [field_id]: value };
    // setUpdatedFields({ ...updatedFields, [field_id]: value });
  };
  //
  const onFinish = async (values) => {
    try {
      setLoadingSubmit(true);
      const checkIfMissingFields = checkIfMissingFieldsRequired();
      if (checkIfMissingFields) return;
      const formData = new FormData();
      idRelation && formData.append("relation_id", idRelation);
      formData.append("family_id", familyId);
      formData.append("import_type_id", 3);
      selectedStageId && formData.append("stage_id", selectedStageId);
      methodSubmit.includes("invite") && formData.append("invite", 1);
      mailingProps?.idEmail &&
        formData.append("id_email", mailingProps?.idEmail);
      // shared with
      if (externalSource?.source === "chat")
        formData.append("message_id", externalSource.id);
      if (familyId !== 4 && !isGuestConnected()) {
        if (!values?.sharedWith) {
          toastNotification("error", t("voip.errorSharedWith"), "topRight", 5);
          return;
        } else if (!isGuestConnected())
          formData.append("shared_with", values?.sharedWith);
      }
      for (const key of Object.keys(values)) {
        const value = values[key];

        if (
          key !== "confirmPassword" &&
          value &&
          value !== "" &&
          !key?.includes("filename") &&
          !key?.includes("isActive")
        ) {
          const type = matchFieldsAndType[key];
          if (type === "file") {
            value?.fileList?.forEach((file) =>
              formData.append(`field[${key}][]`, file?.originFileObj)
            );
            formData.append(
              `filename[${key}]`,
              values?.[`${key}filename`] ? values?.[`${key}filename`] : ""
            );
            formData.append(`isActive[${key}]`, values?.[`${key}isActive`]);
          } else
            formattingDataByType.create(type, key, value, formData, config);
        }
      }
      const create = await storeNewData(formData);
      if (create.status === 200) {
        if (nbrPhoneFromVoip) dispatch(getLogs(true));
        if (source === "viewSphere") {
          dispatch(setNewInteraction({ type: "associateElement" }));
        }

        const { _id, label_data } = create?.data || {};

        if (externalSource?.source && externalSource?.source !== "chat") {
          try {
            const formDataEmail = new FormData();
            formDataEmail.append(
              "element_id",
              externalSource?.callInProcess ? null : externalSource?.id
            );
            formDataEmail.append("type", externalSource?.source);
            formDataEmail.append("affect_to", _id);
            formDataEmail.append("affected_family_id", familyId);
            if (externalSource.callInProcess && externalSource.callNumber) {
              const number = externalSource.callNumber;
              const calId = callsInProcess.find(
                (call) => call.dst === number || call.src === number
              );
              if (calId) {
                formDataEmail.append("affectation_type", "in_progress_call");
                formDataEmail.append("call_id", calId?.id_appel);
              }
            }

            if (externalSource?.source === "email") {
              formDataEmail.append(
                "account_id",
                externalSource?.usedAccount?.value
              );
              for (
                let i = 0;
                i < externalSource?.usedAccount?.departmentId?.length;
                i++
              ) {
                formDataEmail.append(
                  "departement_id[]",
                  externalSource?.usedAccount?.departmentId[i]
                );
              }
            }
            const response = await postAffectations(formDataEmail);
            if (response?.status === 200) {
              dispatch(setRefreshMailInbox(true));
              setExternalSource({});
              dispatch(getLogs(true));
            }
          } catch (e) {
            toastNotification("error", t("toasts.somethingWrong"), "topRight");
          }
        }

        toastNotification(
          "success",
          // `${getFamilyNameById(t, familyId)} ${t("contacts.successCreate")}`,
          `[${label_data}] ${t("contacts.successCreate")}`,
          "topRight"
        );

        setResetFieldsAfterSubmit(true);
        form.resetFields();
        setSubmitIsDisable(true);
        //
        // if (methodSubmit.includes("invite")) {
        //   await sendInvitation(t, _id, label_data, null, null, false);
        // }
        if (methodSubmit.includes("add_another")) {
          var firstKey = matchFieldsIdAndGroupId?.keys()?.next()?.value;
          var firstValue = matchFieldsIdAndGroupId?.get(firstKey);
          setSelectedTab(firstValue);
          if (scrollRef.current) {
            scrollRef.current.scrollTo({ top: 0, behavior: "smooth" });
          }
        } else if (methodSubmit.includes("go_to_detail")) {
          navigate(generateUrlToView360(familyId, _id, "v2"));
        } else if (!methodSubmit.includes("add_another")) {
          dispatch({ type: RESET_ADD_NEW_ELEMENT_FAMILY });
          dispatch({ type: "RMC_RESET_STATE_CREATE_FORM" });
          setOpen && setOpen(false);
        }
        setMethodSubmit("");
        setCatchChange((previewsState) => !previewsState);
      }
    } catch (err) {
      if (err?.response?.status === 422) {
        toastNotification(
          "error",
          err?.response?.data?.message || err?.response?.message,
          "topRight",
          7
        );
      } else if (err?.response?.status === 403) {
        toastNotification("error", t("contacts.errLimitLicense"), "topRight");
      } else {
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
      throw new Error(err?.message ? err.message : err);
    } finally {
      setLoadingSubmit(false);
    }
  };
  //
  const onFinishFailed = (errorInfo) => {
    // console.log(errorInfo, errorInfo.errorFields[0].name)
    setSubmitIsDisable(true);
    const [fieldId] = errorInfo.errorFields[0].name;
    const groupId = matchFieldsIdAndGroupId?.get(fieldId);
    setSelectedTab(groupId);
    form.scrollToField(errorInfo.errorFields[0].name);
    form.scrollToField(errorInfo.errorFields[0].name);
    toastNotification(
      "error",
      `${errorInfo.errorFields[0].errors[0]}`,
      "topRight",
      3
    );
  };
  //
  const checkIfMissingFieldsRequired = () => {
    for (const [key, value] of Object.entries(checkIfRequired)) {
      if (!updatedFields.current[key] || updatedFields.current[key] === "") {
        if (!form.getFieldValue(key)) {
          setSubmitIsDisable(true);
          setSelectedTab(value["groupId"]);
          form.scrollToField(Number(key));
          form.scrollToField(Number(key));
          form.setFields([
            {
              name: Number(key),
              errors: [`The "${value["alias"]}" field is required!`],
            },
          ]);
          toastNotification(
            "error",
            `The "${value["alias"]}" field is required!`,
            "topRight",
            3
          );
          return true;
        }
      }
    }
    return false;
  };
  //
  const menuProps = {
    items: [
      {
        label: t("contacts.createAndAddAnother"),
        key: "create_&_add_another",
      },
      {
        label: t("contacts.createGoDetail"),
        key: "create_&_go_to_detail",
      },
      ...(familyId === 4
        ? [
            {
              label: t("contacts.createAndInvite"),
              key: "create_&_invite",
            },
            {
              label: t("contacts.createAndInviteAndAddAnother"),
              key: "create_&_invite_&_add_another",
            },
            {
              label: "Create & Invite & Go to view sphere",
              key: "create_&_invite_&_go_to_detail",
            },
          ]
        : []),
    ],
    onClick: (e) => {
      setMethodSubmit(e.key);
      form.submit();
    },
  };
  //
  const DrawerProps = {
    title: t(`contacts.${familyId === 1 ? "createNewXf" : "createNewX"}`, {
      x: getFamilyNameById(t, familyId),
    }),
    /////////
    // extra: (
    //   <SharedWithField />
    //   // <Typography.Link
    //   //   underline
    //   //   onClick={() => {
    //   //     window.open(`${getSettingPath(familyId)}`, "_blank");
    //   //   }}
    //   // >
    //   //   {t("contacts.goToXSetting", { x: getFamilyNameById(t, familyId) })}
    //   // </Typography.Link>
    // ),
    /////////
    footer: !isViewOnly && (
      <div className="flex flex-row items-center justify-between">
        <Space
          direction="horizontal"
          // size="middle"
          style={{ padding: "0.250rem" }}
        >
          <Button onClick={() => handleCancelClick("resetFields")}>
            {t("contacts.cancel")}
          </Button>
          <Dropdown.Button
            type="primary"
            disabled={submitIsDisable}
            loading={loadingSubmit}
            trigger={["click"]}
            placement="topLeft"
            icon={<UpOutlined />}
            menu={menuProps}
            onClick={() => {
              setMethodSubmit("create");
              form.submit();
            }}
          >
            {t("contacts.create")}
          </Dropdown.Button>
        </Space>

        {!isGuestConnected() && (
          <Typography.Link
            underline
            onClick={() => {
              window.open(`${getSettingPath(familyId)}`, "_blank");
            }}
          >
            {/* {t("contacts.goToXSetting", { x: getFamilyNameById(t, familyId) })} */}
            {t("contacts.fieldSetting")}
          </Typography.Link>
        )}
      </div>
    ),
  };
  //
  // Clear state when pathName change
  useEffect(() => {
    setFieldsToDisplay([]);
    setCheckIfRequired({});
    setConfig({});
    setMatchFieldsAndType({});
    setMatchFieldsIdAndGroupId(null);
    // setUpdatedFields({});
    updatedFields.current = {};
    setMethodSubmit("");
    setLoadingSubmit(false);
    setSubmitIsDisable(true);
    setSelectedTab(null);
    setSelectedStageId(null);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location?.pathname]);
  //
  return (
    <Drawer
      title={DrawerProps.title}
      placement="right"
      width={windowSize?.width / 2.5 < 600 ? 600 : windowSize?.width / 2.5}
      open={open}
      // closeIcon={DrawerProps.closeIcon}
      onClose={handleCancelClick}
      footer={DrawerProps.footer}
      extra={DrawerProps.extra}
      zIndex={findNextZIndex()}
    >
      {fieldsToDisplay?.length ? (
        <div className="flex-column-container ">
          <Form
            form={form}
            layout={"vertical"}
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
            autoComplete="off"
            onFieldsChange={handleChangedFields}
          >
            <Tabs
              style={{ marginRight: "-23px" }}
              type="card"
              tabBarGutter={8}
              tabPosition="left"
              activeKey={selectedTab}
              onChange={handleSelectedTab}
              items={fieldsToDisplay?.map((group) => {
                return {
                  label: (
                    <Tooltip title={group?.group_name} placement="leftBottom">
                      <div className="tooltip-container">
                        {group?.group_name}
                      </div>
                    </Tooltip>
                  ),
                  key: group?.id,
                  children: (
                    <div
                      className="overflow-content"
                      style={{
                        height:
                          windowSize.height -
                          (familyId === 4 || isGuestConnected() ? 150 : 220),
                      }}
                      ref={scrollRef}
                    >
                      {group?.fields?.map((field) => (
                        <DisplayDynamicFieldsByType
                          form={form}
                          config={config}
                          key={field?.id}
                          fieldType={field?.type}
                          fieldId={field?.id}
                          label={field?.label}
                          familyId={field?.family_id}
                          fieldModule={field?.field_module_id}
                          isModule={field?.isModule}
                          value={field?.value || undefined}
                          options={field?.options}
                          required={field?.required}
                          description={field?.description}
                          placeholder={field?.placeholder}
                          setSubmitIsDisable={setSubmitIsDisable}
                          reset={resetFieldsAfterSubmit}
                          isMultiple={field?.multiple}
                          readOnly={field?.read_only}
                          family_id={familyId}
                        />
                      ))}
                    </div>
                  ),
                };
              })}
            />
            {familyId !== 4 && !isGuestConnected() && (
              <Form.Item key="sharedWith" name="sharedWith">
                <SharedWithField
                  userId={currentUser?.id}
                  form={form}
                  departments={config?.departements}
                  t={t}
                  setSubmitIsDisable={setSubmitIsDisable}
                />
              </Form.Item>
            )}
          </Form>
        </div>
      ) : (
        <FormWithTabsLoader tabsNum={3} inputNum={6} />
      )}
    </Drawer>
  );
};

export default memo(FormCreate);
