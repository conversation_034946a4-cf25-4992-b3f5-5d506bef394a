import {
  ArrowDownOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  ArrowUpOutlined,
  InfoCircleOutlined,
  LikeOutlined,
  MailOutlined,
  MenuOutlined,
  MessageOutlined,
} from "@ant-design/icons";
import {
  Al<PERSON>,
  Avatar,
  Button,
  Card,
  Col,
  Divider,
  Dropdown,
  Empty,
  Input,
  Rate,
  Row,
  Skeleton,
  Space,
  Statistic,
  Table,
  Tag,
  Tooltip,
  Typography,
} from "antd";
import { AvatarChat } from "components/Chat";
import { URL_ENV } from "index";
import { setChatSelectedConversation } from "new-redux/actions/chat.actions";
import {
  setOpenEditor,
  setOpenModalEmail,
} from "new-redux/actions/mail.actions";
import { openDrawerChat } from "new-redux/actions/voip.actions/handleDrawerChat";
import {
  addLastIdToViewSphere,
  setActiveActivity360,
  setActiveTab360,
  setNewInteraction,
  setOpenView360InDrawer,
} from "new-redux/actions/vue360.actions/vue360";
import { SET_CONTACT_INFO_FROM_DRAWER } from "new-redux/constants";
import { getGeneralInfo360 } from "pages/clients&users/services/services";
import useActionCall from "pages/voip/helpers/ActionCall";
import React, { useEffect, useState } from "react";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import { useTranslation } from "react-i18next";
import { FiCopy, FiEdit, FiMoreVertical } from "react-icons/fi";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import TableAssociationsViewSphere from "./TableAssociationsViewSphere";
import FormUpdate from "pages/clients&users/components/FormUpdate";
import { familyIcons } from "./ViewSphere2";
import LoadingGridViewSohere from "./LoadingGridViewSohere";
import GeneralInfosGridView from "./GenralInfosGridView";
import { setEmailFields } from "pages/rmc/mailing/main-components/email-composer-modal/utils";
import { HiOutlinePhone } from "react-icons/hi2";

export function handleDispatchActions(
  dispatch,
  item,
  openView360InDrawer,
  setOpenChat,
  setSelectedKeySideBar
) {
  dispatch({
    type: SET_CONTACT_INFO_FROM_DRAWER,
    payload:
      {
        id: item.id,
        // ...selectedItem,
      } || {},
  });

  if (!openView360InDrawer) {
    dispatch(setOpenView360InDrawer(true));
  }

  dispatch(
    setNewInteraction({
      type: "updateElementFromDrawer",
    })
  );

  dispatch(addLastIdToViewSphere(item?.id));

  setOpenChat(false);

  dispatch(
    setChatSelectedConversation({
      selectedConversation: null,
    })
  );

  setSelectedKeySideBar("");
}
export const DropDownActionViewSphere = ({
  item,
  call,
  t,
  dispatch,
  setElementDetailsGridView,
  setOpenDrawerUpdate,
  source = "listItemsViewSphere",
  contactInfo,
  otherItems = [],
  setOpenChat = () => {},
  setSelectedKeySideBar = () => {},
}) => {
  const {
    family_id,
    id,
    extension,
    phone: phones,
    name,
    uuid,
    email,
    group,
    queue,
    is_member,
    canBeDelete,
  } = item;
  const copyIcon = (text) => (
    <div className="pl-1">
      <Typography.Paragraph
        copyable={{
          text: `${text}`,
          icon: [
            <FiCopy
              style={{
                color: "rgb(22, 119, 255)",
                marginTop: 2,
                fontSize: 15,
              }}
            />,
          ],
        }}
      />
    </div>
  );
  const { openView360InDrawer } = useSelector((state) => state?.vue360);
  const { user } = useSelector((state) => state?.user);
  const menuDropdown = () => {
    const items = [];

    const pushItem = (
      key,
      icon,
      label,
      onClick,
      disabled,
      children,
      danger
    ) => {
      items.push({
        key,
        icon,
        label,
        onClick,
        disabled,
        children,
        danger,
      });
    };

    if (family_id === 4 && user?.id !== item?.id) {
      if (extension && phones?.length) {
        pushItem(
          extension + id,
          <HiOutlinePhone
            className="text-slate-400"
            rotate={100}
            style={{ fontSize: 14 }}
          />,
          t("voip.call"),
          null,
          null,
          [
            {
              key: extension,
              label: (
                <div className="flex flex-row justify-between space-x-1.5">
                  {extension}
                  {copyIcon(extension)}
                </div>
              ),
              onClick: () => call(`${extension}`, id, family_id),
            },
            ...phones.map(({ callNum, copyNum, displayNum }) => ({
              key: displayNum,
              label: (
                <div className="flex flex-row justify-between space-x-2">
                  {displayNum}
                  {copyIcon(copyNum)}
                </div>
              ),
              onClick: () => call(callNum, id, family_id),
            })),
          ]
        );
      } else if (!extension && phones.length) {
        pushItem(
          extension + id,
          <HiOutlinePhone
            className="text-slate-400"
            rotate={100}
            style={{ fontSize: 14 }}
          />,
          t("voip.call"),
          null,
          null,
          [
            ...phones.map(({ callNum, copyNum, displayNum }) => ({
              key: displayNum,
              label: (
                <div className="flex flex-row justify-between space-x-2">
                  {displayNum}
                  {copyIcon(copyNum)}
                </div>
              ),
              onClick: () => call(callNum, id, family_id),
            })),
          ]
        );
      } else if (extension && !phones.length)
        pushItem(
          extension + id,
          <HiOutlinePhone
            className=" text-slate-400"
            rotate={100}
            style={{ fontSize: 14 }}
          />,
          <div className="flex flex-row justify-between space-x-2">
            {`${t("voip.call")} ${extension}`}
            {copyIcon(extension)}
          </div>,
          () => call(extension, id, family_id)
        );
    }
    if (
      family_id &&
      family_id !== 4 &&
      phones.length &&
      user?.id !== item?.id
    ) {
      if (phones.length === 1) {
        const { callNum, copyNum, displayNum } = phones[0];
        pushItem(
          displayNum + id,
          <HiOutlinePhone
            className=" text-slate-400"
            rotate={100}
            style={{ fontSize: 14 }}
          />,
          <div className="flex flex-row justify-between space-x-2">
            {`${t("voip.call")} ${displayNum}`}
            {copyIcon(copyNum)}
          </div>,
          () => call(callNum, id, family_id)
        );
      } else
        pushItem(
          id,
          <HiOutlinePhone
            rotate={100}
            className="text-slate-400"
            style={{ fontSize: 14 }}
          />,
          t("voip.call"),
          null,
          null,
          phones.map(({ callNum, copyNum, displayNum }) => ({
            key: displayNum,
            label: (
              <div className="flex flex-row justify-between space-x-2">
                {displayNum}
                {copyIcon(copyNum)}
              </div>
            ),
            onClick: () => call(callNum, id, family_id),
          }))
        );
    }
    if (group || queue) {
      pushItem(
        extension,
        <HiOutlinePhone
          className="text-slate-400"
          rotate={100}
          style={{ fontSize: 14 }}
        />,
        <div className="flex flex-row justify-between space-x-2">
          {`${t("voip.call")} ${extension}`}
          {copyIcon(extension)}
        </div>,
        () => call(extension),
        is_member
      );
    }
    if (uuid && user?.id !== item?.id) {
      if (item.phone.length || item.extension) items.push({ type: "divider" });
      pushItem(
        `chat-${uuid}`,
        <MessageOutlined
          className=" text-slate-400"
          style={{ fontSize: 14 }}
        />,
        <p className="max-w-[10rem] truncate">{`${t("menu1.chat")}`}</p>,
        () => {
          dispatch(openDrawerChat(uuid));
        }
      );
    }
    if (email && user?.id !== item?.id) {
      family_id !== 4 && items.length && items.push({ type: "divider" });
      if (item?.length && items?.[items.length - 1]?.type !== "divider")
        items.push({ type: "divider" });
      pushItem(
        `email-${email}`,
        <MailOutlined className="text-slate-400" style={{ fontSize: 14 }} />,
        <p className="max-w-[10rem] truncate">{`Email `}</p>,
        () => {
          dispatch(
            setEmailFields({
              sender: user.email,
              receivers: [email],
              // contactId: contactInfo?.id,
              // familyId: contactInfo?.family_id,
            })
          );
          dispatch(setOpenModalEmail(true));
          // setOpenEmailModal(true);
          // setTimeout(() => {
          //   dispatch(setOpenEditor({ state: false, type: "" }));
          // }, 1);
        }
      );
    }
    if (family_id) {
      items.length && items.push({ type: "divider" });
      pushItem(
        `update-${id}`,
        <FiEdit className=" text-slate-400" style={{ fontSize: 14 }} />,
        <p className="max-w-[10rem] truncate">{`${t("contacts.edit")}`}</p>,
        () => {
          setElementDetailsGridView({ id, familyId: family_id, label: name });
          setOpenDrawerUpdate(true);
        }
      );
    }
    if (id && family_id) {
      // items.length && items.push({ type: "divider" });

      pushItem(
        "more-info",
        <InfoCircleOutlined
          className=" text-slate-400"
          style={{ fontSize: 14 }}
        />,
        <p className="max-w-[10rem] truncate">
          {group || queue ? `${t("voip.moreInfo")}` : t("voip.view360")}
        </p>,
        () => {
          handleDispatchActions(
            dispatch,
            item,
            openView360InDrawer,
            setOpenChat,
            setSelectedKeySideBar
          );
        }
      );
    }
    if (Array.isArray(otherItems) && otherItems.length > 0)
      items.push(...otherItems);
    return { items };
  };

  return (
    <Dropdown trigger={["click"]} placement="bottomRight" menu={menuDropdown()}>
      <FiMoreVertical
        onClick={() => {
          setOpenChat(false);
          setSelectedKeySideBar("");
        }}
        className="h-[18px] w-[18px] cursor-pointer text-gray-400 hover:text-gray-700"
      />
    </Dropdown>
  );
};
export const formattingNumPhones = (phones, user) => {
  if (!Array.isArray(phones) || !phones.length) return [];
  return phones.map(([prefix, num]) => {
    const copyNum = `${prefix.replace("+", "00")}${num}`;
    const processedPrefix =
      prefix !== user?.location?.dial_code ? prefix.replace("+", "00") : "";
    return {
      displayNum: `(${prefix}) ${num}`,
      callNum: `${processedPrefix}${num}`,
      copyNum: copyNum,
    };
  });
};
const Content = ({
  item,
  contactInfo,
  relations,
  height,
  copyIcon,
  setElementDetailsGridView,
  setOpenDrawerUpdate,
  setOpenChat,
  setSelectedKeySideBar,
  setOpenPipeline,
  openDrawerUpdate,
  elementDetailsGridView,
  isUpdateElementAssociate,
}) => {
  const call = useActionCall();

  return (
    <div className="flex w-full flex-col  gap-2 ">
      <Row gutter={[16, 16]}>
        {height ? (
          <TableAssociationsViewSphere
            item={item}
            contactInfo={contactInfo}
            setElementDetailsGridView={setElementDetailsGridView}
            call={call}
            copyIcon={copyIcon}
            setOpenDrawerUpdate={setOpenDrawerUpdate}
            setOpenChat={setOpenChat}
            setSelectedKeySideBar={setSelectedKeySideBar}
            setOpenPipeline={setOpenPipeline}
            openDrawerUpdate={openDrawerUpdate}
            elementDetailsGridView={elementDetailsGridView}
            isUpdateElementAssociate={isUpdateElementAssociate}
          />
        ) : null}
      </Row>
    </div>
  );
};
const GridView = ({
  generalInfo,
  setSelectedKey,
  contactInfo,
  kpi,
  height,
  setOpenFields,
  isUpdate,
  setIsUpdate,
  relations,
  setOpenChat,
  setSelectedKeySideBar,
  setOpenPipeline,
  openView360InDrawer,
  setOpenFiles,
  setOpenNotes,
  setOpenList,
  setFilter,
}) => {
  const [columns, setColumns] = useState([]);
  const [t] = useTranslation("common");
  const [generalInfoInDahsboard, setGeneralInfoInDahsboard] = useState({});
  const [openDrawerUpdate, setOpenDrawerUpdate] = useState(false);
  const [elementDetailsGridView, setElementDetailsGridView] = useState({});
  const [isUpdateElementAssociate, setIsUpdateElementAssociate] =
    useState(false);

  const [loading, setLoading] = useState({});
  const call = useActionCall();
  const { user } = useSelector((state) => state?.user);
  const dispatch = useDispatch();
  useEffect(() => {
    if (openView360InDrawer && isUpdateElementAssociate) {
      dispatch(setNewInteraction({ type: "associateElementFromDrawer" }));
    } else if (isUpdateElementAssociate)
      dispatch(setNewInteraction({ type: "associateElement" }));
  }, [isUpdateElementAssociate, openView360InDrawer, dispatch]);

  const copyIcon = (text) => (
    <Typography.Paragraph
      copyable={{
        text: text,
        icon: [
          <FiCopy
            style={{
              color: "rgb(22, 119, 255)",
              marginTop: "4px",
              fontSize: "14px",
            }}
          />,
        ],
      }}
    />
  );
  const goToComponents = (item) => {
    switch (item.title) {
      case "Appels directs":
        setSelectedKey("2");
        dispatch(setActiveTab360(4));
        setSelectedKeySideBar("phone");

        break;
      case "Visio":
        dispatch(setActiveActivity360("1"));
        setSelectedKey("3");
        dispatch(setActiveTab360(null));
        setFilter((prev) => ({ selected: [3] }));
        break;
      case "Meeting":
        dispatch(setActiveActivity360("1"));
        setSelectedKey("3");
        dispatch(setActiveTab360(null));
        setFilter((prev) => ({ selected: [1] }));

        break;
      case "Emails":
        setSelectedKey("2");
        dispatch(setActiveTab360(7));
        setSelectedKeySideBar("mail");
        break;
      case "Notes":
        dispatch(setActiveTab360(6));
        setSelectedKey("note_key");
        break;
      case "Comments":
        setOpenChat(false);
        setSelectedKeySideBar("Notes");
        setOpenNotes(true);
        break;
      case "Files":
        setOpenChat(false);
        setSelectedKeySideBar("Files");
        setOpenFiles(true);
        break;
      case "Todolist":
        setOpenChat(false);
        setSelectedKeySideBar(item.key);
        setOpenList(true);
        break;
      default:
        break;
    }
  };
  const columnsTabs = [
    {
      title: "Name",
      dataIndex: "name",
      render: (_, props) => (
        <div className="flex items-center justify-between">
          <span>{props?.name}</span>
          <DropDownActionViewSphere
            item={props}
            t={t}
            call={call}
            dispatch={dispatch}
            copyIcon={copyIcon}
            setOpenDrawerUpdate={setOpenDrawerUpdate}
            contactInfo={contactInfo}
            setContactInfoToDisplay={() => {}}
            setOpenEmailModal={() => {}}
            handleDeleteContact={() => {}}
            handleOpenDrawerUpdate={() => {}}
          />
        </div>
      ),
    },
    {
      title: "Type",
      dataIndex: "type",
    },
    {
      title: "pipeline",
      dataIndex: "pipeline",
    },
    {
      title: t("pipeline.stage"),
      dataIndex: "stage",
    },
  ];

  // const indexPairs = [];
  // const indexImpairs = [];

  // relations.forEach((element, index) => {
  //   if (index % 2 === 0) {
  //     indexPairs.push(element);
  //   } else {
  //     indexImpairs.push(element);
  //   }
  // });
  function isObject(variable) {
    return (
      variable !== null &&
      typeof variable === "object" &&
      !Array.isArray(variable)
    );
  }

  const initialColumns = {
    "column-1": {
      id: "column-1",
      title: "Column 1",
      family_id: "",
      items: [
        {
          id: "item-infoGeneral",
          name: t("companies.infoGeneral"),
          rank: 1,
          content: (
            <Row gutter={[8]}>
              <Col span={12}>
                <GeneralInfosGridView
                  user={user}
                  height={height}
                  generalInfoInDahsboard={generalInfoInDahsboard}
                  generalInfo={generalInfo}
                  setSelectedKey={setSelectedKey}
                  setOpenFields={setOpenFields}
                  t={t}
                />
              </Col>
              <Col span={12}>
                <Card
                  style={{
                    width: "100%",
                    overflow: "auto",
                    height: user?.role === "guest" ? height - 77 : 192,
                    marginBottom: user?.role === "guest" ? 3 : 0,
                  }}
                >
                  <div className="flex w-full  gap-2 ">
                    <Row gutter={[6, 16]} className="w-full">
                      {kpi.map((item, i) => (
                        <Col
                          xxl={4}
                          xl={
                            openView360InDrawer ||
                            // pathname === "/directory"
                            window.location.pathname === "/telephony/directory"
                              ? 8
                              : 6
                          }
                          lg={8}
                          md={12}
                          key={i}
                        >
                          <Card
                            size="small"
                            // style={{ background: "white" }}
                            styles={{ body: { background: "transparent" } }}
                            onClick={() => goToComponents(item)}
                            className="cursor-pointer bg-white hover:border-blue-200 hover:bg-[#f8fafc]"
                          >
                            <Statistic
                              title={t(
                                `vue360.${item?.title.replace(/\s/g, "")}`
                              )}
                              value={item?.value}
                              precision={0}
                              // valueStyle={{
                              //   color: '#3f8600',
                              // }}
                              // prefix={<ArrowUpOutlined />}
                              // suffix="%"
                            />
                            {/* <Statistic title={item.title + " " + item.value} /> */}
                          </Card>
                        </Col>
                      ))}
                    </Row>
                  </div>
                </Card>
              </Col>
            </Row>
          ),
        },
        ...relations?.map((item, i) => ({
          id: `item-${item?.family_id}`,
          name: item?.label,
          source: "associations",
          family_id: item?.family_id,
          count: item?.child?.length,
          content: (
            <Content
              item={item}
              columnsTabs={columnsTabs}
              relations={relations}
              height={height}
              copyIcon={copyIcon}
              setElementDetailsGridView={setElementDetailsGridView}
              openDrawerUpdate={openDrawerUpdate}
              setOpenDrawerUpdate={setOpenDrawerUpdate}
              contactInfo={contactInfo}
              setOpenChat={setOpenChat}
              setSelectedKeySideBar={setSelectedKeySideBar}
              setOpenPipeline={setOpenPipeline}
              elementDetailsGridView={elementDetailsGridView}
              isUpdateElementAssociate={isUpdateElementAssociate}
            />
          ),
        })),
        // ...indexImpairs.map((item, i) => ({
        //   id: `item-${indexPairs.length + 4 + (i - 1)}`,
        //   name: item?.label,
        //   content: (
        //     <Content
        //       item={item}
        //       columnsTabs={columnsTabs}
        //       height={height}
        //       relations={relations}
        //       setReceiverEmail={setSenderAssoc}
        //       copyIcon={copyIcon}
        //     />
        //   ),
        // })),
      ],
    },
  };
  useEffect(() => {
    const getGeneralInfo = async () => {
      setLoading(true);
      const { data: generalInfos } = await getGeneralInfo360(
        "general_info",
        contactInfo?.id
      );
      setGeneralInfoInDahsboard(generalInfos?.field_value);
      setLoading(false);
      setIsUpdate(false);
    };
    if (!isUpdate && contactInfo?.id) getGeneralInfo();
  }, [
    contactInfo?.id,
    isUpdate,
    // newInteraction,
  ]);
  useEffect(() => {
    if (
      localStorage.getItem("columnsGridView") &&
      localStorage.getItem("columnsGridView") !== undefined
    ) {
      const customOrder = JSON.parse(
        localStorage.getItem("columnsGridView")
      )?.filter((item) =>
        initialColumns["column-1"].items.map((el) => el.id).includes(item)
      );
      initialColumns["column-1"].items
        .map((el) => el.id)
        .forEach((item) => {
          if (!customOrder?.includes(item)) {
            customOrder?.push(item);
          }
        });
      localStorage.setItem("columnsGridView", JSON.stringify(customOrder));
      if (Array.isArray(customOrder) && customOrder.length > 0) {
        const reorderedItems = customOrder.map((id) =>
          initialColumns["column-1"].items.find((item) => item?.id === id)
        );

        initialColumns["column-1"].items = reorderedItems;
        setColumns(initialColumns);
      }
    } else setColumns(initialColumns);
  }, [
    generalInfoInDahsboard,
    localStorage.getItem("columnsGridView"),
    relations,
    // kpi,
  ]);
  const handleOnDragEnd = (result) => {
    const { source, destination } = result;
    if (!destination) return;

    if (
      source.droppableId === destination.droppableId &&
      source.index === destination.index
    ) {
      return;
    }

    const sourceColumn = columns[source.droppableId];
    const destColumn = columns[destination.droppableId];
    if (sourceColumn === destColumn) {
      const newItems = Array.from(sourceColumn.items);
      [newItems[source.index], newItems[destination.index]] = [
        newItems[destination.index],
        newItems[source.index],
      ];
      const newColumn = {
        ...sourceColumn,
        items: newItems,
      };
      setColumns((prevColumns) => ({
        ...prevColumns,
        [source.droppableId]: newColumn,
      }));
      const updatedColumns = {
        ...columns,
        [source.droppableId]: newColumn,
      };
      localStorage.setItem(
        "columnsGridView",
        JSON.stringify(
          Object.entries(updatedColumns)
            .flat()[1]
            ?.items?.map((el) => el.id)
        )
      );
    } else {
      const sourceItems = Array.from(sourceColumn.items);
      const destItems = Array.from(destColumn.items);

      const [sourceItem] = sourceItems.splice(source.index, 1);
      const [destItem] = destItems.splice(destination.index, 1, sourceItem);

      sourceItems.splice(source.index, 0, destItem);

      setColumns((prevColumns) => ({
        ...prevColumns,
        [source.droppableId]: {
          ...sourceColumn,
          items: sourceItems,
        },
        [destination.droppableId]: {
          ...destColumn,
          items: destItems,
        },
      }));
      const updatedColumns = {
        ...columns,
        [source.droppableId]: {
          ...sourceColumn,
          items: sourceItems,
        },
        [destination.droppableId]: {
          ...destColumn,
          items: destItems,
        },
      };
      localStorage.setItem(
        "columnsGridView",
        JSON.stringify(
          Object.entries(updatedColumns)
            .flat()[1]
            ?.items?.map((el) => el.id)
        )
      );
    }
  };

  return (
    <div className="flex flex-col gap-2 ">
      {/* <Alert message="This feature is under dev" type="warning" showIcon /> */}

      <DragDropContext onDragEnd={handleOnDragEnd}>
        <div
          style={{
            display: "flex",
            // overflowX: "auto",
            width: "100%",
          }}
          className="gap-2"
        >
          {Object.entries(columns).map(([columnId, column]) => (
            <div
              key={columnId}
              style={{
                flex: "100%",
                width: "100%",
              }}
            >
              {/* <h3>{column.title}</h3> */}
              <Droppable droppableId={columnId}>
                {(provided) => (
                  <div
                    {...provided.droppableProps}
                    ref={provided.innerRef}
                    style={{
                      // border: "1px solid lightgrey",
                      borderRadius: "4px",
                      width: "100%",
                      minHeight: "100px",
                      // overflowY: "auto", // Gérer le défilement ici si nécessaire
                    }}
                  >
                    {column.items.map((item, index) =>
                      loading ? (
                        <LoadingGridViewSohere />
                      ) : (
                        <Draggable
                          key={item?.id}
                          draggableId={item?.id}
                          index={index}
                        >
                          {(provided) => (
                            <div
                              key={index}
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              // {...provided.dragHandleProps}
                              style={{
                                userSelect: "none",
                                // padding: "14px",
                                margin: "0 0 8px 0",
                                // backgroundColor: "#F8FAFC",
                                // boxShadow:
                                //   "0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",
                                maxHeight:
                                  Array.isArray(relations) &&
                                  relations.length > 0
                                    ? "auto"
                                    : height - 110,
                                // border: "1px solid #ddd",
                                ...provided.draggableProps.style,
                              }}
                              // className="block   rounded-lg border border-gray-200 bg-white p-6 shadow hover:bg-blue-50 "
                            >
                              <Card
                                // hoverable
                                bodyStyle={{
                                  maxHeight:
                                    user.role === "guest"
                                      ? height - 50
                                      : relations.length > 0
                                      ? "auto"
                                      : height - 97,
                                  overflowY: "auto",
                                  overflowX: "hidden",
                                  paddingBottom: 2,
                                }}
                                size="small"
                                title={
                                  user?.role === "guest" ? (
                                    ""
                                  ) : item?.source === "associations" ? (
                                    <Space>
                                      <span>
                                        {
                                          familyIcons(t).find(
                                            (el) => el.key === item?.family_id
                                          )?.label
                                        }
                                      </span>
                                      <span>({item?.count})</span>
                                    </Space>
                                  ) : (
                                    item?.name
                                  )
                                }
                                extra={
                                  user?.role === "guest" ? null : (
                                    <div
                                      {...provided.dragHandleProps} // Drag activé ici
                                      style={{ cursor: "grab" }}
                                    >
                                      <MenuOutlined />
                                    </div>
                                  )
                                }
                                style={{
                                  width: "100%",
                                  maxHeight:
                                    relations.length > 0
                                      ? "auto"
                                      : height - 110,
                                }}
                                styles={{
                                  body: {
                                    background:
                                      item.source === "associations"
                                        ? "linear-gradient(to top, #f8fafc, #E6F4FF)"
                                        : "#F4F9FD",
                                    padding:
                                      item.source === "associations"
                                        ? "0 8px"
                                        : 8,
                                  },
                                  // header: {
                                  //   padding: "0 8px",
                                  // },
                                }}
                              >
                                {item?.content}
                              </Card>
                            </div>
                          )}
                        </Draggable>
                      )
                    )}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </div>
          ))}
        </div>
      </DragDropContext>

      <FormUpdate
        open={openDrawerUpdate}
        setOpen={setOpenDrawerUpdate}
        familyId={
          Object.keys(elementDetailsGridView).length > 0
            ? elementDetailsGridView?.familyId
            : ""
        }
        elementDetails={{
          id:
            Object.keys(elementDetailsGridView).length > 0
              ? elementDetailsGridView?.id
              : "",
          label:
            Object.keys(elementDetailsGridView).length > 0
              ? elementDetailsGridView?.label
              : "",
        }}
        mask={false}
        from="viewSphere"
        setCatchChange={setIsUpdateElementAssociate}
      />
    </div>
  );
};

export default GridView;
