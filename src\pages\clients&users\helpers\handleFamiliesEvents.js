import { toastNotification } from "components/ToastNotification";
import { convertPathToFamilyId, getFamilyNameById } from "../FamilyRouting";
import DisplayAvatar from "pages/voip/components/DisplayAvatar";
import { URL_ENV } from "index";
import { Button } from "antd";
import { RightCircleOutlined } from "@ant-design/icons";
import { generateUrlToView360 } from "pages/voip/helpers/helpersFunc";

export const handleFamilyEvent =
  (message, userId, t, navigate) => (dispatch) => {
    // console.log({ message, userId });

    //
    if (!userId || userId === message?.user?._id) return;
    //
    const pathName = new URL(window.location.href)?.pathname;
    if (
      message?.family_id &&
      convertPathToFamilyId(pathName) === Number(message?.family_id)
    ) {
      dispatch({
        type: "SET_FAMILY_EVENT",
        payload: {
          type: message?.type_event?.split("_")?.[0],
          familyId: message?.family_id,
          elementId: message?.element_id,
          data: message?.data,
          user: message?.user,
          etat: message?.etat,
          createdAt: message?.created_at,
          updatedAt: message?.updated_at,
        },
      });
    }
    //
    const avatar =
      !!message?.user?.avatar &&
      `${URL_ENV?.REACT_APP_BASE_URL + URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL}${
        message?.user?.avatar
      }`;
    //
    const textToDisplay = (type) => {
      switch (type) {
        case "create":
          return (
            <div
              dangerouslySetInnerHTML={{
                __html: t("contacts.createdElement", {
                  user: message.user?.label,
                  family:
                    Number(message?.family_id) === 4
                      ? t("mailing.user")
                      : getFamilyNameById(t, message?.family_id),
                  elementName: message.element_label,
                }),
              }}
            />
          );
        case "update":
          return (
            <div
              dangerouslySetInnerHTML={{
                __html: t("contacts.updatedElement", {
                  user: message.user?.label,
                  family:
                    Number(message?.family_id) === 4
                      ? t("mailing.user")
                      : getFamilyNameById(t, message?.family_id),
                  elementName: message.element_label,
                }),
              }}
            />
          );
        case "delete":
          return "";
        default:
          return "";
      }
    };
    const displayMessage = (
      <div className="flex flex-col space-y-2">
        <div className="flex items-start space-x-3">
          <div className="avatar">
            <DisplayAvatar
              name={message?.user?.label}
              urlImg={avatar}
              size={32}
            />
          </div>

          {textToDisplay(message?.type_event?.split("_")?.[0])}
        </div>
        <Button
          block
          size="small"
          type="link"
          className=" ml-auto flex w-full items-center justify-center  space-x-1 "
          shape="circle"
          onClick={() =>
            navigate(
              generateUrlToView360(
                message?.family_id,
                message?.element_id,
                "v2"
              )
            )
          }
        >
          <span>{t("chat.goto")}</span>
          <RightCircleOutlined
            style={{
              fontSize: "1rem",
            }}
          />
        </Button>
      </div>
    );
    return toastNotification("open", displayMessage, "topRight", 5);
  };
