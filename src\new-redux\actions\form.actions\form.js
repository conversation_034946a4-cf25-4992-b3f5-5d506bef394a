import {
  C<PERSON><PERSON>_SEARCH,
  SET_CREATE_FORM,
  SET_OPEN_IMPORT_DRAWER,
  SET_OPEN_IMPORT_CHILDREN_DRAWER,
  SET_IMPORT_KEY,
  SET_PATH_NAME,
  UPDATE_ELEMENT_SUCCESSFULLY,
} from "../../constants";

export const setCreateForm = (payload) => (dispatch) => {
  dispatch({ type: SET_CREATE_FORM, payload: payload });
};

export const setOpenImportDrawer = (payload) => (dispatch) => {
  dispatch({ type: SET_OPEN_IMPORT_DRAWER, payload: payload });
};

export const setOpenChildrenImportDrawer = (payload) => (dispatch) => {
  dispatch({ type: SET_OPEN_IMPORT_CHILDREN_DRAWER, payload: payload });
};

export const clearSearchInput = (payload) => (dispatch) => {
  dispatch({ type: <PERSON><PERSON><PERSON>_SEARCH, payload: payload });
};

export const setImportKey = (payload) => (dispatch) => {
  dispatch({ type: SET_IMPORT_KEY, payload: payload });
};

export const setPathName = (payload) => (dispatch) => {
  dispatch({ type: SET_PATH_NAME, payload });
};

export const setUpdateElementSuccessfully = (payload) => async (dispatch) => {
  dispatch({ type: UPDATE_ELEMENT_SUCCESSFULLY, payload: payload });
};
