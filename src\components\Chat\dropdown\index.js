import {
  ContainerOutlined,
  MoreOutlined,
  PhoneOutlined,
  QuestionCircleOutlined,
} from "@ant-design/icons";
import { <PERSON><PERSON>, Dropdown, Popconfirm, Typography } from "antd";
import { useCallback, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { <PERSON><PERSON>ell, <PERSON><PERSON>ellOff, FiInfo, FiLogOut } from "react-icons/fi";
import { useDispatch, useSelector } from "react-redux";

import {
  setChatSelectedConversation,
  setDataConversation,
  setOpenDrawer,
  setOpenQuitGroupModal,
  updateChatSelectedConversation,
} from "../../../new-redux/actions/chat.actions";
import {
  addArchivedConversation,
  callApi,
  getArchivedConversationsIds,
  removeArchivedConversation,
  setMuteConversation,
  setUnmuteConversation,
} from "../../../new-redux/services/chat.services";
import { batch } from "react-redux";
import { BiCodeAlt, <PERSON>i<PERSON>oll } from "react-icons/bi";
import {
  ADD_ARCHIVE_CONVERSATION,
  REMOVE_ARCHIVE_CONVERSATION,
} from "../../../new-redux/constants";
import {
  isRoom,
  isPublicRoom,
} from "pages/layouts/chat/utils/ConversationUtils";
import { isGuestConnected } from "utils/role";

const { Text } = Typography;

const DropDownGroupIndex = ({
  item = {},
  source,
  setCurrentSelectedItem = () => {},
}) => {
  const [selectedDropDown, setSelectedDropDown] = useState({
    id: null,
    isArchive: false,
  });
  //const [openPopoverArchive, setOpenPopoverArchive] = useState(false);
  const [loadingArchive, setLoadingArchive] = useState(false);
  const [clicked, setClicked] = useState(false);

  const selectedConversation = useSelector(
    (state) => state.ChatRealTime.selectedConversation
  );
  const { currentUser, sidebarDrawer, openDrawer } = useSelector(
    (state) => state.chat
  );
  const sphereRole = useSelector((state) => state.user?.user?.role);

  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const handleClick = useCallback(
    (element) => {
      if (setCurrentSelectedItem) {
        if (
          !selectedConversation?.id ||
          selectedConversation?.id !== getCurrentItem(element)?._id ||
          selectedConversation?.external
        ) {
          setCurrentSelectedItem(element, true);
        } else if (setCurrentSelectedItem) {
          setCurrentSelectedItem(element, false);
        }
      }
    },
    [
      setCurrentSelectedItem,
      selectedConversation?.external,
      selectedConversation?.id,
    ]
  );

  const getCurrentItem = (item) => {
    if (item.contact !== null) {
      return item.contact;
    } else if (item.room !== null) {
      return item.room;
    } else if (item.bot !== null) {
      return item.bot;
    }
  };

  const handleOkArchive = useCallback(
    async (e, item) => {
      e.stopPropagation();
      let array = [];
      let response;
      setLoadingArchive(true);
      try {
        if (sidebarDrawer === "chat") {
          response = await dispatch(
            addArchivedConversation({
              _id: item._id,
              t,
              item,
            })
          );
          array.push({
            type: ADD_ARCHIVE_CONVERSATION,
            payload: {
              data: response,
              item,
            },
          });
        } else {
          response = await dispatch(
            removeArchivedConversation({
              _id: item._id,
              t,
              item,
            })
          );
          array.push({
            type: REMOVE_ARCHIVE_CONVERSATION,
            payload: {
              data: response,
              item,
            },
          });
          array.push(
            setChatSelectedConversation({
              selectedConversation: null,
            })
          );
          array.push(getArchivedConversationsIds());
        }
      } catch {
        return;
      } finally {
        let time, time2;
        setLoadingArchive(false);
        clearTimeout(time);
        clearTimeout(time2);
        time2 = setTimeout(() => {
          setSelectedDropDown({
            id: null,
            isArchive: false,
          });
        }, 1);
        time = setTimeout(async () => {
          await Promise.all(array.map((action) => dispatch(action)));

          array = [];
          clearTimeout(time);
          clearTimeout(time2);
        }, 10);
      }
    },
    [sidebarDrawer, dispatch, t]
  );
  const handleCancelArchive = (e) => {
    e.stopPropagation();
    setSelectedDropDown((p) => ({
      ...p,
      isArchive: false,
    }));
  };

  const items = useMemo(() => {
    const array = [];
    array.push({
      key: "menu-06",
      disabled: typeof item?._id === "undefined",
      label:
        sidebarDrawer === "chat"
          ? t("chat.archiveDiscussion")
          : t("chat.restoreDiscussion"),
      icon: (
        <Popconfirm
          title={
            sidebarDrawer === "chat"
              ? t("chat.archiveModalTitle")
              : t("chat.archiveRestoreModalTitle")
          }
          description={
            sidebarDrawer === "chat"
              ? t("chat.archiveModalBody")
              : t("chat.archiveRestoreModalBody")
          }
          open={selectedDropDown?.isArchive}
          onConfirm={(e) => handleOkArchive(e, item)}
          okButtonProps={{
            loading: loadingArchive,
          }}
          icon={<QuestionCircleOutlined style={{ color: "blue" }} />}
          onCancel={handleCancelArchive}
          trigger="click"
        >
          <ContainerOutlined style={{ fontSize: "13px" }} />
        </Popconfirm>
      ),
      show: !isGuestConnected(currentUser?.role, sphereRole) && "1",
    });
    if (sidebarDrawer === "archive") return array;

    array.push(
      ...[
        {
          key: "menu-01",
          label: t("chat.informations"),
          disabled: source !== "chat",

          icon: <FiInfo style={{ fontSize: "13px" }} />,
          show: "1",
          onClick: async () => {
            if (source !== "chat") return;
            if (
              openDrawer?.type === "info" &&
              selectedConversation?.conversationId === item?._id
            )
              return;
            dispatch(setOpenDrawer({ type: "info" }));
            handleClick(item);
          },
        },
        {
          key: "menu-02",
          disabled: typeof item?._id === "undefined",

          label: item?.muted_status ? t("chat.unmute") : t("chat.mute"),
          icon: item?.muted_status ? (
            <FiBell style={{ fontSize: "13px" }} />
          ) : (
            <FiBellOff style={{ fontSize: "13px" }} />
          ),
          show: !isGuestConnected(currentUser?.role, sphereRole) && "1",
          onClick: async () => {
            try {
              if (item?._id === selectedConversation?.conversationId) {
                dispatch(
                  updateChatSelectedConversation({
                    muted_status: !selectedConversation?.muted_status,
                  })
                );
              }
              if (item?.muted_status) {
                dispatch(setUnmuteConversation(item));
              } else {
                dispatch(setMuteConversation(item));
              }
            } catch (error) {
              console.log(error);
            }
          },
        },
        {
          key: "menu-03",
          label: t("chat.action.call"),
          icon: <PhoneOutlined className="group-hover:underline" />,
          show: !isRoom(item) ? "1" : "0",
          onClick: async () => {
            dispatch(
              callApi({
                setClicked: setClicked,
                post_numberR: getCurrentItem(item)?.post_number,
                errorText: t("toasts.errorFetchApi"),
              })
            );
          },
          disabled: clicked || !currentUser?.post_number,
        },

        {
          key: "menu-04",
          disabled: source !== "chat",
          label: t("chat.action.list_polls"),
          icon: <BiPoll style={{ fontSize: "13px" }} />,
          show: "1",
          onClick: async () => {
            if (source !== "chat") return;
            dispatch(
              setOpenDrawer({
                type: "polls_1",
                external: item,
              })
            );
            handleClick(item);
          },
        },
        {
          key: "menu-05",
          disabled: source !== "chat" || typeof item?._id === "undefined",

          label: t("chat.bot.dev"),
          icon: <BiCodeAlt style={{ fontSize: "15px" }} />,
          onClick: async () => {
            if (source !== "chat") return;

            if (
              openDrawer?.type === "webhook" &&
              selectedConversation?.conversationId === item?._id
            )
              return;
            dispatch(
              setOpenDrawer({
                type: "webhook",
                external: item,
              })
            );
            handleClick(item);
          },
          show:
            isRoom(item) &&
            !isPublicRoom(item) &&
            getCurrentItem(item)?.admin_id === currentUser?._id
              ? "1"
              : "0",
        },
        {
          key: "menu-07",
          type: "divider",
          show:
            !isGuestConnected(currentUser?.role, sphereRole) &&
            !isPublicRoom(item) &&
            isRoom(item)
              ? "1"
              : "0",
        },
        {
          key: "menu-08",

          label: <Text type="danger">{t("chat.quitGroup")}</Text>,
          icon: <FiLogOut style={{ fontSize: "13px", color: "red" }} />,
          show:
            !isGuestConnected(currentUser?.role, sphereRole) &&
            !isPublicRoom(item) &&
            isRoom(item)
              ? "1"
              : "0",

          onClick: async () => {
            if (isPublicRoom(item)) return;
            batch(async () => {
              await dispatch(
                setDataConversation({
                  conversationId: item._id,
                  id: item?.room?._id,
                  ...item?.room,
                })
              );
              dispatch(setOpenQuitGroupModal(true));
            });
          },
        },
      ]
    );

    return array
      .filter((el) => el.show === "1")
      .sort((a, b) => a.key?.localeCompare(b.key));
  }, [
    clicked,
    selectedDropDown?.isArchive,
    currentUser?._id,
    dispatch,
    handleClick,
    handleOkArchive,
    item,
    loadingArchive,
    selectedConversation,
    sidebarDrawer,
    t,
  ]);
  // useEffect(() => {
  //   let observer;
  //   const ref = document.getElementById(`listItem-${index}`);
  //   if (observer) observer.disconnect();
  //   observer = new IntersectionObserver(
  //     (entries) => {
  //       if (!entries[0].isIntersecting && selectedDropDown) {
  //         ref.parentElement.parentElement.click();

  //         observer.disconnect();
  //         setSelectedDropDown({
  //           id: null,
  //           isArchive: false,
  //         });
  //       }
  //     },
  //     { threshold: 0.8 }
  //   );
  //   if (ref) observer.observe(ref);
  //   return () => {
  //     observer.disconnect();
  //   };
  // }, [index, selectedDropDown]);

  return (
    <Dropdown
      overlayClassName="w-[180px]"
      placement="bottomRight"
      arrow
      key={item?._id}
      getPopupContainer={(trigger) => trigger.parentElement.parentElement}
      open={selectedDropDown.id && selectedDropDown.id === item?._id}
      onOpenChange={(e) => {
        setSelectedDropDown((prev) => {
          if (prev.isArchive) {
            return { ...prev };
          } else
            return {
              id: e ? item?._id : null,
              isArchive: false,
            };
        });
      }}
      trigger={["click"]}
      menu={{
        items,
        className: "bg-red-500",
        onClick: (e) => {
          // if (e.key === "menu-06") {
          //   //  e.domEvent.preventDefault();
          //   e.domEvent.stopPropagation();
          // }

          setSelectedDropDown(() => {
            return {
              id: e.key === "menu-06" ? item?._id : null,
              isArchive: e.key === "menu-06",
            };
          });
        },
      }}
    >
      <Button
        onClick={() => setSelectedDropDown({ id: item?._id, isArchive: false })}
        icon={<MoreOutlined />}
        type="text"
        shape="circle"
        size="small"
      />
    </Dropdown>
  );
};
export default DropDownGroupIndex;
