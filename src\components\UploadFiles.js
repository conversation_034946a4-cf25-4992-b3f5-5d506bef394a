import { memo, useState, useEffect } from "react";
import { Upload } from "antd";
import {
  FileExcelOutlined,
  FileImageOutlined,
  FilePdfOutlined,
  FileTextOutlined,
  FileWordOutlined,
} from "@ant-design/icons";

/**
 * @name UploadFiles
 *
 * @description An upload attachment component.
 *              It's used across multiple modules in sphere:
 *                -src/pages/tasks/ActivityDetails/Activity360.js
 *
 * @param {function} customRequest Trigger upload attachment api after validation.
 * @param {array} defaultFileList Array of attachments.
 * @param {String} className ClassName to apply some custom styling.
 * @param {function} removeUploadedFile Trigger remove uploaded attachment api.
 * @param {Boolean} multiple to enable uploading multiple files at once or not.
 * @param {String} listType Type of list (picture, text, card, ...) https://ant.design/components/upload#api.
 * @param {Number} maxCount Max count of selected items to be uploaded.
 * @param {Boolean} disabled Disable uploading files for some users.
 * @param {function} t Translation instance.
 * @param {function} onPreview Handle open attachment in new tab on click.
 * @param {function} beforeUpload Validation of files before proceeding on the upload.
 * @param {array} allowedFiles Allowed extensions to be used on validation.
 * @param {Boolean} directory Support upload whole directory (folders).
 *
 * @returns {JSX.Element} The rendered upload attachment component.
 */

const UploadFiles = ({
  customRequest,
  defaultFileList,
  className,
  removeUploadedFile,
  multiple,
  listType,
  maxCount,
  disabled,
  t,
  onPreview,
  beforeUpload,
  allowedFiles,
  directory,
  showUploadList = true,
}) => {
  return (
    <Upload.Dragger
      key={defaultFileList?.length}
      customRequest={customRequest}
      multiple={multiple}
      listType={listType}
      defaultFileList={defaultFileList}
      className={className}
      onRemove={(param) => removeUploadedFile(param)}
      maxCount={maxCount}
      disabled={disabled}
      onPreview={onPreview}
      accept={allowedFiles}
      beforeUpload={beforeUpload}
      directory={directory}
      showUploadList={showUploadList}
    >
      <p className="ant-upload-drag-icon">
        <FileWordOutlined style={{ fontSize: "1.5rem" }} />
        <FileExcelOutlined style={{ fontSize: "2rem" }} />
        <FileImageOutlined style={{ fontSize: "3rem" }} />
        <FilePdfOutlined style={{ fontSize: "2rem" }} />
        <FileTextOutlined style={{ fontSize: "1.5rem" }} />
      </p>
      <p className="ant-upload-text">{t("tasks.uploadFile")}</p>
      <p className="ant-upload-hint">{t("tasks.uploadFileHint")}</p>
      <p className="ant-upload-hint">.pdf .docx .xlsx .pptx .txt .csv .jpg .jpeg .png .gif</p>
    </Upload.Dragger>
  );
};

export default UploadFiles;
