/**
 * @name KpiGrid
 *
 * @description `KpiGrid` component is responsible for displaying the list of activities related kpis.
 *
 * @param {Array} kpisList The list of kpis.
 *
 * @returns {JSX.Element} Grid with small cards holding the kpis values.
 */

import { useTranslation } from "react-i18next";

import CardStat from "pages/components/CardStat";

// This function format and translate the key of the object.
export const displayStatKey = (t, key, source) => {
  const keyMap = {
    visio: "tasks.kpiVisioLabel",
    is_overdue:
      source === "visio"
        ? "tasks.kpiIsOverdueVisio"
        : "tasks.kpiIsOverdueLabel",
    created: "tasks.kpiCreatedLabel",
    invited: "tasks.kpiInvitedLabel",
    starting_from_today: "tasks.startingFromToday",
    source_chat: "tasks.KpiChatSource",
  };

  return t(keyMap[key]);
};

const KpiGrid = ({ kpisList }) => {
  const [t] = useTranslation("common");

  return (
    <div className="flex w-full flex-row justify-between gap-4">
      {kpisList &&
        Object.keys(kpisList)?.length > 0 &&
        Object.entries(kpisList).map(([key, value]) => (
          <CardStat
            key={key}
            item={{ title: displayStatKey(t, key, ""), value: value }}
          />
        ))}
    </div>
  );
};

export default KpiGrid;
