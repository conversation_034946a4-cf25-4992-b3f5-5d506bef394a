import {
  BankOutlined,
  <PERSON>Outlined,
  CalendarOutlined,
  CameraOutlined,
  CarOutlined,
  CheckCircleOutlined,
  CommentOutlined,
  CreditCardOutlined,
  EditOutlined,
  FacebookOutlined,
  FieldTimeOutlined,
  FolderOutlined,
  GlobalOutlined,
  InboxOutlined,
  InstagramOutlined,
  MailOutlined,
  MessageOutlined,
  MobileOutlined,
  PhoneOutlined,
  ReadOutlined,
  SettingOutlined,
  UploadOutlined,
  UserOutlined,
  VideoCameraOutlined,
  WarningOutlined,
  WhatsAppOutlined,
} from "@ant-design/icons";
import {
  AtSymbolIcon,
  TruckIcon,
  WrenchScrewdriverIcon,
} from "@heroicons/react/24/outline";
import { Hospital, Stethoscope } from "lucide-react";
import { HiOutlineVideoCamera } from "react-icons/hi2";
export const allIcons = [
  {
    label: <MailOutlined />,
    value: "MailOutlined",
  },
  {
    label: <EditOutlined />,
    value: "EditOutlined",
  },
  {
    label: <BankOutlined />,
    value: "BankOutlined",
  },
  {
    label: <PhoneOutlined />,
    value: "PhoneOutlined",
  },
  {
    label: <CalendarOutlined />,
    value: "CalendarOutlined",
  },
  {
    label: <InboxOutlined />,
    value: "InboxOutlined",
  },
  {
    label: <BellOutlined />,
    value: "BellOutlined",
  },
  {
    label: <AtSymbolIcon className=" relative top-[4px] -mx-[2px] w-[16px] " />,
    value: "AtSymbolIcon",
  },
  {
    label: <MessageOutlined />,
    value: "MessageOutlined",
  },

  {
    label: <CheckCircleOutlined />,
    value: "CheckCircleOutlined",
  },

  {
    label: <CameraOutlined />,
    value: "CameraOutlined",
  },
  {
    label: <VideoCameraOutlined />,
    value: "VideoCameraOutlined",
  },
  {
    label: <ReadOutlined />,
    value: "ReadOutlined",
  },
  {
    label: <CarOutlined />,
    value: "CarOutlined",
  },
  {
    label: <TruckIcon className=" relative top-[3px] -mx-[2px] w-[16px]" />,
    value: "TruckIcon",
  },
  { label: <FolderOutlined />, value: "FolderOutlined" },
  { label: <MobileOutlined />, value: "MobileOutlined" },
  { label: <UserOutlined />, value: "UserOutlined" },
  { label: <CreditCardOutlined />, value: "CreditCardOutlined" },
  { label: <FieldTimeOutlined />, value: "FieldTimeOutlined" },
  { label: <WarningOutlined />, value: "WarningOutlined" },
  { label: <SettingOutlined />, value: "SettingOutlined" },
  {
    label: <WrenchScrewdriverIcon className=" relative top-[4px] w-[16px] " />,
    value: "WrenchScrewdriverIcon",
  },
  { label: <GlobalOutlined />, value: "WebOutlined" },
  { label: <UploadOutlined />, value: "UploadOutlined" },
  { label: <CommentOutlined />, value: "SocialOutlined" },
  { label: <WhatsAppOutlined />, value: "WhatsAppOutlined" },
  { label: <HiOutlineVideoCamera />, value: "visio" },

  { label: <InstagramOutlined />, value: "InstagramOutlined" },
  { label: <FacebookOutlined />, value: "FacebookOutlined" },
  { label: <Stethoscope size={16} />, value: "Consultation" },
  { label: <Hospital size={16} />, value: "Exam" },
  {
    label: (
      <span className="anticon anticon-whats-app">
        <svg
          width="13"
          height="16"
          viewBox="0 -2 13 13"
          xmlns="http://www.w3.org/2000/svg"
          focusable={false}
          data-icon="instagram"
          fill="currentColor"
          aria-hidden="true"
        >
          <path
            d="M6.5 0C2.9305 0 0 2.71177 0 6.07107C0 7.86104 0.850586 9.45098 2.16667 10.5584V13L4.57031 11.8287C5.1818 12.0081 5.8208 12.1421 6.5 12.1421C10.0695 12.1421 13 9.43036 13 6.07107C13 2.71177 10.0695 0 6.5 0ZM6.5 1.05584C9.51091 1.05584 11.9167 3.30774 11.9167 6.07107C11.9167 8.83439 9.51091 11.0863 6.5 11.0863C5.85254 11.0863 5.2347 10.9667 4.65495 10.7728L4.4349 10.7069L3.25 11.2843V10.0964L3.04687 9.93147C1.84505 9.00761 1.08333 7.62389 1.08333 6.07107C1.08333 3.30774 3.48909 1.05584 6.5 1.05584ZM5.89062 4.40482L2.6237 7.7703L5.55208 6.18655L7.10937 7.8198L10.3424 4.40482L7.48177 5.97208L5.89062 4.40482Z"
            // fill="black"
          />
        </svg>
      </span>
    ),
    value: "MessengerOutlined",
  },
];
