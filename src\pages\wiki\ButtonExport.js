import { ExportOutlined } from "@ant-design/icons";
import { Button } from "antd";
import axios from "axios";
import { toastNotification } from "components/ToastNotification";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";

const ButtonExport = ({ fileUrl }) => {
  const [loadingExport, setLoadingExport] = useState(false);
  const [t] = useTranslation("common");

  const handleExport = async () => {
    setLoadingExport(true);
    try {
      const response = await axios.get(fileUrl, { responseType: "blob" });
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "document.pdf");
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      setLoadingExport(false);
    } catch (error) {
      setLoadingExport(false);
      toastNotification("error", t("wiki.ErrDownload"), "topRight");
    }
  };
  return (
    <Button
      type="link"
      icon={<ExportOutlined />}
      onClick={handleExport}
      loading={loadingExport}
      // onClick={() => exportPdf(props.id)}
    >
      {t("signature.exportpdf")}
    </Button>
  );
};

export default ButtonExport;
