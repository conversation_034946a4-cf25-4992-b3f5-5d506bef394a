import {
  CheckOutlined,
  CopyOutlined,
  WarningOutlined,
} from "@ant-design/icons";
import {
  Button,
  DatePicker,
  Form,
  Input,
  Modal,
  Space,
  Tooltip,
  Typography,
} from "antd";
import BottomButtonAddRow from "components/BottomButtonAddRow";
import LabelTable from "components/LabelTable";
import NewTableDraggable from "components/NewTableDraggable";
import { toastNotification } from "components/ToastNotification";
import Header from "components/configurationHelpDesk/Header";
import dayjs from "dayjs";
import { URL_ENV } from "index";
import moment from "moment";
import { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { FiCheck, FiCopy } from "react-icons/fi";
import { useSelector } from "react-redux";
import { generateAxios } from "services/axiosInstance";

const AccessToken = () => {
  const [form] = Form.useForm();

  const [data, setData] = useState([]);
  const [open, setOpen] = useState(false);
  const now = dayjs();
  const [t] = useTranslation("common");
  const [editingKey, setEditingKey] = useState("");
  const [id, setId] = useState(null);
  const [loading, setLoading] = useState(true);
  const isEditing = (record) => record.key === editingKey;
  const [token, setToken] = useState("");
  const inputRefs = useRef([]);
  const [companies, setCompanies] = useState([]);
  const [selectedItems, setSelectedItems] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [oldPage, setOldPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const { user } = useSelector((state) => state.user);
  const disabledDate = (current) => {
    // Désactiver les dates précédentes à partir d'aujourd'hui
    return current && current < moment().startOf("day");
  };
  const EditableCell = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    const inputNode =
      inputType === "date" ? (
        // <GroupColors color={color} setColor={setColor} />
        <DatePicker
          disabledDate={disabledDate}
          format={user?.location?.date_format}
        />
      ) : (
        <Input
          ref={(el) => (inputRefs.current[index] = el)}
          //   onKeyPress={handleKeyPress}
          placeholder={t("activities.name")}
        />
      );
    return (
      <td {...restProps}>
        {editing ? (
          <Form.Item
            name={dataIndex.toLowerCase()}
            style={{
              margin: 0,
            }}
            rules={[
              {
                required: dataIndex === "expires_at" ? false : true,
                message: `${t(`activities.${dataIndex}`)} ${t(
                  "table.header.isrequired"
                )}`,
              },
            ]}
          >
            {inputNode}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };
  useEffect(() => {
    const getAccessToken = async () => {
      setLoading(true);
      try {
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get("/external-tokens");

        if (res.status === 200) {
          setData(res.data.data.map((el) => ({ ...el, key: el.id })));

          setLoading(false);
        }
      } catch (err) {
        setLoading(false);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    };
    getAccessToken();
    // return () => dispatch(setSearch(""));
  }, []);

  const columns = [
    {
      title: t("activities.name"),
      dataIndex: "name",
      key: "name",
      editable: true,
      sorter: (a, b) => a.name.localeCompare(b.name),
      render: (_, record) => {
        return (
          <LabelTable record={record} editingKey={editingKey} edit={edit} />
        );
      },
    },
    {
      title: t("profilemenu.expirationDate"),
      dataIndex: "expires_at",
      key: "expires_at",
      editable: true,
      sorter: (a, b) => a?.expires_at?.localeCompare(b?.expires_at),
      render: (_, record) =>
        // console.log(
        //   dayjs(dayjs(record.expires_at), user?.location?.date_format),
        //   "------",
        //   dayjs(now, user?.location?.date_format)
        // ),
        record?.expires_at ? (
          dayjs(now, user?.location?.date_format)
            .startOf("day")
            .isAfter(
              dayjs(
                dayjs(record.expires_at),
                user?.location?.date_format
              ).startOf("day")
            ) ? (
            <div>
              <Tooltip title={t("profilemenu.expiredDate")}>
                <Typography.Text type="danger">
                  <WarningOutlined />{" "}
                  {dayjs(dayjs(record.expires_at)).format(
                    user?.location?.date_format
                  )}
                </Typography.Text>
              </Tooltip>
            </div>
          ) : (
            dayjs(dayjs(record.expires_at)).format(user?.location?.date_format)
          )
        ) : (
          ""
        ),
    },
    {
      title: t("profilemenu.lastUsed"),
      dataIndex: "last_used",
      key: "last_used",
      editable: false,
      // sorter: (a, b) => a.label.localeCompare(b.label),
      // render: (_, record) => {
      //   return !record?.disabled
      //     ? record?.last_used
      //       ? record?.last_used
      //       : "N/A"
      //     : null;
      // },
    },
    {
      title: t("contacts.createdAt"),
      dataIndex: "created_at",
      key: "created_at",
      editable: false,
      sorter: (a, b) => a.created_at.localeCompare(b.created_at),
      render: (_, record) =>
        record?.disabled
          ? null
          : dayjs(record?.created_at).format(user?.location?.date_format),
    },
  ];
  const edit = (record) => {
    if (record) {
      form.setFieldsValue({
        name: record.name,
        expires_at: record.expires_at ? dayjs(record.expires_at) : "",
      });
      setId(record.id);
    } else {
      form.setFieldsValue({
        name: "",
        expires_at: "",
      });
    }
    setEditingKey(record.key);
  };
  const handleAdd = () => {
    setId(null);
    const ids = data.map((object) => {
      return object.id;
    });
    const newData = {
      key: Math.max(...ids) + 1,
      name: `  `,
      expires_at: "",
      disabled: true,
    };
    setCurrentPage(Math.ceil([...data, newData].length / pageSize));
    setOldPage(Math.ceil([...data, newData].length / pageSize));

    setData([...data, newData]);
    form.setFieldsValue({
      name: "",
      expires_at: "",
    });
    setEditingKey(Math.max(...ids) + 1);
  };
  const onRow = () => {};
  const cancel = (record) => {
    setEditingKey("");
    setToken("");
    setId(null);
    if (!record.id) {
      setData(data.filter((item) => item.key !== record.key));
    }
  };
  const save = async () => {
    setLoading(true);
    if (id) {
      try {
        const row = await form.validateFields();
        const requestData = JSON.stringify({
          name: row.name,
          expires_at: row.expires_at,
        });
        const config = {
          method: "put",
          url: `${URL_ENV?.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}external-tokens/${id}`,
          headers: {
            "Content-Type": "application/json",
            // Cookie: "XDEBUG_SESSION=PHPSTORM",
          },
          data: requestData,
        };
        const res = await generateAxios().request(config);
        setEditingKey("");
        // console.log(res.data.data);
        setData(
          data.map((el) =>
            el.id === res.data.data.id
              ? {
                  ...res.data.data,
                  key: res.data.data.id,
                }
              : el
          )
        );
        form.setFieldsValue({
          name: "",
          expires_at: "",
        });
        setLoading(false);
        toastNotification("success", row.name + t("toasts.edit"), "topRight");
      } catch (errInfo) {
        setLoading(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    } else {
      try {
        const row = await form.validateFields();
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post("/external-tokens", {
          name: row.name,

          expires_at: row.expires_at,
        });
        setEditingKey("");
        // console.log(res.data.data);
        setData([
          ...data.filter((el) => el.id),
          {
            ...res.data.data?.data,
            key: res.data.data.data.id,

            // companies_ids: res.data.data.companies
            //   .map((el) => el.social_reason)
            //   .join(","),
          },
        ]);
        form.setFieldsValue({
          name: "",
          expires_at: "",
        });

        setToken(res.data.data.token);
        setOpen(true);
        setLoading(false);
        // toastNotification(
        //   "success",
        //   row.name + t("toasts.created"),
        //   "topRight"
        // );
      } catch (errInfo) {
        setLoading(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    }
  };
  const onFinishFailed = (values) => {
    console.log(values);
  };
  return (
    <Space direction="vertical" style={{ width: "100%", marginTop: "20px" }}>
      <Header
        active={"2"}
        editingKey={editingKey}
        handleAdd={handleAdd}
        btnText={t("profilemenu.addAccessToken")}
        disabled={loading ? true : editingKey ? true : false}
        data={data}
        api="accessToken"
      />

      <NewTableDraggable
        columns={columns}
        setLoading={setLoading}
        isEditing={isEditing}
        data={data}
        setData={setData}
        loading={loading}
        save={save}
        edit={edit}
        EditableCell={EditableCell}
        onFinishFailed={onFinishFailed}
        cancel={cancel}
        form={form}
        apiRank="/rank-departments"
        editingKey={editingKey}
        api="external-tokens"
        onRow={onRow}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        pageSize={pageSize}
        setPageSize={setPageSize}
      />

      <BottomButtonAddRow
        editingKey={editingKey}
        data={data}
        text={t("profilemenu.addAccessToken")}
        handleAdd={handleAdd}
        loading={loading}
        search={""}
      />
      <Modal
        title={t("profilemenu.pat")}
        centered
        open={open}
        onOk={() => setOpen(false)}
        onCancel={() => setOpen(false)}
        footer={
          <div className="flex items-center justify-end">
            {/* <Button
              onClick={() => {
                setOpen(false);
                setToken("");
              }}
            >
              {t("vue360.hide")}
            </Button> */}
            <Typography.Paragraph
              copyable={{
                text: token,
                icon: [
                  <Button type="primary" icon={<CopyOutlined />}>
                    {t("chat.action.copy")}
                  </Button>,
                  // <Button type="primary" icon={<CheckOutlined />} disabled>
                  <span className="cursor-text">
                    <CheckOutlined className="text-green-600" />{" "}
                    {t("chat.action.copied")}{" "}
                  </span>,
                  // </Button>,
                ],
                tooltips: false,
              }}
            />
          </div>
        }
      >
        <Space direction="vertical" style={{ width: "100%" }}>
          <Typography.Text>
            <WarningOutlined className="text-yellow-500" />{" "}
            {t("profilemenu.lastViewAccessToken")}
          </Typography.Text>
          <Input value={token} />
        </Space>
      </Modal>
    </Space>
  );
};

export default AccessToken;
