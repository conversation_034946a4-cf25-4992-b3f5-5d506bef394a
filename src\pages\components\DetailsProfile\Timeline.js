import React, { useCallback, useEffect, useRef, useState } from "react";
import {
  Avatar,
  Badge,
  Button,
  Card,
  Col,
  Collapse,
  Divider,
  Empty,
  Form,
  List,
  Row,
  Segmented,
  Skeleton,
  Space,
  Spin,
  Statistic,
  Tabs,
  Tag,
  Tooltip,
  Typography,
} from "antd";
import MainService from "../../../services/main.service";
import { useLocation, useParams } from "react-router-dom";
import ChoiceIcons from "../ChoiceIcons";
import { humanDate } from "../../voip/helpers/helpersFunc";
import { useTranslation } from "react-i18next";
import {
  Activity,
  CalendarCheck,
  CheckSquare,
  File,
  Mail,
  MessageCircle,
  PictureInPicture,
  Presentation,
  StickyNote,
} from "lucide-react";
import AllTasks from "./Activities/AllTasks";
import { init } from "emoji-mart";
import UpdateTask from "../../tasks/UpdateTask";
import Mails from "./Activities/Mails";
import {
  CaretRightOutlined,
  PlusOutlined,
  TableOutlined,
  UnorderedListOutlined,
} from "@ant-design/icons";
import CheckList from "./CheckList";
import CreateTask from "../../voip/components/CreateTask";
import moment from "moment";
import { setPage } from "../../../new-redux/actions/visio.actions/visio";
import Files from "./Activities/Files";
import Header from "../../../components/configurationHelpDesk/Header";
import ChatRmc from "./ChatRmc";
// import CallLogs from "../../voip/call_logs/CallLogs";
import { useSelector } from "react-redux";
import ListActivities from "./Activities/ListActivities";
import { setActiveActivity360 } from "../../../new-redux/actions/vue360.actions/vue360";
import { useDispatch } from "react-redux";
import { setTask360 } from "../../../new-redux/actions/chat.actions/Input";
import TasksTableView from "pages/tasks/tasksViews/TasksTableView";
import CardStat from "../CardStat";
import SearchInTable from "../Search";
import FilterTable from "components/FilterTable";
import { GenericButton } from "../GenericButton";
import TasksRoom from "pages/tasks/tasksRoom";
import { setOpenTaskRoomDrawer } from "new-redux/actions/tasks.actions/handleTaskRoomDrawer";
import { toastNotification } from "components/ToastNotification";
import useDebounce from "../UseDebounce/UseDebounce";
import useCompAct360 from "pages/tasks/activityDetails/CompAct360";
import Activity360 from "pages/tasks/activityDetails/Activity360";
import { URL_ENV } from "index";
import EmptyPage from "components/EmptyPage";
import { clearSearchInput } from "new-redux/actions/form.actions/form";

const TimeLine = ({
  tasksTypes,
  setCountTasks,
  countTasks,
  setOpenModal,
  openModal,
  dataSteps,
  headerHeight,
  setKpi = () => {},
  kpi = [],
  from = "",
  contactInfo,
  collapsed,
  setOpenChat = () => {},
  setSelectedKeySideBar = () => {},
  relations = [],
  listFilter,
  setFilter,
}) => {
  const [initLoading, setInitLoading] = useState(true);
  const [openFilterTable, setOpenFilterTable] = useState(false);
  const [dateViewTable, setDateViewTable] = useState(1);
  const [form] = Form.useForm();

  const { activeTab360, activeActivity360 } = useSelector(
    (state) => state?.vue360
  );
  const { user } = useSelector((state) => state?.user);
  const { openTaskRoomDrawer } = useSelector((state) => state?.TasksRealTime);

  const params = useParams();
  const [t] = useTranslation("common");
  const location = useLocation();
  // const [idTask, setIdTask] = useState(null);
  const [pipelines, setPipelines] = useState([]);
  const [externeUpdate, setExterneUpdate] = useState(false);
  const [detailsTask, setDetailsTask] = useState({});
  const dispatch = useDispatch();
  // const [loadTabs, setLoadTabs] = useState(true);
  // const [loadDetails, setLoadDetails] = useState(false);
  const [guestsListPage, setGuestsListPage] = useState(1);
  const [open, setOpen] = useState(false);
  const [guestsList, setGuestsList] = useState([]);
  const [guestsListLastPage, setGuestsListLastPage] = useState(null);
  const [followersListPage, setFollowersListPage] = useState(1);
  const [ownersList, setOwnersList] = useState([]);
  const [followersListLastPage, setFollowersListLastPage] = useState(null);
  const [guestsSearchQuery, setGuestsSearchQuery] = useState("");
  const [followersSearchQuery, setFollowersSearchQuery] = useState("");
  const [tasksToday, setTasksToday] = useState([]);
  const [tasksUpcoming, setTasksUpcoming] = useState([]);
  const [tasksHistory, setTasksHistory] = useState([]);
  const [openTaskAdvanced, setOpenTaskAdvanced] = useState(false);
  const [pageTasksToday, setPageTasksToday] = useState(1);
  const [pageTasksUpComing, setPageTasksUpComing] = useState(1);
  const [activeKey, setActiveKey] = useState(["2"]);
  const [loadUpdateTaskStage, setLoadUpdateTaskStage] = useState(false);
  const [pageTasksHistory, setPageTasksHistory] = useState(1);
  const [lastPageTasksToday, setLastPageTasksToday] = useState(1);
  const [lastPageTasksUpComing, setLastPageTasksUpComing] = useState(1);
  const [limit, setLimit] = useState(10);
  const [activityLabel, setActivityLabel] = useState("");
  const [lastPageTasksHistory, setLastPageTasksHistory] = useState(1);
  const [mount, setIsMount] = useState(false);
  const [totalTasksToday, setTotalTasksToday] = useState(0);
  const [totalTasksUpComing, setTotalTasksUpComing] = useState(0);
  const [showTime, setShowTime] = useState(false);
  const [stageIdToFilter, setStageIdToFilter] = useState("");
  const [updateFilters, setUpdateFilters] = useState(0);
  const [totalTasksHistory, setTotalTasksHistory] = useState(0);
  const [loadTasks, setLoadTasks] = useState(false);
  const [debounceSearch, setDebounceSearch] = useState("");
  const [view, setView] = useState("List");
  const [divWidth, setDivWidth] = useState(null);
  const divRef = useRef(null);
  const [roomActivityId, setRoomActivityId] = useState(null);
  const [checkedItems, setCheckedItems] = useState([]);
  const [checkedFollowers, setCheckedFollowers] = useState([]);
  const [totalEntities, setTotalEntities] = useState({
    colleagues: 0,
    all: 0,
  });
  const [loadOwners, setLoadOwners] = useState(false);

  const [loadGuests, setLoadGuests] = useState(false);

  const [selectedFamilyMembers, setSelectedFamilyMembers] = useState([
    1, 2, 4, 9,
  ]);
  const debounceGuestsSearch = useDebounce(guestsSearchQuery, 500);
  const debounceFollowersSearch = useDebounce(followersSearchQuery, 500);
  const {
    singleTaskData,
    setSingleTaskData,
    loadSpecificTask,
    taskToUpdate: idTask,
    setTaskToUpdate: setIdTask,
    files,
    setFiles,
    openActivity360,
    setOpenActivity360,
    countChanges,
    setCountChanges,
    setShowCardPopover,
    addOnsValues,
    setAddOnsValues,
  } = useCompAct360();
  useEffect(() => {
    const updateDivWidth = () => {
      if (divRef.current) {
        const width = divRef.current.offsetWidth;
        setDivWidth(width);
      }
    };
    window.addEventListener("resize", updateDivWidth);
    updateDivWidth();

    return () => {
      window.removeEventListener("resize", updateDivWidth);
    };
  }, []);
  const { search, task360, cancelTask360 } = useSelector((state) => state.form);
  useEffect(() => {
    let timer;
    timer = setTimeout(() => {
      setDebounceSearch(search || "");
    }, 300);
    return () => clearTimeout(timer);
  }, [search]);
  const updateKpi = async () => {
    const { data: kpiInfo } = await MainService.getKpiOverview360ByElement(
      contactInfo?.id
    );
    setKpi(
      Object.entries(kpiInfo.data[0]).map(([key, value]) => ({
        title: key,
        value,
      }))
    );
  };
  const updateCountTasks = async () => {
    const res = await MainService.getTasks360Count({
      id: contactInfo?.id,
      types:
        listFilter.selected?.length > 0 ? listFilter.selected.join(",") : "",
    });
    setCountTasks(res?.data);
    updateKpi();
  };
  useEffect(() => {
    if (task360 && Object.keys(task360).length > 0) {
      addTask(task360);
      setTimeout(() => {
        dispatch(setTask360({}));
      }, 100);
    } else if (cancelTask360) {
      setTasksToday((prev) => prev.filter((el) => el.id !== cancelTask360));
      setTotalTasksToday(totalTasksToday - 1);
      updateCountTasks();
      // setKpi((prev) =>
      //   prev.map((el) =>
      //     el.title === "Visio" ? { ...el, value: el.value + 1 } : el
      //   ))
      setTimeout(() => {
        dispatch(cancelTask360(""));
      }, 100);
    }
  }, [task360, cancelTask360, dispatch]);
  useEffect(() => {
    setOpenChat(false);
    setSelectedKeySideBar("");
    if (openActivity360 && singleTaskData?.id) {
      setCheckedFollowers(singleTaskData?.followers);
      setCheckedItems(singleTaskData?.guests);
    }
    if (!openActivity360 && countChanges > 0) {
      setSingleTaskData({});
      setCountChanges(0);
      getAllTasks();
    }
  }, [openActivity360, singleTaskData, dateViewTable]);
  const getAllTasks = async (page = "", limit = 10) => {
    setInitLoading(true);
    try {
      // setActiveKey(["2"]);
      const [tasksToday, tasksUpcoming, tasksHistory, countTasks] =
        await Promise.all([
          MainService.getTasks360({
            id: contactInfo?.id,
            types:
              listFilter.selected?.length > 0
                ? listFilter.selected.join(",")
                : "",
            time: 1,
            search: debounceSearch,
          }),

          MainService.getTasks360({
            id: contactInfo?.id,
            types:
              listFilter.selected?.length > 0
                ? listFilter.selected.join(",")
                : "",
            time: 2,
            search: debounceSearch,
          }),
          MainService.getTasks360({
            id: contactInfo?.id,
            types:
              listFilter.selected?.length > 0
                ? listFilter.selected.join(",")
                : "",
            time: 0,
            search: debounceSearch,
          }),
          MainService.getTasks360Count({
            id: contactInfo?.id,
            types:
              listFilter.selected?.length > 0
                ? listFilter.selected.join(",")
                : "",
          }),
        ]);
      setTasksHistory(tasksHistory?.data?.data);
      setTasksToday(tasksToday?.data?.data);
      setTasksUpcoming(tasksUpcoming?.data?.data);
      setLastPageTasksUpComing(tasksUpcoming?.meta?.last_page);
      setLastPageTasksHistory(tasksHistory?.data?.meta?.last_page);
      setLastPageTasksToday(tasksToday?.data?.meta?.last_page);
      setTotalTasksUpComing(tasksUpcoming?.data?.meta?.total);
      setTotalTasksHistory(tasksHistory?.data?.meta?.total);
      setTotalTasksToday(tasksToday?.data?.meta?.total);
      setCountTasks(countTasks?.data);
      // setData(response?.data);
      // if (response?.data?.data?.length > 0) {
      //   setList(response?.data?.data);
      // }
      setInitLoading(false);
      setIsMount(true);
    } catch (error) {
      setInitLoading(false);

      console.log(`Error ${error}`);
    }
  };

  useEffect(() => {
    // setList([]);

    setPageTasksHistory(1);
    setPageTasksToday(1);
    setPageTasksUpComing(1);
    setLastPageTasksHistory(1);
    setLastPageTasksToday(1);
    setLastPageTasksUpComing(1);
    if (view === "List" && contactInfo?.id) {
      getAllTasks();
    }

    // fetch(fakeDataUrl)
    //   .then((res) => res.json())
    //   .then((res) => {
    //     setInitLoading(false);
    //     setData(res.results);
    //     setList(res.results);
    //   });
  }, [listFilter, debounceSearch, contactInfo?.id, view]);
  useEffect(() => {
    const getTaskswithPage = async () => {
      setInitLoading(true);
      try {
        // setActiveKey(["2"]);
        const tasks = await MainService.getTasks360({
          id: contactInfo?.id,
          // types:
          //   listFilter.selected?.length > 0
          //     ? listFilter.selected.join(",")
          //     : "",
          time: dateViewTable,
          search: debounceSearch,
          limit: limit,
          page:
            dateViewTable === 1
              ? pageTasksToday
              : dateViewTable === 2
              ? pageTasksUpComing
              : pageTasksHistory,
        });

        if (dateViewTable === 1) {
          // setTasksToday((prev) => tasks?.data?.data);
          setLastPageTasksToday(tasks?.meta?.last_page);
          setLastPageTasksToday(tasks?.data?.meta?.last_page);
        } else if (dateViewTable === 2) {
          setTasksUpcoming((prev) => tasks?.data?.data);
          setTotalTasksUpComing(tasks?.data?.meta?.total);
          setLastPageTasksUpComing(tasks?.meta?.last_page);
        } else {
          setTasksHistory((prev) => tasks?.data?.data);
          setTotalTasksHistory(tasks?.data?.meta?.total);
          setLastPageTasksHistory(tasks?.meta?.last_page);
        }
        updateCountTasks();
        // setData(response?.data);
        // if (response?.data?.data?.length > 0) {
        //   setList(response?.data?.data);
        // }
        setInitLoading(false);
      } catch (error) {
        setInitLoading(false);

        console.log(`Error ${error}`);
      }
    };
    if (view === "Table" && mount) {
      getTaskswithPage();
    }
  }, [
    // view,
    // dateViewTable,
    // debounceSearch,
    // listFilter?.selected,
    contactInfo?.id,
    pageTasksHistory,
    pageTasksToday,
    pageTasksUpComing,
  ]);
  useEffect(() => {
    if (
      totalTasksToday + totalTasksUpComing + totalTasksHistory === 0 &&
      listFilter?.selected?.length === 0
    ) {
      setView("List");
    }
  }, [
    totalTasksToday,
    totalTasksUpComing,
    totalTasksHistory,
    listFilter?.selected,
  ]);

  const UpdateTaskStage = async (payload, source) => {
    try {
      setInitLoading(true);
      await MainService.updateTaskStageInKanban(payload);
      getAllTasks();

      setLoadUpdateTaskStage(false);
    } catch (error) {
      console.log(`Error ${error}`);
      setInitLoading(false);
      setLoadUpdateTaskStage(false);
    }
  };

  // const getOwners = useCallback(
  //   async (signal) => {
  //     try {
  //       let formData = new FormData();
  //       formData.append("family_id[]", 4);
  //       const response = await MainService.getFamilyOptions(
  //         "",
  //         "",
  //         followersSearchQuery,
  //         formData,
  //         signal
  //       );
  //       if (followersListPage > 1) {
  //         setOwnersList([...ownersList, ...response?.data?.data]);
  //       } else {
  //         setOwnersList(response?.data?.data);
  //       }

  //       setFollowersListLastPage(response?.data?.meta?.last_page);
  //     } catch (error) {
  //       console.log(`Error ${error}`);
  //     }
  //   },
  //   [followersListPage, followersSearchQuery]
  // );
  useEffect(() => {
    const getPipelines = async () => {
      try {
        const response = await MainService.getPipelinesByFamilyTask();
        let systemPipeline = response?.data?.data.find(
          (pipeline) => pipeline?.system === 1
        )?.id;
        setPipelines(response?.data?.data);
      } catch (error) {
        console.log(`Error ${error}`);
      }
    };

    getPipelines();
    return () => {
      dispatch(clearSearchInput());
    };
  }, []);
  // Get owners/followers API.
  const getOwners = useCallback(
    async (signal) => {
      if (!openActivity360) return;
      try {
        setLoadOwners(true);
        let formData = new FormData();
        formData.append("family_id", 4);
        formData.append("search", debounceFollowersSearch);
        const response = await MainService.getFamilyOptions(
          followersListPage,
          50,
          formData
        );
        if (followersListPage > 1) {
          setOwnersList([...ownersList, ...response?.data?.data]);
        } else {
          setOwnersList(response?.data?.data);
        }
        setTotalEntities((prev) => ({
          ...prev,
          colleagues: response?.data?.meta?.total,
        }));
        setFollowersListLastPage(response?.data?.meta?.last_page);
        setLoadOwners(false);
      } catch (error) {
        setLoadOwners(false);
        console.log(`Error ${error}`);
        toastNotification("error", t("toasts.somethingWrong"));
      }
    },
    [debounceFollowersSearch, followersListPage, openActivity360]
  );

  // Get owners/followers on mount.
  useEffect(() => {
    let abort = new AbortController();
    getOwners(abort?.signal);
    return () => abort?.abort();
  }, [getOwners]);

  // Get guests API.
  const getGuests = useCallback(async () => {
    if (!openActivity360) return;

    try {
      let formData = new FormData();
      formData.append("family_id", selectedFamilyMembers.toString());
      formData.append("search", debounceGuestsSearch);
      setLoadGuests(true);
      const response = await MainService.getFamilyOptions(
        guestsListPage,
        50,
        formData
      );
      if (guestsListPage > 1) {
        setGuestsList([...guestsList, ...response?.data?.data]);
      } else {
        setGuestsList(response?.data?.data);
      }
      setTotalEntities((prev) => ({
        ...prev,
        all: response?.data?.meta?.total,
      }));
      setGuestsListLastPage(response?.data?.meta?.last_page);
      setLoadGuests(false);
    } catch (error) {
      console.log(`Error ${error}`);
      toastNotification("error", t("toasts.somethingWrong"));
      setLoadGuests(false);
    }
  }, [
    guestsListPage,
    debounceGuestsSearch,
    selectedFamilyMembers,
    openActivity360,
  ]);
  // Get guests on mount.
  useEffect(() => {
    let abort = new AbortController();
    getGuests();
    return () => abort?.abort();
  }, [getGuests, guestsListPage, debounceGuestsSearch]);
  // useEffect(() => {
  //   let abort = new AbortController();
  //   if (followersListPage !== undefined) {
  //     getOwners(abort?.signal);
  //   }
  //   return () => abort?.abort();
  // }, [followersListPage, getOwners]);

  // useEffect(() => {
  //   let abort = new AbortController();
  //   if (guestsListPage !== undefined) {
  //     getGuests(abort?.signal);
  //   }
  //   return () => abort?.abort();
  // }, [getGuests, guestsListPage]);

  const panelStyle = {
    marginBottom: 24,
    background: location.pathname.includes("v3") ? "white" : "rgb(248 250 252)",
    borderRadius: 8,
    border: "none",
  };
  const getItems = (panelStyle) => [
    {
      key: "1",
      label:
        totalTasksUpComing > 0
          ? t("visio.upComing") + " (" + totalTasksUpComing + ")"
          : t("visio.upComing"),
      children:
        tasksUpcoming.length === 0 ? (
          <EmptyPage
            heroTitle={
              <span>
                {t("tasks.startCreateActivities")}{" "}
                <Typography.Link onClick={() => setOpenTaskAdvanced(true)}>
                  {t("dashboard.activities")}
                </Typography.Link>
              </span>
            }
            mainBtnTitle={""}
            handleMainBtnClick={() => setOpenTaskAdvanced(true)}
          />
        ) : (
          <AllTasks
            key="1"
            setOpenChat={setOpenChat}
            setSelectedKeySideBar={setSelectedKeySideBar}
            page={pageTasksUpComing}
            lastPage={lastPageTasksUpComing}
            setPage={setPageTasksUpComing}
            tasksTypes={tasksTypes}
            list={tasksUpcoming}
            setList={setTasksUpcoming}
            initLoading={initLoading}
            setInitLoading={setInitLoading}
            setDetailsTask={setDetailsTask}
            setIdTask={setIdTask}
            setOpenActivity360={setOpenActivity360}
            activeActivity360={activeActivity360}
            time={2}
            total={totalTasksUpComing}
            setTotal={setTotalTasksUpComing}
            pipelines={pipelines}
            setCountTasks={setCountTasks}
            listFilter={listFilter}
            open={open}
            setOpen={setOpen}
            setRoomActivityId={setRoomActivityId}
            roomActivityId={roomActivityId}
            updateKpi={updateKpi}
            setTaskToUpdate={setIdTask}
          />
        ),
      style: panelStyle,
    },
    {
      key: "2",
      label:
        totalTasksToday > 0
          ? t("visio.today") + " (" + totalTasksToday + ")"
          : t("visio.today"),
      children:
        tasksToday.length === 0 ? (
          <EmptyPage
            heroTitle={
              <span>
                {t("tasks.startCreateActivities")}{" "}
                <Typography.Link onClick={() => setOpenTaskAdvanced(true)}>
                  {t("dashboard.activities")}
                </Typography.Link>
              </span>
            }
            mainBtnTitle={""}
            handleMainBtnClick={() => setOpenTaskAdvanced(true)}
          />
        ) : (
          <AllTasks
            key="2"
            setOpenChat={setOpenChat}
            setSelectedKeySideBar={setSelectedKeySideBar}
            page={pageTasksToday}
            lastPage={lastPageTasksToday}
            setPage={setPageTasksToday}
            setOpenActivity360={setOpenActivity360}
            tasksTypes={tasksTypes}
            list={tasksToday}
            setList={setTasksToday}
            initLoading={initLoading}
            setInitLoading={setInitLoading}
            setDetailsTask={setDetailsTask}
            setIdTask={setIdTask}
            activeActivity360={activeActivity360}
            time={1}
            total={totalTasksToday}
            pipelines={pipelines}
            setCountTasks={setCountTasks}
            listFilter={listFilter}
            setTotal={setTotalTasksToday}
            open={open}
            setOpen={setOpen}
            setRoomActivityId={setRoomActivityId}
            roomActivityId={roomActivityId}
            updateKpi={updateKpi}
            setTaskToUpdate={setIdTask}
          />
        ),

      style: panelStyle,
    },
    {
      key: "3",
      label:
        totalTasksHistory > 0
          ? t("visio.history") + " (" + totalTasksHistory + ")"
          : t("visio.history"),
      children:
        tasksHistory.length === 0 ? (
          <EmptyPage
            heroTitle={
              <span>
                {t("tasks.startCreateActivities")}{" "}
                <Typography.Link onClick={() => setOpenTaskAdvanced(true)}>
                  {t("dashboard.activities")}
                </Typography.Link>
              </span>
            }
            mainBtnTitle={""}
            handleMainBtnClick={() => setOpenTaskAdvanced(true)}
          />
        ) : (
          <AllTasks
            key="3"
            setOpenChat={setOpenChat}
            setSelectedKeySideBar={setSelectedKeySideBar}
            page={pageTasksHistory}
            lastPage={lastPageTasksHistory}
            setPage={setPageTasksHistory}
            tasksTypes={tasksTypes}
            list={tasksHistory}
            setOpenActivity360={setOpenActivity360}
            setList={setTasksHistory}
            initLoading={initLoading}
            setInitLoading={setInitLoading}
            setDetailsTask={setDetailsTask}
            setIdTask={setIdTask}
            activeActivity360={activeActivity360}
            time={0}
            total={totalTasksHistory}
            pipelines={pipelines}
            setCountTasks={setCountTasks}
            listFilter={listFilter}
            setTotal={setTotalTasksHistory}
            open={open}
            setOpen={setOpen}
            setRoomActivityId={setRoomActivityId}
            roomActivityId={roomActivityId}
            updateKpi={updateKpi}
            setTaskToUpdate={setIdTask}
          />
        ),
      style: panelStyle,
    },
  ];

  const items = [
    {
      label: (
        <Space>
          <CalendarCheck size={14} />
          <span className="-mt-1">
            {t("layout_profile_details.activities")}{" "}
            <Badge
              count={countTasks?.all > 0 ? countTasks?.all : 0}
              color={countTasks?.all > 0 ? "#80808014" : ""}
              style={{ color: "black" }}
            />
          </span>
        </Space>
      ),
      key: "1",
      children: (
        <ListActivities
          headerHeight={headerHeight}
          setOpenTaskAdvanced={setOpenTaskAdvanced}
          initLoading={initLoading}
          detailsTask={detailsTask}
          setFilter={setFilter}
          tasksTypes={tasksTypes}
          setActiveKey={setActiveKey}
          activeKey={activeKey}
          countTasks={countTasks}
          tasksHistory={tasksHistory}
          tasksToday={tasksToday}
          tasksUpcoming={tasksUpcoming}
          getItems={getItems}
          panelStyle={panelStyle}
          listFilter={listFilter}
          dataSteps={dataSteps}
          from={from}
        />
      ),
    },

    {
      label: (
        <Space>
          <CheckSquare size={14} />
          <span className="-mt-1">
            {/* {t("layout_profile_details.files")} */}
            Todolist{" "}
            <Badge
              count={
                kpi?.find((el) => el.title === "Todolist")?.value > 0
                  ? kpi.find((el) => el.title === "Todolist")?.value
                  : 0
              }
              color={
                kpi?.find((el) => el.title === "Todolist")?.value > 0
                  ? "#80808014"
                  : ""
              }
              style={{ color: "black" }}
            />
          </span>
        </Space>
      ),
      key: "7",
      children: (
        <CheckList
          headerHeight={headerHeight}
          openModal={openModal}
          setOpenModal={setOpenModal}
          setKpi={setKpi}
          from={from}
        />
      ),
    },

    // {
    //   label: (
    //     <Space>
    //       <MdCall size={14} />
    //       <span className="-mt-1"> Journal d’appels</span>
    //     </Space>
    //   ),
    //   key: "9",
    //   children: <CallLogs relationId={params?.id} />,
    // },
  ];
  const addTask = async (data) => {
    const currentDate = moment().format(user.location.date_format);
    const start_date = moment(data.start_date, user.location.date_format);
    if (
      listFilter?.selected.length === 0 ||
      listFilter?.selected.some((el) => el === data.tasks_type_id)
    ) {
      if (
        activeActivity360 == "1" &&
        (data.guests.some((el) => el.id === contactInfo?.id) ||
          data.followers.some((el) => el.id === contactInfo?.id) ||
          data.owner_id.id === contactInfo?.id ||
          data.creator.id === contactInfo?.id ||
          data.element_id === contactInfo?.id)
      ) {
        if (currentDate === data.start_date) {
          setTasksToday((prev) => [
            {
              ...data,
              created_at: data?.create_at,
            },
            ...prev,
          ]);
          setTotalTasksToday(totalTasksToday + 1);
          setActiveKey(["2"]);
          // setCountTasks((prev) => ({ ...prev, all: prev.all + 1 }));
        } else if (
          start_date.isAfter(
            moment(currentDate, user.location.date_format),
            "day"
          )
        ) {
          setTasksUpcoming((prev) => [
            {
              ...data,
              created_at: data?.create_at,
            },
            ...prev,
          ]);
          setTotalTasksUpComing(totalTasksUpComing + 1);
          setActiveKey(["1"]);
          // setCountTasks((prev) => ({
          //   ...prev,
          //   all: prev.all + 1,
          //   upcoming: prev.upcoming + 1,
          // }));

          // dispatch(setLastPage(Math.ceil((countUpComing + 1) / 10)));
        } else if (
          start_date.isBefore(
            moment(currentDate, user.location.date_format),
            "day"
          )
        ) {
          // dispatch(
          //   setLater({ later: later + 1, countUpComing: countUpComing + 1 })
          // );
          setActiveKey(["3"]);

          setTasksHistory((prev) => [
            {
              ...data,
              created_at: data?.create_at,
            },
            ...prev,
          ]);
          setTotalTasksHistory(totalTasksHistory + 1);

          // dispatch(setLastPage(Math.ceil((countUpComing + 1) / 10)));
        }
      }
    }
    updateCountTasks();
  };

  // }

  const renderSkeletonButtons = () => {
    const skeletonButtons = new Array(10).fill(null); // Crée un tableau de 10 éléments

    return skeletonButtons.map((_, index) => <Skeleton.Button key={index} />);
  };
  const updateTaskFrom360 = async (data) => {
    const currentDate = moment().format(user.location.date_format);
    const start_date = moment(data.start_date, user.location.date_format);
    if (
      listFilter?.selected.length === 0 ||
      listFilter?.selected.some((el) => el === data.tasks_type_id)
    ) {
      if (
        activeActivity360 == "1" &&
        (data.guests.some((el) => el.id === contactInfo?.id) ||
          data.followers.some((el) => el.id === contactInfo?.id) ||
          data.owner_id.id === contactInfo?.id ||
          data.creator.id === contactInfo?.id ||
          data.element_id === contactInfo?.id)
      ) {
        if (currentDate === data.start_date) {
          if (detailsTask.start_date === currentDate) {
            setTasksToday((prev) =>
              prev.map((el) =>
                el.id === data.id
                  ? {
                      created_at: el.created_at,
                      creator: el.creator,
                      ...data,
                      stage_id: data.stage_id,
                    }
                  : el
              )
            );
          } else {
            setTasksToday((prev) => [data, ...prev]);
            setTotalTasksToday(totalTasksToday + 1);
            setActiveKey(["2"]);
            // setCountTasks((prev) => ({ ...prev, all: prev.all + 1 }));$
            if (tasksHistory.find((el) => el.id === data.id)) {
              setTasksHistory((prev) => prev.filter((el) => el.id !== data.id));

              setTotalTasksHistory(totalTasksHistory - 1);
            }
            if (tasksUpcoming.find((el) => el.id === data.id)) {
              setTasksUpcoming((prev) =>
                prev.filter((el) => el.id !== data.id)
              );
              setTotalTasksUpComing(totalTasksUpComing - 1);
              // setCountTasks((prev) => ({
              //   ...prev,
              //   upcoming: prev.upcoming - 1,
              // }));
            }
          }
        } else if (
          start_date.isAfter(
            moment(currentDate, user.location.date_format),
            "day"
          )
        ) {
          if (
            moment(detailsTask.start_date, user.location.date_format).isAfter(
              moment(currentDate, user.location.date_format),
              "day"
            )
          ) {
            setTasksUpcoming((prev) =>
              prev.map((el) =>
                el.id === data.id
                  ? { created_at: el.created_at, creator: el.creator, ...data }
                  : el
              )
            );
          } else {
            setTasksUpcoming((prev) => [data, ...prev]);
            setTotalTasksUpComing(totalTasksUpComing + 1);
            // setCountTasks((prev) => ({
            //   ...prev,
            //   upcoming: prev.upcoming + 1,
            // }));
            setActiveKey(["1"]);

            if (tasksToday.find((el) => el.id === data.id)) {
              setTasksToday((prev) => prev.filter((el) => el.id !== data.id));

              setTotalTasksToday(totalTasksToday - 1);
            }
            if (tasksHistory.find((el) => el.id === data.id)) {
              setTasksHistory((prev) => prev.filter((el) => el.id !== data.id));
              setTotalTasksHistory(totalTasksHistory - 1);
            }
          }
          // dispatch(setLastPage(Math.ceil((countUpComing + 1) / 10)));
        } else if (
          start_date.isBefore(
            moment(currentDate, user.location.date_format),
            "day"
          )
        ) {
          // dispatch(
          //   setLater({ later: later + 1, countUpComing: countUpComing + 1 })
          // );
          if (
            moment(detailsTask.start_date, user.location.date_format).isBefore(
              moment(currentDate, user.location.date_format),
              "day"
            )
          ) {
            setTasksHistory((prev) =>
              prev.map((el) =>
                el.id === data.id
                  ? { created_at: el.created_at, creator: el.creator, ...data }
                  : el
              )
            );
          } else {
            setActiveKey(["3"]);

            setTasksHistory((prev) => [data, ...prev]);
            setTotalTasksHistory(totalTasksHistory + 1);
            if (tasksToday.find((el) => el.id === data.id)) {
              setTasksToday((prev) => prev.filter((el) => el.id !== data.id));

              setTotalTasksToday(totalTasksToday - 1);
            }
            if (tasksUpcoming.find((el) => el.id === data.id)) {
              setTasksUpcoming((prev) =>
                prev.filter((el) => el.id !== data.id)
              );
              setTotalTasksUpComing(totalTasksUpComing - 1);
              // setCountTasks((prev) => ({
              //   ...prev,
              //   upcoming: prev.upcoming - 1,
              // }));
            }
          }
          // dispatch(setLastPage(Math.ceil((countUpComing + 1) / 10)));
        }
      } else {
        if (tasksHistory.find((el) => el.id === data.id)) {
          setTasksHistory((prev) => prev.filter((el) => el.id !== data.id));
          setTotalTasksHistory(totalTasksHistory - 1);
          // setCountTasks((prev) => ({
          //   ...prev,
          //   all: prev.all - 1,
          // }));
        } else if (tasksToday.find((el) => el.id === data.id)) {
          setTasksToday((prev) => prev.filter((el) => el.id !== data.id));
          setTotalTasksToday(totalTasksToday - 1);
          // setCountTasks((prev) => ({
          //   ...prev,
          //   all: prev.all - 1,
          // }));
        } else if (tasksUpcoming.find((el) => el.id === data.id)) {
          setTasksUpcoming((prev) => prev.filter((el) => el.id !== data.id));
          setTotalTasksUpComing(totalTasksUpComing - 1);
          // setCountTasks((prev) => ({
          //   ...prev,
          //   all: prev.all - 1,
          //   upcoming: prev.upcoming - 1,
          // }));
        }
      }
    }
    // if (
    //   (tabKey === 1 && currentDate === data.data.start_date) ||
    //   (tabKey === 2 && start_date.isAfter(currentDate, "day"))
    // ) {
    // const updatedListMeet = [{ ...data.data, newMeet: true }, ...listMeet];
    // dispatch(setListMeet(updatedListMeet));

    updateCountTasks();
  };
  return (
    <Space direction="vertical" style={{ width: "100%" }} size="large">
      <div className="-mx-4">
        <div className="px-4 pb-4">
          <Row gutter={16}>
            {Object.entries(countTasks)
              .slice(0, -1)
              .map(([key, value]) => (
                <Col span={6} key={key}>
                  <CardStat item={{ title: t(`vue360.${key}`), value }} />
                  {/* <Card bordered={true}>
                      <Statistic
                        title={t(`vue360.${key}`)}
                        value={value}
                        valueStyle={{
                          color:
                            key === "overdue"
                              ? "#cf1322"
                              : key === "all"
                              ? "rgb(59 130 246)"
                              : key === "upcoming"
                              ? "black"
                              : "#3f8600",
                        }}
                      />
                    </Card> */}
                </Col>
              ))}
          </Row>
        </div>
        <div className="flex w-full items-center px-4">
          <div className="inline-flex  items-center space-x-2">
            <>
              <SearchInTable
                disabled={
                  tasksToday.length +
                    tasksUpcoming.length +
                    tasksHistory.length ===
                  0
                }
              />
              {listFilter?.selected?.length === 0 &&
              countTasks.all === 0 ? null : (
                <>
                  <Segmented
                    onChange={(e) => {
                      setSingleTaskData("");
                      setIdTask("");
                      setView(e);
                    }}
                    value={view}
                    options={[
                      {
                        value: "List",
                        icon: (
                          <Tooltip title={t("tasks.listView")}>
                            <UnorderedListOutlined />
                          </Tooltip>
                        ),
                      },
                      {
                        value: "Table",
                        icon: (
                          <Tooltip title={t("tasks.tableViewTooltip")}>
                            <TableOutlined />
                          </Tooltip>
                        ),
                      },
                    ]}
                  />
                  {view === "Table" ? (
                    <Segmented
                      options={[
                        // {
                        //   label: t("helpDesk.all"),
                        //   value: 3,
                        // },
                        {
                          label: t("visio.today"),
                          value: 1,
                        },
                        {
                          label: t("visio.upComing"),
                          value: 2,
                        },
                        {
                          label: t("visio.history"),
                          value: 0,
                        },
                      ]}
                      value={dateViewTable}
                      onChange={(e) => setDateViewTable(e)}
                    />
                  ) : null}
                </>
              )}
            </>
          </div>
          <div className="flex w-full ">
            {/* <Header
              editingKey={""}
              handleAdd={() => setOpenTaskAdvanced(true)}
              btnText={t("voip.createTask")}
              disabled={initLoading}
              data={detailsTask}
              api="tasks-360"
              setFilter={setFilter}
              filters={tasksTypes.map((el) => ({
                value: el.id,
                text: <Badge color={el.color || "white"} text={el.label} />,
              }))}
              tasks={tasksTypes}
              selectedFilter={listFilter?.selected}
              setActiveKey={setActiveKey}
              count={countTasks}
            /> */}
            {/* {listFilter?.selected?.length === 0 &&
            countTasks.all === 0 ? null : ( */}
            <div className="flex w-full flex-1  ">
              <div ref={divRef} className="flex w-full justify-end">
                <FilterTable
                  setFilter={setFilter}
                  filters={tasksTypes.map((el) => ({
                    value: el.id,
                    text: <Badge color={el.color || "white"} text={el.label} />,
                  }))}
                  t={t}
                  openFilterTable={openFilterTable}
                  setOpenFilterTable={setOpenFilterTable}
                  disabled={
                    initLoading ||
                    (listFilter?.selected?.length === 0 && countTasks.all === 0)
                  }
                  selectedFilter={listFilter?.selected}
                  tasks={tasksTypes}
                  widthTooltip={Number(divWidth) - 50}
                />
              </div>
            </div>
            {/* )} */}
            {user?.role !== "guest" && (
              <Button
                icon={<PlusOutlined />}
                onClick={() => setOpenTaskAdvanced(true)}
                type="primary"
              >
                {t("voip.createTask")}
              </Button>
            )}
            {/* <span>
                <GenericButton
                  onClick={handleAdd}
                  type="primary"
                  disabled={disabled}
                  // text={btnText}
                  shape="circle"
                />
              </span> */}
          </div>
        </div>
      </div>
      {/* <Tabs
        defaultActiveKey="1"
        items={items}
        activeKey={activeActivity360}
        className={
          location.pathname.includes("v3") ? "" : `tabs-360-activities`
        }
        onChange={(e) => {
          // setLoad(true);
          setList([]);
          setPageTasksHistory(1);
          setPageTasksToday(1);
          setPageTasksUpComing(1);
          setLastPageTasksHistory(1);
          setLastPageTasksToday(1);
          setLastPageTasksUpComing(1);
          dispatch(setActiveActivity360(e));

          //   setChoiceCard((prev) => ({
          //     card1: true,
          //     card2: false,
          //     card3: false,
          //     card4: false,
          //   }));
        }}
        // value={selectedValue}
      /> */}
      {view === "Table" ? (
        <div className="space-y-1">
          <TasksTableView
            t={t}
            form={form}
            collapsedInViewSphere={collapsed}
            tasksData={
              dateViewTable === 1
                ? tasksToday
                : dateViewTable === 2
                ? tasksUpcoming
                : tasksHistory
            }
            setDetailsTask={setDetailsTask}
            loadUpdateTaskStage={loadUpdateTaskStage}
            setLoadUpdateTaskStage={setLoadUpdateTaskStage}
            loadOwners={loadOwners}
            setTasksData={
              dateViewTable === 1
                ? setTasksToday
                : dateViewTable === 2
                ? setTasksUpcoming
                : setTasksHistory
            }
            tasksTypes={tasksTypes}
            loadTasks={initLoading}
            setPipelines={setPipelines}
            pipelines={pipelines}
            setTaskToUpdate={setIdTask}
            ownersList={ownersList}
            guestsList={guestsList}
            showTime={showTime}
            setShowTime={setShowTime}
            setPageNumber={
              dateViewTable === 1
                ? setPageTasksToday
                : dateViewTable === 2
                ? setPageTasksUpComing
                : setPageTasksHistory
            }
            setLimit={setLimit}
            total={
              dateViewTable === 1
                ? totalTasksToday
                : dateViewTable === 2
                ? totalTasksUpComing
                : totalTasksHistory
            }
            setTotal={
              dateViewTable === 1
                ? setTotalTasksToday
                : dateViewTable === 2
                ? setTotalTasksUpComing
                : setTotalTasksHistory
            }
            UpdateTaskStage={UpdateTaskStage}
            stageIdToFilter={stageIdToFilter}
            setStageIdToFilter={setStageIdToFilter}
            setOpenActivity360={setOpenActivity360}
            // selectedPipeline={selectedPipeline}
            source="viewSphere"
            getTasks={getAllTasks}
            setOpenDrawerEditTask={setOpen}
            // getTasksError={getTasksError}
            // setOpenElementDetails={setOpenElementDetails}
            // setElementDetails={setElementDetails}
            // checkedColumns={checkedColumns}
            // setOpenActivity360={setOpenActivity360}
            pageNumber={
              dateViewTable === 1
                ? pageTasksToday
                : dateViewTable === 2
                ? pageTasksUpComing
                : pageTasksHistory
            }
            setActivityLabel={setActivityLabel}
            // setAppliedFilters={setAppliedFilters}
            // setRoomActivityId={setRoomActivityId}
            setUpdateFilters={setUpdateFilters}
            headerHeight={headerHeight}
            setRoomActivityId={setRoomActivityId}
            guestsSearchQuery={guestsSearchQuery}
            setGuestsSearchQuery={setGuestsSearchQuery}
            guestsListPage={guestsListPage}
            setGuestsListPage={setGuestsListPage}
            guestsListLastPage={guestsListLastPage}
            setCheckedItems={setCheckedItems}
            checkedItems={checkedItems}
            checkedFollowers={checkedFollowers}
            setCheckedFollowers={setCheckedFollowers}
            setFollowersSearchQuery={setFollowersSearchQuery}
            followersSearchQuery={followersSearchQuery}
            followersListLastPage={followersListLastPage}
            totalEntities={totalEntities}
            setOwnersList={setOwnersList}
            followersListPage={followersListPage}
            setFollowersListPage={setFollowersListPage}
            loadGuests={loadGuests}
            setSelectedFamilyMembers={setSelectedFamilyMembers}
          />
        </div>
      ) : view === "List" ? (
        <>
          <ListActivities
            headerHeight={headerHeight}
            setOpenTaskAdvanced={setOpenTaskAdvanced}
            initLoading={initLoading}
            detailsTask={detailsTask}
            setFilter={setFilter}
            tasksTypes={tasksTypes}
            setActiveKey={setActiveKey}
            activeKey={activeKey}
            countTasks={countTasks}
            tasksHistory={tasksHistory}
            tasksToday={tasksToday}
            tasksUpcoming={tasksUpcoming}
            getItems={getItems}
            panelStyle={panelStyle}
            listFilter={listFilter}
            dataSteps={dataSteps}
            from={from}
          />
          {!initLoading &&
            tasksHistory.length === 0 &&
            tasksToday.length === 0 &&
            tasksUpcoming.length === 0 &&
            (activeActivity360 === "4" ||
              activeActivity360 === "5" ||
              activeActivity360 === "1" ||
              activeActivity360 === "6") && (
              <Empty
                // image="https://gw.alipayobjects.com/zos/antfincdn/ZHrcdLPrvN/empty.svg"
                imageStyle={{
                  height: 60,
                }}
                // description={
                //   <span>
                //     Customize <a href="#API">Description</a>
                //   </span>
                // }
              >
                {/* <Button
                icon={<PlusOutlined />}
                type="primary"
                onClick={() => setOpenTaskAdvanced(true)}
              >
                {t("voip.createTask")}
              </Button> */}
              </Empty>
            )}
        </>
      ) : (
        ""
      )}
      {/* {selectedValue ? (
        <AllTasks
        setOpenChat={setOpenChat}
              setSelectedKeySideBar={setSelectedKeySideBar}
          tasksTypes={tasksTypes}
          list={list}
          setList={setList}
          initLoading={initLoading}
          setInitLoading={setInitLoading}
          setDetailsTask={setDetailsTask}
          setIdTask={setIdTask}
        />
      ) : selectedValue == "3" ? (
        <Mails />
      ) : (
        ""
      )} */}

      {idTask && !openActivity360 && (
        <UpdateTask
          from="home"
          id={idTask}
          externeUpdate={externeUpdate}
          setId={setIdTask}
          data={{
            ...detailsTask,
            upload:
              detailsTask?.upload == 0
                ? []
                : Array.isArray(detailsTask?.upload)
                ? detailsTask?.upload
                : detailsTask?.files,
          }}
          updateTaskfrom360={updateTaskFrom360}
          // setData={setList}
          tasksTypes={tasksTypes}
          pipelines={pipelines}
          ownersList={ownersList}
          setOwnersList={setOwnersList}
          guestsList={guestsList}
          setGuestsList={setGuestsList}
          guestsListPage={guestsListPage}
          setGuestsListPage={setGuestsListPage}
          followersListPage={followersListPage}
          setFollowersListPage={setFollowersListPage}
          followersListLastPage={followersListLastPage}
          setFollowersListLastPage={setFollowersListLastPage}
          setGuestsListLastPage={setGuestsListLastPage}
          guestsListLastPage={guestsListLastPage}
          setFollowersSearchQuery={setFollowersSearchQuery}
          followersSearchQuery={followersSearchQuery}
          setGuestsSearchQuery={setGuestsSearchQuery}
          guestsSearchQuery={guestsSearchQuery}
          open={open}
          setOpen={setOpen}
          activityLabel={detailsTask?.label}
        />
      )}
      {!idTask && openTaskAdvanced ? (
        <CreateTask
          open={openTaskAdvanced}
          setOpen={setOpenTaskAdvanced}
          mask={true}
          listVisio={true}
          addTask={addTask}
          fromVue360={true}
          relations={relations}
        />
      ) : (
        ""
      )}
      {openTaskRoomDrawer ? (
        <TasksRoom key={roomActivityId} elementId={roomActivityId} />
      ) : null}
      <Activity360
        key={idTask ? idTask : 1}
        openActivity360={openActivity360}
        setOpenActivity360={setOpenActivity360}
        taskToUpdate={idTask}
        singleTaskData={singleTaskData}
        setSingleTaskData={setSingleTaskData}
        loadSpecificTask={loadSpecificTask}
        tasksTypes={tasksTypes}
        setTaskToUpdate={setIdTask}
        pipelines={pipelines}
        guestsList={guestsList}
        checkedItems={checkedItems}
        guestsSearchQuery={guestsSearchQuery}
        setGuestsSearchQuery={setGuestsSearchQuery}
        guestsListPage={guestsListPage}
        setGuestsListPage={setGuestsListPage}
        guestsListLastPage={guestsListLastPage}
        setCheckedItems={setCheckedItems}
        ownersList={ownersList}
        checkedFollowers={checkedFollowers}
        setCheckedFollowers={setCheckedFollowers}
        setFollowersSearchQuery={setFollowersSearchQuery}
        loadOwners={loadOwners}
        loadGuests={loadGuests}
        addOnsValues={addOnsValues}
        setAddOnsValues={setAddOnsValues}
        files={files}
        setFiles={setFiles}
        countChanges={countChanges}
        setCountChanges={setCountChanges}
        setSelectedFamilyMembers={setSelectedFamilyMembers}
        form={form}
        setShowCardPopover={setShowCardPopover}
        totalEntities={totalEntities}
        setFollowersListPage={setFollowersListPage}
      />
    </Space>
  );
};
export default TimeLine;
