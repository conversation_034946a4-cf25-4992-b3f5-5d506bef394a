/*** Table ***/
.table-view .ant-table-body::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

.table-view .ant-table-body::-webkit-scrollbar-thumb {
  -webkit-border-radius: 10px;
  /* border-radius: 10px; */
  background: rgba(0,0,0,0.2);
  border-radius: 2px;
}

.table-view .ant-table-body::-webkit-scrollbar-track {
  background-color: white;
  -webkit-border-radius: 10px;
  /* border-radius: 10px; */
  border-radius: 2px;
}

.table-view .ant-table-bordered .ant-table-thead > tr > th {
    border: 1px  #f0f0f0;
  }
  
.table-view .ant-table-bordered .ant-table-tbody > tr > td {
    border: none !important;
    border-bottom: 1px solid #f0f0f0 !important;
  }

.table-view .table-header-left-align {
  text-align: left !important;
}

.table-view-families-cell .ant-table-cell {
  padding: 6px 8px !important;
}

/*** Drawer ***/
.overflow-content::-webkit-scrollbar {
  width: 7px;
}
.overflow-content::-webkit-scrollbar-thumb {
  /* -webkit-border-radius: 10px;
    border-radius: 10px; */
  background: #dadada;
}
.overflow-content::-webkit-scrollbar-track {
  background-color: #f4f4f4;
  /* -webkit-border-radius: 10px; */
  /* border-radius: 10px; */
}

.overflow-content::-webkit-scrollbar-thumb:hover {
  background: #dadada;
  border-radius: 0px;
}

/*** Form ***/
/* .ant-form-item {
    padding-bottom: 10px;
  } */

/*** Card ***/
.ant-card .ant-card-body {
  padding: 10px;
}

/**********/
.my-div::-webkit-scrollbar {
  width: 8px;
}

/* .my-div::-webkit-scrollbar-track {
  background-color: #f5f5f5;
} */

.my-div::-webkit-scrollbar-thumb {
  background-color: #ccc;
  border-radius: 10px;
}

/* .my-div::-webkit-scrollbar-thumb:hover {
  background-color: #aaa;
} */

/*******Form Create and  Update********/
.flex-column-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.tooltip-container {
  width: 120px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
}

.overflow-content {
  overflow-y: auto;
  padding: 0 1.2rem 1rem 0;
}
.relation-family .ant-collapse > .ant-collapse-item > .ant-collapse-header {
  padding: 12px 0px;
}

.relation-card .ant-card .ant-card-meta-detail > div:not(:last-child) {
  margin-bottom: 0px !important;
}

.badge-etat .ant-badge.ant-badge-status .ant-badge-status-dot {
  width: 6.5px;
  height: 6.5px;
}
