import { SET_DRIVE_PARENT_ITEMS, SET_DRIVE_SELECTED_FOLDER, SET_DRIVE_BREADCRUMB, SET_DRIVE_TREE_DATA, UPDATE_DRIVE_TREE_NODE, SET_DRIVE_SEARCH, SET_DRIVE_SEARCH_INPUT, CLEAR_DRIVE_SEARCH } from "../../constants";

export const setDriveParentItems = (items) => {
  return {
    type: SET_DRIVE_PARENT_ITEMS,
    payload: items,
  };
};

export const setDriveSelectedFolder = (folderId) => {
  return {
    type: SET_DRIVE_SELECTED_FOLDER,
    payload: folderId,
  };
};

export const setDriveBreadcrumb = (breadcrumb) => {
  return {
    type: SET_DRIVE_BREADCRUMB,
    payload: breadcrumb,
  };
};

export const setDriveTreeData = (treeData) => {
  return {
    type: SET_DRIVE_TREE_DATA,
    payload: treeData,
  };
};

export const updateDriveTreeNode = (key, children) => {
  return {
    type: UPDATE_DRIVE_TREE_NODE,
    payload: { key, children },
  };
};

export const setDriveSearch = (search) => {
  return {
    type: SET_DRIVE_SEARCH,
    payload: search,
  };
};

export const setDriveSearchInput = (searchInput) => {
  return {
    type: SET_DRIVE_SEARCH_INPUT,
    payload: searchInput,
  };
};

export const clearDriveSearch = () => {
  return {
    type: CLEAR_DRIVE_SEARCH,
  };
};
