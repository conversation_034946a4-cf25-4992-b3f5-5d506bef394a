import React from "react";
import { Button, Empty } from "antd";
import { PlusOutlined } from "@ant-design/icons";

const EmptyPage = ({ heroTitle, mainBtnTitle, handleMainBtnClick }) => {
  return (
    <div className="m-auto w-[60%] pt-[25px]">
      <Empty
        image="https://gw.alipayobjects.com/zos/antfincdn/ZHrcdLPrvN/empty.svg"
        description={heroTitle}
      >
        {mainBtnTitle ? (
          <Button
            onClick={handleMainBtnClick}
            type="primary"
            icon={<PlusOutlined />}
          >
            {mainBtnTitle}
          </Button>
        ) : null}
      </Empty>
    </div>
  );
};

export default EmptyPage;
