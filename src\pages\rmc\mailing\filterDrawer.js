import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Drawer,
  Form,
  Radio,
  Row,
  Select,
  Space,
  Switch,
} from "antd";

import { URL_ENV } from "index";
import {
  setFilterEmail,
  setFilterEmailActive,
  setPage,
} from "new-redux/actions/mail.actions";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";

import MainService from "services/main.service";
// import FamiliesOption from "./utils/families";
import useFamiliesOption from "./components/families";
import { affectations } from "pages/voip/services/services";
import { toastNotification } from "components/ToastNotification";
import ChoiceIcons from "pages/components/ChoiceIcons";
import {
  disabledDate,
  formatDatePickerRange,
  handleDateTimeRange,
  rangePresets,
} from "pages/voip/helpers/helpersFunc";
import dayjs from "dayjs";
import { MdRadioButtonChecked } from "react-icons/md";

const FilterDrawer = ({
  assignedType,
  chrono,
  dataAssignedFilter,
  affectation,
  assignedNames,
  setAssignedType,
  setChrono,
  setDataAssignedFilter,
  setAffectation,
  setAssignedNames,
  setDataAssignedNames,
  selectedFamily,
  setSelectedFamily,
  dataQualifFilter,
  setDataQualifFilter,
  dataTags,
  dateRange,
  setDateRange,
  selectedStatus,
  setSelectedStatus,
}) => {
  //
  // console.log({ dateRange, selectedStatus });
  //
  const [dataUsers, setDataUsers] = useState([]);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [loadingOptions, setLoadingOptions] = useState(false);

  const [affectationOptions, setAffectationOptions] = useState(null);

  const [showTime, setShowTime] = useState(null);

  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const [t] = useTranslation("common");
  const { user } = useSelector(({ user }) => user);
  const { dataAccounts, filterEmail, mailFilter } = useSelector(
    (state) => state.mailReducer
  );
  const usedAccount = useMemo(
    () => dataAccounts?.find((item) => item.selected === true),
    [dataAccounts]
  );
  const { Option } = Select;
  const familiesOptions = useFamiliesOption();
  const onClose = () => {
    dispatch(setFilterEmail(false));
  };

  const getUsers = useCallback(async () => {
    setLoadingUsers(true);
    var formData = new FormData();
    formData.append("account_id", usedAccount?.value);
    formData.append("filter", 1);
    for (let i = 0; i < usedAccount?.dispatcheur?.length; i++) {
      formData.append("dispatcheur[]", usedAccount?.dispatcheur[i]);
    }
    try {
      const response = await MainService.getUsers(formData);
      if (response?.status === 200) {
        setDataUsers(response?.data?.data);
        setLoadingUsers(false);
      }
    } catch (error) {
      setLoadingUsers(false);
      console.log(error);
    }
  }, [assignedType]);

  const guestOptionsInSelect =
    dataUsers &&
    dataUsers?.map((element) => ({
      //   label: element?.label_data,
      label: (
        <div className="flex">
          <div className="_avatar__">
            {element?.avatar?.length > 0 ? (
              <Avatar
                style={{
                  // backgroundColor: "#c41d7f",
                  marginRight: "10px",
                  marginBottom: "4px",
                }}
                size={22}
                src={`${
                  URL_ENV?.REACT_APP_BASE_URL +
                  URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                }${element?.avatar}`}
              />
            ) : (
              <Avatar
                style={{
                  backgroundColor: "#c41d7f",
                  marginRight: "10px",
                  marginBottom: "4px",
                }}
                size={22}
              >
                {element?.label_data?.charAt(0)?.toUpperCase()}
              </Avatar>
            )}
          </div>
          <div className="w-[75%] ">{element?.label_data}</div>

          <div className="font-semibold ">{element?.kpi_Assign}</div>
        </div>
      ),
      value: element?.owner,
      searchOption: element?.label_data,
    }));

  const getAffectationOptions = async () => {
    setLoadingOptions(true);
    try {
      const {
        data: { data },
      } = await affectations(selectedFamily || selectedFamily, user?.id, "");
      const fetchedOptions = data?.map((e) => ({
        label: `${e.label}${e.reference ? ` - ${e.reference}` : ""}`,
        value: `${e._id}`,
        key: e._id,
      }));
      setLoadingOptions(false);
      setAffectationOptions(fetchedOptions);
    } catch (err) {
      setLoadingOptions(false);
      toastNotification("error", t("toasts.somethingWrong"), "topRight");
    }
  };

  const ResetFilter = () => {
    setAssignedType(4);
    setChrono(0);
    setDataAssignedFilter([]);
    setAffectation([]);
    setDataAssignedNames([]);
    setSelectedFamily(null);
    setDataQualifFilter([]);
    setDateRange([null, null]);
    setSelectedStatus([]);
    dispatch(setPage(1));
  };

  const filterOption = (input, option) =>
    (option?.searchOption ?? "").toLowerCase()?.includes(input.toLowerCase());

  useEffect(() => {
    form.setFieldsValue({
      chrono: chrono,
      assign: assignedType,
      affectation: affectation,
      qualification: dataQualifFilter,
      dateRange:
        dateRange[0] && dateRange[1]
          ? [
              dayjs(
                dateRange[0],
                `${user.location?.date_format} ${
                  showTime ? user.location?.time_format : ""
                }`
              ),
              dayjs(
                dateRange[1],
                `${user.location?.date_format} ${
                  showTime ? user.location?.time_format : ""
                }`
              ),
            ]
          : [null, null],
      status: selectedStatus,
    });
  }, [
    form,
    chrono,
    assignedType,
    affectation,
    dataQualifFilter,
    dateRange,
    selectedStatus,
  ]);

  useEffect(() => {
    if (assignedType === 3) {
      getUsers();
      // return () => {
      //   dispatch(setFilterEmail(false));
      // };
    }
  }, [getUsers]);

  useEffect(() => {
    if (selectedFamily) getAffectationOptions();
  }, [selectedFamily]);

  useEffect(() => {
    // dispatch(setPage(1));
    if (
      assignedType !== 4 ||
      chrono !== 0 ||
      assignedNames.length > 0 ||
      affectation.length > 0 ||
      dataQualifFilter.length > 0 ||
      selectedStatus.length > 0 ||
      (dateRange?.[0] && dateRange?.[1])
    ) {
      dispatch(setFilterEmailActive(true));
    } else {
      dispatch(setFilterEmailActive(false));
    }
  }, [
    assignedType,
    chrono,
    assignedNames,
    affectation,
    dataQualifFilter,
    dateRange,
    selectedStatus,
    dispatch,
  ]);
  //
  // console.log({
  //   assignedType,
  //   chrono,
  //   dataAssignedFilter,
  //   assignedNames,
  //   affectation,
  //   dataQualifFilter,
  // });
  // //
  useEffect(() => {
    // const filter = mailFilter
    if (usedAccount?.shared !== "1") return;
    const accountId = usedAccount.value;
    const obj = {
      assignedType,
      chrono,
      dataQualifFilter,
      assignedNames,
      dataAssignedFilter,
      dateRange,
      selectedStatus,
    };
    const newFilter = {
      ...mailFilter,
      [accountId]: obj,
    };
    dispatch({ type: "SET_FILTER_MAIL", payload: newFilter });
    // localStorage.setItem("mailFilter", JSON.stringify(obj));
  }, [
    assignedNames,
    assignedType,
    chrono,
    dataAssignedFilter,
    dataQualifFilter,
    dateRange,
    selectedStatus,
    usedAccount,
  ]);
  //
  const handleSwitchChange = (checked) => {
    if (checked) {
      setShowTime({
        format: "HH:mm",
        defaultValue: [dayjs("00:00", "HH:mm"), dayjs("23:59", "HH:mm")],
      });
    } else {
      setShowTime(null);
    }
  };

  return (
    <Drawer
      style={{
        width: "390px",
      }}
      title={t("mailing.filterTitle")}
      onClose={onClose}
      open={filterEmail}
      mask={false}
      footer={
        <div className="flex justify-center">
          <Button onClick={() => ResetFilter()}>
            {t("mailing.filterReset")}
          </Button>
        </div>
      }
    >
      <Form name="basic" layout="vertical" form={form}>
        <Form.Item label="Date" name="dateRange">
          <DatePicker.RangePicker
            // disabled={!dataSource?.length && !dateRange[0] && !dateRange[1]}
            style={{ width: "100%" }}
            placement="bottomRight"
            presets={rangePresets(t)}
            // value={}
            onChange={
              (date, dateString) => {
                setDateRange(dateString);
                dispatch(setPage(1));
              }
              // handleDateTimeRange(
              //   dateString,
              //   setDateRange,
              //   showTime !== null,
              //   user?.location
              // )
            }
            // format={formatDatePickerRange(showTime, user?.location)}
            format={`${user.location?.date_format} ${user.location?.time_format}`}
            showTime={showTime}
            renderExtraFooter={() => (
              <div className="ml-2">
                <span>{t("voip.displayTime")}: </span>
                <Switch
                  size="small"
                  // defaultChecked
                  onChange={handleSwitchChange}
                />
              </div>
            )}
            allowClear
            disabledDate={(current) => disabledDate(null, current)}
          />
        </Form.Item>
        <Form.Item label={t("mailing.Assign")} name="assign">
          <Row>
            <Radio.Group
              onChange={(e) => {
                setDataAssignedFilter([]);
                setAssignedType(e.target.value);
                dispatch(setPage(1));
              }}
              value={assignedType}
            >
              <Radio value={4}>{t("mailing.Default")}</Radio>
              <Radio value={1}>{t("mailing.all")}</Radio>
              <Radio value={2}>{t("mailing.MyMode")}</Radio>
              <Radio value={5}>{t("tasks.aucun")}</Radio>
              <Radio value={3}>{t("mailing.RelativeTo")}</Radio>
            </Radio.Group>
          </Row>

          <Row style={{ marginTop: "10px" }}>
            {assignedType === 3 ? (
              <Select
                mode="multiple"
                loading={loadingUsers}
                style={{ width: "341px" }}
                placeholder={
                  loadingUsers
                    ? t("mailing.ThanksToAssign") + "..."
                    : t("mailing.ThanksToAssign")
                }
                showSearch
                optionFilterProp={["label", "searchOption"]}
                filterOption={filterOption}
                onChange={(value, option) => {
                  setDataAssignedFilter(value);
                  setAssignedNames(option);
                  dispatch(setPage(1));
                }}
                options={guestOptionsInSelect}
                value={dataAssignedFilter}
              ></Select>
            ) : null}
          </Row>
        </Form.Item>

        <Form.Item label="Affectation" name="affectation">
          <Row>
            <Select
              style={{ width: "100%" }}
              placeholder={t("voip.selectModule")}
              options={familiesOptions}
              onChange={(e) => {
                setSelectedFamily(e);
                setAffectation([]);
                setDataAssignedNames([]);
                dispatch(setPage(1));
                // getAffectationOptions(e);
              }}
              value={selectedFamily}
            />
          </Row>
          <Row style={{ marginTop: "10px" }}>
            {selectedFamily ? (
              <Select
                mode="multiple"
                loading={loadingOptions}
                style={{ width: "341px" }}
                placeholder={t("voip.selectModule")}
                options={affectationOptions}
                onChange={(value, option) => {
                  setAffectation(value);
                  setDataAssignedNames(option);
                  dispatch(setPage(1));
                }}
                value={affectation}
              />
            ) : null}
          </Row>
        </Form.Item>

        <Form.Item label="Qualification" name="qualification">
          <Select
            placeholder={t("voip.selectQualify")}
            allowClear
            showSearch
            popupMatchSelectWidth={false}
            mode="multiple"
            optionLabelProp="label"
            filterOption={(input, option) =>
              option?.label.toLowerCase().includes(input.toLowerCase())
            }
            onChange={(values, options) => {
              setDataQualifFilter(options);
              dispatch(setPage(1));
            }}
            value={dataQualifFilter}
          >
            {dataTags?.map((item) => (
              <Option
                key={item?.id}
                value={item?.id}
                label={item?.label}
                color={item?.color}
                icon={item?.icon}
              >
                <Space>
                  <ChoiceIcons icon={item?.icon} />
                  <span style={{ color: item?.color }}> {item?.label}</span>
                </Space>
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item label={t("helpDesk.status")} name="status">
          <Select
            allowClear
            showSearch
            mode="multiple"
            optionFilterProp="text"
            style={{ width: "100%" }}
            placeholder="Sélectionner statut"
            onChange={(values, options) => {
              // console.log({ options });
              setSelectedStatus(values);
              dispatch(setPage(1));
            }}
            // value={selectedStatus}
            options={[
              {
                value: "new",
                label: (
                  <div className="flex items-center space-x-2">
                    <MdRadioButtonChecked
                      style={{
                        fontSize: 14,
                        color: "rgb(107 114 128)",
                      }}
                    />
                    <span>{t("mailing.new")}</span>
                  </div>
                ),
                text: t("mailing.new"),
              },
              {
                value: "in-progress",
                label: (
                  <div className="flex items-center space-x-2">
                    <MdRadioButtonChecked
                      style={{
                        fontSize: 14,
                        color: "#49a7e9",
                      }}
                    />
                    <span>{t("mailing.inProgress")}</span>
                  </div>
                ),
                text: t("mailing.inProgress"),
              },
              {
                value: "processed",
                label: (
                  <div className="flex items-center space-x-2">
                    <MdRadioButtonChecked
                      style={{
                        fontSize: 14,
                        color: "#a149e9",
                      }}
                    />
                    <span>{t("mailing.processed")}</span>
                  </div>
                ),
                text: t("mailing.processed"),
              },
              {
                value: "closed",
                label: (
                  <div className="flex items-center space-x-2">
                    <MdRadioButtonChecked
                      style={{
                        fontSize: 14,
                        color: "#3cca5d",
                      }}
                    />
                    <span>{t("mailing.closed")}</span>
                  </div>
                ),
                text: t("mailing.closed"),
              },
            ]}
          />
        </Form.Item>

        <Form.Item label="Chrono" name="chrono">
          <Radio.Group
            onChange={(e) => {
              setChrono(e.target.value);
              dispatch(setPage(1));
            }}
            value={chrono}
          >
            <Radio value={0}>{t("voip.no")}</Radio>
            <Radio value={1}>{t("voip.yes")}</Radio>
            <Radio value={2}>{t("mailing.outOfDeadline")}</Radio>
          </Radio.Group>
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default FilterDrawer;
