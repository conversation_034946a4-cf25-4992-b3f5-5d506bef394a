import React, { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";

import Wrapper from "./Wrapper";
import ConfigWiki2 from "./ConfigWiki2";
import { GenericTabs } from "../components/Tabs";
import { useTranslation } from "react-i18next";
import Binder from "./Binder";
import { getFolders } from "../../new-redux/actions/wiki.actions/getFolders";
import MainService from "../../services/main.service";
import { toastNotification } from "../../components/ToastNotification";
import { sortFolders } from "./sideBarDndUtils";
import { generateAxios } from "../../services/axiosInstance";
import { URL_ENV } from "index";
import { Button, message } from "antd";
import { useSelector } from "react-redux";
import { setEndBuild } from "new-redux/actions/menu.actions/menu";
import { useDispatch } from "react-redux";

const GlobalWrapper = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [selectedGroup, setSelectedGroup] = useState("");
  const [selectedNode, setSelectedNode] = useState(null);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [binders, setBinders] = useState("");
  const [folders, setFolders] = useState([]);
  const [nodeName, setNodeName] = useState("");
  const [selectedFolder, setSelectedFolder] = useState("");
  const { t } = useTranslation("common");
  const [defaultActiveTabKeyWiki, setDefaultActiveKeyWiki] = useState(null);
  const [isVisited, setIsVisited] = useState(false);
  const [loading, setLoading] = useState(false);
  const [loadGroupsWithClasseur, setLoadGroupsWithClasseur] = useState(false);
  const [selectedGroupWiki, setSelectedGroupWiki] = useState("");
  const [disabled, setDisabled] = useState(true);
  const { endBuild } = useSelector((state) => state.menu);
  const dispatch = useDispatch();
  const wikiItems = [
    {
      key: 1,
      label: t(`wiki.Docs`),
    },
    {
      key: 2,
      label: t(`wiki.Groups`),
    },
    {
      key: 3,
      label: t(`wiki.binder`),
    },
  ];
  const [displayedView, setDisplayedView] = useState("");
  const wikiLabelFromKey = (key) => {
    let index =
      wikiItems && key && wikiItems.findIndex((element) => element?.key == key);
    if (index > -1) {
      return wikiItems[index]?.label.toLowerCase();
    } else return "";
  };
  const getFolders = () => {
    setLoading(true);
    MainService.getFolders(selectedGroup)
      .then((res) => {
        let f = [];
        res.data.data.map((doc) => {
          f.push({ ...doc, selectable: false, title: doc.title_fr });
          setExpandedKeys((prevStateArray) => [...prevStateArray, doc.key]);

          f.forEach((item) => {
            item.children.forEach((child) => {
              child.title = child.title_fr;
              // child.icon = <FileOutlined
              //   className='inline'
              // />;
            });
          });
        });

        f = sortFolders(f);

        //setFolders(limitTitleFr(f, 13))
        //sort folders by rank from lowest to highest

        f = f.sort((a, b) => a.rank - b.rank);

        setFolders(f);

        setLoading(false);
      })
      .catch((err) => {
        console.log(err);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
        setLoading(false);
      });
  };
  const build = async () => {
    try {
      dispatch(setEndBuild(false));
      const res = await MainService.wikiBuild();
      toastNotification("success", res.data.message, "topRight");
    } catch (err) {
      dispatch(setEndBuild(false));
      toastNotification(
        "error",
        err && err.response && err.response.data.message,
        "topRight"
      );
    }
  };
  useEffect(() => {
    const getGroupsWithclasseur = async () => {
      setLoadGroupsWithClasseur(true);
      try {
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get("classeur-wiki-with-groups");
        setBinders(
          res.data.data.map((el) => ({
            value: el.id,
            label: el.label_fr,
            // selectable: false,
            children: el.groupe_wiki.map((grp) => ({
              value: grp.id,
              label: grp.label_fr,
            })),
          }))
        );
        setLoadGroupsWithClasseur(false);
      } catch (err) {
        setLoadGroupsWithClasseur(false);
      }
    };
    getGroupsWithclasseur();
  }, []);
  useEffect(() => {
    selectedGroup && getFolders(selectedGroup);
  }, [selectedGroup]);
  useEffect(() => {
    let splittedLocationArray = location?.pathname.split("/");

    if (
      splittedLocationArray[1] == "settings" &&
      splittedLocationArray[2] == "wiki"
    ) {
      let index =
        wikiItems &&
        wikiItems.findIndex(
          (element) =>
            element?.label.toLocaleLowerCase() == splittedLocationArray[3]
        );
      if (index > -1) {
        setDefaultActiveKeyWiki(wikiItems[index]?.key);
      }
    }

    if (location.pathname.includes("/settings/wiki/docs"))
      setDisplayedView(
        <Wrapper
          setSelectedGroupWiki={setSelectedGroupWiki}
          selectedGroup={selectedGroup}
          setSelectedGroup={setSelectedGroup}
          selectedFolder={selectedFolder}
          setSelectedFolder={setSelectedFolder}
          isVisited={isVisited}
          setIsVisited={setIsVisited}
          folders={folders}
          setFolders={setFolders}
          selectedNode={selectedNode}
          setSelectedNode={setSelectedNode}
          expandedKeys={expandedKeys}
          setExpandedKeys={setExpandedKeys}
          nodeName={nodeName}
          setNodeName={setNodeName}
          loading={loading}
          setLoading={setLoading}
          binders={binders}
          setBinders={setBinders}
          disabled={disabled}
          setDisabled={setDisabled}
          loadGroupsWithClasseur={loadGroupsWithClasseur}
          selectedGroupWiki={selectedGroupWiki}
        />
      );
    else if (
      location.pathname.includes("/settings/wiki/groupes") ||
      location.pathname.includes("/settings/wiki/groups")
    )
      setDisplayedView(
        <ConfigWiki2
          binders={binders}
          setGroupsWithBinders={setBinders}
          setSelectedGroup={setSelectedGroup}
          setSelectedGroupWiki={setSelectedGroupWiki}
          selectedGroupWiki={selectedGroupWiki}
        />
      );
    else if (
      location.pathname.includes("/settings/wiki/catalogs") ||
      location.pathname.includes("/settings/wiki/catalogues")
    )
      setDisplayedView(
        <Binder
          setGroupsWithBinders={setBinders}
          setSelectedGroup={setSelectedGroup}
          setSelectedGroupWiki={setSelectedGroupWiki}
          selectedGroupWiki={selectedGroupWiki}
        />
      );
    else setDisplayedView();
  }, [
    location,
    selectedGroup,
    selectedFolder,
    isVisited,
    folders,
    selectedNode,
    expandedKeys,
    nodeName,
    loading,
    binders,
    disabled,
    loadGroupsWithClasseur,
    selectedGroupWiki,
  ]);

  return (
    <div className="overscroll-none">
      <div className="flex items-center justify-between px-4 pt-4">
        <div className="w-full">
          <GenericTabs
            items={wikiItems}
            onChange={(e) => {
              // console.log(e);
            }}
            defaultActiveKey={defaultActiveTabKeyWiki}
            activeKey={defaultActiveTabKeyWiki}
            onClick={(key) =>
              navigate(`/settings/wiki/${wikiLabelFromKey(key)}`)
            }
          />
        </div>

        <Button
          className="self-baseline"
          onClick={build}
          loading={!endBuild}
          disabled={!endBuild}
        >
          {t("wiki.uploadContent")}{" "}
        </Button>
      </div>

      {displayedView}
    </div>
  );
};

export default GlobalWrapper;
