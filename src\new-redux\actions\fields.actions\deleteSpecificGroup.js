import { DELETE_GROUP_SUCCESS,DELETE_GROUP_LOADING, DELETE_GROUP_ERROR, IS_LOADING } from "../../constants";
import MainService from "../../../services/main.service";

export const DeleteSpecificGroup = (groupId) => async (dispatch) => {
  try {
    dispatch({ type: DELETE_GROUP_LOADING });
    const response = await MainService.removeSpecificGroup(groupId);
    dispatch({
      type: DELETE_GROUP_SUCCESS,
      payload: groupId,
    });
  } catch (error) {
    if (error?.status !== 401) {
      dispatch({
        type: DELETE_GROUP_ERROR,
        payload: error,
      });
    }
  }
};
