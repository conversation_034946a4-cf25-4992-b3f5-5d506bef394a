import React, { useCallback, useEffect, useRef, useState } from "react";
import VisioComponent from "./components/visio-component";
import { useParams } from "react-router-dom";
import { useDispatch } from "react-redux";
import {
  getTokenRoom,
  setVisoParams,
} from "../../../new-redux/actions/visio.actions/createVisio";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { store } from "../../../new-redux/store";
import { toastNotification } from "../../../components/ToastNotification";
import { HiOutlineVideoCamera } from "react-icons/hi";
import useLocalStorage from "../../../custom-hooks/useLocalStorage";
import { VISIO_URL_REDIRECT_REGEX } from "../../../utils/regex";
import { URL_ENV } from "index";
import axios from "axios";

function VisioGuest() {
  const params = useParams();
  const { name } = params;
  const { user } = useSelector((state) => state.user);
  const getVisioParams = async () => await store.getState().visio.visioParams;

  const [tokenMercure, setTokenMercure] = useState(null);
  const [isMercureConnected, setMercureConnexion] = useState(false);
  const [newToken] = useLocalStorage(
    "accessToken",
    localStorage.getItem("accessToken")
  );

  const timeout = useRef(null);
  const eventSourceRef = useRef(null);
  const isValidToken =
    newToken &&
    newToken !== "null" &&
    newToken !== undefined &&
    newToken !== "undefined" &&
    newToken !== null;
  const { t } = useTranslation("common");

  const urlLast = new URL(window.location.href);

  const dispatch = useDispatch();

  // Connection to mercure
  const connectionMercure = useCallback(async () => {
    if (
      isValidToken ||
      user?.id ||
      !urlLast.toString().match(VISIO_URL_REDIRECT_REGEX) ||
      !tokenMercure ||
      isMercureConnected
    )
      return;
    clearTimeout(timeout.current);
    let url = new URL(URL_ENV?.REACT_APP_URL_MERCURE);
    url.searchParams.append(
      "topic",
      process.env.REACT_APP_MERCURE_VISIO_TOPIC + name
    );
    url.searchParams.set("authorization", tokenMercure);

    try {
      eventSourceRef.current = new EventSource(url);

      eventSourceRef.current.onopen = async () => {
        clearTimeout(timeout.current);
        setMercureConnexion(true);
        return;
      };
      eventSourceRef.current.onerror = () => {
        clearTimeout(timeout.current);
        setMercureConnexion(false);

        eventSourceRef.current?.close();
      };
    } catch (error) {
      eventSourceRef.current?.close();
    }
  }, [isValidToken, user?.id, isMercureConnected, name, tokenMercure]);

  const getTokenMercure = useCallback(async (payload) => {
    try {
      const response = await axios.post(
        `${
          URL_ENV?.REACT_APP_OAUTH_CHAT_API +
          "/" +
          process.env.REACT_APP_SUFFIX_API
        }jwt-mercure-visio`,
        { uid_room_visio: payload.room },
        {
          headers: {
            "x-api-key": process.env.REACT_APP_API_VISIO_KEY,
            "Content-Type": "application/json",
          },
        }
      );

      if (response.status === 200) setTokenMercure(response.data.jwt_mercure);
      else throw new Error("Something went wrong, please try again!");
    } catch (error) {
      if (
        error?.response?.status === 404 &&
        error?.response.data?.message === "Room not found"
      )
        toastNotification("error", payload.errorText2, "topRight");
      else toastNotification("error", payload.errorText1, "topRight");
    }
  }, []);

  useEffect(() => {
    let mounted = true;

    if (mounted) {
      mounted = false;
      connectionMercure();
    }
    return () => {
      mounted = false;
    };
  }, [connectionMercure]);

  // mercure Even
  useEffect(() => {
    if (!isMercureConnected) return;
    if (eventSourceRef.current) {
      eventSourceRef.current?.addEventListener(
        "message",
        async (messageEvent) => {
          const message = JSON.parse(messageEvent.data);
          if (message.type_event === "moderator_join") {
            const getVisio = async () => {
              const visio = await getVisioParams();
              if (visio.name === message.room) {
                toastNotification(
                  "success",
                  t("visio.moderatorJoin"),
                  "topRight",
                  7,
                  <HiOutlineVideoCamera
                    className="text-green-500"
                    style={{ fontSize: "21px" }}
                  />
                );
                dispatch(setVisoParams({ moderator: true }));
              } else return;
            };
            getVisio();
          }
        }
      );
    }
    return () => {
      eventSourceRef.current?.close();
      eventSourceRef.current = null;
    };
  }, [dispatch, t, isMercureConnected]);
  useEffect(() => {
    const logoElement = document.getElementById("first-time-loader");
    if (logoElement) logoElement.remove();
  }, []);

  useEffect(() => {
    if (window.location.href.match(VISIO_URL_REDIRECT_REGEX)) {
      if (isValidToken) {
        localStorage.setItem("lastHref", urlLast);
        dispatch(
          getTokenRoom({
            room: name,
            errorText1: t("toasts.errorFetchApi"),
            errorText2: t("toasts.errorRoomNotFound"),
          })
        );
      }
    }
  }, [dispatch, isValidToken, t, name]);

  useEffect(() => {
    let mount = true;
    if (mount) {
      dispatch(
        getTokenRoom({
          room: name,
          external: true,
          errorText1: t("toasts.errorFetchApi"),
          errorText2: t("toasts.errorRoomNotFound"),
        })
      );
      getTokenMercure({
        room: name,
        errorText1: t("toasts.errorFetchApi"),
        errorText2: t("toasts.errorRoomNotFound"),
      });
    }

    return () => {
      mount = false;
    };
  }, [dispatch, name, t]);

  if (window.location.href.match(VISIO_URL_REDIRECT_REGEX) && isValidToken) {
    window.location.href = urlLast.origin + "/?room_visio_name=" + name;
    return null;
  } else return <VisioComponent name_room={name} />;
}

export default VisioGuest;
