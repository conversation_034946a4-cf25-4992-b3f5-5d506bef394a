import React from "react";

const FolderIcon = ({ width = "50px", height = "50px", ...props }) => {
  return (
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      xmlnsXlink="http://www.w3.org/1999/xlink" 
      width={width} 
      height={height} 
      viewBox="0 0 32 32"
      {...props}
    >
      <defs>
        <linearGradient id="a" x1="4.494" y1="-1712.086" x2="13.832" y2="-1695.914" gradientTransform="translate(0 1720)" gradientUnits="userSpaceOnUse">
          <stop offset="0" stopColor="#ffc947" />
          <stop offset="0.5" stopColor="#ffb340" />
          <stop offset="1" stopColor="#ff9d38" />
        </linearGradient>
      </defs>
      <title>folder</title>
      <path 
        d="M28.681,7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724 C21.229,0.421,19.9,0,18.681,0H4.681C3.462,0,2.681,0.781,2.681,2v28c0,1.219,0.781,2,2,2h24c1.219,0,2-0.781,2-2V9.319 C30.681,8.1,30.26,6.771,28.681,7.159z" 
        style={{ fill: "#ffc947" }} 
      />
      <path 
        d="M24.681,32h-20c-1.105,0-2-0.895-2-2V2c0-1.105,0.895-2,2-2h14c1.219,0,2.549,0.421,4.161,1.319 c0.947,0.694,2.053,1.662,3.116,2.724s2.030,2.169,2.724,3.116C29.579,8.771,30,10.1,30,11.319V30 C30,31.105,29.105,32,24.681,32z" 
        style={{ fill: "url(#a)" }} 
      />
      <path 
        d="M28.681,7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724 C21.229,0.421,19.9,0,18.681,0H4.681C3.462,0,2.681,0.781,2.681,2v28c0,1.219,0.781,2,2,2h24c1.219,0,2-0.781,2-2V9.319 C30.681,8.1,30.26,6.771,28.681,7.159z" 
        style={{ fill: "#ffb340" }} 
      />
      <polygon 
        points="13.681,10 2.681,10 2.681,30 28.681,30 28.681,12 15.681,12" 
        style={{ fill: "#ffc947" }} 
      />
      <polygon 
        points="13.681,10 2.681,10 2.681,30 28.681,30 28.681,12 15.681,12" 
        style={{ fill: "#ffb340", opacity: 0.8 }} 
      />
    </svg>
  );
};

export default FolderIcon;
