import React from "react";

const TicketIconSphere = ({
  size = 22,
  color = "currentColor",
  className = "",
  style = {},
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      style={style}
    >
      <rect width={size} height={size} fill="none" />
      <path
        d="M10.2499 13.6229H12.0799C13.0899 13.6229 13.9099 12.8029 13.9099 11.7929C13.9099 10.7829 13.0899 9.96289 12.0799 9.96289H9.32992C8.77992 9.96289 8.31992 10.1429 8.04992 10.5129L2.91992 15.4629"
        stroke={color}
        strokeWidth="1.83"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.58008 19.122L8.05008 17.842C8.33008 17.472 8.78008 17.292 9.33008 17.292H13.0001C14.0101 17.292 14.9301 16.922 15.5701 16.192L19.7901 12.162C20.5301 11.462 20.5601 10.302 19.8601 9.57204C19.1601 8.84204 18.0001 8.80204 17.2701 9.50204L13.4201 13.072"
        stroke={color}
        strokeWidth="1.83"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2 14.5332L7.5 20.0332"
        stroke={color}
        strokeWidth="1.83"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <mask
        id="mask0_529_101"
        style={{ maskType: "luminance" }}
        maskUnits="userSpaceOnUse"
        x="9"
        y="1"
        width="9"
        height="8"
      >
        <path
          d="M13.3646 6.91471C12.3421 6.95259 11.4789 6.15518 11.4415 5.13695C11.4079 4.11398 12.2062 3.25935 13.233 3.22102C14.2555 3.18314 15.1187 3.98055 15.1523 5.00352C15.1895 5.97846 14.4537 6.80922 13.494 6.90981C13.4512 6.9143 13.4083 6.91879 13.3655 6.92328M12.3784 1.72105L12.3835 1.89376C12.3916 2.05317 12.2677 2.23507 12.1175 2.28979L11.6643 2.51055C11.5258 2.59437 11.3114 2.57353 11.1921 2.46909L11.0445 2.34163C10.9253 2.23719 10.7359 2.24838 10.6314 2.3676L10.1049 2.96419C9.9962 3.08387 10.0069 3.26898 10.1309 3.37726L10.2833 3.50855C10.4029 3.61728 10.4507 3.82451 10.3884 3.97397L10.2317 4.46249C10.196 4.61782 10.0324 4.7519 9.86875 4.7604L9.67034 4.7682C9.51093 4.77625 9.38213 4.91102 9.39062 5.07472L9.42612 5.86793C9.43417 6.02734 9.57323 6.1557 9.73264 6.14765L9.99577 6.13739C10.1595 6.1289 10.3249 6.21983 10.3718 6.3362C10.4191 6.45684 10.5183 6.86657 10.4556 7.01174L10.0899 7.36624C9.97173 7.47825 10.0107 7.56079 10.1735 7.54372L10.985 7.55828C11.1453 7.55881 11.395 7.62791 11.533 7.7044C11.533 7.7044 12.5735 8.29266 13.3629 8.26189C13.9496 8.23938 14.6877 7.88481 14.6877 7.88481C14.8318 7.8134 15.0476 7.84709 15.1634 7.96055L15.3326 8.12905C15.4485 8.24251 15.6345 8.24034 15.748 8.1245L16.3076 7.55476C16.4211 7.43892 16.4189 7.25291 16.3035 7.14373L16.1248 6.96756C16.0089 6.8541 15.9741 6.64552 16.0454 6.49945L16.2009 6.12366C16.2495 5.96698 16.4224 5.84057 16.5823 5.83681L16.824 5.8288C16.9839 5.82503 17.1122 5.68597 17.1085 5.52612L17.0863 4.73584C17.082 4.57169 16.9434 4.44762 16.7836 4.45139L16.5809 4.45964C16.421 4.4634 16.2439 4.34338 16.1972 4.18801L16.0017 3.72774C15.9218 3.58453 15.9521 3.37779 16.0655 3.26195L16.2259 3.09788C16.3394 2.98203 16.3372 2.79602 16.2214 2.68256L15.6478 2.12765C15.5319 2.01419 15.3459 2.01636 15.2325 2.13221L15.0644 2.30574C14.951 2.42158 14.7424 2.45644 14.5963 2.38513L14.0785 2.19686C13.9223 2.1526 13.7882 1.98908 13.784 1.82493L13.7811 1.67364C13.7731 1.51423 13.6383 1.38543 13.4746 1.39392L12.6719 1.42176C12.6719 1.42176 12.659 1.4231 12.6505 1.424C12.5005 1.43972 12.3846 1.57314 12.3917 1.72399"
          fill="white"
        />
      </mask>
      <g mask="url(#mask0_529_101)">
        <path
          d="M12.6592 -2.47903L5.80859 5.49219L13.7602 12.3259L20.6108 4.35468L12.6592 -2.47903Z"
          stroke={color}
          strokeWidth="0.788288"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <path
        d="M13.3646 6.91471C12.3421 6.95259 11.4789 6.15518 11.4415 5.13695C11.4079 4.11398 12.2062 3.25935 13.233 3.22102C14.2555 3.18314 15.1187 3.98055 15.1523 5.00352C15.1895 5.97846 14.4537 6.80922 13.494 6.90981C13.4512 6.9143 13.4083 6.91879 13.3655 6.92328M12.3784 1.72105L12.3835 1.89376C12.3916 2.05317 12.2677 2.23507 12.1175 2.28979L11.6643 2.51055C11.5258 2.59437 11.3114 2.57353 11.1921 2.46909L11.0445 2.34163C10.9253 2.23719 10.7359 2.24838 10.6314 2.3676L10.1049 2.96419C9.9962 3.08387 10.0069 3.26898 10.1309 3.37726L10.2833 3.50855C10.4029 3.61728 10.4507 3.82451 10.3884 3.97397L10.2317 4.46249C10.196 4.61782 10.0324 4.7519 9.86875 4.7604L9.67034 4.7682C9.51093 4.77625 9.38213 4.91102 9.39062 5.07472L9.42612 5.86793C9.43417 6.02734 9.57323 6.1557 9.73264 6.14765L9.99577 6.13739C10.1595 6.1289 10.3249 6.21983 10.3718 6.3362C10.4191 6.45684 10.5183 6.86657 10.4556 7.01174L10.0899 7.36624C9.97173 7.47825 10.0107 7.56079 10.1735 7.54372L10.985 7.55828C11.1453 7.55881 11.395 7.62791 11.533 7.7044C11.533 7.7044 12.5735 8.29266 13.3629 8.26189C13.9496 8.23938 14.6877 7.88481 14.6877 7.88481C14.8318 7.8134 15.0476 7.84709 15.1634 7.96055L15.3326 8.12905C15.4485 8.24251 15.6345 8.24034 15.748 8.1245L16.3076 7.55476C16.4211 7.43892 16.4189 7.25291 16.3035 7.14373L16.1248 6.96756C16.0089 6.8541 15.9741 6.64552 16.0454 6.49945L16.2009 6.12366C16.2495 5.96698 16.4224 5.84057 16.5823 5.83681L16.824 5.8288C16.9839 5.82503 17.1122 5.68597 17.1085 5.52612L17.0863 4.73584C17.082 4.57169 16.9434 4.44762 16.7836 4.45139L16.5809 4.45964C16.421 4.4634 16.2439 4.34338 16.1972 4.18801L16.0017 3.72774C15.9218 3.58453 15.9521 3.37779 16.0655 3.26195L16.2259 3.09788C16.3394 2.98203 16.3372 2.79602 16.2214 2.68256L15.6478 2.12765C15.5319 2.01419 15.3459 2.01636 15.2325 2.13221L15.0644 2.30574C14.951 2.42158 14.7424 2.45644 14.5963 2.38513L14.0785 2.19686C13.9223 2.1526 13.7882 1.98908 13.784 1.82493L13.7811 1.67364C13.7731 1.51423 13.6383 1.38543 13.4746 1.39392L12.6719 1.42176C12.6719 1.42176 12.659 1.4231 12.6505 1.424C12.5005 1.43972 12.3846 1.57314 12.3917 1.72399"
        stroke={color}
        strokeWidth="0.9"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default TicketIconSphere;
