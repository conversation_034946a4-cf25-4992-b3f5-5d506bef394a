import {
  SET_CREATE_FORM,
  SET_OPEN_ACTIVITY,
  SET_OPEN_CONFIGMAIL,
  SET_OPEN_TAG,
  SET_SEARCH,
  <PERSON><PERSON><PERSON>_SEARCH,
  SET_OPEN_IMPORT_DRAWER,
  SET_OPEN_IMPORT_CHILDREN_DRAWER,
  SET_IMPORT_KEY,
  RESET_STATE,
  SET_PATH_NAME,
  ADD_TASK360,
  UPDATE_ELEMENT_SUCCESSFULLY,
  ADD_NEW_ELEMENT_FAMILY,
  RESET_ADD_NEW_ELEMENT_FAMILY,
  CANCEL_TASK_360,
  OPEN_MODAL_TICKET_GLPI,
  DETAILS_OPEN_INTEGRATION,
  SET_TYPE_USER_LIVECHAT,
} from "../constants";

const initialState = {
  openCreateForm: false,
  familyId: null,
  openImportDrawer: false,
  openChildrenImportDrawer: false,
  typeChildren: "",
  titleChildren: "",
  openTag: false,
  openActivity: false,
  openConfigMail: false,
  search: "",
  importKey: 0,
  pathname: "",
  task360: {},
  dropAction: null,
  cancelTask360: "",
  openModalTicketGlpi: false,
  detailsOpenIntegration: {},
  typeUserLiveChat: "",
};

const form = (state = initialState, action) => {
  const { type, payload } = action;
  switch (type) {
    case ADD_NEW_ELEMENT_FAMILY:
      return {
        ...state,
        openCreateForm: true,
        familyId: payload,
      };
    case RESET_ADD_NEW_ELEMENT_FAMILY:
      return {
        ...state,
        openCreateForm: false,
        familyId: null,
      };
    case SET_CREATE_FORM:
      return {
        ...state,
        openCreateForm: payload,
      };
    case SET_OPEN_TAG:
      return {
        ...state,
        openTag: payload,
      };
    case SET_OPEN_ACTIVITY:
      return {
        ...state,
        openActivity: payload,
      };
    case SET_OPEN_CONFIGMAIL:
      return {
        ...state,
        openConfigMail: payload,
      };
    case SET_SEARCH:
      return {
        ...state,
        search: payload.trimStart().replace(/\s{1,} /g, " "),
      };

    case CLEAR_SEARCH:
      return {
        ...state,
        search: "",
      };
    case SET_OPEN_IMPORT_DRAWER:
      return {
        ...state,
        openImportDrawer: payload,
      };
    case SET_OPEN_IMPORT_CHILDREN_DRAWER:
      return {
        ...state,
        openChildrenImportDrawer: payload.open,
        typeChildren: payload.type,
        titleChildren: payload.title,
      };
    case SET_IMPORT_KEY:
      return {
        ...state,
        importKey: payload.importKey,
      };
    case ADD_TASK360:
      return {
        ...state,
        task360: payload,
      };
    case CANCEL_TASK_360:
      return {
        ...state,
        cancelTask360: payload,
      };
    case RESET_STATE: {
      return initialState;
    }
    case SET_PATH_NAME: {
      return {
        ...state,
        pathname: payload,
      };
    }
    case UPDATE_ELEMENT_SUCCESSFULLY: {
      return {
        ...state,
        dropAction: payload,
      };
    }
    case OPEN_MODAL_TICKET_GLPI: {
      return {
        ...state,
        openModalTicketGlpi: payload,
      };
    }
    case DETAILS_OPEN_INTEGRATION: {
      return {
        ...state,
        detailsOpenIntegration: payload,
      };
    }
    case SET_TYPE_USER_LIVECHAT: {
      return {
        ...state,
        typeUserLiveChat: payload,
      };
    }
    default:
      return state;
  }
};

export default form;
