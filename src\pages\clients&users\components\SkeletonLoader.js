// import { LoadingOutlined } from "@ant-design/icons";
import { Skeleton, Divider } from "antd";
//
const TableLoader = ({ colNum, rowNum, Table }) => {
  // const columns = Array.from({ length: colNum }, (_, index) => (
  //   <Skeleton.Button key={index} active block={true} />
  // ));
  // const columns =

  // const rows = Array.from({ length: rowNum }, (_, index) => (
  //   <div key={index} className="flex flex-row space-x-2">
  //     {columns}
  //   </div>
  // ));
  const columns = [
    {
      title: <Skeleton.Button size="small" active block={true} />,
      dataIndex: "name",
      key: "name",
    },
    {
      title: <Skeleton.Button size="small" active block={true} />,
      children: [
        {
          title: <Skeleton.Button size="small" active block={true} />,
          dataIndex: "age",
          key: "age",
        },
        {
          title: <Skeleton.Button size="small" active block={true} />,
          dataIndex: "street",
          key: "street",
        },
        {
          title: <Skeleton.Button size="small" active block={true} />,
          dataIndex: "building",
          key: "building",
        },
        {
          title: <Skeleton.Button size="small" active block={true} />,
          dataIndex: "number",
          key: "number",
        },
        {
          title: <Skeleton.Button size="small" active block={true} />,
          dataIndex: "companyAddress",
          key: "companyAddress",
        },
        {
          title: <Skeleton.Button size="small" active block={true} />,
          dataIndex: "companyName",
          key: "companyName",
        },
        {
          title: <Skeleton.Button size="small" active block={true} />,
          dataIndex: "gender",
          key: "gender",
        },
      ],
    },
  ];

  // const dataSource = [];
  // for (let i = 0; i < rowNum; i++) {
  //   // dataSource.push({
  //   //   key: i,
  //   //   name: <Skeleton.Button key={i} size="small" active block={true} />,
  //   //   age: <Skeleton.Button key={i} size="small" active block={true} />,
  //   //   street: <Skeleton.Button key={i} size="small" active block={true} />,
  //   //   building: <Skeleton.Button key={i} size="small" active block={true} />,
  //   //   number: <Skeleton.Button key={i} size="small" active block={true} />,
  //   //   companyAddress: (
  //   //     <Skeleton.Button key={i} size="small" active block={true} />
  //   //   ),
  //   //   companyName: <Skeleton.Button key={i} size="small" active block={true} />,
  //   //   gender: <Skeleton.Button key={i} size="small" active block={true} />,
  //   // });
  //   dataSource?.push({});
  // }

  return (
    <Table
      columns={columns}
      // dataSource={dataSource}
      locale={{
        emptyText: (
          <div className=" flex h-64 flex-row items-center justify-center space-x-2 text-xl">
            Loading data, please wait
            <span className="animate-bounce"> ...</span>
          </div>
        ),
      }}
      size={"small"}
      rowSelection={[]}
      loading={true}
      pagination={{ pageSize: 20 }}
    />
  );
};

// return <div className="flex flex-col space-y-2">{rows}</div>;

//
const FormLoader = ({ rowNum }) => {
  const rows = Array.from({ length: rowNum }, (_, index) => (
    <Skeleton.Input key={index} active block={index % 2 === 0 ? false : true} />
  ));
  return <div className="flex flex-col space-y-6">{rows}</div>;
};
//
const FormWithTabsLoader = ({ tabsNum, inputNum }) => {
  const tabs = Array.from({ length: tabsNum }, (_, index) => (
    <Skeleton.Button key={index} active block={true} />
  ));
  //
  const rows = Array.from({ length: inputNum }, (_, index) => (
    <div key={index} className="space-y-3">
      <Skeleton.Input active size="small" style={{ width: `${10}rem` }} />
      <Skeleton.Input active block />
    </div>
  ));
  return (
    <div className="flex flex-row space-x-4">
      <div className="w-1/5 space-y-3">{tabs}</div>
      <Divider type="vertical" style={{ height: `${inputNum * 5.5}rem` }} />
      <div className="w-3/4 space-y-6">{rows}</div>
    </div>
  );
};

export { TableLoader, FormLoader, FormWithTabsLoader };
