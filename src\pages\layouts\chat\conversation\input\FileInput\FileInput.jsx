import { UploadOutlined } from "@ant-design/icons";
import { message, <PERSON><PERSON>, Tooltip } from "antd";
import { useCallback, useState, useRef, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { toastNotification } from "components/ToastNotification";
import mainService from "services/main.service";
import {
  accepetedExtentionImage,
  uuid,
} from "pages/layouts/chat/utils/ConversationUtils";
const getBase64 = (img, callback) => {
  try {
    const reader = new FileReader();
    reader.addEventListener("load", () => callback(reader.result));
    reader.readAsDataURL(img);
  } catch (error) {
    console.log(error);
  }
};
const formatFileSizeBinary = (fileSize) => {
  let size = Math.abs(fileSize);

  if (Number.isNaN(size)) {
    return "Invalid file size";
  }

  if (size === 0) {
    return "0 bytes";
  }

  const units = ["bytes", "KB", "MB", "GB", "TB"];
  let quotient = Math.floor(Math.log2(size) / 10);
  quotient = quotient < units.length ? quotient : units.length - 1;
  size /= 1024 ** quotient;

  return `${+size.toFixed(2)} ${units[quotient]}`;
};

const FileInput = ({
  source = "CHAT",
  cancel,
  setCancel,
  setFileList,
  fileList,
  defaultFileList,
}) => {
  const [messageApi, contextHolder] = message.useMessage();

  const { t } = useTranslation("common");
  const [abortController, setAbortController] = useState([]);

  const fileInputRef = useRef(null);

  const beforeUpload = useCallback(
    (fileArray) => {
      try {
        const isBt50M = fileArray.find((item) => item.size / 1024 / 1024 > 50);
        let acceptedIndexFile = [];

        if (isBt50M) {
          messageApi.open({
            duration: 3,
            type: "error",
            content: t("chat.file.error_taile"),
            style: {
              marginTop: "85vh",
            },
          });
        }
        for (let i = 0; i < fileArray.length; i++) {
          const image = fileArray[i].type?.split("/").pop().toUpperCase();
          const acceptedType = !fileArray[i]?.type?.startsWith("image")
            ? true
            : fileArray[i]?.type?.startsWith("image") &&
              accepetedExtentionImage.includes(image);
          if (!acceptedType) {
            messageApi.open({
              duration: 3,
              type: "error",
              content: t("chat.file.error_extention", {
                extention: accepetedExtentionImage.join(", "),
              }),
              style: {
                marginTop: "85vh",
              },
            });
          }

          if (fileArray[i].size / 1024 / 1024 < 50 && acceptedType)
            acceptedIndexFile.push(i);
        }
        return acceptedIndexFile;
      } catch (error) {
        console.log(error);
      }
    },
    [t, messageApi]
  );

  const handleRequest = useCallback(
    async (file, newAbortArray) => {
      try {
        if (cancel && file.uid === cancel)
          newAbortArray.find(({ uid }) => uid === file.uid)?.abort.abort();
        var formData = new FormData();
        formData.append("file", file.file);

        var config = {
          headers: {
            "Content-type": "multipart/form-data;",
          },
          onUploadProgress: (data) => {
            setFileList((prev) =>
              prev.map((f) => {
                if (file.uid === f.uid) {
                  f.percent = Math.round((data.loaded / data.total) * 100);
                }
                return f;
              })
            );
          },
          signal: newAbortArray.find(({ uid }) => uid === file.uid)?.abort
            .signal,
        };

        const requestAPI = await mainService.uploadFile(
          source,
          formData,
          config
        );

        if (requestAPI.status === 200) {
          newAbortArray.filter(({ uid }) => uid !== file.uid);
          setAbortController(newAbortArray);
          setFileList((prev) =>
            prev.map((f) => {
              if (file.uid === f.uid) {
                delete file.file;
                f.percent = 0;
                f._id = requestAPI.data.message._id;
                f.path = requestAPI.data.message.path;
                f.thumbnail_url = requestAPI.data.message.thumbnail_url;
                f.status = "done";
              }
              return f;
            })
          );
        }
      } catch (error) {
        newAbortArray.filter(({ uid }) => uid !== file.uid);
        setAbortController(newAbortArray);
        setFileList((prev) =>
          prev.map((f) => {
            if (file.uid === f.uid) {
              f.percent = 0;
              f.status = "error";
            }
            return f;
          })
        );
        if (error.name === "CanceledError") return;
        toastNotification("error", t("toasts.errorFetchApi"), "topRight");
      }
    },
    [cancel, setFileList, t]
  );
  const handleChange = useCallback(
    async (e) => {
      let promises = [];

      const filesTarget = [...fileList, ...Object.values(e)];
      let files = [...fileList];

      let newAbortArray = [...abortController];
      const acceptedIndexFile = beforeUpload(filesTarget);
      if (acceptedIndexFile && acceptedIndexFile.length > 5)
        messageApi.open({
          duration: 3,
          type: "info",
          content: t("chat.file.max_file"),
          style: {
            marginTop: "85vh",
          },
        });

      for (let i = 0; i < filesTarget.length; i++) {
        if (
          acceptedIndexFile &&
          acceptedIndexFile.includes(i) &&
          files.length < 5
        ) {
          let fileToSend = {
            uid: uuid(),
            type: filesTarget[i].type ?? "file",
            size: formatFileSizeBinary(filesTarget[i].size),
            file_name: filesTarget[i].name,
            file: Object.keys(filesTarget[i]).includes("file")
              ? filesTarget[i].file
              : filesTarget[i],
            status: "uploading",
            percent: 0,
          };

          newAbortArray.push({
            uid: fileToSend.uid,
            abort: new AbortController(),
          });
          getBase64(fileToSend.file, (url) => {
            if (url) fileToSend.url = url;
          });
          files.push(fileToSend);
          promises.push(handleRequest(fileToSend, newAbortArray));
        }

        setAbortController(newAbortArray);
        setFileList(files);
      }
      try {
        await Promise.all(promises);
      } catch (error) {
        if (error.name === "CanceledError") return;
        toastNotification("error", error.message, "topRight");
      }
      fileInputRef.current.value = null;
    },
    [
      abortController,
      beforeUpload,
      fileList,
      handleRequest,
      messageApi,
      setFileList,
      t,
    ]
  );

  useEffect(() => {
    if (cancel) {
      abortController.find((item) => item.uid === cancel)?.abort?.abort();
      setCancel(null);
    }

    return () => {
      if (cancel) {
        abortController.find((item) => item.uid === cancel)?.abort?.abort();
        setCancel(null);
      }
    };
  }, [cancel, setCancel, abortController]);
  useEffect(() => {
    let mounted = false;
    if (defaultFileList && !mounted) {
      handleChange(defaultFileList);
      mounted = true;
    }
    return () => {
      mounted = true;
    };
  }, [defaultFileList]);
  return (
    <label>
      {contextHolder}
      <Tooltip
        title={
          <p className="flex flex-col items-center justify-center space-x-1">
            <span>{t("chat.action.upload")}</span>
            <span>
              (Max. 5 {t("chat.message_type.file")}s , 50Mo/
              {t("chat.message_type.file")})
            </span>
          </p>
        }
        placement="topLeft">
        <Button
          id="uploadFileBtn"
          type="text"
          size="small"
          shape="circle"
          htmlType="submit"
          disabled={fileList.length >= 5}
          onClick={() => {
            fileInputRef.current.click();
          }}
          icon={<UploadOutlined />}></Button>
      </Tooltip>

      <input
        tabIndex={-1}
        ref={fileInputRef}
        type="file"
        className="hidden"
        multiple={true}
        disabled={fileList.length >= 5}
        onChange={(e) => {
          handleChange(e.target.files);
        }}
      />
    </label>
  );
};

export default FileInput;
