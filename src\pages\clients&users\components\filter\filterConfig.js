import { memo } from "react";
import {
  DatePicker,
  Input,
  InputNumber,
  Rate,
  Select,
  Space,
  TimePicker,
  Tooltip,
} from "antd";
import { colors } from "components/Colors";
import {
  currencyFormatter,
  currencyParser,
} from "../special_fields/CurrencyField";
import dayjs from "dayjs";

export const CustomizeFieldForFilterValue = memo(
  ({
    fieldType,
    onChange,
    value,
    dateTimeValue,
    options,
    configDateTime,
    isMultiple,
    isRange,
    width,
    isDisabled,
  }) => {
    //
    const dateFormat = configDateTime?.dateFormat || "YYYY-MM-DD";
    const timeFormat = configDateTime?.timeFormat || "HH:mm:ss";
    //
    const handleRangeValue = (dateStrings) => {
      if (dateStrings && dateStrings[0] && dateStrings[1]) {
        const start = dateStrings[0];
        let end = dateStrings[1];
        if (end.split(" ")?.[1] === "00:00") {
          end = `${end.split(" ")[0]} 23:59`;
        } else if (
          end.split(" ")?.[1] === "12:00" &&
          end.split(" ")?.[2] === "am"
        ) {
          end = `${end.split(" ")[0]} 11:59 pm`;
        }
        return [start, end];
      } else return null;
    };
    //
    switch (fieldType) {
      case "text":
      case "ip address":
      case "email":
      case "textarea":
      case "link":
      case "phone":
      case "image":
      case "album":
      case "file":
        return (
          <Input
            allowClear
            style={{ width: width }}
            onChange={(e) => onChange(e.target.value)}
            value={isDisabled ? null : value}
            disabled={isDisabled}
          />
        );

      case "country":
        const countryOptions =
          options?.map((country) => ({
            value: country.id,
            label_en: country.name_en,
            label_fr: country.name_fr,
            label: (
              <Space key={country?.id}>
                <span>{country?.flag}</span>
                <span>
                  {localStorage.getItem("language") === "fr"
                    ? country.name_fr
                    : country.name_en}
                </span>
              </Space>
            ),
          })) ?? [];
        return (
          <Select
            allowClear
            showSearch
            mode={isMultiple && "multiple"}
            maxTagCount="responsive"
            popupMatchSelectWidth={false}
            dropdownStyle={{
              maxWidth: "18rem",
            }}
            optionFilterProp="label"
            filterOption={(input, option) =>
              option?.label_en.toLowerCase().includes(input.toLowerCase()) ||
              option?.label_fr.toLowerCase().includes(input.toLowerCase())
            }
            style={{ width: width }}
            options={countryOptions}
            onChange={onChange}
            value={isDisabled ? null : value}
            disabled={isDisabled}
            // maxTagPlaceholder={(omittedValues) => (
            //   <Tooltip
            //     overlayStyle={{
            //       pointerEvents: "none",
            //     }}
            //     title={omittedValues.map(({ label }) => label).join(" | ")}
            //   >
            //     <span>+ {omittedValues.length}...</span>
            //   </Tooltip>
            // )}
          />
        );

      case "color":
      case "radio":
      case "select":
        // case "autocomplete":
        return (
          <Select
            allowClear
            showSearch
            mode={isMultiple && "multiple"}
            maxTagCount="responsive"
            popupMatchSelectWidth={false}
            dropdownStyle={{
              maxWidth: "18rem",
            }}
            optionFilterProp="label"
            style={{ width: width }}
            options={fieldType === "color" ? colors : options}
            onChange={(value) => onChange(value)}
            value={isDisabled ? null : value}
            disabled={isDisabled}
            maxTagPlaceholder={(omittedValues) => (
              <Tooltip
                overlayStyle={{
                  pointerEvents: "none",
                }}
                title={omittedValues.map(({ label }) => label).join(" | ")}
              >
                <span>+ {omittedValues.length}...</span>
              </Tooltip>
            )}
          />
        );

      case "checkbox":
      case "multiselect":
        return (
          <Select
            allowClear
            showSearch
            mode="multiple"
            maxTagCount="responsive"
            popupMatchSelectWidth={false}
            dropdownStyle={{
              maxWidth: "18rem",
            }}
            optionFilterProp="label"
            style={{ width: width }}
            options={options}
            onChange={onChange}
            value={isDisabled ? null : value}
            disabled={isDisabled}
            maxTagPlaceholder={(omittedValues) => (
              <Tooltip
                overlayStyle={{
                  pointerEvents: "none",
                }}
                title={omittedValues.map(({ label }) => label).join(" & ")}
              >
                <span>+ {omittedValues.length}...</span>
              </Tooltip>
            )}
          />
        );

      case "rate":
        return (
          <div
            className="flex items-center justify-center"
            style={{ width: width }}
          >
            <Rate
              allowHalf
              onChange={onChange}
              value={isDisabled ? null : value}
              disabled={isDisabled}
            />
          </div>
        );

      case "monetary":
        return (
          <InputNumber
            allowClear
            style={{ width: width }}
            onChange={onChange}
            value={isDisabled ? null : value}
            disabled={isDisabled}
            controls={false}
            formatter={currencyFormatter}
            parser={currencyParser}
          />
        );
      //
      case "number":
      case "extension":
        return (
          <InputNumber
            allowClear
            style={{ width: width }}
            onChange={onChange}
            value={isDisabled ? null : value}
            disabled={isDisabled}
          />
        );

      case "date_time":
      case "date":
        return isRange ? (
          <DatePicker.RangePicker
            allowClear
            needConfirm={false}
            format={
              fieldType === "date_time"
                ? `${dateFormat} ${timeFormat}`
                : dateFormat
            }
            style={{ width: width }}
            onChange={(date, dateString) =>
              onChange(handleRangeValue(dateString))
            }
            value={
              isDisabled || !value || !Array.isArray(value)
                ? null
                : [
                    dayjs(
                      value[0],
                      fieldType === "date_time"
                        ? `${dateFormat} ${timeFormat}`
                        : dateFormat
                    ),
                    dayjs(
                      value[1],
                      fieldType === "date_time"
                        ? `${dateFormat} ${timeFormat}`
                        : dateFormat
                    ),
                  ]
            }
            disabled={isDisabled}
          />
        ) : (
          <DatePicker
            allowClear
            needConfirm={false}
            use12Hours={timeFormat.includes("a")}
            format={
              fieldType === "date_time"
                ? `${dateFormat} ${timeFormat}`
                : dateFormat
            }
            showTime={fieldType === "date_time"}
            style={{ width: width }}
            onChange={(date, dateString) => onChange(dateString)}
            value={
              isDisabled || !value
                ? null
                : dayjs(
                    value,
                    fieldType === "date_time"
                      ? `${dateFormat} ${timeFormat}`
                      : dateFormat
                  )
            }
            disabled={isDisabled}
          />
        );

      case "time":
        return isRange ? (
          <TimePicker.RangePicker
            changeOnScroll
            needConfirm={false}
            allowClear
            use12Hours={timeFormat.includes("a")}
            format={timeFormat}
            style={{ width: width }}
            onChange={(time, timeString) => onChange(timeString)}
            value={
              isDisabled || !value || !Array.isArray(value)
                ? null
                : [
                    dayjs(value[0], `${timeFormat}`),
                    dayjs(value[1], `${timeFormat}`),
                  ]
            }
            disabled={isDisabled}
          />
        ) : (
          <TimePicker
            changeOnScroll
            needConfirm={false}
            allowClear
            use12Hours={timeFormat.includes("a")}
            format={timeFormat}
            style={{ width: width }}
            onChange={(time, timeString) => onChange(timeString)}
            value={isDisabled || !value ? null : dayjs(value, `${timeFormat}`)}
            disabled={isDisabled}
          />
        );

      default:
        return (
          <Input
            allowClear
            style={{ width: width }}
            onChange={(e) => onChange(e.target.value)}
            value={isDisabled ? null : value}
            disabled={isDisabled}
          />
        );
    }
  }
);
//
//
export const configComparison = (fieldType, t) => {
  switch (fieldType) {
    case "text":
    case "ip address":
    case "email":
    case "textarea":
    case "link":
    case "phone":
      return [
        { value: "is_equal", label: t("filterFamily.isEqual") },
        { value: "is_not_equal", label: t("filterFamily.isNotEqual") },
        { value: "is_like", label: t("filterFamily.isLike") },
        { value: "is_not_like", label: t("filterFamily.isNotLike") },
        { value: "is_blank", label: t("filterFamily.isBlank") },
        { value: "is_not_blank", label: t("filterFamily.isNotBlank") },
      ];

    case "image":
    case "album":
    case "file":
      return [
        { value: "is_like", label: t("filterFamily.filenameContains") },
        {
          value: "is_not_like",
          label: t("filterFamily.filenameDoesNotContain"),
        },
        { value: "is_blank", label: t("filterFamily.isBlank") },
        { value: "is_not_blank", label: t("filterFamily.isNotBlank") },
      ];

    case "radio":
    case "select":
    case "color":
    case "autocomplete":
    case "country":
      return [
        { value: "is", label: t("filterFamily.is") },
        { value: "is_not", label: t("filterFamily.isNot") },
        { value: "contain_any_of", label: t("filterFamily.containsAnyOf") },
        {
          value: "does_not_contain_any_of",
          label: t("filterFamily.doesNotContainAnyOf"),
        },
        { value: "is_blank", label: t("filterFamily.isBlank") },
        { value: "is_not_blank", label: t("filterFamily.isNotBlank") },
      ];
    case "checkbox":
    case "multiselect":
      return [
        { value: "contain_all_of", label: t("filterFamily.containsAllOf") },
        { value: "contain_any_of", label: t("filterFamily.containsAnyOf") },
        {
          value: "does_not_contain_all_of",
          label: t("filterFamily.doesNotContainAllOf"),
        },
        {
          value: "does_not_contain_any_of",
          label: t("filterFamily.doesNotContainAnyOf"),
        },
        { value: "is_blank", label: t("filterFamily.isBlank") },
        { value: "is_not_blank", label: t("filterFamily.isNotBlank") },
      ];

    case "monetary":
    case "number":
    case "extension":
    case "rate":
      return [
        { value: "is_equal", label: t("filterFamily.isEqual") },
        { value: "is_not_equal", label: t("filterFamily.isNotEqual") },
        { value: "is_greater_than", label: t("filterFamily.isGreaterThan") },
        { value: "is_less_than", label: t("filterFamily.isLessThan") },
        {
          value: "is_greater_then_or_equal",
          label: t("filterFamily.isGreaterThanOrEqual"),
        },
        {
          value: "is_less_then_or_equal",
          label: t("filterFamily.isLessThanOrEqual"),
        },
        // { value: "is_not_like", label: t("filterFamily.isNotLike") },
        { value: "is_blank", label: t("filterFamily.isBlank") },
      ];

    case "date_time":
    case "date":
    case "time":
      return [
        { value: "is_equal", label: t("filterFamily.is") },
        { value: "is_not_ equal", label: t("filterFamily.isNot") },
        { value: "is_after", label: t("filterFamily.isAfter") },
        { value: "is_before", label: t("filterFamily.isBefore") },
        { value: "is_on_or_after", label: t("filterFamily.isOnOrAfter") },
        { value: "is_on_or_before", label: t("filterFamily.isOnOrBefore") },
        { value: "is_within", label: t("filterFamily.isWithin") },
      ];

    default:
      return [
        { value: "is_equal", label: t("filterFamily.isEqual") },
        { value: "is_not_equal", label: t("filterFamily.isNotEqual") },
        { value: "is_like", label: t("filterFamily.isLike") },
        { value: "is_not_like", label: t("filterFamily.isNotLike") },
        { value: "is_blank", label: t("filterFamily.isBlank") },
        { value: "is_not_blank", label: t("filterFamily.isNotBlank") },
      ];
  }
};

/*
is_equal
is_not_equal
is_like
is_not_like
is_blank
is_not_blank
is
is_not
contain_any_of
does_not_contain_any_of
contain_all_of
does_not_contain_all_of
is_greater_than
is_less_than
is_greater_then_or_equal 
is_less_then_or_equal 
is_after
is_before
is_on_or_after
is_on_or_before
*/
