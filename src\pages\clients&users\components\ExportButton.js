import { memo, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import { UploadOutlined } from "@ant-design/icons";
//
import { exportData } from "../services/services";
import { toastNotification } from "../../../components/ToastNotification";
import { useTranslation } from "react-i18next";
import { roles } from "../../../utils/role";
import { URL_ENV } from "index";
import { getFamilyNameById } from "../FamilyRouting";
import { Refs_IDs } from "components/tour/tourConfig";

const ExportButton = ({
  familyId,
  search,
  total,
  role,
  pipelineId,
  folderId,
  filters,
  sort,
}) => {
  const [t] = useTranslation("common");

  const downloadPrefix = URL_ENV.REACT_APP_DOWNLOAD_FILE_PREFIX;
  const [loading, setLoading] = useState(false);

  const handleClick = async () => {
    try {
      // if (familyId === 4) return;
      setLoading(true);
      const formData = new FormData();
      formData.append("family_id", familyId);
      formData.append("search", search?.length >= 3 ? search : "");
      !!pipelineId && formData.append("pipeline_id", pipelineId);
      !!folderId && formData.append("tickets_folder_id", folderId);
      sort.field &&
        sort.order &&
        formData.append(`sort[${sort.field}]`, sort.order);
      filters.length &&
        filters.forEach((f, i) => {
          const adjustedFilter = {
            ...f,
            value:
              f.value === undefined
                ? null
                : f.field === "stages" && f.value
                ? f.value.map((item) => item[1])
                : f.value,
          };
          formData.append(`filter[${i}]`, JSON.stringify(adjustedFilter));
        });
      const response = await exportData(formData);

      const url = window.URL.createObjectURL(new Blob([response?.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute(
        "download",
        search?.length >= 3
          ? `${downloadPrefix}_${getFamilyNameById(
              t,
              familyId,
              "plural"
            )}.${search}.xlsx`
          : `${downloadPrefix}_${getFamilyNameById(t, familyId, "plural")}.xlsx`
      );
      document.body.appendChild(link);
      link.click();
      setLoading(false);
    } catch (err) {
      toastNotification("error", t("toasts.somethingWrong"), "topRight");
      setLoading(false);
      throw new Error(err?.message ? `error: ${err.message}` : { err });
    }
  };

  return (
    <Tooltip
      title={`Export (${total}) ${getFamilyNameById(t, familyId, "plural")}`}
    >
      <Button
        ref={Refs_IDs.families_export_data}
        onClick={handleClick}
        loading={loading}
        // disabled={(!familyId && !search) || familyId === 4}
        disabled={familyId === 4 && !roles?.includes(role)}
        icon={<UploadOutlined />}
      />
    </Tooltip>
    //   {`Export (${total}) ${getFamilyNameById(t, familyId, "plural")}`}
    // </Button>
  );
};

export default memo(ExportButton);
