import { useEffect, useMemo, useState } from "react";
import {
  <PERSON><PERSON>r,
  Button,
  Select,
  Tag,
  Space,
  Radio,
  Typography,
  Tooltip,
  Collapse,
} from "antd";
import { CaretRightOutlined, DeleteOutlined } from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { setConfigIPBX } from "../../../../../pages/voip/services/services";
import { toastNotification } from "../../../../ToastNotification";
import { URL_ENV } from "index";
import DisplayAvatar from "pages/voip/components/DisplayAvatar";
import "./index.css";
import {
  SET_INTERNALCALLFORWARD_SUCCESS,
  SET_DEVICE_RINGING_OUTPUT,
} from "new-redux/constants";

const { Text } = Typography;

const RenderLabelItem = ({ id, name, image, extension }) => {
  //
  return (
    <div className="flex flex-row space-x-2" key={id}>
      <div className="__avatar__">
        <DisplayAvatar urlImg={image} name={name} size={30} />
      </div>
      <div className="w-[10rem]">
        <p className="truncate font-semibold leading-4">{name}</p>

        <p className={"leading-4 text-slate-500"}>{extension}</p>
      </div>
    </div>
  );
};

const Settings = () => {
  //
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  //
  const internalforward = useSelector((state) => state.voip.internalforward);
  const outputDeviceId = useSelector((state) => state.voip.outputDeviceId);
  const { forwarding, call_action, rings_number, destination_call } =
    internalforward;
  //
  const user = useSelector((state) => state.user.user);
  const userList = useSelector((state) => state.chat.userList);
  //
  // filter the colleagues by etat 3 (Active) and should have extension
  const colleagues = useMemo(() => {
    return userList.reduce((acc, colleague) => {
      if (colleague?.status === 1 && Boolean(colleague.post_number)) {
        acc.push({
          id: colleague?._id,
          name: colleague?.name,
          extension: `${colleague?.post_number}`,
          image: colleague?.image
            ? `${URL_ENV?.REACT_APP_BASE_URL}${URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL}${colleague.image}`
            : undefined,
        });
      }
      return acc;
    }, []);
  }, [userList]);

  console.log({ colleagues });

  //
  // const [expandedKeys, setExpandedKeys] = useState([]);
  const [forwardingAction, setForwardingAction] = useState(null);
  const [beepsNum, setBeepsNum] = useState(null);
  const [forwardingTo, setForwardingTo] = useState(null);

  const [loadingDeleteForward, setLoadingDeleteForward] = useState(false);
  const [loadingSubmitForwarding, setLoadingSubmitForward] = useState(false);
  const [loadingSubmitQualify, setLoadingSubmitQualify] = useState(false);
  // ring settings
  const [deviceId, setDeviceId] = useState(outputDeviceId);
  const [audioList, setAudioList] = useState([]);

  // console.log({ deviceId, audioList });
  //
  // Initialize the local states from redux states
  const initialState = () => {
    if (forwarding === "1") {
      setForwardingAction(call_action);
      setBeepsNum(rings_number || "3");
      call_action === "1" && setForwardingTo(destination_call);
    } else setBeepsNum("3");
  };

  useEffect(() => {
    initialState();

    const getAudioOutput = async () => {
      let audioOutput = [];
      try {
        audioOutput = await navigator.mediaDevices.enumerateDevices();

        audioOutput = audioOutput.filter(
          (device) =>
            device.label !== "" &&
            device.kind === "audiooutput" &&
            device.deviceId !== "default"
        );

        setDeviceId((prev) => {
          let deviceId = prev;
          if (!deviceId) deviceId = audioOutput[0]?.deviceId;
          return deviceId;
        });
      } catch (e) {
        console.log(e);
      } finally {
        setAudioList(audioOutput);
      }
    };
    getAudioOutput();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  //
  const handleDeleteForwarding = async () => {
    try {
      setLoadingDeleteForward(true);
      const formData = new FormData();
      formData.append("forwarding", "0");
      const response = await setConfigIPBX(formData);
      if (response.status === 200) {
        dispatch({
          type: SET_INTERNALCALLFORWARD_SUCCESS,
          payload: response?.data?.data,
        });
        toastNotification(
          "success",
          t("voip.successDeleteForwarding"),
          "topRight"
        );
        setForwardingAction(null);
        setBeepsNum("3");
        setForwardingTo(null);
      }
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? err.message : err);
    } finally {
      setLoadingDeleteForward(false);
    }
  };
  //
  const handleSubmitForwarding = async () => {
    try {
      setLoadingSubmitForward(true);
      const formData = new FormData();
      formData.append("forwarding", "1");
      formData.append("call_action", forwardingAction);
      formData.append("rings_number", beepsNum);
      forwardingAction === "1" &&
        formData.append("destination_call", forwardingTo);
      forwardingAction === "10" &&
        formData.append(
          "destination_call",
          `${user?.phone?.[0]?.replace("+", "00")}${user?.phone?.[1]}`
        );
      const response = await setConfigIPBX(formData);
      if (response.status === 200) {
        dispatch({
          type: SET_INTERNALCALLFORWARD_SUCCESS,
          payload: response?.data?.data,
        });

        toastNotification(
          "success",
          t("voip.successUpdateForwarding"),
          "topRight"
        );
        // let timer = setTimeout(() => {
        //   initialState();
        //   return () => clearTimeout(timer);
        // }, 1500);
      }
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? err.message : err);
    } finally {
      setLoadingSubmitForward(false);
    }
  };
  //
  const handleChangeQualification = async (value) => {
    try {
      setLoadingSubmitQualify(true);
      const formData = new FormData();
      formData.append("qualification", value);
      const response = await setConfigIPBX(formData);
      if (response.status === 200) {
        dispatch({
          type: SET_INTERNALCALLFORWARD_SUCCESS,
          payload: response?.data?.data,
        });
        toastNotification(
          "success",
          t("voip.successUpdateQualifySetting"),
          "topRight"
        );
      }
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? err.message : err);
    } finally {
      setLoadingSubmitQualify(false);
    }
  };
  //
  const handleChangeDeviceOutputRinging = async () => {
    try {
      if (!deviceId) return;
      dispatch({
        type: SET_DEVICE_RINGING_OUTPUT,
        payload: deviceId,
      });
      toastNotification(
        "success",
        t("voip.successUpdateOutputDeviceSetting"),
        "topRight"
      );
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
    } finally {
    }
  };
  //
  const renderSaveButton = useMemo(() => {
    if (call_action !== forwardingAction) return true;
    if (rings_number !== beepsNum) return true;
    if (forwardingAction === "1" && destination_call !== forwardingTo)
      return true;
    else return false;
  }, [
    beepsNum,
    call_action,
    destination_call,
    forwardingAction,
    forwardingTo,
    rings_number,
  ]);
  //
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const panelStyle = {
    background: "white",
    padding: 0,
    borderRadius: 8,
    marginBottom: 16,
    border: "none",
    boxShadow:
      "0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",
  };
  //
  const collapseItems = useMemo(
    () => [
      {
        key: "forwarding",
        label: (
          <Space>
            <span style={{ fontSize: 13 }} className="font-semibold ">
              {t("voip.renvoi-appel")}
            </span>
            {internalforward.forwarding === "1" && (
              <div className=" h-5 w-5 rounded-full  bg-black text-white">
                <svg
                  className="h-3 w-3"
                  aria-hidden="true"
                  focusable="false"
                  data-prefix="fas"
                  data-icon="random"
                  role="img"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 512 512"
                  data-fa-i2svg=""
                  style={{ margin: "4px" }}
                >
                  <path
                    fill="currentColor"
                    d="M504.971 359.029c9.373 9.373 9.373 24.569 0 33.941l-80 79.984c-15.01 15.01-40.971 4.49-40.971-16.971V416h-58.785a12.004 12.004 0 0 1-8.773-3.812l-70.556-75.596 53.333-57.143L352 336h32v-39.981c0-21.438 25.943-31.998 40.971-16.971l80 79.981zM12 176h84l52.781 56.551 53.333-57.143-70.556-75.596A11.999 11.999 0 0 0 122.785 96H12c-6.627 0-12 5.373-12 12v56c0 6.627 5.373 12 12 12zm372 0v39.984c0 21.46 25.961 31.98 40.971 16.971l80-79.984c9.373-9.373 9.373-24.569 0-33.941l-80-79.981C409.943 24.021 384 34.582 384 56.019V96h-58.785a12.004 12.004 0 0 0-8.773 3.812L96 336H12c-6.627 0-12 5.373-12 12v56c0 6.627 5.373 12 12 12h110.785c3.326 0 6.503-1.381 8.773-3.812L352 176h32z"
                  ></path>
                </svg>
              </div>
            )}
          </Space>
        ),
        style: panelStyle,
        // showArrow: false,
        extra: internalforward.forwarding === "1" && (
          <Tooltip title={t("voip.delete")}>
            <Button
              loading={loadingDeleteForward}
              onClick={() => handleDeleteForwarding()}
              size="small"
              danger
              type="text"
              shape="circle"
              icon={<DeleteOutlined style={{ fontSize: 15 }} />}
            />
          </Tooltip>
        ),
        children: (
          <div className="space-y-2">
            <Radio.Group
              value={forwardingAction || internalforward?.call_action}
              onChange={({ target: { value } }) => setForwardingAction(value)}
              buttonStyle="solid"
            >
              <Space direction="vertical">
                <Radio value="1">{`${t("voip.internalExtension")} (${t(
                  "voip.colleague"
                )})`}</Radio>
                <Tooltip
                  placement="topLeft"
                  title={
                    !user?.phone?.length && t("voip.userDoNotHavePhoneNum")
                  }
                >
                  <Radio disabled={!user?.phone?.length} value="10">
                    {t("voip.yourPhoneNum")}
                  </Radio>
                </Tooltip>
                <Radio value="2">{t("voip.yourVoicemail")}</Radio>
              </Space>
            </Radio.Group>
            {(forwardingAction || internalforward.forwarding === "1") && (
              <>
                <Divider style={{ margin: "10px 0" }} />
                <div className="space-y-2 px-2">
                  <div className="flex flex-row items-center justify-between space-x-2">
                    <p className="font-semibold text-slate-500 ">
                      {t("voip.after")}
                    </p>
                    <Select
                      style={{ width: "40%" }}
                      value={beepsNum}
                      size="small"
                      onChange={setBeepsNum}
                      options={[
                        {
                          value: "1",
                          label: `1 ${t("voip.beep")}`,
                        },
                        {
                          value: "2",
                          label: `2 ${t("voip.beeps")}`,
                        },
                        {
                          value: "3",
                          label: `3 ${t("voip.beeps")}`,
                        },
                        {
                          value: "4",
                          label: `4 ${t("voip.beeps")}`,
                        },
                        {
                          value: "5",
                          label: `5 ${t("voip.beeps")}`,
                        },
                      ]}
                    />
                  </div>
                  <div className="flex flex-row items-center justify-between space-x-2">
                    <p className="font-semibold text-slate-500 ">
                      {t("voip.to")}
                    </p>
                    {forwardingAction === "1" ? (
                      <Select
                        onChange={setForwardingTo}
                        defaultValue={
                          forwardingTo === `${user.extension}`
                            ? null
                            : forwardingTo
                        }
                        style={{ width: "80%", height: "2.15rem" }}
                        showSearch
                        allowClear
                        filterOption={(input, option) =>
                          option?.name
                            .toLowerCase()
                            .includes(input.toLowerCase()) ||
                          option?.extension
                            .toLowerCase()
                            .includes(input.toLowerCase())
                        }
                      >
                        {colleagues?.map((colleague) => (
                          <Select.Option
                            key={colleague?.id}
                            value={colleague?.extension}
                            name={colleague?.name}
                            extension={colleague?.extension}
                          >
                            <RenderLabelItem {...colleague} />
                          </Select.Option>
                        ))}
                      </Select>
                    ) : forwardingAction === "10" && user?.phone?.length ? (
                      <p className="font-semibold leading-4">{`(${user.phone[0]}) ${user.phone[1]}`}</p>
                    ) : forwardingAction === "2" ? (
                      <p className="font-semibold leading-4">
                        {t("voip.yourVoicemail")}
                      </p>
                    ) : null}
                  </div>
                </div>
                {renderSaveButton && (
                  <div className="flex flex-row justify-end py-3 pr-2">
                    <Button
                      size="small"
                      type="primary"
                      loading={loadingSubmitForwarding}
                      disabled={
                        !forwardingAction ||
                        (forwardingAction === "1" && !forwardingTo)
                      }
                      onClick={() => handleSubmitForwarding()}
                    >
                      {t("voip.save")}
                    </Button>
                  </div>
                )}
              </>
            )}
          </div>
        ),
      },
      ...(audioList.length
        ? [
            {
              key: "audioList",
              label: (
                <span style={{ fontSize: 13 }} className="font-semibold ">
                  {t("voip.ringing-output-device")}
                </span>
              ),
              style: panelStyle,
              // showArrow: false,
              children: (
                <div className="space-y-2 ">
                  <div className=" mb-4 max-h-40 overflow-y-auto">
                    <Radio.Group
                      value={deviceId}
                      onChange={(e) => setDeviceId(e.target.value)}
                      buttonStyle="solid"
                    >
                      <Space direction="vertical">
                        {audioList.map((audio) => (
                          <Radio key={audio.deviceId} value={audio.deviceId}>
                            {audio.label?.split("(")[0].trim()}
                          </Radio>
                        ))}
                      </Space>
                    </Radio.Group>
                  </div>
                  <Button
                    htmlType="submit"
                    size="small"
                    className=" ml-auto flex justify-end"
                    type="primary"
                    onClick={handleChangeDeviceOutputRinging}
                    disabled={outputDeviceId && outputDeviceId === deviceId}
                  >
                    {t("voip.save")}
                  </Button>
                </div>
              ),
            },
          ]
        : []),
      {
        key: "qualification",
        label: (
          <span style={{ fontSize: 13 }} className="font-semibold ">
            {t("voip.call_qualification")}
          </span>
        ),
        style: panelStyle,
        // showArrow: false,
        extra: (
          <Tag
            style={{ marginTop: "-2px", marginInlineEnd: 1 }}
            color="#1d4ed8"
          >
            <div className="font-semibold">
              {internalforward.qualification === "2"
                ? t("voip.ask_before")
                : internalforward.qualification === "1"
                ? t("voip.always")
                : t("voip.never")}
            </div>
          </Tag>
        ),
        children: (
          <>
            <Text type="secondary">{t("voip.hintQualifSetting")}</Text>
            <div className="flex flex-row items-center justify-between space-x-1 px-1 ">
              <Text strong underline>
                {t("voip.followUpAction")}
              </Text>
              <Select
                disabled={loadingSubmitQualify}
                loading={loadingSubmitQualify}
                onChange={handleChangeQualification}
                value={internalforward.qualification}
                defaultValue={"2"}
                style={{ width: "9rem" }}
                options={[
                  {
                    label: t("voip.always"),
                    value: "1",
                  },
                  {
                    label: t("voip.ask_before"),
                    value: "2",
                  },
                  {
                    label: t("voip.never"),
                    value: "0",
                  },
                ]}
              />
            </div>
          </>
        ),
      },
    ],
    [
      audioList,
      beepsNum,
      colleagues,
      deviceId,
      forwardingAction,
      forwardingTo,
      internalforward?.call_action,
      internalforward.forwarding,
      internalforward.qualification,
      loadingDeleteForward,
      loadingSubmitForwarding,
      loadingSubmitQualify,
      outputDeviceId,
      panelStyle,
      renderSaveButton,
      t,
      user.extension,
      user.phone,
    ]
  );
  //
  return (
    <div className="relative h-[23rem] ">
      <Divider plain orientation="left" style={{ marginTop: 0 }}>
        <span style={{ fontSize: 14 }} className=" font-semibold ">
          {t("voip.renvoi-title-page")}
        </span>
      </Divider>

      <div className="webPhone-setting h-[21rem] space-y-6  overflow-y-auto px-1 pb-2">
        <Collapse
          style={{
            border: "none",
            backgroundColor: "rgb(248 250 252)",
            padding: "0px 4px",
          }}
          size="small"
          accordion
          expandIcon={({ isActive }) => (
            <CaretRightOutlined rotate={isActive ? 90 : 0} />
          )}
          items={collapseItems}
        />
        {/* <Card
          title={t("voip.renvoi-appel")}
          size="small"
          bordered={false}
          extra={
            internalforward.forwarding === "1" && (
              <Space size={3}>
                <Tag
                  style={{ marginTop: "-2px", marginInlineEnd: 1 }}
                  color="#1d4ed8"
                >
                  <div className="font-semibold">
                    {internalforward?.call_action === "10" ? (
                      <p className="flex max-w-[8rem] truncate">
                        {t("voip.yourPhoneNum")}
                      </p>
                    ) : internalforward?.call_action === "2" ? (
                      t("voip.yourVoicemail")
                    ) : (
                      <div className=" flex max-w-[8rem] flex-row space-x-0.5 ">
                        <p className="truncate">
                          {
                            colleagues?.find(
                              (item) =>
                                item?.extension ===
                                internalforward.destination_call
                            )?.name
                          }
                        </p>
                        <p>({internalforward.destination_call})</p>
                      </div>
                    )}
                  </div>
                </Tag>
                <Tooltip title={t("voip.delete")}>
                  <Button
                    loading={loadingDeleteForward}
                    onClick={() => handleDeleteForwarding()}
                    size="small"
                    danger
                    type="text"
                    shape="circle"
                    icon={<DeleteOutlined style={{ fontSize: 15 }} />}
                  />
                </Tooltip>
              </Space>
            )
          }
        >
          <Divider style={{ margin: "-7px 0px 10px" }} />
          <div className="space-y-2 pb-2">
            <Radio.Group
              value={forwardingAction || internalforward?.call_action}
              onChange={({ target: { value } }) => setForwardingAction(value)}
              buttonStyle="solid"
            >
              <Space direction="vertical">
                <Radio value="1">{`${t("voip.internalExtension")} (${t(
                  "voip.colleague"
                )})`}</Radio>
                <Tooltip
                  placement="topLeft"
                  title={
                    !user?.phone?.length && t("voip.userDoNotHavePhoneNum")
                  }
                >
                  <Radio disabled={!user?.phone?.length} value="10">
                    {t("voip.yourPhoneNum")}
                  </Radio>
                </Tooltip>
                <Radio value="2">{t("voip.yourVoicemail")}</Radio>
              </Space>
            </Radio.Group>
            {(forwardingAction || internalforward.forwarding === "1") && (
              <>
                <Divider style={{ margin: "10px 0" }} />
                <div className="space-y-2 px-2">
                  <div className="flex flex-row items-center justify-between space-x-2">
                    <p className="font-semibold text-slate-500 underline">
                      {t("voip.after")}
                    </p>
                    <Select
                      style={{ width: "40%" }}
                      value={beepsNum}
                      onChange={setBeepsNum}
                      options={[
                        {
                          value: "1",
                          label: `1 ${t("voip.beep")}`,
                        },
                        {
                          value: "2",
                          label: `2 ${t("voip.beeps")}`,
                        },
                        {
                          value: "3",
                          label: `3 ${t("voip.beeps")}`,
                        },
                        {
                          value: "4",
                          label: `4 ${t("voip.beeps")}`,
                        },
                        {
                          value: "5",
                          label: `5 ${t("voip.beeps")}`,
                        },
                      ]}
                    />
                  </div>
                  <div className="flex flex-row items-center justify-between space-x-2">
                    <p className="font-semibold text-slate-500 underline">
                      {t("voip.to")}
                    </p>
                    {forwardingAction === "1" ? (
                      <Select
                        onChange={setForwardingTo}
                        defaultValue={
                          forwardingTo === `${user.extension}`
                            ? null
                            : forwardingTo
                        }
                        style={{ width: "80%", height: "2.15rem" }}
                        showSearch
                        allowClear
                        filterOption={(input, option) =>
                          option?.name
                            .toLowerCase()
                            .includes(input.toLowerCase()) ||
                          option?.extension
                            .toLowerCase()
                            .includes(input.toLowerCase())
                        }
                      >
                        {colleagues?.map((colleague) => (
                          <Select.Option
                            key={colleague?.id}
                            value={colleague?.extension}
                            name={colleague?.name}
                            extension={colleague?.extension}
                          >
                            <RenderLabelItem {...colleague} />
                          </Select.Option>
                        ))}
                      </Select>
                    ) : forwardingAction === "10" && user?.phone?.length ? (
                      <p className="font-semibold leading-4">{`(${user.phone[0]}) ${user.phone[1]}`}</p>
                    ) : forwardingAction === "2" ? (
                      <p className="font-semibold leading-4">
                        {t("voip.yourVoicemail")}
                      </p>
                    ) : null}
                  </div>
                </div>
                {renderSaveButton && (
                  <div className="flex flex-row justify-end py-3 pr-2">
                   
                    <Button
                      size="small"
                      type="primary"
                      loading={loadingSubmitForwarding}
                      disabled={
                        !forwardingAction ||
                        (forwardingAction === "1" && !forwardingTo)
                      }
                      onClick={() => handleSubmitForwarding()}
                    >
                      {t("voip.save")}
                    </Button>
                  
                  </div>
                )}
              </>
            )}
          </div>
        </Card> */}

        {/* Get ringing output     */}
        {/* {audioList.length > 0 && (
          <Card
            actions={[]}
            title={t("voip.ringing-output-device")}
            size="small"
            className="max-h-48 overflow-y-auto"
            bordered={false}
          >
            <Divider style={{ margin: "-7px 0px 10px" }} />

            <div className="space-y-2 pb-2">
              <Radio.Group
                value={deviceId}
                onChange={(e) => setDeviceId(e.target.value)}
                buttonStyle="solid"
              >
                <Space direction="vertical">
                  {audioList.map((audio) => (
                    <Radio key={audio.deviceId} value={audio.deviceId}>
                      {audio.label?.split("(")[0].trim()}
                    </Radio>
                  ))}
                </Space>
              </Radio.Group>

              <Button
                htmlType="submit"
                size="small"
                className=" ml-auto flex justify-end"
                type="primary"
                onClick={handleChangeDeviceOutputRinging}
                disabled={outputDeviceId && outputDeviceId === deviceId}
              >
                {t("voip.save")}
              </Button>
            </div>
          </Card>
        )} */}
        {/*    */}
        {/* <Card
          title={t("voip.call_qualification")}
          size="small"
          bordered={false}
          extra={
            <Tag
              style={{ marginTop: "-2px", marginInlineEnd: 1 }}
              color="#1d4ed8"
            >
              <div className="font-semibold">
                {internalforward.qualification === "2"
                  ? t("voip.ask_before")
                  : internalforward.qualification === "1"
                  ? t("voip.always")
                  : t("voip.never")}
              </div>
            </Tag>
          }
        >
          <Divider style={{ margin: "-7px 0px 10px" }} />
          <Text type="secondary">{t("voip.hintQualifSetting")}</Text>
          <div className="flex flex-row items-center justify-between space-x-1 px-1 pb-4 pt-2">
            <Text strong underline>
              {t("voip.followUpAction")}
            </Text>
            <Select
              disabled={loadingSubmitQualify}
              loading={loadingSubmitQualify}
              onChange={handleChangeQualification}
              value={internalforward.qualification}
              defaultValue={"2"}
              style={{ width: "9rem" }}
              options={[
                {
                  label: t("voip.always"),
                  value: "1",
                },
                {
                  label: t("voip.ask_before"),
                  value: "2",
                },
                {
                  label: t("voip.never"),
                  value: "0",
                },
              ]}
            />
          </div>
        </Card> */}
      </div>
    </div>
  );
};

export default Settings;

// const Settings = () => {
//   const [t] = useTranslation("common");
//   const dispatch = useDispatch();
//   const internalforward = useSelector((state) => state.voip.internalforward);
//   const user = useSelector((state) => state.user.user);
//   const collegues = useSelector((state) => state.voip.collegues);

//   const [expandedKeys, setExpandedKeys] = useState([
//     "forwarding",
//     "qualification",
//   ]);
//   const [forwarding, setForwarding] = useState(null);
//   const [forwardingAction, setForwardingAction] = useState(null);
//   const [beepsNum, setBeepsNum] = useState(null);
//   const [forwardingTo, setForwardingTo] = useState(null);
//   const [qualification, setQualification] = useState(null);

//   const [displaySaveQualify, setDisplaySaveQualify] = useState(false);
//   const [displaySaveForward, setDisplaySaveForward] = useState(false);

//   const [loadingSaveQualify, setLoadingSaveQualify] = useState(false);
//   const [loadingSaveForward, setLoadingSaveForward] = useState(false);
//   const [loadingDeleteForward, setLoadingDeleteForward] = useState(false);
//   //
//   useEffect(() => {
//     setQualification(internalforward?.qualification);
//     setForwarding(internalforward?.forwarding);
//     setBeepsNum(internalforward?.rings_number);
//     setForwardingTo(internalforward?.destination_call);
//     setForwardingAction(internalforward?.call_action);
//   }, [expandedKeys, internalforward]);

//   //
//   useEffect(() => {
//     if (
//       qualification === internalforward?.qualification ||
//       !expandedKeys?.includes("qualification")
//     ) {
//       setDisplaySaveQualify(false);
//     } else setDisplaySaveQualify(true);
//     const { call_action, rings_number, destination_call } = internalforward;
//     if (
//       (forwardingAction === call_action &&
//         beepsNum === rings_number &&
//         forwardingTo === destination_call) ||
//       !expandedKeys?.includes("forwarding")
//     ) {
//       setDisplaySaveForward(false);
//     } else {
//       setDisplaySaveForward(true);
//     }
//   }, [
//     beepsNum,
//     forwarding,
//     forwardingAction,
//     forwardingTo,
//     internalforward,
//     qualification,
//     expandedKeys,
//   ]);
//   //
//   const handleActionSubmit = async (setting) => {
//     try {
//       const formData = new FormData();
//       if (setting === "deleteForwarding") {
//         setLoadingDeleteForward(true);
//         formData.append("forwarding", "0");
//       } else if (setting === "call_forwarding") {
//         setLoadingSaveForward(true);
//         formData.append("rings_number", !beepsNum ? 3 : beepsNum);
//         formData.append("call_action", forwardingAction);
//         formData.append(
//           "destination_call",
//           forwardingAction === "1"
//             ? forwardingTo
//             : forwardingAction === "10"
//             ? `${user?.phone?.[0]?.replace("+", "00")}${user?.phone?.[1]}`
//             : ""
//         );
//         formData.append("forwarding", "1");
//       } else {
//         setLoadingSaveQualify(true);
//         formData.append("qualification", qualification);
//       }
//       const response = await setConfigIPBX(formData);
//       if (response.status === 200) {
//         dispatch({
//           type: "SET_INTERNALCALLFORWARD_SUCCESS",
//           payload: response?.data?.data,
//         });
//         setExpandedKeys([]);
//       }
//     } catch (err) {
//       err?.response?.status !== 401 &&
//         toastNotification("error", t("toasts.somethingWrong"), "topRight");
//       dispatch({
//         type: "SET_INTERNALCALLFORWARD_ERROR",
//         payload: internalforward,
//       });
//       throw new Error(err?.message ? err.message : err);
//     } finally {
//       setLoadingSaveQualify(false);
//       setLoadingSaveForward(false);
//       setLoadingDeleteForward(false);
//     }
//   };
//   //
//   const colleagues = useMemo(
//     () =>
//       collegues?.map((item) => ({
//         id: item?.id,
//         name: item?.name?.replaceAll("_", " "),
//         extension: `${item?.extension}`,
//         image:
//           item?.image &&
//           `${
//             URL_ENV?.REACT_APP_BASE_URL +
//             URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
//           }${item?.image}`,
//       })),
//     [collegues]
//   );
//   //
//   const panelStyle = {
//     background: "white",
//     padding: "4px 0",
//     borderRadius: 8,
//     marginBottom: 16,
//   };
//   //
//   const collapseItems = useMemo(
//     () => [
//       {
//         key: "forwarding",
//         label: (
//           <p className="truncate font-semibold">{t("voip.renvoi-appel")}</p>
//         ),
//         children: (
//           <div className="space-y-2">
//             {/* <Text underline type="secondary">
//               Renvoyer l'appel entrant vers :
//             </Text> */}
//             <Radio.Group
//               value={forwardingAction}
//               onChange={({ target: { value } }) => setForwardingAction(value)}
//               buttonStyle="solid"
//             >
//               <Space direction="vertical">
//                 <Radio value="1">{`${t("voip.internalExtension")} (${t(
//                   "voip.colleague"
//                 )})`}</Radio>
//                 <Tooltip
//                   placement="topLeft"
//                   title={
//                     !user?.phone?.length && t("voip.userDoNotHavePhoneNum")
//                   }
//                 >
//                   <Radio disabled={!user?.phone?.length} value="10">
//                     {t("voip.yourPhoneNum")}
//                   </Radio>
//                 </Tooltip>
//                 <Radio value="2">{t("voip.yourVoicemail")}</Radio>
//               </Space>
//             </Radio.Group>
//             {forwardingAction && (
//               <>
//                 <Divider style={{ margin: "10px 0" }} />

//                 <div className="space-y-2 px-2">
//                   <div className="flex flex-row items-center justify-between space-x-2">
//                     <p className="font-semibold text-slate-500 underline">
//                       {t("voip.after")}
//                     </p>
//                     <Select
//                       style={{ width: "40%" }}
//                       defaultValue={"3"}
//                       value={beepsNum === "0" ? null : beepsNum}
//                       onChange={setBeepsNum}
//                       options={[
//                         {
//                           value: "1",
//                           label: `1 ${t("voip.beep")}`,
//                         },
//                         {
//                           value: "2",
//                           label: `2 ${t("voip.beeps")}`,
//                         },
//                         {
//                           value: "3",
//                           label: `3 ${t("voip.beeps")}`,
//                         },
//                         {
//                           value: "4",
//                           label: `4 ${t("voip.beeps")}`,
//                         },
//                         {
//                           value: "5",
//                           label: `5 ${t("voip.beeps")}`,
//                         },
//                       ]}
//                     />
//                   </div>
//                   <div className="flex flex-row items-center justify-between space-x-2">
//                     <p className="font-semibold text-slate-500 underline">
//                       {t("voip.to")}
//                     </p>
//                     {forwardingAction === "1" ? (
//                       <Select
//                         onChange={setForwardingTo}
//                         value={forwardingTo?.length > 5 ? null : forwardingTo}
//                         style={{ width: "80%" }}
//                         showSearch
//                         allowClear
//                         filterOption={(input, option) =>
//                           option?.name
//                             .toLowerCase()
//                             .includes(input.toLowerCase()) ||
//                           option?.extension
//                             .toLowerCase()
//                             .includes(input.toLowerCase())
//                         }
//                       >
//                         {colleagues?.map((colleague) => (
//                           <Select.Option
//                             key={colleague?.id}
//                             value={colleague?.extension}
//                             name={colleague?.name}
//                             extension={colleague?.extension}
//                           >
//                             <RenderLabelItem {...colleague} />
//                           </Select.Option>
//                         ))}
//                       </Select>
//                     ) : forwardingAction === "10" && user?.phone?.length ? (
//                       <p className="font-semibold leading-4">{`(${user.phone[0]}) ${user.phone[1]}`}</p>
//                     ) : forwardingAction === "2" ? (
//                       <p className="font-semibold leading-4">
//                         {t("voip.yourVoicemail")}
//                       </p>
//                     ) : null}
//                   </div>
//                 </div>
//               </>
//             )}
//           </div>
//         ),
//         extra: (
//           <div className="flex flex-row items-center space-x-0.5">
//             {internalforward.forwarding &&
//               internalforward.forwarding !== "0" && (
//                 <>
//                   <Tooltip
//                     title={
//                       internalforward?.call_action === "10" &&
//                       `(${user.phone[0]}) ${user.phone[1]}`
//                     }
//                   >
//                     <Tag
//                       style={{ marginTop: "-2px", marginInlineEnd: 1 }}
//                       color="#1d4ed8"
//                     >
//                       <div className="font-semibold">
//                         {internalforward?.call_action === "10" ? (
//                           t("voip.yourPhoneNum")
//                         ) : internalforward?.call_action === "2" ? (
//                           t("voip.yourVoicemail")
//                         ) : (
//                           <div className=" flex max-w-[7.5rem] flex-row space-x-0.5 ">
//                             <p className="truncate">
//                               {
//                                 colleagues?.find(
//                                   (item) =>
//                                     item?.extension ===
//                                     internalforward.destination_call
//                                 )?.name
//                               }
//                             </p>
//                             <p>({internalforward.destination_call})</p>
//                           </div>
//                         )}
//                       </div>
//                     </Tag>
//                   </Tooltip>
//                   {!displaySaveForward && (
//                     <Tooltip title={t("voip.delete")}>
//                       <Button
//                         loading={loadingDeleteForward}
//                         onClick={() => handleActionSubmit("deleteForwarding")}
//                         size="small"
//                         danger
//                         type="link"
//                         icon={<DeleteOutlined style={{ fontSize: 15 }} />}
//                       />
//                     </Tooltip>
//                   )}
//                 </>
//               )}
//             {displaySaveForward ? (
//               <>
//                 <Tooltip title={t("voip.cancel")}>
//                   <Button
//                     onClick={() => setExpandedKeys([])}
//                     size="small"
//                     type="link"
//                     danger
//                     icon={<CloseOutlined style={{ fontSize: 15 }} />}
//                   />
//                 </Tooltip>
//                 <Tooltip title={t("voip.save")}>
//                   <Button
//                     onClick={() => handleActionSubmit("call_forwarding")}
//                     loading={loadingSaveForward}
//                     size="small"
//                     type="link"
//                     icon={
//                       <SaveOutlined
//                         className="hover:bg-green-100"
//                         style={{ fontSize: 16, color: "green" }}
//                       />
//                     }
//                   />
//                 </Tooltip>
//               </>
//             ) : !internalforward.forwarding ||
//               internalforward.forwarding === "0" ? (
//               <Tooltip title={t("voip.add")}>
//                 <Button
//                   onClick={() =>
//                     setExpandedKeys((prev) =>
//                       prev.includes("forwarding")
//                         ? setExpandedKeys([])
//                         : setExpandedKeys(["forwarding"])
//                     )
//                   }
//                   size="small"
//                   type="link"
//                   icon={<PlusCircleOutlined style={{ fontSize: 16 }} />}
//                 />
//               </Tooltip>
//             ) : (
//               <Tooltip title={t("voip.edit")}>
//                 <Button
//                   onClick={() =>
//                     setExpandedKeys((prev) =>
//                       prev.includes("forwarding")
//                         ? setExpandedKeys([])
//                         : setExpandedKeys(["forwarding"])
//                     )
//                   }
//                   size="small"
//                   type="link"
//                   icon={<FiEdit style={{ fontSize: 15 }} />}
//                 />
//               </Tooltip>
//             )}
//           </div>
//         ),
//         showArrow: false,
//         style: panelStyle,
//       },
//       {
//         key: "qualification",
//         label: (
//           <p className="truncate font-semibold">
//             {t("voip.call_qualification")}
//           </p>
//         ),
//         children: (
//           <div className="space-y-2">
//             <Text type="secondary">{t("voip.hintQualifSetting")}</Text>
//             <div className="flex flex-row items-center justify-between space-x-1 px-2">
//               <Text strong underline>
//                 {t("voip.followUpAction")}
//               </Text>
//               <Select
//                 onChange={setQualification}
//                 value={qualification}
//                 defaultValue={"2"}
//                 style={{ width: "9rem" }}
//                 options={[
//                   {
//                     label: t("voip.always"),
//                     value: "1",
//                   },
//                   {
//                     label: t("voip.ask_before"),
//                     value: "2",
//                   },
//                   {
//                     label: t("voip.never"),
//                     value: "0",
//                   },
//                 ]}
//               />
//             </div>
//           </div>
//         ),
//         extra: (
//           <div className="flex flex-row items-center space-x-0.5">
//             <Tag
//               style={{ marginTop: "-2px", marginInlineEnd: 1 }}
//               color="#1d4ed8"
//             >
//               <div className="font-semibold">
//                 {internalforward.qualification === "2"
//                   ? t("voip.ask_before")
//                   : internalforward.qualification === "1"
//                   ? t("voip.always")
//                   : t("voip.never")}
//               </div>
//             </Tag>

//             {displaySaveQualify ? (
//               <>
//                 <Tooltip title={t("voip.cancel")}>
//                   <Button
//                     onClick={() => setExpandedKeys([])}
//                     size="small"
//                     type="link"
//                     danger
//                     icon={<CloseOutlined style={{ fontSize: 15 }} />}
//                   />
//                 </Tooltip>

//                 <Tooltip title={t("voip.save")}>
//                   <Button
//                     onClick={() => handleActionSubmit()}
//                     size="small"
//                     type="link"
//                     loading={loadingSaveQualify}
//                     icon={
//                       <SaveOutlined
//                         className="hover:bg-green-100"
//                         style={{ fontSize: 16, color: "green" }}
//                       />
//                     }
//                   />
//                 </Tooltip>
//               </>
//             ) : (
//               <Tooltip title={t("voip.edit")}>
//                 <Button
//                   onClick={() =>
//                     setExpandedKeys((prev) =>
//                       prev.includes("qualification")
//                         ? setExpandedKeys([])
//                         : setExpandedKeys(["qualification"])
//                     )
//                   }
//                   size="small"
//                   type="link"
//                   icon={<FiEdit style={{ fontSize: 15 }} />}
//                 />
//               </Tooltip>
//             )}
//           </div>
//         ),
//         showArrow: false,
//         style: panelStyle,
//       },
//     ],
//     [
//       t,
//       forwardingAction,
//       beepsNum,
//       forwardingTo,
//       displaySaveForward,
//       panelStyle,
//       qualification,
//       displaySaveQualify,
//       handleActionSubmit,
//       internalforward,
//     ]
//   );

//   return (
//     <div className="relative h-[23rem] ">
//       <Divider
//         plain
//         orientation="left"
//         style={{ marginTop: 0 }}
//         className="t-0 relative px-4"
//       >
//         <span className="text-sm text-slate-400">
//           {t("voip.renvoi-title-page")}
//         </span>
//       </Divider>

//       <div className=" h-[20.5rem] space-y-4 overflow-y-auto px-1">
//         <Collapse
//           // accordion
//           collapsible="icon"
//           style={{ border: "none", backgroundColor: "rgb(248 250 252)" }}
//           items={collapseItems}
//           size="small"
//           onChange={setExpandedKeys}
//           activeKey={["forwarding", "qualification"]}
//         />
//       </div>
//     </div>
//   );
// };
// //
// export default Settings;
