import {
  CalendarOutlined,
  CommentOutlined,
  DownloadOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  FireOutlined,
  MailOutlined,
  MobileOutlined,
  PlusOutlined,
  ShareAltOutlined,
  SnippetsOutlined,
  StarOutlined,
  UnorderedListOutlined,
  WarningOutlined,
} from "@ant-design/icons";

import { Button, Dropdown, Menu, Space, Tooltip } from "antd";
import TicketIconSphere from "components/icons/TicketIconSphere";
import {
  Calendar,
  HeartHandshake,
  Mail,
  MessageCircle,
  Phone,
  Plus,
  Repeat2,
  StickyNote,
  Ticket,
} from "lucide-react";
import React from "react";
import { BiPhone } from "react-icons/bi";
import { FaSms } from "react-icons/fa";
import { HiOutlineCalendar } from "react-icons/hi";
import { HiOutlineTicket } from "react-icons/hi2";
import { MdEmail, MdOutlineEmail, MdOutlineSms, MdSms } from "react-icons/md";

const HeaderAction = () => {
  return (
    <div>
      <Space.Compact block>
        <Tooltip title="Call">
          <Dropdown
            placement="bottomRight"
            overlay={
              <Menu
                items={[
                  {
                    key: "1",
                    label: "Action 1",
                    icon: <WarningOutlined />,
                  },
                  {
                    key: "2",
                    label: "Action 2",
                    icon: <MailOutlined />,
                  },
                  {
                    key: "3",
                    label: "Action 3",
                    icon: <MobileOutlined />,
                  },
                ]}
              />
            }
            trigger={["hover"]}
          >
            <Button icon={<Phone className="w-[18px]" />} />
          </Dropdown>
        </Tooltip>
        <Tooltip title="SMS">
          <Button icon={<MessageCircle className="w-[18px]" />} />
        </Tooltip>
        <Tooltip title="Email">
          <Button icon={<Mail className="w-[18px]" />} />
        </Tooltip>
        <Tooltip title="Note">
          <Button icon={<StickyNote className="w-[18px]" />} />
        </Tooltip>

        <Tooltip title="Social">
          <Button icon={<Repeat2 className="w-[18px]" />} />
        </Tooltip>
        <Dropdown
          placement="bottomRight"
          overlay={
            <Menu
              items={[
                {
                  key: "1",
                  label: "Deal",
                  icon: <TicketIconSphere size={18} />,
                },
                {
                  key: "2",
                  label: "Ticket",
                  icon: <HeartHandshake className="w-[18px]" />,
                },
                {
                  key: "3",
                  label: "Tasks",
                  icon: <Calendar className="w-[18px]" />,
                },
              ]}
            />
          }
          trigger={["hover"]}
        >
          <Button icon={<Plus className="w-[18px]" />} />
        </Dropdown>
        {/* <Tooltip title="Note">
          <Button icon={<SnippetsOutlined />} />
        </Tooltip>
        <Tooltip title="Task">
          <Button icon={<UnorderedListOutlined />} />
        </Tooltip>
        <Tooltip title="Meeting">
          <Button icon={<CalendarOutlined />} />
        </Tooltip>
        <Tooltip title="Deal">
          <Button icon={<FireOutlined />} />
        </Tooltip>
        <Tooltip title="Comment">
          <Button icon={<CommentOutlined />} />
        </Tooltip>
        <Tooltip title="Star">
          <Button icon={<StarOutlined />} />
        </Tooltip>

        <Tooltip title="Share">
          <Button icon={<ShareAltOutlined />} />
        </Tooltip>
        <Tooltip title="Download">
          <Button icon={<DownloadOutlined />} />
        </Tooltip>
        <Tooltip title="Report">
          <Button icon={<WarningOutlined />} />
        </Tooltip>

        <Dropdown
          placement="bottomRight"
          overlay={
            <Menu
              items={[
                {
                  key: "1",
                  label: "Action 1",
                  icon: <WarningOutlined />,
                },
                {
                  key: "2",
                  label: "Action 2",
                  icon: <MailOutlined />,
                },
                {
                  key: "3",
                  label: "Action 3",
                  icon: <MobileOutlined />,
                },
              ]}
            />
          }
          trigger={["click"]}
        >
          <Button icon={<EllipsisOutlined />} />
        </Dropdown> */}
      </Space.Compact>
    </div>
  );
};

export default HeaderAction;
