import { URL_ENV } from "index";
import { generateAxios } from "services/axiosInstance";

export const storeNewLabel = async (formData) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).post(`/store-label-email`, formData);
//
export const updateLabel = async (formData) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).post(`/update-label-email`, formData);
//
export const deleteLabel = async (id, accountId) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).delete(`/delete-label-email/${id}/${accountId}`);
//
export const affectMailToLabel = async (formData) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).post(`/affect-label-email`, formData);
//
export const unAffectMailToLabel = async (formData) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).post(`/delete-affected-label-email`, formData);
//
export const unSpamMails = async (formData) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).post(`/email-unmark-spam`, formData);
//
export const postConfigLabel = async (formData) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).post(`/store-filter-label`, formData);
//
export const getConfigLabel = async (id) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(`/get-filter-label/${id}`);
//
export const getEmailsByLabel = async (
  accountId,
  labelId,
  page,
  limit,
  search
) =>
  await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(
    `/get-email-by-label?account_id=${accountId}&label_id=${labelId}&page=${page}&limit=${limit}&search=${search}`
  );
