import Quill from "quill";
const BlockEmbed = Quill.import("blots/block/embed");

class SignatureBlot extends BlockEmbed {
  static blotName = "signature";
  static tagName = "div";
  static className = "custom-signature";

  static create(html) {
    const node = super.create();
    node.setAttribute("contenteditable", "false");
    node.innerHTML = html;
    return node;
  }

  static value(node) {
    return node.innerHTML;
  }
}

// **register** your blot
Quill.register(SignatureBlot);
export default SignatureBlot;
