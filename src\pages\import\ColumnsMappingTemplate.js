import { memo } from "react";
import {
  CheckCircleFilled,
  ExclamationCircleFilled,
  RightOutlined,
  WarningOutlined,
} from "@ant-design/icons";
import { Col, Divider, Form, Row, Select, Tooltip, Typography } from "antd";

const GetColsMappingTemplate = memo(function GetColsMappingTemplate({
  fileSelectOptions,
  i,
  e,
  nameLessCsvColumnRef,
  filePreviewData,
  t,
  historyId,
  dbSelectOptions,
  saveMapping,
  current,
  loadingResult,
  columnMappingDefaultValue,
  restoredMapping,
  dateTimeFields,
  dateFormat,
  timeFormat,
}) {
  const getKeyByValue = (fieldIndex) => {
    for (const obj of restoredMapping) {
      const key = Object.keys(obj)[0];
      const value = obj[key];
      if (value === fieldIndex) {
        return Number(key);
      } else if (typeof value === "object" && value?.option === fieldIndex) {
        return Number(key) + 0.1;
      } else if (typeof value === "object" && value?.value === fieldIndex) {
        return Number(key) + 0.2;
      }
    }
    return null;
  };

  const dispatchInitialValuesOnColMapping = (dataIndex) => {
    if (columnMappingDefaultValue[dataIndex]?.type !== null) {
      return Number(columnMappingDefaultValue[dataIndex]?.id) + 0.2;
    } else {
      return columnMappingDefaultValue[dataIndex]?.id;
    }
  };

  const showDateFormat = (importStatus, index) => {
    let formatValue = undefined;
    if (dateTimeFields && dateTimeFields?.length > 0) {
      if (importStatus === null) {
        formatValue = dateTimeFields.find(
          (el) =>
            Number(el?.id) ===
            Number(columnMappingDefaultValue[e?.dataIndex]?.id)
        )?.dateFormat;
      } else if (index) {
        for (const obj of restoredMapping) {
          const key = Object.keys(obj)[0];
          formatValue = dateTimeFields.find(
            (el) => Number(el?.id) === Number(key)
          )?.dateFormat;
        }
      }
      return formatValue;
    }
  };

  return (
    <>
      <Row justify="center" key={i}>
        <Col span={10}>
          <Form.Item>
            <div
              style={{
                textOverflow: "ellipsis",
                overflow: "hidden",
                whiteSpace: "nowrap",
              }}
            >
              {fileSelectOptions[i] && fileSelectOptions[i].label !== "" ? (
                fileSelectOptions[i]?.label
              ) : (
                <span
                  ref={nameLessCsvColumnRef}
                  className="italic text-red-500"
                >
                  {t("import.empty")}
                  <WarningOutlined style={{ marginLeft: "5px" }} />
                </span>
              )}
            </div>
            <div
              style={{
                textOverflow: "ellipsis",
                overflow: "hidden",
                whiteSpace: "nowrap",
              }}
            >
              {filePreviewData[0] &&
              filePreviewData[0][fileSelectOptions[i]?.label.toLowerCase()] !==
                "" ? (
                <Typography.Text mark>
                  {
                    filePreviewData[0][
                      fileSelectOptions[i]?.label.toLowerCase()
                    ]
                  }
                </Typography.Text>
              ) : (
                <>
                  <span className="italic text-red-500">
                    {t("import.empty")}
                    <WarningOutlined style={{ marginLeft: "5px" }} />
                  </span>
                </>
              )}
            </div>
          </Form.Item>
        </Col>
        <Col span={2}>
          <Form.Item>
            <RightOutlined />
          </Form.Item>
        </Col>
        {/* RIGHT SIDE // SELECT WITH ALL THE FIELDS IN THE SELECTED // STEP 2: MAP COLUMNS */}
        <Col span={10}>
          <Form.Item
            name={e?.key}
            initialValue={
              historyId !== 0 && restoredMapping === null
                ? dispatchInitialValuesOnColMapping(e?.dataIndex)
                : getKeyByValue(i)
            }
          >
            <Select
              options={dbSelectOptions}
              placeholder={t("import.selectMapping")}
              popupMatchSelectWidth={false}
              onChange={saveMapping}
              showSearch
              allowClear
              optionFilterProp={["labelText", "type"]}
              filterOption={(input, option) => {
                let labelLower = (option.labelText || "")
                  .toLowerCase()
                  .normalize();
                let typeLower = (option.type || "").toLowerCase().normalize();
                let inputLower = input.toLowerCase().normalize();
                return (
                  labelLower.includes(inputLower) ||
                  typeLower.includes(inputLower)
                );
              }}
              loading={current === 1 && loadingResult}
            />
          </Form.Item>
          {showDateFormat(restoredMapping, i) ? (
            <>
              <Typography.Text type="secondary">
                {t("import.selectedDateFormat")}
              </Typography.Text>
              <Typography.Text type="secondary">
                {/* {showDateFormat(restoredMapping, i)} */}
                {`${dateFormat} ${timeFormat}`}
              </Typography.Text>
            </>
          ) : null}
        </Col>
        <Col style={{ marginLeft: "10px", alignSelf: "baseline" }}>
          {restoredMapping === null &&
            (Number(columnMappingDefaultValue[e?.dataIndex]?.rating_match) >=
            0.7 ? (
              <Tooltip title={t("import.accurateAutoMap")}>
                <CheckCircleFilled style={{ color: "#73d13d" }} />
              </Tooltip>
            ) : (
              <Tooltip title={t("import.autoMapToVerify")}>
                <ExclamationCircleFilled style={{ color: "#ff4d4f" }} />
              </Tooltip>
            ))}
        </Col>
      </Row>
      <Divider style={{ margin: "12px 0" }} />
    </>
  );
});

export default GetColsMappingTemplate;
