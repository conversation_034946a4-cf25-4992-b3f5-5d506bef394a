import { <PERSON><PERSON>, Col, Image, Row, Tooltip } from "antd";
import DOMPurify from "dompurify";
import { setOpenEditor } from "new-redux/actions/mail.actions";
import { formatDateForDisplay } from "pages/tasks/helpers/formatDateToDisplay";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { BiReply, BiReplyAll } from "react-icons/bi";
import { IoReturnDownForwardSharp } from "react-icons/io5";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import MeetEvent from "../components/meetEvent";
import { SlOptions } from "react-icons/sl";
import { URL_ENV } from "index";
import {
  AiOutlineFile,
  AiOutlineFileExcel,
  AiOutlineFilePdf,
  AiOutlineFileText,
  AiOutlineFileWord,
} from "react-icons/ai";
import { DownloadOutlined } from "@ant-design/icons";

const EmailBody = ({
  item,
  i,
  messageDetails,
  loadingDetails,
  loading,
  conditionActions,
  setSubject,
  setReceiverMail,
  setPreviousBody,
  setAddLineBcc,
  setAddLineCc,
  setBCcMail,
  setCcMail,
  setLoading,
  setValue,
  setAttachments,
  getDetailsMessageInbox,
  setSelectedThread,
}) => {
  const dispatch = useDispatch();
  const [t] = useTranslation("common");
  const { user } = useSelector(({ user }) => user);
  const dataAccounts = useSelector((state) => state.mailReducer?.dataAccounts);

  const [openPreviousMessage, setOpenPreviousMessage] = useState({
    state: false,
    key: null,
  });
  const allowedImageExtensions = ["jpg", "jpeg", "png", "gif", "bmp"];

  // console.log({ item });

  function createMarkup(text, type, shortPath) {
    let sanitizedHtml = DOMPurify.sanitize(text, {
      ALLOWED_TAGS: [],
      ALLOWED_ATTR: [],
      USE_PROFILES: { html: true },
    });

    // regex to get the img tags
    const imgTags = sanitizedHtml.match(/<img[^>]*>/g);
    if (imgTags) {
      imgTags.forEach((imgTag) => {
        // console.log({ imgTag });
        // regex to get the img alt attribute

        const alt = imgTag.match(/alt="(.*?)"/);

        if (alt && alt[1]) {
          const altText = alt[1];
          // regex to get the img src attribute

          const srcMatch = imgTag.match(/src="(.*?)"/);

          if (srcMatch && srcMatch[1]) {
            // if (type === "sent") {
            //   const src = srcMatch[1].replace(/[^/]*$/, altText);
            //   sanitizedHtml = sanitizedHtml.replace(srcMatch[1], src);
            // }
            //  else {
            // const src = srcMatch[1].replace(
            //   /[^/]*$/,
            //   URL_ENV?.REACT_APP_BASE_URL + shortPath + altText
            // );
            const src = srcMatch[0].includes('src="https://')
              ? srcMatch[1]
              : srcMatch[1].replace(
                  /[^/]*$/,
                  URL_ENV?.REACT_APP_BASE_URL + shortPath + altText
                );

            // console.log({ imgTag, alt, srcMatch, src });

            sanitizedHtml = sanitizedHtml.replace(srcMatch[1], src);
            // }
          }
        }
      });
    }
    // Process <a> tags: add target="_blank" and aria-label="link" if not already present.
    sanitizedHtml = sanitizedHtml.replace(/<a\b([^>]*?)>/gi, (match, attrs) => {
      let newAttrs = attrs;
      if (!/target\s*=\s*["']_blank["']/i.test(attrs)) {
        newAttrs += ' target="_blank"';
      }
      if (!/aria-label\s*=\s*["'][^"']*["']/i.test(attrs)) {
        newAttrs += ' aria-label="link"';
      }
      return `<a${newAttrs}>`;
    });
    if (type === "sent") {
      return sanitizedHtml;
    } else return { __html: sanitizedHtml };
  }

  const downloadFileAtUrl = (url, customFileName) => {
    fetch(url)
      .then((response) => response.blob())
      .then((blob) => {
        const blobUrl = window.URL.createObjectURL(blob);
        const fileName = customFileName || url.split("/").pop();
        const aTag = document.createElement("a");
        aTag.href = blobUrl;
        aTag.setAttribute("download", fileName);
        document.body.appendChild(aTag);
        aTag.click();
        aTag.remove();
        // Clean up the URL object.
        window.URL.revokeObjectURL(blobUrl);
      });
  };

  const getReceivers = (email) => {
    const selectedAccount =
      dataAccounts.find((a) => a.selected === true)?.label ?? null;
    if (email.from.address === selectedAccount) {
      return email.to.map((e) => e.address);
    } else return [email.from.address];
  };

  return (
    <div className="mailing-detail-mail">
      {conditionActions(messageDetails?.data[i]) && !loadingDetails ? (
        <div className="flex justify-between">
          <div className="space-x-3">
            <Button
              loading={
                loading.state && loading.type === "reply" && loading.key === i
              }
              onClick={() => {
                setSubject(messageDetails?.data[i]?.subject);
                // setPreviousBody(messageDetails?.reply[i]?.body);

                setReceiverMail(
                  // messageDetails?.data[messageDetails?.data?.length - 1]?.from
                  //   ?.address
                  getReceivers(messageDetails?.data[i])
                );
                setPreviousBody(`
                <div style="margin-left: 30px;color: purple">Le ${formatDateForDisplay(
                  messageDetails?.data[i]?.date,
                  `${user?.location?.date_format} ${user?.location?.time_format}`,
                  user,
                  t
                )},<<p style="display: inline; text-decoration: underline;color:#1677ff;">${
                  messageDetails?.data[i]?.from?.address
                }</p>> a écrit :</b>
                 <div style="border-left: 2px solid #ccc;border-left:3px solid #ccc;margin-left: 5px">
                 <div style="margin-left: 10px">${
                   messageDetails?.data[i]?.body
                 }</div>
                 </div></div>`);
                setAddLineBcc(false);
                setAddLineCc(false);
                setBCcMail([]);
                setCcMail([]);
                setAttachments(messageDetails?.data[i]?.attachments);

                dispatch(
                  setOpenEditor({
                    state: true,
                    type: "reply",
                  })
                );
                setLoading({
                  state: true,
                  type: "reply",
                  key: i,
                });
                setValue("");
                setSelectedThread(messageDetails?.data[i]);
              }}
              icon={<BiReply />}
            >
              <span>{t("mailing.Reply")}</span>
            </Button>

            <Button
              loading={
                loading.state &&
                loading.type === "replyAll" &&
                loading.key === i
              }
              onClick={() => {
                // let arrayRepliesTO = [];
                // let arrayRepliesCC = [];
                // let arrayRepliesCCI = [];

                // // messageDetails?.data[i]?.to &&
                // //   messageDetails?.data[i]?.to?.length > 0 &&
                // //   messageDetails?.data[i]?.to?.map((item) =>
                // //     arrayRepliesTO.push(item.address)
                // //   );
                // // console.log({ messageDetails });
                // // console.log({ item }, messageDetails?.data[i]);

                // arrayRepliesTO.push(messageDetails?.data[i]?.from.address);
                // // messageDetails?.data[i]?.to?.length > 1 &&
                // //   messageDetails?.data[i]?.to?.map((item) =>
                // //     arrayRepliesCC.push(item.address)
                // //   );
                // // messageDetails?.data[i]?.cc &&
                // //   messageDetails?.data[i]?.cc?.length > 0 &&
                // //   messageDetails?.data[i]?.cc?.map((item) =>
                // //     arrayRepliesCC.push(item.address)
                // //   );
                // const selectedAccount = dataAccounts.find(
                //   (a) => a.selected === true
                // )?.label;
                // item?.to?.length &&
                //   item?.to?.forEach(
                //     (item) =>
                //       item?.address !== selectedAccount &&
                //       arrayRepliesCC.push(item.address)
                //   );
                // item?.cc &&
                //   item?.cc?.length &&item?.address !== selectedAccount &&
                //   item?.cc?.forEach((item) =>
                //     arrayRepliesCC.push(item.address)
                //   );
                // messageDetails?.data[i]?.cci &&
                //   messageDetails?.data[i]?.cci?.length > 0 &&
                //   messageDetails?.data[i]?.cci?.forEach((item) =>
                //     arrayRepliesCCI.push(item.address)
                //   );

                // // setReceiverMail([...arrayRepliesTO]);
                // setReceiverMail(getReceivers(messageDetails?.data[i]));
                // setCcMail([...arrayRepliesCC]);
                // setBCcMail([...arrayRepliesCCI]);
                let arrayRepliesTO = [];
                let arrayRepliesCC = [];
                let arrayRepliesCCI = [];

                if (messageDetails?.data[i]?.from?.address) {
                  arrayRepliesTO.push(messageDetails?.data[i]?.from.address);
                }

                const selectedAccount = dataAccounts.find(
                  (a) => a.selected
                )?.label;

                if (item?.to?.length) {
                  item.to.forEach((recipient) => {
                    if (recipient?.address !== selectedAccount) {
                      arrayRepliesCC.push(recipient.address);
                    }
                  });
                }

                if (item?.cc?.length) {
                  item.cc.forEach((recipient) => {
                    if (recipient?.address !== selectedAccount) {
                      arrayRepliesCC.push(recipient.address);
                    }
                  });
                }

                if (messageDetails?.data[i]?.cci?.length) {
                  messageDetails.data[i].cci.forEach((recipient) => {
                    arrayRepliesCCI.push(recipient.address);
                  });
                }

                // arrayRepliesTO = [...new Set(arrayRepliesTO)];
                arrayRepliesTO = getReceivers(messageDetails?.data[i]);

                arrayRepliesCC = [...new Set(arrayRepliesCC)];
                //
                arrayRepliesCC = arrayRepliesCC.filter(
                  (address) => !arrayRepliesTO.includes(address)
                );

                setReceiverMail(getReceivers(messageDetails?.data[i]));
                setCcMail([...arrayRepliesCC]);
                setBCcMail([...arrayRepliesCCI]);
                setSubject(messageDetails?.data[i]?.subject);
                setAttachments(messageDetails?.data[i]?.attachments);

                setPreviousBody(`
                <div style="margin-left: 30px;color: purple">Le ${formatDateForDisplay(
                  messageDetails?.data[i]?.date,
                  `${user?.location?.date_format} ${user?.location?.time_format}`,
                  user,
                  t
                )},<<p style="display: inline; text-decoration: underline;color:#1677ff;">${
                  messageDetails?.data[i]?.from?.address
                }</p>> a écrit :</b>
                 <div style="border-left: 2px solid #ccc;border-left:3px solid #ccc;margin-left: 5px">
                 <div style="margin-left: 10px">${
                   messageDetails?.data[i]?.body
                 }</div>
                 </div></div>`);
                dispatch(
                  setOpenEditor({
                    state: true,
                    type: "replyAll",
                  })
                );

                setValue("");
                setSelectedThread(messageDetails?.data[i]);
                setLoading({
                  state: true,
                  type: "replyAll",
                  key: i,
                });
              }}
              icon={<BiReplyAll />}
            >
              {t("mailing.ReplyAll")}
            </Button>

            <Button
              loading={
                loading.state && loading.type === "Forward" && loading.key === i
              }
              onClick={() => {
                setReceiverMail([]);
                setBCcMail([]);
                setCcMail([]);
                setAddLineBcc(false);
                setAddLineCc(false);
                setLoading({
                  state: true,
                  type: "Forward",
                  key: i,
                });
                setSubject(messageDetails?.data[i]?.subject);
                setAttachments(messageDetails?.data[i]?.attachments);
                dispatch(
                  setOpenEditor({
                    state: true,
                    type: "Forward",
                  })
                );
                // setPreviousBody(messageDetails?.data[i].body);
                // setValue(messageDetails?.data[i].body);
                setValue(`
                <div >---------- Forwarded message ---------</div>
                <div>From:<b> ${
                  messageDetails?.data[i]?.from.name ?? ""
                }</b>< ${messageDetails?.data[i]?.from.address} ></div>
                <div >Date: ${formatDateForDisplay(
                  messageDetails?.data[i]?.date,
                  `${user?.location?.date_format} ${user?.location?.time_format}`,
                  user,
                  t
                )}</div>
                <div >Subject: ${messageDetails?.data[i].subject}</div>
                <div >To: ${messageDetails?.data[i].to.map(
                  (item) => item.address
                )}</div>
    
                ${createMarkup(
                  messageDetails?.data[i].body,
                  "sent",
                  messageDetails?.data[i]?.attachments[0]?.shortPath
                )}
    
                `);
                setSelectedThread(messageDetails?.data[i]);
              }}
              icon={<IoReturnDownForwardSharp className="mr-1 h-4 w-4" />}
            >
              {t("mailing.Forward")}
            </Button>
          </div>
        </div>
      ) : null}
      {item.event
        ? item.event.map((item, i) => <MeetEvent dataMeet={item} />)
        : null}
      <div className="mt-5">
        <span
          dangerouslySetInnerHTML={createMarkup(
            item?.newMessageReply ?? item.body,
            "",
            item.attachments[0]?.shortPath
          )}
        ></span>
      </div>
      {item?.previousMessageReply && item.newMessageReply !== null ? (
        <Tooltip
          title={
            openPreviousMessage.state &&
            openPreviousMessage.key === messageDetails?.data[i]
              ? `masquer les détails`
              : `afficher les détails`
          }
        >
          <SlOptions
            className=" mr-1 h-4 w-5 cursor-pointer rounded-lg  bg-[#DADCE0] text-gray-800"
            style={{
              padding: "2px",
              lineHeight: "6px",
            }}
            onClick={() =>
              setOpenPreviousMessage({
                state: !openPreviousMessage.state,
                key: messageDetails?.data[i],
              })
            }
          />
        </Tooltip>
      ) : null}
      {openPreviousMessage.state &&
      openPreviousMessage.key === messageDetails?.data[i] ? (
        <div className="mt-5">
          {/* {console.log("item.previousMessageReply,", item.previousMessageReply)} */}

          <span
            dangerouslySetInnerHTML={createMarkup(
              messageDetails?.data[i].previousMessageReply,
              "",
              item.attachments[0]?.shortPath
            )}
          ></span>
        </div>
      ) : null}
      {messageDetails?.data[i]?.attachments?.length > 0 ? (
        <>
          <p style={{ fontWeight: "bold", marginTop: "5px" }}>
            {t("mailing.Attachments")}
          </p>
          <Row gutter={16}>
            <Image.PreviewGroup>
              {messageDetails?.data[i]?.attachments?.map((item, index) => (
                <Col span={4}>
                  {allowedImageExtensions.includes(
                    item?.filename?.split(".")[1]?.toLowerCase()
                  ) ? (
                    <Image
                      width={80}
                      height={80}
                      src={
                        URL_ENV?.REACT_APP_BASE_URL +
                        item.shortPath +
                        item.filename
                      }
                    />
                  ) : item.filename.split(".")[1] === "pdf" ? (
                    <Tooltip placement="bottom" title="Voir ">
                      <a
                        href={`${URL_ENV?.REACT_APP_BASE_URL}${item.shortPath}${item.filename}`}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <AiOutlineFilePdf
                          style={{
                            fontSize: "40px",
                            color: "red",
                            height: 80,
                          }}
                        />
                      </a>
                    </Tooltip>
                  ) : item.filename.split(".")[1] === "csv" ? (
                    <Tooltip placement="bottom" title="Voir">
                      <a
                        href={`${URL_ENV?.REACT_APP_BASE_URL}${item.shortPath}${item.filename}`}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <AiOutlineFileExcel
                          style={{
                            fontSize: "40px",
                            color: "green",
                            height: 80,
                          }}
                        />
                      </a>
                    </Tooltip>
                  ) : item.filename.split(".")[1] === "doc" ||
                    item.filename.split(".")[1] === "docx" ? (
                    <Tooltip placement="bottom" title="Voir ">
                      <a
                        href={`${URL_ENV?.REACT_APP_BASE_URL}${item.shortPath}${item.filename}`}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <AiOutlineFileWord
                          style={{
                            fontSize: "40px",
                            color: "blue",
                            height: 80,
                          }}
                        />
                      </a>
                    </Tooltip>
                  ) : item.filename.split(".")[1] === "txt" ? (
                    <Tooltip placement="bottom" title="Voir ">
                      <a
                        href={`${URL_ENV?.REACT_APP_BASE_URL}${item.shortPath}${item.filename}`}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <AiOutlineFileText
                          style={{
                            fontSize: "40px",
                            height: 80,
                          }}
                        />
                      </a>
                    </Tooltip>
                  ) : (
                    <Tooltip placement="bottom" title="Voir ">
                      <a
                        href={`${URL_ENV?.REACT_APP_BASE_URL}${item.shortPath}${item.filename}`}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <AiOutlineFile
                          style={{
                            fontSize: "40px",
                            color: "gray",
                            height: 80,
                          }}
                        />
                      </a>
                    </Tooltip>
                  )}

                  <Row>
                    <Tooltip placement="bottom" title={t("import.download")}>
                      <Button
                        icon={<DownloadOutlined />}
                        size="small"
                        onClick={() => {
                          downloadFileAtUrl(
                            URL_ENV?.REACT_APP_BASE_URL +
                              item.shortPath +
                              item.filename,
                            item.origineName
                          );
                        }}
                      ></Button>
                    </Tooltip>
                    <Tooltip placement="bottom" title={item?.origineName}>
                      {item?.origineName?.length > 16 ? (
                        <p className="ml-1">
                          {item?.origineName?.substring(0, 16)}
                          ...
                        </p>
                      ) : (
                        <p className="ml-1">
                          {item?.origineName?.substring(0, 16)}
                        </p>
                      )}
                    </Tooltip>
                  </Row>
                </Col>
              ))}
            </Image.PreviewGroup>
          </Row>
        </>
      ) : null}
    </div>
  );
};

export default EmailBody;
