import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Dropdown,
  <PERSON>,
  Tooltip,
  Typography,
} from "antd";
import React, { memo, useMemo, useState } from "react";
import { humanDate } from "../voip/helpers/helpersFunc";
import { Minus } from "lucide-react";
import {
  DeleteOutlined,
  EditOutlined,
  ExportOutlined,
  MessageOutlined,
  RestOutlined,
} from "@ant-design/icons";
import { getTokenRoom } from "../../new-redux/actions/visio.actions/createVisio";
import { useDispatch } from "react-redux";
import { getName } from "../layouts/chat/utils/ConversationUtils";
import { AvatarChat } from "../../components/Chat";
import Confirm from "../../components/GenericModal";
import { FiMoreVertical } from "react-icons/fi";
import {
  resetStateOtherUser,
  setChatSelectedConversation,
  setChatSelectedParticipants,
} from "../../new-redux/actions/chat.actions";
import MainService from "../../services/main.service";
import { setOpenTaskRoomDrawer } from "../../new-redux/actions/tasks.actions/handleTaskRoomDrawer";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { URL_ENV } from "index";

const HeaderVisio = memo(
  ({ detailsMeet, isOpen, setExterneUpdate, setIdTask, handleDelete }) => {
    const dispatch = useDispatch();
    const [open, setOpen] = useState(false);
    const [t] = useTranslation("common");
    const { user } = useSelector((state) => state.user);
    const openChat = async (id) => {
      // MainService.createRoomTask({
      //   task_id: id,
      // })
      dispatch(setOpenTaskRoomDrawer(true));

      const singleTaskData = detailsMeet;
      // console.log("CONTENT OF THE TASK", content);
      let usr_ids = ",";
      let usr_ids_arr = [];

      let guestsIds =
        Object.keys(singleTaskData).length > 0 &&
        singleTaskData?.guests &&
        Object.keys(singleTaskData).length > 0 &&
        singleTaskData?.guests.map((guest) =>
          guest?.uuid ? guest?.uuid : null
        );
      let followersIds =
        Object.keys(singleTaskData).length > 0 &&
        singleTaskData?.followers &&
        Object.keys(singleTaskData).length > 0 &&
        singleTaskData?.followers.map((follower) =>
          follower?.uuid ? follower?.uuid : null
        );

      usr_ids_arr = [
        ...guestsIds,
        ...followersIds,
        singleTaskData?.creator?.id !== singleTaskData?.owner_id?.id
          ? singleTaskData?.owner_id?.uuid
          : null,
      ];

      //get user_ids from followers and guests if they had uuid
      singleTaskData?.followers?.forEach((follower) => {
        if (!Array.isArray(follower?.uuid)) {
          usr_ids_arr.push(follower?.uuid);
        }
      });

      singleTaskData?.guests?.forEach((guest) => {
        if (!Array.isArray(guest?.uuid)) {
          usr_ids_arr.push(guest?.uuid);
        }
      });

      //remove duplicates
      usr_ids_arr = [...new Set(usr_ids_arr)]
        .filter((id) => id !== null)
        .filter((el) => el !== singleTaskData?.creator?.uuid);

      await MainService.createRoomTask({
        relation_id: id,
        name: singleTaskData?.label,
        description: singleTaskData?.label,
        admin_id: singleTaskData?.creator?.uuid,
        users_ids: usr_ids_arr?.toString(),
      })
        .then((res) => {
          if (!res?.data?.message) {
            MainService.assignRoomToTask(id, {
              room_id: res?.data?.room?._id,
            })
              .then((res) => {
                //console.log("ASSIGN ROOM TO TASK", res);
              })
              .catch((err) => {
                console.log("ASSIGN ROOM TO TASK ERROR", err);
              });
          }

          dispatch(
            resetStateOtherUser({
              forced: true,
              keepDrawerOpened: false,
              item: null,
            })
          );
          dispatch(
            setChatSelectedConversation({
              selectedConversation: {
                name: res?.data?.room?.name,
                description: res?.data?.room?.description,
                image: res?.data?.room?.image,
                admin_id: res?.data?.room?.admin_id,
                bot: null,
                id: res?.data?.room?._id,
                type: "room",
                mode: "members",
                source: "visio",

                muted_status: false,
                conversationId: res?.data?.room?.conversation_id,
                external: false,
              },
            })
          );
          dispatch(
            setChatSelectedParticipants({
              selectedParticipants: res?.data?.room?.participants ?? [],
            })
          );
        })
        .catch((err) => {
          console.log("ROOM TASK ERROR", err);
        });
    };
    const DropdownOption = memo(({ data }) => {
      const items = [
        {
          label: t("table.edit"),
          key: "2",
          icon: <EditOutlined />,
        },
        {
          label: t("menu1.chat"),
          key: "3",
          icon: <MessageOutlined />,
          disabled:
            (detailsMeet?.guests?.length === 0 ||
              detailsMeet?.guests?.filter((el) => el.uuid !== null)?.length ===
                0) &&
            (detailsMeet?.followers?.length === 0 ||
              detailsMeet?.followers?.filter((el) => el.uuid !== null)
                ?.length === 0) &&
            detailsMeet?.creator?.id === detailsMeet?.owner_id?.id,
        },
        user.id === detailsMeet.owner_id.id ||
        user.id === detailsMeet.creator.id
          ? {
              label: t("table.delete"),
              danger: true,
              key: "4",
              icon: <DeleteOutlined />,
            }
          : "",
      ];

      return (
        <div className="r-8">
          <Dropdown
            trigger={["click"]}
            placement="bottom"
            open={open}
            onOpenChange={(e) => setOpen(e)}
            getPopupContainer={(triggerNode) => triggerNode.parentNode}
            arrow
            menu={{
              items,
              onClick: (e) => {
                e.domEvent.stopPropagation();
                setOpen(false);

                if (e.key === "1") {
                  dispatch(
                    getTokenRoom({
                      room: detailsMeet?.location,
                      errorText1: t("toasts.errorFetchApi"),
                      errorText2: t("toasts.errorRoomNotFound"),
                    })
                  );
                }
                if (e.key === "2") {
                  setExterneUpdate(false);
                  setIdTask(data.id);
                }
                if (e.key === "3") {
                  openChat(data.id);
                }

                if (e.key === "4") {
                  Confirm(
                    `Delete "${data.title}" `,
                    "Confirm",
                    <RestOutlined style={{ color: "red" }} />,
                    function func() {
                      return handleDelete(data.id, data.start_date);
                    },
                    true
                  );
                }
              },
            }}
          >
            <FiMoreVertical className="mt-[2px] h-4 w-4 cursor-pointer text-gray-400 hover:text-gray-700" />
          </Dropdown>
        </div>
      );
    }, []);
    return (
      <div className=" flex w-full items-center  justify-between">
        <div>
          <Space direction="vertical">
            <Typography.Title level={2} ellipsis={true}>
              {detailsMeet.label}
            </Typography.Title>
            <Space>
              <div className="flex space-x-0.5">
                <div>
                  {humanDate(
                    detailsMeet.start_date + " " + detailsMeet.start_time,
                    t
                  )}
                </div>
                {/* <MoveRight /> */}
                <Minus className="" size={16} />
                <div>
                  {detailsMeet.start_date === detailsMeet.end_date
                    ? detailsMeet.end_time
                    : humanDate(
                        detailsMeet.end_date + " " + detailsMeet.end_time,
                        t
                      )}
                </div>
              </div>
              <Divider type="vertical" />
              {t("visio.organizer")} :
              <Tooltip title={getName(detailsMeet?.owner_id?.label, "name")}>
                <div>
                  <AvatarChat
                    fontSize="0.875rem"
                    url={
                      URL_ENV?.REACT_APP_BASE_URL +
                      URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                      detailsMeet?.owner_id?.avatar
                    }
                    type="user"
                    size={38}
                    height={10}
                    width={10}
                    name={getName(detailsMeet?.owner_id?.label, "avatar")}
                    hasImage={detailsMeet?.owner_id?.avatar}
                  />
                </div>
              </Tooltip>
              {detailsMeet.priority ? (
                <>
                  <Divider type="vertical" />
                  {t("tasks.priority")} :
                  <Typography.Paragraph
                    className=""
                    style={{
                      color:
                        detailsMeet.priority === "low"
                          ? "#52c41a"
                          : detailsMeet.priority === "medium"
                          ? "#fadb14"
                          : detailsMeet.priority === "high"
                          ? "#fa8c16"
                          : detailsMeet.priority === "urgent"
                          ? "#f5222d"
                          : null,
                    }}
                  >
                    <Badge
                      color={
                        detailsMeet.priority === "low"
                          ? "#52c41a"
                          : detailsMeet.priority === "medium"
                          ? "#fadb14"
                          : detailsMeet.priority === "high"
                          ? "#fa8c16"
                          : detailsMeet.priority === "urgent"
                          ? "#f5222d"
                          : null
                      }
                    />{" "}
                    {detailsMeet.priority}
                  </Typography.Paragraph>
                </>
              ) : (
                ""
              )}
            </Space>
          </Space>
        </div>
        <Space>
          {/* <Tooltip title={t("visio.editInCalendar")}>
                <Button
                  icon={<EditOutlined />}
                  onClick={() => setIdTask(detailsMeet?.id)}
                >
                  {t("visio.editInCalendar")}
                </Button>
              </Tooltip> */}
          <Button
            icon={<ExportOutlined />}
            type="primary"
            onClick={() =>
              dispatch(
                getTokenRoom({
                  room: detailsMeet.location,

                  errorText1: t("toasts.errorFetchApi"),
                  errorText2: t("toasts.errorRoomNotFound"),
                })
              )
            }
            disabled={isOpen}
          >
            {t("visio.participateMeet")}
          </Button>
          <Divider type="vertical" />
          <DropdownOption
            data={{
              id: detailsMeet.id,
              start_date: detailsMeet.start_date,
              title: detailsMeet.label,
              owner_id: detailsMeet.owner_id.id,
            }}
          />

          {/* {detailsMeet?.owner_id.id === user.id ? (
                <Tooltip title={t("visio.deleteMeeting")}>
                  <Button
                    icon={<DeleteOutlined />}
                    type="primary"
                    danger
                    onClick={() =>
                      Confirm(
                        ` ${t("table.delete")} "${
                          detailsMeet.label
                        }" !`,
                        "Confirm",
                        <DeleteOutlined
                          style={{ color: "red" }}
                        />,
                        function func() {
                          return handleDelete(
                            detailsMeet.id,
                            detailsMeet.start_date
                          );
                        },
                        true,
                        "",
                        loadDelete
                      )
                    }
                  >
                    {t("visio.deleteMeeting")}
                  </Button>
                </Tooltip>
              ) : (
                ""
              )} */}
        </Space>
      </div>
    );
  }
);

export default HeaderVisio;
