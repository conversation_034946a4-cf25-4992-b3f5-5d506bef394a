import React, { useState, useEffect } from "react";
import Collegues from "../contacts/collegues";
import Journal from "../main/journal/journal";

import { Card, Badge, Button, Input, Popover } from "antd";
import {
  HistoryOutlined,
  TeamOutlined,
  PhoneOutlined,
} from "@ant-design/icons";
import Keyboard from "../../../../pages/layouts/webphone/keyborad";

function Home({
  callClick,

  newcallClick,
  closeDrower,
}) {
  const [number, setNumber] = useState("");
  const [navigate, setNavigate] = useState("favorite");

  useEffect(() => {}, []);

  const handler = (event) => {
    if (event.key === "Enter") {
      newcallClick(number);
      closeDrower();
    }
  };

  const makeCall = (number) => {
    newcallClick(number);
    closeDrower();
  };

  return (
    <div>
      <Card
        title={<Badge status="success" />}
        headStyle={{ backgroundColor: "#F1F5F9" }}
        size="small"
        bordered={false}
        extra={[]}
        actions={[
          <Popover
            placement="topRight"
            //   title="Clavier"
            content={
              <div className="w-39">
                <Collegues callClick={null} setNumber={setNumber} />
              </div>
            }
            trigger="click">
            <TeamOutlined
              onClick={() => setNavigate("team")}
              style={{
                fontSize: "18px",
                color: navigate === "team" ? "#1677FF" : "#999c9e",
              }}
            />
          </Popover>,
          <Popover
            placement="topRight"
            //   title="Clavier"
            content={
              <div className="w-39">
                <Journal callClick={callClick} />
              </div>
            }
            trigger="click">
            <HistoryOutlined
              onClick={() => setNavigate("history")}
              style={{
                fontSize: "18px",
                color: navigate === "history" ? "#1677FF" : "#999c9e",
              }}
            />
          </Popover>,
        ]}>
        {
          <>
            <div className="relative mb-3 px-4 ">
              {/* <Call  setNumber={setNumber} number={number} callClick={ backHomeVariable === "conference" ? ConferenceCall : newcallClick   } closeDrower={closeDrower}/> */}

              <Input.Group compact>
                <Input
                  style={{ width: "auto" }}
                  className="pl-9"
                  value={number}
                  onChange={(e) => setNumber(e.target.value)}
                  onKeyPress={(e) => handler(e)}
                />
                <Button
                  type="primary"
                  onClick={() => makeCall(number)}
                  icon={<PhoneOutlined />}></Button>
                {/* <Button type="primary" icon={<MoreOutlined />}></Button> */}
              </Input.Group>
              <div className="absolute left-5 top-1 z-50 bg-white">
                <Keyboard setNumber={setNumber} number={number} />
              </div>
            </div>
          </>
        }
      </Card>
    </div>
  );
}

export default Home;
