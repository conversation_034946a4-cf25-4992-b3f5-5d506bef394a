import { Tag } from "antd";

const CustomTag = ({
  icon,
  handleCloseTag,
  color,
  children,
  closeIcon = true,
}) => {
  return (
    <Tag
      style={{
        borderRadius: "50px",
        margin: "3px 3px",
        fontSize: "12px",
        padding: "2px 3px",
      }}
      color={color}
      icon={icon}
      closeIcon={closeIcon}
      onClose={() => handleCloseTag()}
      bordered={false}
    >
      {children}
    </Tag>
  );
};

export default CustomTag;
