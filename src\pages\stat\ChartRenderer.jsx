import React from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";

const ChartRenderer = ({ pivotData }) => {
  if (
    !pivotData ||
    !pivotData.rowKeys ||
    !pivotData.colKeys ||
    typeof pivotData.getAggregator !== "function"
  ) {
    return <p className="text-center text-red-600">No Data</p>;
  }

  const rowKeys = pivotData.rowKeys;
  const colKeys = pivotData.colKeys;

  if (rowKeys.length === 0 && colKeys.length === 0) {
    return <p className="text-center text-red-600">No Data</p>;
  }

  const isRow = rowKeys.length > 0;
  const baseKeys = isRow ? rowKeys : colKeys;
  const seriesKeys = isRow ? colKeys : rowKeys;

  const categories = baseKeys.map((key) => key.join(" - "));

  const series =
    seriesKeys.length > 0
      ? seriesKeys.map((seriesKey) => ({
          name: seriesKey.join(" - ") || "Total",
          data: baseKeys.map((baseKey) => {
            const r = isRow ? baseKey : seriesKey;
            const c = isRow ? seriesKey : baseKey;
            const value = pivotData.getAggregator(r, c)?.value() || 0;
            return { y: value, z: value };
          }),
        }))
      : [
          {
            name: "Total",
            data: baseKeys.map((baseKey) => {
              const r = isRow ? baseKey : [];
              const c = isRow ? [] : baseKey;
              const value = pivotData.getAggregator(r, c)?.value() || 0;
              return { y: value, z: value };
            }),
          },
        ];

  const options = {
    chart: {
      type: "column",
      height: 400,
    },
    title: {
      text: `${
        pivotData.props.rows.length ? pivotData.props.rows.join(" ") : ""
      }${
        pivotData.props.cols.length
          ? " : " + pivotData.props.cols.join(" ")
          : ""
      }`,
      align: "left",
      style: {
        fontWeight: "normal",
        fontSize: "20px",
        fontFamily: "Inter, sans-serif",
      },
    },
    xAxis: {
      categories: categories,
      crosshair: true,
    },
    yAxis: {
      min: 0,
      title: {
        text: "Values",
      },
    },
    tooltip: {
      headerFormat: '<span style="font-size:10px">{point.key}</span><table>',
      pointFormat:
        '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
        '<td style="padding:0"><b>{point.y:.1f}</b></td></tr>',
      footerFormat: "</table>",
      shared: true,
      useHTML: true,
    },
    plotOptions: {
      column: {
        pointPadding: 0.2,
        borderWidth: 0,
      },
    },
    credits: {
      enabled: false,
    },
    series: series,
  };

  return (
    <div
      style={{ height: "100%", width: "100%" }}
      className="highcharts-figure"
    >
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
};

export default ChartRenderer;
