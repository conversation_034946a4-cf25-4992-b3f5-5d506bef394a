import React, { useEffect, useRef } from "react";
import { Form, InputNumber, Input, Button, Select, Space, Spin } from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { PlusCircleOutlined, RollbackOutlined } from "@ant-design/icons";
import { useNavigate, useParams } from "react-router-dom";
import { toastNotification } from "./ToastNotification";
import Header from "./configurationHelpDesk/Header";
import { AllCountries } from "./AllCountries";
import TabsCompanies from "./TabsCompanies";
import { showNameOrg } from "../new-redux/actions/configCompanies.actions/configCompaniesAction";
import { useDispatch, useSelector } from "react-redux";
import NewTableDraggable from "./NewTableDraggable";
import { setSearch } from "../new-redux/actions/menu.actions/menu";
import { SubmitKeyPress } from "../utils/SubmitKeyPress";
import MainService from "services/main.service";
import NotFoundPage from "pages/404";

const BanksCompanies = () => {
  const [form] = Form.useForm();
  const [currencies, setCurrencies] = useState([]);
  const [data, setData] = useState([]);
  const [editingKey, setEditingKey] = useState("");
  const [id, setId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [loadTable, setLoadTable] = useState(false);

  const isEditing = (record) => record.key === editingKey;
  const [t] = useTranslation("common");
  const [company, setCompany] = useState("");
  const [keyTab, setKeyTab] = useState("2");
  const [currentPage, setCurrentPage] = useState(1);
  const [show404, setShow404] = useState(false);

  const [pageSize, setPageSize] = useState(20);
  const { search } = useSelector((state) => state.form);
  const inputRefs = useRef([]);
  const params = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  useEffect(() => {
    document.getElementById("RIB")?.focus();
  }, [data.length, id]);

  const onFinishFailed = (values) => {
    // console.log(values);
  };

  const handleCharKeyDown = (e) => {
    const onlyLetters = /^[a-zA-Z]+$/;
    const { key } = e;

    if (!onlyLetters.test(key)) {
      e.preventDefault();
    }
  };
  function handleInputNumberKeyDown(e) {
    if (e.key === "." && e.target.value.includes(".")) {
      e.preventDefault();
    }
    if (e.key === "-" && !e.target.value) {
      e.preventDefault();
    }

    if (e.key === "e") {
      e.preventDefault();
    }
    if (
      isNaN(parseInt(e.key)) &&
      ![
        "Tab",
        "Backspace",
        "Delete",
        "ArrowLeft",
        "ArrowRight",
        "Enter",
      ].includes(e.key)
    ) {
      e.preventDefault();
    }
  }
  const EditableCell = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    const inputNode =
      inputType === "selectCurrency" ? (
        <Select
          showSearch
          filterOption={(input, option) =>
            (option?.value.toLowerCase() ?? "").includes(input.toLowerCase())
          }
          options={currencies.map((el) => ({
            label: el.currency + " (" + el.currency_symbol + ")",
            value: el.currency,
          }))}
        />
      ) : inputType === "selectCity" ? (
        <Select
          showSearch
          filterOption={(input, option) =>
            (option?.value.toLowerCase() ?? "").includes(input.toLowerCase())
          }
          options={AllCountries.sort(
            (a, b) =>
              String(a.region).localeCompare(b.region) ||
              String(a.name).localeCompare(b.name)
          ).map((element) => ({
            label: element.region + "/" + element.name,
            value: element.region + "/" + element.name,
          }))}
        />
      ) : inputType === "inputChar" ? (
        <Input
          ref={(el) => (inputRefs.current[index] = el)}
          onKeyPress={handleKeyPress}
          onKeyDown={handleCharKeyDown}
        />
      ) : inputType === "number" ? (
        <InputNumber
          ref={(el) => (inputRefs.current[index] = el)}
          stringMode
          onKeyPress={handleKeyPress}
          onKeyDown={handleInputNumberKeyDown}
          style={{ width: "100%" }}
        />
      ) : (
        <Input
          ref={(el) => (inputRefs.current[index] = el)}
          onKeyPress={handleKeyPress}
        />
      );

    return (
      <td {...restProps}>
        {editing ? (
          <Form.Item
            name={dataIndex}
            style={{
              margin: 0,
            }}
            rules={[
              {
                required: true,
                message: `${title} is required!`,
              },
            ]}
          >
            {inputNode}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };
  const edit = (record) => {
    if (record) {
      form.setFieldsValue({
        RIB: record.RIB,
        Bank: record.Bank,
        Agency: record.Agency,
        city: record.city,
        Swift: record.Swift,
        currency: record.currency,
      });
      setId(record.id);
    } else {
      form.setFieldsValue({
        RIB: "",
        Bank: "",
        Agency: "",
        city: "",
        Swift: "",
        currency: "",
      });
    }
    setEditingKey(record.key);
  };
  const cancel = (record) => {
    setEditingKey("");
    setId(null);
    if (!record.id) {
      setData(data.filter((item) => item.key !== record.key));
    }
  };
  const save = async (key) => {
    setLoadTable(true);
    if (id) {
      try {
        const row = await form.validateFields();
        const res = await MainService.updateBank(
          { ...row, companie_id: params.id },
          id
        );
        setEditingKey("");
        setData((prev) =>
          prev.map((el) =>
            el.id === res.data.data.id
              ? {
                  ...res.data.data,
                  key: res.data.data.id,
                }
              : el
          )
        );
        form.setFieldsValue({
          RIB: "",
          Bank: "",
          Agency: "",
          city: "",
          Swift: "",
          currency: "",
        });
        setLoadTable(false);
        toastNotification(
          "success",
          row.Bank + t("toasts.created"),
          "topRight"
        );
      } catch (errInfo) {
        setLoadTable(false);

        console.log("Validate Failed:", errInfo);
      }
    } else {
      try {
        const row = await form.validateFields();
        const res = await MainService.createBank({
          ...row,
          companie_id: params.id,
        });
        setEditingKey("");
        setData([
          ...data.filter((el) => el.id),
          { ...res.data.data, key: res.data.data.id },
        ]);
        form.setFieldsValue({
          RIB: "",
          Bank: "",
          Agency: "",
          city: "",
          Swift: "",
          currency: "",
        });
        setLoadTable(false);
        toastNotification(
          "success",
          row.Bank + t("toasts.created"),
          "topRight"
        );
      } catch (errInfo) {
        setLoadTable(false);

        console.log("Validate Failed:", errInfo);
      }
    }
  };
  useEffect(() => {
    const getBanks = async () => {
      setLoading(true);
      try {
        const {
          data: { data },
        } = await MainService.getCompanyInSettings(params.id);
        setCompany(data[0]);
        setData(
          data[0].Banks.map((el, i) => ({ ...el, key: el.id, rank: i + 1 }))
        );

        setLoading(false);
      } catch (err) {
        if (err.response.status === 404) {
          setShow404(true);
        }
        setLoading(false);
      }
    };
    const getCurrencies = async () => {
      setLoading(true);
      try {
        const { data } = await MainService.getCurrencies();
        setCurrencies(data?.message);
        setLoading(false);
      } catch (err) {
        setLoading(false);
      }
    };
    if (params.id > 0) getBanks();
    getCurrencies();

    return () => dispatch(setSearch(""));
  }, []);
  const handleKeyPress = (event) => {
    SubmitKeyPress(event, form);
  };

  let columns = [
    {
      title: "RIB",
      dataIndex: "RIB",
      key: "RIB",
      editable: true,
      sorter: (a, b) => a.RIB.localeCompare(b.RIB),
    },
    {
      title: t("companies.bank"),
      dataIndex: "Bank",
      key: "Bank",
      editable: true,
      sorter: (a, b) => a.Bank.localeCompare(b.Bank),
    },
    {
      title: t("companies.agency"),
      dataIndex: "Agency",
      key: "Agency",
      editable: true,
      sorter: (a, b) => a.Agency.localeCompare(b.Agency),
    },
    {
      title: t("companies.countries"),
      dataIndex: "city",
      key: "city",
      width: "250px",
      editable: true,
      sorter: (a, b) => a.city.localeCompare(b.city),
    },
    {
      title: "Swift",
      dataIndex: "Swift",
      key: "Swift",
      editable: true,
      sorter: (a, b) => a.Swift.localeCompare(b.Swift),
    },
    {
      title: t("companies.currency"),
      dataIndex: "currency",
      key: "currency",
      editable: true,
      sorter: (a, b) => a.currency - b.currency,
    },
  ];

  const handleAdd = () => {
    const ids = data.map((object) => {
      return object.id;
    });
    setId(null);
    const newData = {
      key: Math.max(...ids) + 1,
      RIB: "",
      Bank: "",
      Agency: "",
      city: "",
      Swift: "",
      currency: "",
      disabled: true,
    };
    setCurrentPage(Math.ceil([...data, newData].length / pageSize));
    setData([...data, newData]);
    form.setFieldsValue({
      RIB: "",
      Bank: "",
      Agency: "",
      city: "",
      Swift: "",
      currency: "",
    });
    setEditingKey(Math.max(...ids) + 1);
  };
  const onRow = () => {};

  const filteredData = data.filter((item) => {
    return (
      item.RIB.toLowerCase().includes(search.toLowerCase()) ||
      item.Bank.toLowerCase().includes(search.toLowerCase()) ||
      item.Agency.toLowerCase().includes(search.toLowerCase()) ||
      item.city.toLowerCase().includes(search.toLowerCase()) ||
      item.Swift.toLowerCase().includes(search.toLowerCase()) ||
      item.currency.toLowerCase().includes(search.toLowerCase())
    );
  });
  if (show404) {
    return (
      <>
        <Button
          icon={<RollbackOutlined />}
          onClick={() => {
            navigate("/settings/general/companies");
            dispatch(showNameOrg(""));
          }}
          style={{ margin: "16px 16px 0 16px" }}
        >
          {/* {t("companies.accessTableCompanies")} */}
        </Button>
        <div className="height-screen bg-white">
          <NotFoundPage />
        </div>
      </>
    );
  }

  return (
    <>
      <Button
        icon={<RollbackOutlined />}
        onClick={() => {
          navigate("/settings/general/companies");
          dispatch(showNameOrg(""));
        }}
        style={{ margin: "16px 16px 0 16px" }}
      />

      <TabsCompanies keyTab={keyTab} setKeyTab={setKeyTab} company={company} />
      {!loading ? (
        <Space direction="vertical" style={{ width: "100%" }}>
          <Header
            active={"5"}
            editingKey={editingKey}
            handleAdd={handleAdd}
            btnText={t("companies.addbank")}
            disabled={editingKey || search ? true : false}
          />

          <NewTableDraggable
            columns={columns}
            setLoading={setLoadTable}
            isEditing={isEditing}
            data={filteredData}
            setData={setData}
            loading={loadTable}
            save={save}
            edit={edit}
            EditableCell={EditableCell}
            onFinishFailed={onFinishFailed}
            cancel={cancel}
            form={form}
            apiRank="/rank-banks"
            editingKey={editingKey}
            api="banks"
            onRow={onRow}
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            pageSize={pageSize}
            setPageSize={setPageSize}
          />

          <div style={{ width: "0", paddingLeft: "15px" }}>
            <Button
              icon={<PlusCircleOutlined />}
              disabled={editingKey || search ? true : false}
              onClick={handleAdd}
              type="link"
              block
            >
              {t("companies.addbank")}
            </Button>
          </div>
        </Space>
      ) : (
        <div className="flex h-[calc(100vh-57px)] items-center justify-center">
          <Spin />
        </div>
      )}
    </>
  );
};

export default BanksCompanies;
