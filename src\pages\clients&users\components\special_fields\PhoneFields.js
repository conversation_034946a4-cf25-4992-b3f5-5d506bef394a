import { useState, useEffect, useMemo } from "react";
//
import { Input, Select } from "antd";
const { Option } = Select;

const PhoneFields = ({
  fieldValue,
  options,
  defaultOption,
  form,
  fieldId,
  uniqueValue,
  setIsDisable,
  readOnly,
}) => {
  const [phoneNum, setPhoneNum] = useState(fieldValue?.[1]);
  const [dialCode, setDialCode] = useState(fieldValue?.[0]);
  //
  const handleInputChange = (e) => {
    const newValue = e.target.value?.replace(/\D/g, "");
    const regex = /^[0-9]*$/; // Only allow digits
    if (regex.test(newValue) || newValue === undefined) {
      setPhoneNum(newValue);
      setIsDisable(false);
    }
  };

  const handleSelectChange = (value) => {
    setDialCode(value);
    setIsDisable(false);
  };
  //
  useEffect(() => {
    form.setFieldValue(fieldId, [dialCode || defaultOption, phoneNum]);
    if (phoneNum !== fieldValue?.[1] || dialCode !== fieldValue?.[0]) {
      // dialCode !== "+1" && setIsDisable(false);
      form.setFields([
        {
          name: fieldId,
          touched: true,
        },
      ]);
    }
  }, [phoneNum, dialCode]);
  //
  const selectBeforeValue = useMemo(() => {
    if (!dialCode && !defaultOption) return null;
    let value = null;
    if (dialCode) {
      value = dialCode;
    } else if (defaultOption) {
      value = defaultOption;
    }
    if (!value || !value.includes("+")) {
      return null;
    } else return value;
  }, [defaultOption, dialCode]);
  //
  const selectBefore = (
    <Select
      disabled={readOnly && true}
      allowClear
      showSearch
      value={selectBeforeValue}
      // defaultValue={defaultOption}
      onChange={handleSelectChange}
      style={{ width: "6.5rem" }}
      optionFilterProp={["nameEn", "nameFr", "value"]}
      filterOption={(input, option) =>
        option?.nameEn.toLowerCase().includes(input.toLowerCase()) ||
        option?.nameFr.toLowerCase().includes(input.toLowerCase()) ||
        option?.value.toLowerCase().includes(input.toLowerCase())
      }
    >
      {options?.map((option, i) => (
        <Option
          key={i}
          value={option?.dial_code}
          nameEn={option?.name_en}
          nameFr={option?.name_fr}
        >
          {option?.flag} {option?.dial_code}
        </Option>
      ))}
    </Select>
  );
  //
  return (
    <Input
      readOnly={readOnly && true}
      addonBefore={selectBefore}
      style={{ width: "100%" }}
      value={phoneNum}
      onChange={handleInputChange}
    />
  );
};

export default PhoneFields;
