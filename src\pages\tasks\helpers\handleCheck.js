import i18next from "i18next";

// Handle check user from the list (used in guest/followers lists)
export const handleCheck = (
  guestsList,
  key,
  target,
  checkedItems,
  setCheckedItems
) => {
  let guestIndex = guestsList?.findIndex((element) => element?.id === key);
  if (target) {
    if (guestIndex > -1) {
      setCheckedItems([...checkedItems, guestsList[guestIndex]]);
    }
  } else {
    let newArr = checkedItems?.filter((element, i) => element?.id !== key);
    setCheckedItems(newArr);
  }
};

// Handle uncheck user from the list (used in guest/followers lists)
export const uncheckGuest = (checkedItems, setCheckedItems, key) => {
  let newArr = checkedItems?.filter((element, i) => element?.id !== key);
  setCheckedItems(newArr);
};

// Handle check user from the list (used in guest/followers lists)
export const handleReload = () => {
  window.location.reload();
};

// Activities table columns.
export const allColumns = [
  {
    value: "activityType",
    label: i18next.t("common:tasks.tableType"),
    disabled: true,
  },
  {
    value: "label",
    label: i18next.t("common:tasks.tableLabel"),
    disabled: true,
  },
  {
    value: "priority",
    label: i18next.t("common:tasks.tablePriority"),
  },
  {
    value: "activityId",
    label: i18next.t("common:tasks.activityId"),
  },
  {
    value: "startDate",
    label: i18next.t("common:tasks.startDate"),
  },
  {
    value: "dueDate",
    label: i18next.t("common:tasks.tableEndDate"),
  },
  {
    value: "pipeline",
    label: "Pipeline",
  },
  {
    value: "moduleElement",
    label: i18next.t("common:tasks.associatedModule"),
  },
  {
    value: "creator",
    label: i18next.t("common:tasks.creator"),
  },
  {
    value: "owner",
    label: i18next.t("common:tasks.tableOwner"),
  },
  {
    value: "guests",
    label: i18next.t("common:tasks.tableGuests"),
  },
  {
    value: "followers",
    label: i18next.t("common:tasks.followersListTitle"),
  },
  {
    value: "organization",
    label: i18next.t("common:tasks.orgFamily"),
  },
  {
    value: "contact",
    label: i18next.t("common:tasks.contactFamily"),
  },
  {
    value: "code",
    label: "Code",
  },
];
