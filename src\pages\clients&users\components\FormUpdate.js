import { useCallback, useEffect, useRef, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
//
import {
  Form,
  Drawer,
  Space,
  Button,
  Dropdown,
  Tabs,
  Tooltip,
  Typography,
} from "antd";
import { UpOutlined } from "@ant-design/icons";
import {
  getFieldsToCreate,
  getDataToUpdate,
  updateElement,
} from "../services/services";
import { toastNotification } from "../../../components/ToastNotification";
import { FormWithTabsLoader } from "./SkeletonLoader";
import { formattingFieldsForm, formattingDataByType } from "../helpers";
import { useWindowSize } from "./WindowSize";
import DisplayDynamicFieldsByType from "./DisplayDynamicFieldsByType";
import { setUpdateElementSuccessfully } from "../../../new-redux/actions/form.actions/form";
import MainService from "../../../services/main.service";
import "../index.css";
import {
  findNextZIndex,
  generateUrlToView360,
} from "pages/voip/helpers/helpersFunc";
import { getFamilyNameById, getSettingPath } from "../FamilyRouting";
import SharedWithField from "./special_fields/SharedWithField";
import { isGuestConnected } from "utils/role";

const FormUpdate = ({
  open,
  setOpen,
  elementDetails,
  familyId,
  setCatchChange = () => {},
  profile,
  mask,
}) => {
  //
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const windowSize = useWindowSize();
  const location = useLocation();
  const [form] = Form.useForm();
  const updatedFields = useRef({});
  //
  const dropAction = useSelector((state) => state.form.dropAction);
  const currentUser = useSelector((state) => state.user.user);
  //
  const [fieldsAndDataToDisplay, setFieldsAndDataToDisplay] = useState([]);
  const [checkIfRequired, setCheckIfRequired] = useState({});
  const [fieldsValuesBeforeUpdate, setFieldsValuesBeforeUpdate] = useState({});
  const [config, setConfig] = useState({});
  const [matchFieldsIdAndGroupId, setMatchFieldsIdAndGroupId] = useState({});
  const [matchFieldsAndType, setMatchFieldsAndType] = useState({});
  const [selectedTab, setSelectedTab] = useState(null);
  // const [updatedFields, setUpdatedFields] = useState({});
  const [methodSubmit, setMethodSubmit] = useState("");
  const [loadingSubmit, setLoadingSubmit] = useState(false);
  const [submitIsDisable, setSubmitIsDisable] = useState(true);
  //
  // console.log({ fieldsValuesBeforeUpdate });
  //
  const getAllFieldsAndData = useCallback(async () => {
    try {
      const fields = await getFieldsToCreate(
        familyId,
        "update",
        profile,
        elementDetails?.id,
        dropAction?.destination
      );
      setConfig(fields?.data?.config);
      const fieldsValues = await getDataToUpdate(
        elementDetails?.id,
        location?.pathname === "/profile/general" ? true : false
      );
      setFieldsValuesBeforeUpdate(fieldsValues?.data);
      const isCurrentUserUpdating = currentUser.id === elementDetails?.id;
      const { result, matchF_G } = await formattingFieldsForm(
        fields?.data?.data,
        setMatchFieldsAndType,
        setSelectedTab,
        fieldsValues?.data,
        form,
        setCheckIfRequired,
        null,
        null,
        fields?.data?.config,
        updatedFields,
        null,
        null,
        null,
        isCurrentUserUpdating
      );
      setFieldsAndDataToDisplay(result);
      setMatchFieldsIdAndGroupId(matchF_G);
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      setOpen(false);
      throw new Error(err?.message ? err.message : err);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [elementDetails?.id, familyId, open]);
  //
  useEffect(() => {
    if (open && (!elementDetails?.id || !familyId)) {
      setOpen(false);
      toastNotification(
        "error",
        `Missing props ${
          !elementDetails?.id
            ? "elementDetails.id (id of the element)"
            : "familyId (family id of the element"
        }`,
        "topRight",
        7
      );
    } else if (open) {
      form.resetFields();
      getAllFieldsAndData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [form, getAllFieldsAndData]);
  //
  const handleCancelClick = () => {
    setFieldsAndDataToDisplay([]);
    form.resetFields();
    form.resetFields();
    setSubmitIsDisable(true);
    dispatch(setUpdateElementSuccessfully(null));
    setOpen(false);
  };
  //
  const handleSelectedTab = (activeKey) => {
    setSelectedTab(activeKey);
  };
  //
  const handleChangedFields = (changedFields, allFields) => {
    // console.log({ allFields, changedFields });
    submitIsDisable && setSubmitIsDisable(false);
    const field_id = changedFields?.[0]?.name?.[0];
    const value = changedFields?.[0]?.value;
    updatedFields.current = { ...updatedFields, [field_id]: value };
  };
  //
  const onFinish = async (values) => {
    try {
      // console.log({ values });
      setLoadingSubmit(true);
      const check = checkIfMissingFieldsRequired();
      // console.log(check);
      if (!check) return;
      await form.validateFields();
      const allValues = form.getFieldsValue(true);
      // console.log({ allValues });p
      // const updatedValues = Object.keys(values).reduce((obj, key) => {
      //   if (!values[key]) {
      //     if (fieldsValuesBeforeUpdate[key]) {
      //       return { ...obj, [key]: undefined };
      //     }
      //   } else return { ...obj, [key]: values[key] };
      //   return obj;
      // }, {});
      // //
      // for (const key of Object.keys(fieldsValuesBeforeUpdate)) {
      //   if (!updatedValues[key]) {
      //     if (typeof fieldsValuesBeforeUpdate[key] === "object") {
      //       updatedValues[key] = fieldsValuesBeforeUpdate[key]?.id;
      //     } else updatedValues[key] = fieldsValuesBeforeUpdate[key];
      //   }
      // }

      // to handle the profile update because the same component is working in update in all family also in profile
      // const check = location?.pathname?.includes("/profile/general");
      // if (check && updatedValues[323] === "" && fieldsValuesBeforeUpdate[323]) {
      //   updatedValues[323] = fieldsValuesBeforeUpdate[323];
      // }
      const formData = new FormData();
      familyId !== 4 &&
        !isGuestConnected() &&
        formData.append("shared_with", values?.sharedWith);
      for (const key in allValues) {
        const value = allValues[key];
        const type = matchFieldsAndType[key];
        if (
          // to skip the unnecessary field like password ...
          key !== "confirmPassword" &&
          key !== "sharedWith" &&
          !!value
        ) {
          formattingDataByType.update(
            type,
            key,
            type === "file"
              ? {
                  value,
                  filename: allValues?.[`${key}filename`],
                  isActive: allValues?.[`${key}isActive`],
                }
              : value,
            formData,
            config
          );
        }
      }
      // for (let pair of formData.entries()) {
      //   console.log(`${pair[0]}: ${pair[1]}`);
      // }
      const update = await updateElement(elementDetails?.id, formData, profile);
      if (update?.status === 200) {
        if (
          dropAction?.destination ||
          (dropAction?.destination && dropAction?.isFinal)
        ) {
          const updateStageResponse = await MainService.updateElementStage(
            familyId,
            {
              new_stage_id: dropAction?.destination,
              id_element: elementDetails.id,
            }
          );
          if (
            updateStageResponse?.data?.success &&
            updateStageResponse?.data?.is_final
          ) {
            dispatch(
              setUpdateElementSuccessfully({
                ...dropAction,
                closingReasons: updateStageResponse?.data?.data,
                elementId: elementDetails?.id,
              })
            );
          } else {
            dispatch(setUpdateElementSuccessfully(null));
          }
        }
        // if (from === "viewSphere") {
        //   dispatch(setNewInteraction({ type: "updateElement" }));
        // }
        setLoadingSubmit(false);
        setCatchChange && setCatchChange((previewsState) => !previewsState);
        setFieldsAndDataToDisplay([]);
        setSubmitIsDisable(true);
        form.resetFields();
        form.resetFields();
        setOpen(false);
        if (methodSubmit === "save_go_to_details") {
          navigate(generateUrlToView360(familyId, elementDetails?.id, "v2"));
        }
        toastNotification(
          "success",
          t("contacts.successUpdate", {
            x: getFamilyNameById(t, familyId),
            y: elementDetails?.label,
          }),
          "topRight"
        );
      }
    } catch (err) {
      setLoadingSubmit(false);
      if (err?.response?.status === 422) {
        toastNotification(
          "error",
          err?.response?.data?.message || err?.response?.message,
          "topRight",
          7
        );
      } else {
        toastNotification("error", t("toasts.somethingWrong"), "topRight", 3);
      }
      throw new Error(err?.message ? err.message : err);
    } finally {
      setLoadingSubmit(false);
    }
  };
  //
  const onFinishFailed = (errorInfo) => {
    // console.log(errorInfo, errorInfo.errorFields[0].name);
    setSubmitIsDisable(true);
    const [fieldId] = errorInfo.errorFields[0].name;
    const groupId = matchFieldsIdAndGroupId?.get(fieldId);
    setSelectedTab(groupId);
    form.scrollToField(errorInfo.errorFields[0].name);
    form.scrollToField(errorInfo.errorFields[0].name);
    !dropAction &&
      toastNotification(
        "error",
        `${errorInfo.errorFields[0].errors[0]}`,
        "topRight",
        3
      );
  };
  //
  const menuProps = {
    items: [
      {
        label: t("contacts.saveAndGoDetail"),
        // "Save & Go To Details",
        key: "save_go_to_details",
        disabled: submitIsDisable,
      },
    ],
    onClick: (e) => {
      setMethodSubmit(e.key);
      form.submit();
    },
  };
  //
  const DrawerProps = {
    title: elementDetails?.label ? elementDetails.label : "",
    // closeIcon: (
    //   <Button
    //     size="small"
    //     type="text"
    //     icon={<CloseOutlined />}
    //     onClick={() => handleCancelClick()}
    //   />
    //   // <CloseOutlined
    //   //   style={{ fontSize: 16 }}
    //   //   onClick={() => handleCancelClick()}
    //   // />
    // ),
    /////////
    footer: (
      <div className="flex flex-row justify-between">
        <Space
          direction="horizontal"
          // size="middle"
          style={{ padding: "0.250rem" }}
        >
          <Button onClick={handleCancelClick}>{t("contacts.cancel")}</Button>
          <Dropdown.Button
            type="primary"
            disabled={submitIsDisable}
            loading={loadingSubmit}
            trigger={["click"]}
            placement="topLeft"
            icon={<UpOutlined />}
            menu={menuProps}
            onClick={() => {
              setMethodSubmit("create");
              form.submit();
            }}
          >
            {t("contacts.save")}
          </Dropdown.Button>
        </Space>
        <div className="flex items-center">
          {location.pathname !== "/profile/general" &&
            !location.pathname.includes("directory") &&
            !isGuestConnected() && (
              <Typography.Link
                underline
                onClick={() => {
                  window.open(`${getSettingPath(familyId)}`, "_blank");
                }}
              >
                {/* {t("contacts.goToXSetting", {
                  x: getFamilyNameById(t, familyId),
                })} */}
                {t("contacts.fieldSetting")}
              </Typography.Link>
            )}
        </div>
      </div>
    ),
  };
  //
  // Clear States when pathName Change
  const clearSate = useCallback(() => {
    setFieldsAndDataToDisplay([]);
    setFieldsValuesBeforeUpdate({});
    setConfig({});
    setMatchFieldsIdAndGroupId({});
    setCheckIfRequired({});
    setMatchFieldsAndType({});
    setSelectedTab(null);
    // setUpdatedFields({});
    updatedFields.current = {};
    setMethodSubmit("");
    setLoadingSubmit(false);
    setSubmitIsDisable(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location?.pathname]);

  useEffect(() => {
    clearSate();
  }, [clearSate]);
  //
  const checkIfMissingFieldsRequired = () => {
    for (const [key, value] of Object.entries(checkIfRequired)) {
      if (!updatedFields.current[key] || updatedFields.current[key] === "") {
        // console.log(form.getFieldValue(key));
        if (!form.getFieldValue(key)) {
          setSubmitIsDisable(true);
          setSelectedTab(value["groupId"]);
          form.scrollToField(Number(key));
          // form.scrollToField(Number(key));
          form.setFields([
            {
              name: Number(key),
              errors: [`The "${value["alias"]}" field is required!`],
            },
          ]);
          !dropAction &&
            toastNotification(
              "error",
              `The "${value["alias"]}" field is required!`,
              "topRight",
              3
            );
          return false;
        }
      }
    }
    return true;
  };
  //
  useEffect(() => {
    if (fieldsAndDataToDisplay.length && dropAction) {
      const timer = setTimeout(() => {
        checkIfMissingFieldsRequired();
        clearTimeout(timer);
      }, 500);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dropAction, fieldsAndDataToDisplay.length]);

  //
  return (
    <Drawer
      title={DrawerProps.title}
      placement="right"
      width={windowSize?.width / 2.5 < 600 ? 600 : windowSize?.width / 2.5}
      open={open}
      // closeIcon={DrawerProps.closeIcon}
      onClose={handleCancelClick}
      footer={DrawerProps.footer}
      extra={DrawerProps.extra}
      mask={mask}
      zIndex={findNextZIndex()}
    >
      {fieldsAndDataToDisplay?.length ? (
        <div className="flex-column-container">
          <Form
            form={form}
            layout={"vertical"}
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
            autoComplete="off"
            onFieldsChange={handleChangedFields}
          >
            <Tabs
              style={{ marginRight: "-23px" }}
              type="card"
              tabBarGutter={8}
              tabPosition="left"
              activeKey={selectedTab}
              onChange={handleSelectedTab}
              items={fieldsAndDataToDisplay?.map((group) => {
                return {
                  label: (
                    <Tooltip title={group?.group_name} placement="leftBottom">
                      <div className="tooltip-container">
                        {group?.group_name}
                      </div>
                    </Tooltip>
                  ),
                  key: group?.id,
                  children: (
                    <div
                      className="overflow-content"
                      style={{
                        height:
                          windowSize.height -
                          (familyId === 4 || isGuestConnected() ? 150 : 220),
                      }}
                    >
                      {group?.fields?.map((field) => (
                        <DisplayDynamicFieldsByType
                          form={form}
                          config={config}
                          key={field?.id}
                          fieldType={field?.type}
                          fieldId={field?.id}
                          familyId={field?.family_id}
                          fieldModule={field?.field_module_id}
                          isModule={field?.isModule}
                          label={field?.label}
                          value={field?.value ?? undefined}
                          options={field?.options}
                          required={field?.required}
                          description={field?.description}
                          placeholder={field?.placeholder}
                          setSubmitIsDisable={setSubmitIsDisable}
                          readOnly={field?.read_only}
                          isMultiple={field?.multiple}
                        />
                      ))}
                    </div>
                  ),
                };
              })}
            />
            {familyId !== 4 && !isGuestConnected() && (
              <Form.Item key="sharedWith" name="sharedWith">
                <SharedWithField
                  selectedValue={fieldsValuesBeforeUpdate?.shared_with}
                  userId={currentUser?.id}
                  form={form}
                  departments={config?.departements}
                  t={t}
                  setSubmitIsDisable={setSubmitIsDisable}
                />
              </Form.Item>
            )}
          </Form>
        </div>
      ) : (
        <FormWithTabsLoader tabsNum={4} inputNum={6} />
      )}
    </Drawer>
  );
};

export default FormUpdate;
