import React, {
  memo,
  useEffect,
  useRef,
  forwardRef,
  useImperativeHandle,
} from "react";
import { <PERSON><PERSON>, Card, Divider, Empty, List, Space, Typography } from "antd";
import { useWindowSize } from "pages/clients&users/components/WindowSize";
import { useTranslation } from "react-i18next";
import DisplayAvatar from "../components/DisplayAvatar";
import { renderDescription } from "./Directory";
import {
  InfoCircleOutlined,
  MessageOutlined,
  PhoneOutlined,
} from "@ant-design/icons";
import useActionCall from "../helpers/ActionCall";
import { FiCopy } from "react-icons/fi";
import { useDispatch } from "react-redux";
import "../index.css";
import ViewSphere2 from "pages/components/DetailsProfile/ViewSphere2";
import { ImUsers } from "react-icons/im";
import { FaUsers } from "react-icons/fa";

// Create a wrapper component for ViewSphere2 that handles cleanup
const MemoizedViewSphere = memo(({ elementId, from }) => {
  const viewSphereRef = useRef(null);
  const dispatch = useDispatch();

  // Cleanup when this component unmounts
  useEffect(() => {
    return () => {
      // Reset Vue360 state when unmounting
      dispatch({ type: "RESET_CONTACT_HEADER_INFO" });
      dispatch({ type: "RESET_VUE360_STATE" });

      // Force cleanup of any DOM elements
      const viewContainer = document.getElementById("view-container");
      if (viewContainer && viewContainer.parentNode) {
        try {
          viewContainer.parentNode.removeChild(viewContainer);
        } catch (e) {
          console.error("Failed to remove view container", e);
        }
      }

      // Remove other potential dangling elements
      const elementsToRemove = document.querySelectorAll(
        ".ant-modal-root, .ant-tooltip, .ant-popover"
      );
      elementsToRemove.forEach((el) => {
        if (el && el.parentNode) {
          try {
            el.parentNode.removeChild(el);
          } catch (e) {
            console.error("Failed to remove element", e);
          }
        }
      });
    };
  }, [dispatch]);

  return <ViewSphere2 ref={viewSphereRef} elementId={elementId} from={from} />;
});

const DisplayDirectoryInfo = forwardRef(
  (
    {
      contactInfo,
      currentUser,
      handleOpenMgsDrawer,
      setReceiverEmail,
      setOpenDrawerInfo,
      setElementDetails,
      typesAndChannels,
    },
    ref
  ) => {
    const [t] = useTranslation("common");
    const call = useActionCall();
    const dispatch = useDispatch();
    const windowSize = useWindowSize();
    const componentMounted = useRef(true);

    // Export cleanup methods to parent component
    useImperativeHandle(
      ref,
      () => ({
        cleanup: () => {
          componentMounted.current = false;

          // Reset any internal state
          if (contactInfo?.id && contactInfo?.family_id) {
            dispatch({ type: "RESET_CONTACT_HEADER_INFO" });
            dispatch({ type: "RESET_VUE360_STATE" });
          }
        },
      }),
      [contactInfo, dispatch]
    );

    // Cleanup on unmount
    useEffect(() => {
      return () => {
        componentMounted.current = false;
      };
    }, []);

    if (!contactInfo) {
      return (
        <div
          className="flex w-full items-center justify-center"
          style={{ height: (windowSize?.height * 3) / 4 }}
        >
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={<span>{t("voip.clickToDisplayInfo")}</span>}
          />
        </div>
      );
    } else if (contactInfo?.group || contactInfo?.queue) {
      return (
        <div className="px-2 py-1.5">
          <Card styles={{ body: { padding: "10px 10px 0px 10px" } }}>
            <List
              itemLayout="vertical"
              size="small"
              dataSource={[contactInfo]}
              renderItem={(item, index) => (
                <List.Item key={index}>
                  <List.Item.Meta
                    avatar={
                      <DisplayAvatar
                        name={contactInfo.name}
                        urlImg={contactInfo.image}
                        size={45}
                        icon={
                          contactInfo?.queue ? (
                            <ImUsers />
                          ) : contactInfo?.group ? (
                            <FaUsers />
                          ) : null
                        }
                      />
                    }
                    title={<p className="font-semibold">{contactInfo?.name}</p>}
                    description={
                      <Space size={3} split={<Divider type="vertical" />}>
                        {renderDescription(item, typesAndChannels, t)}
                        <Typography.Link
                          onClick={() => call(contactInfo.extension)}
                          key="extension"
                        >
                          <PhoneOutlined
                            rotate={100}
                            style={{ fontSize: 13 }}
                          />{" "}
                          {contactInfo.extension}
                        </Typography.Link>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
          <Space
            style={{
              padding: "0.75rem 0px",
              overflowY: "auto",
              maxHeight: windowSize.height - 200,
            }}
            wrap
          >
            {contactInfo.members.map((member, i) => (
              <Card
                key={i}
                styles={{
                  body: { position: "relative", width: 250 },
                }}
                actions={[
                  <Button
                    key="call"
                    size="small"
                    type="link"
                    onClick={() =>
                      call(
                        member.extension,
                        member?.member_id,
                        member?.family_id
                      )
                    }
                    disabled={member.member_id === currentUser.id}
                    icon={
                      <PhoneOutlined
                        rotate={100}
                        style={{ fontSize: 16 }}
                        key="call"
                      />
                    }
                  />,
                  <Button
                    key="chat"
                    size="small"
                    type="link"
                    disabled={member.member_id === currentUser.id}
                    onClick={() => handleOpenMgsDrawer(member.uuid)}
                    icon={
                      <MessageOutlined style={{ fontSize: 15 }} key="chat" />
                    }
                  />,
                  <Button
                    key="info"
                    size="small"
                    type="link"
                    onClick={() => {
                      setElementDetails({
                        label: member.name,
                        id: member?.member_id,
                        familyId: member?.family_id,
                      });
                      setOpenDrawerInfo(true);
                    }}
                    disabled={member.member_id === currentUser.id}
                    icon={
                      <InfoCircleOutlined style={{ fontSize: 16 }} key="info" />
                    }
                  />,
                ]}
              >
                <Card.Meta
                  avatar={
                    <DisplayAvatar
                      size={35}
                      name={member.name}
                      urlImg={member.image}
                    />
                  }
                  title={
                    <div className="space-y-0">
                      <p className="truncate">
                        {member.member_id !== currentUser.id
                          ? member.name
                          : t("voip.me")}
                      </p>
                      <span className="text-xs font-normal text-slate-500">
                        {member.extension}
                      </span>
                    </div>
                  }
                />
              </Card>
            ))}
          </Space>
        </div>
      );
    } else if (contactInfo?.id && contactInfo?.family_id) {
      return <MemoizedViewSphere elementId={contactInfo.id} from="directory" />;
    } else return null;
  }
);

export default memo(DisplayDirectoryInfo);

export const copyIcon = (text) => (
  <Typography.Paragraph
    copyable={{
      text: `${text}`,
      icon: [
        <FiCopy
          style={{
            color: "rgb(22, 119, 255)",
            marginTop: 2,
            fontSize: 14,
          }}
        />,
      ],
    }}
  />
);

export const IconText = ({ icon, text, onClick }) => (
  <Space>
    {React.cloneElement(icon, { onClick })}
    <span onClick={onClick}>{text}</span>
  </Space>
);
