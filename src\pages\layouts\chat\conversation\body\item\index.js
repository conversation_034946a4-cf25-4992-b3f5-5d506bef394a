import { DeleteOutlined, MessageOutlined } from "@ant-design/icons";
import {
  Toolt<PERSON>,
  List,
  Divider,
  Typo<PERSON>,
  Button,
  Popconfirm,
  Skeleton,
  Space,
  Dropdown,
  Badge,
  Tag,
  Avatar,
} from "antd";
import { useTranslation } from "react-i18next";

import React, {
  memo,
  lazy,
  Suspense,
  useCallback,
  useState,
  useEffect,
} from "react";
import {
  Loader,
  RenderSimpleMessage,
  ClassNames,
  AvatarChat,
  Audio,
  RenderFile,
  PollsMessage,
  ForwardingMessage,
  SystemMessage,
  ReplyMessage,
  HeaderMessage,
  ModalAction,
} from "components/Chat";

import { useDispatch, useSelector } from "react-redux";
import RenderSeen from "./utils/RenderSeen";
import { filterErrorMessage } from "new-redux/actions/chat.actions/Input";
import mainService from "services/main.service";
import {
  resetStateOtherUser,
  setActionInReply,
  setChatSelectedParticipants,
  setNewMessageChatMemebers,
  setOpenDrawer,
  setThreadList,
} from "new-redux/actions/chat.actions";
import { toastNotification } from "components/ToastNotification";
import { motion } from "framer-motion";
import { callApi } from "new-redux/services/chat.services";
import { HiOutlinePhone } from "react-icons/hi";
import { moment_timezone } from "App";
import { lazyRetry } from "utils/lazyRetry";
import { useActionMessage } from "pages/layouts/chat/hooks/useActionMessage";
import {
  audioMessageTypes,
  isTheDay,
  simpleMessageTypes,
  imageMessageTypes,
  fileMessageTypes,
  getName,
  systemMessageTypes,
  safeText,
  getUserFromMsg,
} from "pages/layouts/chat/utils/ConversationUtils";
import { updateMessages } from "pages/layouts/chat/utils/rqUpdate";
import { useSendMessage } from "pages/layouts/chat/hooks/useSendMessage";
import { URL_ENV } from "index";
import { UUID_REGEX } from "utils/regex";
import { isGuestConnected } from "utils/role";

const { Text } = Typography;
const OptionItem = lazy(() =>
  lazyRetry(() => import("./utils/OptionItem"), "OptionItem")
);

const RichTextInput = lazy(() =>
  lazyRetry(
    () => import("../../../../../../components/tiptap_richtext/RichTextInput"),
    "RichTextInput"
  )
);
const ReactionRender = lazy(() =>
  lazyRetry(() => import("./utils/ReactionRender"), "ReactionRender")
);

/**
 * item of the chat body
 * @param {object} item
 * @param {number} index
 * index of the item
 * @param {array} data
 * data of the chat
 * @param {function} scrollToBottom
 * scroll to the bottom of the chat
 * @param {string} source
 * source of the chat
 * @param {boolean} hideAvatar
 * hide the avatar
 * @param {boolean} sameDay
 * same day
 * @param {boolean} from
 * from : reply or main message
 * @param {function} tabopenDropDown
 * open the dropdown
 *  @param {function} setTabOpenDropDown
 * set the dropdown
 * @param {boolean} display_header_icon
 * display the header  to display system message
 * @param {object} GoToComponent
 * go to the component
 * @param {boolean} isBig
 * is big  for reply
 *
 */
export const ChatBodyItem = React.forwardRef(
  (
    {
      item,
      index,
      data,
      scrollToBottom,
      source,
      hideAvatar,
      sameDay,
      from = false,
      tabopenDropDown,
      setTabOpenDropDown,
      display_header_icon = true,
      GoToComponent,
      isBig = false,
      setOpenFormCreate = () => {},
      setInfoFormCreate = () => {},
    },
    ref
  ) => {
    const { t, i18n } = useTranslation("common");

    const [selected, setSelected] = useState(null);
    const [actionType, setActionType] = useState("");
    const [openAction, setOpenAction] = useState({
      state: false,
      type: "",
      item: null,
    });

    const [updateMsg, setUpdateMsg] = useState("");
    const {
      mentionState,
      currentUser,
      errorMessages,
      searchMsgState,
      numberUnreadMsg,
      openDrawer,
    } = useSelector((state) => state.chat);
    const { user } = useSelector((state) => state.user);

    const selectedConversation = useSelector(
      (state) => state.ChatRealTime.selectedConversation
    );
    const onlineUser = useSelector((state) => state.ChatRealTime.onlineUser);

    const [clicked, setClicked] = useState(false);

    const {
      mutate: handleActionMessage,
      isSuccess: isMessageSuccess,
      isLoading,
      isError,
      data: ReturnData,
    } = useActionMessage(actionType);
    const { mutate: reSendMessage, isLoading: isLoadingNewMessage } =
      useSendMessage(actionType, item?._id);

    const dispatch = useDispatch();
    const items = [
      {
        key: "00",
        label: t("chat.action.sendMessage"),
        icon: <MessageOutlined style={{ fontSize: "16px" }} />,
        disabled: source === "reply",
        show: "1",
        onClick: () => {
          const itemFound = getUserFromMsg(item?.sender_id, item);

          dispatch(
            resetStateOtherUser({
              forced: true,
              keepDrawerOpened: false,
              item: {
                _id: item?.sender_id,
                type: "user",
              },
            })
          );
          dispatch(
            setChatSelectedParticipants({
              selectedParticipants: [
                {
                  email: itemFound?.email,

                  name: itemFound?.name,

                  description: null,
                  uuid: itemFound?.uuid,
                  image: itemFound?.image,
                  admin_id: null,
                  bot: null,
                  _id: itemFound?._id,
                  post_number: itemFound?.post_number,
                  role: itemFound?.role,
                },
                currentUser,
              ],
            })
          );
        },
      },
      {
        key: "01",
        label: t("chat.action.call"),

        icon: <HiOutlinePhone style={{ fontSize: "15px" }} />,
        show: "1",
        onClick: () => {
          dispatch(
            callApi({
              setClicked: setClicked,
              post_numberR: getUserFromMsg(item?.sender_id, item)?.post_number,
              errorText: t("toasts.errorFetchApi"),
            })
          );
        },
        disabled: !currentUser?.post_number || clicked,
      },
    ];

    const handleMouseEnter = useCallback(() => {
      if (openAction.type === "edit") return;

      if (display_header_icon) setSelected(index);
    }, [display_header_icon, index, openAction.type]);

    const toggleAction = useCallback(async (state, type, value) => {
      if (value && type === "edit") {
        setUpdateMsg(value.message);
        scrollToBottom();
        setSelected(null);
      }
      setOpenAction({
        state,
        type,
        item: value ?? null,
      });
    }, []);

    const deleteDefinitlyMessage = useCallback(() => {
      try {
        const old_message =
          errorMessages.reverse().find((err) => err.message._id !== item?._id)
            ?.message ??
          data.reverse().find((item) => !UUID_REGEX.test(item?._id));
        console.log(old_message);
        updateMessages(
          null,
          "undo_new_message",
          item?._id,
          selectedConversation?.id,
          selectedConversation?.type,
          null
        );
        if (openDrawer.type === "thread")
          dispatch(
            setActionInReply({
              type: "deleted",
              message_id: item?._id,
              sender_id: currentUser?._id,
            })
          );
        if (source !== "no_chat")
          setTimeout(() => {
            dispatch(
              setNewMessageChatMemebers({
                type: selectedConversation?.type,
                data: old_message,

                discussion_id: selectedConversation?.id,

                user: selectedConversation,
                sender: getUserFromMsg(old_message?.sender_id),
              })
            );

            errorMessages
              ?.find(
                (item) =>
                  item?.discussion_id === selectedConversation?.id &&
                  item.type_conversation === selectedConversation?.type
              )

              ?.message?.file_id?.forEach((file) => {
                if (file)
                  file
                    .toString()
                    .split(",")
                    .map(async (_id) =>
                      Promise.all([await mainService.deleteFile("CHAT", _id)])
                    );
              });
            dispatch(filterErrorMessage(item?._id));
          }, 100);
      } catch (error) {}
    }, [
      errorMessages,
      data,
      item?._id,
      selectedConversation,
      openDrawer.type,
      dispatch,
      currentUser?._id,
      source,
    ]);
    const searchReplies = useCallback(
      async (message) => {
        dispatch(setOpenDrawer({ type: "thread" }));
        if (
          message.type.includes("replay") ||
          message.parent_id ||
          (message.replies && message.replies.length > 0)
        ) {
          let mainMsg = data.find(
            (msg) =>
              msg?._id === message?.main_message?._id ||
              (msg._id === message._id && message.replies.length > 0)
          );

          if (mainMsg) {
            dispatch(
              setThreadList([
                message.replies && message.replies?.length > 0
                  ? message
                  : message.main_message,
                ...mainMsg.replies.sort(
                  (a, b) => new Date(a.created_at) - new Date(b.created_at)
                ),
              ])
            );
          } else {
            const abort = new AbortController();
            try {
              const response = await mainService.getMessage(
                message.main_message._id,

                abort.signal
              );
              dispatch(
                setThreadList([
                  message.main_message,
                  ...response.data.message.replies,
                ])
              );
            } catch (error) {
              if (error.name === "CanceledError") return;
              dispatch(setThreadList([]));
              toastNotification("error", t("toasts.errorFetchApi"), "topRight");
            }
          }
        } else {
          dispatch(setThreadList([message]));
        }
      },
      [data, dispatch, t]
    );
    useEffect(() => {
      let mount = true;
      if (actionType !== "" && !isLoading && isMessageSuccess && mount) {
        toggleAction(false, "", null);
        setActionType("");
      }
      return () => {
        mount = false;
      };
    }, [isMessageSuccess, isLoading, toggleAction, actionType]);

    const handleUpdateMsg = useCallback(
      async (e) => {
        e.preventDefault();
        let taggedPerson = [];

        if (
          !mentionState &&
          openAction.item?.message !== updateMsg &&
          updateMsg.replace(/(<([^>]+)>)/gi, "").length > 0
        ) {
          let msg = safeText(updateMsg);
          let tempContainer = document.createElement("div");

          tempContainer.innerHTML = msg;

          let mentionSpans = tempContainer.querySelectorAll(
            'span[data-type="mention"]'
          );
          if (mentionSpans && mentionSpans.length > 0) {
            mentionSpans.forEach(function (span) {
              const userId = span.getAttribute("userid");
              taggedPerson.push(userId);
            });
          }
          setActionType("update_message");
          handleActionMessage({
            message_id: openAction.item?._id,
            params: {
              msg,
              source,
              taggedPerson: [...new Set(taggedPerson)].toString(),
            },
            type_conversation: selectedConversation?.type,
            type_action: "update_message",
          });
        }
      },
      [mentionState, openAction.item, updateMsg, source]
    );
    if (!item) return null;

    const handleCancel = () => {
      toggleAction(false, "", null);
    };

    const renderMediaMessage = () => {
      if (item?.deleted_at) {
        return (
          <Text type="danger">
            <DeleteOutlined className="ml-0 mr-0.5" />
            {t("chat.delete.messaageDeleted")}
          </Text>
        );
      }
      if (!item?.deleted_at && audioMessageTypes.includes(item?.type))
        return (
          <div className="relative  flex h-auto w-full   items-center      ">
            <Suspense fallback={<Skeleton.Input block active />}>
              <Audio url={item?.voice} index={index} />
            </Suspense>
          </div>
        );
      else if (
        !item?.deleted_at &&
        (imageMessageTypes.includes(item?.type) ||
          fileMessageTypes.includes(item?.type))
      ) {
        return (
          <div
            className={`
              ${!from && item?.main_message?.type ? "response" : ""}
               relative`}
          >
            <Suspense
              fallback={
                <div className="flex items-center justify-center">
                  <Loader size={24} />
                </div>
              }
            >
              <RenderFile
                source={source}
                message={item}
                GoToComponent={GoToComponent}
              />
            </Suspense>
          </div>
        );
      }
    };

    const renderMessage = () => {
      const uniqueReactions = [
        ...new Set(
          item?.reactions ? item?.reactions.map((obj) => obj.reaction) : []
        ),
      ];

      const callMessage = [...systemMessageTypes];
      callMessage.splice(2);
      /*
       ${
                callMessage?.includes(item?.type)
                  ? ""
                  : "max-w-xl md:max-w-2xl xl:max-w-3xl 2xl:max-w-6xl"
              }
       */

      return (
        <div className="flex w-full    justify-between">
          <div className="group flex h-full   w-10/12   flex-grow  flex-col">
            {/* indicate favorite */}
            <Suspense
              fallback={<Skeleton.Input size="small" className="my-1" active />}
            >
              <HeaderMessage
                item={item}
                display_header_icon={display_header_icon}
                hideAvatar={hideAvatar}
              />
            </Suspense>
            {!item?.deleted_at && display_header_icon && (
              <Suspense
                fallback={
                  <Skeleton.Input size="small" className="my-1" active block />
                }
              >
                <ForwardingMessage item={item} />
              </Suspense>
            )}
            {!from && (
              <Suspense
                fallback={
                  <Skeleton.Input size="small" className="my-1" active block />
                }
              >
                <ReplyMessage
                  searchReplies={searchReplies}
                  item={item}
                  source={source}
                />
              </Suspense>
            )}
            {!from && systemMessageTypes.includes(item?.type) && (
              <Suspense
                fallback={
                  <Skeleton.Input size="small" className="my-1" block active />
                }
              >
                <div className="mt-1">
                  <SystemMessage item={item} />
                </div>
              </Suspense>
            )}
            {!item?.deleted_at &&
              item?.type === "poll_message" &&
              item?.poll && (
                <Suspense
                  fallback={
                    <Skeleton.Input
                      size="large"
                      className="my-1"
                      block
                      active
                    />
                  }
                >
                  <PollsMessage
                    item={item}
                    actionType={actionType}
                    setActionType={setActionType}
                    callback={handleActionMessage}
                    source={source}
                    isBig={isBig}
                    isMessageSuccess={isMessageSuccess}
                    isError={isError}
                  />
                </Suspense>
              )}

            {openAction.type === "edit" ? (
              <form onSubmit={handleUpdateMsg}>
                <Suspense fallback={<Skeleton.Input active />}>
                  <RichTextInput
                    source="chat_update"
                    content={openAction.item?.message}
                    setContent={(e) => e && setUpdateMsg(e.trim())}
                    updatewithenterKey={(e) => {
                      if (!e.shiftKey && e.which === 13) {
                        handleUpdateMsg(e);
                      }
                    }}
                  />
                </Suspense>

                <div className="mb-2 mr-2 mt-1 flex items-center justify-end space-x-2">
                  <Button onClick={handleCancel}> {t("form.cancel")} </Button>

                  <Button
                    disabled={
                      openAction.item?.message === updateMsg ||
                      updateMsg.replace(/(<([^>]+)>)/gi, "").length < 1
                    }
                    htmlType="submit"
                    type="primary"
                    loading={isLoading}
                  >
                    {t("fields_management.drawerOkBtn")}
                  </Button>
                </div>
              </form>
            ) : (
              !item?.deleted_at &&
              item?.type !== "poll_message" &&
              (simpleMessageTypes.includes(item?.type) ||
                simpleMessageTypes.includes(item?.main_message?.type)) && (
                <Suspense
                  fallback={<Skeleton.Input className="my-1" block active />}
                >
                  <RenderSimpleMessage
                    source={source}
                    from={display_header_icon}
                    message={item}
                    onItemSelect={searchReplies}
                  />
                </Suspense>
              )
            )}

            {renderMediaMessage()}
            {/* reaction render */}
            {!["thread", "search", "starred", "pinned"].includes(source) &&
              item?.reactions &&
              item?.reactions.length > 0 && (
                <Suspense
                  fallback={
                    <Space size={3}>
                      {Array.from(
                        {
                          length: uniqueReactions.length,
                        },
                        (_, i) => uniqueReactions[i]
                      ).map((item) => {
                        return (
                          <Skeleton.Button
                            key={`skeletonKey_${item}`}
                            active
                            size="small"
                            shape="circle"
                            block
                          />
                        );
                      })}
                    </Space>
                  }
                >
                  <ReactionRender
                    source={source}
                    handleActionMessage={handleActionMessage}
                    setActionType={setActionType}
                    item={item}
                  />
                </Suspense>
              )}
            {/* control message erroné */}
            {item?.unread === "error" && (
              <div className="flex items-center justify-start">
                <Button
                  type="link"
                  className="ml-0 p-0 text-left "
                  loading={
                    isLoadingNewMessage && actionType.includes("re-new_message")
                  }
                  onClick={() => {
                    setActionType(
                      source === "no_chat"
                        ? "re-new_message_no_chat"
                        : "re-new_message"
                    );

                    reSendMessage({
                      params: {
                        selectedConversation,
                        message: item?.message?.trim() ?? "",
                        file: item?.file ?? [],
                        taggedPerson: item?.tags ?? [],
                        from: item?.main_message ? true : from,
                        main_message: item?.main_message,
                      },
                      type_conversation: selectedConversation?.type,
                      type_action:
                        source === "no_chat"
                          ? "re-new_message_no_chat"
                          : "re-new_message",
                    });
                    dispatch(filterErrorMessage(item?._id));
                  }}
                >
                  <Text
                    type="danger"
                    className=" whitespace-normal text-xs transition duration-75 hover:underline "
                  >
                    {t("chat.error_message.message_send")}{" "}
                    <span className=" underline">
                      {t("chat.error_message.re-send_action")}
                    </span>
                  </Text>
                </Button>
                <Tooltip title={t("chat.action.delete")} placement="right">
                  <Popconfirm
                    placement="top"
                    title={t("chat.delete.delete_error_sent_message_title")}
                    description={t(
                      "chat.delete.delete_error_sent_message_description"
                    )}
                    icon={
                      <DeleteOutlined
                        style={{
                          color: "red",
                        }}
                      />
                    }
                    onConfirm={deleteDefinitlyMessage}
                    okText={t("chat.action.delete")}
                    okButtonProps={{
                      danger: true,
                    }}
                    cancelText={t("form.cancel")}
                  >
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined className="  text-base" />}
                    />{" "}
                  </Popconfirm>
                </Tooltip>
              </div>
            )}
          </div>
          {/* seen message */}

          <div className="flex w-1/12  items-end justify-end">
            {(["main", "no_chat"].includes(source) || item?.unread === 2) && (
              <RenderSeen
                item={item}
                index_item={index}
                data={data}
                source={source}
              />
            )}
          </div>
        </div>
      );
    };

    return (
      <div className={`h-full w-full `} ref={ref ? ref : null}>
        {/* condition to render 
        the seperate divider of date  in the screen */}

        {index === data.length - 1 && (
          <Divider plain className="uppercase">
            {isTheDay(
              new Date(item?.created_at?.split("T")[0]),
              "T",
              new Date()
            ) ||
            isTheDay(new Date(item?.created_at?.split("T")[0]), "Y", new Date())
              ? moment_timezone(item?.created_at).calendar(null, {
                  lastDay: () =>
                    i18n.language === "fr" ? "[Hier]" : "[Yesterday]",
                  sameDay: () =>
                    i18n.language === "fr" ? "[Aujourd'hui]" : "[Today]",
                })
              : moment_timezone(item?.created_at).format("ll")}
          </Divider>
        )}

        {index !== data.length - 1 && !sameDay ? (
          <Divider plain className="uppercase">
            {isTheDay(
              new Date(item?.created_at?.split("T")[0]),
              "T",
              new Date()
            ) ||
            isTheDay(new Date(item?.created_at?.split("T")[0]), "Y", new Date())
              ? moment_timezone(item?.created_at).calendar(null, {
                  lastDay: () =>
                    i18n.language === "fr" ? "[Hier]" : "[Yesterday]",
                  sameDay: () =>
                    i18n.language === "fr" ? "[Aujourd'hui]" : "[Today]",
                })
              : moment_timezone(item?.created_at).format("ll")}
          </Divider>
        ) : null}

        {source === "main" &&
          numberUnreadMsg?.number > 0 &&
          index === numberUnreadMsg?.number - 1 &&
          selectedConversation?.id === numberUnreadMsg?.discussion_id && (
            <Divider plain className="uppercase">
              {" "}
              <span className=" text-blue-400 ">
                {t("chat.message_system.newMessage")}
              </span>
            </Divider>
          )}
        {/* end the seperate divider of date  in the screen */}

        {/* list item */}
        <List.Item
          onMouseOver={handleMouseEnter}
          onMouseLeave={() => setSelected(null)}
          key={item?._id}
          className={ClassNames(
            GoToComponent ? "" : "mx-2",
            "group relative transition duration-100",
            !item?.deleted_at &&
              display_header_icon &&
              (searchMsgState.virtual === item?._id
                ? "bg-yellow-400/50"
                : (item?.favoris?.length > 0 &&
                    item?.favoris?.includes(currentUser?._id)) ||
                  item?.important !== ""
                ? " bg-yellow-400/20 "
                : "")
          )}
          // style={{
          //   paddingLeft:
          //     from && isReply && item?.type?.includes("replay") ? "45px" : "",
          // }}
        >
          {/* content message */}

          <List.Item.Meta
            avatar={
              <>
                {(index !== data?.length - 1 && hideAvatar) ||
                systemMessageTypes.includes(item?.type) ? (
                  <>
                    <div className="time invisible ml-2  flex max-w-[27px] flex-col whitespace-normal pt-1.5   group-hover:visible ">
                      <Tooltip
                        title={
                          <div className="flex flex-col items-center">
                            <time>
                              {moment_timezone(item?.created_at).format("lll")}
                            </time>{" "}
                            (
                            {item?.mobile === 1
                              ? t("chat.action.deviceMobile")
                              : t("chat.action.deviceDesktop")}
                            )
                          </div>
                        }
                      >
                        {moment_timezone(item?.created_at).format("LT")}
                      </Tooltip>
                    </div>
                  </>
                ) : (
                  <Dropdown
                    open={
                      isGuestConnected(currentUser?.role, user?.role)
                        ? false
                        : undefined
                    }
                    disabled={
                      item?.type === "message_from_bot" ||
                      (source !== "main" && source !== "reply") ||
                      selectedConversation?.type === "user" ||
                      item?.sender_id === currentUser?._id
                    }
                    menu={{
                      items,
                    }}
                    arrow={{
                      pointAtCenter: true,
                    }}
                    className={`${
                      item?.type === "message_from_bot" ||
                      (source !== "main" && source !== "reply") ||
                      selectedConversation?.type === "user" ||
                      item?.sender_id === currentUser?._id
                        ? "cursor-auto"
                        : "cursor-pointer"
                    }`}
                    trigger={["click"]}
                  >
                    <span onClick={(e) => e.preventDefault()}>
                      <Badge
                        dot={item?.type !== "message_from_bot"}
                        color={
                          onlineUser[
                            item?.sender?.uuid ?? item?.sender_uuid
                          ] === "away"
                            ? "orange"
                            : onlineUser[
                                item?.sender?.uuid ?? item?.sender_uuid
                              ] === "busy"
                            ? "red"
                            : onlineUser[
                                item?.sender?.uuid ?? item?.sender_uuid
                              ] === "online"
                            ? "green"
                            : "#a6a6a6"
                        }
                        offset={[-2, 28]}
                      >
                        <motion.div
                          whileTap={{
                            scale:
                              item?.type === "message_from_bot" ||
                              (source !== "main" && source !== "reply") ||
                              selectedConversation?.type === "user" ||
                              item?.sender_id === currentUser?._id
                                ? 1
                                : 0.6,
                          }}
                        >
                          <AvatarChat
                            fontSize="0.875rem"
                            type={
                              item?.type === "message_from_bot" ? "bot" : "user"
                            }
                            hasImage={
                              item?.type === "message_from_bot"
                                ? item?.bot?.logo
                                : item?.sender_id === currentUser?._id
                                ? user?.avatar
                                : getUserFromMsg(item?.sender_id, item)?.image
                            }
                            height={9}
                            width={9}
                            size={36}
                            url={
                              item?.type === "message_from_bot"
                                ? URL_ENV?.REACT_APP_OAUTH_CHAT_API +
                                  process.env
                                    .REACT_APP_SUFFIX_CHAT_GROUP_STORAGE +
                                  item?.bot?.logo
                                : URL_ENV?.REACT_APP_BASE_URL +
                                  URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                                  (item?.sender_id === currentUser?._id
                                    ? user?.avatar
                                    : getUserFromMsg(item?.sender_id, item)
                                        ?.image)
                            }
                            name={getName(
                              item.type === "message_from_bot"
                                ? item?.bot?.name
                                : item?.sender_id === currentUser?._id
                                ? user?.label
                                : getUserFromMsg(item?.sender_id, item)?.name,
                              "avatar"
                            )}
                          />
                        </motion.div>
                      </Badge>
                    </span>
                  </Dropdown>
                )}
              </>
            }
            title={
              (index !== data?.length - 1 && hideAvatar) ||
              systemMessageTypes.includes(item?.type) ? null : (
                <div className="flex items-center space-x-2">
                  <span
                    className={`
                  
                   whitespace-nowrap  first-letter:capitalize
                  ${
                    item?.type === "message_from_bot"
                      ? "font-semibold text-red-700"
                      : " "
                  }
                  `}
                  >
                    {getName(
                      item?.type === "message_from_bot"
                        ? item?.bot?.name
                        : item.sender_id === currentUser?._id
                        ? user?.label
                        : getUserFromMsg(item?.sender_id, item)?.name,
                      "name"
                    )}
                  </span>

                  <Tooltip
                    title={
                      <div className="flex flex-col items-center">
                        <span>
                          {moment_timezone(item?.created_at).format("lll")}
                        </span>{" "}
                        (
                        {item?.mobile === 1
                          ? t("chat.action.deviceMobile")
                          : t("chat.action.deviceDesktop")}
                        )
                      </div>
                    }
                  >
                    <span className="time text-xs">
                      {moment_timezone(item?.created_at).format("LT")}
                    </span>
                  </Tooltip>

                  <HeaderMessage
                    item={item}
                    display_header_icon={display_header_icon}
                    hideAvatar={true}
                  />
                </div>
              )
            }
            description={renderMessage()}
          />
          {((tabopenDropDown.state && item?._id === tabopenDropDown.selected) ||
            (!tabopenDropDown.state && selected === index)) &&
            !item?.deleted_at &&
            !systemMessageTypes.includes(item?.type) &&
            item?.unread !== "error" &&
            item?.type !== "poll_message" &&
            item?.unread !== 2 && (
              <Suspense fallback={<Loader size={20} />}>
                <OptionItem
                  source={source}
                  searchReplies={searchReplies}
                  handleActionMessage={handleActionMessage}
                  item={item}
                  loading={isLoading}
                  data={ReturnData}
                  setActionType={setActionType}
                  toggleAction={toggleAction}
                  setTabOpenDropDown={setTabOpenDropDown}
                  tabopenDropDown={tabopenDropDown}
                  setOpenFormCreate={setOpenFormCreate}
                  setInfoFormCreate={setInfoFormCreate}
                />
              </Suspense>
            )}

          {GoToComponent && <div className="self-start">{GoToComponent}</div>}
        </List.Item>

        {openAction.state && openAction.type !== "edit" && (
          <Suspense
            fallback={
              <div className="fixed inset-0 z-10 flex h-full w-full   items-center justify-center bg-black/5 ">
                <Loader size="2rem" />
              </div>
            }
          >
            <ModalAction
              openAction={openAction}
              item={item}
              source={source}
              toggleAction={toggleAction}
            />
          </Suspense>
        )}
      </div>
    );
  }
);

export default memo(ChatBodyItem);
