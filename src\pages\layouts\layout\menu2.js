import {
  <PERSON><PERSON><PERSON><PERSON>utlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  ColumnHeightOutlined,
  UnorderedListOutlined,
  FolderViewOutlined,
  BankOutlined,
  ContainerOutlined,
  FileSearchOutlined,
  StopOutlined,
  EditOutlined,
  <PERSON>olderOpenOutlined,
  InfoCircleOutlined,
  <PERSON><PERSON><PERSON>cleOutlined,
  DashboardOutlined,
} from "@ant-design/icons";
import {
  FiActivity,
  FiFolderPlus,
  FiGlobe,
  FiMail,
  FiTag,
  FiTool,
  FiUsers,
  FiInfo,
  FiBell,
  FiKey,
} from "react-icons/fi";
import { HiOutlineArrowDownTray } from "react-icons/hi2";
import { AiOutlineShoppingCart } from "react-icons/ai";

import {
  MdLabelImportantOutline,
  MdOutlinePolicy,
  MdSecurity,
} from "react-icons/md";
import { <PERSON><PERSON>, <PERSON><PERSON>, Divider, <PERSON>u, <PERSON>, Tooltip } from "antd";
import { useEffect, useLayoutEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link, NavLink, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { setImportKey } from "../../../new-redux/actions/form.actions/form";
import { isGuestConnected, roles } from "../../../utils/role";
import { RiMailSettingsLine } from "react-icons/ri";
import { LiaFileSignatureSolid } from "react-icons/lia";

import {
  getMailStats,
  setAccountData,
  setFetchedAccount,
  setLoadingAccount,
  setOpenEditor,
  setOpenModalEmail,
  setPage,
  setPageSize,
  setSearchEmail,
  setSearchPage,
  setSelectedAccount,
} from "../../../new-redux/actions/mail.actions";
import MainService from "services/main.service";
import { AvatarChat } from "components/Chat";
import { getName } from "../chat/utils/ConversationUtils";
import { KEYWORD_MAILING_REGEX } from "utils/regex";
import { SET_USER_INFOS } from "new-redux/constants";
import { GrStorage } from "react-icons/gr";
import {
  File,
  HeartHandshake,
  Inbox,
  SendHorizontal,
  Star,
  Unplug,
} from "lucide-react";
import LabelsMail from "pages/rmc/mailing/components/LabelsMail";
import { useWindowSize } from "pages/clients&users/components/WindowSize";
import TicketIconSphere from "components/icons/TicketIconSphere";
import LiveChatIcon from "components/icons/LiveChatIcon";
import { BiArchiveIn } from "react-icons/bi";
import { LuTrash2 } from "react-icons/lu";
import { PiWarningOctagonBold } from "react-icons/pi";

function getItem(label, key, icon, children, type, disabled) {
  return {
    key,
    icon,
    children,
    label,
    type,
    disabled,
  };
}
const Menu2 = ({ collapsed }) => {
  const location = useLocation();
  const dispatch = useDispatch();
  const windowSize = useWindowSize();
  const { families } = useSelector((state) => state.families);
  const [items, setItems] = useState([]);
  const [loadingMailSideBar, setLoadingMailSideBar] = useState(false);

  const [t] = useTranslation("common");
  const { user } = useSelector((state) => state.user);
  const currentUser = useSelector((state) => state?.chat?.currentUser);

  const { fetchedAccount, statsMail, dataAccounts } = useSelector(
    (state) => state.mailReducer
  );

  const { pathname } = useLocation();

  const usedAccount = useMemo(
    () => dataAccounts?.find((item) => item.selected == 1),
    [dataAccounts]
  );

  const displayNameMail = (name = "", path, count = 0) => (
    <div className="flex flex-1 items-center justify-between">
      <span>{name} </span>

      {(path === "inbox" || path === "spam") && (
        <p
          className={
            pathname.split("/")[3] === path ? "font-bold" : "font-normal"
          }
        >
          {count}
        </p>
      )}

      {/* <Badge
        overflowCount={count}
        size="default"
        style={{
          transition: "all 0.3s ease-in-out",
          backgroundColor:
            pathname.split("/")[3] === path ? "#1C4ED8" : " transparent",
          color: pathname.split("/")[3] === path ? "#EEF6FF" : "#6b7280",
          border:
            pathname.split("/")[3] === path ? "1px solid #6b7280" : "none",
          borderRadius: "-1%",
          padding: "0px 10px",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          boxShadow: "none",
          fontSize: "12px",
          fontWeight: "400",
        }}
        count={count}
      /> */}
    </div>
  );
  const accountId = pathname.split("/")[2] ?? 0;

  const handleClickOnEmailItem = () => {
    dispatch(setPage(1));
    dispatch(setSearchPage(1));
    dispatch(setPageSize(20));
    dispatch(setSearchEmail(""));
  };

  const MailingTypes = useMemo(
    () => [
      {
        key: "inbox",
        name: displayNameMail(
          t("mailing.inbox"),
          "inbox",
          statsMail?.[accountId]?.inbox > 999
            ? `+${999}`
            : statsMail?.[accountId]?.inbox ?? 0
        ),
        link: `inbox`,
        icon: <Inbox size={16} />,
        onClick: handleClickOnEmailItem,
      },
      {
        key: "sent",

        name: displayNameMail(t("mailing.sent"), "sent", 0),

        link: `sent`,
        icon: <SendHorizontal size={16} />,
        onClick: handleClickOnEmailItem,
      },
      {
        key: "drafts",
        name: displayNameMail(t("mailing.draft"), "drafts", 0),
        disabled: true,
        link: `drafts`,
        icon: <File size={15} />,
        onClick: handleClickOnEmailItem,
      },
      {
        key: "starred",
        name: displayNameMail(t("mailing.starred"), "starred", 0),

        link: `starred`,
        icon: <Star size={16} />,
        onClick: handleClickOnEmailItem,
      },
      {
        key: "important",
        name: displayNameMail("Important", "important", 0),

        link: `important`,
        icon: <MdLabelImportantOutline className="h-[18px] w-[18px]" />,
        onClick: handleClickOnEmailItem,
      },
      {
        key: "archive",
        name: displayNameMail("Archive", "archive", 0),

        link: `archive`,
        icon: <BiArchiveIn className="h-4 w-4" />,
        onClick: handleClickOnEmailItem,
      },
      {
        key: "spam",
        name: displayNameMail(
          t("mailing.spam"),
          "spam",
          statsMail?.[accountId]?.spam > 999
            ? `+${999}`
            : statsMail?.[accountId]?.spam ?? 0
        ),

        link: `spam`,
        icon: <PiWarningOctagonBold className="h-4 w-4" />,
        onClick: handleClickOnEmailItem,
      },
      {
        key: "trash",

        name: displayNameMail(t("mailing.trash"), "trash", 0),

        link: `trash`,
        icon: <LuTrash2 className="h-4 w-4" />,
        onClick: handleClickOnEmailItem,
      },
    ],
    [
      t,
      statsMail?.[accountId]?.inbox,
      statsMail?.[accountId]?.spam,
      loadingMailSideBar,
      accountId,
      dispatch,
      displayNameMail,
    ]
  );

  useEffect(() => {
    let isMounted = true;
    const getAccounts = async () => {
      dispatch(setLoadingAccount(true));
      try {
        const response = await MainService.getAccounts();
        if (!isMounted) return;

        if (response?.status === 200) {
          let notifs = await MainService.getNotifcationMailingByUser();

          let data = response?.data.map((item) => ({
            label: item.email,
            value: item.id,
            primary: item.primary_account,
            shared: item.shared,
            sync: item.sync,
            creator: item.creator,
            selected: !!item.selected,
            departmentId: item.departement_id,
            dispatcheur: item.dispatcheur,
            alias: item.alias ?? [],
            labels: item.labels,
            default_signature: item.default_signature,
            notification_status: notifs?.data?.find(
              (el) => el.account_id === item.id
            )?.notification_status,
          }));
          if (isMounted) {
            // Add this check
            dispatch(setAccountData(data));
          }
        }
      } catch (err) {
        if (isMounted) {
          // Add this check
          dispatch(setLoadingAccount(false));
          dispatch(setFetchedAccount(false));
          console.log(err);
        }
      }
      // }, [pathname, dispatch]);
    };
    if (!pathname.includes("mailing") || fetchedAccount) return;
    getAccounts();
    return () => {
      isMounted = false;
    };
  }, [fetchedAccount, pathname, dispatch]);

  useLayoutEffect(() => {
    if (!pathname.includes("mailing")) return;
    dispatch(getMailStats());

    return () => {
      dispatch(setFetchedAccount(false));
      dispatch(setPage(1));
    };
  }, [dispatch]);

  // add loading in case usedAccount is changed
  useEffect(() => {
    let isMounted = true;
    const url = new URL(window.location.href);

    if (!url.pathname.includes("mailing")) return;

    setLoadingMailSideBar(true);
    const time = setTimeout(() => {
      if (isMounted) {
        setLoadingMailSideBar(false);
      }
    }, 1000);

    return () => {
      clearTimeout(time);
      isMounted = false;
    };
  }, [accountId]);

  useEffect(() => {
    switch (true) {
      case location.pathname.includes("/settings"):
      case location.pathname.includes("/settings/general/departments"):
      case location.pathname.includes("/settings/general/services"):
      case location.pathname.includes("/settings/general/companies"):
      case location.pathname.includes("/settings/general/channels"):
      case location.pathname.includes("/settings/general/guestQueue"):
      case location.pathname.includes("/settings/fields"):
      case /\/settings\/fields\/.*/.test(location.pathname):
      case location.pathname.includes("/settings/rmc"):
      case location.pathname.includes("/settings/rmc/inboxSettings"):
      case location.pathname.includes("/settings/rmc/outboxSettings"):
      case location.pathname.includes("/settings/tags"):
      case location.pathname.includes("/settings/emailAccounts"):
      case location.pathname.includes("/settings/user-view"):
      case location.pathname.includes("/settings/helpDesk/folders"):
      case location.pathname.includes("/settings/helpDesk/departments"):
      case location.pathname.includes("/settings/helpDesk/services"):
      case location.pathname.includes("/settings/helpDesk/SLA"):
      case location.pathname.includes("/settings/helpDesk/Levels"):
      case location.pathname.includes("/settings/helpDesk/Subjects"):
      case location.pathname.includes("/settings/helpDesk/folder"):
      case location.pathname.includes("/settings/helpDesk/severities"):
      case location.pathname.includes("/settings/pipeline"):
      case location.pathname.includes("/settings/folder"):
      case /\/settings\/pipeline\/.*/.test(location.pathname):
      case location.pathname.includes("/settings/users"):
      case location.pathname.includes("/settings/guests"):
      case location.pathname.includes("/settings/users/userDetails"):
      case location.pathname.includes("/settings/users/team"):
      case location.pathname.includes("/settings/users/role"):
      case location.pathname.includes("/settings/product"):
      case location.pathname.includes("/settings/wiki"):
      case location.pathname.includes("/settings/integrations"):
      case location.pathname.includes("/settings/products/family"):
      case location.pathname.includes("/settings/channels"):
      case /\/settings\/wiki\/.*/.test(location.pathname):
      case location.pathname.includes("/settings/sales"):
      case location.pathname.includes("/settings/import"):
      case location.pathname.includes("/settings/checklist"):
      case location.pathname.includes("/settings/triggers"):
      case location.pathname.includes("settings/activity/types"):
      case location.pathname.includes("/settings/log-action"):
      case location.pathname.includes("/settings/tour"):
      case location.pathname.includes("/settings/unavailability"):
      case location.pathname.includes("/settings/contacts-types"):
      case location.pathname.includes("/settings/products"):
        setItems([
          roles.includes(user?.role) &&
            getItem(
              <NavLink to="/settings/general/companies">
                {t("menu2.general")}
              </NavLink>,
              "general",
              <FiTool style={{ fontSize: "18px" }} />
            ),
          roles?.includes(user?.role) &&
            getItem(
              <NavLink to="/settings/pipeline/Organisation">
                {t("menu2.pipeline")}
              </NavLink>,
              "pipeline",
              <SisternodeOutlined style={{ fontSize: "18px" }} />
            ),
          roles?.includes(user?.role) &&
            getItem(
              <NavLink to="/settings/fields/User">{t("menu2.fields")}</NavLink>,
              "fields",
              <FiFolderPlus style={{ fontSize: "18px" }} />
            ),
          getItem(
            <NavLink to="/settings/tags">{t("menu2.tags")}</NavLink>,
            "tags",
            <FiTag style={{ fontSize: "18px" }} />
          ),
          getItem(
            <NavLink to="/settings/activity/types">{t("menu1.tasks")}</NavLink>,
            "activity",
            <FiActivity style={{ fontSize: "18px" }} />
          ),
          user &&
            user?.access &&
            user?.access["email"] === "1" &&
            getItem(
              <NavLink
                onClick={() => {
                  dispatch(setFetchedAccount(false));
                }}
                to="/settings/emailAccounts"
              >
                {t("menu2.emailaccounts")}
              </NavLink>,
              "emailAccounts",
              <FiMail style={{ fontSize: "18px" }} />
            ),
          user &&
          roles.includes(user?.role) &&
          user?.access &&
          user?.access["rmc"] === "1"
            ? getItem(
                <NavLink to="/settings/rmc">{t("menu2.rmcSettings")}</NavLink>,
                "rmc",
                <FiTool style={{ fontSize: "18px" }} />
              )
            : "",
          // user &&
          //   user?.access &&
          //   user?.access["email"] === 1 &&
          getItem(
            <NavLink to="/settings/emailTemplates">
              {t("menu2.emailTemplates")}
            </NavLink>,
            "emailTemplates",
            <RiMailSettingsLine style={{ fontSize: "18px" }} />
          ),
          roles?.includes(user?.role) &&
            getItem(
              <NavLink to="/settings/folders">{t("menu2.folders")}</NavLink>,
              "folders",
              <FolderOpenOutlined style={{ fontSize: "18px" }} />
            ),
          process.env.REACT_APP_BRANCH !== "prod" &&
            getItem(
              <NavLink to="/settings/triggers">{t("menu2.triggers")}</NavLink>,
              "Triggers",
              <FolderViewOutlined style={{ fontSize: "18px" }} />
            ),
          getItem(
            <NavLink to="/settings/contacts-types">
              {t("menu2.typesContacts")}
            </NavLink>,
            "contacts-types",
            <ContainerOutlined style={{ fontSize: "18px" }} />
          ),

          roles?.includes(user?.role) &&
            getItem(
              <NavLink to="/settings/users">
                {t("menu2.usersWithoutFields")}
              </NavLink>,
              "users",
              <FiUsers style={{ fontSize: "18px" }} />
            ),
          roles.includes(user?.role) &&
            getItem(
              t("menu2.products"),
              "products",
              <NavLink to="/settings/products">
                <AiOutlineShoppingCart style={{ fontSize: "21px" }} />
              </NavLink>
            ),
          roles.includes(user?.role) &&
            getItem(
              <NavLink to="/settings/sales">{t("menu1.deals")}</NavLink>,
              "sales",
              <HeartHandshake size={19} />
            ),
          roles.includes(user?.role) &&
            user &&
            user?.access &&
            user?.access["ticket"] === "1" &&
            getItem(
              <NavLink to="/settings/helpDesk/severities">
                {t("menu2.ticket")}
              </NavLink>,
              "helpDesk",

              <TicketIconSphere size={20} />
            ),

          getItem(
            <NavLink
              to="/settings/import"
              onClick={() => {
                dispatch(setImportKey({ importKey: new Date().getTime() }));
              }}
            >
              {t("menu1.import")}
            </NavLink>,
            "import",
            <HiOutlineArrowDownTray style={{ fontSize: "18px" }} />
          ),

          getItem(
            <NavLink to="/settings/wiki/docs">{t("Wiki")}</NavLink>,
            "wiki",
            <FiGlobe style={{ fontSize: "18px" }} />
          ),
          roles.includes(user?.role) &&
            getItem(
              <NavLink to="/settings/integrations">
                {t("integrations.integrations")}
              </NavLink>,
              "integrations",
              <Unplug size={18} />
            ),
          // user &&
          //   user?.access &&
          //   user?.access["rmc"] === "1" &&
          getItem(
            <NavLink to="/settings/liveChat">Live chat</NavLink>,
            "liveChat",
            <LiveChatIcon />
          ),

          getItem(
            <NavLink to="/settings/log-action">
              {t("menu2.log-action")}
            </NavLink>,
            "log-action",
            <FileSearchOutlined style={{ fontSize: "18px" }} />
          ),
          ...(process.env.REACT_APP_BRANCH.includes("dev")
            ? [
                getItem(
                  <NavLink to="/settings/checklist">
                    {t("menu2.checklist")}
                  </NavLink>,
                  "checklist",
                  <UnorderedListOutlined style={{ fontSize: "18px" }} />
                ),
                getItem(
                  <NavLink to="/settings/tour">{t("menu2.tour")}</NavLink>,
                  "Tour",
                  <InfoCircleOutlined style={{ fontSize: "18px" }} />
                ),

                getItem(
                  <NavLink to="/settings/unavailability">
                    {t("menu2.unavailability")}
                  </NavLink>,
                  "unavailability",
                  <StopOutlined style={{ fontSize: "18px" }} />
                ),
              ]
            : []),
        ]);
        break;

      case location.pathname.includes("/stats"):
        setItems([
          process.env.REACT_APP_BRANCH !== "prod" &&
            getItem(
              <NavLink to="/stats/general">{t("menu2.general")}</NavLink>,
              "general",
              <AreaChartOutlined className="h-5 w-5" />
            ),
          user &&
            user?.access &&
            user?.access["rmc"] === "1" &&
            getItem(
              <NavLink to="/stats/rmc">{t("menu2.statsRmc")}</NavLink>,
              "rmc",
              <AreaChartOutlined className="h-5 w-5" />
            ),
          getItem(
            <NavLink to="/stats/families">{t("menu1.stats")}</NavLink>,
            "families",
            <DashboardOutlined className="h-5 w-5" />
          ),
        ]);
        break;

      case /\/mailing/.test(location.pathname):
        setItems([
          ...MailingTypes.map((item, index) => {
            return getItem(
              <NavLink
                to={
                  item?.disabled
                    ? false
                    : `/mailing/${usedAccount?.value}/` + item.link
                }
                key={index}
                onClick={item.onClick}
                disabled={item?.disabled}
              >
                {item.name}
              </NavLink>,
              item.key,
              item.icon,
              null,
              null,
              item?.disabled
            );
          }),
        ]);
        break;

      case location.pathname.includes("/profile/general"):
      case location.pathname.includes("/profile/security"):
      case location.pathname.includes("/profile/localization"):
      case location.pathname.includes("/profile/signature"):
      case location.pathname.includes("/profile/accessToken"):
      case location.pathname.includes("/profile/notification"):
      case location.pathname.includes("/profile/infoTenant"):
      case location.pathname.includes("/profile/allNotifications"):
      case location.pathname.includes("/profile/policies"):
        setItems([
          getItem(
            <NavLink to="/profile/general">{t("menu2.general_info")}</NavLink>,
            "general",
            <FiInfo className="h-5 w-5" />
          ),
          getItem(
            <NavLink to="/profile/security">{t("menu2.security")}</NavLink>,
            "security",
            <MdSecurity className="h-5 w-5" />
          ),

          getItem(
            <NavLink to="/profile/localization">
              {t("menu2.localization")}
            </NavLink>,
            "localization",
            <FiGlobe className="h-5 w-5" />
          ),
          !isGuestConnected(currentUser?.role, user?.role) &&
            getItem(
              <NavLink to="/profile/signature">{"Signature email"}</NavLink>,
              "signature",
              <LiaFileSignatureSolid className="h-5 w-5" />
            ),
          // process.env.REACT_APP_BRANCH !== "prod" &&
          getItem(
            <NavLink to="/profile/notification">
              {t("menu1.notifications")}
            </NavLink>,
            "notification",
            <FiBell className="h-5 w-5" />
          ),
          !isGuestConnected(currentUser?.role, user?.role) &&
            getItem(
              <NavLink to="/profile/accessToken">
                {t("profilemenu.accessToken")}
              </NavLink>,
              "accessToken",
              <FiKey className="h-5 w-5" />
            ),
          !isGuestConnected(currentUser?.role, user?.role) &&
            getItem(
              <NavLink to="/profile/infoTenant">
                {t("profilemenu.infoTenant")}
              </NavLink>,
              "infoTenant",
              <GrStorage className="h-5 w-5" />
            ),
          getItem(
            <NavLink to="/profile/allNotifications">
              {t("menu2.allNotifications")}
            </NavLink>,
            "logs",
            <ClockCircleOutlined
              style={{
                fontSize: "21px",
              }}
            />
          ),
          getItem(
            <NavLink to="/profile/policies">{t("menu2.policies")}</NavLink>,
            "policies",
            <MdOutlinePolicy
              style={{
                fontSize: "21px",
              }}
            />
          ),
        ]);
        break;

      case location.pathname.includes("/private/projet-1"):
        setItems([
          getItem(
            <NavLink to="/private/projet-1">Projet 1</NavLink>,
            "project",
            <BankOutlined style={{ fontSize: "18px" }} />
          ),

          getItem(
            <NavLink to="">Projet 2</NavLink>,
            "type",
            <FieldStringOutlined style={{ fontSize: "18px" }} />
          ),
          getItem(
            <NavLink to="">Projet 3</NavLink>,
            "family",
            <AppstoreOutlined style={{ fontSize: "18px" }} />
          ),
          getItem(
            <NavLink to="">Projet 4</NavLink>,
            "unity",
            <ColumnHeightOutlined style={{ fontSize: "18px" }} />
          ),
        ]);
        break;

      default:
        setItems([]);
        break;
    }
  }, [
    location.pathname,
    statsMail?.[accountId]?.inbox,
    statsMail?.[accountId]?.spam,
    families,
    t,
    collapsed,
    user,
  ]);

  const updateSelectedEmail = async (id) => {
    var formData = new FormData();
    formData.append("accountId", id);
    try {
      dispatch(setSelectedAccount(id));
      dispatch(setSearchEmail(""));
      const response = await MainService.updateSelectedEmail(formData);
      if (response.status === 200) {
        const accounts_email = user?.accounts_email?.map((item) =>
          item.id === id ? { ...item, primary_account: 1 } : item
        );
        dispatch({
          type: SET_USER_INFOS,
          payload: {
            ...user,
            accounts_email: accounts_email,
          },
        });
      }
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <div className="flex w-full flex-col ">
      {/\/mailing/?.test(location?.pathname) ? (
        <div className="mx-2">
          {collapsed ? (
            <Tooltip title={t("mailing.newMsg")} placement="right">
              <Button
                block
                type="primary"
                onClick={() => {
                  dispatch(setOpenModalEmail(true));

                  dispatch(setOpenEditor({ state: false, type: "" }));
                }}
                icon={<EditOutlined style={{ color: "#fff" }} />}
              />
            </Tooltip>
          ) : (
            <Button
              block
              type="primary"
              onClick={() => {
                dispatch(setOpenModalEmail(true));
                dispatch(setOpenEditor({ state: false, type: "" }));
              }}
              icon={<EditOutlined />}
            >
              <span>{t("mailing.newMsg")}</span>
            </Button>
          )}
        </div>
      ) : null}
      <div
        className="mt-2 overflow-y-auto"
        style={{ maxHeight: windowSize.height - 150 }}
      >
        <Spin spinning={loadingMailSideBar} size="small">
          <Menu
            selectedKeys={[
              location.pathname.split("/")[1] === "mailing"
                ? location.pathname.match(KEYWORD_MAILING_REGEX)?.[1] ||
                  location.pathname.split("/")[
                    location.pathname.split("/").length - 1
                  ]
                : location.pathname.split("/")[1] === "stats" ||
                  location.pathname.split("/")[1] === "directory"
                ? location.pathname.split("/")[
                    location.pathname.split("/").length - 1
                  ]
                : location.pathname.split("/")[2],
            ]}
            mode="inline"
            items={items}
          />
        </Spin>
        {dataAccounts.length > 1 &&
          location.pathname.split("/")[1] === "mailing" && (
            <div className="w-full  p-2">
              <Divider className="my-2.5 " />
              <p className=" truncate p-2 text-sm text-slate-400 ">
                {t("mailing.Inbox.other")} 11
              </p>

              <div className="  flex w-[95%] flex-col gap-y-2  p-1.5 ">
                {dataAccounts
                  .filter((item) => item.selected !== true)
                  ?.map((item) => (
                    <Link
                      key={item.value}
                      onClick={() => {
                        updateSelectedEmail(item.value);
                        dispatch(setPage(1));
                        dispatch(setSearchEmail(""));
                      }}
                      to={`/mailing/${item.value}/inbox`}
                      className={`flex items-center
           ${item.selected ? "rounded-md bg-[#eff6ff]   font-semibold" : ""}
          ${!collapsed ? "justify-start" : "justify-center"}
           p-1 text-gray-500 `}
                    >
                      <Tooltip
                        placement="right"
                        key={item.value}
                        title={collapsed ? item.label : ""}
                      >
                        <>
                          <AvatarChat
                            size={26}
                            width={15}
                            height={15}
                            fontSize={10}
                            className="flex-none"
                            hasImage={false}
                            backgroundColor="#f56a00"
                            colorTextAvatar="#FFF"
                            name={getName(item.label, "avatar")}
                            type="user"
                          />
                        </>
                      </Tooltip>
                      <div
                        className={
                          !collapsed
                            ? "flex w-full flex-row items-center"
                            : "hidden"
                        }
                      >
                        <Tooltip title={item.label}>
                          <span className="ml-2  max-w-[170px] flex-1  truncate text-sm">
                            {item.label}
                          </span>
                        </Tooltip>
                        <Badge
                          count={
                            statsMail && statsMail[item.value]?.inbox < 1000
                              ? statsMail[item.value]?.inbox
                              : "+999"
                          }
                          overflowCount={
                            statsMail && statsMail[item.value]?.inbox
                          }
                          style={{
                            backgroundColor: "transparent",
                            color: "#6b7280",
                            boxShadow: "none",
                            marginLeft: "5px",
                            fontSize: "12px",
                            fontWeight: "600",
                          }}
                        />
                      </div>
                    </Link>
                  ))}
              </div>
            </div>
          )}
        {location.pathname.split("/")[1] === "mailing" && (
          <>
            <Divider className="my-2.5 " />

            <LabelsMail
              dataAccounts={dataAccounts}
              statsMail={statsMail}
              isMenuCollapsed={collapsed}
            />
          </>
        )}
      </div>
    </div>
  );
};
export default Menu2;
