import {
  Exclamation<PERSON><PERSON><PERSON><PERSON>illed,
  <PERSON><PERSON><PERSON><PERSON>illed,
  Question<PERSON>ircleOutlined,
  RetweetOutlined,
  StarFilled,
} from "@ant-design/icons";
import { Avatar, Modal, Tooltip, Typography } from "antd";
import { moment_timezone } from "App";
import { setOpenDrawer } from "new-redux/actions/chat.actions";

import {
  getName,
  getUserFromMsg,
  systemMessageTypes,
} from "pages/layouts/chat/utils/ConversationUtils";
import { warningModal } from "pages/layouts/components/warningModal";
import React from "react";
import { useTranslation } from "react-i18next";
import { HiOutlineTicket } from "react-icons/hi2";
import { RxDotFilled } from "react-icons/rx";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
const { Text } = Typography;

function HeaderMessage({ item, hideAvatar, display_header_icon }) {
  const { currentUser } = useSelector((state) => state.chat);
  const dispatch = useDispatch();
  const { t } = useTranslation("common");
  const navigate = useNavigate();
  // const openViewSphere = (integration) => {
  //   if (integration?.app_name === "TicketSphere") {
  //     dispatch(
  //       setOpenDrawer({
  //         type: "ticket_sphere",

  //         external: { id: integration?.ticket_id },
  //       })
  //     );
  //     dispatch(setOpenView360InDrawer(true));
  //     dispatch(setNewInteraction({ type: "updateElementFromDrawer" }));
  //     dispatch({
  //       type: SET_CONTACT_INFO_FROM_DRAWER,
  //       payload: { id: integration?.ticket_id } || {},
  //     });
  //     dispatch(addLastIdToViewSphere(integration?.ticket_id));
  //   }
  // };
  const groupedIntegrations =
    Array.isArray(item?.integrations) &&
    item?.integrations.length > 0 &&
    item.integrations.reduce((acc, integration) => {
      const userName = integration?.user?.name || "Unknown User";
      if (!acc[userName]) {
        acc[userName] = [];
      }
      acc[userName].push(integration);
      return acc;
    }, {});
  // const handleButtonClick = (integration) => {
  //   const confirmRedirect = window.confirm(t("integrations.quitChat"));
  //   if (confirmRedirect) {
  //     navigate(`/tickets/v2/${integration?.ticket_id}`);
  //   }
  // };
  const renderIntegrationFields =
    Array.isArray(item?.integrations) && item?.integrations.length > 0
      ? Object.entries(groupedIntegrations).map(
          ([userName, integrations], i) => {
            const appNamesWithIcons = integrations.map((integration, i) => (
              <div
                className={`flex items-center ${
                  integration.app_name === "TicketSphere"
                    ? "cursor-pointer text-xs capitalize text-blue-500 hover:underline"
                    : ""
                }`}
                key={integration.ticket_id}
                onClick={() => {
                  if (integration?.app_name === "TicketSphere") {
                    warningModal(
                      t("integrations.quitChat"),
                      t,
                      <ExclamationCircleFilled />,
                      t("integrations.goToViewTicket"),
                      () => {
                        navigate("/tickets/v2/" + integration?.ticket_id);
                      },
                      () => {}
                    );
                  }
                }}
              >
                {integration?.app_name === "TicketSphere" ? (
                  <HiOutlineTicket style={{ fontSize: "18px" }} />
                ) : integration?.logo ? (
                  <Avatar src={integration?.logo} size={14} />
                ) : null}
                <span className="ml-1">{integration?.app_name}</span>
                <Tooltip
                  title={
                    <div className="flex flex-col text-white">
                      <span>
                        {t("contacts.createdAt")} :{" "}
                        {moment_timezone(integration?.created_at).format("lll")}
                      </span>
                      <Text
                        copyable={{
                          text: integration?.ticket_id,
                          tooltips: "",
                        }}
                        style={{ color: "white" }}
                      >
                        {t("integrations.ticketId")} : {integration?.ticket_id}
                      </Text>
                    </div>
                  }
                >
                  <QuestionCircleOutlined className="ml-1 text-gray-500" />
                </Tooltip>{" "}
                {i < integrations.length - 1 && (
                  <span className="px-1 font-semibold">,</span>
                )}
              </div>
            ));

            return (
              <div key={userName} className="flex items-center">
                {(item?.favoris?.length > 0 &&
                  item?.favoris?.includes(currentUser?._id)) ||
                item?.important !== "" ||
                item.forward?.forwarded === 0 ||
                i > 0 ? (
                  <RxDotFilled className="text-[#808080]" />
                ) : null}
                <Text className="flex items-center text-xs capitalize">
                  {appNamesWithIcons}{" "}
                  {userName ? (
                    <span className="mx-1 italic text-gray-500 ">
                      {t("chat.action.By") + " " + getName(userName, "name")}
                    </span>
                  ) : null}
                </Text>
              </div>
            );
          }
        )
      : [];

  return ((!item?.deleted_at && hideAvatar) ||
    systemMessageTypes.includes(item?.type)) &&
    display_header_icon ? (
    <div className="flex items-center ">
      {item?.favoris?.length > 0 &&
      item?.favoris?.includes(currentUser?._id) ? (
        <Text
          onClick={() => {
            dispatch(setOpenDrawer({ type: "starred" }));
          }}
          className="group cursor-pointer text-xs capitalize text-blue-500 hover:underline "
        >
          <StarFilled className="mr-0.5   " />
          {t("chat.action.starred")}
          <Tooltip title={t("chat.action.starred_info")}>
            <QuestionCircleOutlined className="text-gray-500" />
          </Tooltip>
        </Text>
      ) : null}

      {item?.favoris?.length > 0 &&
        item?.favoris?.includes(currentUser?._id) &&
        (item?.important !== "" || item.forward?.forwarded === 0) && (
          <RxDotFilled className="text-[#808080]" />
        )}

      {item?.important !== "" ? (
        <Text
          onClick={() => {
            dispatch(setOpenDrawer({ type: "pinned" }));
          }}
          className="group cursor-pointer text-xs  text-blue-500 first-letter:uppercase hover:underline "
        >
          <PushpinFilled className="mr-0.5 " />
          {t("chat.action.pinned")}
          <span className="mx-1 italic text-gray-500 ">
            {t("chat.action.By") +
              " " +
              getName(getUserFromMsg(item?.important)?.name, "name")}
          </span>

          <Tooltip title={t("chat.action.pinned_info")}>
            <QuestionCircleOutlined className="ml-0.5 text-gray-500" />
          </Tooltip>
        </Text>
      ) : (
        <></>
      )}
      {((item?.favoris?.length > 0 &&
        item?.favoris?.includes(currentUser?._id)) ||
        item?.important !== "") &&
        item.forward?.forwarded === 0 && (
          <RxDotFilled className="text-[#808080]" />
        )}

      {item.forward && item.forward?.forwarded === 0 && (
        <Text
          onClick={() => {
            dispatch(
              setOpenDrawer({
                type: "forward",

                external: item,
              })
            );
          }}
          className="cursor-pointer gap-x-0.5 text-blue-500 hover:underline"
        >
          <RetweetOutlined className="mr-0.5  " />

          {t("chat.forward.text_message_forwarded")}
        </Text>
      )}
      {renderIntegrationFields}
    </div>
  ) : (
    <></>
  );
}

export default HeaderMessage;
