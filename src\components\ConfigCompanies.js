import { RollbackOutlined, UploadOutlined } from "@ant-design/icons";
import {
  <PERSON>ton,
  Col,
  Divider,
  Form,
  Input,
  InputNumber,
  message,
  Row,
  Select,
  Spin,
  Upload,
} from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useEffect, useRef } from "react";
import { toastNotification } from "./ToastNotification";
import { AllCountries } from "./AllCountries";
import { FormFooter } from "../pages/components/FormFooter";
import { Option } from "antd/es/mentions";
import { useNavigate, useParams } from "react-router-dom";
import TabsCompanies from "./TabsCompanies";
import { useDispatch } from "react-redux";
import { showNameOrg } from "../new-redux/actions/configCompanies.actions/configCompaniesAction";
import { URL_ENV } from "index";
import { Quill } from "react-quill";
import "react-quill/dist/quill.snow.css";
import quillEmoji from "react-quill-emoji";
import "react-quill-emoji/dist/quill-emoji.css";
import ImageResize from "quill-image-resize-module-react";
import "react-quill/dist/quill.snow.css";
import MainService from "services/main.service";
import NotFoundPage from "pages/404";
/* eslint-disable no-template-curly-in-string */
const validateMessages = {
  required: "${label} is required!",
  types: {
    email: "${label} is not a valid email!",
    number: "${label} is not a valid number!",
  },
  number: {
    range: "${label} must be between ${min} and ${max}",
  },
};
const ConfigCompanies = () => {
  const [logo, setLogo] = useState([]);
  const [icon, setIcon] = useState([]);
  const [country, setCountry] = useState("");
  const [form1submittedValues, setForm1SubmittedValues] = useState(null);
  const [form2submittedValues, setForm2SubmittedValues] = useState(null);
  const [keyTab, setKeyTab] = useState("1");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [countries, setCountries] = useState([]);
  const [phones, setPhones] = useState([]);
  const [loading, setLoading] = useState(true);
  const [id, setId] = useState(0);
  const [fileList, setFileList] = useState([]);
  const [loadingInfo, setLoadingInfo] = useState(false);
  const [loadingFinance, setLoadingFinance] = useState(false);
  const [formInfoChanged, setIsFormInfoChanged] = useState(false);
  const [formFiscalChanged, setIsFormFiscalChanged] = useState(false);
  const [suffix, setSuffix] = useState("+216");
  const [currency, setCurrency] = useState("TND");
  const [currencyList, setCurrencyList] = useState([]);
  const [company, setCompany] = useState({});
  const [show404, setShow404] = useState(false);
  const quillRef = useRef(null);
  const [form1] = Form.useForm();
  const [form2] = Form.useForm();
  const dispatch = useDispatch();
  const params = useParams();
  const navigate = useNavigate();
  Quill.register("modules/imageResize", ImageResize);
  Quill.register(
    {
      "formats/emoji": quillEmoji.EmojiBlot,
      "modules/emoji-toolbar": quillEmoji.ToolbarEmoji,
      "modules/emoji-textarea": quillEmoji.TextAreaEmoji,
      "modules/emoji-shortname": quillEmoji.ShortNameEmoji,
    },
    true
  );
  function handleInputNumberKeyDown(e) {
    if (e.key === "-" && !e.target.value) {
      e.preventDefault();
    }
    if (e.key === "e") {
      e.preventDefault();
    }
    if (
      isNaN(parseInt(e.key)) &&
      ![
        "Tab",
        "Backspace",
        "Delete",
        "ArrowLeft",
        "ArrowRight",
        "Enter",
        ".",
      ].includes(e.key)
    ) {
      e.preventDefault();
    }
  }
  async function resizeImage(file, maxWidth) {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = function (event) {
        const img = new Image();
        img.src = event.target.result;
        img.onload = function () {
          const canvas = document.createElement("canvas");
          let width = img.width;
          let height = img.height;

          if (width > maxWidth) {
            height = Math.round((height * maxWidth) / width);
            width = maxWidth;
          }

          canvas.width = width;
          canvas.height = height;

          const ctx = canvas.getContext("2d");
          ctx.drawImage(img, 0, 0, width, height);

          canvas.toBlob(function (blob) {
            const resizedFile = new File([blob], file.name, {
              type: file.type,
            });
            resolve(resizedFile);
          }, file.type);
        };
      };
    });
  }
  const imageHandler = () => {
    if (quillRef?.current != null) {
      const editor = quillRef.current.getEditor();

      const input = document.createElement("input");
      input.setAttribute("type", "file");
      input.setAttribute("accept", "image/*");
      input.click();
      input.onchange = async function () {
        const file = input.files[0];
        const resizedImage = await resizeImage(file, 300);
        const formData = new FormData();
        formData.append("upload", resizedImage);
        formData.append("file_name", file?.name);

        MainService.uploadFile360(formData).then((res) => {
          const range = editor.getSelection();
          const link = `${URL_ENV?.REACT_APP_BASE_URL}${res.data.message.path}`;

          editor.insertEmbed(range.index, "image", link);
        });
      }.bind(this);
    }
  };

  const getCurrencies = async () => {
    try {
      const { data } = await MainService.getCurrencies();
      setCurrencyList(data?.message);
    } catch (err) {}
  };
  useEffect(() => {
    setLoading(true);

    const countries = [];
    const currencies = [];
    AllCountries.forEach((element) => {
      countries.push({ region: element.region, name: element.name });
    });

    AllCountries.forEach((el) => currencies.push(el.currencies));
    setCountries(
      countries.filter(
        (value, index, self) => index === self.findIndex((t) => t === value)
      )
    );
    MainService.getCountries().then((res) => {
      getCurrencies();

      setPhones(res.data.data);
      if (params.id === "new") {
        setLoading(false);
      }
    });
    if (params.id !== "new") {
      try {
        MainService.getCompanyInSettings(params.id)
          .then((response) => {
            setCompany({ ...response.data.data[0] });
            setId(response.data.data[0]?.id);
            setSuffix(response.data.data[0].phone?.split("-")[0]);
            setCurrency(response.data.data[0].capital?.split("-")[1]);
            dispatch(showNameOrg(response.data.data[0].label));
            if (
              response.data.data[0]?.logo &&
              response.data.data[0]?.logo !== "null"
            ) {
              setFileList([
                {
                  uid: "-1",
                  name: "image.png",
                  status: "done",
                  url: `${
                    URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                  }/${response.data.data[0]?.logo}`,
                  thumbUrl: `${
                    URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                  }/${response.data.data[0]?.logo}`,
                },
              ]);

              setLogo([
                {
                  uid: "1",
                  name: "image.png",
                  status: "done",
                  url: `${
                    URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                  }/${response.data.data[0]?.logo}`,
                  thumbUrl: `${
                    URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                  }/${response.data.data[0]?.logo}`,
                },
              ]);
              form1.setFieldsValue({
                company: {
                  logo: [
                    {
                      uid: "1",
                      name: "image.png",
                      status: "done",
                      url: `${
                        URL_ENV?.REACT_APP_BASE_URL +
                        URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                      }/${response.data.data[0]?.logo}`,
                      thumbUrl: `${
                        URL_ENV?.REACT_APP_BASE_URL +
                        URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                      }/${response.data.data[0]?.logo}`,
                    },
                  ],
                },
              });
            }
            if (response.data.data[0]?.icon) {
              setIcon([
                {
                  uid: 2,
                  name: "icon.png",
                  status: "done",
                  url: `${
                    URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                  }/${response.data.data[0]?.icon}`,
                  thumbUrl: `${
                    URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                  }/${response.data.data[0]?.icon}`,
                },
              ]);
              form1.setFieldsValue({
                company: {
                  icon: [
                    {
                      uid: 2,
                      name: "icon.png",
                      status: "done",
                      url: `${
                        URL_ENV?.REACT_APP_BASE_URL +
                        URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                      }/${response.data.data[0]?.icon}`,
                      thumbUrl: `${
                        URL_ENV?.REACT_APP_BASE_URL +
                        URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                      }/${response.data.data[0]?.icon}`,
                    },
                  ],
                },
              });
            }

            setLoading(false);
          })
          .catch(function (error) {
            if (error.response.status === 404) {
              setShow404(true);
            }
            setLoading(false);
          });
      } catch (err) {
        setLoading(false);

        console.log(err);
      }
    }
  }, [params.id]);

  useEffect(() => {
    if (form1.getFieldValue(["company", "country"])) {
      setSuffix(
        AllCountries.find(
          (el) => el.name === form1.getFieldValue(["company", "country"])
        )?.callingCodes[0]
      );
      setCurrency(
        AllCountries.find(
          (el) => el.name === form1.getFieldValue(["company", "country"])
        )?.currencies[0].code
      );
    }
  }, [form1.getFieldValue(["company", "country"])]);
  const [t] = useTranslation("common");
  const handlePreview = (file) => {
    if (file.originFileObj) {
      const reader = new FileReader();
      reader.readAsDataURL(file.originFileObj);

      reader.onloadend = () => {
        const base64String = reader.result;
        // Ouvrir l'image dans une nouvelle fenêtre
        const imageData = atob(base64String.split(",")[1]);
        const arrayBuffer = new ArrayBuffer(imageData.length);
        const uintArray = new Uint8Array(arrayBuffer);

        for (let i = 0; i < imageData.length; i++) {
          uintArray[i] = imageData.charCodeAt(i);
        }

        const blob = new Blob([arrayBuffer], { type: "image/png" });
        const imageUrl = URL.createObjectURL(blob);

        window.open(imageUrl);
      };
    } else {
      window.open(file.url);
    }
  };
  const onFinishInfoGeneral = async (values) => {
    let newValues = values.company;
    let formData = new FormData(); //formdata object
    formData.append("companie_id", id);

    company?.sender_name &&
      formData.append("sender_name", company?.sender_name);
    company?.sender_sms && formData.append("sender_sms", company?.sender_sms);
    company?.signature_system_email &&
      formData.append(
        "signature_system_email",
        company?.signature_system_email
      );
    company?.system_email &&
      formData.append("system_email", company?.system_email);
    if (suffix) {
      formData.append("sufix", suffix);
    }
    if (
      values?.company?.icon?.length > 0 &&
      values?.company?.icon[0]?.uid == 2
    ) {
      newValues = { ...newValues, icon: "" };
    }
    if (
      values?.company?.logo?.length > 0 &&
      values?.company?.logo[0]?.uid == 1
    ) {
      newValues = { ...newValues, logo: "" };
    }

    Object.keys(newValues).map((el) =>
      formData.append(el, newValues[el] || "")
    );

    try {
      setLoadingInfo(true);
      const {
        data: { data },
      } = await MainService.submitGeneralInfoCompany(formData);
      setIsFormInfoChanged(false);
      setLoadingInfo(false);
      setForm1SubmittedValues(data);
      setId(data?.id);

      if (params.id === "new") {
        toastNotification(
          "success",
          data.label + t("toasts.created"),
          "topRight"
        );
      } else {
        toastNotification("success", data.label + t("toasts.edit"), "topRight");
      }
    } catch (err) {
      setLoadingInfo(false);

      toastNotification("error", t("toasts.somethingWrong"), "topRight");

      console.log(err);
    }
  };
  const onFinish = async (values) => {
    let formData = new FormData(); //formdata object
    formData.append("companie_id", id);
    formData.append("sender_name", company?.sender_name);
    formData.append("sender_sms", company?.sender_sms);
    formData.append("signature_system_email", company?.signature_system_email);
    formData.append("system_email", company?.system_email);
    if (currency) {
      formData.append("currency", currency);
    }
    Object.keys(values.company).map((el) =>
      formData.append(el, values.company[el] || "")
    );

    try {
      setLoadingFinance(true);
      const {
        data: { data },
      } = await MainService.submitFinancialInfoCompany(formData);
      setIsFormFiscalChanged(false);
      setForm2SubmittedValues(data);
      if (params.id === "new") {
        setKeyTab("2");
        navigate(`/settings/general/companies/${id}/banks`);
      }
      setLoadingFinance(false);
      toastNotification("success", data.label + t("toasts.edit"), "topRight");
    } catch (err) {
      setLoadingFinance(false);

      toastNotification("error", t("toasts.somethingWrong"), "topRight");

      console.log(err);
    }
  };

  const props = {
    beforeUpload: (file) => {
      const isPNG = file.type.split("/")[0] === "image";
      if (!isPNG) {
        message.error(`${file.name} ${t("import.isNotImage")} !`);
        return Upload.LIST_IGNORE;
      }
      return false;
    },
    onChange: (info) => {
      console.log(info.fileList);
    },
  };

  const handleForm1Reset = () => {
    if (form1submittedValues) {
      form1.setFieldsValue({
        company: {
          ...form1submittedValues,
          phone: form1submittedValues.phone?.split("-")[1],
        },
      });
      setSuffix(form1submittedValues.phone?.split("-")[0]);
    } else {
      form1.resetFields();
    }
  };
  const handleForm2Reset = () => {
    if (form2submittedValues) {
      form2.setFieldsValue({
        company: {
          ...form2submittedValues,
          capital: form2submittedValues.capital?.split("-")[0],
        },
      });
      setCurrency(form2submittedValues.capital?.split("-")[1]);
    } else {
      form2.resetFields();
    }
  };
  const handleFormInfoChange = (values, all) => {
    if (all.company.phone && !suffix) {
      setIsFormInfoChanged(false);
    } else if (!all.company.phone && suffix) {
      setIsFormInfoChanged(false);
    } else setIsFormInfoChanged(true);
  };

  const handleFormFiscalChange = () => {
    setIsFormFiscalChanged(true);
  };

  const websiteValidator = (rule, value, callback) => {
    if (value && value.trim() !== "") {
      const websiteRegex =
        /^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)?[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i;
      if (!websiteRegex.test(value)) {
        callback("Please enter a valid website!");
      } else {
        callback();
      }
    } else {
      callback();
    }
  };
  if (show404) {
    return (
      <>
        <Button
          icon={<RollbackOutlined />}
          onClick={() => {
            navigate("/settings/general/companies");
            dispatch(showNameOrg(""));
          }}
          style={{ margin: "16px 16px 0 16px" }}
        >
          {/* {t("companies.accessTableCompanies")} */}
        </Button>
        <div className="height-screen bg-white">
          <NotFoundPage />
        </div>
      </>
    );
  }
  return (
    <>
      <Button
        icon={<RollbackOutlined />}
        onClick={() => {
          navigate("/settings/general/companies");
          dispatch(showNameOrg(""));
        }}
        style={{ margin: "16px 16px 0 16px" }}
      >
        {/* {t("companies.accessTableCompanies")} */}
      </Button>
      <TabsCompanies
        keyTab={keyTab}
        setKeyTab={setKeyTab}
        companie_id={id}
        nameOrg={company?.label}
        company={company}
      />
      {loading ? (
        <div className="flex h-[calc(100vh-57px)] items-center justify-center">
          <Spin />
        </div>
      ) : (
        <>
          {!loading ? (
            <div className="px-4">
              <Form
                name="nest-messages"
                onFinish={onFinishInfoGeneral}
                validateMessages={validateMessages}
                layout={"vertical"}
                onValuesChange={handleFormInfoChange}
                form={form1}
              >
                <Divider orientation="left">
                  {t("companies.infoGeneral")}{" "}
                </Divider>

                <Row
                  gutter={8}
                  style={{
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <Col sm={24} md={12} lg={8}>
                    <Form.Item
                      name={["company", "label"]}
                      label={t("companies.socialreason")}
                      initialValue={company?.label || ""}
                      rules={[{ required: true }]}
                    >
                      <Input />
                    </Form.Item>
                  </Col>

                  <Col sm={24} md={12} lg={8}>
                    <Form.Item
                      name={["company", "adress"]}
                      label={t("companies.adress")}
                      initialValue={company?.adress || ""}
                    >
                      <Input />
                    </Form.Item>
                  </Col>

                  <Col sm={24} md={12} lg={4}>
                    <Form.Item
                      name={["company", "logo"]}
                      label="Logo"
                      initialValue={company?.logo || ""}
                    >
                      <Upload
                        {...props}
                        className="iconConfigCompany"
                        accept="image/*"
                        listType="picture-card"
                        onChange={(e) => {
                          setLogo(e.fileList);
                          e.fileList.length > 0
                            ? form1.setFieldsValue({
                                company: {
                                  logo: e.file,
                                },
                              })
                            : form1.setFieldsValue({
                                company: {
                                  logo: 1,
                                },
                              });
                        }}
                        defaultFileList={[...fileList]}
                        multiple={false}
                        // className="picture-card"
                        onPreview={handlePreview}
                      >
                        {logo.length < 1 && (
                          <span>
                            {t("wiki.UploadImage")} <UploadOutlined />
                          </span>
                        )}
                      </Upload>
                    </Form.Item>
                  </Col>
                  <Col sm={24} md={12} lg={4}>
                    <Form.Item
                      name={["company", "icon"]}
                      label={t("tags.icon")}
                      initialValue={company?.icon || ""}
                    >
                      <Upload
                        {...props}
                        accept="image/*"
                        className="iconConfigCompany"
                        listType="picture-card"
                        onChange={(e) => {
                          setIcon(e.fileList);
                          e.fileList.length > 0
                            ? form1.setFieldsValue({
                                company: {
                                  icon: e.file,
                                },
                              })
                            : form1.setFieldsValue({
                                company: {
                                  icon: 1,
                                },
                              });
                        }}
                        defaultFileList={[...icon]}
                        multiple={false}
                        // className="picture-card"
                        onPreview={handlePreview}
                      >
                        {icon.length < 1 && (
                          <span>
                            {t("wiki.UploadImage")} <UploadOutlined />
                          </span>
                        )}
                      </Upload>
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={8}>
                  <Col sm={24} md={12} lg={8}>
                    <Form.Item
                      name={["company", "city"]}
                      label={t("companies.city")}
                      initialValue={company?.city || ""}
                    >
                      <Input />
                    </Form.Item>
                  </Col>
                  <Col sm={24} md={12} lg={8}>
                    <Form.Item
                      name={["company", "code_postal"]}
                      label={t("companies.postalCode")}
                      initialValue={company?.code_postal || ""}
                    >
                      <InputNumber
                        onKeyDown={handleInputNumberKeyDown}
                        style={{ width: "100%" }}
                      />
                    </Form.Item>
                  </Col>
                  <Col sm={24} md={12} lg={8}>
                    <Form.Item
                      name={["company", "country"]}
                      label={t("companies.country")}
                      initialValue={company?.country || ""}
                    >
                      <Select
                        value={country}
                        showSearch
                        allowClear
                        onChange={setCountry}
                        placeholder="Select Country"
                        filterSort={(optionA, optionB) =>
                          (optionA?.label ?? "")
                            .toLowerCase()
                            .localeCompare((optionB?.label ?? "").toLowerCase())
                        }
                      >
                        {countries
                          .sort(
                            (a, b) =>
                              String(a.region).localeCompare(b.region) ||
                              String(a.name).localeCompare(b.name)
                          )
                          .map((tz) => (
                            <Select.Option key={tz.name} value={tz.name}>
                              {tz.region}/{tz.name}
                            </Select.Option>
                          ))}
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
                <Row gutter={8}>
                  <Col sm={24} md={12} lg={8}>
                    <Form.Item
                      name={["company", "phone"]}
                      label={t("companies.phone")}
                      initialValue={company?.phone?.split("-")[1] || ""}
                      rules={[
                        {
                          required: !phoneNumber && suffix ? true : false,
                          message: `${t("companies.phone")} ${t(
                            "table.header.isrequired"
                          )}`,
                        },
                      ]}
                    >
                      <InputNumber
                        onKeyDown={handleInputNumberKeyDown}
                        style={{ width: "100%" }}
                        onChange={(value) => {
                          setPhoneNumber(value);
                        }}
                        addonBefore={
                          <Select
                            value={suffix}
                            showSearch
                            onChange={(value) => {
                              setSuffix(value);
                              setIsFormInfoChanged(true);
                            }}
                            filterOption={(input, option) =>
                              option?.nameEn
                                .toLowerCase()
                                .includes(input.toLowerCase()) ||
                              option?.nameFr
                                .toLowerCase()
                                .includes(input.toLowerCase()) ||
                              option?.value
                                .toLowerCase()
                                .includes(input.toLowerCase())
                            }
                            style={{ width: "120px" }}
                            placeholder="Suffix"
                          >
                            {phones.map((el, i) => (
                              <Select.Option
                                key={i}
                                value={el?.dial_code}
                                nameEn={el?.name_en}
                                nameFr={el?.name_fr}
                              >
                                {el.flag} {el.dial_code}
                              </Select.Option>
                            ))}
                          </Select>
                        }
                        minLength={7}
                      />
                    </Form.Item>
                  </Col>

                  <Col sm={24} md={12} lg={8}>
                    <Form.Item
                      name={["company", "website"]}
                      label={t("companies.website")}
                      initialValue={company?.website || ""}
                      rules={[{ validator: websiteValidator }]}
                    >
                      <Input addonBefore="https://" />
                    </Form.Item>
                  </Col>
                  <Col sm={24} md={12} lg={8}>
                    <Form.Item
                      name={["company", "email"]}
                      label="Email"
                      rules={[
                        {
                          type: "email",
                        },
                      ]}
                      initialValue={company?.email || ""}
                    >
                      <Input />
                    </Form.Item>
                  </Col>
                </Row>

                <div className="py-5">
                  <FormFooter
                    onClickCancel={() => {
                      setIsFormInfoChanged(false);
                      handleForm1Reset();
                    }}
                    loadButton={loadingInfo}
                    submitDisabled={!formInfoChanged}
                  />
                </div>
              </Form>
              <Form
                name="nest-messages"
                onFinish={onFinish}
                validateMessages={validateMessages}
                layout={"vertical"}
                onValuesChange={handleFormFiscalChange}
                form={form2}
              >
                <Divider orientation="left" style={{ padding: "20px 0" }}>
                  {t("companies.infoFis")}{" "}
                </Divider>

                <Row gutter={8}>
                  <Col sm={24} md={12}>
                    <Form.Item
                      name={["company", "tax_identification"]}
                      label={t("companies.taxId")}
                      initialValue={company?.tax_identification || ""}
                    >
                      <Input disabled={id === 0 ? true : false} />
                    </Form.Item>
                  </Col>

                  <Col sm={24} md={12}>
                    <Form.Item
                      name={["company", "tva_code"]}
                      label={t("companies.vatCode")}
                      initialValue={company?.tva_code || ""}
                    >
                      <Input disabled={id === 0 ? true : false} />
                    </Form.Item>
                  </Col>
                </Row>
                <Row gutter={8}>
                  <Col sm={24} md={12}>
                    <Form.Item
                      name={["company", "trade_register"]}
                      label={t("companies.tradeRegister")}
                      initialValue={company?.trade_register || ""}
                    >
                      <Input disabled={id === 0 ? true : false} />
                    </Form.Item>
                  </Col>

                  <Col sm={24} md={12}>
                    <Form.Item
                      name={["company", "capital"]}
                      label={t("companies.capital")}
                      initialValue={company?.capital?.split("-")[0] || ""}
                    >
                      <InputNumber
                        onKeyDown={handleInputNumberKeyDown}
                        style={{ width: "100%" }}
                        min="0"
                        addonBefore={
                          <Select
                            value={currency}
                            disabled={id === 0 ? true : false}
                            showSearch
                            onChange={(value) => {
                              setCurrency(value);
                              setIsFormFiscalChanged(true);
                            }}
                            filterOption={(input, option) =>
                              (option?.value.toLowerCase() ?? "").includes(
                                input.toLowerCase()
                              )
                            }
                            style={{ width: "120px" }}
                            placeholder="Devise"
                          >
                            {currencyList.map((el) => (
                              <Option value={el.currency} key={el.currency}>
                                {el.currency} ({el.currency_symbol})
                              </Option>
                            ))}
                          </Select>
                        }
                        disabled={id === 0 ? true : false}
                      />
                    </Form.Item>
                  </Col>
                </Row>
                <div className="py-5">
                  <FormFooter
                    onClickCancel={() => {
                      setIsFormFiscalChanged(false);
                      handleForm2Reset();
                    }}
                    loadButton={loadingFinance}
                    submitDisabled={!formFiscalChanged || id === 0}
                  />
                </div>
              </Form>
            </div>
          ) : (
            <div className="flex h-[calc(100vh-200px)] items-center justify-center">
              <Spin />
            </div>
          )}
        </>
      )}
    </>
  );
};

export default ConfigCompanies;
