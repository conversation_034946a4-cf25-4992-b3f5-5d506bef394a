import { <PERSON><PERSON>, Divider, List, Skeleton, Spin, Tag, Tooltip } from "antd";
import React from "react";
import VirtualList from "rc-virtual-list";
import {
  dispositionInfo,
  formatDateComparison,
} from "pages/voip/helpers/helpersFunc";
import { useTranslation } from "react-i18next";
import { AvatarChat } from "components/Chat";
import { URL_ENV } from "index";
import { getName } from "pages/layouts/chat/utils/ConversationUtils";
import { useSelector } from "react-redux";
import { ConditionIcons } from "pages/rmc/mailing/components/Log";
import { useWindowSize } from "custom-hooks/useWindowSize";
import { getIconRmc } from "pages/components/DetailsProfile/ChatRmc";
import {
  AntDesignOutlined,
  MailOutlined,
  UserOutlined,
  WhatsAppOutlined,
} from "@ant-design/icons";
import { IoEyeSharp } from "react-icons/io5";
import { Mailbox, MoveRight } from "lucide-react";
import { RiMailSendLine } from "react-icons/ri";
import { HiOutlinePhone } from "react-icons/hi2";
import { SlCallIn, SlCallOut } from "react-icons/sl";
import { BiPhoneCall } from "react-icons/bi";
import { TbMessage2Share } from "react-icons/tb";
import { FcDepartment } from "react-icons/fc";

const FakeList = ({
  list,
  setSelectedItem,
  selectedItem,
  setCurrentPage,
  lastPage,
  loading,
}) => {
  const [t] = useTranslation("common");
  const { user } = useSelector((state) => state.user);
  const [_, height] = useWindowSize();

  const onScroll = (e) => {
    // Refer to: https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollHeight#problems_and_solutions
    if (
      Math.abs(
        e.currentTarget.scrollHeight -
          e.currentTarget.scrollTop -
          (height - 200)
      ) <= 1
    ) {
      setCurrentPage((prev) => prev + 1);
    }
  };

  return (
    <List>
      <Spin spinning={false}>
        <div>
          <Divider
            style={{
              textAlign: "center",
              position: "sticky",
              top: "0",
              background: "transparent",
              zIndex: "20",
              marginTop: "-10px",
              padding: "5px 0",
            }}
          >
            Aujourd'hui
          </Divider>

          <div className="pb-2">
            <div
              className={` ${
                selectedItem === "email" ? "bg-slate-100" : "hover:bg-slate-50"
              }
                      cursor-pointer rounded-md px-2`}
            >
              <List.Item
                key={1}
                onClick={() => setSelectedItem("email")}
                className={` 
                cursor-pointer rounded-md px-2 `}
              >
                <List.Item.Meta
                  avatar={
                    <Tooltip title="Type">
                      <Avatar
                        size="large"
                        icon={<MailOutlined />}
                        className="bg-orange-400"
                      />
                    </Tooltip>
                  }
                  title={
                    <Tooltip title="Sender">
                      <div className="flex items-center gap-1">
                        <span><EMAIL></span> <RiMailSendLine />
                      </div>
                    </Tooltip>
                  }
                  description={
                    <Tooltip title="Receiver">
                      <Tag
                        color="green"
                        icon={
                          <Mailbox size={16} className="relative top-1 mr-1" />
                        }
                      >
                        <EMAIL>
                      </Tag>
                    </Tooltip>
                  }
                />
                <div className="flex flex-col items-end ">
                  <span>12:20</span>
                  <span className="flex items-center gap-x-1">
                    <Avatar.Group maxCount={2} size="small">
                      <Avatar
                        src="https://api.dicebear.com/7.x/miniavs/svg?seed=2"
                        size="small"
                      />
                      <Avatar
                        style={{ backgroundColor: "#f56a00" }}
                        size="small"
                      >
                        K
                      </Avatar>
                      <Tooltip title="Ant User" placement="top">
                        <Avatar
                          style={{ backgroundColor: "#87d068" }}
                          icon={<UserOutlined />}
                        />
                      </Tooltip>
                      <Avatar
                        style={{ backgroundColor: "#1677ff" }}
                        icon={<AntDesignOutlined />}
                      />
                    </Avatar.Group>
                    <IoEyeSharp className="h-5 w-5 text-[#757eaa]" />
                  </span>
                </div>
              </List.Item>
            </div>
            <div
              className={` ${
                selectedItem === "call" ? "bg-slate-100" : "hover:bg-slate-50"
              }
                      cursor-pointer rounded-md px-2`}
            >
              <List.Item
                key={2}
                // onClick={() => setSelectedItem(el)}
                onClick={() => setSelectedItem("call")}
                className=""
              >
                <List.Item.Meta
                  avatar={
                    <Tooltip title="Type">
                      <Avatar
                        size="large"
                        icon={<HiOutlinePhone />}
                        className="bg-blue-400"
                      />
                    </Tooltip>
                  }
                  title={
                    <div className="flex items-center gap-1">
                      <span className="flex items-center gap-x-1">
                        <span>
                          23119020 <SlCallOut />
                        </span>{" "}
                        <MoveRight size={16} />{" "}
                        <span>
                          55301471 <SlCallIn />
                        </span>
                      </span>
                    </div>
                  }
                  description={
                    <Tag
                      color="green"
                      icon={
                        <BiPhoneCall
                          style={{ fontSize: 16 }}
                          className="relative top-1 mr-1"
                        />
                      }
                    >
                      Appel en cours 1:20 min
                    </Tag>
                  }
                />
                <div className="flex flex-col items-end ">
                  <span>12:18</span>
                  <span></span>
                </div>
              </List.Item>
            </div>
            <div
              className={` ${
                selectedItem === "whatsapp"
                  ? "bg-slate-100"
                  : "hover:bg-slate-50"
              }
                      cursor-pointer rounded-md px-2`}
            >
              <List.Item
                key={3}
                onClick={() => setSelectedItem("whatsapp")}
                className=""
              >
                <List.Item.Meta
                  avatar={
                    <Tooltip title="Type">
                      <Avatar
                        size="large"
                        icon={<WhatsAppOutlined />}
                        className="bg-green-400"
                      />
                    </Tooltip>
                  }
                  title={
                    <div className="flex items-center gap-1">
                      <span className="flex items-center gap-x-1">
                        <span>
                          23119020 <TbMessage2Share />
                        </span>{" "}
                      </span>
                    </div>
                  }
                  description={
                    <Tag
                      color="purple"
                      icon={
                        <FcDepartment
                          style={{ fontSize: 16 }}
                          className="relative top-1 mr-1"
                        />
                      }
                    >
                      Comunik
                    </Tag>
                  }
                />
                <div className="flex flex-col items-end ">
                  <span>12:17</span>
                  <span></span>
                </div>
              </List.Item>
            </div>
          </div>
          {/* {loading ? (
              <Skeleton
                avatar
                paragraph={{
                  rows: 1,
                }}
                active
              />
            ) : null} */}
          {/*
           */}
        </div>
      </Spin>
    </List>
  );
};

export default FakeList;
