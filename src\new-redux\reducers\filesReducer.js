import {
  ADD_FILE360,
  REMOVE_FILE360,
  UPDATE_FILE360,
  SET_FILES360,
  ADD_FILES360_TO_LIST,
  MODIFY_FILE360_LABEL,
  MODIFY_FILE360_NAME,
  <PERSON>ARCH_FILE360,
  RESET_SEARCH_FILE360,
  SET_CURRENT_SELECTED_CONTACT,
  UNSET_CURRENT_SELECTED_CONTACT,
  UPDATE_FILE_FOR_MERCURE,
  DELETE_FILE_FOR_MERCURE,
  UPDATE_FILE_LABEL_FOR_MERCURE
} from "../constants";

const initialState = {
  files: [],
  oldFilesList: [],
  currentSelectedContact: null,
};

const files = (state = initialState, action) => {
  const { type, payload } = action;
  switch (type) {
    case ADD_FILE360:
      return {
        ...state,
        files: [payload, ...state.files],
        oldFilesList: [payload, ...state.oldFilesList],
      };
    case REMOVE_FILE360:
     // console.log("We are here");
      return {
        ...state,
        // files: state.files.filter((file) => file?._id["$oid"] !== payload),
        files: state.files.filter((file) => file?._id !== payload),
      };
    case UPDATE_FILE360:
      return {
        ...state,
        files: state.files.map((file) =>
          file._id === payload._id ? payload : file
        ),
      };
    case UPDATE_FILE_FOR_MERCURE:
      return {
        ...state,
        files: state.files.map((file) =>
          // file._id["$oid"] == payload._id["$oid"] ? payload : file
          file._id == payload._id ? payload : file
        ),
      };

    case UPDATE_FILE_LABEL_FOR_MERCURE:
      return {
        ...state,
        files: state.files.map((file) =>
          // file._id["$oid"] == payload._id["$oid"]
          file._id == payload._id
            ? {
                ...file,
                file_label: payload.file_label,
              }
            : file
        ),
      };

    case DELETE_FILE_FOR_MERCURE:
      return {
        ...state,
        files: state.files.filter(
          // (file) => file._id["$oid"] != payload._id["$oid"]
          (file) => file._id != payload._id
        ),
      };

    case SET_FILES360:
      return {
        ...state,
        files: payload,
        oldFilesList: payload,
      };

    case ADD_FILES360_TO_LIST:
      //merge new files with old files and remove duplicates

      let mergedFiles = [...state.files, ...payload];

      for (let i = 0; i < mergedFiles.length; i++) {
        for (let j = i + 1; j < mergedFiles.length; j++) {
          if (mergedFiles[i]._id === mergedFiles[j]._id) {
            mergedFiles.splice(j, 1);
          }
        }
      }

      return {
        ...state,
        files: mergedFiles,
      };

    case MODIFY_FILE360_LABEL:
      return {
        ...state,
        files: state.files.map((file) =>
          file._id === payload._id
            ? {
                ...file,
                file_label: payload.file_label,
              }
            : file
        ),
      };

    case MODIFY_FILE360_NAME:
    //  console.log("PAYLOAD", payload);
      return {
        ...state,
        files: state.files.map((file) =>
          // file._id["$oid"] === payload._id["$oid"]
          file._id === payload._id
            ? {
                ...file,
                file_name: payload.file_name,
                size: payload.size,
              }
            : file
        ),
      };

    case SEARCH_FILE360:
      let oldFiles = state.oldFilesList;
      let newFiles = oldFiles.filter((file) =>
        file.file_label.toLowerCase().includes(payload.toLowerCase())
      );
      return {
        ...state,
        files: newFiles,
        oldFilesList: oldFiles,
      };

    case RESET_SEARCH_FILE360:
      return {
        ...state,
        files: state.oldFilesList,
      };

    case SET_CURRENT_SELECTED_CONTACT:
      return {
        ...state,
        currentSelectedContact: payload,
      };

    case UNSET_CURRENT_SELECTED_CONTACT:
      return {
        ...state,
        currentSelectedContact: null,
      };

    default:
      return state;
  }
};

export default files;
