import { CloseOutlined } from "@ant-design/icons";
import { But<PERSON>, Drawer } from "antd";

export const SphereDrawer = ({
  children,
  onClose,
  open,
  title,
  placement = "left",
  mask = false,
  width = 378,
}) => {
  return (
    <>
      <Drawer
        width={width}
        rootStyle={{
          marginLeft: 65,

          boxShadow:
            "0px 0 1px 0 rgba(0, 0, 0, 0.08), 0px 0 0px 0px rgba(0, 0, 0, 0.12), 0px 0 2px 2px rgba(0, 0, 0, 0.05)",
        }}
        title={title}
        mask={mask}
        placement={placement}
        onClose={onClose}
        bodyStyle={{
          paddingLeft: "1rem",
          paddingRight: "1rem",
          paddingTop: 0,
        }}
        open={open}
        closable={false}
        extra={
          <div
            style={{
              display: "flex",
              justifyContent: "flex-end",
            }}
          >
            <Button
              icon={<CloseOutlined />}
              onClick={onClose}
              type="text"
            ></Button>
          </div>
        }
      >
        {children}
      </Drawer>
    </>
  );
};
