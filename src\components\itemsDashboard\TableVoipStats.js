import { SearchOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Card, Empty, Input, Space, Table } from "antd";
import { AvatarChat } from "components/Chat";
import { URL_ENV } from "index";
import { <PERSON><PERSON><PERSON> } from "lucide-react";
import { getName } from "pages/layouts/chat/utils/ConversationUtils";
import ActionsComponent from "pages/tasks/ActionsComponent";
import { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { generateAxios } from "services/axiosInstance";

const TableVoipStats = ({ start, end }) => {
  const { i18n } = useTranslation("common");
  const [data, setData] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);

  const [pageSize, setPageSize] = useState(5);

  const [sorter, setSorter] = useState({ field: "", order: "" });
  const [searchText, setSearchText] = useState("");
  const [openPopover, setOpenPopover] = useState(false);
  const [t] = useTranslation("common");
  const { user: currentUser } = useSelector((state) => state.user);
  const searchInput = useRef(null);
  // useEffect(() => {
  //   if (openPopover) {
  //     setTimeout(() => {
  //       searchInput?.current?.focus();
  //     }, 100);
  //   }
  // }, [openPopover, searchInput]);
  useEffect(() => {
    const fetchCallModule = async () => {
      setLoading(true);
      try {
        const response = await generateAxios(
          `${URL_ENV.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
        ).get(
          `/users-voip-stat?start_date=${start}&end_date=${end}&language=${
            i18n.language
          }&total_calls=1&limit=${pageSize}&page=${
            currentPage || 1
          }&search=${searchText}&sort_field=${sorter?.field}&sort_order=${
            sorter?.order
          }`
        );
        setData(response?.data);
        // setCurrentPage(response?.data?.page?.current_page);
        setTotal(response?.data?.meta?.total);
      } catch (error) {
        console.error(
          "Erreur lors de la récupération des appels sortants :",
          error
        );
      } finally {
        setLoading(false);
      }
    };
    if (currentPage) fetchCallModule();
  }, [
    start,
    end,
    i18n.language,
    currentPage,
    pageSize,
    searchText,
    sorter.field,
    sorter.order,
  ]);
  const handleChangePageSize = (current, size) => {
    setCurrentPage(1);
    setPageSize(size);
  };
  const handleChangePage = (page, pageSize, sorter) => {
    setCurrentPage(page);
    setPageSize(pageSize);
  };

  const handleSearch = (value) => {
    setSearchText(value);
    setCurrentPage(1); // Réinitialiser à la première page
    setOpenPopover(true);
  };
  const handleReset = (clearFilters) => {
    clearFilters();
    setSearchText("");
  };

  const getColumnSearchProps = (dataIndex, handleSearch) => ({
    filterDropdown: ({
      setSelectedKeys,
      selectedKeys,
      confirm,
      clearFilters,
      close,
    }) => {
      setOpenPopover(true);
      return (
        <div style={{ padding: 8 }} onKeyDown={(e) => e.stopPropagation()}>
          <Input
            ref={searchInput}
            placeholder={` ${t("activities.search")} ${dataIndex}`}
            value={selectedKeys[0]}
            onChange={(e) =>
              setSelectedKeys(e.target.value ? [e.target.value] : [])
            }
            onPressEnter={() => {
              handleSearch(selectedKeys[0]);
              confirm();
            }}
            style={{ marginBottom: 8, display: "block" }}
          />
          <Space>
            <Button
              type="primary"
              onClick={() => {
                handleSearch(selectedKeys[0]);
                confirm();
              }}
              icon={<SearchOutlined />}
              size="small"
              style={{ width: 110 }}
            >
              {t("activities.search")}
            </Button>
            <Button
              onClick={() => {
                clearFilters();
                setSearchText("");
                confirm();
              }}
              size="small"
              style={{ width: 90 }}
            >
              {t("activities.reset")}
            </Button>

            <Button type="link" size="small" onClick={() => close()}>
              {t("activities.close")}
            </Button>
          </Space>
        </div>
      );
    },
    onFilterDropdownOpenChange: (visible) => {
      if (visible) {
        setTimeout(() => searchInput.current?.select(), 100);

        // searchInput.current?.focus();
        // searchInput.current?.select();
      }
    },
    filterIcon: (filtered) => (
      <SearchOutlined style={{ color: filtered ? "#1677ff" : undefined }} />
    ),
  });
  const columns =
    Array.isArray(data?.data) &&
    data?.data?.length > 0 &&
    Object.keys(data?.data[0]).map((key) => {
      if (key === "user") {
        return {
          title: t("emailTemplates.fullName"),
          dataIndex: "user",
          key: "user",
          width: 200,
          fixed: "left",
          //   sorter: (a, b) => a.user.label.localeCompare(b.user.label),
          sorter: true,
          ...getColumnSearchProps("user", handleSearch),
          render: (_, { user }) => (
            <div className="flex max-w-[160px] space-x-1 truncate">
              <ActionsComponent
                elementValue={
                  user?._id !== currentUser?.id
                    ? {
                        uuid: user?.uid,
                        extension: user?.extension,
                        id: user?.id,
                        family_id: 4,
                      }
                    : {}
                }
              >
                <AvatarChat
                  // fontSize="0.875rem"
                  url={
                    URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                    user?.avatar
                  }
                  type="user"
                  size={22}
                  height={10}
                  width={10}
                  name={getName(user?.label, "avatar")}
                  hasImage={
                    user?.avatar && user?.avatar !== "/storage/uploads/"
                  }
                />
              </ActionsComponent>{" "}
              <span>{user?.label}</span>
            </div>
          ),
        };
      }
      return {
        title: key.replace(/_/g, " "),
        dataIndex: key,
        key: key,
        sorter: true,
      };
    });
  //   const components = {
  //     header: {
  //       cell: ({ children }) => (
  //         <th style={{ backgroundColor: "#84a3d58a", color: "black" }}>
  //           {children}
  //         </th>
  //       ),
  //     },
  //   };

  const handleTableChange = (pagination, filters, sorter) => {
    if (sorter?.field && sorter?.order)
      setSorter({
        field: sorter?.field,
        order: sorter?.order,
      });
    else
      setSorter({
        field: "",
        order: "",
      });
    setCurrentPage(pagination.current);
    setPageSize(pagination.pageSize);
  };
  return (
    <>
      <Card
        title={
          <div
            style={{
              fontSize: "1.2em",
              color: "rgb(51, 51, 51)",
              fontWeight: "bold",
              fill: "rgb(51, 51, 51)",
            }}
          >
            {data?.name}
          </div>
        }
        styles={{ body: { padding: 0 } }}
      >
        {data?.data?.length > 0 ? (
          <Table
            columns={columns}
            bordered={true}
            className="table-voip-stats"
            loading={loading}
            size="small"
            dataSource={data?.data}
            //   components={components}
            scroll={{
              x: "max-content",
            }}
            pagination={{
              current: currentPage,
              pageSize: pageSize,
              total: total,
              onChange: handleChangePage,
              onShowSizeChange: handleChangePageSize,
              showSizeChanger: true,
              pageSizeOptions: ["5", "10", "20", "50", "100"],
              // hideOnSinglePage:
              //   data && data?.filter((el) => el.id).length < 11 && true,
            }}
            onChange={handleTableChange}
          />
        ) : (
          <Empty />
        )}
      </Card>
    </>
  );
};

export default TableVoipStats;
