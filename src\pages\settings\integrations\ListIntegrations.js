import { Avatar, List, Switch, Typography } from "antd";
import React, { useState } from "react";
import VirtualList from "rc-virtual-list";
import { URL_ENV } from "index";
import { generateAxios } from "services/axiosInstance";
import { toastNotification } from "components/ToastNotification";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import { SET_USER_INFO_CHAT } from "new-redux/constants";

const ListIntegrations = ({
  integrations,
  selectedIntegration,
  setSelectedIntegration,
  setIntegrations,
}) => {
  const [loadCheck, setLoadCheck] = useState(false);
  const [t] = useTranslation("common");
  const currentUser = useSelector((state) => state.chat.currentUser);
  const dispatch = useDispatch();
  const switchCheck = async (e, event, item) => {
    event.stopPropagation();
    setLoadCheck(true);
    try {
      await generateAxios(
        `${URL_ENV?.REACT_APP_OAUTH_CHAT_API}/${process.env.REACT_APP_SUFFIX_API}`
      ).put(
        `/integrations/update-is-active/${item?._id}`,
        { is_active: e ? 1 : 0 },
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      );
      selectedIntegration._id === item?._id &&
        setSelectedIntegration((prev) => ({ ...prev, is_active: e ? 1 : 0 }));
      setIntegrations((prev) =>
        prev.map((el) =>
          el._id === item?._id ? { ...el, is_active: e ? 1 : 0 } : el
        )
      );
      dispatch({
        type: SET_USER_INFO_CHAT,
        payload: {
          ...currentUser,
          integrations: integrations
            .map((el) =>
              el._id === item?._id ? { ...el, is_active: e ? 1 : 0 } : el
            )
            .filter((el) => el.is_active === 1),
        },
      });
      setLoadCheck(false);
      toastNotification(
        "success",
        item.app_name + t("toasts.edit"),
        "topRight"
      );
    } catch (err) {
      setLoadCheck(false);
      console.log(err);
      toastNotification("error", err?.response?.data?.error, "topRight");
    }
  };
  return (
    <div className=" h-[calc(100vh-165px)] overflow-auto">
      {/* <Divider orientation="left" plain>
Mes réunions
</Divider> */}

      <List>
        <VirtualList
          data={integrations}
          // height={windowHeight - 110}
          itemKey="email"
        >
          {(el) => (
            <List.Item
              key={el._id}
              className=" border-none"
              onClick={(e) => {
                setSelectedIntegration(el);
                // e.preventDefault();
              }}
              style={{ borderBlockEnd: "none", padding: "4px" }}
            >
              <div
                className={` flex w-full flex-col    ${
                  selectedIntegration?._id === el?._id
                    ? "bg-blue-100"
                    : "hover:bg-blue-50"
                }  cursor-pointer rounded-md  py-3  pl-2`}
                // onMouseEnter={() => setShowCopy(true)}
                // onMouseLeave={() => setShowCopy(false)}
              >
                <div className="flex w-full  items-center  justify-between pr-1.5">
                  <div className=" flex items-center gap-1">
                    <Avatar size={20} src={el.logo} />
                    <Typography.Title
                      level={5}
                      style={{
                        margin: 0,
                      }}
                    >
                      {el.app_name}
                    </Typography.Title>
                  </div>
                  <Switch
                    size="small"
                    checked={el?.is_active == 1}
                    loading={loadCheck}
                    onChange={(e, event) => switchCheck(e, event, el)}
                  />
                </div>
                {/* 
              <Switch
                size="small"
                checked={el.default}
                onChange={(e) =>
                  setListSignature((prev) =>
                    prev.map((li) =>
                      li.id === el.id ? { ...li, default: e } : li
                    )
                  )
                }
              /> */}
              </div>
            </List.Item>
          )}
        </VirtualList>
        {/* {loading ? <Spin /> : ""} */}
      </List>

      {/* </Flex> */}
    </div>
  );
};

export default ListIntegrations;
