import { Fragment, useEffect, useState } from "react";
import { Listbox, Transition } from "@headlessui/react";
import { CheckIcon, ChevronUpDownIcon } from "@heroicons/react/20/solid";

// const people = [
//   { id: 1, name: '<PERSON>' },
//   { id: 2, name: '<PERSON><PERSON>' },
//   { id: 3, name: '<PERSON>' },
//   { id: 4, name: '<PERSON>' },
//   { id: 5, name: '<PERSON>' },
//   { id: 6, name: '<PERSON><PERSON>' },
//   { id: 7, name: '<PERSON>' },
//   { id: 8, name: '<PERSON>' },
//   { id: 9, name: '<PERSON><PERSON><PERSON>' },
//   { id: 10, name: '<PERSON>' },
// ]

function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}

export default function Select({
  defaultValue,
  options,
  border,
  changeOption,
  index,
}) {
  const [selected, setSelected] = useState(options[0]);

  return (
    <Listbox value={selected} onChange={setSelected}>
      {({ open }) => (
        <>
          <div className={`relative ${border ? "mt-0" : "mt-1"}`}>
            <Listbox.Button
              name="option"
              className={`${
                border
                  ? "border w-32 hover:bg-gray-100 focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500"
                  : "rounded-md  w-40 border-0"
              }  relative  cursor-pointer  bg-white py-2 pl-3 pr-10 text-left   sm:text-sm`}
            >
              <span className="block truncate">{selected.name}</span>
              <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                <ChevronUpDownIcon
                  className="h-5 w-5 text-gray-400"
                  aria-hidden="true"
                />
              </span>
            </Listbox.Button>

            <Transition
              show={open}
              as={Fragment}
              leave="transition ease-in duration-100"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <Listbox.Options
                style={{ padding: 0 }}
                className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"
              >
                {options.map((option) => (
                  <>
                    <Listbox.Option
                      key={option.id}
                      className={({ active }) =>
                        classNames(
                          active ? "text-white bg-indigo-600" : "text-gray-900",
                          "relative cursor-default select-none py-2 pl-8 pr-4"
                        )
                      }
                      value={option}
                      // onClick={ ()=>changeOption((prev)=>(prev.map((el,i)=>index==i?{phone:el.phone,option:option.name}:el)))}
                    >
                      {({ selected, active }) => (
                        <>
                          <span
                            className={classNames(
                              selected ? "font-semibold" : "font-normal",
                              "block truncate"
                            )}
                          >
                            {option.name}
                          </span>

                          {selected ? (
                            <span
                              className={classNames(
                                active ? "text-white" : "text-indigo-600",
                                "absolute inset-y-0 left-0 flex items-center pl-1.5 "
                              )}
                            >
                              <CheckIcon
                                className="h-5 w-5"
                                aria-hidden="true"
                              />
                            </span>
                          ) : null}
                        </>
                      )}
                    </Listbox.Option>
                  </>
                ))}
              </Listbox.Options>
            </Transition>
          </div>
        </>
      )}
    </Listbox>
  );
}
