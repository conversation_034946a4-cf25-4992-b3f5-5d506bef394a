import React, { useEffect, useLayoutEffect, useState } from "react";
import { Avatar, Button, Col, Drawer, Row, Skeleton, Tag, Tooltip } from "antd";
import { extractTitle } from "./utils";
import { useSelector } from "react-redux";
import moment from "moment";
import { AvatarChat } from "components/Chat";
import { useTranslation } from "react-i18next";
import { getName } from "pages/layouts/chat/utils/ConversationUtils";
import { URL_ENV } from "index";
import MainService from "services/main.service";
import { useNavigate } from "react-router-dom";
import { generateUrlToView360 } from "pages/voip/helpers/helpersFunc";
import { useDispatch } from "react-redux";
import ModuleElementDetails from "pages/tasks/ModuleElementDetails";
import getContactDataAndDispatch from "pages/clients&users/helpers/getContactDataAndDispatch";

const DescriptionItem = ({ title, content }) => (
  <div className="flex items-center space-x-2 border-b border-gray-200 py-2">
    <p className="font-semibold text-gray-600">{title}</p>
    <p className="text-gray-800">{content}</p>
  </div>
);

function NoteInformations({
  openDrawer,
  setOpenDrawer,
  note,
  setSelectedNote,
}) {
  const { t } = useTranslation("common");

  const { families } = useSelector((state) => state?.families);

  const navigate = useNavigate();

  const [noteFamily, setNoteFamily] = useState(null);
  const [noteElement, setNoteElement] = useState(null);
  const [loading, setLoading] = useState(true);

  const dispatch = useDispatch();

  const [elementDetails, setElementDetails] = useState({
    id: null,
    module: null,
  });

  const [openElementDetails, setOpenElementDetails] = useState(false);

  const handleClickOnElement = (record) => {
    //console.log("record", record);
    getContactDataAndDispatch(
      record?.family_data?.family_id,
      record?.element_data?.label_data,
      { key: record?.element_data?.element_id },
      record,
      dispatch,
      null,
      () => {}
    );
    setElementDetails((prev) => ({
      ...prev,
      id: record?.element_data?.element_id,
      module: record?.family_data?.family_id,
    }));
    setOpenElementDetails(true);
  };

  const getFamilyElementsById = async (id) => {
    MainService.getFamilyElement(id)
      .then((response) => {
        const elements = response?.data?.data;

        //find the element note.element_id in elements and set noteElement
        let el = elements.find((f) => f.id == note?.element_id);
        setNoteElement(el);
      })
      .catch((error) => {
        console.error("error", error);
        setNoteElement(null);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(async () => {
    console.log("note?.family_id", note?.family_id);
    const fam = families.find((f) => f.id == note?.family_id);
    setNoteFamily(fam);
    await getFamilyElementsById(note?.family_id);
  }, [note]);

  const dateConfig = useSelector(
    (state) => state?.user?.user?.location?.date_format
  );
  const dateTimeConfig = useSelector(
    (state) => state?.user?.user?.location?.time_format
  );
  const dateTimeZoneConfig = useSelector(
    (state) => state?.user?.user?.location?.default_timezone
  );

  const globalDateFormat = (date, time) => {
    return `${date} ${time}`;
  };

  const users = useSelector((state) => state?.chat?.userList);
  const currentUser = useSelector((state) => state?.user?.user);

  const returnSpeceficSharedWithNoteFullUsers = (shared_with) => {
    const usr = { name: currentUser.label, image: currentUser.avatar };
    let sharedUsers = shared_with.map((shared) =>
      users.find((user) => user.uuid === shared.uuid)
    );

    if (shared_with.find((shared) => shared._id === currentUser.id)) {
      sharedUsers.push(usr);
    }

    return sharedUsers;
  };

  const renderDate = (date) => {
    return (
      <time className="text-gray-500">
        {moment
          .tz(date, dateTimeZoneConfig)
          .format(globalDateFormat(dateConfig, dateTimeConfig))}
      </time>
    );
  };

  const onCloseDrawer = () => {
    setOpenDrawer(false);
    setSelectedNote(null);
  };

  return (
    <>
      <Drawer
        title={
          <h3 className="text-md font-semibold text-gray-800">
            Note Information
          </h3>
        }
        open={openDrawer}
        onClose={onCloseDrawer}
        placement="right"
        bodyStyle={{ padding: "20px" }}
        // loading={loading}
      >
        {loading ? (
          <Skeleton active />
        ) : (
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <DescriptionItem
                title={t("selfNotes.title")}
                content={extractTitle(note?.content)}
              />
            </Col>
            <Col span={24}>
              <DescriptionItem
                title={t("selfNotes.owner")}
                content={
                  note?.user_data && (
                    <Tooltip
                      title={note?.user_data?.label_data}
                      placement="top"
                    >
                      <div className="flex items-center space-x-2">
                        <AvatarChat
                          url={
                            URL_ENV?.REACT_APP_BASE_URL +
                            URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                            note?.user_data?.avatar
                          }
                          type="user"
                          size={32}
                          name={getName(note?.dataUser?.label_data, "avatar")}
                          hasImage={note?.user_data?.avatar?.length > 5}
                        />
                        <span className="text-gray-700">
                          {getName(note?.user_data?.label_data, "name")}
                        </span>
                      </div>
                    </Tooltip>
                  )
                }
              />
            </Col>
            <Col span={24}>
              <DescriptionItem
                title={t("selfNotes.createdAt")}
                content={renderDate(note?.created_at)}
              />
            </Col>
            <Col span={24}>
              <DescriptionItem
                title={t("selfNotes.lastUpdateAt")}
                content={renderDate(note?.updated_at)}
              />
            </Col>
            <Col span={24}>
              <DescriptionItem
                title={t("selfNotes.sharedWith")}
                content={
                  <Avatar.Group
                    maxCount={4}
                    size="small"
                    className="mt-0.5 flex space-x-0.5"
                  >
                    {note?.shared_with_users_data?.length > 0 ? (
                      note.shared_with_users_data?.map((user, index) => (
                        <Tooltip
                          title={getName(user?.label_data, "name")}
                          key={index}
                          placement="top"
                        >
                          <AvatarChat
                            url={
                              URL_ENV?.REACT_APP_BASE_URL +
                              URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                              user?.avatar
                            }
                            type="user"
                            size={32}
                            name={getName(user?.label_data, "avatar")}
                            hasImage={user?.avatar?.length > 5}
                          />
                        </Tooltip>
                      ))
                    ) : (
                      <span className="text-gray-500">
                        {t("selfNotes.notShared")}
                      </span>
                    )}
                  </Avatar.Group>
                }
              />
            </Col>
            <Col span={24}>
              <DescriptionItem
                title={t("tasks.assignment")}
                content={
                  note?.family_id ? (
                    // <Tooltip title={noteFamily?.label} placement="top">
                    <Tag
                      color="magenta"
                      className="module-tag-text cursor-pointer"
                      // onClick={() => {
                      //   console.log("noteFamily", noteFamily);
                      //   handleClickOnElement(note?.family_id, noteFamily?.label);
                      // }}
                      onClick={() => {
                        // navigate(
                        //   generateUrlToView360(
                        //     Number(note?.family_id),
                        //     note?.element_id,
                        //     "v2"
                        //   )
                        // );
                        handleClickOnElement(note);
                      }}
                    >
                      {noteFamily?.label}/{noteElement?.label_data}
                    </Tag>
                  ) : (
                    // </Tooltip>
                    <>
                      <Tag
                        color="gray"
                        className="module-tag-text"
                        // onClick={() => {
                        //   console.log("noteFamily", noteFamily);
                        //   handleClickOnElement(note?.family_id, noteFamily?.label);
                        // }}
                      >
                        {t("selfNotes.notAssociated")}
                      </Tag>
                    </>
                  )
                }
              />
            </Col>
            {/* <Col span={24}>
            <DescriptionItem
              title="Element"
              content={
                note?.element_id && note?.family_id ? (
                  noteElement?.label_data
                ) : (
                  <>
                    <span className="text-gray-500">Non défini</span>
                  </>
                )
              }
            />
          </Col> */}
          </Row>
        )}
      </Drawer>
      <ModuleElementDetails
        key={elementDetails?.id}
        openElementDetails={openElementDetails}
        setOpenElementDetails={setOpenElementDetails}
        setElementDetails={setElementDetails}
        elementDetails={elementDetails}
      />
    </>
  );
}

export default NoteInformations;
