import React from "react";
import {
  AppstoreOutlined,
  GlobalOutlined,
  MailOutlined,
  SettingOutlined,
  ToolOutlined,
} from "@ant-design/icons";
import { Menu } from "antd";
import { SquaresPlusIcon, BanknotesIcon } from "@heroicons/react/24/outline";
import { HiOutlineUsers, HiOutlineBuildingOffice } from "react-icons/hi2";
import { MdOutlineHistory } from "react-icons/md";
import { useSelector } from "react-redux";
import { NavLink } from "react-router-dom";
import Contacts from "../contact&companies/Contacts";

/**
 *
 * @param {*} location String: Subdirectory part from the url link.
 *
 * @returns Secondary sidemenu. Links will be displayed according to the Subdirectory (location prop from App.js).
 */

function getItem(label, key, icon, children, type) {
  return {
    key,
    icon,
    children,
    label,
    type,
  };
}

const contacts = [
  getItem(
    <NavLink to="/contacts">Contacts</NavLink>,
    "contacts",
    <HiOutlineUsers className="h-5 w-5" />
  ),
  getItem(
    <NavLink to="/companies">Companies</NavLink>,
    "companies",
    <HiOutlineBuildingOffice className="h-5 w-5" />
  ),
];

const voip = [
  getItem(
    <NavLink to="/voip">Journal</NavLink>,
    "log",
    <MdOutlineHistory className="h-5 w-5" />
  ),
];

const NavSider = ({ location }) => {
  const { families, isLoading } = useSelector((state) => state.families);

  // Settings secondary menu items.
  const settingsMenuItems = [
    getItem(
      <NavLink to="/settings/general/departments">General</NavLink>,
      "general",
      <ToolOutlined style={{ fontSize: "16px" }} />
    ),
    getItem(
      "Fields",
      "fields",
      <SquaresPlusIcon className="h-5 w-5" />,
      families &&
        families.map((element) =>
          getItem(
            <NavLink to={`/settings/fields/${element?.label}`}>
              {element?.label}
            </NavLink>,
            element?.id,
            null
          )
        )
    ),
    getItem("Deals", "deals", <BanknotesIcon className="h-5 w-5" />),

    getItem(
      <NavLink to="/settings/localisation">Localisation</NavLink>,
      "localisation",
      <GlobalOutlined style={{ fontSize: "16px" }} />
    ),
  ];

  const onClick = (e) => {
   // console.log("click ", e);
  };

  // console.log("ffff", families?.data);

  return (
    <Menu
      onClick={onClick}
      style={{
        width: 210,
      }}
      defaultSelectedKeys={["general", "contacts"]}
      defaultOpenKeys={["general", "contacts"]}
      mode="inline"
      items={
        location == "settings"
          ? settingsMenuItems
          : location == "contacts"
          ? contacts
          : location == "companies"
          ? contacts
          : location == "voip"
          ? voip
          : ""
      }
      // expandIcon={
      //   isLoading === true && <ArrowPathIcon className="h-5 w-5 animation-spin text-blue-500" />
      // }
    />
  );
};
export default NavSider;
