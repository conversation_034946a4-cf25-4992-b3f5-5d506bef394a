import { useMemo } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { <PERSON><PERSON>, <PERSON><PERSON>, Result } from "antd";
import { useTranslation } from "react-i18next";
import BoxInterface from "./BoxInterface";
import MainService from "services/main.service";

const MailRouting = () => {
  //
  const [t] = useTranslation("common");
  const navigate = useNavigate();
  const location = useLocation();
  //
  const dataAccounts = useSelector((state) => state.mailReducer.dataAccounts);
  const invalidConfigMail = useSelector(
    (state) => state.menu?.invalidConfigMail
  );
  const user = useSelector((state) => state.user.user);
  //
  const usedAccount = useMemo(
    () =>
      dataAccounts?.length
        ? dataAccounts.find((account) => account.selected) || dataAccounts?.[0]
        : null,
    [dataAccounts]
  );
  //
  const labelInfo = useMemo(() => {
    if (!usedAccount) return {};
    const labelId = location.pathname.split("/").at(-1);
    const labelsList = usedAccount.labels;
    return labelsList?.find((label) => label.id === parseInt(labelId)) || {};
  }, [location.pathname, usedAccount]);
  //
  // console.log({ location, usedAccount, labelInfo });
  //
  return !user?.accounts_email?.length ? (
    <div className="flex h-full w-full items-center justify-center">
      <Result
        status="error"
        title={t("mailing.noEmailConfigured.title")}
        subTitle={t("mailing.noEmailConfigured.subTitle")}
        extra={[
          <Button
            type="primary"
            key="configure"
            onClick={() => navigate("/settings/emailAccounts")}
          >
            {t("mailing.noEmailConfigured.cta")}
          </Button>,
        ]}
      />
    </div>
  ) : invalidConfigMail.length > 0 &&
    invalidConfigMail.includes(Number(usedAccount?.value)) ? (
    <Alert
      message={t("mailing.emailConfig.errorTitle")}
      description={t("mailing.emailConfig.errorDesc")}
      type="warning"
      showIcon
    />
  ) : (
    <BoxInterface
      api={MainService.getInboxEmails}
      path={location.pathname}
      usedAccount={usedAccount}
      label={labelInfo}
    />
  );
};

export default MailRouting;
