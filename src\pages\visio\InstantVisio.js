import React, { useEffect, useState } from 'react'
import { Ty<PERSON><PERSON>, Button, Checkbox, Form, Input, Spin, message } from 'antd'
import { useDispatch, useSelector } from "react-redux";
import { FiCopy } from 'react-icons/fi';
import { useTranslation } from 'react-i18next'



const { Paragraph } = Typography;


const InstantVisio = ({ setSecretCodeVisio, visioToUpdate }) => {

    const [t] = useTranslation('common')

    const [form] = Form.useForm()

    const newVisioReduxUrl = useSelector((state) => state.visio.new.meeting_url);
    const newVisioId = useSelector((state) => state.visio.new.id);
    const newVisioReduxPwd = useSelector((state) => state.visio.new.password);
    const newVisioReduxTitle = useSelector((state) => state.visio.new.title);
    const newVisioReduxLoading = useSelector((state) => state.visio.isLoading)

    const oneVisioId = useSelector((state) => state.visio.oneVisio.id)
    const oneVisioPwd = useSelector((state) => state.visio.oneVisio.password)
    const oneVisioTitle = useSelector((state) => state.visio.oneVisio.title)

    const [link, setLink] = useState("")
    const [pwd, setPwd] = useState("")



    useEffect(() => {
        visioToUpdate == null ?
            form.setFieldsValue({
                link: 'https://sphere-dev.comunikcrm.info/visio/' + newVisioId,
                pwd: newVisioReduxPwd,
                title: newVisioReduxTitle,
            })

            :

            form.setFieldsValue({
                link: 'https://sphere-dev.comunikcrm.info/visio/' + oneVisioId,
                pwd: oneVisioPwd,
                title: oneVisioTitle,
            })
    }

        , [visioToUpdate, form, newVisioId, newVisioReduxUrl, newVisioReduxPwd, newVisioReduxTitle, oneVisioId, oneVisioPwd, oneVisioTitle])


    // useEffect(() => {
    //     form.setFieldsValue({
    //         link: "https://visio.unified.test",
    //         pwd: "123456",

    //         //.replace(/\s/g, ''),
    //     })
    // }, [])


    const onFinish = (values) => {
        console.log('Success:', values);
    };
    const onFinishFailed = (errorInfo) => {
        console.log('Failed:', errorInfo);
    };

    const copyLink = () => {
        navigator.clipboard.writeText(form.getFieldValue().link)
        message.success('Link copied');
    };

    const copyPwd = () => {
        navigator.clipboard.writeText(form.getFieldValue().pwd)
        message.success('Secret code copied');
    };

    const copyInfo = () => {
        navigator.clipboard.writeText(`Link: ${form.getFieldValue().link}, Secret Code: ${form.getFieldValue().pwd}`)
        message.success('Information copied');
    };


    return (
        <div>

            <div>
                <Spin
                    spinning={newVisioReduxLoading}
                    size="medium">
                    <Form
                        name="basic"
                        form={form}
                        onFinish={onFinish}
                        onFinishFailed={onFinishFailed}
                        layout='vertical'
                    //size='small'
                    //autoComplete="off"

                    >
                        <Form.Item>
                            <Button className='float-right' onClick={copyInfo}>Copy Information</Button>


                        </Form.Item>



                        <Form.Item
                            label="Link"
                            name="link"

                        >
                            {/* <Paragraph copyable>{form.getFieldValue().link}</Paragraph> */}
                            <Input readOnly suffix={<FiCopy style={{ cursor: 'pointer' }} className="w-4 h-4" onClick={copyLink} />}
                            />
                        </Form.Item>

                        <Form.Item
                            label="Secret code"
                            name="pwd"
                        // rules={[
                        //     {
                        //         required: true,
                        //         message: 'Please input your password!',
                        //     },
                        // ]}
                        >
                            {/* <Paragraph copyable>{form.getFieldValue().pwd}</Paragraph> */}
                            <Input readOnly suffix={<FiCopy style={{ cursor: 'pointer' }} className="w-4 h-4" onClick={copyPwd} />} />
                        </Form.Item>
                    </Form>

                </Spin>
            </div>

        </div>
    )
}

export default InstantVisio
