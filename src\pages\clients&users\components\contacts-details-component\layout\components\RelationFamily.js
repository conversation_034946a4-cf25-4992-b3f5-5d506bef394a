import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import {
  addLastIdToViewSphere,
  setActiveTab360,
  setNewInteraction,
  setOpenView360InDrawer,
} from "../../../../../../new-redux/actions/vue360.actions/vue360";
import {
  AutoComplete,
  Avatar,
  Button,
  Card,
  Collapse,
  Form,
  Popconfirm,
  Popover,
  Rate,
  Skeleton,
  Space,
  Tag,
  Tooltip,
  Typography,
} from "antd";
import {
  ArrowRightOutlined,
  CaretRightOutlined,
  CheckOutlined,
  CloseCircleFilled,
  CloseOutlined,
  LinkOutlined,
  LoadingOutlined,
  PhoneOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import {
  deleteAssociation,
  getElementByFamily,
  getGeneralInfo360,
  getKPI,
  postAssociate,
} from "../../../../services/services";
import { toastNotification } from "../../../../../../components/ToastNotification";
import { useTranslation } from "react-i18next";
import { FiCopy } from "react-icons/fi";
import { useWindowSize } from "../../../WindowSize";
import FormCreate from "../../../FormCreate";
import ProfileDetails from "../../ProfileDetails";
import TableInterface from "../../../../TableInterface";
import { getFirst2Chart } from "../../../../../voip/helpers/helpersFunc";
import useActionCall from "../../../../../voip/helpers/ActionCall";
import { useSelector } from "react-redux";
import { roles } from "../../../../../../utils/role";
import "../../../../index.css";
import { URL_ENV } from "index";
import { debounce } from "lodash";
import { getFamilyNameById } from "pages/clients&users/FamilyRouting";
import { SET_CONTACT_INFO_FROM_DRAWER } from "new-redux/constants";
import { pathViewSphere } from "pages/components/DetailsProfile/ViewSphere2";
import { useNavigate } from "react-router-dom";

const { Meta } = Card;
const { Text, Link } = Typography;

const RelationFamily = ({
  family_id,
  id,
  source,
  addTab,
  isElementUpdate,
  setAddRemoveAssoc,
  catchChange,
  elementID,
  handleMinimized = () => {},
}) => {
  //
  const windowSize = useWindowSize();
  const [t] = useTranslation("common");
  const call = useActionCall();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const user = useSelector(({ user }) => user.user);
  //
  const [generalInfo, setGeneralInfo] = useState(null);
  const [generalInfoIsLoading, setGeneralInfoIsLoading] = useState(false);
  const [kpi, setKpi] = useState(null);
  const [kpiIsLoading, setKpiIsLoading] = useState(false);
  const [openFormCreate, setOpenFormCreate] = useState(false);
  const [openFamilyId, setOpenFamilyId] = useState(null);
  const [isUpdate, setIsUpdate] = useState(false);
  const [activeKeyKpi, setActiveKeyKpi] = useState(["general_info"]);
  const [isCreateOrAssociate, setIsCreateOrAssociate] = useState(false);
  //
  //
  const fetchGeneralInfo = useCallback(async () => {
    if (!id) return;
    try {
      !generalInfo && setGeneralInfoIsLoading(true);
      const {
        data: { field_value },
      } = await getGeneralInfo360("general_info", id);
      setGeneralInfo(field_value);
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? err.message : err);
    } finally {
      setGeneralInfoIsLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id, isUpdate, isElementUpdate, t]);

  useEffect(() => {
    fetchGeneralInfo();
  }, [fetchGeneralInfo]);
  //
  const fetchKPI = useCallback(async () => {
    if (!family_id || !id) return;
    try {
      setKpiIsLoading(true);
      const { data } = await getKPI(family_id, id);
      setKpi(
        data?.map((kpi) => ({
          ...kpi,
          children: kpi?.child?.length
            ? kpi?.child?.slice(0, 3)?.map((child) => ({
                label: child?.label_data,
                id: child?.id,
                pipeline: child?.pipeline_label,
                stage: child?.stage_label,
              }))
            : [],
        }))
      );
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? err.message : err);
    } finally {
      setKpiIsLoading(false);
      setIsCreateOrAssociate(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [family_id, id, t, isUpdate, isCreateOrAssociate, catchChange]);

  useEffect(() => {
    fetchKPI();
  }, [fetchKPI]);
  //
  const removeAssociation = async (associationId, familyId) => {
    return new Promise((resolve, reject) => {
      deleteAssociation(id, { id: associationId })
        .then(() => {
          setKpi((prevKPIs) =>
            prevKPIs.map((kpi) => {
              if (kpi.family_id !== familyId) return kpi;
              const { number, children, child, ...rest } = kpi;
              return {
                ...rest,
                number: number - 1,
                children: children.filter((c) => c.id !== associationId),
                child: child.filter((c) => c.id !== associationId),
              };
            })
          );
          setAddRemoveAssoc((prev) => !prev);
          resolve();
        })
        .catch((err) => {
          err?.response?.status !== 401 &&
            toastNotification("error", t("toasts.somethingWrong"), "topRight");
          reject();
        });
    });
  };
  //
  const copyIcon = (text) => (
    <Typography.Paragraph
      copyable={{
        text: text,
        icon: [
          <FiCopy
            style={{
              color: "rgb(22, 119, 255)",
              marginTop: "2px",
              fontSize: "15px",
            }}
          />,
        ],
      }}
    />
  );
  //
  const generalInfoItems = useMemo(() => {
    const result = [];
    generalInfo &&
      result.push({
        key: "general_info",
        label: (
          <p className="truncate font-semibold">{t("companies.infoGeneral")}</p>
        ),
        children: (
          <>
            <div className="space-y-2">
              {Object.entries(generalInfo).map(([key, { type, value }]) => (
                <div className="flex flex-row space-x-2" key={key}>
                  <span
                    // type="secondary"
                    style={{
                      width: `${(key?.length + 1) * 10}`,
                      color: "rgba(0, 0, 0, 0.45)",
                    }}
                  >
                    {key}:
                  </span>
                  {type === "checkbox" ||
                  type === "select" ||
                  type === "radio" ||
                  type === "multiselect" ||
                  type === "autocomplete" ? (
                    <Space size={[1, 2]} wrap>
                      {Array.isArray(value) ? (
                        value?.map((item) => <Tag>{item}</Tag>)
                      ) : (
                        <Tag>{value}</Tag>
                      )}
                    </Space>
                  ) : type === "phone" ? (
                    <Text>{`(${value?.[0]}) ${value?.[1]}`}</Text>
                  ) : type === "image" ? (
                    <Avatar
                      size={25}
                      src={`${
                        URL_ENV?.REACT_APP_BASE_URL +
                        URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                      }${value}`}
                    />
                  ) : type === "file" ? (
                    <div className="flex flex-col space-y-1">
                      {value?.map((file, i) => (
                        <a
                          key={i}
                          className="truncate"
                          style={{
                            width: `14rem`,
                          }}
                          href={`${
                            URL_ENV?.REACT_APP_BASE_URL +
                            URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                          }${file}`}
                          target="_blank"
                          rel="noreferrer"
                        >
                          {file}
                        </a>
                      ))}
                    </div>
                  ) : type === "rate" ? (
                    <Rate allowHalf disabled defaultValue={Number(value)} />
                  ) : (
                    <Text>{value}</Text>
                  )}
                  {type === "phone" ? (
                    <Space size={4}>
                      {copyIcon(
                        `${value?.[0]?.replace("+", "00")}${value?.[1]}`
                      )}
                      <Tooltip title={"Call"}>
                        <Button
                          onClick={() =>
                            call(
                              `${value?.[0]?.replace("+", "00")}${value?.[1]}`,
                              id,
                              family_id
                            )
                          }
                          icon={
                            <PhoneOutlined
                              rotate={100}
                              style={{ fontSize: 15, marginBottom: 5 }}
                            />
                          }
                          type="link"
                          size="small"
                        />
                      </Tooltip>
                    </Space>
                  ) : type === "email" ? (
                    copyIcon(`${value}`)
                  ) : (
                    ""
                  )}
                </div>
              ))}
            </div>
            {Object.keys(generalInfo)?.length ? (
              <div className="mt-2 flex justify-end">
                <Link
                  // disabled={source === "webPhone"}
                  // onClick={() => dispatch(setActiveTab360(2))}
                  onClick={() => handleMinimized()}
                >
                  {t("contacts.viewMore")} <ArrowRightOutlined />
                </Link>
              </div>
            ) : (
              ""
            )}
          </>
        ),
        extra: !Object.keys(generalInfo)?.length ? (
          <Link onClick={() => dispatch(setActiveTab360(2))}>
            Details <ArrowRightOutlined />
          </Link>
        ) : (
          ""
        ),
        collapsible: !Object.keys(generalInfo)?.length && "disabled",
      });
    // }
    return result;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [generalInfo, source]);
  //
  const kpiItems = useMemo(() => {
    const panelStyle = {
      marginBottom: 0,
      background: "rgb(241 245 249 )",
    };
    const result = [...generalInfoItems];
    if (!kpi) return result;
    else {
      kpi?.forEach((item) => {
        const { family_id: familyId, label, number } = item;
        result.push({
          key: label,
          label: (
            <Space>
              <p className="truncate font-semibold">{label}</p>
              {number !== 0 && <p>({number})</p>}
            </Space>
          ),
          children:
            number > 0 ? (
              <div className="relation-card space-y-1">
                {item?.children?.map(({ label, pipeline, stage, id }) => (
                  <Tooltip title={t("voip.view360")}>
                    <Card
                      hoverable
                      // hoverable={
                      //   (familyId === 4 && !roles?.includes(user?.role)) ||
                      //   source === "webPhone"
                      //     ? false
                      //     : true
                      // }
                      style={{
                        width: "100%",
                        cursor: "pointer",
                      }}
                      onClick={
                        () => navigate(pathViewSphere(familyId, id))

                        // (familyId === 4 && !roles?.includes(user?.role)) ||
                        // source === "webPhone"
                        //   ? ""
                        //   : addTab
                        //   ? addTab(
                        //       label,
                        //       <ProfileDetails
                        //         familyId={familyId}
                        //         contactId={id}
                        //         numberOfColumns={3}
                        //         contactName={label}
                        //         editable={true}
                        //         isUpdate={isUpdate}
                        //         setIsUpdate={setIsUpdate}
                        //       />
                        //     )
                        //   : ""
                      }
                    >
                      <Meta
                        avatar={
                          <Avatar
                            size={35}
                            className="font-semibold"
                            style={{
                              backgroundColor: "rgb(219, 234, 254)",
                              color: "rgb(30, 64, 175)",
                            }}
                          >
                            {getFirst2Chart(label)}
                          </Avatar>
                        }
                        title={
                          <div
                            className="flex w-full flex-row justify-between"
                            // onClick={(event) => event.stopPropagation()}
                          >
                            <p className="flex truncate">{label}</p>
                            {source !== "webPhone" && (
                              <Popconfirm
                                title={t("voip.removeAssociation")}
                                description={t("contacts.deleteAssociation")}
                                onConfirm={(event) => {
                                  event.stopPropagation();
                                  removeAssociation(id, familyId);
                                }}
                                onCancel={(event) => event.stopPropagation()}
                                okText={t("voip.yes")}
                                cancelText={t("voip.cancel")}
                                onClick={(event) => event.stopPropagation()}
                              >
                                <Button
                                  onClick={(event) => {
                                    event.stopPropagation();
                                    // removeAssociation(id, familyId);
                                  }}
                                  size="small"
                                  type="text"
                                  shape="circle"
                                  icon={<CloseOutlined />}
                                />
                              </Popconfirm>
                            )}
                          </div>
                        }
                        description={`${pipeline ? pipeline + " - " : ""} ${
                          stage ? stage : ""
                        }`}
                      />
                    </Card>
                  </Tooltip>
                ))}
                {/* <div className="flex items-center justify-end">
                  {familyId === 4 && !roles.includes(user?.role) ? (
                    ""
                  ) : (
                    <Typography.Link
                      disabled={source === "webPhone"}
                      onClick={() =>
                        addTab &&
                        addTab(
                          label,
                          <TableInterface
                            familyId={familyId}
                            relation_id={id}
                          />
                        )
                      }
                    >
                      View more <ArrowRightOutlined />
                    </Typography.Link>
                  )}
                </div> */}
              </div>
            ) : (
              ""
            ),
          collapsible: number === 0 ? "disabled" : "",
          extra:
            familyId === 4 && !roles.includes(user?.role) ? null : source ===
              "webPhone" ? null : (
              <Space size={4}>
                <PopoverAssign
                  t={t}
                  family_id={familyId}
                  element_id={id}
                  setData={setKpi}
                  setAddRemoveAssoc={setAddRemoveAssoc}
                >
                  <Tooltip title={`${t("voip.associate")}`}>
                    <Button
                      onClick={(event) => event.stopPropagation()}
                      type="text"
                      shape="circle"
                      size="small"
                      icon={<LinkOutlined style={{ fontSize: 15 }} />}
                    />
                  </Tooltip>
                </PopoverAssign>

                <Tooltip
                  title={`${t("voip.add")} ${getFamilyNameById(t, familyId)}`}
                >
                  <Button
                    onClick={(event) => {
                      event.stopPropagation();
                      setOpenFamilyId(familyId);
                      setOpenFormCreate(true);
                    }}
                    icon={<PlusOutlined style={{ fontSize: 14 }} />}
                    type="text"
                    shape="circle"
                    size="small"
                  />
                </Tooltip>
              </Space>
            ),
          style: panelStyle,
        });
      });
      return result;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [kpi, generalInfoItems, source]);

  //
  return (
    <div
      className="relation-family w-full"
      style={{
        overflow: "auto",
        height: windowSize?.height - (source === "voip" ? 330 : 380),
        width: "360px",
        padding: "0 16px",
      }}
    >
      <Skeleton active loading={kpiIsLoading || generalInfoIsLoading}>
        <Collapse
          key={"kpi"}
          activeKey={activeKeyKpi}
          onChange={setActiveKeyKpi}
          bordered={false}
          items={kpiItems}
          expandIcon={({ isActive }) => (
            <CaretRightOutlined rotate={isActive ? 90 : 0} />
          )}
          style={{ backgroundColor: "rgb(241 245 249 )" }}
        />
      </Skeleton>
      <FormCreate
        open={openFormCreate}
        setOpen={setOpenFormCreate}
        familyId={openFamilyId}
        idRelation={id}
        setCatchChange={setIsCreateOrAssociate}
      />
    </div>
  );
};

export const PopoverAssign = ({
  children,
  family_id,
  element_id,
  setData,
  t,
  setAddRemoveAssoc,
  placement = "bottomRight",
}) => {
  //
  const [form] = Form.useForm();
  const autoCompleteRef = useRef(null);
  //
  const [options, setOptions] = useState([]);
  const [loadingOptions, setLoadingOptions] = useState(false);
  // const [search, setSearch] = useState("");
  const [displaySearch, setDisplaySearch] = useState("");
  const [loadingSubmit, setLoadingSubmit] = useState(false);
  const [selectedValue, setSelectedValue] = useState("");
  const [disabledSave, setDisabledSave] = useState(true);
  //
  //
  const fetchOption = async (search) => {
    try {
      setLoadingOptions(true);
      const {
        data: { data },
      } = await getElementByFamily(family_id, element_id, search);
      data?.length &&
        setOptions(
          data?.map((element) => ({
            // label: element.label,
            label: element.is_used ? (
              <div className="relative flex w-full flex-row justify-between space-x-2">
                <p className="flex truncate">{element.label}</p>
                <CheckOutlined />
              </div>
            ) : (
              element.label
            ),
            value: element.label,
            isUsed: element.is_used,
            key: element.id,
            disabled: element.is_used,
          }))
        );
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      console.error(err);
    } finally {
      setLoadingOptions(false);
    }
  };
  //
  const onFinish = async () => {
    try {
      setLoadingSubmit(true);
      const formData = new FormData();
      formData.append("id", selectedValue);
      const {
        data: { data },
      } = await postAssociate(element_id, formData);
      // setIsUpdate(true);

      data &&
        setData((prev) => {
          if (!prev.length) {
            return [];
          } else {
            return prev.map((element) =>
              element.family_id === family_id
                ? {
                    ...element,
                    children: [
                      {
                        label: data?.label_data,
                        id: data?.id,
                        pipeline: data?.pipeline_label,
                        stage: data?.stage_label,
                      },

                      ...element.children,
                    ],
                    child: [data, ...element.child],
                    number: element?.number + 1,
                  }
                : element
            );
          }
        });
      setAddRemoveAssoc((prev) => !prev);
      setDisplaySearch("");
      setSelectedValue("");
      setOptions([]);
      form.resetFields();
      form.resetFields();
      toastNotification("success", "Association successful!", "topRight");
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification(
          "error",
          err?.response?.data?.message || t("toasts.somethingWrong"),
          "topRight"
        );
      throw new Error(err?.message ? err.message : err);
    } finally {
      setLoadingSubmit(false);
    }
  };
  //
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedSearch = useCallback(
    debounce((nextValue) => fetchOption(nextValue), 1000),
    []
  );

  const onSearch = (value) => {
    setLoadingOptions(true);
    setOptions([]);
    setDisplaySearch(value);
    debouncedSearch(value?.trim());
    // fetchOption(value.trim());
  };
  //
  const onSelect = (data, option) => {
    // console.log("onSelect", data, option);
    setDisplaySearch(option?.label);
    setSelectedValue(option?.key);
    setDisabledSave(false);
  };
  //
  // console.log({ displaySearch });
  //
  const content = (
    <div
      style={{ width: "17rem" }}
      onClick={(event) => event.stopPropagation()}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
        // onFinishFailed={onFinishFailed}
      >
        <Form.Item
          label={t("voip.associateTo") + " :"}
          name="associate"
          rules={[
            {
              required: true,
              // message: 'Please input your password!',
            },
          ]}
        >
          <AutoComplete
            ref={autoCompleteRef}
            style={{
              width: "100%",
            }}
            value={displaySearch}
            onSelect={onSelect}
            onSearch={onSearch}
            // onChange={() => disabledSave && setDisabledSave(true)}
            onChange={() => {
              if (!disabledSave) setDisabledSave(true);
              setSelectedValue("");
            }}
            // onSelect={(_, option) => setDisplaySearch(option.label)}
            // onFocus={() =>
            //   !options.length && !displaySearch.length && fetchOption("")
            // }
            defaultOpen={true}
            options={options}
            allowClear={{
              clearIcon: loadingOptions ? (
                <LoadingOutlined style={{ fontSize: 12, color: "#1677ff" }} />
              ) : (
                <CloseCircleFilled />
              ),
            }}
          />
        </Form.Item>
      </Form>
      <div className="mt-6 flex flex-row justify-end">
        <Button
          type="primary"
          size="small"
          loading={loadingSubmit}
          disabled={disabledSave || !selectedValue}
          onClick={() => form.submit()}
        >
          {t("import.submit")}
        </Button>
      </div>
    </div>
  );
  //
  return (
    <Popover
      onClick={(event) => event.stopPropagation()}
      arrow={false}
      content={content}
      trigger="click"
      placement={placement}
    >
      {children}
    </Popover>
  );
};

export default RelationFamily;
