import { Form, Space } from "antd";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import MainService from "../../services/main.service";
import SidebarTemplateEmail from "./SidebarTemplateEmail";
import ContentEmailTemplate from "./ContentEmailTemplate";
import { useWindowSize } from "../clients&users/components/WindowSize";
import { toastNotification } from "components/ToastNotification";
import { setFamilyId } from "new-redux/actions/menu.actions/menu";
import { useDispatch } from "react-redux";

const EmailTemplates = () => {
  const [t] = useTranslation("common");
  const { user } = useSelector((state) => state.user);
  const {
    family_id: { familyId, typeFamily },
  } = useSelector((state) => state.menu);

  const [form] = Form.useForm();
  const [fields, setFields] = useState([]);

  const windowSize = useWindowSize();

  // const [family_id, setFamily_id] = useState(1);
  const [open, setOpen] = useState({ show: false, type: "" });
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [disabled, setDisabled] = useState(true);
  const [signature, setSignature] = useState("");
  const [senderSms, setSenderSms] = useState("");

  const [selectedGroup, setSelectedGroup] = useState("");
  const [selectedNode, setSelectedNode] = useState(null);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [value, setValue] = useState("");
  const [folders, setFolders] = useState([]);
  const [nodeName, setNodeName] = useState("");
  const [selectedFolder, setSelectedFolder] = useState("");
  const [disabledBtn, setDisabledButton] = useState(true);
  const [fieldsCompany, setFieldsCompany] = useState({});
  const [modules, setModules] = useState([]);
  const [tags, setTags] = useState([]);

  const dispatch = useDispatch();
  const sideBarSize = windowSize?.height - 200;

  useEffect(() => {
    setLoading(true);

    MainService.getConfigCompanies()
      .then((comp) => {
        MainService.getModulesEmails().then((res) =>
          setModules(
            res.data
              .filter((item) => {
                // Toujours conserver "Activity" et "Voip"
                if (item === "Activity" || item === "Voip") {
                  return true;
                }
                // Conserver "Visio" ou "Email" uniquement si leur accès est "1"
                return (
                  (item === "Visio" &&
                    user?.access[item?.toLowerCase()] === "1") ||
                  (item === "Email" &&
                    user?.access[item?.toLowerCase()] === "1")
                );
              })
              .map((item) => ({
                label: item,
                id: item,
                type: 0,
              }))
          )
        );
        setSignature(
          comp.data.data.filter((el) => el.primary_companie == 1)
            ? comp.data.data.filter((el) => el.primary_companie == 1)[0]
            : // ?.signature_system_email
              ""
        );
        const {
          id,
          sender_sms,
          sender_name,
          signature_system_email,
          system_email,
          primary_companie,
          Banks,
          ...newMessage
        } = comp.data.data.filter((el) => el.primary_companie == 1)[0];

        if (newMessage)
          setFieldsCompany({
            label: t("companies.company"),
            // id: 10000,
            fields: Object.entries(newMessage)
              .filter((ell) => !ell.id)
              .map(([key, value]) => ({
                label: "company_" + key,
                alias: t(`companies.${key}`),
              })),
            isCompany: true,
          });

        // setFields(
        //   comp.data.data.filter((el) => el.primary_companie == 1)
        //     ? comp.data.data.filter((el) => el.primary_companie == 1)[0]
        //         ?.signature_system_email
        //     : []
        // );
        setSenderSms(
          comp.data.data.filter((el) => el.primary_companie == 1)
            ? comp.data.data.filter((el) => el.primary_companie == 1)[0]
                ?.sender_sms
            : ""
        );
        MainService.getTags().then((res) => setTags(res.data));
      })
      .catch((err) => {
        console.log(err);
        setLoading(false);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      });
    return () => dispatch(setFamilyId({ familyId: 4, typeFamily: 1 }));
  }, []);
  const onChange = (value, tt) => {
    dispatch(
      setFamilyId({
        familyId: value,
        typeFamily: tt.type,
      })
    );
    // setFamily_id(value);
  };
  const onSearch = (value) => {
    console.log("search:", value);
  };
  const showModal = () => {
    setOpen({ show: true, type: "" });
  };

  const onFinish = async (values) => {
    setConfirmLoading(true);
    try {
      setConfirmLoading(false);
      let formData = new FormData();
      formData.append("label", values.label);
      formData.append("family_id[]", familyId);

      const res = await MainService.addFoldersEmails(formData);
    } catch (err) {}
    setTimeout(() => {
      setOpen(false);
      setConfirmLoading(false);
    }, 2000);
  };
  const handleOk = () => {
    // setConfirmLoading(true);
    // setTimeout(() => {
    //   setOpen(false);
    //   setConfirmLoading(false);
    // }, 2000);
    form.submit();
  };

  const handleCancel = () => {
    console.log("Clicked cancel button");
    setOpen(false);
  };
  const onFinishFailed = (values) => {
    console.log(values);
  };
  // Filter `option.label` match the user type `input`
  const filterOption = (input, option) =>
    (option?.label ?? "").toLowerCase().includes(input.toLowerCase());
  return (
    <div
      style={{
        maxHeight: "100vh",
        minHeight: "calc(100vh - 57px)",
        overflow: "hidden",
      }}
      onClick={(e) => {
        e.preventDefault();

        e.target.id !== "bodySms" &&
        e.target.id !== "subjectMail" &&
        // !e.target.closest(".ql-editor") &&
        !e.target.closest(".ant-collapse") &&
        !e.target.closest(".quill ")
          ? setDisabledButton(true)
          : setDisabledButton(false);
      }}
    >
      <div className="px-4 pt-4">
        {" "}
        <Space direction="vertical" style={{ width: "100%" }}>
          {/* <div className="flex items-center justify-between">
            <Form.Item label={t("tasks.selectFamily")}>
              <Select
                showSearch
                defaultValue={1}
                placeholder={t("tasks.selectFamily")}
                optionFilterProp="children"
                onChange={onChange}
                onSearch={onSearch}
                style={{ minWidth: 250, maxWidth: 300 }}
                filterOption={filterOption}
                options={[...families.map((fam) => ({ ...fam, type: 1 }))].map(
                  (el) => ({
                    value: el.id,
                    label: el.label,
                    type: el.type,
                  })
                )}
              />
            </Form.Item>
          </div> */}
          {familyId ? (
            <div className="flex gap-2">
              <div style={{ width: 300, marginBottom: 20 }}>
                <SidebarTemplateEmail
                  fieldsCompany={fieldsCompany}
                  setSelectedGroup={setSelectedGroup}
                  selectedGroup={familyId || 4}
                  modules={modules}
                  type={typeFamily}
                  setSelectedNode={setSelectedNode}
                  selectedNode={selectedNode}
                  expandedKeys={expandedKeys}
                  setExpandedKeys={setExpandedKeys}
                  value={value}
                  folders={folders}
                  setFolders={setFolders}
                  nodeName={nodeName}
                  setNodeName={setNodeName}
                  selectedFolder={selectedFolder}
                  setSelectedFolder={setSelectedFolder}
                  loading={loading}
                  setLoading={setLoading}
                  delete1={() => {}}
                  setDisabled={setDisabled}
                  fields={fields}
                  setFields={setFields}
                  setSignature={setSignature}
                  setSenderSms={setSenderSms}
                />
              </div>
              {/* <Modal
                title={t(`wiki.AddAFolder`)}
                open={open.show}
                onOk={handleOk}
                confirmLoading={confirmLoading}
                onCancel={handleCancel}
              >
                <Form
                  form={form}
                  onFinish={onFinish}
                  onFinishFailed={onFinishFailed}
                  autoComplete="off"
                  scrollToFirstError
                  layout="inline"
                >
                  <Form.Item
                    name="label"
                    style={{ width: "100%" }}
                    rules={[
                      {
                        required: true,
                        message: `${t(`label`)} ${t(
                          "table.header.isrequired"
                        )}`,
                      },
                    ]}
                  >
                    <Input />
                  </Form.Item>P
                </Form>
              </Modal> */}
              <div className="">
                {folders && folders.length > 0 && selectedNode && (
                  <ContentEmailTemplate
                    selectedNode={selectedNode}
                    folders={folders}
                    setFolders={setFolders}
                    nameGroup={value}
                    nodeName={nodeName}
                    delete1={() => {}}
                    selectedGroup={familyId}
                    setSelectedNode={setSelectedNode}
                    setDisabled={setDisabled}
                    disabled={disabled}
                    fields={fields}
                    setFields={setFields}
                    signature={signature}
                    senderSms={senderSms}
                    disabledBtn={disabledBtn}
                    setDisabledButton={setDisabledButton}
                    tags={tags}
                  />
                )}
              </div>
            </div>
          ) : null}
        </Space>
      </div>
    </div>
  );
};

export default EmailTemplates;
