import {
  AtSymbolIcon,
  CalendarDaysIcon,
  LinkIcon,
} from "@heroicons/react/24/outline";
import { BsCalendar4Range } from "react-icons/bs";
import { BiRadioCircle } from "react-icons/bi";
import { IoImagesOutline } from "react-icons/io5";
import { Vsc<PERSON>hecklist } from "react-icons/vsc";
import { MdTypeSpecimen } from "react-icons/md";
import {
  FontSizeOutlined,
  PhoneOutlined,
  BorderOutlined,
  BarsOutlined,
  FieldNumberOutlined,
  HistoryOutlined,
  CalendarOutlined,
  LineHeightOutlined,
  DiffOutlined,
  PictureOutlined,
  DollarCircleOutlined,
  LockOutlined,
  EnvironmentOutlined,
  BgColorsOutlined,
  GlobalOutlined,
  ApartmentOutlined,
  ApiOutlined,
  StarOutlined,
  DollarOutlined,
  ContainerOutlined,
  TeamOutlined,
} from "@ant-design/icons";
import { AiOutlineShoppingCart } from "react-icons/ai";
import { MdOutlineCurrencyExchange } from "react-icons/md";
import { Cg<PERSON><PERSON>lane } from "react-icons/cg";
import { FiUsers } from "react-icons/fi";
import { LuPalmtree } from "react-icons/lu";
import { GrTransaction } from "react-icons/gr";
import { HiOutlineBuildingOffice } from "react-icons/hi2";
import { Blocks, HeartHandshake } from "lucide-react";
import TicketIconSphere from "components/icons/TicketIconSphere";

export const displayRightIcon = (type, height, width) => {
  switch (type) {
    case "radio":
      return <BiRadioCircle className={`h-${height} w-${width} font-medium`} />;
    case "text":
      return (
        <FontSizeOutlined className={`h-${height} w-${width} font-medium`} />
      );
    case "phone":
      return <PhoneOutlined className={`h-${height} w-${width} font-medium`} />;
    case "checkbox":
      return (
        <BorderOutlined className={`h-${height} w-${width} font-medium`} />
      );
    case "select":
      return <BarsOutlined className={`h-${height} w-${width} font-medium`} />;
    case "email":
      return <AtSymbolIcon className={`h-${height} w-${width} font-medium`} />;
    case "date_time":
      return (
        <CalendarDaysIcon className={`h-${height} w-${width} font-medium`} />
      );
    case "number":
      return (
        <FieldNumberOutlined className={`h-${height} w-${width} font-medium`} />
      );
    case "range":
      return (
        <BsCalendar4Range className={`h-${height} w-${width} font-medium`} />
      );
    case "time":
      return (
        <HistoryOutlined className={`h-${height} w-${width} font-medium`} />
      );
    case "date":
      return (
        <CalendarOutlined className={`h-${height} w-${width} font-medium`} />
      );
    case "textarea":
      return (
        <LineHeightOutlined className={`h-${height} w-${width} font-medium`} />
      );
    case "file":
      return <DiffOutlined className={`h-${height} w-${width} font-medium`} />;
    case "album":
      return (
        <IoImagesOutline className={`h-${height} w-${width} font-medium`} />
      );
    case "monetary":
      return (
        <DollarCircleOutlined
          className={`h-${height} w-${width} font-medium`}
        />
      );
    case "link":
      return <LinkIcon className={`h-${height} w-${width} font-medium`} />;
    case "password":
      return <LockOutlined className={`h-${height} w-${width} font-medium`} />;
    case "image":
      return (
        <PictureOutlined className={`h-${height} w-${width} font-medium`} />
      );
    case "ip address":
      return (
        <EnvironmentOutlined className={`h-${height} w-${width} font-medium`} />
      );
    case "color":
      return (
        <BgColorsOutlined className={`h-${height} w-${width} font-medium`} />
      );
    case "country":
      return (
        <GlobalOutlined className={`h-${height} w-${width} font-medium`} />
      );
    case "module":
      return (
        <ApartmentOutlined className={`h-${height} w-${width} font-medium`} />
      );
    case "extension":
      return <ApiOutlined className={`h-${height} w-${width} font-medium`} />;
    case "multiselect":
      return <VscChecklist className={`h-${height} w-${width} font-medium`} />;
    case "rate":
      return <StarOutlined className={`h-${height} w-${width} font-medium`} />;
    case "autocomplete":
      return (
        <MdTypeSpecimen className={`h-${height} w-${width} font-medium`} />
      );
    case "currency":
      return (
        <MdOutlineCurrencyExchange
          className={`h-${height} w-${width} font-medium`}
        />
      );

    default:
      break;
  }
};

export const displayFamiliesIcons = (type, height, width) => {
  switch (type) {
    case "User":
      return <FiUsers className={`h-${height} w-${width} font-medium`} />;
    case "Contact":
      return <TeamOutlined className={`h-${height} w-${width} font-medium`} />;
    case "Organisation":
      return (
        <HiOutlineBuildingOffice
          className={`h-${height} w-${width} font-medium`}
        />
      );
    case "Leads":
      return <CgUserlane className={`h-${height} w-${width} font-medium`} />;
    case "Lead":
      return <CgUserlane className={`h-${height} w-${width} font-medium`} />;
    case "Deal":
      return <HeartHandshake size={height + 12} />;
    case "Ticket":
      return <TicketIconSphere size={height + 12} />;
    case "Project":
      return <Blocks size={height + 12} />;
    case "Product":
      return (
        <AiOutlineShoppingCart
          className={`h-${height} w-${width} font-medium`}
        />
      );
    case "Booking":
      return <LuPalmtree className={`h-${height} w-${width} font-medium`} />;
    case "Invoices":
      return (
        <DollarOutlined className={`h-${height} w-${width} font-medium`} />
      );
    case "Transaction":
      return <GrTransaction className={`h-${height} w-${width} font-medium`} />;

    default:
      return (
        <MdOutlineCurrencyExchange
          className={`h-${height} w-${width} font-medium`}
        />
      );
  }
};
