// import { useState } from "react";
import {
  Button,
  Divider,
  Input,
  List,
  Popconfirm,
  Popover,
  Segmented,
  Tag,
  Tooltip,
} from "antd";
import { CheckOutlined, SearchOutlined } from "@ant-design/icons";

import { useCallback, useEffect, useMemo, useState } from "react";
import { toastNotification } from "../../../components/ToastNotification";

import MainService from "../../../services/main.service";

import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import moment from "moment";

//

//
const Transfer = ({
  dataRow,
  idEmail,
  setDataSource,
  accountId,
  typeFrom,
  setDetailsMail,
  idThread,
  openAction,
}) => {
  const [t] = useTranslation("common");
  const { i18n } = useTranslation("common");

  const [loadingUsers, setLoadingUsers] = useState(false);
  const [loading, setLoading] = useState(false);
  const [openPopover, setOpenPopover] = useState(false);
  /*
   * type = 0 => private
   * type = 1 => shared
   */
  const [type, setType] = useState(1);
  const [search, setSearch] = useState("");
  const [dataUsers, setDataUsers] = useState([]);
  const [dataUsersFiltred, setDataFiltred] = useState([]);

  const { dataAccounts } = useSelector((state) => state.mailReducer);
  const { user } = useSelector(({ user }) => user);
  const usedAccount = useMemo(
    () => dataAccounts?.find((item) => item?.selected),
    [dataAccounts]
  );

  const getUsers = useCallback(async () => {
    setLoadingUsers(true);
    if (type === "") return;
    try {
      setDataUsers([]);
      setDataFiltred([]);
      const response = await MainService.getUserstransferred(type);
      if (response?.status === 200) {
        if (type === 1) {
          const array = Object.values(response.data.data).map(
            (item, index) => ({
              label: Object.keys(response.data.data)[index],
              options: item.map((el) => ({
                label: el.email,
                value: el.departement_id + "/" + el.accountId + "/" + el.email,
              })),
            })
          );
          setDataUsers([...array]);
          setDataFiltred([...array]);
        } else {
          const array2 = response.data?.data?.map((item) => ({
            label: item.email,
            value: item.userId + "/" + item.accountId + "/" + item.email,
          }));
          setDataUsers([...array2]);
          setDataFiltred([...array2]);
        }

        setLoadingUsers(false);
      }
    } catch (error) {
      setLoadingUsers(false);
      console.log(error);
    }
  }, [type]);

  const TransferEmail = async (value) => {
    setLoading(true);
    var formData = new FormData();
    formData.append(
      "email_id",
      typeFrom === "inbox" || typeFrom === "dropdown" ? idEmail : idThread
    );
    formData.append("emailTransfert", value.split("/")[2]);
    formData.append("type_dispatch", type);
    if (usedAccount.departmentId.length > 0) {
      for (let i = 0; i < usedAccount.departmentId.length; i++) {
        formData.append("departement_id[]", usedAccount.departmentId[i]);
      }
    }
    // else if(usedAccount.departmentId.length>0){
    //   formData.append("departement_id[]", value.split("/")[1]);
    // }

    formData.append("account_id", value.split("/")[1]);
    formData.append("account_id_source", accountId);
    if (type === 0) {
      formData.append("userId", value.split("/")[0]);
    }
    try {
      const response = await MainService.transferEmail(formData);
      if (response?.status === 200) {
        setLoading(false);
        if (typeFrom === "inbox" || typeFrom === "dropdown") {
          setDataSource &&
            setDataSource((prevState) =>
              prevState?.map((item) => {
                return item.id === idEmail
                  ? { ...item, transfert: response?.data.transferred_to }
                  : item;
              })
            );
        } else {
          setDetailsMail((p) => {
            let detail = Object.assign({}, p);
            detail.data = detail.data.map((item) => {
              return item.id === idThread
                ? { ...item, transfert: response?.data?.transferred_to }
                : item;
            });

            // detail.data[detail?.data?.length - 1].tags = response?.data.data.tags;
            return detail;
          });

          // setDetailsMail((p) => {
          //   let prev = { ...p };
          //   prev.data[prev?.data?.length - 1].transfert =
          //     response?.data.transferred_to;
          //   return prev;
          // });
        }
        setType("");
        setDataUsers([]);
        setDataFiltred([]);
        setOpenPopover(false);
        toastNotification("success", t("mailing.movedSuccess"), "topRight", 3);
      }
    } catch (error) {
      console.log(error);
      setLoading(false);
      toastNotification(
        "error",
        error?.response?.data
          ? error?.response?.data?.message
          : t("toasts.errorFetchApi"),
        "topRight",
        4
      );
    }
  };

  const searchTerm = useCallback(
    (value) => {
      value = value.trim();
      let array = [...dataUsers];
      if (value === "") {
        array = [...dataUsers];
      } else {
        if (type === 1) {
          array = array.map((item) => ({
            label: item.label,
            options: item.options?.filter((el) =>
              el?.label?.toLowerCase()?.includes(value.toLowerCase())
            ),
          }));
        } else
          array = array.filter((item) =>
            item.label?.toLowerCase().includes(value?.toLowerCase())
          );
      }

      setSearch(value);

      setDataFiltred([...array]);
    },
    [dataUsers, type]
  );

  useEffect(() => {
    if (openPopover) getUsers();
  }, [getUsers, openPopover]);

  const TransferContents = (
    <>
      <div className="flex justify-center">
        <Segmented
          onChange={(e) => {
            setType(e);

            setSearch("");
          }}
          options={[
            {
              label: "Shared",
              value: 1,
            },
            {
              label: "Private",
              value: 0,
            },
          ]}
        />
      </div>

      <Input
        placeholder={
          type === 1
            ? t("mailing.searchemailPrivate")
            : t("mailing.searchemailShared")
        }
        value={search}
        onChange={(e) => searchTerm(e.target.value)}
        prefix={<SearchOutlined style={{ color: "#cccccc" }} />}
        suffix={null}
        style={{ marginTop: "4px" }}
      />

      <List
        dataSource={dataUsersFiltred}
        loading={loadingUsers}
        itemLayout="vertical"
        style={{ maxHeight: "200px", width: "100%", overflowY: "auto" }}
        renderItem={(item, index) => (
          <>
            <div className="group w-full space-y-1  ">
              {type === 1 ? (
                <>
                  {item.options?.length > 0 && (
                    <>
                      <Divider
                        style={{
                          padding: "0.5px",
                        }}
                        plain
                        orientation="left"
                      >
                        {item.label}
                      </Divider>
                      {item.options?.map((el, index) => (
                        <Popconfirm
                          title={t("mailing.TransferPopConfirm")}
                          description={t("mailing.TransferMail")}
                          onConfirm={() => TransferEmail(el.value)}
                          okText="Yes"
                          cancelText="No"
                        >
                          <List.Item
                            className=" cursor-pointer gap-y-0.5 rounded-md hover:bg-gray-100"
                            key={index}
                            // onClick={() => TransferEmail(el.value)}
                          >
                            <span className="pl-1">{el.label}</span>
                          </List.Item>
                        </Popconfirm>
                      ))}
                    </>
                  )}
                </>
              ) : (
                <Popconfirm
                  title={t("mailing.TransferPopConfirm")}
                  description={t("mailing.TransferMail")}
                  onConfirm={() => TransferEmail(item.value)}
                  okText="Yes"
                  cancelText="No"
                >
                  <List.Item
                    className=" m-1 cursor-pointer gap-y-0.5 rounded-md p-1 group-hover:bg-gray-100"
                    key={index}
                    // onClick={() => TransferEmail(item.value)}
                  >
                    <span className="pl-1">{item.label}</span>
                  </List.Item>
                </Popconfirm>
              )}
            </div>
          </>
        )}
      />
    </>
  );

  useEffect(() => {
    if (openAction) {
      setOpenPopover(openAction);
    }
  }, [openAction]);

  return (
    <>
      {dataRow.transfert &&
      dataRow.transfert.account_id != usedAccount?.value ? (
        typeFrom === "dropdown" ? (
          <p>{t("mailing.moved")}</p>
        ) : (
          <Tooltip
            title={
              t("mailing.emailTransfer") +
              ` ${dataRow?.transfert?.label_data_owner}  ${
                dataRow?.transfert?.email_source ? t("mailing.from") : ` `
              }   ${dataRow?.transfert?.email_source ?? ""} ` +
              t("mailing.to") +
              ` ${dataRow?.transfert?.email}  ${
                moment(dataRow?.transfert?.date_transfert)
                  .locale(i18n.language)
                  .format("LLLL")
                  ? t("mailing.in")
                  : ``
              }  ${
                moment(dataRow?.transfert?.date_transfert)
                  .locale(i18n.language)
                  .format("LLLL") ?? ""
              }`
            }
            placement="bottom"
          >
            <Tag
              icon={<CheckOutlined />}
              color="red"
              style={{ width: "92px", textAlign: "center" }}
            >
              {t("mailing.moved")}
            </Tag>
          </Tooltip>
        )
      ) : dataRow.owner?.owner ? (
        <Tooltip
          title={t("mailing.Tooltip.AssignNoTransfer")}
          placement="bottom"
        >
          {typeFrom === "dropdown" ? (
            t("mailing.move")
          ) : (
            <Button
              size="small"
              style={{
                width: typeFrom === "details" ? "65px" : "92px",
                textAlign: "center",
              }}
              type="dashed"
              disabled={true}
            >
              {t("mailing.move")}
            </Button>
          )}
        </Tooltip>
      ) : (
        <Popover
          showArrow
          content={TransferContents}
          title={t("mailing.moveTo")}
          overlayStyle={{ width: "300px" }}
          open={openPopover}
          onOpenChange={(open) => setOpenPopover(open)}
          trigger={["click"]}
          placement="bottomLeft"
          arrow={true}
          style={{ backgroundColor: "red" }}
        >
          {/* {typeFrom === "inbox" ? ( */}
          {typeFrom === "dropdown" ? (
            t("mailing.move")
          ) : (
            <Tooltip
              title={
                dataRow.transfert
                  ? t("mailing.emailMoved")
                  : t("mailing.Tooltip.Move")
              }
              placement="bottom"
            >
              <Button
                disabled={dataRow.transfert}
                type="dashed"
                size="small"
                style={{ width: "92px" }}
                onClick={(e) => e?.stopPropagation()}
              >
                {dataRow.transfert ? t("mailing.moved") : t("mailing.move")}
              </Button>
            </Tooltip>
          )}
        </Popover>
      )}
    </>
  );
};

export default Transfer;
