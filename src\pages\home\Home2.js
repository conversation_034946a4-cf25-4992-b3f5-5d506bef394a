import {
  ArrowRightOutlined,
  HomeOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons";
import {
  Avatar,
  Card,
  Col,
  List,
  Row,
  Select,
  Space,
  Statistic,
  Tooltip,
  Typography,
} from "antd";
import { AvatarChat } from "components/Chat";
import { URL_ENV } from "index";
import { Mail, Phone, Smartphone } from "lucide-react";
import ItemHeader from "pages/components/DetailsProfile/ItemHeader";
import { getName } from "pages/layouts/chat/utils/ConversationUtils";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { isGuestConnected } from "utils/role";
import { <PERSON>au<PERSON><PERSON><PERSON>, <PERSON><PERSON>hart } from "./components/ChartsDashboard";
import { useDispatch } from "react-redux";
import {
  getDataDashboard,
  getStatsTasks,
  setSelectedMail,
} from "new-redux/actions/dashboard.actions";
import ListTasks from "./components/ListTasks";
import CountUp from "react-countup";
import {
  AtSymbolIcon,
  ChatBubbleBottomCenterTextIcon,
  ChatBubbleLeftRightIcon,
  EnvelopeIcon,
  PhoneArrowDownLeftIcon,
  PhoneArrowUpRightIcon,
} from "@heroicons/react/24/outline";
import { FcCallback } from "react-icons/fc";
import { MdOutlineMarkEmailUnread, MdPhoneMissed } from "react-icons/md";
import { generateAxios } from "services/axiosInstance";
import { SET_RMC } from "new-redux/constants";
import { toastNotification } from "components/ToastNotification";
import { useNavigate } from "react-router-dom";
import { TbMailForward } from "react-icons/tb";
import { familyIcons } from "pages/components/DetailsProfile/ViewSphere2";
import CardQueue from "./components/CardQueue";
import ChoiceIcons from "pages/components/ChoiceIcons";

const Home2 = ({ start, end }) => {
  const { user } = useSelector((state) => state.user);
  const [t] = useTranslation("common");
  const {
    unreadMsgOneToOne,
    unreadMessagesInGroups,
    missedUnreturnedCalls,
    unreadArchivedMessages,
    stages,
    iconsTasks,
    loading,
    unreadEmail,
    totalEmails,
    missedTodayCall,
    outgoingTodayCall,
    receivedTodayCall,
    totalunreadMessagesTags,
    totalQueues,
    channelsRmc,
    depsRmc,
    statsTasks,
    tasks,
    allQueues,
    totalFamilies,
  } = useSelector((state) => state.dashboardRealTime);
  const [selectedMail, setSelecteMail] = useState(
    totalEmails && totalEmails?.length > 0 ? totalEmails[0]?.accountId : null
  );
  const [unreadReceivedEmailByAccount, setUnreadReceivedEmailByAccount] =
    useState(
      totalEmails && totalEmails?.length > 0
        ? totalEmails[0]?.nb_email_received_unread
        : null
    );
  const [receivedEmailByAccount, setReceivedEmailByAccount] = useState(
    totalEmails && totalEmails?.length > 0
      ? totalEmails[0]?.nb_email_received
      : null
  );
  const [totalSentEmailByAccount, setTotalSentEmailByAccount] = useState(
    totalEmails && totalEmails?.length > 0
      ? totalEmails[0]?.nb_email_sent
      : null
  );

  const dispatch = useDispatch();
  const formatter = (value) => <CountUp end={value} separator="," />;
  const navigate = useNavigate();
  const [selectedDep, setSelecteDep] = useState("");

  useEffect(() => {
    if (start && end) {
      dispatch(
        getDataDashboard({
          start,
          end,
          // departement_id: selectedDep,
          // queue_num: selectedQueue,
        })
      );
    }
  }, [dispatch, start, end]);
  const getStatsRmc = async (value, start, end) => {
    const jour = new Date().getDate();
    const mois = (new Date().getMonth() + 1).toString().padStart(2, "0"); // Notez que les mois commencent à partir de 0 (janvier = 0)
    const annee = new Date().getFullYear();

    const dateRmc = jour + "-" + mois + "-" + annee;
    dispatch({
      type: SET_RMC,
      payload: channelsRmc?.map((el) => ({ ...el, count: "-" })),
    });
    try {
      const { data } = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).get(
        `/dashboard/rmcByDepartment?date_start=${start}&date_end=${end}&department_id=${
          value || ""
        }`
      );
      const updatedChannels = channelsRmc?.map((el) => {
        const apiItem = data?.find((item) => item?.channel === el?.channel);
        if (apiItem) {
          return { ...el, count: apiItem.count };
        }
        if (!apiItem) {
          return { ...el, count: 0 };
        }
        return el;
      });
      dispatch({
        type: SET_RMC,
        payload: updatedChannels,
      });
    } catch (err) {
      toastNotification("error", t("toasts.somethingWrong"));
    }
  };
  console.log(statsTasks);
  return (
    <div>
      <Row gutter={[4, 4]}>
        <Col span={7}>
          <div className="flex flex-col gap-y-1">
            <Card>
              <div className="flex flex-col gap-y-1">
                <div className="flex items-center gap-x-2">
                  <AvatarChat
                    fontSize="1.5rem"
                    type="user"
                    url={
                      URL_ENV?.REACT_APP_BASE_URL +
                      URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                      user?.avatar
                    }
                    size={50}
                    name={getName(user?.label, "avatar")}
                    hasImage={user?.avatar}
                  />
                  <div>
                    <span className="text-xl font-bold">
                      {t("dashboard.welcome")}, {getName(user.label, "name")}
                    </span>
                    {user?.email && (
                      <ItemHeader
                        text={user?.email}
                        icon={<Mail size={15} />}
                        title="Email"
                        colorIcon="text-blue-500"
                        colorText="text-slate-600"
                      />
                    )}
                  </div>
                </div>

                <div className="pt-1">
                  <Space size={"small"} className="text-slate-600">
                    <ItemHeader
                      text={user?.extension}
                      icon={<Phone size={15} />}
                      title={t("dashboard.postNumber")}
                      colorIcon="text-blue-500"
                      colorText="text-slate-600"
                    />
                    {user?.phone &&
                      Array.isArray(user?.phone) &&
                      user?.phone?.length > 1 &&
                      user?.phone?.every((el) => el != null) && (
                        <ItemHeader
                          text={user?.phone[0] + user?.phone[1]}
                          icon={<Smartphone size={15} />}
                          title={t("dashboard.phoneNumber")}
                          colorIcon="text-blue-500"
                          colorText="text-black"
                        />
                      )}
                    {user?.tenant && !isGuestConnected() && (
                      <ItemHeader
                        text={user?.tenant}
                        icon={<HomeOutlined />}
                        title={t("dashboard.tenancy")}
                        colorIcon="text-blue-500"
                        colorText="text-slate-600"
                        noIconCopy={true}
                      />
                    )}
                  </Space>
                </div>
              </div>
            </Card>
            <Row gutter={[4, 4]}>
              <Col span={12}>
                <Card>
                  <Statistic
                    formatter={
                      typeof unreadMsgOneToOne === "number" ? formatter : null
                    }
                    title={
                      <Space>
                        {t(`dashboard.unreadmsgOtoO`, {
                          plural: unreadMsgOneToOne > 1 ? "s" : "",
                          pluriel: unreadMsgOneToOne > 1 ? "x" : "",
                        })}
                        {/* <Tooltip title={t("dashboard.independantNumber")}>
                    <InfoCircleOutlined style={{ fontSize: "14px" }} />
                  </Tooltip> */}
                      </Space>
                    }
                    value={unreadMsgOneToOne}
                    prefix={<ChatBubbleLeftRightIcon className="h-6 w-6" />}
                  />
                </Card>
              </Col>

              <Col span={12}>
                <Card>
                  <Statistic
                    formatter={
                      typeof unreadMessagesInGroups === "number"
                        ? formatter
                        : null
                    }
                    title={
                      <Space>
                        {t(`dashboard.unreadmsgtoG`, {
                          plural: unreadMessagesInGroups > 1 ? "s" : "",
                          pluriel: unreadMessagesInGroups > 1 ? "x" : "",
                        })}
                        {/* <Tooltip title={t("dashboard.independantNumber")}>
                    <InfoCircleOutlined style={{ fontSize: "14px" }} />
                  </Tooltip> */}
                      </Space>
                    }
                    value={unreadMessagesInGroups}
                    prefix={
                      <ChatBubbleBottomCenterTextIcon className="h-6 w-6" />
                    }
                  />
                </Card>
              </Col>

              <Col span={12}>
                <Card>
                  <Statistic
                    formatter={
                      typeof unreadArchivedMessages === "number"
                        ? formatter
                        : null
                    }
                    title={
                      <Space>
                        {t(`dashboard.unreadArchivedMessages`, {
                          plural: unreadArchivedMessages > 1 ? "s" : "",
                          pluriel: unreadArchivedMessages > 1 ? "x" : "",
                        })}
                        {/* <Tooltip title={t("dashboard.independantNumber")}>
                    <InfoCircleOutlined style={{ fontSize: "14px" }} />
                  </Tooltip> */}
                      </Space>
                    }
                    value={unreadArchivedMessages}
                    prefix={
                      <ChatBubbleBottomCenterTextIcon className="h-6 w-6" />
                    }
                  />
                </Card>
              </Col>

              {/* {console.log(totalunreadMessagesTags)} */}

              <Col span={12}>
                <Card>
                  <Statistic
                    formatter={
                      typeof totalunreadMessagesTags === "number"
                        ? formatter
                        : null
                    }
                    title={
                      <Space>
                        {t(`dashboard.unreadMention`, {
                          plural: totalunreadMessagesTags > 1 ? "s" : "",
                          pluriel: totalunreadMessagesTags > 1 ? "x" : "",
                        })}

                        {/* <Tooltip title={t("dashboard.independantNumber")}>
                    <InfoCircleOutlined style={{ fontSize: "14px" }} />
                  </Tooltip> */}
                      </Space>
                    }
                    value={totalunreadMessagesTags}
                    prefix={<AtSymbolIcon className="h-6 w-6" />}
                  />
                </Card>
              </Col>
            </Row>
            <Card>
              <Row gutter={[4, 4]} className="h-[250px] overflow-auto">
                {statsTasks?.map((el) => (
                  <Col span={12}>
                    <Card>
                      <Statistic
                        formatter={
                          typeof el.count === "number" ? formatter : null
                        }
                        title={
                          <Space>
                            {el.label}
                            {/* <Tooltip title={t("dashboard.independantNumber")}>
                    <InfoCircleOutlined style={{ fontSize: "14px" }} />
                  </Tooltip> */}
                          </Space>
                        }
                        value={el.count}
                        valueStyle={{
                          color: el.color,
                        }}
                        prefix={<ChoiceIcons icon={el.icons} />}
                      />
                    </Card>
                  </Col>
                ))}
              </Row>
              {/* <PieChart
                data={statsTasks?.map((el) => ({
                  ...el,
                  name: el.label,
                  y: el.count,
                }))}
                total={statsTasks
                  ?.map((el) => el.count)
                  ?.reduce((x, y) => x + y, 0)}
                name={""}
              /> */}
            </Card>
          </div>
        </Col>
        <Col span={12}>
          <div className="flex flex-col gap-y-1">
            <Row gutter={[4, 4]} className="">
              <Col span={12}>
                <Card>
                  <Statistic
                    formatter={
                      typeof receivedTodayCall === "number" ? formatter : null
                    }
                    title={t(`dashboard.receivedCalls`, {
                      plural: receivedTodayCall > 1 ? "s" : "",
                      pluriel: receivedTodayCall > 1 ? "x" : "",
                    })}
                    value={receivedTodayCall}
                    valueStyle={{
                      color: "#3f8600",
                    }}
                    prefix={<PhoneArrowDownLeftIcon className="h-5 w-5" />}
                  />
                </Card>
              </Col>
              <Col span={12}>
                <Card>
                  <Statistic
                    formatter={
                      typeof missedTodayCall === "number" ? formatter : null
                    }
                    title={t(`dashboard.missedCalls`, {
                      plural: missedTodayCall > 1 ? "s" : "",
                      pluriel: missedTodayCall > 1 ? "x" : "",
                    })}
                    value={missedTodayCall}
                    valueStyle={{
                      color: "#cf1322",
                    }}
                    prefix={
                      <MdPhoneMissed className="h-[22px] w-[22px] text-xl text-[#EF4444] " />
                    }
                  />
                </Card>
              </Col>
              <Col span={12}>
                <Card>
                  <Statistic
                    formatter={
                      typeof outgoingTodayCall === "number" ? formatter : null
                    }
                    title={t(`dashboard.outgoingCalls`, {
                      plural: outgoingTodayCall > 1 ? "s" : "",
                      pluriel: outgoingTodayCall > 1 ? "x" : "",
                    })}
                    value={outgoingTodayCall}
                    valueStyle={{
                      color: "#1677ff",
                    }}
                    prefix={<PhoneArrowUpRightIcon className="h-5 w-5" />}
                  />
                </Card>
              </Col>

              <Col span={12}>
                <Card>
                  <Statistic
                    formatter={
                      typeof missedUnreturnedCalls === "number"
                        ? formatter
                        : null
                    }
                    title={t(`dashboard.unreturnedMissedCalls`, {
                      plural: missedUnreturnedCalls > 1 ? "s" : "",
                      pluriel: missedUnreturnedCalls > 1 ? "x" : "",
                    })}
                    value={missedUnreturnedCalls}
                    valueStyle={{
                      color: "black",
                    }}
                    prefix={<FcCallback className="h-[22px] w-[22px]" />}
                  />
                </Card>
              </Col>
            </Row>

            {Array.isArray(tasks) && tasks.length > 0 && (
              <Card>
                <ListTasks list={tasks} />
              </Card>
            )}
            {totalQueues &&
            Array.isArray(totalQueues) &&
            totalQueues.length > 0 ? (
              <CardQueue start={start} end={end} />
            ) : null}
          </div>
        </Col>
        <Col span={5}>
          <div className="flex flex-col gap-y-1">
            <Card>
              <GaugeChart
                data={{
                  total: 25,
                  unit: "ticket",
                  used_storage: 10,
                  usage_percentage: 60,
                  available: 5,
                  title: "",
                }}
                height={200}
                size={"130%"}
              />
            </Card>
            <Card
              styles={{ header: { padding: "4px 8px" } }}
              title={
                <div className="flex flex-col gap-y-1 ">
                  <span>{t("dashboard.socialMedia")}</span>{" "}
                  <Select
                    showSearch
                    size="small"
                    optionFilterProp="children"
                    popupMatchSelectWidth={false}
                    filterOption={(input, option) =>
                      (option?.label2 ?? "")
                        .toLowerCase()
                        .includes(input.toLowerCase())
                    }
                    options={[
                      {
                        value: "",
                        label2: t("helpDesk.all"),
                        label: (
                          <div className="flex items-center justify-between">
                            <span>{t("helpDesk.all")}</span>
                            <Tooltip title={t("dashboard.relativeFigures")}>
                              <InfoCircleOutlined />
                            </Tooltip>
                          </div>
                        ),
                      },
                      ...depsRmc?.map((dep) => ({
                        value: dep.department_id,
                        label: dep.name,
                        label2: dep.name,
                        color: dep.color,
                      })),
                    ]}
                    style={{ minWidth: 100, width: "min-content" }}
                    value={selectedDep}
                    onChange={(value, values) => {
                      setSelecteDep(value);
                      getStatsRmc(value, start, end);
                    }}
                  />{" "}
                </div>
              }
              size=""
              className="h-full bg-white shadow-sm"
              extra={
                <Typography.Link
                  className="pl-2"
                  onClick={() => navigate("/rmc")}
                >
                  <ArrowRightOutlined />
                </Typography.Link>
              }
              style={{ height: "auto" }}
            >
              {/* {channelsRmc?.map((el, i) => ( */}
              <List
                dataSource={channelsRmc}
                renderItem={(item) => (
                  // <div className="px-2">
                  <List.Item>
                    <div className="flex w-full items-center justify-between">
                      <span>
                        <Avatar src={item.icon} style={{ fontSize: 22 }} />
                        <span className="text-base font-semibold">
                          {t(`dashboard.${item?.channel}`)}
                        </span>
                      </span>
                      <span className="text-lg font-bold">{item.count}</span>
                    </div>
                  </List.Item>
                  // </div>
                )}
              />

              {/* ))} */}
            </Card>
            {Array.isArray(user?.accounts_email) &&
            user?.accounts_email?.length > 0 &&
            typeof unreadEmail === "number" ? (
              <Card
                styles={{ header: { padding: "4px 8px" } }}
                title={
                  <div className="flex flex-col  gap-y-1 ">
                    <span>Emails</span>
                    <span className="truncate">
                      <Select
                        size="small"
                        showSearch
                        defaultValue={totalEmails[0]?.accountId}
                        placeholder="Select a person"
                        optionFilterProp="children"
                        popupMatchSelectWidth={false}
                        options={totalEmails?.map((el) => ({
                          value: el?.accountId,
                          label: el?.email,
                          nb_email_received_unread:
                            el?.nb_email_received_unread,
                          nb_email_sent: el?.nb_email_sent,
                        }))}
                        filterOption={(input, option) =>
                          (option?.label ?? "")
                            .toLowerCase()
                            .includes(input.toLowerCase())
                        }
                        value={selectedMail}
                        onChange={(value, values) => {
                          dispatch(setSelectedMail(value));
                          setSelecteMail(value);
                          dispatch(setSelectedMail(value));
                          setUnreadReceivedEmailByAccount(
                            values?.nb_email_received_unread
                          );
                          setReceivedEmailByAccount(values?.nb_email_received);
                          setTotalSentEmailByAccount(values?.nb_email_sent);
                        }}
                      />
                    </span>
                  </div>
                }
                size=""
                className="h-full bg-white shadow-sm"
                extra={
                  <Typography.Link
                    onClick={() =>
                      navigate(
                        `/mailing/${
                          user?.accounts_email &&
                          user?.accounts_email?.length > 0 &&
                          user?.accounts_email?.find(
                            (el) => el.primary_account == 1
                          )
                            ? user?.accounts_email?.find(
                                (el) => el.primary_account == 1
                              )?.id
                            : user?.accounts_email?.length &&
                              user?.accounts_email.length > 0
                            ? user?.accounts_email[0].id
                            : 0
                        }/inbox`
                      )
                    }
                  >
                    <ArrowRightOutlined />
                  </Typography.Link>
                }
                style={{ height: "auto" }}
              >
                {/* {channelsRmc?.map((el, i) => ( */}
                <Row gutter={24} className="">
                  <Col flex="auto">
                    <Statistic
                      formatter={
                        typeof receivedEmailByAccount === "number"
                          ? formatter
                          : null
                      }
                      title={
                        <Space>
                          {t(`dashboard.receivedCalls`, {
                            plural: receivedEmailByAccount > 1 ? "s" : "",
                            pluriel: receivedEmailByAccount > 1 ? "x" : "",
                          })}
                        </Space>
                      }
                      value={receivedEmailByAccount}
                      prefix={
                        <EnvelopeIcon className="h-6 w-6 text-green-600" />
                      }
                    />
                  </Col>

                  <Col flex="auto">
                    <Statistic
                      formatter={
                        typeof unreadReceivedEmailByAccount === "number"
                          ? formatter
                          : null
                      }
                      title={
                        <span>
                          {t(`dashboard.unreadEmailReceived`, {
                            plural: unreadReceivedEmailByAccount > 1 ? "s" : "",
                            pluriel:
                              unreadReceivedEmailByAccount > 1 ? "x" : "",
                          })}{" "}
                          <Tooltip title={t("dashboard.unreadPerSession")}>
                            <InfoCircleOutlined />
                          </Tooltip>
                        </span>
                      }
                      value={unreadReceivedEmailByAccount}
                      // suffix={`/${receivedEmailByAccount}`}
                      prefix={
                        <MdOutlineMarkEmailUnread className="h-6 w-6 text-red-600" />
                      }
                    />
                  </Col>

                  <Col flex="auto">
                    <Statistic
                      formatter={
                        typeof totalSentEmailByAccount === "number"
                          ? formatter
                          : null
                      }
                      title={t(`dashboard.totalEmailSent`, {
                        plural: totalSentEmailByAccount > 1 ? "s" : "",
                        pluriel: totalSentEmailByAccount > 1 ? "x" : "",
                      })}
                      value={totalSentEmailByAccount}
                      prefix={
                        <TbMailForward className="h-6 w-6 text-blue-500" />
                      }
                    />
                  </Col>
                </Row>

                {/* ))} */}
              </Card>
            ) : Array.isArray(user?.accounts_email) &&
              user?.accounts_email?.length > 0 &&
              typeof unreadEmail !== "number" ? (
              <Card
                title={
                  <div className="flex items-center justify-between pr-2">
                    <span>Emails</span>
                  </div>
                }
                size=""
                className="h-full bg-white shadow-sm"
                extra={
                  unreadEmail > 0 ? (
                    <Typography.Link
                      onClick={() =>
                        navigate(
                          `/mailing/${
                            user?.accounts_email &&
                            user?.accounts_email?.length > 0 &&
                            user?.accounts_email?.find(
                              (el) => el.primary_account == 1
                            )
                              ? user?.accounts_email?.find(
                                  (el) => el.primary_account == 1
                                )?.id
                              : user?.accounts_email?.length &&
                                user?.accounts_email.length > 0
                              ? user?.accounts_email[0].id
                              : 0
                          }/inbox`
                        )
                      }
                    >
                      <ArrowRightOutlined />
                    </Typography.Link>
                  ) : null
                }
              >
                <Row gutter={24} className="">
                  <Col span={12}>
                    <Statistic
                      className="moyen_ringing_calls"
                      formatter={
                        typeof unreadReceivedEmailByAccount === "number"
                          ? formatter
                          : null
                      }
                      title={
                        <span>
                          {t(`dashboard.unreadEmailReceived`, {
                            plural: unreadReceivedEmailByAccount > 1 ? "s" : "",
                            pluriel:
                              unreadReceivedEmailByAccount > 1 ? "x" : "",
                          })}{" "}
                          <Tooltip title={t("dashboard.unreadPerSession")}>
                            <InfoCircleOutlined />
                          </Tooltip>
                        </span>
                      }
                      suffix={`-/ -`}
                      prefix={
                        <MdOutlineMarkEmailUnread className="h-6 w-6 text-red-600" />
                      }
                    />
                  </Col>

                  <Col span={12}>
                    <Statistic
                      className="moyen_ringing_calls"
                      formatter={
                        typeof totalSentEmailByAccount === "number"
                          ? formatter
                          : null
                      }
                      title={t(`dashboard.totalEmailSent`, {
                        plural: totalSentEmailByAccount > 1 ? "s" : "",
                        pluriel: totalSentEmailByAccount > 1 ? "x" : "",
                      })}
                      suffix={`-`}
                      prefix={
                        <TbMailForward className="h-6 w-6 text-blue-500" />
                      }
                    />
                  </Col>
                </Row>
              </Card>
            ) : (
              ""
            )}
            <Card>
              <List
                dataSource={familyIcons(t)}
                style={{ height: "150px", overflowY: "auto" }}
                renderItem={(item) => (
                  // <div className="px-2">
                  <List.Item>
                    <div className="flex w-full items-center justify-between pr-2">
                      <span>
                        <span style={{ fontSize: "20px" }}>{item.icon}</span>
                        <span className="pl-1 font-semibold">{item.label}</span>
                      </span>
                      <div className="flex flex-col ">
                        <span>
                          <span className="pr-1 font-medium">
                            {totalFamilies?.length > 1
                              ? totalFamilies.find(
                                  (el) => el.family_id === item.key
                                )?.total_elements
                              : "-"}
                          </span>
                          {t("dashboard.totalElements", {
                            plural:
                              totalFamilies.find(
                                (el) => el.family_id === item.key
                              )?.total_elements > 1
                                ? "s"
                                : "",
                          })}
                        </span>
                        <span>
                          <span className="pr-1 font-medium">
                            {totalFamilies?.length > 1
                              ? totalFamilies.find(
                                  (el) => el.family_id === item.key
                                )?.created_by_you
                              : "-"}
                          </span>
                          {t("dashboard.createdByYou", {
                            plural:
                              totalFamilies.find(
                                (el) => el.family_id === item.key
                              )?.created_by_you > 1
                                ? "s"
                                : "",
                          })}
                        </span>
                      </div>
                    </div>
                  </List.Item>
                  // </div>
                )}
              />
            </Card>
          </div>
        </Col>
        <Col span={7}></Col>
        <Col span={17}>
          {/* {totalQueues &&
          Array.isArray(totalQueues) &&
          totalQueues.length > 0 ? (
            <CardQueue start={start} end={end} />
          ) : null} */}
        </Col>
      </Row>
    </div>
  );
};

export default Home2;
