import React, { useEffect, useRef } from "react";
import {
  Form,
  InputNumber,
  Input,
  Button,
  Space,
  Switch,
  Badge,
  Select,
  Tooltip,
} from "antd";
import { useState } from "react";
import { generateAxios } from "../services/axiosInstance";
import { useTranslation } from "react-i18next";
import { PlusCircleOutlined } from "@ant-design/icons";

import { toastNotification } from "./ToastNotification";
import Header from "./configurationHelpDesk/Header";
import GroupColors from "./GroupColors";
import { colors } from "./Colors";
import TextArea from "antd/es/input/TextArea";
import ColumnColors from "./ColumnColors";
import NewTableDraggable from "./NewTableDraggable";
import { useDispatch, useSelector } from "react-redux";
import { setSearch } from "../new-redux/actions/menu.actions/menu";
import LabelTable from "./LabelTable";
import BottomButtonAddRow from "./BottomButtonAddRow";
import { SubmitKeyPress } from "../utils/SubmitKeyPress";
import { FiEdit3 } from "react-icons/fi";
import { URL_ENV } from "index";

const Severities = () => {
  const [form] = Form.useForm();
  const [, setCount] = useState(0);

  const [data, setData] = useState([]);
  const [editingKey, setEditingKey] = useState("");
  const [id, setId] = useState(null);
  const [editLabel, setEditLabel] = useState("");

  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [oldPage, setOldPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  const isEditing = (record) => record.key === editingKey;
  const [t] = useTranslation("common");
  const [color, setColor] = useState("");
  const inputRefs = useRef([]);
  const dispatch = useDispatch();
  const { search } = useSelector((state) => state.form);
  useEffect(() => {
    inputRefs.current.forEach((input) => {
      input?.focus();
    });
  }, [data.length, id]);
  // useEffect(() => {
  //   if (editingKey && oldPage !== currentPage) {
  //     setData((prev) => prev.filter((el) => el.id));
  //     setEditingKey("");
  //   }
  // }, [currentPage, editingKey, oldPage]);
  const handleKeyPress = (event) => {
    SubmitKeyPress(event, form);
  };

  const onFinishFailed = (values) => {
    console.log(values);
  };

  const EditableCell = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    const inputNode =
      inputType === "radio" ? (
        <Select
          showSearch
          allowClear
          placeholder={t("tags.selectcolor")}
          style={{
            width: 200,
          }}
          options={colors.map((el) => ({
            label: (
              <Space>
                <Badge color={el.value} /> {t(`colors.${el.label}`)}
              </Space>
            ),
            value: el.value,
            label2: el.label,
          }))}
          optionFilterProp="children"
          filterOption={(input, option) => {
            var _a;
            return (
              (_a =
                option === null || option === void 0
                  ? void 0
                  : t(`colors.${option.label2}`)) !== null && _a !== void 0
                ? _a
                : ""
            )
              .toLowerCase()
              .includes(input.toLowerCase());
          }}
          filterSort={(optionA, optionB) =>
            (optionA?.value ?? "")
              .toLowerCase()
              .localeCompare((optionB?.value ?? "").toLowerCase())
          }
          // allowClear
        />
      ) : inputType === "number" ? (
        <InputNumber onKeyPress={handleKeyPress} className="w-full" />
      ) : inputType === "switch" ? (
        <>
          <Switch
            size="small"
            // defaultChecked={(record.status = 0 ? false : true)}
            defaultChecked={
              record ? (record.status === 0 ? false : true) : true
            }
            // onChange={(checked, event) =>
            //   changePrimaryAccount(checked, event, reccord)
            // }
          />
        </>
      ) : inputType === "textArea" ? (
        <TextArea
          showCount
          maxLength={100}
          style={{
            height: 60,
            resize: "none",
          }}
        />
      ) : (
        <Input
          ref={(el) => (inputRefs.current[index] = el)}
          onKeyPress={handleKeyPress}
          placeholder={t("activities.name")}
        />
      );
    return (
      <td {...restProps}>
        {editing ? (
          <Form.Item
            name={dataIndex.toLowerCase()}
            style={{
              margin: 0,
            }}
            rules={[
              {
                required: true,
                message: `${title} ${t("table.header.isrequired")}`,
              },
            ]}
          >
            {inputNode}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };
  const edit = (record) => {
    if (record) {
      form.setFieldsValue({
        label: record.label,
        color: record.color,
        hours: record.hours,
        minutes: record.minutes,
        status: record.status,
        description: record.description,
      });
      setId(record.id);
      setColor(record.color);
    } else {
      form.setFieldsValue({
        label: "",
        color: null,
        hours: "",
        minutes: "",
        status: "",
        description: "",
      });
    }
    setEditingKey(record.key);
  };
  const cancel = (record) => {
    setEditingKey("");
    setColor("");
    setId(null);
    setData(
      data.map((item) =>
        item.id === editLabel ? { ...item, seeder: 0 } : item
      )
    );
    setEditLabel("");
    if (!record.id) {
      setData(data.filter((item) => item.key !== record.key));
    }
  };
  const save = async (key) => {
    setLoading(true);
    if (id) {
      try {
        const row = await form.validateFields();
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post(`/severities/update/${id}`, {
          ...row,
          color: row.color || "",
        });
        setEditingKey("");
        setData(
          data.map((el) =>
            el.id === res.data.data.id
              ? {
                  ...res.data.data,
                  key: res.data.data.id,
                }
              : el
          )
        );
        form.setFieldsValue({
          label: "",
          color: null,
          hours: "",
          minutes: "",
          status: "",
          description: "",
        });
        setColor("");
        setLoading(false);

        toastNotification("success", row.label + t("toasts.edit"), "topRight");
      } catch (errInfo) {
        setLoading(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    } else {
      try {
        const row = await form.validateFields();
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post("/severities", {
          ...row,
          status:
            row.status === null || row.status === ""
              ? 1
              : row.status === false
              ? 0
              : 1,
        });
        setEditingKey("");
        setData([
          ...data.filter((el) => el.id),
          { ...res.data.data, key: res.data.data.id },
        ]);
        form.setFieldsValue({
          label: "",
          color: null,
          hours: "",
          minutes: "",
          status: "",
          description: "",
        });
        setColor("");
        setLoading(false);
        toastNotification(
          "success",
          row.label + t("toasts.created"),
          "topRight"
        );
      } catch (errInfo) {
        setLoading(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    }
  };
  useEffect(() => {
    const getSeverities = async () => {
      setLoading(true);
      try {
        const {
          data: { data },
        } = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get("/severities");
        setData(data.map((el, i) => ({ ...el, key: el.id, rank: i + 1 })));
        if (data.length > 0) {
          setCount(Math.max(...data.map((el) => el.id)));
        }
        setLoading(false);
      } catch (err) {
        setLoading(false);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    };
    getSeverities();
    return () => dispatch(setSearch(""));
  }, []);
  const handleClick = (event) => {
    event.stopPropagation();
  };

  const columns = [
    {
      title: t("activities.name"),
      dataIndex: "label",
      key: "label",
      editable: true,
      sorter: (a, b) => a.label.localeCompare(b.label),
      render: (_, record) => {
        return (
          <>
            {record.seeder == 1 ? (
              <LabelTable record={record} editingKey={editingKey} edit={edit} />
            ) : (
              <div className="flex items-center space-x-1">
                <Tooltip title={t("helpDesk.changeName")}>
                  <div
                    className={` font-medium ${
                      editingKey
                        ? ""
                        : "cursor-pointer text-blue-600 hover:underline dark:text-blue-500"
                    } `}
                    onClick={(e) => {
                      if (!editingKey) {
                        setData((prev) =>
                          prev.map((el) =>
                            el.id === record.id ? { ...el, seeder: 1 } : el
                          )
                        );
                        setEditLabel(record.id);
                        edit(record);
                      }
                    }}
                  >
                    <p> {record.label} </p>
                  </div>
                  {/* <Button
                    type="link"
                    size="small"
                    shape="circle"
                    icon={<FiEdit3 />}
                    disabled={editingKey || editLabel}
                    onClick={(e) => {
                      if (!editingKey) {
                        setData((prev) =>
                          prev.map((el) =>
                            el.id === record.id ? { ...el, seeder: 1 } : el
                          )
                        );
                        setEditLabel(record.id);
                        edit(record);
                      }
                    }}
                  /> */}
                </Tooltip>
              </div>
            )}
          </>
        );
      },
    },
    {
      title: t("helpDesk.hours"),
      dataIndex: "hours",
      key: "hours",
      editable:
        data.find((el) => el.id === editingKey)?.seeder === 1 ? false : true,
      width: "150px",
      sorter: (a, b) => a.id - b.id,
      render: (_, { hours }) => (
        <div
          className={`${
            data.find((el) => el.id === editingKey)?.seeder === 1
              ? "absolute top-1/2 -translate-y-1/2 transform"
              : "relative "
          }`}
        >
          {hours}
        </div>
      ),
    },
    {
      title: t("helpDesk.minutes"),
      dataIndex: "minutes",
      key: "minutes",
      editable:
        data.find((el) => el.id === editingKey)?.seeder === 1 ? false : true,
      width: "150px",

      sorter: (a, b) => a.id - b.id,
      render: (_, { minutes }) => (
        <div
          className={`${
            data.find((el) => el.id === editingKey)?.seeder === 1
              ? "absolute top-1/2 -translate-y-1/2 transform"
              : "relative "
          }`}
        >
          {minutes}
        </div>
      ),
    },
    // {
    //   title: t("helpDesk.state"),
    //   dataIndex: "status",
    //   key: "status",
    //   editable: true,
    //   sorter: (a, b) => a.id - b.id,
    //   render: (_, reccord) => (
    //     <>
    //       <Switch
    //         size="small"
    //         defaultChecked={reccord.status === 1 ? true : false}
    //         onChange={(checked) => changeStatus(checked, reccord)}
    //         loading={loadingS}
    //       />
    //     </>
    //   ),
    // },
    // {
    //   title: "description",
    //   dataIndex: "description",
    //   key: "description",
    //   editable: true,
    //   sorter: (a, b) => a.id - b.id,
    // },
    {
      title: t("activities.color"),
      dataIndex: "color",
      key: "color",
      editable:
        data.find((el) => el.id === editingKey)?.seeder === 1 ? false : true,
      render: (_, { color }) => (
        <div
          className={`${
            data.find((el) => el.id === editingKey)?.seeder === 1
              ? "absolute top-1/2 -translate-y-1/2 transform"
              : "relative "
          }`}
        >
          <ColumnColors color={color} colors={colors} />
        </div>
      ),
    },
  ];

  const handleAdd = () => {
    setId(null);
    const ids = data.map((object) => {
      return object.id;
    });
    const newData = {
      key: Math.max(...ids) + 1,
      label: `  `,
      color: "",
      disabled: true,
    };
    setCurrentPage(Math.ceil([...data, newData].length / pageSize));
    setOldPage(Math.ceil([...data, newData].length / pageSize));
    setData([...data, newData]);
    form.setFieldsValue({
      label: "",
      color: null,
      hours: "",
      minutes: "",
      status: "",
      description: "",
    });
    setEditingKey(Math.max(...ids) + 1);
    setCount(Math.max(...ids) + 1);
  };
  const onRow = () => {};
  const filteredData = data.filter((item) => {
    return item.label.toLowerCase().includes(search.toLowerCase());
  });
  return (
    <Space direction="vertical" style={{ width: "100%" }}>
      <Header
        active={"2"}
        editingKey={editingKey}
        handleAdd={handleAdd}
        btnText={t("helpDesk.addSeverity")}
        disabled={loading ? true : editingKey ? true : search ? true : false}
      />

      <NewTableDraggable
        columns={columns}
        setLoading={setLoading}
        isEditing={isEditing}
        data={filteredData}
        setData={setData}
        loading={loading}
        save={save}
        edit={edit}
        EditableCell={EditableCell}
        onFinishFailed={onFinishFailed}
        cancel={cancel}
        form={form}
        apiRank="/rank-severities"
        editingKey={editingKey}
        api="severities"
        onRow={onRow}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        pageSize={pageSize}
        setPageSize={setPageSize}
      />
      <BottomButtonAddRow
        editingKey={editingKey}
        data={data}
        text={t("helpDesk.addSeverity")}
        handleAdd={handleAdd}
        loading={loading}
        search={search || ""}
      />
    </Space>
  );
};
export default Severities;
