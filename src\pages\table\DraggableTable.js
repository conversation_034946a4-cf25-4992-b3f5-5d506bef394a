/**
 * * @name DraggableTable
 *
 * @description The DraggableTable allows the user to configure fields parameters, update its rank in its designated form.
 *
 *
 * @param {Function} setUpdateFieldProps Sets "updateFieldProps" state.
 * @param {Array} groupList List of fields groups.
 * @param {Function} setGroupList Sets "groupsList" state.
 * @param {Number} selectedGroupId The id of the selected field group.
 * @param {Function} setSelectedGroupId Sets "selectedGroupId" state.
 * @param {String} editingKey This holds the key of the selected row in groups table (enables update fields label directly on the table).
 * @param {Function} setEditingKey Sets "editingKey" state.
 * @param {String} selectedRowKey Holds the id of the selected group.
 * @param {Function} setSelectedRowKey Sets "selectedRowKey" state.
 * @param {Array} modulesList List of modules.
 * @param {String} source The source where the component is being called from.
 * @param {Boolean} openDrawerFromStage Open the drawer in viewsphere.
 * @param {String} familyId The id of the family from drawer.
 * @param {Function} setIsUpdateFromVue360 Re-trigger the get fields api on add/update field from viewsphere.
 *
 * @returns {JSX.Element} The fields and groups tables.
 */

import React, {
  useCallback,
  useRef,
  useState,
  useEffect,
  useMemo,
  useLayoutEffect,
} from "react";
import {
  Tag,
  Table,
  Button,
  message,
  Switch,
  Form,
  Input,
  Tooltip,
  Typography,
  Row,
  Col,
  Space,
  Popover,
  Select,
} from "antd";
import { DndContext } from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { useSelector, useDispatch } from "react-redux";
import { useLocation, useParams } from "react-router-dom";
import {
  LoadingOutlined,
  PlusOutlined,
  FileTextFilled,
  HolderOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { FiSearch } from "react-icons/fi";

import {
  RESET_FIELD_STATE,
  SET_CONTACT_INFO_FROM_DRAWER,
} from "../../new-redux/constants";
import { updateFieldParameter } from "../../new-redux/actions/fields.actions/updateFieldParameter";
import { updateAlias } from "../../new-redux/actions/fields.actions/updateAlias";
import MainService from "../../services/main.service";
import { toastNotification } from "../../components/ToastNotification";
import { DeleteSpecificField } from "../../new-redux/actions/fields.actions/deleteSpecificField";
import { displayRightIcon } from "../../utils/displayIcon";
import { createNewGroup } from "../../new-redux/actions/fields.actions/createNewGroup";
import { updateGroup } from "../../new-redux/actions/fields.actions/updateGroup";
import NewTableDraggable from "../../components/NewTableDraggable";
import { updateFieldsRank } from "../../new-redux/actions/fields.actions/updateFieldRank";
import {
  handleDrawerTitle,
  setOpenFieldDrawer,
} from "../../new-redux/actions/fields.actions/fieldDrawer";
import { useWindowSize } from "pages/clients&users/components/WindowSize";
import DropDownCrud from "components/DropDownCrud";
import SearchInTable from "pages/components/Search";
import { familyIcons } from "pages/components/DetailsProfile/ViewSphere2";

// Draggable row in fields table
const TableRow = ({ children, ...props }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    setActivatorNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: props["data-row-key"],
  });

  // Custom styles when drag is active.
  const style = {
    ...props.style,
    transform: CSS.Transform.toString(
      transform && {
        ...transform,
        scaleY: 1,
      }
    ),
    transition,
    ...(isDragging
      ? {
          position: "relative",
          zIndex: 9999,
        }
      : {}),
  };
  return (
    <tr {...props} ref={setNodeRef} style={style} {...attributes}>
      {React.Children.map(children, (child) => {
        if (child.key === "sort") {
          return React.cloneElement(child, {
            children: (
              <HolderOutlined
                ref={setActivatorNodeRef}
                style={{
                  touchAction: "none",
                  cursor: isDragging ? "grabbing" : "grab",
                  padding: 0,
                }}
                {...listeners}
              />
            ),
          });
        }
        return child;
      })}
    </tr>
  );
};

//Draggable Table main component.
const DraggableTable = ({
  setUpdateFieldProps,
  groupList,
  setGroupList,
  setSelectedGroupId,
  setEditingKey,
  editingKey,
  selectedGroupId,
  setSelectedRowKey,
  selectedRowKey,
  modulesList,
  source = "",
  openDrawerFromStage,
  familyId = null,
  setIsUpdateFromVue360 = () => {},
}) => {
  // Extract fields data from redux store
  const {
    fields,
    updateFieldParamSuccess,
    isLoading,
    updateFieldParamError,
    isFieldRemoved,
    updateFieldAliasSuccess,
    updateFieldAliasLoading,
    isGroupCreated,
    isGroupDeleted,
    isCreateGroupLoading,
    isGroupUpdated,
    updateGroupLoading,
    isGroupRankUpdated,
    isRankFieldUpdated,
    updateFieldRankLoading,
    errors,
  } = useSelector((state) => state?.fields);
  const { activeMenu360 } = useSelector((state) => state?.vue360);
  const { contactHeaderInfo: contactInfo } = useSelector(
    (state) => state?.contacts
  );
  const { contactInfoFromDrawer, openView360InDrawer } = useSelector(
    (state) => state?.vue360
  );
  const { pathname } = useLocation();
  // Local states.
  const [datas, setDatas] = useState([]);
  const [rowsCount, setRowsCount] = useState(0);
  const [searchGrpQuery, setSearchGrpQuery] = useState("");
  const [loadChangeGroupRank, setLoadChangeGroupRank] = useState(false);
  const [loadPipelines, setLoadPipelines] = useState(false);
  const [groupIdToUpdate, setGroupIdToUpdate] = useState(null);
  const [editingAliasKey, setEditingAliasKey] = useState("");
  const [familyProducts, setFamilyProducts] = useState([]);
  const [pipelines, setPipelines] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [loadingSwitchId, setLoadingSwitchId] = useState(null);
  // Instances declaration.
  const GroupInputRefs = useRef([]);
  const deleteBtnRef = useRef();
  const dispatch = useDispatch();
  const { id } = useParams();
  const [form] = Form.useForm();
  const { types } = useSelector((state) => state.types);
  const { families } = useSelector((state) => state.families);
  const { search } = useSelector((state) => state.form);
  const [t] = useTranslation("common");
  const windowSize = useWindowSize();
  let arrayRanksRef = [];
  let updateRankFunctionOccurence = 0;

  // This is used to enable edit group label editable on the table.
  const isEditing = (record) => record.key == editingKey;

  // Add new group
  const onFinish = (values) => {
    saveNewGroup(values);
  };

  const onFinishFailed = (errors) => {};

  // Refresh data source whenever selected group changed.
  const handleDataSource = useCallback(() => {
    let selectedGroup =
      fields?.groups &&
      fields?.groups.find((element) => element?.id == selectedRowKey);
    //This is when using calling fields table on drawer (in viewsphere)
    if (source === "drawer") {
      if (!selectedRowKey) {
        if (selectedGroup == undefined) {
          setDatas(fields?.groups && fields?.groups[0]?.fields);
          setSelectedRowKey(fields?.groups && fields?.groups[0]?.id);
          setSelectedGroupId(fields?.groups && fields?.groups[0]?.id);
        } else {
          setDatas(selectedGroup?.fields);
          setSelectedGroupId(selectedGroup?.id);
        }
      } else {
        setDatas(
          fields?.groups &&
            fields?.groups.find((el) => el.id === selectedGroupId)?.fields
        );
      }
    } else {
      if (selectedGroup === undefined) {
        setDatas(fields?.groups && fields?.groups[0]?.fields);
        setSelectedRowKey(fields?.groups && fields?.groups[0]?.id);
        setSelectedGroupId(fields?.groups && fields?.groups[0]?.id);
      } else {
        setDatas(selectedGroup?.fields);
        setSelectedGroupId(selectedGroup?.id);
      }
    }
    setGroupList(fields?.groups && fields?.groups);
    let ids = fields?.groups && fields?.groups.map((element) => element?.id);
    setRowsCount(ids && ids.sort((a, b) => a - b)[ids.length - 1]);
  }, [fields?.groups, selectedRowKey, search, source]);

  // The actual data that will be displayed.
  const dataSource = useMemo(() => {
    const searchLower = search?.toLowerCase();
    const filteredData = search
      ? datas?.filter((field) =>
          field?.alias?.toLowerCase()?.includes(searchLower)
        )
      : datas;
    return filteredData?.map((item, i) => ({
      key: item?.id,
      rank: item?.rank,
      title: item,
      alias: item,
      associateGroup: item?.field_group_id,
      visible: item,
      unique: item,
      required: item,
      readOnly: item,
      guest: item,
      type: item,
      actions: item,
    }));
  }, [search, datas]);

  // Handle edit field function. (open edit drawer)
  const editFieldHandler = (record) => {
    dispatch(
      handleDrawerTitle(
        t("fields_management.update_field_drawer-title", {
          fieldLabel: record?.alias,
        })
      )
    );
    dispatch(setOpenFieldDrawer(true));
    setUpdateFieldProps(record);
  };

  // Handle delete field function.
  const handleDeleteField = (id) => {
    dispatch(DeleteSpecificField(id));
    if (activeMenu360 === "4") {
      setIsUpdateFromVue360(true);
    }
  };

  // Field details popover.
  const ModuleFieldDetails = ({ data }) => {
    return (
      <div className="w-[100%]">
        {data?.field_display?.length > 0 && (
          <section className="relative flex w-full list-none flex-row items-center justify-between p-1">
            <label className=" text-[#6b7280]">
              {t("fields_management.display")}
            </label>
            <p className="ml-4">
              {data?.field_display?.map((el, index) => (
                <>
                  <span className="text-xs">
                    {el?.label?.replaceAll("_", " ")}
                  </span>
                  {index !== data.field_display.length - 1 && (
                    <span className="">/</span>
                  )}
                </>
              ))}
            </p>
          </section>
        )}
        {data?.module_type && (
          <>
            <section className="relative flex w-full list-none flex-row items-center justify-between p-1">
              <label className="text-[#6b7280]">
                {t("fields_management.moduleName")}
              </label>
              <p>
                {modulesList &&
                  modulesList.find(
                    (element) =>
                      Number(element?.id) === Number(data?.field_module)
                  )?.label}
              </p>
            </section>
            <section className="relative flex w-full list-none flex-row items-center justify-between p-1">
              <label className="text-[#6b7280]">
                {t("fields_management.displayedType")}
              </label>
              <p>
                {displayRightIcon(
                  types &&
                    types.find(
                      (element) =>
                        Number(element?.id) === Number(data?.module_type)
                    )?.fieldType,
                  2,
                  4
                )}
                {types &&
                  types.find(
                    (element) =>
                      Number(element?.id) === Number(data?.module_type)
                  )?.fieldType}
              </p>
            </section>
          </>
        )}
        <section className="mt-5 flex justify-center text-xs text-[#9ca3af]">
          <InfoCircleOutlined style={{ marginRight: "5px" }} />
          {t("fields_management.aliasTooltip")}
        </section>
      </div>
    );
  };

  // Restrict the event.
  const handleClick = (event) => {
    event.stopPropagation();
  };

  // Reset 'contactInfo' reducer when navigating to fields paths.
  useLayoutEffect(() => {
    if (pathname.includes("/settings/fields")) {
      dispatch({
        type: "RESET_CONTACT_HEADER_INFO",
      });
      dispatch({
        type: SET_CONTACT_INFO_FROM_DRAWER,
        payload: {},
      });
    }
  }, []);

  //Retrieve pipelines and stages.
  const getPipelines = useCallback(async () => {
    let response;

    try {
      setLoadPipelines(true);

      if (familyId) {
        response = await MainService.getPipelinesByFamily(familyId);
      } else {
        const familyId = contactInfo?.family_id
          ? contactInfo?.family_id
          : families && families.find((el) => el?.label === id)?.id;
        response = await MainService.getPipelinesByFamily(familyId);
      }
      setPipelines(
        response?.data?.data.map((pipeline) => ({
          label: pipeline?.label,
          value: pipeline?.pipeline_key,
          uid: pipeline?.id,
          options:
            pipeline?.stages &&
            pipeline?.stages.map((stage) => ({
              label: stage?.label,
              value: stage?.id,
            })),
        }))
      );
      setLoadPipelines(false);
    } catch (error) {
      setLoadPipelines(false);
      console.log(`Error ${error}`);
    }
  }, [id, familyId, contactInfo?.family_id]);

  useEffect(() => {
    getPipelines();
  }, [getPipelines]);

  //open drawer from stage
  useEffect(() => {
    if (source === "drawer" && !openDrawerFromStage) {
      setEditingKey("");
      setSelectedRowKey("");
      setSelectedGroupId(null);
    }
  }, [source, openDrawerFromStage]);

  //Update the config switches on table.
  const updateSwitches = (parameter, id) => {
    // Set the ID of the switch being interacted with
    setLoadingSwitchId(id);
    dispatch(updateFieldParameter(parameter, id)).finally(() =>
      setLoadingSwitchId(null)
    );
    // Reset the loading state after the operation
  };

  // Fields table columns.
  const columns = useMemo(
    () => [
      {
        key: "sort",
        width: 15,
        className: "remove_border_right_tr mr-0",
      },
      {
        title: "Alias",
        dataIndex: "alias",
        key: "alias",
        align: "left",
        ellipsis: true,
        width: 170,
        editable: true,
        render: (record) => {
          let newString = record?.alias;
          const isEllipsisNeeded = record?.alias?.length > 22;
          return (
            <div className="flex justify-between">
              <div className="flex flex-row items-center">
                <Popover
                  key={record?.id}
                  content={<ModuleFieldDetails data={record} />}
                  autoAdjustOverflow
                  trigger={["hover"]}
                  title={
                    <div className="flex w-full items-center justify-between">
                      {record?.alias}{" "}
                      <Tag
                        color="processing"
                        bordered={false}
                        icon={displayRightIcon(
                          types &&
                            types.find(
                              (element) =>
                                Number(element?.id) ===
                                Number(record?.field_type_id)
                            )?.fieldType,
                          4,
                          4
                        )}
                      >
                        {types &&
                          types.find(
                            (element) =>
                              Number(element?.id) ===
                              Number(record?.field_type_id)
                          )?.fieldType}
                      </Tag>
                    </div>
                  }
                  overlayStyle={{
                    minWidth: 250,
                  }}
                >
                  <span className="m-0 pr-1 text-[#0e7490]">
                    {displayRightIcon(
                      types &&
                        types.find(
                          (element) =>
                            Number(element?.id) ===
                            Number(record?.field_type_id)
                        )?.fieldType,
                      4,
                      4
                    )}
                  </span>
                  <Typography.Text
                    style={{
                      cursor: "text",
                      maxWidth: "130px",
                      marginLeft:
                        !pathname?.includes("settings/fields") &&
                        contactInfo?.id
                          ? "15px"
                          : "25px",
                    }}
                    ellipsis={isEllipsisNeeded}
                    editable={{
                      text: record?.alias,
                      triggerType: "text",
                      onChange: (key) => {
                        newString = key;
                        setEditingAliasKey(record?.id);
                      },
                      onEnd: () => {
                        let formData = new FormData();
                        formData.append("alias", newString?.trim());
                        if (newString?.trim() !== "") {
                          dispatch(
                            updateAlias({ fieldId: record?.id, formData })
                          );
                          if (activeMenu360 === "4") {
                            setIsUpdateFromVue360(true);
                          }
                        }
                      },
                      icon:
                        updateFieldAliasLoading &&
                        editingAliasKey == record?.id ? (
                          <LoadingOutlined />
                        ) : null,
                    }}
                  >
                    {record?.alias}
                  </Typography.Text>
                </Popover>
                {record?.description ? (
                  <Tooltip
                    title={
                      <>
                        <span className="text-xs italic">Description</span>
                        <p>{record?.description}</p>
                      </>
                    }
                  >
                    <FileTextFilled
                      style={{ marginLeft: "5px", color: "#ff9c6e" }}
                    />
                  </Tooltip>
                ) : null}
              </div>
              <div className="opacity-0 group-hover:opacity-100">
                <DropDownCrud
                  record={record}
                  edit={editFieldHandler}
                  handleDelete={handleDeleteField}
                  form={form}
                  cancel={() => {}}
                  isEditing={isEditing}
                  data={datas}
                  setData={setDatas}
                  editingKey={editingKey}
                  api="fields-management"
                  source="draggableTable"
                  confirmLoading={false}
                  fieldType={"text"}
                  updateOptionField={1}
                />
              </div>
            </div>
          );
        },
      },
      {
        title: t("table.header.hidden"),
        dataIndex: "visible",
        key: "visible",
        align: "center",
        width: 80,
        render: (props) => (
          <Switch
            key={props?.id}
            size="small"
            onChange={() => updateSwitches("hidden", props?.id)}
            defaultChecked={props?.hidden}
            loading={loadingSwitchId === props?.id}
            checked={props?.hidden}
          />
        ),
      },
      {
        title: t("table.header.uniqueValue"),
        dataIndex: "unique",
        key: "unique",
        align: "center",
        width: 80,
        render: (props) => (
          <Switch
            key={props?.id}
            size="small"
            onChange={() => updateSwitches("uniqueValue", props?.id)}
            defaultChecked={props?.uniqueValue}
            loading={loadingSwitchId === props?.id}
            checked={props?.uniqueValue}
          />
        ),
      },
      {
        title: t("table.header.required"),
        dataIndex: "required",
        key: "required",
        align: "center",
        width: 80,
        render: (props) => (
          <Switch
            key={props?.id}
            size="small"
            onChange={() => updateSwitches("required", props?.id)}
            defaultChecked={props?.required}
            loading={loadingSwitchId === props?.id}
            checked={props?.required}
          />
        ),
      },
      {
        title: t("fields_management.readOnly"),
        dataIndex: "readOnly",
        key: "readOnly",
        align: "center",
        width: 80,
        render: (props) => (
          <Switch
            key={props?.id}
            size="small"
            onChange={() => updateSwitches("read_only", props?.id)}
            defaultChecked={props?.read_only}
            loading={loadingSwitchId === props?.id}
            checked={props?.read_only}
          />
        ),
      },
      {
        title: t("fields_management.showToGuest"),
        dataIndex: "guest",
        key: "guest",
        align: "center",
        width: 80,
        render: (props) => (
          <Switch
            key={props?.id}
            size="small"
            onChange={() => updateSwitches("guest", props?.id)}
            defaultChecked={props?.guest}
            loading={loadingSwitchId === props?.id}
            checked={props?.guest}
          />
        ),
      },
    ],
    [
      t,
      types,
      updateFieldAliasLoading,
      editingAliasKey,
      pathname,
      contactInfo?.id,
      loadingSwitchId,
      activeMenu360,
      setIsUpdateFromVue360,
      updateSwitches,
      editFieldHandler,
      handleDeleteField,
      isEditing,
      datas,
      setDatas,
      editingKey,
    ]
  );

  // Memoized dataSource for the Table to prevent unnecessary re-renders
  const memoizedDataSource = useMemo(() => dataSource, [dataSource]);

  // Groups table cols. ("getColumnSearchProps" functions that handles search by query).
  const groupsTableColumns = [
    {
      title: t("fields_management.groupNameCol"),
      dataIndex: "groupName",
      key: "groupName",
      editable: true,
      render: (record) => record?.label,
    },
    {
      title: "Stage",
      dataIndex: "stage",
      key: "stage",
      editable: true,
      render: (_, record) => {
        let correspondingPipeline =
          pipelines &&
          pipelines.find(
            (pipeline) =>
              Number(pipeline?.uid) === Number(_?.stage?.pipeline_id) ||
              Number(pipeline?.uid) === Number(_?.pipeline_id)
          );
        let stage =
          pipelines &&
          pipelines.map(
            (el) =>
              el?.options.find(
                (stage) =>
                  Number(stage?.value) ===
                  Number(record?.stage?.stage?.id || record?.stage?.stage_id)
              )?.label
          );
        return (
          record?.system !== 1 &&
          (stage?.filter((el) => el !== undefined)[0] ? (
            <div
              className="flex cursor-pointer items-center hover:underline"
              onClick={(e) => {
                editGroupField(record);
                handleClick(e);
              }}
            >
              <Tooltip title={t("fields_management.aliasTooltip")}>
                <span>{`${correspondingPipeline?.label}/${
                  stage?.filter((el) => el !== undefined)[0]
                }`}</span>
              </Tooltip>
            </div>
          ) : (
            <Typography.Link
              onClick={(e) => {
                editGroupField(record);
                handleClick(e);
              }}
            >
              {t("fields_management.addPipeline")}
            </Typography.Link>
          ))
        );
      },
    },
  ];

  //For products family, there is extra fields on groups table
  const productTableColumns = [
    {
      title: t("fields_management.groupNameCol"),
      dataIndex: "groupName",
      key: "groupName",
      editable: true,
      ellipsis: true,
      render: (record) => record?.label,
    },
    {
      title: "Type",
      dataIndex: "product_type",
      key: "product_type",
      editable: true,
      ellipsis: true,
      render: (_, record) => (
        <div
          className="flex cursor-pointer items-center hover:underline"
          onClick={(e) => {
            editGroupField(record);
            handleClick(e);
          }}
        >
          <Tooltip title="Click to update">
            <span>{record?.product_type?.product?.label}</span>
          </Tooltip>
        </div>
      ),
    },
    {
      title: "Module",
      dataIndex: "product_modules",
      key: "product_modules",
      width: 100,
      editable: true,
      ellipsis: true,
      render: (_, record) => (
        <div className="flex items-center justify-between">
          <span>{record?.product_modules?.relatif_module?.label}</span>
        </div>
      ),
    },
  ];

  // Handle open drawer function.
  const openDrawer = () => {
    dispatch(
      handleDrawerTitle(t("fields_management.create_field_drawer-title"))
    );
    dispatch(setOpenFieldDrawer(true));
  };

  // Handle refresh dataSource side effect.
  useEffect(() => {
    handleDataSource();
  }, [handleDataSource]);

  // Show a notification on update parameter (change switch button).
  useEffect(() => {
    if (updateFieldParamSuccess === true) {
      message.success(t("toasts.fieldParamSuccess"), 4.5);
      // Reset redux state after 3 seconds.
      let timer = setTimeout(() => {
        dispatch({ type: RESET_FIELD_STATE });
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [dispatch, updateFieldParamSuccess]);

  // Fix double click on remove icon.
  useEffect(() => {
    if (deleteBtnRef && deleteBtnRef.current) {
      deleteBtnRef.current.focus();
    }
  });

  // Show a notification on update alias.
  useEffect(() => {
    if (updateFieldAliasSuccess === true) {
      message.success(t("fields_management.updateAliasSuccess"), 4.5);
      setEditingKey("");
      // Reset redux state after 3 seconds.
      let timer = setTimeout(() => {
        dispatch({ type: RESET_FIELD_STATE });
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [dispatch, updateFieldAliasSuccess]);

  // Display a toast notification whenever a field is removed.
  useEffect(() => {
    if (isFieldRemoved === true) {
      toastNotification("success", t("toasts.fieldDeleted"), "bottomRight", 3);
      // Reset redux state after 3 seconds.
      let timer = setTimeout(() => {
        dispatch({ type: RESET_FIELD_STATE });
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [dispatch, isFieldRemoved]);

  // Display a toast notification whenever a group is removed.
  useEffect(() => {
    if (isGroupDeleted === true) {
      toastNotification("success", t("toasts.groupDeleted"), "bottomRight", 3);
      setSelectedRowKey(selectedGroupId);
      // Reset redux state after 3 seconds.
      let timer = setTimeout(() => {
        dispatch({ type: RESET_FIELD_STATE });
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [dispatch, isGroupDeleted]);

  // Display a toast notification whenever a group ranking is changed.
  useEffect(() => {
    if (isGroupRankUpdated === true) {
      // Reset redux state after 3 seconds.
      let timer = setTimeout(() => {
        dispatch({ type: RESET_FIELD_STATE });
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [dispatch, isGroupRankUpdated]);

  // Show a notification if an error occurs on update parameter (change switch button).
  useEffect(() => {
    if (
      updateFieldParamError === true &&
      errors?.response?.data?.message ===
        "This field already contains duplicate data."
    ) {
      toastNotification(
        "error",
        t("toasts.duplicatedDataError"),
        "bottomRight",
        4.5
      );
      // Reset redux state after 3 seconds.
      let timer = setTimeout(() => {
        dispatch({ type: RESET_FIELD_STATE });
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [dispatch, updateFieldParamError]);

  // Handle add new row on group table (new group).
  const handleAdd = () => {
    const newData = {
      id: rowsCount + 1,
      label: ``,
      staticNewRow: true,
    };
    setCurrentPage(Math.ceil([...groupList, newData].length / pageSize));
    setGroupList([...groupList, newData]);
    form.setFieldsValue({
      label: "",
    });
    setEditingKey(rowsCount + 1);
    setRowsCount(rowsCount + 1);
  };

  // Submit form on press "ENTER" key.
  const handleKeyPress = (event) => {
    if (event.key === "Enter") {
      form.submit();
    }
  };

  // Set focus on group input.
  useEffect(() => {
    GroupInputRefs.current.forEach((input) => {
      input?.focus();
    });
  }, [groupList, id, contactInfo?.family_id]);

  //Retrieve family products elements.
  const getFamilyProducts = async () => {
    try {
      const response = await MainService.getFamilyProducts();
      setFamilyProducts(response?.data?.data);
    } catch (error) {
      console.log(`Error ${error}`);
    }
  };

  useEffect(() => getFamilyProducts(), []);

  // Editable cell component in group table.
  const EditableGroupFormCell = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    const inputNode =
      dataIndex === "product_type" ? (
        <Select
          placeholder="Select Type"
          showSearch
          options={
            familyProducts &&
            familyProducts.map((product, i) => ({
              label: product?.label,
              value: product?.id,
            }))
          }
          optionFilterProp="children"
          filterOption={(input, option) =>
            option?.label.toLowerCase().includes(input.toLowerCase())
          }
          allowClear
          popupMatchSelectWidth={false}
        />
      ) : dataIndex === "product_modules" ? (
        <Select
          placeholder="Select Module"
          showSearch
          options={
            families &&
            families.map((family) => ({
              label: family?.label,
              value: family?.id,
            }))
          }
          optionFilterProp="children"
          filterOption={(input, option) =>
            option?.label.toLowerCase().includes(input.toLowerCase())
          }
          allowClear
          popupMatchSelectWidth={false}
        />
      ) : dataIndex === "stage" ? (
        <Select
          allowClear
          options={pipelines && pipelines}
          style={{ width: "200px" }}
          placeholder={t("fields_management.groupStagePlaceholder")}
        />
      ) : (
        <Input
          ref={(el) => (GroupInputRefs.current[index] = el)}
          onKeyPress={handleKeyPress}
          placeholder={t("fields_management.groupNamePlaceholder")}
        />
      );

    return (
      <td {...restProps}>
        {editing && record?.system !== 1 ? (
          <Form.Item
            name={dataIndex}
            style={{
              margin: 0,
            }}
            rules={[
              {
                required: dataIndex === "groupName" && true,
                message: `${title} ${t("table.header.isrequired")}`,
              },
            ]}
          >
            {inputNode}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };

  // Trigger select/deselect a row.
  const onSelectChange = (selectedRowKeys) => {
    setSelectedRowKey(selectedRowKeys[0]);
  };

  // Adds className to the selected row.
  const rowClassName = (record) => {
    return selectedRowKey == record.key ? "selected-row" : "";
  };

  // Remove new row from the view.
  const cancelNewRow = (record) => {
    setEditingKey("");
    if (record.staticNewRow) {
      setGroupList(
        groupList.filter((item) =>
          item?.staticNewRow ? item.id != record.key : item.key != record.key
        )
      );
    }
    form.setFieldsValue({
      groupName: "",
    });
    setGroupIdToUpdate(null);
  };

  // Edit group
  const editGroupField = (record) => {
    if (record) {
      if (id !== "Product") {
        form.setFieldsValue({
          groupName: record?.groupName?.label,
          stage:
            Number(record?.stage_id) ||
            Number(record?.stage?.stage?.id) ||
            null,
        });
        setGroupIdToUpdate(record?.key);
      } else if (id === "Product") {
        form.setFieldsValue({
          groupName: record?.groupName?.label,
          product_modules: record?.product_type?.relatif_module?.id,
          product_type: record?.product_modules?.product?.id,
        });
        setGroupIdToUpdate(record?.key);
      }
      setEditingKey(record.key);
    } else {
      form.setFieldsValue({
        groupName: "",
        stage: null,
      });
    }
  };

  // Handle Save/Update group (dispatch redux store functions).
  const saveNewGroup = async (values) => {
    let familyId = contactInfo?.family_id
      ? contactInfo?.family_id
      : families && families.find((element) => element?.label == id)?.id;
    const existId = contactInfo?.family_id
      ? familyIcons(t).find((el) => el.key === contactInfo?.family_id)?.pathname
      : id;
    let createGroupPayload =
      existId === "Product"
        ? {
            family_id: familyId,
            label: values?.groupName,
            family_product_id: values?.product_type,
            relatif_module_id: values?.product_modules,
          }
        : {
            family_id: familyId,
            label: values?.groupName,
            stage_id: values?.stage,
          };

    let updateGroupPayload =
      existId === "Product"
        ? {
            label: values?.groupName,
            family_product_id:
              typeof values?.product_type === "undefined"
                ? ""
                : values?.product_type,
            relatif_module_id:
              typeof values?.product_modules === "undefined"
                ? ""
                : values?.product_modules,
          }
        : {
            label: values?.groupName,
            stage_id: values?.stage,
          };

    if (groupIdToUpdate === null) {
      dispatch(createNewGroup(createGroupPayload));
    } else {
      dispatch(updateGroup(groupIdToUpdate, updateGroupPayload));
    }
  };

  // Show notification on add new Group + reset inputs + deactivate disable state.
  useEffect(() => {
    if (isGroupCreated === true) {
      toastNotification(
        "success",
        t("toasts.groupCreated"),
        "bottomRight",
        "4.5"
      );
      setEditingKey("");
      form.resetFields();
      // Reset redux store after 3 seconds.
      let timer = setTimeout(() => {
        dispatch({ type: RESET_FIELD_STATE });
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [dispatch, isGroupCreated]);

  // Show notification on add new Group + reset inputs + deactivate disable state.
  useEffect(() => {
    if (isGroupUpdated === true) {
      toastNotification(
        "success",
        t("toasts.groupUpdated"),
        "bottomRight",
        "4.5"
      );
      setEditingKey("");
      form.resetFields();
      setGroupIdToUpdate(null);
      // Reset redux store after 3 seconds.
      let timer = setTimeout(() => {
        dispatch({ type: RESET_FIELD_STATE });
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [dispatch, isGroupUpdated]);

  // Show notification on successfully change a field's rank.
  useEffect(() => {
    if (isRankFieldUpdated === true) {
      message.success(t("toasts.rankChanged"), 3);
      // Reset redux store after 3 seconds.
      let timer = setTimeout(() => {
        dispatch({ type: RESET_FIELD_STATE });
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [dispatch, isRankFieldUpdated]);

  //Handle format dataSource for groups table.
  const dataTable = useCallback(() => {
    const existId = contactInfo?.family_id
      ? familyIcons(t).find((el) => el.key === contactInfo?.family_id)?.pathname
      : id;

    if (existId && existId === "Product") {
      return (
        groupList &&
        groupList
          ?.filter((group) =>
            group?.label?.toLowerCase()?.includes(searchGrpQuery?.toLowerCase())
          )
          .map((element) => ({
            ...element,
            key: element?.id,
            groupName: element,
            product_type: element,
            product_modules: element,
          }))
      );
    } else {
      return (
        groupList &&
        groupList
          ?.filter((group) =>
            group?.label?.toLowerCase()?.includes(searchGrpQuery?.toLowerCase())
          )
          ?.map((element) => ({
            ...element,
            key: element?.id,
            groupName: element,
            stage: element,
          }))
      );
    }
  }, [groupList, id, searchGrpQuery, contactInfo?.family_id]);

  // Handle click on table's row.
  const onRow = (record) => {
    return {
      onClick: () => {
        if (record.id && editingKey == "") {
          setSelectedGroupId(record?.id);
          setSelectedRowKey(record?.id);
          onSelectChange([record?.id]);
        }
      },
    };
  };

  // Handle drag and drop (refer to https://docs.dndkit.com/api-documentation/context-provider#event-handlers)
  const onDragEnd = ({ active, over }) => {
    if (active?.id != over?.id) {
      if (
        datas[datas?.findIndex((i) => i?.id == active?.id)]?.reference == 1 ||
        datas[datas?.findIndex((i) => i?.id == over?.id)]?.reference == 1
      ) {
        return;
      } else {
        updateRank(
          arrayMove(
            dataSource,
            dataSource?.findIndex((i) => i?.key == active?.id),
            dataSource?.findIndex((i) => i?.key == over?.id)
          )
        );
        setDatas((previous) => {
          const activeIndex = previous?.findIndex((i) => i?.id == active?.id);
          const overIndex = previous?.findIndex((i) => i?.id == over?.id);
          return arrayMove(previous, activeIndex, overIndex);
        });
      }
    }
  };

  // Update rank api call.
  const updateRank = async (array) => {
    var formData = new FormData();
    array &&
      array.forEach((item, i) => {
        if (item) {
          let key = `rank[${item?.key}]`;
          let value = i + 1;
          formData.append(key, value);
        }
      });
    let familyId =
      families && families.filter((element) => element?.label == id)[0]?.id;

    let payload = {
      familyId: pathname.includes("settings/fields")
        ? familyId
        : openView360InDrawer
        ? contactInfoFromDrawer?.family_id
        : contactInfo?.family_id
        ? contactInfo?.family_id
        : "",
      groupId: selectedGroupId,
      formData,
    };
    updateRankFunctionOccurence += 1;
    try {
      dispatch(updateFieldsRank(payload));
    } catch (error) {
      console.log(`Error ${error}`);
    }
  };

  return (
    <Row style={{ paddingTop: "1rem" }}>
      {/* Fields Groups Table*/}
      <Col
        xs={24}
        sm={24}
        md={24}
        lg={9}
        style={{
          padding: "0 5px",
          marginLeft: "15px",
        }}
      >
        <Space direction="vertical" style={{ width: "100%" }}>
          <div className="flex justify-between">
            <div>
              <Typography.Title
                level={4}
                style={{
                  marginRight: "8px",
                  color: "#2253d5",
                  paddingTop: "2px",
                  paddingBottom: "2px",
                  paddingRight: "10px",
                }}
              >
                {t("fields_management.fieldsGroupsTitle")}
              </Typography.Title>
              <Input
                prefix={<FiSearch className="text-slate-400" />}
                onChange={(e) => {
                  setSearchGrpQuery(e?.target?.value);
                }}
                placeholder={t("fields_management.searchGrpPlaceholder")}
                style={{ width: "250px" }}
                value={searchGrpQuery}
                allowClear
              />
            </div>
            <div className="flex items-end">
              <Button
                icon={<PlusOutlined />}
                type="default"
                onClick={() => {
                  handleAdd();
                }}
                disabled={editingKey !== "" ? true : false}
              >
                {t("fields_management.addGroup")}
              </Button>
            </div>
          </div>
        </Space>
        <div className="pt-[13px]">
          <NewTableDraggable
            data={dataTable()}
            setData={setGroupList}
            EditableCell={EditableGroupFormCell}
            showHeader={true}
            form={form}
            isEditing={isEditing}
            editingKey={editingKey}
            columns={
              id === "Product" ? productTableColumns : groupsTableColumns
            }
            cancel={cancelNewRow}
            edit={editGroupField}
            api="fields-groups"
            rowSelection={false}
            loading={
              isCreateGroupLoading ||
              loadChangeGroupRank ||
              updateGroupLoading ||
              isLoading
            }
            apiRank="/change-field-group-rank"
            setLoading={setLoadChangeGroupRank}
            onRow={onRow}
            rowClassName={rowClassName}
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            pageSize={pageSize}
            setPageSize={setPageSize}
          />
        </div>
      </Col>
      {/* Fields Table*/}
      <Col
        xs={24}
        sm={24}
        md={24}
        lg={14}
        style={{
          padding: "0 5px",
          marginLeft: "12px",
        }}
      >
        <Space direction="vertical" style={{ width: "100%" }}>
          <div className="flex justify-between">
            <div>
              <Typography.Title
                level={4}
                style={{
                  marginRight: "8px",
                  color: "#2253d5",
                  paddingTop: "2px",
                  paddingBottom: "2px",
                  paddingRight: "10px",
                }}
              >
                {t("fields_management.fieldsTitle")}
              </Typography.Title>
              <SearchInTable
                key="searchInFields"
                placeholder={"fields_management.searchFieldPlaceholder"}
              />
            </div>
            <div className="flex items-end">
              <Button
                icon={<PlusOutlined />}
                type="default"
                onClick={openDrawer}
                disabled={editingKey ? true : false}
              >
                {t("fields_management.addField")}
              </Button>
            </div>
          </div>
        </Space>
        <Space direction="vertical" style={{ width: "100%" }}>
          <Form
            form={form}
            id="alias-form"
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
          >
            <DndContext onDragEnd={onDragEnd}>
              <SortableContext
                items={
                  dataSource
                    ? dataSource?.map((element) => element?.key)
                    : [1, 2, 3]
                }
                strategy={verticalListSortingStrategy}
              >
                <Table
                  components={{
                    body: {
                      row: dataSource && dataSource?.length > 0 && TableRow,
                    },
                  }}
                  rowKey="key"
                  columns={columns}
                  dataSource={memoizedDataSource}
                  loading={isLoading || updateFieldRankLoading}
                  onRow={(_, index) => {
                    const attr = {
                      index,
                    };
                    return attr;
                  }}
                  size={"small"}
                  scroll={{
                    y: windowSize?.height - 300,
                  }}
                  pagination={false}
                  rowClassName={() => "group"}
                />
              </SortableContext>
            </DndContext>
          </Form>
        </Space>
      </Col>
    </Row>
  );
};

export default DraggableTable;
