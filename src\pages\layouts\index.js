import { Fragment, useState } from "react";
import { Dialog, Transition } from "@headlessui/react";
import { LogoutOutlined, PlusOutlined } from "@ant-design/icons";
import { Button, Divider, Dropdown } from "antd";
import { Outlet } from "react-router-dom";
import {
  Bars3Icon,
  BookmarkSquareIcon,
  FireIcon,
  HomeIcon,
  InboxIcon,
  UserIcon,
  XMarkIcon,
  CalendarIcon,
  ChartBarIcon,
  FolderIcon,
  UsersIcon,
  UserGroupIcon,
  Cog6ToothIcon,
} from "@heroicons/react/24/outline";
import { NavLink, useLocation } from "react-router-dom";
import Drag from "../../components/webphone/index";

import NavSider from "./sider";

/**
 * @param {*} location: String: Subdirectory part from the url link.
 * @param {*} children:  Main app content.
 * @returns App layout: the main sidenav + topbar.
 */

const user = {
  name: "<PERSON>",
  email: "<EMAIL>",
  imageUrl:
    "https://images.unsplash.com/photo-1502685104226-ee32379fefbe?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
};

// Main sidemenu navlinks.
const navigation = [
  { name: "Contacts", href: "/contacts", icon: UserGroupIcon },
  { name: "Voip", href: "/voip", icon: FireIcon },
  { name: "Bookmarks", href: "#", icon: BookmarkSquareIcon },
  { name: "Messages", href: "#", icon: InboxIcon },
  { name: "Profile", href: "#", icon: UserIcon },
  { name: "Settings", href: "/settings/general", icon: Cog6ToothIcon },
];

const nav = [
  { name: "Dashboard", href: "#", icon: HomeIcon, current: true, count: "5" },
  { name: "Team", href: "#", icon: UsersIcon, current: false },
  {
    name: "Projects",
    href: "#",
    icon: FolderIcon,
    current: false,
    count: "19",
  },
  {
    name: "Calendar",
    href: "#",
    icon: CalendarIcon,
    current: false,
    count: "20+",
  },
  { name: "Documents", href: "#", icon: InboxIcon, current: false },
  { name: "Reports", href: "#", icon: ChartBarIcon, current: false },
];

function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}

export default function Layout({
  children,
  location,
  setOpenFieldsDrawer,
  setTitleConfig,
}) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const locationParams = useLocation();

  const drawerConfiguration = () => {
    setOpenFieldsDrawer(true);
    setTitleConfig("Create New Field");
  };

  const items = [
    {
      key: "1",
      label: <a onClick={() => {}}>Logoutt</a>,
      icon: <LogoutOutlined />,
    },
  ];

  // Updates topbar titles according to pathname in url.
  const getPageTitle = () => {
    let displayedContentIndicator = locationParams.pathname.split("/");
    return displayedContentIndicator[displayedContentIndicator.length - 1];
  };

  return (
    <>
      <div className="flex min-h-screen">
        <Transition.Root show={mobileMenuOpen} as={Fragment}>
          <Dialog
            as="div"
            className="relative z-40 lg:hidden"
            onClose={setMobileMenuOpen}
          >
            <Transition.Child
              as={Fragment}
              enter="transition-opacity ease-linear duration-300"
              enterFrom="opacity-0"
              enterTo="opacity-100"
              leave="transition-opacity ease-linear duration-300"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <div className="fixed inset-0 bg-gray-600 bg-opacity-75" />
            </Transition.Child>

            <div className="fixed inset-0 z-40 flex">
              <Transition.Child
                as={Fragment}
                enter="transition ease-in-out duration-300 transform"
                enterFrom="-translate-x-full"
                enterTo="translate-x-0"
                leave="transition ease-in-out duration-300 transform"
                leaveFrom="translate-x-0"
                leaveTo="-translate-x-full"
              >
                <Dialog.Panel className="relative flex w-full max-w-xs flex-1 flex-col bg-white focus:outline-none">
                  <Transition.Child
                    as={Fragment}
                    enter="ease-in-out duration-300"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="ease-in-out duration-300"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                  >
                    <div className="absolute right-0 top-0 -mr-12 pt-4">
                      <button
                        type="button"
                        className="ml-1 flex h-10 w-10 items-center justify-center rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        <span className="sr-only">Close sidebar</span>
                        <XMarkIcon
                          className="h-6 w-6 text-white"
                          aria-hidden="true"
                        />
                      </button>
                    </div>
                  </Transition.Child>
                  <div className="pb-4 pt-5">
                    <div className="flex flex-shrink-0 items-center px-4">
                      <img
                        className="h-8 w-auto"
                        src="https://tailwindui.com/img/logos/mark.svg?color=indigo&shade=600"
                        alt="Your Company"
                      />
                    </div>
                    <nav aria-label="Sidebar" className="mt-5">
                      <div className="space-y-1 px-2">
                        {navigation.map((item) => (
                          <NavLink
                            key={item.name}
                            to={item.href}
                            className="group flex items-center rounded-md p-2 text-base font-medium text-gray-600 hover:bg-gray-200 hover:text-gray-900"
                          >
                            <item.icon
                              className="mr-4 h-6 w-6 text-gray-400 group-hover:text-gray-500"
                              aria-hidden="true"
                            />
                            {item.name}
                          </NavLink>
                        ))}
                      </div>
                    </nav>
                  </div>
                  <div className="flex flex-shrink-0 border-t border-gray-200 p-4">
                    <a href="#" className="group block flex-shrink-0">
                      <div className="flex items-center">
                        <div>
                          <img
                            className="inline-block h-10 w-10 rounded-full"
                            src={user.imageUrl}
                            alt=""
                          />
                        </div>
                        <div className="ml-3">
                          <p className="text-base font-medium text-gray-700 group-hover:text-gray-900">
                            {user.name}
                          </p>
                          <p className="text-sm font-medium text-gray-500 group-hover:text-gray-700">
                            Account Settings
                          </p>
                        </div>
                      </div>
                    </a>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
              <div className="w-14 flex-shrink-0" aria-hidden="true">
                {/* Force sidebar to shrink to fit close icon */}
              </div>
            </div>
          </Dialog>
        </Transition.Root>

        {/* Static sidebar for desktop */}
        <div className="hidden lg:flex lg:flex-shrink-0">
          <div className="flex w-16 flex-col">
            <div className="flex min-h-0 flex-1 flex-col overflow-y-auto bg-gray-900">
              <div className="flex-1">
                <div className="flex h-16 items-center justify-center">
                  <img
                    className="h-9 w-auto"
                    src="/images/logo_comunik.png"
                    alt="Your Company"
                  />
                </div>
                <nav
                  aria-label="Sidebar"
                  className="flex flex-col items-center space-y-3 py-2"
                >
                  {navigation.map((item) => (
                    <NavLink
                      key={item.name}
                      to={item.href}
                      className={[
                        "flex items-center rounded-lg px-2 py-1 text-white/50 hover:text-blue-500",
                        locationParams.pathname.split("/")[1] ===
                          item.name.toLowerCase() ||
                        (locationParams.pathname === "/" &&
                          item.name.toLocaleLowerCase() === "contacts")
                          ? "bg-slate-600"
                          : "",
                      ]
                        .join(" ")
                        .trim()}
                    >
                      <item.icon
                        className="h-6 w-6 hover:text-blue-400"
                        aria-hidden="true"
                      />
                      <span className="sr-only">{item.name}</span>
                    </NavLink>
                  ))}
                </nav>
              </div>
              <div className="flex flex-shrink-0 pb-5">
                <Dropdown
                  menu={{
                    items,
                  }}
                  placement="topLeft"
                  arrow
                >
                  <a href="#" className="w-full flex-shrink-0">
                    <img
                      className="mx-auto block h-10 w-10 rounded-full"
                      src={user.imageUrl}
                      alt=""
                    />
                    <div className="sr-only">
                      <p>{user.name}</p>
                      <p>Account settings</p>
                    </div>
                  </a>
                </Dropdown>
              </div>
            </div>
          </div>
        </div>

        <div className="flex min-w-0 flex-1 flex-col overflow-hidden">
          {/* Mobile top navigation */}
          <div className="lg:hidden">
            <div className="flex items-center justify-between bg-indigo-600 px-4 py-2 sm:px-6 lg:px-8">
              <div>
                <img
                  className="h-8 w-auto"
                  src="https://tailwindui.com/img/logos/mark.svg?color=white"
                  alt="Your Company"
                />
              </div>
              <div>
                <button
                  type="button"
                  className="-mr-3 inline-flex h-12 w-12 items-center justify-center rounded-md bg-indigo-600 text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
                  onClick={() => setMobileMenuOpen(true)}
                >
                  <span className="sr-only">Open sidebar</span>
                  <Bars3Icon className="h-6 w-6" aria-hidden="true" />
                </button>
              </div>
            </div>
          </div>

          <main className="flex flex-1 overflow-hidden">
            {/* Primary column */}
            <div className="w-full">
              <div className="flex h-16 items-center justify-between px-6">
                <section>
                  <h3
                    className={`m-0 ${
                      getPageTitle() == "Organisation" ||
                      getPageTitle() == "Contact"
                        ? "mt-6"
                        : null
                    }`}
                  >
                    {getPageTitle() ? getPageTitle() : "Enterprises"}
                  </h3>
                  {getPageTitle() == "Organisation" ||
                  getPageTitle() == "Contact" ? (
                    <p className="text-sm text-neutral-600 dark:text-white">
                      Fields that are available and will be displayed on records
                      detail.
                    </p>
                  ) : null}
                </section>

                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() =>
                    location == "settings" ? drawerConfiguration() : null
                  }
                >
                  {`Ajouter un ${location == "settings" ? "field" : "contact"}`}
                </Button>
              </div>
              <Divider />
              {/* Main content will be displayed here */}
              <Outlet />
            </div>
            {/* Secondary column (hidden on smaller screens) */}
            <aside className="hidden lg:order-first lg:block lg:flex-shrink-0">
              <div className="h-full bg-gray-50">
                <div className="flex h-16 items-center px-3">
                  <h4 className="m-0">
                    {location !== null &&
                      location.charAt(0).toUpperCase() + location.slice(1)}
                  </h4>
                </div>
                <Divider />
                <NavSider location={location} />
              </div>
            </aside>
          </main>
        </div>
      </div>
      <Drag />
    </>
  );
}
