import {
  UPDATE_FIELD_PARAMETER_SUCCESS,
  UPDATE_FIELD_PARAMETER_ERROR,
  UPDATE_FIELD_PARAM_LOADING,
} from "../../constants";
import MainService from "../../../services/main.service";

export const updateFieldParameter = (optionValue, fieldId) => async (dispatch) => {
  try {
    dispatch({ type: UPDATE_FIELD_PARAM_LOADING });
    const response = await MainService.updateParameters(optionValue, fieldId);
    dispatch({
      type: UPDATE_FIELD_PARAMETER_SUCCESS,
      payload: { response: response?.data, id: fieldId, label: optionValue },
    });
  } catch (error) {
    if (error?.status !== 401) {
      dispatch({
        type: UPDATE_FIELD_PARAMETER_ERROR,
        payload: error,
      });
    }
  }
};
