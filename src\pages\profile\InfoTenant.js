import { Card, Col, Row } from "antd";
import { URL_ENV } from "index";
import {
  GaugeChart,
  // StorageGauge,
  // StorageGauge1,
} from "pages/home/<USER>/ChartsDashboard";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { generateAxios } from "services/axiosInstance";

const InfoTenant = () => {
  const [storageTenant, setStorageTenant] = useState({});
  const [t] = useTranslation("common");

  useEffect(() => {
    const fetchTypeByComunication = async () => {
      try {
        const response = await generateAxios(
          URL_ENV?.REACT_APP_OAUTH_CHAT_API +
            "/" +
            process.env.REACT_APP_SUFFIX_API
        ).get(`/get-storage-size`);
        // console.log({
        //   categories: response?.data?.data.map((el) => el.type),https://sphere.cmk.biz/tasks
        //   series: response?.data?.data,
        // });
        setStorageTenant({
          total: response.data?.total_storage,
          unit: response.data?.unit,
          used_storage: response.data?.used_storage,
          usage_percentage: response.data?.usage_percentage,
          available: response.data?.available_storage,
          title: t("profilemenu.storageUsage") + " " + response?.data?.unit,
        });
      } catch (error) {
        console.error(
          "Erreur lors de la récupération des appels totaux :",
          error
        );
        // setMessagesModuleFrequencyPerPeriod(response?.data);
      }
    };

    fetchTypeByComunication();
  }, []);

  return (
    <div className="p-4">
      <Row gutter={[16, 16]}>
        {/* Task Status Radial Chart */}
        <Col span={8}>
          <Card
            title={
              <div className="flex items-center justify-between">
                <span>{t("profilemenu.chatStorage")}</span>
                <span className="font-normal ">
                  {t("profilemenu.available")}{" "}
                  <span className="text-green-500">
                    {storageTenant?.available} {storageTenant?.unit}
                  </span>{" "}
                </span>
              </div>
            }
          >
            <GaugeChart data={storageTenant} />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default InfoTenant;
