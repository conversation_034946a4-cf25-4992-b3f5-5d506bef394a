import {
  CloseOutlined,
  ExportOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { Button, Card, Input, Popover, Space, Table } from "antd";
import { AvatarChat } from "components/Chat";
import { URL_ENV } from "index";
import { <PERSON>light<PERSON> } from "lucide-react";
import { getName } from "pages/layouts/chat/utils/ConversationUtils";
import ActionsComponent from "pages/tasks/ActionsComponent";
import { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { generateAxios } from "services/axiosInstance";

const TableStatsTickets = ({ start, end, family_id, pipeline_id }) => {
  const { i18n } = useTranslation("common");
  const [data, setData] = useState([]);
  const [firstData, setFirstData] = useState([]);

  const [titleTable, setTitleTable] = useState("");

  const [currentPage, setCurrentPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [pageSize, setPageSize] = useState(5);
  const [sorter, setSorter] = useState({ field: "", order: "" });
  const [searchText, setSearchText] = useState("");
  const [mount, setMount] = useState(false);
  const [filterVisible, setFilterVisible] = useState(false);
  const [selectedKeys, setSelectedKeys] = useState("");
  const [openPopover, setOpenPopover] = useState(false);

  const [t] = useTranslation("common");
  const { user: currentUser } = useSelector((state) => state.user);
  const inputRef = useRef(null);
  const dropdownRef = useRef(null);
  useEffect(() => {
    const fetchCallModule = async () => {
      setLoading(true);
      try {
        const response = await generateAxios(
          `${URL_ENV.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
        ).post(`/get_agent_stage_stats`, {
          family_id,
          pipeline_id,
          date_start: start,
          date_end: end,
          lang: i18n.language,
          limit: pageSize,
          page: currentPage,
          search: searchText,
          sort_field: sorter?.field,
          sort_order: sorter?.order,
        });

        if (!mount) setFirstData(response?.data?.data);
        setData(response?.data?.data);
        if (
          response?.data?.data.length > 0 &&
          response?.data?.data[0]?.stages.length > 0
        ) {
          setMount(true);
        }
        setCurrentPage(response?.data?.page?.currentPage);
        setTotal(response?.data?.page?.total);
        setTitleTable(response?.data?.title);
      } catch (error) {
        console.error(
          "Erreur lors de la récupération des appels sortants :",
          error
        );
      } finally {
        setLoading(false);
      }
    };
    if (currentPage) fetchCallModule();
  }, [
    start,
    end,
    i18n.language,
    currentPage,
    pageSize,
    searchText,
    sorter.field,
    sorter.order,
    pipeline_id,
    family_id,
  ]);
  useEffect(() => {
    if (openPopover) {
      setTimeout(() => {
        inputRef?.current?.focus();
      }, 100);
    }
  }, [openPopover, inputRef]);
  const handleChangePageSize = (current, size) => {
    setCurrentPage(1);
    setPageSize(size);
  };
  const handleChangePage = (page, pageSize, sorter) => {
    setCurrentPage(page);
    setPageSize(pageSize);
  };

  const searchInput = useRef(null);
  const handleSearch = (value) => {
    setSearchText(value);
    setCurrentPage(1); // Réinitialiser à la première page
  };
  const handleReset = (clearFilters) => {
    clearFilters();
    setSearchText("");
  };
  const [timeoutId, setTimeoutId] = useState(null);

  const onSearchChange = (value, setSelectedKeys) => {
    // Annuler le timeout précédent
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    setSelectedKeys(value);

    // Définir un nouveau timeout
    const newTimeoutId = setTimeout(() => {
      handleSearch(value);
    }, 300); // Délai de 300ms

    setTimeoutId(newTimeoutId);
  };

  const getColumnSearchProps = (dataIndex, handleSearch) => ({
    placement: "top",
    filterDropdown: ({
      setSelectedKeys,
      selectedKeys,
      confirm,
      clearFilters,
      close,
      visible,
    }) => {
      setFilterVisible(visible);

      return (
        <div
          style={{ padding: 8 }}
          onKeyDown={(e) => e.stopPropagation()}
          ref={dropdownRef}
        >
          {/* <div className="flex justify-end">
            <Button
              size="small"
              type="text"
              icon={<CloseOutlined />}
              onClick={() => close()}
            />
          </div> */}

          <Input
            allowClear
            ref={inputRef}
            style={{ width: "100%" }}
            placeholder={` ${t("activities.search")} ${dataIndex}`}
            value={selectedKeys[0]}
            onChange={(e) => onSearchChange(e.target.value, setSelectedKeys)}
            // onPressEnter={() => {
            //   handleSearch(selectedKeys[0]);
            //   confirm();
            // }}
          />
          <div className="flex justify-end pt-1">
            <Button size="small" type="primary" onClick={() => close()}>
              {t("activities.close")}
            </Button>
          </div>
          <Space>
            {/* <Button
            type="primary"
            onClick={() => {
              handleSearch(selectedKeys[0]);
              confirm();
            }}
            icon={<SearchOutlined />}
            size="small"
            style={{ width: 110 }}
          >
            {t("activities.search")}
          </Button> */}
            {/* <Button
            onClick={() => {
              clearFilters();
              setSearchText("");
              confirm();
            }}
            size="small"
            style={{ width: 90 }}
          >
            {t("activities.reset")}
          </Button> */}
          </Space>
        </div>
      );
    },
    filterIcon: (filtered) => (
      <>
        <SearchOutlined
          style={{
            color:
              filtered || (!filterVisible && searchText)
                ? "#1677ff"
                : undefined,
          }}
        />
      </>
    ),
  });
  const generateStageColumns = (stages) => {
    return stages.map((stage, index) => ({
      title: stage.stage,
      dataIndex: ["stages", index, "count"],
      key: stage.stage.toLowerCase().replace(/ /g, "_"),
      render: (count) => count || 0,
    }));
  };
  const stageColumns =
    Array.isArray(firstData) && firstData.length > 0
      ? generateStageColumns(firstData[0].stages)
      : []; // On prend les stages du premier agent pour générer les colonnes
  const columns = [
    {
      dataIndex: "agent",
      key: "agent",
      title: (
        <div className="flex items-center justify-between">
          {" "}
          <span>{t("emailTemplates.fullName")}</span>{" "}
          {data.length > 0 || searchText ? (
            <Popover
              placement="top"
              onPopupClick={(e) => e.stopPropagation()}
              trigger={["click"]}
              size="small"
              open={openPopover}
              onOpenChange={(e) => {
                setOpenPopover(e);
              }}
              arrow={false}
              content={
                <div
                  // style={{ padding: 8 }}
                  onKeyDown={(e) => e.stopPropagation()}
                  ref={dropdownRef}
                >
                  {/* <div className="flex justify-end">
            <Button
              size="small"
              type="text"
              icon={<CloseOutlined />}
              onClick={() => close()}
            />
          </div> */}

                  <Input
                    allowClear
                    ref={inputRef}
                    style={{ width: "100%" }}
                    placeholder={` ${t("activities.search")}`}
                    value={selectedKeys}
                    onChange={(e) =>
                      onSearchChange(e.target.value, setSelectedKeys)
                    }
                    // onPressEnter={() => {
                    //   handleSearch(selectedKeys[0]);
                    //   confirm();
                    // }}
                  />
                  <div className="flex justify-end pt-1">
                    <Button
                      size="small"
                      type="link"
                      onClick={() => setOpenPopover(false)}
                    >
                      {t("activities.close")}
                    </Button>
                  </div>
                  <Space>
                    {/* <Button
            type="primary"
            onClick={() => {
              handleSearch(selectedKeys[0]);
              confirm();
            }}
            icon={<SearchOutlined />}
            size="small"
            style={{ width: 110 }}
          >
            {t("activities.search")}
          </Button> */}
                    {/* <Button
            onClick={() => {
              clearFilters();
              setSearchText("");
              confirm();
            }}
            size="small"
            style={{ width: 90 }}
          >
            {t("activities.reset")}
          </Button> */}
                  </Space>
                </div>
              }
            >
              <Button
                type="text"
                onClick={(e) => {
                  e.stopPropagation();
                  searchInput?.current?.focus();
                }}
                icon={
                  <SearchOutlined
                    style={{
                      color:
                        !openPopover && searchText
                          ? "#1677ff"
                          : "rgba(0, 0, 0, 0.29)",
                      fontSize: 10,
                    }}
                  />
                }
                size="small"
              />
            </Popover>
          ) : null}
        </div>
      ),
      width: 200,
      fixed: "left",
      //   sorter: (a, b) => a.user.label.localeCompare(b.user.label),
      sorter: data.length > 0,
      // ...(data.length > 0 || searchText
      //   ? getColumnSearchProps("user", handleSearch)
      //   : {}),
      render: (_, { user }) => (
        <div className="flex max-w-[160px] space-x-1 truncate">
          <ActionsComponent
            elementValue={
              user?._id !== currentUser?.id
                ? {
                    uuid: user?.uid,
                    extension: user?.extension,
                    id: user?.id,
                    family_id: 4,
                  }
                : {}
            }
          >
            <AvatarChat
              // fontSize="0.875rem"
              url={
                URL_ENV?.REACT_APP_BASE_URL +
                URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                user?.avatar
              }
              type="user"
              size={22}
              height={10}
              width={10}
              name={getName(user?.label, "avatar")}
              hasImage={user?.avatar && user?.avatar !== "/storage/uploads/"}
            />
          </ActionsComponent>{" "}
          <span>{user?.label}</span>
        </div>
      ),
    },
    ...stageColumns,

    {
      title: "Total",
      dataIndex: "total",
      key: "total",
      sorter: data.length > 0,
      width: 180,
      fixed: "right",
    },
    // Ajoute les colonnes dynamiques
  ];

  //   const components = {
  //     header: {
  //       cell: ({ children }) => (
  //         <th style={{ backgroundColor: "#84a3d58a", color: "black" }}>
  //           {children}
  //         </th>
  //       ),
  //     },
  //   };
  const handleTableChange = (pagination, filters, sorter) => {
    if (sorter?.field && sorter?.order)
      setSorter({
        field: sorter?.field,
        order: sorter?.order === "ascend" ? "asc" : "descend" ? "desc" : "",
      });
    else
      setSorter({
        field: "",
        order: "",
      });
    setCurrentPage(pagination.current);
    setPageSize(pagination.pageSize);
  };

  const downloadCSV = async () => {
    try {
      const res = await generateAxios(
        `${URL_ENV.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
      ).post(`/export_data_agent_stats`, {
        family_id,
        pipeline_id,
        date_start: start,
        date_end: end,
        lang: i18n.language,
        search: searchText,
        sort_field: sorter?.field,
        sort_order: sorter?.order,
      });
      const csvData = new Blob([res.data], { type: "text/csv" });
      const csvURL = URL.createObjectURL(csvData);
      const link = document.createElement("a");
      link.href = csvURL;
      link.download = `${titleTable}.csv`;
      document.body.appendChild(link);
      link.click();
    } catch (err) {}
  };
  return (
    <div className="tablestatsTickets">
      {/* {data.length > 0 ? ( */}
      <Card
        title={
          <div className="flex flex-col space-y-1 py-1">
            <div className="flex items-center justify-between">
              <div className="mb-1 flex flex-col space-y-1">
                <div
                  style={{
                    fontSize: "1.2em",
                    color: "rgb(51, 51, 51)",
                    fontWeight: "bold",
                    fill: "rgb(51, 51, 51)",
                  }}
                >
                  {titleTable}
                </div>
                {/* <Input
                size="small"
                allowClear
                ref={inputRef}
                style={{ width: "100%" }}
                placeholder={` ${t("activities.search")} `}
                value={selectedKeys}
                onChange={(e) =>
                  onSearchChange(e.target.value, setSelectedKeys)
                }
                // onPressEnter={() => {
                //   handleSearch(selectedKeys[0]);
                //   confirm();
                // }}
              /> */}
              </div>
              <Button
                icon={<ExportOutlined />}
                onClick={() => downloadCSV()}
                disabled={data.length === 0}
              >
                {t("dashboard.exportCsv")}
              </Button>
            </div>

            {/* <Input
              allowClear
              ref={inputRef}
              // style={{ width: "100%" }}
              placeholder={` ${t("activities.search")} `}
              value={selectedKeys[0]}
              onChange={(e) => onSearchChange(e.target.value, setSelectedKeys)}
              // onPressEnter={() => {
              //   handleSearch(selectedKeys[0]);
              //   confirm();
              // }}
            /> */}
          </div>
        }
        styles={{ body: { padding: 0 } }}
      >
        <Table
          columns={columns}
          size="small"
          bordered={true}
          className="table-voip-stats"
          loading={loading}
          dataSource={data}
          //   components={components}
          scroll={{
            x: "max-content",
          }}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            onChange: handleChangePage,
            onShowSizeChange: handleChangePageSize,
            showSizeChanger: true,
            pageSizeOptions: ["5", "10", "20", "50", "100"],
            // hideOnSinglePage:
            //   data && data?.filter((el) => el.id).length < 11 && true,
          }}
          onChange={handleTableChange}
        />
      </Card>
      {/* ) : null} */}
    </div>
  );
};

export default TableStatsTickets;
