import { useEffect, useRef, useState } from "react";
import { AutoComplete, Button, Form, Select, Space, Tooltip } from "antd";
import { HiOutlineBuildingOffice } from "react-icons/hi2";
import { Cg<PERSON><PERSON>lane } from "react-icons/cg";
import {
  CloseCircleFilled,
  LoadingOutlined,
  PlusOutlined,
  TeamOutlined,
} from "@ant-design/icons";
import { AiOutlineShoppingCart } from "react-icons/ai";
import { LuPalmtree } from "react-icons/lu";
import { toastNotification } from "../../../../components/ToastNotification";
import { affectations, postAffectations } from "../../services/services";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import DisplayAvatar from "../DisplayAvatar";
import { URL_ENV } from "index";
import { HighlightSearchW } from "..";
import { Blocks, HeartHandshake } from "lucide-react";
import TicketIconSphere from "components/icons/TicketIconSphere";

const AffectationContent = ({
  source,
  elementId,
  setData,
  t,
  access,
  user,
  UpdateOnlyDataSource,
  setFamilyToAdd,
  setOpenDrawerCreate,
  setExternalSource,
  setOpenPopCon,
}) => {
  //
  const dispatch = useDispatch();
  const logs = useSelector(({ voip }) => voip.logs);
  const autoCompleteRef = useRef();
  const [form] = Form.useForm();
  const prevController = useRef(null);
  // console.log({ UpdateOnlyDataSource });
  //
  const [selectedFamily, setSelectedFamily] = useState(null);
  const [autoCompleteOptions, setAutoCompleteOptions] = useState([]);
  const [loadingOptions, setLoadingOptions] = useState(false);
  const [autoCompleteLabel, setAutoCompleteLabel] = useState("");
  const [autoCompleteValue, setAutoCompleteValue] = useState("");
  const [saveIsDisabled, setSaveIsDisabled] = useState(true);
  const [saveIsLoading, setSaveIsLoading] = useState(false);
  // console.log(selectedFamily, autoCompleteValue);
  // console.log({ autoCompleteLabel }, form.getFieldsValue());
  //
  useEffect(() => {
    if (!selectedFamily) {
      setAutoCompleteValue("");
      setAutoCompleteLabel("");
      setAutoCompleteOptions([]);
      setSaveIsDisabled(true);
    }
  }, [selectedFamily]);
  //
  const familiesOption = [
    ...(access?.companies === "1"
      ? [
          {
            label: (
              <Space>
                <HiOutlineBuildingOffice
                  style={{ fontSize: "16px", marginTop: "5px" }}
                />
                <p>{t("menu1.companies")}</p>
              </Space>
            ),
            value: 1,
          },
        ]
      : []),
    ...(access?.contact === "1"
      ? [
          {
            label: (
              <Space>
                <TeamOutlined style={{ fontSize: "16px", marginTop: "5px" }} />
                <p>{t("menu1.contacts")}</p>
              </Space>
            ),
            value: 2,
          },
        ]
      : []),
    ...(access?.leads === "1"
      ? [
          {
            label: (
              <Space>
                <CgUserlane style={{ fontSize: "16px", marginTop: "5px" }} />
                <p>{t("menu1.leads")}</p>
              </Space>
            ),
            value: 9,
          },
        ]
      : []),
    ...(access?.deals === "1"
      ? [
          {
            label: (
              <Space>
                <HeartHandshake size={18} style={{ marginTop: 7 }} />
                <p>{t("menu1.deals")}</p>
              </Space>
            ),
            value: 3,
          },
        ]
      : []),
    ...(access?.ticket === "1"
      ? [
          {
            label: (
              <Space>
                <TicketIconSphere size={19} style={{ marginTop: 5 }} />
                <p>{t("menu1.tickets")}</p>
              </Space>
            ),
            value: 6,
          },
        ]
      : []),
    ...(access?.projects === "1"
      ? [
          {
            label: (
              <Space>
                <Blocks size={17} style={{ marginTop: 7 }} />
                <p>{t("menu1.projects")}</p>
              </Space>
            ),
            value: 7,
          },
        ]
      : []),
    ...(access?.products === "1"
      ? [
          {
            label: (
              <Space>
                <AiOutlineShoppingCart
                  style={{ fontSize: "16px", marginTop: "5px" }}
                />
                <p>{t("menu1.products")}</p>
              </Space>
            ),
            value: 5,
          },
        ]
      : []),
    ...(access?.booking === "1"
      ? [
          {
            label: (
              <Space>
                <LuPalmtree style={{ fontSize: "16px", marginTop: "5px" }} />
                <p>{t("menu1.booking")}</p>
              </Space>
            ),
            value: 8,
          },
        ]
      : []),
  ];
  //
  const families = {
    1: t("contacts.company"),
    2: t("contacts.contact"),
    3: t("import.deal"),
    5: t("contacts.product"),
    6: t("contacts.ticket"),
    7: t("contacts.project"),
    8: t("contacts.booking"),
    9: t("contacts.leads"),
  };
  //
  const fetchOptions = async (searchText, familyID) => {
    // if (searchText?.length < 0) return;
    const controller = new AbortController();
    const signal = controller.signal;
    if (prevController.current) {
      prevController.current.abort();
    }
    prevController.current = controller;
    try {
      setLoadingOptions(true);
      const {
        data: { data },
      } = await affectations(
        familyID || selectedFamily,
        user?.id,
        searchText,
        signal
      );
      const fetchedOptions = data?.map((e) => ({
        key: e._id,
        // label: `${e.label}${e.reference ? ` - ${e.reference}` : ""}`,
        value: `${e.label}${e.reference ? ` - ${e.reference}` : ""}`,
        label: (
          <div className="flex items-center space-x-1">
            <div className="_avatar_">
              <DisplayAvatar
                size={30}
                name={e.label}
                urlImg={
                  !!e.avatar &&
                  `${
                    URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                  }${e.avatar}`
                }
              />
            </div>
            <div className="">
              <p className="truncate font-semibold leading-4">
                {HighlightSearchW(e.label || e.reference, searchText || "")}
              </p>
              {!!e.label && !!e.reference && (
                <p
                  style={{ fontSize: 11 }}
                  className="leading-4  text-slate-500"
                >
                  {e.reference}
                </p>
              )}
            </div>
          </div>
        ),
      }));
      setAutoCompleteOptions(fetchedOptions);
    } catch (err) {
      if (err.name === "CanceledError") {
        console.log("Request was aborted");
      } else {
        err?.response?.status !== 401 &&
          toastNotification("error", t("toasts.somethingWrong"), "topRight");
        throw new Error(err?.message ? err.message : err);
      }
    } finally {
      setLoadingOptions(false);
    }
  };
  //
  const onChange = (text) => {
    if (text?.length < autoCompleteValue?.length) setSaveIsDisabled(true);
    setAutoCompleteLabel(text);
  };
  //
  const onSelect = (data, option) => {
    // console.log("onSelect", data, option);
    setAutoCompleteLabel(option.label);
    setAutoCompleteValue(option?.key);
    setSaveIsDisabled(false);
  };
  //
  const saveAffectation = async () => {
    try {
      setSaveIsLoading(true);
      const formData = new FormData();
      formData.append("element_id", elementId);
      formData.append("affect_to", autoCompleteValue);
      formData.append("affected_family_id", selectedFamily);
      formData.append("type", source);
      const {
        data: { data },
      } = await postAffectations(formData);
      if (UpdateOnlyDataSource)
        setData((prev) =>
          prev?.map((item) =>
            item?._id === elementId || item?.id === elementId
              ? { ...item, affectation: data }
              : item
          )
        );
      else {
        const newLogs = logs.map((call) => {
          if (call?._id === elementId || call?.id === elementId) {
            return {
              ...call,
              affectation: data,
            };
          }
          return call;
        });
        dispatch({
          type: "GET_LOG_SUCCESS",
          payload: { data: newLogs },
        });
      }
      // console.log({ data });
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? err.message : err);
    } finally {
      setSaveIsLoading(false);
    }
  };
  //
  return (
    <div className="w-64 space-y-6">
      <Form
        form={form}
        layout="vertical"
        // onFieldsChange={(changedFields, allFields) =>
        //   console.log({ changedFields, allFields })
        // }
      >
        <Form.Item
          label={t("voip.selectModule")}
          name={t("voip.selectModule")}
          rules={[
            {
              required: true,
              message: "",
            },
          ]}
        >
          <Select
            style={{ width: "100%" }}
            allowClear
            options={familiesOption}
            // value={selectedFamily}
            onChange={async (value) => {
              setSelectedFamily(value);
              setAutoCompleteLabel("");
              setAutoCompleteOptions([]);
              form.resetFields([t("voip.search_select")]);
              await fetchOptions("", value);
              if (autoCompleteRef.current) {
                autoCompleteRef.current.focus();
              }
            }}
          />
        </Form.Item>

        <Form.Item
          label={
            selectedFamily ? (
              `${t("voip.search_select")} ${families?.[selectedFamily]}`
            ) : (
              <span className=" text-white">Hello Genius</span>
            )
          }
          name={t("voip.search_select")}
          rules={[
            {
              required: selectedFamily,
              message: "",
            },
          ]}
        >
          <div className="flex items-center space-x-1">
            <AutoComplete
              ref={autoCompleteRef}
              disabled={!selectedFamily}
              // value={!selectedFamily ? "" : autoCompleteValue}
              options={autoCompleteOptions}
              style={{
                width: "100%",
              }}
              defaultOpen={true}
              onSelect={onSelect}
              onSearch={(text) => fetchOptions(text)}
              allowClear={{
                clearIcon: loadingOptions ? (
                  <LoadingOutlined style={{ fontSize: 12, color: "#1677ff" }} />
                ) : (
                  <CloseCircleFilled />
                ),
              }}
              onChange={onChange}
            />
            {!!selectedFamily && (
              <Tooltip
                title={`${t("voip.createNew")} ${families?.[selectedFamily]}`}
              >
                <Button
                  type="link"
                  icon={<PlusOutlined />}
                  onClick={() => {
                    setExternalSource({ source: "call", id: elementId });
                    setFamilyToAdd(selectedFamily);
                    setOpenDrawerCreate(true);
                    setOpenPopCon(false);
                  }}
                />
              </Tooltip>
            )}
          </div>
        </Form.Item>
      </Form>
      <div className="flex flex-row justify-end pt-8">
        <Button
          loading={saveIsLoading}
          onClick={saveAffectation}
          disabled={saveIsDisabled}
          size="small"
          type="primary"
        >
          {t("voip.save")}
        </Button>
      </div>
    </div>
  );
};

export default AffectationContent;
