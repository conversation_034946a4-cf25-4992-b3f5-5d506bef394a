import { useTranslation } from "react-i18next";
import { SphereDrawer } from "components/Chat/Drawer";
import { useDispatch, useSelector } from "react-redux";
import {
  setAllPinnedFetched,
  setAllStarredFetched,
  setOpenSideBarDrawer,
} from "new-redux/actions/chat.actions";
import { useEffect, useState, lazy, Suspense } from "react";
import { Tabs } from "antd";
import {
  getAllPinnedMessages,
  getAllStarredMessages,
} from "new-redux/services/chat.services";
import { BookOutlined, PushpinOutlined, StarOutlined } from "@ant-design/icons";
import { lazyRetry } from "utils/lazyRetry";
import { LoaderDrawer } from "..";

const DrawerList = lazy(() =>
  lazyRetry(() => import("components/Chat/Drawer/DrawerList"), "DrawerList")
);

const SavedMessage = () => {
  const [t] = useTranslation("common");
  const dispatch = useDispatch();

  const { openSideBarDrawer, allStarredList, allPinnedList } = useSelector(
    (state) => state.chat
  );

  // starred state
  const [currentPageStarred, setCurrentPageStarred] = useState(1);
  const [loadingMoreStarred, setLoadingMoreStarred] = useState(false);
  const [loadingStarred, setLoadingStarred] = useState(false);
  const [hasMoreStarred, setHasMoreStarred] = useState(false);
  const [errorStarred, setErrorStarred] = useState(false);
  // pinned state
  const [currentPagePinned, setCurrentPagePinned] = useState(1);
  const [loadingMorePinned, setLoadingMorePinned] = useState(false);
  const [loadingPinned, setLoadingPinned] = useState(false);
  const [hasMorePinned, setHasMorePinned] = useState(false);
  const [errorPinned, setErrorPinned] = useState(false);

  // commun state
  const [tabSelected, setTabSelected] = useState("1");

  // I added this to prevent the drawer from closing when clicking on a pinned message.
  // There's an action called "RESET_STATE_OTHER_USER" that runs when clicking on a pinned message, which causes "openSideBarDrawer" to become false.
  // I also commented out the condition "openSideBarDrawer && ..." in /layouts/chat/index.js at line 242.
  // (Hassine Basla)
  const [openDrawer, setOpenDrawer] = useState(false);
  useEffect(() => {
    !!openSideBarDrawer && setOpenDrawer(true);
  }, [openSideBarDrawer]);
  //
  //

  useEffect(() => {
    if (tabSelected === "1") {
      dispatch(
        getAllStarredMessages({
          setLoadingMore: setLoadingMoreStarred,
          setHasMore: setHasMoreStarred,
          currentPage: 1,
          setLoading: setLoadingStarred,
          setError: setErrorStarred,
        })
      );
      dispatch(setAllStarredFetched(true));
    } else if (tabSelected === "2") {
      dispatch(
        getAllPinnedMessages({
          setHasMore: setHasMorePinned,
          setLoadingMore: setLoadingMorePinned,
          currentPage: 1,
          setLoading: setLoadingPinned,
          setError: setErrorPinned,
        })
      );
      dispatch(setAllPinnedFetched(true));
    }
  }, [dispatch, tabSelected]);

  const items = [
    {
      key: "1",
      id: "starredChat",
      label: (
        <>
          <StarOutlined />
          {t("chat.listStarredGlobal")}
        </>
      ),
      children: (
        <Suspense fallback={<LoaderDrawer />}>
          <DrawerList
            source="AllStarred"
            tab="1"
            hasGoTo={true}
            error={errorStarred}
            data={allStarredList}
            loading={loadingStarred}
            loadingMore={loadingMoreStarred}
            hasMore={hasMoreStarred}
            page={currentPageStarred}
            reloadFunction={() =>
              dispatch(
                getAllStarredMessages({
                  setLoadingMore: setLoadingMoreStarred,
                  setHasMore: setHasMoreStarred,
                  currentPage: 1,
                  setLoading: setLoadingStarred,
                  setError: setErrorStarred,
                })
              )
            }
            loadMoreFunction={(page) => {
              dispatch(
                getAllStarredMessages({
                  setHasMore: setHasMoreStarred,
                  setLoadingMore: setLoadingMoreStarred,
                  currentPage: page,
                  setLoading: setLoadingStarred,
                  setError: setErrorStarred,
                })
              );
              setCurrentPageStarred((p) => p + 1);
            }}
          />
        </Suspense>
      ),
    },
    {
      key: "2",
      id: "pinnedChat",
      label: (
        <>
          <PushpinOutlined />
          {t("chat.listPinnedGlobal")}{" "}
        </>
      ),
      children: (
        <Suspense fallback={<LoaderDrawer />}>
          <DrawerList
            source="AllPinned"
            tab="2"
            hasGoTo={true}
            error={errorPinned}
            data={allPinnedList}
            loading={loadingPinned}
            loadingMore={loadingMorePinned}
            hasMore={hasMorePinned}
            page={currentPagePinned}
            reloadFunction={() => {
              console.log("reload");
              dispatch(
                getAllPinnedMessages({
                  setHasMore: setHasMorePinned,
                  setLoadingMore: setLoadingMorePinned,
                  currentPage: 1,
                  setLoading: setLoadingPinned,
                  setError: setErrorPinned,
                })
              );
            }}
            loadMoreFunction={(page) => {
              dispatch(
                getAllPinnedMessages({
                  setHasMore: setHasMorePinned,
                  setLoadingMore: setLoadingMorePinned,
                  currentPage: page,
                  setLoading: setLoadingPinned,
                  setError: setErrorPinned,
                })
              );
              setCurrentPagePinned((p) => p + 1);
            }}
          />
        </Suspense>
      ),
    },
  ];

  return (
    <SphereDrawer
      title={
        <span>
          <BookOutlined /> {t("chat.listSavedMessage")}
        </span>
      }
      // open={openDrawer}
      // onClose={() => dispatch(setOpenSideBarDrawer(false))}
      open={openDrawer}
      onClose={() => {
        dispatch(setOpenSideBarDrawer(false));
        setOpenDrawer(false);
      }}
    >
      <Tabs
        centered
        className="drawer h-full overflow-hidden"
        defaultActiveKey="1"
        items={items}
        onChange={(key) => setTabSelected(key)}
      />
    </SphereDrawer>
  );
};
export default SavedMessage;
