import { <PERSON>, Tooltip, Dropdown, <PERSON><PERSON>, <PERSON><PERSON> } from "antd";
import { useCallback, useEffect, useMemo, useState } from "react";

import moment from "moment/moment";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";
import MarkDown from "../Markdown";
import MainService from "../../../../services/main.service";
import { toastNotification } from "../../../../components/ToastNotification";
import FormCreate from "../../../clients&users/components/FormCreate";
import CreateTask from "../../../voip/components/CreateTask";
import useDebounce from "../../../components/UseDebounce/UseDebounce";
import dayjs from "dayjs";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import {
  setNumberEmailThread,
  setPage,
  setPageSize,
} from "new-redux/actions/mail.actions";
import { formatDateForDisplay } from "pages/tasks/helpers/formatDateToDisplay";
import DropdownActionsTable from "../dropdownActionsTable";
import ModalDeleteEmail from "../components/ModalDeleteEmail";
import { DeleteOutlined } from "@ant-design/icons";
import { checkAccessRoleAccount } from "../mailing";

const Trash = ({
  setDetailsMail,
  notification,
  dataAccounts,
  searchMail,
  refresh,
  setRefresh,
}) => {
  // const [page, setPage] = useState(1);

  const [openForm, setOpenForm] = useState(false);
  const [familyId, setFamilyId] = useState(null);
  const [openTask, setOpenTask] = useState(false);
  const [titleTask, setTiltleTask] = useState("");
  const [typeDelete, setTypeDelete] = useState("");
  const [dataMailTrash, setDataMailTrash] = useState([]);
  const [metaMailInbox, setMetaMailInbox] = useState({});
  const [openModal, setOpenModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [t] = useTranslation("common");
  const navigate = useNavigate();
  const [error, setError] = useState(false);
  const { page, pageSize, searchEmail } = useSelector(
    (state) => state.mailReducer
  );
  const debouncedSearchValue = useDebounce(searchEmail, 500);
  const usedAccount = useMemo(
    () => dataAccounts?.find((item) => item?.selected),
    [dataAccounts]
  );
  const { user } = useSelector(({ user }) => user);
  const isToday = require("dayjs/plugin/isToday");

  dayjs.extend(isToday);

  const dispatch = useDispatch();

  const getMailsTrash = useCallback(async () => {
    // if (Object.values(metaMailInbox).length > 0 && !refresh) return;
    setLoading(true);

    let response = "";
    try {
      if (debouncedSearchValue.length > 0) {
        response = await MainService.searchMailsTrash(
          usedAccount.value,
          debouncedSearchValue,
          page,
          pageSize
        );
      } else {
        response = await MainService.getTrashEmails(
          usedAccount.value,
          page,
          pageSize
        );
      }
      if (response?.status === 200) {
        checkAccessRoleAccount(response, navigate, t);

        setDataMailTrash(response.data.data);
        setMetaMailInbox(response.data.meta);

        setError(false);
      }
    } catch (err) {
      setError(true);
      console.log(err);
    } finally {
      setRefresh(false);
      setLoading(false);
    }
  }, [
    usedAccount?.value,
    debouncedSearchValue,
    page,
    refresh,
    metaMailInbox?.currentPage,
    pageSize,
  ]);

  const dataSourceInbox = dataMailTrash.map((item, i) => ({
    key: item.id,
    from: item.from,
    to: item.to,
    subject: item.subject,
    body: item.body,
    nbr: item.nbr,
    date: item.date,
    seen: item.seen,
    starred: item.starred,
    important: item.important,
    box: item.box,
    archives: item.archives,
  }));

  const RestoreMail = async (id) => {
    try {
      const response = await MainService.restoreMail(id);
      if (response.status === 200) {
        toastNotification(
          "success",
          `Votre email a été récupéré`,
          "bottomRight",
          3
        );
        getMailsTrash(page);
      }
    } catch (error) {
      toastNotification(
        "error",
        error?.response?.data
          ? error?.response?.data?.message
          : t("toasts.errorFetchApi"),
        "topRight",
        4
      );
      console.log(error);
    }
  };

  const ClearTrashById = async (id) => {
    var formData = new FormData();
    formData.append("account_id", usedAccount?.value);
    for (let i = 0; i < selectedRowKeys.length; i++) {
      formData.append("ids[]", selectedRowKeys[i]);
    }
    try {
      const response = await MainService.clearTrashById(formData);
      if (response.status === 200) {
        toastNotification(
          "success",
          t("mailing.successClearTrash"),
          "topRight",
          3
        );
        setOpenModal(false);
        getMailsTrash(page);
      }
    } catch (error) {
      toastNotification(
        "error",
        error?.response?.data
          ? error?.response?.data?.message
          : t("toasts.errorFetchApi"),
        "topRight",
        4
      );
      console.log(error);
    }
  };

  const ClearTrash = async (id) => {
    var formData = new FormData();
    formData.append("account_id", usedAccount?.value);

    try {
      const response = await MainService.clearTrash(formData);
      if (response.status === 200) {
        toastNotification(
          "success",
          t("mailing.successClearTrash"),
          "topRight",
          3
        );
        setOpenModal(false);
        getMailsTrash(page);
      }
    } catch (error) {
      toastNotification(
        "error",
        error?.response?.data
          ? error?.response?.data?.message
          : t("toasts.errorFetchApi"),
        "topRight",
        4
      );
      console.log(error);
    }
  };

  const columns = [
    {
      title: "",
      dataIndex: "from",
      width: "120px",
      render: (text, record) => {
        return (
          <div className={`flex cursor-pointer items-center `}>
            <Tooltip
              placement="topLeft"
              title={text?.name?.length > 0 ? text.name : text.address}
            >
              <p
                className=" max-w-sm truncate"
                style={{ width: "80%", cursor: "pointer", marginLeft: "3px" }}
              >
                {text?.name?.length > 0 ? text.name : text.address}
              </p>
            </Tooltip>

            <div className="action-mail ">
              {" "}
              <Badge
                style={{
                  backgroundColor: "transparent",
                  border: "none",
                  outline: "none",
                  margin: 0,
                  color: "gray",
                  fontSize: 12,
                  fontWeight: record?.seen === 0 ? "bold" : "normal",
                }}
                count={record?.nbr}
              ></Badge>
              <div className="hidden items-center gap-x-1 group-hover:flex ">
                <DropdownActionsTable
                  record={record}
                  t={t}
                  conditionActions={false}
                  usedAccount={usedAccount}
                  dataMailOutbox={dataMailTrash}
                  setDataMailOutbox={setDataMailTrash}
                  user={user}
                  setOpenTask={setOpenTask}
                  getMailsInbox={getMailsTrash}
                  type="trash"
                />
              </div>
            </div>
          </div>
        );
      },
    },
    {
      title: t("mailing.Inbox.subject"),
      dataIndex: "subject",
      width: "150px",
      render: (text, record) => {
        return (
          <div>
            <span
              style={{
                fontWeight: record.seen === 0 ? "bold" : "",
                cursor: "pointer",
              }}
            >
              {text?.length > 30 ? (
                // <MarkDown>{text.toString()?.substring(0, 30)}...</MarkDown>
                <span
                  dangerouslySetInnerHTML={{
                    __html: text.toString()?.substring(0, 30) + "...",
                  }}
                />
              ) : (
                // <MarkDown>{text}</MarkDown>
                <span dangerouslySetInnerHTML={{ __html: text }} />
              )}
            </span>
          </div>
        );
      },
    },
    {
      title: t("mailing.Inbox.message"),
      dataIndex: "body",
      width: "300px",
      ellipsis: true,
      render: (text, record) => {
        return (
          <div style={{ cursor: "pointer" }}>
            {/* <MarkDown>{text}</MarkDown> */}
            <span dangerouslySetInnerHTML={{ __html: text }} />
          </div>
        );
      },
    },
    {
      title: t("mailing.Inbox.date"),
      dataIndex: "date",
      width: "120px",
      render: (dateTime, record) => (
        <span
          style={{
            fontWeight: record.seen === 0 ? "bold" : "",
            cursor: "pointer",
          }}
        >
          {formatDateForDisplay(
            record.date,
            `${user?.location?.date_format} ${user?.location?.time_format}`,
            user,
            t
          )}
        </span>
      ),
    },
  ];

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const toggleSelection = (record) => {
    const key = record.key;
    const newSelectedRowKeys = selectedRowKeys.includes(key)
      ? selectedRowKeys.filter((k) => k !== key)
      : [...selectedRowKeys, key];
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
    renderCell: (checked, record, index, originNode) => (
      <div className="relative">
        <Button
          style={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
          }}
          size="large"
          type="text"
          shape="circle"
          onClick={(e) => {
            e.stopPropagation();
            toggleSelection(record);
          }}
        >
          {originNode}
        </Button>
      </div>
    ),
  };
  const hasSelected = selectedRowKeys.length > 0;

  useEffect(() => {
    if (dataAccounts.length > 0) {
      getMailsTrash();
    }
  }, [getMailsTrash]);

  return (
    <>
      <FormCreate open={openForm} setOpen={setOpenForm} familyId={familyId} />

      {hasSelected ? (
        <div className="mb-[8px] ml-[20px] flex items-center">
          <Button
            type="primary"
            danger
            icon={<DeleteOutlined />}
            onClick={() => {
              setOpenModal(true);
              setTypeDelete("clearTrashById");
            }}
          >
            {t("mailing.ClearTrashButton")} ({selectedRowKeys.length}{" "}
            {selectedRowKeys.length > 1 ? "emails" : "email"})
          </Button>
        </div>
      ) : null}

      <CreateTask
        open={openTask}
        setOpen={setOpenTask}
        mask={false}
        source="mailing"
        object={titleTask}
      />

      <ModalDeleteEmail
        t={t}
        openModal={openModal}
        setOpenModal={setOpenModal}
        loading={loading}
        DeleteMail={
          typeDelete === "clearTrashMultiple" ? ClearTrash : ClearTrashById
        }
        typeDelete={typeDelete}
        selectedRowKeysLength={selectedRowKeys.length}
      />

      <div className="space-x-18 flex h-14 w-full items-center justify-center bg-gray-100 py-4">
        <p className=" text-gray-600">{t("mailing.DeleteDefinitive")}</p>
        {dataMailTrash.length > 0 ? (
          <Button
            color="primary"
            type="link"
            onClick={() => {
              setOpenModal(true);
              setTypeDelete("clearTrashMultiple");
            }}
          >
            {t("mailing.EmptyTrash")}
          </Button>
        ) : null}
      </div>

      <Table
        className="mailing-custom-row"
        loading={loading}
        rowSelection={rowSelection}
        columns={columns}
        dataSource={dataSourceInbox}
        showSizeChanger={false}
        pagination={{
          current: page,
          pageSize: pageSize,
          pageSizeOptions: ["10", "20", "30"],
          total: metaMailInbox.total === 0 ? 1 : metaMailInbox.total,
          showSizeChanger: true,
          onChange: (page, pageSize) => {
            dispatch(setPage(page));
            dispatch(setPageSize(pageSize));
          },
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total}`,
        }}
        scroll={{ y: "calc(100vh - 250px)" }}
        onRow={(record) => {
          return {
            onClick: () => {
              setDetailsMail([]);
              dispatch(setNumberEmailThread(record.nbr));
              navigate(`/mailing/${usedAccount.value}/trash/${record.key}`);
            },
          };
        }}
        rowClassName={(_, index) =>
          `${index === 5 ? "" : "clickable-row"} group`
        }
        size="small"
        locale={{
          emptyText: (
            <p className={error ? "mt-4 text-red-600" : "mt-4 text-[#898282]"}>
              {error
                ? t("toasts.errorFetchApi")
                : loading.state && loading.type === "mails"
                ? "Loading ..."
                : t("mailing.noData")}
            </p>
          ),
        }}
      />
    </>
  );
};

export default Trash;
