import React, { useState } from "react";
import ReactQuill, { Quill } from "react-quill";
import "react-quill/dist/quill.snow.css";
import "./Editor.css";
import quillEmoji from "react-quill-emoji";
import "react-quill-emoji/dist/quill-emoji.css";

const CustomHeart = () => <span>Fidelds</span>;

/*
 * Custom toolbar component including the custom heart button and dropdowns
 */
const CustomToolbar = () => (
  <div id="toolbar-11">
    <select className="ql-header" defaultValue="">
      <option value="1"></option>
      <option value="2"></option>
      <option value=""></option>
    </select>
    <button className="ql-list" value="ordered" />
    <button className="ql-list" value="bullet" />
    <button className="ql-bold"></button>
    <button className="ql-italic"></button>
    <button className="ql-align" value="" />
    <button className="ql-align" value="center" />
    <button className="ql-align" value="right" />
    <select className="ql-color" />
    <select className="ql-background" />
    <button className="ql-image" />
    <button className="ql-clean" />
    <button className="ql-emoji" />
    <button className="ql-insertHeart">
      <CustomHeart />
    </button>
  </div>
);

// Add sizes to whitelist and register them
const Size = Quill.import("formats/size");
Size.whitelist = ["extra-small", "small", "medium", "large"];
Quill.register(Size, true);

// Add fonts to whitelist and register them
const Font = Quill.import("formats/font");
Font.whitelist = [
  "arial",
  "comic-sans",
  "courier-new",
  "georgia",
  "helvetica",
  "lucida",
];
Quill.register(Font, true);

const Emoji = Quill.import("formats/emoji");
Emoji.whitelist = ["extra-small", "small", "medium", "large"];
Quill.register(Emoji, true);

Quill.register(
  {
    "formats/emoji": quillEmoji.EmojiBlot,
    "modules/emoji-toolbar": quillEmoji.ToolbarEmoji,
    "modules/emoji-textarea": quillEmoji.TextAreaEmoji,
    "modules/emoji-shortname": quillEmoji.ShortNameEmoji,
  },
  true
);

const Editor = () => {
  const [value, setValue] = useState("");

  const CustomToolbar = () => (
    <div id="toolbar-11">
      <select className="ql-header" defaultValue="">
        <option value="1"></option>
        <option value="2"></option>
        <option value=""></option>
      </select>
      <button className="ql-list" value="ordered" />
      <button className="ql-list" value="bullet" />
      <button className="ql-bold"></button>
      <button className="ql-italic"></button>
      <button className="ql-align" value="" />
      <button className="ql-align" value="center" />
      <button className="ql-align" value="right" />
      <select className="ql-color" />
      <select className="ql-background" />
      <button className="ql-image" />
      <button className="ql-clean" />
      <button className="ql-emoji" />
      {/* <button className="ql-insertHeart">
        <CustomHeart />
      </button> */}
    </div>
  );
  function insertHeart() {
    const cursorPosition = this.quill.getSelection().index;
    this.quill.insertText(cursorPosition, "Bouzaien");
  }

  const modules = {
    toolbar: {
      container: "#toolbar-11",
      handlers: {
        insertHeart: insertHeart,
      },
    },
    "emoji-toolbar": true,
    "emoji-textarea": true,
    "emoji-shortname": true,
  };

  const formats = [
    "header",
    "font",
    "size",
    "bold",
    "italic",
    "underline",
    "strike",
    "blockquote",
    "list",
    "bullet",
    "indent",
    "link",
    "image",
    "color",
    "align",
    "emoji",
  ];

  return (
    <div className="text-editor">
      <CustomToolbar />
      <ReactQuill
        value={value}
        onChange={setValue}
        modules={modules}
        formats={formats}
        theme="snow"
        className="rounded-lg"
      />
    </div>
  );
};

export default Editor;
