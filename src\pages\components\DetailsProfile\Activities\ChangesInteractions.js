import { Typo<PERSON>, Image, But<PERSON>, Avatar, Popover } from "antd";
import React, { useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { IoEllipsisHorizontalCircleSharp } from "react-icons/io5";
import { AnimatePresence, motion } from "framer-motion";
import { URL_ENV } from "index";
import OneInetraction from "./OneInetraction";
import {
  ArrowDownOutlined,
  ArrowUpOutlined,
  MinusOutlined,
  PlusOutlined,
} from "@ant-design/icons";
export const isImagePath = (value) => {
  const imageExtensions = /\.(jpe?g|png|gif|bmp|svg)$/i;
  return imageExtensions.test(value);
};
const ChangesInteractions = ({ item }) => {
  const [show, setShow] = useState(item.changes.length > 3 ? false : true);
  const [t] = useTranslation("common");
  return (
    <>
      <AnimatePresence>
        <motion.div
          initial={{ height: 0, opacity: 0 }}
          animate={{ height: show ? "auto" : 0, opacity: show ? 1 : 0 }}
          exit={{ height: 0, opacity: 0 }}
          transition={{ duration: 0.3 }}
        >
          <ul className="space-y-0.5">
            {item?.changes &&
              Object.entries(item.changes).map(([key, value], index) => (
                <OneInetraction
                  key={key}
                  keyValue={key}
                  value={value}
                  index={index}
                />
              ))}
          </ul>
        </motion.div>
      </AnimatePresence>
      <Button
        type="link"
        size="large"
        className="justify-centercursor-pointer mt-1.5  inline-flex  w-full items-center space-x-1 text-sky-700 hover:!text-sky-600"
        onClick={() => setShow(!show)}
        icon={show ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
        // aria-expanded={show}
      >
        {show ? t("vue360.hide") : t("vue360.show") + "..."}
      </Button>
    </>
  );
};

export default ChangesInteractions;
