import { <PERSON><PERSON>, Divider, List, Skeleton, Spin } from "antd";
import React from "react";
import VirtualList from "rc-virtual-list";
import {
  dispositionInfo,
  formatDateComparison,
} from "pages/voip/helpers/helpersFunc";
import { useTranslation } from "react-i18next";
import { AvatarChat } from "components/Chat";
import { URL_ENV } from "index";
import { getName } from "pages/layouts/chat/utils/ConversationUtils";
import { useSelector } from "react-redux";
import { ConditionIcons } from "pages/rmc/mailing/components/Log";
import { useWindowSize } from "custom-hooks/useWindowSize";
import { getIconRmc } from "pages/components/DetailsProfile/ChatRmc";

const ListInteractionInInbox = ({
  list,
  setSelectedItem,
  selectedItem,
  setCurrentPage,
  lastPage,
  loading,
}) => {
  const [t] = useTranslation("common");
  const { user } = useSelector((state) => state.user);
  const [_, height] = useWindowSize();

  const onScroll = (e) => {
    // Refer to: https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollHeight#problems_and_solutions
    if (
      Math.abs(
        e.currentTarget.scrollHeight -
          e.currentTarget.scrollTop -
          (height - 200)
      ) <= 1
    ) {
      setCurrentPage((prev) => prev + 1);
    }
  };

  return (
    <List>
      <Spin spinning={loading}>
        <VirtualList
          data={list}
          height={height - 200}
          itemHeight={47}
          itemKey="email"
          onScroll={onScroll}
        >
          {(item) => (
            <div key={item.date?.replace(/\s/g, "")}>
              <Divider
                style={{
                  textAlign: "center",
                  position: "sticky",
                  top: "0",
                  background: "transparent",
                  zIndex: "20",
                  marginTop: "-10px",
                  padding: "5px 0",
                }}
              >
                {formatDateComparison(item.date, t)}
              </Divider>

              <div className="pb-2">
                {item.items.map((el) => {
                  const { icon } = dispositionInfo(
                    { ...el, disposition: el.action_type },
                    user?.name === el.user,
                    t
                  );
                  return (
                    <div
                      className={`${
                        selectedItem?.date === el?.date
                          ? "bg-blue-100"
                          : "hover:bg-blue-50"
                      }  cursor-pointer rounded-md px-2`}
                    >
                      <List.Item
                        key={el.date}
                        onClick={() => setSelectedItem(el)}
                        className=""
                      >
                        <List.Item.Meta
                          avatar={
                            <AvatarChat
                              fontSize="0.875rem"
                              url={
                                URL_ENV?.REACT_APP_BASE_URL +
                                URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                                el?.avatar?.path
                              }
                              type="user"
                              size={36}
                              height={10}
                              width={10}
                              name={getName(
                                Array.isArray(el?.user)
                                  ? el?.user[0]
                                  : el?.user,
                                "avatar"
                              )}
                              hasImage={false}
                            />
                          }
                          title={
                            Array.isArray(el?.user) ? el?.user[0] : el?.user
                          }
                          description={
                            <div className="inline-flex items-center gap-x-2">
                              <span>
                                {" "}
                                {el.family_id === 9
                                  ? getIconRmc(el.type)
                                  : t(`vue360.${el.family_id}`)}
                              </span>
                              {el.type === "voip" ? (
                                icon
                              ) : el.type === "email" ? (
                                <span className="-mt-2 inline-flex">
                                  {ConditionIcons(el)}
                                </span>
                              ) : (
                                ""
                              )}
                            </div>
                          }
                        />
                        <div>{el?.date?.split(" ")[1]?.slice(0, 5)}</div>
                      </List.Item>
                    </div>
                  );
                })}
              </div>
              {/* {loading ? (
              <Skeleton
                avatar
                paragraph={{
                  rows: 1,
                }}
                active
              />
            ) : null} */}
              {/*
               */}
            </div>
          )}
        </VirtualList>
      </Spin>
    </List>
  );
};

export default ListInteractionInInbox;
