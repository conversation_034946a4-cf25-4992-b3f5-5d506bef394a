import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import TabsDetails from "../../components/Tabs";
import { useLayoutEffect } from "react";
import { useSelector } from "react-redux";

const General = ({ active }) => {
  const [t] = useTranslation("common");
  const [keyTab, setKeyTab] = useState("");
  const { pathname } = useLocation();
  const { user } = useSelector((state) => state.user);
  const navigate = useNavigate();
  useLayoutEffect(() => {
    if (pathname == "/settings/general/departments") {
      setKeyTab("1");
    } else if (pathname == "/settings/general/services") {
      setKeyTab("2");
    } else if (pathname == `/settings/general/companies`) {
      setKeyTab("3");
    } else if (pathname == "/settings/general/channels") {
      setKeyTab("4");
    } else if (pathname == "/settings/general/countries") {
      setKeyTab("5");
    } else if (pathname == "/settings/general/guestQueue") {
      setKeyTab("6");
    } else {
      setKeyTab("3");
    }
  }, []);
  useEffect(() => {
    if (keyTab !== "") {
      if (keyTab == 1) {
        navigate("/settings/general/departments");
      } else if (keyTab == 2) {
        navigate("/settings/general/services");
      } else if (keyTab == 3) {
        navigate(`/settings/general/companies`);
      } else if (keyTab == 4) {
        navigate("/settings/general/channels");
      } else if (keyTab == 5) {
        navigate("/settings/general/countries");
      } else if (keyTab == 6) {
        navigate("/settings/general/guestQueue");
      }
    }
  }, [keyTab, navigate]);

  const items = [
    {
      label: (
        <div
          onClick={() => {
            setKeyTab("3");
            navigate(`/settings/general/companies`);
          }}
        >
          {t("general.companies")}
        </div>
      ),
      key: "3",
    },
    {
      label: (
        <div
          onClick={() => {
            setKeyTab("1");
            navigate(`/settings/general/departments`);
          }}
        >
          {user.tenant === "spheredev2" || user.tenant === "taoufikhospitals"
            ? t("tasks.clinics")
            : t("table.header.departments")}
        </div>
      ),
      key: "1",
    },
    {
      label: (
        <div
          onClick={() => {
            setKeyTab("2");
            navigate(`/settings/general/services`);
          }}
        >
          Services
        </div>
      ),
      key: "2",
    },

    {
      label: (
        <div
          onClick={() => {
            setKeyTab("4");
            navigate(`/settings/general/channels`);
          }}
        >
          {t("general.channels")}
        </div>
      ),
      key: "4",
    },
    {
      label: (
        <div
          onClick={() => {
            setKeyTab("5");
            navigate(`/settings/general/countries`);
          }}
        >
          {t("general.countries")}
        </div>
      ),
      key: "5",
    },
    {
      label: (
        <div
          onClick={() => {
            setKeyTab("6");
            navigate(`/settings/general/guestQueue`);
          }}
        >
          {t("general.queueGuest")}
        </div>
      ),
      key: "6",
    },
  ];

  return (
    <>
      {keyTab ? (
        <TabsDetails items={items} keyTab={keyTab} setKey={setKeyTab} />
      ) : (
        ""
      )}
    </>
  );
};

export default General;
