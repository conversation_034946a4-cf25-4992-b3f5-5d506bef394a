import React, { useEffect, useRef } from "react";
import { Form, Input, Button, Space } from "antd";
import { useState } from "react";
import { generateAxios } from "../services/axiosInstance";
import { useTranslation } from "react-i18next";
import { PlusCircleOutlined } from "@ant-design/icons";
import { toastNotification } from "./ToastNotification";
import Header from "./configurationHelpDesk/Header";
import NewTableDraggable from "./NewTableDraggable";
import LabelTable from "./LabelTable";
import { SubmitKeyPress } from "../utils/SubmitKeyPress";
import { URL_ENV } from "index";

const ReasonSales = ({
  data,
  setData,
  reason_type,
  editingKey,
  setEditingKey,
  keyTab,
  btnText,
  load,
}) => {
  const [form] = Form.useForm();
  //   const [data,setData]=useState([])
  const [count, setCount] = useState(0);
  const [rank, setRank] = useState("");
  const [id, setId] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState("");
  const [pageSize, setPageSize] = useState(20);

  const isEditing = (record) => record.key === editingKey;
  const [t] = useTranslation("common");

  const inputRefs = useRef([]);
  useEffect(() => {
    return () => setData((prev) => prev.filter((el) => el.id));
  }, [setData, keyTab]);
  useEffect(() => {
    inputRefs.current.forEach((input) => {
      input?.focus();
    });
  }, [data.length, id]);
  const onFinishFailed = (values) => {
    console.log(values);
  };

  const EditableCell = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    const inputNode = (
      <Input
        ref={(el) => (inputRefs.current[index] = el)}
        onKeyPress={handleKeyPress}
        placeholder={t("activities.name")}
      />
    );

    return (
      <td {...restProps}>
        {editing ? (
          <Form.Item
            name={dataIndex.toLowerCase()}
            style={{
              margin: 0,
            }}
            rules={[
              {
                required: true,
                message: `${title} ${t("table.header.isrequired")}`,
              },
            ]}>
            {inputNode}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };
  const edit = (record) => {
    if (record) {
      form.setFieldsValue({
        label: record.label,
        icon: record.icon,
      });
      setId(record.id);
      setRank(record.rank);
      inputRefs.current[1]?.input.focus();
    } else {
      form.setFieldsValue({
        label: "",
        icon: "",
      });
    }
    setEditingKey(record.key);
  };
  const cancel = (record) => {
    setEditingKey("");
    setId(null);
    if (!record.id) {
      setData(data.filter((item) => item.key !== record.key));
    }
  };
  const save = async (key) => {
    setLoading(true);
    if (id) {
      try {
        const row = await form.validateFields();
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post(`/reasons/update/${id}`, {
          ...row,
          reason_type,
        });
        setEditingKey("");
        setData(
          data.map((el) =>
            el.id === res.data.data.id
              ? {
                  ...res.data.data,
                  key: res.data.data.id,
                  rank,
                }
              : el
          )
        );
        form.setFieldsValue({
          label: "",
          icon: "",
        });
        setLoading(false);
        toastNotification("success", row.label + t("toasts.edit"), "topRight");
      } catch (errInfo) {
        setLoading(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    } else {
      try {
        const row = await form.validateFields();
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post("/reasons", {
          ...row,
          reason_type,
        });
        setEditingKey("");
        setData([
          ...data.filter((el) => el.id),
          { ...res.data.data, key: res.data.data.id, rank: data.length },
        ]);
        form.setFieldsValue({
          label: "",
          icon: "",
        });
        setLoading(false);
        toastNotification(
          "success",
          row.label + t("toasts.created"),
          "topRight"
        );
      } catch (errInfo) {
        setLoading(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    }
  };

  const handleKeyPress = (event) => {
    SubmitKeyPress(event, form);
  };
  const handleClick = (event) => {
    event.stopPropagation();
  };

  const columns = [
    {
      title: t("activities.name"),
      dataIndex: "label",
      key: "label",
      editable: true,
      sorter: (a, b) => a.label.localeCompare(b.label),
      render: (_, record) => {
        return (
          <LabelTable record={record} editingKey={editingKey} edit={edit} />
        );
      },
    },
  ];
  const mergedColumns = columns.map((col, index) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record) => ({
        record,
        inputType: col.dataIndex === "input",
        dataIndex: col.dataIndex,
        title: col.title,
        editing: isEditing(record),
        index,
      }),
    };
  });
  const handleAdd = () => {
    const ids = data.map((object) => {
      return object.id;
    });
    setId(null);
    const newData = {
      key: Math.max(...ids) + 1,
      label: `  `,
      disabled: true,
    };
    setCurrentPage(Math.ceil([...data, newData].length / pageSize));

    setData([...data, newData]);
    form.setFieldsValue({
      label: "",
      color: "",
    });
    setEditingKey(Math.max(...ids) + 1);
    setCount(Math.max(...ids) + 1);
  };
  const onRow = () => {};

  return (
    <Space direction="vertical" style={{ width: "100%" }}>
      <Header
        active={"5"}
        editingKey={editingKey}
        handleAdd={handleAdd}
        btnText={btnText}
        disabled={load ? true : editingKey ? true : false}
      />

      <NewTableDraggable
        columns={columns}
        setLoading={setLoading}
        isEditing={isEditing}
        data={data}
        setData={setData}
        loading={load}
        save={save}
        edit={edit}
        EditableCell={EditableCell}
        onFinishFailed={onFinishFailed}
        cancel={cancel}
        form={form}
        apiRank="/rank-reasons"
        editingKey={editingKey}
        api="reasons"
        onRow={onRow}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        pageSize={pageSize}
        setPageSize={setPageSize}
      />

      <div style={{ width: "0", paddingLeft: "15px" }}>
        <Button
          icon={<PlusCircleOutlined />}
          disabled={load ? true : editingKey ? true : false}
          onClick={handleAdd}
          type="link"
          block>
          {btnText}
        </Button>
      </div>
    </Space>
  );
};
export default ReasonSales;
