import {
  DisconnectOutlined,
  DownOutlined,
  EditOutlined,
  GlobalOutlined,
  InfoOutlined,
  MailOutlined,
  MessageOutlined,
  PlusOutlined,
  RestOutlined,
  SwapOutlined,
} from "@ant-design/icons";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  But<PERSON>,
  Card,
  Col,
  Divider,
  Drawer,
  Dropdown,
  Input,
  List,
  Menu,
  Popconfirm,
  Row,
  Skeleton,
  Space,
  Tag,
  Tooltip,
  Typography,
} from "antd";
import { toastNotification } from "components/ToastNotification";
import ProfileDetails from "pages/clients&users/components/contacts-details-component/ProfileDetails";
import PopOverSelectModule from "pages/clients&users/components/PopOverSelectModule";
import {
  deleteAssociation,
  getElementSystemDetails,
} from "pages/clients&users/services/services";
import { getFirst2Chart } from "pages/voip/helpers/helpersFunc";
import React, { useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import {
  FiEdit,
  FiMoreVertical,
  FiPhoneForwarded,
  FiSearch,
} from "react-icons/fi";
import { useSelector } from "react-redux";
import RelationsViewSphere from "./RelationsViewSphere";
import { Tb360View } from "react-icons/tb";
import { familyIcons } from "./ViewSphere2";
import {
  addLastIdToViewSphere,
  setNewInteraction,
  setOpenView360InDrawer,
} from "new-redux/actions/vue360.actions/vue360";
import { useDispatch } from "react-redux";
import ViewSphere from "./ViewSphere";
import { SET_CONTACT_INFO_FROM_DRAWER } from "new-redux/constants";
import FormUpdate from "pages/clients&users/components/FormUpdate";
import Confirm from "components/GenericModal";
import { setChatSelectedConversation } from "new-redux/actions/chat.actions";
import MainService from "services/main.service";
import { AvatarChat } from "components/Chat";
import { URL_ENV } from "index";
import { getName } from "pages/layouts/chat/utils/ConversationUtils";
import { DropDownActionViewSphere, formattingNumPhones } from "./GridView";
import useActionCall from "pages/voip/helpers/ActionCall";
import { openDrawerChat } from "new-redux/actions/voip.actions/handleDrawerChat";
import { setOpenModalEmail } from "new-redux/actions/mail.actions";
import { copyIcon } from "pages/clients&users/components/RenderDescriptionDetails";
import { WrapText } from "lucide-react";
import { setEmailFields } from "pages/rmc/mailing/main-components/email-composer-modal/utils";

const ListItemsRelations = ({
  list,
  familyId,
  setRelations,
  setSelectedItem,
  selectedItem,
  headerHeight,
  data,
  contactInfo,
  setSelectedKey,
  setOpenChat,
  setSelectedKeySideBar,
  setListConv,
  openView360InDrawer,
  // setElementDetailsGridView,
  // elementDetailsGridView,
  // openDrawerUpdate,
  // setOpenDrawerUpdate,
}) => {
  // const [selectedItem, setSelectedItem] = React.useState(null);
  const [search, setSearch] = React.useState("");
  const [loading, setLoading] = React.useState("");
  const [btnFocus, setBtnFocus] = React.useState(false);
  const [item, setItem] = React.useState("");
  const [catchChange, setCatchChange] = React.useState(true);
  const [elementDetailsGridView, setElementDetailsGridView] = React.useState(
    {}
  );

  const [openDrawerUpdate, setOpenDrawerUpdate] = React.useState(false);
  const call = useActionCall();

  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state?.user);
  const { contactInfoFromDrawer } = useSelector((state) => state?.vue360);
  const { contactHeaderInfo } = useSelector((state) => state?.contacts);
  useEffect(() => {
    if (openDrawerUpdate && catchChange && contactInfoFromDrawer?.id) {
      dispatch(setNewInteraction({ type: "associateElementFromDrawer" }));

      if (contactHeaderInfo?.id === item?.id) {
        dispatch(setNewInteraction({ type: "associateElementFromDrawer" }));
        setTimeout(() => {
          dispatch(setNewInteraction({ type: "updateElement" }));
        }, 1000);
      }
      setCatchChange(false);
    } else if (openDrawerUpdate && catchChange && contactHeaderInfo?.id) {
      dispatch(setNewInteraction({ type: "associateElement" }));
      setCatchChange(false);
    }
  }, [catchChange, item, openDrawerUpdate]);
  // useEffect(() => {
  //   if (!openDrawerUpdate) {
  //     setElementDetailsGridView({});
  //   }
  // }, [openDrawerUpdate]);
  const items = [
    {
      label: t("table.edit"),
      key: "1",
      icon: <EditOutlined />,
      // disabled: record?.default == 1 || record?.system == 1 ? true : false,
      //  || source == "fieldOptions"
    },

    contactHeaderInfo?.id !== selectedItem?.id && {
      label: t("voip.view360"),
      key: "3",
      icon: <GlobalOutlined />,
    },

    {
      type: "divider",
    },
    {
      label: t("voip.disassociate"),
      danger: true,
      key: "2",
      // disabled: data.type_relation === 1,
      icon: <DisconnectOutlined />,
    },
  ];
  const getAccessDisussion = async (contactInfo) => {
    try {
      const { data: element } = await getElementSystemDetails(contactInfo?.id);
      dispatch({
        type: openView360InDrawer
          ? SET_CONTACT_INFO_FROM_DRAWER
          : "SET_CONTACT_HEADER_INFO",
        payload: {
          ...contactInfo,
          access_discussion: element.access_discussion,
        },
      });
    } catch (err) {}
  };
  const removeAssociation = async (associationId, familyId) => {
    return new Promise((resolve, reject) => {
      deleteAssociation(contactInfo?.id, { id: associationId })
        .then(() => {
          if (openView360InDrawer && associationId === contactHeaderInfo?.id) {
            dispatch(setNewInteraction({ type: "associateElement" }));
          }
          setLoading(true);
          setRelations((prevKPIs) =>
            prevKPIs.map((kpi) => {
              if (kpi.family_id !== familyId) return kpi;
              const { number, children, child, ...rest } = kpi;
              number === 1 && setSelectedKey("1");
              return {
                ...rest,
                number: number - 1,
                children: children.filter((c) => c.id !== associationId),
                child: child.filter((c) => c.id !== associationId),
              };
            })
          );
          MainService.getConvRmc360(contactInfo?.id)
            .then((convRmc) => {
              setListConv(convRmc.data.data.filter((el) => el.conversation_id));
            })
            .catch((error) => {
              console.error("An error occurred:", error);
            });
          getAccessDisussion(contactInfo);
          if (associationId === selectedItem?.id) setSelectedItem(null);
          // if (
          //   associationId !== user?.id ||
          //   contactInfo?.owner?.id !== user?.id ||
          //   (contactInfo?.owner?.id === user?.id && data.child.length === 1)
          // ) {
          //   getAccessDisussion(contactInfo)

          // }
          //   setAddRemoveAssoc((prev) => !prev);
          resolve();
          setLoading(false);
        })
        .catch((err) => {
          if (err?.response?.status === 422) {
            toastNotification(
              "error",
              err?.response?.data?.message,
              "topRight"
            );
          }
          err?.response?.status !== 401 &&
            err?.response?.status !== 422 &&
            toastNotification("error", t("toasts.somethingWrong"), "topRight");
          reject();
          setLoading(false);
        });
    });
  };

  const menuItems = [
    ...(selectedItem?.extension
      ? [
          {
            key: selectedItem.extension,
            label: (
              <div className="flex flex-row justify-between space-x-2">
                {selectedItem.extension}
                {/* Uncomment if you want to show the copy icon */}
                {copyIcon(selectedItem.extension)}
              </div>
            ),
            onClick: () =>
              call(selectedItem.extension, selectedItem.id, familyId),
          },
        ]
      : []),
    ...formattingNumPhones(selectedItem?.phone, user).map(
      ({ callNum, copyNum, displayNum }) => ({
        key: displayNum,
        label: (
          <div className="flex flex-row justify-between space-x-2">
            {displayNum}
            {/* Uncomment if you want to show the copy icon */}
            {copyIcon(copyNum)}
          </div>
        ),
        onClick: () => call(callNum, selectedItem.id, familyId),
      })
    ),
  ];

  const menuPhones = (
    <Menu>
      {menuItems.map((item) => (
        <Menu.Item key={item.key} onClick={item.onClick}>
          {item.label}
        </Menu.Item>
      ))}
    </Menu>
  );
  return (
    <div
      className="mt-3 flex flex-col gap-y-4"
      onClick={() => setBtnFocus(false)}
    >
      <Row gutter={16}>
        <Col
          className="gutter-row "
          // style={{ borderRight: "2px solid #f5f5f5" }}
          md={openView360InDrawer ? 9 : 8}
          xxl={openView360InDrawer ? 9 : 6}
        >
          <div className="flex items-center justify-between">
            <span className="text-base font-semibold">
              Associations {t("voip.with")}{" "}
              {familyIcons(t).find((el) => el.key === familyId)?.label}
            </span>
            {data.typeRelation !== 1 && (
              <RelationsViewSphere
                module={data}
                setSelectedItem={setSelectedItem}
                contactInfo={contactInfo}
                setRelations={setRelations}
                setListConv={setListConv}
                btn={
                  <Button
                    icon={<PlusOutlined />}
                    // type="primary"
                    // shape="circle"
                    onClick={(e) => e.stopPropagation()}
                  />
                }
              />
            )}
          </div>
          {/* <Divider plain>{`1-${list?.length} of ${list.length} items`}</Divider> */}
          {/* en attendant que les types seront corrigé coté back */}
          {/* {data.fieldType === "multiselect" ? (
            <div className="-mt-1">
              {data.typeRelation !== 1 ? (
                <span className=" inline-flex w-full items-center justify-between gap-x-1 text-xs">
                  <Alert
                    message={
                      <span className="text-xs font-semibold">
                        {t("vue360.multipleRelations")}
                      </span>
                    }
                    type="info"
                    showIcon
                  />

                  <RelationsViewSphere
                    module={data}
                    setSelectedItem={setSelectedItem}
                    contactInfo={contactInfo}
                    setRelations={setRelations}
                    setListConv={setListConv}
                    btn={
                      <Button
                        icon={<PlusOutlined />}
                        // type="primary"
                        // shape="circle"
                        onClick={(e) => e.stopPropagation()}
                      />
                    }
                  />
                </span>
              ) : null}
            </div>
          ) : (
            <>
              {data.typeRelation !== 1 ? (
                <div className="-mt-1">
                  <span className=" inline-flex w-full items-center justify-between gap-x-1 text-xs ">
                    <Alert
                      message={
                        <span className="text-xs font-semibold">
                          {t("vue360.oneRelation")}
                        </span>
                      }
                      type="info"
                      showIcon
                    />
                    <RelationsViewSphere
                      contactInfo={contactInfo}
                      setSelectedItem={setSelectedItem}
                      module={data}
                      setListConv={setListConv}
                      btn={
                        <Button
                          icon={<SwapOutlined />}
                          // type="primary"
                          // shape="circle"
                          onClick={(e) => e.stopPropagation()}
                        />
                      }
                      setRelations={setRelations}
                    />
                  </span>
                </div>
              ) : null}
            </>
          )} */}
          {data.child.length > 1 ? (
            <Input
              prefix={<FiSearch className="text-slate-400" />}
              onChange={(e) => {
                setSearch(e.target.value);
              }}
              placeholder={t("table.search")}
              style={{ width: "100%", marginTop: "8px" }}
            />
          ) : null}
          <div
            className="mt-2 overflow-auto "
            style={{
              height: `calc(100vh - ${
                data.typeRelation === 0
                  ? headerHeight + (data.child.length > 1 ? 215 - 17 : 185)
                  : headerHeight + (data.child.length > 1 ? 185 - 17 : 153)
              }px)`,
              marginRight: "-6px",
              paddingRight: "4px",
            }}
          >
            <List
              className="items-associations-view-sphere"
              // className="demo-loadmore-list"
              itemLayout="horizontal"
              dataSource={list.filter((el) =>
                el.label_data.toLowerCase().includes(search.toLowerCase())
              )}
              renderItem={(item, i) => (
                <>
                  <List.Item
                    style={{
                      background: selectedItem?.id === item?.id && "#F1F5F9",
                      padding: "8px",
                      borderRadius: " 8px",
                      cursor: "pointer",
                      margin: "4px 0",
                      borderBlockEnd: 0,
                      overflowX: "hidden",
                      border: `2px solid ${
                        selectedItem?.id === item?.id ? "#93c5fd" : "#e5e7eb"
                      }`,
                    }}
                    className="hover:bg-[#f8fafc]"
                    //   actions={[
                    //     <a key="list-loadmore-edit">edit</a>,
                    //     <a key="list-loadmore-more">more</a>,
                    //   ]}
                  >
                    <Skeleton
                      avatar
                      title={false}
                      loading={item.loading}
                      active
                    >
                      <List.Item.Meta
                        onClick={() => setSelectedItem(item)}
                        avatar={
                          <AvatarChat
                            fontSize="0.875rem"
                            url={
                              URL_ENV?.REACT_APP_BASE_URL +
                              URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                              item?.avatar
                            }
                            type="user"
                            size={35}
                            height={10}
                            width={10}
                            name={getName(item?.label_data, "avatar")}
                            hasImage={
                              item?.avatar &&
                              item?.avatar !== "/storage/uploads/"
                            }
                          />
                        }
                        title={item?.label_data}
                        description={
                          <div className="flex items-center justify-between ">
                            <div className="text-slate-500">
                              {item?.pipeline_label}
                              {item?.pipeline_label && item?.stage_label ? (
                                <Divider
                                  type="vertical"
                                  style={{ borderColor: "#64748b" }}
                                />
                              ) : null}
                              {item?.stage_label}
                            </div>
                          </div>
                        }
                      />
                      <div
                        // className={`rounded-r-lg ${
                        //   selectedItem?.id === item?.id && "bg-[#E2E8F0] "
                        // }  px-1 pb-[13px] pt-[13px] `}
                        className="flex items-center space-x-1"
                      >
                        {item?.field ? (
                          <Tooltip
                            title={item?.field.length > 15 ? item?.field : ""}
                          >
                            <Tag color="geekblue">
                              {item?.field.length > 15
                                ? item?.field.slice(0, 15) + "..."
                                : item?.field}
                            </Tag>
                          </Tooltip>
                        ) : null}
                        {/* {console.log(item)} */}
                        <DropDownActionViewSphere
                          item={{
                            family_id: familyId,

                            extension: item?.extension,
                            name: item?.label_data,
                            phone: formattingNumPhones(item?.phone, user),
                            email: item?.email,
                            uuid: item?.uuid,
                            id: item?.id,
                          }}
                          contactInfo={contactInfo}
                          setElementDetailsGridView={setElementDetailsGridView}
                          t={t}
                          call={call}
                          dispatch={dispatch}
                          otherItems={[
                            { type: "divider" },
                            {
                              key: "2",
                              danger: true,
                              icon: (
                                <DisconnectOutlined
                                  style={{ fontSize: "14px" }}
                                />
                              ),
                              label: t("voip.disassociate"),
                              onClick: (e) => {
                                if (
                                  (!openView360InDrawer &&
                                    data.typeRelation === 1) ||
                                  (openView360InDrawer &&
                                    item?.id !== contactHeaderInfo?.id)
                                ) {
                                  e.domEvent.stopPropagation();
                                  setBtnFocus(true);
                                  setSelectedItem(item);
                                } else {
                                  Confirm(
                                    t("voip.removeAssociation"),
                                    "Confirm",
                                    <SwapOutlined style={{ color: "red" }} />,
                                    function func() {
                                      return removeAssociation(
                                        item?.id,
                                        familyId
                                      );
                                    },
                                    true,
                                    t("contacts.deleteAssociation")
                                  );
                                }
                              },
                            },
                          ]}
                          // copyIcon={copyIcon}
                          setOpenDrawerUpdate={setOpenDrawerUpdate}
                          setOpenChat={setOpenChat}
                          setSelectedKeySideBar={setSelectedKeySideBar}
                        />
                        {/* <Dropdown
                          trigger={["click"]}
                          placement="bottomRight"
                          menu={{
                            items,
                            onClick: (e) => {
                              if (e.key === "1") {
                                setItem(item);
                                setOpenDrawerUpdate(true);
                              }
                              if (e.key === "2") {
                                e.domEvent.stopPropagation();
                                if (data.type_relation === 1) {
                                  setBtnFocus(true);
                                  setSelectedItem(item);
                                } else
                                  Confirm(
                                    t("voip.removeAssociation"),
                                    "Confirm",

                                    <SwapOutlined style={{ color: "red" }} />,
                                    function func() {
                                      return removeAssociation(
                                        item?.id,
                                        familyId
                                      );
                                    },
                                    true,
                                    t("contacts.deleteAssociation")
                                  );
                              }
                              if (e.key === "3") {
                                dispatch({
                                  type: SET_CONTACT_INFO_FROM_DRAWER,
                                  payload:
                                    {
                                      id: item.id,
                                      // ...selectedItem,
                                    } || {},
                                });
                                !openView360InDrawer &&
                                  dispatch(setOpenView360InDrawer(true));
                                dispatch(
                                  setNewInteraction({
                                    type: "updateElementFromDrawer",
                                  })
                                );
                                dispatch(
                                  addLastIdToViewSphere(selectedItem?.id)
                                );
                                setOpenChat(false);
                                dispatch(
                                  setChatSelectedConversation({
                                    selectedConversation: null,
                                  })
                                );
                                setSelectedKeySideBar("");
                              }
                            },
                          }}
                        >
                          <FiMoreVertical className="h-[18px] w-[18px] cursor-pointer text-gray-400 hover:text-gray-700" />
                        </Dropdown> */}
                      </div>
                    </Skeleton>
                  </List.Item>
                  {/* {list.length - 1 !== i ? (
                    <Divider style={{ margin: "4px 0" }} />
                  ) : null} */}
                </>
              )}
            />
          </div>
        </Col>
        <Col
          className="gutter-row"
          md={openView360InDrawer ? 15 : 16}
          xxl={openView360InDrawer ? 15 : 18}
        >
          <Card
            styles={{ header: { padding: "0px" } }}
            title={
              selectedItem?.id ? (
                <span className="inline-flex w-full justify-end gap-x-2 p-2">
                  {selectedItem?.extension ||
                  (
                    selectedItem?.phone &&
                    formattingNumPhones(selectedItem?.phone, user)
                  )?.length > 0 ? (
                    <Dropdown overlay={menuPhones}>
                      <Button
                        icon={
                          <FiPhoneForwarded
                            style={{ fontSize: "14px" }}
                            className="relative top-0.5"
                          />
                        }
                        size={openView360InDrawer ? "small" : "default"}
                      >
                        <Space>
                          {t("voip.call")}
                          <DownOutlined />
                        </Space>
                      </Button>
                    </Dropdown>
                  ) : null}
                  {familyId === 4 && selectedItem?.id !== user?.id ? (
                    <>
                      {/* <Button
            onClick={() =>
              call(
                `${selectedItem?.extension}`,
                selectedItem?.id,
                familyId
              )
            }
            icon={<FiPhoneForwarded style={{ fontSize: "14px" }} />}
            
            type="primary"
            
          >
            {t("voip.call")}
          </Button> */}

                      <Button
                        icon={<MessageOutlined style={{ fontSize: 14 }} />}
                        size={openView360InDrawer ? "small" : "default"}
                        onClick={() =>
                          dispatch(openDrawerChat(selectedItem?.uuid))
                        }
                      >
                        {t("chat.action.sendMessage")}
                      </Button>
                      {selectedItem?.email ? (
                        <Button
                          icon={<MailOutlined style={{ fontSize: 14 }} />}
                          size={openView360InDrawer ? "small" : "default"}
                          onClick={() => {
                            dispatch(
                              setEmailFields({
                                sender: user.email,
                                receivers: [selectedItem?.email],
                                // contactId: contactInfo?.id,
                                // familyId: contactInfo?.family_id,
                              })
                            );
                            dispatch(setOpenModalEmail(true));
                          }}
                        >
                          EMAIL
                        </Button>
                      ) : null}
                    </>
                  ) : null}
                  <Button
                    icon={<FiEdit className="relative top-0.5 h-3.5 w-3.5  " />}
                    size={openView360InDrawer ? "small" : "default"}
                    onClick={() => {
                      setElementDetailsGridView({
                        id: selectedItem?.id,
                        familyId: familyId,
                        label: selectedItem?.label_data,
                      });
                      setItem(selectedItem);
                      setOpenDrawerUpdate(true);
                    }}
                  >
                    {t("contacts.edit")}
                  </Button>

                  {contactHeaderInfo?.id !== selectedItem?.id ? (
                    <Tooltip
                      color="red"
                      title={
                        btnFocus
                          ? t("vue360.NotAuthTodeleteAssoc", {
                              module: familyIcons(t).find(
                                (el) => el.value === contactInfo?.family_id
                              )?.label,
                              label:
                                contactInfo?.family_id === 6
                                  ? contactInfo?.subject_helpdesk
                                  : contactInfo?.name,
                            })
                          : ""
                      }
                      open={btnFocus && true}
                    >
                      <Button
                        style={{
                          outline: btnFocus ? "3px solid #1677ff5c" : "",
                        }}
                        type={btnFocus ? "text" : "default"}
                        icon={<GlobalOutlined spin={btnFocus ? true : false} />}
                        size={openView360InDrawer ? "small" : "default"}
                        onClick={() => {
                          setBtnFocus(false);
                          // setSelectIdInDrawer(selectedItem?.id);
                          setItem(selectedItem?.id);
                          dispatch({
                            type: SET_CONTACT_INFO_FROM_DRAWER,
                            payload:
                              {
                                id: selectedItem?.id,
                                ...selectedItem,
                              } || {},
                          });
                          !openView360InDrawer &&
                            dispatch(setOpenView360InDrawer(true));
                          dispatch(
                            setNewInteraction({
                              type: "updateElementFromDrawer",
                            })
                          );
                          dispatch(addLastIdToViewSphere(selectedItem?.id));
                          setOpenChat(false);
                          dispatch(
                            setChatSelectedConversation({
                              selectedConversation: null,
                            })
                          );
                          // setOpenDrawer
                          // !openDrawer && setOpenDrawer(true);
                        }}
                      >
                        {t("voip.view360")}
                      </Button>
                    </Tooltip>
                  ) : null}
                </span>
              ) : null
            }
            bordered={true}
            style={{
              minHeight: `calc(100vh - ${headerHeight + 135}px)`,
              boxShadow:
                "0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.06)",
            }}
            headStyle={{
              background: "linear-gradient(to top, #f8fafc, #E6F4FF)",
            }}
          >
            {selectedItem?.id ? (
              // <div
              //   className="overflow-auto"
              //   style={{
              //     maxHeight:
              //       dataSteps.length > 0
              //         ? "calc(100vh - 220px)"
              //         : "calc(100vh - 160px)",
              //   }}
              // >
              <div>
                {selectedItem?.id ? (
                  <ProfileDetails
                    familyId={familyId}
                    contactId={selectedItem?.id}
                    contactName={selectedItem?.label_data}
                    numberOfColumns={3}
                    source="viewSphereRelations"
                    setIsUpdate={setCatchChange}
                    headerHeight={headerHeight}
                    isUpdate={true}
                  />
                ) : null}
              </div>
            ) : (
              <div className="absolute right-0 top-1/2 flex w-full -translate-y-1/2 transform cursor-pointer items-center justify-center">
                <WrapText className="mr-1" />
                <Typography.Title level={3}>
                  {t("emailTemplates.plsSelect")}{" "}
                  {familyIcons(t)
                    .find((el) => el.key === familyId)
                    ?.label.toLowerCase()
                    .slice(0, -1)}
                </Typography.Title>
              </div>
            )}
          </Card>
        </Col>
        <FormUpdate
          open={openDrawerUpdate}
          setOpen={setOpenDrawerUpdate}
          familyId={familyId}
          elementDetails={{
            id: elementDetailsGridView?.id,
            label: elementDetailsGridView?.label,
          }}
          mask={false}
          from="viewSphere"
          setCatchChange={setCatchChange}
        />
      </Row>
    </div>
  );
};

export default ListItemsRelations;
