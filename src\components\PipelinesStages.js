import { useState } from "react";
import SelectStages from "./SelectStages";

const PipelinesStages = ({
  record,
  stages,
  iconsTasks,
  setSelectedStage,
  selectedStage,
  setList = () => {},
  setCountTasks = () => {},
  listFilter,
}) => {
  const [pipeline, setPipeline] = useState("");

  return (
    <SelectStages
      stages={stages}
      record={record}
      iconsTasks={iconsTasks}
      setSelectedStage={setSelectedStage}
      selectedStage={selectedStage}
      setList={setList}
      setPipeline={setPipeline}
      setCountTasks={setCountTasks}
      listFilter={listFilter}
    />
  );
};

export default PipelinesStages;
