import { Checkbox } from "antd";

const FilterTableCols = ({ allColumns, checkedColumns, setCheckedColumns }) => {
  return (
    <Checkbox.Group
      defaultValue={checkedColumns}
      options={allColumns}
      value={checkedColumns}
      onChange={(checkedVal) => setCheckedColumns(checkedVal)}
      style={{
        display: "flex",
        flexDirection: "column",
        justifyContent: "space-between",
      }}
    />
  );
};

export default FilterTableCols;
