import { Button, Space, Table, Tag } from "antd";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { CloseOutlined, DeleteOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import FormCreate from "../../../clients&users/components/FormCreate";
import CreateTask from "../../../voip/components/CreateTask";
import useDebounce from "../../../components/UseDebounce/UseDebounce";
import dayjs from "dayjs";
import { ReadUnreadMessages, TrashListMessages } from "../services/ActionsApi";
import MainService from "../../../../services/main.service";
import { useSelector } from "react-redux";
import FormUpdate from "../../../clients&users/components/FormUpdate";
import {
  setFilterEmail,
  setNumberEmailThread,
  setPage,
  setPageSize,
  setSearchPage,
} from "../../../../new-redux/actions/mail.actions";
import { useDispatch } from "react-redux";
import { useWindowSize } from "pages/clients&users/components/WindowSize";
import Log from "../components/Log";
import useColumnInbox from "../columnsInbox";
import FilterDrawer from "../filterDrawer";
import "../mailing.css";
import ModalDeleteEmail from "../components/ModalDeleteEmail";
import { renderHighlight } from "pages/global-search/components/render-search-items";
import { checkAccessRoleAccount } from "../mailing";

const Inbox = ({ dataAccounts, setDetailsMail, refresh }) => {
  //
  // const lastFilter = localStorage.getItem("mailFilter") || {};
  const usedAccount = useMemo(() => {
    return dataAccounts?.find((item) => item.selected) || dataAccounts?.[0];
  }, [dataAccounts]);
  //
  const {
    newMailNumber,
    refreshMail,
    page,
    refreshMailInbox,
    pageSize,
    searchEmail,
    searchPage,
    filterEmail,
    mailFilter,
  } = useSelector((state) => state.mailReducer);
  //
  const lastFilter =
    usedAccount?.shared === "1" &&
    mailFilter &&
    mailFilter?.[usedAccount?.value]
      ? mailFilter?.[usedAccount?.value]
      : {};
  //
  const [openForm, setOpenForm] = useState(false);
  const [familyId, setFamilyId] = useState(null);

  const [dataMailInbox, setDataMailInbox] = useState([]);
  const [metaMailInbox, setMetaMailInbox] = useState({
    total: 0,
    current_page: 0,
  });
  const [loading, setLoading] = useState({ state: false, type: null });
  const [openModal, setOpenModal] = useState(false);
  const [openTask, setOpenTask] = useState(false);
  const [EmailId, setEmailId] = useState("");
  const [typeDelete, setTypeDelete] = useState("");

  const [openFormUpdate, setOpenFormUpdate] = useState(false);
  const [dataTags, setDataTags] = useState([]);
  const [mailingProps, setMailingProps] = useState({
    label: "",
    email: "",
    idEmail: "",
    familyId: "",
  });
  const [openLogDrawer, setOpenLogDrawer] = useState(null);
  const [isHovered, setIsHovered] = useState(null);
  const [thirdid, setThirdId] = useState("");

  const [assignedType, setAssignedType] = useState(
    lastFilter.assignedType || 4
  );
  const [assignedNames, setAssignedNames] = useState(
    lastFilter.assignedNames || []
  );
  const [chrono, setChrono] = useState(lastFilter.chrono || 0);
  const [dataAssignedFilter, setDataAssignedFilter] = useState(
    lastFilter.dataAssignedFilter || []
  );
  const [dataAssignedNames, setDataAssignedNames] = useState([]);
  const [dataQualifFilter, setDataQualifFilter] = useState(
    lastFilter.dataQualifFilter || []
  );
  const [affectation, setAffectation] = useState([]);
  const [taskId, setTaskId] = useState(null);
  const [selectedFamily, setSelectedFamily] = useState(null);
  const [expandedRows, setExpandedRows] = useState([]);
  const [dateRange, setDateRange] = useState(
    lastFilter.dateRange || [null, null]
  );
  const [selectedStatus, setSelectedStatus] = useState(
    lastFilter.selectedStatus || []
  );

  // const usedAccount = useMemo(() => {
  //   return dataAccounts?.find((item) => item.selected) || dataAccounts?.[0];
  // }, [dataAccounts]);

  const { user } = useSelector(({ user }) => user);

  const dispatch = useDispatch();
  const [t] = useTranslation("common");
  const navigate = useNavigate();
  const debouncedSearchValue = useDebounce(searchEmail, 500);
  const windowSize = useWindowSize();
  const isToday = require("dayjs/plugin/isToday");

  dayjs.extend(isToday);

  const getTags = useCallback(async () => {
    try {
      const response = await MainService.getTags();
      if (response?.status === 200) {
        const data =
          response?.data?.map((tag) => ({
            id: tag?.typetask
              ? `${tag?.id}-${tag?.typetask?.id}`
              : `${tag?.id}`,
            label: tag?.label,
            color: tag?.color,
            icon: tag?.icon,
            taskType: tag?.typetask ? true : false,
          })) || [];
        setDataTags(data);
      }
    } catch (error) {
      console.log(error);
    }
  }, []);

  const getMailsInbox = useCallback(async () => {
    // if (loading.state && loading.type === "mails") return;
    setLoading({ state: true, type: "mails" });

    if (refreshMailInbox === null && metaMailInbox.total !== 0) return;
    // console.log("Go throw the func get");
    var formData = new FormData();

    formData.append("assigned", assignedType);
    if (assignedType === 3) {
      for (let i = 0; i < dataAssignedFilter.length; i++) {
        formData.append("assigned_selected[]", dataAssignedFilter[i]);
      }
    }
    for (let i = 0; i < affectation.length; i++) {
      formData.append("element_id_affectation_filter[]", affectation[i]);
    }
    selectedStatus.length &&
      selectedStatus.forEach((e) => formData.append("state[]", e));
    if (dateRange?.[0] && dateRange?.[1]) {
      formData.append("start_date", dateRange[0]);
      formData.append("end_date", dateRange[1]);
    }
    formData.append("chrono_filter", chrono);
    formData.append("account_id", usedAccount?.value);
    formData.append("folder", "inbox");
    for (let i = 0; i < dataQualifFilter.length; i++) {
      formData.append("qualification[]", dataQualifFilter[i].label);
    }
    let response;
    try {
      response = await MainService.getInboxEmails(
        formData,
        page,
        pageSize,
        searchEmail
      );

      if (response?.status === 200) {
        checkAccessRoleAccount(response, navigate, t);

        setDataMailInbox(response?.data?.data);
        setMetaMailInbox(response?.data?.meta);
        // setLoading({ state: false, type: "mails" });
        dispatch(setSearchPage(1));

        // dispatch(setHasNewMessage(0));
      }
    } catch (err) {
      console.log(err);
      // setLoading({ state: false, type: "mails" });
    } finally {
      setLoading({ state: false, type: "mails" });
    }
  }, [
    usedAccount?.value,
    newMailNumber,
    debouncedSearchValue,
    refreshMail,
    page,
    refresh,
    refreshMailInbox,
    pageSize,
    dispatch,
    chrono,
    assignedType,
    dataAssignedFilter,
    affectation,
    dataQualifFilter,
    searchEmail,
    selectedStatus,
    dateRange,
  ]);

  // const getMailsSearchInbox = useCallback(async () => {
  //   setLoading({ state: true, type: "mails" });

  //   if (refreshMailInbox === null && metaMailInbox.total !== 0) return;
  //   let response;
  //   var formData = new FormData();

  //   formData.append("assigned_to_me", assignedType === 2 ? 1 : 0);
  //   formData.append("assigned_all", assignedType === 1 ? 1 : 0);
  //   for (let i = 0; i < dataAssignedFilter.length; i++) {
  //     formData.append("assigned_selected[]", dataAssignedFilter[i]);
  //   }
  //   for (let i = 0; i < affectation.length; i++) {
  //     formData.append("element_id_affectation_filter[]", affectation[i]);
  //   }
  //   formData.append("chrono_filter", chrono);
  //   formData.append("account_id", usedAccount?.value);
  //   for (let i = 0; i < dataQualifFilter.length; i++) {
  //     formData.append("qualification", dataQualifFilter[i]);
  //   }
  //   try {
  //     response = await MainService.searchMailsInbox(
  //       formData,
  //       searchEmail,
  //       searchPage,
  //       pageSize
  //     );
  //     if (response?.status === 200) {
  //       setDataMailInbox(response?.data?.data);
  //       setMetaMailInbox(response?.data?.meta);
  //       dispatch(setPage(1));
  //     }
  //   } catch (err) {
  //     console.log(err);
  //   } finally {
  //     setLoading({ state: false, type: "mails" });
  //   }
  // }, [
  //   usedAccount?.value,
  //   newMailNumber,
  //   debouncedSearchValue,
  //   refreshMail,
  //   searchPage,
  //   refresh,
  //   refreshMailInbox,
  //   pageSize,
  //   dispatch,
  //   dataQualifFilter,
  // ]);

  const dataSourceInbox = useMemo(() => {
    return dataMailInbox?.map((item, i) => ({
      key: item?.id,
      from: {
        name: item?.from?.name,
        address: item?.from?.address,
        nbr: item?.nbr,
      },
      subject: item?.subject,
      body: item?.body,
      date: item?.date,
      seen: item?.seen,
      starred: item?.starred,
      important: item?.important,
      exist: item?.exist,
      new: item.new ?? "",
      affectation: item?.affectation,
      element_id: item?.element_id,
      owner: item?.owner,
      tags: item?.tags,
      transfert: item?.transfert,
      identification: item?.identification,
      userId: item?.userId,
      third_id: item?.third_id,
      state: item?.state,
      chrono_now: item?.chrono_now,
      expire_Processing_Time: item?.expire_Processing_Time,
      status_processing_duration: item?.status_processing_duration,
      fromThread: item?.from_Thread,
      tasks: item?.tasks,
      nbr: item?.nbr,
      box: item?.box,
      highlight: item.highlight ?? null,
      labelEmail: item?.labelEmail,
      isBounced: Boolean(item?.bounced),
    }));
  }, [dataMailInbox]);

  // trigger when search and rows need to be expand to display highlighted
  useEffect(() => {
    if (!!debouncedSearchValue) {
      const highlightedRows = dataSourceInbox
        .filter((item) => !!item.highlight)
        .map((item) => item.key);
      setExpandedRows(highlightedRows);
    } else setExpandedRows([]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dataSourceInbox]);

  const conditionActions = (record, condition) => {
    if (
      record?.owner?.owner &&
      record?.transfert &&
      record?.owner?.owner === user?.id &&
      record?.transfert?.account_id == usedAccount?.value
    ) {
      return true;
    } else if (
      !record?.owner?.owner &&
      record?.transfert &&
      record?.transfert?.account_id == usedAccount?.value
    ) {
      return true;
    } else if (
      record?.owner?.owner &&
      !record?.transfert &&
      record?.owner?.owner === user?.id &&
      record?.owner?.user_id === user?.id
    ) {
      return true;
    } else if (!record?.owner?.owner && !record?.transfert) {
      return true;
    } else if (
      record?.owner?.owner &&
      !record?.transfert &&
      record?.owner?.owner === user?.id &&
      record?.owner?.user_id !== user?.id
    ) {
      return true;
    } else {
      // verifier
      return false;
    }
    return false;
  };

  const columns = useColumnInbox({
    searchEmail,
    getMailsInbox,
    // getMailsSearchInbox,
    usedAccount,
    setThirdId,
    setOpenLogDrawer,
    setEmailId,
    setOpenModal,
    setTypeDelete,
    dataMailInbox,
    setDataMailInbox,
    t,
    setMailingProps,
    setFamilyId,
    setOpenForm,
    setOpenFormUpdate,
    setOpenTask,
    conditionActions,
    setIsHovered,
    isHovered,
    dataTags,
    setTaskId,
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  const toggleSelection = (record) => {
    const key = record.key;
    const newSelectedRowKeys = selectedRowKeys.includes(key)
      ? selectedRowKeys.filter((k) => k !== key)
      : [...selectedRowKeys, key];
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const filteredColumns = columns.filter((column) => column !== null);

  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
    getCheckboxProps: (record) => ({
      disabled: !conditionActions(record),
    }),
    renderCell: (checked, record, index, originNode) => (
      <div className="relative">
        <Button
          style={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
          }}
          disabled={!conditionActions(record)}
          size="large"
          type="text"
          shape="circle"
          onClick={(e) => {
            e.stopPropagation();
            toggleSelection(record);
          }}
        >
          {originNode}
        </Button>
      </div>
    ),
  };
  const hasSelected = selectedRowKeys.length > 0;

  const DeleteMail = async () => {
    setLoading({ state: true, type: "delete" });
    const response = await TrashListMessages({
      usedAccount,
      id: EmailId,
      selectedRowKeys,
      setSelectedRowKeys,
      setOpenModal,
      folder: "inbox",
      typeDelete,
      setLoading,
      t,
      dispatch,
    });
    // if (response && debouncedSearchValue?.length > 0) getMailsSearchInbox();
    // else if (response && debouncedSearchValue?.length === 0)
    getMailsInbox();
  };

  useEffect(() => {
    let mounted = true;
    if (assignedType === 3 && assignedNames.length === 0) return;

    if (
      usedAccount &&
      Object.values(usedAccount)?.length > 0 &&
      mounted
      //  &&
      // debouncedSearchValue?.length === 0
    ) {
      getMailsInbox();
    }
    return () => {
      mounted = false;
    };
  }, [getMailsInbox]);

  //triggers when you search in the search bar
  // useEffect(() => {
  //   let mounted = true;

  //   if (
  //     usedAccount &&
  //     Object.values(usedAccount)?.length > 0 &&
  //     mounted &&
  //     debouncedSearchValue?.length > 0
  //   ) {
  //     getMailsSearchInbox();
  //   }
  // }, [getMailsSearchInbox]);

  useEffect(() => {
    getTags();

    return () => {
      dispatch(setFilterEmail(false));
    };
  }, []);

  //triggers when you click change in filter drawer
  // useEffect(() => {
  //   if (
  //     assignedType === 4 &&
  //     chrono === 0 &&
  //     assignedNames.length === 0 &&
  //     affectation.length === 0 &&
  //     dataQualifFilter.length === 0 &&
  //     selectedStatus.length === 0 &&
  //     !dateRange?.[0] &&
  //     !dateRange?.[1]
  //   ) {
  //     dispatch(setFilterEmailActive(false));
  //   }
  // }, [
  //   dispatch,
  //   assignedType,
  //   chrono,
  //   assignedNames,
  //   affectation,
  //   dataQualifFilter,
  //   selectedStatus,
  //   dateRange,
  // ]);

  const handleMarkAsReadUnread = async (status, selectedRowKeys) => {
    const response = await ReadUnreadMessages({
      usedAccount,
      status,
      selectedRowKeys,
      setSelectedRowKeys,
      t,
      dispatch,
    });
    // if (response && debouncedSearchValue?.length > 0) getMailsSearchInbox();
    // else if (response && debouncedSearchValue?.length === 0)
    getMailsInbox();
  };

  return (
    <div>
      <div className="mb-[5px] ml-[22px] mt-[5px] flex items-center">
        {dateRange[0] && dateRange[1] ? (
          <Tag color="#a149e9" className="max-w-[580px] overflow-y-scroll ">
            {`Date: ${dateRange[0]} ~ ${dateRange[1]}`}
            <CloseOutlined
              style={{ height: "10px", width: "10px" }}
              onClick={() => {
                setDateRange([null, null]);
                dispatch(setPage(1));
              }}
            />
          </Tag>
        ) : null}
        {assignedType === 1 || assignedType === 2 || assignedType === 5 ? (
          <Tag color="#f50" className="overflow-y-scroll ">
            {t("mailing.AssignedTo")}:{" "}
            {assignedType === 1
              ? t("mailing.all")
              : assignedType === 5
              ? t("tasks.aucun")
              : t("mailing.MyMode")}{" "}
            <CloseOutlined
              style={{ height: "10px", width: "10px" }}
              onClick={() => {
                setAssignedType(4);
                dispatch(setPage(1));
              }}
            />
          </Tag>
        ) : null}
        {assignedType === 3 && assignedNames?.length > 0 ? (
          <Tag color="#f50" className="max-w-[580px] overflow-y-scroll ">
            {t("mailing.AssignedTo")}:{" "}
            {assignedNames.map((item, i) => {
              return (
                <>
                  {item.searchOption}
                  {i < assignedNames?.length - 1 && ", "}
                </>
              );
            })}
            <CloseOutlined
              style={{ height: "10px", width: "10px" }}
              onClick={() => {
                setAssignedNames([]);
                dispatch(setPage(1));
              }}
            />
          </Tag>
        ) : null}
        {dataAssignedNames.length > 0 ? (
          <Tag color="#2db7f5" className="max-w-[580px] overflow-y-scroll ">
            {t("mailing.Affected")}:
            {dataAssignedNames.map((item, i) => {
              return (
                <>
                  {item.label}
                  {i < dataAssignedNames?.length - 1 && ", "}
                </>
              );
            })}
            <CloseOutlined
              style={{ height: "10px", width: "10px" }}
              onClick={() => {
                setAffectation([]);
                setDataAssignedNames([]);
                dispatch(setPage(1));
              }}
            />
          </Tag>
        ) : null}
        {selectedStatus.length > 0 ? (
          <Tag color="#3cca5d" className="max-w-[580px] overflow-y-scroll ">
            {t("helpDesk.status")}:
            {selectedStatus.map((item, i) => {
              return (
                <>
                  {item === "new"
                    ? t("mailing.new")
                    : item === "processed"
                    ? t("mailing.processed")
                    : item === "closed"
                    ? t("mailing.closed")
                    : t("mailing.inProgress")}
                  {i < selectedStatus?.length - 1 && ", "}
                </>
              );
            })}
            <CloseOutlined
              style={{ height: "10px", width: "10px" }}
              onClick={() => {
                setSelectedStatus([]);
                dispatch(setPage(1));
              }}
            />
          </Tag>
        ) : null}
        {chrono !== 0 ? (
          <Tag color="#87d068">
            {chrono === 1 ? t("mailing.chronoYes") : t("mailing.outOfDeadline")}{" "}
            <CloseOutlined
              style={{ height: "10px", width: "10px" }}
              onClick={() => {
                setChrono(0);
                dispatch(setPage(1));
              }}
            />
          </Tag>
        ) : null}

        {dataQualifFilter.length > 0 ? (
          <Tag color="#ff7316" className="max-w-[580px] overflow-y-scroll ">
            {t("mailing.qualifiedTo")}:
            {dataQualifFilter.map((item, i) => {
              return (
                <>
                  {item.label}
                  {i < dataQualifFilter?.length - 1 && ", "}
                </>
              );
            })}
            <CloseOutlined
              style={{ height: "10px", width: "10px" }}
              onClick={() => {
                setDataQualifFilter([]);
                dispatch(setPage(1));
              }}
            />
          </Tag>
        ) : null}
      </div>

      {hasSelected ? (
        <div className="mb-[8px] ml-[20px] flex items-center space-x-3">
          <Space>
            <Button
              type="primary"
              danger
              icon={<DeleteOutlined />}
              onClick={() => {
                setOpenModal(true);
                setTypeDelete("multiple");
              }}
            >
              {t("mailing.DeleteButton")} ({selectedRowKeys.length}{" "}
              {selectedRowKeys.length > 1 ? "emails" : "email"})
            </Button>
          </Space>
          {usedAccount?.shared == 0 ? (
            <>
              <Button
                type="default"
                onClick={() => handleMarkAsReadUnread(1, selectedRowKeys)}
              >
                {t("mailing.markRead")} ({selectedRowKeys.length})
              </Button>

              <Button
                type="default"
                onClick={() => handleMarkAsReadUnread(0, selectedRowKeys)}
              >
                {t("mailing.markUnread")} ({selectedRowKeys.length})
              </Button>
            </>
          ) : null}
        </div>
      ) : null}

      <FormCreate
        open={openForm}
        setOpen={setOpenForm}
        familyId={mailingProps?.familyId}
        // mailingProps={mailingProps}
        externalSource={{
          source: "email",
          usedAccount: usedAccount,
          ...mailingProps,
        }}
      />

      <FormUpdate
        open={openFormUpdate}
        setOpen={setOpenFormUpdate}
        familyId={familyId}
        elementDetails={mailingProps}
      />

      <CreateTask
        open={openTask}
        setOpen={setOpenTask}
        mask={false}
        source="mailing"
        taskId={taskId}
        setTaskId={setTaskId}
        mailingProps={mailingProps}
      />

      {openLogDrawer !== null ? (
        <Log
          openLogDrawer={openLogDrawer}
          setOpenLogDrawer={setOpenLogDrawer}
          setLoading={setLoading}
          thirdid={thirdid}
        />
      ) : null}

      {/* {filterEmail ? ( */}
      <FilterDrawer
        assignedType={assignedType}
        chrono={chrono}
        dataAssignedFilter={dataAssignedFilter}
        assignedNames={assignedNames}
        setAssignedType={setAssignedType}
        setChrono={setChrono}
        setDataAssignedFilter={setDataAssignedFilter}
        affectation={affectation}
        setAffectation={setAffectation}
        setAssignedNames={setAssignedNames}
        setDataAssignedNames={setDataAssignedNames}
        selectedFamily={selectedFamily}
        setSelectedFamily={setSelectedFamily}
        dataQualifFilter={dataQualifFilter}
        setDataQualifFilter={setDataQualifFilter}
        dataTags={dataTags}
        dateRange={dateRange}
        setDateRange={setDateRange}
        selectedStatus={selectedStatus}
        setSelectedStatus={setSelectedStatus}
      />
      {/* ) : null} */}

      <ModalDeleteEmail
        t={t}
        openModal={openModal}
        setOpenModal={setOpenModal}
        loading={loading}
        DeleteMail={DeleteMail}
        typeDelete={typeDelete}
        selectedRowKeysLength={selectedRowKeys.length}
      />

      <Table
        className="mailing-custom-row"
        loading={loading.state && loading.type === "mails"}
        rowSelection={rowSelection}
        columns={filteredColumns}
        dataSource={dataSourceInbox}
        pagination={{
          current: page,
          pageSize: pageSize,
          pageSizeOptions: ["10", "20", "30"],
          total: metaMailInbox?.total === 0 ? 1 : metaMailInbox.total,
          showSizeChanger: true,
          onChange: (page, pageSize) => {
            // if (debouncedSearchValue?.length > 0) {
            //   dispatch(setSearchPage(page));
            // } else {
            dispatch(setPage(page));
            // }

            dispatch(setPageSize(pageSize));
          },
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total}`,
        }}
        scroll={{
          x: "max-content",
          y: windowSize?.height - 240,
        }}
        onRow={(record) => {
          return {
            onClick: (e) => {
              const target = e.target.closest(".ant-checkbox-wrapper");
              if (!target) {
                setDetailsMail([]);
                dispatch(setNumberEmailThread(record.nbr));
                navigate(`/mailing/${usedAccount?.value}/inbox/${record.key}`);
              }
            },
          };
        }}
        rowClassName={(record, index) =>
          `group ${record.seen ? "read-row" : "unread-row"}`
        }
        expandable={
          !!debouncedSearchValue
            ? {
                expandedRowKeys: expandedRows,
                expandedRowRender: (record) =>
                  !!debouncedSearchValue ? (
                    <p className="m-0 ml-[10%]">
                      {renderHighlight(record.highlight, t, "email")}
                    </p>
                  ) : null,
                rowExpandable: (record) => !!record.highlight,
                showExpandColumn: false,
              }
            : {}
        }
        size="small"
        style={{
          minHeight:
            loading.state && loading.type === "mails" ? "700px" : "auto",
        }}
      />
    </div>
  );
};

export default Inbox;
