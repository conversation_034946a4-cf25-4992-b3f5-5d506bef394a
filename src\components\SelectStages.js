import { Badge, Select, message } from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { generateAxios } from "../services/axiosInstance";
import { CaretDownOutlined } from "@ant-design/icons";
import MainService from "../services/main.service";
import { useLocation, useParams } from "react-router-dom";
import { setDetailsMeet } from "../new-redux/actions/visio.actions/visio";
import { useDispatch } from "react-redux";
import { URL_ENV } from "index";
import { useSelector } from "react-redux";

const SelectStages = ({
  record,
  stages,
  setPipeline,
  setList = () => {},
  setCountTasks = () => {},
  listFilter,
  source = "",
  size = "default",
}) => {
  const [loading, setLoading] = useState(false);
  const [selectedStage, setSelectedStage] = useState("");

  const [messageApi, contextHolder] = message.useMessage();
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const location = useLocation();
  const { contactHeaderInfo: contactInfo } = useSelector(
    (state) => state?.contacts
  );
  const { contactInfoFromDrawer, openView360InDrawer } = useSelector(
    (state) => state?.vue360
  );

  const getIdByLabel = (data, label) => {
    for (const { stages } of data) {
      for (const { id, label: stageLabel } of stages) {
        if (stageLabel === label) {
          return id;
        }
      }
    }
    return null;
  };
  const success = () => {
    messageApi.open({
      type: "success",
      content: t("toasts.stageChanged"),
    });
  };
  const error = () => {
    messageApi.open({
      type: "error",
      content: t("toasts.actionFailed"),
    });
  };
  const changeStage = async (task_id, new_stage_id, record) => {
    setLoading(true);
    try {
      const res = await MainService.updateTaskStageInKanban({
        "task_id[]": task_id,
        new_stage_id,
      });
      if (location.pathname === "/dashboard") {
        setList(res.data.data);
      }
      if (source === "vue360") {
        const count = await MainService.getTasks360Count({
          id: openView360InDrawer ? contactInfoFromDrawer?.id : contactInfo?.id,
          types:
            listFilter?.selected?.length > 0
              ? listFilter?.selected.join(",")
              : "",
        });

        setCountTasks(count?.data);
      }
      if (source === "listVisio") {
        dispatch(setDetailsMeet(res.data.data));
      } else
        setList((prev) =>
          prev.map((el) =>
            el.id === task_id
              ? { ...el, stage: new_stage_id, stage_id: new_stage_id }
              : el
          )
        );
      setSelectedStage(new_stage_id);
      setPipeline(record.pipeline_label);
      setLoading(false);

      success();
    } catch (e) {
      console.log(e);
      setLoading(false);
      error();
    }
  };

  return (
    <>
      {contextHolder}

      <Select
        placeholder={t("fields_management.groupStagePlaceholder")}
        size={size}
        value={
          record?.stage_id ||
          selectedStage ||
          getIdByLabel(stages, record.stage_label)
        }
        loading={loading}
        bordered={true}
        dropdownStyle={{ width: "200px" }}
        onChange={(value) => changeStage(record.id, value, record)}
        optionLabelProp="label2"
        options={stages.map((el) => ({
          label: <div>{el.label}</div>,
          options: el.stages.map((st) => ({
            label: (
              <div className="space-x-1">
                {st.color ? (
                  <Badge color={st.color} text={st.label} />
                ) : (
                  st.label
                )}
              </div>
            ),
            label2: (
              <div className="space-x-1">
                {st.color ? (
                  <span>
                    {el.label} / <Badge color={st.color} text={st.label} />
                  </span>
                ) : (
                  el.label + " / " + st.label
                )}
              </div>
            ),
            value: st.id,
          })),
        }))}
      />
    </>
  );
};

export default SelectStages;
