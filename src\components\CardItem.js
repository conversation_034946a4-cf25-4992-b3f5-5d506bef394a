import React, { useState, useEffect, lazy } from "react";
import { useSelector, useDispatch } from "react-redux";

import { getTypes } from "../new-redux/actions/types.actions/getTypes";
import "./style.css";

const DraggableTable = lazy(() => import("../pages/table/DraggableTable"));

/**
 *
 * @param {*} param0
 * @returns Fields management table with search and filter tools.
 */

const CardItem = ({
  setAddNewFieldModal,
  setTitleConfig,
  setUpdateFieldProps,
  loadFields,
  fieldsArray,
  familyId,
}) => {
  const [fieldToUpdate, setFieldToUpdate] = useState(null);
  const [visibility, setVisibility] = useState(null);
  const [requiredField, setRequiredField] = useState(null);
  const [uniqueValue, setUniqueValue] = useState(null);

  const dispatch = useDispatch();
  const { types } = useSelector((state) => state.types);

  useEffect(() => {
    if (types === null) {
      dispatch(getTypes());
    }
  }, [dispatch, types]);

  return (
    <DraggableTable
      setVisibility={setVisibility}
      setRequiredField={setRequiredField}
      setUniqueValue={setUniqueValue}
      setAddNewFieldModal={setAddNewFieldModal}
      setTitleConfig={setTitleConfig}
      setUpdateFieldProps={setUpdateFieldProps}
      setFieldToUpdate={setFieldToUpdate}
      loadFields={loadFields}
      fieldsArray={fieldsArray}
      familyId={familyId}
    />
  );
};

export default CardItem;
