import { Modal } from "antd";
import React, { useCallback, useEffect, useState } from "react";
import { Button, Form, Input, Select, Space } from "antd";
import MainService from "../services/main.service";
import { toastNotification } from "./ToastNotification";
import { useTranslation } from "react-i18next";

const ModalActionTicket = ({
  isModalOpen,
  setIsModalOpen,
  type,
  detailsElement,
  ticketIds,
  setShouldFetchData,
}) => {
  const [dataLevels, setDataLevels] = useState([]);
  const [dataTickets, setDataTickets] = useState([]);
  const [loadingLevels, setLoadingLevels] = useState(false);
  const [loadingButton, setLoadingButton] = useState({
    type: "",
    state: false,
  });
  const [dataUsers, setDataUsers] = useState([]);
  const [dataFolders, setDataFolders] = useState([]);

  const [form] = Form.useForm();
  const [formMerge] = Form.useForm();
  const [formMove] = Form.useForm();
  const [t] = useTranslation("common");
  const getLevels = useCallback(async () => {
    if (!isModalOpen) return;
    setLoadingLevels(true);
    try {
      let response = await MainService.getLevels();

      if (response?.status === 200) {
        setLoadingLevels(false);
        setDataLevels(response.data.data.filter((item) => item.status === 1));
      }
    } catch (err) {
      console.log(err);
      setLoadingLevels(false);
    }
  }, [isModalOpen]);

  const getTickets = useCallback(async () => {
    var formData = new FormData();
    for (let i = 0; i < ticketIds.length; i++) {
      formData.append("tickets_id[]", ticketIds[i]);
    }

    try {
      let response = await MainService.getTicketLabel(formData);

      if (response?.status === 200) {
        setDataTickets(
          response.data.map((item) => ({
            value: item[1],
            label: item[0] + "-" + item[2],
          }))
        );
      }
    } catch (err) {
      console.log(err);
    }
  }, [isModalOpen, ticketIds]);

  const getFolders = useCallback(async () => {
    if (!isModalOpen) return;
    setLoadingLevels(true);
    try {
      let response = await MainService.getFoldersTicketFilter();

      if (response?.status === 200) {
        setDataFolders(
          response.data.data
            .filter((item) => item.status === 1 && item.hidden === 0)
            .map((item) => ({
              label: item.label,
              value: item.id,
            }))
        );
      }
    } catch (err) {
      console.log(err);
      setLoadingLevels(false);
    }
  }, [isModalOpen]);

  const dataSelectLevels = dataLevels?.map((item, i) => ({
    value: item.id,
    label: item.label,
  }));

  const dataSelectUsers = (e) => {
    setDataUsers(
      dataLevels
        ?.filter((item) => item.id === e)[0]
        .users?.map((item, i) => ({
          value: item.id,
          label: item.label,
        }))
    );
  };

  const TransferAPI = async (values) => {
    setLoadingButton({ type: "transfer", state: true });
    var formData = new FormData();
    formData.append("traiteur", values.recipient);
    formData.append("transfer_level", values.sender);
    formData.append("transfer_solution", values.raison ? values.raison : "");
    formData.append(
      "transfer_reason",
      values.intervention ? values.intervention : ""
    );

    try {
      const response = await MainService.TransferTicket(
        formData,
        detailsElement.id
      );
      if (response.status === 200) {
        setLoadingButton({ type: "transfer", state: false });
        setIsModalOpen(false);
        toastNotification(
          "success",
          "Transférer avec succés",
          "bottomRight",
          3
        );
        setShouldFetchData(true);
        form.setFieldsValue({
          sender: "",
          recipient: "",
          raison: "",
          intervention: "",
        });
      }
    } catch (error) {
      setLoadingButton({ type: "transfer", state: false });
      if (error.response.status === 422) {
        toastNotification(
          "error",
          error?.response?.data
            ? error?.response?.data?.message
            : t("helpDesk.activeTemplateEmail"),
          "topRight",
          4
        );
      } else
        toastNotification(
          "error",
          error?.response?.data
            ? error?.response?.data?.message
            : t("toasts.errorFetchApi"),
          "topRight",
          4
        );

      console.log("err", error);
    }
  };

  const TransferBlockAPI = async (values) => {
    setLoadingButton({ type: "transfer", state: true });
    var formData = new FormData();
    formData.append("traiteur", values.recipient);
    formData.append("transfer_level", values.sender);
    formData.append("transfer_solution", values.raison ? values.raison : "");
    formData.append(
      "transfer_reason",
      values.intervention ? values.intervention : ""
    );
    for (let i = 0; i < ticketIds.length; i++) {
      formData.append("tickets_id[]", ticketIds[i]);
    }

    try {
      const response = await MainService.TransferBlockTickets(formData);
      if (response.status === 200) {
        setLoadingButton({ type: "transfer", state: false });
        setIsModalOpen(false);
        toastNotification(
          "success",
          "Transférer avec succés",
          "bottomRight",
          3
        );
        setShouldFetchData(true);
        form.setFieldsValue({
          sender: "",
          recipient: "",
          raison: "",
          intervention: "",
        });
      }
    } catch (error) {
      setLoadingButton({ type: "transfer", state: false });
      console.log(error);
      if (error.response.status === 422) {
        toastNotification(
          "error",
          error?.response?.data
            ? error?.response?.data?.message
            : t("helpDesk.activeTemplateEmail"),
          "topRight",
          4
        );
      } else
        toastNotification(
          "error",
          error?.response?.data
            ? error?.response?.data?.message
            : t("toasts.errorFetchApi"),
          "topRight",
          4
        );

      console.log("err", error);
    }
  };

  const MergeTickets = async (values) => {
    setLoadingButton({ type: "merge", state: true });
    var formData = new FormData();

    formData.append("merge_id", values.ticket);
    for (let i = 0; i < ticketIds.length; i++) {
      formData.append("ticket_id[]", ticketIds[i]);
    }

    try {
      const response = await MainService.mergeTickets(formData);
      if (response.status === 200) {
        setLoadingButton({ type: "merge", state: false });
        setIsModalOpen(false);
        toastNotification("success", "Fusionné avec succés", "bottomRight", 3);
        setShouldFetchData(true);
        form.setFieldsValue({
          ticket: "",
        });
      }
    } catch (error) {
      setLoadingButton({ type: "merge", state: false });
      toastNotification(
        "error",
        error?.response?.data
          ? error?.response?.data?.message
          : t("toasts.errorFetchApi"),
        "topRight",
        4
      );

      console.log("err", error);
    }
  };

  const AssignTicket = async () => {
    setLoadingButton({ type: "assign", state: true });
    try {
      const response = await MainService.AssignTicket(detailsElement.id);
      if (response.status === 200) {
        setLoadingButton({ type: "assign", state: false });
        setIsModalOpen(false);
        toastNotification("success", "Assigné avec succés", "bottomRight", 3);
        setShouldFetchData(true);
      }
    } catch (error) {
      setLoadingButton({ type: "assign", state: false });
      if (error.response.status === 422) {
        toastNotification(
          "error",
          error?.response?.data
            ? error?.response?.data?.message
            : t("helpDesk.activeTemplateEmail"),
          "topRight",
          4
        );
      } else
        toastNotification(
          "error",
          error?.response?.data
            ? error?.response?.data?.message
            : t("toasts.errorFetchApi"),
          "topRight",
          4
        );
      console.log("err", error);
    }
  };

  // console.log("ticketIds", ticketIds);

  const RemettreTicket = async () => {
    setLoadingButton({ type: "remettre", state: true });
    var formData = new FormData();
    if (ticketIds.length > 0) {
      for (let i = 0; i < ticketIds.length; i++) {
        formData.append("tickets_id[]", ticketIds[i]);
      }
    } else {
      formData.append("tickets_id[]", detailsElement.id);
    }

    try {
      const response = await MainService.RemettreTicket(formData);
      if (response.status === 200) {
        setLoadingButton({ type: "remettre", state: false });
        setIsModalOpen(false);
        toastNotification("success", "Remis avec succés", "topRight", 3);
      }
    } catch (error) {
      setLoadingButton({ type: "remettre", state: false });
      toastNotification(
        "error",
        error?.response?.data
          ? error?.response?.data?.message
          : t("toasts.errorFetchApi"),
        "topRight",
        4
      );
      console.log("err", error);
    }
  };

  const LaunchTicket = async () => {
    setLoadingButton({ type: "launch", state: true });
    try {
      const response = await MainService.LaunchTicket(detailsElement.id);
      if (response.status === 200) {
        setLoadingButton({ type: "launch", state: false });
        setIsModalOpen(false);
        toastNotification("success", "Relancé avec succés", "topRight", 3);
        setShouldFetchData(true);
      }
    } catch (error) {
      setLoadingButton({ type: "launch", state: false });
      if (error.response.status === 422) {
        toastNotification(
          "error",
          error?.response?.data
            ? error?.response?.data?.message
            : t("helpDesk.activeTemplateEmail"),
          "topRight",
          4
        );
      } else
        toastNotification(
          "error",
          error?.response?.data
            ? error?.response?.data?.message
            : t("toasts.errorFetchApi"),
          "topRight",
          4
        );
      console.log("err", error);
    }
  };

  const MoveTickets = async (values) => {
    setLoadingButton({ type: "move", state: true });
    var formData = new FormData();

    formData.append("folder_id", values.folder);
    for (let i = 0; i < ticketIds.length; i++) {
      formData.append(
        "tickets_id[]",
        type === "deplacer" ? ticketIds[i] : detailsElement.id
      );
    }
    if (type === "deplacerByOne") {
      formData.append("tickets_id[]", detailsElement.id);
    }

    try {
      const response = await MainService.moveTickets(formData);
      if (response.status === 200) {
        setLoadingButton({ type: "move", state: false });
        setIsModalOpen(false);
        toastNotification(
          "success",
          "Transférer avec succés",
          "bottomRight",
          3
        );
        form.setFieldsValue({
          folder: "",
        });
        setShouldFetchData(true);
      }
    } catch (error) {
      setLoadingButton({ type: "move", state: false });
      toastNotification(
        "error",
        error?.response?.data
          ? error?.response?.data?.message
          : t("toasts.errorFetchApi"),
        "topRight",
        4
      );

      console.log("err", error);
    }
  };

  const onFinish = (values) => {
    if (type === "transfer") TransferAPI(values);
    else {
      TransferBlockAPI(values);
    }
  };

  const onFinishMerge = (values) => {
    MergeTickets(values);
  };

  const onFinishMove = (values) => {
    MoveTickets(values);
  };

  useEffect(() => {
    if (isModalOpen && (type === "transfer" || type === "transfer_multiple")) {
      getLevels();
    }
  }, [getLevels]);

  useEffect(() => {
    if (isModalOpen && type === "fusionner") {
      getTickets();
    }
  }, [getTickets]);

  useEffect(() => {
    if (isModalOpen && (type === "deplacer" || type === "deplacerByOne")) {
      getFolders();
    }
  }, [getFolders]);

  return (
    <div>
      <Modal
        title={
          type === "transfer" ? (
            <p className="uppercase ">
              {t("helpDesk.transfer")}{" "}
              {t("helpDesk.ticket", {
                plural: ticketIds.length > 1 ? "s" : "",
                single: ticketIds.length < 2 ? " the " : "",
              })}{" "}
              {ticketIds.length < 2 ? ' "' + detailsElement?.label + '"' : ""}
            </p>
          ) : type === "fusionner" ? (
            <p className="uppercase ">
              {t("helpDesk.merge")}{" "}
              {t("helpDesk.ticket", {
                plural: ticketIds.length > 1 ? "s" : "",
                single: ticketIds.length < 2 ? " the " : "",
              })}{" "}
              {ticketIds.length < 2 ? ' "' + detailsElement?.label + '"' : ""}
            </p>
          ) : type === "transfer_multiple" ? (
            <p className="uppercase ">{t("helpDesk.transfertTicketBlock")}</p>
          ) : type === "assign" ? (
            <p className="uppercase ">
              {t("helpDesk.assign")}{" "}
              {t("helpDesk.ticket", {
                plural: ticketIds.length > 1 ? "s" : "",
                single: ticketIds.length < 2 ? " the " : "",
              })}{" "}
              {ticketIds.length < 2 ? ' "' + detailsElement?.label + '"' : ""}
            </p>
          ) : type === "launch" ? (
            <p className="uppercase ">
              {t("helpDesk.relaunch")}{" "}
              {t("helpDesk.ticket", {
                plural: ticketIds.length > 1 ? "s" : "",
                single: ticketIds.length < 2 ? " the " : "",
              })}{" "}
              {ticketIds.length < 2 ? ' "' + detailsElement?.label + '"' : ""}
            </p>
          ) : type === "deplacer" || type === "deplacerByOne" ? (
            <p className="uppercase ">
              {t("helpDesk.move")}{" "}
              {t("helpDesk.ticket", {
                plural: ticketIds.length > 1 ? "s" : "",
                single: ticketIds.length < 2 ? " the " : "",
              })}{" "}
              {ticketIds.length < 2 ? ' "' + detailsElement?.label + '"' : ""}
            </p>
          ) : type === "remettre" ? (
            <p className="uppercase ">
              {t("helpDesk.handOver")}{" "}
              {t("helpDesk.ticket", {
                plural: ticketIds.length > 1 ? "s" : "",
                single: ticketIds.length < 2 ? " the " : "",
              })}{" "}
              {ticketIds.length < 2 ? ' "' + detailsElement?.label + '"' : ""}
            </p>
          ) : null
        }
        open={isModalOpen}
        onOk={(value) => {
          setIsModalOpen(false);
        }}
        onCancel={() => setIsModalOpen(false)}
        footer={null}
      >
        {type === "transfer" || type === "transfer_multiple" ? (
          <>
            <Form
              form={form}
              name="control-hooks"
              onFinish={onFinish}
              layout="vertical"
            >
              <Form.Item
                name="sender"
                label={t("helpDesk.listLevels")}
                rules={[
                  {
                    required: true,
                  },
                ]}
              >
                <Select
                  loading={loadingLevels}
                  onChange={(e) => dataSelectUsers(e)}
                  showSearch
                  placeholder="Select level"
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    (option?.label ?? "").includes(input)
                  }
                  options={dataSelectLevels}
                />
              </Form.Item>
              <Form.Item
                name="recipient"
                label={t("helpDesk.cateringAgent")}
                rules={[
                  {
                    required: true,
                  },
                ]}
              >
                <Select
                  showSearch
                  placeholder="Search agent"
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    (option?.label ?? "").includes(input)
                  }
                  options={dataUsers}
                />
              </Form.Item>
              <Form.Item name="raison" label={t("helpDesk.reasonTransfer")}>
                <Input.TextArea rows={3} />
              </Form.Item>
              <Form.Item
                name="intervention"
                label={t("helpDesk.InterventionCarriedOut")}
              >
                <Input.TextArea rows={3} />
              </Form.Item>
              <Form.Item>
                <Space style={{ display: "flex", justifyContent: "flex-end" }}>
                  <Button
                    htmlType="button"
                    onClick={() => {
                      setIsModalOpen(false);
                      form.setFieldsValue({
                        sender: "",
                        recipient: "",
                        raison: "",
                        intervention: "",
                      });
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    loading={
                      loadingButton.type === "transfer" && loadingButton.state
                    }
                    type="primary"
                    htmlType="submit"
                  >
                    Submit
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </>
        ) : type === "fusionner" ? (
          <Form
            form={formMerge}
            name="control-hooks"
            onFinish={onFinishMerge}
            layout="vertical"
          >
            <Form.Item
              name="ticket"
              label="ticket"
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <Select
                showSearch
                placeholder="Select ticket"
                optionFilterProp="children"
                filterOption={(input, option) =>
                  (option?.label ?? "").includes(input)
                }
                options={dataTickets}
              />
            </Form.Item>
            <Form.Item>
              <Space style={{ display: "flex", justifyContent: "flex-end" }}>
                <Button
                  htmlType="button"
                  onClick={() => {
                    setIsModalOpen(false);
                    form.setFieldsValue({
                      ticket: "",
                    });
                  }}
                >
                  Cancel
                </Button>
                <Button
                  loading={
                    loadingButton.type === "merge" && loadingButton.state
                  }
                  type="primary"
                  htmlType="submit"
                >
                  Submit
                </Button>
              </Space>
            </Form.Item>
          </Form>
        ) : type === "assign" ? (
          <>
            <p>{t("helpDesk.coverTicket")}</p>
            <Space style={{ display: "flex", justifyContent: "flex-end" }}>
              <Button
                htmlType="button"
                onClick={() => {
                  setIsModalOpen(false);
                }}
              >
                Cancel
              </Button>
              <Button
                loading={loadingButton.type === "assign" && loadingButton.state}
                type="primary"
                htmlType="submit"
                onClick={() => AssignTicket()}
              >
                Submit
              </Button>
            </Space>
          </>
        ) : type === "launch" ? (
          <>
            <p>{t("helpDesk.SureRelaunchTicket")}</p>
            <Space style={{ display: "flex", justifyContent: "flex-end" }}>
              <Button
                htmlType="button"
                onClick={() => {
                  setIsModalOpen(false);
                }}
              >
                Cancel
              </Button>
              <Button
                loading={loadingButton.type === "launch" && loadingButton.state}
                type="primary"
                htmlType="submit"
                onClick={() => LaunchTicket()}
              >
                Submit
              </Button>
            </Space>
          </>
        ) : type === "deplacerByOne" || type === "deplacer" ? (
          <Form
            form={formMove}
            name="control-hooks"
            onFinish={onFinishMove}
            layout="vertical"
          >
            <Form.Item
              name="folder"
              label="Folder"
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <Select
                showSearch
                placeholder="Select folder"
                optionFilterProp="children"
                filterOption={(input, option) =>
                  (option?.label ?? "").includes(input)
                }
                options={dataFolders}
              />
            </Form.Item>
            <Form.Item>
              <Space style={{ display: "flex", justifyContent: "flex-end" }}>
                <Button
                  htmlType="button"
                  onClick={() => {
                    setIsModalOpen(false);
                    formMove.setFieldsValue({
                      folder: null,
                    });
                  }}
                >
                  Cancel
                </Button>
                <Button
                  loading={loadingButton.type === "move" && loadingButton.state}
                  type="primary"
                  htmlType="submit"
                >
                  Submit
                </Button>
              </Space>
            </Form.Item>
          </Form>
        ) : type === "remettre" ? (
          <>
            <p>{t("helpDesk.sureHandTicket")}</p>
            <Space style={{ display: "flex", justifyContent: "flex-end" }}>
              <Button
                htmlType="button"
                onClick={() => {
                  setIsModalOpen(false);
                }}
              >
                Cancel
              </Button>
              <Button
                loading={
                  loadingButton.type === "remettre" && loadingButton.state
                }
                type="primary"
                htmlType="submit"
                onClick={() => RemettreTicket()}
              >
                Submit
              </Button>
            </Space>
          </>
        ) : null}
      </Modal>
    </div>
  );
};

export default ModalActionTicket;
