/**
 * @name <PERSON><PERSON><PERSON>er
 *
 * @description `Field<PERSON>enderer` component is responsible for rendering field label and its value on kanban card.
 *
 * @returns {JSX.Element} field's label and value.
 */

import { useMemo, useState } from "react";
import {
  Badge,
  Button,
  Card,
  Image,
  Popover,
  Rate,
  Tag,
  Typography,
} from "antd";
import { useTranslation } from "react-i18next";
import {
  CalendarOutlined,
  DownloadOutlined,
  FieldTimeOutlined,
  PaperClipOutlined,
} from "@ant-design/icons";

import { AvatarChat } from "components/Chat";
import ActionsComponent from "pages/tasks/ActionsComponent";
import { URL_ENV } from "index";
import { EXTENSIONS_ARRAY } from "pages/tasks/helpers/calculateSum";
import { getName } from "pages/layouts/chat/utils/ConversationUtils";
import AvatarGroup from "components/AvatarGroup";

//Load failure fault-tolerant src. Refer to https://ant.design/components/image#image
const fallbackSrc =
  "data:image/png;base64,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";

const FieldRenderer = ({ item, index }) => {
  const [downloadProgress, setDownloadProgress] = useState(false);
  const [keyOfFile, setKeyOfFile] = useState(null);

  const [t] = useTranslation("common");

  const fieldType = item?.field_type;
  const value = item[Object.keys(item)[0]];
  const hasFamilyId = item?.family_id;

  //Handle download attachment.
  const handleDownloadFile = (e, file) => {
    if (e) e.preventDefault();
    setDownloadProgress(true);
    if (file) {
      fetch(
        URL_ENV?.REACT_APP_BASE_URL +
          URL_ENV.REACT_APP_SUFFIX_AVATAR_URL +
          file?.path
      )
        .then((response) => response.blob())
        .then((blob) => {
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement("a");
          a.style.display = "none";
          a.href = url;
          a.download = file?.name;
          document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(url);
        })
        .catch((error) => {
          console.log({ error });
        })
        .finally(() => {
          setDownloadProgress(false);
          setKeyOfFile(null);
        });
    }
  };

  //Display attachments in a popover if there is any.
  const AttachmentsList = ({ item }) => {
    return (
      <div className="flex flex-col justify-between gap-2">
        {item?.content?.map((el) => {
          return (
            <Card key={el?.path}>
              <div className="flex flex-row justify-between">
                <div className="flex flex-col">
                  <div className="flex flex-row items-center hover:cursor-pointer">
                    <PaperClipOutlined
                      style={{ fontSize: "9px", marginRight: "4px" }}
                    />
                    <Typography.Text
                      onClick={() =>
                        window.open(
                          `${URL_ENV.REACT_APP_BASE_URL}${URL_ENV.REACT_APP_SUFFIX_AVATAR_URL}${el?.path}`,
                          "_blank",
                          "noopener,noreferrer"
                        )
                      }
                      ellipsis={{
                        tooltip: true,
                      }}
                      className="w-80 whitespace-nowrap text-[16px] text-xs text-[#1677ff] hover:text-[#69b1ff]"
                    >
                      {el?.name}
                    </Typography.Text>
                  </div>
                  <div className="flex flex-row items-center">
                    <Tag icon={<CalendarOutlined />} bordered={false}>
                      {el?.date}
                    </Tag>
                    <Tag bordered={false}>
                      {el?.size} {t("tasks.fileSize")}
                    </Tag>
                  </div>
                </div>
                <Button
                  icon={<DownloadOutlined />}
                  shape="circle"
                  size="small"
                  type="text"
                  onClick={(e) => {
                    setKeyOfFile(el?.path);
                    handleDownloadFile(e, el);
                  }}
                  loading={keyOfFile === el?.path && downloadProgress}
                />
              </div>
            </Card>
          );
        })}
      </div>
    );
  };

  // Render field value based on its type.
  const renderFieldContent = useMemo(() => {
    //Render select field value
    const renderSelectValue = (value) => {
      if (Array.isArray(value)) {
        return value?.map((el) => (
          <Tag
            bordered={false}
            color="volcano"
            className="max-w-[100px] overflow-hidden text-ellipsis whitespace-nowrap"
            key={el}
          >
            {el}
          </Tag>
        ));
      } else {
        return (
          <Tag bordered={false} color="volcano">
            {value}
          </Tag>
        );
      }
    };

    //Render avatars in from select field.
    const renderFamilyIdValue = (value) => {
      if (!Array.isArray(value)) {
        return (
          <ActionsComponent elementValue={value}>
            <AvatarChat
              size="small"
              fontSize="0.7rem"
              className="flex items-center justify-center"
              height="32px"
              width="32px"
              url={`${URL_ENV?.REACT_APP_BASE_URL}${URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL}${value?.avatar}`}
              hasImage={EXTENSIONS_ARRAY?.includes(
                value?.avatar?.split(".")?.at(-1)
              )}
              name={getName(value?.label, "avatar")}
              type="user"
            />
          </ActionsComponent>
        );
      } else {
        return (
          <AvatarGroup
            source="elementKanban"
            usersArray={value}
            uncheckUser={() => {}}
            disableDelete
            roleInActivity={null}
          />
        );
      }
    };

    //Render file field value
    const renderFileField = (item) => (
      <Popover
        content={<AttachmentsList key={item?.id} item={item} />}
        title={t("mailing.Attachments")}
        trigger={["click"]}
      >
        <Button
          icon={
            <>
              <PaperClipOutlined />
              <span>{item?.content?.length}</span>
            </>
          }
          type="text"
          size="small"
          shape="circle"
        />
      </Popover>
    );

    //Render album field value
    const renderAlbumField = (item) => (
      <div>
        <Image.PreviewGroup
          items={item?.content?.map((el) => ({
            src: `${URL_ENV.REACT_APP_BASE_URL}${URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL}${el?.path}`,
          }))}
        >
          {item?.content?.slice(0, 2)?.map((el, index) => (
            <Image
              key={`image_${index}`}
              width={25}
              height={25}
              style={{ borderRadius: "50%" }}
              fallback={fallbackSrc}
              src={`${URL_ENV.REACT_APP_BASE_URL}${URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL}${el?.path}`}
              placeholder={true}
            />
          ))}
          {item?.content.length - 2 > 0 && (
            <Badge count={`+${item?.content?.length - 2}`} size="small">
              <Image
                width={25}
                height={25}
                src={`${URL_ENV.REACT_APP_BASE_URL}${URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL}${item?.content[2]?.path}`}
                style={{ opacity: 0.5, borderRadius: "50%" }}
                placeholder={true}
              />
            </Badge>
          )}
        </Image.PreviewGroup>
      </div>
    );

    //Differentiate between multiselects values.
    // If has family id -> display avatars, if not -> show values in tags.
    if (
      fieldType === "select" ||
      fieldType === "multiselect" ||
      fieldType === "checkbox" ||
      fieldType === "radio"
    ) {
      return hasFamilyId
        ? renderFamilyIdValue(value)
        : renderSelectValue(value);
    }

    const typographyStyles = {
      maxWidth: 170,
      whiteSpace: "nowrap",
      fontSize: "12px",
    };

    //JSX renderer
    switch (fieldType) {
      case "text":
        return (
          <Typography.Text
            ellipsis={{ tooltip: true }}
            style={typographyStyles}
            strong
          >
            {value}
          </Typography.Text>
        );
      case "image":
        return (
          <Image
            width={25}
            height={25}
            style={{ borderRadius: "50%" }}
            fallback={fallbackSrc}
            src={`${URL_ENV.REACT_APP_BASE_URL}${URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL}${item?.content?.path}`}
          />
        );
      case "file":
        return renderFileField(item);
      case "album":
        return renderAlbumField(item);
      case "link":
        return (
          <Typography.Link
            onClick={() => window.open(item?.["Web Site"], "_blank")}
            style={typographyStyles}
            strong
          >
            {item?.["Web Site"]}
          </Typography.Link>
        );
      case "textarea":
        return (
          <Typography.Text
            ellipsis={{
              tooltip: {
                overlayInnerStyle: { maxHeight: 300, overflow: "auto" },
              },
            }}
            style={typographyStyles}
            strong
          >
            {value}
          </Typography.Text>
        );
      case "rate":
        return (
          <Rate value={Number(value)} disabled style={{ fontSize: "12px" }} />
        );
      case "monetary":
        return (
          <Typography.Text
            ellipsis={{ tooltip: true }}
            style={typographyStyles}
            strong
          >{`(${value[0]}) ${value[1]}`}</Typography.Text>
        );
      case "date":
      case "date_time":
      case "time":
        return (
          <Tag
            bordered={false}
            color={fieldType === "date" ? "processing" : "#000"}
            icon={
              fieldType === "date" ? (
                <CalendarOutlined />
              ) : (
                <FieldTimeOutlined />
              )
            }
          >
            {value}
          </Tag>
        );
      default:
        return null;
    }
  }, [fieldType, value, item, fallbackSrc]);

  return (
    <li
      className="flex w-full list-none flex-row items-center justify-between py-1"
      key={`card_list_value_${index}`}
    >
      {/* Field Label */}
      <Typography.Text
        type="secondary"
        ellipsis={{ tooltip: true }}
        style={{ maxWidth: 150, whiteSpace: "nowrap" }}
      >
        {fieldType !== "file" && fieldType !== "image" && fieldType !== "album"
          ? Object.keys(item)[0]
          : value}
      </Typography.Text>
      {/* Field Value */}
      {renderFieldContent}
    </li>
  );
};

export default FieldRenderer;
