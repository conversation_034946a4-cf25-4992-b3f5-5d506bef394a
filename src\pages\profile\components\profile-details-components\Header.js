import { createElement } from "react";
// import { useDispatch } from "react-redux";
// import { setUserInfos } from "../../../../new-redux/actions/user.actions/getUser";
import { <PERSON><PERSON>, Card, Divider, List, Space, Tag } from "antd";
import {
  EditOutlined,
  MailOutlined,
  MobileOutlined,
  PhoneOutlined,
} from "@ant-design/icons";
// import { fetchColleagues } from "../../../voip/services/services";
// import { toastNotification } from "../../../../components/ToastNotification";
// import { fetchProfile } from "../../services";
import DisplayAvatar from "../../../voip/components/DisplayAvatar";
import { URL_ENV } from "index";
import { useSelector } from "react-redux";
import { isGuestConnected } from "utils/role";

// const urlImage = process.env.REACT_APP_AVATAR_URL;

const Header = ({ setOpenDrawerUpdate, t }) => {
  //
  // const dispatch = useDispatch();
  const { user } = useSelector((state) => state.user);
  const chatUserRole = useSelector((state) => state?.chat?.currentUser?.role);

  const personalEmailAccounts = user?.accounts_email?.filter(
    (email) => !Number(email?.shared) && email?.email !== user?.email
  );
  const sharedEmailAccounts = user?.accounts_email?.filter((email) =>
    Number(email?.shared)
  );

  // console.log({ personalEmailAccounts, sharedEmailAccounts });

  // const [userData, setUserData] = useState({});
  // const [isLoading, setIsLoading] = useState(false);

  // const fetchHeaderInfo = useCallback(async () => {
  //   try {
  //     setIsLoading(true);
  //     const {
  //       data: { data },
  //     } = await fetchColleagues(null, null, null, user?.id);
  //     setUserData({
  //       ...data,
  //       name: data?.name?.replaceAll("_", " "),
  //       image: data?.image
  //         ? `${
  //             URL_ENV?.REACT_APP_BASE_URL +
  //             URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
  //           }${data.image}`
  //         : null,
  //     });
  //     setIsLoading(false);
  //     JSON.stringify(userData) !== "{}" && fetchProfile(dispatch, setUserInfos);
  //   } catch (err) {
  //     err?.response?.status !== 401 &&
  //       toastNotification("error", t("toasts.somethingWrong"), "topRight");
  //     throw new Error(err?.message ? err.message : err);
  //   }
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [user?.id]);

  // useEffect(() => {
  //   fetchHeaderInfo();
  // }, [fetchHeaderInfo]);
  //
  const listData = [
    {
      title: (
        <span className="font-bold">{user?.label?.replaceAll("_", " ")}</span>
      ),
      avatar: (
        <DisplayAvatar
          name={user?.label}
          urlImg={
            user.avatar &&
            `${URL_ENV?.REACT_APP_BASE_URL}${URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL}${user.avatar}`
          }
          size={85}
        />
      ),
      description: (
        <div className="flex flex-col space-y-0.5">
          <Space
            key="department-role"
            wrap
            size={3}
            split={<Divider type="vertical" />}
          >
            {user?.department}
            {user?.role}
          </Space>
          <Space key="rmcAccess" size={5}>
            <span style={{ fontSize: 13, color: "rgb(100 116 139)" }}>
              {t("profile.rmcAccess")}:
            </span>
            {!user?.rmc_access || user?.rmc_access === "NON" ? (
              // <CloseCircleOutlined style={{ fontSize: 14, color: "red" }} />
              <Tag bordered={false} color="error">
                {t("voip.no")}
              </Tag>
            ) : (
              <Tag bordered={false} color="success">
                {t("voip.yes")}
              </Tag>
              // <CheckCircleOutlined style={{ fontSize: 14, color: "green" }} />
            )}
          </Space>

          {personalEmailAccounts?.length ? (
            <Space key="sharedEmail" align="start" size={5}>
              <span
                className="whitespace-nowrap"
                style={{ fontSize: 13, color: "rgb(100 116 139)" }}
              >
                {t("profile.personalEmail")}:
              </span>
              <Space wrap size={3} split={<Divider type="vertical" />}>
                {personalEmailAccounts.map((email, i) => (
                  <span key={i} className="select-all">
                    {email?.email}
                  </span>
                ))}
              </Space>
            </Space>
          ) : null}

          {sharedEmailAccounts?.length ? (
            <Space key="sharedEmail" align="start" size={5}>
              <span
                className="whitespace-nowrap"
                style={{ fontSize: 13, color: "rgb(100 116 139)" }}
              >
                {t("profile.sharedEmail")}:
              </span>
              <Space wrap size={3} split={<Divider type="vertical" />}>
                {sharedEmailAccounts.map((email, i) => (
                  <span key={i} className="select-all">
                    {email?.email}
                  </span>
                ))}
              </Space>
            </Space>
          ) : null}
        </div>
      ),
      content: "",
    },
  ];

  const IconText = ({ icon, text }) => (
    <div className="flex flex-row">
      {createElement(icon, {
        style: {
          marginRight: 8,
          // fontSize: "15px",
          // cursor: icon === CommentOutlined && "pointer",
          // color: "rgb(22, 119, 255)",
          transform: icon === PhoneOutlined && "rotate(100deg)",
        },
      })}
      <span className="select-all">{text}</span>
    </div>
  );

  // const fetchDialAndNbr = (phones) => {
  //   let dial = "";
  //   let nbr = "";
  //   if (phones?.length) {
  //     const phone = phones[0];
  //     dial = phone[0];
  //     nbr = phone[1];
  //   }
  //   return {
  //     get dial() {
  //       return dial;
  //     },
  //     get number() {
  //       return nbr;
  //     },
  //   };
  // };
  // const phoneInfo = fetchDialAndNbr(user?.phones);

  const actions = [
    <IconText icon={MailOutlined} text={user?.email} key={user?.email} />,
    ...(user?.phone?.length
      ? [
          <IconText
            icon={MobileOutlined}
            text={`(${user.phone?.[0]}) ${user.phone?.[1]}`}
            key={user?.phone}
          />,
        ]
      : []),
    <IconText
      icon={PhoneOutlined}
      text={user?.extension}
      key={user?.extension}
    />,
  ];

  return (
    <Card style={{ padding: "0px" }}>
      {/* <Skeleton loading={isLoading} avatar active> */}
      <List
        itemLayout="vertical"
        size="small"
        dataSource={listData}
        renderItem={(item) => (
          <List.Item
            key={user?.id}
            extra={
              <>
                <div className="mb-8" />
                <Button
                  disabled={isGuestConnected(chatUserRole, user?.role)}
                  onClick={() => setOpenDrawerUpdate(true)}
                  size="small"
                  type="primary"
                  ghost
                  icon={<EditOutlined />}
                >
                  {t("profile.edit")}
                </Button>
              </>
            }
          >
            <List.Item.Meta
              avatar={item?.avatar}
              title={item?.title}
              description={item?.description}
            />
            <Space split={<Divider type="vertical" />}>
              {actions?.map((action, i) => (
                <span key={i}>{action}</span>
              ))}
            </Space>
          </List.Item>
        )}
      />
      {/* </Skeleton> */}
    </Card>
  );
};

export default Header;
