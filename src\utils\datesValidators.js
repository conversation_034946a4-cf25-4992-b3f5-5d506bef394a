import dayjs from "dayjs";

// Disable following dates of a selected date.
export const disableNextDates = (selectedEndDate, selectedDate) => {
  if (selectedEndDate !== "") {
    return selectedDate ? selectedDate > selectedEndDate : null;
  }
};

// Disable following hours of a selected hour.
export const disableNextHours = (selectedEndTime, selectedStartDate, selectedEndDate) => {
  const hours = [];
  if (
    selectedEndTime !== "" &&
    selectedStartDate &&
    (selectedStartDate == selectedEndDate ||
      (selectedStartDate == dayjs().format("YYYY-MM-DD") && selectedEndDate == ""))
  )
    for (let i = Number(selectedEndTime.split(":")[0]); i < 24; i++) {
      hours.push(i);
    }
  return hours;
};

// Disable Previous hours of a selected hour.
export const disablePrevHours = (selectedStartDate, selectedEndDate, selectedStartTime) => {
  const hours = [];
  if (selectedStartDate && dayjs(selectedStartDate).isSame(selectedEndDate))
    for (let i = 0; i < Number(selectedStartTime.split(":")[0]); i++) {
      hours.push(i);
    }
  return hours;
};

// Disable previous minutes of a selected minute.
export const disablePrevMinutes = (selectedStartDate, selectedEndDate, selectedStartTime, time) => {
  const minutes = [];
  if (
    dayjs(selectedStartDate).isSame(selectedEndDate) &&
    Number(selectedStartTime.split(":")[0]) === time
  ) {
    for (let i = 0; i < Number(selectedStartTime.split(":")[1].split(" ")[0]); i++) {
      minutes.push(i);
    }
  }
  return minutes;
};

// Disable Previous dates of a selected date.
export const disablePreviousDates = (selectedDate, selectedStartDate) => {
  return selectedDate ? selectedDate < selectedStartDate : null;
};
