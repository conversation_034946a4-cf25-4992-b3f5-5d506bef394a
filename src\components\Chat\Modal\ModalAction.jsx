import {
  AudioOutlined,
  CloseCircleFilled,
  DeleteOutlined,
  FileImageOutlined,
  FileOutlined,
  RetweetOutlined,
} from "@ant-design/icons";
import { Avatar, Input, Tooltip, Typography } from "antd";
import React, {
  Suspense,
  lazy,
  useCallback,
  useEffect,
  useLayoutEffect,
  useRef,
  useState,
} from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";

import { AvatarChat, Loader, ModalConfirm } from "components/Chat";
import { useActionMessage } from "pages/layouts/chat/hooks/useActionMessage";
import { lazyRetry } from "utils/lazyRetry";
import {
  audioMessageTypes,
  convertToPlain,
  fileMessageTypes,
  getName,
  imageMessageTypes,
  simpleMessageTypes,
} from "pages/layouts/chat/utils/ConversationUtils";
import { URL_ENV } from "index";

const MembersAdd = lazy(() =>
  lazyRetry(
    () => import("pages/layouts/chat/sidebar/header/members_add"),
    "MembersAdd"
  )
);
const { Text } = Typography;

function ModalAction({ openAction, item, source, toggleAction }) {
  const [actionType, setActionType] = useState("");
  const [focusInput, setFocusInput] = useState(null);
  const [errorListUser, setErrorListUser] = useState(false);
  const selectedConversation = useSelector(
    (state) => state.ChatRealTime.selectedConversation
  );

  const { userList, membersGroupsChat } = useSelector((state) => state.chat);

  const { t } = useTranslation("common");
  const [listAllUsers, setAllListUsers] = useState([]);
  const [search, setSearch] = useState("");
  const ref = useRef(null);
  const {
    mutate: handleActionMessage,
    isLoading,
    isSuccess,
  } = useActionMessage(actionType);
  const generateDisucssion = useCallback(() => {
    const selectedId = `${selectedConversation?.id}__${selectedConversation?.type}`;
    const discussionList = [
      ...membersGroupsChat
        .filter(
          (el) =>
            !(
              (el?.contact || el?.room)?._id === selectedConversation?.id &&
              (el?.room !== null ? "room" : "user") ===
                selectedConversation?.type
            )
        )
        .map((el) => ({
          ...(el.contact || el.room),
          _id: el.contact
            ? `${el.contact?._id}__user`
            : `${el.room?._id}__room`,
          type: "disc",
        })),
    ];
    const usersIdsSet = new Set(membersGroupsChat.map((el) => el.contact?._id));

    let list = [];
    for (let i = 0; i < userList.length; i++) {
      if (!usersIdsSet.has(userList[i]._id)) {
        list.push({
          ...userList[i],
          _id: `${userList[i]._id}__user`,
          type: "users",
          uuid: userList[i].uuid,
        });
      }
    }

    const selectedConversation_object = {
      _id: selectedId,
      email:
        selectedConversation?.type === "room"
          ? undefined
          : selectedConversation?.email,
      name: selectedConversation?.name,
      image: selectedConversation?.image,
      uuid: selectedConversation?.uuid,
      post_number:
        selectedConversation?.type === "room"
          ? undefined
          : selectedConversation?.post_number,
      // controll user
      admin_id:
        selectedConversation?.type === "user"
          ? undefined
          : selectedConversation?.admin_id,
      description:
        selectedConversation?.type === "user"
          ? undefined
          : selectedConversation?.description,
      predefined:
        selectedConversation?.type === "user"
          ? undefined
          : selectedConversation?.predefined,
      type: "disc",
    };

    const finalArray = [
      selectedConversation_object,
      ...discussionList,
      ...list,
    ];

    return finalArray;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    membersGroupsChat,
    selectedConversation?.id,
    selectedConversation?.type,
    userList,
  ]);

  const handleCancel = () => {
    toggleAction(false, "", null);
  };
  const deselectUser = (user) => {
    setAllListUsers((prev) => {
      for (let i = 0; i < prev.length; i++) {
        if (prev[i]._id === user._id) {
          prev[i].selected = false;
          break;
        }
      }

      return [...prev];
    });
  };
  useLayoutEffect(() => {
    let mounted = true;
    if (openAction.type === "forward" && openAction.state && mounted) {
      setAllListUsers(generateDisucssion());
      setFocusInput(Math.floor(Math.random() * 10000 + 1));
    }

    return () => {
      mounted = false;
    };
  }, [openAction.type, openAction.state]);
  useEffect(() => {
    let time;
    if (ref.current && focusInput) {
      document.getElementById("editor-input")?.blur();
      clearTimeout(time);
      time = setTimeout(() => {
        ref.current?.focus();
      }, 400);
    }
    if (isSuccess) {
      toggleAction(false, "", null);
    }
    return () => {
      clearTimeout(time);
    };
  }, [focusInput, isSuccess]);

  return (
    <ModalConfirm
      open={openAction.state && openAction.type !== "edit"}
      title={
        <span className="ant-modal-confirm-title flex items-center space-x-2">
          {openAction.type === "delete" ? (
            <>
              <DeleteOutlined className="text-[#ff4d4f]" />

              <p>{t("chat.delete.title_modal")} </p>
            </>
          ) : (
            <>
              <RetweetOutlined className="text-slate-400" />

              <p>{t("chat.forward.title_modal")}</p>
            </>
          )}
        </span>
      }
      content={
        <div className=" ant-modal-confirm-content">
          {openAction.type === "forward" ? (
            <div className="flex flex-col">
              {listAllUsers.filter((el) => el.selected)?.length > 0 && (
                <div className="mb-2 overflow-hidden">
                  <Text type="secondary">{t("chat.forward.to")}:</Text>
                  <Avatar.Group className="w-[650px] space-x-2 overflow-auto px-1 py-5">
                    {listAllUsers
                      .filter((el) => el.selected)
                      .map((el) => (
                        <Tooltip placement="top" title={el.name} key={el._id}>
                          <div className="relative">
                            <CloseCircleFilled
                              style={{
                                position: "absolute",
                                right: 0,
                                top: "-10px",
                                cursor: "pointer",
                                zIndex: 20,
                                color: "red",
                                fontSize: "15px",
                              }}
                              onClick={() => deselectUser(el)}
                            />
                            <AvatarChat
                              isPublic={el.predefined === 2}
                              url={
                                el.admin_id
                                  ? URL_ENV?.REACT_APP_OAUTH_CHAT_API +
                                    process.env
                                      .REACT_APP_SUFFIX_CHAT_GROUP_STORAGE +
                                    el?.image
                                  : URL_ENV?.REACT_APP_BASE_URL +
                                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                                    el?.image
                              }
                              type={el.admin_id ? "room" : "user"}
                              size={38}
                              height={10}
                              width={10}
                              name={getName(el.name, "avatar")}
                              hasImage={el.image}
                            />
                          </div>
                        </Tooltip>
                      ))}
                  </Avatar.Group>
                </div>
              )}

              <div
                role="button"
                onClick={() => {
                  let time;
                  document.getElementById("editor-input")?.blur();
                  clearTimeout(time);

                  time = setTimeout(() => {
                    setFocusInput(Math.floor(Math.random() * 10000 + 1));
                  }, 10);
                }}
                className="mb-1 flex items-center justify-between space-x-2">
                <Input.Search
                  allowClear
                  id="search-input"
                  size="middle"
                  style={{
                    width: "100%",
                  }}
                  name="search-input"
                  ref={ref}
                  placeholder={t("chat.searchSide.searchMembers")}
                  value={search}
                  onChange={(e) =>
                    setSearch(
                      e.target.value.trimStart().replace(/\s{1,} /g, " ")
                    )
                  }
                />
              </div>
              <div className="my-2 mt-1  flex h-5 items-center gap-x-1 text-sm text-gray-500">
                <p> {t("chat.forward.previewMsg")}</p>
                {imageMessageTypes.includes(openAction.item.type) ? (
                  <div className="flex items-center">
                    <FileImageOutlined className="mr-0.5" />
                    <span> {t("chat.message_type.image")}</span>
                  </div>
                ) : audioMessageTypes.includes(openAction.item.type) ? (
                  <div className="flex items-center">
                    <AudioOutlined className="mr-0.5" />
                    <span> {t("chat.audio")}</span>
                  </div>
                ) : fileMessageTypes.includes(openAction.item.type) ? (
                  <div className="flex items-center">
                    {" "}
                    <FileOutlined className="mr-0.5" />
                    <span> {t("chat.message_type.file")}</span>
                  </div>
                ) : simpleMessageTypes.includes(openAction.item.type) ? (
                  <>
                    {'" '}
                    <p className=" max-w-xs truncate  ">
                      {convertToPlain(openAction.item.message)}
                    </p>
                    {' " '}
                  </>
                ) : null}
              </div>

              <Suspense fallback={<Loader size={30} />}>
                <MembersAdd
                  listAllUsers={listAllUsers}
                  setAllListUsers={setAllListUsers}
                  search={search}
                />
              </Suspense>

              {errorListUser &&
              listAllUsers.filter((el) => el.selected).length === 0 ? (
                <div className="ant-form-item-explain-error text-[#ff4d4f]">
                  {t("chat.forward.error_select_user")}
                </div>
              ) : (
                ""
              )}
            </div>
          ) : openAction.type === "delete" ? (
            t("chat.delete.content_modal_message")
          ) : null}
        </div>
      }
      dangerMode={openAction.type === "delete"}
      okText={
        openAction.type === "delete"
          ? t("chat.action.delete")
          : t("chat.action.forward")
      }
      cancelText={t("form.cancel")}
      onOk={() => {
        if (
          openAction.type === "forward" &&
          listAllUsers.filter((el) => el.selected).length === 0
        ) {
          setErrorListUser(true);
        } else {
          setErrorListUser(false);

          handleActionMessage({
            message_id: item?._id,

            params: {
              source,
              users:
                openAction.type === "delete"
                  ? item
                  : listAllUsers
                      .filter((el) => el.selected)
                      .map((el) => ({
                        _id: el._id.split("__")[0],
                        email: el.email,
                      })),
              msg: item,
            },
            type_conversation: selectedConversation?.type,
            type_action:
              openAction.type === "delete"
                ? "remove_message"
                : "forward_message",
          });

          setActionType(
            openAction.type === "delete" ? "remove_message" : "forward_message"
          );
        }
      }}
      onCancel={handleCancel}
      loading={isLoading}
      width={openAction.type === "forward" ? 700 : undefined}
    />
  );
}

export default ModalAction;
