import { memo, useCallback, useRef, useState } from "react";
import { But<PERSON>, Divider, Form, Modal, Select, Space, Spin } from "antd";
import { useTranslation } from "react-i18next";
import { HiOutlineBuildingOffice } from "react-icons/hi2";
import { TeamOutlined } from "@ant-design/icons";
import { CgUserlane } from "react-icons/cg";
import { Blocks, HeartHandshake } from "lucide-react";
import TicketIconSphere from "components/icons/TicketIconSphere";
import { AiOutlineShoppingCart } from "react-icons/ai";
import { LuPalmtree } from "react-icons/lu";
import { useSelector } from "react-redux";
import { affectations, postAffectations } from "pages/voip/services/services";
import DisplayAvatar from "pages/voip/components/DisplayAvatar";
import { URL_ENV } from "index";
import { HighlightSearchW } from "pages/voip/components";
import { toastNotification } from "components/ToastNotification";
import { renderIcon } from "pages/clients&users/components/RenderColumnsTable";
import { debounce } from "lodash";

const FAMILY_ICONS = {
  1: <HiOutlineBuildingOffice style={{ fontSize: "16px", marginTop: "5px" }} />,
  2: <TeamOutlined style={{ fontSize: "16px", marginTop: "5px" }} />,
  3: <HeartHandshake size={18} style={{ marginTop: 7 }} />,
  5: <AiOutlineShoppingCart style={{ fontSize: "16px", marginTop: "5px" }} />,
  6: <TicketIconSphere size={19} style={{ marginTop: 5 }} />,
  7: <Blocks size={17} style={{ marginTop: 7 }} />,
  8: <LuPalmtree style={{ fontSize: "16px", marginTop: "5px" }} />,
  9: <CgUserlane style={{ fontSize: "16px", marginTop: "5px" }} />,
};
//
const getAvatarUrl = (avatar) => {
  return (
    avatar &&
    `${
      URL_ENV?.REACT_APP_BASE_URL + URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
    }${avatar}`
  );
};

const handleApiError = (err, t) => {
  if (err.name !== "CanceledError" && err?.response?.status !== 401) {
    toastNotification("error", t("toasts.somethingWrong"), "topRight");
  }
};

const getFamilyLabel = (familyId, t) => {
  const families = {
    1: t("contacts.company"),
    2: t("contacts.contact"),
    3: t("import.deal"),
    5: t("contacts.product"),
    6: t("contacts.ticket"),
    7: t("contacts.project"),
    8: t("contacts.booking"),
    9: t("contacts.leads"),
  };
  return families[familyId];
};

const getFamiliesOption = (access, t) => {
  return Object.entries(FAMILY_ICONS)
    .filter(([key]) => access?.[getAccessKey(key)] === "1")
    .map(([key, icon]) => ({
      label: (
        <Space>
          {icon}
          <p>{getFamilyLabel(key, t)}</p>
        </Space>
      ),
      value: Number(key),
    }));
};

const getAccessKey = (key) => {
  const accessMap = {
    1: "companies",
    2: "contact",
    3: "deals",
    5: "products",
    6: "ticket",
    7: "projects",
    8: "booking",
    9: "leads",
  };
  return accessMap[key];
};
const ModalAffectation = ({
  open,
  setOpen,
  record,
  usedAccount,
  setDataSource,
}) => {
  //
  const [t] = useTranslation("common");
  const prevController = useRef(null);
  const selectRef = useRef(null);
  //
  const user = useSelector((state) => state.user?.user);
  //
  const { affectation } = record;
  //
  const [state, setState] = useState({
    selectedFamily: affectation?.family_id ?? null,
    loadingOptions: false,
    fetchedOptions: affectation?._id
      ? [
          {
            key: affectation.affect_to,
            value: `${affectation.affect_to}-${affectation.affect_label}`,
            label: (
              <div className="flex items-center space-x-1">
                <DisplayAvatar
                  size={30}
                  name={affectation.affect_label}
                  urlImg={
                    affectation.avatar && getAvatarUrl(affectation.avatar)
                  }
                  icon={renderIcon(Number(affectation.family_id), 16)}
                />

                <p className="truncate font-semibold leading-4">
                  {affectation.affect_label}
                </p>
              </div>
            ),
          },
        ]
      : [],
    selectedElement: affectation?.affect_to ?? null,
    saveIsLoading: false,
  });

  const {
    selectedFamily,
    loadingOptions,
    fetchedOptions,
    selectedElement,
    saveIsLoading,
  } = state;
  //
  const updateState = (newState) => {
    setState((prev) => ({ ...prev, ...newState }));
  };
  //
  const handleCancel = useCallback(() => {
    setOpen({});
    updateState({
      selectedFamily: null,
      fetchedOptions: [],
      selectedElement: null,
    });
  }, [setOpen]);
  //
  //
  const fetchOptions = useCallback(
    async (searchText, familyID) => {
      const controller = new AbortController();
      const signal = controller.signal;
      if (prevController.current) {
        prevController.current.abort();
      }
      prevController.current = controller;
      try {
        updateState({ loadingOptions: true });
        const {
          data: { data },
        } = await affectations(familyID, user?.id, searchText, signal);
        const options = data?.map((e) => ({
          key: e._id,
          value: `${e._id}-${e.label}`,
          label: (
            <div className="flex items-center space-x-1">
              <DisplayAvatar
                size={30}
                name={e.label}
                urlImg={e.avatar && getAvatarUrl(e.avatar)}
                icon={renderIcon(Number(familyID), 16)}
              />
              <div>
                <p className="truncate font-semibold leading-4">
                  {HighlightSearchW(e.label || e.reference, searchText || "")}
                </p>
                {e.label && e.reference && (
                  <p className="text-xs leading-4 text-slate-500">
                    {e.reference}
                  </p>
                )}
              </div>
            </div>
          ),
        }));

        updateState({ fetchedOptions: options });
      } catch (err) {
        handleApiError(err, t);
      } finally {
        updateState({ loadingOptions: false });
      }
    },
    [t, user?.id]
  );
  //
  const saveAffectation = async () => {
    try {
      updateState({ saveIsLoading: true });
      const formData = new FormData();
      formData.append("element_id", record?.key);
      formData.append("affect_to", selectedElement);
      formData.append("affected_family_id", selectedFamily);
      formData.append("type", "email");
      formData.append("account_id", usedAccount?.value);
      usedAccount?.departmentId?.forEach((id) => {
        formData.append("departement_id[]", id);
      });
      const response = await postAffectations(formData);

      setDataSource((prev) =>
        prev.map((email) =>
          email.key === record?.key
            ? {
                ...email,
                affectation: {
                  ...response.data.data,
                  _id: response.data.data.id,
                },
              }
            : email
        )
      );
      handleCancel();
      toastNotification("success", t("mailing.affectedSuccess"), "topRight", 3);
    } catch (err) {
      handleApiError(err, t);
    } finally {
      updateState({ saveIsLoading: false });
    }
  };
  //
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const handleSearch = useCallback(
    debounce((value, familyId) => {
      fetchOptions(value, familyId);
    }, 300),
    []
  );
  //
  if (!open || !record) return null;
  //
  const modalTitle = (
    <div className="relative flex flex-grow flex-col">
      <div className="space-y-1">
        <p className="text-base font-semibold">
          {affectation?._id ? t("mailing.updateAffectation") : "Affectation"}
        </p>
        <div className="flex space-x-1 text-sm">
          <p className="whitespace-nowrap font-semibold">
            {t("emailTemplates.subjectMail")}:
          </p>
          <p className="truncate">{record?.subject}</p>
        </div>
      </div>
      <Divider style={{ margin: "5px 0 0 0" }} />
    </div>
  );
  //
  return (
    <Modal
      width={450}
      title={modalTitle}
      open={open}
      onCancel={handleCancel}
      maskClosable={false}
      footer={[
        <Button
          // key="submit"
          key="save-button"
          type="primary"
          loading={saveIsLoading}
          onClick={saveAffectation}
          disabled={
            !selectedFamily ||
            !selectedElement ||
            affectation?.affect_to === selectedElement
          }
        >
          {t("voip.save")}
        </Button>,
      ]}
    >
      <Form layout="vertical">
        <Form.Item
          label={t("voip.selectModule")}
          name="module"
          rules={[
            {
              required: true,
              // message: "",
            },
          ]}
          initialValue={selectedFamily}
        >
          <Select
            allowClear
            style={{ width: "100%" }}
            options={getFamiliesOption(user.access, t)}
            onChange={(value) => {
              updateState({ selectedFamily: value });
              if (value) {
                fetchOptions("", value);
                if (selectRef.current) selectRef.current.focus();
              }
            }}
          />
        </Form.Item>

        <Form.Item
          label={
            selectedFamily ? (
              `${t("voip.search_select")} ${getFamilyLabel(selectedFamily, t)}`
            ) : (
              <span className=" text-white">Hello Genius</span>
            )
          }
          name="element"
          rules={[
            {
              required: selectedFamily,
              // message: "",
            },
          ]}
          initialValue={
            affectation?._id
              ? `${affectation.affect_to}-${affectation.affect_label}`
              : null
          }
        >
          <Select
            ref={selectRef}
            // allowClear
            showSearch
            style={{ width: "100%" }}
            options={fetchedOptions}
            loading={loadingOptions}
            disabled={!selectedFamily}
            onSelect={(_, option) =>
              updateState({ selectedElement: option.key })
            }
            onSearch={(value) => handleSearch(value, selectedFamily)}
            suffixIcon={<Spin spinning={loadingOptions} size="small" />}
            notFoundContent={loadingOptions ? <Spin size="small" /> : false}
          />
        </Form.Item>
      </Form>
      <Divider />
    </Modal>
  );
};
//

export default memo(ModalAffectation);
