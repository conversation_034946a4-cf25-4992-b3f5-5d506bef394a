import React, { useEffect, useLayoutEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate } from "react-router-dom";
import TabsDetails from "../../components/Tabs";

const HelpDesk = () => {
  const [t] = useTranslation("common");
  const [keyTab, setKeyTab] = useState("");
  const { pathname } = useLocation();
  const navigate = useNavigate();
  const items = [
    // {
    //   label: (
    //     <div onClick={() => navigate("/settings/helpDesk/folders")}>
    //       {t("helpDesk.folder")}
    //     </div>
    //   ),
    //   key: "1",
    // },

    {
      label: (
        <div onClick={() => navigate("/settings/helpDesk/severities")}> {t("menu2.severity")}</div>
      ),
      key: "2",
    },
    {
      label: <div onClick={() => navigate("/settings/helpDesk/SLA")}> SLA</div>,
      key: "3",
    },
    {
      label: (
        <div onClick={() => navigate("/settings/helpDesk/Subjects")}> {t("helpDesk.subject")}</div>
      ),
      key: "4",
    },
    {
      label: (
        <div onClick={() => navigate("/settings/helpDesk/Levels")}> {t("helpDesk.Levels")}</div>
      ),
      key: "5",
    },
  ];
  useLayoutEffect(() => {
    if (pathname == "/settings/helpDesk/folders") {
      setKeyTab("1");
    } else if (pathname == "/settings/helpDesk/severities") {
      setKeyTab("2");
    } else if (pathname == "/settings/helpDesk/SLA") {
      setKeyTab("3");
    } else if (pathname == "/settings/helpDesk/Subjects") {
      setKeyTab("4");
    } else if (pathname == "/settings/helpDesk/Levels") {
      setKeyTab("5");
    } else {
      setKeyTab("1");
    }
  }, []);

  useEffect(() => {
    if (keyTab !== "") {
      if (keyTab == 1) {
        navigate("/settings/helpDesk/folders");
      } else if (keyTab == 2) {
        navigate("/settings/helpDesk/severities");
      } else if (keyTab == 3) {
        navigate("/settings/helpDesk/SLA");
      } else if (keyTab == 4) {
        navigate("/settings/helpDesk/Subjects");
      } else if (keyTab == 5) {
        navigate("/settings/helpDesk/Levels");
      }
    }
  }, [keyTab, navigate]);

  return <>{keyTab ? <TabsDetails items={items} keyTab={keyTab} setKey={setKeyTab} /> : ""}</>;
};

export default HelpDesk;
