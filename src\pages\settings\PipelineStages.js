import React, { useEffect, useLayoutEffect, useState } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import TabsDetails from "../../components/Tabs";
import ShowPipelineStage from "../../components/ShowPipelineStage";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { displayFamiliesIcons } from "utils/displayIcon";
const PipelineStages = () => {
  const [t] = useTranslation("common");
  const [keyTab, setKeyTab] = useState("");
  const { pathname } = useLocation();
  const { families } = useSelector((state) => state?.families);
  const { user } = useSelector((state) => state?.user);

  const navigate = useNavigate();
  const { id } = useParams();
  const familyLabelFromKey = (key) => {
    let index = families && families.findIndex((element) => element?.id == key);
    if (index > -1) {
      console.log(families[index]?.label);
      return families[index]?.label;
    }
  };

  const items =
    families &&
    families.map((element) => ({
      key: element?.id,
      icon: displayFamiliesIcons(element?.label, 4, 4),
      label: t(`fields_management.${element?.label.toLowerCase()}`).includes(
        "fields_management"
      )
        ? element?.label
        : t(`fields_management.${element?.label.toLowerCase()}`),
    }));
  useLayoutEffect(() => {
    if (pathname) {
      setKeyTab(families.find((el) => el.label === id)?.id || 1);
    }
  }, [id]);
  useEffect(() => {
    if (keyTab) {
      navigate(`/settings/pipeline/${familyLabelFromKey(keyTab)}`);
    }
  }, [keyTab, navigate]);
  return (
    <div className="mr-1">
      {keyTab ? (
        <>
          <TabsDetails items={items} keyTab={keyTab} setKey={setKeyTab} />
          <ShowPipelineStage items={items} keyTab={keyTab} />
        </>
      ) : (
        ""
      )}
    </div>
  );
};

export default PipelineStages;
