import React, { useState } from "react";
import { Upload, Button, Typography, Space, Card, message } from "antd";
import { 
  CloudUploadOutlined, 
  InboxOutlined, 
  FileOutlined, 
  DeleteOutlined, 
  UploadOutlined, 
  CloseOutlined 
} from "@ant-design/icons";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { uploadFileDrive } from "../../../services/main.service";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";

const { Dragger } = Upload;
const { Title, Text } = Typography;

const UploadInput = ({
  onFileUpload,
  onUploadSuccess,
  onUploadError,
  showDragDropUpload = true,
  maxFiles = 10,
  maxFileSize = 50 * 1024 * 1024, // 50MB default
  acceptedFileTypes = null,
  uploadEndpoint = uploadFileDrive,
  queryKey = ["drive-items"],
  className = "",
  style = {},
  disabled = false,
  ...props
}) => {
  const [t] = useTranslation("common");
  const queryClient = useQueryClient();
  const { parentItem } = useSelector((state) => state.drive);
  
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadingFile, setUploadingFile] = useState(null);
  const [abortController, setAbortController] = useState(null);

  // Upload mutation
  const uploadMutation = useMutation({
    mutationFn: ({ formData, config }) => uploadEndpoint(formData, config),
    onSuccess: (data) => {
      queryClient.invalidateQueries([queryKey], { exact: false });
      message.success(t("drive.fileUploadedSuccessfully"));
      resetUploadState();
      setSelectedFiles([]);
      if (onUploadSuccess) {
        onUploadSuccess(data);
      }
    },
    onError: (error) => {
      console.error("Upload error:", error);
      if (error.name !== 'CanceledError') {
        message.error(t("drive.failedToUploadFile"));
      } else {
        message.info(t("drive.uploadCanceled"));
      }
      resetUploadState();
      if (onUploadError) {
        onUploadError(error);
      }
    },
  });

  const resetUploadState = () => {
    setIsUploading(false);
    setUploadProgress(0);
    setUploadingFile(null);
    setAbortController(null);
  };

  const cancelUpload = () => {
    if (abortController) {
      abortController.abort();
      resetUploadState();
    }
  };

  const handleFilesSelected = (files) => {
    // Filter files based on max count
    const limitedFiles = files.slice(0, maxFiles);
    
    // Filter files based on size
    const validFiles = limitedFiles.filter(file => {
      if (file.size > maxFileSize) {
        message.error(`${file.name} is too large. Maximum size is ${formatFileSize(maxFileSize)}`);
        return false;
      }
      return true;
    });

    // Filter files based on accepted types
    const finalFiles = acceptedFileTypes 
      ? validFiles.filter(file => {
          const fileExtension = file.name.split('.').pop().toLowerCase();
          if (!acceptedFileTypes.includes(fileExtension)) {
            message.error(`${file.name} has an unsupported file type`);
            return false;
          }
          return true;
        })
      : validFiles;

    setSelectedFiles(finalFiles);
    if (onFileUpload) {
      onFileUpload(finalFiles);
    }
  };

  const handleConfirmUpload = () => {
    if (selectedFiles.length === 0) {
      message.warning(t("drive.noFilesSelected") || "No files selected");
      return;
    }

    // Create abort controller for cancellation
    const controller = new AbortController();
    setAbortController(controller);
    
    setIsUploading(true);
    setUploadingFile({ 
      name: `${selectedFiles.length} file(s)`, 
      size: selectedFiles.reduce((total, file) => total + file.size, 0) 
    });
    setUploadProgress(0);

    const uploadData = new FormData();
   
    selectedFiles.forEach((file) => {
      uploadData.append("file[]", file);
    });
    !!parentItem && uploadData.append("parent", parentItem || "");
    uploadData.append("type", "file");

    const config = {
      onUploadProgress: (progressEvent) => {
        const progress = Math.round((progressEvent.loaded / progressEvent.total) * 100);
        setUploadProgress(progress);
      },
      signal: controller.signal,
    };

    uploadMutation.mutate({ formData: uploadData, config });
  };

  const uploadProps = {
    name: 'file',
    multiple: true,
    showUploadList: false,
    fileList: [],
    disabled: disabled || isUploading,
    beforeUpload: (file, fileList) => {
      handleFilesSelected(fileList);
      return false;
    },
    onChange: (info) => {
      const files = info.fileList.map(file => file.originFileObj || file);
      handleFilesSelected(files);
    },
    ...props
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const clearFiles = () => {
    setSelectedFiles([]);
    if (onFileUpload) {
      onFileUpload([]);
    }
  };

  const removeFile = (indexToRemove) => {
    const updatedFiles = selectedFiles.filter((_, index) => index !== indexToRemove);
    setSelectedFiles(updatedFiles);
    if (onFileUpload) {
      onFileUpload(updatedFiles);
    }
  };

  return (
    <div className={`flex flex-col w-full justify-center items-center`} style={style}>
      {showDragDropUpload && (
        <div className="w-full max-w-2xl">
          <Dragger
            {...uploadProps}
            loading={isUploading}
            style={{
              background: 'transparent',
              border: '2px dashed #d9d9d9',
              borderRadius: '12px',
              padding: '40px 20px',
              transition: 'all 0.3s ease',
            }}
            className="hover:border-blue-400 hover:bg-blue-50"
          >
            <div className="flex flex-col items-center justify-center gap-4">
              <div className="flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full">
                <CloudUploadOutlined 
                  style={{ 
                    fontSize: '32px', 
                    color: '#1890ff' 
                  }} 
                />
              </div>
              
              <div className="text-center">
                <p className="text-lg font-semibold text-gray-700 mb-2">
                  {t("drive.dragDropFiles")}
                </p>
                <p className="text-sm text-gray-500 mb-4">
                  {t("drive.dragDropDescription")}
                </p>
                <div className="flex items-center justify-center gap-2 text-blue-600">
                  <InboxOutlined />
                  <span className="font-medium">
                    {t("drive.clickToUpload")}
                  </span>
                </div>
              </div>
            </div>
          </Dragger>
        </div>
      )}

      {/* Selected Files List */}
      {selectedFiles.length > 0 && (
        <div className="w-full max-w-2xl mt-4">
          <Card 
            size="small"
            title={
              <Space>
                <FileOutlined style={{ color: '#1890ff' }} />
                <Text strong>
                  {t("drive.selectedFiles") || "Selected Files"} ({selectedFiles.length})
                </Text>
              </Space>
            }
            extra={
              <Space>
                <Button
                  type="primary"
                  size="small"
                  icon={<UploadOutlined />}
                  onClick={handleConfirmUpload}
                  loading={isUploading}
                  disabled={selectedFiles.length === 0 || isUploading}
                >
                  {isUploading ? `${uploadProgress -1}%` : (t("drive.uploadFiles") || "Upload")}
                </Button>
                {isUploading && (
                  <Button
                    type="default"
                    size="small"
                    danger
                    icon={<CloseOutlined />}
                    onClick={cancelUpload}
                  >
                    {t("drive.cancelUpload") || "Cancel"}
                  </Button>
                )}
                {!isUploading && (
                  <Button
                    type="text"
                    size="small"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={clearFiles}
                  >
                    {t("drive.clearAll") || "Clear All"}
                  </Button>
                )}
              </Space>
            }
            style={{ 
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
              borderRadius: '8px'
            }}
          >
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {selectedFiles.map((file, index) => (
                <Card
                  key={index}
                  size="small"
                  hoverable
                  style={{
                    borderRadius: '6px',
                    border: '1px solid #f0f0f0',
                    backgroundColor: '#fafafa'
                  }}
                  styles={{
                body: {padding: '8px 12px'}
                  }}
                 
                >
                  <div className="flex items-center justify-between">
                    <Space size="small" className="flex-1 min-w-0">
                      <FileOutlined style={{ color: '#1890ff', fontSize: '14px' }} />
                      <Text 
                        ellipsis={{ tooltip: file.name }}
                        style={{ fontSize: '13px', maxWidth: '200px' }}
                      >
                        {file.name}
                      </Text>
                    </Space>
                    <Space size="small" className="flex-shrink-0">
                      <Text type="secondary" style={{ fontSize: '11px' }}>
                        {formatFileSize(file.size)}
                      </Text>
                      <Button
                        type="text"
                        size="small"
                        danger
                        icon={<CloseOutlined style={{ fontSize: '10px' }} />}
                        onClick={() => removeFile(index)}
                        style={{ 
                          width: '20px', 
                          height: '20px',
                          minWidth: '20px',
                          padding: 0,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}
                        title={t("drive.removeFile") || "Remove file"}
                      />
                    </Space>
                  </div>
                </Card>
              ))}
            </div>
          </Card>
        </div>
      )}
    </div>
  );
};

export default UploadInput; 