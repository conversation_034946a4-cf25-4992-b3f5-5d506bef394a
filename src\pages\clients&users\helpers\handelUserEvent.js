import { getUser } from "new-redux/actions/voip.actions/getUser";
import { toastNotification } from "../../../components/ToastNotification";
import { message } from "antd";

export const handleEventMercure =
  (setDataTable, userEvent, t) => (dispatch /*, getState*/) => {
    // console.log(getState());
    const {
      type_event,
      data: { success, error, users, admin, guest },
    } = userEvent;

    const displayToast = (type, message, duration) => {
      toastNotification(type, message, "topRight", duration);
    };

    if (error || !success) {
      const cause = error?.message || error;
      displayToast(
        "error",
        // t("contacts.errorMercure", { error: cause }),
        <div
          dangerouslySetInnerHTML={{
            __html: t("contacts.errorMercure", {
              error: cause,
            }),
          }}
        />,
        5
      );
      dispatch({ type: "RESET_USER_EVENT" });
      return;
    }

    const updateDataForEvent = () => {
      const updatedUsersStatus = users.reduce((acc, user) => {
        const userData =
          type_event === "user_accept_invitation"
            ? {
                etat: user?.etat,
                extensionId: user?.fields?.id,
                extensionVal: user?.fields?.value,
                invitation: user?.invitation,
                role: user?.role,
              }
            : type_event === "send_invitation"
            ? {
                etat: user?.etat,
                invitation: user?.invitation,
                role: user?.role,
              }
            : user?.etat;

        acc[user.id] = userData;
        return acc;
      }, {});
      return (prevData) =>
        prevData.map((data) => {
          const userStatus = updatedUsersStatus[data?.key];
          if (!userStatus) return { ...data };

          if (type_event === "user_accept_invitation") {
            const { etat, extensionId, extensionVal, invitation } = userStatus;
            return {
              ...data,
              invitation: invitation || data.invitation,
              status: etat || data.status,
              [extensionId]: extensionVal
                ? { value: extensionVal, fieldType: "extension" }
                : data?.[extensionId],
            };
          } else if (type_event === "send_invitation") {
            const { etat, invitation } = userStatus;
            return {
              ...data,
              invitation: invitation || data.invitation,
              status: etat || data.status,
            };
          } else {
            return { ...data, status: userStatus || data.status };
          }
        });
    };
    const formatUserNames = (users) => {
      if (!users.length) return "";

      if (users.length <= 3) {
        return users.map((user) => user.label).join(", ");
      } else {
        const displayedUsers = users.slice(0, 3).map((user) => user.label);
        const remainingCount = users.length - 3;
        return `${displayedUsers.join(", ")}, +${remainingCount}`;
      }
    };
    // console.log({ users });
    setDataTable && setDataTable(updateDataForEvent());
    message.destroy("processInviteUser");
    type_event === "user_accept_invitation"
      ? displayToast(
          "info",
          <span
            dangerouslySetInnerHTML={{
              __html: t("contacts.user_accept_invitation", {
                name: formatUserNames(users),
                element: guest ? t("contacts.guest") : t("mailing.user"),
              }),
            }}
          />,
          7
        )
      : displayToast(
          "info",
          <span
            dangerouslySetInnerHTML={{
              __html: t("contacts.successMercure", {
                element: guest ? t("contacts.guest") : t("mailing.user"),
                name: formatUserNames(users),
                action: t(`contacts.${type_event}`),
                admin_name: admin?.replaceAll("_", " "),
              }),
            }}
          />,
          7
        );
    if (
      type_event === "user_accept_invitation" ||
      type_event === "users_unblocked" ||
      type_event === "users_blocked"
    ) {
      dispatch(getUser());
    }
    dispatch({ type: "RESET_USER_EVENT" });
  };
