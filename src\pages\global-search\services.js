import { URL_ENV } from "index";
import { generateAxios } from "services/axiosInstance";

export const searchGlobal = async (
  search,
  page,
  limit = 15,
  category,
  signal
) => {
  const encodedSearch = encodeURIComponent(search);
  return await generateAxios(
    URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
  ).get(
    `/find?search=${encodedSearch}&limit=${limit}&page=${page}&category=${category}`,
    { signal }
  );
};
