import React, { useState, useEffect, useCallback, useRef } from "react";
import { Drawer, Form, Button, Tooltip, Space, Typography } from "antd";
import {
  CloseOutlined,
  MessageOutlined,
  LoadingOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import dayjs from "dayjs";
import "react-quill/dist/quill.snow.css";
import customParseFormat from "dayjs/plugin/customParseFormat";

import MainService from "../../services/main.service";
import { toastNotification } from "../../components/ToastNotification";
import CreateTaskForm from "./CreateTaskForm";
import "./style.css";
import { getName } from "../layouts/chat/utils/ConversationUtils";

import { useLocation } from "react-router-dom";
import { setMsgTask } from "../../new-redux/actions/tasks.actions/handleTaskDrawer";
import TasksRoom from "./tasksRoom";

import {
  resetStateOtherUser,
  setChatSelectedConversation,
  setChatSelectedParticipants,
} from "../../new-redux/actions/chat.actions";
import { setOpenTaskRoomDrawer } from "../../new-redux/actions/tasks.actions/handleTaskRoomDrawer";
import {
  setKpiDateVisio,
  setKpiVisio,
  updateVisio,
} from "../../new-redux/actions/visio.actions/visio";
import CreateTask from "../voip/components/CreateTask";
import { setTasksInHome } from "../../new-redux/actions/dashboard.actions";
import { URL_ENV } from "index";
import { AvatarChat } from "components/Chat";
import moment from "moment";
import { displayStatKey } from "./KpiGrid";
import useDebounce from "pages/components/UseDebounce/UseDebounce";
import { EXTENSIONS_ARRAY } from "./helpers/calculateSum";

dayjs.extend(customParseFormat);

/**
 *
 * @param {*} param0
 * @returns Tasks wrapper.
 */

const UpdateTask = ({
  id = "",
  setId = () => {},
  message = "",
  externeUpdate = false,
  setData = () => {},
  updateTaskfrom360 = () => {},
  data,
  ownersList,
  setOwnersList,
  guestsList,
  setGuestsList,
  guestsListPage,
  setGuestsListPage,
  followersListPage,
  setFollowersListPage,
  followersListLastPage,
  setFollowersListLastPage,
  setGuestsListLastPage,
  guestsListLastPage,
  followersSearchQuery,
  setFollowersSearchQuery,
  setGuestsSearchQuery,
  guestsSearchQuery,
  from = "",
  source = "task",
  activityLabel,
  open,
  setOpen,
}) => {
  const { user } = useSelector((state) => state.user);
  const debounceGuestsSearch = useDebounce(guestsSearchQuery, 500);
  const debounceFollowersSearch = useDebounce(followersSearchQuery, 500);
  const { detailsMeetExternal } = useSelector((state) => state.visioList);
  const { tasks } = useSelector((state) => state.dashboardRealTime);
  const [tasksTypes, setTasksTypes] = useState([]);
  const [pipelines, setPipelines] = useState([]);
  const usersList = useSelector((state) => state?.chat?.userList);
  const location = useLocation();
  const dispatch = useDispatch();
  const formTopRef = useRef();
  const [singleTaskData, setSingleTaskData] = useState({});
  const [propsTitle, setPropsTitle] = useState("");
  const [isError, setIsError] = useState(false);
  const [checkedItems, setCheckedItems] = useState(
    data?.guests != null ? data?.guests : []
  );
  const [checkedFollowers, setCheckedFollowers] = useState(
    data?.followers !== null ? data?.followers : []
  );
  const [tasksData, setTasksData] = useState([]);
  const [loadGuests, setLoadGuests] = useState(false);
  const [loadSpecificTask, setLoadSpecificTask] = useState(false);
  const [createAndUpdateTaskLoading, setCreateAndUpdateTaskLoading] =
    useState(false);
  const [selectedStartDate, setSelectedStartDate] = useState("");
  const [selectedEndDate, setSelectedEndDate] = useState("");
  const [selectedStartTime, setSelectedStartTime] = useState("");
  const [selectedEndTime, setSelectedEndTime] = useState("");
  const [loadOwners, setLoadOwners] = useState(false);
  const [, setIdType] = useState("");
  const [addOnsValues, setAddOnsValues] = useState({
    description: data?.description,
    note: data?.note,
  });
  const [selectedTaskType, setSelectedTaskType] = useState(null);
  const [files, setFiles] = useState([]);
  const [loadPipelines] = useState(false);
  const [selectedStageId, setSelectedStageId] = useState(null);
  const [deletedTaskIndicator, setDeletedTaskIndicator] = useState(false);
  const [checkDueDateReminder, setCheckDueDateReminder] = useState(false);
  const [openDrawer, setOpenDrawer] = useState(false);
  const [selectedFamilyMembers, setSelectedFamilyMembers] = useState([
    1, 2, 4, 9,
  ]);
  const [emailNotification, setEmailNotification] = useState(false);
  const [totalEntities, setTotalEntities] = useState({
    colleagues: 0,
    all: 0,
  });

  const [form] = Form.useForm();
  const [t] = useTranslation("common");
  const { stages, iconsTasks } = useSelector(
    (state) => state?.dashboardRealTime
  );

  const updateForm = (data) => {
    form.setFieldsValue({
      taskType: data?.tasks_type_id,
      title: data?.label,
      priority: data?.priority,
      module:
        data?.module_id != null ? data?.module_id.map((el) => Number(el)) : [],
      startDate:
        data?.start_date && selectedStartDate === ""
          ? dayjs(data?.start_date, user.location.date_format)
          : selectedStartDate !== "" &&
            dayjs(selectedStartDate, user.location.date_format),
      startTime:
        data?.start_time && selectedStartTime === ""
          ? dayjs(data?.start_time, user.location.time_format)
          : selectedStartTime !== "" &&
            dayjs(selectedStartTime, user.location.time_format),
      endDate:
        data?.end_date && selectedStartDate === ""
          ? dayjs(data?.end_date, user.location.date_format)
          : selectedStartDate !== "" &&
            dayjs(selectedStartDate, user.location.date_format),
      endTime:
        data?.end_time && selectedStartTime === ""
          ? dayjs(data?.end_time, user.location.time_format)
          : selectedStartTime !== "" &&
            dayjs(form.getFieldValue("startTime")).add(1, "hour"),
      reminder: data?.Reminder && data?.Reminder.split(" ")[0],
      addonAfter: data?.Reminder && data?.Reminder.split(" ")[1],
      stage: Number(data?.stage_id) || null,
      stage_id: Number(data?.stage_id) || null,
      owner: {
        label: (
          <>
            <AvatarChat
              fontSize="0.875rem"
              className="mr-1.5"
              size={28}
              height={17}
              width={17}
              url={`${
                URL_ENV?.REACT_APP_BASE_URL +
                URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
              }${data?.owner_id?.avatar}`}
              hasImage={EXTENSIONS_ARRAY?.includes(
                data?.owner_id?.avatar?.split(".")?.pop()
              )}
              name={getName(data?.owner_id?.label, "avatar")}
              type="user"
            />
            {getName(data?.owner_id?.label, "name")}
          </>
        ),
        value: data?.owner_id?.id,
      },

      description: data?.description,
      note: data?.note,
      upload: data?.upload,
      reminderEndDate: data?.reminder_before_end,
    });
    setAddOnsValues({ ...addOnsValues, note: data?.note });
  };

  useEffect(() => {
    setFollowersListPage(1);
  }, [debounceFollowersSearch]);
  const getOwners = useCallback(
    async (signal) => {
      setLoadOwners(true);
      try {
        let formData = new FormData();
        formData.append("family_id", 4);
        formData.append("search", debounceFollowersSearch);
        const response = await MainService.getFamilyOptions(
          followersListPage,
          50,
          formData
        );
        if (followersListPage > 1) {
          setOwnersList([...ownersList, ...response?.data?.data]);
        } else {
          setOwnersList(response?.data?.data);
        }
        setTotalEntities((prev) => ({
          ...prev,
          colleagues: response?.data?.meta?.total,
        }));
        setFollowersListLastPage(response?.data?.meta?.last_page);
        setLoadOwners(false);
      } catch (error) {
        setLoadOwners(false);
        console.log(`Error ${error}`);
        toastNotification("error", t("toasts.somethingWrong"));
      }
    },
    [followersListPage, debounceFollowersSearch]
  );

  useEffect(() => {
    let abort = new AbortController();
    // if (ownersList && ownersList?.length === 0 && followersListPage !== undefined) {
    getOwners(abort?.signal);
    // }
    return () => abort?.abort();
  }, [followersListPage, getOwners, debounceFollowersSearch]);

  const getGuests = useCallback(async () => {
    setLoadGuests(true);
    try {
      let formData = new FormData();
      formData.append("family_id", selectedFamilyMembers.toString());
      formData.append("search", debounceGuestsSearch);
      const response = await MainService.getFamilyOptions(
        guestsListPage,
        50,
        formData
      );
      if (guestsListPage > 1) {
        setGuestsList([...guestsList, ...response?.data?.data]);
      } else {
        setGuestsList(response?.data?.data);
      }
      setTotalEntities((prev) => ({
        ...prev,
        all: response?.data?.meta?.total,
      }));
      setGuestsListLastPage(response?.data?.meta?.last_page);
      setLoadGuests(false);
    } catch (error) {
      console.log(`Error ${error}`);
      toastNotification("error", t("toasts.somethingWrong"));
      setLoadGuests(false);
    }
  }, [guestsListPage, debounceGuestsSearch, selectedFamilyMembers]);

  useEffect(() => {
    let abort = new AbortController();
    // if (guestsList && guestsList?.length === 0 && guestsListPage !== undefined) {
    getGuests();
    // }
    return () => abort?.abort();
  }, [getGuests, guestsListPage, debounceGuestsSearch]);

  const getSpecificTask = useCallback(async () => {
    setLoadSpecificTask(true);

    try {
      const response = await MainService.getSpecificTask(id);
      if (response?.data?.message === "This task was deleted") {
        setDeletedTaskIndicator(true);
      } else {
        const pipelinesList = await MainService.getPipelinesByFamilyTask();
        const taskTypesList = await MainService.getTasksTypes();
        setPipelines(pipelinesList?.data?.data);
        setTasksTypes(taskTypesList?.data?.data?.tasks_type);
        setEmailNotification(response?.data?.data?.send_email);
        setAddOnsValues({ ...addOnsValues, note: response?.data?.data?.note });

        setSingleTaskData({
          ...response?.data?.data,
          upload:
            response?.data?.data.upload == 0
              ? null
              : response?.data?.data?.upload,
        });
        setCheckedItems(
          response?.data?.data?.guests != null
            ? response?.data?.data?.guests
            : checkedItems
        );
        setCheckedFollowers(
          response?.data?.data?.followers !== null
            ? response?.data?.data?.followers
            : checkedFollowers
        );
      }
      updateForm(response?.data?.data);
      if (!loadOwners) {
        setTimeout(() => {
          setLoadSpecificTask(false);
        }, 1000);
      }
    } catch (error) {
      console.log(`Error ${error}`);
      setLoadSpecificTask(false);
    }
  }, [id, loadOwners]);
  useEffect(() => {
    if (from === "home") {
      getSpecificTask();
    } else {
      if (externeUpdate) {
        setSingleTaskData({
          ...detailsMeetExternal,
          upload:
            detailsMeetExternal?.upload == 0
              ? null
              : detailsMeetExternal?.files,
        });
        setCheckedItems(
          detailsMeetExternal?.guests != null
            ? detailsMeetExternal?.guests
            : checkedItems
        );
        setCheckedFollowers(
          detailsMeetExternal?.followers !== null
            ? detailsMeetExternal?.followers
            : checkedFollowers
        );
        updateForm(detailsMeetExternal);

        setAddOnsValues({ ...addOnsValues, note: detailsMeetExternal?.note });
      } else {
        setSingleTaskData({
          ...data,
          upload:
            data?.upload == 0 || data.upload == null
              ? null
              : Array.isArray(data?.upload)
              ? data.upload
              : data?.files,
        });
        setCheckedItems(data?.guests != null ? data?.guests : checkedItems);
        setCheckedFollowers(
          data?.followers !== null ? data?.followers : checkedFollowers
        );
        updateForm(data);

        setAddOnsValues({ ...addOnsValues, note: data?.note });
      }
    }
  }, [detailsMeetExternal, id, externeUpdate]);

  const prefillTaskLabel = (value) => {
    let index =
      tasksTypes && tasksTypes.findIndex((element) => element?.id == value);

    if (index > -1 && propsTitle === "") {
      form.setFieldsValue({
        title: tasksTypes && tasksTypes[index]?.label,
      });
    }
  };

  const resetDrawerForm = () => {
    setSingleTaskData({});
    setCheckedItems([]);
    setCheckedFollowers([]);
    setId("");
    setData({});
    form.resetFields();
    setGuestsSearchQuery("");
    setFollowersSearchQuery("");
    form.setFieldsValue({});
    setSelectedStartDate("");
    setSelectedEndDate("");
    setSelectedStartTime("");
    setSelectedEndTime("");
    setAddOnsValues({
      description: "",
      note: "",
    });
    setFiles([]);
    setCheckDueDateReminder(false);
    setPropsTitle("");
    formTopRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  // Update task API.
  const updateTask = async (taskId, payload) => {
    try {
      setCreateAndUpdateTaskLoading(true);
      const response = await MainService.updateTask(taskId, payload);
      if (response?.status === 200) {
        let itemIndex =
          tasksData && tasksData.findIndex((element) => element?.id === taskId);
        tasksData && tasksData.filter((task) => task?.id !== taskId);
        let newArray =
          tasksData &&
          tasksData.map((element, i) =>
            i === itemIndex
              ? {
                  ...response?.data?.data,
                  key: response?.data?.data?.id,
                  created_at: element.created_at,
                }
              : element
          );
        if (from === "home") {
          dispatch(
            setTasksInHome({
              tasks: tasks.map((el) =>
                el.id === response.data.data.id
                  ? {
                      ...response?.data?.data,
                      key: response?.data?.data?.id,
                    }
                  : el
              ),
              stages: stages,
              iconsTasks: iconsTasks,
            })
          );
          dispatch(
            updateVisio({
              data: response.data,
              t,
              lastStartDate: singleTaskData?.start_date,
            })
          );
          setData(response.data.data);
          updateTaskfrom360({
            ...response.data.data,
            created_at: response?.data?.data?.create_at,
          });
        } else {
          setTasksData(newArray);
          updateTaskfrom360({
            ...response.data.data,
            created_at: response?.data?.data?.create_at,
          });

          dispatch(
            updateVisio({
              data: response.data,
              t,
              lastStartDate: singleTaskData?.start_date,
            })
          );
        }
        setCreateAndUpdateTaskLoading(false);
        setOpen(false);
        resetDrawerForm();
        toastNotification(
          "success",
          "Task updated successfully",
          "bottomRight"
        );
        setCheckedItems([]);
      }
    } catch (error) {
      setCreateAndUpdateTaskLoading(false);
      toastNotification("error", t("toasts.somethingWrong"));
      console.log(`Error ${error}`);
    }
  };

  const onFinish = async (values) => {
    let formData = new FormData();
    const allFields = form.getFieldsValue(true);

    formData.append("label", values?.title);
    formData.append("priority", allFields?.priority ? allFields?.priority : "");
    formData.append(
      "Reminder",
      values?.reminder && values?.addonAfter
        ? `${values?.reminder} ${values?.addonAfter}`
        : ""
    );
    formData.append(
      "start_date",
      values?.startDate
        ? dayjs(values?.startDate).format(user.location.date_format)
        : ""
    );
    formData.append(
      "start_time",
      values?.startTime
        ? dayjs(values?.startTime)
            .format(user.location.time_format)
            .toUpperCase()
        : ""
    );
    formData.append(
      "end_time",
      values?.endTime
        ? dayjs(values?.endTime).format(user.location.time_format).toUpperCase()
        : ""
    );
    formData.append(
      "end_date",
      values?.endDate
        ? dayjs(values?.endDate).format(user.location.date_format)
        : ""
    );
    formData.append(
      "owner_id",
      typeof values?.owner === "object" ? values?.owner?.value : values?.owner
    );
    if (values.exam_id) {
      values.exam_id.forEach((id) => {
        formData.append("exam_id[]", id);
      });
    }
    formData.append("department_id", values?.department_id);
    formData.append("code", values?.code);
    formData.append("send_email", emailNotification ? 1 : 0);
    allFields?.family && formData.append("family_id", allFields?.family);
    allFields?.relatedElement &&
      formData.append("element_id", allFields?.relatedElement);

    let result =
      usersList &&
      usersList.filter(
        (user) =>
          checkedItems && checkedItems.some((list) => user?.id === list?.id)
      );
    let filteredArray =
      checkedItems && checkedItems.filter((el) => typeof el?.id === "string");
    let allTogetherArray = [...result, ...filteredArray];
    location.pathname === "/chat"
      ? allTogetherArray &&
        allTogetherArray.forEach((el) =>
          typeof el?.id === "string"
            ? formData.append("guests[]", JSON.stringify({ mid: el?.id }))
            : formData.append("guests[]", JSON.stringify({ uid: el?.uuid }))
        )
      : checkedItems &&
        checkedItems.forEach((el) => formData.append("guests[]", el?.id));
    checkedFollowers &&
      checkedFollowers.forEach((el) => formData.append("followers[]", el?.id));
    formData.append("tasks_type_id", source === "visio" ? 3 : values?.taskType);
    formData.append(
      "description",
      addOnsValues?.description ? addOnsValues?.description : ""
    );
    formData.append("note", addOnsValues?.note ? addOnsValues?.note : "");

    formData.append("stage_id", values?.stage ? values?.stage : "");
    formData.append("reminder_before_end", checkDueDateReminder ? 1 : 0);

    selectedTaskType === 1 && formData.append("location", values?.location);

    files &&
      files.length > 0 &&
      files.forEach((file) => formData.append("upload[]", file?.id));

    message !== "" && formData.append("source", 1);

    if (Object.keys(singleTaskData).length === 0) {
      CreateTask(formData);
    } else {
      updateTask(id, formData);
    }
    if (window.location.pathname.includes("visio")) {
      let kpi = { data: [] };
      let kpiDate = { data: [] };
      const currentDate = moment().format(user.location.date_format);
      const start_date = moment(values.startDate, user.location.date_format);

      kpiDate = await MainService.getKpiDateVisio();
      kpi = await MainService.getKpiVisio(
        currentDate === values.startDate
          ? 1
          : start_date.isAfter(moment(), "day")
          ? 2
          : 0
      );

      dispatch(
        setKpiVisio(
          kpi &&
            kpi.data && [
              ...Object.entries(kpi.data).map(([key, value]) => {
                return {
                  title: displayStatKey(t, key, ""),
                  value: value,
                  tr: false,
                };
              }),
            ]
        )
      );
      dispatch(
        setKpiDateVisio(
          kpiDate &&
            kpiDate.data &&
            Object.entries(kpiDate.data).map(([key, value]) => {
              return { title: key, value: value, tr: true };
            })
        )
      );
    }

    dispatch(setMsgTask(""));
  };

  useEffect(() => {
    if (propsTitle !== "") {
      form.setFieldValue({
        title: propsTitle,
      });
    }
  }, [form, propsTitle]);

  useEffect(() => {
    if (
      id !== null &&
      open === true &&
      Object.keys(singleTaskData).length !== 0
    ) {
      form.setFieldsValue({
        startDate:
          data?.start_date && selectedStartDate === ""
            ? dayjs(data?.start_date, user.location.date_format)
            : selectedStartDate !== "" &&
              dayjs(selectedStartDate, user.location.date_format),
        startTime:
          data?.start_time && selectedStartTime === ""
            ? dayjs(data?.start_time, user.location.time_format)
            : selectedStartTime !== "" &&
              dayjs(selectedStartTime, user.location.time_format),
        endDate:
          data?.end_date && selectedStartDate === ""
            ? dayjs(data?.end_date, user.location.date_format)
            : selectedStartDate !== "" &&
              dayjs(selectedStartDate, user.location.date_format),
        endTime:
          data?.end_time && selectedStartTime === ""
            ? dayjs(data?.end_time, user.location.time_format)
            : selectedStartTime !== "" &&
              dayjs(form.getFieldValue("startTime")).add(1, "hour"),
      });
    }
  }, [id, open, singleTaskData, selectedStartDate, selectedStartTime]);

  const ownersOptionsInSelect = ownersList
    .sort((a, b) => a?.label?.localeCompare(b?.label))
    .map((element) => ({
      label: (
        <>
          <AvatarChat
            fontSize="0.875rem"
            className="mr-1.5"
            size={28}
            height={17}
            width={17}
            url={`${
              URL_ENV?.REACT_APP_BASE_URL + URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
            }${element?.avatar}`}
            hasImage={EXTENSIONS_ARRAY?.includes(
              element?.avatar?.split(".")?.pop()
            )}
            name={getName(element?.label, "avatar")}
            type="user"
          />
          {getName(element?.label, "name")}
        </>
      ),
      value: element?.id,
    }));

  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e?.key === "Enter") {
        form.submit();
      }
    };

    window.addEventListener("keydown", handleKeyDown);

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [form]);

  return (
    <>
      {openDrawer ? (
        <TasksRoom key={id} elementId={id} canCreateRoom={1} />
      ) : (
        ""
      )}

      <Drawer
        title={activityLabel}
        footer={
          <Form.Item>
            <div className="flex items-center justify-between">
              <Space>
                <Button
                  htmlType="reset"
                  onClick={() => {
                    setOpen(false);
                    dispatch(setMsgTask(""));
                    setDeletedTaskIndicator(false);
                    setSelectedStageId(null);
                    setId(null);
                  }}
                >
                  {t("fields_management.drawerCloseBtn")}
                </Button>
                <Button
                  type="primary"
                  form="form"
                  loading={createAndUpdateTaskLoading}
                  disabled={deletedTaskIndicator}
                  onClick={() => form.submit()}
                >
                  {t("fields_management.drawerOkBtn")}
                </Button>
              </Space>
              <Typography.Text
                type="secondary"
                style={{ display: "flex", alignItems: "center" }}
              >
                {source === "visio"
                  ? t("tasks.drawerHeaderVisioOne")
                  : t("tasks.drawerHeaderOne")}{" "}
                {loadSpecificTask ? (
                  <LoadingOutlined className="ml-1" />
                ) : (
                  <Tooltip
                    title={getName(singleTaskData?.creator?.label, "name")}
                  >
                    <div className="">
                      <AvatarChat
                        className={"mx-1.5 flex items-center justify-center"}
                        fontSize={"0.875rem"}
                        height={"32px"}
                        width={"32px"}
                        url={`${
                          URL_ENV?.REACT_APP_BASE_URL +
                          URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                        }${singleTaskData?.creator?.avatar}`}
                        hasImage={EXTENSIONS_ARRAY?.includes(
                          singleTaskData?.creator?.avatar?.split(".")?.pop()
                        )}
                        name={getName(singleTaskData?.creator?.label, "avatar")}
                        type="user"
                      />
                    </div>
                  </Tooltip>
                )}{" "}
                {loadSpecificTask ? null : (
                  <Typography.Text mark>
                    {moment(singleTaskData?.create_at).format(
                      `${user?.location?.date_format} ${user?.location?.time_format}`
                    )}
                  </Typography.Text>
                )}
              </Typography.Text>
            </div>
          </Form.Item>
        }
        className="tasks-drawer"
        size="large"
        placement="right"
        afterOpenChange={(open) => {
          if (open === false) {
            setOpen(false);
            resetDrawerForm();
          }
          setDeletedTaskIndicator(false);
        }}
        closeIcon={
          <CloseOutlined
            onClick={(e) => {
              e.stopPropagation();
              setOpen(false);
              resetDrawerForm();
              setId("");
              setDeletedTaskIndicator(false);
              dispatch(setMsgTask(""));
            }}
          />
        }
        extra={
          Object.keys(singleTaskData).length > 0 && (
            <Tooltip
              title={
                singleTaskData?.room_id !== 0
                  ? t("tasks.openChatRoom")
                  : t("tasks.createChatRoom")
              }
            >
              <Button
                type="text"
                shape="circle"
                icon={<MessageOutlined />}
                onClick={async () => {
                  setOpenDrawer(true);
                  dispatch(setOpenTaskRoomDrawer(true));

                  let usr_ids_arr = [];

                  let guestsIds =
                    Object.keys(singleTaskData).length > 0 &&
                    singleTaskData?.guests &&
                    Object.keys(singleTaskData).length > 0 &&
                    singleTaskData?.guests.map((guest) =>
                      guest?.uuid ? guest?.uuid : null
                    );
                  let followersIds =
                    Object.keys(singleTaskData).length > 0 &&
                    singleTaskData?.followers &&
                    Object.keys(singleTaskData).length > 0 &&
                    singleTaskData?.followers.map((follower) =>
                      follower?.uuid ? follower?.uuid : null
                    );

                  usr_ids_arr = [
                    ...guestsIds,
                    ...followersIds,
                    singleTaskData?.creator?.id !== singleTaskData?.owner_id?.id
                      ? singleTaskData?.owner_id?.uuid
                      : null,
                  ];

                  singleTaskData?.followers?.forEach((follower) => {
                    if (!Array.isArray(follower?.uuid)) {
                      usr_ids_arr.push(follower?.uuid);
                    }
                  });

                  singleTaskData?.guests?.forEach((guest) => {
                    if (!Array.isArray(guest?.uuid)) {
                      usr_ids_arr.push(guest?.uuid);
                    }
                  });
                  usr_ids_arr = [...new Set(usr_ids_arr)]
                    .filter((id) => id !== null)
                    .filter((el) => el !== singleTaskData?.creator?.uuid);

                  await MainService.createRoomTask("tasks", id)
                    .then((res) => {
                      if (!res?.data?.message) {
                        MainService.assignRoomToTask(id, {
                          room_id: res?.data?.room?._id,
                        })
                          .then((res) => {})
                          .catch((err) => {
                            console.log("ASSIGN ROOM TO TASK ERROR", err);
                          });
                      }
                      dispatch(
                        resetStateOtherUser({
                          forced: true,
                          keepDrawerOpened: false,
                          item: null,
                        })
                      );
                      dispatch(
                        setChatSelectedConversation({
                          selectedConversation: {
                            name: res?.data?.data?.room?.name,
                            description: res?.data?.data?.room?.description,
                            image: res?.data?.data?.room?.image,
                            admin_id: res?.data?.data?.room?.admin_id,
                            bot: null,
                            id: res?.data?.data?.room?._id,
                            type: "room",
                            source: "task",

                            mode: "members",
                            muted_status: false,
                            conversationId:
                              res?.data?.data?.room?.conversation_id,
                            external: false,
                          },
                        })
                      );
                      dispatch(
                        setChatSelectedParticipants({
                          selectedParticipants:
                            res?.data?.data?.room?.participants,
                        })
                      );
                    })
                    .catch((err) => {
                      console.log("ROOM TASK ERROR", err);
                    });
                }}
                disabled={!singleTaskData?.can_create_room}
              />
            </Tooltip>
          )
        }
        open={open}
      >
        <div ref={formTopRef}></div>
        <CreateTaskForm
          form={form}
          onFinish={onFinish}
          loadSpecificTask={loadSpecificTask}
          prefillTaskLabel={prefillTaskLabel}
          tasksTypes={tasksTypes}
          singleTaskData={singleTaskData}
          selectedStartDate={selectedStartDate}
          setSelectedStartDate={setSelectedStartDate}
          setSelectedStartTime={setSelectedStartTime}
          selectedEndTime={selectedEndTime}
          setSelectedEndTime={setSelectedEndTime}
          selectedEndDate={selectedEndDate}
          selectedStartTime={selectedStartTime}
          setSelectedEndDate={setSelectedEndDate}
          ownersOptionsInSelect={ownersOptionsInSelect}
          loadOwners={loadOwners}
          checkedItems={checkedItems}
          guestsSearchQuery={guestsSearchQuery}
          setGuestsSearchQuery={setGuestsSearchQuery}
          checkedFollowers={checkedFollowers}
          source={source}
          setIdType={setIdType}
          addOnsValues={addOnsValues}
          setAddOnsValues={setAddOnsValues}
          selectedTaskType={selectedTaskType}
          setSelectedTaskType={setSelectedTaskType}
          files={files}
          setFiles={setFiles}
          id={id}
          taskToUpdate={id}
          pipelines={pipelines}
          loadPipelines={loadPipelines}
          selectedStageId={selectedStageId}
          guestsList={guestsList}
          ownersList={ownersList}
          setCheckedFollowers={setCheckedFollowers}
          setCheckedItems={setCheckedItems}
          guestsListPage={guestsListPage}
          followersListPage={followersListPage}
          setGuestsListPage={setGuestsListPage}
          setFollowersListPage={setFollowersListPage}
          followersSearchQuery={followersSearchQuery}
          setFollowersSearchQuery={setFollowersSearchQuery}
          setPropsTitle={setPropsTitle}
          deletedTaskIndicator={deletedTaskIndicator}
          setCheckDueDateReminder={setCheckDueDateReminder}
          guestsListLastPage={guestsListLastPage}
          followersListLastPage={followersListLastPage}
          setSelectedFamilyMembers={setSelectedFamilyMembers}
          setGuestsList={setGuestsList}
          setEmailNotification={setEmailNotification}
          loadGuests={loadGuests}
          setIsError={setIsError}
          isError={isError}
          totalEntities={totalEntities}
        />
      </Drawer>
    </>
  );
};

export default UpdateTask;
