import CardStat from "pages/components/CardStat";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { useLocation, useParams } from "react-router-dom";
import MainService from "services/main.service";
import "./mailing.css";
const KPI = ({ dataAccounts }) => {
  const [kpiMail, setKPIMail] = useState({});
  const [loadingKPI, setLoadingKPI] = useState(false);
  const { accountId } = useParams();
  const location = useLocation();
  const { refreshKPI } = useSelector((state) => state.mailReducer);
  const [t] = useTranslation("common");
  const usedAccount = useMemo(
    () => dataAccounts?.find((item) => item?.selected),
    [dataAccounts]
  );

  const getKPIByUser = useCallback(async () => {
    if (
      location.pathname !== `/mailing/${accountId}/inbox` ||
      usedAccount?.shared == 0
    )
      return;
    try {
      const response = await MainService.getKPIByUser(accountId);
      if (response.status === 200) {
        setKPIMail(response?.data?.data);
        setLoadingKPI(false);
      }
    } catch (err) {
      console.log(err);
    }
  }, [accountId, refreshKPI]);

  useEffect(() => {
    getKPIByUser();
  }, [getKPIByUser]);

  return (
    <div className="ml-8  flex w-24 space-x-4">
      {Object.entries(kpiMail)?.length > 0 &&
        usedAccount?.shared != 0 &&
        Object.entries(kpiMail)
          // .filter(
          //   ([key]) =>
          //     key !== "Total_assigned_emails" &&
          //     key !== "Assigned_emails_closed"
          // )
          .map(([key, subject], i) => {
            return (
              <div className="custom-card-height" key={i}>
                <CardStat
                  key={i}
                  // className="custom-card-height"
                  item={{
                    title: (
                      <span className="small-text uppercase">
                        {key === "Total_unassigned_emails"
                          ? t("mailing.notAssigned")
                          : key === "Assigned_emails_closed"
                          ? t("mailing.closed")
                          : key === "Assigned_emails_inProgress"
                          ? t("mailing.InProgress")
                          : key === "Assigned_emails_to_process"
                          ? t("mailing.PROCESS")
                          : key === "Total_assigned_emails"
                          ? t("mailing.Assigned")
                          : t("mailing.outOfTime")}
                      </span>
                    ),
                    key: key,
                    value: subject,
                  }}
                />
              </div>
            );
          })}
    </div>
  );
};

export default KPI;
