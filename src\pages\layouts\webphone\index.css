/* .autoComplete-input-call .ant-select-dropdown .ant-select-item-option-grouped {
    padding-inline-start: 10 !important;
} */

.autoComplete-input-call .ant-select-item.ant-select-item-option.ant-select-item-option-grouped {
    padding-inline-start: 5px !important;
}

.centered-input {
    width: 60%;
    text-align: center;
    border: none;
    outline: none;
    font-size: 16px;
    background-color: rgb(241, 245, 249);
    /* background-color: white */

}

.scroll_bar ::-webkit-scrollbar {
    width: 6px;
    
}

.scroll_bar ::-webkit-scrollbar-thumb {
    background: rgb(226 232 240); 
    border-radius: 0px;
}

.scroll_bar ::-webkit-scrollbar-track {
    background-color:rgb(241 245 249);
    border-radius: 0px;
}

.scroll_bar ::-webkit-scrollbar-thumb:hover {
    background: rgb(203 213 225);
    border-radius: 0px;
    
  }

  