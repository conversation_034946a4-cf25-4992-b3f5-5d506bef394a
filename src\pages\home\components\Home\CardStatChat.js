import { Col, Row } from "antd";
import React, { useMemo } from "react";
import CardWithGraph from "../CardWithGraph";
import { GoTo } from "pages/home/<USER>";
import {
  ArchiveBoxIcon,
  AtSymbolIcon,
  ChatBubbleBottomCenterTextIcon,
  ChatBubbleLeftRightIcon,
} from "@heroicons/react/24/outline";
import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";

const CardStatChat = () => {
  const [t] = useTranslation("common");
  const navigate = useNavigate();
  const {
    unreadMsgOneToOne,
    unreadMessagesInGroups,
    unreadArchivedMessages,
    totalunreadMessagesTags,
  } = useSelector((state) => state.dashboardRealTime);
  const { user } = useSelector((state) => state.user);
  const chartDataChat = useMemo(
    () => ({
      data: [
        {
          name: t(`dashboard.unreadmsgOtoO`, {
            plural: unreadMsgOneToOne > 1 ? "s" : "",
            pluriel: unreadMsgOneToOne > 1 ? "x" : "",
          }),
          y: Number(unreadMsgOneToOne),
          color: "#7cb5ec",
        },
        {
          name: t(`dashboard.unreadmsgtoG`, {
            plural: unreadMessagesInGroups > 1 ? "s" : "",
            pluriel: unreadMessagesInGroups > 1 ? "x" : "",
          }),
          y: unreadMessagesInGroups,
          color: "#309898",
        },
        {
          name: t(`dashboard.unreadArchivedMessages`, {
            plural: unreadArchivedMessages > 1 ? "s" : "",
            pluriel: unreadArchivedMessages > 1 ? "x" : "",
          }),
          y: unreadArchivedMessages,
          color: "#9ca3af",
        },
        {
          name: t(`dashboard.unreadMention`, {
            plural: totalunreadMessagesTags > 1 ? "s" : "",
            pluriel: totalunreadMessagesTags > 1 ? "x" : "",
          }),
          y: totalunreadMessagesTags,
          color: "#f87171 ",
        },
      ],
      name: "",
    }),
    [
      unreadMsgOneToOne,
      unreadMessagesInGroups,
      unreadArchivedMessages,
      totalunreadMessagesTags,
      t,
    ]
  );

  return (
    <Row gutter={[8, 8]}>
      <Col span={24}>
        <CardWithGraph
          title={
            <div className="flex items-center justify-between">
              {" "}
              {t("menu1.chat") + " " + t("tasks.unread").toLowerCase()} &nbsp;
              <GoTo
                to={"5"}
                title={t("menu1.chat")}
                navigate={navigate}
                t={t}
                user={user}
              />
            </div>
          }
          stats={[
            {
              icon: (
                <ChatBubbleBottomCenterTextIcon className="h-[22px] w-[22px] text-[#68abea]" />
              ),
              name: t(`dashboard.unreadmsgOtoO`, {
                plural: unreadMsgOneToOne > 1 ? "s" : "",
                pluriel: unreadMsgOneToOne > 1 ? "x" : "",
              }),
              number: unreadMsgOneToOne,
            },
            {
              icon: (
                <ChatBubbleLeftRightIcon className="h-[22px] w-[22px] text-[#309898]" />
              ),
              name: t(`dashboard.unreadmsgtoG`, {
                plural: unreadMessagesInGroups > 1 ? "s" : "",
                pluriel: unreadMessagesInGroups > 1 ? "x" : "",
              }),
              number: unreadMessagesInGroups,
            },
            {
              icon: (
                <ArchiveBoxIcon className="h-[22px] w-[22px] text-gray-400" />
              ),
              name: t(`dashboard.unreadArchivedMessages`, {
                plural: unreadArchivedMessages > 1 ? "s" : "",
                pluriel: unreadArchivedMessages > 1 ? "x" : "",
              }),
              number: unreadArchivedMessages,
            },
            {
              icon: (
                <AtSymbolIcon className="h-[22px] w-[22px] text-red-400 " />
              ),
              name: t(`dashboard.unreadMention`, {
                plural: totalunreadMessagesTags > 1 ? "s" : "",
                pluriel: totalunreadMessagesTags > 1 ? "x" : "",
              }),
              number: totalunreadMessagesTags,
            },
          ]}
          chartData={chartDataChat}
        />
      </Col>
    </Row>
  );
};

export default CardStatChat;
