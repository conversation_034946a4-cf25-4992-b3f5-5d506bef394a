import { RESET_STATE, SET_USER_INFOS, SET_TOURS_ACCESS } from "../constants";

const initialState = {
  user: "",
};

const user = (state = initialState, action) => {
  const { type, payload } = action;
  switch (type) {
    case SET_USER_INFOS:
      return {
        ...state,
        user: payload,
      };
    case SET_TOURS_ACCESS:
      return {
        ...state,
        user: { ...(state?.user || {}), toursAccess: payload },
      };

    case RESET_STATE: {
      return initialState;
    }
    default:
      return state;
  }
};

export default user;
