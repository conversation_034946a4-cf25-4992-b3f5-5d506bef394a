antd=> ^5.3.1



 //"react-modal-image": "^2.6.0",
   // "react-notifications": "^1.7.2",
    //"react-phone-input-2": "^2.15.1",
    //"react-placeholder": "^4.0.3",
     //"nprogress": "^0.2.0",

             // "@ant-design/charts": "^1.4.2",
   // "@ant-design/compatible": "^1.0.8",
    //"@ant-design/plots": "^1.2.5",
    //"@ctrl/tinycolor": "^3.6.0",
    //"algoliasearch": "^4.8.5",
  
    //"body-parser": "^1.20.1",
   //"connected-react-router": "^6.8.0",

    //"d3-scale": "^3.2.3",
   // "mic-recorder-to-mp3": "^2.2.2",
   // "react-big-calendar": "^0.30.0",
    //"react-hook-use-state": "^1.1.0",
    // "react-image-crop": "9.1.1",
    // "react-image-lightbox": "^5.1.4",
       //"react-slick": "^0.27.14",
    //"react-star-rating-component": "^1.4.1",
   // "react-table": "^7.8.0",
   //"recharts": "^2.0.3",
   // "slick-carousel": "^1.8.1",
       //"url-search-params": "^1.1.0",
    //"react-simple-maps": "^2.3.0",


//    "react-custom-scrollbars": "^4.2.1",
//