import React, { useEffect, useRef } from "react";
import { Form, Input, Space, Select, Tag } from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { CaretRightOutlined } from "@ant-design/icons";
import { useParams } from "react-router-dom";
import { toastNotification } from "./ToastNotification";
import NewTableDraggable from "./NewTableDraggable";
import { generateAxios } from "../services/axiosInstance";
import BottomButtonAddRow from "./BottomButtonAddRow";
import Header from "./configurationHelpDesk/Header";
import { useDispatch, useSelector } from "react-redux";
import { setSearch } from "../new-redux/actions/menu.actions/menu";
import { SubmitKeyPress } from "../utils/SubmitKeyPress";
import { URL_ENV } from "index";
import { familyIcons } from "pages/components/DetailsProfile/ViewSphere2";
import { setNewInteraction } from "new-redux/actions/vue360.actions/vue360";
import MainService from "services/main.service";

const Pipeline = ({
  setEditingKey,
  editingKey,
  setIdPipeline,
  pipeline_id,
  setEditingKeyStage,
  items,
  setDataStage,
  loading,
  setLoading,
  setDisabledAddStage,
  data,
  setData,
  keyTab,
  id,
  setId,
  setCount,
  handleAdd,
  form,
  from = "",
}) => {
  const [rank, setRank] = useState(null);
  const { id: family } = useParams();
  const [selectedRowKey, setSelectedRowKey] = useState("");
  const [departments, setDepartments] = useState([]);
  const [columnWidth, setColumnWidth] = useState(0);
  const colRef = useRef(null);
  const isEditing = (record) => record.key === editingKey;
  const [t] = useTranslation("common");
  const [dataSaved, setDataSaved] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSIze, setPageSize] = useState(20);
  const inputRefs = useRef([]);
  const [filter, setFilter] = useState({ selected: [] });
  const { search } = useSelector((state) => state.form);
  const { user } = useSelector((state) => state.user);

  const dispatch = useDispatch();
  const { contactHeaderInfo: contactInfo } = useSelector(
    (state) => state?.contacts
  );
  const { contactInfoFromDrawer, openView360InDrawer } = useSelector(
    (state) => state?.vue360
  );

  useEffect(() => {
    const getPipelines = async () => {
      setLoading(true);
      try {
        let res;
        if (from === "task") {
          res = await MainService.getPipelinesByFamilyTask();
        } else {
          res = await MainService.getPipelinesByFamily(
            keyTab || items.find((el) => el.label === family)?.key
          );
        }
        const dep = await MainService.getDepartments();
        setDepartments(
          dep.data.data.map((el) => ({
            label: el.label,
            value: el.id,
            color: el.color,
          }))
        );
        setData(
          res.data.data.map((el, i) => ({ ...el, key: el.id, rank: i + 1 }))
        );
        setDataSaved(res.data.data);
        if (res.data.data.length > 0) {
          setCount(Math.max(...data.map((el) => el.id)));
          if (openView360InDrawer && contactInfoFromDrawer?.pipeline) {
            setIdPipeline(contactInfoFromDrawer?.pipeline);
            setDisabledAddStage(
              res.data.data.find(
                (el) =>
                  Number(el.id) === Number(contactInfoFromDrawer?.pipeline)
              )?.system == 1
                ? true
                : false
            );
            setSelectedRowKey(contactInfoFromDrawer?.pipeline);
          } else if (!openView360InDrawer && contactInfo?.pipeline) {
            setIdPipeline(contactInfo?.pipeline);
            setDisabledAddStage(
              res.data.data.find(
                (el) => Number(el.id) === Number(contactInfo?.pipeline)
              )?.system == 1
                ? true
                : false
            );
            setSelectedRowKey(contactInfo?.pipeline);
          } else {
            setIdPipeline(res.data.data[0].id);
            setDisabledAddStage(res.data.data[0]?.system == 1 ? true : false);
            setSelectedRowKey(res.data.data[0]?.id);
          }
        } else {
          setIdPipeline("");
        }
        setLoading(false);
      } catch (err) {
        setLoading(false);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");

        console.log(err);
      }
    };
    getPipelines();
    return () => dispatch(setSearch(""));
  }, [
    family,
    openView360InDrawer,
    contactInfo?.pipeline,
    contactInfoFromDrawer?.pipeline,
  ]);

  useEffect(() => {
    inputRefs.current.forEach((input) => {
      input?.focus();
    });
  }, [data.length, id]);
  useEffect(() => {
    if (colRef.current) {
      setColumnWidth(colRef.current.offsetWidth);
    }
  }, [colRef.current]);

  const onFinishFailed = (values) => {
    console.log(values);
  };
  const handleKeyPress = (event) => {
    if (form) SubmitKeyPress(event, form);
  };

  const handleClick = (event) => {
    event.stopPropagation();
  };
  const EditableCell = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    const inputNode =
      inputType === "select" ? (
        <Select
          placeholder={t("services.selectdepartment")}
          mode="multiple"
          options={departments}
          showSearch
          filterOption={(input, option) =>
            (option?.label?.toLowerCase() ?? "").includes(input.toLowerCase())
          }
        />
      ) : (
        <Input
          ref={(el) => (inputRefs.current[index] = el)}
          onKeyPress={handleKeyPress}
          placeholder={t("pipeline.pipelineName")}
          onClick={handleClick}
          autoFocus={true}
        />
      );

    return (
      <td {...restProps}>
        {editing ? (
          <Form.Item
            name={dataIndex}
            style={{
              margin: 0,
            }}
            rules={[
              {
                required: dataIndex === "label" ? true : false,
                message: `${
                  t("pipeline.pipelineName") +
                  " " +
                  t("table.header.isrequired")
                }`,
              },
            ]}
          >
            {inputNode}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };
  const edit = (record) => {
    if (record) {
      form.setFieldsValue({
        label: record.label,
        rank: record.rank,
        department: record.departments.map((el) => el.id),
      });
      setRank(record.rank);
      setId(record.id);
    } else {
      form.setFieldsValue({
        label: "",
        department: [],
      });
    }
    setEditingKey(record.key);
  };
  const cancel = (record) => {
    setEditingKey("");
    setId(null);
    if (!record.id) {
      setData(data.filter((item) => item.key !== record.key));
    }
  };
  const save = async (values) => {
    const row = await form.validateFields();
    setLoading(true);
    if (id) {
      const config = {
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      };

      setLoading(true);
      try {
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).put(`pipelines/${id}`, { ...row, rank }, config);
        setData((prev) =>
          prev.map((el) =>
            el.id == id ? { ...res.data.data, key: el.key } : el
          )
        );
        setEditingKey("");
        setRank(null);

        if (openView360InDrawer && contactInfoFromDrawer?.pipeline === id) {
          dispatch(
            setNewInteraction({ type: "updateStageFromDrawer", user: user?.id })
          );
        } else if (contactInfo?.pipeline === id) {
          dispatch(setNewInteraction({ type: "updateStage", user: user?.id }));
        }
        setLoading(false);

        toastNotification("success", row.label + t("toasts.edit"), "topRight");
      } catch (err) {
        console.log(err);
        setLoading(false);

        if (err.response.request.status === 422) {
          if (
            err?.response?.data?.message === "Pipeline label already exists"
          ) {
            toastNotification(
              "error",
              t("toasts.labelExist", {
                label: t("pipeline.pipelineName"),
              }),
              "topRight"
            );
          }
        } else {
          toastNotification("error", t("toasts.somethingWrong"), "topRight");
        }
      }
    } else {
      try {
        let payload = {};

        if (from === "task") {
          payload = {
            ...row,
            module_system: "task",
            rank: dataSaved.length + 1,
          };
        } else {
          payload = {
            ...row,
            family_id: keyTab,
            rank: dataSaved.length + 1,
          };
        }
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post("/pipelines", payload);
        setEditingKey("");
        setData([
          ...data.filter((el) => el.id),
          { ...res.data.message, key: res.data.message.id, system: false },
        ]);

        setDataSaved([
          ...data.filter((el) => el.id),
          { ...res.data.message, key: res.data.message.id, system: false },
        ]);
        form.setFieldsValue({
          label: "",
          department: [],
        });
        setDataStage([]);
        setLoading(false);
        setDisabledAddStage(false);
        setSelectedRowKey(res.data.message.id);
        onSelectChange([res.data.message.id]);
        toastNotification(
          "success",
          row.label + t("toasts.created"),
          "topRight"
        );
      } catch (err) {
        console.log(err);

        setLoading(false);
        if (err.response.request.status === 422) {
          if (
            err?.response?.data?.message === "Pipeline label already exists"
          ) {
            toastNotification(
              "error",
              t("toasts.labelExist", {
                label: t("pipeline.pipelineName"),
              }),
              "topRight"
            );
          }
        } else {
          toastNotification("error", t("toasts.somethingWrong"), "topRight");
        }
      }
    }
  };

  const columns = [
    {
      title: t("activities.name"),
      dataIndex: "label",
      key: "label",
      editable: true,
      width: "45%",
      sorter: (a, b) => a.label.localeCompare(b.label),
      render: (_, record) => (
        <div className="flex justify-between" ref={colRef}>
          <p
            className=" truncate font-semibold"
            style={{ width: columnWidth - 10 }}
          >
            {record.label}
          </p>
        </div>
      ),
    },
    {
      title: t("table.header.department"),
      dataIndex: "department",
      key: "department",
      width: "55%",

      editable: true,
      sorter: (a, b) =>
        departments
          .find((el) => el.value === a.departement_id)
          ?.label.localeCompare(
            departments.find((el) => el.value === b.departement_id)?.label
          ),

      render: (_, record) => (
        <div className="flex justify-between">
          <div>
            {record.departments?.map((el) => (
              <Tag key={el.id}>{el.label}</Tag>
            ))}
          </div>
          <div>
            {record.id == pipeline_id ? (
              <CaretRightOutlined className="ml-1" />
            ) : (
              ""
            )}
          </div>
        </div>
      ),
    },
  ];

  const onSelectChange = (selectedRowKeys, record) => {
    setSelectedRowKey(selectedRowKeys[0]);

    if (record && record.length > 0)
      setDisabledAddStage(record[0]?.system == 1 ? true : false);
    if (selectedRowKeys !== pipeline_id) {
      setEditingKeyStage("");
      setIdPipeline(selectedRowKeys[0]);
    }
  };

  const rowClassName = (record) => {
    if (record["key"] === editingKey) {
      return "editingRow";
    }
    if (selectedRowKey == record.key) {
      return "selected-row";
    }

    return "";
  };
  const onRow = (record, rowIndex) => {
    return {
      onClick: () => {
        if (record.id) {
          setSelectedRowKey(record.key);

          setDisabledAddStage(record?.system == 1 ? true : false);

          onSelectChange([record.key]);
        }
      },
    };
  };

  const filteredData = data.filter((item) => {
    const lowercasedSearch = search.toLowerCase();
    return item.label?.toLowerCase().includes(lowercasedSearch) &&
      filter?.selected?.length > 0
      ? item?.departments?.some((dept) => filter?.selected.includes(dept.label))
      : item.label?.toLowerCase().includes(lowercasedSearch);
  });
  return (
    <Space direction="vertical" style={{ width: "100%" }}>
      <div className="ml-2 mr-2 px-2.5 text-base font-semibold text-[#2253d5]">
        Pipeline{" "}
        {openView360InDrawer && contactInfoFromDrawer
          ? familyIcons(t).find(
              (el) => el.key === contactInfoFromDrawer?.family_id
            )?.label
          : !openView360InDrawer && contactInfo?.id
          ? familyIcons(t).find((el) => el.key === contactInfo?.family_id)
              ?.label
          : family}
      </div>
      <Header
        active={"2"}
        editingKey={editingKey}
        handleAdd={handleAdd}
        filters={departments.map((el) => ({
          text: el.label,
          value: el.label,
        }))}
        selectedFilter={filter.selected}
        btnText={""}
        disabled={loading ? true : editingKey ? true : search ? true : false}
        shape={"circle"}
        api="pipelines"
        setFilter={setFilter}
      />
      <NewTableDraggable
        columns={columns}
        setLoading={setLoading}
        isEditing={isEditing}
        data={filteredData}
        setData={setData}
        loading={loading}
        save={save}
        edit={edit}
        EditableCell={EditableCell}
        onFinishFailed={onFinishFailed}
        cancel={cancel}
        form={form}
        apiRank="/pipelines/updateRank"
        editingKey={editingKey}
        api="pipelines"
        btnText={t("helpDesk.addFolder")}
        pagination={false}
        setDataStage={setDataStage}
        pipeline_id={pipeline_id}
        onRow={onRow}
        rowSelection={false}
        rowClassName={rowClassName}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        pageSize={pageSIze}
        setPageSize={setPageSize}
        setIdPipeline={setIdPipeline}
      />

      <BottomButtonAddRow
        editingKey={editingKey}
        data={data}
        text={t("companies.addPipeline")}
        handleAdd={handleAdd}
        loading={loading}
        search={search}
        pagination={false}
      />
    </Space>
  );
};
export default Pipeline;
