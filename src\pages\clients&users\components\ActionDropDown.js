import { Button, Dropdown, Space, message } from "antd";
import {
  BranchesOutlined,
  DownOutlined,
  SwapOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { FiTrash, FiUserPlus } from "react-icons/fi";
import { MdBlockFlipped } from "react-icons/md";
import { roles } from "../../../utils/role";
import { handleDelete, sendInvitation } from "../helpers";
import blockOrUnblockUser from "../helpers/blockUser";
import { ImUnlocked } from "react-icons/im";
import { TbExchange } from "react-icons/tb";
import { RxReset } from "react-icons/rx";

const ActionDropDown = ({
  selectedRowKeys,
  role,
  familyId,
  setSelectedRowKeys,
  setShouldFetchData,
  setModalType,
  setOpenModal,
  folderId,
  // dispatch,
}) => {
  //
  const [t] = useTranslation("common");

  const condition = !roles?.includes(role);
  //
  //
  const items = [
    ...(familyId === 4
      ? [
          {
            label: t("contacts.invite"),
            key: "invite",
            icon: <FiUserPlus className="h-4 w-4 text-slate-400" />,
            onClick: async () =>
              await sendInvitation(
                t,
                null,
                null,
                selectedRowKeys,
                setSelectedRowKeys
              ),
          },
          {
            label: t("contacts.unblock"),
            // danger: true,
            key: "unblock",
            icon: <ImUnlocked className="h-4 w-4 text-slate-400" />,
            // disabled: true,
            onClick: async () =>
              await blockOrUnblockUser(
                t,
                null,
                null,
                selectedRowKeys,
                setSelectedRowKeys,
                "unblock"
              ),
          },
          {
            label: t("contacts.block"),
            danger: true,
            key: "block",
            icon: <MdBlockFlipped className="h-4 w-4 " />,
            // disabled: true,
            onClick: async () =>
              await blockOrUnblockUser(
                t,
                null,
                null,
                selectedRowKeys,
                setSelectedRowKeys,
                "block"
              ),
          },
        ]
      : []),
    ...(familyId === 6
      ? [
          {
            label: t("helpDesk.move"),
            key: "move",
            icon: (
              <TbExchange
                className="text-slate-400"
                style={{ fontSize: "16px" }}
              />
            ),
            onClick: () => {
              setModalType("deplacer");
              setOpenModal(true);
            },
          },
          {
            label: t("helpDesk.transfer"),
            key: "transfer",
            icon: (
              <SwapOutlined
                className="text-slate-400"
                style={{ fontSize: "16px" }}
              />
            ),
            onClick: () => {
              setModalType("transfer_multiple");
              setOpenModal(true);
            },
          },
          {
            label: t("helpDesk.merge"),
            key: "fusionner",
            icon: (
              <BranchesOutlined
                rotate={180}
                className="text-slate-400"
                style={{ fontSize: "16px" }}
              />
            ),
            onClick: () => {
              setModalType("fusionner");
              setOpenModal(true);
            },
          },
          {
            label: t("helpDesk.handOver"),
            key: "remettre",
            disabled: folderId === 1,
            icon: (
              <RxReset
                className="text-slate-400"
                style={{ fontSize: "16px" }}
              />
            ),
            onClick: () => {
              setModalType("remettre");
              setOpenModal(true);
            },
          },
        ]
      : []),
    {
      label: t("contacts.delete"),
      danger: true,
      key: "delete",
      icon: <FiTrash className="h-4 w-4" />,
      disabled: condition || folderId === 3,
      onClick: async () =>
        await handleDelete(
          t,
          familyId,
          null,
          null,
          selectedRowKeys,
          setSelectedRowKeys,
          setShouldFetchData
          // dispatch
        ),
    },
  ];
  const menuProps = {
    items,
    // onClick: handleMenuClick,
  };
  return (
    <Dropdown menu={menuProps}>
      <Button>
        <Space>
          {/* {`Actions (${selectedRowKeys.length} ${
            selectedRowKeys.length > 1 ? "records" : "record"
          })`} */}
          {`Actions (${selectedRowKeys.length})`}
          <DownOutlined />
        </Space>
      </Button>
    </Dropdown>
  );
};

export default ActionDropDown;
