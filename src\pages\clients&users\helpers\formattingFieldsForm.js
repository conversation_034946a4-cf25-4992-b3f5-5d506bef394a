import dayjs from "dayjs";
import { URL_ENV } from "index";
import {
  channelSourceId,
  // matchFamiliesWithPipelineFieldId,
} from "../FamilyRouting";
//
const formattingFieldsForm = async (
  fields,
  setMatchFieldsAndType,
  setSelectedTab,
  fieldsValues,
  form,
  setCheckIfRequired,
  nbrPhone,
  mailingProps,
  config,
  updatedFields,
  selectedPipeline,
  familyId,
  selectedStageId,
  isCurrentUserUpdating
) => {
  const result = [];
  const matchF_G = new Map();
  const matchFieldsAndType = {};
  const check = {};
  const updateValues = {};

  let matchNbrPhoneWithField = false;

  fields?.forEach((group, i) => {
    const { id, label, fields } = group;
    const fieldsByGroup = [];

    if (i === 0) {
      setSelectedTab(id);
    }

    fields?.forEach((field, index) => {
      const {
        id: fieldId,
        alias,
        field_type: fieldType,
        hidden,
        required,
        uniqueValue,
        field_list_value: fieldListValue,
        description,
        placeholder,
        family_id,
        field_module_id,
        module,
        read_only,
        multiple,
      } = field;
      if (updatedFields && fieldsValues?.[fieldId])
        updateValues[fieldId] = fieldsValues?.[fieldId];
      if (!hidden) {
        if (required)
          check[fieldId] = { groupId: id, required: required, alias: alias };
        matchF_G.set(fieldId, id);
        const options = fieldListValue?.length ? fieldListValue : undefined;
        const oneField = {
          id: fieldId,
          label: alias,
          type: fieldType,
          family_id: family_id,
          field_module_id: field_module_id,
          required,
          uniqueValue,
          options,
          description: description,
          placeholder: placeholder,
          isModule: !!module && module,
          read_only:
            (!!selectedStageId && module === "Pipeline") ||
            (isCurrentUserUpdating && fieldId === 34)
              ? 1
              : read_only,
          multiple,
        };

        if (mailingProps?.label && i === 0 && index === 0) {
          oneField["value"] = mailingProps?.label;
        }

        if (module === "Pipeline") {
          if (selectedPipeline) oneField["value"] = selectedPipeline;
          else
            oneField["value"] =
              config.module_values?.Pipeline?.find((obj) => obj.system === true)
                ?.id || undefined;
        }

        if (fieldsValues?.[fieldId]) {
          const value = fieldsValues?.[fieldId];
          const dateFormat = config?.date_format;
          const timeFormat = config?.time_format;
          switch (fieldType) {
            case "image":
              const field_value_image = [
                {
                  uid: Math.floor(Math.random() * 100),
                  name: fieldsValues?.[fieldId]?.file_name,
                  status: "done",
                  url: `${
                    URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                  }${fieldsValues?.[fieldId]?.path}`,
                },
              ];
              oneField["value"] = field_value_image;
              form && form.setFieldValue(fieldId, field_value_image);
              break;
            case "album":
              const field_value_album = fieldsValues?.[fieldId]?.map(
                (val, i) => ({
                  uid: Math.floor(Math.random() * 10000),
                  name: val?.file_name,
                  status: "done",
                  url: `${
                    URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                  }${val.path}`,
                })
              );
              oneField["value"] = field_value_album;
              form && form.setFieldValue(fieldId, field_value_album);
              break;
            case "file":
              const field_value_file = {
                fileName: value?.filename,
                isActive: value?.isActive ? true : false,
                files: value?.files?.map((val, i) => ({
                  uid: i,
                  name: val?.file_name,
                  status: "done",
                  url: `${
                    URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                  }${val?.path}`,
                })),
              };
              oneField["value"] = field_value_file;
              if (form) {
                form.setFieldValue(
                  fieldId,
                  value?.files?.map((val, i) => ({
                    uid: i,
                    name: val?.file_name,
                    status: "done",
                    url: `${
                      URL_ENV?.REACT_APP_BASE_URL +
                      URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                    }${val?.path}`,
                  }))
                );
                form.setFieldValue(`${fieldId}filename`, value?.filename);
                form.setFieldValue(`${fieldId}isActive`, value?.isActive);
              }
              break;
            default:
              oneField["value"] = value;
              if (form) {
                if (fieldType === "date_time") {
                  form.setFieldValue(
                    fieldId,
                    dayjs(value, `${dateFormat} ${timeFormat}`)
                  );
                } else if (fieldType === "date") {
                  form.setFieldValue(fieldId, dayjs(value, `${dateFormat}`));
                } else if (fieldType === "time") {
                  form.setFieldValue(fieldId, dayjs(value, `${timeFormat}`));
                } else if (fieldType === "range") {
                  form.setFieldValue(fieldId, [
                    dayjs(value[0], `${dateFormat}`),
                    dayjs(value[1], `${dateFormat}`),
                  ]);
                } else form.setFieldValue(fieldId, value);
              }
              oneField["value"] = value;
              break;
          }
        } else if (
          fieldType === "phone" &&
          nbrPhone &&
          !matchNbrPhoneWithField
        ) {
          oneField["value"] = [null, nbrPhone];
          matchNbrPhoneWithField = true;
        } else if (mailingProps?.email && fieldType === "email" && i === 0) {
          oneField["value"] = mailingProps?.email;
        } else if (
          mailingProps?.source === "email" &&
          fieldId === channelSourceId[familyId]
        ) {
          oneField["value"] = 3;
        } else if (familyId === 6 && fieldId === 164 && !fieldsValues) {
          oneField["value"] = 1;
        }
        fieldsByGroup.push(oneField);
      }
      matchFieldsAndType[fieldId] = fieldType;
    });

    const oneGroup = {
      id,
      group_name: label,
      fields: fieldsByGroup,
    };

    result.push(oneGroup);
  });
  setCheckIfRequired && setCheckIfRequired(check);
  setMatchFieldsAndType(matchFieldsAndType);
  if (updatedFields) updatedFields.current = updateValues;
  return { result, matchF_G };
};

export default formattingFieldsForm;
