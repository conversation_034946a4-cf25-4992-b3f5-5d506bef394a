import { Button, Form, Input, Space, Typography, message } from "antd";
import { X } from "lucide-react";
import React, { useRef, useState } from "react";
import useOnClickOutside from "../../layouts/chat/hooks/useClickOutside";

export const AddLinkBox = ({ editor, modal, setOpen, form }) => {
  const [input, setInput] = useState("");
  const [isURLValid, setIsURLValid] = useState(false);
  const addLinkRef = useRef(null);
  useOnClickOutside(addLinkRef, undefined, () => {
    form.resetFields();
  });
  const onFinish = () => {
    setlink(input);
    setOpen(false);
    form.resetFields();
    setInput("");
  };
  const onFinishFailed = () => {
    message.error("Submit failed!");
  };

  const handleChange = (e) => {
    setInput(e.target.value);
  };

  const setlink = (input) => {
    editor
      .chain()
      .focus()
      .extendMarkRange("link")
      .setLink({ href: form.getFieldsValue().url })
      .run();
  };

  const handleSubmit = (e, link) => {
    e.preventDefault();
    setlink(input);
    setOpen(false);
    setInput("");
  };

  const handleURLChange = (e) => {
    const value = e.target.value;
   // console.log(value);
    setIsURLValid(value.startsWith("https://") || value.startsWith("http://"));
  };
  return (
    <div className="relative" ref={addLinkRef}>
      <div className="flex items-center justify-between">
        <Typography.Text className="mr-2">Add Link</Typography.Text>
        <Button
          type="text"
          shape="circle"
          onClick={() => {
            form.resetFields();
            setOpen(false);
          }}
        >
          <X size={14} />
        </Button>
      </div>
      <div className="my-2">
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
        >
          <Form.Item
            name="url"
            label="URL"
            rules={[
              {
                required: true,
              },
              {
                type: "url",
                warningOnly: true,
              },
              //   {
              //     type: "string",
              //     min: 6,
              //   },
            ]}
          >
            <Input placeholder="url placeholder" onChange={handleURLChange} />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" disabled={!isURLValid}>
                Ok
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};
