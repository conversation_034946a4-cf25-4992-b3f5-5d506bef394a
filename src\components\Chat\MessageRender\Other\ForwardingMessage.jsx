import { Typography } from "antd";
import {
  forwardMessageTypes,
  getUserFromMsg,
  getName,
} from "pages/layouts/chat/utils/ConversationUtils";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
const { Text } = Typography;

function ForwardingMessage({ item }) {
  const currentUser = useSelector((state) => state.chat.currentUser);
  const { t } = useTranslation("common");

  return (
    forwardMessageTypes.includes(item?.type) && (
      <>
        {item?.forwarded?.forwarded_by === currentUser?._id ? (
          <Text type="secondary">
            {t("chat.forward.you_forward_message")}
            <em>
              {getName(
                getUserFromMsg(item?.forwarded?.sender_id)?.name ?? "",

                "name"
              )}{" "}
            </em>
          </Text>
        ) : (
          <Text type="secondary">
            <b className="mr-0.5 capitalize">
              {getName(
                getUserFromMsg(item?.forwarded?.forwarded_by)?.name ?? "",
                "name"
              )}
            </b>
            {item.forwarded.room === 1
              ? t("chat.forward.room_received_forwarded_message")
              : t("chat.forward.you_received_forwarded_message")}
            <em>
              {getName(
                getUserFromMsg(item?.forwarded?.sender_id)?.name ?? "",

                "name"
              )}
            </em>
          </Text>
        )}
      </>
    )
  );
}

export default ForwardingMessage;
