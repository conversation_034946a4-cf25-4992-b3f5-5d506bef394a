import React, { useState, useEffect, useCallback } from "react";
import {
  FilterFilled,
  CloseOutlined,
  PlusCircleOutlined,
  EditOutlined,
  DeleteOutlined,
  CloseSquareFilled,
} from "@ant-design/icons";
import {
  Card,
  Divider,
  Drawer,
  Button,
  Radio,
  Select,
  Input,
  Spin,
  AutoComplete,
} from "antd";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { setInternalForward } from "../../../../../new-redux/actions/voip.actions/setInternalForward";
import { getInternalForward } from "../../../../../new-redux/actions/voip.actions/getInternalForward";
const DrawerForward = ({ onClose, open }) => {
  const [t] = useTranslation("common");
  const collegues = useSelector((state) => state.voip.collegues);
  const internalforward = useSelector((state) => state.voip.internalforward);
  const makeInternalforward = useSelector(
    (state) => state.voip.makeInternalforward
  );

  const user = useSelector((state) => state.user.user);
  const posteVoip = (user?.extension).toString();

  const [spin, setSpin] = useState(false);
  const [value, setValue] = useState(
    internalforward.default_action === "1"
      ? 1
      : internalforward.default_action === "2"
      ? 2
      : internalforward.default_action === "10"
      ? 10
      : 0
  );
  const [valueMobile, setValueMobile] = useState(
    internalforward.default_action === "10"
      ? internalforward.default_destination
      : ""
  );
  const [valueSon, setValueSon] = useState(
    internalforward.etat_renvoi === "activer"
      ? internalforward.nb_sonnerie / 5
      : 1
  );
  const [valueDis, setValueDis] = useState(
    internalforward.default_action === "1"
      ? collegues.find(
          (element) =>
            `${element.extension}` === internalforward.default_destination
        )?.name
      : ""
  );

  const dispatch = useDispatch();
  var options = [];
  collegues?.map((user) =>
    options.push({ value: `${user?.label?.replaceAll("_", " ")}` })
  );

  // function makeInternalForward(posteVoip, valueSon, action, valueDis) {

  //   dispatch(setInternalForward(posteVoip, valueSon * 5, action, value === 2 ? posteVoip : value === 10 ? valueMobile : valueDis))
  //   if (makeInternalforward?.etat_ws === "ERREUR_BOITE_VOCAL_INEXISTANTE") {
  //     //  alert("nooo")
  //   }
  //   else {
  //     setSpin(true)
  //     setTimeout(() => {
  //       dispatch(getInternalForward(posteVoip));
  //       onClose()
  //       setSpin(false)
  //     }, "1000")

  //   }
  // }

  function makeInternalForward(posteVoip, valueSon, action, valueDis) {
    dispatch(
      setInternalForward(
        posteVoip,
        valueSon * 5,
        action,
        value === 2 ? posteVoip : value === 10 ? valueMobile : valueDis
      )
    );

    if (makeInternalforward?.etat_ws === "ERREUR_BOITE_VOCAL_INEXISTANTE") {
      //  alert("nooo")
    } else {
      setSpin(true);
      setTimeout(() => {
        dispatch(getInternalForward(posteVoip));
        onClose();
        setSpin(false);
      }, "1000");
    }
  }

  const handleChangeSon = (value) => {
    setValueSon(value);
  };

  const handleChangeDis = (value) => {
    let name = collegues.find((element) => element.name === value);

    setValueDis(name?.extension);
  };

  const onChange = (e) => {
    setValue(e.target.value);
  };
  const onChangeMobile = (e) => {
    setValueMobile(e.target.value);
  };

  var disabledButton =
    value === 0
      ? true
      : value === 10 && valueMobile === ""
      ? true
      : value === 1 && valueDis === undefined
      ? true
      : false;

  return (
    <>
      <Drawer
        title={
          <div>
            <Button
              size="small"
              style={{ border: "0" }}
              icon={<CloseOutlined />}
              onClick={() => onClose()}
            ></Button>{" "}
            {t("voip.renvoi-title")}
          </div>
        }
        placement="right"
        closable={false}
        onClose={onClose}
        open={open}
        getContainer={false}
        width={270}
      >
        {!spin ? (
          <>
            <>
              <div className="mb-5">
                <Radio.Group onChange={onChange} value={value}>
                  <Radio value={1}>{t("voip.renvoi-vers-poste")}</Radio>
                  <Radio value={10}>{t("voip.renvoi-vers-numero")}</Radio>
                  <Radio value={2}>{t("voip.renvoi-vers-boite")}</Radio>
                </Radio.Group>
              </div>
              <div className="mb-2">
                <div>{t("voip.renvoi-sonneries")}: </div>
                <Select
                  defaultValue={valueSon}
                  style={{
                    width: 130,
                  }}
                  onChange={handleChangeSon}
                  options={[
                    {
                      label: `1 ${t("voip.sonnerie")}`,
                      value: 1,
                    },
                    {
                      label: `2 ${t("voip.sonnerie")}`,
                      value: 2,
                    },
                    {
                      label: `3 ${t("voip.sonnerie")}`,
                      value: 3,
                    },
                    {
                      label: `4 ${t("voip.sonnerie")}`,
                      value: 4,
                    },
                    {
                      label: `5 ${t("voip.sonnerie")}`,
                      value: 5,
                    },
                  ]}
                />
              </div>

              {value === 1 ? (
                <div>
                  <div>{t("voip.renvoi-vers")}: </div>
                  <Select
                    showSearch
                    onChange={handleChangeDis}
                    defaultValue={valueDis}
                    popupClassName="certain-category-search-dropdown"
                    popupMatchSelectWidth={200}
                    allowClear={{
                      clearIcon: <CloseSquareFilled />,
                    }}
                    style={{
                      width: 150,
                    }}
                    options={options}
                    filterOption={(inputValue, option) =>
                      option.value
                        .toUpperCase()
                        .indexOf(inputValue.toUpperCase()) !== -1
                    }
                  >
                    <Input size="large" />
                  </Select>
                </div>
              ) : value === 10 ? (
                <div>
                  <div>{t("voip.renvoi-vers")}: </div>
                  <Input
                    style={{ width: 130 }}
                    value={valueMobile}
                    onChange={onChangeMobile}
                  />
                </div>
              ) : value === 2 ? (
                t("voip.renvoi-vers-boite-message")
              ) : (
                <></>
              )}
            </>

            <Button
              style={{ position: "absolute", bottom: "19px", right: "26px" }}
              type="primary"
              onClick={() =>
                makeInternalForward(
                  `${posteVoip}`,
                  `${valueSon}`,
                  `${value}`,
                  `${valueDis}`
                )
              }
              disabled={disabledButton}
            >
              {t("voip.renvoi-button")}
            </Button>
          </>
        ) : (
          <div className="text-center">
            <Spin />
          </div>
        )}
      </Drawer>
    </>
  );
};
export default DrawerForward;
