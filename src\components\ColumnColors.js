import { Badge, Space } from "antd";
import React from "react";
import { useTranslation } from "react-i18next";

const ColumnColors = ({ color, colors }) => {
  const [t] = useTranslation("common");

  return (
    <>
      {color && (
        <Space>
          <Badge color={color} />{" "}
          <span style={{ color }}>
            {t(`colors.${colors.find((el) => el.value === color)?.label}`)}
          </span>
        </Space>
      )}
    </>
  );
};

export default ColumnColors;
