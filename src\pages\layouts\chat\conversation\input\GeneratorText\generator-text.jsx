import { Button, message, Tooltip } from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { FaRobot } from "react-icons/fa";
import { useDispatch } from "react-redux";
import { generateTextWithAi } from "new-redux/services/chat.services";
import { convertToPlain } from "pages/layouts/chat/utils/ConversationUtils";
import "./style.css";
function GeneratorText({ editor, source }) {
  const { t } = useTranslation("common");
  const [messageApi, contextHolder] = message.useMessage();
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  if (!editor) {
    return <></>;
  }
  const generateText = async (event) => {
    let editorId;
    if (source.includes("tasks")) {
      editorId = event.currentTarget.closest("span")?.id;
    } else {
      editorId = "editor-input";
    }
    const message = editor.getHTML() ?? "";
    if (!message || convertToPlain(message).trim().length === 0) {
      messageApi.open({
        type: "error",
        content: t("chat.action.messageEmptyErrorAi"),
        style: {
          marginTop: "75vh",
        },
      });
      return;
    }
    const container = document.getElementById(editorId);
    container?.classList?.add("disable-editor");
    const abort = new AbortController();
    setTimeout(() => abort.abort(), 20000);
    setLoading(true);
    editor.chain().focus().selectAll().run();
    const response = await dispatch(
      generateTextWithAi({
        message,
        signal: abort?.signal,
      })
    );
    setLoading(false);
    editor.commands.clearContent();
    editor.commands.insertContent(response);
    editor.chain().focus().selectAll().run();
    container?.classList?.remove("disable-editor");
  };
  return (
    <>
      {contextHolder}
      <Tooltip title={t("chat.action.generateText")}>
        <Button
          disabled={convertToPlain(editor.getHTML()).length === 0}
          loading={loading}
          onClick={generateText}
          type="text"
          icon={<FaRobot className="text-white" />}
          shape="circle"
          size="small"
          className="ai-generate-button flex items-center justify-center"
        />
      </Tooltip>
    </>
  );
}

export default GeneratorText;
