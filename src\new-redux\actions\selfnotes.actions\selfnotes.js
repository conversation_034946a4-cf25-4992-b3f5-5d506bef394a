import MainService from "services/main.service";
import {
  TRIGGER_SAVE_NOTE,
  TRIGGER_DELETE_NOTE,
  SAVE_SELF_NOTE_PENDING,
  SAVE_SELF_NOTE_SUCCESS,
  SAVE_SELF_NOTE_ERROR,
  SET_SELF_NOTES,
  MODIFY_SELF_NOTE,
  DELETE_SELF_NOTE,
  ADD_SELF_NOTE,
  ADD_SELF_NOTES_TO_LIST,
  SAVE_NEW_NOTE_AFTER_POST,
  REMOVE_NEW_NOTES_ON_UNMOUNT,
  SET_NOTE_UNLOCKED,
  SET_NOTE_LOCKED,
  SET_SAVE_PENDING,
  UNSET_SAVE_PENDING,
  REMOVE_IS_NEW_NOTE,
  UPDATE_NOTE_FAMILY_AND_ELEMENT,
  SET_NOTES_NOTIFICATIONS_COUNT,
  INCREMENT_NOTES_NOTIFICATIONS_COUNT,
  DECREMENT_NOTES_NOTIFICATIONS_COUNT,
  SET_NOTE_OPEN_ELEMENT_MODAL,
  SET_NOTES_NOTIFICATIONS_LIST,
  ADD_TO_NOTES_NOTIFICATIONS_LIST,
  REMOVE_FROM_NOTES_NOTIFICATIONS_LIST,
} from "../../constants";

export const triggerSaveNote = (payload) => ({
  type: TRIGGER_SAVE_NOTE,
  payload,
});

export const triggerDeleteNote = (payload) => ({
  type: TRIGGER_DELETE_NOTE,
  payload: payload,
});

export const saveSelfNotePending = (payload) => ({
  type: SAVE_SELF_NOTE_PENDING,
  payload: payload,
});

export const saveSelfNoteSuccess = (payload) => ({
  type: SAVE_SELF_NOTE_SUCCESS,
  payload: payload,
});

export const saveSelfNoteError = (payload) => ({
  type: SAVE_SELF_NOTE_ERROR,
  payload: payload,
});

export const setSelfNotes = (payload) => ({
  type: SET_SELF_NOTES,
  payload: payload,
});

export const modifySelfNote = (payload) => ({
  type: MODIFY_SELF_NOTE,
  payload: payload,
});

export const deleteSelfNote = (payload) => ({
  type: DELETE_SELF_NOTE,
  payload: payload,
});

export const addSelfNote = (payload) => ({
  type: ADD_SELF_NOTE,
  payload: payload,
});

export const addSelfNotesToList = (payload) => ({
  type: ADD_SELF_NOTES_TO_LIST,
  payload: payload,
});

export const saveNewNoteAfterPost = (payload) => ({
  type: SAVE_NEW_NOTE_AFTER_POST,
  payload: payload,
});

export const removeNewNotesOnUnmount = (payload) => ({
  type: REMOVE_NEW_NOTES_ON_UNMOUNT,
  payload: payload,
});

export const saveNote = (noteId, data) => {
  return async (dispatch) => {
    dispatch(saveSelfNotePending());
    try {
      let response = await MainService.updateNote360(noteId, data);
      dispatch(saveSelfNoteSuccess());
      dispatch(modifySelfNote(response.data));
    } catch (error) {
      dispatch(saveSelfNoteError());
    }
  };
};

/**
 * export const SET_SELECTED_NOTE = "SET_SELECTED_NOTE";
export const REMOVE_SELECTED_NOTE = "REMOVE_SELECTED_NOTE";
 */

export const setSelectedNote = (payload) => ({
  type: "SET_SELECTED_NOTE",
  payload: payload,
});

export const removeSelectedNote = () => ({
  type: "REMOVE_SELECTED_NOTE",
});

//MODIFY_SELECTED_NOTE_CONTENT

export const modifySelectedNoteContent = (payload) => ({
  type: "MODIFY_SELECTED_NOTE_CONTENT",
  payload: payload,
});

export const setNoteUnlocked = (payload) => ({
  type: SET_NOTE_UNLOCKED,
  payload: payload,
});

export const setNoteLocked = (payload) => ({
  type: SET_NOTE_LOCKED,
  payload: payload,
});

export const setSavePending = (payload) => ({
  type: SET_SAVE_PENDING,
  payload: payload,
});

export const unsetSavePending = (payload) => ({
  type: UNSET_SAVE_PENDING,
  payload: payload,
});

export const removeIsNewNote = (payload) => ({
  type: REMOVE_IS_NEW_NOTE,
  payload: payload,
});

export const updateNoteFamilyAndElement = (payload) => ({
  type: UPDATE_NOTE_FAMILY_AND_ELEMENT,
  payload: payload,
});

export const setNotesNotificationsCount = (payload) => ({
  type: SET_NOTES_NOTIFICATIONS_COUNT,
  payload: payload,
});

export const incrementNotesNotificationsCount = (payload) => ({
  type: INCREMENT_NOTES_NOTIFICATIONS_COUNT,
  payload: payload,
});

export const decrementNotesNotificationsCount = (payload) => ({
  type: DECREMENT_NOTES_NOTIFICATIONS_COUNT,
  payload: payload,
});


export const setNoteOpenElementModal = (payload) => ({
  type: SET_NOTE_OPEN_ELEMENT_MODAL,
  payload: payload,
});

export const setNotesNotificationsList = (payload) => ({
  type: SET_NOTES_NOTIFICATIONS_LIST,
  payload: payload,
});

export const addToNotesNotificationsList = (payload) => ({
  type: ADD_TO_NOTES_NOTIFICATIONS_LIST,
  payload: payload,
});

export const removeFromNotesNotificationsList = (payload) => ({
  type: REMOVE_FROM_NOTES_NOTIFICATIONS_LIST,
  payload: payload,
});