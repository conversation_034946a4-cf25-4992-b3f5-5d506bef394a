import { Tooltip, Divider, List, Skeleton } from "antd";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import { useTranslation } from "react-i18next";
import localizedFormat from "dayjs/plugin/localizedFormat";
import { useSelector } from "react-redux";
import InfiniteScroll from "react-infinite-scroll-component";

import { getName, isTheDay } from "pages/layouts/chat/utils/ConversationUtils";
import { URL_ENV } from "index";
import { moment_timezone } from "App";
import { formatNotificationMessage } from "../helpers/handleDisplayNotificationMessage";
import { useWindowSize } from "pages/clients&users/components/WindowSize";
import { AvatarChat } from "components/Chat";
import { EXTENSIONS_ARRAY } from "../helpers/calculateSum";

dayjs.extend(relativeTime);
dayjs.extend(localizedFormat);

const HistoryFeed = ({ historyList, logLastPage, logCurrentPage, setCurrentLogPage, loadLogs }) => {
  const [t, i18n] = useTranslation("common");
  const user = useSelector((state) => state?.user?.user);
  const windowSize = useWindowSize();

  // Handle increment log page on scroll.
  const handleIncrementLogPage = () => setCurrentLogPage(logCurrentPage + 1);

  return (
    <div
      id="scrollableDiv"
      style={{
        height: windowSize?.height - 250,
        overflowY: "auto",
      }}
    >
      <InfiniteScroll
        dataLength={historyList && historyList?.length}
        hasMore={logCurrentPage < logLastPage}
        next={handleIncrementLogPage}
        loader={
          <div className="px-[16px] py-[8px]">
            <Skeleton avatar paragraph={{ rows: 1 }} active={true} />
          </div>
        }
        endMessage={
          loadLogs ? (
            <Divider plain>{t("chat.loading")}</Divider>
          ) : (
            <Divider plain>{t("tasks.logFeedEndOfList")}</Divider>
          )
        }
        scrollableTarget="scrollableDiv"
      >
        <List
          split={false}
          dataSource={historyList && historyList?.filter((el) => el !== null)}
          size="small"
          renderItem={(item, index) => (
            <div key={item?.id}>
              <div className="sticky top-0 z-10">
                {!moment_timezone(item?.created_at?.split(" ")[0]).isSame(
                  moment_timezone(historyList[index - 1]?.created_at?.split(" ")[0])
                ) && (
                  <Divider plain className="uppercase">
                    {isTheDay(new Date(item?.created_at?.split(" ")[0]), "Y", new Date()) ||
                    moment_timezone(item?.created_at?.split(" ")[0]).isSame(
                      moment_timezone(),
                      "day"
                    )
                      ? moment_timezone(item?.created_at).calendar(null, {
                          lastDay: () => (i18n.language === "fr" ? "[Hier]" : "[Yesterday]"),
                          sameDay: () => (i18n.language === "fr" ? "[Aujourd'hui]" : "[Today]"),
                        })
                      : moment_timezone(item?.created_at).format("ll")}
                  </Divider>
                )}
              </div>
              <List.Item
                style={{ marginInlineStart: 0 }}
                actions={[
                  dayjs(item?.created_at?.split(" ")[1], "HH:mm").format(
                    user?.location?.time_format
                  ),
                ]}
              >
                <List.Item.Meta
                  style={{
                    padding: "2px",
                    borderRadius: "6px",
                  }}
                  avatar={
                    <Tooltip title={getName(item?.user, "name")}>
                      <div>
                        <AvatarChat
                          height={"20px"}
                          width={"20px"}
                          url={`${
                            URL_ENV?.REACT_APP_BASE_URL + URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                          }${item?.avatar || item?.user_2?.avatar}`}
                          hasImage={
                            item?.avatar
                              ? EXTENSIONS_ARRAY?.includes(item?.avatar?.split(".")?.pop())
                              : EXTENSIONS_ARRAY?.includes(item?.user_2?.avatar?.split(".")?.pop())
                          }
                          name={getName(item?.label || item?.user_2?.label, "avatar")}
                          type="user"
                        />
                      </div>
                    </Tooltip>
                  }
                  title={
                    <div className="text-[13px] text-[#000000A6]" style={{ whiteSpace: "normal" }}>
                      {formatNotificationMessage(
                        t,
                        getName(item?.user_2?.label ? item?.user_2?.label : item?.user, "name"),
                        item?.action,
                        item?.user_2?.id === user?.id,
                        user
                      )}
                    </div>
                  }
                />
              </List.Item>
            </div>
          )}
        />
      </InfiniteScroll>
    </div>
  );
};

export default HistoryFeed;
