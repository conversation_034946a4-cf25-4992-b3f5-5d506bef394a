import { Tooltip } from "antd";
import dayjs from "dayjs";
import moment from "moment";
import React from "react";
import { GiStopwatch } from "react-icons/gi";

const Chrono = ({ chronoNow, expireTime, processDuration }) => {
  // const now = new Date();
  // const apiDate = new Date(expireTime.replace(" ", "T"));

  return (
    <>
      <Tooltip
        title={
          chronoNow ? (
            <>
              <p className="">
                {`Date de début : ${moment(chronoNow).format("llll")}`}
              </p>
              <p className="">
                {`Date de fin : ${moment(expireTime).format("llll")}`}
              </p>
            </>
          ) : null
        }
      >
        <GiStopwatch
          className={
            processDuration === 1
              ? `h-4 w-4 cursor-default text-[#ba1435]`
              : processDuration === 0
              ? `h-4 w-4 cursor-default text-[#34bc44]`
              : `h-4 w-4 cursor-default`
          }
        />
      </Tooltip>
    </>
  );
};

export default Chrono;
