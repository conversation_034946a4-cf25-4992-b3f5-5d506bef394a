import React, { useEffect, useRef } from "react";
import { Form, Input, Button, Space, Switch, InputNumber } from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";

import { generateAxios } from "services/axiosInstance";
import { URL_ENV } from "index";
import { toastNotification } from "components/ToastNotification";
import { setSearch } from "new-redux/actions/menu.actions/menu";
import { SubmitKeyPress } from "utils/SubmitKeyPress";
import LabelTable from "components/LabelTable";
import Header from "components/configurationHelpDesk/Header";
import NewTableDraggable from "components/NewTableDraggable";
import BottomButtonAddRow from "components/BottomButtonAddRow";

const DiscountProduct = () => {
  const [form] = Form.useForm();
  const [, setCount] = useState(0);

  const [data, setData] = useState([]);
  const [editingKey, setEditingKey] = useState("");
  const [id, setId] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [oldPage, setOldPage] = useState(1);
  const [loading, setLoading] = useState(true);
  const [pageSize, setPageSize] = useState(20);
  const isEditing = (record) => record.key === editingKey;
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const { search } = useSelector((state) => state.form);
  const inputRefs = useRef([]);
  useEffect(() => {
    inputRefs.current.forEach((input) => {
      input?.focus();
    });
  }, [data.length, id]);
  // useEffect(() => {
  //   if (editingKey && oldPage !== currentPage) {
  //     setData((prev) => prev.filter((el) => el.id));
  //     setEditingKey("");
  //   }
  // }, [currentPage, editingKey, oldPage]);
  const onFinishFailed = (values) => {
    console.log(values);
  };

  const onChange = (checked) => {
    console.log(`switch to ${checked}`);
  };

  const EditableCell = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    const inputNode =
      inputType === "switch" ? (
        <>
          <Switch
            size="small"
            defaultChecked={
              !loading
                ? record.status == 1
                  ? true
                  : false
                : form.getFieldsValue().status == 0
                ? false
                : true
            }
          />
        </>
      ) : (
        <InputNumber
          onKeyDown={handleInputNumberKeyDown}
          min="0"
          max="100"
          addonAfter="%"
          placeholder={title}
          onKeyPress={handleKeyPress}
          ref={(el) => (inputRefs.current[index] = el)}
        />
      );

    return (
      <td {...restProps}>
        {editing ? (
          <Form.Item
            name={dataIndex.toLowerCase()}
            style={{
              margin: 0,
            }}
            rules={[
              {
                required: dataIndex.toLowerCase() === "label" ? true : false,
                message: `${title} ${t("table.header.isrequired")}`,
              },
            ]}
          >
            {inputNode}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };
  function handleInputNumberKeyDown(e) {
    if (e.key === "." && e.target.value.includes(".")) {
      e.preventDefault();
    }
    if (e.key === "-" && !e.target.value) {
      e.preventDefault();
    }
    if (e.key === "e") {
      e.preventDefault();
    }
    if (
      isNaN(parseInt(e.key)) &&
      ![
        "Tab",
        "Backspace",
        "Delete",
        "ArrowLeft",
        "ArrowRight",
        "Enter",
      ].includes(e.key)
    ) {
      e.preventDefault();
    }
  }
  const edit = (record) => {
    if (record) {
      form.setFieldsValue({
        rate: record.rate,
        status: record.status == 1,
      });
      setId(record.id);
    } else {
      form.setFieldsValue({
        rate: "",
        status: "",
      });
    }
    setEditingKey(record.key);
  };
  const cancel = (record) => {
    setEditingKey("");
    setId(null);
    if (!record.id) {
      setData(data.filter((item) => item.key !== record.key));
    }
  };

  const UpdateSwitch = async (record, status) => {
    setLoading(true);
    try {
      const res = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).put(
        `/discounts/${record.id}`,
        { rate: record.rate, status: status ? 1 : 0 },
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      );
      setEditingKey("");
      setData(
        data.map((el) =>
          el.id === res.data.data.id
            ? {
                ...res.data.data,
                key: res.data.data.id,
              }
            : el
        )
      );
      form.setFieldsValue({
        rate: "",
        status: "",
      });
      setLoading(false);
      toastNotification(
        "success",
        t("familyProduct.discount") + t("toasts.edit"),
        "topRight"
      );
    } catch (errInfo) {
      setLoading(false);

      toastNotification("error", t("toasts.somethingWrong"), "topRight");
    }
  };

  const save = async (key) => {
    setLoading(true);
    if (id) {
      try {
        const row = await form.validateFields();
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).put(
          `/discounts/${id}`,
          { ...row, status: row.status ? 1 : 0 },
          {
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
            },
          }
        );
        setEditingKey("");
        setData(
          data.map((el) =>
            el.id === res.data.data.id
              ? {
                  ...res.data.data,
                  key: res.data.data.id,
                }
              : el
          )
        );
        form.setFieldsValue({
          rate: "",
          status: "",
        });
        setLoading(false);
        toastNotification(
          "success",
          t("familyProduct.discount") + t("toasts.edit"),
          "topRight"
        );
      } catch (errInfo) {
        setLoading(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    } else {
      try {
        const row = await form.validateFields();
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post("/discounts", { ...row, status: row.status ? 1 : 0 });
        setEditingKey("");
        setData([
          ...data.filter((el) => el.id),
          { ...res.data.data, key: res.data.data.id },
        ]);
        form.setFieldsValue({
          rate: "",
          status: "",
        });
        setLoading(false);
        toastNotification(
          "success",
          t("familyProduct.discount") + t("toasts.created"),
          "topRight"
        );
      } catch (errInfo) {
        setLoading(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    }
  };
  useEffect(() => {
    const getFolders = async () => {
      setLoading(true);
      try {
        const {
          data: { data },
        } = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get("/discounts");
       // console.log(data);
        setData(data.map((el, i) => ({ ...el, key: el.id, rank: i + 1 })));
        if (data.length > 0) {
          setCount(Math.max(...data.map((el) => el.id)));
        }
        setLoading(false);
      } catch (err) {
        setLoading(false);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    };
    getFolders();
    return () => dispatch(setSearch(""));
  }, []);

  const handleKeyPress = (event) => {
    SubmitKeyPress(event, form);
  };
  const handleClick = (event) => {
    event.stopPropagation();
  };

  const columns = [
    {
      title: t("familyProduct.rate"),
      dataIndex: "rate",
      key: "rate",
      editable: true,
      sorter: (a, b) => a.rate - b.rate,
      render: (_, record) => (
        <span>
          {record.rate} {typeof record.rate === "number" ? "%" : ""}
        </span>
      ),
    },
    {
      title: t("helpDesk.actif"),
      key: "status",
      dataIndex: "status",
      editable: true,
      filters: [
        {
          text: t(`helpDesk.actif`),
          value: "1",
        },
        {
          text: t(`helpDesk.noActif`),
          value: "0",
        },
      ],
      onFilter: (value, record) => record.status == value,
      sorter: (a, b) => a.status - b.status,

      render: (_, record) => (
        <>
          <Switch
            size="small"
            checked={record.status == 1 ? true : false}
            onChange={(e) => UpdateSwitch(record, e)}
          />
        </>
      ),
    },
  ];

  const handleAdd = () => {
    const ids = data.map((object) => {
      return object.id;
    });
    setId(null);
    const newData = {
      key: Math.max(...ids) + 1,
      rate: `  `,
      status: false,
      disabled: true,
    };
    setCurrentPage(Math.ceil([...data, newData].length / pageSize));
    setOldPage(Math.ceil([...data, newData].length / pageSize));
    setData([...data, newData]);
    form.setFieldsValue({
      rate: "",
      status: false,
    });
    setEditingKey(Math.max(...ids) + 1);
    setCount(Math.max(...ids) + 1);
  };

  //do not delete this line
  const onRow = () => {};

  //   const filteredData = data.filter((item) => {
  //     return item.label.toLowerCase().includes(search.toLowerCase());
  //   });
  return (
    <Space direction="vertical" style={{ width: "100%" }}>
      <Header
        active={"5"}
        editingKey={editingKey}
        handleAdd={handleAdd}
        btnText={t("familyProduct.addDiscount")}
        disabled={loading ? true : editingKey ? true : search ? true : false}
        api="discounts"
      />

      <NewTableDraggable
        columns={columns}
        setLoading={setLoading}
        isEditing={isEditing}
        data={data}
        setData={setData}
        loading={loading}
        save={save}
        edit={edit}
        EditableCell={EditableCell}
        onFinishFailed={onFinishFailed}
        cancel={cancel}
        form={form}
        apiRank="/rank-discounts"
        editingKey={editingKey}
        api="discounts"
        onRow={onRow}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        pageSize={pageSize}
        setPageSize={setPageSize}
      />

      <BottomButtonAddRow
        editingKey={editingKey}
        data={data}
        text={t("familyProduct.addDiscount")}
        handleAdd={handleAdd}
        loading={loading}
        search={search || ""}
      />
    </Space>
  );
};
export default DiscountProduct;
