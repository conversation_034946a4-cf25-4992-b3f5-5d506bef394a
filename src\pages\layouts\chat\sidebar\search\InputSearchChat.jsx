import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { useTranslation } from "react-i18next";
import { FiSearch } from "react-icons/fi";
import { Input, Tooltip } from "antd";
import { useDispatch, useSelector } from "react-redux";

import {
  setDisplayMembersRoomsPreview,
  setSearchChatSidebar,
} from "new-redux/actions/chat.actions";
import { CloseCircleOutlined, FilterOutlined } from "@ant-design/icons";
import { BsCommand } from "react-icons/bs";
import { FilterSidebarType } from "new-redux/reducers/chatReducer";

export const InputSearchChat = forwardRef((_, ref) => {
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const { activeFiltersDiscussion, searchChatSideBar } = useSelector(
    (state) => state.chat
  );
  const eventMercure = useSelector((state) => state.ChatRealTime.eventMercure);
  const [forceFocus, setForceFocus] = useState(null);
  const [search, setSearch] = useState("");
  const inputRef = useRef(null);
  useImperativeHandle(
    ref,
    () => {
      return {
        focusInput() {
          if (inputRef.current) {
            const length = document.getElementById("search-input-sidebar").value
              ?.length;

            setForceFocus({
              number: Math.floor(Math.random() * 10000 + 1),
              position: length,
            });
          }
        },
      };
    },
    []
  );
  useEffect(() => {
    inputRef.current?.setSelectionRange(
      forceFocus?.position,
      forceFocus?.position
    );
    const time = setTimeout(() => {
      inputRef.current?.focus();
    }, 200);
    return () => {
      clearTimeout(time);
    };
  }, [forceFocus?.position, forceFocus?.number]);
  useEffect(() => {
    if (!eventMercure) {
      setSearch(searchChatSideBar);
    }
    // handle the diffrence between hover chat and the original menu
    if (!searchChatSideBar) {
      setSearch("");
    }
  }, [eventMercure, searchChatSideBar]);

  const handleSearch = (value) => {
    setSearch(value?.trimStart());
    const searchTerm = value?.trimStart();

    const timeout = setTimeout(() => {
      dispatch(setSearchChatSidebar(searchTerm));
      clearTimeout(timeout);
    }, 1);
  };

  const cancelFilter = () => {
    dispatch(
      setDisplayMembersRoomsPreview([...Object.values(FilterSidebarType)])
    );
  };

  const getPlaceholderText = () => {
    if (
      activeFiltersDiscussion?.length ===
      Object.values(FilterSidebarType).length
    ) {
      return t("chat.searchSide.search");
    } else if (activeFiltersDiscussion?.includes(FilterSidebarType.room)) {
      return t("chat.searchSide.searchGroup");
    } else if (activeFiltersDiscussion?.includes(FilterSidebarType.member)) {
      return t("chat.searchSide.searchMember");
    } else if (activeFiltersDiscussion?.includes(FilterSidebarType.guest)) {
      return t("chat.searchSide.searchGuest");
    } else {
      return t("chat.searchSide.searchEmpty");
    }
  };

  return (
    <Input
      id="search-input-sidebar"
      tabIndex={1}
      onBlur={() => {
        document.getElementById("search-shortcut")?.classList?.remove("hidden");
      }}
      onFocus={(e) => {
        document.getElementById("search-shortcut")?.classList?.add("hidden");

        document.getElementById("editor-input")?.blur();
        const time = setTimeout(() => {
          setForceFocus({
            number: Math.floor(Math.random() * 10000 + 1),
            position: e.clientX,
          });
          clearTimeout(time);
        }, 1);
      }}
      ref={inputRef}
      size="middle"
      className="h-full w-full"
      autoFocus
      placeholder={getPlaceholderText()}
      prefix={<FiSearch className="text-slate-500" />}
      suffix={
        <div className=" group flex items-center  space-x-1 text-xs ">
          <div
            id="search-shortcut"
            className="flex items-center rounded-md bg-gray-100 px-2 text-gray-400 transition duration-300   "
          >
            {navigator.userAgent.indexOf("Macintosh") !== -1 ? (
              <>
                <BsCommand /> <span>K</span>
              </>
            ) : (
              "Ctrl + K"
            )}
          </div>
          {activeFiltersDiscussion?.length <
            Object.values(FilterSidebarType).length && (
            <Tooltip title={t("chat.searchSide.cancelFilter")}>
              <div className=" group relative flex cursor-pointer  items-center justify-center  rounded-full bg-blue-600/10 p-1 text-blue-600">
                <FilterOutlined className="flex  group-hover:hidden" />
                <CloseCircleOutlined
                  onClick={cancelFilter}
                  className="hidden  group-hover:flex"
                />
              </div>
            </Tooltip>
          )}
        </div>
      }
      onChange={(e) => handleSearch(e.target.value)}
      value={search}
      allowClear
    />
  );
});
