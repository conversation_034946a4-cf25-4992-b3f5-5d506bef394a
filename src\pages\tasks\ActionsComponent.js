/**
 * @name ActionsComponent
 *
 * @description `ActionsComponent` component is responsible for displaying the dropdown menu.
 *
 * @param {Object} elementValue The object of the child element, containing id, uuid, label, extension, ...
 * @param {String} tooltipText The text of the tooltip to be displayed on top.
 * @param {Object} children The content to be rendered in side the jsx.
 * @param {String} placement The placement of the tooltip popover.
 *
 * @returns {JSX.Element} Dropdown with the options of calling or sending a message to user.
 */

import { useMemo, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { useSelector, useDispatch } from "react-redux";
import { MessageOutlined } from "@ant-design/icons";
import { HiOutlinePhone } from "react-icons/hi2";
import { Dropdown, Tooltip } from "antd";

import { getName } from "pages/layouts/chat/utils/ConversationUtils";
import useActionCall from "pages/voip/helpers/ActionCall";
import { openDrawerChat } from "new-redux/actions/voip.actions/handleDrawerChat";
import { isGuestConnected } from "utils/role";

const ActionsComponent = ({
  elementValue,
  tooltipText = null,
  children,
  placement = "bottomLeft",
}) => {
  const { t } = useTranslation("common");
  const { currentUser } = useSelector((state) => state.chat);
  const { user } = useSelector((state) => state.user);
  const isActivityOverviewActive = useSelector(
    (state) => state.TasksRealTime?.isOverviewActive
  );
  const actionCall = useActionCall();
  const dispatch = useDispatch();
  const { openChatInViewSphere } = useSelector((state) => state?.vue360);

  // Handle open chat discussion.
  const handleSendMessage = useCallback(() => {
    const drawerType = isActivityOverviewActive ? "overviewTask" : undefined;
    dispatch(openDrawerChat(elementValue?.uuid, null, drawerType));
  }, [dispatch, elementValue?.uuid, isActivityOverviewActive]);

  // Handle call on click.
  const handleCall = useCallback(() => {
    actionCall(elementValue?.extension, null, 4);
  }, [actionCall, elementValue?.extension]);

  // Dropdown items (call + send message options).
  const actionsItems = useMemo(
    () => [
      user?.access?.["chat"] === "1" && {
        key: "00",
        label: t("chat.action.sendMessage"),
        icon: <MessageOutlined style={{ fontSize: "16px" }} />,
        onClick: handleSendMessage,
        show: "1",
        disabled: openChatInViewSphere,
      },
      {
        key: "01",
        label: t("chat.action.call"),
        icon: <HiOutlinePhone style={{ fontSize: "15px" }} />,
        onClick: handleCall,
        show: "1",
        disabled: !currentUser?.post_number,
      },
    ],
    [
      t,
      handleSendMessage,
      handleCall,
      currentUser?.post_number,
      openChatInViewSphere,
    ]
  );

  // Handle disable open options dropdown.
  const isDisabled =
    elementValue?.id === user?.id ||
    elementValue?.family_id !== 4 ||
    isGuestConnected(currentUser?.role, user?.role);

  return (
    <Tooltip
      title={
        tooltipText !== false &&
        (tooltipText || getName(elementValue?.label, "name"))
      }
      overlayStyle={{ zIndex: "99999" }}
    >
      <Dropdown
        disabled={isDisabled}
        menu={{ items: actionsItems }}
        arrow={{ pointAtCenter: true }}
        className={`${
          isDisabled ? "cursor-auto" : "cursor-pointer"
        } flex justify-center`}
        trigger={["click"]}
        overlayStyle={{ zIndex: "99999" }}
        placement={placement}
      >
        <span onClick={(e) => e.preventDefault()}>{children}</span>
      </Dropdown>
    </Tooltip>
  );
};

export default ActionsComponent;
