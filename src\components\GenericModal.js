import { Modal } from "antd";
export default function Confirm(
  title,
  okText,
  icon,
  func,
  danger,
  body,
  confirmLoading,
  onCancelFunc
) {
  Modal.confirm({
    title,
    centered: true,
    okText,
    // width: 520,
    icon,
    okButtonProps: {
      danger: danger,
    },
    content: <div>{body}</div>,
    onOk: async () => {
      try {
        await func(); // Ensure func is async if necessary
      } catch (error) {
        console.error("Error in onOk:", error);
      }
    },
    onCancel(close) {
      return onCancelFunc ? onCancelFunc() : close();
    },
    closable: true,
    // confirmLoading: confirmLoading,
  });
}
