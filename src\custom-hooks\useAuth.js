import { useEffect, useLayoutEffect, useState } from "react";
import useLocalStorage from "./useLocalStorage";
import { useDispatch } from "react-redux";
import { getTokenRoom } from "../new-redux/actions/visio.actions/createVisio";
import { useTranslation } from "react-i18next";
import {
  clearAllExcept,
  setUserInfos,
} from "../new-redux/actions/user.actions/getUser";
import useNetwork from "./useNetwork";
import { getFamilies } from "../new-redux/actions/families.actions/getFamilies";
import { getTypes } from "../new-redux/actions/types.actions/getTypes";
import {
  getUserChatInfo,
  getUserListChat,
  redirectToChat,
} from "../new-redux/services/chat.services";
import { useSelector } from "react-redux";
import {
  CONFIRM_ACCESS_URL_REGEX,
  INVITATION_URL_REGEX,
  REDIRECT_TO_CHAT_REGEX,
  VISIO_URL_REDIRECT_REGEX,
  VISIO_URL_REGEX,
} from "../utils/regex";
import {
  fetchProfile,
  getUserChatWithComunik,
} from "../pages/profile/services";
import { URL_ENV } from "index";
import { LogoutLink } from "pages/layouts/chat/utils/ConversationUtils";
import MainService from "services/main.service";
import {
  setAccountData,
  setFetchedAccount,
  setLoadingAccount,
} from "new-redux/actions/mail.actions";
import { useNavigate } from "react-router-dom";
import { setSelectedAccountInEmail } from "new-redux/actions/dashboard.actions";
import { isGuestConnected } from "utils/role";
//import { getInternalForward } from "new-redux/actions/voip.actions/getInternalForward";

function useAuth({ setOpen }) {
  const [newToken, setNewToken] = useLocalStorage(
    "accessToken",
    localStorage.getItem("accessToken")
  );
  const [tokenState, setTokenState] = useState({
    loading: true,
    token: "",
  });
  const [_, setNewRefreshToken] = useLocalStorage(
    "refreshToken",
    localStorage.getItem("refreshToken")
  );
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { t } = useTranslation("common");
  const { isOnline } = useNetwork();
  const { currentUser } = useSelector((state) => state?.chat);
  const { user } = useSelector((state) => state.user);

  useLayoutEffect(() => {
    const isValidToken =
      newToken &&
      newToken !== "null" &&
      newToken !== undefined &&
      newToken !== "undefined" &&
      newToken !== null;
    const urlLast = new URL(window.location.href);
    if (urlLast.toString().match(VISIO_URL_REGEX)) {
      const match = urlLast.toString().match(VISIO_URL_REGEX);

      if (match) {
        const roomName = urlLast.searchParams.get("room_visio_name");

        if (isValidToken) {
          localStorage.setItem("lastHref", urlLast);
          dispatch(
            getTokenRoom({
              room: roomName,
              errorText1: t("toasts.errorFetchApi"),
              errorText2: t("toasts.errorRoomNotFound"),
            })
          );
        } else {
          requestAnimationFrame(() => {
            window.location.href = urlLast.origin + "/visio-home/" + roomName;
          });
        }
      }
    } else if (urlLast.toString().match(CONFIRM_ACCESS_URL_REGEX)) {
      const match = urlLast.toString().match(CONFIRM_ACCESS_URL_REGEX);

      if (match) {
        const token = urlLast.searchParams.get("token");

        if (!isValidToken) {
          requestAnimationFrame(() => {
            window.location.href = urlLast.origin + "/invitation-user/" + token;
          });
        }
      }
    } else if (urlLast.toString().match(REDIRECT_TO_CHAT_REGEX)) {
      const match = urlLast.toString().match(REDIRECT_TO_CHAT_REGEX);

      if (match) {
        const email = urlLast.searchParams.get("email");
        if (isValidToken) {
          dispatch(redirectToChat(email, navigate));
        } else {
          localStorage.setItem("redirectChatLink", urlLast);
          LogoutLink();
        }
      }
    } else {
      setTokenState((p) => ({ ...p, loading: true }));

      const URL_REGEX_AUTH = new RegExp(
        (process.env.REACT_APP_BRANCH === "devLocal"
          ? process.env.REACT_APP_LOCAL_URL
          : URL_ENV?.REACT_APP_DOMAIN) + process.env?.REACT_APP_URL_AUTH_REGEX
      );

      if (window.location.href.match(URL_REGEX_AUTH)) {
        const getLastHref = localStorage.getItem("lastHref");

        const url_token = new URL(window.location.href);
        (async () => {
          // clearAllExcept();

          try {
            const oldLangue = localStorage.getItem("language");

            const access_token = url_token.searchParams.get("access_token");
            const refresh_token = url_token.searchParams.get("refresh_token");
            const langue = url_token.searchParams.get("lang");
            setNewToken(access_token);
            setNewRefreshToken(refresh_token);
            if (typeof oldLangue === "string" && oldLangue !== langue) {
              setOpen(true);
            }
          } catch (error) {
            //   console.error("here to logout", error);
            clearAllExcept();
            LogoutLink();
          }
        })();

        if (getLastHref) {
          requestAnimationFrame(() => {
            window.location.href = getLastHref;
            localStorage.removeItem("lastHref");
          });
        }

        return;
      } else if (!isValidToken) {
        if (
          window.location.href.match(VISIO_URL_REDIRECT_REGEX) ||
          window.location.href.match(INVITATION_URL_REGEX)
        ) {
          return;
        }
        //  console.error("here to logout2");

        LogoutLink();
        return;
      } else return;
    }
  }, []);
  useEffect(() => {
    const getAccounts = async () => {
      dispatch(setLoadingAccount(true));
      try {
        const response = await MainService.getAccounts();
        if (response?.status === 200) {
          let notifs = await MainService.getNotifcationMailingByUser();
          let data = response?.data?.map((item) => ({
            label: item.email,
            value: item.id,
            primary: item.primary_account,
            shared: item.shared,
            sync: item.sync,
            creator: item.creator,
            selected: !!item.selected,
            departmentId: item.departement_id,
            dispatcheur: item.dispatcheur,
            alias: item.alias ?? [],
            labels: item.labels,
            default_signature: item.default_signature,
            notification_status: notifs?.data?.find(
              (el) => el.account_id === item.id
            )?.notification_status,
          }));
          dispatch(setAccountData(data));
          Array.isArray(response?.data) &&
            response?.data.filter((el) => el.shared === "1").length > 0 &&
            dispatch(
              setSelectedAccountInEmail(
                response?.data.filter((el) => el.shared === "1")[0]?.id
              )
            );
        }
      } catch (err) {
        dispatch(setLoadingAccount(false));
        dispatch(setFetchedAccount(false));
        console.log(err);
      }
    };

    const getToken = async () => {
      document.getElementById("first-time-loader")?.remove();
      if (!newToken) return;
      if (!localStorage.getItem("language"))
        localStorage.setItem("language", "fr");

      window.history.pushState({}, document.title, window.location.pathname);
      setTokenState((p) => {
        return { ...p, token: newToken, loading: false };
      });
      isOnline &&
        (await Promise.all([
          !currentUser?._id && dispatch(getUserChatInfo()),
          fetchProfile(dispatch, setUserInfos),
        ]));

      Promise.all([
        dispatch(getUserListChat()),
        dispatch(getFamilies()),
        dispatch(getTypes()),
        getAccounts(),
        getUserChatWithComunik(),

        // it has been called in phonceCPT : L:283
        //  dispatch(getInternalForward()),
      ]);
      const getRedirectChatLink = localStorage.getItem("redirectChatLink");
      if (getRedirectChatLink) {
        navigate("/chat");
      }
    };
    getToken();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [newToken]);

  return {
    isValidToken: tokenState?.token?.length > 0 ?? "",
    isLoading: tokenState.loading,
  };
}

export default useAuth;
