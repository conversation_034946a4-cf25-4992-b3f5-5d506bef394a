import { Button, Space } from "antd";
import { useTranslation } from "react-i18next";
import { useLocation } from "react-router-dom";

export const FormFooter = ({ onClickCancel, loadButton, submitDisabled }) => {
  const [t] = useTranslation("common");
  const { pathname } = useLocation();
  return (
    <Space>
      <Button onClick={onClickCancel} disabled={submitDisabled}>
        {t("form.cancel")}
      </Button>
      <Button
        type="primary"
        htmlType="submit"
        disabled={submitDisabled}
        loading={loadButton}
      >
        {pathname.includes("profile") ? t("wiki.Save") : t("form.save")}
      </Button>
    </Space>
  );
};
