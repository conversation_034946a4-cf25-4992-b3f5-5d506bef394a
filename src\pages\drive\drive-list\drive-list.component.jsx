import React, { useState } from "react";
import { Table, Alert, Empty, message } from "antd";
import { useTranslation } from "react-i18next";
import CreateItemsDropdown from "../createItemsDropdown";
import UploadInput from "../UploadInput";
import { useSelector } from "react-redux";

const DriveList = ({
  isFetching,
  showDragDropUpload = true,
  onFileUpload,
  data,
  meta,
  onPageChange,
  columns,
  onRow,
  isError,
  onMoveItem,
  draggedItem,
  setDraggedItem,
}) => {
  const [t] = useTranslation("common");
  const search = useSelector((state) => state.drive.search);
  const [dragOverItem, setDragOverItem] = useState(null);

  const handleDragStart = (e, item) => {
    setDraggedItem(item);
    e.dataTransfer.effectAllowed = "move";
    e.dataTransfer.setData("text/plain", item.id);
  };

  const handleDragEnd = (e) => {
    setDraggedItem(null);
    setDragOverItem(null);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";
  };

  const handleDragEnter = (e, item) => {
    e.preventDefault();
    if (item.type === "folder" && draggedItem && draggedItem.id !== item.id) {
      setDragOverItem(item.id);
    }
  };

  const handleDragLeave = (e) => {
    // Only remove drag over effect if we're leaving the row entirely
    if (!e.currentTarget.contains(e.relatedTarget)) {
      setDragOverItem(null);
    }
  };

  const handleDrop = (e, targetItem) => {
    e.preventDefault();
    setDragOverItem(null);
    
    if (!draggedItem || !targetItem) return;
    
    if (targetItem.type !== "folder") {
      message.warning(t("drive.canOnlyDropIntoFolders"));
      return;
    }
    
    if (draggedItem.id === targetItem.id) {
      message.warning(t("drive.cannotDropIntoItself"));
      return;
    }
    
    if (onMoveItem) {
      onMoveItem(draggedItem, targetItem);
    }
    
    setDraggedItem(null);
  };

  // Enhanced onRow function that combines existing functionality with drag and drop
  const enhancedOnRow = (record, index) => {
    const originalRowProps = onRow ? onRow(record, index) : {};
    
    return {
      ...originalRowProps,
      draggable: true,
      onDragStart: (e) => handleDragStart(e, record),
      onDragEnd: handleDragEnd,
      onDragOver: handleDragOver,
      onDragEnter: (e) => handleDragEnter(e, record),
      onDragLeave: handleDragLeave,
      onDrop: (e) => handleDrop(e, record),
      style: {
        ...originalRowProps.style,
        backgroundColor: dragOverItem === record.id && record.type === 'folder' 
          ? 'rgba(24, 144, 255, 0.05)' 
          : originalRowProps.style?.backgroundColor,
        borderLeft: dragOverItem === record.id && record.type === 'folder'
          ? '3px solid #1890ff'
          : originalRowProps.style?.borderLeft,
        opacity: draggedItem?.id === record.id ? 0.5 : 1,
        transition: 'all 0.2s ease',
      },
      className: `${originalRowProps.className || ''} ${
        dragOverItem === record.id && record.type === 'folder' ? 'drag-over-row' : ''
      }`.trim(),
    };
  };

  if (isError) {
    return (
      <div className="flex h-full items-center justify-center p-4">
        <Alert
          message="Error"
          description={t("drive.failedToLoadItems")}
          type="error"
          showIcon
        />
      </div>
    );
  }

  return (
    <div>
      <style>{`
        .drag-drop-target:hover {
          background-color: rgba(24, 144, 255, 0.02) !important;
        }
        
        .drag-over-row {
          background-color: rgba(24, 144, 255, 0.05) !important;
          border-left: 3px solid #1890ff !important;
        }
        
        .drag-over-row::after {
          content: 'Drop here';
          position: absolute;
          right: 10px;
          top: 50%;
          transform: translateY(-50%);
          background: #1890ff;
          color: white;
          padding: 2px 8px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 500;
          z-index: 10;
          opacity: 0.9;
        }
        
        .dragging {
          opacity: 0.5 !important;
          cursor: move !important;
        }
        
        .drive-table .ant-table-tbody > tr.drag-drop-target {
          position: relative;
        }
        
        .drive-table .ant-table-tbody > tr:hover.drag-drop-target {
          background-color: rgba(24, 144, 255, 0.02) !important;
        }
      `}</style>
      <Table
      rowKey={"id"}
      columns={columns}
      dataSource={data}
      size="small"
      onRow={enhancedOnRow}
      className="drive-table"
      rowClassName={(record) =>
        `cursor-pointer [&_.row-actions]:hover:opacity-100 [&_.row-actions]:hover:transition-opacity ${
          record?.type === 'folder' ? 'drag-drop-target' : ''
        } ${draggedItem?.id === record?.id ? 'dragging' : ''}`
      }
      scroll={{ x: 'max-content', y: "calc(100vh - 250px)" }}
      pagination={{
        current: meta?.current_page,
        pageSize: meta?.per_page,
        total: meta?.total,
      }}
      locale={{
        emptyText: (
          !isFetching && !search ? <div className="flex flex-col items-center justify-center gap-2 ">
            <UploadInput
              onFileUpload={onFileUpload}
              showDragDropUpload={showDragDropUpload}
            />

            <div className="flex items-center gap-4">
              {showDragDropUpload && (
                <div className="flex items-center gap-2 text-gray-400">
                  <div className="h-px w-12 bg-gray-300"></div>
                  <span className="text-sm">{t("drive.or")}</span>
                  <div className="h-px w-12 bg-gray-300"></div>
                </div>
              )}
            </div>
            <CreateItemsDropdown />
          </div>:  !isFetching && search  && <Empty />
        ),  
      }}
      onChange={onPageChange}
    />
    </div>
  );
};

export default DriveList;
