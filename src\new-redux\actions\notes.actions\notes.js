import {
  ADD_NOTE360,
  REMOVE_NOTE360,
  UPDATE_NOTE360,
  SET_NOTES360,
  ADD_NOTES360_TO_LIST,
} from "../../constants";

export const addNote360 = (payload) => ({
  type: ADD_NOTE360,
  payload: payload,
});

export const removeNote360 = (payload) => ({
  type: REMOVE_NOTE360,
  payload: payload,
});

export const updateNote360 = (payload) => ({
  type: UPDATE_NOTE360,
  payload: payload,
});

export const setNotes360 = (payload) => ({
  type: SET_NOTES360,
  payload: payload,
});

export const addNotes360ToList = (payload) => ({
  type: ADD_NOTES360_TO_LIST,
  payload: payload,
});
