const generatePassword = (numOfChart) => {
    let password = "";
    const characters =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let numUpper = 0;
    let numLower = 0;
    let numNum = 0;
  
    while (
      password.length < numOfChart ||
      numUpper === 0 ||
      numLower === 0 ||
      numNum === 0
    ) {
      password = "";
      numUpper = 0;
      numLower = 0;
      numNum = 0;
      for (let i = 0; i < numOfChart; i++) {
        const randomChar = characters.charAt(
          Math.floor(Math.random() * characters.length)
        );
        if (randomChar >= "A" && randomChar <= "Z") {
          numUpper++;
        } else if (randomChar >= "a" && randomChar <= "z") {
          numLower++;
        } else {
          numNum++;
        }
        password += randomChar;
      }
    }
    return password;
  };

  export default generatePassword  