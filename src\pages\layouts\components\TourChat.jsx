import { Image } from "antd";
import axios from "axios";
import { CustomTour } from "components/tour/CustomTour";
import { closeTour } from "pages/profile/services";
import React, { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import i18n from "translations/i18n";

const TourChat = () => {
  const [openTour, setOpenTour] = useState(false);
  const [steps, setSteps] = useState([]);

  const { isOpenTour } = useSelector((state) => state.menu);
  const [t] = useTranslation("common");
  const hasInitialFetch = useRef(false);

  const dispatch = useDispatch();
  const closeTourDashboard = async () => {
    closeTour(setOpenTour, "chat", dispatch);
  };

  useEffect(() => {
    const fetchTourData = async () => {
      try {
        const res = await axios.get(
          `${
            process.env.REACT_APP_URL_TENANT + process.env.REACT_APP_SUFFIX_API
          }tours/tour-by-selector/chat?lang=${i18n.language}`
        );
        console.log(res.data.data);
        if (res.data.data.status) {
          setSteps(
            res.data.data.steps.map((el) => ({
              ...el,
              description: (
                <div dangerouslySetInnerHTML={{ __html: el.description }} />
              ),
              target: () => document.getElementById(el.selector),
              cover: el.cover ? (
                <Image
                  loading="lazy"
                  preview={false}
                  width={200}
                  height={200}
                  alt="tour.png"
                  src={process.env.REACT_APP_URL_TENANT + "storage/" + el.cover}
                />
              ) : null,
            }))
          );
          setOpenTour(true);
          // dispatch(setOpenTourInProfile(true)); // Décommenter si nécessaire
        }
      } catch (err) {
        console.log(err);
      }
    };
    if (process.env.REACT_APP_BRANCH.includes("dev")) {
      if (!hasInitialFetch.current) {
        hasInitialFetch.current = true;
        fetchTourData();
      } else if (isOpenTour) {
        fetchTourData();
      }
    }
  }, [isOpenTour, dispatch]);
  return (
    <div>
      {process.env.REACT_APP_BRANCH.includes("dev") ? (
        <CustomTour
          placement="right"
          open={openTour}
          zIndex={9999}
          onClose={closeTourDashboard}
          steps={steps}
          t={t}
        />
      ) : null}
    </div>
  );
};

export default TourChat;
