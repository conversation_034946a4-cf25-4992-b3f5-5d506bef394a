import { TableOutlined } from "@ant-design/icons";
import { <PERSON>ton, Popover, Tooltip } from "antd";
import React, { useCallback, useState } from "react";

const TableEditor = ({ editorRef, savedSelection, setContent = () => {} }) => {
  const [activeTable, setActiveTable] = useState(null);
  const [visible, setVisible] = useState(false);
  const [hoveredSize, setHoveredSize] = useState({ rows: 0, cols: 0 });
  const [selectedSize, setSelectedSize] = useState({ rows: 0, cols: 0 });

  const maxRows = 8;
  const maxCols = 8;

  const handleCellHover = (row, col) => {
    setHoveredSize({ rows: row, cols: col });
  };

  const handleCellClick = (row, col) => {
    setVisible(false);
    insertTable(row, col);
  };

  const content = (
    <div style={{ width: "200px" }}>
      <div style={{ marginBottom: "8px", fontWeight: "bold" }}>
        {hoveredSize.rows > 0 && hoveredSize.cols > 0
          ? `${hoveredSize.rows} × ${hoveredSize.cols}`
          : "Insérer un tableau"}
      </div>
      <div
        style={{
          display: "grid",
          gridTemplateColumns: `repeat(${maxCols}, 1fr)`,
          gap: "2px",
        }}
      >
        {Array.from({ length: maxRows * maxCols }).map((_, index) => {
          const row = Math.floor(index / maxCols) + 1;
          const col = (index % maxCols) + 1;

          return (
            <div
              key={index}
              style={{
                width: "20px",
                height: "20px",
                backgroundColor:
                  row <= hoveredSize.rows && col <= hoveredSize.cols
                    ? "#1890ff"
                    : "#f0f0f0",
                cursor: "pointer",
                transition: "background-color 0.2s",
              }}
              onMouseEnter={() => handleCellHover(row, col)}
              onClick={() => handleCellClick(row, col)}
            />
          );
        })}
      </div>
    </div>
  );

  const initializeTableHandlers = useCallback(() => {
    if (!editorRef.current) return;

    // Sélectionner tous les tableaux dans l'éditeur
    const tables = editorRef.current.querySelectorAll("table.editor-table");

    tables.forEach((table) => {
      // S'assurer que nous n'ajoutons pas de gestionnaires en double
      if (table.dataset.initialized === "true") return;

      // Marquer le tableau comme initialisé
      table.dataset.initialized = "true";

      // Ajouter un gestionnaire de clic pour le tableau entier
      table.addEventListener("click", (e) => {
        // Sélectionner le tableau actif
        setActiveTable(table);
      });

      // Ajouter un gestionnaire de survol pour le tableau
      table.addEventListener("mouseover", (e) => {
        // Sélectionner le tableau actif
        setActiveTable(table);
      });

      // Ajouter un gestionnaire de sortie pour le tableau
      table.addEventListener("mouseout", (e) => {
        // Ne pas désélectionner le tableau si on survole le popover
        const relatedTarget = e.relatedTarget;
        if (
          relatedTarget &&
          (relatedTarget.closest(".table-popover") ||
            relatedTarget.closest("table.editor-table") === table)
        ) {
          return;
        }

        // Vérifier si le curseur est sorti complètement du tableau
        if (
          !table.contains(e.relatedTarget) &&
          !e.relatedTarget?.closest(".table-popover")
        ) {
          // Désélectionner le tableau après un court délai
          // pour permettre à l'utilisateur d'atteindre le popover
          setTimeout(() => {
            if (!document.querySelector(".table-popover:hover")) {
              setActiveTable(null);
            }
          }, 200);
        }
      });

      // Ajouter des gestionnaires pour les cellules
      const cells = table.querySelectorAll("td");
      cells.forEach((cell) => {
        // Double-clic pour éditer le contenu
        cell.addEventListener("dblclick", () => {
          cell.focus();
        });
      });
    });
  }, [editorRef]);
  const insertTable = useCallback(
    (rows, cols) => {
      if (!editorRef.current) return;

      // Créer le tableau
      const table = document.createElement("table");
      table.setAttribute("contenteditable", "true");
      table.style.width = "100%";
      table.style.borderCollapse = "collapse";
      table.style.marginBottom = "1em";
      table.className = "editor-table";

      const tbody = document.createElement("tbody");
      table.appendChild(tbody);

      // Créer les lignes et cellules
      for (let i = 0; i < rows; i++) {
        const tr = document.createElement("tr");

        for (let j = 0; j < cols; j++) {
          const td = document.createElement("td");
          td.style.border = "1px solid #ddd";
          td.style.padding = "8px";
          td.style.position = "relative";

          // Ajouter un espace vide pour que la cellule soit éditable
          td.innerHTML = "<br>";

          tr.appendChild(td);
        }

        tbody.appendChild(tr);
      }

      // Restaurer la sélection sauvegardée si elle existe
      if (savedSelection.current) {
        const selection = window.getSelection();
        selection.removeAllRanges();
        selection.addRange(savedSelection.current);
      }

      // Insérer le tableau à la position du curseur
      const selection = window.getSelection();
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);

        // Vérifier si la sélection est dans l'éditeur
        let container = range.commonAncestorContainer;
        while (container && container !== editorRef.current) {
          container = container.parentNode;
        }

        if (!container) {
          // La sélection n'est pas dans l'éditeur, placer le tableau à la fin
          editorRef.current.appendChild(table);
        } else {
          // Insérer le tableau à la position de la sélection
          range.deleteContents();
          range.insertNode(table);

          // Placer le curseur après le tableau
          range.setStartAfter(table);
          range.setEndAfter(table);
          selection.removeAllRanges();
          selection.addRange(range);
        }
      } else {
        // Aucune sélection, ajouter le tableau à la fin de l'éditeur
        editorRef.current.appendChild(table);
      }

      // Mettre à jour le contenu
      setContent(editorRef.current.innerHTML);

      // Initialiser les gestionnaires d'événements pour le tableau
      initializeTableHandlers();

      // Placer le curseur dans la première cellule
      setTimeout(() => {
        const firstCell = table.querySelector("td");
        if (firstCell) {
          const newRange = document.createRange();
          newRange.selectNodeContents(firstCell);
          newRange.collapse(true);

          const selection = window.getSelection();
          selection.removeAllRanges();
          selection.addRange(newRange);

          // Assurer que l'éditeur a le focus
          editorRef.current.focus();
        }
      }, 0);
    },
    [editorRef]
  );
  return (
    <div>
      {" "}
      <Popover
        content={content}
        trigger="click"
        visible={visible}
        onVisibleChange={setVisible}
        placement="bottom"
      >
        <Tooltip title="Insérer un tableau">
          <Button
            size="small"
            icon={<TableOutlined />}
            onClick={() => setVisible(true)}
          />
        </Tooltip>
      </Popover>
    </div>
  );
};

export default TableEditor;
