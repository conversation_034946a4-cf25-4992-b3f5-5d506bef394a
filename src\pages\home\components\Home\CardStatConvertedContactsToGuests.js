import { Col, Row } from "antd";
import React, { useMemo } from "react";
import CardWithGraph from "../CardWithGraph";
import { GoTo } from "pages/home/<USER>";
import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { CheckOutlined, CloseOutlined, TeamOutlined } from "@ant-design/icons";
import { BsLuggage } from "react-icons/bs";

const CardStatConvertedContactsToGuests = () => {
  const [t] = useTranslation("common");
  const navigate = useNavigate();
  const { contacts } = useSelector((state) => state.dashboardRealTime);
const { user } = useSelector((state) => state.user);
  const processContactData = (contact) => {
    const totalContacts =
      contacts.find((item) => item.name === "Total")?.value || 1;
    const isConverted = contact.name === "Contact_guest";
    const isTotal = contact.name === "Total";

    const rate = !isTotal && ((contact.value / totalContacts) * 100).toFixed(2);

    return {
      ...contact,
      name: (
        <div className="flex flex-col gap-y-1.5">
          <span>
            {isConverted
              ? `${t("menu1.contacts")} → ${t("users.guests")}`
              : isTotal
              ? t("menu1.contacts")
              : t("dashboard.Contact_non_guest")}
          </span>
          {isConverted && (
            <span className="pb-1 text-xs text-gray-400">
              {t("dashboard.successfulInvitations")}- ({t("familyProduct.rate")}
              : {rate}%)
            </span>
          )}
          {isTotal && (
            <span className="pb-1 text-xs text-gray-400">
              {t("contacts.total_elements")}
            </span>
          )}
          {!isTotal && !isConverted && (
            <span className="pb-1 text-xs text-gray-400">Taux: {rate}%</span>
          )}
        </div>
      ),
      number: contact.value,
      icon: isTotal ? (
        <TeamOutlined style={{ fontSize: "18px" }} className="text-gray-600" />
      ) : (
        <span
          className={`relative ${
            isConverted ? "text-[#34B7FE]" : "text-[#B5BCC8]"
          }`}
        >
          <BsLuggage style={{ fontSize: "18px" }} />
          {isConverted ? (
            <CheckOutlined
              style={{ fontSize: "10px" }}
              className="absolute -right-[7px] -top-[2px]"
            />
          ) : (
            <CloseOutlined
              style={{ fontSize: "10px" }}
              className="absolute -right-[5px]"
            />
          )}
        </span>
      ),
    };
  };

  const dataLeads_Contacts = contacts?.map(processContactData) || [];

  const chartData = useMemo(() => {
    const filteredContacts =
      contacts?.filter((el) =>
        ["Contact_guest", "Contact_non_guest"].includes(el.name)
      ) || [];

    const totalContacts =
      contacts?.find((item) => item.name === "Total")?.value || 1;

    return {
      data: filteredContacts.map((el) => {
        const isConverted = el.name === "Contact_guest";

        return {
          ...el,
          name: isConverted
            ? `${t("menu1.contacts")} → ${t("users.guests")}`
            : `${t("dashboard.Contact_non_guest")}`,
          color: isConverted ? "#34B7FE" : "#B5BCC8",
          y: el.value,
        };
      }),
      name: "",
    };
  }, [contacts, t]);
  return (
    <Row gutter={[8, 8]}>
      <Col span={24}>
        <CardWithGraph
          title={
            <div className="flex items-center justify-between">
              <span>{t("menu1.contacts")} &nbsp;</span>
              <GoTo
                to={"11"}
                title={t("menu1.contacts")}
                navigate={navigate}
                t={t}
                user={user}
              />
            </div>
          }
          stats={dataLeads_Contacts}
          chartData={chartData}
        />
      </Col>
    </Row>
  );
};

export default React.memo(CardStatConvertedContactsToGuests);
