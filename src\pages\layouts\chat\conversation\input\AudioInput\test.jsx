import React,{memo} from 'react'
import { ReactMic } from 'react-mic'

const Test = memo(({ open,  isRecording, close }) => {
    console.log("render")
  return (
    <div>  <ReactMic
    record={isRecording}
    // onData={onData}
    className="sound-wave"
    visualSetting="sinewave"
    strokeColor="#000000"
    backgroundColor="white"
    mimeType="audio/webm"
  /></div>
  )
})

export default Test