import {
  STOP_CALL,
  START_CALL,
  START_CALL_VIDEO,
  STOP_CALL_VIDEO,
  RESET_STATE,
  SET_FORWARDING_CALL,
  SET_RECEIVER_INFO,
  SET_CMKPHONE_INSTANCE,
  UPDATE_CALL_IPBX,
  SET_CONF_INFO,
  RESET_CONF_INFO,
  SET_SENDING_MAIL,
  SET_EMAIL_PROPS,
} from "../constants";
const initialState = {
  callIPBX: null,
  callVideoIPBX: null,
  forwardingCall: null,
  receiverInfo: null,
  confInfo: null,
  sendingMail: false,
  emailProps: null,
  cmkPhone: {
    phone: null,
    status: "default",
  },
};
const voipBlackList = (state = initialState, action) => {
  const { type, payload } = action;
  switch (type) {
    case START_CALL:
      return {
        ...state,
        callIPBX: payload,
      };
    case UPDATE_CALL_IPBX: {
      return {
        ...state,
        callIPBX: {
          ...state.callIPBX,
          ...payload,
        },
      };
    }
    case STOP_CALL:
      return {
        ...state,
        callIPBX: null,
        confInfo: null,
      };
    case START_CALL_VIDEO:
      return {
        ...state,
        callVideoIPBX: payload,
      };

    case STOP_CALL_VIDEO:
      return {
        ...state,
        callVideoIPBX: null,
      };
    case SET_FORWARDING_CALL: {
      if (payload === null || payload?.exactPayload)
        return {
          ...state,
          forwardingCall: payload,
        };
      else
        return {
          ...state,
          forwardingCall: {
            ...state.forwardingCall,
            dst: !state.forwardingCall?.dst
              ? payload.dst
              : state.forwardingCall.dst,
            dst_forwarding: Array.isArray(state.forwardingCall?.dst_forwarding)
              ? [
                  ...state.forwardingCall?.dst_forwarding,
                  ...payload.dst_forwarding,
                ]
              : payload.dst_forwarding,
          },
        };
    }
    case SET_RECEIVER_INFO: {
      return {
        ...state,
        receiverInfo: payload,
      };
    }
    case SET_CMKPHONE_INSTANCE: {
      return {
        ...state,
        cmkPhone: {
          phone:
            typeof payload.phone === "undefined"
              ? state.cmkPhone.phone
              : payload.phone,
          status:
            typeof payload.status === "undefined"
              ? state.cmkPhone.status
              : payload.status,
        },
      };
    }
    case SET_CONF_INFO:
      return {
        ...state,
        confInfo: payload,
      };
    case RESET_CONF_INFO:
      return {
        ...state,
        confInfo: null,
      };

    case RESET_STATE: {
      return initialState;
    }

    case SET_SENDING_MAIL:
      return {
        ...state,
        sendingMail: payload,
      };
    case SET_EMAIL_PROPS:
      return {
        ...state,
        emailProps: payload,
      };
    default:
      return state;
  }
};
export default voipBlackList;
