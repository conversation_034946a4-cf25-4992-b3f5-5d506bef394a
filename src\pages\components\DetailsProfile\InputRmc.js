import React, { Suspense, lazy, useRef, useState } from "react";
import { Button, Input, Tooltip } from "antd";
import { SendOutlined, SmileOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { Loader } from "../../../components/Chat";
import { toastNotification } from "../../../components/ToastNotification";
import FileInput from "../../layouts/chat/conversation/input/FileInput/FileInput";
import useOnClickOutside from "../../layouts/chat/hooks/useClickOutside";
import data from "@emoji-mart/data";
import { useEffect } from "react";

const Picker = lazy(() => import("@emoji-mart/react"));
const { TextArea } = Input;

const InputRmc = ({
  fileList,
  setFileList,
  fileFromPase,
  onRemoveFile,
  cancel,
  setCancel,
  sendMessage,
  textInput,
  setTextInput,
  loadSend,
  handleKeyDown,
}) => {
  const [showEmojis, setShowEmojis] = useState(false);
  const textAreaRef = useRef(null);
  const [selectedEmoji, setSelectedEmoji] = useState(null);
  const emojiRef = useRef(null);
  const [t] = useTranslation("common");
  const buttonSendRef = useRef(null);
  useOnClickOutside(emojiRef, showEmojis, () => {
    setShowEmojis(false);
  });
  useEffect(() => {
    if (textAreaRef.current && !loadSend) {
      textAreaRef.current.selectionStart = 0;
      textAreaRef.current.selectionEnd = 0;
      textAreaRef.current.focus();
    }
  }, [loadSend]);

  //   useOnClickOutside(emojiRef, showEmojis, () => {
  //     setShowEmojis(false);
  //   });
  const addEmoji = (emojiObject) => {
    let symbol = emojiObject?.unified.split("-");
    let codesArray = [];
    symbol.forEach((element) => codesArray.push("0x" + element));
    let emoji = String.fromCodePoint(...codesArray);
    setSelectedEmoji(emoji);
    setTextInput((prev) => prev + emoji);
    setSelectedEmoji(null);
  };

  return (
    <div className="flex w-full items-center space-x-2">
      <div className="relative w-full">
        {showEmojis && (
          <div
            ref={emojiRef}
            className={`absolute right-0 ${"z-10"} ${"bottom-10"}`}
          >
            <Suspense fallback={<Loader size={30} />}>
              <Picker data={data} emojiSize={25} onEmojiSelect={addEmoji} />
            </Suspense>
          </div>
        )}{" "}
        <TextArea
          placeholder="Aa..."
          className="inputRmc  rounded-full hover:border-gray-300 focus:border-gray-300 focus:ring-0"
          style={{
            maxHeight: "100px",
            paddingLeft: "20px",
            paddingRight: "60px",
          }}
          ref={textAreaRef}
          disabled={loadSend}
          autoSize={{
            minRows: 1,
            //   maxRows: 6,
          }}
          onChange={(e) => {
            setTextInput(e.target.value);
          }}
          onKeyDown={handleKeyDown}
          value={textInput}
        />
        <div
          id="action-menu"
          className={`absolute bottom-1 right-3 z-50 flex justify-end space-x-1 p-0.5 `}
        >
          <Suspense
            fallback={
              <div className="my-1">
                <Loader size={20} />
              </div>
            }
          >
            <FileInput
              source="EXTERNE"
              ref={buttonSendRef}
              defaultFileList={fileFromPase} // default value to send type event.target.files
              onRemove={onRemoveFile}
              cancel={cancel}
              setCancel={setCancel}
              fileList={fileList}
              setFileList={(e) => {
                setFileList(e);
              }}
            />
          </Suspense>
          <Tooltip title={t("chat.action.emoji")}>
            <Button
              onClick={() => setShowEmojis(true)}
              className={showEmojis ? "is_active" : ""}
              type="text"
              icon={<SmileOutlined />}
              shape="circle"
              size="small"
            />
          </Tooltip>
        </div>
      </div>

      <Button
        type="link"
        size="middle"
        loading={loadSend}
        icon={<SendOutlined />}
        disabled={textInput.trim() === "" && fileList.length < 1}
        onClick={() => sendMessage(textInput)}
      />
    </div>
  );
};

export default InputRmc;
