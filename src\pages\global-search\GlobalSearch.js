import { useState, useRef, useCallback, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { Input, Tooltip } from "antd";
import { debounce } from "lodash";
import { FiSearch } from "react-icons/fi";
import { InfoCircleTwoTone } from "@ant-design/icons";
import { searchGlobal } from "./services";
import { toastNotification } from "components/ToastNotification";
import { getTokenRoom } from "new-redux/actions/visio.actions/createVisio";
import { Refs_IDs } from "components/tour/tourConfig";
import SearchModel from "./components/SearchModel";
import "./index.css";

const GlobalSearch = () => {
  //
  const [t] = useTranslation("common");
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const inputRef = useRef();
  const autoCompleteRef = useRef();
  const prevController = useRef(null);
  const limit = 15;
  //
  const onlineUser = useSelector((state) => state?.ChatRealTime?.onlineUser);
  //
  const [search, setSearch] = useState("");
  const [displaySearch, setDisplaySearch] = useState("");
  const [page, setPage] = useState(1);
  const [searchOptions, setSearchOptions] = useState([]);
  const [totalData, setTotalData] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState([]);

  const [isOpenModal, setIsOpenModal] = useState(false);
  //
  // console.log({ isOpenModal });

  const clearState = (clearSearch) => {
    if (clearSearch) {
      setSearch("");
      setDisplaySearch("");
    }
    setPage(1);
    setSearchOptions([]);
    setTotalData(0);
    setSelectedFilter([]);
  };
  //
  const fetchSearchOptions = useCallback(async () => {
    if (!isOpenModal) {
      return;
    }
    const controller = new AbortController();
    const signal = controller.signal;
    if (prevController.current) {
      prevController.current.abort();
    }
    prevController.current = controller;
    setIsLoading(true);
    try {
      const {
        data: { data, meta },
      } = await searchGlobal(
        search,
        page,
        limit,
        selectedFilter.join(","),
        signal
      );
      if (!data?.length) {
        setSearchOptions([]);
        setTotalData(0);
      } else {
        page === 1
          ? setSearchOptions(data)
          : setSearchOptions((prevData) => [...prevData, ...data]);
        setTotalData(meta.total);
      }
      setIsLoading(false);
    } catch (err) {
      if (err.name === "CanceledError") {
        console.log("Request was aborted");
      } else {
        setSearchOptions([]);
        setIsLoading(false);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
        throw new Error(err?.message ? err.message : err);
      }
    }
  }, [limit, page, search, selectedFilter, t, isOpenModal]);

  useEffect(() => {
    fetchSearchOptions();
    return () => {
      if (prevController.current) {
        prevController.current.abort();
      }
    };
  }, [fetchSearchOptions]);
  //
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedSearch = useCallback(
    debounce((nextValue) => {
      setPage(1);
      setSearch(nextValue);
    }, 300),
    []
  );

  const handleSearchChange = (event) => {
    const searchText = event.target.value;
    setDisplaySearch(searchText);
    debouncedSearch(searchText.trim());
  };
  //
  //
  const handleClickOnItem = (path, id) => {
    if (path === "/notes") {
      navigate("/notes", { state: { id } });
    } else navigate(path);

    setIsOpenModal(false);
    clearState(true);
  };
  //
  const handleJoinVisioMeeting = (room_id) => {
    if (!room_id) return;
    dispatch(
      getTokenRoom({
        room: room_id,
        errorText1: t("toasts.errorFetchApi"),
        errorText2: t("toasts.errorRoomNotFound"),
      })
    );
    clearState(true);
  };
  //
  // handle shortcut press
  useEffect(() => {
    const handleKeyDown = (event) => {
      const isMac = navigator.userAgent.indexOf("Macintosh") !== -1;

      const openModal = () => {
        event.preventDefault();
        if (!isOpenModal) setIsOpenModal(true);
        else if (inputRef.current) {
          inputRef.current.focus();
        }
      };

      if (isMac) {
        if (event.metaKey && event.altKey && event.keyCode === 75) {
          openModal();
        }
      } else {
        if (event.ctrlKey && event.altKey && event.keyCode === 75) {
          openModal();
        }
      }
    };

    window.addEventListener("keydown", handleKeyDown);

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  //
  //
  return (
    <>
      <div
        className="relative"
        onClick={() => {
          setIsOpenModal(true);
        }}
        ref={Refs_IDs.globalSearch}
      >
        <Input
          key="input-search-global-extern"
          ref={autoCompleteRef}
          readOnly
          value={null}
          prefix={
            <FiSearch className="text-slate-400" style={{ fontSize: 16 }} />
          }
          suffix={
            <>
              {shortcutsForSearch}
              <Tooltip title={t("globalSearch.infoOfSearchFunc")}>
                <InfoCircleTwoTone
                  style={{
                    fontSize: 14,
                    cursor: "help",
                  }}
                />
              </Tooltip>
            </>
          }
          placeholder={t("globalSearch.globalSearchAuto")}
        />
      </div>
      <SearchModel
        inputRef={inputRef}
        isOpen={isOpenModal}
        setIsOpen={setIsOpenModal}
        data={searchOptions}
        total={totalData}
        setPage={setPage}
        selectedFilter={selectedFilter}
        setSelectedFilter={setSelectedFilter}
        handleClickOnItem={handleClickOnItem}
        onlineUser={onlineUser}
        handleJoinVisioMeeting={handleJoinVisioMeeting}
        handleSearchChange={handleSearchChange}
        isLoading={isLoading}
        search={displaySearch}
        clearState={clearState}
      />
    </>
  );
};

export const shortcutsForSearch = (
  <div
    id="global-search-shortcut"
    className="flex items-center rounded-md bg-gray-100 px-2 text-sm text-gray-400 transition duration-300   "
  >
    {navigator.userAgent.indexOf("Macintosh") !== -1
      ? "⌘ + ⌥ + K"
      : "Ctrl + Alt + K"}
  </div>
);

export default GlobalSearch;
