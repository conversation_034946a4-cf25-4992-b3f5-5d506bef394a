import React, { Fragment, useState, useEffect, useCallback } from "react";
import { ArrowPathIcon } from "@heroicons/react/24/outline";
import { useDispatch, useSelector } from "react-redux";
import { Form, Button, Input, Switch, Radio, Alert } from "antd";
import { useParams } from "react-router-dom";

import { createNewField } from "../redux/fieldsSlice";
// import CheckboxGroup from "./CheckboxGroup";
// import Input from "./Input";
import DetailsTable from "./DetailsTable";
import MainService from "../services/main.service";
import { reset, updateSpecificField } from "../redux/fieldsSlice";
import { toastNotification } from "./ToastNotification";
import EditableTable from "./EditableTable";
import { displayRightIcon } from "../utils/displayIcon";

const SlideOver = ({
  form,
  setAddNewFieldModal,
  setFieldType,
  setLabel,
  fieldType,
  label,
  // errors,
  // addNewFieldModal,
  updateFieldProps,
  setUpdateFieldProps,
  setErrors,
  // updateFieldsOptions,
  // setUpdateFieldsOptions,
  mem,
  setMem,
  setPhoneList,
  phoneList,
}) => {
  const [familyId, setFamilyId] = useState(null);
  const [successAlert, setSuccessAlert] = useState(false);
  const [failAlert, setFailAlert] = useState(false);
  const [typesList, setTypesList] = useState([]);
  const [loadSaveNewOption, setLoadSaveNewOption] = useState(false);
  const [tableRows, setTableRows] = useState(null);
  const [hidden, setHidden] = useState(
    Object.keys(updateFieldProps).length !== 0 ? updateFieldProps?.hidden : false
  );
  const [required, setRequired] = useState(
    Object.keys(updateFieldProps).length !== 0 ? updateFieldProps?.required : false
  );
  const [uniqueValue, setUniqueValue] = useState(
    Object.keys(updateFieldProps).length !== 0 ? updateFieldProps?.uniqueValue : false
  );
  const [dataSource, setDataSource] = useState(
    Object.keys(updateFieldProps).length !== 0 ? updateFieldProps?.field_list_value : []
  );

  // const [form] = Form.useForm();
  const dispatch = useDispatch();
  const { types } = useSelector((state) => state.types);
  const { isError, isLoading, isFieldCreated, isFieldUpdated, isOptDeleted } = useSelector(
    (state) => state.fields
  );
  const { families } = useSelector((state) => state.families);
  const { id } = useParams();

  const handleInputPhoneChange = (e, index) => {
    let newFormValues = [...phoneList];
    newFormValues[index][e.target.name] = e.target.value;
    setPhoneList(newFormValues);
  };

  const handleAddNewClick = () => {
    setPhoneList([
      ...phoneList,
      { listElementValue: "", hidden: "0", deleted: "0", defaultValue: "0" },
    ]);
  };

  const handleRemoveNewClick = (index) => {
    let list = [...phoneList];
    list.splice(index, 1);
    setPhoneList(list);
  };

  const validateAddField = (values) => {
    let errors = {};
    if (values.fieldType === null) {
      errors.fieldType = "The label Field Type is required.";
    }

    if (!values.label) {
      errors.label = "The label field is required.";
    }

    if (values.fieldType == 1 || values.fieldType == 4 || values.fieldType == 5) {
      if (
        values?.phoneList.find((element) => element?.listElementValue === "")?.listElementValue ===
        ""
      ) {
        errors.fieldOpts = "The option field is required";
      }
      if (values?.phoneList.length < 2) {
        errors.fieldOpts = "You must at least add 2 options";
      }
    }

    // if(values.)
    return errors;
  };

  useEffect(() => {
    let familyIdFromUrl =
      families?.data && families?.data.filter((element) => element?.label == id)[0]?.id;
    setFamilyId(familyIdFromUrl);
  }, [families?.data, id]);

  let typeListObject = typesList && typesList.filter((element) => element?.id == fieldType);
  // console.log("item", typeListObject[0]?.list);

  // createNewField, createFieldOptions
  const saveNewField = async (e) => {
    e && e.preventDefault();

    let normalFieldPayload = {
      label: label,
      field_type_id: fieldType,
      family_id: familyId,
      hidden: transformBoolean(hidden),
      uniqueValue: transformBoolean(uniqueValue),
      required: transformBoolean(required),
    };

    if (
      form
        .getFieldsError()
        .map((element) => element.errors)
        .flat().length === 0
    ) {
      // console.log("normalFieldPayload", normalFieldPayload);
      dispatch(createNewField(normalFieldPayload));
    }
  };

  useEffect(() => {
    if (isFieldCreated === true) {
      setSuccessAlert(true);
    } else if (isError === true) {
      setFailAlert(true);
    }
    const timer = setTimeout(() => {
      dispatch(reset());
    }, 5000);
    return () => clearTimeout(timer);
  }, [isError, isFieldCreated, dispatch]);

  const saveNewFieldWithOptions = async (e) => {
    e && e.preventDefault();

    // console.log(
    //   "errrr",
    //   form
    //     .getFieldsError()
    //     .map((element) => element.errors)
    //     .flat().length
    // );

    var formData = new FormData();
    dataSource &&
      dataSource.forEach((item, i) => {
        for (var key in item) {
          // console.log("key foreach", item);
          let dd = `options[${i}][${key}]`;
          let value = item[key];
          formData.append(dd, value);

          // console.log("dd", dd);
        }
      });
    formData.append("label", label);
    formData.append("field_type_id", mem);
    formData.append("family_id", familyId);
    formData.append("hidden", transformBoolean(hidden));
    formData.append("uniqueValue", transformBoolean(uniqueValue));
    formData.append("required", transformBoolean(required));

    // if (
    //   form
    //     .getFieldsError()
    //     .map((element) => element.errors)
    //     .flat().length === 0
    // ) {
    // console.log("multiselectPayload", formData);
    dispatch(createNewField(formData));
    // }
  };

  const getTypes = useCallback(() => {
    setTypesList(types?.data);
    // try {
    //   setLoadTypes(true);
    //   const response = await MainService.getFieldTypes();
    //   setTypesList(response?.data?.data);
    //   setLoadTypes(false);
    // } catch (error) {
    //   setLoadTypes(false);
    //   console.log("errrrrr", error);
    // }
  }, []);

  useEffect(() => {
    if (isFieldCreated === true) {
      toastNotification("success", "Field was created successfully", "bottomRight", "4.5");
      setAddNewFieldModal(false);
      form.resetFields();
      let timer = setTimeout(() => {
        dispatch(reset());
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [dispatch, isFieldCreated]);

  useEffect(() => {
    if (isFieldUpdated === true) {
      toastNotification("success", "Field was updated successfully", "bottomRight", "4.5");
      setAddNewFieldModal(false);
      // setLabel("");
      let timer = setTimeout(() => {
        dispatch(reset());
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [dispatch, isFieldUpdated]);

  useEffect(() => {
    if (isOptDeleted === true) {
      toastNotification("success", "Option value was deleted successfully", "bottomRight", "4.5");
      let timer = setTimeout(() => {
        dispatch(reset());
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [dispatch, isOptDeleted]);

  useEffect(() => {
    getTypes();
  }, [getTypes]);

  const showOptionsInput = (typeId, typesArray) => {
    const item = typesArray && typesArray.find((element) => element?.id == typeId);
    // console.log("type item", item);
    if (item?.list === true) {
      return true;
    } else return false;
  };

  const fn = () => console.log("azx_1090");

  const getFieldsListOptions = useCallback(async () => {
    try {
      const response = await MainService.getSpecificField(updateFieldProps?.id);
      console.log("getFieldsListOptions res", response);
      setTableRows(response?.data?.data?.field_list_value);
    } catch (error) {
      console.log(`Error ${error}`);
    }
  }, [updateFieldProps?.id]);

  useEffect(() => {
    if (Object.keys(updateFieldProps).length !== 0) {
      getFieldsListOptions();
    }
  }, [getFieldsListOptions, updateFieldProps]);
  // const prefix = "cf_";

  const transformBoolean = (value) => (value === true ? 1 : 0);

  const updateLabelField = async (e) => {
    e && e.preventDefault();

    setErrors(validateAddField({ label }));

    var formData = new FormData();
    formData.append("label", label);
    formData.append("field_type_id", updateFieldProps?.field_type_id);
    formData.append("hidden", transformBoolean(hidden));
    formData.append("required", transformBoolean(required));
    formData.append("minValue", updateFieldProps?.minValue);
    formData.append("maxValue", updateFieldProps?.maxValue);
    formData.append("rank", updateFieldProps?.rank);
    formData.append("uniqueValue", transformBoolean(uniqueValue));
    formData.append("family_id", updateFieldProps?.family_id);

    let payload = { fieldId: updateFieldProps?.id, formData };
    // if (Object.keys(validateAddField({ label })).length === 0) {
    dispatch(updateSpecificField(payload));
    // }

    // try {
    //   const response = await MainService.updateSpecificField(payload);
    //   console.log("drawer updateLabelField", response);
    // } catch (error) {
    //   console.log(`Error ${error}`);
    // }
  };

  const onFinish = (values) => {
    console.log("Success:", values);
    // Object.keys(updateFieldProps).length === 0
    //   ? typeListObject[0]?.list === true
    //     ? saveNewFieldWithOptions()
    //     : saveNewField()
    //   : updateLabelField();
  };
  const onFinishFailed = (errorInfo) => {
    console.log("Failed:", errorInfo);
  };

  // console.log("label", label);

  return (
    <>
      {/* Start of drawer */}
      <Form
        name="basic"
        onFinish={onFinish}
        onFinishFailed={onFinishFailed}
        className="flex h-full flex-col divide-y divide-gray-200 bg-white"
        layout="vertical"
        form={form}
        // initialValues={{
        //   radioGroup: Object.keys(updateFieldProps).length !== 0 && updateFieldProps?.field_type_id,
        //   label: Object.keys(updateFieldProps).length !== 0 && updateFieldProps?.label,
        //   hidden: Object.keys(updateFieldProps).length !== 0 && updateFieldProps?.hidden,
        //   uniqueValue: Object.keys(updateFieldProps).length !== 0 && updateFieldProps?.uniqueValue,
        //   required: Object.keys(updateFieldProps).length !== 0 && updateFieldProps?.required,
        // }}
      >
        <div className="h-0 flex-1 overflow-y-auto">
          <div className="flex flex-1 flex-col justify-between">
            <div className="divide-y divide-gray-200 px-4 sm:px-6">
              <div className="space-y-6 pt-6 pb-5">
                <Form.Item
                  label="Field Type"
                  name="radioGroup"
                  rules={[
                    {
                      required: true,
                      message: "Field Type is required!",
                    },
                  ]}
                >
                  <Radio.Group
                    options={
                      types?.data &&
                      types?.data.map((element) => ({
                        label: (
                          <p className="flex justify-between items-center">
                            {element?.fieldType}
                            {displayRightIcon(element?.fieldType, 4, 4)}
                          </p>
                        ),
                        value: element?.id,
                      }))
                    }
                    optionType="button"
                    buttonStyle="solid"
                    onChange={(e) => {
                      setMem(e.target.value);
                      setFieldType(e.target.value);
                    }}
                    // defaultValue={
                    //   Object.keys(updateFieldProps).length !== 0 && updateFieldProps?.field_type_id
                    // }
                  />
                </Form.Item>
                <Form.Item
                  label="Label"
                  name="label"
                  rules={[
                    {
                      required: true,
                      message: "Field label is required!",
                    },
                  ]}
                >
                  <Input
                    // onFocus={(key) => console.log("dd", key)}
                    // placeholder="Label field"
                    onChange={(e) => {
                      console.log("e", e.target.value);
                      // e.stopPropagation();
                      setLabel(e.target.value);
                    }}
                  />
                </Form.Item>
                <div className="flex flex-row justify-between">
                  <Form.Item label="Hidden" name="hidden">
                    <Switch onChange={(value) => setHidden(value)} defaultChecked={hidden} />
                  </Form.Item>
                  <Form.Item label="Unique Value" name="uniqueValue">
                    <Switch
                      onChange={(value) => setUniqueValue(value)}
                      defaultChecked={uniqueValue}
                    />
                  </Form.Item>
                  <Form.Item label="Required" name="required">
                    <Switch onChange={(value) => setRequired(value)} defaultChecked={required} />
                  </Form.Item>
                </div>

                {/* <div>
                  <Input
                    label="Label"
                    inputName="label"
                    type="text"
                    htmlFor=""
                    required={true}
                    handleChange={(e) => setLabel(e.target.value)}
                    defaultValue={
                      Object.keys(updateFieldProps).length !== 0 ? updateFieldProps?.label : label
                    }
                  />
                  {errors?.label && <p className="text-sm text-red-500">{errors?.label}</p>}
                </div> */}
                {/* ---- if mem === "multiselect" || mem === "radio"---- */}
                {/*  ||
                            showOptionsInput(updateFieldProps?.field_type_id, typesList) */}
                {showOptionsInput(mem, typesList) && Object.keys(updateFieldProps).length === 0 ? (
                  // <>
                  //   <label
                  //     htmlFor="phone"
                  //     className="block text-sm font-medium text-gray-900"
                  //   >
                  //     <span className="mr-1 text-sm text-red-600">*</span>
                  //     Options
                  //   </label>
                  //   {phoneList &&
                  //     phoneList.map((phone, i) => (
                  //       <div key={i}>
                  //         <div className="flex rounded-md shadow-sm">
                  //           <div className="relative flex flex-grow items-stretch focus-within:z-10">
                  //             <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                  //               <ExclamationCircleIcon
                  //                 className="h-5 w-5 text-gray-400"
                  //                 aria-hidden="true"
                  //               />
                  //             </div>

                  //             <input
                  //               type="text"
                  //               name="listElementValue"
                  //               id={`phone${i}`}
                  //               value={phone?.phone}
                  //               onChange={(e) => handleInputPhoneChange(e, i)}
                  //               className="block w-full rounded-none rounded-l-md border border-gray-300 pl-10 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  //             />
                  //           </div>
                  //           {phoneList.length !== 1 && (
                  //             <button
                  //               type="button"
                  //               className="relative -ml-px inline-flex items-center space-x-2 rounded-r-md border border-gray-300  px-4 py-2 text-sm font-medium text-gray-700 bg-red-500 hover:bg-red-600 focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500"
                  //               onClick={() => handleRemoveClick(i)}
                  //             >
                  //               <XMarkIcon
                  //                 className="h-5 w-5 text-white"
                  //                 aria-hidden="true"
                  //               />
                  //             </button>
                  //           )}
                  //         </div>
                  //         {errors?.fieldOpts && (
                  //           <p className="text-sm text-red-500">{errors?.fieldOpts}</p>
                  //         )}
                  //       </div>
                  //     ))}
                  //   <div className="text-right mt-1">
                  //     <button
                  //       type="button"
                  //       className="link mr-2 text-sm text-[#46b7cf] hover:opacity-0.8"
                  //       onClick={(i) => {
                  //         handleAddClick();
                  //       }}
                  //     >
                  //       + Add another
                  //     </button>
                  //   </div>
                  // </>
                  <EditableTable
                    form={form}
                    dataSource={dataSource}
                    setDataSource={setDataSource}
                  />
                ) : // <DetailsTable
                //   newOptionArray={phoneList}
                //   getFieldsListOptions={fn}
                //   updateFieldProps={updateFieldProps}
                //   handleAddNewClick={handleAddNewClick}
                //   handleRemoveNewClick={handleRemoveNewClick}
                //   newFieldOptionsHandler={handleInputPhoneChange}
                //   errors={errors}
                // />
                null}
                {/* Details table goes here. */}
                {Object.keys(updateFieldProps).length !== 0 &&
                types?.data
                  .filter((element) => element?.list == true)
                  .filter((el) => el?.id == updateFieldProps?.field_type_id).length > 0 ? (
                  // <DetailsTable
                  //   updateFieldProps={updateFieldProps}
                  //   updateFieldsOptions={updateFieldsOptions}
                  //   setUpdateFieldsOptions={setUpdateFieldsOptions}
                  //   tableRows={tableRows}
                  //   setTableRows={setTableRows}
                  //   getFieldsListOptions={getFieldsListOptions}
                  // />
                  <EditableTable
                    form={form}
                    dataSource={dataSource}
                    setDataSource={setDataSource}
                    updateFieldProps={updateFieldProps}
                  />
                ) : null}
              </div>
            </div>
          </div>
        </div>
        <Form.Item className="flex mb-0 flex-shrink-0 justify-end border-t-2">
          {/* <Button
            className="mr-2"
            onClick={() => {
              form.resetFields();
              setUpdateFieldProps({});
              setAddNewFieldModal(false);
              setErrors({});
              // setFailAlert(false);
              // setSuccessAlert(false);
              setLabel("");
              setDataSource([]);
              setMem("");
              // console.log("cancel");
            }}
          >
            Close
          </Button> */}
          <Button
            type="primary"
            htmlType="submit"
            // onClick={() => {
            //   Object.keys(updateFieldProps).length === 0
            //     ? typeListObject[0]?.list === true
            //       ? saveNewFieldWithOptions()
            //       : saveNewField()
            //     : updateLabelField();
            // }}
            loading={isLoading}
          >
            Save
            {isLoading || loadSaveNewOption ? (
              <ArrowPathIcon className="flex justify-center items-center h-6 w-6 animate-spin text-white" />
            ) : (
              "Save"
            )}
          </Button>
        </Form.Item>
      </Form>
      {/* End of drawer */}
    </>
  );
};

export default SlideOver;
