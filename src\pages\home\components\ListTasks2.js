import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Divider,
  List,
  Skeleton,
  Space,
  Spin,
  Tooltip,
  Typography,
} from "antd";
import React, { memo, useEffect, useState } from "react";
import { useLocation, useParams } from "react-router-dom";
import VirtualList from "rc-virtual-list";
import { Clock } from "lucide-react";
import { FaFlag } from "react-icons/fa";
import { useSelector } from "react-redux";

import {
  DeleteOutlined,
  MessageOutlined,
  RestOutlined,
  VideoCameraOutlined,
} from "@ant-design/icons";
import { AvatarChat } from "components/Chat";
import { URL_ENV } from "index";
import { getName } from "pages/layouts/chat/utils/ConversationUtils";
import { humanDate } from "pages/voip/helpers/helpersFunc";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import useCompAct360 from "pages/tasks/activityDetails/CompAct360";
import MainService from "services/main.service";
import { toastNotification } from "components/ToastNotification";
import Confirm from "components/GenericModal";
import { getTokenRoom } from "new-redux/actions/visio.actions/createVisio";
import ChoiceIcons from "pages/components/ChoiceIcons";
import { displayPriorityColor } from "pages/tasks/helpers/handlePriorities";
import SelectPipelinesStages from "components/SelectPipelinesStages";
import Activity360 from "pages/tasks/activityDetails/Activity360";
import { setOpenTaskRoomDrawer } from "new-redux/actions/tasks.actions/handleTaskRoomDrawer";

function TaskLabel({
  item,
  openView360InDrawer,
  setTaskToUpdate,
  setDetailsTask,
  setIdTask,
  setOpenActivity360,
  setOpenChat = () => {},
  setSelectedKeySideBar = () => {},
}) {
  const handleClick = () => {
    setOpenChat(false);
    setSelectedKeySideBar("");
    setTaskToUpdate(item.id);
    setDetailsTask(item);
    setIdTask(item.id);
    setOpenActivity360(true);
  };

  const textStyle = {
    maxWidth: openView360InDrawer ? "325px" : "calc(100vw - 690px)",
    overflow: "hidden",
    textOverflow: "ellipsis",
    whiteSpace: "nowrap",
    // cursor: canUpdate ? "pointer" : "default",
    cursor: "pointer",
  };
  return (
    <Typography.Link style={textStyle} onClick={handleClick}>
      {item.label}
    </Typography.Link>
  );
}
const AllTasks2 = ({
  list,
  onUpdateTask = () => {},
  onDeleteTask = () => {},
  setDetailsTask = () => {},
  setIdTask = () => {},
  setCountTasks = () => {},
  listFilter,
  setRoomActivityId = () => {},
  setOpenChat = () => {},
  setSelectedKeySideBar = () => {},
}) => {
  const [loading, setLoading] = useState(false);
  const [selectedStage, setSelectedStage] = useState("");
  const [pipelines, setPipelines] = useState([]);
  const { user } = useSelector((state) => state.user);
  const { openView360InDrawer } = useSelector((state) => state?.vue360);
  const { iconsTasks } = useSelector((state) => state.dashboardRealTime);
  const dispatch = useDispatch();
  const [t] = useTranslation("common");

  const selected = (item) => {
    return iconsTasks?.tasks_type?.find((el) => el.id === item.tasks_type_id);
  };
  const {
    singleTaskData,
    setSingleTaskData,
    form,
    checkedItems,
    setCheckedItems,
    checkedFollowers,
    setCheckedFollowers,

    loadGuests,
    loadSpecificTask,
    taskToUpdate,
    setTaskToUpdate,
    ownersList,
    guestsList,
    guestsSearchQuery,
    setGuestsSearchQuery,
    setFollowersSearchQuery,
    guestsListPage,
    setGuestsListPage,
    guestsListLastPage,
    loadOwners,
    files,
    setFiles,
    openActivity360,
    setOpenActivity360,
    countChanges,
    setCountChanges,
    setSelectedFamilyMembers,
    setShowCardPopover,
    addOnsValues,
    setAddOnsValues,
  } = useCompAct360();
  const openChat = async (id) => {
    dispatch(setOpenTaskRoomDrawer(true));
    setRoomActivityId(id);
  };
  useEffect(() => {
    if (list.length > 0) {
      const getPipelines = async () => {
        // setLoading(true);
        try {
          const response = await MainService.getPipelinesByFamilyTask();

          setPipelines(response?.data?.data);
          setLoading(false);
        } catch (error) {
          setLoading(false);
          toastNotification("error", t("toasts.somethingWrong"));
          console.log(`Error ${error}`);
        }
      };
      getPipelines();
    }
  }, [list.length]);

  useEffect(() => {
    if (openActivity360 && singleTaskData?.id) {
      onUpdateTask(singleTaskData);
    }
  }, [openActivity360, singleTaskData]);

  const prioritiesList = [
    {
      value: "low",
      label: t("tasks.lowPriority"),
    },
    {
      value: "medium",
      label: t("tasks.mediumPriority"),
    },
    {
      value: "high",
      label: t("tasks.highPriority"),
    },
    {
      value: "urgent",
      label: t("tasks.urgentPriority"),
    },
  ];

  const handleDelete = async (id, item) => {
    try {
      let formData = new FormData();
      formData.append("id[]", id);
      const response = await MainService.deleteSpecificTask(formData);
      if (response?.status === 200) {
        onDeleteTask(item);

        toastNotification("success", t("toasts.taskDeleted"), "bottomRight");
      }
    } catch (error) {
      console.log(`Error ${error}`);
      toastNotification("error", t("toasts.somethingWrong"));
    }
  };
  return (
    <>
      <List>
        <VirtualList data={list} height={300} itemHeight={40} itemKey="id">
          {(item, index) => (
            <Card
              styles={{ body: { padding: 0 } }}
              stlyles
              style={{
                width: "100%",
                padding: 0,
                background: "white",
              }}
              key={index}
            >
              <List.Item style={{ padding: 0, margin: 8 }}>
                <List.Item.Meta
                  avatar={
                    <Tooltip
                      title={
                        iconsTasks?.tasks_type?.find(
                          (el) => el.id === item.tasks_type_id
                        )?.label
                      }
                    >
                      <Avatar
                        style={{
                          display: "flex",
                          alignItems: "center",
                          background: iconsTasks?.tasks_type?.find(
                            (el) => el.id === selected(item)?.id ?? "black"
                          )?.color,
                        }}
                        gap={5}
                      >
                        <span>
                          <ChoiceIcons
                            icon={selected(item)?.icons}
                            fontSize={"16px"}
                            width={18}
                            color={"white"}
                          />
                        </span>
                      </Avatar>
                    </Tooltip>
                  }
                  title={
                    <div className="flex items-center justify-between pr-2">
                      <div className="flex items-center space-x-2 ">
                        {item.priority ? (
                          <Tooltip
                            title={
                              prioritiesList.find(
                                (el) => el.value === item.priority
                              )?.label
                            }
                          >
                            <FaFlag
                              style={{
                                color: displayPriorityColor(item.priority),
                              }}
                            />
                          </Tooltip>
                        ) : null}
                        {/* <ChoiceIcons icon={selected(item)?.icons} />{" "} */}
                        <TaskLabel
                          item={item}
                          openView360InDrawer={openView360InDrawer}
                          setTaskToUpdate={setTaskToUpdate}
                          setDetailsTask={setDetailsTask}
                          setIdTask={setIdTask}
                          setOpenActivity360={setOpenActivity360}
                          setOpenChat={setOpenChat}
                          setSelectedKeySideBar={setSelectedKeySideBar}
                        />
                      </div>

                      <div className="flex items-center space-x-1 text-slate-500">
                        <Clock size={15} />
                        <div className="flex items-center space-x-1 whitespace-nowrap text-xs">
                          <div>
                            {/* {t("mailing.NewMsg.from")}:{" "} */}
                            {humanDate(
                              `${item?.start_date} ${item?.start_time}`,
                              t
                            )}{" "}
                            -{" "}
                            {humanDate(
                              `${item?.end_date} ${item?.end_time}`,
                              t
                            )}
                          </div>
                        </div>
                      </div>

                      {/* {item.created_at ? (
                                        <Tag>
                                          <MdMoreTime /> : {humanDate(item.created_at, t)}
                                        </Tag>
                                      ) : (
                                        ""
                                      )} */}
                    </div>
                  }
                  description={
                    <div className="mt-2 flex items-center justify-between gap-x-2 pr-2">
                      <Space split={<Divider type="vertical" />}>
                        <Space
                          style={{
                            textAlign: "center",
                            width: "max-content",
                          }}
                        >
                          <Typography.Text>
                            {t("tasks.creatorRole")}:
                          </Typography.Text>
                          <Tooltip title={item?.creator?.label}>
                            <span>
                              <AvatarChat
                                fontSize="0.875rem"
                                url={
                                  URL_ENV?.REACT_APP_BASE_URL +
                                  URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                                  item?.creator?.avatar
                                }
                                type="user"
                                size={24}
                                height={10}
                                width={10}
                                name={getName(item.creator?.label, "avatar")}
                                hasImage={
                                  item?.creator?.avatar &&
                                  item?.creator?.avatar !== "/storage/uploads/"
                                }
                              />
                            </span>
                          </Tooltip>
                        </Space>

                        <Space
                          style={{
                            textAlign: "center",
                            width: "max-content",
                          }}
                        >
                          <Typography.Text>{t("tasks.owner")}:</Typography.Text>
                          <Tooltip title={item?.owner_id?.label}>
                            <span>
                              <AvatarChat
                                fontSize="0.875rem"
                                url={
                                  URL_ENV?.REACT_APP_BASE_URL +
                                  URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                                  item?.owner_id?.avatar
                                }
                                type="user"
                                size={24}
                                height={10}
                                width={10}
                                name={getName(item.owner_id?.label, "avatar")}
                                hasImage={
                                  item?.owner_id?.avatar &&
                                  item?.owner_id?.avatar !== "/storage/uploads/"
                                }
                              />
                            </span>
                          </Tooltip>
                        </Space>
                        {item?.guests?.length > 0 && (
                          <Space
                            style={{
                              textAlign: "center",
                              width: "max-content",
                            }}
                          >
                            <Typography.Text>
                              {t("visio.guest", {
                                plural: item?.guests?.length > 1 ? "s" : "",
                              })}
                              :
                            </Typography.Text>
                            <Avatar.Group
                              maxCount={2}
                              maxPopoverTrigger="click"
                              maxStyle={{
                                color: "#f56a00",
                                backgroundColor: "#fde3cf",
                                cursor: "pointer",
                              }}
                              size={26}
                            >
                              {item?.guests.map((el, index) => (
                                <Tooltip
                                  title={getName(el.label, "name")}
                                  key={index}
                                >
                                  <span key={index}>
                                    <AvatarChat
                                      fontSize="0.72rem"
                                      url={
                                        URL_ENV?.REACT_APP_BASE_URL +
                                        URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                                        el?.avatar
                                      }
                                      type="user"
                                      size={26}
                                      height={6}
                                      width={6}
                                      name={getName(el.label, "avatar")}
                                      hasImage={[
                                        "jpg",
                                        "jpeg",
                                        "png",
                                        "webp",
                                        "gif",
                                        "bmp",
                                        "tiff",
                                        "tif",
                                        "svg",
                                        "avif",
                                        "jfif",
                                      ].includes(el?.avatar?.split(".")?.pop())}
                                    />
                                  </span>
                                </Tooltip>
                              ))}
                            </Avatar.Group>{" "}
                          </Space>
                        )}
                        {item?.followers?.length > 0 && (
                          <Space
                            style={{
                              textAlign: "center",
                              width: "max-content",
                            }}
                          >
                            <Typography.Text>
                              {t("tasks.followers", {
                                s: item?.followers?.length > 1 ? "s" : "",
                              })}
                              :
                            </Typography.Text>
                            <Avatar.Group
                              maxCount={2}
                              maxPopoverTrigger="click"
                              maxStyle={{
                                color: "#f56a00",
                                backgroundColor: "#fde3cf",
                                cursor: "pointer",
                              }}
                              size={26}
                            >
                              {item.followers.map((el, index) => (
                                <Tooltip
                                  title={getName(el.label, "name")}
                                  key={index}
                                >
                                  <span key={index}>
                                    <AvatarChat
                                      fontSize="0.72rem"
                                      url={
                                        URL_ENV?.REACT_APP_BASE_URL +
                                        URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                                        el?.avatar
                                      }
                                      type="user"
                                      size={26}
                                      height={6}
                                      width={6}
                                      name={getName(el.label, "avatar")}
                                      hasImage={[
                                        "jpg",
                                        "jpeg",
                                        "png",
                                        "webp",
                                        "gif",
                                        "bmp",
                                        "tiff",
                                        "tif",
                                        "svg",
                                        "avif",
                                        "jfif",
                                      ]?.includes(
                                        el?.avatar?.split(".")?.pop()
                                      )}
                                    />
                                  </span>
                                </Tooltip>
                              ))}
                            </Avatar.Group>{" "}
                          </Space>
                        )}
                        {item.pipeline_label &&
                        (item.owner_id.id === user.id ||
                          item.creator?.id === user.id) ? (
                          <SelectPipelinesStages
                            stages={pipelines}
                            record={item}
                            iconsTasks={iconsTasks?.tasks_types || []}
                            setSelectedStage={setSelectedStage}
                            selectedStage={selectedStage}
                            setList={onUpdateTask}
                            setCountTasks={setCountTasks}
                            listFilter={listFilter}
                            source=""
                            size="small"
                          />
                        ) : null}
                      </Space>
                      <span className="flex items-center gap-x-2">
                        {item?.can_create_room === 1 &&
                          user?.access?.["chat"] === "1" && (
                            <Tooltip title={t("tasks.openChatRoom")}>
                              <Button
                                onClick={() => openChat(item?.id)}
                                type="text"
                                shape="circle"
                                style={{
                                  color: " #6b7280",
                                }}
                                icon={<MessageOutlined />}
                              />
                            </Tooltip>
                          )}

                        {item?.tasks_type_id === 3 ? (
                          <Tooltip title={t("chat.header.visio.join")}>
                            <Button
                              icon={<VideoCameraOutlined />}
                              shape="circle"
                              type="text"
                              className="hover:!bg-blue-50"
                              style={{
                                color: "#3b82f6 ",
                              }}
                              //   className="relative "
                              onClick={() => {
                                dispatch(
                                  getTokenRoom({
                                    room: item?.location,
                                    errorText1: t("toasts.errorFetchApi"),
                                    errorText2: t("toasts.errorRoomNotFound"),
                                  })
                                );
                              }}
                            />
                          </Tooltip>
                        ) : null}
                        {item?.owner_id?.id === user.id ||
                        item?.creator?.id === user.id ? (
                          <Button
                            onClick={() => {
                              Confirm(
                                `Delete "${item.label}" `,
                                "Confirm",
                                <RestOutlined style={{ color: "red" }} />,
                                function func() {
                                  return handleDelete(item.id, item);
                                },
                                true
                              );
                            }}
                            danger
                            type="text"
                            shape="circle"
                            icon={<DeleteOutlined />}
                          />
                        ) : null}
                      </span>
                    </div>
                  }
                />
              </List.Item>
            </Card>
          )}
        </VirtualList>
        {loading ? <Spin /> : ""}
      </List>
      {/* {openActivity360 ? ( */}
      {openActivity360 ? (
        <Activity360
          key={taskToUpdate ? taskToUpdate : 1}
          openActivity360={openActivity360}
          setOpenActivity360={setOpenActivity360}
          taskToUpdate={taskToUpdate}
          singleTaskData={singleTaskData}
          setSingleTaskData={setSingleTaskData}
          loadSpecificTask={loadSpecificTask}
          tasksTypes={iconsTasks?.tasks_type || []}
          setTaskToUpdate={setTaskToUpdate}
          pipelines={pipelines}
          guestsList={guestsList}
          checkedItems={checkedItems}
          guestsSearchQuery={guestsSearchQuery}
          setGuestsSearchQuery={setGuestsSearchQuery}
          guestsListPage={guestsListPage}
          setGuestsListPage={setGuestsListPage}
          guestsListLastPage={guestsListLastPage}
          setCheckedItems={setCheckedItems}
          ownersList={ownersList}
          checkedFollowers={checkedFollowers}
          setCheckedFollowers={setCheckedFollowers}
          setFollowersSearchQuery={setFollowersSearchQuery}
          loadOwners={loadOwners}
          loadGuests={loadGuests}
          addOnsValues={addOnsValues}
          setAddOnsValues={setAddOnsValues}
          files={files}
          setFiles={setFiles}
          countChanges={countChanges}
          setCountChanges={setCountChanges}
          setSelectedFamilyMembers={setSelectedFamilyMembers}
          form={form}
          setShowCardPopover={setShowCardPopover}
        />
      ) : null}
    </>
  );
};

export default memo(AllTasks2);
