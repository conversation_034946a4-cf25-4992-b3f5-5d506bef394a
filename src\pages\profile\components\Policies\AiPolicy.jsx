import React from "react";
import { Typography, Card } from "antd";

const { Title, Text, Paragraph } = Typography;

const AIPolicy = ({
  contactEmail,
  responsiblePerson,
  version,
  updateDate,
  backgroundColor = "white",
}) => {
  return (
    <div className="mx-auto rounded-lg  p-6" style={{ backgroundColor }}>
      <Title level={2} className="mb-6 pb-2 text-center">
        Politique d'utilisation de l'Intelligence Artificielle (IA)
      </Title>

      <div className="mt-3 flex max-h-[calc(100vh-300px)] flex-col gap-4 overflow-auto">
        {/* Section 1 */}
        <section>
          <Title level={5} className="mb-3 pb-1">
            1. Objectif de l'IA dans l'application
          </Title>
          <Paragraph className="mb-3">
            Notre application intègre des fonctionnalités d'intelligence
            artificielle dans le but d'améliorer l'expérience utilisateur,
            d'automatiser certaines tâches et de fournir des services
            personnalisés. L'IA peut être utilisée notamment pour :
          </Paragraph>
          <ul className="mb-3 list-disc pl-6">
            <li className="mb-2">Fournir des recommandations</li>
            <li className="mb-2">
              Répondre automatiquement à certaines questions
            </li>
            <li className="mb-2">
              Analyser des données pour aider à la prise de décision
            </li>
            <li>Automatiser certaines actions répétitives</li>
          </ul>
        </section>

        {/* Section 2 */}
        <section>
          <Title level={5} className="mb-3 pb-1">
            2. Transparence
          </Title>
          <Paragraph>
            Nous nous engageons à informer clairement nos utilisateurs
            lorsqu'ils interagissent avec un système utilisant l'intelligence
            artificielle. Toute interaction avec une IA est signalée, et le rôle
            de l'IA est explicité.
          </Paragraph>
        </section>

        {/* Section 3 */}
        <section>
          <Title level={5} className="mb-3 pb-1">
            3. Protection des données
          </Title>
          <Paragraph>
            Les données personnelles collectées et traitées par l'IA sont
            utilisées dans le strict respect de notre politique de
            confidentialité et des réglementations en vigueur (notamment le RGPD
            si applicable). Les traitements automatiques respectent les
            principes de minimisation, de finalité et de proportionnalité.
          </Paragraph>
        </section>

        {/* Section 4 */}
        <section>
          <Title level={5} className="mb-3 pb-1">
            4. Consentement et contrôle utilisateur
          </Title>
          <ul className="mb-3 list-disc pl-6">
            <li className="mb-2">
              Les utilisateurs sont informés et peuvent, dans la mesure du
              possible, choisir d'activer ou de désactiver les fonctionnalités
              basées sur l'IA.
            </li>
            <li>
              Certaines fonctionnalités peuvent nécessiter un consentement
              explicite.
            </li>
          </ul>
        </section>

        {/* Section 5 */}
        <section>
          <Title level={5} className="mb-3 pb-1">
            5. Éthique et biais
          </Title>
          <Paragraph>
            Nous mettons en œuvre des mécanismes pour limiter les biais
            algorithmiques dans les systèmes d'IA. Notre objectif est de fournir
            un service juste, impartial et non discriminatoire.
          </Paragraph>
        </section>

        {/* Section 6 */}
        <section>
          <Title level={5} className="mb-3 pb-1">
            6. Responsabilité
          </Title>
          <Paragraph>
            Bien que l'IA puisse suggérer ou automatiser certaines actions,
            l'utilisateur conserve toujours le contrôle final sur les décisions
            importantes. Nous restons responsables du bon fonctionnement de l'IA
            et des impacts qu'elle peut avoir.
          </Paragraph>
        </section>

        {/* Section 7 */}
        <section>
          <Title level={5} className="mb-3 pb-1">
            7. Amélioration continue
          </Title>
          <Paragraph>
            Les systèmes d'IA sont régulièrement mis à jour et évalués afin
            d'améliorer leur efficacité, leur fiabilité et leur conformité
            éthique.
          </Paragraph>
        </section>

        {/* Section 8 */}
        <section>
          <Title level={5} className="mb-3 pb-1">
            8. Contact
          </Title>
          <Paragraph className="mb-3">
            Pour toute question concernant cette politique ou pour exercer vos
            droits en lien avec l'utilisation de l'IA, vous pouvez nous
            contacter à :
          </Paragraph>
          <Card size="small" className="bg-gray-50">
            <Paragraph className="mb-1">
              <Text strong>Email :</Text> {contactEmail}
            </Paragraph>
            <Paragraph>
              <Text strong>Responsable :</Text> {responsiblePerson}
            </Paragraph>
          </Card>
        </section>

        {/* Version info */}
        <div className="mt-8 text-right text-sm text-gray-500">
          <Text type="secondary">
            Version {version} - Mise à jour le {updateDate}
          </Text>
        </div>
      </div>
    </div>
  );
};

// Valeurs par défaut pour les props
AIPolicy.defaultProps = {
  contactEmail: "<EMAIL>",
  responsiblePerson: "Responsable IA COMUNIK",
  version: "1.0",
  updateDate: new Date().toLocaleDateString("fr-FR"),
};

export default AIPolicy;
