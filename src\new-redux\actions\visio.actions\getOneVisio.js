import { GET_ONE_VISIO_SUCCESS, GET_ONE_VISIO_ERROR, IS_LOADING_VISIO } from '../../constants'
import MainService from '../../../services/main.service'
import { toastNotification } from '../../../components/ToastNotification';

export const getOneVisio = (id) => async (dispatch) => {
    try {
        dispatch({ type: IS_LOADING_VISIO });
        const response = await MainService.getVisioById(id)
        dispatch({
            type: GET_ONE_VISIO_SUCCESS,
            payload: response?.data.data,
        })
        console.log('ok')
    } catch (error) {
        dispatch({
            type: GET_ONE_VISIO_ERROR,
            payload: error,
        })
        toastNotification(
            "error",
            "Something went wrong, please try again!",
            "topRight"
        );
    }
}
