import React, { useEffect, useRef, useState } from "react";

import HighchartsReact from "highcharts-react-official";
import Highcharts from "highcharts";
import HighchartsMore from "highcharts/highcharts-more"; // Import the necessary module
import ItemSeries from "highcharts/modules/item-series";
import Highcharts3D from "highcharts/highcharts-3d";
import HighchartsDrilldown from "highcharts/modules/drilldown";
import annotationsAdvanced from "highcharts/modules/annotations-advanced"; // Importer le module d'annotations avancées

import solidGauge from "highcharts/modules/solid-gauge";

// Appliquer le module solidgauge
import { generateAxios } from "services/axiosInstance";
import { URL_ENV } from "index";
import i18next from "i18next";
HighchartsDrilldown(Highcharts);
annotationsAdvanced(Highcharts);

ItemSeries(Highcharts);
Highcharts3D(Highcharts);
// Initialize the modules
HighchartsMore(Highcharts);
require("highcharts/modules/exporting")(Highcharts);
require("highcharts/modules/export-data")(Highcharts);
require("highcharts/modules/solid-gauge")(Highcharts);

// let csvToJson = require("convert-csv-to-json");
// let JSON = csvToJson
//   .fieldDelimiter(",")
//   .formatValueByType()
//   .getJsonFromCsv("test.csv");
function getLastWordCapitalized(key) {
  const words = key.split("_");
  const lastWord = words[words.length - 1];
  return lastWord.charAt(0).toUpperCase() + lastWord.slice(1);
}

const DonutChart = () => {
  const donutChartRef = useRef(null);
  const options = {
    chart: {
      type: "pie",
      custom: {},
      height: 280,
      events: {
        render() {
          const chart = this,
            series = chart.series[0];
          let customLabel = chart.options.chart.custom.label;

          if (!customLabel) {
            customLabel = chart.options.chart.custom.label = chart.renderer
              .label("Total Calls <br/>" + "<strong>100</strong>")
              .css({
                color: "#000",
                textAnchor: "middle",
              })
              .add();
          }

          const x = series.center[0] + chart.plotLeft,
            y =
              series.center[1] + chart.plotTop - customLabel.attr("height") / 2;

          customLabel.attr({
            x,
            y,
          });
          // Définir la taille de la police en fonction du diamètre du graphique
          customLabel.css({
            fontSize: `${series.center[2] / 12}px`,
          });
        },
      },
    },
    accessibility: {
      point: {
        valueSuffix: "%",
      },
    },
    title: {
      text: "",
    },
    // subtitle: {
    //   text: 'Source: <a href="https://www.ssb.no/transport-og-reiseliv/faktaside/bil-og-transport">SSB</a>',
    // },
    tooltip: {
      pointFormat: `${i18next.t(
        "common:dashboard.percentage"
      )}: <b>{point.percentage:.0f}%</b>`,
    },
    legend: {
      enabled: true,
    },
    plotOptions: {
      series: {
        allowPointSelect: true,
        cursor: "pointer",
        borderRadius: 8,
        dataLabels: [
          {
            enabled: false,
            distance: 5,
            format: "{point.name}",
          },
          {
            enabled: true,
            distance: -15,
            format: "{point.percentage:.0f}%",
            style: {
              fontSize: "0.9em",
            },
          },
        ],
        showInLegend: true,
      },
    },
    series: [
      {
        name: i18next.t("common:chat.message_system.removed_auth_user_modal"),
        colorByPoint: true,
        innerSize: "73%",

        data: [
          { name: "27 Inbounds ", y: 27 },
          { name: "25 Outbounds ", y: 25 },
          { name: "18 Missed ", y: 18 },
          { name: "15 Internal ", y: 15 },
        ],
      },
    ],

    // exporting: {
    //   csv: {
    //     itemDelimiter: ";",
    //   },
    // },
  };

  return (
    <HighchartsReact
      constructorType={"chart"}
      highcharts={Highcharts}
      options={options}
      ref={donutChartRef}
    />
  );
};

export const PetroleumProductsChart = ({ data }) => {
  const options = {
    title: {
      text: "Sales of petroleum products March, Norway",
      align: "left",
    },
    xAxis: {
      categories: [
        "Jet fuel",
        "Duty-free diesel",
        "Petrol",
        "Diesel",
        "Gas oil",
      ],
    },
    yAxis: {
      title: {
        text: "Million liters",
      },
    },
    tooltip: {
      valueSuffix: " million liters",
    },
    plotOptions: {
      series: {
        borderRadius: "25%",
      },
    },
    series: [
      {
        type: "column",
        name: "Completed",
        data: [59, 83, 65, 228, 184],
      },
      {
        type: "column",
        name: "Overdue",
        data: [24, 79, 72, 240, 167],
      },
      {
        type: "column",
        name: "In progress",
        data: [58, 88, 75, 250, 176],
      },
      {
        type: "line",
        step: "center",
        name: "Average",
        data: [47, 83.33, 70.66, 239.33, 175.66],
        marker: {
          lineWidth: 2,
          lineColor: Highcharts.getOptions().colors[3],
          fillColor: "white",
        },
      },
      {
        type: "pie",
        name: "Total",
        data: [
          {
            name: "Completed",
            y: 619,
            color: Highcharts.getOptions().colors[0],
            dataLabels: {
              enabled: true,
              distance: -50,
              format: "{point.total} M",
              style: {
                fontSize: "15px",
              },
            },
          },
          {
            name: "Overdue",
            y: 586,
            color: Highcharts.getOptions().colors[1],
          },
          {
            name: "In progress",
            y: 647,
            color: Highcharts.getOptions().colors[2],
          },
        ],
        center: [75, 65],
        size: 100,
        innerSize: "70%",
        showInLegend: false,
        dataLabels: {
          enabled: false,
        },
      },
    ],
  };

  return <HighchartsReact highcharts={Highcharts} options={options} />;
};
export const PieChart = React.memo(({ data, total, name, height = 350 }) => {
  const processedData = data?.map((item) => ({
    name: item.name,
    y: item.y,
    color: item.color,
    percentage: ((item.y / total) * 100).toFixed(2),
  }));

  const options = {
    chart: {
      type: "pie",
      height,
    },
    title: {
      text: name || "",
      align: "left",
    },
    legend: {
      labelFormat: '{name} <span style="color:#475569">{y}</span>',
    },
    tooltip: {
      pointFormat: `${i18next.t(
        "common:dashboard.percentage"
      )}: <b>{point.percentage:.0f}%</b>`,
    },

    subtitle: {
      text: "",
    },
    plotOptions: {
      series: {
        allowPointSelect: true,
        cursor: "pointer",
        dataLabels: [
          {
            enabled: true,
            distance: 1,
            format:
              '<span style="font-size: 1.2em"><b>{point.name}</b>' +
              "</span>: " +
              '<span style="opacity: 0.6">{point.y} ' +
              "</span>",
          },
          {
            enabled: true,
            distance: -40,
            format: "{point.y}",
            style: {
              fontSize: "1.2em",
              textOutline: "none",
              opacity: 0.7,
            },
            filter: {
              operator: ">",
              property: i18next.t(
                "common:chat.message_system.removed_auth_user_modal"
              ),
              value: 0,
            },
          },
        ],
      },
    },
    series: [
      {
        name: "Pourcentage",
        colorByPoint: true,
        data: processedData,
      },
    ],
    exporting: {
      buttons: {
        contextButton: {
          menuItems: [
            "viewFullscreen", // Keep fullscreen option
            "printChart", // Keep Print option
            "separator",
            "downloadPNG", // Keep PNG download option
            "downloadJPEG", // Keep JPEG download option
            "downloadPDF", // Keep PDF download option
            "downloadSVG", // Keep SVG download option
            "downloadCSV", // Keep CSV download option
            "downloadXLS", // Keep XLS download option
            // 'viewData' // Remove or comment this line to exclude "View Data"
          ],
        },
      },
    },
  };

  return (
    <div>
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
});
export const PieChartWithPercent = ({ data, total, name }) => {
  const options = {
    chart: {
      type: "pie",
      height: 350,
    },
    title: {
      text: data?.name || "",
      align: "left",
    },
    tooltip: {
      valueSuffix: "%",
    },
    subtitle: {
      text: "",
    },
    plotOptions: {
      series: {
        allowPointSelect: true,
        cursor: "pointer",
        dataLabels: [
          {
            enabled: true,
            distance: 20,
          },
          {
            enabled: true,
            distance: -40,
            format: "{point.percentage:.1f}%",
            style: {
              fontSize: "1.2em",
              textOutline: "none",
              opacity: 0.7,
            },
            filter: {
              operator: ">",
              property: i18next.t("common:dashboard.percentage"),
              value: 10,
            },
          },
        ],
      },
    },
    series: [
      {
        name: i18next.t("common:dashboard.percentage"),
        colorByPoint: true,
        data: data.data,
      },
    ],
    exporting: {
      buttons: {
        contextButton: {
          menuItems: [
            "viewFullscreen", // Keep fullscreen option
            "printChart", // Keep Print option
            "separator",
            "downloadPNG", // Keep PNG download option
            "downloadJPEG", // Keep JPEG download option
            "downloadPDF", // Keep PDF download option
            "downloadSVG", // Keep SVG download option
            "downloadCSV", // Keep CSV download option
            "downloadXLS", // Keep XLS download option
            // 'viewData' // Remove or comment this line to exclude "View Data"
          ],
        },
      },
    },
  };

  return (
    <div>
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
};

export const PieChartWithLegend = React.memo(
  ({
    data,
    isExistDate = true,
    height = 350,
    alignLegend = {},
    exporting = true,
  }) => {
    const options = {
      chart: {
        plotBackgroundColor: null,
        backgroundColor: "transparent",
        plotBorderWidth: null,
        plotShadow: false,
        type: "pie",
        height: height,
        spacing: [0, 0, 0, 0], // Remove all spacing
      },
      title: {
        text: data?.name,
        align: "left",
      },
      subtitle: {
        text: !isExistDate
          ? i18next.t("common:dashboard.independantNumber")
          : "",
        align: "left",
      },
      tooltip: {
        useHTML: true, // Enable HTML for the tooltip
        formatter: function () {
          return `<div style="display: flex; justify-content: space-between; align-items: center;color:${this.point.color}">
                        <span>${this.point.name}</span>
                       &nbsp; <b>${this.point.y}</b>
                    </div>`;
        },
      },
      accessibility: {
        point: {
          valueSuffix: "%",
        },
      },
      plotOptions: {
        pie: {
          enabled: false,
          dataLabels: {
            enabled: true,
            formatter: function () {
              const percentage = this.percentage;
              return this.percentage > 0 && percentage % 1 === 0 // Check if there's no decimal part
                ? `${Math.round(percentage)}%` // Show whole number
                : this.percentage > 0 && percentage % 1 !== 0
                ? `${percentage.toFixed(1)}%`
                : ""; // Show one decimal place
            },
            distance: -30,
          },
          showInLegend: true,
        },
      },
      legend: {
        ...alignLegend,
      },
      exporting: {
        enabled: exporting, // Disable exporting
      },
      series: [
        {
          name: "",
          colorByPoint: true,
          data: data?.data,
        },
      ],
    };

    return <HighchartsReact highcharts={Highcharts} options={options} />;
  }
);

// export const GaugeComponent = ({ data }) => {
//   const gaugeOptions = {
//     chart: {
//       type: "solidgauge",
//       height: 350, // Réduire la hauteur
//     },
//     title: null,
//     pane: {
//       center: ["50%", "85%"],
//       size: "100%", // Ajuster la taille du panneau
//       startAngle: -90,
//       endAngle: 90,
//       background: {
//         backgroundColor:
//           Highcharts.defaultOptions.legend.backgroundColor || "#fafafa",
//         borderRadius: 5,
//         innerRadius: "60%", // Réduire le rayon intérieur
//         outerRadius: "100%", // Garder le rayon extérieur
//         shape: "arc",
//       },
//     },
//     exporting: {
//       enabled: false,
//     },
//     tooltip: {
//       enabled: false,
//     },
//     yAxis: {
//       min: 0,
//       //   max: 200,
//       stops: [
//         [0.1, data?.jauge_1?.color],
//         [0.9, data?.jauge_2?.color],
//       ],
//       lineWidth: 0,
//       tickWidth: 0,
//       minorTickInterval: null,
//       tickAmount: 2,
//       tickInterval: data?.jauge_2?.max ? data.jauge_2.max / 4 : null,
//       title: {
//         y: -50, // Ajuster la position du titre
//         text: null,
//       },
//       labels: {
//         enabled: true,
//         y: 10, // Ajuster la position des étiquettes
//         style: {
//           fontSize: "10px", // Réduire la taille de la police
//         },
//         formatter: function () {
//           return this.value === 0 || this.value === this.axis.max
//             ? this.value
//             : "";
//         },
//       },
//     },
//     plotOptions: {
//       solidgauge: {
//         borderRadius: 3,
//         dataLabels: {
//           y: 5,
//           borderWidth: 0,
//           useHTML: true,
//           style: {
//             fontSize: "14px", // Ajuster la taille de la police des étiquettes de données
//           },
//         },
//       },
//     },
//   };

//   // Options pour le gauge de tâches complètes
//   const speedGaugeOptions = {
//     ...gaugeOptions,
//     yAxis: {
//       ...gaugeOptions.yAxis,
//       title: {
//         text: data?.jauge_2?.name,
//         y: 20,
//         style: {
//           fontWeight: "bold",
//           fontSize: "14px", // Réduire la taille de la police du titre
//         },
//       },
//       max: data?.jauge_2?.max ? data?.jauge_2?.max : 0,
//     },
//     series: [
//       {
//         name: "Completed Tasks",
//         data: [data?.jauge_2?.data],
//         dataLabels: {
//           format:
//             '<div style="text-align:center">' +
//             '<span style="font-size:24px; font-weight: bold;">{y}</span><br/>' + // Réduire la taille de la police
//             '<span style="font-size:10px;opacity:0.4"></span>' +
//             "</div>",
//         },
//       },
//     ],
//   };

//   // Options pour le gauge de tâches en retard
//   const rpmGaugeOptions = {
//     ...gaugeOptions,
//     yAxis: {
//       ...gaugeOptions.yAxis,
//       title: {
//         text: data?.jauge_1?.name,
//         y: 20,
//         style: {
//           fontWeight: "bold",
//           fontSize: "14px", // Réduire la taille de la police du titre
//         },
//       },
//       max: data?.jauge_1?.max ? data?.jauge_1?.max : 0,
//     },
//     series: [
//       {
//         name: "",
//         data: [data?.jauge_1?.data],
//         dataLabels: {
//           format:
//             '<div style="text-align:center">' +
//             '<span style="font-size:24px; font-weight: bold;">{y}</span><br/>' + // Réduire la taille de la police
//             '<span style="font-size:10px;opacity:0.4"></span>' +
//             "</div>",
//         },
//       },
//     ],
//   };

//   return (
//     <div className="flex  gap-1 bg-white px-2">
//       <div id="container-rpm" style={{ width: "50%", height: "100%" }}>
//         <HighchartsReact highcharts={Highcharts} options={rpmGaugeOptions} />
//       </div>
//       <div id="container-speed" style={{ width: "50%", height: "100%" }}>
//         <HighchartsReact highcharts={Highcharts} options={speedGaugeOptions} />
//       </div>
//     </div>
//   );
// };
export const TaskBarChart = () => {
  const options = {
    chart: {
      type: "bar",
      height: 350,
    },
    legend: {
      enabled: false,
    },
    title: {
      text: "",
    },
    xAxis: {
      categories: ["Completed", "Overdue", "To Do", "In Progress"],
      title: {
        text: null,
      },
    },
    yAxis: {
      min: 0,
      title: {
        text: "Nombre de Tâches",
        align: "high",
      },
      labels: {
        overflow: "justify",
      },
    },
    tooltip: {
      valueSuffix: " tâches",
    },
    plotOptions: {
      bar: {
        dataLabels: {
          enabled: true,
        },
      },
    },
    series: [
      {
        name: "Tâches",
        data: [
          { y: 5, color: "#28a745" }, // Completed
          { y: 2, color: "#dc3545" }, // Overdue
          { y: 8, color: "#ffc107" }, // To Do
          { y: 3, color: "#17a2b8" }, // In Progress
        ],
      },
    ],
    exporting: {
      buttons: {
        contextButton: {
          menuItems: [
            "viewFullscreen", // Keep fullscreen option
            "printChart", // Keep Print option
            "separator",
            "downloadPNG", // Keep PNG download option
            "downloadJPEG", // Keep JPEG download option
            "downloadPDF", // Keep PDF download option
            "downloadSVG", // Keep SVG download option
            "downloadCSV", // Keep CSV download option
            "downloadXLS", // Keep XLS download option
            // 'viewData' // Remove or comment this line to exclude "View Data"
          ],
        },
      },
    },
  };

  return (
    <div>
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
};
// export const TaskGaugeChart = () => {
//   const totalTasks = 18; // Total des tâches
//   const completedTasks = 5; // Tâches complètes
//   const overdueTasks = 2; // Tâches en retard
//   const toDoTasks = 8; // Tâches à faire
//   const inProgressTasks = 3; // Tâches en cours

//   const options = {
//     chart: {
//       type: "gauge",
//       plotBackgroundColor: null,
//       plotBackgroundImage: null,
//       plotBorderWidth: 0,
//       plotShadow: false,
//     },
//     title: {
//       text: "Répartition des Tâches",
//     },
//     pane: {
//       startAngle: -150,
//       endAngle: 150,
//       background: [
//         {
//           backgroundColor:
//             Highcharts.defaultOptions.chart.backgroundColor || "#EEE",
//           borderWidth: 0,
//           outerRadius: "109%",
//           innerRadius: "101%",
//         },
//       ],
//     },
//     yAxis: {
//       min: 0,
//       max: totalTasks,
//       minorTickInterval: "auto",
//       title: {
//         text: "Nombre de Tâches",
//       },
//       plotBands: [
//         {
//           from: 0,
//           to: completedTasks,
//           color: "#28a745", // Vert pour les tâches complètes
//           label: {
//             text: "Complètes",
//             style: { color: "#000" },
//           },
//         },
//         {
//           from: completedTasks,
//           to: completedTasks + overdueTasks,
//           color: "#dc3545", // Rouge pour les tâches en retard
//           label: {
//             text: "En Retard",
//             style: { color: "#000" },
//           },
//         },
//         {
//           from: completedTasks + overdueTasks,
//           to: completedTasks + overdueTasks + toDoTasks,
//           color: "#ffc107", // Jaune pour les tâches à faire
//           label: {
//             text: "À Faire",
//             style: { color: "#000" },
//           },
//         },
//         {
//           from: completedTasks + overdueTasks + toDoTasks,
//           to: totalTasks,
//           color: "#17a2b8", // Cyan pour les tâches en cours
//           label: {
//             text: "En Cours",
//             style: { color: "#000" },
//           },
//         },
//       ],
//     },
//     series: [
//       {
//         name: "Tâches",
//         data: [totalTasks], // Montre le total dans la jauge
//         tooltip: {
//           valueSuffix: " tâches",
//         },
//       },
//     ],
//   };

//   return (
//     <div>
//       <HighchartsReact highcharts={Highcharts} options={options} />
//     </div>
//   );
// };

export const Chart2Bars = ({ data, isExistDate = true, height = 350 }) => {
  const options = {
    chart: {
      type: "column",
      height,
    },
    title: {
      text: data?.name,
      align: "left",
    },
    subtitle: {
      text: !isExistDate ? i18next.t("common:dashboard.independantNumber") : "",
      align: "left",
    },
    xAxis: {
      categories: data?.categories,
      crosshair: true,
    },
    yAxis: {
      min: 0,
      title: {
        text: "",
        // text: "Nombre de Tâches",
        // align: "high",
      },
    },
    tooltip: {
      valueSuffix: "",
    },
    plotOptions: {
      column: {
        pointPadding: 0.2,
        borderWidth: 0,
        dataLabels: {
          enabled: true, // Enable data labels
          formatter: function () {
            return this.y !== 0 ? this.y : null; // Show label only if value is not zero
          },
          // Format to display the value, // Format to display the value
        },
      },
    },
    series: data?.series,
    exporting: {
      buttons: {
        contextButton: {
          menuItems: [
            "viewFullscreen", // Keep fullscreen option
            "printChart", // Keep Print option
            "separator",
            "downloadPNG", // Keep PNG download option
            "downloadJPEG", // Keep JPEG download option
            "downloadPDF", // Keep PDF download option
            "downloadSVG", // Keep SVG download option
            "downloadCSV", // Keep CSV download option
            "downloadXLS", // Keep XLS download option
            // 'viewData' // Remove or comment this line to exclude "View Data"
          ],
        },
      },
    },
  };

  return (
    <div>
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
};
export const CompletedTasksGauge = () => {
  const options = {
    chart: {
      type: "gauge",
      plotBackgroundColor: null,
      plotBackgroundImage: null,
      plotBorderWidth: 0,
      plotShadow: false,
      height: 350,
    },
    title: {
      text: "Overdue tasks",
      y: 20,
      style: {
        fontWeight: "bold", // Mettre le titre en gras
        fontSize: "18px",
      },
    },
    pane: {
      startAngle: -150,
      endAngle: 150,
      background: [
        {
          backgroundColor:
            Highcharts.defaultOptions.chart.backgroundColor || "#EEE",
          borderWidth: 0,
          outerRadius: "109%",
        },
      ],
    },
    yAxis: {
      min: 0,
      max: 100, // Ajustez en fonction de votre besoin
      minorTickInterval: "auto",
      minorTickWidth: 1,
      minorTickLength: 10,
      tickPixelInterval: 30,
      tickWidth: 2,
      tickLength: 10,

      title: {
        text: "Tasks",
        align: "bottom",
      },
      plotBands: [
        {
          from: 0,
          to: 28, // Nombre de tâches complétées
          color: "#55BF3B", // Vert
        },
      ],
    },
    series: [
      {
        name: "Tasks completed",
        data: [28], // Valeur à afficher
        tooltip: {
          valueSuffix: " tasks",
        },
      },
    ],
    credits: {
      enabled: false,
    },
    exporting: {
      buttons: {
        contextButton: {
          menuItems: [
            "viewFullscreen", // Keep fullscreen option
            "printChart", // Keep Print option
            "separator",
            "downloadPNG", // Keep PNG download option
            "downloadJPEG", // Keep JPEG download option
            "downloadPDF", // Keep PDF download option
            "downloadSVG", // Keep SVG download option
            "downloadCSV", // Keep CSV download option
            "downloadXLS", // Keep XLS download option
            // 'viewData' // Remove or comment this line to exclude "View Data"
          ],
        },
      },
    },
  };

  return (
    <div>
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
};
export const DonutChart2 = ({
  data,
  total = 0,
  name = "",
  isExistDate = true,
  height = 350,
}) => {
  const donutChartRef = useRef(null);
  useEffect(() => {
    const chart = donutChartRef.current.chart;
    const series = chart.series[0];

    let customLabel = chart.options.chart.custom.label;

    if (!customLabel) {
      customLabel = chart.options.chart.custom.label = chart.renderer
        .label("")
        .css({
          color: "#000",
          textAnchor: "middle",
        })
        .add();
    }

    const totalCalls = total ?? 0;

    customLabel.attr({
      text: `Total <br/> <strong>${totalCalls}</strong>`,
    });

    const x = series.center[0] + chart.plotLeft;
    const y = series.center[1] + chart.plotTop - customLabel.attr("height") / 2;

    customLabel.attr({ x, y });

    customLabel.css({
      fontSize: `${series.center[2] / 12}px`,
    });
  }, [total, name]);
  const options = {
    chart: {
      type: "pie",
      custom: {},
      height,

      events: {
        render() {
          const chart = this;
          const series = chart.series[0];
          let customLabel = chart.options.chart.custom.label;

          if (!customLabel) {
            customLabel = chart.options.chart.custom.label = chart.renderer
              .label("")
              .css({
                color: "#000",
                textAnchor: "middle",
              })
              .add();
          }

          const x = series.center[0] + chart.plotLeft;
          const y =
            series.center[1] + chart.plotTop - customLabel.attr("height") / 2;

          customLabel.attr({ x, y });

          customLabel.css({
            fontSize: `${series.center[2] / 12}px`,
          });
        },
      },
    },
    legend: {
      labelFormat: '{name} <strong style="color:#475569">{y}</strong>',
    },
    accessibility: {
      point: {
        valueSuffix: "%",
      },
    },
    title: {
      text: name,
      align: "left",
    },
    subtitle: {
      text: !isExistDate ? i18next.t("common:dashboard.independantNumber") : "",
      align: "left",
    },
    tooltip: {
      headerFormat: "",
      pointFormat: `<b>{point.name}</b>: {point.percentage:.2f}%`,
    },

    plotOptions: {
      series: {
        allowPointSelect: true,
        cursor: "pointer",
        borderRadius: 8,
        dataLabels: [
          {
            enabled: false,
            distance: 5,
            format: "{point.name}",
          },
          {
            enabled: true,
            distance: -15,
            formatter: function () {
              const percentage = this.point.percentage;
              return percentage % 1 === 0
                ? `${percentage.toFixed(0)}%`
                : `${percentage.toFixed(2)}%`;
            },
            style: {
              fontSize: "0.9em",
            },
          },
        ],
        showInLegend: false,
      },
    },
    series: [
      {
        name: i18next.t("common:chat.message_system.removed_auth_user_modal"),
        colorByPoint: true,
        innerSize: "65%",

        data,
      },
    ],
    exporting: {
      buttons: {
        contextButton: {
          menuItems: [
            "viewFullscreen", // Keep fullscreen option
            "printChart", // Keep Print option
            "separator",
            "downloadPNG", // Keep PNG download option
            "downloadJPEG", // Keep JPEG download option
            "downloadPDF", // Keep PDF download option
            "downloadSVG", // Keep SVG download option
            "downloadCSV", // Keep CSV download option
            "downloadXLS", // Keep XLS download option
            // 'viewData' // Remove or comment this line to exclude "View Data"
          ],
        },
      },
      // filename: "mon_graphique_exporte",
    },
    // exporting: {
    //   csv: {
    //     itemDelimiter: ";",
    //   },
    // },
  };

  return (
    <HighchartsReact
      constructorType={"chart"}
      highcharts={Highcharts}
      options={options}
      ref={donutChartRef}
    />
  );
};
export const DonutChartWithoutTotal = React.memo(
  ({ data, total = 0, name = "", isExistDate = true, height = 350 }) => {
    const donutChartRef = useRef(null);

    const options = {
      chart: {
        type: "pie",
        custom: {},
        height,

        events: {
          render() {
            const chart = this;
            const series = chart.series[0];
            let customLabel = chart.options.chart.custom.label;

            if (!customLabel) {
              customLabel = chart.options.chart.custom.label = chart.renderer
                .label("")
                .css({
                  color: "#000",
                  textAnchor: "middle",
                })
                .add();
            }

            const x = series.center[0] + chart.plotLeft;
            const y =
              series.center[1] + chart.plotTop - customLabel.attr("height") / 2;

            customLabel.attr({ x, y });

            customLabel.css({
              fontSize: `${series.center[2] / 12}px`,
            });
          },
        },
      },
      legend: {
        labelFormat: '{name} <strong style="color:#475569">{y}</strong>',
      },
      accessibility: {
        point: {
          valueSuffix: "%",
        },
      },
      title: {
        text: name,
        align: "left",
      },
      subtitle: {
        text: !isExistDate
          ? i18next.t("common:dashboard.independantNumber")
          : "",
        align: "left",
      },
      tooltip: {
        headerFormat: "",
        pointFormat: `<b>{point.name}</b>: {point.percentage:.2f}%`,
      },

      plotOptions: {
        series: {
          allowPointSelect: true,
          cursor: "pointer",
          borderRadius: 8,
          dataLabels: [
            {
              enabled: false,
              distance: 5,
              format: "{point.name}",
            },
            {
              enabled: true,
              distance: -15,
              formatter: function () {
                const percentage = this.point.percentage;
                return percentage % 1 === 0
                  ? `${percentage.toFixed(0)}%`
                  : `${percentage.toFixed(2)}%`;
              },
              style: {
                fontSize: "0.9em",
              },
            },
          ],
          showInLegend: false,
        },
      },
      series: [
        {
          name: i18next.t("common:chat.message_system.removed_auth_user_modal"),
          colorByPoint: true,
          innerSize: "85%",

          data,
        },
      ],
    };

    return (
      <HighchartsReact
        constructorType={"chart"}
        highcharts={Highcharts}
        options={options}
        ref={donutChartRef}
      />
    );
  }
);
export const BigDonutChart = ({
  data,
  name = "",
  isExistDate = true,
  height = 350,
}) => {
  const donutChartRef = useRef(null);
  useEffect(() => {
    const chart = donutChartRef.current.chart;
    const series = chart.series[0];

    let customLabel = chart.options.chart.custom.label;

    if (!customLabel) {
      customLabel = chart.options.chart.custom.label = chart.renderer
        .label("")
        .css({
          color: "#000",
          textAnchor: "middle",
        })
        .add();
    }

    // const totalCalls = total ?? 0;

    // customLabel.attr({
    //   text: `Total <br/> <strong>${totalCalls}</strong>`,
    // });

    const x = series.center[0] + chart.plotLeft;
    const y = series.center[1] + chart.plotTop - customLabel.attr("height") / 2;

    customLabel.attr({ x, y });

    customLabel.css({
      fontSize: `${series.center[2] / 12}px`,
    });
  }, [name]);
  const options = {
    chart: {
      type: "pie",
      custom: {},
      height,

      events: {
        render() {
          const chart = this;
          const series = chart.series[0];
          let customLabel = chart.options.chart.custom.label;

          if (!customLabel) {
            customLabel = chart.options.chart.custom.label = chart.renderer
              .label("")
              .css({
                color: "#000",
                textAnchor: "middle",
              })
              .add();
          }

          const x = series.center[0] + chart.plotLeft;
          const y =
            series.center[1] + chart.plotTop - customLabel.attr("height") / 2;

          customLabel.attr({ x, y });

          customLabel.css({
            fontSize: `${series.center[2] / 12}px`,
          });
        },
      },
    },
    legend: {
      labelFormat: '{name} <span style="color:#475569">{y}</span>',
    },
    accessibility: {
      point: {
        valueSuffix: "%",
      },
    },
    title: {
      text: name,
      align: "left",
    },
    subtitle: {
      text: !isExistDate ? i18next.t("common:dashboard.independantNumber") : "",
      align: "left",
    },
    tooltip: {
      headerFormat: "",
      pointFormat: `<b>{point.name}</b>: {point.percentage:.2f}%`,
    },

    plotOptions: {
      series: {
        allowPointSelect: true,
        cursor: "pointer",
        borderRadius: 8,
        dataLabels: [
          {
            enabled: false,
            distance: 5,
            format: "{point.name}",
          },
          {
            enabled: true,
            distance: -15,
            formatter: function () {
              const percentage = this.point.percentage;
              return percentage % 1 === 0
                ? `${percentage.toFixed(0)}%`
                : `${percentage.toFixed(2)}%`;
            },
            style: {
              fontSize: "0.9em",
            },
          },
        ],
        showInLegend: true,
      },
    },
    series: [
      {
        name: i18next.t("common:chat.message_system.removed_auth_user_modal"),
        colorByPoint: true,
        innerSize: "60%",

        data,
      },
    ],
    exporting: {
      buttons: {
        contextButton: {
          menuItems: [
            "viewFullscreen", // Keep fullscreen option
            "printChart", // Keep Print option
            "separator",
            "downloadPNG", // Keep PNG download option
            "downloadJPEG", // Keep JPEG download option
            "downloadPDF", // Keep PDF download option
            "downloadSVG", // Keep SVG download option
            "downloadCSV", // Keep CSV download option
            "downloadXLS", // Keep XLS download option
            // 'viewData' // Remove or comment this line to exclude "View Data"
          ],
        },
      },
      // filename: "mon_graphique_exporte",
    },
    // exporting: {
    //   csv: {
    //     itemDelimiter: ";",
    //   },
    // },
  };

  return (
    <HighchartsReact
      constructorType={"chart"}
      highcharts={Highcharts}
      options={options}
      ref={donutChartRef}
    />
  );
};
export const BrowserMarketShareChart = ({
  allData,
  parentName,
  childName,
  hasSelect = false,
  isExistDate = true,
}) => {
  const colors = Highcharts.getOptions().colors;
  const categories = allData.categories;
  const data = allData.data ?? [];
  const browserData = [];
  const versionsData = [];

  for (let i = 0; i < data?.length; i++) {
    // add browser data
    browserData.push({
      name: categories[i],
      y: data[i].y,
      color: data[i].color,
    });

    // add version data
    const drillDataLen = data[i].drilldown.data.length;
    for (let j = 0; j < drillDataLen; j++) {
      const name = data[i].drilldown.categories[j].name;
      const color = data[i].drilldown.categories[j].color;

      versionsData.push({
        name,
        y: data[i].drilldown.data[j],
        color,
        custom: {
          version: name?.split(" ")[1] || name?.split(" ")[0],
        },
        showInLegend: true,
      });
    }
  }

  const options = {
    chart: {
      type: "pie",
      height: hasSelect ? 277 : 350,
    },

    title: {
      text: hasSelect ? "" : allData?.name,
      align: "left",
    },
    subtitle: {
      text: !isExistDate ? i18next.t("common:dashboard.independantNumber") : "",
      align: "left",
    },

    plotOptions: {
      pie: {
        shadow: false,
        center: ["50%", "50%"],
      },
      showInLegend: true,
    },
    tooltip: {
      pointFormat: `${i18next.t(
        "common:dashboard.percentage"
      )}: <b>{point.percentage:.0f}%</b>`,
    },
    series: [
      {
        name: parentName,
        data: browserData,
        size: "45%",
        dataLabels: {
          color: "#ffffff",
          distance: "-80%",
          format: "{point.name}: {point.y}", // Affiche le nom et la valeur
        },
      },
      {
        name: childName,
        data: versionsData,
        size: "80%",
        innerSize: "60%",
        dataLabels: {
          format:
            '<b>{point.name}:</b> <span style="opacity: 0.5">' +
            "{point.y}</span>", // Affiche le nom et la valeur
          filter: {
            property: "y",
            operator: ">",
            value: 0,
          },
          style: {
            fontWeight: "normal",
          },
          distance: 2,
        },
        id: "",
      },
    ],
    legend: {
      enabled: true,
      layout: "vertical",
      align: "center",
      verticalAlign: "bottom",
      floating: false,
      backgroundColor: "white",
      borderColor: "#CCC",
      borderWidth: 1,
      shadow: false,
      itemFormatter: function () {
        return this.name + ": " + this.y.toFixed(2) + "%";
      },
    },
    responsive: {
      rules: [
        {
          condition: {
            maxWidth: 400,
          },
          chartOptions: {
            series: [
              {},
              {
                id: "versions",
                dataLabels: {
                  distance: 10,
                  format: "{point.custom.version}: {point.y}", // Affiche le nom de la version et la valeur
                },
              },
            ],
          },
        },
      ],
    },
    data,
    exporting: {
      buttons: {
        contextButton: {
          menuItems: [
            "viewFullscreen", // Keep fullscreen option
            "printChart", // Keep Print option
            "separator",
            "downloadPNG", // Keep PNG download option
            "downloadJPEG", // Keep JPEG download option
            "downloadPDF", // Keep PDF download option
            "downloadSVG", // Keep SVG download option
            "downloadCSV", // Keep CSV download option
            "downloadXLS", // Keep XLS download option
            // 'viewData' // Remove or comment this line to exclude "View Data"
          ],
        },
      },
    },
  };

  return <HighchartsReact highcharts={Highcharts} options={options} />;
};
export const VerticalOneChart = ({ data, total, name }) => {
  const options = {
    chart: {
      type: "column",
      height: 350,
    },
    title: {
      text: "",
    },
    subtitle: {
      text: "",
    },
    xAxis: {
      type: "category",
      labels: {
        autoRotation: [-45, -90],
        style: {
          fontSize: "13px",
          fontFamily: "Verdana, sans-serif",
        },
      },
    },
    yAxis: {
      min: 0,
      title: {
        text: "",
      },
    },
    legend: {
      labelFormat: `${name} <span style="color:#475569">${total}</span>`,
    },
    tooltip: {
      pointFormat: `${i18next.t(
        "common:dashboard.percentage"
      )}: <b>{point.percentage:.0f}%</b>`,
    },

    series: [
      {
        name,
        colorByPoint: true,
        groupPadding: 0,
        data: data.map((type) => ({
          ...type,
          percentage: (type.y / total) * 100, // Calculer le pourcentage
        })),
        dataLabels: {
          enabled: true,
          rotation: -90,
          color: "#FFFFFF",
          inside: true,
          verticalAlign: "top",
          //   format: "{point.y}", // one decimal
          y: 10, // 10 pixels down from the top
          style: {
            fontSize: "13px",
            fontFamily: "Verdana, sans-serif",
          },
        },
      },
    ],
    exporting: {
      buttons: {
        contextButton: {
          menuItems: [
            "viewFullscreen", // Keep fullscreen option
            "printChart", // Keep Print option
            "separator",
            "downloadPNG", // Keep PNG download option
            "downloadJPEG", // Keep JPEG download option
            "downloadPDF", // Keep PDF download option
            "downloadSVG", // Keep SVG download option
            "downloadCSV", // Keep CSV download option
            "downloadXLS", // Keep XLS download option
            // 'viewData' // Remove or comment this line to exclude "View Data"
          ],
        },
      },
    },
  };

  return (
    <div>
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
};
export const LeftBarsChart = ({ data }) => {
  const options = {
    chart: {
      type: "bar",
      height: 500,
    },
    title: {
      text: "",
      align: "left",
    },
    subtitle: {
      text: "",
      align: "left",
    },
    xAxis: {
      categories: data.categories,
      title: {
        text: null,
      },
      gridLineWidth: 1,
      lineWidth: 0,
    },
    yAxis: {
      min: 0,
      title: {
        text: "",
        align: "high",
      },
      labels: {
        overflow: "justify",
      },
      gridLineWidth: 0,
    },
    tooltip: {
      valueSuffix: " millions",
    },
    plotOptions: {
      bar: {
        borderRadius: "50%",
        dataLabels: {
          enabled: true,
        },
        groupPadding: 0.1,
      },
    },
    legend: {
      layout: "vertical",
      align: "right",
      verticalAlign: "top",
      x: -40,
      y: 80,
      floating: true,
      borderWidth: 1,
      backgroundColor:
        Highcharts.defaultOptions.legend.backgroundColor || "#FFFFFF",
      shadow: true,
    },
    credits: {
      enabled: false,
    },
    series: data.series,
    exporting: {
      buttons: {
        contextButton: {
          menuItems: [
            "viewFullscreen", // Keep fullscreen option
            "printChart", // Keep Print option
            "separator",
            "downloadPNG", // Keep PNG download option
            "downloadJPEG", // Keep JPEG download option
            "downloadPDF", // Keep PDF download option
            "downloadSVG", // Keep SVG download option
            "downloadCSV", // Keep CSV download option
            "downloadXLS", // Keep XLS download option
            // 'viewData' // Remove or comment this line to exclude "View Data"
          ],
        },
      },
    },
  };

  return <HighchartsReact highcharts={Highcharts} options={options} />;
};
export const TrophiesChart = ({ data }) => {
  const filteredSeries = data?.series
    // ?.map((series) => ({
    //   ...series,
    //   data: series.data.map((value) => (value > 0 ? value : null)),
    //   visible: series.name !== "none", // Set visibility based on the name
    // }))
    ?.filter((series) => series?.data?.some((value) => value !== null));
  const options = {
    chart: {
      type: "column",
      height: 350,
      marginTop: 50,
      spacingTop: 10,
    },
    title: {
      text: data?.name,
      align: "left",
    },
    xAxis: {
      categories: data?.categories,
      labels: {
        formatter: function () {
          const index = this.pos;
          const value = filteredSeries.reduce(
            (acc, series) => acc + (series.data[index] || 0),
            0
          );
          return `${this.value} <span style="color: #333333;opacity:0.4">${value}</span>`;
        },
      },
    },
    yAxis: {
      min: 0,
      title: {
        text: "",
      },
      stackLabels: {
        enabled: false,
      },
    },
    legend: {
      align: "center",
      verticalAlign: "top",
      y: 30,
      floating: true,
      backgroundColor:
        Highcharts.defaultOptions.legend.backgroundColor || "white",
      borderColor: "#CCC",
      borderWidth: 1,
      shadow: false,
      labelFormatter: function () {
        const total = this.yData.reduce((acc, val) => acc + (val || 0), 0);
        return `${total} ${this.name}`;
      },
    },
    tooltip: {
      headerFormat: "<b>{category}</b><br/>",
      pointFormat: "{series.name}: {point.y}<br/>Total: {point.stackTotal}",
    },
    plotOptions: {
      column: {
        stacking: "normal",
        dataLabels: {
          enabled: true,
        },
      },
    },
    series: filteredSeries, // Use the filtered series with visibility set
    exporting: {
      buttons: {
        contextButton: {
          menuItems: [
            "viewFullscreen", // Keep fullscreen option
            "printChart", // Keep Print option
            "separator",
            "downloadPNG", // Keep PNG download option
            "downloadJPEG", // Keep JPEG download option
            "downloadPDF", // Keep PDF download option
            "downloadSVG", // Keep SVG download option
            "downloadCSV", // Keep CSV download option
            "downloadXLS", // Keep XLS download option
            // 'viewData' // Remove or comment this line to exclude "View Data"
          ],
        },
      },
    },
  };

  return (
    <div id="container">
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
};
export const TaskStatusRadialChart = ({ data }) => {
  // S'assurer que les données sont au format correct
  const formattedData = [
    { y: 148, color: "#28a745", name: "Completed" }, // Vert pour Completed
    { y: 113, color: "#dc3545", name: "Overdue" }, // Rouge pour Overdue
    { y: 104, color: "#6c757d", name: "To do" }, // Gris pour To do
    { y: 71, color: "#007bff", name: "In Progress" }, // Bleu pour In Progress
  ];

  const options = {
    chart: {
      type: "column",
      inverted: true,
      polar: true,
    },
    title: {
      text: "Tasks",
      align: "left",
    },
    subtitle: {
      text:
        "Source: " +
        '<a href="https://en.wikipedia.org/wiki/All-time_Olympic_Games_medal_table" target="_blank">Wikipedia</a>',
      align: "left",
    },
    tooltip: {
      outside: true,
    },
    pane: {
      size: "85%",
      innerSize: "20%",
      endAngle: 270,
    },
    xAxis: {
      tickInterval: 1,
      labels: {
        align: "right",
        useHTML: true,
        allowOverlap: true,
        step: 1,
        y: 3,
        style: {
          fontSize: "13px",
        },
      },
      lineWidth: 0,
      gridLineWidth: 0,
      categories: ["Completed", "Overdue", "To do", "In Progress"],
    },
    yAxis: {
      lineWidth: 0,
      tickInterval: 25,
      reversedStacks: false,
      endOnTick: true,
      showLastLabel: true,
      gridLineWidth: 0,
    },
    plotOptions: {
      column: {
        stacking: "normal",
        borderWidth: 0,
        pointPadding: 0,
        groupPadding: 0.15,
        borderRadius: "50%",
        dataLabels: {
          enabled: true,
          format: "{point.name}: {point.y}", // Afficher le nom de la catégorie et la valeur
        },
      },
    },
    series: [
      {
        name: "Task Status", // Nom de la série
        data: formattedData, // Utiliser les données formatées
      },
    ],
    legend: {
      layout: "vertical", // Disposition verticale
      align: "right", // Alignement à droite
      verticalAlign: "middle", // Alignement au milieu
      itemMarginTop: 10, // Marge entre les éléments
      itemStyle: {
        fontSize: "14px", // Taille de la police
      },
      labelFormatter: function () {
        return this.name; // Afficher le nom de la catégorie dans la légende
      },
    },
    exporting: {
      buttons: {
        contextButton: {
          menuItems: [
            "viewFullscreen", // Keep fullscreen option
            "printChart", // Keep Print option
            "separator",
            "downloadPNG", // Keep PNG download option
            "downloadJPEG", // Keep JPEG download option
            "downloadPDF", // Keep PDF download option
            "downloadSVG", // Keep SVG download option
            "downloadCSV", // Keep CSV download option
            "downloadXLS", // Keep XLS download option
            // 'viewData' // Remove or comment this line to exclude "View Data"
          ],
        },
      },
    },
  };

  return (
    <div>
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
};
export const StorageGauge1 = ({ data }) => {
  const chartRef = useRef(null);

  // Vos données de stockage
  const storageData = {
    total_storage: "10 GB",
    used_storage: "0.17 GB",
    available_storage: "9.83 GB",
    usage_percentage: "1.7%",
  };

  // Conversion des données en nombres
  const usedStorageGB = parseFloat(data.used_storage); // Convertir en nombre
  const totalStorageGB = parseFloat(data.total_storage); // Convertir en nombre
  const options = {
    chart: {
      type: "gauge",
      plotBackgroundColor: null,
      plotBackgroundImage: null,
      plotBorderWidth: 0,
      plotShadow: false,
      height: "80%",
    },
    title: {
      text: "Utilisation du Stockage",
    },
    pane: {
      startAngle: -90,
      endAngle: 90,
      background: null,
      center: ["50%", "75%"],
      size: "110%",
    },
    yAxis: {
      min: 0, // Minimum de 0 GB
      max: data?.total, // Maximum de 10 GB
      tickPixelInterval: 72,
      tickPosition: "inside",
      tickColor: Highcharts.defaultOptions.chart.backgroundColor || "#FFFFFF",
      tickLength: 20,
      tickWidth: 2,
      minorTickInterval: null,
      labels: {
        distance: 20,
        style: {
          fontSize: "14px",
        },
      },
      lineWidth: 0,
      plotBands: [
        {
          from: 0,
          to: Math.floor(Math.floor(data?.total) / 2),
          color: "#55BF3B", // Vert
          thickness: 20,
          borderRadius: "50%",
        },
        {
          from: Math.floor(Math.floor(data?.total) / 2),
          to: Math.floor(Math.floor(data?.total) / 1.2),
          color: "#DDDF0D", // Jaune
          thickness: 20,
        },
        {
          from: Math.floor(Math.floor(data?.total) / 1.2),
          to: data?.total,
          color: "#DF5353", // Rouge
          thickness: 20,
        },
      ],
    },
    series: [
      {
        name: "Utilisation",
        data: [usedStorageGB], // Utilisation en GB
        tooltip: {
          valueSuffix: " GB",
        },
        dataLabels: {
          format: "GB {y:.2f}", // Afficher "GB" devant le nombre
          borderWidth: 0,
          color: "#333333",
          style: {
            fontSize: "16px",
          },
        },
        dial: {
          radius: "80%",
          backgroundColor: "gray",
          baseWidth: 12,
          baseLength: "0%",
          rearLength: "0%",
        },
        pivot: {
          backgroundColor: "gray",
          radius: 6,
        },
      },
    ],
  };

  useEffect(() => {
    const interval = setInterval(() => {
      const chart = chartRef.current.chart;
      if (chart) {
        const point = chart.series[0].points[0];
        const inc = Math.round((Math.random() - 0.5) * 0.5); // Changer l'intervalle pour le stockage

        let newVal = point.y + inc;
        if (newVal < 0 || newVal > 10) {
          // Limite de 10 GB
          newVal = point.y - inc;
        }

        point.update(newVal);
      }
    }, 3000);

    return () => clearInterval(interval); // Nettoyage de l'intervalle
  }, []);

  return (
    <HighchartsReact ref={chartRef} highcharts={Highcharts} options={options} />
  );
};
export const StorageGauge = ({ data }) => {
  const options = {
    chart: {
      type: "gauge",
      plotBackgroundColor: null,
      plotBackgroundImage: null,
      plotBorderWidth: 0,
      plotShadow: false,
    },
    title: {
      text: "Utilisation du stockage",
      style: {
        fontSize: "16px",
        fontWeight: "bold",
      },
    },
    pane: {
      startAngle: -150,
      endAngle: 150,
      background: [
        {
          backgroundColor: "#f0f0f0", // Couleur de fond
          borderWidth: 1,
          borderColor: "#ccc",
          outerRadius: "109%",
          innerRadius: "60%",
        },
        {
          backgroundColor: null,
        },
      ],
    },
    yAxis: {
      min: 0,
      max: data?.total, // Capacité totale de stockage en GB
      minorTickInterval: "auto",
      minorTickWidth: 1,
      minorTickLength: 10,
      minorTickPosition: "inside",
      tickPixelInterval: 30,
      tickWidth: 2,
      tickPosition: "inside",
      tickLength: 10,
      title: {
        text: data?.usage_percentage,
        style: {
          fontWeight: "bold",
        },
      },
      labels: {
        formatter: function () {
          return this.value + " " + data?.unit;
        },
      },
      plotBands: [
        {
          // Bandes de couleurs
          from: 0,
          to: Math.floor(Math.floor(data?.total) / 2),
          color: "#55BF3B", // Vert
        },
        {
          from: Math.floor(Math.floor(data?.total) / 2),
          to: Math.floor(Math.floor(data?.total) / 1.2),
          color: "#DDDF0D", // Jaune
        },
        {
          from: Math.floor(Math.floor(data?.total) / 1.2),
          to: data?.total,
          color: "#DF5353", // Rouge
        },
      ],
    },
    series: [
      {
        name: "Utilisation",
        data: [data?.used_storage], // Utilisation actuelle en GB
        tooltip: {
          valueSuffix: " " + data?.unit,
        },
        color: "#000000", // Couleur de la jauge
      },
      {
        name: "Utilisation",
        data: [data?.used_storage], // Utilisation actuelle en GB
        tooltip: {
          valueSuffix: " " + data?.unit,
        },
        color: "#000000", // Couleur de la jauge
      },
    ],
    credits: {
      enabled: false, // Désactiver les crédits
    },
    exporting: {
      enabled: false, // Désactiver l'exportation
    },
  };

  return (
    <div>
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
};
// export const StorageGauge = ({ data }) => {
//   const options = {
//     chart: {
//       type: "gauge",
//       plotBackgroundColor: null,
//       plotBackgroundImage: null,
//       plotBorderWidth: 0,
//       plotShadow: false,
//     },
//     title: {
//       text: "Utilisation du stockage",
//     },
//     pane: {
//       startAngle: -150,
//       endAngle: 150,
//       background: [
//         {
//           backgroundColor:
//             Highcharts.defaultOptions.legend.backgroundColor || "none",
//           borderWidth: 0,
//           outerRadius: "109%",
//           innerRadius: "60%",
//         },
//       ],
//     },
//     yAxis: {
//       min: 0,
//       max: data?.total, // Capacité totale de stockage en GB
//       minorTickInterval: "auto",
//       minorTickWidth: 1,
//       minorTickLength: 10,
//       minorTickPosition: "inside",
//       minorGridLineWidth: 0,
//       tickPixelInterval: 30,
//       tickWidth: 2,
//       tickPosition: "inside",
//       tickLength: 10,
//       title: {
//         text: data?.unit,
//       },
//       labels: {
//         formatter: function () {
//           return this.value + " " + data?.unit;
//         },
//       },
//     },
//     series: [
//       {
//         name: "Utilisation",
//         data: [data?.used_storage], // Utilisation actuelle en GB
//         tooltip: {
//           valueSuffix: " " + data?.unit,
//         },
//       },
//     ],
//     legend: {
//       layout: "vertical",
//       align: "right",
//       verticalAlign: "middle",
//       floating: true,
//       backgroundColor:
//         Highcharts.defaultOptions.legend.backgroundColor || "white",
//       borderWidth: 1,
//       shadow: true,
//       title: {
//         text: "Données de stockage",
//         style: {
//           fontWeight: "bold",
//         },
//       },
//       itemStyle: {
//         fontSize: "12px",
//       },
//       itemMarginTop: 5,
//       useHTML: true,
//       labelFormatter: function () {
//         return `<b>${this.name}</b>: ${this.y} GB`;
//       },
//     },
//   };

//   return (
//     <div>
//       <HighchartsReact highcharts={Highcharts} options={options} />
//     </div>
//   );
// };

// export const EvolutionChart = ({ data, isExistDate = true }) => {
//   const [chartOptions, setChartOptions] = useState({});

//   useEffect(() => {
//     const titleText = `
//       <div style="text-align: left;">
//           <span style="font-weight: bold;">Titre</span>
//           ${
//             !isExistDate
//               ? `<span
//                 id="info-circle"
//                 style="border-radius: 50%; border: 2px solid black; display: inline-block; width: 20px; height: 20px; line-height: 20px; text-align: center; cursor: pointer;"
//             >
//                 i
//             </span>`
//               : ""
//           }
//       </div>
//     `;

//     const options = {
//       chart: {
//         type: "line",
//         height: 350,
//       },
//       title: {
//         useHTML: true,
//         text: titleText,
//         align: "left",
//       },
//       xAxis: {
//         categories: data?.categories || [],
//       },
//       yAxis: {
//         title: {
//           text: "",
//         },
//       },
//       series: data?.series || [],
//       exporting: {
//         buttons: {
//           contextButton: {
//             menuItems: [
//               "viewFullscreen",
//               "printChart",
//               "separator",
//               "downloadPNG",
//               "downloadJPEG",
//               "downloadPDF",
//               "downloadSVG",
//               "downloadCSV",
//               "downloadXLS",
//             ],
//           },
//         },
//       },
//     };

//     setChartOptions(options);

//     // Ajouter un événement de survol pour afficher le tooltip
//     const infoCircle = document.getElementById("info-circle");
//     if (infoCircle) {
//       infoCircle.addEventListener("mouseenter", function () {
//         const tooltipText = i18next.t("common:dashboard.independantNumber");
//         const tooltip = document.createElement("div");
//         tooltip.innerHTML = tooltipText;
//         tooltip.style.position = "absolute";
//         tooltip.style.backgroundColor = "white";
//         tooltip.style.border = "1px solid black";
//         tooltip.style.padding = "5px";
//         tooltip.style.zIndex = 1000;
//         tooltip.style.pointerEvents = "none"; // Pour éviter que le tooltip ne bloque les événements
//         document.body.appendChild(tooltip);

//         // Positionner le tooltip
//         const rect = this.getBoundingClientRect();
//         tooltip.style.top = `${rect.bottom + window.scrollY}px`;
//         tooltip.style.left = `${rect.left + window.scrollX}px`;

//         // Supprimer le tooltip lorsque la souris quitte le cercle
//         this.addEventListener(
//           "mouseleave",
//           function () {
//             document.body.removeChild(tooltip);
//           },
//           { once: true }
//         );
//       });
//     }
//   }, [data, isExistDate]);

//   return <HighchartsReact highcharts={Highcharts} options={chartOptions} />;
// };
export const EvolutionChart = ({ data, isExistDate = true }) => {
  const options = {
    chart: {
      type: "line",
      height: 350,
    },
    title: {
      text: data?.name,
      align: "left",
    },
    subtitle: {
      text: !isExistDate ? i18next.t("common:dashboard.independantNumber") : "",
      align: "left",
    },
    xAxis: {
      categories: data?.categories,
    },
    yAxis: {
      title: {
        text: "",
      },
    },
    series: data?.series,
    exporting: {
      buttons: {
        contextButton: {
          menuItems: [
            "viewFullscreen", // Keep fullscreen option
            "printChart", // Keep Print option
            "separator",
            "downloadPNG", // Keep PNG download option
            "downloadJPEG", // Keep JPEG download option
            "downloadPDF", // Keep PDF download option
            "downloadSVG", // Keep SVG download option
            "downloadCSV", // Keep CSV download option
            "downloadXLS", // Keep XLS download option
            // 'viewData' // Remove or comment this line to exclude "View Data"
          ],
        },
      },
    },
  };

  return <HighchartsReact highcharts={Highcharts} options={options} />;
};
export const GaugeChart = React.memo(
  ({
    data,
    title = true,
    inverseColor = false,
    name = "",
    subtitle = "",
    height = 350,
    size = "100%",
  }) => {
    const gaugeOptions = {
      chart: {
        type: "solidgauge",
        height,
      },
      title: name,
      pane: {
        center: ["50%", "85%"],
        size,
        startAngle: -90,
        endAngle: 90,
        background: {
          backgroundColor:
            Highcharts.defaultOptions.legend.backgroundColor || "#fafafa",
          borderRadius: 5,
          innerRadius: "60%",
          outerRadius: "100%",
          shape: "arc",
        },
      },
      exporting: {
        enabled: false,
      },
      tooltip: {
        enabled: false,
      },
      yAxis: {
        min: 0,
        max: data?.total,
        stops: inverseColor
          ? [
              [0.1, "#DF5353"], // red
              [0.5, "#DDDF0D"], // yellow
              [0.8, "#55BF3B"], // green
            ]
          : [
              [0.1, "#55BF3B"], // green
              [0.5, "#DDDF0D"], // yellow
              [0.8, "#DF5353"], // red
            ],
        lineWidth: 0,
        tickWidth: 0,
        minorTickInterval: null,
        tickAmount: 0,
        title: {
          text: title ? data?.title : "",
          y: -70,
        },
        labels: {
          y: 16,
          formatter: function () {
            return this.value >= data?.total && data?.total !== 0
              ? data?.total
              : 0; // Show max value only
          },
        },
      },
      plotOptions: {
        solidgauge: {
          borderRadius: 3,
          dataLabels: {
            y: 5,
            borderWidth: 0,
            useHTML: true,
          },
        },
      },
      series: [
        {
          name: data?.title,
          data: [data?.used_storage],
          dataLabels: {
            format:
              '<div style="text-align:center">' +
              '<span style="font-size:25px">{y}</span><br/>' +
              `<span style="font-size:12px;opacity:0.4">${data?.unit}</span>` +
              "</div>",
          },
          tooltip: {
            valueSuffix: " " + data?.unit,
          },
        },
      ],
    };

    // useEffect(() => {
    //   const interval = setInterval(() => {
    //     const chart = Highcharts.charts[0]; // Assuming this is the first chart
    //     if (chart) {
    //       const point = chart.series[0].points[0];
    //       const inc = Math.round((Math.random() - 0.5) * 100);
    //       let newVal = point.y + inc;

    //       if (newVal < 0 || newVal > 200) {
    //         newVal = point.y - inc;
    //       }

    //       point.update(newVal);
    //     }
    //   }, 2000);

    //   return () => clearInterval(interval);
    // }, []);

    return <HighchartsReact highcharts={Highcharts} options={gaugeOptions} />;
  }
);
export const OneBarChart = React.memo(
  ({ data, hasSelect = false, height = 350 }) => {
    const options = {
      chart: {
        type: "column",
        height: hasSelect ? 277 : height,
      },
      title: {
        align: "left",
        text: hasSelect ? "" : data?.name,
      },
      subtitle: {
        align: "left",
        text: "",
      },
      accessibility: {
        announceNewData: {
          enabled: true,
        },
      },
      xAxis: {
        type: "category",
      },
      yAxis: {
        title: {
          text: "",
        },
      },
      legend: {
        enabled: false,
      },
      plotOptions: {
        series: {
          borderWidth: 0,
          dataLabels: {
            enabled: true,
            format: "{point.y}",
          },
        },
      },
      tooltip: {
        headerFormat: '<span style="font-size:11px">{series.name}</span><br>',
        pointFormat:
          '<span style="color:{point.color}">{point.name}</span>: ' +
          "<b>{point.y}</b>",
      },
      series: [
        {
          name: "",
          colorByPoint: true,
          data: data.data,
        },
      ],
      exporting: {
        buttons: {
          contextButton: {
            menuItems: [
              "viewFullscreen", // Keep fullscreen option
              "printChart", // Keep Print option
              "separator",
              "downloadPNG", // Keep PNG download option
              "downloadJPEG", // Keep JPEG download option
              "downloadPDF", // Keep PDF download option
              "downloadSVG", // Keep SVG download option
              "downloadCSV", // Keep CSV download option
              "downloadXLS", // Keep XLS download option
              // 'viewData' // Remove or comment this line to exclude "View Data"
            ],
          },
        },
      },
    };

    return <HighchartsReact highcharts={Highcharts} options={options} />;
  }
);

export const HorizontalOneBarChart = ({ data, isExistDate = true }) => {
  const options = {
    chart: {
      type: "bar",
      height: 350,
    },
    title: {
      text: data?.name,
      align: "left",
    },
    subtitle: {
      text: !isExistDate ? i18next.t("common:dashboard.independantNumber") : "",
      align: "left",
    },
    xAxis: {
      categories: data?.categories,
      title: {
        text: "",
      },
    },
    yAxis: {
      title: {
        text: "",
      },
    },
    legend: {
      enabled: true,
      itemStyle: {
        visibility: "hidden",
      },
      symbolHeight: 0, // Hauteur du symbole (invisible)
      symbolWidth: 0,
      title: {
        style: {
          visibility: "hidden",
        },
      },
    },
    series: [
      {
        name: "",
        data: data?.data,
      },
    ],
    exporting: {
      buttons: {
        contextButton: {
          menuItems: [
            "viewFullscreen", // Keep fullscreen option
            "printChart", // Keep Print option
            "separator",
            "downloadPNG", // Keep PNG download option
            "downloadJPEG", // Keep JPEG download option
            "downloadPDF", // Keep PDF download option
            "downloadSVG", // Keep SVG download option
            "downloadCSV", // Keep CSV download option
            "downloadXLS", // Keep XLS download option
            // 'viewData' // Remove or comment this line to exclude "View Data"
          ],
        },
      },
      // filename: "mon_graphique_exporte",
    },
  };

  return (
    <div className="relative w-full">
      <HighchartsReact highcharts={Highcharts} options={options} />
      {/* <div className="absolute bottom-2 flex w-full justify-center gap-4">
        {data?.data?.map((el) => (
          <div
            key={el.name}
            className="flex flex-wrap items-center justify-center gap-1"
            onClick={() => data.filter((it) => it.name !== el.name)}
          >
            <div
              class="h-3 w-3 rounded-full"
              style={{ backgroundColor: el.color }}
            ></div>
            <span style={{ fontSize: "1em" }}>
              {el.name} {el.y}
            </span>
          </div>
        ))}
      </div> */}
    </div>
  );
};
export const DonutChartWithDrillDownRmc = ({ data }) => {
  const seriesData = []; // Données principales pour le donut
  const drilldownData = []; // Données pour les détails (drilldown)

  if (data && data.data) {
    for (const [department, channels] of Object.entries(data.data)) {
      const total = channels.reduce((sum, channel) => sum + channel.y, 0);

      seriesData.push({
        name: department,
        y: total,
        drilldown: department, // Relier au drilldown
      });

      drilldownData.push({
        name: department,
        id: department,
        data: channels.map((channel) => [channel.name, channel.y]),
      });
    }
  }

  const options = {
    chart: {
      type: "pie",
      height: 350,
    },
    title: {
      text: data?.name,
      align: "left",
    },
    subtitle: {
      text: "",
    },
    plotOptions: {
      pie: {
        innerSize: "50%", // Donut chart
        depth: 45,
        dataLabels: {
          enabled: true,
          format: "{point.name}: {point.y}",
        },
        showInLegend: true, // Affiche dans la légende
      },
    },
    series: [
      {
        name: data?.parent_name,
        colorByPoint: true,
        data: seriesData,
      },
    ],
    drilldown: {
      series: drilldownData,
    },
    accessibility: {
      point: {
        valueSuffix: "%",
      },
    },
    exporting: {
      buttons: {
        contextButton: {
          menuItems: [
            "viewFullscreen", // Keep fullscreen option
            "printChart", // Keep Print option
            "separator",
            "downloadPNG", // Keep PNG download option
            "downloadJPEG", // Keep JPEG download option
            "downloadPDF", // Keep PDF download option
            "downloadSVG", // Keep SVG download option
            "downloadCSV", // Keep CSV download option
            "downloadXLS", // Keep XLS download option
            // 'viewData' // Remove or comment this line to exclude "View Data"
          ],
        },
      },
      // filename: "mon_graphique_exporte",
    },
  };

  return (
    <div>
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
};
export const DonutChartWithDrillDown = ({
  data,
  subtitle = "",
  height = 350,
  selected,
  legend = true,
}) => {
  const defaultSeries =
    data?.drilldown?.series?.find((el) => el.id === selected)?.data || [];
  const options = {
    chart: {
      type: "pie",
      height,
    },
    title: {
      text: data?.name,
      align: "left",
    },
    subtitle: {
      // text: selected
      //   ? `<span style="font-size: 14px; color: black;">${
      //       data?.parent_name !== undefined ? data.parent_name : ""
      //     } ${data?.series?.find((el) => el.name === selected)?.y || ""}</span>` // Correction ici
      //   : subtitle,
      useHTML: true,
      align: "left", // Centre le sous-titre
      // verticalAlign: "bottom", // Place le sous-titre en bas
      // y: selected ? 40 : subtitle ? 30 : 0,
    },
    plotOptions: {
      pie: {
        innerSize: "50%", // Donut chart
        depth: 45,
        dataLabels: {
          enabled: true,
          format: "{point.name}: {point.y}",
          distance: 2,
        },
        showInLegend: false, // Affiche dans la légende
      },
    },
    series: [
      {
        name: data?.parent_name,
        colorByPoint: true,
        data: selected ? defaultSeries : data?.series ?? [],
      },
    ],
    drilldown: data?.drilldown ?? { series: [] },
    accessibility: {
      point: {
        valueSuffix: "%",
      },
    },
    exporting: {
      buttons: {
        contextButton: {
          menuItems: [
            "viewFullscreen", // Keep fullscreen option
            "printChart", // Keep Print option
            "separator",
            "downloadPNG", // Keep PNG download option
            "downloadJPEG", // Keep JPEG download option
            "downloadPDF", // Keep PDF download option
            "downloadSVG", // Keep SVG download option
            "downloadCSV", // Keep CSV download option
            "downloadXLS", // Keep XLS download option
            // 'viewData' // Remove or comment this line to exclude "View Data"
          ],
        },
      },
      // filename: "mon_graphique_exporte",
    },
  };

  return (
    <div>
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
};
export const DonutChartWithDrillDownWithPercent = ({
  data,
  subtitle = "",
  height = 350,
  selected,
  showInLegend = true,
}) => {
  const defaultSeries =
    data?.drilldown?.series?.find((el) => el.id === selected)?.data || [];
  const options = {
    chart: {
      type: "pie",
      height,
      spacing: [0, 0, 10, 0],
    },
    title: {
      text: data?.name,
      align: "left",
    },
    subtitle: {
      useHTML: true,
      align: "left",
    },
    tooltip: {
      useHTML: true, // Enable HTML for the tooltip
      formatter: function () {
        return `<div style="display: flex; justify-content: space-between; align-items: center;color:${this.point.color}">
                        <span>${this.point.name}</span>
                       &nbsp; <b>${this.point.y}</b>
                    </div>`;
      },
    },
    plotOptions: {
      pie: {
        innerSize: "50%", // Donut chart
        depth: 45,
        dataLabels: {
          enabled: true,
          formatter: function () {
            const percentage = this.point.percentage;
            const name = this.point.name;
            return percentage > 0
              ? `${name}: ${
                  percentage % 1 === 0
                    ? percentage.toFixed(0)
                    : percentage.toFixed(1)
                }%`
              : "";
          },
          distance: -1, // Adjust distance from pie chart
          style: {
            fontSize: "10px", // Smaller font size
            textAlign: "center", // Center text
          },
        },
        showInLegend: false,
      },
    },
    series: [
      {
        name: data?.parent_name,
        colorByPoint: true,
        data: selected ? defaultSeries : data?.series ?? [],
      },
    ],
    drilldown: data?.drilldown ?? { series: [] },
    accessibility: {
      point: {
        valueSuffix: "%",
      },
    },
    exporting: {
      enabled: false, // Disable exporting
    },
  };

  return (
    <div style={{ width: "100%", height: "100%", margin: 0, padding: 0 }}>
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
};

export const StatsChart = ({ data, hasSelect = false, height = 350 }) => {
  const options = {
    chart: {
      type: "column", // Type de graphique pour les catégories
      height: hasSelect ? 277 : height,
    },
    title: {
      text: hasSelect ? "" : data?.name,
    },
    xAxis: {
      categories: data.categories,
    },
    yAxis: [
      {
        title: {
          text: "",
        },
      },
    ],
    legend: {
      enabled: false,
    },
    series: [
      {
        name: "",
        type: "column",
        data: data?.data,
      },
    ],
    annotations: [
      {
        labels: [
          {
            point: {
              x: 1.5, // Position X pour centrer la carte
              y: 8, // Ajustez la position Y pour qu'elle soit visible
              xAxis: 10,
              yAxis: 10,
            },
            text: data?.text, // Remplacez par vos valeurs
            backgroundColor: "rgba(255, 255, 255, 0.9)", // Couleur de fond de la carte
            borderWidth: 1,
            borderColor: "#ccc",
            style: {
              color: "#333", // Couleur du texte
              fontWeight: "bold", // Style du texte
            },
          },
        ],
      },
    ],
    exporting: {
      buttons: {
        contextButton: {
          menuItems: [
            "viewFullscreen", // Keep fullscreen option
            "printChart", // Keep Print option
            "separator",
            "downloadPNG", // Keep PNG download option
            "downloadJPEG", // Keep JPEG download option
            "downloadPDF", // Keep PDF download option
            "downloadSVG", // Keep SVG download option
            "downloadCSV", // Keep CSV download option
            "downloadXLS", // Keep XLS download option
            // 'viewData' // Remove or comment this line to exclude "View Data"
          ],
        },
      },
      // filename: "mon_graphique_exporte",
    },
  };

  return (
    <div>
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
};
export const DualAxes = ({ data }) => {
  const options = {
    chart: {
      zooming: {
        type: "xy",
      },
      height: 350,
    },
    title: {
      text: data?.name, // Titre représentant le chart
      align: "left",
    },
    xAxis: [
      {
        categories: data?.categories, // Assurez-vous que les catégories sont un tableau
        crosshair: true,
      },
    ],
    yAxis: [
      {
        // Primary yAxis
        labels: {
          format: "{value}",
          style: {
            color: Highcharts.getOptions().colors[1],
          },
          formatter: function () {
            return Math.round(this.value); // Round to the nearest integer
          },
        },
        title: {
          text: data?.colum_name, // Utiliser colum_name pour le titre de l'axe Y
          style: {
            color: Highcharts.getOptions().colors[1],
          },
        },
      },
      {
        // Secondary yAxis
        title: {
          text: data?.spline_name, // Utiliser spline_name pour le titre de l'axe Y secondaire
          style: {
            color: Highcharts.getOptions().colors[0],
          },
        },
        labels: {
          format: "{value} ",
          style: {
            color: Highcharts.getOptions().colors[0],
          },
        },
        opposite: true,
      },
    ],
    tooltip: {
      shared: true,
    },
    legend: {
      align: "left",
      verticalAlign: "top",
      backgroundColor:
        Highcharts.defaultOptions.legend.backgroundColor || // theme
        "rgba(255,255,255,0.25)",
    },
    series: [
      {
        name: data?.colum_name, // Utiliser colum_name pour le nom de la série
        type: "column",
        yAxis: 1,
        data: data?.colum_data, // Utiliser colum_data pour les données de la série
        tooltip: {
          valueSuffix: " ",
        },
      },
      {
        name: data?.spline_name, // Utiliser spline_name pour le nom de la série
        type: "spline",
        data: data?.colum_spline, // Utiliser colum_spline pour les données de la série
        tooltip: {
          valueSuffix: "",
        },
      },
    ],
  };

  return (
    <div>
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
};
export const SemiCirclePie = ({ data, isExistDate = true, height = 350 }) => {
  const options = {
    chart: {
      plotBackgroundColor: null,
      plotBorderWidth: 0,
      plotShadow: false,
      height: 350,
    },
    title: {
      text: `${data?.name || ""}`,
      align: "top",
      verticalAlign: "top",
      // y: 60,
      style: {
        fontSize: "1.1em",
      },
    },
    subtitle: {
      text: !isExistDate ? i18next.t("common:dashboard.independantNumber") : "",
      align: "left",
    },
    tooltip: {
      pointFormat: `${i18next.t(
        "common:dashboard.percentage"
      )}: <b> ({point.percentage:.1f}%)</b>`,
    },
    accessibility: {
      point: {
        valueSuffix: "%",
      },
    },
    plotOptions: {
      pie: {
        dataLabels: {
          enabled: true,
          distance: -30,
          style: {
            fontWeight: "bold",
            color: "white",
            textAlign: "center",
          },
          formatter: function () {
            return (
              this.point.name +
              '<br><strong style="font-size: 1.2em; display: block; text-align: center;">' +
              this.point.y +
              " %" + // Afficher le pourcentage
              "</strong>"
            );
          },
        },
        startAngle: -90,
        endAngle: 90,
        center: ["50%", "75%"],
        size: "110%",
      },
    },
    series: [
      {
        type: "pie",
        name: data?.content,
        innerSize: "50%",
        data: data?.data,
      },
    ],
    exporting: {
      buttons: {
        contextButton: {
          menuItems: [
            "viewFullscreen", // Keep fullscreen option
            "printChart", // Keep Print option
            "separator",
            "downloadPNG", // Keep PNG download option
            "downloadJPEG", // Keep JPEG download option
            "downloadPDF", // Keep PDF download option
            "downloadSVG", // Keep SVG download option
            "downloadCSV", // Keep CSV download option
            "downloadXLS", // Keep XLS download option
            // 'viewData' // Remove or comment this line to exclude "View Data"
          ],
        },
      },
    },
  };

  return <HighchartsReact highcharts={Highcharts} options={options} />;
};
