import { useState } from "react";
import { LoadingAnimation } from "../components/loader";
import { URL_ENV } from "index";

const RmcStats = () => {
  const [hide, setHide] = useState(false);
  const token = localStorage.getItem("accessToken");

  return (
    <div>
      {hide === false ? <LoadingAnimation /> : <></>}
      <iframe
        src={`${URL_ENV?.REACT_APP_RMC_URL}?Token=${token}&area=reports`}
        title="chat"
        display="block"
        width="100%"
        className="h-[calc(100vh-3px)] w-full flex-1 overflow-y-hidden border-none"
        // height= {`${deviceHeight}px -120px`}
        sendbox="allow-same-origin allow-popups"
        allowFullScreen={true}
        allowtransparency="true"
        onLoad={() => setHide(true)}></iframe>
    </div>
  );
};
export default RmcStats;
