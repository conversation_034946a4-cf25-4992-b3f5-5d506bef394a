import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import TabsDetails from "../../components/Tabs";
import { useLayoutEffect } from "react";

const Products = ({ active }) => {
  const [t] = useTranslation("common");
  const [keyTab, setKeyTab] = useState("");
  const { pathname } = useLocation();
  const navigate = useNavigate();
  useLayoutEffect(() => {
    if (pathname == "/settings/products/type") {
      setKeyTab("1");
    } else if (pathname == "/settings/products/family") {
      setKeyTab("2");
    } else if (pathname == `/settings/products/unity`) {
      setKeyTab("3");
    } else if (pathname == `/settings/products/discount`) {
      setKeyTab("4");
    } else if (pathname == `/settings/products`) {
      setKeyTab("5");
    } else {
      setKeyTab("5");
    }
  }, []);
  useEffect(() => {
    if (keyTab !== "") {
      if (keyTab == 1) {
        navigate("/settings/products/type");
      } else if (keyTab == 2) {
        navigate("/settings/products/family");
      } else if (keyTab == 3) {
        navigate(`/settings/products/unity`);
      } else if (keyTab == 4) {
        navigate(`/settings/products/discount`);
      } else if (keyTab == 5) {
        navigate(`/settings/products`);
      }
    }
  }, [keyTab, navigate]);

  const items = [
    {
      label: (
        <div
          onClick={() => {
            setKeyTab("5");
            navigate(`/settings/products`);
          }}
        >
          {t("menu2.products")}
        </div>
      ),
      key: "5",
    },
    {
      label: (
        <div
          onClick={() => {
            setKeyTab("1");
            navigate(`/settings/products/type`);
          }}
        >
          {t("menu2.type")}
        </div>
      ),
      key: "1",
    },
    {
      label: (
        <div
          onClick={() => {
            setKeyTab("2");
            navigate(`/settings/products/family`);
          }}
        >
          {t("menu2.family")}
        </div>
      ),
      key: "2",
    },
    {
      label: (
        <div
          onClick={() => {
            setKeyTab("3");
            navigate(`/settings/products/unity`);
          }}
        >
          {t("menu2.unity")}
        </div>
      ),
      key: "3",
    },
    {
      label: (
        <div
          onClick={() => {
            setKeyTab("4");
            navigate(`/settings/products/discount`);
          }}
        >
          {t("menu2.discount")}
        </div>
      ),
      key: "4",
    },
  ];

  return (
    <>
      {keyTab ? (
        <TabsDetails items={items} keyTab={keyTab} setKey={setKeyTab} />
      ) : (
        ""
      )}
    </>
  );
};

export default Products;
