import { Form, Radio, Space } from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";

import { toastNotification } from "components/ToastNotification";
import { setConfigChatUser } from "new-redux/services/chat.services";
import { FormFooter } from "pages/components/FormFooter";

function ChatNotification() {
  const [form] = Form.useForm();
  const { currentUser } = useSelector((state) => state.chat);
  const [t] = useTranslation("common");

  const [disabled, setDisabled] = useState(true);
  const [loading, setLoading] = useState(false);
  const dispatch = useDispatch();

  const onFinish = async (values) => {
    setLoading(true);
    try {
      dispatch(
        setConfigChatUser({
          sort_message: currentUser?.config?.sort_message,
          hidden_message: currentUser?.config?.hidden_message,
          sound_notification: values.sound_notification,
          notification: values.notification,
        })
      );

      setLoading(false);
      setDisabled(true);
      toastNotification(
        "success",
        t("notifications.notificationUpdated"),
        "topRight"
      );
    } catch (e) {
      setLoading(false);

      console.log(e);
    }
  };
  const onValuesChange = () => {
    const valuesForm = form.getFieldsValue();
    const data = {
      sound_notification: currentUser?.config?.sound_notification,
      notification: currentUser?.config?.notification,
    };
    let sameValues = true;

    for (const key in valuesForm) {
      if (valuesForm.hasOwnProperty(key)) {
        if (valuesForm[key] !== data[key]) {
          sameValues = false;
          break;
        }
      }
    }

    if (sameValues) {
      setDisabled(true);
    } else {
      setDisabled(false);
    }
  };
  const onFinishFailed = () => {};

  return (
    <div className="flex items-center justify-start p-6">
      <Form
        form={form}
        name="basic"
        labelCol={{
          span: 24,
        }}
        wrapperCol={{
          span: 24,
        }}
        initialValues={{
          sound_notification: !isNaN(currentUser?.config?.sound_notification)
            ? currentUser?.config?.sound_notification
            : 1,
          notification: !isNaN(currentUser?.config?.notification)
            ? currentUser?.config?.notification
            : 1,
        }}
        onFinish={onFinish}
        onFinishFailed={onFinishFailed}
        autoComplete="off"
        scrollToFirstError
        layout="vertical"
        onValuesChange={onValuesChange}
      >
        <Form.Item
          label={t("notifications.senddesktopnotifications")}
          name="notification"
        >
          <Radio.Group>
            <Space direction="vertical">
              <Radio value={1}>{t("notifications.forallactivities")}</Radio>
              <Radio value={2}>
                {t("notifications.onlyforpersonalmentionsandmessages")}
              </Radio>
              <Radio value={0}>{t("notifications.never")}</Radio>
            </Space>
          </Radio.Group>
        </Form.Item>

        <Form.Item
          label={t("notifications.notificationsound")}
          name="sound_notification"
        >
          <Radio.Group>
            <Space direction="vertical">
              <Radio value={1}>{t("notifications.on")}</Radio>
              <Radio value={0}>{t("notifications.off")}</Radio>
            </Space>
          </Radio.Group>
        </Form.Item>
        <FormFooter
          onClickCancel={() => {
            setDisabled(true);
            form.resetFields();
          }}
          loadButton={loading}
          submitDisabled={disabled}
        />
      </Form>
    </div>
  );
}

export default ChatNotification;
