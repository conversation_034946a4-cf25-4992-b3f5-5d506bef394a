import React, { memo, useEffect, useMemo, useState } from "react";
import { Badge, Button, Popover, Space, Typography, Select } from "antd";
import { useTranslation } from "react-i18next";
import { AlignRightOutlined } from "@ant-design/icons";
// import { sortableTypes } from "pages/clients&users/FamilyRouting";
import { FaRegTrashCan } from "react-icons/fa6";
import { Refs_IDs } from "components/tour/tourConfig";

const SortFamilyTables = ({ sortedInfo, handleSort, disabled, columns }) => {
  //
  const [t] = useTranslation("common");
  //
  // const [sortableColumns, setSortableColumns] = useState([]);
  const [selectedColumn, setSelectedColumn] = useState(null);
  const [selectedOrder, setSelectedOrder] = useState(null);
  //
  // console.log({ selectedColumn, selectedOrder, sortedInfo });
  // console.log(columns);
  const sortableColumns = useMemo(() => {
    if (!columns?.length) return [];

    return columns.reduce((acc, { title, alias, dataIndex, children }) => {
      // grouped columns
      if (children?.length) {
        acc.push({
          label: <span className="font-semibold">{title}</span>,
          title,
          options: children.map(
            ({
              title: childTitle,
              alias: childAlias,
              dataIndex: childIndex,
            }) => ({
              label: title === "Timestamps" ? childTitle : childAlias,
              value: childIndex,
            })
          ),
        });
      }
      // standalone column
      else if (alias && dataIndex) {
        acc.push({ label: alias, value: dataIndex });
      }

      return acc;
    }, []);
  }, [columns]);
  //
  // track the field selected & order selected
  useEffect(() => {
    if (sortedInfo.field && sortedInfo.field !== selectedColumn) {
      setSelectedColumn(sortedInfo.field);
    }
    if (sortedInfo.order && sortedInfo.order !== selectedOrder) {
      setSelectedOrder(sortedInfo.order);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sortedInfo]);
  //
  const sortContent = useMemo(
    () => (
      <div className="min-w-44 max-w-96 p-2.5	">
        <Space.Compact block>
          <Select
            showSearch
            allowClear
            placeholder={t("filterFamily.selectField")}
            defaultValue={null}
            popupMatchSelectWidth={false}
            dropdownStyle={{
              maxWidth: "16rem",
            }}
            style={{
              width: "12rem",
            }}
            optionFilterProp="label"
            filterOption={(input, option) => {
              if (typeof option.label === "string") {
                return option.label.toLowerCase().includes(input.toLowerCase());
              }
              return false;
            }}
            options={sortableColumns}
            // onChange={(value) => setSortedInfo((p) => ({ ...p, field: value }))}
            onChange={(value) => {
              setSelectedColumn(value);
              setSelectedOrder(null);
            }}
            value={selectedColumn}
          />
          {!!selectedColumn && (
            <Select
              allowClear
              placeholder={t("filterFamily.selectOrder")}
              defaultValue={null}
              popupMatchSelectWidth={true}
              style={{
                width: "10rem",
              }}
              options={[
                {
                  label: t("filterFamily.ascendant"),
                  value: "ascend",
                },
                { label: t("filterFamily.descendant"), value: "descend" },
              ]}
              onChange={(value) => {
                // setSortedInfo((p) => ({
                //   ...p,
                //   order: value,
                //   field: selectedColumn,
                // }));
                handleSort({ order: value, field: selectedColumn });
                setSelectedOrder(value);
              }}
              value={selectedOrder}
            />
          )}
          {!!sortedInfo.order && sortedInfo.field && (
            <Button
              icon={<FaRegTrashCan />}
              onClick={() => {
                handleSort({});
                setSelectedColumn(null);
              }}
            />
          )}
        </Space.Compact>
      </div>
    ),
    [
      selectedColumn,
      selectedOrder,
      handleSort,
      sortableColumns,
      sortedInfo.field,
      sortedInfo.order,
      t,
    ]
  );
  //
  return (
    <Popover
      arrow={false}
      trigger="click"
      placement="bottomLeft"
      content={sortContent}
      overlayInnerStyle={{ padding: 2 }}
    >
      <Button
        ref={Refs_IDs.families_sort_icon}
        disabled={disabled || !sortableColumns.length}
        icon={
          <Badge dot={!!sortedInfo?.order}>
            <AlignRightOutlined
              style={{
                color: !disabled || !sortableColumns.length ? "#1677ff" : "",
              }}
            />
          </Badge>
        }
        type="text"
        style={{ paddingLeft: 8, paddingRight: 8 }}
      >
        <Space size={4}>
          {disabled || !sortableColumns.length ? (
            t("filterFamily.sort")
          ) : (
            <Typography.Link>{t("filterFamily.sort")}</Typography.Link>
          )}
        </Space>
      </Button>
    </Popover>
  );
};

export default memo(SortFamilyTables);
