import {
  DELETE_FIELD_OPTION_SUCCESS,
  DELETE_FIELD_OPTION_ERROR,
  DELETE_FIELD_OPTION_LOADING,
} from "../../constants";
import MainService from "../../../services/main.service";

export const deleteFieldOption = (fieldId, optionId) => async (dispatch) => {
  try {
    dispatch({ type: DELETE_FIELD_OPTION_LOADING });
    const response = await MainService.deleteSpecificOptionValue(fieldId, optionId);
    dispatch({
      type: DELETE_FIELD_OPTION_SUCCESS,
      payload: { optionId, fieldId },
    });
  } catch (error) {
    if (error?.status !== 401) {
      dispatch({
        type: DELETE_FIELD_OPTION_ERROR,
        payload: error,
      });
    }
  }
};
