import React from "react";
import { Input, Button } from "antd";
import { PhoneFilled, PhoneOutlined } from "@ant-design/icons";
import { PhoneIcon } from "@heroicons/react/20/solid";

const onChange = (value) => {
  console.log(`selected ${value}`);
};
const onSearch = (value) => {
  console.log("search:", value);
};

const Inputwebphone = () => (
  <div className="shadow-sm">
    <Input
      defaultValue="Me<PERSON> (106)"
      allowClear
      placeholder="Type name or number"
    />
  </div>
);
export default Inputwebphone;
