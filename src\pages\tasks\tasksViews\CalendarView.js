/**
 * @name CalendarView
 *
 * @description `CalendarView` component is responsible for displaying the list of activities in a calendar.
 * Refer to https://fullcalendar.io/docs/react
 *
 *
 * @param {Array} events - The array of events to
 * @param {Array} tasksTypes .
 * @param {Number} selectedPipeline .
 * @param {Array} selectedRoles .
 * @param {String} selectedFamily .
 * @param {Number} countChanges .
 * @param {Boolean} openActivity360 .
 * @param {String} switchViews .
 * @param {String} filterTable - The filter table for tasks.
 * @param {Number} activeStage .
 * @param {String} activePriority .
 * @param {Object} showCardPopover .
 * @param {Array} filterCondition .
 * @param {Function} setRoomActivityId .
 * @param {Function} setSelectedStartDate - Function to set the selected start date.
 * @param {Function} setSelectedEndDate - Function to set the selected end date.
 * @param {Function} setSelectedStartTime - Function to set the selected start time.
 * @param {Function} setSelectedEndTime - Function to set the selected end time.
 * @param {Function} setTaskToUpdate - Function to set the task to update.
 * @param {Function} setEvents .
 * @param {Function} setActivityLabel .
 * @param {Function} setTotal .
 * @param {Function} setOpenActivity360 .
 * @param {Function} setCountChanges .
 * @param {Function} setUpdateFilters .
 *
 * @returns {JSX.Element} Calendar view.
 */

import { useState, useEffect, useCallback, useRef } from "react";
import { Popover, Spin, Typography } from "antd";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import interactionPlugin from "@fullcalendar/interaction";
import timeGridPlugin from "@fullcalendar/timegrid";
import listPlugin from "@fullcalendar/list";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";
import allLocales from "@fullcalendar/core/locales-all";
import { useDispatch } from "react-redux";
import DOMPurify from "dompurify";

import MainService from "../../../services/main.service";
import { toastNotification } from "../../../components/ToastNotification";
import ChoiceIcons from "../../components/ChoiceIcons";
import { setOpenTaskDrawer } from "../../../new-redux/actions/tasks.actions/handleTaskDrawer";
import { useSelector } from "react-redux";
import { useWindowSize } from "../../clients&users/components/WindowSize";
import { handleReload } from "../helpers/handleCheck";
import DropdownTask from "components/DropdownTask";
import TaskItem from "./kanban/TaskItem";
import { setChatSelectedConversation } from "new-redux/actions/chat.actions";
import { closeDrawerChat } from "new-redux/actions/voip.actions/handleDrawerChat";
import { isGuestConnected } from "utils/role";
import { setOpenTaskRoomDrawer } from "new-redux/actions/tasks.actions/handleTaskRoomDrawer";
import { formatDates, getPriorityColor } from "../helpers/calculateSum";

const CalendarView = ({
  setSelectedStartDate,
  setSelectedEndDate,
  setSelectedStartTime,
  setSelectedEndTime,
  setTaskToUpdate,
  filterTable,
  events,
  setEvents,
  tasksTypes,
  selectedPipeline,
  selectedRoles,
  selectedFamily,
  setActivityLabel,
  setTotal,
  setOpenActivity360,
  countChanges,
  openActivity360,
  setCountChanges,
  switchViews,
  activeStage,
  activePriority,
  showCardPopover,
  setUpdateFilters,
  filterCondition,
  setRoomActivityId,
  selectedUser,
  selectedTags,
}) => {
  const [loadCalendarTasks, setLoadCalendarTasks] = useState(false);
  const [selectedCalendarView, setSelectedCalendarView] =
    useState("dayGridMonth");
  const [updateEventLoader, setUpdateEventLoader] = useState(false);
  const [calendarEdges, setCalendarEdges] = useState({
    startEdge: null,
    endEdge: null,
  });
  const [getActivitiesError, setGetActivitiesError] = useState(false);
  const [optionTask, setOptionTask] = useState("");
  const languageFromLocalStorage = localStorage.getItem("language");
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const windowSize = useWindowSize();
  const popoverRef = useRef(null);
  const calendarRef = useRef(null);

  const {
    isUserNotified,
    selectedViewInTask,
    taskNotifActionType,
    taskNotifPayload,
  } = useSelector((state) => state?.TasksRealTime);
  const { user } = useSelector((state) => state?.user);
  const { search } = useSelector((state) => state?.form);

  //Retrieve actvities by date range (ex 1st -> 31st of the month).
  const getTasksByTimeRange = useCallback(async () => {
    try {
      if (
        calendarEdges?.startEdge !== null &&
        calendarEdges?.endEdge !== null
      ) {
        // let payload = {
        //   search_param,
        // };
        let formData = new FormData();
        formData.append(
          "start",
          dayjs(calendarEdges?.startEdge).format("YYYY-MM-DD")
        );
        formData.append(
          "end",
          dayjs(calendarEdges?.endEdge)
            .subtract(1, "milliseconds")
            .format("YYYY-MM-DD")
        );
        formData.append("type_task", filterTable);
        formData.append("search", DOMPurify.sanitize(search));
        formData.append(
          "pipeline_id",
          selectedPipeline === null ? 0 : selectedPipeline
        );
        formData.append(
          "roles",
          selectedRoles ? selectedRoles?.toString() : ""
        );
        // *****//
        formData.append("priorities", activePriority);
        formData.append("stages_ids", activeStage);
        formData.append("logic", filterCondition);
        formData.append("user_id", selectedUser?.id?.toString() || user?.id);
        if (Array.isArray(selectedFamily) && selectedFamily?.length === 2) {
          formData.append("family_id", selectedFamily[0]?.toString());
          formData.append("related_element", selectedFamily[1]?.toString());
        }
        if (Array.isArray(selectedTags) && selectedTags?.length > 0) {
          selectedTags.forEach((id) => {
            formData.append("tags_ids[]", id);
          });
        }
        // formData.append("search_param", selectedFamily ? selectedFamily : "");
        setLoadCalendarTasks(true);
        const response = await MainService.getCalendarTasks(formData);
        setEvents(
          response?.data?.data?.map((element) => ({
            id: element?.id,
            title: element?.label,
            start: formatDates(user, element?.start_date, element?.start_time),
            end: formatDates(user, element?.end_date, element?.end_time),
            display: "block",
            owner: element?.owner_id?.id,
            creator: element?.creator?.id,
            tasks_type_id: element?.tasks_type_id,
            icon: element?.icon,
            can_update_task: element?.can_update_task,
            can_create_room: element?.can_create_room,
            backgroundColor: getPriorityColor(element?.priority),
            borderColor: getPriorityColor(element?.priority),
            element_label: element?.element_label,
            family_label: element?.family_label,
            reminder: element?.reminder,
            is_overdue: element?.is_overdue,
            is_final_stage: element?.is_final_stage,
            is_follower: element?.is_follower,
            upload: element?.upload,
            stage_id: element?.stage_id,
            stage_label: element?.stage_label,
            pipeline_label: element?.pipeline_label,
            priority: element?.priority,
            files: element?.files,
            followers: element?.followers,
            owner_id: element?.owner_id,
            guests: element?.guests,
            label: element?.label,
            end_date: element?.end_date,
            end_time: element?.end_time,
            start_date: element?.start_date,
            start_time: element?.start_time,
            visio_in_progress: element?.visio_in_progress ?? undefined,
          }))
        );
        setTotal(response?.data?.data?.length);
        setLoadCalendarTasks(false);
      }
      getActivitiesError && setGetActivitiesError(false);
    } catch (error) {
      console.log(`Error ${error}`);
      setGetActivitiesError(true);
      setLoadCalendarTasks(false);
    }
  }, [
    calendarEdges?.endEdge,
    calendarEdges?.startEdge,
    filterTable,
    search,
    selectedPipeline,
    selectedRoles,
    selectedFamily,
    activeStage,
    activePriority,
    filterCondition,
    selectedUser,
    selectedTags,
  ]);
  // Dans le code qui ferme le popover
  const popoverCheckTimeout = useRef(null);

  useEffect(() => {
    const handlePopover = () => {
      document.querySelectorAll(".fc-popover").forEach((popover) => {
        // Bloque la fermeture au clic extérieur
        popover.style.pointerEvents = "auto";

        // Gère uniquement la fermeture via le bouton X
        const closeBtn = popover.querySelector(".fc-popover-close");
        if (closeBtn) {
          closeBtn.onclick = (e) => {
            e.stopPropagation();
            popover.style.display = "none";
          };
        }
      });
    };

    // Observer les changements DOM
    const observer = new MutationObserver(handlePopover);
    observer.observe(document.body, { subtree: true, childList: true });

    return () => observer.disconnect();
  }, []);

  // Trigger get tasks on mount component.
  useEffect(() => {
    getTasksByTimeRange();
    return () => setEvents([]);
  }, [getTasksByTimeRange]);

  //Re-trigger get tasks when update a single activity.
  useEffect(() => {
    if (switchViews === "Calendar" && countChanges > 0 && !openActivity360) {
      getTasksByTimeRange();
      setCountChanges(0);
    }
  }, [countChanges, openActivity360, switchViews]);

  //Update calendar when mercure event
  useEffect(() => {
    if (isUserNotified) {
      if (selectedViewInTask === "Calendar" && taskNotifPayload !== null) {
        getTasksByTimeRange();
      }
    }
  }, [
    dispatch,
    isUserNotified,
    selectedViewInTask,
    taskNotifActionType,
    taskNotifPayload,
    events,
  ]);

  // Get dates and times on select cell(s).
  const handleSelect = (info) => {
    if (isGuestConnected()) {
      return;
    }
    const { start, end } = info;
    dispatch(setOpenTaskDrawer(true));
    setSelectedStartDate(
      dayjs(start)
        .format(`${user?.location?.date_format} ${user?.location?.time_format}`)
        .split(" ")[0]
    );
    setSelectedStartTime(
      dayjs(start)
        .format(`${user?.location?.date_format} ${user?.location?.time_format}`)
        .split(" ")[1]
    );
    setSelectedEndDate(
      dayjs(end)
        .format(`${user?.location?.date_format} ${user?.location?.time_format}`)
        .split(" ")[0]
    );
    setSelectedEndTime(
      dayjs(end)
        .format(`${user?.location?.date_format} ${user?.location?.time_format}`)
        .split(" ")[1]
    );
  };

  // Handle click on calendar's cell.
  const handleClickOnEvent = (clickedEvent) => {
    handleOpenActivityIn360(clickedEvent?.event);
  };

  // Get event's data function.
  const getSpecificEventFromCalendar = (event) => {
    let specificEvent = events && events.find((el) => el?.id === event);
    return specificEvent;
  };

  //Handle open edit drawer.
  const editTask = (props) => {
    setActivityLabel(props?.title);
    dispatch(setOpenTaskDrawer(true));
    setTaskToUpdate(props?.id);
  };

  // Handle click on event slot.
  const handleOpenActivityIn360 = (props) => {
    dispatch(
      setChatSelectedConversation({
        selectedConversation: null,
      })
    );
    dispatch(closeDrawerChat());
    setTaskToUpdate(props?.id);
    setOpenActivity360(true);
  };

  //Handle open chat room.
  const openChat = (id) => {
    dispatch(setOpenTaskRoomDrawer(true));
    setRoomActivityId(id);
  };

  // Custom event content that'd be injected in calendar's DOM.
  const renderEventContent = (eventInfo) => {
    const closeFullCalendarPopover = () => {
      // Vérifie si un popover FullCalendar existe
      const fcPopovers = document.querySelectorAll(".fc-popover");

      if (fcPopovers.length > 0) {
        // Ferme tous les popovers FullCalendar
        fcPopovers.forEach((popover) => {
          popover.style.display = "none";
        });

        // Alternative plus ciblée pour le popover actif
        // const activePopover = document.querySelector('.fc-popover.fc-more-popover');
        // if (activePopover) activePopover.style.display = 'none';
      }
    };
    if (optionTask) {
      closeFullCalendarPopover();
    }
    let idEvent =
      typeof eventInfo?.event?.id !== "undefined"
        ? eventInfo?.event?.id
        : eventInfo?.event?._def?.publicId;
    let specificEvent = getSpecificEventFromCalendar(idEvent);
    return (
      <div
        onMouseDown={(evt) => {
          if (optionTask) return;
          evt.stopPropagation();
          evt.preventDefault();
        }}
      >
        <div
          key={`eventSlot${eventInfo?.event?.id}`}
          className={`flex ${
            selectedCalendarView !== "listWeek" && "w-full"
          } flex-row items-center justify-between`}
          ref={popoverRef}
        >
          <Popover
            key={`eventSlotPopover_${idEvent}`}
            trigger={!isGuestConnected() ? ["hover", "click"] : null}
            autoAdjustOverflow={true}
            destroyTooltipOnHide
            onOpenChange={(open) => !open && setOptionTask("")}
            content={
              <div ref={popoverRef}>
                <TaskItem
                  key={eventInfo?.event?.id}
                  id={eventInfo?.event?.id}
                  setTaskToUpdate={setTaskToUpdate}
                  content={specificEvent}
                  deleteTask={deleteTask}
                  tasksTypes={tasksTypes}
                  setOpenActivity360={setOpenActivity360}
                  setActivityLabel={setActivityLabel}
                  source="calendar"
                />
              </div>
            }
            overlayStyle={{ width: "30%", zIndex: "99999" }}
            open={showCardPopover[idEvent]}
          >
            <div className={`flex grow items-center truncate`}>
              <div className="flex w-auto flex-row items-center truncate">
                <div style={{ fontSize: "14px", marginRight: "3px" }}>
                  <ChoiceIcons
                    icon={
                      tasksTypes &&
                      tasksTypes.find(
                        (el) =>
                          Number(el?.id) ===
                          Number(getSpecificEventFromCalendar(idEvent)?.icon)
                      )?.icons
                    }
                  />
                </div>
                <Typography.Text
                  ellipsis
                  style={{
                    color:
                      selectedCalendarView === "listWeek" ? "#000" : "#fff",
                    fontSize: "12px",
                  }}
                >
                  {eventInfo?.event?.title}
                </Typography.Text>
              </div>

              {getSpecificEventFromCalendar(idEvent)?.id ===
                eventInfo?.event?.id && selectedCalendarView !== "listWeek" ? (
                !getSpecificEventFromCalendar(idEvent)
                  ?.start_time ? null : !getSpecificEventFromCalendar(idEvent)
                    ?.end_time ? (
                  <span>
                    {getSpecificEventFromCalendar(idEvent)?.start_time}
                  </span>
                ) : (
                  <div>
                    <span>
                      {getSpecificEventFromCalendar(idEvent)?.start_time}
                    </span>{" "}
                    -{" "}
                    <span>
                      {getSpecificEventFromCalendar(idEvent)?.end_time}
                    </span>
                  </div>
                )
              ) : null}
            </div>
          </Popover>
          <div
            className="opacity-0 transition duration-200 group-hover:opacity-100"
            onMouseEnter={(e) => e.stopPropagation()}
          >
            <DropdownTask
              source="activity-calendar"
              props={getSpecificEventFromCalendar(eventInfo?.event?.id)}
              handleDelete={deleteTask}
              editTask={editTask}
              handleOpenActivityIn360={handleOpenActivityIn360}
              selectedCalendarView={selectedCalendarView}
              openChat={openChat}
              setOptionTask={setOptionTask}
            />
          </div>
        </div>
      </div>
    );
  };

  // Delete specific task.
  const deleteTask = async (id) => {
    try {
      let formData = new FormData();
      formData.append("id[]", id);
      const response = await MainService.deleteSpecificTask(formData);
      if (response?.status === 200) {
        getTasksByTimeRange();
        toastNotification("success", t("toasts.taskDeleted"), "bottomRight");
        setUpdateFilters((prev) => prev + 1);
      }
    } catch (error) {
      console.log(`Error ${error}`);
      toastNotification("error", t("toasts.somethingWrong"));
    }
  };

  //Update task's date on drag'n'drop.
  const handleUpdateTaskOnDrop = async (eventInfo) => {
    try {
      let newStartDate = eventInfo?.event?.start;
      let newEndDate = eventInfo?.event?.end;
      let payload = {
        start_date: dayjs(newStartDate)
          .format(
            `${user?.location?.date_format} ${user?.location?.time_format}`
          )
          .split(" ")[0],
        start_time: dayjs(newStartDate)
          .format(
            `${user?.location?.date_format} ${user?.location?.time_format}`
          )
          .split(" ")
          .splice(1, 2)
          .join(" ")
          .toUpperCase(),
        end_date: dayjs(newEndDate)
          .format(
            `${user?.location?.date_format} ${user?.location?.time_format}`
          )
          .split(" ")[0],
        end_time: dayjs(newEndDate)
          .format(
            `${user?.location?.date_format} ${user?.location?.time_format}`
          )
          .split(" ")
          .splice(1, 2)
          .join(" ")
          .toUpperCase(),
      };
      setUpdateEventLoader(true);
      const response = await MainService.updateTaskDateTime(
        eventInfo?.event?.id,
        payload
      );
      if (response?.data?.success) {
        getTasksByTimeRange();
        toastNotification("success", t("toasts.taskUpdated"), "bottomRight");
        setUpdateEventLoader(false);
      }
    } catch (error) {
      setUpdateEventLoader(false);
      console.log(`Error ${error}`);
    }
  };

  // Allow drag and drop event slot.
  const handleEventAllow = (dropInfo, draggedEvent) => {
    const draggedEventSlot =
      events &&
      events.find((task) => task?.id === draggedEvent?._def?.publicId);
    return draggedEventSlot?.can_update_task === 1;
  };

  // Retrieve date range on load the calendar on DOM.
  const handleDatesRange = (datesInfo) => {
    setSelectedCalendarView(datesInfo?.view?.type);
    if (calendarEdges.startEdge !== datesInfo?.startStr) {
      setCalendarEdges({
        ...calendarEdges,
        startEdge: datesInfo?.startStr,
        endEdge: datesInfo?.endStr,
      });
    }
  };

  //Fullcalendar configuration props
  let props = {
    eventResizableFromStart: true,
    locales: allLocales,
    nowIndicator: true,
    editable: true,
    selectable: true,
    expandRows: true,
    dayMaxEvents: true,
    navLinks: true,
    selectMirror: true,
    displayEventTime: true,
    locale: languageFromLocalStorage === "en" ? "en-gb" : "fr",
    lazyFetching: true,
  };

  return (
    <Spin spinning={loadCalendarTasks || updateEventLoader}>
      <FullCalendar
        ref={calendarRef}
        headerToolbar={{
          start: getActivitiesError
            ? `today prev next errorBtn`
            : "today prev next",
          center: "title",
          end: "dayGridMonth,timeGridWeek,timeGridDay,listWeek",
        }}
        customButtons={{
          errorBtn: {
            text: t("tasks.calendarTryAgain"),
            class: "btn btn-danger",
            click: handleReload,
          },
        }}
        buttonText={{
          today: t("tasks.TodayView"),
          month: t("tasks.monthView"),
          week: t("tasks.weekView"),
          day: t("tasks.dayView"),
          list: t("tasks.listView"),
        }}
        views={{
          monthView: {
            eventOverlap: false,
          },
        }}
        eventClassNames={"activities-calendar group"}
        height={windowSize?.height - 180}
        eventContent={renderEventContent}
        plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin, listPlugin]}
        events={events}
        datesSet={handleDatesRange}
        select={handleSelect}
        eventClick={handleClickOnEvent}
        eventResize={handleUpdateTaskOnDrop}
        eventDrop={handleUpdateTaskOnDrop}
        businessHours={{
          startTime: "08:00",
          endTime: "00:00",
        }}
        eventAllow={handleEventAllow}
        {...props}
      />
    </Spin>
  );
};

export default CalendarView;
