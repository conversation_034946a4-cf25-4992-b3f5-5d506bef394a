import React, { useState } from "react";
import { Layout, Menu, Typography, Divider, Button, Space, Modal } from "antd";
import {
  HomeOutlined,
  FileTextOutlined,
  BookOutlined,
  DollarOutlined,
  CalendarOutlined,
} from "@ant-design/icons";
import AIPolicy from "./components/Policies/AiPolicy";
import WebPolicy from "./components/Policies/WebPolicy";
import MobilePolicy from "./components/Policies/MobilePolicy";

const { Header, Content, Footer } = Layout;
const { Title, Paragraph, Text } = Typography;

export default function ComunikImprintPage() {
  const [typeOpenModal, setTypeOpenModal] = useState("");
  const menuItems = [
    {
      key: "home",
      icon: <HomeOutlined />,
      label: "Accueil",
    },
    {
      key: "stories",
      label: "Témoignages Clients",
    },
    {
      key: "resources",
      label: "Ressources",
    },
    {
      key: "pricing",
      label: "Tarifs",
    },
  ];

  const sidebarItems = [
    "Politique de cookies",
    "RGPD & Protection des données",
    "Accord Partenaire Commercial HIPAA",
    "Traitement fiscal",
    "Politique de confidentialité",
    "Mentions légales",
    "Conditions générales",
    "Confiance & Sécurité",
  ];

  return (
    <Layout style={{ backgroundColor: "white" }}>
      {/* Header */}
      {/* <Header
        style={{
          backgroundColor: "white",
          padding: "0 50px",
          boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
          position: "sticky",
          top: 0,
          zIndex: 1000,
        }}
      >
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            height: "64px",
          }}
        >
          <div style={{ display: "flex", alignItems: "center" }}>
            <div
              style={{
                width: 40,
                height: 40,
                backgroundColor: "#6366f1",
                borderRadius: "8px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                marginRight: 12,
              }}
            >
              <Text
                style={{ color: "white", fontWeight: "bold", fontSize: "18px" }}
              >
                C
              </Text>
            </div>
            <Title level={3} style={{ margin: 0, color: "#1f2937" }}>
              comunik
            </Title>
          </div>

          <Menu
            mode="horizontal"
            items={menuItems}
            style={{
              border: "none",
              backgroundColor: "transparent",
              flex: 1,
              justifyContent: "center",
            }}
          />

          <Button
            type="primary"
            style={{
              backgroundColor: "#6366f1",
              borderColor: "#6366f1",
              borderRadius: "6px",
              fontWeight: "500",
            }}
          >
            RÉSERVER DÉMO
          </Button>
        </div>
      </Header> */}

      <Layout style={{ background: "white" }}>
        <Content style={{ padding: "0 24px" }}>
          <div
            style={{
              display: "flex",
              gap: "40px",
              //   maxWidth: "1200px",
              margin: "24px auto",
              alignItems: "flex-start",
            }}
          >
            {/* Main Content */}
            <div
              style={{
                flex: 1,
                backgroundColor: "white",
                padding: "24px",
                borderRadius: "8px",
                // boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
              }}
            >
              {/* Breadcrumb
              <div
                style={{
                  marginBottom: "30px",
                  color: "#6b7280",
                  fontSize: "14px",
                }}
              >
                <Text>Accueil</Text> /{" "}
                <Text style={{ color: "#1f2937" }}>Mentions légales</Text>
              </div> */}

              <Title level={2}>Politiques de Confidentialité</Title>

              {/* Highlighted Section */}
              <div
                style={{
                  backgroundColor: "#f8fafc",
                  padding: "30px",
                  borderRadius: "8px",
                  marginBottom: "24px",
                  border: "1px solid #e2e8f0",
                  marginTop: "20px",
                }}
              >
                <Title
                  level={3}
                  style={{
                    color: "#1f2937",
                    marginBottom: "20px",
                    fontSize: "22px",
                  }}
                >
                  <strong>Vos données, notre priorité.</strong>
                </Title>
                <Paragraph
                  style={{
                    fontSize: "16px",
                    lineHeight: "1.6",
                    color: "#4b5563",
                    marginTop: "8px",
                    marginBottom: "20px",
                  }}
                >
                  Chez <strong>Comunik</strong>, la transparence et la
                  protection de vos données personnelles sont au cœur de nos
                  engagements. Nous mettons à votre disposition l'ensemble de
                  nos politiques pour que vous puissiez comprendre comment nous
                  collectons, utilisons et protégeons vos informations, ainsi
                  que notre position sur l'usage responsable de l'intelligence
                  artificielle.
                </Paragraph>

                <Paragraph
                  style={{
                    fontSize: "16px",
                    color: "#4b5563",
                    marginBottom: "20px",
                  }}
                >
                  Veuillez consulter nos politiques :
                </Paragraph>

                <div className="mt-2 flex flex-col items-start">
                  <Button type="link" onClick={() => setTypeOpenModal("web")}>
                    Politique de confidentialité – Application Web
                  </Button>
                  <Button
                    type="link"
                    onClick={() => setTypeOpenModal("mobile")}
                  >
                    Politique de confidentialité – Application Mobile
                  </Button>
                  <Button type="link" onClick={() => setTypeOpenModal("ia")}>
                    Politique d'utilisation de l'intelligence artificielle
                  </Button>
                </div>

                <Paragraph
                  style={{
                    fontSize: "16px",
                    color: "#4b5563",
                    marginBottom: "10px",
                  }}
                >
                  Ces documents ont pour but de :
                </Paragraph>

                <ul style={{ paddingLeft: "20px" }} className=" mt-3 space-y-4">
                  <li style={{ marginBottom: "8px", color: "#4b5563" }}>
                    Garantir la{" "}
                    <strong>protection de vos données personnelles</strong> ;
                  </li>
                  <li style={{ marginBottom: "8px", color: "#4b5563" }}>
                    Vous informer sur les{" "}
                    <strong>droits dont vous disposez</strong> ;
                  </li>
                  <li style={{ marginBottom: "8px", color: "#4b5563" }}>
                    Expliquer{" "}
                    <strong>notre usage des technologies modernes</strong>, y
                    compris l'IA, de manière éthique et responsable.
                  </li>
                </ul>
              </div>
              {/* 
              <Title
                level={3}
                style={{
                  color: "#1f2937",
                  marginBottom: "20px",
                  fontSize: "20px",
                }}
              >
                Informations selon § 5 TMG
              </Title>

              <Paragraph
                style={{
                  fontSize: "16px",
                  color: "#4b5563",
                  marginBottom: "20px",
                }}
              >
                Le service sur{" "}
                <a href="https://www.comunik.fr" style={{ color: "#6366f1" }}>
                  https://www.comunik.fr
                </a>{" "}
                est fourni par :
              </Paragraph>

              <div style={{ marginBottom: "30px" }}>
                <Text strong style={{ fontSize: "16px", color: "#1f2937" }}>
                  Comunik SAS
                </Text>
                <br />
                <Text style={{ color: "#4b5563" }}>
                  15 Rue de la Innovation, 75001 Paris
                </Text>
                <br />
                <Text style={{ color: "#4b5563" }}>Postbox 12345 75001</Text>
                <br />
                <Text style={{ color: "#4b5563" }}><EMAIL></Text>
              </div>

              <Title
                level={4}
                style={{ color: "#1f2937", marginBottom: "15px" }}
              >
                Directeurs Généraux :
              </Title>
              <Paragraph style={{ color: "#4b5563", marginBottom: "30px" }}>
                Marie Dubois, Pierre Lefrançois
              </Paragraph>

              <Title
                level={4}
                style={{ color: "#1f2937", marginBottom: "15px" }}
              >
                Registre du Commerce :
              </Title>
              <Paragraph style={{ color: "#4b5563", marginBottom: "30px" }}>
                Tribunal de Commerce de Paris RCS 123456789
              </Paragraph>

              <Title
                level={4}
                style={{ color: "#1f2937", marginBottom: "15px" }}
              >
                **N° TVA (Numéro d'identification fiscale)**
              </Title> */}
            </div>

            {/* Sidebar */}
            {/* <div
              style={{
                width: "280px",
                backgroundColor: "white",
                borderRadius: "8px",
                padding: "30px 20px",
                boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
                height: "fit-content",
                position: "sticky",
                top: "100px",
              }}
            >
              {sidebarItems.map((item, index) => (
                <div
                  key={index}
                  style={{
                    padding: "12px 0",
                    borderBottom:
                      index < sidebarItems.length - 1
                        ? "1px solid #f1f5f9"
                        : "none",
                  }}
                >
                  <Text
                    style={{
                      color: index === 5 ? "#6366f1" : "#6b7280",
                      fontWeight: index === 5 ? "500" : "400",
                      cursor: "pointer",
                      fontSize: "14px",
                    }}
                  >
                    {item}
                  </Text>
                </div>
              ))}
            </div> */}
          </div>
        </Content>
      </Layout>
      <Modal
        // title="Basic Modal"
        closable={{ "aria-label": "Custom Close Button" }}
        open={typeOpenModal}
        onCancel={() => setTypeOpenModal("")}
        width={1080}
        footer={[]}
      >
        {typeOpenModal === "web" ? (
          <WebPolicy />
        ) : typeOpenModal === "mobile" ? (
          <MobilePolicy />
        ) : typeOpenModal === "ia" ? (
          <AIPolicy />
        ) : null}
      </Modal>
    </Layout>
  );
}
