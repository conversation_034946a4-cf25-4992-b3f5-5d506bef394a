import { lazy, Suspense, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { setOpenModalEmail } from "new-redux/actions/mail.actions";
import { SendingMailProgress } from "./utils";

const EmailComposerModal = lazy(
  () => import("./EmailComposerModal"),
  "EmailComposerModal"
);

const NewMessageEmail = ({ dataAccounts }) => {
  //
  const dispatch = useDispatch();
  //
  const sendingMail = useSelector((state) => state.voipBlackList.sendingMail);
  const openModalEmail = useSelector(
    (state) => state.mailReducer.openModalEmail
  );
  //
  const [openComposer, setOpenComposer] = useState(false);
  const [isMinimize, setIsMinimize] = useState(false);
  const [isReduce, setIsReduce] = useState(false);
  //
  useEffect(() => {
    if (!openModalEmail) return;
    setOpenComposer(true);
    setIsMinimize(false);
    dispatch(setOpenModalEmail(false));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [openModalEmail]);
  //
  return (
    <Suspense fallback={<></>}>
      {sendingMail?.open ? (
        <SendingMailProgress data={sendingMail?.data} />
      ) : openComposer ? (
        <EmailComposerModal
          open={openComposer}
          setOpen={setOpenComposer}
          isReduce={isReduce}
          setIsReduce={setIsReduce}
          isMinimize={isMinimize}
          setIsMinimize={setIsMinimize}
          dataAccounts={dataAccounts}
        />
      ) : null}
    </Suspense>
  );
};

export default NewMessageEmail;
