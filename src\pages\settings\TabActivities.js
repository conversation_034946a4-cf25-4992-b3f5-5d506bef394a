import React, { useEffect, useLayoutEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate } from "react-router-dom";
import TabsDetails from "../../components/Tabs";

const TabActivities = () => {
  const [t] = useTranslation("common");
  const [keyTab, setKeyTab] = useState("");
  const { pathname } = useLocation();
  const navigate = useNavigate();
  const items = [
    {
      label: (
        <div onClick={() => navigate("/settings/activity/types")}>
          {t("activities.activitiesTypes")}
        </div>
      ),
      key: "1",
    },

    {
      label: (
        <div onClick={() => navigate("/settings/activity/pipelines")}>
          {" "}
          {t("activities.activitiesPipelines")}
        </div>
      ),
      key: "2",
    },
  ];
  useLayoutEffect(() => {
    if (pathname == "/settings/activity/types") {
      setKeyTab("1");
    } else if (pathname == "/settings/activity/pipelines") {
      setKeyTab("2");
    } else {
      setKeyTab("1");
    }
  }, []);

  useEffect(() => {
    if (keyTab !== "") {
      if (keyTab == 1) {
        navigate("/settings/activity/types");
      } else if (keyTab == 2) {
        navigate("/settings/activity/pipelines");
      }
    }
  }, [keyTab, navigate]);

  return (
    <>
      {keyTab ? (
        <TabsDetails items={items} keyTab={keyTab} setKey={setKeyTab} />
      ) : (
        ""
      )}
    </>
  );
};

export default TabActivities;
