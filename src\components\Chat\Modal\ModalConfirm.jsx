import { Modal, Button } from "antd";

const ModalConfirm = ({
  title,
  content,
  loading,
  open,
  onCancel,
  onOk,
  cancelText,
  okText,
  dangerMode,
  disabled,
  customOKButton,
  footer = true,
  footerButton = null,
  closable = true,
  width,
  wrapClassName,
}) => {
  return (
    <Modal
      wrapClassName={wrapClassName}
      centered
      width={width}
      confirmLoading={loading}
      open={open}
      onCancel={onCancel}
      maskClosable={closable}
      closable={closable}
      closeIcon={undefined}
      keyboard={closable}
      title={title}
      className={!closable ? "syncData" : ""}
      footer={
        footer ? (
          footerButton ? (
            footerButton
          ) : (
            <div className="flex items-center justify-end space-x-2">
              <Button onClick={onCancel}>{cancelText}</Button>
              {/*  */}
              <span>
                {customOKButton || (
                  <Button
                    disabled={disabled}
                    key="submit"
                    danger={dangerMode}
                    type="primary"
                    onClick={onOk}
                    loading={loading}>
                    {okText}
                  </Button>
                )}
              </span>
            </div>
          )
        ) : null
      }>
      {content}
    </Modal>
  );
};

export default ModalConfirm;
