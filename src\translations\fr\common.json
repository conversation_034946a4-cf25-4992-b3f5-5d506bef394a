{"languageEnglish": "<PERSON><PERSON><PERSON>", "languageFrench": "Français", "404Subtitle": "<PERSON><PERSON><PERSON><PERSON>, la page que vous avez visitée n'existe pas.", "404": "Non Existant", "logout": " Déconnexion", "unauthorized": "Non autorisé", "unauthorizedSubtitle": "<PERSON><PERSON><PERSON><PERSON>, vous n'êtes pas autorisé à accéder à cette page.", "BackToHome": "Retour à l'accueil", "supportContact": "Si le problème persiste encore, ve<PERSON><PERSON><PERSON> contacter le support de  sphere.", "updateElement": "Modifier", "errorOccurred": "Une erreur s'est produites", "menu1": {"dashboard": "Accueil", "chat": "Discussions", "voip": "Voip", "callLog": "Journal d'appels", "company_directory": "Annuaire d'entreprise", "visio": "Visio", "notes": "Notes", "tasks": "Activités", "rmc": "Multi-chanal", "contacts": "Contacts", "settings": "Configuration", "notifications": "Notifications", "manage": "<PERSON><PERSON><PERSON> {{module}} ", "stats": "Statistiques", "mailing": "Email", "import": "Importer", "": "Tableau de bord", "profile": "Profil", "products": "Produits", "deals": "Offres", "tickets": "Tickets", "projects": "Projets", "family": "{{ family }}", "pre-meet": "Pre-meet", "booking": "Réservations", "leads": "<PERSON><PERSON><PERSON>", "companies": "Organisations", "task": "Activité", "email": "Email", "invoices": "Factures", "inbox": "Boite de r<PERSON>", "interactions": "Interactions", "appMobile": "Application mobile", "my_charts": "Mes statistiques", "scanMobileApp": "Scannez pour obtenir l'App", "drive": "Drive"}, "menu2": {"general": "Général", "fields": "<PERSON><PERSON>", "organisation": "Champs/Organisation", "contact": "Champs/Contact", "deal": "Champs/Offre", "leads": "Champs/Pistes", "booking": "Champs/Réservations", "localisation": "Localisation", "activities": "Activités", "rmcSettings": "Config RMC", "inboxsettings": "Config <PERSON><PERSON><PERSON>", "outboxsettings": "Config <PERSON>i", "contacts": "Contacts", "contacts-types": "Types de contact", "companies": "Général/{{company}}", "guestQueue": "Général/File d'attente guest", "companiesOnly": "Organisations", "colleagues": "<PERSON><PERSON>è<PERSON>", "groups": "Groupes", "prospects": "Prospects", "logs": "Téléphonie", "messaging": "Boîte vocale", "phoneBook": "Annuaire", "phone-book": "Annuaire", "statsRmc": "Statistiques RMC", "tags": "Étiquettes", "activity": "Activités", "importOrganismes": "Entreprises", "importContacts": "Contacts", "user": "Champs/Utilisateur", "user-view": "<PERSON><PERSON> Utilisateur", "manageaccount": "Manage Account", "emailaccounts": "<PERSON><PERSON><PERSON>", "inbox": "<PERSON><PERSON><PERSON>", "sent": "Boîte d'envoi", "drafts": "Brouillons", "starred": "Messages Suivis", "trash": "<PERSON><PERSON><PERSON><PERSON>", "pipeline": "Pipelines", "product": "Champs/Produit", "products": "Produits", "helpdesk": "Champs/Helpdesk", "helpdeskWithoutFields": "Helpdesk", "departments": "Général/Départements", "services": "Général/Services", "channels": "Général/Canaux", "folders": "Dossiers", "wiki": "Wiki", "ticket": "Tickets", "tickets": "Pipeline/Tickets", "severity": "Sévérités", "severities": "Ticket/Sévérités", "project": "Projets", "projects": "Pipeline/Projets", "general_info": "Général", "security": "Sécurité", "localization": "Localisation", "family": "Familles", "sales": "Transactions", "role": "Utilisateurs/Rôles", "team": "Utilisateurs/Équipes", "import": "Importer", "sla": "Ticket/SLA", "subjects": "Ticket/Sujet", "levels": "Ticket/Niveau", "wiki-config": "Wiki/Groupes", "wiki-docs": "Wiki/Documents", "usersWithoutFields": "Utilisateurs", "users": "Utilisateurs", "pipelineDeals": "Pipeline/Deals", "type": "Types", "unity": "Unités", "types": "Types", "typesContacts": "Types de contact", "typesCompanies": "Types d'entreprise ", "countries": "Général/Pays", "currencies": "Ventes/Devises", "checklist": "Liste de contrôle", "visio": "Visio", "triggers": "<PERSON><PERSON><PERSON>nch<PERSON><PERSON>", "allNotifications": "Toutes les notifications", "notificationsManagement": "Gestion des notifications du chat", "log-action": "<PERSON><PERSON><PERSON>", "tour": "Visite guidée", "unavailability": "Indisponibilité", "dashboard": "Accueil", "notification": "<PERSON><PERSON><PERSON> les notifications", "emailTemplates": "<PERSON><PERSON><PERSON><PERSON>", "discount": "Remises", "invoices": "Factures", "expandMenu": "<PERSON><PERSON><PERSON><PERSON> le menu", "hideMenu": "Masquer le menu", "integrations": "Intégrations", "transactions": "Transactions", "policies": "Politiques"}, "emailAccounts": {"accountType": "Type de compte", "account": "<PERSON><PERSON><PERSON><PERSON>", "primaryAccount": "Compte principal", "disableSync": "Synchronisation", "synchronous": "Synchrone", "asynchronous": "Asynchrone", "syncEmailsFrom": "Synchroniser les emails de", "createContactRecord": "<PERSON><PERSON>ez un enregistrement de piste s'il n'existe pas", "password": "Mot de passe", "userName": "Nom d'utilisateur", "createEmailAccount": "<PERSON><PERSON>er un compte de messagerie", "updateEmailAccount": "Modfier le compte de messagerie", "incomingMail": "Courrier entrant (IMAP)", "server": "Nom d'hôte", "shared": "Partagé", "personal": "Personnel", "errEmail": "L'entrée n'est pas un email valide !", "port": "Port", "encryption": "Encryptage", "outgoingMail": "Courrier sortant (SMTP)", "allownon-secure": "Autoriser les certificats non sécurisés", "oK": "OK", "cancel": "Annuler", "connectSharedAccount": "Connecter le compte partagé", "connectpersonnalEmailAccount": "Connecter le compte de messagerie personnel", "connectAccount": "<PERSON><PERSON>er un compte email", "1": "<PERSON><PERSON><PERSON>", "0": "Compte personnel", "1monthago": "Il y a 1 mois", "1weekago": "Il y a 1 semaine", "3monthago": "Il y a 3 mois", "6monthago": "Il y a 6 mois", "now": "<PERSON><PERSON><PERSON>'hui", "notSync": "Pas de synchronisation", "selectAccountType": "Sélectionner le type de compte", "defaultSignature": "La signature par défaut", "infoTest": "Avant d'envoyer les données de configuration, il faut cliquer sur 'Vérifier'", "unassignedMail": " Nombre de mail total non assigné", "myChargeMail": "Nombre de mail à mon charge", "ProcessedAndClosedMail": "Nombre de mail à mon charge et traité (traité et clôturé )", "progressMail": "Nombre de mail  à mon charge et en cours", "processedMail": "Nombre de mail à mon charge à traiter (nouveau)", "out-of-timeMails": "Nombre de mail Ho<PERSON> d<PERSON>s", "displayIndicators": "Config de l'affichage des indicateurs sur bandeau", "tooltipBanner": "Ces <PERSON> (indicateurs clés de performance) seront affiché sur une boite email partagé", "convertMailToTicket": "Convertir l'email en ticket", "mailToTicket": "Email en ticket", "successVerification": "La vérification a été complétée avec succès", "failedVerification": "La configuration a échoué. Veuillez vérifier vos paramètres", "check": "Vérifier", "defaultDepartment": "Département par défaut", "autoAffectAgent": "Affectation automatique des agents à la réception des emails", "defaultDepartmentInfo": "Ce département est sélectionné par défaut lorsqu'aucun sujet n'est pas choisi ou que le sujet ne contient pas de département", "automaticUnhandledEmails": "Affectation automatique quotidienne des emails non pris en charge", "useSameNamePassword": "Utilisez les mêmes identifiants de la messagerie entrante (IMAP) pour la messagerie sortante (SMTP)"}, "table": {"search": "Entrer un caractère pour la recherche", "search3": "Entrer au moins 3 caractères...", "startDate": "Date de début", "endDate": "Date de fin", "createContact": "<PERSON><PERSON><PERSON> un <PERSON>", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "primaryFieldTooltip": "Champ primaire", "header": {"type": "Type", "caller": "<PERSON><PERSON><PERSON>", "when": "Quand", "duration": "<PERSON><PERSON><PERSON>", "contacts": "Contacts", "name": "Nom", "selectRangeDate": "Sélectionnez la plage de dates", "description": "Description", "uploadImage": "Upload Image", "pricingAmount": "Montant du prix", "source": "Source", "rank": "<PERSON>ng", "title": "Titre", "hidden": "Caché", "uniqueValue": "Unique", "required": "Requis", "isrequired": "est requis !", "actions": "Actions", "department": "Département", "departments": "Départements", "percentage": "Progression", "region": "Continent", "currency": "<PERSON><PERSON>", "flag": "<PERSON><PERSON><PERSON>", "timezone": "<PERSON><PERSON> ho<PERSON>", "prefix": "Préfixe", "country": "Pays", "extra": "Configuration supplémentaire"}}, "localisation": {"timeFormat": "Format de l'heure", "dateFormat": "Format de la date", "weekStartsOn": "La semaine commence le", "defaultLanguage": "Langue par défaut", "defaultTimezone": "Fuseau horaire par défaut", "submit": "Envoyer", "sumbit2": "Valider", "reset": "Réinitialiser", "cancel": "Annuler", "monday": "<PERSON><PERSON>", "sunday": "<PERSON><PERSON><PERSON>", "saturday": "<PERSON><PERSON>", "french": "Français", "english": "<PERSON><PERSON><PERSON>", "dialCode": "Indicatif", "wednesday": "<PERSON><PERSON><PERSON><PERSON>", "january": "<PERSON><PERSON>", "noConfig": "Il n'y a pas de configuration de localisation disponible.", "titleChangeLangue": "Changer la langue", "contentChangeLangue": "La langue par défaut est actuellement réglée sur <strong>  {{lang}}.</strong> <PERSON><PERSON><PERSON><PERSON>-vous la changer, comme spécifié dans l'authentication?"}, "sales": {"addReasonToconclude": "Ajouter une raison pour conclure", "addReasonforLoss": "Ajouter la raison de la perte", "ReasonforLoss": "<PERSON><PERSON> de la perte", "ReasonToconclude": "<PERSON><PERSON> de conclure", "reason": "<PERSON>son", "defaultcurrency": "<PERSON><PERSON> <PERSON><PERSON>", "used": "Utilisé(e)", "unused": "Inutilisé(e)", "currencies": "Devi<PERSON>", "symbol": "Symbole", "successReason": "<PERSON><PERSON><PERSON><PERSON>", "failReason": "Perdu", "selectReasonTitle": "Raison de l'action sur cette opportunité", "failStatus": "Perdu", "successStatus": "<PERSON><PERSON><PERSON><PERSON>", "normalStatus": "En cours"}, "activities": {"createActivity": "Ajouter un type d'activité", "id": "Identifiant", "label": "Nom", "icons": "Iône", "search": "<PERSON><PERSON><PERSON>", "reset": "Réinitialiser", "filter": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "createActivityType": "Ajouter un type d'activité", "editActivityType": "Modifier le type d'activité", "iconTaked": "Cette icône a été prise", "name": "Nom", "icon": "Icône", "color": "<PERSON><PERSON><PERSON>", "oK": "OK", "cancel": "Annuler", "typefamilyproduct_id": "Type", "companies": "Entreprises", "activitiesTypes": "Types", "activitiesPipelines": "Pipelines"}, "tags": {"createTag": "Ajouter une étiquette", "id": "Identifiant", "label": "Label", "search": "<PERSON><PERSON><PERSON>", "reset": "Réinitialiser", "filter": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "editTag": "Modifier l'étiquette", "name": "Nom", "icon": "Icône", "color": "<PERSON><PERSON><PERSON>", "withAtypeOfActivity": "avec type d'activité", "withOutTypeOfActivity": "Sans type d'activité", "typeTask": "Type d'activité déclenchée", "ListOfCategories": "Catégories", "oK": "OK", "cancel": "Annuler", "selectcolor": "<PERSON><PERSON><PERSON><PERSON>ner une couleur", "selecttypeactivity": "Sélectionner le type d'activité", "selectcategories": "Sélectionner les modules", "selectUsers": "Sélectionner les utilisateurs", "selecticon": "Sélectionner une icône", "typetask_id": "Type d'activité", "Families": "Familles", "selectFamily": "<PERSON>ner famille"}, "channels": {"label": "Nom", "icon": "Icône"}, "page": {"underConstruction": "Cette page est en cours de construction."}, "toasts": {"fieldCreated": "Champs a été créé avec succès", "groupCreated": "Groupe a été créé avec succès", "groupUpdated": "Groupe Champs a été mis à jour avec succès", "fieldUpdated": "Champs a été mis à jour avec succès", "fieldDeleted": "Champs a été supprimé avec succès", "usedField": "Ce champ ne peut pas être supprimé ! Il est déjà utilisé !", "taskDeleted": "Activity a été supprimé avec succès", "groupDeleted": "Groupe a été supprimé avec succès", "optionValueDeleted": "La valeur de l'option a été supprimée avec succès", "somethingWrong": "<PERSON><PERSON><PERSON> chose s'est mal passé, veuillez réessayer", "deleted": " a été supprimée avec succès", "taskUpdated": "Activity a été mis à jour avec succès", "created": " a été créé avec succès", "updated": "mise à jour réussie", "edit": " a été modifié avec succès", "error": "<PERSON><PERSON><PERSON>", "rankChanged": "<PERSON><PERSON> modifi<PERSON>", "stageChanged": "Statut modifié", "rankFailed": "Échec du classement", "fieldParamSuccess": "Paramètre a été mis à jour avec succès", "succès": "Su<PERSON>ès", "companyDeleted": "Entreprise supprimée avec succès", "companiesDeleted": "Entreprises supprimées avec succès", "contactCreated": "Contact créé avec succès", "contactUpdated": "Contact mis à jour avec succès", "contactDeleted": "Contact supprimé avec succès", "contactsDeleted": "Contacts supprimés avec succès", "formFailed": "La validation du formulaire a échoué, vérifiez tous les champs", "recordUpdated": "Enregistrement mis à jour avec succès", "ownerUpdated": "{{label}} a été affecté à un nouveau propriétaire", "networkError": "<PERSON><PERSON><PERSON>", "networkErrorDescription": "Vous êtes actuellement hors ligne.", "networkErrorSendMessage": "Vous ne pouvez pas envoyer de message lorsque vous êtes hors ligne.", "networkStatusGood": "Le réseau est bon.", "networkStatusModerate": "Le réseau est modéré.", "networkStatusPoor": "Le réseau est mauvais.", "networkStatusPoorDescription": "Votre réseau est mauvais, veuillez vérifier votre connexion. Merci d'actualiser la page en cliquant sur ce ", "networkStatusOffline": "Vous êtes actuellement hors ligne.", "re-connection": " Reconnexion en cours...", "mercureConnectionDescription": "Essayez de vous reconnecter, veuillez patienter... ", "errorFetchApi": "Une erreur s'est produite. Veuillez réessayer", "errorRoomNotFound": "La salle de visio n'a pas été trouvée", "associationDone": "L'association est terminée", "associationFailed": "Échec de l'association", "dissociationDone": "La dissociation est terminée", "dissociationFailed": "Échec de la dissociation", "deleteFailed": "La suppression a échoué", "contactAlreadyAssociated": "Ce contact est déjà associé", "actionFailed": "Échec de l'action", "customError": "{{error}}", "callForwardingActive": "Renvoi d'appel actif", "module used you cannot deleted": "Module utilisé vous ne pouvez pas supprimer !", "primaryCurrency": " est devenu la devise par défaut", "used": " est utilisé(e)", "notUsed": " n'est plus utilisé(e)", "primaryCompany": " est devenue la principale entreprise", "primaryMail": " est devenu l'email principal", "sync": " est devenu synchronisé", "notSync": " n'est plus synchronisé", "connectionSuccess": "Connexion établie avec succès", "existingFieldLabelError": "Le libellé du champ existe déjà", "multiselectInfo": "Multiselect field allows to select multiple options at once", "duplicatedDataError": "Données dupliquées trouvées!!", "duplicatedGrpNameError": "Nom du groupe déjà existant", "reconnect": "Session d'authentification expirée, veuil<PERSON><PERSON> vous reconnecter", "addTaskNotification": "<strong>{{user}}</strong> vous a ajouté comme <strong>{{role}}</strong> dans une nouvelle activité <strong>{{label}}</strong>", "updateTaskNotification": "<strong>{{user}}</strong> a mis à jour l'activité <strong>{{label}}</strong>", "updateRoleTaskNotification": "<strong>{{user}}</strong> a mis à jour votre rôle en tant que <strong>{{role}}</strong> dans l'activité <strong>{{label}}</strong>", "deleteTaskNotification": "<strong>{{user}}</strong> a supprimé une activité", "updateTaskpriorityNotification": "<strong>{{user}}</strong> a modifié la priorité de l'activité <strong>{{label}}</strong>", "taskReminderNotification": "<strong>{{task<PERSON><PERSON><PERSON>}}</strong> rappel d'activité", "updateTaskDatesNotification": "<strong>{{user}}</strong> a modifié l'horaire de l'activité <strong>{{label}}</strong>", "updateTaskStageNotification": "<strong>{{user}}</strong> a changé l'étape de l'activité <strong>{{label}}</strong>", "updateTaskNoteNotification": "<strong>{{user}}</strong> a modifié la <strong>{{noteDescription}}</strong> dans l'activité <strong>{{label}}</strong>", "updateTaskListNotification": "<strong>{{user}}</strong> a mis à jour la liste <strong>{{labelList}}</strong> de l'activité <strong>{{label}}</strong>", "meUpdateTaskListNotification": "<strong>Vous</strong> avez mis à jour la liste <strong>{{labelList}}</strong> de l'activité <strong>{{label}}</strong>", "updateNameTaskListNotification": "<strong>{{user}}</strong> a renommé une liste de <strong>{{oldName}} à {{newName}}</strong> pour l'activité <strong>{{label}}</strong>", "meUpdateNameTaskListNotification": "<strong>Vous</strong> avez renommé une liste de <strong>{{oldName}} à {{newName}}</strong> pour l'activité <strong>{{label}}</strong>", "deleteTaskListNotification": "<strong>{{user}}</strong> a supprimé la liste <strong>{{labelList}}</strong> de l'activité <strong>{{label}}</strong>", "meDeleteTaskListNotification": "<strong>Vous</strong> avez supprimé l'élément la liste <strong>{{labelList}}</strong> de l'activité <strong>{{label}}</strong>", "deleteItemInTaskListNotification": "<strong>{{user}}</strong> a supprimé l'élément <strong>{{labelItem}}</strong> de la liste <strong>{{labelList}}</strong> de l'activité <strong>{{label}}</strong>", "meDeleteItemInTaskListNotification": "<strong>Vous</strong> avez supprimé l'élément <strong>{{labelItem}}</strong> de la liste <strong>{{labelList}}</strong> de l'activité <strong>{{label}}</strong>", "createTaskListNotification": "<strong>{{user}}</strong> a créé une nouvelle liste <strong>{{labelList}}</strong> dans l'activité <strong>{{label}}</strong>", "meCreateTaskListNotification": "<strong>Vous</strong> avez créé une nouvelle liste <strong>{{labelList}}</strong> dans l'activité <strong>{{label}}</strong>", "updateRankaskListNotification": "<strong>{{user}}</strong> a mis à jour les rangs des éléments de la liste <strong>{{labelList}}</strong> dans l'activité<strong>{{label}}</strong>", "meUpdateRankTaskListNotification": "<strong>Vous</strong> avez les rangs des éléments de la liste <strong>{{labelList}}</strong> dans l'activité <strong>{{label}}</strong>", "statusChanged": "Statut changé avec succès", "error_status": "Vous ne pouvez pas appeler ce numéro, veuil<PERSON><PERSON> vérifier le numéro et réessayer", "messageNotSent": "le message n'est pas envoyé", "indisponibilite": "Indisponibilité", "discounts": "Remises", "taxes": "Tax", "cannotDeleteStage": "Impossible de supprimer une étape déjà utilisée!", "searchInfo": "Rechercher par toutes les colonnes", "groupe-wikis": "Groupe wiki", "updateActivityLabel": "<strong>{{user}}</strong> a modifié le libéllé à <strong>{{newLabel}}</strong>", "updateActivityType": "<strong>{{user}}</strong> a modifié le type de l'activité <strong>{{label}}</strong> de <strong>{{oldType}}</strong> à <strong>{{newType}}</strong> ", "updateActivityElement": "<strong>{{user}}</strong> a associé l'élément <strong>{{element}}</strong> de module <strong>{{module}}</strong> à l'activité <strong>{{label}}</strong>", "newEmail": "Vous avez reçu un nouvel e-mail de <strong>{{from}}</strong> dans la boîte <strong>{{to}}</strong>", "updateReminder": "<strong>{{user}}</strong> a modifié le rappel de l'activité <strong>{{label}}</strong>", "addFilesInActivity": "<strong>{{user}}</strong> a ajouté des fichiers dans l'activité <strong>{{label}}</strong>", "removeFilesInActivity": "<strong>{{user}}</strong> a supprimé un fichier dans l'activité <strong>{{label}}</strong>", "driveItemShared": "<strong>{{user}}</strong> a partagé un {{itemType}} <strong>{{itemName}}</strong> avec vous", "driveItemUnshared": "<strong>{{user}}</strong> a retiré le partage d'un {{itemType}} <strong>{{itemName}}</strong> avec vous", "driveItemTrashed": "<strong>{{user}}</strong> a déplacé {{itemType}} <strong>{{itemName}}</strong> dans la corbeille", "driveItemMoved": "<strong>{{user}}</strong> a déplacé {{itemType}} <strong>{{itemName}}</strong> vers un autre emplacement", "driveItemRenamed": "<strong>{{user}}</strong> a renommé {{itemType}} <strong>{{itemName}}</strong>", "reminderTooltip": "<p>Si vous laissez ce champ vide, vous ne serez pas rappelé avant le début de cette activité.</p><strong>N.B. : Ce champ n'accepte que les chiffres</strong>", "currentUserPronoun": "Vous", "external-tokens": "La jeton d'accés", "labelExist": "Le {{label}} existe déjà", "queue-guest-display": "La file d'attente"}, "fields_management": {"field_drawer_btn": "<PERSON><PERSON><PERSON> un champ", "create_field_drawer-title": "<PERSON><PERSON><PERSON> un nouveau champ", "update_field_drawer-title": "<PERSON>tre à jour le champ [{{fieldLabel}}]", "field_type_label": "Veuillez sélectionner un type de champ", "field_label": "Nom interne", "field_alias": "Libellé", "hidden": "Caché", "uniqueValue": "Valeur unique", "required": "Champ obligatoire", "opt_table_btn": "Ajouter une nouvelle option", "opt_value": "Nom interne", "opt_label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "optsTableConfirmPopup": "Valider la suppression?", "popupConfirmYesBtn": "O<PERSON>", "popupConfirmNoBtn": "Non", "optsTableTooltip": "Vous devez ajouter au moins une option !", "drawerOkBtn": "Enregistrer", "drawerCloseBtn": "<PERSON><PERSON><PERSON>", "fieldTypeError": "Veuillez sélectionner un type de champ.", "fieldLabelError": "Le libellé du champ est requis !", "noOptsError": "V<PERSON> de<PERSON> remplir toutes les entrées d'options !", "optValueError": "La valeur de l'option est requise !", "optLabelError": "La valeur de l'étiquette est requise !", "moduleFieldRedirectBtn": "Aller à {{moduleName}}", "fieldsGroupsTitle": "Champs de données", "fieldsTitle": "<PERSON><PERSON>", "groupNameCol": "Nom du groupe", "cancelAlertMsg": "Etes vous sûr de vouloir annuler ?", "cancelText": "Annuler", "okText": "O<PERSON>", "associatedGroup": "Groupe associé", "selectModulePlaceholder": "Sélectionner un module", "moduleType": "Type d'affichage", "selectGroupErrorMsg": "Veuillez associer un groupe à ce champ.", "selectModuleErrorMsg": "Le module est requis !", "searchBtn": "<PERSON><PERSON><PERSON>", "resetBtn": "Réinitialiser", "SearchPlaceholder": "<PERSON><PERSON><PERSON>", "selectedFieldType": "Type de champ sélectionné", "aliasTooltip": "C<PERSON>r sur l'alias pour changer", "redirectToCountries": "Aller aux paramètres des pays", "redirectBtnHelpLine": "Cliquer ici pour consulter les paramètres '{{dept}}'.", "country": "Pays", "optTooltip": "Cliquer pour éditer", "placeholderField": "<PERSON>e d'aide", "fieldDescription": "Description du champ", "multiselectInfo": "Le champ multiselect permet de sélectionner plusieurs options à la fois.", "display": "Affichage", "dateFormatLabel": "Sélectionner le format de la date", "timeFormatLabel": "Sélectionner le format d'heure", "dateFormatPlaceholder": "Sélectionner le format de la date", "dateFormatInfo": "Vous pouvez désormais sélectionner le format de la date", "displayModelInfo": "Sélectionnez les vues où ce champ sera affiché. La limite par type de vue est indiquée à droite.", "exceedDisplayError": "Vous avez dépassé la limite autorisée pour le(s) type(s) de vue <strong>{{views}}</strong>.", "groupStageLabel": "Étape", "groupStagePlaceholder": "Selectionner pipeline/étape", "groupNamePlaceholder": "Nom du Groupe", "addPipeline": "Ajouter pipeline/étape", "readOnly": "Champ en lecture seule", "multiple": "Sélection multiple", "multipleInfo": "Cette option permet la sélection multiple de 'country'", "changeParamsError": "Vous ne pouvez pas modifier les propriétés comme cachées et requises simultanément.", "moduleName": "<PERSON><PERSON><PERSON>", "displayedType": "Affichage", "addField": "<PERSON><PERSON>", "addGroup": "Groupe", "searchFieldPlaceholder": "Rechercher un champ", "searchGrpPlaceholder": "Rechercher un groupe ou un champ", "fieldDesc": "Description du champ", "externalUserParam": "Afficher ce champ aux utilisateurs externes (invités)", "showToGuest": "Afficher aux invités", "externalUserParamInfo": "En cochant cette option, la valeur de ce champ sera affichée à vos utilisateurs externes (invités).", "showInGuestForm": "Afficher dans le formulaire invité", "optionsTable": "Tableau des options", "updateAliasSuccess": "L'alias a été mis à jour avec succès", "deleteGrpError": "Ce groupe ne peut pas être supprimé car ses champs sont utilisés.", "deleteSystemViewError": "Impossible de modifier les vues d'affichage système.", "hiddenAndRequiredError": "Impossible de stocker des propriétés comme cachées et requises simultanément.", "reorder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "update_Group": "Modifier le groupe", "delete_group": "Supprimer le groupe", "contact": "Contact", "leads": "<PERSON><PERSON>", "lead": "<PERSON><PERSON>", "deal": "Offre", "ticket": "Ticket", "helpdesk": "Helpdesk", "project": "Projet", "product": "Produit", "booking": "Réservation", "user": "Utilisa<PERSON>ur", "transaction": "Transaction", "invoices": "Factures", "organisation": "Organisation", "expandAll": "<PERSON><PERSON> d<PERSON>vel<PERSON>per", "collapseAll": "<PERSON><PERSON> r<PERSON>", "fieldsInfo": "Améliorez la qualité de vos données en choisissant l'emplacement où apparaissent certains champs et en en signalant certains comme importants. Ceci comprend les champs personnalisés, qui vous aident à ajuster au mieux vos données aux besoins de votre activité.", "view": " Vue formulaire des champs", "requiredFields": " Champs requis dans ce groupe", "uniqueFields": " Champs uniques dans ce groupe", "emptyFields": "Pas encore de champs dans ce groupe", "emptyFieldText": "Vous pouvez créer un champ dans ce groupe à partir d'une de nos suggestions des champs les plus utilisés."}, "mailing": {"newMsg": "Nouveau message", "accept": "Accepter", "confirm": "valider", "DeleteMail": "Êtes-vous sûr de vouloir supprimer l'email", "DeleteMails": "Êtes-vous sûr de vouloir supprimer", "ArchiveMail": "Êtes-vous sûr de vouloir archiver l'email", "UnarchiveMail": "Êtes-vous sûr de vouloir unarchiver l'email", "TransferMail": "Êtes-vous sûr de vouloir déplacer l'email", "TransferPopConfirm": "d<PERSON><PERSON>r l'email", "Delete": "<PERSON><PERSON><PERSON><PERSON>", "DeleteButton": "<PERSON><PERSON><PERSON><PERSON>", "ClearTrashButton": "Supprimer définitivement", "Syncronized": "Synchronisé", "NotSyncronized": "Non Synchronisé", "Primary": "Primaire", "Secondary": "Secondaire", "Shared": "Partagé", "Personal": "Personnel", "ConfigureMail": "Veuillez configurer votre email", "DeleteDefinitive": "Les messages restés dans la corbeille pendant plus de 30 jours seront automatiquement supprimés.", "newMail": "Envoyer un e-mail", "inbox": "Boite de r<PERSON>", "sent": "Boîte d'envoi", "draft": "Brouillons", "starred": "Messages Suivis", "trash": "<PERSON><PERSON><PERSON><PERSON>", "spam": "Spam", "archive": "Archiver", "unarchive": "<PERSON><PERSON><PERSON>", "archiveEmail": "Archiver L'email", "unarchiveMail": "Unarchiver L'email", "search": "<PERSON><PERSON><PERSON>", "Reply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ReplyAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> tous", "Forward": "<PERSON><PERSON><PERSON><PERSON>", "Actions": "Actions", "Contact": "Convertir en contact", "EditContact": "Modifier contact", "Deal": "<PERSON><PERSON><PERSON> une offre", "Activity": "Créer une activité", "Organisation": "Créer une organisation", "Ticket": "<PERSON><PERSON><PERSON> un ticket", "ErrorMsg": "Veuillez saisir le message", "ErrorObject": "Veuillez saisir l'objet ", "ErrorMail": "Veuillez sélectionner au moins un email", "ValidMail": "Veuillez saisir un e-mail valide", "empty": "vide", "Close": "<PERSON><PERSON><PERSON>", "successTest": "Votre adresse email est valide", "noData": "pas donnée", "invalidConfig": "La configuration de l'e-mail {{email}} est invalide.", "Historique": "Historique", "Subject": "Sujet", "AssigntransferredError": "Vous ne pouvez pas effectuer d'actions sur cette mission car elle a été transférée vers un autre compte", "AssignDispatcherError": "Vous ne pouvez pas effectuer cette action car vous n'avez pas le rôle de dispatcheur", "AssignContactAdmin": "Please contact your administrator for dispatcher configuration", "qualify": "Qualifier", "Affect": "Affecter", "Assign": "Assigner", "AssignTo": "Assigner <PERSON>", "identify": "Identifier", "identifyEmail": "Identifier l'email à un module", "filterTitle": "Filtre avancé", "yes": "OUI", "no": "NO", "filterReset": "Réinitialiser le filtre", "Default": "<PERSON><PERSON> <PERSON><PERSON>", "ThanksToAssign": "<PERSON><PERSON><PERSON> d'assigner à", "loading": "chargement", "notAssigned": "NON ASSIGNÉ", "InProgress": "EN COURS", "PROCESS": "À TRAITER", "Assigned": "ASSIGNÉ", "outOfTime": "HORS DÉLAI", "successUpdate": "La qualification a été modifiée avec succès", "successQualif": "L'email est qualifié avec succès", "thanksAssign": "<PERSON><PERSON><PERSON> d'assigner à", "processEmail": "Le délais de traitement de mail", "expiredEmail": "<PERSON><PERSON><PERSON> de traitement expiré", "depassed": "est dépassé", "markRead": "Marquer comme lu", "markUnread": "Marquer comme non lu", "read": "marqué comme lu", "notRead": "Marqué comme non lu", "markSpam": "Marquer comme spam", "markfavorite": "marquer comme favori", "marknonfavorite": "marquer comme non favori", "markImportant": "marquer comme important", "marknotImportant": "marquer comme non important", "emailImportant": "<PERSON><PERSON><PERSON> email a été marqué comme important", "emailnotImportant": "V<PERSON>re email a été marqué comme non important", "emailRead": "Votre email a été marqué comme Lu", "emailnotRead": "Votre email a été marqué comme non Lu", "backReception": "Retour à la boite de reception", "emailArchived": "Votre email a été archivé", "searchemailPrivate": "Rechercher email privé", "searchemailShared": "Rechercher email partagé", "affectedSuccess": "affecté avec success", "identifiedSuccess": "Votre email a été identifié avec succés", "emailMoved": "l'email est déplacé", "identifDeletedSuccess": "L'identificiation a été supprimé avec succés", "movedSuccess": "l'email a été déplacé avec succés", "Emaildeleted": "Votre email a été supprimé", "Emailsdeleted": "emails ont été supprimés", "EmailAssigned": "Votre email a été assigné à", "modifiedSuccess": "modifié avec success", "signed": "Signé par", "detailed": "<PERSON><PERSON><PERSON><PERSON>r la vue détaillée", "itemSelected": "éléments sélectionnés", "yourEmail": "Votre email a été", "ShowMore": "Afficher plus", "Inbox": {"other": "Autres boîtes de réception", "sender": "Nom de l'expéditeur", "To": " À", "subject": "Sujet", "message": "Message", "date": "Date"}, "NewMsg": {"Cancel": "Annuler", "send": "Envoyer", "received": "<PERSON><PERSON><PERSON>", "sent": "<PERSON><PERSON><PERSON>", "from": "De", "To": "À", "object": "Objet", "message": "Message", "newMsg": "Nouveau message", "placeholderMail": "Rechercher ou entrer une adresse e-mail", "file": "Jo<PERSON>re des fichiers", "photo": "Insérer une photo", "emoji": "Insérer un emoji"}, "optLabelError": "La valeur de l'étiquette est requise !", "primaryFieldTooltip": "Ce champs est primaire", "titleconfigureEmail": "<PERSON><PERSON><PERSON> bo<PERSON> configurée", "configureEmailBtn": "Configurer une boîte mail", "configureEmail": "Il semble que vous ne soyez pas membre d'une boîte partagée et que vous n'ayez pas configuré d'e-mails sur votre compte.", "selectUsers": "Sélectionner des utilisateurs", "dispatcher": "<PERSON><PERSON><PERSON><PERSON>", "emailTransfer": "cet email a été déplacé par", "from": "de", "to": "à", "new": "Nouveau", "inProgress": "En cours", "processed": "Traité", "closed": "<PERSON><PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON><PERSON>", "searchPrivate": "Rechercher email privé", "searchShared": "Rechercher email partagé", "moveTo": "<PERSON><PERSON><PERSON><PERSON> à", "move": "<PERSON><PERSON><PERSON><PERSON>", "Tooltip": {"important": "<PERSON>quer comme important", "starred": "Marquer comme favori", "supprimer": "<PERSON><PERSON><PERSON><PERSON>", "Log": "Historique", "AssignTransferedNoIdentif": "Vous ne pouvez pas identifier l'email car il est assigné et déplacé", "AssignTransferedNoIdentifNoAffect": "Vous ne pouvez pas affecter l'email car il est assigné et déplacé", "AssignTransferedNoIdentifNoQualif": "Vous ne pouvez pas qualifier l'email car il est assigné et déplacé", "AssignTransferedNoIdentifNoTransfer": "Vous ne pouvez pas déplacer l'email car il est assigné et déplacé", "AssignNoIdentif": "Vous ne pouvez pas identifier l'email car il est assigné", "AssignNoAffect": "Vous ne pouvez pas affecter l'email car il est assigné", "AssignNoQualif": "Vous ne pouvez pas qualifier l'email car il est assigné", "AssignNoTransfer": "Vous ne pouvez pas déplacer l'email car il est assigné", "TransferedNoIdentif": "Vous ne pouvez pas identifier l'email car il est déplacé", "TransferedNoAffect": "Vous ne pouvez pas affecter l'email car il est déplacé", "TransferedQualif": "Vous ne pouvez pas qualifier l'email car il est déplacé", "TransferedNoAssign": "Cet email a été déplacé et ne peut pas être assigné", "TransferedAndAssigned": "L'email est assigné", "Affecter": "Affecter cet email à un élément de module", "Assigner": "Assigner cet email", "Move": "Déplacer l'email à une autre boite", "cannotAssignNotFromDepartment": "L'assignation n'est pas autorisée : vous n'appartenez pas au département qui gère cette boîte partagée"}, "processingTime": "Temps de traitement", "all": "Tous", "MyMode": "Mode moi", "RelativeTo": "Relative à", "filter": "<PERSON><PERSON><PERSON>", "identifier": "Identifier", "convertTo": "Convertir en", "Qualifier": "Qualifier", "affecter": "Affecter", "qualifierà": "Qualifier à", "affecterà": "Affecter à", "of": "sur", "sender": "Expéditeur", "user": "utilisateur", "messageSucces": "Votre message a été envoyé avec succés", "openOtherMessages": "ouvrir les autres messages", "addTemplate": "AJOUTER UN MODÈLE", "actionTemplate": "Cette action permet d'ajouter le contenu du modèle sélectionné.", "confirmAction": " Voulez vous confirmer cette action?", "emailTemplate": "mod<PERSON>le d'email", "Attachments": "Pièces jointes", "uploadFile": "Télécharger le fichier", "changeSignature": "Votre signature sera incluse dans le corps du message. Si vous souhaitez la modifier,", "clickHere": "Cliquez ici", "selectReceiver": "Sélectionnez l'email du destinataire pour obtenir le modèle.", "noSubject": "Pas d'objet", "Date": "Date", "Lieu": "<PERSON><PERSON>", "AssignedTo": " <PERSON><PERSON><PERSON>", "Affected": "Affect<PERSON>", "chronoYes": "chrono :<PERSON><PERSON>", "synchroStart": "Syncronisation en cours", "synchroFailed": "la synchronisation a echoué", "synchroSuccess": "la synchronisation a été effectuée avec succès", "EmptyTrash": "Vider la corbeille", "ConfirmClearTrashTitle": "Confirmer la suppression des messages", "ConfirmClearTrashMessage": "Êtes-vous sûr de vouloir supprimer définitivement tous les messages de la corbeille ? Cette action est irréversible et les messages ne pourront pas être récupérés", "successClearTrash": "Tous les messages ont été supprimés", "forever": "définitivement", "assigned": "<PERSON><PERSON><PERSON>", "notAllowedExt": "Le type de fichier <strong>.{{ext}}</strong> n'est pas pris en charge.", "includeAttachments": "Inclure les pièces jointes d'origine", "labels": "Libellés", "notSpam": "Non-spam", "makeItNotSpam": "Faite-le non-spam", "labelAs": "Ajouter un libellé", "removeLabelFromMail": "Retirer ce Libellé de ce mail ?", "movedBy": "Déplacé par", "assignToMe": "M'assigner", "takeOver": "<PERSON><PERSON><PERSON> le re<PERSON>", "noAccessThisBox": "Vous n'avez pas accès à cette boîte mail.", "moveToast": "<strong>{{owner}}</strong> a déplacé un email de <strong>{{from}}</strong> vers <strong>{{to}}</strong> [\"{{subject}}\"]", "emailAttachment": "Contient <strong>{{nbr}}</strong> pi<PERSON>ce(s) jointe(s)", "containsAttch": "Contient pièce(s) jointe(s)", "expireProcessing": "Le délai de traitement de l'e-mail [{{subject}}] de <strong>{{address}}</strong> est expiré. Pris en charge par <strong>{{user}}</strong> (Début: {{startDate}}).", "assignEmail": "L'e-mail [{{subject}}] de <strong>{{sender}}</strong> dans la boîte <strong>{{account}}</strong> a été assigné à <strong>{{owner}}</strong> par <strong>{{user}}</strong>.", "selfAssignEmail": "L'e-mail [{{subject}}] de <strong>{{sender}}</strong> dans la boîte <strong>{{account}}</strong>  a été auto-assigné par <strong>{{user}}</strong>.", "deleteAssignEmail": "L'e-mail [{{subject}}] de <strong>{{sender}}</strong> dans la boîte <strong>{{account}}</strong> a été désassigné de <strong>{{owner}}</strong> par <strong>{{user}}</strong>.", "selfDeleteAssignEmail": "L'e-mail [{{subject}}] de <strong>{{sender}}</strong> dans la boîte <strong>{{account}}</strong> a été auto-désassigné par <strong>{{user}}</strong>.", "affectEmail": "L'e-mail [{{subject}}] de <strong>{{sender}}</strong> dans la boîte <strong>{{account}}</strong> a été affecté au {{family}}: <strong>{{element}}</strong> par <strong>{{user}}</strong>.", "autoAffectEmail": "L'e-mail [{{subject}}] de <strong>{{sender}}</strong> dans la boîte <strong>{{account}}</strong> a été affecté au {{family}}: <strong>{{element}}</strong> automatiquement.", "updateStateEmail": "L'e-mail [{{subject}}] de <strong>{{sender}}</strong> a vu son état passer de <strong>{{lastState}}</strong> à <strong>{{newState}}</strong> par <strong>{{user}}</strong>.", "qualificationEmail": "L'e-mail [{{subject}}] de <strong>{{sender}}</strong> a été qualifié avec le(s) tag(s) '<strong>{{tags}}</strong>' par <strong>{{user}}</strong>.", "deleteQualificationEmail": "La qualification de l'e-mail [{{subject}}] de <strong>{{sender}}</strong> a été supprimée par <strong>{{user}}</strong>.", "identificationEmail": "L'e-mail [{{subject}}] de <strong>{{sender}}</strong> a été identifié comme {{family}}: <strong>{{element}}</strong> par <strong>{{user}}</strong>.", "deleteIdentificationEmail": "L'identification de l'e-mail [{{subject}}] de <strong>{{sender}}</strong> en tant que {{family}}: <strong>{{element}}</strong> a été supprimée par <strong>{{user}}</strong>.", "identifiedAs": "Identifié(e) en tant que", "affectTo": "Affecté(e) à", "clickToEdit": "Cliquez pour éditer", "start": "D<PERSON>but", "end": "Fin", "outOfDeadline": "<PERSON><PERSON>", "bouncedMail": "Email non délivré", "affectThisMail": "Affecter cet email", "updateAffectation": "Mis à jour de l'affectation", "updateIdentification": "Mis à jour de l'identification", "noMailings": "<PERSON><PERSON><PERSON> mod<PERSON>", "manageMailings": "<PERSON><PERSON><PERSON> les modèles", "selectMailing": "Sélectionnez un modèle", "insertMailing": "<PERSON><PERSON><PERSON><PERSON> le modèle", "append": "<PERSON><PERSON><PERSON> le modèle", "clearAndInsert": "Effacer et insérer le modèle", "insertPrompt": "Comment souhaitez-vous insérer ce modèle ?", "small": "<PERSON>", "normal": "Normal", "large": "Grand", "huge": "Très grand", "sendingMail": "Envoi de courrier...", "updateQualification": "Mis à jour de la qualification", "noEmailConfigured": {"title": "Aucun compte e-mail configuré", "subTitle": "On dirait que vous n’avez pas encore configuré de boîte mail.\nPour commencer à envoyer et recevoir des e-mails, ajoutez un compte.", "cta": "Ajouter un compte e-mail"}, "emailConfig": {"errorTitle": "Erreur de configuration de l’e-mail", "errorDesc": "Impossible de se connecter à votre compte e-mail. Veuillez vérifier vos paramètres et réessayer."}, "dateRange": "Plage de dates", "senderAddress": "<PERSON><PERSON><PERSON> de l'expéditeur", "subjectEmail": "Object de l'email", "showInOriginalBox": "Afficher aussi dans la boîte d’origine", "atLeastAddressOrSubject": "Veuillez sélectionner au moins une adresse e-mail ou un objet!", "status": "Statut", "chatMail": "Discuter ce email", "roomAlreadyExist": "Le chat room existe déjà"}, "tasks": {"tablePagination": "{{range}} de {{totalItems}} activités", "addQuickTask": "Nouvelle activité", "deleteBtn": "Supprimer ({{s}} élément{{plural}})", "tableLabel": "LIBELLÉ", "tableType": "TYPE", "tablePriority": "PRIORITÉ", "startDate": "DATE DE DÉBUT", "tableEndDate": "DATE DE FIN", "tableOwner": "PROPRIÉTAIRE", "tableGuests": "PARTICIPANTS", "titlePlaceholder": "Titre", "typePlaceholder": "Type d'activité", "priorityPlaceholder": "Priorité", "startDatePlaceholder": "Date de début", "endDatePlaceholder": "Date de fin", "startTimePlaceholder": "<PERSON><PERSON> d<PERSON>", "endTimePlaceholder": "Heure de fin", "ownerPlaceholder": "Choisir un propriétaire", "guestsPlaceholder": "Choisir des participants", "createTaskTitle": "<PERSON><PERSON>er une nouvelle activité", "updateTaskTitle": "Mise à jour de l'activité [{{label}}]", "updateVisioTitle": "Mise à jour de la visio [{{label}}]", "type": "Type d'activité", "title": "Titre", "priority": "Priorité", "dateTime": "Choisir la date et l'heure", "reminder": "<PERSON><PERSON>", "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "guests": "Participant{{s}}", "followers": "<PERSON><PERSON><PERSON>{{s}}", "addGuestBtn": "Ajouter des participants", "addFollowerBtn": "Ajouter des suiveurs", "descriptionHelp": "La description est visible pour tous les participants.", "noteHelp": "Les notes sont privées et visibles uniquement pour les collègues.", "upload": "Piéces jointes", "uploadRulesInfo": "Vous pouvez téléchargez jusqu'à 6 fichiers, pour une taille totale maximale de 15 Mo", "uploadBtn": "Cliquez ou faites glisser le fichier dans cette zone pour télécharger", "weeks": "Se<PERSON>ines", "hours": "<PERSON><PERSON>", "days": "Jours", "reminderSelect": "Choi<PERSON>", "typeError": "Le type d'activité est obligatoire !", "titleError": "Le champ titre est obligatoire !", "priorityError": "Veuillez choisir le statut prioritaire !", "ownerError": "Veuillez choisir un propriétaire !", "tasksSidemenuTooltip": "Activités", "toastsTask": "Activité", "updatePriorityMsg": "La priorité a été mise à jour avec succès", "activitytypes": "Activités par type", "timeRangeStart": "de", "timeRangeEnd": "à", "addGuestsBtn": "participants", "addFollowersBtn": "Ajouter des suiveurs", "addGuestsBtnInCard": "Ajouter des participants", "urgentPriority": "Urgente", "mediumPriority": "<PERSON><PERSON><PERSON>", "lowPriority": "Faible", "highPriority": "Élevée", "deleteTaskModal": "Supprimer activité{{plural}} sélectionnée{{plural}}?", "guestsListTitle": "Participants", "followersListTitle": "<PERSON><PERSON><PERSON>", "followersListTitleInfo": "(Uniquement les collègues)", "listSearchPlaceholder": "Recherche par nom, adresse email, numéro de poste, téléphone", "ascendSort": "Cliquer pour trier par ordre croissant", "descendSort": "Cliquez pour trier par ordre décroissant", "cancelSort": "Cliquer pour annuler le tri", "associateModule": "Associer un module", "selectModulePlaceholder": "Sélectionner un ou plusieurs modules", "endListIndicator": "Pas plus", "drawerHeaderOne": "L'activité a été créée par", "drawerHeaderVisioOne": "La Visio a été créée par", "drawerHeaderTwo": "le", "visioErr": "Veuillez réessayer plus tard", "startTime": "<PERSON><PERSON> d<PERSON>", "endTime": "Heure de fin", "showTime": "Afficher l'heure", "correspondingStage": "Étape", "correspondingStagePlaceholder": "Étape", "setPriority": "Définir la priorité", "attachments": "Pi<PERSON><PERSON>{{s}} jointe{{s}}", "addTaskInKanban": "<PERSON><PERSON>er un nouveau {{component}}", "AddNewStage": "Ajouter une nouvelle étape", "stageLabel": "Libéllé de l'étape", "WinProbability": "Probabilité de gain", "color": "<PERSON><PERSON> une couleur", "stageLabelError": "Le libellé est requis", "taskToastMsgwithOptions": "<strong>{{user}}</strong> vous a ajouté à une nouvelle activité: <strong>{{taskLabel}}</strong>", "updateTaskToastMsgwithOptions": "<strong>{{user}}</strong> a modifié l'activité <strong>{{taskLabel}}</strong>", "deleteTaskToastMsg": "<strong>{{user}}</strong> a supprimé l'activité <strong>{{taskLabel}}</strong>", "updateTaskPriorityToastMsgwithOptions": "<strong>{{user}}</strong> a modifié la priorité de <strong>{{taskLabel}}</strong> de <strong>{{from}}</strong> à <strong>{{to}}</strong>", "updateTaskStageToastMsgwithOptions": "<strong>{{user}}</strong> a modifié le statut de <strong>{{taskLabel}}</strong> de <strong>{{from}}</strong> à <strong>{{to}}</strong>", "noPriority": "pas de priorité", "mercureReminder": "L'activité <strong>{{task<PERSON><PERSON><PERSON>}}</strong> commence dans <strong>{{time}}</strong>", "mercureReminderDesktopNotif": "L'activité [{{task<PERSON><PERSON><PERSON>}}] commence dans [{{time}}]", "reminderTooltip": "<PERSON><PERSON><PERSON>", "calendarViewTooltip": "<PERSON><PERSON><PERSON>", "tableViewTooltip": "<PERSON><PERSON>", "listViewTooltip": "Liste", "noMoreNotifs": "Fin des notifications", "noMoreReminders": "Fin des rappels", "noMoreComments": "Fin des commentaires", "createChatRoom": "Créer un groupe de discussions", "openChatRoom": "Ouv<PERSON>r le groupe de discussions", "menu2Header": "Activités: affichage du {{selected<PERSON>iew}}", "reminderBeforeStart": "Rappel avant la date de début", "reminderBeforeDue": "Rappel avant la date de fin", "reminderInfo": "En cochant cette option, vous recevrez un rappel avant l'échéance de cette activité.", "moveToStage": "<PERSON><PERSON><PERSON>r vers une nouvelle étape", "reminderTitle": "Rappel d'activité", "overdueTaskInfo": "Cette activité n'est pas terminée à l'échéance prévue ou attendue.", "taskDeletedToast": "L'activité a été supprimée avec succès", "joinVisio": "Accédez instantanément à la réunion", "inviteMembersParam": "Envoyer des e-mails de notification aux participants sélectionnés", "invitecolleaguesParam": "Envoyer des messages système aux collègues sélectionnés", "uploadFile": "C<PERSON>r ou faites glisser le fichier dans cette zone pour le télécharger", "uploadFileHint": "Les images et les fichiers sont supportés.", "today": "Aujou<PERSON>'hui à", "choose": "Choi<PERSON>", "addActivity": "L'activité a bien été ajoutée", "notAuthorizedToSeeActivity": "Vous n'êtes plus assigné à cette activité.", "selectFamily": "Sélectionner un module", "selectFamilyPlaceholder": "Sélectionner un module", "selectFamilyElement": "Élément relatif", "selectFamilyElementPlaceholder": "Sélectionner un élément", "deleteAllBtn": "<PERSON><PERSON>", "locationPlaceholder": "Lieu de la réunion", "creatorRole": "<PERSON><PERSON><PERSON><PERSON>", "OwnerRole": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MemberRole": "Participant", "followerRole": "<PERSON><PERSON><PERSON>", "selectRolePlaceholder": "Sélectionnez votre rôle", "generalTab": "Activité", "detailsTab": "Détails", "sendNotifs": "Réglages de notification", "msgSystemNotif": "Un message système sera envoyé à tous les collègues sélectionnés.", "emailSystemNotif": "Un E-mail sera envoyé à tous les membres sélectionnés lors de l'ajout ou de la mise à jour d'une activité.", "requiredElementOnSelectModule": "<PERSON><PERSON> devez sélectionner un élément du module sélectionné", "requiredModuleOnSelectElement": "V<PERSON> devez sélectionner le module auquel appartient l'élément sélectionné", "elementNotBelongingToSelectedModule": "L'élément sélectionné n'appartient pas au module sélectionné", "filterTitle": "Filtre avancé", "clearFilter": "Réinitialiser le filtre", "clearBtn": "Annuler", "filterResult": "Résultats: ", "loadActivitiesError": "Une erreur s'est produite lors du chargement de votre activité", "tryAgain": "<PERSON><PERSON><PERSON><PERSON>", "calendarTryAgain": "Une erreur s'est produite, veuil<PERSON><PERSON> r<PERSON>", "searchOptions": "Rechercher dans:", "searchByLabel": "libellé d'activité", "searchByName": "nom du membre", "aucun": "Aucun", "addDate": "Ajouter une Date", "userFamily": "<PERSON><PERSON><PERSON><PERSON>", "orgFamily": "Organisation", "contactFamily": "Contact", "fileExtensionError": "l'Extension de fichier n'est pas supportée!", "associatedModule": "<PERSON><PERSON><PERSON> associé", "creator": "Planificateur", "activityId": "ID d'activité", "showCols": "{{displayed}} sur {{total}}", "show/hide": "afficher/masquer les colonnes", "activityDeleted": "Cette activité a été supprimée", "userRemovedFromActivity": "Vous n'êtes plus en mesure de participer à cette activité.", "unathorizedActivity": "Vous n'êtes pas autorisé à accéder aux détails de l'activité.", "noResult": "Aucun résultat", "activityStartsNow": "l'activité <strong>{{activityLabel}}</strong> commence maintenant", "activityEndsNow": "L'activité <strong>{{activity<PERSON>abe<PERSON>}}</strong> est terminée {{endTime}}", "moduleElement": "Cliquez pour voir les détails de {{elementLabel}}.", "view360": "<PERSON><PERSON> générale", "copyLink": "Copier le lien vers l'activité", "configureLocalizationNotif": "Format de date et d'heure manquant! <br /> <PERSON><PERSON><PERSON>z configurer vos paramètres", "GoToLocalization": "Accéder aux réglages", "noFollower": "Au<PERSON>n suiveur", "noGuest": "Aucun participant", "noChat": "Chat non disponible", "noChatInfo": "Il doit y avoir au moins 2 collègues différents pour activer le chat.", "deleteStage": "Supprimer <strong>{{label}}</strong>", "moreInfo": "Plus d'options", "markAllRead": "<PERSON><PERSON> tous comme lus", "unread": "Non lus", "kpiVisioLabel": "Visio", "kpiCreatedLabel": "Créés par moi", "kpiIsOverdueLabel": "Dépassées", "kpiIsOverdueVisio": "Visio <PERSON>", "kpiInvitedLabel": "Vous ê<PERSON> invité", "KpiChatSource": "Source à partir de la discussion", "logFeedTitle": "Historique", "logFeedEndOfList": "Liste terminée", "historyBtn": "Historique", "allPipelines": "Tous les Pipelines", "reminderErrorMsg": "Le champ '<PERSON><PERSON>' ne peut pas être vide", "commentsTab": "Commentaires", "unreadNotifs": "Notifications non lues", "elementShowMoreBtn": "Afficher {{totalHidden}} plus", "showMoreBtn": "Afficher plus", "elementShowLessBtn": "Affiche<PERSON> moins", "newComment": "{{messageCount}} Nouveau commentaire{{s}}", "dateTimeError": "La date et l'heure de début ne peuvent pas être postérieurs à la date et l'heure d'échéance.", "filterFromToday": "À partir d'aujourd'hui <span class='bg-blue-100 text-black underline decoration-blue-500 underline-offset-1'>{{today}}</span>", "startFrom": "À partir de {{today}}", "occurredActivityReminder": "Ce rappel pour <strong>{{activityLabel}}</strong> programmé <strong>{{reminderDuration}}</strong> avant. (commencé à <strong>{{startDate}}</strong>)", "filterInfo": "Les paramètres du filtre sont automatiquement enregistrés.", "creatorInfoTip": "le {{date}} à {{time}}", "rename": "<PERSON>mmer", "startingFromToday": "À partir d'aujourd'hui", "monthView": "<PERSON><PERSON>", "weekView": "<PERSON><PERSON><PERSON>", "dayView": "Jour", "listView": "Liste", "TodayView": "<PERSON><PERSON><PERSON>'hui", "openAttachment": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loadRoomError": "Une erreur s'est produite, veuillez réessayer plus tard.", "noDescriptionNote": "Aucune description/Note", "goToSettings": "Aller aux paramètres des pipelines", "assignment": "Affectation", "visioInProgress": "Visio conférence en cours...", "meMode": "Mode moi", "filterConditionInfo": "Cette option vous permet de combiner les filtres. Choisissez 'OU' pour correspondre à l'une des conditions, ou 'ET' pour correspondre à toutes les conditions. Au moins deux filtres doivent être activés pour utiliser cette option.", "filterConditionLabel": "Sélectionnez comment les filtres sont combinés :", "and": "ET", "or": "OU", "startCreateActivities": "Commencer <PERSON> créer", "activities": "Activités", "recordings": "Enregistrements", "None": "Aucune", "selectModuleInfo": "Sélectionnez un module à associer à l'activité. Le module définit la catégorie ou le contexte de l'activité.", "selectModuleElementInfo": "Choisissez un élément du module sélectionné à associer à l'activité. Les éléments représentent des items spécifiques au sein du module.", "checklistTooltip": "T<PERSON><PERSON>(s)", "addPipelineError": "<PERSON><PERSON> de<PERSON> d'abord ajouter un pipeline", "subtasksTitle": "Listes", "done": "<PERSON><PERSON><PERSON><PERSON>", "fileSize": "Ko", "selectTags": "Sélectionnez des étiquettes", "doctors": "Doctors", "selectDoctor": "Sélectionnez un docteur", "exams": "Spécialité / Examen", "products": "Familles / produits", "selectExam": "Sélectionnez un examen", "clinics": "Hôpitals", "selectClinic": "Sélectionnez un Hôpital", "medicalStaff": "Personnel médical", "patients": "Patients"}, "form": {"save": "Enregistrer", "cancel": "Annuler", "submit": "Envoyer", "create": "<PERSON><PERSON><PERSON>"}, "beforeDeleted": {"departments": "Si vous supprimez ce département, tous ces services seront supprimés.", "type-family-products": "Si vous supprimez ce type de produit, tous ces familles seront supprimés.", "pipelines": "Si vous supprimez ce pipeline, toutes ses étapes seront supprimées.", "steps": "Attention : les étapes qui dépendent de cette étape seront dépend automatiquement à l'étape précédente"}, "helpDesk": {"Levels": "Niveau", "addChannel": "Ajouter un canal", "addDep": "Ajouter un département", "addService": "Ajouter un service", "addSeverity": "Ajouter une sévérité", "addSla": "Ajouter un SLA", "addFolder": "Ajouter un dossier", "channels": "Canaux", "folder": "Dossiers", "hours": "<PERSON><PERSON>", "minutes": "Minutes", "days": "Jours", "state": "État", "addType": "Ajouter un type de contact", "actif": "Actif", "noActif": "Inactif", "status": "Statut", "customers": "Contacts", "all": "Tous", "listcontacts": "Listes des contacts", "listproduits": "Listes des produits", "listteams": "Listes des équipes", "listorganisations": "Listes des organisations", "listproducts": "Listes des produits", "clients": "Clients", "produits": "Produits", "teams": "Equipes", "selectproduits": "Sélectionner les produits", "selectclients": "Sélectionner les clients", "selectteams": "Sélectionner les équipes", "selectorganisations": "Sélectionner les organisations", "selectcontacts": "Sélectionner les contacts", "allproduits": "Tous les produits", "allclients": "Tous les contacts", "allteams": "Tous les équipes", "createsla": "Créer SLA", "updatesla": "Modifer SLA", "usedothersla": "est existe dans un autre sla", "assignedTo": "<PERSON><PERSON><PERSON>", "qualifiedTo": "Qualifié à", "changeName": "Changer uniquement le nom", "subject": "Sujets", "addSubject": "Ajouter sujet", "addLevel": "A<PERSON>ter niveau", "contacts": "Contacts", "products": "Produits", "organisations": "Organisations", "activeTemplateEmail": "Veuillez affecter le modèle requis et vous assurer qu'il est activé.", "successRemis": "Remis avec succés.", "successRelaunched.": "Relancé avec succés.", "assign": "M'assigner", "move": "<PERSON><PERSON><PERSON><PERSON>", "moved": "Déplacé", "transfer": "<PERSON><PERSON><PERSON><PERSON>", "relaunch": "Relancer", "handOver": "Remettre", "ticket": "le{{plural}} ticket{{plural}}", "merge": "<PERSON><PERSON>", "mergeTicketBlock": "Fusionner ticket en block", "transfertTicketBlock": "Transférer les tickets en bloc", "coverTicket": "Etes vous sure de prendre en charge ce ticket ?", "SureRelaunchTicket": "Etes vous sur de vouloir relancer ce ticket ?", "listLevels": "Liste des niveaux", "cateringAgent": "Agent traiteur", "reasonTransfer": "<PERSON><PERSON> du <PERSON>", "InterventionCarriedOut": "Intervention effectuée", "sureHandTicket": "Etes vous sur de vouloir remettre ce ticket ?", "moveToTrash": "<PERSON><PERSON><PERSON><PERSON> vers la corbeille", "slaRespected": "SLA respectée", "slaDepassed": "SLA dépassée", "closeTicket": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reopenTicket": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "closeTicketConfirmMessage": "Êtes-vous sûr de clôturer ce ticket?", "reopenTicketConfirmMessage": "Êtes-vous sûr de rouvrir ce ticket?", "cancelNoAuth": "Vous n'êtes pas autorisé à annuler ce ticket"}, "users": {"addRole": "Ajouter un rôle", "addTeam": "Ajouter une équipe", "users": "Utilisateurs", "teams": "Équipes", "roles": "<PERSON><PERSON><PERSON>", "guests": "Invités"}, "familyProduct": {"addFamily": "Ajouter une famille", "addType": "Ajouter un type de produit", "addUnity": "Ajouter une unité", "addCompany": "Ajouter une entreprise", "addDiscount": "Ajouter une remise", "discount": "La remise", "addTax": "Add une tax", "rate": "<PERSON><PERSON>"}, "import": {"uploadSample": "Télécharger", "uploadSampleDescription": "Guide de mise en forme des feuilles de calcul", "mapColumns": "Mapping des colonnes", "mapColumnsDescription": "Mappez les colonnes avec les champs", "configuration": "Configuration", "configurationDescription": "Configuration d'importation de feuille de calcul", "import": "Importer", "importDescription": "Démarrer le processus d'importation", "date": "Date", "fileName": "Nom du fichier", "OK": "Importés", "duplicates": "Doublons", "errors": "<PERSON><PERSON><PERSON>", "actions": "Actions", "delete": "<PERSON><PERSON><PERSON><PERSON>", "upload": "Cliquez ou glissez pour télécharger la feuille de calcul", "delimiter": "Délimiteur", "filePreview": "Aperçu du fichier", "submit": "So<PERSON><PERSON><PERSON>", "selectFamily": "Sélectionnez une famille", "spreadsheetColumns": "Colonnes de la feuille de calcul", "forceOption": "Option Force (Remplacer les champs dupliqués par de nouvelles valeurs)", "extraFieldOption": "Option Champs supplémentaires (Ajouter de nouveaux champs et les fusionner avec l'ancienne importation)", "fileProcessed": "<PERSON><PERSON><PERSON> fichier a été traité", "uploadAnother": "télécharger un autre fichier ?", "history": "Historique", "family": "<PERSON><PERSON><PERSON>", "selectMapping": "Sélectionner un champ mappé", "choosePk": "Veuillez choisir une clé primaire !", "selectPk": "Sélectionner une clé primaire", "uploadedSuccess": "téléchargement terminé avec succès.", "uploadFailed": "le téléchargement a échoué.", "jobLaunched": "La tache a été lancée avec succès.", "jobFailed": "Le lancement de la tâche a échoué.", "nameOfColumnExist": "la première ligne de votre fichier contient les noms des colonnes", "reset": "Réinitialiser", "inputFile": "Veuillez entrer votre fichier", "chooseFamily": "<PERSON><PERSON><PERSON>z choisir une famille", "unkownColumnName": "Colonne", "continue": "<PERSON><PERSON><PERSON>", "standardOption": "Option standard (importation standard sans forcer les doublons et les champs supplémentaires)", "restoreMappingFailed": "Erreur lors de la restauration de ce mappage.", "duplicatesNotification": "Il y a des doublons dans le fichier. Vouslez-vous forcer l'upload ? si oui merci de choisir une clé primaire :", "next": "Suivant", "previous": "Précédant", "titleOfFileColumns": "Titre des colonnes de votre fichier", "familyFields": "<PERSON><PERSON>", "uploadHint": "Formats pris en charge : .csv, .xls, .xlsx.", "uploadWarning": "Votre fichier sera automatiquement converti en .csv.", "downloadFileWithTitle": "Télécharger {{filename}}", "uploadSuccessHint": "Historique ID : {{historyId}} / Importées : {{nbr_ok}} / Passées : {{nbr_doublons}} / Erreurs : {{nbr_error}} / Famille : {{family}}", "uploadRows": "Nombre de lignes", "uploadColumns": "Nombre de colonnes", "uploadSize": "<PERSON>lle de télechargement", "uploadFileName": "Nom du fichier", "archived": "Archivés", "deleted": "Supprimés", "deleteAndArchiveRecordsNotification": "Vou<PERSON>z-vous supprimer ou archiver les données trouvées ?", "archiveAction": "Archiver", "deleteAction": "<PERSON><PERSON><PERSON><PERSON>", "deleteAndArchiveAction": "Supprimer / Archiver", "isNotImage": "n'est pas une image", "deleteArchiveSummary": "Historique ID: {{historyId}} / Familles liées: {{families}} / À Archiver : {{archived}} / À Supprimer : {{deleted}}", "fileProcessedWithWarnings": "Votre fichier n'a pas été traité correctement", "fileInfos": "Information<PERSON> <PERSON><PERSON>", "download": "Télécharger", "downloadSample": "Télécharger un échantillon", "deleteRecordsNotification": "Voulez-vous supprimer les données trouvées ?", "deleteDescription": "Cet enregistrement dans la table d'historique avec l'ID {{historyId}} sera définitivement supprimé.", "status": "Statut", "completed": "Complété", "mapping": "Mapping", "downloadSampleDescription": "En cas de liste si vous trouvez des séparations avec 'OU' dans le fichier d'exemple, vous devez choisir une valeur dans cette liste. En cas d'intervalle, si vous trouvez des séparations avec ',' dans le fichier d'exemple, vous devez mettre un intervalle de deux éléments.", "notSupportedFormat": "format de ce fichier non supporté.", "uploadFileFamily": "<PERSON><PERSON><PERSON>", "organisation": "Organisation", "contact": "Contact", "deal": "Offre", "user": "Utilisa<PERSON>ur", "project": "Projet", "booking": "Réservation", "leads": "<PERSON><PERSON><PERSON>", "product": "Produit", "helpdesk": "Helpdesk", "mapLists": "Mapping des listes", "importDetails": "Détails d'import", "spreadsheetLists": "Listes de la feuille de calcul", "titleOfFileLists": "Titre des listes de votre fichier", "lists": "Listes", "selectMappingList": "Sélectionner une liste mappée", "selectMappingListFamily": "Commencez à taper.", "logs": "Logs", "logsEmpty": "Vide", "addType": "Ajouter {{title}}", "company": "Organisation", "department": "Département", "service": "Service", "role": "R<PERSON><PERSON>", "startImport": "Commencer l'import", "downloadFileAgain": "Télécharger la feuille de calcul", "mappingColumn": "Colonnes mappées", "mappingList": "Listes mappées", "empty": "Valeur vide", "requiredFields": "Champs Requis", "sharedWith": "Veuillez sélectionner les départements à partager avec", "uniqueFields": "Champs Uniques", "cancelImport": "Annuler l'import", "importingFile": "Fichier en cours d'importation", "mappingListNotice": "Sélectionnez les colonnes à mapper dans la liste ci-dessous. Les colonnes non mappées seront ignorées", "dualCode": "indicatif", "phoneCode": "Téléphone", "monetaryCurrency": "<PERSON><PERSON>", "monetaryValue": "<PERSON><PERSON>", "importWarningText": "Un import est en cours, voulez-vous l'annuler et démarrer un nouvel import ?", "importWarningTitle": "Avertissement concernant l'importation", "importWarningContinueBtn": "continuer l'importation", "importWarningCancelBtn": "annuler l'importation", "nextStepBtn": "Etape suivante", "cancelBtn": "Annuler et recommencer", "shareWith": "Partager avec", "shareWithInfo": "Indiquez l'option de confidentialité de partage de vos contacts importés: Si vous choisissez 'Moi uniquement', les contacts ne s'affichent que pour vous. Si vous choisissez 'Départements', vous devez choisir les départements qui sont autorisés à accéder aux contacts avec la possibilité d'apporter les modifications nécessaires.", "onlyMe": "<PERSON><PERSON> uniquement", "others": "Autres", "selectDeptsError": "Veuillez sélectionner au moins un département", "shareBtnError": "Veuillez choisir une option", "selectFamilyError": "<PERSON><PERSON><PERSON>z choisir une famille", "inviteImports": "Envoyer des invitations par E-mail aux utilisateurs importés", "sendInvitaionsToast": "Un e-mail d'invitation a été envoyé à tous les utilisateurs importés", "autocompletePlaceholder": "<PERSON><PERSON> la valeur de la liste", "inviteUsers": "Inviter", "inviteUsersTooltip": "{{invitedNumber}} utilisateur(s) sur {{importedNumber}} importé(s) ont déjà été invités", "dialCode": "Forcer les valeurs manquantes de l'indicatif", "dialCodeInfoTooltip": "L'option sélectionnée sera forcée pour les valeurs manquantes dans la colonne {{value}} de votre fichier importé.", "currency": "Forcer les valeurs Devises manquantes", "currencyInfoTooltip": "L'option sélectionnée sera forcée pour les valeurs manquantes dans la colonne {{value}} de votre fichier importé.", "accurateAutoMap": "Auto-sélection précise", "autoMapToVerify": "Auto-sélection pour vérifier", "selectCurrencyPlaceholder": "Sélectionner la devise", "selectDialPlaceholder": "Sélectionner l'indicatif", "selectFamilyPlaceholder": "Sélectionner la famille correspondante", "monetary": "<PERSON><PERSON>", "dialCodeLabel": "Indicatif téléphonique", "importDone": "L'importation a été lancée avec succès. Vous serez informé une fois l'importation terminée.", "importFinished": "L'import est achevé avec succès. Veuillez consulter le tableau de l'historique pour obtenir des informations.", "startAnother": "Importer un autre fichier", "importHasStarted": "L'importation a commencé. Vous serez notifié lorsque l'import sera terminé.", "cancelImportTitle": "Annuler l'import", "cancelImportDesc": "si vous annulez l'importation, vous pouvez la reprendre à tout moment à partir de cette étape. Il vous suffit de cliquer sur 'continuer' dans le tableau de l'historique.", "continueImportBtn": "Continuer l'mport", "selectAll": "<PERSON><PERSON>", "selectedDateFormat": "Format de date sélectionné: "}, "companies": {"socialreason": "Raison sociale", "adress": "<PERSON><PERSON><PERSON>", "city": "Ville", "postalCode": "Code postal", "country": "Pays", "phone": "Téléphone", "website": "Site Web", "email": "Email", "banks": "Banques", "taxId": "Numéro d'identification fiscale", "vatCode": "Code TVA", "tradeRegister": "Registre du commerce", "capital": "Capital", "addbank": "Ajouter une Banque", "infoFis": "Informations fiscales", "infoGeneral": "Informations générales", "accessTableCompanies": "Accéder au tableau des entreprises", "principalCompany": "Entreprise principale", "addPipeline": "Ajouter un Pipeline", "addStage": "Ajouter une étape", "addcompany": "Ajouter une entreprise", "selectcompanies": "Sélectionner des entreprises", "systemEmail": "Adresse EMAIL d'envoie automatique (Système)", "senderName": "Nom de l'expéditeur", "shippingEmail/Sms": "Paramètres d'envoi (EMAIL/SMS)", "infoSenderEmail": "La valeur de l'expéditeur (email) est extraite de la section Général/Entreprises/{{label}}/Expédition (EMAIL/SMS)", "infoSenderSms": "La valeur de l'expéditeur (SMS) est extraite de la section Général/Entreprises/{{label}}/Expédition (EMAIL/SMS)", "infoSignSms": "La valeur de la signature (EMAIL) est extraite de la section Général/Entreprises/{{label}}/Expédition (EMAIL/SMS)", "tax_identification": "Tax Identification Number", "tva_code": "Code TVA", "trade_register": "Registre du commerce", "code_postal": "Code postal", "logo": "Logo", "icon": "Icône", "label": "Raison sociale", "company": "Entreprise", "goToSettings": "Allez à Général/Entreprises/{{label}}/Expédition (EMAIL/SMS)", "searchInfoInModule": "Rechercher par toutes les colonnes", "searchInfoInModuleInTable": "Rechercher dans toutes les colonnes", "searchInfoInModuleInKanban": "Recherche possible pour tout les critères. Pour date, utilisez le format yyyy-mm-dd.", "kanbanSettingsInfo": "Aller vers les paramètres de pipelines de <strong>{{moduleLabel}}</strong>", "agency": "Agence", "countries": "Pays", "currency": "<PERSON><PERSON>", "bank": "Banque", "correctNumberPhone": "Veuillez entrer un numéro de téléphone valide."}, "general": {"companies": "Entreprises", "channels": "Canaux", "countries": "Pays", "queueGuest": "File d'attente guest", "addQueueGuest": "Ajouter file d'attente guest"}, "guestQueue": {"name_en": "Nom en EN", "name_fr": "Nom en FR", "queue_number": "File d'attente", "selectQueue": "Sélectionner file d'attente"}, "services": {"label": "Nom", "departement_id": "Département", "selectdepartment": "Sélectionner un département"}, "colors": {"Black": "Noir", "Red": "Rouge", "Dark Red": "Rouge F<PERSON>cé", "Orange": "Orange", "Dark Orange": "Orange Foncé", "Yellow": "Jaune", "Green": "<PERSON>ert", "Dark Green": "<PERSON><PERSON>", "Emerald": "<PERSON><PERSON><PERSON><PERSON>", "Teal": "<PERSON><PERSON><PERSON>", "Cyan": "<PERSON><PERSON>", "Light Blue": "<PERSON><PERSON><PERSON>", "Blue": "Bleu", "Dark Blue": "<PERSON><PERSON><PERSON>", "BlueViolet": "B<PERSON>u <PERSON>", "Indigo": "Indigo", "Purple": "Violet", "Violet": "Violet", "Fuchsia": "Fuchsia", "Pink": "<PERSON>", "Rose": "<PERSON>", "Gray": "<PERSON><PERSON>", "Dark Gray": "<PERSON><PERSON>", "Slate": "<PERSON><PERSON><PERSON>", "Brown": "<PERSON><PERSON>", "Amber": "Ambre", "Lime": "Citron Vert", "Sky": "<PERSON><PERSON><PERSON>"}, "wiki": {"Docs": "Docs", "Groups": "Groupes", "SelectGroup": "Sélectionnez un groupe...", "AddFolder": "Ajouter dossier", "AddPage": "Ajouter une page", "AddBinder": "Ajouter un catalogue ", "editBinder": "Modifier le catalogue ", "binder": "Catalogues", "EditFolder": "Modifier le dossier", "DeleteFolder": "Supp<PERSON>er le dossier", "AddAFolder": "Ajouter un dossier", "TitleFolderFr": "Titre en FR", "TitleFolderEn": "Titre en EN", "Cancel": "<PERSON><PERSON><PERSON>", "Ok": "<PERSON><PERSON><PERSON><PERSON>", "AddPageToFolder": "Ajouter une page au dossier: ", "TitlePageFr": "Titre de la page en FR", "TitlePageEn": "Titre de la page en EN", "Delete": "<PERSON><PERSON><PERSON><PERSON> ", "Edit": "Modifier", "Confirm": "Confirmer", "Save": "Enregistrer", "SlugUrl": "Slug Url", "French": "Français", "English": "<PERSON><PERSON><PERSON>", "TitlePage": "<PERSON><PERSON><PERSON> de la page", "Content": "Contenu", "SlugUrlCopied": "Slug URL copié", "AddGroup": "Ajouter Groupe", "Image": "Image", "UploadImage": "Importer une image", "sorting": "sort", "AddAGroup": "Créer un Groupe", "EditGroup": "Modifier le Groupe", "Title": "Titre", "Description": "Description", "FolderAlreadyExists": "Ce dossier existe déja!", "PageAlreadyExists": "Cette page existe déja!", "NoGroupSelected": "Aucun groupe selectionné", "TitleRequired": "Le titre est obligatoire!", "PageEditedSuccessfully": " modifiée avec succès!", "Published": "Publiée", "Draft": "Brouillon", "ImageRequired": "L'image est obligatoire!", "The label fr has already been taken.": "Le label a déjà été pris.", "uploadContent": "Mettre en ligne le contenu", "ErrDownload": "Erreur lors du téléchargement du document", "metaDescription": "Méta Description"}, "pipeline": {"pipelinestickets": "Tickets de pipelines", "pipelinesdeals": "Offres de pipelines", "pipelinesprojects": "Projets de pipelines", "stagestickets": "Tickets d'états", "stagesdeals": "Offres de stages", "stagesprojects": "Projets de stages", "pipelineName": "Nom du pipeline", "stageName": "Nom de l'étape", "finalStage": "Final", "stage": "Étapes", "errFinalStage": "Pi<PERSON>ine doit contenir au moins une étape finale.", "isDefault": "Vous ne pouvez pas mettre à jour cette étape", "chooseAnotherPipeline": "Vous pouvez choisir un autre type de pipeline.", "createPipelineFirst": "Créer un pipeline d'abord", "addStageToGrouName": "Ajouter cette étape dans un nom de groupe"}, "chat": {"reload": "Recharger", "loading": "Chargement", "more": "Plus...", "less": "Moins.", "loadPreviousMessage": "Charger le message précédent", "groupe": "Groupe", "groupe_public": "Groupe publique", "groupe_guest": "Groupe avec invités", "empty_discussion": "Lancement d'une nouvelle discussion", "addGroup": "Créer un groupe", "nameGroup": "Nom du groupe", "selectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> tout", "uploadDragImage": "Cliquez ou faites glisser l'image dans cette zone pour la télécharger", "errSizeImage": "doit être inférieur à {{size}} !", "addColleague": "Ajouter une discussion", "filter": "<PERSON><PERSON><PERSON>", "startconversation": "Démarrer une nouvelle discussion", "retrieveFileDataError": "Une erreur s'est produite lors de la récupération du fichier. Veuillez réessayer plus tard.", "viewFileWithTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> le <PERSON> <u>{{fileName}}</u>", "forward": {"text_message_forwarded_at": "le ", "text_message_forwarded": "Transféré", "text_message_forwarded_title": "Historique de transfert", "other_room": "une autre discussion", "previewMsg": "Le message transféré : ", "forwardTo": "Votre message a été transféré par :", "forwardToOther": "Le message envoyé par <i class=' font-semibold '>{{sent_name}}</i> a été transféré par : ", "title_modal": " Transférer un message", "content_modal_message": "Etes-vous sur de vouloir transfèrer ce message?", "you_forward_message": "V<PERSON> avez transféré un message envoyé par ", "you_forward_your_message": "Vous avez transféré un message envoyé par vous-même", "you_received_forwarded_message": " vous a transféré un message envoyé par ", "room_received_forwarded_message": " a transféré un message envoyé par ", "to": "Vers", "placeholder_select1_forward": " Tapper pour séléctionner le destinataire", "placeholder_select2_forward": " Choisir le nom du destinataire", "message_sidebar": "Transféré", "error_select_user": "Veuillez choisir un ou plusieurs utilisateurs"}, "error_message": {"message_send": "Message non envoyé. ", "re-send_action": "Cliquez ici pour renvoyer le message ou le supprimer.", "re-send": "<PERSON><PERSON>abord, renvoyer ou supprimer le message erroné.", "chooseUsers": "Sélectionnez un ou plusieurs utilisateurs !", "failedAddMembers": "Échec d'ajouter de membre(s)", "delete_error_member": "Êtes-vous sûr de vouloir retirer ce membre ?", "selectNewAdmin": "Sélectionnez le nouvel administrateur.", "designateAdmin": "Êtes-vous sûr de d<PERSON> ce membre en tant qu'administrateur?", "min3chart": "Le nom doit contenir au moins 3 caractères"}, "delete": {"deleteMember": "<PERSON><PERSON><PERSON><PERSON>", "messaageDeleted": "Message supprimé", "title_modal": "Supprimer un message", "delete_member": "Le membre a été retiré avec succès", "content_modal_message": "Etes-vous sur de vouloir supprimer ce message?", "delete_error_sent_message_title": "Suppression définitive d'un message", "delete_error_sent_message_description": "Êtes-vous sûr de vouloir supprimer ce message ?"}, "polls": {"poll": "Sondage", "title_modal": "<PERSON><PERSON><PERSON> un sondage", "question": "Question", "question_placeholder": "Tapez votre question", "control_question": "Veuillez saisir une question", "option_placeholder": "Tapez votre option", "add_option": "Ajouter une option", "Options": "Options", "allow_multiple_answers": "Autoriser les réponses multiples", "multiple_choice": " Choix multiples", "single_choice": " Choix unique", "Yes": "O<PERSON>", "No": "Non", "all": "Tous", "incomplete": "En cours", "public_tooltip": "Permet de voir la liste des personnes qui ont répondu au sondage ainsi que leurs réponses.", "expire_At": "Date d'expiration", "finished": "<PERSON><PERSON><PERSON><PERSON>, ", "private": "Afficher les participants", "unique_option": "Les options doivent être uniques", "min_option": " <PERSON><PERSON> devez ajouter au moins 2 options", "option_not_empty": "L'option ne peut pas être vide", "success": "Sondage créé avec succès.", "voted": "votes", "max_option": " Vous pouvez sélectionner seulement 5 options", "show_result": "Aff<PERSON>r le résultat", "hide_result": "Cacher le résultat", "limit_time": " Aucun limite de temps", "limit_time_tooltip": "En activant cette option, le sondage sera définitivement clôturé à la date configurée.", "you_voted": " avez voté dans : ", "other_voted": " a voté dans : ", "expire": "Expire", "list": "Liste de tous les sondages", "incomplete_list": "Liste des sondages inachevés", "polls": "Sondages", "validation": {"time_out": "Le sondage a expiré"}}, "reply": {"message": "a répondu au message  ", "sentBy": "envoyé par", "sidebar": "a répondu : ", "close": "<PERSON><PERSON><PERSON> d'abord le fil de discussion"}, "file": {"error_taile": "Le fichier doit être inférieur à 50Mo !", "max_file": "Vous pouvez sélectionner seulement 5 fichiers, par défaut le système prend les 5 premiers !", "error_extention": "Les seules extensions autorisées sont les suivantes : '{{extention}}'"}, "action": {"deviceMobile": "Depuis Mobile", "deviceDesktop": "Depuis Web", "delete": "<PERSON><PERSON><PERSON><PERSON>", "forward": "<PERSON><PERSON><PERSON><PERSON>", "resend": "Relancer", "pin": "<PERSON><PERSON><PERSON>", "pinned": "<PERSON><PERSON><PERSON>", "By": " par ", "pinned_info": "Epinglé (pour tous les membres)", "unpin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "react": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "reply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "emoji": "<PERSON><PERSON><PERSON>", "generateText": "Réécrire avec l'IA", "messageEmptyErrorAi": "Impossible d'améliorer le message car aucun contenu n'est présent.", "rich_text": "<PERSON><PERSON><PERSON>", "upload": "Chargement de fichiers", "call": "<PERSON><PERSON><PERSON>", "call_audio": " Appel audio", "call_video": " <PERSON><PERSON> vidéo", "create_conferance": "Créer une réunion vidéo", "star": "Mettre dans les favoris", "starred": "<PERSON><PERSON><PERSON> ", "starred_info": "Favoris (que pour vous)", "unstar": "Supprimer des favoris", "list_starred": "Liste des messages favoris", "list_polls": " Liste des sondages", "incomplete_list": "Liste des sondages incomplets", "list_pinned": "Liste des messages épinglés", "sendMessage": "Discuter", "designateAdmin": "Confirmer la désignation", "newAdmin": "Nouveau admin", "option": "Option", "copy": "<PERSON><PERSON><PERSON> ", "copied": "<PERSON><PERSON><PERSON>", "task": "Créer une activité"}, "header": {"members": "participants", "member": "participant", "share_video_call": "Partager le lien de la réunion", "share": "Partager lien d'invitation", "participate": " Participer à la réunion", "copyLink": " Copier le lien d'invitation", "shareWithEmail": " Partager par email", "visio": {"roomName": "Nom de la réunion", "invitation": "Vous êtes invités à participer à une vidéoconférence crée par  ", "timeToStart": "prévu {{time1}} à {{time2}}", "created_by": " créé par  ", "join": "Rejoindre la réunion", "invitation-link": "Cliquez sur le lien ci-dessous pour vous inscrire :", "title": "Invitation à la réunion vidéo de ", "title2": "Invitation à la réunion vidéo", "description": "vous a invité à rejoindre une visioconférence sur Sphere.", "visio-leave": "Merci d'avoir participé à la réunion vidéo. À bientôt !", "user-leave": "{{name}} a quitté la réunion vidéo", "user-join": "{{name}} a rejoint la réunion vidéo", "callVideoSIP": "Appel vidéo SIP", "createVideoConferance": "Créer une réunion vidéo", "validationHint": "Appuyer sur Entrée pour valider le nom", "full-screen": "Plein écran"}, "createVideoLater": "Planifier une réunion", "createVideoCallDescription": "Enregistrez ce lien et partagez-le avec les personnes que vous souhaitez rencontrer. Veillez à en conserver une copie pour un usage ultérieur. ", "createVideoCallError": "Une erreur s'est produite lors de la création de l'appel vidéo, veuillez réessayer plus tard.", "createVideoCallWait": "Attendez <timer> {{timer}} </timer> pour que la vidéo se charge automatiquement, ou cliquez sur le lien pour y accéder instantanément."}, "edit": {"text_message_old": "Message précédent : '", "text_message_update_at": "éditer le", "at": "à"}, "react": {"message_to_delete": "Cliquer pour supprimer la réaction", "other_user": "et {{number}} autre(s) "}, "audio_message": {"errorCharge": "Erreur lors du chargement de l'audio", "title_modal": "Message audio", "stop": "<PERSON><PERSON><PERSON><PERSON>", "permission_message": "Veuillez accepter l'autorisation afin d'utiliser votre microphone", "description": "Envoyer un message vocal dans une minutes, cliquer {{stop}} sur afin d'avoir le message.", "submit_directory": "Envoyer sans voir le message vocal ", "accessMic": "Veuillez autoriser l'accès au microphone pour utiliser cette fonctionnalité."}, "info": {"noMedia": "Aucun  média trouvé", "noLinks": "Aucun  lien trouvé", "noDocuments": "Aucun  document trouvé"}, "searchSide": {"all": "Tous", "conversations": "Discussions", "users": "<PERSON><PERSON>è<PERSON>", "messages": "Messages", "new_user": "Veuillez saisir votre message à {{name}} ...", "searchIcon": "<PERSON><PERSON><PERSON>", "searchTitle": " Quelle entrée de recherche choisir ?", "searchGroupUser": "Rechercher un groupe ou un collègue", "searchConversation": "Recherche de discussion", "navigation": "Navigation", "search": "Rechercher...", "searchMembers": "Rechercher les participants", "searchMessages": "Rechercher un message", "searchTips": "<PERSON><PERSON> le mot à chercher ensuite cliquer sur \"ENTRER\"", "searchResults": "<span class='font-semibold text-black'>{{count}}</span> résultat(s).", "cancelFilter": "<PERSON><PERSON><PERSON> le filtre", "searchMembersToAdd": "Rechercher des participants à ajouter", "searchGroup": "Rechercher un groupe", "searchGuest": "Rechercher un invité", "searchMember": "Rechercher un membre", "searchEmpty": "<PERSON><PERSON>r le filtre et refaire une recherche", "searchDocument": "Rechercher un document", "searchLinks": "Rechercher un lien"}, "message_system": {"draft": " Brouillon", "update_general_group": "a effectué une modification sur les informations du canal.", "leave_group": "a quitté le groupe", "remove_membre": "a retiré", "removed_auth_user": "vous a retiré du canal", "removed_auth_user_modal": "Vous avez été retiré du groupe par l'administrateur.", "groupe_deleted": "Le groupe a été supprimé par l'administrateur.", "add_membre": "a ajouté", "add_auth_user_membre": "vous a ajouté au canal", "successAddMembers": "L'ajout de membre(s) est effectué avec succès.", "successChangeAdmin": "Changement d'admin avec succès.", "leaveSuccessGroup": "<PERSON><PERSON> avez quitté le groupe avec succès.", "designateAdmin": "La désignation d'un nouveau admin est fait.", "successChangeQuitAdmin": "Changement d'administrateur effectué avec succès. Vous avez quitté le groupe en tant qu'utilisateur.", "newMessage": "Nouveau message", "SyncNewMessage": "Des modifications ont été apportées à ce canal...", "DeniedAccessChat": "Vous ne pouvez pas accéder au chat, contacter l'administrateur", "error_fetch_new_messages": "Erreur lors de l'obtention de nouveaux messages", "successDeleteGroup": "Ce groupe a été supprimé avec succès.", "missed_call": "<PERSON><PERSON> vocal manqué", "outgoing_call": "<PERSON><PERSON> vocal sortant ", "duration": "d'une durée de ", "incoming_call": "<PERSON><PERSON> vocal entrant ", "statusChanged": "Le statut a été modifié avec succès.", "task_created": "a créé une activité", "task_updated_stage": "a mis à jour l'étape de l'activité", "task_created_auth": "avez créé une activité", "task_updated_stage_auth": "avez mis à jour l'étape de l'activité", "selectReasonInDeal": "Veuillez sélectionner une raison de conclure cette offre", "deal_updated_stage": "l'étape a été mise à jour", "visio-created": "a créé une réunion vidéo", "visio-created-auth": " avez créé une réunion vidéo"}, "room": {"error_send_message403": " Vous n'êtes pas autorisé à envoyer des messages dans ce canal.", "description_under_title_room": " C'est le début de l'historique du canal", "description_under_title_user": "C'est le début de l'historique du salon privé avec", "confirmAbandonment": "Confirmer l'abandon", "quitGroup": "Êtes-vous sûr de quitter ce groupe ?", "confirmDelete": "Confirmer la <PERSON>", "deleteGroup": "Êtes-vous sûr de supprimer ce groupe ?", "chooseImage": "Choi<PERSON> une image"}, "message_type": {"file": "<PERSON><PERSON><PERSON>", "image": "image", "voice": "message vocal", "message_from_bot": "Message de bot {{name}}", "message_visio_conf": "{{name}} organiser une réunion vidéo", "message_deleted": "Message supprimé", "message_deleted_room": "{{name}} a supprimé le message"}, "external_drawer": {"image": "{{name}} a envoyé une image", "file": "{{name}} a envoyé un file", "voice": "{{name}} a envoyé un message vocal", "mixed_image": "{{name}} a envoyé une image et un texte", "mixed_file": "{{name}} a envoyé un fichier et un texte"}, "notification": {"privateMessage": "Message privé depuis", "commentTask": "Nouveau commentaire", "inTask": " dans la tâche ", "inModule": " dans le module {{module<PERSON>ame}} ", "publicMessage": "Message depuis", "titleNewMessage": "Vous avez un nouveau message", "sendBy": " Envoyé par : "}, "bot": {"code": "Code", "description": "Vous pouvez utiliser le code ci-dessous pour intégrer l'API d'envoi de messages comme notification dans votre application.", "dev": "Webhook", "create": " <PERSON><PERSON><PERSON> un bot", "edit": " Modifier le bot", "activate_bot": "<PERSON><PERSON> le bot", "active": "Actif", "deactive": "Inactif", "deactivate_bot": "Dés<PERSON>r le bot", "activate_bot_description": "Êtes-vous sûr de vouloir activer ce bot ?", "deactivate_bot_description": "Êtes-vous sûr de vouloir désactiver ce robot ? Le jeton API sera <b>perdu</b> et l'API sera <b>arrê<PERSON><PERSON>.</b>", "name": "Nom", "namePlaceholder": "<PERSON><PERSON> le nom du bot", "acceptedOnlyImageType": " Seuls les fichiers de type image sont acceptés (png, jpg, jpeg) ", "image": "Logo", "deleteImage": "Supprimer l'image", "uploadImage": "Cliquez ou faites glisser le fichier dans cette zone pour le télécharger", "requiredName": "Le nom du bot est requis", "minName": "Le nom du bot doit contenir au moins 3 caractères", "requiredLogo": " Le logo du bot est requise", "file_min_size": " La taille du fichier doit être inférieur à 150 Ko", "successCreateBot": " Le bot a été créé avec succès", "successUpdateBot": " Le bot a été modifié avec succès", "successStatusBot": " Le statut du bot a été modifié avec succès", "apiSendSuccess": "La demande a été envoyée avec succès", "empty": " Aucun bot n'a été créé", "selectLang": "Sélectionner la langue", "copy": "<PERSON><PERSON><PERSON>", "copied": "<PERSON><PERSON><PERSON>", "testApi": "Envoyer une demande de test", "apiTokenNotFound": "Le jeton n'est pas trouvé", "detail": {"paragraph": "Les Webhooks sont un moyen facile d'obtenir des messages automatiques et des données envoyées à un groupe en utilisant la magie des scripts générés.", "list1": "C<PERSON>z sur «créer un bot»", "list2": "Renseignez les informations sur le formulaire de création et validez", "list3": "Copier le script d'intégration généré et placez-le dans le script de votre application tierce"}}, "typing": " en train d'écrire...", "placeholderInput": "Ecrivez votre message...", "others": "et d'autres", "userstyping": "sont en train d'écrire...", "respond": "a répondu: ", "audio": "message vocal", "deleted": "message supprimé", "informations": "Informations", "mute": "Mode silencieux", "valid&quit": "Val<PERSON> et quitter", "quitGroup": "<PERSON><PERSON><PERSON> le groupe", "removeFromList": "<PERSON><PERSON><PERSON> de la liste", "messagePreview": "Aperçu Message", "sortBy": "Trier par", "AZ": "A-Z", "recents": "<PERSON><PERSON><PERSON><PERSON>", "UnReadMessage": "Non lus", "display": "Affichage par", "members": "Me<PERSON><PERSON>", "groups": "Groupes", "guests": "Invités", "chat_group": "groupe", "chat_member": "membre", "ok": "OK", "add": "Ajouter", "create": "<PERSON><PERSON><PERSON>", "deleteGroup": "Supprimer le groupe", "connected": "Connecté(s)", "userInfos": "Informations Utilisateur", "username": "Nom d'utilisateur", "email": "Email", "telephone": "N° de poste", "stopSearch": "Retour à la discussion initiale", "itsAll": "C'est tout, rien de plus 🤐", "medias": "<PERSON><PERSON><PERSON><PERSON>", "documents": "Documents", "links": "<PERSON><PERSON>", "link": "lien", "infos": "Informations", "emptyPlaceholder": "<PERSON>rlez afin que je puisse vous voir.", "create_group_by_auth": "Vous avez créé ce groupe  ", "create_group": " Vous avez été ajouté ce groupe  ", "new": "Nouveau", "decrypt": "Décryptage des messages", "admin": "Admin", "reacted": "a réagi", "onMessage": "sur : ", "starredMessages": "Messages Favoris", "pinnedMessages": "Messages Épinglés", "goto": "<PERSON><PERSON>", "openHere": "<PERSON><PERSON><PERSON><PERSON><PERSON> ici", "you": "Vous", "name": "Nom", "thread": "<PERSON><PERSON><PERSON>", "back": "Retour", "archiveList": "Archivées", "successfullyArchived": "Discussion archivée avec succès.", "successfullyRestored": "Discussion restorée avec succès.", "archiveModalTitle": "Confirmer l'archivage de cette discussion", "archiveModalBody": "Êtes-vous sûr de vouloir archiver cette discussion ?", "validate&archive": "Valider et archiver", "archiveRestoreModalTitle": "Confirmer la restauration de cette discussion", "archiveRestoreModalBody": "Êtes-vous sûr de vouloir restaurer cette discussion ?", "validate&restore": "Valider et restaurer", "restoreDiscussion": "<PERSON><PERSON><PERSON>", "archiveDiscussion": "Archiver", "unmute": "<PERSON><PERSON><PERSON><PERSON> le son", "muteModalTitle": "Confirmer la mise en sourdine de cette discussion", "muteModalBody": "Êtes-vous sûr de vouloir mettre en sourdine cette discussion ?", "unmuteModalTitle": "Confirmer le rétablissement du son de cette discussion", "unmuteModalBody": "Êtes-vous sûr de vouloir rétablir le son de cette discussion ?", "validate": "valider", "listStarredGlobal": "  <PERSON><PERSON><PERSON>", "listPinnedGlobal": "  <PERSON><PERSON><PERSON><PERSON>", "listSavedMessage": " Les messages enregistrés", "all": "TOUS"}, "visio": {"room_name": "Nom de la salle", "waitModerator": "Veuillez attendre que le modérateur rejoigne la réunion...", "moderatorJoin": "Le modérateur a rejoint la réunion", "searchInfo": "Rechercher par toutes les colonnes (sauf Date de début/Date de fin) et le nombre de KPIs (indicateurs clés de performance) ne dépend pas de votre recherche.", "addVisio": "<PERSON><PERSON><PERSON>", "createNewVisio": "Créer une nouvelle réunion", "visioToTask": "Transformer en activité", "plan": "Planifier", "start": "Commencer", "url": "Url", "password": "Code secret", "participate": "Participer", "participateMeet": "Participer à la réunion", "loginInformation": "Informations de connexion    ", "linkMeeting": "Lien de la réunion    ", "organizer": "Organisateur", "guest": "Invité{{plural}}", "waiting": "en attente{{plural}}", "yes": "O<PERSON>", "myMeetings": "<PERSON><PERSON>", "editInCalendar": "Modifier la réunion", "deleteMeeting": "Supprimer la réunion", "newMeeting": "Lancer une réunion", "today": "<PERSON><PERSON><PERSON>'hui", "upComing": "Prochains jours", "history": "Historique", "refresh": "Actualiser", "endDate": "Termine", "startDate": "Commence", "colleague": "Collègue{{plural}}", "contact": "Contact{{plural}}", "company": "Entreprise{{plural}}", "textCreator": "Activité a été créée", "creator": "<PERSON><PERSON><PERSON><PERSON>", "searchGuests&followers": "Rechercher des suiveurs ou des invités", "noVisioToday": "Pas de visoConf prévue aujourd'hui", "noVisioLater": "Pas de visoConf prévue pour l'avenir", "noVisioHistory": "Pas de visoConf prévue pour l'histoire", "linkClicked": "Le lien a été copié", "this_week": "<PERSON><PERSON> se<PERSON>", "upcoming": "A venir", "tomorrow": "<PERSON><PERSON><PERSON>", "followVisioLinkClipboard": "Pour participer à la visioconférence, suivez ce lien", "followVisioInPhoneClipboardPartOne": "Pour participer par téléphone, composez le", "followVisioInPhoneClipboardPartTwo": "et saisissez ce code", "morePhoneClipboard": "Voici d'autre numéro de téléphone", "callOn": "<PERSON><PERSON><PERSON> le", "infoCnx": "Via téléphone", "infoCnxTitle": "Vos informations de connexion", "visioInfoText": "Envoyez ce lien aux personnes que vous souhaitez inviter à la réunion. Veillez à enregistrer leurs informations de contact pour pouvoir les réutiliser ultérieurement.", "canceledVisio": "<PERSON><PERSON><PERSON>", "startInstantMeeting": "Dé<PERSON>rer une réunion instantanée", "visioMeet": "réunions vidéo", "recordedDownloadTitle": "Video enregistrée", "recordedVideoTitle": "Video enregistrée", "roomClosed": "La visio-conférence n'a pas encore commencé.", "roomExpired": "La visio-conférence est terminée. A bientot.", "meetStartsIn": "La visio-conférence commence dans ", "CreateVisioBtn": "<PERSON><PERSON>er une Visio"}, "checklist": {"label": "Nom", "tasktype_id": "Type d'activité", "duration": "<PERSON><PERSON><PERSON>", "listChecklist": "Liste de contrôle", "stepsChecklist": "Étape de la liste de contrôle"}, "login": {"login": " Connexion", "Title1": "Bienvenue", "username": "Nom d'utilisateur", "usernameRequired": "Veuillez saisir votre nom d'utilisateur !", "usernameEmail": "L'entrée n'est pas un e-mail valide !", "password": "Mot de passe", "passwordRequired": "Veuillez saisir votre mot de passe !", "back": "Retour", "reset_password": {"verifToken": "Vérification du jeton ...", "invitationExpired": "Votre invitation a expiré, merci de  contacter l'administrateur.", "resend_code": " <PERSON><PERSON> pouvez renvoyer le code après ", "wrongLink": " <PERSON><PERSON><PERSON><PERSON>, le lien que vous avez suivi est invalide ou a expiré. Veuillez réessayer. ", "button": "Confirmer le mot de passe  ", "passwordMatch": " Les mots de passe ne correspondent pas ", "passwordRequired": " Mot de passe est requis ", "minLength": "Le mot de passe doit contenir au moins 8 caractères.", "uppercase": "Le mot de passe doit contenir au moins une lettre majuscule.", "number": "Le mot de passe doit contenir au moins un chiffre.", "specialChar": "Le mot de passe doit contenir au moins un caractère spécial.", "passwordPattern": "Le format du mot de passe est incorrect.", "password": "  Nouveau mot de passe", "confirmPassword": " Confirmer le mot de passe", "passwordLength": " Le mot de passe doit contenir au moins 8 caractères ", "title": " Création de mot de passe", "subtitle": " Choisissez un mot de passe sécurisé pour protéger votre compte. Assurez-vous qu'il soit unique et difficile à deviner.", "success_password_Set": " Votre mot de passe a été défini avec succès."}}, "notifications": {"senddesktopnotifications": "Envoyer des notifications de bureau :", "forallactivities": "Pour toutes les activités", "onlyforpersonalmentionsandmessages": "Uniquement pour les mentions et messages personnels", "never": "<PERSON><PERSON>", "notificationsound": "Son de notification:", "on": "Activé", "off": "Désactivé", "notificationUpdated": "Notification Successfully Updated", "currentUserCreatesActivity": "Vous avez créé cette activité", "currentUserUpdatesActivity": "Vous avez mis à jour cette activité", "currentUserDeletesActivity": "Vous avez supprimé cette activité", "currentUserUpdatesDateActivity": "Vous avez changé l'horaire de cette activité", "currentUserUpdatesLabel": "Vous avez changé le libellé de cette activité à <strong>{{new<PERSON><PERSON><PERSON>}}</strong>", "currentUserAddsFiles": "V<PERSON> avez ajouté des fichiers à cette activité", "currentUserRemovesFiles": "Vous avez retiré des fichiers de cette activité", "currentUserAddsNewMemberActivity": "V<PERSON> avez a<PERSON> <strong>{{newMember}}</strong> dans cette activité", "currentUserUpdatesPriority": "Vous avez changé la priorité de cette activité de <strong>{{oldPriority}}</strong> à <strong>{{newPriority}}</strong>", "currentUserUpdatesStage": "Vous avez changé l'étape de cette activité de <strong>{{oldStage}}</strong> à <strong>{{newStage}}</strong>", "currentUserUpdatesType": "Vous avez changé le type de cette activité de <strong>{{oldType}}</strong> à <strong>{{newType}}</strong>", "currentUserUpdatesElement": "V<PERSON> avez assigné cette activité à l'élément <strong>{{element}}</strong> du module <strong>{{module}}</strong>", "currentUserUpdatesDatesActivity": "Vous avez changé les dates de cette activité", "currentUserUpdatesReminder": "Vous avez changé le rappel de cette activité", "currentUserUpdatesDescription": "Vous avez changé la description de cette activité", "currentUserUpdatesNote": "Vous avez changé la note de cette activité", "imagesInKanban": "Image{{s}}", "currentUserRemovesMember": "V<PERSON> avez retiré <strong>{{removedUser}}</strong> de l'activité <strong>{{activity<PERSON>abe<PERSON>}}</strong>", "userRemovesMember": "{{user}} a retiré {{removedUser}} de l'activité {{activity<PERSON>abe<PERSON>}}", "currentUserAddsMemberWithRole": "V<PERSON> a<PERSON> a<PERSON> <strong>{{addedUser}}</strong> comme {{role}} dans l'activité <strong>{{activity<PERSON><PERSON><PERSON>}}</strong>", "currentUserRemovesMemberWithRole": "V<PERSON> avez retiré <strong>{{removedUser}}</strong> ({{role}}) de l'activité <strong>{{activity<PERSON><PERSON><PERSON>}}</strong>", "userAddsMemberWithRole": "<strong>{{user}}</strong> a ajouté <strong>{{addedUser}}</strong> comme {{role}} dans l'activité <strong>{{activityLabel}}</strong>", "userRemovesMemberWithRole": "<strong>{{user}}</strong> a retiré <strong>{{removedUser}}</strong> ({{role}}) de l'activité <strong>{{activityLabel}}</strong>"}, "webphone": {"errorGetData": "Une erreur est survenue lors de la récupération des données de paramètres pour votre webPhone!", "notifReceiveCallTitle": "Appel entrant de:", "reconnect": "Reconnexion...", "error": "Webphone déconnecté. Tentatives de reconnexions lancées ...", "mustAllowMicrophonePermission": "<PERSON><PERSON> de<PERSON> autoriser l'accès au microphone."}, "unavailability": {"start_date": "Date de début", "end_date": "Date de fin", "nature": "Nature", "description": "Description", "Leave": "<PERSON><PERSON>", "Authorization": "Autorisation", "Other": "<PERSON><PERSON>", "otherDesc": "Par exemple : D<PERSON>placement, Réunion, Pause déjeuner ...", "Youarenotallowedtorequestoffourhoursofauthorizationspermonth": "Vous n'êtes pas autorisé à demander quatre heures d'autorisations par mois.", "checkthetimeslotforunavailability.": "Vérifiez que le créneau horaire n'est pas indisponible.", "Theminimumdurationofleaveis4hours,pleaseverifytheselectedtimeslot.": "La durée minimale du congé est de 4 heures, veuillez vérifier le créneau horaire sélectionné.", "Youhaveexceedtheallowednumberofdays!": "Vous avez dépassé le nombre de jours autorisés !"}, "profilemenu": {"profile": "Mon profil", "support": "Support", "documentation": "Documentation", "logout": "Se Déconnecter", "signedAs": "Connecté en tant que: ", "Tenancy": "Tenant: ", "status": "Statut", "online": "En ligne", "offline": "<PERSON><PERSON> ligne", "away": "Absent", "busy": "<PERSON><PERSON><PERSON><PERSON>", "custom": "<PERSON><PERSON><PERSON><PERSON>", "addAccessToken": "Ajouter un jeton d'accès", "expirationDate": "Date d'expiration", "lastUsed": "Dernière utilisation", "lastViewAccessToken": "Voici votre nouveau jeton d'accès personnel. C'est la seule fois qu'il apparaîtra, alors ne le perdez pas ! Vous pouvez maintenant utiliser ce jeton pour effectuer des demandes d'API.", "pat": "Jeton d'accès personnel", "accessToken": "<PERSON>on d'a<PERSON>ès", "expiredDate": "La date d'expiration a expiré", "infoTenant": "Infos du tenant", "chatStorage": "Stockage des Discussions", "available": "Disponible", "storageUsage": "Utilisation de stockage", "appMobile": "Application mobile", "paragraphAppMobile": "Une solution de communication sécurisée qui intègre messagerie avancée et téléphonie professionnelle pour faciliter les échanges en équipe avec des fonctionnalités complètes et une utilisation fluide.", "scanCode": "Scanner Qr code pour télécharger l'application", "fillFieldsAppMobile": "Utilisez vos paramètres pour vous connecter", "aboutTheApplication": "À propos de l'application", "usePassword": "Veuillez utiliser votre mot de passe pour vous connecter.", "EnterTenancy": "Saisir l'URL de tenant", "loginqrCode": "Se connecter avec QR code", "loginEmailPassword": "Se connecter avec email et mot de passe", "generateCodeQr": "Générer un code QR", "scanCodeAndConnect": "Scannez pour vous connecter à l'application", "ReceiveQrCode": "Cliquez ici pour recevoir un nouveau code QR", "notificationMailing": "Les notifications sont gérées par les créateurs des comptes email", "notificationEnabledStatus": "Les notifications sont activées sur", "notificationDisabledStatus": "Les notifications sont désactivées sur"}, "dashboard": {"welcome": "Bienvenue", "unreadmsgOtoO": " Privée{{plural}}", "unreadArchivedMessages": "Archivée{{plural}}", "unreadmsgtoG": " Groupes", "unreadEmail": " E-mail non lu{{plural}}", "missedCalls": " Manqué{{plural}} ", "receivedCalls": " Reçu{{plural}} ", "outgoingCalls": " Sortant{{plural}} ", "visitor": " Nouveau{{pluriel}} visiteur{{plural}}", "newConversations": " Nouvelle{{plural}} conversation{{plural}}", "newMessages": " Nouveau{{pluriel}} message{{plural}} du jour", "todoToday": "À faire aujourd'hui", "goToModuleTask": "Voir toutes les activités", "youhave": "<PERSON><PERSON> avez", "postNumber": "Poste", "phoneNumber": "Numéro de télephone", "checkTasks": "Vérifiez vos activités actuelles et à venir ici", "checkTasksDesc": "Vérifiez les activités que vous faites actuellement et les autres activités à venir", "viewCalendar": "Voir le calendrier", "goToCalls": "Accéder au journal des appels", "unreturnedMissedCalls": " Non retourné{{plural}}", "unreadMention": "Mentionnée{{plural}}", "independantNumber": "Les chiffres affichés ne dépendent pas du filtre de date.", "independantFilter": "Les chiffres affichés ne dépendent pas du filtre de {{name}}.", "unreadEmailReceived": "Non lu{{plural}} ", "unreadEmailSent": "Email{{plural}} envoyé{{plural}} non lu{{plural}}", "totalEmailSent": "Envoyé{{plural}}", "statsCall": "Les chiffres concernent l'appel", "statsMsgs": "Les chiffres concernent les messages", "unreadPerSession": "Non lu par session", "socialMedia": "Réseaux sociaux", "wa": "WhatsApp", "fb": "<PERSON>", "ig": "Instagram", "chat": "Live Chat", "relativeFigures": "Chi<PERSON>res relatifs à toutes les conversations, indépendamment des départements.", "queue": "File d'attente", "lostNotRecalled": "Manqué{{plural}} rappelé{{plural}}", "recalled": "Rappelé{{plural}}", "notRecalled": "Non rappelé{{plural}}", "answered": "Décroché{{plural}}", "unreadDiscussions": "Discussions non lues", "createdByYou": "<PERSON><PERSON><PERSON>{{plural}} par moi", "totalElements": "Élément{{plural}}", "percentage": "Pourcentage", "sharedAccounts": "Seulement les boîtes partagées", "tenancy": "Tenant", "ticket_by_stage": "Nombre de tickets par étape", "ticket_by_perimiter": "Nombre de tickets par périmètre", "ticket_by_type": "Nombre de tickets par type", "ticket_by_priority": "Nombre de tickets par priorité", "ticket_by_product": "Nombre de tickets par produit", "ticket_by_channel": "Nombre de tickets par canal", "moduleBySource": "Nombre de {{name}} by source", "moduleByType": "Nombre de {{name}} by type", "exportCsv": "Exporter en CSV", "accountEmail": "un compte email", "nextTasksTypes": "Types d'activités suivantes", "prevTasksTypes": "Types d'activités précédentes", "numberElementsPerModule": "Nombre d'éléments par module", "activities": "des activités", "leadsNotConverted": "Pistes non converties en contacts", "Contact_non_guest": "Contacts non invités", "successfulConversions": "Conversions réussies", "successfulInvitations": "Invitations réussies"}, "voip": {"unknown": "Inconnu", "callMade": "<PERSON><PERSON>, sonn<PERSON>", "callFailed": "<PERSON><PERSON>", "callReceived": "<PERSON><PERSON>, sonn<PERSON>", "missedCall": "<PERSON><PERSON>, sonn<PERSON>", "failedCall": "<PERSON><PERSON>", "callMadeAnswered": "<PERSON><PERSON>, sonn<PERSON>", "callMadeNotAnswered": "Appel émis non répondu, sonn<PERSON>", "callMadeFailed": "Échec de l'appel émis", "callReceivedAnswered": "<PERSON><PERSON> reçu <PERSON>, sonn<PERSON>", "callReceivedNotAnswered": "<PERSON><PERSON>, sonn<PERSON>", "callReceivedFailed": "Échec de l'appel reçu", "addCompany": "Ajouter une entreprise", "addContact": "Ajouter un contact", "addLead": "Ajouter un piste", "onTheNum": "sur le N°", "transferred": "ren<PERSON><PERSON> à", "issued": "<PERSON><PERSON>", "receipts": "<PERSON><PERSON><PERSON>", "missed": "Manq<PERSON>s", "outgoing_missed": "Non répondu", "failed": "<PERSON><PERSON><PERSON><PERSON>", "answered": "Répondu", "busy": "<PERSON><PERSON><PERSON><PERSON>", "callMissed": "<PERSON><PERSON> manq<PERSON>", "apply": "Appliquer", "displayTime": "Afficher l'heure", "yesterday": "<PERSON>er", "today": "<PERSON><PERSON><PERSON>'hui", "today&yesterday": "Aujourd'hui et hier", "currentWeek": "<PERSON><PERSON><PERSON> en cours", "currentMonth": "Mois en cours", "last7days": "7 derniers jours", "last14days": "14 derniers jour", "last30days": "30 derniers jours", "last90days": "90 derniers jours", "audioRecording": "Enregistrement audio", "all": "Tous", "unread": "Non lus", "filterCallByType": "Filtrer les appels par type", "search3chart": "Saisissez au moins 3 caractères pour rechercher...", "log_search": "Nom, poste ou numéro de téléphone", "group_queue_search": "Recherche par nom ou par numéro", "colleagueNum": "Nombre de collègues", "moreInfo": "Plus d'infos", "moreInfoWith": "Plus d'infos sur", "clickToDisplayInfo": "Cliquez sur l'élément pour afficher des informations.", "clickToSendMail": "Cliquer pour envoyer un E-Mail", "clickToCall": "Cliquer pour appeler", "contactNum": "Numbre de contacts", "of": "sur", "call": "<PERSON><PERSON><PERSON>", "chat": "Cha<PERSON>", "chatWith": "Chat avec", "videoCall": "Appel video", "family": "<PERSON><PERSON><PERSON>", "companies": "Organisations", "contacts": "Contacts", "colleagues": "<PERSON><PERSON>è<PERSON>", "groups_queues": "Groupes & File d'attente", "notInContacts": "Pas dans vos contacts", "tagDelete": "Tags supprimés avec succès", "editTag": "Éditer la qualification", "areYouSureToDelete": "Êtes-vous sûr de supprimer", "thoseTags": "ces tags", "thisTag": "cette tag", "tagIsRequired": "Le champ '{{x}}' est obligatoire!", "qualification": "Qualification", "writeNote": "Rédigez votre note...", "save": "<PERSON><PERSON><PERSON><PERSON>", "addTag": "Ajouter tag", "processing": "Traitement", "inputMessage": "Recherche collègues & contacts & journal", "renvoi-vers-poste": "Poste interne", "renvoi-vers-numero": "Numéro de téléphone", "renvoi-vers-boite": "Boite Vocale", "renvoi-sonneries": "Sonneries", "renvoi-vers": "Vers", "renvoi-vers-boite-message": "Vers votre boite vocale", "renvoi-title": "Renvoi d'appel", "renvoi-button": "Confirmer", "renvoi-appel": "Renvoi d'appel", "ringing-output-device": "Sonnerie sur le périphérique de sortie", "renvoi-appel-message": "Pas de renvoi", "renvoi-title-page": "Paramètres", "renvoi-conditionnel": "Renvoi conditionnel", "searchLogs": "Chercher par nom ou numéro", "qualifyTheCall": "Qualifier l'appel", "or": "Ou", "createTask": "Créer une Activité", "updateQualify": "Mettre à jour la qualification de l'appel", "successUpdateQualify": "La qualification d'appel avec <strong>{{name}}</strong> est mise à jour avec succès", "successCreateQualify": "La qualification d'appel avec <strong>{{name}}</strong> est créée avec succès", "save_createTask": "Enregistrer & c<PERSON>er {{nbr}} activité{{s}}", "audioCall": "Appel audio", "VideoCall": "<PERSON><PERSON> vidéo", "with": "avec", "sonnerie": "Son<PERSON><PERSON>", "pauseAppel": "Appel en pause", "coursAppel": "Appel en cours", "connectingInProgress": "Connexion en cours", "switchTo": "Passer à", "conf": "Conférence audio", "nouvelAppel": "Nouvel appel", "pauseBut": "Pause", "playBut": "Reprendre", "silence": "Silence", "transfert": "Transfert", "conférence": "Confé<PERSON>ce", "taskCall": "Créer une activité", "qualif": "Qualifier cet appel", "recalled_today_answered": "<PERSON><PERSON><PERSON> le même jour et décroché", "recalled_answered": "Rappelé et décroché", "recalled_not_answered": "Rappelé et non décroché", "not_recalled": "Non rappelé", "yesterday_at": "<PERSON><PERSON> <PERSON>", "at": "à", "moreHistory": "Cliquez ici pour plus de détails", "directory": "Répertoire", "numPhoneNotFoundRmc": "Ce contact n'a pas de numéro de téléphone", "add": "Ajouter", "addToModule": "Crée un(e)", "invalidPhoneFormat": "Format de téléphone non valide ! Veuillez saisir un format de téléphone valide.", "openChat": "Vous ne pouvez pas ouvrir plus d'une discussion", "alreadyInChat": "Vous êtes déjà dans le chat!", "call_qualification": "Qualification d'appel", "ask_before": "<PERSON><PERSON><PERSON> avant", "never": "<PERSON><PERSON>", "always": "Toujours", "internalExtension": "Extension interne", "yourPhoneNum": "Mon numéro de téléphone", "yourVoicemail": "<PERSON> boîte vocale", "colleague": "<PERSON><PERSON><PERSON><PERSON>", "beep": "Bip", "beeps": "Bips", "yes": "O<PERSON>", "no": "Non", "userDoNotHavePhoneNum": "Vous n'avez pas de numéro de téléphone, vous devez en ajouter un pour choisir cette option. Pour ce faire, vous devez vous rendre dans votre profil et ajouter votre numéro de téléphone.", "after": "après", "to": "Vers", "delete": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "Annuler", "edit": "Modifier", "hintQualifSetting": "Il s'agit d'un formulaire qui s'affiche lorsque vous avez terminé l'appel et qui vous permet de qualifier l'appel et d'effectuer des activités.", "followUpAction": "Action de suivi", "suggested": "<PERSON><PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>", "fieldRequired": "Ce champ est obligatoire", "selectQualify": "Sélectionner la qualification", "assignCallTo": "Affecter cet appel à", "selectModule": "Sélectionner un module", "search_select": "Ch<PERSON><PERSON> et sélectionner un(e)", "assignment": "Affecter cet appel", "searchGroup": "Recherche par nom ou numéro de groupe", "alreadyMember": "Vous êtes déjà membre de ce groupe", "logAndSearch": "Journal & Recherche", "dialPad": "Clavier numérique", "hideDialPad": "Masquer", "treatedBy": "Traitée par ", "from": "De", "emptyContent": "Contenu vide", "phoneNumbers": "Numéros de téléphone", "treated": "Traité", "groupsOrQueues": "Groupes / files d'attente", "waitingTime": "Attente", "noAnswer": "Pas de réponse", "recalledBy": "Rappelé(e) par", "queues": "Files d'attente", "callGroup": "Groupe d'appel", "myPhoneNumLog": "Mon n° de tél.", "hisPhoneNumLog": "Son n° de tél.", "afterCallForwarding": "Suite à un renvoi", "afterCallTransfer": "Suite à un transfer", "afterCallForwardingToVoicemail": "Suite à un renvoi vers la boite vocale", "forwarding": "Renvoi", "transfer": "<PERSON><PERSON><PERSON><PERSON>", "me": "<PERSON><PERSON>", "group": "Groupe d'appel", "groups": "Groupes d'appels", "queue": "File d'attente", "iAmMember": "Je suis membre", "view360": "<PERSON><PERSON> sphère", "sort": "<PERSON><PERSON>", "alphabetically": "Par ordre alphabétique", "byModules": "Par modules", "firstTime": "Première fois", "exportLog": "Exporter le journal", "fullScreen": "Vue plein écran", "alreadyInView360": "Vous êtes déjà dans une interface incluant la vue sphère. Souhaitez-vous quitter cette page pour ouvrir une autre vue de la sphère?", "selectGroupOrQueue": "Sélectionner un groupe ou une file d'attente", "missed_today_call": "Manq<PERSON>s", "total_today": "Total", "received_today_call": "<PERSON><PERSON><PERSON>", "outgoing_today_call": "<PERSON><PERSON>", "missed_calls_not_returned_today": "Manqués non rappelés", "answered_calls": "<PERSON><PERSON><PERSON>", "no_answered_calls": "Appels non traités", "moyen_ringing_calls": "Du<PERSON>e moyenne d'attente", "moyen_treatement_calls": "Durée moyenne de communication", "DMS": "DMAT", "DMC": "DMC", "associate": "Associer", "disassociate": "Dissocier", "associateTo": "Associer à", "removeAssociation": "Supprimer l'association", "closeCurrentDrawerMsg": "Veuillez fermer le panneau ouvert pour pouvoir ouvrir la remontée de fiche de <strong>{{name}}</strong>", "closeCurrentDrawerToCreate": "Veuillez fermer le panneau ouvert pour pouvoir ouvrir le panneau créer", "callOn": "<PERSON><PERSON><PERSON> {{name}} sur", "searchDirectory": "Recherche par nom ou par numéro", "successUpdateQualifySetting": "Qualification a été mis à jour avec succès.", "successUpdateOutputDeviceSetting": "Périphérique de sortie a été mis à jour avec succès.", "successDeleteForwarding": "Renvoi d'appel supprimé avec succès.", "successUpdateForwarding": "Renvoi d'appel a été mis à jour avec succès.", "taskInfo": "Informations sur l'activité", "infoInputWebPhone": "Le nombre d'éléments affichés est limité. Pour une recherche exhaustive, veuil<PERSON><PERSON> consulter le répertoire complet.", "notTheOwner": "Vous n'êtes pas le créateur de cet élément, vous ne pouvez donc pas le supprimer.", "referral": "Renvoi", "updateTask": "Mise à jour de l'activité", "noteTagInfo": "Cette note est liée à la qualification de cet appel et sera affichée dans le journal des appels.", "qualifNote": "Note de la qualification", "noteTaskInfo": "Cette note sera liée à la note de l'activité <strong>{{taskName}}</strong>", "qualificationInfo": "Sélectionner une qualification avec un point rouge enregistrera une qualification d'appel et créera une activité portant le nom de la qualification avec la date de fin choisie.", "remove": "<PERSON><PERSON><PERSON>", "qualifContainsTask": "Cette qualification contient une tâche", "minimizeDrawerChat": "Minimiser [Echap]", "msgWhenDisplayChatSidebarIsOpen": "Vous ne pouvez pas afficher la liste de discussion parce que vous êtes déjà dans une vue qui inclut le chat ou que la fenêtre pour le nouvel e-mail est ouverte !", "errorRetrievingLogData": "Une erreur est survenue lors de la récupération des données du journal d'appels!", "blind": "<PERSON><PERSON><PERSON>", "attended": "Accompag<PERSON><PERSON>", "backTo": "Retour à", "confirmTransfer": "<PERSON><PERSON><PERSON><PERSON> le <PERSON>t", "onlyMe": "<PERSON><PERSON> se<PERSON>", "departments": "Départements", "sharedWith": "Partagé avec", "errorSharedWith": "Veuillez sélectionner au moins un département avec lequel partager!", "selectDepartments": "Sélectionner les départements", "noCallLogFound": "Aucun historique d'appels. Vous n'avez pas passé ou reçu d'appels.", "noVoicemailFound": "Aucun historique de messagerie vocale. Vous n'avez pas reçu de messages vocaux.", "sendMsgWhenRinging": "Envoyer un message", "createNew": "<PERSON><PERSON><PERSON>", "infoCallInProcess": "<strong>{{member}}</strong> est en train de traiter l'appel de <strong>{{srcSource}}</strong> sur <strong>{{dstSource}}</strong>.", "callInProcess": "Appel en cours", "inProcess": "En cours de traitement", "livePanel": "Panneau live"}, "profile": {"forgotPwd": "Mot de passe oublié", "pwdUpdateSuccess": "Mot de passe mis à jour avec succès", "yourNewPwd": "Votre nouveau mot de passe", "thisNotifWillDisappear": "Cette notification disparaîtra dans {{x}} secondes.", "theOldPwdIncorrect": "L'ancien mot de passe est incorrect", "resendInXSec": "Renvoi dans {{x}} secondes", "anEmailHasBeenSent": "Un courriel a été envoyé avec succès à votre boîte de réception à l'adresse", "editPwd": "Modifier le mot de passe", "oldPwd": "Ancien mot de passe", "msgForgotPwd": "En cliquant ici, une URL sera envoyée à votre e-mail pour vous rediriger vers une interface où vous pourrez changer votre mot de passe.", "fieldRequired": "Ce champ est obligatoire", "confirmNewPwd": "Confirmer le nouveau mot de passe", "min8chart": "Minimum huit (8) caractères !", "passwordMatch": "Les deux mots de passe que vous avez saisis ne correspondent pas !", "confirm": "Confirmer", "cancel": "Annuler", "newPwd": "Nouveau mot de passe", "profile": "Profil", "password": "Mot de passe", "edit": "Editer", "invalidPhoneFormat": "Format de téléphone invalide ! Veuillez entrer un format de téléphone valide.", "rmcAccess": "Accès au RMC", "sharedEmail": "<PERSON><PERSON><PERSON>(s) <PERSON><PERSON>(s)", "personalEmail": "Compte(s) Email personnel(s)"}, "contacts": {"cannotDelete": "Vous ne pouvez pas effectuer cette action car <strong>{{label}}</strong> est déjà utilisé dans <strong>{{errMsg}}</strong>!", "edit": "Editer", "delete": "<PERSON><PERSON><PERSON><PERSON>", "successDelete": "supprimé avec succès", "the": "le", "notAllowed": "Vous n'avez pas le droit de faire cette action!", "successCreate": "est créé avec succès", "errExistingMail": "<PERSON><PERSON> p<PERSON>", "createAndAddAnother": "Créer & Ajouter un autre", "createGoDetail": "Créer & Aller au vue sphere", "createAndInvite": "Créer & Inviter", "createAndInviteAndAddAnother": "Créer & Inviter & Ajouter un autre", "createAndInviteAndGoDetail": "Créer & Inviter & Aller au vue sphere", "cancel": "Annuler", "create": "<PERSON><PERSON><PERSON>", "goToXSetting": "Paramètres de champs de {{x}}", "fieldSetting": "Paramètre de champs", "createNewX": "Nouveau {{x}}", "createNewXf": "Nouvelle {{x}}", "successUpdate": "La mise à jour de {{x}} {{y}} a été effectuée avec succès.", "saveAndGoDetail": "Sauvegarder & Aller à la vue sphère", "save": "<PERSON><PERSON><PERSON><PERSON>", "validated": "<PERSON><PERSON><PERSON> (facultatif)", "upload": "Télécharger", "enterFileName": "<PERSON><PERSON> le nom du fichier...(facultatif)", "fieldXRequired": "Le champ [{{x}}] est obligatoire!", "enterValidEmail": "Veu<PERSON>z saisir une adresse e-mail valide !", "onlyNum": "Ce champ n'accepte que les numéros!", "validUrl": "Veuillez saisir une adresse URL valide!", "min10chat": "Minimum dix (10) caractères, au moins une lettre majuscule, une lettre minuscule et un chiffre!", "goBack": "<PERSON><PERSON><PERSON>", "showAll": "<PERSON><PERSON><PERSON><PERSON> tout", "column": "Colonne", "columns": "Colonnes", "showColumns": "Afficher les colonnes", "company": "Organisation", "contact": "Contact", "user": "<PERSON><PERSON><PERSON><PERSON>", "guest": "invité(e)", "product": "Produit", "collegues": "<PERSON><PERSON>è<PERSON>", "ticket": "Ticket", "project": "Projet", "companies": "Organisations", "contacts": "Contacts", "users": "Utilisateurs", "products": "Produits", "tickets": "Tickets", "projects": "Projets", "booking": "Réservation", "bookings": "Réservations", "leads": "<PERSON><PERSON><PERSON>", "lead": "<PERSON><PERSON>", "invoices": "Factures", "invoice": "Facture", "invite": "Inviter", "block": "Bloquer", "unblock": "Débloquer", "errorDelete": "Vous ne pouvez pas effectuer cette action car", "alreadyUsed": "déjà utilisé dans", "those": "ces", "is": "est", "are": "sont", "deleteConfirmMsg": "Êtes-vous sûr de vouloir effectuer cette action?", "successSendInvite": "Invitation envoyée avec succès ! Réception de l'e-mail différée.", "successSendInvitations": "Invitations envoyées avec succès ! Arrivée des e-mails différée.", "blockUser": "{{x}} est bloqué avec succès !", "blockUsers": "{{x}} utilisateur sont bloqué avec succès !", "unblockUser": "{{x}} est Débloqué(e) avec succès !", "unblockUsers": "{{x}} utilisateur sont Débloqué(e) avec succès !", "created": "<PERSON><PERSON><PERSON>(e)", "actif": "Actif", "invited": "Inviter", "re-invited": "Re-inviter", "blocked": "B<PERSON>q<PERSON>(e)", "unblocked": "Déb<PERSON>qué(e)", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "Mis à jour le", "createdBy": "C<PERSON><PERSON> par", "noEmojisAllowed": "Les émojis ne sont pas autorisés dans ce champ!", "linkInvitation": "Lien d'invitation", "linkExpired": "Ce lien d'invitation a expiré. Veuillez réinviter l'utilisateur pour générer un nouveau lien valide.", "errorMercure": "Erreur: [<strong>{{error}}</strong>] lors du traitement de votre action. Veuillez réessayer ou contacter le support si le problème persiste.", "successMercure": "Succès! L'{{element}} [<strong>{{name}}</strong>] a été <strong>{{action}}</strong> avec succès par <strong>{{admin_name}}</strong>", "linkInviteError": "<PERSON><PERSON><PERSON> chose s'est mal passé lors du traitement de votre action. Veuillez réessayer ou contacter le support si le problème persiste.", "thisWindowWillClose": "Cette fenêtre se fermera automatiquement dans {{timer}} secondes", "fieldFilledAuto": "Ce champ sera rempli automatiquement", "deleteAssociation": "Êtes-vous sûr de vouloir supprimer cette association?", "users_unblocked": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(e)", "users_blocked": "bloq<PERSON>(e)", "send_invitation": "invité(e)", "user_accept_invitation": "L'{{element}} [<strong>{{name}}</strong>] a accepté l'invitation.", "loadingDataTable": "Chargement des données, veuil<PERSON><PERSON> patienter", "total_elements": "Total", "created_by_you": "<PERSON><PERSON><PERSON> par moi", "associated_to_you": "As<PERSON><PERSON><PERSON> à moi", "total_active": "Total actif", "notPermissionToDelete": "Vous n'avez pas l'autorisation de supprimer cet/ces élément(s), (Vous n'êtes pas le créateur ou un administrateur!)", "btnCreateCompany": "Créer une organisation", "btnCreateContact": "<PERSON><PERSON><PERSON> un contact", "btnCreateDeal": "<PERSON><PERSON><PERSON> une offre", "btnCreateUser": "C<PERSON>er un utilisateur", "btnCreateProduct": "Créer un produit", "btnCreateTicket": "<PERSON><PERSON><PERSON> un ticket", "btnCreateProject": "Créer un projet", "btnCreateBooking": "C<PERSON>er une réservation", "btnCreateLead": "<PERSON><PERSON><PERSON> une piste", "btnCreateInvoice": "<PERSON><PERSON>er une facture", "btnCreateTransaction": "Créer une Transaction", "createdFileEvent": "<strong>{{creator_name}}</strong> a téléchargé un fichier dans le <strong>{{family_name}}</strong> <strong>{{element_name}}</strong>", "createdCommentEvent": "<strong>{{creator_name}}</strong> a commenté le <strong>{{family_name}}</strong> <strong>{{element_name}}</strong>", "updatedCommentEvent": "<strong>{{creator_name}}</strong> a mis à jour un commentaire sur le <strong>{{family_name}}</strong> <strong>{{element_name}}</strong>", "openInNewTab": "<PERSON><PERSON><PERSON><PERSON>r dans un nouvel onglet", "createdElement": "<strong>{{user}}</strong> a créé {{family}} [<strong>{{elementName}}</strong>]", "updatedElement": "<strong>{{user}}</strong> a mis à jour {{family}} [<strong>{{elementName}}</strong>]", "deletedElement": "<strong>{{user}}</strong> a supprimé {{family}} <strong>{{elementName}}</strong>", "deletedElements": "<strong>{{user}}</strong> a supprimé <strong>{{nbr}}</strong> <strong>{{family}}</strong>", "errLimitLicense": "Vous avez atteint la limite de licence. Veuillez effectuer une mise à niveau pour ajouter ou débloquer d'autres utilisateurs.", "notifProccessBeingProcess": "Votre demande est en cours de traitement. Cela peut prendre un certain temps. Vous serez informé(e) dès que le processus sera terminé.", "selectFolderDisabled": "Vous ne pouvez pas sélectionner de dossier lorsque des filtres sont actifs. Veuillez effacer les filtres pour continuer.", "selectPipelineDisabled": "Action désactivée tant que des filtres sont appliqués. Veuillez les désactiver pour continuer.", "selectKanbanViewDisabled": "Vous ne pouvez pas sélectionner une vue Kanban lorsque des filtres sont actifs. Veuillez effacer les filtres pour continuer.", "triggerDesc": "Trier par ordre décroissant", "triggerAsc": "Trier par ordre croissant", "cancelSort": "<PERSON><PERSON><PERSON> le tri", "inviteUserProcess1": "Votre demande est en cours de traitement. Cela peut prendre un certain temps.", "inviteUserProcess2": "Vous serez informé(e) dès que le processus sera terminé.", "inviteContacts": "Inviter {{name}} à être un invité", "emailTakenError": "Cet email est déjà utilisé", "enterEmailTitle": "Ce contact n'a pas d'adresse email.", "inviteNotAllowed": "Vous n'avez pas la permission de faire cette action !", "viewMore": "Afficher plus", "more": "Plus", "sendEmail": "Envoyer un email"}, "layout_profile_details": {"overview": "Vue d'ensemble", "activities": "Activités", "upcomingEvents": "Événements à venir", "recentInteractions": "Intéractions", "callTask": "Activité d'appel", "meetingTask": "Activité de réunion", "allTasks": "Toutes les activités", "files": "Fichiers", "upcoming": " À venir", "cart": "<PERSON><PERSON>"}, "vue360": {"createTask": "{{avoir}} c<PERSON>é cette activité ", "deType": "de type", "ascreatorandowner": "en tant que créateur et propriétaire.", "asGuest": "comme invités", "delete": "Suppression  d'un(e)", "and": "et", "asFollowers": "comme suiveurs", "isOwner": "le propriétaire.", "noInteractions": "Aucune interaction", "or": "où", "you": "Vous", "all": "Tous", "upcoming": "A venir", "overdue": "En retard", "completed": "Completé", "are": "étes", "is": "est", "titleCreation": "Création d'un(e)", "titleUpdate": "Mettre à jour d'un(e) ", "actionCreate": "a créé ", "actionUpdate": "a modifié ", "actionDelete": "a supprimé ", "Product": "Produit", "Deal": "Offre", "Helpdesk": "Ticket", "helpdesk": " Ticket", "ticket": " Ticket", "Ticket": " Ticket", "Project": "Projet", "Booking": "Réservation", "voip": "appel", "email": " Email", "call": "appel", "Leads": "<PERSON><PERSON>", "leads": " <PERSON><PERSON>", "task": "Activité", "Contact": "Contact", "Organisation": "Organisation", "User": "Utilisa<PERSON>ur", "actionUpdateFrom": "a mis à jour ", "from": "de", "to": "à", "by": "par", "noChat": "Aucune discussion", "ANSWERED": "Réponse à l'", "FAILED": "Échec d'", "BUSY": "Occupation de l'", "NO ANSWER": "Non-réponse à l'", "CONGESTION": "Encombrement de l'", "actionCall": "a appelé", "actionCallMe": "vous a appelé", "null": "", "empty": "vide", "withDuration": "avec du<PERSON>e", "numberModification": "{{number}} modification{{plural}} effectuées", "show": "<PERSON><PERSON><PERSON><PERSON> ", "hide": "Masquer ", "families": "Familles", "actionsDelete": "<PERSON><PERSON><PERSON><PERSON>", "actionsCreate": "<PERSON><PERSON><PERSON>", "actionsUpdate": "Mise à jour", "cardFilterTask": "Activités", "cardFilterCall": "<PERSON><PERSON><PERSON>", "transfer": "a transféré", "relaunch": "a relancé", "move": "a déplacé", "assign": "a pris en charge le", "affectation": "a affecté", "qualification": "a qualifié", "merge": "a fusionné", "return": "a remis le", "toTheDefaultFolder": " dans le dossier par défaut", "update_qualification": "a mis à jour la qualification de", "delete_qualification": "a supprimé la qualification de", "update_affectation": "a mis à jour l'affectation de", "delete_affectation": "a supprimé l'affectation de", "note": "Note", "Email": " Email", "association": "a associé", "delete_association": "a supprimé l'association avec", "copyLink": "Copier le lien", "subject": "Sujet", "level": "Niveau", "severity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "folders": "Dossier", "slas": "Sla", "associateWithElement": "Association à un élément (utilisateur, transaction, etc.)", "informationSheet": "Informations", "generalInteraction": "Interactions", "channel": "Canal", "cannotAccessChat": "Vous n'avez pas l'accès pour accéder à la discussion.", "multipleRelations": "<PERSON><PERSON> pouvez établir plusieurs associations", "oneRelation": "<PERSON><PERSON> pouvez établir une seule association", "noInfoGeneral": "Il faut sélectionner un champ et cliquer sur Modifier, puis choisir Vue 360 dans le mode d'affichage", "todoList": "Liste de tâches", "convertedToContact": " est converti en contact", "noAssociationsAvailable": "Aucune association disponible", "history": "Historique", "replaceBy": "Voulez-vous remplacer {{oldValue}} par {{newValue}} ?", "noEmail": "Pas d'adresse e-mail", "NotAuthTodeleteAssoc": "Cliquez ici pour sélectionner {{module}} dans Association et dissocier {{label}}", "roomChat": "Sallon de discussion ", "privateChat": "Conversation privée avec ", "readMore": "Lire la suite", "readLess": "<PERSON><PERSON> moins", "convertLeadToCtc": "Voulez-vous convertir cette piste en contact?", "reopen": " <PERSON><PERSON><PERSON><PERSON><PERSON>", "closedTicket": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reference": "Référence", "closeThisTicket?": "Êtes-vous sûr de vouloir clôturer ce ticket?", "reopenThisTicket?": "Êtes-vous sûr de vouloir rouvrir ce ticket?", "reopen?": "Êtes-vous sûr de vouloir rouvrir {{name}}?", "cancelThisTicket?": "Êtes-vous sûr de vouloir annuler ce ticket?", "cancelThis?": "Êtes-vous sûr de vouloir annuler {{name}}?", "reopenSuccess": "{{name}} a <PERSON>té rouvert avec succès", "closeSuccess": "{{name}} a été clôturé avec succès", "resolved": "traités", "theTicket": "Ce ticket", "listOfInteractions": "Liste des interactions", "Transaction": " Transaction", "Invoices": "Facture", "showMore": "Voir plus", "showLess": "Voir moins", "comment": "Commentaire", "toAddress": "à l'adresse", "withSubject": "pour sujet", "user": "Utilisa<PERSON>ur", "minimize": "<PERSON><PERSON><PERSON><PERSON>", "maximize": "<PERSON><PERSON><PERSON><PERSON>", "Emails": "Emails", "Meeting": "Réunion", "Visio": "Visio", "Notes": "Notes", "Comments": "Commentaires", "Todolist": "Listes", "Files": "Fichiers", "Appelsdirects": "<PERSON><PERSON><PERSON> directs"}, "emailTemplates": {"TitlePage": "<PERSON><PERSON><PERSON> de la page", "smsHeader": "En-<PERSON><PERSON>te de SMS", "smsContent": "Contenu SMS", "subjectMail": "Objet de l'email", "bodymail": "Contenu de l'email", "notDeletePrimaryTemplate": "La template par défaut ne peut pas être supprimée.", "folder label must be unique.": "Le nom du dossier doit être unique.", "system": "Système", "template": "Le modéle ", "limitCaracter": "Vous avez dépassé la limite de caractères.", "cannotDeleteSystem": "Vous ne pouvez pas supprimer le modèle système.", "resetBodyMail": "Vou<PERSON>z-vous confirmer la réinitialisation du contenu de l'email ?", "titleResetBodyMail": "Confirmation de réinitialisation du contenu de l'email", "resetBodySms": "Vou<PERSON>z-vous confirmer la réinitialisation du contenu de SMS?", "titleResetBodySms": "Confirmation de réinitialisation du contenu de SMS", "recoveryLink": "Lien pour mot de passe oublié", "otp": "Code de Sécurité", "nameApp": "Nom de l'entreprise", "fullName": "Nom complet", "authentication": "Authentification", "callerNumber": "Numéro de l'appelant", "postNumber": "Numéro de poste", "duration": "<PERSON><PERSON><PERSON>", "cannotAddPage": "Vous ne pouvez pas ajouter une page dans un modèle système", "goToCompany": "Aller aux informations de {{label}}", "plsSelect": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>ner", "acceptInvi": "Accepter l'invitation"}, "files360": {"labelErr": "Veuillez saisir votre étiquette !", "labelPlaceHolder": "Étiquette de fi<PERSON>", "fileCannotBeUploadedToSecurityReasons": "Le fichier ne peut pas être téléchargé pour des raisons de sécurité.", "fileErrorWhileUploading": "Une erreur s'est produite lors du téléchargement du fichier.", "file": "<PERSON><PERSON><PERSON>", "fileErr": "Veuillez télécharger votre fichier !", "filePlaceHolder": "Cliquez ou glissez le fichier dans cette zone pour le télécharger", "uploadSubmit": "So<PERSON><PERSON><PERSON>", "uploadReset": "Annuler", "uploadSearch": "Rechercher des fichiers par nom...", "fileMaxSizeMsg": "La taille du fichier doit être inférieure à 10Mo", "fileName": "Nom du fichier", "fileUploadedAt": "Télécha<PERSON><PERSON> le", "fileUpatedAt": "Mis à jour le", "fileCreator": "<PERSON><PERSON><PERSON><PERSON>", "modifyFile": "Modifier le fichier", "deleteFile": "<PERSON><PERSON><PERSON><PERSON> le fichier", "deleteFileDescription": "Êtes-vous sûr de vouloir supprimer ce fichier ?", "deleteFileTitle": "<PERSON><PERSON><PERSON><PERSON> le fichier", "onOk": "O<PERSON>", "onCancel": "Non", "viewFile": "Voir le fichier", "downloadFile": "Télécharger le fichier", "updateLabel": "Libellé du fichier {{filename}} mis à jour avec succès", "deleteFileSuccess": "Fichier {{filename}} supprimé avec succès", "updateFile": "Fichier mis à jour avec succès", "uploadFile": "<PERSON><PERSON><PERSON> téléchargé avec succès", "fileUploadedSuccessfully": "<PERSON><PERSON>er téléversé avec succès", "failedToUploadFile": "Échec du téléversement du fichier. Veuillez réessayer.", "folderCreatedSuccessfully": "Dossier c<PERSON><PERSON> avec succès", "failedToCreateFolder": "Échec de la création du dossier. Veuillez réessayer.", "uploading": "Téléversement en cours", "completed": "terminé", "processing": "Traitement en cours", "cancelUpload": "Annuler le téléversement", "uploadCanceled": "Téléversement annulé", "createFolderModal": {"title": "<PERSON><PERSON>er un nouveau dossier", "folderName": "Nom du dossier", "placeholder": "Saisissez le nom du dossier", "required": "Veuillez saisir un nom de dossier", "tooLong": "Le nom est trop long", "cancel": "Annuler", "create": "<PERSON><PERSON><PERSON>"}, "shareModal": {"title": "Partager {{name}}", "selectUsers": "Sélectionner les utilisateurs avec qui partager", "selectUsersPlaceholder": "Rechercher et sélectionner des utilisateurs...", "permissions": "Permissions", "permissionsPlaceholder": "Sélectionner le niveau de permission", "viewer": "<PERSON><PERSON><PERSON>", "editor": "<PERSON><PERSON><PERSON>", "viewerDescription": "Peut voir et télécharger", "editorDescription": "Peut voir, télécharger, modifier et supprimer", "fileDetails": "<PERSON><PERSON><PERSON> du fi<PERSON>", "folderDetails": "<PERSON><PERSON><PERSON> du dossier", "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "size": "<PERSON><PERSON>", "extension": "Extension", "type": "Type", "createdAt": "<PERSON><PERSON><PERSON>", "cancel": "Annuler", "share": "Partager", "noUsersSelected": "Veuillez sélectionner au moins un utilisateur avec qui partager", "noPermissionSelected": "Veuillez sélectionner un niveau de permission"}, "fileDownloadedSuccessfully": "<PERSON><PERSON><PERSON> téléchargé avec succès", "failedToDownloadFile": "Échec du téléchargement du fichier. Veuillez réessayer.", "canOnlyDownloadFiles": "Vous ne pouvez télécharger que des fichiers, pas des dossiers"}, "notes360": {"createNote": "<PERSON><PERSON>er un commentaire", "public": "Public", "internal": "Interne", "visibility": "Visibilité", "editNote": "Modifier ce commentaire", "deleteNote": "Supprimer ce commentaire", "deleteTheNote": "Supprimer ce commentaire", "ensureDeleteMessage": "Êtes-vous sûr de vouloir supprimer ce commentaire ?", "okDelete": "O<PERSON>", "cancelDelete": "Non", "saveEdit": "<PERSON><PERSON><PERSON><PERSON>", "noNotesFound": "Aucun commentaire trouvé", "searchNotes": "Rechercher des commentaires …", "noteCreatedSuccess": "Commentaire créé avec succès", "noteDeletedSuccess": "Commentaire supprimé avec succès"}, "signature": {"unregistred": "Cette signature n'a jamais été enregistré. Veuillez appuyer sur le bouton 'Enregistrer' pour sauvegarder la signature.", "addSignature": "Ajouter une nouvelle signature d'email.", "listSignature": "Liste des signatures d'email", "newSignature": "nouvelle signature d'email", "defaultSignature": "Définir comme signature par défaut", "labelUsed": "Ce nom est déjà utilisé.", "defaultRequired": "Au moins une signature est marquée comme étant la signature par défaut.", "exportpdf": "Exporter le pdf"}, "build": {"start": "La publication de wiki est en cours.", "end": "Le wiki est généré et publié en ligne avec succès.", "fail": "La génération et la publication de wiki est échouée."}, "modules": {"companies": "Société", "contacts": "Contact", "leads": "<PERSON><PERSON>", "deals": "Offre", "tickets": "Ticket", "projects": "Projet", "products": "Produit", "bookings": "Réservation"}, "todolist": {"createList": "<PERSON><PERSON><PERSON> une liste", "createListTitleModal": "<PERSON><PERSON>er une nouvelle liste", "updateListTitleModal": "Modifier la liste [{{listLabel}}]", "listLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "newListItem": "Nouvel élément de la liste", "completed": "{{done}} sur {{total}} sont terminés", "listLabelError": "Veuillez ajouter un libellé à votre liste", "listItemError": "Veuillez ajouter un élément", "noListItems": "Il n'y a aucun élément dans cette liste", "noMore": "Pas Plus"}, "selfNotes": {"addNote": "Ajouter une note", "searchNotes": "Rechercher des notes ...", "sharingSuccess": "Partage de la note réussi", "sharingError": "Échec du partage de la note", "deleteSuccess": "Note supprimée avec succès", "deleteError": "Échec de la suppression de la note", "deleteNote": "Supprimer la note", "deleteNoteMessage": "Êtes-vous sûr de vouloir supprimer cette note ?", "autoSaveEnabled": "Votre note sera enregistrée automatiquement.", "noteLockedBy": "Cette note est verrouillée par", "youAreTheLocker": "(Vous).", "youAreNotTheLocker": ". Vous ne pouvez pas la modifier, et le contenu que vous écrirez ne sera pas pris en compte.", "shareTitle": "Partager la note avec des collègues", "searchForColleaguesToShare": "Rechercher des collègues...", "saveShare": "Enregistrer", "cancelShare": "Annuler", "delete": "<PERSON><PERSON><PERSON><PERSON>", "shareWith": "Partager avec", "sharingInfo": "La note sera partagée avec les membres sélectionnés en mode lecture.", "selectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> tout", "edit": "Editer", "archiver": "Archiver", "deleteNoteRichText": "Vous êtes sur le point de supprimer cette note. Êtes-vous sûr de vouloir continuer ?", "readOnly": "Accès en mode lecture seulement.", "quote": "<PERSON><PERSON><PERSON> des notes, c'est faire des gammes de littérature.", "observer": "Observateur", "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "discussion": "Discussion", "download": "Télécharger", "allNotes": "Toutes les notes", "myNotes": "Mes notes", "sharedNotes": "Partagées par moi", "personalNotes": "Notes personnelles", "sharedWithMe": "Partagées avec moi", "associate": "Affecter", "affectNote": "Affecter la note", "affectationSaved": "Affectation enregistrée avec succès.", "affectationSavedError": "<PERSON><PERSON>ur lors de l'affectation.", "family": "<PERSON><PERSON><PERSON> associé", "element": "Élément lié", "selectFamily": "Sélectionnez un module", "selectElement": "Sélectionnez un élément", "familyRequired": "Le champ module est obligatoire.", "elementRequired": "Le champ élément est obligatoire.", "cancel": "Annuler", "informations": "Informations", "title": "Titre", "createdAt": "<PERSON><PERSON><PERSON>", "lastUpdateAt": "<PERSON><PERSON><PERSON> mise à jour le", "sharedWith": "Partagé avec", "notShared": "Cette note n'est partagée avec personne.", "notAssociated": "Cette note n'est associée à aucun module.", "deleteAffectation": "Supprimer l'affectation", "affectationDeleted": "Affectation supprimée avec succès.", "note_has_discussion": "Cette note comprend une discussion", "note_is_affected": "Cette note a été associée", "noteListPagination": "{{range}} de {{totalItems}} notes"}, "globalSearch": {"globalSearchAuto": "Recherche Globale...", "infoOfSearchFunc": "Recherchez des emails, activités, contacts, collègues, affaires, projets...", "other": "<PERSON><PERSON>", "resultsFound": "1-{{dataLen}} sur {{total}} éléments trouvés", "infoAboutClick": "Si vous cliquez sur un élément, il s'ouvrira !", "noResultsFound": "Aucun résultat trouvé ! Essayez d'utiliser d'autres mots-clés.", "clearFilter": "<PERSON><PERSON><PERSON><PERSON> le filtre", "unread": "Non lu", "createdBy": "C<PERSON><PERSON> par", "created": "<PERSON><PERSON><PERSON>", "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "visioInProgress": "En cours", "joinVisio": "Participer", "overdue": "En retard", "lastItemsFound": "Derniers {{total}} éléments trouvés", "emailBody": "Contenu de l'email", "shared": "Partagé"}, "filterFamily": {"where": "<PERSON><PERSON>", "and": "Et", "or": "Ou", "isEqual": "est égal à", "isNotEqual": "n'est pas égal à", "isLike": "contient", "isNotLike": "ne contient pas", "isBlank": "est vide", "isNotBlank": "n'est pas vide", "is": "est", "isNot": "n'est pas", "containsAnyOf": "contient l'un de", "doesNotContainAnyOf": "ne contient aucun de", "containsAllOf": "contient tous", "doesNotContainAllOf": "ne contient pas tous", "isGreaterThan": "est supérieur à", "isLessThan": "est inférieur à", "isGreaterThanOrEqual": "est supérieur ou égal à", "isLessThanOrEqual": "est inférieur ou égal à", "isAfter": "est après", "isBefore": "est avant", "isOnOrAfter": "est le jour ou après", "isOnOrBefore": "est le jour ou avant", "isWithin": "est entre", "filenameContains": "le nom de fichier contient", "filenameDoesNotContain": "le nom de fichier ne contient pas", "noFilterAdded": "Aucun filtre ajou<PERSON>", "addFilter": "Ajouter un filtre", "resetFilters": "Réinitialiser les filtres", "apply": "Appliquer ", "filter": "Filtre", "allStages": "Toutes les étapes", "sort": "<PERSON><PERSON>", "selectField": "Sélectionner un champ...", "selectOrder": "Sélectionner l'ordre...", "ascendant": "Croissant", "descendant": "Décroissant"}, "integrations": {"listOfIntegrations": "Liste des intégrations", "integrations": "Intégrations", "folderId": "Identifiant de dossier", "repository": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ticketId": "Identifiant de ticket", "resetConfirmation": "Confirmation de Réinitialisation", "ticketTitleIn": "Titre du ticket dans ", "descriptionTicketIn": "Description du ticket dans ", "categories_id": "Catégories", "urgency": "Urgence", "priority": "Priorité", "choose": "Veuillez choisir ", "quitChat": "Quitter discussions", "goToViewTicket": "Êtes-vous certain de vouloir accéder à la vue détaillée de ce ticket ?", "entities_id": "Entités", "iaTools": "Outils IA", "emptyIntegration": "Cliquer sur une intégration pour afficher les paramètres à configurer"}, "cart": {"createCartTitle": "<PERSON><PERSON> associ<PERSON>", "UpdateCartTitle": "<PERSON>é<PERSON> du panier", "addNewProductBtn": "Ajouter un nouveau produit", "quantity": "Quantité", "unitPrice": "Prix unitaire", "unit": "Unité", "discount": "Réduction", "tax": "Taxe", "amount": "<PERSON><PERSON>", "total": "Totale", "subtotal": "Subtotale", "selectProduct": "Produit", "selectDiscount": "Sélectionnez une réduction", "emptyCart": "Panier vide", "startAddingProducts": "Commencez à ajouter des produits", "products": "Produit{{plural}}"}, "tours": {"skip": "<PERSON><PERSON><PERSON>", "key": "Clé", "name": "Nom", "description": "Description", "add": "Ajouter une visite", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON> une visite", "actions": "Actions", "deleteConfirm": "Voulez-vous vraiment supprimer cette visite ?", "successCreated": "Visite créée avec succès", "successUpdated": "Visite mise à jour avec succès", "successDeleted": "Visite supprimée avec succès", "errorDeleting": "<PERSON><PERSON><PERSON> lors de <PERSON>", "errorGeneric": "Une erreur est survenue", "errorLoading": "Erreur de chargement des visites", "key_required": "La clé est requise", "name_required": "Le nom est requis", "description_required": "La description est requise", "noGuide": "Cette interface ne dispose pas de guide.", "fetchError": "Impossible de récupérer le guide, veuil<PERSON><PERSON> réessayer plus tard.", "loadingSteps": "Chargement des étapes du guide, veuil<PERSON>z patienter…", "steps": {"manage_steps": "<PERSON><PERSON><PERSON> les étapes", "add": "Ajouter une étape", "edit": "Modifier l'étape", "delete": "<PERSON><PERSON><PERSON><PERSON>", "deleted": "Étape supprimée", "updated": "Étape mise à jour", "created": "<PERSON><PERSON><PERSON>", "error_fetch": "Erreur lors du chargement des étapes", "error_save": "Erreur lors de l'enregistrement de l'étape", "error_delete": "Erreur lors de la suppression de l'étape", "error_reorder": "<PERSON><PERSON>ur lors du réordonnancement", "reordered": "Étapes réordonnées", "title": "Titre", "description": "Description", "selector": "<PERSON><PERSON><PERSON><PERSON>", "actions": "Actions", "upload": "Image de couverture", "drag_text": "Cliquez ou faites glisser un fichier à téléverser", "drag_hint": "Un seul fichier image est autorisé.", "confirm_delete": "Êtes-vous sûr de vouloir supprimer cette étape ?", "invalid_image_type": "Seules les images JPEG, PNG, GIF ou WEBP sont autorisées.", "too_large": "L'image doit faire moins de 2 Mo.", "no_image": "Aucune image"}}, "livePanel": {"searchPLive": "Recherche par nom ou par number", "online": "En ligne", "offline": "<PERSON><PERSON> ligne", "busy": "Occupé(e)", "away": "Absent(e)", "onCall": "En appel", "free": "Libre", "ringing": "Son<PERSON><PERSON>", "onPause": "En pause", "users": "Utilisateurs", "availability": "Disponibilités", "statusCall": "Statut (APPELS)", "onCallWith": "En appel avec", "callTime": "Temps d'appel", "incomingCalls": "Appels Entrants", "answered": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notAnswered": "Non Répondus", "lost": "Perdus", "recalled": "Rappelés", "outgoingCalls": "Appels Sortants", "appMobile": "App Mobile", "deskPhone": "Téléphone de bureau (IP)", "webPhone": "Téléphone Web (webPhone)", "statusCalls": "Statut des appels", "todayNbrCalls": "Nombre d'appels aujourd'hui", "inbound": "Entrants", "outbound": "Sortants"}, "drive": {"completed": "terminé", "uploading": "en cours de téléchargement", "processing": "en cours de traitement", "cancelUpload": "Annuler le téléchargement", "uploadCanceled": "Telechargement annulé", "fileDownloadedSuccessfully": "<PERSON><PERSON><PERSON> téléchargé avec succès", "failedToDownloadFile": "Échec du téléchargement du fichier. Veuillez réessayer.", "canOnlyDownloadFiles": "Vous ne pouvez télécharger que des fichiers, pas des dossiers", "path": "Chemin", "basicInformation": "Informations de base", "ownerInformation": "Informations du propriétaire", "dateInformation": "Informations de date", "technicalInformation": "Informations techniques", "locationInformation": "Informations de localisation", "New": "Nouveau", "extension": "Extension", "createFolder": "<PERSON><PERSON><PERSON> un dossier", "folder": "Dossier", "file": "<PERSON><PERSON><PERSON>", "createFile": "<PERSON><PERSON><PERSON> un fichier", "rename": "<PERSON>mmer", "delete": "<PERSON><PERSON><PERSON><PERSON>", "upload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "download": "Télécharger", "view": "Voir", "share": "Partager", "details": "Détails", "edit": "Modifier", "name": "Nom", "type": "Type", "size": "<PERSON><PERSON>", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "Mis à jour le", "actions": "Actions", "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createNew": "Créer nouveau", "uploadFile": "Telecharger un fichier", "myDrive": "Mon Drive", "noFilesFound": "<PERSON><PERSON><PERSON> fi<PERSON>er ou dossier trouvé", "shareFeatureComingSoon": "Fonctionnalité de partage bientôt disponible", "itemDeletedSuccessfully": "Élément supprimé avec succès", "failedToDeleteItem": "Échec de la suppression de l'élément. Veuillez réessayer.", "itemRenamedSuccessfully": "Élément renommé avec succès", "failedToRenameItem": "Échec du renommage de l'élément. Veuillez réessayer.", "youCanOnlyUploadToFolders": "Vous ne pouvez telecharger que dans des dossiers", "loadingData": "Chargement des données", "fileUploadedSuccessfully": "Fichier telechargé avec succès", "failedToUploadFile": "Échec du telechargement du fichier. Veuillez réessayer.", "folderCreatedSuccessfully": "Dossier c<PERSON><PERSON> avec succès", "failedToCreateFolder": "Échec de la création du dossier. Veuillez réessayer.", "createFolderModal": {"title": "<PERSON><PERSON>er un nouveau dossier", "folderName": "Nom du dossier", "placeholder": "Saisissez le nom du dossier", "required": "Veuillez saisir un nom de dossier", "tooLong": "Le nom est trop long", "cancel": "Annuler", "create": "<PERSON><PERSON><PERSON>", "tooShort": "Le nom est trop court"}, "shareModal": {"removeSuccess": "Utilisateur supprimé avec succès", "shareSuccess": "Partage r<PERSON><PERSON>i", "sharedWith": "Partagé", "selectPermissionRequired": "Sélectionner un niveau de permission", "selectUserRequired": "Sélectionner au moins un utilisateur avec qui partager", "noSharedUsers": "Cet élément n'est partagé avec personne", "confirmRemoveUser": "Êtes-vous sûr de vouloir supprimer cet utilisateur?", "yes": "O<PERSON>", "no": "Non", "title": "Partager", "selectUsers": "Sélectionner les utilisateurs avec qui partager", "selectUsersPlaceholder": "Rechercher et sélectionner des utilisateurs...", "permissions": "Permissions", "permissionsPlaceholder": "Sélectionner le niveau de permission", "viewer": "<PERSON><PERSON><PERSON>", "editor": "<PERSON><PERSON><PERSON>", "viewerDescription": "Peut voir et télécharger", "editorDescription": "Peut voir, télécharger, modifier et supprimer", "fileDetails": "<PERSON><PERSON><PERSON> du fi<PERSON>", "folderDetails": "<PERSON><PERSON><PERSON> du dossier", "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "size": "<PERSON><PERSON>", "extension": "Extension", "type": "Type", "createdAt": "<PERSON><PERSON><PERSON>", "cancel": "Annuler", "share": "Partager", "noUsersSelected": "Veuillez sélectionner au moins un utilisateur avec qui partager", "noPermissionSelected": "Veuillez sélectionner un niveau de permission"}, "deleteConfirmation": {"deleteFolder": "Supp<PERSON>er le dossier", "deleteFile": "<PERSON><PERSON><PERSON><PERSON> le fichier", "areYouSure": "Êtes-vous sûr de vouloir supprimer", "folderWarning": "Cette action supprimera définitivement le dossier et tout son contenu.", "fileWarning": "Cette action ne peut pas être annulée.", "cancel": "Annuler", "delete": "<PERSON><PERSON><PERSON><PERSON>"}, "renameModal": {"title": "Renommer {{name}}", "name": "Nom", "required": "Veuillez entrer un nom.", "minText": "Le nom doit comporter au moins 2 caractères.", "maxText": "Le nom doit comporter moins de 30 caractères.", "placeholder": "Entrez le nouveau nom", "cancel": "Annuler", "save": "Enregistrer"}, "dropHere": "Dé<PERSON>r ici", "canOnlyDropIntoFolders": "Vous ne pouvez déposer des éléments que dans des dossiers", "cannotDropIntoItself": "Impossible de déposer un élément sur lui-même", "itemMovedSuccessfully": "{{itemName}} déplacé vers {{folderName}} avec succès", "dragDropFiles": "Glissez-dé<PERSON>z vos fichiers ici", "dragDropDescription": "Support pour le téléchargement de plusieurs fichiers", "clickToUpload": "Cliquez pour télécharger", "or": "ou", "selectedFiles": "Fichiers sélectionnés", "clearAll": "Tout effacer", "uploadFiles": "Télécharger", "noFilesSelected": "<PERSON><PERSON><PERSON> fichier s<PERSON>", "removeFile": "<PERSON><PERSON><PERSON><PERSON> le fichier", "dropFilesHere": "Déposez vos fichiers ici pour les télécharger", "releaseToUpload": "Relâchez pour télécharger les fichiers", "filesUploadedSuccessfully": "Fichiers téléchargés avec succès", "failedToUploadFiles": "Échec du téléchargement des fichiers", "open": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "failedToOpenFile": "Échec de l'ouverture du fichier", "addFolder": "Ajouter un dossier", "newFolder": "Nouveau dossier", "enterFolderName": "Entrez le nom du dossier", "folderDeletedSuccessfully": "Dossier supprimé avec succès", "failedToDeleteFolder": "Échec de la suppression du dossier", "folderRenamedSuccessfully": "Dossier renommé avec succès", "failedToRenameFolder": "Échec du renommage du dossier", "confirmDeleteFolder": "Êtes-vous sûr de vouloir supprimer ce dossier ? Tout le contenu sera définitivement supprimé.", "confirmDeleteFolderTitle": "Supp<PERSON>er le dossier", "failedToLoadItems": "Échec du chargement des éléments du drive. Veuillez réessayer plus tard.", "folders": "Dossiers", "files": "Fichiers", "filesInFolder": "Fichiers dans le dossier", "selectFolder": "Sélectionner un dossier", "selectFolderToViewFiles": "Sélectionnez un dossier dans l'arbre pour voir ses fichiers", "noFilesInFolder": "Aucun fichier dans ce dossier", "loading": "Chargement...", "failedToLoadSharedUsers": "Échec du chargement des utilisateurs partagés", "notShared": "Non partagé avec personne", "sharedWith": "Partagé avec", "sharedItem": "Cet élément est partagé", "andMoreUsers": "et {{count}} de plus...", "searchFiles": "Rechercher des fichiers et dossiers...", "noResultsFound": "Aucun résultat trouvé pour \"{{search}}\"", "searchResultsCount": "{{count}} résultat{{plural}} trouvé{{plural}} pour \"{{search}}\"", "clearSearch": "<PERSON><PERSON><PERSON><PERSON>", "suggestedImages": "Résultats d'images par IA", "aiImageSearchDescription": "Images trouvées grâce à la recherche sémantique par IA", "clearingSearchToNavigate": "Effacement de la recherche pour naviguer vers le dossier...", "scrollDownForMore": "Faites défiler vers le bas pour voir plus de résultats dans la liste principale", "viewAllImages": "Voir toutes les {{count}} images", "itemNotFound": "Élément non trouvé", "errorLoadingItemDetails": "Erreur lors du chargement des détails de l'élément", "failedToLoadDetails": "Échec du chargement des détails", "storage": "Stockage"}, "tour": {"next": "Suivant", "prev": "Précédent", "finish": "<PERSON><PERSON><PERSON>"}, "liveChat": {"installationIn": "installation dans", "recomandedForOptimalLoading": "Recommandé pour un chargement optimal", "pasteTheCodeBeforeThe": "Collez le code avant la balise", "validAlternative": "Alternatif valide", "testUrl": "URL de test", "titleChat": "<PERSON><PERSON><PERSON> du chat", "welcomeMessage": "Message de Bienvenue", "positionWidget": "Position du Widget", "colorTheme": "<PERSON><PERSON><PERSON>", "behaviorChat": "Comportement du Chat", "showOnline": "A<PERSON><PERSON><PERSON> le statut en ligne"}, "stat": {"load_model": "Charger le modèle", "no_data": "<PERSON><PERSON><PERSON> donnée disponible", "show_graph": "Afficher le graphique", "save_chart": "Sauvegarder le graphique", "saveAsModel": "Sauvegarder un modèle", "title_is_required": "Le titre est requis", "myCharts": "Mes graphiques", "delete_selected": "Supprimer la sélection", "search_by_chart_title": "Rechercher par titre de graphique", "no_charts": "Aucun graphique disponible", "enter_title": "<PERSON><PERSON> un titre", "delete": "<PERSON><PERSON><PERSON><PERSON>", "update_chart": "Mettre à jour", "choose_model": "<PERSON><PERSON><PERSON><PERSON>", "add_chart": "Ajouter un graphique", "edit_chart": "Modifier le graphique", "stop_refresh": "<PERSON><PERSON><PERSON><PERSON> le rafraîchissement", "start_refresh": "D<PERSON><PERSON>rer le rafraîchissement", "addNewTable": "Ajouter un nouvel onglet", "noChartGenerated": "Aucun graphique généré", "SelectAFamilyToLoadAvailableDataModels": "Sélectionnez une famille pour charger les modèles de données disponibles", "ChooseAModelTemplateForQuickSetup": "Vous pouvez choisir un modèle de données pour une configuration rapide", "option3": "Assistant IA pour générer automatiquement des visualisations ", "ReadyToVisualize": "<PERSON><PERSON><PERSON><PERSON> à visualiser", "ConfigureYourSettingsAndCreateStunningDataVisualizations": "Configurez vos paramètres et créez des visualisations de données époustouflantes", "ChooseYourDataFamilyCategory": "Choisissez votre catégorie de famille de données", "QuickGuide": "Guide rapide", "public": "Public", "private": "Priv<PERSON>", "Refresh_Layout": "Actualiser", "ChartActions": "Actions du graphique", "ConfigurationPanel": "Panneau de configuration", "createNewTab": "<PERSON><PERSON>er un nouvel onglet", "TotalCharts": "G<PERSON><PERSON><PERSON> to<PERSON>", "of": "de", "edit": "Modifier l'onglet", "charts": "graphiques", "Clear": "<PERSON><PERSON><PERSON><PERSON>", "Created": "<PERSON><PERSON><PERSON>", "help_message": "Je suis là pour vous aider à créer des visualisations professionnelles et à analyser vos données efficacement.", "create_chartAssistant": "Créez un graphique professionnel avec des métriques avancées.", "click_to_start": "Cliquez pour commencer", "Showing": "Affichage", "professional_support": "Assistance professionnelle", "ai_assistant": "Assistant IA", "Cancel": "Annuler", "select_preconfigured_template": "Sélectionnez un modèle prédéfini", "deleted_successfully": "Graphique(s) supprimé(s) avec succès.", "delete_error": "Une erreur s'est produite lors de la suppression du/des graphique(s).", "tab_deleted_successfully": "Onglet supprimé avec succès.", "tab_delete_error": "Une erreur s'est produite lors de la suppression de l'onglet.", "TabName": "Nom de l'onglet", "AfficherLaConfiguration": "Afficher la configuration", "RéduireLaConfiguration": "Reduire la configuration", "Début": "D<PERSON>but", "Fin": "Fin", "AnullerLaModification": "Annuler la modification", "Auto-refreshActive": "Auto-refresh active", "chartRendererNotAvailable": "Le rendu du graphique n'est pas disponible", "Auto-refreshInactive": "Auto-refresh inactive", "genereleghraphique": "générer le graphique", "ChooseEmail": "Choisissez adresse email", "editing_message": "Modification du message"}}