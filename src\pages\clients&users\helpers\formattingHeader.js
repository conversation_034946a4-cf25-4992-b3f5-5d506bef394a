/* eslint-disable jsx-a11y/anchor-is-valid */
import { <PERSON><PERSON>, Toolt<PERSON> } from "antd";
import RenderColumnsTable from "../components/RenderColumnsTable";
import DropDownOption from "../components/DropDownOption";
import getContactDataAndDispatch from "./getContactDataAndDispatch";
import { familyWithAvatar, sortableTypes } from "../FamilyRouting";
import DisplayAvatar from "pages/voip/components/DisplayAvatar";
import { URL_ENV } from "index";
import "../index.css";
import { TbClockCheck, TbClockExclamation } from "react-icons/tb";

const displayTooltipTitle = (status, t) => {
  switch (status) {
    case 1:
      return t("contacts.created");

    case 2:
      return t("contacts.invited");

    case 3:
      return t("contacts.active");

    case 4:
      return t("contacts.blocked");

    default:
      return t("contacts.created");
  }
};

const formattingHeader = async (
  data,
  handleDropDownOptions,
  user,
  familyId,
  dispatch,
  location,
  navigate,
  t,
  relation_id,
  sortedInfo,
  folderId
) => {
  // console.log({ folderId });
  const canSort = true;
  // user.id === "66a3b88d956860ca6308cb44" ||
  // user.id === "66827c78558f7d4aed076212" ||
  // user.id === "667c1a0641b03e7f73004b13" ||
  // user.id === "667ef9a9177386131e0474e4";

  const baseUrlImg =
    URL_ENV?.REACT_APP_BASE_URL + URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL;
  const result = [];
  const checkFieldType = {};
  data?.forEach((group, indexGroup) => {
    const { label, fields, user_rank: groupRank, id: groupId } = group;
    let fieldsList = [];
    fields?.forEach((field, indexField) => {
      const {
        alias,
        id,
        field_type,
        show,
        avatar,
        user_rank: fieldRank,
      } = field;
      const { fieldType } = field_type;
      checkFieldType[id] = fieldType;
      if (alias !== "" && fieldType !== "password") {
        if (indexGroup === 0 && indexField === 0) {
          result.push({
            title: alias,
            alias,
            dataIndex: id,
            key: id,
            show: 1,
            fixed: "left",
            ellipsis: true,
            width: !!familyWithAvatar?.[familyId] ? 290 : 250,
            fieldType,
            sorter: true && canSort,
            sortOrder: sortedInfo.field === id ? sortedInfo.order : null,
            render: (_data, record, index) => (
              <div className="relative flex w-full items-center justify-between">
                <div className="badge-etat relative flex w-9/12 flex-row items-center space-x-2  font-semibold">
                  {!!familyWithAvatar?.[familyId] && (
                    <div>
                      <DisplayAvatar
                        name={_data?.value}
                        urlImg={
                          !!record?.avatar?.path &&
                          `${baseUrlImg}${record?.avatar?.path}`
                        }
                        size={28}
                        canPreview={true}
                      />
                    </div>
                  )}
                  {familyId === 4 && (
                    <Tooltip title={displayTooltipTitle(record?.status, t)}>
                      <Badge
                        className="cursor-help"
                        status={
                          record?.status === 3
                            ? "success"
                            : record?.status === 2
                            ? "processing"
                            : record?.status === 4
                            ? "error"
                            : "default"
                        }
                      />
                    </Tooltip>
                  )}
                  {relation_id ? (
                    <p className={`truncate`}>{_data?.value}</p>
                  ) : (
                    <div className="flex w-full items-center space-x-1">
                      <a
                        id="storeData"
                        onClick={() =>
                          getContactDataAndDispatch(
                            familyId,
                            _data?.value,
                            record,
                            data,
                            dispatch,
                            location?.pathname,
                            navigate
                          )
                        }
                        className={`truncate`}
                      >
                        {_data?.value}
                      </a>
                      {familyId === 6 && record.sla_ticket?.time && (
                        <Tooltip
                          title={
                            // record.sla_ticket?.is_overdue
                            //   ? t("helpDesk.slaDepassed")
                            //   :
                            record.sla_ticket?.time
                          }
                        >
                          {record.sla_ticket?.is_overdue ? (
                            <TbClockExclamation
                              style={{
                                color: "red",
                                fontSize: 16,
                                cursor: "help",
                              }}
                            />
                          ) : (
                            <TbClockCheck
                              style={{
                                color: "green",
                                fontSize: 16,
                                cursor: "help",
                              }}
                            />
                          )}
                        </Tooltip>
                      )}
                      {familyId === 6 &&
                      record?.pipelineStage?.isFinalStage &&
                      !record.resolvedStage ? (
                        <span
                          style={{ fontSize: 11 }}
                          className=" text-slate-500"
                        >
                          ({t("mailing.closed")})
                        </span>
                      ) : familyId === 2 && record?.guest ? (
                        <span
                          style={{ fontSize: 11 }}
                          className=" text-slate-500"
                        >
                          ({t("visio.guest", { plural: "" })})
                        </span>
                      ) : null}
                    </div>
                  )}
                </div>
                <DropDownOption
                  key={index}
                  index={index}
                  elementID={record.key}
                  elementLabel={_data?.value}
                  handleDropDownOptions={handleDropDownOptions}
                  role={user?.role}
                  familyId={familyId}
                  etat={record?.status}
                  linkInvitation={record?.invitation}
                  relation_id={relation_id}
                  canOpenRoom={record?.can_create_room}
                  pipelineStage={record?.pipelineStage}
                  resolvedStage={record?.resolvedStage}
                  guest={record?.guest}
                  folderId={folderId}
                />
              </div>
            ),
          });
        } else if (!avatar) {
          fieldsList.push({
            onHeaderCell: () => ({
              className: "table-header-left-align",
            }),
            title: alias,
            alias,
            dataIndex: id,
            key: id,
            show: show,
            ellipsis: true,
            rank: fieldRank,
            width:
              fieldType === "image" ||
              // fieldType === "album" ||
              fieldType === "color" ||
              fieldType === "extension"
                ? 150
                : "auto",
            align:
              fieldType === "image" || fieldType === "album" /*||
                fieldType === "color"*/
                ? "center"
                : fieldType === "monetary"
                ? "right"
                : "auto",
            fieldType,
            sorter: /*!!sortableTypes[fieldType] &&*/ canSort,
            sortOrder: sortedInfo.field === id ? sortedInfo.order : null,
            render: (data, record) => {
              return (
                <RenderColumnsTable
                  key={record?.id}
                  values={data?.value}
                  type={data?.fieldType}
                  relatedType={data?.relatedType}
                  relatedOptions={data?.relatedOptions}
                  // setOpenModalMessage={setOpenModalMessage}
                  // setReceiverMail={setReceiverMail}
                  // poste={userPoste}
                />
              );
            },
          });
        }
      }
    });
    result.push({
      title: label,
      id: groupId,
      rank: groupRank,
      children: fieldsList,
    });
    fieldsList = [];
  });
  //
  result.push({
    title: "Timestamps",
    children: [
      {
        title: t("contacts.createdBy"),
        dataIndex: "created_by",
        key: "Timestamps_created_by",
        show: 1,
        width: 120,
        align: "center",
      },
      {
        title: t("contacts.createdAt"),
        dataIndex: "created_at",
        key: "Timestamps_created_at",
        show: 1,
        width: 200,
        fieldType: "created_at",
        sorter: true && canSort,
        sortOrder: sortedInfo.field === "created_at" ? sortedInfo.order : null,
      },
      {
        title: t("contacts.updatedAt"),
        dataIndex: "updated_at",
        key: "Timestamps_updated_at",
        show: 1,
        width: 200,
        fieldType: "updated_at",
        sorter: true && canSort,
        sortOrder: sortedInfo.field === "updated_at" ? sortedInfo.order : null,
      },
    ],
  });

  return { result, checkFieldType };
};

export default formattingHeader;
