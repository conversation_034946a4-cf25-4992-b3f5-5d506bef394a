import { CLEAR_TOKEN, SET_TOKEN, SET_REFRESH_TOKEN } from "../../constants";

export const setToken = (payload) => (dispatch) => {
    dispatch({ type: SET_TOKEN, payload: payload });
};

export const setRefreshToken = (payload) => (dispatch) => {
    dispatch({ type: SET_REFRESH_TOKEN, payload: payload });
};

export const clearToken = () => async (dispatch) => {
    dispatch({ type: CLEAR_TOKEN, payload: "" });
};