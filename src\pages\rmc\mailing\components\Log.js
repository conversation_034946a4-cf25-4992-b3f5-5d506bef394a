import { <PERSON><PERSON>, Drawer, Empty, Spin, Timeline } from "antd";
import React, { useCallback, useEffect, useState } from "react";
import {
  MdAssignmentTurnedIn,
  MdDeleteOutline,
  MdEmail,
  MdLabelImportantOutline,
  MdMarkEmailRead,
  MdOutlineLabelImportant,
  MdOutlineReply,
} from "react-icons/md";
import InfiniteScroll from "react-infinite-scroll-component";
import MainService from "services/main.service";
import moment from "moment/moment";
import { HiEyeSlash, HiIdentification } from "react-icons/hi2";
import { BiTransfer } from "react-icons/bi";
import { IoEyeSharp, IoPricetag } from "react-icons/io5";
import { IoIosRibbon } from "react-icons/io";
import { AiFillStar, AiOutlineStar } from "react-icons/ai";
import {
  TbArrowForwardUpDouble,
  TbRestore,
  TbStatusChange,
} from "react-icons/tb";
import { useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { GiStopwatch } from "react-icons/gi";
import i18n from "translations/i18n";
import DOMPurify from "dompurify";
import { getName } from "pages/layouts/chat/utils/ConversationUtils";

export const ConditionActions = (families, item) => {
  const isFrench = i18n.language === "fr";
  const findFamilyLabel = (familyId) =>
    families.find((family) => family.id === familyId)?.label || "";

  const getEmailQualificationTags = (tags) =>
    tags ? tags.map((tag) => tag.label).join(", ") : "";

  const formatDate = (date) => moment(date).format("llll");

  // Actions messages
  const messages = {
    fr: {
      Identification: {
        create: (auto, familyLabel, labelData) =>
          auto
            ? `L'email a été identifié automatiquement ${familyLabel} ${labelData}`
            : `a identifié l'email à ${familyLabel} ${labelData}`,
        update: (familyLabel, labelData) =>
          `a modifié l'identification de l'email ${familyLabel} ${labelData}`,
        delete: (familyLabel, labelData) =>
          `a supprimé l'identification de l'email ${familyLabel} ${labelData}`,
      },
      Assign: {
        create: (auto, user, assigned) =>
          auto
            ? `L'email a été assigné automatiquement${
                assigned && user !== "JOB" ? ` à ${assigned}` : ""
              }`
            : `<strong style="font-size: 14px">${user.replace(
                /^./,
                (firstChar) => firstChar.toUpperCase()
              )}</strong> a assigné l'email${assigned ? ` à ${assigned}` : ""}`,
        update: (assigned) => `${assigned} a modifié l'assignation de l'email`,
        delete: (auto, user, assigned) =>
          `<strong style="font-size: 14px">${user.replace(/^./, (firstChar) =>
            firstChar.toUpperCase()
          )}</strong> a supprimé l'assignation de ${assigned}`,
      },
      Transfert: {
        default: (email) =>
          email ? `a déplacé l'email à ${email}` : `a déplacé l'email`,
      },
      Qualification: {
        create: (auto, tags) =>
          auto
            ? `L'email a été qualifié automatiquement`
            : `a qualifié l'email${tags ? ` par ${tags}` : ""}`,
        update: (tags) =>
          `a modifié la qualification de l'email${tags ? ` par ${tags}` : ""}`,
        delete: () => `a supprimé la qualification de l'email`,
      },
      Affectation: {
        create: (auto, familyLabel, element) =>
          auto
            ? `L'email a été affecté automatiquement à ${familyLabel} ${element}`
            : `a affecté l'email à ${familyLabel} ${element}`,
        update: () => `a modifié l'affectation de l'email`,
        delete: (familyLabel, element) =>
          `a supprimé l'affectation de l'email de ${familyLabel} ${element}`,
      },
      Seen: () => `a vu l'email`,
      UnSeen: () => `a marqué l'email comme non lu`,
      Trash: () => `a supprimé l'email`,
      Flag: {
        Starred: {
          add: () => `a marqué l'email comme favori`,
          clear: () => `a marqué l'email comme non favori`,
        },
        Important: {
          add: () => `a marqué l'email comme important`,
          clear: () => `a marqué l'email comme non important`,
        },
      },
      Send: () => `a envoyé un email`,
      Reply: () => `a répondu à un email`,
      Restore: () => `a restauré l'email`,
      Received: (address) => `L'email a été reçu sur ${address}`,
      UpdateState: (lastState, newState) =>
        `à modifié le statut de ${lastState} à ${newState}`,
      ChronoStart: (user, chronoNow, expireTime) =>
        `Délenchement chrono pour <strong style="font-size: 14px">${user.replace(
          /^./,
          (firstChar) => firstChar.toUpperCase()
        )}</strong> le ${formatDate(chronoNow)} et il expirera le ${formatDate(
          expireTime
        )}`,
    },
    en: {
      Identification: {
        create: (auto, familyLabel, labelData) =>
          auto
            ? `The email was identified automatically at ${familyLabel} ${labelData}`
            : `identified the email at ${familyLabel} ${labelData}`,
        update: (familyLabel, labelData) =>
          `modified the email identification at ${familyLabel} ${labelData}`,
        delete: (familyLabel, labelData) =>
          `deleted the email identification at ${familyLabel} ${labelData}`,
      },
      Assign: {
        create: (auto, user, assigned) =>
          auto
            ? `The email was assigned automatically${
                assigned && user !== "JOB" ? ` to ${assigned}` : ""
              }`
            : `<strong style="font-size: 14px">${user.replace(
                /^./,
                (firstChar) => firstChar.toUpperCase()
              )}</strong> assigned the email${
                assigned ? ` to ${assigned}` : ""
              }`,
        update: (assigned) => `${assigned} modified the email assignment`,
        delete: (auto, user, assigned) =>
          `<strong style="font-size: 14px">${user.replace(/^./, (firstChar) =>
            firstChar.toUpperCase()
          )}</strong> deleted the assignment of ${assigned}`,
      },
      Transfert: {
        default: (email) =>
          email ? `moved the email to ${email}` : `moved the email`,
      },
      Qualification: {
        create: (auto, tags) =>
          auto
            ? `The email was qualified automatically`
            : `qualified the email${tags ? ` with ${tags}` : ""}`,
        update: (tags) =>
          `modified the email qualification${tags ? ` with ${tags}` : ""}`,
        delete: () => `deleted the email qualification`,
      },
      Affectation: {
        create: (auto, familyLabel, element) =>
          auto
            ? `The email was automatically assigned to ${familyLabel} ${element}`
            : `assigned the email to ${familyLabel} ${element}`,
        update: () => `modified the email assignment`,
        delete: (familyLabel, element) =>
          `deleted the email assignment from ${familyLabel} ${element}`,
      },
      Seen: () => `viewed the email`,
      UnSeen: () => `marked the email as unread`,
      Trash: () => `deleted the email`,
      Flag: {
        Starred: {
          add: () => `marked the email as starred`,
          clear: () => `marked the email as unstarred`,
        },
        Important: {
          add: () => `marked the email as important`,
          clear: () => `marked the email as not important`,
        },
      },
      Send: () => `sent an email`,
      Reply: () => `replied to an email`,
      Restore: () => `restored the email`,
      Received: (address) => `The email was received at ${address}`,
      UpdateState: (lastState, newState) =>
        `changed the status from ${lastState} to ${newState}`,
      ChronoStart: (user, chronoNow, expireTime) =>
        `Chrono triggered for <strong style="font-size: 14px">${user.replace(
          /^./,
          (firstChar) => firstChar.toUpperCase()
        )}</strong> on ${formatDate(chronoNow)} and will expire on ${formatDate(
          expireTime
        )}`,
    },
  };

  // Determine the language
  const lang = isFrench ? "fr" : "en";

  // Extract data
  const { action, mail, user = [], data } = item; // Default user to an empty array if undefined
  const auto = user.length > 0 && user[0] === "JOB"; // Check if user has elements
  const familyLabel = findFamilyLabel(data?.family_id);
  const tags = getEmailQualificationTags(data?.tags);
  const createMarkup = (html) => ({ __html: DOMPurify.sanitize(html) });

  // Handle each action
  switch (action) {
    case "Identification Email":
      return createMarkup(
        messages[lang].Identification[mail]?.(
          auto,
          familyLabel,
          data?.label_data
        )
      );
    case "Assign Email":
      return createMarkup(
        messages[lang].Assign[mail]?.(auto, user[0] ?? "", data?.assigned)
      );
    case "Transfert Email":
      return createMarkup(messages[lang].Transfert.default(data?.email));
    case "Qualification Email":
      return createMarkup(messages[lang].Qualification[mail]?.(auto, tags));
    case "Affectation Email":
      return createMarkup(
        messages[lang].Affectation[mail]?.(auto, familyLabel, data?.element)
      );
    case "Seen Email":
      return createMarkup(messages[lang].Seen());
    case "UnSeen Email":
      return createMarkup(messages[lang].UnSeen());
    case "Trash Email":
      return createMarkup(messages[lang].Trash());
    case "Add Flag Starred Email":
      return createMarkup(messages[lang].Flag.Starred.add());
    case "Clear Flag Starred Email":
      return createMarkup(messages[lang].Flag.Starred.clear());
    case "Add Flag Important Email":
      return createMarkup(messages[lang].Flag.Important.add());
    case "Clear Flag Important Email":
      return createMarkup(messages[lang].Flag.Important.clear());
    case "Send Email":
      return createMarkup(messages[lang].Send());
    case "Reply Email":
      return createMarkup(messages[lang].Reply());
    case "Restaure Email":
    case "Restore Email":
      return createMarkup(messages[lang].Restore());
    case "Received Email":
      return createMarkup(messages[lang].Received(data?.addresse));
    case "Update State Email":
      return createMarkup(
        messages[lang].UpdateState(data?.last_state, data?.new_state)
      );
    case "chrono start":
      return createMarkup(
        messages[lang].ChronoStart(
          user[0] ?? "",
          data?.chrono_now,
          data?.expire_Processing_Time
        )
      );
    default:
      return createMarkup("");
  }
};
export const ConditionIcons = (item) => {
  if (item.action === "Identification Email") {
    return <HiIdentification className=" mt-2.5 h-5 w-5 text-[#757eaa]" />;
  } else if (item.action === "Assign Email") {
    return <MdAssignmentTurnedIn className=" mt-2.5 h-5 w-5 text-[#757eaa]" />;
  } else if (item.action === "Transfert Email") {
    return <BiTransfer className=" mt-2.5 h-5 w-5 text-[#757eaa]" />;
  } else if (item.action === "Qualification Email") {
    return <IoPricetag className=" mt-2.5 h-5 w-5 text-[#757eaa]" />;
  } else if (item.action === "Affectation Email") {
    return <IoIosRibbon className=" mt-2.5 h-5 w-5 text-[#757eaa]" />;
  } else if (item.action === "Seen Email") {
    return <IoEyeSharp className=" mt-2.5 h-5 w-5 text-[#757eaa]" />;
  } else if (item.action === "UnSeen Email") {
    return <HiEyeSlash className=" mt-2.5 h-5 w-5 text-[#757eaa]" />;
  } else if (item.action === "Trash Email") {
    return <MdDeleteOutline className=" mt-2.5 h-5 w-5 text-[#757eaa]" />;
  } else if (item.action === "Add Flag Starred Email") {
    return <AiFillStar className=" mt-2.5 h-5 w-5 text-[#757eaa]" />;
  } else if (item.action === "Clear Flag Starred Email") {
    return <AiOutlineStar className=" mt-2.5 h-5 w-5 text-[#757eaa]" />;
  } else if (item.action === "Add Flag Important Email") {
    return (
      <MdOutlineLabelImportant className=" mt-2.5 h-5 w-5 text-[#757eaa]" />
    );
  } else if (item.action === "Clear Flag Important Email") {
    return (
      <MdLabelImportantOutline className=" mt-2.5 h-5 w-5 text-[#757eaa]" />
    );
  } else if (item.action === "Send Email") {
    return <MdEmail className="mt-2.5 h-5 w-5 text-[#757eaa]" />;
  } else if (item.action === "Reply Email") {
    return <MdOutlineReply className=" mt-2.5 h-5 w-5 text-[#757eaa]" />;
  } else if (item.action === "Reply Email") {
    return (
      <TbArrowForwardUpDouble className=" mt-2.5 h-5 w-5 text-[#757eaa]" />
    );
  } else if (item.action === "Restaure Email") {
    return <TbRestore className=" mt-2.5 h-5 w-5 text-[#757eaa]" />;
  } else if (item.action === "Received Email") {
    return <MdMarkEmailRead className=" mt-2.5 h-5 w-5 text-[#757eaa]" />;
  } else if (item.action === "Update State Email") {
    return <TbStatusChange className=" mt-2.5 h-5 w-5 text-[#757eaa]" />;
  } else if (item.action === "chrono start") {
    return <GiStopwatch className=" mt-2.5 h-5 w-5 text-[#757eaa]" />;
  } else return;
};
const Log = ({ openLogDrawer, setOpenLogDrawer, thirdid, accountId360 }) => {
  const [dataLog, setDataLog] = useState([]);
  const [pageAllLog, setPageAllLog] = useState(1);
  const [pageLog, setPageLog] = useState(1);
  const [lastPageAllLog, setLastPageAllLog] = useState(1);
  const [date, setDate] = useState("");
  const [loading, setLoading] = useState(false);
  const { families } = useSelector((state) => state.families);
  const { accountId } = useParams();
  const [t] = useTranslation("common");

  const getLog = useCallback(async () => {
    setLoading(true);
    try {
      const response = await MainService.getLog(
        accountId ?? accountId360,
        pageAllLog,
        thirdid
      );
      if (response?.status === 200) {
        setDataLog((prevData) => [
          ...prevData,
          ...response?.data?.logValuesByDatePaginated.map((item, index) => ({
            key: item.date,
            color: "orange",
            children: (
              <div
                key={`children_${item.date}_${index}`}
                className="block space-y-2"
              >
                <p className="text-[12px] font-semibold">
                  {moment(item.date).format("ll")}
                </p>
                {item.logAction.data.map((val, i) => (
                  <div
                    key={`log_${i}_${val._id}`}
                    className="flex items-center space-x-3 space-y-2 py-1"
                  >
                    <div className="">{ConditionIcons(val)}</div>
                    <p className=" text-[14px] ">
                      {val.user[0] !== "JOB" &&
                      val.action !== "Assign Email" &&
                      val.action !== "chrono start" ? (
                        <strong className="text-[14px]">{val.user[0]}</strong>
                      ) : null}{" "}
                      <span
                        dangerouslySetInnerHTML={ConditionActions(
                          families,
                          val
                        )}
                      />
                      <span className="ml-2 ">
                        {moment(val.created_at).format("LT")}
                      </span>
                    </p>
                  </div>
                ))}
                {item.logAction.current_page < item.logAction.last_page ? (
                  <Button
                    id="btnShowMore"
                    type="text"
                    className=" text-[12px] font-bold text-[#727070]"
                    onClick={() => {
                      setPageLog((prevPage) => prevPage + 1);
                      setDate(item.date);
                    }}
                  >
                    {t("mailing.ShowMore")}
                  </Button>
                ) : null}
              </div>
            ),
          })),
        ]);
        setLastPageAllLog(response?.data?.pagination.last_page);
        setLoading(false);
      }
    } catch (error) {
      setLoading(false);
      console.log(error);
    }
  }, [pageAllLog, openLogDrawer]);

  const getLogDate = useCallback(async () => {
    // setLoadingDetailsLogs(true);
    try {
      setAttribute(true);
      const response = await MainService.getLogDetails(
        accountId ?? accountId360,
        pageLog,
        thirdid,
        date
      );
      if (response?.status === 200) {
        let newArray = [
          ...response?.data?.data[0].logAction?.data?.map((item, index) => {
            return (
              <div
                key={`log_${index}_${item._id}`}
                className="flex items-center space-x-3 space-y-2 py-1"
              >
                {ConditionIcons(item)}

                <p className=" text-[14px] ">
                  {item.user[0] !== "JOB" &&
                  item.action !== "Assign Email" &&
                  item.action !== "chrono start" ? (
                    <strong className="text-[14px]">{item.user[0]}</strong>
                  ) : null}{" "}
                  <span
                    dangerouslySetInnerHTML={ConditionActions(families, item)}
                  />
                  <span className="ml-2 ">
                    {moment(item.created_at).format("LT")}
                  </span>
                </p>
              </div>
            );
          }),
        ];

        setDataLog((prev) => {
          let array = [...prev];
          array.map((item) => {
            if (item.key === response?.data?.data[0].date) {
              item.children = Object.keys(item?.children).includes("props")
                ? [item?.children, ...newArray]
                : [...item.children, ...newArray];

              if (
                response?.data?.data[0].logAction.current_page <
                response?.data?.data[0].logAction.last_page
              ) {
                document.getElementById("btnShowMore")?.remove();

                item.children.push(
                  <Button
                    id="btnShowMore"
                    type="text"
                    className=" text-[12px] font-bold text-[#727070]"
                    onClick={() => {
                      setPageLog((prevPage) => prevPage + 1);
                      setDate(response?.data?.data[0].date);
                    }}
                  >
                    {t("mailing.ShowMore")}
                  </Button>
                );
              } else document.getElementById("btnShowMore")?.remove();
            }
            return item;
          });
          return array;
        });
      }
    } catch (error) {
      console.log(error);
    } finally {
      setAttribute(false);
    }
  }, [pageLog, thirdid, date]);

  useEffect(() => {
    if (openLogDrawer) getLog();
  }, [getLog, openLogDrawer]);

  useEffect(() => {
    if (pageLog > 1) getLogDate();
  }, [getLogDate]);

  const setAttribute = (value) => {
    try {
      const elem = document.getElementById("btnShowMore");
      if (value) elem?.setAttribute("disabled", true);
      else elem?.removeAttribute("disabled");
    } catch (error) {
      console.log(error);
    }
  };

  const handleNextPage = async () => {
    setPageAllLog((prevPage) => prevPage + 1);
  };

  return (
    <Drawer
      title={t("mailing.Historique")}
      onClose={() => setOpenLogDrawer(null)}
      width={500}
      open={openLogDrawer !== null}
      className="overflow-hidden"
    >
      {loading ? (
        <div className="flex justify-center ">
          <Spin />
        </div>
      ) : dataLog.length > 0 ? (
        <div className="w-[440px]" id="scrollableDiv">
          <InfiniteScroll
            // height={598}
            // height={200}
            dataLength={dataLog.length}
            next={handleNextPage}
            hasMore={pageAllLog < lastPageAllLog}
            // loader={<h4>Loading...</h4>}
            scrollableTarget="scrollableDiv"
          >
            <Timeline style={{ padding: "8px" }} items={dataLog} />
          </InfiniteScroll>
        </div>
      ) : (
        <div className="flex items-center justify-center py-[300px]">
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={t("mailing.noData")}
          />
        </div>
      )}
    </Drawer>
  );
};

export default Log;
