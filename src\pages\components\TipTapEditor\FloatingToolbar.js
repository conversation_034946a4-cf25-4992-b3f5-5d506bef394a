import React, { useEffect, useState } from "react";
import { createPortal } from "react-dom";
import { <PERSON><PERSON>, Tooltip } from "antd";
import {
  SettingOutlined,
  CloseOutlined,
  PlusOutlined,
  DeleteOutlined,
} from "@ant-design/icons";

const FloatingToolbar = ({ editor }) => {
  const [isVisible, setIsVisible] = useState(false); // For toolbar visibility
  const [isButtonVisible, setIsButtonVisible] = useState(false); // For "Settings" button visibility
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const [tableNode, setTableNode] = useState(null);

  useEffect(() => {
    if (!editor) return;

    const updateToolbarPosition = () => {
      const { state, view } = editor;
      const { selection } = state;

      // Check if the selection is within a table
      const isTableSelected = editor.isActive("table");

      if (isTableSelected) {
        const tableElement = view
          .domAtPos(selection.$anchor.pos)
          ?.node.closest("table");

        if (tableElement) {
          const rect = tableElement.getBoundingClientRect();

          // Position the toolbar based on the table
          setTableNode(tableElement);
          setPosition({
            top: rect.top + window.scrollY - 10, // Adjust to appear above the table
            left: rect.left + rect.width / 2, // Centered horizontally
          });

          setIsButtonVisible(true); // Show "Settings" button when table is visible
          return;
        }
      }

      // Reset if no table is selected
      setTableNode(null);
      setIsButtonVisible(false);
    };

    editor.on("selectionUpdate", updateToolbarPosition);

    return () => {
      editor.off("selectionUpdate", updateToolbarPosition);
    };
  }, [editor]);

  // Use IntersectionObserver to detect if the table is in view
  useEffect(() => {
    if (!tableNode) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const tableEntry = entries[0];
        if (!tableEntry.isIntersecting) {
          setIsButtonVisible(false); // Hide "Settings" button and toolbar
          setIsVisible(false);
        } else {
          setIsButtonVisible(true); // Show "Settings" button
        }
      },
      { root: null, threshold: 0.1 } // Trigger when 10% of the table is visible
    );

    observer.observe(tableNode);

    return () => {
      observer.disconnect();
    };
  }, [tableNode]);

  if (!tableNode) return null;

  return (
    <>
      {/* "Settings" Button */}
      {isButtonVisible &&
        createPortal(
          <div
            style={{
              position: "absolute",
              top: `${position.top}px`,
              left: `${position.left}px`,
              transform: "translateX(-50%)",
              zIndex: 1000,
            }}
          >
            <Button
              icon={<SettingOutlined />}
              onClick={() => setIsVisible((prev) => !prev)} // Toggle toolbar visibility
              shape="circle"
            />
          </div>,
          document.body
        )}

      {/* Toolbar */}
      {isVisible &&
        createPortal(
          <div
            style={{
              position: "absolute",
              top: `${position.top + 40}px`, // Adjust below the settings button
              left: `${position.left}px`,
              transform: "translateX(-50%)",
              background: "#fff",
              border: "1px solid #e0e0e0",
              borderRadius: "8px",
              padding: "8px",
              boxShadow: "0 4px 16px rgba(0, 0, 0, 0.1)",
              zIndex: 1000,
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
            }}
          >
            {/* Close Button */}
            <Button
              onClick={() => setIsVisible(false)}
              icon={<CloseOutlined />}
              style={{
                position: "absolute",
                top: "4px",
                right: "4px",
                background: "transparent",
                border: "none",
                cursor: "pointer",
              }}
            />

            {/* Toolbar Buttons */}
            <div style={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
              <Tooltip title="Add Column Before">
                <Button
                  icon={<PlusOutlined />}
                  onClick={() => editor.chain().focus().addColumnBefore().run()}
                  disabled={!editor.can().addColumnBefore()}
                />
              </Tooltip>
              <Tooltip title="Add Column After">
                <Button
                  icon={<PlusOutlined />}
                  onClick={() => editor.chain().focus().addColumnAfter().run()}
                  disabled={!editor.can().addColumnAfter()}
                />
              </Tooltip>
              <Tooltip title="Delete Column">
                <Button
                  icon={<DeleteOutlined />}
                  onClick={() => editor.chain().focus().deleteColumn().run()}
                  disabled={!editor.can().deleteColumn()}
                />
              </Tooltip>
              <Tooltip title="Add Row Before">
                <Button
                  icon={<PlusOutlined />}
                  onClick={() => editor.chain().focus().addRowBefore().run()}
                  disabled={!editor.can().addRowBefore()}
                />
              </Tooltip>
              <Tooltip title="Add Row After">
                <Button
                  icon={<PlusOutlined />}
                  onClick={() => editor.chain().focus().addRowAfter().run()}
                  disabled={!editor.can().addRowAfter()}
                />
              </Tooltip>
              <Tooltip title="Delete Row">
                <Button
                  icon={<DeleteOutlined />}
                  onClick={() => editor.chain().focus().deleteRow().run()}
                  disabled={!editor.can().deleteRow()}
                />
              </Tooltip>
              <Tooltip title="Delete Table">
                <Button
                  icon={<DeleteOutlined />}
                  onClick={() => editor.chain().focus().deleteTable().run()}
                  disabled={!editor.can().deleteTable()}
                />
              </Tooltip>
            </div>
          </div>,
          document.body
        )}
    </>
  );
};

export default FloatingToolbar;
