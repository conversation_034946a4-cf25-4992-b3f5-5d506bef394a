import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, Date<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import {
  FullscreenExitOutlined,
  FullscreenOutlined,
  PieChartOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";
import { useLocation } from "react-router-dom";
import { useSelector } from "react-redux";
import DashboardTicket from "pages/home/<USER>/ticket.stat/ticket";
import { useTranslation } from "react-i18next";
import { rangePresets } from "pages/voip/helpers/helpersFunc";
import Dashboard, { disabledDate } from "pages/home/<USER>";
import DashboardTasks from "pages/home/<USER>/task.stat/Task";
import DashboardVoip from "components/itemsDashboard/DashboardVoip";
import DashboardEmails from "pages/home/<USER>/email.stat/email";
import DashboardLead from "pages/home/<USER>/lead.stat/lead";
import DashboardContacts from "pages/home/<USER>/contact.stat/contact";

const StatsDrawer = ({ familyId }) => {
  const { user } = useSelector((state) => state?.user);
  const userPreferences = useSelector(
    (state) => state?.TasksRealTime?.userPreferences
  );
  const { dataAccounts } = useSelector((state) => state.mailReducer);
  const [keyDrawerStats, setKeyDrawerStats] = useState(0);
  const [start, setStart] = useState("");
  const [end, setEnd] = useState("");
  const [open, setOpen] = useState(false);
  const [full, setFull] = useState(false);

  const [t] = useTranslation("common");
  const location = useLocation();
  // useEffect(() => {
  //   if (user?.location && open) {
  //     if (user?.location.date_format) {

  //       setStart(dayjs(dayjs(), user?.location?.date_format));
  //       setEnd(dayjs(dayjs(), user?.location?.date_format));
  //     } else {
  //       setStart(dayjs().format("YYYY-MM-DD"));
  //       setEnd(dayjs().format("YYYY-MM-DD"));
  //     }
  //   }
  // }, [user, open]);
  const showDrawer = () => {
    setKeyDrawerStats((prev) => prev + 1);
    setOpen(true);
  };
  const onClose = () => {
    setOpen(false);
  };
  useEffect(() => {
    if (!open) {
      setStart("");
      setEnd("");
    }
  }, [open]);

  return (
    <>
      {familyId === "email" &&
      dataAccounts.find((el) => el.selected)?.shared === "0" ? null : (
        <Tooltip title={t("menu1.stats")}>
          <Button
            type={
              familyId === "task" || familyId === "email" ? "text" : "default"
            }
            icon={<PieChartOutlined style={{ fontSize: 18 }} />}
            shape={familyId === "task" ? "circle" : "default"}
            onClick={showDrawer}
          />
        </Tooltip>
      )}
      <Drawer
        title={
          <div className="flex items-center justify-between">
            <span>{t("menu1.stats")}</span>
            <span className="flex items-center space-x-1">
              {start && end ? (
                <DatePicker.RangePicker
                  // value={[start, end]}
                  // disabled={key === "4"}
                  defaultValue={[start, end]}
                  allowClear={false}
                  style={{ width: "100%" }}
                  disabledDate={disabledDate}
                  format={user?.location?.date_format}
                  presets={rangePresets(t)}
                  onChange={(date, dateString) => {
                    setStart(dateString[0]);
                    setEnd(dateString[1]);
                  }}
                />
              ) : (
                <DatePicker.RangePicker
                  // value={[start, end]}
                  // disabled={key === "4"}
                  defaultValue={[dayjs(), dayjs()]}
                  allowClear={false}
                  style={{ width: "100%" }}
                  disabledDate={disabledDate}
                  format={user?.location?.date_format}
                  presets={rangePresets(t)}
                  onChange={(date, dateString) => {
                    setStart(dateString[0]);
                    setEnd(dateString[1]);
                  }}
                />
              )}
              <Button
                type="text"
                icon={
                  full ? <FullscreenExitOutlined /> : <FullscreenOutlined />
                }
                shape="circle"
                onClick={() => setFull(!full)}
              />
            </span>
          </div>
        }
        width={full ? "calc(100vw - 65px)" : 1040}
        onClose={onClose}
        open={open}
        key={keyDrawerStats}
        styles={{ body: { padding: 0 } }}
      >
        <div className="bg-[#F8FAFC] px-3 pt-3">
          {familyId === 6 ? (
            <DashboardTicket
              start={start || dayjs().format(user?.location?.date_format)}
              end={end || dayjs().format(user?.location?.date_format)}
              from={full ? "full-drawer" : "drawer"}
              idPipeline={
                userPreferences?.find(
                  (el) => el?.currentRoute === location?.pathname
                )?.selectedPipeline
              }
            />
          ) : familyId === 9 ? (
            <DashboardLead
              start={start || dayjs().format(user?.location?.date_format)}
              end={end || dayjs().format(user?.location?.date_format)}
              from={full ? "full-drawer" : "drawer"}
            />
          ) : familyId === 2 ? (
            <DashboardContacts
              start={start || dayjs().format(user?.location?.date_format)}
              end={end || dayjs().format(user?.location?.date_format)}
              from={full ? "full-drawer" : "drawer"}
            />
          ) : familyId === "task" ? (
            <DashboardTasks
              start={start || dayjs().format(user?.location?.date_format)}
              end={end || dayjs().format(user?.location?.date_format)}
              from={full ? "full-drawer" : "drawer"}
            />
          ) : familyId === "voip" ? (
            <DashboardVoip
              start={start || dayjs().format(user?.location?.date_format)}
              end={end || dayjs().format(user?.location?.date_format)}
              from={full ? "full-drawer" : "drawer"}
            />
          ) : familyId === "email" ? (
            <DashboardEmails
              IdSelectedEmail={dataAccounts.find((el) => el.selected)?.value}
              start={start || dayjs().format(user?.location?.date_format)}
              end={end || dayjs().format(user?.location?.date_format)}
              from={full ? "full-drawer" : "drawer"}
            />
          ) : null}
        </div>
      </Drawer>
    </>
  );
};

export default StatsDrawer;
