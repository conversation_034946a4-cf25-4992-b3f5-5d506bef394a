import React, { useEffect, useRef } from "react";
import { Form, InputNumber, Input, Select, Space, Badge, Button } from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { PlusCircleOutlined, PlusOutlined } from "@ant-design/icons";
import { colors } from "./Colors";

import { useParams } from "react-router-dom";
import { toastNotification } from "./ToastNotification";
import ColumnColors from "./ColumnColors";
import NewTableDraggable from "./NewTableDraggable";
import LabelTable from "./LabelTable";
import { SubmitKeyPress } from "../utils/SubmitKeyPress";
const itemsActions = ["Sms", "email", "Suiveur", "Etat", "Step", "Score"];

const Rules = ({ trigger_id, setEditingKey, editingKey, data, setData }) => {
  const [form] = Form.useForm();
  const [count, setCount] = useState(0);

  const [currentPage, setCurrentPage] = useState(1);
  const [id, setId] = useState(null);
  const [saveData, setSaveData] = useState([]);
  const [loading, setLoading] = useState(false);
  const isEditing = (record) => record.key === editingKey;
  const [t] = useTranslation("common");
  const [rank, setRank] = useState(null);
  const [errorForm, setErrorForm] = useState(false);
  const [pageSize, setPageSize] = useState(20);
  const inputRefs = useRef([]);
  const { family } = useParams();
  //   useEffect(() => {
  //     setData([]);
  //   }, [family]);
  useEffect(() => {
    inputRefs.current.forEach((input) => {
      input?.focus();
    });
  }, [data.length, id, rank]);
  const handleKeyPress = (event) => {
    SubmitKeyPress(event, form);
  };
  const onFinishFailed = (values) => {};
  function handleInputNumberKeyDown(e) {
    if (e.key === "." && e.target.value.includes(".")) {
      e.preventDefault();
    }
    if (e.key === "-" && !e.target.value) {
      e.preventDefault();
    }
    if (e.key === "e") {
      e.preventDefault();
    }
    if (
      isNaN(parseInt(e.key)) &&
      ![
        "Tab",
        "Backspace",
        "Delete",
        "ArrowLeft",
        "ArrowRight",
        "Enter",
      ].includes(e.key)
    ) {
      e.preventDefault();
    }
  }

  const onRow = () => {};

  const EditableCell = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    const inputNode =
      inputType === "select" ? (
        <Select
          showSearch
          placeholder={t("tags.selectcolor")}
          style={{
            width: 200,
          }}
          options={itemsActions.map((el) => ({
            label: (
              <Space>
                <Badge color={el} /> {el}
              </Space>
            ),
            value: el,
          }))}
          optionFilterProp="children"
          // filterOption={(input, option) =>
          //   (
          //     colors
          //       .find((el) => el.value === option.value)
          //       ?.label?.toLowerCase() ?? ""
          //   ).includes(input.toLowerCase())
          // }
          // filterSort={(optionA, optionB) =>
          //   (optionA?.value ?? "")
          //     .toLowerCase()
          //     .localeCompare((optionB?.value ?? "").toLowerCase())
          // }
          allowClear
        />
      ) : (
        <Input
          ref={(el) => (inputRefs.current[index] = el)}
          placeholder="Stage name"
          onKeyPress={handleKeyPress}
        />
      );
    return (
      <td {...restProps}>
        {editing ? (
          <Form.Item
            name={dataIndex.toLowerCase()}
            style={{
              margin: 0,
            }}
            rules={[
              {
                required: dataIndex === "label" ? true : false,
                message: "Stage name is required !",
              },
            ]}
          >
            {inputNode}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };
  const edit = (record) => {
    if (record) {
      form.setFieldsValue({
        label: record.label,
      });
      setRank(record.rank);
      setId(record.id);
    } else {
      form.setFieldsValue({
        label: "",
      });
    }
    setEditingKey(record.key);
  };
  const cancel = (record) => {
    setEditingKey("");
    setErrorForm(false);

    setId(null);
    if (!record.id) {
      setData(data.filter((item) => item.key !== record.key));
    }
  };
  const save = async (key) => {};
  //   useEffect(() => {
  //     const getStages = async () => {
  //       setLoading(true);
  //       try {
  //         const {
  //           data: { data },
  //         } = await axiosInstance.get(
  //           `/stages/getStagesByPipeline/${trigger_id}`
  //         );
  //         setData(data.map((el, i) => ({ ...el, key: el.id, rank: i + 1 })));
  //         setSaveData(data.map((el, i) => ({ ...el, key: el.id, rank: i + 1 })));
  //         if (data.length > 0) {
  //           setCount(Math.max(...data.map((el) => el.id)));
  //         }

  //         setLoading(false);
  //       } catch (err) {
  //         setLoading(false);
  //         toastNotification("error", t("toasts.somethingWrong"), "topRight");
  //       }
  //     };
  //     if (trigger_id) getStages();
  //     return;
  //   }, [trigger_id]);
  const handleClick = (event) => {
    event.stopPropagation();
  };
  const columns = [
    {
      title: t("activities.name"),
      dataIndex: "label",
      key: "label",
      editable: true,
      sorter: (a, b) => a.label.localeCompare(b.label),
      render: (_, record) => {
        return (
          <LabelTable record={record} editingKey={editingKey} edit={edit} />
        );
      },
    },

    {
      title: "Action",
      dataIndex: "action",
      key: "action",
      editable: true,
    },
  ];

  const handleAdd = () => {
    setId(null);
    const ids = data.map((object) => {
      return object.id;
    });
    const newData = {
      key: Math.max(...ids) + 1,
      label: `  `,

      disabled: true,
    };
    setCurrentPage(Math.ceil([...data, newData].length / pageSize));

    setData([...data, newData]);
    form.setFieldsValue({
      label: "",
    });
    setEditingKey(Math.max(...ids) + 1);
    setCount(Math.max(...ids) + 1);
  };
  return (
    <Space direction="vertical" style={{ width: "100%" }}>
      <Space direction="vertical" style={{ width: "100%" }}>
        <div className="flex items-center justify-between pt-3">
          <span className="ml-2 mr-2 rounded	 px-2.5 py-0.5 text-base font-medium text-[#2253d5] dark:bg-blue-200 dark:text-blue-800"></span>

          {trigger_id ? (
            <Button
              shape="circle"
              icon={<PlusOutlined />}
              disabled={loading ? true : editingKey ? true : false}
              onClick={handleAdd}
              type="primary"
            />
          ) : (
            ""
          )}
        </div>
      </Space>

      <NewTableDraggable
        columns={columns}
        setLoading={setLoading}
        isEditing={isEditing}
        data={data}
        setData={setData}
        loading={loading}
        onRow={onRow}
        save={save}
        edit={edit}
        EditableCell={EditableCell}
        onFinishFailed={onFinishFailed}
        cancel={cancel}
        form={form}
        apiRank="/stages/updateRank"
        editingKey={editingKey}
        api="stages"
        btnText={t("helpDesk.addFolder")}
        pagination={false}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        pageSize={pageSize}
        setPageSize={setPageSize}
      />
      <div style={{ width: "0" }}>
        <Button
          icon={<PlusCircleOutlined />}
          disabled={loading ? true : editingKey ? true : false}
          onClick={handleAdd}
          type="link"
          block
        ></Button>
      </div>
    </Space>
  );
};
export default Rules;
