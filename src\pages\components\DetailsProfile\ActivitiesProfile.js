import { Tabs } from "antd";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import Overview from "./Activities/Overview";
import AllActivities from "./Activities/AllActivities";
import { useEffect } from "react";
import MainService from "../../../services/main.service";
import { useParams } from "react-router-dom";

const ActivitiesProfile = () => {
  const [t] = useTranslation("common");
  const [selectedValue, setSelectedValue] = useState("1");
  const [load, setLoad] = useState(true);
  const [tasks360, setTasks360] = useState([]);
  const params = useParams();
  useEffect(() => {
    const getTasks = async () => {
      try {
        const response = await MainService.getTasks360({ id: params.id });
        setTasks360(response?.data);
      } catch (error) {
        console.log(`Error ${error}`);
      }
    };
    getTasks();
  }, []);

  const items = [
    {
      key: "1",
      label: t("layout_profile_details.overview"),
      children: <Overview />,
    },
    {
      key: "2",
      label: t("layout_profile_details.activities"),
      children: (
        <AllActivities
          selectedValue={selectedValue}
          setSelectedValue={setSelectedValue}
          load={load}
          setLoad={setLoad}
          tasks360={tasks360}
        />
      ),
    },
  ];
  const onChangeTab = (e) => {
    setSelectedValue("1");
   // console.log(e);
  };
  const onChange = (key) => {
    console.log(key);
  };
  const onEdit = (key) => {
    console.log(key);
  };
  const onRemove = (key) => {
    console.log(key);
  };
  return (
    <div>
      <Tabs onChange={onChangeTab} type="card" items={items} />
    </div>
  );
};

export default ActivitiesProfile;
