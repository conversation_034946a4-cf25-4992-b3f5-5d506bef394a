import { memo, useCallback, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { Button, Divider, Modal, Space, Spin, Select, Form } from "antd";
import { TeamOutlined } from "@ant-design/icons";
import { HiOutlineBuildingOffice } from "react-icons/hi2";
import { HeartHandshake } from "lucide-react";
import { CgUserlane } from "react-icons/cg";
import { URL_ENV } from "index";
import { toastNotification } from "components/ToastNotification";
import { debounce } from "lodash";
import { HighlightSearchW } from "pages/voip/components";
import DisplayAvatar from "pages/voip/components/DisplayAvatar";
import MainService from "services/main.service";

const FAMILY_ICONS = {
  1: <HiOutlineBuildingOffice style={{ fontSize: "16px", marginTop: "5px" }} />,
  2: <TeamOutlined style={{ fontSize: "16px", marginTop: "5px" }} />,
  3: <HeartHandshake size={18} style={{ marginTop: 7 }} />,
  9: <CgUserlane style={{ fontSize: "16px", marginTop: "5px" }} />,
};
//
const getAvatarUrl = (avatar) => {
  return (
    avatar &&
    `${
      URL_ENV?.REACT_APP_BASE_URL + URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
    }${avatar}`
  );
};

const handleApiError = (err, t) => {
  if (err.name !== "CanceledError" && err?.response?.status !== 401) {
    toastNotification("error", t("toasts.somethingWrong"), "topRight");
  }
};

const getFamilyLabel = (familyId, t) => {
  const families = {
    1: t("contacts.company"),
    2: t("contacts.contact"),
    3: t("import.deal"),
    9: t("contacts.leads"),
  };
  return families[familyId];
};

const getAccessKey = (key) => {
  const accessMap = {
    1: "companies",
    2: "contact",
    3: "deals",
    9: "leads",
  };
  return accessMap[key];
};

const getFamiliesOption = (access, t) => {
  return Object.entries(FAMILY_ICONS)
    .filter(([key]) => access?.[getAccessKey(key)] === "1")
    .map(([key, icon]) => ({
      label: (
        <Space>
          {icon}
          <p>{getFamilyLabel(key, t)}</p>
        </Space>
      ),
      value: Number(key),
    }));
};

//
const ModalIdentification = ({
  open,
  setOpen,
  record,
  usedAccount,
  setDataSource,
}) => {
  //
  const [t] = useTranslation("common");
  const selectRef = useRef(null);
  //
  const user = useSelector((state) => state.user?.user);
  //
  const { identification } = record;
  //
  const [state, setState] = useState({
    selectedFamily: identification?.family_id ?? null,
    loadingOptions: false,
    fetchedOptions: identification?.id
      ? [
          {
            key: identification.identificationId,
            value: `${identification.identificationId}-${identification.label_data}`,
            label: (
              <div className="flex items-center space-x-1">
                <DisplayAvatar
                  size={30}
                  name={identification.label_data}
                  urlImg={
                    identification.avatar && getAvatarUrl(identification.avatar)
                  }
                />

                <p className="truncate font-semibold leading-4">
                  {identification.label_data}
                </p>
              </div>
            ),
          },
        ]
      : [],
    selectedElement: identification?.identificationId ?? null,
    saveIsLoading: false,
  });

  const {
    selectedFamily,
    loadingOptions,
    fetchedOptions,
    selectedElement,
    saveIsLoading,
  } = state;
  //
  const updateState = (newState) => {
    setState((prev) => ({ ...prev, ...newState }));
  };
  //
  const handleCancel = useCallback(() => {
    setOpen({});
    updateState({
      selectedFamily: null,
      fetchedOptions: [],
      selectedElement: null,
    });
  }, [setOpen]);
  //
  const fetchOptions = useCallback(
    async (searchText, familyID) => {
      try {
        updateState({ loadingOptions: true });
        const {
          data: { data },
        } = await MainService.getIdentifSelect(
          familyID,
          record?.from?.[0]?.address,
          1,
          searchText
        );
        const options = data?.map((e) => ({
          key: e.id,
          value: `${e._id}-${e.label}`,
          label: (
            <div className="flex items-center space-x-1">
              <DisplayAvatar
                size={30}
                name={e.label}
                urlImg={e.avatar && getAvatarUrl(e.avatar)}
              />
              <div>
                <p className="truncate font-semibold leading-4">
                  {HighlightSearchW(e.label || e.reference, searchText || "")}
                </p>
                {e.label && e.reference && (
                  <p className="text-xs leading-4 text-slate-500">
                    {e.reference}
                  </p>
                )}
              </div>
            </div>
          ),
        }));

        updateState({ fetchedOptions: options });
      } catch (err) {
        handleApiError(err, t);
      } finally {
        updateState({ loadingOptions: false });
      }
    },
    [record?.from?.address, t]
  );
  //
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const handleSearch = useCallback(
    debounce((value, familyId) => {
      fetchOptions(value, familyId);
    }, 300),
    []
  );
  //
  if (!open || !record) return null;
  //
  const saveAffectation = async () => {
    try {
      updateState({ saveIsLoading: true });
      const formData = new FormData();
      formData.append("emailId", record?.key);
      formData.append("familyId", selectedFamily);
      formData.append("id", selectedElement);
      formData.append("account_id", usedAccount?.value);
      usedAccount?.departmentId?.forEach((id) => {
        formData.append("departement_id[]", id);
      });
      const response = await MainService.storeIdentifEmail(formData);

      setDataSource((prev) =>
        prev.map((email) =>
          email.key === record?.key
            ? {
                ...email,
                identification: {
                  ...response.data.data,
                },
              }
            : email
        )
      );
      handleCancel();
      toastNotification(
        "success",
        t("mailing.identifiedSuccess"),
        "topRight",
        3
      );
    } catch (err) {
      handleApiError(err, t);
    } finally {
      updateState({ saveIsLoading: false });
    }
  };
  //
  const modalTitle = (
    <div className="relative flex flex-grow flex-col">
      <div className="space-y-1">
        <p className="text-base font-semibold">
          {identification?.id
            ? t("mailing.updateIdentification")
            : "Identification"}
        </p>
        <div className="flex space-x-1 text-sm">
          <p className="whitespace-nowrap font-semibold">
            {t("emailTemplates.subjectMail")}:
          </p>
          <p className="truncate">{record?.subject}</p>
        </div>
      </div>
      <Divider style={{ margin: "5px 0 0 0" }} />
    </div>
  );
  //
  //
  return (
    <Modal
      width={450}
      title={modalTitle}
      open={open}
      onCancel={handleCancel}
      maskClosable={false}
      footer={[
        <Button
          //   key="submit"
          key="save-button"
          type="primary"
          loading={saveIsLoading}
          onClick={saveAffectation}
          disabled={
            !selectedFamily ||
            !selectedElement ||
            identification?.identificationId === selectedElement
          }
        >
          {t("voip.save")}
        </Button>,
      ]}
    >
      <Form layout="vertical">
        <Form.Item
          label={t("voip.selectModule")}
          name="module"
          rules={[
            {
              required: true,
              // message: "",
            },
          ]}
          initialValue={selectedFamily}
        >
          <Select
            allowClear
            style={{ width: "100%" }}
            options={getFamiliesOption(user.access, t)}
            onChange={(value) => {
              updateState({ selectedFamily: value });
              if (value) {
                fetchOptions("", value);
                if (selectRef.current) selectRef.current.focus();
              }
            }}
          />
        </Form.Item>

        <Form.Item
          label={
            selectedFamily ? (
              `${t("voip.search_select")} ${getFamilyLabel(selectedFamily, t)}`
            ) : (
              <span className=" text-white">Hello Genius</span>
            )
          }
          name="element"
          rules={[
            {
              required: selectedFamily,
              // message: "",
            },
          ]}
          initialValue={
            identification?.id
              ? `${identification.identificationId}-${identification.label_data}`
              : null
          }
        >
          <Select
            ref={selectRef}
            // allowClear
            showSearch
            style={{ width: "100%" }}
            options={fetchedOptions}
            loading={loadingOptions}
            disabled={!selectedFamily}
            onSelect={(_, option) =>
              updateState({ selectedElement: option.key })
            }
            onSearch={(value) => handleSearch(value, selectedFamily)}
            suffixIcon={<Spin spinning={loadingOptions} size="small" />}
            notFoundContent={loadingOptions ? <Spin size="small" /> : false}
          />
        </Form.Item>
      </Form>
      <Divider />
    </Modal>
  );
};
//
//
export default memo(ModalIdentification);
