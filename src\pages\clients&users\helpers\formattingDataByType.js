import dayjs from "dayjs";
//
const getPathUrl = (url) => url?.split("/").filter(Boolean).pop() || "";

//
const formattingDataByType = {
  create: (type, key, value, formData, config) => {
    if (!key || !type) return;
    switch (type) {
      case "image":
        !!value?.fileList?.length &&
          formData.append(`field[${key}]`, value?.fileList?.[0]?.originFileObj);
        break;
      case "album":
        value?.fileList?.forEach((file) =>
          formData.append(`field[${key}][]`, file?.originFileObj)
        );
        break;
      case "date":
        formData.append(
          `field[${key}]`,
          dayjs(value).format(config?.date_format)
        );
        break;
      case "date_time":
        formData.append(
          `field[${key}]`,
          dayjs(value).format(`${config?.date_format} HH:mm`)
        );
        break;
      case "range":
        formData.append(
          `field[${key}][]`,
          dayjs(value[0]).format(config?.date_format)
        );
        formData.append(
          `field[${key}][]`,
          dayjs(value[1]).format(config?.date_format)
        );
        break;
      case "time":
        formData.append(`field[${key}]`, dayjs(value).format("HH:mm"));
        break;
      case "phone":
      case "phone_number":
      case "monetary":
        value?.length === 2 &&
          value?.[0] &&
          value?.[1] &&
          value?.forEach((val) => formData.append(`field[${key}][]`, val));
        break;
      case "checkbox":
      case "multiselect":
        value?.forEach((val) => formData.append(`field[${key}][]`, val));
        break;
      case "country":
        if (Array.isArray(value))
          value?.forEach((val) => formData.append(`field[${key}][]`, val));
        else formData.append(`field[${key}]`, value);
        break;
      default:
        formData.append(`field[${key}]`, value);
        break;
    }
  },
  /////////////////////////////////////////////////
  update: (type, key, value, formData, config) => {
    if (!key || !type) return;
    switch (type) {
      case "image":
        if (value?.fileList?.length) {
          formData.append(`field[${key}]`, value.fileList[0]?.originFileObj);
        }
        if (Array.isArray(value) && value?.length) {
          if (value?.[0]?.status === "done") {
            formData.append(`field[${key}][file_name]`, value?.[0]?.name);
            formData.append(`field[${key}][path]`, getPathUrl(value?.[0]?.url));
            //  formattingFilesPhotosPayload(value?.[0]?.name, value?.[0]?.url, key, formData, type)
          } else formData.append(`field[${key}]`, value?.[0]?.originFileObj);
        }
        break;
      case "album":
        const fileListAlbum = value?.fileList || value;
        if (!!fileListAlbum?.length) {
          fileListAlbum?.forEach((val, i) => {
            if (val?.status === "done") {
              formData.append(`field[${key}][${i}][file_name]`, val?.name);
              formData.append(
                `field[${key}][${i}][path]`,
                getPathUrl(val?.url)
              );
            } else formData.append(`field[${key}][${i}]`, val?.originFileObj);
          });
        }
        break;
      case "file":
        const fileListFile = value?.value?.fileList || value?.value;
        if (!!fileListFile?.length) {
          fileListFile?.forEach((val, i) => {
            if (val?.status === "done") {
              formData.append(`field[${key}][${i}][file_name]`, val?.name);
              formData.append(
                `field[${key}][${i}][path]`,
                getPathUrl(val?.url)
              );
            } else formData.append(`field[${key}][${i}]`, val?.originFileObj);
          });
          formData.append(`filename[${key}]`, value?.filename ?? "");
          formData.append(`isActive[${key}]`, !!value?.isActive);
        }
        break;
      case "date":
        typeof value === "string"
          ? formData.append(`field[${key}]`, value)
          : formData.append(
              `field[${key}]`,
              dayjs(value).format(config?.date_format)
            );
        break;
      case "date_time":
        typeof value === "string"
          ? formData.append(`field[${key}]`, value)
          : formData.append(
              `field[${key}]`,
              dayjs(value).format(`${config?.date_format} HH:mm`)
            );
        break;
      case "range":
        if (Array.isArray(value)) {
          if (
            typeof value?.[0] === "string" &&
            typeof value?.[1] === "string"
          ) {
            value?.forEach((val) => formData.append(`field[${key}][]`, val));
          } else {
            value?.forEach((val) =>
              formData.append(
                `field[${key}][]`,
                dayjs(val).format(config?.date_format)
              )
            );
          }
        }
        break;
      case "time":
        typeof value === "string"
          ? formData.append(`field[${key}]`, value)
          : formData.append(`field[${key}]`, dayjs(value).format("HH:mm"));
        break;
      case "phone":
        if (Array.isArray(value) && value.length === 2 && value[1] !== null) {
          const hasTwoNonEmptyStrings = value.every(
            (val) => typeof val === "string" && val !== ""
          );

          if (hasTwoNonEmptyStrings) {
            value?.forEach((val) => formData.append(`field[${key}][]`, val));
          }
        }
        break;
      case "monetary":
        if (
          Array.isArray(value) &&
          value?.length === 2 &&
          value[0] &&
          value[1]
        ) {
          value?.forEach((val) => formData.append(`field[${key}][]`, val));
        }

        break;
      case "checkbox":
      case "multiselect":
        if (Array.isArray(value) && value?.length > 0) {
          value?.forEach((val) => formData.append(`field[${key}][]`, val));
        }
        break;
      case "country":
        if (Array.isArray(value) && value?.length)
          value?.forEach((val) => formData.append(`field[${key}][]`, val));
        else if (value) formData.append(`field[${key}]`, value);
        break;
      case "autocomplete":
        if (value && typeof value == "string")
          formData.append(`field[${key}]`, value);
        else if (value) formData.append(`field[${key}]`, value?.id);
        break;
      default:
        !!value &&
          !(key?.includes("filename") || key?.includes("isActive")) &&
          formData.append(`field[${key}]`, value);
        break;
    }
  },
};

export default formattingDataByType;
