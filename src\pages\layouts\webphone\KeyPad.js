import { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from "react";
import { useSelector } from "react-redux";

import { <PERSON><PERSON>, <PERSON>, Config<PERSON><PERSON><PERSON>, Divider, Empty, Row } from "antd";
import { RiDeleteBack2Fill } from "react-icons/ri";

import { useTranslation } from "react-i18next";
import useActionCall from "../../voip/helpers/ActionCall";
import { suggestLog } from "../../voip/helpers/helpersFunc";
import { loaderOptionTemplate, renderItem, transformData } from "./call";
import MainService from "../../../services/main.service";
import { toastNotification } from "../../../components/ToastNotification";
import { debounce } from "lodash";
import "./index.css";
import { openDrawerChat } from "new-redux/actions/voip.actions/handleDrawerChat";
import { useDispatch } from "react-redux";
import { HiOutlinePhone } from "react-icons/hi2";

//
const key = [
  { number: "1", key: null },
  { number: "2", key: "ABC" },
  { number: "3", key: "DEF" },
  { number: "4", key: "GHI" },
  { number: "5", key: "JKL" },
  { number: "6", key: "MNO" },
  { number: "7", key: "PQRS" },
  { number: "8", key: "TUV" },
  { number: "9", key: "WXYZ" },
  { number: "*", key: null },
  { number: "0", key: "+" },
  { number: "#", key: "" },
];
//
const style = {
  background: "#F1F5F9",
  padding: "0",
};
const regexNumber = /^[0-9*#]*$/;

const KeyPad = ({ search, setSearch, isKeyPadUp, setIsKeyPadUp }) => {
  //
  const [t] = useTranslation("common");
  const actionCall = useActionCall();
  const dispatch = useDispatch();

  const log = useSelector((state) => state.voip.logs);
  const user = useSelector((state) => state.user.user);

  const userPoste = `${user?.extension}`;
  const dialCode = useSelector(
    ({ user: { user } }) => user?.location?.dial_code
  );
  //
  const [searchOptions, setSearchOptions] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const [displayValue, setDisplayValue] = useState("");
  //
  const fetchSearchOptions = useCallback(async () => {
    try {
      setIsLoading(true);
      if (!search?.length) return;
      const { data } = await MainService.searchByNumOrName({
        key: search?.replace("+", "00"),
      });
      if (!data?.length) {
        setSearchOptions([]);
        return;
      }
      const transformedOptions = transformData(data, search, dialCode, t);
      setSearchOptions(transformedOptions);
    } catch (err) {
      if (err?.response?.status !== 401) {
        setSearchOptions([]);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
      throw new Error(err?.message ? err.message : err);
    } finally {
      setIsLoading(false);
    }
  }, [dialCode, search, t]);

  useEffect(() => {
    fetchSearchOptions();
  }, [fetchSearchOptions]);
  //
  const suggestLogOptions = useMemo(
    () => suggestLog(log, userPoste, t),
    [log, t, userPoste]
  );
  //
  const handleOpenMgsDrawer = useCallback(
    (uuid) => {
      dispatch(openDrawerChat(uuid));
    },
    [dispatch]
  );
  //
  const options = useMemo(() => {
    if (!search.length) {
      return [
        {
          key: "key_suggested",
          label: (
            <span className="text-xs font-semibold">{t("voip.suggested")}</span>
          ),
          options: suggestLogOptions.map((item) =>
            renderItem(item, search, handleOpenMgsDrawer, actionCall, t)
          ),
        },
      ];
    } else {
      return searchOptions.map((item, i) => ({
        key: `${item.key}_${i}`,
        label: <span className="text-xs font-semibold	">{item.label}</span>,
        options: item.values
          .slice(0, 5)
          .map((value) =>
            renderItem(value, search, handleOpenMgsDrawer, actionCall, t)
          ),
      }));
    }
  }, [
    actionCall,
    handleOpenMgsDrawer,
    search,
    searchOptions,
    suggestLogOptions,
    t,
  ]);
  //
  return (
    <div className="scroll_bar relative h-[23rem]">
      <div
        className={`overflow-y-auto px-1 pb-6 ${
          isKeyPadUp ? "h-1/3" : "h-[23rem]"
        }`}
      >
        {isLoading ? (
          loaderOptionTemplate(2)
        ) : options.length ? (
          options?.map((item, index) => (
            <div className="space-y-1" key={item.key + index}>
              <Divider
                orientation="left"
                style={{ margin: "8px 0px 4px 0" }}
                className="text-slate-400"
              >
                {item?.label}
              </Divider>
              <div>
                {item?.options?.map((option) => (
                  <div
                    className="cursor-pointer px-2 py-1.5 hover:bg-slate-200"
                    key={option?.key}
                  >
                    {option.label}
                  </div>
                ))}
              </div>
            </div>
          ))
        ) : (
          <div className="flex justify-center p-4 text-sm font-semibold">
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={"No Contacts Found"}
            />
          </div>
        )}
      </div>
      {isKeyPadUp ? (
        <Keys
          setIsKeyPadUp={setIsKeyPadUp}
          search={search}
          setSearch={setSearch}
          t={t}
          actionCall={actionCall}
          displayValue={displayValue}
          setDisplayValue={setDisplayValue}
        />
      ) : null}
    </div>
  );
};

export function Keys({
  search,
  setSearch,
  t,
  actionCall,
  displayValue,
  setDisplayValue,
}) {
  //
  const inputRef = useRef(null);

  //
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedSearch = useCallback(
    debounce((nextValue) => setSearch(nextValue), 300),
    []
  );
  //
  const handleChange = (value, method) => {
    let text;
    if (method === "delete_last_chart") {
      text = displayValue.slice(0, -1);
      setDisplayValue(text);
      debouncedSearch(text);
    } else {
      // text = value?.trim();
      text = value?.replace(/[^0-9+*#]+/g, "");
      // if (regexNumber.test(text) || text === "") {
      setDisplayValue(text);
      debouncedSearch(text);
      // }
    }
  };

  const focusInput = () => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };
  // const keypadClass = isKeyPadUp ? "keypad-visible" : "keypad-hidden";
  //
  return (
    <div className={`z-10 bg-slate-100 px-2 `}>
      <div className=" relative flex  h-8 items-center justify-center px-2 ">
        <input
          autoFocus
          ref={inputRef}
          type="text"
          className="centered-input"
          value={displayValue}
          onChange={({ target: { value } }) => handleChange(value)}
          onKeyDown={(e) => {
            if (e.key === "Enter" && e.target.value.trim() !== "") {
              actionCall(e.target.value.trim());
            }
          }}
        />
        {search?.length ? (
          <Button
            className="absolute right-6"
            type="link"
            size="small"
            onClick={() => handleChange(null, "delete_last_chart")}
            icon={
              <RiDeleteBack2Fill
                className=" text-slate-400 hover:text-slate-500"
                style={{ fontSize: 18 }}
              />
            }
          />
        ) : null}
      </div>
      <Divider style={{ margin: "0 0 6px 0" }} />
      <div className="px-4">
        <Row gutter={[8, 4]}>
          {key.map(({ number, key }) => (
            <Col key={number} className="gutter-row" span={8}>
              <div style={style} key={number}>
                <Button
                  key={number}
                  // size="large"
                  block="true"
                  className={"flex h-9 items-center justify-center"}
                  onClick={() => {
                    handleChange(displayValue + number);
                    focusInput();
                  }}
                >
                  <div className="grid items-center">
                    <span
                      style={{
                        marginTop: (number === "*" || key) && 5,
                        marginBottom:
                          number === "1" ? 10 : number === "0" ? -4 : null,
                        fontSize:
                          number === "*" ? 24 : number === "#" ? 17 : null,
                      }}
                      className=" font-semibold leading-4"
                    >
                      {number}
                    </span>
                    <span
                      style={{ fontSize: key === "+" ? 14 : 10 }}
                      className="leading-4 	 text-slate-400 "
                    >
                      {key}
                    </span>
                  </div>
                </Button>
              </div>
            </Col>
          ))}
        </Row>
      </div>

      <div className=" px-4  pt-1.5">
        <Row justify="center" gutter={[8, 4]}>
          <Col className="gutter-row" span={8}>
            <ConfigProvider
              theme={{
                components: {
                  Button: {
                    colorPrimary: "#00B96B",
                    algorithm: true,
                  },
                },
              }}
            >
              <Button
                type="primary"
                block="true"
                className={"flex h-9 items-center justify-center"}
                disabled={!regexNumber.test(search) || !search.length}
                onClick={() => actionCall(search)}
              >
                <div className="flex flex-row items-center justify-center space-x-1">
                  <HiOutlinePhone style={{ fontSize: 18 }} />
                  <span>{t("voip.call")}</span>
                </div>
              </Button>
            </ConfigProvider>
          </Col>
        </Row>
      </div>

      <Divider style={{ margin: "7px 0 0 0" }} />
    </div>
  );
}

export default KeyPad;
