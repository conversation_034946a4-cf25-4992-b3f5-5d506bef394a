import {
  CloseOutlined,
  DeleteOutlined,
  EditOutlined,
  MoreOutlined,
  RestOutlined,
  SaveOutlined,
} from "@ant-design/icons";
import { Popconfirm, Space, Tooltip, Button, Dropdown } from "antd";
import { useTranslation } from "react-i18next";
import Confirm from "./GenericModal";
import { useState } from "react";

const DropDownCrud = ({
  record,
  edit,
  handleDelete,
  form,
  cancel,
  isEditing,
  data,
  setData,
  editingKey,
  api,
  source,
  confirmLoading,
  fieldType,
  updateOptionField,
  onRow,
}) => {
  const [isClicked, setIsClicked] = useState(null);
  const [t] = useTranslation("common");
  let editable = isEditing(record);
  // Validate disable/enable delete button for fields option table.
  const handleDisableDeleteBtn = (array, fieldType, rec) => {
    if (fieldType != "radio" && updateOptionField == 0 && array.length <= 1) {
      return true;
    } else if (updateOptionField != 0) {
      if (
        array.map((element) => element?.id).filter((el) => el != undefined).length <= 1 &&
        rec?.key == undefined
      ) {
        return true;
      }
    }
    if (fieldType == "radio") {
      if (updateOptionField == 0 && array.length <= 2) {
        return true;
      } else if (
        array.map((element) => element?.id).filter((el) => el != undefined).length <= 2 &&
        rec?.key == undefined
      ) {
        return true;
      }
    }
    return false;
  };

  const items = [
    source != "fieldOptions" && {
      label: t("table.edit"),
      key: "1",
      icon: <EditOutlined />,
      // disabled: record?.default == 1 || record?.system == 1 ? true : false,
      //  || source == "fieldOptions"
    },
    {
      label: t("table.delete"),
      danger: true,
      key: "2",
      icon: <DeleteOutlined />,
      disabled:
        record?.default == 1 ||
        record?.system == 1 ||
        record?.editable == 0 ||
        record?.is_used == 1 ||
        (source === "fieldOptions" && handleDisableDeleteBtn(data, fieldType, record))
          ? true
          : false,
    },
  ];
  function hasReturnValue(func) {
    const returnValue = func();
    return returnValue !== undefined;
  }

  const handleClick = (event) => {
    // if (hasReturnValue(onRow) && !isClicked)
    event.stopPropagation();
  };
  return (
    <div onClick={handleClick}>
      {editable ? (
        <div
          className=" mt-[5px] flex  justify-end"
          style={{ marginLeft: !record.id ? "-12px" : "-8px" }}
        >
          <Space size={2}>
            <Popconfirm
              title={t("fields_management.cancelAlertMsg")}
              onConfirm={() => cancel(record)}
              cancelText={t("fields_management.cancelText")}
              okText={t("fields_management.okText")}
            >
              <Button
                type="text"
                size="small"
                shape="circle"
                icon={<CloseOutlined className="text-slate-400" />}
              />
            </Popconfirm>
            <Tooltip title="Save">
              <Button
                type="text"
                size="small"
                shape="circle"
                icon={<SaveOutlined className="text-green-600" />}
                onClick={() => form.submit()}
              />
            </Tooltip>
          </Space>
        </div>
      ) : (
        <Dropdown
          trigger={["click"]}
          autoFocus={true}
          placement="bottom"
          arrow
          onOpenChange={(e) => (e ? setIsClicked(record.id) : setIsClicked(null))}
          // disabled={record?.default == 1 || record?.system == 1 || editingKey != ""}
          menu={{
            items,
            onClick: (e) => {
              if (e.key === "1") {
                edit(record);
              }
              if (e.key === "2") {
                record?.id
                  ? Confirm(
                      ` ${t("table.delete")} "${
                        record.name?.replace(/<\/?[^>]+(>|$)/g, "") ||
                        record.label?.replace(/<\/?[^>]+(>|$)/g, "") ||
                        record.Bank?.replace(/<\/?[^>]+(>|$)/g, "") ||
                        record.title?.replace(/<\/?[^>]+(>|$)/g, "") ||
                        record?.listElementValue?.replace(/<\/?[^>]+(>|$)/g, "")
                      }" !`,
                      "Confirm",
                      <RestOutlined style={{ color: "red" }} />,
                      function func() {
                        return handleDelete(
                          record.id == undefined
                            ? record.key
                            : source == "draggableTable"
                            ? {
                                fieldId: record.id.toString(),
                                groupId: record?.field_group_id,
                              }
                            : record.id.toString() || record.key
                        );
                      },
                      true,
                      api === "departments" ||
                        api === "type-family-products" ||
                        api === "pipelines" ||
                        (api === "steps" &&
                          data[data.indexOf(record) + 1]?.confirmation === "true") ? (
                        <span className="text-xs">{t(`beforeDeleted.${api}`)}</span>
                      ) : (
                        ""
                      ),
                      confirmLoading
                    )
                  : setData(data.filter((item) => item.key !== record.key));
              }
            },
          }}
        >
          <Button icon={<MoreOutlined />} size="small" type="text" shape="circle" />
        </Dropdown>
      )}
    </div>
  );
};

export default DropDownCrud;
