import { Table, <PERSON><PERSON><PERSON>, Badge, Button, Space, Modal } from "antd";
import { useCallback, useEffect, useMemo, useState } from "react";

import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";

import MainService from "../../../../services/main.service";
import FormCreate from "../../../clients&users/components/FormCreate";
import CreateTask from "../../../voip/components/CreateTask";
import useDebounce from "../../../components/UseDebounce/UseDebounce";
import dayjs from "dayjs";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import {
  setNumberEmailThread,
  setPage,
  setPageSize,
} from "new-redux/actions/mail.actions";

import ActionsStarredImportant from "../actionsStarredImportant";
import { formatDateForDisplay } from "pages/tasks/helpers/formatDateToDisplay";
import { TrashListMessages } from "../services/ActionsApi";
import ModalDeleteEmail from "../components/ModalDeleteEmail";
import DropdownActionsTable from "../dropdownActionsTable";
import Log from "../components/Log";
import { DeleteOutlined, ExclamationCircleOutlined } from "@ant-design/icons";
import "../mailing.css";
import { renderHighlight } from "pages/global-search/components/render-search-items";
import { toastNotification } from "components/ToastNotification";
import { unSpamMails } from "../services/services";
import { checkAccessRoleAccount } from "../mailing";

const Spam = ({
  setDetailsMail,
  notification,
  dataAccounts,
  searchMail,
  refresh,
}) => {
  // const [page, setPage] = useState(1);

  const [openForm, setOpenForm] = useState(false);
  const [familyId, setFamilyId] = useState(null);
  const [openTask, setOpenTask] = useState(false);
  const [titleTask, setTiltleTask] = useState("");
  const [openModal, setOpenModal] = useState(false);
  const [thirdid, setThirdId] = useState("");
  const [dataMailSpam, setDataMailSpam] = useState([]);
  const [metaMailSpam, setMetaMailSpam] = useState({});
  const [openLogDrawer, setOpenLogDrawer] = useState(null);
  const [typeDelete, setTypeDelete] = useState("");
  const [EmailId, setEmailId] = useState("");
  const [loading, setLoading] = useState(false);
  const [expandedRows, setExpandedRows] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  const [t] = useTranslation("common");
  const navigate = useNavigate();
  const [error, setError] = useState(false);
  const { page, pageSize, searchEmail } = useSelector(
    (state) => state.mailReducer
  );
  const debouncedSearchValue = useDebounce(searchEmail, 500);
  const usedAccount = useMemo(
    () => dataAccounts?.find((item) => item?.selected),
    [dataAccounts]
  );
  const { user } = useSelector(({ user }) => user);
  const isToday = require("dayjs/plugin/isToday");

  dayjs.extend(isToday);

  const dispatch = useDispatch();

  const getMailsSpam = useCallback(async () => {
    // if (Object.values(metaMailSpam).length > 0 && !refresh) return;
    setLoading(true);

    let response = "";
    try {
      if (debouncedSearchValue.length > 0) {
        response = await MainService.searchMailsSpam(
          usedAccount.value,
          debouncedSearchValue,
          page,
          pageSize
        );
      } else {
        response = await MainService.getSpamEmails(
          usedAccount.value,
          page,
          pageSize
        );
      }
      if (response?.status === 200) {
        checkAccessRoleAccount(response, navigate, t);

        setDataMailSpam(response.data.data);
        setMetaMailSpam(response.data.meta);

        setError(false);
      }
    } catch (err) {
      setError(true);
      console.log(err);
    } finally {
      setLoading(false);
    }
  }, [
    usedAccount?.value,
    debouncedSearchValue,
    page,
    refresh,
    metaMailSpam?.currentPage,
  ]);

  const dataSourceSpam = useMemo(() => {
    const result = [];
    const highlightedRows = [];
    dataMailSpam.forEach((item) => {
      if (!!item.highlight) {
        highlightedRows.push(item.id);
      }
      result.push({
        key: item.id,
        from: item.from,
        to: item.to,
        subject: item.subject,
        body: item.body,
        nbr: item.nbr,
        date: item.date,
        seen: item.seen,
        third_id: item.third_id,
        starred: item.starred,
        important: item.important,
        highlight: item.highlight,
      });
    });
    if (!!debouncedSearchValue) setExpandedRows(highlightedRows);
    else setExpandedRows([]);
    return result;
  }, [dataMailSpam]);

  const DeleteMail = async () => {
    setLoading({ state: true, type: "delete" });

    const response = await TrashListMessages({
      usedAccount,
      id: EmailId,
      setSelectedRowKeys,
      selectedRowKeys,
      setOpenModal,
      folder: "junk",
      typeDelete,
      setLoading,
      t,
    });
    if (response) getMailsSpam();
  };
  //
  const unSpam = async (id, thirdId) => {
    console.log({ id, thirdId, selectedRowKeys });
    const markUnSpam = async () => {
      try {
        if (!selectedRowKeys.length && !id) return;
        const formData = new FormData();
        formData.append("accountId", usedAccount.value);
        if (id) {
          formData.append("thirdId[]", thirdId);
          formData.append("ids[]", id);
        } else {
          selectedRowKeys.forEach((row) => {
            formData.append("ids[]", row);
            const mail = dataSourceSpam.find((item) => item.key === row);
            !!mail && formData.append("thirdId[]", mail.third_id);
          });
        }
        await unSpamMails(formData);
        await getMailsSpam();
      } catch (err) {
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
        throw new Error(err?.message ? err.message : err);
      }
    };

    Modal.confirm({
      title: t("mailing.makeItNotSpam"),
      content: t("contacts.deleteConfirmMsg"),
      okText: t("profile.confirm"),
      cancelText: t("profile.cancel"),
      icon: <ExclamationCircleOutlined style={{ color: "#1677ff" }} />,
      onOk() {
        markUnSpam();
      },
      onCancel() {
        console.log("Cancel");
      },
    });
  };

  //
  const columns = [
    {
      dataIndex: "starred",
      width: "90px",
      fixed: "left",

      render: (_, record) => (
        <ActionsStarredImportant
          record={record}
          getMails={getMailsSpam}
          usedAccount={usedAccount}
        />
      ),
    },
    {
      title: t("mailing.Inbox.sender"),
      dataIndex: "from",
      // width: "120px",
      render: (text, record) => {
        return (
          <div
            className={`relative flex cursor-pointer items-center ${
              record.seen === 0 ? " font-bold" : ""
            } `}
          >
            <Tooltip
              placement="topLeft"
              title={text?.name?.length > 0 ? text.name : text?.address}
            >
              <p
                className="  ml-1 cursor-pointer truncate"
                style={{ width: "80%" }}
              >
                {text?.name?.length > 0 ? text.name : text?.address}
              </p>
            </Tooltip>

            <div className="action-mail">
              <Badge
                style={{
                  backgroundColor: "transparent",
                  border: "none",
                  outline: "none",
                  margin: 0,
                  color: "gray",
                  fontSize: 12,
                  fontWeight: record?.seen === 0 ? "bold" : "normal",
                }}
                count={record?.nbr}
              ></Badge>
              <DropdownActionsTable
                record={record}
                setThirdId={setThirdId}
                setOpenLogDrawer={setOpenLogDrawer}
                t={t}
                conditionActions={false}
                usedAccount={usedAccount}
                dataMailOutbox={dataMailSpam}
                setDataMailOutbox={setDataMailSpam}
                user={user}
                // access={access}
                // dataTags={dataTags}
                setOpenTask={setOpenTask}
                setEmailId={setEmailId}
                setOpenModal={setOpenModal}
                setTypeDelete={setTypeDelete}
                getMailsInbox={getMailsSpam}
                type="spam"
                unSpam={unSpam}
              />
            </div>
          </div>
        );
      },
    },
    {
      title: t("mailing.Inbox.subject"),
      dataIndex: "subject",
      // width: "150px",
      ellipsis: true,
      render: (text, record) => {
        return (
          <div>
            <span
              style={{
                fontWeight: record.seen === 0 ? "bold" : "",
                cursor: "pointer",
              }}
            >
              {text?.length > 30 && usedAccount?.shared == 1 ? (
                // <MarkDown>{text.toString()?.substring(0, 30)}...</MarkDown>
                <span
                  dangerouslySetInnerHTML={{
                    __html: text.toString()?.substring(0, 30) + "...",
                  }}
                />
              ) : (
                // <MarkDown>{text}</MarkDown>
                <span dangerouslySetInnerHTML={{ __html: text }} />
              )}
            </span>
          </div>
        );
      },
    },
    ...(usedAccount?.shared == 0
      ? [
          {
            title: t("mailing.Inbox.message"),
            dataIndex: "body",
            // width: "300px",
            ellipsis: true,
            render: (text, record) => {
              return (
                <div style={{ cursor: "pointer" }}>
                  {/* <MarkDown>{text}</MarkDown> */}
                  <span dangerouslySetInnerHTML={{ __html: text }} />
                </div>
              );
            },
          },
        ]
      : []),
    {
      title: t("mailing.Inbox.date"),
      dataIndex: "date",
      // width: "120px",
      render: (dateTime, record) => (
        <span
          style={{
            fontWeight: record.seen === 0 ? "bold" : "",
            cursor: "pointer",
          }}
        >
          {formatDateForDisplay(
            record.date,
            `${user?.location?.date_format} ${user?.location?.time_format}`,
            user,
            t
          )}
        </span>
      ),
    },
  ];

  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };
  const toggleSelection = (record) => {
    const key = record.key;
    const newSelectedRowKeys = selectedRowKeys.includes(key)
      ? selectedRowKeys.filter((k) => k !== key)
      : [...selectedRowKeys, key];
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
    renderCell: (checked, record, index, originNode) => (
      <div className="relative">
        <Button
          style={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
          }}
          size="large"
          type="text"
          shape="circle"
          onClick={(e) => {
            e.stopPropagation();
            toggleSelection(record);
          }}
        >
          {originNode}
        </Button>
      </div>
    ),
  };

  useEffect(() => {
    if (dataAccounts.length > 0) {
      getMailsSpam();
    }
  }, [getMailsSpam]);

  return (
    <>
      {selectedRowKeys.length ? (
        <Space>
          <Button
            type="primary"
            danger
            icon={<DeleteOutlined />}
            onClick={() => {
              setOpenModal(true);
              setTypeDelete("multiple");
            }}
          >
            {t("mailing.DeleteButton")} ({selectedRowKeys.length}{" "}
            {selectedRowKeys.length > 1 ? "emails" : "email"})
          </Button>
          <Button onClick={() => unSpam()}>{t("mailing.notSpam")}</Button>
        </Space>
      ) : null}
      <FormCreate open={openForm} setOpen={setOpenForm} familyId={familyId} />

      <CreateTask
        open={openTask}
        setOpen={setOpenTask}
        mask={false}
        source="mailing"
        object={titleTask}
      />

      <Table
        className="mailing-custom-row"
        loading={loading}
        rowSelection={rowSelection}
        columns={columns}
        dataSource={dataSourceSpam}
        showSizeChanger={false}
        pagination={{
          current: page,
          pageSize: pageSize,
          pageSizeOptions: ["10", "20", "30"],
          total: metaMailSpam.total === 0 ? 1 : metaMailSpam.total,
          showSizeChanger: true,
          onChange: (page, pageSize) => {
            dispatch(setPage(page));
            dispatch(setPageSize(pageSize));
          },
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total}`,
        }}
        scroll={{ y: "calc(100vh - 250px)" }}
        onRow={(record) => {
          return {
            onClick: () => {
              setDetailsMail([]);
              dispatch(setNumberEmailThread(record.nbr));
              navigate(`/mailing/${usedAccount.value}/spam/${record.key}`);
            },
          };
        }}
        rowClassName={(_, index) => "group clickable-row"}
        expandable={
          !!debouncedSearchValue
            ? {
                expandedRowKeys: expandedRows,
                expandedRowRender: (record) =>
                  !!debouncedSearchValue ? (
                    <p className="m-0 ml-[10%]">
                      {renderHighlight(record.highlight, t, "email")}
                    </p>
                  ) : null,
                rowExpandable: (record) => !!record.highlight,
                showExpandColumn: false,
              }
            : {}
        }
        size="small"
        locale={{
          emptyText: (
            <p className={error ? "mt-4 text-red-600" : "mt-4 text-[#898282]"}>
              {error
                ? t("toasts.errorFetchApi")
                : loading.state && loading.type === "mails"
                ? "Loading ..."
                : t("mailing.noData")}
            </p>
          ),
        }}
      />

      {openLogDrawer !== null ? (
        <Log
          openLogDrawer={openLogDrawer}
          setOpenLogDrawer={setOpenLogDrawer}
          setLoading={setLoading}
          thirdid={thirdid}
        />
      ) : null}

      <ModalDeleteEmail
        t={t}
        openModal={openModal}
        setOpenModal={setOpenModal}
        loading={loading}
        DeleteMail={DeleteMail}
        typeDelete={typeDelete}
        selectedRowKeysLength={selectedRowKeys.length}
      />
    </>
  );
};

export default Spam;
