import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { Tabs } from "antd";

const TabsCompanies = ({ keyTab, setKeyTab, company }) => {
  const [t] = useTranslation("common");
  const { pathname } = useLocation();
  const navigate = useNavigate();
  const { id } = useParams();
  const { nameOrg } = useSelector((state) => state.configCompanies);

  useEffect(() => {
    if (pathname == `/settings/general/companies/${id}`) {
      setKeyTab("1");
    } else if (
      id !== "new" &&
      pathname == `/settings/general/companies/${id}/banks`
    ) {
      setKeyTab("2");
    } else if (
      id !== "new" &&
      pathname == `/settings/general/companies/${id}/tax`
    ) {
      setKeyTab("3");
    } else if (
      id !== "new" &&
      pathname == `/settings/general/companies/${id}/signature`
    ) {
      setKeyTab("4");
    } else {
      setKeyTab("1");
    }
  }, []);

  const navigateToBanks = () => {
    if (id !== "new") {
      setKeyTab("2");
      navigate(`/settings/general/companies/${id}/banks`, {
        state: company,
        // replace: false,
      });
    }
  };
  const navigateToTax = () => {
    if (id !== "new") {
      setKeyTab("3");
      navigate(`/settings/general/companies/${id}/tax`, {
        state: company,
        // replace: false,
      });
    }
  };
  const navigateToSignature = () => {
    if (id !== "new") {
      setKeyTab("4");
      navigate(`/settings/general/companies/${id}/signature`, {
        state: company,
        // replace: false,
      });
    }
  };
  const items = [
    {
      label: (
        <div
          onClick={() => {
            setKeyTab("1");
            navigate(`/settings/general/companies/${id}`);
          }}
        >
          Informations
        </div>
      ),
      key: "1",
    },
    {
      label: <div onClick={navigateToBanks}>{t("companies.banks")}</div>,
      key: "2",
      disabled: id === "new" ? true : false,
    },
    {
      label: <div onClick={navigateToTax}>Tax </div>,
      key: "3",
      disabled: id === "new" ? true : false,
    },
    {
      label: (
        <div onClick={navigateToSignature}>
          {t("companies.shippingEmail/Sms")}
        </div>
      ),
      key: "4",
      disabled: id === "new" ? true : false,
    },
  ];
  const onTabClick = (keyTab) => {
    if (keyTab === "1") {
      navigate(`/settings/general/companies/${id}`);
    }
    if (id !== "new" && keyTab === "2") {
      navigate(`/settings/general/companies/${id}/banks`);
    }
    if (id !== "new" && keyTab === "3") {
      navigate(`/settings/general/companies/${id}/tax`);
    }
    if (id !== "new" && keyTab === "4") {
      navigate(`/settings/general/companies/${id}/signature`);
    }
  };

  return (
    <>
      {keyTab ? (
        <div className="px-4 pt-4">
          <Tabs
            onTabClick={onTabClick}
            // onChange={(key) => setKey(key)}
            // activeKey={keyTab}
            defaultActiveKey={keyTab}
            items={items}
            type="card"
          />
        </div>
      ) : (
        ""
      )}
    </>
  );
};

export default TabsCompanies;
