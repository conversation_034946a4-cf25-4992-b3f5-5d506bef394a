import { forwardRef, useCallback } from "react";
import ReactQuill, { Quill } from "react-quill";
import "react-quill/dist/quill.snow.css";
import "./index.css";

const Size = Quill.import("formats/size");
Size.whitelist = ["small", false, "large", "huge"];
Quill.register(Size, true);

const icons = Quill.import("ui/icons");

icons["undo"] = `<svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    stroke-width="2"
    stroke-linecap="round"
    stroke-linejoin="round"
    class="lucide lucide-undo2-icon lucide-undo-2"
  >
    <path d="M9 14 4 9l5-5" />
    <path d="M4 9h10.5a5.5 5.5 0 0 1 5.5 5.5a5.5 5.5 0 0 1-5.5 5.5H11" />
  </svg>`;

icons["redo"] = `<svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    stroke-width="2"
    stroke-linecap="round"
    stroke-linejoin="round"
    class="lucide lucide-redo2-icon lucide-redo-2"
  >
    <path d="m15 14 5-5-5-5" />
    <path d="M20 9H9.5A5.5 5.5 0 0 0 4 14.5A5.5 5.5 0 0 0 9.5 20H13" />
  </svg>`;

const CustomEditor = forwardRef(({ value, onChange }, ref) => {
  //
  // console.log({ ref });
  const handleUndo = useCallback(() => {
    const editor = ref.current?.getEditor();
    editor?.history.undo();
  }, [ref]);

  const handleRedo = useCallback(() => {
    const editor = ref.current?.getEditor();
    editor?.history.redo();
  }, [ref]);
  //
  const modules = {
    blotFormatter: {},
    toolbar: {
      container: [
        ["undo", "redo"],
        [{ size: ["small", false, "large", "huge"] }],
        ["bold", "italic", "underline", "strike"],
        ["blockquote", "code-block"],
        [{ list: "ordered" }, { list: "bullet" }],
        [{ color: [] }, { background: [] }],
        ["link"],
        [{ align: [] }],
        ["image"],
        ["code-block"],
        ["emoji"],
      ],
      handlers: {
        undo: handleUndo,
        redo: handleRedo,
      },
    },
    history: {
      delay: 2000,
      maxStack: 200,
      userOnly: true,
    },
    imageResize: {
      parchment: Quill.import("parchment"),
      modules: ["Resize", "DisplaySize", "Toolbar"],
    },
    "emoji-toolbar": true,
    "emoji-textarea": false,
    "emoji-shortname": true,
  };
  //
  const formats = [
    "size",
    "bold",
    "italic",
    "align",
    "blockquote",
    "background",
    "list",
    "bullet",
    "indent",
    "emoji",
    "link",
    "image",
    "color",
    "code-block",
    "signature",
  ];
  //
  return (
    <div className="editor-wrapper">
      <ReactQuill
        ref={ref}
        theme="snow"
        value={value}
        onChange={onChange}
        modules={modules}
        className="gmail-editor"
        formats={formats}
      />
    </div>
  );
});

export default CustomEditor;
