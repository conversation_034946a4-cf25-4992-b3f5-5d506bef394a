import React, { use<PERSON><PERSON>back, useEffect, useState } from "react";
import {
  <PERSON>ton,
  Cascader,
  Form,
  Input,
  Modal,
  Select,
  Skeleton,
  Space,
  Tooltip,
  TreeSelect,
  Typography,
} from "antd";
import { PlusOutlined } from "@ant-design/icons";
import { NavLink } from "react-router-dom";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";

import MainService from "../../services/main.service";
import { getGroups } from "../../new-redux/actions/wiki.actions/getGroups";
import { getFolders } from "../../new-redux/actions/wiki.actions/getFolders";

import { createGroupWiki } from "../../new-redux/actions/wiki.actions/createGroup";
import { generateAxios } from "../../services/axiosInstance";

const SelectProduct = ({
  setSelectedGroupWiki,
  selectedGroup,
  setSelectedGroup,
  setSelectedNode,
  selectedNode,
  expandedKeys,
  setExpandedKeys,
  value,
  setValue,
  isVisited,
  setIsVisited,
  binders,
  setBinders,
  setSelectedKeys,
  loadGroupsWithClasseur,
  selectedGroupWiki,
}) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [t] = useTranslation("common");

  const [groups, setGroups] = useState([]);

  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  // const [value, setValue] = useState('')
  const [newGroup, setNewGroup] = useState("");
  const [selectedGroupNew, setSelectedGroupNew] = useState("");
  const [count, setCount] = useState(0);

  const { groupWiki } = useSelector((state) => state.wiki);
  const newWiki = useSelector((state) => state.wiki.new);

  // const [selectedGroup, setSelectedGroup] = useState('')
  // const createGroup = () => {
  //   MainService.createGroup({
  //     label: newGroup,
  //     value: newGroup,
  //   })
  //     .then((res) => {
  //       console.log('api add-------', res)
  //     })
  //     .catch((err) => {
  //       console.log(err)
  //     })
  // }

  const createGroup = () => {
    setLoading(true);
    setVisible(false);
    dispatch(
      createGroupWiki({
        label_fr: newGroup,
        //value: newGroup,
      })
    );
    setSelectedGroup(newWiki);
    setValue(newGroup);
    setCount(count + 1);
    setLoading(false);
    navigate(`/settings/wiki/docs/${generateUrl(newGroup)}`);
    setNewGroup("");
  };
  useEffect(() => {
    if (count > 0) {
      setSelectedGroup(newWiki);
      setSelectedGroupWiki(newWiki);
    }
  }, [count, newWiki]);

  // try {
  //   const response = await MainService.createGroup({
  //     label: newGroup,
  //     value: newGroup,
  //   })
  //   setGroups([...groups, response.data.data])
  //   navigate(`/settings/wiki/${newGroup}`)
  //   console.log(response)
  // } catch (error) {
  //   console.log(`error ${error}`)
  // }

  // useEffect(() => {
  //   if (!isVisited) {
  //     dispatch(getGroups());
  //     setIsVisited(true);
  //   }
  // }, [dispatch, isVisited]);
  // useEffect(() => {
  //   const getGroupsWithclasseur = async () => {
  //     try {
  //       const res = await generateAxios(URL_ENV?.REACT_APP_BASE_URL+process.env.REACT_APP_SUFFIX_API).get(
  //         "classeur-wiki-with-groups"
  //       );
  //       setBinders(
  //         res.data.data.map((el) => ({
  //           // value: el.id,
  //           label: el.label_fr,
  //           // selectable: false,
  //           options: el.groupe_wiki.map((grp) => ({
  //             value: grp.id,
  //             label: grp.label_fr,
  //           })),
  //         }))
  //       );
  //       console.log(res.data.data);
  //     } catch (err) {}
  //   };
  //   getGroupsWithclasseur();
  // }, []);
  const showModal = () => {
    setVisible(true);
  };
  const handleOk = () => {
    setVisible(false);
    setGroups((data) => {
      const updatedOptions = [...data];
      //const newValue = `${updatedOptions.length}`
      updatedOptions.push({
        //id: newValue,
        //value: newGroup,
        label_fr: newGroup,
      });
      createGroup();

      setValue(newGroup);

      return updatedOptions;
    });
  };

  const handleCancel = () => {
    setVisible(false);
  };

  const handleKeyPress = (event) => {
    if (event.key === "Enter") {
      //form.submit();
      createGroup();
    }
  };

  const generateUrl = (str) => {
    const slug = str
      .toLowerCase()
      .trim()
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .replace(/[^a-z0-9]+/g, "_")
      .replace(/(^-|-$)+/g, "");
    return slug;
  };
  return (
    <>
      <div className="flex justify-between">
        {/* <Tooltip title="Ajouter un groupe">
          <Button
            onClick={() => {
              showModal()
            }}
            shape="circle"
            icon={<PlusOutlined />}
          />
        </Tooltip> */}
        {/* <TreeSelect
          showSearch
          style={{
            width: "100%",
          }}
          value={selectedGroup ? Number(selectedGroup) : t(`wiki.SelectGroup`)}
          dropdownStyle={{
            maxHeight: 400,
            overflow: "auto",
          }}
          placeholder="Please select"
          allowClear
          treeDefaultExpandAll
          onChange={(value) => setSelectedGroup(value.split("-")[1])}
          onSelect={(key, el) => {
            // navigate(`/settings/wiki/docs/${generateUrl(key)}`);
            console.log(key);
            setSelectedGroupWiki(el.id);
            // setSelectedGroup(el.id);
            setSelectedNode("");

            //setExpandedKeys([])
          }}
          // onChange={onChange}
          treeData={binders}
        /> */}
        {loadGroupsWithClasseur ? (
          <Skeleton.Input className=" inline-flex w-[400px]" />
        ) : (
          <Space direction="vertical" style={{ width: "100%" }}>
            <Cascader
              style={{
                width: "100%",
              }}
              allowClear={false}
              showSearch
              optionFilterProp="children"
              filterOption={(input, option) =>
                (option?.label.toLowerCase() ?? "").includes(
                  input.toLowerCase()
                )
              }
              // filterSort={(optionA, optionB) =>
              //   (optionA?.label_fr ?? '')
              //     .toLowerCase()
              //     .localeCompare((optionB?.label_fr ?? '').toLowerCase())
              // }
              onChange={(value) => {
                setSelectedNode("");
                setSelectedKeys("");
                setSelectedGroupWiki(value);
                setSelectedGroup(value[1]);
              }}
              placeholder={t(`wiki.SelectGroup`)}
              value={selectedGroupWiki ? selectedGroupWiki : []}
              options={binders}
              // onSelect={(key, el) => {
              //   // navigate(`/settings/wiki/docs/${generateUrl(key)}`);
              //   // setSelectedGroupWiki(el.id);
              //   // setSelectedGroup(el.id);
              //   setSelectedNode("");
              //   setSelectedKeys("");
              // }}
            />
          </Space>
        )}
        <Modal
          title={t(`wiki.AddFolder`)}
          open={visible}
          //onOk={createGroup}
          onCancel={handleCancel}
          footer={[
            <Button key="back" onClick={handleCancel}>
              {t(`wiki.Cancel`)}
            </Button>,
            <Button
              key="submit"
              type="primary"
              loading={loading}
              onClick={createGroup}
            >
              {t(`wiki.Ok`)}
            </Button>,
          ]}
        >
          <Input
            value={newGroup}
            onChange={(e) => {
              setNewGroup(e.target.value);
            }}
            onKeyPress={handleKeyPress}
          />
        </Modal>
      </div>
    </>
  );
};

export default SelectProduct;
