<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Department Tags Overflow - Final Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            line-height: 1.5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .pipeline-item {
            background: white;
            border-bottom: 1px solid #e5e5e5;
            padding: 16px;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: flex-start;
        }
        .pipeline-item:last-child {
            border-bottom: none;
        }
        .pipeline-item:hover {
            background-color: #f9f9f9;
        }
        .pipeline-item.selected {
            background-color: #e6f7ff;
            border-left: 4px solid #1890ff;
        }
        .drag-handle {
            margin-right: 12px;
            cursor: move;
            opacity: 0;
            transition: opacity 0.2s;
            color: #999;
            padding: 4px;
            border-radius: 4px;
        }
        .pipeline-item:hover .drag-handle {
            opacity: 1;
        }
        .drag-handle:hover {
            background: #f0f0f0;
        }
        .content {
            flex: 1;
            min-width: 0;
        }
        .pipeline-name {
            font-weight: 600;
            font-size: 16px;
            color: #333;
            margin-bottom: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .departments {
            display: flex;
            align-items: center;
            gap: 4px;
            min-height: 22px;
        }
        .tag {
            background: #f0f8ff;
            color: #1890ff;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            border: 1px solid #91d5ff;
            white-space: nowrap;
            margin: 0;
        }
        .tag.sales { background: #fff2e8; color: #fa8c16; border-color: #ffbb96; }
        .tag.marketing { background: #f6ffed; color: #52c41a; border-color: #b7eb8f; }
        .tag.support { background: #fff1f0; color: #ff4d4f; border-color: #ffadd2; }
        .tag.tech { background: #f9f0ff; color: #722ed1; border-color: #d3adf7; }
        .tag.more {
            background: #f0f0f0;
            color: #666;
            border-color: #d9d9d9;
            cursor: pointer;
        }
        .tag.more:hover {
            background: #e6e6e6;
        }
        .actions {
            margin-left: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .arrow {
            color: #1890ff;
            font-size: 18px;
        }
        .sidebar-container {
            border: 1px solid #d9d9d9;
            border-radius: 8px;
            overflow: hidden;
            background: white;
        }
        .popover {
            position: absolute;
            background: white;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            padding: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1000;
            max-width: 250px;
            display: none;
        }
        .popover.show {
            display: block;
        }
        .popover-title {
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }
        .popover-content {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
        }
        .width-indicator {
            background: #f0f0f0;
            border: 1px dashed #ccc;
            padding: 4px 8px;
            font-size: 11px;
            color: #666;
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>Department Tags Overflow - Test Final</h2>
        <p>Simulation du comportement du composant DepartmentTags avec différentes largeurs.</p>
        
        <div class="sidebar-container">
            <div class="pipeline-item selected">
                <div class="drag-handle">⋮⋮</div>
                <div class="content">
                    <div class="pipeline-name">Sales Pipeline</div>
                    <div class="width-indicator">Largeur: 250px (tous visibles)</div>
                    <div class="departments" style="max-width: 250px;">
                        <span class="tag sales">Sales</span>
                        <span class="tag marketing">Marketing</span>
                        <span class="tag support">Support</span>
                    </div>
                </div>
                <div class="actions">
                    <span class="arrow">▶</span>
                </div>
            </div>
            
            <div class="pipeline-item">
                <div class="drag-handle">⋮⋮</div>
                <div class="content">
                    <div class="pipeline-name">Customer Success Pipeline</div>
                    <div class="width-indicator">Largeur: 200px (overflow avec +3)</div>
                    <div class="departments" style="max-width: 200px;">
                        <span class="tag sales">Customer Success</span>
                        <span class="tag marketing">Account Management</span>
                        <span class="tag more" onmouseover="showPopover(this, 'popover1')" onmouseout="hidePopover('popover1')">+3</span>
                    </div>
                </div>
            </div>
            
            <div class="pipeline-item">
                <div class="drag-handle">⋮⋮</div>
                <div class="content">
                    <div class="pipeline-name">Development Pipeline</div>
                    <div class="width-indicator">Largeur: 150px (overflow avec +5)</div>
                    <div class="departments" style="max-width: 150px;">
                        <span class="tag tech">Development</span>
                        <span class="tag more" onmouseover="showPopover(this, 'popover2')" onmouseout="hidePopover('popover2')">+5</span>
                    </div>
                </div>
            </div>
            
            <div class="pipeline-item">
                <div class="drag-handle">⋮⋮</div>
                <div class="content">
                    <div class="pipeline-name">Support Pipeline</div>
                    <div class="width-indicator">Largeur: 300px (tous visibles)</div>
                    <div class="departments" style="max-width: 300px;">
                        <span class="tag support">Technical Support</span>
                        <span class="tag sales">Customer Care</span>
                        <span class="tag tech">Level 2 Support</span>
                        <span class="tag marketing">Documentation</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Popovers -->
        <div class="popover" id="popover1">
            <div class="popover-title">Départements</div>
            <div class="popover-content">
                <span class="tag support">Technical Support</span>
                <span class="tag tech">Quality Assurance</span>
                <span class="tag marketing">Business Intelligence</span>
            </div>
        </div>
        
        <div class="popover" id="popover2">
            <div class="popover-title">Départements</div>
            <div class="popover-content">
                <span class="tag tech">Quality Assurance</span>
                <span class="tag tech">DevOps</span>
                <span class="tag support">Technical Writing</span>
                <span class="tag marketing">Product Management</span>
                <span class="tag sales">Business Analysis</span>
            </div>
        </div>
        
        <div style="margin-top: 20px; color: #666; font-size: 14px;">
            <h3>✅ Fonctionnalités implémentées :</h3>
            <ul>
                <li><strong>Calcul intelligent de l'espace</strong> : Estimation précise de la largeur des tags</li>
                <li><strong>Affichage adaptatif</strong> : Nombre de tags visibles selon l'espace disponible</li>
                <li><strong>Tag "+X" informatif</strong> : Indique le nombre d'éléments cachés</li>
                <li><strong>Popover au survol</strong> : Affiche tous les départements cachés</li>
                <li><strong>Hauteur constante</strong> : Tous les éléments ont la même hauteur</li>
                <li><strong>Pas de retour à la ligne</strong> : Interface propre et cohérente</li>
                <li><strong>Couleurs préservées</strong> : Chaque département garde sa couleur</li>
                <li><strong>Performance optimisée</strong> : Calcul efficace sans mesure DOM</li>
            </ul>
        </div>
    </div>

    <script>
        function showPopover(element, popoverId) {
            const popover = document.getElementById(popoverId);
            const rect = element.getBoundingClientRect();
            
            popover.style.left = rect.left + 'px';
            popover.style.top = (rect.bottom + 5) + 'px';
            popover.classList.add('show');
        }
        
        function hidePopover(popoverId) {
            const popover = document.getElementById(popoverId);
            popover.classList.remove('show');
        }
    </script>
</body>
</html>
