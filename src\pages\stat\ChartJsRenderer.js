import React from "react";
import { Bar } from "react-chartjs-2";

const ChartJsRenderer = ({ pivotData }) => {
    if (!pivotData || !pivotData.getData) {
        console.warn("pivotData est invalide ou la méthode getData est manquante");
        return <div>Aucune donnée disponible pour le rendu</div>;
    }

    const data = pivotData.getData();
    console.log("Données récupérées pour le graphique:", data);

    if (data.length === 0) {
        return <div>Aucune donnée à afficher</div>;
    }


    const chartData = {
        labels: data.map(item => item[0]), // Supposons que la première colonne contient les labels
        datasets: [
            {
                label: "Valeurs",
                data: data.map(item => item[1]), // Supposons que la deuxième colonne contient les valeurs
                backgroundColor: "rgba(75,192,192,0.4)",
            },
        ],
    };

    return <Bar data={chartData} />;
};


export default ChartJsRenderer;
