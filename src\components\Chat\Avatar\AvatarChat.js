
import { <PERSON><PERSON>, Toolt<PERSON> } from "antd";
import "./avatar.css";
import ClassNames from "../MessageRender/ClassNames";
import { IoEarthOutline } from "react-icons/io5";
import { TbUserSquareRounded } from "react-icons/tb";
import i18next from "i18next";
import { useCallback, useEffect, useRef, useMemo } from "react";

/**
 *
 * generate avatar for sphere
 * params required: size, width, height, url,  hasImage, name,  type
 *
 *
 * size: size of avatar (heigh and width * 4 )
 * @param {number} size
 *
 * @param {number} width
 * @param {number} height
 * url : url of avatar
 * @param {string} url
 * hasImage :  check if url is image or show text
 *
 * @param {boolean} hasImage
 * backgroundColor /colorTextAvatar/ colorTextAvatar /fontBold/fontSize:  show when url is not image
 *
 * @param {string} backgroundColor
 * @param {string} colorTextAvatar
 * @param {string} colorTextAvatar
 * @param {string} fontBold
 * @param {any} fontSize
 * name : name of user
 * @param {string} name
 * type : type of avatar ("room", "user", "bot")
 *
 * @param {Enumerator} type (room, user, bot)
 * className : class of avatar
 * @param {string} className
 *
 * @returns
 */
const Container = ({ isPublic, children }) => {
  return !isPublic ? (
    <>{children} </>
  ) : (
    <div
      className={`relative flex items-center justify-center rounded-full p-0.5 ring-2 ring-blue-500`}
    >
      {children}
    </div>
  );
};

// Map of default avatar paths - defined outside component to avoid recreation
const DEFAULT_AVATARS = {
  room: process.env.PUBLIC_URL + "/avatar/group.png",
  user: process.env.PUBLIC_URL + "/avatar/avatar.png",
  bot: process.env.PUBLIC_URL + "/avatar/bot.png",
};

const AvatarChat = ({
  size,
  width,
  height,
  url,
  hasImage,
  name,
  type,
  backgroundColor,
  colorTextAvatar,
  fontBold,
  fontSize,
  className,
  isPublic = false,
  fromInfo = false,
  iconPublicSize = "17px",
  titleTooltip = "",
  roomPro = 2,
}) => {
  const retryCountRef = useRef(0);
  const MAX_RETRIES = 3;

  // Create styles as memoized objects to prevent unnecessary re-renders
  const avatarStyles = useMemo(() => ({
    fontSize: fontSize || "1rem",
    backgroundColor: backgroundColor || "#dbeafe",
    color: colorTextAvatar || "#1e40af",
  }), [backgroundColor, colorTextAvatar, fontSize]);
  
  const publicIconStyles = useMemo(() => ({
    fontSize: fromInfo ? "2rem" : iconPublicSize,
    bottom: fromInfo ? "10px" : "0",
    right: fromInfo ? "10px" : "0",
  }), [fromInfo, iconPublicSize]);

  // Get the fallback image URL based on type
  const getFallbackSrc = useCallback(() => {
    return DEFAULT_AVATARS[type] || DEFAULT_AVATARS.user;
  }, [type]);

  // Error handler for image loading failures
  const handleError = useCallback((e) => {
    if (!e || !e.target) return;
    
    if (navigator.onLine && retryCountRef.current < MAX_RETRIES) {
      retryCountRef.current++;
      e.target.src = getFallbackSrc();
    } else {
      // After max retries or when offline, remove the error handler to prevent future attempts
      if (e.target) {
        e.target.onerror = null;
      }
    }
  }, [getFallbackSrc]);

  // Reset retry count when URL changes
  useEffect(() => {
    retryCountRef.current = 0;
    return () => {
      // Clean up any references
      retryCountRef.current = 0;
    };
  }, [url]);

  // Cleanup function for the component
  useEffect(() => {
    return () => {
      // Any additional cleanup needed
    };
  }, []);
  
  // Determine tooltip title for public rooms
  const publicTooltipTitle = useMemo(() => {
    return i18next.t(
      [5, 6].includes(roomPro)
        ? "common:chat.groupe_guest"
        : "common:chat.groupe_public"
    );
  }, [roomPro]);

  return (
    <Container isPublic={isPublic}>
      <Tooltip title={titleTooltip}>
        {hasImage ? (
          <Avatar
            size={size}
            className={ClassNames(`h-${height} w-${width}`, className)}
            src={
              <img
                loading="lazy"
                id="target-image"
                onError={handleError}
                className="h-auto w-full rounded-full object-cover"
                style={{
                  aspectRatio: "1",
                }}
                src={url}
                alt={name ? `avatar_${name}` : "avatar"}
              />
            }
          />
        ) : (
          <Avatar
            className={ClassNames(
              `h-${height} w-${width} text-center text-xs uppercase ${fontBold || "font-medium"}`,
              className
            )}
            shape="circle"
            size={size}
            alt={name ? `avatar_${name}` : "avatar"}
            style={avatarStyles}
            icon={<>{name}</>}
          />
        )}
      </Tooltip>
      {isPublic && (
        <Tooltip placement="top" title={publicTooltipTitle}>
          {[5, 6].includes(roomPro) ? (
            <TbUserSquareRounded
              style={publicIconStyles}
              className="absolute ml-1 rounded-full bg-gray-50 p-0.5 text-blue-500"
            />
          ) : (
            <IoEarthOutline
              style={publicIconStyles}
              className="absolute ml-1 rounded-full bg-gray-50 p-0.5 text-blue-500"
            />
          )}
        </Tooltip>
      )}
    </Container>
  );
};

export default AvatarChat;
