import React, { useEffect, useRef } from "react";
import { Badge, Form, Input, Select, Space } from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { colors } from "./Colors";
import { toastNotification } from "./ToastNotification";
import Header from "./configurationHelpDesk/Header";
import ColumnColors from "./ColumnColors";
import NewTableDraggable from "./NewTableDraggable";
import { useDispatch, useSelector } from "react-redux";
import { setSearch } from "../new-redux/actions/menu.actions/menu";
import LabelTable from "./LabelTable";
import BottomButtonAddRow from "./BottomButtonAddRow";
import { SubmitKeyPress } from "../utils/SubmitKeyPress";
import MainService from "services/main.service";

const Departments = () => {
  const [form] = Form.useForm();
  const [data, setData] = useState([]);
  const [editingKey, setEditingKey] = useState("");
  const [id, setId] = useState(null);
  const [loading, setLoading] = useState(true);
  const isEditing = (record) => record.key === editingKey;
  const [t] = useTranslation("common");
  const inputRefs = useRef([]);
  const [companies, setCompanies] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const dispatch = useDispatch();
  const { search } = useSelector((state) => state.form);

  useEffect(() => {
    inputRefs.current.forEach((input) => {
      input?.focus();
    });
  }, [data.length, id]);

  const handleKeyPress = (event) => {
    SubmitKeyPress(event, form);
  };
  const onFinishFailed = (values) => {
    console.log(values);
  };

  const EditableCell = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    const inputNode =
      inputType === "radio" ? (
        <Select
          showSearch
          placeholder={t("tags.selectcolor")}
          style={{
            minWidth: 100,
          }}
          options={colors.map((el) => ({
            label: (
              <Space>
                <Badge color={el.value} /> {t(`colors.${el.label}`)}
              </Space>
            ),
            value: el.value,
          }))}
          optionFilterProp="children"
          filterOption={(input, option) =>
            (
              colors
                .find((el) => el.value === option.value)
                ?.label?.toLowerCase() ?? ""
            ).includes(input.toLowerCase())
          }
          filterSort={(optionA, optionB) =>
            (optionA?.value ?? "")
              .toLowerCase()
              .localeCompare((optionB?.value ?? "").toLowerCase())
          }
          allowClear
        />
      ) : inputType === "companies" ? (
        <Select
          placeholder={t("companies.selectcompanies")}
          mode="multiple"
          filterOption={(input, option) => {
            var _a;
            return (
              (_a =
                option === null || option === void 0
                  ? void 0
                  : option.label) !== null && _a !== void 0
                ? _a
                : ""
            )
              .toLowerCase()
              .includes(input.toLowerCase());
          }}
          options={
            companies.length > 0 &&
            companies.map((item) => ({
              value: item.id,
              label: item.label,
            }))
          }
        />
      ) : (
        <Input
          ref={(el) => (inputRefs.current[index] = el)}
          onKeyPress={handleKeyPress}
          placeholder={t("activities.name")}
        />
      );
    return (
      <td {...restProps}>
        {editing ? (
          <Form.Item
            name={dataIndex.toLowerCase()}
            style={{
              margin: 0,
            }}
            rules={[
              {
                required: dataIndex === "color" ? false : true,
                message: `${t(`activities.${dataIndex}`)} ${t(
                  "table.header.isrequired"
                )}`,
              },
            ]}
          >
            {inputNode}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };
  const edit = (record) => {
    if (record) {
      form.setFieldsValue({
        label: record.label,
        color: record.color,
        companies: record?.companies?.map((el) => el.id),
      });
      setId(record.id);
    } else {
      form.setFieldsValue({
        label: "",
        color: undefined,
        companies: [],
      });
    }
    setEditingKey(record.key);
  };
  const cancel = (record) => {
    setEditingKey("");
    setId(null);
    if (!record.id) {
      setData(data.filter((item) => item.key !== record.key));
    }
  };
  const save = async () => {
    setLoading(true);
    if (id) {
      try {
        const row = await form.validateFields();
        const res = await MainService.updateDepartment(
          {
            label: row.label,
            color: row.color,
            companies_ids: row.companies?.join(",") || null,
          },
          id
        );
        setEditingKey("");
        setData(
          data.map((el) =>
            el.id === res.data.data.id
              ? {
                  ...res.data.data,
                  key: res.data.data.id,
                  companies_ids: res.data.data.companies?.map(
                    (el) => el?.social_reason
                  ),
                }
              : el
          )
        );
        form.setFieldsValue({
          label: "",
          color: undefined,
          companies: [],
        });
        setLoading(false);
        toastNotification("success", row.label + t("toasts.edit"), "topRight");
      } catch (errInfo) {
        setLoading(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    } else {
      try {
        const row = await form.validateFields();
        const res = await MainService.createDepartment({
          label: row.label,
          color: row.color,
          companies_ids: row.companies?.join(","),
        });
        setEditingKey("");
        setData([
          ...data.filter((el) => el.id),
          {
            ...res.data.data,
            key: res.data.data.id,
            companies_ids: row.companies?.join(","),
          },
        ]);
        form.setFieldsValue({
          label: "",
          color: undefined,
          companies: [],
        });
        setLoading(false);
        toastNotification(
          "success",
          row.label + t("toasts.created"),
          "topRight"
        );
      } catch (errInfo) {
        setLoading(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    }
  };

  useEffect(() => {
    const getDepartments = async () => {
      setLoading(true);
      try {
        const res = await MainService.getDepartments();
        if (res.status === 200) {
          const {
            data: { data },
          } = await MainService.getCompaniesInSettings();
          setData(
            res.data.data.map((el, i) => ({ ...el, key: el.id, rank: i + 1 }))
          );
          setCompanies(data);
          setLoading(false);
        }
      } catch (err) {
        setLoading(false);
        console.log(err);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    };
    if (!search && data.length == 0) getDepartments();
  }, [search, data.length, t]);
  useEffect(() => {
    return () => dispatch(setSearch(""));
  }, []);

  const columns = [
    {
      title: t("activities.name"),
      dataIndex: "label",
      key: "label",
      editable: true,
      sorter: (a, b) => a.label.localeCompare(b.label),
      render: (_, record) => {
        return (
          <LabelTable record={record} editingKey={editingKey} edit={edit} />
        );
      },
    },

    {
      title: t("general.companies"),
      dataIndex: "companies",
      key: "companies",

      editable: true,
      render: (_, { companies }) => (
        <div>
          {companies?.map((el) => (
            <div key={el.id}>{el.label}</div>
          ))}
        </div>
      ),
    },
    {
      title: t("activities.color"),
      dataIndex: "color",
      key: "color",

      editable: true,
      render: (_, { color }) => <ColumnColors color={color} colors={colors} />,
    },
  ];
  const handleAdd = () => {
    setId(null);
    const ids = data.map((object) => {
      return object.id;
    });
    const newData = {
      key: Math.max(...ids) + 1,
      label: `  `,
      color: undefined,
      companies: [],
      disabled: true,
    };
    setCurrentPage(Math.ceil([...data, newData].length / pageSize));
    setData([...data, newData]);
    form.setFieldsValue({
      label: "",
      color: undefined,
      companies: [],
    });
    setEditingKey(Math.max(...ids) + 1);
  };
  const onRow = () => {};
  const filteredData = data.filter((item) => {
    let isMatch = false;
    item.companies?.forEach((el) => {
      if (el.label.toLowerCase().includes(search.toLowerCase())) isMatch = true;
    });

    return item.label.toLowerCase().includes(search.toLowerCase()) || isMatch;
  });
  return (
    <Space direction="vertical" style={{ width: "100%" }}>
      <Header
        active={"2"}
        editingKey={editingKey}
        handleAdd={handleAdd}
        btnText={t("helpDesk.addDep")}
        disabled={loading ? true : editingKey ? true : search ? true : false}
        data={filteredData}
        api="departments"
      />

      <NewTableDraggable
        columns={columns}
        setLoading={setLoading}
        isEditing={isEditing}
        data={filteredData}
        setData={setData}
        loading={loading}
        save={save}
        edit={edit}
        EditableCell={EditableCell}
        onFinishFailed={onFinishFailed}
        cancel={cancel}
        form={form}
        apiRank="/rank-departments"
        editingKey={editingKey}
        api="departments"
        onRow={onRow}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        pageSize={pageSize}
        setPageSize={setPageSize}
      />

      <BottomButtonAddRow
        editingKey={editingKey}
        data={filteredData}
        text={t("helpDesk.addDep")}
        handleAdd={handleAdd}
        loading={loading}
        search={search || ""}
      />
    </Space>
  );
};
export default Departments;
