import { URL_ENV } from "index";
import { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { generateAxios } from "services/axiosInstance";
import {
  BigDonutChart,
  DonutChart2,
  DonutChartWithDrillDown,
  DualAxes,
  EvolutionChart,
  GaugeChart,
  HorizontalOneBarChart,
  OneBarChart,
  PieChartWithLegend,
  PieChartWithPercent,
  SemiCirclePie,
} from "../ChartsDashboard";
import {
  Card,
  Col,
  Divider,
  Row,
  Select,
  Skeleton,
  Spin,
  Statistic,
  Table,
  Typography,
} from "antd";
import MainService from "services/main.service";
import { toastNotification } from "components/ToastNotification";
import { icons } from "lucide-react"; // Importer tous les icônes de Lucide
import TableStatsTickets from "./TableStatsTickets";
import { isGuestConnected } from "utils/role";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import {
  setPipelinesTicket,
  setSelectedPipelineInTicket,
} from "new-redux/actions/dashboard.actions";
import {
  getStatByFamily,
  getStatFamilyByField,
} from "pages/home/<USER>";
import CardStat from "../CardStat";
export function LucideIcon({ iconName, color = "", size = 24 }) {
  // Vérifier si l'icône demandée existe
  if (icons[iconName]) {
    const IconComponent = icons[iconName];

    return <IconComponent color={color} size={size} />;
  } else {
    return null;
  }
}
const DashboardTicket2 = ({ start, end, from = "", idPipeline = "" }) => {
  const [statsByFamily, setStatsByFamily] = useState({});
  const [statsPriorities, setStatsPriorities] = useState({});
  const [statsPermiters, setStatsPerimiters] = useState({});
  const [statsTypes, setStatsTypes] = useState({});
  const [statsChannels, setStatsChannels] = useState({});
  const [statsProducts, setStatsProducts] = useState({});
  const [statsClosedTickets, setStatsClosedTickets] = useState({});
  const [agentsClosedTickets, setagentsClosedTickets] = useState({});
  const [loading, setLoading] = useState(false);
  const [cardsTickets, setCardsTickets] = useState([]);
  const [loadCardsTickets, setLoadCardsTickets] = useState([]);

  const [topCLient, setTopClient] = useState({});

  const { i18n } = useTranslation("common");
  const [t] = useTranslation("common");
  const {
    selectedPipelineInTicket: selectedPipeline,
    pipelinesTicket: pipelines,
  } = useSelector((state) => state.dashboardRealTime);
  const dispatch = useDispatch();
  const getStatFamilyByFieldInTicket = async (field) => {
    try {
      const result = await getStatFamilyByField(
        start,
        end,
        6,
        field,
        selectedPipeline
      );
      return result;
    } catch (error) {
      console.error("Error in getStatFamilyByFieldInDeal:", error);
      throw error;
    }
  };

  useEffect(() => {
    const getPipelines = async () => {
      setLoading(true);
      try {
        const response = await MainService.getPipelinesByFamily(6);

        dispatch(
          setPipelinesTicket(
            response?.data?.data.map((el) => ({
              value: el.id,
              label: el.label,
            }))
          )
        );
        if (!selectedPipeline?.value) {
          dispatch(
            setSelectedPipelineInTicket({
              label: response?.data?.data[0]?.label,
              value: response?.data?.data[0]?.id,
            })
          );
        }
        setLoading(false);
      } catch (error) {
        console.log(`Error ${error}`);
        setLoading(false);
        toastNotification("error", t("toasts.somethingWrong"));
      }
    };
    if (!selectedPipeline?.value) getPipelines();
  }, [selectedPipeline?.value, t]);
  useEffect(() => {
    const fetchData = async () => {
      setLoadCardsTickets(true);
      try {
        const res = await generateAxios(
          `${URL_ENV?.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
        ).post(`/get_cardkpi_family`, {
          date_start: start,
          date_end: end,
          lang: i18n.language,
          family_id: 6,
          pipeline_id: selectedPipeline?.value,
          // field_system_id: 55,
        });
        setCardsTickets(res.data.data);
        setLoadCardsTickets(false);

        // setStatsClosedTickets(res.data?.da ta);
      } catch (err) {
        setLoadCardsTickets(false);
      }
    };
    if (selectedPipeline?.value) {
      fetchData();
    }
  }, [start, end, i18n.language, selectedPipeline.value]);
  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await generateAxios(
          `${URL_ENV?.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
        ).post(`/get_sum_kpiclosed_family`, {
          date_start: start,
          date_end: end,
          lang: i18n.language,
          family_id: 6,
          pipeline_id: selectedPipeline?.value,
          // field_system_id: 55,
        });

        setStatsClosedTickets(res.data);
      } catch (err) {}
    };
    if (selectedPipeline?.value) {
      fetchData();
    }
  }, [start, end, i18n.language, selectedPipeline.value]);
  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await generateAxios(
          `${URL_ENV?.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
        ).post(`/get_agent_closed_family`, {
          date_start: start,
          date_end: end,
          lang: i18n.language,
          family_id: 6,
          pipeline_id: selectedPipeline?.value,
          // field_system_id: 55,
        });
        setagentsClosedTickets(res.data);
      } catch (err) {}
    };
    if (selectedPipeline && !isGuestConnected()) {
      fetchData();
    }
  }, [start, end, i18n.language, selectedPipeline.value]);
  //no
  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await getStatByFamily(
          start,
          end,
          6,
          selectedPipeline?.value
        );
        setStatsByFamily({
          ...res.data.data,
          drilldown: {
            series: Object.keys(res.data.data.drilldown).map((pipeline) => ({
              id: pipeline,
              data: res.data.data.drilldown[pipeline].data, // Drilldown data
            })),
          },
        });
      } catch (err) {}
    };
    if (selectedPipeline?.value) fetchData();
  }, [start, end, i18n.language, selectedPipeline.value]);
  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await getStatFamilyByFieldInTicket(57);

        setStatsPriorities(res.data?.data);
      } catch (err) {}
    };
    if (selectedPipeline && !isGuestConnected()) {
      fetchData();
    }
  }, [start, end, i18n.language, selectedPipeline.value]);
  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await getStatFamilyByFieldInTicket(52);

        setStatsPerimiters(res.data?.data);
      } catch (err) {}
    };
    if (selectedPipeline && !isGuestConnected()) {
      fetchData();
    }
  }, [start, end, i18n.language, selectedPipeline.value]);
  //no
  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await getStatFamilyByFieldInTicket(54);

        setStatsTypes(res.data?.data);
      } catch (err) {}
    };
    if (selectedPipeline?.value) {
      fetchData();
    }
  }, [start, end, i18n.language, selectedPipeline.value]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await getStatFamilyByFieldInTicket(55);

        setStatsChannels(res.data?.data);
      } catch (err) {}
    };
    if (selectedPipeline && !isGuestConnected()) {
      fetchData();
    }
  }, [start, end, i18n.language, selectedPipeline.value]);
  //no

  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await generateAxios(
          `${URL_ENV?.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
        ).post(`/get_topclient_family`, {
          date_start: start,
          date_end: end,
          lang: i18n.language,
          family_id: 6,
          pipeline_id: selectedPipeline?.value,
          // field_system_id: 55,
        });
        setTopClient(res.data);
      } catch (err) {}
    };
    if (selectedPipeline && !isGuestConnected()) {
      fetchData();
    }
  }, [start, end, i18n.language, selectedPipeline.value]);
  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await getStatFamilyByFieldInTicket(59);
        setStatsProducts(res.data?.data);
      } catch (err) {}
    };
    if (selectedPipeline && !isGuestConnected()) {
      fetchData();
    }
  }, [start, end, i18n.language, selectedPipeline.value]);

  // useEffect(() => {
  //   const fetchData = async () => {
  //     try {
  //       const res = await generateAxios(
  //         `${URL_ENV?.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
  //       ).post(`/get_stat_family`, {
  //         date_start: start,
  //         date_end: end,
  //         lang: i18n.language,
  //         family_id: 6,
  //       });

  //       setStatsByFamily({
  //         ...res.data.data,
  //         drilldown: {
  //           series: Object.keys(res.data.data.drilldown).map((pipeline) => ({
  //             id: pipeline,
  //             data: res.data.data.drilldown[pipeline].data, // Drilldown data
  //           })),
  //         },
  //       });
  //     } catch (err) {}
  //   };
  //   fetchData();
  // }, [start, end, i18n.language]);
  const gaugeDataTickets = useMemo(
    () => ({
      total: 25,
      unit: "%",
      used_storage: 10,
      usage_percentage: 60, // Calcul dynamique si nécessaire
      available: 5,
      title: "",
    }),
    []
  );

  const dataAgents = [
    { key: "1", agent: "Nizar Tlili", traite: 10, total: 50 },
    { key: "2", agent: "Sabrine Gmati", traite: 20, total: 80 },
    { key: "3", agent: "Anissa Ayari", traite: 15, total: 60 },
    { key: "4", agent: "Seif", traite: 25, total: 100 },
    { key: "4", agent: "Iselm", traite: 17, total: 67 },
  ];

  // Colonnes du tableau
  const columnsAgents = [
    {
      title: "Agent traitant",
      dataIndex: "agent",
      key: "agent",
    },
    {
      title: "Traité",
      dataIndex: "traite",
      key: "traite",
    },
    {
      title: "Total",
      dataIndex: "total",
      key: "total",
    },
  ];
  const dataCritiqueTypes = [
    {
      key: "1",
      reference: "TCK_0001368",
      organisation: "Nrj",
      perimetre: "IN",
      pipeline: "PL. BU-CCC",
      type: "Déblocage",
      canal: "Email",
      sujet: "RE: déblocage",
      severite: "Normale",
      agent: "Loudhaief_Saief",
      sla: "2025-03-06 17:07:16",
    },
    {
      key: "2",
      reference: "TCK_0001369",
      organisation: "Godiez",
      perimetre: "IN",
      pipeline: "PL. BU-CCC",
      type: "Demande",
      canal: "Email",
      sujet: "SDA a supprimer",
      severite: "Normale",
      agent: "Gmati_sabrine",
      sla: "2025-03-06 17:40:05",
    },
    {
      key: "3",
      reference: "TCK_0001370",
      organisation: "Comunik",
      perimetre: "IN",
      pipeline: "PL. BU-CCC",
      type: "Réclamation",
      canal: "Email",
      sujet: "Problème récurrent des hachures en écoute sur le panneau live",
      severite: "Normale",
      agent: "Gmati_sabrine",
      sla: "2025-03-06 17:42:28",
    },
    {
      key: "4",
      reference: "TCK_0001371",
      organisation: "BIAR",
      perimetre: "IN",
      pipeline: "PL. BU-CCC",
      type: "Déblocage de SDA",
      canal: "Call",
      sujet: "attente",
      severite: "Normale",
      agent: "Gmati_sabrine",
      sla: "2025-03-06 18:30:03",
    },
    {
      key: "5",
      reference: "TCK_0001374",
      organisation: "STB",
      perimetre: "IN",
      pipeline: "PL.BU-VOIP",
      type: "Demande",
      canal: "Email",
      sujet: "annulation du renvoi des appels",
      severite: "Normale",
      agent: "",
      sla: "2025-03-06",
    },
  ];

  // Colonnes du tableau
  const columnsCritiqueTypes = [
    { title: "Référence", dataIndex: "reference", key: "reference" },
    { title: "Organisation", dataIndex: "organisation", key: "organisation" },
    { title: "Périmètres", dataIndex: "perimetre", key: "perimetre" },
    { title: "Pipeline", dataIndex: "pipeline", key: "pipeline" },
    { title: "Type", dataIndex: "type", key: "type" },
    { title: "Canal", dataIndex: "canal", key: "canal" },
    { title: "Sujet", dataIndex: "sujet", key: "sujet" },
    { title: "Sévérité", dataIndex: "severite", key: "severite" },
    { title: "Agent Traitant", dataIndex: "agent", key: "agent" },
    { title: "SLA", dataIndex: "sla", key: "sla" },
  ];
  return (
    <Spin spinning={loading}>
      <Card
        style={{ background: "#F8FAFC", border: 0 }}
        title={
          <div className="mb-3 flex max-w-[350px] flex-col  justify-start gap-y-0.5">
            <Typography.Text>
              {t("emailTemplates.plsSelect")} pipeline
            </Typography.Text>
            <Select
              showSearch
              popupMatchSelectWidth={false}
              filterOption={(input, option) =>
                (option?.label ?? "")
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
              style={{ minWidth: 250 }}
              onChange={(value, data) => {
                dispatch(
                  setSelectedPipelineInTicket({
                    label: data.label,
                    value: data.value,
                  })
                );
              }}
              value={selectedPipeline?.value}
              options={pipelines}
            />
          </div>
        }
      >
        <div
          style={{
            height: `calc(100vh - ${from === "drawer" ? "164" : "315"}px)`,
          }}
          className={`flex flex-col gap-4 overflow-y-auto overflow-x-hidden`}
        >
          {" "}
          <Divider>
            {" "}
            <Typography.Title level={4}>
              Vue d’ensemble (Dashboard principal)
            </Typography.Title>
          </Divider>
          <Row gutter={[16, 16]}>
            {!loadCardsTickets
              ? [...cardsTickets, ...cardsTickets].map((el, i) => (
                  <Col
                    span={from === "drawer" ? 12 : 6}
                    key={`cardsTickets_Col_${i}`}
                  >
                    <Card
                      bordered={true}
                      // style={{ background: el.color }}
                    >
                      <Statistic
                        title={
                          <div className="flex items-center justify-between">
                            <span className="font-semibold">
                              {el.name?.toUpperCase()}
                            </span>
                            <LucideIcon iconName={el?.icon} color={el?.color} />
                          </div>
                        }
                        value={el?.value}
                        precision={0}
                        valueStyle={{
                          color: el?.color,
                        }}
                        // prefix={<ArrowUpOutlined />}
                        // suffix="%"
                      />
                    </Card>
                  </Col>
                ))
              : Array.from(
                  { length: from === "drawer" ? 2 : 8 },
                  () => null
                ).map((_, index) => (
                  <Col
                    style={{ flex: from === "drawer" ? "0 0 50%" : "0 0 25%" }}
                    key={`load_cardsTickets_Col_${index}`}
                  >
                    <div className="skeletonSelectDepartments grow">
                      <Skeleton.Input
                        key={index}
                        active
                        style={{ width: "100%", height: "84px" }}
                      />
                    </div>
                  </Col>
                ))}
          </Row>
          <Row gutter={[16, 16]}>
            <Col className="gutter-row" span={from === "drawer" ? 12 : 9}>
              <CardStat title="Temps moyen de résolution">
                <OneBarChart
                  data={{
                    data: [
                      { name: "Nizar Tlili", y: 50 },
                      { name: "Sabrine Gmati", y: 40 },
                      { name: "Anissa Ayari", y: 30 },
                      { name: "Seif", y: 70 },
                      { name: "Iselm", y: 20 },
                    ],
                    name: "",
                  }}
                />
              </CardStat>
            </Col>
            <Col className="gutter-row" span={from === "drawer" ? 12 : 7}>
              <CardStat title={statsClosedTickets?.name}>
                <GaugeChart
                  data={{
                    total: statsClosedTickets?.data?.total,
                    used_storage: statsClosedTickets?.data?.used,
                    title: statsClosedTickets?.data?.name,
                    unit: statsClosedTickets?.data?.name,
                  }}
                  title=""
                  name={statsClosedTickets?.name}
                  inverseColor={true}
                />
              </CardStat>
            </Col>
            <Col span={from === "drawer" ? 12 : 8}>
              <Card
                title={
                  <div className="flex flex-col">
                    <div
                      style={{
                        fontSize: "1.2em",
                        color: "rgb(51, 51, 51)",
                        fontWeight: "bold",
                        fill: "rgb(51, 51, 51)",
                      }}
                    >
                      {statsByFamily?.name}
                    </div>
                    <div>
                      {" "}
                      {statsByFamily?.parent_name !== undefined &&
                      statsByFamily?.series?.find(
                        (el) => el.name === selectedPipeline.label
                      )?.y > 0
                        ? statsByFamily?.parent_name +
                          " " +
                          statsByFamily?.series?.find(
                            (el) => el.name === selectedPipeline.label
                          )?.y
                        : ""}
                    </div>
                  </div>
                }
              >
                <DonutChartWithDrillDown
                  data={{ ...statsByFamily, name: "" }}
                  subtitle={t("dashboard.independantFilter", {
                    name: "pipeline",
                  })}
                  selected={selectedPipeline.label}
                />
              </Card>
              {/* {isGuestConnected() ? (
                <DonutChartWithDrillDown
                  data={statsByFamily}
                  subtitle={t("dashboard.independantFilter", {
                    name: "pipeline",
                  })}
                  selected={namePipeline}
                />
              ) : (
                <DonutChartWithDrillDown
                  data={statsByFamily}
                  // subtitle={t("dashboard.independantFilter", {
                  //   name: "pipeline",
                  // })}
                  subtitle=""
                  selected={""}
                />
              )} */}
            </Col>

            {!isGuestConnected() ? (
              <>
                <Col className="gutter-row" span={8}>
                  <CardStat title={"Pic des tickets per period"}>
                    <EvolutionChart
                      data={{
                        categories: [
                          "2024-12",
                          "2025-01",
                          "2025-02",
                          "2025-03",
                        ],
                        series: [
                          {
                            name: "",
                            data: [6, 49, 95, 68],
                          },
                        ],

                        name: "",
                      }}
                    />
                  </CardStat>
                </Col>
                <Col className="gutter-row" span={from === "drawer" ? 12 : 8}>
                  <CardStat title={t("dashboard.ticket_by_channel")}>
                    <OneBarChart
                      data={{
                        ...statsChannels,
                        name: "",
                      }}
                    />
                  </CardStat>
                </Col>
                <Col span={from === "drawer" ? 12 : 8}>
                  <CardStat title={agentsClosedTickets?.name}>
                    <DualAxes
                      data={{ ...agentsClosedTickets, name: "" }}
                      // isExistDate={false}
                    />
                  </CardStat>
                </Col>
                <Col className="gutter-row" span={from === "drawer" ? 12 : 8}>
                  <CardStat title="Taux de tickets résolus dés la premiére réponse">
                    <GaugeChart data={gaugeDataTickets} />
                  </CardStat>
                </Col>
                <Col className="gutter-row" span={from === "drawer" ? 12 : 8}>
                  <CardStat title={topCLient?.name}>
                    <HorizontalOneBarChart
                      data={{ ...topCLient, name: "" }}
                      // isExistDate={false}
                    />
                  </CardStat>
                </Col>
                <Col span={from === "drawer" ? 12 : 8}>
                  <CardStat title={t("dashboard.ticket_by_priority")}>
                    <PieChartWithLegend
                      data={{
                        ...statsPriorities,
                        name: "",
                      }}
                      withArrow={true}
                    />
                  </CardStat>
                </Col>
                {/* <Col span={from === "drawer" ? 12 : 8}>
                  <CardStat
                    title={t("dashboard.ticket_by_perimiter")}
                    style={{
                      fontSize: "1.2em",
                      color: "rgb(51, 51, 51)",
                      fontWeight: "bold",
                      fill: "rgb(51, 51, 51)",
                    }}
                  >
                    <PieChartWithLegend
                      data={{
                        ...statsPermiters,
                        name: "",
                      }}
                      withArrow={true}
                    />
                  </CardStat>
                </Col> */}
                {/* <Col span={from === "drawer" ? 12 : 8}>
                  <CardStat
                    title={t("dashboard.ticket_by_type")}
                    style={{
                      fontSize: "1.2em",
                      color: "rgb(51, 51, 51)",
                      fontWeight: "bold",
                      fill: "rgb(51, 51, 51)",
                    }}
                  >
                    <BigDonutChart data={statsTypes?.data} name="" />
                  </CardStat>
                </Col> */}
                {/* 
                <Col span={from === "drawer" ? 12 : 8}>
                  <CardStat title={t("dashboard.ticket_by_product")}>
                    <PieChartWithLegend
                      data={{
                        ...statsProducts,
                        name: "",
                      }}
                      withArrow={true}
                    />
                  </CardStat>
                </Col> */}
                {selectedPipeline?.value ? (
                  <Col span={24}>
                    <TableStatsTickets
                      start={start}
                      end={end}
                      family_id={6}
                      pipeline_id={selectedPipeline?.value}
                    />
                  </Col>
                ) : null}
              </>
            ) : (
              <CardStat title={agentsClosedTickets?.name}>
                <DualAxes
                  data={{ ...agentsClosedTickets, name: "" }}
                  // isExistDate={false}
                />
              </CardStat>
            )}
            <Divider>
              {" "}
              <Typography.Title level={4}>
                Performance des agents
              </Typography.Title>
            </Divider>
            <Col span={12}>
              <CardStat
                title="            Nombre des tickets traité/Cloturé
"
              >
                <Table
                  size="small"
                  dataSource={dataAgents}
                  columns={columnsAgents}
                  pagination={false}
                />
              </CardStat>
            </Col>
            <Col span={12}>
              <CardStat
                title="Temp moyen de resolution par agent"
                style={{ height: 217 }}
              >
                <OneBarChart
                  data={{
                    data: [
                      { name: "Nizar Tlili", y: 3 },
                      { name: "Sabrine Gmari", y: 4 },
                      { name: "Anissa Ayari", y: 3 },
                      { name: "Seif", y: 7 },
                      { name: "Iselm", y: 2 },
                    ],
                    name: "",
                  }}
                  height={217}
                />
              </CardStat>
            </Col>
            <Col span={8}>
              <CardStat title="Temp moyen de premier réponse par agent">
                <OneBarChart
                  data={{
                    data: [
                      { name: "Nizar Tlili", y: 3 },
                      { name: "Sabrine Gmari", y: 2 },
                      { name: "Anissa Ayari", y: 1.5 },
                      { name: "Seif", y: 3.5 },
                      { name: "Iselm", y: 4 },
                    ],
                    name: "",
                  }}
                />
              </CardStat>
            </Col>
            <Col span={8}>
              <CardStat title="Les agents les plus performants">
                <HorizontalOneBarChart
                  data={{
                    categories: [
                      "Nizar Tlili",
                      "Sabrine Gmati",
                      "Anissa Ayari",
                      "Seif",
                      "LIVE CHAT",
                    ],
                    data: [
                      { name: "Nizar Tlili", y: 7 / 10 },
                      { name: "Sabrine Gmati", y: 5 / 10 },
                      { name: "Anissa Ayari", y: 3 / 10 },
                      { name: "Seif", y: 8 / 10 },
                      { name: "Iselm", y: 4 / 10 },
                    ],
                    name: "",
                  }}
                />
              </CardStat>
            </Col>
            <Col span={8}>
              <CardStat title={"% de satisfaction client par agent"}>
                <EvolutionChart
                  data={{
                    categories: [
                      "Nizar Tlili",
                      "Sabrine Gmati",
                      "Anissa Ayari",
                      "Seif",
                      "Iselm",
                    ],
                    series: [
                      {
                        name: "",
                        data: [40, 70, 30, 60, 50],
                      },
                    ],

                    name: "",
                  }}
                />
              </CardStat>
            </Col>
            <Divider>
              {" "}
              <Typography.Title level={4}>
                Analyse des types de tickets{" "}
              </Typography.Title>
            </Divider>
            <Col span={from === "drawer" ? 12 : 8}>
              <CardStat title={t("dashboard.ticket_by_type")}>
                <BigDonutChart data={statsTypes?.data} name="" />
              </CardStat>
            </Col>
            <Col span={from === "drawer" ? 12 : 8}>
              <CardStat title={"Taux de resolution par type"}>
                <DonutChart2
                  data={[
                    { name: "Commercial", y: 3 },
                    { name: "Sav", y: 2 },
                    { name: "Incident", y: 1 },
                  ]}
                  total={6}
                  name=""
                />
              </CardStat>
            </Col>
            <Col span={from === "drawer" ? 12 : 8}>
              <CardStat title={"Temp moyen de resolution par catégorie"}>
                <PieChartWithLegend
                  data={{
                    data: [
                      { name: "Unified", y: 25 },
                      { name: "Voip", y: 25 },
                      { name: "HELPDESK", y: 22 },
                      { name: "Contact", y: 29 },
                      { name: "SALES", y: 16 },
                    ],
                    name: "",
                  }}
                  name=""
                />
              </CardStat>
            </Col>
            <Col span={6}>
              <CardStat
                title={"Nombre des tickets les plus critiques par type"}
              >
                <HorizontalOneBarChart
                  data={{
                    categories: ["Incident", "Sav", "Demande"],
                    data: [
                      { name: "Incident", y: 196 },
                      { name: "Sav", y: 136 },
                      { name: "Demande", y: 97 },
                    ],
                    name: "",
                  }}
                />
              </CardStat>
            </Col>
            <Col span={from === "drawer" ? 12 : 18}>
              <CardStat title={"Liste des tickets les plus critique par types"}>
                <Table
                  dataSource={dataCritiqueTypes}
                  columns={columnsCritiqueTypes}
                  size="small"
                  pagination={false}
                />
              </CardStat>
            </Col>
            <Divider>
              <Typography.Title level={4}>
                Analyse des canaux de contact
              </Typography.Title>
            </Divider>
            <Col span={8}>
              <CardStat
                title={
                  "Comparaison Email vs Téléphone vs Chat vs Réseaux sociaux"
                }
              >
                <SemiCirclePie
                  data={{
                    name: "",
                    content: "Nombre des ticket",
                    data: [
                      ["CHAT", 17.89],
                      ["PHONE", 27.64],
                      ["EMAIL", 26.83],
                      ["WHATSAPP", 17.89],
                      ["LIVE CHAT", 7.69],
                    ],
                  }}
                />
              </CardStat>
            </Col>
            <Col span={8}>
              <CardStat title={"Temp moyen de traitement par canal"}>
                <OneBarChart
                  data={{
                    data: [
                      { name: "CHAT", y: 1.5 },
                      { name: "PHONE", y: 4 },
                      { name: "EMAIL", y: 3 },
                      { name: "WHATSAPP", y: 2.5 },
                      { name: "LIVE CHAT", y: 2 },
                    ],
                    name: "",
                  }}
                />
              </CardStat>
            </Col>
            <Col span={8}>
              <CardStat title="Taux de satisfaction client par canal">
                <HorizontalOneBarChart
                  data={{
                    categories: [
                      "CHAT",
                      "PHONE",
                      "EMAIL",
                      "WHATSAPP",
                      "LIVE CHAT",
                    ],
                    data: [
                      { name: "CHAT", y: 7 },
                      { name: "PHONE", y: 5 },
                      { name: "EMAIL", y: 3 },
                      { name: "WHATSAPP", y: 8 },
                      { name: "LIVE CHAT", y: 4 },
                    ],
                    name: "",
                  }}
                />
              </CardStat>
            </Col>
            <Col span={8}>
              <CardStat
                title={"Taux de conversion des tickets en solution resolue"}
              >
                <EvolutionChart
                  data={{
                    categories: [
                      "CHAT",
                      "PHONE",
                      "EMAIL",
                      "WHATSAPP",
                      "LIVE CHAT",
                    ],
                    series: [
                      {
                        name: "",
                        data: [40, 70, 30, 60, 50],
                      },
                    ],

                    name: "",
                  }}
                />
              </CardStat>
            </Col>
            <Col span={8}>
              <CardStat title={"Les canaux les plus traités"}>
                <PieChartWithPercent
                  data={{
                    data: [
                      { name: "Appel", y: 50 },
                      { name: "Email", y: 25 },
                      { name: "Whatsapp", y: 25 },
                    ],
                    name: "",
                  }}
                  name=""
                />
              </CardStat>
            </Col>
            <Col span={8}>
              <CardStat title={"Les canaux les moins traités"}>
                <PieChartWithPercent
                  data={{
                    data: [
                      { name: "CHAT", y: 50 },
                      { name: "LIVE CHAT", y: 25 },
                    ],
                    name: "",
                  }}
                  name=""
                />
              </CardStat>
            </Col>
            <Divider>
              {" "}
              <Typography.Title level={4}>
                Expérience et satisfaction client
              </Typography.Title>
            </Divider>
          </Row>
        </div>
      </Card>
    </Spin>
  );
};

export default DashboardTicket2;
