



.remove_border_right_tr {
  position: initial !important;
}

.remove_border_right_tr.editing<PERSON>ey span {
  margin-top: 10px !important;
}

.remove_border_right_tr span {
  margin-top: 5px !important;
}

.selected-row:hover td {
  cursor: pointer;
  background-color: rgb(237, 245, 255) !important;
}

.selectTimeCheckList .ant-select-selector {
  border: 0 !important;
}

/* .ant-list-item-meta-title {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
} */

.addGroup-uploader.ant-upload-wrapper.ant-upload-picture-card-wrapper
  .ant-upload.ant-upload-select {
  width: 650px;
}

:root {
  --listNotifications: calc(100vw - 200px);
}

.ant-menu-item:has(.profile) {
  display: flex !important;
  align-items: center;
}
.select-stages-dashboard.ant-select-dropdown
  .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  background-color: rgba(0, 0, 0, 0.04) !important;
  opacity: 0.7 !important;
}
.stagePipelines ol {
  display: flex;
  align-items: center;
}
