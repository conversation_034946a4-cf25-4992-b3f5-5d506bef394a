import { RightCircleOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, But<PERSON> } from "antd";
import { URL_ENV } from "index";
import { SET_EMAILS_STATS } from "new-redux/constants";
import DisplayAvatar from "pages/voip/components/DisplayAvatar";
import { truncateString } from "pages/voip/helpers/helpersFunc";

export const getLabelNameById = (usedAccount, dataAccounts, pathName, t) => {
  const labelId = Number(pathName.split("/")[4]);
  let label =
    labelId && usedAccount?.labels?.length
      ? usedAccount?.labels?.find((el) => el.id === labelId)?.label
      : null;
  if (!label) {
    const labels = dataAccounts.map((item) => item?.labels || []).flat();
    label = labels.find((el) => el.id === labelId)?.label;
  }
  return label ? label : null;
};
//
export const handleDataUsersToAssign = (data, user, onlineUser, t) => {
  if (!Array.isArray(data) || data.length === 0) return [];

  const sortedData = [...data].sort((a, b) => {
    if (a.owner === user.id) return -1;
    if (b.owner === user.id) return 1;
    return b.kpi_Assign - a.kpi_Assign;
  });

  return sortedData.map((item) => ({
    value: item.owner,
    searchOption: item.label_data,
    label: (
      <div key={item.owner} className="relative flex items-center space-x-2">
        <div className="_avatar_">
          <Badge
            dot
            offset={[-1, 19]}
            color={
              onlineUser[item?.uuid] === "away"
                ? "orange"
                : onlineUser[item?.uuid] === "busy"
                ? "red"
                : onlineUser[item?.uuid] === "online"
                ? "green"
                : "#a6a6a6"
            }
          >
            <DisplayAvatar
              size={22}
              cursor="pointer"
              name={item.label_data}
              urlImg={
                item?.avatar?.path
                  ? `${URL_ENV?.REACT_APP_BASE_URL}${URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL}${item.avatar.path}`
                  : undefined
              }
            />
          </Badge>
        </div>
        <div className="flex flex-1 items-center justify-between">
          <p className="flex-1 truncate">
            {item.owner === user.id
              ? t("voip.me")
              : truncateString(item.label_data, 22)}
          </p>

          <p className="ml-2 whitespace-nowrap font-semibold text-slate-500">
            {item.kpi_Assign}
          </p>
        </div>
      </div>
    ),
  }));
};
//
export const decrementNumberMailsInLabel =
  (accountId, labelIds, statsMail) => (dispatch) => {
    if (
      !accountId ||
      !statsMail ||
      !Array.isArray(labelIds) ||
      labelIds.length === 0
    ) {
      return;
    }

    const accountStats = statsMail[accountId];
    if (!accountStats || !accountStats.labels) {
      return;
    }

    const newLabels = { ...accountStats.labels };
    let updated = false;

    labelIds.forEach((labelId) => {
      if (typeof newLabels[labelId] === "number") {
        const currentCount = newLabels[labelId];

        if (currentCount > 0) {
          newLabels[labelId] = currentCount - 1;
          updated = true;
        }
      }
    });

    if (!updated) {
      return;
    }

    const newStatsMail = {
      ...statsMail,
      [accountId]: {
        ...accountStats,
        labels: newLabels,
      },
    };

    dispatch({
      type: SET_EMAILS_STATS,
      payload: newStatsMail,
    });
  };
//
export const DisplayMailsEvents = ({ message, navigateTo, t }) => {
  return (
    <div className="space-y-2">
      {message}
      <div className="flex justify-end">
        <Button
          type="link"
          icon={<RightCircleOutlined />}
          onClick={() => navigateTo()}
        >
          {t("common:chat.goto")}
        </Button>
      </div>
    </div>
  );
};
