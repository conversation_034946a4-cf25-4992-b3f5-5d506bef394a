export const updateViewAccordingToNotification = (
  notificationType,
  payload,
  stateSetter,
  stateGetter
) => {
  switch (notificationType) {
    case "create":
      payload !== null && stateSetter([payload, ...stateGetter]);
      break;

    case "update_task":
      let oldArray = [...stateGetter];
      let updatedItemIndex =
        oldArray && oldArray.findIndex((element) => element?.id === payload?.id);
      let newArray =
        updatedItemIndex > -1
          ? oldArray && oldArray.map((element, i) => (updatedItemIndex === i ? payload : element))
          : [payload, ...stateGetter];

      stateSetter(newArray);
      break;

    case "delete_task":
      let data = [...stateGetter];
      let filteredArray = data && data.filter((item) => payload && !payload.includes(item?.id));
      return stateSetter(filteredArray);

    default:
      return stateGetter;
  }
};
