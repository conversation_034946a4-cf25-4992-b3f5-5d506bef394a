import React, { useEffect, useLayoutEffect, useState } from "react";
import { Row, Col } from "antd";
import Pipeline from "../../components/Pipeline";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate } from "react-router-dom";
import Stage from "../../components/Stage";
import TabsDetails from "../../components/Tabs";
import CheckList from "../../components/CheckList";
import StepsCheckList from "../../components/StepsCheckList";

const CheckLists = () => {
  const [t] = useTranslation("common");
  const [editingKey, setEditingKey] = useState("");
  const [editingKeyStage, setEditingKeyStage] = useState("");
  const [dataSteps, setDataSteps] = useState([]);
  const [keyTab, setKeyTab] = useState("");
  const [checkListId, setCheckListId] = useState("");
  const { pathname } = useLocation();
  const navigate = useNavigate();

  useLayoutEffect(() => {
    if (pathname == "/settings/pipeline/tickets") {
      setKeyTab("1");
    } else if (pathname == "/settings/pipeline/deals") {
      setKeyTab("2");
    } else if (pathname == "/settings/pipeline/projects") {
      setKeyTab("3");
    } else {
      setKeyTab("1");
    }
    setCheckListId("");
    setEditingKey("");
    setEditingKeyStage("");
  }, []);
  // useEffect(() => {
  //   if (keyTab) {
  //     if (keyTab == 1) {
  //       navigate("/settings/pipeline/tickets");
  //     } else if (keyTab == 2) {
  //       navigate("/settings/pipeline/deals");
  //     } else if (keyTab == 3) {
  //       navigate("/settings/pipeline/projects");
  //     }
  //     setCheckListId("");
  //     setEditingKey("");
  //     setEditingKeyStage("");
  //   }
  // }, [keyTab, navigate]);

  const items = [
    {
      label: (
        <div
          onClick={() => {
            setKeyTab("1");
            // navigate(`/settings/pipeline/tickets`);
          }}
        >
          Tickets
        </div>
      ),
      key: "1",
      item: "tickets",
    },
    {
      label: (
        <div
          onClick={() => {
            setKeyTab("2");
            // navigate(`/settings/pipeline/deals`);
          }}
        >
          {t("menu1.deals")}
        </div>
      ),
      key: "2",
      item: "deals",
    },
    {
      label: (
        <div
          onClick={() => {
            setKeyTab("3");
            // navigate(`/settings/pipeline/projects`);
          }}
        >
          {t("menu2.project")}
        </div>
      ),
      key: "3",
      item: "projects",
    },
  ];

  return (
    <div className="mt-4">
      {/* {keyTab ? (
        <TabsDetails items={items} keyTab={keyTab} setKey={setKeyTab} />
      ) : (
        ""
      )} */}
      <Row>
        <Col
          xs={24}
          sm={24}
          md={24}
          lg={11}
          style={{
            // borderRight: "solid 1px #e2e8f0",
            padding: "0 5px",
            marginLeft: "15px",
            boxShadow:
              "0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",
          }}
        >
          <CheckList
            checkListId={checkListId}
            setCheckListId={setCheckListId}
            editingKey={editingKeyStage}
            setEditingKey={setEditingKeyStage}
          />
        </Col>
        <Col
          xs={24}
          sm={24}
          md={24}
          lg={12}
          style={{
            padding: "0 5px",
            marginLeft: "12px",
            boxShadow:
              "0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",
          }}
        >
          <StepsCheckList
            setEditingKey={setEditingKeyStage}
            editingKey={editingKeyStage}
            checkListId={checkListId}
            data={dataSteps}
            setData={setDataSteps}
          />
        </Col>
      </Row>
    </div>
  );
};

export default CheckLists;
