import React from "react";
import { Quill } from "react-quill";

import "react-quill/dist/quill.snow.css";
import quillEmoji from "react-quill-emoji";
import "react-quill-emoji/dist/quill-emoji.css";
import "./editorCSS.css";
import { But<PERSON> } from "antd";
import { AiOutlineBold, AiOutlineItalic } from "react-icons/ai";
import { BsCodeSlash } from "react-icons/bs";
import ImageResize from "quill-image-resize-module-react";

const Emoji = Quill.import("formats/emoji");
Emoji.whitelist = ["extra-small", "small", "medium", "large"];
Quill.register(Emoji, true);

Quill.register("modules/imageResize", ImageResize);

Quill.register(
  {
    "formats/emoji": quillEmoji.EmojiBlot,
    "modules/emoji-toolbar": quillEmoji.ToolbarEmoji,
    "modules/emoji-textarea": quillEmoji.TextAreaEmoji,
    "modules/emoji-shortname": quillEmoji.ShortNameEmoji,
  },
  true
);

function insertlastName() {
  const cursorPosition = this.quill.getSelection().index;
  this.quill.insertText(cursorPosition, "Bouzaien");
}

function insertfirstName() {
  const cursorPosition = this.quill.getSelection().index;
  this.quill.insertText(cursorPosition, "Aymen");
}

function insertemail() {
  const cursorPosition = this.quill.getSelection().index;
  this.quill.insertText(cursorPosition, "<EMAIL>");
}

// insert an @ symbol which will trigger the mentions
function insertMention() {
  const cursorPosition = this.quill.getSelection().index;
  this.quill.insertText(cursorPosition, "@");
  this.quill.setSelection(cursorPosition + 1);
}

// Add sizes to whitelist and register them
const Size = Quill.import("formats/size");
Size.whitelist = ["extra-small", "small", "medium", "large"];
Quill.register(Size, true);

// Modules object for setting up the Quill editor
export const modules = {
  toolbar: {
    container: "#toolbar",
    handlers: {
      mention: insertMention,
      mentionfirstname: insertlastName,
      mentionlastName: insertfirstName,
      mentionemail: insertemail,
    },
  },
  imageResize: {
    parchment: Quill.import("parchment"),
    modules: ["Resize", "DisplaySize"],
  },
  "emoji-toolbar": true,
  "emoji-textarea": false,
  "emoji-shortname": true,
};

// Formats objects for setting up the Quill editor
export const formats = [
  "header",
  "bold",
  "italic",
  "align",
  "blockquote",
  "background",
  "list",
  "bullet",
  "indent",
  "link",
  "image",
  "color",
  "code-block",
  "emoji",
];

// Quill Toolbar component
export const QuillToolbar = ({ showFields, setShowFields }) => (
  <div id="toolbar">
    <span
      className="ql-formats space-x-12"
      style={{
        display: showFields ? "block" : "none",
      }}
    >
      <Button className={`ql-mentionlastName`}>Last Name</Button>
      <Button className={`ql-mentionfirstname`}>First Name</Button>
      <Button className={`ql-mentionemail`}>Email adress</Button>
    </span>

    <div>
      <span className="ql-formats">
        <select className="ql-header" defaultValue="3">
          <option value="1">h1</option>
          <option value="2">h2</option>
          <option value="3">Normal text</option>
        </select>
      </span>

      <span className="ql-formats">
        <button className="ql-bold">
          <AiOutlineBold className="h-4 w-4" />
        </button>
        <button className="ql-italic">
          <AiOutlineItalic className="h-4 w-4" />
        </button>
      </span>
      <span className="ql-formats">
        <button className="ql-list" value="ordered" />
        <button className="ql-list" value="bullet" />
      </span>
      <span className="ql-formats">
        <button className="ql-blockquote" />
      </span>
      <span className="ql-formats">
        <select className="ql-color" />
        <select className="ql-align" />
      </span>
      <span className="ql-formats">
        <button className="ql-image" />
        <button className="ql-emoji" />
        <button className="ql-code-block">
          <BsCodeSlash className="h-4 w-4" />
        </button>
      </span>
    </div>
  </div>
);

export default QuillToolbar;
