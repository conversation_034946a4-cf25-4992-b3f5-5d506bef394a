import { SET_NEW_LOG_GROUPS_QUEUES_IN_PROCESS } from "new-redux/constants";
import DisplayAvatar from "../components/DisplayAvatar";
import { URL_ENV } from "index";
import { toastNotification } from "components/ToastNotification";

const generateMessage = (data, t) => {
  const srcSource = data?.src_name || data?.src;
  const dstSource = data?.queue_num
    ? `${data.queue_name} (${data.queue_num})`
    : `${data.group_name || ""} (${data.group_num || ""})`;

  return (
    <span
      dangerouslySetInnerHTML={{
        __html: t("voip.infoCallInProcess", {
          member: data?.dst_name,
          srcSource,
          dstSource,
        }),
      }}
    />
  );
};

const getNotificationIcon = (dstName, dstImage) => (
  <DisplayAvatar
    name={dstName}
    size={32}
    urlImg={
      dstImage
        ? `${URL_ENV.REACT_APP_BASE_URL}${URL_ENV.REACT_APP_SUFFIX_AVATAR_URL}${dstName}`
        : null
    }
  />
);

export const handleEventCallInProgress =
  (data, currentUserId, t) => (dispatch) => {
    if (!data) return;
    // console.log(data);

    const { type, dst_name, dst_image, dst_id, src_id } = data;

    let condition = type === "QUEUE" || type === "GROUPE";
    // &&
    // currentUserId !== dst_id &&
    // currentUserId !== src_id;

    dispatch({
      type: SET_NEW_LOG_GROUPS_QUEUES_IN_PROCESS,
      payload: {
        data: { ...data, disposition: "IN_PROGRESS" },
        isQueue: condition,
      },
    });
    condition =
      condition && currentUserId !== dst_id && currentUserId !== src_id;
    if (condition) {
      const message = generateMessage(data, t);
      const notifIcon = getNotificationIcon(dst_name, dst_image);

      toastNotification("open", message, "topRight", 7, notifIcon, 3);
    }
  };
