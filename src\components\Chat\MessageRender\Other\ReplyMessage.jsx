import { Typography } from "antd";
import {
  audioMessageTypes,
  convertToPlain,
  fileMessageTypes,
  getUserFromMsg,
  getName,
  imageMessageTypes,
} from "pages/layouts/chat/utils/ConversationUtils";
import { useTranslation } from "react-i18next";
const { Text } = Typography;

function ReplyMessage({ item, source, searchReplies }) {
  const { t } = useTranslation("common");

  return (
    convertToPlain(item?.main_message?.message).trim().length === 0 && (
      <>
        {imageMessageTypes.includes(item?.main_message?.type) ? (
          <div className="flex items-start space-x-1">
            <Text
              type="secondary"
              className={`${
                source === "main"
                  ? "whitespace-nowrap"
                  : "max-w-[200px] truncate"
              }  flex items-start space-x-2 `}>
              {t("chat.reply.message")}
              {item?.main_message && (
                <span className=" mx-1 max-w-[200px] truncate italic">
                  {t("chat.reply.sentBy") +
                    " " +
                    getName(
                      item?.main_message?.type === "message_from_bot"
                        ? item?.main_message?.bot?.name
                        : getUserFromMsg(item?.main_message?.sender_id)?.name ??
                            "",
                      "name"
                    )}
                </span>
              )}
              :
            </Text>

            <span
              role="button"
              onClick={() => searchReplies(item)}
              className="cursor-pointer text-[#1677ff] hover:opacity-70">
              {t("chat.message_type.image")}
            </span>
          </div>
        ) : fileMessageTypes.includes(item?.main_message?.type) ? (
          <div className="flex items-start space-x-1">
            <Text
              type="secondary"
              className={`${
                source === "main"
                  ? "whitespace-nowrap"
                  : "max-w-[200px] truncate"
              }  flex items-start space-x-2 `}>
              {t("chat.reply.message")}
              {item?.main_message && (
                <span className=" mx-1 max-w-[200px] truncate italic">
                  {t("chat.reply.sentBy") +
                    " " +
                    getName(
                      item?.main_message?.type === "message_from_bot"
                        ? item?.main_message?.bot?.name
                        : getUserFromMsg(item?.main_message?.sender_id)?.name ??
                            "",

                      "name"
                    )}
                </span>
              )}
              :
            </Text>
            <span
              role="button"
              onClick={() => searchReplies(item)}
              className="cursor-pointer text-[#1677ff] hover:opacity-70">
              {t("chat.message_type.file")}
            </span>
          </div>
        ) : audioMessageTypes.includes(item?.main_message?.type) ? (
          <div className="flex items-start space-x-1">
            <Text
              type="secondary"
              className={`${
                source === "main"
                  ? "whitespace-nowrap"
                  : "max-w-[200px] truncate"
              }  flex items-start space-x-2 `}>
              {t("chat.reply.message")}
              {item?.main_message && (
                <span className=" mx-1 max-w-[200px] truncate italic">
                  {t("chat.reply.sentBy") +
                    " " +
                    getName(
                      item?.main_message?.type === "message_from_bot"
                        ? item?.main_message?.bot?.name
                        : getUserFromMsg(item?.main_message?.sender_id)?.name ??
                            "",
                      "name"
                    )}
                </span>
              )}
              :
            </Text>
            <span
              role="button"
              onClick={() => searchReplies(item)}
              className="mb-0 cursor-pointer text-[#1677ff] hover:opacity-70">
              {t("chat.message_type.voice")}
            </span>
          </div>
        ) : null}
      </>
    )
  );
}

export default ReplyMessage;
