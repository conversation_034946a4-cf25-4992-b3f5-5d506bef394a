import { useCallback, useEffect, useState } from "react";
import {
  Button,
  Descriptions,
  Divider,
  Popconfirm,
  Skeleton,
  Space,
  Tabs,
  Typography,
} from "antd";
import { fetchDataDetails, getFieldsToCreate } from "../../services/services";
import { toastNotification } from "../../../../components/ToastNotification";
import { useWindowSize } from "../WindowSize";
import { useTranslation } from "react-i18next";
import useActionCall from "../../../voip/helpers/ActionCall";
import { useLocation, useNavigate } from "react-router-dom";
import { Tb360View } from "react-icons/tb";
import {
  checkIfPathOnView360,
  generateUrlToView360,
  humanDate,
} from "pages/voip/helpers/helpersFunc";
import RenderDescriptionDetails from "../RenderDescriptionDetails";
import DisplayAvatar from "pages/voip/components/DisplayAvatar";
import { URL_ENV } from "index";

const ProfileDetails = ({
  familyId,
  contactId,
  contactName,
  numberOfColumns,
  editable = false,
  isUpdate = true,
  setIsUpdate,
  source = "",
  headerHeight = "",
}) => {
  //
  const [t] = useTranslation("common");
  const call = useActionCall();
  const navigate = useNavigate();
  const location = useLocation();
  const windowSize = useWindowSize();

  const [dataToDisplay, setDataToDisplay] = useState([]);
  const [timestamps, setTimestamps] = useState({});
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState(null);

  const getAllFieldsAndData = useCallback(async () => {
    if (!contactId || !isUpdate) return;
    try {
      setLoading(true);
      const fields = await getFieldsToCreate(familyId, "read", null, contactId);
      const fieldsValues = await fetchDataDetails(contactId);
      const labels = fields?.data?.data;

      const values = fieldsValues?.data;
      const result = [];
      labels?.forEach((group) => {
        result.push({
          id: group?.id,
          groupeName: group?.label,
          fields: group?.fields
            ?.filter(
              (el) =>
                el.field_type !== "password" &&
                el.label !== "uuid" &&
                !el.hidden
            )
            .map((el) => ({
              id: el?.id,
              label: el?.alias,
              value: values[el?.id] || "-",
              type: el?.field_type,
            })),
        });
      });
      setActiveTab(result[0]?.id);
      setDataToDisplay(result);
      setTimestamps({
        createdAt: values.created_at,
        createdBy: values.created_by,
        UpdatedAt: values.updated_at,
      });
      setIsUpdate(false);
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification(
          "error",
          "Something Went Wrong (cannot get header), Please Try Again",
          "topRight"
        );
      throw new Error(err?.message ? err.message : err);
    } finally {
      setLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [contactId, familyId, isUpdate]);

  useEffect(() => {
    getAllFieldsAndData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [getAllFieldsAndData]);
  return (
    <>
      <Skeleton className="mt-4" loading={loading} active>
        <Tabs
          tabBarExtraContent={
            <Space size={5}>
              {!!checkIfPathOnView360(location.pathname) &&
                source !== "viewSphere" &&
                source !== "viewSphereRelations" && (
                  <Popconfirm
                    title={t("voip.view360")}
                    description={t("voip.alreadyInView360")}
                    zIndex={9999}
                    onConfirm={() =>
                      navigate(generateUrlToView360(familyId, contactId, "v2"))
                    }
                    okText={t("voip.yes")}
                    cancelText={t("voip.no")}
                    overlayStyle={{ maxWidth: 360 }}
                  >
                    <Button
                      size="small"
                      type="primary"
                      ghost
                      icon={<Tb360View className="h-3 w-3" />}
                    >
                      {t("voip.view360")}
                    </Button>
                  </Popconfirm>
                )}
            </Space>
          }
          activeKey={activeTab}
          onChange={(key) => setActiveTab(Number(key))}
          // style={{ padding: "1rem 0" }}
          items={dataToDisplay?.map((group) => {
            return {
              label: group?.groupeName,
              key: group?.id,
              children: (
                <div
                  className="overflow-content overflow-x-hidden"
                  style={{
                    height:
                      source === "viewSphereRelations"
                        ? `calc(100vh - ${headerHeight + 310 - 47}px)`
                        : source === "viewSphere"
                        ? `calc(100vh - ${headerHeight + 233}px)`
                        : `${windowSize.height - 230}px`,
                  }}
                >
                  <Descriptions
                    colon={false}
                    column={4}
                    // column={numberOfColumns ? numberOfColumns : 3}
                    key={group?.id}
                    contentStyle={{ marginTop: 6, marginBottom: 12 }}
                  >
                    {group?.fields?.map((field) => (
                      <Descriptions.Item
                        key={field?.id}
                        label={
                          <Typography.Paragraph
                            style={{
                              textTransform: "uppercase",
                              marginRight: 12,
                              color: "rgb(100, 116, 139)",
                            }}
                            ellipsis={{
                              rows: 2,
                              expandable: true,
                              defaultExpanded: false,
                              symbol: (
                                <span className="capitalize">
                                  {t("contacts.more")}
                                </span>
                              ),
                            }}
                          >
                            {field.label}
                          </Typography.Paragraph>
                        }
                      >
                        {field?.value === "-" ? (
                          "-"
                        ) : (
                          <div key={field?.id} className="pr-4">
                            <RenderDescriptionDetails
                              type={field.type}
                              value={field.value}
                              t={t}
                              call={call}
                              isProfile={true}
                            />
                          </div>
                        )}
                      </Descriptions.Item>
                    ))}
                  </Descriptions>
                </div>
              ),
            };
          })}
        />
      </Skeleton>
      {loading ? (
        <div className="px-1 py-0.5">
          <Skeleton.Button active size="small" block />
        </div>
      ) : timestamps.createdAt && timestamps.UpdatedAt ? (
        <div className="mr-2 flex  justify-end p-2">
          <Space split={<Divider type="vertical" />}>
            <Space size="small">
              <p className="text-slate-500">{t("contacts.createdBy")}</p>
              <DisplayAvatar
                name={timestamps.createdBy?.label}
                urlImg={
                  !!timestamps.createdBy?.avatar &&
                  `${
                    URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                  }${timestamps.createdBy?.avatar}`
                }
                tooltip={true}
                size={24}
                cursor="help"
              />
            </Space>

            <span className=" text-slate-700	">
              <span className="text-slate-500">
                {t("contacts.createdAt")}:{" "}
              </span>
              {humanDate(timestamps.createdAt, t, "table")}
            </span>
            <span className=" text-slate-700	">
              <span className="text-slate-500">
                {t("contacts.updatedAt")}:{" "}
              </span>
              {humanDate(timestamps.UpdatedAt, t, "table")}
            </span>
          </Space>
        </div>
      ) : null}
    </>
  );
};

export default ProfileDetails;
