import { Popover, Tag } from "antd";
import { useState, useEffect, useRef, useCallback, useMemo } from "react";

export const TagWithHiddenCount = ({ tags, name = "tags" }) => {
  const tagContainerRef = useRef(null);
  const tagRefs = useRef([]);
  const [visibleCount, setVisibleCount] = useState(0);

  // Memoize pour éviter les recalculs inutiles
  const validTags = useMemo(
    () =>
      Array.isArray(tags)
        ? tags.filter((tag) => tag != null && tag !== "")
        : [],
    [tags]
  );

  const visibleTags = useMemo(
    () => validTags.slice(0, visibleCount),
    [validTags, visibleCount]
  );

  const hiddenTags = useMemo(
    () => validTags.slice(visibleCount),
    [validTags, visibleCount]
  );

  // Fonction optimisée pour calculer la largeur d'un tag
  const getTagWidth = useCallback((tagText) => {
    const canvas = document.createElement("canvas");
    const context = canvas.getContext("2d");

    // Style approximatif d'un Tag Ant Design
    context.font = '14px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto';
    const textWidth = context.measureText(tagText).width;

    // Ajouter padding et marges approximatives (32px pour le padding + bordures)
    return textWidth + 32;
  }, []);

  // Fonction pour calculer les tags visibles
  const calculateVisibleTags = useCallback(() => {
    if (!tagContainerRef.current || validTags.length === 0) {
      setVisibleCount(validTags.length);
      return;
    }

    const containerWidth = tagContainerRef.current.offsetWidth;
    let totalWidth = 0;
    let visibleCount = 0;

    // Calculer la largeur du tag de comptage (+X tags)
    const hiddenCountText = `+ ${validTags.length} ${
      name?.toLowerCase() || "items"
    }`;
    const hiddenTagWidth = getTagWidth(hiddenCountText);

    for (let i = 0; i < validTags.length; i++) {
      const tagWidth = getTagWidth(validTags[i]);

      // Si ce n'est pas le dernier tag, on doit réserver l'espace pour le tag de comptage
      const isLastTag = i === validTags.length - 1;
      const requiredWidth = isLastTag ? tagWidth : tagWidth + hiddenTagWidth;

      if (totalWidth + requiredWidth > containerWidth && visibleCount > 0) {
        break;
      }

      totalWidth += tagWidth + 8; // 8px pour l'espacement
      visibleCount++;
    }

    setVisibleCount(visibleCount);
  }, [validTags, name, getTagWidth]);

  // Debounce pour les redimensionnements
  const debouncedCalculate = useCallback(() => {
    let timeoutId;
    return () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(calculateVisibleTags, 100);
    };
  }, [calculateVisibleTags])();

  useEffect(() => {
    calculateVisibleTags();
  }, [calculateVisibleTags]);

  useEffect(() => {
    if (validTags.length === 0) return;

    window.addEventListener("resize", debouncedCalculate);
    return () => window.removeEventListener("resize", debouncedCalculate);
  }, [debouncedCalculate, validTags.length]);

  // Fonction pour générer le texte du compteur
  const getCounterText = useCallback(() => {
    const count = hiddenTags.length;
    if (!name) return `+${count}`;

    const lowerName = name.toLowerCase();
    const singularName = lowerName.endsWith("s")
      ? lowerName.slice(0, -1)
      : lowerName;

    return `+${count} ${count > 1 ? lowerName : singularName}`;
  }, [hiddenTags.length, name]);

  if (validTags.length === 0) {
    return null;
  }

  return (
    <div
      ref={tagContainerRef}
      className="flex flex-wrap gap-1"
      // style={{ minHeight: "32px" }}
    >
      {visibleTags.map((tag, index) => (
        <Tag
          key={`${tag}-${index}`}
          ref={(el) => (tagRefs.current[index] = el)}
        >
          {tag}
        </Tag>
      ))}

      {hiddenTags.length > 0 && (
        <Popover
          content={
            <div className="flex max-w-xs flex-wrap gap-1">
              {hiddenTags.map((tag, index) => (
                <Tag key={`hidden-${tag}-${index}`}>{tag}</Tag>
              ))}
            </div>
          }
          // title={`${hiddenTags.length} ${name?.toLowerCase() || "éléments"}`}
          placement="bottomLeft"
        >
          <Tag style={{ cursor: "pointer" }} className="hover:border-blue-400">
            {getCounterText()}
          </Tag>
        </Popover>
      )}
    </div>
  );
};
