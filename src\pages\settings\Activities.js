import React, { useEffect, useRef } from "react";
import {
  Form,
  Typography,
  Input,
  Button,
  Select,
  Space,
  Tooltip,
  Badge,
} from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { PlusCircleOutlined } from "@ant-design/icons";
import GroupColors from "../../components/GroupColors";
import { toastNotification } from "../../components/ToastNotification";
import Header from "../../components/configurationHelpDesk/Header";
import ChoiceIcons from "../components/ChoiceIcons";
import ColumnColors from "../../components/ColumnColors";
import { allIcons } from "../../components/Icons";
import { colors } from "../../components/Colors";
import { addRowinTableTypeActivity } from "../../new-redux/actions/table.actions/table";
import { useDispatch, useSelector } from "react-redux";
import NewTableDraggable from "../../components/NewTableDraggable";
import { setSearch } from "../../new-redux/actions/menu.actions/menu";
import { generateAxios } from "../../services/axiosInstance";
import LabelTable from "../../components/LabelTable";
import { SubmitKeyPress } from "../../utils/SubmitKeyPress";
import { URL_ENV } from "index";

const Activities = () => {
  const [form] = Form.useForm();
  const [count, setCount] = useState(0);
  const [dataform, setDataForm] = useState({});
  const [currentPage, setCurrentPage] = useState(1);
  const [oldPage, setOldPage] = useState(1);
  const [data, setData] = useState([]);
  const [editingKey, setEditingKey] = useState("");
  const [id, setId] = useState(null);
  const [color, setColor] = useState("");
  const [loading, setLoading] = useState(true);
  const [pageSize, setPageSize] = useState(20);
  const isEditing = (record) => record.key === editingKey;
  const [t] = useTranslation("common");
  const [icons, setIcons] = useState([]);
  const dispatch = useDispatch();
  const { addRowActivity } = useSelector((state) => state.table);
  const { search } = useSelector((state) => state.form);
  const inputRefs = useRef([]);
  const filteredData = data.filter((item) => {
    return item.id
      ? item.label?.toLowerCase().includes(search?.toLowerCase())
      : item;
  });
  useEffect(() => {
    inputRefs.current.forEach((input) => {
      input?.focus();
    });
  }, [id, data.length]);
  // useEffect(() => {
  //   if (editingKey && oldPage !== currentPage) {
  //     setData((prev) => prev.filter((el) => el.id));
  //     setEditingKey("");
  //   }
  // }, [currentPage, editingKey, oldPage]);
  useEffect(() => {
    if (addRowActivity) {
      const ids = data.map((object) => {
        return object.id;
      });
      setColor("");
      setId(null);

      const newData = {
        key: Math.max(...ids) + 1,
        label: `  `,
        color: undefined,
      };

      setData([...data, newData]);
      form.setFieldsValue({
        label: "",
        icons: null,
        color: undefined,
      });
      setEditingKey(Math.max(...ids) + 1);
      setCount(Math.max(...ids) + 1);
    }
  }, [addRowActivity]);
  const onFinishFailed = (values) => {
    console.log(values);
  };

  const handleKeyPress = (event) => {
    SubmitKeyPress(event, form);
  };
  const EditableCell = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    const inputNode =
      inputType === "icons" ? (
        <Select
          placeholder={t("tags.selecticon")}
          showSearch
          style={{
            width: 250,
          }}
          options={allIcons.map((el) =>
            icons &&
            icons.length > 0 &&
            icons.find((icon) => icon === el.value && icon !== record?.icons)
              ? {
                  label: (
                    <Tooltip
                      placement="right"
                      title={t("activities.iconTaked")}
                    >
                      <Space className="px-1">
                        <Typography.Text type="secondary">
                          {el.label}
                        </Typography.Text>
                        <Typography.Text type="secondary">
                          {el.value.replaceAll("Outlined", "")}
                        </Typography.Text>
                      </Space>
                    </Tooltip>
                  ),
                  value: el.value,
                  disabled: true,
                }
              : {
                  label: (
                    <Space className="px-1">
                      <Typography.Text>{el.label}</Typography.Text>
                      <Typography.Text type="secondary">
                        {el.value.replaceAll("Outlined", "")}{" "}
                      </Typography.Text>
                    </Space>
                  ),
                  value: el.value,
                }
          )}
          optionFilterProp="children"
          filterOption={(input, option) =>
            (option?.value?.toLowerCase() ?? "").includes(input?.toLowerCase())
          }
          filterSort={(optionA, optionB) =>
            (optionA?.value ?? "")
              .toLowerCase()
              .localeCompare((optionB?.value ?? "")?.toLowerCase())
          }
          allowClear
        />
      ) : inputType === "radio" ? (
        <Select
          showSearch
          allowClear
          placeholder={t("tags.selectcolor")}
          style={{
            width: 200,
          }}
          options={colors.map((el) => ({
            label: (
              <Space>
                <Badge color={el.value} /> {t(`colors.${el.label}`)}
              </Space>
            ),
            value: el.value,
            label2: el.label,
          }))}
          optionFilterProp="children"
          filterOption={(input, option) => {
            var _a;
            return (
              (_a =
                option === null || option === void 0
                  ? void 0
                  : t(`colors.${option.label2}`)) !== null && _a !== void 0
                ? _a
                : ""
            )
              .toLowerCase()
              .includes(input.toLowerCase());
          }}
          filterSort={(optionA, optionB) =>
            (optionA?.value ?? "")
              .toLowerCase()
              .localeCompare((optionB?.value ?? "").toLowerCase())
          }
          // allowClear
        />
      ) : (
        <Input
          ref={(el) => (inputRefs.current[0] = el)}
          onKeyPress={handleKeyPress}
          placeholder={t("activities.name")}
        />
      );
    return (
      <td {...restProps}>
        {editing ? (
          <Form.Item
            name={dataIndex?.toLowerCase()}
            style={{
              margin: 0,
            }}
            rules={[
              {
                required: dataIndex === "color" ? false : true,
                message: `${title} ${t("table.header.isrequired")}`,
              },
            ]}
          >
            {inputNode}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };
  const edit = (record) => {
    if (record) {
      setEditingKey(record.key);
      setColor(record.color);
      form.setFieldsValue({
        label: record.label,
        icons: record.icons,
        color: record.color,
      });
      setDataForm({
        label: record.label,
        icons: record.icons,
        color: record.color,
      });
      setId(record.id);
    } else {
      form.setFieldsValue({
        label: "",
        icons: null,
        color: undefined,
      });
    }
  };
  const cancel = (record) => {
    setEditingKey("");
    setId(null);
    if (!record.id) {
      setData(data.filter((item) => item.key !== record.key));
    }

    dispatch(addRowinTableTypeActivity(false));
    setColor("");
  };
  const save = async (key) => {
    setLoading(true);
    if (id) {
      try {
        const row = await form.validateFields();
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post(`/task-types/update/${id}`, {
          ...dataform,
          ...row,

          // color,
        });
        setEditingKey("");
        setIcons(res.data.data.icons);
        setData(res.data.data.tasks_type.map((el) => ({ ...el, key: el.id })));
        form.setFieldsValue({
          label: "",
          icons: null,
          color: undefined,
        });
        setDataForm({});
        setLoading(false);
        toastNotification("success", row.label + t("toasts.edit"), "topRight");
      } catch (errInfo) {
        setLoading(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    } else {
      try {
        const row = await form.validateFields();
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post("/task-types", { ...row });
        setEditingKey("");
        setData(res.data.data.tasks_type.map((el) => ({ ...el, key: el.id })));
        setIcons(res.data.data.icons);
        form.setFieldsValue({
          label: "",
          icons: null,
          color: undefined,
        });
        setColor("");
        setDataForm({});

        setLoading(false);
        toastNotification(
          "success",
          row.label + t("toasts.created"),
          "topRight"
        );
      } catch (errInfo) {
        setLoading(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    }
    dispatch(addRowinTableTypeActivity(false));
  };
  useEffect(() => {
    const getActivities = async () => {
      setLoading(true);
      try {
        const {
          data: { data },
        } = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get("/task-types");

        setData(
          data.tasks_type.map((el, i) => ({ ...el, key: el.id, rank: i + 1 }))
        );
        setIcons(data.icons);
        if (data.tasks_type.length > 0) {
          setCount(Math.max(...data.tasks_type.map((el) => el.id)));
        }
        setLoading(false);
      } catch (err) {
        setLoading(false);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    };
    getActivities();
    return () => {
      dispatch(setSearch(""));
    };
  }, []);

  const handleDelete = async (key) => {
    try {
      const res = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).delete(`/channels/${key}`);
      const deleted = data.find((item) => item.key == key);
      const newData = data.filter((item) => item.key != key);
      setData(
        newData.map((el) =>
          el.rank > deleted.rank ? { ...el, rank: el.rank - 1 } : el
        )
      );
      toastNotification(
        "success",
        deleted.label + t("toasts.deleted"),
        "topRight"
      );
    } catch (err) {}
  };
  const handleClick = (event) => {
    event.stopPropagation();
  };
  const columns = [
    {
      title: t("activities.name"),
      dataIndex: "label",
      key: "label",
      width: 300,
      editable: true,
      sorter: (a, b) => a.label.localeCompare(b.label),
      render: (_, record) => {
        return (
          <LabelTable
            record={record}
            editingKey={editingKey}
            edit={edit}
            width={290}
          />
        );
      },
    },

    {
      title: t("activities.icon"),
      dataIndex: "icons",
      key: "icons",
      editable: true,

      render: (_, { icons }) => (
        <div
          className={`${
            data.find((el) => el.id === editingKey)?.default === 1
              ? "absolute top-1/2 -translate-y-1/2 transform"
              : "relative "
          }`}
        >
          <ChoiceIcons icon={icons} fontSize={16} />
        </div>
      ),
    },
    {
      title: t("activities.color"),
      dataIndex: "color",
      key: "color",
      editable:
        data.find((el) => el.id === editingKey)?.default === 1 ? false : true,
      render: (_, { color }) => (
        <div
          className={`${
            data.find((el) => el.id === editingKey)?.default === 1
              ? "absolute top-1/2 -translate-y-1/2 transform"
              : "relative"
          }`}
        >
          {" "}
          <ColumnColors color={color} colors={colors} />{" "}
        </div>
      ),
    },
  ];

  const handleAdd = () => {
    const ids = data.map((object) => {
      return object.id;
    });
    setId(null);

    const newData = {
      key: Math.max(...ids) + 1,
      label: `  `,
      icons: null,
      color: undefined,
      disabled: true,
    };
    setCurrentPage(Math.ceil([...data, newData].length / pageSize));
    setOldPage(Math.ceil([...data, newData].length / pageSize));
    setData([...data, newData]);
    form.setFieldsValue({
      label: "",
      icons: null,
      color: undefined,
    });
    setEditingKey(Math.max(...ids) + 1);
    setCount(Math.max(...ids) + 1);
  };
  const onRow = () => {};

  return (
    <Space direction="vertical" style={{ width: "100%" }}>
      <div style={{ height: "calc(100vh - 490px)" }} className="px-2">
        <div className="mb-2 pt-2">
          <Header
            active={"4"}
            editingKey={editingKey}
            handleAdd={handleAdd}
            btnText={t("activities.createActivity")}
            disabled={editingKey ? true : loading ? true : false}
          />
        </div>
        <NewTableDraggable
          columns={columns}
          setLoading={setLoading}
          isEditing={isEditing}
          data={filteredData}
          setData={setData}
          loading={loading}
          save={save}
          edit={edit}
          EditableCell={EditableCell}
          onFinishFailed={onFinishFailed}
          cancel={cancel}
          form={form}
          apiRank="rank-task-type"
          editingKey={editingKey}
          api="task-types"
          onRow={onRow}
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
          pageSize={pageSize}
          setPageSize={setPageSize}
          setIcons={setIcons}
        />
        <div style={{ width: "0", paddingLeft: "28px" }}>
          <Button
            shape="circle"
            icon={<PlusCircleOutlined />}
            onClick={handleAdd}
            type="link"
            block
            disabled={editingKey ? true : loading ? true : false}
          >
            {t("activities.createActivity")}
          </Button>
        </div>
      </div>
    </Space>
  );
};
export default Activities;
