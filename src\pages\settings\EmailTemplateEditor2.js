import React, { useState, useRef, useEffect, useCallback } from "react";
import { Input, Button, message } from "antd";

// Composants
import EditorToolbar from "./components/EditorToolbar";
import ButtonModal from "./components/ButtonModal";
import HtmlModal from "./components/HtmlModal";
import ImageResizeModal from "./components/ImageResizeModal";

const EmailTemplateEditor2 = () => {
  // Références
  const editorRef = useRef(null);
  const toolbarRef = useRef(null);

  const editorContainerRef = useRef(null);
  const imageRef = useRef(null);
  const savedSelection = useRef(null);

  // États pour l'éditeur
  const [content, setContent] = useState("");
  const [subject, setSubject] = useState("");
  const [fontFamily, setFontFamily] = useState("Arial");
  const [fontSize, setFontSize] = useState("14px");
  const [showPreview, setShowPreview] = useState(false);
  const [selectedColor, setSelectedColor] = useState("#000000");
  const [selectedBgColor, setSelectedBgColor] = useState("#ffffff");
  const [templates, setTemplates] = useState([]);

  // États pour les modals
  const [showButtonModal, setShowButtonModal] = useState(false);
  const [showHtmlModal, setShowHtmlModal] = useState(false);
  const [imageModalVisible, setImageModalVisible] = useState(false);
  const [htmlInput, setHtmlInput] = useState("");

  // États pour les images
  const [imageSrc, setImageSrc] = useState("");
  const [imageSize, setImageSize] = useState({ width: 300, height: 200 });
  const [originalSize, setOriginalSize] = useState({ width: 300, height: 200 });
  const [aspectRatio, setAspectRatio] = useState(1.5);
  const [lockAspectRatio, setLockAspectRatio] = useState(true);
  const [selectedImage, setSelectedImage] = useState(null);

  // États pour les liens
  const [linkPopoverVisible, setLinkPopoverVisible] = useState(false);
  const [selectionPosition, setSelectionPosition] = useState(null);
  const [linkUrl, setLinkUrl] = useState("");
  const [currentSelection, setCurrentSelection] = useState(null);
  const restoreSelection = useCallback(() => {
    if (savedSelection.current) {
      const selection = window.getSelection();
      selection.removeAllRanges();
      selection.addRange(savedSelection.current);
      return true;
    }
    return false;
  }, []);
  // État pour le bouton
  const [buttonConfig, setButtonConfig] = useState({
    text: "",
    url: "",
    bgColor: "#1890ff",
    textColor: "#ffffff",
    padding: "10px 20px",
    borderRadius: "5px",
  });
  // Fonctions pour l'éditeur
  const execCommand = useCallback((command, value = null) => {
    document.execCommand(command, false, value);
    if (editorRef.current) {
      editorRef.current.focus();
    }
  }, []);

  const handleColorChange = useCallback(
    (color) => {
      setSelectedColor(color);
      execCommand("foreColor", color);
    },
    [execCommand]
  );

  const handleEditorMouseUp = () => {
    // Appeler la fonction de sauvegarde de sélection du toolbar
    if (toolbarRef.current && toolbarRef.current.handleEditorMouseUp) {
      toolbarRef.current.handleEditorMouseUp();
    }
  };

  // Fonctions pour les liens
  const handleLinkClick = useCallback(() => {
    const selection = window.getSelection();
    if (selection.toString().trim() === "") {
      message.warning("Veuillez sélectionner du texte avant d'ajouter un lien");
      return;
    }

    // Sauvegarder la sélection
    const range = selection.getRangeAt(0);
    setCurrentSelection({
      range: range.cloneRange(),
      text: selection.toString(),
    });

    // Calculer la position pour le popover
    const rect = range.getBoundingClientRect();
    const editorRect = editorRef.current.getBoundingClientRect();

    setSelectionPosition({
      x: rect.left + rect.width / 2 - editorRect.left,
      y: rect.bottom - editorRect.top,
    });

    setLinkPopoverVisible(true);
  }, []);

  const applyLink = useCallback(() => {
    if (!currentSelection || !currentSelection.range) return;

    let url = linkUrl.trim();
    if (!url) {
      message.warning("Veuillez entrer une URL valide");
      return;
    }

    // Ajouter le préfixe https:// si nécessaire
    if (!url.startsWith("http://") && !url.startsWith("https://")) {
      url = `https://${url}`;
    }

    try {
      // Restaurer la sélection
      const selection = window.getSelection();
      selection.removeAllRanges();
      selection.addRange(currentSelection.range);

      // Créer le lien
      execCommand("createLink", url);

      // Mettre à jour le contenu
      setContent(editorRef.current.innerHTML);

      // Initialiser les gestionnaires d'événements pour les liens
      setTimeout(() => {
        initializeLinkHandlers();
      }, 100);
    } catch (error) {
      console.error("Erreur lors de l'application du lien:", error);
      message.error("Erreur lors de l'application du lien");
    } finally {
      // Réinitialiser et fermer le popover
      setLinkUrl("");
      setLinkPopoverVisible(false);
      setSelectionPosition(null);
      setCurrentSelection(null);
    }
  }, [currentSelection, linkUrl, execCommand]);

  const cancelLink = useCallback(() => {
    setLinkUrl("");
    setLinkPopoverVisible(false);
    setSelectionPosition(null);
    setCurrentSelection(null);
  }, []);

  const initializeLinkHandlers = useCallback(() => {
    if (!editorRef.current) return;

    const links = editorRef.current.querySelectorAll("a");
    links.forEach((link) => {
      link.addEventListener("click", (e) => {
        e.preventDefault();
      });
    });
  }, []);

  // Fonctions pour les images
  const handleImageUpload = useCallback(() => {
    // Sauvegarder la sélection actuelle
    saveSelection();

    const input = document.createElement("input");
    input.type = "file";
    input.accept = "image/*";

    input.onchange = (e) => {
      const file = e.target.files[0];
      if (!file) return;

      if (!file.type.startsWith("image/")) {
        message.error("Veuillez sélectionner un fichier image valide");
        return;
      }

      const reader = new FileReader();
      reader.onload = (event) => {
        const img = new Image();
        img.onload = () => {
          // Définir les dimensions originales et initiales
          const originalWidth = img.width;
          const originalHeight = img.height;

          setOriginalSize({ width: originalWidth, height: originalHeight });

          // Calculer les dimensions initiales (max 500px de large)
          const maxWidth = 500;
          let initialWidth = originalWidth;
          let initialHeight = originalHeight;

          if (initialWidth > maxWidth) {
            const ratio = maxWidth / initialWidth;
            initialWidth = maxWidth;
            initialHeight = originalHeight * ratio;
          }

          setImageSize({ width: initialWidth, height: initialHeight });
          setAspectRatio(originalWidth / originalHeight);

          // Définir l'image source et ouvrir le modal
          setImageSrc(event.target.result);
          setSelectedImage(file);
          setImageModalVisible(true);
        };
        img.src = event.target.result;
      };
      reader.readAsDataURL(file);
    };

    input.click();
  }, []);

  const insertImageFromUrl = useCallback(() => {
    // Sauvegarder la sélection actuelle
    saveSelection();

    const url = prompt("Entrez l'URL de l'image:");
    if (!url || !url.trim()) return;

    const img = new Image();
    img.onload = () => {
      // Définir les dimensions originales et initiales
      const originalWidth = img.width;
      const originalHeight = img.height;

      setOriginalSize({ width: originalWidth, height: originalHeight });

      // Calculer les dimensions initiales (max 500px de large)
      const maxWidth = 500;
      let initialWidth = originalWidth;
      let initialHeight = originalHeight;

      if (initialWidth > maxWidth) {
        const ratio = maxWidth / initialWidth;
        initialWidth = maxWidth;
        initialHeight = originalHeight * ratio;
      }

      setImageSize({ width: initialWidth, height: initialHeight });
      setAspectRatio(originalWidth / originalHeight);

      // Définir l'image source et ouvrir le modal
      setImageSrc(url);
      setImageModalVisible(true);
    };
    img.onerror = () => {
      message.error("Impossible de charger l'image depuis cette URL");
    };
    img.src = url;
  }, []);

  const handleResize = useCallback((e, { size }) => {
    const { width, height } = size;
    setImageSize({ width, height });
  }, []);

  const resetImageSize = useCallback(() => {
    if (lockAspectRatio) {
      // Maintenir le ratio mais revenir aux dimensions originales
      setImageSize({
        width: originalSize.width,
        height: originalSize.height,
      });
    } else {
      // Simplement revenir aux dimensions originales
      setImageSize({
        width: originalSize.width,
        height: originalSize.height,
      });
      // Réinitialiser également le ratio
      setAspectRatio(originalSize.width / originalSize.height);
      setLockAspectRatio(true);
    }
  }, [lockAspectRatio, originalSize]);

  const insertImage = useCallback(() => {
    try {
      // Restaurer la sélection sauvegardée
      if (!restoreSelection()) {
        // Si pas de sélection, insérer à la fin
        editorRef.current.focus();
        const selection = window.getSelection();
        const range = document.createRange();
        range.setStart(editorRef.current, editorRef.current.childNodes.length);
        range.collapse(true);
        selection.removeAllRanges();
        selection.addRange(range);
      }

      // Créer l'élément image
      const imgElement = document.createElement("img");
      imgElement.src = imageSrc;
      imgElement.alt = "Image insérée";
      imgElement.style.width = `${imageSize.width}px`;
      imgElement.style.height = `${imageSize.height}px`;
      imgElement.style.maxWidth = "100%";

      // Insérer l'image
      const selection = window.getSelection();
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        range.deleteContents();
        range.insertNode(imgElement);
        range.setStartAfter(imgElement);
        range.setEndAfter(imgElement);
        selection.removeAllRanges();
        selection.addRange(range);
      }

      // Mettre à jour le contenu
      setContent(editorRef.current.innerHTML);

      // Fermer le modal et nettoyer
      setImageModalVisible(false);
      setImageSrc("");
      setSelectedImage(null);
      savedSelection.current = null;

      message.success("Image insérée avec succès");
    } catch (error) {
      console.error("Erreur lors de l'insertion de l'image:", error);
      message.error("Erreur lors de l'insertion de l'image");
    }
  }, [imageSrc, imageSize, restoreSelection]);

  // Fonctions pour le HTML
  const insertHtml = useCallback(() => {
    if (!htmlInput.trim()) {
      message.warning("Veuillez saisir du code HTML");
      return;
    }

    try {
      // Créer un élément temporaire pour parser le HTML
      const tempDiv = document.createElement("div");
      tempDiv.innerHTML = htmlInput.trim();

      // Essayer de restaurer la sélection sauvegardée
      if (restoreSelection()) {
        const selection = window.getSelection();
        const range = selection.getRangeAt(0);

        // Insérer chaque nœud du HTML
        const fragment = document.createDocumentFragment();
        while (tempDiv.firstChild) {
          fragment.appendChild(tempDiv.firstChild);
        }

        range.deleteContents();
        range.insertNode(fragment);
        range.collapse(false);
        selection.removeAllRanges();
        selection.addRange(range);
      } else {
        // Si aucune sélection, ajouter à la fin de l'éditeur
        while (tempDiv.firstChild) {
          editorRef.current.appendChild(tempDiv.firstChild);
        }
      }

      // Mettre à jour le contenu
      setContent(editorRef.current.innerHTML);

      // Initialiser les gestionnaires d'événements pour les nouveaux liens
      setTimeout(() => {
        initializeLinkHandlers();
      }, 100);

      message.success("HTML inséré avec succès");
    } catch (error) {
      console.error("Erreur lors de l'insertion du HTML:", error);
      message.error("Erreur lors de l'insertion du HTML");
    }

    // Nettoyer et fermer le modal
    setHtmlInput("");
    setShowHtmlModal(false);
    savedSelection.current = null;
  }, [htmlInput, restoreSelection, initializeLinkHandlers]);

  // Fonctions pour les boutons
  const insertButton = useCallback(() => {
    try {
      // Créer l'élément bouton (lien stylisé)
      const button = document.createElement("a");
      button.href = buttonConfig.url;
      button.textContent = buttonConfig.text;
      button.style.cssText = `
        display: inline-block;
        background-color: ${buttonConfig.bgColor};
        color: ${buttonConfig.textColor};
        padding: ${buttonConfig.padding || "10px 20px"};
        text-decoration: none;
        border-radius: ${buttonConfig.borderRadius || "5px"};
        font-weight: bold;
        margin: 10px 0;
      `;
      button.setAttribute("target", "_blank");
      button.setAttribute("rel", "noopener noreferrer");

      // Vérifier si l'éditeur est disponible
      if (!editorRef.current) {
        console.error("Référence de l'éditeur non disponible");
        message.error("Erreur: Éditeur non disponible");
        return;
      }

      // Restaurer la sélection sauvegardée
      if (!restoreSelection()) {
        // Si pas de sélection, insérer à la fin
        editorRef.current.focus();
        const selection = window.getSelection();
        const range = document.createRange();
        range.setStart(editorRef.current, editorRef.current.childNodes.length);
        range.collapse(true);
        selection.removeAllRanges();
        selection.addRange(range);
      }

      // Obtenir la sélection actuelle
      const selection = window.getSelection();

      // Si une sélection existe, insérer le bouton à cet endroit
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);

        // Vérifier si la sélection est dans l'éditeur
        let container = range.commonAncestorContainer;
        while (container && container !== editorRef.current) {
          container = container.parentNode;
        }

        if (!container) {
          // La sélection n'est pas dans l'éditeur, placer le bouton à la fin
          editorRef.current.appendChild(button);
        } else {
          // Insérer le bouton à la position de la sélection
          range.deleteContents();
          range.insertNode(button);

          // Placer le curseur après le bouton
          range.setStartAfter(button);
          range.setEndAfter(button);
          selection.removeAllRanges();
          selection.addRange(range);
        }
      } else {
        // Aucune sélection, ajouter le bouton à la fin de l'éditeur
        editorRef.current.appendChild(button);
      }

      // Mettre à jour le contenu
      setContent(editorRef.current.innerHTML);

      // Afficher un message de succès
      message.success("Bouton inséré avec succès");

      // Fermer le modal
      setShowButtonModal(false);

      // Réinitialiser le focus sur l'éditeur
      editorRef.current.focus();
    } catch (error) {
      console.error("Erreur lors de l'insertion du bouton:", error);
      message.error("Erreur lors de l'insertion du bouton");
    }
  }, [buttonConfig, restoreSelection]);

  // Fonctions pour les variables
  const insertVariable = useCallback((variable) => {
    const selection = window.getSelection();
    if (selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const span = document.createElement("span");
      span.style.backgroundColor = "#e3f2fd";
      span.style.padding = "2px 4px";
      span.style.borderRadius = "3px";
      span.style.color = "#1976d2";
      span.textContent = `{{${variable}}}`;
      range.insertNode(span);
      range.setStartAfter(span);
      selection.removeAllRanges();
      selection.addRange(range);

      // Mettre à jour le contenu
      setContent(editorRef.current.innerHTML);
    }
  }, []);

  // Fonctions pour les templates
  const saveTemplate = useCallback(() => {
    const template = {
      id: Date.now(),
      name: prompt("Nom du template:") || "Template sans nom",
      subject,
      content: editorRef.current?.innerHTML || "",
      createdAt: new Date().toLocaleString(),
    };
    setTemplates([...templates, template]);
    message.success("Template sauvegardé!");
  }, [subject, templates]);

  const loadTemplate = useCallback((template) => {
    setSubject(template.subject);
    if (editorRef.current) {
      editorRef.current.innerHTML = template.content;
      setContent(template.content);
    }
  }, []);

  const exportHTML = useCallback(() => {
    const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${subject}</title>
    <style>
        body { font-family: ${fontFamily}, sans-serif; margin: 0; padding: 20px; }
        .email-container { max-width: 600px; margin: 0 auto; }
    </style>
</head>
<body>
    <div class="email-container">
        <h1>${subject}</h1>
        ${editorRef.current?.innerHTML || ""}
    </div>
</body>
</html>`;

    const blob = new Blob([htmlContent], { type: "text/html" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${subject || "email-template"}.html`;
    a.click();
    URL.revokeObjectURL(url);
  }, [subject, fontFamily]);

  // Fonctions utilitaires
  const saveSelection = useCallback(() => {
    const selection = window.getSelection();
    if (selection.rangeCount > 0) {
      savedSelection.current = selection.getRangeAt(0).cloneRange();
      return true;
    }
    return false;
  }, []);

  const handleEditorClick = useCallback(() => {
    // Fermer les popovers ouverts
    setLinkPopoverVisible(false);
  }, []);

  // Effets
  useEffect(() => {
    // Initialiser les gestionnaires d'événements pour les liens
    initializeLinkHandlers();

    // Charger un template par défaut si l'éditeur est vide
    // if (editorRef.current && !editorRef.current.innerHTML.trim()) {
    //   editorRef.current.innerHTML =
    //     "<p>Commencez à écrire votre email ici...</p>";
    // }

    return () => {
      // Nettoyer les événements lors du démontage
      if (editorRef.current) {
        const links = editorRef.current.querySelectorAll("a");
        links.forEach((link) => {
          link.removeEventListener("click", (e) => {
            e.preventDefault();
          });
        });
      }
    };
  }, [initializeLinkHandlers]);

  // Rendu
  return (
    <div className="mx-auto w-full" style={{ border: "1px solid #ccc" }}>
      {/* Barre d'outils */}
      <div className="p-2" ref={toolbarRef}>
        <EditorToolbar
          execCommand={execCommand}
          handleLinkClick={handleLinkClick}
          handleImageUpload={handleImageUpload}
          insertImageFromUrl={insertImageFromUrl}
          openButtonModal={() => {
            saveSelection();
            setShowButtonModal(true);
          }}
          openHtmlModal={() => {
            saveSelection();
            setShowHtmlModal(true);
          }}
          showPreview={showPreview}
          togglePreview={() => setShowPreview(!showPreview)}
          saveTemplate={saveTemplate}
          exportHTML={exportHTML}
          selectedColor={selectedColor}
          handleColorChange={handleColorChange}
          insertVariable={insertVariable}
          setFontSize={setFontSize}
          fontSize={fontSize}
          setFontFamily={setFontFamily}
          fontFamily={fontFamily}
          editorRef={editorRef}
          setContent={setContent}
        />
      </div>

      {/* Éditeur */}
      <div className="relative" ref={editorContainerRef}>
        {/* <div className="border-b bg-gray-50 p-4">
            <h3 className="font-medium text-gray-800">
              {showPreview ? "Aperçu du template" : "Contenu de l'email"}
            </h3>
          </div> */}
        <div className="">
          {showPreview ? (
            <div className="prose max-w-none">
              <h2 className="mb-4 text-xl font-bold">{subject}</h2>
              <div
                dangerouslySetInnerHTML={{
                  __html: editorRef.current?.innerHTML || "",
                }}
              />
            </div>
          ) : (
            <div
              ref={editorRef}
              contentEditable
              className="min-h-96   p-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Commencez à écrire votre email..."
              onInput={(e) => setContent(e.target.innerHTML)}
              onClick={handleEditorClick}
              onMouseUp={handleEditorMouseUp}
              onKeyUp={handleEditorMouseUp}
              style={{
                // Styles de base pour l'éditeur seulement
                lineHeight: "1.6",
                color: "#333",
                fontFamily: "Arial, sans-serif",
                borderTop: "1px solid #ccc",
              }}
            />
          )}
        </div>
        {linkPopoverVisible && selectionPosition && (
          <div
            className="absolute z-10"
            style={{
              left: `${selectionPosition.x}px`,
              top: `${selectionPosition.y + 10}px`,
            }}
          >
            <div
              className="rounded bg-white p-2 shadow-lg"
              style={{ width: 250 }}
            >
              <Input
                placeholder="Ajouter un lien"
                value={linkUrl}
                onChange={(e) => setLinkUrl(e.target.value)}
                onPressEnter={applyLink}
                autoFocus
              />
              <div className="mt-3 flex justify-end space-x-2">
                <Button size="small" onClick={cancelLink}>
                  Annuler
                </Button>
                <Button type="primary" size="small" onClick={applyLink}>
                  Enregistrer
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
      {/* Modals */}
      <ButtonModal
        isOpen={showButtonModal}
        onClose={() => setShowButtonModal(false)}
        buttonConfig={buttonConfig}
        setButtonConfig={setButtonConfig}
        onInsert={insertButton}
      />

      <HtmlModal
        isOpen={showHtmlModal}
        onClose={() => setShowHtmlModal(false)}
        htmlInput={htmlInput}
        setHtmlInput={setHtmlInput}
        onInsert={insertHtml}
      />

      <ImageResizeModal
        isVisible={imageModalVisible}
        onCancel={() => {
          setImageModalVisible(false);
          setImageSrc("");
          setSelectedImage(null);
          savedSelection.current = null;
        }}
        imageSrc={imageSrc}
        imageSize={imageSize}
        setImageSize={setImageSize}
        originalSize={originalSize}
        aspectRatio={aspectRatio}
        lockAspectRatio={lockAspectRatio}
        setLockAspectRatio={setLockAspectRatio}
        onInsert={insertImage}
        onReset={resetImageSize}
        handleResize={handleResize}
        imageRef={imageRef}
      />

      {/* Popover pour les liens */}
    </div>
  );
};

export default EmailTemplateEditor2;
