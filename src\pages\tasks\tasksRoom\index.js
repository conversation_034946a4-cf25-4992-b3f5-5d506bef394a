/**
 * @name TasksRoom
 *
 * @description `TasksRoom` component is responsible for displaying the chat room.
 *
 * @param {String} elementId The id of element when open the chat in module.
 * @param {Number} canCreateRoom whether to open the chat drawer or not (0: disable,1: enable).
 * @param {Function} setElementDetailToUpdate Reset element id and label after close discussion.
 * @param {Function} setTaskToUpdate Id of the activity when open the drawer in activities.
 * @param {Function} setOpenActivity360 Open activity overview on click on room label.
 * @param {String} roomName Room name from note module.
 * @param {String} chatSource Chat source for note module.
 *
 * @returns {JSX.Element} Chat drawer.
 */

import { useMemo, useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  Drawer,
  Button,
  Space,
  Typography,
  Badge,
  Input,
  Popover,
  List,
} from "antd";
import {
  ArrowLeftOutlined,
  CloseOutlined,
  LoadingOutlined,
  TeamOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { Fi<PERSON>ellOff, FiSearch } from "react-icons/fi";
import { useLocation, useNavigate } from "react-router-dom";

import {
  filterReadMessage,
  setOpenTaskRoomDrawer,
} from "../../../new-redux/actions/tasks.actions/handleTaskRoomDrawer";
import {
  resetStateOtherUser,
  setChatSelectedConversation,
  setChatSelectedParticipants,
  setExternalItem,
} from "../../../new-redux/actions/chat.actions";
import ChatConversations from "../../layouts/chat/conversation/body";
import InputChat from "../../layouts/chat/conversation/input";
import { AvatarChat } from "../../../components/Chat";
import { getName } from "../../layouts/chat/utils/ConversationUtils";
import { ConnectedUsersListItem } from "../../../components/Chat/ConnectedUsersPopover/listItem";
import { URL_ENV, queryClient } from "index";
import useInfoRoomInModules from "pages/layouts/chat/hooks/useInfoRoomInModules";
import { closeDrawerChat } from "new-redux/actions/voip.actions/handleDrawerChat";
import { setRelationId } from "new-redux/actions/tasks.actions/realTime";
import getContactDataAndDispatch from "pages/clients&users/helpers/getContactDataAndDispatch";
import { isGuestConnected } from "utils/role";
import { skl } from "../helpers/displaySkeletonInChat";

const TasksRoom = ({
  elementId,
  canCreateRoom = 1,
  setElementDetailToUpdate = () => {},
  setTaskToUpdate = () => {},
  setOpenActivity360 = () => {},
  roomName,
  chatSource,
  formData, // mailing
  setOpen = () => {}, // mailing
}) => {
  const selectedParticipants = useSelector(
    (state) => state.chat.selectedParticipants
  );
  const relationType = useSelector((state) => state.ChatRealTime?.relationType);
  const sphereRole = useSelector((state) => state?.user?.user?.role);
  const chatRole = useSelector((state) => state?.chat?.currentUser?.role);
  const { openView360InDrawer } = useSelector((state) => state?.vue360);
  const { contactHeaderInfo } = useSelector((state) => state?.contacts);
  const { t } = useTranslation("common");
  const dispatch = useDispatch();
  const location = useLocation();
  const navigate = useNavigate();

  const [searchParticipant, setSearchParticipant] = useState("");
  const [searchOnlineParticipant, setSearchOnlineParticipant] = useState("");
  const [previousRoom, setPreviousRoom] = useState(null);
  const [previousRoomParticipants, setPreviousRoomParticipants] = useState([]);
  const { status, data, isFetched, fetchStatus } = useInfoRoomInModules(
    location?.state?.roomId ?? elementId,
    canCreateRoom,
    roomName,
    chatSource,
    formData // mailing
  );

  const openTaskRoomDrawer = useSelector(
    (state) => state?.TasksRealTime?.openTaskRoomDrawer
  );
  const openTaskDrawer = useSelector(
    (state) => state?.TasksRealTime?.openTaskDrawer
  );
  const chatSelectedConv = useSelector(
    (state) => state?.ChatRealTime?.selectedConversation
  );
  const onlineUsers = useSelector((state) => state?.ChatRealTime?.onlineUser);
  const activitiesMessages = useSelector(
    (state) => state?.TasksRealTime?.activitiesMessages
  );
  const openDrawerChat = useSelector((state) => state?.voip?.openDrawerChat);

  // Handle close chat drawer.
  const onClose = () => {
    dispatch(setOpenTaskRoomDrawer(false));
    dispatch(
      setChatSelectedConversation({
        selectedConversation: null,
      })
    );
    queryClient.removeQueries({
      queryKey: ["INFO_CHAT_MODULE", elementId],
    });
    setPreviousRoom(null);
    dispatch(setExternalItem(null));
    dispatch(setRelationId(null));
    setElementDetailToUpdate({ id: null, label: null });
    setOpen({}); // mailing
  };

  //List of connected users.
  const connectedUser = useMemo(() => {
    let users = 0;
    if (status === "success")
      selectedParticipants?.forEach((element) => {
        if (onlineUsers[element?.uuid] === "online") users++;
      });

    return users;
  }, [selectedParticipants, onlineUsers, status]);

  //On open chat drawer and successfull retrieve of discussion, dispatch store actions
  useEffect(() => {
    if (status === "success" && isFetched) {
      openDrawerChat && dispatch(closeDrawerChat());
      dispatch(
        resetStateOtherUser({
          forced: true,
          keepDrawerOpened: false,
          item: null,
        })
      );
      dispatch(
        setChatSelectedParticipants({
          selectedParticipants: data?.data?.room?.participants ?? [],
        })
      );
      dispatch(
        setChatSelectedConversation({
          selectedConversation: {
            name: data?.data?.room?.name,
            description: data?.data?.room?.description,
            image: data?.data?.room?.image,
            admin_id: data?.data?.room?.admin_id,
            bot: null,
            id: data?.data?.room?._id,
            type: "room",
            source: "module",
            muted_status: false,
            conversationId: data?.data?.room?._id,
            external: false,
          },
        })
      );

      setPreviousRoom({
        name: data?.data?.room?.name,
        description: data?.data?.room?.description,
        image: data?.data?.room?.image,
        admin_id: data?.data?.room?.admin_id,
        bot: null,
        id: data?.data?.room?._id,
        type: "room",
        source: "module",
        mode: "members",
        muted_status: false,
        conversationId: data?.data?.room?._id,
        external: false,
      });
      // setPreviousRoomParticipants(data?.data?.room?.participants ?? []);
      let activatedRoom =
        activitiesMessages &&
        activitiesMessages?.unread_msg_room?.find(
          (room) => room?.room_id === data?.data?.room?._id
        );
      if (activatedRoom) {
        dispatch(
          setExternalItem({
            _id: data?.data?.room?._id,
            total_unread: activatedRoom?.messages_count,
            last_message: { unread: 1 },
            tag_status: false,
          })
        );
        dispatch(filterReadMessage(data?.data?.room?._id));
      }
    }
  }, [status, isFetched]);

  return (
    <Drawer
      title={
        <>
          {/* Drawer header: room avatar + title + list of users (all and connected) */}
          <Space>
            <Badge offset={[-4, 40]} style={{ width: "8px", height: "8px" }}>
              <AvatarChat
                type={chatSelectedConv?.type}
                fontBold="font-semibold"
                hasImage={chatSelectedConv?.image}
                height={12}
                width={12}
                size={52}
                url={
                  chatSelectedConv?.type === "room"
                    ? URL_ENV?.REACT_APP_OAUTH_CHAT_API +
                      process.env.REACT_APP_SUFFIX_CHAT_GROUP_STORAGE +
                      chatSelectedConv?.image
                    : URL_ENV?.REACT_APP_BASE_URL +
                      URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                      chatSelectedConv?.image
                }
                name={getName(chatSelectedConv?.name, "avatar")}
              />
            </Badge>
            <div className="flex flex-col">
              {openView360InDrawer ||
              contactHeaderInfo?.id ||
              isGuestConnected(chatRole, sphereRole) ? (
                getName(chatSelectedConv?.name, "name")
              ) : (
                <Typography.Text
                  className="text-lg font-medium text-[#1677ff] first-letter:capitalize hover:cursor-pointer hover:text-[#69b1ff]"
                  onClick={() => {
                    if (chatSelectedConv?.type === "user") {
                      return;
                    }
                    if (!openTaskDrawer) {
                      onClose();
                      if (["/tasks", "/visio"]?.includes(location?.pathname)) {
                        setTaskToUpdate(location?.state?.roomId ?? elementId);
                        setOpenActivity360(true);
                      } else if (location?.pathname?.includes("notes")) {
                        navigate("/notes", { state: { id: elementId } });
                      } else {
                        getContactDataAndDispatch(
                          relationType,
                          chatSelectedConv?.name,
                          { key: location?.state?.roomId ?? elementId },
                          { key: location?.state?.roomId ?? elementId },
                          dispatch,
                          `/${location?.pathname?.split("/")[1]}`,
                          navigate
                        );
                      }
                    }
                  }}
                  style={{
                    maxWidth: 378,
                    whiteSpace: "nowrap",
                  }}
                  ellipsis={{
                    tooltip: true,
                  }}
                >
                  {getName(chatSelectedConv?.name, "name")}{" "}
                  {chatSelectedConv?.muted_status && (
                    <FiBellOff className="time" style={{ fontSize: "13px" }} />
                  )}
                </Typography.Text>
              )}

              {chatSelectedConv?.id && chatSelectedConv?.type === "room" && (
                <Space size={12}>
                  {chatSelectedConv?.id && (
                    <Popover
                      content={
                        <div className="flex w-80 flex-col space-y-2">
                          <Input
                            size="middle"
                            placeholder={t("chat.searchSide.searchMembers")}
                            prefix={<FiSearch className="text-slate-500" />}
                            value={searchParticipant}
                            onChange={(e) =>
                              setSearchParticipant(
                                e.target.value
                                  .trimStart()
                                  .replace(/\s{1,} /g, " ")
                              )
                            }
                            className="w-full flex-1"
                            allowClear
                            autoFocus={true}
                          />
                          <List
                            className="membersList max-h-80 overflow-auto"
                            dataSource={
                              selectedParticipants &&
                              selectedParticipants?.filter((el) =>
                                getName(el.name, "name")
                                  ?.toLowerCase()
                                  ?.includes(searchParticipant.toLowerCase())
                              )
                            }
                            renderItem={(item) => (
                              <ConnectedUsersListItem
                                item={item}
                                source={"chat"}
                                setPreviousRoomParticipants={
                                  setPreviousRoomParticipants
                                }
                              />
                            )}
                          />
                        </div>
                      }
                      trigger="click"
                    >
                      <Button
                        type="text"
                        className="space-x-1 transition duration-300"
                      >
                        <TeamOutlined className="text-gray-400" />
                        <span className="text-gray-400">
                          {selectedParticipants?.length}
                        </span>
                        <span className="text-gray-400">
                          {t("chat.header.members")}
                        </span>
                      </Button>
                    </Popover>
                  )}
                  {chatSelectedConv?.id && (
                    <Popover
                      content={
                        <div className="flex w-80 flex-col space-y-2">
                          <Input
                            size="middle"
                            placeholder={t("chat.searchSide.searchMembers")}
                            prefix={<FiSearch className="text-slate-500" />}
                            value={searchOnlineParticipant}
                            onChange={(e) =>
                              setSearchOnlineParticipant(
                                e.target.value
                                  .trimStart()
                                  .replace(/\s{1,} /g, " ")
                              )
                            }
                            className="w-full flex-1"
                            allowClear
                            autoFocus={true}
                          />
                          <List
                            className="membersList max-h-80 overflow-auto"
                            dataSource={
                              selectedParticipants &&
                              selectedParticipants.filter(
                                (user) => onlineUsers[user?.uuid] === "online"
                              ) &&
                              selectedParticipants
                                ?.filter(
                                  (user) => onlineUsers[user?.uuid] === "online"
                                )
                                ?.filter((el) =>
                                  getName(el.name, "name")
                                    ?.toLowerCase()
                                    ?.includes(
                                      searchOnlineParticipant.toLowerCase()
                                    )
                                )
                            }
                            renderItem={(item) => (
                              <ConnectedUsersListItem
                                item={item}
                                source="task"
                                setPreviousRoomParticipants={
                                  setPreviousRoomParticipants
                                }
                              />
                            )}
                          />
                        </div>
                      }
                      trigger="click"
                    >
                      <Button
                        className="space-x-1 transition duration-300 hover:cursor-pointer hover:underline"
                        type="text"
                      >
                        <Badge status="success" />
                        <span className="text-gray-400">
                          {" " + connectedUser}
                        </span>
                        <span className="text-gray-400">
                          {t("chat.connected")}
                        </span>
                      </Button>
                    </Popover>
                  )}
                </Space>
              )}
            </div>
          </Space>
        </>
      }
      placement="right"
      width={500}
      onClose={(e) => {
        if (chatSelectedConv?.type === "user") {
          dispatch(
            setChatSelectedConversation({
              selectedConversation: {
                ...previousRoom,
              },
            })
          );
          dispatch(
            setChatSelectedParticipants({
              selectedParticipants: previousRoomParticipants ?? [],
            })
          );
        } else {
          e && e.stopPropagation();
          e && e.preventDefault();
          onClose();
        }
      }}
      closeIcon={
        chatSelectedConv?.type === "user" ? (
          <>
            {status === "loading" && fetchStatus !== "idle" ? (
              <LoadingOutlined />
            ) : (
              <ArrowLeftOutlined />
            )}
          </>
        ) : (
          <CloseOutlined />
        )
      }
      open={openTaskRoomDrawer}
    >
      {/* Discussion body */}
      {status === "loading" ? (
        <>{skl()}</>
      ) : status === "error" ? (
        <div className="relative top-1/3 flex h-auto w-full justify-center">
          <div className="flex flex-col items-center">
            <p className="text-red-500">{t("tasks.loadRoomError")}</p>
          </div>
        </div>
      ) : (
        <div className="flex h-full flex-1 flex-col justify-between overflow-hidden">
          <div className=" flex-1 overflow-hidden pl-4">
            <ChatConversations source="no_chat" />
          </div>

          <InputChat source="no_chat" />
        </div>
      )}
    </Drawer>
  );
};

export default TasksRoom;
