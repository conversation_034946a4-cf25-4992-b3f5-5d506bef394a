tr.drop-over-downward td {
  border-bottom: 2px dashed #1677ff !important;
}

tr.drop-over-upward td {
  border-top: 2px dashed #1677ff !important;
}
.animated-row {
  animation-name: jump;
  animation-duration: 0.5s;
  animation-fill-mode: forwards;
  animation-timing-function: ease-in-out;
  transition: transform 0.2s ease-in-out;

}

@keyframes jump {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0);
  }
}
