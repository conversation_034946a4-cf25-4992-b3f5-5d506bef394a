// import { useEffect, useState, memo, useMemo } from "react";
// import {
//   Button,
//   Card,
//   Progress,
//   Skeleton,
//   Statistic,
//   Tag,
//   Typography,
// } from "antd";
// import { Line, Liquid, TinyArea } from "@ant-design/plots";
// // import {
// //   HiPhoneIncoming,
// //   HiPhoneOutgoing,
// //   HiPhoneMissedCall,
// //   HiPhone,
// // } from "react-icons/hi";

// import {
//   HiPhoneIncoming,
//   HiPhoneOutgoing,
//   HiPhoneMissedCall,
// } from "react-icons/hi";
// import {
//   MdPhoneMissed,
//   MdPersonAddAlt1,
//   MdInfoOutline,
//   MdPhone,
// } from "react-icons/md";
// import { FiSearch } from "react-icons/fi";
// import {
//   HiOutlineBuildingOffice,
//   HiOutlineUsers,
//   HiPhone,
// } from "react-icons/hi2";

// import CountUp from "react-countup";
// //
// import FlipFlopCard from "../FlipFlopCard";
// import { data } from "./data";
// import { useWindowSize } from "../../../clients&users/components/WindowSize";
// import { Pie, G2 } from "@ant-design/plots";
// import { Navigate, useNavigate } from "react-router-dom";
// import { generateAxios } from "../../../../services/axiosInstance";
// import { toastNotification } from "../../../../components/ToastNotification";
// import { useTranslation } from "react-i18next";
// import { HiBarsArrowUp } from "react-icons/hi2";
// import { useSelector } from "react-redux";
// import { useDispatch } from "react-redux";
// import { setCreateForm } from "../../../../new-redux/actions/form.actions/form";
// import { CalendarOutlined, PlusOutlined } from "@ant-design/icons";

// const { Title } = Typography;

// const Voip = ({ stats_call }) => {
//   // const [stats_call, setStatsCall] = useState(true);
//   const [t] = useTranslation("common");
//   const dispatch = useDispatch();
//   const navigate = useNavigate();

//   const windowSize = useWindowSize();
//   //
//   const [isFlipped, setIsFlipped] = useState(false);

//   //   console.log({ data });

//   const handleClick = (event) => {
//     event.preventDefault();
//     setIsFlipped(!isFlipped);
//   };

//   //
//   const formatter = (value) => <CountUp end={value} separator="," />;
//   //
//   const FrontContents = (
//     // <Card
//     //   hoverable
//     //   //   type="inner"
//     //   bodyStyle={{ backgroundColor: "rgba(0, 0, 0, 0.02)" }}
//     //   title="VoIp"
//     //   extra={
//     //     <a href="#" onClick={handleClick}>
//     //       More Details
//     //     </a>
//     //   }
//     // >
//     // <div className="flex h-full w-full items-center  p-4">
//     // <div className="flex flex-col gap-2 p-4">
//     // <div className="grid w-full grid-cols-3 gap-2 p-4">
//     //   <div className="row-span-3 grid grid-cols-1 gap-2">
//     //     <Card bordered={true} className="row-span-2 shadow-md">
//     //       <div className="absolute left-0 top-0 h-1 w-full rounded-tl-lg rounded-tr-lg bg-[#6594F9]"></div>
//     //       <div className="flex w-full flex-col  justify-center space-x-2">
//     //         <Statistic
//     //           title=" Total calls"
//     //           value={stats_call?.total}
//     //           formatter={formatter}
//     //           prefix={<HiPhone style={{ height: "1.2rem" }} />}
//     //           valueStyle={{
//     //             color: "#6594F9",
//     //           }}
//     //         />

//     //         {/* <HiBarsArrowUp/> */}
//     //         <DemoTinyArea />
//     //       </div>
//     //     </Card>
//     //     <Card
//     //       bordered={true}
//     //       className=" row-span-1 flex items-center justify-center bg-[#B6D8A9] text-center  shadow-md"
//     //     >
//     //       <Title>
//     //         <div className="text-center text-gray-100">{total_contact}</div>
//     //         <div className="text-gray-100">Contacts</div>
//     //       </Title>

//     //       <Button
//     //         type="text"
//     //         onClick={() => {
//     //           dispatch(setCreateForm(true));
//     //           navigate("/contacts/contacts");
//     //         }}
//     //         icon={
//     //           <PlusOutlined
//     //             className="text-gray-100 "
//     //             style={{ fontSize: "22px" }}
//     //           />
//     //         }
//     //       ></Button>

//     //       {/* <Statistic
//     //           title=" Contacts"
//     //           value={stats_call?.total}
//     //           formatter={formatter}
//     //           prefix={<HiPhone style={{ height: "1.2rem" }} />}
//     //           valueStyle={{
//     //             color: "#6594F9",
//     //           }}
//     //         /> */}

//     //       {/* <HiBarsArrowUp/> */}
//     //     </Card>
//     //   </div>
//     //   <Card bordered={true} className="shadow-md">
//     //     <div className="absolute left-0 top-0 h-1 w-full rounded-tl-lg rounded-tr-lg bg-[#2563eb]"></div>
//     //     <div className="flex w-full items-center justify-between space-x-2">
//     //       <Statistic
//     //         title="Received calls"
//     //         value={stats_call?.received_call}
//     //         formatter={formatter}
//     //         prefix={<HiPhoneIncoming style={{ height: "1.2rem" }} />}
//     //         valueStyle={{ color: "rgb(37 99 235)" }}
//     //       />

//     //       <DemoLiquid percent={stats_call?.received_call / stats_call?.total} />

//     //       {/* <Progress
//     //         type="circle"
//     //         percent={65}
//     //         className=""
//     //         // format={(percent) => `${percent} Days`}
//     //         size={40}
//     //       /> */}
//     //     </div>
//     //   </Card>
//     //   <Card bordered={true} className="shadow-md">
//     //     <div className="absolute left-0 top-0 h-1 w-full rounded-tl-lg rounded-tr-lg bg-[#6EB36B]"></div>
//     //     <div className="flex w-full items-center justify-between space-x-2">
//     //       <Statistic
//     //         title="Received outgoing call"
//     //         value={stats_call?.received_outgoing_call}
//     //         formatter={formatter}
//     //         prefix={<HiPhoneOutgoing style={{ height: "1.2rem" }} />}
//     //         valueStyle={{
//     //           color: "rgb(22 163 74)",
//     //         }}
//     //       />

//     //       <DemoLiquid
//     //         percent={stats_call?.received_outgoing_call / stats_call?.total}
//     //       />
//     //       {/* <Progress
//     //         type="circle"
//     //         percent={21}
//     //         className=""
//     //         // format={(percent) => `${percent} Days`}
//     //         size={40}
//     //       /> */}
//     //     </div>
//     //   </Card>

//     //   <Card bordered={true} className="shadow-md">
//     //     <div className="absolute left-0 top-0 h-1 w-full rounded-tl-lg rounded-tr-lg bg-[#ef4444]"></div>
//     //     <div className="flex w-full items-center justify-between space-x-2">
//     //       <Statistic
//     //         title="Total Number Of Missed Calls"
//     //         value={stats_call?.missed_call}
//     //         formatter={formatter}
//     //         prefix={
//     //           <HiPhoneMissedCall
//     //             className="missed"
//     //             style={{ height: "1.2rem" }}
//     //           />
//     //         }
//     //         valueStyle={{
//     //           color: "rgb(239 68 68)",
//     //         }}
//     //       />

//     //       <DemoLiquid percent={stats_call?.missed_call / stats_call?.total} />

//     //       {/* <Progress
//     //         type="circle"
//     //         percent={14}
//     //         className=""
//     //         // format={(percent) => `${percent} Days`}
//     //         size={40}
//     //       /> */}
//     //     </div>
//     //   </Card>
//     //   <Card bordered={true} className="shadow-md">
//     //     <div className="absolute left-0 top-0 h-1 w-full rounded-tl-lg rounded-tr-lg bg-[#9ca3af]"></div>
//     //     <div className="flex w-full items-center justify-between space-x-2">
//     //       <Statistic
//     //         title="Missed outgoing calls"
//     //         value={stats_call?.missed_outgoing_call}
//     //         formatter={stats_call?.missed_outgoing_call}
//     //         prefix={
//     //           <div>
//     //             <HiPhoneMissedCall
//     //               className="missed"
//     //               style={{ height: "1.2rem" }}
//     //             />
//     //           </div>
//     //         }
//     //         valueStyle={{
//     //           color: "#9ca3af",
//     //         }}
//     //       />

//     //       <DemoLiquid
//     //         percent={stats_call?.missed_outgoing_call / stats_call?.total}
//     //       />

//     //       {/* <Progress
//     //         type="circle"
//     //         percent={14}
//     //         className=""
//     //         // format={(percent) => `${percent} Days`}
//     //         size={40}
//     //       /> */}
//     //     </div>
//     //   </Card>
//     //   <Card bordered={true} className="shadow-md">
//     //     <div className="absolute left-0 top-0 h-1 w-full rounded-tl-lg rounded-tr-lg bg-slate-500"></div>
//     //     <div className="flex w-full items-center justify-between space-x-2">
//     //       <Statistic
//     //         title="Failed outgoing call"
//     //         value={stats_call?.failed_outgoing_call}
//     //         formatter={formatter}
//     //         prefix={<HiPhoneOutgoing style={{ height: "1.2rem" }} />}
//     //         valueStyle={{
//     //           color: "#64748B",
//     //         }}
//     //       />

//     //       <DemoLiquid
//     //         percent={stats_call?.failed_outgoing_call / stats_call?.total}
//     //       />
//     //       {/* <Progress
//     //         type="circle"
//     //         percent={21}
//     //         className=""
//     //         // format={(percent) => `${percent} Days`}
//     //         size={40}
//     //       /> */}
//     //     </div>
//     //   </Card>
//     //   <Card bordered={true} className="shadow-md">
//     //     <div className="absolute left-0 top-0 h-1 w-full rounded-tl-lg rounded-tr-lg bg-black"></div>
//     //     <div className="flex w-full items-center justify-between space-x-2">
//     //       <Statistic
//     //         title="Failed call"
//     //         value={stats_call?.failed_call}
//     //         formatter={formatter}
//     //         prefix={<HiPhoneOutgoing style={{ height: "1.2rem" }} />}
//     //         valueStyle={{
//     //           color: "black",
//     //         }}
//     //       />

//     //       <DemoLiquid percent={stats_call?.failed_call / stats_call?.total} />
//     //       {/* <Progress
//     //         type="circle"
//     //         percent={21}
//     //         className=""
//     //         // format={(percent) => `${percent} Days`}
//     //         size={40}
//     //       /> */}
//     //     </div>
//     //   </Card>
//     // </div>
//     <Card bordered={true} className="row-span-2 shadow-md">
//       <div className="absolute left-0 top-0 h-1 w-full rounded-tl-lg rounded-tr-lg bg-[#6594F9]"></div>
//       <div className="flex w-full flex-col  justify-center space-x-2">
//         <Statistic
//           title=" Total calls"
//           className="flex items-center justify-between"
//           value={stats_call?.total}
//           formatter={formatter}
//           prefix={<HiPhone style={{ height: "1rem" }} />}
//           valueStyle={{
//             color: "#6594F9",
//           }}
//         />

//         {/* <HiBarsArrowUp/> */}
//         {/* <DemoLine /> */}
//         <div className="pt-4">
//           <Tag
//             className="w- inline-flex items-center"
//             icon={<HiPhoneIncoming style={{ marginRight: "2px" }} />}
//             color="#159e48">
//             Received calls{" "}
//             <span className="ml-2">{stats_call?.received_call || 0}</span>
//           </Tag>
//           <Tag
//             className="w- inline-flex items-center"
//             icon={<HiPhoneOutgoing style={{ marginRight: "2px" }} />}
//             color="#2460E4">
//             Outgoing calls{" "}
//             <span className="ml-2">{stats_call?.received_call || 0}</span>
//           </Tag>
//           <Tag
//             className="w- inline-flex items-center"
//             icon={<MdPhoneMissed style={{ marginRight: "2px" }} />}
//             color="#EB4343">
//             Missed Calls{" "}
//             <span className="ml-2">{stats_call?.missed_call || 0}</span>
//           </Tag>

//           <Tag
//             className="w- inline-flex items-center"
//             icon={<HiPhoneMissedCall style={{ marginRight: "2px" }} />}
//             color="#EB4343">
//             Received calls failed{" "}
//             <span className="ml-2">
//               {stats_call?.failed_outgoing_call || 0}
//             </span>
//           </Tag>
//           <Tag
//             className="w- inline-flex items-center"
//             icon={<HiPhoneMissedCall style={{ marginRight: "2px" }} />}
//             color="#EB4343">
//             outgoing calls failed{" "}
//             <span className="ml-2">
//               {stats_call?.missed_outgoing_call || 0}
//             </span>
//           </Tag>
//           {/* <div className="flex items-center">Received calls <HiPhoneIncoming style={{ height: "1.2rem",paddingLeft:"2px" }} /></div> */}
//         </div>
//       </div>
//     </Card>
//     // </Card>
//   );
//   //

//   //
//   const { registerTheme } = G2;
//   registerTheme("custom-theme", {
//     colors10: ["#159e48", "#2460E4", "#EB4343"],
//   });

//   const config = useMemo(() => {
//     const data = [
//       {
//         type: `${stats_call?.received_call || 0}  ${t(
//           "dashboard.receivedCalls",
//           {
//             plural: stats_call?.received_call > 0 ? "s" : "",
//           }
//         )} `,
//         value: stats_call?.received_call || 0,
//       },
//       {
//         type: `${stats_call?.received_outgoing_call || 0} ${t(
//           "dashboard.outgoingCalls",
//           {
//             plural: stats_call?.received_outgoing_call > 0 ? "s" : "",
//           }
//         )}`,
//         value: stats_call?.received_outgoing_call || 0,
//       },
//       {
//         type: `${stats_call?.missed_call || 0} ${t("dashboard.missedCalls", {
//           plural: stats_call?.missed_call > 0 ? "s" : "",
//         })} `,
//         value: stats_call?.missed_call || 0,
//       },
//     ];

//     return {
//       appendPadding: 10,
//       data,
//       height: 300,
//       angleField: "value",
//       colorField: "type",
//       theme: "custom-theme",
//       radius: 0.75,
//       label: {
//         type: "spider",
//         labelHeight: 28,
//         content: "{name}\n{percentage}",
//       },
//       interactions: [
//         {
//           type: "element-selected",
//         },
//         {
//           type: "element-active",
//         },
//       ],
//     };
//   }, []);

//   //   const DemoPie = () => {
//   //     const G = G2.getEngine('canvas');
//   //     const { registerTheme } = G2;
//   //     registerTheme("custom-theme", {
//   //       colors10: ['#159e48',
//   //       '#2460E4',
//   // "#EB4343",],
//   //     });

//   //     const data = [
//   //       {
//   //         type: "Received calls",
//   //         value: stats_call?.received_call||10,
//   //       },
//   //       {
//   //         type: "Outgoing calls",
//   //         value: stats_call?.received_outgoing_call||20,
//   //       },

//   //       {
//   //         type: "Missed calls",
//   //         value: stats_call?.missed_call||20,
//   //       },
//   //       // {
//   //       //   type: "Received outgoing calls",
//   //       //   value: stats_call?.received_outgoing_call||20,
//   //       // },
//   //       // {
//   //       //   type: "Missed outgoing calls",
//   //       //   value: stats_call?.missed_outgoing_call||20,
//   //       // },
//   //       // {
//   //       //   type: "Failed calls",
//   //       //   value: stats_call?.failed_call||20,
//   //       // },
//   //     ];
//   //     const cfg = {
//   //       appendPadding: 10,

//   //       data,
//   //       angleField: 'value',
//   //       colorField: 'type',
//   //       radius: 0.75,
//   //       height:300,
//   //       legend: false,
//   //       theme: "custom-theme",
//   //       label: {
//   //         type: 'spider',
//   //         labelHeight: 40,
//   //         formatter: (data, mappingData) => {
//   //           const group = new G.Group({});
//   //           group.addShape({
//   //             type: 'circle',
//   //             attrs: {
//   //               x: 0,
//   //               y: 0,
//   //               width: 40,
//   //               height: 50,
//   //               r: 5,
//   //               fill: mappingData.color,
//   //             },
//   //           });
//   //           group.addShape({
//   //             type: 'text',
//   //             attrs: {
//   //               x: 10,
//   //               y: 8,
//   //               text: `${data.type}`,
//   //               fill: mappingData.color,
//   //             },
//   //           });
//   //           group.addShape({
//   //             type: 'text',
//   //             attrs: {
//   //               x: 0,
//   //               y: 25,
//   //               text: `${data.value}个 ${data.percent * 100}%`,
//   //               fill: 'rgba(0, 0, 0, 0.65)',
//   //               fontWeight: 700,
//   //             },
//   //           });
//   //           return group;
//   //         },
//   //       },
//   //       interactions: [
//   //         {
//   //           type: 'element-selected',
//   //         },
//   //         {
//   //           type: 'element-active',
//   //         },
//   //       ],
//   //     };
//   //     const config = cfg;
//   //     return <Pie {...config} />;
//   //   };

//   // const DemoPie = ({ stats_call }) => {
//   //   const { registerTheme } = G2;
//   //   registerTheme("custom-theme", {
//   //     colors10: ['#159e48',
//   //     '#2460E4',
//   //     '#EB4343',],
//   //   });

//   //   const data = [
//   //     {
//   //       type: "Received calls",
//   //       value: stats_call?.received_call||10,
//   //     },
//   //     {
//   //       type: "Outgoing calls",
//   //       value: stats_call?.received_outgoing_call||20,
//   //     },

//   //     {
//   //       type: "Missed calls",
//   //       value: stats_call?.missed_call||20,
//   //     },
//   //     // {
//   //     //   type: "Received outgoing calls",
//   //     //   value: stats_call?.received_outgoing_call||20,
//   //     // },
//   //     // {
//   //     //   type: "Missed outgoing calls",
//   //     //   value: stats_call?.missed_outgoing_call||20,
//   //     // },
//   //     // {
//   //     //   type: "Failed calls",
//   //     //   value: stats_call?.failed_call||20,
//   //     // },
//   //   ];
//   //   const config = {
//   //     appendPadding: 10,
//   //     data,
//   //     angleField: "value",
//   //     colorField: "type",
//   //     radius: 0.8,
//   //     label: {
//   //       type: "inner",
//   //       offset: "-10%",
//   //       content: "{percentage}",
//   //     },
//   //     interactions: [
//   //       {
//   //         type: "element-active",
//   //       },
//   //     ],
//   //     "theme: "custom-theme","
//   //   };
//   //   // return <Pie {...config} className="flex-1 pt-[450px] lg:pt-[300px] " />;
//   //   return <Pie {...config}  />;

//   // };

//   return (
//     <>
//       <div className="  ">
//         {/* <FlipFlopCard
//           isFlipped={false}
//           frontContents={FrontContents}
//           frontCardHeight={100}
//         /> */}
//         {/* {FrontContents} */}
//         <Pie {...config} />
//         {/* <DemoPie stats_call={stats_call} /> */}

//         {/* <div
//             className=" flex flex-col items-center justify-center gap-3 px-4 pt-[500px] lg:flex-row lg:pt-[100px] xl:pt-0"
//             style={{ height: (windowSize?.height - 0) / 3 }}
//           >
//             <FlipFlopCard
//               isFlipped={false}
//               frontContents={BackContents}
//               frontCardHeight={"100%"}
//             />

//             <DemoPie stats_call={stats_call} />
//           </div> */}
//       </div>
//     </>
//   );
// };

// export default memo(Voip);
import DashboardVoip from "components/itemsDashboard/DashboardVoip";
import React from "react";

const Voip = () => {
  return (
    <div>
      <DashboardVoip />
    </div>
  );
};

export default Voip;
