import emojiRegex from "emoji-regex";
import urlRegex from "url-regex";

export const VISIO_URL_REGEX = /room_visio_name=([\w-]+)/g;
export const VISIO_URL_REDIRECT_REGEX =
  /\/visio-home\/[a-f\d]{8}-[a-f\d]{4}-[a-f\d]{4}-[a-f\d]{4}-[a-f\d]{12}$/g;
export const CONFIRM_ACCESS_URL_REGEX =
  /\/confirm-access\?token=([\w-]*\.[\w-]*\.[\w-]*$)/;
export const REDIRECT_TO_CHAT_REGEX =
  /\/chat\?email=([a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/;
export const INVITATION_URL_REGEX =
  /\/invitation-user\/([\w-]*\.[\w-]*\.[\w-]*$)/;

//export const EVENT_MERCURE_NAME_REGEX = /@\w+\//g;
/*
http://localhost:3000/confirm-access?token=d35bf3bec422e5ea251fad0371c8cf77c916503598aac1f9a1a5181a3dca1095&name=givawiw917
*/
export const LINK_REGEX = urlRegex({
  //exact: true,
});
// /(?!eyJ)((?<!\()(?:(?:https?|ftp):\/\/[^\s\/$.?#].[^\s]*|www\.[^\s]+\.[^\s]{2,})|\b^(?!.*@)(?:[a-zA-Z0-9][a-zA-Z0-9-]*\.[a-zA-Z0-9-]+[^\s]{2,})\b)/gim;

// new RegExp(
//   "((?:(http|https|Http|Https|rtsp|Rtsp):\\/\\/(?:(?:[a-zA-Z0-9\\$\\-\\_\\.\\+\\!\\*\\'\\(\\)" +
//     "\\,\\;\\?\\&\\=]|(?:\\%[a-fA-F0-9]{2})){1,64}(?:\\:(?:[a-zA-Z0-9\\$\\-\\_" +
//     "\\.\\+\\!\\*\\'\\(\\)\\,\\;\\?\\&\\=]|(?:\\%[a-fA-F0-9]{2})){1,25})?\\@)?)?" +
//     "((?:(?:[a-zA-Z0-9][a-zA-Z0-9\\-]{0,64}\\.)+" + // named host
//     "(?:" + // plus top level domain
//     "(?:aero|arpa|asia|a[cdefgilmnoqrstuwxz])" +
//     "|(?:biz|b[abdefghijmnorstvwyz])" +
//     "|(?:cat|com|coop|c[acdfghiklmnoruvxyz])" +
//     "|d[ejkmoz]" +
//     "|(?:edu|e[cegrstu])" +
//     "|f[ijkmor]" +
//     "|(?:gov|g[abdefghilmnpqrstuwy])" +
//     "|h[kmnrtu]" +
//     "|(?:info|int|i[delmnoqrst])" +
//     "|(?:jobs|j[emop])" +
//     "|k[eghimnrwyz]" +
//     "|l[abcikrstuvy]" +
//     "|(?:mil|mobi|museum|m[acdghklmnopqrstuvwxyz])" +
//     "|(?:name|net|n[acefgilopruz])" +
//     "|(?:org|om)" +
//     "|(?:pro|p[aefghklmnrstwy])" +
//     "|qa" +
//     "|r[eouw]" +
//     "|s[abcdeghijklmnortuvyz]" +
//     "|(?:tel|travel|t[cdfghjklmnoprtvwz])" +
//     "|u[agkmsyz]" +
//     "|v[aceginu]" +
//     "|w[fs]" +
//     "|y[etu]" +
//     "|z[amw]))" +
//     "|(?:(?:25[0-5]|2[0-4]" + // or ip address
//     "[0-9]|[0-1][0-9]{2}|[1-9][0-9]|[1-9])\\.(?:25[0-5]|2[0-4][0-9]" +
//     "|[0-1][0-9]{2}|[1-9][0-9]|[1-9]|0)\\.(?:25[0-5]|2[0-4][0-9]|[0-1]" +
//     "[0-9]{2}|[1-9][0-9]|[1-9]|0)\\.(?:25[0-5]|2[0-4][0-9]|[0-1][0-9]{2}" +
//     "|[1-9][0-9]|[0-9])))" +
//     "(?:\\:\\d{1,5})?)" + // plus option port number
//     "(\\/(?:(?:[a-zA-Z0-9\\;\\/\\?\\:\\@\\&\\=\\#\\~" + // plus option query params
//     "\\-\\.\\+\\!\\*\\'\\(\\)\\,\\_])|(?:\\%[a-fA-F0-9]{2}))*)?" +
//     "(?:\\b|$)",
//   "gi"
// );

///[(?!eyJ)((?<!\()(?:(?:https?|ftp):\/\/(www\.)?a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/gim;

export const LINK_TO_SENT_REGEX =
  /(?!eyJ)((?<!\()(?:(?:https?|ftp):\/\/[^\s\/$.?#].[^\s]*|www\.[^\s]+\.[^\s]{2,})|\b^(?!.*@)(?:[a-zA-Z0-9][a-zA-Z0-9-]*\.[a-zA-Z0-9-]+[^\s]{2,})\b)/gim;

export const EMAIL_REGEX =
  // old=>/([a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4})/g;
  /(?:^|\s)(?<!https\?:\/\/|rtsp:\/\/)([a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4})/g;
export const HASHTAG_REGEX =
  /(#+(?![a-fA-F0-9]{6}\b)[a-zA-Z0-9(_)\u00C0-\u017F]{1,}(?::[a-zA-Z0-9(_)\u00C0-\u017F]*)?)/gim;
export const PRE_CODE_REGEX = /<pre><code>.*<\/code><\/pre>/s;
export const SPLIT_ITEM_REGEX = /(<\/?p>)|\s+/g;
export const EMOJI_REGEX = emojiRegex();
export const SPECIAL_CHAR_REGEX = /[.*+?^${}()|[\]\\]/g;
export const PHONE_NUMBER_REGEX = /(?:^|\s)(?!\d*[a-zA-Z])?\d{3,15}(?:\s|$)/gm;
//old->   /(?:^|\s)(?!\d*[a-zA-Z])\d{3}[ \-.]?\d{3}[ \-.]?\d{2,8}(?:\s|$)/gm;
export const DATE_FORMAT_REGEX =
  /\b(0[1-9]|[12]\d|3[01])[-\/.](0[1-9]|1[0-2])[-\/.](19\d\d|20\d\d)\b/gm;

// Regex to word happy birthday  in english and french
export const HAPPY__REGEX = /congratulations|bravo|congrats|félicitations.*/gi;
//*happy_birthday|joyeux_anniversaire|
export const KEYWORD_MAILING_REGEX =
  /\/(inbox|sent|drafts|starred|important|archive|trash|spam)\//;
export const UUID_REGEX =
  /[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}/;
