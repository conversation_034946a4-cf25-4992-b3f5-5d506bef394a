	
@media only screen and (max-width: 1400px){
    .markdown-style-show-disc * {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 40px;
      max-height: 16px;
  
    }
  
  }
  
  @media only screen and (min-width: 1401px) and (max-width: 1450px){
    .markdown-style-show-disc * {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 90px;
      max-height: 16px;
  
    }
  
  }
  
  @media only screen and (min-width: 1451px) and (max-width: 1500px){
    .markdown-style-show-disc * {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100px;
      max-height: 16px;
  
    }
  
  }
  
  @media only screen and (min-width: 1501px) and (max-width: 1600px){
    .markdown-style-show-disc * {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 120px;
      max-height: 16px;
  
    }
  
  }
  
  @media only screen and (min-width: 1601px) and (max-width: 1730px){
    .markdown-style-show-disc * {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 140px;
      max-height: 16px;
  
    }
  
  }
  
  @media only screen and (min-width: 1731px){
    .markdown-style-show-disc * {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 170px;
      max-height: 16px;
    }
  
  }
  
  @media only screen and (max-width: 1400px){
    .markdown-style-show-disc {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 40px;
      max-height: 16px;
  
    }
  
  }
  
  @media only screen and (min-width: 1401px) and (max-width: 1450px){
    .markdown-style-show-disc  {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 90px;
      max-height: 16px;
  
    }
  
  }
  
  @media only screen and (min-width: 1451px) and (max-width: 1500px){
    .markdown-style-show-disc  {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100px;
      max-height: 16px;
  
    }
  
  }
  
  @media only screen and (min-width: 1501px) and (max-width: 1600px){
    .markdown-style-show-disc  {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 120px;
      max-height: 16px;
  
    }
  
  }
  
  @media only screen and (min-width: 1601px) and (max-width: 1730px){
    .markdown-style-show-disc  {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 140px;
      max-height: 16px;
  
    }
  
  }
  
  @media only screen and (min-width: 1731px){
    .markdown-style-show-disc {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 170px;
      max-height: 16px;
    }
  
  }
  
  @media only screen and (max-width: 1200px){
    .reply-responsive-class {
      height: 60vh;
    }
  
  }

