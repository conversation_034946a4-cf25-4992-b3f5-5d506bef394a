import { Button } from "antd";
import { Check, ChevronDown } from "lucide-react";

export const BackgroundColorSelector = ({ editor, isOpen, setIsOpen }) => {
  const colors = [
    { name: "Yellow", color: "#ffff00" },
    { name: "<PERSON>", color: "#9333ea" },
    { name: "Red", color: "#ff0000" },
    { name: "Blue", color: "#2563eb" },
    { name: "Green", color: "#00a000" },
    { name: "Orange", color: "#ffa500" },
    { name: "Pink", color: "#ff69b4" },
    { name: "<PERSON>", color: "#d1d5db" },
  ];

  // Check if any color is currently active
  const activeColor = colors.find(({ color }) =>
    editor?.isActive("highlight", { color })
  );

  return (
    <div className="relative h-full">
      <Button
        type="text"
        className="flex h-full items-center gap-1 p-2 text-sm font-medium text-stone-600 hover:bg-stone-100 active:bg-stone-200"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span
          style={{
            backgroundColor: activeColor?.color || "transparent",
            padding: "0 4px",
            borderRadius: "2px",
          }}
        >
          A
        </span>
        <ChevronDown className="h-4 w-4" />
      </Button>

      {isOpen && (
        <section className="animate-in fade-in slide-in-from-top-1 fixed top-full z-[99999] mt-1 flex w-48 flex-col overflow-hidden rounded border border-stone-200 bg-white p-1 shadow-xl">
          {colors.map(({ name, color }, index) => (
            <Button
              type="text"
              key={index}
              onClick={() => {
                editor.chain().focus().toggleHighlight({ color }).run();
                setIsOpen(false);
              }}
              className={`flex items-center justify-between rounded-sm px-2 py-1 text-sm text-stone-600 hover:bg-stone-100 ${
                editor?.isActive("highlight", { color }) ? "text-blue-600" : ""
              }`}
            >
              <div className="flex items-center space-x-2">
                <div
                  className="rounded-sm border border-stone-200 px-1 py-px font-medium"
                  style={{ backgroundColor: color }}
                >
                  A
                </div>
                <span>{name}</span>
              </div>
              {editor?.isActive("highlight", { color }) && (
                <Check className="h-4 w-4" />
              )}
            </Button>
          ))}
        </section>
      )}
    </div>
  );
};
