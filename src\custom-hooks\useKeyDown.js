import { useEffect } from "react";

/**
 * @param {number} keyCode
 * keyCode: code of the key you want to use (e.g. "27:esc","13:Enter")
 * 
 * @param {boolean} withCTRL
 * withCTRL: true if you want to use the CTRL key
 * @param {string} nameEvent
 * nameEvent: event name (keydown, keyup, keypress, etc.)
 * 
 * @param {function} callback
 
 */
function useKeyDown(keyCode, withCTRL, nameEvent, callback) {
  useEffect(() => {
    function handleKeyDown(e) {
      const condition =
        (withCTRL ? e.ctrlKey || e.metaKey : true) &&
        e.keyCode === keyCode &&
        !e.altKey;
      if (condition) {
        callback(e);
        e.preventDefault();
      }
    }

    window.addEventListener(nameEvent, handleKeyDown);

    return () => window.removeEventListener(nameEvent, handleKeyDown);
  }, [keyCode, nameEvent, withCTRL, callback]);
  return null;
}
export default useKeyDown;
