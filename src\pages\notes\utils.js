export const handleNewNoteCreation = () => {
  //function to generate random id
  const generateRandomId = () => {
    return Math.random().toString(36).substr(2, 9);
  };

  return {
    _id: generateRandomId(),
    object: null,
    //content : New note
    content: "<div data-type='rootblock'><h1>New note</h1></div>",
    priority: null,
    status_note: 1,
    module_note: null,
    family_id: null,
    element_id: null,
    label_element: null,
    voice: null,
    shared: 0,
    updated_at: new Date().toISOString(),
    created_at: new Date().toISOString(),
    reminder_date: null,
    permission: 1,
    isNew: true,
  };
};

export const truncate = (str, n = 30) => {
  return str?.length > n ? str.substr(0, n - 1) + "..." : str;
};

export const extractTitle = (htmlString) => {
  const parser = new DOMParser();
  const doc = parser.parseFromString(htmlString, "text/html");

  const ignoreTags = ["IMG", "VIDEO", "TABLE"]; // Tags to ignore

  // Remove ignored elements from the document completely
  ignoreTags.forEach((tag) => {
    const elements = doc.body.querySelectorAll(tag);
    elements.forEach((element) => element.remove());
  });

  const elements = doc.body.querySelectorAll("*");

  // Determine the max length based on screen width
  let maxLength = 22; // Default max length
  // const screenWidth = window.innerWidth;

  // if (screenWidth >= 1200) {
  //   maxLength = 35; // Large screen
  // } else if (screenWidth >= 768) {
  //   maxLength = 30; // Medium screen
  // } else {
  //   maxLength = 20; // Small screen
  // }

  for (let element of elements) {
    const content = element.textContent.trim();
    if (content) {
      return content.length > maxLength
        ? content.slice(0, maxLength) + "..."
        : content; // Return the first non-empty content as the title
    }
  }

  return "Untitled";
};
