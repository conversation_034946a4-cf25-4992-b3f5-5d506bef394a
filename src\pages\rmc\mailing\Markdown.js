import React from "react";

import "./Markdown.css";
import { convertToPlain } from "../../layouts/chat/utils/ConversationUtils";

function MarkDown({ children, type }) {
  return (
    <p
      //    dangerouslySetInnerHTML={createMarkup(
      //   children
      // )}
      className={
        type === "details"
          ? "m-0 max-h-5 truncate text-xs"
          : type === "title"
          ? ""
          : "truncate"
      }
    >
      {convertToPlain(children)}
    </p>
  );
}

export default MarkDown;
