import { Toolt<PERSON>, <PERSON><PERSON>, Badge } from "antd";

const SiderButton = ({
  selectedSider,
  setSelectedSider,
  sideKey,
  icon,
  title,
  badgeCount,
  placement = "bottom",
  showUploadList,
  setShowUploadList = () => {},
}) => {
  return (
    <Tooltip title={title} placement={placement}>
      <Button
        type={selectedSider === sideKey ? "primary" : "text"}
        icon={
          badgeCount ? (
            <Badge
              offset={[4, 2]}
              count={badgeCount}
              color="#faad14"
              size="small"
            >
              {icon}
            </Badge>
          ) : (
            icon
          )
        }
        onClick={() => {
          setSelectedSider(sideKey);
          setShowUploadList(showUploadList);
        }}
        className={`mb-2 flex flex-col items-center justify-center ${
          selectedSider !== sideKey && "bg-gray-50"
        } p-1`}
        style={{ height: "40px", width: sideKey === "files" ? "40px" : "auto" }}
      />
    </Tooltip>
  );
};

export default SiderButton;
