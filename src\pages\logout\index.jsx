import { Spin, Typography } from "antd";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";

function Logout() {
  const { t } = useTranslation("common");
  useEffect(() => {
    const style = document.createElement("style");

    style.innerHTML = `
  .sb-chat .sb-popup-message{
  display: none !important;}
    .sb-chat-btn {
      display: none !important;
    }
    .sb-chat-btn img {
     display: none !important;
    }
  `;
    document.head.appendChild(style);
  }, []);

  return (
    <div className="flex h-[calc(100vh-57px)]  flex-col items-center justify-center">
      <div className="mt-10 flex h-screen w-full items-center  justify-center space-x-2">
        <Spin size="large" />
        <Typography.Title level={4}> {t("chat.loading")} ... </Typography.Title>
      </div>
    </div>
  );
}

export default Logout;
