.mailing-table-view .ant-table-wrapper .ant-table-tbody>tr.ant-table-row:hover>td,
.mailing-table-view .ant-table-wrapper .ant-table-tbody>tr>td.ant-table-cell-row-hover {
  background: transparent !important;
}

.mailing-table-view mark {
  background-color: rgb(253,226,147);
  color: inherit;
}

.mailing-table-view .ant-table-body {
  scroll-behavior: smooth;
}

.mailing-table-view .ant-table-tbody>tr>td {
  vertical-align: top;
}

.mailing-table-view .ant-table-tbody>tr>td.vertical-middle {
  vertical-align: middle;
}


.mailing-table-view .ant-table-body::-webkit-scrollbar {
  width: 12px;
}

.mailing-table-view .ant-table-body::-webkit-scrollbar-thumb {
  -webkit-border-radius: 10px;
  /* border-radius: 10px; */
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
  /* border-left: 2px solid white; */
}

.mailing-table-view .ant-table-body::-webkit-scrollbar-track {
  background-color: white;
  -webkit-border-radius: 10px;
  /* border-radius: 10px; */
  border-radius: 2px;
}

.mailing-table-view .ant-table-tbody>tr>td {
  border-bottom: 1px solid rgb(226 232 240);
}

/* .mailing-table-view .ant-table-body {
  box-shadow: 0 0 50px -12px rgb(0 0 0 / 0.25);
} */