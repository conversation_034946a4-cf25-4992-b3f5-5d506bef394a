import {
  Bad<PERSON>,
  Card,
  Col,
  Collapse,
  Row,
  Skeleton,
  Space,
  Statistic,
  Tag,
} from "antd";
import React, { useMemo } from "react";
import { CaretRightOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { useLocation } from "react-router-dom";
import { XIcon } from "lucide-react";
import ChoiceIcons from "pages/components/ChoiceIcons";
export const TagWithCloseIcon = ({ color, onClose, children, setFilter }) => {
  return (
    <div
      className={`flex items-center rounded-md px-2 py-0.5  text-white`}
      style={{ backgroundColor: color }}
      onClick={() => {}}
    >
      <ChoiceIcons
        icon={children?.icons}
        fontSize={"11px"}
        top={0}
        width={"11px"}
      />
      <span className="mx-1 w-max text-[11px]	"> {children.label}</span>
      <XIcon
        onClick={() =>
          setFilter((prev) => ({
            selected: prev.selected.filter(
              (element) => element !== children.id
            ),
          }))
        }
        size={10}
        className=" cursor-pointer text-white hover:text-gray-100"
      />
    </div>
  );
};
const ListActivities = ({
  setOpenTaskAdvanced,
  initLoading,
  detailsTask,
  setFilter,
  tasksTypes,
  setActiveKey,
  activeKey,
  countTasks,
  tasksHistory,
  tasksToday,
  tasksUpcoming,
  getItems,
  panelStyle,
  listFilter,
  dataSteps,
  headerHeight = "",
  from = "",
}) => {
  const [t] = useTranslation("common");
  const location = useLocation();
  // console.log(
  //   listFilter,
  //   "----",
  //   tasksTypes.filter(
  //     (element) => listFilter?.selected.indexOf(element.id) !== -1
  //   ),
  //   "----"
  // );

  return (
    <Space direction="vertical" size="large" style={{ width: "100%" }}>
      <div
        style={{
          height:
            tasksHistory.length + tasksToday.length + tasksUpcoming.length > 0
              ? from === "viewSphere" || from === "directory"
                ? `calc(100vh -  ${headerHeight + 248}px)`
                : "100%"
              : "auto",
          overflow: "auto",
          marginRight: "-12px",
          paddingRight: "16px",
        }}
      >
        {initLoading ? (
          <Space direction="vertical" size="small" style={{ width: "100%" }}>
            <Skeleton
              avatar
              paragraph={{
                rows: 1,
              }}
            />
            <Skeleton
              avatar
              paragraph={{
                rows: 1,
              }}
            />
            <Skeleton
              avatar
              paragraph={{
                rows: 1,
              }}
            />
            {/* <Skeleton
              avatar
              paragraph={{
                rows: 1,
              }}
            />
            <Skeleton
              avatar
              paragraph={{
                rows: 1,
              }}
            /> */}
          </Space>
        ) : tasksHistory.length === 0 &&
          tasksToday.length === 0 &&
          tasksUpcoming.length === 0 ? null : (
          <Space direction="vertical" style={{ width: "100%" }} size="middle">
            <Collapse
              bordered={false}
              activeKey={activeKey}
              style={{
                background: location.pathname.includes("v3")
                  ? "#F8FAFC"
                  : "white",
              }}
              expandIcon={({ isActive }) => (
                <CaretRightOutlined rotate={isActive ? 90 : 0} />
              )}
              items={getItems(panelStyle)}
              onChange={(e) => setActiveKey(e)}
            />
          </Space>
        )}
      </div>
    </Space>
  );
};

export default ListActivities;
