import React, { useCallback, useState, useEffect } from "react";
import {
  Table,
  Button,
  Space,
  Collapse,
  theme,
  Tag,
  Typography,
  Badge,
  Tooltip,
} from "antd";
import { CaretRightOutlined } from "@ant-design/icons";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import { DndContext, closestCenter } from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { useTranslation } from "react-i18next";
import { MdVideoCameraFront } from "react-icons/md";

import MainService from "services/main.service";
import ChoiceIcons from "pages/components/ChoiceIcons";

const { Text } = Typography;

const SortableRow = ({ children, id }) => {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({
      id,
    });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <tr ref={setNodeRef} style={style} {...attributes} {...listeners}>
      {children}
    </tr>
  );
};

const initialData = [
  {
    key: "1",
    name: "Task 1",
    priority: "High",
    assignee: "John Doe",
    dueDate: "2024-12-05",
  },
  {
    key: "2",
    name: "Task 2",
    priority: "Medium",
    assignee: "Jane Smith",
    dueDate: "2024-12-07",
  },
];

const TasksListView = ({
  lists,
  setLists,
  selectedPipeline,
  pipelines,
  switchViews,
  tasksTypes,
}) => {
  const [data, setData] = useState(initialData);

  const [t] = useTranslation("common");

  const handleDragEnd = ({ active, over }) => {
    if (active.id !== over.id) {
      const oldIndex = data.findIndex((item) => item.key === active.id);
      const newIndex = data.findIndex((item) => item.key === over.id);
      const newData = arrayMove(data, oldIndex, newIndex);
      setData(newData);
    }
  };

  const getTasks = useCallback(
    async (signal) => {
      try {
        if (pipelines && pipelines?.length > 0) {
          const response = await MainService.getKanbanTasks(
            pipelines[0]?.id,
            //payload,
            signal
          );
          setLists(response?.data?.stages);
        }
        /* setLoadStages(true);
        let roles = selectedRoles ? selectedRoles?.toString() : "";
        let payload = {
          type_task: filterTable,
          search:
            search.length >= 3 && switchViews === "Kanban"
              ? DOMPurify.sanitize(search)
              : "",
          roles,
          start: dateFilter
            ? dateFilter[0].toUpperCase()
            : selectFutureActivities === true
            ? dayjs(dayjs().startOf("day")).format(user?.location?.date_format)
            : "",
          end:
            !selectFutureActivities && dateFilter
              ? dateFilter[1].toUpperCase()
              : "",
          family_id: selectedFamily,
          priorities: activePriority,
          logic: filterCondition,
        };
        if (pipelines && pipelines.length > 0) {
          const response = await MainService.getKanbanTasks(
            selectedPipeline === 0
              ? pipelines && pipelines[0]?.id
              : selectedPipeline,
            payload,
            signal
          );
          setColumns(response?.data?.stages);
          setScrollParameter(
            response?.data?.stages.map((stage) => {
              return {
                ...scrollParameter,
                id: stage?.stage_id,
                page: 1,
                lastPage: stage?.last_page,
              };
            })
          );
          setTotal(sumTotal(response?.data?.stages)); 
        }
        getActivitiesError && setGetActivitiesError(false);
        setLoadStages(false);
        setLoadUpdateTaskStage(false); */
      } catch (error) {
        /* setLoadStages(false);
        setGetActivitiesError(true);
        console.log(`Error ${error}`);
        toastNotification("error", t("toasts.somethingWrong")); */
      }
    },
    [pipelines]
  );

  useEffect(() => {
    if (switchViews === "List") {
      getTasks();
    }
  }, [switchViews, getTasks]);

  const { token } = theme.useToken();
  const panelStyle = {
    // marginBottom: 24,
    borderRadius: token.borderRadiusLG,
    border: "none",
  };

  const cols = [
    {
      title: "activityType",
      dataIndex: "activityType",
      key: "name",
      render: (props) => (
        <Tooltip title={props?.label}>
          <>
            <ChoiceIcons fontSize="18px" icon={props?.icons} />
          </>
        </Tooltip>
      ),
    },
    {
      title: "label",
      dataIndex: "label",
      key: "priority",
      render: (props) => {
        return (
          <div className="flex w-[100%] flex-row items-center">
            {/* <Tooltip title={props?.label}> */}
            <Typography.Text
              ellipsis={{
                tooltip: true,
              }}
              className="max-w-[180px] overflow-hidden text-ellipsis whitespace-nowrap text-[16px] text-xs text-[#1677ff] hover:cursor-pointer hover:text-[#69b1ff]"
              /* onClick={() => {
                  if (source === "viewSphere") {
                    setOpenDrawerEditTask(true);
                    setDetailsTask({
                      ...props,
                      upload:
                        props?.upload == 0
                          ? []
                          : Array.isArray(props?.upload)
                          ? props?.upload
                          : props?.files,
                    });
                    setTaskToUpdate(props?.id);
                  } else handleOpenActivityIn360(props);
                }} */
            >
              {props?.label}
            </Typography.Text>
            {/* </Tooltip> */}
            {props?.visio_in_progress && props?.visio_in_progress === 1 ? (
              <Tooltip title={t("tasks.visioInProgress")}>
                <MdVideoCameraFront className="ml-2  animate-pulse text-sm text-red-500" />
              </Tooltip>
            ) : null}
            <div className="ml-auto flex items-center opacity-0 transition duration-200 group-hover:opacity-100">
              {/* <DropdownTask
                source={source === "viewSphere" ? "viewSphere" : "activity"}
                props={props}
                handleDelete={deleteTask}
                editTask={editTask}
                handleOpenActivityIn360={handleOpenActivityIn360}
                openChat={openChat}
              /> */}
            </div>
          </div>
        );
      },
    },
    {
      title: "priority",
      dataIndex: "priority",
      key: "assignee",
    },
    {
      title: "activityId",
      dataIndex: "activityId",
      key: "dueDate",
    },
    {
      title: "startDate",
      key: "startDate",
      /* render: (_, record) => (
        <Space>
          <Button type="link">Edit</Button>
        </Space>
      ), */
    },
    {
      title: "dueDate",
      dataIndex: "dueDate",
      key: "name",
    },
    {
      title: "owner",
      dataIndex: "owner",
      key: "priority",
    },
    {
      title: "creator",
      dataIndex: "creator",
      key: "assignee",
    },
    {
      title: "guests",
      dataIndex: "guests",
      key: "dueDate",
    },
    {
      title: "followers",
      key: "followers",
    },
    {
      title: "moduleElement",
      dataIndex: "moduleElement",
      key: "assignee",
    },
  ];

  const formatDataSource = (dataToFormat) =>
    dataToFormat?.map((item, i) => ({
      key: item?.id,
      label: item?.label,
      activityType:
        tasksTypes &&
        tasksTypes?.find(
          (el) => Number(el?.id) === Number(item?.tasks_type_id)
        ),
      priority: item?.priority,
      activityId: item?.id,
      startDate: `${item?.start_date} ${item?.start_time}`,
      dueDate: `${item?.end_date} ${item?.end_time}`,
      pipeline: item?.pipeline,
      owner: item?.owner_id?.id,
      creator: item?.creator,
      guests: item?.guests,
      followers: item?.followers,
      moduleElement: {
        familyLabel: item?.family_label,
        elementLabel: item?.element_label,
        id: item?.element_id,
        familyId: item?.family_id,
      },
    }));

  const TableDetails = ({ dataTable }) => {
    return (
      <Table
        size="small"
        components={{
          body: {
            wrapper: (props) => <tbody {...props} />,
            row: ({ children, ...restProps }) => {
              const id = restProps["data-row-key"];
              return <SortableRow id={id}>{children}</SortableRow>;
            },
          },
        }}
        columns={cols}
        dataSource={formatDataSource(dataTable)}
        pagination={false}
      />
    );
  };

  return (
    <div>
      <DndContext collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
        <SortableContext
          items={data?.map((item) => item?.key)}
          strategy={verticalListSortingStrategy}
        >
          <Collapse
            ghost={true}
            style={{
              background: token.colorBgContainer,
            }}
            bordered={false}
            expandIcon={({ isActive }) => (
              <CaretRightOutlined rotate={isActive ? 90 : 0} />
            )}
            items={
              lists?.length > 0
                ? lists?.map((el) => ({
                    key: el?.stage_id,
                    label: (
                      <Tag
                        bordered={false}
                        color={el?.stage_color}
                        style={{
                          fontSize: "15px",
                          padding: "5px",
                        }}
                      >
                        {el?.stage_name}
                        <span className="ml-3 text-[12px]">{el?.total}</span>
                        {/* <Badge
                          count={el?.total}
                          showZero
                          color="#000"
                          style={{
                            marginLeft: "10px",
                          }}
                        /> */}
                      </Tag>
                    ),
                    children: <TableDetails dataTable={el?.elements} />,
                    style: panelStyle,
                  }))
                : null
            }
          />
        </SortableContext>
      </DndContext>
    </div>
  );
};

export default TasksListView;
/* import React, { useState } from 'react';
import { Table, Button, Space, Collapse, theme, Tag, Typography } from 'antd';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';

const { Text } = Typography;

const initialData = [
  {
    key: '1',
    name: 'Task 1',
    priority: 'High',
    assignee: 'John Doe',
    dueDate: '2024-12-05',
  },
  {
    key: '2',
    name: 'Task 2',
    priority: 'Medium',
    assignee: 'Jane Smith',
    dueDate: '2024-12-07',
  },
];

const Demo2 = () => {
  const [data, setData] = useState(initialData);

  const handleDragEnd = (result) => {
    if (!result.destination) return;

    const items = Array.from(data);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);
    setData(items);
  };
  const { token } = theme.useToken();
  const panelStyle = {
    marginBottom: 24,
    background: token.colorFillAlter,
    borderRadius: token.borderRadiusLG,
    border: 'none',
  };

  return (
    <div>
      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="table">
          {(provided) => (
            <Collapse 
            style={{
              background: token.colorBgContainer,
            }}
            bordered={false}
            items={[{
              key: '1',
              label: <Tag bordered={false} color="success">This is panel header</Tag>,
              children:
              <Table
              components={{
                body: {
                  wrapper: (props) => (
                    <tbody ref={provided.innerRef} {...provided.droppableProps} {...props} />
                  ),
                  row: (props) => {
                    const { children, ...rest } = props;
                    const index = rest['data-row-key'];
                    return (
                      <Draggable draggableId={String(index)} index={index}>
                        {(dragProvided) => (
                          <tr
                            ref={dragProvided.innerRef}
                            {...dragProvided.draggableProps}
                            {...dragProvided.dragHandleProps}
                          >
                            {children}
                          </tr>
                        )}
                      </Draggable>
                    );
                  },
                },
              }}
              columns={[
                {
                  title: 'Task Name',
                  dataIndex: 'name',
                  key: 'name',
                  render: (text) => (
                    <Text editable={{ onChange: (value) => console.log(value) }}>{text}</Text>
                  ),
                },
                {
                  title: 'Priority',
                  dataIndex: 'priority',
                  key: 'priority',
                  render: (priority) => {
                    const color =
                      priority === 'High' ? 'red' : priority === 'Medium' ? 'orange' : 'green';
                    return <Tag color={color}>{priority}</Tag>;
                  },
                },
                {
                  title: 'Assignee',
                  dataIndex: 'assignee',
                  key: 'assignee',
                },
                {
                  title: 'Due Date',
                  dataIndex: 'dueDate',
                  key: 'dueDate',
                },
                {
                  title: 'Actions',
                  key: 'actions',
                  render: (_, record) => (
                    <Space>
                      <Button type="link">Edit</Button>
                      <Button type="link" danger>
                        Delete
                      </Button>
                    </Space>
                  ),
                },
              ]}
              dataSource={data}
              pagination={false}
            />
            }]} />
          )}
        </Droppable>
      </DragDropContext>
    </div>
  );
};

export default Demo2; */
