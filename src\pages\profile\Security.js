/* eslint-disable jsx-a11y/anchor-is-valid */
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { Button, Form, Input, Tooltip, Typography } from "antd";
import { updatePassword } from "./services";
// import NotifUpdatePwd from "./components/NotifUpdatePwd";
import { toastNotification } from "../../components/ToastNotification";
import { useTranslation } from "react-i18next";
import { InfoCircleTwoTone } from "@ant-design/icons";
import { useDispatch } from "react-redux";
import { logoutOut } from "../../new-redux/actions/user.actions/getUser";
import { useNavigate } from "react-router-dom";

const Security = () => {
  const dispatch = useDispatch();
  const { Text } = Typography;
  const { i18n } = useTranslation("common");
  const currentLanguage = i18n.language;
  const [t] = useTranslation("common");
  const [form] = Form.useForm();

  const {
    user: { email },
  } = useSelector((state) => state.user);

  // console.log({ currentLanguage });
  const navigate = useNavigate();
  const [timer, setTimer] = useState(null);
  const [resendText, setResendText] = useState(t("profile.forgotPwd"));

  const [loading, setLoading] = useState(false);

  const onFinish = async (values) => {
    try {
      setLoading(true);
      const formData = new FormData();
      formData.append("old_password", values?.oldPassword);
      formData.append("new_password", values?.newPassword);
      formData.append("new_password_confirmation", values?.confirmNewPassword);
      const { status } = await updatePassword(formData);
      if (status === 200) {
        dispatch(logoutOut(navigate));
        return;
        // const key = `open${Date.now()}`;

        // const closeNotification = () => {
        //   notification.close(key);
        // };

        // notification.open({
        //   message: t("profile.pwdUpdateSuccess"),
        //   type: "success",
        //   key,
        //   duration: 0,
        //   description: (
        //     <NotifUpdatePwd
        //       password={values?.newPassword}
        //       closeNotification={closeNotification}
        //       t={t}
        //     />
        //   ),
        // });
        // setLoading(false);
      }
    } catch (err) {
      const {
        response: { status },
        message,
      } = err;
      if (status === 422)
        form.setFields([
          {
            name: "oldPassword",
            errors: [t("profile.theOldPwdIncorrect")],
          },
        ]);
      else toastNotification("error", t("toasts.somethingWrong"), "topRight");
      setLoading(false);
      throw new Error(message || err);
    }
  };

  // const handleForgotPassword = async () => {
  //   try {
  //     const formData = new FormData();
  //     formData.append("email", email);
  //     const { status } = await sendForgotPasswordEmail(formData);
  //     if (status === 200) {
  //       setTimer(30);
  //       setResendText(t("profile.resendInXSec", { x: 30 }));
  //       toastNotification(
  //         "success",
  //         <span>
  //           {t("profile.anEmailHasBeenSent")} <strong>{email}</strong>
  //         </span>,
  //         "topRight",
  //         5
  //       );
  //     }
  //   } catch (err) {
  //     toastNotification("error", t("toasts.somethingWrong"), "topRight");

  //     throw new Error(err?.message || err);
  //   }
  // };

  useEffect(() => {
    let timerInterval;
    if (timer > 0) {
      timerInterval = setInterval(() => {
        setTimer((prevTimer) => {
          const newTimer = prevTimer - 1;
          setResendText(t("profile.resendInXSec", { x: newTimer }));
          return newTimer;
        });
      }, 1000);
    } else if (timer === 0) {
      clearInterval(timerInterval);
      setResendText(t("profile.forgotPwd"));
    }

    return () => {
      clearInterval(timerInterval);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [timer]);

  const forgotPwdHint = {
    en: (
      <div className="flex flex-col space-y-2">
        <Text strong>If you have forgotten your old password:</Text>
        <div className="flex flex-col space-x-1.5">
          <Text>
            1. Ensure you have access to your email at <Text mark>{email}</Text>
            .
          </Text>
          <Text>2. Log out of your account.</Text>
          <Text>
            3. Go to the login interface and click on "Forgot Password".
          </Text>
          <Text>
            4. An email with a reset link will be sent to your address. Click on
            it and set your new password.
          </Text>
          <Text>
            5. If you encounter any issues or can't access your email, please
            contact the administration.
          </Text>
        </div>
      </div>
    ),
    fr: (
      <div className="flex flex-col space-y-2">
        <Text strong>Si vous avez oublié votre ancien mot de passe:</Text>
        <div className="flex flex-col space-x-1.5">
          <Text>
            1. Assurez-vous d'avoir accès à votre boîte email à{" "}
            <Text mark>{email}</Text>.
          </Text>
          <Text>2. Déconnectez-vous de votre compte.</Text>
          <Text>
            3. Rendez-vous sur l'interface de connexion et cliquez sur "Mot de
            passe oublié".
          </Text>
          <Text>
            4. Un email avec un lien de réinitialisation sera envoyé à votre
            adresse. Cliquez dessus et définissez votre nouveau mot de passe.
          </Text>
          <Text>
            5. Si vous rencontrez des problèmes ou si vous ne pouvez pas accéder
            à votre boîte email, veuillez contacter l'administration.
          </Text>
        </div>
      </div>
    ),
  };

  return (
    <div
      style={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        height: "50%",
      }}
    >
      <div
        style={{
          width: "25rem",
          position: "relative",
        }}
      >
        <h2>{t("profile.editPwd")}</h2>
        <Form
          layout="vertical"
          form={form}
          onFinish={onFinish}
          autoComplete="off"
        >
          <Form.Item
            label={
              <div className="flex w-96 justify-between">
                <span>{t("profile.oldPwd")}</span>
                <span>
                  ({resendText}{" "}
                  <Tooltip
                    // overlayClassName="tooltip-content"
                    color="#f9f9f9"
                    title={
                      currentLanguage === "en"
                        ? forgotPwdHint?.en
                        : forgotPwdHint.fr
                    }
                    // overlay={
                    //   <div className="tooltip-content">
                    //     {currentLanguage === "en"
                    //       ? forgotPwdHint?.en
                    //       : forgotPwdHint.fr}
                    //   </div>
                    // }
                    overlayStyle={{ width: "30rem" }}
                  >
                    <InfoCircleTwoTone className="cursor-help" />
                  </Tooltip>
                  )
                </span>
              </div>
              // <span>
              //   {t("profile.oldPwd")}{" "}
              //   {timer ? (
              //     <span
              //       style={{
              //         color: "grey",
              //         cursor: "not-allowed",
              //       }}
              //     >
              //       ({resendText})
              //     </span>
              //   ) : (
              //     <Tooltip title={t("profile.msgForgotPwd")}>
              //       <a
              //         onClick={handleForgotPassword}
              //         style={{
              //           // color: "blue",
              //           textDecoration: "underline",
              //           cursor: "pointer",
              //         }}
              //       >
              //         ({resendText})
              //       </a>
              //     </Tooltip>
              //   )}
              // </span>
            }
            name="oldPassword"
            rules={[
              {
                required: true,
                message: "This field is required",
              },
            ]}
          >
            <Input.Password autoComplete="current-password" />
          </Form.Item>

          <Form.Item
            label={t("profile.newPwd")}
            name="newPassword"
            rules={[
              {
                required: true,
                message: t("profile.fieldRequired"),
              },
              {
                pattern: new RegExp(".{8,}"),
                message: `Minimum eight (8) characters!`,
              },
            ]}
          >
            <Input.Password autoComplete="new-password" />
          </Form.Item>
          <Form.Item
            label={t("profile.confirmNewPwd")}
            name="confirmNewPassword"
            rules={[
              {
                required: true,
                message: t("profile.fieldRequired"),
              },
              {
                pattern: new RegExp(".{8,}"),
                message: t("profile.min8chart"),
              },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue("newPassword") === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error(t("profile.passwordMatch")));
                },
              }),
            ]}
          >
            <Input.Password autoComplete="new-password" />
          </Form.Item>
        </Form>
        <div className="mt-4 flex justify-end space-x-2">
          <Button onClick={() => form.resetFields()} disabled={loading}>
            {t("tags.reset")}
          </Button>
          <Button
            type="primary"
            onClick={() => form.submit()}
            loading={loading}
          >
            {t("profile.confirm")}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Security;
