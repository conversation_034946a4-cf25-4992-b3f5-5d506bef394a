import {
  UPDATE_ALIAS_SUCCESS,
  UPDATE_ALIAS_ERROR,
  UPDATE_ALIAS_LOADING,
} from "../../constants";
import MainService from "../../../services/main.service";

export const updateAlias = (payload) => async (dispatch) => {
  try {
    dispatch({ type: UPDATE_ALIAS_LOADING });
    const response = await MainService.updateFieldAlias(payload);
    dispatch({
      type: UPDATE_ALIAS_SUCCESS,
      payload: {
        response: response?.data,
        updatedAliasFieldId: payload?.fieldId,
      },
    });
  } catch (error) {
    if (error?.status !== 401) {
      dispatch({
        type: UPDATE_ALIAS_ERROR,
        payload: error,
      });
    }
  }
};
