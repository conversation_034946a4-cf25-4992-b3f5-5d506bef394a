import { <PERSON><PERSON>, <PERSON><PERSON>, Dropdown, Tooltip } from "antd";
import { toastNotification } from "components/ToastNotification";
import { setRefreshKpi } from "new-redux/actions/mail.actions";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import MainService from "services/main.service";
import "./mailing.css";
const StateEmail = ({
  idEmail,
  usedAccount,
  state,
  detailsMail,
  setDetailsMail,
  getMails,
  type,
  disabled,
}) => {
  const [t] = useTranslation("common");

  const dispatch = useDispatch();

  const UpdateState = async (state) => {
    var formData = new FormData();
    formData.append("emailId", idEmail);
    formData.append("accountId", usedAccount?.value);
    formData.append("state", state);
    for (let i = 0; i < usedAccount.departmentId.length; i++) {
      formData.append("departement_id[]", usedAccount.departmentId[i]);
    }
    try {
      const response = await MainService.updateStateEmail(formData);
      if (response.status === 200) {
        toastNotification(
          "success",
          t("mailing.modifiedSuccess"),
          "topRight",
          3
        );
        if (type === "inbox") {
          getMails();
          dispatch(setRefreshKpi(true));
        } else {
          setDetailsMail((p) => {
            let detail = Object.assign({}, p);
            detail.data[detail?.data?.length - 1].state =
              response.data.data.state;
            return detail;
          });
        }
      }
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <>
      {state ? (
        <Dropdown
          disabled={disabled}
          menu={{
            items: [
              {
                label: (
                  <span className="dropdown-title">{t("helpDesk.status")}</span>
                ),
                key: "title",
                type: "group", // or use `disabled: true`
                disabled: true,
              },
              {
                label: t("mailing.new"),
                key: "1",
                icon: (
                  <button className="flex h-[13px] w-[13px] cursor-pointer items-center justify-center rounded-full border-none bg-[#9ea5aa]"></button>
                ),
                onClick: (e) => {
                  UpdateState("new");
                },
              },
              {
                label: t("mailing.inProgress"),
                key: "2",
                icon: (
                  <button className="flex h-[13px] w-[13px] cursor-pointer items-center justify-center rounded-full border-none bg-[#49a7e9]"></button>
                ),
                onClick: (e) => {
                  UpdateState("in-progress");
                },
              },
              {
                label: t("mailing.processed"),
                key: "3",
                icon: (
                  <button className="flex h-[13px] w-[13px] cursor-pointer items-center justify-center rounded-full border-none bg-[#a149e9]"></button>
                ),
                onClick: (e) => {
                  UpdateState("processed");
                },
              },
              {
                label: t("mailing.closed"),
                key: "4",
                icon: (
                  <button className="flex h-[13px] w-[13px] cursor-pointer items-center justify-center rounded-full border-none bg-[#3cca5d]"></button>
                ),
                onClick: (e) => {
                  UpdateState("closed");
                },
              },
            ],
          }}
          trigger={["click"]}
        >
          <Tooltip
            title={
              <div className="text-center">
                <p className="text-[#d3d3d3]">Email</p>
                <p>
                  {state === "new"
                    ? t("mailing.new")
                    : state === "in-progress"
                    ? t("mailing.inProgress")
                    : state === "processed"
                    ? t("mailing.processed")
                    : t("mailing.closed")}
                </p>
              </div>
            }
            overlayStyle={{ width: "90px" }}
          >
            <Button
              shape="circle"
              type="text"
              onClick={(e) => {
                e.stopPropagation();
              }}
              disabled={disabled}
            >
              <div
                className={`ml-[11.4px] h-[8px] w-[8px] cursor-pointer rounded-full  ring-1 ring-offset-2
              ${
                state === "new"
                  ? `ring-[#9ea5aa]`
                  : state === "in-progress"
                  ? `ring-[#49a7e9]`
                  : state === "processed"
                  ? `ring-[#a149e9]`
                  : `ring-[#3cca5d]`
              }
          ${
            state === "new"
              ? `bg-[#9ea5aa]`
              : state === "in-progress"
              ? `bg-[#49a7e9]`
              : state === "processed"
              ? `bg-[#a149e9]`
              : `bg-[#3cca5d]`
          }`}
              ></div>
            </Button>
          </Tooltip>
        </Dropdown>
      ) : null}
    </>
  );
};

export default StateEmail;
