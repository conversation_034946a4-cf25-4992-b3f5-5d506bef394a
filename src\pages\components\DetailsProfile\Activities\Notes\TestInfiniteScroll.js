import React, { useState, useEffect } from "react";

function TestInfiniteScroll() {
  const [notes, setNotes] = useState([
    { _id: 1, content: "Note 1" },
    { _id: 2, content: "Note 2" },
    { _id: 3, content: "Note 3" },
    { _id: 4, content: "Note 4" },
    { _id: 5, content: "Note 5" },
    { _id: 6, content: "Note 6" },
    { _id: 7, content: "Note 7" },
    { _id: 8, content: "Note 8" },
    { _id: 9, content: "Note 9" },
    { _id: 10, content: "Note 10" },
    //more
    { _id: 11, content: "Note 11" },
    { _id: 12, content: "Note 12" },
    { _id: 13, content: "Note 13" },
    { _id: 14, content: "Note 14" },
    { _id: 15, content: "Note 15" },
    { _id: 16, content: "Note 16" },
    { _id: 17, content: "Note 17" },
    { _id: 18, content: "Note 18" },
    { _id: 19, content: "Note 19" },
    { _id: 20, content: "Note 20" },
    //more
    { _id: 21, content: "Note 21" },
    { _id: 22, content: "Note 22" },
    { _id: 23, content: "Note 23" },
    { _id: 24, content: "Note 24" },
    { _id: 25, content: "Note 25" },
    { _id: 26, content: "Note 26" },
    { _id: 27, content: "Note 27" },
    { _id: 28, content: "Note 28" },
    { _id: 29, content: "Note 29" },
    { _id: 30, content: "Note 30" },
    //more
    { _id: 31, content: "Note 31" },
    { _id: 32, content: "Note 32" },
    { _id: 33, content: "Note 33" },
    { _id: 34, content: "Note 34" },
    { _id: 35, content: "Note 35" },
    { _id: 36, content: "Note 36" },
    { _id: 37, content: "Note 37" },
    { _id: 38, content: "Note 38" },
    { _id: 39, content: "Note 39" },
    { _id: 40, content: "Note 40" },
  ]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Simulated fetch function to load more data
    const fetchMoreData = () => {
      setLoading(true);
      // Simulating an API call delay with setTimeout
      setTimeout(() => {
        // Mock data - add more items to notes
        const newNotes = Array.from({ length: 10 }, (_, index) => ({
          _id: index + 1,
          content: `Note ${notes.length + index + 1}`,
        }));
        setNotes([...notes, ...newNotes]);
        setLoading(false);
      }, 1000); // Simulated delay of 1 second
    };

    const handleScroll = () => {
      const { scrollTop, clientHeight, scrollHeight } =
        document.documentElement;

      // Check if user has scrolled to the bottom
      if (scrollTop + clientHeight >= scrollHeight - 20) {
        console.Console("Triggered");
        fetchMoreData();
      }
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [notes]); // Add dependencies as needed

  return (
    <div style={{ height: "100vh", overflowY: "scroll" }}>
      {notes.map((note) => (
        <div
          key={note._id}
          style={{ padding: "20px", borderBottom: "1px solid #ccc" }}
        >
          {note.content}
        </div>
      ))}
      {loading && (
        <div style={{ textAlign: "center", padding: "10px" }}>Loading...</div>
      )}
    </div>
  );
}

export default TestInfiniteScroll;
