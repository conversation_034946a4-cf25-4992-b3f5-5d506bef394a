:root {
  --ombre: 0 0 10px rgba(0, 0, 0, 0.5);
  --bordure-radius: 15px;
  --largeur: calc(100%-36Opx);
  --hauteur: 90vh;
  --hauteur2: 35vh;
  --largeur2: 35vh;
  --transition: 0.4s;
}
iframe {
  width: 100%;
  height: 100%;
  border-bottom-left-radius: var(--bordure-radius);
  border-bottom-right-radius: var(--bordure-radius);
}

.visio-box {
  box-shadow: var(--ombre);
  border-radius: var(--bordure-radius);
  width: var(--largeur);
  height: var(--hauteur);
  transition: right var(--transition), bottom var(--transition);
}

.minimized {
  position: fixed;
  transition: all 0.4 ease-in-out;
  height: var(--hauteur2);
  width: var(--largeur2);
  z-index: 2000;
  right: 1%;
}

.phones-select .ant-select-selection-item {
  color: #6b7280;
}
