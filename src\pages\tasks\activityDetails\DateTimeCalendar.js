import { useEffect } from "react";
import { DatePicker, Form, TimePicker } from "antd";
import { useSelector } from "react-redux";
import dayjs from "dayjs";

const DateTimeCalendar = ({
  source,
  date,
  time,
  status,
  readOnly,
  dispatchUpdateDate,
  beginningDate,
  form,
}) => {
  const currentUser = useSelector((state) => state?.user);

  const disableHours = () => {
    const startingHour = Number(beginningDate.split(" ")[1].split(":")[0]);
    const hours = [];
    for (let i = 0; i < startingHour; i++) {
      hours.push(i);
    }
    return hours;
  };

  const disableMinutes = (timeValue) => {
    let curr = dayjs(
      timeValue,
      `${currentUser?.user?.location?.date_format} ${currentUser?.user?.location?.time_format}`
    ).hour();
    const startingMinute = Number(time.split(":")[1]); // Extract starting minute
    const startingDefaultHour = Number(time.split(":")[0]); // Extract starting default hour
    const minutes = [];
    if (curr === startingDefaultHour) {
      for (let i = 0; i < startingMinute; i++) {
        minutes.push(i);
      }
    }
    return minutes;
  };

  const handleStartDateChange = (type, newDateValue) => {
    let newStartDate = dayjs(
      newDateValue,
      currentUser?.user?.location?.date_format
    );
    let endDate = dayjs(date, currentUser?.user?.location?.date_format);
    if (type === "start" && newStartDate.isAfter(endDate)) {
      form.setFieldsValue({
        end_date: newStartDate,
      });
      dispatchUpdateDate("end", newDateValue, "date");
    }
    dispatchUpdateDate(type, newDateValue, "date");
  };

  const handleDisableEndDate = (dateValue) => {
    if (source === "end") {
      return (
        dayjs(dateValue, currentUser?.user?.location?.date_format) <
        dayjs(
          form.getFieldValue("start_date"),
          currentUser?.user?.location?.date_format
        )
      );
    }
  };

  const handleChangeTime = async (newTimeValue) => {
    await dispatchUpdateDate(
      source,
      dayjs(newTimeValue).format(currentUser?.user?.location?.time_format),
      "time"
    );
  };

  const handleDisableEndTime = (timeValue) => {
    if (
      source === "end" &&
      beginningDate &&
      dayjs(date, currentUser?.user?.location?.date_format)
        .startOf("day")
        .isSame(
          dayjs(
            beginningDate.split(" ")[0],
            currentUser?.user?.location?.date_format
          ).startOf("day"),
          "day"
        )
    ) {
      return {
        disabledHours: () => disableHours(),
        disabledMinutes: () => disableMinutes(timeValue),
      };
    }

    // If conditions aren't met or `beginningDate` is undefined,
    // return an empty object to prevent errors:
    return {};
  };

  useEffect(() => {
    form.setFieldsValue({
      [`${source}_date`]: date
        ? dayjs(date, currentUser?.user?.location?.date_format)
        : undefined,
      [`${source}_time`]: time
        ? dayjs(time, currentUser?.user?.location?.time_format)
        : undefined,
    });
  }, [form, date, time]);

  return (
    <>
      <Form.Item name={`${source}_date`} noStyle>
        <DatePicker
          disabled={readOnly}
          format={currentUser?.user?.location?.date_format}
          className={`${
            status && "overdue-task"
          } activity-details mr-[10px] bg-[#0000000a]`}
          onChange={(newDateValue) => {
            handleStartDateChange(
              source,
              dayjs(newDateValue).format(
                currentUser?.user?.location?.date_format
              )
            );
          }}
          disabledDate={handleDisableEndDate}
          variant="borderless"
          allowClear={false}
        />
      </Form.Item>
      <Form.Item name={`${source}_time`} noStyle>
        <TimePicker
          minuteStep={15}
          disabled={readOnly}
          format={currentUser?.user?.location?.time_format}
          className={`${
            status && "overdue-task"
          } activity-details bg-[#0000000a]`}
          onChange={handleChangeTime}
          disabledTime={handleDisableEndTime}
          variant="borderless"
          allowClear={false}
        />
      </Form.Item>
    </>
  );
};

export default DateTimeCalendar;
