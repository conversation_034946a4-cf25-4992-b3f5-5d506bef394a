import React, {
  Suspense,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from "react";
import {
  CheckCircleOutlined,
  CopyOutlined,
  LinkOutlined,
  MailOutlined,
  ShareAltOutlined,
  VideoCameraOutlined,
  ArrowLeftOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import {
  Button,
  Divider,
  Dropdown,
  Select,
  Spin,
  Tooltip,
  Typography,
  message,
} from "antd";
import {
  setVisoParams,
  toggleVisio,
  updateVisioName,
} from "new-redux/actions/visio.actions/createVisio";
import { useDispatch } from "react-redux";
import {
  createVisio,
  setKpiDateVisio,
  setKpiVisio,
  setListMeet,
  setNow,
  updateLabelinListVisio,
} from "new-redux/actions/visio.actions/visio";
import { useLocation, useParams } from "react-router-dom";
import { URL_ENV } from "index";
import {
  cancelTask360,
  setTask360,
} from "new-redux/actions/chat.actions/Input";
import { Loader, ModalConfirm } from "components/Chat";
import { moment_timezone } from "App";
import MainService from "services/main.service";
import { displayStatKey } from "pages/tasks/KpiGrid";
import { convertToPlain } from "pages/layouts/chat/utils/ConversationUtils";
import { setDataConversation } from "new-redux/actions/chat.actions";
import { toastNotification } from "components/ToastNotification";
import { setVisioEventId } from "new-redux/actions/chat.actions/realTime";
import { useNavigate } from "react-router-dom";
import { setEmailFields } from "pages/rmc/mailing/main-components/email-composer-modal/utils";

const state = {
  default: "default",
  error: "error",
};
let visioName = "";

function ShareVisio({ open, onClose, guest = "", setTasksToday = () => {} }) {
  const { t } = useTranslation("common");
  const params = useParams();
  const navigate = useNavigate();
  const selectedConversation = useSelector(
    (state) => state.ChatRealTime.selectedConversation
  );
  const { dataConversation, selectedParticipants } = useSelector(
    (state) => state.chat
  );
  const visioEventId = useSelector((state) => state.ChatRealTime.visioEventId);
  const { openView360InDrawer, contactInfoFromDrawer } = useSelector(
    (state) => state?.vue360
  );

  const { id } =
    Object.keys(params).length > 0
      ? params
      : {
          id: selectedConversation?.uuid,
        };
  const dispatch = useDispatch();
  const contactInfo = useSelector(
    (state) => state?.contacts?.contactHeaderInfo
  );
  const { user } = useSelector((state) => state.user);
  const { isOpen } = useSelector((state) => state.visio);
  const { id_visio } = useSelector((state) => state.visio?.visioParams);
  const rmcElementId = useSelector((state) => state?.rmc?.rmcElementId);
  const { listMeet, now, countToday, tabKey } = useSelector(
    (state) => state.visioList
  );

  const { pathname } = useLocation();
  const [urlData, setUrlDATA] = useState({
    visio_name: "",
    owner_id: null,
    owner: "",
    task_id: null,
    loading: false,
    room_name: "",
    token: "",
    url: "",
    status: state.default,
    errorType: "",
    href: "",
    visioPin: null,
    visioPhone: [],
  });
  const [loadCancel, setLoadCancel] = useState(false);
  const [isCopied, setCopied] = useState(false);
  const [roomName, setRoomName] = useState(
    `Visio Conf ${moment_timezone(new Date()).format("DD-MM-YYYY HH:mm:ss")}`
  );

  // const textToCopy = useCallback(
  //   (owner_name, pretty_url) =>
  //     `${t("chat.header.visio.title2")},\n\n
  //   ${owner_name} ${t("chat.header.visio.description")}\n
  // ${pretty_url}`,
  //   [t]
  // );

  const textToCopy = useCallback(
    (owner_name, pretty_url) => `${t("chat.header.visio.title2")},\n\n 
    ${owner_name} ${t("chat.header.visio.description")}\n
  ${pretty_url}`,

    [t]
  );

  const advancedTextToCopy = useCallback(() => {
    return `
    <p>${t("visio.followVisioLinkClipboard")}: <a href="${
      urlData?.url
    }" target="_blank" rel="noopener noreferrer">${urlData?.url}</a></p>
    ${
      urlData?.visioPhone?.length > 0
        ? `<p>${t("visio.followVisioInPhoneClipboardPartOne")} <strong>${
            urlData?.visioPhone[0]
          }</strong>`
        : ""
    } ${" "} ${
      urlData?.visioPhone?.length > 0
        ? `${t("visio.followVisioInPhoneClipboardPartTwo")}: <strong>${
            urlData?.visioPin
          }</strong>`
        : ""
    }</p>
    ${
      urlData?.visioPhone?.length > 1
        ? `<p>${t("visio.morePhoneClipboard")}: ${urlData?.visioPhone?.map(
            (number, index) =>
              `<span key={phoneList-${index}}> ${number}</span>`
          )}</p>`
        : ""
    }
  `;
  }, [t, urlData]);

  const handleCopy = useCallback(
    async (type) => {
      if (isCopied) return;
      let time;
      setCopied(true);
      if (type === "simple") {
        const blobHtml = new Blob([urlData.url], { type: "text/html" });
        const blobText = new Blob([convertToPlain(urlData.url)], {
          type: "text/plain",
        });
        const data = [
          new ClipboardItem({
            "text/plain": blobText,
            "text/html": blobHtml,
          }),
        ];

        navigator.clipboard.write(data).then(() => {
          message.success(t("chat.bot.copied"));
        });
      } else if (type === "all") {
        const copiedText = textToCopy(urlData.owner, urlData.url);
        const blobHtml = new Blob([copiedText], { type: "text/html" });
        const blobText = new Blob([convertToPlain(copiedText)], {
          type: "text/plain",
        });
        const data = [
          new ClipboardItem({
            "text/plain": blobText,
            "text/html": blobHtml,
          }),
        ];

        navigator.clipboard.write(data).then(() => {
          message.success(t("chat.bot.copied"));
        });
      } else if (type === "advanced") {
        const copiedText = advancedTextToCopy();
        const blobHtml = new Blob([copiedText], { type: "text/html" });
        const blobText = new Blob([convertToPlain(copiedText)], {
          type: "text/plain",
        });
        const data = [
          new ClipboardItem({
            "text/plain": blobText,
            "text/html": blobHtml,
          }),
        ];

        navigator.clipboard.write(data).then(() => {
          message.success(t("chat.bot.copied"));
        });
      }

      clearTimeout(time);
      time = setTimeout(() => {
        setCopied(false);
        clearTimeout(time);
      }, 3000);
    },
    [isCopied, textToCopy, urlData.owner, urlData.url, t]
  );

  const handleSendEmailModal = useCallback(() => {
    dispatch(
      setDataConversation({
        owner: urlData.owner,
        href: urlData.href,
        url: urlData.url,
      })
    );
    const { email: sender = "", label } = user || {};
    const receivers = selectedConversation?.email
      ? [selectedConversation.email]
      : selectedParticipants
          .map(({ email }) => email)
          .filter((e) => e && e !== sender);

    const title = t("chat.header.visio.title");

    const description = t("chat.header.visio.description");

    const message = `
    <p><strong>${title} ${urlData.owner}</strong></p>
    <p>${label} ${description}</p>
    <p>
      <a href="${urlData.href}" target="_blank" rel="noreferrer noopener">
        ${urlData.url}
      </a>
    </p>
  `;
    dispatch(setEmailFields({ sender, receivers, subject: title, message }));
  }, [user, urlData, selectedConversation, selectedParticipants, t, dispatch]);

  const itemsPopOverMeet = useMemo(
    () => [
      {
        key: "dropdown1",
        label: t("chat.header.copyLink"),
        icon: <LinkOutlined style={{ fontSize: "100%" }} />,
        onClick: () => handleCopy("all"),
      },
      {
        key: "dropdown2",
        label: t("chat.header.shareWithEmail"),
        icon: <MailOutlined style={{ fontSize: "100%" }} />,
        disabled: user?.access && user?.access["email"] === "0",
        onClick: () => {
          if (user?.access["email"] === "1") {
            onClose();
            handleSendEmailModal();
          }
        },
      },
    ],
    [t, user?.access, handleCopy, dispatch, handleSendEmailModal]
  );
  const handleGetUrl = useCallback(
    async (elementID) => {
      try {
        let url = new URL(URL_ENV?.REACT_APP_DOMAIN);
        if (urlData.url) return;
        setUrlDATA((p) => ({ ...p, errorType: "", loading: true }));

        const formData = new FormData();
        formData.append(
          "label",
          `Visio Conf ${moment_timezone(new Date()).format(
            "DD-MM-YYYY HH:mm:ss"
          )}`
        );

        formData.append("owner_id", user?.id);
        if (selectedConversation?.id) {
          if (selectedConversation?.type === "user")
            id && formData.append("guests[]", id);
          else {
            formData.append("room_id", selectedConversation?.id);
          }
        }

        formData.append("tasks_type_id", 3);
        if (pathname === "/rmc") {
          formData.append("source", 4);
          formData.append("family_id", 9);
          formData.append("element_id", elementID);
        } else {
          if (guest) {
            formData.append("guests[]", guest);
          } else if (contactInfo.id || openView360InDrawer) {
            formData.append(
              "family_id",
              openView360InDrawer
                ? contactInfoFromDrawer.family_id
                : contactInfo.family_id
            );
            formData.append(
              "element_id",
              openView360InDrawer ? contactInfoFromDrawer.id : contactInfo.id
            );
          }
          formData.append("source", 2);
        }

        const response = await MainService.createNewTask(formData);
        if (response.status === 200) {
          url.searchParams.set(
            "room_visio_name",
            response?.data?.visio?.room_name
          );

          const pretty_url = url.href;
          url.searchParams.set("jwt", response?.data?.visio.token);
          const href_url = url.href;
          setUrlDATA({
            task_id: response?.data?.data?.id,
            owner_id: response?.data?.data?.owner_id?.id,
            owner: response?.data?.data?.owner_id?.label,
            visio_name: response?.data?.data?.label,
            room_name: response?.data?.visio?.room_name,
            loading: false,
            token: response?.data?.visio?.token,
            url: pretty_url,
            href: href_url,
            status: state.default,
            visioPin: response?.data?.data?.visio?.pin,
            visioPhone: response?.data?.data?.visio?.phone_numbers,
          });
          if (!id_visio) {
            dispatch(
              setVisoParams({
                visio_name: response?.data?.data?.label,
                moderator: false,
                owner_id: response?.data?.data?.owner_id?.id,
                token: response?.data?.visio?.token,
                name: response?.data?.visio?.room_name,
              })
            );
          }
          dispatch(createVisio({ data: response.data, t }));
          //update kpi in visio

          if (window.location.pathname.includes("visio")) {
            let kpi = { data: [] };
            let kpiDate = { data: [] };
            kpiDate = await MainService.getKpiDateVisio();
            kpi = await MainService.getKpiVisio(1);

            dispatch(
              setKpiVisio(
                kpi &&
                  kpi.data && [
                    ...Object.entries(kpi.data).map(([key, value]) => {
                      return {
                        title: displayStatKey(t, key, ""),
                        value: value,
                        tr: false,
                      };
                    }),
                  ]
              )
            );
            dispatch(
              setKpiDateVisio(
                kpiDate &&
                  kpiDate.data &&
                  Object.entries(kpiDate.data).map(([key, value]) => {
                    return { title: key, value: value, tr: true };
                  })
              )
            );
          }
          if (guest) {
            dispatch(setTask360(response.data.data));
          } else if (contactInfo.id || openView360InDrawer) {
            dispatch(setTask360(response.data.data));
          }
        } else throw Error(response.data);
      } catch (error) {
        setUrlDATA((p) => ({
          ...p,
          status: state.error,
          loading: false,
          errorType: error?.response?.data?.errors,
        }));
      }
    },
    [
      contactInfo.id,
      pathname,
      urlData.url,
      id,
      user?.id,
      selectedConversation?.type,
      selectedConversation?.id,
      t,
      dispatch,
    ]
  );

  // useEffect(() => {
  //   if (open) handleGetUrl();
  // }, [handleGetUrl, open]);

  useEffect(() => {
    if (open) {
      if (pathname === "/rmc" && rmcElementId) {
        handleGetUrl(rmcElementId);
      } else handleGetUrl();
    }
  }, [handleGetUrl, open, pathname, rmcElementId]);

  const cancelVisio = async () => {
    try {
      setLoadCancel(true);
      let formData = new FormData();
      formData.append("id[]", urlData?.task_id);
      formData.append("room", selectedConversation?.type === "room" ? 1 : 0);
      pathname?.includes("/chat") &&
        formData.append("message_id", visioEventId);
      const response = await MainService.deleteSpecificTask(formData);
      if (response?.status === 200) {
        setLoadCancel(false);
        if (contactInfo.id || openView360InDrawer) {
          dispatch(cancelTask360(urlData?.task_id));
        }
        toastNotification("success", "Visio is canceled", "topRight");
        dispatch(setVisioEventId(null));
        if (pathname?.includes("/visio")) {
          let newArr =
            listMeet &&
            listMeet.filter((element) => urlData?.task_id !== element?.id);
          dispatch(setListMeet(newArr));
          dispatch(setNow({ now: now, countToday: countToday - 1 }));
          let kpi = { data: [] };
          let kpiDate = { data: [] };
          kpiDate = await MainService.getKpiDateVisio();
          kpi = await MainService.getKpiVisio(tabKey);
          dispatch(
            setKpiVisio(
              kpi &&
                kpi.data && [
                  ...Object.entries(kpi.data).map(([key, value]) => {
                    return {
                      title: displayStatKey(t, key, ""),
                      value: value,
                      tr: false,
                    };
                  }),
                ]
            )
          );
          dispatch(
            setKpiDateVisio(
              kpiDate &&
                kpiDate.data &&
                Object.entries(kpiDate.data).map(([key, value]) => {
                  return { title: key, value: value, tr: true };
                })
            )
          );
        }
        onClose();
      }
    } catch (error) {
      setLoadCancel(false);
      console.log(`Error ${error}`);
    }
  };

  return (
    <>
      <Suspense
        fallback={
          <div className="fixed inset-0 z-[9999] flex h-full w-full   items-center justify-center bg-black/5 ">
            <Loader size="2rem" />
          </div>
        }
      >
        <ModalConfirm
          onCancel={() => {
            setUrlDATA({
              visio_name: "",
              owner_id: null,
              owner: "",
              task_id: null,
              loading: false,
              room_name: "",
              token: "",
              url: "",
              status: state.default,
              href: "",
              visioPin: null,
              visioPhone: [],
            });
            onClose();
          }}
          open={open}
          title={
            <p className="w-full text-center text-xl">
              {" "}
              {t("chat.header.share_video_call")}{" "}
            </p>
          }
          content={
            <div className="flex w-full flex-col items-center gap-y-1 px-4">
              {urlData.loading ? (
                <Spin size="large" />
              ) : urlData.status === state.error ? (
                urlData.errorType === "Localization error" ? (
                  <div className="flex flex-col items-center justify-center">
                    <Typography.Text type="danger">
                      {t("localisation.noConfig")}
                    </Typography.Text>
                    <Button
                      onClick={() => navigate("/profile/localization")}
                      className="mt-2"
                      danger
                      type="primary"
                      icon={<ArrowLeftOutlined />}
                    >
                      {t("tasks.GoToLocalization")}
                    </Button>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center">
                    <Typography.Text type="danger">
                      {t("chat.header.createVideoCallError")}
                    </Typography.Text>
                    <Button
                      onClick={handleGetUrl}
                      className="mt-2"
                      danger
                      type="primary"
                    >
                      {t("chat.reload")}
                    </Button>
                  </div>
                )
              ) : (
                <>
                  <p className="text-center text-sm text-zinc-400">
                    {" "}
                    {t("chat.header.createVideoCallDescription")}{" "}
                  </p>
                  <Divider className="my-2.5" />
                  <div className="flex w-full flex-col items-start">
                    <div className=" flex w-full  space-x-3 text-sm ">
                      <p>{t("chat.header.visio.roomName")} : </p>
                      <Typography.Text
                        type="secondary"
                        className="w-full flex-1 text-sm underline "
                        editable={{
                          text: roomName,
                          onChange: (key) => (visioName = key),
                          maxLength: 200,
                          onEnd: () => {
                            if (visioName.trim() !== "") {
                              dispatch(
                                updateVisioName({
                                  id_visio: id_visio || null,
                                  label: visioName,
                                  task_id: urlData?.task_id,
                                  errorText: t("toasts.errorFetchApi"),
                                })
                              );
                              dispatch(
                                updateLabelinListVisio({
                                  id: urlData?.task_id,
                                  label: visioName,
                                })
                              );

                              setRoomName(visioName);
                            } else return false;
                          },
                        }}
                      >
                        {roomName}
                      </Typography.Text>
                    </div>
                    <p className="my-1 text-[10px] text-gray-400 ">
                      {t("chat.header.visio.validationHint")}
                    </p>
                  </div>
                  <div className="mt-1 flex w-full items-center justify-between rounded-md border border-zinc-600 bg-zinc-200 px-2 py-2">
                    <div className="pl-2">
                      {/* <p className="text-gray-500 ">{urlData.url}</p> */}
                      <Typography.Text
                        className="text-gray-500 "
                        copyable={{
                          text: urlData.url,
                          tooltips: [
                            t("vue360.copyLink"),
                            t("chat.action.copied"),
                          ],
                        }}
                      >
                        {urlData.url}
                      </Typography.Text>
                      {urlData?.visioPhone?.length > 0 && (
                        <section className="flex flex-row items-center">
                          <label>{t("visio.callOn")}: </label>{" "}
                          {/* <p className="ml-1 text-gray-500">{urlData?.visioPhone[0]}</p> */}
                          <Select
                            className="phones-select"
                            defaultValue={
                              urlData?.visioPhone?.length > 0
                                ? urlData?.visioPhone[0]
                                : null
                            }
                            bordered={false}
                            popupMatchSelectWidth={false}
                            options={urlData?.visioPhone?.map((phone) => ({
                              label: phone,
                              value: phone,
                            }))}
                          />
                        </section>
                      )}
                      {urlData?.visioPhone?.length > 0 && (
                        <section className="flex flex-row items-center">
                          <label>Code: </label>{" "}
                          <p className="ml-1 text-gray-500">
                            {urlData?.visioPin}
                          </p>
                        </section>
                      )}
                    </div>

                    {urlData?.visioPhone?.length > 0 && (
                      <Tooltip
                        title={
                          !isCopied ? t("chat.bot.copy") : t("chat.bot.copied")
                        }
                      >
                        <Button
                          shape="circle"
                          type="text"
                          className="text-gray-500/70 hover:text-gray-500/90"
                          onClick={() => handleCopy("advanced")}
                          icon={
                            !isCopied ? (
                              <CopyOutlined className=" scale-110" />
                            ) : (
                              <CheckCircleOutlined className=" scale-110" />
                            )
                          }
                        />
                      </Tooltip>
                    )}
                  </div>
                </>
              )}
            </div>
          }
          footerButton={
            <div className="flex w-full items-center justify-between">
              <Dropdown
                //   disabled={urlData.loading || urlData.status === state.error}
                arrow
                menu={{
                  items: itemsPopOverMeet,
                }}
                placement="bottomRight"
              >
                <Button icon={<ShareAltOutlined />} type="default">
                  {t("chat.header.share")}
                </Button>
              </Dropdown>

              <Button
                // className="ml-2"
                onClick={() => {
                  onClose();
                  dispatch(toggleVisio(true));
                  // dispatch(onChangeTabVisio({ value: 1, tabKey: 1, t,search }));
                }}
                disabled={
                  isOpen || urlData.loading || urlData.status === state.error
                }
                type="primary"
                icon={<VideoCameraOutlined />}
              >
                {t("chat.header.participate")}
              </Button>
              <Button
                danger
                onClick={() => cancelVisio()}
                // className="ml-2"
                loading={loadCancel}
                disabled={
                  (pathname?.includes("/chat") && !visioEventId) ||
                  urlData.errorType === "Localization error"
                }
              >
                {t("voip.cancel")}
              </Button>
            </div>
          }
        />
      </Suspense>
    </>
  );
}

export default ShareVisio;
