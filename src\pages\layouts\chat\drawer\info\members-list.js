import { List } from "antd";
import { useState, useRef } from "react";
import GroupItemChat from "components/Chat/GroupItem";
import { getName, TAB_LIST_HEIGHT } from "../../utils/ConversationUtils";
import useGetInfoDiscussion from "../../hooks/useGetInfoDiscussion";
import { useSelector } from "react-redux";

const Members = ({ updateRoom, searchMembersInInfo }) => {
  const [openDropDown, setOpenDropDown] = useState(false);
  const [selected, setSelected] = useState({
    state: false,
    item: null,
    clientY: null,
  });
  const { data } = useGetInfoDiscussion();
  const selectedConversation = useSelector(
    (state) => state.ChatRealTime.selectedConversation
  );
  const listRef = useRef(null);

  return (
    <div
      id="scrollableDiv"
      style={{
        height: `calc(100vh - ${TAB_LIST_HEIGHT} - 20px)`,

        overflow: "auto",
        padding: "0",
      }}
      className="scrollableDiv"
      ref={listRef}
    >
      <List
        className="membersList"
        dataSource={data?.data?.room_info?.participants
          ?.filter((item) =>
            getName(item?.name, "name")
              .toLowerCase()
              .includes(searchMembersInInfo.toLowerCase())
          )

          .sort((a, b) => {
            if (a._id === selectedConversation?.admin_id) return -1;
            if (b._id === selectedConversation?.admin_id) return 1;
            return 0;
          })}
        renderItem={(item) => (
          <GroupItemChat
            key={item._id}
            item={item}
            updateRoom={updateRoom}
            setSelected={setSelected}
            selected={selected}
            openDropDown={openDropDown}
            setOpenDropDown={setOpenDropDown}
          />
        )}
      />
    </div>
  );
};
export default Members;
