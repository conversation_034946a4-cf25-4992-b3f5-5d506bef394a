import { Col, Row } from "antd";
import React, { useMemo } from "react";
import CardWithGraph from "../CardWithGraph";
import { GoTo } from "pages/home/<USER>";
import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { CheckOutlined, CloseOutlined, TeamOutlined } from "@ant-design/icons";
import { CgUserlane } from "react-icons/cg";

const CardStatConvertedLeadsToContacts = () => {
  const [t] = useTranslation("common");
  const navigate = useNavigate();
  const { leads } = useSelector((state) => state.dashboardRealTime);
  const { user } = useSelector((state) => state.user);
  const processLeadData = (lead) => {
    const totalLeads = leads.find((item) => item.name === "Total")?.value || 1;
    const isConverted = lead.name === "Leads_converted";
    const isTotal = lead.name === "Total";

    const rate = !isTotal && ((lead.value / totalLeads) * 100).toFixed(2);

    return {
      ...lead,
      name: (
        <div className="flex flex-col gap-y-1.5">
          <span>
            {isConverted
              ? `${t("menu1.leads")} → ${t("menu1.contacts")}`
              : isTotal
              ? t("menu1.leads")
              : t("dashboard.leadsNotConverted")}
          </span>
          {isConverted && (
            <span className="pb-1 text-xs text-gray-400">
              {t("dashboard.successfulConversions")} - (
              {t("familyProduct.rate")}: {rate}%)
            </span>
          )}
          {isTotal && (
            <span className="pb-1 text-xs text-gray-400">
              {t("contacts.total_elements")}
            </span>
          )}
          {!isTotal && !isConverted && (
            <span className="pb-1 text-xs text-gray-400">Taux: {rate}%</span>
          )}
        </div>
      ),
      number: lead.value,
      icon: isTotal ? (
        <CgUserlane className="h-5 w-5 text-gray-600" />
      ) : (
        <span
          className={`relative ${
            isConverted ? "text-[#34B7FE]" : "text-[#B5BCC8]"
          }`}
        >
          <TeamOutlined style={{ fontSize: "18px" }} />
          {isConverted ? (
            <CheckOutlined
              style={{ fontSize: "10px" }}
              className="absolute -right-[7px] -top-[2px]"
            />
          ) : (
            <CloseOutlined
              style={{ fontSize: "10px" }}
              className="absolute -right-[6px]"
            />
          )}
        </span>
      ),
    };
  };

  const dataLeads_Contacts = useMemo(
    () => leads?.map(processLeadData) || [],
    [leads, t]
  );

  const chartData = useMemo(() => {
    const filteredLeads =
      leads?.filter((el) =>
        ["Leads_converted", "Leads_non_converted"].includes(el.name)
      ) || [];

    const totalLeads = leads?.find((item) => item.name === "Total")?.value || 1;

    return {
      data: filteredLeads.map((el) => {
        const isConverted = el.name === "Leads_converted";

        return {
          ...el,
          name: isConverted
            ? `${t("menu1.leads")} → ${t("menu1.contacts")}`
            : `${t("dashboard.leadsNotConverted")}`,
          color: isConverted ? "#34B7FE" : "#B5BCC8",
          y: el.value,
        };
      }),
      name: "",
    };
  }, [leads, t]);

  return (
    <Row gutter={[8, 8]}>
      <Col span={24}>
        <CardWithGraph
          title={
            <div className="flex items-center justify-between">
              <span>{t("menu1.leads")} &nbsp;</span>
              <GoTo
                to={"10"}
                title={t("menu1.leads")}
                navigate={navigate}
                t={t}
                user={user}
              />
            </div>
          }
          stats={dataLeads_Contacts}
          chartData={chartData}
        />
      </Col>
    </Row>
  );
};

export default React.memo(CardStatConvertedLeadsToContacts);
