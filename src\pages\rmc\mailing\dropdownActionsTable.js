import {
  ExclamationCircleOutlined,
  MoreOutlined,
  SyncOutlined,
} from "@ant-design/icons";
import { Button, Dropdown, Modal } from "antd";
import React, { useMemo, useState } from "react";
import { AiOutlineDelete } from "react-icons/ai";
import { IoMdCheckboxOutline } from "react-icons/io";
import { IoArchiveOutline } from "react-icons/io5";
import {
  Md<PERSON>abel,
  MdOutlineHistoryToggleOff,
  MdOutlineLabel,
  MdOutlineMarkunread,
} from "react-icons/md";
import { PiIdentificationBadge } from "react-icons/pi";
import { VscMailRead } from "react-icons/vsc";
import Identification from "./identification";
import Affectation from "./Affectation";
import Qualification from "./Qualification";
import {
  ArchiveMessage,
  makeAsLabel,
  SeenMessage,
  UnarchiveMessage,
} from "./services/ActionsApi";
import { useDispatch } from "react-redux";
import {
  getMailStats,
  updateEmailsStats,
} from "new-redux/actions/mail.actions";
import Transfer from "./Transfer";
import { BiTransferAlt } from "react-icons/bi";
import { GoTag } from "react-icons/go";
import { TbArchiveOff, TbRestore } from "react-icons/tb";
import { toastNotification } from "components/ToastNotification";
import MainService from "services/main.service";
import { truncateString } from "pages/voip/helpers/helpersFunc";
import TicketIconSphere from "components/icons/TicketIconSphere";

const DropdownActionsTable = ({
  record,
  setThirdId,
  setOpenLogDrawer,
  t,
  type,
  conditionActions,
  usedAccount,
  dataMailInbox,
  setDataMailInbox,
  user,
  access,
  dataTags,
  setOpenTask,
  setEmailId,
  setOpenModal,
  setTypeDelete,
  clickArchive,
  getMailsInbox,
  setClickArchive,
  setMailingProps,
  setOpenForm,
  unSpam,
}) => {
  const dispatch = useDispatch();
  const [openModalArchive, setOpenModalArchive] = useState(false);
  const [popoverStates, setPopoverStates] = useState({
    popoverIdentifVisible: false,
    popoverAffectVisible: false,
    popoverQualifVisible: false,
    popoverMoveVisible: false,
  });

  const [id, setId] = useState(false);
  const url_string = window.location.href;
  const url = new URL(url_string);
  const folderMailing = url.pathname.split("/")[3];
  const [typeArchive, setTypeArchive] = useState("");

  const togglePopover = (popoverName) => {
    setPopoverStates((prevState) => ({
      ...prevState,
      [popoverName]: !prevState[popoverName],
    }));
  };

  const SeenMail = async (id, message) => {
    const response = await SeenMessage({
      id,
      message,
      t,
      accountId: usedAccount?.value,
    });

    if (response) {
      dispatch(
        updateEmailsStats({
          id: usedAccount?.value,
          type: response.data === 1 ? "substract" : "add",
          typeEmail: "inbox", //add back type new email: inbox or spam
        })
      );
      getMailsInbox();
    }
  };

  const ArchiveMail = async (id, message) => {
    try {
      const response = await ArchiveMessage({
        usedAccount,
        id,
        message,
        setClickArchive,
        t,
        setOpenModalArchive,
      });
      if (response) {
        getMailsInbox();
        dispatch(getMailStats());
      }
    } catch (error) {
      console.log("error: " + error);
    }
  };

  const UnarchiveMail = async (id, message) => {
    try {
      const response = await UnarchiveMessage({
        usedAccount,
        id,
        message,
        setClickArchive,
        t,
        setOpenModalArchive,
        dispatch,
      });
      if (response) {
        getMailsInbox();
        dispatch(getMailStats());
      }
    } catch (error) {
      console.log("error: " + error);
    }
  };

  const RestoreMail = async (id, box, archives) => {
    var formData = new FormData();

    formData.append("id", id);
    formData.append("box", box);
    formData.append("archives", archives);

    try {
      const response = await MainService.restoreMail(formData);
      if (response.status === 200) {
        toastNotification(
          "success",
          `Votre email a été récupéré`,
          "topRight",
          3
        );
        getMailsInbox();
        dispatch(getMailStats());
      }
    } catch (error) {
      toastNotification(
        "error",
        error?.response?.data
          ? error?.response?.data?.message
          : t("toasts.errorFetchApi"),
        "topRight",
        4
      );
      console.log(error);
    }
  };

  //
  const itemLabels = useMemo(() => {
    if (!usedAccount?.labels?.length) return [];
    const labels = usedAccount?.labels.map((label) => ({
      label: truncateString(label.label, 20, false),
      key: label.id,
      icon: (
        <MdLabel
          style={{ fontSize: 16, color: label?.color || "rgb(107 114 128)" }}
        />
      ),
      disabled:
        record?.labelEmail?.length &&
        record?.labelEmail?.find((e) => e.label === label.label),
    }));
    const item = {
      key: "labels",
      label: (
        <span onClick={(e) => e.stopPropagation()}>{t("mailing.labelAs")}</span>
      ),
      icon: (
        <MdOutlineLabel style={{ fontSize: 18, color: "rgb(148 163 184)" }} />
      ),
      children: labels,
      onClick: (e) => {
        e.domEvent.stopPropagation();
        dispatch(makeAsLabel(usedAccount.value, e.key, record.third_id, t));
      },
    };
    return [item];
  }, [t, usedAccount]);
  //

  return (
    <>
      <Dropdown
        menu={{
          items: [
            folderMailing === "inbox"
              ? {
                  label: (
                    <div
                      onClick={(e) => {
                        e.stopPropagation();
                        togglePopover("popoverIdentifVisible");
                      }}
                      style={{
                        display: "flex",
                        alignItems: "center",
                        cursor: "pointer",
                        width: "100%",
                      }}
                    >
                      <PiIdentificationBadge
                        style={{
                          width: "16px",
                          height: "16px",
                          color: "rgb(148 163 184)",
                          marginRight: "8px", // Spacing between icon and label
                        }}
                      />
                      <span style={{ flex: 1 }}>
                        <Identification
                          id={record.key}
                          fromName={record.from.name}
                          fromEmail={record.from.address}
                          idEmail={record.key}
                          identification={record.identification}
                          dataMailInbox={dataMailInbox}
                          setData={setDataMailInbox}
                          usedAccount={usedAccount}
                          t={t}
                          user={user}
                          access={access}
                          owner={record?.owner}
                          transfert={record?.transfert}
                          type="dropdown"
                          openAction={popoverStates.popoverIdentifVisible}
                          togglePopover={togglePopover}
                        />
                      </span>
                    </div>
                  ),

                  key: "6",
                  onClick: (e) => {
                    e.domEvent.stopPropagation();
                  },
                  disabled:
                    (type === "inbox" && !conditionActions(record)) ||
                    record.identification?.label_data ||
                    (record?.nbr > 1 &&
                      record?.fromThread[0]?.identification?.label_data),
                }
              : null,

            ...(type === "inbox" /*|| type === "outbox"*/
              ? [
                  {
                    key: "convert",
                    label: (
                      <span onClick={(e) => e.stopPropagation()}>
                        {"Convert to"}
                      </span>
                    ),
                    // label: "Convert to",
                    icon: (
                      <SyncOutlined
                        style={{
                          fontSize: 14,
                          color: "rgb(148 163 184)",
                        }}
                      />
                    ),
                    disabled:
                      (type === "inbox" && !conditionActions(record)) ||
                      record.affectation?.affect_label,
                    onClick: (e) => {
                      e.domEvent.stopPropagation();
                    },
                    children: [
                      {
                        key: "convert-6",
                        label: (
                          <span
                            onClick={(e) => {
                              e.stopPropagation();
                              setMailingProps({
                                id: record.key,
                                familyId: 6,
                              });
                              setOpenForm(true);
                              // console.log({ record });
                            }}
                          >
                            {t("contacts.ticket")}
                          </span>
                        ),
                        onClick: (e) => {
                          // e.stopPropagation();
                          e.domEvent.stopPropagation();
                          setMailingProps({
                            id: record.key,
                            familyId: 6,
                          });
                          setOpenForm(true);
                        },
                        icon: (
                          <TicketIconSphere
                            size={18}
                            color="rgb(148 163 184)"
                          />
                        ),
                      },
                      // {
                      //   key: "convert-3",
                      //   label: (
                      //     <span
                      //       onClick={(e) => e.stopPropagation()}
                      //       className=" cursor-not-allowed"
                      //       style={{ color: "rgba(0, 0, 0, 0.25)" }}
                      //     >
                      //       {t("import.deal")}
                      //     </span>
                      //   ),
                      //   disabled: true,
                      //   icon: (
                      //     <HandCoins
                      //       style={{
                      //         fontSize: 15,
                      //         color: "rgb(148 163 184)",
                      //       }}
                      //     />
                      //   ),
                      // },
                    ],
                  },
                ]
              : []),

            type === "inbox" || type === "outbox"
              ? {
                  label: (
                    <div
                      onClick={(e) => {
                        e.stopPropagation();
                        togglePopover("popoverAffectVisible");
                      }}
                      style={{
                        display: "flex",
                        alignItems: "center",
                        cursor: "pointer",
                        width: "100%",
                      }}
                    >
                      <IoMdCheckboxOutline
                        style={{
                          width: "16px",
                          height: "16px",
                          color: "rgb(148 163 184)",
                          marginRight: "8px",
                        }}
                      />
                      <span style={{ flex: 1 }}>
                        <Affectation
                          id={record?.key}
                          owner={record?.owner}
                          // fromName={record.from.name}
                          fromEmail={record?.from?.address}
                          idEmail={record?.key}
                          transfert={record?.transfert}
                          affectation={record?.affectation}
                          setDataSource={setDataMailInbox}
                          usedAccount={usedAccount}
                          t={t}
                          user={user}
                          access={access}
                          type="dropdown"
                          openAction={popoverStates.popoverAffectVisible}
                        />
                      </span>
                    </div>
                  ),
                  key: "7",
                  // icon: (
                  //   <IoMdCheckboxOutline
                  //     style={{
                  //       width: "16px",
                  //       height: "16px",
                  //       color: "rgb(148 163 184)",
                  //     }}
                  //   />
                  // ),
                  onClick: (e) => {
                    e.domEvent.stopPropagation();
                  },
                  disabled:
                    (type === "inbox" && !conditionActions(record)) ||
                    record.affectation?.affect_label,
                }
              : null,

            type === "inbox" || type === "outbox"
              ? {
                  label: (
                    <div
                      onClick={(e) => {
                        e.stopPropagation();
                        togglePopover("popoverQualifVisible");
                      }}
                      style={{
                        display: "flex",
                        alignItems: "center",
                        cursor: "pointer",
                        width: "100%",
                      }}
                    >
                      <GoTag
                        style={{
                          width: "16px",
                          height: "16px",
                          color: "rgb(148 163 184)",
                          marginRight: "8px",
                        }}
                      />
                      <span style={{ flex: 1 }}>
                        <Qualification
                          tags={record.tags}
                          id={record.key}
                          info={{
                            name: record?.qualification?.label,
                            number: record?.qualification?.note,
                          }}
                          owner={record?.owner}
                          transfert={record?.transfert}
                          data={dataTags}
                          setDataSource={setDataMailInbox}
                          setOpenTask={setOpenTask}
                          usedAccount={usedAccount}
                          type="dropdown"
                          openAction={popoverStates.popoverQualifVisible}
                        />
                      </span>
                    </div>
                  ),
                  key: "8",

                  onClick: (e) => {
                    e.domEvent.stopPropagation();
                  },
                  disabled:
                    (type === "inbox" && !conditionActions(record)) ||
                    record.tags?.tags?.length > 0,
                }
              : null,

            folderMailing === "inbox"
              ? {
                  label: (
                    <div
                      onClick={(e) => {
                        e.stopPropagation();
                        togglePopover("popoverMoveVisible");
                      }}
                      style={{
                        display: "flex",
                        alignItems: "center",
                        cursor: "pointer",
                        width: "100%",
                      }}
                    >
                      <BiTransferAlt
                        style={{
                          width: "16px",
                          height: "16px",
                          color: "rgb(148 163 184)",
                          marginRight: "8px",
                        }}
                      />
                      <Transfer
                        idEmail={record?.key}
                        accountId={usedAccount?.value}
                        dataRow={record}
                        // typeFrom="inbox"
                        setDataSource={setDataMailInbox}
                        typeFrom="dropdown"
                        openAction={popoverStates.popoverMoveVisible}
                      />
                    </div>
                  ),
                  key: "9",
                  // icon: (
                  //   <BiTransferAlt
                  //     style={{
                  //       width: "16px",
                  //       height: "16px",
                  //       color: "rgb(148 163 184)",
                  //     }}
                  //   />
                  // ),
                  onClick: (e) => {
                    e.domEvent.stopPropagation();
                  },
                  disabled:
                    (type === "inbox" && !conditionActions(record)) ||
                    record?.transfert?.account_id ||
                    record?.owner?.label_data,
                }
              : null,

            type === "inbox" || type === "outbox"
              ? {
                  type: "divider",
                }
              : null,
            ...itemLabels,
            {
              label: t("mailing.Historique"),
              key: "historic",
              icon: (
                <MdOutlineHistoryToggleOff
                  style={{
                    width: "16px",
                    height: "16px",
                    color: "rgb(148 163 184)",
                  }}
                />
              ),
              onClick: (e) => {
                e.domEvent.stopPropagation();
                setThirdId(record.third_id);
                setOpenLogDrawer(record.key);
              },
            },

            usedAccount?.shared == 0 && {
              label:
                record.seen === 0
                  ? t("mailing.markRead")
                  : t("mailing.markUnread"),
              key: "4",
              icon:
                record.seen === 0 ? (
                  <MdOutlineMarkunread
                    style={{
                      width: "16px",
                      height: "16px",
                      color: "rgb(148 163 184)",
                    }}
                  />
                ) : (
                  <VscMailRead
                    style={{
                      width: "16px",
                      height: "16px",
                      color: "rgb(148 163 184)",
                    }}
                  />
                ),
              onClick: (e) => {
                e.domEvent.stopPropagation();
                record.seen === 0
                  ? SeenMail(record.key, t("mailing.emailRead"))
                  : SeenMail(record.key, t("mailing.emailnotRead"));
              },
            },

            type !== "archive" && type !== "trash"
              ? {
                  label: t("import.archiveAction"),
                  key: "3",
                  disabled:
                    clickArchive ||
                    (type === "inbox" && !conditionActions(record)),
                  icon: (
                    <IoArchiveOutline
                      style={{
                        width: "16px",
                        height: "16px",
                        color: "rgb(148 163 184)",
                      }}
                    />
                  ),
                  onClick: (e) => {
                    e.domEvent.stopPropagation();
                    setTypeArchive("archive");
                    setOpenModalArchive(true);
                    setId(record.key);
                  },
                }
              : null,
            type === "archive"
              ? {
                  label: t("mailing.unarchive"),
                  key: "10",
                  disabled:
                    clickArchive ||
                    (type === "inbox" && !conditionActions(record)),
                  icon: (
                    <TbArchiveOff
                      style={{
                        width: "16px",
                        height: "16px",
                        color: "rgb(148 163 184)",
                      }}
                    />
                  ),
                  onClick: (e) => {
                    e.domEvent.stopPropagation();
                    setTypeArchive("unarchive");
                    setOpenModalArchive(true);
                    setId(record.key);
                  },
                }
              : null,
            ...(type === "spam"
              ? [
                  {
                    label: t("mailing.notSpam"),
                    key: "notSpam",
                    icon: (
                      <ExclamationCircleOutlined
                        style={{
                          fontSize: 14,
                          color: "rgb(148 163 184)",
                        }}
                      />
                    ),
                    onClick: (e) => {
                      e.domEvent.stopPropagation();
                      // e.stopPropagation();
                      unSpam(record.key, record.third_id);
                    },
                  },
                ]
              : []),

            type !== "trash"
              ? {
                  label: t("chat.action.delete"),
                  key: "delete",
                  icon: (
                    <AiOutlineDelete
                      style={{
                        width: "16px",
                        height: "16px",
                      }}
                    />
                  ),
                  onClick: (e) => {
                    e.domEvent.stopPropagation();
                    setEmailId(record.key);
                    setOpenModal(true);
                    setTypeDelete("simple");
                  },
                  danger: true,
                  disabled: type === "inbox" && !conditionActions(record),
                }
              : null,

            type === "trash"
              ? {
                  label: t("mailing.restaureEmail"),
                  key: "11",
                  icon: (
                    <TbRestore
                      style={{
                        width: "16px",
                        height: "16px",
                        color: "rgb(148 163 184)",
                      }}
                    />
                  ),
                  onClick: (e) => {
                    e.domEvent.stopPropagation();
                    RestoreMail(record.key, record.box, record.archives);
                  },
                }
              : null,
          ],
        }}
        trigger={["click"]}
        // overlayStyle={{ width: "135px" }}
        // open={openPopover}
        // onOpenChange={setOpenPopover}
      >
        <Button
          shape="circle"
          type="text"
          size="small"
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          <MoreOutlined
            className=" cursor-pointer text-gray-400 hover:text-gray-700"
            style={{ fontSize: "18px" }}
            // onClick={(e) => e.stopPropagation()}
          />
        </Button>
      </Dropdown>

      <Modal
        title={
          typeArchive === "archive"
            ? t("mailing.archiveEmail")
            : t("mailing.unarchiveMail")
        }
        open={openModalArchive}
        onCancel={() => setOpenModalArchive(false)}
        okButtonProps={{
          style: {
            backgroundColor: "red",
            color: "white",
            width: "70",
            border: "none",
          },
        }}
        cancelButtonProps={{
          disabled: true,
        }}
        footer={
          <div style={{ display: "flex", justifyContent: "end", gap: "5px" }}>
            <Button key="cancel" onClick={() => setOpenModal(false)}>
              {t("mailing.NewMsg.Cancel")}
            </Button>
            <Button
              type="primary"
              // loading={loading.type === "delete" && loading.state}
              style={{
                color: "white",
              }}
              onClick={(e) => {
                e.stopPropagation();
                if (typeArchive === "archive") {
                  ArchiveMail(id, t("mailing.emailArchived"));
                } else {
                  UnarchiveMail(id, t("mailing.emailArchived"));
                }
              }}
            >
              {t("mailing.confirm")}
            </Button>
          </div>
        }
      >
        {typeArchive === "archive" ? (
          <p>{t("mailing.ArchiveMail")}</p>
        ) : (
          <p>{t("mailing.UnarchiveMail")}</p>
        )}
      </Modal>
    </>
  );
};

export default DropdownActionsTable;
