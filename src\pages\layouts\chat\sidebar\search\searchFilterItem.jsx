import { Tag } from "antd";
import React from "react";
import { useTranslation } from "react-i18next";

const SearchFilterItem = ({ searchMeta, setSelectedFilter }) => {
  const { t } = useTranslation("common");
  if (searchMeta.keyword === "") return <></>;

  const filters = [
    {
      name: t("chat.searchSide.all"),
      key: "all",
      onClick: () =>
        setSelectedFilter((p) => ({
          ...p,
          filter: "all",
        })),
    },

    {
      name: t("chat.searchSide.conversations"),
      key: "conversations",
      onClick: () =>
        setSelectedFilter((p) => ({
          ...p,
          filter: "conversations",
        })),
    },
    {
      name: t("chat.searchSide.users"),
      key: "users",
      onClick: () =>
        setSelectedFilter((p) => ({
          ...p,
          filter: "users",
        })),
    },
    {
      name: t("chat.searchSide.messages"),
      key: "messages",
      onClick: () =>
        setSelectedFilter((p) => ({
          ...p,
          filter: "messages",
        })),
    },
  ];
  return (
    <div className="flex items-center justify-evenly py-2">
      {filters.map((element) => (
        <Tag
          key={element.key}
          onClick={element.onClick}
          className="min-w-12 max-w-24 cursor-pointer rounded-full p-1 text-center text-xs font-medium"
          color={element.key === searchMeta.filter ? "processing" : "default"}>
          {element.name}
        </Tag>
      ))}
    </div>
  );
};

export default SearchFilterItem;
