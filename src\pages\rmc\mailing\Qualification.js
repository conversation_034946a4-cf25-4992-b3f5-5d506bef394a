import { useEffect, useState } from "react";
import { <PERSON>ton, Popover, Tooltip, Divider, Tag, Badge } from "antd";
import { useTranslation } from "react-i18next";
import TagContent from "./TagContent";
import { FileTextTwoTone } from "@ant-design/icons";
import "../../../index.css";
import { useSelector } from "react-redux";
import { FaCalendar } from "react-icons/fa";
import PopoverTask from "./components/popoverTask";
const Qualification = ({
  tags,
  id,
  info,
  data,
  setDataSource,
  transfert,
  owner,
  usedAccount,
  setOpenTask,
  type,
  setDetailsMail,
  setMailingProps,
  idThread,
  openAction,
  getDetailsMessageInbox,
  getDetailsThreadsCollapse,
  pageDetailsThread,
}) => {
  const [t] = useTranslation("common");
  const { user } = useSelector(({ user }) => user);
  const [openPopCon, setOpenPopCon] = useState(false);

  const tagContents = (
    <div className="flex w-60 items-center ">
      <TagContent
        tags={tags}
        id={id}
        data={data}
        info={info}
        setDataSource={setDataSource}
        setOpen={setOpenPopCon}
        type={type}
        setDetailsMail={setDetailsMail}
        idThread={idThread}
        getDetailsMessageInbox={getDetailsMessageInbox}
        getDetailsThreadsCollapse={getDetailsThreadsCollapse}
        pageDetailsThread={pageDetailsThread}
      />
    </div>
  );

  const displayTags = (tags) => {
    if (!tags?.tags?.length) return null;
    const firstTag = tags.tags[0];
    const remainingTags = tags.tags.slice(1);
    return (
      <div className="group flex ">
        <div className="flex flex-wrap	 content-center gap-y-0.5">
          {(transfert?.account_id &&
            transfert?.account_id != usedAccount?.value) ||
          (owner?.owner && owner?.owner != user.id) ? (
            <div className="flex flex-row ">
              <Tag
                style={{ cursor: "pointer" }}
                key={firstTag?.id}
                color={firstTag?.color}
              >
                <span className="text-xs font-semibold">{firstTag?.label}</span>
              </Tag>
              {firstTag.task ? (
                <PopoverTask
                  t={t}
                  // setIdCall={setIdCall}
                  // setTaskId={setTaskId}
                  setOpenTask={setOpenTask}
                  idCall={id}
                  task={firstTag.task}
                >
                  <Badge
                    offset={[-10, -10]}
                    // status="processing"
                    count={
                      <FaCalendar
                        style={{ color: "red", cursor: "help", fontSize: 16 }}
                      />
                    }
                  ></Badge>
                </PopoverTask>
              ) : null}
            </div>
          ) : (
            <Popover
              title={
                <div className="px-2 pt-2">
                  <div className="flex flex-col">
                    <p>{t("updateElement")} qualification</p>
                    <p className="w-36 truncate font-extralight 	 text-gray-500">
                      {info?.name === null ? info?.number : info?.name}
                    </p>
                  </div>
                  <Divider className="my-2" />
                </div>
              }
              content={tagContents}
              open={openPopCon}
              onOpenChange={(open) => {
                setOpenPopCon(open);
              }}
              trigger={["click"]}
              placement="bottomRight"
              overlayClassName="popover-tag-voip"
              overlayStyle={{ width: "17.5rem" }}
              overlayInnerStyle={{
                padding: "0px",
              }}
              arrow={false}
            >
              {firstTag?.task?.label ? (
                <div className="flex flex-row ">
                  <Tooltip title={t("voip.editTag")}>
                    <Tag
                      style={{ cursor: "pointer" }}
                      key={firstTag?.id}
                      color={firstTag?.color}
                    >
                      <span className="text-xs font-semibold">
                        {firstTag?.label}
                      </span>
                    </Tag>
                  </Tooltip>
                  {firstTag.task ? (
                    <PopoverTask
                      t={t}
                      // setIdCall={setIdCall}
                      // setTaskId={setTaskId}
                      setOpenTask={setOpenTask}
                      idCall={id}
                      task={firstTag.task}
                    >
                      <Badge
                        offset={[-10, -10]}
                        // status="processing"
                        count={
                          <FaCalendar
                            style={{
                              color: "red",
                              cursor: "help",
                              fontSize: 16,
                            }}
                          />
                        }
                      ></Badge>
                    </PopoverTask>
                  ) : null}
                </div>
              ) : (
                <Tag
                  style={{ cursor: "pointer" }}
                  key={firstTag?.id}
                  color={firstTag?.color}
                >
                  <span className="text-xs font-semibold">
                    {firstTag?.label}
                  </span>
                </Tag>
              )}
            </Popover>
          )}

          {remainingTags.length > 0 && (
            <Popover
              placement="bottom"
              content={
                <div className="flex flex-row space-x-1">
                  {remainingTags.map((tag, index) =>
                    tag.task ? (
                      <PopoverTask
                        t={t}
                        // setIdCall={setIdCall}
                        // setTaskId={setTaskId}
                        setOpenTask={setOpenTask}
                        idCall={id}
                        task={tag.task}
                      >
                        <Badge
                          offset={[-6, -4]}
                          // status="processing"
                          count={
                            <FaCalendar
                              style={{
                                color: "red",
                                cursor: "help",
                                fontSize: 16,
                              }}
                            />
                          }
                        >
                          <Tag
                            style={{
                              cursor: "pointer",
                              marginRight:
                                index !== remainingTags.length - 1
                                  ? "5px"
                                  : "0",
                            }}
                            key={tag?.id}
                            color={tag?.color}
                          >
                            <span className="text-xs font-semibold">
                              {tag?.label}
                            </span>
                          </Tag>
                        </Badge>
                      </PopoverTask>
                    ) : (
                      <Tag
                        style={{
                          cursor: "pointer",
                          marginRight:
                            index !== remainingTags.length - 1 ? "5px" : "0",
                        }}
                        key={tag?.id}
                        color={tag?.color}
                      >
                        <span className="text-xs font-semibold">
                          {tag?.label}
                        </span>
                      </Tag>
                    )
                  )}
                </div>
              }
              trigger={["hover"]}
            >
              <Tag className="cursor-help">
                <span className="text-xs font-semibold">{`+ ${remainingTags.length}...`}</span>
              </Tag>
            </Popover>
          )}
        </div>
        {tags?.note && (
          <Popover
            title="Note"
            content={tags?.note}
            placement="bottomRight"
            overlayStyle={{ maxWidth: "17rem" }}
            // overlayInnerStyle={{
            //   padding: "0px",
            // }}
          >
            <Button
              style={{ cursor: "help" }}
              icon={<FileTextTwoTone />}
              shape="circle"
              type="link"
            />
          </Popover>
        )}
      </div>
    );
  };
  //
  const popTitle = (
    <div className="px-2 pb-px pt-2">
      <div className="flex flex-row items-center  justify-between">
        <span>{t("mailing.Qualifier")} </span>
        <p>{t("voip.or")}</p>
        <div>
          {/* <Tooltip title="This action will open a task form"> */}
          <Button
            size="small"
            type="primary"
            onClick={() => {
              // setIdCall(id);
              setOpenPopCon(false);
              setOpenTask(true);
            }}
          >
            {t("voip.createTask")}
          </Button>
          {/* </Tooltip> */}
        </div>
      </div>
      <p className="w-36 truncate font-extralight 	 text-gray-500">
        {info?.name === null ? info?.number : info?.name}
      </p>
      <Divider className="my-2" />
    </div>
  );

  useEffect(() => {
    if (openAction) {
      setOpenPopCon(openAction);
    }
  }, [openAction]);

  return (
    <>
      {tags?.tags?.length > 0 ? (
        type === "dropdown" ? (
          t("mailing.qualify")
        ) : (
          displayTags(tags)
        )
      ) : (transfert?.account_id &&
          transfert?.account_id != usedAccount?.value) ||
        (owner?.owner && owner?.owner != user.id) ? (
        <Tooltip
          title={
            transfert?.account_id && transfert?.account_id != usedAccount?.value
              ? t("mailing.Tooltip.TransferedNoAffect")
              : owner?.owner && owner?.owner != user.id
              ? t("mailing.Tooltip.AssignNoAffect")
              : transfert?.account_id && owner?.owner
              ? t("mailing.Tooltip.TransferedAndAssigned")
              : t("mailing.Tooltip.Affecter")
          }
        >
          {type === "dropdown" ? (
            t("mailing.qualify")
          ) : (
            <Button size="small" type="dashed" disabled={true}>
              {t("mailing.qualify")}
            </Button>
          )}
        </Tooltip>
      ) : (
        <Popover
          content={tagContents}
          title={popTitle}
          open={openPopCon}
          onOpenChange={(open) => setOpenPopCon(open)}
          trigger={["click"]}
          placement="bottomLeft"
          arrow={false}
          overlayClassName="popover-tag-voip"
          overlayStyle={{ width: "17.5rem" }}
          overlayInnerStyle={{
            padding: "0px",
          }}
        >
          <Tooltip
            title={
              transfert?.account_id &&
              transfert?.account_id != usedAccount?.value
                ? t("mailing.Tooltip.TransferedQualif")
                : owner?.owner && owner?.owner != user.id
                ? t("mailing.Tooltip.AssignNoQualif")
                : transfert?.account_id && owner?.owner
                ? t("mailing.Tooltip.TransferedAndAssigned")
                : t("mailing.qualifier")
            }
          >
            {type === "dropdown" ? (
              t("mailing.qualify")
            ) : (
              <Button
                type="dashed"
                size="small"
                disabled={
                  (transfert?.account_id &&
                    transfert?.account_id != usedAccount?.value) ||
                  (owner?.owner && owner?.owner != user.id)
                }
              >
                {t("mailing.qualify")}
              </Button>
            )}
          </Tooltip>
        </Popover>
      )}
    </>
  );
};

export default Qualification;
