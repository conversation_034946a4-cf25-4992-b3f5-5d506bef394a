import React, { useState, useEffect, useCallback } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  getDriveItem,
  deleteDriveItem,
  updateDriveItem,
  getFile,
  uploadFileDrive,
  moveDriveItem,
} from "../../services/main.service";
import {
  Spin,
  Breadcrumb,
  Button,
  Dropdown,
  Form,
  message,
  Radio,
  Progress,
  Card,
  Input,
  Image,
  Tooltip,
  Divider,
} from "antd";
import {
  MoreOutlined,
  DeleteOutlined,
  InfoCircleOutlined,
  EditOutlined,
  ShareAltOutlined,
  DownloadOutlined,
  AppstoreOutlined,
  UnorderedListOutlined,
  CloudUploadOutlined,
  FileOutlined,
  CloseOutlined,
  SearchOutlined,
  EyeOutlined,
} from "@ant-design/icons";
import { debounce } from "lodash";

import { useTranslation } from "react-i18next";
import { CustomTag } from "pages/clients&users/components/RenderColumnsTable";
import DisplayAvatar from "pages/voip/components/DisplayAvatar";
import { humanDate } from "pages/voip/helpers/helpersFunc";
import { URL_ENV } from "index";
import { useSelector, useDispatch } from "react-redux";
import {
  openFileOnOnlyOfficeFromDrive,
  canOpenInOnlyOffice,
} from "./drive-helpers/functions";
import {
  setDriveParentItems,
  setDriveBreadcrumb,
  setDriveSelectedFolder,
  setDriveTreeData,
  setDriveSearch,
  setDriveSearchInput,
  clearDriveSearch,
} from "new-redux/actions/drive.actions/drive";
import { RenameModal, DeleteModal, ShareModal, DetailsDrawer } from "./modals";
import DriveList from "./drive-list/drive-list.component";
import DriveCards from "./drive-cards/drive-cards.component";
import useLocalStorage from "../../custom-hooks/useLocalStorage";
import { FaFolder } from "react-icons/fa";
import { useSearchParams } from "react-router-dom";
import DriveIcons from "./drive-icons";
import DriveTree from "./drive-tree/drive-tree.component";
import SharedUsersPopover from "./drive-list/SharedUsersPopover.component";

const DriveComponent = () => {
  const dispatch = useDispatch();
  const [t] = useTranslation("common");
  const parent = useSelector((state) => state.drive.parentItem);
  const breadcrumb = useSelector((state) => state.drive.breadcrumb);
  const search = useSelector((state) => state.drive.search);
  const searchInput = useSelector((state) => state.drive.searchInput);
  const user = useSelector((state) => state.user.user);
  
  const queryClient = useQueryClient();
  const [isRenameModalVisible, setIsRenameModalVisible] = useState(false);
  const [itemToRename, setItemToRename] = useState(null);
  const [renameForm] = Form.useForm();
  const [searchParams, setSearchParams] = useSearchParams();
  const [pagination, setPagination] = useState(1);
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const [itemToDelete, setItemToDelete] = useState(null);
  const [isShareModalVisible, setIsShareModalVisible] = useState(false);
  const [itemToShare, setItemToShare] = useState(null);
  const [isDetailsDrawerVisible, setIsDetailsDrawerVisible] = useState(false);
  const [itemToShowDetails, setItemToShowDetails] = useState(null);
  const [draggedItem, setDraggedItem] = useState(null);
  // Drag and drop states
  const [isDragOver, setIsDragOver] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadingFiles, setUploadingFiles] = useState([]);
  const [abortController, setAbortController] = useState(null);
  const [viewMode, setViewMode] = useLocalStorage("driveViewMode", "table");

  useEffect(() => {
    const id = searchParams.get("id");

    id && setIsDetailsDrawerVisible(true)
  }, [searchParams]);

  const { data, isLoading, isError, isFetching, refetch } = useQuery({
    queryKey: [["drive-items"], pagination, parent, search],
    queryFn: ({ signal }) =>
      getDriveItem(signal, {
        search: search,
        parent: parent,
        limit: 10,
        page: pagination,
      }),
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    retry: false,
    refetchOnReconnect: false,
  });

  // Handle different response structure for search vs normal browse
  const processedData = React.useMemo(() => {
    if (!data) return null;

    // If search is active and response has files/images structure
    if (search && (data.files || data.images)) {
      const files = data.files || [];
      const images = data.images || [];

      // Get the first 12 images that will be shown in suggested images section
      const suggestedImages = images.slice(0, 12);
      const suggestedImageIds = new Set(suggestedImages.map((img) => img.id));

      // Filter out suggested images from the main list to avoid duplicates
      const filteredImages = images.filter(
        (img) => !suggestedImageIds.has(img.id)
      );
      const combinedItems = [...files, ...filteredImages];

      // Sort combined items by name for better UX
      combinedItems.sort((a, b) => {
        // Folders first, then files
        if (a.type === "folder" && b.type !== "folder") return -1;
        if (a.type !== "folder" && b.type === "folder") return 1;
        // Then alphabetically by name
        return (a.name || "")
          .toLowerCase()
          .localeCompare((b.name || "").toLowerCase());
      });

      return {
        data: combinedItems, // Main list without the suggested images
        images: images, // Keep all images for suggested images section
        files: files, // Keep files separately if needed
        meta: data.meta || {
          current_page: pagination,
          total: combinedItems.length,
          per_page: 10,
          last_page: Math.ceil(combinedItems.length / 10),
        },
      };
    }

    // Normal response structure
    return data;
  }, [data, search, pagination]);

  const deleteMutation = useMutation({
    mutationFn: (itemId) => {
      return deleteDriveItem(itemId);
    },
    onSuccess: ({data}) => {
     queryClient.invalidateQueries(["drive-storage-info"], { exact: false });
     data.data.type === "folder" && queryClient.invalidateQueries(["drive-tree"], { exact: false });

      refetch();
      message.success(t("drive.itemDeletedSuccessfully"));
    },
    onError: (error) => {
      console.error("Delete error:", error);
      message.error(t("drive.failedToDeleteItem"));
    },
  });

  const renameMutation = useMutation({
    mutationFn: (params) => {
      return updateDriveItem(params.id, {
        name: params.name,
      });
    },
    onSuccess: (data) => {
      setIsRenameModalVisible(false);
      data.data.type === "folder" && queryClient.invalidateQueries(["drive-tree"], { exact: false });
      refetch();
      message.success(t("drive.itemRenamedSuccessfully"));
    },
    onError: (error) => {
      console.error("Rename error:", error);
      message.error(t("drive.failedToRenameItem"));
    },
  });

  const downloadMutation = useMutation({
    mutationFn: ({ itemId, itemName }) => {
      return getFile(itemId).then((response) => ({ ...response, itemName }));
    },
    onSuccess: (response) => {
      const blob = new Blob([response.data], {
        type: response.headers["content-type"] || "application/octet-stream",
      });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;

      // Get filename from response headers with multiple fallback methods
      let filename = response.itemName || "download"; // Use item name as primary fallback
      const contentDisposition = response.headers["content-disposition"];

      if (contentDisposition) {
        // Try different patterns for filename extraction
        const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
        const matches = filenameRegex.exec(contentDisposition);
        if (matches != null && matches[1]) {
          filename = matches[1].replace(/['"]/g, "");
        } else {
          // Try filename* pattern (RFC 5987)
          const filenameStarRegex = /filename\*=UTF-8''(.+)/;
          const starMatches = filenameStarRegex.exec(contentDisposition);
          if (starMatches != null && starMatches[1]) {
            filename = decodeURIComponent(starMatches[1]);
          }
        }
      }

      link.setAttribute("download", filename);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      message.success(t("drive.fileDownloadedSuccessfully"));
    },
    onError: (error) => {
      console.error("Download error:", error);
      message.error(t("drive.failedToDownloadFile"));
    },
  });

  // Upload mutation for drag and drop
  const uploadMutation = useMutation({
    mutationFn: ({ formData, config }) => uploadFileDrive(formData, config),
    onSuccess: () => {
      queryClient.invalidateQueries([["drive-items"]], { exact: false });
      message.success(t("drive.filesUploadedSuccessfully"));
      resetUploadState();
      refetch();
    },
    onError: (error) => {
      console.error("Upload error:", error);
      if (error.name !== "CanceledError") {
        message.error(t("drive.failedToUploadFiles"));
      } else {
        message.info(t("drive.uploadCanceled"));
      }
      resetUploadState();
    },
  });

  const resetUploadState = () => {
    setIsUploading(false);
    setUploadProgress(0);
    setUploadingFiles([]);
    setAbortController(null);
  };

  const cancelUpload = () => {
    if (abortController) {
      abortController.abort();
      resetUploadState();
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (!isDragOver) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    // Only hide drag overlay if we're leaving the main container
    if (!e.currentTarget.contains(e.relatedTarget)) {
      setIsDragOver(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileUpload(files);
    }
  };
  const handleOpenFile = async (fileId) => {
    try {
      await openFileOnOnlyOfficeFromDrive(fileId, t);
    } catch (error) {
      // Error handling is already done in the helper function
    }
  };

  const handleFileUpload = (files) => {
    // Create abort controller for cancellation
    const controller = new AbortController();
    setAbortController(controller);

    setIsUploading(true);
    setUploadingFiles(files);
    setUploadProgress(0);

    const uploadData = new FormData();
    files.forEach((file) => {
      uploadData.append("file[]", file);
    });
    !!parent && uploadData.append("parent", parent || "");
    uploadData.append("type", "file");

    const config = {
      onUploadProgress: (progressEvent) => {
        const progress = Math.round(
          (progressEvent.loaded / progressEvent.total) * 100
        );
        setUploadProgress(progress);
      },
      signal: controller.signal,
    };

    uploadMutation.mutate({ formData: uploadData, config });
  };

  const handlePageChange = (page) => {
    setPagination(page);
  };

  // Debounced search handler using lodash
  const debouncedSearch = useCallback(
    debounce((value) => {
      dispatch(setDriveSearch(value));
      setPagination(1); // Reset to first page when searching
    }, 700),
    [dispatch]
  );

  const handleSearchChange = (value) => {
    dispatch(setDriveSearchInput(value));
    debouncedSearch(value);
  };

  const handleSearchClear = () => {
    dispatch(clearDriveSearch());
    setPagination(1);
    debouncedSearch.cancel(); // Cancel any pending debounced calls
  };

  const handleDeleteItem = (record) => {
    setItemToDelete(record);
    setIsDeleteModalVisible(true);
  };

  const confirmDelete = () => {
    if (itemToDelete) {
      deleteMutation.mutate(itemToDelete.id);
      setIsDeleteModalVisible(false);
      setItemToDelete(null);
    }
  };

  const cancelDelete = () => {
    setIsDeleteModalVisible(false);
    setItemToDelete(null);
  };

  const showRenameModal = (record) => {
    setItemToRename(record);
    renameForm.setFieldsValue({ name: record.name });
    setIsRenameModalVisible(true);
  };

  const handleRename = (values) => {
    if (!itemToRename) return;

    renameMutation.mutate({
      id: itemToRename.id,
      name: values.name,
      type: itemToRename.type,
    });
  };

  const renderMenuItems = (item) => {
    const menuItems = [
      {
        key: "share",
        label: t("drive.share"),
        icon: <ShareAltOutlined />,
        onClick: (e) => {
          e.domEvent.stopPropagation();
          showShareModal(item);
        },
      },
      {
        key: "details",
        label: t("drive.details"),
        icon: <InfoCircleOutlined />,
        onClick: (e) => {
          e.domEvent.stopPropagation();
          showDetailsDrawer(item);
        },
      },
      {
        key: "download",
        label: t("drive.download"),
        icon: <DownloadOutlined />,
        onClick: (e) => {
          e.domEvent.stopPropagation();
          handleDownload(item);
        },
        disabled: item.type !== "file",
      },
      {
        key: "rename",
        label: t("drive.rename"),
        icon: <EditOutlined />,
        onClick: (e) => {
          e.domEvent.stopPropagation();
          showRenameModal(item);
        },
      },
      {
        type: "divider",
      },
      {
        key: "delete",
        label: t("drive.delete"),
        icon: <DeleteOutlined />,

        onClick: (e) => {
          e.domEvent.stopPropagation();
          handleDeleteItem(item);
        },
        danger: true,
      },
    ];

    if (item.type === "file" && canOpenInOnlyOffice(item.extension)) {
      menuItems.splice(2, 0, {
        key: "open",
        label: t("drive.open"),
        icon: <FileOutlined />,
        onClick: (e) => {
          e.domEvent.stopPropagation();
          handleOpenFile(item.id);
        },
      });
    }

    return menuItems;
  };

  const columns = [
    {
      key: "name",
      title: t("drive.name"),
      width: 120,
      fixed: "left",
      render: (text, record) => {
        const actionItems = renderMenuItems(record);

        return (
          <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3 ">
            {getIcon(record)}
            <SharedUsersPopover itemId={record?.id} itemName={record?.name}>
              <div className="flex items-center gap-1 max-w-[160px]">
                <span className="cursor-pointer truncate hover:text-blue-600">
                  {record?.name}
                </span>
                {record?.is_shared && (
                  <ShareAltOutlined 
                    className="text-blue-500 text-xs flex-shrink-0" 
                    title={t("tasks.shared") || "Shared"}
                  />
                )}
              </div>
            </SharedUsersPopover>
          </div>
            <Dropdown
              menu={{ items: actionItems }}
              trigger={["click"]}
              placement="bottomRight"
              onClick={(e) => e.stopPropagation()}
            >
              <Button
                type="text"
                icon={<MoreOutlined />}
                className="flex items-center justify-center"
                onClick={(e) => e.stopPropagation()}
              />
            </Dropdown>
          </div>
        );
      },
      dataIndex: "name",
    },
    {
      key: "own",
      title: t("drive.owner"),
      width: 60,
      dataIndex: "created_by",
      render: (text, record) => {
        return (
          <CustomTag
            content={record?.created_by?.label}
            maxChars={35}
            color="gray"
            avatar={
              <DisplayAvatar
                name={record?.created_by?.label}
                size={22}
                urlImg={
                  !!record?.created_by?.avatar &&
                  URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                    record?.created_by?.avatar
                }
              />
            }
          />
        );
      },
    },
    {
      key: "created_at",
      title: t("drive.createdAt"),
      width: 90,
      dataIndex: "created_at",
      render: (text, record) => {
        return <span> {humanDate(record?.created_at, t)}</span>;
      },
    },

    {
      key: "updated_at",
      title: t("drive.updatedAt"),
      width: 90,
      render: (text, record) => {
        return <span>{humanDate(record?.updated_at, t)}</span>;
      },
    },
    {
      key: "type",
      title: t("drive.type"),
      dataIndex: "type",
      width: 30,
      render: (text, record) => {
        if (record.type === "folder") {
          return "Folder";
        }
        const extension = record.extension.toUpperCase();
        switch (extension) {
          case "DOC":
          case "DOCX":
            return "Document";
          case "XLS":
          case "XLSX":
            return "Excel";
          case "PDF":
            return "PDF";
          case "CSV":
            return "CSV";
          case "PNG":
            return "PNG";
          case "JPG":
          case "JPEG":
            return "JPEG";
          default:
            return extension;
        }
      },
    },
    {
      key: "size",
      title: t("drive.size"),
      dataIndex: "size",
      width: 35,
      render: (text, record) => {
        return <span>{record?.type === "file" ? record?.size : "-"}</span>;
      },
    },
    {
      key: "actions",
      title: "Actions",
      width: 45,
      render: (_, record) => {
        return (
          <div className="row-actions flex items-center space-x-2 opacity-0">
            <Button
              type="text"
              icon={<ShareAltOutlined />}
              title={t("drive.share")}
              onClick={(e) => {
                e.stopPropagation();
                showShareModal(record);
              }}
            />
            <Button
              type="text"
              icon={<InfoCircleOutlined />}
              title={t("drive.details")}
              onClick={(e) => {
                e.stopPropagation();
                showDetailsDrawer(record);
              }}
            />
            <Button
              type="text"
              icon={<DownloadOutlined />}
              title={t("drive.download")}
              onClick={(e) => {
                e.stopPropagation();
                handleDownload(record);
              }}
              disabled={record.type !== "file"}
            />
            <Button
              type="text"
              icon={<EditOutlined />}
              title={t("drive.rename")}
              onClick={(e) => {
                e.stopPropagation();
                showRenameModal(record);
              }}
            />
          </div>
        );
      },
    },
  ];

  const cardClick = (record) => {
    if (record?.type === "folder") {
      // If in search mode, show a message and clear search first
      if (search) {
        message.info(
          t("drive.clearingSearchToNavigate") ||
            "Clearing search to navigate to folder..."
        );
        handleSearchClear();

        // Add a small delay to let the search clear, then navigate
        setTimeout(() => {
          const newBreadcrumb = [
            ...breadcrumb,
            { id: record?.id, name: record?.name, path: record?.path },
          ];
          dispatch(setDriveBreadcrumb(newBreadcrumb));
          dispatch(setDriveParentItems(record?.id));
          dispatch(setDriveSelectedFolder(record?.id));
        }, 100);
        return;
      }

      // Update breadcrumb when clicking on a folder
      const newBreadcrumb = [
        ...breadcrumb,
        { id: record?.id, name: record?.name, path: record?.path },
      ];
      dispatch(setDriveBreadcrumb(newBreadcrumb));
      dispatch(setDriveParentItems(record?.id));
      dispatch(setDriveSelectedFolder(record?.id));
    }
  };
  const rowSelection = (record, index) => {
    return {
      onClick: () => {
        if (record?.type === "folder") {
          // If in search mode, show a message and clear search first
          if (search) {
            message.info(
              t("drive.clearingSearchToNavigate") ||
                "Clearing search to navigate to folder..."
            );
            handleSearchClear();

            // Add a small delay to let the search clear, then navigate
            setTimeout(() => {
              const newBreadcrumb = [
                ...breadcrumb,
                { id: record?.id, name: record?.name, path: record?.path },
              ];
              dispatch(setDriveBreadcrumb(newBreadcrumb));
              setPagination(1);
              dispatch(setDriveParentItems(record?.id));
              dispatch(setDriveSelectedFolder(record?.id));
            }, 100);
            return;
          }

          // Update breadcrumb when clicking on a folder row
          const newBreadcrumb = [
            ...breadcrumb,
            { id: record?.id, name: record?.name, path: record?.path },
          ];
          dispatch(setDriveBreadcrumb(newBreadcrumb));
          setPagination(1);
          dispatch(setDriveParentItems(record?.id));
          dispatch(setDriveSelectedFolder(record?.id));
        }
      },
      className: "cursor-pointer hover:bg-gray-50", // Add cursor-pointer class to each row
    };
  };

  useEffect(() => {
    if (breadcrumb.length === 1 && breadcrumb[0]?.name === "My Drive") {
      dispatch(
        setDriveBreadcrumb([{ id: "", name: t("drive.myDrive"), path: "" }])
      );
    }
  }, [t]);

  useEffect(() => {
    return () => {
      // Cleanup debounced search
      debouncedSearch.cancel();

      dispatch(setDriveParentItems(""));
      dispatch(
        setDriveBreadcrumb([{ id: "", name: t("drive.myDrive"), path: "" }])
      );
      dispatch(setDriveSelectedFolder(""));
      dispatch(setDriveTreeData([]));
      queryClient.removeQueries(["drive-items"], { exact: false });
      queryClient.removeQueries(["drive-tree"], { exact: false });
      queryClient.removeQueries(["drive-storage-info"], { exact: false });
      };
  }, [debouncedSearch]);

  const showShareModal = (record) => {
    setItemToShare(record);
    setIsShareModalVisible(true);
  };

  const handleShare = (shareData) => {
    message.success(
      `Shared "${shareData.item?.name}" with ${shareData.users.length} user(s) as ${shareData.permission}`
    );

    setIsShareModalVisible(false);
    setItemToShare(null);
  };

  const cancelShare = () => {
    setIsShareModalVisible(false);
    setItemToShare(null);
  };

  const showDetailsDrawer = (item) => {
    setItemToShowDetails(item.id);
    setIsDetailsDrawerVisible(true);
  };

  const hideDetailsDrawer = () => {
    setSearchParams({});
    setIsDetailsDrawerVisible(false);
    setItemToShowDetails(null);
  };

  const handleDownload = (record) => {
    if (record.type === "file") {
      downloadMutation.mutate({
        itemId: record.id,
        itemName: record.name,
      });
    } else {
      message.info(t("drive.canOnlyDownloadFiles"));
    }
  };
  const mutationMove = useMutation({
    mutationFn: ({ draggedItem, targetFolder }) =>
      moveDriveItem({ draggedItem, targetFolder }),
    onSuccess: () => {
      queryClient.invalidateQueries([["drive-items"]], { exact: false });
      queryClient.invalidateQueries(["drive-tree"], { exact: false });
      resetUploadState();
      refetch();
    },
  });

  const handleMoveItem = (draggedItem, targetFolder) => {
    mutationMove.mutate({
      draggedItem: draggedItem.id,
      targetFolder: targetFolder.id,
    });
    message.success(
      t("drive.itemMovedSuccessfully", {
        itemName: draggedItem.name,
        folderName: targetFolder.name,
      })
    );
  };

  return (
    <div className=" h-full w-full overflow-auto">
      <div className=" flex h-full w-full overflow-auto">
        <div className="h-full w-[20%] min-w-[200px] bg-slate-100">
          <DriveTree setPagination={setPagination} />
        </div>

        {/* Right content area with drag and drop */}
        <div
          className="relative w-[100%] min-w-[200px] p-4 [&_.ant-spin-container]:h-full [&_.ant-spin-nested-loading]:h-full"
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          {/* Drag overlay */}
          {isDragOver && draggedItem == null && (
            <div className="absolute inset-0 z-50 flex items-center justify-center rounded-lg border-2 border-dashed border-blue-400 bg-blue-50 bg-opacity-90">
              <div className="text-center">
                <div className="mb-4">
                  <CloudUploadOutlined className="text-6xl text-blue-500" />
                </div>
                <h3 className="mb-2 text-xl font-semibold text-blue-700">
                  {t("drive.dropFilesHere")}
                </h3>
                <p className="text-blue-600">{t("drive.releaseToUpload")}</p>
              </div>
            </div>
          )}

          <Spin
            className="flex h-full w-full flex-col items-center justify-center"
            spinning={isFetching}
          >
            <div className="mb-4 flex flex-col gap-2">
              {/* Breadcrumb */}
              <div className="flex flex-1 items-center justify-between">
                <Breadcrumb
                  items={breadcrumb.map((item, index) => ({
                    title:
                      index === 0 ? (
                        <span
                          onClick={() => {
                            handleSearchClear();
                            dispatch(setDriveParentItems(""));
                            dispatch(
                              setDriveBreadcrumb([
                                { id: "", name: t("drive.myDrive"), path: "" },
                              ])
                            );
                            dispatch(setDriveSelectedFolder(""));
                          }}
                          className="cursor-pointer text-lg font-medium "
                        >
                          {item.name}
                        </span>
                      ) : (
                        <span
                          onClick={() => {
                            handleSearchClear();
                            dispatch(setDriveParentItems(item.id));
                            dispatch(setDriveSelectedFolder(item.id));
                            // Update breadcrumb to show only up to the clicked item
                            dispatch(
                              setDriveBreadcrumb(breadcrumb.slice(0, index + 1))
                            );
                          }}
                          className="cursor-pointer text-lg font-medium "
                        >
                          {item.name}
                        </span>
                      ),
                  }))}
                  separator={<span className="mx-2 text-lg">›</span>}
                />
              </div>

              {/* Search and View Controls */}
              <div className="flex items-center justify-between gap-4">
                <div className="max-w-md flex-1">
                  <Input
                    placeholder={
                      t("drive.searchFiles") || "Search files and folders..."
                    }
                    value={searchInput}
                    onChange={(e) => handleSearchChange(e.target.value)}
                    prefix={<SearchOutlined className="text-gray-400" />}
                    suffix={isFetching && search ? <Spin size="small" /> : null}
                    allowClear
                    className="rounded-lg"
                    size="large"
                    style={{
                      borderRadius: "8px",
                      boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)",
                      ...(search && {
                        borderColor: "#1890ff",
                        boxShadow: "0 0 0 2px rgba(24, 144, 255, 0.2)",
                      }),
                    }}
                  />
                </div>

                <Radio.Group
                  value={viewMode}
                  onChange={(e) => setViewMode(e.target.value)}
                >
                  <Radio.Button
                    value="table"
                    style={{ borderRadius: "8px 0 0 8px" }}
                  >
                    <UnorderedListOutlined />
                  </Radio.Button>
                  <Radio.Button
                    value="cards"
                    style={{ borderRadius: "0 8px 8px 0" }}
                  >
                    <AppstoreOutlined />
                  </Radio.Button>
                </Radio.Group>
              </div>

              {/* Suggested Images Section */}
              {search &&
                processedData?.images &&
                processedData.images.length > 0 && (
                  <div>
                    <div className="mb-2  flex items-center gap-1">
                      <span className="text-sm font-medium text-gray-700">
                        Images
                      </span>
                      <Tooltip
                        title={
                          t("drive.aiImageSearchDescription") ||
                          "Images found using AI-powered semantic search"
                        }
                        placement="top"
                      >
                        <InfoCircleOutlined className="mt-1 cursor-help text-xs text-blue-500 transition-colors hover:text-blue-600" />
                      </Tooltip>
                    </div>

                    <div className="rounded-lg border border-gray-200 bg-white ">
                      <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6">
                        {processedData.images.slice(0, 12).map((image) => (
                          <div
                            key={image.id}
                            className="group relative aspect-square cursor-pointer overflow-hidden rounded-lg border border-gray-200 transition-all duration-200 hover:border-blue-400"
                            onClick={() => {
                              if (image.type === "folder") {
                                cardClick(image);
                              }
                            }}
                          >
                            <Image
                              src={`${image.thumbnail_url}`}
                              alt={image.name}
                              className="h-full w-full object-cover"
                              preview={{
                                destroyOnHidden: true,
                                mask: (
                                  <div className="flex items-center justify-center">
                                    <EyeOutlined className="text-lg text-white" />
                                  </div>
                                ),
                              }}
                              fallback="data:image/png;base64,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"
                            />

                            {/* Overlay with image name and actions */}
                            <div className="absolute inset-0 flex items-end bg-black bg-opacity-0 transition-all duration-200 group-hover:bg-opacity-50">
                              <div className="w-full bg-gradient-to-t from-black/70 to-transparent p-2 opacity-0 transition-opacity duration-200 group-hover:opacity-100">
                                <p className="truncate text-xs font-medium text-white">
                                  {image.name}
                                </p>
                                <div className="mt-1 flex items-center justify-between">
                                  <span className="text-xs text-white/80">
                                    {image.size}
                                  </span>
                                  <div className="flex gap-1">
                                    <Button
                                      type="text"
                                      size="small"
                                      icon={<DownloadOutlined />}
                                      className="p-1 text-white hover:text-blue-400"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleDownload(image);
                                      }}
                                    />
                                    <Button
                                      type="text"
                                      size="small"
                                      icon={<EyeOutlined />}
                                      className="p-1 text-white hover:text-blue-400"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        // Trigger image preview
                                        const imageElement = e.target
                                          .closest(".group")
                                          .querySelector(".ant-image");
                                        if (imageElement) {
                                          imageElement.click();
                                        }
                                      }}
                                    />
                                    <Button
                                      type="text"
                                      size="small"
                                      icon={<ShareAltOutlined />}
                                      className="p-1 text-white hover:text-blue-400"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        showShareModal(image);
                                      }}
                                    />
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>

                      {processedData.images.length > 5 && (
                        <div className="mt-4 text-center">
                          <Button
                            type="link"
                            className="text-blue-600 hover:text-blue-800"
                            onClick={() => {
                              // Scroll to main list or show more images
                              message.info(
                                t("drive.scrollDownForMore") ||
                                  "Scroll down to see more results in the main list"
                              );
                            }}
                          >
                            {t("drive.viewAllImages", {
                              count: processedData.images.length,
                            }) ||
                              `View all ${processedData.images.length} images`}
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                )}

              {/* Visual separator between suggested images and main content */}
              {search &&
                processedData?.images &&
                processedData.images.length > 0 && (
                  <Divider style={{ margin: "0px" }} />
                )}
            </div>

            {viewMode === "table" ? (
              <DriveList
                onPageChange={(page) => setPagination(page.current)}
                data={processedData?.data}
                meta={processedData?.meta}
                isFetching={isFetching}
                columns={columns}
                onRow={rowSelection}
                isError={isError}
                onMoveItem={handleMoveItem}
                draggedItem={draggedItem}
                setDraggedItem={setDraggedItem}
              />
            ) : (
              <DriveCards
                getIcon={getIcon}
                isFetching={isFetching}
                data={processedData?.data}
                showDetailsDrawer={showDetailsDrawer}
                handleDownload={handleDownload}
                showRenameModal={showRenameModal}
                handleDeleteItem={handleDeleteItem}
                showShareModal={showShareModal}
                pagination={processedData?.meta}
                onPageChange={handlePageChange}
                onCardClick={cardClick}
                renderMenuItems={renderMenuItems}
                isError={isError}
                onMoveItem={handleMoveItem}
                draggedItem={draggedItem}
                setDraggedItem={setDraggedItem}
              />
            )}
          </Spin>

          {/* Upload progress bar at bottom */}
          {isUploading && (
            <div className="fixed bottom-0 left-[20%] right-0 z-40 border-t bg-white p-4 shadow-lg">
              <Card size="small" className="mx-auto max-w-md">
                <div className="mb-3 flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <FileOutlined className="text-blue-500" />
                    <span className="text-sm font-medium">
                      {t("drive.uploading")} {uploadingFiles.length}{" "}
                      {uploadingFiles.length === 1
                        ? t("drive.file")
                        : t("drive.files")}
                    </span>
                  </div>
                  <Button
                    type="text"
                    size="small"
                    icon={<CloseOutlined />}
                    onClick={cancelUpload}
                    danger
                  />
                </div>

                <div className="mb-2">
                  <div className="mb-1 flex justify-between text-xs">
                    <span>
                      {uploadProgress}% {t("drive.completed")}
                    </span>
                    <span>
                      {formatFileSize(
                        uploadingFiles.reduce(
                          (total, file) => total + file.size,
                          0
                        )
                      )}
                    </span>
                  </div>
                  <Progress
                    percent={uploadProgress}
                    size="small"
                    status={uploadProgress < 100 ? "active" : "success"}
                  />
                </div>

                <div className="truncate text-xs text-gray-500">
                  {uploadingFiles.map((file) => file.name).join(", ")}
                </div>
              </Card>
            </div>
          )}
        </div>
      </div>

      {isRenameModalVisible && (
        <RenameModal
          isVisible={isRenameModalVisible}
          onCancel={() => setIsRenameModalVisible(false)}
          onSubmit={handleRename}
          itemToRename={itemToRename}
          form={renameForm}
          loading={renameMutation?.isLoading}
        />
      )}

      <DeleteModal
        isVisible={isDeleteModalVisible}
        onCancel={cancelDelete}
        onConfirm={confirmDelete}
        itemToDelete={itemToDelete}
        loading={deleteMutation?.isLoading}
      />

      {isShareModalVisible && (
        <ShareModal
          key={itemToShare?.id}
          isVisible={isShareModalVisible}
          onCancel={cancelShare}
          onShare={handleShare}
          itemToShare={itemToShare}
          loading={false}
        />
      )}

      {isDetailsDrawerVisible && (
        <DetailsDrawer
          getIcon={getIcon}
          isVisible={isDetailsDrawerVisible}
          onClose={hideDetailsDrawer}
          itemId={itemToShowDetails || searchParams.get("id")}
        />
      )}
    </div>
  );
};

export const getIcon = (record, size = "25px") => {
  if (record?.type === "folder") {
    return <FaFolder style={{ fontSize: size }} />;
  }
  const extension = record?.extension?.toUpperCase();
  switch (extension) {
    case "DOC":
    case "DOCX":
    case "WORD":
      return <DriveIcons.WordIcon width={size} height={size} />;
    case "XLS":
    case "XLSX":
      return <DriveIcons.ExcelIcon width={size} height={size} />;
    case "PDF":
      return <DriveIcons.PdfIcon width={size} height={size} />;
    case "CSV":
      return <DriveIcons.CsvIcon width={size} height={size} />;
    case "PNG":
    case "WEBP":
    case "JPG":
    case "JPEG":
      return <DriveIcons.ImageIcon width={size} height={size} />;
    case "PPT":
    case "PPTX":
      return <DriveIcons.PowerPointIcon width={size} height={size} />;
    case "EXE":
      return <DriveIcons.EXEIcon width={size} height={size} />;
    case "ZIP":
    case "RAR":
      return <DriveIcons.ZipIcon width={size} height={size} />;
    case "TXT":
      return <DriveIcons.TxtIcon width={size} height={size} />;
    default:
      return <DriveIcons.FileIcon width={size} height={size} />;
  }
};

export default DriveComponent;
