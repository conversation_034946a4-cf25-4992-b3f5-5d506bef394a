import { <PERSON><PERSON>, <PERSON>, Tooltip, <PERSON>, Dropdown, Modal, Badge } from "antd";
import { useCallback, useEffect, useMemo, useState } from "react";
import { AiOutlineStar, AiFillStar, AiOutlineDelete } from "react-icons/ai";

import { DeleteOutlined } from "@ant-design/icons";
import moment from "moment/moment";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import MarkDown from "../Markdown";
import MainService from "../../../../services/main.service";
import { toastNotification } from "../../../../components/ToastNotification";
import CreateTask from "../../../voip/components/CreateTask";
import FormCreate from "../../../clients&users/components/FormCreate";
import useDebounce from "../../../components/UseDebounce/UseDebounce";
import dayjs from "dayjs";
import {
  ReadUnreadMessages,
  SeenMessage,
  StarredMessage,
  TrashListMessages,
} from "../services/ActionsApi";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import { setPage, setPageSize } from "new-redux/actions/mail.actions";
import ActionsStarredImportant from "../actionsStarredImportant";
import { formatDateForDisplay } from "pages/tasks/helpers/formatDateToDisplay";
import DropdownActionsTable from "../dropdownActionsTable";
import Log from "../components/Log";
import "../mailing.css";
import { checkAccessRoleAccount } from "../mailing";

const Starred = ({ setDetailsMail, notification, dataAccounts, refresh }) => {
  const [clickStar, setClickStar] = useState(false);

  // const [page, setPage] = useState(1);
  const [error, setError] = useState(false);

  const [openForm, setOpenForm] = useState(false);
  const [familyId, setFamilyId] = useState(null);
  const [openTask, setOpenTask] = useState(false);
  const [titleTask, setTiltleTask] = useState("");
  const [dataMailStarred, setDataMailStarred] = useState([]);
  const [metaMailStarred, setMetaMailStarred] = useState({});
  const [loading, setLoading] = useState({ state: false, type: null });
  const [openModal, setOpenModal] = useState(false);
  const [EmailId, setEmailId] = useState("");
  const [thirdid, setThirdId] = useState("");
  const [clickArchive, setClickArchive] = useState(false);
  const [typeDelete, setTypeDelete] = useState("");
  const [openLogDrawer, setOpenLogDrawer] = useState(null);
  const [t] = useTranslation("common");
  const navigate = useNavigate();
  const { page, pageSize, searchEmail } = useSelector(
    (state) => state.mailReducer
  );
  const debouncedSearchValue = useDebounce(searchEmail, 500);
  const usedAccount = useMemo(
    () => dataAccounts?.find((item) => item?.selected),
    [dataAccounts]
  );
  const { user } = useSelector(({ user }) => user);
  const isToday = require("dayjs/plugin/isToday");

  dayjs.extend(isToday);

  const dispatch = useDispatch();

  const getMailsStarred = useCallback(async () => {
    // if (Object.values(metaMailInbox).length > 0 && !refresh) return;
    setLoading({ state: true, type: "mails" });

    let response = "";
    try {
      if (debouncedSearchValue.length > 0) {
        response = await MainService.searchMailsTStarred(
          usedAccount?.value,
          debouncedSearchValue,
          page,
          pageSize
        );
      } else {
        response = await MainService.getStarredEmails(
          usedAccount?.value,
          page,
          pageSize
        );
      }
      if (response?.status === 200) {
        checkAccessRoleAccount(response, navigate, t);

        setDataMailStarred(response.data.data);
        setMetaMailStarred(response.data.meta);
        setError(false);
      }
    } catch (err) {
      setError(true);
      console.log(err);
    } finally {
      setLoading(false);
    }
  }, [
    usedAccount?.value,
    debouncedSearchValue,
    page,
    refresh,
    metaMailStarred?.currentPage,
    pageSize,
  ]);

  const dataSourceStarred = dataMailStarred.map((item, i) => ({
    key: item.id,
    from: { name: item.from.name, address: item.from.address, nbr: item?.nbr },
    subject: item.subject,
    body: item.body,
    date: item.date,
    seen: item.seen,
    third_id: item.third_id,
    starred: item.starred,
    important: item.important,
    nbr: item.nbr,
  }));

  const columns = [
    {
      dataIndex: "starred",
      width: "90px",
      fixed: "left",
      render: (_, record) => (
        <ActionsStarredImportant
          record={record}
          getMails={getMailsStarred}
          usedAccount={usedAccount}
        />
      ),
    },
    {
      title: t("mailing.Inbox.To"),
      dataIndex: "from",
      width: "150px",
      fixed: "left",

      ellipsis: true,
      render: (text, record) => {
        return (
          <div className={`flex cursor-pointer items-center `}>
            <Tooltip
              placement="topLeft"
              title={text?.name?.length > 0 ? text.name : text.address}
            >
              <p
                className=" max-w-sm truncate"
                style={{ width: "80%", cursor: "pointer", marginLeft: "3px" }}
              >
                {text?.name?.length > 0 ? text.name : text.address}
              </p>
            </Tooltip>

            <div className="action-mail ">
              {" "}
              <Badge
                style={{
                  backgroundColor: "transparent",
                  border: "none",
                  outline: "none",
                  margin: 0,
                  color: "gray",
                  fontSize: 12,
                  fontWeight: record?.seen === 0 ? "bold" : "normal",
                }}
                count={record?.nbr}
              ></Badge>
              <div className="hidden items-center gap-x-1 group-hover:flex ">
                <DropdownActionsTable
                  record={record}
                  t={t}
                  conditionActions={false}
                  setOpenLogDrawer={setOpenLogDrawer}
                  usedAccount={usedAccount}
                  dataMailOutbox={dataMailStarred}
                  setDataMailOutbox={setDataMailStarred}
                  user={user}
                  setOpenTask={setOpenTask}
                  setEmailId={setEmailId}
                  setOpenModal={setOpenModal}
                  setTypeDelete={setTypeDelete}
                  getMailsInbox={getMailsStarred}
                  clickArchive={clickArchive}
                  setClickArchive={setClickArchive}
                  setThirdId={setThirdId}
                  type="drafts"
                />
              </div>
            </div>
          </div>
        );
      },
    },
    {
      title: t("mailing.Inbox.subject"),
      dataIndex: "subject",
      width: "150px",
      render: (text, record) => {
        return (
          <div style={{ cursor: "pointer" }}>
            <span
              style={{
                fontWeight: record.seen === 0 ? "bold" : "",
                cursor: "pointer",
              }}
            >
              {text?.length > 30 ? (
                // <MarkDown>{text.toString()?.substring(0, 30)}...</MarkDown>
                <span
                  dangerouslySetInnerHTML={{
                    __html: text.toString()?.substring(0, 30) + "...",
                  }}
                />
              ) : (
                // <MarkDown>{text}</MarkDown>
                <span dangerouslySetInnerHTML={{ __html: text }} />
              )}
            </span>
          </div>
        );
      },
    },
    {
      title: t("mailing.Inbox.message"),
      dataIndex: "body",
      width: "300px",
      ellipsis: true,
      render: (text, record) => {
        return (
          <div style={{ cursor: "pointer" }}>
            {/* <MarkDown>{text}</MarkDown> */}
            <span dangerouslySetInnerHTML={{ __html: text }} />
          </div>
        );
      },
    },
    {
      title: t("mailing.Inbox.date"),
      dataIndex: "date",
      width: "120px",
      render: (dateTime, record) => (
        <span
          style={{
            fontWeight: record.seen === 0 ? "bold" : "",
            cursor: "pointer",
          }}
        >
          {formatDateForDisplay(
            record.date,
            `${user?.location?.date_format} ${user?.location?.time_format}`,
            user,
            t
          )}
        </span>
      ),
    },
  ];

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  const onSelectChange = (newSelectedRowKeys) => {
    console.log("selectedRowKeys changed: ", newSelectedRowKeys);
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const toggleSelection = (record) => {
    const key = record.key;
    const newSelectedRowKeys = selectedRowKeys.includes(key)
      ? selectedRowKeys.filter((k) => k !== key)
      : [...selectedRowKeys, key];
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
    renderCell: (checked, record, index, originNode) => (
      <div className="relative">
        <Button
          style={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
          }}
          size="large"
          type="text"
          shape="circle"
          onClick={(e) => {
            e.stopPropagation();
            toggleSelection(record);
          }}
        >
          {originNode}
        </Button>
      </div>
    ),
  };
  const hasSelected = selectedRowKeys.length > 0;

  const handleMarkAsReadUnread = async (status, selectedRowKeys) => {
    const response = await ReadUnreadMessages({
      usedAccount,
      status,
      selectedRowKeys,
      setSelectedRowKeys,
      t,
      dispatch,
    });

    if (response && debouncedSearchValue?.length === 0) getMailsStarred();
  };

  const DeleteMail = async () => {
    setLoading({ state: true, type: "delete" });
    const response = await TrashListMessages({
      usedAccount,
      id: EmailId,
      setSelectedRowKeys,
      selectedRowKeys,
      setOpenModal,
      folder: "starred",
      typeDelete,
      setLoading,
      t,
    });
    if (response) getMailsStarred();
  };

  useEffect(() => {
    if (dataAccounts.length > 0) {
      getMailsStarred();
    }
  }, [getMailsStarred]);

  return (
    <div>
      {notification ? (
        <>
          {toastNotification(
            "error",
            "Veuillez configurer votre email",
            "bottomRight",
            3,
            undefined,
            1
          )}
          <div className="flex h-full flex-1 items-center  justify-center space-x-2 py-24">
            <blockquote className="flex  flex-col space-y-2 text-[#46B7CF]">
              <q className="text-5xl ">Veuillez configurer votre email</q>
              {/* <div className="text-right">-- Socrates</div> */}
            </blockquote>
          </div>
        </>
      ) : (
        <>
          {hasSelected ? (
            <div className="mb-[8px] ml-[20px] flex items-center space-x-3">
              <Button
                type="primary"
                danger
                icon={<DeleteOutlined />}
                onClick={() => {
                  setOpenModal(true);
                  setTypeDelete("multiple");
                }}
              >
                {t("mailing.DeleteButton")} ({selectedRowKeys.length}{" "}
                {selectedRowKeys.length > 1 ? "emails" : "email"})
              </Button>
              {usedAccount?.shared == 0 ? (
                <>
                  <Button
                    type="default"
                    onClick={() => handleMarkAsReadUnread(1, selectedRowKeys)}
                  >
                    {t("mailing.markRead")} ({selectedRowKeys.length})
                  </Button>

                  <Button
                    type="default"
                    onClick={() => handleMarkAsReadUnread(0, selectedRowKeys)}
                  >
                    {t("mailing.markUnread")} ({selectedRowKeys.length})
                  </Button>
                </>
              ) : null}
            </div>
          ) : null}

          <FormCreate
            open={openForm}
            setOpen={setOpenForm}
            familyId={familyId}
          />

          <CreateTask
            open={openTask}
            setOpen={setOpenTask}
            mask={false}
            source="mailing"
            object={titleTask}
          />
          {openLogDrawer !== null ? (
            <Log
              openLogDrawer={openLogDrawer}
              setOpenLogDrawer={setOpenLogDrawer}
              setLoading={setLoading}
              thirdid={thirdid}
            />
          ) : null}

          <Table
            className="mailing-custom-row"
            loading={loading.state && loading.type === "mails"}
            rowSelection={rowSelection}
            columns={columns}
            dataSource={dataSourceStarred}
            showSizeChanger={false}
            pagination={{
              current: page,
              pageSize: pageSize,
              pageSizeOptions: ["10", "20", "30"],
              total: metaMailStarred.total === 0 ? 1 : metaMailStarred.total,
              showSizeChanger: true,
              onChange: (page, pageSize) => {
                dispatch(setPage(page));
                dispatch(setPageSize(pageSize));
              },
              showTotal: (total, range) =>
                `${range[0]}-${range[1]} of ${total} items`,
            }}
            scroll={{ y: "calc(100vh - 250px)" }}
            onRow={(record) => {
              return {
                onClick: () => {
                  setDetailsMail([]);
                  navigate(
                    `/mailing/${usedAccount.value}/starred/${record.key}`
                  );
                },
              };
            }}
            locale={{
              emptyText: (
                <p
                  className={
                    error ? "mt-4 text-red-600" : "mt-4 text-[#898282]"
                  }
                >
                  {error
                    ? t("toasts.errorFetchApi")
                    : loading.state && loading.type === "mails"
                    ? "Loading ..."
                    : t("mailing.noData")}
                </p>
              ),
            }}
            rowClassName={(_, index) =>
              `${index === 5 ? "" : "clickable-row"} group`
            }
            size="small"
          />

          <Modal
            title={t("mailing.Delete")}
            open={openModal}
            // onOk={handleOk}
            onCancel={() => setOpenModal(false)}
            okButtonProps={{
              style: {
                backgroundColor: "red",
                color: "white",
                width: "70",
                border: "none",
              },
            }}
            cancelButtonProps={{
              disabled: true,
            }}
            footer={
              <div style={{ display: "flex", justifyContent: "end" }}>
                <Button key="cancel" onClick={() => setOpenModal(false)}>
                  Cancel
                </Button>
                <Button
                  key="Delete"
                  loading={loading.type === "delete" && loading.state}
                  style={{
                    backgroundColor: "red",
                    color: "white",
                  }}
                  onClick={DeleteMail}
                >
                  {t("mailing.DeleteButton")}
                </Button>
              </div>
            }
          >
            <p>
              {typeDelete === "simple" || selectedRowKeys.length === 1
                ? t("mailing.DeleteMail")
                : t("mailing.DeleteMails") +
                  " " +
                  selectedRowKeys.length +
                  " " +
                  "emails ?"}
            </p>
          </Modal>
        </>
      )}
    </div>
  );
};

export default Starred;
