import "../index.css";

function FlipFlopCard({
  frontContents,
  backContents,
  isFlipped,
  frontCardHeight,
  backCardHeight,
}) {
  return (
    <div
      className={`flip-card flex-1 ${isFlipped ? "flipped" : ""}`}
      style={{
        height: isFlipped ? backCardHeight : frontCardHeight,
      }}
    >
      <div className="flip-card-inner">
        <div className="flip-card-front">{frontContents}</div>
        <div className="flip-card-back">{backContents}</div>
      </div>
    </div>
  );
}

export default FlipFlopCard;
