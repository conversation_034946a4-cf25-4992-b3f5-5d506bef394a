import { useState } from "react";
import ModalConfirm from "./ModalConfirm";
import { Form, Radio } from "antd";
import { lang } from "translations/lang";
import { useTranslation } from "react-i18next";
import { generateAxios } from "services/axiosInstance";
import { URL_ENV } from "index";
import { useDispatch } from "react-redux";
import { SET_USER_INFOS } from "new-redux/constants";
import { useSelector } from "react-redux";
import { toastNotification } from "components/ToastNotification";

const oldLangue = localStorage.getItem("language");

function ChangeLangModal({ open, setOpen }) {
  const [loading, setLoading] = useState(false);
  const { i18n, t } = useTranslation("common");
  const [default_language, setDefault_language] = useState(i18n.language);
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state?.user);

  const onSubmit = async () => {
    try {
      setLoading(true);
      const response = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).post("localizations/update", { default_language });

      dispatch({
        type: SET_USER_INFOS,
        payload: {
          ...user,
          location: response.data.data,
        },
      });
      setOpen(false);

      i18n.changeLanguage(default_language);
      localStorage.setItem("language", default_language);
      toastNotification(
        "success",
        `${t("menu2.localisation")} ${t("toasts.updated")}`,
        "topRight"
      );
    } catch (err) {
      setLoading(false);
      toastNotification("error", t("toasts.errorFetchApi"), "topRight");
    }
  };
  const onCancel = () => {
    setOpen(false);
    i18n.changeLanguage(oldLangue);
    localStorage.setItem("language", oldLangue);
  };
  return (
    <ModalConfirm
      open={open}
      title={t("localisation.titleChangeLangue")}
      content={
        <div className="w-full">
          <p
            dangerouslySetInnerHTML={{
              __html: t("localisation.contentChangeLangue", {
                lang: lang.find((item) => item.value === i18n.language)?.label,
              }),
            }}
            className="my-3 text-sm text-gray-600"></p>

          <div className="my-3 w-full text-center">
            <Radio.Group
              defaultValue={default_language}
              onChange={(e) => setDefault_language(e.target.value)}>
              {lang.map((item, index) => (
                <Radio value={item.value} key={index}>
                  {item.label}
                </Radio>
              ))}
            </Radio.Group>
          </div>
        </div>
      }
      closable={false}
      loading={loading}
      onCancel={onCancel}
      onOk={onSubmit}
      cancelText={t("localisation.cancel")}
      okText={t("localisation.sumbit2")}
      width={500}
    />
  );
}

export default ChangeLangModal;
