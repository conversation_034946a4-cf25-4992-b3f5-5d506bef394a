import axios from "axios";
import { generateAxios } from "../../../services/axiosInstance";
import WhatsApp from "../../../assets/WhatsApp.svg";
import Messenger from "../../../assets/messenger.svg";
import Instagram from "../../../assets/instagram.png";
import Livechat from "../../../assets/livechat.svg";

import { toastNotification } from "../../../components/ToastNotification";
import {
  GET_DATA,
  SET_LOADING,
  SET_RMC,
  SET_TASKS,
  SET_VOIP_DASHBOARD,
  SET_EMAIL_DASHBOARD,
  SET_SELECTED_MAIL,
  GET_STATS_CHAT,
  SET_SELECTED_QUEUE,
  SET_QUEUE_DASHBOARD,
  SET_STATS_TASKS_DASHBOARD,
  SET_DATE_DASHBOARD,
  SET_SELECTED_PIPELINE_IN_TICKET,
  SET_PIPELINES_TICKET,
  SET_SELECTED_ACOUNT_IN_EMAIL,
  SET_PIPELINES_LEAD,
  SET_SELECTED_PIPELINE_IN_LEAD,
  SET_SELECTED_PIPELINE_IN_CONTACT,
  SET_PIPELINES_CONTACT,
  ADD_TASK_IN_DASHBOARD,
  REMOVE_TASK_IN_DASHBOARD,
  UPDATE_TASK_IN_DASHBOARD,
  SET_STATS_DEALS,
  RESET_DATA,
  SET_NAMES_QUEUES,
  SET_SELECTED_DEP_RMC,
  SET_STATS_TICKETS,
  SET_STATS_LEADS_CONTACTS,
} from "../../constants";
import MainService from "../../../services/main.service";
import dayjs from "dayjs";
import { URL_ENV } from "index";
import { MailOutlined } from "@ant-design/icons";
import { store } from "new-redux/store";
import i18next from "i18next";
import { getConfigQueuesAndGroups } from "pages/voip/services/services";
import i18n from "translations/i18n";

const getDataRmc = async (name, date_start, date_end) => {
  const dataRmc = new URLSearchParams();
  dataRmc.append("token", "c1adab37bcb62f2a7f99b675c2ad6c0fdc50b51e");
  dataRmc.append("function", "reports");
  dataRmc.append("name", name);
  dataRmc.append("date_start", date_start);
  dataRmc.append("date_end", date_end);

  try {
    const response = await axios.post(
      "https://rmcdemo.comunikcrm.info/comuniksocial/include/api.php",
      dataRmc,
      {
        headers: {
          "content-type": "application/x-www-form-urlencoded",
        },
      }
    );
    return response;
  } catch (error) {
    console.error(error);
  }
};

export const getDataDashboard = (payload) => async (dispatch) => {
  const { user } = await store.getState().user;
  const { selectedMail, selectedDepRmc } = store.getState().dashboardRealTime;

  dispatch({ type: SET_LOADING, payload: true });

  try {
    // if (!payload.start || !payload.end) {
    //   throw new Error("Start and end dates are required");
    // }

    const startDate =
      payload.start || dayjs().format(user?.location?.date_format);
    const endDate = payload.end || dayjs().format(user?.location?.date_format);

    // Configuration des requêtes
    const apiCalls = [
      // Appel API pour les files d'attente
      getConfigQueuesAndGroups(),
      // Appel API pour les stats d'appel
      generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).get(`/dashboard/call?date_start=${startDate}&date_end=${endDate}`),

      // Appel API pour les stats RMC
      generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).get(
        `/dashboard/rmcByDepartment?date_start=${dayjs(
          payload.start,
          user?.location?.date_format
        ).format("DD.MM.YYYY")}&date_end=${dayjs(
          payload.end,
          user?.location?.date_format
        ).format("DD.MM.YYYY")}&department_id=${payload?.departement_id || ""}`
      ),

      // Appel API pour les emails
      generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).get(`/dashboard/email?date_start=${startDate}&date_end=${endDate}`),

      // Appel API pour les départements RMC
      generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).get(`/rmc-departments`),

      // Appel API pour les familles KPI
      generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).post(`/kpi-all-families?start=${startDate}&end=${endDate}`),
    ];

    const [
      queuesResult,
      callsResult,
      rmcResult,
      emailsResult,
      departmentsResult,

      familiesResult,
    ] = await Promise.allSettled(apiCalls);

    // Gestion des réponses avec vérification
    const stats_call =
      callsResult.status === "fulfilled" ? callsResult.value.data : null;
    const stats_rmc =
      rmcResult.status === "fulfilled" ? rmcResult.value.data : [];
    const stats_emails =
      emailsResult.status === "fulfilled" ? emailsResult.value.data : [];
    const depRmc =
      departmentsResult.status === "fulfilled"
        ? departmentsResult.value.data
        : [];
    const listQueues =
      queuesResult.status === "fulfilled"
        ? queuesResult.value.data
        : { queues: {} };
    const families =
      familiesResult.status === "fulfilled" ? familiesResult.value.data : [];

    // Traitement des canaux RMC
    const defaultChannels = [
      { channel: "wa", count: 0 },
      { channel: "ig", count: 0 },
      { channel: "fb", count: 0 },
      { channel: "chat", count: 0 },
    ];

    const updatedChannels = defaultChannels.map((channel) => {
      const apiItem = Array.isArray(stats_rmc)
        ? stats_rmc.find((item) => item.channel === channel.channel)
        : null;
      return apiItem ? { ...channel, count: Number(apiItem.count) } : channel;
    });

    // Dispatch des données
    dispatch({
      type: GET_DATA,
      payload: {
        isMount: true,
        missedCalls: stats_call?.missed_call || 0,
        missedUnreturnedCalls: stats_call?.missed_calls_not_returned_today || 0,
        missedTodayCall: stats_call?.missed_today_call || 0,
        outgoingTodayCall: stats_call?.outgoing_today_call || 0,
        receivedTodayCall: stats_call?.received_today_call || 0,
        totalCalltoday: stats_call?.total_today || 0,
        existRmc: !!stats_rmc,
        receivedCalls: stats_call?.received_call || 0,
        outgoingCalls: stats_call?.outgoing_call || 0,
        selectedDepRmc: selectedDepRmc,
        // || Number(user?.department_id)
        channelsRmc: updatedChannels.map((el) => ({
          ...el,
          color: {
            wa: "#4BC557",
            fb: "#168AFF",
            ig: "#e1306c",
            chat: "#049CFF",
            em: "black",
          }[el.channel],
          icon: {
            wa: (
              <img
                src={WhatsApp}
                alt="WhatsApp"
                style={{ width: "30px", height: "30px" }}
              />
            ),
            fb: (
              <img
                src={Messenger}
                alt="messenger"
                style={{ width: "23px", height: "23px" }}
              />
            ),
            ig: (
              <img
                src={Instagram}
                alt="instagram"
                style={{ width: "23px", height: "23px" }}
              />
            ),
            chat: (
              <img
                src={Livechat}
                alt="livechat"
                style={{ width: "23px", height: "23px" }}
              />
            ),
            em: <MailOutlined />,
          }[el.channel],
        })),
        depsRmc: Array.isArray(depRmc) ? depRmc : [],
        unreadEmail: stats_emails[0]?.nb_email_received_unread || 0,
        totalEmails: stats_emails,
        selectedMail: selectedMail?.accountId
          ? stats_emails.find((el) => el?.accountId === selectedMail?.accountId)
          : stats_emails[0],
        totalUnreadEmailReceived:
          stats_emails[0]?.nb_email_received_unread || 0,
        totalUnreadEmailSent: stats_emails[0]?.nb_email_sent || 0,
        emailsReceived: stats_emails[0]?.nb_email_received || 0,
        allQueues: Object.keys(listQueues.queues || {}).map((key) => ({
          label: key,
          value: listQueues.queues[key].number,
        })),
        totalFamilies: families,
      },
    });
    dispatch({ type: SET_LOADING, payload: false });
  } catch (err) {
    console.error("Dashboard error:", err);
    dispatch({ type: SET_LOADING, payload: false });
    toastNotification(
      "error",
      err?.response?.data?.message ?? i18next.t("common:toasts.somethingWrong"),
      "topRight"
    );
  } finally {
    dispatch({ type: SET_LOADING, payload: false });
  }
};
export const getLeads_contacts = (payload) => async (dispatch) => {
  try {
    const { data } = await MainService.getStatLead_Contact({
      start: payload?.start,
      end: payload?.end,
    });
    dispatch({
      type: SET_STATS_LEADS_CONTACTS,
      payload: data?.data,
    });
  } catch (err) {}
};
export const getStatsTasks = (payload) => async (dispatch) => {
  const { user } = await store.getState().user;
  try {
    const { data } = await MainService.getStatsTaks({
      start: payload?.startDate
        ? payload.startDate
        : user?.location?.date_format
        ? dayjs().format(user?.location?.date_format)
        : dayjs().format("YYYY-MM-DD"),
      end: payload?.endDate
        ? payload.endDate
        : user?.location?.date_format
        ? dayjs().format(user?.location?.date_format)
        : dayjs().format("YYYY-MM-DD"),
    });
    dispatch({
      type: SET_STATS_TASKS_DASHBOARD,
      payload: data,
    });
  } catch (err) {}
};

export const getStatsTickets = (payload) => async (dispatch) => {
  try {
    const res = await generateAxios(
      `${URL_ENV?.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
    ).post(`/get_cardkpi_family`, {
      date_start: payload?.start,
      date_end: payload?.end,
      lang: payload.language,
      family_id: 6,
    });

    const response = await generateAxios(
      `${URL_ENV?.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
    ).post(`/get_sum_kpiclosed_family`, {
      date_start: payload?.start,
      date_end: payload?.end,
      lang: payload.language,
      family_id: 6,
    });

    dispatch({
      type: SET_STATS_TICKETS,
      payload: { cards: res.data.data, gauge: response.data },
    });
  } catch (err) {}
};
export const getStatsCall = (payload) => async (dispatch) => {
  const { user } = await store.getState().user;
  try {
    const res = await generateAxios(
      URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
    ).get(
      `/dashboard/call?date_start=${
        user?.location?.date_format
          ? dayjs().format(user?.location?.date_format)
          : dayjs().format("YYYY-MM-DD")
      }&date_end=${
        user?.location?.date_format
          ? dayjs().format(user?.location?.date_format)
          : dayjs().format("YYYY-MM-DD")
      }`,

      {
        headers: {
          "content-type": "application/x-www-form-urlencoded",
        },
      }
    );
    dispatch({
      type: SET_VOIP_DASHBOARD,
      payload: {
        missed_calls_not_returned_today: String(
          res.data?.missed_calls_not_returned_today
        ),
        missed_today_call: String(res.data?.missed_today_call),
        outgoing_today_call: String(res.data?.outgoing_today_call),
        received_today_call: String(res.data?.received_today_call),
        total_today: String(res.data?.total_today),
      },
    });
  } catch (err) {}
};
export const setTasksInHome = (payload) => ({
  type: SET_TASKS,
  payload,
});

export const UpdateTasksInDashboard = (payload) => ({
  type: UPDATE_TASK_IN_DASHBOARD,
  payload,
});

export const setSelectedMail = (payload) => ({
  type: SET_SELECTED_MAIL,
  payload,
});

export const setSelectedDepRmc = (payload) => ({
  type: SET_SELECTED_DEP_RMC,
  payload,
});

export const setSelectedQueue = (payload) => ({
  type: SET_SELECTED_QUEUE,
  payload,
});
export const getStatsChat = (payload) => async (dispatch) => {
  const { user } = await store.getState().user;
  try {
    const { data: stats_chat } = await generateAxios(
      URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
    ).get(
      `/dashboard/chat?date_start=${
        user?.location?.date_format
          ? dayjs().format(user?.location?.date_format)
          : dayjs().format("YYYY-MM-DD")
      }&date_end=${
        user?.location?.date_format
          ? dayjs().format(user?.location?.date_format)
          : dayjs().format("YYYY-MM-DD")
      }`,

      {
        headers: {
          "content-type": "application/x-www-form-urlencoded",
        },
      }
    );

    dispatch({
      type: GET_STATS_CHAT,
      payload: {
        unreadMsgOneToOne: stats_chat?.total_unread_messages,
        unreadMessagesInGroups: stats_chat?.total_unread_messages_room,
        totalMessages: stats_chat?.total_messages,
        totalunreadMessagesTags: stats_chat?.total_unread_messages_tags,
        unreadArchivedMessages: stats_chat?.total_unread_messages_archived,
      },
    });
  } catch (err) {
    return;
  }
};
export const setStatsChat = (message) => async (dispatch) => {
  const {
    unreadMsgOneToOne,
    unreadMessagesInGroups,
    totalunreadMessagesTags,
    unreadArchivedMessages,
    isMount,
  } = await store.getState().dashboardRealTime;

  const currentUser = await store.getState().chat.currentUser;
  if (!isMount) return;

  let unreadMessageUser = unreadMsgOneToOne;
  let unreadMessageGroup = unreadMessagesInGroups;
  let unreadMessagesTags = totalunreadMessagesTags;
  let unreadMessagesArchived = unreadArchivedMessages;
  const isArchived =
    Array.isArray(message?.archive) &&
    message.archive.length > 0 &&
    message.archive.includes(message?.room?.admin_id);

  const hasTags = message.message?.tags;
  const isGroupMessage = message.message.room_id;

  // Fonction pour vérifier les tags
  const checkTags = () => {
    if (!hasTags) return false;

    if (typeof message.message.tags === "string") {
      if (message.message.tags.length > 0) {
        const tagsArray = message.message.tags.split(",");
        return tagsArray.includes(currentUser?._id);
      }
      return message.message.tags === "0";
    }
    return false;
  };

  const shouldIncrementTags = checkTags();

  if (isArchived) {
    unreadMessagesArchived += 1;
    // if (shouldIncrementTags) {
    //   unreadMessagesTags += 1;
    // }
  } else if (hasTags) {
    if (shouldIncrementTags) {
      unreadMessagesTags += 1;
    }
    unreadMessageGroup += 1;
  } else if (isGroupMessage) {
    unreadMessageGroup += 1;
  } else {
    unreadMessageUser += 1;
  }

  dispatch({
    type: GET_STATS_CHAT,
    payload: {
      unreadMsgOneToOne: unreadMessageUser,
      unreadMessagesInGroups: unreadMessageGroup,
      totalMessages:
        unreadMessageUser + unreadMessageGroup + unreadMessagesTags,
      totalunreadMessagesTags: unreadMessagesTags,
      unreadArchivedMessages: unreadMessagesArchived,
    },
  });
};

export const setTasksInDashboard = (payload) => async (dispatch) => {
  dispatch({ type: SET_LOADING, payload: true });
  const { user } = await store.getState().user;
  try {
    const res = await MainService.getTasksHome({
      start: payload?.startDate
        ? payload.startDate
        : user?.location?.date_format
        ? dayjs().format(user?.location?.date_format)
        : dayjs().format("YYYY-MM-DD"),
      end: payload?.endDate
        ? payload?.endDate
        : user?.location?.date_format
        ? dayjs().format(user?.location?.date_format)
        : dayjs().format("YYYY-MM-DD"),
      module_system: "task",
    });
    dispatch(
      setTasksInHome({
        tasks: res.data?.tasks.reverse(),
        stages: res.data?.pipelines,
        iconsTasks: res.data?.task_types,
      })
    );
    dispatch({ type: SET_LOADING, payload: false });
  } catch (err) {
    console.log(err);
  }
};
export const setEmailsInDashboard = (payload) => async (dispatch) => {
  const { user } = await store.getState().user;
  const { selectedMail } = await store.getState().dashboardRealTime;

  try {
    const res = await generateAxios(
      URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
    ).get(
      `/dashboard/email?date_start=${
        user?.location?.date_format
          ? dayjs().format(user?.location?.date_format)
          : dayjs().format("YYYY-MM-DD")
      }&date_end=${
        user?.location?.date_format
          ? dayjs().format(user?.location?.date_format)
          : dayjs().format("YYYY-MM-DD")
      }`,

      {
        headers: {
          "content-type": "application/x-www-form-urlencoded",
        },
      }
    );
    const email = res.data?.find((email) => email.accountId === selectedMail);

    dispatch({
      type: SET_EMAIL_DASHBOARD,
      payload: {
        unreadEmail: email?.nb_email_received_unread,
        totalEmails: res.data,
        totalUnreadEmailReceived: email?.nb_email_received_unread,
        totalUnreadEmailSent: email?.nb_email_sent,
        emailsReceived: email?.nb_email_received,
      },
    });
  } catch (err) {}
};
export const setQueueInDashboard = (payload) => async (dispatch) => {
  const { user } = await store.getState().user;
  payload.setLoad(true);
  try {
    const res = await generateAxios(
      URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
    ).get(
      `/dashboard/queue?date_start=${
        payload?.startDate
          ? payload?.startDate
          : user?.location?.date_format
          ? dayjs().format(user?.location?.date_format)
          : dayjs().format("YYYY-MM-DD")
      }&date_end=${
        payload?.endDate
          ? payload?.endDate
          : user?.location?.date_format
          ? dayjs().format(user?.location?.date_format)
          : dayjs().format("YYYY-MM-DD")
      }&queue_num=${payload?.queue_num}`
      // {
      //   headers: {
      //     "content-type": "application/x-www-form-urlencoded",
      //   },
      // }
    );

    dispatch({
      type: SET_QUEUE_DASHBOARD,
      payload: {
        totalQueues: res?.data?.data.map((el) => ({
          ...el,
          value: el.queue_num,
          label: el.queue_name,
        })),
      },
    });
    payload.setLoad(false);
  } catch (err) {
    payload.setLoad(false);
    console.log(err);
  }
};

export const setDateDashboard = (payload) => async (dispatch) => {
  dispatch({
    type: SET_DATE_DASHBOARD,
    payload: {
      startDate: payload.startDate,
      endDate: payload.endDate,
    },
  });
  localStorage.setItem(
    "dateDashboard",
    JSON.stringify({
      startDate: payload.startDate,
      endDate: payload.endDate,
    })
  );
};
export const setSelectedPipelineInTicket = (payload) => async (dispatch) => {
  dispatch({
    type: SET_SELECTED_PIPELINE_IN_TICKET,
    payload,
  });
};
export const setSelectedPipelineInLead = (payload) => async (dispatch) => {
  dispatch({
    type: SET_SELECTED_PIPELINE_IN_LEAD,
    payload,
  });
};
export const setSelectedPipelineInContact = (payload) => async (dispatch) => {
  dispatch({
    type: SET_SELECTED_PIPELINE_IN_CONTACT,
    payload,
  });
};

export const addTaskInDashboard = (payload) => async (dispatch) => {
  dispatch({
    type: ADD_TASK_IN_DASHBOARD,
    payload,
  });
};
export const RemoveTaskInDashboard = (payload) => async (dispatch) => {
  dispatch({
    type: REMOVE_TASK_IN_DASHBOARD,
    payload,
  });
};
export const setPipelinesTicket = (payload) => async (dispatch) => {
  dispatch({
    type: SET_PIPELINES_TICKET,
    payload,
  });
};
export const setPipelinesLead = (payload) => async (dispatch) => {
  dispatch({
    type: SET_PIPELINES_LEAD,
    payload,
  });
};
export const setPipelinesContact = (payload) => async (dispatch) => {
  dispatch({
    type: SET_PIPELINES_CONTACT,
    payload,
  });
};
export const setStatsDeals = (payload) => async (dispatch) => {
  const { user } = await store.getState().user;
  try {
    const {
      data: { data },
    } = await MainService.getKpiDealsStatus(
      {
        date_start: payload?.startDate
          ? payload?.startDate
          : user?.location?.date_format
          ? dayjs().format(user?.location?.date_format)
          : dayjs().format("YYYY-MM-DD"),
        date_end: payload?.endDate
          ? payload?.endDate
          : user?.location?.date_format
          ? dayjs().format(user?.location?.date_format)
          : dayjs().format("YYYY-MM-DD"),
        pipeline_id: "",
        lang: i18n.language === "fr" ? "fr" : "en",
      }

      // {
      //   headers: {
      //     "content-type": "application/x-www-form-urlencoded",
      //   },
      // }
    );

    dispatch({
      type: SET_STATS_DEALS,
      payload: data,
    });
  } catch (err) {
    console.log(err);
  }
};
export const setSelectedAccountInEmail = (payload) => async (dispatch) => {
  dispatch({
    type: SET_SELECTED_ACOUNT_IN_EMAIL,
    payload,
  });
};
export const setResetData = (payload) => async (dispatch) => {
  dispatch({
    type: RESET_DATA,
    payload,
  });
};
export const getNamesQueues = (payload) => async (dispatch) => {
  try {
    const { data: listQueues } = await getConfigQueuesAndGroups();
    dispatch({
      type: SET_NAMES_QUEUES,
      payload:
        listQueues?.queues && typeof listQueues.queues === "object"
          ? Object.keys(listQueues?.queues).map((key) => ({
              label: key,
              value: listQueues?.queues[key].number,
            }))
          : [],
    });
    // console.log(Object.keys(listQueues?.queues)[0]);
    dispatch(
      setQueueInDashboard({
        startDate: payload?.start,
        endDate: payload?.end,
        queue_num: Object.keys(listQueues?.queues)[0],
      })
    );
  } catch (err) {}
};
