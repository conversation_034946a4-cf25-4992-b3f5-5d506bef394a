import { memo, useCallback, useEffect, useState } from "react";
import {
  Drawer,
  Tabs,
  Tooltip,
  Descriptions,
  Skeleton,
  Space,
  Divider,
  Typography,
} from "antd";
import {
  getFieldsToCreate,
  fetchDataDetails,
} from "../../clients&users/services/services";
import { toastNotification } from "../../../components/ToastNotification";
import { FormWithTabsLoader } from "../../clients&users/components/SkeletonLoader";
import { useWindowSize } from "../../clients&users/components/WindowSize";
import "../../clients&users/index.css";
import { useTranslation } from "react-i18next";
import useActionCall from "../helpers/ActionCall";
import { humanDate } from "../helpers/helpersFunc";
import RenderDescriptionDetails from "pages/clients&users/components/RenderDescriptionDetails";
import { URL_ENV } from "index";
import DisplayAvatar from "./DisplayAvatar";

const DisplayElementInfo = ({ open, setOpen, elementDetails }) => {
  const [t] = useTranslation("common");
  // const dispatch = useDispatch();
  const call = useActionCall();
  const windowSize = useWindowSize();
  //
  //
  const [dataToDisplay, setDataToDisplay] = useState([]);
  const [timestamps, setTimestamps] = useState({});
  const [isLoading, setIsLoading] = useState(true);

  // console.log({ elementDetails });
  //
  const getAllFieldsAndData = useCallback(async () => {
    try {
      setIsLoading(true);
      const fields = await getFieldsToCreate(
        elementDetails?.familyId,
        "read",
        null,
        elementDetails?.id
      );
      const fieldsValues = await fetchDataDetails(elementDetails?.id);
      const labels = fields?.data?.data;
      const values = fieldsValues?.data;
      const result = [];
      labels?.forEach((group) => {
        result.push({
          id: group?.id,
          groupeName: group?.label,
          fields: group?.fields
            ?.filter(
              (el) =>
                el.field_type !== "password" &&
                el.label !== "uuid" &&
                !el.hidden
            )
            ?.map((el) => ({
              id: el?.id,
              label: el?.alias,
              value: values[el?.id] || "-",
              type: el?.field_type,
            })),
        });
      });
      setDataToDisplay(result);
      setTimestamps({
        createdAt: values.created_at,
        createdBy: values.created_by,
        UpdatedAt: values.updated_at,
      });
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      setOpen(false);
      throw new Error(err?.message ? err.message : err);
    } finally {
      setIsLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [elementDetails?.id, elementDetails?.familyId]);

  useEffect(() => {
    if (open && elementDetails?.id && elementDetails?.familyId) {
      getAllFieldsAndData();
    }
  }, [elementDetails?.familyId, elementDetails?.id, getAllFieldsAndData, open]);
  //
  const handleCloseClick = () => {
    setDataToDisplay([]);
    setOpen(false);
  };
  //
  const familiesName = {
    1: t("contacts.company"),
    2: t("contacts.contact"),
    3: t("menu1.deals"),
    4: t("contacts.user"),
    5: t("contacts.product"),
    6: t("contacts.ticket"),
    7: t("contacts.project"),
    8: t("contacts.booking"),
    9: t("contacts.leads"),
    11: t("contacts.invoice"),
    12: "Transaction",
  };
  //
  return (
    <Drawer
      title={
        <div
          style={{
            maxWidth:
              windowSize?.width / 2.5 < 600 ? 500 : windowSize?.width / 2.8,
          }}
          className="relative flex  space-x-2"
        >
          <p className="truncate">{elementDetails?.label}</p>
          <p className="italic">({familiesName[elementDetails?.familyId]})</p>
        </div>
      }
      placement="right"
      width={windowSize?.width / 2.5 < 600 ? 600 : windowSize?.width / 2.5}
      open={open}
      onClose={handleCloseClick}
      // mask={false}
      // zIndex={findNextZIndex()}
      footer={
        isLoading ? (
          <div className="px-1 py-0.5">
            <Skeleton.Button active size="small" block />
          </div>
        ) : timestamps.createdAt && timestamps.UpdatedAt ? (
          <div className="p-1">
            <Space split={<Divider type="vertical" />}>
              <Space size="small">
                <span className="text-slate-500">
                  {t("contacts.createdBy")}
                </span>
                <DisplayAvatar
                  name={timestamps.createdBy?.label}
                  urlImg={
                    !!timestamps.createdBy?.avatar &&
                    `${
                      URL_ENV?.REACT_APP_BASE_URL +
                      URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                    }${timestamps.createdBy?.avatar}`
                  }
                  tooltip={true}
                  size={24}
                  cursor="help"
                />
              </Space>

              <span className=" text-slate-700	">
                <span className="text-slate-500">
                  {t("contacts.createdAt")}:{" "}
                </span>
                {humanDate(timestamps.createdAt, t, "table")}
              </span>
              <span className=" text-slate-700	">
                <span className="text-slate-500">
                  {t("contacts.updatedAt")}:{" "}
                </span>
                {humanDate(timestamps.UpdatedAt, t, "table")}
              </span>
            </Space>
          </div>
        ) : null
      }
    >
      {dataToDisplay?.length && !isLoading ? (
        <Tabs
          style={{ marginRight: "-23px" }}
          type="card"
          tabBarGutter={8}
          tabPosition="left"
          items={dataToDisplay?.map((group) => {
            return {
              label: (
                <Tooltip title={group?.groupeName} placement="leftBottom">
                  <div className="tooltip-container">{group?.groupeName}</div>
                </Tooltip>
              ),
              key: group?.id,
              children: (
                <div
                  className="overflow-content overflow-x-hidden"
                  style={{ maxHeight: `${windowSize.height - 155}px` }}
                >
                  <Descriptions
                    colon={false}
                    column={1}
                    key={group?.id}
                    contentStyle={{ marginTop: 6, marginBottom: 12 }}
                  >
                    {group?.fields?.map((field) => (
                      <Descriptions.Item
                        key={field?.id}
                        label={
                          <Typography.Paragraph
                            style={{
                              textTransform: "uppercase",
                              marginRight: 12,
                              color: "rgb(100, 116, 139)",
                            }}
                            ellipsis={{
                              rows: 2,
                              expandable: true,
                              defaultExpanded: false,
                              symbol: (
                                <span className="capitalize">
                                  {t("contacts.more")}
                                </span>
                              ),
                            }}
                          >
                            {field.label}
                          </Typography.Paragraph>
                        }
                      >
                        {field?.value === "-" ? (
                          "-"
                        ) : (
                          <RenderDescriptionDetails
                            type={field.type}
                            value={field.value}
                            t={t}
                            call={call}
                          />
                        )}
                      </Descriptions.Item>
                    ))}
                  </Descriptions>
                </div>
              ),
            };
          })}
        />
      ) : (
        <FormWithTabsLoader tabsNum={4} inputNum={6} />
      )}
    </Drawer>
  );
};

export default memo(DisplayElementInfo);
