import { RESET_STATE, SET_CURRENT_WINDOW } from "../constants";

const initialState = {
  currentWindow: null,
};

const helpers = (state = initialState, action) => {
  const { type, payload } = action;
  switch (type) {
    case SET_CURRENT_WINDOW:
      return {
        ...state,
        currentWindow: payload,
      };
    case RESET_STATE: {
      return initialState;
    }
    default:
      return state;
  }
};

export default helpers;
