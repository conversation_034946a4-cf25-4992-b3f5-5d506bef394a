import { <PERSON>, <PERSON>, Divide<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Typo<PERSON> } from "antd";
import React, { useEffect, useLayoutEffect, useState } from "react";
import {
  BrowserMarketShareChart,
  DonutChart2,
  <PERSON>tal<PERSON>ne<PERSON>ar<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Pie<PERSON>hartWithLegend,
} from "../ChartsDashboard";
import { generateAxios } from "services/axiosInstance";
import { URL_ENV } from "index";
import { useSelector } from "react-redux";
import { Divide } from "lucide-react";
import { InfoCircleOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { setSelectedAccountInEmail } from "new-redux/actions/dashboard.actions";
import { useDispatch } from "react-redux";
import CardStat from "../CardStat";
// Centralisation des endpoints pour éviter les erreurs
const KPI_ENDPOINTS = {
  STAT_EMAIL: "/stat-email",
  STAT_EMAIL_AFFECTATION: "/stat-email/affectation",
  STAT_EMAIL_FOLDER: "stat-email/folder",
  STAT_EMAIL_TAGS: "stat-email/tags",
  STAT_EMAIL_STATUS: "stat-email/status",
  STAT_EMAIL_ASSIGN: "stat-email/assign",
  STAT_EMAIL_PROCESSING_RATE: "stat-email/email-processing-rate",
};

// Fonction utilitaire pour l'appel d'API
const fetchApiData = async (endpoint, body, setStateCallback) => {
  try {
    const res = await generateAxios(
      `${URL_ENV?.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
    ).post(endpoint, body);
    setStateCallback(res.data);
  } catch (err) {
    console.error(`Error fetching data from ${endpoint}:`, err);
  }
};

const DashboardEmails = ({ start, end, IdSelectedEmail = "", from = "" }) => {
  const { dataAccounts } = useSelector((state) => state.mailReducer);
  const { selectedAccountInEmail } = useSelector(
    (state) => state.dashboardRealTime
  );
  const selectedEmail = IdSelectedEmail
    ? IdSelectedEmail
    : selectedAccountInEmail;
  const [t] = useTranslation("common");
  const [statsEmail, setStatsEmail] = useState([]);
  const [statsEmailAffectation, setStatsEmailAffectation] = useState([]);
  const [statsEmailFolder, setStatsEmailFolder] = useState([]);
  const [statsEmailTags, setStatsEmailTags] = useState([]);
  const [statsEmailStatus, setStatsEmailStatus] = useState([]);
  const [statsEmailAssign, setStatsEmailAssign] = useState([]);
  const [statsEmailProcRate, setStatsEmailProcRate] = useState({});
  const dispatch = useDispatch();
  // const [selectedEmail, setSelectedEmail] = useState(
  //   Array.isArray(dataAccounts) &&
  //     dataAccounts.length > 0 &&
  //     dataAccounts.filter((el) => el.shared === "1").length > 0
  //     ? dataAccounts.filter((el) => el.shared === "1")[0]?.value
  //     : null
  // );

  useEffect(() => {
    if (selectedEmail)
      fetchApiData(
        KPI_ENDPOINTS.STAT_EMAIL,
        { start_date: start, end_date: end, account_id: selectedEmail },
        (data) => {
          setStatsEmail(data);
        }
      );
  }, [start, end, selectedEmail]);
  useEffect(() => {
    if (selectedEmail)
      fetchApiData(
        KPI_ENDPOINTS.STAT_EMAIL_AFFECTATION,
        { start_date: start, end_date: end, account_id: selectedEmail },
        (data) => {
          setStatsEmailAffectation(data);
        }
      );
  }, [start, end, selectedEmail]);

  useEffect(() => {
    if (selectedEmail)
      fetchApiData(
        KPI_ENDPOINTS.STAT_EMAIL_FOLDER,
        { account_id: selectedEmail },
        (data) => {
          setStatsEmailFolder(data);
        }
      );
  }, [selectedEmail]);
  useEffect(() => {
    if (selectedEmail)
      fetchApiData(
        KPI_ENDPOINTS.STAT_EMAIL_TAGS,
        { start_date: start, end_date: end, account_id: selectedEmail },
        (data) => {
          setStatsEmailTags(data);
        }
      );
  }, [start, end, selectedEmail]);
  useEffect(() => {
    fetchApiData(
      KPI_ENDPOINTS.STAT_EMAIL_STATUS,
      { account_id: selectedEmail },
      (data) => {
        setStatsEmailStatus(data);
      }
    );
  }, [selectedEmail]);
  useEffect(() => {
    fetchApiData(
      KPI_ENDPOINTS.STAT_EMAIL_ASSIGN,
      { account_id: selectedEmail },
      (data) => {
        setStatsEmailAssign({
          ...data,
          name: data.data.drilldown?.name,
          data: [
            {
              ...data.data,
              drilldown: {
                ...data.data.drilldown,
                categories: data.data.drilldown.categories.map((el) => ({
                  name: el,
                })),
              },
            },
          ],
        });
      }
    );
  }, [selectedEmail]);
  useEffect(() => {
    fetchApiData(
      KPI_ENDPOINTS.STAT_EMAIL_PROCESSING_RATE,
      { account_id: selectedEmail },
      (data) => {
        setStatsEmailProcRate(data);
      }
    );
  }, [selectedEmail]);

  return (
    <div>
      <Card
        style={{
          // background: "#F8FAFC",
          border: 0,
        }}
        styles={{ body: { padding: "6px 0px" } }}
        title={
          IdSelectedEmail ? null : (
            <div className="mb-3 flex max-w-[350px] flex-col  justify-start gap-y-0.5">
              <div className="flex items-center justify-between gap-x-1">
                <Typography.Text>
                  {t("emailTemplates.plsSelect") +
                    " " +
                    t("dashboard.accountEmail") +
                    " (" +
                    t("dashboard.sharedAccounts") +
                    ")"}
                </Typography.Text>
              </div>
              <Select
                showSearch
                popupMatchSelectWidth={false}
                filterOption={(input, option) =>
                  (option?.label ?? "")
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
                style={{ minWidth: 250 }}
                onChange={(value) => dispatch(setSelectedAccountInEmail(value))}
                value={selectedEmail}
                options={dataAccounts
                  .filter((el) => el.shared === "1")
                  .map((el) => ({ label: el?.label, value: el?.value }))}
              />
            </div>
          )
        }
      >
        <div
          style={{
            height: from.includes("drawer") ? "100%" : "calc(100vh - 250px)",
          }}
          className=" overflow-y-auto overflow-x-hidden"
        >
          <Row gutter={[16, 16]}>
            <Col className="gutter-row" span={12}>
              {/* <Card
          title=""
          extra={
            <Select
              showSearch
              filterOption={(input, option) =>
                (option?.label ?? "")
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
              onChange={(value) => setSelectedEmail(value)}
              value={selectedEmail}
              options={dataAccounts
                .filter((el) => el.shared === "1")
                .map((el) => ({ label: el.label, value: el.value }))}
            />
          }
        > */}
              <CardStat title={statsEmail?.name}>
                <PieChart
                  data={statsEmail?.data}
                  total={statsEmail?.data
                    ?.map((el) => el.y)
                    ?.reduce((x, y) => x + y, 0)}
                  name={""}
                />
              </CardStat>
            </Col>
            <Col className="gutter-row" span={12}>
              {/* <Card
          title=""
          extra={
            <Select
              showSearch
              filterOption={(input, option) =>
                (option?.label ?? "")
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
              onChange={(value) => setSelectedEmail(value)}
              value={selectedEmail}
              options={dataAccounts
                .filter((el) => el.shared === "1")
                .map((el) => ({ label: el.label, value: el.value }))}
            />
          }
        > */}
              <CardStat title={statsEmailAffectation?.name}>
                <DonutChart2
                  data={statsEmailAffectation?.data}
                  total={statsEmailAffectation?.total}
                  name=""
                />
              </CardStat>
            </Col>

            <Col className="gutter-row" span={12}>
              {/* <Card
          title=""
          extra={
            <Select
              showSearch
              filterOption={(input, option) =>
                (option?.label ?? "")
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
              onChange={(value) => setSelectedEmail(value)}
              value={selectedEmail}
              options={dataAccounts
                .filter((el) => el.shared === "1")
                .map((el) => ({ label: el.label, value: el.value }))}
            />
          }
        > */}
              <CardStat title={statsEmailTags?.name}>
                <OneBarChart data={{ ...statsEmailTags, name: "" }} />
              </CardStat>
            </Col>
            <Col className="gutter-row" span={12}>
              <CardStat
                title={statsEmailAssign?.name}
                subtitle={t("common:dashboard.independantNumber")}
              >
                <BrowserMarketShareChart
                  hasSelect={false}
                  isExistDate={true}
                  allData={{ ...statsEmailAssign, name: "" }}
                  parentName={""}
                  childName={""}
                />
              </CardStat>
            </Col>

            <Col className="gutter-row" span={12}>
              <Card
                title={statsEmailStatus?.name}
                subtitle={t("common:dashboard.independantNumber")}
              >
                <HorizontalOneBarChart
                  data={{ ...statsEmailStatus, name: "" }}
                  isExistDate={true}
                />
              </Card>
            </Col>
            <Col className="gutter-row" span={12}>
              <Card
                title={statsEmailProcRate?.name}
                subtitle={t("common:dashboard.independantNumber")}
              >
                <PieChartWithLegend
                  isExistDate={true}
                  data={{ ...statsEmailProcRate, name: "" }}
                  // total={statsEmailProcRate?.data
                  //   ?.map((el) => Number(el.y))
                  //   .reduce((x, y) => x + y, 0)}
                  // name={statsEmailProcRate?.name}
                />
              </Card>
            </Col>
          </Row>
        </div>
      </Card>
    </div>
  );
};

export default DashboardEmails;
