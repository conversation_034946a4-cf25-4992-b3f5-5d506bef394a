import {
  Suspense,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useDispatch } from "react-redux";
import { useLocation } from "react-router-dom";
import { useSelector } from "react-redux";
import {
  resetStateOtherUser,
  setChatSelectedConversation,
  setOpenDrawer,
} from "new-redux/actions/chat.actions";
import { <PERSON><PERSON>, <PERSON><PERSON>, Drawer, Toolt<PERSON> } from "antd";
import {
  CloseCircleOutlined,
  CompressOutlined,
  EllipsisOutlined,
} from "@ant-design/icons";
import { URL_ENV } from "index";
import DisplayAvatar from "./DisplayAvatar";
import ChatConversations from "../../layouts/chat/conversation/body";
import InputChat from "../../layouts/chat/conversation/input";
import { LoaderDrawer } from "pages/layouts/chat";
import { closeDrawerChat } from "new-redux/actions/voip.actions/handleDrawerChat";
import { toastNotification } from "components/ToastNotification";
import { getUserListChat } from "new-redux/services/chat.services";
import { useWindowSize } from "pages/clients&users/components/WindowSize";
import { setOpenTaskRoomDrawer } from "new-redux/actions/tasks.actions/handleTaskRoomDrawer";
import { useTranslation } from "react-i18next";
import { truncateString } from "../helpers/helpersFunc";
import Header from "pages/layouts/chat/conversation/header/Header";
import ChatDrawerThread from "pages/layouts/chat/drawer/thread";
import ChatDrawerAction from "pages/layouts/chat/drawer/ChatDrawerAction";
import { setSearchMessageTerm } from "new-redux/actions/chat.actions/Input";
// import Draggable from "react-draggable";

const ChatWithColleague = () => {
  //
  const windowSize = useWindowSize();
  const dispatch = useDispatch();
  const [t] = useTranslation("common");
  const location = useLocation();
  const firstPathname = useRef(location.pathname);
  const chatSelectedConvRef = useRef(null);
  //
  const chatSelectedConv = useSelector(
    (state) => state?.ChatRealTime?.selectedConversation
  );
  const onlineUser = useSelector((state) => state?.ChatRealTime?.onlineUser);
  const typingUsers = useSelector((state) => state?.ChatRealTime?.typingUsers);
  const membersGroupsChat = useSelector(
    (state) => state?.chat?.membersGroupsChat
  );

  const userList = useSelector((state) => state.chat.userList);
  const openDrawerThread = useSelector((state) => state.chat.openDrawer);
  const openDrawer = useSelector(({ voip }) => voip.openDrawerChat);
  const selectedExternalConv = useSelector(
    ({ voip }) => voip.selectedExternalConv
  );
  const { uuid, _id, source } = selectedExternalConv;
  //
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [isHovering, setIsHovering] = useState(false);
  const [lastMsg, setLastMsg] = useState("");

  // const [isDragging, setIsDragging] = useState(false);
  //
  const dispatchResetStateOtherUser = () => {
    dispatch(setOpenTaskRoomDrawer(false));
    let timer = setTimeout(() => {
      dispatch(
        resetStateOtherUser({
          forced: true,
          keepDrawerOpened: false,
          item: {
            _id: chatSelectedConvRef.current?.id,
            type: chatSelectedConvRef.current?.type,
            participants:
              chatSelectedConvRef.current?.type === "user" ? true : false,
          },
        })
      );
      clearTimeout(timer);
    }, 10);
  };
  //
  //Handle the change of the pathName
  const handlePathNameChange = useCallback(() => {
    if (
      firstPathname.current !== "/chat" &&
      location.pathname === "/chat" &&
      selectedExternalConv &&
      openDrawer
    ) {
      setIsOpen(false);
      dispatch(closeDrawerChat());
      dispatchResetStateOtherUser();
    }
    firstPathname.current = location.pathname;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location.pathname]);

  useEffect(() => {
    handlePathNameChange();
  }, [handlePathNameChange]);
  //
  const handleCancel = () => {
    setIsOpen(false);
    dispatch(closeDrawerChat());
    const timer = setTimeout(() => {
      chatSelectedConvRef.current = null;
      dispatch(setChatSelectedConversation({ selectedConversation: null }));
      (openDrawerThread.type === "thread" ||
        openDrawerThread.type === "search") &&
        dispatch(setOpenDrawer({ type: "" }));
      openDrawer?.type === "search" &&
        dispatch(
          setSearchMessageTerm({
            value: "",
            forced: false,
          })
        );
      clearTimeout(timer);
    }, 100);
  };
  //
  const handleMsgClick = () => {
    isMinimized && setIsMinimized(false);
    if (source === "popoverChat" && _id) {
      if (!chatSelectedConv && !chatSelectedConvRef.current) {
        dispatch(closeDrawerChat());
      } else setIsOpen(true);
    } else if (uuid) {
      if (!userList?.length) dispatch(getUserListChat());
      const userInfo = userList?.find(
        (user) => user?.uuid && user?.uuid === uuid
      );
      if (!userInfo) {
        toastNotification("error", "Cannot find this user", "topRight");
        handleCancel();
        return;
      }
      dispatch(setOpenTaskRoomDrawer(false));
      dispatch(
        resetStateOtherUser({
          forced: true,
          keepDrawerOpened: false,
          item: {
            _id: userInfo?._id,
            type: "user",
            participants: true,
          },
        })
      );
      if (location.pathname !== "/chat" && source !== "overviewTask")
        setIsOpen(true);
      else dispatch(closeDrawerChat());
    } else handleCancel();

    // if (source === "popoverChat") {
    //   dispatch(setOpenTaskRoomDrawer(false));
    //   if (chatSelectedConv) {
    //     chatSelectedConvRef.current = chatSelectedConv;
    //     setIsOpen(true);
    //   } else if (!chatSelectedConv && chatSelectedConvRef.current) {
    //     dispatch(
    //       resetStateOtherUser({
    //         forced: true,
    //         keepDrawerOpened: false,
    //         item: {
    //           _id: chatSelectedConvRef.current?.id,
    //           type: chatSelectedConvRef.current?.type,
    //           participants:
    //             chatSelectedConvRef.current?.type === "user" ? true : false,
    //         },
    //       })
    //     );
    //   } else {
    //     handleCancel();
    //   }
    //   if (location.pathname === "/chat") {
    //     let timer = setTimeout(() => {
    //       dispatch(closeDrawerChat());
    //       return () => clearTimeout(timer);
    //     }, 1);
    //   }
    //   return;
    // }

    // if (!userList?.length) dispatch(getUserListChat());

    // const userInfo = userList?.find(
    //   (user) => user?.uuid && user?.uuid === uuid
    // );
    // if (!userInfo) {
    //   toastNotification("error", "Cannot find this user", "topRight");
    //   handleCancel();
    //   return;
    // }
    // dispatch(setOpenTaskRoomDrawer(false));
    // dispatch(
    //   resetStateOtherUser({
    //     forced: true,
    //     keepDrawerOpened: false,
    //     item: {
    //       _id: userInfo?._id,
    //       type: "user",
    //       participants: true,
    //     },
    //   })
    // );
    // // console.log({ userInfo });
    // const selectedConversation = {
    //   email: userInfo?.email,
    //   name: userInfo?.name,
    //   desrciption: null,
    //   image: userInfo?.image,
    //   uuid: userInfo?.uuid,
    //   admin_id: null,
    //   bot: null,
    //   id: userInfo?._id,
    //   post_number: userInfo?.post_number,
    //   role: userInfo?.role,
    //   type: "user",
    // };

    // if (location.pathname !== "/chat") {
    //   chatSelectedConvRef.current = selectedConversation;
    //   setIsOpen(true);
    // } else {
    //   let timer = setTimeout(() => {
    //     dispatch(closeDrawerChat());
    //     return () => clearTimeout(timer);
    //   }, 1);
    // }
  };

  useEffect(() => {
    if (openDrawer) {
      handleMsgClick();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [openDrawer, uuid, _id, source]);
  //
  const handleMinimizeClick = () => {
    let timer;
    if (!isMinimized) {
      setIsMinimized(true);
      timer = setTimeout(() => {
        dispatch(setChatSelectedConversation({ selectedConversation: null }));
      }, 100);
    } else {
      dispatchResetStateOtherUser();
      setIsHovering(false);
      // Just for the UX
      timer = setTimeout(() => {
        setIsMinimized(false);
      }, 200);
    }
    return () => clearTimeout(timer);
  };
  //
  const DrawerProps = useMemo(
    () => ({
      // closeIcon: (
      //   <Button
      //     size="small"
      //     type="text"
      //     icon={<CloseOutlined />}
      //     onClick={() => handleCancel()}
      //   />
      // ),
      title: (
        <Header source="external" />
        // <div className="flex flex-row items-center space-x-2">
        //   <Badge
        //     styles={{
        //       indicator: {
        //         height: 9,
        //         width: 9,
        //       },
        //     }}
        //     offset={[-5, 35]}
        //     color={
        //       onlineUser?.[chatSelectedConvRef.current?.uuid] === "online"
        //         ? "green"
        //         : onlineUser?.[chatSelectedConvRef.current?.uuid] === "busy"
        //         ? "red"
        //         : onlineUser?.[chatSelectedConvRef.current?.uuid] === "away"
        //         ? "orange"
        //         : "#a6a6a6"
        //     }
        //     dot={
        //       chatSelectedConvRef.current?.type === "user"
        //       // ? !!chatSelectedConvRef.current?.id
        //       // : false
        //     }
        //   >
        //     <DisplayAvatar
        //       name={chatSelectedConvRef.current?.name}
        //       urlImg={
        //         chatSelectedConvRef.current?.image &&
        //         `${
        //           chatSelectedConvRef.current?.type === "user"
        //             ? URL_ENV?.REACT_APP_BASE_URL +
        //               URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
        //             : URL_ENV?.REACT_APP_OAUTH_CHAT_API +
        //               process.env.REACT_APP_SUFFIX_CHAT_GROUP_STORAGE
        //         }${chatSelectedConvRef.current?.image}`
        //       }
        //       size={40}
        //     />
        //   </Badge>
        //   <div>
        //     <p className="max-w-[250px] truncate">
        //       {chatSelectedConvRef.current?.name?.replaceAll("_", " ")}
        //     </p>
        //     {participantsStatus.participants?.length ? (
        //       <Button type="text" style={{ color: "rgba(0, 0, 0, 0.45)" }}>
        //         <Space size={4} split={<Divider type="vertical" />}>
        //           <Space size={3}>
        //             <Badge status="success" />
        //             <span>{participantsStatus?.onlineParticipants || 0}</span>
        //           </Space>
        //           <Space size={3}>
        //             <TeamOutlined />
        //             <span>{selectedParticipants?.length}</span>
        //           </Space>
        //         </Space>
        //       </Button>
        //     ) : (
        //       <Skeleton.Input active={true} size="small" />
        //     )}
        //   </div>
        // </div>
      ),
      extra: (
        <Tooltip title={t("voip.minimizeDrawerChat")} placement="bottomRight">
          <Button
            size="small"
            shape="circle"
            onClick={handleMinimizeClick}
            icon={<CompressOutlined style={{ fontSize: 16 }} />}
            type="text"
          />
        </Tooltip>
      ),
    }),

    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      // chatSelectedConv,
      chatSelectedConvRef.current,
      // onlineUser,
      // selectedParticipants,
    ]
  );
  //
  // tracker of condition isMinimized & chatSelectedConv to update the ref chatSelectedConvRef
  useEffect(() => {
    if (!openDrawer) {
      let timer = setTimeout(() => {
        chatSelectedConvRef.current = null;
        return () => clearTimeout(timer);
      }, 300);
      return;
    } else {
      if (chatSelectedConv?.id) {
        chatSelectedConvRef.current = chatSelectedConv;
      }
      // if (isMinimized && location.pathname !== "/chat") {
      //   let timer = setTimeout(() => {
      //     dispatch(setChatSelectedConversation({ selectedConversation: null }));
      //     clearTimeout(timer);
      //   }, 300);
      // }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [chatSelectedConv]);
  //
  const unreadMsg = useMemo(() => {
    if (!chatSelectedConvRef?.current?.id || !isMinimized) return 0;

    const findDiscussion = (discussions, id) => {
      return discussions?.find(
        (discussion) =>
          discussion?.contact?._id === id || discussion?.room?._id === id
      );
    };

    const discussion = findDiscussion(
      membersGroupsChat,
      chatSelectedConvRef?.current?.id
    );

    // console.log(member);
    if (discussion?.total_unread) {
      const lastMsg = discussion?.last_message;
      if (!lastMsg) {
        const senderName = truncateString(discussion.sender?.name, 20);
        setLastMsg(
          discussion.room
            ? t("chat.message_type.message_deleted_room", {
                name: senderName ? `[${senderName}]` : "",
              })
            : t("chat.message_type.message.message_deleted")
        );
      } else {
        const typeMsg = lastMsg?.type;
        const msg = extractTextFromHtml(lastMsg?.message);
        const senderName = truncateString(discussion.sender?.name, 20);
        const isRoom = !!discussion.room;
        switch (typeMsg) {
          case "message":
            if (!isRoom) setLastMsg(msg);
            else setLastMsg(senderName ? `${senderName}: ${msg}` : msg);
            break;
          case "message_from_bot":
            setLastMsg(
              t("chat.message_type.message_from_bot", {
                name: discussion.bot.name ? `[${discussion.bot.name}]` : "",
              })
            );
            break;
          case "message_visio_conf":
            setLastMsg(
              t("chat.message_type.message_visio_conf", {
                name: discussion.sender?.name
                  ? `[${truncateString(discussion.sender?.name, 20)}]`
                  : "A member",
              })
            );
            break;
          case "image":
            setLastMsg(t("chat.external_drawer.image", { name: senderName }));
            break;
          case "file":
            setLastMsg(t("chat.external_drawer.file", { name: senderName }));
            break;
          case "voice":
            setLastMsg(t("chat.external_drawer.voice", { name: senderName }));
            break;
          case "mixed_image":
            setLastMsg(
              t("chat.external_drawer.mixed_image", { name: senderName })
            );
            break;
          case "mixed_file":
            setLastMsg(
              t("chat.external_drawer.mixed_file", { name: senderName })
            );
            break;
          case "message_missed_call":
            setLastMsg(t("chat.message_system.missed_call"));
            break;
          case "message_received_call":
            setLastMsg(t("chat.message_system.incoming_call"));
            break;
          case "replay":
            setLastMsg([
              extractTextFromHtml(lastMsg?.message),
              t("chat.action.reply"),
            ]);
            break;
          default:
            setLastMsg("");
            break;
        }
      }
    } else setLastMsg("");
    return discussion?.total_unread ?? 0;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [membersGroupsChat, chatSelectedConvRef, chatSelectedConv]);
  //
  const isColleagueTyping = useMemo(
    () =>
      typingUsers.find(
        (user) =>
          !user?.room_id
            ? user.user_id === chatSelectedConvRef.current?.id
            : user.room_id === chatSelectedConvRef.current?.id
        // (user.room_id
        //   ? user.room_id === chatSelectedConvRef.current?.conversationId
        //   : true)
      )
        ? true
        : false,
    [typingUsers, chatSelectedConvRef]
  );
  //
  // Handle ESC press
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === "Escape" && !openDrawerThread.type) {
        if (isMinimized) handleCancel();
        else handleMinimizeClick();
      } else if (event.key === "Escape" && openDrawerThread.type) {
        dispatch(setOpenDrawer({ type: "" }));
        if (openDrawer?.type === "search") {
          dispatch(
            setSearchMessageTerm({
              value: "",
              forced: false,
            })
          );
        }
      }
    };

    window.addEventListener("keydown", handleKeyDown);

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isMinimized, openDrawerThread.type]);
  //
  return (
    <>
      {isMinimized ? (
        <Tooltip
          placement="left"
          title={
            lastMsg && unreadMsg ? (
              <p className="font-semibold">{truncateMsg(lastMsg, 65)}</p>
            ) : null
          }
        >
          <div
            key="bubble-chat"
            className="bubble-chat flex cursor-pointer flex-row rounded-full bg-slate-100 p-[3px] shadow-2xl drop-shadow-xl"
            style={{
              position: "fixed",
              bottom: 45,
              right: 45,
              zIndex: 9999,
            }}
            onMouseEnter={() => setIsHovering(true)}
            onMouseLeave={() => setIsHovering(false)}
            onClick={handleMinimizeClick}
          >
            <Badge // Badge appear when user typing
              offset={[-45, 50]}
              count={
                isColleagueTyping ? (
                  <div className="flex rounded-lg bg-white px-1.5">
                    <EllipsisOutlined
                      className="animate-ping"
                      style={{ fontSize: 18 }}
                    />
                  </div>
                ) : (
                  0
                )
              }
            >
              <Badge // Badge cancelClick (close the bubble) when user hover && number unread msg
                offset={[0, 0]}
                overflowCount={10}
                count={
                  isHovering ? (
                    <Button
                      onClick={() => handleCancel()}
                      size="small"
                      type="default"
                      shape="circle"
                      icon={<CloseCircleOutlined style={{ fontSize: 14 }} />}
                    />
                  ) : (
                    unreadMsg
                  )
                }
              >
                <Badge // Badge status of user (online, busy...)
                  offset={[-5, 48]}
                  color={
                    onlineUser?.[chatSelectedConvRef.current?.uuid] === "online"
                      ? "green"
                      : onlineUser?.[chatSelectedConvRef.current?.uuid] ===
                        "busy"
                      ? "red"
                      : onlineUser?.[chatSelectedConvRef.current?.uuid] ===
                        "away"
                      ? "orange"
                      : "#a6a6a6"
                  }
                  styles={{
                    indicator: {
                      height: 10,
                      width: 10,
                    },
                  }}
                  dot={chatSelectedConvRef.current?.type === "user"}
                >
                  <DisplayAvatar
                    name={chatSelectedConvRef.current?.name}
                    urlImg={handleImgUrl(
                      chatSelectedConvRef.current?.image,
                      chatSelectedConvRef.current?.type
                    )}
                    size={55}
                    cursor="pointer"
                  />
                </Badge>
              </Badge>
            </Badge>
          </div>
        </Tooltip>
      ) : (
        <Drawer
          open={isOpen && !isMinimized}
          mask={false}
          title={DrawerProps?.title}
          // closeIcon={DrawerProps.closeIcon}
          onClose={handleCancel}
          width={windowSize?.width / 2.5 <= 600 ? 600 : windowSize?.width / 2.5}
          extra={DrawerProps.extra}
          // zIndex={findNextZIndex()}
          zIndex={1001}
          styles={{ body: { padding: "5px 24px" } }}
        >
          {chatSelectedConv?.id && (
            <Suspense fallback={<LoaderDrawer />}>
              <div className="mr-[-23px] flex h-full flex-1 flex-col justify-between overflow-hidden">
                <div className="flex-1 overflow-hidden pl-4">
                  <ChatConversations />
                </div>
                <InputChat maxWidth={"auto"} />
              </div>
            </Suspense>
          )}
          <Drawer
            open={
              openDrawerThread.type === "thread" ||
              openDrawerThread.type === "search"
            }
            bodyStyle={{ padding: "5px 0px" }}
            width={384}
            mask={false}
            closable={false}
          >
            {openDrawerThread.type === "thread" ? (
              <ChatDrawerThread source={"external"} />
            ) : openDrawerThread.type === "search" ? (
              <ChatDrawerAction source={"external"} />
            ) : null}
          </Drawer>
        </Drawer>
      )}
    </>
  );
};
//
export const extractTextFromHtml = (html) => {
  const tempDivElement = document.createElement("div");
  tempDivElement.innerHTML = html;
  return tempDivElement.textContent || tempDivElement.innerText || "";
};

export const truncateMsg = (text, maxLength = 65) => {
  if (typeof text === "string") {
    return text.length > maxLength
      ? text.substring(0, maxLength) + "..."
      : text;
  } else if (Array.isArray(text) && text.length > 0) {
    const truncatedMessage =
      text[0].length > maxLength
        ? text[0].substring(0, maxLength) + "..."
        : text[0];
    return `${text[1]}: ${truncatedMessage}`;
  }
  return text;
};

export const handleImgUrl = (img, type) => {
  const baseUrl =
    type === "user"
      ? URL_ENV?.REACT_APP_BASE_URL
      : URL_ENV.REACT_APP_OAUTH_CHAT_API;
  const avatarSuffix = URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL;

  if (!img) return null;
  if (type === "user") {
    return `${baseUrl}${avatarSuffix}${img}`;
  }
  // const imgFileName = img.split("/storage/uploads/")[1];
  // console.log({ imgFileName });
  // return `${baseUrl}/${avatarSuffix}${imgFileName}`;
  return `${baseUrl}/${img}`;
};

export default ChatWithColleague;

// "https://spherechatbackmongodb.cmk.biz:4543storage/uploads/room/D9dzOcvYOMrkFZep5yOjc4f2Wx5aPcv6FwXLORMd.png"

// "https://spherechatbackmongodb.cmk.biz:4543/storage/uploads/room/D9dzOcvYOMrkFZep5yOjc4f2Wx5aPcv6FwXLORMd.png"
