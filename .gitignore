# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing

# production
/build

public/env.json
public/old-env.json
.env
# misc

*.env.prod
*.env.stage
*.env.dev
 public/env.json
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Ignore Dockerfile and docker-compose files
Dockerfile
docker-compose.yml
