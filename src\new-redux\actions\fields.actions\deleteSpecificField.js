import { DELETE_FIELD_SUCCESS, DELETE_FIELD_ERROR, DELETE_FIELD_LOADING } from "../../constants";
import MainService from "../../../services/main.service";

export const DeleteSpecificField = (object) => async (dispatch) => {
  try {
    dispatch({ type: DELETE_FIELD_LOADING });
    const response = await MainService.removeSpecificField(object);
    dispatch({
      type: DELETE_FIELD_SUCCESS,
      payload: object,
    });
  } catch (error) {
    if (error?.status !== 401) {
      dispatch({
        type: DELETE_FIELD_ERROR,
        payload: error,
      });
    }
  }
};
