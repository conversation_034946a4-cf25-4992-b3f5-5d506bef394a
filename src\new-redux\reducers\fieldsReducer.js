/**
 * Reducer function for managing state related to fields in the application.
 * @param {object} state - The current state of the fields.
 * @param {object} action - The action to be performed on the state.
 * @returns The updated state based on the action type and payload.
 */
import {
  GET_FIELDS_SUCCESS,
  OPEN_FIELD_DRAWER,
  TIT<PERSON>_CONFIG,
  GET_FIELDS_ERROR,
  DELETE_GROUP_ERROR,
  CREATE_NEW_FIELD_SUCCESS,
  CREATE_NEW_FIELD_ERROR,
  RESET_STATE,
  DELETE_FIELD_LOADING,
  DELETE_FIELD_SUCCESS,
  DELETE_GROUP_SUCCESS,
  <PERSON>LETE_GROUP_LOADING,
  DELETE_FIELD_ERROR,
  IS_LOADING,
  UPDATE_FIELD_SUCCESS,
  UPDATE_FIELD_ERROR,
  DELETE_FIELD_OPTION_SUCCESS,
  DELETE_FIELD_OPTION_ERROR,
  UPDATE_FIELD_PARAMETER_SUCCESS,
  UPDATE_FIELD_PARAMETER_ERROR,
  UPDATE_FIELD_PARAM_LOADING,
  UPDATE_ALIAS_SUCCESS,
  UPDATE_ALIAS_ERROR,
  UPDATE_ALIAS_LOADING,
  GET_SEARCH_FIELDS_SUCCESS,
  CREATE_FIELD_LOADING,
  UPDATE_FIELD_LOADING,
  CREATE_NEW_GROUP_SUCCESS,
  CREATE_NEW_GROUP_ERROR,
  CREATE_GROUP_LOADING,
  UPDATE_GROUP_SUCCESS,
  UPDATE_GROUP_ERROR,
  UPDATE_GROUP_LOADING,
  UPDATE_GROUP_RANK_SUCCESS,
  UPDATE_GROUP_RANK_ERROR,
  UPDATE_GROUP_RANK_LOADING,
  DELETE_FIELD_OPTION_LOADING,
  UPDATE_FIELD_RANK_SUCCESS,
  UPDATE_FIELD_RANK_ERROR,
  UPDATE_FIELD_RANK_LOADING,
  UPDATE_FIELD_OPTIONS_RANK_SUCCESS,
  UPDATE_FIELD_OPTIONS_RANK_ERROR,
  UPDATE_FIELD_OPTIONS_RANK_LOADING,
  RESET_FIELD_STATE,
  DESTROY_FIELD_STATE,
} from "../constants";

// Initial reducer state.
const initialState = {
  fields: {},
  errors: {},
  isLoading: false,
  isFieldCreated: false,
  isGroupCreated: false,
  isFieldUpdated: false,
  isFieldRemoved: false,
  isOptDeleted: false,
  deleteFieldOptLoading: false,
  updateFieldParamSuccess: false,
  updateFieldAliasSuccess: false,
  updateFieldParamError: false,
  updateParamLoading: false,
  updateFieldAliasLoading: false,
  isCreateFieldLoading: false,
  isUpdateFieldLoading: false,
  isUpdateGroupRankLoading: false,
  isCreateGroupLoading: false,
  isGroupDeleted: false,
  updateGroupLoading: false,
  isGroupUpdated: false,
  isGroupRankUpdated: false,
  updateFieldRankLoading: false,
  updateFieldOptionRankLoading: false,
  isRankFieldUpdated: false,
  isError: false,
  fieldsTotal: 0,
  openFieldDrawer: false,
  drawerTitle: null,
  isAvatarUsed: 0,
  isDeletingFieldLoading: false,
  updateGroupRankLoading: false,
  deletingGroupLoading: false,
};

const fields = (state = initialState, action) => {
  const { type, payload } = action;
  switch (type) {
    case IS_LOADING:
      return {
        ...state,
        isLoading: true,
      };

    case UPDATE_FIELD_PARAM_LOADING:
      return {
        ...state,
        updateParamLoading: true,
      };

    case UPDATE_ALIAS_LOADING:
      return {
        ...state,
        updateFieldAliasLoading: true,
      };

    case CREATE_FIELD_LOADING:
      return {
        ...state,
        isCreateFieldLoading: true,
      };

    case UPDATE_FIELD_LOADING:
      return {
        ...state,
        isUpdateFieldLoading: true,
      };

    case CREATE_GROUP_LOADING:
      return {
        ...state,
        isCreateGroupLoading: true,
      };

    case UPDATE_GROUP_LOADING:
      return {
        ...state,
        updateGroupLoading: true,
      };
    case DELETE_FIELD_LOADING:
      return {
        ...state,
        isDeletingFieldLoading: true,
      };

    case UPDATE_FIELD_OPTIONS_RANK_LOADING:
      return {
        ...state,
        updateFieldOptionRankLoading: true,
      };

    case UPDATE_FIELD_RANK_LOADING:
      return {
        ...state,
        updateFieldRankLoading: true,
      };

    case DELETE_FIELD_OPTION_LOADING:
      return {
        ...state,
        deleteFieldOptLoading: true,
      };

    case UPDATE_GROUP_RANK_LOADING:
      return {
        ...state,
        updateGroupRankLoading: true,
      };

    case GET_FIELDS_SUCCESS:
    case GET_SEARCH_FIELDS_SUCCESS:
      return {
        ...state,
        isLoading: false,
        fields: payload?.family ? { ...payload?.family } : payload,
        updateParamLoading: false,
        fieldsTotal: payload?.page?.total,
        isAvatarUsed: payload?.family?.avatar_used,
      };

    case CREATE_NEW_FIELD_SUCCESS:
      let allGroups = state?.fields?.groups;
      let groupIndex = allGroups.findIndex(
        (element) => element?.id == payload?.field_group_id
      );
      return {
        ...state,
        fields: {
          ...state?.fields,
          groups: allGroups.map((group, i) =>
            groupIndex === i
              ? { ...group, fields: [...group?.fields, payload] }
              : group
          ),
        },
        isFieldCreated: true,
        isLoading: false,
        isCreateFieldLoading: false,
      };

    case CREATE_NEW_GROUP_SUCCESS:
      return {
        ...state,
        fields: {
          ...state?.fields,
          groups: [...state?.fields?.groups, payload],
        },
        isGroupCreated: true,
      
        isCreateGroupLoading: false,
      };

    case DELETE_FIELD_SUCCESS:
      let groupsArray = state?.fields?.groups;
      return {
        ...state,
        isFieldRemoved: true,
        isLoading: false,
        isDeletingFieldLoading: false,
        fields: {
          ...state?.fields,
          groups:
            groupsArray &&
            groupsArray.map((group) =>
              group?.id == payload?.groupId
                ? {
                    ...group,
                    fields: group?.fields.filter(
                      (field) => field?.id != Number(payload?.fieldId)
                    ),
                  }
                : group
            ),
        },
        fieldsTotal: state?.fieldsTotal - 1,
      };

    case UPDATE_FIELD_SUCCESS:
      let oldGroup = state?.fields?.groups || [];
      let updatedFieldGroup = oldGroup.findIndex(
        (element) =>
          Number(element?.id) ===
          Number(payload?.response?.data?.field_group_id)
      );
      let updatedFieldIndex =
        updatedFieldGroup >= 0 &&
        oldGroup[updatedFieldGroup]?.fields.findIndex(
          (element) => Number(element?.id) === Number(payload?.updatedFieldId)
        );
      return {
        ...state,
        isFieldUpdated: true,
        isUpdateFieldLoading: false,
        fields: {
          ...state.fields,
          groups: oldGroup.map((group, groupIndex) => {
            if (
              payload?.response?.message === "Group has been changed " &&
              groupIndex === updatedFieldGroup
            ) {
              return {
                ...group,
                fields: [
                  ...(group?.fields?.filter(
                    (field) => field?.id !== payload?.response?.data?.id
                  ) || []),
                  payload?.response?.data,
                ],
              };
            }

            if (groupIndex === updatedFieldGroup) {
              return {
                ...group,
                fields: group?.fields?.map((field, fieldIndex) =>
                  fieldIndex === updatedFieldIndex
                    ? payload?.response?.data
                    : field
                ),
              };
            }

            return group;
          }),
        },
      };

    case UPDATE_ALIAS_SUCCESS:
      let originArray = state?.fields?.groups;
      let specificGroup = originArray.findIndex(
        (element) => element?.id == payload?.response?.field_group_id
      );
      let updateAliasFieldIndex = originArray[specificGroup]?.fields.findIndex(
        (element) => element?.id == payload?.updatedAliasFieldId
      );

      return {
        ...state,
        updateFieldAliasSuccess: true,
        updateFieldAliasLoading: false,
        fields: {
          ...state?.fields,
          groups:
            originArray &&
            originArray.map((group, i) =>
              specificGroup == i
                ? {
                    ...group,
                    fields: group?.fields.map((item, i) =>
                      i == updateAliasFieldIndex ? payload?.response : item
                    ),
                  }
                : group
            ),
        },
      };

    case UPDATE_GROUP_RANK_SUCCESS:
      return {
        ...state,
        updateGroupRankLoading: false,
        isGroupRankUpdated: true,
        fields: {
          ...state?.fields,
          groups: payload,
        },
      };

    case UPDATE_FIELD_RANK_SUCCESS:
      let grp = state?.fields?.groups;
      let grpIndex =
        grp && grp.findIndex((element) => element?.id == payload?.groupId);
      return {
        ...state,
        updateFieldRankLoading: false,
        isRankFieldUpdated: true,
        fields: {
          ...state?.fields,
          groups: grp.map((group, i) =>
            i == grpIndex
              ? { ...group, fields: payload?.response?.data }
              : group
          ),
        },
      };

    case DELETE_FIELD_OPTION_SUCCESS:
      let groups = state?.fields?.groups;

      return {
        ...state,
        deleteFieldOptLoading: false,
        fields: {
          ...state?.fields,
          groups:
            groups &&
            groups.map((element) => {
              return {
                ...element,
                fields: element?.fields.map((el) => {
                  if (el?.id == payload?.fieldId) {
                    return {
                      ...el,
                      field_list_value: el?.field_list_value.filter(
                        (data) => data?.id != payload?.optionId
                      ),
                    };
                  } else {
                    return el;
                  }
                }),
              };
            }),
        },
        isOptDeleted: true,
        isLoading: false,
      };

    case UPDATE_FIELD_OPTIONS_RANK_SUCCESS:
      let originGroups = state?.fields?.groups;

      return {
        ...state,
        updateFieldOptionRankLoading: false,
        fields: {
          ...state?.fields,
          groups:
            originGroups &&
            originGroups.map((element) => {
              return {
                ...element,
                fields: element?.fields.map((el) => {
                  if (el?.id == payload?.fieldId) {
                    return {
                      ...el,
                      field_list_value:
                        payload?.response?.data?.field_list_value,
                    };
                  } else {
                    return el;
                  }
                }),
              };
            }),
        },
      };

    case DELETE_GROUP_SUCCESS:
      return {
        ...state,
        fields: {
          ...state?.fields,
          groups: state?.fields?.groups.filter(
            (element) => element?.id != payload
          ),
        },
        isGroupDeleted: true,
        deletingGroupLoading: false,
      };
    case DELETE_GROUP_LOADING:
      return {
        ...state,

        deletingGroupLoading: true,
      };

    case UPDATE_FIELD_PARAMETER_SUCCESS:
      let originalArray = state?.fields?.groups;
      let correspondingGroup = originalArray.findIndex(
        (element) => element?.id == payload?.response?.data?.field_group_id
      );
      let itemIdx = originalArray[correspondingGroup].fields.findIndex(
        (element) => element?.id == payload?.response?.data?.id
      );
      return {
        ...state,
        updateFieldParamSuccess: true,
        updateParamLoading: false,
        fields: {
          ...state?.fields,
          groups:
            originalArray &&
            originalArray.map((group, i) =>
              i == correspondingGroup
                ? {
                    ...group,
                    fields: group?.fields.map((field, i) =>
                      i == itemIdx ? payload?.response?.data : field
                    ),
                  }
                : group
            ),
        },
      };

    case UPDATE_FIELD_PARAMETER_ERROR:
      return {
        ...state,
        updateFieldParamError: true,
        updateParamLoading: false,
        errors: payload,
      };

    case UPDATE_GROUP_SUCCESS:
      let array = state?.fields?.groups;
      let correspondGroup = array.findIndex(
        (element) => element?.id == payload?.updatedGroupId
      );
      return {
        ...state,
        isGroupUpdated: true,
        updateGroupLoading: false,
        fields: {
          ...state?.fields,
          groups: array.map((item, i) =>
            correspondGroup === i ? payload?.response : item
          ),
        },
      };

    case OPEN_FIELD_DRAWER:
      return { ...state, openFieldDrawer: payload };

    case TITLE_CONFIG:
      return { ...state, drawerTitle: payload };

    case GET_FIELDS_ERROR:
    case DELETE_FIELD_ERROR:
    case CREATE_NEW_FIELD_ERROR:
    case UPDATE_FIELD_ERROR:
    case DELETE_FIELD_OPTION_ERROR:
    case CREATE_NEW_GROUP_ERROR:
    case UPDATE_ALIAS_ERROR:
    case UPDATE_GROUP_ERROR:
    case UPDATE_GROUP_RANK_ERROR:
    case DELETE_GROUP_ERROR:
    case UPDATE_FIELD_RANK_ERROR:
    case UPDATE_FIELD_OPTIONS_RANK_ERROR:
      return {
        ...state,
        isLoading: false,
        errors: payload?.response,
        isError: true,
        isDeletingFieldLoading: false,
        deleteFieldOptLoading: false,
        updateParamLoading: false,
        isCreateFieldLoading: false,
        isUpdateFieldLoading: false,
        isCreateGroupLoading: false,
        updateGroupLoading: false,
        isGroupUpdated: false,
        isGroupRankUpdated: false,
        updateFieldRankLoading: false,
        isRankFieldUpdated: false,
        updateFieldOptionRankLoading: false,
        updateGroupRankLoading: false,
        deletingGroupLoading: false,
      };

    case RESET_FIELD_STATE:
      return {
        ...state,
        errors: {},
        isLoading: false,
        isDeletingFieldLoading: false,
        isFieldCreated: false,
        isFieldUpdated: false,
        isFieldRemoved: false,
        isOptDeleted: false,
        isError: false,
        deleteFieldOptLoading: false,
        updateFieldParamSuccess: false,
        updateFieldParamError: false,
        updateParamLoading: false,
        updateFieldAliasSuccess: false,
        updateFieldAliasLoading: false,
        isCreateFieldLoading: false,
        isUpdateFieldLoading: false,
        isCreateGroupLoading: false,
        updateFieldRankLoading: false,
        isGroupDeleted: false,
        isGroupCreated: false,
        updateGroupLoading: false,
        isGroupUpdated: false,
        isGroupRankUpdated: false,
        isRankFieldUpdated: false,
        updateFieldOptionRankLoading: false,
      };

      case DESTROY_FIELD_STATE:
        return {...state,...initialState };

    case RESET_STATE:
      return initialState;

    default:
      return state;
  }
};

export default fields;
