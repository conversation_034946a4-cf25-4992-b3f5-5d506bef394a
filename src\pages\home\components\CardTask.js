import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Drawer,
  Form,
  List,
  Select,
  Skeleton,
  Table,
  Tag,
  Tooltip,
  Typography,
  message,
} from "antd";
import { useTranslation } from "react-i18next";
import { AvatarChat } from "../../../components/Chat";
import { getName } from "../../layouts/chat/utils/ConversationUtils";
import ChoiceIcons from "../../components/ChoiceIcons";
import _ from "lodash";
import { generateAxios } from "../../../services/axiosInstance";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { FiEdit3 } from "react-icons/fi";
import moment from "moment";
import dayjs from "dayjs";
import UpdateTask from "../../tasks/UpdateTask";
import { URL_ENV } from "index";
import SelectPipelinesStages from "components/SelectPipelinesStages";
import { formatDateForDisplay } from "pages/tasks/helpers/formatDateToDisplay";

const CardTask = ({ setTasks }) => {
  const [activeTabKey1, setActiveTabKey1] = useState("tab1");
  const [activeTabKey2, setActiveTabKey2] = useState("app");
  const [selectedStage, setSelectedStage] = useState("");
  const { user } = useSelector((state) => state?.user);

  const [externeUpdate, setExterneUpdate] = useState(false);
  const [search, setSearch] = useState("");
  const [open, setOpen] = useState(false);
  // const [loadTabs, setLoadTabs] = useState(true);
  // const [loadDetails, setLoadDetails] = useState(false);
  const [guestsListPage, setGuestsListPage] = useState(1);
  const [guestsList, setGuestsList] = useState([]);
  const [guestsListLastPage, setGuestsListLastPage] = useState(null);
  const [followersListPage, setFollowersListPage] = useState(1);
  const [ownersList, setOwnersList] = useState([]);
  const [followersListLastPage, setFollowersListLastPage] = useState(null);
  const [guestsSearchQuery, setGuestsSearchQuery] = useState("");
  const [followersSearchQuery, setFollowersSearchQuery] = useState("");
  const language = localStorage.getItem("language");
  const [activityLabel, setActivityLabel] = useState("");

  const { tasks, stages, iconsTasks, loading } = useSelector(
    (state) => state.dashboardRealTime
  );

  const [idTask, setIdTask] = useState("");
  const { Link } = Typography;
  const navigate = useNavigate();
  const prioritySelectStyles = {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  };
  const [form] = Form.useForm();
  const [t] = useTranslation("common");

  const prioritiesList = [
    {
      value: "low",
      label: (
        <Badge
          style={prioritySelectStyles}
          color="#52c41a"
          text={<div className="text-[#52c41a]">{t("tasks.lowPriority")}</div>}
        />
      ),
    },
    {
      value: "medium",
      label: (
        <Badge
          style={prioritySelectStyles}
          color="#fadb14"
          text={
            <div className="text-[#fadb14]">{t("tasks.mediumPriority")}</div>
          }
        />
      ),
    },
    {
      value: "high",
      label: (
        <Badge
          style={prioritySelectStyles}
          color="#fa8c16"
          text={<div className="text-[#fa8c16]">{t("tasks.highPriority")}</div>}
        />
      ),
    },
    {
      value: "urgent",
      label: (
        <Badge
          style={prioritySelectStyles}
          color="#f5222d"
          text={
            <div className="text-[#f5222d]">{t("tasks.urgentPriority")}</div>
          }
        />
      ),
    },
  ];
  const tabList = [
    {
      key: "tab1",
      tab: `To do today${
        !loading ? " (" + tasks.length + ")" : ""
        // tasks?.filter((el) => el.owner_id.id === user?.id).length
      } `,
    },
    // {
    //   key: 'tab2',
    //   tab: 'Late',
    // },
  ];
  const columns = [
    {
      title: t("tasks.tableType"),
      dataIndex: "activityType",
      key: "activityType",
      width: 100,
      render: (_, record) => (
        <>
          {!loading ? (
            <Tooltip title={record?.label} trigger={["hover"]}>
              <ChoiceIcons
                fontSize="18px"
                icon={
                  iconsTasks?.tasks_type?.find(
                    (el) => el.id === record.tasks_type_id
                  )?.icons
                }
              />
            </Tooltip>
          ) : (
            <Skeleton.Avatar
              shape={"button"}
              block={loading}
              active={loading}
            />
          )}
        </>
      ),
    },

    {
      title: t("tasks.tableLabel"),
      dataIndex: "label",
      key: "label",
      // width: "200px",
      render: (_, record) => (
        <>
          {!loading ? (
            <Link
              onClick={() => {
                setOpen(true);
                setIdTask(record.id);
                setActivityLabel(record?.label);

                // dispatch(setOpenTaskDrawer(true));
              }}
              className="flex items-center justify-between"
            >
              {record.label} <FiEdit3 />
            </Link>
          ) : (
            <Skeleton.Button className="w-full" />
          )}
        </>
      ),
      sorter: (a, b) => a?.label.localeCompare(b?.label),
    },
    {
      title: t("tasks.tablePriority"),
      dataIndex: "priority",
      key: "priority",
      width: "150px",
      filters: [
        {
          text: <Badge color="#52c41a" text={t("tasks.lowPriority")} />,
          value: "low",
        },
        {
          text: <Badge color="#fadb14" text={t("tasks.mediumPriority")} />,
          value: "medium",
        },
        {
          text: <Badge color="#fa8c16" text={t("tasks.highPriority")} />,
          value: "high",
        },
        {
          text: <Badge color="#f5222d" text={t("tasks.urgentPriority")} />,
          value: "urgent",
        },
      ],
      onFilter: (value, record) => record.priority == value,

      render: (_, record) => {
        return (
          <>
            {!loading ? (
              prioritiesList.find((el) => el.value === record.priority)?.label
            ) : (
              <Skeleton.Button className="w-full" />
            )}
          </>
        );
      },
    },
    {
      title: t("tasks.startDate"),
      dataIndex: "startDate",
      key: "startDate",
      render: (_, props) => {
        return (
          <>
            {!loading ? (
              props?.start_date !== null ? (
                <Tooltip title={`${props?.start_date} ${props?.start_time}`}>
                  {formatDateForDisplay(
                    `${props?.start_date} ${props?.start_time}`,
                    `${user?.location?.date_format} ${user?.location?.time_format}`,
                    user,
                    t
                  )}
                </Tooltip>
              ) : (
                <Typography.Link
                // onClick={() => {
                //   editTask(props);
                // }}
                >
                  {t("tasks.addDate")}
                </Typography.Link>
              )
            ) : (
              <Skeleton.Button className="w-full" />
            )}
          </>
        );
      },
    },
    {
      title: t("tasks.tableEndDate"),
      dataIndex: "dueDate",
      key: "dueDate",
      render: (_, record) => {
        return (
          <>
            {!loading ? (
              record?.end_date != null ? (
                <Tooltip title={`${record?.end_date} ${record?.end_time}`}>
                  <Typography.Text type={record?.is_overdue && "danger"}>
                    {formatDateForDisplay(
                      `${record?.end_date} ${record?.end_time}`,
                      `${user?.location?.date_format} ${user?.location?.time_format}`,
                      user,
                      t
                    )}
                  </Typography.Text>
                </Tooltip>
              ) : (
                <Typography.Link
                // onClick={() => {
                //   editTask(record);
                // }}
                >
                  {t("tasks.addDate")}
                </Typography.Link>
              )
            ) : (
              <Skeleton.Button className="w-full" />
            )}
          </>
        );
      },
    },

    {
      title: "Pipeline/Stage",
      dataIndex: "pipeline_label",
      key: "pipeline_label",
      width: "180px",
      render: (_, record) => (
        <>
          {loading ? (
            <Skeleton.Button className="w-full" />
          ) : record.stage_id ? (
            <SelectPipelinesStages
              stages={stages}
              record={record}
              iconsTasks={iconsTasks}
              setSelectedStage={setSelectedStage}
              selectedStage={selectedStage}
            />
          ) : (
            ""
          )}
        </>
      ),
    },
    {
      title: t("tasks.tableOwner"),
      dataIndex: "owner",
      key: "owner",
      width: "150px",
      render: (_, record) => {
        return (
          <>
            {loading ? (
              <Skeleton.Avatar shape={"circle"} />
            ) : (
              <div className="flex justify-start">
                <Tooltip
                  title={record?.owner_id?.label
                    ?.replace("_", " ")
                    .split(" ")
                    .reverse()
                    .join(" ")}
                  placement="top"
                >
                  <div>
                    <AvatarChat
                      fontSize="0.875rem"
                      type="user"
                      url={
                        URL_ENV?.REACT_APP_BASE_URL +
                        URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                        record.owner_id?.avatar
                      }
                      // size={30}
                      // height={8}
                      // width={8}
                      name={getName(record?.name, "avatar")}
                      hasImage={
                        record.owner_id?.avatar &&
                        record.owner_id?.avatar !== "/storage/uploads/"
                      }
                    />
                  </div>

                  {/* <Avatar
          style={{
            backgroundColor: "rgb(114, 101, 230)",
            verticalAlign: 'middle',
            // position: 'static',
          }}
          // gap={gap}
          maxStyle={{
            position:"static"
          }}
        >
               {record.owner_id.label?.replace("_"," ").split(" ").reverse().join("")[0]} 

        </Avatar> */}
                </Tooltip>
              </div>
            )}
          </>
        );
      },
    },
    // {
    //   title: t("tasks.tableGuests"),
    //   dataIndex: "guests",
    //   key: "guests",
    //   width: 180,

    // },
  ];
  const contentList = {
    tab1: (
      <Table
        columns={columns}
        dataSource={
          tasks
          // user?.id ? tasks.filter((el) => el?.owner_id?.id === user?.id) : tasks
        }
        scroll={{ y: "calc(100vh - 200px)" }}
        pagination={false}
        loading={tasks.length < 1 ? loading : false}
      />
    ),

    tab2: <p>content2</p>,
  };
  const onTab1Change = (key) => {
    setActiveTabKey1(key);
  };
  const onTab2Change = (key) => {
    setActiveTabKey2(key);
  };

  return (
    <>
      <Card
        style={{
          width: "100%",
        }}
        title={t("menu1.tasks")}
        extra={
          <Typography.Link onClick={() => navigate("/tasks")}>
            {t("dashboard.goToModuleTask")}
          </Typography.Link>
        }
        // tabList={tabList}
        activeTabKey={activeTabKey1}
        onTabChange={onTab1Change}
      >
        {/* <div
          className=" h-full w-full flex-col items-center justify-center bg-cover bg-center "
          // 
        > */}

        {/* </div> */}

        {contentList[activeTabKey1]}
      </Card>

      {idTask ? (
        <UpdateTask
          open={open}
          setOpen={setOpen}
          id={idTask}
          externeUpdate={externeUpdate}
          setId={setIdTask}
          data={{
            ...tasks.find((el) => el.id === idTask),
            upload:
              tasks.find((el) => el.id === idTask).upload == 0
                ? null
                : Array.isArray(tasks.find((el) => el.id === idTask)?.upload)
                ? tasks.find((el) => el.id === idTask)?.upload
                : tasks.find((el) => el.id === idTask)?.files,
          }}
          tasksTypes={iconsTasks?.tasks_type}
          pipelines={stages}
          ownersList={ownersList}
          setOwnersList={setOwnersList}
          guestsList={guestsList}
          setGuestsList={setGuestsList}
          guestsListPage={guestsListPage}
          setGuestsListPage={setGuestsListPage}
          followersListPage={followersListPage}
          setFollowersListPage={setFollowersListPage}
          followersListLastPage={followersListLastPage}
          setFollowersListLastPage={setFollowersListLastPage}
          setGuestsListLastPage={setGuestsListLastPage}
          guestsListLastPage={guestsListLastPage}
          setFollowersSearchQuery={setFollowersSearchQuery}
          followersSearchQuery={followersSearchQuery}
          setGuestsSearchQuery={setGuestsSearchQuery}
          guestsSearchQuery={guestsSearchQuery}
          from="home"
          activityLabel={activityLabel}
        />
      ) : (
        ""
      )}
      {/* </Drawer>{" "} */}
    </>
  );
};
export default CardTask;
