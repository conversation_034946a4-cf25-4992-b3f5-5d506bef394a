import { useEffect, useState } from "react";
import { <PERSON><PERSON>, Drawer, Space } from "antd";
import {
  CloseOutlined,
  FullscreenExitOutlined,
  FullscreenOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import LayoutDetails from "pages/components/DetailsProfile/LayoutDetails";
import { useWindowSize } from "pages/clients&users/components/WindowSize";
import { checkIfPathOnView360, findNextZIndex } from "../helpers/helpersFunc";
import ViewSphere from "pages/components/DetailsProfile/ViewSphere";
import { useDispatch } from "react-redux";
import { SET_CONTACT_INFO_FROM_DRAWER } from "new-redux/constants";
import {
  addLastIdToViewSphere,
  setNewInteraction,
  setOpenView360InDrawer,
} from "new-redux/actions/vue360.actions/vue360";
import { useLocation, useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import {
  familyIcons,
  pathViewSphere,
} from "pages/components/DetailsProfile/ViewSphere2";

const Drawer360 = ({ isOpen, elementInfo, handleDrawerSiderLayout }) => {
  //
  const dispatch = useDispatch();
  const [t] = useTranslation("common");
  const location = useLocation();
  const windowSize = useWindowSize();
  const { contactInfoFromDrawer } = useSelector((state) => state?.vue360);
  //
  const [isMinimized, setIsMinimized] = useState(true);
  const navigate = useNavigate();
  //
  useEffect(() => {
    if (!isOpen) setIsMinimized(true);
  }, [isOpen]);
  //
  const closeDrawer = (method) => {
    dispatch(setNewInteraction({ type: "updateElement" }));
    dispatch({ type: SET_CONTACT_INFO_FROM_DRAWER, payload: {} });
    !checkIfPathOnView360(location.pathname) &&
      dispatch({
        type: "RESET_CONTACT_HEADER_INFO",
      });
    method === "navigate" && dispatch({ type: "RESET_CONTACT_HEADER_INFO" });
    dispatch(setOpenView360InDrawer(false));
    handleDrawerSiderLayout("close");
  };
  //
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === "Escape" && isOpen) {
        closeDrawer();
      }
    };

    window.addEventListener("keydown", handleKeyDown);

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen]);
  //
  const handleMinimized = () => {
    if (isMinimized) {
      dispatch(setOpenView360InDrawer(true));
      dispatch(setNewInteraction({ type: "updateElementFromDrawer" }));
      dispatch({
        type: SET_CONTACT_INFO_FROM_DRAWER,
        payload: { id: elementInfo?.id } || {},
      });
      dispatch(addLastIdToViewSphere(elementInfo?.id));
    } else {
      dispatch(setOpenView360InDrawer(false));
    }
    setIsMinimized((prev) => !prev);
  };
  //
  useEffect(() => {
    if (checkIfPathOnView360(location.pathname)) {
      closeDrawer("navigate");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location]);
  //
  return (
    <Drawer
      open={isOpen && elementInfo?.id}
      onClose={() => closeDrawer()}
      extra={
        // <Tooltip placement="bottomRight" title={isMinimized ? t("voip.fullScreen") : t("voip")}>
        <Space>
          {!isMinimized && (
            <div className="flex items-center justify-between gap-2">
              <Button
                type="link"
                onClick={() =>
                  navigate(
                    pathViewSphere(
                      contactInfoFromDrawer?.family_id,
                      contactInfoFromDrawer?.id
                    )
                  )
                }
              >
                {contactInfoFromDrawer?.name || contactInfoFromDrawer?.reference
                  ? t("chat.goto")
                  : null}{" "}
                {contactInfoFromDrawer?.name ||
                  contactInfoFromDrawer?.reference}
              </Button>
            </div>
          )}
          <Button
            disabled={!elementInfo?.family_id || !elementInfo?.id}
            size="small"
            shape="circle"
            type="text"
            icon={
              isMinimized ? (
                <FullscreenOutlined style={{ fontSize: 18 }} />
              ) : (
                <FullscreenExitOutlined style={{ fontSize: 18 }} />
              )
            }
            onClick={handleMinimized}
          />
        </Space>

        // </Tooltip>
      }
      width={isMinimized ? "" : Math.max(windowSize.width * 0.75, 1080)}
      // bodyStyle={{ padding: 0 }}
      styles={{ body: { padding: 0 } }}
      mask={false}
      // zIndex={findNextZIndex()}
    >
      {isOpen && isMinimized ? (
        <LayoutDetails
          elementID={elementInfo?.id}
          source={isMinimized ? "webPhone" : null}
          isMinimized={isMinimized}
          handleMinimized={handleMinimized}
        />
      ) : isOpen && !isMinimized ? (
        <ViewSphere elementId={contactInfoFromDrawer?.id || elementInfo?.id} />
      ) : null}
    </Drawer>
  );
};

export default Drawer360;
