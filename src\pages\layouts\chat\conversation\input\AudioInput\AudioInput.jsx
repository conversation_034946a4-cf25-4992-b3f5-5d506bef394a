// import React, { useState, useRef, useEffect, useCallback } from "react";
// import { ReactMic } from "react-mic";
// import WaveSurfer from "wavesurfer.js";
// import "./style.css";
// import { Button, FloatButton, Input } from "antd";
// import {
//   AudioOutlined,
//   DeleteOutlined,
//   PauseOutlined,
//   PlayCircleOutlined,
//   SendOutlined,
// } from "@ant-design/icons";
// // import WaveSurferCursor from "wavesurfer.js/dist/plugin/wavesurfer.cursor.min.js";
// import { connect, useDispatch, useSelector } from "react-redux";
// import { scrollToBottom } from "../../../../../../new-redux/actions/chat.actions/Input";
// import { stopSearchMsg } from "../../../../../../new-redux/actions/chat.actions";
// import { useActionMessage } from "../../../hooks/useActionMessage";
// import { BiStopCircle } from "react-icons/bi";
// import { useTranslation } from "react-i18next";
// import { useLocation, useNavigate } from "react-router-dom";
// import {
//   FiMic,
//   FiPauseCircle,
//   FiPlay,
//   FiPlayCircle,
//   FiStopCircle,
//   FiTrash2,
// } from "react-icons/fi";

// function AudioInput({
//   open,
//   close,
//   isRecording,
//   setIsRecording,
//   isFullURL,
//   setRecordedBlobNotes,
// }) {
//   const { t } = useTranslation("common");
//   const [recordedBlob, setRecordedBlob] = useState(null);
//   const [recordingTime, setRecordingTime] = useState(0);
//   const [recordingTimeVoice, setRecordingTimeVoice] = useState(0);

//   const [stopRecording, setStopRecording] = useState(false);
//   const [sendmessage, setSendMessage] = useState(false);

//   const [intervalId, setIntervalId] = useState(null);
//   const [fullTime, setFullTime] = useState(0);
//   const reactMicRef = useRef(null);
//   const wavesurferRef = useRef(null);
//   const audioRef = useRef(null);
//   const timelineRef = useRef(null);
//   const { selectedConversation } = useSelector((state) => state.ChatRealTime);
//   const dispatch = useDispatch();
//   const [isPlaying, setIsPlaying] = useState(false);
//   function formatTimeCallback(seconds, pxPerSec) {
//     seconds = Number(seconds);
//     var minutes = Math.floor(seconds / 60);
//     seconds = seconds % 60;

//     // fill up seconds with zeroes
//     var secondsStr = Math.round(seconds).toString();
//     if (pxPerSec >= 25 * 10) {
//       secondsStr = seconds.toFixed(2);
//     } else if (pxPerSec >= 25 * 1) {
//       secondsStr = seconds.toFixed(1);
//     }

//     if (minutes > 0) {
//       if (seconds < 10) {
//         secondsStr = "0" + secondsStr;
//       }
//       return `${minutes}:${secondsStr}`;
//     }
//     return secondsStr;
//   }

//   /**
//    * Use timeInterval to set the period between notches, in seconds,
//    * adding notches as the number of pixels per second increases.
//    *
//    * Note that if you override the default function, you'll almost
//    * certainly want to override formatTimeCallback, primaryLabelInterval
//    * and/or secondaryLabelInterval so they all work together.
//    *
//    * @param: pxPerSec
//    */
//   function timeInterval(pxPerSec) {
//     var retval = 1;
//     if (pxPerSec >= 25 * 100) {
//       retval = 0.01;
//     } else if (pxPerSec >= 25 * 40) {
//       retval = 0.025;
//     } else if (pxPerSec >= 25 * 10) {
//       retval = 0.1;
//     } else if (pxPerSec >= 25 * 4) {
//       retval = 0.25;
//     } else if (pxPerSec >= 25) {
//       retval = 1;
//     } else if (pxPerSec * 5 >= 25) {
//       retval = 5;
//     } else if (pxPerSec * 15 >= 25) {
//       retval = 15;
//     } else {
//       retval = Math.ceil(0.5 / pxPerSec) * 60;
//     }
//     return retval;
//   }
//   /**
//    * Return the cadence of notches that get labels in the primary color.
//    * EG, return 2 if every 2nd notch should be labeled,
//    * return 10 if every 10th notch should be labeled, etc.
//    *
//    * Note that if you override the default function, you'll almost
//    * certainly want to override formatTimeCallback, primaryLabelInterval
//    * and/or secondaryLabelInterval so they all work together.
//    *
//    * @param pxPerSec
//    */
//   function primaryLabelInterval(pxPerSec) {
//     console.log(pxPerSec);
//     var retval = 1;
//     if (pxPerSec >= 25 * 100) {
//       retval = 10;
//     } else if (pxPerSec >= 25 * 40) {
//       retval = 4;
//     } else if (pxPerSec >= 25 * 10) {
//       retval = 10;
//     } else if (pxPerSec >= 25 * 4) {
//       retval = 4;
//     } else if (pxPerSec >= 25) {
//       retval = 1;
//     } else if (pxPerSec * 5 >= 25) {
//       retval = 5;
//     } else if (pxPerSec * 15 >= 25) {
//       retval = 15;
//     } else {
//       retval = Math.ceil(0.5 / pxPerSec) * 60;
//     }
//     return retval;
//   }

//   /**
//    * Return the cadence of notches to get labels in the secondary color.
//    * EG, return 2 if every 2nd notch should be labeled,
//    * return 10 if every 10th notch should be labeled, etc.
//    *
//    * Secondary labels are drawn after primary labels, so if
//    * you want to have labels every 10 seconds and another color labels
//    * every 60 seconds, the 60 second labels should be the secondaries.
//    *
//    * Note that if you override the default function, you'll almost
//    * certainly want to override formatTimeCallback, primaryLabelInterval
//    * and/or secondaryLabelInterval so they all work together.
//    *
//    * @param pxPerSec
//    */
//   function secondaryLabelInterval(pxPerSec) {
//     // draw one every 10s as an example
//     return Math.floor(10 / timeInterval(pxPerSec));
//   }
//   const navigate = useNavigate();
//   const { pathname } = useSelector((state) => state.form);
//   useEffect(() => {
//     setIsRecording(false);
//     setRecordedBlob(null);
//     setStopRecording(false);
//     return () => {
//       setIsRecording(false);
//       // wavesurferRef.current.destroy();
//       handleDeleteRecording();
//       setRecordedBlob(null);
//     };
//   }, [selectedConversation?.id]);
//   useEffect(() => {
//     // Initialiser le waveform de Wavesurfer lors du montage du composant
//     wavesurferRef.current = WaveSurfer.create({
//       container: "#waveform",
//       waveColor: "gray",
//       progressColor: "gray",
//       height: 40,
//       // barHeight: 2,
//       // container: "#waveform",
//       // waveColor: "gray",
//       // progressColor: "gray",
//       // barWidth: 3,
//       // barHeight: 2,
//       // height: 50,
//       // barMinHeight: 1,
//       // responsive: true,
//       // autoCenterImmediately: true,
//       // scrollParent: true,
//       // barGap: 3,
//       // plugins: [
//       //   WaveSurferTimeline.create({
//       //     container: timelineRef.current,
//       //     formatTimeCallback: formatTimeCallback,
//       //     timeInterval: timeInterval,
//       //     primaryLabelInterval: primaryLabelInterval,
//       //     secondaryLabelInterval: secondaryLabelInterval,
//       //     primaryColor: "blue",
//       //     secondaryColor: "red",
//       //     primaryFontColor: "blue",
//       //     secondaryFontColor: "red",
//       //   }),
//       // ],
//       // normalize: true,
//     });
//     wavesurferRef.current.on("ready", () => {
//       // Faites quelque chose lorsque l'enregistrement est terminé
//       setIsRecording(false);
//     });
//     wavesurferRef.current.on("pause", () => {
//       // Faites quelque chose lorsque l'enregistrement est terminé
//     });
//     wavesurferRef.current.on("play", () => {
//       // Faites quelque chose lorsque l'enregistrement est terminé
//       handlePlayback();
//     });

//     return () => {
//       // Nettoyer Wavesurfer lors du démontage du composant
//       wavesurferRef.current.destroy();
//     };
//   }, []);
//   useEffect(() => {
//     if (isRecording && !recordedBlob) {
//       const timer = setTimeout(() => {
//         setIsRecording(false);
//         handleStopRecording();
//         setFullTime(60);
//         setRecordingTimeVoice(60);
//       }, 60000);

//       return () => clearTimeout(timer);
//     }
//   }, [isRecording, recordedBlob]);
//   useEffect(() => {
//     if (open && !recordedBlob) {
//       handleStartRecording();
//     }
//     if (!open && !recordedBlob?.blob) {
//       handleDeleteRecording();
//     }
//     if (!open && recordedBlob?.blob) {
//       setRecordedBlob(null);
//     }
//     // return () => {
//     //   if (open && recordedBlob) setRecordedBlob(null);
//     // };
//   }, [open, recordedBlob, wavesurferRef.current]);
//   useEffect(() => {
//     const handleBeforeUnload = (event) => {
//       if (isRecording) {
//         event.preventDefault();

//         event.returnValue = "";
//       }
//     };

//     const handleNavigationClick = (event) => {
//       if (isRecording) {
//         event.preventDefault();
//         setIsRecording(false);

//         // Vous pouvez également afficher un message d'alerte ou une confirmation pour informer l'utilisateur
//         // qu'il est en cours d'enregistrement et lui demander s'il souhaite continuer la navigation.
//       }
//     };

//     window.addEventListener("beforeunload", handleBeforeUnload);
//     const navigationIcons = document.querySelectorAll(".navigation-icon");
//     navigationIcons.forEach((icon) => {
//       icon.addEventListener("click", handleNavigationClick);
//     });

//     return () => {
//       if (isRecording) {
//         window.removeEventListener("beforeunload", handleBeforeUnload);
//         navigationIcons.forEach((icon) => {
//           icon.removeEventListener("click", handleNavigationClick);
//         });
//       }
//     };
//   }, [isRecording]);
//   useEffect(() => {
//     {
//       if (!isFullURL) {
//         if (open && pathname)
//           setTimeout(() => {
//             close();
//             // if (!isFullURL) {
//             navigate(pathname);
//             // }
//           }, 200);
//       }
//     }
//   }, [pathname, navigate, open]);

//   useEffect(() => {
//     wavesurferRef.current.on("finish", () => {
//       // Faites quelque chose lorsque l'enregistrement est terminé
//       clearInterval(intervalId);
//       setIsPlaying(false);
//       wavesurferRef.current.seekTo(0);
//       setRecordingTime(0);

//       // setTimeout(() => {
//       //   clearInterval(intervalId);
//       // }, Math.floor(wavesurferRef.current.getDuration() * 1000));

//       // Effectuez les actions nécessaires pour revenir à 0
//       // ...
//     });
//     wavesurferRef.current.on("seek", (progress) => {
//       // Récupérez la position en seconde où vous avez cliqué
//       const duration = wavesurferRef.current.getDuration();
//       const clickedTime = progress * duration;
//       setRecordingTime(Math.floor(Number(clickedTime)));
//       clearInterval(intervalId);
//       // startRecordingTime();

//       // startRecordingTime();
//     });
//   }, [intervalId]);

//   const {
//     mutate: handleActionMessage,
//     isSuccess: isMessageSuccess,
//     isLoading,
//     isError,
//     isIdle,
//   } = useActionMessage("send_message_voice");

//   const sendMessage = useCallback(async () => {
//     dispatch(stopSearchMsg(true));
//     handleActionMessage({
//       message_id: null,
//       params: {
//         voice: recordedBlob?.blob,
//         voice_size: formatTime(recordingTimeVoice),
//       },

//       type_conversation: selectedConversation?.type,
//       type_action: "send_voice_message",
//     });
//     setRecordingTimeVoice(0);

//     // alert(recordingTimeVoice);
//   }, [
//     selectedConversation?.type,
//     handleActionMessage,
//     dispatch,
//     recordedBlob,
//     isLoading,
//   ]);

//   useEffect(() => {
//     if (sendmessage && recordedBlob && !isError) {
//       sendMessage();
//     }
//     if (isError) {
//       close();
//     }
//     return () => {
//       if (sendmessage && recordedBlob && !isError) {
//         setSendMessage(false);
//       }
//     };
//   }, [sendmessage, recordedBlob, isError]);
//   useEffect(() => {
//     if (isMessageSuccess) {
//       dispatch(scrollToBottom(Math.floor(Math.random() * 1000000 + 1)));
//       close();
//     }
//   }, [isMessageSuccess, dispatch]);
//   const handleStartRecording = () => {
//     setIsRecording(true);

//     setRecordingTime(0);

//     startRecordingTime();
//   };
//   const handleStopRecording = () => {
//     setIsRecording(false);
//     setFullTime(recordingTime);
//     setStopRecording(true);
//     stopRecordingTime();
//   };

//   const handleData = (recorded) => {
//     setIsRecording(false);
//     wavesurferRef.current.load(recorded.blobURL);

//     setRecordedBlob(recorded);
//     if (isFullURL) {
//       setRecordedBlobNotes(recorded);
//     }
//     // audioRef.current.src = recordedBlob?.blobURL; // Assigner l'URL au lecteur audio
//     stopRecordingTime();
//   };

//   const onData = (e) => {
//     console.log(e);
//     setTimeout(() => {
//       setIsRecording(false);
//       handleData();
//       stopRecordingTime();
//     }, 5000);
//   };
//   const handlePlayback = () => {
//     clearInterval(intervalId);
//     setIsPlaying(true);
//     // setStopRecording(false);
//     // wavesurferRef.current.play(); // Démarrer la lecture avec Wavesurfer
//     // audioRef.current.addEventListener("timeupdate", handleTimeUpdate);
//     setRecordingTime(Math.floor(wavesurferRef.current.getCurrentTime()));
//     startRecordingTime();
//   };
//   const handlePause = () => {
//     setIsPlaying(false);

//     wavesurferRef.current.pause();
//     // setRecordingTime((prevTime) => prevTime);
//     clearInterval(intervalId);
//   };
//   const handleDeleteRecording = () => {
//     setIsRecording(false);
//     clearInterval(intervalId);
//     setRecordingTime(0);
//     setRecordingTimeVoice(0);
//     setRecordedBlob(null);
//     setStopRecording(false);
//     setIsPlaying(false);
//     setIsRecording(false);
//     clearInterval(intervalId);

//     close();
//   };
//   const handleTimeUpdate = () => {
//     setRecordingTime(audioRef.current.currentTime);
//   };
//   const startRecordingTime = () => {
//     clearInterval(intervalId);

//     const timerId = setInterval(() => {
//       setRecordingTime((prevTime) => prevTime + 1);
//       setRecordingTimeVoice((prevTime) => prevTime + 1);
//     }, 1000);

//     setIntervalId(timerId);

//     // if (recordedBlob) {
//     //   // setTimeout(() => {
//     //   //   clearInterval(timerId);
//     //   // }, Math.floor(wavesurferRef.current.getDuration() * 1000) - Math.floor(wavesurferRef.current.getCurrentTime() * 1000));
//     //   setTimeout(() => {
//     //     clearInterval(timerId);
//     //   }, Math.floor(wavesurferRef.current.getDuration() * 1000));
//     // }
//   };

//   const stopRecordingTime = () => {
//     setRecordingTime(0);
//     clearInterval(intervalId);
//   };

//   function formatTime(seconds) {
//     const hours = Math.floor(seconds / 3600);
//     const minutes = Math.floor((seconds % 3600) / 60);
//     const remainingSeconds = seconds % 60;

//     const formattedHours = String(hours).padStart(2, "0");
//     const formattedMinutes = String(minutes).padStart(2, "0");
//     const formattedSeconds = String(remainingSeconds).padStart(2, "0");

//     return `${formattedMinutes}:${formattedSeconds}`;
//   }
//   const deleteAfterRecord = () => {
//     wavesurferRef.current.pause();
//     setRecordedBlob(null);
//     setStopRecording(false);
//     setIsPlaying(false);
//     setIsRecording(false);
//     if (isFullURL) {
//       setRecordedBlobNotes(null);
//     }
//     close();
//   };

//   return (
//     <div className={`app-container z-20 flex h-[40px] items-center `}>
//       {/* {recordedBlob && !isRecording && (
//               <Button
//               type="text"
//               size="small"
//               shape="circle"
//               onClick={deleteAfterRecord}

//       icon={ <DeleteOutlined
//               className="hover:opacity-70"
//               style={{fontSize:"18px"}}          
              
//               />
//       }
//       disabled={isLoading}
//               />
//    ) } */}
//       <div
//         className="flex items-center justify-between"
//         style={{
//           display: recordedBlob || isRecording ? "flex" : "none",
//           width: "400px",
//           height: "40px",
//         }}>
//         <div className="z-20 flex items-center space-x-2">
//           {recordedBlob && !isRecording && stopRecording && (
//             <div className="space-x-2">
//               <Button
//                 type="text"
//                 size="default"
//                 shape="circle"
//                 danger
//                 onClick={deleteAfterRecord}
//                 icon={
//                   <FiTrash2
//                     style={{
//                       fontSize: "18px",
//                     }}
//                   />
//                 }
//                 className="hover:text-blue-600"
//                 disabled={isLoading}
//               />
//               {isPlaying ? (
//                 <Button
//                   type="text"
//                   size="default"
//                   shape="circle"
//                   className="hover:text-blue-600"
//                   onClick={handlePause}
//                   icon={
//                     <FiPauseCircle
//                       className="hover:text-blue-600"
//                       style={{
//                         fontSize: "18px",
//                       }}
//                     />
//                   }
//                 />
//               ) : (
//                 <Button
//                   type="text"
//                   size="default"
//                   shape="circle"
//                   onClick={() => {
//                     clearInterval(intervalId);
//                     wavesurferRef.current.play();
//                   }}
//                   icon={
//                     <FiPlayCircle
//                       className="hover:text-blue-600"
//                       style={{
//                         fontSize: "18px",
//                       }}
//                     />
//                   }
//                   disabled={isLoading}
//                 />
//               )}
//             </div>
//           )}
//           <div
//             id="waveform"
//             className="waveform"
//             style={{
//               display: recordedBlob && stopRecording ? "block" : "none",
//               // width: recordedBlob && !isRecording ? "auto" : "0",
//             }}></div>
//           {recordedBlob && !isRecording && stopRecording && (
//             <div className="flex">
//               {" "}
//               <span className="w-[43px]">
//                 {formatTime(recordingTime)}
//               </span> / {formatTime(fullTime)}
//             </div>
//           )}
//         </div>
//         {/* {recordedBlob && !isRecording && (
       
//         )} */}
//         <div
//           style={{
//             display:
//               isRecording && !recordedBlob && !stopRecording ? "flex" : "none",
//             alignItems: "center",
//             width: "280px",
//           }}
//           className="space-x-2">
//           {open && isRecording && !recordedBlob && !stopRecording && (
//             <div className=" flex items-center space-x-2">
//               <div className="text-red-500">
//                 <FiMic className="animate-blink" style={{ fontSize: "18px" }} />
//               </div>

//               {isRecording && (
//                 <Button
//                   type="text"
//                   size="default"
//                   shape="circle"
//                   onClick={handleStopRecording}
//                   icon={
//                     <FiStopCircle
//                       className="hover:text-blue-600"
//                       style={{
//                         fontSize: "18px",
//                       }}
//                     />
//                   }
//                   disabled={isLoading}
//                 />
//               )}
//               <div>{formatTime(recordingTime)}</div>
//             </div>
//           )}

//           <ReactMic
//             record={isRecording}
//             onStop={handleData}
//             // onData={onData}
//             ref={reactMicRef}
//             className="sound-wave"
//             visualSetting="sinewave"
//             strokeColor="#000000"
//             backgroundColor="white"
//             mimeType="audio/webm"
//           />
//         </div>
//         {open && isRecording && !recordedBlob && !stopRecording && (
//           <Button
//             type="text"
//             size="default"
//             shape="circle"
//             className="ml-16"
//             danger
//             onClick={handleDeleteRecording}
//             icon={<FiTrash2 style={{ fontSize: "18px" }} />}
//           />
//         )}
//       </div>
//       <div ref={timelineRef} />
//       <div style={{ display: "flex", justifyContent: "justify-between" }}>
//         {/* {!stopRecording && !recordedBlob && !isRecording ? (
//           <Button
//             shape="circle"
//             type="primary"
//             onClick={handleStartRecording}
//             danger
//           >
//             <AudioOutlined />
//           </Button>
//         ) : (
//           ""
//         )} */}
//       </div>
//       {open && !isFullURL && (
//         <>
//           <Button
//             type="link"
//             icon={<SendOutlined />}
//             onClick={() => {
//               if (recordedBlob) sendMessage();
//               else {
//                 setIsRecording(false);
//                 setSendMessage(true);
//               }
//             }}
//             // disabled={!recordedBlob && isRecording}
//             loading={isLoading}
//           />
//           {isLoading ? t("chat.loading") + "..." : ""}
//         </>
//       )}
//     </div>
//   );
// }

// const mapStateToProps = (state) => {
//   return {
//     selectedConversation: state.chat.selectedConversation,
//   };
// };

// export default connect(mapStateToProps, { scrollToBottom, stopSearchMsg })(
//   React.memo(AudioInput)
// );
// import React from 'react'

// const AudioInput = () => {
//   return (
//     <div>AudioInput</div>
//   )
// }

// export default AudioInput
