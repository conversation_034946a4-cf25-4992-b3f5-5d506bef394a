import React, { useState, useRef, useCallback, useEffect } from "react";
import ReactDOM from "react-dom";
import {
  Button,
  message,
  Select,
  Upload,
  Modal,
  Slider,
  Switch,
  Popover,
  Input,
  Typography,
  Form,
  ColorPicker,
} from "antd";
import {
  Align<PERSON>enterOutlined,
  AlignLeftOutlined,
  AlignRightOutlined,
  BoldOutlined,
  CodeOutlined,
  DownloadOutlined,
  EyeOutlined,
  ItalicOutlined,
  LinkOutlined,
  OrderedListOutlined,
  PictureOutlined,
  QuestionCircleOutlined,
  SaveOutlined,
  UnderlineOutlined,
  UnorderedListOutlined,
  UploadOutlined,
  BorderOutlined, // Remplace SquareOutlined
} from "@ant-design/icons";
import { MdSmartButton } from "react-icons/md";
import { Resizable } from "react-resizable";
import "react-resizable/css/styles.css";
import useOnClickOutside from "pages/layouts/chat/hooks/useClickOutside";

const EmailTemplateEditor = () => {
  const [content, setContent] = useState("");
  const [subject, setSubject] = useState("");
  const [selectedColor, setSelectedColor] = useState("#000000");
  const [selectedBgColor, setSelectedBgColor] = useState("#ffffff");
  const [fontSize, setFontSize] = useState("14");
  const [fontFamily, setFontFamily] = useState("Arial");
  const [showPreview, setShowPreview] = useState(false);
  const [templates, setTemplates] = useState([]);
  const [showHtmlModal, setShowHtmlModal] = useState(false);
  const [htmlInput, setHtmlInput] = useState("");
  const [showButtonModal, setShowButtonModal] = useState(false);
  const [buttonConfig, setButtonConfig] = useState({
    text: "Cliquez ici",
    url: "#",
    bgColor: "#2196F3",
    textColor: "#ffffff",
    padding: "12px 24px",
    borderRadius: "5px",
  });

  // États pour le popover de lien
  const [linkPopoverVisible, setLinkPopoverVisible] = useState(false);
  const [linkUrl, setLinkUrl] = useState("");
  const [selectionPosition, setSelectionPosition] = useState(null);
  const [selectedText, setSelectedText] = useState("");
  const [currentSelection, setCurrentSelection] = useState(null);

  // États pour l'ajustement d'image
  const [imageModalVisible, setImageModalVisible] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [imageSrc, setImageSrc] = useState("");
  const [imageSize, setImageSize] = useState({ width: 300, height: 200 });
  const [originalSize, setOriginalSize] = useState({ width: 0, height: 0 });
  const [aspectRatio, setAspectRatio] = useState(1);
  const [lockAspectRatio, setLockAspectRatio] = useState(true);
  const imageRef = useRef(null);

  const editorRef = useRef(null);
  const editorContainerRef = useRef(null);
  const popoverRef = useRef(null);

  const colors = [
    "#000000",
    "#FF0000",
    "#00FF00",
    "#0000FF",
    "#FFFF00",
    "#FF00FF",
    "#00FFFF",
    "#FFA500",
    "#800080",
    "#008000",
    "#FFC0CB",
    "#A52A2A",
    "#808080",
    "#000080",
    "#800000",
  ];

  const fontFamilies = [
    "Arial",
    "Helvetica",
    "Times New Roman",
    "Georgia",
    "Verdana",
    "Trebuchet MS",
    "Impact",
    "Courier New",
  ];
  const initialButtonConfig = {
    text: "",
    url: "",
    bgColor: "#1890ff",
    textColor: "#ffffff",
    padding: "12px 24px",
    borderRadius: "5px",
  };
  const resetButtonModal = () => {
    setButtonConfig(initialButtonConfig);
    formInsertButton.resetFields();
    setShowButtonModal(false);
  };

  const [formInsertButton] = Form.useForm();

  const execCommand = useCallback((command, value = null) => {
    document.execCommand(command, false, value);
    if (editorRef.current) {
      editorRef.current.focus();
    }
  }, []);

  const handleColorChange = (color) => {
    setSelectedColor(color);
    execCommand("foreColor", color);
  };

  const handleBgColorChange = (color) => {
    setSelectedBgColor(color);
    execCommand("backColor", color);
  };

  const handleFontSizeChange = (size) => {
    setFontSize(size);
    execCommand("fontSize", size);
  };

  const handleFontFamilyChange = (family) => {
    setFontFamily(family);
    execCommand("fontName", family);
  };

  const insertLink = () => {
    const selection = window.getSelection();
    if (selection.toString().trim() === "") {
      alert("Veuillez sélectionner du texte avant d'ajouter un lien");
      return;
    }

    const url = prompt("Entrez l'URL du lien:");
    if (url && url.trim()) {
      execCommand("createLink", url.trim());
    }
  };

  const handleImageUpload = () => {
    // Sauvegarder la sélection actuelle avant d'ouvrir le modal
    saveSelection();

    const input = document.createElement("input");
    input.type = "file";
    input.accept = "image/*";

    input.onchange = (e) => {
      const file = e.target.files[0];
      if (!file) return;

      if (!file.type.startsWith("image/")) {
        message.error("Veuillez sélectionner un fichier image valide");
        return;
      }

      setSelectedImage(file);
      const url = URL.createObjectURL(file);
      setImageSrc(url);

      // Charger l'image pour obtenir ses dimensions originales
      const img = new Image();
      img.onload = () => {
        const width = img.width;
        const height = img.height;
        setOriginalSize({ width, height });
        setAspectRatio(width / height);

        // Définir une taille initiale raisonnable pour l'aperçu
        const maxWidth = 500;
        const maxHeight = 400;
        let newWidth = width;
        let newHeight = height;

        if (width > maxWidth) {
          newWidth = maxWidth;
          newHeight = maxWidth / (width / height);
        }

        if (newHeight > maxHeight) {
          newHeight = maxHeight;
          newWidth = maxHeight * (width / height);
        }

        setImageSize({ width: newWidth, height: newHeight });
        setImageModalVisible(true);
      };
      img.src = url;
    };

    input.click();
  };

  const insertImageFromUrl = () => {
    const url = prompt("Entrez l'URL de l'image:");
    if (url && url.trim()) {
      const img = document.createElement("img");
      img.src = url.trim();
      img.style.maxWidth = "100%";
      img.style.height = "auto";

      const selection = window.getSelection();
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        range.insertNode(img);
        range.setStartAfter(img);
        selection.removeAllRanges();
        selection.addRange(range);
      }
    }
  };
  const [savedSelection, setSavedSelection] = useState(null);

  // Fonction pour sauvegarder la sélection actuelle
  const saveSelection = () => {
    const selection = window.getSelection();
    if (selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      // Vérifier que la sélection est dans l'éditeur
      if (
        editorRef.current &&
        editorRef.current.contains(range.commonAncestorContainer)
      ) {
        setSavedSelection(range.cloneRange());
      }
    }
  };

  // Fonction pour restaurer la sélection sauvegardée
  const restoreSelection = () => {
    if (savedSelection) {
      const selection = window.getSelection();
      selection.removeAllRanges();
      selection.addRange(savedSelection);
      return true;
    }
    return false;
  };
  const insertButton = () => {
    const button = document.createElement("a");
    button.href = buttonConfig.url;
    button.textContent = buttonConfig.text;
    button.style.cssText = `
      display: inline-block;
      background-color: ${buttonConfig.bgColor};
      color: ${buttonConfig.textColor};
      padding: ${buttonConfig.padding};
      text-decoration: none;
      border-radius: ${buttonConfig.borderRadius};
      font-weight: bold;
      margin: 10px 0;
    `;

    // Essayer de restaurer la sélection sauvegardée
    if (restoreSelection()) {
      const selection = window.getSelection();
      const range = selection.getRangeAt(0);

      // Insérer le bouton à la position du curseur
      range.deleteContents();
      range.insertNode(button);

      // Placer le curseur après le bouton
      range.setStartAfter(button);
      range.setEndAfter(button);
      selection.removeAllRanges();
      selection.addRange(range);
    } else {
      // Fallback: vérifier s'il y a une sélection active
      const selection = window.getSelection();
      if (
        selection.rangeCount > 0 &&
        editorRef.current &&
        editorRef.current.contains(selection.focusNode)
      ) {
        const range = selection.getRangeAt(0);
        range.deleteContents();
        range.insertNode(button);
        range.setStartAfter(button);
        range.setEndAfter(button);
        selection.removeAllRanges();
        selection.addRange(range);
      } else {
        // Si aucune sélection, ajouter à la fin
        editorRef.current.appendChild(button);
      }
    }

    // Mettre à jour le contenu
    setContent(editorRef.current.innerHTML);

    resetButtonModal();
    setSavedSelection(null); // Nettoyer la sélection sauvegardée
  };
  const insertHtml = () => {
    if (!htmlInput.trim()) {
      message.warning("Veuillez saisir du code HTML");
      return;
    }

    try {
      // Créer un élément temporaire pour parser le HTML
      const tempDiv = document.createElement("div");
      tempDiv.innerHTML = htmlInput.trim();

      // Essayer de restaurer la sélection sauvegardée
      if (restoreSelection()) {
        const selection = window.getSelection();
        const range = selection.getRangeAt(0);

        // Insérer chaque nœud du HTML
        const fragment = document.createDocumentFragment();
        while (tempDiv.firstChild) {
          fragment.appendChild(tempDiv.firstChild);
        }

        // Supprimer le contenu sélectionné et insérer le fragment
        range.deleteContents();
        range.insertNode(fragment);

        // Placer le curseur après le contenu inséré
        range.collapse(false);
        selection.removeAllRanges();
        selection.addRange(range);
      } else {
        // Fallback: vérifier s'il y a une sélection active
        const selection = window.getSelection();
        if (
          selection.rangeCount > 0 &&
          editorRef.current &&
          editorRef.current.contains(selection.focusNode)
        ) {
          const range = selection.getRangeAt(0);

          // Créer un fragment pour insérer le HTML
          const fragment = document.createDocumentFragment();
          while (tempDiv.firstChild) {
            fragment.appendChild(tempDiv.firstChild);
          }

          range.deleteContents();
          range.insertNode(fragment);
          range.collapse(false);
          selection.removeAllRanges();
          selection.addRange(range);
        } else {
          // Si aucune sélection, ajouter à la fin de l'éditeur
          while (tempDiv.firstChild) {
            editorRef.current.appendChild(tempDiv.firstChild);
          }
        }
      }

      // Mettre à jour le contenu
      setContent(editorRef.current.innerHTML);

      // Initialiser les gestionnaires d'événements pour les nouveaux liens
      setTimeout(() => {
        initializeLinkHandlers();
      }, 100);

      message.success("HTML inséré avec succès");
    } catch (error) {
      console.error("Erreur lors de l'insertion du HTML:", error);
      message.error("Erreur lors de l'insertion du HTML");
    }

    // Nettoyer et fermer le modal
    setHtmlInput("");
    setShowHtmlModal(false);
    setSavedSelection(null);
  };
  const insertVariable = (variable) => {
    const selection = window.getSelection();
    if (selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const span = document.createElement("span");
      span.style.backgroundColor = "#e3f2fd";
      span.style.padding = "2px 4px";
      span.style.borderRadius = "3px";
      span.style.color = "#1976d2";
      span.textContent = `{{${variable}}}`;
      range.insertNode(span);
      range.setStartAfter(span);
      selection.removeAllRanges();
      selection.addRange(range);
    }
  };

  const saveTemplate = () => {
    const template = {
      id: Date.now(),
      name: prompt("Nom du template:") || "Template sans nom",
      subject,
      content: editorRef.current?.innerHTML || "",
      createdAt: new Date().toLocaleString(),
    };
    setTemplates([...templates, template]);
    alert("Template sauvegardé!");
  };

  const loadTemplate = (template) => {
    setSubject(template.subject);
    if (editorRef.current) {
      editorRef.current.innerHTML = template.content;
    }
  };

  const exportHTML = () => {
    const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${subject}</title>
    <style>
        body { font-family: ${fontFamily}, sans-serif; margin: 0; padding: 20px; }
        .email-container { max-width: 600px; margin: 0 auto; }
    </style>
</head>
<body>
    <div class="email-container">
        <h1>${subject}</h1>
        ${editorRef.current?.innerHTML || ""}
    </div>
</body>
</html>`;

    const blob = new Blob([htmlContent], { type: "text/html" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${subject || "email-template"}.html`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const predefinedTemplates = [
    {
      name: "Newsletter",
      subject: "Notre Newsletter Mensuelle",
      content:
        '<h2>Bonjour {{nom}},</h2><p>Nous sommes ravis de partager avec vous les dernières actualités...</p><div style="background: #f5f5f5; padding: 20px; margin: 20px 0; border-radius: 8px;"><h3>Actualités du mois</h3><ul><li>Nouveauté 1</li><li>Nouveauté 2</li><li>Nouveauté 3</li></ul></div><p>Cordialement,<br>L\'équipe</p>',
    },
    {
      name: "Bienvenue",
      subject: "Bienvenue chez nous !",
      content:
        '<h1 style="color: #2196F3;">Bienvenue {{nom}} !</h1><p>Nous sommes enchantés de vous accueillir dans notre communauté.</p><div style="text-align: center; margin: 30px 0;"><a href="#" style="background: #2196F3; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px;">Commencer</a></div><p>Si vous avez des questions, n\'hésitez pas à nous contacter.</p>',
    },
    {
      name: "Promotion",
      subject: "Offre Spéciale - 50% de Réduction !",
      content:
        '<div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px;"><h1>OFFRE LIMITÉE</h1><h2 style="font-size: 3em; margin: 20px 0;">50% OFF</h2><p style="font-size: 1.2em;">Valable jusqu\'au {{date_fin}}</p><a href="#" style="background: white; color: #667eea; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-weight: bold;">J\'en profite</a></div>',
    },
  ];

  // 1. Assurez-vous que la référence est correctement définie
  const fileInputRef = useRef(null);
  const openButtonModal = () => {
    saveSelection();
    setShowButtonModal(true);
  };
  const handleEditorClick = () => {
    // Attendre un peu pour que la sélection soit mise à jour
    setTimeout(() => {
      saveSelection();
    }, 10);
  };
  // 2. Créez une fonction dédiée pour ouvrir la fenêtre de sélection de fichier
  const openFileSelector = () => {
    // Vérifiez que la référence existe
    if (fileInputRef.current) {
      // Déclenchez un clic programmatique sur l'input file
      fileInputRef.current.click();
    } else {
      console.error("La référence à l'input file est null");
      message.error("Impossible d'ouvrir le sélecteur de fichier");
    }
  };

  // Fonction pour gérer le redimensionnement
  const handleResize = (event, { size }) => {
    const { width, height } = size;

    if (lockAspectRatio) {
      // Maintenir le ratio d'aspect
      if (
        Math.abs(width / imageSize.width - 1) >
        Math.abs(height / imageSize.height - 1)
      ) {
        // Largeur a changé plus que la hauteur
        setImageSize({
          width,
          height: width / aspectRatio,
        });
      } else {
        // Hauteur a changé plus que la largeur
        setImageSize({
          width: height * aspectRatio,
          height,
        });
      }
    } else {
      // Redimensionnement libre
      setImageSize({ width, height });
    }
  };

  // Méthode alternative pour insérer l'image
  const insertResizedImage = () => {
    if (!selectedImage || !imageSrc) {
      message.error("Aucune image sélectionnée");
      return;
    }

    try {
      // Créer un canvas pour redimensionner l'image
      const canvas = document.createElement("canvas");
      canvas.width = imageSize.width;
      canvas.height = imageSize.height;
      const ctx = canvas.getContext("2d");
      ctx.fillStyle = "#fff";
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Dessiner l'image redimensionnée sur le canvas
      const img = new Image();
      img.onload = () => {
        try {
          ctx.drawImage(img, 0, 0, imageSize.width, imageSize.height);

          // Convertir le canvas en URL de données
          const dataUrl = canvas.toDataURL("image/jpeg", 0.9);

          // Créer un élément img
          const imgElement = document.createElement("img");
          imgElement.src = dataUrl;
          imgElement.style.maxWidth = "100%";
          imgElement.style.height = "auto";

          // Essayer de restaurer la sélection sauvegardée
          if (restoreSelection()) {
            const selection = window.getSelection();
            const range = selection.getRangeAt(0);

            // Insérer l'image à la position du curseur
            range.deleteContents();
            range.insertNode(imgElement);

            // Placer le curseur après l'image
            range.setStartAfter(imgElement);
            range.setEndAfter(imgElement);
            selection.removeAllRanges();
            selection.addRange(range);
          } else {
            // Fallback: vérifier s'il y a une sélection active
            const selection = window.getSelection();
            if (
              selection.rangeCount > 0 &&
              editorRef.current &&
              editorRef.current.contains(selection.focusNode)
            ) {
              const range = selection.getRangeAt(0);
              range.deleteContents();
              range.insertNode(imgElement);
              range.setStartAfter(imgElement);
              range.setEndAfter(imgElement);
              selection.removeAllRanges();
              selection.addRange(range);
            } else {
              // Si aucune sélection, ajouter à la fin
              editorRef.current.appendChild(imgElement);
            }
          }

          // Mettre à jour le contenu
          setContent(editorRef.current.innerHTML);

          // Afficher un message de succès
          message.success("Image insérée avec succès");

          // Fermer le modal et nettoyer
          setImageModalVisible(false);
          URL.revokeObjectURL(imageSrc);
          setImageSrc("");
          setSelectedImage(null);
          setSavedSelection(null); // Nettoyer la sélection sauvegardée
        } catch (error) {
          console.error("Erreur lors du dessin de l'image:", error);
          message.error("Erreur lors du traitement de l'image");
        }
      };

      img.onerror = (error) => {
        console.error("Erreur lors du chargement de l'image:", error);
        message.error("Erreur lors du chargement de l'image");
      };

      img.src = imageSrc;
    } catch (error) {
      console.error("Erreur globale:", error);
      message.error("Une erreur est survenue");
    }
  };

  // Fonction pour réinitialiser la taille de l'image
  const resetImageSize = () => {
    // Calculer une taille qui tient dans la fenêtre modale
    const maxWidth = 500;
    const maxHeight = 400;
    let newWidth = originalSize.width;
    let newHeight = originalSize.height;

    if (newWidth > maxWidth) {
      newWidth = maxWidth;
      newHeight = maxWidth / aspectRatio;
    }

    if (newHeight > maxHeight) {
      newHeight = maxHeight;
      newWidth = maxHeight * aspectRatio;
    }

    setImageSize({ width: newWidth, height: newHeight });
  };

  // Ajoutez ces états et références
  const [selectionMarker, setSelectionMarker] = useState(null);
  const markerRef = useRef(null);

  // Ajoutez un état pour suivre si le texte sélectionné est un lien
  const [isSelectionLink, setIsSelectionLink] = useState(false);

  // Fonction pour vérifier si la sélection actuelle est un lien
  const checkIfSelectionIsLink = () => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return false;

    const range = selection.getRangeAt(0);

    // Vérifier si la sélection est à l'intérieur d'un lien
    let currentNode = range.commonAncestorContainer;

    // Si le nœud est un nœud de texte, remonter au parent
    if (currentNode.nodeType === 3) {
      currentNode = currentNode.parentNode;
    }

    // Vérifier si le nœud est un lien ou contient un lien
    return currentNode.tagName === "A" || currentNode.closest("a") !== null;
  };

  // Fonction pour gérer le clic sur le bouton link
  const handleLinkButtonClick = () => {
    const selection = window.getSelection();
    const selectedText = selection.toString().trim();

    if (selectedText === "") {
      message.warning("Veuillez sélectionner du texte avant d'ajouter un lien");
      return;
    }

    // Vérifier si la sélection est déjà un lien
    const isLink = checkIfSelectionIsLink();

    if (isLink) {
      // Si c'est déjà un lien, récupérer le nœud de lien
      let linkNode = selection.anchorNode;
      if (linkNode.nodeType === 3) {
        // Nœud de texte
        linkNode = linkNode.parentNode;
      }
      if (linkNode.tagName !== "A") {
        linkNode = linkNode.closest("a");
      }

      // Récupérer l'URL actuelle
      const currentUrl = linkNode.getAttribute("href");
      setLinkUrl(currentUrl);

      // Créer un range autour du lien complet
      const range = document.createRange();
      range.selectNodeContents(linkNode);

      // Sélectionner le lien complet
      selection.removeAllRanges();
      selection.addRange(range);

      // Mettre à jour la sélection
      setSelectedText(linkNode.textContent);
      setCurrentSelection({
        range: range.cloneRange(),
        text: linkNode.textContent,
      });
    } else {
      // Comportement normal pour un nouveau lien
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        setCurrentSelection({
          range: range.cloneRange(),
          text: selectedText,
        });
      }
    }

    // Obtenir la position de la sélection
    const rect = selection.getRangeAt(0).getBoundingClientRect();
    const editorRect = editorRef.current.getBoundingClientRect();

    // Calculer la position relative à l'éditeur
    setSelectionPosition({
      left: rect.left - editorRect.left + rect.width / 2,
      top: rect.bottom - editorRect.top,
    });

    // Ouvrir le popover
    setLinkPopoverVisible(true);
  };

  // Fonction pour appliquer le lien (mise à jour pour gérer à la fois les nouveaux liens et les modifications)
  const applyLink = () => {
    if (!currentSelection || !currentSelection.range) return;

    let url = linkUrl.trim();
    if (!url) {
      message.warning("Veuillez entrer une URL valide");
      return;
    }

    // Ajouter le préfixe https:// si nécessaire
    if (!url.startsWith("http://") && !url.startsWith("https://")) {
      url = `https://${url}`;
    }

    try {
      // Restaurer la sélection
      const selection = window.getSelection();
      selection.removeAllRanges();
      selection.addRange(currentSelection.range);

      // Obtenir le texte sélectionné
      const selectedText = selection.toString();

      // Vérifier si nous modifions un lien existant
      const isEditingLink = checkIfSelectionIsLink();

      if (isEditingLink) {
        // Si nous modifions un lien existant, nous mettons simplement à jour l'URL
        let linkNode = selection.anchorNode;
        if (linkNode.nodeType === 3) {
          linkNode = linkNode.parentNode;
        }
        if (linkNode.tagName !== "A") {
          linkNode = linkNode.closest("a");
        }

        // Mettre à jour l'URL du lien
        linkNode.setAttribute("href", url);
        linkNode.setAttribute("target", "_blank");
        linkNode.setAttribute("rel", "noopener noreferrer");
      } else {
        // Pour un nouveau lien, remplacer chaque espace par un underscore
        const modifiedText = selectedText.replace(/\s/g, "_");

        // Supprimer le contenu sélectionné
        selection.deleteFromDocument();

        // Créer un élément de lien
        const linkElement = document.createElement("a");
        linkElement.href = url;
        linkElement.textContent = modifiedText;
        linkElement.target = "_blank";
        linkElement.rel = "noopener noreferrer";

        // Insérer le lien à la position de la sélection
        const range = selection.getRangeAt(0);
        range.insertNode(linkElement);

        // Placer le curseur après le lien
        range.setStartAfter(linkElement);
        range.setEndAfter(linkElement);
        selection.removeAllRanges();
        selection.addRange(range);
      }

      // Mettre à jour le contenu
      setContent(editorRef.current.innerHTML);

      // Initialiser les gestionnaires d'événements pour les liens
      initializeLinkHandlers();
    } catch (error) {
      console.error("Erreur lors de l'application du lien:", error);
      message.error("Erreur lors de l'application du lien");
    } finally {
      // Réinitialiser et fermer le popover
      setLinkUrl("");
      setLinkPopoverVisible(false);
      setSelectionPosition(null);
      setCurrentSelection(null);

      // Supprimer le marqueur si nécessaire
      if (selectionMarker && selectionMarker.parentNode) {
        selectionMarker.parentNode.removeChild(selectionMarker);
      }
      setSelectionMarker(null);
    }
  };

  // Fonction pour annuler
  const cancelLink = () => {
    setLinkUrl("");
    setLinkPopoverVisible(false);
    setSelectionPosition(null);
    setCurrentSelection(null);

    // Supprimer le marqueur
    if (selectionMarker && selectionMarker.parentNode) {
      selectionMarker.parentNode.removeChild(selectionMarker);
    }
    setSelectionMarker(null);
  };

  // Ajoutez ces états pour gérer le popover de lien au survol
  const [hoveredLink, setHoveredLink] = useState(null);
  const [linkHoverPopoverVisible, setLinkHoverPopoverVisible] = useState(false);
  const [linkHoverPosition, setLinkHoverPosition] = useState(null);

  // Fonction pour initialiser les gestionnaires d'événements sur les liens
  const initializeLinkHandlers = () => {
    if (editorRef.current) {
      const links = editorRef.current.querySelectorAll("a");
      links.forEach((link) => {
        // S'assurer que tous les liens s'ouvrent dans un nouvel onglet
        link.setAttribute("target", "_blank");
        link.setAttribute("rel", "noopener noreferrer");

        // Supprimer les gestionnaires d'événements existants pour éviter les doublons
        const newLink = link.cloneNode(true);
        link.parentNode.replaceChild(newLink, link);

        // Ajouter les gestionnaires d'événements pour le hover
        newLink.addEventListener("mouseenter", (e) => {
          const rect = e.target.getBoundingClientRect();
          const editorRect = editorRef.current.getBoundingClientRect();

          setHoveredLink(e.target);
          setLinkHoverPosition({
            left: rect.left - editorRect.left + rect.width / 2,
            top: rect.bottom - editorRect.top,
          });
          setLinkHoverPopoverVisible(true);
        });

        // Pas besoin de mouseleave ici, car nous gérons cela dans le popover
      });

      // Ajouter un gestionnaire global pour détecter quand la souris quitte l'éditeur
      editorRef.current.addEventListener("mouseleave", () => {
        // Vérifier si la souris est sur le popover
        const isMouseOverPopover = document.querySelector(
          ".link-hover-popover:hover"
        );

        if (!isMouseOverPopover) {
          setTimeout(() => {
            setLinkHoverPopoverVisible(false);
          }, 200);
        }
      });
    }
  };

  // Fonction pour modifier un lien existant
  const editLink = () => {
    if (hoveredLink && hoveredLink.tagName === "A") {
      // Récupérer l'URL actuelle du lien
      const currentUrl = hoveredLink.getAttribute("href");
      setLinkUrl(currentUrl);

      // Créer un range autour du lien
      const range = document.createRange();
      range.selectNodeContents(hoveredLink);

      // Sélectionner le lien
      const selection = window.getSelection();
      selection.removeAllRanges();
      selection.addRange(range);

      // Sauvegarder la sélection
      setCurrentSelection({
        range: range.cloneRange(),
        text: hoveredLink.textContent,
      });

      // Obtenir la position du lien
      const rect = range.getBoundingClientRect();
      const editorRect = editorRef.current.getBoundingClientRect();

      setSelectionPosition({
        left: rect.left - editorRect.left + rect.width / 2,
        top: rect.bottom - editorRect.top,
      });

      // Fermer le popover de survol et ouvrir le popover d'édition
      setLinkHoverPopoverVisible(false);
      setLinkPopoverVisible(true);
    }
  };

  // Fonction pour supprimer un lien
  const removeLink = () => {
    if (hoveredLink && hoveredLink.tagName === "A") {
      // Créer un range autour du lien
      const range = document.createRange();
      range.selectNodeContents(hoveredLink);

      // Sélectionner le lien
      const selection = window.getSelection();
      selection.removeAllRanges();
      selection.addRange(range);

      // Supprimer le lien mais garder le texte
      document.execCommand("unlink");

      // Mettre à jour le contenu
      setContent(editorRef.current.innerHTML);

      // Fermer le popover
      setLinkHoverPopoverVisible(false);
      setHoveredLink(null);
    }
  };

  // Ajoutez cet useEffect pour initialiser les gestionnaires d'événements
  useEffect(() => {
    if (editorRef.current) {
      initializeLinkHandlers();
    }
  }, [content]); // Réexécuter lorsque le contenu change

  // Ajoutez un gestionnaire d'événements pour détecter les changements de sélection
  useEffect(() => {
    const handleSelectionChange = () => {
      const isLink = checkIfSelectionIsLink();
      setIsSelectionLink(isLink);
    };

    document.addEventListener("selectionchange", handleSelectionChange);

    return () => {
      document.removeEventListener("selectionchange", handleSelectionChange);
    };
  }, []);

  // Créez un composant pour le popover flottant
  const FloatingLinkPopover = () => {
    const popoverRef = useRef(null);

    // Utilisez le hook pour détecter les clics à l'extérieur
    useOnClickOutside(popoverRef, linkPopoverVisible, () => {
      setLinkPopoverVisible(false);
      setSelectionPosition(null);
      setLinkUrl("");
    });

    // Ajoutez un effet pour suivre la position du texte sélectionné lors du défilement
    useEffect(() => {
      if (!selectionPosition || !linkPopoverVisible) return;

      const handleScroll = () => {
        if (currentSelection && currentSelection.range) {
          try {
            // Recréer la sélection temporairement pour obtenir sa position
            const tempSelection = window.getSelection();
            const tempRange = currentSelection.range.cloneRange();

            // Sauvegarder la sélection actuelle
            const currentRanges = [];
            for (let i = 0; i < tempSelection.rangeCount; i++) {
              currentRanges.push(tempSelection.getRangeAt(i).cloneRange());
            }

            // Appliquer la sélection originale
            tempSelection.removeAllRanges();
            tempSelection.addRange(tempRange);

            // Obtenir la position
            const rect = tempRange.getBoundingClientRect();

            // Obtenir la position de l'éditeur
            const editorRect = editorRef.current.getBoundingClientRect();

            // Calculer la position relative à l'éditeur
            setSelectionPosition({
              left: rect.left - editorRect.left + rect.width / 2,
              top: rect.bottom - editorRect.top,
            });

            // Restaurer la sélection précédente
            tempSelection.removeAllRanges();
            currentRanges.forEach((range) => tempSelection.addRange(range));
          } catch (error) {
            console.error("Erreur lors du suivi de la sélection:", error);
          }
        }
      };

      window.addEventListener("scroll", handleScroll);
      return () => window.removeEventListener("scroll", handleScroll);
    }, [selectionPosition, linkPopoverVisible, currentSelection]);

    if (!selectionPosition || !linkPopoverVisible) return null;

    // Rendre le popover à l'intérieur de l'éditeur
    return ReactDOM.createPortal(
      <div
        ref={popoverRef}
        style={{
          position: "absolute",
          left: `${
            selectionPosition.left < 120
              ? 120
              : selectionPosition.left > 504
              ? "75%"
              : selectionPosition.left
          }px`,
          top: `${selectionPosition.top - 40}px`,
          transform: "translateX(-50%)",
          zIndex: 1050,
        }}
      >
        <div className="rounded bg-white p-2 shadow-lg" style={{ width: 250 }}>
          <Input
            placeholder="Ajouter un lien"
            value={linkUrl}
            onChange={(e) => setLinkUrl(e.target.value)}
            onPressEnter={applyLink}
            autoFocus
          />
          <div className="mt-3 flex justify-end space-x-2">
            <Button size="small" onClick={cancelLink}>
              Annuler
            </Button>
            <Button type="primary" size="small" onClick={applyLink}>
              Enregistrer
            </Button>
          </div>
        </div>
      </div>,
      editorRef.current // Rendre dans l'éditeur au lieu de document.body
    );
  };

  // Créez un composant pour le popover de survol de lien
  const LinkHoverPopover = () => {
    if (!linkHoverPosition || !linkHoverPopoverVisible || !hoveredLink)
      return null;

    // Calculer les dimensions et la position du lien
    const linkRect = hoveredLink.getBoundingClientRect();
    const editorRect = editorRef.current.getBoundingClientRect();

    // Position relative du lien par rapport à l'éditeur
    const linkRelativePosition = {
      left: linkRect.left - editorRect.left,
      top: linkRect.top - editorRect.top,
      width: linkRect.width,
      height: linkRect.height,
    };

    // Position du popover
    const popoverTop = linkRelativePosition.top + linkRelativePosition.height;

    return ReactDOM.createPortal(
      <>
        {/* Créer un "pont" invisible entre le lien et le popover */}
        <div
          style={{
            position: "absolute",
            left: `${linkRelativePosition.left}px`,
            top: `${linkRelativePosition.top}px`,
            width: `${linkRelativePosition.width}px`,
            height: `${popoverTop + 10}px`, // Étendre jusqu'au popover
            zIndex: 1049, // Juste en dessous du popover
            pointerEvents: "none", // Ne pas interférer avec les clics
          }}
        />

        {/* Le popover lui-même */}
        <div
          style={{
            position: "absolute",
            left: `${
              linkHoverPosition.left < 120
                ? 120
                : linkHoverPosition.left > 504
                ? "75%"
                : linkHoverPosition.left
            }px`,
            top: `${linkHoverPosition.top}px`,
            transform: "translateX(-50%)",
            zIndex: 1050,
          }}
          onMouseEnter={() => setLinkHoverPopoverVisible(true)}
          onMouseLeave={() => {
            // Ajouter un délai avant de fermer le popover
            setTimeout(() => {
              // Vérifier si la souris est sur le lien ou le popover
              const isMouseOverLink =
                document.querySelector("a:hover") === hoveredLink;
              const isMouseOverPopover = document.querySelector(
                ".link-hover-popover:hover"
              );

              if (!isMouseOverLink && !isMouseOverPopover) {
                setLinkHoverPopoverVisible(false);
              }
            }, 100);
          }}
          className="link-hover-popover"
        >
          <div className="rounded bg-white p-2 shadow-lg">
            <div className="mb-1 text-xs text-gray-500">
              {hoveredLink.getAttribute("href")}
            </div>
            <div className="flex space-x-2">
              <Button size="small" onClick={editLink}>
                Modifier
              </Button>
              <Button size="small" danger onClick={removeLink}>
                Supprimer
              </Button>
            </div>
          </div>
        </div>
      </>,
      editorRef.current
    );
  };

  // Ajoutez un gestionnaire global pour fermer le popover lorsque l'utilisateur clique ailleurs
  useEffect(() => {
    const handleClickOutside = (e) => {
      if (
        linkHoverPopoverVisible &&
        !e.target.closest(".link-hover-popover") &&
        !e.target.closest("a")
      ) {
        setLinkHoverPopoverVisible(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [linkHoverPopoverVisible]);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="mx-auto w-full">
        <div className="grid grid-cols-1 ">
          {/* Barre d'outils */}
          <div className="lg:col-span-3">
            <div className="mb-4 rounded-lg border bg-white p-4 shadow-sm">
              {/* Sujet */}
              {/* Outils de formatage */}
              <div className="mb-4 flex flex-wrap gap-2 border-b pb-4">
                {/* Police et taille */}
                <Select
                  size="small"
                  style={{ width: 100 }}
                  popupMatchSelectWidth={false}
                  value={fontFamily}
                  onChange={(e) => handleFontFamilyChange(e.target.value)}
                >
                  {fontFamilies.map((font) => (
                    <option key={font} value={font}>
                      {font}
                    </option>
                  ))}
                </Select>

                <Select
                  size="small"
                  popupMatchSelectWidth={false}
                  style={{ width: 70 }}
                  value={fontSize}
                  onChange={(e) => handleFontSizeChange(e.target.value)}
                >
                  {[8, 10, 12, 14, 16, 18, 20, 24, 28, 32].map((size) => (
                    <option key={size} value={size}>
                      {size}px
                    </option>
                  ))}
                </Select>

                {/* Formatage de base */}
                <Button
                  onClick={() => execCommand("bold")}
                  icon={<BoldOutlined />}
                  title="Gras"
                  size="small"
                />
                <Button
                  onClick={() => execCommand("italic")}
                  icon={<ItalicOutlined />}
                  title="Italique"
                  size="small"
                />
                <Button
                  onClick={() => execCommand("underline")}
                  icon={<UnderlineOutlined />}
                  title="Souligné"
                  size="small"
                />

                {/* Alignement */}
                <Button
                  onClick={() => execCommand("justifyLeft")}
                  icon={<AlignLeftOutlined />}
                  title="Aligner à gauche"
                  size="small"
                />
                <Button
                  onClick={() => execCommand("justifyCenter")}
                  icon={<AlignCenterOutlined />}
                  title="Centrer"
                  size="small"
                />
                <Button
                  onClick={() => execCommand("justifyRight")}
                  icon={<AlignRightOutlined />}
                  title="Aligner à droite"
                  size="small"
                />

                {/* Listes */}
                <Button
                  onClick={() => execCommand("insertUnorderedList")}
                  icon={<UnorderedListOutlined />}
                  title="Liste à puces"
                  size="small"
                />
                <Button
                  onClick={() => execCommand("insertOrderedList")}
                  icon={<OrderedListOutlined />}
                  title="Liste numérotée"
                  size="small"
                />

                {/* Autres outils */}
                <Button
                  type={isSelectionLink ? "primary" : "default"}
                  size="small"
                  icon={
                    <LinkOutlined
                    // style={{ color: isSelectionLink ? "#1890ff" : undefined }}
                    />
                  }
                  onClick={handleLinkButtonClick}
                  // className={isSelectionLink ? "text-blue-600" : ""}
                  title={
                    isSelectionLink ? "Modifier le lien" : "Ajouter un lien"
                  }
                >
                  {/* {isSelectionLink ? "Modifier le lien" : "Ajouter un lien"} */}
                </Button>

                <>
                  <Button
                    onClick={handleImageUpload}
                    icon={<UploadOutlined />}
                    title="Upload une image"
                    size="small"
                  />

                  {/* Modal de redimensionnement */}
                  <Modal
                    title="Redimensionner l'image"
                    open={imageModalVisible}
                    onCancel={() => {
                      setImageModalVisible(false);
                      URL.revokeObjectURL(imageSrc);
                      setImageSrc("");
                    }}
                    width={700}
                    footer={[
                      <Button key="reset" onClick={resetImageSize}>
                        Réinitialiser
                      </Button>,
                      <Button
                        key="lock"
                        type={lockAspectRatio ? "primary" : "default"}
                        onClick={() => setLockAspectRatio(!lockAspectRatio)}
                      >
                        {lockAspectRatio
                          ? "Proportions verrouillées"
                          : "Proportions libres"}
                      </Button>,
                      <Button
                        key="cancel"
                        onClick={() => {
                          setImageModalVisible(false);
                          URL.revokeObjectURL(imageSrc);
                          setImageSrc("");
                        }}
                      >
                        Annuler
                      </Button>,
                      <Button
                        key="submit"
                        type="primary"
                        onClick={insertResizedImage}
                      >
                        Insérer l'image
                      </Button>,
                    ]}
                  >
                    <div className="mb-4">
                      <p className="mb-2">
                        Redimensionnez l'image en faisant glisser les poignées:
                      </p>
                      <div className="my-4 flex justify-center">
                        {imageSrc && (
                          <Resizable
                            width={imageSize.width}
                            height={imageSize.height}
                            onResize={handleResize}
                            handle={
                              <div
                                className="custom-resize-handle"
                                style={{
                                  position: "absolute",
                                  width: "10px",
                                  height: "10px",
                                  background: "#1890ff",
                                  borderRadius: "50%",
                                  bottom: "-5px",
                                  right: "-5px",
                                  cursor: "se-resize",
                                }}
                              />
                            }
                          >
                            <div
                              style={{
                                width: `${imageSize.width}px`,
                                height: `${imageSize.height}px`,
                                position: "relative",
                                overflow: "hidden",
                                border: "1px dashed #d9d9d9",
                              }}
                            >
                              <img
                                ref={imageRef}
                                src={imageSrc}
                                alt="Aperçu"
                                style={{
                                  width: "100%",
                                  height: "100%",
                                  objectFit: "contain",
                                }}
                              />
                            </div>
                          </Resizable>
                        )}
                      </div>

                      <div className="mt-4">
                        <p>
                          Dimensions: {Math.round(imageSize.width)} ×{" "}
                          {Math.round(imageSize.height)} pixels
                        </p>
                        <p className="text-sm text-gray-500">
                          Dimensions originales: {originalSize.width} ×{" "}
                          {originalSize.height} pixels
                        </p>
                      </div>

                      <div className="mt-4">
                        <p className="mb-2">Largeur:</p>
                        <Slider
                          min={50}
                          max={Math.min(1000, originalSize.width * 2)}
                          value={imageSize.width}
                          onChange={(value) => {
                            if (lockAspectRatio) {
                              setImageSize({
                                width: value,
                                height: value / aspectRatio,
                              });
                            } else {
                              setImageSize({
                                ...imageSize,
                                width: value,
                              });
                            }
                          }}
                        />
                      </div>

                      <div className="mt-4">
                        <p className="mb-2">Hauteur:</p>
                        <Slider
                          min={50}
                          max={Math.min(800, originalSize.height * 2)}
                          value={imageSize.height}
                          onChange={(value) => {
                            if (lockAspectRatio) {
                              setImageSize({
                                width: value * aspectRatio,
                                height: value,
                              });
                            } else {
                              setImageSize({
                                ...imageSize,
                                height: value,
                              });
                            }
                          }}
                        />
                      </div>
                    </div>
                  </Modal>
                </>

                <Button
                  onClick={insertImageFromUrl}
                  icon={<PictureOutlined />}
                  title="Insérer une image par URL"
                  size="small"
                />

                <Button
                  onClick={openButtonModal}
                  icon={<MdSmartButton />}
                  title="Insérer un bouton"
                  size="small"
                />

                <Button
                  onClick={() => setShowHtmlModal(true)}
                  icon={<CodeOutlined />}
                  title="Insérer du HTML"
                  size="small"
                />

                <Button
                  onClick={() => execCommand("formatBlock", "blockquote")}
                  icon={<QuestionCircleOutlined />}
                  title="Citation"
                  size="small"
                />
              </div>

              {/* Couleurs */}
              <div className="mb-4 flex flex-wrap gap-2 border-b pb-4">
                <span className="mr-2 text-sm font-medium text-gray-700">
                  Couleurs:
                </span>
                {colors.map((color) => (
                  <Button
                    key={color}
                    onClick={() => handleColorChange(color)}
                    className={
                      selectedColor === color
                        ? "border-gray-400"
                        : "border-gray-200"
                    }
                    style={{
                      backgroundColor: color,
                      width: "24px",
                      height: "24px",
                      minWidth: "unset",
                      padding: 0,
                      border:
                        selectedColor === color
                          ? "2px solid #666"
                          : "1px solid #ddd",
                    }}
                    title={`Couleur ${color}`}
                  />
                ))}
              </div>

              {/* Variables */}
              <div className="mb-4 flex flex-wrap gap-2 border-b pb-4">
                <span className="mr-2 text-sm font-medium text-gray-700">
                  Variables:
                </span>
                {[
                  "nom",
                  "prenom",
                  "email",
                  "entreprise",
                  "date",
                  "lien_desinscription",
                ].map((variable) => (
                  <Button
                    key={variable}
                    onClick={() => insertVariable(variable)}
                    type="default"
                    size="small"
                    style={{ backgroundColor: "#e6f7ff", color: "#1890ff" }}
                  >
                    {`{{${variable}}}`}
                  </Button>
                ))}
              </div>

              {/* Actions */}
              <div className="flex flex-wrap gap-2">
                <Button
                  onClick={() => setShowPreview(!showPreview)}
                  type="primary"
                  icon={<EyeOutlined />}
                >
                  {showPreview ? "Éditer" : "Aperçu"}
                </Button>
                <Button
                  onClick={saveTemplate}
                  type="primary"
                  icon={<SaveOutlined />}
                  style={{ backgroundColor: "#52c41a", borderColor: "#52c41a" }}
                >
                  Sauvegarder
                </Button>
                <Button
                  onClick={exportHTML}
                  type="primary"
                  icon={<DownloadOutlined />}
                  style={{ backgroundColor: "#722ed1", borderColor: "#722ed1" }}
                >
                  Exporter HTML
                </Button>
              </div>
            </div>

            {/* Éditeur */}
            <div className="relative" ref={editorContainerRef}>
              <div className="border-b bg-gray-50 p-4">
                <h3 className="font-medium text-gray-800">
                  {showPreview ? "Aperçu du template" : "Contenu de l'email"}
                </h3>
              </div>
              <div className="">
                {showPreview ? (
                  <div className="prose max-w-none">
                    <h2 className="mb-4 text-xl font-bold">{subject}</h2>
                    <div
                      dangerouslySetInnerHTML={{
                        __html: editorRef.current?.innerHTML || "",
                      }}
                    />
                  </div>
                ) : (
                  <div
                    ref={editorRef}
                    contentEditable
                    className="min-h-96 rounded border p-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    style={{ fontFamily, fontSize: `${fontSize}px` }}
                    placeholder="Commencez à écrire votre email..."
                    onInput={(e) => setContent(e.target.innerHTML)}
                    onClick={handleEditorClick}
                    onKeyUp={handleEditorClick}
                  />
                )}
              </div>
              <FloatingLinkPopover />
              <LinkHoverPopover />
            </div>
          </div>

          {/* Panneau latéral */}
        </div>

        {/* Modal pour insérer du HTML */}
        <Modal
          title="Insérer du HTML personnalisé"
          open={showHtmlModal}
          onCancel={() => {
            setShowHtmlModal(false);
            setHtmlInput("");
          }}
          footer={[
            <Button
              key="cancel"
              onClick={() => {
                setShowHtmlModal(false);
                setHtmlInput("");
              }}
            >
              Annuler
            </Button>,
            <Button key="insert" type="primary" onClick={insertHtml}>
              Insérer HTML
            </Button>,
          ]}
          width={600}
          centered
        >
          <Input.TextArea
            value={htmlInput}
            onChange={(e) => setHtmlInput(e.target.value)}
            rows={8}
            placeholder="Collez votre code HTML ici..."
            style={{ resize: "none" }}
          />
        </Modal>

        {/* Modal pour créer un bouton */}
        <Modal
          title="Créer un bouton"
          open={showButtonModal}
          onCancel={resetButtonModal}
          footer={[
            <Button key="cancel" onClick={resetButtonModal}>
              Annuler
            </Button>,
            <Button key="submit" type="primary" onClick={insertButton}>
              Insérer le bouton
            </Button>,
          ]}
          width={500}
          centered
        >
          <Form layout="vertical" form={formInsertButton}>
            <Form.Item
              label="Texte du bouton"
              rules={[
                {
                  required: true,
                  message: "Veuillez saisir le texte du bouton",
                },
              ]}
            >
              <Input
                value={buttonConfig.text}
                onChange={(e) =>
                  setButtonConfig({ ...buttonConfig, text: e.target.value })
                }
                placeholder="Cliquez ici"
              />
            </Form.Item>

            <Form.Item
              label="URL de destination"
              rules={[{ required: true, message: "Veuillez saisir une URL" }]}
            >
              <Input
                value={buttonConfig.url}
                onChange={(e) =>
                  setButtonConfig({ ...buttonConfig, url: e.target.value })
                }
                placeholder="https://exemple.com"
              />
            </Form.Item>

            <div className="grid grid-cols-2 gap-3">
              <Form.Item label="Couleur de fond">
                <div className="flex items-center">
                  <ColorPicker
                    value={buttonConfig.bgColor}
                    onChange={(color, hex) =>
                      setButtonConfig({ ...buttonConfig, bgColor: hex })
                    }
                    showText
                  />
                </div>
              </Form.Item>

              <Form.Item label="Couleur du texte">
                <div className="flex items-center">
                  <ColorPicker
                    value={buttonConfig.textColor}
                    onChange={(color, hex) =>
                      setButtonConfig({ ...buttonConfig, textColor: hex })
                    }
                    showText
                  />
                </div>
              </Form.Item>
            </div>

            <Form.Item label="Espacement interne (padding)">
              <Input
                value={buttonConfig.padding}
                onChange={(e) =>
                  setButtonConfig({ ...buttonConfig, padding: e.target.value })
                }
                placeholder="12px 24px"
              />
            </Form.Item>

            <Form.Item label="Bordure arrondie">
              <Input
                value={buttonConfig.borderRadius}
                onChange={(e) =>
                  setButtonConfig({
                    ...buttonConfig,
                    borderRadius: e.target.value,
                  })
                }
                placeholder="5px"
              />
              <Slider
                min={0}
                max={20}
                value={parseInt(buttonConfig.borderRadius) || 0}
                onChange={(value) =>
                  setButtonConfig({
                    ...buttonConfig,
                    borderRadius: `${value}px`,
                  })
                }
                className="mt-2"
              />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </div>
  );
};

export default EmailTemplateEditor;
