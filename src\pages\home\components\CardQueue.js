import { ArrowRightOutlined, InfoCircleOutlined } from "@ant-design/icons";
import { Card, Col, Row, Select, Statistic, Tooltip, Typography } from "antd";
import { Phone, PhoneIncoming, PhoneMissed, Speech } from "lucide-react";
import { setQueueInDashboard } from "new-redux/actions/dashboard.actions";
import React, { useState } from "react";
import CountUp from "react-countup";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
export const convertToHour = (duree) => {
  const parties = duree?.split(":");
  if (Array.isArray(parties) && parties.length === 3) {
    const heures = parseInt(parties[0]);
    const minutes = parseInt(parties[1]);
    const secondes = parseInt(parties[2]);

    // Construction de la chaîne de caractères pour l'affichage
    let affichage = "";
    if (heures > 0) {
      affichage += heures + "h ";
    }
    if (minutes > 0) {
      affichage += minutes + "m ";
    }
    affichage += secondes + "s";

    return affichage;
  }
};
const CardQueue = ({ start, end }) => {
  const {
    receivedTodayCall,
    totalQueues,
    //localhost:3000/logs

    allQueues,
  } = useSelector((state) => state.dashboardRealTime);
  const [selectedQueue, setSelectedQueue] = useState(
    totalQueues && Array.isArray(totalQueues) && totalQueues.length > 0
      ? totalQueues[0].queue_num
      : ""
  );
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const formatter = (value) => <CountUp end={value} separator="," />;

  return (
    <Card
      title={
        <div className="flex items-center justify-between pr-2">
          <span>
            {t("dashboard.queue")}{" "}
            <Tooltip title={t("dashboard.statsCall")}>
              <InfoCircleOutlined />
            </Tooltip>
          </span>
          <Select
            showSearch
            defaultValue={totalQueues[0]?.queue_num}
            popupMatchSelectWidth={false}
            placeholder=""
            optionFilterProp="children"
            filterOption={(input, option) =>
              (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
            }
            options={allQueues}
            value={selectedQueue?.queue_num}
            onChange={(value, values) => {
              dispatch(
                setQueueInDashboard({
                  startDate: start,
                  endDate: end,
                  queue_num: value,
                })
              );
              setSelectedQueue(value);
              // setSelectedQueue({
              //   ...values,
              // });
            }}
          />
        </div>
      }
      // styles={{ body: { height: "100%" } }}
      size=""
      className="full bg-white shadow-sm"
      extra={
        <Typography.Link
          onClick={() => navigate("/logs", { state: "groups_queues" })}
        >
          <ArrowRightOutlined />
        </Typography.Link>
      }
    >
      <Row
        gutter={24}
        className=" flex flex-nowrap overflow-auto"
        style={{ marginLeft: 0, marginRight: 0 }}
      >
        <Col flex="auto">
          <Statistic
            formatter={typeof receivedTodayCall === "number" ? formatter : null}
            title={
              <div className="flex w-max flex-nowrap">
                {t("dashboard.receivedCalls", {
                  plural: totalQueues[0]?.total_calls > 1 ? "s" : "",
                  pluriel: totalQueues[0]?.total_calls > 1 ? "x" : "",
                })}
              </div>
            }
            value={totalQueues[0]?.total_calls}
            // valueStyle={{
            //   color: "#3f8600",
            // }}
            prefix={<PhoneIncoming size={20} />}
          />
        </Col>
        <Col flex="auto">
          <Statistic
            formatter={
              typeof totalQueues[0]?.answered_calls === "number"
                ? formatter
                : null
            }
            title={
              <div className="flex w-max flex-nowrap">
                {t("dashboard.answered", {
                  plural: totalQueues[0]?.answered_calls > 1 ? "s" : "",
                })}
              </div>
            }
            value={totalQueues[0]?.answered_calls}
            // valueStyle={{
            //   color: "#1677ff",
            // }}
            prefix={<Phone size={20} />}
          />
        </Col>

        <Col flex="auto">
          <Statistic
            formatter={typeof receivedTodayCall === "number" ? formatter : null}
            title={
              <div className="flex w-max flex-nowrap">
                {t("dashboard.missedCalls", {
                  plural: totalQueues[0]?.no_answered_calls > 1 ? "s" : "",
                  pluriel: totalQueues[0]?.no_answered_calls > 1 ? "x" : "",
                })}{" "}
              </div>
            }
            value={totalQueues[0]?.no_answered_calls}
            // valueStyle={{
            //   color: "#3f8600",
            // }}
            prefix={<PhoneMissed size={20} />}
          />
        </Col>

        <Col flex="auto">
          <Statistic
            formatter={typeof receivedTodayCall === "number" ? formatter : null}
            title={
              <Tooltip
                title={t("dashboard.lostNotRecalled", {
                  plural:
                    totalQueues[0]?.no_answered_calls_not_returned > 1
                      ? "s"
                      : "",
                })}
              >
                <div className="flex w-max flex-nowrap">
                  {t("dashboard.recalled", {
                    plural:
                      totalQueues[0]?.no_answered_calls_not_returned > 1
                        ? "s"
                        : "",
                  })}
                </div>
              </Tooltip>
            }
            value={totalQueues[0]?.no_answered_calls_not_returned}
            // valueStyle={{
            //   color: "#3f8600",
            // }}
            prefix={
              <svg
                width="30"
                height="32"
                viewBox="0 -2 34 34"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g clip-path="url(#clip0_455_13067)">
                  <path
                    d="M6.41959 20.5743L4.29505 18.4562C4.09702 18.2603 3.94207 18.0253 3.84013 17.7661C3.7382 17.5069 3.69151 17.2293 3.70307 16.9511C3.71463 16.6728 3.78418 16.4 3.90726 16.1502C4.03034 15.9004 4.20425 15.679 4.41783 15.5003C6.82719 13.5571 9.65876 12.2063 12.685 11.5562C15.4782 10.9291 18.3752 10.9247 21.1703 11.5433C24.211 12.1883 27.057 13.5406 29.4777 15.4905C29.6912 15.668 29.8654 15.8881 29.9891 16.1366C30.1129 16.3851 30.1836 16.6567 30.1966 16.9341C30.2097 17.2114 30.1649 17.4885 30.065 17.7475C29.9651 18.0066 29.8124 18.2421 29.6165 18.4389L27.4984 20.5634C27.1592 20.9105 26.7053 21.1223 26.2214 21.1595C25.7375 21.1967 25.2566 21.0566 24.8683 20.7654C24.099 20.1772 23.2656 19.678 22.3841 19.2772C22.0356 19.1198 21.7396 18.8658 21.5311 18.5452C21.3226 18.2247 21.2103 17.8512 21.2076 17.4688L21.2048 15.6727C18.4268 14.9133 15.4953 14.9177 12.7196 15.6856L12.7223 17.4817C12.7207 17.8641 12.6096 18.238 12.402 18.5591C12.1945 18.8803 11.8992 19.1352 11.5512 19.2937C10.6709 19.6971 9.83908 20.1989 9.07157 20.7894C8.68007 21.085 8.19357 21.2265 7.70461 21.1868C7.21564 21.1471 6.7583 20.9291 6.41959 20.5743Z"
                    stroke="black"
                    strokeWidth="1.67"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M14.9362 4.7113L14.4493 4.22435V4.91301V6.59229H14.1778V3.79502H16.9751V4.06646H15.2958H14.6072L15.0941 4.55341L17.6199 7.07924L17.8216 7.28095L18.0234 7.07924L21.1894 3.91317L21.3473 4.07106L17.8216 7.59673L14.9362 4.7113Z"
                    stroke="black"
                    strokeWidth="0.570502"
                  />
                </g>
                <defs>
                  <clipPath id="clip0_455_13067">
                    <rect
                      width="24"
                      height="24"
                      fill="white"
                      transform="translate(33.9355 16.9404) rotate(134.913)"
                    />
                  </clipPath>
                </defs>
              </svg>
            }
          />
        </Col>
        <Col flex="auto">
          <Statistic
            // formatter={
            //   typeof missedTodayCall === "number" ? formatter : null
            // }
            className="moyen_ringing_calls"
            title={
              <Tooltip title={t("voip.moyen_ringing_calls")}>
                <div className="flex w-max flex-nowrap">{t("voip.DMS")}</div>
              </Tooltip>
            }
            // value={totalQueues[0]?.moyen_ringing_calls}
            suffix={
              totalQueues[0]?.moyen_ringing_calls
                ? convertToHour(totalQueues[0]?.moyen_ringing_calls)
                : "-"
            }
            value={null}
            // valueStyle={{
            //   color: "#cf1322",
            // }}

            prefix={
              <svg
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M21.9999 16.9201V19.9201C22.0011 20.1986 21.944 20.4743 21.8324 20.7294C21.7209 20.9846 21.5572 21.2137 21.352 21.402C21.1468 21.5902 20.9045 21.7336 20.6407 21.8228C20.3769 21.912 20.0973 21.9452 19.8199 21.9201C16.7428 21.5857 13.7869 20.5342 11.1899 18.8501C8.77376 17.3148 6.72527 15.2663 5.18993 12.8501C3.49991 10.2413 2.44818 7.27109 2.11993 4.1801C2.09494 3.90356 2.12781 3.62486 2.21643 3.36172C2.30506 3.09859 2.4475 2.85679 2.6347 2.65172C2.82189 2.44665 3.04974 2.28281 3.30372 2.17062C3.55771 2.05843 3.83227 2.00036 4.10993 2.0001H7.10993C7.59524 1.99532 8.06572 2.16718 8.43369 2.48363C8.80166 2.80008 9.04201 3.23954 9.10993 3.7201C9.23656 4.68016 9.47138 5.62282 9.80993 6.5301C9.94448 6.88802 9.9736 7.27701 9.89384 7.65098C9.81408 8.02494 9.6288 8.36821 9.35993 8.6401L8.08993 9.9101C9.51349 12.4136 11.5864 14.4865 14.0899 15.9101L15.3599 14.6401C15.6318 14.3712 15.9751 14.1859 16.3491 14.1062C16.723 14.0264 17.112 14.0556 17.4699 14.1901C18.3772 14.5286 19.3199 14.7635 20.2799 14.8901C20.7657 14.9586 21.2093 15.2033 21.5265 15.5776C21.8436 15.9519 22.0121 16.4297 21.9999 16.9201Z"
                  stroke="black"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <g clip-path="url(#clip0_757_5993)">
                  <path
                    d="M18 3V6L20 7M23 6C23 8.76142 20.7614 11 18 11C15.2386 11 13 8.76142 13 6C13 3.23858 15.2386 1 18 1C20.7614 1 23 3.23858 23 6Z"
                    stroke="black"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </g>
                <defs>
                  <clipPath id="clip0_757_5993">
                    <rect
                      width="12"
                      height="12"
                      fill="white"
                      transform="translate(12)"
                    />
                  </clipPath>
                </defs>
              </svg>
            }
          />
        </Col>
        <Col flex="auto">
          <Statistic
            // formatter={
            //   typeof missedUnreturnedCalls === "number"
            //     ? formatter
            //     : null
            // }
            className="moyen_ringing_calls"
            title={
              <Tooltip title={t("voip.moyen_treatement_calls")}>
                <div className="flex w-max flex-nowrap">{t("voip.DMC")}</div>
              </Tooltip>
            }
            suffix={
              totalQueues[0]?.moyen_treatement_calls
                ? convertToHour(totalQueues[0]?.moyen_treatement_calls)
                : "-"
            }
            value={null}
            // value={selectedQueue?.moyen_treatement_calls}
            // valueStyle={{
            //   color: "black",
            // }}
            prefix={<Speech size={20} />}
          />
        </Col>
      </Row>
    </Card>
  );
};

export default CardQueue;
