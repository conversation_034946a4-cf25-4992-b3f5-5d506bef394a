/* eslint-disable react/jsx-no-duplicate-props */
import { AudioOutlined, PlusOutlined, SendOutlined } from "@ant-design/icons";
import { Button, message, Popover, Skeleton, Tooltip, Typography } from "antd";
import {
  lazy,
  Suspense,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useTranslation } from "react-i18next";
import { connect, useDispatch, useSelector } from "react-redux";
import {
  scrollToBottom,
  setInputValue,
} from "new-redux/actions/chat.actions/Input";
import mainService from "services/main.service";
import { toastNotification } from "components/ToastNotification";
import { useSendMessage } from "../../hooks/useSendMessage";
import {
  handleTypingUser,
  setNumberUnreadMsg,
  setSyncNewMsg,
  stopSearchMsg,
} from "new-redux/actions/chat.actions";
import { <PERSON><PERSON>, ModalConfirm, CreatePollsComponent } from "components/Chat";
import { getLastMessageAPI } from "new-redux/services/chat.services";
import RichTextInput from "components/tiptap_richtext/RichTextInput";
import useTyping from "../../hooks/useTyping";
import { motion } from "framer-motion";
import useNetwork from "custom-hooks/useNetwork";
import { BiPoll } from "react-icons/bi";
import useVisiblityChange from "../../hooks/useVisiblityChange";
import { lazyRetry } from "utils/lazyRetry";
import useGetMessage from "../../hooks/useGetMessage";
import useKeyDown from "custom-hooks/useKeyDown";
import {
  convertToPlain,
  getName,
  safeText,
} from "../../utils/ConversationUtils";
import RecordComponent from "./AudioInput/AudioInput3";
import { isGuestConnected } from "utils/role";

const FileInput = lazy(() =>
  lazyRetry(() => import("./FileInput/FileInput"), "FileInput")
);
const ShowFileList = lazy(() =>
  lazyRetry(() => import("./FileInput/ShowFileList"), "ShowFileList")
);

const externSource = ["RMC"];

/**
 *
 * @param {boolean} from
 * from : indicate if the input from thread or not
 * @param {boolean} from_big
 * from_big : indicate if the input from thread opned in big screen or not
 * @param {string} source
 * source : indicate the source of the input default value is "chat" / "no_chat"
 *
 * @returns
 */

function InputChat({
  from = false,
  from_big = false,
  source = "chat",
  maxWidth = false,
  //  callback = () => {},
}) {
  const { t } = useTranslation("common");
  const {
    numberUnreadMsg,
    errorMessages,
    userList,
    syncNewMsg,
    threadList,
    mentionState,
    userIsTyping,
    openDrawer,
    currentUser,
    searchMsgState,
  } = useSelector((state) => state.chat);
  const selectedConversation = useSelector(
    (state) => state.ChatRealTime.selectedConversation
  );
  const typingUsers = useSelector((state) => state.ChatRealTime.typingUsers);
  const sphereUserRole = useSelector((state) => state?.user?.user?.role);
  const { status, fetchStatus } = useGetMessage();
  const [msg, setMsg] = useState({
    text: "",
    path: "",
  });
  const [isRecording, setIsRecording] = useState(false);
  const [showAudioInput, setShowAudioInput] = useState(false);
  const [openRecord, setOpenRecord] = useState(false);

  const [isOpenPolls, setOpenPolls] = useState(false);
  const [openPopOver, setOpenPopOver] = useState(false);

  // const [clicked, setClicked] = useState(false);
  const [isMenuBarActive, setIsMenuBarActive] = useState(false);
  const [fileList, setFileList] = useState([]);
  const [fileFromPase, setFileFromPaste] = useState(null);
  const [cancel, setCancel] = useState(null);
  const [isMessageSent, setIsMessageSent] = useState(null);

  const timer = useRef(null);
  const dispatch = useDispatch();
  const { mutate: handleActionMessage, isLoading } = useSendMessage(
    source === "no_chat" ? "new_message_no_chat" : "new_message"
  );
  const { isOnline } = useNetwork();

  const [messageApi, contextHolder] = message.useMessage();

  const errorMic = () => {
    messageApi.open({
      type: "error",
      content: t("chat.audio_message.accessMic"),
      style: {
        marginTop: "85vh",
      },
    });
  };
  const path = useMemo(() => (from ? "chat_reply" : "chat_main"), [from]);
  const onRemoveFile = async (item) => {
    setCancel(item.uid);
    setFileList((prev) => prev.filter((f) => f.uid !== item.uid));

    if (item._id)
      await mainService
        .deleteFile(
          externSource.includes(source) ? "EXTERNE" : "CHAT",
          item._id
        )
        .catch(() => {
          setFileList((prev) => [...prev, item]);
          toastNotification("error", t("toasts.errorFetchApi"), "topRight");
        });
  };
  const clearInput = useCallback(() => {
    let timer;
    setMsg({
      text: "",
      path: "",
    });

    setFileList([]);
    clearTimeout(timer);

    timer = setTimeout(() => {
      if (numberUnreadMsg.number > 0)
        dispatch(
          setNumberUnreadMsg({
            id: null,
            number: 0,
          })
        );

      dispatch(
        setInputValue({
          conversationId: selectedConversation?.id,
          value: "",
          files: [],
          type: selectedConversation?.type,
          path,
        })
      );
      clearTimeout(timer);
    }, 1);
  }, [
    numberUnreadMsg.number,
    dispatch,
    selectedConversation?.id,
    selectedConversation?.type,
    path,
  ]);
  // controll if there are error message in the conversation to prevent sending message
  const foundErrorMessage = useMemo(
    () =>
      errorMessages?.find(
        (item) =>
          item?.discussion_id === selectedConversation?.id &&
          item?.type_conversation === selectedConversation?.type
      ),
    [errorMessages, selectedConversation?.id]
  );
  // check if the input is disabled or not
  const disabledInput = useCallback(
    (general) => {
      const disableCondition =
        !isOnline ||
        showAudioInput ||
        // in case there an old message being sent and try to retreive the new messages
        syncNewMsg ||
        (!searchMsgState.id &&
          status === "loading" &&
          fetchStatus !== "idle") ||
        (fileList.length > 0 &&
          fileList.find((item) => !Object.keys(item).includes("_id"))) ||
        (convertToPlain(
          msg.text
            .replaceAll("&lt;", "<")
            .replaceAll("&gt;", ">")
            .replaceAll("&amp;", "&")
            .replaceAll("<p></p>", "")
        ).trim().length === 0 &&
          fileList.length === 0);

      if (general) return disableCondition || foundErrorMessage;
      else return disableCondition;
    },
    [
      searchMsgState.id,
      isOnline,

      showAudioInput,
      syncNewMsg,
      fileList,
      msg.text,
      status,
      foundErrorMessage,
    ]
  );
  useEffect(() => {
    let mounted = true;
    if (openDrawer.type === "thread" && mounted) {
      setIsRecording(false);
      setShowAudioInput(false);
    }
    return () => {
      mounted = false;
      clearTimeout(timer.current);
    };
  }, [openDrawer.type]);
  const checkMicrophonePermission = async () => {
    const constraints = {
      audio: true,
      video: false,
    };

    await navigator.mediaDevices
      .getUserMedia(constraints)
      .then(() => {
        setShowAudioInput(true);
      })
      .catch((error) => {
        setShowAudioInput(false);
        errorMic();
      });
    // setIsBlocked(stream.active)
  };

  const sendMessage = useCallback(async () => {
    let taggedPerson = [];
    let timer, from_type;
    if (!isOnline) messageApi.info(t("toasts.networkErrorSendMessage"));
    if (!disabledInput(true)) {
      if (
        document?.activeElement?.parentElement
          ?.getAttribute("data-id-editor")
          ?.includes("editor-chat_reply") ||
        document.activeElement?.id.includes("send-button-chat_reply")
      ) {
        from_type = "reply";
      }
      // if (
      //   document?.activeElement?.parentElement
      //     ?.getAttribute("data-id-editor")
      //     ?.includes("editor-chat_main") ||
      //   document.activeElement?.id.includes("send-button-chat_main")
      // )
      else {
        from_type = "main";
      }
      if (msg.path !== from_type && fileList.length === 0) return;

      const newMsg = safeText(msg.text);

      if (newMsg.trim().length >= 8000) {
        alert("le message contient plus que 8000 caractéres");
        return;
      }
      let tempContainer = document.createElement("div");

      tempContainer.innerHTML = newMsg;

      const mentionSpans = tempContainer.querySelectorAll(
        'span[data-type="mention"]'
      );
      if (mentionSpans && mentionSpans.length > 0) {
        mentionSpans.forEach(function (span) {
          const userId = span.getAttribute("userid");
          taggedPerson.push(userId);
        });
      }
      setIsMessageSent(Math.floor(Math.random() * 10000 + 1));
      clearInput();

      handleActionMessage({
        params: {
          selectedConversation,
          message: newMsg?.trim(),
          file: fileList,
          taggedPerson: [...new Set(taggedPerson)].toString(),
          from,
          main_message: from
            ? threadList.find((item) => !item.type.includes("replay"))
            : null,
        },
        type_conversation: selectedConversation?.type,
        type_action:
          source === "no_chat" ? "new_message_no_chat" : "new_message",
      });

      taggedPerson = [];

      timer = setTimeout(() => {
        dispatch(stopSearchMsg());
        clearTimeout(timer.current);

        handleTyping(2);
        clearTimeout(timer);
      }, 2);
    }
  }, [
    isOnline,
    messageApi,
    t,
    disabledInput,
    msg.text,
    msg.path,
    clearInput,
    handleActionMessage,
    selectedConversation,
    fileList,
    from,
    threadList,
    source,
    dispatch,
  ]);

  // Watch show input's menuBar.
  const watchShowInputMenuBar = (state) => {
    setIsMenuBarActive(state);
  };

  // toggle input to force re-render

  // key enter event

  useKeyDown(13, false, "keydown", (e) => {
    if (!document?.activeElement?.parentElement?.getAttribute("data-id-editor"))
      return;
    if (!mentionState && !e.shiftKey) {
      sendMessage();

      document.getElementById("uploadFileBtn")?.blur();

      if (foundErrorMessage && !disabledInput(false))
        messageApi.info(t("chat.error_message.re-send"));
    }
  });

  const renderTypingUser = () => {
    switch (selectedConversation?.type) {
      case "user":
        return (
          typingUsers.find(
            (item) =>
              item.user_id === selectedConversation?.id && item.room_id === null
          ) && (
            <span className="absolute left-16 top-0 text-xs  font-semibold text-blue-500 first-letter:capitalize">
              {" "}
              {getName(
                userList.find((item) => item._id === selectedConversation?.id)
                  ?.name,
                "name"
              ) +
                " " +
                t("chat.typing")}
            </span>
          )
        );
      case "room": {
        let users_typing = [];
        //31, 34, 158, 161, 162, 24, 42, 90
        typingUsers.forEach((element) => {
          if (element.room_id === selectedConversation?.id)
            users_typing.push(element.user_id);
        });
        users_typing = [...new Set([...users_typing])].filter(
          (user) => user !== currentUser?._id
        );

        // console.log(userList.find((item) => item._id === users_typing[0]));
        return (
          <div className="typing absolute left-16 top-0 flex w-full items-center  space-x-1">
            {users_typing.slice(0, 5).map((element, index) => (
              <span
                key={`typing_user_${index}`}
                className="mr-0.5 text-xs font-semibold  text-blue-500 first-letter:capitalize"
              >
                {getName(
                  userList.find((item) => item._id === element)?.name,
                  "name"
                )}
                {index !== users_typing.slice(0, 5).length - 1 && ", "}
              </span>
            ))}
            {users_typing.length > 6 && (
              <span className="ml-1  text-xs  font-semibold text-blue-500">
                {t("chat.others")}
              </span>
            )}
            {users_typing.length > 0 && (
              <span className="ml-1  text-xs  font-semibold text-blue-500">
                {users_typing.length === 1
                  ? t("chat.typing")
                  : t("chat.userstyping")}
              </span>
            )}
          </div>
        );
      }
      default:
        break;
    }
    return;
  };
  const handleBlurWindow = useCallback(() => {
    if (
      !document.hasFocus() &&
      userIsTyping === 1 &&
      convertToPlain(msg.text).trim().length === 0
    ) {
      handleTyping(2);
    }
  }, [msg.text, userIsTyping]);
  useVisiblityChange({ callback: handleBlurWindow });

  useEffect(() => {
    let mount = true;
    if (mount) {
      if (fileList.length > 0) fileList.forEach((file) => onRemoveFile(file));
      setIsRecording(false);
      setShowAudioInput(false);
    }
    return () => {
      mount = false;
    };
  }, [selectedConversation?.id]);

  // open popover for polls
  const handleOpenChange = (newOpen) => {
    setOpenPopOver(newOpen);
  };

  /** ----------------------------------> TYPING <---------------------------------- **/
  // number : 0 => default
  // number : 1 => typing
  // number : 2 => stop typing

  const handleTyping = useCallback((number = 0) => {
    if (userIsTyping === number) return;
    dispatch(handleTypingUser(number));
  }, []);

  useTyping();

  const handleCloseAudioInput = useCallback(() => {
    setShowAudioInput(false);
  }, []);

  return (
    <div
      id="chat"
      onBlur={() => {
        if (
          userIsTyping === 1 &&
          convertToPlain(msg.text).trim().length !== 0
        ) {
          clearTimeout(timer.current);

          handleTyping(2);
        }
      }}
      // min-h-[80px] bg-gray-100
      className={`relative   ${
        showAudioInput ? "h-[80px]" : "auto "
      } w-full px-6
      ${source === "chat" ? "py-4" : ""}
      ${!from && source === "chat" ? "pb-6" : ""}
      transition duration-75`}
    >
      {contextHolder}
      <Suspense
        fallback={
          <div className="fixed z-[9999] flex h-full w-full items-center justify-center">
            <Loader size={30} />
          </div>
        }
      >
        <ModalConfirm
          //
          open={syncNewMsg}
          onCancel={() => dispatch(setSyncNewMsg(null))}
          onOk={() =>
            dispatch(
              getLastMessageAPI({
                refetch: true,
                type: syncNewMsg?.type,
                id: syncNewMsg?.id,
                message_id: syncNewMsg?.message_id,
                errorText: t("chat.message_system.error_fetch_new_messages"),
              })
            )
          }
          content={null}
          footer={typeof syncNewMsg === "boolean" ? null : true}
          dangerMode={true}
          okText={t("chat.reload")}
          cancelText={t("form.cancel")}
          closable={false}
          title={
            <div className="flex items-center  space-x-2">
              {typeof syncNewMsg === "boolean" && syncNewMsg && (
                <Loader size={26} />
              )}

              <Typography.Title
                level={4}
                type={typeof syncNewMsg === "object" ? "danger" : "secondary"}
              >
                {typeof syncNewMsg === "boolean"
                  ? t("chat.message_system.SyncNewMessage") + "..."
                  : typeof syncNewMsg === "object"
                  ? t("chat.message_system.error_fetch_new_messages")
                  : null}
              </Typography.Title>
            </div>
          }
          loading={syncNewMsg && syncNewMsg?.loading}
        />
      </Suspense>

      {!from && renderTypingUser()}
      <div id="editor-container" className="flex w-full">
        <motion.div
          initial={{ opacity: 1 }}
          animate={{
            opacity: showAudioInput ? 0 : 1,
            // x: showAudioInput ? "-100%" : 0,
          }}
          exit={{ opacity: 0 }}
          transition={{ delay: 0, duration: 0 }}
          // style={{ width: showAudioInput ? 1 : "100%" }}
          className={`flex w-full flex-row items-center justify-between space-x-2  ${
            isMenuBarActive ? "mt-10" : ""
          }   `}
        >
          {!from && source === "chat" && (
            <Popover
              open={openPopOver}
              onOpenChange={handleOpenChange}
              content={
                <div className="flex flex-col items-start justify-between">
                  <Typography.Text
                    onClick={() => {
                      setOpenPolls(true);
                      handleOpenChange(false);
                    }}
                    type="secondary"
                    className="  flex w-full cursor-pointer items-center space-x-1 rounded-md px-2 py-1 text-base first-letter:capitalize  hover:bg-slate-50"
                  >
                    <BiPoll className=" text-yellow-400" size={20} />
                    {t("chat.polls.title_modal")}{" "}
                  </Typography.Text>
                </div>
              }
              //  title={t("chat.action.option")}
              trigger="click"
            >
              <Button
                //
                disabled={isGuestConnected(currentUser?.role, sphereUserRole)}
                type="dashed"
                size="small"
                shape="circle"
                icon={<PlusOutlined />}
              />
            </Popover>
          )}
          <div className="relative flex-1 ">
            <RichTextInput
              key={selectedConversation?.id + selectedConversation?.type + from}
              onKeyPress={() => {
                clearTimeout(timer.current);
                if (
                  userIsTyping !== 1 &&
                  !externSource.includes(source) &&
                  convertToPlain(msg).trim().length > 1
                ) {
                  handleTyping(1);
                }
              }}
              onKeyUp={() => {
                clearTimeout(timer.current);
                timer.current = setTimeout(() => {
                  handleTyping(2);
                }, 2500);
              }}
              source={
                from_big && from
                  ? "chat_reply_big"
                  : source === "no_chat"
                  ? "no_chat"
                  : from
                  ? "chat_reply"
                  : "chat_main"
              }
              content={msg.text}
              setContent={(e) => {
                let time;
                setMsg({
                  text: e?.trim(),
                  path: from ? "reply" : "main",
                });
                if (convertToPlain(e).length === 0) {
                  clearTimeout(time);
                  time = setTimeout(() => {
                    dispatch(
                      setInputValue({
                        conversationId: selectedConversation?.id,
                        value: "",
                        //  files: [],
                        type: selectedConversation?.type,
                        path,
                      })
                    );
                    clearTimeout(time);
                  }, 300);
                }
              }}
              uploadFile={(e) => {
                setFileFromPaste(e);
              }}
              filesToUpload={fileList}
              FileComponent={
                <Suspense
                  fallback={
                    <div className="my-1">
                      <Loader size={20} />
                    </div>
                  }
                >
                  <FileInput
                    source={externSource.includes(source) ? "EXTERNE" : "CHAT"}
                    defaultFileList={fileFromPase} // default value to send type event.target.files
                    onRemove={onRemoveFile}
                    cancel={cancel}
                    setCancel={setCancel}
                    fileList={fileList}
                    setFileList={(e) => {
                      setFileList(e);
                    }}
                  />
                </Suspense>
              }
              AudioInput={
                source === "chat" &&
                !from && (
                  <Tooltip
                    title={
                      <span className="capitalize"> {t("chat.audio")} </span>
                    }
                  >
                    <Button
                      onClick={() => {
                        if (foundErrorMessage)
                          messageApi.info(t("chat.error_message.re-send"));
                        else {
                          checkMicrophonePermission();
                        }
                      }}
                      type="text"
                      disabled={
                        !searchMsgState.id &&
                        status === "loading" &&
                        fetchStatus !== "idle"
                      }
                      size="small"
                      shape="circle"
                      icon={<AudioOutlined />}
                    />
                  </Tooltip>
                )
              }
              onBlurInput={(e) => {
                if (
                  externSource.includes(source) ||
                  convertToPlain(e).length === 0
                )
                  return;
                let html = e?.replaceAll(/@<\/?p[^>]*>/g, "@ ");
                let tempP = document.createElement("p");
                tempP.innerHTML = html;
                let modifiedHtml = tempP.innerHTML;
                modifiedHtml = modifiedHtml.replace(/(<[^>]*>)\s+/g, "$1");
                dispatch(
                  setInputValue({
                    conversationId: selectedConversation?.id,
                    value: modifiedHtml,
                    type: selectedConversation?.type,
                    path,
                  })
                );
              }}
              watchShowInputMenuBar={watchShowInputMenuBar}
              isMessageSent={isMessageSent}
              maxWidth={maxWidth}
            />
          </div>

          <Button
            disabled={disabledInput(false)}
            type="link"
            size="middle"
            id={
              "send-button-" +
              (from_big && from
                ? "chat_reply_big"
                : source === "no_chat"
                ? "no_chat"
                : from
                ? "chat_reply"
                : "chat_main")
            }
            onClick={() => {
              //  setClicked(true);
              if (foundErrorMessage)
                messageApi.info(t("chat.error_message.re-send"));
              else sendMessage();
            }}
            icon={<SendOutlined />}
            style={{
              visibility:
                showAudioInput && openRecord ? "invisible" : "visible",
            }}
          />
        </motion.div>

        {/* {showAudioInput && !from && ( */}
        {(!from || source === "chat") && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: showAudioInput && openRecord ? 1 : 0 }}
            exit={{ opacity: 0 }}
            transition={{ delay: 0, duration: 0.3 }}
            // style={{ width: showAudioInput ? "100%" : 0 }}
          >
            <Suspense fallback={<Skeleton.Input active />}>
              <RecordComponent
                isRecording={isRecording}
                setIsRecording={setIsRecording}
                open={showAudioInput}
                close={handleCloseAudioInput}
                setOpenRecord={setOpenRecord}
                openRecord={openRecord}
              />
            </Suspense>
          </motion.div>
        )}
      </div>
      {isOpenPolls && (
        <Suspense
          fallback={
            <div className="fixed inset-0 z-[9999] flex h-full w-full   items-center justify-center bg-black/5 ">
              <Loader size="2rem" />
            </div>
          }
        >
          <CreatePollsComponent open={isOpenPolls} setOpen={setOpenPolls} />
        </Suspense>
      )}
      {fileList && fileList.length > 0 && (
        <Suspense fallback={<Skeleton.Button size="large" />}>
          <ShowFileList
            from={from}
            fileList={fileList}
            fileFromPase={fileFromPase}
            onRemove={onRemoveFile}
          />
        </Suspense>
      )}
    </div>
  );
}
const mapStateToProps = (state) => {
  return {
    selectedConversation: state.chat.selectedConversation,
    errorMessages: state.chat.errorMessages,
    userList: state.chat.userList,
    syncNewMsg: state.chat.syncNewMsg,
    mentionState: state.chat.mentionState,
  };
};

export default connect(mapStateToProps, {
  scrollToBottom,
  setSyncNewMsg,
})(InputChat);
