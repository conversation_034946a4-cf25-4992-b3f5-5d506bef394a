import React, { useEffect, useRef } from "react";
import {
  Form,
  InputNumber,
  Input,
  Select,
  Space,
  Badge,
  Button,
  Switch,
} from "antd";
import { useState } from "react";
import { generateAxios } from "../services/axiosInstance";
import { useTranslation } from "react-i18next";
import { PlusCircleOutlined, PlusOutlined } from "@ant-design/icons";
import { colors } from "./Colors";

import { useParams } from "react-router-dom";
import { toastNotification } from "./ToastNotification";
import ColumnColors from "./ColumnColors";
import NewTableDraggable from "./NewTableDraggable";
import LabelTable from "./LabelTable";
import BottomButtonAddRow from "./BottomButtonAddRow";
import { FiSearch } from "react-icons/fi";
import { useSelector } from "react-redux";
import { SubmitKeyPress } from "../utils/SubmitKeyPress";
import { URL_ENV } from "index";
const StepsCheckList = ({ checkListId, data, setData }) => {
  const [form] = Form.useForm();
  const [count, setCount] = useState(0);
  const [editingKey, setEditingKey] = useState("");
  const [id, setId] = useState(null);
  const [search, setSearch] = useState("");
  const [saveData, setSaveData] = useState([]);
  const [loading, setLoading] = useState(false);
  const isEditing = (record) => record.key === editingKey;
  const [t] = useTranslation("common");
  const [color, setColor] = useState("");
  const [rank, setRank] = useState(null);
  const [required, setRequired] = useState(false);
  const [confirmation, setConfirmation] = useState("");
  const [errorForm, setErrorForm] = useState(false);
  const [loadRequired, setLoadRequired] = useState(false);
  const inputRefs = useRef([]);
  const durationRef = useRef(null);
  const { isDeleteRows } = useSelector((state) => state.table);

  const { family } = useParams();
  useEffect(() => {
    setData([]);
  }, [family]);
  useEffect(() => {
    inputRefs.current.forEach((input) => {
      input?.focus();
    });
    // document.getElementById("label")?.focus()
  }, [data.length, id, rank]);
  useEffect(() => {
    if (data.find((el) => !el.id)) {
      document.querySelector(
        `tr[data-row-key="${data.find((el) => !el.id)?.key}"]`
      ).style.height = "60px";
    }
  }, [data]);
  const detectChangeTime = (e) => {
    if (e) {
      const rowElement = document.querySelector(
        `tr[data-row-key="${data[data.length - 1]?.key}"]`
      );
      if (rowElement) {
        rowElement.style.height = `auto`;
      }
    }
  };
  const handleKeyPress = (event) => {
    SubmitKeyPress(event, form);
  };
  const onFinishFailed = (values) => {
    if (values.values.label && values.values.duration && !values.values.date) {
      const rowElement = document.querySelector(
        `tr[data-row-key="${data[data.length - 1]?.key}"]`
      );
      if (rowElement) {
        rowElement.style.height = `75px`;
      }
    }
  };
  function handleInputNumberKeyDown(e) {
    if (e.key === "." && e.target.value.includes(".")) {
      e.preventDefault();
    }
    if (e.key === "-" && !e.target.value) {
      e.preventDefault();
    }
    if (e.key === "e") {
      e.preventDefault();
    }
    if (
      isNaN(parseInt(e.key)) &&
      ![
        "Tab",
        "Backspace",
        "Delete",
        "ArrowLeft",
        "ArrowRight",
        "Enter",
      ].includes(e.key)
    ) {
      e.preventDefault();
    }
  }
  function handleInputNumberKeyDown(e) {
    if (e.key === "." && e.target.value.includes(".")) {
      e.preventDefault();
    }
    if (e.key === "-" && !e.target.value) {
      e.preventDefault();
    }

    if (e.key === "e") {
      e.preventDefault();
    }
    if (
      isNaN(parseInt(e.key)) &&
      ![
        "Tab",
        "Backspace",
        "Delete",
        "ArrowLeft",
        "ArrowRight",
        "Enter",
      ].includes(e.key)
    ) {
      e.preventDefault();
    }
  }
  const onRow = () => {};
  const EditableCell = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    const inputNode =
      inputType === "select" ? (
        <Select
          showSearch
          placeholder={t("tags.selectcolor")}
          style={{
            width: 200,
          }}
          options={colors.map((el) => ({
            label: (
              <Space>
                <Badge color={el.value} /> {t(`colors.${el.label}`)}
              </Space>
            ),
            value: el.value,
          }))}
          optionFilterProp="children"
          filterOption={(input, option) =>
            (
              colors
                .find((el) => el.value === option.value)
                ?.label?.toLowerCase() ?? ""
            ).includes(input.toLowerCase())
          }
          filterSort={(optionA, optionB) =>
            (optionA?.value ?? "")
              .toLowerCase()
              .localeCompare((optionB?.value ?? "").toLowerCase())
          }
          allowClear
        />
      ) : inputType === "requiredSwitch" ? (
        <Switch
          size="small"
          defaultChecked={
            !loading
              ? record.required === "true"
                ? true
                : false
              : form.getFieldsValue().required == false
              ? false
              : form.getFieldsValue().required == "false"
              ? false
              : true
          }
        />
      ) : inputType === "previousSwitch" ? (
        <Switch
          size="small"
          defaultChecked={
            !loading
              ? record.confirmation === "true"
                ? true
                : false
              : form.getFieldsValue().confirmation == false
              ? false
              : form.getFieldsValue().confirmation == "false"
              ? false
              : true
          }
        />
      ) : inputType === "duration" ? (
        <InputNumber
          ref={durationRef}
          onKeyDown={handleInputNumberKeyDown}
          onKeyPress={handleKeyPress}
          min="1"
          addonAfter={
            <Form.Item
              name="date"
              rules={[
                {
                  required: true,
                  // message: "Stage name is required !",
                  message: `${t("table.header.required")}` + " !",
                },
              ]}
              style={{ marginBottom: "-5px", height: "34px" }}
              getValueProps={id ? undefined : () => detectChangeTime()}
            >
              <Select
                style={{ width: "90px", height: "35px" }}
                className="selectTimeCheckList"
                placeholder="time"
              >
                <Select.Option value="minute">
                  {t("helpDesk.minutes")}
                </Select.Option>
                <Select.Option value="hours">
                  {t("helpDesk.hours")}
                </Select.Option>
                <Select.Option value="days">{t("helpDesk.days")}</Select.Option>
              </Select>
            </Form.Item>
          }
        />
      ) : (
        <Input
          ref={(el) => (inputRefs.current[index] = el)}
          placeholder="Step checklist"
          onKeyPress={handleKeyPress}
        />
      );
    return (
      <td {...restProps}>
        {editing ? (
          <Form.Item
            name={dataIndex.toLowerCase()}
            style={{
              margin: 0,
            }}
            rules={[
              {
                required: true,
                message: `${t(`checklist.${dataIndex}`)} ${t(
                  "table.header.isrequired"
                )}`,
              },
            ]}
          >
            {inputNode}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };
  const edit = (record) => {
    if (record) {
      setRequired(record.required);
      form.setFieldsValue({
        label: record.label,
        required: record.required,
        confirmation: record.confirmation,
        duration: record.duration?.split(" ")[0],
        date: record.duration?.split(" ")[1],
      });
      setRank(record.rank);
      setId(record.id);
    } else {
      form.setFieldsValue({
        label: "",
        required: false,
        confirmation: false,
        duration: "",
        date: null,
      });
    }
    setEditingKey(record.key);
  };
  const cancel = (record) => {
    setEditingKey("");
    setErrorForm(false);
    // setCountRequired(0)
    setColor("");
    setId(null);
    if (!record.id) {
      setData(data.filter((item) => item.key !== record.key));
    }
  };

  const updateRequired = async (record, checked) => {
    setLoadRequired(true);
    try {
      const res = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).post(`/steps/update/${record.id}`, {
        label: record.label,
        duration: record.duration,
        checklist_id: checkListId,
        required: checked,
        confirmation: record.confirmation,
      });

      setData((prev) =>
        prev.map((el) =>
          el.id === record.id ? { ...el, required: checked.toString() } : el
        )
      );
      setLoadRequired(false);

     // console.log(record, checked);
    } catch (e) {
      setLoadRequired(false);

      toastNotification("error", t("toasts.somethingWrong"), "topRight");
    }
  };

  const updateConfirmation = async (record, checked) => {
    setLoadRequired(true);
    try {
      const res = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).post(`/steps/update/${record.id}`, {
        label: record.label,
        duration: record.duration,
        checklist_id: checkListId,
        required: record.checked,
        confirmation: checked,
      });

      setData((prev) =>
        prev.map((el) =>
          el.id === record.id ? { ...el, confirmation: checked.toString() } : el
        )
      );
      setLoadRequired(false);

     // console.log(record, checked);
    } catch (e) {
      setLoadRequired(false);

      toastNotification("error", t("toasts.somethingWrong"), "topRight");
    }
  };
  const save = async (key) => {
    setLoading(true);
    setRequired(form.getFieldsValue()?.required);
    if (id) {
      const config = {
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      };
      try {
        const row = await form.validateFields();
        setRequired(row.required == "true" ? true : false);
        setConfirmation(row.confirmation === "true" ? true : false);
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post(`/steps/update/${id}`, {
          label: row.label,
          duration: row.duration + " " + row.date,
          checklist_id: checkListId,
          required: row.required,
          confirmation: row.confirmation,
        });

        setEditingKey("");
        setRank("");
        setErrorForm(false);

        setData(
          data.map((el) =>
            el.id === res.data.data.id
              ? {
                  ...res.data.data,
                  key: res.data.data.id + res.data.data.label,
                }
              : el
          )
        );
        form.setFieldsValue({
          label: "",
          required: false,
          confirmation: false,
          duration: "",
          date: null,
        });
        setLoading(false);
        setRequired(false);
        toastNotification("success", row.label + t("toasts.edit"), "topRight");
      } catch (errInfo) {
        console.log(errInfo);
        setLoading(false);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    } else {
      try {
        const row = await form.validateFields();

        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post(
          "/steps",
          // {
          //   ...Object.fromEntries(
          //     Object.entries(row).filter(([_, v]) => v != "")
          //   ),
          //   pipeline_id,
          //   rank: saveData.length + 1,
          // }
          {
            label: row.label,
            duration: row.duration + " " + row.date,
            checklist_id: checkListId,
            required: row.required,
            confirmation: row.confirmation,
          }
        );
        setEditingKey("");
        setData([
          ...data.filter((el) => el.id),
          { ...res.data.data, key: res.data.data.id },
        ]);

        setSaveData([
          ...data.filter((el) => el.id),
          { ...res.data.data, key: res.data.data.id },
        ]);
        form.setFieldsValue({
          label: "",
          required: false,
          confirmation: false,
          duration: "",
          date: null,
        });
        setLoading(false);
        toastNotification(
          "success",
          row.label + t("toasts.created"),
          "topRight"
        );
      } catch (errInfo) {
        setLoading(false);
        console.log(errInfo);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    }
  };
  useEffect(() => {
    const getStages = async () => {
      setLoading(true);
      try {
        const {
          data: { data },
        } = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get(`steps/getStepsByChecklist/${checkListId}`);
        setData(
          data.map((el, i) => ({ ...el, key: el.id + el.label, rank: i + 1 }))
        );
        setSaveData(
          data.map((el, i) => ({ ...el, key: el.id + el.label, rank: i + 1 }))
        );
        if (data.length > 0) {
          setCount(Math.max(...data.map((el) => el.id)));
        }

        setLoading(false);
      } catch (err) {
        console.log(err);
        setLoading(false);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    };
    if (checkListId) getStages();
  }, [checkListId]);
  console.log(checkListId);
  const handleClick = (event) => {
    event.stopPropagation();
  };

  const columns = [
    {
      title: t("activities.name"),
      dataIndex: "label",
      key: "label",
      editable: true,
      sorter: (a, b) => a.label.localeCompare(b.label),
      render: (_, record) => {
        return (
          <LabelTable record={record} editingKey={editingKey} edit={edit} />
        );
      },
    },
    {
      title: "required",
      dataIndex: "required",
      key: "required",
      width: "90px",
      editable: true,
      render: (_, record) => (
        <Switch
          size="small"
          // defaultChecked={record.required === "true" ? true : false}
          checked={record.required === "true" ? true : false}
          onChange={(checked) => updateRequired(record, checked)}
          // disabled
          loading={loadRequired}
        />
      ),
    },
    {
      title: "Previous REQUIRED",
      dataIndex: "confirmation",
      key: "confirmation",
      width: "90px",
      editable: true,
      render: (_, record) => (
        <Switch
          size="small"
          onChange={(checked) => updateConfirmation(record, checked)}
          checked={record.confirmation === "true" ? true : false}
          // disabled
          loading={loadRequired}
        />
      ),
    },
    {
      title: "duration",
      dataIndex: "duration",
      key: "duration",
      editable: true,
    },
  ];

  const handleAdd = () => {
    setId(null);
    const ids = data.map((object) => {
      return object.id;
    });
    const newData = {
      key: Math.max(...ids) + 1,
      label: "",
      required: false,
      confirmation: false,
      duration: "",
      date: null,
    };

    setData([...data, newData]);
    form.setFieldsValue({
      label: "",
      required: false,
      confirmation: false,
      duration: "",
      date: null,
    });
    setEditingKey(Math.max(...ids) + 1);
    setCount(Math.max(...ids) + 1);
  };
  const filteredData = data.filter((item) => {
    return (
      item.label?.toLowerCase().includes(search.toLowerCase()) ||
      item?.duration?.toString().includes(search.toString())
    );
  });

  return (
    <Space direction="vertical" style={{ width: "100%" }}>
      {/* <Space direction="vertical" style={{ width: "100%" }}>
        <div className="flex justify-between items-center pt-3">
          {pipeline_id ? (
            <Button
              shape="circle"
              icon={<PlusOutlined />}
              disabled={loading ? true : editingKey ? true : false}
              onClick={handleAdd}
              type="primary"
            />
          ) : (
            ""
          )}
        </div>
      </Space> */}
      <div className="ml-2 mr-2 mt-4 rounded	 px-2.5 py-0.5 text-base font-medium text-[#2253d5] ">
        {t(`checklist.stepsChecklist`)}
      </div>
      <div className="flex items-center justify-between ">
        <div
          className={`flex w-full pr-4 transition-all duration-200 ease-in ${
            isDeleteRows ? "pl-[180px]" : "pl-4"
          }  `}
        >
          <Input
            prefix={<FiSearch className="text-slate-400" />}
            onChange={(e) => setSearch(e.target.value)}
            placeholder={t("table.search")}
            style={{ width: "250px" }}
            value={search}
            allowClear
          />
        </div>

        {checkListId ? (
          <Button
            shape="circle"
            icon={<PlusOutlined />}
            disabled={
              loading ? true : editingKey ? true : search ? true : false
            }
            onClick={handleAdd}
            type="primary"
          />
        ) : (
          ""
        )}
      </div>

      <NewTableDraggable
        columns={columns}
        setLoading={setLoading}
        isEditing={isEditing}
        data={filteredData}
        setData={setData}
        loading={loading}
        onRow={onRow}
        save={save}
        edit={edit}
        EditableCell={EditableCell}
        onFinishFailed={onFinishFailed}
        cancel={cancel}
        form={form}
        apiRank="rank-steps"
        editingKey={editingKey}
        api="steps"
        btnText={t("helpDesk.addFolder")}
        pagination={false}
      />
      <BottomButtonAddRow
        editingKey={editingKey}
        data={data}
        text={"Add a step"}
        handleAdd={handleAdd}
        loading={loading}
        search={""}
        pagination={false}
      />
    </Space>
  );
};
export default StepsCheckList;
