import { useState, useEffect, useCallback, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { toastNotification } from "components/ToastNotification";
import useDebounce from "pages/components/UseDebounce/UseDebounce";
import MainService from "services/main.service";
import { Form } from "antd";

const useCompAct360 = () => {
  const { user } = useSelector((state) => state.user);
  const selectedConversation = useSelector(
    (state) => state.ChatRealTime.selectedConversation
  );
  const chat = useSelector((state) => state.chat);
  const usersList = useSelector((state) => state?.chat?.userList);
  const { search } = useSelector((state) => state.form);
  const { t } = useTranslation("common");
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const searchInputRef = useRef();
  const debouncedSearchValue = useDebounce(search, 500);
  const [form] = Form.useForm();

  const {
    openTaskDrawer,
    isUserNotified,
    taskNotifPayload,
    taskNotifDescription,
    totalNotificationNumber,
    taskNotifActionType,
    tasksFilters,
    openTaskRoomDrawer,
    msgTask,
  } = useSelector((state) => state?.TasksRealTime);

  const preferredView = localStorage.getItem("tasks-view");
  const [columnsBackup, setColumnsBackup] = useState([]);

  const [singleTaskData, setSingleTaskData] = useState({});
  const [filterTable, setFilterTable] = useState("");
  const [propsTitle, setPropsTitle] = useState("");
  const [switchViews, setSwitchViews] = useState(
    !preferredView ? "Table" : preferredView
  );
  const [checkedItems, setCheckedItems] = useState([]);
  const [checkedFollowers, setCheckedFollowers] = useState([]);
  const [tasksData, setTasksData] = useState([]);
  const [tasksTypes, setTasksTypes] = useState([]);
  const [loadTasks, setLoadTasks] = useState(false);
  const [loadGuests, setLoadGuests] = useState(false);
  const [loadSpecificTask, setLoadSpecificTask] = useState(false);
  const [createAndUpdateTaskLoading, setCreateAndUpdateTaskLoading] =
    useState(false);
  const [taskToUpdate, setTaskToUpdate] = useState(null);
  const [ownersList, setOwnersList] = useState([]);
  const [guestsList, setGuestsList] = useState([]);
  const [guestsSearchQuery, setGuestsSearchQuery] = useState("");
  const [followersSearchQuery, setFollowersSearchQuery] = useState("");
  const [showTime, setShowTime] = useState(false);
  const [guestsListPage, setGuestsListPage] = useState(1);
  const [guestsListLastPage, setGuestsListLastPage] = useState(null);
  const [followersListPage, setFollowersListPage] = useState(1);
  const [followersListLastPage, setFollowersListLastPage] = useState(null);
  const [selectedStartDate, setSelectedStartDate] = useState("");
  const [selectedEndDate, setSelectedEndDate] = useState("");
  const [selectedStartTime, setSelectedStartTime] = useState("");
  const [selectedEndTime, setSelectedEndTime] = useState("");
  const [loadOwners, setLoadOwners] = useState(false);
  const [pageNumber, setPageNumber] = useState(1);
  const [limit, setLimit] = useState(10);
  const [total, setTotal] = useState(null);
  const [files, setFiles] = useState([]);
  const [events, setEvents] = useState([]);
  const [pipelines, setPipelines] = useState([]);
  const [loadNotifs, setLoadNotifs] = useState(false);
  const [selectedPipeline, setSelectedPipeline] = useState(0);
  const [columns, setColumns] = useState([]);
  const [selectedStageId, setSelectedStageId] = useState(null);
  const [loadUpdateTaskStage, setLoadUpdateTaskStage] = useState(false);
  const [stageIdToFilter, setStageIdToFilter] = useState("");
  const [notifications, setNotifications] = useState([]);
  const [showNotificationsMenu, setShowNotificationsMenu] = useState(false);
  const [showRemindersMenu, setShowRemindersMenu] = useState(false);
  const [notificationsPage, setNotificationsPage] = useState(1);
  const [lastNotificationsPage, setLastNotificationsPage] = useState(null);
  const [deletedTaskIndicator, setDeletedTaskIndicator] = useState(false);
  const [notFoundTaskIndicator, setNotFoundTaskIndicator] = useState(false);
  const [reminders, setReminders] = useState([]);
  const [checkDueDateReminder, setCheckDueDateReminder] = useState(false);
  const [loadingExport, setLoadingExport] = useState(false);
  const [isError, setIsError] = useState(false);
  const [selectedRoles, setSelectedRoles] = useState(null);
  const [openFilterMenu, setOpenFilterMenu] = useState(false);
  const [dateFilter, setDateFilter] = useState(null);
  const [appliedFilters, setAppliedFilters] = useState([]);
  const [selectedFamily, setSelectedFamily] = useState(null);
  const [getTasksError, setGetTasksError] = useState(false);
  const [searchParams, setSearchParams] = useState([]);
  const [openElementDetails, setOpenElementDetails] = useState(false);
  const [openActivity360, setOpenActivity360] = useState(false);
  const [activeTabKey, setActiveTabKey] = useState("generalInfoActivities");
  const [activityLabel, setActivityLabel] = useState("");
  const [emailNotification, setEmailNotification] = useState(false);
  const [countChanges, setCountChanges] = useState(0);
  const [selectedFamilyMembers, setSelectedFamilyMembers] = useState([1, 2, 4]);
  const [filterNotifications, setFilterNotifications] = useState(0);
  const [kpisList, setKpisList] = useState(null);
  const [showCardPopover, setShowCardPopover] = useState({});
  const [allColumns, setAllColumns] = useState([
    {
      value: "activityType",
      label: t("tasks.tableType"),
      disabled: true,
    },
    {
      value: "label",
      label: t("tasks.tableLabel"),
      disabled: true,
    },
    {
      value: "priority",
      label: t("tasks.tablePriority"),
    },
    {
      value: "activityId",
      label: t("tasks.activityId"),
    },
    {
      value: "startDate",
      label: t("tasks.startDate"),
    },
    {
      value: "dueDate",
      label: t("tasks.tableEndDate"),
    },
    {
      value: "pipeline",
      label: "Pipeline",
    },
    {
      value: "moduleElement",
      label: t("tasks.associatedModule"),
    },
    {
      value: "creator",
      label: t("tasks.creator"),
    },
    {
      value: "owner",
      label: t("tasks.tableOwner"),
    },
    {
      value: "guests",
      label: t("tasks.tableGuests"),
    },
    {
      value: "followers",
      label: t("tasks.followersListTitle"),
    },
  ]);

  const [elementDetails, setElementDetails] = useState({
    id: null,
    module: null,
  });
  const [addOnsValues, setAddOnsValues] = useState({
    description: "",
    note: "",
  });
  const debounceGuestsSearch = useDebounce(guestsSearchQuery, 500);
  const debounceFollowersSearch = useDebounce(followersSearchQuery, 500);
  const getSpecificTask = useCallback(async () => {
    if (taskToUpdate) {
      try {
        setLoadSpecificTask(true);
        const response = await MainService.getSpecificTask(taskToUpdate);
        if (response?.data?.message === "This task was deleted") {
          setTaskToUpdate(null);
          setOpenActivity360(false);
          toastNotification("error", t("tasks.activityDeleted"));
          setDeletedTaskIndicator(true);
        } else if (response?.data?.message === "Task not found") {
          setTaskToUpdate(null);
          setOpenActivity360(false);
          toastNotification("error", t("tasks.unathorizedActivity"));
          setNotFoundTaskIndicator(true);
        } else {
          console.log(response?.data?.data);
          setSingleTaskData(response?.data?.data);
          setCheckedItems(
            response?.data?.data?.guests != null
              ? response?.data?.data?.guests
              : checkedItems
          );
          setCheckedFollowers(
            response?.data?.data?.followers !== null
              ? response?.data?.data?.followers
              : checkedFollowers
          );
          setAddOnsValues({
            ...addOnsValues,
            note: response?.data?.data?.note,
            description: response?.data?.data?.description,
          });
          setFiles(response?.data?.data?.upload);
        }
        setLoadSpecificTask(false);
      } catch (error) {
        console.log(`Error ${error}`);
        toastNotification("error", t("toasts.somethingWrong"));
        setLoadSpecificTask(false);
      }
    }
  }, [taskToUpdate]);
  // console.log(singleTaskData);
  useEffect(() => {
    if (taskToUpdate) {
      getSpecificTask();
    }
  }, [getSpecificTask]);
  useEffect(() => {
    if (
      isUserNotified &&
      Object.keys(singleTaskData)?.length > 0 &&
      singleTaskData?.id === taskNotifPayload?.id
    ) {
      getSpecificTask();
    }
  }, [isUserNotified, taskNotifPayload?.id]);
  const getTasksTypes = async () => {
    try {
      const response = await MainService.getTasksTypes();
      setTasksTypes(response?.data?.data?.tasks_type);
    } catch (error) {
      console.log(`Error ${error}`);
      toastNotification("error", t("toasts.somethingWrong"));
    }
  };

  const getTasks = useCallback(
    async (param) => {
      try {
        let priority = tasksFilters?.filters?.priority
          ? tasksFilters?.filters?.priority.toString()
          : "";
        let activePipeline =
          tasksFilters?.filters?.pipeline &&
          tasksFilters?.filters?.pipeline !== null
            ? tasksFilters?.filters?.pipeline.toString()
            : stageIdToFilter;
        let pp =
          selectedPipeline && selectedPipeline !== null ? selectedPipeline : 0;
        let search_param = param
          ? param.toString()
          : searchParams
          ? searchParams.toString()
          : "";
        let payload = {
          limit,
          page: pageNumber,
          search: debouncedSearchValue,
          stages_ids: activePipeline,
          pipeline_id: pp,
          priorities: priority,
          task_type_id: filterTable,
          roles: selectedRoles ? selectedRoles?.toString() : "",
          start: dateFilter ? dateFilter[0].toUpperCase() : "",
          end: dateFilter ? dateFilter[1].toUpperCase() : "",
          family_id: selectedFamily ? selectedFamily : "",
          search_param,
        };
        setLoadTasks(true);
        const response = await MainService.getTasks(payload);
        setTasksData(response?.data?.data);
        setTotal(response?.data?.meta?.total);
        setLoadTasks(false);
        getTasksError && setGetTasksError(false);
      } catch (error) {
        console.error(`Error ${error}`);
        setGetTasksError(true);
        toastNotification("error", t("toasts.somethingWrong"));
        setLoadTasks(false);
      }
    },
    [
      limit,
      pageNumber,
      stageIdToFilter,
      selectedPipeline,
      tasksFilters?.filters?.priority,
      tasksFilters?.filters?.pipeline,
      filterTable,
      debouncedSearchValue,
      selectedRoles,
      dateFilter,
      selectedFamily,
      isUserNotified,
    ]
  );

  const getGuests = useCallback(async () => {
    if (!openActivity360) return;
    try {
      let formData = new FormData();
      formData.append("family_id", selectedFamilyMembers.toString());
      formData.append("search", debounceGuestsSearch);
      setLoadGuests(true);
      const response = await MainService.getFamilyOptions(
        guestsListPage,
        20,
        formData
      );
      if (guestsListPage > 1) {
        setGuestsList([...guestsList, ...response?.data?.data]);
      } else {
        setGuestsList(response?.data?.data);
      }
      setGuestsListLastPage(response?.data?.meta?.last_page);
      setLoadGuests(false);
    } catch (error) {
      console.log(`Error ${error}`);
      toastNotification("error", t("toasts.somethingWrong"));
      setLoadGuests(false);
    }
  }, [
    guestsListPage,
    debounceGuestsSearch,
    selectedFamilyMembers,
    openActivity360,
  ]);

  const getOwners = useCallback(
    async (signal) => {
      if (!openActivity360) return;

      try {
        let formData = new FormData();
        setLoadOwners(true);
        formData.append("family_id", 4);
        formData.append("search", debounceFollowersSearch);
        const response = await MainService.getFamilyOptions("", "", formData);
        if (followersListPage > 1) {
          setOwnersList([...ownersList, ...response?.data?.data]);
        } else {
          setOwnersList(response?.data?.data);
        }
        setFollowersListLastPage(response?.data?.meta?.last_page);
        setLoadOwners(false);
      } catch (error) {
        setLoadOwners(false);
        console.log(`Error ${error}`);
        toastNotification("error", t("toasts.somethingWrong"));
      }
    },
    [followersListPage, debounceFollowersSearch, openActivity360]
  );

  useEffect(() => {
    let abort = new AbortController();
    getOwners(abort?.signal);
    return () => abort?.abort();
  }, [followersListPage, getOwners, debounceFollowersSearch]);

  useEffect(() => {
    let abort = new AbortController();
    getGuests();
    return () => abort?.abort();
  }, [getGuests, guestsListPage, debounceGuestsSearch]);

  const getPipelines = async () => {
    try {
      const response = await MainService.getPipelinesByFamilyTask();
      setSelectedPipeline(
        switchViews === "Kanban" ? response?.data?.data[0]?.id : 0
      );
      setPipelines(response?.data?.data);
    } catch (error) {
      toastNotification("error", t("toasts.somethingWrong"));
      console.log(`Error ${error}`);
    }
  };

  useEffect(() => {
    if (openActivity360) {
      getPipelines();
      getTasksTypes();
    }
  }, [openActivity360]);

  return {
    user,
    selectedConversation,
    chat,
    usersList,
    search,
    t,
    location,
    navigate,
    dispatch,
    searchInputRef,
    debouncedSearchValue,
    openTaskDrawer,
    isUserNotified,
    taskNotifPayload,
    taskNotifDescription,
    totalNotificationNumber,
    taskNotifActionType,
    tasksFilters,
    openTaskRoomDrawer,
    msgTask,
    columnsBackup,
    setColumnsBackup,
    debounceGuestsSearch,
    debounceFollowersSearch,
    singleTaskData,
    setSingleTaskData,
    filterTable,
    setFilterTable,
    propsTitle,
    setPropsTitle,
    switchViews,
    setSwitchViews,
    checkedItems,
    setCheckedItems,
    checkedFollowers,
    setCheckedFollowers,
    tasksData,
    setTasksData,
    tasksTypes,
    setTasksTypes,
    loadTasks,
    setLoadTasks,
    loadGuests,
    setLoadGuests,
    loadSpecificTask,
    setLoadSpecificTask,
    createAndUpdateTaskLoading,
    setCreateAndUpdateTaskLoading,
    taskToUpdate,
    setTaskToUpdate,
    ownersList,
    setOwnersList,
    guestsList,
    setGuestsList,
    guestsSearchQuery,
    setGuestsSearchQuery,
    followersSearchQuery,
    setFollowersSearchQuery,
    showTime,
    setShowTime,
    guestsListPage,
    setGuestsListPage,
    guestsListLastPage,
    setGuestsListLastPage,
    followersListPage,
    setFollowersListPage,
    followersListLastPage,
    setFollowersListLastPage,
    selectedStartDate,
    setSelectedStartDate,
    selectedEndDate,
    setSelectedEndDate,
    selectedStartTime,
    setSelectedStartTime,
    selectedEndTime,
    setSelectedEndTime,
    loadOwners,
    setLoadOwners,
    pageNumber,
    setPageNumber,
    limit,
    setLimit,
    total,
    setTotal,
    files,
    setFiles,
    events,
    setEvents,
    pipelines,
    setPipelines,
    loadNotifs,
    setLoadNotifs,
    selectedPipeline,
    setSelectedPipeline,
    columns,
    setColumns,
    selectedStageId,
    setSelectedStageId,
    loadUpdateTaskStage,
    setLoadUpdateTaskStage,
    stageIdToFilter,
    setStageIdToFilter,
    notifications,
    setNotifications,
    showNotificationsMenu,
    setShowNotificationsMenu,
    showRemindersMenu,
    setShowRemindersMenu,
    notificationsPage,
    setNotificationsPage,
    lastNotificationsPage,
    setLastNotificationsPage,
    deletedTaskIndicator,
    setDeletedTaskIndicator,
    notFoundTaskIndicator,
    setNotFoundTaskIndicator,
    reminders,
    setReminders,
    checkDueDateReminder,
    setCheckDueDateReminder,
    loadingExport,
    setLoadingExport,
    isError,
    setIsError,
    selectedRoles,
    setSelectedRoles,
    openFilterMenu,
    setOpenFilterMenu,
    dateFilter,
    setDateFilter,
    appliedFilters,
    setAppliedFilters,
    selectedFamily,
    setSelectedFamily,
    getTasksError,
    setGetTasksError,
    searchParams,
    setSearchParams,
    openElementDetails,
    setOpenElementDetails,
    openActivity360,
    setOpenActivity360,
    activeTabKey,
    setActiveTabKey,
    activityLabel,
    setActivityLabel,
    emailNotification,
    setEmailNotification,
    countChanges,
    setCountChanges,
    selectedFamilyMembers,
    setSelectedFamilyMembers,
    filterNotifications,
    setFilterNotifications,
    kpisList,
    setKpisList,
    showCardPopover,
    setShowCardPopover,
    allColumns,
    setAllColumns,
    elementDetails,
    setElementDetails,
    addOnsValues,
    setAddOnsValues,
    getSpecificTask,
    getTasksTypes,
    getTasks,
    getGuests,
    getOwners,
    getPipelines,
    form,
  };
};

export default useCompAct360;
