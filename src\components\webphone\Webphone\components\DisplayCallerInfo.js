import { Divider, Space, Tooltip } from "antd";
import useActionCall from "../../../../pages/voip/helpers/ActionCall";
import { PiArrowFatRightFill } from "react-icons/pi";
import DisplayAvatar from "../../../../pages/voip/components/DisplayAvatar";
import { ImUsers } from "react-icons/im";
import { FaUsers } from "react-icons/fa";
import { Fragment } from "react";
import { InfoCircleTwoTone } from "@ant-design/icons";
import DisplayModuleIconAndText from "pages/voip/components/DisplayModuleIconAndText";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { ActionMenu } from "pages/voip/components/CallerColumn";
import { truncateString } from "pages/voip/helpers/helpersFunc";
import { IoArrowUndo } from "react-icons/io5";
import { HiOutlineVideoCamera } from "react-icons/hi2";
//
export const RenderLogWebPhone = ({
  log,
  t,
  currentUser,
  handleDisplayElementInfo,
  handleAddItem,
  isGuest,
}) => {
  //
  const call = useActionCall();
  //
  return log.map((item, i) => (
    <Fragment key={i}>
      {item.dividerDate ? (
        <div className={`sticky top-0 z-10 bg-slate-50`}>
          <Divider
            key={i}
            style={{
              margin: "0rem",
              fontWeight: 600,
            }}
            plain
          >
            {item.dividerDate}
          </Divider>
        </div>
      ) : (
        ""
      )}
      <div key={i} className="rounded-md hover:bg-slate-200">
        <RenderOneLog
          index={i}
          item={item}
          t={t}
          call={call}
          currentUser={currentUser}
          handleDisplayElementInfo={handleDisplayElementInfo}
          handleAddItem={handleAddItem}
          isGuest={isGuest}
        />
      </div>
    </Fragment>
  ));
};
//
export const RenderOneLog = ({
  index,
  item,
  t,
  call,
  currentUser,
  handleDisplayElementInfo,
  handleAddItem,
  isGuest,
}) => {
  //
  const navigate = useNavigate();
  const dispatch = useDispatch();
  //
  const {
    callInfo,
    isQueue,
    isQueueTreated,
    queueInfo,
    isGroup,
    isGroupTreated,
    groupInfo,
    isForwarding,
    forwardingInfo,
    isOnTheNum,
    conf,
    firstTime,
    onTheNum,
    disposition,
    callSense,
    humanDate,
    time,
    icon,
    tooltipMsg,
    isInternalForwarding,
    isInternalTransfer,
    internalForwardingInfo,
  } = item;
  //
  const mainText = `font-semibold truncate `;
  //
  const secondaryTextStyle = (truncate) =>
    `${
      disposition === "missed" ? "text-red-500" : "text-slate-500"
    }  leading-4 font-medium	${truncate ? "truncate" : ""}`;
  //
  // const popContent = (item) =>
  //   item.id ? (
  //     <div className="flex flex-row items-center space-x-2">
  //       <DisplayAvatar name={item.name} urlImg={item.image} size={30} />
  //       <p className={mainText}>{item.name}</p>
  //     </div>
  //   ) : null;

  const handleCallAction = () => {
    if (isGroup) call(groupInfo.number);
    else if (isQueue) call(queueInfo.number);
    else if (!isGroup && !isQueue)
      call(callInfo.number, callInfo?.id, callInfo?.familyId);
  };
  //
  return (
    <div
      key={index}
      className="group/dropDown-time relative flex flex-row items-center space-x-1.5 p-1.5"
    >
      {/* Call Icon (missed, answered call, ...) */}
      <div className="flex w-[6%] items-center">
        <Tooltip placement="topLeft" title={tooltipMsg}>
          <span style={{ fontSize: 17 }}>{icon}</span>
        </Tooltip>
      </div>
      {/* Avatar caller or called */}
      <div className="_avatar_ w-[12.5%] items-center">
        <DisplayAvatar
          name={
            isQueue
              ? queueInfo?.name
              : isGroup
              ? groupInfo?.name
              : callInfo?.name
          }
          urlImg={
            isQueue
              ? queueInfo?.image
              : isGroup
              ? groupInfo?.image
              : callInfo?.image
          }
          size={38}
          icon={
            isQueue ? (
              <ImUsers />
            ) : isGroup ? (
              <FaUsers />
            ) : callInfo.number === "sphere_visio" ? (
              <HiOutlineVideoCamera />
            ) : null
          }
        />
      </div>
      {/* Info about The Call */}
      <div
        onClick={handleCallAction}
        className={`relative  w-[50%] grow cursor-pointer  ${
          disposition === "missed" && "text-red-500"
        }`}
      >
        {/* Info about The Call => main text */}
        <div className="relative flex w-full flex-row space-x-1">
          <p className={`${mainText} `}>
            {callInfo.number === "sphere_visio"
              ? "Sphere Visio"
              : isQueue || isGroup
              ? queueInfo?.name ?? groupInfo?.name
              : callInfo?.name ?? callInfo?.number}
          </p>
          <div className="Group_Queue_Icons cursor-help pr-1">
            {
              // isGroup || isQueue ? (
              //   <Tooltip title={isGroup ? t("voip.callGroup") : t("voip.queues")}>
              //     {isGroup ? (
              //       <FaUsers
              //         style={{
              //           fontSize: 16,
              //         }}
              //       />
              //     ) : (
              //       <ImUsers
              //         style={{
              //           fontSize: 15,
              //         }}
              //       />
              //     )}
              //   </Tooltip>
              // ) :
              !!callInfo.familyId && callInfo.familyId !== 4 ? (
                <DisplayModuleIconAndText
                  familyId={conf ? "conf" : callInfo.familyId}
                  t={t}
                  iconStyle={conf ? { fontSize: 16, cursor: "help" } : null}
                />
              ) : null
            }
          </div>
        </div>
        {/* Info about The Call => secondary text */}
        <div className={`${secondaryTextStyle()} relative`}>
          {isQueue || isGroup ? (
            isQueueTreated || isGroupTreated ? (
              callSense === "outgoing" ? (
                <div className={` flex flex-row space-x-1`}>
                  <span>{groupInfo?.number || queueInfo?.number}</span>
                  <span>{t("voip.treatedBy")}</span>
                  <DisplayAvatar
                    cursor="help"
                    tooltip={true}
                    name={`${truncateString(callInfo?.name, 20)} (${
                      callInfo?.number
                    })`}
                    urlImg={callInfo?.image}
                    size={18}
                  />
                </div>
              ) : (
                <div className={` flex flex-row space-x-1`}>
                  <span>{queueInfo?.number || groupInfo?.number}</span>
                  <span>{t("voip.from")}</span>
                  {!callInfo?.id ? (
                    <span>{callInfo?.number}</span>
                  ) : (
                    <DisplayAvatar
                      cursor="help"
                      tooltip={true}
                      name={`${truncateString(callInfo?.name, 20)} (${
                        callInfo?.number
                      })`}
                      urlImg={callInfo?.image}
                      size={18}
                    />
                  )}
                  {/* <Popover content={popContent(callInfo)}>
                    <span className={`${!!callInfo?.id && "cursor-help"}`}>
                      {callInfo?.number}
                    </span>
                  </Popover> */}
                </div>
              )
            ) : (
              <span>{queueInfo?.number || groupInfo?.number}</span>
            )
          ) : isOnTheNum ? (
            <div className={` flex flex-row space-x-1`}>
              <span>{callInfo?.number}</span>
              <span>{t("voip.onTheNum")}</span>
              <span>{onTheNum}</span>
            </div>
          ) : isForwarding ? (
            <div className="flex flex-row space-x-1">
              <span>{callInfo?.number}</span>
              <PiArrowFatRightFill
                style={{
                  fontSize: 14,
                  color: "rgb(234 88 12)",
                  marginTop: 1,
                }}
              />
              <span>{forwardingInfo?.number}</span>
            </div>
          ) : (
            <div className="flex flex-row space-x-2">
              <span>{callInfo.name ? callInfo.number : null}</span>
              {firstTime ? (
                <Tooltip title={t("voip.firstTime")}>
                  <div
                    style={{ backgroundColor: "#ffe58f" }}
                    className="flex h-4 w-4 cursor-help rounded-full"
                  >
                    <span className="text-[11px] font-semibold text-black">
                      1<sup>st</sup>
                    </span>
                  </div>
                </Tooltip>
              ) : null}
            </div>
          )}
        </div>
      </div>
      {/* If there's more infos about the call */}
      <div className="flex w-[6%] cursor-help">
        {isForwarding ? (
          <Tooltip title={t("voip.referral")}>
            <IoArrowUndo
              style={{
                fontSize: 20,
                transform: "rotate(150deg)",
                color: "#d4380d",
              }}
            />
          </Tooltip>
        ) : isInternalForwarding || isInternalTransfer ? (
          <Tooltip
            key={"isInternalForwarding_isInternalTransfer_info"}
            placement="bottomRight"
            title={
              <Space
                direction="vertical"
                size={1}
                split={
                  <PiArrowFatRightFill
                    style={{
                      fontSize: 16,
                      color: "rgb(234 88 12)",
                      transform: "rotate(90deg)",
                      marginLeft: 7,
                    }}
                  />
                }
              >
                {internalForwardingInfo.map((element, i) => (
                  <div className="flex flex-row items-center space-x-1" key={i}>
                    <div className="_avatar_">
                      <DisplayAvatar
                        name={element?.name}
                        urlImg={element?.image}
                        size={30}
                      />
                    </div>
                    <div className="w-full flex-col space-y-0.5 ">
                      <p
                        className={`truncate font-semibold`}
                        style={{ maxWidth: "8rem" }}
                      >
                        {element.id === currentUser.id
                          ? t("voip.me")
                          : element.name}
                      </p>
                      <p
                        className={
                          callInfo.name
                            ? "font-medium leading-4 text-slate-500"
                            : "truncate font-semibold"
                        }
                      >
                        {element.id === currentUser.id ? null : element.number}
                      </p>
                    </div>
                  </div>
                ))}
              </Space>
            }
          >
            <Tooltip
              key={"isInternalForwarding_isInternalTransfer_title"}
              title={
                isInternalForwarding ? t("voip.forwarding") : t("voip.transfer")
              }
            >
              <InfoCircleTwoTone />
            </Tooltip>
          </Tooltip>
        ) : null}
      </div>
      {/* Time & Dropdown action */}
      <div className=" relative flex w-[12%] flex-row items-center">
        <span
          className={`${
            disposition === "missed" ? "text-red-500" : "text-slate-500"
          }  absolute inset-0 flex items-center group-hover/dropDown-time:invisible`}
        >
          {time}
        </span>
        {callInfo.number !== "sphere_visio" && (
          <div
            key={index}
            className="invisible absolute flex items-center pl-2  group-hover/dropDown-time:visible "
          >
            <ActionMenu
              key={index}
              record={item}
              t={t}
              call={call}
              dispatch={dispatch}
              handleDisplayElementInfo={handleDisplayElementInfo}
              handleAddItem={handleAddItem}
              navigate={navigate}
              isGuest={isGuest}
            />
          </div>
        )}
      </div>
    </div>
  );
};
