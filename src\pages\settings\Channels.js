import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import TabsDetails from "../contacts/src_contacts/components/Tabs";

const Channels = ({ active }) => {
  const [t] = useTranslation("common");

  const [keyTab, setKeyTab] = useState("");

  const navigate = useNavigate();


  const items = [
    {
      label: (
        <div
          onClick={() => {
            setKeyTab("1");
            navigate(`/settings/channels`);
          }}
        >
          {t("menu2.channels")}
        </div>
      ),
      key: "1",
    },
  ];

  return <>{keyTab ? <TabsDetails items={items} /> : ""}</>;
};

export default Channels;
