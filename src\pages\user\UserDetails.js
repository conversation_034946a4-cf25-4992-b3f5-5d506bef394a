import { useState, useEffect, lazy } from "react";
//
import { Card, Form, Avatar, Image, Switch, Layout, Menu } from "antd";
import { CloseOutlined, CheckOutlined } from "@ant-design/icons";
//
import { FiInfo, FiGlobe } from "react-icons/fi";
import { MdSecurity, MdPhonelinkSetup } from "react-icons/md";
//
import GeneraleInfo from "./components/UserGeneraleInfo";
import Security from "../profile/components/ResetPassword";
import Localization from "../settings/LocalisationSettings";
import { URL_ENV } from "index";
//
// const GeneraleInfo = lazy(() => import("./components/UserGeneraleInfo"))
// const Security = lazy(() => import("../profile/components/ResetPassword"))
// const Localization = lazy(() => import("../settings/LocalisationSettings"))

const UserDetails = () => {
  const { <PERSON><PERSON>, <PERSON>er, Sider, Content } = Layout;
  const [form] = Form.useForm();
  const { Meta } = Card;
  const [userIsActive, setUserIsActive] = useState(true);
  const [menuOption, setMenuOption] = useState("generale_info");
  //
  // console.log(menuOption);
  const RenderContent = () => {
    switch (menuOption) {
      case "generale_info":
        return <GeneraleInfo />;
      case "security":
        return (
          <div className="p-4">
            <Security />
          </div>
        );
      case "voip_config":
        return <div className="p-4">VoIP configuration</div>;
      case "localization":
        return (
          <div className="p-4">
            <Localization />
          </div>
        );
      default:
        break;
    }
  };

  return (
    <Layout className="min-h-full p-4">
      {/* <Form form={form}> */}
      <Card>
        <div className="flex flex-row justify-between px-4">
          <Meta
            avatar={
              <Avatar
                size={60}
                src={
                  URL_ENV?.REACT_APP_BASE_URL +
                  URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                  "hassine_basla.jpg"
                }
              />
            }
            title="Hassine Turki Basla"
            description="Front-End Developer"
          />
          <div className="flex flex-row space-x-4">
            <p
              className={`text-base font-semibold ${
                userIsActive ? "text-green-600" : "text-red-500"
              }`}>
              {userIsActive ? "Active" : "Inactive"}
            </p>
            <Switch
              checked={userIsActive}
              onChange={(checked) => setUserIsActive(checked)}
              checkedChildren={<CheckOutlined />}
              unCheckedChildren={<CloseOutlined className="text-red-500" />}
            />
          </div>
        </div>
      </Card>
      <Layout className="my-4">
        <Sider style={siderStyle}>
          <Menu
            onSelect={(item) => setMenuOption(item.key)}
            // onClick={onClick}
            // style={{
            //   width: 256,
            // }}
            // openKeys={['sub1']}
            // selectedKeys={[current]}
            mode="inline"
            // theme="dark"
            items={items}
          />
        </Sider>
        <Content style={contentStyle}>
          <RenderContent />
        </Content>
      </Layout>
      {/* </Form> */}
    </Layout>
  );
};

export default UserDetails;
//
function getItem(icon, label, key, children, theme) {
  return {
    icon,
    label,
    key,
    children,
    theme,
  };
}
const items = [
  getItem(
    <FiInfo className="h-4 w-4" />,
    "Generale Infos",
    "generale_info",
    null,
    "dark"
  ),
  getItem(<MdSecurity className="h-4 w-4" />, "Security", "security"),
  getItem(
    <MdPhonelinkSetup className="h-4 w-4" />,
    "VoIP Config",
    "voip_config"
  ),
  getItem(<FiGlobe className="h-4 w-4" />, "Localization", "localization"),
];
//
const headerStyle = {
  textAlign: "center",
  color: "#fff",
  height: 64,
  paddingInline: 50,
  lineHeight: "64px",
  backgroundColor: "#7dbcea",
};
const contentStyle = {
  // padding: "1rem",
  height: "fit-content",
  // textAlign: "center",
  // minHeight: 120,
  // lineHeight: "120px",
  color: "black",
  backgroundColor: "white",
  marginLeft: "1rem",
};
const siderStyle = {
  maxHeight: `${items.length}rem`,
  borderRadius: "1rem",
  // textAlign: "center",
  // lineHeight: "120px",
  // color: "black",
  // backgroundColor: "white",
};
const footerStyle = {
  textAlign: "center",
  color: "#fff",
  backgroundColor: "#7dbcea",
};
