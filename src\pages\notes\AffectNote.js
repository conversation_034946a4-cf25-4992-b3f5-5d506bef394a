import { CloseCircleFilled, LoadingOutlined } from "@ant-design/icons";
import {
  Avatar,
  Button,
  Checkbox,
  Form,
  Input,
  List,
  Modal,
  Select,
  Spin,
  Tooltip,
} from "antd";
import NoteAvatar from "pages/components/DetailsProfile/Activities/Notes/NoteAvatar";
import React, { useEffect, useLayoutEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { getName } from "pages/layouts/chat/utils/ConversationUtils";
import { URL_ENV } from "index";
import { AvatarChat } from "components/Chat";
import MainService from "services/main.service";
import { toastNotification } from "components/ToastNotification";
import { useDispatch } from "react-redux";
import { updateNoteFamilyAndElement } from "new-redux/actions/selfnotes.actions/selfnotes";

function AffectNote({ open, setOpen, item }) {
  //   const voipCollegues = useSelector((state) => state.voip.collegues);

  const { t } = useTranslation("common");

  const [form] = Form.useForm();

  const dispatch = useDispatch();

  const { families } = useSelector((state) => state?.families);

  const [familyElements, setFamilyElements] = React.useState([]);
  const [loadFamilyElements, setLoadFamilyElements] = React.useState(false);

  const [loadingAffection, setLoadingAffection] = React.useState(false);

  const affect = (values) => {
    console.log("values", values);
    console.log("item", item);
    let data = { ...values, note_id: item._id };
    console.log("data", data);
    setLoadingAffection(true);

    MainService.affectNoteToFamilyElement(data)
      .then((response) => {
        console.log("response", response?.data?.data);
        dispatch(updateNoteFamilyAndElement(response?.data?.data));
        setOpen(false);
        toastNotification("success", t("selfNotes.affectationSaved"));
      })
      .catch((error) => {
        toastNotification("error", t("selfNotes.affectationSavedError"));
        console.error("error", error);
      })
      .finally(() => {
        setLoadingAffection(false);
        // setOpen(false);
      });
  };

  const [deleteAffectationLoading, setDeleteAffectationLoading] =
    useState(false);

  const deleteAffectation = () => {
    setDeleteAffectationLoading(true);
    let data = {
      note_id: item._id,
    };

    MainService.deleteAssociationNoteToFamilyElement(data)
      .then((response) => {
        console.log("response", response);
        dispatch(
          updateNoteFamilyAndElement({
            _id: item._id,
            family_id: null,
            element_id: null,
            element_data: null,
            family_data: null
          })
        );
        setOpen(false);
        toastNotification("success", t("selfNotes.affectationDeleted"));
      })
      .catch((err) => {
        console.log("err", err);
      })
      .finally(() => {
        setDeleteAffectationLoading(false);
      });
  };

  const getFamilyElementsById = async (id) => {
    MainService.getFamilyElement(id)
      .then((response) => {
        const elements = response?.data?.data;
        if (Array.isArray(elements)) {
          setFamilyElements(elements);
        } else {
          console.error("Unexpected data format:", elements);
          setFamilyElements([]); // fallback to empty array
        }
      })
      .catch((error) => {
        console.error("error", error);
        setFamilyElements([]); // handle the error by setting an empty array
      })
      .finally(() => {
        setLoadFamilyElements(false);
      });
  };

  //handle the case if it has already affection value
  useLayoutEffect(() => {
    let isMounted = true;

    if (isMounted) {
      if (item?.family_id && item?.element_id) {
        getFamilyElementsById(item.family_id);

        //get the family from the family list
        const family = families.find((f) => f.id == item?.family_id);

        form.setFieldsValue({
          family_id: family.id,
          element_id: item.element_id,
        });
      }
    }

    return () => {
      isMounted = false;
    };
  }, [item]);

  const handleSelectModule = (value) => {
    getFamilyElementsById(value);
    form.setFieldsValue({ element_id: null });
  };

  console.log("families", families);
  return (
    <Modal
      title={t("selfNotes.affectNote")}
      // style={{ width: "700px" }}
      visible={open}
      //   onOk={() => {
      //     share();
      //   }}
      onCancel={() => {
        setOpen(false);
      }}
      //   okText="Share"
      //   cancelText="Cancel"
      //   confirmLoading={shareLoading}
      //   disableOk={selectedCollegues.length === 0}
      footer={null}
      width={700}
    >
      <Form
        layout="vertical"
        onFinish={(values) => {
          console.log("values", values);
          affect(values);
        }}
        form={form}
      >
        <Form.Item
          name="family_id"
          label={t("selfNotes.family")}
          rules={[{ required: true, message: t("selfNotes.familyRequired") }]}
        >
          <Select
            showSearch
            placeholder={t("selfNotes.selectFamily")}
            optionFilterProp={["label"]}
            filterOption={(input, option) =>
              option?.label?.toLowerCase()?.indexOf(input?.toLowerCase()) >= 0
            }
            onSelect={handleSelectModule}
            options={
              families &&
              families.map((family) => ({
                label: family.label,
                value: family.id,
              }))
            }
          />
        </Form.Item>
        <Form.Item
          name="element_id"
          label={t("selfNotes.element")}
          rules={[{ required: true, message: t("selfNotes.elementRequired") }]}
        >
          <Select
            showSearch={familyElements && familyElements.length > 0}
            optionFilterProp={["label_data"]}
            placeholder={t("selfNotes.selectElement")}
            disabled={
              !Array.isArray(familyElements) || familyElements.length === 0
            } // added check here
            filterOption={(input, option) =>
              option?.label?.toLowerCase()?.indexOf(input?.toLowerCase()) >= 0
            }
            options={
              familyElements &&
              familyElements.map((element) => ({
                label: element.label_data,
                value: element.id,
              }))
            }
          />
        </Form.Item>
        <div className="mt-2 flex w-full items-center justify-between">
          {item?.family_id && item?.element_id && (
            <Form.Item>
              <Button
                type="primary"
                danger
                onClick={() => {
                  deleteAffectation();
                  setOpen(false);
                }}
                loading={deleteAffectationLoading}
              >
                {t("selfNotes.deleteAffectation")}
              </Button>
            </Form.Item>
          )}
          <div className="flex w-full justify-end space-x-1">
            <Form.Item>
              <Button onClick={() => setOpen(false)}>
                {t("selfNotes.cancel")}
              </Button>
            </Form.Item>
            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                disabled={deleteAffectationLoading}
                loading={loadingAffection}
              >
                {t("selfNotes.associate")}
              </Button>
            </Form.Item>
          </div>
        </div>
      </Form>
    </Modal>
  );
}

export default AffectNote;
