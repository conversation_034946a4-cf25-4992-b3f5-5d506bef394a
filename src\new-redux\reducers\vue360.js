import {
  SET_ACTIVE_ACTIVITY360,
  SET_ACTIVE_TAB360,
  SET_CONTACT_INFO_FROM_DRAWER,
  SET_NEW_INTERACTION,
  SET_OPEN_VIEW360_IN_DRAWER,
  SET_OPEN_CHAT_VIEW_SPHERE,
  SET_NBR_CHAT_VIEW_SPHERE,
  SET_CHAT_VIEW_SPHERE_FROM_DRAWER,
  SET_PREV_IDS_FROM_DRAWER,
  REMOVE_PREV_IDS_FROM_DRAWER,
  RESET_PREV_IDS_FROM_DRAWER,
  SET_ACTIVE_MENU_360,
} from "../constants";

const initialState = {
  activeTab360: "",
  activeActivity360: "",
  // userPreferences: [],
  newInteraction: { type: "updateElement" },
  openView360InDrawer: false,
  contactInfoFromDrawer: {},
  openChatInViewSphere: false,
  chatInViewSphere: { number: 0, id: "", relation_id: "" },
  chatInViewSphereFromDrawer: { number: 0, id: "", relation_id: "" },
  prevIds: [],
  activeMenu360: "4",
};

const vue360 = (state = initialState, action) => {
  const { type, payload } = action;
  switch (type) {
    case SET_ACTIVE_TAB360:
      return {
        ...state,
        activeTab360: payload,
      };
    case SET_ACTIVE_MENU_360:
      return {
        ...state,
        activeMenu360: payload,
      };
    case SET_ACTIVE_ACTIVITY360:
      return {
        ...state,
        activeActivity360: payload,
      };
    case SET_NEW_INTERACTION:
      return {
        ...state,
        newInteraction: payload,
      };
    case SET_OPEN_VIEW360_IN_DRAWER:
      return {
        ...state,
        openView360InDrawer: payload,
      };
    case SET_OPEN_CHAT_VIEW_SPHERE:
      return {
        ...state,
        openChatInViewSphere: payload,
      };
    case SET_CONTACT_INFO_FROM_DRAWER:
      return {
        ...state,
        contactInfoFromDrawer: payload,
      };
    case SET_NBR_CHAT_VIEW_SPHERE:
      return {
        ...state,
        chatInViewSphere: payload,
      };
    case SET_CHAT_VIEW_SPHERE_FROM_DRAWER:
      return {
        ...state,
        chatInViewSphereFromDrawer: payload,
      };
    case SET_PREV_IDS_FROM_DRAWER:
      return {
        ...state,
        prevIds: [...state.prevIds, payload],
      };
    case REMOVE_PREV_IDS_FROM_DRAWER:
      return {
        ...state,
        prevIds: state.prevIds.filter((el) => el !== payload),
      };
    case RESET_PREV_IDS_FROM_DRAWER:
      return {
        ...state,
        prevIds: [],
      };
    // case SAVE_PREFERENCES:
    //   return {
    //     ...state,
    //     userPreferences: payload,
    //   };

    default:
      return state;
  }
};

export default vue360;
