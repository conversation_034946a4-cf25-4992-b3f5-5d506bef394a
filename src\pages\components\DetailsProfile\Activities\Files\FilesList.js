import React, { useState, useEffect } from "react";
import {
  FileTextOutlined,
  EyeOutlined,
  DownloadOutlined,
  DeleteOutlined,
  EditOutlined,
  GlobalOutlined,
  TeamOutlined,
} from "@ant-design/icons";
import { Empty, Popconfirm, Spin, Tooltip, Typography, message } from "antd";
import MainService from "../../../../../services/main.service";
import { useSelector, useDispatch } from "react-redux";
import { renderFileSize } from "./utlis";
// import moment from "moment";
import { getName } from "../../../../layouts/chat/utils/ConversationUtils";
import {
  modifyFile360Label,
  removeFile360,
  setFiles360,
} from "../../../../../new-redux/actions/files.actions/files";
import moment from "moment-timezone";
import RenderFileExtension from "./RenderFileExtension";
import { RiFileEditLine } from "react-icons/ri";
import ModifyFile from "./ModifyFile";
import SearchFile from "./SearchFile";
import { useTranslation } from "react-i18next";
import NoteAvatar from "../Notes/NoteAvatar";
import { URL_ENV } from "index";
import { toastNotification } from "components/ToastNotification";
import { noteDate } from "pages/voip/helpers/helpersFunc";

const { Paragraph } = Typography;

function FilesList({ setKpi = () => {},contactInfo }) {
  const dispatch = useDispatch();
  const [t] = useTranslation("common");



  const dateConfig = useSelector(
    (state) => state?.user?.user?.location?.date_format
  );

  const dateTimeConfig = useSelector(
    (state) => state?.user?.user?.location?.time_format
  );

  const dateTimeZoneConfig = useSelector(
    (state) => state?.user?.user?.location?.default_timezone
  );

  const globalDateFormat = (date, time) => {
    return `${date} ${time}`;
  };

  const files = useSelector((state) => state?.files?.files);

  const [loading, setLoading] = useState(true);

  const [creator, setCreator] = useState(null);
  function handleClick(e, file) {
    e.preventDefault();

    if (file) {
      fetch(`${URL_ENV?.REACT_APP_BASE_URL}${file.path}`)
        .then((response) => {
          //console.log(response);
          return response.blob();
        })
        .then((blob) => {
         // console.log(blob);
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement("a");
          a.style.display = "none";
          a.href = url;
          a.download = file.file_name;
          document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(url);
        })
        .catch((err) => {
          console.log(err);
          alert("Une erreur s'est produite");
        });
    }
  }

  const connectedUserId = useSelector((state) => state?.user?.user?.id);
  const defaultUsr = useSelector((state) => state?.user?.user);

  const renderVisibility = (file) => {

    if(defaultUsr?.role?.toLowerCase() === "guest" || defaultUsr?.role?.toLowerCase() === "superguest") {
      return null;
    } else {
      if (file?.public === true) {
        return (
          <>
          <Tooltip title={t("notes360.public")}>
            <GlobalOutlined className="text-base text-gray-500 bg-white rounded-full bg-white rounded-full p-0.5 hover:bg-gray-100 border border-solid" />
          </Tooltip>
          </>
          
        );
      } else if (file?.public === false) {
        return (
          <>
          <Tooltip title={t("notes360.internal")}>
            <TeamOutlined className="text-base text-gray-500 font-bold bg-white rounded-full p-0.5 hover:bg-gray-100 border border-solid" />
          </Tooltip>
          </>
         
        );
      } else {
        return null;
      }

    }

};

  const [fileToModify, setFileToModify] = useState(null);

  const deleteFile = (file) => {
    MainService.deleteFile360(file?._id)
      .then((response) => {
       // console.log(response);
        // dispatch(removeFile360(file?._id["$oid"]));
        dispatch(removeFile360(file?._id));
        // message.success("File deleted successfully");
        setKpi((prev) =>
          prev.map((el) =>
            el.title === "Files" ? { ...el, value: el.value - 1 } : el
          )
        );
        toastNotification(
          "success",
          t("files360.deleteFileSuccess", {
            filename: file.file_label,
          })
        );
      })
      .catch((error) => {
        console.log(error);
      });
  };

  const updateFile = (file, label) => {
    if (file.file_label == label) return;
    if (label == "") return;
    dispatch(
      modifyFile360Label({
        _id: file?._id,
        file_label: label,
      })
    );
    MainService.updateLabelFile360(file?._id, {
      file_label: label,
    })
      .then((response) => {
        //console.log(response);
        // message.success("File label updated successfully");
        toastNotification(
          "success",
          t("files360.updateLabel", {
            filename: file.file_label,
          })
        );
        // dispatch(
        //   modifyFile360Label({
        //     _id: file?._id,
        //     file_label: label,
        //   })
        // );
      })
      .catch((error) => {
        console.log(error);
        message.error("An error occured");
      });
  };

  const [fileToUpdate, setFileToUpdate] = useState(null);

  useEffect(() => {
    setLoading(true);
    MainService.getFiles360ByElement(contactInfo?.id)
      .then((response) => {
       // console.log(response?.data?.data);

        // const filesArray = Object.keys(response?.data?.data).map((key) => {
        //   return {
        //     ...response?.data?.data[key],
        //     creator: response?.data?.data?.creator[0],
        //   };
        // });

        // setCreator(response?.data?.data?.creator[0]);

        // //remove the last element of the array which is the creator
        // filesArray.pop();

        dispatch(setFiles360(response?.data?.data));

        // setFiles(response?.data?.data);
      })
      .catch((error) => {
        console.log(error);
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  return (
    <>
      <SearchFile />
      {!loading ? (
        <>
          {files.length > 0 ? (
            <div className="">
              {files &&
                files
                  //order by created_at
                  .sort((a, b) => {
                    return new Date(b.created_at) - new Date(a.created_at);
                  })
                  .map((file, index) => (
                    <div
                      className="my-2 rounded-md border border-gray-500 px-4 py-4"
                      style={{
                        border: "1.5px solid #E5E7EB",
                      }}
                    >
                      <div
                        key={index}
                        className=" flex w-full items-center justify-between space-x-2 "
                      >
                        <div className="flex w-full space-x-4">
                         <div className="relative">
                         <RenderFileExtension
                            //extract the file extension which is the last element of the array
                            fileExtension={file.file_name.split(".").pop()}
                          />
                          <div
                            className="absolute top-9 right-0"
                          >
                            {renderVisibility(file)}
                          </div>
                          </div>
                          <div className="w-full flex-col">
                            <div className="flex w-full items-center justify-between space-x-2">
                              <p className="text-md cursor-pointer font-bold">
                                <Paragraph
                                  editable={
                                    connectedUserId == file?.creator_id
                                      ? {
                                          triggerType: ["text", "icon"],
                                          onChange: (value) => {
                                            //console.log("On change", value);
                                            updateFile(file, value);
                                          },
                                          //editable input size
                                          // autoSize: { maxRows: 1, minRows: 1 },
                                        }
                                      : false
                                  }
                                >
                                  {file.file_label}
                                </Paragraph>
                              </p>
                              <div className="flex cursor-pointer space-x-3">
                                {/* {connectedUserId == file?.creator_id
                          ? `YES EQUAL ${connectedUserId} ${file?.creator_id}`
                          : `NOT EQUAL ${connectedUserId} ${file?.creator_id}`} */}

                                {connectedUserId == file?.creator_id && (
                                  <>
                                    <Tooltip
                                      title={t("files360.modifyFile")}
                                      placement="top"
                                    >
                                      <a
                                        style={{
                                          all: "unset",
                                        }}
                                      >
                                        <EditOutlined
                                          className="mx-1.5 text-sm"
                                          onClick={() => {
                                            if (
                                              fileToModify?._id == file?._id
                                            ) {
                                              setFileToModify(null);
                                            } else {
                                              setFileToModify(file);
                                            }
                                          }}
                                        />
                                      </a>
                                    </Tooltip>
                                    <Tooltip
                                      title={t("files360.deleteFile")}
                                      placement="top"
                                    >
                                      <a
                                        style={{
                                          all: "unset",
                                        }}
                                      >
                                        <Popconfirm
                                          title={t("files360.deleteFileTitle")}
                                          description={t(
                                            "files360.deleteFileDescription"
                                          )}
                                          onConfirm={() => deleteFile(file)}
                                          // onCancel={cancel}
                                          okText={t("files360.onOk")}
                                          cancelText={t("files360.onCancel")}
                                        >
                                          <DeleteOutlined className="mx-1.5 text-sm" />
                                        </Popconfirm>
                                      </a>
                                    </Tooltip>
                                  </>
                                )}
                                <Tooltip
                                  title={t("files360.viewFile")}
                                  placement="top"
                                >
                                  <a
                                    href={`${URL_ENV?.REACT_APP_BASE_URL}${file.path}`}
                                    target="_blank"
                                    style={{
                                      all: "unset",
                                    }}
                                  >
                                    <EyeOutlined className="mx-1.5 text-sm" />
                                  </a>
                                </Tooltip>

                                <Tooltip
                                  title={t("files360.downloadFile")}
                                  placement="top"
                                >
                                  <a
                                    style={{
                                      all: "unset",
                                    }}
                                  >
                                    <DownloadOutlined
                                      className="mx-1.5 text-sm"
                                      onClick={(e) => {
                                        handleClick(e, file);
                                      }}
                                    />
                                  </a>
                                </Tooltip>
                              </div>
                            </div>
                            <p className="text-md cursor-pointer ">
                              <span>{t("files360.fileName")}</span>{" "}
                              <span className="font-semibold">
                                {file.file_name} ( {renderFileSize(file.size)} )
                              </span>
                            </p>
                            <p className="text-md">
                              <span>{t("files360.fileUploadedAt")}</span>{" "}
                              <span className="font-semibold">
                                {/* {moment
                                  .tz(file.created_at, dateTimeZoneConfig)
                                  .format(
                                    globalDateFormat(dateConfig, dateTimeConfig)
                                  )} */}
                                {noteDate(file.created_at, t, dateConfig, dateTimeConfig, dateTimeZoneConfig)}
                              </span>
                            </p>{" "}
                            {file?.updated_at && (
                              <p className="text-md">
                                <span>{t("files360.fileUpatedAt")}</span>{" "}
                                <span className="font-semibold">
                                  {/* {moment
                                    .tz(file.updated_at, dateTimeZoneConfig)
                                    .format(
                                      globalDateFormat(
                                        dateConfig,
                                        dateTimeConfig
                                      )
                                    )} */}
                                    {noteDate(file.created_at, t, dateConfig, dateTimeConfig, dateTimeZoneConfig)}
                                </span>
                              </p>
                            )}
                            <p className="text-md mt-1">
                              <span>{t("files360.fileCreator")} </span>{" "}
                              <NoteAvatar
                                avatar={file?.creator?.avatar}
                                name={file?.creator?.label}
                                size={"default"}
                              />
                              <span className="ml-1 font-semibold">
                                {getName(file?.creator?.label, "name")}
                              </span>
                            </p>
                          </div>
                        </div>
                      </div>
                      {fileToModify?._id == file?._id && (
                        <ModifyFile
                          fileToModify={fileToModify}
                          setFileToModify={setFileToModify}
                        />
                      )}
                    </div>
                  ))}
            </div>
          ) : (
            <Empty />
          )}
        </>
      ) : (
        <div className="flex h-64 items-center justify-center">
          <Spin />
        </div>
      )}
    </>
  );
}

export default FilesList;
