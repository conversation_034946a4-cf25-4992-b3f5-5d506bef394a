import { useCallback, useState } from "react";

const useLocalStorage = (key, initialValue) => {
  const [storedValue, setStoredValue] = useState(() => {
    if (typeof window === "undefined") {
      return initialValue;
    }
    try {
      const item = localStorage.getItem(key);
      return item ?? initialValue;
    } catch (error) {
      console.log(error);
      return initialValue;
    }
  });

  const setValue = useCallback(
    (value) => {
      try {
        setStoredValue(value);

        if (typeof window !== "undefined") {
          localStorage.setItem(key, value);
        }
      } catch (error) {
        console.log(error);
      }
    },
    [key]
  );
  return [storedValue, setValue];
};

export default useLocalStorage;
