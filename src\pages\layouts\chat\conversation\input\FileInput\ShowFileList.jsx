import {
  DeleteOutlined,
  FileOutlined,
  PictureOutlined,
  VideoCameraOutlined,
} from "@ant-design/icons";
import { Progress, Typography, Button, Tooltip } from "antd";
import React from "react";
import { useTranslation } from "react-i18next";
import { Loader } from "components/Chat";
import "./fileInput.css";

const ShowFileList = ({ fileList, onRemove, fileFromPase }) => {
  const { t } = useTranslation("common");
  return (
    <>
      {fileList && [...fileList, fileFromPase].length > 0 && (
        <div className=" flex max-w-2xl items-center gap-x-3 overflow-x-auto  pt-1 2xl:max-w-5xl">
          {fileList.map((item) => (
            <div
              key={item.uid}
              className={`border ${
                item.status === "uploading"
                  ? "border-dashed p-1.5 "
                  : "border-solid  p-3"
              }  flex max-h-20  w-60  items-center whitespace-nowrap rounded-md bg-white   
              ${
                item.status === "error"
                  ? "border-[#ff4d4f] "
                  : "border-[#d9d9d9] "
              }
               justify-between space-x-3`}
            >
              {item.status === "uploading" ? (
                <Loader size={32} />
              ) : (
                <div
                  className={`imgThumbnail  ${
                    item.status === "error"
                      ? "text-[#ff4d4f]"
                      : " text-black/50"
                  }`}
                >
                  {item.type?.includes("image") ? (
                    item.status === "error" ? (
                      <PictureOutlined className="text-2xl" />
                    ) : (
                      <img
                        className=" size-6 rounded-md object-contain"
                        src={`${item.url}`}
                        alt={item.file_name}
                      />
                    )
                  ) : item.type?.includes("video") ? (
                    <VideoCameraOutlined className=" text-2xl" />
                  ) : (
                    <FileOutlined className=" text-2xl" />
                  )}
                </div>
              )}
              <div className="flex w-full flex-col ">
                <div className="flex items-center   justify-between ">
                  <div className="ml-1 flex w-full flex-col justify-start gap-y-0.5 font-semibold">
                    <Tooltip title={item.file_name}>
                      <Typography.Text
                        className=" w-32 truncate "
                        type={item.status === "error" ? "danger" : "secondary"}
                      >
                        {item.file_name}
                      </Typography.Text>
                    </Tooltip>
                    {item.status === "done" && (
                      <Typography.Text
                        className=" w-full text-xs  "
                        type="secondary"
                      >
                        {item.size}
                      </Typography.Text>
                    )}
                  </div>

                  <Tooltip title={t("chat.action.delete")}>
                    <Button
                      onClick={() => onRemove(item)}
                      type="text"
                      danger={item.status === "error"}
                      icon={<DeleteOutlined />}
                    />
                  </Tooltip>
                </div>
                {item.status === "uploading" && (
                  <Progress size="small" percent={item.percent} />
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </>
  );
};

export default ShowFileList;
