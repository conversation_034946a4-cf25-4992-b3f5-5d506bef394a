import { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>ati<PERSON>, Tooltip } from "antd";
import { Speech } from "lucide-react";
import { setQueueInDashboard } from "new-redux/actions/dashboard.actions";
import React, { useMemo } from "react";
import CountUp from "react-countup";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import {
  DonutChartWithDrillDown,
  DonutChartWithDrillDownWithPercent,
  PieChartWithLegend,
} from "./components/ChartsDashboard";
import { HiPhoneIncoming, HiPhoneOutgoing } from "react-icons/hi";
import { MdPhoneMissed } from "react-icons/md";
import { useNavigate } from "react-router-dom";
import { GoTo, stylesCard } from "./Home4";
import EmptyPage from "components/EmptyPage";
export const convertToHour = (duree) => {
  const parties = duree?.split(":");
  if (Array.isArray(parties) && parties.length === 3) {
    const heures = parseInt(parties[0]);
    const minutes = parseInt(parties[1]);
    const secondes = parseInt(parties[2]);

    // Construction de la chaîne de caractères pour l'affichage
    let affichage = "";
    if (heures > 0) {
      affichage += heures + "h ";
    }
    if (minutes > 0) {
      affichage += minutes + "m ";
    }
    affichage += secondes + "s";

    return affichage;
  }
};

const CardQueue2 = ({
  start,
  end,
  source = "",
  selectedQueue,
  setSelectedQueue,
  backgroundImagecard,
}) => {
  const gridStyle = {
    width: "30%",
    padding: "13px",
    background: "white",
    marginBottom: "1px",

    // textAlign: "center",
  };
  const {
    receivedTodayCall,
    totalQueues,
    //localhost:3000/logs
    loading,
    allQueues,
  } = useSelector((state) => state.dashboardRealTime);
  const { user } = useSelector((state) => state.user);
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const formatter = (value) => <CountUp end={value} separator="," />;
  const data = useMemo(() => {
    return {
      data: [
        {
          name: t("dashboard.answered", {
            plural: totalQueues[0]?.answered_calls > 1 ? "s" : "",
          }),
          y: Number(totalQueues[0]?.answered_calls),
          color: "#16a34a",
        },
        {
          name: t("dashboard.missedCalls", {
            plural: totalQueues[0]?.no_answered_calls > 1 ? "s" : "",
            pluriel: totalQueues[0]?.no_answered_calls > 1 ? "x" : "",
          }),
          y: Number(totalQueues[0]?.no_answered_calls),
          color: "#EF4444",
        },
        {
          name: t("dashboard.lostNotRecalled", {
            plural:
              totalQueues[0]?.no_answered_calls_not_returned > 1 ? "s" : "",
          }),
          y: Number(totalQueues[0]?.no_answered_calls_not_returned),
          color: "#4CD964",
        },
      ],
      name: "",
    };
  }, [totalQueues, t]);
  return (
    <Card
      style={{
        height: source === "home3" ? 224 : "auto",
        backgroundImage: backgroundImagecard,
      }}
      styles={{ ...stylesCard }}
      title={
        <div className="flex items-center justify-between ">
          <span>
            {t("menu2.logs") + " / " + t("dashboard.queue")} &nbsp;
            <Select
              showSearch
              defaultValue={totalQueues[0]?.queue_num}
              popupMatchSelectWidth={false}
              placeholder=""
              optionFilterProp="children"
              filterOption={(input, option) =>
                (option?.label ?? "")
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
              options={allQueues}
              value={selectedQueue || totalQueues[0]?.queue_num}
              onChange={(value, values) => {
                dispatch(
                  setQueueInDashboard({
                    startDate: start,
                    endDate: end,
                    queue_num: value,
                  })
                );
                setSelectedQueue(value);
                // setSelectedQueue({
                //   ...values,
                // });
              }}
            />
          </span>
          <GoTo
            to={"2"}
            title={t("dashboard.queue")}
            navigate={navigate}
            t={t}
            user={user}
          />
        </div>
      }
      // styles={{ body: { height: "100%" } }}
      size=""
      className="full bg-white shadow-sm"
      //   extra={
      //     <Typography.Link
      //       onClick={() => navigate("/logs", { state: "groups_queues" })}
      //     >
      //       <ArrowRightOutlined />
      //     </Typography.Link>
      //   }
    >
      <Row>
        <Col
          span={source !== "home3" ? 14 : 24}
          style={{ paddingLeft: 0, paddingRight: 0 }}
        >
          <Card style={{ background: backgroundImagecard, border: 0 }}>
            <Card.Grid
              hoverable={false}
              style={{ ...gridStyle, borderStartStartRadius: 6 }}
            >
              <Statistic
                formatter={
                  typeof totalQueues[0]?.total_calls === "number"
                    ? formatter
                    : null
                }
                title={
                  <div className="flex w-max flex-nowrap">
                    {t("dashboard.receivedCalls", {
                      plural: totalQueues[0]?.total_calls > 1 ? "s" : "",
                      pluriel: totalQueues[0]?.total_calls > 1 ? "x" : "",
                    })}
                  </div>
                }
                value={totalQueues[0]?.total_calls}
                valueStyle={{
                  color: "#3b82f6",
                }}
                prefix={
                  <HiPhoneIncoming className="h-[22px] w-[22px] fill-blue-500 " />
                }
              />
            </Card.Grid>
            <Card.Grid hoverable={false} style={gridStyle}>
              <Statistic
                formatter={
                  typeof totalQueues[0]?.answered_calls === "number"
                    ? formatter
                    : null
                }
                title={
                  <div className="flex w-max flex-nowrap">
                    {t("dashboard.answered", {
                      plural: totalQueues[0]?.answered_calls > 1 ? "s" : "",
                    })}
                  </div>
                }
                value={totalQueues[0]?.answered_calls}
                valueStyle={{
                  color: "#16a34a",
                }}
                prefix={
                  <HiPhoneIncoming className="h-[22px] w-[22px] fill-green-600 " />
                }
              />
            </Card.Grid>
            <Card.Grid hoverable={false} style={{ ...gridStyle, width: "40%" }}>
              <Statistic
                // formatter={
                //   typeof missedTodayCall === "number" ? formatter : null
                // }
                className="moyen_ringing_calls"
                title={
                  <Tooltip title={t("voip.moyen_ringing_calls")}>
                    <div className="flex w-max flex-nowrap">
                      {t("voip.DMS")}
                    </div>
                  </Tooltip>
                }
                // value={totalQueues[0]?.moyen_ringing_calls}
                suffix={
                  totalQueues[0]?.moyen_ringing_calls
                    ? convertToHour(totalQueues[0]?.moyen_ringing_calls)
                    : "-"
                }
                value={null}
                // valueStyle={{
                //   color: "#cf1322",
                // }}

                prefix={
                  <svg
                    width="22"
                    height="22"
                    viewBox="0 0 24 24"
                    fill="#1d4ed830"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M21.9999 16.9201V19.9201C22.0011 20.1986 21.944 20.4743 21.8324 20.7294C21.7209 20.9846 21.5572 21.2137 21.352 21.402C21.1468 21.5902 20.9045 21.7336 20.6407 21.8228C20.3769 21.912 20.0973 21.9452 19.8199 21.9201C16.7428 21.5857 13.7869 20.5342 11.1899 18.8401C8.77376 17.3148 6.72527 15.2663 5.18993 12.8501C3.49991 10.2413 2.44818 7.27109 2.11993 4.1801C2.09494 3.90356 2.12781 3.62486 2.21643 3.36172C2.30506 3.09859 2.4475 2.85679 2.6347 2.65172C2.82189 2.44665 3.04974 2.28281 3.30372 2.17062C3.55771 2.05843 3.83227 2.00036 4.10993 2.0001H7.10993C7.59524 1.99532 8.06572 2.16718 8.43369 2.48363C8.80166 2.80008 9.04201 3.23954 9.10993 3.7201C9.23656 4.68016 9.47138 5.62282 9.80993 6.5301C9.94448 6.88802 9.9736 7.27701 9.89384 7.65098C9.81408 8.02494 9.6288 8.36821 9.35993 8.6401L8.08993 9.9101C9.51349 12.4136 11.5864 14.4865 14.0899 15.9101L15.3599 14.6401C15.6318 14.3712 15.9751 14.1859 16.3491 14.1062C16.723 14.0264 17.112 14.0556 17.4699 14.1901C18.3772 14.5286 19.3199 14.7635 20.2799 14.8901C20.7657 14.9586 21.2093 15.2033 21.5265 15.5776C21.8436 15.9519 22.0121 16.4297 21.9999 16.9201Z"
                      stroke="black"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <g clipPath="url(#clip0_757_5993)">
                      <path
                        d="M18 3V6L20 7M23 6C23 8.76142 20.7614 11 18 11C15.2386 11 13 8.76142 13 6C13 3.23858 15.2386 1 18 1C20.7614 1 23 3.23858 23 6Z"
                        stroke="black"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </g>
                    <defs>
                      <clipPath id="clip0_757_5993">
                        <rect
                          width="12"
                          height="12"
                          fill="white"
                          transform="translate(12)"
                        />
                      </clipPath>
                    </defs>
                  </svg>
                }
              />
            </Card.Grid>
            <Card.Grid
              hoverable={false}
              style={{ ...gridStyle, borderEndStartRadius: 6 }}
            >
              <Statistic
                formatter={
                  typeof totalQueues[0]?.no_answered_calls === "number"
                    ? formatter
                    : null
                }
                title={
                  <div className="flex w-max flex-nowrap">
                    {t("dashboard.missedCalls", {
                      plural: totalQueues[0]?.no_answered_calls > 1 ? "s" : "",
                      pluriel: totalQueues[0]?.no_answered_calls > 1 ? "x" : "",
                    })}{" "}
                  </div>
                }
                value={totalQueues[0]?.no_answered_calls}
                valueStyle={{
                  color: "#EF4444",
                }}
                prefix={
                  <MdPhoneMissed className="h-[22px] w-[22px] text-[#EF4444] " />
                }
              />
            </Card.Grid>

            <Card.Grid hoverable={false} style={gridStyle}>
              <Statistic
                formatter={
                  typeof totalQueues[0]?.no_answered_calls_not_returned ===
                  "number"
                    ? formatter
                    : null
                }
                title={
                  <Tooltip
                    title={t("dashboard.lostNotRecalled", {
                      plural:
                        totalQueues[0]?.no_answered_calls_not_returned > 1
                          ? "s"
                          : "",
                    })}
                  >
                    <div className="flex w-max flex-nowrap">
                      {t("dashboard.recalled", {
                        plural:
                          totalQueues[0]?.no_answered_calls_not_returned > 1
                            ? "s"
                            : "",
                      })}
                    </div>
                  </Tooltip>
                }
                value={totalQueues[0]?.no_answered_calls_not_returned}
                valueStyle={{
                  color: "#22c55e",
                }}
                prefix={
                  <svg
                    stroke="currentColor"
                    fill="currentColor"
                    strokeWidth="0"
                    viewBox="0 0 24 24"
                    className="h-[22px] w-[22px] text-[#114d1f]"
                    height="1em"
                    width="1em"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path fill="none" d="M0 0h24v24H0z"></path>
                    <path
                      fill="#22c55e"
                      d="M6.5 5.5L12 11l7-7-1-1-6 6-4.5-4.5H11V3H5v6h1.5V5.5zm17.21 11.17A16.971 16.971 0 0012 12C7.46 12 3.34 13.78.29 16.67c-.18.18-.29.43-.29.71s.11.53.29.71l2.48 2.48c.***********.71.29.27 0 .52-.11.7-.28.79-.74 1.69-1.36 2.66-1.85.33-.16.56-.5.56-.9v-3.1c1.45-.48 3-.73 4.6-.73 1.6 0 3.15.25 4.6.72v3.1c0 .39.23.74.56.9.98.49 1.87 1.12 2.67 1.85.18.18.43.28.7.28.28 0 .53-.11.71-.29l2.48-2.48c.18-.18.29-.43.29-.71s-.12-.52-.3-.7z"
                    ></path>
                  </svg>
                }
              />
            </Card.Grid>

            <Card.Grid hoverable={false} style={{ ...gridStyle, width: "40%" }}>
              <Statistic
                // formatter={
                //   typeof missedUnreturnedCalls === "number"
                //     ? formatter
                //     : null
                // }
                className="moyen_ringing_calls"
                title={
                  <Tooltip title={t("voip.moyen_treatement_calls")}>
                    <div className="flex w-max flex-nowrap">
                      {t("voip.DMC")}
                    </div>
                  </Tooltip>
                }
                suffix={
                  totalQueues[0]?.moyen_treatement_calls
                    ? convertToHour(totalQueues[0]?.moyen_treatement_calls)
                    : "-"
                }
                value={null}
                // value={selectedQueue?.moyen_treatement_calls}
                valueStyle={{
                  color: "#6b7280",
                }}
                prefix={<Speech size={22} className="fill-gray-500" />}
              />
            </Card.Grid>
          </Card>
        </Col>
        {source !== "home3" ? (
          <Col
            span={10}
            style={{
              paddingLeft: 0,
              borderLeft: "1px solid #f0f0f0",
              width: "100%",
              height: "100%",
              borderTopRightRadius: 6,
              borderBottomRightRadius: 6,
              background: "white",
            }}
          >
            {data?.data
              ?.map((el) => el.y)
              .reduce(
                (accumulator, currentValue) => accumulator + currentValue,
                0
              ) > 0 ? (
              <DonutChartWithDrillDownWithPercent
                key={`${start}-${end}-${selectedQueue}`}
                height={173}
                showInLegend={false}
                data={
                  !loading
                    ? {
                        series: [
                          {
                            name: t("dashboard.answered", {
                              plural:
                                totalQueues[0]?.answered_calls > 1 ? "s" : "",
                            }),
                            y: Number(totalQueues[0]?.answered_calls),
                            color: "#16a34a",
                            // "drilldown": "Default Pipeline"
                          },
                          {
                            name: t("dashboard.missedCalls", {
                              plural:
                                totalQueues[0]?.no_answered_calls > 1
                                  ? "s"
                                  : "",
                              pluriel:
                                totalQueues[0]?.no_answered_calls > 1
                                  ? "x"
                                  : "",
                            }),
                            y: Number(totalQueues[0]?.no_answered_calls),
                            color: "#EF4444",
                            drilldown: "missedCalls",
                          },
                        ],
                        drilldown: {
                          series: [
                            {
                              id: "missedCalls",
                              data: [
                                {
                                  name: t("dashboard.recalled", {
                                    plural:
                                      totalQueues[0]
                                        ?.no_answered_calls_not_returned > 1
                                        ? "s"
                                        : "",
                                  }),
                                  y: Number(
                                    totalQueues[0]
                                      ?.no_answered_calls_not_returned
                                  ),
                                  color: "#4CD964",
                                },
                                {
                                  name: t("dashboard.notRecalled", {
                                    plural:
                                      totalQueues[0]?.no_answered_calls -
                                        totalQueues[0]
                                          ?.no_answered_calls_not_returned >
                                      1
                                        ? "s"
                                        : "",
                                  }),
                                  y: Number(
                                    totalQueues[0]?.no_answered_calls -
                                      totalQueues[0]
                                        ?.no_answered_calls_not_returned
                                  ),
                                  color: "#EF4444",
                                },

                                // {
                                //     "name": "First Stage",
                                //     "y": 1,
                                //     "color": "#10b981"
                                // },
                                // {
                                //     "name": "S45",
                                //     "y": 1,
                                //     "color": "#374151"
                                // }
                              ],
                            },
                          ],
                        },
                        name: "",
                        parent_name: t("dashboard.receivedCalls", {
                          plural: totalQueues[0]?.total_calls > 1 ? "s" : "",
                          pluriel: totalQueues[0]?.total_calls > 1 ? "x" : "",
                        }),
                      }
                    : {
                        series: [],
                        name: "",
                        parent_name: "",
                        drilldown: { series: [] },
                      }
                }
              />
            ) : (
              // <PieChartWithLegend
              //   height={173}
              //   data={{
              //     data: !loading
              //       ? [
              //           {
              //             name: t("dashboard.answered", {
              //               plural:
              //                 totalQueues[0]?.answered_calls > 1 ? "s" : "",
              //             }),
              //             y: Number(totalQueues[0]?.answered_calls),
              //             color: "#16a34a",
              //           },
              //           {
              //             name: t("dashboard.missedCalls", {
              //               plural:
              //                 totalQueues[0]?.no_answered_calls > 1 ? "s" : "",
              //               pluriel:
              //                 totalQueues[0]?.no_answered_calls > 1 ? "x" : "",
              //             }),
              //             y: Number(totalQueues[0]?.no_answered_calls),
              //             color: "#EF4444",
              //           },
              //           {
              //             name: t("dashboard.lostNotRecalled", {
              //               plural:
              //                 totalQueues[0]?.no_answered_calls_not_returned > 1
              //                   ? "s"
              //                   : "",
              //             }),
              //             y: Number(
              //               totalQueues[0]?.no_answered_calls_not_returned
              //             ),
              //             color: "#4CD964",
              //           },
              //         ]
              //       : [],
              //     name: "",
              //   }}
              //   alignLegend={{
              //     align: "right",
              //     verticalAlign: "middle",
              //     layout: "vertical",
              //     enabled: false,
              //   }}
              //   exporting={false}
              // />
              <div className="h-[173px]">
                <EmptyPage />
              </div>
            )}
          </Col>
        ) : null}
      </Row>
    </Card>
  );
};

export default CardQueue2;
