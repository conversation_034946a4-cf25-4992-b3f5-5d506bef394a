import { WarningOutlined } from "@ant-design/icons";
import { deleteElements } from "../services/services";
import { toastNotification } from "../../../components/ToastNotification";
import { getFamilyNameById } from "../FamilyRouting";
import { Modal, Typography } from "antd";
import { getUser } from "new-redux/actions/voip.actions/getUser";

const handleDelete = async (
  t,
  familyId,
  elementID,
  label,
  selectedRowKeys,
  setSelectedRowKeys,
  setShouldFetchData,
  dispatch
) => {
  const confirm = Modal.confirm({
    icon: <WarningOutlined />,
    title: (
      <p className="truncate">{`${t("contacts.delete")} ${
        label
          ? label
          : `${selectedRowKeys?.length} ${getFamilyNameById(
              t,
              familyId,
              "plural"
            )}`
      }`}</p>
    ),
    content: t("contacts.deleteConfirmMsg"),
    okText: t("profile.confirm"),
    okType: "danger",
    cancelText: t("profile.cancel"),
    onCancel() {},
    onOk: () => {
      return new Promise(async (resolve, reject) => {
        try {
          const formData = new FormData();
          const elementsToDelete = elementID ? [elementID] : selectedRowKeys;
          elementsToDelete?.forEach((e) => formData.append("ids[]", e));
          formData.append("family_id", familyId);
          const { status, data } = await deleteElements(formData);
          if (status === 200) {
            setShouldFetchData(true);
            !elementID && setSelectedRowKeys([]);
            familyId === 4 && dispatch(getUser());
            toastNotification(
              "success",
              <>
                {elementsToDelete?.length > 1 ? (
                  `${elementsToDelete?.length} ${getFamilyNameById(
                    t,
                    familyId,
                    "plural"
                  )} ${t("contacts.successDelete")}`
                ) : (
                  <>
                    {`${t("contacts.the")} ${getFamilyNameById(t, familyId)} `}
                    <Typography.Text strong>{label}</Typography.Text>{" "}
                    {t("contacts.successDelete")}
                  </>
                )}
              </>,
              "topRight",
              5
            );
            resolve();
          }
        } catch (err) {
          const status = err?.response?.status;
          const errMsg = Object.keys(err?.response?.data?.message);
          switch (status) {
            case 401:
              confirm.destroy();
              reject();
              return;
            case 403:
              toastNotification(
                "error",
                t("contacts.notPermissionToDelete"),
                "topRight",
                7
              );
              confirm.destroy();
              reject();
              return;
            case 409:
              toastNotification(
                "error",
                <div>
                  {t("contacts.errorDelete")}{" "}
                  <strong>
                    {label
                      ? label
                      : `${t("contacts.those")} ${getFamilyNameById(
                          t,
                          familyId,
                          "plural"
                        )}`}
                  </strong>{" "}
                  {label ? `${t("contacts.is")} ` : `${t("contacts.are")} `}{" "}
                  {t("contacts.alreadyUsed")}{" "}
                  {errMsg?.map((e, i) => (
                    <strong>
                      {e}
                      {errMsg?.length !== i + 1 && ","}{" "}
                    </strong>
                  ))}
                  !
                </div>,
                "topRight",
                7
              );
              confirm.destroy();
              reject();
              return;
            default:
              toastNotification(
                "error",
                t("toasts.somethingWrong"),
                "topRight"
              );
              confirm.destroy();
              reject();
              return;
          }
        }
      });
    },
  });
  return confirm;
};

export default handleDelete;
