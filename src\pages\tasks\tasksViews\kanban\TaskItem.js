/**
 * @name TaskItem
 *
 * @description `TaskItem` component is responsible for displaying the activity card on kanban.
 *
 * @returns {JSX.Element} Activity card.
 */

import { memo, useMemo, forwardRef } from "react";
import {
  CalendarOutlined,
  CarryOutOutlined,
  EditOutlined,
  MessageOutlined,
  EyeOutlined,
  PaperClipOutlined,
  UserOutlined,
  <PERSON>Outlined,
  MergeOutlined,
} from "@ant-design/icons";
import { Badge, Button, Card, Tag, Tooltip, Typography } from "antd";
import { TbFlag3, TbFlag3Filled } from "react-icons/tb";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";

import ChoiceIcons from "../../../components/ChoiceIcons";
import AvatarGroup from "../../../../components/AvatarGroup";
import { setOpenTaskDrawer } from "../../../../new-redux/actions/tasks.actions/handleTaskDrawer";
import { setOpenTaskRoomDrawer } from "../../../../new-redux/actions/tasks.actions/handleTaskRoomDrawer";

import { getName } from "../../../layouts/chat/utils/ConversationUtils";
import { URL_ENV } from "index";
import { AvatarChat } from "components/Chat";
import {
  displayPriorityColor,
  handlePriorityLabelOnHover,
} from "pages/tasks/helpers/handlePriorities";
import getContactDataAndDispatch from "pages/clients&users/helpers/getContactDataAndDispatch";
import DropdownTask from "components/DropdownTask";
import { EXTENSIONS_ARRAY } from "pages/tasks/helpers/calculateSum";
import { closeDrawerChat } from "new-redux/actions/voip.actions/handleDrawerChat";
import ActionsComponent from "pages/tasks/ActionsComponent";
import { setChatSelectedConversation } from "new-redux/actions/chat.actions";
import { formatDateForDisplay } from "pages/tasks/helpers/formatDateToDisplay";
import { MdVideoCameraFront } from "react-icons/md";
import { familyIcons } from "pages/components/DetailsProfile/ViewSphere2";
import SubtasksIndicator from "pages/tasks/helpers/SubtasksIndicator";

//Activity description.
const DescriptionContent = memo(
  ({ content, user, t, source, openModuleElement, id }) => {
    // Helper function for formatting date with Tooltip
    const renderDateTooltip = (date, time, isOverdue) => {
      return date && time ? (
        <Tooltip
          title={
            <div className="flex flex-col items-center justify-center">
              <p>{`${date} ${time}`}</p>
              {isOverdue && <p>{t("tasks.overdueTaskInfo")}</p>}
            </div>
          }
          placement="bottom"
          overlayStyle={{ zIndex: "99999" }}
        >
          <Typography.Text type={isOverdue && "danger"}>
            {formatDateForDisplay(
              `${date} ${time}`,
              `${user?.location?.date_format} ${user?.location?.time_format}`,
              user,
              t
            )}
          </Typography.Text>
        </Tooltip>
      ) : null;
    };

    // Helper function for rendering section with AvatarGroup
    const renderAvatarGroup = (users, label, role) => {
      return users && users.length > 0 ? (
        <section className="flex flex-row items-center pt-1">
          <UserOutlined style={{ marginRight: "6px" }} />
          <AvatarGroup
            source="taskTable"
            usersArray={users}
            uncheckUser={() => {}}
            disableDelete={true}
            roleInActivity={role}
          />
        </section>
      ) : null;
    };

    return (
      <div className="flex h-full flex-col justify-evenly">
        {/* MergeOutlined and Stage */}
        {content?.stage_id && source === "calendar" && (
          <section className="flex flex-row items-center pt-1">
            <MergeOutlined style={{ transform: "rotate(45deg)" }} />
            <Tag color="cyan" className="mt-1 w-auto truncate" bordered={false}>
              {`${content?.pipeline_label}/${content?.stage_label}`}
            </Tag>
          </section>
        )}
        {/* Start Date */}
        <section className="pt-1">
          <CalendarOutlined />{" "}
          {renderDateTooltip(content?.start_date, content?.start_time, false)}
        </section>
        {/* End Date */}
        <section className="pt-1">
          <CarryOutOutlined
            style={{ color: content?.is_overdue ? "red" : "inherit" }}
          />{" "}
          {renderDateTooltip(
            content?.end_date,
            content?.end_time,
            content?.is_overdue
          )}
        </section>
        {/* Guests */}
        {renderAvatarGroup(content?.guests, "guests", "participants")}
        {/* Followers */}
        {renderAvatarGroup(content?.followers, "followers", "followers")}
        {/* Family and Element Labels */}
        {content?.family_label && content?.element_label && (
          <section className="flex flex-row items-center pt-1">
            <LinkOutlined />
            <Tag
              bordered={false}
              color="magenta"
              className="max-w-auto ml-1 flex h-[25px] items-center truncate"
              icon={
                <span className="relative top-[1px] pr-1">
                  {
                    familyIcons(t).find((el) => el.key === content?.family_id)
                      ?.icon
                  }
                </span>
              }
              onClick={() => {
                if (source !== "calendar") {
                  openModuleElement(
                    {
                      familyId: content?.family_id,
                      elementLabel: content?.element_label,
                      id: content?.element_id,
                      familyLabel: content?.family_label,
                    },
                    { ...content, key: id }
                  );
                }
              }}
            >
              <span className="flex w-[130px] items-center truncate">
                <span className="truncate">{`${content?.family_label}/${content?.element_label}`}</span>
              </span>
            </Tag>
          </section>
        )}
      </div>
    );
  }
);

const TaskItem = forwardRef(function TaskItem({
  id,
  setTaskToUpdate,
  content,
  deleteTask,
  tasksTypes,
  setOpenActivity360,
  setActivityLabel,
  source = null,
  setOpenElementDetails,
  setElementDetails,
  stageColor,
  setRoomActivityId,
}, ref) {
  const { user } = useSelector((state) => state?.user);
  const dispatch = useDispatch();
  const [t] = useTranslation("common");

  // Handle open associated element details (sphere360°).
  const openModuleElement = (props, record) => {
    getContactDataAndDispatch(
      props?.familyId,
      props?.elementLabel,
      { key: props?.id },
      record,
      dispatch,
      null,
      () => {}
    );
    setElementDetails((prev) => ({
      ...prev,
      id: props?.id,
      module: props?.familyId,
    }));
    setOpenElementDetails(true);
  };

  // Handle open drawer.
  const handleOpenUpdateDrawer = () => {
    setTaskToUpdate(id);
    setActivityLabel(content?.label);
    dispatch(setOpenTaskDrawer(true));
  };

  // Handle open overview modal.
  const handleOpenOverview = () => {
    dispatch(
      setChatSelectedConversation({
        selectedConversation: null,
      })
    );
    dispatch(closeDrawerChat());
    setTaskToUpdate(id);
    setOpenActivity360(true);
  };

  // Handle open chat room.
  const handleOpenChatRoom = () => {
    dispatch(setOpenTaskRoomDrawer(true));
    setRoomActivityId(id);
  };

  // Card actions array.
  const cardActions = useMemo(() => {
    //Validations
    const canCreateRoom = content?.can_create_room === 0;
    const canUpdateTask = content?.can_update_task === 0;
    const hasAttachments = content?.upload > 0;

    return [
      <Tooltip title={t("wiki.Edit")}>
        <Button
          type="text"
          shape="circle"
          onClick={handleOpenUpdateDrawer}
          icon={<EditOutlined key="edit" />}
          disabled={canUpdateTask}
        />
      </Tooltip>,
      <Tooltip title={t("tasks.view360")}>
        <Button
          type="text"
          shape="circle"
          icon={<EyeOutlined key="icon" className="mt-0.5 text-base" />}
          onClick={handleOpenOverview}
        />
      </Tooltip>,
      <Tooltip
        title={
          content?.room_id !== 0
            ? t("tasks.openChatRoom")
            : t("tasks.createChatRoom")
        }
        overlayStyle={{
          display: canCreateRoom && "none",
        }}
      >
        <Button
          type="text"
          shape="circle"
          onClick={handleOpenChatRoom}
          disabled={canCreateRoom}
        >
          <MessageOutlined key="message" />
        </Button>
      </Tooltip>,
      content?.todoList ? (
        <Button type="text" shape="circle" onClick={handleOpenOverview}>
          <SubtasksIndicator
            subtasksNumber={content?.todoList}
            position="relative"
          />
        </Button>
      ) : null,
      hasAttachments ? (
        <Tooltip
          title={
            !hasAttachments
              ? "No Attachments"
              : `${content?.upload} attachments`
          }
        >
          <Badge
            offset={[-5, 8]}
            count={content?.upload}
            color="#faad14"
            size="small"
          >
            <Button
              type="text"
              shape="circle"
              icon={<PaperClipOutlined />}
              disabled={!hasAttachments}
              onClick={handleOpenOverview}
            />
          </Badge>
        </Tooltip>
      ) : null,
      <DropdownTask
        source="activity"
        from="Kanban"
        props={{
          can_update_task: content?.can_update_task,
          tasks_type_id: content?.tasks_type_id,
          can_create_room: content?.can_create_room,
          id: content?.id,
          label: content?.label,
          location: content?.location,
        }}
        handleDelete={deleteTask}
        editTask={handleOpenUpdateDrawer}
        handleOpenActivityIn360={handleOpenOverview}
      />,
    ];
  }, [
    content?.can_create_room,
    content?.can_update_task,
    content?.description,
    content?.id,
    content?.label,
    content?.location,
    content?.note,
    content?.room_id,
    content?.tasks_type_id,
    content?.upload,
    content?.todoList,
    t,
  ]);

  return (
    <Card
      ref={ref}
      className="kanban-task"
      key={id}
      hoverable={source === null}
      bordered={source !== "calendar"}
      actions={source === null && cardActions?.filter((el) => el !== null)}
      style={{
        willChange: "unset",
        width: "100%",
        borderTopColor: stageColor ? stageColor : "#bfbfbf",
        borderTopWidth: "2px",
        cursor: source === "calendar" ? "default" : "grab",
      }}
    >
      <Card.Meta
        avatar={
          <Tooltip
            title={getName(content?.owner_id?.label, "name")}
            overlayStyle={{ zIndex: "99999" }}
          >
            <ActionsComponent elementValue={content?.owner_id}>
              <AvatarChat
                className={"mx-1.5 flex items-center justify-center"}
                fontSize={"0.875rem"}
                height={"32px"}
                width={"32px"}
                url={`${
                  URL_ENV?.REACT_APP_BASE_URL +
                  URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                }${content?.owner_id?.avatar}`}
                hasImage={EXTENSIONS_ARRAY?.includes(
                  content?.owner_id?.avatar?.split(".")?.pop()
                )}
                name={getName(content?.owner_id?.label, "avatar")}
                type="user"
              />
            </ActionsComponent>
          </Tooltip>
        }
        title={
          <div className="flex w-full flex-row items-center">
            <div
              style={{
                marginRight: "4px",
                marginTop: "2px",
              }}
            >
              <ChoiceIcons
                fontSize="18px"
                icon={
                  tasksTypes.find(
                    (el) =>
                      Number(el?.id) === Number(content?.tasks_type_id) ||
                      Number(el?.id) === Number(content?.icon)
                  )?.icons
                }
              />
            </div>
            <Typography.Text
              ellipsis={{
                tooltip: {
                  overlayStyle: {
                    zIndex: "99999",
                  },
                },
              }}
              onClick={() => {
                if (source !== "calendar") {
                  handleOpenOverview();
                }
              }}
              className={`relative justify-start overflow-hidden text-ellipsis text-[16px] text-xs text-[${
                source !== "calendar" ? "#1677ff" : "##000000E0"
              }] hover:cursor-${
                source !== "calendar" ? "pointer" : "default"
              } hover:text-[${
                source !== "calendar" ? "#69b1ff" : "##000000E0"
              }]`}
            >
              {content?.label}
            </Typography.Text>
            {content?.visio_in_progress && content?.visio_in_progress === 1 ? (
              <Tooltip
                title={t("tasks.visioInProgress")}
                overlayStyle={{ zIndex: "99999" }}
              >
                <MdVideoCameraFront className="ml-2 animate-pulse justify-start text-sm text-red-500" />
              </Tooltip>
            ) : null}
            <div
              className="ml-auto flex items-center"
              onClick={() => {
                if (source === null) {
                  handleOpenOverview();
                }
              }}
            >
              {content?.priority ? (
                <Tooltip
                  title={handlePriorityLabelOnHover(content?.priority)}
                  overlayStyle={{ zIndex: "99999" }}
                >
                  <TbFlag3Filled
                    style={{
                      fontSize: "18px",
                      cursor:
                        content?.can_update_task === 0
                          ? "not-allowed"
                          : "pointer",
                      color: displayPriorityColor(content?.priority),
                    }}
                  />
                </Tooltip>
              ) : (
                <Tooltip
                  title={
                    source === null ? t("tasks.setPriority") : "No Priority"
                  }
                  overlayStyle={{ zIndex: "99999" }}
                >
                  <TbFlag3
                    style={{
                      fontSize: "18px",
                      cursor:
                        content?.can_update_task === 0
                          ? "not-allowed"
                          : "pointer",
                      color: "#bfbfbf",
                    }}
                  />
                </Tooltip>
              )}
            </div>
          </div>
        }
        description={
          <DescriptionContent
            content={content}
            user={user}
            t={t}
            source={source}
            openModuleElement={openModuleElement}
            id={id}
          />
        }
      />
    </Card>
  );
});

TaskItem.displayName = 'TaskItem';

export default memo(TaskItem);
