import { useEffect, useState, lazy } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { setCreateForm } from "../../new-redux/actions/form.actions/form";
//
import {
  Drawer,
  Form,
  Space,
  Button,
  Dropdown,
  Input,
  Tooltip,
  InputNumber,
  Row,
  Col,
  Card,
  message,
  Select,
} from "antd";
import { CloseOutlined, UpOutlined } from "@ant-design/icons";
import { FiInfo, FiMail, FiSmartphone, FiCopy, FiKey } from "react-icons/fi";
//
import { generateAxios } from "../../services/axiosInstance";
import { toastNotification } from "../../components/ToastNotification";
import { URL_ENV } from "index";

const UserDetails = lazy(() => import("./UserDetails"));

const CreateUser = ({ open, change, setChange }) => {
  //
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  //
  const [userRoles, setUserRoles] = useState([]);
  const [loadingSubmit, setLoadingSubmit] = useState(false);
  const [submitAction, setSubmitAction] = useState("");
  const [openSecondDrawer, setOpenSecondDrawer] = useState(false);
  //
  //   console.log(userRoles)
  //
  const getRoles = async () => {
    try {
      const roles = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).get(`roles`);
      if (roles.status === 200) {
        const result = [];
        roles?.data?.data?.forEach((role) => {
          result.push({
            value: role.id,
            label: role.label,
          });
        });
        setUserRoles(result);
      }
    } catch (err) {
      setLoadingSubmit(false);
      toastNotification("error", "Cannot get user roles", "topRight");
      throw new Error(err?.message ? err.message : err);
    }
  };
  useEffect(() => {
    if (open) getRoles();
  }, [open]);
  //
  const onFinish = async (values) => {
    //console.log(values);
    try {
      setLoadingSubmit(true);
      const formData = new FormData();
      formData.append("family_id", 4);
      formData.append("import_type_id", 3);
      let index = 0;
      for (let key in values) {
        // if (!values["confirmPassword"] || !values["userRoles"]) {
        switch (key) {
          case "username":
            formData.append(`values[${index}][field_id]`, 291);
            formData.append(`values[${index}][enteredValue]`, values[key]);
            index++;
            break;
          case "email":
            formData.append(`values[${index}][field_id]`, 294);
            formData.append(`values[${index}][enteredValue]`, values[key]);
            index++;
            break;
          case "phoneNumber":
            formData.append(`values[${index}][field_id]`, 377);
            formData.append(`values[${index}][enteredValue]`, values[key]);
            index++;
            break;
          case "password":
            formData.append(`values[${index}][field_id]`, 292);
            formData.append(`values[${index}][enteredValue]`, values[key]);
            index++;
            break;
          default:
            break;
        }
      }
      const create = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).post("/element-lists", formData);
      if (create?.status === 200) {
        setLoadingSubmit(false);
        toastNotification("success", `User Created Successfully`, "topRight");
        switch (submitAction) {
          case "create":
            setChange(change + 1);
            form.resetFields();
            dispatch(setCreateForm(false));
            break;
          case "add_another":
            setChange(change + 1);
            form.resetFields();
            break;
          case "go_to_log":
            // message.open({
            //   type: "warning",
            //   content: "This Action Is Under Dev!",
            // });
            setChange(change + 1);
            setOpenSecondDrawer(true);
            break;
        }
      }
    } catch (err) {
      setLoadingSubmit(false);
      toastNotification(
        "error",
        "Something Went Wrong, Please Try Again",
        "topRight"
      );
      throw new Error(err?.message ? err.message : err);
    }
  };
  //
  const onFinishFailed = (errorInfo) => {
    console.log(errorInfo);
  };
  //
  const MenuProps = {
    items: [
      { label: "Create & Add Another", key: "add_another" },
      { label: "Create & Go To Details", key: "go_to_log" },
    ],
    onClick: (e) => {
      setSubmitAction(e.key);
      form.submit();
    },
  };
  const FooterDrawer = (
    <Space direction="horizontal" size="middle" style={{ padding: "0.250rem" }}>
      <Button
        onClick={() => {
          form.resetFields();
          dispatch(setCreateForm(false));
        }}>
        Cancel
      </Button>
      <Dropdown.Button
        type="primary"
        loading={loadingSubmit}
        trigger={["click"]}
        placement="topLeft"
        icon={<UpOutlined />}
        menu={MenuProps}
        onClick={() => {
          setSubmitAction("create");
          form.submit();
        }}>
        Create
      </Dropdown.Button>
    </Space>
  );
  const ExtraDrawer = (
    <Space>
      <Button type="primary" onClick={() => navigate(`/settings/fields/User`)}>
        Go To User Fields
      </Button>
    </Space>
  );
  //
  const handleGeneratePassword = () => {
    const password = generatePassword();
    form.setFieldsValue({
      password: password,
      confirmPassword: password,
    });
  };
  //
  return (
    <Drawer
      title={`Create User`}
      placement="right"
      width={openSecondDrawer ? window.innerWidth / 1.5 : window.innerWidth / 3}
      open={open}
      closeIcon={
        <CloseOutlined
          onClick={() => {
            dispatch(setCreateForm(false));
          }}
        />
      }
      extra={ExtraDrawer}
      footer={FooterDrawer}>
      <Form
        form={form}
        layout={"vertical"}
        onFinish={onFinish}
        onFinishFailed={onFinishFailed}
        autoComplete="off"
        scrollToFirstError
        // onFieldsChange={(changedFields, allFields) => {
        //   console.log(changedFields);
        // }}
      >
        <Form.Item
          label="Username"
          name="username"
          rules={[
            {
              required: true,
              message: `The 'Email' field is required`,
            },
          ]}>
          <Input
            suffix={
              <Tooltip
                title="This field is a unique value and cannot be changed later"
                placement="topLeft">
                <FiInfo className="h-4 w-4 text-slate-400" />
              </Tooltip>
            }
          />
        </Form.Item>
        <Form.Item
          label="Email"
          name="email"
          rules={[
            {
              required: true,
              message: `The 'Email' field is required`,
            },
            {
              pattern: emailRegex,
              message: `Please enter a valid 'Email'`,
            },
          ]}>
          <Input
            suffix={
              <Tooltip
                title="This field is a unique value and cannot be changed later!"
                placement="topLeft">
                <FiInfo className="h-4 w-4 text-slate-400" />
              </Tooltip>
            }
            prefix={<FiMail className="mr-1 h-4 w-4 text-slate-400" />}
          />
        </Form.Item>
        <Form.Item
          label="Phone Number"
          name="phoneNumber"
          rules={[
            {
              required: true,
              message: `The 'Phone Number' field is required`,
            },
          ]}>
          <InputNumber
            style={{ width: "100%" }}
            controls={false}
            step={1}
            formatter={(value) => value.replace(/\D/g, "")}
            parser={(value) => value.replace(/\D/g, "")}
            prefix={<FiSmartphone className="mr-1 h-4 w-4 text-slate-400" />}
          />
        </Form.Item>
        <Card className="mb-4">
          <Form.Item
            label="Password"
            name="password"
            rules={[
              {
                required: true,
                message: `The 'Password' field is required`,
              },
              {
                pattern: passwordRegex,
                message: `Minimum ten (10) characters, at least one uppercase letter, one lowercase letter and one number!`,
              },
            ]}>
            <Input.Password />
          </Form.Item>
          <Form.Item
            label="Confirm Password"
            name="confirmPassword"
            dependencies={["password"]}
            hasFeedback
            rules={[
              {
                required: true,
                message: `The 'Password' field is required`,
              },
              {
                pattern: passwordRegex,
                message: `Minimum ten (10) characters, at least one uppercase letter, one lowercase letter and one number!`,
              },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue("password") === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(
                    new Error(
                      "The two passwords that you entered do not match!"
                    )
                  );
                },
              }),
            ]}>
            <Input.Password />
          </Form.Item>
          <Space>
            <Button
              onClick={handleGeneratePassword}
              type="primary"
              icon={<FiKey className="mr-1" />}>
              Generate Password
            </Button>
            <Button
              onClick={() => {
                navigator.clipboard.writeText(form.getFieldValue()["password"]);
                message.open({
                  type: "success",
                  content: "Password copied",
                });
              }}
              icon={<FiCopy className="mr-1" />}>
              Copy Password
            </Button>
          </Space>
        </Card>
        {/* {userRoles.length ? (
          <Form.Item
            label="Select Role"
            name="userRoles"
            rules={[
              {
                required: true,
                message: `The 'Select Role' field is required!`,
              },
            ]}
          >
            <Select
              style={{
                width: "100%",
              }}
              showSearch
              optionFilterProp="children"
              filterOption={(input, option) =>
                (option?.label ?? "")
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
              allowClear
              options={userRoles}
            />
          </Form.Item>
        ) : (
          ""
        )} */}
      </Form>
      <Drawer
        title={`User Information`}
        extra={
          <span className="flex text-lg font-semibold text-red-500 decoration-double">
            It's Just a View With Static Data
          </span>
        }
        placement="right"
        width={window.innerWidth / 2}
        open={openSecondDrawer}
        closeIcon={
          <CloseOutlined
            onClick={() => {
              setOpenSecondDrawer(false);
            }}
          />
        }>
        <UserDetails />
      </Drawer>
    </Drawer>
  );
};

export default CreateUser;
//
const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
const passwordRegex = /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[a-zA-Z]).{10,}$/;
const numberRegex = /^\d+$/;
//
const generatePassword = () => {
  let password = "";
  const characters =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let numUpper = 0;
  let numLower = 0;
  let numNum = 0;

  while (
    password.length < 10 ||
    numUpper === 0 ||
    numLower === 0 ||
    numNum === 0
  ) {
    password = "";
    numUpper = 0;
    numLower = 0;
    numNum = 0;
    for (let i = 0; i < 10; i++) {
      const randomChar = characters.charAt(
        Math.floor(Math.random() * characters.length)
      );
      if (randomChar >= "A" && randomChar <= "Z") {
        numUpper++;
      } else if (randomChar >= "a" && randomChar <= "z") {
        numLower++;
      } else {
        numNum++;
      }
      password += randomChar;
    }
  }
  return password;
};
