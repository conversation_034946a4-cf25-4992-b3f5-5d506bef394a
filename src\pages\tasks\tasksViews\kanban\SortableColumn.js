/**
 * @name SortableColumn
 *
 * @description `SortableColumn` is the single stage in kanban.
 *
 * @param {Number} id The id of stage.
 * @param {Number} index The index of clicked stage in the array.
 * @param {String} placement The placement of the tooltip popover.
 * @param {String} source The source where the component is being called (Task/"").
 * @param {Boolean} canDeleteStage Whether the stage is system or not.
 * @param {Object} children The content to be rendered in side the jsx.
 * @param {Object} elementValue The object of the child element, containing id, uuid, label, extension, ...
 * @param {Function} setOpenDrawerCreate Sets 'openDrawerCreate' state.
 * @param {Function} setSelectedStageId Sets 'selectedStageId' state.
 * @param {Function} t Translation function.
 *
 * @returns {JSX.Element} Kanban stage.
 */

// React and 3rd party libraries imports
import { memo, useCallback } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
  DeleteOutlined,
  MoreOutlined,
  PlusOutlined,
  RestOutlined,
  SettingOutlined,
} from "@ant-design/icons";
import { Draggable } from "react-beautiful-dnd";
import { useDispatch } from "react-redux";
import { Badge, Button, Card, Col, Dropdown, Tooltip, Progress } from "antd";
import { useTranslation } from "react-i18next";

// Common imports
import { handleTranslateModuleLabel } from "pages/tasks/helpers/handleTranslateModuleLabel";
import { setOpenTaskDrawer } from "new-redux/actions/tasks.actions/handleTaskDrawer";
import Confirm from "components/GenericModal";
import { isGuestConnected } from "utils/role";

const SortableColumn = memo(function SortableColumn({
  children,
  index,
  source,
  setOpenDrawerCreate,
  setSelectedStageId,
  deleteStage,
  columnData,
  isScrolling,
}) {
  const [t] = useTranslation("common");
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const location = useLocation();

  //Destructuring assignment
  const {
    stage_id,
    stage_name,
    elements,
    stage_color,
    stage_percent,
    stage_system,
    total,
  } = columnData;

  // Dropdown items
  const items = [
    {
      key: 1,
      label:
        source === "Task"
          ? t("tasks.addQuickTask")
          : t("contacts.createNewX", {
              x: handleTranslateModuleLabel(location.pathname?.split("/")[1]),
            }),
      icon: <PlusOutlined />,
    },
    ...(source === "Task"
      ? [
          {
            key: 2,
            label: t("tasks.goToSettings"),
            icon: <SettingOutlined />,
          },
        ]
      : []),
    ...(stage_system
      ? []
      : [
          {
            key: 4,
            danger: true,
            label: (
              <div
                dangerouslySetInnerHTML={{
                  __html: t("tasks.deleteStage", { label: stage_name }),
                }}
              />
            ),
            icon: <DeleteOutlined />,
          },
        ]),
  ];

  // Handle click on dropdown items
  const handleClickOnDropdownItem = useCallback(
    (e) => {
      const { key } = e || {};
      switch (key) {
        case "1":
          setSelectedStageId(stage_id);
          if (source === "Task") {
            dispatch(setOpenTaskDrawer(true));
          } else {
            setOpenDrawerCreate(true);
          }
          break;
        case "2":
          navigate("/settings/activity/pipelines");
          break;
        case "4":
          Confirm(
            <p
              dangerouslySetInnerHTML={{
                __html: t("tasks.deleteStage", { label: stage_name }),
              }}
            />,
            "Confirm",
            <RestOutlined style={{ color: "red" }} />,
            () => deleteStage(stage_id),
            true
          );
          break;
        default:
          break;
      }
    },
    [dispatch, source, stage_id]
  );

  return (
    <Draggable
      draggableId={stage_id?.toString()}
      index={index}
      key={stage_id}
      isDragDisabled={isGuestConnected() || isScrolling}
   
    >
      {(provided, snapshot) => (
        <Col
          span={5}
          ref={provided?.innerRef}
          {...provided?.draggableProps}
          {...provided.dragHandleProps}
        >
          {/* Stage card header */}
          <Card
            className="kanban-header-card"
            title={
              <div className="flex flex-col">
                {stage_name}
                {stage_percent && stage_percent !== null ? (
                  <Progress
                    percent={stage_percent}
                    format={() => `${stage_percent}%`}
                    size="small"
                    status="normal"
                    strokeColor={stage_color ?? "#bfbfbf"}
                    style={{ width: "40%" }}
                  />
                ) : (
                  <div />
                )}
              </div>
            }
            extra={
              <>
                {elements?.length > 0 && (
                  <Badge
                    style={{
                      backgroundColor: stage_color ?? "#8c8c8c",
                      color: "#fff",
                    }}
                    count={elements && total}
                    overflowCount={999}
                  />
                )}
                {!isGuestConnected() && (
                  <Tooltip title={t("tasks.moreInfo")}>
                    <Dropdown
                      menu={{
                        items,
                        onClick: handleClickOnDropdownItem,
                      }}
                      trigger={["click"]}
                    >
                      <Button
                        type="text"
                        size="small"
                        shape="circle"
                        className="ml-2 text-black"
                        icon={<MoreOutlined />}
                      />
                    </Dropdown>
                  </Tooltip>
                )}
              </>
            }
            bordered
            bodyStyle={{ display: "none" }}
        
            {...provided?.dragHandleProps}
          />
          {children}
        </Col>
      )}
    </Draggable>
  );
});

export default SortableColumn;
