import { SET_DRIVE_PARENT_ITEMS, SET_DRIVE_SELECTED_FOLDER, SET_DRIVE_BREADCRUMB, SET_DRIVE_TREE_DATA, UPDATE_DRIVE_TREE_NODE, SET_DRIVE_SEARCH, SET_DRIVE_SEARCH_INPUT, CLEAR_DRIVE_SEARCH } from "../constants";

const initialState = {
  parentItem: "",
  selectedFolder: "1",
  breadcrumb: [{ id: "", name: "My Drive", path: "" }],
  treeData: [],
  search: "",
  searchInput: "",
};

const drive = (state = initialState, action) => {
  const { type, payload } = action;
  switch (type) {
    case SET_DRIVE_PARENT_ITEMS:
      return {
        ...state,
        parentItem: payload,
      };
    
    case SET_DRIVE_SELECTED_FOLDER:
      return {
        ...state,
        selectedFolder: payload,
      };
    
    case SET_DRIVE_BREADCRUMB:
      return {
        ...state,
        breadcrumb: payload,
      };
    
    case SET_DRIVE_TREE_DATA:
      return {
        ...state,
        treeData: payload,
      };
    
    case UPDATE_DRIVE_TREE_NODE:
      const updateTreeData = (nodes, targetKey, newChildren) => {
        return nodes.map((node) => {
          if (node.key === targetKey && !!node.children) {
            return {
              ...node,
              children: newChildren,
              isLeaf: newChildren.length === 0,
            };
          } else if (!!node.children) {
            return {
              ...node,
              children: updateTreeData(node.children, targetKey, newChildren),
            };
          }
          return node;
        });
      };
      
      return {
        ...state,
        treeData: updateTreeData(state.treeData, payload.key, payload.children),
      };

    case SET_DRIVE_SEARCH:
      return {
        ...state,
        search: payload,
      };

    case SET_DRIVE_SEARCH_INPUT:
      return {
        ...state,
        searchInput: payload,
      };

    case CLEAR_DRIVE_SEARCH:
      return {
        ...state,
        search: "",
        searchInput: "",
      };

    default:
      return state;
  }
};

export default drive;
