import { GET_SEARCH_FIELDS_SUCCESS, GET_FIELDS_ERROR, IS_LOADING } from "../../constants";
import MainService from "../../../services/main.service";

export const getFieldsWithSearch = (familyId, query) => async (dispatch) => {
  try {
    dispatch({ type: IS_LOADING });
    const response = await MainService.getSearchFields(familyId, query);
    dispatch({
      type: GET_SEARCH_FIELDS_SUCCESS,
      payload: response?.data,
    });
    console.log("res search", response);
  } catch (error) {
    if (error?.status !== 401) {
      dispatch({
        type: GET_FIELDS_ERROR,
        payload: error,
      });
    }
  }
};
