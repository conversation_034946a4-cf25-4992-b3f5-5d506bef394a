/**
 * @name FilterOptions
 *
 * @description `FilterOptions` component is responsible for rendering the form of the advanced filter.
 *               Allowing the user to apply filters based on different criteria.
 *
 * @param {Function} setTasksFilters
 * @param {Object} filtersForm
 * @param {Function} setAppliedFilters
 * @param {Function} setPageNumber
 * @param {Array} columnsBackup
 * @param {String} filterTable
 * @param {Function} setFilterTable
 * @param {Function} setColumns
 * @param {Function} setColumnsBackup
 * @param {Array} appliedFilters
 * @param {Array} tasksTypes
 * @param {String} switchViews
 * @param {Function} setDateFilter
 * @param {Function} setSelectedFamily
 * @param {Function} handleResetRolesSelect
 * @param {Function} setSelectedRoles
 * @param {Boolean} openFilterMenu
 * @param {Function} setOpenFilterMenu
 * @param {Function} clearFilters
 * @param {Number} totalFiltered
 * @param {Function} setTotalFiltered
 *
 * @returns {JSX.Element} Advanced filter form.
 */

import { memo, useEffect, useCallback, useMemo, useState } from "react";
import {
  Form,
  Button,
  Select,
  DatePicker,
  Checkbox,
  Radio,
  Drawer,
  Typography,
  Col,
  Row,
  Tooltip,
  Alert,
  Cascader,
  Spin,
  Tag,
  Space,
} from "antd";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import dayjs from "dayjs";
import {
  CloseCircleOutlined,
  CloseOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons";

import ChoiceIcons from "pages/components/ChoiceIcons";
import { prioritiesListInFilter } from "./helpers/handlePriorities";
import { getValueByKey } from "./helpers/calculateSum";
import { getRelevantFiltersCount, rolesList } from "./helpers/filterCount";
import MainService from "services/main.service";
import form from "new-redux/reducers/formReducer";
const FilterOptions = memo(function FilterOptions({
  setTasksFilters,
  filtersForm,
  setAppliedFilters,
  setPageNumber,
  columnsBackup,
  filterTable,
  setFilterTable,
  setColumns,
  setColumnsBackup,
  appliedFilters,
  tasksTypes,
  switchViews,
  setDateFilter,
  setSelectedFamily,
  handleResetRolesSelect,
  setSelectedRoles,
  openFilterMenu,
  setOpenFilterMenu,
  clearFilters,
  totalFiltered,
  setTotalFiltered,
  dateSegmented,
  usersList,
  setUsersListPage,
  loadUsers,
  setSearchUsers,
  setSelectedUser,
  selectedUser,
  selectedRoles,
  setMeMode,
  tags,
  setSelectedTags,
}) {
  const { user } = useSelector((state) => state.user);
  const { families } = useSelector((state) => state?.families);
  const activeFilters = useSelector(
    (state) => state?.TasksRealTime?.activeFilters
  );

  const tasksFilters = useSelector(
    (state) => state?.TasksRealTime?.tasksFilters
  );
  const dispatch = useDispatch();
  const [t] = useTranslation("common");
  const [searchFamily, setSearchFamily] = useState("");
  const [isMount, setIsMount] = useState(false);
  const [optionsFamilies, setOptionsFamilies] = useState(
    families?.map((item) => ({
      value: item.id,
      label: item.label,
      isLeaf: false,
    }))
  );

  // The preset ranges for quick selection.
  const rangePresets = useMemo(
    () => [
      {
        label: t("voip.yesterday"),
        value: [
          dayjs().startOf("day").add(-1, "d"),
          dayjs().endOf("day").add(-1, "d"),
        ],
      },
      {
        label: t("voip.today"),
        value: [dayjs().startOf("day"), dayjs().endOf("day")],
      },
      {
        label: t("voip.today&yesterday"),
        value: [dayjs().startOf("day").add(-1, "d"), dayjs().endOf("day")],
      },
      {
        label: t("voip.currentWeek"),
        value: [dayjs().startOf("week"), dayjs().endOf("day")],
      },
      {
        label: t("voip.currentMonth"),
        value: [dayjs().startOf("month"), dayjs().endOf("month")],
      },
      {
        label: t("voip.last7days"),
        value: [dayjs().add(-7, "d"), dayjs()],
      },
      {
        label: t("voip.last14days"),
        value: [dayjs().add(-14, "d"), dayjs()],
      },
      {
        label: t("voip.last30days"),
        value: [dayjs().add(-30, "d"), dayjs()],
      },
      {
        label: t("voip.last90days"),
        value: [dayjs().add(-90, "d"), dayjs()],
      },
    ],
    [t]
  );

  // Update redux reducer on change.
  const handlePriorityFilterChange = useCallback(
    (checkedValues) => {
      const filterData = checkedValues?.length > 0 ? checkedValues : null;
      dispatch(
        setTasksFilters({ ...tasksFilters, filters: { priority: filterData } })
      );
    },
    [dispatch, tasksFilters, setTasksFilters]
  );
  useEffect(() => {
    const getChildrenElements = async (options) => {
      const selectedFamily = families.find((el) => el.id === options[0]);
      const targetOption = {
        value: selectedFamily?.id,
        label: selectedFamily?.label,
        isLeaf: false,
        // loading: false,
      };
      targetOption.loading = true;
      try {
        let elements = await getFamilyElementsById(options[0]);
        targetOption.children =
          elements.length > 0
            ? elements.map((el) => ({ ...el, isLeaf: true }))
            : [];
        setOptionsFamilies((prev) =>
          prev.map((el) => (el.value === options[0] ? targetOption : el))
        );
        filtersForm.setFieldsValue({ related_element: options });
        setSelectedFamily(
          getValueByKey(activeFilters, "related_element")?.related_element
        );
        targetOption.loading = false;
      } catch (e) {
        targetOption.loading = false;
        console.log(e);
      }
    };
    const related_element = getValueByKey(
      activeFilters,
      "related_element"
    )?.related_element;
    filtersForm.setFieldsValue({
      filterCondition:
        activeFilters?.length < 3 ? "and" : handleFilterConditionValue(),
      types: getValueByKey(activeFilters, "types")?.types,
      filterByDate: getValueByKey(activeFilters, "filterByDate")?.filterByDate
        ? [
            dayjs(
              getValueByKey(activeFilters, "filterByDate")?.filterByDate[0],
              user?.location?.date_format
            ),
            dayjs(
              getValueByKey(activeFilters, "filterByDate")?.filterByDate[1],
              user?.location?.date_format
            ),
          ]
        : undefined,
      filterByModule: getValueByKey(activeFilters, "filterByModule")
        ?.filterByModule,
      selectRoles: getValueByKey(activeFilters, "selectRoles")?.selectRoles,
      priorityFilter: getValueByKey(activeFilters, "priorityFilter")
        ?.priorityFilter,
      filterFromTodayOn: getValueByKey(activeFilters, "filterFromTodayOn")
        ?.filterFromTodayOn,
      user: getValueByKey(activeFilters, "user")?.user || user?.id,
      tags: getValueByKey(activeFilters, "tags")?.tags,
      // related_element:
      //   getValueByKey(activeFilters, "related_element")?.related_element || [],
    });
    if (
      Array.isArray(related_element) &&
      related_element.length === 2 &&
      !isMount
    ) {
      getChildrenElements(related_element);
      setIsMount(true);
    } else if (getValueByKey(activeFilters, "user")?.user) {
      setSelectedUser({ id: getValueByKey(activeFilters, "user")?.user });
    }
  }, [user?.location?.date_format]);
  // // // Prefill the form if there's any pre-activated filter.
  // useEffect(() => {
  //   const getChildrenElements = async (options) => {
  //     const selectedFamily = families.find((el) => el.id === options[0]);
  //     const targetOption = {
  //       value: selectedFamily?.id,
  //       label: selectedFamily?.label,
  //       isLeaf: false,
  //       // loading: false,
  //     };
  //     targetOption.loading = true;
  //     try {
  //       let elements = await getFamilyElementsById(options[0]);
  //       targetOption.children =
  //         elements.length > 0
  //           ? elements.map((el) => ({ ...el, isLeaf: true }))
  //           : [];
  //       setOptionsFamilies((prev) =>
  //         prev.map((el) => (el.value === options[0] ? targetOption : el))
  //       );
  //       filtersForm.setFieldsValue({ related_element: options });
  //       targetOption.loading = false;
  //     } catch (e) {
  //       targetOption.loading = false;
  //       console.log(e);
  //     }
  //   };
  //   console.log("again", "--");
  //   const related_element = getValueByKey(
  //     activeFilters,
  //     "related_element"
  //   )?.related_element;
  //   filtersForm.setFieldsValue({
  //     filterCondition:
  //       activeFilters?.length < 3 ? "and" : handleFilterConditionValue(),
  //     types: getValueByKey(activeFilters, "types")?.types,
  //     filterByDate: getValueByKey(activeFilters, "filterByDate")?.filterByDate
  //       ? [
  //           dayjs(
  //             getValueByKey(activeFilters, "filterByDate")?.filterByDate[0],
  //             user?.location?.date_format
  //           ),
  //           dayjs(
  //             getValueByKey(activeFilters, "filterByDate")?.filterByDate[1],
  //             user?.location?.date_format
  //           ),
  //         ]
  //       : undefined,
  //     filterByModule: getValueByKey(activeFilters, "filterByModule")
  //       ?.filterByModule,
  //     selectRoles: getValueByKey(activeFilters, "selectRoles")?.selectRoles,
  //     priorityFilter: getValueByKey(activeFilters, "priorityFilter")
  //       ?.priorityFilter,
  //     filterFromTodayOn: getValueByKey(activeFilters, "filterFromTodayOn")
  //       ?.filterFromTodayOn,
  //     // user: getValueByKey(activeFilters, "user")?.user || user?.id,
  //     // related_element:
  //     //   getValueByKey(activeFilters, "related_element")?.related_element || [],
  //   });
  //   if (
  //     Array.isArray(related_element) &&
  //     related_element.length === 2 &&
  //     !isMount
  //   ) {
  //     getChildrenElements(related_element);
  //     setIsMount(true);
  //   }
  //   console.log(
  //     getValueByKey(activeFilters, "user")?.user,
  //     "--",
  //     getValueByKey(activeFilters, "related_element")?.related_element
  //   );
  //   /* if (
  //     getValueByKey(activeFilters, "filterByModule")?.filterByModule?.length > 0
  //   ) {
  //     console.log("call elements api", activeFilters);
  //     getFamilyElementsById(
  //       getValueByKey(activeFilters, "filterByModule")?.filterByModule[0]
  //     );
  //   } */
  // }, [filtersForm, activeFilters, user?.location?.date_format]);
  const handleFilterConditionValue = () => {
    if (getValueByKey(activeFilters, "filterCondition")?.filterCondition) {
      return getValueByKey(activeFilters, "filterCondition")?.filterCondition;
    } else {
      return "and";
    }
  };

  const handlePopupScroll = (e) => {
    const target = e.target;
    const scrollHeight = target.scrollHeight;
    const scrollTop = target.scrollTop;
    const clientHeight = target.clientHeight;

    // Vérifie si on est proche de la fin (à 5px près)
    if (scrollHeight - scrollTop - clientHeight < 5 && !loadUsers) {
      setUsersListPage((prev) => prev + 1);
    }
  };

  // Handle close the drawer
  const closeDrawer = () => setOpenFilterMenu(false);

  // Set total filter to null after close drawer.
  const handleAfterOpenDrawerEffect = (open) =>
    !open && activeFilters?.length === 0 && setTotalFiltered(null);

  // // Handle form fields change.
  const handleFormChange = () => {
    function findModuleValue(data, word) {
      let moduleValue = null;

      const traverse = (obj) => {
        if (obj instanceof Object) {
          if (word in obj) {
            moduleValue = obj[word];
          } else {
            Object.values(obj).forEach((value) => traverse(value));
          }
        }
      };

      traverse(data);
      if (moduleValue) return { [word]: moduleValue };
    }
    const formValues = filtersForm.getFieldsValue();
    const filteredValues = Object.entries(formValues)
      .filter(
        ([, value]) =>
          value !== undefined &&
          value !== null &&
          value !== false &&
          (Array.isArray(value) ? value.length > 0 : true) &&
          value !== ""
      )
      .map(([key, value]) => ({ [key]: value }));
    const module = findModuleValue(appliedFilters, "module");
    const userName = findModuleValue(appliedFilters, "userName");

    function removeKeys(data) {
      const keysToRemove = ["module", "userName"];

      const removeKeys = (obj) => {
        if (obj instanceof Object) {
          keysToRemove.forEach((key) => {
            if (key in obj) {
              delete obj[key];
            }
          });
          Object.values(obj).forEach((value) => {
            removeKeys(value);
          });
        }
      };

      data.forEach((obj) => {
        removeKeys(obj);
      });
      return data;
    }
    setAppliedFilters(
      [...removeKeys(filteredValues), userName, module].filter((el) => el)
    );

    setPageNumber(1);
    if (filteredValues?.length < 3) {
      filtersForm.setFieldsValue({
        filterCondition: "and",
      });
    }
  };

  // Get family elements by family id.
  const getFamilyElementsById = async (familyId) => {
    try {
      const response = await MainService.getFamilyElement(familyId);

      // setOptionsrelated_element(
      //   response?.data?.data.map((el) => ({
      //     label: el.label_data,
      //     value: el.id,
      //   }))
      // );
      return response?.data?.data.map((el) => ({
        label: el.label_data.length > 20 ? el.label_data : el.label_data,
        value: el.id,
      }));
    } catch (error) {
      console.log(`Error ${error}`);
    }
  };

  // This function will be called when a user selects a value
  const loadDataFamily = async (selectedOptions) => {
    const targetOption = selectedOptions[selectedOptions.length - 1];

    // Vérifie si l'élément a déjà des enfants
    if (targetOption.children && targetOption.children.length > 0) {
      return;
    }
    targetOption.loading = true;

    try {
      let elements = await getFamilyElementsById(targetOption.value);
      targetOption.children =
        elements.length > 0
          ? elements.map((el) => ({ ...el, isLeaf: true }))
          : [];
      setOptionsFamilies([...optionsFamilies]);
      targetOption.loading = false;
    } catch (e) {
      targetOption.loading = false;
      console.log(e);
    }
  };

  const displayRender = (labels, selectedOptions) => {
    // Si pas d'option sélectionnée ou si ce n'est pas une feuille, retourne vide

    if (
      !selectedOptions ||
      selectedOptions.length === 0 ||
      !selectedOptions[selectedOptions.length - 1]?.isLeaf
    ) {
      return "";
    }
    return labels.join(" / ");
  };
  const filter = (inputValue, path) => {
    return path.some(
      (option) =>
        option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1
    );
  };
  const onSearchSelect = (value, selectedOptions) => {
    if (!selectedOptions || selectedOptions.length === 0) return;
    const lastOption = selectedOptions[selectedOptions.length - 1];
    if (!lastOption.isLeaf && !lastOption.children && !lastOption.loading) {
      loadDataFamily(selectedOptions);
    }
    if (lastOption.isLeaf) {
      let moduleFilterIndex = appliedFilters.findIndex(
        (filter) => "module" in filter
      );

      if (moduleFilterIndex !== -1) {
        const updatedFilters = [...appliedFilters];
        updatedFilters[moduleFilterIndex] = {
          module: selectedOptions[0]?.label + "/" + selectedOptions[1]?.label,
        };
        setAppliedFilters(updatedFilters);
      } else {
        setAppliedFilters([
          ...appliedFilters,
          {
            module: selectedOptions[0]?.label + "/" + selectedOptions[1]?.label,
          },
        ]);
      }
      setSelectedFamily([selectedOptions[0]?.value, selectedOptions[1]?.value]);
    }

    // if (lastOption.isLeaf) {
    //   filtersForm.setFieldsValue({ related_element: value });
    //   setSelectedFamily(selectedOptions);
    //   setAppliedFilters((prev) => [
    //     ...prev,
    //     { module: selectedOptions[0]?.label + "/" + selectedOptions[1]?.label },
    //   ]);
    // } else {
    //   if (!searchFamily) filtersForm.setFieldsValue({ related_element: [] });
    //   // else {
    //   //   filtersForm.setFieldsValue({ related_element: value });
    //   // }
    // }
  };
  const searchRender = (inputValue, path, options) => {
    const fullPath = path.map((option) => option.label).join(" / ");
    const lastOption = path[path.length - 1];

    // Ajoute un préfixe invisible pour trier : parents d'abord (0), enfants ensuite (1)
    return `${fullPath}`;
  };
  const customTagRender = (props) => {
    const { label, value, closable, onClose } = props;
    const isClosable = filtersForm?.getFieldsValue().selectRoles?.length > 1; // Show X icon only if more than 1 item
    return (
      <Tag
        closable={isClosable} // Enable close icon only if more than 1 item
        onClose={onClose} // Handle close action
        closeIcon={<CloseOutlined />} // Use Ant Design's X icon
        style={{ margin: "2px" }}
      >
        {label}
      </Tag>
    );
  };

  function removeModuleKey(data, wordsToRemove) {
    const removeKey = (obj) => {
      if (obj instanceof Object) {
        if (wordsToRemove in obj) {
          delete obj[wordsToRemove];
        } else {
          Object.values(obj).forEach((value) => removeKey(value));
        }
      }
    };

    removeKey(data);

    return data;
  }
  return (
    <Drawer
      width={500}
      mask={false}
      open={openFilterMenu}
      afterOpenChange={handleAfterOpenDrawerEffect}
      onClose={closeDrawer}
      title={
        <div className="flex items-center justify-between">
          {/* Result of filter */}
          <Typography.Title level={4}>
            {t("tasks.filterTitle")}
          </Typography.Title>
          {getRelevantFiltersCount(activeFilters, [
            "dateType",
            "filterCondition",
          ]) > 0 &&
            totalFiltered !== null && (
              <div className="flex items-center">
                <p className="mr-1 text-sm text-gray-400">
                  {t("tasks.filterResult")}
                </p>
                <Typography.Text mark strong>
                  {totalFiltered > 0 ? totalFiltered : t("tasks.noResult")}
                </Typography.Text>
              </div>
            )}
        </div>
      }
      footer={
        getRelevantFiltersCount(activeFilters, [
          "dateType",
          "filterCondition",
          "selectRoles",
        ]) > 0 && (
          <Form.Item className="flex justify-center">
            <Button onClick={clearFilters} type="text">
              {t("tasks.clearFilter")}
            </Button>
          </Form.Item>
        )
      }
    >
      <Form
        form={filtersForm}
        layout="vertical"
        onFieldsChange={handleFormChange}
      >
        {/* Info Alert */}
        <Alert
          message={
            <Typography.Text italic>{t("tasks.filterInfo")}</Typography.Text>
          }
          type="info"
          showIcon
          className="mb-6"
        />
        {/* Select AND or OR condition for filter in advanced */}
        <Form.Item
          name="filterCondition"
          label={
            <>
              {t("tasks.filterConditionLabel")}{" "}
              <Tooltip title={t("tasks.filterConditionInfo")}>
                <InfoCircleOutlined style={{ marginLeft: "5px" }} />
              </Tooltip>
            </>
          }
        >
          <Radio.Group
            disabled={
              getRelevantFiltersCount(activeFilters, [
                "dateType",
                "filterCondition",
              ]) <= 1
            }
            options={[
              {
                label: t("tasks.and"),
                value: "and",
              },
              {
                label: t("tasks.or"),
                value: "or",
              },
            ]}
          />
        </Form.Item>
        {/* Filter By Type */}
        <Form.Item label="Type" name="types">
          <Select
            allowClear
            showSearch
            onClear={() => setFilterTable("")}
            placeholder="Select a type"
            onChange={(value) => value && setFilterTable(Number(value))}
            filterOption={(input, option) => {
              var _a;
              return (
                (_a =
                  option === null || option === void 0
                    ? void 0
                    : option.labelType) !== null && _a !== void 0
                  ? _a
                  : ""
              )
                .toLowerCase()
                .includes(input.toLowerCase());
            }}
            options={tasksTypes.map((el) => ({
              labelType: el.label,
              label: (
                <div className="flex items-center gap-x-1">
                  <span>
                    <ChoiceIcons icon={el?.icons} />
                  </span>
                  <span>{el.label}</span>
                </div>
              ),
              value: el.id,
            }))}
          />
        </Form.Item>
        {/* Filter By Date */}
        <Form.Item name="filterByDate" label="Date">
          <DatePicker.RangePicker
            disabled={
              switchViews === "Calendar" ||
              (switchViews === "Table" && dateSegmented !== 3)
            }
            style={{ width: "100%" }}
            format={user?.location?.date_format}
            presets={rangePresets}
            onChange={(date, dateString) => setDateFilter(dateString)}
          />
        </Form.Item>
        {/* Filter By Module */}
        {/* <Form.Item name="related_element" label="Module">
          <Cascader
            options={options}
            loadData={loadData}
            changeOnSelect
            dropdownMenuColumnStyle={{
              maxWidth: "320px",
            }}
            placeholder={t("tasks.selectFamilyPlaceholder")}
          />
        </Form.Item> */}
        <Form.Item
          name="related_element"
          label="Module"
          rules={[
            {
              validator: (_, value) => {
                if (value && value.length > 0) {
                  const path = optionsFamilies.find(
                    (opt) => opt.value === value[0]
                  );
                  if (path && path.children) {
                    const child = path.children.find(
                      (opt) => opt.value === value[value.length - 1]
                    );
                    if (child && child.isLeaf) {
                      return Promise.resolve();
                    }
                  }
                  return Promise.reject(
                    "Veuillez sélectionner un élément enfant!"
                  );
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <Cascader
            options={optionsFamilies}
            loadData={loadDataFamily}
            onChange={onSearchSelect}
            onSearch={(value) => setSearchFamily(value)}
            changeOnSelect
            onClear={() => {
              setSelectedFamily([]);
              setAppliedFilters(removeModuleKey(appliedFilters, "module"));
              setAppliedFilters(
                removeModuleKey(appliedFilters, "related_element")
              );
            }}
            onDropdownVisibleChange={(isOpen, value) => {
              !isOpen &&
                searchFamily &&
                filtersForm?.getFieldsValue()?.related_element?.length === 1 &&
                filtersForm.setFieldsValue({ related_element: [] });
            }}
            showSearch={{
              filter,
              render: searchRender,
            }}
            placeholder="Recherchez ou sélectionnez un élément"
            // displayRender={displayRender}
          />
          {/* <Select
            allowClear
            showSearch
            placeholder={t("tasks.selectFamilyPlaceholder")}
            optionFilterProp="label"
            filterOption={(input, option) =>
              option?.label?.toLowerCase().includes(input?.toLowerCase())
            }
            options={families
              ?.map((el) => ({
                label: el.label,
                value: el.id,
              }))
              .sort((a, b) => a.label.localeCompare(b.label))}
            onSelect={(value) => {
              setSelectedFamily(value);
              getFamilyElementsById(value);
            }}
            onClear={() => setSelectedFamily(null)}
          /> */}
        </Form.Item>
        {/* <Form.Item name="related_element" label="Related element">
          <Select
            allowClear
            showSearch
            placeholder={t("tasks.selectFamilyPlaceholder")}
            optionFilterProp="label"
            filterOption={(input, option) =>
              option?.label?.toLowerCase().includes(input?.toLowerCase())
            }
            disabled={!ily}
            options={optionsrelated_element}
          />
        </Form.Item> */}
        <Form.Item label={t("menu2.users")} name="user">
          <Select
            showSearch
            placeholder={t("tags.selectUsers")}
            onSearch={setSearchUsers}
            options={usersList}
            filterOption={false}
            onPopupScroll={handlePopupScroll}
            loading={loadUsers}
            notFoundContent={loadUsers ? <Spin size="small" /> : null}
            onChange={(value, all) => {
              let moduleFilterIndex = appliedFilters.findIndex(
                (filter) => "userName" in filter
              );
              const filters = removeModuleKey(appliedFilters, "user");

              if (value !== user.id) {
                if (moduleFilterIndex !== -1) {
                  const updatedFilters = [...filters, { user: value }];
                  updatedFilters[moduleFilterIndex] = {
                    userName: all.name,
                  };
                  setAppliedFilters(updatedFilters);
                } else {
                  setAppliedFilters([
                    ...filters,
                    { user: value },
                    {
                      userName: all.name,
                    },
                  ]);
                }
              } else {
                setAppliedFilters((prev) => prev.filter((el) => !el.userName));
              }
              setSelectedUser({ id: all.value, label: all.name });
              setMeMode((prev) => (value !== user.id ? false : prev));
            }}
          />
        </Form.Item>
        {/* Filter By Role */}

        <Form.Item label="Role" name="selectRoles" initialValue={[0, 1, 2, 3]}>
          <Select
            // allowClear
            placeholder={t("tasks.selectRolePlaceholder")}
            mode="multiple"
            showSearch={false}
            options={rolesList(t)}
            // onClear={handleResetRolesSelect}
            tagRender={customTagRender}
            onChange={(value) => {
              if (value.length === 0) {
                filtersForm.setFieldsValue({ selectRoles: [0, 1, 2, 3] });
                setSelectedRoles([0, 1, 2, 3]);
              } else {
                // Sinon, on met à jour la sélection
                setSelectedRoles(value);
              }
            }}
          />
        </Form.Item>

        <Form.Item label={t("menu2.tags")} name="tags">
          <Select
            allowClear
            placeholder={t("tasks.selectTags")}
            mode="multiple"
            options={tags}
            onChange={setSelectedTags}
            filterOption={(input, option) => {
              var _a;
              return (
                (_a =
                  option === null || option === void 0
                    ? void 0
                    : option.label) !== null && _a !== void 0
                  ? _a
                  : ""
              )
                .toLowerCase()
                .includes(input.toLowerCase());
            }}
            optionRender={(option) => (
              <Space>
                <span role="img" aria-label={option.data.label}>
                  {option.data.icon}
                </span>
                {option.data.label}
              </Space>
            )}
            // onClear={handleResetRolesSelect}
          />
        </Form.Item>

        {/* Filter By Priority */}
        <Form.Item label={t("tasks.priority")} name="priorityFilter">
          <Checkbox.Group
            options={prioritiesListInFilter()}
            onChange={handlePriorityFilterChange}
          />
        </Form.Item>
      </Form>
    </Drawer>
  );
});

export default FilterOptions;
