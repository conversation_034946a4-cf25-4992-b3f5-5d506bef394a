import {
  GET_VOICE_SUCCESS,
  GET_VOICE_ERROR,
  IS_LOADING_VOICE,
} from "../../constants";
import MainService from "../../../services/main.service";

export const getVoice = () => async (dispatch) => {
  try {
    dispatch({ type: IS_LOADING_VOICE });
    const response = await MainService.voiceApiIPBX();
    dispatch({
      type: GET_VOICE_SUCCESS,
      payload: response?.data?.data,
    });
  } catch (error) {
    dispatch({
      type: GET_VOICE_ERROR,
      payload: error,
    });
  }
};
