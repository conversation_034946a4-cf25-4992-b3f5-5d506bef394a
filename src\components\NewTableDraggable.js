import {
  CaretRightOutlined,
  DeleteOutlined,
  MenuOutlined,
} from "@ant-design/icons";
import { DndContext } from "@dnd-kit/core";

import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import React, {
  useCallback,
  useEffect,
  useRef,
  useState,
  memo,
  useMemo,
} from "react";
import { generateAxios } from "../services/axiosInstance";
import { Form, Table, Space, Pagination, Button } from "antd";
import { useTranslation } from "react-i18next";
import { toastNotification } from "./ToastNotification";
import ActionsHeaderTable from "./ActionsHeaderTable";
import { useDispatch, useSelector } from "react-redux";
import { DeleteGroupWiki } from "../new-redux/actions/wiki.actions/deleteGroup";
import { DeleteSpecificGroup } from "../new-redux/actions/fields.actions/deleteSpecificGroup";
import { useLocation } from "react-router-dom";
import DropDownCrud from "./DropDownCrud";
import { updateGroupsRank } from "../new-redux/actions/fields.actions/updateGroupsRank";
import { changeIsDeleteRows } from "../new-redux/actions/table.actions/table";
import moment from "moment";
import { URL_ENV } from "index";
import { setNewInteraction } from "new-redux/actions/vue360.actions/vue360";
// const Row = ({ children, ...props }) => {
//   const {
//     attributes,
//     listeners,
//     setNodeRef,
//     setActivatorNodeRef,
//     transform,
//     transition,
//     isDragging,
//   } = useSortable({
//     id: props["data-row-key"],
//   });

//   const style = {
//     ...props.style,
//     transform: CSS.Transform.toString(
//       transform && {
//         ...transform,
//         scaleY: 1,
//       }
//     ),
//     transition,
//     ...(isDragging
//       ? {
//           position: "relative",
//           zIndex: 9999,
//         }
//       : {}),
//   };
//   //
//   return (
//     <tr {...props} ref={setNodeRef} style={style} {...attributes}>
//       {React.Children.map(children, (child) => {
//         if (child.key === "sort" && child?.props?.record?.system != 1) {
//           return React.cloneElement(child, {
//             children: (
//               <MenuOutlined
//                 ref={setActivatorNodeRef}
//                 style={{
//                   touchAction: "none",
//                   cursor: "move",
//                 }}
//                 {...listeners}
//               />
//             ),
//           });
//         }
//         return child;
//       })}
//     </tr>
//   );
// };
const NewTableDraggable = ({
  data,
  setData,
  loading,
  EditableCell,
  save,
  onFinishFailed,
  showHeader,
  cancel,
  form,
  apiRank,
  edit,
  isEditing,
  editingKey,
  columns,
  api,
  setDataStage,
  pipeline_id,
  setLoading,
  onRow,
  rowSelection,
  onRowClick,
  setIdPipeline = () => {},
  rowClassName,
  currentPage = 1,
  setCurrentPage = () => {},
  pageSize,
  setPageSize = () => {},
  handleTableChange = () => {},
  setAllData = () => {},
  setMaxDate = () => {},
  setGroupsWithBinders = () => {},
  setSelectedGroupWiki = () => {},
  selectedGroupWiki,
  setIcons = () => {},
}) => {
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.user);
  const { contactHeaderInfo: contactInfo } = useSelector(
    (state) => state?.contacts
  );
  const { contactInfoFromDrawer, openView360InDrawer } = useSelector(
    (state) => state?.vue360
  );
  const { pathname } = useLocation();
  // const [currentPage, setCurrentPage] = useState(1);
  const [hoveredRow, setHoveredRow] = useState(null);
  const [idDropdown, setIdDropdown] = useState(null);
  const [editingRecord, setEditingRecord] = useState(null);
  // const maxHeight = `calc(100vh - 300px) auto`;
  const tableRef = useRef(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const refSave = useRef(null);
  // const [buttonHeight, setButtonHeight] = useState("auto");
  // useEffect(() => {
  //   if (selectedRowKeys.length > 0) {
  //     setButtonHeight("auto");
  //   } else {
  //     const timeout = setTimeout(() => {
  //       setButtonHeight("0");
  //     }, 150);
  //     return () => clearTimeout(timeout);
  //   }
  // }, [selectedRowKeys]);
  // const handleRowHover = (record) => {
  //   setHoveredRow(record.key);
  //   console.log(record);
  // };

  useEffect(() => {
    setSelectedRowKeys([]);
    return () => {
      dispatch(changeIsDeleteRows(false));
    };
  }, [pathname]);
  const onSelectChange = (newSelectedRowKeys) => {
    dispatch(changeIsDeleteRows(newSelectedRowKeys.length > 0 ? true : false));
    setSelectedRowKeys(newSelectedRowKeys);
  };
  function getMaxEndDate(data) {
    let maxEndDate = null;
    for (let i = 0; i < data.length; i++) {
      const endDate = moment(data[i].end_date);
      if (!maxEndDate || endDate.isAfter(maxEndDate)) {
        maxEndDate = endDate;
      }
    }
    return maxEndDate?.format("YYYY-MM-DD HH:mm:ss");
  }
  // show button deleteAll
  const rowSelectionDeleteAll = {
    selectedRowKeys,
    onChange: onSelectChange,
    getCheckboxProps: (record) => ({
      disabled:
        record.system == "1" ||
        record.count > 0 ||
        record.is_used == "1" ||
        !record.id,

      // Column configuration not to be checked
      name: record.name,
      className: `${editingKey ? "mt-[5px]" : ""} `,
    }),
  };
  const handleRowLeave = () => {
    setHoveredRow(null);
  };
  // useEffect(() => {
  //   const tableBody = tableRef.current?.querySelector(".ant-table-body");
  //   if (tableBody) {
  //     const shouldShow = tableBody.offsetHeight >= window.innerHeight - 300;
  //     // setShouldShowScroll(shouldShow);
  //     if (shouldShow) {
  //       tableBody.style.overflowY = "auto";
  //     } else {
  //       tableBody.style.overflowY = "hidden";
  //     }
  //   }
  // }, [data, tableRef]);

  // useEffect(() => {
  //   const handleResize = () => {
  //     const windowHeight = window.innerHeight;
  //     const paginationHeight = 40; // Hauteur de la pagination, ajustez en conséquence
  //     const tableHeaderHeight = 54; // Hauteur de l'en-tête de la table, ajustez en conséquence
  //     const newTableHeight = windowHeight - paginationHeight - tableHeaderHeight - 32; // Ajustez 32 en fonction de vos styles
  //     setTableHeight(newTableHeight);
  //   };

  //   handleResize();
  //   window.addEventListener('resize', handleResize);
  //   return () => window.removeEventListener('resize', handleResize);
  // }, []);
  // useEffect(() => {
  //   const tableBody = document.querySelector('.ant-table-body');
  //   const tableHeight = tableBody ? tableBody.getBoundingClientRect().height : 0;
  //   const shouldShowScroll = tableHeight >= window.innerHeight - 300;
  //   console.log(tableHeight)
  //   console.log(window.innerHeight)
  //   if (shouldShowScroll) {
  //     tableBody.style.overflowY = 'auto';
  //   } else {
  //     tableBody.style.overflowY = 'hidden';
  //   }
  // }, [data]); //
  const Row = ({ children, ...props }) => {
    const {
      attributes,
      listeners,
      setNodeRef,
      setActivatorNodeRef,
      transform,
      transition,
      isDragging,
    } = useSortable({
      id: props["data-row-key"],
      // disabled: children[0]?.props?.record?.system == 1 ? true : false,
      disabled: data && data?.length < 2,
    });
    const style = {
      ...props.style,
      transform: CSS.Transform.toString(
        transform && {
          ...transform,
          scaleY: 1,
        }
      ),
      transition,
      ...(isDragging
        ? {
            position: "relative",
            zIndex: 99,
          }
        : {}),
    };

    return (
      // <tr {...props} ref={setNodeRef} style={style} {...attributes}>
      //   {React.Children.map(children, (child) => {
      //     if (child.key === "sort") {
      //       return React.cloneElement(child, {
      //         children: data.map((el, i) =>
      //           el.id == props["data-row-key"] ||
      //           el.id + el.label == props["data-row-key"] ? (
      //             <MenuOutlined
      //               ref={setActivatorNodeRef}
      //               style={{
      //                 touchAction: "none",
      //                 cursor: "move",
      //               }}
      //               {...listeners}
      //               key={el.id || i}
      //             />
      //           ) : (
      //             ""
      //           )
      //         ),
      //       });
      //     }
      //     return child;
      //   })}
      // </tr>

      //avec condition
      // <tr {...props} ref={setNodeRef} style={style} {...attributes}>
      //   {React.Children.map(children, (child) => {
      //     if (
      //       child.key === "sort" &&
      //       child?.props?.record?.system != 1 &&
      //       child?.props?.record?.id &&
      //       data.length > 1 &&
      //       api !== "indisponibilite"
      //     ) {
      //       return React.cloneElement(child, {
      //         children: (
      //           <Button
      //             type="text"
      //             size="small"
      //             shape="circle"
      //             ref={setActivatorNodeRef}
      //             style={{
      //               touchAction: "none",
      //               cursor: "move",
      //             }}
      //             // onMouseEnter={handleMouseEnter}
      //             // onMouseLeave={handleMouseLeave}
      //             {...listeners}
      //           >
      //             <MenuOutlined />
      //           </Button>
      //         ),
      //       });
      //     }
      //     if (
      //       child.key === "sort" &&
      //       child?.props?.record?.system == 1 &&
      //       data.length > 1
      //     ) {
      //       return React.cloneElement(child, {
      //         children: (
      //           <Button
      //             type="text"
      //             size="small"
      //             shape="circle"
      //             ref={setActivatorNodeRef}
      //             style={{
      //               touchAction: "none",
      //               cursor: "not-allowed",
      //               color: "#cccccc",
      //               background: "transparent",
      //             }}
      //             {...listeners}
      //           >
      //             <MenuOutlined />
      //           </Button>
      //         ),
      //       });
      //     }
      //     return child;
      //   })}
      // </tr>

      <tr {...props} ref={setNodeRef} style={style} {...attributes}>
        {React.Children.map(children, (child) => {
          if (
            child.key === "sort" &&
            child?.props?.record?.id &&
            data.length > 1 &&
            api !== "external-tokens"
            // &&
            // (user?.role === "SuperAdmin" || user?.role === "Admin")
            // &&
            //   api !== "pipelines" &&
            //   api !== "stages") ||
            //   pathname === "/settings/activity/pipelines")
          ) {
            return React.cloneElement(child, {
              children:
                user?.role === "SuperAdmin" ||
                user?.role === "Admin" ||
                (api === "stages" &&
                  child?.props?.record?.can_update_rank == 1) ? (
                  <MenuOutlined
                    ref={setActivatorNodeRef}
                    style={{
                      touchAction: "none",
                      cursor: "move",
                    }}
                    {...listeners}
                  />
                ) : (user?.role === "SuperAdmin" || user?.role === "Admin") &&
                  api !== "stages" ? (
                  <MenuOutlined
                    ref={setActivatorNodeRef}
                    style={{
                      touchAction: "none",
                      cursor: "move",
                    }}
                    {...listeners}
                  />
                ) : (
                  <MenuOutlined
                    ref={setActivatorNodeRef}
                    className="cursor-not-allowed"
                    style={{ color: "#dddddd" }}
                  />
                ),
            });
          }
          return child;
        })}
      </tr>
    );
  };
  const [dataBeforeRank, setDataBeforeRank] = useState(data);
  useEffect(() => {
    setDataBeforeRank(data);
  }, [loading]);
  const handleClick = (event) => {
    event.stopPropagation();
  };
  function hasReturnValue(func) {
    const returnValue = func();
    return returnValue !== undefined;
  }
  const mergedColumns = useMemo(
    () =>
      [
        {
          key: "sort",
          // width: data && data.length > 1 ? "48px" : 0,
          width: "30px",
          // api === "external-tokens"
          //   ? 0
          //   : user?.role === "SuperAdmin" || user?.role === "Admin"
          //   ? "48px"
          //   : 0,
          className: editingKey
            ? "remove_border_right_tr editingKey"
            : "remove_border_right_tr ",
        },

        {
          dataIndex: "operation",

          width: editingKey ? "60px" : "50px",

          render: (_, record, rowIndex) => {
            return (
              <div
                // onClick={handleClick}
                // className="flex items-center justify-start"
                className={`${editingKey ? "mt-[6px]" : ""} -ml-[10px]`}
              >
                {/* <DropDownCrud
              record={record}
              edit={edit}
              handleDelete={handleDelete}
              form={form}
              isEditing={isEditing}
              cancel={cancel}
              data={data}
              setData={setData}
              editingKey={editingKey}
              api={api}
              onRow={onRow}
              rowIndex={rowIndex}
              setIdDropdown={setIdDropdown}
              idDropdown={idDropdown}
            /> */}
                <ActionsHeaderTable
                  record={record}
                  edit={edit}
                  handleDelete={handleDelete}
                  form={form}
                  isEditing={isEditing}
                  cancel={cancel}
                  data={data}
                  setData={setData}
                  editingKey={editingKey}
                  api={api}
                  onRow={onRow}
                  rowIndex={rowIndex}
                  setIdDropdown={setIdDropdown}
                  idDropdown={idDropdown}
                  setEditingRecord={setEditingRecord}
                  ref={refSave}
                />
              </div>
              // record?.system != 1 && (
              // <div
              //   // onClick={handleClick}
              //   className="flex items-center justify-start"
              // >
              //   <DropDownCrud
              //     record={record}
              //     edit={edit}
              //     handleDelete={handleDelete}
              //     form={form}
              //     isEditing={isEditing}
              //     cancel={cancel}
              //     data={data}
              //     setData={setData}
              //     editingKey={editingKey}
              //     api={api}
              //     onRow={onRow}
              //     rowIndex={rowIndex}
              //     setIdDropdown={setIdDropdown}
              //     idDropdown={idDropdown}
              //   />
              // </div>
              // )
            );
          },
        },
        // ,{
        //     title: t("activities.name"),
        //     dataIndex: "label",
        //     key: "label",
        //     editable: true,
        //     sorter: (a, b) => a.label.localeCompare(b.label),
        //     render: (_, record) => {
        //       return (
        //         <div className="flex justify-between">
        //           <div
        //             className="font-medium text-blue-600 dark:text-blue-500 hover:underline cursor-pointer"
        //             onClick={(e) => {
        //               edit(record);
        //               handleClick(e);
        //             }}
        //           >
        //             <div dangerouslySetInnerHTML={{ __html: record.label }} />
        //           </div>
        //         </div>
        //       );
        //     },
        //   }
        ...columns,
      ].map((col) => {
        if (col != null && !col.editable) {
          return col;
        }

        return {
          ...col,
          onCell: (record, index) => ({
            record,
            inputType:
              col != null && col.dataIndex === "color"
                ? "radio"
                : col != null &&
                  (col.dataIndex === "departement_id" ||
                    col.dataIndex === "product_type" ||
                    col.dataIndex === "product_modules" ||
                    col.dataIndex === "nature" ||
                    col.dataIndex === "department" ||
                    col.dataIndex === "id_department")
                ? "select"
                : col != null && col.dataIndex === "action"
                ? "select"
                : col != null && col.dataIndex === "Color"
                ? "select"
                : col != null && col.dataIndex === "typefamilyproduct_id"
                ? "select"
                : col != null && col.dataIndex === "icon"
                ? "select"
                : col != null &&
                  (col.dataIndex === "typetask_id" ||
                    col.dataIndex === "users" ||
                    col.dataIndex === "tasktype_id")
                ? "tasks"
                : col != null &&
                  (col.dataIndex === "categorie" ||
                    col.dataIndex === "family_id")
                ? "categories"
                : col != null &&
                  (col.dataIndex === "companies" ||
                    col.dataIndex === "queue_number")
                ? "companies"
                : col != null && col.dataIndex === "icons"
                ? "icons"
                : col != null && col.dataIndex === "city"
                ? "selectCity"
                : col != null && col.dataIndex === "currency"
                ? "selectCurrency"
                : col != null && col.dataIndex === "percent"
                ? "percent"
                : col != null && col.dataIndex === "hours"
                ? "number"
                : col != null && col.dataIndex === "RIB"
                ? "number"
                : col != null && col.dataIndex === "minutes"
                ? "number"
                : col != null && col.dataIndex === "status"
                ? "switch"
                : col != null && col.dataIndex === "hidden"
                ? "switch"
                : col != null && col.dataIndex === "description"
                ? "textArea"
                : col != null && col.dataIndex === "Bank"
                ? "inputChar"
                : col != null &&
                  (col.dataIndex === "required" ||
                    col.dataIndex === "authorized")
                ? "requiredSwitch"
                : col != null && col.dataIndex === "confirmation"
                ? "previousSwitch"
                : col != null && col.dataIndex === "duration"
                ? "duration"
                : col != null &&
                  (col.dataIndex === "start_date" ||
                    col.dataIndex === "end_date" ||
                    col.dataIndex === "expires_at")
                ? "date"
                : col != null && col.dataIndex === "tags"
                ? "customDropdown"
                : "input",

            dataIndex: col != null && col.dataIndex,
            title: col != null && col.title,
            editing: col != null && isEditing(record),
            index,
          }),
        };
      }),
    [columns, editingKey, isEditing]
  );

  // Memoize the data source for the Table
  const memoizedData = useMemo(() => data, [data]);

  const handleDelete = async (key) => {
    let res;
    let deleted;
    let newData;

    if (api == "fields-groups") {
      dispatch(DeleteSpecificGroup(key));
    } else {
      try {
        if (api === "steps") {
          deleted = data && data?.find((item) => item.id == key);
          newData = data && data?.filter((item) => item.id != key);
        } else {
          deleted = data && data?.find((item) => item.key == key);
          newData = data && data?.filter((item) => item.key != key);
          setIcons((prev) => prev.filter((el) => el !== deleted.icons));
        }
        res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).delete(`/${api}/${key}`);

        // if (
        //   api === "steps" &&
        //   data.length === 2 &&
        //   data[1].confirmation === "true"
        // ) {
        //   const res = await generateAxios(URL_ENV?.REACT_APP_BASE_URL+process.env.REACT_APP_SUFFIX_API).post(
        //     `/steps/update/${data[1].id}`,
        //     {
        //       label: data[1].label,
        //       duration: data[1].duration,
        //       checklist_id: data[1].checkListId,
        //       required: data[1].required,
        //       confirmation: "false",
        //     }
        //   );

        //   setData((prev) =>
        //     prev
        //       .filter((el) => el.id !== data[0].id)
        //       .map((el) =>
        //         el.id === prev[1].id ? { ...el, confirmation: "false" } : el
        //       )
        //   );
        // }
        //else {
        setData((prev) =>
          prev
            .filter((item) => item.id != key)
            .map((el) =>
              el?.rank > deleted?.rank ? { ...el, rank: el?.rank - 1 } : el
            )
        );
        setAllData((prev) =>
          prev
            .filter((item) => item.id != key)
            .map((el) =>
              el?.rank > deleted?.rank ? { ...el, rank: el?.rank - 1 } : el
            )
        );
        if (api === "indisponibilite") {
          setMaxDate(
            getMaxEndDate(
              data &&
                data
                  .filter((item) => item.id != key)
                  .map((el) =>
                    el?.rank > deleted?.rank
                      ? { ...el, rank: el?.rank - 1 }
                      : el
                  )
            )
          );
        }
        if (
          openView360InDrawer &&
          contactInfoFromDrawer?.pipeline &&
          api === "stages" &&
          data.find((el) => Number(el.id) === Number(key))?.pipeline_id ===
            contactInfoFromDrawer?.pipeline
        ) {
          dispatch(setNewInteraction({ type: "updateStageFromDrawer" }));
        } else if (
          !openView360InDrawer &&
          api === "stages" &&
          data.find((el) => Number(el.id) === Number(key))?.pipeline_id ===
            contactInfo?.pipeline
        ) {
          dispatch(setNewInteraction({ type: "updateStage" }));
        }
        if (selectedRowKeys.length > 0) {
          setSelectedRowKeys((prev) => prev.filter((el) => el != key));
          dispatch(
            changeIsDeleteRows(
              selectedRowKeys.filter((el) => el != key).length > 0
                ? true
                : false
            )
          );
        }
        // }
        if (Number(pipeline_id) === Number(key)) {
          setDataStage([]);
        }
        if (
          api === "steps" &&
          data &&
          data.length === 2 &&
          data[1].confirmation === "true"
        ) {
          const res = await generateAxios(
            URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
          ).post(`/steps/update/${data[1].id}`, {
            label: data[1].label,
            duration: data[1].duration,
            checklist_id: data[1].checkListId,
            required: data[1].required,
            confirmation: "false",
          });

          setData((prev) =>
            prev.map((el) =>
              el.id === prev[0].id ? { ...el, confirmation: "false" } : el
            )
          );
        }
        if (api === "pipelines" && Number(pipeline_id) === Number(key)) {
          setIdPipeline("");
        }
        const deletedName = deleted?.name?.replace(/<\/?[^>]+(>|$)/g, "");
        let deletedLabel = deleted?.label?.replace(/<\/?[^>]+(>|$)/g, "");
        const deletedBank = deleted?.Bank?.replace(/<\/?[^>]+(>|$)/g, "");
        const deletedBinder = deleted?.label_fr;
        if (api === "groupe-wikis") {
          if (deleted) {
            setGroupsWithBinders((prev) =>
              prev.map((el) =>
                el.value === deleted?.classeur_id
                  ? {
                      ...el,
                      children: el.children.filter(
                        (opt) => opt.value !== deleted.id
                      ),
                    }
                  : el
              )
            );

            if (selectedGroupWiki[1] === deleted.id) {
              setSelectedGroupWiki([selectedGroupWiki[0], null]);
            }
          }
        }
        if (api === "classeur-wikis") {
          setGroupsWithBinders((prev) =>
            prev.filter((el) => Number(el.value) !== Number(key))
          );
          if (selectedGroupWiki[0] == key) {
            setSelectedGroupWiki([]);
          }
        }

        const message =
          deletedName ||
          deletedLabel ||
          deletedBank ||
          deletedBinder ||
          // t("menu2.unavailability") ||
          t(`toasts.${api}`);

        toastNotification("success", message + t("toasts.deleted"), "topRight");
      } catch (err) {
        console.log(err);
        setData(data);

        if (
          (api === "groupe-wikis" || api === "classeur-wikis") &&
          err?.response?.status === 424
        ) {
          toastNotification("error", err?.response?.data?.message, "topRight");
        } else if (err?.response?.status === 422 && api === "stages") {
          toastNotification("error", t("toasts.cannotDeleteStage"), "topRight");
        } else if (err?.response?.status === 422) {
          toastNotification(
            "error",
            // t(`toasts.${err?.response?.data?.message}`),,
            err?.response?.data?.message,

            "topRight"
          );
        } else {
          toastNotification("error", t("toasts.somethingWrong"), "topRight");
        }
      }
    }
  };

  const onDragEnd = async ({ active, over }) => {
    // if (active.id !== over?.id) {
    //   if (data[data.findIndex((i) => i.key === over?.id)]?.disabled) {
    //     return;
    //   } else if (
    //     data[data.findIndex((i) => i.id == over?.id)]?.staticNewRow ||
    //     data[data.findIndex((i) => i.id == active?.id)]?.staticNewRow
    //   ) {
    //     return;
    //   } else if (
    //     data[data.findIndex((i) => i.id == over?.id)]?.system == 1 ||
    //     data[data.findIndex((i) => i.id == active?.id)]?.system == 1
    //   ) {
    //     return;
    //   } else if (editingKey != "") {
    //     return;
    //   } else {
    if (active?.id && active?.id !== over?.id) {
      updateRank(
        arrayMove(
          data,
          data.findIndex((i) => i.key === active.id),
          data.findIndex((i) => i.key === over?.id)
        )
      );
      setData((previous) => {
        const activeIndex = previous.findIndex((i) => i.key === active.id);
        const overIndex = previous.findIndex((i) => i.key === over?.id);
        return arrayMove(previous, activeIndex, overIndex);
      });
    }
    //}
    // }
  };

  const updateRank = async (array) => {
    const formData = new FormData();
    array &&
      array.forEach((item, i) => formData.append(`rank[${item?.id}]`, i + 1));
    try {
      setLoading(true);
      apiRank != "/change-field-group-rank"
        ? await generateAxios(
            URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
          ).post(apiRank, formData)
        : dispatch(updateGroupsRank(formData));
      setData(array);
      setDataBeforeRank(array);
      if (
        openView360InDrawer &&
        contactInfoFromDrawer?.pipeline &&
        api === "stages" &&
        Array.isArray(data) &&
        data.length > 0 &&
        data[0]?.pipeline_id === contactInfoFromDrawer?.pipeline
      ) {
        dispatch(setNewInteraction({ type: "updateStageFromDrawer" }));
      }
      if (
        !openView360InDrawer &&
        api === "stages" &&
        Array.isArray(data) &&
        data.length > 0 &&
        data[0]?.pipeline_id === contactInfo?.pipeline
      ) {
        dispatch(setNewInteraction({ type: "updateStage" }));
      }
      // apiRank != "/change-field-group-rank" &&
      //   toastNotification("success", `rank succeeded`, "topRight");
      if (api === "groupe-wikis" || api === "classeur-wikis") {
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get("classeur-wiki-with-groups");

        setGroupsWithBinders(
          res.data.data.map((el) => ({
            value: el.id,
            label: el.label_fr,
            // selectable: false,
            children: el.groupe_wiki.map((grp) => ({
              value: grp.id,
              label: grp.label_fr,
            })),
          }))
        );
      }

      setLoading(false);
    } catch (err) {
      setData(dataBeforeRank);
      toastNotification("error", `rank failed`, "topRight");
      setLoading(false);

      console.log(err);
    }
  };

  const handleChangePage = (page, pageSize) => {
    setCurrentPage(page);
    setPageSize(pageSize);
    cancel({});
    setData(data.filter((item) => item.id));
  };

  const handleChangePageSize = (current, size) => {
    setCurrentPage(1);
    setPageSize(size);
  };

  const rowClassName1 = (record, index) => {
    if (record["key"] === editingKey) {
      return "editingRow";
    }
    return "";
  };

  const handleFormItemChange = (changedValues, allValues) => {
    // let myButton = document.getElementById("buttonSaveTable");

    let commonAttributes = {};
    // let svgElement = myButton.querySelector("svg");

    // Obtenir la liste des clés de l'objet plus petit
    let smallerKeys = Object.keys(allValues);
    // Filtrer les attributs de l'objet plus grand en fonction de la liste des clés
    editingRecord &&
      smallerKeys.forEach(function (key) {
        if (key in editingRecord) {
          commonAttributes[key] = editingRecord[key];
          if (editingRecord[key] === undefined) commonAttributes[key] = null;
        }
      });

    let trimmedJsonObject = Object.keys(allValues).reduce((acc, key) => {
      if (typeof allValues[key] === "string") {
        acc[key] = allValues[key].trim();
      } else {
        acc[key] = allValues[key] || null;
      }
      return acc;
    }, {});
    if (refSave.current) {
      if (
        JSON.stringify(commonAttributes) !== JSON.stringify(trimmedJsonObject)
      ) {
        refSave.current.disabled = false;
        refSave.current.querySelector("svg").style.color = "#16a34a";
      } else {
        refSave.current.disabled = true;
        refSave.current.querySelector("svg").style.color = "#00000040";
      }
    }
  };

  return (
    <>
      {
        <div className="-mt-10 mb-2">
          <Button
            type="primary"
            danger
            icon={<DeleteOutlined />}
            className={`${
              selectedRowKeys.length > 0
                ? "mx-4  flex items-center  opacity-100 "
                : "m-0 p-0 opacity-0"
            }    w-[200px] transition-all duration-300`}
            style={{
              visibility: selectedRowKeys.length > 0 ? "visible" : "hidden",
              // height: buttonHeight,
            }}
          >
            {selectedRowKeys.length}{" "}
            {t("tasks.deleteBtn", { s: selectedRowKeys.length > 1 ? "s" : "" })}
          </Button>
        </div>
      }

      <Form
        form={form}
        component={false}
        onFinish={save}
        onFinishFailed={onFinishFailed}
        autoComplete="off"
        scrollToFirstError
        layout="inline"
        onValuesChange={handleFormItemChange}
      >
        <DndContext onDragEnd={onDragEnd}>
          <SortableContext
            // rowKey array
            items={typeof data === "undefined" ? [] : data.map((i) => i.key)}
            strategy={verticalListSortingStrategy}
          >
            <Table
              components={{
                body: {
                  cell: EditableCell,

                  row: Row,
                },
              }}
              className="dragTable"
              ref={tableRef}
              onRow={(record, index) => {
                const attr = {
                  index,
                  ...onRow(record, index),
                };
                return attr;
              }}
              rowClassName={rowClassName || rowClassName1}
              rowKey="key"
              columns={mergedColumns}
              dataSource={memoizedData}
              loading={loading}
              size={"small"}
              scroll={{ y: "calc(100vh - 300px)" }}
              // style={{ overflowY: shouldShowScroll ? 'auto' : 'hidden' }}
              // scroll={{ y: maxHeight ,x:"auto"}}
              rowSelection={rowSelection}
              pagination={
                api === "checklists" ||
                api === "steps" ||
                api === "pipelines" ||
                api === "stages"
                  ? false
                  : {
                      current: currentPage,
                      pageSize: pageSize,
                      total: data && data.length,
                      onChange: handleChangePage,
                      onShowSizeChange: handleChangePageSize,
                      showSizeChanger: true,
                      pageSizeOptions: ["10", "20", "50", "100"],
                      hideOnSinglePage:
                        data && data?.filter((el) => el.id).length < 11 && true,
                    }
              }
              onChange={handleTableChange}
            />
          </SortableContext>
        </DndContext>
      </Form>
    </>
  );
};
export default NewTableDraggable;
