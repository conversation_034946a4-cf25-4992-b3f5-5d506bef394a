import { memo, useState } from "react";
import { useSelector } from "react-redux";
import {
  Button,
  Popover,
  Tooltip,
  Divider,
  Tag,
  Badge,
  Typography,
  Space,
} from "antd";
import { useTranslation } from "react-i18next";
import TagContent from "./TagContent";
import { FaCalendar } from "react-icons/fa";
import {
  hexToRgba,
  humanDate,
  truncateString,
} from "pages/voip/helpers/helpersFunc";
import "../../index.css";
import ChoiceIcons from "pages/components/ChoiceIcons";
import {
  CalendarCheck,
  CalendarCheck2,
  LucideNotebookText,
} from "lucide-react";
import DisplayAvatar from "../DisplayAvatar";

const TagColumn = ({
  tags,
  tasks,
  id,
  info,
  data,
  setDataSource,
  setIdCall,
  setOpenTask,
  setTaskId,
  canUserCreateOrUpdate,
  infoMetaTable,
}) => {
  //
  const [t] = useTranslation("common");

  const { user } = useSelector(({ user }) => user);
  const access = user.access || {};

  const [openPopCon, setOpenPopCon] = useState(false);
  //
  //
  const tagContents = (
    <div className="flex w-60 items-center justify-center">
      <TagContent
        tags={tags}
        id={id}
        data={data}
        info={info}
        setDataSource={setDataSource}
        setOpen={setOpenPopCon}
        infoMetaTable={infoMetaTable}
      />
    </div>
  );
  //
  const DisplayTags = memo(({ tags }) => {
    if (!tags?.tags?.length) return null;
    const firstTag = tags.tags[0];
    const remainingTags = tags.tags.slice(1);
    const renderTag = (tag, cursor) => (
      <div
        key={tag.id}
        className={`inline-flex cursor-${cursor} items-center space-x-1 rounded-md px-1 py-[3px] text-xs font-semibold`}
        style={{
          backgroundColor: hexToRgba(tag.color, 0.1),
          color: tag.color,
        }}
      >
        {tag.icon && (
          <DisplayAvatar
            size={20}
            icon={<ChoiceIcons icon={tag.icon} fontSize={16} />}
          />
        )}
        <span>{truncateString(tag?.label, 20, true)}</span>
      </div>
    );

    const remainingTagsContent = remainingTags?.length > 0 && (
      <div className="flex items-center space-x-1.5">
        {remainingTags.map(renderTag)}
      </div>
    );
    return (
      <div className="flex space-x-2">
        {firstTag && (
          <Popover
            title={
              <div className="px-2 pt-2">
                <div className="flex flex-col">
                  <p>{t("voip.updateQualify")}</p>
                  <p className="w-36 truncate font-extralight 	 text-gray-500">
                    {info?.name === null ? info?.number : info?.name}
                  </p>
                </div>
                <Divider style={{ margin: "5px 0 0" }} />
              </div>
            }
            content={tagContents}
            open={openPopCon}
            onOpenChange={(open) => {
              canUserCreateOrUpdate !== false && setOpenPopCon(open);
            }}
            trigger={["click"]}
            placement="bottomRight"
            overlayClassName="popover-tag-voip"
            overlayStyle={{ width: "17.5rem" }}
            overlayInnerStyle={{
              padding: "0px",
            }}
            arrow={false}
          >
            <Tooltip
              key={firstTag?.label}
              title={canUserCreateOrUpdate === false ? null : t("voip.editTag")}
            >
              {renderTag(
                firstTag,
                canUserCreateOrUpdate === false ? null : "pointer"
              )}
            </Tooltip>
          </Popover>
        )}

        {remainingTags?.length > 0 && (
          <Popover
            title={false}
            arrow={false}
            content={remainingTagsContent}
            placement="bottomRight"
          >
            <div
              className="inline-flex cursor-help items-center rounded-md bg-gray-500/10 px-1 text-xs font-semibold text-gray-600"
              style={{ backgroundColor: hexToRgba(null, 0.1) }}
            >
              +{remainingTags.length}...
            </div>
          </Popover>
        )}

        {tags?.note && (
          <Popover
            title={t("voip.qualifNote")}
            content={tags?.note}
            arrow={false}
            placement="bottomRight"
            overlayStyle={{ maxWidth: "17rem" }}
          >
            <LucideNotebookText
              size={17}
              style={{ margin: "3px 0 0 6px" }}
              className="mt-0.5 cursor-help text-blue-600"
            />
          </Popover>
        )}
      </div>
      // <div className="group flex w-full flex-row ">
      //   <div className="flex flex-wrap	 content-center gap-y-0.5">
      //     <div className="flex flex-row">
      //       <Popover
      //         title={
      //           <div className="px-2 pt-2">
      //             <div className="flex flex-col">
      //               <p>{t("voip.updateQualify")}</p>
      //               <p className="w-36 truncate font-extralight 	 text-gray-500">
      //                 {info?.name === null ? info?.number : info?.name}
      //               </p>
      //             </div>
      //             <Divider style={{ margin: "5px 0 0" }} />
      //           </div>
      //         }
      //         content={tagContents}
      //         open={openPopCon}
      //         onOpenChange={(open) => {
      //           canUserCreateOrUpdate !== false && setOpenPopCon(open);
      //         }}
      //         trigger={["click"]}
      //         placement="bottomRight"
      //         overlayClassName="popover-tag-voip"
      //         overlayStyle={{ width: "17.5rem" }}
      //         overlayInnerStyle={{
      //           padding: "0px",
      //         }}
      //         arrow={false}
      //       >
      //         <div className="flex flex-row ">
      //           <Tooltip
      //             key={firstTag?.label}
      //             title={
      //               canUserCreateOrUpdate === false ? null : t("voip.editTag")
      //             }
      //           >
      //             <Tag
      //               style={{
      //                 cursor: canUserCreateOrUpdate === false ? "" : "pointer",
      //               }}
      //               key={firstTag?.id}
      //               color={firstTag?.color}
      //             >
      //               <Space size={3}>
      //                 <ChoiceIcons icon={firstTag?.icon} fontSize={14} />
      //                 <span className="text-xs font-semibold">
      //                   {firstTag?.label}
      //                 </span>
      //               </Space>
      //             </Tag>
      //           </Tooltip>
      //         </div>
      //       </Popover>
      //       {firstTag.task ? (
      //         <PopoverTask
      //           t={t}
      //           setIdCall={setIdCall}
      //           setTaskId={setTaskId}
      //           setOpenTask={setOpenTask}
      //           idCall={id}
      //           task={firstTag.task}
      //         >
      //           <Badge
      //             offset={[-15, -8]}
      //             // status="processing"
      //             count={
      //               <FaCalendar
      //                 style={{
      //                   color: "red",
      //                   cursor: "help",
      //                   fontSize: 16,
      //                 }}
      //               />
      //             }
      //           />
      //         </PopoverTask>
      //       ) : null}
      //     </div>
      //     {remainingTags.length > 0 && (
      //       <Popover
      //         arrow={false}
      //         placement="bottomRight"
      //         color="rgb(250 250 250)"
      //         content={
      //           <div className="space-y-4">
      //             {remainingTags.map((tag, index) => (
      //               <div key={index} className="flex flex-row ">
      //                 <Tag
      //                   style={{
      //                     cursor: "pointer",
      //                     // marginRight:
      //                     //   index !== remainingTags.length - 1 ? "5px" : "0",
      //                   }}
      //                   key={tag?.id}
      //                   color={tag?.color}
      //                 >
      //                   <Space>
      //                     <ChoiceIcons icon={tag?.icon} fontSize={14} />
      //                     <span className="text-xs font-semibold">
      //                       {tag?.label}
      //                     </span>
      //                   </Space>
      //                 </Tag>
      //                 {tag.task ? (
      //                   <PopoverTask
      //                     t={t}
      //                     setIdCall={setIdCall}
      //                     setTaskId={setTaskId}
      //                     setOpenTask={setOpenTask}
      //                     idCall={id}
      //                     task={tag.task}
      //                   >
      //                     <Badge
      //                       offset={[-16, -8]}
      //                       status="processing"
      //                       count={
      //                         <FaCalendar
      //                           style={{
      //                             color: "red",
      //                             cursor: "help",
      //                             fontSize: 16,
      //                           }}
      //                         />
      //                       }
      //                     />
      //                   </PopoverTask>
      //                 ) : null}
      //               </div>
      //             ))}
      //           </div>
      //         }
      //         trigger={["hover"]}
      //       >
      //         <Tag className="cursor-help">
      //           <span className="text-xs font-semibold">{`+ ${remainingTags.length}...`}</span>
      //         </Tag>
      //       </Popover>
      //     )}
      //   </div>
      //   {tags?.note && (
      //     <Popover
      //       title={t("voip.qualifNote")}
      //       content={tags?.note}
      //       arrow={false}
      //       placement="bottomRight"
      //       overlayStyle={{ maxWidth: "17rem" }}
      //     >
      //       <LucideNotebookText className="mt-0.5 h-[18px] w-[18px] cursor-help text-blue-600" />
      //     </Popover>
      //   )}
      //   {tasks?.length > 0 && (
      //     <Space size={3} wrap>
      //       {tasks.map((task) => (
      //         <PopoverTask
      //           t={t}
      //           setIdCall={setIdCall}
      //           setTaskId={setTaskId}
      //           setOpenTask={setOpenTask}
      //           idCall={id}
      //           task={task}
      //         >
      //           <FaCalendar
      //             key={task.id}
      //             style={{
      //               color: "red",
      //               cursor: "help",
      //               fontSize: 16,
      //             }}
      //           />
      //         </PopoverTask>
      //       ))}
      //     </Space>
      //   )}
      // </div>
    );
  });
  //
  const popTitle = (
    <div className="px-2 pb-px pt-2">
      <div className="flex flex-row items-center  justify-between">
        <span>{t("voip.qualifyTheCall")}</span>
        {access.activities === "1" ? (
          <>
            <p>{t("voip.or")}</p>
            <div>
              {/* <Tooltip title="This action will open a task form"> */}
              <Button
                size="small"
                type="primary"
                onClick={() => {
                  setIdCall(id);
                  setOpenPopCon(false);
                  setOpenTask(true);
                }}
              >
                {t("voip.createTask")}
              </Button>
              {/* </Tooltip> */}
            </div>
          </>
        ) : null}
      </div>
      <p className="w-36 truncate font-extralight 	 text-gray-500">
        {info?.name === null ? info?.number : info?.name}
      </p>
      <Divider className="my-2" />
    </div>
  );
  //
  return tags?.tags?.length ? (
    // displayTags(tags)
    <DisplayTags tags={tags} />
  ) : (
    <Space>
      <Popover
        content={tagContents}
        title={popTitle}
        open={openPopCon}
        onOpenChange={(open) => setOpenPopCon(open)}
        trigger={["click"]}
        placement="bottomLeft"
        arrow={false}
        overlayClassName="popover-tag-voip"
        overlayStyle={{ width: "17.5rem" }}
        overlayInnerStyle={{
          padding: "0px",
        }}
      >
        <Button
          type="dashed"
          size="small"
          disabled={canUserCreateOrUpdate === false}
        >
          {t("voip.qualifyTheCall")}
        </Button>
      </Popover>
      {tasks?.length > 0 && (
        <Space size={3} wrap>
          {tasks.map((task) => (
            <PopoverTask
              key={task.id}
              t={t}
              setIdCall={setIdCall}
              setTaskId={setTaskId}
              setOpenTask={setOpenTask}
              idCall={id}
              task={task}
            >
              <FaCalendar
                key={task.id}
                style={{
                  color: "red",
                  cursor: "help",
                  fontSize: 16,
                }}
              />
            </PopoverTask>
          ))}
        </Space>
      )}
    </Space>
  );
};

export const PopoverTask = ({
  children,
  t,
  setIdCall,
  setTaskId,
  setOpenTask,
  idCall,
  task,
}) => {
  return (
    <Popover
      key={"task" + task.id}
      title={t("voip.taskInfo")}
      content={
        <div className="flex flex-col">
          <div className="flex flex-row justify-between space-x-1">
            <span className="font-semibold text-slate-500">
              {t("table.startDate")}
            </span>
            <div>
              <span className="font-semibold">
                {humanDate(`${task.start_date} ${task.start_time}`, t, "table")}
              </span>
            </div>
          </div>
          <div className="flex flex-row justify-between space-x-1">
            <span className="font-semibold text-slate-500">
              {t("table.endDate")}
            </span>
            <div>
              <span className="font-semibold">
                {humanDate(`${task.end_date} ${task.end_time}`, t, "table")}
              </span>
            </div>
          </div>
          <Typography.Link
            onClick={(e) => {
              e.preventDefault();
              setIdCall(idCall);
              setTaskId(task.id);
              setOpenTask(true);
            }}
            className="mt-3 flex justify-end font-semibold"
          >
            {t("voip.moreInfo")}
          </Typography.Link>
        </div>
      }
    >
      {children}
    </Popover>
  );
};

export default TagColumn;
