import {
  Button,
  Dropdown,
  Popover,
  Space,
  Tag,
  Tooltip,
  Typography,
} from "antd";
import {
  InfoCircleOutlined,
  InfoCircleTwoTone,
  MessageOutlined,
  PhoneOutlined,
  TeamOutlined,
  GlobalOutlined,
} from "@ant-design/icons";
import { FiCopy } from "react-icons/fi";
import { FiMoreVertical } from "react-icons/fi";
import {
  HiOutlineBuildingOffice,
  HiOutlineUserGroup,
  HiOutlineVideoCamera,
} from "react-icons/hi2";
import { generateUrlToView360 } from "../helpers/helpersFunc";
import { HighlightSearchW } from ".";
import { CgUserlane } from "react-icons/cg";
import { PiArrowFatRightFill } from "react-icons/pi";
import DisplayAvatar from "./DisplayAvatar";
import { FaUsers } from "react-icons/fa";
import { ImUsers } from "react-icons/im";
import { MdOutlinePersonAddAlt } from "react-icons/md";
import DisplayModuleIconAndText from "./DisplayModuleIconAndText";
import { openDrawerChat } from "new-redux/actions/voip.actions/handleDrawerChat";
import { Refs_IDs } from "components/tour/tourConfig";

const CallerColumn = (props) => {
  //
  const {
    record: {
      callInfo,
      isQueue,
      isQueueTreated,
      queueInfo,
      isGroup,
      isGroupTreated,
      groupInfo,
      isForwarding,
      forwardingInfo,
      isOnTheNum,
      conf,
      firstTime,
      onTheNum,
      disposition,
      checkedIcon,
      callSense,
      isInternalForwarding,
      isInternalTransfer,
      internalForwardingInfo,
    },
    t,
    call,
    currentUser,
    searchText,
    dispatch,
  } = props;
  //
  const mainText = `font-semibold truncate`;
  //
  const secondaryTextStyle = (truncate) =>
    `${
      disposition === "missed" ? "text-red-500" : "text-slate-500"
    }  leading-4 font-medium	${truncate ? "truncate" : ""}`;
  //
  const popContent = (item) =>
    item.id ? (
      <div className="flex flex-row items-center space-x-2">
        <DisplayAvatar name={item.name} urlImg={item.image} size={30} />
        <p className={mainText}>{item.name}</p>
        <Space size={3}>
          <Button
            disabled={!item.uuid}
            onClick={() => dispatch(openDrawerChat(item.uuid))}
            size="small"
            type="link"
            icon={<MessageOutlined style={{ fontSize: 14 }} />}
          />

          <Button
            onClick={() => call(item?.number, item?.id, item?.familyId)}
            disabled={props?.extension === `${currentUser?.extension}`}
            size="small"
            type="link"
            icon={<PhoneOutlined rotate={100} style={{ fontSize: 16 }} />}
          />
        </Space>
      </div>
    ) : null;
  //
  return (
    <div className={`relative flex w-full flex-row justify-between`}>
      <div className="relative flex w-10/12 flex-row items-center space-x-2">
        <div className="_avatar_">
          <DisplayAvatar
            name={
              isQueue
                ? queueInfo?.name
                : isGroup
                ? groupInfo?.name
                : callInfo?.name
            }
            urlImg={
              isQueue
                ? queueInfo?.image
                : isGroup
                ? groupInfo?.image
                : callInfo?.image
            }
            size={40}
            icon={
              isQueue ? (
                <ImUsers />
              ) : isGroup ? (
                <FaUsers />
              ) : callInfo.number === "sphere_visio" ? (
                <HiOutlineVideoCamera />
              ) : null
            }
          />
        </div>
        <div
          className={`${
            disposition === "missed" ? "text-red-500" : ""
          } w-11/12 `}
        >
          {isQueue ? (
            <div className=" flex-col space-y-0.5 ">
              <div className="flex flex-row space-x-1">
                <p className={mainText}>
                  {HighlightSearchW(queueInfo.name, searchText)}
                </p>
                {/* <Tooltip title={t("voip.queue")}>
                  <ImUsers
                    style={{
                      fontSize: 15,
                      cursor: "help",
                    }}
                  />
                </Tooltip> */}
              </div>
              {isQueueTreated ? (
                callSense === "outgoing" ? (
                  <div className="flex flex-row space-x-1">
                    <p className={secondaryTextStyle()}>
                      {HighlightSearchW(queueInfo?.number, searchText)}
                    </p>
                    <p className={secondaryTextStyle(true)}>
                      {t("voip.treatedBy")}
                    </p>
                    <Popover content={popContent(callInfo)}>
                      <p className={`${secondaryTextStyle()} cursor-help`}>
                        {HighlightSearchW(callInfo?.number, searchText)}
                      </p>
                    </Popover>
                  </div>
                ) : (
                  <div className="flex flex-row space-x-1">
                    <p className={secondaryTextStyle()}>
                      {HighlightSearchW(queueInfo?.number, searchText)}
                    </p>
                    <p className={secondaryTextStyle(true)}>{t("voip.from")}</p>
                    <Popover content={popContent(callInfo)}>
                      <p
                        className={`${secondaryTextStyle()} ${
                          callInfo.id ? "cursor-help" : ""
                        }`}
                      >
                        {HighlightSearchW(callInfo?.number, searchText)}
                      </p>
                    </Popover>
                  </div>
                )
              ) : (
                <p className={secondaryTextStyle()}>
                  {HighlightSearchW(queueInfo.number, searchText)}
                </p>
              )}
            </div>
          ) : isGroup ? (
            <div className=" flex-col space-y-0.5 ">
              <div className="flex flex-row space-x-1">
                <p className={mainText}>
                  {HighlightSearchW(groupInfo.name, searchText)}
                </p>
                {/* <Tooltip title={t("voip.callGroup")}>
                  <FaUsers
                    style={{
                      fontSize: 16,
                      cursor: "help",
                    }}
                  />
                </Tooltip> */}
              </div>
              {isGroupTreated ? (
                callSense === "outgoing" ? (
                  <div className="flex flex-row space-x-1">
                    <p className={secondaryTextStyle()}>
                      {HighlightSearchW(groupInfo?.number, searchText)}
                    </p>
                    <p className={secondaryTextStyle(true)}>
                      {t("voip.treatedBy")}
                    </p>
                    <Popover content={popContent(callInfo)}>
                      <p className={`${secondaryTextStyle()} cursor-help`}>
                        {HighlightSearchW(callInfo?.number, searchText)}
                      </p>
                    </Popover>
                  </div>
                ) : (
                  <div className="flex flex-row space-x-1">
                    <p className={secondaryTextStyle()}>
                      {HighlightSearchW(groupInfo?.number, searchText)}
                    </p>
                    <p className={secondaryTextStyle(true)}>{t("voip.from")}</p>
                    <Popover content={popContent(callInfo, searchText)}>
                      <p
                        className={`${secondaryTextStyle()} ${
                          callInfo.id ? "cursor-help" : ""
                        }`}
                      >
                        {HighlightSearchW(callInfo?.number, searchText)}
                      </p>
                    </Popover>
                  </div>
                )
              ) : (
                <p className={secondaryTextStyle()}>
                  {HighlightSearchW(groupInfo.number, searchText)}
                </p>
              )}
            </div>
          ) : isForwarding ? (
            <div className="w-full flex-col space-y-0.5 ">
              <div className="flex flex-row space-x-0.5">
                <p className={`${mainText} max-w-[50%]`}>
                  {HighlightSearchW(
                    callInfo?.name ?? callInfo?.number,
                    searchText
                  )}
                </p>
                <Tag
                  style={{ lineHeight: "17px", paddingInline: 3 }}
                  color="volcano"
                >
                  {t("voip.referral")}
                </Tag>
              </div>
              <div className="flex flex-row space-x-1">
                <p className={secondaryTextStyle()}>
                  {HighlightSearchW(callInfo?.number, searchText)}
                </p>
                <p className={secondaryTextStyle(true)}>
                  {t("voip.transferred")}
                </p>
                <p className={secondaryTextStyle()}>
                  {HighlightSearchW(forwardingInfo?.number, searchText)}
                </p>
              </div>
            </div>
          ) : isOnTheNum ? (
            <div className="w-full flex-col space-y-0.5 ">
              <p className={mainText}>
                {HighlightSearchW(callInfo.name, searchText)}
              </p>
              <div className="flex flex-row space-x-1">
                <p className={secondaryTextStyle()}>
                  {HighlightSearchW(callInfo?.number, searchText)}
                </p>
                <p className={secondaryTextStyle(true)}>{t("voip.onTheNum")}</p>
                <p className={secondaryTextStyle()}>
                  {HighlightSearchW(onTheNum, searchText)}
                </p>
              </div>
            </div>
          ) : (
            <div className="flex flex-col space-y-0.5">
              <div
                className={`${
                  ""
                  // isInternalForwarding ? "max-w-[50%]" : "max-w-full"
                } flex flex-row space-x-0.5`}
              >
                <p className={mainText}>
                  {callInfo.name
                    ? HighlightSearchW(callInfo.name, searchText)
                    : HighlightSearchW(
                        callInfo.number === "sphere_visio"
                          ? "Sphere Visio"
                          : callInfo.number,
                        searchText
                      )}{" "}
                  <DisplayModuleIconAndText
                    familyId={conf ? "conf" : callInfo.familyId}
                    t={t}
                    iconStyle={conf ? { fontSize: 16, cursor: "help" } : null}
                  />
                </p>
                {isInternalForwarding || isInternalTransfer ? (
                  <div className="flex flex-row items-start space-x-0.5">
                    (
                    <p>
                      {isInternalForwarding
                        ? t("voip.afterCallForwarding")
                        : t("voip.afterCallTransfer")}
                    </p>
                    <Tooltip
                      placement="bottomRight"
                      title={
                        <Space
                          direction="vertical"
                          size={1}
                          split={
                            <PiArrowFatRightFill
                              style={{
                                fontSize: 16,
                                color: "rgb(234 88 12)",
                                transform: "rotate(90deg)",
                                marginLeft: 7,
                              }}
                            />
                          }
                        >
                          {internalForwardingInfo.map((item, i) => (
                            <div
                              className="flex flex-row items-center space-x-1"
                              key={i}
                            >
                              <div className="_avatar_">
                                <DisplayAvatar
                                  name={item?.name}
                                  urlImg={item?.image}
                                  size={30}
                                />
                              </div>
                              <div className="w-full flex-col space-y-0.5 ">
                                <p
                                  className={`truncate font-semibold`}
                                  style={{ maxWidth: "8rem" }}
                                >
                                  {item.id === currentUser.id
                                    ? t("voip.me")
                                    : item.name}
                                </p>
                                <p
                                  className={
                                    callInfo.name
                                      ? "font-medium leading-4 text-slate-500"
                                      : "truncate font-semibold"
                                  }
                                >
                                  {item.id === currentUser.id
                                    ? null
                                    : item.number}
                                </p>
                              </div>
                            </div>
                          ))}
                        </Space>
                      }
                    >
                      <InfoCircleTwoTone
                        style={{
                          fontSize: 14,
                          cursor: "help",
                          marginTop: 2,
                        }}
                      />
                    </Tooltip>
                    )
                  </div>
                ) : null}
              </div>
              <div className="flex flex-row space-x-1">
                <p className={secondaryTextStyle()}>
                  {callInfo.name
                    ? HighlightSearchW(callInfo.number, searchText)
                    : null}
                </p>
                {firstTime ? (
                  <Typography.Text mark className={secondaryTextStyle()}>
                    {t("voip.firstTime")}
                  </Typography.Text>
                ) : null}
              </div>
            </div>
          )}
        </div>
      </div>
      <div className="flex flex-row items-center space-x-1">
        {checkedIcon}
        {callInfo.number !== "sphere_visio" && <ActionMenu {...props} />}
      </div>
    </div>
  );
};

export const ActionMenu = (props) => {
  //
  const {
    index,
    record: {
      callInfo,
      isQueue,
      isQueueTreated,
      queueInfo,
      isGroup,
      isGroupTreated,
      groupInfo,
      isForwarding,
      forwardingInfo,
      isOnTheNum,
    },
    t,
    call,
    dispatch,
    handleDisplayElementInfo,
    handleAddItem,
    navigate,
    isGuest,
  } = props;

  const menuDropdown = () => {
    const items = [];

    const copyIcon = (text) => (
      <div className="pl-1">
        <Typography.Paragraph
          copyable={{
            text: text,
            icon: [
              <FiCopy
                style={{
                  color: "rgb(22, 119, 255)",
                  marginTop: "2px",
                  fontSize: "15px",
                }}
              />,
            ],
          }}
        />
      </div>
    );

    const pushItem = (key, icon, label, onClick, disabled, children) => {
      items.push({
        key,
        icon,
        label,
        onClick,
        disabled,
        children,
      });
    };
    // If Group
    if (isGroup) {
      pushItem(
        `call`,
        <PhoneOutlined
          rotate={100}
          style={{ fontSize: 15 }}
          className="text-slate-500"
        />,
        isGroupTreated ? (
          t("voip.call")
        ) : (
          <div className="flex flex-row justify-between space-x-2">
            <span>{t("voip.call")}</span>
            {copyIcon(groupInfo.number)}
          </div>
        ),
        () => !isGroupTreated && call(groupInfo.number),
        null,
        isGroupTreated
          ? [
              {
                key: "call-group",
                label: (
                  <div className="flex flex-row justify-between space-x-2">
                    <span>{groupInfo.number}</span>
                    {copyIcon(groupInfo.number)}
                  </div>
                ),
                onClick: () => call(groupInfo.number),
              },
              {
                key: "call-treated",
                label: (
                  <div className="flex flex-row justify-between space-x-2">
                    <span>{callInfo.number}</span>
                    {copyIcon(callInfo.number)}
                  </div>
                ),
                onClick: () =>
                  call(callInfo.number, callInfo?.id, callInfo?.familyId),
              },
            ]
          : null
      );
      if (isGroupTreated) {
        if (callInfo.id && callInfo.uuid) {
          items.push({ type: "divider" });
          pushItem(
            `chat-${callInfo.number}`,
            <MessageOutlined
              style={{ fontSize: 14 }}
              className="text-slate-500"
            />,
            `${t("voip.chat")}`,
            () => dispatch(openDrawerChat(callInfo?.uuid))
          );
          items.push({ type: "divider" });
          pushItem(
            "more-info",
            <InfoCircleOutlined
              style={{ fontSize: 14 }}
              className="text-slate-500"
            />,
            `${t("voip.moreInfo")}`,
            () =>
              handleDisplayElementInfo(callInfo.name, {
                id: callInfo.id,
                familyId: callInfo.familyId,
              })
          );
          pushItem(
            "vue-360",
            <GlobalOutlined
              className="text-slate-500"
              style={{ fontSize: 14 }}
            />,
            `${t("voip.view360")}`,
            () => {
              dispatch({
                type: "SET_CONTACT_HEADER_INFO",
                payload: {
                  name: callInfo.name,
                  id: callInfo.id,
                },
              });
              navigate(
                generateUrlToView360(callInfo.familyId, callInfo.id, "v2")
              );
            }
          );
        } else {
          items.push({ type: "divider" });
          pushItem(
            "add",
            <MdOutlinePersonAddAlt
              className="text-slate-500"
              style={{ fontSize: 15 }}
            />,
            `${t("voip.addToModule")}`,
            null,
            null,
            [
              {
                label: t("contacts.leads"),
                key: "add-leads",
                icon: (
                  <CgUserlane
                    className="text-slate-500"
                    style={{ fontSize: 15 }}
                  />
                ),
                onClick: () => handleAddItem(9, callInfo?.number),
              },
              {
                label: t("contacts.contact"),
                key: "add-contact",
                icon: (
                  <HiOutlineUserGroup
                    className="text-slate-500"
                    style={{ fontSize: 15 }}
                  />
                ),
                onClick: () => handleAddItem(2, callInfo?.number),
              },
              {
                label: t("contacts.company"),
                key: "add-company",
                icon: (
                  <HiOutlineBuildingOffice
                    className="text-slate-500"
                    style={{ fontSize: 15 }}
                  />
                ),
                onClick: () => handleAddItem(1, callInfo?.number),
              },
            ]
          );
        }
      }
    }
    // If Queue
    else if (isQueue) {
      pushItem(
        `${queueInfo.number}`,
        <PhoneOutlined
          className="text-slate-500"
          rotate={100}
          style={{ fontSize: 15 }}
        />,
        isQueueTreated ? (
          t("voip.call")
        ) : (
          <div className="flex flex-row justify-between space-x-2">
            <span>{t("voip.call")}</span>
            {copyIcon(queueInfo.number)}
          </div>
        ),
        () => !isQueueTreated && call(queueInfo.number),
        null,
        isQueueTreated
          ? [
              {
                key: "call-group",
                label: (
                  <div className="flex flex-row justify-between space-x-2">
                    <span>{queueInfo.number}</span>
                    {copyIcon(queueInfo.number)}
                  </div>
                ),
                onClick: () => call(queueInfo.number),
              },
              {
                key: "call-treated",
                label: (
                  <div className="flex flex-row justify-between space-x-2">
                    <span>{callInfo.number}</span>
                    {copyIcon(callInfo.number)}
                  </div>
                ),
                onClick: () =>
                  call(callInfo.number, callInfo?.id, callInfo?.familyId),
              },
            ]
          : null
      );
      if (isQueueTreated) {
        if (callInfo.id && callInfo.uuid) {
          items.push({ type: "divider" });
          pushItem(
            `chat-${callInfo.number}`,
            <MessageOutlined
              style={{ fontSize: 14 }}
              className="text-slate-500"
            />,
            `${t("voip.chat")}`,
            () => dispatch(openDrawerChat(callInfo?.uuid))
          );
          items.push({ type: "divider" });
          pushItem(
            "more-info",
            <InfoCircleOutlined
              style={{ fontSize: 14 }}
              className="text-slate-500"
            />,
            `${t("voip.moreInfo")}`,
            () =>
              handleDisplayElementInfo(callInfo.name, {
                id: callInfo.id,
                familyId: callInfo.familyId,
              })
          );
          pushItem(
            "vue-360",
            <GlobalOutlined
              style={{ fontSize: 14 }}
              className="text-slate-500"
            />,
            `${t("voip.view360")}`,
            () => {
              dispatch({
                type: "SET_CONTACT_HEADER_INFO",
                payload: {
                  name: callInfo.name,
                  id: callInfo.id,
                },
              });
              navigate(
                generateUrlToView360(callInfo.familyId, callInfo.id, "v2")
              );
            }
          );
        } else {
          items.push({ type: "divider" });
          pushItem(
            "add",
            <MdOutlinePersonAddAlt
              style={{ fontSize: 15 }}
              className="text-slate-500"
            />,
            `${t("voip.addToModule")}`,
            null,
            null,
            [
              {
                label: t("contacts.leads"),
                key: "add-leads",
                icon: (
                  <CgUserlane
                    style={{ fontSize: 15 }}
                    className="text-slate-500"
                  />
                ),
                onClick: () => handleAddItem(9, callInfo?.number),
              },
              {
                label: t("contacts.contact"),
                key: "add-contact",
                icon: (
                  <HiOutlineUserGroup
                    style={{ fontSize: 15 }}
                    className="text-slate-500"
                  />
                ),
                onClick: () => handleAddItem(2, callInfo?.number),
              },
              {
                label: t("contacts.company"),
                key: "add-company",
                icon: (
                  <HiOutlineBuildingOffice
                    style={{ fontSize: 15 }}
                    className="text-slate-500"
                  />
                ),
                onClick: () => handleAddItem(1, callInfo?.number),
              },
            ]
          );
        }
      }
    }
    // Is Forwarding
    else if (isForwarding) {
      pushItem(
        `${callInfo.number}`,
        <PhoneOutlined
          rotate={100}
          style={{ fontSize: 15 }}
          className="text-slate-500"
        />,
        <div className="flex flex-row justify-between space-x-2">
          <span>{t("voip.call")}</span>
          {copyIcon(callInfo.number)}
        </div>,
        () => call(callInfo.number, callInfo?.id, callInfo?.familyId)
      );
      if (callInfo.id && callInfo.uuid) {
        items.push({ type: "divider" });
        pushItem(
          `chat-${callInfo.number}`,
          <MessageOutlined
            style={{ fontSize: 14 }}
            className="text-slate-500"
          />,
          `${t("voip.chat")}`,
          () => dispatch(openDrawerChat(callInfo.uuid))
        );
        items.push({ type: "divider" });
        pushItem(
          "more-info",
          <InfoCircleOutlined
            style={{ fontSize: 14 }}
            className="text-slate-500"
          />,
          `${t("voip.moreInfo")}`,
          () =>
            handleDisplayElementInfo(callInfo.name, {
              id: callInfo.id,
              familyId: callInfo.familyId,
            })
        );
        pushItem(
          "vue-360",
          <GlobalOutlined
            style={{ fontSize: 14 }}
            className="text-slate-500"
          />,
          `${t("voip.view360")}`,
          () => {
            dispatch({
              type: "SET_CONTACT_HEADER_INFO",
              payload: {
                name: callInfo.name,
                id: callInfo.id,
              },
            });
            navigate(
              generateUrlToView360(callInfo.familyId, callInfo.id, "v2")
            );
          }
        );
      }
    }
    //if on the number
    else if (
      isOnTheNum ||
      (!isGroup &&
        !isQueue &&
        !forwardingInfo &&
        callInfo.id &&
        callInfo.familyId)
    ) {
      pushItem(
        `${callInfo.number}`,
        <PhoneOutlined
          rotate={100}
          style={{ fontSize: 15 }}
          className="text-slate-500"
        />,
        <div className="flex flex-row justify-between space-x-2">
          <span>{t("voip.call")}</span>
          {copyIcon(callInfo.number)}
        </div>,
        () => call(callInfo.number, callInfo?.id, callInfo?.familyId)
      );
      if (callInfo.id) {
        if (callInfo.uuid) {
          items.push({ type: "divider" });
          pushItem(
            `chat-${callInfo.number}`,
            <MessageOutlined
              style={{ fontSize: 14 }}
              className="text-slate-500"
            />,
            `${t("voip.chat")}`,
            () => dispatch(openDrawerChat(callInfo.uuid))
          );
        }
        items.push({ type: "divider" });
        pushItem(
          "more-info",
          <InfoCircleOutlined
            style={{ fontSize: 14 }}
            className="text-slate-500"
          />,
          `${t("voip.moreInfo")}`,
          () =>
            handleDisplayElementInfo(callInfo.name, {
              id: callInfo.id,
              familyId: callInfo.familyId,
            }),
          isGuest
        );
        pushItem(
          "vue-360",
          <GlobalOutlined
            style={{ fontSize: 14 }}
            className="text-slate-500"
          />,
          `${t("voip.view360")}`,
          () => {
            dispatch({
              type: "SET_CONTACT_HEADER_INFO",
              payload: {
                name: callInfo.name,
                id: callInfo.id,
              },
            });
            navigate(
              generateUrlToView360(callInfo.familyId, callInfo.id, "v2")
            );
          },
          isGuest
        );
      }
    } else if (
      !callInfo.id &&
      !callInfo.familyId &&
      !isGroup &&
      !isQueue &&
      !forwardingInfo &&
      callInfo.number
    ) {
      pushItem(
        `${callInfo.number}`,
        <PhoneOutlined
          rotate={100}
          style={{ fontSize: 15 }}
          className="text-slate-500"
        />,
        <div className="flex flex-row justify-between space-x-2">
          <span>{t("voip.call")}</span> {copyIcon(callInfo.number)}
        </div>,
        () => call(callInfo.number, callInfo?.id, callInfo?.familyId)
      );
      items.push({ type: "divider" });
      pushItem(
        "add",
        <MdOutlinePersonAddAlt
          style={{ fontSize: 15 }}
          className="text-slate-500"
        />,
        t("voip.addToModule"),
        null,
        isGuest,
        [
          {
            label: t("contacts.leads"),
            key: "add-leads",
            icon: (
              <CgUserlane style={{ fontSize: 15 }} className="text-slate-500" />
            ),
            onClick: () => handleAddItem(9, callInfo?.number),
          },
          {
            label: t("contacts.contact"),
            key: "add-contact",
            icon: (
              <TeamOutlined
                style={{ fontSize: 15 }}
                className="text-slate-500"
              />
            ),
            onClick: () => handleAddItem(2, callInfo?.number),
          },
          {
            label: t("contacts.company"),
            key: "add-company",
            icon: (
              <HiOutlineBuildingOffice
                style={{ fontSize: 15 }}
                className="text-slate-500"
              />
            ),
            onClick: () => handleAddItem(1, callInfo?.number),
          },
        ]
      );
    }

    return { items };
  };

  return (
    <div ref={index === 0 ? Refs_IDs.logs_dropDown_actions : null}>
      <Dropdown
        trigger={["click"]}
        placement="bottomRight"
        menu={menuDropdown()}
      >
        <FiMoreVertical className="h-[18px] w-[18px] cursor-pointer text-gray-400 hover:text-gray-700" />
      </Dropdown>
    </div>
  );
};

export default CallerColumn;
