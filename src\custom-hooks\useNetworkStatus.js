import { useEffect, useState } from "react";
import MainService from "../services/main.service";
import { useDispatch } from "react-redux";
import { store } from "../new-redux/store";
import useNetwork from "./useNetwork";
import { closeEventMerucre } from "../utils/real-time-function/chat/function";
import { pingPongFunction } from "new-redux/services/chat.services";
const useNetworkStatus = () => {
  const { isOnline } = useNetwork();
  const dispatch = useDispatch();
  const [isServerReachable, setIsServerReachable] = useState(null);
  const newAbortSignal = (timeoutMs) => {
    const abortController = new AbortController();
    const time = setTimeout(() => {
      abortController.abort();

      clearTimeout(time);
    }, timeoutMs || 0);

    return abortController.signal;
  };

  useEffect(() => {
    let mount = true;
    const pingServer = async () => {
      if (!isOnline) return;
      let latenceLocal;
      let tokenExpire = false;
      let timeout;
      let currentUser = await store.getState().chat.currentUser;
      if (currentUser && currentUser?._id) {
        let isReachable = false;
        latenceLocal = await dispatch(pingPongFunction());
        while (!isReachable) {
          try {
            newAbortSignal();
            const response = await MainService.pingServer(
              newAbortSignal(Math.floor(latenceLocal) + 6000 || 15000)
            );
            if (response?.status && response?.status === 200) {
              isReachable = true;
              setIsServerReachable(true);
              if (response?.status && response?.status === 401) {
                tokenExpire = true;
                clearTimeout(timeout);
              }
            } else if (
              response?.status &&
              (response?.status === 429 || response?.status === 404)
            ) {
              isReachable = true;
              setIsServerReachable(true);
            }
          } catch (error) {
            if (error.response && error.response?.status === 401) {
              newAbortSignal();
              clearTimeout(timeout);

              isReachable = true;
            }
            if (
              error?.response?.status &&
              (error.response.status === 429 || error.response.status === 404)
            ) {
              clearTimeout(timeout);
              isReachable = true;
              setIsServerReachable(true);
            }
            if (error?.message === "canceled") return;

            console.error("network error", error);
          }

          if (!isReachable) {
            // eslint-disable-next-line no-loop-func
            await new Promise(
              // eslint-disable-next-line no-loop-func
              (resolve) =>
                (timeout = setTimeout(
                  resolve,
                  Math.floor(latenceLocal) + 6000
                    ? Math.floor(latenceLocal) +
                        4000 +
                        100 +
                        (tokenExpire ? 4000 : 0)
                    : 4100
                ))
            );
          }
        }
      } else setIsServerReachable(true);
    };
    if (isOnline && mount) {
      mount = false;
      pingServer();
    } else {
      closeEventMerucre();
      setIsServerReachable(false);
    }
    return () => {
      mount = false;
    };
  }, [isOnline, dispatch]);

  return { isServerReachable };
};

export default useNetworkStatus;
