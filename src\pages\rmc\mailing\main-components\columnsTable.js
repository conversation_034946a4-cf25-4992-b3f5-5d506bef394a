import { Fragment, memo, useCallback, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import {
  Avatar,
  Button,
  Divider,
  Dropdown,
  Popover,
  Space,
  Tooltip,
} from "antd";
import { AiFillStar, AiOutlineStar } from "react-icons/ai";
import {
  MdDriveFileMoveOutline,
  MdLabel,
  MdLabelImportant,
  MdLabelImportantOutline,
  MdOutlineAttachment,
  MdOutlineLabel,
  MdOutlineMarkEmailUnread,
  MdRadioButtonChecked,
} from "react-icons/md";
import {
  generateUrlToView360,
  hexToRgba,
  humanDate,
  truncateString,
} from "pages/voip/helpers/helpersFunc";
import {
  ArrowDownOutlined,
  ArrowUpOutlined,
  CloseOutlined,
  MessageOutlined,
  SyncOutlined,
} from "@ant-design/icons";
import {
  EllipsisVertical,
  History,
  LucideNotebookText,
  MailX,
  Tag,
} from "lucide-react";
import { URL_ENV } from "index";
import { GiStopwatch } from "react-icons/gi";
import { toastNotification } from "components/ToastNotification";
import { TbArchiveOff, TbChecks } from "react-icons/tb";
import { AssignMail } from "./AssignMail";
import { renderIcon } from "pages/clients&users/components/RenderColumnsTable";
import { getFamilyNameById } from "pages/clients&users/FamilyRouting";
import { unAffectMailToLabel } from "../services/services";
import { PiIdentificationBadge } from "react-icons/pi";
import { IoMdCheckboxOutline } from "react-icons/io";
import { BiArchiveIn } from "react-icons/bi";
import { LuMailOpen, LuTrash2 } from "react-icons/lu";
import { CgUnblock } from "react-icons/cg";
import { conditionActions } from "./utils";
import moment from "moment";
import MainService from "services/main.service";
import ChoiceIcons from "pages/components/ChoiceIcons";
import DisplayAvatar from "pages/voip/components/DisplayAvatar";
import TicketIconSphere from "components/icons/TicketIconSphere";
import { setRefreshKpi } from "new-redux/actions/mail.actions";

export const useMailColumns = ({
  setData,
  usedAccount,
  label,
  user,
  sharedAccountUsers,
  setOpenElementInfo,
  setElementDetails,
  handleDropDown,
  search,
}) => {
  //
  const [t] = useTranslation("common");
  const navigate = useNavigate();
  const location = useLocation();
  const pathName = location.pathname;
  //
  const onlineUser = useSelector((state) => state.ChatRealTime.onlineUser);
  //
  const isSharedAccount = !!Number(usedAccount.shared);
  const isInLabel = !!label?.id;
  const isInbox = pathName.split("/").pop() === "inbox";
  const isSent = pathName.split("/").pop() === "sent";
  const folder = pathName.split("/").pop();
  //
  const openElementInfo = (id, familyId, label) => {
    setElementDetails({
      id,
      familyId,
      label,
    });
    setOpenElementInfo(true);
  };
  //
  const columns = useMemo(
    () => [
      {
        title: "Actions",
        dataIndex: "actions",
        key: "actions",
        // fixed: "left",
        width: isSharedAccount ? 110 : 80,
        render: (_, record) => (
          <div className="flex items-center space-x-1">
            {!isSent && <IsSent t={t} record={record} />}
            <ActionStar
              record={record}
              usedAccount={usedAccount}
              user={user}
              t={t}
            />
            <ActionImportant
              record={record}
              usedAccount={usedAccount}
              user={user}
              t={t}
            />
            {isSharedAccount && (
              <ActionState
                record={record}
                usedAccount={usedAccount}
                user={user}
                t={t}
              />
            )}
          </div>
        ),
      },
      {
        title: "From",
        dataIndex: "from",
        key: "from",
        width: isSharedAccount ? 270 : 240,
        ellipsis: true,
        render: (from, record) => (
          <div
            className={`relative mt-0.5 flex justify-between space-x-2	 ${
              record.seen ? "" : "font-semibold"
            }`}
          >
            <p className="truncate">
              <DisplayFrom from={from} isSearch={!!search.length} />
            </p>

            <div className="flex space-x-1.5">
              {record.nbr > 1 && (
                <span className="mt-[-1px] font-semibold text-[#5f6368]">
                  {record.nbr}
                </span>
              )}
              {!!record.room_id && (
                <OpenRoomChat
                  t={t}
                  record={record}
                  handleDropDown={handleDropDown}
                />
              )}
              <BouncedMail isBounced={record?.bounced} t={t} />
              <TransferInfo transfer={record?.transfert} t={t} />
              {isSharedAccount && <TimerMail record={record} t={t} />}
              {isSharedAccount && (
                <MailViewers viewers={record?.userId ?? []} user={user} />
              )}
              <DropDownOptionMailing
                t={t}
                usedAccount={usedAccount}
                record={record}
                isInLabel={isInLabel}
                folder={folder}
                handleDropDown={handleDropDown}
                user={user}
              />
            </div>
          </div>
        ),
      },
      ...(isSharedAccount
        ? [
            {
              title: "Assign",
              dataIndex: "owner",
              key: "owner",
              // fixed: "left",
              width: 95,
              render: (owner, record) => (
                <AssignMail
                  setData={setData}
                  owner={owner?.label_data ? owner : null}
                  onlineUser={onlineUser}
                  user={user}
                  usedAccount={usedAccount}
                  t={t}
                  id={record?.id}
                  transfer={record?.transfert}
                  dataUsers={sharedAccountUsers}
                />
              ),
            },
          ]
        : []),
      {
        title: "Subject & Body",
        dataIndex: "subject",
        key: "subject",
        ellipsis: true,
        className: "vertical-middle",
        render: (subject, record) => (
          <>
            <div className={`flex space-x-1`}>
              {isInLabel && <FolderNameInLabel folder={record?.source} t={t} />}
              <LabelTag
                usedAccount={usedAccount}
                record={record}
                label={label}
                setData={setData}
                t={t}
              />
              {!!subject ? (
                <p
                  className={`${isSharedAccount && "truncate"} text-[#202124] ${
                    !record.seen && "font-semibold"
                  }`}
                  dangerouslySetInnerHTML={{
                    __html: isSharedAccount
                      ? subject
                      : truncateString(subject, 50),
                  }}
                />
              ) : (
                t("mailing.noSubject")
              )}
              {!isSharedAccount && <span className="font-semibold">-</span>}
              {!isSharedAccount && (
                <p
                  className="truncate text-[#5f6368]"
                  dangerouslySetInnerHTML={{ __html: record.body }}
                />
              )}
              <DisplayAttachments attachments={record?.attachments} t={t} />
            </div>

            <div className="mt-1.5 flex items-center space-x-2">
              <DisplayIdentification
                record={record}
                setData={setData}
                usedAccount={usedAccount}
                user={user}
                navigate={navigate}
                openElementInfo={openElementInfo}
                handleDropDown={handleDropDown}
                t={t}
              />

              {record.affectation?._id &&
              record.identification?.identificationId ? (
                <Divider type="vertical" style={{ height: "1rem" }} />
              ) : null}

              <DisplayAffectation
                record={record}
                setData={setData}
                usedAccount={usedAccount}
                user={user}
                navigate={navigate}
                openElementInfo={openElementInfo}
                handleDropDown={handleDropDown}
                t={t}
              />

              {record.tags?.tags &&
              record.affectation?._id &&
              record.identification?.identificationId ? (
                <Divider type="vertical" style={{ height: "1rem" }} />
              ) : null}

              <DisplayTags
                record={record}
                setData={setData}
                usedAccount={usedAccount}
                handleDropDown={handleDropDown}
                user={user}
                t={t}
              />
            </div>
          </>
        ),
      },
      {
        title: "Date",
        dataIndex: "date",
        key: "date",
        // fixed: "left",
        width: 100,
        align: "right",
        render: (date, record) => (
          <p className={`${!record.seen && "font-semibold"}`}>
            <Tooltip placement="right" title={humanDate(date, t, null, true)}>
              <span className="cursor-help">
                {humanizeDate(
                  date,
                  user.location?.date_format || "DD/MM/YYYY",
                  user?.location?.default_language,
                  t
                )}
              </span>
            </Tooltip>
          </p>
        ),
      },
    ],
    [
      t,
      user,
      folder,
      isSent,
      isInLabel,
      onlineUser,
      usedAccount,
      search.length,
      isSharedAccount,
      sharedAccountUsers,
    ]
  );
  //
  return columns;
};
//
export const ActionStar = memo(({ record, usedAccount, user, t }) => {
  //
  const condition = !conditionActions(record, usedAccount, user);
  //
  const [isChecked, setIsChecked] = useState(record?.starred);
  const [isLoading, setIsLoading] = useState(false);
  //
  const starredMail = async () => {
    try {
      setIsLoading(true);
      await MainService.starredMessage(record?.id);
      setIsChecked((p) => !p);
    } catch (err) {
      toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? `error: ${err.message}` : { err });
    } finally {
      setIsLoading(false);
    }
  };
  //
  return (
    <Button
      size="small"
      type="text"
      shape="circle"
      disabled={condition}
      loading={isLoading}
      onClick={(e) => {
        e.stopPropagation();
        starredMail();
      }}
      icon={
        isChecked ? (
          <AiFillStar className={`h-5 w-5 text-yellow-400`} />
        ) : (
          <AiOutlineStar
            className={`h-5 w-5 ${
              !condition && "text-gray-500 hover:text-gray-600"
            }`}
          />
        )
      }
    />
  );
});
//
export const ActionImportant = memo(({ record, usedAccount, user, t }) => {
  //
  const condition = !conditionActions(record, usedAccount, user);
  //
  const [isChecked, setIsChecked] = useState(record?.important);
  const [isLoading, setIsLoading] = useState(false);
  //
  const importantMail = async () => {
    try {
      setIsLoading(true);
      await MainService.importantMessage(record.id);
      setIsChecked((p) => !p);
    } catch (err) {
      toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? `error: ${err.message}` : { err });
    } finally {
      setIsLoading(false);
    }
  };
  //
  return (
    <Button
      size="small"
      type="text"
      shape="circle"
      disabled={condition}
      loading={isLoading}
      onClick={(e) => {
        e.stopPropagation();
        importantMail();
      }}
      icon={
        isChecked ? (
          <MdLabelImportant className="h-5 w-5 text-gray-600" />
        ) : (
          <MdLabelImportantOutline
            className={`h-5 w-5 ${
              !condition && "text-gray-500 hover:text-gray-600"
            }`}
          />
        )
      }
    />
  );
});
//
export const ActionState = memo(({ record, usedAccount, user, t }) => {
  //
  const dispatch = useDispatch();
  //
  const condition = !conditionActions(record, usedAccount, user);

  //
  const [state, setState] = useState(record?.state ?? "new");
  const [isLoading, setIsLoading] = useState(false);
  //
  const stateToColorMap = {
    new: "rgb(107 114 128)",
    "in-progress": "#49a7e9",
    processed: "#a149e9",
    closed: "#3cca5d",
  };
  const currentColor = stateToColorMap[state];
  //
  const changeState = async (newState) => {
    try {
      setIsLoading(true);
      const formData = new FormData();
      formData.append("emailId", record.id);
      formData.append("accountId", usedAccount?.value);
      formData.append("state", newState);
      usedAccount.departmentId.length &&
        usedAccount.departmentId.forEach((id) =>
          formData.append("departement_id[]", id)
        );
      await MainService.updateStateEmail(formData);
      dispatch(setRefreshKpi(Date.now()));
      setState(newState);
    } catch (err) {
      toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? `error: ${err.message}` : { err });
    } finally {
      setIsLoading(false);
    }
  };
  //
  return (
    <div onClick={(e) => e.stopPropagation()}>
      <Dropdown
        menu={{
          items: [
            {
              label: t("mailing.status"),
              key: "title",
              type: "group",
              disabled: true,
            },
            {
              label: t("mailing.new"),
              key: "new",
              icon: (
                <MdRadioButtonChecked
                  style={{
                    fontSize: 14,
                    color: stateToColorMap.new,
                  }}
                />
              ),
            },
            {
              label: t("mailing.inProgress"),
              key: "in-progress",
              icon: (
                <MdRadioButtonChecked
                  style={{
                    fontSize: 14,
                    color: stateToColorMap["in-progress"],
                  }}
                />
              ),
            },
            {
              label: t("mailing.processed"),
              key: "processed",
              icon: (
                <MdRadioButtonChecked
                  style={{
                    fontSize: 14,
                    color: stateToColorMap.processed,
                  }}
                />
              ),
            },
            {
              label: t("mailing.closed"),
              key: "closed",
              icon: (
                <MdRadioButtonChecked
                  style={{
                    fontSize: 14,
                    color: stateToColorMap.closed,
                  }}
                />
              ),
            },
          ],
          selectable: true,
          selectedKeys: [state],
          onClick: (e) => changeState(e.key),
        }}
        trigger={["click"]}
        disabled={condition || record.source === "sent"}
      >
        <Tooltip
          title={
            record.source === "sent"
              ? false
              : `${t("mailing.status")}: ${
                  state === "new"
                    ? t("mailing.new")
                    : state === "in-progress"
                    ? t("mailing.inProgress")
                    : state === "processed"
                    ? t("mailing.processed")
                    : t("mailing.closed")
                }`
          }
        >
          <Button
            shape="circle"
            type="text"
            size="small"
            disabled={condition || record.source === "sent"}
            loading={isLoading}
            onClick={(e) => {
              e.stopPropagation();
            }}
            icon={
              <MdRadioButtonChecked
                style={{
                  color:
                    condition && state === "new"
                      ? "rgba(0, 0, 0, 0.25)"
                      : currentColor,
                  fontSize: 16,
                }}
              />
            }
          />
        </Tooltip>
      </Dropdown>
    </div>
  );
});
//
export const BouncedMail = memo(
  ({ isBounced, t }) =>
    Boolean(isBounced) && (
      <Tooltip title={t("mailing.bouncedMail")}>
        <MailX
          size={16}
          strokeWidth={2.5}
          className="cursor-help text-yellow-500"
        />
      </Tooltip>
    )
);
//
export const OpenRoomChat = memo(({ t, record, handleDropDown }) => (
  <Tooltip title={t("mailing.chatMail")}>
    <Button
      size="small"
      type="link"
      shape="circle"
      onClick={(e) => {
        e.stopPropagation();
        handleDropDown({
          action: "chat",
          mailId: record.key,
          thirdId: record.third_id,
          record,
        });
      }}
      icon={<MessageOutlined style={{ fontSize: 14, marginTop: -6 }} />}
    />
  </Tooltip>
));
//
export const TimerMail = memo(({ record, t }) =>
  !!record?.chrono_now ? (
    <div onClick={(e) => e.stopPropagation()}>
      <Tooltip
        title={
          <div className="flex flex-col space-y-1">
            <div className="flex justify-between space-x-1.5">
              <span className="font-semibold">{t("mailing.start")}:</span>
              <span>{humanDate(record.chrono_now, t)}</span>
            </div>

            <div className="flex justify-between space-x-1">
              <span className="font-semibold">{t("mailing.end")}:</span>
              <span>{humanDate(record.expire_Processing_Time, t)}</span>
            </div>
          </div>
        }
      >
        <GiStopwatch
          className={` cursor-help ${
            record?.status_processing_duration === 1
              ? ` text-[#ba1435]`
              : record?.status_processing_duration === 0
              ? ` text-[#34bc44]`
              : ` cursor-default`
          }`}
          style={{ fontSize: 16 }}
        />
      </Tooltip>
    </div>
  ) : null
);
//
export const IsSent = memo(({ record, t }) => (
  <div onClick={(e) => e.stopPropagation()} className=" mt-0.5">
    <Tooltip
      title={
        record.box === 0 || record.box === 2
          ? t("mailing.NewMsg.received")
          : t("mailing.NewMsg.sent")
      }
    >
      {record.box === 0 || record.box === 2 ? (
        <ArrowDownOutlined
          style={{ color: "green", fontSize: 14, cursor: "help" }}
        />
      ) : (
        <ArrowUpOutlined
          style={{ color: "blue", fontSize: 14, cursor: "help" }}
        />
      )}
    </Tooltip>
  </div>
));
//
export const FolderNameInLabel = memo(({ folder, t }) => (
  <span className="inline-flex items-center rounded bg-[#ddd] px-1 py-px text-xs font-medium text-[#666] ">
    {t(`mailing.${folder}`)}
  </span>
));
//
export const TransferInfo = memo(({ transfer, t }) => {
  //
  if (!transfer) return null;
  //
  const message = (
    <div
      className="relative font-semibold"
      onClick={(e) => e.stopPropagation()}
    >
      <p>
        {t("mailing.movedBy")}:{" "}
        <span className="font-normal">{transfer?.label_data_owner}</span>
      </p>
      <p>
        {t("voip.from")}:{" "}
        <span className="font-normal">{transfer?.email_source}</span>
      </p>
      <p>
        {t("voip.to")}: <span className="font-normal">{transfer?.email}</span>
      </p>
      <p>
        Date:{" "}
        <span className="font-normal">
          {humanDate(transfer?.date_transfert, t, "table", true)}
        </span>
      </p>
    </div>
  );
  //
  return (
    <div onClick={(e) => e.stopPropagation()}>
      <Tooltip title={message}>
        <MdDriveFileMoveOutline
          className="cursor-help text-[#1677FF]"
          style={{ fontSize: 18 }}
        />
      </Tooltip>
    </div>
  );
});
//
export const MailViewers = memo(({ viewers, user }) => {
  //
  if (!viewers?.length) return null;
  //
  const viewersList = viewers.map((viewer, i) => (
    <DisplayAvatar
      key={i}
      size={26}
      cursor="help"
      tooltip={viewer?.user_id === user?.id ? "me" : true}
      name={viewer?.label_data}
      urlImg={
        !!viewer?.avatar &&
        `${URL_ENV?.REACT_APP_BASE_URL + URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL}${
          viewer?.avatar
        }`
      }
    />
  ));
  //
  const displayViewers = (
    <Avatar.Group
      maxCount={5}
      size={26}
      maxStyle={{
        backgroundColor: "rgb(30, 64, 175)",
        color: "rgb(219, 234, 254)",
        fontWeight: 600,
      }}
      style={{
        display: "flex",
        gap: 8,
      }}
      maxPopoverTrigger="click"
    >
      {viewersList}
    </Avatar.Group>
  );
  //
  return (
    <div onClick={(e) => e.stopPropagation()}>
      <Tooltip arrow={false} title={displayViewers} color="white">
        <TbChecks className="h-4 w-4 cursor-help text-[#1677FF]" />
      </Tooltip>
    </div>
  );
});
//
export const LabelTag = memo(({ usedAccount, record, label, setData, t }) => {
  const [loading, setLoading] = useState(false);
  //
  const labels = record?.labelEmail || [];
  if (!labels.length) return null;
  else if (label?.id && labels.length < 2) return null;
  else {
    //
    const usedLabels = usedAccount?.labels || [];
    const usedLabelsLookup = usedLabels.reduce((acc, used) => {
      acc[used.id] = used;
      return acc;
    }, {});
    //
    const labelsToDisplay = labels
      .filter((labelItem) => labelItem.id !== label?.id)
      .map((labelItem) => {
        const matchingUsedLabel = usedLabelsLookup[labelItem.id];
        return {
          ...labelItem,
          label: matchingUsedLabel?.label || labelItem.label,
          color: matchingUsedLabel?.color || labelItem.color,
        };
      });
    //
    return labelsToDisplay.map((item) => (
      <div
        key={item.id}
        className={`relative inline-flex items-center space-x-0.5  rounded px-1 py-px text-xs font-medium`}
        style={{
          backgroundColor: hexToRgba(item.color, 0.1),
          color: item.color,
        }}
      >
        <MdLabel
          style={{
            fontSize: 16,
            color: item.color || "rgb(107 114 128)",
          }}
        />
        <span>{truncateString(item?.label, 20, true)}</span>
        <Tooltip key="delete_label" title={t("voip.delete")}>
          <Button
            size="small"
            type="text"
            loading={item.id === loading}
            icon={<CloseOutlined style={{ color: item.color || "" }} />}
            style={{ width: 20, height: 18 }}
            onClick={(e) => {
              e.stopPropagation();
              deleteLabelFromMail(
                usedAccount.value,
                item.id,
                record.third_id,
                t,
                setData,
                setLoading
              );
            }}
          />
        </Tooltip>
      </div>
    ));
  }
  //
});
//
export const DisplayAttachments = memo(({ attachments, t }) => {
  //
  if (!attachments || !attachments?.length) return null;
  //
  return (
    <div className="flex" onClick={(e) => e.stopPropagation()}>
      <Tooltip
        title={
          t("mailing.containsAttch")
          // <span
          //   dangerouslySetInnerHTML={{
          //     __html: t("mailing.emailAttachment", {
          //       nbr: attachments.length,
          //     }),
          //   }}
          // />
        }
      >
        <MdOutlineAttachment
          className="cursor-help text-blue-600"
          style={{ fontSize: 20 /*color: "rgb(32,33,36)"*/ }}
        />
      </Tooltip>
    </div>
  );
});
//
export const DropDownOptionMailing = memo(
  ({ t, usedAccount, record, isInLabel, folder, handleDropDown, user }) => {
    //
    const currentFolder = isInLabel ? record?.source : folder;
    const isSeen = record.seen;
    const DisableCondition = !conditionActions(record, usedAccount, user);
    //

    const itemLabels = useMemo(() => {
      if (!usedAccount?.labels?.length) return [];
      const labels = usedAccount?.labels.map((label) => ({
        key: `labels--${label.id}`,
        label: truncateString(label.label, 20, false),
        icon: (
          <MdLabel
            style={{ fontSize: 16, color: label?.color || "rgb(107 114 128)" }}
          />
        ),
        disabled:
          record?.labelEmail?.length &&
          record?.labelEmail?.find((e) => e.label === label.label),
      }));
      const item = {
        key: "labels",
        label: (
          <span onClick={(e) => e.stopPropagation()}>
            {t("mailing.labelAs")}
          </span>
        ),
        icon: (
          <MdOutlineLabel style={{ fontSize: 18, color: "rgb(148 163 184)" }} />
        ),
        children: labels,
        // onClick: (e) => {
        //   e.domEvent.stopPropagation();
        //   dispatch(makeAsLabel(usedAccount.value, e.key, record.third_id, t));
        // },
      };
      return [item];
    }, [record?.labelEmail, t, usedAccount?.labels]);
    //
    const iconStyling = { fontSize: 16, color: "rgb(148, 163, 184)" };
    //
    const items = useMemo(
      () => [
        ...(currentFolder === "inbox"
          ? [
              {
                key: "identify",
                label: t("mailing.identifier"),
                icon: <PiIdentificationBadge style={iconStyling} />,
                disabled:
                  (currentFolder === "inbox" && DisableCondition) ||
                  record?.identification?.identificationId ||
                  (record?.nbr > 1 &&
                    record?.from_Thread?.[0]?.identification?.label_data),
              },
            ]
          : []),
        ...(currentFolder === "inbox" || currentFolder === "sent"
          ? [
              {
                key: "affect",
                label: t("mailing.Affect"),
                icon: <IoMdCheckboxOutline style={iconStyling} />,
                disabled:
                  (currentFolder === "inbox" && DisableCondition) ||
                  record.affectation?.affect_label,
              },
              {
                key: "convert",
                label: t("mailing.convertTo"),
                icon: <SyncOutlined style={{ ...iconStyling, fontSize: 14 }} />,
                disabled:
                  (currentFolder === "inbox" && DisableCondition) ||
                  record.affectation?.affect_label,
                children: [
                  {
                    key: "convert-6",
                    label: t("contacts.ticket"),
                    icon: (
                      <TicketIconSphere size={18} color="rgb(148, 163, 184)" />
                    ),
                  },
                ],
              },
              {
                key: "qualify",
                label: t("mailing.qualify"),
                icon: <Tag size={16} strokeWidth={2.5} style={iconStyling} />,
                disabled:
                  (currentFolder === "inbox" && DisableCondition) ||
                  record.tags?.tags?.length > 0,
              },
              ...(currentFolder === "inbox"
                ? [
                    {
                      key: "move",
                      label: t("mailing.move"),
                      icon: (
                        <MdDriveFileMoveOutline
                          style={{ ...iconStyling, fontSize: 17 }}
                        />
                      ),
                      disabled:
                        DisableCondition ||
                        record.transfert?.account_id ||
                        record?.owner?.label_data,
                    },
                  ]
                : []),
              ...(Number(usedAccount.shared)
                ? [
                    {
                      key: "chat",
                      label: (
                        <Tooltip
                          placement="right"
                          title={t("mailing.roomAlreadyExist")}
                        >
                          {t("menu1.chat")}
                        </Tooltip>
                      ),
                      icon: (
                        <MessageOutlined
                          style={{ ...iconStyling, fontSize: 14 }}
                        />
                      ),
                      disabled: record?.room_id,
                    },
                  ]
                : []),
              {
                type: "divider",
              },
            ]
          : []),

        ...itemLabels,
        {
          key: "historic",
          label: t("mailing.Historique"),
          icon: <History size={16} style={{ ...iconStyling }} />,
          disabled: false,
        },
        ...(!Number(usedAccount.shared)
          ? [
              {
                key: isSeen ? "makeUnread" : "makeRead",
                label: isSeen ? t("mailing.markUnread") : t("mailing.markRead"),
                icon: isSeen ? (
                  <MdOutlineMarkEmailUnread style={iconStyling} />
                ) : (
                  <LuMailOpen style={iconStyling} />
                ),
                disabled: false,
              },
            ]
          : []),
        ...(currentFolder !== "archive" && currentFolder !== "trash"
          ? [
              {
                key: "archive",
                label: t("import.archiveAction"),
                icon: <BiArchiveIn style={iconStyling} />,
                disabled: false,
              },
            ]
          : []),
        ...(currentFolder === "archive"
          ? [
              {
                key: "unarchive",
                label: t("mailing.unarchive"),
                icon: <TbArchiveOff style={iconStyling} />,
                disabled: false,
              },
            ]
          : []),
        // {
        //   key: "span",
        //   label: t("mailing.markSpam"),
        //   icon: <PiWarningOctagonBold style={iconStyling} />,
        //   disabled: false,
        // },
        ...(currentFolder === "span"
          ? [
              {
                key: "unSpam",
                label: t("mailing.notSpam"),
                icon: <CgUnblock style={{ ...iconStyling, fontSize: 17 }} />,
                disabled: false,
              },
            ]
          : []),
        ...(currentFolder !== "trash"
          ? [
              {
                key: "delete",
                label: t("voip.delete"),
                icon: <LuTrash2 style={{ fontSize: 16 }} />,
                disabled: false,
                danger: true,
              },
            ]
          : []),
      ],
      [
        currentFolder,
        isSeen,
        record,
        itemLabels,
        t,
        DisableCondition,
        usedAccount.shared,
      ]
    );
    //
    return (
      <div onClick={(e) => e.stopPropagation()}>
        <Dropdown
          overlayStyle={{ minWidth: items?.length > 2 ? "7rem" : "" }}
          trigger={["click"]}
          placement="bottomRight"
          arrow={false}
          menu={{
            items,
            onClick: (e) =>
              handleDropDown({
                action: e.key,
                mailId: record.key,
                thirdId: record.third_id,
                record,
              }),
          }}
        >
          <EllipsisVertical
            size={19}
            className="cursor-pointer  text-gray-400  hover:text-gray-800"
          />
        </Dropdown>
      </div>
    );
  }
);
//
export function humanizeDate(
  date,
  userDateFormat = "DD/MM/YYYY",
  locale = "en",
  t
) {
  moment.locale(locale);

  const parseFormats = [
    `${userDateFormat} HH:mm`,
    userDateFormat,
    moment.ISO_8601,
  ];
  const m = moment(date, parseFormats, true);
  if (!m.isValid()) {
    return typeof date === "string" ? date : m.toString();
  }

  const now = moment();

  if (m.isSame(now, "day")) {
    return m.format("HH:mm");
  }

  if (m.isSame(now.clone().subtract(1, "day"), "day")) {
    return t("voip.yesterday");
  }

  if (m.year() === now.year()) {
    return locale === "fr" ? m.format("D MMM") : m.format("MMM D");
  }

  return m.format(userDateFormat);
}

//
export const DisplayIdentification = memo(
  ({
    record,
    setData,
    usedAccount,
    user,
    openElementInfo,
    handleDropDown,
    navigate,
    t,
  }) => {
    //
    const identification = record.identification;
    const [loading, setLoading] = useState(false);
    const [openToolt, setOpenToult] = useState(false);
    //
    if (!identification?.identificationId) return null;
    //
    const canDeleteUpdate = identification
      ? !(
          record.transfert?.account_id &&
          record.transfert?.account_id !== usedAccount?.value
        ) && !(record.owner?.owner && record.owner?.owner !== user.id)
      : false;
    //
    const handleDeleteIdentification = async () => {
      if (!identification?.identificationId) return;
      try {
        setLoading(true);
        const formData = new FormData();
        formData.append("type", "email");
        formData.append("emailId", record.id);
        formData.append("account_id", usedAccount?.value);
        usedAccount.departmentId.forEach((id) =>
          formData.append("departement_id[]", id)
        );
        formData.append("identificationId", identification.identificationId);
        await MainService.deleteIdentification(formData);
        setData((prev) =>
          prev.map((item) =>
            item.id === record.id ? { ...item, identification: null } : item
          )
        );
      } catch (err) {
        if (err?.response?.status !== 401) {
          toastNotification("error", t("toasts.somethingWrong"), "topRight");
        }
        throw new Error(err?.message || err);
      } finally {
        setLoading(false);
      }
    };
    //
    const tooltipContent = (
      <div className="space-y-0.5">
        <div className="flex flex-nowrap space-x-1 whitespace-nowrap">
          <span>{t("mailing.identifiedAs")}</span>
          <span className="font-semibold">
            {`${identification.label_data} ${
              familyWithAvatar[identification.family_id]
                ? `(${getFamilyNameById(t, Number(identification.family_id))})`
                : ""
            }`}
          </span>
        </div>

        <div className="flex justify-end">
          <Space size={3} split={<Divider type="vertical" />}>
            {canDeleteUpdate && (
              <Button
                key="edit"
                size="small"
                type="link"
                style={{ paddingLeft: 0, paddingRight: 0 }}
                onClick={() => {
                  handleDropDown({
                    action: "identify",
                    mailId: record.key,
                    thirdId: record.third_id,
                    record,
                  });
                  setOpenToult(false);
                }}
              >
                {t("contacts.edit")}
              </Button>
            )}
            <Button
              key="more_info"
              size="small"
              type="link"
              style={{ paddingLeft: 0, paddingRight: 0 }}
              onClick={() => {
                openElementInfo(
                  identification.id,
                  identification.family_id,
                  identification.label_data
                );
                setOpenToult(false);
              }}
            >
              {t("voip.moreInfo")}
            </Button>
            <Button
              key="view_sphere"
              size="small"
              type="link"
              style={{ paddingLeft: 0, paddingRight: 0 }}
              onClick={() =>
                navigate(
                  generateUrlToView360(
                    identification.family_id,
                    identification.id,
                    "v2"
                  )
                )
              }
            >
              {t("voip.view360")}
            </Button>
          </Space>
        </div>
      </div>
    );
    //
    return (
      <div
        key={identification.identificationId}
        className="inline-flex cursor-help items-center space-x-1 rounded-md bg-gray-500/10 px-1 py-0.5 text-xs font-semibold  text-gray-600 "
        onClick={(e) => e.stopPropagation()}
      >
        <Tooltip
          key="update_identification"
          arrow={false}
          overlayInnerStyle={{ width: "fit-content" }}
          title={tooltipContent}
          placement="topLeft"
          open={openToolt}
          onOpenChange={setOpenToult}
        >
          <div className="flex items-center space-x-1">
            <DisplayAvatar
              size={20}
              name={identification.label_data}
              urlImg={
                identification.avatar &&
                `${
                  URL_ENV.REACT_APP_BASE_URL +
                  URL_ENV.REACT_APP_SUFFIX_AVATAR_URL
                }${identification.avatar}`
              }
              icon={renderIcon(Number(identification.family_id), 14)}
            />
            <span>{truncateString(identification.label_data, 18)}</span>
          </div>
        </Tooltip>
        {canDeleteUpdate && (
          <Tooltip key="delete_identification" title={t("voip.delete")}>
            <Button
              size="small"
              type="text"
              loading={loading}
              icon={<CloseOutlined />}
              style={{ width: 20, height: 18 }}
              onClick={handleDeleteIdentification}
            />
          </Tooltip>
        )}
      </div>
    );
  }
);
//
export const DisplayAffectation = memo(
  ({
    record,
    setData,
    usedAccount,
    user,
    openElementInfo,
    handleDropDown,
    navigate,
    t,
  }) => {
    //
    const affectation = record.affectation;
    //
    const [loading, setLoading] = useState(false);
    const [openToolt, setOpenToult] = useState(false);
    //
    if (!affectation?._id) return null;
    //
    const canDeleteUpdate =
      !(
        record.transfert?.account_id &&
        record.transfert?.account_id !== usedAccount?.value
      ) && !(record.owner?.owner && record.owner?.owner !== user.id);
    //
    const handleDeleteIdentification = async () => {
      if (!affectation?._id) return;
      try {
        setLoading(true);
        const formData = new FormData();
        formData.append("type", "email");
        formData.append("account_id", usedAccount?.value);
        usedAccount.departmentId.forEach((id) =>
          formData.append("departement_id[]", id)
        );
        formData.append("id", affectation._id);
        await MainService.deleteAffectation(formData);
        setData((prev) =>
          prev.map((item) =>
            item.id === record.id ? { ...item, affectation: null } : item
          )
        );
      } catch (err) {
        if (err?.response?.status !== 401) {
          toastNotification("error", t("toasts.somethingWrong"), "topRight");
        }
        throw new Error(err?.message || err);
      } finally {
        setLoading(false);
      }
    };
    //
    const tooltipContent = (
      <div className="space-y-0.5">
        <div className="flex flex-nowrap space-x-1 whitespace-nowrap">
          <span>{t("mailing.affectTo")}</span>
          <span className="font-semibold">
            {`${affectation.affect_label} ${
              familyWithAvatar[affectation.family_id]
                ? `(${getFamilyNameById(t, Number(affectation.family_id))})`
                : ""
            }`}
          </span>
        </div>

        <div className="flex justify-end">
          <Space size={3} split={<Divider type="vertical" />}>
            {canDeleteUpdate && (
              <Button
                key="edit"
                size="small"
                type="link"
                style={{ paddingLeft: 0, paddingRight: 0 }}
                onClick={() => {
                  handleDropDown({
                    action: "affect",
                    mailId: record.key,
                    thirdId: record.third_id,
                    record,
                  });
                  setOpenToult(false);
                }}
              >
                {t("contacts.edit")}
              </Button>
            )}
            <Button
              key="more_info"
              size="small"
              type="link"
              style={{ paddingLeft: 0, paddingRight: 0 }}
              onClick={() => {
                openElementInfo(
                  affectation.affect_to,
                  affectation.family_id,
                  affectation.affect_label
                );
                setOpenToult(false);
              }}
            >
              {t("voip.moreInfo")}
            </Button>
            <Button
              key="view_sphere"
              size="small"
              type="link"
              style={{ paddingLeft: 0, paddingRight: 0 }}
              onClick={() =>
                navigate(
                  generateUrlToView360(
                    affectation.family_id,
                    affectation.affect_to,
                    "v2"
                  )
                )
              }
            >
              {t("voip.view360")}
            </Button>
          </Space>
        </div>
      </div>
    );
    //
    return (
      <div
        key={affectation._id}
        className="inline-flex cursor-help items-center space-x-1 rounded-md bg-gray-500/10 px-1 py-0.5 text-xs font-semibold  text-gray-600 "
        onClick={(e) => e.stopPropagation()}
      >
        <Tooltip
          key="update_identification"
          arrow={false}
          overlayInnerStyle={{ width: "fit-content" }}
          title={tooltipContent}
          placement="topLeft"
          open={openToolt}
          onOpenChange={setOpenToult}
        >
          <div className="flex items-center space-x-1">
            <DisplayAvatar
              size={20}
              name={affectation.affect_label}
              urlImg={
                affectation.avatar &&
                `${
                  URL_ENV.REACT_APP_BASE_URL +
                  URL_ENV.REACT_APP_SUFFIX_AVATAR_URL
                }${affectation.avatar}`
              }
              icon={renderIcon(Number(affectation.family_id), 14)}
            />
            <span>{truncateString(affectation.affect_label, 18)}</span>
          </div>
        </Tooltip>
        {canDeleteUpdate && (
          <Tooltip key="delete_identification" title={t("voip.delete")}>
            <Button
              size="small"
              type="text"
              loading={loading}
              icon={<CloseOutlined />}
              style={{ width: 20, height: 18 }}
              onClick={handleDeleteIdentification}
            />
          </Tooltip>
        )}
      </div>
    );
  }
);
//
export const DisplayTags = memo(
  ({ record, setData, usedAccount, handleDropDown, user, t }) => {
    //
    const [loading, setLoading] = useState(false);
    const [openToolt, setOpenToult] = useState(false);

    const tags = record.tags?.tags;
    if (!tags?.length) return null;

    const note = record.tags?.note;
    const [firstTag, ...remainingTags] = tags;

    const canDeleteUpdate = tags
      ? !(
          record.transfert?.account_id &&
          record.transfert?.account_id !== usedAccount?.value
        ) && !(record.owner?.owner && record.owner?.owner !== user.id)
      : false;

    const deleteQualification = async () => {
      setLoading(true);
      try {
        const formData = new FormData();
        formData.append("id", record?.tags?._id);
        formData.append("type", "email");
        formData.append("account_id", usedAccount?.value);
        usedAccount?.departmentId?.forEach((e) =>
          formData.append("departement_id[]", e)
        );
        await MainService.deleteTags(formData);
        setData((prev) =>
          prev.map((item) => {
            if (item.key === record.key)
              return {
                ...item,
                tags: [],
              };
            return item;
          })
        );
        toastNotification("success", t("voip.tagDelete"), "topRight");
      } catch (err) {
        err?.response?.status !== 401 &&
          toastNotification("error", t("toasts.somethingWrong"), "topRight");
      } finally {
        setLoading(false);
      }
    };

    const renderTag = (tag, firstTag = false) => (
      <div
        key={tag.id}
        className={`inline-flex ${
          canDeleteUpdate && firstTag ? "cursor-help" : "cursor-default"
        } items-center space-x-1 rounded-md px-1 py-0.5 text-xs font-semibold`}
        style={{
          backgroundColor: hexToRgba(tag.color, 0.1),
          color: tag.color,
        }}
      >
        {tag.icon && (
          <DisplayAvatar
            size={20}
            icon={<ChoiceIcons icon={tag.icon} fontSize={16} />}
          />
        )}
        <span>{truncateString(tag?.label, 20, true)}</span>
      </div>
    );

    const remainingTagsContent = remainingTags?.length > 0 && (
      <div className="flex items-center space-x-1.5">
        {remainingTags.map(renderTag)}
      </div>
    );

    const tooltipContent = canDeleteUpdate ? (
      <Space size={3} split={<Divider type="vertical" />}>
        <Button
          key="edit"
          size="small"
          type="link"
          style={{ paddingLeft: 0, paddingRight: 0 }}
          onClick={() => {
            handleDropDown({
              action: "qualify",
              mailId: record.key,
              thirdId: record.third_id,
              record,
            });
            setOpenToult(false);
          }}
        >
          {t("contacts.edit")}
        </Button>
        <Button
          danger
          key="delete"
          size="small"
          type="link"
          style={{ paddingLeft: 0, paddingRight: 0 }}
          onClick={deleteQualification}
          loading={loading}
        >
          {t("voip.delete")}
        </Button>
      </Space>
    ) : (
      false
    );

    return (
      <div className="flex space-x-0" onClick={(e) => e.stopPropagation()}>
        {firstTag && (
          <Tooltip
            key="qualify"
            arrow={false}
            overlayInnerStyle={{ width: "fit-content" }}
            title={tooltipContent}
            placement="topLeft"
            open={canDeleteUpdate && openToolt}
            onOpenChange={setOpenToult}
          >
            {renderTag(firstTag, true)}
          </Tooltip>
        )}

        {remainingTags?.length > 0 && (
          <Popover
            title={false}
            arrow={false}
            content={remainingTagsContent}
            placement="bottomRight"
          >
            <div
              className="inline-flex cursor-help items-center rounded-md bg-gray-500/10 px-1 text-xs font-semibold text-gray-600"
              style={{ backgroundColor: hexToRgba(null, 0.1) }}
            >
              +{remainingTags.length}...
            </div>
          </Popover>
        )}

        {note && (
          <Popover
            title={t("voip.qualifNote")}
            content={note}
            arrow={false}
            placement="bottomRight"
            overlayStyle={{ maxWidth: "17rem" }}
          >
            <LucideNotebookText
              size={17}
              style={{ margin: "3px 0 0 6px" }}
              className=" mt-0.5 cursor-help text-blue-600"
            />
          </Popover>
        )}
      </div>
    );
  }
);
//
export const familyWithAvatar = {
  1: "company",
  2: "contact",
  4: "user",
  9: "leads",
};
//
export const PopoverFrom = memo(({ children, item, navigate, i, t }) => {
  const baseUrl = `${URL_ENV?.REACT_APP_BASE_URL}${URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL}`;
  const name =
    item?.identification?.label_data ||
    item?.name ||
    item?.address?.split("@")?.[0];

  const handleView360 = useCallback(() => {
    if (item?.identification?.family_id && item?.identification?.id) {
      navigate(
        generateUrlToView360(
          item.identification.family_id,
          item.identification.id,
          "v2"
        )
      );
    }
  }, [item, navigate]);

  const content = (
    <div className="min-w-[200px]">
      <div className="flex gap-2">
        <DisplayAvatar
          size={40}
          name={name}
          urlImg={
            item?.identification?.avatar &&
            `${baseUrl}${item?.identification?.avatar}`
          }
        />
        <div>
          <p
            className="text-base font-semibold"
            dangerouslySetInnerHTML={{ __html: name }}
          />
          <p
            className="text-sm text-gray-600"
            dangerouslySetInnerHTML={{ __html: item?.address }}
          />
        </div>
      </div>
      {item?.identification?.id && item?.identification?.family_id && (
        <>
          <Divider className="my-2 bg-gray-300" />
          <Button
            type="link"
            size="small"
            onClick={handleView360}
            className="p-0"
          >
            {t("voip.view360")}
          </Button>
        </>
      )}
    </div>
  );

  return (
    <span onClick={(e) => e.stopPropagation()}>
      <Popover
        key={`popover-${i}`}
        arrow={false}
        content={content}
        overlayInnerStyle={{ padding: 12 }}
        destroyTooltipOnHide
        placement="topLeft"
      >
        <span className="cursor-help">{children}</span>
      </Popover>
    </span>
  );
});

export const DisplayFrom = ({ from, isSearch }) => {
  const [t] = useTranslation("common");
  const navigate = useNavigate();

  if (!Array.isArray(from)) return isSearch ? [] : "";

  if (isSearch) {
    const [item] = from;
    return (
      <PopoverFrom key="search" i={1} t={t} item={item} navigate={navigate}>
        <span
          dangerouslySetInnerHTML={{
            __html: item.address?.includes("<mark>")
              ? item.address
              : item.name || item.address,
          }}
        />
      </PopoverFrom>
    );
  }

  if (from.length === 1) {
    const [item] = from;
    return (
      <PopoverFrom key="single" i={1} t={t} item={item} navigate={navigate}>
        <span
          dangerouslySetInnerHTML={{
            __html: [item?.name, item?.address].filter(Boolean)[0] || "",
          }}
        />
      </PopoverFrom>
    );
  }

  return (
    <p className="truncate">
      {from.map((item, index) => (
        <Fragment key={`item-${index}`}>
          <PopoverFrom i={index} t={t} item={item} navigate={navigate}>
            <span
              dangerouslySetInnerHTML={{
                __html:
                  item?.name?.split(" ")[0] ||
                  item?.address?.split("@")[0] ||
                  "",
              }}
            />
          </PopoverFrom>
          {index < from.length - 1 && ", "}
        </Fragment>
      ))}
    </p>
  );
};
//
// used Func <---------------->
export const deleteLabelFromMail = async (
  accountId,
  idLabel,
  thirdId,
  t,
  setData,
  setLoading = () => {}
) => {
  try {
    setLoading(idLabel);
    const formData = new FormData();
    formData.append("account_id", accountId);
    formData.append("id", idLabel);
    formData.append("third_id[]", thirdId);
    await unAffectMailToLabel(formData);
    if (setData) {
      setData((prev) =>
        prev.map((mail) => {
          if (mail.third_id === thirdId) {
            return {
              ...mail,
              labelEmail: mail.labelEmail.filter(
                (label) => label.id !== idLabel
              ),
            };
          } else return mail;
        })
      );
    }
  } catch (error) {
    toastNotification(
      "error",
      error?.response?.data
        ? error?.response?.data?.message
        : t("toasts.errorFetchApi"),
      "topRight",
      4
    );
    throw new Error(error);
  } finally {
    setLoading(false);
  }
};
