import {
  CREATE_NEW_GROUP_SUCCESS,
  CREATE_NEW_GROUP_ERROR,
  CREATE_GROUP_LOADING,
} from "../../constants";
import MainService from "../../../services/main.service";

export const createNewGroup = (payload) => async (dispatch) => {
  try {
    dispatch({ type: CREATE_GROUP_LOADING });
    const response = await MainService.handleCreateNewGroup(payload);
    dispatch({
      type: CREATE_NEW_GROUP_SUCCESS,
      payload: response?.data?.data,
    });
  } catch (error) {
    if (error?.status !== 401) {
      dispatch({
        type: CREATE_NEW_GROUP_ERROR,
        payload: error,
      });
    }
  }
};
