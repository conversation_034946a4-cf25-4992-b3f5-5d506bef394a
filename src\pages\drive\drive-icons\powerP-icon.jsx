import React from "react";

const PowerPointIcon = ({ width = "50px", height = "50px", ...props }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      width={width}
      height={height}
      viewBox="0 0 32 32"
      {...props}
    >
      <defs>
        <linearGradient
          id="a"
          x1="4.494"
          y1="-2092.086"
          x2="13.832"
          y2="-2075.914"
          gradientTransform="translate(0 2100)"
          gradientUnits="userSpaceOnUse"
        >
          <stop offset="0" stopColor="#ca4a2f" />
          <stop offset="0.5" stopColor="#b63525" />
          <stop offset="1" stopColor="#a02516" />
        </linearGradient>
      </defs>
      <title>file_type_powerpoint</title>
      <path
        d="M19.581,15.35,8.512,13.4V27.809A1.192,1.192,0,0,0,9.705,29h19.1A1.192,1.192,0,0,0,30,27.809h0V22.5Z"
        style={{ fill: "#ca4a2f" }}
      />
      <path
        d="M19.581,3H9.705A1.192,1.192,0,0,0,8.512,4.191h0V9.5L19.581,16l5.861,1.95L30,16V9.5Z"
        style={{ fill: "#e86e58" }}
      />
      <path d="M8.512,9.5H19.581V16H8.512Z" style={{ fill: "#ca4a2f" }} />
      <path
        d="M16.434,8.2H8.512V24.45h7.922a1.2,1.2,0,0,0,1.194-1.191V9.391A1.2,1.2,0,0,0,16.434,8.2Z"
        style={{ opacity: 0.10000000149011612, isolation: "isolate" }}
      />
      <path
        d="M15.783,8.85H8.512V25.1h7.271a1.2,1.2,0,0,0,1.194-1.191V10.041A1.2,1.2,0,0,0,15.783,8.85Z"
        style={{ opacity: 0.20000000298023224, isolation: "isolate" }}
      />
      <path
        d="M15.783,8.85H8.512V23.8h7.271a1.2,1.2,0,0,0,1.194-1.191V10.041A1.2,1.2,0,0,0,15.783,8.85Z"
        style={{ opacity: 0.20000000298023224, isolation: "isolate" }}
      />
      <path
        d="M15.132,8.85H8.512V23.8h6.62a1.2,1.2,0,0,0,1.194-1.191V10.041A1.2,1.2,0,0,0,15.132,8.85Z"
        style={{ opacity: 0.20000000298023224, isolation: "isolate" }}
      />
      <path
        d="M3.194,8.85H15.132a1.193,1.193,0,0,1,1.194,1.191V21.959a1.193,1.193,0,0,1-1.194,1.191H3.194A1.192,1.192,0,0,1,2,21.959V10.041A1.192,1.192,0,0,1,3.194,8.85Z"
        style={{ fill: "#d35230" }}
      />
      <path
        d="M6.5,12.127h2.747c1.4,0,2.35.308,2.85.923a3.364,3.364,0,0,1,.75,2.308,3.175,3.175,0,0,1-.825,2.3c-.55.6-1.5.9-2.85.9H7.75v2.315H6.5Zm1.25,4.58h1.4c.7,0,1.2-.15,1.5-.45a1.7,1.7,0,0,0,.45-1.25,1.6,1.6,0,0,0-.45-1.2c-.3-.3-.8-.45-1.5-.45H7.75Z"
        style={{ fill: "#fff" }}
      />
      <path
        d="M28.806,3H19.581V9.5H30V4.191A1.192,1.192,0,0,0,28.806,3Z"
        style={{ fill: "#f4b2a7" }}
      />
      <path d="M19.581,16H30v6.5H19.581Z" style={{ fill: "#ca4a2f" }} />
    </svg>
  );
};

export default PowerPointIcon;
