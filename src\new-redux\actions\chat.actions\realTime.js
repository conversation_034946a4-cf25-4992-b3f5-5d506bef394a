import {
  SET_EVENT_MERCURE,
  SET_ONLINE_USER,
  SET_LATENCE_TIMEOUT,
  GET_VISIO_EVENT_ID,
} from "../../constants";

export const setEventMercure = (payload) => ({
  type: SET_EVENT_MERCURE,
  payload,
});

export const setOnlineUser = (payload) => ({
  type: SET_ONLINE_USER,
  payload,
});

export const setLatenceTimeout = (payload) => ({
  type: SET_LATENCE_TIMEOUT,
  payload,
});

export const setVisioEventId = (payload) => ({
  type: GET_VISIO_EVENT_ID,
  payload,
});
