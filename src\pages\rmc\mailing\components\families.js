import { Space } from "antd";
import { AiOutlineShoppingCart } from "react-icons/ai";
import { CgUserlane } from "react-icons/cg";
import { FiUsers } from "react-icons/fi";
import { HiOutlineBuildingOffice, HiOutlineUserGroup } from "react-icons/hi2";
import { LuPalmtree } from "react-icons/lu";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { Blocks, HeartHandshake } from "lucide-react";
import TicketIconSphere from "components/icons/TicketIconSphere";
const useFamiliesOption = () => {
  const { user } = useSelector(({ user }) => user);
  const access = user.access || {};
  const [t] = useTranslation("common");

  const familiesOption = [
    ...(access?.companies === "1"
      ? [
          {
            label: (
              <Space>
                <HiOutlineBuildingOffice
                  style={{ fontSize: "16px", marginTop: "5px" }}
                />
                <p>{t("menu1.companies")}</p>
              </Space>
            ),
            value: 1,
          },
        ]
      : []),
    ...(access?.contact === "1"
      ? [
          {
            label: (
              <Space>
                <HiOutlineUserGroup
                  style={{ fontSize: "16px", marginTop: "5px" }}
                />
                <p>{t("menu1.contacts")}</p>
              </Space>
            ),
            value: 2,
          },
        ]
      : []),
    ...(access?.leads === "1"
      ? [
          {
            label: (
              <Space>
                <CgUserlane style={{ fontSize: "16px", marginTop: "5px" }} />
                <p>{t("menu1.leads")}</p>
              </Space>
            ),
            value: 9,
          },
        ]
      : []),
    ...(access?.deals === "1"
      ? [
          {
            label: (
              <Space>
                <HeartHandshake size={18} style={{ marginTop: 7 }} />
                <p>{t("menu1.deals")}</p>
              </Space>
            ),
            value: 3,
          },
        ]
      : []),
    ...(access?.colleague === "1"
      ? [
          {
            label: (
              <Space>
                <FiUsers style={{ fontSize: "17px", marginTop: "5px" }} />
                <p>{t("contacts.collegues")}</p>
              </Space>
            ),
            value: 4,
          },
        ]
      : []),
    ...(access?.ticket === "1"
      ? [
          {
            label: (
              <Space>
                <TicketIconSphere size={19} style={{ marginTop: 5 }} />
                <p>{t("menu1.tickets")}</p>
              </Space>
            ),
            value: 6,
          },
        ]
      : []),
    ...(access?.projects === "1"
      ? [
          {
            label: (
              <Space>
                <Blocks size={17} style={{ marginTop: 7 }} />
                <p>{t("menu1.projects")}</p>
              </Space>
            ),
            value: 7,
          },
        ]
      : []),
    ...(access?.products === "1"
      ? [
          {
            label: (
              <Space>
                <AiOutlineShoppingCart
                  style={{ fontSize: "16px", marginTop: "5px" }}
                />
                <p>{t("menu1.products")}</p>
              </Space>
            ),
            value: 5,
          },
        ]
      : []),
    ...(access?.booking === "1"
      ? [
          {
            label: (
              <Space>
                <LuPalmtree style={{ fontSize: "16px", marginTop: "5px" }} />
                <p>{t("menu1.booking")}</p>
              </Space>
            ),
            value: 8,
          },
        ]
      : []),
  ];

  return familiesOption;
};

export default useFamiliesOption;
