import { Typography } from "antd";
import React, { useEffect, useState } from "react";

const Time = () => {
  const [time, setTime] = useState(new Date());

  useEffect(() => {
    const interval = setInterval(() => {
      setTime(new Date());
    }, 1000);

    return () => {
      clearInterval(interval);
    };
  }, []);
  return (
    <Typography.Title level={3}>
      {time.toLocaleTimeString().replace(/(.*)\D\d+/, "$1")}
    </Typography.Title>
  );
};

export default Time;
