import React from "react";
import { Mo<PERSON>, But<PERSON> } from "antd";
import { FolderFilled, FileTextFilled } from "@ant-design/icons";
import { useTranslation } from "react-i18next";

const DeleteModal = ({
  isVisible,
  onCancel,
  onConfirm,
  itemToDelete,
  loading = false
}) => {
  const [t] = useTranslation("common");

  return (
    <Modal
      title={t(`drive.deleteConfirmation.${itemToDelete?.type === 'folder' ? 'deleteFolder' : 'deleteFile'}`)}
      open={isVisible}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          {t("drive.deleteConfirmation.cancel")}
        </Button>,
        <Button 
          key="delete" 
          type="primary" 
          danger 
          loading={loading}
          onClick={onConfirm}
        >
          {t("drive.deleteConfirmation.delete")}
        </Button>
      ]}
      destroyOnClose
    >
      <div className="flex items-center space-x-3">
        {itemToDelete?.type === 'folder' ? (
          <FolderFilled style={{ fontSize: '24px', color: '#faad14' }} />
        ) : (
          <FileTextFilled style={{ fontSize: '24px', color: '#1890ff' }} />
        )}
        <div>
          <p className="mb-2">
            {t("drive.deleteConfirmation.areYouSure")} <strong>"{itemToDelete?.name}"</strong>?
          </p>
          {itemToDelete?.type === 'folder' && (
            <p className="text-sm text-gray-500">
              {t("drive.deleteConfirmation.folderWarning")}
            </p>
          )}
          {itemToDelete?.type === 'file' && (
            <p className="text-sm text-gray-500">
              {t("drive.deleteConfirmation.fileWarning")}
            </p>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default DeleteModal; 