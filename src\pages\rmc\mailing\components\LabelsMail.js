import { memo, useEffect, useMemo, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import { SET_COLLAPSE_INNER_MENU } from "new-redux/constants";
import { useTranslation } from "react-i18next";
import { setAccountData } from "new-redux/actions/mail.actions";
import {
  Button,
  Tooltip,
  Form,
  Space,
  Input,
  Dropdown,
  Spin,
  Modal,
  Popover,
  Badge,
} from "antd";
import {
  CloseOutlined,
  PlusOutlined,
  SaveOutlined,
  WarningOutlined,
} from "@ant-design/icons";
import { MdLabel } from "react-icons/md";
import { FiEdit, FiMoreVertical, FiSettings, FiTrash } from "react-icons/fi";
import { toastNotification } from "components/ToastNotification";
import { deleteLabel, storeNewLabel, updateLabel } from "../services/services";
import { labelColors, ModalConfigLabel } from "./utils";

const defaultColor = "rgb(107 114 128)";
const LabelsMail = ({ dataAccounts, statsMail, isMenuCollapsed }) => {
  //
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [t] = useTranslation("common");
  const [form] = Form.useForm();
  const location = useLocation();
  const inputRef = useRef(null);
  //
  const [addNewLabel, setAddNewLabel] = useState(false);
  const [labelIdToEdit, setLabelIdToEdit] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedColor, setSelectedColor] = useState(null);
  const [openConfigLabel, setOpenConfigLabel] = useState(false);
  const [infoLabelConfig, setInfoLabelConfig] = useState({});
  //
  const usedAccount = dataAccounts.find((account) => !!account.selected);
  //
  const statsLabels = useMemo(
    () => statsMail?.[usedAccount?.value]?.labels || {},
    [statsMail, usedAccount?.value]
  );
  //
  const labels = useMemo(() => {
    addNewLabel && setAddNewLabel(false);
    labelIdToEdit && setLabelIdToEdit(null);
    isLoading && setIsLoading(false);
    form?.resetFields();
    if (!usedAccount) return [];
    return usedAccount.labels || [];
  }, [usedAccount]);
  //
  const addLabel = () => {
    isMenuCollapsed &&
      dispatch({
        type: SET_COLLAPSE_INNER_MENU,
        payload: false,
      });
    labelIdToEdit && setLabelIdToEdit(null);
    setAddNewLabel(true);
  };
  //
  const handleAddUpdateLabel = async (newLabel, isAdd) => {
    const normalizedNewLabel = newLabel.trim().toLowerCase();

    if (isAdd) {
      const duplicate = labels?.find(
        (label) => label.label.trim().toLowerCase() === normalizedNewLabel
      );
      if (duplicate) {
        return form.setFields([
          {
            name: "add_label",
            errors: [<span>The label name already exists!</span>],
          },
        ]);
      }
    } else {
      const currentLabel = labels?.find((label) => label.id === labelIdToEdit);
      const duplicate = labels?.find(
        (label) =>
          label.label.trim().toLowerCase() === normalizedNewLabel &&
          label.id !== labelIdToEdit
      );
      if (duplicate) {
        return form.setFields([
          {
            name: "update_label",
            errors: [<span>The label name already exists!</span>],
          },
        ]);
      }
      if (
        normalizedNewLabel === currentLabel?.label.trim().toLowerCase() &&
        (selectedColor === currentLabel?.color ||
          selectedColor === defaultColor)
      ) {
        return form.setFields([
          {
            name: "update_label",
            errors: [<span>You did not change anything!</span>],
          },
        ]);
      }
    }

    try {
      setIsLoading(true);
      const formData = new FormData();
      formData.append("label", newLabel);
      if (isAdd) {
        formData.append("account_id", usedAccount?.value);
      } else {
        formData.append("id", labelIdToEdit);
      }
      formData.append("color", selectedColor || defaultColor);
      const {
        data: { data },
      } = isAdd ? await storeNewLabel(formData) : await updateLabel(formData);

      const newDataAccounts = dataAccounts.map((item) => {
        if (item.value === usedAccount.value) {
          return {
            ...item,
            labels: isAdd
              ? [data, ...(item.labels || [])]
              : item.labels.map((label) =>
                  label.id === labelIdToEdit ? data : label
                ),
          };
        }
        return item;
      });
      dispatch(setAccountData(newDataAccounts));
      form.resetFields();
      if (isAdd) setAddNewLabel(false);
      if (labelIdToEdit) setLabelIdToEdit(null);
      if (selectedColor) setSelectedColor(null);
    } catch (err) {
      toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? err.message : err);
    } finally {
      setIsLoading(false);
    }
  };
  //
  const handleUpdateLabel = (id, color) => {
    if (addNewLabel) {
      setAddNewLabel(false);
    }
    setLabelIdToEdit(id);
    setSelectedColor(color);
  };
  //
  const handleDeleteLabel = (label) => {
    const DeleteLabel = async (labelId) => {
      if (!labelId) return;
      try {
        setIsLoading(true);
        await deleteLabel(labelId, usedAccount?.value);
        const newDataAccounts = dataAccounts.map((item) =>
          item.value === usedAccount.value
            ? {
                ...item,
                labels: item.labels.filter((label) => label.id !== labelId),
              }
            : item
        );
        dispatch(setAccountData(newDataAccounts));
      } catch (err) {
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
        throw new Error(err?.message ? err.message : err);
      } finally {
        setIsLoading(false);
      }
    };
    Modal.confirm({
      title: `${t("contacts.delete")} label ${label.label}`,
      content: t("contacts.deleteConfirmMsg"),
      icon: <WarningOutlined />,
      okText: t("profile.confirm"),
      okType: "danger",
      cancelText: t("profile.cancel"),
      onOk() {
        DeleteLabel(label.id);
      },
      onCancel() {
        console.log("Cancel");
      },
    });
  };
  //
  // focus input add / update
  useEffect(() => {
    if (inputRef.current && (addNewLabel || labelIdToEdit)) {
      inputRef.current.focus({ cursor: addNewLabel ? "start" : "all" });
    }
  }, [addNewLabel, labelIdToEdit]);
  //

  const popoverContent = (
    <div className="relative grid grid-cols-7 gap-2">
      {labelColors.map(({ label, value }) => (
        <label
          key={label}
          className={` relative cursor-pointer`}
          style={{ color: value }}
        >
          <input
            type="radio"
            name="color"
            value={label}
            checked={selectedColor === value}
            onChange={() => setSelectedColor(value)}
            className="sr-only"
            aria-label={label.replace}
          />
          <span
            className={`block h-[22px] w-[22px] rounded-md border border-black/10 transition-all hover:scale-110 ${
              selectedColor === value ? "ring-2 ring-current ring-offset-1" : ""
            }`}
            style={{ backgroundColor: value }}
          />
        </label>
      ))}
    </div>
  );
  //
  const selectedLabel = useMemo(() => {
    const path = location.pathname;
    if (path.split("/")[3] === "label") {
      const labelId = path.split("/")[4];
      return Number(labelId);
    } else return null;
  }, [location]);
  //
  return (
    <div className="relative p-2 pb-12">
      <div
        className={`flex items-center px-2 pb-2 ${
          isMenuCollapsed ? "justify-center" : "justify-between"
        }`}
      >
        {!isMenuCollapsed && (
          <span className="text-sm  font-semibold text-slate-500 ">
            {t("mailing.labels")}
          </span>
        )}
        <Button
          aria-label="Add label"
          shape="circle"
          type="text"
          icon={<PlusOutlined />}
          disabled={addNewLabel}
          onClick={addLabel}
        />
      </div>

      <div className={``}>
        {addNewLabel && !isMenuCollapsed && (
          <Form
            name="add_label"
            form={form}
            autoComplete="off"
            onFinish={(values) => handleAddUpdateLabel(values.add_label, true)}
          >
            <Space.Compact
              style={{
                width: "100%",
              }}
            >
              <Popover
                content={popoverContent}
                placement="bottomLeft"
                arrow={false}
                trigger={["click"]}
              >
                <Button
                  type="text"
                  icon={
                    <MdLabel
                      style={{
                        fontSize: 22,
                        color: selectedColor || defaultColor,
                      }}
                    />
                  }
                />
              </Popover>

              <Form.Item
                key="input-add-new-label"
                name="add_label"
                rules={[
                  {
                    required: true,
                    message: (
                      <span className="ml-3">{`Label cannot be empty!`}</span>
                    ),
                  },
                ]}
                style={{ marginBottom: 0 }}
              >
                <Input
                  ref={inputRef}
                  variant="borderless"
                  placeholder="Enter a new label"
                  disabled={isLoading}
                  // prefix={
                  //   <MdLabel
                  //     style={{
                  //       fontSize: 22,
                  //       color: "rgb(107 114 128)",
                  //       marginRight: "0.5rem",
                  //     }}
                  //   />
                  // }
                />
              </Form.Item>
              <Button
                size="small"
                type="link"
                icon={<SaveOutlined style={{ fontSize: 15, marginTop: 4 }} />}
                loading={isLoading}
                onClick={form.submit}
              />
              <Button
                size="small"
                type="link"
                danger
                icon={<CloseOutlined style={{ fontSize: 15, marginTop: 4 }} />}
                disabled={isLoading}
                onClick={() => {
                  setAddNewLabel(false);
                  form.resetFields();
                }}
              />
            </Space.Compact>
          </Form>
        )}

        <Spin spinning={isLoading}>
          {labels.map((label) => (
            <div
              key={label.id}
              className={`group relative flex w-full items-center  justify-between space-x-0.5 rounded-md p-2  ${
                label.id !== labelIdToEdit &&
                selectedLabel !== label.id &&
                "hover:bg-gray-200"
              } ${selectedLabel === label.id && "bg-blue-100 text-white"}`}
            >
              {labelIdToEdit === label.id && !isMenuCollapsed ? (
                <Form
                  name="update_label"
                  form={form}
                  autoComplete="off"
                  onFinish={(values) =>
                    handleAddUpdateLabel(values.update_label, false)
                  }
                >
                  <Space.Compact
                    style={{
                      width: "100%",
                    }}
                  >
                    <Popover
                      content={popoverContent}
                      placement="bottomLeft"
                      arrow={false}
                      trigger={["click"]}
                    >
                      <Button
                        type="text"
                        icon={
                          <MdLabel
                            style={{
                              fontSize: 22,
                              color: selectedColor || defaultColor,
                            }}
                          />
                        }
                      />
                    </Popover>
                    <Form.Item
                      key="input-add-new-label"
                      name="update_label"
                      style={{ marginBottom: 0 }}
                      rules={[
                        {
                          required: true,
                          message: (
                            <span className="ml-3">{`Label cannot be empty!`}</span>
                          ),
                        },
                      ]}
                      initialValue={label.label}
                    >
                      <Input
                        ref={inputRef}
                        variant="borderless"
                        placeholder="Enter a new label"
                        disabled={isLoading}
                      />
                    </Form.Item>
                    <Button
                      type="link"
                      icon={<SaveOutlined style={{ fontSize: 15 }} />}
                      loading={isLoading}
                      onClick={form.submit}
                    />
                    <Button
                      type="link"
                      danger
                      icon={<CloseOutlined style={{ fontSize: 15 }} />}
                      disabled={isLoading}
                      onClick={() => {
                        setLabelIdToEdit(null);
                        form.resetFields();
                      }}
                    />
                  </Space.Compact>
                </Form>
              ) : (
                <Tooltip
                  key={label.id}
                  placement="right"
                  title={
                    !!isMenuCollapsed && (
                      <p>
                        {label.label}
                        {statsLabels[label?.id] > 0 && (
                          <span className="ml-3 inline-flex items-center rounded-full bg-white px-1 text-xs font-semibold text-slate-600">
                            {statsLabels[label?.id] > 99
                              ? "+99"
                              : statsLabels[label?.id]}
                          </span>
                        )}
                      </p>
                    )
                  }
                >
                  <div
                    className={`flex w-[90%] cursor-pointer  items-center  space-x-3 ${
                      isMenuCollapsed ? "justify-center" : ""
                    }`}
                    onClick={() =>
                      navigate(
                        `mailing/${usedAccount?.value}/label/${label.id}`
                      )
                    }
                  >
                    <Badge
                      dot={statsLabels[label?.id] > 0 && !!isMenuCollapsed}
                    >
                      <MdLabel
                        style={{
                          fontSize: 22,
                          color: label?.color || defaultColor,
                        }}
                      />
                    </Badge>
                    {!isMenuCollapsed && (
                      <p
                        className={`max-w-[84%] truncate text-sm text-gray-500`}
                      >
                        {label.label}
                      </p>
                    )}
                  </div>
                </Tooltip>
              )}

              {!isMenuCollapsed && !labelIdToEdit && !addNewLabel && (
                <div className="group relative">
                  <div className="mr-1 block items-center justify-center font-semibold text-slate-500 group-hover:hidden">
                    <span>{statsLabels[label?.id] || ""}</span>
                  </div>
                  <div className="hidden group-hover:block">
                    <DropdownAction
                      key={label.id}
                      t={t}
                      label={label}
                      handleDeleteLabel={handleDeleteLabel}
                      handleUpdateLabel={handleUpdateLabel}
                      setOpenModal={setOpenConfigLabel}
                      setInfoLabelConfig={setInfoLabelConfig}
                      selectedLabel={selectedLabel}
                    />
                  </div>
                </div>
              )}
            </div>
          ))}
        </Spin>
      </div>

      <ModalConfigLabel
        key={infoLabelConfig}
        isOpen={openConfigLabel}
        setIsOpen={setOpenConfigLabel}
        infoLabel={infoLabelConfig}
        setInfoLabel={setInfoLabelConfig}
        usedAccount={usedAccount}
      />
    </div>
  );
};

export const DropdownAction = ({
  label,
  handleDeleteLabel,
  handleUpdateLabel,
  setOpenModal,
  setInfoLabelConfig,
  selectedLabel,
  t,
}) => {
  //
  //
  const items = [
    {
      key: "edit",
      label: t("contacts.edit"),
      icon: <FiEdit className="h-4 w-4 text-slate-400" />,
      onClick: () => {
        handleUpdateLabel(label.id, label?.color);
      },
    },
    {
      key: "setting",
      label: t("menu1.settings"),
      icon: <FiSettings className="h-4 w-4 text-slate-400" />,
      onClick: () => {
        setInfoLabelConfig(label);
        setOpenModal(true);
      },
    },
    {
      key: "delete",
      danger: true,
      label: t("contacts.delete"),
      icon: <FiTrash className="h-4 w-4" />,
      disabled: selectedLabel === label?.id,
      onClick: () => handleDeleteLabel(label),
    },
  ];

  return (
    <Dropdown
      key={label.id}
      trigger={["click"]}
      placement="bottomRight"
      menu={{
        items,
      }}
    >
      <FiMoreVertical className="h-[18px] w-[18px] cursor-pointer text-gray-400 hover:text-gray-700" />
    </Dropdown>
  );
};
//

export default memo(LabelsMail);
