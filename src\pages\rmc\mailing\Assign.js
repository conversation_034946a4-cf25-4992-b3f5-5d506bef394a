import { useMemo, useState } from "react";
import { <PERSON><PERSON>, <PERSON>over, <PERSON>lt<PERSON>, Divider, Tag, Avatar, Badge } from "antd";
import { useTranslation } from "react-i18next";
import { CloseCircleOutlined } from "@ant-design/icons";
import "../../../index.css";
import AssignContent from "./AssignContent";
import { URL_ENV } from "index";
import MainService from "services/main.service";
import { toastNotification } from "components/ToastNotification";
import { useSelector } from "react-redux";

const Assign = ({
  idEmail,
  owner,
  setDataMail,
  dataMailInbox,
  info,
  type,
  transfert,
  detailsMail,
  setDetailsMail,
  circleDelete,
  isHovered,
  setIsHovered,
  getDetailsMessageInbox,
}) => {
  //
  const [t] = useTranslation("common");
  const [openPopCon, setOpenPopCon] = useState(false);
  const [openSelect, setOpenSelect] = useState(false);
  const { user } = useSelector(({ user }) => user);
  const { dataAccounts } = useSelector((state) => state.mailReducer);
  const onlineUser = useSelector((state) => state.ChatRealTime.onlineUser);

  // const handleMouseEnter = () => {
  //   setIsHovered(true);
  // };

  // const handleMouseLeave = () => {
  //   setIsHovered(false);
  // };

  const usedAccount = useMemo(
    () => dataAccounts?.find((item) => item.selected === true),
    [dataAccounts]
  );

  const tagContents = (
    <div className="flex w-[260px] items-center justify-center">
      <AssignContent
        key={idEmail}
        idEmail={idEmail}
        owner={owner}
        setDataMail={setDataMail}
        dataMailInbox={dataMailInbox}
        openSelect={openSelect}
        setOpenSelect={setOpenSelect}
        setOpen={setOpenPopCon}
        openPopCon={openPopCon}
        type={type}
        setDetailsMail={setDetailsMail}
        getDetailsMessageInbox={getDetailsMessageInbox}
      />
    </div>
  );

  const popTitle = (
    <div className="px-2 pb-px pt-2">
      <div className="flex flex-row items-center  justify-between">
        <span>{t("mailing.AssignTo")}</span>
      </div>

      <Divider className="my-2" />
    </div>
  );

  const DeleteAssignEmail = async () => {
    var formData = new FormData();
    formData.append("email_id", idEmail);
    formData.append("user_id", owner?.user_id);
    formData.append("owner", owner?.owner);
    formData.append("account_id", usedAccount.value);
    if (usedAccount?.departmentId?.length > 0)
      for (let i = 0; i < usedAccount?.departmentId?.length; i++) {
        formData.append("departement_id[]", usedAccount?.departmentId[i]);
      }
    try {
      const response = await MainService.deleteAssignEmail(formData);
      if (response?.status === 200) {
        if (type === "inbox") {
          setDataMail(
            dataMailInbox.map((item) =>
              item.id === idEmail ? { ...item, owner: {} } : item
            )
          );
        } else {
          setDetailsMail((p) => {
            let prev = { ...p };
            prev.data[prev?.data?.length - 1].owner = {};
            return prev;
          });
        }

        toastNotification(
          "success",
          t("contacts.successDelete"),
          "topRight",
          3
        );
      }
    } catch (error) {
      console.log(error);

      toastNotification(
        "error",
        error?.response?.data
          ? error?.response?.data?.message
          : t("toasts.errorFetchApi"),
        "topRight",
        4
      );
    }
  };

  const displayAvatarAssigned = () => {
    return (
      <Popover
        content={tagContents}
        title={popTitle}
        open={openPopCon}
        onOpenChange={(open) => setOpenPopCon(open)}
        trigger={["click"]}
        placement="bottomLeft"
        arrow={false}
        overlayClassName="popover-tag-voip"
        overlayStyle={{ width: "17.5rem" }}
        overlayInnerStyle={{
          padding: "0px",
        }}
      >
        {owner?.avatar?.length > 0 ? (
          <Tooltip title={owner?.label_data} placement="bottom">
            <Badge
              dot
              style={{ width: "10px", height: "10px" }}
              // dot={true}
              color={
                onlineUser[owner?.uuid] === "away"
                  ? "orange"
                  : onlineUser[owner?.uuid] === "busy"
                  ? "red"
                  : onlineUser[owner?.uuid] === "online"
                  ? "green"
                  : "#a6a6a6"
              }
              offset={[-2, 28]}
            >
              <Badge
                count={
                  idEmail === isHovered &&
                  (owner.owner === user?.id ||
                    owner.user_id === user?.id ||
                    (usedAccount?.dispatcheur &&
                      usedAccount?.dispatcheur?.includes(user?.id))) ? (
                    <CloseCircleOutlined
                      style={{
                        cursor: "pointer",
                        color: "red",
                        opacity: "0.8",
                        fontSize: "17px",
                        top: "2px",
                        marginRight: "2px",
                        // fontWeight: 400,
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        DeleteAssignEmail();
                      }}
                    />
                  ) : null
                }
              >
                <Avatar
                  style={{
                    marginBottom: "4px",
                  }}
                  size={30}
                  height={10}
                  width={10}
                  src={
                    <img
                      src={`${
                        URL_ENV?.REACT_APP_BASE_URL +
                        URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                      }${owner?.avatar}`}
                      alt="A"
                    />
                  }
                />
              </Badge>
            </Badge>
          </Tooltip>
        ) : (
          <Tooltip title={owner?.label_data} placement="bottom">
            <Badge
              dot
              style={{ width: "10px", height: "10px" }}
              // dot={true}
              color={
                onlineUser[owner?.uuid] === "away"
                  ? "orange"
                  : onlineUser[owner?.uuid] === "busy"
                  ? "red"
                  : onlineUser[owner?.uuid] === "online"
                  ? "green"
                  : "#a6a6a6"
              }
              offset={[-2, 28]}
            >
              <Badge
                count={
                  idEmail === isHovered &&
                  (owner.owner === user?.id ||
                    owner.user_id === user?.id ||
                    (usedAccount?.dispatcheur &&
                      usedAccount?.dispatcheur?.includes(user?.id))) ? (
                    <CloseCircleOutlined
                      style={{
                        cursor: "pointer",
                        color: "red",
                        opacity: "0.8",
                        fontSize: "17px",
                        top: "2px",
                        marginRight: "2px",
                        // fontWeight: 400,
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        DeleteAssignEmail();
                      }}
                    />
                  ) : null
                }
              >
                <Avatar
                  style={{
                    backgroundColor: "#c41d7f",
                    marginBottom: "4px",
                  }}
                  size={30}
                  height={10}
                  width={10}
                >
                  {owner?.label_data?.charAt(0)?.toUpperCase()}
                </Avatar>
              </Badge>
            </Badge>
          </Tooltip>
        )}
      </Popover>
    );
  };

  return (
    <>
      {owner && Object.values(owner)[0] != null ? (
        displayAvatarAssigned()
      ) : (transfert?.account_id &&
          transfert?.account_id !== String(usedAccount?.value)) ||
        (owner?.owner && owner?.user_id !== user.id) ? (
        <Tooltip
          title={
            transfert?.account_id &&
            transfert?.account_id !== String(usedAccount?.value)
              ? t("mailing.Tooltip.TransferedNoAssign")
              : owner?.owner && owner?.user_id != user.id
              ? `vous ne pouvez pas assigné l'email `
              : transfert?.account_id && owner?.owner
              ? `vous ne pouvez pas assigné l'email car il est transféré et assigné`
              : // : !usedAccount?.dispatcheur?.includes(user?.id)
                // ? `Vous ne pouvez pas effectuer cette action car vous n'avez pas le rôle de dispatcheur`
                t("mailing.Tooltip.Assigner")
          }
          placement={type === "details" ? "bottom" : "top"}
        >
          <Badge>
            <Button size="small" type="dashed" disabled={true}>
              {t("mailing.Assign")}
            </Button>
          </Badge>
        </Tooltip>
      ) : (
        <Popover
          content={tagContents}
          title={popTitle}
          open={openPopCon}
          onOpenChange={(open) => {
            setOpenSelect(open);
            setOpenPopCon(open);
          }}
          trigger={["click"]}
          placement="bottomLeft"
          arrow={false}
          overlayClassName="popover-tag-voip"
          overlayStyle={{ width: "17.5rem" }}
          overlayInnerStyle={{
            padding: "0px",
          }}
        >
          <Tooltip
            title={t("mailing.Tooltip.Assigner")}
            placement={type === "details" ? "bottom" : "top"}
          >
            <Button
              type="dashed"
              size="small"
              onClick={() => {
                setOpenSelect(true);
              }}
            >
              {t("mailing.Assign")}
            </Button>
          </Tooltip>
        </Popover>
      )}
    </>
  );
};

export default Assign;
