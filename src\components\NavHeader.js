import React, { useEffect } from "react";
import { Form, Select, Segmented, Badge, Tooltip } from "antd";
import Proptypes from "prop-types";
import { useTranslation } from "react-i18next";
import { Refs_IDs } from "./tour/tourConfig";

/**
 * @name NavHeader
 *
 * @description This component holds the select and segmented components which helps display data and switch views.
 *
 * @param {function} handleSegmentedChange Handles all the functions on switch views.
 * @param {object} defaultValues Default components values. (To be displayed on first load).
 * @param {array} SelectOptions Array of the data to be displayed on the select.
 * @param {function} segmentedData Array of the data to be displayed on the segmented.
 * @param {function} onSelectChange Handles every change in the select.
 *
 * @returns {JSX.Element} The rendered nav header (switch views + pipeline select) on top of the component.
 */

const NavHeader = ({
  handleSegmentedChange,
  SelectOptions,
  segmentedData,
  defaultValues,
  onSelectChange,
  source,
  isDisabled,
}) => {
  const [form] = Form.useForm();
  const [t] = useTranslation("common");

  // Set pipeline select default value.
  useEffect(() => {
    form.setFieldsValue({
      pipelineSelect:
        defaultValues?.selectedPipeline || SelectOptions[0]?.value,
    });
  }, [defaultValues?.selectedPipeline, form, SelectOptions]);

  return (
    <Form
      form={form}
      className={`relative mb-0 flex w-full flex-row ${
        source === "module" ? "justify-around" : ""
      }`}
    >
      <Badge
        offset={[-30, 1]}
        dot={
          typeof defaultValues?.selectedPipeline !== "undefined" &&
          defaultValues?.selectedPipeline
        }
      >
        <Tooltip title={isDisabled ? t("contacts.selectPipelineDisabled") : ""}>
          <div ref={Refs_IDs.families_select_pipeline}>
            <Form.Item
              name="pipelineSelect"
              className={`mr-${source === "module" ? "1" : "4"}`}
            >
              <Select
                options={SelectOptions}
                style={{ width: "200px", height: "32px" }}
                popupMatchSelectWidth={false}
                onChange={(val) => {
                  onSelectChange(val);
                }}
                disabled={isDisabled}
              />
            </Form.Item>
          </div>
        </Tooltip>
      </Badge>
      <Tooltip title={isDisabled ? t("contacts.selectKanbanViewDisabled") : ""}>
        <div ref={Refs_IDs.families_select_display_view} className="ml-1">
          <Segmented
            ref={Refs_IDs.choiceViewTask}
            style={{
              height: "32px",
            }}
            options={segmentedData}
            onChange={(value) => {
              handleSegmentedChange(value);
            }}
            defaultValue={defaultValues?.switchViews}
            disabled={isDisabled}
          />
        </div>
      </Tooltip>
    </Form>
  );
};

NavHeader.propTypes = {
  handleSegmentedChange: Proptypes.func,
  SelectOptions: Proptypes.array,
  segmentedData: Proptypes.array,
  defaultValues: Proptypes.object,
  onSelectChange: Proptypes.func,
};

NavHeader.defaultProps = {
  defaultValues: null,
};

export default NavHeader;
