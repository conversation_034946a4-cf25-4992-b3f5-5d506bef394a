import { useState, useEffect } from "react";
import { FileTextOutlined } from "@ant-design/icons";
import { List, Avatar, Button, Tag } from "antd";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { dayjs_timezone } from "App";
import MainService from "services/main.service";
import { URL_ENV } from "index";
import { extractTitle } from "pages/notes/utils";
import { formatDateForDisplay } from "pages/tasks/helpers/formatDateToDisplay";
import { formatDateComparison } from "pages/voip/helpers/helpersFunc";
import { AvatarChat } from "components/Chat";
import { getName } from "pages/layouts/chat/utils/ConversationUtils";
import { setNoteOpenElementModal } from "new-redux/actions/selfnotes.actions/selfnotes";
import { useDispatch } from "react-redux";

const NotesList = ({ contactInfo }) => {
  const [notesList, setNotesList] = useState([]);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [loadNotes, setLoadNotes] = useState(false);

  const noteOpenElementModal = useSelector(
    (state) => state?.selfNotesReducer?.noteOpenElementModal
  );

  const [t] = useTranslation("common");
  const navigate = useNavigate();
  const user = useSelector((state) => state?.user?.user);

  const getSharedNotesWithElement = async () => {
    try {
      setLoadNotes(true);
      const response = await MainService.getSharedNotesInViewSphere(
        contactInfo?.id,
        page
      );
      setNotesList(response?.data?.data?.data);
      setTotalPages(response?.data?.data?.total);
      setLoadNotes(false);
    } catch (error) {
      setLoadNotes(false);
    }
  };

  useEffect(() => {
    getSharedNotesWithElement();
  }, [contactInfo?.id, page]);

  const dispatch = useDispatch();

  const handleOpenNote = (noteId) => {
    //console.log("note open element modal", noteOpenElementModal);
    if (noteOpenElementModal?.noteId) {
      dispatch(
        setNoteOpenElementModal({
          noteId: noteOpenElementModal?.noteId,
          noteToOpen: noteId,
          closeModalSignal: true,
        })
      );
    }
    navigate("/notes", { state: { id: noteId } });
  };
  return (
    <div
      style={{
        height: "calc(100vh - 210px)",
        overflowY: "auto",
        paddingRight: "8px",
        marginRight: "-8px",
      }}
    >
      <List
        pagination={
          notesList?.length > 0
            ? {
                size: "small",
                pageSize: 15,
                current: page,
                total: totalPages,
                showTotal: (total, range) => {
                  return t("selfNotes.noteListPagination", {
                    range: `${range[0]}-${range[1]}`,
                    totalItems: total,
                  });
                },
                onChange: (pageNum, size) => {
                  setPage(pageNum);
                },
              }
            : null
        }
        loading={loadNotes}
        dataSource={notesList}
        renderItem={(item, index) => (
          <List.Item
            key={`sharedNote_${contactInfo?._id}_${index}`}
            disabled={
              item?.user !== user?.id &&
              !item?.shared_with?.some((shared) => shared?._id === user?.id)
            }
            className={
              item?.user === user?.id ||
              item?.shared_with?.some((shared) => shared?._id === user?.id)
                ? "cursor-pointer hover:rounded-md hover:bg-[#F1F5F9]"
                : "cursor-not-allowed hover:rounded-md"
            }
            onClick={() =>
              item?.user !== user?.id &&
              !item?.shared_with?.some((shared) => shared?._id === user?.id)
                ? false
                : handleOpenNote(item?._id)
            }
            actions={[
              <Button
                type="link"
                size="small"
                key="list-loadmore-edit"
                onClick={() => handleOpenNote(item?._id)}
                disabled={
                  item?.user !== user?.id &&
                  !item?.shared_with?.some((shared) => shared?._id === user?.id)
                }
              >
                {t("chat.goto")}
              </Button>,
            ]}
          >
            <List.Item.Meta
              avatar={<Avatar icon={<FileTextOutlined />} className="ml-2" />}
              title={extractTitle(
                item?.content?.length > 100
                  ? item?.content?.slice(0, 100) + "..."
                  : item?.content
              )}
              description={
                <div className="flex items-center gap-1">
                  {t("contacts.createdBy")}
                  <span className="flex items-center gap-1">
                    <AvatarChat
                      // fontSize="0.875rem"
                      url={
                        URL_ENV?.REACT_APP_BASE_URL +
                        URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                        item?.dataUser?.avatar?.path
                      }
                      type="user"
                      size={22}
                      height={10}
                      width={10}
                      name={getName(item?.user_data, "avatar")}
                      hasImage={
                        item?.dataUser?.avatar?.path &&
                        item?.dataUser?.avatar?.path !== "/storage/uploads/"
                      }
                    />
                    <span className="text-[13px] text-[#00000072]">
                      {item?.user_data}
                    </span>
                  </span>
                  {formatDateComparison(item?.updated_at, t)?.toLowerCase()}{" "}
                  {t("chat.edit.at")}{" "}
                  {new Date(item?.updated_at)?.getHours() +
                    ":" +
                    (new Date(item?.updated_at)?.getMinutes() < 10
                      ? "0" + new Date(item?.updated_at)?.getMinutes()
                      : new Date(item?.updated_at)?.getMinutes())}
                </div>
              }
            />
          </List.Item>
        )}
      />
    </div>
  );
};

export default NotesList;
