import { TeamOutlined } from "@ant-design/icons";
import { Tooltip } from "antd";
import { Space } from "antd/lib";
import { CgUserlane } from "react-icons/cg";
import { FaUsers } from "react-icons/fa";
import { HiOutlineBuildingOffice } from "react-icons/hi2";
import { ImUsers } from "react-icons/im";
import { FaUsersBetweenLines } from "react-icons/fa6";

const DisplayModuleIconAndText = ({
  familyId,
  iconStyle = { fontSize: 14, cursor: "help" },
  showText = false,
  textStyle = {},
  hideTooltip = false,
  t,
}) => {
  const iconStyles = { ...iconStyle, cursor: hideTooltip ? "cursor" : "help" };

  const familyIcons = {
    1: { title: t("contacts.companies"), icon: HiOutlineBuildingOffice },
    2: { title: t("contacts.contacts"), icon: TeamOutlined },
    9: { title: t("contacts.leads"), icon: Cg<PERSON><PERSON><PERSON> },
    group: { title: t("voip.group"), icon: FaUsers },
    queue: { title: t("voip.queue"), icon: ImUsers },
    conf: { title: t("voip.conf"), icon: FaUsersBetweenLines },
  };

  const { title, icon: Icon } = familyIcons[familyId] || {};

  if (!title || !Icon) return null;

  return (
    <Space size={3}>
      {!hideTooltip && (
        <Tooltip title={title} destroyTooltipOnHide>
          <Icon style={iconStyles} />
        </Tooltip>
      )}

      {hideTooltip && <Icon style={iconStyles} />}

      <span style={textStyle}>{showText && title}</span>
    </Space>
  );
};

export default DisplayModuleIconAndText;
