import { Tour } from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { familyIcons } from "./ViewSphere2";
import { isGuestConnected } from "utils/role";

const TourViewSphere = ({ contactInfo, selectedTags }) => {
  const [openTour, setOpenTour] = useState(true);
  const [t] = useTranslation("common");

  const { user } = useSelector((state) => state.user);
  const nameFamily = familyIcons(t)
    .find((el) => el.key === contactInfo?.family_id)
    ?.label?.slice(0, -1)
    ?.toLowerCase();

  return (
    <>
      {process.env.REACT_APP_BRANCH.includes("dev") && (
        <Tour
          placement="bottom"
          open={openTour}
          zIndex={9999}
          onClose={() => setOpenTour(false)}
          steps={[
            {
              title: `Bonjour ${user?.label}, voici une présentation de vue sphére`,
              description: (
                <div>
                  Retournez à l'interface des{" "}
                  <strong>
                    {familyIcons(t)
                      .find((el) => el.key === contactInfo?.family_id)
                      ?.label?.toLowerCase()}
                  </strong>
                </div>
              ),
              target: () => document.getElementById("btn-back-view-sphere"),
            },
            ...(contactInfo?.family_id == 2 && !contactInfo?.guest
              ? [
                  {
                    title: <span>Invitez {contactInfo?.name}</span>,
                    description: (
                      <div>
                        Invitez {contactInfo?.name} à être{" "}
                        <strong>un invité.</strong>
                      </div>
                    ),
                    target: () =>
                      document.getElementById(
                        "invite-contact-button-view-sphere"
                      ),
                  },
                ]
              : contactInfo?.family_id == 9 && !contactInfo?.guest
              ? [
                  {
                    title: "Convertir en contact",
                    description: (
                      <div>
                        En cliquant ici, vous avez la possibilité{" "}
                        <strong>
                          de convertir {contactInfo?.name} en un contact
                        </strong>
                      </div>
                    ),

                    target: () =>
                      document.getElementById(
                        "btn-convert-to-contact-header-view-sphere"
                      ),
                  },
                ]
              : contactInfo?.family_id == 3 &&
                contactInfo.closing_reason?.reason_type == null
              ? [
                  {
                    title: "Gagner cette offre",
                    description: (
                      <div>
                        En cliquant ici, vous indiquez la raison{" "}
                        <strong>de gagner {contactInfo?.name}</strong>
                      </div>
                    ),
                    target: () =>
                      document.getElementById("btn-success-deal-view-sphere"),
                  },
                  {
                    title: "Perdre cette offre",
                    description: (
                      <div>
                        En cliquant ici, vous indiquez la raison{" "}
                        <strong>de perdre {contactInfo?.name}</strong>
                      </div>
                    ),
                    target: () =>
                      document.getElementById("btn-fail-deal-view-sphere"),
                  },
                ]
              : (contactInfo?.family_id == 3 &&
                  contactInfo.closing_reason?.reason_type == "1") ||
                contactInfo.closing_reason?.reason_type == "0"
              ? [
                  {
                    title: "Retour à l'étape précédant la finale",
                    description: (
                      <div>
                        Cette action implique de revenir{" "}
                        <strong>à l'étape antérieure à la phase finale</strong>,
                        généralement pour réviser, ajuster ou compléter des
                        éléments avant la conclusion finale{" "}
                      </div>
                    ),
                    target: () =>
                      document.getElementById("reopen-stage-view-sphere"),
                  },
                ]
              : contactInfo?.family_id == 6 &&
                selectedTags.default &&
                selectedTags.final &&
                selectedTags.resolved == 0
              ? [
                  {
                    title: "Retour à la première étape d'un ticket",
                    description: (
                      <div>
                        Retour <strong>à la première étape d'un ticket</strong>,
                        pour reprise du traitement.
                      </div>
                    ),
                    target: () =>
                      document.getElementById(
                        "reopen-stage-ticket-view-sphere"
                      ),
                  },
                ]
              : contactInfo?.family_id == 6 &&
                contactInfo?.pipeline &&
                selectedTags?.resolved == 1
              ? [
                  {
                    title: "Clôturer le ticket en sélectionnant l'étape finale",
                    description: (
                      <div>
                        Finalisez le processus de ticket en choisissant{" "}
                        <strong>l'étape finale</strong> pour marquer
                        officiellement la résolution du problème ou la
                        conclusion de la tâche.
                      </div>
                    ),
                    target: () =>
                      document.getElementById("close-ticket-view-sphere"),
                  },
                ]
              : []),

            ...(user?.id === contactInfo?.owner?.id &&
            contactInfo?.family_id == 6
              ? [
                  {
                    title: "Supprimer le ticket",
                    description: (
                      <div>
                        Cette action permet{" "}
                        <strong>
                          d'éliminer complètement le ticket du système
                        </strong>
                        , indiquant ainsi que le problème ou la tâche associée
                        n'a plus besoin d'être suivi ou traité.
                      </div>
                    ),
                    target: () =>
                      document.getElementById("cancel-ticket-view-sphere"),
                  },
                ]
              : []),
            ...(selectedTags?.id
              ? [
                  {
                    title: "Modifier l'étape du pipeline",
                    description: (
                      <div>
                        Cette fonctionnalité vous permet de modifier{" "}
                        <strong>l'étape actuelle</strong> du pipeline pour mieux
                        organiser et suivre le flux de travail de votre{" "}
                        {nameFamily}
                      </div>
                    ),
                    target: () =>
                      document.getElementById(
                        "select-pipeline/stage-view-sphere"
                      ),
                  },
                  {
                    title: "Visualiser les étapes de la pipeline",
                    description: (
                      <div>
                        Cette fonctionnalité vous permet de modifier{" "}
                        <strong>l'étape actuelle</strong> du pipeline avec plus
                        de détails pour mieux organiser et suivre le flux de
                        travail de votre {nameFamily}
                      </div>
                    ),
                    target: () =>
                      document.getElementById(
                        "show-pipeline/stage-view-sphere"
                      ),
                  },
                ]
              : []),

            ...(!isGuestConnected()
              ? [
                  {
                    title: "Les actions rapides de " + contactInfo?.name,
                    description: (
                      <div>
                        Les actions rapides sont les suivantes :{" "}
                        {contactInfo?.family_id == 1 ||
                        contactInfo?.family_id == 2 ||
                        contactInfo?.family_id == 4 ? (
                          <>
                            <strong>Appeler</strong> {contactInfo?.name},{" "}
                            <strong>Envoyer un mail</strong> à{" "}
                            {contactInfo?.name},{" "}
                          </>
                        ) : (
                          ""
                        )}
                        <strong>Créer une activité</strong> avec{" "}
                        {contactInfo?.name}, et{" "}
                        <strong>Créer une visioconférence instantanée</strong>{" "}
                        avec {contactInfo?.name}.
                      </div>
                    ),

                    target: () =>
                      document.getElementById("actions-header-view-sphere"),
                  },
                  {
                    title: "Options du " + contactInfo?.name,
                    description: (
                      <div>
                        En cliquant ici, vous pouvez <strong>éditer</strong>{" "}
                        {contactInfo?.name},{" "}
                        <strong>
                          ouvrir le tableau des groupes de champs et le tableau
                          de champs
                        </strong>{" "}
                        de {nameFamily} dans la même interface, ainsi que{" "}
                        <strong>
                          le tableau des pipelines et le tableau des étapes
                        </strong>{" "}
                        de {nameFamily} la même interface.
                      </div>
                    ),

                    target: () =>
                      document.getElementById("btn-more-header-view-sphere"),
                  },
                ]
              : []),
            {
              title: "Tableau de bord de " + contactInfo?.name,
              description: (
                <div>
                  En cliquant ici, vous pouvez visualiser{" "}
                  <strong>les informations générales</strong>,{" "}
                  <strong>les statistiques</strong> et{" "}
                  <strong>des tableaux d'associations</strong>.
                </div>
              ),

              target: () => document.getElementById("dashboard-view-sphere"),
            },
            {
              title: "Informations de " + contactInfo?.name,
              description: (
                <div>
                  En cliquant ici, vous pouvez visualiser{" "}
                  <strong>toutes les informations</strong> selon{" "}
                  <strong>les groupes de champs</strong> et trouver le bouton{" "}
                  <strong>ouvrir champs</strong> pour ajouter et supprimer les
                  champs qui leur sont liés.
                </div>
              ),

              target: () => document.getElementById("informations-view-sphere"),
            },
            {
              title: "Intéractions de " + contactInfo?.name,
              description: (
                <div>
                  En cliquant ici, vous pouvez visualiser{" "}
                  <strong>des menus</strong>. Le premier menu est{" "}
                  <strong>l'historique</strong>, qui permet de retracer de
                  manière organisée et intuitive l'historique des activités
                  telles que les associations, les réunions, les e-mails
                  échangés, les tâches assignées et les commentaires
                  enregistrés, etc. Il inclut également le{" "}
                  <strong>journal d'appels</strong>, les{" "}
                  <strong>e-mails</strong> où {contactInfo?.name} a été
                  identifié, les <strong>discussions</strong> si{" "}
                  {contactInfo?.name} a été associé avec des utilisateurs, et
                  les <strong>Réseaux sociaux</strong> si {contactInfo?.name}{" "}
                  provient d'un réseau social.
                </div>
              ),

              target: () => document.getElementById("interactions-view-sphere"),
            },
            ...(!isGuestConnected()
              ? [
                  {
                    title:
                      "Raccourci du journal d'appels de " + contactInfo?.name,
                    description: (
                      <div>
                        En cliquant ici, vous pouvez visualiser{" "}
                        <strong>un tableau de journal d'appels</strong> entre
                        vous et {contactInfo?.name}, affichant les détails de
                        ces appels.
                      </div>
                    ),

                    target: () =>
                      document.getElementById(
                        "raccourci-log-calls-view-sphere"
                      ),
                  },
                  {
                    title: "Raccourci des e-mails de " + contactInfo?.name,
                    description: (
                      <div>
                        En cliquant ici, vous pouvez visualiser{" "}
                        <strong>un tableau des e-mails</strong> où le compte de{" "}
                        {contactInfo?.name} a été identifié depuis l'interface
                        e-mail.
                      </div>
                    ),

                    target: () =>
                      document.getElementById("raccourci-emails-view-sphere"),
                  },
                  {
                    title:
                      "Raccourci de la discussion à propos de " +
                      contactInfo?.name,
                    description: (
                      <div>
                        En cliquant ici, vous pouvez accéder{" "}
                        <strong>
                          à une fenêtre contextuelle contenant une discussion
                        </strong>
                      </div>
                    ),

                    target: () =>
                      document.getElementById("raccourci-chat-view-sphere"),
                  },
                  {
                    title:
                      "Raccourci de la discussion de " +
                      contactInfo?.name +
                      " en réseaux sociaux",
                    description: (
                      <div>
                        En cliquant ici, vous pouvez accéder{" "}
                        <strong>
                          à une fenêtre contextuelle contenant une discussion de
                          réseaux sociaux
                        </strong>
                        .
                      </div>
                    ),

                    target: () =>
                      document.getElementById("raccourci-rmc-view-sphere"),
                  },
                  {
                    title: "Activités de " + contactInfo?.name,
                    description: (
                      <div>
                        En cliquant ici, vous pouvez visualiser{" "}
                        <strong>les activités</strong> dans une liste et un
                        tableau en classant les activités dans trois catégories
                        : <strong>aujourd'hui, à venir et historique.</strong>
                      </div>
                    ),

                    target: () => document.getElementById("tasks-view-sphere"),
                  },
                  {
                    title: "Notes de " + contactInfo?.name,
                    description: (
                      <div>
                        En cliquant ici, vous pouvez visualiser{" "}
                        <strong>les notes </strong> affectés en
                        {contactInfo?.name}
                      </div>
                    ),

                    target: () => document.getElementById("notes-view-sphere"),
                  },
                  {
                    title: "Ajouter des associations avec " + contactInfo?.name,
                    description: (
                      <div>
                        En cliquant ici, vous pouvez <strong>associer</strong>{" "}
                        {contactInfo?.name} avec d'autres modules qui sont liés
                        à {nameFamily} selon la configuration des champs.
                      </div>
                    ),

                    target: () =>
                      document.getElementById("add-association-view-sphere"),
                  },
                  {
                    title: "Commentaires à propos de  " + contactInfo?.name,
                    description: (
                      <div>
                        En cliquant ici, vous pouvez{" "}
                        <strong>
                          ajouter, modifier et supprimer des commentaires
                        </strong>{" "}
                        qu'ils soient internes ou publics.
                      </div>
                    ),

                    target: () =>
                      document.getElementById("comments-view-sphere"),
                  },
                  {
                    title: "Fichiers à propos de  " + contactInfo?.name,
                    description: (
                      <div>
                        En cliquant ici, vous pouvez{" "}
                        <strong>
                          ajouter, modifier et supprimer des fichiers
                        </strong>
                        , qu'ils soient internes ou publics.
                      </div>
                    ),

                    target: () => document.getElementById("files-view-sphere"),
                  },
                  {
                    title: "Listes à propos de  " + contactInfo?.name,
                    description: (
                      <div>
                        En cliquant ici, vous pouvez{" "}
                        <strong>
                          ajouter, modifier et supprimer des listes.
                        </strong>
                      </div>
                    ),

                    target: () => document.getElementById("list-view-sphere"),
                  },
                ]
              : [
                  {
                    title: "Activités de " + contactInfo?.name,
                    description: (
                      <div>
                        En cliquant ici, vous pouvez visualiser{" "}
                        <strong>les activités</strong> dans une liste et un
                        tableau en classant les activités dans trois catégories
                        : <strong>aujourd'hui, à venir et historique.</strong>
                      </div>
                    ),

                    target: () => document.getElementById("tasks-view-sphere"),
                  },
                  {
                    title: "Commentaires à propos de  " + contactInfo?.name,
                    description: (
                      <div>
                        En cliquant ici, vous pouvez{" "}
                        <strong>
                          ajouter, modifier et supprimer des commentaires
                        </strong>{" "}
                        qu'ils soient internes ou publics.
                      </div>
                    ),

                    target: () =>
                      document.getElementById("comment-guest-view-sphere"),
                  },
                  {
                    title: "Fichiers à propos de  " + contactInfo?.name,
                    description: (
                      <div>
                        En cliquant ici, vous pouvez{" "}
                        <strong>
                          ajouter, modifier et supprimer des fichiers
                        </strong>
                        , qu'ils soient internes ou publics.
                      </div>
                    ),

                    target: () =>
                      document.getElementById("files-guest-view-sphere"),
                  },
                ]),
          ]}
        />
      )}
    </>
  );
};

export default TourViewSphere;
