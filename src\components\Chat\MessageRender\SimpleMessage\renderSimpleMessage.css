.message > p {
  display: block;
  align-items: center;
  justify-content: start;
}

.message > p > .search {
  margin: 0px !important;
}
.message > p > * :not(.search) {
  margin: 0 4px;
}
p span[aria-label="emoji"] {
  min-height: 25px;
}
/* .response.ant-typography .ant-typography p {
 
} */
div:where(.css-dev-only-do-not-override-110d0e6).ant-typography,
:where(.css-dev-only-do-not-override-110d0e6).ant-typography p {
  margin-bottom: 0 !important;
}

#containerChat span[data-type="mention"] {
  opacity: 0.5;
  height: 24px;
  box-shadow: #0d0d0d;
  box-decoration-break: clone;
  margin-right: 2px;
  user-select: all;
  border-radius: 1rem;
  padding: 0.1rem 0.3rem;
  margin: 0 3px;
}
#containerChat span[data-type="mention"][current-user="NO"] {
  opacity: 1;

  color: #0366d6;
  border: 1px solid #0366d6;
  background-color: rgb(219, 234, 254);
}
#containerChat span[data-type="mention"][current-user="ALL"] {
  opacity: 1;

  color: #008000;
  background-color: rgba(0, 128, 0, 0.1);
  border: 1px solid #008000;
}

#containerChat span[data-type="mention"][current-user="ME"] {
  opacity: 1;

  color: #ff4d4f;
  background-color: rgba(255, 128, 130, 0.1);
  border: 1px solid #ff4d4f;
}
.message > ul {
  display: block;
  list-style-type: disc !important;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
}
.message > ul > li ul {
  list-style-type: circle !important;
}
.message > ul > li ul > li ul {
  list-style-type: square !important;
}
.mentionHover:hover {
  cursor: pointer;
  opacity: 0.6;
}

#containerChat span[data-type="mention"] > span {
  margin: 0 3px;
}
span[aria-label="emoji"] {
  font-size: 19px;
}

span[aria-label="hashtag"] {
  color: rgb(59 130 246);
  text-decoration: underline;
  cursor: pointer;
}

a[aria-label="link"], span[aria-label="phone-number"],span[aria-label="date"] {
  color: rgb(59 130 246);
  text-decoration: underline;
  cursor: pointer;
}
a[aria-label="link"]:hover,span[aria-label="date"]:hover, span[aria-label="phone-number"]:hover {
  opacity: 0.8;
}

span[aria-label="hashtag"]:hover {
  opacity: 0.8;
}
.message code {
  background-color: rgba(#616161, 0.1);
  color: #616161;
}

.message pre {
  background: #0d0d0d;
  color: #fff;
  font-family: "JetBrainsMono", monospace;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
}
.message code {
  color: inherit;
  padding: 0;
  background: none;
  font-size: 0.8rem;
}
.message > p > strong,em,u,s {
 margin-left: 2px;
}
