/**
 * @name ElementItem
 *
 * @description `ElementItem` component is responsible for displaying the module element card on kanban.
 *
 * @returns {JSX.Element} Element card.
 */

//React and 3rd party imports
import { useMemo, useState, useRef, forwardRef } from "react";
import {
  DeleteOutlined,
  EditOutlined,
  MessageOutlined,
  EyeOutlined,
  MoreOutlined,
  RestOutlined,
  MinusOutlined,
  PlusOutlined,
  SwapOutlined,
  TrademarkOutlined,
  CheckCircleOutlined,
  QuestionCircleOutlined,
  FileSearchOutlined,
  CloseCircleOutlined,
} from "@ant-design/icons";
import { Button, Card, Tooltip, Typography, Dropdown, Popconfirm } from "antd";
import { TbExchange, TbFlag3, TbFlag3Filled } from "react-icons/tb";
import { FiInfo } from "react-icons/fi";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { useNavigate, useLocation } from "react-router-dom";
import { FiCopy } from "react-icons/fi";
import { RiUserReceivedLine } from "react-icons/ri";
import { RxReset } from "react-icons/rx";
import { MdReplay } from "react-icons/md";
import { TbClockExclamation, TbClockCheck } from "react-icons/tb";

//Common imports
import { setOpenTaskRoomDrawer } from "../../../../new-redux/actions/tasks.actions/handleTaskRoomDrawer";
import { getName } from "../../../layouts/chat/utils/ConversationUtils";
import getContactDataAndDispatch from "../../../clients&users/helpers/getContactDataAndDispatch";
import { URL_ENV } from "index";
import { AvatarChat } from "components/Chat";
import Confirm from "components/GenericModal";
import { EXTENSIONS_ARRAY } from "pages/tasks/helpers/calculateSum";
import ActionsComponent from "pages/tasks/ActionsComponent";
import { isGuestConnected } from "utils/role";
import { getIcon } from "pages/tasks/helpers/kanbanHelpers";
import FieldRenderer from "./FieldRenderer";
import MainService from "services/main.service";

const ElementItem = forwardRef(({
  id,
  setOpenDrawerUpdate,
  setElementDetailToUpdate,
  content,
  handleDeleteElement,
  stageColor,
  setModalType,
  setOpenModal,
  setActiveRoomId,
  setElementInfo,
  setOpenDrawerInfo,
  closeTicketForGuest,
  isFinalStage,
  reopenTicketForGuest,
  isResolved,
}, ref) => {
  const [cardContent, setCardContent] = useState(content);
  const [sliceIndex, setSliceIndex] = useState(4);
  const [expandedCards, setExpandedCards] = useState([]);
  const [activeCard, setActiveCard] = useState({
    activeCardId: null,
    showLoader: false,
  });

  const user = useSelector((state) => state?.user?.user);

  const contentRef = useRef(null);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const [t] = useTranslation("common");

  //Handle copy element link on clipboard.
  const copyElementLinkToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
    } catch (err) {
      console.error("Failed to copy: ", err);
    }
  };

  //Handle show info drawer (guest mode).
  const handleShowMoreInfo = () => {
    setElementInfo({
      id: cardContent?.element_id,
      label: cardContent?.element_info?.label_data,
      familyId: cardContent?.family_id,
    });
    setOpenDrawerInfo(true);
  };

  //Handle open discussion room.
  const handleOpenRoom = () => {
    dispatch(setOpenTaskRoomDrawer(true));
    setElementDetailToUpdate({
      id: cardContent?.element_id,
      label: cardContent?.element_info?.label_data,
    });
    setActiveRoomId(cardContent?.element_id);
  };

  //More options list.
  const items = useMemo(() => {
    // Base items without conditionals
    const baseItems = [
      {
        label: t("layout_profile_details.overview"),
        key: "4",
        icon: getIcon(EyeOutlined),
      },
      {
        label: t("voip.moreInfo"),
        key: "moreInfo",
        icon: <FiInfo className="h-4 w-4 text-slate-400" />,
      },
      {
        label: t("updateElement"),
        key: "3",
        icon: getIcon(EditOutlined),
      },
      {
        disabled: cardContent?.can_create_room !== 1,
        label: "Discussion",
        key: "5",
        icon: getIcon(MessageOutlined),
      },
      {
        label: t("vue360.copyLink"),
        key: "1",
        icon: <FiCopy className="text-[16px] text-slate-400" />,
        danger: false,
      },
    ];

    // Conditionally add guest items
    const guestItems = isGuestConnected()
      ? {
          label: "Close",
          key: "6",
          icon: getIcon(CheckCircleOutlined),
        }
      : null;

    // Conditionally add ticket-related items
    const ticketItems =
      location?.pathname === "/tickets"
        ? [
            {
              label: t("helpDesk.move"),
              key: "deplacerByOne",
              icon: getIcon(TbExchange),
            },
            {
              label: t("helpDesk.transfer"),
              key: "transfer",
              icon: getIcon(SwapOutlined),
            },
            {
              label: t("helpDesk.assign"),
              key: "assign",
              icon: <RiUserReceivedLine className="h-4 w-4 text-slate-400" />,
            },
            {
              label: t("helpDesk.relaunch"),
              key: "launch",
              icon: getIcon(TrademarkOutlined),
            },
            {
              label: t("helpDesk.handOver"),
              key: "remettre",
              icon: getIcon(RxReset),
            },
          ]
        : [];

    // Always append the "Delete" item at the end
    const deleteItem = {
      label:
        location?.pathname === "/tickets"
          ? t("helpDesk.moveToTrash")
          : t("wiki.Delete"),
      key: "2",
      icon: <DeleteOutlined style={{ fontSize: "16px" }} />,
      danger: true,
    };

    // Combine all items, ensuring the "Delete" button is always at the end
    return [...baseItems, guestItems, ...ticketItems, deleteItem].filter(
      Boolean
    );
  }, [cardContent, location, t]);

  // Handle click on dropdown item.
  const handleCardDropdownClick = (e) => {
    // Utility function for setting modal type and element details
    const setModal = (type) => {
      setModalType(type);
      setElementDetailToUpdate({
        id: cardContent?.element_id,
        label: cardContent?.element_info?.label_data,
      });
      setOpenModal(true);
    };

    // Action map for handling different keys
    const actionMap = {
      1: () => {
        copyElementLinkToClipboard(
          `${URL_ENV?.REACT_APP_DOMAIN}${location?.pathname}/${cardContent?.element_id}`
        );
      },
      2: () => {
        Confirm(
          `Delete "${cardContent?.element_info?.label_data}" `,
          "Confirm",
          <RestOutlined style={{ color: "red" }} />,
          async function func() {
            await handleDeleteElement(
              cardContent?.element_id,
              Number(cardContent?.family_id)
            );
          },
          true
        );
      },
      3: () => {
        setOpenDrawerUpdate(true);
        setElementDetailToUpdate({
          id: cardContent?.element_id,
          label: cardContent?.element_info?.label_data,
        });
      },
      4: openOverview,
      5: handleOpenRoom,
      transfer: () => setModal("transfer"),
      assign: () => setModal("assign"),
      launch: () => setModal("launch"),
      deplacerByOne: () => setModal("deplacerByOne"),
      remettre: () => setModal("remettre"),
      moreInfo: handleShowMoreInfo,
    };

    // Execute the corresponding action based on the key
    if (e?.key && actionMap[e.key]) {
      actionMap[e.key]();
    }
  };

  //Handle display deal status (won/lost).
  const displayStatus = () => (
    <div className="flex flex-col">
      <section className="flex flex-row">
        <label className="mr-1 italic ">{t("helpDesk.status")}: </label>
        <p>
          {Number(cardContent?.status) === 0
            ? t("sales.failStatus")
            : t("sales.successStatus")}
        </p>
      </section>
      <section className="flex flex-row">
        <label className="mr-1 italic ">{t("sales.reason")}: </label>
        <p>{cardContent?.closing_reason?.label}</p>
      </section>
    </div>
  );

  //Open overview for more details.
  const openOverview = () => {
    getContactDataAndDispatch(
      cardContent?.family_id,
      cardContent?.element_info?.label_data,
      { key: cardContent?.element_id },
      [],
      dispatch,
      location?.pathname,
      navigate
    );
  };

  const getMoreValuesToShow = async (kanbanId) => {
    try {
      setActiveCard((prev) => ({ ...prev, showLoader: true }));
      const response = await MainService.getKanbanElementMoreValues(kanbanId);
      setCardContent(response?.data);
    } catch (error) {
      console.log(`Error: ${error}`);
    } finally {
      setActiveCard({
        activeCardId: null,
        showLoader: false,
      });
    }
  };

  const handleExpandCollapseCard = (kanbanId) => {
    let idExists = expandedCards?.find((el) => el === kanbanId);
    if (idExists) {
      setExpandedCards((prev) => prev?.filter((el) => el !== idExists));
      setCardContent({
        ...cardContent,
        element_info: {
          ...cardContent?.element_info,
          info: cardContent?.element_info?.info?.slice(0, 4),
        },
      });
    } else {
      setExpandedCards((prev) => [...prev, kanbanId]);
    }
  };

  const handleClickOnShowMore = (kanbanId) => {
    setActiveCard((prev) => ({
      ...prev,
      activeCardId: kanbanId,
    }));
    if (!expandedCards?.includes(cardContent?.kanban_id)) {
      getMoreValuesToShow(kanbanId);
    }
    handleExpandCollapseCard(kanbanId);
  };

  // Pass the ref to the top-level Card component
  return (
    <Card
      ref={ref}
      key={id}
      hoverable
      className="taskItem-card"
      style={{
        willChange: "unset",
        width: "100%",
        borderTopColor: stageColor ? stageColor : "#bfbfbf",
        borderTopWidth: "2px",
      }}
      //Display actions footer for the guest user.
      actions={
        isGuestConnected()
          ? [
              <Tooltip title={t("voip.moreInfo")}>
                <Dropdown
                  trigger={["click"]}
                  menu={{
                    items: [
                      {
                        key: "kanban_guest_overview",
                        label: t("layout_profile_details.overview"),
                        icon: <EyeOutlined style={{ fontSize: "14px" }} />,
                        onClick: () => openOverview(),
                      },
                      {
                        key: "kanban_guest_edit",
                        label: t("tasks.detailsTab"),
                        icon: (
                          <FileSearchOutlined style={{ fontSize: "14px" }} />
                        ),
                        onClick: () => handleShowMoreInfo(),
                      },
                    ],
                  }}
                >
                  <Button type="text" shape="circle" icon={<MoreOutlined />} />
                </Dropdown>
              </Tooltip>,
              <Tooltip title="Discussion">
                <Button
                  type="text"
                  shape="circle"
                  onClick={handleOpenRoom}
                  icon={<MessageOutlined />}
                  disabled={cardContent?.can_create_room !== 1}
                />
              </Tooltip>,
              isFinalStage ? (
                <Popconfirm
                  title={t("helpDesk.reopenTicketConfirmMessage")}
                  icon={
                    <QuestionCircleOutlined
                      style={{
                        color: "#faad14",
                      }}
                    />
                  }
                  onConfirm={() =>
                    reopenTicketForGuest(cardContent?.element_id)
                  }
                >
                  <Tooltip
                    title={t("helpDesk.reopenTicket")}
                    placement="bottom"
                  >
                    <Button
                      type="text"
                      shape="circle"
                      icon={<MdReplay style={{ fontSize: "15px" }} />}
                    />
                  </Tooltip>
                </Popconfirm>
              ) : (
                <Popconfirm
                  title={t("helpDesk.closeTicketConfirmMessage")}
                  icon={
                    <QuestionCircleOutlined
                      style={{
                        color: "#faad14",
                      }}
                    />
                  }
                  disabled={!isResolved}
                  onConfirm={() =>
                    closeTicketForGuest(
                      cardContent?.element_id,
                      cardContent?.element_info?.label_data
                    )
                  }
                >
                  <Tooltip title={t("helpDesk.closeTicket")} placement="bottom">
                    <Button
                      type="text"
                      shape="circle"
                      icon={<CheckCircleOutlined />}
                      disabled={!isResolved}
                    />
                  </Tooltip>
                </Popconfirm>
              ),
              <Tooltip
                title={
                  cardContent?.owner_id !== user?.id
                    ? t("helpDesk.cancelNoAuth")
                    : t("localisation.cancel")
                }
              >
                <Button
                  type="text"
                  shape="circle"
                  onClick={() => {
                    Confirm(
                      `${t("localisation.cancel")} "${
                        cardContent?.element_info?.label_data
                      }" ?`,
                      "Confirm",
                      <CloseCircleOutlined style={{ color: "red" }} />,
                      async function func() {
                        await handleDeleteElement(
                          cardContent?.element_id,
                          Number(cardContent?.family_id)
                        );
                      },
                      true
                    );
                  }}
                  icon={<CloseCircleOutlined />}
                  disabled={isFinalStage || cardContent?.owner_id !== user?.id}
                />
              </Tooltip>,
            ]
          : null
      }
    >
      <Card.Meta
        title={
          <div className="flex w-full items-center">
            {location?.pathname === "/settings/users" && (
              <ActionsComponent elementValue={cardContent?.element_info?.user}>
                <AvatarChat
                  className={"mx-1.5 flex items-center justify-center"}
                  fontSize={"0.875rem"}
                  height={"32px"}
                  width={"32px"}
                  url={`${
                    URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                  }${cardContent?.element_info?.user?.avatar}`}
                  hasImage={EXTENSIONS_ARRAY?.includes(
                    cardContent?.element_info?.user?.avatar?.split(".")?.pop()
                  )}
                  name={getName(
                    cardContent?.element_info?.user?.label,
                    "avatar"
                  )}
                  type="user"
                />
              </ActionsComponent>
            )}
            <Typography.Text
              onClick={() => {
                openOverview();
              }}
              className={`mr-3 text-[16px] text-[#1677ff] hover:text-[#69b1ff]`}
              ellipsis={{
                tooltip: true,
              }}
            >
              {cardContent?.element_info?.label_data}
            </Typography.Text>
            {location?.pathname === "/tickets" ? (
              <Tooltip
                title={
                  <div className="flex flex-col justify-center">
                    <span className="font-semibold">
                      {cardContent?.is_overdue
                        ? t("helpDesk.slaDepassed")
                        : t("helpDesk.slaRespected")}
                    </span>
                    <span>
                      {
                        cardContent?.element_info?.info?.find((el) =>
                          el?.hasOwnProperty("SLA")
                        )?.SLA
                      }
                    </span>
                  </div>
                }
              >
                {cardContent?.is_overdue ? (
                  <TbClockExclamation
                    style={{ color: "red", fontSize: "16px" }}
                  />
                ) : (
                  <TbClockCheck style={{ color: "green", fontSize: "16px" }} />
                )}
              </Tooltip>
            ) : location?.pathname !== "/tickets" &&
              cardContent?.status !== null ? (
              <Tooltip
                title={displayStatus()}
                overlayStyle={{ zIndex: "99999" }}
              >
                <TbFlag3Filled
                  className={`text-[18px] ${
                    cardContent?.can_update_task === 0
                      ? "cursor-not-allowed"
                      : "cursor-pointer"
                  } ${
                    Number(cardContent?.status) === 0
                      ? "text-[#f5222d]"
                      : "text-[#52c41a]"
                  }`}
                />
              </Tooltip>
            ) : (
              <Tooltip
                title={t("sales.normalStatus")}
                overlayStyle={{ zIndex: "99999" }}
              >
                <TbFlag3 className="cursor-pointer text-[18px] text-[#bfbfbf]" />
              </Tooltip>
            )}
            <div className="ml-auto flex items-center">
              {!isGuestConnected() && (
                <Dropdown
                  trigger={["click"]}
                  menu={{
                    items: items?.filter((el) => el !== null),
                    onClick: handleCardDropdownClick,
                  }}
                >
                  <Button
                    type="text"
                    size="small"
                    shape="circle"
                    icon={<MoreOutlined />}
                  />
                </Dropdown>
              )}
            </div>
          </div>
        }
      />
      <div
        className={`relative flex w-full flex-col justify-center ${
          location?.pathname === "/settings/users" ? "mt-3" : ""
        }`}
      >
        {/* Content section */}
        <div
          ref={contentRef}
          className={`hide-scrollbar-element-card flex flex-col overflow-hidden transition-all duration-500 ease-in-out`}
          style={{
            maxHeight: expandedCards?.includes(cardContent?.kanban_id)
              ? `${contentRef.current.scrollHeight}px`
              : "150px",
            marginLeft: 0,
          }}
        >
          {cardContent?.element_info?.info?.map((item, index) => (
            <FieldRenderer
              key={`element_card_${index}`}
              item={item}
              index={index}
              setSliceIndex={setSliceIndex}
              sliceIndex={sliceIndex}
              content={cardContent}
            />
          ))}
        </div>
        {/* Show more content button */}
        {cardContent?.element_has_more && (
          <div className="inline-block self-center">
            <Button
              type="link"
              icon={
                expandedCards?.includes(cardContent?.kanban_id) ? (
                  <MinusOutlined />
                ) : (
                  <PlusOutlined />
                )
              }
              onClick={() => {
                handleClickOnShowMore(cardContent?.kanban_id);
              }}
              loading={
                activeCard?.activeCardId === cardContent?.kanban_id &&
                activeCard?.showLoader
              }
            >
              {expandedCards?.includes(cardContent?.kanban_id)
                ? t("tasks.elementShowLessBtn")
                : t("tasks.elementShowMoreBtn", {
                    totalHidden: cardContent?.fields_count - 4,
                  })}
            </Button>
          </div>
        )}
      </div>
    </Card>
  );
});

ElementItem.displayName = 'ElementItem';

export default ElementItem;
