import { Card, Skeleton, Avatar } from "antd";
const { Meta } = Card;
//
const MessagingLoader = ({ numberOfMessaging }) => {
  return new Array(numberOfMessaging).fill(null).map((_, i) => (
    <Card
      key={i}
      size="small"
      style={{
        width: 300,
      }}
      actions={[
        <div className="mx-4 my-2 grid grid-flow-col space-x-2">
          <Skeleton.Avatar active size={"small"} />
          <Skeleton.Input active size={"small"} block />
        </div>,
      ]}
    >
      <Skeleton loading={true} avatar active>
        <Meta
          style={{
            padding: "1rem",
          }}
          avatar={<Avatar src="https://joeschmoe.io/api/v1/random" />}
          title="Card title"
          description="This is the description"
        />
      </Skeleton>
    </Card>
  ));
};

export { MessagingLoader };
