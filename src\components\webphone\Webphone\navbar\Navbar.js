import React, { useState, useEffect } from "react";
import { Badge } from "antd";
function Navbar({ setTap, tap, navbar, show,notifAppel,notifAppelFromWB,deleteNotif,showNotif}) {

  function setTabJournal ()
  {
    setTap("logs")
    deleteNotif()

  }
  return (
    <div>
      <div
        className="dark:bg-gray-800 grid grid-cols-4"  
        style={{
          backgroundColor: "#111827",
          position: "absolute",
          width: navbar ? "100%" : "40px",
          bottom: "-1PX",
          borderRadius: navbar ? "0  0 1rem 1rem" : "29px 0px 0px 29px",
          borderTop:"1px solid #f9f9f929",
          height:"63px"
        }}
      >
        {show ? (
          <div
            className="tooltip"
            onClick={() => setTap("phone")}
            style={{
              backgroundColor:tap === "phone" ? "rgb(41 48 61)" : "", 
              color:tap === "phone" ? "white" : "hsla(0,0%,100%,.4)", 
              cursor: "pointer",
              paddingTop:"10px",
              borderRadius:"0 0 0 1rem"
            }}
          >
      
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6"
             style={{
                margin: "auto",
              }}>
            <path fillRule="evenodd" d="M3 6a3 3 0 013-3h2.25a3 3 0 013 3v2.25a3 3 0 01-3 3H6a3 3 0 01-3-3V6zm9.75 0a3 3 0 013-3H18a3 3 0 013 3v2.25a3 3 0 01-3 3h-2.25a3 3 0 01-3-3V6zM3 15.75a3 3 0 013-3h2.25a3 3 0 013 3V18a3 3 0 01-3 3H6a3 3 0 01-3-3v-2.25zm9.75 0a3 3 0 013-3H18a3 3 0 013 3V18a3 3 0 01-3 3h-2.25a3 3 0 01-3-3v-2.25z" clipRule="evenodd" />
             </svg>

            <p
              style={{
                fontSize: ".75rem",
                lineHeight: "1rem",
                textAlign: "center",
              }}
            >
              Clavier
            </p>
          </div>
        ) : null}
        <div
          className="tooltip"
          onClick={() => setTabJournal()}
          style={{
            backgroundColor:tap === "logs" ? "rgb(41 48 61)" : "", 
            color:tap === "logs" ? "white" : "hsla(0,0%,100%,.4)", 
            zIndex:"1",
            cursor: "pointer",
            paddingTop:"10px",
          }}
        >

          {showNotif ?
                     <Badge count={notifAppel+notifAppelFromWB}>
                     <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6"
                        style={{
                         margin: "auto",
                         color:tap === "logs" ? "white" : "hsla(0,0%,100%,.4)", 
                       }}>
                     <path fillRule="evenodd" d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25zM12.75 6a.75.75 0 00-1.5 0v6c0 .414.336.75.75.75h4.5a.75.75 0 000-1.5h-3.75V6z" clipRule="evenodd" />
                     </svg>
                     </Badge>
                     :
                     <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6"
                        style={{
                         margin: "auto",
                         color:tap === "logs" ? "white" : "hsla(0,0%,100%,.4)", 
                       }}>
                     <path fillRule="evenodd" d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25zM12.75 6a.75.75 0 00-1.5 0v6c0 .414.336.75.75.75h4.5a.75.75 0 000-1.5h-3.75V6z" clipRule="evenodd" />
                     </svg>

          }


          <p
            style={{
              fontSize: ".75rem",
              lineHeight: "1rem",
              textAlign: "center",
            }}
          >
            Journal
          </p>
        </div>

        <div
          className="tooltip"
          onClick={() => setTap("contacts")}
          style={{
            backgroundColor:tap === "contacts" ? "rgb(41 48 61)" : "", 
            color:tap === "user" ? "white" : "hsla(0,0%,100%,.4)", 

            cursor: "pointer",
            paddingTop:"10px",
          }}
        >
      

          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6"
                style={{
                  margin: "auto",
                  color:tap === "contacts" ? "white" : "hsla(0,0%,100%,.4)", 

                }}>
          <path fillRule="evenodd" d="M18.685 19.097A9.723 9.723 0 0021.75 12c0-5.385-4.365-9.75-9.75-9.75S2.25 6.615 2.25 12a9.723 9.723 0 003.065 7.097A9.716 9.716 0 0012 21.75a9.716 9.716 0 006.685-2.653zm-12.54-1.285A7.486 7.486 0 0112 15a7.486 7.486 0 015.855 2.812A8.224 8.224 0 0112 20.25a8.224 8.224 0 01-5.855-2.438zM15.75 9a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0z" clipRule="evenodd" />
          </svg>


          <p
            style={{
              fontSize: ".75rem",
                lineHeight: "1rem",
              textAlign: "center",
            }}
          >
            Contacts
          </p>
        </div>
        {show ? (
          <div
            className="tooltip"
            onClick={() => setTap("voicecall")}
            style={{
              backgroundColor:tap === "voicecall" ? "rgb(41 48 61)" : "", 
              color:tap === "voicecall" ? "white" : "hsla(0,0%,100%,.4)", 

              cursor: "pointer",
              paddingTop:"10px",
              borderRadius:"0 0 1rem 0"

            }}
          >

            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="w-6 h-6"
              style={{
                margin: "auto",
                
              }}
              aria-hidden="true"
            >
              <circle cx="5.5" cy="11.5" r="4.5"></circle>
              <circle cx="18.5" cy="11.5" r="4.5"></circle>
              <line x1="5.5" y1="16" x2="18.5" y2="16"></line>
            </svg>

            <p
              style={{
                fontSize: ".75rem",
                lineHeight: "1rem",
                textAlign: "center",
              }}
            >
              Messagerie
            </p>
          </div>
        ) : null}
        {/* <div
          className="tooltip"
          onClick={() => setTap("contacts")}
          style={{
            margin: "auto",
            borderRadius: "5px",
            // width: "57px",
            cursor: "pointer",
          }}
        >
          <span className="tooltiptext">Contacts</span>

          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            strokeWidth={1}
            style={{
              color: tap === "contacts" ? "#21ba90" : "white",
              margin: "auto",
            }}
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
            />
          </svg>
          <p
            style={{
              fontSize: "xx-small",
              color: "white",
              textAlign: "center",
            }}
          >
            Contacts
          </p>
        </div> */}
      </div>
    </div>
  );
}

export default Navbar;
