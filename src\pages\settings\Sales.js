import React, { useEffect, useState, useLayoutEffect } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { generateAxios } from "../../services/axiosInstance";
import ReasonSales from "../../components/ReasonSales";
import { toastNotification } from "../../components/ToastNotification";
import { setSearch } from "../../new-redux/actions/menu.actions/menu";
import TabsDetails from "../../components/Tabs";
import { URL_ENV } from "index";

const Sales = () => {
  const [dataConclude, setDataConclude] = useState([]);
  const [dataLoss, setDataLoss] = useState([]);
  const { search } = useSelector((state) => state.form);
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(true);
  const [editingKey, setEditingKey] = useState("");
  const [keyTab, setKeyTab] = useState("");
  const navigate = useNavigate();
  const { pathname } = useLocation();
  useEffect(() => {
    const getSales = async () => {
      setLoading(true);
      try {
        const {
          data: { data },
        } = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get("/reasons");
        if (data.length > 0) {
          setDataConclude(
            data
              .filter((el) => el.reason_type == "1")
              .map((el, i) => ({ ...el, rank: i + 1, key: el.id }))
          );
          setDataLoss(
            data
              .filter((el) => el.reason_type == "0")
              .map((el, i) => ({ ...el, rank: i + 1, key: el.id }))
          );
        }
        setLoading(false);
      } catch (err) {
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
        setLoading(false);
      }
    };
    getSales();
    return () => dispatch(setSearch(""));
  }, []);

  useLayoutEffect(() => {
    if (pathname == "/settings/sales") {
      setKeyTab("1");
    } else if (pathname == "/settings/sales/currencies") {
      setKeyTab("3");
    } else {
      setKeyTab("1");
    }
  }, []);

  useEffect(() => {
    if (keyTab == 1) {
      dispatch(setSearch(""));
      setEditingKey("");
      navigate("/settings/sales");
    } else if (keyTab == 2) {
      dispatch(setSearch(""));
      setEditingKey("");
      navigate("/settings/sales");
    } else if (keyTab == 3) {
      dispatch(setSearch(""));
      navigate("/settings/sales/currencies");
    }
  }, [keyTab, navigate]);
  const filteredDataConclude = dataConclude.filter((item) => {
    return item.label.toLowerCase().includes(search.toLowerCase());
  });
  const filteredDataLoss = dataLoss.filter((item) => {
    return item.label.toLowerCase().includes(search.toLowerCase());
  });
  const items = [
    {
      key: "1",
      label: (
        <div onClick={() => navigate("/settings/sales")}>
          {t("sales.ReasonToconclude")}
        </div>
      ),
      children: (
        <ReasonSales
          data={filteredDataConclude}
          setData={setDataConclude}
          reason_type="1"
          editingKey={editingKey}
          setEditingKey={setEditingKey}
          keyTab={keyTab}
          btnText={t("sales.addReasonToconclude")}
          load={loading}
        />
      ),
    },
    {
      key: "2",
      label: (
        <div onClick={() => navigate("/settings/sales")}>
          {t("sales.ReasonforLoss")}
        </div>
      ),
      children: (
        <ReasonSales
          data={filteredDataLoss}
          setData={setDataLoss}
          reason_type="0"
          editingKey={editingKey}
          setEditingKey={setEditingKey}
          keyTab={keyTab}
          btnText={t("sales.addReasonforLoss")}
          load={loading}
        />
      ),
    },
    {
      key: "3",
      label: (
        <div onClick={() => navigate("/settings/sales/currencies")}>
          {t("sales.currencies")}
        </div>
      ),
      // children: <TableCurrencies />,
    },
  ];

  return (
    <>
      {keyTab ? (
        <TabsDetails items={items} keyTab={keyTab} setKey={setKeyTab} />
      ) : (
        ""
      )}
    </>
  );
};

export default Sales;
