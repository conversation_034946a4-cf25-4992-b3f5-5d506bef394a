/* General Styles for Ant List Members */
/* .ant-list-item-meta-title {
  font-weight: 600 !important;
  color: #000 !important;
} */

.ant-list.notesList .ant-list-item .ant-list-item-meta {
  display: flex;
  align-items: center;
}

.ant-list.notesList
  .ant-list-item
  .ant-list-item-meta
  .ant-list-item-meta-title {
  margin-bottom: 0;
  color: #1e293b; /* text-slate-900 equivalent */
}

.ant-list.notesList
  .ant-list-item
  .ant-list-item-meta
  .ant-list-item-meta-description {
  color: #64748b; /* text-slate-500 equivalent */
}

.ant-list.notesList
  .ant-list-item.isActive
  .ant-list-item-meta
  .ant-list-item-meta-title {
  font-weight: 600;
  color: #334155; /* text-slate-700 equivalent */
}

.ant-list.notesList
  .ant-list-item.isActive
  .ant-list-item-meta
  .ant-list-item-meta-description {
  font-weight: 500;
  color: #334155; /* text-slate-700 equivalent */
}

.ant-list.notesList .ant-list-item.isActive .time {
  font-weight: 500;
  color: #334155; /* text-slate-700 equivalent */
}

/* Lock Icon Styling (Always Visible) */
.ant-list.notesList .ant-list-item .lock-icon {
  position: absolute; /* Ensures proper placement */
  right: 1rem; /* Adjust for spacing from the right */
  top: 50%; /* Center vertically */
  transform: translateY(-50%);
  color: #6b7280; /* Gray color */
  z-index: 1; /* Ensure it is above other elements */
  transition: color 0.3s ease-in-out;
}

.ant-list.notesList .ant-list-item .noteLockIcon {
  position: absolute;
  right: 1rem;
  top: 25%;
  transform: translateY(-50%);
  z-index: 1;
}
.ant-list.notesList .ant-list-item .noteTime {
  position: absolute;
  right: 1rem;
  top: 25%;
  transform: translateY(-50%);
  z-index: 1;
}

.ant-list.notesList .ant-list-item .noteLock {
  position: absolute;
  right: 2.5rem;
  top: 75%;
  transform: translateY(-50%);
  z-index: 1;
}

.ant-list.notesList .ant-list-item .noteMessage {
  position: absolute;
  right: 1rem;
  top: 75%;
  transform: translateY(-50%);
  z-index: 1;
}

/* Optionally change color on hover */
.ant-list.notesList .ant-list-item:hover .lock-icon {
  color: #2563eb; /* Blue on hover */
}

/* List Item Styling */
.ant-list.notesList .ant-list-item {
  position: relative; /* Required for absolute positioning of children */
  display: flex; /* Align content horizontally */
  align-items: center; /* Center content vertically */
  justify-content: space-between; /* Space content out */
  /* padding-right: 2rem; Leave space for lock icon */
  padding-left: 0.6rem;
  transition: all 0.3s ease-in-out;
}

.ant-list.notesList .ant-list-item:hover {
  @apply  border-b-0 transition-all  duration-300 hover:bg-slate-200;
}

.ant-list.notesList .ant-list-item.isActive {
  background-color: #e2e8f0; /* Active item background */
}

/* Title Truncation for Consistent Layout */
.ant-list.notesList
  .ant-list-item
  .ant-list-item-meta
  .ant-list-item-meta-title {
  /* max-width: calc(100% - 3rem); Adjust width for lock icon */
  overflow: hidden; /* Hide overflowing text */
  text-overflow: ellipsis; /* Add ellipsis for overflowed text */
  white-space: nowrap; /* Prevent wrapping */
}

/* Avatar Styling */
.ant-list.notesList
  .ant-list-item
  .ant-list-item-meta
  .ant-list-item-meta-avatar {
  margin-right: 0.5rem;
}

.ant-list.notesList
  .ant-list-item
  .ant-list-item-meta
  .ant-badge
  .ant-badge-dot {
  height: 0.5rem;
  width: 0.5rem;
  box-shadow: 0 0 0 1.5px #ffffff;
}

/* Divider Styling */
.ant-divider .ant-divider-inner-text {
  font-size: 0.75rem; /* Text-xs equivalent */
  letter-spacing: 0.05em;
  color: #64748b; /* Text-slate-500 equivalent */
}

.ant-divider-vertical {
  margin: 0 0.25rem;
  border-color: #d1d5db; /* Border-slate-300 equivalent */
}

/* Additional Utility Styles */
.ant-list.notesList .ant-list-item {
  margin: 0;
  border-bottom: 0;
  transition: all 0.3s ease-in-out;
}

.ant-list.notesList .ant-list-item .ant-list-item-action {
  margin-left: 0.25rem !important;
}

.ant-list.notesList
  .ant-list-item
  .ant-list-item-meta
  .ant-list-item-meta-description {
  font-size: 0.875rem; /* Text-sm equivalent */
  color: #475569; /* Text-slate-600 equivalent */
}
