import React, {
  useState,
  useEffect,
  useRef,
  useMemo,
  useLayoutEffect,
} from "react";
import {
  Button,
  Form,
  Input,
  Switch,
  Tabs,
  Badge,
  Spin,
  Typography,
  message,
  Segmented,
  Space,
  Dropdown,
  ConfigProvider,
} from "antd";
import ReactQuill, { Quill } from "react-quill";
import ImageResize from "quill-image-resize-module-react";
import "react-quill/dist/quill.snow.css";
import quillEmoji from "react-quill-emoji";
import "react-quill-emoji/dist/quill-emoji.css";

import { useTranslation } from "react-i18next";
// import quillEmoji from 'react-quill-emoji'
// import 'react-quill-emoji/dist/quill-emoji.css'
import MainService from "../../services/main.service";
import { toastNotification } from "../../components/ToastNotification";
import {
  CopyOutlined,
  DownOutlined,
  EyeInvisibleOutlined,
  EyeOutlined,
  RestOutlined,
} from "@ant-design/icons";
import { FiCopy } from "react-icons/fi";
import RichTextInput from "../../components/tiptap_richtext/RichTextInput";
import Confirm from "../../components/GenericModal";
import ImageUploader from "quill-image-uploader";
import { URL_ENV } from "index";
import { htmlToMarkdown } from "./parser";

const { TabPane } = Tabs;
const { Paragraph } = Typography;
// Quill.register("modules/imageUploader", ImageUploader);

const Content = ({
  selectedNode,
  folders,
  setFolders,
  nameGroup,
  nodeName,
  delete1,
  setSelectedNode,
  disabled,
  setDisabled,
  setGroupsWithBinders,
}) => {
  const [form] = Form.useForm();
  const [pageNameFr, setPageNameFr] = useState(selectedNode?.title_fr);
  const [pageNameEn, setPageNameEn] = useState(selectedNode?.title_en);
  const [contentFr, setContentFr] = useState(selectedNode?.content_fr);
  const quillRef = useRef(null);

  const [contentEn, setContentEn] = useState(selectedNode?.content_en);
  const [status, setStatus] = useState(selectedNode?.status);
  const [loading, setLoading] = useState(false);
  const [slug, setSlug] = useState("");
  const [slugUrlValue, setSlugUrlValue] = useState(selectedNode?.slug_url);
  const [screenfull, setScreenFull] = useState(false);

  const [t] = useTranslation("common");
  Quill.register("modules/imageResize", ImageResize);
  Quill.register(
    {
      "formats/emoji": quillEmoji.EmojiBlot,
      "modules/emoji-toolbar": quillEmoji.ToolbarEmoji,
      "modules/emoji-textarea": quillEmoji.TextAreaEmoji,
      "modules/emoji-shortname": quillEmoji.ShortNameEmoji,
    },
    true
  );
  // Quill.register('modules/imageResize', ImageResize)
  // console.log('ImageResize', ImageResize)
  // Quill.register(
  //   {
  //     'formats/emoji': quillEmoji.EmojiBlot,
  //     'modules/emoji-toolbar': quillEmoji.ToolbarEmoji,
  //     'modules/emoji-textarea': quillEmoji.TextAreaEmoji,
  //     'modules/emoji-shortname': quillEmoji.ShortNameEmoji,
  //   },
  //   true,
  // )

  const imageHandlers = () => {
    let input = document.getElementById("formItemPath_contentFr");
    input.onChange = async () => console.log("input", input);
  };

  const imageHandler = (e) => {
    if (quillRef?.current != null) {
      const editor = quillRef.current.getEditor();

      const input = document.createElement("input");
      input.setAttribute("type", "file");
      input.setAttribute("accept", "image/*");
      input.click();
      input.onchange = async function () {
        const file = input.files[0];

        const formData = new FormData();
        formData.append("upload", file);

        MainService.uploadImageWiki(formData).then((res) => {
          const range = editor.getSelection();
          const link = `${
            URL_ENV?.REACT_APP_BASE_URL +
            process.env.REACT_APP_SUFFIX_AUTH_IMAGE_FILE
          }uploads-wiki/${res.data.message.fileName}`;

          // this part the image is inserted
          // by 'image' option below, you just have to put src(link) of img here.
          editor.insertEmbed(range.index, "image", link);
          // resolve(res.data.data);
        });
      }.bind(this);
    }
  };

  const modules = useMemo(
    () => ({
      toolbar: {
        container: [
          [{ header: [1, 2, 3, 4, 5, 6, false] }],
          [{ font: [] }],
          [{ size: [] }],
          [{ color: [] }, { background: [] }],
          ["bold", "italic", "strike", "blockquote", "underline"],
          [
            { list: "ordered" },
            { list: "bullet" },
            { indent: "-1" },
            { indent: "+1" },
          ],
          ["link", "image", "video"],
          // ['emoji'],
          [{ align: [] }],
          ["clean"],
          ["code-block"],
          ["omega"],
        ],
        handlers: {
          image: imageHandler,
          omega: () => {
            screenfull ? setScreenFull(false) : setScreenFull(true);
          },
        },
      },
      // imageUploader : {
      //   upload: file => {
      //     return new Promise((resolve, reject) => {
      //       const formData = new FormData();
      //       formData.append("upload", file);
      //       MainService.uploadImageWiki(formData)
      //         .then((res) => {
      //           console.log(res.data.message.path);
      //           resolve(`${URL_ENV?.REACT_APP_BASE_URL+process.env.REACT_APP_SUFFIX_BASE_URL_FILE_PATH}public/storage/uploads-wiki/${res.data.message.fileName}`)
      //           // resolve(res.data.data);
      //         })
      //         .catch((err) => {
      //           reject("Upload failed");
      //           console.log(err);
      //         });
      //     });
      //   }
      // }

      // 'emoji-toolbar': true,
      // 'emoji-textarea': true,
      // 'emoji-shortname': true,
    }),
    [screenfull]
  );
  useEffect(() => {
    document.querySelector(".ql-tooltip").style.opacity = 0;
    const adjustTooltipPosition = () => {
      const tooltipElement = document.querySelector(".ql-tooltip");
      if (tooltipElement && !tooltipElement.classList.contains("ql-hidden")) {
        const left = parseFloat(tooltipElement.style.left);
        if (left < 0) {
          tooltipElement.style.left = "0px";
          setTimeout(() => {
            tooltipElement.style.opacity = 1;
          }, 100);
        } else {
          tooltipElement.style.opacity = 1;
        }
      }
    };

    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === "childList") {
          adjustTooltipPosition();
        }
      });
    });

    observer.observe(document.body, { childList: true, subtree: true });

    return () => {
      observer.disconnect();
    };
  }, []);
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === "Escape") {
        setScreenFull(false);
        // Ajoutez votre logique ici
      }
    };

    // Attache le gestionnaire d'événements à l'élément racine du composant
    document.addEventListener("keydown", handleKeyDown);

    // Nettoie le gestionnaire d'événements lorsque le composant est démonté
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, []);
  const generateSlug = (str) => {
    const slug = str
      .toLowerCase()
      .trim()
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .replace(/[^a-z0-9]+/g, "_")
      .replace(/(^-|-$)+/g, "");
    return slug;
  };
  const handlePaste = (e) => {
    const clipboardData = e.clipboardData || window.clipboardData;
    const plainText = clipboardData.getData("text/plain");
    const htmlText = clipboardData.getData("text/html");

    // Vérifier si le contenu collé contient des balises <img>
    if (htmlText.includes("<img")) {
      e.preventDefault();
      alert("Le collage d'images est désactivé dans cet éditeur.");
    } else {
      // Autoriser le collage du texte non lié aux images
      document.execCommand("insertText", false, plainText);
    }
  };

  const handleInputChange = (event) => {
    const inputValue = generateSlug(event.target.value);
    // const inputSlug = generateSlug(inputValue);
    setPageNameFr(inputValue);
    // setSlugUrlValue(generateSlug(inputSlug))
    // setSlugUrlValue(inputValue);
    form.setFieldsValue({ slugUrlValue: inputValue });
  };

  const handleCopyClick = () => {
    navigator.clipboard.writeText(
      `${generateSlug(nameGroup)}/${generateSlug(selectedNode.folder_title)}/${
        form.getFieldValue().slugUrlValue
      }`
    );
    message.success(t(`wiki.SlugUrlCopied`));
  };

  const createBlobFromBase64 = (htmlInput) => {
    let base64Regex = /src="(data:image\/[^;]+;base64[^"]+)"/g;
    let base64FromHtml = htmlInput.match(base64Regex);
    // const test = base64FromHtml[0]?.split(",");
    const binaryString = base64FromHtml[0].split('"'); // Binary data string
    const removeComma = atob(binaryString[1]?.split(",")[1]);
    const blobImg = new Blob([removeComma], { type: "image/png" }); // Create a BLOB object
    const myFile = new File(blobImg, { type: "image/png" });
    // console.log({ myFile });
  };

  const onFinish = (value) => {
    // createBlobFromBase64(contentFr);
    setLoading(true);
    // console.log("value", htmlToMarkdown(form.getFieldValue().contentFr));
    MainService.updatePage(selectedNode?.id, {
      title_fr: form.getFieldValue().pageNameFr,
      title_en: form.getFieldValue().pageNameEn,
      content_fr: form.getFieldValue().contentFr,
      content_en: form.getFieldValue().contentEn,
      slug_url: form.getFieldValue().slugUrlValue,
      meta_description_en: form.getFieldValue().meta_description_en,
      meta_description_fr: form.getFieldValue().meta_description_fr,
    })
      .then((res) => {
        const updatedTreeData = folders.map((node) => {
          node.children.map((doc) => {
            if (doc.id == res.data.data.id) {
              return (
                (doc.title = res.data.data.title_fr),
                (doc.title_en = res.data.data.title_en),
                (doc.title_fr = res.data.data.title_fr),
                (doc.content_fr = res.data.data.content_fr),
                (doc.content_en = res.data.data.content_en),
                (doc.status = res.data.data.status),
                (doc.slug_url = res.data.data.slug_url),
                (doc.meta_description_en = res.data.data.meta_description_en),
                (doc.meta_description_fr = res.data.data.meta_description_fr)
              );
            }
          });

          return node;
        });

        setFolders(updatedTreeData);
        setDisabled(true);
        setLoading(false);
        toastNotification(
          "success",
          form.getFieldValue().pageNameFr + t(`wiki.PageEditedSuccessfully`),
          "topRight"
        );
      })
      .catch((err) => {
        setLoading(false);
        console.log(err);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      });
  };

  const updateStatus = () => {
    MainService.updatePage(selectedNode?.id, {
      status: form.getFieldValue().status,
    });
  };
  const onFinishFailed = (value) => {
    console.log(value);
  };
  // useEffect(() => {
  //   const handleKeyDown = (event) => {
  //     if (event.key === "Escape" && screenfull) {
  //       // La touche "Escape" a été enfoncée
  //       setScreenFull(false);
  //       // Ajoutez votre logique ici
  //     }
  //   };
  //   var customButton = document.querySelector(".ql-omega");
  //   customButton.addEventListener("click", function () {
  //     if (screenfull) {
  //       setScreenFull(false);
  //       // screenfull.request();
  //     } else {
  //       // console.log("Screenfull not enabled");
  //       setScreenFull(true);
  //     }
  //   });
  //   // document.addEventListener("keydown", handleKeyDown);

  //   // return () => {
  //   //   if (screenfull) document.removeEventListener("keydown", handleKeyDown);
  //   // };
  // }, [screenfull]);
  const handleDrop = (event) => {
    event.preventDefault();
  };
  useEffect(() => {
    form.setFieldsValue({
      // slugUrlValue: generateSlug(selectedNode?.title_fr),
      slugUrlValue: selectedNode?.slug_url,
      pageNameFr: selectedNode?.title_fr,
      pageNameEn: selectedNode?.title_en,
      pageUrl: selectedNode?.name,
      contentFr: selectedNode?.content_fr,
      contentEn: selectedNode?.content_en,
      status: selectedNode?.status,
      //folderName: selectedNode?.folder_title,
      folderName: selectedNode?.folder_title,
      meta_description_fr: selectedNode?.meta_description_fr,
      meta_description_en: selectedNode?.meta_description_en,
      //.replace(/\s/g, ''),
    });
  }, [form, selectedNode]);

  const [value, setValue] = useState("1");
  const onChange = (e) => {
    setValue(e.target.value);
    console.log(htmlToMarkdown(e.target.value));
  };

  // console.log(form.getFieldValue().slugUrlValue)

  const updatePageName = (e) => {};
  // const updatePage = () => {
  //   MainService.updatePage(selectedNode?.id, {
  //     title: pageName,
  //     content_fr: contentFr,
  //     content_en: contentEn,
  //     status: status,
  //   })
  //     .then((res) => {
  //       console.log(res.data.data)
  //       const updatedTreeData = folders.map((node) => {
  //         console.log('children', node.children)
  //         // if (node.children[0].id == selectedNode?.id) {
  //         //   return {
  //         //     ...node,
  //         //     children: [
  //         //       {
  //         //         ...node.children,
  //         //         title: pageName,
  //         //         content_fr: contentFr,
  //         //         content_en: contentEn,
  //         //         status: status,
  //         //       },
  //         //     ],
  //         //   }
  //         // }

  //         node.children.map((doc) => {
  //           if (doc.id == res.data.data.id) {
  //             return (doc.title = res.data.data.title)
  //           }
  //         })

  //         return node
  //       })
  //       setFolders(updatedTreeData)
  //       toastNotification(
  //         'success',
  //         pageName + ' updated successfully',
  //         'topRight',
  //       )
  //     })
  //     .catch((err) => {
  //       console.log(err)
  //     })
  // }

  const onChangeSwitch = (checked) => {
    setStatus(checked);
  };

  function handleChange(content, delta, source, editor) {
    //setJsonOutput(editor.getContents());
    console.log(editor.getContents());
    onChange(content);

    if (
      content.length === 0 ||
      delta?.ops[0]?.insert === "\n" ||
      content === "<p><br></p>"
    ) {
      onChange("");
    }
  }

  //console.log(typeof titlee)
  // const items = [
  //   {
  //     key: '1',
  //     label: `Français`,
  //     children: (
  //       <div>
  //         <ReactQuill
  //           theme="snow"
  //           value={contentFr}
  //           // onChange={setValue}
  //           modules={modules}
  //           style={{ height: '380px' }}
  //           onChange={setContentFr}
  //         />
  //       </div>
  //     ),
  //   },
  //   {
  //     key: '2',
  //     label: `Anglais`,
  //     children: (
  //       <div>
  //         <ReactQuill
  //           theme="snow"
  //           value={contentEn}
  //           // onChange={setValue}
  //           modules={modules}
  //           style={{ height: '380px' }}
  //           onChange={setContentEn}
  //         />
  //       </div>
  //     ),
  //   },
  // ]

  const validateMessages = {
    required: "'${name}' is required!",
  };

  const handleFormItemChange = (changedValues, allValues) => {
    // let myButton = document.getElementById("buttonSaveTable");
    let commonAttributes = {};
    // let svgElement = myButton.querySelector("svg");

    // Obtenir la liste des clés de l'objet plus petit
    let record = {
      // content:
      // "wiki-docs"
      contentEn: selectedNode.content_en || "<p><br></p>",
      contentFr: selectedNode.content_fr || "<p><br></p>",
      pageNameEn: selectedNode.title_en,
      pageNameFr: selectedNode.title_fr,
      slugUrlValue: selectedNode.slug_url.replace(/^_\w{2}_/, ""),
      meta_description_fr: selectedNode.meta_description_fr,
      meta_description_en: selectedNode.meta_description_en,
      status: String(selectedNode.status),
      content: selectedNode.content,
    };
    let smallerKeys = Object.keys(record);

    let newValues = {
      contentEn:
        allValues.contentEn || selectedNode.content_en || "<p><br></p>",
      contentFr:
        allValues.contentFr || selectedNode.content_fr || "<p><br></p>",
      pageNameEn: allValues.pageNameEn,
      pageNameFr: allValues.pageNameFr,
      slugUrlValue: changedValues.pageNameFr
        ? allValues.pageNameFr
        : allValues.slugUrlValue.replace(/^_\w{2}_/, ""),
      meta_description_fr: allValues.meta_description_fr,
      meta_description_en: allValues.meta_description_en,
      status: String(allValues.status),
      content: selectedNode.content,
    };
    // Filtrer les attributs de l'objet plus grand en fonction de la liste des clés
    record &&
      smallerKeys.forEach(function (key) {
        if (key in record) {
          commonAttributes[key] = record[key];
          if (record[key] === undefined) commonAttributes[key] = null;
        }
      });

    let trimmedJsonObject = Object.keys(newValues).reduce((acc, key) => {
      if (typeof newValues[key] === "string") {
        acc[key] = newValues[key].trim();
      } else {
        acc[key] = newValues[key] || null;
      }
      return acc;
    }, {});

    if (!allValues?.pageNameFr) {
      setDisabled(true);
    } else if (
      JSON.stringify(commonAttributes) !== JSON.stringify(trimmedJsonObject)
    ) {
      setDisabled(false);
    } else {
      setDisabled(true);
    }
  };
  function handlePaste1(event) {
    const clipboardData = event.clipboardData || window.clipboardData;

    // Vérifier si le presse-papiers contient des données d'image
    if (clipboardData && clipboardData.items) {
      const clipboardItems = clipboardData.items;
      for (let i = 0; i < clipboardItems.length; i++) {
        const item = clipboardItems[i];
        console.log(item);
        if (item.type.indexOf("image") !== -1) {
          event.preventDefault();
          // Le contenu copié est une image

          return;
        }
      }
    }

    // Le contenu copié n'est pas une image
  }
  const items2 = [
    {
      label: (
        <div>
          <EyeOutlined /> &nbsp;{t("wiki.Published")}
        </div>
      ),
      value: "1",
      key: "1",
    },
    {
      label: (
        <div>
          <EyeInvisibleOutlined /> &nbsp;{t("wiki.Draft")}
        </div>
      ),
      value: "0",
      key: "0",
    },
  ];
  const handleMenuClick = (e) => {
    setDisabled(true);
    setLoading(true);
    MainService.updatePage(selectedNode?.id, {
      status: e.key,
      slug_url: form.getFieldsValue().slugUrlValue || selectedNode.slug_url,

      title_fr: form.getFieldsValue().pageNameFr || selectedNode.title_fr,
      title_en: form.getFieldsValue().pageNameEn || selectedNode.title_en,
      content_en: form.getFieldsValue().contentEn || selectedNode.content_en,
      content_fr: form.getFieldsValue().contentFr || selectedNode.content_fr,
    })
      .then((res) => {
        const updatedTreeData = folders.map((node) => {
          node.children.map((doc) => {
            if (doc.id == res.data.data.id) {
              return (
                (doc.title = res.data.data.title_fr),
                (doc.title_en = res.data.data.title_en),
                (doc.title_fr = res.data.data.title_fr),
                (doc.content_fr = res.data.data.content_fr),
                (doc.content_en = res.data.data.content_en),
                (doc.status = e.key),
                (doc.slug_url = res.data.data.slug_url)
              );
            }
          });

          return node;
        });

        setFolders(updatedTreeData);
        setSelectedNode({
          ...selectedNode,
          ...res.data.data,
          status: e.key,
        });

        setLoading(false);
        toastNotification(
          "success",
          selectedNode.title + t(`wiki.PageEditedSuccessfully`),
          "topRight"
        );
      })
      .catch((err) => {
        setLoading(false);
        console.log(err);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      });
  };
  const menuProps = {
    items: items2.filter((el) => el.value != selectedNode.status),
    onClick: handleMenuClick,
  };

  return (
    <div style={{ margin: 8 }}>
      {/* {selectedNode ? */}
      <Spin spinning={loading} size="medium">
        <Form
          name="formItemPath"
          layout="vertical"
          onFinish={onFinish}
          onFinishFailed={onFinishFailed}
          validateMessages={validateMessages}
          onValuesChange={handleFormItemChange}
          form={form}
          id="form"
        >
          <div
            className="sticky top-0 flex justify-between"
            style={{ backgroundColor: "white", zIndex: 99 }}
          >
            <div></div>
            <div className="flex">
              <div className="pr-2">
                <Form.Item
                  name="status"
                  className="relative ml-0"
                  valuePropName="checked"
                >
                  <ConfigProvider
                    theme={{
                      token: {
                        // Seed Token
                        colorPrimary:
                          selectedNode.status == 1 ? "#3CC3A5" : "#DF4F37",
                        // borderRadius: 2,

                        // Alias Token
                      },
                    }}
                  >
                    <Dropdown menu={menuProps} trigger={["click"]}>
                      <Button
                        type="primary"
                        // type="dashed"
                        // className={`${
                        //   selectedNode.status == 1
                        //     ? "bg-green-600 text-white hover:bg-green-700"
                        //     : "bg-orange-500 text-white hover:bg-orange-600"
                        // }`}
                        icon={
                          selectedNode.status == 1 ? (
                            <EyeOutlined />
                          ) : (
                            <EyeInvisibleOutlined />
                          )
                        }
                      >
                        <Space>
                          {selectedNode.status == 1
                            ? t("wiki.Published")
                            : t("wiki.Draft")}
                          <DownOutlined />
                        </Space>
                      </Button>
                    </Dropdown>
                  </ConfigProvider>
                  {/* <Segmented
                    options={[
                      {
                        label: t("wiki.Published"),
                        value: 1,
                      },
                      {
                        label: t("wiki.Draft"),
                        value: 0,
                      },
                    ]}
                    onChange={(e) => {
                      if (e === 1) {
                        setDisabled(true);

                        setLoading(true);
                        MainService.updatePage(selectedNode?.id, {
                          status: 1,
                          slug_url:
                            form.getFieldsValue().slugUrlValue ||
                            selectedNode.slug_url,

                          title_fr:
                            form.getFieldsValue().pageNameFr ||
                            selectedNode.title_fr,
                          title_en:
                            form.getFieldsValue().pageNameEn ||
                            selectedNode.title_en,
                          content_en:
                            form.getFieldsValue().contentEn ||
                            selectedNode.content_en,
                          content_fr:
                            form.getFieldsValue().contentFr ||
                            selectedNode.content_fr,
                        })
                          .then((res) => {
                            const updatedTreeData = folders.map((node) => {
                              node.children.map((doc) => {
                                if (doc.id == res.data.data.id) {
                                  return (
                                    (doc.title = res.data.data.title_fr),
                                    (doc.title_en = res.data.data.title_en),
                                    (doc.title_fr = res.data.data.title_fr),
                                    (doc.content_fr = res.data.data.content_fr),
                                    (doc.content_en = res.data.data.content_en),
                                    (doc.status = res.data.data.status),
                                    (doc.slug_url = res.data.data.slug_url)
                                  );
                                }
                              });

                              return node;
                            });

                            setFolders(updatedTreeData);
                            setSelectedNode({
                              ...selectedNode,
                              ...res.data.data,
                              status: 1,
                            });

                            setLoading(false);
                            toastNotification(
                              "success",
                              selectedNode.title +
                                t(`wiki.PageEditedSuccessfully`),
                              "topRight"
                            );
                          })
                          .catch((err) => {
                            setLoading(false);
                            console.log(err);
                            toastNotification(
                              "error",
                              t("toasts.somethingWrong"),
                              "topRight"
                            );
                          });
                      } else {
                        setDisabled(true);

                        setLoading(true);
                        MainService.updatePage(selectedNode?.id, {
                          status: 0,
                          slug_url:
                            form.getFieldsValue().slugUrlValue ||
                            selectedNode.slug_url,

                          title_fr:
                            form.getFieldsValue().pageNameFr ||
                            selectedNode.title_fr,
                          title_en:
                            form.getFieldsValue().pageNameEn ||
                            selectedNode.title_en,
                          content_en:
                            form.getFieldsValue().contentEn ||
                            selectedNode.content_en,
                          content_fr:
                            form.getFieldsValue().contentFr ||
                            selectedNode.content_fr,
                        })
                          .then((res) => {
                            const updatedTreeData = folders.map((node) => {
                              node.children.map((doc) => {
                                if (doc.id == res.data.data.id) {
                                  return (
                                    (doc.title = res.data.data.title_fr),
                                    (doc.title_en = res.data.data.title_en),
                                    (doc.title_fr = res.data.data.title_fr),
                                    (doc.content_fr = res.data.data.content_fr),
                                    (doc.content_en = res.data.data.content_en),
                                    (doc.status = res.data.data.status),
                                    (doc.slug_url = res.data.data.slug_url)
                                  );
                                }
                              });

                              return node;
                            });
                            setFolders(updatedTreeData);
                            setSelectedNode({
                              ...selectedNode,
                              ...res.data.data,
                              // ...form.getFieldsValue(),
                              status: 0,
                            });

                            setLoading(false);

                            toastNotification(
                              "success",
                              selectedNode.title +
                                t(`wiki.PageEditedSuccessfully`),
                              "topRight"
                            );
                          })
                          .catch((err) => {
                            setLoading(false);
                            console.log(err);
                            toastNotification(
                              "error",
                              t("toasts.somethingWrong"),
                              "topRight"
                            );
                          });
                      }
                    }}
                    // defaultValue={selectedNode.status}
                    value={selectedNode.status}
                  /> */}
                </Form.Item>
              </div>
              <div className="pr-2">
                <Button
                  disabled={disabled}
                  type="primary"
                  htmlType="submit"
                  onClick={() => {
                    //form.submit()
                    // form.resetFields()
                    // updatePage()
                  }}
                >
                  {t(`wiki.Save`)}
                </Button>
              </div>
              <div>
                <Button
                  danger
                  type="primary"
                  onClick={() => {
                    Confirm(
                      `${t(`wiki.Delete`)} "${selectedNode?.title_fr}" `,
                      t(`wiki.Confirm`),
                      <RestOutlined style={{ color: "red" }} />,
                      function func() {
                        return delete1(
                          selectedNode?.id,
                          selectedNode?.folder_id
                        );
                      },
                      true
                    );
                  }}
                >
                  {t(`wiki.Delete`)}
                </Button>
              </div>
            </div>
          </div>
          <Form.Item
            name="slugUrlValue"
            //initialValue={slugUrlValue}
            label={t(`wiki.SlugUrl`)}
            // style={{
            //   display: 'inline-block',
            //   width: 'calc(50% - 8px)',
            //   margin: '0 8px ',
            // }}
          >
            {/* <Input.Search
              addonBefore={`${generateSlug(nameGroup)}/${generateSlug(selectedNode.folder_title)}/`}
              enterButton={<CopyOutlined />}
              onSearch={handleCopyClick}
            /> */}
            <Input
              addonBefore={`${generateSlug(nameGroup)}/${generateSlug(
                selectedNode.folder_title
              )}/`}
              onChange={handleInputChange}
              suffix={
                <FiCopy
                  style={{ cursor: "pointer" }}
                  className="h-4 w-4"
                  onClick={handleCopyClick}
                />
              }
            />
          </Form.Item>
          <Form.Item name="content">
            {/* <Tabs defaultActiveKey="1" items={items} onChange={onChange} /> */}

            <Tabs>
              <TabPane
                tab={
                  <div className="mx-4 flex pb-0.5">
                    <span>{t(`wiki.French`)}</span>
                    <Badge />
                  </div>
                }
                className="TabsBody"
                key="wiki-docs"
              >
                <Form.Item
                  name="pageNameFr"
                  label={t(`wiki.Title`)}
                  rules={[
                    {
                      required: true,
                      message: t(`wiki.TitleRequired`),
                    },
                  ]}
                  //validateFirst="parallel"
                >
                  <Input
                    style={{ width: "100%" }}
                    onChange={handleInputChange}
                    // let arr = e.target.value.split(' ')
                    // let text = arr.join('_')

                    // setSlugUrlValue(
                    //   `${
                    //     form.getFieldValue().folderName
                    //   }_${text}`.toLowerCase(),
                    // )
                    //setPageNameFr(e.target.value)
                  />
                </Form.Item>

                <Form.Item
                  name="meta_description_fr"
                  label={t(`wiki.metaDescription`)}

                  //validateFirst="parallel"
                >
                  <Input.TextArea style={{ width: "100%" }} />
                </Form.Item>
                <div onDrop={handleDrop} onPaste={handlePaste1}>
                  <Form.Item
                    name="contentFr"
                    label={t(`wiki.Content`)}
                    // style={{
                    //   display: 'inline-block',
                    //   width: 'calc(50% - 8px)',
                    //   margin: '0 8px ',
                    // }}
                  >
                    <ReactQuill
                      theme="snow"
                      className="content-wiki"
                      ref={quillRef}
                      style={{
                        position: screenfull ? "fixed" : "relative",
                        height: screenfull ? "calc(100vh - 42px)" : "380px",
                        zIndex: screenfull ? 1000 : 0,
                        top: 0,
                        left: screenfull ? 60 : 0,
                        // bottom: 0,
                        right: 0,
                        marginBottom: 0,
                        background: screenfull ? "white" : "transparent",
                      }}
                      // value={contentFr}
                      modules={modules}
                      onChange={setContentFr}
                    />
                    {/* } */}

                    {/* <RichTextInput
                    setEditorContent={(e) => setContentFr(e)}
                    editorContent={contentFr}
                  /> */}
                  </Form.Item>
                </div>
              </TabPane>
              <TabPane
                tab={
                  <div className="mx-4 flex pb-0.5">
                    <span>{t(`wiki.English`)}</span>
                    <Badge />
                  </div>
                }
                className=""
                key="wiki-config"
              >
                <Form.Item
                  name="pageNameEn"
                  label={t(`wiki.Title`)}
                  // rules={[
                  //   {
                  //     required: true,
                  //     message: 'The page name is required!',
                  //   },
                  // ]}
                >
                  <Input
                    style={{ width: "100%" }}
                    onChange={(e) => setPageNameEn(e.target.value)}
                  />
                </Form.Item>

                <Form.Item
                  name="meta_description_en"
                  label={t(`wiki.metaDescription`)}

                  //validateFirst="parallel"
                >
                  <Input.TextArea style={{ width: "100%" }} />
                </Form.Item>
                <div onDrop={handleDrop} onPaste={handlePaste1}>
                  <Form.Item
                    name="contentEn"
                    label={t(`wiki.Content`)}
                    // style={{
                    //   display: 'inline-block',
                    //   width: 'calc(50% - 8px)',
                    //   margin: '0 8px ',
                    // }}
                  >
                    <ReactQuill
                      theme="snow"
                      className="content-wiki"
                      ref={quillRef}
                      style={{
                        position: screenfull ? "fixed" : "relative",
                        height: screenfull ? "calc(100vh - 42px)" : "380px",
                        zIndex: screenfull ? 1000 : 0,
                        top: 0,
                        left: screenfull ? 60 : 0,
                        // bottom: 0,
                        right: 0,
                        marginBottom: 0,
                        background: screenfull ? "white" : "transparent",
                      }}
                      // value={contentFr}
                      modules={modules}
                      onChange={setContentEn}
                    />

                    {/* } */}
                  </Form.Item>
                </div>
              </TabPane>
            </Tabs>
          </Form.Item>

          {/* <div style={{ marginTop: "40px", float: "right" }}>
            <Button
              type="primary"
              htmlType="submit"
              onClick={() => {
                //form.submit()
                // form.resetFields()
                // updatePage()
              }}
            >
              {t(`wiki.Save`)}
            </Button>
          </div> */}
        </Form>
      </Spin>

      {/*    : 
         <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
       } */}
    </div>
  );
};
export default Content;
