import { applyMiddleware, compose, legacy_createStore as createStore } from "redux";
import { persistStore, persistReducer } from "redux-persist";
import thunk from "redux-thunk";
import storage from "redux-persist/lib/storage";

import rootReducer from "./reducers";

const persistConfig = {
  key: "root",
  blacklist: [
    "ChatRealTime",
    "form",
    "table",
    // "menu",
    "ConfigCompanies",
    "dashboardRealTime",
    "visio",
    "visioList",
    "voipBlackList",
    "importReducer",
    "notes",
    "vue360",
    "files",
    // "rmc",
    "fields",
    //  "mailReducer",
    "selfNotesReducer",
    "drive",
  ],
  storage,
};

const persistedReducer = persistReducer(persistConfig, rootReducer);
const middlewares = [thunk];
const composeEnhancers =
  process.env.REACT_APP_BRANCH === "devLocal"
    ? window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__({
        actionsDenylist: "SET_ONLINE_USER",
      })
    : null || compose;

const store = createStore(
  persistedReducer,
  compose(composeEnhancers(applyMiddleware(...middlewares)))
);
const persistor = persistStore(store);
export { store, persistor };
