import { <PERSON><PERSON>, Popover } from "antd";
import React, { useCallback, useRef, useState } from "react";
import UsersList from "../UsersList";
import { EditOutlined, PlusOutlined, ReloadOutlined } from "@ant-design/icons";
import MainService from "services/main.service";
import { toastNotification } from "components/ToastNotification";
import { useTranslation } from "react-i18next";
import ColleaguesList from "../ColleaguesList";
import { handleCheck } from "../helpers/handleCheck";
import { useDispatch } from "react-redux";
import { setListMeet } from "new-redux/actions/visio.actions/visio";
import { useSelector } from "react-redux";

const PopOverMembersTask = ({
  from,
  keyColumn,
  type = "",
  // key={props?.id}
  // key={"guestsList"}
  usersList,
  defaultOwner = "",
  searchQuery,
  setSearchQuery,
  lastPage,
  currentPage,
  setCurrentPage,
  checkedItems,
  loading,
  setCheckedItems,
  multipleAddMember = true,
  setSelectedFamilyMembers,
  setFilterParticipantsValue,
  filterParticipantsValue,
  totalEntities,
  item,
  source = "",
  setTasksData,
  tasksData,
}) => {
  const [selectedId, setSelectedId] = useState("");
  const [t] = useTranslation("common");
  const [openPopover, setOpenPopover] = useState(false);
  const [displayFollowersList, setDisplayFollowersList] = useState(null);
  const [popoverKey, setPopoverKey] = useState(0);

  const listHeight = useRef(null);
  const dispatch = useDispatch();
  // const { tasksData } = useSelector((state) => state.visioList);
  const handleAddMembers = useCallback(
    (role, id) => {
      let payload = {
        role: role,
        member_id: id,
      };

      MainService.addMembers(selectedId, payload)
        .then((response) => {
          if (response?.data?.message === "Owner changed successfully") {
            type === "visio"
              ? dispatch(
                  setTasksData(
                    tasksData.map((el) =>
                      el.id === item?.id ? response.data.data : el
                    )
                  )
                )
              : setTasksData((prev) =>
                  prev.map((el) =>
                    el.id === item?.id ? response.data.data : el
                  )
                );

            toastNotification(
              "success",
              t("tasks.notAuthorizedToSeeActivity"),
              "topRight"
            );
          } else {
            type === "visio"
              ? dispatch(
                  setTasksData(
                    tasksData.map((el) =>
                      el.id === item?.id ? { ...el, ...response.data.data } : el
                    )
                  )
                )
              : setTasksData((prev) =>
                  prev.map((el) =>
                    el.id === item?.id ? response.data.data : el
                  )
                );
          }
        })
        .catch((error) => {
          console.log(`Error ${error}`);
        });
    },
    [dispatch, tasksData, selectedId, setTasksData, type, item, t]
  );

  const handleRemoveMembers = useCallback(
    (role, id) => {
      let payload = {
        role: role,
        member_id: id,
      };

      MainService.removeMembers(selectedId, payload)
        .then((response) => {
          type === "visio"
            ? dispatch(
                setListMeet(
                  tasksData.map((el) =>
                    el.id === item?.id ? { ...el, ...response.data.data } : el
                  )
                )
              )
            : setTasksData((prev) =>
                prev.map((el) => (el.id === item?.id ? response.data.data : el))
              );
        })
        .catch((error) => {
          console.log(`Error ${error}`);
        });
    },
    [dispatch, tasksData, selectedId, setTasksData, type, item]
  );
  const resetStates = () => {
    setSearchQuery("");
    setCurrentPage(1);
    setCheckedItems([]);
    setSelectedId("");
    setDisplayFollowersList(null);
    setFilterParticipantsValue(0);
    setSelectedFamilyMembers([1, 2, 4, 9]);
  };
  const handleOpenPopover = (open) => {
    setOpenPopover(open);
    if (keyColumn === "followers") {
      setDisplayFollowersList(open ? "followersList" : null);
      if (!open) {
        setSearchQuery("");
        setCurrentPage(1);
      }
    }
    if (!open) {
      setSearchQuery(""); // Réinitialiser la recherche
      resetStates();
    } else if (open) {
      setPopoverKey((prev) => prev + 1);
    }
  };
  return (
    <Popover
      destroyTooltipOnHide={true}
      autoAdjustOverflow={true}
      open={openPopover}
      onOpenChange={handleOpenPopover}
      content={
        <>
          {keyColumn === "guests" ? (
            <UsersList
              from="columnTable"
              key="guests"
              usersList={usersList}
              handleCheckedItems={handleCheck}
              searchQuery={searchQuery}
              setSearchQuery={setSearchQuery}
              lastPage={lastPage}
              currentPage={currentPage}
              setCurrentPage={setCurrentPage}
              checkedItems={checkedItems}
              loading={loading}
              setCheckedItems={setCheckedItems}
              dispatchAddMembers={handleAddMembers}
              dispatchRemoveMembers={handleRemoveMembers}
              setSelectedFamilyMembers={setSelectedFamilyMembers}
              setFilterParticipantsValue={setFilterParticipantsValue}
              filterParticipantsValue={filterParticipantsValue}
              totalEntities={totalEntities}
            />
          ) : (
            <ColleaguesList
              key="colleagues"
              source={defaultOwner ? "owner" : "followersList"}
              from="columnTable"
              defaultOwner={defaultOwner}
              usersList={usersList}
              searchQuery={searchQuery}
              checkedItems={checkedItems}
              setCheckedItems={setCheckedItems}
              displayFollowersList={displayFollowersList}
              handleCheckedItems={handleCheck}
              setFollowersSearchQuery={setSearchQuery}
              loading={loading}
              ref={listHeight}
              dispatchAddMembers={handleAddMembers}
              dispatchRemoveMembers={handleRemoveMembers}
              totalEntities={totalEntities}
              setCurrentPage={setCurrentPage}
              currentPage={currentPage}
            />
          )}
        </>
      }
      title=""
      trigger={["click"]}
    >
      <Button
        // type="primary"
        type={multipleAddMember ? "default" : "text"}
        shape="circle"
        onClick={() => {
          setCheckedItems(item[keyColumn]);
          setSelectedId(item?.id);
        }}
        icon={multipleAddMember ? <PlusOutlined /> : <EditOutlined />}
      />
    </Popover>
  );
};

export default PopOverMembersTask;
