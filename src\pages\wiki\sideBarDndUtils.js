export const isChild = (str) => {
  return str.indexOf("-") > -1;
};

export const isFolder = (str) => {
  return str.indexOf("-") == -1;
};

export const sortFolders = (folders) => {
  folders?.sort((a, b) => (a.rank > b.rank ? 1 : -1));
  folders?.forEach((item) => {
    item?.children?.sort((a, b) => (a.rank > b.rank ? 1 : -1));
  });
  return folders;
};

export const returnFoldersAndTheirChildrenRanksAsJson = (folders) => {
  let foldersAndTheirChildrenRanks = [];
  folders.forEach((item) => {
    let folder = {
      id: item.id,
      rank: item.rank,
      key: item.key,
      children: [],
    };
    item.children.forEach((child) => {
      folder.children.push({
        id: child.id,
        rank: child.rank,
        key: child.key,
      });
    });
    foldersAndTheirChildrenRanks.push(folder);
  });
  return foldersAndTheirChildrenRanks;
};

export const DragFoldersOrdering = (
  dragKeyn,
  dropKey,
  dropPosition,
  folders
) => {
  // dropPosition = 1 ? the drop is on the bottom gap of the drop node : the drop is on the top gap of the drop node

  // Create a copy of the folders array
  const newTreeData = folders.slice();

  // Find the indices of the nodes with the specified keys
  let dragIndex = -1;
  let dropIndex = -1;

  for (let i = 0; i < newTreeData.length; i++) {
    if (newTreeData[i].key === dragKeyn) {
      dragIndex = i;
    } else if (newTreeData[i].key === dropKey) {
      dropIndex = i;
    }

    // If both nodes are found, break the loop
    if (dragIndex !== -1 && dropIndex !== -1) {
      break;
    }
  }

  if (dragIndex !== -1 && dropIndex !== -1) {
    // console.log("drag key", dragKeyn);
    // console.log("dorp key", dropKey);

    if (dropPosition == -1) {
      //the item is dropped before the drop key
      newTreeData[dragIndex].rank = newTreeData[dropIndex].rank;
      for (let i = dropIndex; i < dragIndex; i++) {
        newTreeData[i].rank += 1;
      }
    } else {
      //dropPosition == 1
      //the item is dropped after the drop key

      let dragRank = newTreeData[dragIndex].rank;
      let dropRank = newTreeData[dropIndex].rank;

      if (dropRank > dragRank) {
        newTreeData[dragIndex].rank = newTreeData[dropIndex].rank;
        for (let i = dropIndex; i >= dragIndex; i--) {
          if (newTreeData[i].key != dragKeyn) {
            newTreeData[i].rank -= 1;
          }
        }
      } else if (dragRank > dropRank) {
        newTreeData[dragIndex].rank = newTreeData[dropIndex].rank + 1;
        for (let i = dropIndex + 1; i < dragIndex; i++) {
          newTreeData[i].rank += 1;
        }
      }
    }
  }

  let jsonToReturn = {
    groupeWikiId: newTreeData[0].product_id,
    rank: {},
  };

  newTreeData.forEach((item) => {
    jsonToReturn.rank[item.id] = item.rank;
  });

  // console.log("JSON TO RETURN", jsonToReturn);

  // console.log("NEW TREE DATA", newTreeData);

  sortFolders(newTreeData);
  return {
    newTreeData,
    jsonToReturn,
  };

  //     // Check if both nodes were found
  //     console.log("newTreeData", newTreeData);
  //     if (dragIndex !== -1 && dropIndex !== -1) {
  //       // Swap the ranks
  //       if(dropPosition == 1){
  //         //insert the drag node after the drop node
  //         const dragNodeRank = newTreeData[dragIndex].rank;
  //         newTreeData[dragIndex].rank = newTreeData[dropIndex].rank + 1;
  //         for (let i = dropIndex + 1; i < newTreeData.length; i++) {
  //             if(newTreeData[i].key == dragKeyn) continue;
  //             else {
  //                 newTreeData[i].rank = newTreeData[i].rank + 1;
  //             }
  //         }
  //         // set the rank of the drop node to the rank of the drag node
  //         newTreeData[dropIndex].rank = dragNodeRank;

  //       } else {
  //         //insert the drag node before the drop node
  //         const dragNodeRank = newTreeData[dragIndex].rank;
  //         newTreeData[dragIndex].rank = newTreeData[dropIndex].rank;
  //         for (let i = dropIndex; i < newTreeData.length; i++) {
  //             if(newTreeData[i].key == dragKeyn) continue;
  //             else {
  //                 newTreeData[i].rank = newTreeData[i].rank + 1;
  //             }
  //         }

  //         // set the rank of the drop node to the rank of the drag node
  //         newTreeData[dropIndex].rank = dragNodeRank;

  //       }

  //       //this form
  //       /**
  //        *
  //        * {
  //          "groupeWikiId": 120,
  //           "rank": {
  //           "204" : 0 ,
  //             "205" : 1 ,
  //             "226" : 2 ,
  //             "503" : 25 ,
  //             "504" : 26 ,
  //             "505" : 27 ,
  //             "506" : 28
  //                     }
  // }
  //        */

  //       return newTreeData;

  //     }
};

export const handleDragAndDropFromChildToFolder = (
  dragKey,
  dropKey,
  info,
  dropPosition,
  folders
) => {
  // console.log("handleDragAndDropFromChildToFolder")
  // console.log("DROP POSITION", dropPosition);
  //   console.log("DRAG KEY", dragKey);
  // console.log("DROP KEY", dropKey);
  const newTreeData = folders.slice();
  const dragKeyParent = dragKey.split("-")[0];

  const dragKeyParentIndex = newTreeData.findIndex(
    (node) => node.key == dragKeyParent
  );
  const dropKeyParentIndex = newTreeData.findIndex(
    (node) => node.key == dropKey
  );

  let dataToSend = {};

  let dragElementId = newTreeData[dragKeyParentIndex].children.find(
    (node) => node.key == dragKey
  ).id;

  if (dragKeyParentIndex == -1 || dropKeyParentIndex == -1) return;

  if (dropKey == dragKeyParent) {
    const dragKeyIndex = newTreeData[dragKeyParentIndex].children.findIndex(
      (node) => node.key == dragKey
    );
    const dropKeyIndex = 0;

    let dropElem = newTreeData[dropKeyParentIndex].children[dropKeyIndex];

    let dragElem = newTreeData[dragKeyParentIndex].children.find(
      (node) => node.key == dragKey
    );

    if (dropPosition == 0) {
      //Inserted at top
      dragElem.rank = 0;
      for (let i = 0; i < dragKeyIndex; i++) {
        newTreeData[dropKeyParentIndex].children[i].rank += 1;
      }

      //   sortFolders(newTreeData);

      //   return newTreeData;
    } else if (dropPosition == 1) {
      //search for the max rank on the array
      let arrLength = newTreeData[dropKeyParentIndex].children.length - 1;
      dragElem.rank = arrLength;
      for (let i = dragKeyIndex + 1; i < arrLength; i++) {
        newTreeData[dropKeyParentIndex].children[i].rank -= 1;
      }
      //Inserted at bottom

      //     sortFolders(newTreeData);

      //   return newTreeData;
    }
    dataToSend = {
      folders: [
        {
          folder_id: newTreeData[dragKeyParentIndex].id,
          ranks: [
            {
              page_id: dragElementId,
              rank: newTreeData[dragKeyParentIndex].children.find(
                (node) => node.id == dragElementId
              ).rank,
            },
          ],
        },
      ],
    };
  } else {
    // console.log("We are here now");

    const dragKeyIndex = newTreeData[dragKeyParentIndex].children.findIndex(
      (node) => node.key == dragKey
    );
    const dropKeyIndex = 0;

    let dropElem = newTreeData[dropKeyParentIndex].children[dropKeyIndex];

    let dragElem = newTreeData[dragKeyParentIndex].children.find(
      (node) => node.key == dragKey
    );

    if (dropPosition == 0) {
      //Inserted at top
      //remove the dragElem from the dragKeyParent
      newTreeData[dragKeyParentIndex].children = newTreeData[
        dragKeyParentIndex
      ].children.filter((node) => node.key !== dragKey);

      //reset the new ranks of the dragKeyParent
      for (
        let i = 0;
        i < newTreeData[dragKeyParentIndex].children.length;
        i++
      ) {
        newTreeData[dragKeyParentIndex].children[i].rank = i;
      }

      // console.log("NEW TREE DATA", newTreeData);

      //add one to the ranks of the dropKeyParent children
      for (
        let i = 0;
        i < newTreeData[dropKeyParentIndex].children.length;
        i++
      ) {
        newTreeData[dropKeyParentIndex].children[i].rank += 1;
      }

      //add the dragElem to the dropKeyParent
      newTreeData[dropKeyParentIndex].children.unshift(dragElem);
      //set the rank of the dragElem to 0
      dragElem.rank = 0;
      dragElem.key = `${dropKey}-${dragElem.id}`;
    } else if (dropPosition == 1) {
      //Inserted at bottom

      //remove the dragElem from the dragKeyParent
      newTreeData[dragKeyParentIndex].children = newTreeData[
        dragKeyParentIndex
      ].children.filter((node) => node.key !== dragKey);

      //reset the new ranks of the dragKeyParent
      for (
        let i = 0;
        i < newTreeData[dragKeyParentIndex].children.length;
        i++
      ) {
        newTreeData[dragKeyParentIndex].children[i].rank = i;
      }

      //add the dragElem to the dropKeyParent
      newTreeData[dropKeyParentIndex].children.push(dragElem);
      //set the rank of the dragElem to the length of the dropKeyParent children
      //get the max rank
      dragElem.rank = newTreeData[dropKeyParentIndex].children.length - 1;
      dragElem.key = `${dropKey}-${dragElem.id}`;

      //   sortFolders(newTreeData);

      //   return newTreeData;
    }
    dataToSend = {
      folders: [
        {
          folder_id: newTreeData[dropKeyParentIndex].id,
          ranks: [
            {
              page_id: dragElementId,
              rank: newTreeData[dropKeyParentIndex].children.find(
                (node) => node.id == dragElementId
              ).rank,
            },
          ],
        },
      ],
    };
  }

  //if the operation is at the same folder

  //   let dataToSend =  {
  //     folders: [
  //         {
  //             folder_id: newTreeData[dropKeyParentIndex].id,
  //             ranks: [
  //                 {
  //                     page_id: newTreeData[dragKeyParentIndex].id,
  //                     rank: newTreeData[dragKeyParentIndex].rank
  //                 }
  //             ]
  //         }
  //     ]
  //   }

  //   console.log("DATA TO SEND", dataToSend);

  sortFolders(newTreeData);

  return { newTreeData, dataToSend };

  //if the operation is not at the same folder
  //to implement
};

export const changeRankOnDragAndDropChild = (
  dragKey,
  dropKey,
  info,
  dropPosition,
  dropGaps,
  folders
) => {
  // Create a copy of the folders array
  // console.log("changeRankOnDragAndDropChild")

  const newTreeData = folders.slice();

  //we search for the parents of the dragKey and the dropKey
  const dragKeyParent = dragKey.split("-")[0];
  const dropKeyParent = dropKey.split("-")[0];

  //check if realy exists
  const dragKeyParentIndex = newTreeData.findIndex(
    (node) => node.key == dragKeyParent
  );
  const dropKeyParentIndex = newTreeData.findIndex(
    (node) => node.key == dropKeyParent
  );

  //draggedElement id
  const dragElementId = newTreeData[dragKeyParentIndex].children.find(
    (node) => node.key == dragKey
  ).id;

  let dataToSend = {};

  if (dragKeyParentIndex == -1 || dropKeyParentIndex == -1) return;

  if (dragKeyParent == dropKeyParent) {
    //if the dragKey and the dropKey have the same parent
    //we just change the rank of the dragKey and the dropKey
    const dragKeyIndex = newTreeData[dragKeyParentIndex].children.findIndex(
      (node) => node.key == dragKey
    );
    const dropKeyIndex = newTreeData[dropKeyParentIndex].children.findIndex(
      (node) => node.key == dropKey
    );

    let dropElem = newTreeData[dropKeyParentIndex].children.find(
      (node) => node.key == dropKey
    );
    let dragElem = newTreeData[dragKeyParentIndex].children.find(
      (node) => node.key == dragKey
    );

    if (dragKeyIndex > dropKeyIndex) {
      dragElem.rank = dropElem.rank + 1;
      for (let i = dropKeyIndex + 1; i < dragKeyIndex; i++) {
        newTreeData[dropKeyParentIndex].children[i].rank += 1;
      }
    } else if (dragKeyIndex < dropKeyIndex) {
      dragElem.rank = dropElem.rank;
      for (let i = dragKeyIndex + 1; i <= dropKeyIndex; i++) {
        newTreeData[dropKeyParentIndex].children[i].rank -= 1;
      }
    }

    dataToSend = {
      folders: [
        {
          folder_id: newTreeData[dragKeyParentIndex].id,
          ranks: [
            {
              page_id: dragElementId,
              rank: newTreeData[dragKeyParentIndex].children.find(
                (node) => node.id == dragElementId
              ).rank,
            },
          ],
        },
      ],
    };

    // Swap the ranks
  } else {
    const dragKeyIndex = newTreeData[dragKeyParentIndex].children.findIndex(
      (node) => node.key == dragKey
    );
    const dropKeyIndex = newTreeData[dropKeyParentIndex].children.findIndex(
      (node) => node.key == dropKey
    );

    let dropElem = newTreeData[dropKeyParentIndex].children.find(
      (node) => node.key == dropKey
    );
    let dragElem = newTreeData[dragKeyParentIndex].children.find(
      (node) => node.key == dragKey
    );

    if (dropGaps.dragOverGapBottom) {
      //remove the dragElem from the dragKeyParent
      newTreeData[dragKeyParentIndex].children = newTreeData[
        dragKeyParentIndex
      ].children.filter((node) => node.key !== dragKey);

      //reset the new ranks of the dragKeyParent
      for (
        let i = 0;
        i < newTreeData[dragKeyParentIndex].children.length;
        i++
      ) {
        newTreeData[dragKeyParentIndex].children[i].rank = i;
      }

      //add the dragElem to the dropKeyParent
      for (
        let i = dropKeyIndex + 1;
        i < newTreeData[dropKeyParentIndex].children.length;
        i++
      ) {
        newTreeData[dropKeyParentIndex].children[i].rank += 1;
      }

      newTreeData[dropKeyParentIndex].children.push(dragElem);

      dragElem.rank = dropElem.rank + 1;
      dragElem.key = `${dropKeyParent}-${dragElem.id}`;

      dataToSend = {
        folders: [
          {
            folder_id: newTreeData[dropKeyParentIndex].id,
            ranks: [
              {
                page_id: dragElementId,
                rank: newTreeData[dropKeyParentIndex].children.find(
                  (node) => node.id == dragElementId
                ).rank,
              },
            ],
          },
        ],
      };
    }
  }

  // console.log("DATA TO SEND", dataToSend);

  sortFolders(newTreeData);

  return { newTreeData, dataToSend };
};
