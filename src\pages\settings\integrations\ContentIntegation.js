import { URL_ENV } from "index";
import React, { useEffect, useState } from "react";
import { generateAxios } from "services/axiosInstance";
import ListIntegrations from "./ListIntegrations";
import {
  <PERSON><PERSON>,
  Badge,
  Button,
  Form,
  Input,
  message,
  Modal,
  Spin,
  Switch,
  Typography,
} from "antd";
import { useTranslation } from "react-i18next";
import {
  ExclamationCircleFilled,
  SaveOutlined,
  SyncOutlined,
} from "@ant-design/icons";
import { toastNotification } from "components/ToastNotification";
import AIPolicy from "pages/profile/components/Policies/AiPolicy";
const { confirm } = Modal;

const ContentIntegration = ({ selectedIntegration }) => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const [form] = Form.useForm();
  const [t] = useTranslation("common");
  const { i18n } = useTranslation("common");
  const [isContentEmpty, setIsContentEmpty] = useState(true);
  const [disabledReset, setIsDisabledReset] = useState(false);
  const [openModal, setOpenModal] = useState(false);

  const [reset, setReset] = useState(false);
  useEffect(() => {
    const getListIntegrations = async () => {
      setLoading(true);
      try {
        setIsContentEmpty(true);
        form.resetFields();
        const res = await generateAxios(
          `${URL_ENV?.REACT_APP_OAUTH_CHAT_API}/${process.env.REACT_APP_SUFFIX_API}`
        ).get(`/integrations/show/${selectedIntegration?._id}`);
        form.setFieldsValue(res?.data?.data.config);

        setData(res?.data?.data);
        if (Object.values(res?.data?.data.config).some((el) => el)) {
          setIsDisabledReset(false);
        } else {
          setIsDisabledReset(true);
        }
        setLoading(false);
      } catch (err) {
        setLoading(false);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    };

    if (selectedIntegration?._id) {
      getListIntegrations();
    }
  }, [selectedIntegration?._id, form, t]);

  const onFinish = async (values) => {
    try {
      setLoading(true);
      const formData = new FormData();
      formData.append("app_name", selectedIntegration?.app_name);
      Object.entries(values).forEach(([key, value]) => {
        formData.append(
          "configs[" + key + "]",
          value && value !== undefined && value !== null ? value : ""
        );
      });
      const res = await generateAxios(
        `${URL_ENV?.REACT_APP_OAUTH_CHAT_API}/${process.env.REACT_APP_SUFFIX_API}`
      ).post(`/integrations/store`, formData);

      // form.setFieldsValue(res?.data?.data.config);
      if (Object.values(res?.data?.data.config).some((el) => el)) {
        setIsDisabledReset(false);
        setIsContentEmpty(true);
      } else {
        setIsDisabledReset(true);
        setIsContentEmpty(true);
      }
      setLoading(false);
      setReset(false);
      toastNotification(
        "success",
        selectedIntegration.app_name + t("toasts.edit"),
        "topRight"
      );
    } catch (err) {
      setLoading(false);
      toastNotification("error", t("toasts.somethingWrong"), "topRight");
    }
  };
  const onValuesChange = (changedValues, allvalues) => {
    const allValuesAreFilled = Object.values(allvalues).every(
      (value) => value !== "" && value !== undefined && value !== null
    );
    setIsContentEmpty(!allValuesAreFilled);
  };
  const warning = () => {
    confirm({
      title: t("integrations.resetConfirmation"),
      icon: <ExclamationCircleFilled />,
      content: t("mailing.confirmAction"),
      async onOk() {
        return new Promise((resolve, reject) => {
          form.resetFields("");
          setReset(true);
          setTimeout(() => {
            form.submit();
            !loading ? resolve() : reject();
          }, 200);
        }).catch(() => console.log("Oops errors!"));
      },
      onCancel() {},
    });
  };

  return (
    <div className="p-4 ">
      <div className="flex items-center justify-between">
        <Typography.Title level={3}>
          {selectedIntegration?.app_name}
        </Typography.Title>

        {selectedIntegration?.app_name === "OpenAi" && (
          <Typography.Link onClick={() => setOpenModal(true)}>
            Politique d'utilisation de l'intelligence artificielle
          </Typography.Link>
        )}
      </div>

      <br />
      <Spin mask={true} spinning={loading}>
        <Form
          name="dynamic_form"
          layout="vertical"
          onFinish={onFinish}
          form={form}
          onValuesChange={onValuesChange}
          // Initialise les valeurs du formulaire avec les données de configuration si disponibles
          // initialValues={data?.config || {}}
        >
          <Form.Item>
            <div className="">
              <div className="flex items-center justify-end gap-4">
                <Badge
                  status={
                    selectedIntegration?.is_active === 1 ? "success" : "error"
                  }
                  text={
                    selectedIntegration?.is_active === 1 ? (
                      <span className=" text-[#52c41a]">
                        {t(`helpDesk.actif`)}
                      </span>
                    ) : (
                      <span className=" text-red-600">
                        {t(`helpDesk.noActif`)}
                      </span>
                    )
                  }
                />
                <Button
                  icon={<SyncOutlined />}
                  disabled={disabledReset}
                  loading={loading && reset}
                  onClick={warning}
                >
                  {t("localisation.reset")}
                </Button>
                <Button
                  icon={<SaveOutlined />}
                  type="primary"
                  htmlType="submit"
                  loading={!reset && loading}
                  disabled={isContentEmpty}
                >
                  {t("form.save")}
                </Button>
              </div>
            </div>
          </Form.Item>
          {data?.schema?.map((item) => (
            <Form.Item
              key={item?.value}
              label={i18n.language === "en" ? item.en : item.fr}
              name={item.value}
              rules={[
                {
                  required: !reset,
                  message: `${i18n.language === "en" ? item.en : item.fr} ${t(
                    "table.header.isrequired"
                  )}`,
                },
              ]}
              // initialValue={data?.config?.[item?.value]}
            >
              <Input />
            </Form.Item>
          ))}
        </Form>
      </Spin>
      {selectedIntegration?.app_name === "OpenAi" && (
        <Modal
          // title="Basic Modal"
          closable={{ "aria-label": "Custom Close Button" }}
          open={openModal}
          onCancel={() => setOpenModal(false)}
          width={1080}
          footer={[]}
        >
          <AIPolicy />
        </Modal>
      )}
    </div>
  );
};

export default ContentIntegration;
