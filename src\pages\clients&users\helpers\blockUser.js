import { Modal } from "antd";
import { WarningOutlined } from "@ant-design/icons";
import { blockOrUnblockUsers } from "../services/services";
import { toastNotification } from "../../../components/ToastNotification";

const blockOrUnblockUser = async (
  t,
  elementID,
  label,
  selectedRowKeys,
  setSelectedRowKeys,
  option
) => {
  const confirm = Modal.confirm({
    icon: <WarningOutlined />,
    title: (
      <p className="truncate">{`${
        option === "unblock" ? t("contacts.unblocked") : t("contacts.blocked")
      } ${
        label ? label : `${selectedRowKeys?.length} ${t("contacts.users")}`
      }`}</p>
    ),
    content: t("contacts.deleteConfirmMsg"),
    okText: t("profile.confirm"),
    okType: option === "unblock" ? "default" : "danger",
    cancelText: t("profile.cancel"),
    onCancel() {},
    onOk: async () => {
      try {
        const formData = new FormData();
        formData.append("status", option === "unblock" ? 1 : 0);
        const elementsToDelete = elementID ? [elementID] : selectedRowKeys;
        elementsToDelete?.forEach((e) => formData.append("ids[]", e));
        const { status } = await blockOrUnblockUsers(formData);

        if (status === 200) {
          // setShouldFetchData(true);
          !elementID && setSelectedRowKeys([]);
          // toastNotification(
          //   "success",
          //   elementID
          //     ? t(
          //         option === "unblock"
          //           ? "contacts.unblockUser"
          //           : "contacts.blockUser",
          //         { x: label }
          //       )
          //     : t(
          //         option === "unblock"
          //           ? "contacts.unblockUsers"
          //           : "contacts.blockUsers",
          //         { x: selectedRowKeys?.length }
          //       ),
          //   "topRight",
          //   5
          // );
          toastNotification(
            "success",
            t("contacts.notifProccessBeingProcess"),
            "topRight",
            3,
            null,
            1
          );
        }
      } catch (err) {
        const status = err?.response?.status;
        // const errMsg = Object.keys(err?.response?.data?.message);
        switch (status) {
          case 401:
            return;
          //   case 409:
          //     toastNotification("error", <div></div>, "topRight", 7);
          //     return;
          case 403:
            toastNotification(
              "error",
              t("contacts.errLimitLicense"),
              "topRight"
            );
            return;
          default:
            toastNotification("error", t("toasts.somethingWrong"), "topRight");
            return;
        }
      }
    },
  });
  return confirm;
};

export default blockOrUnblockUser;
