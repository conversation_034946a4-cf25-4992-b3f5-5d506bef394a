import {
  SET_ACTIVE_ACTIVITY360,
  SET_ACTIVE_TAB360,
  SAVE_PREFERENCES,
  SET_NEW_INTERACTION,
  SET_OPEN_VIEW360_IN_DRAWER,
  SET_OPEN_CHAT_VIEW_SPHERE,
  SET_NBR_CHAT_VIEW_SPHERE,
  SET_CHAT_VIEW_SPHERE_FROM_DRAWER,
  SET_PREV_IDS_FROM_DRAWER,
  REMOVE_PREV_IDS_FROM_DRAWER,
  RESET_PREV_IDS_FROM_DRAWER,
  SET_ACTIVE_MENU_360,
} from "../../constants";

export const setActiveTab360 = (payload) => (dispatch) => {
  dispatch({ type: SET_ACTIVE_TAB360, payload });
};
export const setActiveMenu360 = (payload) => (dispatch) => {
  dispatch({ type: SET_ACTIVE_MENU_360, payload });
};
export const setActiveActivity360 = (payload) => (dispatch) => {
  dispatch({ type: SET_ACTIVE_ACTIVITY360, payload });
};

export const setSavePreferences = (payload) => (dispatch) => {
  dispatch({ type: SAVE_PREFERENCES, payload });
};
export const setNewInteraction = (payload) => (dispatch) => {
  dispatch({ type: SET_NEW_INTERACTION, payload });
};
export const setOpenView360InDrawer = (payload) => (dispatch) => {
  dispatch({ type: SET_OPEN_VIEW360_IN_DRAWER, payload });
};

export const setOpenChatInViewSPhere = (payload) => (dispatch) => {
  dispatch({ type: SET_OPEN_CHAT_VIEW_SPHERE, payload });
};
export const setNbrChatInViewSPhere = (payload) => (dispatch) => {
  dispatch({ type: SET_NBR_CHAT_VIEW_SPHERE, payload });
};
export const setChatInViewSPhereFromDrawer = (payload) => (dispatch) => {
  dispatch({ type: SET_CHAT_VIEW_SPHERE_FROM_DRAWER, payload });
};
export const addLastIdToViewSphere = (payload) => (dispatch) => {
  dispatch({ type: SET_PREV_IDS_FROM_DRAWER, payload });
};
export const removeLastIdFromViewSphere = (payload) => (dispatch) => {
  dispatch({ type: REMOVE_PREV_IDS_FROM_DRAWER, payload });
};
export const resetPrevIdsFromViewSphere = (payload) => (dispatch) => {
  dispatch({ type: RESET_PREV_IDS_FROM_DRAWER, payload });
};
