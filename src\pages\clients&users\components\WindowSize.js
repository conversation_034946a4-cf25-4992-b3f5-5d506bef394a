import { useState, useEffect } from "react";
import debounce from "lodash/debounce"; // lodash library

export function useWindowSize() {
  const [size, setSize] = useState({
    height: window.innerHeight,
    width: window.innerWidth,
  });

  useEffect(() => {
    const handleResize = debounce(
      () =>
        setSize({
          height: window.innerHeight,
          width: window.innerWidth,
        }),
      150
    );
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  return size;
}
