import React from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import * as Exporting from "highcharts/modules/exporting";
import * as ExportData from "highcharts/modules/export-data";
import * as OfflineExporting from "highcharts/modules/offline-exporting";

// Initialize the modules
if (typeof Exporting === "function") {
  Exporting(Highcharts);
}
if (typeof ExportData === "function") {
  ExportData(Highcharts);
}
if (typeof OfflineExporting === "function") {
  OfflineExporting(Highcharts);
}

const Piechartr = ({ pivotData }) => {
  if (
    !pivotData ||
    !pivotData.rowKeys ||
    !pivotData.colKeys ||
    !pivotData.getAggregator
  ) {
    return <p className="text-center text-red-600">No Data</p>;
  }

  const rowKeys = pivotData.rowKeys;
  const colKeys = pivotData.colKeys;

  if (rowKeys.length === 0 && colKeys.length === 0) {
    return <p className="text-center text-red-600">No Data</p>;
  }

  const pieData = (rowKeys.length > 0 ? rowKeys : colKeys)
    .map((entry) => {
      const label = entry.join(" - ") || "Total";
      const otherAxis = rowKeys.length > 0 ? colKeys : rowKeys;

      const sum =
        otherAxis.length > 0
          ? otherAxis.reduce((acc, val) => {
              const r = rowKeys.length > 0 ? entry : val;
              const c = rowKeys.length > 0 ? val : entry;
              return acc + (pivotData.getAggregator(r, c)?.value() || 0);
            }, 0)
          : pivotData.getAggregator(entry, [])?.value() ||
            pivotData.getAggregator([], entry)?.value() ||
            0;

      return { name: label, y: sum };
    })
    .filter((item) => item.y > 0);

  if (pieData.length === 0) {
    return <p className="text-center text-red-600">No Data</p>;
  }

  const options = {
    chart: {
      type: "pie",
    },
    title: {
      text: `📊
            ${pivotData.props.rows.length ? pivotData.props.rows.join(" ") : ""}
            ${
              pivotData.props.cols.length
                ? " : " + pivotData.props.cols.join(" ")
                : ""
            }`,
      align: "left",
      dispalay: "block",
      fontFamily: "Arial, sans-serif",
      style: {
        fontWeight: "normal",
        fontSize: "20px",
        fontFamily: "Inter, sans-serif",
      },
    },
    tooltip: {
      pointFormat: "{series.name}: <b>{point.y}</b> ({point.percentage:.1f})",
    },
    plotOptions: {
      pie: {
        allowPointSelect: true,
        cursor: "pointer",
        dataLabels: {
          enabled: true,
          format: "<b>{point.name}</b>: {point.y} ({point.percentage:.1f}%)",
        },
      },
    },
    credits: {
      enabled: false,
    },
    exporting: {
      buttons: {
        contextButton: {
          menuItems: [
            "viewFullscreen",
            "printChart",
            "separator",
            "downloadPNG",
            "downloadJPEG",
            "downloadSVG",
            "downloadPDF",
          ],
        },
      },
    },
    series: [
      {
        name: "Share",
        data: pieData,
      },
    ],
  };

  return (
    <div
      style={{ width: "100%", height: "100%" }}
      className="highcharts-figure"
    >
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
};

export default Piechartr;
