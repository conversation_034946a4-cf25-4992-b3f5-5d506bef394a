import React, { useState, useRef, useEffect, useCallback } from "react";
import { Input, Button, message, Popover, Tooltip } from "antd";

// Composants
import EditorToolbar from "./components/EditorToolbar";
import ButtonModal from "./components/ButtonModal";
import HtmlModal from "./components/HtmlModal";
import ImageResizeModal from "./components/ImageResizeModal";
import { Table } from "lucide-react";

const EmailTemplateEditor2 = () => {
  // Références
  const editorRef = useRef(null);
  const toolbarRef = useRef(null);

  const editorContainerRef = useRef(null);
  const imageRef = useRef(null);
  const savedSelection = useRef(null);

  // États pour l'éditeur
  const [content, setContent] = useState("");
  const [subject, setSubject] = useState("");
  const [fontFamily, setFontFamily] = useState("Arial");
  const [fontSize, setFontSize] = useState("14px");
  const [showPreview, setShowPreview] = useState(false);
  const [selectedColor, setSelectedColor] = useState("#000000");
  const [selectedBgColor, setSelectedBgColor] = useState("#ffffff");
  const [templates, setTemplates] = useState([]);

  // États pour les modals
  const [showButtonModal, setShowButtonModal] = useState(false);
  const [showHtmlModal, setShowHtmlModal] = useState(false);
  const [imageModalVisible, setImageModalVisible] = useState(false);
  const [htmlInput, setHtmlInput] = useState("");

  // États pour les images
  const [imageSrc, setImageSrc] = useState("");
  const [imageSize, setImageSize] = useState({ width: 300, height: 200 });
  const [originalSize, setOriginalSize] = useState({ width: 300, height: 200 });
  const [aspectRatio, setAspectRatio] = useState(1.5);
  const [lockAspectRatio, setLockAspectRatio] = useState(true);
  const [selectedImage, setSelectedImage] = useState(null);

  // États pour les liens
  const [linkPopoverVisible, setLinkPopoverVisible] = useState(false);
  const [selectionPosition, setSelectionPosition] = useState(null);
  const [linkUrl, setLinkUrl] = useState("");
  const [currentSelection, setCurrentSelection] = useState(null);
  const restoreSelection = useCallback(() => {
    if (savedSelection.current) {
      const selection = window.getSelection();
      selection.removeAllRanges();
      selection.addRange(savedSelection.current);
      return true;
    }
    return false;
  }, []);
  // État pour le bouton
  const [buttonConfig, setButtonConfig] = useState({
    text: "",
    url: "",
    bgColor: "#1890ff",
    textColor: "#ffffff",
    padding: "10px 20px",
    borderRadius: "5px",
  });
  // Fonctions pour l'éditeur
  const execCommand = useCallback((command, value = null) => {
    document.execCommand(command, false, value);
    if (editorRef.current) {
      editorRef.current.focus();
    }
  }, []);

  const handleColorChange = useCallback(
    (color) => {
      setSelectedColor(color);
      execCommand("foreColor", color);
    },
    [execCommand]
  );

  const handleEditorMouseUp = () => {
    // Appeler la fonction de sauvegarde de sélection du toolbar
    if (toolbarRef.current && toolbarRef.current.handleEditorMouseUp) {
      toolbarRef.current.handleEditorMouseUp();
    }
  };

  // Fonctions pour les liens
  const handleLinkClick = useCallback(() => {
    const selection = window.getSelection();
    if (selection.toString().trim() === "") {
      message.warning("Veuillez sélectionner du texte avant d'ajouter un lien");
      return;
    }

    // Sauvegarder la sélection
    const range = selection.getRangeAt(0);
    setCurrentSelection({
      range: range.cloneRange(),
      text: selection.toString(),
    });

    // Calculer la position pour le popover
    const rect = range.getBoundingClientRect();
    const editorRect = editorRef.current.getBoundingClientRect();

    setSelectionPosition({
      x: rect.left + rect.width / 2 - editorRect.left,
      y: rect.bottom - editorRect.top,
    });

    setLinkPopoverVisible(true);
  }, []);

  const applyLink = useCallback(() => {
    if (!currentSelection || !currentSelection.range) return;

    let url = linkUrl.trim();
    if (!url) {
      message.warning("Veuillez entrer une URL valide");
      return;
    }

    // Ajouter le préfixe https:// si nécessaire
    if (!url.startsWith("http://") && !url.startsWith("https://")) {
      url = `https://${url}`;
    }

    try {
      // Restaurer la sélection
      const selection = window.getSelection();
      selection.removeAllRanges();
      selection.addRange(currentSelection.range);

      // Créer le lien
      execCommand("createLink", url);

      // Mettre à jour le contenu
      setContent(editorRef.current.innerHTML);

      // Initialiser les gestionnaires d'événements pour les liens
      setTimeout(() => {
        initializeLinkHandlers();
      }, 100);
    } catch (error) {
      console.error("Erreur lors de l'application du lien:", error);
      message.error("Erreur lors de l'application du lien");
    } finally {
      // Réinitialiser et fermer le popover
      setLinkUrl("");
      setLinkPopoverVisible(false);
      setSelectionPosition(null);
      setCurrentSelection(null);
    }
  }, [currentSelection, linkUrl, execCommand]);

  const cancelLink = useCallback(() => {
    setLinkUrl("");
    setLinkPopoverVisible(false);
    setSelectionPosition(null);
    setCurrentSelection(null);
  }, []);

  const initializeLinkHandlers = useCallback(() => {
    if (!editorRef.current) return;

    const links = editorRef.current.querySelectorAll("a");
    links.forEach((link) => {
      link.addEventListener("click", (e) => {
        e.preventDefault();
      });
    });
  }, []);

  // Fonctions pour les images
  const handleImageUpload = useCallback(() => {
    // Sauvegarder la sélection actuelle
    saveSelection();

    const input = document.createElement("input");
    input.type = "file";
    input.accept = "image/*";

    input.onchange = (e) => {
      const file = e.target.files[0];
      if (!file) return;

      if (!file.type.startsWith("image/")) {
        message.error("Veuillez sélectionner un fichier image valide");
        return;
      }

      const reader = new FileReader();
      reader.onload = (event) => {
        const img = new Image();
        img.onload = () => {
          // Définir les dimensions originales et initiales
          const originalWidth = img.width;
          const originalHeight = img.height;

          setOriginalSize({ width: originalWidth, height: originalHeight });

          // Calculer les dimensions initiales (max 500px de large)
          const maxWidth = 500;
          let initialWidth = originalWidth;
          let initialHeight = originalHeight;

          if (initialWidth > maxWidth) {
            const ratio = maxWidth / initialWidth;
            initialWidth = maxWidth;
            initialHeight = originalHeight * ratio;
          }

          setImageSize({ width: initialWidth, height: initialHeight });
          setAspectRatio(originalWidth / originalHeight);

          // Définir l'image source et ouvrir le modal
          setImageSrc(event.target.result);
          setSelectedImage(file);
          setImageModalVisible(true);
        };
        img.src = event.target.result;
      };
      reader.readAsDataURL(file);
    };

    input.click();
  }, []);

  const insertImageFromUrl = useCallback(() => {
    // Sauvegarder la sélection actuelle
    saveSelection();

    const url = prompt("Entrez l'URL de l'image:");
    if (!url || !url.trim()) return;

    const img = new Image();
    img.onload = () => {
      // Définir les dimensions originales et initiales
      const originalWidth = img.width;
      const originalHeight = img.height;

      setOriginalSize({ width: originalWidth, height: originalHeight });

      // Calculer les dimensions initiales (max 500px de large)
      const maxWidth = 500;
      let initialWidth = originalWidth;
      let initialHeight = originalHeight;

      if (initialWidth > maxWidth) {
        const ratio = maxWidth / initialWidth;
        initialWidth = maxWidth;
        initialHeight = originalHeight * ratio;
      }

      setImageSize({ width: initialWidth, height: initialHeight });
      setAspectRatio(originalWidth / originalHeight);

      // Définir l'image source et ouvrir le modal
      setImageSrc(url);
      setImageModalVisible(true);
    };
    img.onerror = () => {
      message.error("Impossible de charger l'image depuis cette URL");
    };
    img.src = url;
  }, []);

  const handleResize = useCallback((e, { size }) => {
    const { width, height } = size;
    setImageSize({ width, height });
  }, []);

  const resetImageSize = useCallback(() => {
    if (lockAspectRatio) {
      // Maintenir le ratio mais revenir aux dimensions originales
      setImageSize({
        width: originalSize.width,
        height: originalSize.height,
      });
    } else {
      // Simplement revenir aux dimensions originales
      setImageSize({
        width: originalSize.width,
        height: originalSize.height,
      });
      // Réinitialiser également le ratio
      setAspectRatio(originalSize.width / originalSize.height);
      setLockAspectRatio(true);
    }
  }, [lockAspectRatio, originalSize]);

  const insertImage = useCallback(() => {
    try {
      // Restaurer la sélection sauvegardée
      if (!restoreSelection()) {
        // Si pas de sélection, insérer à la fin
        editorRef.current.focus();
        const selection = window.getSelection();
        const range = document.createRange();
        range.setStart(editorRef.current, editorRef.current.childNodes.length);
        range.collapse(true);
        selection.removeAllRanges();
        selection.addRange(range);
      }

      // Créer l'élément image
      const imgElement = document.createElement("img");
      imgElement.src = imageSrc;
      imgElement.alt = "Image insérée";
      imgElement.style.width = `${imageSize.width}px`;
      imgElement.style.height = `${imageSize.height}px`;
      imgElement.style.maxWidth = "100%";

      // Insérer l'image
      const selection = window.getSelection();
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        range.deleteContents();
        range.insertNode(imgElement);
        range.setStartAfter(imgElement);
        range.setEndAfter(imgElement);
        selection.removeAllRanges();
        selection.addRange(range);
      }

      // Mettre à jour le contenu
      setContent(editorRef.current.innerHTML);

      // Fermer le modal et nettoyer
      setImageModalVisible(false);
      setImageSrc("");
      setSelectedImage(null);
      savedSelection.current = null;

      message.success("Image insérée avec succès");
    } catch (error) {
      console.error("Erreur lors de l'insertion de l'image:", error);
      message.error("Erreur lors de l'insertion de l'image");
    }
  }, [imageSrc, imageSize, restoreSelection]);

  // Fonctions pour le HTML
  const insertHtml = useCallback(() => {
    if (!htmlInput.trim()) {
      message.warning("Veuillez saisir du code HTML");
      return;
    }

    try {
      // Créer un élément temporaire pour parser le HTML
      const tempDiv = document.createElement("div");
      tempDiv.innerHTML = htmlInput.trim();

      // Essayer de restaurer la sélection sauvegardée
      if (restoreSelection()) {
        const selection = window.getSelection();
        const range = selection.getRangeAt(0);

        // Insérer chaque nœud du HTML
        const fragment = document.createDocumentFragment();
        while (tempDiv.firstChild) {
          fragment.appendChild(tempDiv.firstChild);
        }

        range.deleteContents();
        range.insertNode(fragment);
        range.collapse(false);
        selection.removeAllRanges();
        selection.addRange(range);
      } else {
        // Si aucune sélection, ajouter à la fin de l'éditeur
        while (tempDiv.firstChild) {
          editorRef.current.appendChild(tempDiv.firstChild);
        }
      }

      // Mettre à jour le contenu
      setContent(editorRef.current.innerHTML);

      // Initialiser les gestionnaires d'événements pour les nouveaux liens
      setTimeout(() => {
        initializeLinkHandlers();
      }, 100);

      message.success("HTML inséré avec succès");
    } catch (error) {
      console.error("Erreur lors de l'insertion du HTML:", error);
      message.error("Erreur lors de l'insertion du HTML");
    }

    // Nettoyer et fermer le modal
    setHtmlInput("");
    setShowHtmlModal(false);
    savedSelection.current = null;
  }, [htmlInput, restoreSelection, initializeLinkHandlers]);

  // Fonctions pour les boutons
  const insertButton = useCallback(() => {
    try {
      // Créer l'élément bouton (lien stylisé)
      const button = document.createElement("a");
      button.href = buttonConfig.url;
      button.textContent = buttonConfig.text;
      button.style.cssText = `
        display: inline-block;
        background-color: ${buttonConfig.bgColor};
        color: ${buttonConfig.textColor};
        padding: ${buttonConfig.padding || "10px 20px"};
        text-decoration: none;
        border-radius: ${buttonConfig.borderRadius || "5px"};
        font-weight: bold;
        margin: 10px 0;
      `;
      button.setAttribute("target", "_blank");
      button.setAttribute("rel", "noopener noreferrer");

      // Vérifier si l'éditeur est disponible
      if (!editorRef.current) {
        console.error("Référence de l'éditeur non disponible");
        message.error("Erreur: Éditeur non disponible");
        return;
      }

      // Restaurer la sélection sauvegardée
      if (!restoreSelection()) {
        // Si pas de sélection, insérer à la fin
        editorRef.current.focus();
        const selection = window.getSelection();
        const range = document.createRange();
        range.setStart(editorRef.current, editorRef.current.childNodes.length);
        range.collapse(true);
        selection.removeAllRanges();
        selection.addRange(range);
      }

      // Obtenir la sélection actuelle
      const selection = window.getSelection();

      // Si une sélection existe, insérer le bouton à cet endroit
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);

        // Vérifier si la sélection est dans l'éditeur
        let container = range.commonAncestorContainer;
        while (container && container !== editorRef.current) {
          container = container.parentNode;
        }

        if (!container) {
          // La sélection n'est pas dans l'éditeur, placer le bouton à la fin
          editorRef.current.appendChild(button);
        } else {
          // Insérer le bouton à la position de la sélection
          range.deleteContents();
          range.insertNode(button);

          // Placer le curseur après le bouton
          range.setStartAfter(button);
          range.setEndAfter(button);
          selection.removeAllRanges();
          selection.addRange(range);
        }
      } else {
        // Aucune sélection, ajouter le bouton à la fin de l'éditeur
        editorRef.current.appendChild(button);
      }

      // Mettre à jour le contenu
      setContent(editorRef.current.innerHTML);

      // Afficher un message de succès
      message.success("Bouton inséré avec succès");

      // Fermer le modal
      setShowButtonModal(false);

      // Réinitialiser le focus sur l'éditeur
      editorRef.current.focus();
    } catch (error) {
      console.error("Erreur lors de l'insertion du bouton:", error);
      message.error("Erreur lors de l'insertion du bouton");
    }
  }, [buttonConfig, restoreSelection]);

  // Fonctions pour les variables
  const insertVariable = useCallback((variable) => {
    const selection = window.getSelection();
    if (selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const span = document.createElement("span");
      span.style.backgroundColor = "#e3f2fd";
      span.style.padding = "2px 4px";
      span.style.borderRadius = "3px";
      span.style.color = "#1976d2";
      span.textContent = `{{${variable}}}`;
      range.insertNode(span);
      range.setStartAfter(span);
      selection.removeAllRanges();
      selection.addRange(range);

      // Mettre à jour le contenu
      setContent(editorRef.current.innerHTML);
    }
  }, []);

  // Fonctions pour les templates
  const saveTemplate = useCallback(() => {
    const template = {
      id: Date.now(),
      name: prompt("Nom du template:") || "Template sans nom",
      subject,
      content: editorRef.current?.innerHTML || "",
      createdAt: new Date().toLocaleString(),
    };
    setTemplates([...templates, template]);
    message.success("Template sauvegardé!");
  }, [subject, templates]);

  const loadTemplate = useCallback((template) => {
    setSubject(template.subject);
    if (editorRef.current) {
      editorRef.current.innerHTML = template.content;
      setContent(template.content);
    }
  }, []);

  const exportHTML = useCallback(() => {
    const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${subject}</title>
    <style>
        body { font-family: ${fontFamily}, sans-serif; margin: 0; padding: 20px; }
        .email-container { max-width: 600px; margin: 0 auto; }
    </style>
</head>
<body>
    <div class="email-container">
        <h1>${subject}</h1>
        ${editorRef.current?.innerHTML || ""}
    </div>
</body>
</html>`;

    const blob = new Blob([htmlContent], { type: "text/html" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${subject || "email-template"}.html`;
    a.click();
    URL.revokeObjectURL(url);
  }, [subject, fontFamily]);

  // Fonctions utilitaires
  const saveSelection = useCallback(() => {
    const selection = window.getSelection();
    if (selection.rangeCount > 0) {
      savedSelection.current = selection.getRangeAt(0).cloneRange();
      return true;
    }
    return false;
  }, []);

  const handleEditorClick = useCallback(() => {
    // Fermer les popovers ouverts
    setLinkPopoverVisible(false);
  }, []);

  // Fonction pour insérer un tableau
  const insertTable = useCallback((rows, cols) => {
    if (!editorRef.current) return;

    // Créer le tableau
    const table = document.createElement("table");
    table.setAttribute("contenteditable", "true");
    table.style.width = "100%";
    table.style.borderCollapse = "collapse";
    table.style.marginBottom = "1em";
    table.className = "editor-table";

    const tbody = document.createElement("tbody");
    table.appendChild(tbody);

    // Créer les lignes et cellules
    for (let i = 0; i < rows; i++) {
      const tr = document.createElement("tr");

      for (let j = 0; j < cols; j++) {
        const td = document.createElement("td");
        td.style.border = "1px solid #ddd";
        td.style.padding = "8px";
        td.style.position = "relative";

        // Ajouter un espace vide pour que la cellule soit éditable
        td.innerHTML = "<br>";

        tr.appendChild(td);
      }

      tbody.appendChild(tr);
    }

    // Restaurer la sélection sauvegardée si elle existe
    if (savedSelection.current) {
      const selection = window.getSelection();
      selection.removeAllRanges();
      selection.addRange(savedSelection.current);
    }

    // Insérer le tableau à la position du curseur
    const selection = window.getSelection();
    if (selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);

      // Vérifier si la sélection est dans l'éditeur
      let container = range.commonAncestorContainer;
      while (container && container !== editorRef.current) {
        container = container.parentNode;
      }

      if (!container) {
        // La sélection n'est pas dans l'éditeur, placer le tableau à la fin
        editorRef.current.appendChild(table);
      } else {
        // Insérer le tableau à la position de la sélection
        range.deleteContents();
        range.insertNode(table);

        // Placer le curseur après le tableau
        range.setStartAfter(table);
        range.setEndAfter(table);
        selection.removeAllRanges();
        selection.addRange(range);
      }
    } else {
      // Aucune sélection, ajouter le tableau à la fin de l'éditeur
      editorRef.current.appendChild(table);
    }

    // Mettre à jour le contenu
    setContent(editorRef.current.innerHTML);

    // Initialiser les gestionnaires d'événements pour le tableau
    initializeTableHandlers();

    // Placer le curseur dans la première cellule
    setTimeout(() => {
      const firstCell = table.querySelector("td");
      if (firstCell) {
        const newRange = document.createRange();
        newRange.selectNodeContents(firstCell);
        newRange.collapse(true);

        const selection = window.getSelection();
        selection.removeAllRanges();
        selection.addRange(newRange);

        // Assurer que l'éditeur a le focus
        editorRef.current.focus();
      }
    }, 0);
  }, []);

  // Fonction pour initialiser les gestionnaires d'événements des tableaux
  const initializeTableHandlers = useCallback(() => {
    if (!editorRef.current) return;

    // Sélectionner tous les tableaux dans l'éditeur
    const tables = editorRef.current.querySelectorAll("table.editor-table");

    tables.forEach((table) => {
      // S'assurer que nous n'ajoutons pas de gestionnaires en double
      if (table.dataset.initialized === "true") return;

      // Marquer le tableau comme initialisé
      table.dataset.initialized = "true";

      // Ajouter un gestionnaire de clic pour le tableau entier
      table.addEventListener("click", (e) => {
        // Sélectionner le tableau actif
        setActiveTable(table);
      });

      // Ajouter un gestionnaire de survol pour le tableau
      table.addEventListener("mouseover", (e) => {
        // Sélectionner le tableau actif
        setActiveTable(table);
      });

      // Ajouter un gestionnaire de sortie pour le tableau
      table.addEventListener("mouseout", (e) => {
        // Ne pas désélectionner le tableau si on survole le popover
        const relatedTarget = e.relatedTarget;
        if (
          relatedTarget &&
          (relatedTarget.closest(".table-popover") ||
            relatedTarget.closest("table.editor-table") === table)
        ) {
          return;
        }

        // Vérifier si le curseur est sorti complètement du tableau
        if (
          !table.contains(e.relatedTarget) &&
          !e.relatedTarget?.closest(".table-popover")
        ) {
          // Désélectionner le tableau après un court délai
          // pour permettre à l'utilisateur d'atteindre le popover
          setTimeout(() => {
            if (!document.querySelector(".table-popover:hover")) {
              setActiveTable(null);
            }
          }, 200);
        }
      });

      // Ajouter des gestionnaires pour les cellules
      const cells = table.querySelectorAll("td");
      cells.forEach((cell) => {
        // Double-clic pour éditer le contenu
        cell.addEventListener("dblclick", () => {
          cell.focus();
        });
      });
    });
  }, []);

  // État pour stocker le tableau actif
  const [activeTable, setActiveTable] = useState(null);
  // État pour contrôler la visibilité du popover de tableau
  const [tablePopoverVisible, setTablePopoverVisible] = useState(false);
  // Position du popover
  const [tablePopoverPosition, setTablePopoverPosition] = useState({
    x: 0,
    y: 0,
  });

  // Effet pour afficher le popover lorsqu'un tableau est sélectionné
  useEffect(() => {
    if (activeTable) {
      // Calculer la position du popover (en haut à droite du tableau)
      const tableRect = activeTable.getBoundingClientRect();
      const editorRect = editorRef.current.getBoundingClientRect();

      setTablePopoverPosition({
        x: tableRect.right - editorRect.left - 20,
        y: tableRect.top - editorRect.top - 10,
      });

      setTablePopoverVisible(true);

      // Ajouter un gestionnaire pour fermer le popover lors d'un clic en dehors
      const handleClickOutside = (e) => {
        // Ne pas fermer si on clique sur le tableau ou le popover
        if (
          activeTable &&
          (activeTable.contains(e.target) || e.target.closest(".table-popover"))
        ) {
          return;
        }

        setTablePopoverVisible(false);
        setActiveTable(null);
      };

      document.addEventListener("mousedown", handleClickOutside);

      return () => {
        document.removeEventListener("mousedown", handleClickOutside);
      };
    } else {
      setTablePopoverVisible(false);
    }
  }, [activeTable]);

  // Fonctions pour manipuler le tableau
  const insertRowBefore = useCallback(() => {
    if (!activeTable) return;

    // Trouver la première ligne sélectionnée
    const selection = window.getSelection();
    if (!selection.rangeCount) return;

    const range = selection.getRangeAt(0);
    let cell = range.startContainer;

    // Remonter jusqu'à la cellule
    while (cell && cell.nodeName !== "TD") {
      cell = cell.parentNode;
    }

    if (!cell) return;

    const row = cell.parentNode;
    const newRow = row.cloneNode(true);

    // Vider le contenu des cellules
    Array.from(newRow.cells).forEach((cell) => {
      cell.innerHTML = "<br>";
    });

    row.parentNode.insertBefore(newRow, row);

    // Mettre à jour le contenu
    setContent(editorRef.current.innerHTML);

    // Réinitialiser les gestionnaires
    initializeTableHandlers();
  }, [activeTable, initializeTableHandlers]);

  const insertRowAfter = useCallback(() => {
    if (!activeTable) return;

    // Trouver la première ligne sélectionnée
    const selection = window.getSelection();
    if (!selection.rangeCount) return;

    const range = selection.getRangeAt(0);
    let cell = range.startContainer;

    // Remonter jusqu'à la cellule
    while (cell && cell.nodeName !== "TD") {
      cell = cell.parentNode;
    }

    if (!cell) return;

    const row = cell.parentNode;
    const newRow = row.cloneNode(true);

    // Vider le contenu des cellules
    Array.from(newRow.cells).forEach((cell) => {
      cell.innerHTML = "<br>";
    });

    if (row.nextSibling) {
      row.parentNode.insertBefore(newRow, row.nextSibling);
    } else {
      row.parentNode.appendChild(newRow);
    }

    // Mettre à jour le contenu
    setContent(editorRef.current.innerHTML);

    // Réinitialiser les gestionnaires
    initializeTableHandlers();
  }, [activeTable, initializeTableHandlers]);

  const insertColumnBefore = useCallback(() => {
    if (!activeTable) return;

    // Trouver la première cellule sélectionnée
    const selection = window.getSelection();
    if (!selection.rangeCount) return;

    const range = selection.getRangeAt(0);
    let cell = range.startContainer;

    // Remonter jusqu'à la cellule
    while (cell && cell.nodeName !== "TD") {
      cell = cell.parentNode;
    }

    if (!cell) return;

    const columnIndex = Array.from(cell.parentNode.cells).indexOf(cell);

    // Ajouter une cellule à chaque ligne
    Array.from(activeTable.rows).forEach((row) => {
      const newCell = document.createElement("td");
      newCell.style.border = "1px solid #ddd";
      newCell.style.padding = "8px";
      newCell.style.position = "relative";
      newCell.innerHTML = "<br>";

      row.insertBefore(newCell, row.cells[columnIndex]);
    });

    // Mettre à jour le contenu
    setContent(editorRef.current.innerHTML);

    // Réinitialiser les gestionnaires
    initializeTableHandlers();
  }, [activeTable, initializeTableHandlers]);

  const insertColumnAfter = useCallback(() => {
    if (!activeTable) return;

    // Trouver la première cellule sélectionnée
    const selection = window.getSelection();
    if (!selection.rangeCount) return;

    const range = selection.getRangeAt(0);
    let cell = range.startContainer;

    // Remonter jusqu'à la cellule
    while (cell && cell.nodeName !== "TD") {
      cell = cell.parentNode;
    }

    if (!cell) return;

    const columnIndex = Array.from(cell.parentNode.cells).indexOf(cell);

    // Ajouter une cellule à chaque ligne
    Array.from(activeTable.rows).forEach((row) => {
      const newCell = document.createElement("td");
      newCell.style.border = "1px solid #ddd";
      newCell.style.padding = "8px";
      newCell.style.position = "relative";
      newCell.innerHTML = "<br>";

      if (columnIndex === row.cells.length - 1) {
        row.appendChild(newCell);
      } else {
        row.insertBefore(newCell, row.cells[columnIndex + 1]);
      }
    });

    // Mettre à jour le contenu
    setContent(editorRef.current.innerHTML);

    // Réinitialiser les gestionnaires
    initializeTableHandlers();
  }, [activeTable, initializeTableHandlers]);

  const deleteRow = useCallback(() => {
    if (!activeTable) return;

    // Trouver la première ligne sélectionnée
    const selection = window.getSelection();
    if (!selection.rangeCount) return;

    const range = selection.getRangeAt(0);
    let cell = range.startContainer;

    // Remonter jusqu'à la cellule
    while (cell && cell.nodeName !== "TD") {
      cell = cell.parentNode;
    }

    if (!cell) return;

    const row = cell.parentNode;

    row.parentNode.removeChild(row);

    // Si c'était la dernière ligne, supprimer le tableau
    if (activeTable.rows.length === 0) {
      activeTable.parentNode.removeChild(activeTable);
      setActiveTable(null);
      setTablePopoverVisible(false);
    }

    // Mettre à jour le contenu
    setContent(editorRef.current.innerHTML);
  }, [activeTable]);

  const deleteColumn = useCallback(() => {
    if (!activeTable) return;

    // Trouver la première cellule sélectionnée
    const selection = window.getSelection();
    if (!selection.rangeCount) return;

    const range = selection.getRangeAt(0);
    let cell = range.startContainer;

    // Remonter jusqu'à la cellule
    while (cell && cell.nodeName !== "TD") {
      cell = cell.parentNode;
    }

    if (!cell) return;

    const columnIndex = Array.from(cell.parentNode.cells).indexOf(cell);

    // Supprimer la cellule de chaque ligne
    Array.from(activeTable.rows).forEach((row) => {
      if (row.cells[columnIndex]) {
        row.removeChild(row.cells[columnIndex]);
      }
    });

    // Si c'était la dernière colonne, supprimer le tableau
    if (activeTable.rows[0].cells.length === 0) {
      activeTable.parentNode.removeChild(activeTable);
      setActiveTable(null);
      setTablePopoverVisible(false);
    }

    // Mettre à jour le contenu
    setContent(editorRef.current.innerHTML);
  }, [activeTable]);

  const deleteTable = useCallback(() => {
    if (!activeTable) return;

    activeTable.parentNode.removeChild(activeTable);
    setActiveTable(null);
    setTablePopoverVisible(false);

    // Mettre à jour le contenu
    setContent(editorRef.current.innerHTML);
  }, [activeTable]);

  // Composant de sélection de tableau
  const TableSelector = () => {
    const [visible, setVisible] = useState(false);
    const [hoveredSize, setHoveredSize] = useState({ rows: 0, cols: 0 });
    const [selectedSize, setSelectedSize] = useState({ rows: 0, cols: 0 });

    const maxRows = 8;
    const maxCols = 8;

    const handleCellHover = (row, col) => {
      setHoveredSize({ rows: row, cols: col });
    };

    const handleCellClick = (row, col) => {
      setVisible(false);
      insertTable(row, col);
    };

    const content = (
      <div style={{ width: "200px" }}>
        <div style={{ marginBottom: "8px", fontWeight: "bold" }}>
          {hoveredSize.rows > 0 && hoveredSize.cols > 0
            ? `${hoveredSize.rows} × ${hoveredSize.cols}`
            : "Insérer un tableau"}
        </div>
        <div
          style={{
            display: "grid",
            gridTemplateColumns: `repeat(${maxCols}, 1fr)`,
            gap: "2px",
          }}
        >
          {Array.from({ length: maxRows * maxCols }).map((_, index) => {
            const row = Math.floor(index / maxCols) + 1;
            const col = (index % maxCols) + 1;

            return (
              <div
                key={index}
                style={{
                  width: "20px",
                  height: "20px",
                  backgroundColor:
                    row <= hoveredSize.rows && col <= hoveredSize.cols
                      ? "#1890ff"
                      : "#f0f0f0",
                  cursor: "pointer",
                  transition: "background-color 0.2s",
                }}
                onMouseEnter={() => handleCellHover(row, col)}
                onClick={() => handleCellClick(row, col)}
              />
            );
          })}
        </div>
      </div>
    );

    return (
      <Popover
        content={content}
        trigger="click"
        visible={visible}
        onVisibleChange={setVisible}
        placement="bottom"
      >
        <Tooltip title="Insérer un tableau">
          <Button icon={<Table />} onClick={() => setVisible(true)} />
        </Tooltip>
      </Popover>
    );
  };

  // Effets
  useEffect(() => {
    // Initialiser les gestionnaires d'événements pour les liens
    initializeLinkHandlers();

    // Charger un template par défaut si l'éditeur est vide
    // if (editorRef.current && !editorRef.current.innerHTML.trim()) {
    //   editorRef.current.innerHTML =
    //     "<p>Commencez à écrire votre email ici...</p>";
    // }

    return () => {
      // Nettoyer les événements lors du démontage
      if (editorRef.current) {
        const links = editorRef.current.querySelectorAll("a");
        links.forEach((link) => {
          link.removeEventListener("click", (e) => {
            e.preventDefault();
          });
        });
      }
    };
  }, [initializeLinkHandlers]);

  useEffect(() => {
    // Initialiser les gestionnaires d'événements pour les tableaux existants
    if (editorRef.current) {
      initializeTableHandlers();
    }

    // Ajouter des styles CSS pour les tableaux
    const style = document.createElement("style");
    style.textContent = `
      .editor-table {
        border-collapse: collapse;
        width: 100%;
        margin-bottom: 1em;
        position: relative;
      }
      
      .editor-table td {
        border: 1px solid #ddd;
        padding: 8px;
        position: relative;
        min-width: 30px;
        min-height: 20px;
      }
      
      .editor-table:hover {
        outline: 2px solid #1890ff;
      }
      
      .editor-table:focus-within {
        outline: 2px solid #1890ff;
      }
      
      .editor-table td:focus {
        background-color: rgba(24, 144, 255, 0.1);
      }
      
      .table-popover {
        animation: fadeIn 0.2s;
        pointer-events: auto;
      }
      
      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }
    `;

    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, [initializeTableHandlers]);

  // Rendu du popover de tableau
  const renderTablePopover = () => {
    if (!tablePopoverVisible || !activeTable) return null;

    return (
      <div
        className="table-popover"
        style={{
          position: "absolute",
          left: `${tablePopoverPosition.x}px`,
          top: `${tablePopoverPosition.y}px`,
          zIndex: 1000,
          backgroundColor: "white",
          border: "1px solid #d9d9d9",
          borderRadius: "4px",
          boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
          padding: "8px",
        }}
        onMouseEnter={() => {
          // Assurer que le popover reste visible quand on le survole
          setTablePopoverVisible(true);
        }}
        onMouseLeave={(e) => {
          // Vérifier si on retourne sur le tableau
          if (!activeTable.contains(e.relatedTarget)) {
            // Fermer le popover après un court délai
            setTimeout(() => {
              if (!activeTable.contains(document.activeElement)) {
                setTablePopoverVisible(false);
                setActiveTable(null);
              }
            }, 200);
          }
        }}
      >
        <div style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
          <div style={{ display: "flex", gap: "8px" }}>
            <Button
              size="small"
              onClick={insertRowBefore}
              title="Insérer une ligne au-dessus"
            >
              ↑ Ligne
            </Button>
            <Button
              size="small"
              onClick={insertRowAfter}
              title="Insérer une ligne en-dessous"
            >
              ↓ Ligne
            </Button>
          </div>

          <div style={{ display: "flex", gap: "8px" }}>
            <Button
              size="small"
              onClick={insertColumnBefore}
              title="Insérer une colonne à gauche"
            >
              ← Colonne
            </Button>
            <Button
              size="small"
              onClick={insertColumnAfter}
              title="Insérer une colonne à droite"
            >
              → Colonne
            </Button>
          </div>

          <div style={{ display: "flex", gap: "8px" }}>
            <Button
              size="small"
              onClick={deleteRow}
              danger
              title="Supprimer la ligne"
            >
              Suppr. ligne
            </Button>
            <Button
              size="small"
              onClick={deleteColumn}
              danger
              title="Supprimer la colonne"
            >
              Suppr. colonne
            </Button>
          </div>

          <Button
            size="small"
            onClick={deleteTable}
            danger
            title="Supprimer le tableau"
          >
            Supprimer tableau
          </Button>
        </div>
      </div>
    );
  };

  // Rendu
  return (
    <div className="email-template-editor">
      {/* Barre d'outils */}
      <div className="p-2" ref={toolbarRef}>
        <EditorToolbar
          execCommand={execCommand}
          handleLinkClick={handleLinkClick}
          handleImageUpload={handleImageUpload}
          insertImageFromUrl={insertImageFromUrl}
          openButtonModal={() => {
            saveSelection();
            setShowButtonModal(true);
          }}
          openHtmlModal={() => {
            saveSelection();
            setShowHtmlModal(true);
          }}
          showPreview={showPreview}
          togglePreview={() => setShowPreview(!showPreview)}
          saveTemplate={saveTemplate}
          exportHTML={exportHTML}
          selectedColor={selectedColor}
          handleColorChange={handleColorChange}
          insertVariable={insertVariable}
          setFontSize={setFontSize}
          fontSize={fontSize}
          setFontFamily={setFontFamily}
          fontFamily={fontFamily}
          editorRef={editorRef}
        />
        <TableSelector />
      </div>

      {/* Éditeur */}
      <div
        ref={editorContainerRef}
        className="editor-container"
        style={{ position: "relative" }}
      >
        <div
          ref={editorRef}
          className="editor-content"
          contentEditable={true}
          dangerouslySetInnerHTML={{ __html: content }}
          onInput={(e) => setContent(e.target.innerHTML)}
          onMouseUp={handleEditorMouseUp}
          style={{
            minHeight: "300px",
            border: "1px solid #d9d9d9",
            borderRadius: "4px",
            padding: "8px",
            fontFamily: fontFamily,
            fontSize: fontSize,
          }}
        />

        {/* Rendu du popover de tableau */}
        {renderTablePopover()}
      </div>
      {/* Modals */}
      <ButtonModal
        isOpen={showButtonModal}
        onClose={() => setShowButtonModal(false)}
        buttonConfig={buttonConfig}
        setButtonConfig={setButtonConfig}
        onInsert={insertButton}
      />

      <HtmlModal
        isOpen={showHtmlModal}
        onClose={() => setShowHtmlModal(false)}
        htmlInput={htmlInput}
        setHtmlInput={setHtmlInput}
        onInsert={insertHtml}
      />

      <ImageResizeModal
        isVisible={imageModalVisible}
        onCancel={() => {
          setImageModalVisible(false);
          setImageSrc("");
          setSelectedImage(null);
          savedSelection.current = null;
        }}
        imageSrc={imageSrc}
        imageSize={imageSize}
        setImageSize={setImageSize}
        originalSize={originalSize}
        aspectRatio={aspectRatio}
        lockAspectRatio={lockAspectRatio}
        setLockAspectRatio={setLockAspectRatio}
        onInsert={insertImage}
        onReset={resetImageSize}
        handleResize={handleResize}
        imageRef={imageRef}
      />

      {/* Popover pour les liens */}
    </div>
  );
};

export default EmailTemplateEditor2;
