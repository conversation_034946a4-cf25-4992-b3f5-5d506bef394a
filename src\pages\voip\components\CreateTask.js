import { useCallback, useEffect, useRef, useState } from "react";
import { <PERSON><PERSON>, Drawer, Dropdown, Form, Skeleton, Space } from "antd";
import CreateTaskForm from "../../tasks/CreateTaskForm";
import { toastNotification } from "../../../components/ToastNotification";
import { useTranslation } from "react-i18next";
import MainService from "../../../services/main.service";
import {
  CloseOutlined,
  RightCircleOutlined,
  UpOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";
import { useWindowSize } from "../../clients&users/components/WindowSize";
import {
  createVisio,
  setKpiDateVisio,
  setKpiVisio,
} from "../../../new-redux/actions/visio.actions/visio";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import { getName } from "../../layouts/chat/utils/ConversationUtils";
import { useLocation, useNavigate } from "react-router-dom";
import { setTask360 } from "../../../new-redux/actions/chat.actions/Input";
import { URL_ENV } from "index";
import { AvatarChat } from "components/Chat";
import { displayStatKey } from "pages/tasks/KpiGrid";
import useDebounce from "pages/components/UseDebounce/UseDebounce";
import moment from "moment";
import { findNextZIndex } from "../helpers/helpersFunc";
import { setRefreshMailInbox } from "new-redux/actions/mail.actions";
import { EXTENSIONS_ARRAY } from "pages/tasks/helpers/calculateSum";
import { setActiveActivity360 } from "new-redux/actions/vue360.actions/vue360";
import { SET_DATE_CONVERSATION_TO_TASK } from "new-redux/constants";

const CreateTask = ({
  open,
  setOpen,
  mask,
  source = "",
  idCall,
  mailingProps,
  titleLabel,
  listVisio = false,
  addTask = () => {},
  taskId,
  setTaskId,
  message = "",
  relations = [],
  setShouldFetchData,
  fromVue360,
  setSelectedKey = () => {},
}) => {
  //
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const { pathname } = useLocation();
  const navigate = useNavigate();
  const formTopRef = useRef(null);
  const windowSize = useWindowSize();
  const [form] = Form.useForm();
  const [emailNotification, setEmailNotification] = useState(false);

  const contactInfo = useSelector(
    (state) => state?.contacts?.contactHeaderInfo
  );
  const selectedConversation = useSelector(
    (state) => state.ChatRealTime.selectedConversation
  );
  const chat = useSelector((state) => state.chat);

  const { user } = useSelector((state) => state?.user);
  const { contactInfoFromDrawer, openView360InDrawer } = useSelector(
    (state) => state?.vue360
  );
  const [activityType, setActivityType] = useState([]);
  const [guests, setGuests] = useState([]);
  const [followers, setFollowers] = useState([]);
  const [pipelines, setPipelines] = useState([]);
  const [propsTitle, setPropsTitle] = useState("");
  const [loadGuests, setLoadGuests] = useState(false);

  const [selectedTaskType, setSelectedTaskType] = useState(null);
  const [isError, setIsError] = useState(false);

  const [guestsListPage, setGuestsListPage] = useState(1);
  const [followersListPage, setFollowersListPage] = useState(1);
  const [guestsListLastPage, setGuestsListLastPage] = useState(null);
  const [followersListLastPage, setFollowersListLastPage] = useState(null);

  const [guestsSearchQuery, setGuestsSearchQuery] = useState("");
  const [followersSearchQuery, setFollowersSearchQuery] = useState("");
  const [loadOwners, setLoadOwners] = useState(false);

  const [selectedGuests, setSelectedGuests] = useState([]);
  const [selectedFollowers, setSelectedFollowers] = useState([]);

  const [noteAndDescription, setNoteAndDescription] = useState({
    note: "",
    description: message,
  });
  const [files, setFiles] = useState([]);

  const [submitType, setSubmitType] = useState("");
  const [loadingSubmit, setLoadingSubmit] = useState(false);
  const [selectedStartDate, setSelectedStartDate] = useState("");
  const [selectedEndDate, setSelectedEndDate] = useState("");

  const [selectedStartTime, setSelectedStartTime] = useState("");
  const [selectedEndTime, setSelectedEndTime] = useState("");
  const [tasksTypes, setTasksTypes] = useState([]);

  const [singleTaskData, setSingleTaskData] = useState({});
  const [totalEntities, setTotalEntities] = useState({
    colleagues: 0,
    all: 0,
  });

  const [isLoading, setIsLoading] = useState(false);
  const [selectedFamilyMembers, setSelectedFamilyMembers] = useState([
    1, 2, 4, 9,
  ]);
  const debounceGuestsSearch = useDebounce(guestsSearchQuery, 500);
  const debounceFollowersSearch = useDebounce(followersSearchQuery, 500);
  // console.log({ selectedGuests });
  // const x = MainService.getSpecificTask("id")
  useEffect(() => {
    const handleDateFromMessage = (event) => {
      setSelectedStartDate(event.detail.date);
    };
    window.addEventListener(SET_DATE_CONVERSATION_TO_TASK, (e) =>
      handleDateFromMessage(e)
    );
    return () => {
      window.removeEventListener(SET_DATE_CONVERSATION_TO_TASK, (e) =>
        handleDateFromMessage(e)
      );
    };
  }, []);
  const fetchFollowers = async () => {
    setLoadOwners(true);
    try {
      const formData = new FormData();
      formData.append("family_id", 4);
      formData.append("search", debounceFollowersSearch);

      const response = await MainService.getFamilyOptions(
        followersListPage,
        50,
        formData
      );
      if (followersListPage > 1) {
        setFollowers((prev) => [...prev, ...response?.data?.data]);
      } else {
        setFollowers(response?.data?.data);
      }
      setTotalEntities((prev) => ({
        ...prev,
        colleagues: response?.data?.meta?.total,
      }));
      setFollowersListLastPage(response?.data?.meta?.last_page);

      // setFollowers(response.data.data);
      setLoadOwners(false);
    } catch (err) {
      setLoadOwners(false);

      console.log(err);
    }

    // !followersListLastPage && setFollowersListLastPage(total);
  };

  // console.log("render");

  useEffect(() => {
    if (contactInfoFromDrawer.id || contactInfo.id) {
      setSelectedGuests((prev) =>
        relations
          .map((el) => el.child)
          .flat()
          .map((guest) => ({
            id: guest.id,
            label: guest.label_data,
            avatar: guest.avatar,
          }))
      );
    }
    if (
      openView360InDrawer &&
      contactInfoFromDrawer.id &&
      (contactInfoFromDrawer.family_id === 4 ||
        contactInfoFromDrawer.family_id === 1 ||
        contactInfoFromDrawer.family_id === 2) &&
      fromVue360
    ) {
      setSelectedGuests((prev) => [
        {
          id: contactInfoFromDrawer?.id,
          label: getName(contactInfoFromDrawer?.name, "name"),
          avatar:
            contactInfoFromDrawer?.nameImg ||
            getName(contactInfoFromDrawer?.name, "name")[0],
        },
        ...relations
          .map((el) => el.child)
          .flat()
          .map((guest) => ({
            id: guest.id,
            label: guest.label_data,
            avatar: guest.avatar,
          })),
      ]);
    } else if (
      !openView360InDrawer &&
      contactInfo.id &&
      // /^\/((contacts|companies|settings\/users)\/([^/]+)$|directory)/.test(
      //   pathname
      // ) &&
      (contactInfo.family_id === 4 ||
        contactInfo.family_id === 1 ||
        contactInfo.family_id === 2) &&
      fromVue360
    ) {
      setSelectedGuests((prev) => [
        {
          id: contactInfo?.id,
          label: getName(contactInfo?.name, "name"),
          avatar: contactInfo?.nameImg || getName(contactInfo?.name, "name")[0],
        },
        ...relations
          .map((el) => el.child)
          .flat()
          .map((guest) => ({
            id: guest.id,
            label: guest.label_data,
            avatar: guest.avatar,
          })),
      ]);
    }
    if (pathname === "/chat") {
      selectedConversation?.type === "user"
        ? setSelectedGuests([
            {
              id: selectedConversation?.id,
              uuid: selectedConversation?.uuid,
              label: getName(selectedConversation?.name, "name"),
              avatar:
                selectedConversation?.image ||
                getName(selectedConversation?.name, "name")[0],
            },
          ])
        : selectedConversation?.type === "room"
        ? setSelectedGuests(
            chat?.selectedParticipants.map((el) => ({
              id: el?.id,
              label: getName(el?.name, "name"),
              avatar: el?.image || getName(el?.name, "name")[0],
              uuid: el?.uuid,
            }))
          )
        : setSelectedGuests([]);
    }
  }, [
    contactInfo?.id,
    pathname,
    selectedConversation,
    openView360InDrawer,
    contactInfoFromDrawer?.id,
  ]);

  useEffect(() => {
    open && fetchFollowers();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debounceFollowersSearch, followersListPage]);

  useEffect(() => {
    setFollowersListPage(1);
  }, [debounceFollowersSearch]);

  useEffect(() => {
    setGuestsListPage(1);
  }, [debounceGuestsSearch]);

  //
  const fetchGuests = async () => {
    setLoadGuests(true);
    try {
      const formData = new FormData();
      formData.append("family_id", selectedFamilyMembers.toString());
      formData.append("search", debounceGuestsSearch);

      const response = await MainService.getFamilyOptions(
        guestsListPage,
        50,
        formData
      );
      if (guestsListPage > 1) {
        setGuests((prev) => [...prev, ...response?.data?.data]);
      } else {
        setGuests(response?.data?.data);
      }
      setTotalEntities((prev) => ({
        ...prev,
        all: response?.data?.meta?.total,
      }));
      setGuestsListLastPage(response?.data?.meta?.last_page);
      setLoadGuests(false);
    } catch (err) {
      setLoadGuests(false);
    }
    //SOUMAYA: REMOVE FAMILY IDS 1 AND 2
    // formData.append("family_id[]", 4);
    // formData.append("family_id[]", 1);
    // formData.append("family_id[]", 2);
  };

  useEffect(() => {
    open && fetchGuests();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [guestsListPage, selectedFamilyMembers, debounceGuestsSearch]);

  // Get Activity Type & Guests & Followers && Pipelines
  const getParams = useCallback(async () => {
    try {
      setIsLoading(true);
      const {
        data: {
          data: { tasks_type },
        },
      } = await MainService.getTasksTypes();
      setTasksTypes(tasks_type);

      await fetchFollowers();
      await fetchGuests();

      const {
        data: { data },
      } = await MainService.getPipelinesByFamilyTask();

      setActivityType(tasks_type);
      setPipelines(data);
      if (
        openView360InDrawer &&
        contactInfoFromDrawer.id &&
        (contactInfoFromDrawer.family_id === 4 ||
          contactInfoFromDrawer.family_id === 1 ||
          contactInfoFromDrawer.family_id === 2) &&
        fromVue360
      ) {
        setSelectedGuests([
          {
            id: contactInfoFromDrawer?.id,
            label: getName(contactInfoFromDrawer?.name, "name"),
            avatar:
              contactInfoFromDrawer?.nameImg ||
              getName(contactInfoFromDrawer?.name, "name")[0],
          },
          ...relations
            .map((el) => el.child)
            .flat()
            .map((guest) => ({
              id: guest.id,
              label: guest.label_data,
              avatar: guest.avatar,
            })),
        ]);
      } else if (
        !openView360InDrawer &&
        contactInfo.id &&
        // /^\/((contacts|companies|settings\/users)\/([^/]+)$|directory)/.test(
        //   pathname
        // ))
        (contactInfo.family_id === 4 ||
          contactInfo.family_id === 1 ||
          contactInfo.family_id === 2) &&
        fromVue360
      ) {
        setSelectedGuests([
          {
            id: contactInfo?.id,
            label: getName(contactInfo?.name, "name"),
            avatar:
              contactInfo?.nameImg ||
              (typeof getName(contactInfo?.name, "name") === "string" &&
                getName(contactInfo?.name, "name").length > 0 &&
                getName(contactInfo?.name, "name")[0]),
          },
          ...relations
            .map((el) => el.child)
            .flat()
            .map((guest) => ({
              id: guest.id,
              label: guest.label_data,
              avatar: guest.avatar,
            })),
        ]);
      }
    } catch (err) {
      console.log(err);
      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? err.message : err);
    } finally {
      setIsLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  //
  useEffect(() => {
    if (propsTitle !== "") {
      form.setFieldValue({
        title: propsTitle,
      });
    }
  }, [form, propsTitle]);
  //
  useEffect(() => {
    open && getParams();
  }, [getParams, open]);
  //
  const getTaskDetails = useCallback(async () => {
    if (!taskId) return;
    try {
      const {
        data: { data },
      } = await MainService.getSpecificTask(taskId);
      setSingleTaskData(data);
      setSelectedGuests(data?.guests || []);
      setSelectedFollowers(data?.followers || []);
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? err.message : err);
    }
  }, [t, taskId]);

  useEffect(() => {
    getTaskDetails();
  }, [getTaskDetails]);
  //
  const resetFields = () => {
    setSelectedTaskType(null);
    setGuestsSearchQuery("");
    setFollowersSearchQuery("");
    setSelectedGuests([]);
    setSelectedFollowers([]);
    setNoteAndDescription({ note: "", description: "" });
    setFiles([]);
    setSubmitType("");
    form.resetFields();
    form.setFieldsValue({});
    setSelectedStartTime("");
    setGuestsSearchQuery("");
    setFollowersSearchQuery("");
    formTopRef.current?.scrollIntoView({ behavior: "smooth" });
  };
  //
  const handleAfterSubmit = () => {
    resetFields();
    if (submitType === "create_&_add_another") {
      setLoadingSubmit(false);
      formTopRef.current?.scrollIntoView({ behavior: "smooth" });
      return;
    }
    if (submitType === "create") {
      setLoadingSubmit(false);
      formTopRef.current?.scrollIntoView({ behavior: "smooth" });
      dispatch({ type: "RMC_RESET_STATE_CREATE_TASK" });
      setOpen && setOpen(false);

      return;
    }
  };
  //

  const onFinish = async (values) => {
    // console.log({ values });
    try {
      setLoadingSubmit(true);
      const formData = new FormData();
      const allFields = form.getFieldsValue(true);

      formData.append("label", values?.title);
      formData.append(
        "priority",
        values?.priority ?? allFields?.priority ?? ""
      );
      formData.append(
        "Reminder",
        values?.reminder && values?.addonAfter
          ? `${values?.reminder} ${values?.addonAfter}`
          : ""
      );

      (values?.family || allFields?.family) &&
        formData.append("family_id", values?.family || allFields?.family);
      (values?.relatedElement || allFields?.relatedElement) &&
        formData.append(
          "element_id",
          values?.relatedElement || allFields?.relatedElement
        );
      const ownerId =
        typeof values?.owner === "object"
          ? values?.owner?.value
          : values?.owner;
      formData.append("send_email", emailNotification ? 1 : 0);

      formData.append("owner_id", ownerId);
      formData.append(
        "tasks_type_id",
        source === "visio" ? 3 : values?.taskType
      );
      formData.append("stage_id", values?.stage || "");
      formData.append(
        "start_date",
        values?.startDate
          ? dayjs(values?.startDate).format(user?.location?.date_format)
          : ""
      );
      formData.append(
        "start_time",
        values?.startTime
          ? dayjs(values?.startTime)
              .format(user?.location?.time_format)
              .toUpperCase()
          : ""
      );

      formData.append("reminder_before_end", values?.reminderEndDate ? 1 : 0);
      formData.append(
        "end_time",
        values?.endTime
          ? dayjs(values?.endTime)
              .format(user?.location?.time_format)
              .toUpperCase()
          : ""
      );
      formData.append(
        "end_date",
        values?.endDate
          ? dayjs(values?.endDate).format(user?.location?.date_format)
          : ""
      );
      formData.append(
        "description",
        noteAndDescription?.description ? noteAndDescription?.description : ""
      );
      formData.append("code", values?.code || "");
      if (values.exam_id) {
        values.exam_id.forEach((id) => {
          formData.append("exam_id[]", id);
        });
      }
      formData.append("department_id", values?.department_id);
      formData.append(
        "note",
        noteAndDescription?.note ? noteAndDescription?.note : ""
      );
      //
      values?.location && formData.append("location", values?.location);

      //
      selectedGuests?.length > 0 &&
        selectedGuests.forEach((el) =>
          formData.append(
            "guests[]",
            pathname === "/chat"
              ? el?.uuid
                ? `{ "uid": "${el.uuid}" }`
                : `{ "mid": "${el.id}" }`
              : el.id
          )
        );
      //
      selectedFollowers?.length > 0 &&
        selectedFollowers.forEach((el) =>
          formData.append("followers[]", el?.id)
        );
      //
      files.length > 0 &&
        files.forEach((file) => formData.append("upload[]", file?.id));

      if (idCall) {
        formData.append("source", 0);
        formData.append("type_id", idCall);
      } else if (source === "mailing" && mailingProps) {
        formData.append("source", 3);
        formData.append("type_id", mailingProps.idEmail);
      } else if (pathname === "/chat") {
        formData.append("source", 1);
      }

      if (taskId) {
        const { status, data } = await MainService.updateTask(taskId, formData);
        if (status === 200) {
          setShouldFetchData && setShouldFetchData(true);
          toastNotification("success", t("toasts.taskUpdated"), "bottomRight");
          handleAfterSubmit();
        }
      } else {
        const { status, data } = await MainService.createNewTask(formData);

        if (status === 200) {
          setShouldFetchData && setShouldFetchData(true);
          toastNotification("success", t("tasks.addActivity"), "bottomRight");
          if (listVisio) {
            dispatch(createVisio({ data: { ...data, key: data.id }, t }));
            // addTask(data.data);
          }
          if (window.location.pathname.includes("visio")) {
            //update kpi in visio
            let kpi = { data: [] };
            let kpiDate = { data: [] };
            const currentDate = moment().format(user.location.date_format);
            const start_date = moment(
              data.data.start_date,
              user.location.date_format
            );

            kpiDate = await MainService.getKpiDateVisio();
            kpi = await MainService.getKpiVisio(
              currentDate === data.data.start_date
                ? 1
                : start_date.isAfter(moment(), "day")
                ? 2
                : 0
            );

            dispatch(
              setKpiVisio(
                kpi &&
                  kpi.data && [
                    ...Object.entries(kpi.data).map(([key, value]) => {
                      return {
                        title: displayStatKey(t, key, ""),
                        value: value,
                        tr: false,
                      };
                    }),
                  ]
              )
            );
            dispatch(
              setKpiDateVisio(
                kpiDate &&
                  kpiDate.data &&
                  Object.entries(kpiDate.data).map(([key, value]) => {
                    return { title: key, value: value, tr: true };
                  })
              )
            );
          }
          if (source === "mailing") dispatch(setRefreshMailInbox(true));
          dispatch(setTask360(data.data));
          dispatch(setActiveActivity360("1"));
          setSelectedKey("3");
          handleAfterSubmit();
        }
      }
    } catch (err) {
      console.log("dccdc", err?.response?.data?.errors);
      err?.response?.data?.errors === "Localization error" &&
        toastNotification(
          "warning",
          <div className="flex w-full flex-col   pl-2 ">
            <p
              className=" mt-0.5 "
              dangerouslySetInnerHTML={{
                __html: t("tasks.configureLocalizationNotif"),
              }}
            ></p>{" "}
            <Button
              type="link"
              className=" ml-auto  w-full "
              onClick={() => navigate(`/profile/localization`)}
            >
              {t("common:tasks.GoToLocalization")}
              <RightCircleOutlined
                style={{
                  fontSize: "1rem",
                }}
              />
            </Button>
          </div>,
          "topRight",
          0,
          null,
          1,
          "Localization-error-notif",
          {
            padding: "20px  24px 20px 24px",
            margin: "10px 0px",
          }
        );
      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      setLoadingSubmit(false);
      formTopRef.current?.scrollIntoView({ behavior: "smooth" });
      dispatch({ type: "RMC_RESET_STATE_CREATE_TASK" });
      setOpen && setOpen(false);

      throw new Error(err?.message ? err.message : err);
    } finally {
      setLoadingSubmit(false);
      taskId && setTaskId(null);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  };
  //
  const drawerMenuProps = {
    items: [
      {
        label: t("contacts.createAndAddAnother"),
        key: "create_&_add_another",
      },
      {
        label: t("contacts.createGoDetail"),
        key: "create_&_go_to_detail",
        disabled: true,
      },
    ],
    onClick: (e) => {
      setSubmitType(e.key);
      form.submit();
    },
  };

  const DrawerProps = {
    // closeIcon: (
    //   <CloseOutlined
    //     style={{ fontSize: 16 }}
    //     onClick={() => {
    //       dispatch({ type: "RMC_RESET_STATE_CREATE_TASK" });
    //       setOpen && setOpen(false);
    //       taskId && setTaskId(null);
    //       resetFields();
    //     }}
    //   />
    // ),
    /////////
    footer: (
      <Space direction="horizontal" style={{ padding: "0.250rem" }}>
        <Button
          onClick={() => {
            resetFields();
            dispatch({ type: "RMC_RESET_STATE_CREATE_TASK" });
            setOpen && setOpen(false);
            taskId && setTaskId(null);
          }}
        >
          {t("contacts.cancel")}
        </Button>
        {source === "call" ? (
          <Button
            type="primary"
            // disabled={submitIsDisable}
            loading={loadingSubmit}
            onClick={() => {
              setSubmitType("create");
              form.submit();
            }}
          >
            {taskId ? t("voip.save") : t("contacts.create")}
          </Button>
        ) : (
          <Dropdown.Button
            type="primary"
            // disabled={submitIsDisable}
            loading={loadingSubmit}
            trigger={["click"]}
            placement="topLeft"
            icon={<UpOutlined />}
            menu={drawerMenuProps}
            onClick={() => {
              setSubmitType("create");
              form.submit();
            }}
          >
            {t("contacts.create")}
          </Dropdown.Button>
        )}
      </Space>
    ),
  };
  //
  // Fill task label  input on select task type function.
  const prefillTaskLabel = (value) => {
    let selectedType =
      tasksTypes &&
      tasksTypes.find((element) => Number(element?.id) === Number(value));
    let doesLabelExistIntypesList =
      tasksTypes &&
      tasksTypes.some(
        (element) => element?.label === form.getFieldValue("title")
      );
    if (
      form.getFieldValue("title") === "" ||
      typeof form.getFieldValue("title") === "undefined" ||
      doesLabelExistIntypesList
    ) {
      form.setFieldsValue({
        title: selectedType?.label,
      });
    }
  };

  /*   useEffect(() => {
    const handleKeyDown = (e) => {
      if (e?.key === "Enter") {
        form.submit();
      }
    };

    // Add keydown event listener
    window.addEventListener("keydown", handleKeyDown);

    // Clean up event listener on component unmount
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [form]); */

  return (
    <Drawer
      open={open}
      title={
        taskId
          ? t("voip.updateTask" /*, { label: titleLabel }*/)
          : source === "visio"
          ? t("visio.createNewVisio")
          : t("tasks.createTaskTitle")
      }
      width={windowSize?.width / 2.5 < 685 ? 685 : windowSize?.width / 2.5}
      // closeIcon={DrawerProps?.closeIcon}
      onClose={() => {
        dispatch({ type: "RMC_RESET_STATE_CREATE_TASK" });
        setOpen && setOpen(false);
        taskId && setTaskId(null);
        resetFields();
      }}
      footer={DrawerProps?.footer}
      mask={mask}
      zIndex={findNextZIndex()}
    >
      <div ref={formTopRef}></div>
      {isLoading ? (
        <Skeleton active />
      ) : (
        <CreateTaskForm
          fromVue360={fromVue360}
          form={form}
          onFinish={onFinish}
          guestsList={guests}
          ownersList={followers}
          pipelines={pipelines}
          ownersOptionsInSelect={followers
            ?.sort((a, b) => a?.label?.localeCompare(b?.label))
            .map((element) => ({
              label: (
                <>
                  <AvatarChat
                    fontSize="0.875rem"
                    className="mr-1.5"
                    size={28}
                    height={17}
                    width={17}
                    url={`${
                      URL_ENV?.REACT_APP_BASE_URL +
                      URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                    }${element?.avatar}`}
                    hasImage={EXTENSIONS_ARRAY?.includes(
                      element?.avatar?.split(".")?.pop()
                    )}
                    name={getName(element?.label, "avatar")}
                    type="user"
                  />
                  {getName(element?.label, "name")}
                </>
              ),
              value: element?.id,
            }))}
          guestsSearchQuery={guestsSearchQuery}
          setGuestsSearchQuery={setGuestsSearchQuery}
          setFollowersSearchQuery={setFollowersSearchQuery}
          followersSearchQuery={followersSearchQuery}
          setCheckedItems={setSelectedGuests}
          checkedItems={selectedGuests}
          setCheckedFollowers={setSelectedFollowers}
          checkedFollowers={selectedFollowers}
          setAddOnsValues={setNoteAndDescription}
          addOnsValues={noteAndDescription}
          setFiles={setFiles}
          files={files}
          setSelectedTaskType={setSelectedTaskType}
          selectedTaskType={selectedTaskType}
          loadSpecificTask={false}
          prefillTaskLabel={prefillTaskLabel}
          tasksTypes={activityType}
          selectedStartDate={selectedStartDate}
          setSelectedStartDate={setSelectedStartDate}
          setSelectedStartTime={setSelectedStartTime}
          selectedEndTime={selectedEndTime}
          setSelectedEndTime={setSelectedEndTime}
          selectedEndDate={selectedEndDate}
          selectedStartTime={selectedStartTime}
          setSelectedEndDate={setSelectedEndDate}
          moduleOptions={[]}
          loadModuleList={false}
          followersDropdownContent=""
          uncheckFollower={() => {}}
          source={taskId ? null : source}
          idType={() => {}}
          setIdType={() => {}}
          guestsListPage={guestsListPage}
          setGuestsListPage={setGuestsListPage}
          followersListPage={followersListPage}
          setFollowersListPage={setFollowersListPage}
          guestsListLastPage={guestsListLastPage}
          followersListLastPage={followersListLastPage}
          object={mailingProps}
          titleLabel={titleLabel ?? ""}
          setPropsTitle={setPropsTitle}
          singleTaskData={singleTaskData}
          setSelectedFamilyMembers={setSelectedFamilyMembers}
          setGuestsList={setGuests}
          loadGuests={loadGuests}
          loadOwners={loadOwners}
          setEmailNotification={setEmailNotification}
          isError={isError}
          setIsError={setIsError}
          totalEntities={totalEntities}
        />
      )}
    </Drawer>
  );
};

export default CreateTask;
