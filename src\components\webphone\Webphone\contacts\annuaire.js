import { useState, useEffect, useCallback, useRef, memo } from "react";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import {
  Divider,
  Skeleton,
  Spin,
  Empty,
  Space,
  Button,
  Tooltip,
  Typography,
  Popover,
  List,
  Dropdown,
  Badge,
} from "antd";
import {
  fetchContactsTypes,
  fetchPhoneBook,
} from "../../../../pages/voip/services/services";
import { toastNotification } from "../../../ToastNotification";
import { useTranslation } from "react-i18next";
import useActionCall from "../../../../pages/voip/helpers/ActionCall";
import { HighlightSearchW } from "../../../../pages/voip/components";
import { FiCopy, FiMoreVertical } from "react-icons/fi";
import InfiniteScroll from "react-infinite-scroll-component";
import {
  GlobalOutlined,
  InfoCircleOutlined,
  PhoneOutlined,
} from "@ant-design/icons";
import DisplayAvatar from "../../../../pages/voip/components/DisplayAvatar";
import { URL_ENV } from "index";
import "./index.css";
import DisplayModuleIconAndText from "pages/voip/components/DisplayModuleIconAndText";
import { generateUrlToView360 } from "pages/voip/helpers/helpersFunc";
import VirtualList from "rc-virtual-list";
import { useInView } from "react-intersection-observer";

const loaderTemplate = (len = 2, ref) => (
  <div className="ml-2 mt-2" ref={ref}>
    {Array.from({ length: len }, (_, index) => (
      <Skeleton
        size="small"
        key={index}
        avatar
        paragraph={{
          rows: 0,
        }}
        active
      />
    ))}
  </div>
);
//
const handleData = (data, dialCode, t) =>
  data?.map((item) => ({
    ...item,
    name: item.name?.replaceAll("_", " "),
    image: item?.image
      ? `${URL_ENV?.REACT_APP_BASE_URL + URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL}${
          item.image
        }`
      : null,
    family:
      item?.family_id === 1
        ? t("contacts.company")
        : item?.family_id === 2
        ? t("contacts.contact")
        : t("contacts.leads"),
    phones: item.phone?.map(([prefix, num]) => ({
      displayNum: `(${prefix}) ${num}`,
      callNum:
        dialCode === prefix ? num : `${prefix?.replace("+", "00")} ${num}`,
    })),
  })) || [];
//

const Annuaire = ({ search, handleActionWebPhone }) => {
  //
  const { ref, inView } = useInView();
  const [t] = useTranslation("common");
  const call = useActionCall();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const scrollableDivRef = useRef(null);
  const dialCode = useSelector(
    ({ user: { user } }) => user?.location?.dial_code
  );
  //
  const [shouldFetchData, setShouldFetchData] = useState(true);
  const [dataSource, setDataSource] = useState([]);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [totalData, setTotalData] = useState(0);
  const [page, setPage] = useState(1);
  const limit = 20;

  const [dataTypes, setDataTypes] = useState({});
  // const [expandedKeys, setExpandedKeys] = useState([]);
  const [popoverOpen, setPopoverOpen] = useState(null);
  const hasNextPage = totalData > dataSource.length;
  //
  // console.log({ dataSource });
  //
  useEffect(() => {
    let isMounted = true;
    const fetchDataTypes = async () => {
      try {
        const { data } = await fetchContactsTypes();
        // console.log({ data });
        if (isMounted) {
          const types = data.contact_types.reduce((acc, item) => {
            acc[item.label] = item.color;
            return acc;
          }, {});
          setDataTypes(types || {});
        }
      } catch (err) {
        err?.response?.status !== 401 &&
          toastNotification("error", t("toasts.somethingWrong"), "topRight");
        throw new Error(err?.message ? err.message : err);
      }
    };
    fetchDataTypes();
    return () => {
      isMounted = false;
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  //
  useEffect(() => {
    setShouldFetchData(true); // Set the trigger for fetching data
  }, [page, search]);

  useEffect(() => {
    setPage(1);
    setIsLoadingData(true);
    if (scrollableDivRef.current) {
     
    }
  }, [search]);
  //
  const fetchData = useCallback(async () => {
    if (!shouldFetchData) return;
    try {
      // setIsLoadingData(true);
      const modifiedSearch = /^\+?[0-9]+$/.test(search)
        ? search.replace("+", "00")
        : search;
      const {
        data: {
          data,
          meta: { total },
        },
      } = await fetchPhoneBook(
        null,
        page,
        limit,
        modifiedSearch,
        null,
        null,
        null,
        "contacts"
      );
      setTotalData(Number(total));
      const treatedData = handleData(data, dialCode, t);
      page === 1
        ? setDataSource(treatedData)
        : setDataSource((prevData) => [...prevData, ...treatedData]);
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? err.message : err);
    } finally {
      setIsLoadingData(false);
      setShouldFetchData(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shouldFetchData]);
  //
  useEffect(() => {
    fetchData();
  }, [fetchData]);
  //
  const loadMoreData = async () => {
    setPage((p) => p + 1);
  };
  //
  // close popover when scrolling
  useEffect(() => {
    if (!isLoadingData && hasNextPage && inView) {
      loadMoreData();
    }
  }, [inView, hasNextPage]);
  //
  return (
    <Spin spinning={isLoadingData}>
      <div
        ref={scrollableDivRef}
        id="scrollableDiv-webPhone-contacts"
        className="colleague-collapse  overflow-y-auto p-0 px-1 py-0.5"
      >
        {dataSource.length ? (
          <div className="voip-list px-0.5">
            <List
              itemLayout="vertical"
              locale={{
                emptyText: isLoadingData ? (
                  <div className=" flex h-64 flex-row items-center justify-center space-x-2 text-xl">
                    {t("contacts.loadingDataTable")}
                    <span className="animate-bounce">...</span>
                  </div>
                ) : (
                  ""
                ),
              }}
            >
              <VirtualList
                data={dataSource}
                height={288}
                itemHeight={60}
                itemKey={"id"}
                style={{ padding: "4px 0" }}
              >
                {(item, index) => (
                  <List.Item
                    style={{
                      alignItems: "center",
                      padding: "0px 4px 0px 4px",
                      border: "none",
                    }}
                    key={item?.id}
                  >
                    <div
                      style={{
                        padding: "0.25rem 0.25rem 0.25rem 0.25rem",
                      }}
                      className="flex  cursor-pointer flex-row items-center justify-between space-x-1 rounded-md hover:bg-slate-200"
                    >
                      <ConditionalPopover
                        item={item}
                        t={t}
                        call={call}
                        open={popoverOpen}
                        setOpen={setPopoverOpen}
                      >
                        <Tooltip
                          title={item?.phones?.length ? t("voip.call") : null}
                        >
                          <List.Item.Meta
                            onClick={() =>
                              item?.phones?.length === 1 &&
                              call(
                                item?.phones?.[0]?.callNum,
                                item?.id,
                                item?.family_id
                              )
                            }
                            avatar={
                              <DisplayAvatar
                                name={item.name}
                                urlImg={item.image}
                                size={38}
                              />
                            }
                            title={
                              <p className="truncate font-semibold">
                                {HighlightSearchW(item?.name, search)}
                              </p>
                            }
                            description={
                              <Space
                                size={0}
                                split={<Divider type="vertical" />}
                              >
                                <DisplayModuleIconAndText
                                  t={t}
                                  showText={true}
                                  hideTooltip={true}
                                  familyId={item?.family_id}
                                  textStyle={{ fontSize: 13 }}
                                  iconStyle={{ fontSize: 14 }}
                                />
                                {item?.type ? (
                                  <Badge
                                    color={dataTypes?.[item.type] || ""}
                                    dot
                                    text={
                                      <span
                                        className="truncate text-slate-500"
                                        style={{ fontSize: 13 }}
                                      >
                                        {item.type}
                                      </span>
                                    }
                                  />
                                ) : null}
                              </Space>
                            }
                          />
                        </Tooltip>
                      </ConditionalPopover>
                      <DropDownAction
                        item={item}
                        call={call}
                        t={t}
                        dispatch={dispatch}
                        navigate={navigate}
                        handleActionWebPhone={handleActionWebPhone}
                      />
                    </div>
                    {index < dataSource?.length - 1 && (
                      <Divider style={{ margin: "0.25rem" }} />
                    )}
                    {index == dataSource.length - 1 &&
                      hasNextPage &&
                      loaderTemplate(2, ref)}
                  </List.Item>
                )}
              </VirtualList>
            </List>
          </div>
        ) : !dataSource.length && isLoadingData ? (
          loaderTemplate(4)
        ) : (
          <div className="flex justify-center p-4 text-sm font-semibold">
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={"No data Found"}
            />
          </div>
        )}
      </div>
    </Spin>
  );
};
//
export const copyIcon = (text) => (
  <div className="pl-1">
    <Typography.Paragraph
      copyable={{
        text: text,
        icon: [
          <FiCopy
            style={{
              color: "rgb(22, 119, 255)",
              marginTop: 3,
              fontSize: 16,
            }}
          />,
        ],
      }}
    />
  </div>
);
//
const ConditionalPopover = memo(
  ({ children, item, call, t, open, setOpen }) => {
    //
    if (item?.phones?.length > 1) {
      const { id, name, phones, family_id } = item;
      const firstName = name ? name.split(" ")?.[0] : "";
      return (
        <Popover
          open={open === id}
          onOpenChange={(state) => (state ? setOpen(id) : setOpen(null))}
          title={t("voip.callOn", { name: firstName })}
          content={
            <div className="space-y-1">
              {phones?.map((phone, i) => (
                <div
                  key={i}
                  className="ml-1 flex flex-row justify-between space-x-1"
                >
                  {phone?.displayNum}
                  <Space size={3}>
                    <Button
                      type="link"
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        call(phone?.callNum, id, family_id);
                      }}
                      icon={
                        <PhoneOutlined rotate={100} style={{ fontSize: 16 }} />
                      }
                    />
                    {copyIcon(phone?.callNum)}
                  </Space>
                </div>
              ))}
            </div>
          }
          trigger="click"
          arrow={false}
          placement="bottom"
        >
          {children}
        </Popover>
      );
    } else return children;
  }
);
//
const DropDownAction = memo(
  ({ item, call, t, dispatch, navigate, handleActionWebPhone }) => {
    //
    const { id, name, email, phones, family_id } = item;
    //
    const menuDropdown = () => {
      const items = [];
      //
      const pushItem = (key, icon, label, onClick, disabled, children) => {
        items.push({
          key,
          icon,
          label,
          onClick,
          disabled,
          children,
        });
      };
      //
      if (phones.length) {
        if (phones.length === 1) {
          const { callNum, displayNum } = phones[0];
          pushItem(
            displayNum + id,
            <PhoneOutlined
              className="text-slate-500"
              rotate={100}
              style={{ fontSize: 14 }}
            />,
            // <div className="flex flex-row justify-between space-x-2">
            //   {`${t("voip.call")} ${displayNum}`}
            //   {copyIcon(displayNum)}
            // </div>,
            t("voip.call"),
            () => call(callNum, id, family_id)
          );
        } else
          pushItem(
            id,
            <PhoneOutlined rotate={100} start={{ fontSize: 14 }} />,
            t("voip.call"),
            null,
            null,
            phones.map(({ callNum, displayNum }) => ({
              key: displayNum,
              label: (
                <div className="flex flex-row justify-between space-x-2">
                  {displayNum}
                  {copyIcon(displayNum)}
                </div>
              ),
              onClick: () => call(callNum, id, family_id),
            }))
          );
      }
      if (id && family_id) {
        items.length && items.push({ type: "divider" });
        pushItem(
          "more-info",
          <InfoCircleOutlined style={{ fontSize: 14 }} />,
          // `${t("voip.moreInfoWith")} ${name?.split(" ")?.[0]}`,
          t("voip.moreInfo"),
          () =>
            handleActionWebPhone("display_info", {
              name: name,
              id: id,
              familyId: family_id,
            })
        );
        pushItem(
          "vue-360",
          <GlobalOutlined
            className="text-slate-500"
            style={{ fontSize: 14 }}
          />,
          // <p className="max-w-[10rem] truncate">
          //   {`${t("voip.view360")} ${name?.split(" ")?.[0]}`}
          // </p>,
          t("voip.view360"),
          () => navigate(generateUrlToView360(family_id, id, "v2"))
        );
      }

      return { items };
    };
    //
    return (
      <Dropdown
        trigger={["click"]}
        placement="bottomRight"
        menu={menuDropdown()}
        destroyPopupOnHide
      >
        <FiMoreVertical className="h-[18px] w-[18px] cursor-pointer text-gray-400 hover:text-gray-700" />
      </Dropdown>
    );
  }
);
// const collapseItems = useMemo(() => {
//   if (!dataSource?.length) return;
//   return (
//     dataSource.map((item) => ({
//       key: item.id,
//       label: <RenderLabelItem {...item} search={search} />,
//       children: <RenderChildItem {...item} call={call} t={t} />,
//       extra: (
//         <Tooltip title={item.phone?.[0]?.displayNum}>
//           <Button
//             onClick={() =>
//               call(item.phone?.[0]?.callNum, item?.id, item?.family_id)
//             }
//             // disabled={!item?.extension}
//             size="small"
//             type="link"
//             shape="circle"
//             icon={<PhoneOutlined rotate={100} style={{ fontSize: 18 }} />}
//           />
//         </Tooltip>
//       ),
//       showArrow: false,
//       style: {
//         background: "rgb(248 250 252)",
//         marginBottom: 0,
//         // borderBottomLeftRadius: 8,
//         // borderBottomRightRadius: 8,
//         padding: expandedKeys.includes(`${item.id}`) && "0 8px",
//       },
//     })) || []
//   );
//   // eslint-disable-next-line react-hooks/exhaustive-deps
// }, [dataSource, expandedKeys]);

// function RenderLabelItem({ name, image, family, type, search }) {
//   //
//   const RenderAvatar = () => (
//     <DisplayAvatar urlImg={image} name={name} size={38} />
//   );

//   //
//   return (
//     <div className="flex flex-row space-x-2">
//       <RenderAvatar />
//       <div className="w-[12rem]">
//         <p className="truncate font-semibold leading-5">
//           {HighlightSearchW(name, search)}
//         </p>
//         <Space size={3} split={<Divider type="vertical" />}>
//           <p className={"leading-4 text-slate-500"}>{family}</p>
//           {type ? <p className={"leading-4 text-slate-500"}>{type}</p> : null}
//         </Space>
//       </div>
//     </div>
//   );
// }

// function RenderChildItem({ id, email, phone, call, t, family }) {
//   //
//   const copyIcon = (text) => (
//     <Typography.Paragraph
//       copyable={{
//         text: text,
//         icon: [
//           <FiCopy
//             style={{
//               color: "rgb(22, 119, 255)",
//               // marginTop: 2,
//               fontSize: 15,
//             }}
//           />,
//         ],
//       }}
//     />
//   );
//   //

//   const mobileContents = phone?.length ? (
//     <div className="flex flex-col space-y-2">
//       {phone.map(({ displayNum, callNum }, i) => (
//         <div className="flex flex-row justify-between space-x-2" key={i}>
//           <Tooltip title={t("voip.call")}>
//             <Typography.Link onClick={() => call(callNum, id, family)}>
//               {displayNum}
//             </Typography.Link>
//           </Tooltip>

//           {copyIcon(callNum)}
//         </div>
//       ))}
//     </div>
//   ) : (
//     ""
//   );

//   return (
//     <div id={`panel-${id}`} key={id} className="flex justify-center">
//       <Space size={10} split={<Divider type="vertical" />}>
//         <Tooltip title="Visio">
//           <Button
//             type="link"
//             size="small"
//             shape="circle"
//             icon={<VideoCameraOutlined style={{ fontSize: 16 }} />}
//             onClick={() => message.warning("This Feature is not ready yet!")}
//           />
//         </Tooltip>
//         {/* <Tooltip title={phones?.length ? "Mobile" : ""}> */}
//         <Popover placement="right" content={mobileContents}>
//           <Button
//             type="link"
//             size="small"
//             shape="circle"
//             icon={<MobileOutlined style={{ fontSize: 16 }} />}
//             disabled={!phone?.length}
//           />
//         </Popover>
//         {/* </Tooltip> */}
//         <Tooltip title={!email ? "" : "Send Mail"}>
//           <Button
//             type="link"
//             size="small"
//             shape="circle"
//             icon={<MailOutlined style={{ fontSize: 16 }} />}
//             disabled={!email}
//             onClick={() => message.warning("This Feature is not ready yet!")}
//           />
//         </Tooltip>
//         <Tooltip title={"View 360"}>
//           <Button
//             type="link"
//             size="small"
//             shape="circle"
//             icon={<GlobalOutlined style={{ fontSize: 16 }} />}
//             // disabled={!email}
//             onClick={() => message.warning("This Feature is not ready yet!")}
//           />
//         </Tooltip>
//         {/* <Tooltip title={!isOpenDrawerChat || uuid ? "Chat" : ""}>
//           <Button
//             type="link"
//             size="small"
//             shape="circle"
//             icon={<MessageOutlined style={{ fontSize: 16 }} />}
//             disabled={isOpenDrawerChat || !uuid}
//             onClick={() => handleOpenMgsDrawer(uuid)}
//           />
//         </Tooltip> */}
//         <Tooltip title="History">
//           <Button
//             type="link"
//             size="small"
//             shape="circle"
//             icon={<HistoryOutlined style={{ fontSize: 16 }} />}
//             onClick={() => message.warning("This Feature is not ready yet!")}
//           />
//         </Tooltip>
//       </Space>
//     </div>
//   );
// }

export default Annuaire;
