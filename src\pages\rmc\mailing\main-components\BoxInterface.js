import {
  lazy,
  memo,
  Suspense,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import { useLocation } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { Button, Input, Space } from "antd";
import { FiSearch } from "react-icons/fi";
import { LuRefreshCcw } from "react-icons/lu";
import { toastNotification } from "components/ToastNotification";
import { useMailColumns } from "./columnsTable";
import { debounce } from "lodash";
import { ActionsMailing } from "./ActionsMailing";
import {
  getDataUsersToAssign,
  handleArchiveEmail,
  handleDeleteEmail,
  handleReadAndUnreadEmails,
  makeAsLabel2,
} from "../services/ActionsApi";
import {
  handleDataTable,
  handleFormData,
  MailPagination,
  openMailingRoomChat,
  scrollToTop,
  SelectRows,
} from "./utils";
import TableView from "./TableView";
import MainService from "services/main.service";
import PopoverNotification from "../popoverNotification";
import FilterMail from "./FilterMail";
import KpiMailing from "./KpiMailing";

//
const DisplayElementInfo = lazy(
  () => import("pages/voip/components/DisplayElementInfo"),
  "DisplayElementInfo"
);
const ModalMoveMail = lazy(() => import("./ModalMoveMail"), "ModalMoveMail");
const ModalAffectation = lazy(
  () => import("./ModalAffectation"),
  "ModalAffectation"
);
const ModalIdentification = lazy(
  () => import("./ModalIdentification"),
  "ModalIdentification"
);
const ModalQualification = lazy(
  () => import("./ModalQualification"),
  "ModalQualification"
);
const LogDrawer = lazy(() => import("../components/Log"), "LogDrawer");
const FormCreate = lazy(
  () => import("pages/clients&users/components/FormCreate"),
  "FormCreate"
);
const TasksRoom = lazy(() => import("pages/tasks/tasksRoom"), "TasksRoom");

const BoxInterface = ({ path, usedAccount, label }) => {
  //
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const location = useLocation();
  const locationStateRef = useRef(location.state);
  //
  const user = useSelector((state) => state.user.user);
  const refreshMailInbox = useSelector(
    (state) => state.mailReducer.refreshMailInbox
  );
  //
  const isSharedAccount = !!Number(usedAccount.shared);
  //
  const [shouldFetchData, setShouldFetchData] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [isLoadingData, setIsLoadingData] = useState(false);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(20);
  const [search, setSearch] = useState("");
  const [filter, setFilter] = useState({});
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [expandedRows, setExpandedRows] = useState([]);
  const [openNotifPop, setOpenNotifPop] = useState(false);
  //
  const [openElementInfo, setOpenElementInfo] = useState(false);
  const [elementDetails, setElementDetails] = useState({});
  const [openLogDrawer, setOpenLogDrawer] = useState(false);
  const [openFormCreate, setOpenFormCreate] = useState(false);
  const [formCreateProps, setFormCreateProps] = useState({});
  const [openModalMove, setOpenModalMove] = useState({});
  const [openModalAffectation, setOpenModalAffectation] = useState({});
  const [openModalIdentification, setOpenModalIdentification] = useState({});
  const [openModalQualification, setOpenModalQualification] = useState({});
  const [openRoomChat, setOpenRoomChat] = useState({});
  //
  const [sharedAccountUsers, setSharedAccountUsers] = useState([]);
  //
  // console.log({ selectedRowKeys });
  //
  // reset states (page, limit ..) when user navigate from label to other label
  useEffect(() => {
    setPage(locationStateRef.current?.page ?? 1);
    setLimit(locationStateRef.current?.limit ?? 20);
    setSearch(locationStateRef.current?.search ?? "");
    setFilter(locationStateRef.current?.filter ?? {});
    setSelectedRowKeys(locationStateRef.current?.selectedRowKeys ?? []);
  }, [path]);
  //
  // trigger to fetch data
  useEffect(() => {
    setShouldFetchData(true);
    scrollToTop();
  }, [search, filter, page, limit, refreshMailInbox]);
  //
  const getDataTable = useCallback(async () => {
    if (!shouldFetchData) return;
    try {
      setIsLoadingData(true);
      !locationStateRef.current?.selectedRowKeys && setSelectedRowKeys([]);
      const formData = new FormData();
      formData.append("folder", label?.id ? "label" : path?.split("/")?.pop());
      label?.id && formData.append("labelId", label.id);
      formData.append("account_id", usedAccount?.value);
      !!Object.keys(filter)?.length && handleFormData(filter, formData);
      const {
        data: {
          data,
          meta: { total },
        },
      } = await MainService.getInboxEmails(formData, page, limit, search);
      setDataSource(handleDataTable(data, !!search.length));
      setTotal(total);
      if (search) {
        const expandable = [];
        data.forEach((item) => {
          item.highlight && expandable.push(item.id);
        });
        setExpandedRows(expandable);
      } else if (expandedRows.length) setExpandedRows([]);
      //
      if (locationStateRef.current?.scrollY) {
        const scrollY = locationStateRef.current?.scrollY;
        const timer = setTimeout(() => {
          scrollToTop(scrollY);
          return () => clearTimeout(timer);
        }, 500);
      }
    } catch (err) {
      toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? `error: ${err.message}` : { err });
    } finally {
      setIsLoadingData(false);
      setShouldFetchData(false);
      locationStateRef.current = null;
      window.history.replaceState(null, "", window.location.href);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shouldFetchData]);

  useEffect(() => {
    getDataTable();
  }, [getDataTable]);
  //
  const getSharedAccountUsers = useCallback(async () => {
    try {
      if (!isSharedAccount) return;
      const data = await getDataUsersToAssign(usedAccount, t);
      setSharedAccountUsers(data);
    } catch (err) {
      toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? `error: ${err.message}` : { err });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isSharedAccount]);

  useEffect(() => {
    getSharedAccountUsers();
  }, [getSharedAccountUsers]);
  //
  const handleDropDown = ({ action, mailId, thirdId, record }) => {
    const accountId = usedAccount.value;
    if (action.includes("labels")) {
      const idLabel = action.split("--")[1];
      makeAsLabel2(accountId, idLabel, thirdId, setShouldFetchData, t);
    } else if (action.includes("convert")) {
      const family = action.split("-")[1];
      setFormCreateProps({ id: mailId, familyId: family });
      setOpenFormCreate(true);
    } else
      switch (action) {
        case "historic":
          setOpenLogDrawer({ open: true, thirdId });
          break;
        case "move":
          setOpenModalMove({
            open: true,
            record: record,
          });
          break;
        case "affect":
          setOpenModalAffectation({
            open: true,
            record: record,
          });
          break;
        case "identify":
          setOpenModalIdentification({
            open: true,
            record: record,
          });
          break;
        case "qualify":
          setOpenModalQualification({
            open: true,
            record: record,
          });
          break;
        case "chat":
          openMailingRoomChat({
            user,
            thirdId,
            dispatch,
            usedAccount,
            setOpenRoomChat,
          });
          setDataSource((prev) =>
            prev.map((item) =>
              item.third_id === thirdId
                ? {
                    ...item,
                    room_id: true,
                  }
                : item
            )
          );
          break;
        case "delete":
          handleDeleteEmail(
            [record.key],
            usedAccount.value,
            null,
            setShouldFetchData,
            t
          );
          break;
        case "archive":
        case "unarchive":
          handleArchiveEmail(
            [thirdId],
            usedAccount.value,
            action === "archive" ? 1 : 0,
            null,
            setShouldFetchData,
            t
          );
          break;
        case "makeUnread":
        case "makeRead":
          handleReadAndUnreadEmails(
            [record.key],
            usedAccount.value,
            action === "makeRead" ? 1 : 0,
            setDataSource,
            t
          );
          break;
        default:
          break;
      }
  };
  //
  const tableColumns = useMailColumns({
    setData: setDataSource,
    usedAccount,
    label,
    user,
    sharedAccountUsers,
    setOpenElementInfo,
    setElementDetails,
    handleDropDown,
    search,
  });
  //
  //
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedSearch = useCallback(
    debounce((nextValue) => {
      setPage(1);
      setSearch(nextValue.trim());
    }, 500),
    []
  );
  //
  const trackTableParams = (scrollY) => {
    return {
      scrollY,
      page,
      limit,
      search,
      filter,
      path,
      selectedRowKeys,
    };
  };
  //
  return (
    <div className="relative w-full space-y-3 py-4">
      <div className="flex justify-between px-3 ">
        <Space.Compact>
          <Input
            allowClear
            style={{ width: "18rem" }}
            placeholder={t("mailing.search") + "..."}
            prefix={<FiSearch className="text-slate-600" />}
            defaultValue={search}
            onChange={(e) => debouncedSearch(e.target.value)}
          />
          <FilterMail
            filter={filter}
            setFilter={setFilter}
            usedAccount={usedAccount}
            isSharedAccount={isSharedAccount}
          />
        </Space.Compact>

        {
          /*!Object.keys(filter)?.length &&*/
          isSharedAccount && path.split("/").pop() === "inbox" && (
            <KpiMailing
              filter={filter}
              setFilter={setFilter}
              usedAccount={usedAccount}
            />
          )
        }
        <div
          className={`flex items-center justify-end space-x-2 ${
            path.split("/").pop() === "inbox" ? "" : "hidden"
          }`}
        >
          <PopoverNotification
            popoverVisible={openNotifPop}
            setPopoverVisible={setOpenNotifPop}
            usedAccount={usedAccount}
          />
        </div>
      </div>
      <div className="flex justify-between px-3 ">
        <div className="flex items-center space-x-2">
          {!!dataSource.length && (
            <SelectRows
              selectedRowKeys={selectedRowKeys}
              setSelectedRowKeys={setSelectedRowKeys}
              dataSource={dataSource}
              // disabled={!dataSource.length}
              usedAccount={usedAccount}
              user={user}
            />
          )}

          {!selectedRowKeys.length || shouldFetchData ? (
            <Button
              type="text"
              shape="circle"
              loading={shouldFetchData}
              onClick={() => {
                setShouldFetchData(true);
                scrollToTop();
              }}
              icon={<LuRefreshCcw className="h-4 w-4 text-slate-600" />}
            />
          ) : (
            <ActionsMailing
              t={t}
              dataSource={dataSource}
              setDataSource={setDataSource}
              usedAccount={usedAccount}
              selectedRowKeys={selectedRowKeys}
              setSelectedRowKeys={setSelectedRowKeys}
              setShouldFetchData={setShouldFetchData}
              isSharedAccount={isSharedAccount}
            />
          )}
          {Object.keys(filter)?.length > 0 && (
            <Button
              size="small"
              type="link"
              danger
              onClick={() => setFilter({})}
            >
              {t("mailing.filterReset")}
            </Button>
          )}
        </div>
        <MailPagination
          total={total}
          page={page}
          setPage={setPage}
          limit={limit}
          setLimit={setLimit}
        />
      </div>

      <TableView
        columns={tableColumns}
        dataSource={dataSource}
        selectedRowKeys={selectedRowKeys}
        setSelectedRowKeys={setSelectedRowKeys}
        loading={isLoadingData}
        usedAccount={usedAccount}
        user={user}
        labelId={label?.id}
        expandedRows={expandedRows}
        trackTableParams={trackTableParams}
      />

      <Suspense fallback={<></>}>
        {openElementInfo && (
          <DisplayElementInfo
            open={openElementInfo}
            setOpen={setOpenElementInfo}
            elementDetails={elementDetails}
          />
        )}

        {!!openLogDrawer?.open && (
          <LogDrawer
            openLogDrawer={openLogDrawer?.open || null}
            setOpenLogDrawer={setOpenLogDrawer}
            thirdid={openLogDrawer?.thirdId}
          />
        )}

        {openFormCreate && (
          <FormCreate
            open={openFormCreate}
            setOpen={setOpenFormCreate}
            familyId={formCreateProps?.familyId}
            externalSource={{
              source: "email",
              usedAccount: usedAccount,
              ...formCreateProps,
            }}
          />
        )}

        {!!openModalMove?.open && (
          <ModalMoveMail
            open={openModalMove?.open}
            setOpen={setOpenModalMove}
            record={openModalMove?.record}
            usedAccount={usedAccount}
            setDataSource={setDataSource}
          />
        )}

        {!!openModalAffectation?.open && (
          <ModalAffectation
            open={openModalAffectation?.open}
            setOpen={setOpenModalAffectation}
            record={openModalAffectation?.record}
            usedAccount={usedAccount}
            setDataSource={setDataSource}
          />
        )}
        {!!openModalIdentification?.open && (
          <ModalIdentification
            open={openModalIdentification?.open}
            setOpen={setOpenModalIdentification}
            record={openModalIdentification?.record}
            usedAccount={usedAccount}
            setDataSource={setDataSource}
          />
        )}
        {!!openModalQualification?.open && (
          <ModalQualification
            open={openModalQualification?.open}
            setOpen={setOpenModalQualification}
            record={openModalQualification?.record}
            usedAccount={usedAccount}
            setDataSource={setDataSource}
          />
        )}
        {!!openRoomChat?.open && (
          <TasksRoom
            elementId={openRoomChat?.elementId}
            formData={openRoomChat?.formData}
            setOpen={setOpenRoomChat}
          />
        )}
      </Suspense>
    </div>
  );
};
//
//
export default memo(BoxInterface);
