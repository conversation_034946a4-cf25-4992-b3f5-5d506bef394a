import React from "react";
import { Input, Space, message } from "antd";
import { useDispatch } from "react-redux";
import {
  resetSearchFile360,
  searchFile360,
} from "../../../../../new-redux/actions/files.actions/files";
import { useTranslation } from "react-i18next";

const { Search } = Input;

function SearchFile() {
  const dispatch = useDispatch();
  const [t] = useTranslation("common");

  const onSearch = (value, _e, info) => {
   // console.log("value", value);
   // console.log("info", info);
   // console.log("e", _e);
    if (value) {
      dispatch(searchFile360(value));
    } else {
      if (info?.source == "clear") {
        dispatch(resetSearchFile360());
      } else {
        dispatch(resetSearchFile360());
      }
    }
  };
  return (
    <>
      <Search
        placeholder={t("files360.uploadSearch")}
        onSearch={onSearch}
        onChange={(e) => {
          if (e.target.value == "") {
            console.log("clear");
            dispatch(resetSearchFile360());
          } else {
            dispatch(searchFile360(e.target.value));
          }
        }}
        allowClear
        style={{
          width: "100%",
          marginTop: "1rem",
          padding: "0.5rem",
        }}
      />
    </>
  );
}

export default SearchFile;
