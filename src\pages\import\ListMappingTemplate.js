import { memo, useState } from "react";
import {
  CheckCircleFilled,
  DownOutlined,
  ExclamationCircleFilled,
  LoadingOutlined,
  PlusOutlined,
  RightOutlined,
  WarningOutlined,
} from "@ant-design/icons";
import { Button, Col, Form, Row, Select, Tooltip } from "antd";
import { useDispatch } from "react-redux";
import { debounce } from "lodash";

import { setOpenChildrenImportDrawer } from "new-redux/actions/form.actions/form";
import MainService from "services/main.service";

// custom component for adding custom types
const GetAddButtonToDrawer = ({ label, isModule }) => {
  const dispatch = useDispatch();
  return isModule !== null ? (
    <Col span={1} style={{ marginLeft: 10, marginTop: 3 }}>
      <Button
        type="primary"
        shape="circle"
        style={{ width: 10, heigh: 10 }}
        onClick={() =>
          dispatch(
            setOpenChildrenImportDrawer({
              open: true,
              type: isModule,
              title: label,
            })
          )
        }
        size="small"
        icon={<PlusOutlined />}
      />
    </Col>
  ) : (
    <></>
  );
};

const GetListMappingTemplate = memo(function GetListMappingTemplate({
  keyItem,
  csv,
  listValues,
  csvIndex,
  rowIndex,
  changedListValues,
  saveMapping,
  setChangedListValues,
  restoredListMapping,
  t,
}) {
  const [autocompleteOptionsMap, setAutocompleteOptionsMap] = useState({});
  const [autocompleteLoader, setAutocompleteLoader] = useState(false);

  const key = keyItem;
  let array = changedListValues;
  let formKey = `${csvIndex}-${keyItem}`;
  let formItemName = `${csv}-${csvIndex}-${keyItem}-id-${listValues[key]?.id}`;

  const getAutocompleteOptions = async (query, key, familyID) => {
    try {
      setAutocompleteLoader(true);
      const response = await MainService.getListMappingOptions(query, familyID);
      setAutocompleteOptionsMap((prevMap) => ({
        ...prevMap,
        [key]: response?.data?.message.map((el) => ({
          label: el?.label,
          value: el?.id,
        })),
      }));
      setAutocompleteLoader(false);
    } catch (error) {
      console.log(`Error ${error}`);
      setAutocompleteLoader(false);
    }
  };

  const debounceGetAutocompleteOptions = debounce((searchQuery, key) => {
    getAutocompleteOptions(searchQuery, key, listValues[keyItem]?.family);
  }, 100);

  const handleSelect = (value) => {
    let idx = array.findIndex((item) => item?.index === rowIndex);
    if (idx > -1) {
      saveMapping(
        false,
        array.map((item, i) => (i === idx ? { ...item, value: value?.value } : item))
      );
      setChangedListValues(
        array.map((item, i) => (i === idx ? { ...item, value: value?.value } : item))
      );
    } else {
      let newArray = [...array, { name: formItemName, index: rowIndex, value: value?.value }];
      saveMapping(false, newArray);
      setChangedListValues([
        ...changedListValues,
        { name: formItemName, index: rowIndex, value: value?.value },
      ]);
    }
  };

  const dispatchDefaultListValues = (restoreDataObject, index) => {
    let id = listValues[key]?.id;
    let itemById = restoreDataObject[id];
    let itemByIndex = itemById[index];
    if (typeof itemByIndex === "object" && itemByIndex !== null) {
      return { value: itemByIndex?.id, label: itemByIndex?.label };
    } else return itemByIndex;
  };

  const processListValues = () => {
    if (restoredListMapping === null) {
      if (listValues[keyItem]?.family === "") {
        if (listValues[key]?.auto !== null && csv.trim() !== "") {
          return listValues[key]?.auto[csv]?.value;
        }
      } else {
        if (csv.trim() !== "") {
          return {
            value: listValues[key]?.auto[csv]?.id,
            label: listValues[key]?.auto[csv]?.value,
          };
        }
      }
    }
  };

  return (
    <div className="my-6">
      <Row key={csvIndex}>
        <Col span={10}>
          <Form.Item>
            <div
              style={{
                textOverflow: "ellipsis",
                overflow: "hidden",
                whiteSpace: "nowrap",
              }}
            >
              {csv ? (
                csv
              ) : (
                <span className="italic text-red-500">
                  {t("import.empty")}
                  <WarningOutlined style={{ marginLeft: "5px" }} />
                </span>
              )}
            </div>
          </Form.Item>
        </Col>
        <Col span={2}>
          <Form.Item>
            <RightOutlined />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Row>
            <Col span={11} key={formKey}>
              <Form.Item
                key={formItemName}
                name={formItemName}
                shouldUpdate={(prevValues, curValues) =>
                  prevValues?.formItemName !== curValues?.formItemName
                }
                initialValue={
                  restoredListMapping === null
                    ? processListValues()
                    : dispatchDefaultListValues(restoredListMapping, csvIndex)
                }
              >
                <Select
                  options={
                    listValues[keyItem]?.family === ""
                      ? listValues[key]?.list?.map((e, index) => ({
                          value: e,
                          label: e,
                          key: index,
                        }))
                      : autocompleteOptionsMap[formItemName] || []
                  }
                  placeholder={
                    listValues[keyItem]?.family === ""
                      ? t("import.selectMappingList")
                      : t("import.selectMappingListFamily")
                  }
                  onSearch={(text) => {
                    if (listValues[keyItem]?.family !== "") {
                      debounceGetAutocompleteOptions(text, formItemName);
                    }
                  }}
                  notFoundContent={
                    listValues[keyItem]?.family !== "" && autocompleteLoader ? (
                      <LoadingOutlined
                        style={{
                          fontSize: 16,
                          display: "flex",
                          justifyContent: "center",
                        }}
                        spin
                      />
                    ) : null
                  }
                  popupMatchSelectWidth={false}
                  suffixIcon={listValues[keyItem]?.family !== "" ? null : <DownOutlined />}
                  style={{ width: "100%" }}
                  onSelect={(value, option) => handleSelect(option)}
                  onClear={() => {
                    console.log("clear");
                    saveMapping();
                  }}
                  showSearch
                  allowClear
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    (option?.label.toLowerCase() ?? "").includes(input.toLowerCase())
                  }
                  filterSort={(optionA, optionB) =>
                    (optionA?.label ?? "")
                      .toLowerCase()
                      .localeCompare((optionB?.label ?? "").toLowerCase())
                  }
                />
              </Form.Item>
            </Col>
            <Col style={{ marginLeft: "10px", alignSelf: "baseline" }}>
              {restoredListMapping === null &&
                listValues[key]?.auto !== null &&
                (Number(listValues[key]?.auto[csv]?.rating_match) >= 0.6 ? (
                  <Tooltip title={t("import.accurateAutoMap")}>
                    <CheckCircleFilled style={{ color: "#73d13d" }} />
                  </Tooltip>
                ) : (
                  <Tooltip title={t("import.autoMapToVerify")}>
                    <ExclamationCircleFilled style={{ color: "#ff4d4f" }} />
                  </Tooltip>
                ))}
            </Col>
            <GetAddButtonToDrawer
              isModule={listValues[key]?.route}
              label={listValues[key]?.label}
            />
          </Row>
        </Col>
      </Row>
    </div>
  );
});

export default GetListMappingTemplate;
