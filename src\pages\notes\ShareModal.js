import React, { useEffect, useState, useCallback } from "react";
import {
  Modal,
  List,
  Avatar,
  Spin,
  Input,
  Checkbox,
  Button,
  Tooltip,
  Skeleton,
} from "antd";
import { useTranslation } from "react-i18next";
import MainService from "services/main.service";
import VirtualList from "rc-virtual-list";
import debounce from "lodash.debounce";
import { AvatarChat } from "components/Chat";
import { URL_ENV } from "index";
import { getName } from "pages/layouts/chat/utils/ConversationUtils";
import "./notes.css";
import { CloseCircleFilled } from "@ant-design/icons";
import { useSelector } from "react-redux";

function ShareModal({ open, setOpen, shareLoading, item, shareFunction }) {
  const { t } = useTranslation("common");

  const [page, setPage] = useState(1);
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");

  const [selectedCollegues, setSelectedCollegues] = useState([]);

  const currentUser = useSelector((state) => state?.chat?.currentUser);

  const share = () => {
    let sharedWith = selectedCollegues
      ? selectedCollegues?.map((collegue) => collegue.uuid)
      : [];

    shareFunction(item._id, {
      shared_with: sharedWith,
    });
  };

  useEffect(() => {
    //set the list of uuids of the users already shared with the note
    setSelectedCollegues(
      item?.shared_with_users_data?.map((sharedUser) => sharedUser)
    );
  }, [item]);

  //toggle add or remove the user from the shared list
  const toggleUser = (user) => {
    // if (selectedCollegues.includes(user.uuid)) {
    //   setSelectedCollegues(
    //     selectedCollegues.filter((sharedUser) => sharedUser !== user.uuid)
    //   );
    // }
    // //add the user to the shared list
    // else {
    //   setSelectedCollegues([...selectedCollegues, user.uuid]);
    // }
    if (
      selectedCollegues?.find((sharedUser) => sharedUser.uuid === user.uuid)
    ) {
      setSelectedCollegues(
        selectedCollegues?.filter((sharedUser) => sharedUser.uuid !== user.uuid)
      );
    } else {
      if (selectedCollegues?.length > 0) {
        setSelectedCollegues([
          ...selectedCollegues,
          {
            uuid: user.uuid,
            id: user.id,
            label_data: user.label,
            avatar: user.avatar,
          },
        ]);
      } else {
        setSelectedCollegues([
          {
            uuid: user.uuid,
            id: user.id,
            label_data: user.label,
            avatar: user.avatar,
          },
        ]);
      }
      // setSelectedCollegues([
      //   ...selectedCollegues,
      //   {
      //     uuid: user.uuid,
      //     id: user.id,
      //     label_data: user.label,
      //     avatar: user.avatar,
      //   },
      // ]);
    }
  };

  //console.log("note item", item);

  // Debounced function to handle search input changes
  const handleSearch = debounce((query) => {
    setSearchQuery(query);
    setPage(1); // Reset to first page for new search
    setUsers([]); // Clear users for new search
    fetchUsers(1, query);
  }, 300);

  const fetchUsers = async (pageNum = 1, search = "") => {
    setLoading(true);
    let formData = new FormData();
    formData.append("family_id", 4);
    if (search) formData.append("search", search);

    try {
      const res = await MainService.getFamilyOptions(pageNum, 20, formData);
      const newUsers = res?.data?.data || [];
      setUsers((prevUsers) =>
        pageNum === 1 ? newUsers : [...prevUsers, ...newUsers]
      );
      setHasMore(res?.data?.links?.next !== null);
    } catch (err) {
      console.error("Error fetching users:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (open) {
      setUsers([]); // Reset users on modal open
      setPage(1); // Reset page on modal open
      fetchUsers(1); // Fetch the first page
    }
  }, [open]);

  const handleScroll = useCallback(
    debounce((e) => {
      const { scrollHeight, scrollTop, clientHeight } = e.target;
      if (
        scrollHeight - scrollTop <= clientHeight + 20 &&
        hasMore &&
        !loading
      ) {
        setPage((prevPage) => prevPage + 1);
      }
    }, 300),
    [hasMore, loading]
  );

  useEffect(() => {
    if (page > 1) {
      fetchUsers(page, searchQuery);
    }
  }, [page]);

  return (
    <Modal
      title={t("selfNotes.shareTitle")}
      visible={open}
      onCancel={() => setOpen(false)}
      footer={null}
      width={700}
    >
      {
        <Avatar.Group
          //   maxCount={7}
          className="mb-0.5 ml-4 flex space-x-1 overflow-x-auto py-5"
        >
          {selectedCollegues &&
            selectedCollegues?.map((collegue) => (
              <Tooltip
                title={collegue.label_data}
                key={collegue.uuid}
                placement="bottom"
              >
                <div
                  style={{
                    position: "relative",
                    cursor: "pointer",
                  }}
                >
                  <CloseCircleFilled
                    style={{
                      position: "absolute",
                      right: 0,
                      top: "-10px",
                      cursor: "pointer",
                      zIndex: 20,
                      color: "red",
                      fontSize: "15px",
                    }}
                    onClick={() =>
                      setSelectedCollegues(
                        selectedCollegues?.filter(
                          (selectedCollegue) =>
                            selectedCollegue.uuid !== collegue.uuid
                        )
                      )
                    }
                  />
                  {/* <NoteAvatar
                  avatar={collegue.image}
                  name={collegue.name}
                  size={38}
                /> */}
                  <AvatarChat
                    url={
                      URL_ENV?.REACT_APP_BASE_URL +
                      URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                      collegue?.avatar
                    }
                    type="user"
                    size={38}
                    height={10}
                    width={10}
                    name={getName(collegue?.label_data, "avatar")}
                    hasImage={collegue?.avatar}
                  />
                </div>
              </Tooltip>
            ))}
        </Avatar.Group>
      }
      <Input.Search
        placeholder={t("selfNotes.searchForColleaguesToShare")}
        onChange={(e) => handleSearch(e.target.value)}
        style={{ marginBottom: 16 }}
        allowClear
      />
      <VirtualList
        data={users.filter((user) => currentUser?.uuid !== user.uuid)}
        height={300}
        itemHeight={47}
        itemKey="id"
        onScroll={handleScroll}
      >
        {(user) => (
          <List.Item
            key={user.id}
            style={{
              padding: "4px 10px",
              borderBottom: "1px solid #f0f0f0",
              cursor: "pointer",
            }}
            className="flex items-center justify-between "
            onClick={() => toggleUser(user)}
          >
            <List.Item.Meta
              title={
                <div className="flex items-center px-2">
                  <AvatarChat
                    url={`${URL_ENV?.REACT_APP_BASE_URL}${URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL}${user.avatar}`}
                    type="user"
                    size={38}
                    height={10}
                    width={10}
                    name={getName(user.label, "avatar")}
                    hasImage={user.avatar?.length > 5 ? true : false}
                  />
                  <div className="ml-2 flex-col">
                    <div>{user.label}</div>
                    <div className="text-muted text-xs text-gray-500">
                      {user.email}
                    </div>
                  </div>
                </div>
              }
            />
            <Checkbox
              // onChange={(e) => {
              //   console.log("checked item", item);
              //   if (e.target.checked) {
              //     setSelectedCollegues([...selectedCollegues, item]);
              //   } else {
              //     setSelectedCollegues(
              //       selectedCollegues.filter(
              //         (collegue) => collegue.uuid !== item.uuid
              //       )
              //     );
              //   }
              // }}
              // checked={selectedCollegues.find(
              //   (selectedCollegue) => selectedCollegue.uuid === item.uuid
              // )}

              onChange={() => toggleUser(user)}
              checked={
                //intiitlay check if the user is already shared with the note
                selectedCollegues?.find(
                  (sharedUser) => sharedUser.uuid === user.uuid
                )
              }
            />
          </List.Item>
        )}
      </VirtualList>
      {loading && (
        <div
          style={{ textAlign: "center", padding: 8 }}
          className="flex w-full items-center justify-center"
        >
          <Spin />
        </div>
      )}
      <p
        className="text-xs text-gray-500"
        style={{
          textAlign: "justify",
          padding: "10px",
        }}
      >
        {t("selfNotes.sharingInfo")}
      </p>
      <div className="mt-2 flex items-center justify-end space-x-2">
        <Button
          type="default"
          primary
          onClick={() => {
            setOpen(false);
          }}
        >
          {t("selfNotes.cancelShare")}
        </Button>

        <Button
          type="primary"
          // disabled={shareLoading}
          loading={shareLoading}
          onClick={() => {
            share();
          }}
        >
          {/* {shareLoading ? (
            <Spin
              indicator={<LoadingOutlined spin />}
              size="small"
              //color="blue"
            />
          ) : (
            <>{t("selfNotes.saveShare")}</>
          )} */}
          <>{t("selfNotes.saveShare")}</>
        </Button>
      </div>
    </Modal>
  );
}

export default ShareModal;
