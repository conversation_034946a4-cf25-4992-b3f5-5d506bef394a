import React, { useEffect, useLayoutEffect, useState } from "react";
import { Row, Col } from "antd";
import Pipeline from "../../components/Pipeline";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate } from "react-router-dom";
import Stage from "../../components/Stage";
import TabsDetails from "../../components/Tabs";
import Trigger from "../../components/Trigger";
import Rules from "../../components/Rules";

const TriggersRules = () => {
  const [t] = useTranslation("common");
  const [editingKey, setEditingKey] = useState("");
  const [editingKeyRule, setEditingKeyRule] = useState("");
  const [dataRules, setDataRules] = useState([]);
  const [keyTab, setKeyTab] = useState("");
  const [trigger_id, setIdTrigger] = useState("1");
  const { pathname } = useLocation();
  const navigate = useNavigate();

  return (
    <>
      <Row>
        <Col
          xs={24}
          sm={24}
          md={24}
          lg={8}
          style={{
            // borderRight: "solid 1px #e2e8f0",
            padding: "0 5px",
            marginLeft: "15px",
            boxShadow:
              "0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",
          }}
        >
          <Trigger
            setEditingKey={setEditingKey}
            editingKey={editingKey}
            setEditingKeyRule={setEditingKeyRule}
            editingKeyRule={editingKeyRule}
            setIdTrigger={setIdTrigger}
            trigger_id={trigger_id}
            dataRules={dataRules}
            setDataRules={setDataRules}
          />
        </Col>
        <Col
          xs={24}
          sm={24}
          md={24}
          lg={15}
          style={{
            padding: "0 5px",
            marginLeft: "12px",
            boxShadow:
              "0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",
          }}
        >
          <Rules
            setEditingKey={setEditingKeyRule}
            editingKey={editingKeyRule}
            trigger_id={trigger_id}
            data={dataRules}
            setData={setDataRules}
          />
        </Col>
      </Row>
    </>
  );
};

export default TriggersRules;
