import { useEffect } from "react";
import MainService from "../../../../services/main.service";
import { useDispatch } from "react-redux";
import { handleTypingUser } from "new-redux/actions/chat.actions";
import { useSelector } from "react-redux";

export default function useTyping() {
  const dispatch = useDispatch();
  const userIsTyping = useSelector((state) => state.chat.userIsTyping);
  const selectedConversation = useSelector(
    (state) => state.ChatRealTime.selectedConversation
  );
  useEffect(() => {
    const controller = new AbortController();

    const setTypingFunction = async () => {
      if (userIsTyping === 0) {
        return;
      } else if (userIsTyping === 1) {
        try {
          await MainService.setTyping(
            selectedConversation?.id,
            selectedConversation?.type,
            controller.signal
          );
        } catch (error) {
          if (error.name === "CanceledError") return;
        }
      } else if (userIsTyping === 2) {
        try {
          const response = await MainService.stopTyping(
            selectedConversation?.id,
            selectedConversation?.type,
            controller.signal
          );
          if (response.status === 200) {
            dispatch(handleTypingUser(0));
          }
        } catch (error) {
          if (error?.name === "CanceledError") return;
        }
      }
    };

    if (!selectedConversation?.id) return;
    setTypingFunction();
    return () => {
      // controller.abort();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userIsTyping, selectedConversation?.id, selectedConversation?.type]);
}
