import React from "react";
import { EnvelopeIcon, MagnifyingGlassIcon } from "@heroicons/react/20/solid";

const Input = ({
  label,
  inputName,
  handleChange,
  type,
  htmlFor,
  placeholder,
  required,
  value,
  defaultValue,
  disabled,
}) => {
  return (
    <div>
      <label
        htmlFor={htmlFor}
        className="block text-sm font-medium text-gray-700"
      >
        {required ? <span className="mr-1 text-sm text-red-600">*</span> : null}
        {label}
      </label>
      <div className="relative mt-1 rounded-md shadow-sm">
        {inputName === "search" ? (
          <div className="pointer-events-none absolute inset-y-0 left-auto flex items-center pl-3">
            <MagnifyingGlassIcon
              className="h-5 w-5 text-gray-400"
              aria-hidden="true"
            />
          </div>
        ) : null}

        <input
          type={type}
          name={inputName}
          onChange={handleChange}
          placeholder={placeholder}
          value={value}
          disabled={disabled}
          defaultValue={defaultValue}
          className={`block w-full rounded-md border-gray-300 ${
            inputName === "search" && "pl-10"
          } focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm ${
            disabled
              ? "disabled:bg-slate-50 disabled:text-slate-500 disabled:border-slate-200 disabled:shadow-none cursor-not-allowed"
              : null
          }`}
        />
      </div>
    </div>
  );
};

export default Input;
