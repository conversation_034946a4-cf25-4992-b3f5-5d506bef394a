import { useCallback, useEffect, useState } from "react";

function useFocusWindow({ callback }) {
  const [timeBlured, setTimeBlured] = useState(0);

  const handleCallback = useCallback(() => callback(), [callback]);
  useEffect(() => {
    const blurFunc = () => setTimeBlured(Math.floor(Date.now() / 1000) + 1);

    window.addEventListener("focus", handleCallback, false);
    window.addEventListener("blur", blurFunc, false);

    return () => {
      window.removeEventListener("focus", handleCallback, false);
      window.removeEventListener("blur", blurFunc, false);
    };
  }, [handleCallback]);
  return { timeBlured, setTimeBlured };
}

export default useFocusWindow;
