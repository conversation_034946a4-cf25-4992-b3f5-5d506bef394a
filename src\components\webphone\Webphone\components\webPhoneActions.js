import { <PERSON><PERSON>, <PERSON><PERSON>, Toolt<PERSON> } from "antd";
import { ClockCircleOutlined } from "@ant-design/icons";
import { PiBookOpenBold } from "react-icons/pi";
import { FaVoicemail } from "react-icons/fa";
import { FiSettings } from "react-icons/fi";
import { IoKeypadSharp } from "react-icons/io5";
import { TbBackslash } from "react-icons/tb";
import { isGuestConnected } from "utils/role";

export const webPhoneActions = (
  navigate,
  setNavigate,
  nbrMissedCalls,
  nbrVoiceMessaging,
  t,
  isKeyPadUp,
  setIsKeyPadUp
) => {
  //
  const isGuest = isGuestConnected();
  //
  return [
    <Tooltip placement="bottom" title={t("voip.logAndSearch")}>
      <Badge count={nbrMissedCalls}>
        <Button
          key={"history"}
          size="small"
          type="text"
          shape="circle"
          onClick={() => setNavigate("history")}
          icon={
            <ClockCircleOutlined
              style={{
                fontSize: 19,
                color: navigate === "history" ? "#1677FF" : "#999c9e",
              }}
            />
          }
        />
      </Badge>
    </Tooltip>,

    <Tooltip placement="bottom" title={t("voip.directory")}>
      <Button
        key={"team"}
        size="small"
        type="text"
        shape="circle"
        onClick={() => setNavigate("team")}
        icon={
          <PiBookOpenBold
            style={{
              fontSize: 20,
              color: navigate === "team" ? "#1677FF" : "#999c9e",
            }}
          />
        }
      />
    </Tooltip>,

    <Tooltip
      placement="bottom"
      title={isKeyPadUp ? t("voip.hideDialPad") : t("voip.dialPad")}
    >
      <Button
        key={"keyPad"}
        size="small"
        type="text"
        shape="circle"
        onClick={() => {
          setNavigate("keyPad");
          setIsKeyPadUp((prev) => !prev);
        }}
        icon={
          <div className="relative flex items-start justify-center">
            <IoKeypadSharp
              style={{
                fontSize: 20,
                color: navigate === "keyPad" ? "#1677FF" : "#999c9e",
              }}
            />

            {isKeyPadUp && (
              <TbBackslash
                style={{
                  fontSize: 30,
                  color: "rgb(100 116 139)",
                  position: "absolute",
                  bottom: -5,
                }}
              />
            )}
          </div>
        }
      />
    </Tooltip>,

    <Tooltip placement="bottom" title={t("menu2.messaging")}>
      <Badge count={nbrVoiceMessaging}>
        <Button
          key={"voice"}
          size="small"
          type="text"
          shape="circle"
          onClick={() => setNavigate("voice")}
          disabled={isGuest}
          icon={
            <FaVoicemail
              style={{
                fontSize: 20,
                color: isGuest
                  ? ""
                  : navigate === "voice"
                  ? "#1677FF"
                  : "#999c9e",
              }}
            />
          }
        />
      </Badge>
    </Tooltip>,

    <Tooltip placement="bottom" title={t("menu1.settings")}>
      <Button
        key={"settings"}
        size="small"
        type="text"
        shape="circle"
        onClick={() => setNavigate("settings")}
        disabled={isGuest}
        icon={
          <FiSettings
            style={{
              fontSize: 19,
              color: isGuest
                ? ""
                : navigate === "settings"
                ? "#1677FF"
                : "#999c9e",
            }}
          />
        }
      />
    </Tooltip>,
  ];
};
