import { SearchOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Card, Input, Space, Table } from "antd";
import { AvatarChat } from "components/Chat";
import { URL_ENV } from "index";

import { getName } from "pages/layouts/chat/utils/ConversationUtils";
import ActionsComponent from "pages/tasks/ActionsComponent";
import { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { generateAxios } from "services/axiosInstance";
import Highlighter from "react-highlight-words";

const TableSdaStats = ({ start, end }) => {
  const { i18n } = useTranslation("common");
  const [data, setData] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);

  const [pageSize, setPageSize] = useState(5);
  const [sorter, setSorter] = useState({ field: "", order: "" });
  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState("");
  const [t] = useTranslation("common");

  useEffect(() => {
    const fetchCallModule = async () => {
      setLoading(true);
      try {
        const response = await generateAxios(
          `${URL_ENV.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
        ).get(
          `/sda-stat-table?start_date=${start}&end_date=${end}&language=${i18n.language}`
        );
        setData(response?.data);
        // setCurrentPage(response?.data?.page?.current_page);
        setTotal(response?.data?.meta?.total);
      } catch (error) {
        console.error(
          "Erreur lors de la récupération des appels sortants :",
          error
        );
      } finally {
        setLoading(false);
      }
    };
    fetchCallModule();
  }, [
    start,
    end,
    i18n.language,
    // currentPage,
    // pageSize,
    // searchText,
    // sorter.field,
    // sorter.order,
  ]);
  const handleChangePageSize = (current, size) => {
    setCurrentPage(1);
    setPageSize(size);
  };
  const handleChangePage = (page, pageSize, sorter) => {
    setCurrentPage(page);
    setPageSize(pageSize);
  };

  const searchInput = useRef(null);
  const handleSearch = (selectedKeys, confirm, dataIndex) => {
    confirm();
    setSearchText(selectedKeys.length > 0 ? selectedKeys[0] : "");
    setSearchedColumn(dataIndex);
  };
  const handleReset = (clearFilters) => {
    clearFilters();
    setSearchText("");
  };

  const getColumnSearchProps = (dataIndex) => ({
    filterDropdown: ({
      setSelectedKeys,
      selectedKeys,
      confirm,
      clearFilters,
      close,
    }) => (
      <div
        style={{
          padding: 8,
        }}
        onKeyDown={(e) => e.stopPropagation()}
      >
        <Input
          ref={searchInput}
          placeholder={`Search ${dataIndex}`}
          value={selectedKeys[0]}
          onChange={(e) =>
            setSelectedKeys(e.target.value ? [e.target.value] : [])
          }
          onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
          style={{
            marginBottom: 8,
            display: "block",
          }}
        />
        <Space>
          <Button
            type="primary"
            onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
            icon={<SearchOutlined />}
            size="small"
            style={{
              width: 90,
            }}
          >
            {t("activities.search")}
          </Button>
          <Button
            onClick={() => {
              clearFilters();
              setSearchText("");
              confirm();
            }}
            size="small"
            style={{
              width: 90,
            }}
          >
            {t("activities.reset")}
          </Button>

          <Button
            type="link"
            size="small"
            onClick={() => {
              close();
            }}
          >
            {t("activities.close")}
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered) => (
      <SearchOutlined
        style={{
          color: filtered ? "#1677ff" : undefined,
        }}
      />
    ),

    onFilterDropdownOpenChange: (visible) => {
      if (visible) {
        setTimeout(() => searchInput.current?.select(), 100);
      }
    },
    render: (text) => {
      // Vérifie si le texte inclut le terme recherché
      if (
        searchText &&
        text &&
        text?.toString()?.toLowerCase()?.includes(searchText?.toLowerCase())
      ) {
        return (
          <Highlighter
            highlightStyle={{
              backgroundColor: "#ffc069",
              padding: 0,
            }}
            searchWords={[searchText]}
            autoEscape
            textToHighlight={text ? text.toString() : ""}
          />
        );
      }
      // Retourne un texte vide si la recherche ne correspond pas
      return searchText ? null : text;
    },
  });
  const columns =
    Array.isArray(data?.data) &&
    data?.data?.length > 0 &&
    Object.keys(data?.data[0]).map((key) => {
      if (key === "SDA") {
        return {
          title: key,
          dataIndex: key,
          key,
          width: 200,
          fixed: "left",
          //   sorter: (a, b) => a.user.label.localeCompare(b.user.label),
          sorter: true,
          ...getColumnSearchProps(key, handleSearch),
        };
      }
      return {
        title: key.replace(/_/g, " "),
        dataIndex: key,
        key: key,
        sorter: (a, b) => a[key] - b[key],
      };
    });
  //   const components = {
  //     header: {
  //       cell: ({ children }) => (
  //         <th style={{ backgroundColor: "#84a3d58a", color: "black" }}>
  //           {children}
  //         </th>
  //       ),
  //     },
  //   };

  const handleTableChange = (pagination, filters, sorter) => {
    if (sorter?.field && sorter?.order)
      setSorter({
        field: sorter?.field,
        order: sorter?.order,
      });
    else
      setSorter({
        field: "",
        order: "",
      });
    setCurrentPage(pagination.current);
    setPageSize(pagination.pageSize);
  };
  const filteredData = data?.data?.filter((el) =>
    el["SDA"]?.toString().includes(searchText)
  );
  return (
    <div>
      {data?.data?.length > 0 ? (
        <Card
          title={
            <div
              style={{
                fontSize: "1.2em",
                color: "rgb(51, 51, 51)",
                fontWeight: "bold",
                fill: "rgb(51, 51, 51)",
              }}
            >
              {data?.name}
            </div>
          }
          styles={{ body: { padding: 0 } }}
        >
          <Table
            columns={columns}
            bordered={true}
            className="table-voip-stats"
            loading={loading}
            dataSource={filteredData}
            size="small"
            //   components={components}
            scroll={{
              x: "max-content",
            }}
            pagination={{
              current: currentPage,
              pageSize: pageSize,
              total: total,
              onChange: handleChangePage,
              onShowSizeChange: handleChangePageSize,
              showSizeChanger: true,
              pageSizeOptions: ["5", "10", "20", "50", "100"],
              // hideOnSinglePage:
              //   data && data?.filter((el) => el.id).length < 11 && true,
            }}
            onChange={handleTableChange}
          />
        </Card>
      ) : null}
    </div>
  );
};

export default TableSdaStats;
