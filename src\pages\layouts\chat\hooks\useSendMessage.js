import { useMutation } from "@tanstack/react-query";
import { toastNotification } from "components/ToastNotification";
import mainService from "services/main.service";
import { updateMessages } from "../utils/rqUpdate";
import { useTranslation } from "react-i18next";
import {
  objectNewMessage,
  imageExtensions,
  getOrGenerateTabId,
  uuid,
  getUrlsFromMessage,
} from "../utils/ConversationUtils";
import { useDispatch, useSelector } from "react-redux";
import {
  setAssetsToList,
  setDocumentToList,
  setImageToList,
  addThreadToList,
  updateThreadToList,
  setNewMessageChatMemebers,
  addPollList,
  addAllPollList,
  addLinksToList,
  updateChatSelectedConversation,
} from "new-redux/actions/chat.actions";
import {
  addUuidToMessage,
  removeUuidToMessage,
  scrollToBottom,
  setNewErrorMessage,
} from "new-redux/actions/chat.actions/Input";
import { moment_timezone } from "App";
let message_id_uid = null;

const abort = new AbortController();
export const useSendMessage = (type, uid) => {
  const { currentUser, msgUUID, openDrawer } = useSelector(
    (state) => state.chat
  );
  const dispatch = useDispatch();
  const { t } = useTranslation("common");
  return useMutation({
    mutationKey: "useSendMessage" + type,

    mutationFn: async ({ params, type_conversation, type_action }) => {
      //  let newMessage = insertSpaceBeforeTagOpening(params.message.trim());

      // console.log({ params });

      let newMessage = params.message
        .trim()
        //     .replace(/(<[^>]*>)\s+/g, "$1")
        .replace(/&nbsp;/g, " ");
      //  newMessage = insertSpaceAfterTag(newMessage);
      newMessage = newMessage === "<p></p>" ? "" : newMessage;
      message_id_uid = type_action.includes("re-new_message") ? uid : uuid();
      const new_message = objectNewMessage(
        type_conversation,
        message_id_uid,
        currentUser,
        params.selectedConversation,
        params.file,
        newMessage,
        params?.taggedPerson,
        params?.from,

        type_action === "create_polls" ? "poll_message" : undefined,
        undefined,
        params.from ? params.main_message : undefined,
        type_action === "create_polls"
          ? {
              question: params?.values?.question,
              options: params?.values?.options.map(
                (item, index) =>
                  item && {
                    id: index,
                    option: item,
                    users_id: [],
                    vote: 0,
                  }
              ),
              multi_answer: params?.values?.multi_answer ? 1 : 0,
              _id: message_id_uid,
            }
          : null
      );
      try {
        // saving each new msg in queue to be able to update it later.
        dispatch(
          addUuidToMessage({
            type: type_conversation,
            id: params.selectedConversation?.id,
            msg: new_message,
            uuid: message_id_uid,
          })
        );

        let response;

        const formData = new FormData();
        const accepted_type = [
          "new_message",
          "re-new_message",
          "new_message_no_chat",
        ];
        const refused_type = [
          // "re-new_message",
          "re-new_message_no_chat",
          "create_polls",
        ];
        if (!refused_type.includes(type_action)) {
          if (accepted_type.includes(type_action)) {
            updateMessages(
              new_message,
              type_conversation === "room"
                ? "new_message_event"
                : "new_message",
              params?.from ? params.main_message?._id : null,
              params.selectedConversation?.id,
              type_conversation,
              null,
              params.selectedConversation?.conversationId
            );
            dispatch(scrollToBottom(Math.floor(Math.random() * 1000000 + 1)));
          }
          if (type_action !== "new_message_no_chat") {
            if (params.from && type_action !== "re-new_message") {
              dispatch(addThreadToList(new_message));
            }

            dispatch(
              setNewMessageChatMemebers({
                type: type_conversation,
                data: new_message,
                discussion_id: params.selectedConversation?.id,

                user: params.selectedConversation,
                sender: currentUser,
              })
            );
          }
        }
        // afecting the message to update the id later after succes api .
        formData.append("code", `${message_id_uid}`);
        if (type_conversation === "room")
          formData.append(
            "source",
            type?.includes("no_chat") ? "no_chat" : "chat"
          );
        !params.webPhone && formData.append("tab_id", getOrGenerateTabId());

        formData.append(
          type_conversation === "room" ? "room_id" : "receiver_id",
          `${
            params.selectedConversation?.id || params.selectedConversation?._id
          }`
        );
        if (type_action === "create_polls") {
          formData.append("question", params?.values?.question);
          params?.values?.options?.forEach((item) => {
            item && formData.append("options[]", item);
          });
          formData.append("multi_answer", params?.values?.multi_answer ? 1 : 0);
          formData.append("private", params?.values?.private ? 0 : 1);
          formData.append("end_date", params?.values?.expire_At);
          response = await mainService.createPoll(formData, type_conversation);
        } else {
          if (params.from)
            formData.append("parent_id", params.main_message?._id ?? null);
          if (newMessage.length > 0) {
            formData.append("message", newMessage);

            if (type_conversation === "room" && params.taggedPerson.length > 0)
              formData.append("tags", params.taggedPerson);
          }
          if (params.file.length > 0)
            formData.append(
              "file_id",
              params.file.map((item) => item._id).toString()
            );

          if (getUrlsFromMessage(newMessage).length > 0)
            formData.append("urls", getUrlsFromMessage(newMessage).toString());
          if (params.from) {
            response = await mainService.sendReply(
              formData,
              type_conversation,
              abort.signal
            );
          } else
            response = await mainService.sendMessage(
              formData,
              type_conversation,
              abort.signal
            );
        }

        if (response.data.message === "You are not member of this room") {
          throw new Error("You are not member of this room");
        }
        return response;
      } catch (err) {
        console.error(err);
        if (type_action === "create_polls") {
          toastNotification("error", t("toasts.errorFetchApi"), "topRight");
          //  dispatch(filterWaitingMessage(new_message.id));

          throw new Error(err);
        }
        if (err?.message === "You are not member of this room") {
          toastNotification(
            "error",
            t("chat.room.error_send_message403"),
            "topRight",
            6,
            undefined,
            1,
            "not_allowed"
          );
          return;
        }

        if (
          (err.response &&
            err.response.status &&
            err.response.status === 401) ||
          err.message === "Network Error" ||
          err.name === "CanceledError"
          //   || type_action === "send_missed_call"
        )
          return;
        updateMessages(
          { ...new_message, unread: "error" },
          "all",
          new_message._id,
          type_conversation === "room"
            ? new_message?.room_id
            : new_message.receiver_id,
          new_message?.room_id ? "room" : "user",

          null
        );
        if (type_action !== "new_message_no_chat") {
          dispatch(
            updateThreadToList({
              newMsg: { ...new_message, unread: "error" },
              id_old: message_id_uid,
            })
          );
          // array of messages errors
          dispatch(
            setNewErrorMessage({
              message: { ...new_message, unread: "error" },
              type_conversation,
              discussion_id:
                type_conversation === "room"
                  ? new_message?.room_id
                  : new_message.receiver_id,
            })
          );

          dispatch(
            setNewMessageChatMemebers({
              data: { ...new_message, unread: "error" },
              unread: "error",
              sender: currentUser,
              type: type_conversation,
              discussion_id:
                type_conversation === "room"
                  ? new_message?.room_id
                  : new_message.receiver_id,
              user:
                type_conversation === "room"
                  ? new_message.room_info
                  : new_message.receiver,
              current_user: true,
            })
          );
        }
        //   dispatch(filterWaitingMessage(new_message.id));

        toastNotification("error", t("toasts.errorFetchApi"), "topRight");
      }
    },
    onError: () => {
      toastNotification("error", t("toasts.errorFetchApi"), "topRight");
    },
    onSuccess: (response) => {
      if (response) {
        let newMsg = {
          ...msgUUID.find((item) => response.data.message?.code === item.uuid)
            ?.msg,
          unread: 1,
          _id: response.data.message?._id,
          file_id: response.data?.message?.file_id,
          file: response.data?.message?.file,
          poll: response.data?.message?.poll || null,
        };
        try {
          if (response.data.action.includes("poll")) {
            dispatch(
              setNewMessageChatMemebers({
                type: newMsg?.room_id ? "room" : "user",
                data: newMsg,
                discussion_id: newMsg?.room_id ?? newMsg.receiver_id,

                user: newMsg?.room_id ? newMsg.room_info : newMsg.receiver,
                sender: currentUser,
              })
            );
            updateMessages(
              newMsg,

              "new_message_poll",
              undefined,

              newMsg?.room_id ?? newMsg.receiver_id,
              newMsg?.room_id ? "room" : "user",

              null
            );
            if (response.data?.message?.poll.end_date) {
              dispatch(addPollList(newMsg));
            }
            dispatch(addAllPollList(newMsg));
            toastNotification("success", t("chat.polls.success"), "topRight");
          } else {
            if (type !== "new_message_no_chat") {
              dispatch(
                setNewMessageChatMemebers({
                  type: newMsg?.room_id ? "room" : "user",
                  data: response.data.message._id,
                  discussion_id: newMsg?.room_id ?? newMsg.receiver_id,
                  reaction: null,
                  user: newMsg?.room_id ? newMsg.room_info : newMsg.receiver,
                  sender: currentUser,
                  conversation_id: response.data.conversation_id,
                })
              );
              if (response.data.action.includes("replay")) {
                dispatch(
                  updateThreadToList({
                    newMsg: newMsg,
                    id_old: response.data.message.code,
                  })
                );
                updateMessages(
                  newMsg,
                  "new_message_replay",
                  newMsg?.main_message?._id,
                  newMsg?.room_id ?? newMsg.receiver_id,
                  newMsg?.room_id ? "room" : "user",

                  null
                );
              }
            }
            if (response.data.conversation_id) {
              dispatch(
                updateChatSelectedConversation({
                  conversationId: response.data.conversation_id,
                })
              );
            }
            // }
            if (openDrawer.type === "info") {
              if (
                Object.keys(response.data.message).includes("file") &&
                response.data.message?.file?.length > 0
              ) {
                response.data.message.file.forEach((element) => {
                  if (imageExtensions.includes(element.type.split("/")[1])) {
                    dispatch(setAssetsToList({ images: 1 }));
                    dispatch(setImageToList({ element, type: "add" }));
                  } else {
                    dispatch(setDocumentToList({ element, type: "add" }));
                    dispatch(setAssetsToList({ documents: 1 }));
                  }
                });
              }
              const message =
                msgUUID.find(
                  (item) => response.data.message?.code === item.uuid
                )?.msg?.message || "<p></p>";

              const urls = getUrlsFromMessage(message);
              if (urls.length > 0) {
                dispatch(
                  addLinksToList(
                    urls.map((item) => ({
                      url: item,
                      message_id: response.data.message?._id,
                      user: currentUser?.name,
                      created_at: moment_timezone(new Date()).format(),
                    }))
                  )
                );
                dispatch(
                  setAssetsToList({
                    links: urls.length,
                  })
                );
              }
            }

            updateMessages(
              {
                _id: response.data.message?._id,
                file_id: response.data.message?.file_id,
                file: response.data.message?.file,
                poll: response.data.message?.poll,
                created_at:
                  response.data.message?.created_at ?? new Date().toISOString(),
              },

              "update_ids",
              type === "re-new_message" ? uid : response.data.message.code,

              newMsg?.room_id ?? newMsg.receiver_id,
              newMsg?.room_id ? "room" : "user",

              null
            );
          }
        } catch (error) {
          console.log(error);
        } finally {
          message_id_uid = null;

          dispatch(removeUuidToMessage(response.data.message.code));
        }
      }
    },
  });
};
