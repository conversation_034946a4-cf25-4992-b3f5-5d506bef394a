import { moment_timezone } from "App";
import dayjs from "dayjs";

// Format dates to display. Refer to https://day.js.org/docs/en/display/calendar-time
export const formatDateForDisplay = (dateString, format, user, t, type) => {
  const dateMoment = moment_timezone(dateString, format);

  return dateMoment.calendar(null, {
    sameDay: () => {
      return `${user?.location?.time_format}`;
    },
    lastDay: () => {
      return `[${t("voip.yesterday_at")}] ${user?.location?.time_format}`;
    },
    nextDay: () => {
      return `${user?.location?.date_format}`;
    },
    lastWeek: () => {
      if (type === "mailing") {
        return `${user?.location?.date_format} ${user?.location?.time_format}`;
      } else {
        return `ddd ${user?.location?.date_format}`;
      }
    },
    nextWeek: () => {
      return `ddd ${user?.location?.date_format} `;
    },
    sameElse: () => {
      if (type === "mailing") {
        return `${user?.location?.date_format} ${user?.location?.time_format}`;
      } else {
        return `ddd ${user?.location?.date_format}`;
      }
    },
  });
};

export const formatDatePayload = (value, format) => {
  if (value) {
    return dayjs(value)
      .format(format)
      .replaceAll(",", " ")
      .trim()
      .split(/[\s,\t,\n]+/)
      .join(" ");
  } else return "";
};

export const formatTimePayload = (value, format) => {
  if (value) {
    return dayjs(value).format(format).toUpperCase();
  } else return "";
};
