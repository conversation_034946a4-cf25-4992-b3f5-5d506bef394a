import { useState } from "react";
import { Button } from "antd";
import { PlusOutlined } from "@ant-design/icons";
//
import CreateTemplate from "./components/CreateTemplate";

const EmailTemplates = () => {
  //
  const [openCreate, setOpenCreate] = useState(false);
  //
  return (
    <div className="p-4">
      <Button
        icon={<PlusOutlined />}
        type="primary"
        onClick={() => setOpenCreate(true)}
      >
        Create New Template
      </Button>

      <CreateTemplate open={openCreate} setOpen={setOpenCreate} />
    </div>
  );
};

export default EmailTemplates;
