import { Tooltip } from "antd";
import React from "react";
import { useTranslation } from "react-i18next";

const LabelTable = ({ editingKey, edit, record, width = "100%" }) => {
  const [t] = useTranslation("common");

  const handleClick = (event) => {
    event.stopPropagation();
  };
  return (
    <Tooltip
      title={typeof width === "number" ? record.label || record.name : ""}
    >
      <div className="flex justify-between">
        <div
          className={` font-medium ${
            editingKey
              ? // || record.system == "1"
                ""
              : "cursor-pointer text-blue-600 hover:underline dark:text-blue-500"
          } `}
          onClick={(e) => {
            if (
              !editingKey
              // && record.system != "1"
            ) {
              edit(record);
              handleClick(e);
            }
          }}
        >
          <span className="flex space-x-1">
            <p className=" truncate" style={{ maxWidth: width }}>
              {record.label || record.name}{" "}
            </p>
            <span>
              {record.resolved == 1 ? `(${t("pipeline.resolved")})` : ""}
            </span>
          </span>
        </div>
      </div>
    </Tooltip>
  );
};

export default LabelTable;
