import { Avatar, Dropdown, Switch, Table, Tooltip } from "antd";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import Confirm from "./GenericModal";
import "./table.css";
import { toastNotification } from "./ToastNotification";
import {
  DeleteOutlined,
  EditOutlined,
  FieldTimeOutlined,
  RestOutlined,
} from "@ant-design/icons";

import { useTranslation } from "react-i18next";
import { generateAxios } from "../services/axiosInstance";
import { useLocation } from "react-router-dom";
import { URL_ENV } from "index";
const TableSla = ({
  data,
  setData,
  loading,
  setLabelId,
  setCount,
  count,
  setOpen,
  loadSwitch,
  setLoadSwitch,
  setSwitchId,
  switchId,
  severities,
  setTypeSla,
  confirmLoading,
}) => {
  const [t] = useTranslation("common");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [avatarColors, setAvatarColors] = useState([]);
  useEffect(() => {
    function generateRandomColor() {
      let maxVal = 0xffffff; // 16777215
      let randomNumber = Math.random() * maxVal;
      randomNumber = Math.floor(randomNumber);
      randomNumber = randomNumber.toString(16);
      let randColor = randomNumber.padStart(6, 0);
      return `#${randColor.toUpperCase()}`;
    }

    setData((prev) =>
      prev.map((el) => ({
        ...el,
        list_contact: el.list_contact.map((em) => ({
          ...em,
          color: em.color ? em.color : generateRandomColor(),
        })),
      }))
    );
    // console.log(
    //   data.map((el) => ({
    //     ...el,
    //     list_contact: el.list_contact.map((em) => ({
    //       ...em,
    //       color: generateRandomColor(),
    //     })),
    //   }))
    // );
  }, [setData, confirmLoading, loading]);

  const types = [
    { label: "contacts", value: "2" },
    { label: "products", value: "5" },
    { label: "teams", value: "63" },
    { label: "organisations", value: "1" },
  ];
  const handleChangePage = (page, pageSize) => {
    setCurrentPage(page);
    setPageSize(pageSize);
  };

  const handleChangePageSize = (current, size) => {
    setCurrentPage(1);
    setPageSize(size);
  };

  const columns = [
    {
      title: t("activities.name"),
      dataIndex: "label",
      key: "label",
      sorter: (a, b) => a.label.localeCompare(b.label),

      render: (_, record) => (
        <div className="flex justify-between">
          <div
            className={`font-medium  hover:underline dark:text-blue-500 ${
              loadSwitch
                ? "cursor-not-allowed	text-gray-200"
                : "cursor-pointer text-blue-600"
            } `}
            onClick={() => {
              if (!loadSwitch) {
                setLabelId(record.id);
                setOpen(true);
                setCount(count + 1);
              }
            }}
          >
            {record.label}
          </div>
          {!loadSwitch ? (
            <DropdownOption reccord={record} />
          ) : (
            <div className="r-8">
              <svg
                className="ant-dropdown-trigger h-4 w-4 cursor-pointer text-gray-400 hover:text-gray-700"
                aria-hidden="true"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"></path>
              </svg>
            </div>
          )}
        </div>
      ),
    },
    {
      title: "Type",
      dataIndex: "sla_type",
      key: "sla_type",
      filters: [
        {
          text: t(`helpDesk.customers`),
          value: "customers",
        },
        {
          text: t(`helpDesk.teams`),
          value: "teams",
        },
        {
          text: t(`helpDesk.produits`),
          value: "produits",
        },
      ],
      onFilter: (value, record) =>
        record.sla_type ===
        types.find(
          (type) =>
            t(`helpDesk.${type.label}`).toLowerCase() ===
            t(`helpDesk.${value}`).toLowerCase()
        )?.value,
      render: (_, { sla_type, seeder }) => (
        <div>
          {" "}
          {t(`helpDesk.${types.find((el) => el.value == sla_type)?.label}`)}
        </div>
      ),
    },
    {
      title: t("helpDesk.assignedTo"),
      dataIndex: "clients",
      key: "clients",
      render: (_, { list_contact, all_contact, sla_type, status }) =>
        all_contact ? (
          <div>{t(`helpDesk.${all_contact}`)} </div>
        ) : (
          <Avatar.Group
            maxCount={4}
            maxPopoverTrigger="click"
            // size="small"
            maxStyle={{
              color: "#f56a00",
              backgroundColor: "#fde3cf",
              cursor: "pointer",
              display: "flex",
              alignItems: "center",
            }}
          >
            {status == 0
              ? list_contact &&
                list_contact.length > 0 &&
                list_contact.map((el, i) => (
                  <Tooltip placement="top" title={el.label}>
                    <Avatar
                      style={{
                        backgroundColor: el.color,
                      }}
                      className="flex items-center justify-center"
                      key={el.id}
                    >
                      {el.label
                        ?.split(" ")
                        .map((el) => el[0])
                        .flat()}
                    </Avatar>
                  </Tooltip>
                ))
              : list_contact.length > 0 &&
                list_contact
                  .filter((el) => el.used == 1)
                  .map((el, i) => (
                    <Tooltip placement="top" title={el.label}>
                      <Avatar
                        style={{
                          backgroundColor: el.color,
                        }}
                        className="flex items-center justify-center"
                        key={el.id}
                      >
                        {el.label
                          ?.split(" ")
                          .map((el) => el[0])
                          .flat()}
                      </Avatar>
                    </Tooltip>
                  ))}
          </Avatar.Group>
        ),
    },
    {
      title: "Severities",
      children: [
        ...severities.map((severitie) => ({
          title: (
            <div style={{ color: severitie.color }} key={severitie.id}>
              {severitie.label}
            </div>
          ),
          dataIndex: severitie.label,
          key: severitie.label,
          render: (_, { severity }) => (
            <div className="flex items-center space-x-0.5">
              <span>
                {severity?.find((el) => el.label == severitie.label)?.hours ? (
                  <div>
                    <FieldTimeOutlined />

                    {severity?.find((el) => el.label == severitie.label)
                      ?.hours + "h"}
                  </div>
                ) : (
                  ""
                )}
              </span>
              {severity?.find((el) => el.label == severitie.label)?.minutes ? (
                <span>
                  :{" "}
                  {(severity?.find((el) => el.label == severitie.label)
                    ?.minutes || "") + "m"}
                </span>
              ) : null}
            </div>
          ),
        })),
      ],
    },

    {
      title: t("helpDesk.actif"),
      key: "status",
      filters: [
        {
          text: t(`helpDesk.actif`),
          value: "1",
        },
        {
          text: t(`helpDesk.noActif`),
          value: "0",
        },
      ],
      onFilter: (value, record) => record.status == value,
      sorter: (a, b) => a.status - b.status,

      render: ({ ...reccord }, { status, sla_type, seeder }) => (
        <>
          <Switch
            loading={reccord.id == switchId ? loadSwitch : ""}
            size="small"
            defaultChecked={status == 1 ? true : false}
            checked={status == 1 ? true : false}
            disabled={status == 1 && seeder == 1}
            onChange={(checked, event) => changeStatus(checked, event, reccord)}
          />
        </>
      ),
    },
  ];
  const deleteTask = async (id, label) => {
    try {
      const { data } = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).delete(`slas/${id}`);

      setData((prev) => prev.filter((el) => el.id !== id));
      toastNotification(
        "success",
        `${label}  ${t("toasts.deleted")}`,
        "topRight"
      );
    } catch (err) {
      toastNotification("error", `error`, "topRight");
    }
  };

  const changeStatus = async (checked, event, reccord) => {
    setSwitchId(reccord.id);
    setLoadSwitch(true);
    if (checked) {
      let formData = new FormData();
      formData.append("status", 1);
      formData.append(
        "list_contact",
        reccord.list_contact.map((el) => el.id).join(",")
      );

      try {
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post(
          `sla-status/update/${reccord.id}
      `,

          formData
        );

        // var updatedData = data.map(function (record) {
        //   var updatedContacts = [];

        //   if (record.id === reccord.id) {
        //     // Ne rien faire pour le contact ayant un ID égal à recordId
        //     record.status = 1;
        //     updatedContacts = record.list_contact.map(function (contact) {
        //       contact.used = 1;
        //       return contact;
        //     });
        //   }

        //   // Mettre à jour les contacts de la liste
        //   if (record.id !== reccord.id) {
        //     updatedContacts = record.list_contact.map(function (contact) {
        //       if (res.data.data.all_contact.split(",").includes(contact.id)) {
        //         contact.used = 0;
        //       }
        //       return contact;
        //     });
        //   }

        //   var allContactsUsedZero = record.list_contact.every(function (
        //     contact
        //   ) {
        //     return contact.used === 0;
        //   });

        //   // Mettre à jour le statut en fonction de la condition
        //   if (allContactsUsedZero) {
        //     record.status = 0;
        //   }
        //   // Retourner l'enregistrement mis à jour
        //   return {
        //     ...record,
        //     list_contact: updatedContacts,
        //   };
        // });

        // setData(updatedData);
        // setTypeSla(res.data.data.sla_type);
        // setLoadSwitch(false);
        // setSwitchId("");

        // toastNotification("success", data.label + t("toasts.edit"), "topRight");
        // // Utiliser le tableau mis à jour
        // console.log(updatedData);

        if (res.data.success === true) {
          const result = await generateAxios(
            URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
          ).get(
            `/slas
        `
          );
          setData(
            result.data.data.sla.map((el, index) => ({
              ...el,
              list_contact:
                el.list_contact.length > 0
                  ? el.list_contact.map((it, i) =>
                      it.id === data[index].list_contact[i].id
                        ? { ...it, color: data[index].list_contact[i]?.color }
                        : it
                    )
                  : [],
            }))
          );

          // setData((previous) =>
          //   previous.map((el) =>
          //     el.id == reccord.id ||
          //     (el.sla_type == data.sla_type && el.seeder == 1)
          //       ? { ...el, status: 1 }
          //       : el.sla_type == data.sla_type
          //       ? el
          //       : { ...el, status: 0 }
          //   )
          // );

          // setData(data);
          setTypeSla(res.data.data.sla_type);
          setLoadSwitch(false);
          setSwitchId("");

          toastNotification(
            "success",
            res.data.data.label + t("toasts.edit"),
            "topRight"
          );
        }
      } catch (err) {
        setSwitchId("");
        setData(
          data.map((el) => (el.id == reccord.id ? { ...el, status: 0 } : el))
        );

        setLoadSwitch(false);
        toastNotification("error", ` failed`, "topRight");
      }
    } else {
      let formData = new FormData();
      formData.append("status", 0);
      formData.append(
        "list_contact",
        reccord.list_contact.map((el) => el.id).join(",")
      );
      try {
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post(
          `sla-status/update/${reccord.id}
        `,

          formData
        );
        setData(
          data.map((el) => (el.id == reccord.id ? { ...el, status: 0 } : el))
        );
        setLoadSwitch(false);
        setSwitchId("");
        toastNotification(
          "success",
          res.data.data.label + t("toasts.edit"),
          "topRight"
        );
      } catch (err) {
        setLoadSwitch(false);
        setSwitchId("");
        toastNotification("error", ` failed`, "topRight");
      }
    }
  };

  const DropdownOption = (reccord) => {
    const items = [
      {
        label: t("table.edit"),
        key: "1",
        icon: <EditOutlined />,
        disabled: reccord.reccord.default == 1 ? true : false,
      },
      {
        label: t("table.delete"),
        danger: true,
        key: "2",
        icon: <DeleteOutlined />,
        disabled: reccord.reccord.seeder == 1 ? true : false,
      },
    ];
    return (
      <div className="r-8">
        <Dropdown
          trigger={["click"]}
          placement="bottomLeft"
          arrow
          menu={{
            items,
            onClick: (e) => {
              if (e.key === "1") {
                setLabelId(reccord.reccord.id);
                setCount(count + 1);
                setOpen(true);
              }
              if (e.key === "2") {
                Confirm(
                  `Delete "${reccord.reccord.label}" `,
                  "Confirm",
                  <RestOutlined style={{ color: "red" }} />,
                  function func() {
                    return deleteTask(
                      reccord.reccord.id,
                      reccord.reccord.label
                    );
                  },
                  true
                );
              }
            },
          }}
        >
          <svg
            className="h-4 w-4 cursor-pointer text-gray-400 hover:text-gray-700"
            aria-hidden="true"
            fill="currentColor"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"></path>
          </svg>
        </Dropdown>
      </div>
    );
  };
  return (
    <>
      <Table
        columns={columns}
        dataSource={
          data &&
          data.slice((currentPage - 1) * pageSize, currentPage * pageSize)
        }
        loading={loading}
        size={"small"}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          total: data && data.length,
          onChange: handleChangePage,
          onShowSizeChange: handleChangePageSize,
          showSizeChanger: false,
          pageSizeOptions: ["10", "20", "50", "100"],
          hideOnSinglePage: true,
        }}
      />
    </>
  );
};
export default TableSla;
