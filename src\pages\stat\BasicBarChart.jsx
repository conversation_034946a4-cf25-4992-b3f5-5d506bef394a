import React from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";

const BasicBarChart = ({ pivotData }) => {
  if (
    !pivotData ||
    !pivotData.rowKeys ||
    !pivotData.colKeys ||
    !pivotData.getAggregator
  ) {
    console.error("Invalid pivotData:", pivotData);
    return <p className="text-center text-red-600">No Data</p>;
  }

  const rowKeys = pivotData.rowKeys;
  const colKeys = pivotData.colKeys;

  if (rowKeys.length === 0 && colKeys.length === 0) {
    console.error("No row or column keys");
    return <p className="text-center text-red-600">No Data</p>;
  }

  const isRow = rowKeys.length > 0;
  const baseKeys = isRow ? rowKeys : colKeys;
  const seriesKeys = isRow ? colKeys : rowKeys;
  const categories = baseKeys.map((key) => key.join(" - "));

  const series =
    seriesKeys.length > 0
      ? seriesKeys.map((seriesKey) => ({
          name: seriesKey.join(" - ") || "Total",
          data: baseKeys.map((baseKey) => {
            const r = isRow ? baseKey : seriesKey;
            const c = isRow ? seriesKey : baseKey;
            const value = pivotData.getAggregator(r, c)?.value() || 0;
            return value;
          }),
        }))
      : [
          {
            name: "Total",
            data: baseKeys.map((baseKey) => {
              const r = isRow ? baseKey : [];
              const c = isRow ? [] : baseKey;
              const value = pivotData.getAggregator(r, c)?.value() || 0;
              return value;
            }),
          },
        ];

  const options = {
    chart: {
      type: "bar",
      height: 400,
    },
    title: {
      text: `📊
            ${pivotData.props.rows.length ? pivotData.props.rows.join(" ") : ""}
            ${
              pivotData.props.cols.length
                ? " : " + pivotData.props.cols.join(" ")
                : ""
            }`,
      align: "left",
      style: {
        fontWeight: "normal",
        fontSize: "20px",
        fontFamily: "Inter, sans-serif",
      },
    },
    xAxis: {
      categories: categories,
      gridLineWidth: 1,
      lineWidth: 0,
    },
    yAxis: {
      min: 0,
      labels: {
        overflow: "justify",
      },
      gridLineWidth: 0,
    },
    plotOptions: {
      series: {
        dataLabels: {
          enabled: true,
          formatter: function () {
            return this.y === 0 ? null : this.y;
          },
        },
      },
    },
    legend: {
      align: "left",
      verticalAlign: "top",
      backgroundColor:
        Highcharts.defaultOptions.legend?.backgroundColor ||
        "rgba(255,255,255,0.25)",
    },
    credits: {
      enabled: false,
    },
    series: series,
  };

  return (
    <div
      style={{ height: "100%", width: "100%" }}
      className="highcharts-figure"
    >
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
};

export default BasicBarChart;
