import { Avatar, Dropdown, Space, Switch, Table } from "antd";
import React, { useEffect, useState } from "react";
import { DndContext } from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { DeleteOutlined, EditOutlined, RestOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import Confirm from "../../components/GenericModal";
import { toastNotification } from "../../components/ToastNotification";
import { useNavigate } from "react-router-dom";
import Header from "../../components/configurationHelpDesk/Header";
import { setSearch } from "../../new-redux/actions/menu.actions/menu";
import { useDispatch, useSelector } from "react-redux";
import { URL_ENV } from "index";
import { showNameOrg } from "new-redux/actions/configCompanies.actions/configCompaniesAction";
import MainService from "services/main.service";

const TableCompanies = () => {
  const [t] = useTranslation("common");
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { search } = useSelector((state) => state.form);

  const columns = [
    {
      title: t("tags.icon"),
      dataIndex: "Icon",
      key: "Icon",

      width: "100px",
      render: (_, { icon }) =>
        icon ? (
          <>
            <Avatar
              size={32}
              src={`${
                URL_ENV?.REACT_APP_BASE_URL +
                URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
              }${icon}`}
              alt="icon"
            />
          </>
        ) : (
          ""
        ),
    },
    {
      title: t("companies.socialreason"),
      dataIndex: "label",
      key: "label",
      sorter: (a, b) => a.label.localeCompare(b.label),
      render: (_, record) => (
        <div className="flex justify-between">
          <div
            className="cursor-pointer font-medium text-blue-600 hover:underline dark:text-blue-500"
            onClick={() => {
              dispatch(showNameOrg(record.label));
              navigate(`/settings/general/companies/${record.id}`);
            }}
          >
            {record.label}
          </div>
          <DropdownOption reccord={record} />
        </div>
      ),
    },

    {
      title: t("companies.country"),
      dataIndex: "country",
      width: "250px",
      key: "country",
      sorter: (a, b) => a.country.localeCompare(b.country),
    },
    {
      title: t("companies.phone"),
      dataIndex: "phone",
      key: "phone",
    },
    {
      title: t("companies.email"),
      dataIndex: "email",
      key: "email",
      sorter: (a, b) => a.email.localeCompare(b.email),
    },
    {
      title: t("companies.principalCompany"),
      key: "primary_companie",
      sorter: (a, b) => b.primary_companie - a.primary_companie,

      render: ({ ...reccord }) => {
        return (
          <Switch
            size="small"
            disabled={reccord.primary_companie == 1}
            checked={reccord.primary_companie == 1}
            onChange={(checked, event) =>
              changePrimaryCompany(checked, event, reccord)
            }
          />
        );
      },
    },
  ];

  useEffect(() => {
    const getData = async () => {
      setLoading(true);
      try {
        const {
          data: { data },
        } = await MainService.getCompaniesInSettings();
        setData(data.map(item => ({
          ...item,
          key: item.id.toString()
        })));
        setLoading(false);
      } catch (err) {
        setLoading(false);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    };
    getData();
    return () => dispatch(setSearch(""));
  }, []);
  const deleteCompany = async (id, label) => {
    try {
      await MainService.deleteCompanyInSettings(id);
      setData((prev) => prev.filter((el) => el.id !== id));
      toastNotification(
        "success",
        `${label}  ${t("toasts.deleted")}`,
        "topRight"
      );
    } catch (err) {
      toastNotification("error", `error`, "topRight");
    }
  };
  const onDragEnd = (active, over) => {
    if (active.id !== over?.id) {
      setData((previous) => {
        const activeIndex = previous.findIndex((i) => i.key === active.id);
        const overIndex = previous.findIndex((i) => i.key === over?.id);
        return arrayMove(previous, activeIndex, overIndex);
      });
    }
  };
  const changePrimaryCompany = async (checked, event, reccord) => {
    setLoading(true);

    try {
      const res = await MainService.changePrimaryCompanyInSettings(
        { primary_companie: 1 },
        reccord.id
      );
      onDragEnd(data[0], res.data.data);
      setData((prev) =>
        prev.map((el) =>
          el.id == reccord.id
            ? { ...el, primary_companie: 1 }
            : { ...el, primary_companie: 0 }
        )
      );
      setLoading(false);

      toastNotification(
        "success",
        reccord.label + t("toasts.primaryCompany"),
        "topRight"
      );
    } catch (err) {
      setLoading(false);

      if (err.response.status === 422)
        toastNotification(
          "error",
          `${err.response.data.errors[0]}`,
          "topRight"
        );
      else toastNotification("error", ` failed`, "topRight");
    }
  };

  const DropdownOption = (reccord) => {
    const items = [
      {
        label: t("table.edit"),
        key: "1",
        icon: <EditOutlined />,
        disabled: reccord.reccord.default == 1 ? true : false,
      },
      {
        label: t("table.delete"),
        danger: true,
        key: "2",
        icon: <DeleteOutlined />,
        disabled: reccord.reccord.primary_companie == 1 ? true : false,
      },
    ];
    return (
      <div className="r-8">
        <Dropdown
          trigger={["click"]}
          placement="bottomLeft"
          arrow
          menu={{
            items,
            onClick: (e) => {
              if (e.key === "1") {
                dispatch(showNameOrg(reccord.reccord.label));

                navigate(`/settings/general/companies/${reccord.reccord.id}`);
              }
              if (e.key === "2") {
                Confirm(
                  `Delete "${reccord.reccord.label}" `,
                  "Confirm",
                  <RestOutlined style={{ color: "red" }} />,
                  function func() {
                    return deleteCompany(
                      reccord.reccord.id,
                      reccord.reccord.label
                    );
                  },
                  true
                );
              }
            },
          }}
        >
          <svg
            className="h-4 w-4 cursor-pointer text-gray-400 hover:text-gray-700"
            aria-hidden="true"
            fill="currentColor"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"></path>
          </svg>
        </Dropdown>
      </div>
    );
  };
  const handleChangePage = (page, pageSize) => {
    setCurrentPage(page);
    setPageSize(pageSize);
  };

  const handleChangePageSize = (current, size) => {
    setCurrentPage(1);
    setPageSize(size);
  };
  const filteredData = data.filter((item) => {
    return (
      item.label?.toLowerCase().includes(search.toLowerCase()) ||
      item.country?.toLowerCase().includes(search.toLowerCase()) ||
      item.email?.toLowerCase().includes(search.toLowerCase()) ||
      item.phone?.toLowerCase().includes(search.toLowerCase())
    );
  });
  return (
    <Space direction="vertical" style={{ width: "100%" }}>
      <Header
        active={"5"}
        editingKey={""}
        handleAdd={() => navigate("/settings/general/companies/new")}
        btnText={t("companies.addcompany")}
        disabled={loading ? true : false}
        api="companies"
      />
      <Space direction="vertical" style={{ width: "100%" }}>
        <DndContext onDragEnd={onDragEnd}>
          <SortableContext
            items={data.map((i) => i.key || `item-${i.id}`)}
            strategy={verticalListSortingStrategy}
          >
            <Table
              columns={columns}
              dataSource={filteredData}
              loading={loading}
              size={"small"}
              scroll={{ y: "calc(100vh - 290px)" }}
              rowKey="key"
              pagination={{
                current: currentPage,
                pageSize: pageSize,
                total: data && data.length,
                onChange: handleChangePage,
                onShowSizeChange: handleChangePageSize,
                showSizeChanger: true,
                pageSizeOptions: ["10", "20", "50", "100"],
                hideOnSinglePage: data.length < 11 && true,
              }}
            />
          </SortableContext>
        </DndContext>
      </Space>
    </Space>
  );
};
export default TableCompanies;
