import { tuple } from "antd/es/_util/type";
import React, { useState, useEffect } from "react";
import moment from "moment";
var userInfo = localStorage.getItem('infoUser');
const posteUser = (userInfo === null) ? "" : JSON.parse(userInfo).collegues[0].poste;

var journalTab =[];

function InfoUserContact({backToCollegues,userJournalInfo,journalLogs,callClick}) {


    function journalTab (i)
  {
    const journal = journalLogs.filter(word => word.src === userJournalInfo.number || word.dst === userJournalInfo.number);
    var icon;
    var type;
    var iconType;

    if (
        journal[journal.length - i].disposition === "FAILED" ||
        journal[journal.length - i].disposition === "BUSY"
      ) {
        if (journal[journal.length - i].src === posteUser) {
          icon = (
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-3 h-3 text-gray-700"  style={{marginRight:"0.25rem"}}><path fillRule="evenodd" d="M15 3.75a.75.75 0 01.75-.75h4.5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0V5.56l-4.72 4.72a.75.75 0 11-1.06-1.06l4.72-4.72h-2.69a.75.75 0 01-.75-.75z" clipRule="evenodd"></path><path fillRule="evenodd" d="M1.5 4.5a3 3 0 013-3h1.372c.86 0 1.61.586 1.819 1.42l1.105 4.423a1.875 1.875 0 01-.694 1.955l-1.293.97c-.135.101-.164.249-.126.352a11.285 11.285 0 006.697 6.697c.**************.352-.126l.97-1.293a1.875 1.875 0 011.955-.694l4.423 1.105c.834.209 1.42.959 1.42 1.82V19.5a3 3 0 01-3 3h-2.25C8.552 22.5 1.5 15.448 1.5 6.75V4.5z" clipRule="evenodd"></path></svg>

          );
          type = "Appel Émis";
          iconType = "Appel échoué";
        } else {
          icon = (
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-3 h-3 text-gray-700" style={{marginRight:"0.25rem"}}> <path fillRule="evenodd" d="M15.22 3.22a.75.75 0 011.06 0L18 4.94l1.72-1.72a.75.75 0 111.06 1.06L19.06 6l1.72 1.72a.75.75 0 01-1.06 1.06L18 7.06l-1.72 1.72a.75.75 0 11-1.06-1.06L16.94 6l-1.72-1.72a.75.75 0 010-1.06zM1.5 4.5a3 3 0 013-3h1.372c.86 0 1.61.586 1.819 1.42l1.105 4.423a1.875 1.875 0 01-.694 1.955l-1.293.97c-.135.101-.164.249-.126.352a11.285 11.285 0 006.697 6.697c.**************.352-.126l.97-1.293a1.875 1.875 0 011.955-.694l4.423 1.105c.834.209 1.42.959 1.42 1.82V19.5a3 3 0 01-3 3h-2.25C8.552 22.5 1.5 15.448 1.5 6.75V4.5z" clipRule="evenodd" /> </svg>
          );

          type = "Appel Manqués";
          iconType = "Appel échoué";
        }
      } else if (journal[journal.length - i].disposition === "ANSWERED") {
        if (journal[journal.length - i].src === posteUser) {
          icon = (
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-3 h-3 text-gray-700"  style={{marginRight:"0.25rem"}}><path fillRule="evenodd" d="M15 3.75a.75.75 0 01.75-.75h4.5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0V5.56l-4.72 4.72a.75.75 0 11-1.06-1.06l4.72-4.72h-2.69a.75.75 0 01-.75-.75z" clipRule="evenodd"></path><path fillRule="evenodd" d="M1.5 4.5a3 3 0 013-3h1.372c.86 0 1.61.586 1.819 1.42l1.105 4.423a1.875 1.875 0 01-.694 1.955l-1.293.97c-.135.101-.164.249-.126.352a11.285 11.285 0 006.697 6.697c.**************.352-.126l.97-1.293a1.875 1.875 0 011.955-.694l4.423 1.105c.834.209 1.42.959 1.42 1.82V19.5a3 3 0 01-3 3h-2.25C8.552 22.5 1.5 15.448 1.5 6.75V4.5z" clipRule="evenodd"></path></svg>

          );
          type = "Appel Émis";
          iconType = "Appel émis";
        } else {
          icon = (
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-3 h-3 text-gray-700"  style={{marginRight:"0.25rem"}}> <path fillRule="evenodd" d="M19.5 9.75a.75.75 0 01-.75.75h-4.5a.75.75 0 01-.75-.75v-4.5a.75.75 0 011.5 0v2.69l4.72-4.72a.75.75 0 111.06 1.06L16.06 9h2.69a.75.75 0 01.75.75z" clipRule="evenodd" /> <path fillRule="evenodd" d="M1.5 4.5a3 3 0 013-3h1.372c.86 0 1.61.586 1.819 1.42l1.105 4.423a1.875 1.875 0 01-.694 1.955l-1.293.97c-.135.101-.164.249-.126.352a11.285 11.285 0 006.697 6.697c.**************.352-.126l.97-1.293a1.875 1.875 0 011.955-.694l4.423 1.105c.834.209 1.42.959 1.42 1.82V19.5a3 3 0 01-3 3h-2.25C8.552 22.5 1.5 15.448 1.5 6.75V4.5z" clipRule="evenodd" /> </svg>
          );
          type = "Appel Reçus";
          iconType = "Appel reçus";
        }
      } else if (
        journal[journal.length - i].disposition === "NO ANSWER"
      ) {
        if (journal[journal.length - i].src === posteUser) {
          icon = (
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-3 h-3 text-gray-700"  style={{marginRight:"0.25rem"}}><path fillRule="evenodd" d="M15 3.75a.75.75 0 01.75-.75h4.5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0V5.56l-4.72 4.72a.75.75 0 11-1.06-1.06l4.72-4.72h-2.69a.75.75 0 01-.75-.75z" clipRule="evenodd"></path><path fillRule="evenodd" d="M1.5 4.5a3 3 0 013-3h1.372c.86 0 1.61.586 1.819 1.42l1.105 4.423a1.875 1.875 0 01-.694 1.955l-1.293.97c-.135.101-.164.249-.126.352a11.285 11.285 0 006.697 6.697c.**************.352-.126l.97-1.293a1.875 1.875 0 011.955-.694l4.423 1.105c.834.209 1.42.959 1.42 1.82V19.5a3 3 0 01-3 3h-2.25C8.552 22.5 1.5 15.448 1.5 6.75V4.5z" clipRule="evenodd"></path></svg>

          );
          type = "Appel Émis";
          iconType = "Appel émis";
        } else {
          icon = (
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-3 h-3 text-gray-700" style={{marginRight:"0.25rem"}}> <path fillRule="evenodd" d="M15.22 3.22a.75.75 0 011.06 0L18 4.94l1.72-1.72a.75.75 0 111.06 1.06L19.06 6l1.72 1.72a.75.75 0 01-1.06 1.06L18 7.06l-1.72 1.72a.75.75 0 11-1.06-1.06L16.94 6l-1.72-1.72a.75.75 0 010-1.06zM1.5 4.5a3 3 0 013-3h1.372c.86 0 1.61.586 1.819 1.42l1.105 4.423a1.875 1.875 0 01-.694 1.955l-1.293.97c-.135.101-.164.249-.126.352a11.285 11.285 0 006.697 6.697c.**************.352-.126l.97-1.293a1.875 1.875 0 011.955-.694l4.423 1.105c.834.209 1.42.959 1.42 1.82V19.5a3 3 0 01-3 3h-2.25C8.552 22.5 1.5 15.448 1.5 6.75V4.5z" clipRule="evenodd" /> </svg>

          );
          type = "Appel Manqués";
          iconType = "Appel manqué";
        }
      }

      return {sec:journal[journal.length - i].billsec,type:type,icon:icon,date:moment(journal[journal.length - i].calldate_start).format('ll'),heure:moment(journal[journal.length - i].calldate_start).format('LT')};

  }


    return (

        <div>

<div className="flex items-center justify-between px-4 pt-2">
    <div style={{cursor:"pointer",display:"flex"}}  onClick={backToCollegues}>
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2.5} stroke="currentColor" className="w-4 h-4" style={{alignSelf:"center"}}>
  <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
</svg>
<h1 className="text-sm font-semibold text-gray-300" style={{margin:"0"}}>Contacts</h1>

    </div>
    
                    </div>

                    <div>
                        <div style={{display:"flex",flexDirection:"column",alignItems:"center"}} >
                            <div style={{marginBottom:"8px"}}>
                            <svg className=" text-gray-500" style={{ borderRadius: "100%", height: "6rem", width: "6rem", }} fill="currentColor" viewBox="0 0 24 24"><path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z"></path></svg>
                                 </div>
                                        <p style={{color:"black",fontWeight:"500",fontSize:"1.5rem",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"normal",margin:"0"}} >{userJournalInfo.name}</p>
                                        <div style={{color:"gray"}}>{userJournalInfo.company}</div>
                                        </div>
                                        </div>








                                        <div>
                   <div className="flex justify-center space-x-3 my-4">
                    <div className="relative inline-block text-left" data-headlessui-state="">
                   <div>
                   <button onClick={() => callClick(userJournalInfo.number)} className="text-gray-200 group flex items-center justify-center px-5 pl-2 py-1.5 text-xs border border-gray-700 hover:bg-gray-800 hover:text-green-500 rounded-md" >
                   <div className="text-gray-400 group-hover:text-green-500 mr-1">
                   <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-4 h-4"><path fillRule="evenodd" d="M2 3.5A1.5 1.5 0 013.5 2h1.148a1.5 1.5 0 011.465 1.175l.716 3.223a1.5 1.5 0 01-1.052 1.767l-.933.267c-.41.117-.643.555-.48.95a11.542 11.542 0 006.254 6.254c.395.163.833-.07.95-.48l.267-.933a1.5 1.5 0 011.767-1.052l3.223.716A1.5 1.5 0 0118 15.352V16.5a1.5 1.5 0 01-1.5 1.5H15c-1.149 0-2.263-.15-3.326-.43A13.022 13.022 0 012.43 8.326 13.019 13.019 0 012 5V3.5z" clipRule="evenodd"></path></svg>
                   </div>
                   <span className="text-black">Appel</span>
                   {/* <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-4 h-4"><path fillRule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clipRule="evenodd"></path></svg> */}
                   </button>
                   
                   </div>
                   </div>
                   {/* <div className="relative inline-block text-left" data-headlessui-state="">
                    <div>
                        <button className="text-gray-200 group flex items-center justify-center px-2 py-1.5 text-xs border border-gray-700 hover:bg-gray-800 hover:text-green-500 rounded-md" id="headlessui-menu-button-:R356:" type="button" aria-haspopup="true" aria-expanded="false" data-headlessui-state=""><div className="text-gray-400 group-hover:text-green-500 mr-1">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-4 h-4"><path fillRule="evenodd" d="M10 2c-2.236 0-4.43.18-6.57.524C1.993 2.755 1 4.014 1 5.426v5.148c0 1.413.993 2.67 2.43 2.902 1.168.188 2.352.327 3.55.414.28.02.521.18.642.413l1.713 3.293a.75.75 0 001.33 0l1.713-3.293a.783.783 0 01.642-.413 41.102 41.102 0 003.55-.414c1.437-.231 2.43-1.49 2.43-2.902V5.426c0-1.413-.993-2.67-2.43-2.902A41.289 41.289 0 0010 2zM6.75 6a.75.75 0 000 1.5h6.5a.75.75 0 000-1.5h-6.5zm0 2.5a.75.75 0 000 1.5h3.5a.75.75 0 000-1.5h-3.5z" clipRule="evenodd"></path></svg>
                            </div>
                            <span>Message</span>
                            </button>
                            </div>
                            </div> */}
                            {/* <div className="relative inline-block text-left" data-headlessui-state="">
                                <div>
                                    <button className="text-gray-200 group flex items-center justify-center px-2 py-1.5 text-xs border border-gray-700 hover:bg-gray-800 hover:text-green-500 rounded-md" id="headlessui-menu-button-:R3l6:" type="button" aria-haspopup="true" aria-expanded="false" data-headlessui-state="">
                                        <span>Actions</span>
                                        <div className="text-gray-400 group-hover:text-green-500 ml-1">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-4 h-4"><path fillRule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clipRule="evenodd"></path></svg>
                                            </div>
                                            </button>
                                            </div>
                                            </div> */}
                                            </div>
                                            </div>


 <div className="bg-gray-200 px-2 py-3">

<div className="divide-y divide-gray-700">
{
    journalLogs.filter(word => word.src === userJournalInfo.number || word.dst === userJournalInfo.number).length !==0?
       <div className="py-2"><div className="flex space-x-12 items-start justify-between mt-1">
           <div className="flex flex-col"><div className="flex items-center space-x-1">
                 {journalTab(1).icon}           
               <span className="text-black font-medium text-sm">{journalTab(1).type}</span>
               </div><div className="text-gray-400 text-xs pl-4">{journalTab(1).sec}</div>
               </div>
               <div className="flex flex-col justify-end items-end"><div className="text-sm text-gray-300 font-medium text-right">{journalTab(1).date}</div>
               <div className="text-gray-700 text-xs">{journalTab(1).heure}</div>
               </div>
               </div>
               </div>

:null


}

{
    journalLogs.filter(word => word.src === userJournalInfo.number || word.dst === userJournalInfo.number).length >1?
               <div className="py-2"><div className="flex space-x-12 items-start justify-between mt-1">
           <div className="flex flex-col"><div className="flex items-center space-x-1">
                 {journalTab(2).icon}           
               <span className="text-black font-medium text-sm">{journalTab(2).type}</span>
               </div><div className="text-gray-400 text-xs pl-4">{journalTab(2).sec}</div>
               </div>
               <div className="flex flex-col justify-end items-end"><div className="text-sm text-gray-300 font-medium text-right">{journalTab(2).date}</div>
               <div className="text-gray-700 text-xs">{journalTab(2).heure}</div>
               </div>
               </div>
               </div>

:null


}


               </div>
               </div>





        </div>
    )
    
}

export default InfoUserContact;
