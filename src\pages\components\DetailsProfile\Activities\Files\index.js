import React, { useEffect, useState, useRef } from "react";
import {
  Button,
  Form,
  Input,
  Upload,
  Space,
  message,
  Switch,
  Tooltip,
} from "antd";
import {
  InboxOutlined,
  FileTextOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  GlobalOutlined,
  TeamOutlined,
} from "@ant-design/icons";
import { useSelector, useDispatch } from "react-redux";
import MainService from "../../../../../services/main.service";
import FilesList from "./FilesList";
import { renderFileSize } from "./utlis";
import {
  addFile360,
  setCurrentSelectedContact,
  unsetCurrentSelectedContact,
} from "../../../../../new-redux/actions/files.actions/files";
import { useTranslation } from "react-i18next";
import { toastNotification } from "components/ToastNotification";

function Files({ headerHeight, from, setKpi = () => {}, contactInfo }) {
  const [form] = Form.useForm();
  const [t] = useTranslation("common");
  const dispatch = useDispatch();

  const [formLayout, setFormLayout] = useState("vertical");
  const [uploading, setUploading] = useState(false);
  const [labelValue, setLabelValue] = useState(""); // [
  const [fileToUpload, setFileToUpload] = useState(null); // [
  const [switchValue, setSwitchValue] = useState(false);

  const defaultUsr = useSelector((state) => state?.user?.user);

  const onFormLayoutChange = ({ layout }) => {
    setFormLayout(layout);
  };

  useEffect(() => {
    dispatch(setCurrentSelectedContact(contactInfo.id));

    return () => {
      dispatch(unsetCurrentSelectedContact(null));
    };
  }, [contactInfo]);

  const asteriskRed = () => {
    return (
      <div
        _dangerouslySetInnerHTML={{
          __html: '<span className="text-red-500">*</span>',
        }}
      />
    );
  };
  const onFinish = (values) => {
    console.log("Received values of form: ", values);
    let formData = new FormData();
    formData.append("file_label", values.label);
    formData.append(
      "file_name",
      values.dragger[values.dragger.length - 1].name
    );
    formData.append(
      "upload",
      values.dragger[values.dragger.length - 1].originFileObj
    );
    formData.append("family_id", contactInfo?.family_id);
    formData.append("element_id", contactInfo?.id);
    if (
      defaultUsr?.role?.toLowerCase() === "guest" ||
      defaultUsr?.role?.toLowerCase() === "superguest"
    ) {
      formData.append("is_public", 1);
    } else {
      formData.append("is_public", switchValue ? 1 : 0);
    }

    console.log("FORM DATA", formData);

    setUploading(true);

    //if is a task the send (type:0 and not send family_id)
    //else dend (type: 1 an send family_id)

    // if (contactInfo?.type === "task") {
    //   formData.append("type", 0);
    // } else {
    //   formData.append("type", 1);
    //   formData.append("family_id", contactInfo?.family_id);
    // }

    MainService.uploadFile360(formData)
      .then((response) => {
        console.log(response);
        // message.success("File uploaded successfully");
        toastNotification(
          "success",
          t("files360.uploadFile", { filename: values.label })
        );
        console.log(response?.data);
        console.log("collection log", response?.data?.message?.collection);
        let collection = response?.data?.message?.collection;
        //cause the returned value of creator is an array
        // collection.creator = collection?.creator;
        dispatch(addFile360(collection));
        setFileToUpload(null);
        setKpi((prev) =>
          prev.map((el) =>
            el.title === "Files" ? { ...el, value: el.value + 1 } : el
          )
        );
        form.resetFields();
      })
      .catch((error) => {
        // console.log("ERROR upload", error);
        if (error?.response?.status == 422) {
          toastNotification(
            "error",
            t("files360.fileCannotBeUploadedToSecurityReasons")
          );
        } else {
          toastNotification("error", t("files360.fileErrorWhileUploading"));
        }
      })
      .finally(() => {
        setUploading(false);
      });
  };

  const formItemLayout =
    formLayout === "horizontal"
      ? {
          labelCol: {
            span: 4,
          },
          wrapperCol: {
            span: 14,
          },
        }
      : null;
  const buttonItemLayout =
    formLayout === "horizontal"
      ? {
          wrapperCol: {
            span: 14,
            offset: 4,
          },
        }
      : null;

  const normFile = (e) => {
    // console.log("Upload event:", e);

    if (e?.file?.size > 10485760) {
      // message.error("File size must be less than 10MB");
      toastNotification("error", "File size must be less than 10MB");
      return null;
    }

    if (Array.isArray(e)) {
      return e;
    }
    return e?.fileList;
  };
  const textareaRef = useRef(null);

  const handleKeyDown = (event) => {
    // Vous pouvez gérer certaines touches ici si nécessaire
    event.stopPropagation();
  };

  return (
    <div
      style={{
        height:
          from === "sidebarViewSphere"
            ? "calc(100vh - 75px)"
            : `calc(100vh - ${headerHeight + 135}px`,
      }}
      className={`${
        from !== "sidebarViewSphere" ? "-mr-[20px] pr-[10px]" : "-mr-2 pr-2"
      } overflow-y-auto `}
      ref={textareaRef}
      onKeyDown={handleKeyDown}
    >
      <div
        style={{
          border: "1px solid #e8e8e8",
        }}
        className="rounded-md px-3 py-2"
      >
        <Form
          {...formItemLayout}
          layout={"vertical"}
          form={form}
          initialValues={{
            layout: formLayout,
          }}
          onValuesChange={onFormLayoutChange}
          style={{
            // maxWidth: formLayout === "vertical" ? "none" : 600,
            maxWidth: "100%",
          }}
          onFinish={onFinish}
        >
          <Form.Item
            label="Label"
            name="label"
            rules={[
              {
                required: true,
                message: t("files360.labelErr"),
              },
            ]}
          >
            <Input
              placeholder={t("files360.labelPlaceHolder")}
              onChange={(e) => {
                setLabelValue(e.target.value);
              }}
            />
          </Form.Item>

          <Form.Item
            label={`${t("files360.file")}`}
            rules={[
              {
                required: true,
              },
            ]}
          >
            <Form.Item
              name="dragger"
              valuePropName="fileList"
              getValueFromEvent={normFile}
              noStyle
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <Upload.Dragger
                name="files"
                onChange={(info) => {
                  //file size must be less than 10MB
                  console.log("INFOS", info);
                  if (info?.file?.size <= 10485760) {
                    setFileToUpload(info?.file);
                  }
                }}
                accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.jpg,.png,.zip"
                onPreview={(file) => {
                  console.log("FILE", file);
                }}
                showUploadList={false}
                multiple={false}
              >
                {fileToUpload ? (
                  <div className="flex flex-col items-center justify-center">
                    <FileTextOutlined className="text-4xl" />
                    <p className="text-md">{fileToUpload.name}</p>
                    <p className="text-md">
                      {renderFileSize(fileToUpload.size)}
                    </p>
                  </div>
                ) : (
                  <>
                    <p className="ant-upload-drag-icon">
                      <InboxOutlined />
                    </p>
                    <p className="ant-upload-text">
                      {t("files360.filePlaceHolder")}
                    </p>
                  </>
                )}
              </Upload.Dragger>
            </Form.Item>
          </Form.Item>

          <Form.Item
            //   wrapperCol={{
            //     span: 12,
            //     offset: 6,
            //   }}
            className="text-right"
          >
            <Space className="flex items-center justify-end space-x-2">
              {defaultUsr.role.toLowerCase() !== "guest" &&
              defaultUsr.role.toLowerCase() !== "superguest" ? (
                <div
                  className="flex items-center space-x-2 "
                  style={{ marginTop: "10px" }}
                >
                  <div className="flex items-center">
                    {switchValue ? (
                      <>
                        <GlobalOutlined className="text-lg text-gray-400" />
                        <p className="ml-1 text-xs text-gray-400">
                          {t("notes360.public")}
                        </p>
                      </>
                    ) : (
                      <>
                        <TeamOutlined className="text-lg text-gray-400" />
                        <p className="ml-1 text-xs text-gray-400">
                          {t("notes360.internal")}
                        </p>
                      </>
                    )}
                  </div>
                  <Switch
                    direction="vertical"
                    value={switchValue}
                    onChange={(checked) => {
                      setSwitchValue(checked);
                    }}
                    // style={{ marginTop: "10px" , float: "left" }}
                  />
                </div>
              ) : null}
              <Button
                type="primary"
                htmlType="submit"
                loading={uploading}
                disabled={!labelValue || !fileToUpload}
              >
                {t("files360.uploadSubmit")}
              </Button>
              <Button
                htmlType="reset"
                onClick={() => {
                  form.resetFields();
                  setLabelValue("");
                  setFileToUpload(null);
                }}
                disabled={uploading}
              >
                {t("files360.uploadReset")}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </div>
      <FilesList setKpi={setKpi} contactInfo={contactInfo} />
    </div>
  );
}

export default Files;
