import { queryClient } from "index";
import {
  addSavedMessage,
  removeSavedMessage,
  updateAllPollList,
  updatePollList,
} from "new-redux/actions/chat.actions";
import { store } from "new-redux/store";
import { systemMessageTypes, uniqByKey, uuid } from "./ConversationUtils";
import { UUID_REGEX } from "utils/regex";

export const updateMessages = (
  newMessage,
  type,
  message_id,
  discussion_id,
  discussion_type,
  sender_id,
  isNew
) => {
  queryClient.setQueryData(
    ["getMessages", discussion_id, discussion_type],
    (oldData) => {
      // push message only for systemType and new conversation or locale message
      if (
        !oldData &&
        type.includes("new_message") &&
        (newMessage.locale ||
          (systemMessageTypes.includes(newMessage.type) && isNew)) &&
        newMessage.receiver_id
      ) {
        return {
          pages: [
            {
              data: [newMessage],
              meta: {
                current_page: 1,
                last_page: 1,
              },
            },
          ],
        };
      } else if (!oldData) {
        return oldData;
      }

      let newData = oldData;
      const type_message = ["new_message", "new_message_poll"];
      if (type_message.includes(type)) {
        const isExist = newData.pages?.[0]?.data?.some(
          (element) => newMessage?._id === element?._id
        );
        newData.pages[0].data = !isExist
          ? [newMessage, ...newData.pages[0].data]
          : [...newData.pages[0].data];
      } else if (type === "new_message_event") {
        let found = false;
        newData.pages.map((page, key) => {
          if (key === 0) {
            page.data = !page?.data?.some(
              (element) => newMessage?._id === element?._id
            )
              ? [newMessage, ...page.data]
              : [...page.data];
          }

          page.data = page.data.map((item, key) => {
            if (
              ((!UUID_REGEX.test(item._id) &&
                UUID_REGEX.test(page.data[0]?._id) &&
                !found) ||
                key === 0) &&
              page.meta.current_page === 1 &&
              item.type !== "message_from_bot"
            ) {
              found = true;
              item.unread = key > 0 ? 0 : item.unread;

              item.unread_room = [...new Set([...item.unread_room, sender_id])];
            } else
              item.unread_room = item.unread_room.filter(
                (e) => e !== sender_id
              );
            return item;
          });

          return page;
        });
      } else if (type === "new_messages") {
        newData.pages[0].data = [...newMessage, ...newData.pages[0].data];
      } else if (type === "undo_new_message") {
        newData.pages[0].data = newData.pages[0].data.filter(
          (item) => item._id !== message_id
        );
      } else {
        newData.pages.map((page) => {
          page.data = page.data.map((item, key) => {
            if (type === "make_read") {
              return {
                ...item,
                unread: item.unread === "error" ? item.unread : 0,
                updated_at: new Date().toISOString(),
              };
            } else if (type === "make_read_room") {
              if (item.unread !== 0 && item.unread !== "error") {
                item.unread = 0;
              }
              if (
                key === 0 &&
                page.meta.current_page === 1
                // &&
                // item.type !== "message_from_bot"
              ) {
                item.unread_room = [
                  ...new Set([...item.unread_room, sender_id]),
                ];
                item.updated_at = new Date().toISOString();
              } else
                item.unread_room = item.unread_room.filter(
                  (e) => e !== sender_id
                );
              item.updated_at = new Date().toISOString();

              return item;
            } else if (
              type === "deleted" &&
              item.parent_id &&
              item.parent_id === message_id
            ) {
              item.main_message = null;
              item.updated_at = new Date().toISOString();
            } else if (
              type === "update" &&
              item.parent_id &&
              item.parent_id === message_id
            ) {
              item.main_message = {
                ...item.main_message,
                message: newMessage.message,
              };
              item.updated_at = new Date().toISOString();
            }

            if (item._id === message_id) {
              if (type === "all") {
                return newMessage;
              } else if (type === "add_react") {
                return {
                  ...item,

                  reactions: [item.reactions || []]?.some(
                    (rec) =>
                      rec.reaction === Number(newMessage) &&
                      rec.user_id === sender_id
                  )
                    ? [...item.reactions]
                    : [
                        ...(item.reactions || []),
                        {
                          _id: uuid(),
                          user_id: sender_id,
                          reaction: Number(newMessage),
                          created_at: new Date().toISOString(),

                          updated_at: new Date().toISOString(),
                        },
                      ].sort((a, b) => a.reaction - b.reaction),
                  updated_at: new Date().toISOString(),
                };
              } else if (type === "add_react_replies") {
                return {
                  ...item,
                  replies: item.replies.map((reply) =>
                    reply._id === newMessage.message_replies_id
                      ? {
                          ...reply,
                          reactions: [
                            ...(reply.reactions || []),
                            {
                              _id: uuid(),
                              user_id: sender_id,
                              reaction: newMessage.reaction,
                              created_at: new Date().toISOString(),
                              updated_at: new Date().toISOString(),
                            },
                          ].sort((a, b) => a.reaction - b.reaction),
                        }
                      : reply
                  ),
                  updated_at: new Date().toISOString(),
                };
              } else if (type === "remove_react_replies") {
                return {
                  ...item,
                  updated_at: new Date().toISOString(),

                  replies: item.replies.map((reply) =>
                    reply._id === newMessage.message_replies_id
                      ? {
                          ...reply,
                          reactions: reply.reactions
                            ? reply.reactions
                                ?.filter(
                                  (rec) =>
                                    !(
                                      rec.user_id === sender_id &&
                                      rec.reaction === newMessage.reaction
                                    )
                                )
                                .sort((a, b) => a.reaction - b.reaction)
                            : [],
                        }
                      : reply
                  ),
                };
              } else if (type === "remove_react") {
                return {
                  ...item,
                  reactions: item.reactions
                    ? item.reactions
                        ?.filter(
                          (item) =>
                            !(
                              item.user_id === sender_id &&
                              item.reaction === newMessage
                            )
                        )
                        .sort((a, b) => a.reaction - b.reaction)
                    : [],
                  updated_at: new Date().toISOString(),
                };
              } else if (type === "add_favorite") {
                return {
                  ...item,
                  favoris: !item.favoris
                    ? [sender_id]
                    : item.favoris && !item.favoris?.includes(sender_id)
                    ? [...item.favoris, sender_id]
                    : null,
                  updated_at: new Date().toISOString(),
                };
              } else if (type === "remove_favorite") {
                return {
                  ...item,
                  favoris:
                    item.favoris && item.favoris.length > 1
                      ? item.favoris.filter((favoris) => favoris !== sender_id)
                      : null,
                  updated_at: new Date().toISOString(),
                };
              } else if (type === "deleted") {
                return {
                  ...item,
                  message: "Message deleted",
                  replies: [],
                  deleted_at: new Date().toISOString(),
                  updated_at: new Date().toISOString(),
                };
              } else if (type === "deleted_replies") {
                return {
                  ...item,
                  replies: item.replies.filter(
                    (reply) => reply._id !== newMessage
                  ),
                  updated_at: new Date().toISOString(),
                };
              } else if (type.includes("add_saved")) {
                if (type === "add_saved_E")
                  store.dispatch(
                    addSavedMessage({ ...item, important: sender_id })
                  );

                return {
                  ...item,
                  important: sender_id,
                  updated_at: new Date().toISOString(),
                };
              } else if (type.includes("remove_saved")) {
                if (type === "remove_saved_E")
                  store.dispatch(
                    removeSavedMessage({ ...item, important: "" })
                  );

                return {
                  ...item,
                  important: "",
                  updated_at: new Date().toISOString(),
                };
              } else if (type === "update") {
                return {
                  ...item,
                  type: newMessage.type,
                  message: newMessage.message,
                  edit: newMessage.edit,
                  updated_at: new Date().toISOString(),
                };
              } else if (type === "update_replies") {
                return {
                  ...item,
                  updated_at: new Date().toISOString(),

                  replies: item.replies.map((reply) =>
                    reply._id === newMessage._id
                      ? {
                          ...reply,
                          message: newMessage.message,
                          edit: newMessage.edit,
                        }
                      : reply
                  ),
                };
              } else if (type === "new_message_replay") {
                return {
                  ...item,
                  replies: uniqByKey([...item.replies, newMessage], "_id"),
                  updated_at: new Date().toISOString(),
                };
              } else if (type === "update_ids")
                return {
                  ...item,
                  created_at: newMessage.created_at,
                  updated_at: newMessage.created_at,

                  _id: newMessage._id,
                  unread: 1,
                  file_id: newMessage.file_id ?? [],
                  file: newMessage.file ?? [],
                  poll: newMessage.poll ?? null,
                };
              else if (type === "forward_message") {
                return {
                  ...item,
                  forward: newMessage,
                };
              } else if (type === "new_integration") {
                if (!Array.isArray(item.integrations)) {
                  item.integrations = [];
                }
                return {
                  ...item,
                  integrations: [...item.integrations, newMessage],
                };
              } else if (type.includes("add_vote")) {
                let new_array =
                  type === "add_vote" && item.poll && item.poll.options
                    ? item.poll.options.map((op) => {
                        let updatedOption = { ...op };

                        if (newMessage.values.includes(op._id)) {
                          let canAdd = ![...op.users_id].includes(sender_id);
                          updatedOption = {
                            ...op,
                            users_id: [...new Set([...op.users_id, sender_id])],
                            vote: canAdd ? op.vote + 1 : op.vote,
                          };
                        } else {
                          let canRemove = [...op.users_id].includes(sender_id);

                          updatedOption = {
                            ...op,
                            users_id: op.users_id.filter(
                              (user) => user !== sender_id
                            ),
                            vote: canRemove
                              ? op.vote > 0
                                ? op.vote - 1
                                : 0
                              : op.vote,
                          };
                        }

                        return updatedOption;
                      })
                    : [];
                const new_item =
                  type === "add_vote_event"
                    ? newMessage
                    : {
                        ...item.poll,
                        users_voted_ids:
                          !newMessage.values || newMessage?.values.length === 0
                            ? item.poll.users_voted_ids.filter(
                                (item) => item !== sender_id
                              )
                            : [
                                ...new Set([
                                  ...item.poll.users_voted_ids,
                                  sender_id,
                                ]),
                              ],
                        // total_vote_users:
                        //   !item.poll.user_has_answer ||
                        //   newMessage?.values.length > 0
                        //     ? item.poll.total_vote_users + 1
                        //     : item.poll.total_vote_users,
                        total_vote:
                          !newMessage.remove &&
                          (item.poll.multi_answer === 0
                            ? !item.poll.users_voted_ids.includes(sender_id)
                            : true)
                            ? item.poll.total_vote + 1
                            : !newMessage.values ||
                              newMessage.values.length === 0 ||
                              newMessage.remove
                            ? item.poll.total_vote > 0
                              ? item.poll.total_vote - 1
                              : 0
                            : item.poll.total_vote,
                        // ,
                        options: new_array,
                      };
                if (item.poll?.end_date)
                  store.dispatch(
                    updatePollList({
                      ...item,
                      poll: new_item,
                    })
                  );
                store.dispatch(
                  updateAllPollList({
                    ...item,
                    poll: new_item,
                  })
                );

                return {
                  ...item,
                  updated_at: new Date().toISOString(),

                  poll: new_item,
                };
              } else if (type === "undo_vote") {
                if (item.poll?.end_date)
                  store.dispatch(updatePollList({ ...item, poll: newMessage }));
                store.dispatch(
                  updateAllPollList({ ...item, poll: newMessage })
                );
                return {
                  ...item,
                  poll: newMessage,
                };
              }
            }

            return item;
          });

          return page;
        });
      }
      newData.pages[0].data = newData?.pages[0]?.data?.sort((a, b) => {
        if (!a.created_at && b.created_at) {
          return -1; // `b` comes after `a`
        }
        if (a.created_at && !b.created_at) {
          return 1; // `b` comes before `a`
        }
        if (!a.created_at && !b.created_at) {
          return 0; // `a` and `b` are considered equal
        }
        return new Date(b.created_at) - new Date(a.created_at);
      });

      return {
        ...oldData,
        newData,
      };
    }
  );

  queryClient.setQueryData(
    ["INFO_CANAL", discussion_id, discussion_type],
    (oldData) => {
      if (
        !oldData &&
        type.includes("new_message") &&
        (newMessage.locale || systemMessageTypes.includes(newMessage.type)) &&
        newMessage.receiver_id
      )
        return {
          data: {
            total_important: 0,
            total_favoris: 0,
            total_incomplete_poll: 0,
          },
        };
      else if (!oldData) return oldData;

      let newData = { ...oldData };

      if (type.includes("add_saved")) {
        return {
          ...newData,
          data: {
            ...newData.data,
            total_important: newData.data.total_important + 1,
          },
        };
      } else if (type.includes("remove_saved")) {
        return {
          ...newData,
          data: {
            ...newData.data,
            total_important:
              newData.data.total_important > 0
                ? newData.data.total_important - 1
                : 0,
          },
        };
      } else if (type === "add_favorite") {
        return {
          ...newData,
          data: {
            ...newData.data,
            total_favoris: newData.data.total_favoris + 1,
          },
        };
      } else if (type === "remove_favorite") {
        return {
          ...newData,
          data: {
            ...newData.data,
            total_favoris:
              newData.data.total_favoris > 0
                ? newData.data.total_favoris - 1
                : 0,
          },
        };
      }
    }
  );
  if (
    type === "new_message_poll" ||
    // type === "deleted" ||
    type === "deleted_poll"
  ) {
    queryClient.setQueryData(
      ["INFO_CANAL", discussion_id, discussion_type],
      (oldData) => {
        let newData_poll = oldData;
        if (!newMessage?.poll || !newData_poll)
          return {
            data: {
              total_important: 0,
              total_favoris: 0,
              total_incomplete_poll: 0,
            },
          };
        if (newMessage?.poll.end_date && type === "new_message_poll") {
          return {
            ...newData_poll,
            data: {
              ...newData_poll.data,
              total_incomplete_poll:
                newData_poll.data.total_incomplete_poll + 1,
            },
          };
        } else if (
          (newMessage?.poll.end_date &&
            new Date(newMessage?.poll.end_date).getTime() >
              new Date().getTime()) ||
          //       &&  type === "deleted"

          type === "deleted_poll"
        ) {
          return {
            ...newData_poll,
            data: {
              ...newData_poll.data,
              total_incomplete_poll:
                newData_poll.data.total_incomplete_poll > 0
                  ? newData_poll.data.total_incomplete_poll - 1
                  : 0,
            },
          };
        }

        return newData_poll;
      }
    );
  }
};
