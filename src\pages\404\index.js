import { Button, Result } from "antd";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";

const NotFoundPage = () => {
  const [t] = useTranslation("common");
  useEffect(() => {
    document.title = t("404");
  }, [t]);
  return (
    <Result
      status="404"
      title="404"
      subTitle={t("404Subtitle")}
      extra={
        <Button type="primary" onClick={() => (window.location.href = "/")}>
          {t("BackToHome")}
        </Button>
      }
    />
  );
};
export default NotFoundPage;
