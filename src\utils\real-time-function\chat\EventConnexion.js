import React, { useCallback, useEffect, useRef } from "react";
import { BsWifiOff } from "react-icons/bs";
import {
  setEventMercure,
  setOnlineUser,
} from "new-redux/actions/chat.actions/realTime";
import {
  getConversationApi,
  getLastMessageAPI,
  isConnectedFunc,
  pingPongFunction,
  totalNotification,
} from "new-redux/services/chat.services";
import { moment_timezone } from "App";

import { store } from "new-redux/store";
import { useDispatch, useSelector } from "react-redux";
import MainService from "services/main.service";
import { useTranslation } from "react-i18next";
import { LoadingOutlined } from "@ant-design/icons";
import {
  setRemindersArray,
  setTotalNotificationsNumber,
} from "new-redux/actions/tasks.actions/realTime";
import {
  LATENCE_ADDING_TIME,
  getOrGenerateTabId,
} from "pages/layouts/chat/utils/ConversationUtils";
import { getNewNotifVoip } from "pages/voip/services/services";
import { UPDATE_CURRENT_USER_STATUS_PRESENCE } from "new-redux/constants";
import {
  getRemindersList,
  setCountNotificationVisio,
  setCountReminders,
} from "new-redux/actions/visio.actions/visio";
import {
  setLoadingSideBar,
  setMembersGroupsChatFetched,
} from "new-redux/actions/chat.actions";
import { URL_ENV, queryClient } from "index";
import useEventMercure from "custom-hooks/useEventMercure";
import { closeEventMerucre } from "./function";
import useGetMessage from "pages/layouts/chat/hooks/useGetMessage";
import { LoadingSideBarStatus } from "new-redux/reducers/chatReducer";
import useFocusWindow from "pages/layouts/chat/hooks/useFocusWindow";
import { getMailNotification } from "new-redux/actions/mail.actions";
import useNetwork from "custom-hooks/useNetwork";
import { getDataDashboard } from "new-redux/actions/dashboard.actions";
import dayjs from "dayjs";
import {
  setNotesNotificationsCount,
  setNotesNotificationsList,
} from "new-redux/actions/selfnotes.actions/selfnotes";
import { getLogs } from "new-redux/actions/voip.actions/getLogs";
function EventConnexion({ messageApi, notificationApi, isServerReachable }) {
  const [t] = useTranslation("common");
  const eventMercure = useSelector((state) => state.ChatRealTime.eventMercure);
  const dispatch = useDispatch();
  const { latence, currentUser, loadingSideBar } = useSelector(
    (state) => state.chat
  );
  const { user } = useSelector((state) => state.user);
  const { isOnline } = useNetwork();

  const openNotification = useCallback(
    (title, description, icon, placement) => {
      notificationApi["error"]({
        key: "errorConnexion",
        duration: 0,
        placement,
        message: title,
        description,
        icon,
      });
    },
    [notificationApi]
  );
  // to reconnect each time in case there are probleme
  const timeout = useRef(null);
  // to show reconnect forced
  const interval = useRef(null);
  // to show  notification
  const notificationTiemout = useRef(null);
  // to show the loading
  const latenceNumber = useRef(latence || 1000);
  // var to put the ventSource
  const eventSourceRef = useRef(null);
  //test if there any error in reconnection
  const errorConnectedAPI = useRef(false);

  // reset the eventSource
  const setStatusOffline = useCallback(() => {
    eventSourceRef.current?.close();
    eventSourceRef.current = null;
    closeEventMerucre();
  }, []);
  // get the online users
  /**
   * @function connectedUsers get the online users
   * @returns  Object-  response if there is a response,
   * @returns  Boolean- if there is no response,
   *  @returns String- error if there is an error
   *
   */
  const connectedUsers = useCallback(async () => {
    try {
      const abortController = new AbortController();
      const time = setTimeout(() => {
        abortController.abort();
        clearTimeout(time);
      }, LATENCE_ADDING_TIME * 8);
      const response = await MainService.getOnlineList(abortController.signal);
      if (response && response.status === 200) {
        latenceNumber.current = 0;
        return response.data || {};
      }
    } catch (error) {
      if (
        error.response?.status &&
        (error.response.status === 429 || error.response.status === 401)
      ) {
        return {};
      }
      if (error.response?.status && error?.response?.status === 500) {
        errorConnectedAPI.current = true;
        return "error";
      }

      return false;
    }
  }, []);

  // bloc connection
  const getTasksNotificationsNumber = useCallback(async () => {
    try {
      const response = await MainService.getNumberOfNotifications();
      if (response?.status === 200) {
        dispatch(
          setTotalNotificationsNumber(Number(response?.data?.task_count))
        );
        dispatch(setCountNotificationVisio(response?.data?.visio_count));
        dispatch(setRemindersArray(response?.data?.reminders));
        dispatch(getRemindersList(response?.data?.reminders_visio));
        dispatch(
          setCountReminders(response?.data?.reminders_visio?.meta?.total)
        );
      }
    } catch (error) {
      return;
    }
  }, [dispatch]);

  const getNotesNotificationsNumer = useCallback(async () => {
    try {
      const response = await MainService.getUnreadNotesCount();
      if (response?.status === 200) {
        dispatch(
          setNotesNotificationsCount(response?.data?.data?.notes_unread_count)
        );
        dispatch(setNotesNotificationsList(response?.data?.data?.notes_ids));
      }
    } catch (error) {
      return;
    }
  }, [dispatch]);

  //const { getMessagesData, status } = useGetMessage();

  const connectionMercure = useCallback(
    async () => {
      dispatch(setLoadingSideBar(LoadingSideBarStatus.load));

      clearTimeout(timeout.current);
      setStatusOffline();
      const mercureURL = new URL(URL_ENV?.REACT_APP_URL_MERCURE);

      mercureURL.searchParams.append(
        "topic",
        process.env.REACT_APP_MERCURE_TOPIC + currentUser?._id
      );
      mercureURL.searchParams.append(
        "topic",
        process.env.REACT_APP_MERCURE_TOPIC_TASKS + currentUser?.uuid
      );
      mercureURL.searchParams.set("authorization", currentUser?.jwt_mercure);

      eventSourceRef.current = new EventSource(mercureURL);
      const url = new URL(window.location.href);

      eventSourceRef.current.onopen = async () => {
        try {
          const response =
            currentUser?.status === 2 ? {} : await connectedUsers();

          if (response === "error") {
            clearInterval(interval.current);
            clearTimeout(timeout.current);
            clearTimeout(notificationTiemout.current);
            return;
          }

          if (response === false) {
            if (latenceNumber.current === 0) clearTimeout(timeout.current);

            latenceNumber.current = latenceNumber.current + 1000;

            timeout.current = setTimeout(() => {
              setStatusOffline();
            }, latence - 2500 + latenceNumber.current);
          } else {
            clearInterval(interval.current);
            clearTimeout(timeout.current);
            latenceNumber.current = 0;
            errorConnectedAPI.current = false;
            if (
              Object.values(response)?.length > 0 ||
              currentUser?.status === 2
            ) {
              dispatch(setEventMercure(eventSourceRef.current));
              clearTimeout(notificationTiemout.current);
            }

            dispatch({
              type: UPDATE_CURRENT_USER_STATUS_PRESENCE,
              payload: response[currentUser?.uuid] || "offline",
            });

            dispatch(setOnlineUser(response));

            if (currentUser?.status === 1) {
              // // Declare IP address when reconnecting For IPBX and SIP.js
              // dispatch(getIpAndDeclareIt(user?.extension, param, currentIP));
              dispatch(getLogs(false, null, null, false));
              dispatch(setMembersGroupsChatFetched(false));
              const responseConversation = await dispatch(
                getConversationApi({ errorText: t("toasts.errorFetchApi") })
              );
              const selectedConversation = await store.getState().ChatRealTime
                .selectedConversation;
              const filteredQueries = queryClient
                .getQueryCache()
                .findAll()
                .filter((query) => {
                  const queryKey = query.queryKey;
                  return !(
                    queryKey.includes(selectedConversation?.id) &&
                    queryKey.includes(selectedConversation?.type)
                  );
                });
              filteredQueries.length > 0 &&
                filteredQueries.forEach((query) => {
                  queryClient.removeQueries(query.queryKey);
                });
              dispatch(totalNotification());
              const getMessagesData = await queryClient.getQueryData([
                "getMessages",
                selectedConversation?.id,
                selectedConversation?.type,
              ]);
              const state = await queryClient.getQueryState([
                "getMessages",
                selectedConversation?.id,
                selectedConversation?.type,
              ]);

              if (selectedConversation?.id && url.pathname === "/chat") {
                const lastMessageDate = getMessagesData?.pages[0]?.data[0];

                const message_id = lastMessageDate?._id;
                const conversationFromAPI = responseConversation?.find(
                  (item) => item._id === selectedConversation?.conversationId
                );
                const mustGetLastMessage = !responseConversation
                  ? false
                  : moment_timezone(
                      conversationFromAPI?.last_message_date
                    ).isAfter(
                      moment_timezone(lastMessageDate?.updated_at).add(
                        1,
                        "seconds"
                      )
                    ) ||
                    lastMessageDate?.unread !==
                      conversationFromAPI?.last_message?.unread;
                // if (conversationFromAPI?.total_unread > 0) {
                //   dispatch(
                //     scrollToBottom(Math.floor(Math.random() * 1000000 + 1))
                //   );
                // }

                if (
                  mustGetLastMessage &&
                  message_id &&
                  state?.status === "success" &&
                  state?.fetchStatus === "idle"
                ) {
                  dispatch(
                    getLastMessageAPI({
                      refetch: false,

                      type: selectedConversation?.type,
                      id: selectedConversation?.id,
                      message_id,
                      errorText1: t(
                        "chat.message_system.error_fetch_new_messages"
                      ),
                      errorText2: t("toasts.somethingWrong"),
                    })
                  );
                }
              }
            }

            dispatch(setLoadingSideBar(LoadingSideBarStatus.idle));

            Promise.all([
              getTasksNotificationsNumber(),
              getNotesNotificationsNumer(),
              getNewNotifVoip(dispatch),
              dispatch(getMailNotification()),
              dispatch(pingPongFunction()),
            ]);

            return;
          }
        } catch (error) {
          console.error("ERROR_CONNECTION_MERCURE", error);
          // setStatusOffline();

          dispatch(setLoadingSideBar(LoadingSideBarStatus.idle));
        }

        return;
      };
      eventSourceRef.current.onerror = () => {
        clearTimeout(timeout.current);
        latenceNumber.current += 500;

        setStatusOffline();
      };
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      dispatch,
      connectedUsers,
      latence,
      currentUser?._id,
      currentUser?.status,
      t,
      getTasksNotificationsNumber,
      setStatusOffline,
    ]
  );

  // to manage the notification of the connection
  useEffect(() => {
    if (isOnline && eventMercure?.readyState === 1) {
      notificationApi.destroy("errorConnexion");
      messageApi.destroy();
    } else if (isOnline && !eventMercure?.readyState) {
      messageApi.destroy();
      interval.current = setInterval(
        async () => {
          const eventMercureStore = await store.getState().ChatRealTime
            .eventMercure;
          if (
            (!eventSourceRef.current || !eventMercureStore) &&
            !errorConnectedAPI.current &&
            currentUser?._id
          ) {
            connectionMercure(true);
          }
        },

        latence + LATENCE_ADDING_TIME * 2 < 11000
          ? 11000
          : latence + LATENCE_ADDING_TIME * 2
      );

      notificationTiemout.current = setTimeout(
        async () => {
          const eventMercureStore = await store.getState().ChatRealTime
            .eventMercure;
          if ((!eventSourceRef.current || !eventMercureStore) && isOnline) {
            openNotification(
              t("toasts.re-connection"),
              t("toasts.mercureConnectionDescription"),
              <LoadingOutlined className="  animate-spin text-blue-400 " />,
              "topRight"
            );
          } else clearTimeout(notificationTiemout.current);
        },
        latence + LATENCE_ADDING_TIME * 2 < 11000
          ? 11000
          : latence + LATENCE_ADDING_TIME * 2
      );
    } else if (!isOnline) {
      notificationApi.destroy("errorConnexion");
      messageApi.loading({
        key: "offline",
        duration: 0,

        content: (
          <div className="px-auto fixed inset-x-[40%] top-0 z-[99999] mt-0.5 inline-flex w-full max-w-md flex-1  items-center  justify-center rounded-md bg-red-50 p-4 text-base shadow-sm ring-1 ring-red-400 ">
            <div className="flex-shrink-0">
              <BsWifiOff className=" animate-pulse  text-red-700 " />
            </div>
            <div className="ml-3">
              <p className="m-0  font-medium text-red-700">
                {t("toasts.networkErrorDescription") + ".."}{" "}
              </p>
            </div>
          </div>
        ),
      });
    } else clearInterval(interval.current);

    return () => clearInterval(interval.current);
  }, [
    connectionMercure,
    notificationApi,
    isOnline,
    openNotification,
    t,
    dispatch,
    eventMercure,
    messageApi,
    isServerReachable,
    latence,
    currentUser?._id,
  ]);
  // to connect the user

  useEffect(() => {
    let mounted = true;
    if (
      eventMercure?.readyState !== 1 &&
      mounted &&
      isServerReachable &&
      isOnline &&
      currentUser?._id
    ) {
      mounted = false;
      connectionMercure(true);
    }

    return () => {
      mounted = false;
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [eventMercure?.readyState, isServerReachable, currentUser?._id, isOnline]);
  // to check if the user is online or offline

  const handleBlurVisibility = async () => {
    let closeEvent = false;

    try {
      const currentUser = await store.getState().chat.currentUser;
      const isOnlineStatus = currentUser?.online !== "offline";
      if (
        ((!timeBlured || timeBlured < 10) && isOnlineStatus) ||
        !isOnline ||
        loadingSideBar === LoadingSideBarStatus.load
      )
        return;

      const currentTimeInSeconds = Math.floor(Date.now() / 1000) + 1;
      const timeDifference = currentTimeInSeconds - timeBlured;
      if (isOnline && (timeDifference > 35 || !isOnlineStatus)) {
        // to check if the user is connected in mercure
        const isDisconnected = await dispatch(
          isConnectedFunc(currentUser?._id)
        );
        //refresh dashboard
        window.location.pathname.includes("dashboard") &&
          dispatch(
            getDataDashboard(
              dayjs().format(user?.location?.date_format),
              dayjs().format(user?.location?.date_format)
            )
          );
        closeEvent = !isDisconnected;
      }

      setTimeBlured(0);
    } catch (error) {
      closeEvent = true;
      console.log("error", error);
    } finally {
      if (closeEvent) {
        connectionMercure(true);
      }
    }
  };

  const { timeBlured, setTimeBlured } = useFocusWindow({
    callback: handleBlurVisibility,
  });
  useEventMercure();

  // to set the tab_id
  useEffect(() => {
    const removeSessionStorage = () => sessionStorage.removeItem("tab_id");
    window.addEventListener("beforeunload", removeSessionStorage, false);

    if (!sessionStorage.getItem("tab_id")) {
      getOrGenerateTabId();
    } else removeSessionStorage();
    return () =>
      window.removeEventListener("beforeunload", removeSessionStorage, false);
  }, []);
  return <></>;
}

export default EventConnexion;
