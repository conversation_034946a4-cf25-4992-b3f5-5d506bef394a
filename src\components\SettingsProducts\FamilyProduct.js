import React, { useEffect, useRef } from "react";
import { Form, Input, Button, Select, Space } from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { PlusCircleOutlined } from "@ant-design/icons";
import { generateAxios } from "../../services/axiosInstance";
import { toastNotification } from "../ToastNotification";
import Header from "../configurationHelpDesk/Header";
import { useDispatch, useSelector } from "react-redux";
import NewTableDraggable from "../NewTableDraggable";
import { setSearch } from "../../new-redux/actions/menu.actions/menu";
import LabelTable from "../LabelTable";
import BottomButtonAddRow from "../BottomButtonAddRow";
import { SubmitKeyPress } from "../../utils/SubmitKeyPress";
import { URL_ENV } from "index";

const FamilyProduct = () => {
  const [form] = Form.useForm();
  const [count, setCount] = useState(0);
  const [data, setData] = useState([]);
  const [editingKey, setEditingKey] = useState("");
  const [id, setId] = useState(null);
  const [loading, setLoading] = useState(true);
  const isEditing = (record) => record.key === editingKey;
  const [t] = useTranslation("common");
  const [types, setTypes] = useState([]);
  const dispatch = useDispatch();
  const { search } = useSelector((state) => state.form);
  const inputRefs = useRef([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [oldPage, setOldPage] = useState(1);
  const [allData, setAllData] = useState([]);
  const [sorter, setSorter] = useState({
    field: null,
    order: null,
  });
  const [filter, setFilter] = useState({});
  useEffect(() => {
    inputRefs.current.forEach((input) => {
      input?.focus();
    });
  }, [data.length, id]);
  // useEffect(() => {
  //   if (editingKey && oldPage !== currentPage) {
  //     setData((prev) => prev.filter((el) => el.id));
  //     // setEditingKey("");
  //   }
  // }, [currentPage, editingKey, oldPage]);
  const onFinishFailed = (values) => {
    console.log(values);
  };
  const handleTableChange = (pagination, filters, sorter) => {
    setSorter({
      field: sorter.field,
      order: sorter.order,
    });
    setFilter(filters);
  };

  const EditableCell = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    const inputNode =
      inputType === "select" ? (
        <Select
          placeholder="Types"
          options={types}
          defaultValue={null}
          allowClear
        />
      ) : (
        <Input
          ref={(el) => (inputRefs.current[index] = el)}
          onKeyPress={handleKeyPress}
          placeholder={t("activities.name")}
        />
      );

    return (
      <td {...restProps}>
        {editing ? (
          <Form.Item
            name={dataIndex.toLowerCase()}
            style={{
              margin: 0,
            }}
            rules={[
              {
                required: true,
                message: `${title}
                ${t("table.header.isrequired")}`,
              },
            ]}>
            {inputNode}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };
  const edit = (record) => {
    if (record && currentPage) {
      form.setFieldsValue({
        label: record.label,
        typefamilyproduct_id: record.typefamilyproduct_id,
      });
      setId(record.id);

      inputRefs.current[1]?.input.focus();
    } else {
      form.setFieldsValue({
        label: "",
        typefamilyproduct_id: null,
      });
    }
    setEditingKey(record.key);
  };
  const cancel = (record) => {
    setEditingKey("");
    setId(null);
    if (!record.id) {
      setData(data.filter((item) => item.key !== record.key));
    }
    form.setFieldsValue({
      label: "",
      typefamilyproduct_id: null,
    });
  };

  const sortData = (data, sortField, sortOrder) => {
    // Trier les données en fonction de la colonne sélectionnée par l'utilisateur
    let compareFn;
    if (sortField === "typefamilyproduct_id")
      compareFn = (a, b) =>
        sortOrder === "ascend"
          ? a[sortField] - b[sortField]
          : sortOrder === "descend"
          ? b[sortField] - a[sortField]
          : data;
    else
      compareFn = (a, b) =>
        sortOrder === "ascend"
          ? a[sortField].localeCompare(b[sortField])
          : sortOrder === "descend"
          ? b[sortField].localeCompare(a[sortField])
          : data;
    return data.filter((el) => el.label).sort(compareFn);
  };
  const save = async (key) => {
    setLoading(true);
    if (id) {
      try {
        const row = await form.validateFields();
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post(`/family-products/update/${id}`, row);
        setEditingKey("");
        setData(
          data.map((el) =>
            el.id === res.data.data.id
              ? {
                  ...res.data.data,
                  key: res.data.data.id,
                }
              : el
          )
        );
        setAllData(
          data.map((el) =>
            el.id === res.data.data.id
              ? {
                  ...res.data.data,
                  key: res.data.data.id,
                }
              : el
          )
        );

        form.setFieldsValue({
          label: "",
          typefamilyproduct_id: "",
        });
        setLoading(false);
        toastNotification("success", row.label + t("toasts.edit"), "topRight");
      } catch (errInfo) {
        setLoading(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    } else {
      try {
        const row = await form.validateFields();
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post("/family-products", row);
        setEditingKey("");
        setData([
          ...data.filter((el) => el.id),
          { ...res.data.data, key: res.data.data.id },
        ]);
        setAllData([
          ...data.filter((el) => el.id),
          { ...res.data.data, key: res.data.data.id },
        ]);
        form.setFieldsValue({
          label: "",
          typefamilyproduct_id: "",
        });
        setLoading(false);
        toastNotification(
          "success",
          row.label + t("toasts.created"),
          "topRight"
        );
      } catch (errInfo) {
        setLoading(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    }
  };
  useEffect(() => {
    const getProducts = async () => {
      setLoading(true);
      try {
        const {
          data: { data },
        } = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get("/family-products");
        if (data) {
          const res = await generateAxios(
            URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
          ).get("/type-family-products");
          setTypes(
            res.data.data.map((el) => ({
              label: el.label,
              value: el.id,
            }))
          );
        }
        setData(data.map((el, i) => ({ ...el, key: el.id, rank: i + 1 })));
        setAllData(data.map((el, i) => ({ ...el, key: el.id, rank: i + 1 })));

        if (data.length > 0) {
          setCount(Math.max(...data.map((el) => el.id)));
        }
        setLoading(false);
      } catch (err) {
        setLoading(false);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    };
    getProducts();
    return () => {
      dispatch(setSearch(""));
    };
  }, []);
  useEffect(() => {
    setData(
      allData.filter((object) => {
        if (
          filter.typefamilyproduct_id &&
          filter.typefamilyproduct_id.length > 0
        ) {
          return filter.typefamilyproduct_id.includes(
            object.typefamilyproduct_id.toString()
          );
        } else {
          return object;
        }
      })
    );
  }, [sorter, filter, allData]);
  const handleKeyPress = (event) => {
    SubmitKeyPress(event, form);
  };
  const handleClick = (event) => {
    event.stopPropagation();
  };
  const columns = [
    {
      title: t("activities.name"),
      dataIndex: "label",
      key: "label",
      editable: true,
      sorter: (a, b) => a.label.localeCompare(b.label),
      render: (_, record) => {
        return (
          <LabelTable record={record} editingKey={editingKey} edit={edit} />
        );
      },
    },
    {
      title: "Type",
      dataIndex: "typefamilyproduct_id",
      key: "typefamilyproduct_id",
      editable: true,
      filters: types.map((el) => ({
        text: el.label,
        value: el.value.toString(),
      })),

      onFilter: (value, record) => record.typefamilyproduct_id == value,
      sorter: (a, b) =>
        types
          .find((el) => el.value === a.typefamilyproduct_id)
          .label.localeCompare(
            types.find((el) => el.value === b.typefamilyproduct_id).label
          ),
      render: (_, record) => (
        <>
          {record.typefamilyproduct_id
            ? types.find((el) => el.value === record.typefamilyproduct_id)
                ?.label
            : ""}
        </>
      ),
    },
  ];
  const handleAdd = () => {
    const ids = data.map((object) => {
      return object.id;
    });
    setId(null);
    const newData = {
      key: Math.max(...ids) + 1,
      label: `  `,
      typefamilyproduct_id: null,
      disabled: true,
    };
    setCurrentPage(Math.ceil([...data, newData].length / pageSize));
    setOldPage(Math.ceil([...data, newData].length / pageSize));
    setData([...data, newData]);
    form.setFieldsValue({
      label: "",
    });
    setEditingKey(Math.max(...ids) + 1);
    setCount(Math.max(...ids) + 1);
  };
  const onRow = () => {};
  const sortedData = sortData(data, sorter.field, sorter.order);

  const filteredData = sortedData.filter((item) => {
    return (
      item.label?.toLowerCase().includes(search.toLowerCase()) ||
      types
        ?.find((el) => el.value == item.typefamilyproduct_id)
        ?.label?.toLowerCase()
        .includes(search.toLowerCase())
    );
  });
  return (
    <Space direction="vertical" style={{ width: "100%", marginTop: "16px" }}>
      <div className="pt-4">
        <Header
          active={"4"}
          editingKey={editingKey}
          handleAdd={handleAdd}
          btnText={t("familyProduct.addFamily")}
          disabled={
            loading
              ? true
              : editingKey
              ? true
              : search
              ? true
              : filter.typefamilyproduct_id &&
                filter.typefamilyproduct_id.length > 0
              ? true
              : false
          }
        />
      </div>
      <NewTableDraggable
        columns={columns}
        setLoading={setLoading}
        isEditing={isEditing}
        data={filteredData}
        onRow={onRow}
        setData={setData}
        loading={loading}
        save={save}
        edit={edit}
        EditableCell={EditableCell}
        onFinishFailed={onFinishFailed}
        cancel={cancel}
        form={form}
        apiRank="/rank-products"
        editingKey={editingKey}
        api="family-products"
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        pageSize={pageSize}
        setPageSize={setPageSize}
        handleTableChange={handleTableChange}
        setAllData={setAllData}
      />
      <BottomButtonAddRow
        editingKey={editingKey}
        data={filteredData}
        text={t("familyProduct.addFamily")}
        handleAdd={handleAdd}
        loading={loading}
        search={
          search ||
          (filter.typefamilyproduct_id &&
            filter.typefamilyproduct_id.length > 0) ||
          ""
        }
      />
    </Space>
  );
};
export default FamilyProduct;
