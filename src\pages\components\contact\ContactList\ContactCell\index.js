import React from "react";
import {Avatar, Checkbox, Dropdown, Menu} from "antd";

import AddContact from "../../AddContact/index";

const options = [
  'Edit',
  'Delete',
];

class ContactCell extends React.Component {

  onContactClose = () => {
    this.setState({addContactState: false});
  };
  onDeleteContact = (contact) => {
    this.setState({addContactState: false});
    this.props.onDeleteContact(contact);
  };
  onEditContact = () => {
    this.setState({addContactState: true});
  };
  menus = () => (<Menu onClick={(e) => {
    if (e.key === 'Edit') {
      this.onEditContact()
    } else {
      this.onDeleteContact(this.props.contact)
    }
  }
  }>
    {options.map(option =>
      <Menu.Item key={option}>
        {option}
      </Menu.Item>,
    )}
  </Menu>);

  constructor() {
    super();
    this.state = {
      addContactState: false,
    }
  }

  render() {
    const {contact, addFavourite, onContactSelect, onSaveContact} = this.props;
    const {addContactState} = this.state;
    const {name, thumb, email, phone, designation, starred ,edit} = contact;

    return (

      <div className="gx-contact-item">
        <div className="gx-module-list-icon">
          <div className="gx-d-none gx-d-sm-flex" style={{padding:"0 10px"}}>
          </div>
          <div className="gx-ml-2 gx-d-none gx-d-sm-flex">

              <Avatar size="large" style={{backgroundColor:"#50b3eb"}}>
                {name.charAt(0).toUpperCase()}
              </Avatar>
       
          </div>
        </div>

        <div className="gx-module-list-info gx-contact-list-info">
          <div className="gx-module-contact-content">
            <p className="gx-mb-1">
              <span className="gx-text-truncate gx-contact-name">{name}</span>
              <span className="gx-toolbar-separator">&nbsp;</span>
              <span className="gx-text-truncate gx-job-title" style={{backgroundColor:"#50b3eb40",padding:"2px 17px",borderRadius:"6px"}}>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-5 h-5" style={{width:"11px",marginRight:"4px"}}>
              <path fillRule="evenodd" d="M2 3.5A1.5 1.5 0 013.5 2h1.148a1.5 1.5 0 011.465 1.175l.716 3.223a1.5 1.5 0 01-1.052 1.767l-.933.267c-.41.117-.643.555-.48.95a11.542 11.542 0 006.254 6.254c.395.163.833-.07.95-.48l.267-.933a1.5 1.5 0 011.767-1.052l3.223.716A1.5 1.5 0 0118 15.352V16.5a1.5 1.5 0 01-1.5 1.5H15c-1.149 0-2.263-.15-3.326-.43A13.022 13.022 0 012.43 8.326 13.019 13.019 0 012 5V3.5z" clipRule="evenodd" />
              </svg>
                {phone}
                </span>
              <span className="gx-toolbar-separator">&nbsp;</span>
              {
                email !== "" && email !== " "?
              <span className="gx-email gx-d-inline-block gx-mr-2" style={{backgroundColor: "#50b3eb40",padding:"2px 17px",borderRadius:"6px"}}>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-5 h-5" style={{width:"11px",marginRight:"4px"}}>
                <path d="M3 4a2 2 0 00-2 2v1.161l8.441 4.221a1.25 1.25 0 001.118 0L19 7.162V6a2 2 0 00-2-2H3z" />
                <path d="M19 8.839l-7.77 3.885a2.75 2.75 0 01-2.46 0L1 8.839V14a2 2 0 002 2h14a2 2 0 002-2V8.839z" />
                </svg>
                {email}</span>
              :
              null 
            } 
                   <span className="gx-toolbar-separator">&nbsp;</span>
              {
                designation !== "" && designation !== " "?
              <span className="gx-email gx-d-inline-block gx-mr-2" style={{backgroundColor: "#50b3eb40",padding:"2px 17px",borderRadius:"6px"}}>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-5 h-5" style={{width:"11px",marginRight:"4px"}}>
              <path fillRule="evenodd" d="M9.69 18.933l.003.001C9.89 19.02 10 19 10 19s.11.02.308-.066l.002-.001.006-.003.018-.008a5.741 5.741 0 00.281-.14c.186-.096.446-.24.757-.433.62-.384 1.445-.966 2.274-1.765C15.302 14.988 17 12.493 17 9A7 7 0 103 9c0 3.492 1.698 5.988 3.355 7.584a13.731 13.731 0 002.273 1.765 11.842 11.842 0 00.976.544l.***************.006.003zM10 11.25a2.25 2.25 0 100-4.5 2.25 2.25 0 000 4.5z" clipRule="evenodd" />
              </svg>

                {designation}</span>
              :
              null 
            }
            </p>

            {/* <div className="gx-text-muted">
            <span className="gx-email gx-d-inline-block gx-mr-2">{email}</span>
              <span className="gx-phone gx-d-inline-block">{designation}</span>
            </div> */}
          </div>

          <div className="gx-module-contact-right">
            {
              edit ?
              
            <Dropdown overlay={this.menus()} placement="bottomRight" trigger={['click']}>
              <i className="gx-icon-btn icon icon-ellipse-v"/>
            </Dropdown>
            :
            null
             }

            {addContactState &&
            <AddContact open={addContactState} contact={contact} onSaveContact={onSaveContact}
                        onContactClose={this.onContactClose} onDeleteContact={this.onDeleteContact}/>}
          </div>
        </div>
      </div>
    )
  }
}

export default ContactCell;
