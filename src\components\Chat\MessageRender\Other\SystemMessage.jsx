import {
  CheckCircleOutlined,
  CopyOutlined,
  StopOutlined,
} from "@ant-design/icons";
import { moment_timezone } from "App";
import {
  Button,
  Divider,
  Select,
  Tag,
  Tooltip,
  Typography,
  message,
} from "antd";
import { URL_ENV } from "index";
import {
  convertToPlain,
  getUserFromMsg,
  getName,
} from "pages/layouts/chat/utils/ConversationUtils";
import React, { useCallback, useState } from "react";
import { useTranslation } from "react-i18next";
import { HiOutlineCalendar, HiOutlineVideoCamera } from "react-icons/hi2";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import { getTokenRoom } from "new-redux/actions/visio.actions/createVisio";
import { HiPhoneIncoming, HiPhoneOutgoing } from "react-icons/hi";
import { MdPhoneMissed } from "react-icons/md";
import { UUID_REGEX } from "utils/regex";

const { Text, Title } = Typography;
let users_ids = [];

function SystemMessage({ item }) {
  const currentUser = useSelector((state) => state.chat.currentUser);

  const dispatch = useDispatch();
  const { t } = useTranslation("common");
  const { user } = useSelector((state) => state.user);
  const [loadingVisio, setLoadingVisio] = useState(false);
  const [isCopied, setCopied] = useState(false);

  const advancedTextToCopy = useCallback(
    (item) => {
      let url = new URL(URL_ENV?.REACT_APP_DOMAIN);
      const visioPart = item?.message?.split(" / ");
      url.searchParams.set("room_visio_name", visioPart[0]);
      const phoneNumbers = item?.message?.split(" / ")[3]?.split(",");
      const pinCode = item?.message?.split(" / ")[4];
      return `
        <p>${t(
          "visio.followVisioLinkClipboard"
        )}: <u><a href="${url}" target="_blank" rel="noopener noreferrer">${url}</a></u>&nbsp;</p><p>${t(
        "visio.followVisioInPhoneClipboardPartOne"
      )} <strong>${phoneNumbers[0]}</strong> ${t(
        "visio.followVisioInPhoneClipboardPartTwo"
      )}: <strong>${pinCode}</strong></p>
        ${
          phoneNumbers?.length > 1
            ? `<p>${t("visio.morePhoneClipboard")}: ${phoneNumbers?.map(
                (number, index) =>
                  `<span key={phoneList-${index}}> ${number}</span>`
              )}</p>`
            : ""
        }
      `;
    },
    [t]
  );

  const handleCopy = useCallback(
    async (item) => {
      if (isCopied) return;
      const copiedText = advancedTextToCopy(item);
      const blobHtml = new Blob([copiedText], { type: "text/html" });
      const blobText = new Blob([convertToPlain(copiedText)], {
        type: "text/plain",
      });
      const data = [
        new ClipboardItem({
          "text/plain": blobText,
          "text/html": blobHtml,
        }),
      ];

      navigator.clipboard.write(data).then(() => {
        setCopied(true);
        message.success(t("chat.bot.copied"));
      });
      setTimeout(() => setCopied(false), 3000);
    },
    [isCopied, t]
  );
  switch (item?.type) {
    case "message_system_add_user_room": {
      users_ids = item?.message?.toString()?.split(",");
      return (
        //data.message contain the user id
        <div className="flex w-full  flex-col space-y-1  ">
          {users_ids?.map((el, key) => (
            <React.Fragment key={`message_add_${key}`}>
              {el !== currentUser?._id ? (
                <Text type="secondary">
                  <b className="mr-1">
                    {getName(
                      getUserFromMsg(item?.sender_id)?.name ?? "",
                      "name"
                    )}
                  </b>
                  {t("chat.message_system.add_membre")}
                  <b className="ml-1">
                    {getName(getUserFromMsg(el)?.name, "name")}
                  </b>
                </Text>
              ) : (
                <Text type="secondary">
                  <b className="mr-1">
                    {getName(
                      getUserFromMsg(item?.sender_id)?.name ?? "",
                      "name"
                    )}
                  </b>
                  {t("chat.message_system.add_auth_user_membre")}
                </Text>
              )}
            </React.Fragment>
          ))}
        </div>
      );
    }
    case "message_system_remove_user_room":
      users_ids = item?.message?.toString()?.split(",") ?? [];

      return users_ids.map((el, index) => {
        const isUUID = UUID_REGEX.test(el);
        const key = isUUID ? "uuid" : "_id";
        return (
          <React.Fragment key={`message_remove_key${index}`}>
            {el !== currentUser?.[key] ? (
              <Text type="secondary" className="flex items-center">
                <b className="mr-1">
                  {getName(getUserFromMsg(item?.sender_id)?.name ?? "", "name")}
                </b>
                {t("chat.message_system.remove_membre")}
                <b className="ml-1">
                  {getName(getUserFromMsg(el)?.name, "name")}
                </b>
              </Text>
            ) : (
              <Text type="secondary" className="flex items-center ">
                <b className="mr-1">
                  {getName(getUserFromMsg(item?.sender_id)?.name ?? "", "name")}
                </b>
                {t("chat.message_system.removed_auth_user")}
              </Text>
            )}
          </React.Fragment>
        );
      });
    case "message_system_update_room":
      return (
        <Text type="secondary" className="flex items-center ">
          <b className="mr-1">
            {getName(getUserFromMsg(item?.sender_id)?.name ?? "", "name")}
          </b>
          {t("chat.message_system.update_general_group")}
        </Text>
      );
    case "message_system_leave_user_room": {
      return (
        <Text type="secondary" className="flex items-center ">
          <b className="mr-1">
            {getName(getUserFromMsg(item?.sender_id)?.name ?? "", "name")}
          </b>
          {t("chat.message_system.leave_group")}.
        </Text>
      );
    }
    case "message_task": {
      return (
        <Text type="secondary" className="flex items-center ">
          <HiOutlineCalendar
            className="mr-1 h-3 w-3  text-pink-500"
            style={{ fontSize: "21px" }}
          />
          {item?.message.split("_")[0] === "create" ? (
            item.sender_id !== currentUser?._id ? (
              <Text type="secondary" className="flex items-center">
                <b className="mr-1">
                  {getName(getUserFromMsg(item?.sender_id)?.name ?? "", "name")}
                </b>
                {t("chat.message_system.task_created")}
              </Text>
            ) : (
              <Text type="secondary" className="flex items-center ">
                <b className="mr-1">{t("chat.you")}</b>
                {t("chat.message_system.task_created_auth")}
              </Text>
            )
          ) : item.sender_id !== currentUser?._id ? (
            <Text type="secondary" className="flex items-center">
              <b className="mr-1">
                {getName(getUserFromMsg(item?.sender_id)?.name ?? "", "name")}
              </b>
              {t("chat.message_system.task_updated_stage")}
            </Text>
          ) : (
            <Text type="secondary" className="flex items-center ">
              <b className="mr-1">{t("chat.you")}</b>
              {t("chat.message_system.task_updated_stage_auth")}
            </Text>
          )}
          <b className="ml-1"> {item?.message.split("_")[1]}</b>
        </Text>
      );
    }
    case "message_visio_conf": {
      let url = new URL(URL_ENV?.REACT_APP_DOMAIN);
      const visioPart = item?.message?.split(" / ");
      url.searchParams.set("room_visio_name", visioPart[0]);
      const phoneNumbers = item?.message?.split(" / ")[3]?.split(",");
      const pinCode = item?.message?.split(" / ")[4];
      const isVisioCanceled = item?.message?.split(" / ")?.length > 5;
      return (
        <div className="my-2 w-[98%] rounded-md border-solid border-[#F2F2F2] p-3">
          <div className="flex flex-row justify-between">
            <div className="flex items-center  ">
              <div className="flex h-10 w-10 items-center justify-center rounded-md bg-blue-600 text-white">
                <HiOutlineVideoCamera style={{ fontSize: "130%" }} />
              </div>
              <div className="ml-2 flex flex-col">
                <div className="flex items-center space-x-4">
                  <Title level={5} className="flex items-center">
                    {t("chat.header.visio.title2")}
                  </Title>
                  {visioPart[2] && (
                    <Text italic type="secondary">
                      {t("chat.header.visio.timeToStart", {
                        time1: moment_timezone(visioPart[2]).fromNow(),
                        time2: moment_timezone(visioPart[2]).format("HH:mm"),
                      })}
                    </Text>
                  )}
                </div>
                <Text italic type="secondary">
                  {t("chat.header.visio.created_by") +
                    " " +
                    getName(
                      getUserFromMsg(item?.sender_id)?.name ?? "",
                      "name"
                    )}
                </Text>
              </div>
            </div>
            {isVisioCanceled && (
              <Tag
                icon={<StopOutlined />}
                color="error"
                className="relative h-[100%]"
              >
                {t("visio.canceledVisio")}
              </Tag>
            )}
          </div>
          {!isVisioCanceled && (
            <>
              <Divider className="my-2" />
              <div className="flex items-center justify-between space-x-0.5 ">
                <div className="flex flex-col justify-between ">
                  <div className="flex flex-row">
                    <Text italic type="secondary">
                      {t("chat.header.visio.roomName")} :
                    </Text>
                    <div className="ml-0.5 flex items-center">
                      <a target="_blank" href={url} rel="noreferrer">
                        {visioPart[1]}
                      </a>
                    </div>
                  </div>
                  <div className="flex flex-row items-center">
                    <Text italic type="secondary">
                      {t("visio.callOn")} :
                    </Text>
                    <Select
                      className="phones-select"
                      defaultValue={phoneNumbers && phoneNumbers[0]}
                      bordered={false}
                      popupMatchSelectWidth={false}
                      options={phoneNumbers?.map((phone) => ({
                        label: phone,
                        value: phone,
                      }))}
                    />
                  </div>
                  <div className="flex flex-row">
                    <Text italic type="secondary">
                      Code :
                    </Text>
                    <div className="ml-0.5 flex items-center">
                      <p>{pinCode}</p>
                    </div>
                  </div>
                </div>

                <Tooltip
                  title={!isCopied ? t("chat.bot.copy") : t("chat.bot.copied")}
                >
                  <Button
                    shape="circle"
                    type="link"
                    onClick={() => handleCopy(item)}
                    icon={
                      !isCopied ? (
                        <CopyOutlined className=" m-0 " />
                      ) : (
                        <CheckCircleOutlined className=" m-0 " />
                      )
                    }
                  />
                </Tooltip>
                <Button
                  disabled={
                    loadingVisio ||
                    (user?.access && user?.access["visio"] === "0")
                  }
                  loading={loadingVisio}
                  onClick={async () => {
                    setLoadingVisio(true);
                    const api = await dispatch(
                      getTokenRoom({
                        room: visioPart[0],
                        errorText1: t("toasts.errorFetchApi"),
                        errorText2: t("toasts.errorRoomNotFound"),
                      })
                    );
                    if (api) setLoadingVisio(false);
                  }}
                  shape="default"
                  type="primary"
                >
                  {t("chat.header.visio.join")}
                </Button>
              </div>
            </>
          )}
        </div>
      );
    }
    case "message_missed_call":
      return (
        <div className="flex w-full  items-center justify-center ">
          {item.sender_id === currentUser?._id ? (
            <>
              <HiPhoneOutgoing className="mr-1 h-3.5 w-3.5  fill-gray-500" />

              <Text type="secondary">
                {t("chat.message_system.outgoing_call") +
                  " " +
                  t("chat.edit.at")}
                {" " + moment_timezone(item?.created_at).format("LT")}
              </Text>
            </>
          ) : (
            <>
              <MdPhoneMissed className="mr-1 h-3.5 w-3.5  fill-red-500" />

              <Text type="secondary">
                {t("chat.message_system.missed_call") + " " + t("chat.edit.at")}
                {" " + moment_timezone(item?.created_at).format("LT")}
              </Text>
            </>
          )}
        </div>
      );
    case "message_received_call":
      return (
        <div className="flex w-full  items-center justify-center ">
          {item.sender_id === currentUser?._id ? (
            <>
              <HiPhoneOutgoing className="mr-1 h-3.5 w-3.5  fill-blue-500" />

              <Text type="secondary">
                {t("chat.message_system.outgoing_call") +
                  " " +
                  t("chat.edit.at")}
                {" " +
                  moment_timezone(item?.created_at).format("LT") +
                  " " +
                  t("chat.message_system.duration") +
                  item?.message}
              </Text>
            </>
          ) : (
            <>
              <HiPhoneIncoming className="mr-1 h-3.5 w-3.5  fill-green-500" />

              <Text type="secondary">
                {t("chat.message_system.incoming_call") +
                  " " +
                  t("chat.edit.at")}
                {" " +
                  moment_timezone(item?.created_at).format("LT") +
                  " " +
                  t("chat.message_system.duration") +
                  item?.message}
              </Text>
            </>
          )}
        </div>
      );

    default:
      return <></>;
  }
}

export default SystemMessage;
