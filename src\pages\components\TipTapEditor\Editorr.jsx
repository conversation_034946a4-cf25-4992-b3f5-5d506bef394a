// import './styles.scss';
import React, { useCallback, useEffect, useRef, useState } from "react";
import { Color } from "@tiptap/extension-color";
import { common, createLowlight } from "lowlight";
import CodeBlockLowlight from "@tiptap/extension-code-block-lowlight";
import ListItem from "@tiptap/extension-list-item";
import TextStyle from "@tiptap/extension-text-style";
import Table from "@tiptap/extension-table";
import TableCell from "@tiptap/extension-table-cell";
import TableHeader from "@tiptap/extension-table-header";
import TableRow from "@tiptap/extension-table-row";
import Underline from "@tiptap/extension-underline";
import Document from "@tiptap/extension-document";
import Gapcursor from "@tiptap/extension-gapcursor";
import Paragraph from "@tiptap/extension-paragraph";
import Text from "@tiptap/extension-text";
import Image from "@tiptap/extension-image";
import Highlight from "@tiptap/extension-highlight";
import TextAlign from "@tiptap/extension-text-align";
import { EditorContent, useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import "tippy.js/animations/shift-toward-subtle.css";
import Link from "@tiptap/extension-link";
import ImageResize from "tiptap-extension-resize-image";

import { Icons } from "./Icons";

import Extensions, { TipTapEditorExtensions } from "./extensions";
import Props from "./props";
import { Button, Select, message } from "antd";
import "./Styles.css";
import { TipTapEditorProps } from "./TipTapEditorProps";
import BubbleMenu from "@tiptap/extension-bubble-menu";
import { NodeTypeDropdown } from "./Signature/NodeTypeDropdown";
import { useTranslation } from "react-i18next";
import { EditorBubbleMenu } from "./Signature/bubbleMenu";
import MainService from "../../../services/main.service.js";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import {
  modifySelfNote,
  saveNewNoteAfterPost,
  saveSelfNoteError,
  saveSelfNoteSuccess,
  triggerSaveNote,
} from "../../../new-redux/actions/selfnotes.actions/selfnotes.js";
import { useLocation } from "react-router-dom";
import SuperchargedTableExtensions from "./Table/supercharged-table-kit";
import { TipTapEditorSignatureExtensions } from "./Signature/extensions";
// const TableMenu = ({ editor }) => [
//   {
//     id: 1,
//     name: "Insert Table",
//     action: () =>
//       editor
//         .chain()
//         .focus()
//         .insertTable({ rows: 3, cols: 3, withHeaderRow: true })
//         .run(),
//   },
//   {
//     id: 2,
//     name: "Add Column Before",
//     action: () => editor.chain().focus().addColumnBefore().run(),
//   },
//   {
//     id: 3,
//     name: "Add Column After",
//     action: () => editor.chain().focus().addColumnAfter().run(),
//   },
//   {
//     id: 4,
//     name: "Delete Column",
//     action: () => editor.chain().focus().deleteColumn().run(),
//   },
//   {
//     id: 5,
//     name: "Add Row Before",
//     action: () => editor.chain().focus().addRowBefore().run(),
//   },
//   {
//     id: 6,
//     name: "Add Row After",
//     action: () => editor.chain().focus().addRowAfter().run(),
//   },
//   {
//     id: 7,
//     name: "Delete Row",
//     action: () => editor.chain().focus().deleteRow().run(),
//   },
//   {
//     id: 8,
//     name: "Delete Table",
//     action: () => editor.chain().focus().deleteTable().run(),
//   },
//   {
//     id: 9,
//     name: "Merge Cells",
//     action: () => editor.chain().focus().mergeCells().run(),
//   },
//   {
//     id: 11,
//     name: "Toggle Header Column",
//     action: () => editor.chain().focus().toggleHeaderColumn().run(),
//   },
//   {
//     id: 12,
//     name: "Toggle Header Row",
//     action: () => editor.chain().focus().toggleHeaderRow().run(),
//   },
//   {
//     id: 13,
//     name: "Toggle Header Cell",
//     action: () => editor.chain().focus().toggleHeaderCell().run(),
//   },
//   {
//     id: 14,
//     name: "Merge Or Split",
//     action: () => editor.chain().focus().mergeOrSplit().run(),
//   },
//   {
//     id: 15,
//     name: "Set Cell Attribute",
//     action: () => editor.chain().focus().setCellAttribute("colspan", 2).run(),
//   },
// ];

// const debounce = (func, delay) => {
//   let timeoutId;
//   return (...args) => {
//     if (timeoutId) clearTimeout(timeoutId);
//     timeoutId = setTimeout(() => {
//       func(...args);
//     }, delay);
//   };
// };

const useDebounce = (callback, delay) => {
  const timeoutRef = useRef(null);

  const debouncedFunction = (...args) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  };

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return debouncedFunction;
};

const MenuBarIcon = ({ editor }) => [
  {
    id: 1,
    name: "bold",
    icon: Icons.bold,
    onClick: () => editor.chain().focus().toggleBold().run(),
    disable: !editor.can().chain().focus().toggleBold().run(),
    isActive: editor.isActive("bold") ? "is-active text-green-700" : "",
    hover: false,
    split: false,
  },
  {
    id: 2,
    name: "italic",
    icon: Icons.italic,
    onClick: () => editor.chain().focus().toggleItalic().run(),
    disable: !editor.can().chain().focus().toggleItalic().run(),
    isActive: editor.isActive("italic") ? "is-active text-green-700" : "",
    hover: false,
    split: false,
  },
  {
    id: 21,
    name: "underline",
    icon: Icons.underline,
    onClick: () => editor.chain().focus().toggleUnderline().run(),
    disable: false,
    isActive: editor.isActive("underline") ? "is-active text-green-700" : "",
    hover: false,
    split: false,
  },
  {
    id: 3,
    name: "strike",
    icon: Icons.strikethrough,
    onClick: () => editor.chain().focus().toggleStrike().run(),
    disable: !editor.can().chain().focus().toggleStrike().run(),
    isActive: editor.isActive("strike") ? "is-active text-green-700" : "",
    hover: false,
    split: false,
  },
  {
    id: 4,
    name: "code",
    icon: Icons.code,
    onClick: () => editor.chain().focus().toggleCode().run(),
    disable: !editor.can().chain().focus().toggleCode().run(),
    isActive: editor.isActive("code") ? "is-active text-green-700" : "",
    hover: false,
    split: true,
  },
  {
    id: 5,

    items: [
      {
        name: "paragraph",
        icon: Icons.paragraph,
        onClick: () => editor.chain().focus().setParagraph().run(),
        disable: false,
        isActive: editor.isActive("paragraph")
          ? "is-active text-green-700"
          : "",
        hover: false,
        split: true,
      },
      {
        name: "heading1",
        icon: Icons.h1,
        onClick: () => editor.chain().focus().toggleHeading({ level: 1 }).run(),
        disable: false,
        isActive: editor.isActive("heading", { level: 1 })
          ? "is-active text-green-700"
          : "",
        hover: false,
        split: false,
      },
      {
        name: "heading2",
        icon: Icons.h2,
        onClick: () => editor.chain().focus().toggleHeading({ level: 2 }).run(),
        disable: false,
        isActive: editor.isActive("heading", { level: 2 })
          ? "is-active text-green-700"
          : "",
        hover: false,
        split: false,
      },
      {
        name: "heading3",
        icon: Icons.h3,
        onClick: () => editor.chain().focus().toggleHeading({ level: 3 }).run(),
        disable: false,
        isActive: editor.isActive("heading", { level: 3 })
          ? "is-active text-green-700"
          : "",
        hover: false,
        split: false,
      },
      {
        name: "heading4",
        icon: Icons.h4,
        onClick: () => editor.chain().focus().toggleHeading({ level: 4 }).run(),
        disable: false,
        isActive: editor.isActive("heading", { level: 4 })
          ? "is-active text-green-700"
          : "",
        hover: false,
        split: false,
      },
      {
        name: "heading5",
        icon: Icons.h5,
        onClick: () => editor.chain().focus().toggleHeading({ level: 5 }).run(),
        disable: false,
        isActive: editor.isActive("heading", { level: 5 })
          ? "is-active text-green-700"
          : "",
        hover: false,
        split: false,
      },
      {
        name: "bullet list",
        icon: Icons.ul,
        onClick: () => editor.chain().focus().toggleBulletList().run(),
        disable: false,
        isActive: editor.isActive("bulletList")
          ? "is-active text-green-700 list-disc"
          : "",
        hover: false,
        split: false,
      },
      {
        name: "ordered list",
        icon: Icons.ol,
        onClick: () => editor.chain().focus().toggleOrderedList().run(),
        disable: false,
        isActive: editor.isActive("orderedList")
          ? "is-active text-green-700 list-decimal"
          : "",
        hover: false,
        split: false,
      },
    ],
    isSelect: true,
  },
  {
    id: 16,
    items: [
      {
        name: "align left",
        icon: Icons.alignLeft,
        onClick: () => editor.chain().focus().setTextAlign("left").run(),
        disable: false,
        isActive: editor.isActive({ textAlign: "left" }) ? "is-active" : "",
        hover: false,
        split: false,
      },
      {
        name: "align center",
        icon: Icons.alignCenter,
        onClick: () => editor.chain().focus().setTextAlign("center").run(),
        disable: false,
        isActive: editor.isActive({ textAlign: "center" })
          ? "is-active text-green-700 text-center"
          : "",
        hover: false,
        split: false,
      },
      {
        name: "align right",
        icon: Icons.alignRight,
        onClick: () => editor.chain().focus().setTextAlign("right").run(),
        disable: false,
        isActive: editor.isActive({ textAlign: "right" }) ? "is-active" : "",
        hover: false,
        split: false,
      },
      {
        name: "align justify",
        icon: Icons.alignJustify,
        onClick: () => editor.chain().focus().setTextAlign("justify").run(),
        disable: false,
        isActive: editor.isActive({ textAlign: "justify" }) ? "is-active" : "",
        hover: false,
        split: true,
      },
    ],

    isSelect: true,
  },

  {
    id: 20,
    name: "highlight",
    icon: Icons.bg,
    onClick: () => editor.chain().focus().toggleHighlight().run(),
    disable: false,
    isActive: editor.isActive("highlight") ? "is-active text-green-700" : "",
    hover: false,
    split: false,
  },
  {
    id: 10,
    name: "code block",
    icon: Icons.codeblock,
    onClick: () => editor.chain().focus().toggleCodeBlock().run(),
    disable: false,
    isActive: editor.isActive("codeBlock") ? "is-active text-green-700" : "",
    hover: false,
    split: false,
  },
  {
    id: 11,
    name: "blockquote",
    icon: Icons.blockquote,
    onClick: () => editor.chain().focus().toggleBlockquote().run(),
    disable: false,
    isActive: editor.isActive("blockquote") ? "is-active text-green-700" : "",
    hover: false,
    split: false,
  },
  {
    id: 12,
    name: "table",
    icon: Icons.table,
    onClick: () =>
      editor
        .chain()
        .focus()
        .insertTable({ rows: 3, cols: 3, withHeaderRow: true })
        .run(),
    disable: false,
    isActive: editor.isActive("table") ? "is-active text-green-700" : "",
    hover: true,
    split: true,
  },
  {
    id: 30,
    name: "undo",
    icon: Icons.undo,
    onClick: () => editor.chain().focus().undo().run(),
    disable: !editor.can().undo(),
    isActive: editor.isActive("table") ? "is-active text-green-700" : "",
    hover: false,
    split: false,
  },
  {
    id: 31,
    name: "redo",
    icon: Icons.redo,
    onClick: () => editor.chain().focus().redo().run(),
    disable: !editor.can().redo(),
    isActive: editor.isActive("table") ? "is-active text-green-700" : "",
    hover: false,
    split: true,
  },
];
// const MenuBarIcon = ({ editor }) => [
//   {
//     id: 1,
//     name: 'bold',
//     icon: Icons.bold,
//     onClick: () => editor.chain().focus().toggleBold().run(),
//     disable: !editor.can().chain().focus().toggleBold().run(),
//     isActive: editor.isActive('bold') ? 'is-active text-green-700' : '',
//     hover: false,
//     split: false,
//   },
//   {
//     id: 2,
//     name: 'italic',
//     icon: Icons.italic,
//     onClick: () => editor.chain().focus().toggleItalic().run(),
//     disable: !editor.can().chain().focus().toggleItalic().run(),
//     isActive: editor.isActive('italic') ? 'is-active text-green-700' : '',
//     hover: false,
//     split: false,
//   },
//   {
//     id: 21,
//     name: 'underline',
//     icon: Icons.underline,
//     onClick: () => editor.chain().focus().toggleUnderline().run(),
//     disable: false,
//     isActive: editor.isActive('underline') ? 'is-active text-green-700' : '',
//     hover: false,
//     split: false,
//   },
//   {
//     id: 3,
//     name: 'strike',
//     icon: Icons.strikethrough,
//     onClick: () => editor.chain().focus().toggleStrike().run(),
//     disable: !editor.can().chain().focus().toggleStrike().run(),
//     isActive: editor.isActive('strike') ? 'is-active text-green-700' : '',
//     hover: false,
//     split: false,
//   },
//   {
//     id: 4,
//     name: 'code',
//     icon: Icons.code,
//     onClick: () => editor.chain().focus().toggleCode().run(),
//     disable: !editor.can().chain().focus().toggleCode().run(),
//     isActive: editor.isActive('code') ? 'is-active text-green-700' : '',
//     hover: false,
//     split: true,
//   },
//   {
//     id: 5,
//     name: 'heading1',
//     icon: Icons.h1,
//     onClick: () => editor.chain().focus().toggleHeading({ level: 1 }).run(),
//     disable: false,
//     isActive: editor.isActive('heading', { level: 1 })
//       ? 'is-active text-green-700'
//       : '',
//     hover: false,
//     split: false,
//   },
//   {
//     id: 6,
//     name: 'heading2',
//     icon: Icons.h2,
//     onClick: () => editor.chain().focus().toggleHeading({ level: 2 }).run(),
//     disable: false,
//     isActive: editor.isActive('heading', { level: 2 })
//       ? 'is-active text-green-700'
//       : '',
//     hover: false,
//     split: false,
//   },
//   {
//     id: 13,
//     name: 'heading3',
//     icon: Icons.h3,
//     onClick: () => editor.chain().focus().toggleHeading({ level: 3 }).run(),
//     disable: false,
//     isActive: editor.isActive('heading', { level: 3 })
//       ? 'is-active text-green-700'
//       : '',
//     hover: false,
//     split: false,
//   },
//   {
//     id: 14,
//     name: 'heading4',
//     icon: Icons.h4,
//     onClick: () => editor.chain().focus().toggleHeading({ level: 4 }).run(),
//     disable: false,
//     isActive: editor.isActive('heading', { level: 4 })
//       ? 'is-active text-green-700'
//       : '',
//     hover: false,
//     split: false,
//   },
//   {
//     id: 15,
//     name: 'heading5',
//     icon: Icons.h5,
//     onClick: () => editor.chain().focus().toggleHeading({ level: 5 }).run(),
//     disable: false,
//     isActive: editor.isActive('heading', { level: 5 })
//       ? 'is-active text-green-700'
//       : '',
//     hover: false,
//     split: false,
//   },
//   {
//     id: 7,
//     name: 'paragraph',
//     icon: Icons.paragraph,
//     onClick: () => editor.chain().focus().setParagraph().run(),
//     disable: false,
//     isActive: editor.isActive('paragraph') ? 'is-active text-green-700' : '',
//     hover: false,
//     split: true,
//   },
//   {
//     id: 8,
//     name: 'bullet list',
//     icon: Icons.ul,
//     onClick: () => editor.chain().focus().toggleBulletList().run(),
//     disable: false,
//     isActive: editor.isActive('bulletList')
//       ? 'is-active text-green-700 list-disc'
//       : '',
//     hover: false,
//     split: false,
//   },
//   {
//     id: 9,
//     name: 'ordered list',
//     icon: Icons.ol,
//     onClick: () => editor.chain().focus().toggleOrderedList().run(),
//     disable: false,
//     isActive: editor.isActive('orderedList')
//       ? 'is-active text-green-700 list-decimal'
//       : '',
//     hover: false,
//     split: false,
//   },
//   {
//     id: 16,
//     name: 'align left',
//     icon: Icons.alignLeft,
//     onClick: () => editor.chain().focus().setTextAlign('left').run(),
//     disable: false,
//     isActive: editor.isActive({ textAlign: 'left' }) ? 'is-active' : '',
//     hover: false,
//     split: false,
//   },
//   {
//     id: 17,
//     name: 'align center',
//     icon: Icons.alignCenter,
//     onClick: () => editor.chain().focus().setTextAlign('center').run(),
//     disable: false,
//     isActive: editor.isActive({ textAlign: 'center' })
//       ? 'is-active text-green-700 text-center'
//       : '',
//     hover: false,
//     split: false,
//   },
//   {
//     id: 18,
//     name: 'align right',
//     icon: Icons.alignRight,
//     onClick: () => editor.chain().focus().setTextAlign('right').run(),
//     disable: false,
//     isActive: editor.isActive({ textAlign: 'right' }) ? 'is-active' : '',
//     hover: false,
//     split: false,
//   },
//   {
//     id: 19,
//     name: 'align justify',
//     icon: Icons.alignJustify,
//     onClick: () => editor.chain().focus().setTextAlign('justify').run(),
//     disable: false,
//     isActive: editor.isActive({ textAlign: 'justify' }) ? 'is-active' : '',
//     hover: false,
//     split: true,
//   },
//   {
//     id: 20,
//     name: 'highlight',
//     icon: Icons.bg,
//     onClick: () => editor.chain().focus().toggleHighlight().run(),
//     disable: false,
//     isActive: editor.isActive('highlight') ? 'is-active text-green-700' : '',
//     hover: false,
//     split: false,
//   },
//   {
//     id: 10,
//     name: 'code block',
//     icon: Icons.codeblock,
//     onClick: () => editor.chain().focus().toggleCodeBlock().run(),
//     disable: false,
//     isActive: editor.isActive('codeBlock') ? 'is-active text-green-700' : '',
//     hover: false,
//     split: false,
//   },
//   {
//     id: 11,
//     name: 'blockquote',
//     icon: Icons.blockquote,
//     onClick: () => editor.chain().focus().toggleBlockquote().run(),
//     disable: false,
//     isActive: editor.isActive('blockquote') ? 'is-active text-green-700' : '',
//     hover: false,
//     split: false,
//   },
//   {
//     id: 12,
//     name: 'table',
//     icon: Icons.table,
//     onClick: () =>
//       editor
//         .chain()
//         .focus()
//         .insertTable({ rows: 3, cols: 3, withHeaderRow: true })
//         .run(),
//     disable: false,
//     isActive: editor.isActive('table') ? 'is-active text-green-700' : '',
//     hover: true,
//     split: true,
//   },
//   {
//     id: 30,
//     name: 'undo',
//     icon: Icons.undo,
//     onClick: () => editor.chain().focus().undo().run(),
//     disable: !editor.can().undo(),
//     isActive: editor.isActive('table') ? 'is-active text-green-700' : '',
//     hover: false,
//     split: false,
//   },
//   {
//     id: 31,
//     name: 'redo',
//     icon: Icons.redo,
//     onClick: () => editor.chain().focus().redo().run(),
//     disable: !editor.can().redo(),
//     isActive: editor.isActive('table') ? 'is-active text-green-700' : '',
//     hover: false,
//     split: true,
//   },
// ];

export function MenuBar({ editor, setImageURL }) {
  const [open, setOpen] = useState(true);
  const fileInputRef = useRef(null);

  if (!editor) {
    return null;
  }
  const MenuBarIconValue = MenuBarIcon({ editor });

  const handleIconClick = () => {
    fileInputRef.current?.click();
  };
  const handleChangeHeading = (value) => {
    if (value === "paragraph") {
      editor.chain().focus().setParagraph().run();
    } else if (value === "heading1") {
      editor.chain().focus().toggleHeading({ level: 1 }).run();
    } else if (value === "heading2") {
      editor.chain().focus().toggleHeading({ level: 2 }).run();
    } else if (value === "heading3") {
      editor.chain().focus().toggleHeading({ level: 3 }).run();
    } else if (value === "heading4") {
      editor.chain().focus().toggleHeading({ level: 4 }).run();
    } else if (value === "heading5") {
      editor.chain().focus().toggleHeading({ level: 5 }).run();
    } else if (value === "align left") {
      editor.chain().focus().setTextAlign("left").run();
    } else if (value === "align center") {
      editor.chain().focus().setTextAlign("center").run();
    } else if (value === "align right") {
      editor.chain().focus().setTextAlign("right").run();
    } else if (value === "align justify") {
      editor.chain().focus().setTextAlign("justify").run();
    } else if (value === "ordered list") {
      editor.chain().focus().toggleOrderedList().run();
    } else if (value === "bullet list") {
      editor.chain().focus().toggleBulletList().run();
    }
  };
  return (
    <div className="flex w-full flex-wrap items-center gap-1 bg-white p-2  text-black">
      <input
        type="color"
        onInput={(event) =>
          editor.chain().focus().setColor(event.target.value).run()
        }
        value={editor.getAttributes("textStyle").color}
      />
      {MenuBarIconValue.map((item) =>
        item.hover ? (
          <>
            {item?.isSelect ? (
              <Select
                // defaultValue={item.items[0]}
                size="small"
                style={{
                  width: 120,
                }}
                onChange={handleChangeHeading}
                options={item.items.map((el) => ({
                  label: (
                    <div className="flex items-center space-x-1">
                      <el.icon size={16} />
                      <span>{el.name}</span>
                    </div>
                  ),
                  value: el.name,
                }))}
              />
            ) : (
              <Button
                key={item.id}
                // onClick={item.onClick}
                size="small"
                disabled={item.disable}
                className={`${
                  item.disable ? "cursor-not-allowed" : "cursor-pointer"
                } flex items-center`}
                type="text"
              >
                <item.icon size={16} />
              </Button>
            )}
            {item.split && (
              <div className="mx-1 flex h-6 w-[1px] bg-gray-100" />
            )}
            {/* {TableMenu({ editor }).map((menuItem) => (
                  <MenubarItem key={menuItem.id} onClick={menuItem.action}>
                    {menuItem.name}
                  </MenubarItem>
                ))} */}
          </>
        ) : (
          <>
            {item?.isSelect ? (
              <Select
                defaultValue={item.items[0].name}
                size="small"
                style={{
                  width: "50px",
                }}
                onChange={(value) => handleChangeHeading(value)}
                options={item.items.map((el) => ({
                  label: (
                    <div className="flex items-center space-x-1">
                      <el.icon size={16} />
                    </div>
                  ),
                  value: el.name,
                }))}
              />
            ) : (
              <div className="flex h-full items-center gap-1">
                <Button
                  key={item.id}
                  size="small"
                  onClick={item.onClick}
                  disabled={item.disable}
                  className={`${
                    item.disable
                      ? "cursor-not-allowed p-1"
                      : "hover: cursor-pointer p-1 hover:bg-gray-100"
                  } + ${item.isActive ? item.isActive : ""} flex items-center`}
                >
                  <item.icon size={16} />
                </Button>
                {/* {item.split && (
              <div className="mx-1 w-[1px] flex bg-gray-100 h-6" />
            )} */}
              </div>
            )}
          </>
        )
      )}
      {/* <div className="cursor-pointer hover:bg-gray-100 hover: p-1">
        <input
          type="file"
          onChange={handleImageChange}
          ref={fileInputRef}
          className="hidden"
        />
        <Icons.image onClick={handleIconClick} />
      </div> */}
    </div>
  );
}

function Editorr(props) {
  const {
    editorText,
    contentSignature,
    source = "",
    selectedNote,
    setClickedNote,
    signature,
    setHasSelectedNode = () => {},
    height=""
  } = props;
  // console.log("selectedNote", selectedNote);
  const lowlight = createLowlight(common);
  const editorRef = useRef();
  const [cursorPosition, setCursorPosition] = React.useState({});
  const [imageURL, setImageURL] = useState(null);
  const [showMenuBar, setShowMenuBar] = useState(null);
  const [messageApi, contextHolder] = message.useMessage();
  const [t] = useTranslation("common");
  const { pathname } = useLocation();
  const dispatch = useDispatch();

  const handleImageChange = (file) => {
    let filesize = (file.size / 1024 / 1024).toFixed(4);
    if (!file.type.includes("image")) {
      messageApi.open({
        type: "error",
        content: t("chat.bot.acceptedOnlyImageType"),
      });
    }
    if (file && file.type.includes("image") && filesize < 10) {
      const reader = new FileReader();
      reader.onloadend = () => {
        if (typeof reader.result === "string") {
          setImageURL(reader.result);
        }
      };
      reader.readAsDataURL(file);
    }
  };
  const saveNote = useSelector((state) => state.selfNotesReducer.triggerSave);
  const [isSaving, setIsSaving] = useState(false);

  const testText = `<div data-type="rootblock"><p>ffdfdfdsfds</p></div><div data-type="rootblock"><p>dfdfdfdf</p></div><div data-type="rootblock"><h1>fdfdsfdsfdsfdsf</h1></div><div data-type="rootblock"><p><strong>fdsfdsfdsfdfdsfdfdf</strong></p></div>`;
  let editor;

  // console.log("selectedNote", selectedNote);

  editor = useEditor({
    editable: selectedNote?.is_locked == true ? false : true,
    editorProps: {
      handleDrop: function (view, event, slice, moved) {
        if (
          !moved &&
          event.dataTransfer &&
          event.dataTransfer.files &&
          event.dataTransfer.files[0]
        ) {
          // if dropping external files
          let file = event.dataTransfer.files[0]; // the dropped file
          handleImageChange(file);

          return true; // handled
        }
        return false; // not handled use default behaviour
      },
    },
    extensions: [
      ...TipTapEditorSignatureExtensions,
      ImageResize,
      Color.configure({ types: [TextStyle.name, ListItem.name] }),
      TextStyle.configure({ types: [ListItem.name] }),
      // StarterKit.configure({
      //   bulletList: {
      //     keepMarks: true,
      //     keepAttributes: false, // TODO : Making this as `false` becase marks are not preserved when I try to preserve attrs, awaiting a bit of help
      //   },
      //   orderedList: {
      //     keepMarks: true,
      //     keepAttributes: false, // TODO : Making this as `false` becase marks are not preserved when I try to preserve attrs, awaiting a bit of help
      //   },
      // }),
      TextAlign.configure({
        types: ["heading", "paragraph"],
      }),
      Image.configure({
        inline: true,
        allowBase64: true,
      }),
      Underline.configure({
        HTMLAttributes: {
          class: "my-custom-class",
        },
      }),
      // CodeBlockLowlight.configure({
      //   lowlight,
      // }),
      Highlight,
      Link,
      // Document,
      Paragraph,
      Text,
      Gapcursor,
    ],
    // .filter((extension) => {
    //   if (selectedNote?.is_locked) {
    //     return ![ImageResize.name, Image.name, Link.name].includes(
    //       extension.name
    //     );
    //   } else {
    //     return true;
    //   }
    // })
    selectedNote,

    onBlur({ editor, event }) {
      // setClickedNote({ ...selectedNote, content: editor?.getHTML() });
      source === "signature" && setClickedNote(editor?.getHTML());

      // The editor isn’t focused anymore.
    },
    onUpdate: ({ editor }) => {
      editorRef.current = editor;
    },
    onSelectionUpdate({ editor }) {
      const { empty, from, to } = editor.view.state.selection;
      if (pathname === "/profile/signature") {
        if (
          editor?.getHTML() == '<div data-type="rootblock"><p></p></div>' ||
          editor?.getHTML() === ""
          // || editor?.getHTML()===signature?.value
        ) {
          // setDisabled(true)
          setHasSelectedNode(false);
        } else {
          // setDisabled(false);
          setHasSelectedNode(true);
        }
      }
    },
  });

  // const workerRef = useRef(null);

  // useEffect(() => {
  //   workerRef.current = new Worker(new URL("./saveWorker.js", import.meta.url));

  //   workerRef.current.onmessage = function (e) {
  //     const { type, payload } = e.data;

  //     switch (type) {
  //       case "SAVE_NOTE":
  //         handleSaveNoteInWorker(payload.noteId, payload.data);
  //         break;

  //       default:
  //         break;
  //     }
  //   };

  //   return () => {
  //     workerRef.current.terminate();
  //   };
  // }, []);
  const isFirstChange = useRef(true);

  // const handleSaveNote = useCallback(() => {
  //   if (!editor || isSaving) return;

  //   const data = {
  //     content: editor.getHTML(),
  //     permission: 1,
  //   };

  //   setIsSaving(true);
  //   MainService.updateNote360(selectedNote?._id, data)
  //     .then((response) => {
  //       dispatch(modifySelfNote(response.data.data));
  //       dispatch(saveSelfNoteSuccess());
  //     })
  //     .catch((error) => {
  //       console.error("Failed to save note:", error);
  //       dispatch(saveSelfNoteError());
  //     })
  //     .finally(() => {
  //       setIsSaving(false);
  //       dispatch(triggerSaveNote());
  //       // isFirstChange.current = true;
  //     });
  // }, [dispatch, selectedNote?._id, isSaving, editor]);

  // const debouncedSave = useDebounce(handleSaveNote, 1500);

  // useEffect(() => {
  //   if (!editor) return;

  //   const handleChange = () => {
  //     console.log("editor changed", isFirstChange.current);
  //     // if (isFirstChange.current) {
  //     //   MainService.lockNote(selectedNote._id).then((response) => {
  //     //     console.log("lock note", response);
  //     //     isFirstChange.current = false;
  //     //   });
  //     // }
  //     if (!isSaving) {
  //       debouncedSave();
  //     }
  //   };

  //   // Register a listener for editor content change
  //   editor.on("update", handleChange);

  //   return () => {
  //     editor.off("update", handleChange);
  //   };
  // }, [debouncedSave, isSaving, editor]);

  // useEffect(() => {
  //   MainService.lockNote(selectedNote?._id)
  //     .then((response) => {
  //       console.log("lock note 01", response);
  //       isFirstChange.current = false;
  //     })
  //     .catch((error) => {
  //       console.error("Failed to lock note:", error);
  //     });

  //   return () => {
  //     MainService.unlockNote(selectedNote?._id)
  //       .then((response) => {
  //         console.log("unlock note 01", response);
  //       })
  //       .catch((error) => {
  //         console.error("Failed to unlock note:", error);
  //       });
  //   };
  // }, []);

  const handleCreateNote = () => {
    let formData = new FormData();
    formData.append("content", editor?.getHTML());

    MainService.createNote360(formData)
      .then((response) => {
        console.log(response);
        dispatch(
          saveNewNoteAfterPost({
            localId: selectedNote?._id,
            newNote: response?.data?.data[0]?.note,
          })
        );
        setClickedNote(
          response?.data?.data[0]?.note
            ? response?.data?.data[0]?.note
            : selectedNote
        );
        dispatch(saveSelfNoteSuccess());
      })
      .catch((error) => {
        console.log(error);
        dispatch(saveSelfNoteError());
      })
      .finally(() => {
        dispatch(triggerSaveNote());
      });
  };

  // useEffect(() => {
  //   if (saveNote && source !== "signature") {
  //     if (selectedNote?.isNew === true) {
  //       handleCreateNote();
  //       console.log("selectedNote", selectedNote);
  //     } else {
  //       handleSaveNote();
  //       console.log("saveNote", saveNote);
  //       console.log("selectedNote", selectedNote);
  //     }
  //   }
  // }, [saveNote, source]);

  const handleSelectionUpdate = () => {
    const { selection } = editor.state;
    const { from, to } = selection;
    if (from !== to) {
      const startPosition = editor.view.coordsAtPos(from);
      const endPosition = editor.view.coordsAtPos(to);

      // setCursorPosition(editor.view.dom.getBoundingClientRect());
    }
  };
  // Attacher l'événement de mise à jour de la sélection
  // useEffect(() => {
  //   const view = editor?.view;
  //   if(view){

  //   view.dom.addEventListener('mouseup', handleSelectionUpdate);
  //   view.dom.addEventListener('keyup', handleSelectionUpdate);
  //   }
  //   return () => {
  //     if(view){

  //     view.dom.removeEventListener('mouseup', handleSelectionUpdate);
  //     view.dom.removeEventListener('keyup', handleSelectionUpdate);
  //     }
  //   };
  // }, [editor])

  const getSelectionCoordinates = (selection) => {
    const { from, to } = selection;

    const startPosition = editor.view.coordsAtPos(from);
    const endPosition = editor.view.coordsAtPos(to);

    const top = startPosition.top;
    const left = startPosition.left;
    const right = endPosition.right;
    const bottom = endPosition.bottom;
    setCursorPosition(top);
  };
  const handleDoubleClick = (event) => {
    const selection = editor.state.selection;
    const { from, to, empty } = selection;
    getSelectionCoordinates(selection);
    // setCursorPosition(anchor)

    // Vérifier si la sélection est un double-clic sur du texte
    const isAlreadySelected =
      !empty && from === to && from === editor.state.selection.from;
    setShowMenuBar(!empty && from !== to && !isAlreadySelected);
  };
  const handleSelectionChange = () => {
    const selection = editor.state.selection;
    // Vérifier si la sélection est vide (déselection)
    if (selection.empty) {
      // La déselection est détectée
      setShowMenuBar(false);
    }
  };
  const onScroll = async (e) => {
    if (e?.currentTarget?.scrollTop) {
    }
  };

  // console.log(editor?.getHTML());

  // React.useEffect(() => {
  //   const view = editor?.view;
  //   const editorElement = editorRef.current;
  //   // Attacher l'événement double-clic à l'élément contenant l'éditeur
  //   if(view){
  //   editorElement.addEventListener('dblclick', handleDoubleClick);
  //   view.dom.addEventListener('selectionchange', handleSelectionChange);
  //   }
  //   return () => {
  //     // Détacher l'événement lors du démontage du composant
  //     if(view){
  //     editorElement.removeEventListener('dblclick', handleDoubleClick);
  //     view.dom.removeEventListener('selectionchange', handleSelectionChange);

  //     }
  //   };

  // }, [editor]);
  useEffect(() => {
    if (editor && imageURL) {
      editor.commands.setImage({
        src: imageURL,
      });
    }
  }, [imageURL]);

  useEffect(() => {
    if (editor && editorText) {
      editor
        .chain()
        .focus()
        .insertContent(editorText)
        .insertContent(`<br  />`)
        .run();
    }
  }, [editorText, editor]);

  useEffect(() => {
    if (pathname === "/profile/signature" && contentSignature) {
      // setTimeout(() => {

      editor?.commands?.setContent(contentSignature);

      // }, 100);
    } else {
      setTimeout(() => {
        if (selectedNote?.content) {
          //delete the content of the editor
          editor?.chain().focus().clearContent().run();
          //insert the content of the clicked note
          editor?.chain().focus().insertContent(selectedNote.content).run();
        } else {
          editor?.chain().focus().insertContent("").run();
        }
      }, 100);
    }
  }, [
    contentSignature,
    pathname,
    editor,
    signature?.id,
    selectedNote?.content,
  ]);

  const selfNotes = useSelector((state) => state.selfNotesReducer.selfNotes);

  // useEffect(() => {
  //   editor?.chain().focus().clearContent().run();
  //   if (selectedNote?.content) {
  //     //delete the content of the editor

  //     //insert the content of the clicked note
  //     editor?.chain().focus().insertContent(selectedNote.content).run();
  //   } else {
  //     editor?.chain().focus().insertContent("").run();
  //   }

  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [selectedNote]);

  return (
    <div className="temp">
      {/* <div className={showMenuBar?`visible`:`invisible`} style={{position:"fixed",top:Math.ceil((cursorPosition))-50,zIndex:500000,overflow:"hidden"}}>
        <MenuBar editor={editor} setImageURL={setImageURL} />
        
        </div> */}

      {contextHolder}

      <EditorContent
        editor={editor}
        style={{ minHeight:height?height: "400px", borderStyle: "none" }}
        className="templateEmail"
      />

      <EditorBubbleMenu editor={editor} />
    </div>
  );
}

export default Editorr;

// import { useEditor, EditorContent } from "@tiptap/react";
// import { useState, useEffect, } from "react";
// import { TipTapEditorExtensions } from "./extensions";
// import { TipTapEditorProps } from "./TipTapEditorProps";
// import "./Styles.css"
// export default function Editor({
//   document,
//   publicId,
// }) {
//   const [saveStatus, setSaveStatus] = useState("Saved");
//   const [hydrated, setHydrated] = useState(false);
//   const [content, setContent] = useState();

//   const editor = useEditor({
//     extensions: TipTapEditorExtensions,
//     editorProps: TipTapEditorProps,
//     onUpdate: (e) => {
//       setSaveStatus("Saving...");
//     },
//     content: content,
//   });

//   // Hydrate the editor with the content from the database.
//   useEffect(() => {
//     if (editor && document && !hydrated) {
//       editor.commands.setContent(document.document);
//       setHydrated(true);
//     }
//   }, [editor, document, hydrated]);

//   return (
//     <div  className="editor-container " style={{height:"calc(100vh - 450px)",overflowY: "auto"}} >

//     <div
//       onClick={() => {
//         editor?.chain().focus().run();
//       }}
//       className="relative flex min-h-screen w-full cursor-text flex-col items-center p-32"
//     >
//       <div className=" w-full max-w-screen-lg">
//         <div className="absolute left-8 top-8 rounded-lg bg-gray-100 px-2 py-1 text-sm text-gray-400">
//           {saveStatus}
//         </div>
//         <EditorContent editor={editor} />
//       </div>
//     </div>
//     </div>
//   );
// }
