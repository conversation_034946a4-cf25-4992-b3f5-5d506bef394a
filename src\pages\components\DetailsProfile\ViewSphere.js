import React, {
  memo,
  Suspense,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import { DollarOutlined, TeamOutlined } from "@ant-design/icons";
import { Form, Layout, Spin, message } from "antd";
import { HiOutlineBuildingOffice, HiOutlineTicket } from "react-icons/hi2";
import {
  Blocks,
  BookCopy,
  HandCoins,
  HeartHandshake,
  Loader,
} from "lucide-react";
import { useTranslation } from "react-i18next";

import MainService from "services/main.service";
import { Link, useLocation } from "react-router-dom";
import {
  getElementSystemDetails,
  getGeneralInfo360,
  getKPI,
  postAssociate,
} from "pages/clients&users/services/services";
import { useDispatch } from "react-redux";
import { URL_ENV } from "index";
import { useSelector } from "react-redux";
import ModalMessage from "pages/rmc/mailing/ModalMessage";
import {
  removeLastIdFromViewSphere,
  resetPrevIdsFromViewSphere,
  setActiveActivity360,
  setActiveTab360,
  setChatInViewSPhereFromDrawer,
  setNewInteraction,
  setOpenChatInViewSPhere,
  setOpenView360InDrawer,
} from "new-redux/actions/vue360.actions/vue360";
import { generateAxios } from "services/axiosInstance";
import { FiCopy, FiEdit, FiList, FiUsers } from "react-icons/fi";
import { CgUserlane } from "react-icons/cg";
import { AiOutlineShoppingCart } from "react-icons/ai";
import { LuPalmtree } from "react-icons/lu";
import { setUpdateElementSuccessfully } from "new-redux/actions/form.actions/form";

import { SET_CONTACT_INFO_FROM_DRAWER } from "new-redux/constants";
import ContentViewSphere from "./ContentViewSphere";
import HeaderViewSphere from "./HeaderViewSphere";
import party from "party-js";
import TicketIconSphere from "components/icons/TicketIconSphere";
import LoadingViewSphere from "./LoadingViewSphere";

export const familyIcons = (t) => {
  return [
    {
      value: 1,
      label: t("contacts.company"),
      icon: <HiOutlineBuildingOffice />,
      key: 1,
      pathname: "Organisation",
    },
    {
      value: 2,
      label: t("contacts.contact"),
      icon: <TeamOutlined />,
      key: 2,
      pathname: "Contact",
    },
    {
      value: 3,
      label: t("modules.deals"),
      icon: <HeartHandshake size={14} />,
      key: 3,
      pathname: "Deal",
    },
    {
      value: 4,
      label: t("contacts.user"),
      icon: <FiUsers />,
      key: 4,
      pathname: "User",
    },

    {
      value: 5,
      label: t("contacts.product"),
      icon: <AiOutlineShoppingCart />,
      key: 5,
      pathname: "Product",
    },
    {
      value: 6,
      label: "Tickets",
      icon: <TicketIconSphere size={14} />,
      key: 6,
      pathname: "Helpdesk",
    },
    {
      value: 7,
      label: t("contacts.project"),
      icon: <Blocks size={14} />,
      key: 7,
      pathname: "Project",
    },
    {
      value: 8,
      label: t("contacts.booking"),
      icon: <LuPalmtree />,
      key: 8,
      pathname: "Booking",
    },
    {
      value: 9,
      label: t("contacts.lead"),
      icon: <CgUserlane />,
      key: 9,
      pathname: "Leads",
    },
    {
      value: 11,
      label: t("menu2.invoices"),
      icon: <DollarOutlined />,
      key: 11,
      pathname: "Invoices",
    },
    {
      value: 12,
      label: "Transaction",
      icon: <HandCoins />,
      key: 12,
      pathname: "Transaction",
    },
  ];
};

const ViewSphere = ({ from = "viewSphere", elementId = "", source = "" }) => {
  const [tasksTypes, setTasksTypes] = useState([]);
  const [selectedTags, setSelectedTags] = useState({});
  const [selectedKey, setSelectedKey] = useState("1");
  const [selectedKeySideBar, setSelectedKeySideBar] = useState(null);
  const [openTask, setOpenTask] = useState(false);
  const [isFinalStage, setIsFinalStage] = useState(false);
  const [openEmailModal, setOpenEmailModal] = useState(false);
  const [disabledRelation, setDisabledRelation] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [headerHeight, setHeaderHeight] = useState(110);
  const [dataSteps, setDataSteps] = useState([]);
  const [loading, setLoading] = useState([]);
  const [mountChat, setMountchat] = useState(false);
  const [openRmc, setOpenRmc] = useState(false);
  const [listConv, setListConv] = useState([]);
  const [countTasks, setCountTasks] = useState({});
  const [kpi, setKpi] = useState([]);
  const [relations, setRelations] = useState([]);
  const [generalInfo, setGeneralInfo] = useState({});
  const [detailsInfo, setDetailsInfo] = useState({});
  const [openDrawerUpdate, setOpenDrawerUpdate] = useState(false);
  const [isUpdate, setIsUpdate] = useState(false);
  const [channel, setChannel] = useState(null);
  const [reference, setReference] = useState(null);
  const [openModalCheckList, setOpenModalCheckList] = useState(false);
  const [loadUpdateStage, setLoadUpdateStage] = useState(false);
  const [selectedReasonType, setSelectedReasonType] = useState(null);
  const [reasons, setReasons] = useState([]);
  const [selectedStage, setSelectedStage] = useState(null);
  const [collapsed, setCollapsed] = useState(true);
  const [openFields, setOpenFields] = useState(false);
  const [openPipeline, setOpenPipeline] = useState(false);
  const [widthHeaderRefInfo, setWidthHeaderRefInfo] = useState(0);
  const [windowHeight, setWindowHeight] = useState(window.innerHeight - 200);
  const [contactType, setContactType] = useState(null);
  const [mountRelation, setMountRelation] = useState(false);
  const [mountDrawer, setMountDrawer] = useState(false);
  const [actionDeal, setActionDeal] = useState(null);
  const [loadFinalStage, setLoadFinalStage] = useState(false);
  const [refreshInfo, setRefreshInfo] = useState(true);
  const token = localStorage.getItem("accessToken");
  const { user } = useSelector((state) => state.user);
  const [t] = useTranslation("common");
  const [finalStageForm] = Form.useForm();
  const headerInfoRef = useRef(null);
  const openModalEmail = useSelector(
    (state) => state.mailReducer.openModalEmail
  );
  const {
    activeTab360,
    newInteraction,
    contactInfoFromDrawer: contactInfo,
    openView360InDrawer,
  } = useSelector((state) => state?.vue360);
  const location = useLocation();
  const headerRef = useRef(null);

  const dispatch = useDispatch();
  useEffect(() => {
    if (activeTab360 == 9 || selectedKeySideBar === "Chat") {
      dispatch(setOpenChatInViewSPhere(true));
      setChatInViewSPhereFromDrawer({
        id: "",
        number: 0,
        relation_id: "",
      });
    } else {
      dispatch(setOpenChatInViewSPhere(false));
    }
  }, [activeTab360, selectedKeySideBar, dispatch]);
  // useEffect(() => {
  //   if (
  //     contactInfo?.id &&
  //     newInteraction?.type === "updateStageFromDrawer"
  //     // &&
  //     // user.id !== newInteraction?.user
  //   ) {
  //     getStages(contactInfo);
  //   }
  // }, [contactInfo?.id, newInteraction?.type]);

  useEffect(() => {
    if (
      contactInfo?.id &&
      newInteraction?.type === "updateStageFromDrawer" &&
      user.id !== newInteraction?.user
    ) {
      getStages(contactInfo);
    }
  }, [contactInfo?.id, newInteraction?.type]);
  useEffect(() => {
    if (!loading && headerRef.current) {
      const updateHeight = () => {
        setHeaderHeight(headerRef.current.clientHeight);
      };

      updateHeight();
      //   headerRef.current.addEventListener("transitionend", updateHeight);

      //   return () => {
      //     headerRef.current.removeEventListener("transitionend", updateHeight);
      //   };
    }
    if (!loading && headerInfoRef.current) {
      const updateWidth = () => {
        setWidthHeaderRefInfo(headerInfoRef.current.clientWidth);
      };

      updateWidth();
      //   headerRef.current.addEventListener("transitionend", updateHeight);

      //   return () => {
      //     headerRef.current.removeEventListener("transitionend", updateHeight);
      //   };
    }
  }, [loading]);
  useEffect(() => {
    if (!openTask) {
      setSelectedKeySideBar(null);
    }
  }, [openTask]);
  useEffect(() => {
    const getTasksCount = async () => {
      try {
        const res = await MainService.getTasks360Count({
          id: elementId,
          types: "",
        });

        setCountTasks(res?.data);
      } catch (err) {}
    };

    if (elementId) {
      getTasksCount();
    }
    setSelectedKey("1");

    // setSelectedKey(localStorage.getItem("selectedKeyViewSphere") || "1");
    // dispatch(
    //   setActiveTab360(
    //     Number(localStorage.getItem("activeTabInteractionsViewSphere"))
    //   )
    // );
    return () => {
      dispatch(setActiveTab360(null));
      // selectedKey != 2
      //   ? dispatch(setActiveTab360(null))
      //   : dispatch(
      //       setActiveTab360(
      //         Number(localStorage.getItem("activeTabInteractionsViewSphere"))
      //       )
      //     );
      dispatch(setActiveActivity360(""));
      setSelectedKeySideBar("");
      setRelations([]);
      setGeneralInfo({});
      dispatch(setUpdateElementSuccessfully(null));

      // dispatch(resetPrevIdsFromViewSphere());
      // dispatch(setNewInteraction({ type: "updateElementFromDrawer" }));
      // dispatch(setOpenView360InDrawer(false));
      setMountRelation(false);
      dispatch(setOpenChatInViewSPhere(false));
      if (mountDrawer) {
        setTimeout(() => {
          dispatch(setNewInteraction({ type: "associateElement" }));
        }, 1000);
      }
    };
  }, [dispatch, elementId]);

  useEffect(() => {
    const getAccessDisussion = async (contactInfo) => {
      try {
        const { data: element } = await getElementSystemDetails(
          contactInfo?.id
        );
        dispatch({
          type: SET_CONTACT_INFO_FROM_DRAWER,
          payload: {
            ...contactInfo,
            access_discussion: element.access_discussion,
          },
        });
      } catch (err) {}
    };
    const fetchKPI = async () => {
      try {
        setMountRelation(false);
        dispatch(setNewInteraction({ type: "" }));
        const { data } = await getKPI(
          contactInfo?.family_id,
          elementId || contactInfo?.id
        );
        if (
          // contactInfo?.owner?.id !== user.id &&
          (data
            .find((el) => el.family_id === 4)
            .child.some((el) => el.id === contactInfo?.owner?.id) &&
            data.find((el) => el.family_id === 4).number < 2) ||
          data.find((el) => el.family_id === 4)?.child?.length === 0
        ) {
          getAccessDisussion(contactInfo);
        }
        const convRmc = await MainService.getConvRmc360(contactInfo?.id);
        setListConv(convRmc.data.data.filter((el) => el.conversation_id));
        setRelations(
          data?.map((kpi) => ({
            ...kpi,
            children: kpi?.child?.length
              ? kpi?.child?.slice(0, 3)?.map((child) => ({
                  label: child?.label_data,
                  id: child?.id,
                  pipeline: child?.pipeline_label,
                  stage: child?.stage_label,
                }))
              : [],
          }))
        );
      } catch (err) {}
    };
    if (
      contactInfo.family_id &&
      (elementId || contactInfo?.id) &&
      (mountRelation || newInteraction.type === "associateElementFromDrawer")
    ) {
      fetchKPI();
    }
  }, [
    dispatch,
    contactInfo?.id,
    contactInfo?.family_id,
    elementId,
    newInteraction.type,
    mountRelation,
  ]);

  const getStages = async (contactInfo) => {
    try {
      const res = await MainService.getStages(elementId || elementId);
      const currentIndex = res.data.data.findIndex(
        (item) => item.currentstage === 1
      );

      let fetchElementInfo =
        contactInfo || (await getElementSystemDetails(elementId)).data;

      dispatch({
        type: SET_CONTACT_INFO_FROM_DRAWER,
        payload: { ...fetchElementInfo } || {},
      });

      if (newInteraction?.type === "updateStageFromDrawer") {
        dispatch(setNewInteraction({ type: "" }));
      }

      if (res.data.data.length > 0) {
        const dataSteps = res.data.data.map((item, index) => {
          const status =
            index < currentIndex
              ? "finish"
              : index > currentIndex
              ? "wait"
              : "process";
          return { ...item, status };
        });

        setDataSteps(dataSteps);
        const selectedTag = res.data.data.find((el) => el.currentstage === 1);
        setSelectedTags(selectedTag);
        selectedKey === "4" &&
          newInteraction?.type !== "updateStage" &&
          setRefreshInfo(true);
        if (
          isUpdate &&
          selectedTag?.final &&
          selectedTag?.default &&
          selectedTag?.resolved === 0 &&
          isFinalStage &&
          contactInfo?.family_id == 6
        ) {
          setLoading(false);
          SuccessWon();
          message.success(
            t("vue360.closeSuccess", {
              name: contactInfo?.name || contactInfo?.reference,
            }),
            3
          );
        }
      } else {
        setWindowHeight(window.innerHeight - 135);
        setHeaderHeight(76);
      }
    } catch (error) {
      // Gérer les erreurs ici
    }
  };

  useEffect(() => {
    const getALLdata = async () => {
      setLoading(true);
      try {
        // setIsUpdate(true);
        const [
          // { data: stats_chat },
          { data: fetchElementInfo },
          { data: tasks_type },
          // { data: generalInfos },
          { data: detailsInfo },
          { data: kpiInfo },
        ] = await Promise.all([
          getElementSystemDetails(elementId || elementId),
          MainService.getTasksTypes(),
          // getGeneralInfo360("general_info", elementId || elementId),
          getGeneralInfo360("header-info", elementId || elementId),
          MainService.getKpiOverview360ByElement(elementId || elementId),
        ]);

        // setGeneralInfo(generalInfos?.field_value);
        setDetailsInfo(detailsInfo?.field_value);
        const { data } = await getKPI(
          fetchElementInfo?.family_id,
          fetchElementInfo?.id
        );
        setRelations(
          data?.map((kpi) => ({
            ...kpi,
            children: kpi?.child?.length
              ? kpi?.child?.slice(0, 3)?.map((child) => ({
                  label: child?.label_data,
                  id: child?.id,
                  pipeline: child?.pipeline_label,
                  stage: child?.stage_label,
                }))
              : [],
          }))
        );

        setKpi(
          Object.entries(kpiInfo.data[0]).map(([key, value]) => ({
            title: key,
            value,
          }))
        );

        if (fetchElementInfo.source) {
          const channels = await generateAxios(
            URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
          ).get("/channels");
          setChannel(
            channels?.data?.data.find(
              (el) => el?.label === fetchElementInfo.source
            )
          );
        }
        if (fetchElementInfo.type) {
          const ContactTypes = await generateAxios(
            URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
          ).get("/type-contacts");
          setContactType(
            ContactTypes?.data?.data?.find(
              (el) => el?.label === fetchElementInfo.type
            )
          );
        }

        setReference(fetchElementInfo?.reference);

        if (fetchElementInfo.family_id) {
          const res = await generateAxios(
            URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
          ).get(`get-relation-family/${fetchElementInfo?.family_id}`);
          const convertedData = Object.entries(res.data).map(([key, label]) => {
            return Number(key);
          });
          const getUnreadMsgRoomInViewSphere = async () => {
            try {
              const res = await MainService.getUnreadMsgRoomInViewSphere({
                relation_id: elementId,
              });

              dispatch(
                setChatInViewSPhereFromDrawer({
                  number: res?.data?.total_message_room_count,
                  relation_id: elementId,
                  id: res?.data?.room_id,
                })
              );
            } catch (err) {
              console.log(err);
            }
          };
          if (fetchElementInfo.access_discussion === 1)
            getUnreadMsgRoomInViewSphere();
          if (user?.rmc_access === "OUI") {
            const convRmc = await MainService.getConvRmc360(
              fetchElementInfo.id
            );
            setListConv(convRmc.data.data.filter((el) => el.conversation_id));
          }
          setDisabledRelation(convertedData?.length < 1);
        }
        await getStages(fetchElementInfo);
        setTasksTypes(tasks_type?.data?.tasks_type);
        setIsUpdate(false);
        setLoading(false);

        // setIsUpdate(false);
        dispatch(setNewInteraction({ type: "" }));
        setMountDrawer(true);
      } catch (err) {
        setLoading(false);
      }
    };
    if (
      elementId &&
      (newInteraction?.type === "updateElementFromDrawer" || isUpdate)
    ) {
      getALLdata();
    }
  }, [newInteraction.type, elementId, isUpdate]);
  const handleDropdownClick = (e) => {
    const key = e?.key;
    switch (key) {
      case "edit":
        contactInfo?.id && setOpenDrawerUpdate(true);
        break;
      default:
        message.info("This feature is not ready yet!");
        break;
    }
  };

  const DropdownProps = {
    items: [
      {
        label: t("contacts.edit"),
        key: "edit",
        icon: <FiEdit className="h-4 w-4 text-slate-500" />,
        disabled: !contactInfo?.shared,
      },

      // {
      //   label: "Clone",
      //   icon: <BookCopy className="w-[16px] text-slate-500" />,

      //   key: "3",
      // },
      // {
      //   label: "Archived",
      //   icon: <Archive className="w-[16px] text-slate-500" />,
      //   key: "4",
      // },
      // {
      //   type: "divider",
      // },
      // {
      //   label: t("contacts.delete"),
      //   key: "1",
      //   icon: <FiTrash className="h-4 w-4 " />,
      //   danger: true,
      // },
    ],
    onClick: (e) => handleDropdownClick(e),
  };

  // Update element's stage.
  const updateElementStage = async (id, payload, newStageId) => {
    try {
      setLoadUpdateStage(true);
      const response = await MainService.updateElementStage(
        id,
        payload,
        newStageId
      );
      setLoadUpdateStage(false);
      return response;
    } catch (error) {
      setLoadUpdateStage(false);
      console.log(`Error ${error}`);
      message.error(error, 3);
    }
  };
  const SuccessWon = () => {
    const particleContainer = document.getElementById("header-container");
    if (particleContainer) {
      party.confetti(particleContainer, {
        count: party.variation.range(500, 1000), // Plus de confettis
        size: party.variation.range(0.5, 1.5), // Taille des confettis
        duration: party.variation.range(3000, 5000), // Durée de l'animation
        spread: party.variation.range(60, 120), // Répartition des confettis
      });
    } else {
      console.error("Element with ID 'header-container' not found.");
    }
  };
  // Handle submit reasons form (after choosing reasons).
  const handleSumitReasonsForm = async () => {
    setLoadFinalStage(true);
    try {
      // setLoadUpdateInFinalStages(true);
      let selectedReasons = finalStageForm.getFieldsValue();
      let formData = new FormData();
      formData.append("new_stage_id", selectedStage);
      formData.append("id_element", elementId || contactInfo?.id);
      formData.append("id_reason", selectedReasons?.reasons);
      if (typeof actionDeal === "number")
        formData.append("reason_type", actionDeal);
      else formData.append("reason_type", selectedReasons?.reasonType);
      let response = await MainService.updateElementToFinalStage(
        contactInfo.family_id,
        formData
      );
      if (response?.status === 200) {
        await getStages(contactInfo);

        if (actionDeal === 1) {
          // Trigger the particle animation
          SuccessWon();
        }
        setReasons(null);
        if (actionDeal !== 1) {
          message.success(t("chat.message_system.deal_updated_stage"), 3);
        }

        finalStageForm.resetFields();
        dispatch(setUpdateElementSuccessfully(null));
        setSelectedStage(null);
        setSelectedReasonType(null);
        setActionDeal(null);
        if (activeTab360 == 3)
          dispatch(setNewInteraction({ type: "updateWithoutMercure" }));
      }
    } catch (error) {
      console.log(`Error ${error}`);
      message.error("Something went wrong", 3);
    } finally {
      setLoadFinalStage(false);
    }
  };

  return (
    <>
      {openView360InDrawer ? (
        <Layout>
          {/* <Spin spinning={mountRelation && selectedKey == 4 ? false : loading}> */}
          {loading ? (
            <LoadingViewSphere />
          ) : (
            <HeaderViewSphere
              openView360InDrawer={openView360InDrawer}
              // key="drawerViewSphere"
              from={from}
              loading={loading}
              dataSteps={dataSteps}
              contactInfo={contactInfo}
              reference={reference}
              selectedTags={selectedTags}
              setOpenEmailModal={setOpenEmailModal}
              isUpdate={isUpdate}
              setRefreshInfo={setRefreshInfo}
              setSelectedKey={setSelectedKey}
              setOpenModalCheckList={setOpenModalCheckList}
              setOpenDrawerUpdate={setOpenDrawerUpdate}
              setOpenFields={setOpenFields}
              setOpenPipeline={setOpenPipeline}
              setHeaderHeight={setHeaderHeight}
              channel={channel}
              contactType={contactType}
              detailsInfo={detailsInfo}
              setReasons={setReasons}
              setSelectedTags={setSelectedTags}
              setWindowHeight={setWindowHeight}
              headerHeight={headerHeight}
              windowHeight={windowHeight}
              source={source}
              setSelectedStage={setSelectedStage}
              selectedStage={selectedStage}
              setSelectedReasonType={setSelectedReasonType}
              setActionDeal={setActionDeal}
              actionDeal={actionDeal}
              setLoading={setLoading}
              setIsFinalStage={setIsFinalStage}
              selectedKey={selectedKey}
            />
          )}
          {/* <div className="h-1 bg-white"></div> */}
          <ContentViewSphere
            key={contactInfo.id}
            selectedKey={selectedKey}
            setSelectedKey={setSelectedKey}
            setListConv={setListConv}
            setSelectedItem={setSelectedItem}
            relations={relations}
            collapsed={collapsed}
            contactInfo={contactInfo}
            setRelations={setRelations}
            generalInfo={generalInfo}
            loading={loading}
            headerHeight={headerHeight}
            from={from}
            windowHeight={windowHeight}
            kpi={kpi}
            openFields={openFields}
            setOpenFields={setOpenFields}
            isUpdate={isUpdate}
            setIsUpdate={setIsUpdate}
            dataSteps={dataSteps}
            listConv={listConv}
            tasksTypes={tasksTypes}
            setSelectedKeySideBar={setSelectedKeySideBar}
            countTasks={countTasks}
            setTasksTypes={setTasksTypes}
            setCountTasks={setCountTasks}
            setKpi={setKpi}
            disabledRelation={disabledRelation}
            selectedItem={selectedItem}
            selectedKeySideBar={selectedKeySideBar}
            mountChat={mountChat}
            setMountchat={setMountchat}
            openTask={openTask}
            setOpenTask={setOpenTask}
            setCollapsed={setCollapsed}
            openDrawerUpdate={openDrawerUpdate}
            setOpenDrawerUpdate={setOpenDrawerUpdate}
            setOpenPipeline={setOpenPipeline}
            openPipeline={openPipeline}
            handleSumitReasonsForm={handleSumitReasonsForm}
            reasons={reasons}
            setReasons={setReasons}
            selectedReasonType={selectedReasonType}
            setSelectedReasonType={setSelectedReasonType}
            selectedStage={selectedStage}
            setSelectedStage={setSelectedStage}
            finalStageForm={finalStageForm}
            actionDeal={actionDeal}
            loadFinalStage={loadFinalStage}
            openView360InDrawer={openView360InDrawer}
            setIsFinalStage={setIsFinalStage}
            refreshInfo={refreshInfo}
            setRefreshInfo={setRefreshInfo}
            setActionDeal={setActionDeal}
            // className="menuViewSphere"
          />{" "}
          {/* </Spin> */}
          {user.rmc_access === "OUI" ? (
            <iframe
              src={`${URL_ENV?.REACT_APP_RMC_URL}?Token=${token}`}
              title="chat"
              display="block"
              width="100%"
              // height= {`${deviceHeight}px -120px`}
              sendbox="allow-same-origin allow-popups"
              allowFullScreen="true"
              style={{
                display: "none",
                border: "none",
              }}
              allowtransparency="true"
              // onLoad={() => setHide(true)}
            ></iframe>
          ) : null}
          {/* <Tooltip
          title={!open ? "Open the list of modules" : ""}
          // id="showMeViewSphere"
          placement="left"
        > */}
          {/* <FloatButton.Group
          open={open}
          trigger="click"
          style={{
            right: 15,
            bottom: 25,
          }}
          // onOpenChange={(e) => setOpen(!open)}
          onClick={() => setOpen(!open)}
          icon={
            <div>
              <ArrowsAltOutlined />
              <Badge
                dot={
                  selectedKey === "5" ||
                  selectedKey === "6" ||
                  (selectedKey === "2" && activeTab360 === 5)
                }
                id="showMeViewSphere"
                className="absolute -top-[10px] "
              />
            </div>
          }
        >
          <Tooltip title="Notes" placement="left">
            <FloatButton
              icon={<FileTextOutlined />}
              // style={{ width: "60px" }}
              className="flex justify-end"
              type={selectedKey === "5" ? "primary" : "default"}
              onClick={() => {
                setSelectedKey("5");
                // setIdModule(4);
              }}
            />
          </Tooltip>
          <Tooltip title={t("layout_profile_details.files")} placement="left">
            <FloatButton
              type={selectedKey === "6" ? "primary" : "default"}
              icon={<FileAddOutlined />}
              onClick={() => {
                setSelectedKey("6");
                // setIdModule(1);
              }}
            />
          </Tooltip>
          <Tooltip title="Email" placement="left">
            <FloatButton
              icon={<MailOutlined />}
              type={openModalEmail ? "primary" : "default"}
              onClick={() => {
                dispatch(setOpenModalEmail(true));
              }}
            />
          </Tooltip>
          <Tooltip title="RMC" placement="left">
            <FloatButton
              icon={<HiOutlineShare />}
              type={
                selectedKey === "2" && activeTab360 === 5 ? "primary" : "default"
              }
              onClick={() => {
                setSelectedKey("2");
                dispatch(setActiveTab360(5));
              }}
            />
          </Tooltip>
        </FloatButton.Group> */}
          {/* {openModalEmail && (
            <Suspense
              fallback={
                <div className="fixed inset-0 z-[9999] flex h-full w-full   items-center justify-center bg-black/5 ">
                  <Loader size="2rem" />
                </div>
              }
            >
              <ModalMessage
                title={t("chat.header.shareWithEmail")}
                setSelectedKey={setSelectedKeySideBar}
                receiver={
                  Array.isArray(contactInfo?.email) &&
                  contactInfo?.email.length > 0
                    ? contactInfo?.email[0]
                    : null
                }
                sender={
                  user?.accounts_email?.length > 0
                    ? user?.accounts_email.find(
                        (item) =>
                          item.primary_account === 1 ||
                          parseInt(item.shared) === 1
                      )?.id
                    : undefined
                }
              />
            </Suspense>
          )} */}
        </Layout>
      ) : null}
    </>
  );
};
export default memo(ViewSphere);
