pipeline {
    agent any
    environment {
        GIT_COMMIT_REV=''
    }
    stages {
        stage('build sphere project') {
            steps {
                sh "git branch"
                script {
                    GIT_COMMIT_REV = sh(returnStdout: true, script: "git log -n 1").trim()
                }
                sh """
                    curl --location 'https://spherechatback.cmk.biz:4543/api/send-message-bot' \
                    --header 'Authorization: zYPC9B3FxUD4KDkQz8fuwwI247iB41rnM0qQWBRWH06GT6Ut2RVUE0xgfUj5' \
                    --form 'bot_id="66a4e7c7f1fab1025b7caefc"' \
                    --form-string 'message=<p style=\"color:gray;\">Sphere Build Started : ${JOB_NAME} [${BUILD_NUMBER}] ${GIT_COMMIT_REV}</p>'
                """
            }
       }
        stage('create dev environment') {
            when {
                branch "dev"
            }
            steps {
                // sh "rm -rf node_modules/"

                sh "npm install --force"
                sh "mv .env.dev .env"
                sh "mv public/env-dev.json public/env.json"
                sh "JENKINS_NODE_COOKIE=dontKillMe  NODE_ENV=production npm run build"
                sh "sudo rm -rf /var/www/html/dev/*"
                sh "sudo cp -r ./build/* /var/www/html/dev"
             //  sh "ssh <EMAIL> 'cd /var/www/html/sphere;sudo chmod -R 777 /var/www/html/sphere;git config --global --add safe.directory /var/www/html/sphere;git remote -v;git remote set-url origin https://khaoula:<EMAIL>./info/front/sphere.git;git config --global user.email \"<EMAIL>\";git config --global user.name \"khaoula.touati\";/git reset --hard;git pull --no-ff'"
            // sh "ssh <EMAIL> 'cd /var/www/html/sphere; sudo chmod -R 777 /var/www/html/sphere; git config --global --add safe.directory /var/www/html/sphere; git remote -v; git remote set-url origin https://khaoula:<EMAIL>/unikcrm/info/front/sphere.git; git config --global user.email "<EMAIL>"; git config --global user.name "khaoula.touati"; git reset --hard; git pull --no-ff'"

                 // sh "ssh <EMAIL> 'cd /var/www/html/sphere; mv .env .env-old '"
                 // sh "ssh <EMAIL> 'cd /var/www/html/sphere; mv sfornt sfront-old  '"
                    sh "ssh <EMAIL> 'cd /var/www/html/sphere; sudo chmod -R 777 /var/www/html/sphere; git config --global --add safe.directory /var/www/html/sphere; git remote -v; git remote set-url origin https://khaoula:<EMAIL>/front/sphere.git; git config --global user.email \"<EMAIL>\"; git config --global user.name \"khaoula.touati\"; git reset --hard; git pull --no-ff'"
                    sh  "ssh  <EMAIL>  'cd  /var/www/html/sphere;  npm  install  --force'"
                   sh "ssh <EMAIL> 'cd /var/www/html/sphere; JENKINS_NODE_COOKIE=dontKillMe  NODE_ENV=production npm run build'"
               // sh "ssh <EMAIL> 'cd /var/www/html/sphere; mv sfornt sfront-old'"
                //sh "ssh <EMAIL> 'cd /var/www/html/sphere; npm install --force'"
                sh "ssh <EMAIL> 'cd /var/www/html/sphere; rm -rf sfront-old '"
               // sh "ssh <EMAIL> 'cd /var/www/html/sphere; cp public/env.json build/'"
                //sh "ssh <EMAIL> 'cd /var/www/html/sphere; JENKINS_NODE_COOKIE=dontKillMe  NODE_ENV=production npm run build'"
                sh "ssh <EMAIL> 'cd /var/www/html/sphere; mv sfront sfront-old'"
               // sh "ssh <EMAIL> 'cd /var/www/html/sphere; cp -R sfornt-old/env.json  build/env.json '"
                 // sh "ssh <EMAIL> 'cd /var/www/html/sphere; rm sfront.old'"
                   sh "ssh <EMAIL> 'cd /var/www/html/sphere; mv build sfront'"
                     //sh "ssh <EMAIL> 'cd /var/www/html/sphere; rm -rf  sfront.old; mv sfront sfront.old; mv build sfront'"
               // sh "sudo rm -rf /var/www/html/dev/*"
              // sh "sudo scp -r ./build/* <EMAIL>:/var/www/html/sphere/sfront/"
            }
       }
       stage('create devtenancy environment') {
               when {
                   branch "devtenancy"
               }
               steps {
                    sh "ssh <EMAIL> 'cd /var/www/html/sphere; sudo chmod -R 777 /var/www/html/sphere; git config --global --add safe.directory /var/www/html/sphere; git remote -v; git reset --hard; git pull --no-ff'"
                    sh  "ssh  <EMAIL>  'cd  /var/www/html/sphere;  npm  install  --force'"
                    sh "ssh <EMAIL> 'cd /var/www/html/sphere; JENKINS_NODE_COOKIE=dontKillMe  NODE_ENV=production npm run build'"
                    sh "ssh <EMAIL> 'cd /var/www/html/sphere; rm -rf sfront-old || true'"
                    sh "ssh <EMAIL> 'cd /var/www/html/sphere; mv sfront sfront-old || true'"
                    sh "ssh <EMAIL> 'cd /var/www/html/sphere; mv build sfront'"
               }
          }
       stage('create prod environment') {
            when {
                branch "release"
            }
            //TODO VERFIY SPHEREEE
            steps {
                sh "ssh <EMAIL> 'cd /var/www/html/sphere;sudo chmod -R 777 /var/www/html/sphere;git config --global --add safe.directory /var/www/html/sphere;git remote -v;git reset --hard;git pull origin release --no-ff;mv .env.prod .env; npm i --force; NODE_ENV=production npm run build;ls -la; rm -rf sfront_old; mv sfront sfront_old; mv build sfront;'"
            }
       }
    }
    post {
        success {
            sh """
                curl --location 'https://spherechatback.cmk.biz:4543/api/send-message-bot' \
                    --header 'Authorization: zYPC9B3FxUD4KDkQz8fuwwI247iB41rnM0qQWBRWH06GT6Ut2RVUE0xgfUj5' \
                    --form 'bot_id="66a4e7c7f1fab1025b7caefc"' \
                    --form-string 'message=<p style=\"color:green;\">Sphere ${GIT_BRANCH} Build Success : ${GIT_COMMIT_REV}</p>'
            """
        }
        failure {
            sh """
               curl --location 'https://spherechatback.cmk.biz:4543/api/send-message-bot' \
                    --header 'Authorization: zYPC9B3FxUD4KDkQz8fuwwI247iB41rnM0qQWBRWH06GT6Ut2RVUE0xgfUj5' \
                    --form 'bot_id="66a4e7c7f1fab1025b7caefc"' \
                    --form-string 'message=<p style=\"color:red;\">Sphere ${GIT_BRANCH} Build Failure : ${GIT_COMMIT_REV}</p>'
            """
        }
    }
 }
