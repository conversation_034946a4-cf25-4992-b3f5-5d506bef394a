import { CheckOutlined, CloseOutlined } from "@ant-design/icons";
import { Button, Radio, Space } from "antd";
import { colors } from "./Colors";

const GroupColors = ({ color, setColor, size }) => {
  return (
    <Space>
      <Radio.Group
        onChange={(e) => setColor(e.target.value)}
        optionType="button"
        size={size == undefined ? "middle" : size} 
      >
        {colors.map((el) => (
          <Radio.Button value={el.value} key={el.value} style={{ background: el.value }}>
            {color === el.value ? (
              <CheckOutlined
                style={{
                  color: el.value === "#f3f4f6" ? "black" : "white",
                }}
              />
            ) : (
              <span>
                <CheckOutlined style={{ color: el.value }} />
              </span>
            )}
          </Radio.Button>
        ))}
      </Radio.Group>
      {color ? (
        <Button type="text" shape="circle" size="small" onClick={() => setColor("")}>
          <CloseOutlined />
        </Button>
      ) : (
        ""
      )}
    </Space>
  );
};

export default GroupColors;
