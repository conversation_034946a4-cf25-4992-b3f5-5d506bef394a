import { useState, useRef, useEffect } from "react";
import { Card, Button, Space, Typography, Avatar, Rate, Empty } from "antd";
import { ArrowRightOutlined } from "@ant-design/icons";
import { CustomTag } from "pages/clients&users/components/RenderColumnsTable";
import { URL_ENV } from "index";

function GeneralInfosGridView({
  user,
  height,
  generalInfoInDahsboard,
  generalInfo,
  setSelectedKey,
  setOpenFields,
  t,
}) {
  const [showAll, setShowAll] = useState(false);
  const [needsCollapse, setNeedsCollapse] = useState(false);
  const contentRef = useRef(null);
  function isObject(variable) {
    return (
      variable !== null &&
      typeof variable === "object" &&
      !Array.isArray(variable)
    );
  }
  useEffect(() => {
    if (contentRef.current) {
      const isOverflowing =
        contentRef.current.scrollHeight > contentRef.current.clientHeight;
      setNeedsCollapse(isOverflowing);
    }
  }, [generalInfoInDahsboard, showAll]);

  const toggleShowAll = () => {
    setShowAll(!showAll);
  };

  const cardHeight = user?.role === "guest" ? height - 77 : 192;

  return (
    <Card
      style={{
        width: "100%",
        height: showAll ? "auto" : cardHeight,
        overflow: "hidden",
        marginBottom: user?.role === "guest" ? 3 : 0,
        position: "relative",
      }}
    >
      <div
        ref={contentRef}
        className="space-y-2"
        style={{
          maxHeight: showAll ? "none" : cardHeight - 50,
          overflow: "hidden",
        }}
      >
        {Object.keys(generalInfoInDahsboard).length > 0 ? (
          Object.entries(generalInfoInDahsboard)
            .filter(([label, { value }]) => {
              return (
                value !== null &&
                value !== undefined &&
                !(Array.isArray(value) && value.length === 0) &&
                !(typeof value === "object" && Object.keys(value).length === 0)
              );
            })
            .map(([key, { type, value }]) => (
              <div className="flex flex-row items-center space-x-2" key={key}>
                <span
                  style={{
                    // width: `${(key?.length + 1) * 10}px`,
                    color: "rgba(0, 0, 0, 0.45)",
                  }}
                >
                  {key}:
                </span>
                {/* Le reste de votre rendu des champs */}
                {type === "checkbox" ||
                type === "select" ||
                type === "radio" ||
                type === "multiselect" ||
                type === "autocomplete" ? (
                  <Space size={[1, 2]} wrap>
                    {Array.isArray(value) ? (
                      value?.map((item) => (
                        <CustomTag content={item} color="#4f46e5" />
                      ))
                    ) : isObject(value) ? (
                      Object.values(value)?.map((item) => (
                        <CustomTag content={item} color="#4f46e5" />
                      ))
                    ) : typeof value === "string" ? (
                      <CustomTag content={value} color="#4f46e5" />
                    ) : null}
                  </Space>
                ) : type === "phone" &&
                  typeof value[0] === "string" &&
                  typeof value[1] === "string" ? (
                  <Typography.Text>{`(${
                    typeof value[0] === "string" ? value[0] : ""
                  }) ${
                    typeof value[1] === "string" ? value[1] : ""
                  }`}</Typography.Text>
                ) : type === "image" ? (
                  <Avatar
                    size={25}
                    src={`${
                      URL_ENV?.REACT_APP_BASE_URL +
                      URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                    }${value}`}
                  />
                ) : type === "file" ? (
                  <div className="flex flex-col space-y-1">
                    {value?.map((file, i) => (
                      <a
                        key={i}
                        className="truncate"
                        style={{
                          width: `14rem`,
                        }}
                        href={`${
                          URL_ENV?.REACT_APP_BASE_URL +
                          URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                        }${file}`}
                        target="_blank"
                        rel="noreferrer"
                      >
                        {file}
                      </a>
                    ))}
                  </div>
                ) : type === "rate" ? (
                  <Rate allowHalf disabled defaultValue={Number(value)} />
                ) : (
                  <Typography.Text className="truncate">
                    {Array.isArray(value)
                      ? value.map((el) => el + " ")
                      : typeof value === "string" || typeof value === "number"
                      ? value
                      : null}
                  </Typography.Text>
                )}
                {/* Le reste de votre logique d'affichage */}
              </div>
            ))
        ) : (
          <div>
            <Empty />
            <div className="flex justify-center whitespace-break-spaces">
              <Button type="link" onClick={() => setOpenFields(true)}>
                <span className="whitespace-break-spaces">
                  {t("vue360.noInfoGeneral")}
                </span>
              </Button>
            </div>
          </div>
        )}
      </div>

      {(needsCollapse || showAll) && (
        <div
          style={{
            textAlign: "center",
            marginTop: 0,
            position: "relative",
            top: "2px",
          }}
        >
          <Button type="link" onClick={toggleShowAll}>
            {showAll
              ? t("vue360.showLess") + " ."
              : t("vue360.showMore") + " ..."}
          </Button>
        </div>
      )}

      {Object.keys(generalInfo)?.length ? (
        <div className="mt-2 flex justify-end">
          <Typography.Link onClick={() => setSelectedKey("4")}>
            {t("voip.moreInfo")} <ArrowRightOutlined />
          </Typography.Link>
        </div>
      ) : null}
    </Card>
  );
}

export default GeneralInfosGridView;
