import { Button, Tour } from "antd";
import { memo, useState } from "react";

export const CustomTour = memo(({ steps, onClose, t, ...props }) => {
  //
  const [current, setCurrent] = useState(0);
  //
  const closeTour = () => {
    onClose();
    setCurrent(0);
  };
  const renderFooter = (idx, tot) => (
    <div className="flex h-6 w-full flex-grow items-center justify-between space-x-3">
      <div style={{ display: "flex", gap: 4 }}>
        {Array.from({ length: tot }).map((_, i) => (
          <span
            key={i}
            style={{
              width: 6,
              height: 6,
              borderRadius: "50%",
              background: i === idx ? "#1890ff" : "#ccc",
            }}
          />
        ))}
      </div>

      {idx < tot - 1 ? (
        <Button type="" size="small" onClick={closeTour}>
          {t("tours.skip")}
        </Button>
      ) : (
        <div style={{ width: 60 }} />
      )}
    </div>
  );

  return (
    <Tour
      {...props}
      steps={steps.map((step, index) => ({
        ...step,
        nextButtonProps: {
          children:
            index === steps.length - 1 ? t("tour.finish") : t("tour.next"),
        },
        prevButtonProps: {
          children: t("tour.prev"),
        },
      }))}
      current={current}
      onChange={setCurrent}
      onClose={closeTour}
      indicatorsRender={renderFooter}
      zIndex={9999}
    />
  );
});
