import { Card } from "antd";
import React from "react";

const CardStat = ({ title, subtitle = "", children, extra = "" }) => {
  return (
    <Card
      title={
        <div className="flex flex-col">
          <div
            style={{
              fontSize: "1.2em",
              color: "rgb(51, 51, 51)",
              fontWeight: "bold",
              fill: "rgb(51, 51, 51)",
            }}
          >
            {title}
          </div>
          <div className="text-gray-500 ">{subtitle}</div>
        </div>
      }
      extra={extra}
    >
      {children}
    </Card>
  );
};

export default CardStat;
