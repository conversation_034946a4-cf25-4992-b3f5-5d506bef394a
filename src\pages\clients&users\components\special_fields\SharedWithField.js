import { memo, useEffect, useState } from "react";
import { Divider, Radio, Select, Space } from "antd";
import {
  ApartmentOutlined,
  LockOutlined,
  UnlockOutlined,
} from "@ant-design/icons";
import DisplayAvatar from "pages/voip/components/DisplayAvatar";
import { URL_ENV } from "index";

const SharedWithField = ({
  userId,
  form,
  selectedValue,
  departments,
  t,
  setSubmitIsDisable = () => {},
}) => {
  //
  const [sharedWith, setSharedWith] = useState(() => {
    if (selectedValue) {
      return Array.isArray(selectedValue)
        ? "departments"
        : selectedValue?.constructor === Object
        ? selectedValue?._id
        : selectedValue;
    }
    return "all";
  });
  const [selectedDepartments, setSelectedDepartments] = useState(() => {
    if (Array.isArray(selectedValue)) {
      return selectedValue;
    }
    return [];
  });
  //
  // Update the instance "form" when initialization
  useEffect(() => {
    if (selectedValue) {
      if (Array.isArray(selectedValue))
        form.setFieldValue("sharedWith", selectedValue?.join(","));
      else if (selectedValue?.constructor === Object)
        form.setFieldValue("sharedWith", selectedValue?._id);
      else form.setFieldValue("sharedWith", selectedValue);
    } else form.setFieldValue("sharedWith", "all");
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  //
  const handleRadioChange = (event) => {
    setSubmitIsDisable(false);
    const value = event.target.value;
    setSharedWith(value);
    if (value === "departments") {
      if (!selectedDepartments.length) setSubmitIsDisable(true);
      if (selectedDepartments.length)
        form.setFieldValue("sharedWith", value?.join(","));
      else form.setFieldValue("sharedWith", undefined);
    } else form.setFieldValue("sharedWith", value);
  };
  //
  const handleSelectChange = (values) => {
   // console.log({ values });
    values.length ? setSubmitIsDisable(false) : setSubmitIsDisable(true);
    setSelectedDepartments(values);
    form.setFieldValue("sharedWith", values);
  };
  //
  return (
    <div className="relative w-full">
      <Divider orientation="left" style={{ margin: "10px 0 10px -35px" }}>
        <span className="flex font-semibold">{t("voip.sharedWith")}</span>
      </Divider>
      <div className="relative flex w-full flex-row space-x-0">
        <div className="flex  self-center">
          <Radio.Group
            size="small"
            value={sharedWith}
            onChange={handleRadioChange}
          >
            <Radio value="all">
              <Space size={3}>
                <UnlockOutlined style={{ fontSize: 14 }} />
                <span>{t("voip.all")}</span>
              </Space>
            </Radio>
            <Radio
              value={userId}
              disabled={
                !!selectedValue &&
                selectedValue?.constructor === Object &&
                selectedValue?._id !== userId
              }
            >
              <Space size={3}>
                <LockOutlined style={{ fontSize: 14 }} />
                {!!selectedValue &&
                selectedValue?.constructor === Object &&
                selectedValue?._id !== userId ? (
                  <DisplayAvatar
                    tooltip={true}
                    name={selectedValue?.label}
                    size={30}
                    cursor={"help"}
                    urlImg={
                      !!selectedValue?.avatar &&
                      `${
                        URL_ENV?.REACT_APP_BASE_URL +
                        URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                      }${selectedValue?.avatar}`
                    }
                  />
                ) : (
                  <span>{t("voip.onlyMe")}</span>
                )}
              </Space>
            </Radio>
            <Radio value="departments">
              <Space size={3}>
                <ApartmentOutlined style={{ fontSize: 14 }} />
                <span>{t("voip.departments")}</span>
              </Space>
            </Radio>
          </Radio.Group>
        </div>
        <div className="grow">
          <Select
            style={{
              width: "100%",
            }}
            allowClear
            mode="multiple"
            maxTagCount="responsive"
            optionFilterProp="label"
            placeholder={t("voip.selectDepartments")}
            value={selectedDepartments}
            onChange={handleSelectChange}
            disabled={sharedWith !== "departments"}
            filterSort={(optionA, optionB) =>
              (optionA?.label ?? "")
                .toLowerCase()
                .localeCompare((optionB?.label ?? "").toLowerCase())
            }
            options={departments?.map((department) => ({
              value: department.id,
              label: department.label,
            }))}
          />
        </div>
      </div>
    </div>
  );
};

export default memo(SharedWithField);
