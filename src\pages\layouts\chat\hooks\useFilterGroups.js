import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import {
  sortDataList,
  getUserFromMsg,
  uuid,
  isGuestRoom,
  isRoom,
} from "../utils/ConversationUtils";
import { globalSearch } from "new-redux/services/chat.services";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { SET_NEW_UPDATE_ON_MESSAGE } from "new-redux/constants";
import { FilterSidebarType } from "new-redux/reducers/chatReducer";
import { store } from "new-redux/store";

const initialPage = {
  users: 1,
  conversations: 1,
  messages: 1,
};
const initialLoadMore = {
  users: false,
  conversations: false,
  messages: false,
};
const initialHasMore = {
  users: false,
  conversations: false,
  messages: false,
};
const initialData = {
  users: [],
  conversations: [],
  messages: [],
};
const initialSearch = {
  loading: false,
  hasMore: initialHasMore,
  loadingMore: initialLoadMore,
  page: initialPage,
  keyword: "",
  filter: "all",
  data: initialData,
};
const initialDataState = {
  conversations: [],
  users: [],
  messages: [],
};

function useFilterGroups({ inViewLastElement }) {
  const {
    searchChatSideBar,
    activeFiltersDiscussion,
    sidebarDrawer,
    membersGroupsChat,
    currentUser,
    archivedList,
  } = useSelector((state) => state.chat);
  const [searchMeta, setSearchMeta] = useState(initialSearch);
  const [membersGroupsFilteredChat, setFiltredChat] =
    useState(initialDataState);
  const [archivedListFiltred, setArchivedFiltredChat] =
    useState(initialDataState);

  const dispatch = useDispatch();
  const { t } = useTranslation("common");
  const setDataList = (list) => {
    if (sidebarDrawer === "chat") setFiltredChat(list);
    else setArchivedFiltredChat(list);
  };
  const isMatch = (item, condition) => {
    try {
      switch (condition) {
        case FilterSidebarType.member:
          return !isRoom(item);
        case FilterSidebarType.room:
          return isRoom(item) && !isGuestRoom(item);
        case FilterSidebarType.guest:
          return isGuestRoom(item);
        default:
          return false;
      }
    } catch (e) {
      return false;
    }
  };
  const filterAndSortData = (data = []) => {
    let filteredData = [];
    if (activeFiltersDiscussion?.length === 0) {
      filteredData = [];
    } else if (
      activeFiltersDiscussion?.length ===
      Object.values(FilterSidebarType).length
    ) {
      filteredData = [...data];
    } else {
      filteredData = data.filter((item) =>
        activeFiltersDiscussion?.some((filter) => isMatch(item, filter))
      );
    }

    filteredData = sortDataList(
      filteredData,
      currentUser?.config?.sort_message,
      "last_message_date"
    );
    setDataList({
      conversations: [...filteredData],
      messages: [],
      users: [],
    });
  };

  /** ----------------------------------> GLOBAL SEARCH PART <---------------------------------- **/

  useEffect(() => {
    let mount = true;
    let timeout = null;
    const abortController = new AbortController();

    const fetchGlobalSearchData = async (params) => {
      // throw new Error("Not implemented yet");
      const data = await dispatch(globalSearch(params));
      // get user data
      const users = !Array.isArray(data?.users?.data)
        ? []
        : data?.users?.data.map((item) => ({
            _id: uuid(),
            contact: { ...item },
            type_item: "users",
          }));
      // get messages data
      const messages = !Array.isArray(data?.messages?.data)
        ? []
        : data?.messages?.data?.map((item) => ({
            conversation_id: item.conversation_id,
            _id: uuid(),
            bot: item.bot,
            contact:
              getUserFromMsg(
                item.receiver_id === currentUser?._id
                  ? item.sender_id
                  : item.receiver_id
              ) ?? null,
            sender: getUserFromMsg(item.sender_id),
            room: item.room_info ?? null,
            message_id: item._id,
            last_message: item.deleted_at
              ? null
              : {
                  _id: item._id,
                  type: item.type,
                  unread: item.unread,
                  message: item.message,
                },
            reaction: null,
            last_message_date: item.created_at,
            type_item: "messages",
          }));
      setSearchMeta((p) => {
        let prev = { ...p };
        prev = {
          ...prev,
          keyword: searchChatSideBar,
          loading: false,
          hasMore: {
            users:
              data?.users?.meta?.current_page < data?.users?.meta?.last_page,
            conversations:
              data?.conversations?.meta?.current_page <
              data?.conversations?.meta?.last_page,
            messages:
              data?.messages?.meta?.current_page <
              data?.messages?.meta?.last_page,
          },
          filter: !params.filter ? "all" : prev.filter,
          data: {
            messages: [...prev.data.messages, ...messages],
            users: [...prev.data.users, ...users],
            conversations: [
              ...prev.data.conversations,
              ...data?.conversations?.data,
            ],
          },
        };
        setDataList(prev.data);
        return prev;
      });
      // set the value from searchMeta data
    };
    const handleGlobalSearch = async () => {
      try {
        if (!searchChatSideBar) {
          setSearchMeta(initialSearch);
          return;
        }
        if (searchChatSideBar !== searchMeta.keyword) {
          setDataList(initialData);
          setSearchMeta((p) => ({
            ...p,
            hasMore: initialHasMore,
            page: initialPage,
            keyword: "",
            filter: "all",
            data: initialData,
          }));
        }

        setSearchMeta((p) => ({
          ...p,
          loadingMore: {
            ...p.loadingMore,
            [p.filter]: p.page[p.filter] > 1,
          },
          loading: p.filter === "all" || p.page[p.filter] === 1 ? true : false,
        }));

        const params = {
          errorText: t("toasts.errorFetchApi"),
          signal: abortController.signal,
          page:
            searchMeta.filter === "all"
              ? 1
              : searchMeta.page[searchMeta.filter],
          filter: searchMeta.filter === "all" ? "" : searchMeta.filter,
          keyword: searchChatSideBar,
        };
        await fetchGlobalSearchData(params);
      } catch (e) {
        console.log(e);
      } finally {
        setSearchMeta((p) => ({
          ...p,
          loadingMore: initialLoadMore,
          loading: false,
        }));
      }
    };

    handleGlobalSearch();
    return () => {
      if (mount) {
        setSearchMeta((p) => ({
          ...p,
          loadingMore: initialLoadMore,
          loading: false,
        }));
      }
      mount = false;
      abortController.abort();
      clearTimeout(timeout);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sidebarDrawer, searchChatSideBar, searchMeta.page]);

  // pagination next page
  useEffect(() => {
    if (inViewLastElement) {
      setSearchMeta((prevMeta) => ({
        ...prevMeta,
        page: {
          ...prevMeta.page,
          [prevMeta.filter]: prevMeta.page[prevMeta.filter] + 1,
        },
      }));
    }
  }, [inViewLastElement]);
  // filter and sort data on filter change.
  useEffect(() => {
    let mounted = true;

    if (mounted && searchChatSideBar === "") {
      filterAndSortData(
        sidebarDrawer === "chat" ? membersGroupsChat : archivedList
      );
      setSearchMeta(initialSearch);
    }

    return () => {
      mounted = false;
    };
  }, [
    archivedList,
    membersGroupsChat,
    searchChatSideBar,
    activeFiltersDiscussion,
    currentUser?.config?.sort_message,
    sidebarDrawer,
  ]);
  useEffect(() => {
    const abort = new AbortController();

    const updateConversationInList = (
      conversations,
      conversationId,
      updatedConversation
    ) => {
      return conversations.map((conv) =>
        conv._id === conversationId ? updatedConversation : conv
      );
    };

    const handleUpdateConversation = async (event) => {
      const conversationId = event.detail?.conversation_id;

      if (!conversationId) return;

      const storeState = await store.getState().chat;
      const isChatTab = sidebarDrawer === "chat";

      const updatedConversation = isChatTab
        ? storeState.membersGroupsChat.find(
            (item) => item._id === conversationId
          )
        : storeState.archivedList.find((item) => item._id === conversationId);

      if (!updatedConversation) return;

      const newData = {
        ...searchMeta.data,
        conversations: updateConversationInList(
          searchMeta.data.conversations,
          conversationId,
          updatedConversation
        ),
      };

      setDataList(newData);

      setSearchMeta((prev) => ({
        ...prev,
        data: newData,
      }));
    };

    window.addEventListener(
      SET_NEW_UPDATE_ON_MESSAGE,
      handleUpdateConversation,
      {
        signal: abort.signal,
      }
    );

    return () => {
      abort.abort();
    };
  }, [searchMeta.data, sidebarDrawer]);

  return {
    membersGroupsFilteredChat,
    archivedListFiltred,
    searchMeta,
    setSearchMeta,
  };
}

export default useFilterGroups;
