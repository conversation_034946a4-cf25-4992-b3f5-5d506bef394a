import React, { useContext, useEffect, useRef, useState } from "react";
import { Button, Form, Input, Popconfirm, Table } from "antd";
import { DeleteOutlined, EditOutlined, PlusOutlined } from "@ant-design/icons";
import { StopOutlined } from "@ant-design/icons";

import "./EditableTable.css";

const EditableContext = React.createContext(null);

const EditableTable = ({
  form,
  dataSource,
  setDataSource,
  updateFieldProps,
}) => {
  // console.log("data_source", dataSource);

  const [editing, setEditing] = useState(false);
  const inputRef = useRef(null);

  const handleInputOptionsChange = (e, index) => {
    console.log("handleInputOptionsChange index", index);
    let newInputValues = [...dataSource];
    console.log("newInputValues", newInputValues);
    index = e.target.value;
    console.log("right element", index);
    setDataSource(newInputValues);
  };

  const EditableRow = ({ ...props }) => {
    // console.log("EditableRow", props);
    // const [form] = Form.useForm();
    return (
      <>
        {/* <Form.List form={form} component={false}> */}
        {console.log("EditableContext form props", props)}
        <EditableContext.Provider value={form}>
          <tr {...props} />
        </EditableContext.Provider>
        {/* </Form.List> */}
      </>
    );
  };

  const EditableCell = ({
    title,
    editable,
    children,
    dataIndex,
    record,
    handleSave,
    ...restProps
  }) => {
    // console.log("record", record);
    const form = useContext(EditableContext);
    useEffect(() => {
      if (dataSource !== null && editing) {
        inputRef.current.focus();
      }
    }, [editing]);
    const toggleEdit = () => {
      setEditing(!editing);
      form.setFieldsValue({
        [dataIndex]: record[dataIndex],
      });
    };
    const save = async () => {
      try {
        const values = await form.validateFields();
        toggleEdit();
        handleSave({
          ...record,
          ...values,
        });
      } catch (errInfo) {
        console.log("Save failed:", errInfo);
      }
    };
    let childNode = children;
    if (editable) {
      childNode = editing ? (
        <Form.Item
          style={{
            margin: 0,
          }}
          name={dataIndex}
          rules={[
            {
              required: true,
              message: `option field is required.`,
            },
          ]}
        >
          <Input
            ref={inputRef}
            onPressEnter={save}
            onBlur={save}
            // value={Object.keys(updateFieldProps).length > 0 && console.log("children", childNode)}
            // onChange={(e) => handleInputOptionsChange(e, dataIndex)}
          />
        </Form.Item>
      ) : (
        <div
          className="editable-cell-value-wrap"
          style={{
            paddingRight: 24,
          }}
          onClick={toggleEdit}
        >
          {children}
        </div>
      );
    }
    return <td {...restProps}>{childNode}</td>;
  };

  const [count, setCount] = useState(0);
  const handleDelete = (key) => {
    // const newData = dataSource.filter((item) => item.id !== key);
    // setDataSource(newData);
    let newData = [...dataSource];
    newData.splice(key, 1);
    setDataSource(newData);
  };
  const defaultColumns = [
    {
      // key: count,
      title: "Option Value",
      dataIndex: `ListElementValue`,
      editable: true,
    },
    {
      title: "Actions",
      dataIndex: "actions",
      width: "30%",
      render: (_, record, i) =>
        dataSource.length >= 1 ? (
          <div style={{ display: "flex", justifyContent: "space-around" }}>
            {/* <EditOutlined
              className="text-lg text-yellow-500 hover:text-yellow-700 cursor-pointer"
              onClick={() => setEditing(true)}
            /> */}
            <Popconfirm
              title="Sure to delete?"
              onConfirm={() => handleDelete(i)}
            >
              <DeleteOutlined className="cursor-pointer text-lg text-red-500 hover:text-red-700" />
            </Popconfirm>
          </div>
        ) : null,
    },
  ];
  const handleAdd = () => {
    const newData = {
      ListElementValue: "",
    };
    setDataSource([...dataSource, newData]);
    setCount(count++);
  };
  const handleSave = (row) => {
    const newData = [...dataSource];
    const index = newData.findIndex((item) => row.key === item.id);
    const item = newData[index];
    newData.splice(index, 1, {
      ...item,
      ...row,
    });
    setDataSource(newData);
  };
  const components = {
    body: {
      row: EditableRow,
      cell: EditableCell,
    },
  };
  const columns = defaultColumns.map((col) => {
    if (!col.editable) {
      return col;
    }
    // console.log("col", col);
    return {
      ...col,
      onCell: (record) => ({
        record,
        // id: col.id,
        editable: col.editable,
        dataIndex: col.dataIndex,
        title: col.title,
        handleSave,
      }),
    };
  });

  let empty = {
    emptyText: (
      <span className="flex items-center justify-center">
        <p className="text-xl text-gray-400">
          <StopOutlined className="ml-10" />
          No Option
        </p>
      </span>
    ),
  };

  // console.log("array", dataSource);

  return (
    <div>
      <Button
        onClick={handleAdd}
        type="primary"
        style={{
          marginBottom: 16,
          float: "right",
        }}
        icon={<PlusOutlined />}
      >
        Add an option
      </Button>
      <Table
        components={components}
        rowClassName={() => "editable-row"}
        bordered
        dataSource={dataSource}
        columns={columns}
        pagination={false}
        locale={empty}
      />
    </div>
  );
};

export default EditableTable;
