import {
  SET_MENU_TITLE,
  SET_OPEN_TAG,
  SET_OPEN_ACTIVITY,
  SET_OPEN_CONFIGMAIL,
  SET_SEARCH,
  RESET_STATE,
  SET_INVALID_CONFIGMAIL,
  SET_FAMILYID,
  SET_END_BUILD,
  SET_COLLAPSE_INNER_MENU,
  SET_OPEN_TOUR,
} from "../constants";

const initialState = {
  menu: "",
  openConfigMail: false,
  invalidConfigMail: [],
  family_id: { familyId: 4, typeFamily: 1 },
  endBuild: true,
  isMenuCollapsed: false,
  isOpenTour: true,
};

const menu = (state = initialState, action) => {
  const { type, payload } = action;
  switch (type) {
    case SET_MENU_TITLE:
      return {
        ...state,
        menu: payload,
      };
    case SET_OPEN_TAG:
      return {
        ...state,
        openTag: payload,
      };
    case SET_OPEN_ACTIVITY:
      return {
        ...state,
        openActivity: payload,
      };
    case SET_OPEN_CONFIGMAIL:
      return {
        ...state,
        openConfigMail: payload,
      };
    case SET_END_BUILD:
      return {
        ...state,
        endBuild: payload,
      };
    case SET_SEARCH:
      return {
        ...state,
        search:
          payload.trim() === ""
            ? ""
            : payload.indexOf("  ") === -1
            ? payload
            : null,
      };
    case SET_INVALID_CONFIGMAIL:
      return {
        ...state,
        invalidConfigMail: payload,
      };
    case SET_FAMILYID:
      return {
        ...state,
        family_id: payload,
      };
    case SET_COLLAPSE_INNER_MENU:
      return {
        ...state,
        isMenuCollapsed: payload,
      };
    case SET_OPEN_TOUR:
      return {
        ...state,
        isOpenTour: payload,
      };
    case RESET_STATE: {
      return initialState;
    }
    default:
      return state;
  }
};

export default menu;
