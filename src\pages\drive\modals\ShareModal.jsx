import React, { useState, useEffect, useCallback } from "react";
import { Modal, Button, Select, Spin, message, Radio, Form, Popconfirm } from "antd";
import {  CloseOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { useInfiniteQuery,useMutation,useQuery, useQueryClient } from "@tanstack/react-query";
import { debounce } from "lodash";
import DisplayAvatar from "pages/voip/components/DisplayAvatar";
import { URL_ENV } from "index";
import { shareDriveItem, getSharedDriveItem, removeSharedDriveItem } from "../../../services/main.service";
import  MainService  from "../../../services/main.service";
import { useInView } from "react-intersection-observer";

const ShareModal = ({
  isVisible,
  onCancel,
  onShare,
  itemToShare,
  loading = false,
}) => {
  const client = useQueryClient();
  const [t] = useTranslation("common");
  const [selectedUser, setSelectedUser] = useState(null);
  const [selectedPermission, setSelectedPermission] = useState(null);
  const [debouncedSearchValue, setDebouncedSearchValue] = useState("");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const { ref, inView } = useInView();
  const [form] = Form.useForm();

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((value) => {
      setDebouncedSearchValue(value);
    }, 600),
    []
  );

  useEffect(() => {
    return () => {
      client.invalidateQueries({ queryKey: ["guests"] });
      setIsDropdownOpen(false);
    };
  }, []);


const shareDrive = useMutation({
  mutationFn: ({id, value}) => shareDriveItem(id, value),
  onSuccess: () => {
    client.invalidateQueries([["drive-items"]], { exact: false });
    message.success(t("drive.shareModal.shareSuccess"));
    onCancel();
  },
  onError: (error) => {
    message.error(error.response.data.message);
  },
});

  const {
    data: guestsData,
    isLoading: loadGuests,
    isFetching: isFetchingGuests,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch,
  } = useInfiniteQuery({
    queryKey: ["guests", debouncedSearchValue],
    queryFn: async ({ pageParam = 1 }) => {
      const formData = new FormData();
      formData.append("family_id", [1, 2, 4, 9]);
      formData.append("search", debouncedSearchValue);

      const response = await MainService.getFamilyOptions(
        pageParam,
        50,
        formData
      );

      return {
        data: response?.data?.data || [],
        meta: response?.data?.meta || {},
        currentPage: pageParam,
      };
    },
    getNextPageParam: (lastPage) => {
      const { current_page, last_page } = lastPage.meta;
      return current_page < last_page ? current_page + 1 : undefined;
    },
    enabled: isDropdownOpen,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });

  const { data: sharedItemDetails,isFetching: isSharedLoading , isError: isSharedError } = useQuery({
    queryKey: ["shared-drive-item-details", itemToShare.id],
    queryFn: () => getSharedDriveItem(itemToShare.id),
    enabled: !!itemToShare.id && isVisible,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
  });

  const removeSharedMutation = useMutation({
    mutationFn: ({id, value}) => removeSharedDriveItem(id, value),
    onSuccess: () => {
      message.success(t("drive.shareModal.removeSuccess"));
      client.invalidateQueries( ["shared-drive-item-details", itemToShare.id] );
    },
  });

  const guestsList = guestsData?.pages?.flatMap((page) => page.data) || [];


  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage && isDropdownOpen) {
      fetchNextPage();
    }
  }, [inView, hasNextPage, isFetchingNextPage, isDropdownOpen, fetchNextPage]);

  useEffect(() => {
    if (isVisible) {
      setSelectedUser(null);
      setSelectedPermission(null);
    
      setDebouncedSearchValue("");
      setIsDropdownOpen(false);

      client.resetQueries({ queryKey: ["guests"] });
    }
  }, [isVisible, client]);

  useEffect(() => {
    if (!isVisible) {
  
      client.resetQueries({ queryKey: ["guests"] });
      setIsDropdownOpen(false);
    
      setDebouncedSearchValue("");
    }
  }, [isVisible, client]);


  const handleUserSearch = (value) => {
   
    debouncedSearch(value);
  };

  
  const handleDropdownVisibleChange = (open) => {
    setIsDropdownOpen(open);
    if (open) {
      client.resetQueries({ queryKey: ["guests", debouncedSearchValue] });
    }
    if (!open) {

     
      setDebouncedSearchValue("");
    }
  };

  const renderUserOption = (user, index) => (
    <div className="flex flex-col gap-2">
      <div className="flex items-center space-x-3 py-1">
        <DisplayAvatar
          name={user.label}
          size={24}
          urlImg={
            user.avatar &&
            URL_ENV?.REACT_APP_BASE_URL +
              URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
              user.avatar
          }
        />
        <div className="flex-1">
          <div className="font-medium">{user.label}</div>
          <div className="text-xs text-gray-500">{user.email}</div>
        </div>
        <div className="text-xs text-gray-400">{user.family_name}</div>
      </div>
      {index === guestsList.length - 1 && hasNextPage && (
        <div ref={ref} className="flex justify-center p-2">
          <Spin size="small" spinning={isFetchingNextPage} />
        </div>
      )}
    </div>
  );

  const permissionOptions = [
    {
      value: "viewer",
      label: t("drive.shareModal.viewer"),
      title: t("drive.shareModal.viewerDescription"),
    },
    {
      value: "editor",
      label: t("drive.shareModal.editor"),
      title: t("drive.shareModal.editorDescription"),
    },
  ];



  const handleShare = () => {
    form.validateFields()?.then((values) => {
   
      const formData = new FormData();
      formData.append("share_with", selectedUser.key);
      formData.append("permission_level", values.permission);
      shareDrive.mutate({ id: itemToShare?.id, value: formData });
    }).catch((errorInfo) => {
      console.log('Validation Failed:', errorInfo);
    });
  };

  const usersOptions = guestsList.map((user, index) => ({
    value: user.label,
    label: renderUserOption(user, index),
    key: user.id,
    title: user.label,
    avatar: user.avatar,
    email: user.email,
    family_name: user.family_name,
  }));

  const renderSharedUsers = () => (
    <div className="space-y-4 ">
      <h4 className="text-lg font-medium">{t("drive.shareModal.sharedWith")}</h4>
      {sharedItemDetails?.data?.length === 0 ? (
        <div className="text-gray-500">{t("drive.shareModal.noSharedUsers")}</div>
      ) : (
        <div className="max-h-[200px] overflow-auto">
      {  sharedItemDetails?.data?.map((user) => (
          <div key={user.user.id} className="flex items-center justify-between ">
            <div className="flex items-center gap-2">
              <DisplayAvatar
                name={user.user.label}
                size={24}
                urlImg={
                  user.user.avatar &&
                  URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                    user.user.avatar
                }
              />
              <div>{user.user.label}</div>
            </div>
            <div className="flex items-center gap-2">
              <div>{user.permission_level}</div>
              <Popconfirm
                title={t("drive.shareModal.confirmRemoveUser")}
                onConfirm={() => removeSharedMutation.mutate({id:itemToShare.id,  value: user?.id  })}
                okText={t("drive.shareModal.yes")}
                cancelText={t("drive.shareModal.no")}
              >
                <Button
                  type="text"
                  icon={<CloseOutlined />}
                />
              </Popconfirm>
            </div>
          </div>
        ))}
        </div>
      )}
    </div>
  );

  return (
    <Modal
    title={<span className="flex items-center gap-2"><span className=" text-lg text-gray-500 font-medium">{t("drive.shareModal.title")} : </span> <span className=" text-black-500">{itemToShare?.name}</span></span>}
      open={isVisible}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          {t("drive.shareModal.cancel")}
        </Button>,
        <Button
          key="share"
          type="primary"
          loading={shareDrive?.isLoading}
          onClick={handleShare}
        >
          {t("drive.shareModal.share")}
        </Button>,
      ]}
      destroyOnClose
      width={600}
    >
      <Spin spinning={isSharedLoading}>
      <Form form={form} layout="vertical">
        <div className="space-y-6">
          {/* User Selection */}
          <Form.Item
            name="user"
            label={t("drive.shareModal.selectUsers")}
            rules={[{ required: true, message: t("drive.shareModal.selectUserRequired") }]}
          >
            <Select
              filterOption={false}
              style={{ width: "100%" }}
              placeholder={t("drive.shareModal.selectUsersPlaceholder")}
              value={selectedUser}
              onChange={(_, option) => setSelectedUser(option)}
              onSearch={handleUserSearch}
              loading={loadGuests}
              showSearch
              labelRender={(option) => {
               
                return (
                  <div className="flex  items-center space-x-3 py-1">
                    <DisplayAvatar
                      name={option.value}
                      size={24}
                      urlImg={
                        option.avatar &&
                        URL_ENV?.REACT_APP_BASE_URL +
                          URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                          option.avatar
                      }
                    />

                    <div className="font-medium">{option.value}</div>
                  </div>
                );
              }}
              notFoundContent={loadGuests ? <Spin size="small" /> : null}
              options={usersOptions}
              onDropdownVisibleChange={handleDropdownVisibleChange}
            />
          </Form.Item>

          {/* Permission Selection */}
          <Form.Item
            name="permission"
            label={t("drive.shareModal.permissions")}
            rules={[{ required: true, message: t("drive.shareModal.selectPermissionRequired") }]}
          >
            <Radio.Group
              onChange={(e) => setSelectedPermission(e.target.value)}
              value={selectedPermission?.value}
            >
              {permissionOptions.map((option) => (
                <Radio key={option.value} value={option.value}>
                  <div>
                    <div className="font-medium">{option.label}</div>
                    <div className="text-xs text-gray-500">{option.title}</div>
                  </div>
                </Radio>
              ))}
            </Radio.Group>
          </Form.Item>

          {/* Shared Users List */}
          {sharedItemDetails?.data?.length > 0 && renderSharedUsers()}
        </div>
      </Form>
      </Spin>
    </Modal>
  );
};

export default ShareModal;
