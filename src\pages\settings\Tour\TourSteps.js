import React, { useEffect, useState } from "react";
import {
  Drawer,
  Button,
  Table,
  Modal,
  Form,
  Input,
  Popconfirm,
  message,
  Space,
  Upload,
  Image,
  Tag
} from "antd";
import { useTranslation } from "react-i18next";
import { MenuOutlined, InboxOutlined } from "@ant-design/icons";
import {
  DndContext,
  closestCenter,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import MainService from "services/main.service";
import { URL_ENV } from "index";

const { Dragger } = Upload;

const DraggableRow = ({ children, ...props }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    setActivatorNodeRef,
    transform,
    transition,
  } = useSortable({ id: props["data-row-key"] });

  const style = {
    ...props.style,
    transform: CSS.Transform.toString(transform),
    transition,
    cursor: "move",
  };

  return (
    <tr {...props} ref={setNodeRef} style={style} {...attributes}>
      {React.Children.map(children, (child) => {
        if (child.key === "sort") {
          return React.cloneElement(child, {
            children: (
              <MenuOutlined
                ref={setActivatorNodeRef}
                style={{ cursor: "grab", color: "#999" }}
                {...listeners}
              />
            ),
          });
        }
        return child;
      })}
    </tr>
  );
};

export default function TourSteps({ tourKey, visible, onClose }) {
  const { t } = useTranslation("common");
  const [steps, setSteps] = useState([]);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [modalVisible, setModalVisible] = useState(false);
  const [editStep, setEditStep] = useState(null);
  const [uploadFile, setUploadFile] = useState(null);
  const [btnLoading, setBtnLoading] = useState(false);

  useEffect(() => {
    if (visible) fetchSteps();
  }, [visible]);

  const fetchSteps = async () => {
    setLoading(true);
    try {
      const { data } = await MainService.getTourSteps(tourKey);
      setSteps(data);
    } catch (err) {
      message.error(t("tours.steps.error_fetch"));
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (values) => {
    setBtnLoading(true);
    try {
      const formData = new FormData();
      formData.append("title", values.title);
      formData.append("description", values.description);
      formData.append("selector", values.selector.startsWith("#") ? values.selector : `#${values.selector}`);
      if (uploadFile) formData.append("image", uploadFile);

      let response;
      if (editStep) {
        response = await MainService.updateTourStep(editStep._id, formData);
        message.success(t("tours.steps.updated"));
        setSteps(prev =>
          prev.map((step) => step._id === editStep._id ? response.data : step)
        );
      } else {
        response = await MainService.createTourStep(tourKey, formData);
        message.success(t("tours.steps.created"));
        setSteps((prev) => [...prev, response.data]);
      }

      form.resetFields();
      setUploadFile(null);
      setModalVisible(false);
    } catch (e) {
      message.error(t("tours.steps.error_save"));
    } finally {
      setBtnLoading(false);
    }
  };

  const handleDelete = async (id) => {
    try {
      await MainService.deleteTourStep(id);
      message.success(t("tours.steps.deleted"));
      setSteps((prev) => prev.filter((s) => s._id !== id).map((s, i) => ({ ...s, order: i })));
    } catch (e) {
      message.error(t("tours.steps.error_delete"));
    }
  };

  const handleDragEnd = async ({ active, over }) => {
    if (active.id !== over?.id) {
      const oldIndex = steps.findIndex((i) => i._id === active.id);
      const newIndex = steps.findIndex((i) => i._id === over?.id);
      const reordered = arrayMove(steps, oldIndex, newIndex);
      setSteps(reordered);

      try {
        await MainService.reorderTourSteps(tourKey, {
          order: reordered.map((step, index) => ({ id: step._id, order: index })),
        });
        message.success(t("tours.steps.reordered"));
      } catch (err) {
        message.error(t("tours.steps.error_reorder"));
      }
    }
  };

  const columns = [
    {
      key: "sort",
      width: 40,
    },
    {
      title: "#",
      dataIndex: "order",
      render: (_, __, index) => index + 1,
    },
    {
      title: t("tours.steps.title"),
      dataIndex: "title",
    },
    // {
    //   title: t("tours.steps.description"),
    //   dataIndex: "description",
    // },
    {
      title: t("tours.steps.upload"),
      dataIndex: "upload",
      render: (_, record) =>
        record.upload?.path ? (
          <Image
            width={50}
            height={50}
            src={`${URL_ENV?.REACT_APP_BASE_URL}${record.upload.path}`}
            alt="Preview"
            style={{ objectFit: "cover", borderRadius: 4 }}
            preview={{ mask: <span style={{ color: "#fff" }}>{t("tours.steps.preview")}</span> }}
          />
        ) : 
        <Tag color="red">{t("tours.steps.no_image")}</Tag>,
        
    },    
    {
      title: t("tours.steps.actions"),
      render: (_, record) => (
        <Space>
          <Button size="small" onClick={() => {
            setEditStep(record);
            form.setFieldsValue(record);
            setModalVisible(true);
          }}>{t("tours.steps.edit")}</Button>
          <Popconfirm
            title={t("tours.steps.confirm_delete")}
            onConfirm={() => handleDelete(record._id)}
          >
            <Button size="small" danger>{t("tours.steps.delete")}</Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <Drawer
      title={t("tours.steps.manage_steps")}
      placement="right"
      onClose={onClose}
      open={visible}
      width={1000}
    >
      <Button type="primary" onClick={() => {
        setEditStep(null);
        form.resetFields();
        setUploadFile(null);
        setModalVisible(true);
      }}>
        {t("tours.steps.add")}
      </Button>
      <DndContext collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
        <SortableContext
          items={steps.map((s) => s._id)}
          strategy={verticalListSortingStrategy}
        >
          <Table
            dataSource={steps}
            columns={columns}
            loading={loading}
            rowKey="_id"
            components={{ body: { row: DraggableRow } }}
            pagination={false}
            expandable={{
              expandedRowRender: (record) => (
                <p style={{ margin: 0 }} className="px-4">{record.description}</p>
              ),
              rowExpandable: (record) => !!record.description,
            }}
          />
        </SortableContext>
      </DndContext>

      <Modal
        title={editStep ? t("tours.steps.edit") : t("tours.steps.add")}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => form.submit()}
        confirmLoading={btnLoading}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
        >
          <Form.Item
            name="title"
            label={t("tours.steps.title")}
            rules={[{ required: true }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="description"
            label={t("tours.steps.description")}
            rules={[{ required: true }]}
          >
            <Input.TextArea rows={3} />
          </Form.Item>
          <Form.Item
            name="selector"
            label={t("tours.steps.selector")}
            rules={[{ required: true }]}
          >
            <Input />
          </Form.Item>

          <Form.Item label={t("tours.steps.upload")}>
            <Dragger
              maxCount={1}
              accept="image/jpeg,image/png,image/webp,image/gif"
              beforeUpload={(file) => {
                const isImage =
                  file.type === "image/jpeg" ||
                  file.type === "image/png" ||
                  file.type === "image/webp" ||
                  file.type === "image/gif";

                if (!isImage) {
                  message.error(t("tours.steps.invalid_image_type"));
                  return Upload.LIST_IGNORE;
                }

                const isTooLarge = file.size / 1024 / 1024 > 2;
                if (isTooLarge) {
                  message.error(t("tours.steps.too_large"));
                  return Upload.LIST_IGNORE;
                }

                setUploadFile(file);
                return false; // prevent auto upload
              }}
              fileList={uploadFile ? [uploadFile] : []}
              onRemove={() => setUploadFile(null)}
            >
              <p className="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p className="ant-upload-text">{t("tours.steps.drag_text")}</p>
              <p className="ant-upload-hint">{t("tours.steps.drag_hint")}</p>
            </Dragger>
          </Form.Item>

        </Form>
      </Modal>
    </Drawer>
  );
}
