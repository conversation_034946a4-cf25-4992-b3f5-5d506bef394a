import React, { useState } from "react";
import { Card, Col, Row, Dropdown, Pagination, Alert, message, Empty } from "antd";
import { MoreOutlined, ShareAltOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import CreateItemsDropdown from "../createItemsDropdown";
import DisplayAvatar from "pages/voip/components/DisplayAvatar";
import { URL_ENV } from "index";
import { CustomTag } from "pages/clients&users/components/RenderColumnsTable";
import UploadInput from "../UploadInput";
import { Image } from "antd";
import SharedUsersPopover from "../drive-list/SharedUsersPopover.component";
import { useSelector } from "react-redux";
const DriveCards = ({
  data,
  isFetching,
  getIcon,
  renderMenuItems,
  onCardClick,
  pagination,
  onPageChange,
  isError,
  onMoveItem,
  onFileUpload,
  showDragDropUpload = true,
  draggedItem,
  setDraggedItem,
}) => {
  const [t] = useTranslation("common");
  //const [draggedItem, setDraggedItem] = useState(null);
  const [dragOverItem, setDragOverItem] = useState(null);
  const search = useSelector((state) => state.drive.search);

  const handleDragStart = (e, item) => {
    setDraggedItem(item);
    e.dataTransfer.effectAllowed = "move";
    e.dataTransfer.setData("text/plain", item.id);
    
    // Add visual feedback
    e.target.style.opacity = "0.5";
  };

  const handleDragEnd = (e) => {
    e.target.style.opacity = "1";
    setDraggedItem(null);
    setDragOverItem(null);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";
  };

  const handleDragEnter = (e, item) => {
    e.preventDefault();
    if (item.type === "folder" && draggedItem && draggedItem.id !== item.id) {
   
      setDragOverItem(item.id);
    }
  };

  const handleDragLeave = (e) => {
    // Only remove drag over effect if we're leaving the card entirely
    if (!e.currentTarget.contains(e.relatedTarget)) {
      setDragOverItem(null);
    }
  };

  const handleDrop = (e, targetItem) => {
    e.preventDefault();
    setDragOverItem(null);
    
    if (!draggedItem || !targetItem) return;
    
  
    if (targetItem.type !== "folder") {
      message.warning(t("drive.canOnlyDropIntoFolders"));
      return;
    }
    

    if (draggedItem.id === targetItem.id) {
      message.warning(t("drive.cannotDropIntoItself"));
      return;
    }
    

    if (onMoveItem) {
      onMoveItem(draggedItem, targetItem);
    }
    
    setDraggedItem(null);
  };

const RenderEmptyList = () => {
  return (
    <div className="h-full w-full">
      <div className="flex flex-col items-center justify-center gap-6 p-8 w-full">
        <UploadInput
          onFileUpload={onFileUpload}
          showDragDropUpload={showDragDropUpload}
   
        />
         
        <div className="flex items-center gap-4">
          {showDragDropUpload && (
            <div className="flex items-center gap-2 text-gray-400">
              <div className="h-px bg-gray-300 w-12"></div>
              <span className="text-sm">{t("drive.or")}</span>
              <div className="h-px bg-gray-300 w-12"></div>
            </div>
          )}
        </div>
         
        <CreateItemsDropdown />
      </div>
    </div>
  );
};

  if (isError) {
    return (
      <div className="flex h-full items-center justify-center p-4">
        <Alert
          message="Error"
          description={t("drive.failedToLoadItems")}
          type="error"
          showIcon
        />
      </div>
    );
  }

  return (
    <div className=" h-full w-full">
      {data?.length > 0 ? (
        <>
          <Row
            gutter={[16, 16]}
            justify="start"
            className="min-h-[200px] min-w-[200px]"
          >
            {data?.map((item) => (
              <Col key={item.id} xs={24} sm={12} md={8} lg={6} xl={4}>
                <Card
                  hoverable
                 draggable
                  onDragStart={(e) => handleDragStart(e, item)}
                  onDragEnd={handleDragEnd}
                  onDragOver={handleDragOver}
                  onDragEnter={(e) => handleDragEnter(e, item)}
                  onDragLeave={handleDragLeave}
                  onDrop={(e) => handleDrop(e, item)}
                  title={
                    <CustomTag
                      content={item?.created_by?.label}
                      maxChars={35}
                      avatar={
                        <DisplayAvatar
                          name={item?.created_by?.label}
                          size={22}
                          urlImg={
                            !!item?.created_by?.avatar &&
                            URL_ENV?.REACT_APP_BASE_URL +
                              URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                              item?.created_by?.avatar
                          }
                        />
                      }
                    />
                  }
                  styles={{
                    header: {
                      minHeight: "20px",
                      padding: "0 0 10px 0",
                      margin: "0px",
                      borderBottom: "none",
                      backgroundColor: "transparent",
                      fontSize: "12px",
                      fontWeight: "400",
                    },
                  }}
                  style={{
                    borderRadius: "8px",
                    overflow: "hidden",
                    boxShadow: dragOverItem === item.id 
                      ? "0 4px 16px rgba(24, 144, 255, 0.3)" 
                      : "0 2px 8px rgba(0, 0, 0, 0.1)",
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "space-between",
                    height: "200px",
                    padding: "10px",
                    backgroundColor: dragOverItem === item.id 
                      ? "rgba(24, 144, 255, 0.05)" 
                      : "transparent",
                    border: dragOverItem === item.id 
                      ? "2px dashed #1890ff" 
                      : "1px solid #d9d9d9",
                    //cursor: "grab",
                    transition: "all 0.3s ease",
                  }}
                  cover={
                    <div
                      onClick={() => onCardClick(item)}
                      style={{
                        height: "115px",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        backgroundColor: "#f5f5f5",
                        position: "relative",
                      }}
                    >
                      {item?.thumbnail_url ? (
                        // Check if it's a video file
                        item?.extension && ["MP4", "AVI", "MOV", "WMV", "FLV", "WEBM", "MKV"].includes(item.extension.toUpperCase()) ? (
                          <video
                            width={170}
                            height={114}
                            controls
                            preload="metadata"
                            className="object-cover"
                            style={{ borderRadius: "4px" }}
                            onClick={(e) => e.stopPropagation()}
                          >
                            <source src={ item.thumbnail_url} type={`video/${item.extension.toLowerCase()}`} />
                            Your browser does not support the video tag.
                          </video>
                        ) : (
                          <Image src={item?.thumbnail_url} width={170} height={114} className="object-cover" />
                        )
                      ) : (
                        getIcon(item, "100px")
                      )}
                      
                      {/* Drop zone indicator for folders */}
                      {dragOverItem === item.id && item.type === "folder" && (
                        <div
                          style={{
                            position: "absolute",
                            top: 0,
                            left: 0,
                            right: 0,
                            bottom: 0,
                            backgroundColor: "rgba(24, 144, 255, 0.1)",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            color: "#1890ff",
                            fontWeight: "bold",
                            fontSize: "12px",
                            borderRadius: "4px",
                          }}
                        >
                          {t("drive.dropHere")}
                        </div>
                      )}
                    </div>
                  }
                >
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }}
                  >
                    <div
                      style={{
                        flex: 1,
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                      }}
                    >
                      <SharedUsersPopover itemId={item?.id} itemName={item?.name}>
                        <div className="flex items-center gap-1">
                          <span className="cursor-pointer hover:text-blue-600 truncate">
                            {item.name}
                          </span>
                          {item?.is_shared  && (
                                                         <ShareAltOutlined 
                               className="text-blue-500 text-xs flex-shrink-0" 
                               title={t("tasks.shared") || "Shared"}
                             />
                          )}
                        </div>
                      </SharedUsersPopover>
                    </div>
                    <Dropdown
                      menu={{ items: renderMenuItems(item) }}
                      trigger={["click"]}
                      placement="bottomRight"
                    >
                      <MoreOutlined
                        style={{ fontSize: "15px", cursor: "pointer" }}
                        onClick={(e) => e.stopPropagation()}
                      />
                    </Dropdown>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              marginTop: "20px",
            }}
          >
            <Pagination
              current={pagination.current_page}
              pageSize={pagination.pageSize}
              total={pagination.total}
              onChange={onPageChange}
              showSizeChanger={false}
            />
          </div>
        </>
      ) : (
        !isFetching && !search ? (
          <RenderEmptyList />
        ) : (
          <Empty />
        )
      )}
    </div>
  );
};

export default DriveCards;
