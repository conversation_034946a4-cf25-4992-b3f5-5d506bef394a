/**
 * @name SortableItem
 *
 * @description `SortableItem` is the single card in kanban.
 *
 * @param {Number} id The card id.
 * @param {Number} familyId Id of module.
 * @param {String} source The source where the component is being called (Task/"").
 * @param {String} stageColor The configured color for the stage.
 * @param {Array} tasksTypes Array of all task types.
 * @param {Boolean} isResolved Returns if the stage is resolved (system+isFinal+isResolved).
 * @param {Boolean} isFinalStage whether the stage is a final stage (system+isFinal).
 * @param {Object} content The content of the card.
 * @param {Function} reopenTicketForGuest Reopen ticket for guest (module tickets).
 * @param {Function} setTaskToUpdate Sets 'taskToUpdate' state.
 * @param {Function} setOpenDrawerUpdate Sets 'openDrawerUpdate' state.
 * @param {Function} setElementDetailToUpdate Sets 'elementDetailToUpdate' state.
 * @param {Function} setOpenActivity360 Sets 'openActivity360' state.
 * @param {Function} handleDeleteElement Handle delete module element from kanban api.
 * @param {Function} deleteTask Handle delete activity from kanban api.
 * @param {Function} setActivityLabel Sets 'activityLabel' state.
 * @param {Function} setOpenElementDetails Sets 'openElementDetails' state.
 * @param {Function} setElementDetails  Sets 'elementDetails' state.
 * @param {Function} setRoomActivityId Sets 'roomActivityId' state.
 * @param {Function} setModalType Sets 'modalType' state.
 * @param {Function} setOpenModal Sets 'openModal' state.
 * @param {Function} setActiveRoomId Sets 'activeRoomId' state.
 * @param {Function} setElementInfo Sets 'elementInfo' state.
 * @param {Function} setOpenDrawerInfo Sets 'openDrawerInfo' state.
 * @param {Function} closeTicketForGuest Close ticket for guest (module tickets).
 * @param {Object} forwardedRef Ref object forwarded from parent component.
 *
 * @returns {JSX.Element} Retruns the card component: Activity card or module element card.
 */

import { memo } from "react";

import TaskItem from "./TaskItem";
import ElementItem from "./ElementItem";

const SortableItem = memo(function SortableItem({
  id,
  content,
  source,
  setTaskToUpdate,
  deleteTask,
  tasksTypes,
  setOpenDrawerUpdate,
  setElementDetailToUpdate,
  handleDeleteElement,
  familyId,
  setOpenActivity360,
  setActivityLabel,
  setOpenElementDetails,
  setElementDetails,
  stageColor,
  setRoomActivityId,
  setModalType,
  setOpenModal,
  setActiveRoomId,
  setElementInfo,
  setOpenDrawerInfo,
  closeTicketForGuest,
  isFinalStage,
  reopenTicketForGuest,
  isResolved,
  forwardedRef,
}) {
  return source === "Task" ? (
    <TaskItem
      id={id}
      setTaskToUpdate={setTaskToUpdate}
      content={content}
      deleteTask={deleteTask}
      tasksTypes={tasksTypes}
      setOpenActivity360={setOpenActivity360}
      setActivityLabel={setActivityLabel}
      setOpenElementDetails={setOpenElementDetails}
      setElementDetails={setElementDetails}
      stageColor={stageColor}
      setRoomActivityId={setRoomActivityId}
      ref={forwardedRef}
    />
  ) : (
    <ElementItem
      id={id}
      setOpenDrawerUpdate={setOpenDrawerUpdate}
      setElementDetailToUpdate={setElementDetailToUpdate}
      content={content}
      handleDeleteElement={handleDeleteElement}
      familyId={familyId}
      setTaskToUpdate={setTaskToUpdate}
      stageColor={stageColor}
      setRoomActivityId={setRoomActivityId}
      setModalType={setModalType}
      setOpenModal={setOpenModal}
      setActiveRoomId={setActiveRoomId}
      setElementInfo={setElementInfo}
      setOpenDrawerInfo={setOpenDrawerInfo}
      closeTicketForGuest={closeTicketForGuest}
      reopenTicketForGuest={reopenTicketForGuest}
      isFinalStage={isFinalStage}
      isResolved={isResolved}
      ref={forwardedRef}
    />
  );
});

export default SortableItem;

SortableItem.defaultProps = {
  content: { label: "Card title" },
  forwardedRef: null,
};
