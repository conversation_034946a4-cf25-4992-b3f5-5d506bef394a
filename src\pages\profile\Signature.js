import {
  DeleteOutlined,
  FormOutlined,
  PlusOutlined,
  RestOutlined,
  SaveOutlined,
} from "@ant-design/icons";
import {
  <PERSON>ert,
  Button,
  Divider,
  Form,
  Input,
  Layout,
  Select,
  Space,
  Spin,
  Switch,
  Tooltip,
  Typography,
} from "antd";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import ListOfSignature from "./components/Signature/ListOfSignature";
import Editorr from "../components/TipTapEditor/Editorr";
import { generateAxios } from "../../services/axiosInstance";
import { URL_ENV } from "index";
import { toastNotification } from "components/ToastNotification";
import Confirm from "components/GenericModal";
import { FiSearch } from "react-icons/fi";
import { useSelector } from "react-redux";

const { Header, Content, Footer, Sider } = Layout;
const Signature = () => {
  const [t] = useTranslation("common");
  const [listSignature, setListSignature] = useState([]);
  const [allSignature, setAllSignature] = useState([]);
  const [firstRender, setFirstRender] = useState(true);

  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [lastPage, setLastPage] = useState(1);
  const [keySignature, setKeySignature] = useState("");

  const [selectedNote, setClickedNote] = useState(null);
  const [content, setContent] = useState(null);

  const [hasSelectedNote, setHasSelectedNode] = useState(false);
  const [isMount, setIsMount] = useState(false);

  const [search, setSearch] = useState("");

  const [loadingContent, setLoadingContent] = useState(false);

  const [disabled, setDisabled] = useState(true);
  const { dataAccounts } = useSelector((state) => state.mailReducer);
  const { user } = useSelector((state) => state.user);
  const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return email === "" || email === null || emailRegex.test(email);
  };
  const selectedSignature = listSignature.find((el) => el.id === keySignature);
  // Sinon, vérifiez s'il est un email valide
  useEffect(() => {
    const getSignature = async () => {
      setLoading(true);
      try {
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get("/get-signature");
        setListSignature(res.data.data);
        setAllSignature(res.data.data);
        setClickedNote(res.data.data.find((el) => el.default)?.value);
        setContent(res.data.data.find((el) => el.default)?.value);
        setTimeout(() => {
          setKeySignature(res.data.data.find((el) => el.default)?.id);
        }, 100);
        setTimeout(() => {
          setHasSelectedNode(false);
        }, 100);

        setLoading(false);
        setTimeout(
          () => {
            setIsMount(true);
          },

          4000
        );
      } catch (err) {
        setLoading(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    };

    getSignature();
  }, []);
  useEffect(() => {
    if (firstRender) {
      setDisabled(true);
    } else
      setDisabled(
        !hasSelectedNote ||
          selectedSignature?.label === "" ||
          typeof selectedSignature?.account_id !== "number" ||
          !isValidEmail(selectedSignature?.alias)
      );
  }, [hasSelectedNote, selectedSignature, firstRender]);
  useEffect(() => {
    setFirstRender(false);
  }, [hasSelectedNote]);
  const onFinish = async () => {
    setLoadingContent(true);
    if (typeof keySignature === "number") {
      try {
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post("/store-signature", {
          signature: selectedNote,
          default: selectedSignature?.default,
          label: selectedSignature?.label,
          account_id: selectedSignature?.account_id,
          alias: selectedSignature?.alias,
          // logo: null,
        });

        // console.log(res.data.data);
        if (listSignature.find((el) => el.id === keySignature)?.default) {
          setListSignature(
            [
              ...listSignature.map((el) =>
                el.id === keySignature ? res.data.data : el
              ),
            ].map((el) =>
              el.id === res.data.data.id
                ? { ...el, default: true }
                : { ...el, default: false }
            )
          );
          setAllSignature(
            [
              ...listSignature.map((el) =>
                el.id === keySignature ? res.data.data : el
              ),
            ].map((el) =>
              el.id === res.data.data.id
                ? { ...el, default: true }
                : { ...el, default: false }
            )
          );
        } else {
          setListSignature([
            ...listSignature.filter((el) => el.id !== keySignature),
            res.data.data,
          ]);
          setAllSignature([
            ...listSignature.filter((el) => el.id !== keySignature),
            res.data.data,
          ]);
        }
        // setListSignature(
        //   listSignature.map((el) =>
        //     el.id === keySignature ? res.data.data : el
        //   )
        // );
        setContent(selectedNote);
        setHasSelectedNode(false);
        setKeySignature(res.data.data.id);
        setDisabled(true);
        setFirstRender(true);
        setLoadingContent(false);
        toastNotification(
          "success",
          res.data.data.label + t("toasts.created"),
          "topRight"
        );
      } catch (err) {
        setLoadingContent(false);
        if (err.response.status === 409) {
          toastNotification("error", t("signature.labelUsed"), "topRight");
        } else if (err.response.status === 422) {
          toastNotification(
            "error",
            t("signature.defaultRequired"),
            "topRight"
          );
        } else
          toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    } else {
      try {
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).put(
          `update-signature/${keySignature}`,
          {
            signature: selectedNote,
            default: selectedSignature?.default,
            label: selectedSignature?.label,
            account_id: selectedSignature?.account_id,
            alias: selectedSignature?.alias,
          },
          {
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
            },
          }
        );
        if (listSignature.find((el) => el.id === keySignature)?.default) {
          setListSignature(
            listSignature.map((el) =>
              el.id === keySignature ? res.data.data : { ...el, default: false }
            )
          );

          setAllSignature(
            listSignature.map((el) =>
              el.id === keySignature ? res.data.data : { ...el, default: false }
            )
          );
        } else {
          setListSignature(
            listSignature.map((el) =>
              el.id === keySignature ? res.data.data : el
            )
          );

          setAllSignature(
            listSignature.map((el) =>
              el.id === keySignature ? res.data.data : el
            )
          );
        }
        setDisabled(true);
        setFirstRender(true);

        setLoadingContent(false);
        setHasSelectedNode(false);

        toastNotification(
          "success",
          res.data.data.label + t("toasts.edit"),
          "topRight"
        );
      } catch (err) {
        setLoadingContent(false);
        if (err.response.status === 409) {
          toastNotification("error", t("signature.labelUsed"), "topRight");
          setListSignature((prev) =>
            allSignature.map((el) =>
              el.id === keySignature ? { ...el, label: el.label } : el
            )
          );
        } else if (err.response.status === 422) {
          toastNotification(
            "error",
            t("signature.defaultRequired"),
            "topRight"
          );
          setListSignature((prev) =>
            allSignature.map((el) =>
              el.id === keySignature ? { ...el, default: true } : el
            )
          );
          // setAllSignature((prev) =>
          //   allSignature.map((el) =>
          //     el.id === keySignature ? { ...el, default: true } : el
          //   )
          // );
        } else
          toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    }
  };
  const handleDelete = async () => {
    try {
      const res = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).delete(`delete-signature/${keySignature}`);
      const deleted = listSignature.find((el) => el.id === keySignature)?.label;
      setListSignature(listSignature.filter((el) => el.id !== keySignature));
      setAllSignature(listSignature.filter((el) => el.id !== keySignature));
      setKeySignature("");
      toastNotification("success", deleted + t("toasts.deleted"), "topRight");
    } catch (err) {
      toastNotification("error", t("toasts.somethingWrong"), "topRight");
    }
  };

  return (
    <>
      <Spin spinning={loading}>
        <Layout>
          <Sider
            width="360px"
            style={{
              background: "white",
              //   padding: "12px 0px",
              borderRight: "1px solid #e8e8e8",
              height: "calc(100vh - 58px)",
              // marginTop: "-20px",
            }}
          >
            <div className="my-2 flex w-full flex-col gap-y-2 px-3  ">
              {/* Title */}
              <div className="flex flex-col gap-2">
                <div className="flex items-center justify-between">
                  <Typography.Title level={4}>
                    {/* {t("signature.listSignature")} */}
                    Signature
                  </Typography.Title>
                  <Tooltip
                    title={t("signature.addSignature")}
                    placement="bottom"
                  >
                    <Button
                      type="text"
                      className="group text-gray-500"
                      size="large"
                      shape="circle"
                      // icon={<PlusOutlined />}
                      icon={<FormOutlined />}
                      onClick={() => {
                        const num = Math.floor(Math.random() * 1000);
                        setListSignature([
                          ...listSignature,
                          {
                            label: `new Signature ${num}`,
                            id:
                              listSignature.length > 0
                                ? Math.max(
                                    ...listSignature.map((el) =>
                                      typeof el.id === "number" ? el.id : 1
                                    )
                                  ) + 1
                                : 1,
                            value: '<div data-type="rootblock"><p></p></div>',
                            default: listSignature.find(
                              (el) => typeof el.id !== "number"
                            )
                              ? false
                              : true,
                            account_id: null,
                            alias: "",
                          },
                        ]);
                        setAllSignature((prev) => [
                          ...prev,
                          {
                            label: `new Signature ${num}`,
                            id:
                              listSignature.length > 0
                                ? Math.max(
                                    ...listSignature.map((el) =>
                                      typeof el.id === "number" ? el.id : 1
                                    )
                                  ) + 1
                                : 1,
                            value: '<div data-type="rootblock"><p></p></div>',
                            default: allSignature.find(
                              (el) => typeof el.id !== "number"
                            )
                              ? false
                              : true,
                            account_id: null,
                            alias: "",
                          },
                        ]);
                        setKeySignature(
                          listSignature.length > 0
                            ? Math.max(
                                ...listSignature.map((el) =>
                                  typeof el.id === "number" ? el.id : 1
                                )
                              ) + 1
                            : 1
                        );
                        setClickedNote(
                          '<div data-type="rootblock"><p></p></div>'
                        );
                        setContent('<div data-type="rootblock"><p></p></div>');
                        setHasSelectedNode(false);
                      }}
                    />
                  </Tooltip>
                </div>
                <Input
                  prefix={<FiSearch className="text-slate-500" />}
                  placeholder={t("table.search")}
                  value={search.trimStart()}
                  onChange={(e) => setSearch(e.target.value.trimStart())}
                />
              </div>
              <Divider />

              <ListOfSignature
                setListSignature={setListSignature}
                listSignature={listSignature.filter((el) =>
                  el?.label?.toLowerCase().includes(search?.toLowerCase())
                )}
                loadingList={false}
                page={page}
                lastPage={lastPage}
                keySignature={keySignature}
                setKeySignature={setKeySignature}
                setContent={setContent}
                setClickedNote={setClickedNote}
                setDisabled={setDisabled}
                allSignature={allSignature.filter((el) =>
                  el?.label?.toLowerCase().includes(search?.toLowerCase())
                )}
                setHasSelectedNode={setHasSelectedNode}
                setFirstRender={setFirstRender}
              />
            </div>
          </Sider>
          <Layout
            style={{
              // padding: "0 24px 24px",

              background: "white",
            }}
          >
            <Content
              style={{
                margin: 0,
                minHeight: 280,
                background: "#80808008",
              }}
            >
              <Spin spinning={loadingContent}>
                {keySignature ? (
                  <Space direction="vertical" style={{ width: "100%" }}>
                    {typeof keySignature === "number" ? (
                      <Alert
                        type="info"
                        showIcon
                        message={
                          <div className="flex items-center justify-between">
                            {t("signature.unregistred")}{" "}
                            <Button
                              icon={<DeleteOutlined />}
                              onClick={() => {
                                setListSignature((prev) =>
                                  listSignature.filter(
                                    (el) => el.id !== keySignature
                                  )
                                );
                                setAllSignature((prev) =>
                                  listSignature.filter(
                                    (el) => el.id !== keySignature
                                  )
                                );
                                setKeySignature("");
                              }}
                              type="link"
                              danger
                            >
                              {t("table.delete")}
                            </Button>
                            <Button
                              icon={<SaveOutlined />}
                              onClick={onFinish}
                              type="link"
                              disabled={disabled}
                            >
                              {t("wiki.Save")}
                            </Button>
                          </div>
                        }
                        className="m-2"
                      />
                    ) : null}
                    <>
                      {typeof keySignature !== "number" ? (
                        <div className="m-4 flex justify-end gap-2">
                          <Button
                            icon={<DeleteOutlined />}
                            onClick={() =>
                              Confirm(
                                `Delete "${selectedSignature.label}" `,
                                "Confirm",
                                <RestOutlined style={{ color: "red" }} />,
                                function func() {
                                  return handleDelete(keySignature);
                                },
                                true
                              )
                            }
                            type="primary"
                            // disabled={
                            //   allSignature.find((el) => el.id === keySignature)
                            //     ?.default
                            // }
                            danger
                          >
                            {t("table.delete")}
                          </Button>
                          <Button
                            icon={<SaveOutlined />}
                            onClick={onFinish}
                            type="primary"
                            disabled={disabled}
                          >
                            {t("wiki.Save")}
                          </Button>
                        </div>
                      ) : null}

                      <div className="space-y-2 px-4">
                        <div
                          style={{
                            height: "calc(100vh - 130px)",
                            overflowY: "auto",
                          }}
                          className="px-2"
                        >
                          <Form layout="vertical">
                            <Form.Item
                              label={t("menu2.emailaccounts")}
                              className="flex-grow"
                              required
                            >
                              <Select
                                showSearch
                                popupMatchSelectWidth={false}
                                filterOption={(input, option) =>
                                  (option?.label ?? "")
                                    .toLowerCase()
                                    .includes(input.toLowerCase())
                                }
                                placeholder={t(
                                  "mailing.NewMsg.placeholderMail"
                                )}
                                style={{ minWidth: 250 }}
                                onChange={(value) => {
                                  setListSignature(
                                    listSignature.map((el) =>
                                      el.id === keySignature
                                        ? { ...el, account_id: value }
                                        : el
                                    )
                                  );
                                  setHasSelectedNode(
                                    selectedNote !==
                                      '<div data-type="rootblock"><p></p></div>' &&
                                      selectedNote
                                  );
                                  setFirstRender(false);
                                }}
                                value={selectedSignature?.account_id}
                                options={dataAccounts
                                  .filter((el) => el?.creator === user?.id)
                                  .map((el) => ({
                                    label: el?.label,
                                    value: el?.value,
                                  }))}
                              />
                            </Form.Item>
                            <Form.Item
                              label="Alias Email"
                              className="flex-grow"
                            >
                              <Input
                                value={selectedSignature?.alias}
                                onChange={(e) => {
                                  setListSignature(
                                    listSignature.map((el) =>
                                      el.id === keySignature
                                        ? { ...el, alias: e.target.value }
                                        : el
                                    )
                                  );
                                  setFirstRender(false);
                                  setHasSelectedNode(
                                    selectedNote !==
                                      '<div data-type="rootblock"><p></p></div>' &&
                                      selectedNote
                                  );
                                }}
                              />
                              {!isValidEmail(selectedSignature?.alias) && (
                                <div class="ant-form-item-explain-error">
                                  {t("emailAccounts.errEmail")}
                                </div>
                              )}
                            </Form.Item>
                            {/* <Divider /> */}
                            <Form.Item
                              label={t("tags.name")}
                              className="flex-grow"
                            >
                              <Input
                                value={selectedSignature?.label}
                                onChange={(e) => {
                                  setListSignature(
                                    listSignature.map((el) =>
                                      el.id === keySignature
                                        ? { ...el, label: e.target.value }
                                        : el
                                    )
                                  );
                                  setHasSelectedNode(
                                    selectedNote !==
                                      '<div data-type="rootblock"><p></p></div>' &&
                                      selectedNote
                                  );
                                  setFirstRender(false);
                                  // hasSelectedNote &&
                                  //   e.target.value !== "" &&
                                  //   setDisabled(false);
                                }}
                              />
                            </Form.Item>
                            <div className="flex items-center gap-2 pb-1.5">
                              <div
                                style={{
                                  paddingBottom: "0.25rem",
                                  fontWeight: 500,
                                  opacity: 1,
                                  color: "rgb(100 116 139 / 1)",
                                }}
                              >
                                {t("signature.defaultSignature")}
                              </div>
                              <Switch
                                size="small"
                                checked={selectedSignature?.default}
                                disabled={
                                  listSignature.find(
                                    (el) => typeof el.id !== "number"
                                  )
                                    ? false
                                    : true
                                }
                                onChange={(e) => {
                                  setListSignature((prev) =>
                                    prev.map((li) =>
                                      li.id === keySignature
                                        ? { ...li, default: e }
                                        : li
                                    )
                                  );
                                  setHasSelectedNode(
                                    selectedNote !==
                                      '<div data-type="rootblock"><p></p></div>' &&
                                      selectedNote
                                  );
                                  setFirstRender(false);
                                  // hasSelectedNote &&
                                  //   listSignature.find(
                                  //     (el) => el.id === keySignature
                                  //   )?.label &&
                                  //   setDisabled(false);
                                }}
                              />
                            </div>
                            {/* <Typography.Title level={5}>
                            Signature
                          </Typography.Title> */}
                            <Form.Item label="Signature">
                              <div
                                className="rounded-md border border-solid border-gray-200"
                                style={{ minHeight: "calc(100vh - 450px)" }}
                              >
                                <Editorr
                                  // selectedNote={selectedNote}
                                  source="signature"
                                  contentSignature={content}
                                  setClickedNote={setClickedNote}
                                  setDisabled={setDisabled}
                                  signature={listSignature.find(
                                    (el) => el.id === keySignature
                                  )}
                                  height="calc(100vh - 500px)"
                                  setHasSelectedNode={setHasSelectedNode}
                                />
                              </div>
                            </Form.Item>
                          </Form>
                        </div>
                      </div>
                    </>
                  </Space>
                ) : null}
              </Spin>
            </Content>
          </Layout>
        </Layout>
      </Spin>
    </>
  );
};

export default Signature;
