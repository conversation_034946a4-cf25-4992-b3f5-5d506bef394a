import DisplayAvatar from "pages/voip/components/DisplayAvatar";
import React, { forwardRef, useEffect, useState } from "react";
import { URL_ENV } from "index";
import {
  Avatar,
  Badge,
  Button,
  Dropdown,
  Rate,
  Select,
  Space,
  Tooltip,
  Typography,
} from "antd";
import ActionsList from "pages/clients&users/components/contacts-details-component/layout/components/ActionsList";
import { MoreOutlined, PhoneOutlined } from "@ant-design/icons";
import { AvatarChat } from "components/Chat";
import { getName } from "pages/layouts/chat/utils/ConversationUtils";
import ChoiceIcons from "../ChoiceIcons";
import { copyIcon } from "pages/voip/components/DisplayDescriptionByType";
import { useTranslation } from "react-i18next";
import useActionCall from "pages/voip/helpers/ActionCall";
import ActionsComponent from "pages/tasks/ActionsComponent";
import { convertContactToGuest } from "pages/clients&users/helpers";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import { familyIcons } from "./ViewSphere2";

const HeaderDirectoryViewSphere = forwardRef(
  (
    {
      dataSteps,
      contactInfo,
      openView360InDrawer,
      reference,
      selectedTags,
      loadStage,
      handleChange,
      setOpenEmailModal,
      channel,
      isUpdate,
      setSelectedKey,
      setOpenModalCheckList,
      DropdownProps,
      openChatInViewSphere,
      contactType,
      detailsInfo,
    },
    headerInfoRef
  ) => {
    const [t] = useTranslation("common");
    const [invited, setInvited] = useState(false);
    const call = useActionCall();
    const dispatch = useDispatch();
    // "Hassine Basla" i add the selector user to passed the role as a params to func "convertContactToGuest"
    const user = useSelector(({ user }) => user?.user);
    useEffect(() => {
      if (invited) {
        dispatch({
          type: "SET_CONTACT_HEADER_INFO",
          payload: {
            ...contactInfo,
            guest: true,
          },
        });
      }
    }, [invited, contactInfo]);

    return (
      <div className="flex w-full items-center justify-between">
        <div className=" flex items-center gap-x-3">
          {contactInfo?.family_id == "1" ||
          contactInfo?.family_id == "2" ||
          contactInfo?.family_id == "4" ||
          contactInfo?.family_id == "9" ? (
            <ActionsComponent
              elementValue={{
                uuid: contactInfo?.uid,
                extension: contactInfo?.extension,
                id: contactInfo?.id,
                family_id: contactInfo?.family_id,
              }}
            >
              <DisplayAvatar
                background="#cccccc63"
                size={56}
                cursor={
                  contactInfo?.uid || contactInfo?.extension ? "pointer" : ""
                }
                icon={
                  contactInfo?.image
                    ? null
                    : familyIcons(t).find(
                        (el) => el.key === contactInfo?.family_id
                      )?.icon
                }
                urlImg={
                  contactInfo?.image
                    ? URL_ENV?.REACT_APP_BASE_URL +
                      URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                      contactInfo?.image
                    : null
                }
                name={contactInfo?.name}
              />
            </ActionsComponent>
          ) : (
            <DisplayAvatar
              background="#cccccc63"
              size={56}
              icon={
                familyIcons(t).find((el) => el.key === contactInfo?.family_id)
                  ?.icon
              }
              urlImg={null}
              name={contactInfo?.name}
            />
          )}
          <div
            className="flex items-center space-x-3 "
            // className="lg:min-w[150px] flex flex-col truncate lg:max-w-[200px] xl:min-w-[200px] xl:max-w-[350px]"
          >
            <div>
              <span className={`flex  items-center`}>
                <span className={`text-xs font-medium text-gray-600`}>
                  {/* {contactInfo?.type ? (
                                      <span
                                        style={{
                                          color: contactType?.color,
                                        }}
                                      >
                                        {contactInfo?.type}
                                      </span>
                                    ) : null} */}

                  {reference ? "# " + reference : null}
                </span>
              </span>
              <span className={`flex  items-center`}>
                <span className={`  truncate text-xl font-semibold`}>
                  {contactInfo?.family_id === 6
                    ? contactInfo?.subject_helpdesk
                    : contactInfo?.name}
                </span>
              </span>
            </div>
            <div className="flex  items-center gap-5" ref={headerInfoRef}>
              {contactInfo?.owner?.id && contactInfo?.owner?.name ? (
                <div className="flex flex-col gap-1">
                  <span className="truncate text-xs text-gray-600 ">
                    {t("tasks.owner")}
                  </span>
                  <span className="flex gap-1 truncate text-sm font-medium">
                    {/* {contactInfo?.owner?.avatar && ( */}
                    <ActionsComponent
                      elementValue={{
                        uuid: contactInfo?.owner?.uid,
                        extension: contactInfo?.owner?.extension,
                        id: contactInfo?.owner?.id,
                        family_id: 4,
                      }}
                    >
                      <AvatarChat
                        fontSize="0.625rem"
                        url={
                          URL_ENV?.REACT_APP_BASE_URL +
                          URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                          contactInfo?.owner?.avatar
                        }
                        type="user"
                        size={22}
                        height={10}
                        width={10}
                        name={getName(contactInfo?.owner?.name, "avatar")}
                        hasImage={
                          contactInfo?.owner?.avatar &&
                          contactInfo?.owner?.avatar !== "/storage/uploads/"
                        }
                      />
                    </ActionsComponent>
                    {/* )} */} {contactInfo?.owner?.name}
                  </span>
                </div>
              ) : null}
              {contactInfo?.type ? (
                <div className="flex flex-col gap-1">
                  <span className={`  truncate text-xs text-gray-600 `}>
                    {contactInfo?.type ? "Type" : null}
                  </span>
                  <span className={`  truncate text-sm font-medium `}>
                    {contactInfo?.type ? (
                      <span style={{ color: contactType?.color }}>
                        {contactInfo?.type}
                      </span>
                    ) : null}
                  </span>
                </div>
              ) : null}
              {channel?.id ? (
                <div className="flex flex-col gap-1">
                  <span className="flex  items-center">
                    <span className={`  truncate text-xs text-gray-600 `}>
                      {t("vue360.channel")}
                    </span>
                  </span>
                  <span className="flex  items-center">
                    <span className={`  truncate text-sm font-medium `}>
                      <span style={{ color: channel?.color }}>
                        <ChoiceIcons icon={channel?.icon} /> {channel?.label}{" "}
                      </span>
                    </span>
                  </span>
                </div>
              ) : null}

              {channel?.id ? (
                <div className="flex flex-col gap-1">
                  <span className="flex  items-center">
                    <span className={`  truncate text-xs text-gray-600 `}>
                      Pipeline
                    </span>
                  </span>
                  <span className="flex  items-center">
                    <span className={`  truncate text-sm font-medium `}>
                      {dataSteps[dataSteps.length - 1]?.pipeline?.label}
                    </span>
                  </span>
                </div>
              ) : null}
              {Object.keys(detailsInfo).length > 0 &&
                Object.entries(detailsInfo)
                  .slice(0, 1)
                  .map(([key, { type, value }]) => (
                    <div className="flex flex-col gap-1" key={key}>
                      <span
                        // type="secondary"

                        className="text-xs text-gray-600"
                      >
                        {key}
                      </span>
                      <span className="flex gap-1 truncate text-sm font-medium">
                        {type === "checkbox" ||
                        type === "select" ||
                        type === "radio" ||
                        type === "multiselect" ||
                        type === "autocomplete" ? (
                          <>
                            {Array.isArray(value) ? (
                              value?.map((item) => item)
                            ) : (
                              <span className={`text-sm font-medium`}>
                                {value}{" "}
                              </span>
                            )}
                          </>
                        ) : type === "phone" ? (
                          <Typography.Text>{`(${value?.[0]}) ${value?.[1]}`}</Typography.Text>
                        ) : type === "image" ? (
                          <AvatarChat
                            url={
                              URL_ENV?.REACT_APP_BASE_URL +
                              URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                              value
                            }
                            fontSize={12}
                            type="user"
                            size={22}
                            height={10}
                            width={10}
                            name={getName(contactInfo?.owner?.name, "avatar")}
                            hasImage={value && value !== "/storage/uploads/"}
                          />
                        ) : type === "file" ? (
                          <div className="flex flex-col space-y-1">
                            {value?.map((file, i) => (
                              <a
                                key={i}
                                className="truncate"
                                style={{
                                  width: `14rem`,
                                }}
                                href={`${
                                  URL_ENV?.REACT_APP_BASE_URL +
                                  process.env.REACT_APP_SUFFIX_AVATAR_URL
                                }${file}`}
                                target="_blank"
                                rel="noreferrer"
                              >
                                {file}
                              </a>
                            ))}
                          </div>
                        ) : type === "rate" ? (
                          <Rate
                            allowHalf
                            disabled
                            defaultValue={Number(value)}
                          />
                        ) : (
                          <Tooltip title={key}>
                            <span className="text-sm font-medium">
                              {Array.isArray(value)
                                ? value.map((el) => el + " ")
                                : typeof value === "string"
                                ? value
                                : null}
                            </span>
                          </Tooltip>
                        )}
                        {type === "phone" ? (
                          <Space size={4}>
                            {copyIcon(
                              `${value?.[0]?.replace("+", "00")}${value?.[1]}`
                            )}
                            <Tooltip title={"Call"}>
                              <Button
                                onClick={() =>
                                  call(
                                    `${value?.[0]?.replace("+", "00")}${
                                      value?.[1]
                                    }`,
                                    contactInfo?.id,
                                    contactInfo?.family_id
                                  )
                                }
                                icon={
                                  <PhoneOutlined
                                    rotate={100}
                                    style={{
                                      fontSize: 15,
                                      marginBottom: 5,
                                    }}
                                  />
                                }
                                type="link"
                                size="small"
                              />
                            </Tooltip>
                          </Space>
                        ) : type === "email" ? (
                          copyIcon(`${value}`)
                        ) : (
                          ""
                        )}
                      </span>
                    </div>
                  ))}
            </div>
          </div>
        </div>
        <div className="flex flex-col gap-1 ">
          {contactInfo?.family_id && !contactInfo?.guest && (
            <span className="inline-flex items-center justify-end space-x-1">
              {contactInfo?.family_id == 2 && (
                <Button
                  type="primary"
                  size="small"
                  style={{ height: 25 }}
                  onClick={() =>
                    convertContactToGuest(
                      t,
                      contactInfo?.id,
                      contactInfo?.name,
                      setInvited,
                      true,
                      user?.role
                    )
                  }
                >
                  {t("import.inviteUsers")}
                </Button>
              )}
              <ActionsList
                {...contactInfo}
                setReceiver={() => {}}
                setOpenEmailModal={setOpenEmailModal}
                elementID={contactInfo?.id}
                module={contactInfo?.family_id}
                source={""}
                isElementUpdate={isUpdate}
                isAssociationUpdate={false}
                setSelectedKey={setSelectedKey}
                setOpenModalCheckList={setOpenModalCheckList}
                from="directory"
              />
              <Dropdown
                menu={DropdownProps}
                disabled={openView360InDrawer && openChatInViewSphere}
              >
                <Button
                  icon={<MoreOutlined className="font-semibold" />}
                  size="small"
                  style={{
                    height: "30px",
                    width: "34px",
                  }}
                  // onClick={() => setOpenModalMessage(true)}
                />
              </Dropdown>
            </span>
          )}
          {contactInfo?.pipeline && selectedTags?.id ? (
            <div className="flex flex-col">
              {/* <span className="inline-flex items-center ">
                  <span className="truncate text-xs text-gray-600">
                    {dataSteps[dataSteps.length - 1]?.pipeline?.label}
                    {"  "}-
                  </span>
                  <span className="ml-[0.5] text-xs">{`
                                      ${t("fields_management.groupStageLabel")}
                                      
                                      ${
                                        dataSteps
                                          .map((el) => el.id)
                                          .indexOf(selectedTags?.id) + 1
                                      }/${dataSteps?.length - 1}`}</span>
                </span> */}
              <span className="inline-flex items-center gap-x-1">
                <Select
                  // suffixIcon={
                  //   loadUpdateTaskStage ? (
                  //     <LoadingOutlined style={{ color: "#000", fontSize: "10px" }} />
                  //   ) : (
                  //     <DownOutlined
                  //       style={{
                  //         color: "#000",
                  //         fontSize: "10px",
                  //         pointerEvents: "none",
                  //       }}
                  //     />
                  //   )
                  // }
                  size="small"
                  loading={loadStage}
                  style={{
                    // width: "100%",
                    // minWidth: "200px",
                    // maxWidth: "300px",
                    background: "white",
                  }}
                  className="min-w-[150px] lg:w-[160px] xl:w-[180px] "
                  placeholder={t("tasks.choose")}
                  onChange={(e, value) => handleChange(value)}
                  //   bordered={false}
                  //   onChange={(e) => {
                  //     UpdateTaskStage(
                  //       {
                  //         "task_id[]": props?.id,
                  //         new_stage_id: e,
                  //       },
                  //       "singleUpdate"
                  //     );
                  //   }}
                  value={selectedTags?.id}
                  optionLabelProp="label2"
                  options={dataSteps
                    .slice(0, dataSteps.length - 1)
                    .map((el) => ({
                      ...el,
                      label: (
                        <span className="viewSphereSelect flex">
                          <Badge
                            color={el.color}
                            text={
                              el.percent ? (
                                <>{el.label + " (" + el.percent + "%)"}</>
                              ) : (
                                el.label
                              )
                            }
                          />
                        </span>
                      ),
                      label2: (
                        <span className="viewSphereSelect flex">
                          {/* {dataSteps[dataSteps.length - 1]?.pipeline?.label}{" "}
                          -&nbsp; */}
                          <Badge
                            color={el.color}
                            text={
                              el.percent ? (
                                <>{el.label + " (" + el.percent + "%)"}</>
                              ) : (
                                el.label
                              )
                            }
                          />
                        </span>
                      ),

                      value: el.id,
                    }))}
                  //   popupMatchSelectWidth={false}
                />
              </span>
            </div>
          ) : null}
        </div>
      </div>
    );
  }
);

export default HeaderDirectoryViewSphere;
