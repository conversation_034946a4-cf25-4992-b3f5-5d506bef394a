import moment from "moment";
import { toastNotification } from "../../../components/ToastNotification";
import { generateAxios } from "../../../services/axiosInstance";
import {
  SET_LOADING_TAB_VISIO,
  GET_DATA_CHANGE_VISIO,
  SET_LOADING_DETAILS_VISIO,
  GET_DETAILS_VISIO,
  SET_LIST_MEET,
  SET_PAGE,
  SET_KEY_MEET,
  SET_TAB_KEY,
  SET_DETAILS_MEET,
  SET_NOW,
  SET_LATER,
  SET_LAST_PAGE,
  SET_HISTORY_COUNT,
  SET_LABEL,
  SET_OPEN_DRAWER_VISIO,
  SET_DETAILS_MEET_EXTERNAL,
  SET_NOTIFICATION_COUNT,
  SET_NOTIFICATION_LIST,
  SET_PAGE_NOTIFICATION_LIST,
  SET_LAST_PAGE_NOTIFICATION_LIST,
  SET_REMINDERS_LIST,
  GET_REMINDERS_LIST,
  SET_LIMIT,
  SET_SEARCH_LIST_VISIO,
  SET_KPI,
  SET_LOAD_NOTIF,
  UPDATE_NOTIFICATION_LIST,
  MAKE_ALL_NOTIFS_READ,
  SET_KPI_DATE,
  SET_COUNT_REMINDERS,
} from "../../constants";
import { store } from "../../store";
import MainService from "../../../services/main.service";
import { URL_ENV } from "index";
import { displayStatKey } from "pages/tasks/KpiGrid";
import i18next from "i18next";

export const onChangeTabVisio = (payload) => async (dispatch) => {
  dispatch({ type: SET_TAB_KEY, payload: payload.value });
  dispatch({ type: SET_LOADING_TAB_VISIO, payload: true });
  try {
    const res = await generateAxios(
      URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
    ).get(
      `/get-tasks-visio?type=${payload.value}&limit=${
        payload.limit || 10
      }&search=${payload.search}&page=${payload?.page || 1}&priorities=${
        payload?.priorities || ""
      }&stages_ids=${payload?.stages_ids}`
      // {
      //   cancelToken: payload?.source?.token,
      // }
    );

    if (res.status === 200) {
      let result = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).get(
        `/get-tasks-visio-count?search=${payload.search}`
        // , {
        //   cancelToken: payload?.source?.token,
        // }
      );
      let kpi = { data: [] };
      let kpiDate = { data: [] };

      if (window.location.pathname.includes("visio")) {
        kpiDate = await MainService.getKpiDateVisio();
        kpi = await MainService.getKpiVisio(payload.value);
      }
      // console.log(res?.data?.meta.total, "--", result.data);
      dispatch({
        type: GET_DATA_CHANGE_VISIO,
        payload: {
          listMeet: res?.data?.data.map((el) => ({ ...el, key: el.id })),
          lastPage: res?.data?.meta.last_page,
          total: res?.data?.meta.total,
          keyMeet: payload.keyMeet,
          page: payload.page || 1,
          // tabKey: payload.value,
          countToday:
            payload.value == 1 ? res?.data?.meta.total : result.data.today,
          // payload.priorities && payload.value == 1
          //   ? res?.data?.meta.total
          //   : result.data.today

          countUpComing:
            payload.value == 2 ? res?.data?.meta.total : result.data.upcoming,
          // payload.priorities && payload.value == 2
          //   ? res?.data?.meta.total
          //   : result.data.upcoming
          countHistory:
            payload.value == 0 ? res?.data?.meta.total : result.data.history,
          // payload.priorities && payload.value == 0
          //   ? res?.data?.meta.total
          //   : result.data.history
          now: 0,
          later: 0,
          listKpi: kpi &&
            kpi.data && [
              ...Object.entries(kpi.data).map(([key, value]) => {
                return {
                  title: displayStatKey(payload.t, key, "visio"),
                  value: value,
                  tr: false,
                };
              }),
            ],
          listKpiDate:
            kpiDate &&
            kpiDate.data &&
            Object.entries(kpiDate.data).map(([key, value]) => {
              return { title: key, value: value, tr: true };
            }),
        },
      });

      dispatch({ type: SET_LOADING_TAB_VISIO, payload: false });
      dispatch(setOpenDrawerVisio(false));
    }
  } catch (err) {
    console.log(err.message);
    dispatch({ type: SET_LOADING_TAB_VISIO, payload: false });

    if (err?.message !== "canceled") {
      dispatch({ type: SET_LOADING_TAB_VISIO, payload: false });
    }
    if (err?.response?.status === 500) {
      toastNotification(
        "error",
        payload.t("toasts.somethingWrong"),
        "topRight"
      );
    }
  }
};

export const getDetailsMeet = (payload) => async (dispatch) => {
  dispatch({ type: SET_LOADING_DETAILS_VISIO, payload: true });

  try {
    const res = await generateAxios(
      URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
    ).get(`/get-task/${payload.keyMeet}`);
    dispatch({
      type: GET_DETAILS_VISIO,
      payload: {
        detailsMeet: res?.data?.data,
      },
    });

    dispatch({ type: SET_LOADING_DETAILS_VISIO, payload: false });
  } catch (err) {
    dispatch({
      type: GET_DETAILS_VISIO,
      payload: {
        detailsMeet: [],
      },
    });
    dispatch({ type: SET_LOADING_DETAILS_VISIO, payload: false });

    toastNotification("error", payload.t("toasts.somethingWrong"), "topRight");
  }
};
export const setNotificationList = (payload) => async (dispatch) => {
  const { pageNotificationList, notificationList } = await store.getState()
    .visioList;
  // dispatch({ type: SET_LOADING_DETAILS_VISIO, payload: true });
  dispatch({ type: SET_LOAD_NOTIF, payload: true });
  try {
    const response = await MainService.getNotifications(
      payload.page || 1,
      3,
      payload.unread
    );

    dispatch({
      type: SET_NOTIFICATION_LIST,
      payload: {
        notificationList:
          payload.page > 1
            ? [...notificationList, ...response?.data?.data]
            : response?.data?.data,
        pageNotificationList: response.data.meta.current_page,
        lastPageNotificationList: response.data.meta.last_page,
      },
    });
    dispatch({
      type: SET_PAGE_NOTIFICATION_LIST,
      payload: response.data.meta.current_page,
    });
    dispatch({
      type: SET_LAST_PAGE_NOTIFICATION_LIST,
      payload: response.data.meta.last_page,
    });
    dispatch({ type: SET_LOAD_NOTIF, payload: false });
  } catch (err) {
    // dispatch({ type: SET_LOADING_DETAILS_VISIO, payload: false });
    dispatch({ type: SET_LOAD_NOTIF, payload: false });

    toastNotification("error", payload.t("toasts.somethingWrong"), "topRight");
  }
};

export const setAllNotifsAsRead = (payload) => async (dispatch) => {
  dispatch({
    type: MAKE_ALL_NOTIFS_READ,
    payload,
  });
};

export const getRemindersList = (payload) => async (dispatch) => {
  // dispatch({ type: SET_LOADING_DETAILS_VISIO, payload: true });

  try {
    // const response = await MainService.getTasksReminders(3);
    dispatch({
      type: GET_REMINDERS_LIST,
      payload,
    });
  } catch (err) {
    // dispatch({ type: SET_LOADING_DETAILS_VISIO, payload: false });

    toastNotification("error", payload.t("toasts.somethingWrong"), "topRight");
  }
};
export const setRemindersList = (payload) => async (dispatch) => {
  // dispatch({ type: SET_LOADING_DETAILS_VISIO, payload: true });
  const { remindersListVisio } = await store.getState().visioList;
  dispatch({
    type: SET_REMINDERS_LIST,
    payload: {
      remindersListVisio: {
        ...remindersListVisio,
        meta: payload.meta,
        data: [...payload.data],
      },
    },
  });
};
export const setCountReminders = (payload) => async (dispatch) => {
  // dispatch({ type: SET_LOADING_DETAILS_VISIO, payload: true });

  dispatch({
    type: SET_COUNT_REMINDERS,
    payload,
  });
};

export const resetListViseo = () => async (dispatch) => {
  dispatch({
    type: GET_DATA_CHANGE_VISIO,
    payload: {
      listMeet: [],
      lastPage: 1,
      keyMeet: "",
      page: 1,
      tabKey: 1,
      countToday: 0,
      countUpComing: 0,
      countHistory: 0,
      now: 0,
      later: 0,
      loadTabs: true,
      loadDetails: false,
      detailsMeet: {},

      openDrawerVisio: false,
    },
  });
};
export const setListMeet = (payload) => ({
  type: SET_LIST_MEET,
  payload: payload,
});
export const setDetailsMeet = (payload) => ({
  type: SET_DETAILS_MEET,
  payload: payload,
});
export const setDetailsMeetExternal = (payload) => ({
  type: SET_DETAILS_MEET_EXTERNAL,
  payload: payload,
});
export const setKpiVisio = (payload) => ({
  type: SET_KPI,
  payload: payload,
});
export const setKpiDateVisio = (payload) => ({
  type: SET_KPI_DATE,
  payload: payload,
});
export const setKeyMeet = (payload) => ({
  type: SET_KEY_MEET,
  payload: payload,
});
export const setLimitVisio = (payload) => ({
  type: SET_LIMIT,
  payload: payload,
});
export const setSearchListVisio = (payload) => ({
  type: SET_SEARCH_LIST_VISIO,
  payload: payload,
});
export const changeListNotifs = (payload) => ({
  type: UPDATE_NOTIFICATION_LIST,
  payload: payload,
});
export const setPage = (payload) => ({
  type: SET_PAGE,
  payload: payload,
});

export const setTabKey = (payload) => ({
  type: SET_TAB_KEY,
  payload: payload,
});
export const setNow = (payload) => ({
  type: SET_NOW,
  payload: payload,
});
export const setLoadNotif = (payload) => ({
  type: SET_LOAD_NOTIF,
  payload: payload,
});
export const setLater = (payload) => ({
  type: SET_LATER,
  payload: payload,
});
export const setHistory = (payload) => ({
  type: SET_HISTORY_COUNT,
  payload: payload,
});
export const setCountNotificationVisio = (payload) => ({
  type: SET_NOTIFICATION_COUNT,
  payload,
});
export const setLastPage = (payload) => ({
  type: SET_LAST_PAGE,
  payload: payload,
});
export const setLabel = (payload) => ({
  type: SET_LABEL,
  payload: payload,
});
export const setOpenDrawerVisio = (payload) => ({
  type: SET_OPEN_DRAWER_VISIO,
  payload: payload,
});
export const updateVisio = ({ data, t, lastStartDate }) => {
  return async (dispatch) => {
    const {
      listMeet,
      tabKey,
      now,
      later,
      countUpComing,
      countToday,
      keyMeet,
      search,
    } = await store.getState().visioList;
    const { user } = await store.getState().user;

    const currentDate = moment().format(user.location.date_format);
    const start_date = moment(data.data.start_date, user.location.date_format);

    if (3 == data.data.tasks_type_id) {
      if (
        (tabKey === 1 && currentDate === data.data.start_date) ||
        (tabKey === 2 &&
          start_date.isAfter(
            moment(currentDate, user.location.date_format),
            "day"
          )) ||
        (tabKey === 0 &&
          start_date.isBefore(
            moment(currentDate, user.location.date_format),
            "day"
          ))
      ) {
        dispatch(
          setListMeet(
            listMeet.map((el) =>
              el.id === data.data.id ? { ...data.data, key: data.data.id } : el
            )
          )
        );
        dispatch(setKeyMeet(data.data.id));
        dispatch(setDetailsMeet(data.data));
        dispatch(setOpenDrawerVisio(false));
      } else if (currentDate === data.data.start_date) {
        dispatch(setNow({ now: now, countToday: countToday + 1 }));
        // dispatch(
        //   onChangeTabVisio({
        //     value: 1,
        //     keyMeet: data.data.id,
        //     t,
        //     search: search || "",
        //   })
        // );
        dispatch(setTabKey(1));

        dispatch(setDetailsMeet(data.data));

        if (
          moment(lastStartDate, user.location.date_format).isAfter(
            moment(currentDate, user.location.date_format),
            "day"
          )
        ) {
          dispatch(
            setLater({ later: later, countUpComing: countUpComing - 1 })
          );
        }

        // dispatch(setLastPage(Math.ceil((countToday + 1) / 10)));
      }
      //--------------------//
      else if (
        start_date.isAfter(
          moment(currentDate, user.location.date_format),
          "day"
        )
      ) {
        dispatch(setLater({ later: later, countUpComing: countUpComing + 1 }));
        dispatch(setTabKey(2));

        // dispatch(
        //   onChangeTabVisio({
        //     value: 2,
        //     keyMeet: data.data.id,
        //     t,
        //     search: search || "",
        //   })
        // );
        dispatch(setDetailsMeet(data.data));

        if (
          moment(lastStartDate, user.location.date_format).isSame(
            moment(currentDate, user.location.date_format),
            "day"
          )
        ) {
          dispatch(setNow({ now: now, countToday: countToday - 1 }));
        }
        // dispatch(setLastPage(Math.ceil((countUpComing + 1) / 10)));
      }

      //--------------------//
      else if (
        start_date.isBefore(
          moment(currentDate, user.location.date_format),
          "day"
        )
      ) {
        // dispatch(
        //   setLater({ later: later + 1, countUpComing: countUpComing + 1 })
        // );

        // dispatch(
        //   onChangeTabVisio({
        //     value: 0,
        //     keyMeet: data.data.id,
        //     t,
        //     search: search || "",
        //   })
        // );
        dispatch(setTabKey(0));

        dispatch(setDetailsMeet(data.data));

        if (
          moment(lastStartDate, user.location.date_format).isAfter(
            moment(currentDate, user.location.date_format),
            "day"
          )
        ) {
          dispatch(
            setLater({ later: later, countUpComing: countUpComing - 1 })
          );
        }
        if (
          moment(lastStartDate, user.location.date_format).isSame(
            moment(currentDate, user.location.date_format),
            "day"
          )
        ) {
          dispatch(setNow({ now: now, countToday: countToday - 1 }));
        }

        // dispatch(setLastPage(Math.ceil((countUpComing + 1) / 10)));
      } else {
        if (
          moment(lastStartDate, user.location.date_format).isAfter(
            moment(currentDate, user.location.date_format),
            "day"
          )
        ) {
          dispatch(
            setLater({ later: later, countUpComing: countUpComing - 1 })
          );

          dispatch(
            setListMeet(listMeet.filter((el) => el.id !== data.data.id))
          );
          if (keyMeet === data.data.id) {
            dispatch(setDetailsMeet({}));
          }
        }
        if (
          moment(lastStartDate, user.location.date_format).isBefore(
            moment(currentDate, user.location.date_format),
            "day"
          )
        ) {
          dispatch(
            setListMeet(listMeet.filter((el) => el.id !== data.data.id))
          );
          if (keyMeet === data.data.id) {
            dispatch(setDetailsMeet({}));
          }
        }
        if (currentDate === lastStartDate) {
          dispatch(setNow({ now: now, countToday: countToday - 1 }));
          dispatch(
            setListMeet(listMeet.filter((el) => el.id !== data.data.id))
          );
          if (keyMeet === data.data.id) {
            dispatch(setDetailsMeet({}));
          }
        }
      }
    }
    // if (
    //   (tabKey === 1 && currentDate === data.data.start_date) ||
    //   (tabKey === 2 && start_date.isAfter(currentDate, "day"))
    // ) {
    // const updatedListMeet = [{ ...data.data, newMeet: true }, ...listMeet];
    // dispatch(setListMeet(updatedListMeet));
  };

  // }
};
export const createVisio = ({ data, t }) => {
  return async (dispatch) => {
    const {
      listMeet,
      tabKey,
      now,
      later,
      countUpComing,
      countToday,
      loadTabs,
      search,
    } = await store.getState().visioList;

    const { user } = await store.getState().user;
    const currentDate = moment().format(user.location.date_format);
    const start_date = moment(data.data.start_date, user.location.date_format);
    if (3 == data.data.tasks_type_id) {
      if (
        (tabKey === 1 && currentDate === data.data.start_date) ||
        (tabKey === 2 && start_date.isAfter(moment(), "day")) ||
        (tabKey === 0 && start_date.isBefore(moment(), "day"))
      ) {
        const updatedListMeet = [{ ...data.data, newMeet: true }, ...listMeet];
        dispatch(
          setListMeet(updatedListMeet.map((el) => ({ ...el, key: el.id })))
        );
        if (tabKey === 1) {
          dispatch(setNow({ now: now + 1, countToday: countToday + 1 }));
        }
        if (tabKey === 2) {
          dispatch(
            setLater({ later: later + 1, countUpComing: countUpComing + 1 })
          );
        }

        dispatch(setKeyMeet(data.data.id));
        dispatch(setDetailsMeet(data.data));
      } else if (currentDate === data.data.start_date) {
        dispatch(setNow({ now: now, countToday: countToday + 1 }));
        // dispatch(
        //   onChangeTabVisio({
        //     value: 1,
        //     keyMeet: data.data.id,
        //     t,
        //     search: search || "",
        //   })
        // );
        dispatch(setPage(1));
        dispatch(setTabKey(1));

        dispatch(setDetailsMeet(data.data));

        // dispatch(setLastPage(Math.ceil((countToday + 1) / 10)));
      } else if (
        start_date.isAfter(moment(moment(), user.location.date_format), "day")
      ) {
        dispatch(setPage(1));

        dispatch(setLater({ later: later, countUpComing: countUpComing + 1 }));
        // dispatch(
        //   onChangeTabVisio({
        //     value: 2,
        //     keyMeet: data.data.id,
        //     t,
        //     search: search || "",
        //   })
        // );
        dispatch(setTabKey(2));

        if (!loadTabs) dispatch(setDetailsMeet(data.data));

        // dispatch(setLastPage(Math.ceil((countUpComing + 1) / 10)));
      } else if (
        start_date.isBefore(moment(moment(), user.location.date_format), "day")
      ) {
        dispatch(setPage(1));

        // dispatch(
        //   setLater({ later: later + 1, countUpComing: countUpComing + 1 })
        // );
        // await dispatch(
        //   onChangeTabVisio({
        //     value: 0,
        //     keyMeet: data.data.id,
        //     t,
        //     search: search || "",
        //   })
        // );
        dispatch(setTabKey(0));

        if (!loadTabs) dispatch(setDetailsMeet(data.data));

        // dispatch(setLastPage(Math.ceil((countUpComing + 1) / 10)));
      }

      // if (
      //   (tabKey === 1 && currentDate === data.data.start_date) ||
      //   (tabKey === 2 && start_date.isAfter(currentDate, "day"))
      // ) {
      // const updatedListMeet = [{ ...data.data, newMeet: true }, ...listMeet];
      // dispatch(setListMeet(updatedListMeet));
    }
  };

  // }
};

export const createVisioMercure = (data, t) => {
  return async (dispatch) => {
    const {
      listMeet,
      tabKey,
      now,
      later,
      keyMeet,
      search,
      countUpComing,
      countToday,
      listKpi,
    } = await store.getState().visioList;
    const { user } = await store.getState().user;
    const currentDate = moment().format(user.location.date_format);
    const start_date = moment(data.data.start_date);
    dispatch(
      onChangeTabVisio({
        value: tabKey,
        keyMeet: keyMeet,
        t,
        search: search || "",
        listKpi,
      })
    );
    //   if (
    //     3 == data.data.tasks_type_id &&
    //     user.id !== data.data.creator.id
    //   ) {
    //     if (
    //       (tabKey === 1 &&
    //         currentDate ===
    //           moment(data.data.start_date).format(user.location.date_format)) ||
    //       (tabKey === 2 &&
    //         start_date.isAfter(
    //           moment(currentDate, user.location.date_format),
    //           "day"
    //         )) ||
    //       (tabKey === 0 &&
    //         start_date.isBefore(
    //           moment(currentDate, user.location.date_format),
    //           "day"
    //         ))
    //     ) {
    //       const updatedListMeet = [{ ...data.data, newMeet: true }, ...listMeet];

    //       if (tabKey === 1) {
    //         dispatch(setNow({ now: now + 1, countToday: countToday + 1 }));
    //       }
    //       if (tabKey === 2) {
    //         dispatch(
    //           setLater({ later: later + 1, countUpComing: countUpComing + 1 })
    //         );
    //       }

    //       dispatch(setListMeet(updatedListMeet));
    //       // dispatch(setKeyMeet(data.data.id));
    //       // dispatch(setDetailsMeet(data.data));
    //     } else if (
    //       currentDate ===
    //       moment(data.data.start_date).format(user.location.date_format)
    //     ) {
    //       dispatch(setNow({ now: now, countToday: countToday + 1 }));

    //       // dispatch(setLastPage(Math.ceil((countToday + 1) / 10)));
    //     } else if (
    //       start_date.isAfter(
    //         moment(currentDate, user.location.date_format),
    //         "day"
    //       )
    //     ) {
    //       dispatch(setLater({ later: later, countUpComing: countUpComing + 1 }));

    //       // dispatch(setLastPage(Math.ceil((countUpComing + 1) / 10)));
    //     } else if (
    //       start_date.isBefore(
    //         moment(currentDate, user.location.date_format),
    //         "day"
    //       )
    //     ) {
    //       // dispatch(
    //       //   setLater({ later: later + 1, countUpComing: countUpComing + 1 })
    //       // );
    //       // dispatch(setLastPage(Math.ceil((countUpComing + 1) / 10)));
    //     }

    //     // if (
    //     //   (tabKey === 1 && currentDate === data.data.start_date) ||
    //     //   (tabKey === 2 && start_date.isAfter(currentDate, "day"))
    //     // ) {
    //     // const updatedListMeet = [{ ...data.data, newMeet: true }, ...listMeet];
    //     // dispatch(setListMeet(updatedListMeet));
    //   }
  };

  // }
};
export const updateVisioMercure = ({ data, t, lastStartDate }) => {
  return async (dispatch) => {
    const {
      tabKey,

      keyMeet,
      search,
    } = await store.getState().visioList;
    dispatch(
      onChangeTabVisio({
        value: tabKey,
        keyMeet: keyMeet,
        t,
        search: search || "",
        stages_ids: "",
      })
    );
  };
};
// export const updateVisioMercure = ({ data, t, lastStartDate }) => {
//   return async (dispatch) => {
//     const {
//       listMeet,
//       tabKey,
//       now,
//       later,
//       countUpComing,
//       countToday,
//       keyMeet,
//       detailsMeet,
//     } = await store.getState().visioList;
//     const { user } = await store.getState().user;

//     const currentDate = moment().format(user.location.date_format);
//     const start_date = moment(data.data.start_date, user.location.date_format);
//     //   moment(
//     //     moment(
//     //       moment(currentDate, "DD-MM-YYYY"),
//     //       user.location.date_format
//     //     ),
//     //     user.location.date_format
//     //   ),
//     //   "day"
//     // )
//     if (
//       3 == data.data.tasks_type_id
//     ) {
//       if (
//         (tabKey === 1 &&
//           currentDate ===
//             moment(data.data.start_date, "DD-MM-YYYY").format(
//               user.location.date_format
//             )) ||
//         (tabKey === 2 &&
//           start_date.isAfter(
//             moment(currentDate, user.location.date_format),
//             "day"
//           )) ||
//         (tabKey === 0 &&
//           start_date.isBefore(
//             moment(currentDate, user.location.date_format),
//             "day"
//           ))
//       ) {
//         if (
//           tabKey == 2 &&
//           // moment(lastStartDate, user.location.date_format).isBefore(
//           //   moment(currentDate, user.location.date_format),
//           //   "day"
//           // ) &&
//           // moment(data?.data?.start_date, user.location.date_format).isAfter(
//           //   moment(currentDate, user.location.date_format),
//           //   "day"
//           // ) &&
//           moment(lastStartDate).format(user.location.date_format) !==
//             currentDate
//         ) {
//           alert("1");

//           dispatch(
//             setLater({ later: later, countUpComing: countUpComing + 1 })
//           );
//           dispatch(setListMeet([data.data, ...listMeet]));
//         } else if (
//           tabKey == 2 &&
//           moment(lastStartDate).format(user.location.date_format) ===
//             currentDate
//           //   &&
//           // moment(data?.data?.start_date, user.location.date_format).isAfter(
//           //   moment(currentDate, user.location.date_format),
//           //   "day"
//           // )
//         ) {
//           alert("2");
//           dispatch(setNow({ now: now, countToday: countToday - 1 }));
//           dispatch(
//             setLater({ later: later, countUpComing: countUpComing + 1 })
//           );
//           dispatch(setListMeet([data.data, ...listMeet]));
//         } else if (
//           tabKey == 1 &&
//           moment(
//             moment(lastStartDate).format(user.location.date_format),
//             user.location.date_format
//           ).isAfter(moment(currentDate, user.location.date_format), "day") &&
//           data?.data?.start_date === currentDate
//         ) {
//           dispatch(
//             setLater({ later: later, countUpComing: countUpComing - 1 })
//           );
//           dispatch(setNow({ now: now, countToday: countToday + 1 }));
//           dispatch(setListMeet([data.data, ...listMeet]));
//         } else if (
//           tabKey == 1 &&
//           moment(
//             moment(lastStartDate).format(user.location.date_format),
//             user.location.date_format
//           ).isBefore(moment(currentDate, user.location.date_format), "day") &&
//           data?.data?.start_date === currentDate
//         ) {
//           dispatch(setNow({ now: now, countToday: countToday + 1 }));
//           dispatch(setListMeet([data.data, ...listMeet]));
//         } else if (
//           tabKey == 0 &&
//           moment(
//             moment(lastStartDate).format(user.location.date_format),
//             user.location.date_format
//           ).isAfter(moment(currentDate, user.location.date_format), "day") &&
//           moment(data?.data?.start_date, user.location.date_format).isBefore(
//             moment(currentDate, user.location.date_format),
//             "day"
//           )
//         ) {
//           dispatch(
//             setLater({ later: later, countUpComing: countUpComing - 1 })
//           );
//           dispatch(setListMeet([data.data, ...listMeet]));
//         } else if (
//           tabKey == 0 &&
//           moment(lastStartDate).format(user.location.date_format) ===
//             currentDate &&
//           moment(data?.data?.start_date, user.location.date_format).isBefore(
//             currentDate,
//             "day"
//           )
//         ) {
//           dispatch(setNow({ now: now, countToday: countToday - 1 }));
//           dispatch(setListMeet([data.data, ...listMeet]));
//         } else {
//           alert("else in if");
//           dispatch(
//             setListMeet(
//               listMeet.map((el) => (el.id === data.data.id ? data.data : el))
//             )
//           );
//         }
//       }

//       // if (
//       //   (tabKey === 1 && currentDate === data.data.start_date) ||
//       //   (tabKey === 2 && start_date.isAfter(currentDate, "day"))
//       // ) {
//       // const updatedListMeet = [{ ...data.data, newMeet: true }, ...listMeet];
//       // dispatch(setListMeet(updatedListMeet));
//       else {
//         alert("else out if");

//         if (
//           moment(
//             moment(lastStartDate).format(user.location.date_format),
//             user.location.date_format
//           ).isAfter(moment(currentDate, user.location.date_format), "day")
//         ) {
//           dispatch(
//             setLater({ later: later, countUpComing: countUpComing - 1 })
//           );

//           dispatch(
//             setListMeet(listMeet.filter((el) => el.id !== data.data.id))
//           );
//           if (
//             (currentDate === data?.data?.start_date, user.location.date_format)
//           ) {
//             dispatch(setNow({ now: now, countToday: countToday + 1 }));
//           }
//           if (keyMeet === data.data.id) {
//             dispatch(setDetailsMeet({}));
//           }
//         }
//         if (
//           moment(
//             moment(lastStartDate).format(user.location.date_format),
//             user.location.date_format
//           ).isBefore(moment(currentDate, user.location.date_format), "day")
//         ) {
//           dispatch(
//             setListMeet(listMeet.filter((el) => el.id !== data.data.id))
//           );
//           if (
//             currentDate ===
//             moment(data?.data?.start_date, user.location.date_format)
//           ) {
//             dispatch(setNow({ now: now, countToday: countToday + 1 }));
//           }
//           if (
//             moment(data?.data?.start_date, user.location.date_format).isAfter(
//               moment(currentDate, user.location.date_format),
//               "day"
//             )
//           ) {
//             dispatch(
//               setLater({ later: later, countUpComing: countUpComing + 1 })
//             );
//           }
//           if (keyMeet === data.data.id) {
//             dispatch(setDetailsMeet({}));
//           }
//         }
//         if (
//           currentDate ===
//           moment(lastStartDate).format(user.location.date_format)
//         ) {
//           dispatch(setNow({ now: now, countToday: countToday - 1 }));
//           dispatch(
//             setListMeet(listMeet.filter((el) => el.id !== data.data.id))
//           );
//           if (
//             moment(data?.data?.start_date, user.location.date_format).isAfter(
//               moment(currentDate, user.location.date_format),
//               "day"
//             )
//           ) {
//             dispatch(
//               setLater({ later: later, countUpComing: countUpComing + 1 })
//             );
//           }
//           if (keyMeet === data.data.id) {
//             dispatch(setDetailsMeet({}));
//           }
//         }
//         // dispatch(setKeyMeet(data.data.id));
//         // dispatch(setDetailsMeet(data.data));

//         dispatch(setOpenDrawerVisio(false));
//       }
//     }
//   };

//   // }
// };
export const deleteVisioMercure = ({ data, t }) => {
  return async (dispatch) => {
    const {
      listMeet,
      later,
      countUpComing,
      search,
      now,
      countToday,
      tabKey,
      keyMeet,
    } = await store.getState().visioList;
    const { user } = await store.getState().user;

    const currentDate = moment().format(user.location.date_format);
    const start_date = moment(data.start_date, user.location.date_format);
    dispatch(
      onChangeTabVisio({
        value: tabKey,
        keyMeet: keyMeet,
        t,
        search: search || "",
      })
    );
    //   if (
    //     currentDate ===
    //     moment(data.data.start_date).format(user.location.date_format)
    //   ) {
    //     dispatch(setNow({ now: now, countToday: countToday - 1 }));
    //     if (tabKey == 1) {
    //       dispatch(setListMeet(listMeet.filter((el) => el.id !== data.tasks_id)));
    //     }
    //   }
    //   if (
    //     moment(start_date, user.location.date_format).isAfter(
    //       moment(currentDate, user.location.date_format),
    //       "day"
    //     )
    //   ) {
    //     dispatch(setLater({ later: later, countUpComing: countUpComing - 1 }));
    //     if (tabKey == 2) {
    //       dispatch(setListMeet(listMeet.filter((el) => el.id !== data.tasks_id)));
    //     }
    //   }
    //   if (
    //     moment(start_date, user.location.date_format).isBefore(
    //       moment(currentDate, user.location.date_format),
    //       "day"
    //     )
    //   ) {
    //     if (tabKey == 0) {
    //       dispatch(setListMeet(listMeet.filter((el) => el.id !== data.tasks_id)));
    //     }
    //   }
    //   if (keyMeet === data.tasks_id) {
    //     dispatch(setDetailsMeet({}));
    //   }
  };
};
export const updateLabelinListVisio = (payload) => async (dispatch) => {
  const { listMeet } = await store.getState().visioList;
  dispatch(
    setListMeet(
      listMeet.map((item) =>
        item.id === payload.id ? { ...item, label: payload.label } : item
      )
    )
  );
  dispatch(setLabel(payload.label));
};
