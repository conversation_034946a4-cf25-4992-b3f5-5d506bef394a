import { useMutation } from "@tanstack/react-query";
import { toastNotification } from "components/ToastNotification";
import mainService from "services/main.service";
import { useTranslation } from "react-i18next";
import {
  getOrGenerateTabId,
  insertSpaceBeforeTagOpening,
  uuid,
  systemMessageTypes,
  urlChatComponent,
  getUrlsFromMessage,
  imageExtensions,
  simpleMessageTypes,
  getName,
  getUserFromMsg,
} from "../utils/ConversationUtils";
import { useDispatch, useSelector } from "react-redux";
import {
  setAssetsToList,
  setImageToList,
  removeAllPollList,
  removePollList,
  setActionInReply,
  setActionInSearchStatus,
  setDeleteMessageInChatMembers,
  setNewMessageChatMemebers,
  setUpdateMessageInChatMembers,
  setDocumentToList,
  setLinksUpdateOrRemoveList,
  setOpenDrawer,
  setNumberUnreadMsg,
} from "new-redux/actions/chat.actions";
import { updateMessages } from "../utils/rqUpdate";
import { moment_timezone } from "App";
import { queryClient } from "index";
export const useActionMessage = (type) => {
  const { currentUser, searchMsgState, openDrawer, numberUnreadMsg } =
    useSelector((state) => state.chat);

  const selectedConversation = useSelector(
    (state) => state.ChatRealTime.selectedConversation
  );

  const dispatch = useDispatch();
  const { t } = useTranslation("common");

  return useMutation({
    mutationKey: "useActionMessage" + type + uuid(),

    mutationFn: async ({
      message_id,
      params,

      type_conversation,
      type_action,
    }) => {
      try {
        let response;

        const formData = new FormData();
        formData.append("message_id", `${message_id}`);
        formData.append("tab_id", getOrGenerateTabId());

        switch (type_action) {
          case "remove_react": {
            if (params.source !== "no_chat") {
              if (searchMsgState?.id)
                dispatch(
                  setActionInSearchStatus({
                    newMessage: params.params,
                    type: "remove_react",
                    message_id,
                    sender_id: currentUser?._id,
                  })
                );

              if (openDrawer.type === "thread")
                dispatch(
                  setActionInReply({
                    newMessage: params.params,
                    type: "remove_react",
                    message_id,
                    sender_id: currentUser?._id,
                  })
                );

              if (params.main_message_id)
                updateMessages(
                  { reaction: params.params, message_replies_id: message_id },
                  "remove_react_replies",
                  params.main_message_id,
                  selectedConversation?.id,
                  selectedConversation?.type,
                  currentUser?._id
                );
            }
            updateMessages(
              params.params,
              "remove_react",
              message_id,
              selectedConversation?.id,
              selectedConversation?.type,
              currentUser?._id
            );
            if (type_conversation === "room")
              formData.append(
                "source",
                params.source === "no_chat" ? "no_chat" : "chat"
              );
            formData.append("reaction", `${params.params}`);
            response = await mainService.removeReactOnMessage(
              formData,
              type_conversation
            );
            break;
          }
          case "add_react": {
            if (params.params > 5) throw new Error("error");
            updateMessages(
              params.params,
              "add_react",
              message_id,
              selectedConversation?.id,
              selectedConversation?.type,
              currentUser?._id
            );
            if (params.source !== "no_chat") {
              if (params.main_message_id)
                updateMessages(
                  { reaction: params.params, message_replies_id: message_id },
                  "add_react_replies",
                  params.main_message_id,
                  selectedConversation?.id,
                  selectedConversation?.type,
                  currentUser?._id
                );
              if (searchMsgState?.id)
                dispatch(
                  setActionInSearchStatus({
                    newMessage: params.params,
                    type: "add_react",
                    message_id,
                    sender_id: currentUser?._id,
                  })
                );
              if (openDrawer.type === "thread")
                dispatch(
                  setActionInReply({
                    newMessage: params.params,
                    type: "add_react",
                    message_id,
                    sender_id: currentUser?._id,
                  })
                );
            }
            if (type_conversation === "room")
              formData.append(
                "source",
                params.source === "no_chat" ? "no_chat" : "chat"
              );

            formData.append("reaction", `${params.params}`);
            response = await mainService.reactMessage(
              formData,
              type_conversation
            );
            break;
          }
          case "add_favorite": {
            updateMessages(
              null,
              "add_favorite",
              message_id,
              selectedConversation?.id,
              selectedConversation?.type,

              currentUser?._id
            );
            if (searchMsgState?.id)
              dispatch(
                setActionInSearchStatus({
                  newMessage: null,
                  type: "add_favorite",
                  message_id,
                  sender_id: currentUser?._id,
                })
              );

            response = await mainService.addFavorisMessage(
              formData,
              type_conversation
            );
            break;
          }
          case "remove_favorite": {
            updateMessages(
              null,
              "remove_favorite",
              message_id,
              selectedConversation?.id,
              selectedConversation?.type,
              currentUser?._id
            );
            if (searchMsgState?.id)
              dispatch(
                setActionInSearchStatus({
                  newMessage: null,
                  type: "remove_favorite",
                  message_id,
                  sender_id: currentUser?._id,
                })
              );

            response = await mainService.removeFavorisMessage(
              formData,
              type_conversation
            );
            break;
          }
          case "remove_message":
            if (type_conversation === "room")
              formData.append(
                "source",
                params.source === "no_chat" ? "no_chat" : "chat"
              );
            response = await mainService.deleteMessage(
              formData,
              type_conversation
            );
            break;
          case "forward_message": {
            let roomsId = [];

            let usersId = [];
            const newMessage =
              params?.msg.message?.trim().length > 0
                ? insertSpaceBeforeTagOpening(params?.msg.message.trim())
                : "";
            params.users.forEach((item) => {
              if (item.email) usersId.push(item._id);
              else roomsId.push(item._id);
            });
            const data = {
              receiver_ids: [...new Set(usersId)].toString(),
              room_ids: [...new Set(roomsId)].toString(),
              urls: getUrlsFromMessage(newMessage).toString(),
            };

            if (getUrlsFromMessage(newMessage).length === 0) delete data.urls;

            response = await mainService.forwardMessage(
              message_id,
              data,
              type_conversation
            );

            break;
          }
          case "send_voice_message": {
            const formDataVoice = new FormData();
            formDataVoice.append(
              type_conversation === "room" ? "room_id" : "receiver_id",
              `${selectedConversation?.id}`
            );
            formDataVoice.append("tab_id", getOrGenerateTabId());
            formDataVoice.append("voice", params.voice);
            formDataVoice.append("voice_size", params.voice_size);

            formDataVoice.append("voice_name", uuid());
            response = await mainService.sendMessageVoice(
              formDataVoice,
              type_conversation
            );
            break;
          }
          case "add_saved": {
            updateMessages(
              null,
              "add_saved",
              message_id,
              selectedConversation?.id,
              selectedConversation?.type,

              currentUser?._id
            );
            if (searchMsgState?.id)
              dispatch(
                setActionInSearchStatus({
                  newMessage: null,
                  type: "add_saved",
                  message_id,
                  sender_id: currentUser?._id,
                })
              );

            response = await mainService.addSavedMsg(
              formData,
              type_conversation
            );
            break;
          }
          case "remove_saved": {
            updateMessages(
              null,
              "remove_saved",
              message_id,
              selectedConversation?.id,
              selectedConversation?.type,

              currentUser?._id
            );
            if (searchMsgState?.id)
              dispatch(
                setActionInSearchStatus({
                  newMessage: null,
                  type: "remove_saved",
                  message_id,
                  sender_id: currentUser?._id,
                })
              );

            response = await mainService.removeSavedMsg(
              formData,
              type_conversation
            );
            break;
          }
          case "update_message": {
            let data = new URLSearchParams();
            const newMessage = insertSpaceBeforeTagOpening(params?.msg?.trim());

            data.append("message", newMessage);
            if (type_conversation === "room")
              data.append(
                "source",
                params.source === "no_chat" ? "no_chat" : "chat"
              );
            data.append("tab_id", getOrGenerateTabId());
            params.taggedPerson.length > 0 &&
              type_conversation === "room" &&
              data.append("tags", params.taggedPerson);
            getUrlsFromMessage(newMessage).length > 0 &&
              data.append("urls", getUrlsFromMessage(newMessage).toString());
            response = await mainService.updateMessage(
              message_id,
              data,
              type_conversation
            );
            break;
          }
          case "add_vote": {
            const formDataVote = new FormData();
            formDataVote.append("option_id", params.oldValue);
            formDataVote.append("tab_id", getOrGenerateTabId());
            updateMessages(
              {
                remove:
                  params.value.length === 0 ||
                  !params.value.includes(params.oldValue),
                values: params.value,
              },
              "add_vote",
              message_id,
              selectedConversation?.id,
              selectedConversation?.type,
              currentUser?._id
            );

            response = await mainService.addVote(
              formDataVote,
              type_conversation
            );
            break;
          }
          default:
            break;
        }

        return response;
      } catch (err) {
        if (type_action === "remove_react") {
          updateMessages(
            params.params,
            "add_react",
            message_id,
            selectedConversation?.id,
            selectedConversation?.type,

            currentUser?._id
          );
          if (searchMsgState?.id)
            dispatch(
              setActionInSearchStatus({
                newMessage: params.params,
                type: "add_react",
                message_id,
                sender_id: currentUser?._id,
              })
            );
          if (openDrawer.type === "thread")
            dispatch(
              setActionInReply({
                newMessage: params.params,
                type: "add_react",
                message_id,
                sender_id: currentUser?._id,
              })
            );
          if (params.main_message_id)
            updateMessages(
              { reaction: params.params, message_replies_id: message_id },
              "add_react_replies",
              params.main_message_id,
              selectedConversation?.id,
              selectedConversation?.type,
              currentUser?._id
            );
        } else if (type_action === "add_react") {
          updateMessages(
            params.params,
            "remove_react",
            message_id,
            selectedConversation?.id,
            selectedConversation?.type,

            currentUser?._id
          );
          if (params.main_message_id)
            updateMessages(
              { reaction: params.params, message_replies_id: message_id },
              "remove_react_replies",
              params.main_message_id,
              selectedConversation?.id,
              selectedConversation?.type,
              currentUser?._id
            );

          if (searchMsgState?.id)
            dispatch(
              setActionInSearchStatus({
                newMessage: null,
                type: "remove_react",
                message_id,
                sender_id: currentUser?._id,
              })
            );
          if (openDrawer.type === "thread")
            dispatch(
              setActionInReply({
                newMessage: params.params,
                type: "remove_react",
                message_id,
                sender_id: currentUser?._id,
              })
            );
        } else if (type_action === "add_favorite") {
          updateMessages(
            null,
            "remove_favorite",
            message_id,
            selectedConversation?.id,
            selectedConversation?.type,

            currentUser?._id
          );
          if (searchMsgState?.id)
            dispatch(
              setActionInSearchStatus({
                newMessage: null,
                type: "remove_favorite",
                message_id,
                sender_id: currentUser?._id,
              })
            );
        } else if (type_action === "remove_favorite") {
          updateMessages(
            null,
            "add_favorite",
            message_id,
            selectedConversation?.id,
            selectedConversation?.type,

            currentUser?._id
          );
          if (searchMsgState?.id)
            dispatch(
              setActionInSearchStatus({
                newMessage: null,
                type: "add_favorite",
                message_id,
                sender_id: currentUser?._id,
              })
            );
        } else if (type === "add_saved") {
          updateMessages(
            null,
            "remove_saved",
            message_id,
            selectedConversation?.id,
            selectedConversation?.type,

            currentUser?._id
          );
          if (searchMsgState?.id)
            dispatch(
              setActionInSearchStatus({
                newMessage: null,
                type: "remove_saved",
                message_id,
                sender_id: currentUser?._id,
              })
            );
        } else if (type === "remove_saved") {
          updateMessages(
            null,
            "add_saved",
            message_id,
            selectedConversation?.id,
            selectedConversation?.type,

            currentUser?._id
          );
          if (searchMsgState?.id)
            dispatch(
              setActionInSearchStatus({
                newMessage: null,
                type: "remove_saved",
                message_id,
                sender_id: currentUser?._id,
              })
            );
        } else if (type_action === "add_vote") {
          updateMessages(
            params.oldPoll,
            "undo_vote",
            message_id,
            selectedConversation?.id,
            selectedConversation?.type,

            currentUser?._id
          );
        }

        throw new Error(err);
      }
    },
    onError: () => {
      toastNotification("error", t("toasts.errorFetchApi"), "topRight");
    },
    onSuccess: async (response) => {
      const url = new URL(window.location.href);

      if (response) {
        try {
          if (response.data.action === "forward message") {
            const senderName = getUserFromMsg(response.data?.sender_id);
            toastNotification(
              "success",
              response.data?.sender_id === currentUser?._id
                ? t("chat.forward.you_forward_your_message")
                : t("chat.forward.you_forward_message") +
                    getName(senderName?.name, "name"),
              "topRight"
            );

            dispatch(
              setNumberUnreadMsg({
                id: null,
                number: 0,
              })
            );
          } else if (response.data.action === "delete message") {
            updateMessages(
              response.data.data,
              "deleted",
              response.data.data._id,
              selectedConversation?.id,
              selectedConversation?.type,
              null
            );
            if (urlChatComponent.includes(url.pathname)) {
              if (response.data.data.poll?.end_date)
                dispatch(removePollList(response.data.data._id));

              dispatch(removeAllPollList(response.data.data._id));

              if (response.data.data.parent_id)
                updateMessages(
                  response.data.data._id,
                  "deleted_replies",
                  response.data.data.parent_id,
                  selectedConversation?.id,
                  selectedConversation?.type,

                  currentUser?._id
                );
              if (searchMsgState?.id)
                dispatch(
                  setActionInSearchStatus({
                    newMessage: response.data.data,
                    type: "deleted",
                    message_id: response.data.data._id,
                    sender_id: currentUser?._id,
                  })
                );
              if (openDrawer.type === "thread")
                dispatch(
                  setActionInReply({
                    newMessage: response.data.data,
                    type: "deleted",
                    message_id: response.data.data._id,
                    sender_id: currentUser?._id,
                  })
                );

              if (response.data.data.important)
                updateMessages(
                  null,
                  "remove_saved",
                  response.data.data._id,
                  selectedConversation?.id,
                  selectedConversation?.type,

                  currentUser?._id
                );
              if (response.data.data.favoris?.includes(currentUser?._id))
                updateMessages(
                  null,
                  "remove_favorite",
                  response.data.data._id,
                  selectedConversation?.id,
                  selectedConversation?.type,

                  currentUser?._id
                );
              if (openDrawer.type === "info") {
                if (response.data.data.file?.length > 0) {
                  response.data.data.file.forEach((element) => {
                    if (imageExtensions.includes(element.type.split("/")[1])) {
                      dispatch(setAssetsToList({ images: -1, documents: 0 }));
                      dispatch(setImageToList({ element, type: "delete" }));
                    } else {
                      dispatch(setDocumentToList({ element, type: "delete" }));
                      dispatch(setAssetsToList({ images: 0, documents: -1 }));
                    }
                  });
                }

                if (simpleMessageTypes.includes(response.data.data?.type)) {
                  dispatch(
                    setLinksUpdateOrRemoveList({
                      type: "delete",
                      message_id: response.data.data._id,
                    })
                  );
                }
              } else if (
                openDrawer.type === "forward" &&
                openDrawer.external?._id === response.data.data._id
              ) {
                dispatch(
                  setOpenDrawer({
                    type: "",
                    external: null,
                  })
                );
              }

              dispatch(
                setDeleteMessageInChatMembers({
                  sender: currentUser,
                  _id: response.data.data._id,
                  type: selectedConversation?.type,
                  discussion_id: selectedConversation?.id,
                  reaction: null,
                })
              );
            }
          } else if (type === "send_message_voice") {
            updateMessages(
              response.data.message,
              "new_message",
              null,
              selectedConversation?.id,
              selectedConversation?.type,

              currentUser?._id,
              selectedConversation?.conversationId
            );

            dispatch(
              setNewMessageChatMemebers({
                data: response.data.message,
                discussion_id: selectedConversation?.id,
                user: selectedConversation,
                sender: currentUser,
                type: selectedConversation?.type,
                current_user: true,
              })
            );
            if (numberUnreadMsg.number > 0)
              dispatch(
                setNumberUnreadMsg({
                  id: null,
                  number: 0,
                })
              );
          } else if (response.data.action === "add react") {
            if (urlChatComponent.includes(url.pathname)) {
              dispatch(
                setUpdateMessageInChatMembers({
                  data: response.data.message,

                  reaction: Number(response.data.reaction),

                  type: selectedConversation?.type,
                  discussion_id: selectedConversation?.id,
                  sender: currentUser,
                })
              );
            }
          } else if (response.data.action === "delete react") {
            const getMessagesData = await queryClient.getQueryData([
              "getMessages",
              selectedConversation?.id,
              selectedConversation?.type,
            ]);
            if (
              urlChatComponent.includes(url.pathname) &&
              getMessagesData?.pages[0]?.data
            ) {
              let FilterdMessages = [...getMessagesData?.pages[0]?.data];
              dispatch(
                setUpdateMessageInChatMembers({
                  last_message_date: FilterdMessages?.at(0)?.created_at,
                  data: FilterdMessages?.at(0)?.deleted_at
                    ? null
                    : systemMessageTypes.includes(FilterdMessages?.at(0)?.type)
                    ? {
                        _id: FilterdMessages?.at(1)?._id,
                        type: FilterdMessages?.at(1)?.type,
                        message: FilterdMessages?.at(1)?.message,
                      }
                    : {
                        _id: FilterdMessages?.at(0)?._id,
                        type: FilterdMessages?.at(0)?.type,
                        message: FilterdMessages?.at(0)?.message,
                      },
                  reaction: "delete_react",
                  type: selectedConversation?.type,
                  discussion_id: selectedConversation?.id,
                  sender: getUserFromMsg(
                    FilterdMessages?.at(
                      systemMessageTypes.includes(FilterdMessages?.at(0)?.type)
                        ? 1
                        : 0
                    )?.sender_id
                  ),
                })
              );
            }
          } else if (type === "update_message") {
            updateMessages(
              response.data.result,
              "update",
              response.data.result._id,
              selectedConversation?.id,
              selectedConversation?.type,

              null
            );

            if (urlChatComponent.includes(url.pathname)) {
              if (response.data.result.main_message)
                updateMessages(
                  response.data.result,
                  "update_replies",
                  response.data.result?.main_message?._id,
                  selectedConversation?.id,
                  selectedConversation?.type,
                  null
                );
              if (searchMsgState?.id)
                dispatch(
                  setActionInSearchStatus({
                    newMessage: response.data.result,
                    type: "update",
                    message_id: response.data.result._id,
                    sender_id: null,
                  })
                );

              if (openDrawer.type === "thread")
                dispatch(
                  setActionInReply({
                    newMessage: response.data.result,
                    type: "update",
                    message_id: response.data.result._id,
                    sender_id: null,
                  })
                );
              if (openDrawer.type === "info") {
                const urls = getUrlsFromMessage(response.data.result?.message);

                dispatch(
                  setLinksUpdateOrRemoveList({
                    list: urls?.map((item) => ({
                      url: item,
                      message_id: response.data.result?._id,
                      user: currentUser?.name,
                      created_at: moment_timezone(new Date()).format(),
                    })),
                    type: urls.length > 0 ? "update" : "delete",
                    message_id: response.data.result?._id,
                  })
                );
              }
              dispatch(
                setUpdateMessageInChatMembers({
                  data: response.data.result,
                  last_message_date: response.data.result.updated_at,
                  type: selectedConversation?.type,
                  discussion_id: selectedConversation?.id,
                  sender: currentUser,
                  reaction: null,
                })
              );
            }
          }
        } catch (error) {
          console.log(error);
        }
      }
    },
  });
};
