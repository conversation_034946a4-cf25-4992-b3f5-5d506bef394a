import { memo, useCallback, useEffect, useMemo, useState } from "react";
import { Divider, Skeleton, Space, Tag } from "antd";
import { toastNotification } from "components/ToastNotification";
import { useTranslation } from "react-i18next";
import {
  getKpiFamily,
  getKpiFamilyDeals,
  getKpiFamilyTicket,
} from "../services/services";
import CardStat from "pages/components/CardStat";

const KpiFamily = memo(
  ({
    familyId,
    shouldFetchData,
    pipelineId,
    pipelines,
    dataFolders,
    idFolder,
    total,
  }) => {
    //
    const [t] = useTranslation("common");

    //
    const [dataSource, setDataSource] = useState(null);
    const [dataSourceDeals, setDataSourceDeals] = useState(null);
    const [dataSourceTicket, setDataSourceTicket] = useState(null);
    //
    // console.log({ dataSource, dataSourceDeals, dataSourceTicket });
    //
    const fetchKpiFamily = useCallback(
      async (isMounted) => {
        if (!isMounted || !familyId || !shouldFetchData) return;
        try {
          const {
            data: { data },
          } = await getKpiFamily(familyId);
          setDataSource(data);
          if (familyId === 3 && !!pipelineId) {
            const {
              data: { data },
            } = await getKpiFamilyDeals(pipelineId);
            setDataSourceDeals(data);
          }
          if (familyId === 6 && !!pipelineId) {
            const formData = new FormData();
            formData.append("family_id", familyId);
            formData.append("pipeline_id", pipelineId);
            formData.append("folder_id", idFolder);
            const {
              data: { data },
            } = await getKpiFamilyTicket(formData);
            data?.length
              ? setDataSourceTicket(data?.[0])
              : setDataSourceTicket(null);
          }
        } catch (err) {
          if (err?.response?.status === 401 || err?.code === "ERR_CANCELED")
            return;
          else {
            toastNotification("error", t("toasts.somethingWrong"), "topRight");
            throw new Error(err?.message ? err.message : err);
          }
        }
      },
      [familyId, shouldFetchData, pipelineId, idFolder, t]
    );

    useEffect(() => {
      let isMounted = true;
      fetchKpiFamily(isMounted);
      //
      return () => {
        isMounted = false;
      };
    }, [fetchKpiFamily]);
    //
    const isEmptyData = useMemo(
      () =>
        dataSource === null ||
        (Object.keys(dataSource).length === 0 &&
          dataSource.constructor === Object),
      [dataSource]
    );
    //
    return !isEmptyData ? (
      <Space split={<Divider type="vertical" style={{ height: "2rem" }} />}>
        <Space wrap>
          {Object.entries(dataSource).map(([key, value], index) => (
            <CardStat
              key={index}
              item={{
                title: t(`contacts.${key}`),
                value:
                  key === "total_elements" && value > total
                    ? `${total}/${value}`
                    : value,
              }}
            />
          ))}
        </Space>
        {/* Render Deals KPI */}
        {familyId === 3 && !!pipelineId && dataSourceDeals && (
          <div className="flex items-center space-x-1">
            <Tag bordered={false} color="default">
              <span className="text-[13px]">
                {pipelines?.find((pip) => pip.id === pipelineId)?.label}
              </span>
            </Tag>
            <Space wrap>
              {Object.entries(dataSourceDeals).map(([key, value], index) => (
                <CardStat
                  key={index}
                  item={{
                    title: key,
                    value: value,
                  }}
                />
              ))}
            </Space>
          </div>
        )}
        {/* Render Ticket KPI */}
        {familyId === 6 && !!pipelineId && !!dataSourceTicket && (
          <div className="flex items-center space-x-1">
            <Tag bordered={false} color="default">
              <span className="text-[13px]">
                {pipelines?.find((pip) => pip.id === pipelineId)?.label}
                {!!idFolder
                  ? ` - ${
                      dataFolders?.find((folder) => folder.value === idFolder)
                        ?.label
                    }`
                  : ""}
              </span>
            </Tag>
            <Space wrap>
              <CardStat
                key={"Total-Ticket"}
                item={{
                  title: "Total",
                  value: dataSourceTicket?.total,
                }}
              />
              {dataSourceTicket?.stages?.map(({ stageLabel, count }, i) => (
                <CardStat
                  key={i}
                  item={{
                    title: stageLabel,
                    value: count,
                  }}
                />
              ))}
            </Space>
          </div>
        )}
      </Space>
    ) : (
      <Space>
        {Array.from({ length: 3 }, () => null).map((_, index) => (
          <Skeleton.Input key={index} active style={{ width: 100 }} />
        ))}
      </Space>
    );
  }
);

export default KpiFamily;
