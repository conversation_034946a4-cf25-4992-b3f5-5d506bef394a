import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Tooltip } from "antd";
import { HiOutlineBuildingOffice, HiOutlineUserGroup } from "react-icons/hi2";
import { CloseOutlined, LoadingOutlined } from "@ant-design/icons";
import { AiOutlineShoppingCart } from "react-icons/ai";
import { LuPalmtree } from "react-icons/lu";
import AffectationContent from "./AffectationContent";
import { useEffect, useState } from "react";
import { toastNotification } from "../../../components/ToastNotification";

import MainService from "../../../services/main.service";

import { CgUserlane } from "react-icons/cg";
import { setRefreshMailInbox } from "new-redux/actions/mail.actions";
import { useDispatch } from "react-redux";
import { FiUsers } from "react-icons/fi";
import { Blocks, HeartHandshake } from "lucide-react";
import TicketIconSphere from "components/icons/TicketIconSphere";
// import FormCreate from "pages/clients&users/components/FormCreate";
//

//
const Affectation = ({
  affectation,
  id,
  setDataSource,
  t,
  user,
  access,
  transfert,
  owner,
  usedAccount,
  type,
  setDetailsMail,
  fromName,
  fromEmail,
  idEmail,
  openAction,
}) => {
  const [loadingDelete, setLoadingDelete] = useState(false);
  const [openPopCon, setOpenPopCon] = useState(false);
  const [idModule, setIdModule] = useState(null);
  const [openForm, setOpenForm] = useState(false);
  const [familyId, setFamilyId] = useState(null);
  const [mailingProps, setMailingProps] = useState({
    label: "",
    email: "",
    idEmail: "",
    id: "",
  });
  const dispatch = useDispatch();
  //
  const familiesName = {
    1: t("contacts.company"),
    2: t("contacts.contact"),
    3: "Deals",
    4: t("contacts.collegues"),
    5: t("contacts.product"),
    6: t("contacts.ticket"),
    7: t("contacts.project"),
    8: t("contacts.booking"),
    9: t("contacts.leads"),
  };
  const families = {
    1: {
      label: t("contacts.company"),
      icon: <HiOutlineBuildingOffice style={{ fontSize: 16 }} />,
    },
    2: {
      label: t("contacts.contact"),
      icon: <HiOutlineUserGroup style={{ fontSize: 16 }} />,
    },
    3: {
      label: "Deals",
      icon: <HeartHandshake size={17} />,
    },
    4: {
      label: t("contacts.collegues"),
      icon: <FiUsers style={{ fontSize: 16 }} />,
    },
    5: {
      label: t("contacts.product"),
      icon: <AiOutlineShoppingCart style={{ fontSize: 16 }} />,
    },
    6: {
      label: t("contacts.ticket"),
      icon: <TicketIconSphere size={19} />,
    },
    7: {
      label: t("contacts.project"),
      icon: <Blocks size={17} />,
    },
    8: {
      label: t("contacts.booking"),
      icon: <LuPalmtree style={{ fontSize: 16 }} />,
    },
    9: {
      label: t("contacts.leads"),
      icon: <CgUserlane style={{ fontSize: "15px" }} />,
    },
  };

  // const OpenDrawer = () => {
  //   setMailingProps({
  //     label: fromName?.length > 0 ? fromName : fromEmail,
  //     email: fromEmail,
  //     idEmail: idEmail,
  //     source: "email",
  //   });
  //   setFamilyId(idModule);
  //   setOpenForm(true);

  //   setOpenPopCon(false);
  // };

  const affectationTitle = (
    <div className="">
      <div className="flex flex-row items-center  justify-between">
        <span>{t("mailing.affecter")}</span>
        {/* {idModule ? <p>{t("voip.or")}</p> : null} */}
        {/* {idModule ? (
          <div>
            <Button
              size="small"
              type="primary"
              onClick={() => {
                OpenDrawer();
              }}
            >
              {t("form.create")} {families[idModule]?.label}
            </Button>
          </div>
        ) : null} */}
      </div>
      <Divider className="my-2" />
    </div>
  );

  const AffectationContents = (
    <div className="flex items-center justify-center">
      <AffectationContent
        elementId={id}
        source={"email"}
        setData={setDataSource}
        t={t}
        access={access}
        user={user}
        setOpenPopCon={setOpenPopCon}
        type={type}
        setDetailsMail={setDetailsMail}
        setIdModule={setIdModule}
        affectation={affectation}
      />
    </div>
  );

  const handleDelete = async () => {
    var formData = new FormData();

    formData.append("id", affectation?._id || affectation?.id);
    formData.append("type", "email");
    for (let i = 0; i < usedAccount?.departmentId?.length; i++) {
      formData.append("departement_id[]", usedAccount?.departmentId[i]);
    }
    formData.append("account_id", usedAccount?.value);
    try {
      setLoadingDelete(true);
      const { status } = await MainService.deleteAffectation(formData);
      if (status === 200) {
        if (type === "inbox") {
          setDataSource((prev) =>
            prev.map((item) => {
              return item.id === id ? { ...item, affectation: null } : item;
            })
          );
        } else {
          setDetailsMail((p) => {
            let detail = Object.assign({}, p);
            detail.data[detail?.data?.length - 1].affectation = [];
            return detail;
          });
        }
        toastNotification(
          "success",
          t("contacts.successDelete"),
          "topRight",
          3
        );
        dispatch(setRefreshMailInbox(true));
      }
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? err.message : err);
    } finally {
      setLoadingDelete(false);
    }
  };
  //
  const shouldDisableAffectation =
    (access?.companies !== "1" &&
      access.contact !== "1" &&
      access.leads !== "1" &&
      access.deals !== "1" &&
      access.ticket !== "1" &&
      access.projects !== "1" &&
      access.products !== "1" &&
      access?.booking !== "1") ||
    (transfert?.account_id && transfert?.account_id != usedAccount?.value) ||
    (owner?.owner && owner?.owner != user.id);

  const handleOpenChange = (open) => {
    if (!shouldDisableAffectation) {
      setOpenPopCon(open);
    }
  };

  useEffect(() => {
    if (openAction) {
      setOpenPopCon(openAction);
    }
  }, [openAction]);

  return affectation?.affect_label ? (
    type === "dropdown" ? (
      t("mailing.affecter")
    ) : (
      <Popover
        content={AffectationContents}
        title={affectationTitle}
        open={openPopCon}
        onOpenChange={handleOpenChange}
        trigger={["click"]}
        placement="bottomLeft"
        arrow={false}
      >
        <Tag style={{ maxWidth: "100%", position: "relative" }}>
          <>
            <div className="flex flex-row items-center space-x-1.5">
              <span>
                <Tooltip title={familiesName[affectation?.affected_family_id]}>
                  {families[affectation?.affected_family_id]?.icon}
                </Tooltip>
              </span>
              <Tooltip title={`Affecté à ${affectation?.affect_label}`}>
                <p className="truncate text-xs font-semibold">
                  {`${affectation?.affect_label}`}
                </p>
              </Tooltip>
              {loadingDelete ? (
                <Button type="link" size="small" icon={<LoadingOutlined />} />
              ) : !(
                  transfert?.account_id &&
                  transfert?.account_id != usedAccount?.value
                ) && !(owner?.owner && owner?.owner != user.id) ? (
                <Tooltip title={t("voip.delete")}>
                  <Button
                    type="text"
                    size="small"
                    icon={<CloseOutlined />}
                    onClick={handleDelete}
                  />
                </Tooltip>
              ) : null}
            </div>
          </>
        </Tag>
      </Popover>
    )
  ) : (transfert?.account_id && transfert?.account_id != usedAccount?.value) ||
    (owner?.owner && owner?.owner != user.id) ? (
    <Tooltip
      title={
        transfert?.account_id && transfert?.account_id != usedAccount?.value
          ? t("mailing.Tooltip.TransferedNoAffect")
          : owner?.owner && owner?.owner != user.id
          ? t("mailing.Tooltip.AssignNoAffect")
          : transfert?.account_id && owner?.owner
          ? t("mailing.Tooltip.TransferedAndAssigned")
          : t("mailing.Tooltip.Affecter")
      }
    >
      {type === "dropdown" ? (
        t("mailing.affecter")
      ) : (
        <Button size="small" type="dashed" disabled={true}>
          {t("mailing.affecter")}
        </Button>
      )}
    </Tooltip>
  ) : (
    <>
      <Popover
        content={AffectationContents}
        title={affectationTitle}
        open={openPopCon}
        onOpenChange={(open) => setOpenPopCon(open)}
        trigger={["click"]}
        placement="bottomLeft"
        arrow={false}
      >
        <Tooltip
          title={
            transfert?.account_id && transfert?.account_id != usedAccount?.value
              ? t("mailing.Tooltip.TransferedNoAffect")
              : owner?.owner && owner?.owner != user.id
              ? t("mailing.Tooltip.AssignNoAffect")
              : transfert?.account_id && owner?.owner
              ? `vous ne pouvez pas affecter l'email car il est transféré et assigné`
              : t("mailing.Tooltip.Affecter")
          }
        >
          {type === "dropdown" ? (
            t("mailing.affecter")
          ) : (
            <Button
              size="small"
              type="dashed"
              disabled={shouldDisableAffectation}
            >
              {t("mailing.affecter")}
            </Button>
          )}
        </Tooltip>
      </Popover>

      {/* <FormCreate
        open={openForm}
        setOpen={setOpenForm}
        familyId={familyId}
        mailingProps={mailingProps}
      /> */}
    </>
  );
};

export default Affectation;
