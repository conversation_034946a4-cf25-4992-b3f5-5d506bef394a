import { memo, useMemo } from "react";
import { Card, Skeleton } from "antd";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import { PieChartOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";

const KpiLivePanel = memo(
  ({
    dataSource,
    usersStatus,
    loading,
    availabilityFilter,
    setAvailabilityFilter,
    statusComFilter,
    setStatusComFilter,
    selectedStats,
    setSelectedStats,
  }) => {
    //
    const [t] = useTranslation("common");
    // console.log({ selectedStats });
    //
    const { callStatusStats, dailyTotals, userStatusStats } = useMemo(
      () => calculateAllStats(dataSource, usersStatus),
      [dataSource, usersStatus]
    );
    //
    const handleAvailabilityFilter = (key) => {
      setAvailabilityFilter((prev = []) => {
        prev = prev || [];
        if (prev.includes(key)) {
          return prev.filter((e) => e !== key);
        }
        return [...prev, key];
      });
    };

    const handleStatusComFilter = (key) => {
      setStatusComFilter((prev = []) => {
        prev = prev || [];
        if (prev.includes(key)) {
          return prev.filter((e) => e !== key);
        }
        return [...prev, key];
      });
    };
    //
    const gridStyle = {
      width: "50%",
      // textAlign: "center",
      padding: "10px 15px",
      height: 68,
    };
    // console.log({ callStatusStats, dailyTotals, userStatusStats });
    return (
      <div
        className="relative w-full items-center px-1.5 py-2"
        style={{ backgroundColor: "rgb(240, 242, 245)" }}
      >
        <div className="livePanel-dashboard  flex space-x-2.5">
          <div className="w-[29%]">
            <div className="divide-y divide-gray-200 overflow-hidden rounded-lg bg-white shadow">
              <div className="px-5 py-1.5 text-sm  font-semibold ">
                {t("livePanel.availability").toUpperCase()}
              </div>
              <div className="flex">
                <PieChartUserStatus
                  loading={loading}
                  userStatusStats={userStatusStats}
                  t={t}
                />
                <Card
                  title={false}
                  variant="borderless"
                  style={{ width: "100%", height: 136 }}
                >
                  <Card.Grid
                    hoverable
                    style={{
                      ...gridStyle,
                      cursor: "pointer",
                      backgroundColor: availabilityFilter?.includes("online")
                        ? "#f0fdf4"
                        : "transparent",
                      border: availabilityFilter?.includes("online")
                        ? "1px solid #16a34a"
                        : "",
                    }}
                    onClick={() => handleAvailabilityFilter("online")}
                  >
                    <div className="flex h-full flex-col justify-between space-y-1">
                      {/* <span className="text-xs text-slate-500">En ligne</span> */}
                      <div className="flex items-center space-x-1">
                        <svg
                          viewBox="0 0 6 6"
                          aria-hidden="true"
                          className="size-2 fill-[#16a34a]"
                        >
                          <circle r={3} cx={3} cy={3} />
                        </svg>
                        <span className="text-xs text-slate-500">
                          {t("livePanel.online")}
                        </span>
                      </div>
                      {loading && !dataSource.length ? (
                        <Skeleton.Button active />
                      ) : (
                        <span className=" text-2xl font-semibold	text-[##16a34a]">
                          {userStatusStats.online}
                        </span>
                      )}
                    </div>
                  </Card.Grid>
                  <Card.Grid
                    hoverable
                    style={{
                      ...gridStyle,
                      cursor: "pointer",
                      backgroundColor: availabilityFilter?.includes("offline")
                        ? "#f9fafb"
                        : "transparent",
                      border: availabilityFilter?.includes("offline")
                        ? "1px solid #9ca3af"
                        : "",
                    }}
                    onClick={() => handleAvailabilityFilter("offline")}
                  >
                    <div className="flex flex-col space-y-1">
                      {/* <span className="text-xs text-slate-500">Hors ligne</span> */}
                      <div className="flex items-center space-x-1">
                        <svg
                          viewBox="0 0 6 6"
                          aria-hidden="true"
                          className="size-2 fill-[#9ca3af]"
                        >
                          <circle r={3} cx={3} cy={3} />
                        </svg>
                        <span className="text-xs text-slate-500">
                          {t("livePanel.offline")}
                        </span>
                      </div>
                      {loading && !dataSource.length ? (
                        <Skeleton.Button active />
                      ) : (
                        <span className=" text-2xl font-semibold	text-[##9ca3af]">
                          {userStatusStats.offline}
                        </span>
                      )}
                    </div>
                  </Card.Grid>
                  <Card.Grid
                    hoverable
                    style={{
                      ...gridStyle,
                      cursor: "pointer",
                      backgroundColor: availabilityFilter?.includes("busy")
                        ? "#fef2f2"
                        : "transparent",
                      border: availabilityFilter?.includes("busy")
                        ? "1px solid #ef4444"
                        : "",
                    }}
                    onClick={() => handleAvailabilityFilter("busy")}
                  >
                    <div className="flex flex-col space-y-1">
                      {/* <span className="text-xs text-slate-500">Occupé(e)</span> */}
                      <div className="flex items-center space-x-1">
                        <svg
                          viewBox="0 0 6 6"
                          aria-hidden="true"
                          className="size-2 fill-[#ef4444]"
                        >
                          <circle r={3} cx={3} cy={3} />
                        </svg>
                        <span className="text-xs text-slate-500">
                          {t("livePanel.busy")}
                        </span>
                      </div>
                      {loading && !dataSource.length ? (
                        <Skeleton.Button active />
                      ) : (
                        <span className=" text-2xl font-semibold	text-[##ef4444]">
                          {userStatusStats.busy}
                        </span>
                      )}
                    </div>
                  </Card.Grid>
                  <Card.Grid
                    hoverable
                    style={{
                      ...gridStyle,
                      cursor: "pointer",
                      backgroundColor: availabilityFilter?.includes("away")
                        ? "#fffbeb"
                        : "transparent",
                      border: availabilityFilter?.includes("away")
                        ? "1px solid #f59e0b"
                        : "",
                    }}
                    onClick={() => handleAvailabilityFilter("away")}
                  >
                    <div className="flex flex-col space-y-1">
                      {/* <span className="text-xs text-slate-500">Absent(e)</span> */}
                      <div className="flex items-center space-x-1">
                        <svg
                          viewBox="0 0 6 6"
                          aria-hidden="true"
                          className="size-2 fill-[#faad14]"
                        >
                          <circle r={3} cx={3} cy={3} />
                        </svg>
                        <span className="text-xs text-slate-500">
                          {t("livePanel.away")}
                        </span>
                      </div>
                      {loading && !dataSource.length ? (
                        <Skeleton.Button active />
                      ) : (
                        <span className=" text-2xl font-semibold	text-[##faad14]">
                          {userStatusStats.away}
                        </span>
                      )}
                    </div>
                  </Card.Grid>
                </Card>
              </div>
            </div>
          </div>

          <div className="w-[29%]">
            <div className="divide-y divide-gray-200 overflow-hidden rounded-lg bg-white shadow">
              <div className="px-5 py-1.5 text-sm  font-semibold ">
                {t("livePanel.statusCalls").toUpperCase()}
              </div>
              <div className="flex">
                <PieChartStatusCom
                  loading={loading}
                  callStatusStats={callStatusStats}
                  t={t}
                />
                <Card
                  title={false}
                  variant="borderless"
                  style={{ width: "100%", height: 136 }}
                >
                  <Card.Grid
                    hoverable
                    style={{
                      ...gridStyle,
                      cursor: "pointer",
                      backgroundColor: statusComFilter?.includes("free")
                        ? "#f3f4f6"
                        : "transparent",
                      border: statusComFilter?.includes("free")
                        ? "1px solid #9ca3af"
                        : "",
                    }}
                    onClick={() => handleStatusComFilter("free")}
                  >
                    <div className="flex h-full flex-col justify-between space-y-1">
                      {/* <span className="text-xs text-slate-500">Libre</span> */}
                      <div className="flex items-center space-x-1">
                        <svg
                          viewBox="0 0 6 6"
                          aria-hidden="true"
                          className="size-2 fill-[#9ca3af]"
                        >
                          <circle r={3} cx={3} cy={3} />
                        </svg>
                        <span className="text-xs text-slate-500">
                          {t("livePanel.free")}
                        </span>
                      </div>
                      {loading && !dataSource.length ? (
                        <Skeleton.Button active />
                      ) : (
                        <span className=" text-2xl font-semibold	text-[##9ca3af]">
                          {callStatusStats.free}
                        </span>
                      )}
                    </div>
                  </Card.Grid>
                  <Card.Grid
                    hoverable
                    style={{
                      ...gridStyle,
                      cursor: "pointer",
                      backgroundColor: statusComFilter?.includes("ringing")
                        ? "#eff6ff"
                        : "transparent",
                      border: statusComFilter?.includes("ringing")
                        ? "1px solid #2563eb"
                        : "",
                    }}
                    onClick={() => handleStatusComFilter("ringing")}
                  >
                    <div className="flex flex-col space-y-1">
                      {/* <span className="text-xs text-slate-500">Sonnerie</span> */}
                      <div className="flex items-center space-x-1">
                        <svg
                          viewBox="0 0 6 6"
                          aria-hidden="true"
                          className="size-2 fill-[#2563eb]"
                        >
                          <circle r={3} cx={3} cy={3} />
                        </svg>
                        <span className="text-xs text-slate-500">
                          {t("livePanel.ringing")}
                        </span>
                      </div>
                      {loading && !dataSource.length ? (
                        <Skeleton.Button active />
                      ) : (
                        <span className=" text-2xl font-semibold	text-[##2563eb]">
                          {callStatusStats.ringing}
                        </span>
                      )}
                    </div>
                  </Card.Grid>
                  <Card.Grid
                    hoverable
                    style={{
                      ...gridStyle,
                      cursor: "pointer",
                      backgroundColor: statusComFilter?.includes("inCall")
                        ? "#f0fdf4"
                        : "transparent",
                      border: statusComFilter?.includes("inCall")
                        ? "1px solid #16a34a"
                        : "",
                    }}
                    onClick={() => handleStatusComFilter("inCall")}
                  >
                    <div className="flex flex-col space-y-1">
                      {/* <span className="text-xs text-slate-500">En appel</span> */}
                      <div className="flex items-center space-x-1">
                        <svg
                          viewBox="0 0 6 6"
                          aria-hidden="true"
                          className="size-2 fill-[#16a34a]"
                        >
                          <circle r={3} cx={3} cy={3} />
                        </svg>
                        <span className="text-xs text-slate-500">
                          {t("livePanel.onCall")}
                        </span>
                      </div>
                      {loading && !dataSource.length ? (
                        <Skeleton.Button active />
                      ) : (
                        <span className=" text-2xl font-semibold	text-[##16a34a]">
                          {callStatusStats.inCall}
                        </span>
                      )}
                    </div>
                  </Card.Grid>
                  <Card.Grid
                    hoverable
                    style={{
                      ...gridStyle,
                      cursor: "pointer",
                      backgroundColor: statusComFilter?.includes("onHold")
                        ? "#fffbeb"
                        : "transparent",
                      border: statusComFilter?.includes("onHold")
                        ? "1px solid #f59e0b"
                        : "",
                    }}
                    onClick={() => handleStatusComFilter("onHold")}
                  >
                    <div className="flex flex-col space-y-1">
                      {/* <span className="text-xs text-slate-500">En attente</span> */}
                      <div className="flex items-center space-x-1">
                        <svg
                          viewBox="0 0 6 6"
                          aria-hidden="true"
                          className="size-2 fill-[#faad14]"
                        >
                          <circle r={3} cx={3} cy={3} />
                        </svg>
                        <span className="text-xs text-slate-500">
                          {t("livePanel.onPause")}
                        </span>
                      </div>
                      {loading && !dataSource.length ? (
                        <Skeleton.Button active />
                      ) : (
                        <span className=" text-2xl font-semibold	text-[##faad14]">
                          {callStatusStats.onHold}
                        </span>
                      )}
                    </div>
                  </Card.Grid>
                </Card>
              </div>
            </div>
          </div>

          <div className="w-[42%]">
            <div className="divide-y divide-gray-200 overflow-hidden rounded-lg bg-white shadow">
              <div className="px-5 py-1.5 text-sm  font-semibold ">
                {t("livePanel.todayNbrCalls").toUpperCase()}
              </div>
              <div className="flex">
                <PieChartDailyStats
                  loading={loading}
                  dailyTotals={dailyTotals}
                  selectedStats={selectedStats}
                  t={t}
                />
                <Card
                  title={false}
                  variant="borderless"
                  style={{ width: "100%", height: 136 }}
                >
                  <Card.Grid
                    hoverable={false}
                    style={{
                      ...gridStyle,
                      width: "33.33%",
                      padding: "10px 12px",
                    }}
                  >
                    <div className="flex h-full flex-col justify-between space-y-1">
                      <span className="text-xs text-slate-500">Total</span>
                      {loading && !dataSource.length ? (
                        <Skeleton.Button active />
                      ) : (
                        <span className=" text-2xl font-semibold	">
                          {dailyTotals.inBound + dailyTotals.outBound}
                        </span>
                      )}
                    </div>
                  </Card.Grid>
                  <Card.Grid
                    hoverable
                    style={{
                      ...gridStyle,
                      width: "33.33%",
                      padding: "10px 12px",
                      cursor: "pointer",
                      backgroundColor:
                        selectedStats === "inBound" ? "#f3f4f6" : "transparent",
                      border:
                        selectedStats === "inBound" ? "1px solid #9ca3af" : "",
                    }}
                    onClick={() => setSelectedStats("inBound")}
                  >
                    <div className="flex flex-col space-y-1">
                      <span className="text-xs text-slate-500">
                        {t("livePanel.inbound")}
                      </span>
                      {loading && !dataSource.length ? (
                        <Skeleton.Button active />
                      ) : (
                        <div className="flex items-end">
                          <span className=" text-2xl font-semibold	">
                            {dailyTotals.inBound}
                          </span>
                          <span className="mb-1 ml-1 text-xs font-medium text-slate-500">
                            {Math.round(
                              (dailyTotals.inBound /
                                (dailyTotals.inBound + dailyTotals.outBound)) *
                                100
                            ) || 0}
                            %
                          </span>
                        </div>
                      )}
                    </div>
                  </Card.Grid>
                  <Card.Grid
                    hoverable
                    style={{
                      ...gridStyle,
                      width: "33.33%",
                      padding: "10px 12px",
                      cursor: "pointer",
                      backgroundColor:
                        selectedStats === "outBound"
                          ? "#f3f4f6"
                          : "transparent",
                      border:
                        selectedStats === "outBound" ? "1px solid #9ca3af" : "",
                    }}
                    onClick={() => setSelectedStats("outBound")}
                  >
                    <div className="flex flex-col space-y-1">
                      <span className="text-xs text-slate-500">
                        {t("livePanel.outbound")}
                      </span>
                      {loading && !dataSource.length ? (
                        <Skeleton.Button active />
                      ) : (
                        <div className="flex items-end">
                          <span className=" text-2xl font-semibold	">
                            {dailyTotals.outBound}
                          </span>
                          <span className="mb-1 ml-1 text-xs font-medium text-slate-500">
                            {Math.round(
                              (dailyTotals.outBound /
                                (dailyTotals.inBound + dailyTotals.outBound)) *
                                100
                            ) || 0}
                            %
                          </span>
                        </div>
                      )}
                    </div>
                  </Card.Grid>
                  <Card.Grid
                    hoverable={false}
                    style={{
                      ...gridStyle,
                      ...(selectedStats === "inBound"
                        ? { width: "33.33%" }
                        : {}),
                      padding: "10px 10px",
                    }}
                  >
                    <div className="flex flex-col space-y-1">
                      <div className="flex items-center space-x-1">
                        <svg
                          viewBox="0 0 6 6"
                          aria-hidden="true"
                          className="size-2 fill-[#16a34a]"
                        >
                          <circle r={3} cx={3} cy={3} />
                        </svg>
                        <span className="text-xs text-slate-500">
                          {t("livePanel.answered")}
                        </span>
                      </div>

                      {/* <span className="text-xs text-slate-500">Répondu</span> */}

                      {loading && !dataSource.length ? (
                        <Skeleton.Button active />
                      ) : (
                        <div className="flex items-end">
                          <span className=" text-2xl font-semibold	text-[##16a34a]">
                            {selectedStats === "inBound"
                              ? dailyTotals.answered
                              : dailyTotals.outAnswered}
                          </span>
                          <span className="mb-1 ml-1 text-[10px] font-medium text-slate-500">
                            {selectedStats === "inBound"
                              ? t("livePanel.inbound")
                              : t("livePanel.outbound")}
                          </span>
                        </div>
                      )}
                    </div>
                  </Card.Grid>
                  <Card.Grid
                    hoverable={false}
                    style={{
                      ...gridStyle,
                      ...(selectedStats === "inBound"
                        ? { width: "33.33%" }
                        : {}),
                      padding: "10px 10px",
                    }}
                  >
                    <div className="flex flex-col space-y-1">
                      <div className="flex items-center space-x-1">
                        <svg
                          viewBox="0 0 6 6"
                          aria-hidden="true"
                          className="size-2 fill-[#ef4444]"
                        >
                          <circle r={3} cx={3} cy={3} />
                        </svg>
                        <span className="text-xs text-slate-500">
                          {t("livePanel.notAnswered")}
                        </span>
                      </div>
                      {/* <span className="text-xs text-slate-500">Non répondu</span> */}

                      {loading && !dataSource.length ? (
                        <Skeleton.Button active />
                      ) : (
                        <div className="flex items-end">
                          <span className=" text-2xl font-semibold text-[##ef4444]	">
                            {selectedStats === "inBound"
                              ? dailyTotals.unAnswered
                              : dailyTotals.outUnAnswered}
                          </span>
                          <span className="mb-1 ml-1 text-[10px] font-medium text-slate-500">
                            {selectedStats === "inBound"
                              ? t("livePanel.inbound")
                              : t("livePanel.outbound")}
                          </span>
                        </div>
                      )}
                    </div>
                  </Card.Grid>
                  {selectedStats === "inBound" && (
                    <Card.Grid
                      hoverable={false}
                      style={{
                        ...gridStyle,
                        width: "33.33%",
                        padding: "10px 10px",
                      }}
                    >
                      <div className="flex flex-col space-y-1">
                        <div className="flex items-center space-x-1">
                          <svg
                            viewBox="0 0 6 6"
                            aria-hidden="true"
                            className="size-2 fill-[#4ADE80]"
                          >
                            <circle r={3} cx={3} cy={3} />
                          </svg>
                          <span className="text-xs text-slate-500">
                            {t("livePanel.recalled")}
                          </span>
                        </div>
                        {/* <span className="text-xs text-slate-500">Non répondu</span> */}

                        {loading && !dataSource.length ? (
                          <Skeleton.Button active />
                        ) : (
                          <div className="flex items-end">
                            <span className=" text-2xl font-semibold text-[##ef4444]	">
                              {dailyTotals.returned}
                            </span>
                            <span className="mb-1 ml-1 text-[10px] font-medium text-slate-500">
                              {t("livePanel.notAnswered")}
                            </span>
                          </div>
                        )}
                      </div>
                    </Card.Grid>
                  )}
                </Card>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
);
//
const calculateAllStats = (usersData, usersStatus) => {
  // Initialize stats objects
  const dailyTotals = {
    inBound: 0,
    answered: 0,
    unAnswered: 0,
    returned: 0,
    outBound: 0,
    outAnswered: 0,
    outUnAnswered: 0,
  };
  const callStatusStats = {
    total: 0,
    inCall: 0,
    ringing: 0,
    onHold: 0,
    free: 0,
  };
  const userStatusStats = {
    total: usersData.length,
    online: 0,
    busy: 0,
    away: 0,
    offline: 0,
  };

  usersData.forEach((user) => {
    //
    const ds = user.daily_stat || {};
    dailyTotals.inBound += ds.inBound || 0;
    dailyTotals.answered += ds.answered || 0;
    dailyTotals.unAnswered += ds.unAnswered || 0;
    dailyTotals.returned += ds.returned || 0;
    dailyTotals.outBound += ds.outBound || 0;
    dailyTotals.outAnswered += ds.outAnswered || 0;
    dailyTotals.outUnAnswered += ds.outUnAnswered || 0;

    const uid = user.user_info?.uid;
    const webStatus = user.webphone?.status;

    if (webStatus === "inCall") {
      callStatusStats.inCall += 1;
    } else if (webStatus === "ringing") {
      callStatusStats.ringing += 1;
    } else if (webStatus === "onHold") {
      callStatusStats.onHold += 1;
    }
    //  else {
    //   const userStatus = usersStatus[uid] || "offline";
    //   if (webStatus === "free" || userStatus !== "offline") {
    //     callStatusStats.free += 1;
    //   }
    // }

    const status = usersStatus[uid] || "offline";
    userStatusStats[status] = (userStatusStats[status] || 0) + 1;
  });

  callStatusStats.total = usersData.length - userStatusStats.offline;
  callStatusStats.free = callStatusStats.total
    ? callStatusStats.total -
      callStatusStats.inCall -
      callStatusStats.ringing -
      callStatusStats.onHold
    : 0;

  return {
    dailyTotals,
    callStatusStats,
    userStatusStats,
  };
};
//
export const PieChartUserStatus = ({ userStatusStats, loading, t }) => {
  const colorMapping = {
    [t("livePanel.online")]: "#16a34a",
    // "En appel": "rgb(183, 235, 143)",
    [t("livePanel.busy")]: "#ef4444",
    [t("livePanel.away")]: "#faad14",
    [t("livePanel.offline")]: "#9ca3af",
  };

  const pieData = [
    { name: t("livePanel.online"), y: userStatusStats.online },
    // { name: "En appel", y: callStatusStats.inCall },
    { name: t("livePanel.busy"), y: userStatusStats.busy },
    { name: t("livePanel.away"), y: userStatusStats.away },
    { name: t("livePanel.offline"), y: userStatusStats.offline },
  ].map((item) => ({
    ...item,
    color: colorMapping[item.name], // Assign the color
  }));

  const options = {
    chart: {
      type: "pie",
      // width: 180,
      height: 160,
      // Force less right margin if needed
      marginRight: 0,
      custom: {}, // ensure custom property exists
      events: {
        render() {
          const chart = this,
            series = chart.series[0];
          const newText =
            "<span style='font-size:0.7rem; color:#666'>Total</span><br/>" +
            `<span style="font-size:1rem; font-weight:bold;">${userStatusStats.total}</span>`;

          // If label doesn't exist, create it; otherwise update its text.
          if (!chart.options.chart.custom.label) {
            chart.options.chart.custom.label = chart.renderer
              .label(newText)
              .css({
                textAnchor: "middle",
              })
              .add();
          } else {
            chart.options.chart.custom.label.attr({ text: newText });
          }

          const customLabel = chart.options.chart.custom.label;
          const x = series.center[0] + chart.plotLeft,
            y =
              series.center[1] +
              chart.plotTop -
              customLabel.getBBox().height / 2;
          customLabel.attr({ x, y });

          // Optionally adjust font size based on chart diameter
          customLabel.css({
            fontSize: `${series.center[2] / 12}px`,
          });
        },
      },
    },
    legend: false,
    exporting: {
      enabled: false,
    },
    title: { text: null },
    subtitle: { text: null },
    plotOptions: {
      pie: {
        center: ["45%", "40%"], // Move the pie center closer to the right
        size: "100%",
        dataLabels: {
          enabled: true,
          // You can adjust distance to move the labels inside/outside the donut
          distance: -23,
          // Format to show percentage with 0 decimals (e.g., 25%)
          formatter: function () {
            // 'this.percentage' is the slice's percentage
            if (this.percentage === 0) {
              return null;
            }
            // Show percentage with no decimal places
            return `${this.percentage.toFixed(0)}%`;
          },
          style: {
            fontSize: "0.65em",
          },
        },
        showInLegend: true,
      },
    },
    tooltip: {
      headerFormat: "",
      pointFormat:
        '<span /*style="color:{point.color}"*/>{point.name}</span>: <b>{point.y}</b>',
      style: {
        fontSize: "0.8em",
        zIndex: 999,
      },
    },
    series: [
      {
        name: "",
        innerSize: "75%",
        colorByPoint: true,
        data: pieData,
      },
    ],
  };

  return (
    <Card variant="borderless" style={{ borderRight: 0 }}>
      <Card.Grid
        hoverable={false}
        style={{
          width: 150,
          height: 135,
          padding: "1px 0px",
          // textAlign: "center",
        }}
      >
        <div>
          {!userStatusStats.total && loading ? (
            <Skeleton.Node active style={{ width: 150, height: 135 }}>
              <PieChartOutlined style={{ fontSize: 90, color: "#bfbfbf" }} />
            </Skeleton.Node>
          ) : (
            <HighchartsReact highcharts={Highcharts} options={options} />
          )}
        </div>
      </Card.Grid>
    </Card>
  );
};
//
export const PieChartStatusCom = ({ callStatusStats, loading, t }) => {
  const colorMapping = {
    [t("livePanel.onCall")]: "#16a34a",
    // "En appel": "rgb(183, 235, 143)",
    [t("livePanel.ringing")]: "#2563eb",
    [t("livePanel.onPause")]: "#faad14",
    [t("livePanel.free")]: "#9ca3af",
  };

  const pieData = [
    { name: t("livePanel.onCall"), y: callStatusStats.inCall },
    // { name: "En appel", y: callStatusStats.inCall },
    { name: t("livePanel.ringing"), y: callStatusStats.ringing },
    { name: t("livePanel.onPause"), y: callStatusStats.onHold },
    { name: t("livePanel.free"), y: callStatusStats.free },
  ].map((item) => ({
    ...item,
    color: colorMapping[item.name], // Assign the color
  }));

  const options = {
    chart: {
      type: "pie",
      // width: 180,
      height: 160,
      // Force less right margin if needed
      marginRight: 0,
      custom: {}, // ensure custom property exists
      events: {
        render() {
          const chart = this,
            series = chart.series[0];
          const newText =
            "<span style='font-size:0.7rem; color:#666'>Total</span><br/>" +
            `<span style="font-size:1rem; font-weight:bold;">${callStatusStats.total}</span>`;

          // If label doesn't exist, create it; otherwise update its text.
          if (!chart.options.chart.custom.label) {
            chart.options.chart.custom.label = chart.renderer
              .label(newText)
              .css({
                textAnchor: "middle",
              })
              .add();
          } else {
            chart.options.chart.custom.label.attr({ text: newText });
          }

          const customLabel = chart.options.chart.custom.label;
          const x = series.center[0] + chart.plotLeft,
            y =
              series.center[1] +
              chart.plotTop -
              customLabel.getBBox().height / 2;
          customLabel.attr({ x, y });

          // Optionally adjust font size based on chart diameter
          customLabel.css({
            fontSize: `${series.center[2] / 12}px`,
          });
        },
      },
    },
    legend: false,
    exporting: {
      enabled: false,
    },
    title: { text: null },
    subtitle: { text: null },
    plotOptions: {
      pie: {
        center: ["45%", "40%"], // Move the pie center closer to the right
        size: "100%",
        dataLabels: {
          enabled: true,
          // You can adjust distance to move the labels inside/outside the donut
          distance: -23,
          // Format to show percentage with 0 decimals (e.g., 25%)
          formatter: function () {
            // 'this.percentage' is the slice's percentage
            if (this.percentage === 0) {
              return null;
            }
            // Show percentage with no decimal places
            return `${this.percentage.toFixed(0)}%`;
          },
          style: {
            fontSize: "0.65em",
          },
        },
        showInLegend: true,
      },
    },
    tooltip: {
      headerFormat: "",
      pointFormat:
        '<span /*style="color:{point.color}"*/>{point.name}</span>: <b>{point.y}</b>',
      style: {
        fontSize: "0.8em",
        zIndex: 999,
      },
    },
    series: [
      {
        name: "",
        innerSize: "75%",
        colorByPoint: true,
        data: pieData,
      },
    ],
  };

  return (
    <Card variant="borderless" style={{ borderRight: 0 }}>
      <Card.Grid
        hoverable={false}
        style={{
          width: 150,
          height: 135,
          padding: "1px 0px",
          // textAlign: "center",
        }}
      >
        <div>
          {!callStatusStats.free && loading ? (
            <Skeleton.Node active style={{ width: 150, height: 135 }}>
              <PieChartOutlined style={{ fontSize: 90, color: "#bfbfbf" }} />
            </Skeleton.Node>
          ) : (
            <HighchartsReact highcharts={Highcharts} options={options} />
          )}
        </div>
      </Card.Grid>
    </Card>
  );
};
//
export const PieChartDailyStats = ({
  dailyTotals,
  loading,
  selectedStats,
  t,
}) => {
  //
  const colorMapping = {
    // Entrants: "rgb(183, 235, 143)",
    // Sortants: "#2563eb",
    [t("livePanel.answered")]: "#16a34a",
    // "Rappelés": "#4ADE80.",
    [t("livePanel.notAnswered")]: "#ef4444",
  };

  // 2. Build your data array, injecting the color property
  const pieData = [
    // { name: "Entrants", y: dailyTotals.inBound },
    // { name: "Sortants", y: dailyTotals.outBound },
    {
      name: t("livePanel.answered"),
      y:
        selectedStats === "inBound"
          ? dailyTotals.answered
          : dailyTotals.outAnswered,
    },
    // { name: "Rappelés", y: dailyTotals.returned },
    {
      name: t("livePanel.notAnswered"),
      y:
        selectedStats === "inBound"
          ? dailyTotals.unAnswered
          : dailyTotals.outUnAnswered,
    },
  ].map((item) => ({
    ...item,
    color: colorMapping[item.name], // Assign the color
  }));

  const options = {
    chart: {
      type: "pie",
      // width: 220,
      height: 160,
      // Force less right margin if needed
      marginRight: 0,
      custom: {}, // ensure custom property exists
      events: {
        render() {
          const chart = this,
            series = chart.series[0];
          const newText =
            `<span style='font-size:0.7rem; color:#666'>${
              selectedStats === "inBound" ? "Entrants" : "Sortants"
            }</span><br/>` +
            `<span style="font-size:1rem; font-weight:bold;">${
              selectedStats === "inBound"
                ? dailyTotals.inBound
                : dailyTotals.outBound
            }</span>`;

          // If label doesn't exist, create it; otherwise update its text.
          if (!chart.options.chart.custom.label) {
            chart.options.chart.custom.label = chart.renderer
              .label(newText)
              .css({
                textAnchor: "middle",
              })
              .add();
          } else {
            chart.options.chart.custom.label.attr({ text: newText });
          }

          const customLabel = chart.options.chart.custom.label;
          const x = series.center[0] + chart.plotLeft,
            y =
              series.center[1] +
              chart.plotTop -
              customLabel.getBBox().height / 2;
          customLabel.attr({ x, y });

          // Optionally adjust font size based on chart diameter
          customLabel.css({
            fontSize: `${series.center[2] / 12}px`,
          });
        },
      },
    },
    legend: false,
    exporting: {
      enabled: false,
    },
    title: { text: null },
    subtitle: { text: null },
    plotOptions: {
      pie: {
        center: ["45%", "40%"], // Move the pie center closer to the right
        size: "100%",
        dataLabels: {
          enabled: true,
          // You can adjust distance to move the labels inside/outside the donut
          distance: -23,
          // Format to show percentage with 0 decimals (e.g., 25%)
          formatter: function () {
            // 'this.percentage' is the slice's percentage
            if (this.percentage === 0) {
              return null;
            }
            // Show percentage with no decimal places
            return `${this.percentage.toFixed(0)}%`;
          },
          style: {
            fontSize: "0.65em",
          },
        },
        showInLegend: true,
      },
    },
    tooltip: {
      headerFormat: "",
      pointFormat:
        '<span /*style="color:{point.color}"*/>{point.name}</span>: <b>{point.y}</b>',
      style: {
        fontSize: "0.8em",
        zIndex: 999,
      },
    },
    series: [
      {
        name: "",
        innerSize: "75%",
        colorByPoint: true,
        data: pieData,
      },
    ],
  };

  return (
    <Card variant="borderless" style={{ borderRight: 0 }}>
      <Card.Grid
        hoverable={false}
        style={{
          width: 150,
          height: 135,
          padding: "1px 0px",
          // textAlign: "center",
        }}
      >
        <div>
          {!dailyTotals.inBound && loading ? (
            <Skeleton.Node active style={{ width: 150, height: 135 }}>
              <PieChartOutlined style={{ fontSize: 90, color: "#bfbfbf" }} />
            </Skeleton.Node>
          ) : (
            <HighchartsReact highcharts={Highcharts} options={options} />
          )}
        </div>
      </Card.Grid>
    </Card>
  );
};
//
export default KpiLivePanel;
