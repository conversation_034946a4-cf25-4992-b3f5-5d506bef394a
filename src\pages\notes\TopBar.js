import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Di<PERSON>r,
  Input,
  Popover,
  Radio,
  Space,
  Tooltip,
  Typography,
} from "antd";
import React, { useEffect, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import {
  addSelfNote,
  removeSelectedNote,
  setSelectedNote,
} from "../../new-redux/actions/selfnotes.actions/selfnotes";
import { handleNewNoteCreation } from "./utils";
//import { SaveOutlined } from "@ant-design/icons";
import { FiSearch } from "react-icons/fi";
import { BsCommand } from "react-icons/bs";
import { useTranslation } from "react-i18next";
import useKeyDown from "../../custom-hooks/useKeyDown";
import {
  FileOutlined,
  FilterOutlined,
  FilterTwoTone,
  FormOutlined,
} from "@ant-design/icons";
import { useSelector } from "react-redux";

function TopBar({
  selectedNote,
  setClickedNote,
  searchKeyWord,
  setSearchKeyWord,
  displayBy,
  showDisplayBy,
  updateDisplayBy,
}) {
  const { t } = useTranslation("common");
  const dispatch = useDispatch();

  const notesRedux = useSelector((state) => state?.selfNotesReducer?.slefNotes);

  // console.log("selfNotes", notesRedux);

  const inputRef = useRef(null);

  const [displayByHover, setDisplayByHover] = useState(false);

  const [search, setSearch] = useState("");
  useKeyDown(75, true, "keydown", () => {
    inputRef.current?.focus();

    const length = inputRef.current?.value?.length;
    inputRef.current?.setSelectionRange(length, length);
  });

  const handleSearch = (value) => {
    // setSearch(value?.trimStart());
    setSearchKeyWord(value?.trimStart());
  };

  const [openPopOver, setOpenPopover] = useState(false);
  const handleOpenChange = (newOpen) => {
    setOpenPopover(newOpen);
  };

  const content = (
    <div className="flex flex-col space-y-2">
      {/* <Text type="secondary">{t("chat.sortBy")}</Text> */}
      <Radio.Group
        // onChange={onChangeSortBy}
        // value={
        //   !isNaN(currentUser?.config?.sort_message)
        //     ? currentUser?.config?.sort_message
        //     : 0
        // }
        onChange={(e) => updateDisplayBy(e.target.value)}
        defaultValue={0}
      >
        <Space direction="vertical">
          <Radio value={0}>{t("selfNotes.allNotes")}</Radio>
          <Radio value={1}>{t("selfNotes.myNotes")}</Radio>
          <Radio value={2}>{t("selfNotes.sharedNotes")}</Radio>
          <Radio value={3}>{t("selfNotes.personalNotes")}</Radio>
          <Radio value={4}>{t("selfNotes.sharedWithMe")}</Radio>
        </Space>
      </Radio.Group>
      {/* <Checkbox
        onChange={onChangeMessagePreview}
        checked={currentUser?.config?.hidden_message === 0}
      >
        {t("chat.messagePreview")}
      </Checkbox> */}
    </div>
  );

  useEffect(() => {
    // selfNotes.map((note) => {
    //   // console.log("note is new", note.isNew);
    //   console.log("note", note);
    // });
    // console.log("selfNotes", notesRedux);
  }, [notesRedux]);

  return (
    <div className="flex w-full flex-col gap-y-2 px-3 pb-2  ">
      {/* Title */}
      {/* {
        //check if there is a note.isNew
        notesRedux?.find((note) => note.isNew == true) ? (
          <h2>is New</h2>
        ) : (
          <h2>is not New</h2>
        )
      } */}

      {/* <div className="flex items-center justify-between">
        <Typography.Title level={4}></Typography.Title>
      </div> */}
      {/* Input */}
      <div className="flex items-center justify-between">
        {showDisplayBy()}
        <div className="flex items-center justify-end">
          <Popover
            placement="bottom"
            trigger="click"
            open={openPopOver}
            onOpenChange={handleOpenChange}
            content={content}
          >
            {/* <div className="absolute w-full">
              {displayBy != "all" ? (
                //i want to isplay a red dot
                <div className="relative left-5 top-1 h-2 w-2 rounded-full bg-red-500"></div>
              ) : null}
            </div> */}
            <Tooltip
              open={displayByHover}
              onOpenChange={(e) => {
                setDisplayByHover(e);
              }}
              placement="bottom"
              title={t("chat.display")}
            >
              <Button
                className="group text-gray-500"
                type="text"
                shape="circle"
                // size="middle"
                icon={
                  <Badge dot={displayBy != "all"}>
                    <FilterTwoTone />
                  </Badge>
                }
              />
            </Tooltip>
          </Popover>
          <div className="flex">
            <Tooltip title={t("selfNotes.addNote")} placement="bottom">
              <Button
                type="text"
                className="group text-gray-500"
                size="large"
                shape="circle"
                icon={<FormOutlined />}
                disabled={notesRedux?.find((note) => note.isNew == true)}
                onClick={() => {
                  let newNote = handleNewNoteCreation();
                  setSearchKeyWord("");
                  dispatch(removeSelectedNote());
                  dispatch(addSelfNote(newNote));
                  dispatch(setSelectedNote(newNote));
                  setClickedNote(newNote);
                }}
              />
            </Tooltip>
            {/* <Tooltip title="Files" placement="bottom">
            <Button
              type="text"
              className="group text-gray-500"
              size="large"
              shape="circle"
              icon={<FileOutlined />}
            />
          </Tooltip> */}
          </div>
        </div>
      </div>
      <div className=" flex items-center ">
        <Input
          tabIndex={1}
          onBlur={() => {
            document
              .getElementById("search-shortcut")
              ?.classList?.remove("hidden");
          }}
          onFocus={() => {
            document
              .getElementById("search-shortcut")
              ?.classList?.add("hidden");
          }}
          ref={inputRef}
          size="middle"
          className=" w-full"
          autoFocus
          placeholder={t("selfNotes.searchNotes")}
          prefix={<FiSearch className="text-slate-500" />}
          suffix={
            <div className=" group flex items-center  space-x-1 text-xs ">
              <div
                id="search-shortcut"
                className="flex items-center rounded-md bg-gray-100 px-2 text-gray-400 transition duration-300   "
              >
                {navigator.userAgent.indexOf("Macintosh") !== -1 ? (
                  <>
                    <BsCommand /> <kbd>K</kbd>
                  </>
                ) : (
                  "Ctrl + K"
                )}
              </div>
            </div>
          }
          onChange={(e) => handleSearch(e.target.value)}
          value={searchKeyWord}
          allowClear
        />
      </div>
    </div>
  );
}

export default TopBar;
