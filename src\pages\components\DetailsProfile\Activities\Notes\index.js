import React, { useEffect, useState, lazy, Suspense, useRef } from "react";
import {
  <PERSON>ton,
  Card,
  Space,
  Avatar,
  Tooltip,
  Typography,
  Spin,
  Modal,
  Popconfirm,
  message,
  Empty,
  Switch,
  Select,
} from "antd";
import {
  AudioOutlined,
  DeleteOutlined,
  EditOutlined,
  EyeInvisibleOutlined,
  EyeOutlined,
  GlobalOutlined,
  TeamOutlined,
} from "@ant-design/icons";
import RichTextInput from "../../../../../components/tiptap_richtext/RichTextInput";
import MainService from "../../../../../services/main.service";
import { useSelector, useDispatch } from "react-redux";
// import moment from "moment";
import { getName } from "../../../../layouts/chat/utils/ConversationUtils";
import InfiniteScroll from "react-infinite-scroll-component";
import TestInfiniteScroll from "./TestInfiniteScroll";
import NoteAvatar from "./NoteAvatar";
import NoteSearch from "./NoteSearch";
import {
  addNote360,
  addNotes360ToList,
  removeNote360,
  setNotes360,
  updateNote360,
} from "../../../../../new-redux/actions/notes.actions/notes";
// import AudioInput from "../../../../layouts/chat/conversation/input/AudioInput/AudioInput";
import { Audio } from "../../../../../components/Chat";
import moment from "moment-timezone";
import { useTranslation } from "react-i18next";
import { URL_ENV } from "index";
import RecordComponent from "../../../../layouts/chat/conversation/input/AudioInput/AudioInput3";
import { humanDate, noteDate } from "pages/voip/helpers/helpersFunc";
import { AiOutlineGlobal } from "react-icons/ai";
import { HiOutlineUsers } from "react-icons/hi";
import { PiUsers } from "react-icons/pi";
import { setNewInteraction } from "new-redux/actions/vue360.actions/vue360";

function Notes({
  isExternal,
  headerHeight = "",
  from = "",
  setKpi = () => {},
  contactInfo,
}) {
  const dispatch = useDispatch();
  const [t] = useTranslation("common");

  const [content, setContent] = useState("");
  const [onFetch, setOnFetch] = useState(false); // [1
  const [onSaveLoading, setOnSaveLoading] = useState(false); // [1

  const dateConfig = useSelector(
    (state) => state?.user?.user?.location?.date_format
  );

  const dateTimeConfig = useSelector(
    (state) => state?.user?.user?.location?.time_format
  );

  const dateTimeZoneConfig = useSelector(
    (state) => state?.user?.user?.location?.default_timezone
  );
  const { activeTab360 } = useSelector((state) => state?.vue360);
  //to remove after update
  const defaultUsr = useSelector((state) => state?.user?.user);

  const globalDateFormat = (date, time) => {
    return `${date} ${time}`;
  };

  const [switchValue, setSwitchValue] = useState(false);

  const [clearInput, setClearInput] = useState(false); // [1

  const [searchLoading, setSearchLoading] = useState(false); // [1

  const notesRedux = useSelector((state) => state?.notes?.notes);

  const [pageNum, setPageNum] = useState(1);
  const [totalPages, setTotalPages] = useState(null);
  const [searchResultNote, setSearchResultNote] = useState(null); // [1

  const [onUpdateLoading, setOnUpdateLoading] = useState(false); // [1

  const [person, setPerson] = useState({}); // [1
  const [isRecording, setIsRecording] = useState(false);

  const [noteToEditId, setNoteToEditId] = useState(null);
  const [noteToEditContent, setNoteToEditContent] = useState("");
  const [recordedBlobNotes, setRecordedBlobNotes] = useState(null);

  const [lastElement, setLastElement] = useState(null);

  const observer = useRef(
    new IntersectionObserver((entries) => {
      const first = entries[0];
      if (first.isIntersecting) {
        setPageNum((no) => no + 1);
      }
    })
  );
  const textareaRef = useRef(null);

  const [noteToEditVisibility, setNoteToEditVisibility] = useState(null);

  const handleKeyDown = (event) => {
    // Vous pouvez gérer certaines touches ici si nécessaire
    event.stopPropagation();
  };
  const toggleNoteEdit = (note) => {
    if (noteToEditId == note?._id) {
      setNoteToEditId(null);
      setNoteToEditContent("");
      setNoteToEditVisibility(null);
    } else {
      setNoteToEditId(note?._id);
      setNoteToEditContent(note?.content);
      setNoteToEditVisibility(note?.public);
    }
  };

  const [loadingSwitchVis, setLoadingSwitchVis] = useState({
    noteId: null,
    loading: false,
  });

  const modifyNoteVisibility = (note, publicValue) => {
    setLoadingSwitchVis({
      noteId: note._id,
      loading: true,
    });
    let data = {
      public: publicValue ? 1 : 0,
    };
    MainService.modifyNote360Visibility(note._id, data)
      .then((response) => {
        //console.log(response);
        dispatch(updateNote360(response?.data?.data));
      })
      .catch((error) => {
        console.log(error);
      })
      .finally(() => {
        setLoadingSwitchVis({
          noteId: null,
          loading: false,
        });
      });
  };

  useEffect(() => {
    // console.log(content);
  }, [content]);
  const noteCreate = () => {
    setOnSaveLoading(true);

    let formData = new FormData();

    if (content) {
      formData.append("content", content);
    }

    if (recordedBlobNotes) {
      formData.append("audio_file", recordedBlobNotes);
    }

    // let data = {
    //   content: content,
    // };
    let familyId = contactInfo?.family_id;
    let elementId = contactInfo?.id;
    if (!isExternal) {
      formData.append("family_id", familyId);
      formData.append("element_id", elementId);
      if (
        defaultUsr?.role.toLowerCase() === "guest" ||
        defaultUsr?.role.toLowerCase() === "superguest"
      ) {
        formData.append("is_public", 1);
      } else {
        formData.append("is_public", switchValue ? 1 : 0);
      }
    }

    // formData.append("module_note", null);
    MainService.createNote360(formData)
      .then((response) => {
        // console.log(response);

        // console.log(response?.data?.data[0]?.note);

        //at the beginning
        // setNotes((prev) => [response?.data?.data, ...prev]);
        dispatch(
          addNote360({
            ...response?.data?.data[0].note,
            permission: 1,
          })
        );
        setKpi((prev) =>
          prev.map((el) =>
            el.title === "Comments" ? { ...el, value: el.value + 1 } : el
          )
        );
        setContent("<p></p>"); //reset content to "
        setClearInput(true);
        message.success(t("notes360.noteCreatedSuccess"));
        if (activeTab360 == 3) {
          dispatch(setNewInteraction({ type: "createNote" }));
          setTimeout(() => {
            dispatch(setNewInteraction({ type: "" }));
          }, 100);
        }
        setShowAudioInput(false);
        setRecordedBlobNotes(null);
      })
      .catch((error) => {
        console.log(error);
      })
      .finally(() => {
        setOnSaveLoading(false);
        setClearInput(false);
      });
  };

  const noteDelete = (id) => {
    MainService.deleteNote360(id)
      .then((response) => {
        // console.log(response);
        // setNotes(notes.filter((note) => note._id !== id));
        dispatch(removeNote360(id));
        setKpi((prev) =>
          prev.map((el) =>
            el.title === "Comments" ? { ...el, value: el.value - 1 } : el
          )
        );
        message.success(t("notes360.noteDeletedSuccess"));
        if (activeTab360 == 3) {
          dispatch(setNewInteraction({ type: "createNote" }));
          setTimeout(() => {
            dispatch(setNewInteraction({ type: "" }));
          }, 100);
        }
      })
      .catch((error) => {
        console.log(error);
      });
  };

  const updateNote = (id) => {
    setOnUpdateLoading(true);
    let data = {
      content: noteToEditContent,
      permission: 1,
      is_public: noteToEditVisibility ? 1 : 0,
    };
    MainService.updateNote360(id, data)
      .then((response) => {
        //console.log(response);
        // setNotes(
        //   notes.map((note) => {
        //     if (note._id == id) {
        //       return response?.data?.data;
        //     }
        //     return note;
        //   })
        // );
        dispatch(updateNote360(response?.data?.data));
        setNoteToEditId(null);
        setNoteToEditContent("");
        setNoteToEditVisibility(null);
        if (activeTab360 == 3) {
          dispatch(setNewInteraction({ type: "createNote" }));
          setTimeout(() => {
            dispatch(setNewInteraction({ type: "" }));
          }, 100);
        }
      })
      .catch((error) => {
        console.log(error);
      })
      .finally(() => {
        setOnUpdateLoading(false);
      });
  };

  const checkContentValue = (c) => {
    if (c == "" || c == "<p></p>" || c == "<p><br></p>") {
      return false;
    }
    return true;
  };

  useEffect(() => {
    return () => {
      dispatch(setNotes360([]));
    };
  }, []);

  const getNotesPages = async () => {
    console.log("FIRED GET NOTES PAGE");

    setOnFetch(true);
    await MainService.getNotes360(
      contactInfo?.family_id,
      contactInfo?.id,
      pageNum,
      10,
      isExternal
    )
      .then((response) => {
        // console.log(response?.data?.data?.data);
        // if (totalPages == null) {
        //   setPerson({
        //     name: response?.data?.data?.label_data,
        //     avatar: response?.data?.data?.avatar,
        //   });
        // }

        //console.log(response?.data?.data);
        if (totalPages == null) {
          if (response?.data?.data?.data?.length > 0) {
            // setNotes(response?.data?.data?.notes?.data);
            dispatch(setNotes360(response?.data?.data?.data));
          }
        } else {
          // setNotes((prev) => {
          //   let newNotes = [...prev, ...response?.data?.data?.notes?.data];
          //   let uniqueNotes = newNotes.filter(
          //     (item, index) =>
          //       newNotes.findIndex((item2) => item2._id === item._id) === index
          //   );
          //   return uniqueNotes;
          // });
          dispatch(addNotes360ToList(response?.data?.data?.data));
        }

        // console.log(response?.data?.data?.last_page);
        // console.log("Total Pages", totalPages);

        if (totalPages == null) {
          setTotalPages(response?.data?.data?.last_page);
        }
      })
      .catch((error) => {
        console.log(error);
      })
      .finally(() => {
        setOnFetch(false);
      });
  };

  useEffect(() => {
    if (pageNum <= totalPages || totalPages == null) {
      getNotesPages();
    }
  }, [pageNum]);

  useEffect(() => {
    const currentElement = lastElement;
    const currentObserver = observer.current;

    if (currentElement) {
      currentObserver.observe(currentElement);
    }

    return () => {
      if (currentElement) {
        currentObserver.unobserve(currentElement);
      }
    };
  }, [lastElement]);

  //mic param
  const [showAudioInput, setShowAudioInput] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();
  const errorMic = () => {
    messageApi.open({
      type: "error",
      content: "Please allow microphone permission to use this feature",
      style: {
        marginTop: "85vh",
      },
    });
  };

  const checkMicrophonePermission = async () => {
    const constraints = {
      audio: true,
      video: false,
    };

    await navigator.mediaDevices
      .getUserMedia(constraints)
      .then(() => {
        setShowAudioInput(true);
      })
      .catch((error) => {
        setShowAudioInput(false);
        errorMic();
      });
    // setIsBlocked(stream.active)
  };

  useEffect(() => {
    // console.log(recordedBlobNotes);
  }, [recordedBlobNotes]);

  const renderVisibility = (note) => {
    if (
      defaultUsr.role.toLowerCase() === "guest" ||
      defaultUsr.role.toLowerCase() === "superguest"
    ) {
      return null;
    } else {
      if (note?.public === true) {
        return (
          <>
            <span className="mx-1">|</span>
            <Tooltip title={t("notes360.public")}>
              <GlobalOutlined className="text-base" />
            </Tooltip>
          </>
        );
      } else if (note?.public === false) {
        return (
          <>
            <span className="mx-1">|</span>
            <Tooltip title={t("notes360.internal")}>
              <TeamOutlined className="text-base font-bold" />
            </Tooltip>
          </>
        );
      } else {
        return null;
      }
    }
  };

  return (
    <div
      className={`${isExternal ? "w-full px-4 py-4" : `overflow-auto `}
      ${from !== "sidebarViewSphere" ? "-mr-[20px] pr-[10px]" : "-mr-2 pr-2"}
  `}
      style={{
        height:
          from === "sidebarViewSphere"
            ? "calc(100vh - 75px)"
            : headerHeight
            ? `calc(100vh - ${headerHeight + 130}px)`
            : "100%",
      }}
      ref={textareaRef}
      onKeyDown={handleKeyDown}
    >
      <div className="top-0 w-full">
        <div className={` w-full flex-col items-end`}>
          {contextHolder}
          {/* {from === "sidebarViewSphere" ? null : (
            <Typography.Title
              level={5}
              style={{
                marginBottom: "10px",
              }}
            >
              Note
            </Typography.Title>
          )} */}
          <div>
            <RichTextInput
              source="notes"
              content={content}
              setContent={(e) => {
                if (!e) setContent("");
                setContent(e?.trim());
              }}
              setEditorContent={(e) => {
                if (!e) setContent("");
                setContent(e?.trim());
              }}
              isMessageSent={clearInput}
              AudioInput={
                <Tooltip
                  title={<span className="capitalize"> Note Audio </span>}
                >
                  <Button
                    onClick={() => {
                      checkMicrophonePermission();
                    }}
                    type="text"
                    size="small"
                    shape="circle"
                    icon={<AudioOutlined />}
                    disabled={showAudioInput}
                  />
                </Tooltip>
              }
            />
          </div>
          <div className="flex w-full items-center justify-end space-x-3">
            {defaultUsr.role.toLowerCase() !== "guest" &&
            defaultUsr.role.toLowerCase() !== "superguest" ? (
              <div
                className="flex items-center space-x-2 "
                style={{ marginTop: "10px" }}
              >
                <div className="flex items-center">
                  {switchValue ? (
                    <>
                      <GlobalOutlined className="text-lg text-gray-400" />
                      <p className="ml-1 text-xs text-gray-400">
                        {t("notes360.public")}
                      </p>
                    </>
                  ) : (
                    <>
                      <TeamOutlined className="text-lg text-gray-400" />
                      <p className="ml-1 text-xs text-gray-400">
                        {t("notes360.internal")}
                      </p>
                    </>
                  )}
                </div>
                <Switch
                  direction="vertical"
                  value={switchValue}
                  onChange={(checked) => {
                    setSwitchValue(checked);
                  }}
                  // style={{ marginTop: "10px" , float: "left" }}
                />
              </div>
            ) : null}
            <Button
              type="primary"
              style={{ marginTop: "10px", float: "right" }}
              onClick={
                checkContentValue(content) || recordedBlobNotes
                  ? noteCreate
                  : null
              }
              loading={onSaveLoading}
              disabled={isRecording}
            >
              {t("notes360.createNote")}
            </Button>
          </div>
          {/* {showAudioInput ? ( */}
          <div className="mt-2">
            <RecordComponent
              isRecording={isRecording}
              setIsRecording={setIsRecording}
              open={showAudioInput}
              close={() => {
                setShowAudioInput(false);
              }}
              setRecordedBlobNotes={setRecordedBlobNotes}
              isFullURL={true}
            />
          </div>
          {/* ) : null} */}
        </div>

        <NoteSearch
          elementId={contactInfo?.id}
          searchResultNote={searchResultNote}
          setSearchResultNote={setSearchResultNote}
          setSearchLoading={setSearchLoading}
          isExternal={isExternal}
        />
      </div>

      <div
        className={`${isExternal ? " h-[calc(40vh)] overflow-y-scroll" : ""}`}
      >
        {searchLoading ? (
          <div className="flex h-64 items-center justify-center">
            <Spin />
          </div>
        ) : null}

        {searchResultNote == null && (
          <div className="w-full">
            {notesRedux && notesRedux?.length > 0 && !searchLoading && (
              <>
                {notesRedux &&
                  notesRedux
                    //orderBy created_at
                    .sort((a, b) => {
                      return (
                        new Date(b?.created_at).getTime() -
                        new Date(a?.created_at).getTime()
                      );
                    })
                    .map((note, index) => (
                      <Card
                        key={note?._id}
                        style={{ marginTop: "10px", width: "100%" }}
                        ref={
                          notesRedux?.length === index + 1
                            ? //  && isSearching == false
                              setLastElement
                            : null
                        }
                      >
                        <Space direction="vertical" style={{ width: "100%" }}>
                          <div className="flex w-full items-center space-x-5">
                            <div className="flex space-x-2">
                              <NoteAvatar
                                name={getName(
                                  note?.dataUser?.label_data,
                                  "avatar"
                                )}
                                avatar={
                                  note?.dataUser?.avatar?.path
                                    ? note?.dataUser?.avatar?.path
                                    : note?.dataUser?.avatar
                                }
                              />

                              <div className="flex-col">
                                <div className="text-xs font-semibold">
                                  {getName(note?.dataUser.label_data, "name")}
                                </div>
                                <div className="flex items-center text-xs font-thin">
                                  {/* {moment
                                    .tz(note.created_at, dateTimeZoneConfig)
                                    .format(
                                      globalDateFormat(
                                        dateConfig,
                                        dateTimeConfig
                                      )
                                    )} */}
                                  {noteDate(
                                    note.created_at,
                                    t,
                                    dateConfig,
                                    dateTimeConfig,
                                    dateTimeZoneConfig
                                  )}
                                  {/* {humanDate(note.created_at, t)} */}
                                  {renderVisibility(note)}
                                </div>
                              </div>
                            </div>
                            {note?.permission == 1 && (
                              <div className="flex space-x-1">
                                <div className="">
                                  <Tooltip
                                    placement="top"
                                    title={t("notes360.editNote")}
                                  >
                                    <Button
                                      type="text"
                                      icon={
                                        <EditOutlined className="text-sm" />
                                      }
                                      onClick={() => toggleNoteEdit(note)}
                                    />
                                  </Tooltip>
                                </div>

                                <div className="">
                                  <Tooltip
                                    placement="top"
                                    title={t("notes360.deleteNote")}
                                  >
                                    <Popconfirm
                                      title={t("notes360.deleteTheNote")}
                                      description={t(
                                        "notes360.ensureDeleteMessage"
                                      )}
                                      onConfirm={() => noteDelete(note._id)}
                                      // onCancel={cancel}
                                      okText={t("notes360.okDelete")}
                                      cancelText={t("notes360.cancelDelete")}
                                    >
                                      <Button
                                        type="text"
                                        icon={
                                          <DeleteOutlined className="text-sm" />
                                        }
                                        // onClick={() => noteDelete(note._id)}
                                        // onClick={showDeleteConfirm}
                                      />
                                    </Popconfirm>
                                  </Tooltip>
                                </div>
                              </div>
                            )}
                          </div>

                          {noteToEditId == note?._id &&
                          noteToEditContent != "" ? (
                            <div className="mt-16 w-full flex-col">
                              <RichTextInput
                                source="note_update"
                                content={note?.content}
                                editorContent={noteToEditContent}
                                setContent={(e) => {
                                  if (!e) setNoteToEditContent("");
                                  setNoteToEditContent(e?.trim());
                                }}
                                setEditorContent={(e) => {
                                  if (!e) setNoteToEditContent("");
                                  setNoteToEditContent(e?.trim());
                                }}
                              />

                              <div className="mt-4 flex w-full items-center justify-end space-x-2">
                                {defaultUsr.role.toLowerCase() !== "guest" &&
                                defaultUsr.role.toLowerCase() !==
                                  "superguest" ? (
                                  <div
                                    className="flex items-center space-x-2 "
                                    style={{ marginTop: "10px" }}
                                  >
                                    <div className="flex items-center">
                                      {note?.public === true ? (
                                        <>
                                          <GlobalOutlined className="text-lg text-gray-400" />
                                          <p className="ml-1 text-xs text-gray-400">
                                            {t("notes360.public")}
                                          </p>
                                        </>
                                      ) : (
                                        <>
                                          <TeamOutlined className="text-lg text-gray-400" />
                                          <p className="ml-1 text-xs text-gray-400">
                                            {t("notes360.internal")}
                                          </p>
                                        </>
                                      )}
                                    </div>
                                    <Switch
                                      direction="vertical"
                                      value={noteToEditVisibility}
                                      // loading={loadingSwitchVis.noteId === note._id && loadingSwitchVis.loading}
                                      onChange={(checked) => {
                                        setNoteToEditVisibility(checked);
                                        // modifyNoteVisibility(note, checked);
                                      }}

                                      // style={{ marginTop: "10px" , float: "left" }}
                                    />
                                  </div>
                                ) : null}
                                <Button
                                  type="primary"
                                  // style={{ marginTop: "10px", float: "right" }}
                                  onClick={() => {
                                    if (checkContentValue(noteToEditContent))
                                      updateNote(note?._id);
                                  }}
                                  loading={onUpdateLoading}
                                >
                                  {t("notes360.saveEdit")}
                                </Button>
                              </div>
                            </div>
                          ) : (
                            <div
                              className="w-full px-4"
                              dangerouslySetInnerHTML={{
                                __html: note?.content,
                              }}
                            />
                          )}
                        </Space>
                        {note?.voice && (
                          <div
                            className={`ml-4
                          ${note?.content ? "mt-2" : ""}
                        `}
                          >
                            <Audio
                              url={`
                               ${
                                 URL_ENV?.REACT_APP_BASE_URL +
                                 process.env.REACT_APP_SUFFIX_AUTH_IMAGE_FILE
                               }${note?.voice[0].audio_file.replace(
                                "/storage/",
                                ""
                              )}
                              `}
                              isFullURL={true}
                            />
                          </div>
                        )}
                      </Card>
                    ))}
              </>
            )}

            {notesRedux?.length == 0 && onFetch == false && (
              <Empty
                style={{
                  marginTop: "10px",
                }}
                description={<span> {t("notes360.noNotesFound")}</span>}
              />
            )}
          </div>
        )}

        {searchResultNote != null && (
          <div className="w-full">
            {searchResultNote &&
              searchResultNote?.length > 0 &&
              !searchLoading && (
                <>
                  {searchResultNote &&
                    searchResultNote
                      //orderBy created_at
                      .sort((a, b) => {
                        return (
                          new Date(b?.created_at).getTime() -
                          new Date(a?.created_at).getTime()
                        );
                      })
                      .map((note, index) => (
                        <Card
                          key={note?._id}
                          style={{ marginTop: "10px", width: "100%" }}
                          // ref={
                          //   searchResultNote?.length === index + 1
                          //     ? //  && isSearching == false
                          //       setLastElement
                          //     : null
                          // }
                        >
                          <Space direction="vertical" style={{ width: "100%" }}>
                            <div className="flex w-full items-center justify-between">
                              <div className="flex space-x-2">
                                <NoteAvatar
                                  name={getName(
                                    note?.dataUser?.label_data,
                                    "avatar"
                                  )}
                                  avatar={
                                    note?.dataUser?.avatar?.path
                                      ? note?.dataUser?.avatar?.path
                                      : note?.dataUser?.avatar
                                  }
                                />

                                <div className="flex-col">
                                  <div className="text-xs font-semibold">
                                    {getName(note?.dataUser.label_data, "name")}
                                  </div>
                                  <div className="flex items-center text-xs font-thin">
                                    {/* {moment
                                    .tz(note.created_at, dateTimeZoneConfig)
                                    .format(
                                      globalDateFormat(
                                        dateConfig,
                                        dateTimeConfig
                                      )
                                    )} */}
                                    {noteDate(
                                      note.created_at,
                                      t,
                                      dateConfig,
                                      dateTimeConfig,
                                      dateTimeZoneConfig
                                    )}

                                    {/* {humanDate(note.created_at, t)} */}
                                    {renderVisibility(note)}
                                  </div>
                                </div>
                              </div>
                              {note?.permission == 1 && (
                                <div className="flex space-x-1">
                                  <div className="">
                                    <Tooltip
                                      placement="top"
                                      title={t("notes360.editNote")}
                                    >
                                      {" "}
                                      <Button
                                        type="text"
                                        icon={
                                          <EditOutlined className="text-sm" />
                                        }
                                        onClick={() => toggleNoteEdit(note)}
                                      />
                                    </Tooltip>
                                  </div>

                                  <div className="">
                                    <Tooltip
                                      placement="top"
                                      title={t("notes360.deleteNote")}
                                    >
                                      <Popconfirm
                                        title={t("notes360.deleteTheNote")}
                                        description={t(
                                          "notes360.ensureDeleteMessage"
                                        )}
                                        onConfirm={() => noteDelete(note._id)}
                                        // onCancel={cancel}
                                        okText={t("notes360.okDelete")}
                                        cancelText={t("notes360.cancelDelete")}
                                      >
                                        <Button
                                          type="text"
                                          icon={
                                            <DeleteOutlined className="text-sm" />
                                          }
                                          // onClick={() => noteDelete(note._id)}
                                          // onClick={showDeleteConfirm}
                                        />
                                      </Popconfirm>
                                    </Tooltip>
                                  </div>
                                </div>
                              )}
                            </div>

                            {noteToEditId == note?._id &&
                            noteToEditContent != "" ? (
                              <div className="mt-16 w-full flex-col ">
                                <RichTextInput
                                  source="note_update"
                                  content={note?.note?.content}
                                  editorContent={noteToEditContent}
                                  setContent={(e) => {
                                    if (!e) setNoteToEditContent("");
                                    setNoteToEditContent(e?.trim());
                                  }}
                                  setEditorContent={(e) => {
                                    if (!e) setNoteToEditContent("");
                                    setNoteToEditContent(e?.trim());
                                  }}
                                />
                                <div className="mt-4 flex w-full items-center justify-end space-x-2">
                                  {defaultUsr.role.toLowerCase() !== "guest" &&
                                  defaultUsr.role.toLowerCase() !==
                                    "superguest" ? (
                                    <div
                                      className="flex items-center space-x-2 "
                                      style={{ marginTop: "10px" }}
                                    >
                                      <div className="flex items-center">
                                        {note?.public === true ? (
                                          <>
                                            <GlobalOutlined className="text-lg text-gray-400" />
                                            <p className="ml-1 text-xs text-gray-400">
                                              {t("notes360.public")}
                                            </p>
                                          </>
                                        ) : (
                                          <>
                                            <TeamOutlined className="text-lg text-gray-400" />
                                            <p className="ml-1 text-xs text-gray-400">
                                              {t("notes360.internal")}
                                            </p>
                                          </>
                                        )}
                                      </div>
                                      <Switch
                                        direction="vertical"
                                        value={noteToEditVisibility}
                                        // loading={loadingSwitchVis.noteId === note._id && loadingSwitchVis.loading}
                                        onChange={(checked) => {
                                          setNoteToEditVisibility(checked);
                                          // modifyNoteVisibility(note, checked);
                                        }}

                                        // style={{ marginTop: "10px" , float: "left" }}
                                      />
                                    </div>
                                  ) : null}
                                  <Button
                                    type="primary"
                                    // style={{ marginTop: "10px", float: "right" }}
                                    onClick={() => {
                                      if (checkContentValue(noteToEditContent))
                                        updateNote(note?._id);
                                    }}
                                    loading={onUpdateLoading}
                                  >
                                    {t("notes360.saveEdit")}
                                  </Button>
                                </div>
                              </div>
                            ) : (
                              <div
                                className="w-full px-4"
                                dangerouslySetInnerHTML={{
                                  __html: note?.content,
                                }}
                              />
                            )}
                          </Space>
                          {note?.voice && (
                            <div
                              className={`ml-4
                          ${note?.content ? "mt-2" : ""}
                        `}
                            >
                              <Audio
                                url={`
                               ${
                                 URL_ENV?.REACT_APP_BASE_URL +
                                 process.env.REACT_APP_SUFFIX_AUTH_IMAGE_FILE
                               }${note?.voice[0].audio_file.replace(
                                  "/storage/",
                                  ""
                                )}
                              `}
                                isFullURL={true}
                              />
                            </div>
                          )}
                        </Card>
                      ))}
                </>
              )}

            {searchResultNote?.length == 0 && onFetch == false && (
              <Empty
                style={{
                  marginTop: "10px",
                }}
                description={<span> {t("notes360.noNotesFound")}</span>}
              />
            )}
          </div>
        )}

        {onFetch ? (
          <div className="flex h-64 items-center justify-center">
            <Spin />
          </div>
        ) : null}
      </div>
    </div>
  );
}

export default Notes;
