import { Skeleton } from "antd";

export const skl = () => {
  const skeletonCount = Math.floor(document?.body?.clientHeight / 5);

  return Array.from({ length: skeletonCount }, (_, i) => {
    const randomRows = Math.floor((Math.random() + 1) * 2);
    return (
      <div className="flex items-center px-1" key={`sklt_${i}`}>
        <Skeleton avatar paragraph={{ rows: randomRows }} active />
      </div>
    );
  });
};
