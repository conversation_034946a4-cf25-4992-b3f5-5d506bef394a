import { useCallback, useEffect, useRef, useState } from "react";
import { Table, Tag } from "antd";
import { toastNotification } from "../../../components/ToastNotification";
import { useTranslation } from "react-i18next";
import { getLog360 } from "../services/services";
import { handlePageSizeOptions, humanDate } from "../helpers/helpersFunc";

import "../index.css";
import CallerColumn360 from "../components/CallerColumn360";
import { useWindowSize } from "../../clients&users/components/WindowSize";
import DurationColumn from "../components/DurationColumn";
import { useSelector } from "react-redux";
import { URL_ENV } from "index";
import { SyncOutlined } from "@ant-design/icons";

const handleLog = (data, t) =>
  data?.map((item) => ({
    id: item?._id,
    date: humanDate(item?.calldate_start, t),
    disposition: item?.disposition,
    callerId: item?.src_id,
    callerFamilyId: item?.scr_family,
    caller: item?.src_name?.replaceAll("_", " "),
    callerNum: item?.src,
    callerImg: item?.src_image
      ? `${URL_ENV?.REACT_APP_BASE_URL + URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL}${
          item?.src_image
        }`
      : null,
    calledId: item?.dst_id,
    calledFamilyId: item?.dst_family,
    called: item?.dst_name?.replaceAll("_", " "),
    calledNum: item?.dst,
    calledImg: item?.dst_image
      ? `${URL_ENV?.REACT_APP_BASE_URL + URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL}${
          item?.dst_image
        }`
      : null,
    duration: item?.billsec,
    audioRecording:
      item?.id_enregistrement?.length > 0 && item?.id_enregistrement,
  }));

const CallLogs360 = ({ contactId }) => {
  //
  const [t] = useTranslation("common");
  const tableRef = useRef(null);
  const windowSize = useWindowSize();

  const { logs } = useSelector(({ voip }) => voip);
  //
  const [shouldFetchData, setShouldFetchData] = useState(true);
  const [dataSource, setDataSource] = useState([]);
  const [loadingTable, setLoadingTable] = useState(true);
  const [totalLogs, setTotalLogs] = useState(0);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(20);
  //
  //
  useEffect(() => {
    setShouldFetchData(true);
  }, [page, limit, contactId, logs]);
  //
  const fetchLog = useCallback(async () => {
    if (!shouldFetchData || !contactId) return;
    try {
      setLoadingTable(true);
      const {
        data: {
          data,
          meta: { total },
        },
      } = await getLog360(contactId, limit, page);

      setDataSource(handleLog(data, t));
      setTotalLogs(total);
    } catch (err) {
      if (err?.response?.status === 401 || err?.code === "ERR_CANCELED") return;
      else {
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
        throw new Error(err?.message ? err.message : err);
      }
    } finally {
      setLoadingTable(false);
      setShouldFetchData(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shouldFetchData, contactId]);

  useEffect(() => {
    fetchLog();
  }, [fetchLog]);
  //
  // const trackRelatedLog = useCallback(() => {
  //   if (!logs || !logs.length) return;
  //   // const slicedLogs = logs.slice(0, Math.min(logs?.length, limit));
  //   const slicedLogs = logs.slice(0, 1);
  //   const newLogs = slicedLogs.filter(
  //     (logItem) =>
  //       logItem?.dst_id === contactId || logItem?.src_id === contactId
  //   );
  //   setDataSource((prev) => {
  //     const existingIds = new Set(prev.map((item) => item._id));
  //     const filteredNewLogs = newLogs.filter(
  //       (log) => !existingIds.has(log._id)
  //     );
  //     return [...handleLog(filteredNewLogs, t), ...prev];
  //   });
  //   setTotalLogs((prev) => prev + newLogs?.length);
  // }, [contactId, logs, t]);

  // useEffect(() => {
  //   logs?.length && setShouldFetchData(true);
  // }, [logs]);
  //
  const columns = [
    {
      title: "Appelant",
      dataIndex: "caller",
      key: "caller",
      fixed: "left",
      render: (_, record) => (
        <CallerColumn360
          {...record}
          col={"caller"}
          // colWidth={getColumnWidth(tableRef, 0)}
        />
      ),
    },
    {
      title: "Appelé(e)",
      dataIndex: "called",
      key: "called",
      fixed: "left",
      render: (_, record) => (
        <CallerColumn360
          {...record}
          col={"called"}
          // colWidth={getColumnWidth(tableRef, 1)}
        />
      ),
    },
    {
      title: "Disposition",
      dataIndex: "disposition",
      key: "disposition",
      width: "150px",
      render: (disposition) => (
        <Tag
          bordered={false}
          icon={disposition === "IN_PROGRESS" ? <SyncOutlined spin /> : false}
          color={
            disposition === "ANSWERED"
              ? "success"
              : disposition === "BUSY"
              ? "warning"
              : disposition === "IN_PROGRESS"
              ? "processing"
              : "error"
          }
          // text={disposition}
        >
          {disposition === "ANSWERED"
            ? t("voip.answered")
            : disposition === "BUSY"
            ? t("voip.busy")
            : disposition === "IN_PROGRESS"
            ? t("globalSearch.visioInProgress")
            : t("voip.callMissed")}
        </Tag>
      ),
    },
    {
      title: "Date",
      dataIndex: "date",
      key: "date",
      width: "200px",
    },
    {
      title: "Durée",
      dataIndex: "duration",
      key: "duration",
      render: (duration, { audioRecording }) =>
        duration &&
        duration !== "00:00:00" && (
          <DurationColumn
            audioRecording={audioRecording}
            // width={getColumnWidth(tableRef, 4)}
            duration={duration}
          />
        ),
    },
  ];
  //
  const handleShowTotal = (total, range) => (
    <span>{`${range[0]}-${range[1]} of ${total} `}</span>
  );
  //
  return (
    <div className="table-view">
      <Table
        ref={tableRef}
        columns={columns}
        dataSource={dataSource}
        loading={loadingTable}
        size="small"
        pagination={
          totalLogs <= 10
            ? false
            : {
                showTotal: (total, range) => handleShowTotal(total, range),
                showSizeChanger: true,
                showQuickJumper: true,
                total: totalLogs - 1,
                pageSize: limit,
                current: page,
                onChange: (page) => setPage(page),
                onShowSizeChange: (current, size) => setLimit(size),
                pageSizeOptions: totalLogs && handlePageSizeOptions(totalLogs),
                size: "small",
              }
        }
        scroll={{
          // y: windowSize?.height - (isStepsDisplayed ? 425 : 280),
          y: windowSize?.height - 400,

          // x: 1100,
        }}
      />
    </div>
  );
};

export default CallLogs360;
