/**
 * @name GeneralInfos
 *
 * @description `GeneralInfos` component .
 *
 * @param {String} guestsSearchQuery .
 * @param {String} followersSearchQuery .
 * @param {String} source .
 * @param {Number} guestsListPage .
 * @param {Number} followersListPage .
 * @param {Number} guestsListLastPage .
 * @param {Boolean} loadOwners .
 * @param {Boolean} loadGuests .
 * @param {Boolean} openActivity360 .
 * @param {Array} pipelines .
 * @param {Array} guestsList .
 * @param {Array} checkedItems .
 * @param {Array} ownersList .
 * @param {Array} checkedFollowers .
 * @param {Object} singleTaskData .
 * @param {Object} totalEntities .
 * @param {Object} form .
 * @param {Function} setGuestsSearchQuery .
 * @param {Function} setGuestsListPage .
 * @param {Function} setCheckedItems .
 * @param {Function} setSingleTaskData .
 * @param {Function} setCheckedFollowers .
 * @param {Function} setFollowersSearchQuery .
 * @param {Function} setCountChanges .
 * @param {Function} setSelectedFamilyMembers .
 * @param {Function} onCloseModal .
 * @param {Function} setFollowersListPage .
 * @param {Function} tasksTypes .
 *
 * @returns {JSX.Element} .
 */

import { useLayoutEffect, useRef, useState, memo } from "react";
import {
  Avatar,
  Badge,
  Descriptions,
  Dropdown,
  Popover,
  Select,
  Tooltip,
  message,
  Form,
  Button,
} from "antd";
import { TbFlag3, TbFlag3Filled } from "react-icons/tb";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import {
  PlusOutlined,
  QuestionCircleOutlined,
  MoreOutlined,
  RightCircleOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";

import ChoiceIcons from "pages/components/ChoiceIcons";
import { getName } from "pages/layouts/chat/utils/ConversationUtils";
import UsersList from "../UsersList";
import { handleCheck, uncheckGuest } from "../helpers/handleCheck";
import { AvatarChat } from "components/Chat";
import { URL_ENV } from "index";
import AvatarGroup from "components/AvatarGroup";
import {
  displayPriorityColor,
  handlePriorityLabelOnHover,
  prioritiesList,
} from "../helpers/handlePriorities";
import MainService from "services/main.service";
import DateTimeCalendar from "./DateTimeCalendar";
import ColleaguesList from "../ColleaguesList";
import { toastNotification } from "components/ToastNotification";
import { EXTENSIONS_ARRAY } from "../helpers/calculateSum";
import ActionsComponent from "../ActionsComponent";

const GeneralInfos = memo(function GeneralInfos({
  singleTaskData,
  tasksTypes,
  pipelines,
  guestsList,
  checkedItems,
  guestsSearchQuery,
  setGuestsSearchQuery,
  followersSearchQuery,
  guestsListPage,
  setGuestsListPage,
  guestsListLastPage,
  setCheckedItems,
  setSingleTaskData,
  ownersList,
  checkedFollowers,
  setCheckedFollowers,
  setFollowersSearchQuery,
  loadOwners,
  loadGuests,
  form,
  setCountChanges,
  setSelectedFamilyMembers,
  source = "",
  openActivity360,
  onCloseModal = () => {},
  totalEntities,
  followersListPage,
  setFollowersListPage,
}) {
  const { families } = useSelector((state) => state?.families);
  const currentUser = useSelector((state) => state?.user?.user);
  const [t] = useTranslation("common");
  const listHeight = useRef(null);

  const [displayFollowersList, setDisplayFollowersList] = useState(null);
  const [familyElements, setFamilyElements] = useState([]);
  const [loadFamilyElements, setLoadFamilyElements] = useState(false);
  const [filterParticipantsValue, setFilterParticipantsValue] = useState(0);

  // Dispatch change priority API.
  const updatePriority = async (id, newPriorityStatus) => {
    try {
      let response = await MainService.updateTaskPriority(
        id,
        newPriorityStatus
      );
      setSingleTaskData(response?.data?.data);
      setCountChanges((prev) => prev + 1);
    } catch (error) {
      console.log(`Error ${error}`);
      message.error("Something went wrong!", 4.5);
    }
  };

  const UpdateTaskStage = async (payload, source) => {
    try {
      const response = await MainService.updateTaskStageInKanban(payload);
      setSingleTaskData(response?.data?.data);
      setCountChanges((prev) => prev + 1);
    } catch (error) {
      console.log(`Error ${error}`);
    }
  };

  const handleDatePickerChange = (date, dateString) => {
    // console.log(date, dateString);
    // setPopoverVisible(false); // Close Popover after selecting a date
  };

  const handleAddMembers = async (role, id) => {
    try {
      let payload = {
        role: role,
        member_id: id,
      };
      let response = await MainService.addMembers(singleTaskData?.id, payload);
      if (response?.data?.message === "Owner changed successfully") {
        onCloseModal();
        toastNotification(
          "success",
          t("tasks.notAuthorizedToSeeActivity"),
          "topRight"
        );
      } else {
        setSingleTaskData(response?.data?.data);
      }
      setCountChanges((prev) => prev + 1);
    } catch (error) {
      console.log(`Error ${error}`);
    }
  };

  const handleRemoveMembers = async (role, id) => {
    try {
      let payload = {
        role: role,
        member_id: id,
      };
      const response = await MainService.removeMembers(
        singleTaskData?.id,
        payload
      );
      setSingleTaskData(response?.data?.data);
      setCountChanges((prev) => prev + 1);
    } catch (error) {
      console.log(`Error ${error}`);
    }
  };

  const handleUpdateDate = async (type, fulldate, source) => {
    try {
      let payload = {
        start_date: dayjs(form.getFieldValue("start_date")).format(
          `${currentUser?.location?.date_format}`
        ),
        start_time: dayjs(form.getFieldValue("start_time"))
          .format(`${currentUser?.location?.time_format}`)
          ?.toUpperCase(),
        end_date: dayjs(form.getFieldValue("end_date")).format(
          `${currentUser?.location?.date_format}`
        ),
        end_time: dayjs(form.getFieldValue("end_time"))
          .format(`${currentUser?.location?.time_format}`)
          ?.toUpperCase(),
      };
      const response = await MainService.updateTaskDateTime(
        singleTaskData?.id,
        payload
      );
      setSingleTaskData(response?.data?.data);
      setCountChanges((prev) => prev + 1);
      return response;
    } catch (error) {
      if (
        error?.response?.data?.errors[0] ===
        "The start date-time cannot be after the end date-time."
      ) {
        toastNotification("error", t("tasks.dateTimeError"));
      }
      console.log(`Error ${error}`);
      return error;
    }
  };

  // Get family elements by family id.
  const getFamilyElementsById = async (familyId) => {
    setLoadFamilyElements(true);
    try {
      const response = await MainService.getFamilyElement(familyId);
      setFamilyElements(response?.data?.data);
      return response;
    } catch (error) {
      console.log(`Error ${error}`);
    } finally {
      setLoadFamilyElements(false);
    }
  };

  // Show family and element on update activity.
  useLayoutEffect(() => {
    let isMounted = true;
    if (isMounted) {
      if (
        Object.keys(singleTaskData)?.length > 0 &&
        singleTaskData?.element_id
      ) {
        const getData = async () => {
          let response = await getFamilyElementsById(singleTaskData?.family_id);
          setFamilyElements(response?.data?.data);
          form.setFieldsValue({
            family: singleTaskData?.family_id,
            relatedElement: singleTaskData?.element_id,
          });
        };
        getData();
      }
    }
    return () => {
      isMounted = false;
    };
  }, [singleTaskData]);

  const updateModuleElement = async (familyId, elementId) => {
    try {
      let payload = {
        family_id: familyId,
        element_id: elementId,
      };
      const response = await MainService.updateModuleElement(
        singleTaskData?.id,
        payload
      );
      setSingleTaskData(response?.data?.data);
      setCountChanges((prev) => prev + 1);
    } catch (error) {
      console.log(`Error ${error}`);
    }
  };

  const updateActivityType = async (newTypeId) => {
    try {
      let payload = {
        task_type: newTypeId,
      };
      const response = await MainService.updateActivityType(
        singleTaskData?.id,
        payload
      );
      setSingleTaskData(response?.data?.data);
      setCountChanges((prev) => prev + 1);
    } catch (error) {
      console.log(`Error ${error}`);
    }
  };

  // Handle search.
  const handleSearchInSelect = (input, option) =>
    option?.label?.toLowerCase()?.includes(input?.toLowerCase());

  // Handle select module.
  const handleSelectModule = (value) => {
    getFamilyElementsById(value);
    form.setFieldsValue({
      relatedElement: null,
    });
  };

  // Clear the module field.
  const handleClearModuleField = () => {
    setFamilyElements([]);
    form.setFieldsValue({
      relatedElement: null,
    });
    updateModuleElement(null, null);
  };

  // Handle select specific element.
  const selectModuleElement = (value) => {
    let familyId = form.getFieldValue("family");
    updateModuleElement(familyId, value);
  };

  const items = [
    {
      key: "1",
      span: 1,
      label: t("table.header.type"),
      children: (
        <Select
          value={singleTaskData?.tasks_type_id}
          popupMatchSelectWidth={false}
          optionFilterProp={["labelValue"]}
          filterOption={(input, option) =>
            option?.labelValue.toLowerCase().includes(input.toLowerCase())
          }
          bordered={false}
          style={{
            width: "auto",
            padding: 0,
            backgroundColor: "#0000000a",
            borderRadius: "6px",
          }}
          disabled={
            Object.keys(singleTaskData).length > 0
              ? singleTaskData?.can_update_task === 0 || source === "visio"
              : false
          }
          options={
            tasksTypes &&
            tasksTypes.map((el) => ({
              value: el?.id,
              label: (
                <>
                  <span className="mb-2">
                    <ChoiceIcons icon={el?.icons} />
                  </span>{" "}
                  {el?.label}
                </>
              ),
              labelValue: el?.label,
            }))
          }
          onSelect={(value) => {
            updateActivityType(value);
          }}
        />
      ),
    },
    {
      key: "2",
      span: 1,
      label: t("tasks.priority"),
      children: (
        <Dropdown
          menu={{
            items: prioritiesList(),
            onClick: (e) =>
              updatePriority(singleTaskData?.id, { priority: e?.key }),
            selectedKeys: [singleTaskData?.priority],
          }}
          trigger={["click"]}
          open={
            Boolean(singleTaskData?.can_update_task === 0) ? false : undefined
          }
        >
          {singleTaskData?.priority ? (
            <div className="flex items-center">
              <TbFlag3Filled
                style={{
                  fontSize: "18px",
                  cursor:
                    singleTaskData?.can_update_task === 0
                      ? "not-allowed"
                      : "pointer",
                  color: displayPriorityColor(singleTaskData?.priority),
                }}
              />
              <span className="ml-2">
                {handlePriorityLabelOnHover(singleTaskData?.priority)}
              </span>
            </div>
          ) : (
            <Tooltip title={t("tasks.setPriority")}>
              <TbFlag3
                style={{
                  fontSize: "18px",
                  cursor:
                    singleTaskData?.can_update_task === 0
                      ? "not-allowed"
                      : "pointer",
                  color: "#bfbfbf",
                }}
              />
            </Tooltip>
          )}
        </Dropdown>
      ),
    },
    {
      key: "3",
      span: 1,
      label: "Pipeline/Stage",
      children: (
        <Select
          style={{
            width: "auto",
            padding: 0,
            backgroundColor: "#0000000a",
            borderRadius: "6px",
          }}
          placeholder={t("tasks.choose")}
          bordered={false}
          disabled={singleTaskData?.is_follower === 1}
          value={
            singleTaskData?.stage_id
              ? {
                  label: `${singleTaskData?.pipeline_label}/${singleTaskData?.stage_label}`,
                  value: singleTaskData?.stage_id,
                }
              : null
          }
          allowClear
          popupMatchSelectWidth={false}
          onChange={(e) => {
            UpdateTaskStage(
              {
                "task_id[]": singleTaskData?.id,
                new_stage_id: e ?? 0,
              },
              "singleUpdate"
            );
          }}
          options={
            pipelines &&
            pipelines.map((pipeline) => ({
              label: pipeline?.label,
              value: pipeline?.pipeline_key,
              options:
                pipeline?.stages &&
                pipeline?.stages.map((stage) => ({
                  label: (
                    <>
                      <Badge
                        color={stage?.color}
                        style={{ marginRight: "10px" }}
                      />
                      {stage?.label}{" "}
                      {stage?.percent &&
                      stage?.percent !== null &&
                      Number(stage?.percent) !== 0 ? (
                        <span className="text-[#8c8c8c]">{`(${stage?.percent}%)`}</span>
                      ) : null}
                    </>
                  ),
                  value: stage?.id,
                })),
            }))
          }
        />
      ),
    },
    {
      key: "4",
      span: 1,
      label: t("tasks.owner"),
      children: (
        <div className="relative">
          <Tooltip
            title={getName(singleTaskData?.owner_id?.label, "name")}
            overlayStyle={{ zIndex: "99999" }}
          >
            <Popover
              key={"ownersList"}
              content={
                <ColleaguesList
                  key={"ownersList"}
                  source="owner"
                  usersList={ownersList}
                  searchQuery={followersSearchQuery}
                  checkedItems={checkedFollowers}
                  setCheckedItems={setCheckedFollowers}
                  displayFollowersList={displayFollowersList}
                  handleCheckedItems={handleCheck}
                  setFollowersSearchQuery={setFollowersSearchQuery}
                  loading={loadOwners}
                  ref={listHeight}
                  dispatchAddMembers={handleAddMembers}
                  dispatchRemoveMembers={handleRemoveMembers}
                  defaultOwner={singleTaskData?.owner_id?.id}
                  totalEntities={totalEntities}
                  setCurrentPage={setFollowersListPage}
                  currentPage={followersListPage}
                />
              }
              showAudioInput={false}
              title={t("tasks.tableOwner")}
              trigger={"click"}
              onOpenChange={(open) => {
                setDisplayFollowersList(open ? "ownersList" : null);
                if (!open) {
                  setFollowersSearchQuery("");
                  setFollowersListPage(1);
                }
              }}
              open={
                Boolean(
                  Object.keys(singleTaskData).length > 0 &&
                    singleTaskData?.can_update_task === 0
                )
                  ? false
                  : undefined
              }
              arrow={false}
              destroyTooltipOnHide={true}
            >
              <div>
                <AvatarChat
                  fontSize="0.7rem"
                  className="mx-1.5"
                  size={34}
                  height={14}
                  width={14}
                  url={`${
                    URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                  }${singleTaskData?.owner_id?.avatar}`}
                  hasImage={EXTENSIONS_ARRAY?.includes(
                    singleTaskData?.owner_id?.avatar?.split(".")?.pop()
                  )}
                  name={getName(singleTaskData?.owner_id?.label, "avatar")}
                  type="user"
                />
              </div>
            </Popover>
          </Tooltip>
          <Badge
            className="absolute -right-0.5 -top-0.5"
            count={
              singleTaskData?.owner_id?.id !== currentUser?.id ? (
                <ActionsComponent
                  elementValue={singleTaskData?.owner_id}
                  tooltipText={false}
                  showDropdown={true}
                  placement="topLeft"
                >
                  <RightCircleOutlined />
                </ActionsComponent>
              ) : null
            }
          />
        </div>
      ),
    },
    {
      key: "5",
      span: 1,
      label: t("tasks.guests", {
        s: checkedItems && checkedItems?.length > 1 ? "s" : "",
      }),
      children: (
        <div className="flex flex-row-reverse items-center justify-center">
          <Popover
            destroyTooltipOnHide={true}
            content={
              <UsersList
                key={"guestsList"}
                usersList={guestsList}
                searchQuery={guestsSearchQuery}
                setSearchQuery={setGuestsSearchQuery}
                lastPage={guestsListLastPage}
                currentPage={guestsListPage}
                setCurrentPage={setGuestsListPage}
                checkedItems={checkedItems}
                handleCheckedItems={handleCheck}
                loading={loadGuests}
                setCheckedItems={setCheckedItems}
                dispatchAddMembers={handleAddMembers}
                dispatchRemoveMembers={handleRemoveMembers}
                setSelectedFamilyMembers={setSelectedFamilyMembers}
                setFilterParticipantsValue={setFilterParticipantsValue}
                filterParticipantsValue={filterParticipantsValue}
                totalEntities={totalEntities}
              />
            }
            showAudioInput={false}
            title={t("tasks.guestsListTitle")}
            trigger={["click"]}
            onOpenChange={(open) => {
              if (!open) {
                setGuestsSearchQuery("");
                setFilterParticipantsValue(0);
                setSelectedFamilyMembers([1, 2, 4, 9]);
              }
            }}
            open={
              Boolean(
                Object.keys(singleTaskData).length > 0 &&
                  singleTaskData?.can_update_task === 0
              )
                ? false
                : undefined
            }
            arrow={false}
          >
            {singleTaskData?.can_update_task === 1 && (
              <Avatar
                shape="circle"
                alt={"item_avatar"}
                style={{
                  cursor: "pointer",
                  marginBottom: "4px",
                }}
                icon={<PlusOutlined />}
              ></Avatar>
            )}
          </Popover>
          {checkedItems && checkedItems?.length > 0 ? (
            <AvatarGroup
              source="taskTable"
              usersArray={checkedItems}
              uncheckUser={uncheckGuest}
              disableDelete={singleTaskData?.can_update_task === 0}
              setCheckedItems={setCheckedItems}
              dispatchRemoveMembers={handleRemoveMembers}
              role={2}
              from={openActivity360}
            />
          ) : (
            singleTaskData?.can_update_task === 0 && t("tasks.noGuest")
          )}
        </div>
      ),
    },
    {
      key: "6",
      span: 1,
      label: t("tasks.followers", {
        s: checkedFollowers && checkedFollowers?.length > 1 ? "s" : "",
      }),
      children: (
        <div className="flex flex-row-reverse items-center justify-center">
          <Popover
            key={"followersList"}
            destroyTooltipOnHide={true}
            content={
              <ColleaguesList
                key={"followersList"}
                source="followersList"
                usersList={ownersList}
                searchQuery={followersSearchQuery}
                checkedItems={checkedFollowers}
                setCheckedItems={setCheckedFollowers}
                displayFollowersList={displayFollowersList}
                handleCheckedItems={handleCheck}
                setFollowersSearchQuery={setFollowersSearchQuery}
                loading={loadOwners}
                ref={listHeight}
                dispatchAddMembers={handleAddMembers}
                dispatchRemoveMembers={handleRemoveMembers}
                totalEntities={totalEntities}
                setCurrentPage={setFollowersListPage}
                currentPage={followersListPage}
              />
            }
            showAudioInput={false}
            title={t("tasks.followersListTitle")}
            trigger={"click"}
            onOpenChange={(open) => {
              setDisplayFollowersList(open ? "followersList" : null);
              if (!open) {
                setFollowersSearchQuery("");
                setFollowersListPage(1);
              }
            }}
            open={
              Boolean(
                Object.keys(singleTaskData).length > 0 &&
                  singleTaskData?.can_update_task === 0
              )
                ? false
                : undefined
            }
            arrow={false}
          >
            {singleTaskData?.can_update_task === 1 && (
              <Avatar
                shape="circle"
                alt={"item_avatar"}
                style={{
                  cursor: "pointer",
                  marginBottom: "4px",
                }}
                icon={<PlusOutlined />}
              ></Avatar>
            )}
          </Popover>
          {checkedFollowers && checkedFollowers?.length > 0 ? (
            <AvatarGroup
              source="taskTable"
              usersArray={checkedFollowers}
              disableDelete={singleTaskData?.can_update_task === 0}
              setCheckedItems={setCheckedFollowers}
              uncheckUser={uncheckGuest}
              dispatchRemoveMembers={handleRemoveMembers}
              role={3}
            />
          ) : (
            singleTaskData?.can_update_task === 0 && t("tasks.noFollower")
          )}
        </div>
      ),
    },
    {
      key: "7",
      span: 3,
      label: t("tasks.startDatePlaceholder"),
      children: (
        <DateTimeCalendar
          key={`Start_Date_${singleTaskData?.id}`}
          source="start"
          date={singleTaskData?.start_date}
          time={singleTaskData?.start_time}
          handlePanelChange={handleDatePickerChange}
          variant="filled"
          dispatchUpdateDate={handleUpdateDate}
          readOnly={singleTaskData?.can_update_task === 0}
          beginningDate={null}
          form={form}
        />
      ),
    },
    {
      key: "8",
      span: 3,
      label: t("tasks.endDatePlaceholder"),
      children: (
        <DateTimeCalendar
          key={`End_Date_${singleTaskData?.id}`}
          source="end"
          date={singleTaskData?.end_date}
          time={singleTaskData?.end_time}
          handlePanelChange={handleDatePickerChange}
          variant="filled"
          status={singleTaskData?.is_overdue}
          readOnly={singleTaskData?.can_update_task === 0}
          dispatchUpdateDate={handleUpdateDate}
          beginningDate={`${singleTaskData?.start_date} ${singleTaskData?.start_time}`}
          form={form}
        />
      ),
    },
    {
      key: "9",
      span: 1.5,
      label: (
        <div className="flex items-center">
          {t("tasks.associatedModule")}{" "}
          <Tooltip title={t("tasks.selectModuleInfo")}>
            <QuestionCircleOutlined style={{ marginLeft: "5px" }} />
          </Tooltip>
        </div>
      ),
      children: (
        <Form.Item noStyle name="family">
          <Select
            allowClear
            showSearch
            defaultValue={singleTaskData?.family_id}
            popupMatchSelectWidth={false}
            optionFilterProp={["label"]}
            filterOption={handleSearchInSelect}
            disabled={singleTaskData?.can_update_task === 0}
            className="max-w-[230px] rounded-[6px] bg-[#0000000a] p-0"
            placeholder={t("tasks.selectFamilyPlaceholder")}
            options={families
              ?.map((element) => ({
                label: element?.label,
                value: element?.id,
              }))
              ?.sort((a, b) => a?.label?.localeCompare(b?.label))}
            onSelect={handleSelectModule}
            onClear={handleClearModuleField}
            bordered={false}
          />
        </Form.Item>
      ),
    },
    {
      key: "10",
      span: 1.5,
      label: (
        <div className="flex items-center">
          {t("tasks.selectFamilyElement")}{" "}
          <Tooltip title={t("tasks.selectModuleElementInfo")}>
            <QuestionCircleOutlined style={{ marginLeft: "5px" }} />
          </Tooltip>
        </div>
      ),
      children: (
        <Form.Item noStyle name="relatedElement">
          <Select
            showSearch={familyElements?.length > 0}
            optionFilterProp={["label"]}
            virtual
            popupMatchSelectWidth={false}
            filterOption={handleSearchInSelect}
            disabled={singleTaskData?.can_update_task === 0}
            className="max-w-[250px] rounded-[6px] bg-[#0000000a] p-0"
            placeholder={t("tasks.selectFamilyElementPlaceholder")}
            options={familyElements?.map((el) => ({
              label: el?.label_data || "no data",
              value: el?.id,
            }))}
            onSelect={selectModuleElement}
            loading={loadFamilyElements}
            bordered={false}
            dropdownStyle={{
              maxWidth: 350,
            }}
          />
        </Form.Item>
      ),
    },
  ];

  return (
    <>
      <Descriptions layout="horizontal">
        {items
          .filter((_, index) => index <= 5)
          .map((item) => {
            return (
              <Descriptions.Item
                className="activity-details"
                key={item?.key}
                label={item?.label}
                span={item?.span}
                style={{ verticalAlign: "initial" }}
              >
                {item?.children}
              </Descriptions.Item>
            );
          })}
      </Descriptions>
      <Descriptions layout="horizontal">
        {items
          .filter((_, index) => index > 5 && index <= 7)
          .map((item) => {
            return (
              <Descriptions.Item
                className="activity-details"
                key={item?.key}
                label={item?.label}
                span={1}
                style={{ verticalAlign: "initial" }}
              >
                {item?.children}
              </Descriptions.Item>
            );
          })}
      </Descriptions>
      <Descriptions layout="horizontal">
        {items
          .filter((_, index) => index > 7)
          .map((item) => {
            return (
              <Descriptions.Item
                className="activity-details"
                key={item?.key}
                label={item?.label}
                span={1}
                style={{ verticalAlign: "initial" }}
              >
                {item?.children}
              </Descriptions.Item>
            );
          })}
      </Descriptions>
    </>
  );
});

export default GeneralInfos;
