import { Card, Col, Row, Statistic } from "antd";
import {
  backgroundImagecard,
  GoTo,
  gridStyle,
  stylesCard,
} from "pages/home/<USER>";
import React, { useMemo } from "react";
import CountUp from "react-countup";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { LucideIcon } from "../ticket.stat/ticket";
import { PieChartWithLegend } from "../ChartsDashboard";
import EmptyPage from "components/EmptyPage";

const CardStatDeal = () => {
  const { statsDeals } = useSelector((state) => state.dashboardRealTime);
  const navigate = useNavigate();
  const [t] = useTranslation("common");
  const formatter = (value) => <CountUp end={value} separator="," />;
  const { user } = useSelector((state) => state.user);

  const dealsData = useMemo(
    () => ({
      data: statsDeals?.slice(1).map((el) => ({ ...el, y: el.value })),
      total: statsDeals?.slice(0, 1)?.value,
      name: "",
    }),
    [statsDeals]
  );
  return (
    <Card
      style={{ backgroundImage: backgroundImagecard }}
      styles={{ ...stylesCard }}
      title={
        <div className="flex items-center justify-between pr-2">
          <span>{t("menu1.deals")}</span> &nbsp;
          <GoTo
            to={"9"}
            title={t("menu1.deals")}
            navigate={navigate}
            t={t}
            user={user}
          />
        </div>
      }
    >
      <Row>
        <Col span={12}>
          <Card style={{ background: backgroundImagecard, border: 0 }}>
            {statsDeals?.slice(1).map((el, i) => (
              <Card.Grid
                hoverable={false}
                style={{
                  ...gridStyle,
                  borderStartStartRadius: i === 0 ? 6 : 0,
                  borderEndStartRadius: i === 2 ? 6 : 0,
                }}
                key={i}
              >
                <Statistic
                  formatter={typeof el.value === "number" ? formatter : null}
                  title={el.name}
                  value={el.value}
                  valueStyle={{
                    color: el.color,
                  }}
                  prefix={
                    <LucideIcon
                      iconName={el?.icon}
                      color={el?.color}
                      size={20}
                    />
                  }
                />
              </Card.Grid>
            ))}
          </Card>
        </Col>
        <Col
          span={12}
          style={{
            paddingLeft: 0,
            borderLeft: "1px solid #f0f0f0",
            width: "100%",
            height: "100%",
            borderTopRightRadius: 6,
            borderBottomRightRadius: 6,
            background: "white",
          }}
        >
          {dealsData?.data
            ?.map((el) => el.y)
            ?.reduce(
              (accumulator, currentValue) => accumulator + currentValue,
              0
            ) > 0 ? (
            <PieChartWithLegend
              data={dealsData}
              height={173}
              alignLegend={{
                align: "right",
                verticalAlign: "middle",
                layout: "vertical",
                enabled: false,
              }}
              exporting={false}
            />
          ) : (
            <div className="h-[173px]">
              <EmptyPage />
            </div>
          )}
        </Col>
      </Row>
    </Card>
  );
};

export default CardStatDeal;
