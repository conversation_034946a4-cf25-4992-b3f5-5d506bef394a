import {
  ArrowRightOutlined,
  HomeOutlined,
  InfoCircleOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import {
  Avatar,
  Badge,
  Button,
  Card,
  Col,
  Divider,
  Empty,
  List,
  Row,
  Select,
  Space,
  Statistic,
  Tag,
  Tooltip,
  Typography,
} from "antd";
import { AvatarChat } from "components/Chat";
import { URL_ENV } from "index";
import { ArrowUp, ChevronLeft, ChevronRight } from "lucide-react";
import React, { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import {
  Chart2<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  OneBar<PERSON>hart,
  Pie<PERSON>hart,
} from "./components/ChartsDashboard";
import { useDispatch } from "react-redux";
import {
  addTaskInDashboard,
  getDataDashboard,
  RemoveTaskInDashboard,
  setSelectedMail,
  UpdateTasksInDashboard,
} from "new-redux/actions/dashboard.actions";
import ListTasks from "./components/ListTasks";
import {
  AtSymbolIcon,
  ChatBubbleBottomCenterTextIcon,
  ChatBubbleLeftRightIcon,
  PhoneArrowDownLeftIcon,
  PhoneArrowUpRightIcon,
} from "@heroicons/react/24/outline";
import { FcCallback } from "react-icons/fc";
import { MdPhoneMissed } from "react-icons/md";
import { generateAxios } from "services/axiosInstance";
import { SET_RMC, SET_STATS_TASKS_DASHBOARD } from "new-redux/constants";

import { familyIcons } from "pages/components/DetailsProfile/ViewSphere2";
import ChoiceIcons from "pages/components/ChoiceIcons";
import i18n from "translations/i18n";
import { LucideIcon } from "./components/ticket.stat/ticket";
import EmptyPage from "components/EmptyPage";
import TasksRoom from "pages/tasks/tasksRoom";
import CreateTask from "pages/voip/components/CreateTask";
import { setTask360 } from "new-redux/actions/chat.actions/Input";
import CardQueue2 from "./CardQueue2";
import { toastNotification } from "components/ToastNotification";
import { lightenColor } from "./Home4";
import { FaFileInvoice, FaFileInvoiceDollar } from "react-icons/fa";

const DashboardCards = ({ item }) => {
  return (
    <div className="flex flex-wrap gap-4">
      <Card style={{ minWidth: 100, height: 100 }}>
        <div className="flex flex-col items-center gap-y-1">
          <Typography.Text type="secondary">{item.label}</Typography.Text>

          <span
            className={`rounded-md bg-${item.color} px-2 py-1 text-xs font-semibold`}
          >
            {item.value}
          </span>
          <span className="flex items-center">{item?.icon}</span>
        </div>
      </Card>
    </div>
  );
};

const Home3 = ({ start, end }) => {
  const { user } = useSelector((state) => state.user);

  const [statsClosedTickets, setStatsClosedTickets] = useState({});
  const [t] = useTranslation("common");
  const {
    unreadMsgOneToOne,
    unreadMessagesInGroups,
    missedUnreturnedCalls,
    unreadArchivedMessages,
    stages,
    iconsTasks,
    loading,
    unreadEmail,
    totalEmails,
    missedTodayCall,
    outgoingTodayCall,
    receivedTodayCall,
    totalunreadMessagesTags,
    totalQueues,
    channelsRmc,
    depsRmc,
    statsTasks,
    tasks,
    allQueues,
    totalFamilies,
    selectedMail,
  } = useSelector((state) => state.dashboardRealTime);
  const { openTaskRoomDrawer } = useSelector((state) => state?.TasksRealTime);
  const [cardsTickets, setCardsTickets] = useState([]);
  const [indexTypesTasks, setIndexTypesTasks] = useState({ start: 0, end: 4 });
  const dispatch = useDispatch();

  const [selectedDep, setSelecteDep] = useState("");
  const [roomActivityId, setRoomActivityId] = useState(null);
  const [openTaskAdvanced, setOpenTaskAdvanced] = useState(false);
  useEffect(() => {
    if (start && end) {
      dispatch(
        getDataDashboard({
          start,
          end,
          // departement_id: selectedDep,
          // queue_num: selectedQueue,
        })
      );

      (async () => {
        const res = await generateAxios(
          `${URL_ENV?.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
        ).post(`/get_cardkpi_family`, {
          date_start: start,
          date_end: end,
          lang: i18n.language,
          family_id: 6,
          // field_system_id: 55,
        });
        setCardsTickets(res.data.data);
        const response = await generateAxios(
          `${URL_ENV?.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
        ).post(`/get_sum_kpiclosed_family`, {
          date_start: start,
          date_end: end,
          lang: i18n.language,
          family_id: 6,
          // pipeline_id: selectedPipeline?.value,
          // field_system_id: 55,
        });

        setStatsClosedTickets(response.data);
      })();
    }
  }, [dispatch, start, end, i18n]);
  const { task360 } = useSelector((state) => state.form);

  useEffect(() => {
    if (task360 && Object.keys(task360).length > 0) {
      addTask(task360);

      setTimeout(() => {
        dispatch(setTask360({}));
      }, 100);
    }
  }, [task360]);

  const getStatsRmc = async (value, start, end) => {
    const jour = new Date().getDate();
    const mois = (new Date().getMonth() + 1).toString().padStart(2, "0"); // Notez que les mois commencent à partir de 0 (janvier = 0)
    const annee = new Date().getFullYear();

    const dateRmc = jour + "-" + mois + "-" + annee;
    dispatch({
      type: SET_RMC,
      payload: channelsRmc?.map((el) => ({ ...el, count: "-" })),
    });
    try {
      const { data } = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).get(
        `/dashboard/rmcByDepartment?date_start=${start}&date_end=${end}&department_id=${
          value || ""
        }`
      );
      const updatedChannels = channelsRmc?.map((el) => {
        const apiItem = data?.find((item) => item?.channel === el?.channel);
        if (apiItem) {
          return { ...el, count: apiItem.count };
        }
        if (!apiItem) {
          return { ...el, count: 0 };
        }
        return el;
      });
      dispatch({
        type: SET_RMC,
        payload: updatedChannels,
      });
    } catch (err) {
      toastNotification("error", t("toasts.somethingWrong"));
    }
  };
  const addTask = (task) => {
    dispatch(addTaskInDashboard(task));
    dispatch({
      type: SET_STATS_TASKS_DASHBOARD,
      payload: statsTasks.map((el) =>
        el.id === task360?.tasks_type_id ? { ...el, count: el.count + 1 } : el
      ),
    });
  };
  const onDeleteTask = (task) => {
    dispatch(RemoveTaskInDashboard(task?.id));
    dispatch({
      type: SET_STATS_TASKS_DASHBOARD,
      payload: statsTasks.map((el) =>
        el.id === task?.tasks_type_id ? { ...el, count: el.count - 1 } : el
      ),
    });
  };
  const onUpdateTask = (task) => {
    dispatch(UpdateTasksInDashboard(task));
  };
  const chartDataEmail = useMemo(
    () => ({
      data: [
        {
          name: t(`dashboard.receivedCalls`, {
            plural: selectedMail?.nb_email_received > 1 ? "s" : "",
            pluriel: selectedMail?.nb_email_received > 1 ? "x" : "",
          }),
          y: selectedMail?.nb_email_received,
          color: "oklch(0.792 0.209 151.711)",
        },
        {
          name: t(`dashboard.unreadEmailReceived`, {
            plural: selectedMail?.nb_email_received_unread > 1 ? "s" : "",
            pluriel: selectedMail?.nb_email_received_unread > 1 ? "x" : "",
          }),
          y: selectedMail?.nb_email_received_unread,
          color: "oklch(0.704 0.191 22.216)",
        },
        {
          name: t(`dashboard.totalEmailSent`, {
            plural: selectedMail?.nb_email_sent > 1 ? "s" : "",
            pluriel: selectedMail?.nb_email_sent > 1 ? "x" : "",
          }),
          y: selectedMail?.nb_email_sent,
          color: "oklch(0.707 0.165 254.624)",
        },
      ],
      name: "",
    }),
    [selectedMail, t]
  );
  const gaugeDataDeals = useMemo(
    () => ({
      total: 127,
      unit: t("users.guests"),
      used_storage: 40,
      usage_percentage: 40, // Calcul dynamique si nécessaire
      available: 5,
      title: "",
    }),
    []
  );
  // console.log(s)
  return (
    <div>
      <Row gutter={[8, 8]} className="pb-2">
        <Col span={12}>
          <div className="flex items-center gap-x-2">
            <Card style={{ minWidth: 100, height: 100 }}>
              <div className="flex flex-col items-center gap-y-1">
                <Typography.Text type="secondary">
                  {t(`dashboard.receivedCalls`, {
                    plural: receivedTodayCall > 1 ? "s" : "",
                    pluriel: receivedTodayCall > 1 ? "x" : "",
                  })}
                </Typography.Text>
                <span className="rounded-md bg-[#e0ffe0] px-2 py-1 text-xs font-semibold ">
                  {receivedTodayCall}
                </span>
                <span className="mt-2 flex items-center">
                  <PhoneArrowDownLeftIcon className="h-4 w-4 text-[#3f8600]" />
                </span>
              </div>
            </Card>
            <Card
              style={{
                minWidth: 100,
                height: 100,
              }}
            >
              <div className="flex flex-col items-center gap-y-1">
                <Typography.Text type="secondary">
                  {t(`dashboard.missedCalls`, {
                    plural: missedTodayCall > 1 ? "s" : "",
                    pluriel: missedTodayCall > 1 ? "x" : "",
                  })}
                </Typography.Text>
                <span className="rounded-md bg-[#ffdddf]  px-2 py-1 text-xs font-semibold ">
                  {missedTodayCall}
                </span>
                <span className="mt-2 flex items-center">
                  <MdPhoneMissed className="h-4 w-4 text-[#EF4444]" />
                </span>
              </div>
            </Card>
            <Card style={{ minWidth: 100, height: 100 }}>
              <div className="flex flex-col items-center gap-y-1">
                <Typography.Text type="secondary">
                  {t(`dashboard.outgoingCalls`, {
                    plural: outgoingTodayCall > 1 ? "s" : "",
                    pluriel: outgoingTodayCall > 1 ? "x" : "",
                  })}
                </Typography.Text>

                <span className="rounded-md bg-[#efefff]  px-2 py-1 text-xs font-semibold ">
                  {outgoingTodayCall}
                </span>
                <span className="mt-2 flex items-center">
                  <PhoneArrowUpRightIcon className="h-4 w-4 text-[#1677ff] " />
                </span>
              </div>
            </Card>
            <Card style={{ minWidth: 100, height: 100 }}>
              <div className="flex flex-col items-center gap-y-1">
                <Typography.Text type="secondary">
                  {t(`dashboard.unreturnedMissedCalls`, {
                    plural: missedUnreturnedCalls > 1 ? "s" : "",
                    pluriel: missedUnreturnedCalls > 1 ? "x" : "",
                  })}
                </Typography.Text>

                <span className="rounded-md bg-blue-100  px-2 py-1 text-xs font-semibold">
                  {missedUnreturnedCalls}
                </span>
                <span className="mt-2 flex items-center">
                  <FcCallback className="h-4 w-4 " />
                </span>
              </div>
            </Card>
            {/* <Card
              title={t("users.guests")}
              styles={{
                header: { padding: "4px 24px", minHeight: "auto" },
              }}
            > */}
            <GaugeChart data={gaugeDataDeals} height={100} size={"190%"} />
            {/* </Card> */}
          </div>
        </Col>
        <Col span={12}>
          <div className="flex items-center justify-between gap-2">
            {cardsTickets.map((el) => (
              <span className="flex items-end gap-x-2">
                <Card style={{ minWidth: 100, height: 100 }}>
                  <div className="flex flex-col items-center gap-y-1">
                    <Typography.Text
                      type="secondary"
                      style={{ width: "max-content" }}
                    >
                      {el?.name}
                    </Typography.Text>

                    <span
                      style={{ background: lightenColor(el.color, 0.3) }}
                      className="rounded-md  px-2 py-1 text-xs font-semibold "
                    >
                      {el?.value}
                    </span>
                    <span className="flex items-center">
                      <Avatar
                        src={
                          <LucideIcon
                            iconName={el?.icon}
                            color={el?.color}
                            size={16}
                          />
                        }
                        //   className="bg-gray-100"
                      />{" "}
                    </span>
                  </div>
                </Card>
              </span>
            ))}
            <Card>
              <div className="flex flex-col gap-y-5 pr-1">
                <Typography.Title level={3}>Revenue</Typography.Title>
                <span className="flex items-center gap-x-3">
                  <div className="text-xl font-semibold">$528,976.82</div>

                  <span className="flex items-center">
                    <ArrowUp size={16} /> 7.9%
                  </span>
                  <span className="rounded-md bg-pink-500 px-2 py-1 text-xs font-semibold text-white">
                    $27.335
                  </span>
                </span>
              </div>
            </Card>
          </div>
        </Col>
      </Row>
      <Row gutter={[8, 8]}>
        <Col span={12}>
          <Row gutter={[8, 8]}>
            <Col span={12}>
              {Array.isArray(user?.accounts_email) &&
              user?.accounts_email?.length > 0 ? (
                <Card
                  title={
                    <div className="flex items-center justify-between">
                      <span>Emails</span>
                      <span className="truncate">
                        <Select
                          size="small"
                          showSearch
                          defaultValue={totalEmails[0]?.accountId}
                          placeholder=""
                          optionFilterProp="children"
                          popupMatchSelectWidth={false}
                          options={
                            Array.isArray(totalEmails) &&
                            totalEmails.length > 0 &&
                            totalEmails?.map((el) => ({
                              ...el,
                              value: el?.accountId,
                              label: el?.email,
                            }))
                          }
                          filterOption={(input, option) =>
                            (option?.label ?? "")
                              .toLowerCase()
                              .includes(input.toLowerCase())
                          }
                          value={selectedMail?.accountId}
                          onChange={(value, values) => {
                            dispatch(setSelectedMail(values));
                            // setUnreadReceivedEmailByAccount(
                            //   values?.nb_email_received_unread
                            // );
                            // setReceivedEmailByAccount(
                            //   values?.nb_email_received
                            // );
                            // setTotalSentEmailByAccount(values?.nb_email_sent);
                          }}
                        />
                      </span>
                    </div>
                  }
                  styles={{
                    header: { padding: "4px 24px", minHeight: "auto" },
                  }}
                >
                  <OneBarChart data={chartDataEmail} height={172} />
                </Card>
              ) : null}

              {/* <Card
                style={{
                  backgroundImage: "linear-gradient(to right, #F4F8F9,#F8F2F9)",
                }}
                title={t("menu1.voip")}
                styles={{ header: { padding: "4px 24px", minHeight: "auto" } }}
              >
                <Row gutter={[4, 4]} className="">
                  <Col span={24}>
                    <div className="flex items-center justify-between rounded-md bg-white px-2 py-1 ">
                      <div className="flex items-center gap-x-2">
                        <Avatar
                          src={
                            <PhoneArrowDownLeftIcon className="h-4 w-4 text-[#3f8600]" />
                          }
                          //   className="bg-gray-100"
                        />
                        <Typography.Text type="secondary">
                          {t(`dashboard.receivedCalls`, {
                            plural: receivedTodayCall > 1 ? "s" : "",
                            pluriel: receivedTodayCall > 1 ? "x" : "",
                          })}
                        </Typography.Text>
                      </div>
                      <span className="text-base font-semibold">
                        {receivedTodayCall}
                      </span>
                    </div>
                  </Col>
                  <Col span={24}>
                    <div className="flex items-center justify-between rounded-md bg-white px-2 py-1 ">
                      <div className="flex items-center gap-x-2">
                        <Avatar
                          src={
                            <MdPhoneMissed className="h-4 w-4 text-[#EF4444]" />
                          }
                          //   className="bg-gray-100"
                        />
                        <Typography.Text type="secondary">
                          {t(`dashboard.missedCalls`, {
                            plural: missedTodayCall > 1 ? "s" : "",
                            pluriel: missedTodayCall > 1 ? "x" : "",
                          })}
                        </Typography.Text>
                      </div>
                      <span className="text-base font-semibold">
                        {missedTodayCall}
                      </span>
                    </div>
                  </Col>
                  <Col span={24}>
                    <div className="flex items-center justify-between rounded-md bg-white px-2 py-1 ">
                      <div className="flex items-center gap-x-2">
                        <Avatar
                          src={
                            <PhoneArrowUpRightIcon className="h-4 w-4 text-[#1677ff] " />
                          }
                          //   className="bg-gray-100"
                        />
                        <Typography.Text type="secondary">
                          {t(`dashboard.outgoingCalls`, {
                            plural: outgoingTodayCall > 1 ? "s" : "",
                            pluriel: outgoingTodayCall > 1 ? "x" : "",
                          })}
                        </Typography.Text>
                      </div>
                      <span className="text-base font-semibold">
                        {outgoingTodayCall}
                      </span>
                    </div>
                  </Col>

                  <Col span={24}>
                    <div className="flex items-center justify-between rounded-md bg-white px-2 py-1 ">
                      <div className="flex items-center gap-x-2">
                        <Avatar
                          src={<FcCallback className="h-4 w-4 " />}
                          //   className="bg-gray-100"
                        />
                        <Typography.Text type="secondary">
                          {t(`dashboard.unreturnedMissedCalls`, {
                            plural: missedUnreturnedCalls > 1 ? "s" : "",
                            pluriel: missedUnreturnedCalls > 1 ? "x" : "",
                          })}
                        </Typography.Text>
                      </div>
                      <span className="text-base font-semibold">
                        {missedUnreturnedCalls}
                      </span>
                    </div>
                  </Col>
                </Row>
              </Card> */}
            </Col>
            <Col span={12}>
              <Row gutter={[4, 4]}>
                <Card
                  style={{
                    backgroundImage:
                      "linear-gradient(to right, #F4F8F9,#F8F2F9)",
                  }}
                  title={t("menu1.chat")}
                  styles={{
                    header: { padding: "4px 24px", minHeight: "auto" },
                  }}
                >
                  <Row gutter={[4, 4]} className="">
                    <Col span={24}>
                      <div className="flex items-center justify-between rounded-md bg-white px-2 py-1 ">
                        <div className="flex items-center gap-x-2">
                          <Avatar
                            src={
                              <ChatBubbleLeftRightIcon className="h-4 w-4 text-black" />
                            }
                            // className="bg-gray-100"
                          />
                          <Typography.Text type="secondary">
                            {t(`dashboard.unreadmsgOtoO`, {
                              plural: unreadMsgOneToOne > 1 ? "s" : "",
                              pluriel: unreadMsgOneToOne > 1 ? "x" : "",
                            })}
                          </Typography.Text>
                        </div>
                        <span className="text-base font-semibold">
                          {unreadMsgOneToOne}
                        </span>
                      </div>
                    </Col>
                    <Col span={24}>
                      <div className="flex items-center justify-between rounded-md bg-white px-2 py-1 ">
                        <div className="flex items-center gap-x-2">
                          <Avatar
                            src={
                              <ChatBubbleBottomCenterTextIcon className="h-4 w-4" />
                            }
                            className=" text-black"
                          />
                          <Typography.Text type="secondary">
                            {t(`dashboard.unreadmsgtoG`, {
                              plural: unreadMessagesInGroups > 1 ? "s" : "",
                              pluriel: unreadMessagesInGroups > 1 ? "x" : "",
                            })}
                          </Typography.Text>
                        </div>
                        <span className="text-base font-semibold">
                          {unreadMessagesInGroups}
                        </span>
                      </div>
                    </Col>
                    <Col span={24}>
                      <div className="flex items-center justify-between rounded-md bg-white px-2 py-1 ">
                        <div className="flex items-center gap-x-2">
                          <Avatar
                            src={
                              <ChatBubbleBottomCenterTextIcon className="h-4 w-4 text-black" />
                            }
                            // className="bg-gray-100"
                          />
                          <Typography.Text type="secondary">
                            {t(`dashboard.unreadArchivedMessages`, {
                              plural: unreadArchivedMessages > 1 ? "s" : "",
                              pluriel: unreadArchivedMessages > 1 ? "x" : "",
                            })}
                          </Typography.Text>
                        </div>
                        <span className="text-base font-semibold">
                          {unreadArchivedMessages}
                        </span>
                      </div>
                    </Col>
                    <Col span={24}>
                      <div className="flex items-center justify-between rounded-md bg-white px-2 py-1 ">
                        <div className="flex items-center gap-x-2">
                          <Avatar
                            src={
                              <AtSymbolIcon className="h-4 w-4 text-black" />
                            }
                            // className="bg-gray-100"
                          />
                          <Typography.Text type="secondary">
                            {t(`dashboard.unreadMention`, {
                              plural: totalunreadMessagesTags > 1 ? "s" : "",
                              pluriel: totalunreadMessagesTags > 1 ? "x" : "",
                            })}
                          </Typography.Text>
                        </div>
                        <span className="text-base font-semibold">
                          {totalunreadMessagesTags}
                        </span>
                      </div>
                    </Col>
                  </Row>
                </Card>
              </Row>
            </Col>
          </Row>
        </Col>
        <Col span={12}>
          <Row gutter={[8, 8]}>
            <Col span={12}>
              <Card
                styles={{
                  header: { padding: "4px 24px", minHeight: "auto" },
                }}
                title={"Tickets " + t("vue360.resolved").toLowerCase()}
              >
                <GaugeChart
                  data={{
                    total: statsClosedTickets?.data?.total,
                    used_storage: statsClosedTickets?.data?.used,
                    title: statsClosedTickets?.data?.name,
                    unit: statsClosedTickets?.data?.name,
                  }}
                  title=""
                  height={172}
                  name={statsClosedTickets?.name}
                  inverseColor={true}
                  size={"170%"}
                />
              </Card>
            </Col>

            <Col span={12}>
              <Card
                title={t("menu1.deals")}
                styles={{
                  header: { padding: "4px 24px", minHeight: "auto" },
                }}
              >
                <PieChart
                  data={useMemo(
                    () => [
                      {
                        name: "Won",
                        y: 50,
                        color: "oklch(0.792 0.209 151.711)",
                      },
                      {
                        name: "Lost",
                        y: 20,
                        color: "oklch(0.704 0.191 22.216)",
                      },
                      {
                        name: "In progress",
                        y: 70,
                        color: "oklch(0.707 0.165 254.624)",
                      },
                    ],
                    []
                  )}
                  total={140}
                  name=""
                  height={172}
                />
              </Card>
            </Col>
          </Row>
        </Col>
        <Col span={12}>
          <Row gutter={[8, 8]}>
            <Col span={12}>
              <Card
                style={{
                  backgroundImage: "linear-gradient(to right, #F4F8F9,#F8F2F9)",
                  // height: "352px",
                }}
                title={
                  <div className="flex items-center gap-x-1">
                    <span>{t("tasks.activitytypes")}</span>{" "}
                    <Badge
                      count={statsTasks?.length}
                      style={{
                        backgroundColor: "#52c41a",
                      }}
                    />
                  </div>
                }
                styles={{ header: { padding: "4px 24px", minHeight: "auto" } }}
                extra={
                  <div className="flex items-center gap-x-1">
                    <span>
                      {indexTypesTasks?.start +
                        1 +
                        "-" +
                        (indexTypesTasks.end >= statsTasks.length
                          ? statsTasks.length
                          : indexTypesTasks.end) +
                        " " +
                        t("mailing.of") +
                        " " +
                        statsTasks?.length}
                    </span>

                    <Tooltip title={t("dashboard.prevTasksTypes")}>
                      <Button
                        size="small"
                        icon={<ChevronLeft size={16} />}
                        type="link"
                        style={{
                          visibility:
                            indexTypesTasks.start === 0 ? "hidden" : "visible",
                        }}
                        onClick={() =>
                          setIndexTypesTasks({
                            start: indexTypesTasks.start - 4,
                            end: indexTypesTasks.end - 4,
                          })
                        }
                      />
                    </Tooltip>

                    <Tooltip title={t("dashboard.nextTasksTypes")}>
                      <Button
                        icon={<ChevronRight size={16} />}
                        size="small"
                        type="link"
                        style={{
                          visibility:
                            indexTypesTasks.end >= statsTasks.length
                              ? "hidden"
                              : "visible",
                        }}
                        onClick={() =>
                          setIndexTypesTasks({
                            start: indexTypesTasks.start + 4,
                            end: indexTypesTasks.end + 4,
                          })
                        }
                      />
                    </Tooltip>
                  </div>
                }
              >
                <Row gutter={[4, 4]}>
                  {statsTasks
                    .slice(indexTypesTasks.start, indexTypesTasks.end)
                    .map((stat, i) => (
                      <Col span={24}>
                        <div className="flex items-center justify-between rounded-md bg-white px-2 py-1 ">
                          <div className="flex items-center gap-x-2">
                            <Avatar style={{ backgroundColor: "transparent" }}>
                              <ChoiceIcons
                                icon={stat.icons}
                                fontSize={"16px"}
                                color={stat?.color || "black"}
                              />
                            </Avatar>
                            <Typography.Text type="secondary">
                              {stat?.label?.length > 12
                                ? `${stat?.label?.slice(0, 12)}...`
                                : stat?.label}
                            </Typography.Text>
                          </div>
                          <span className="text-base font-semibold">
                            {stat.count}
                          </span>
                        </div>
                      </Col>
                    ))}
                </Row>
              </Card>
            </Col>

            <Col span={12}>
              <Row gutter={[4, 4]}>
                <Card
                  style={{
                    backgroundImage:
                      "linear-gradient(to right, #F4F8F9,#F8F2F9)",
                  }}
                  title={
                    <div className="flex items-center justify-between ">
                      <span>{t("dashboard.socialMedia")}</span>{" "}
                      <Select
                        showSearch
                        size="small"
                        optionFilterProp="children"
                        popupMatchSelectWidth={false}
                        filterOption={(input, option) =>
                          (option?.label2 ?? "")
                            .toLowerCase()
                            .includes(input.toLowerCase())
                        }
                        options={[
                          {
                            value: "",
                            label2: t("helpDesk.all"),
                            label: (
                              <div className="flex items-center justify-between">
                                <span>{t("helpDesk.all")}</span>
                                <Tooltip title={t("dashboard.relativeFigures")}>
                                  <InfoCircleOutlined />
                                </Tooltip>
                              </div>
                            ),
                          },
                          ...depsRmc?.map((dep) => ({
                            value: dep.department_id,
                            label: dep.name,
                            label2: dep.name,
                            color: dep.color,
                          })),
                        ]}
                        style={{ minWidth: 100, width: "min-content" }}
                        value={selectedDep}
                        onChange={(value, values) => {
                          setSelecteDep(value);
                          getStatsRmc(value, start, end);
                        }}
                      />{" "}
                    </div>
                  }
                  styles={{
                    header: { padding: "4px 24px", minHeight: "auto" },
                  }}
                >
                  <Row gutter={[4, 4]} className="">
                    {channelsRmc.map((item) => (
                      <Col span={24}>
                        <div className="flex items-center justify-between rounded-md bg-white px-2 py-1 ">
                          <div className="flex items-center gap-x-2">
                            <Avatar src={item.icon} style={{ fontSize: 22 }} />
                            <Typography.Text type="secondary">
                              {t(`dashboard.${item?.channel}`)}
                            </Typography.Text>
                          </div>
                          <span className="text-base font-semibold">
                            {item.count}
                          </span>
                        </div>
                      </Col>
                    ))}
                  </Row>
                </Card>
              </Row>
            </Col>
          </Row>
        </Col>
        <Col span={12}>
          <Row gutter={[8, 8]}>
            <Col span={12}>
              <Card
                style={{
                  backgroundImage: "linear-gradient(to right, #F4F8F9,#F8F2F9)",
                }}
                title={t("menu1.invoices")}
                styles={{
                  header: { padding: "4px 24px", minHeight: "auto" },
                }}
              >
                <Row gutter={[4, 4]} className="">
                  <Col span={24}>
                    <div className="flex items-center justify-between rounded-md bg-white px-2 py-1 ">
                      <div className="flex items-center gap-x-2">
                        <Avatar
                          src={<FaFileInvoice className="h-4 w-4 text-black" />}
                        />
                        <Typography.Text type="secondary">
                          Total invoices
                        </Typography.Text>
                      </div>
                      <span className="text-base font-semibold">50</span>
                    </div>
                  </Col>
                  <Col span={24}>
                    <div className="flex items-center justify-between rounded-md bg-white px-2 py-1 ">
                      <div className="flex items-center gap-x-2">
                        <Avatar
                          src={<FaFileInvoiceDollar className="h-4 w-4" />}
                          className="text-green-500"
                        />
                        <Typography.Text type="secondary">
                          Paid invoices
                        </Typography.Text>
                      </div>
                      <span className="text-base font-semibold">20 </span>
                    </div>
                  </Col>
                  <Col span={24}>
                    <div className="flex items-center justify-between rounded-md bg-white px-2 py-1 ">
                      <div className="flex items-center gap-x-2">
                        <Avatar
                          src={
                            <FaFileInvoiceDollar className="h-4 w-4 text-orange-500" />
                          }
                        />
                        <Typography.Text type="secondary">
                          Partially paid invoices
                        </Typography.Text>
                      </div>
                      <span className="text-base font-semibold">10 </span>
                    </div>
                  </Col>
                  <Col span={24}>
                    <div className="flex items-center justify-between rounded-md bg-white px-2 py-1 ">
                      <div className="flex items-center gap-x-2">
                        <Avatar
                          src={
                            <FaFileInvoiceDollar className="h-4 w-4 text-red-500" />
                          }
                        />
                        <Typography.Text type="secondary">
                          Unpaid invoices
                        </Typography.Text>
                      </div>
                      <span className="text-base font-semibold">20 </span>
                    </div>
                  </Col>
                </Row>
              </Card>
            </Col>
            <Col span={12}>
              <CardQueue2 source="home3" start={start} end={end} />
            </Col>
          </Row>
        </Col>

        {/* <Col span={24}>
          <div className="flex flex-wrap items-center gap-2">
            {statsTasks.map((stat, i) => (
              <Tag
                icon={<ChoiceIcons icon={stat.icons} fontSize={"12px"} />}
                color={stat.color}
              >
                <span className="w-max pl-1">
                  {stat?.label?.length > 12
                    ? `${stat?.label?.slice(0, 12)}...`
                    : stat?.label}
                </span>
                {"   "}
                {stat?.count}
              </Tag>
            ))}
          </div>
        </Col> */}
        {/* <Col span={4}>
          <Card
            style={{
              //   backgroundImage: "linear-gradient(to right, #F4F8F9,#F8F2F9)",
              height: "352px",
            }}
            title={t("tasks.activitytypes")}
            styles={{ header: { padding: "4px 24px", minHeight: "auto" } }}
          >
            <Row gutter={[4, 4]} className="h-[305px] overflow-auto">
              {statsTasks.map((stat, i) => (
                <Col span={24}>
                  <div
                    className="flex items-center justify-between rounded-md  px-2 py-1 "
                    style={{ color: stat?.color }}
                  >
                    <div className="flex items-center gap-x-2">
                      <ChoiceIcons icon={stat.icons} fontSize={"16px"} />
                      <Typography.Text type="secondary">
                        {stat?.label?.length > 12
                          ? `${stat?.label?.slice(0, 12)}...`
                          : stat?.label}
                      </Typography.Text>
                    </div>
                    <span className="text-base font-semibold">
                      {stat?.count}{" "}
                    </span>
                  </div>
                </Col>
              ))}
            </Row>
          </Card>
        </Col> */}
        <Col span={10}>
          <Card
            style={{
              backgroundImage: "linear-gradient(to right, #F4F8F9,#F8F2F9)",
            }}
            styles={{
              header: { padding: "4px 24px", minHeight: "auto" },
            }}
            title={t("menu1.tasks")}
            extra={
              Array.isArray(tasks) && tasks.length > 2 ? (
                <Button
                  size="small"
                  onClick={() => setOpenTaskAdvanced(true)}
                  type="primary"
                  icon={<PlusOutlined />}
                >
                  {t("tasks.addQuickTask")}
                </Button>
              ) : null
            }
          >
            {Array.isArray(tasks) && tasks.length > 0 ? (
              <div className="relative">
                <ListTasks
                  list={tasks}
                  setRoomActivityId={setRoomActivityId}
                  roomActivityId={roomActivityId}
                  onDeleteTask={onDeleteTask}
                  onUpdateTask={onUpdateTask}
                />
                {tasks.length < 2 ? (
                  <div className="absolute bottom-0 left-0 right-0 flex justify-center">
                    <EmptyPage
                      heroTitle={
                        <span>
                          {t("tasks.startCreateActivities")}{" "}
                          <Typography.Link>
                            {t("tasks.activities")}
                          </Typography.Link>
                        </span>
                      }
                      mainBtnTitle={t("tasks.addQuickTask")}
                      handleMainBtnClick={() => setOpenTaskAdvanced(true)}
                    />
                  </div>
                ) : tasks.length < 2 ? (
                  <div className="absolute bottom-0 left-0 right-0 flex justify-center">
                    <EmptyPage
                      heroTitle={
                        <span>
                          {t("tasks.startCreateActivities")}{" "}
                          <Typography.Link>
                            {t("tasks.activities")}
                          </Typography.Link>
                        </span>
                      }
                      mainBtnTitle={t("tasks.addQuickTask")}
                      handleMainBtnClick={() => setOpenTaskAdvanced(true)}
                    />
                  </div>
                ) : tasks.length === 1 ? (
                  <div className="absolute bottom-0 left-0 right-0 flex justify-center">
                    <EmptyPage
                      heroTitle={
                        <span>
                          {t("tasks.startCreateActivities")}{" "}
                          <Typography.Link>
                            {t("tasks.activities")}
                          </Typography.Link>
                        </span>
                      }
                      mainBtnTitle={t("tasks.addQuickTask")}
                      handleMainBtnClick={() => setOpenTaskAdvanced(true)}
                    />
                  </div>
                ) : tasks.length === 2 ? (
                  <div className="absolute bottom-0 left-0 right-0 flex justify-center">
                    <Button
                      icon={<PlusOutlined />}
                      type="primary"
                      onClick={() => setOpenTaskAdvanced(true)}
                    >
                      {t("tasks.addQuickTask")}
                    </Button>
                  </div>
                ) : null}
              </div>
            ) : (
              <div className="h-[300px]">
                <EmptyPage
                  heroTitle={
                    <span>
                      {t("tasks.startCreateActivities")}{" "}
                      <Typography.Link>{t("tasks.activities")}</Typography.Link>
                    </span>
                  }
                  mainBtnTitle={t("tasks.addQuickTask")}
                  handleMainBtnClick={() => setOpenTaskAdvanced(true)}
                />{" "}
              </div>
            )}
          </Card>
        </Col>
        {/* {totalQueues && Array.isArray(totalQueues) && totalQueues.length > 0 ? (
          <Col className="gutter-row" span={10}>
            <Card
              title={t("dashboard.queue")}
              styles={{
                header: { padding: "4px 24px", minHeight: "auto" },
              }}
              extra={
                <Select
                  size="small"
                  showSearch
                  // defaultValue={totalQueues[0]?.queue_num}
                  popupMatchSelectWidth={false}
                  placeholder=""
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    (option?.label ?? "")
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  options={allQueues}
                  value={totalQueues[0]?.queue_num}
                  onChange={(value, values) => {
                    dispatch(
                      setQueueInDashboard({
                        startDate: start,
                        endDate: end,
                        queue_num: value,
                      })
                    );
                    // setSelectedQueue(value);
                    // setSelectedQueue({
                    //   ...values,
                    // });
                  }}
                />
              }
            >
              <StatsChart
                data={{
                  categories: [
                    t("dashboard.receivedCalls"),
                    t("dashboard.answered"),
                    t("dashboard.missedCalls"),
                    t("dashboard.lostNotRecalled"),
                  ],
                  data: [
                    {
                      y: Number(totalQueues[0]?.total_calls),
                      color: "#fb923c",
                    },
                    {
                      y: Number(totalQueues[0]?.answered_calls),
                      color: "#16a34a",
                    },
                    {
                      y: Number(totalQueues[0]?.no_answered_calls),
                      color: "#ef4444",
                    },
                    {
                      y: Number(totalQueues[0]?.no_answered_calls_not_returned),
                      color: "#4ade80",
                    },
                  ],
                  text: `Average communication duration :  ${
                    totalQueues[0]?.moyen_treatement_calls
                      ? convertToHour(totalQueues[0]?.moyen_treatement_calls)
                      : "-"
                  }  <br> Average waiting time : ${
                    totalQueues[0]?.moyen_ringing_calls
                      ? convertToHour(totalQueues[0]?.moyen_ringing_calls)
                      : "-"
                  } `,
                  name: "",
                }}
                height={300}
              />
            </Card>
          </Col>
        ) : null} */}
        <Col span={14}>
          <Card
            styles={{
              header: { padding: "4px 24px", minHeight: "auto" },
            }}
            title={t("menu2.family")}
          >
            <Chart2Bars
              data={useMemo(
                () => ({
                  categories: totalFamilies.map(
                    (el) =>
                      familyIcons(t).find((fam) => fam.key === el.family_id)
                        ?.label
                  ),
                  series: [
                    {
                      name: t("dashboard.totalElements", { plural: "s" }),
                      data: totalFamilies.map((el) => el.total_elements),
                    },
                    {
                      name: t("dashboard.createdByYou", { plural: "s" }),
                      data: totalFamilies.map((el) => el.created_by_you),
                    },
                  ],
                  name: "",
                }),
                [totalFamilies, start, end]
              )}
              height={300}
            />
          </Card>
        </Col>
      </Row>
      {openTaskRoomDrawer ? (
        <TasksRoom key={roomActivityId} elementId={roomActivityId} />
      ) : null}
      <CreateTask
        open={openTaskAdvanced}
        setOpen={setOpenTaskAdvanced}
        mask={true}
        listVisio={false}
        fromVue360={false}
      />
    </div>
  );
};

export default Home3;
