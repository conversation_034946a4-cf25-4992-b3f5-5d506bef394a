import {
  DeleteOutlined,
  EditOutlined,
  InfoCircleOutlined,
  MessageOutlined,
  PlayCircleOutlined,
  RestOutlined,
} from "@ant-design/icons";
import { Dropdown } from "antd";
import {
  setDetailsMeet,
  setDetailsMeetExternal,
  setHistory,
  setKeyMeet,
  setLater,
  setListMeet,
  setNow,
} from "../../new-redux/actions/visio.actions/visio";
import { FiMoreVertical } from "react-icons/fi";
import { useDispatch } from "react-redux";
import { setOpenTaskRoomDrawer } from "../../new-redux/actions/tasks.actions/handleTaskRoomDrawer";
import MainService from "../../services/main.service";
import {
  resetStateOtherUser,
  setChatSelectedConversation,
  setChatSelectedParticipants,
} from "../../new-redux/actions/chat.actions";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { toastNotification } from "../../components/ToastNotification";
import { useSelector } from "react-redux";
import { getTokenRoom } from "../../new-redux/actions/visio.actions/createVisio";
import { useState } from "react";
import Confirm from "../../components/GenericModal";

const DropdownVisio = ({
  dataExternal,
  setExterneUpdate,
  setIdTask,
  setOpenDrawerMsg,
}) => {
  const dispatch = useDispatch();
  const [t] = useTranslation("common");
  const [open, setOpen] = useState(false);
  const { isOpen } = useSelector((state) => state.visio);
  const {
    keyMeet,
    now,
    later,
    listMeet,
    countToday,
    countUpComing,
    countHistory,
  } = useSelector((state) => state.visioList);
  const { user } = useSelector((state) => state.user);

  const openChat = async (id) => {
    // MainService.createRoomTask({
    //   task_id: id,{}
    // })
    setOpenDrawerMsg(true);
    dispatch(setOpenTaskRoomDrawer(true));

    const {
      data: { data: singleTaskData },
    } = await MainService.getSpecificTask(id);

    // console.log("CONTENT OF THE TASK", content);
    let usr_ids = ",";
    let usr_ids_arr = [];

    let guestsIds =
      Object.keys(singleTaskData).length > 0 &&
      singleTaskData?.guests &&
      Object.keys(singleTaskData).length > 0 &&
      singleTaskData?.guests.map((guest) => (guest?.uuid ? guest?.uuid : null));
    let followersIds =
      Object.keys(singleTaskData).length > 0 &&
      singleTaskData?.followers &&
      Object.keys(singleTaskData).length > 0 &&
      singleTaskData?.followers.map((follower) =>
        follower?.uuid ? follower?.uuid : null
      );

    usr_ids_arr = [
      ...guestsIds,
      ...followersIds,
      singleTaskData?.creator?.id !== singleTaskData?.owner_id?.id
        ? singleTaskData?.owner_id?.uuid
        : null,
    ];

    //get user_ids from followers and guests if they had uuid
    singleTaskData?.followers?.forEach((follower) => {
      if (!Array.isArray(follower?.uuid)) {
        usr_ids_arr.push(follower?.uuid);
      }
    });

    singleTaskData?.guests?.forEach((guest) => {
      if (!Array.isArray(guest?.uuid)) {
        usr_ids_arr.push(guest?.uuid);
      }
    });

    //remove duplicates
    usr_ids_arr = [...new Set(usr_ids_arr)]
      .filter((id) => id !== null)
      .filter((el) => el !== singleTaskData?.creator?.uuid);

    await MainService.createRoomTask({
      relation_id: id,
      name: singleTaskData?.label,
      description: singleTaskData?.label,
      admin_id: singleTaskData?.creator?.uuid,
      users_ids: usr_ids_arr?.toString(),
    })
      .then((res) => {
        if (!res?.data?.message) {
          MainService.assignRoomToTask(id, {
            room_id: res?.data?.room?._id,
          })
            .then((res) => {
              // console.log("ASSIGN ROOM TO TASK", res);
            })
            .catch((err) => {
              console.log("ASSIGN ROOM TO TASK ERROR", err);
            });
        }

        dispatch(
          setChatSelectedConversation({
            selectedConversation: {
              name: res?.data?.room?.name,
              description: res?.data?.room?.description,
              image: res?.data?.room?.image,
              admin_id: res?.data?.room?.admin_id,
              bot: null,
              id: res?.data?.room?._id,
              type: "room",
              muted_status: false,
              source: "visio",

              conversationId: res?.data?.room?.conversation_id,
              external: false,
            },
          })
        );
        dispatch(
          resetStateOtherUser({
            forced: true,
            keepDrawerOpened: false,
            item: null,
          })
        );
        dispatch(
          setChatSelectedParticipants({
            selectedParticipants: res?.data?.room?.participants ?? [],
          })
        );
      })
      .catch((err) => {
        console.log("ROOM TASK ERROR", err);
      });
  };
  const handleDelete = async (id, start_date) => {
    try {
      let formData = new FormData();
      formData.append("id[]", id);
      const response = await MainService.deleteSpecificTask(formData);
      if (response?.status === 200) {
        dispatch(setListMeet(listMeet.filter((el) => el.id !== id)));
        const currentDate = moment().format("YYYY-MM-DD");
        const startDate = moment(start_date, "YYYY-MM-DD");
        if (start_date === currentDate) {
          dispatch(setNow({ now: now, countToday: countToday - 1 }));
          // dispatch(setLastPage(Math.ceil((countToday - 1) / 10)));
        }

        if (startDate.isAfter(currentDate, "day")) {
          dispatch(
            setLater({ later: later, countUpComing: countUpComing - 1 })
          );
          // dispatch(setLastPage(Math.ceil(countUpComing - 1 / 10)));
        }

        if (startDate.isBefore(currentDate, "day")) {
          dispatch(setHistory({ countHistory: countHistory - 1 }));
          // dispatch(setLastPage(Math.ceil(countUpComing - 1 / 10)));
        }
        if (id === keyMeet) dispatch(setDetailsMeet({}));

        toastNotification("success", t("toasts.taskDeleted"), "bottomRight");
      }
    } catch (error) {
      console.log(`Error ${error}`);
      toastNotification("error", t("toasts.somethingWrong"));
    }
  };
  const handleClick = () => {
    dispatch(setKeyMeet(dataExternal?.id));
    // dispatch(getDetailsMeet({ keyMeet: id, t }));
    dispatch(setDetailsMeet(dataExternal));
    // if (newMeet) {
    //   const currentDate = moment().format("YYYY-MM-DD");
    //   const startDate = moment(start_date, "YYYY-MM-DD");
    //   dispatch(
    //     setListMeet(
    //       listMeet.map((el) =>
    //         el.id === id ? { ...el, newMeet: false } : el
    //       )
    //     )
    //   );
    //   if (start_date === currentDate && now > 0) {
    //     dispatch(setNow({ now: now - 1, countToday: countToday }));
    //   }

    //   if (startDate.isAfter(currentDate, "day") && later > 0) {
    //     dispatch(
    //       setLater({ later: later - 1, countUpComing: countUpComing })
    //     );
    //   }
    // }
  };
  const items = [
    {
      label: t("visio.participate"),
      key: "1",
      icon: <PlayCircleOutlined />,
    },
    {
      label: t("table.edit"),
      key: "2",
      icon: <EditOutlined />,
    },
    {
      label: t("menu1.chat"),
      key: "3",
      icon: <MessageOutlined />,
      disabled:
        (dataExternal?.guests?.length === 0 ||
          dataExternal?.guests?.filter((el) => el.uuid !== null)?.length ===
            0) &&
        (dataExternal?.followers?.length === 0 ||
          dataExternal?.followers?.filter((el) => el.uuid !== null)?.length ===
            0),
    },
    user.id === dataExternal.owner_id.id || user.id === dataExternal.creator.id
      ? {
          label: t("table.delete"),
          danger: true,
          key: "4",
          icon: <DeleteOutlined />,
        }
      : "",
    {
      type: "divider",
    },

    { label: t("voip.moreInfo"), key: "5", icon: <InfoCircleOutlined /> },
  ];
  return (
    <div className="r-8">
      <Dropdown
        trigger={["click"]}
        placement="bottomLeft"
        open={open}
        destroyPopupOnHide
        autoAdjustOverflow
        onOpenChange={(e) => {
          setOpen(e);
          e
            ? dispatch(setDetailsMeetExternal(dataExternal))
            : dispatch(setDetailsMeetExternal({}));
        }}
        getPopupContainer={(triggerNode) => triggerNode.parentNode}
        arrow
        menu={{
          items,
          onClick: (e) => {
            e.domEvent.stopPropagation();
            setOpen(false);

            if (e.key === "1") {
              dispatch(
                getTokenRoom({
                  room: dataExternal.location,
                  errorText1: t("toasts.errorFetchApi"),
                  errorText2: t("toasts.errorRoomNotFound"),
                })
              );
            }
            if (e.key === "2") {
              if (dataExternal.id) {
                setExterneUpdate(true);

                setIdTask(dataExternal?.id);
              }
            }
            if (e.key === "3") {
              openChat(dataExternal.id);
            }

            if (e.key === "4") {
              Confirm(
                `Delete "${dataExternal.label}" `,
                "Confirm",
                <RestOutlined style={{ color: "red" }} />,
                function func() {
                  return handleDelete(dataExternal.id, dataExternal.start_date);
                },
                true
              );
            }
            if (e.key === "5") {
              handleClick();
            }
          },
        }}
      >
        <FiMoreVertical
          className="h-4 w-4 cursor-pointer text-gray-400 hover:text-gray-700"
          onClick={(e) => {
            // setOpen(!open);
            e.stopPropagation();
          }}
        />
      </Dropdown>
    </div>
  );
};

export default DropdownVisio;
