import { useState, useEffect, useCallback, useRef } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import PivotTableUI from "react-pivottable/PivotTableUI";
import "react-pivottable/pivottable.css";
import "./pivottable.css";
import { PivotData, aggregators } from "react-pivottable/Utilities";
import {
  Card,
  Select,
  Form,
  Button,
  Empty,
  Tabs,
  Spin,
  Input,
  Checkbox,
  Tooltip,
  DatePicker,
  Dropdown,
  Menu,
} from "antd";
import TableRenderers from "react-pivottable/TableRenderers";
import { generateAxios } from "services/axiosInstance";
import { URL_ENV } from "index";
import Variwide from "./Variwide";
import Columendrilldown from "./Columendrilldown";
import ColumnNegative from "./ColumnNegative";
import BarNegative from "./BarNegative";
import ColumnStackedd from "./ColumnStackedd";
import LineChart from "./LineChart";
import Spline from "./Spline";
import AreaChart from "./AreaChart";
import DualAxes from "./DualAxes";
import Piechartr from "./Piechartr";
import Donutchart from "./Donutchart";
import Semicercle from "./Semicercle";
import Variablepie from "./Variablepie";
import ChartRenderer from "./ChartRenderer";
import BasicBarChart from "./BasicBarChart";
import {
  UploadOutlined as CloudUploadIcon,
  BarChartOutlined as ChartBarIcon,
  EyeOutlined as EyeIcon,
  SaveOutlined as SaveIcon,
  FileAddOutlined as SaveAsIcon,
  AppstoreOutlined as TemplateIcon,
  PieChartOutlined as ChartPieIcon,
  SyncOutlined as RefreshIcon,
  EditOutlined as PencilIcon,
  PlusOutlined as PlusIcon,
  EditOutlined,
  CloseOutlined,
  PlusOutlined,
  // CommentOutlined,
  // SendOutlined,
  // MessageOutlined,
  MinusOutlined,
  FileTextOutlined,
  AppstoreOutlined,
  InfoCircleOutlined,
  SearchOutlined,
  RestOutlined,
  MoreOutlined,
  MailOutlined,
  // DoubleLeftOutlined,
} from "@ant-design/icons";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import { Rnd } from "react-rnd";
// import CardQueue2 from "pages/home/<USER>";
import { toastNotification } from "components/ToastNotification";
import Confirm from "components/GenericModal";
// Factory function for creating renderers
const { RangePicker } = DatePicker;

const chartComponents = {
  "Column Chart": ChartRenderer,
  "Bar Chart": BasicBarChart,
  Variwide: Variwide,
  "Column Drilldown": Columendrilldown,
  "Column With Negative Values": ColumnNegative,
  "Bar With Negative Values": BarNegative,
  "Column Stacked": ColumnStackedd,
  "Line Charts": LineChart,
  Spline: Spline,
  "Area Chart": AreaChart,
  "Dual Axes": DualAxes,
  "Pie Chart": Piechartr,
  "Donut Chart": Donutchart,
  "Semi Cercle": Semicercle,
  "Variable Pie": Variablepie,
};

const createRenderer = (Component) => (pivotProps) => {
  try {
    const pivotData = new PivotData({
      ...pivotProps,
      aggregators,
    });
    return <Component pivotData={pivotData} />;
  } catch (error) {
    console.error(`Error rendering ${Component.name}:`, error);
    return <p>Error rendering {Component.name}.</p>;
  }
};

const allRenderers = Object.entries(chartComponents).reduce(
  (acc, [name, Component]) => {
    acc[name] = createRenderer(Component);
    return acc;
  },
  {}
);

const GeneralStat = () => {
  const [data, setData] = useState([]);
  const [states, setStates] = useState({
    cols: [],
    rows: [],
    aggregatorName: "Count",
    vals: [],
    hiddenAttributes: [],
    attrValues: {},
  });
  const [loading, setLoading] = useState(false);
  const [dataStatTable, setDataStatTable] = useState([]);
  const [selectedProductId, setSelectedProductId] = useState(null);
  const [productOptions, setProductOptions] = useState([]);
  const [t] = useTranslation("common");
  const [form] = Form.useForm();
  const [chartComponent, setChartComponent] = useState(null);
  const [errors, setErrors] = useState({ title: "" });
  const [title, setTitle] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [charts, setCharts] = useState([]);
  const [selectedUsers, setSelectedUsers] = useState(new Set());
  const [width, setWidth] = useState(0);
  const [editingChartId, setEditingChartId] = useState(null);
  const [chartsTemplate, setChartsTemplate] = useState([]);
  const [selectedModelId, setSelectedModelId] = useState([]);
  const [editingTabId, setEditingTabId] = useState(null);
  const [activeKey, setActiveKey] = useState("1");
  const [label, setLabel] = useState("");
  const [showForm, setShowForm] = useState(false);
  const [chartsTabs, setChartsTabs] = useState([]);
  const sectionRef = useRef(null);
  const [isTabLoading, setTaLoading] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [newTabIndex, setNewTabIndex] = useState(2);
  const [tabIsPublic, setTabIsPublic] = useState(false);
  // const [isChatOpen, setIsChatOpen] = useState(false);
  // const messagesEndRef = useRef(null);
  // const [chatMessages, setChatMessages] = useState([]);
  // const [messageInput, setMessageInput] = useState("");
  // const [unreadCount, setUnreadCount] = useState(0);
  const [dateRange, setDateRange] = useState([null, null]);
  const [refreshInterval, setRefreshInterval] = useState(300000);
  // const [editingIndex, setEditingIndex] = useState(null);
  const { user } = useSelector((state) => state?.user);
  const [isCollapsed, setIsCollapsed] = useState(true);
  const [emailOptions, setEmailOptions] = useState(null);
  const [emailProductId, setEmailProductId] = useState(null);

  const handleChange = (s) => {
    setStates(s);
    const renderer = allRenderers[s.rendererName];
    if (renderer) {
      setChartComponent(renderer(s));
    } else {
      setChartComponent(<p>Chart not available</p>);
    }
  };
  const DonneChart = () => {
    const renderer = allRenderers[states.rendererName];
    if (renderer) {
      const chart = renderer(states);
      setChartComponent(chart);
    } else {
      setChartComponent(<p>Chart not available</p>);
    }
  };

  const handleDragEnd = async (result) => {
    if (!result.destination) return;

    const items = [...charts];
    const [movedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, movedItem);
    setCharts(items);

    if (!activeKey) {
      console.warn("Aucun onglet actif sélectionné pour le reorder.");
      return;
    }
    try {
      const axiosInstance = generateAxios(
        `${URL_ENV?.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
      );
      const updatedOrder = items.map((chart, index) => ({
        id: chart._id,
        position: index,
      }));
      await Promise.all(
        updatedOrder.map(({ id, position }) =>
          axiosInstance.put(
            `/charts/reorder/${id}`,
            { position },
            {
              headers: {
                "Content-Type": "application/json",
                Accept: "application/json",
              },
            }
          )
        )
      );
      console.log("Ordre des graphiques mis à jour avec succès.");
    } catch (error) {
      console.error(
        "Erreur lors de la mise à jour de l'ordre :",
        error.response?.data || error.message
      );
    }
  };

  const getStatTable = useCallback(async (productId) => {
    setLoading(true);
    try {
      const response = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).get(`data/${productId}`);
      if (response.status === 200) {
        setLoading(false);
        setDataStatTable(response.data);
      }
    } catch (error) {
      console.log(error);
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    if (selectedProductId) {
      getStatTable(selectedProductId);
    }
  }, [selectedProductId, getStatTable]);

  const getFamilies = async () => {
    setLoading(true);

    try {
      const response = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).get(`families`);
      if (response.status === 200) {
        setProductOptions(response.data.data);
        setSelectedProductId((prev) => prev ?? response.data.data[0].id);
      }
      setLoading(false);
    } catch (error) {
      console.log(error);
      setLoading(false);
    }
  };

  useEffect(() => getFamilies(), []);

  useEffect(() => {
    if (productOptions?.length > 0 && !selectedProductId) {
      form.setFieldsValue({
        familyOptions: productOptions[0]?.id,
      });
    }
  }, [form, productOptions, selectedProductId]);

  const getmodelbyFamily = useCallback(async (familyId) => {
    try {
      const axiosInstance = generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      );
      const res = await axiosInstance.get(`/templates/user/${familyId}`, {
        withCredentials: true,
      });
      console.log("MODELS for Family:", res.data);
      setChartsTemplate(res.data);
    } catch (error) {
      console.error(
        "Error fetching models by family:",
        error.response?.data || error.message
      );
    }
  }, []);

  useEffect(() => {
    if (selectedProductId) {
      getmodelbyFamily(selectedProductId);
    }
  }, [selectedProductId, getmodelbyFamily]);

  const saveChart = async () => {
    console.log("states", states);

    let tempErrors = {};
    if (!title.trim()) {
      tempErrors.title = t("title_is_required");
    }

    if (Object.keys(tempErrors).length > 0) {
      setErrors(tempErrors);
      return;
    }

    const axiosInstance = generateAxios(
      URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
    );
    if (editingChartId) {
      try {
        const payload = {
          title,
          chartConfig: states,
          width,
        };

        const response = await axiosInstance.put(
          `/charts/${editingChartId}`,
          payload,
          {
            withCredentials: true,
            headers: {
              "Content-Type": "application/json",
              Accept: "application/json",
            },
          }
        );

        console.log("Chart updated:", response.data);

        setCharts((prev) =>
          prev.map((c) => (c.id === editingChartId ? response.data : c))
        );

        const res = await axiosInstance.get(`/charts/tab/${activeKey}`);
        setCharts(res.data);

        // Reset
        setEditingChartId(null);
        setTitle("");
        setStates(null);
        setChartComponent(null);

        toastNotification("success", "updated !", "topRight");
        return;
      } catch (error) {
        console.error(
          "Error updating chart:",
          error.response?.data || error.message
        );
        toastNotification("error", "update failed", "topRight");
        return;
      }
    }

    try {
      if (!activeKey) {
        toastNotification("error", "No tab selected!", "topRight");
        return;
      }

      const response = await axiosInstance.post(
        `/charts`,
        {
          userId: user.id,
          tabId: activeKey,
          title: title,
          chartConfig: states,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
        }
      );

      console.log("Chart created:", response.data);

      setCharts((prev) => [...prev, response.data.data]);
      setStates(null);
      setChartComponent(null);
      setTitle("");
      document.getElementById("chart")?.scrollIntoView({ behavior: "smooth" });

      toastNotification("success", "saved !", "topRight");
    } catch (error) {
      console.error("Error:", error.response?.data || error.message);

      let tempErrors = {};
      for (let key of Object.keys(error.response?.data || {})) {
        tempErrors[key] =
          error.response.data[key]?.message || error.response.data[key];
      }

      setErrors(tempErrors);
    }
  };

  const handleEdit = (chart) => {
    setEditingChartId(chart._id);
    setTitle(chart.title);
    setStates({
      ...chart.chartConfig,
      aggregators,
    });
    setWidth(chart.width || 0);
    document.getElementById("chart")?.scrollIntoView({ behavior: "smooth" });
  };

  const handleCheckboxChange = (user_id) => {
    setSelectedUsers((prevSelected) => {
      const updatedSelected = new Set(prevSelected);

      if (updatedSelected.has(user_id)) {
        updatedSelected.delete(user_id);
      } else {
        updatedSelected.add(user_id);
      }
      return updatedSelected;
    });
  };
  // const filteredCharts = charts.filter((chart) =>
  //   (chart.title || "").toLowerCase().includes(searchQuery.toLowerCase())
  // );

  const updateChartWidth = async (chartId, newWidth) => {
    console.log("width up:", chartId, "to", newWidth);

    const axiosInstance = generateAxios(
      URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
    );

    try {
      await axiosInstance.put(
        `/charts/${chartId}`,
        { width: newWidth },
        {
          withCredentials: true,
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
        }
      );

      setCharts((prev) =>
        prev.map((chart) =>
          chart.id === chartId ? { ...chart, width: newWidth } : chart
        )
      );

      console.log("Width updated successfully.");
    } catch (err) {
      console.error(
        "Failed to update width:",
        err.response?.data || err.message
      );
    }
  };

  const deleteChart = async () => {
    if (selectedUsers.size === 0) {
      // toast.error("Please select charts to delete");
      return;
    }

    const axiosInstance = generateAxios(
      URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
    );

    try {
      const idsToDelete = Array.from(selectedUsers);

      await Promise.all(
        idsToDelete.map((_id) => axiosInstance.delete(`/charts/${_id}`))
      );
      const response = await axiosInstance.get(`/charts/tab/${activeKey}`, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });
      setCharts(response.data);
      setSelectedUsers(new Set());
      toastNotification("success", t("stat.deleted_successfully"), "topRight");
    } catch (err) {
      console.error("Error during delete:", err.response?.data || err.message);
      toastNotification("error", t("stat.delete_error"), "topRight");
    }
  };

  useEffect(() => {
    //  localStorage.getItem("chartDraft") &&
    //setCharts(JSON.parse(localStorage.getItem("chartDraft")));
  }, [user?.id]);

  const saveModel = async () => {
    if (!title.trim()) {
      // toast.error('Model name is required');
      return;
    }

    try {
      const axiosInstance = generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      );

      const payload = {
        ownerId: user.id,
        familyId: String(selectedProductId),
        titleModel: title,
        chartConfig: { ...states, isTemplate: true },
      };

      const response = await axiosInstance.post(`/templates`, payload, {
        withCredentials: true,
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      const newTemplate = response.data;
      setChartsTemplate((prev) => [...prev, newTemplate]);
      // toast.success('Model saved successfully!');
      setTitle("");
    } catch (error) {
      console.error(
        "Error saving model:",
        error.response?.data || error.message
      );
      // toast.error('Failed to save model');
    }
  };

  const handleModelSelect = (modelId) => {
    setSelectedModelId(modelId);
    const template = chartsTemplate.find((t) => t._id === modelId);
    if (template) {
      setStates(template.chartConfig);
    }
  };
  const handleDragEndTb = async (result) => {
    if (!result.destination) return;

    const items = Array.from(chartsTabs);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);
    setChartsTabs(items);

    try {
      const axiosInstance = generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      );

      const updatedOrder = items.map((tab) => tab._id);
      const tabIdForUpdate = items[0]?._id;
      if (!tabIdForUpdate) {
        console.warn("Aucun tab ID disponible pour update");
        return;
      }
      const response = await axiosInstance.put(
        `/tabs/update-ordertab/${tabIdForUpdate}`,
        { order: updatedOrder },
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
        }
      );

      console.log("Order updated successfully");
    } catch (err) {
      console.error("Order update failed:", err.response?.data || err.message);
    }
  };

  const addTab = async (isPublic = false) => {
    try {
      const axiosInstance = generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      );

      const response = await axiosInstance.post(
        `/tabs`,
        {
          tableId: user.id,
          label: label || `Plus ${newTabIndex}`,
          isPublic: isPublic,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
        }
      );

      const newTab = response.data;

      setChartsTabs((prev) => [
        ...prev,
        {
          _id: newTab._id,
          label: newTab.label,
          key: newTab._id,
        },
      ]);
      setActiveKey(newTab._id);
      setLabel("");
      setNewTabIndex((prev) => prev + 1);

      console.log("newTab", newTab);
    } catch (error) {
      console.error(
        "Error creating tab:",
        error.response?.data || error.message
      );
    }
  };

  const editTab = async () => {
    if (!editingTabId) return;

    try {
      const axiosInstance = generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      );

      const response = await axiosInstance.put(
        `/tabs/${editingTabId}`,
        { label, isPublic: tabIsPublic },
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
        }
      );
      setChartsTabs((prev) =>
        prev.map((tab) =>
          tab._id === editingTabId
            ? { ...tab, label: response.data.label }
            : tab
        )
      );

      setEditingTabId(null);
      setLabel("");
      // toast.success("Onglet modifié avec succès !");
    } catch (error) {
      console.error(
        "Erreur lors de l'édition de l'onglet :",
        error.response?.data || error.message
      );
      // toast.error("Échec de la mise à jour de l'onglet");
    }
  };

  const getTabsNewTab = async (axiosInstance) => {
    try {
      const response = await axiosInstance.get(`/tabs/newtab/${user.id}`);
      if (response.status === 200) {
        return response.data.map((tab) => ({
          label: tab.label,
          _id: tab._id,
          key: tab._id,
        }));
      }
    } catch (error) {
      console.error("Erreur newtab :", error.response?.data || error.message);
      return [];
    }
  };

  const getTabsPublic = async (axiosInstance) => {
    try {
      const response = await axiosInstance.get("/tabs/public");
      if (response.status === 200) {
        return response.data.map((tab) => ({
          label: tab.label,
          _id: tab._id,
          key: tab._id,
        }));
      }
    } catch (error) {
      console.error("Erreur public :", error.response?.data || error.message);
      return [];
    }
  };

  const getAllTabs = async () => {
    setLoading(true);
    const axiosInstance = generateAxios(
      URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
    );

    try {
      let tabs = [];

      if (user?.id) {
        const [privateTabs, publicTabs] = await Promise.all([
          getTabsNewTab(axiosInstance),
          getTabsPublic(axiosInstance),
        ]);
        tabs = [...privateTabs, ...publicTabs];
      } else {
        tabs = await getTabsPublic(axiosInstance);
      }
      const uniqueTabs = tabs.filter(
        (tab, index, self) => index === self.findIndex((t) => t._id === tab._id)
      );

      setChartsTabs(uniqueTabs);
      setActiveKey(uniqueTabs[0]?._id);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getAllTabs();
  }, [user?.id]);

  useEffect(() => {
    const fetchCharts = async () => {
      setTaLoading(true);
      try {
        const axiosInstance = generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        );
        const response = await axiosInstance.get(`/charts/tab/${activeKey}`, {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
        });
        setCharts(response.data);
        setSelectedModelId(null);
      } catch (err) {
        console.error(
          "Failed to fetch charts:",
          err.response?.data || err.message
        );
      } finally {
        setTaLoading(false);
      }
    };

    if (activeKey) {
      fetchCharts();
    }
  }, [activeKey]);

  const deleteTab = async (tabId) => {
    try {
      const axiosInstance = generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      );

      await axiosInstance.delete(`/tabs/${tabId}`, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });
      setChartsTabs((prev) => prev.filter((tab) => tab._id !== tabId));
      toastNotification(
        "success",
        t("stat.tab_deleted_successfully"),
        "topRight"
      );
    } catch (error) {
      console.error(
        "Erreur lors de la suppression de l'onglet :",
        error.response?.data || error.message
      );
      toastNotification("error", t("stat.tab_delete_error"), "topRight");
    }
  };

  const refreshChartsData = useCallback(async () => {
    if (!selectedProductId) return;
    try {
      const axiosInstance = generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      );

      const response = await axiosInstance.get(`/data/${selectedProductId}`, {
        withCredentials: true,
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      const newData = response.data;
      setData(newData);
      const updatedCharts = charts.map((chart) => ({
        ...chart,
        chartConfig: {
          ...chart.chartConfig,
          data: newData,
        },
      }));

      for (const chart of updatedCharts) {
        try {
          await axiosInstance.put(
            `/charts/${chart._id}`,
            {
              title: chart.title,
              chartConfig: chart.chartConfig,
              width: chart.width,
            },
            {
              headers: {
                "Content-Type": "application/json",
                Accept: "application/json",
              },
            }
          );
        } catch (error) {
          console.error(
            `Erreur lors de la mise à jour du chart ${chart.id}:`,
            error.response?.data || error.message
          );
        }
      }
      setCharts(updatedCharts);
    } catch (error) {
      console.error("Erreur lors du rafraîchissement des données:", error);
    }
  }, [selectedProductId, charts]);

  useEffect(() => {
    let intervalId;
    if (autoRefresh) {
      intervalId = setInterval(refreshChartsData, refreshInterval);
    }
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [autoRefresh, refreshInterval, refreshChartsData]);
  const renderRefreshControls = () => (
    <div className="mb-4 flex items-center gap-4">
      <Button
        onClick={() => setAutoRefresh(!autoRefresh)}
        className={`${autoRefresh ? "bg-green-500" : "bg-gray-500"} text-white`}
      >
        {autoRefresh
          ? "Arrêter le rafraîchissement"
          : "Activer le rafraîchissement"}
      </Button>
      {autoRefresh && (
        <Select
          value={refreshInterval}
          onChange={setRefreshInterval}
          options={[
            { value: 60000, label: "1 minute" },
            { value: 300000, label: "5 minutes" },
            { value: 900000, label: "15 minutes" },
            { value: 1800000, label: "30 minutes" },
            { value: ********, label: "1 jour" },
            { value: *********, label: "1 semaine" },
          ]}
        />
      )}
    </div>
  );

  const getEmail = async () => {
    setLoading(true);
    try {
      const axiosInstance = generateAxios(
        `${URL_ENV?.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
      );
      const response = await axiosInstance.get("accounts");
      console.log("API res:", response);
      let data = response.data.data;
      if (!Array.isArray(data)) {
        data = response.data;
      }
      if (Array.isArray(data)) {
        setEmailOptions(data);
        setEmailProductId((prev) => prev ?? data[0]?.id);
        console.log("emailOptions to set:", data);
      } else {
        setEmailOptions([]);
      }
    } catch (error) {
      console.error("Erreur lors de la récupération des emails :", error);
      setEmailOptions([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getEmail();
  }, []);

  const getStatEmail = useCallback(async (accountId) => {
    setLoading(true);
    try {
      const response = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).get(`data/email?account_id=${accountId}`);
      if (response.status === 200) {
        setLoading(false);
        setDataStatTable(response.data);
      }
    } catch (error) {
      console.log(error);
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    if (emailProductId) {
      getStatEmail(emailProductId);
    }
  }, [emailProductId, getStatEmail]);

  const menu = (
    <Menu>
      <Menu.Item key="1" onClick={DonneChart}>
        {t("stat.show_graph")}
      </Menu.Item>
      <Menu.Item key="2" onClick={saveChart}>
        {editingChartId ? t("stat.update_chart") : t("stat.save_chart")}
      </Menu.Item>
      {editingChartId && (
        <Menu.Item
          key="3"
          danger
          onClick={() => {
            setStates(null);
            setChartComponent(null);
            setEditingChartId(null);
            setTitle("");
          }}
        >
          {t("stat.AnullerLaModification")}
        </Menu.Item>
      )}
      <Menu.Item key="4" onClick={saveModel}>
        {t("stat.saveAsModel")}
      </Menu.Item>
    </Menu>
  );

  return (
    <Spin spinning={loading}>
      {/* <GlobalStyle /> */}
      <div className="full-h-screen min-h-screen">
        <main className="mx-auto p-2">
          <section>
            <div className="flex justify-end p-2">
              <Button onClick={() => setIsCollapsed(!isCollapsed)}>
                {isCollapsed && (
                  <span className="flex items-center gap-1">
                    <PlusOutlined /> {t("stat.AfficherLaConfiguration")}
                  </span>
                )}
                {!isCollapsed && (
                  <span className="flex items-center gap-1">
                    <MinusOutlined /> {t("stat.RéduireLaConfiguration")}
                  </span>
                )}
              </Button>
            </div>
            {/* Header Section */}
            {!isCollapsed && (
              <Card className="mb-4 rounded-xl border border-gray-100 bg-white shadow-sm">
                <div className="flex flex-col gap-4 lg:flex-row lg:items-start">
                  {/* Bloc Email (si mail sélectionné) */}
                  {selectedProductId === "mail" && (
                    <div className="flex-1">
                      <label className="mb-1 block flex items-center gap-2 text-xs font-medium text-gray-600">
                        <MailOutlined className="text-blue-400" />
                        {t("companies.email")}
                      </label>
                      <Select
                        key={emailProductId}
                        loading={loading}
                        options={emailOptions?.map((account) => ({
                          label: account.email,
                          value: account.id,
                        }))}
                        value={emailProductId}
                        onChange={(e) => {
                          setEmailProductId(e);
                          setStates({
                            cols: [],
                            rows: [],
                            aggregatorName: "Count",
                            vals: [],
                            hiddenAttributes: [],
                            attrValues: {},
                          });
                        }}
                        placeholder="Choisir un email"
                        className="w-full"
                        size="middle"
                      />
                      <div className="mt-2 flex items-center gap-2 text-xs text-gray-500">
                        <div
                          className="h-1 w-1 animate-pulse rounded-full bg-blue-400"
                          style={{ animationDelay: "0.5s" }}
                        ></div>
                        {t("stat.ChooseEmail")}
                      </div>
                    </div>
                  )}

                  {/* Bloc Family + Model */}
                  <div className="flex flex-1 flex-col gap-4">
                    <div>
                      <label className="mb-1 block flex items-center gap-2 text-xs font-medium text-gray-600">
                        <AppstoreOutlined className="text-blue-400" />
                        {t("menu2.family")}
                      </label>
                      <Select
                        loading={loading}
                        options={[
                          ...(productOptions?.map((product) => ({
                            label: product.label,
                            value: product.id,
                          })) || []),
                          { label: t("companies.phone"), value: "phone" },
                          { label: t("companies.email"), value: "mail" },
                        ]}
                        placeholder={t("import.chooseFamily")}
                        className="w-full"
                        size="middle"
                        onChange={(e) => {
                          setSelectedProductId(e);
                          getmodelbyFamily(e);
                          setChartsTemplate([]);
                          setSelectedModelId(null);
                          setStates({
                            cols: [],
                            rows: [],
                            aggregatorName: "Count",
                            vals: [],
                            hiddenAttributes: [],
                            attrValues: {},
                          });
                        }}
                      />
                      <div className="mt-2 flex items-center gap-2 text-xs text-gray-500">
                        <div
                          className="h-1 w-1 animate-pulse rounded-full bg-blue-400"
                          style={{ animationDelay: "0.5s" }}
                        ></div>
                        {t("stat.ChooseYourDataFamilyCategory")}
                      </div>
                    </div>
                    <div>
                      <label className="mb-1 block flex items-center gap-2 text-xs font-medium text-gray-600">
                        <FileTextOutlined className="text-emerald-400" />
                        {t("stat.choose_model")}
                      </label>
                      <Select
                        placeholder={t("stat.load_model")}
                        value={selectedModelId}
                        onChange={handleModelSelect}
                        options={chartsTemplate.map((template) => ({
                          label: template.titleModel,
                          value: template._id,
                        }))}
                        className="w-full"
                        size="middle"
                      />
                      <div className="mt-2 flex items-center gap-2 text-xs text-gray-500">
                        <div className="h-1 w-1 animate-pulse rounded-full bg-emerald-400"></div>
                        {t("stat.select_preconfigured_template")}
                      </div>
                    </div>
                  </div>

                  {/* Bloc Guide */}
                  <div className="hidden flex-1 pl-6 lg:block">
                    <div className="rounded-lg border border-blue-100 bg-blue-100 p-4">
                      <div className="mb-2 flex items-center gap-2">
                        <InfoCircleOutlined className="text-blue-400" />
                        <span className="font-semibold text-blue-800">
                          {t("stat.QuickGuide")}
                        </span>
                      </div>
                      <ol className="list-decimal space-y-1 pl-4 text-xs text-blue-700">
                        <li>
                          {t("stat.SelectAFamilyToLoadAvailableDataModels")}
                        </li>
                        <li>{t("stat.ChooseAModelTemplateForQuickSetup")}</li>
                      </ol>
                    </div>
                  </div>
                </div>
              </Card>
            )}

            {/* Chart Section */}
            {dataStatTable?.length > 0 ? (
              <div className="space-y-6">
                {!isCollapsed && (
                  <Card className="rounded-xl bg-white p-4">
                    <div className="overflow-x-auto">
                      <div className="min-w-[800px]">
                        <PivotTableUI
                          data={dataStatTable}
                          onChange={handleChange}
                          {...states}
                          aggregators={aggregators}
                          renderers={Object.assign(
                            {},
                            TableRenderers,
                            allRenderers
                          )}
                          attrValues={""}
                          hiddenAttributes={[]}
                          hiddenFromAggregators={[]}
                        />
                      </div>
                    </div>
                    <div className="mt-4 flex justify-end">
                      <Button
                        type="primary"
                        danger
                        ghost
                        onClick={() => {
                          setStates(null);
                          setChartComponent(null);
                          setEditingChartId(null);
                          setTitle("");
                        }}
                        icon={<CloseOutlined />}
                      >
                        {t("stat.Clear")}
                      </Button>
                    </div>
                  </Card>
                )}
                {/* Chart Controls */}
                {!isCollapsed && (
                  <div>
                    {/* Chart Display Panel */}
                    <Card className="relative rounded-2xl p-4 lg:col-span-2">
                      {/* Header: Title centered + Icon top-right */}
                      <div className="relative mb-6">
                        <Tooltip
                          title={t(
                            "stat.ConfigureYourSettingsAndCreateStunningDataVisualizations"
                          )}
                        >
                          <div className="flex justify-center">
                            <p className="text-center text-lg font-semibold">
                              {t("stat.genereleghraphique")}
                            </p>
                            <InfoCircleOutlined className="ml-1" />
                          </div>
                        </Tooltip>
                        <div className="absolute right-0 top-0">
                          <Dropdown overlay={menu} trigger={["click"]}>
                            <Button
                              type="text"
                              shape="circle"
                              icon={<MoreOutlined />}
                            />
                          </Dropdown>
                        </div>
                      </div>

                      <div className="space-y-6">
                        {/* Title Input */}
                        <div>
                          <Input
                            type="text"
                            value={title}
                            onChange={(e) => {
                              setTitle(e.target.value);
                              if (errors.title) {
                                setErrors({ ...errors, title: "" });
                              }
                            }}
                            prefix={<EditOutlined className="text-gray-400" />}
                            className="rounded-xl"
                            placeholder={t("selfNotes.title")}
                            size="large"
                          />
                          {errors.title && (
                            <p className="mt-2 flex items-center gap-1 text-sm text-red-500">
                              <span className="h-1 w-1 rounded-full bg-red-500"></span>
                              {errors.title}
                            </p>
                          )}
                        </div>

                        {/* Chart Card */}
                        <Card className="flex min-h-[400px] items-center justify-center rounded-xl bg-white p-6">
                          {chartComponent || (
                            <div className="space-y-4 text-center">
                              <Empty
                                description={t("stat.noChartGenerated")}
                                image="https://gw.alipayobjects.com/zos/antfincdn/ZHrcdLPrvN/empty.svg"
                              />
                            </div>
                          )}
                        </Card>
                      </div>
                    </Card>
                  </div>
                )}

                {/* Charts List Section */}
                <Card className="space-y-6 rounded-xl p-4">
                  {/* Actions Toolbar */}
                  <div className="rounded-xl p-4">
                    {/* Control Section */}
                    <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                      {/* Left Side Controls */}
                      <div className="flex flex-wrap items-center gap-4">
                        {/* Status Indicator */}
                        <div className="flex items-center gap-2">
                          <div className="relative">
                            <div
                              className={`h-3 w-3 rounded-full transition-colors ${
                                autoRefresh ? "bg-green-500" : "bg-gray-400"
                              }`}
                            >
                              {autoRefresh && (
                                <div className="absolute inset-0 h-3 w-3 animate-ping rounded-full bg-green-500 opacity-75"></div>
                              )}
                            </div>
                          </div>
                          <span className="text-sm font-medium text-gray-700">
                            {autoRefresh
                              ? t("stat.Auto-refreshActive")
                              : t("stat.Auto-refreshInactive")}
                          </span>
                        </div>

                        {/* Toggle Button */}
                        <Button
                          type={autoRefresh ? "primary" : "default"}
                          size="small"
                          onClick={() => setAutoRefresh(!autoRefresh)}
                          className={`min-w-[120px] ${
                            autoRefresh
                              ? "bg-green-600 hover:bg-green-700"
                              : "border-gray-300 bg-white hover:bg-gray-50"
                          }`}
                        >
                          {autoRefresh
                            ? t("stat.stop_refresh")
                            : t("stat.start_refresh")}
                        </Button>
                      </div>

                      {/* Right Side Controls */}
                      <div className="flex flex-col items-center justify-center space-y-2">
                        {/* Refresh Controls */}
                        {autoRefresh && (
                          <div className="flex items-center">
                            {renderRefreshControls()}
                          </div>
                        )}

                        {/* Delete Button */}
                        {selectedUsers.size > 0 && (
                          <Button
                            danger
                            icon={<CloseOutlined />}
                            onClick={() => {
                              Confirm(
                                "delete ",
                                "Confirm",
                                <RestOutlined style={{ color: "red" }} />,
                                function func() {
                                  return deleteChart();
                                },
                                true
                              );
                            }}
                          >
                            {t("stat.delete_selected")} ({selectedUsers.size})
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Tabs Section */}
                  <div>
                    {showForm && (
                      <Card className="my-6 bg-white">
                        <div className="mb-4 flex items-center justify-between border-b border-gray-200 pb-4">
                          <div className="flex items-center gap-3">
                            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-100">
                              <PlusOutlined className="h-4 w-4 text-blue-600" />
                            </div>
                            <h4 className="text-lg font-semibold text-gray-900">
                              {editingTabId
                                ? t("stat.edit_tab")
                                : t("stat.createNewTab")}
                            </h4>
                          </div>
                          <Button
                            type="text"
                            icon={<CloseOutlined />}
                            onClick={() => setShowForm(false)}
                            className="rounded-lg text-gray-500 hover:bg-gray-100 hover:text-gray-700"
                          />
                        </div>
                        <div className="space-y-6">
                          <div>
                            <label className="mb-1 block text-sm font-medium text-gray-700">
                              {t("stat.TabName")}
                            </label>
                            <Input
                              placeholder={t("selfNotes.title")}
                              value={label}
                              onChange={(e) => setLabel(e.target.value)}
                              className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                              size="large"
                            />
                          </div>

                          <div className="flex flex-col space-y-4 pt-2">
                            {editingTabId ? (
                              <div className="space-y-4">
                                <div className="flex items-center space-x-4">
                                  <label className="inline-flex items-center">
                                    <input
                                      type="radio"
                                      name="privacy"
                                      value="private"
                                      checked={!tabIsPublic}
                                      onChange={() => setTabIsPublic(false)}
                                      className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                                    />
                                    <span className="ml-2 text-sm text-gray-700">
                                      {t("stat.private")}
                                    </span>
                                  </label>
                                  <label className="inline-flex items-center">
                                    <input
                                      type="radio"
                                      name="privacy"
                                      value="public"
                                      checked={tabIsPublic}
                                      onChange={() => setTabIsPublic(true)}
                                      className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                                    />
                                    <span className="ml-2 text-sm text-gray-700">
                                      {t("stat.public")}
                                    </span>
                                  </label>
                                </div>
                                <div className="flex justify-end">
                                  <Button
                                    type="primary"
                                    onClick={() => {
                                      editTab();
                                      setEditingTabId(null);
                                      setLabel("");
                                      setShowForm(false);
                                    }}
                                    className=" border-blue-600 bg-blue-600 hover:bg-blue-700"
                                    size="large"
                                  >
                                    {t("stat.edit")}
                                  </Button>
                                </div>
                              </div>
                            ) : (
                              <div className="flex justify-end space-x-3">
                                <Button
                                  onClick={() => {
                                    addTab(false);
                                    setLabel("");
                                    setShowForm(false);
                                  }}
                                  className="border-gray-300 hover:border-gray-400"
                                  size="large"
                                >
                                  {t("stat.private")}
                                </Button>
                                <Button
                                  type="primary"
                                  onClick={() => {
                                    addTab(true);
                                    setLabel("");
                                    setShowForm(false);
                                  }}
                                  className="border-blue-600 bg-blue-600 hover:bg-blue-700"
                                  size="large"
                                >
                                  {t("stat.public")}
                                </Button>
                              </div>
                            )}
                          </div>
                        </div>
                      </Card>
                    )}
                    <DragDropContext onDragEnd={handleDragEndTb}>
                      <Droppable
                        droppableId="tabs-droppable"
                        direction="horizontal"
                      >
                        {(provided) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.droppableProps}
                          >
                            <Tabs
                              activeKey={activeKey}
                              onChange={setActiveKey}
                              type="card"
                              tabBarExtraContent={
                                <Tooltip title={t("stat.createNewTab")}>
                                  <Button
                                    type="primary"
                                    ghost
                                    onClick={() => {
                                      setShowForm(!showForm);
                                      setEditingTabId(null);
                                      setLabel("");
                                    }}
                                    className="flex items-center justify-center border-0 text-gray-900 hover:bg-gray-50"
                                    style={{ fontSize: "12px" }}
                                  >
                                    <PlusOutlined />
                                    {t("stat.createNewTab")}
                                  </Button>
                                </Tooltip>
                              }
                              items={chartsTabs.map((tab, index) => ({
                                key: tab._id,
                                label: (
                                  <Draggable
                                    draggableId={tab._id}
                                    index={index}
                                  >
                                    {(provided) => (
                                      <div
                                        ref={provided.innerRef}
                                        {...provided.draggableProps}
                                        {...provided.dragHandleProps}
                                        className="flex items-center"
                                      >
                                        <span className="font-medium text-gray-700">
                                          {tab.label}
                                        </span>
                                        <div className="group flex items-center">
                                          <EditOutlined
                                            className="ml-2 h-3 w-3 text-gray-500 opacity-0 hover:text-blue-600 group-hover:opacity-100"
                                            onClick={(e) => {
                                              setShowForm(!showForm);
                                              e.stopPropagation();
                                              setEditingTabId(tab._id);
                                              setLabel(tab.label);
                                              setTabIsPublic(!!tab.isPublic);
                                            }}
                                          />
                                          <CloseOutlined
                                            className="ml-1 cursor-pointer text-gray-400 opacity-0 hover:text-red-600 group-hover:opacity-100"
                                            onClick={() => {
                                              Confirm(
                                                "delete ",
                                                "Confirm",
                                                <RestOutlined
                                                  style={{ color: "red" }}
                                                />,
                                                () => deleteTab(tab._id),
                                                true
                                              );
                                            }}
                                          />
                                        </div>
                                      </div>
                                    )}
                                  </Draggable>
                                ),
                                children: (
                                  <div>
                                    <div className="mb-4 border-t pt-[1%]">
                                      <div className="mb-4 flex flex-wrap items-center justify-between gap-2">
                                        <div className="flex items-center gap-2 text-sm text-gray-500">
                                          {/* Total Charts*/}
                                          <div className="flex items-center gap-2 rounded-lg bg-gray-100 px-3 py-1.5">
                                            <span className="text-xs font-medium text-gray-600">
                                              {t("stat.TotalCharts")}:
                                            </span>
                                            <span className="text-sm font-bold text-blue-600">
                                              {charts?.length || 0}
                                            </span>
                                          </div>
                                          <span className="text-sm text-gray-600">
                                            {t("stat.Showing")}{" "}
                                            <span className="font-medium text-gray-800">
                                              {
                                                charts.filter((chart) =>
                                                  (chart.title || "")
                                                    .toLowerCase()
                                                    .includes(
                                                      searchQuery.toLowerCase()
                                                    )
                                                ).length
                                              }
                                            </span>{" "}
                                            {t("stat.of")}{" "}
                                            <span className="font-medium text-gray-800">
                                              {charts.length}
                                            </span>{" "}
                                            {t("stat.charts")}
                                          </span>
                                        </div>

                                        {/* Controls Section */}
                                        <div className="flex items-center gap-2">
                                          <RangePicker
                                            format="DD/MM/YYYY HH"
                                            value={dateRange}
                                            showTime
                                            allowClear
                                            className="w-80"
                                            placeholder={[
                                              t("stat.Début"),
                                              t("stat.Fin"),
                                            ]}
                                            onChange={(dates) =>
                                              setDateRange(dates)
                                            }
                                          />

                                          {/* Refresh Button */}
                                          <Button
                                            type="default"
                                            icon={<RefreshIcon />}
                                            onClick={() => {
                                              charts.forEach((chart) => {
                                                if (chart._id) {
                                                  updateChartWidth(
                                                    chart._id,
                                                    490
                                                  );
                                                }
                                              });
                                            }}
                                            className="inline-flex items-center justify-center gap-2 rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 transition-all duration-200 hover:border-blue-500 hover:bg-blue-50 hover:text-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 active:scale-95"
                                          >
                                            {t("stat.Refresh_Layout")}
                                          </Button>
                                        </div>
                                      </div>
                                      <div className="relative mb-4 flex items-center justify-center">
                                        <Input
                                          type="search"
                                          onChange={(e) =>
                                            setSearchQuery(e.target.value)
                                          }
                                          prefix={
                                            <SearchOutlined className="text-gray-400" />
                                          }
                                          className="h-10 w-full rounded-md border border-gray-200 bg-white hover:border-blue-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                                          placeholder={t("activities.search")}
                                          style={{
                                            paddingLeft: "40px",
                                            width: "25%",
                                          }}
                                        />
                                      </div>
                                    </div>
                                    <div>
                                      {charts?.length === 0 ? (
                                        <div className="rounded-lg border border-dashed border-gray-300 bg-white p-8 text-center">
                                          <Empty
                                            description={t("stat.no_charts")}
                                          />
                                        </div>
                                      ) : (
                                        <section
                                          ref={sectionRef}
                                          className="relative min-h-screen overflow-y-hidden px-4 pb-[1%] pt-[1%] sm:px-6 lg:px-8"
                                        >
                                          {isTabLoading ? (
                                            <div className="flex min-h-[200px] items-center justify-center">
                                              <Spin size="large" />
                                            </div>
                                          ) : (
                                            <DragDropContext
                                              onDragEnd={handleDragEnd}
                                            >
                                              <Droppable
                                                droppableId="charts"
                                                direction="vertical"
                                              >
                                                {(provided) => (
                                                  <div
                                                    className="relative flex flex-wrap items-start justify-start gap-6"
                                                    {...provided.droppableProps}
                                                    ref={provided.innerRef}
                                                  >
                                                    {charts
                                                      .filter((chart) => {
                                                        // Filtre par titre
                                                        const matchTitle = (
                                                          chart.title || ""
                                                        )
                                                          .toLowerCase()
                                                          .includes(
                                                            searchQuery.toLowerCase()
                                                          );
                                                        // Filtre par date
                                                        if (
                                                          !dateRange[0] ||
                                                          !dateRange[1]
                                                        )
                                                          return matchTitle;
                                                        const createdAt =
                                                          new Date(
                                                            chart.created_at
                                                          );
                                                        return (
                                                          matchTitle &&
                                                          createdAt >=
                                                            dateRange[0].toDate() &&
                                                          createdAt <=
                                                            dateRange[1].toDate()
                                                        );
                                                      })
                                                      .map((chart, index) => {
                                                        const Renderer =
                                                          allRenderers[
                                                            chart.chartConfig
                                                              .rendererName
                                                          ];
                                                        const ChartComponent =
                                                          Renderer ? (
                                                            Renderer(
                                                              chart.chartConfig
                                                            )
                                                          ) : (
                                                            <p className="italic text-gray-500">
                                                              {t(
                                                                "stat.chartRendererNotAvailable"
                                                              )}
                                                            </p>
                                                          );
                                                        return (
                                                          <Rnd
                                                            key={chart._id}
                                                            bounds="parent"
                                                            default={{
                                                              width:
                                                                chart.width ||
                                                                400,
                                                              height: "auto",
                                                            }}
                                                            style={{
                                                              position:
                                                                "relative",
                                                            }}
                                                            minWidth={300}
                                                            maxWidth={1300}
                                                            className="w-full sm:w-[48%] xl:w-[32%]"
                                                            onResizeStop={(
                                                              e,
                                                              direction,
                                                              ref,
                                                              delta,
                                                              position
                                                            ) => {
                                                              const newWidth =
                                                                ref.offsetWidth;
                                                              updateChartWidth(
                                                                chart._id,
                                                                newWidth
                                                              );
                                                            }}
                                                          >
                                                            <Draggable
                                                              draggableId={
                                                                chart._id
                                                              }
                                                              index={index}
                                                            >
                                                              {(
                                                                provided,
                                                                snapshot
                                                              ) => (
                                                                <div
                                                                  ref={
                                                                    provided.innerRef
                                                                  }
                                                                  {...provided.draggableProps}
                                                                  className={`
group relative rounded-xl border-2 bg-white ease-in-out
${
  snapshot.isDragging
    ? "scale-105 transform cursor-grabbing border-blue-400 bg-blue-50"
    : "border-gray-200 hover:border-blue-300"
}
`}
                                                                >
                                                                  {snapshot.isDragging && (
                                                                    <div className="absolute -left-2 -top-2 rounded-full bg-blue-500 px-2 py-1 text-xs text-white"></div>
                                                                  )}
                                                                  <Card
                                                                    className="group rounded-lg border border-gray-200"
                                                                    bodyStyle={{
                                                                      padding:
                                                                        "20px",
                                                                    }}
                                                                  >
                                                                    {/* Header */}
                                                                    <div
                                                                      {...provided.dragHandleProps}
                                                                      className={`brd mb-4 flex items-center justify-between border-b border-gray-200 pb-3 ${
                                                                        snapshot.isDragging
                                                                          ? "cursor-grabbing"
                                                                          : "cursor-grab"
                                                                      }`}
                                                                    >
                                                                      {/* Titre avec point bleu */}
                                                                      <div className="flex max-w-[70%] items-center gap-2 border ">
                                                                        <div className="h-2 w-2 rounded-full bg-blue-500" />
                                                                        <h3 className="truncate text-base font-semibold text-gray-800">
                                                                          {
                                                                            chart.title
                                                                          }
                                                                        </h3>
                                                                      </div>
                                                                      {/* Actions (Checkbox + Edit) */}
                                                                      <div className="opacity-0 transition-opacity duration-300 group-hover:opacity-100">
                                                                        <div className="flex items-center space-x-3">
                                                                          <Checkbox
                                                                            checked={selectedUsers.has(
                                                                              chart._id
                                                                            )}
                                                                            onChange={() =>
                                                                              handleCheckboxChange(
                                                                                chart._id
                                                                              )
                                                                            }
                                                                            className="h-4 w-4 text-blue-600"
                                                                          />
                                                                          <EditOutlined
                                                                            onClick={() => {
                                                                              handleEdit(
                                                                                chart
                                                                              );
                                                                              setIsCollapsed(
                                                                                !isCollapsed
                                                                              );
                                                                            }}
                                                                            className="cursor-pointer rounded-md p-1 text-gray-500 transition duration-200 hover:bg-gray-100 hover:text-blue-600"
                                                                          />
                                                                        </div>
                                                                      </div>
                                                                    </div>
                                                                    {/* Zone Chart */}
                                                                    <div className="w-full overflow-hidden">
                                                                      <div className="rounded-md bg-white p-4">
                                                                        {
                                                                          ChartComponent
                                                                        }
                                                                      </div>
                                                                    </div>
                                                                    {/* Footer */}
                                                                    <div className="br mt-4 flex items-center justify-between pt-3 text-xs text-gray-500">
                                                                      <span>
                                                                        {t(
                                                                          "stat.Created"
                                                                        )}
                                                                        :{" "}
                                                                        {chart.created_at
                                                                          ? new Date(
                                                                              chart.created_at
                                                                            )
                                                                              .toISOString()
                                                                              .slice(
                                                                                0,
                                                                                13
                                                                              )
                                                                              .replace(
                                                                                "T",
                                                                                " "
                                                                              )
                                                                          : "N/A"}
                                                                      </span>
                                                                    </div>
                                                                  </Card>
                                                                </div>
                                                              )}
                                                            </Draggable>
                                                          </Rnd>
                                                        );
                                                      })}
                                                    {provided.placeholder}
                                                  </div>
                                                )}
                                              </Droppable>
                                            </DragDropContext>
                                          )}
                                        </section>
                                      )}
                                    </div>
                                  </div>
                                ),
                              }))}
                              className="mt-4"
                            />
                          </div>
                        )}
                      </Droppable>
                    </DragDropContext>
                  </div>
                </Card>
              </div>
            ) : (
              <Card className="flex flex-col items-center justify-center rounded-xl border bg-white p-12">
                <Empty
                  description={
                    <span className="text-lg text-gray-500">
                      {t("mailing.noData")}
                    </span>
                  }
                />
              </Card>
            )}
          </section>
        </main>
      </div>
    </Spin>
  );
};
export default GeneralStat;
