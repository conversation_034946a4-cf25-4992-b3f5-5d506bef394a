const manageShowHideCol = (columns) => {
  let columnsNumber = 0;
  let showColumnsNumber = 0;
  const colConfig = {};
  const showHideColumns = {};
  showHideColumns.groups = {};
  columns?.forEach((column, i) => {
    let groupColNumber = 0;
    let groupShowColumnsNumber = 0;
    if (i === 0) {
      columnsNumber++;
      showColumnsNumber++;
      showHideColumns.mainColumn = column?.title;
      colConfig[column?.title] = {
        id: column?.key,
        show: 1,
        rank: 1,
      };
    } else {
      const allColumns = [];
      const allShowColumns = [];
      column?.children?.forEach((col) => {
        columnsNumber++;
        groupColNumber++;
        allColumns.push(col?.title);
        colConfig[col?.title] = {
          id: col?.key,
          show: col.show,
          rank: col.rank,
        };
        if (col?.show) {
          showColumnsNumber++;
          groupShowColumnsNumber++;
          allShowColumns.push(col?.title);
        }
      });
      showHideColumns.groups[column.title] = {
        allColumns: allColumns,
        showColumns: allShowColumns,
        columnsNumber: groupColNumber,
        showColumnsNumber: groupShowColumnsNumber,
        rank: i,
      };
    }
  });
  showHideColumns.totalColumns = columnsNumber;
  showHideColumns.totalShowColumns = showColumnsNumber;
  showHideColumns.colConfig = colConfig;
  return showHideColumns;
};

export default manageShowHideCol;
