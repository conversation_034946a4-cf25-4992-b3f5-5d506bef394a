import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON> } from "antd";
import {
  BoldOutlined,
  ItalicOutlined,
  UnderlineOutlined,
  StrikethroughOutlined,
  AlignLeftOutlined,
  AlignCenterOutlined,
  AlignRightOutlined,
  OrderedListOutlined,
  UnorderedListOutlined,
  LinkOutlined,
  PictureOutlined,
  UploadOutlined,
  CodeOutlined,
  QuestionCircleOutlined,
} from "@ant-design/icons";
import { MdSmartButton } from "react-icons/md";
import TableEditor from "./TableEditor";
// Couleurs disponibles
const colors = [
  "#000000",
  "#1890ff",
  "#52c41a",
  "#faad14",
  "#f5222d",
  "#722ed1",
  "#eb2f96",
  "#fa8c16",
  "#a0d911",
  "#13c2c2",
];
const EditorToolbar = ({
  execCommand,
  handleLinkClick,
  handleImageUpload,
  insertImageFromUrl,
  openButtonModal,
  openHtmlModal,
  selectedColor,
  handleColorChange,
  fontSize,
  fontFamily,
  setFontSize,
  setFontFamily,
  editorRef,
  setContent,
}) => {
  // État pour sauvegarder la sélection
  const [savedSelection, setSavedSelection] = useState(null);
  const [changeFontFamily, setChangeFontfamily] = useState(false);

  // État pour les styles actifs
  const [activeStyles, setActiveStyles] = useState({
    bold: false,
    italic: false,
    underline: false,
    // strikeThrough: false,
    justifyLeft: false,
    justifyCenter: false,
    justifyRight: false,
    insertOrderedList: false,
    insertUnorderedList: false,
  });

  // État pour la police et taille actives détectées
  const [detectedFont, setDetectedFont] = useState({
    family: "",
    size: "",
  });

  // Fonction pour vérifier les styles actifs
  const checkActiveStyles = () => {
    if (!editorRef.current || !document.getSelection().rangeCount) {
      return;
    }
    // console.log(detectedFont);
    const selection = window.getSelection();
    const range = selection.getRangeAt(0);

    // Vérifier si la sélection est dans l'éditeur
    if (!editorRef.current.contains(range.commonAncestorContainer)) {
      return;
    }

    const getActiveStyles = () => {
      const selection = window.getSelection();
      if (!selection.rangeCount) return {};

      const range = selection.getRangeAt(0);
      const startContainer = range.startContainer;

      // Pour les éléments parents
      const getParentElements = (element) => {
        const parents = [];
        while (element && element !== document.body) {
          parents.push(element);
          element = element.parentNode;
        }
        return parents;
      };
      const parents = getParentElements(startContainer);
      return {
        bold: parents.some(
          (el) =>
            el.style?.fontWeight === "bold" ||
            (el instanceof HTMLElement &&
              window.getComputedStyle(el).fontWeight === "700")
        ),
        italic: parents.some(
          (el) =>
            el.style?.fontStyle === "italic" ||
            (el instanceof HTMLElement &&
              window.getComputedStyle(el).fontStyle === "italic")
        ),
        underline: parents.some(
          (el) =>
            el.style?.textDecoration.includes("underline") ||
            (el instanceof HTMLElement &&
              window.getComputedStyle(el).textDecoration.includes("underline"))
        ),
        // strikeThrough: parents.some(
        //   (el) =>
        //     el.style?.textDecoration.includes("line-through") ||
        //     (el instanceof HTMLElement &&
        //       window
        //         .getComputedStyle(el)
        //         .textDecoration.includes("line-through"))
        // ),
        justifyLeft: parents.some(
          (el) =>
            el.style?.textAlign === "left" ||
            (el instanceof HTMLElement &&
              window.getComputedStyle(el).textAlign === "left")
        ),
        justifyCenter: parents.some(
          (el) =>
            el.style?.textAlign === "center" ||
            (el instanceof HTMLElement &&
              window.getComputedStyle(el).textAlign === "center")
        ),
        justifyRight: parents.some(
          (el) =>
            el.style?.textAlign === "right" ||
            (el instanceof HTMLElement &&
              window.getComputedStyle(el).textAlign === "right")
        ),
        insertOrderedList: parents.some((el) => el.nodeName === "OL"),
        insertUnorderedList: parents.some((el) => el.nodeName === "UL"),
      };
    };

    // Utilisation
    const newActiveStyles = getActiveStyles();

    setActiveStyles(newActiveStyles);

    // Détecter la police et taille actuelles
    const commonAncestor = range.commonAncestorContainer;
    let element =
      commonAncestor.nodeType === Node.TEXT_NODE
        ? commonAncestor.parentElement
        : commonAncestor;

    if (element && editorRef.current.contains(element)) {
      const computedStyle = window.getComputedStyle(element);
      const currentFontFamily = computedStyle.fontFamily;
      const currentFontSize = parseInt(computedStyle.fontSize);

      // Nettoyer le nom de la police (supprimer les guillemets et fallbacks)
      let cleanFontFamily = currentFontFamily
        .split(",")[0]
        .replace(/['"]/g, "")
        .trim();

      // Vérifier si c'est une police dans notre liste
      const matchedFont = fontFamilies.find(
        (font) =>
          cleanFontFamily.toLowerCase().includes(font.toLowerCase()) ||
          font.toLowerCase().includes(cleanFontFamily.toLowerCase())
      );

      setDetectedFont({
        family: matchedFont || cleanFontFamily,
        size: currentFontSize,
      });
      // console.log(matchedFont, "--", cleanFontFamily);
      // Mettre à jour les selects avec les valeurs détectées
      if (matchedFont) {
        setFontFamily(matchedFont);
      }

      if (currentFontSize && currentFontSize !== fontSize) {
        setFontSize(currentFontSize + "px");
      }
    }
  };

  // Fonction pour sauvegarder la sélection
  const saveSelection = () => {
    const selection = window.getSelection();
    if (selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      setSavedSelection({
        startContainer: range.startContainer,
        startOffset: range.startOffset,
        endContainer: range.endContainer,
        endOffset: range.endOffset,
      });

      // Vérifier les styles actifs après avoir sauvegardé la sélection
      setTimeout(checkActiveStyles, 10);
    }
  };

  // Fonction pour restaurer la sélection
  const restoreSelection = () => {
    if (savedSelection) {
      const selection = window.getSelection();
      const range = document.createRange();
      try {
        range.setStart(
          savedSelection.startContainer,
          savedSelection.startOffset
        );
        range.setEnd(savedSelection.endContainer, savedSelection.endOffset);
        selection.removeAllRanges();
        selection.addRange(range);
      } catch (error) {
        console.warn("Could not restore selection:", error);
      }
    }
  };

  // Fonction pour appliquer un style au curseur (nouveaux caractères)
  const applyStyleToCursor = (property, value) => {
    if (!editorRef.current) return;

    // S'assurer que l'éditeur a le focus
    editorRef.current.focus();

    const selection = window.getSelection();
    if (!selection.rangeCount) return;

    const range = selection.getRangeAt(0);

    // Créer un span invisible au point d'insertion
    const span = document.createElement("span");
    span.style[property] = value;
    span.innerHTML = "&#8203;"; // Caractère de largeur zéro

    // Insérer le span au curseur
    range.insertNode(span);

    // Positionner le curseur après le span
    range.setStartAfter(span);
    range.setEndAfter(span);
    selection.removeAllRanges();
    selection.addRange(range);
  };

  // Fonction ultra-robuste pour appliquer les styles
  const applyStyleToSelection = (property, value) => {
    // Restaurer la sélection d'abord
    restoreSelection();

    // Vérifier que l'éditeur est focalisé
    if (!editorRef.current) return;

    const selection = window.getSelection();
    if (!selection.rangeCount) return;

    const range = selection.getRangeAt(0);
    if (range.collapsed) {
      // Si pas de sélection, appliquer au curseur pour les nouveaux caractères
      applyStyleToCursor(property, value);
      return;
    }

    // Vérifier que la sélection est dans l'éditeur
    const editorElement = editorRef.current;
    if (!editorElement.contains(range.commonAncestorContainer)) {
      alert("Veuillez sélectionner du texte dans l'éditeur");
      return;
    }

    try {
      // Méthode robuste avec création de span
      const selectedContent = range.extractContents();
      const span = document.createElement("span");
      span.style[property] = value;
      span.appendChild(selectedContent);
      range.insertNode(span);

      // Nettoyer la sélection sauvegardée
      setSavedSelection(null);
    } catch (error) {
      console.warn("Style application failed:", error);
    }
  };

  const handleFontSizeChange = (size) => {
    setFontSize(size);
    applyStyleToSelection("fontSize", `${size}px`);
  };

  const handleFontFamilyChange = (family) => {
    setChangeFontfamily(true);
    setFontFamily(family);
    applyStyleToSelection("fontFamily", family);

    setTimeout(() => {
      setChangeFontfamily(false);
    }, 50);
  };

  // Fonction pour forcer le focus sur l'éditeur
  const ensureEditorFocus = () => {
    if (editorRef.current) {
      editorRef.current.focus();
    }
  };

  // Gestionnaire pour les événements de souris sur l'éditeur
  const handleEditorMouseUp = () => {
    // Sauvegarder la sélection après chaque sélection dans l'éditeur
    setTimeout(saveSelection, 10);
  };

  // Ajouter des écouteurs d'événements pour détecter les changements de sélection
  useEffect(() => {
    if (!changeFontFamily) {
      const handleSelectionChange = () => {
        if (editorRef.current && document.activeElement === editorRef.current) {
          setTimeout(checkActiveStyles, 10);
        }
      };

      const handleKeyUp = () => {
        if (editorRef.current && document.activeElement === editorRef.current) {
          setTimeout(checkActiveStyles, 10);
        }
      };

      document.addEventListener("selectionchange", handleSelectionChange);

      if (editorRef.current) {
        editorRef.current.addEventListener("keyup", handleKeyUp);
        editorRef.current.addEventListener("mouseup", handleEditorMouseUp);
      }

      return () => {
        if (!changeFontFamily) {
          document.removeEventListener(
            "selectionchange",
            handleSelectionChange
          );
          if (editorRef.current) {
            editorRef.current.removeEventListener("keyup", handleKeyUp);
            editorRef.current.removeEventListener(
              "mouseup",
              handleEditorMouseUp
            );
          }
        }
      };
    }
  }, [editorRef, changeFontFamily]);

  const fontFamilies = [
    "Arial",
    "Helvetica",
    "Times New Roman",
    "Georgia",
    "Verdana",
    "Trebuchet MS",
    "Impact",
    "Courier New",
  ];

  return (
    <div className="">
      {/* Instructions pour l'utilisateur */}
      <div className="mb-2 text-xs text-gray-500">
        💡 Sélectionnez du texte pour le modifier, ou changez la police/taille
        pour les nouveaux caractères
        {savedSelection && (
          <span className="ml-2 text-green-600">✓ Sélection sauvegardée</span>
        )}
        {detectedFont.family && (
          <span className="ml-2 text-blue-600">
            Police détectée: {detectedFont.family} ({detectedFont.size})
          </span>
        )}
      </div>
      {/* Formatage de texte */}
      <div className="flex flex-wrap gap-2 border-b pb-4">
        <Select
          size="small"
          style={{ width: 120 }}
          popupMatchSelectWidth={false}
          value={fontFamily}
          onChange={handleFontFamilyChange}
          placeholder="Police"
          onDropdownVisibleChange={(open) => {
            if (open) saveSelection();
          }}
        >
          {fontFamilies.map((font) => (
            <Select.Option key={font} value={font}>
              <span style={{ fontFamily: font }}>{font}</span>
            </Select.Option>
          ))}
        </Select>

        <Select
          size="small"
          popupMatchSelectWidth={false}
          style={{ width: 80 }}
          value={fontSize}
          onChange={handleFontSizeChange}
          placeholder="Taille"
          onDropdownVisibleChange={(open) => {
            if (open) saveSelection();
          }}
        >
          {[8, 10, 12, 14, 16, 18, 20, 24, 28, 32, 36, 48].map((size) => (
            <Select.Option key={size} value={size}>
              {size}px
            </Select.Option>
          ))}
        </Select>

        <div className="mx-1 h-6 w-px bg-gray-300" />

        <Button
          onClick={() => {
            execCommand("bold");
            setActiveStyles((prev) => ({ ...prev, bold: !prev.bold }));
          }}
          icon={<BoldOutlined />}
          title="Gras"
          size="small"
          type={activeStyles.bold ? "primary" : "default"}
        />
        <Button
          onClick={() => {
            execCommand("italic");
            setActiveStyles((prev) => ({ ...prev, italic: !prev.italic }));
          }}
          icon={<ItalicOutlined />}
          title="Italique"
          size="small"
          type={activeStyles.italic ? "primary" : "default"}
        />
        <Button
          onClick={() => {
            execCommand("underline");
            setActiveStyles((prev) => ({
              ...prev,
              underline: !prev.underline,
            }));
          }}
          icon={<UnderlineOutlined />}
          title="Souligné"
          size="small"
          type={activeStyles.underline ? "primary" : "default"}
        />
        {/* <Button
          onClick={() => {
            execCommand("strikeThrough");
            setActiveStyles((prev) => ({
              ...prev,
              strikeThrough: !prev.strikeThrough,
            }));
          }}
          icon={<StrikethroughOutlined />}
          title="Barré"
          size="small"
          type={activeStyles.strikeThrough ? "primary" : "default"}
        /> */}

        <div className="mx-1 h-6 w-px bg-gray-300" />

        <Button
          onClick={() => {
            execCommand("justifyLeft");
            setActiveStyles((prev) => ({
              ...prev,
              justifyLeft: true,
              justifyCenter: false,
              justifyRight: false,
            }));
          }}
          icon={<AlignLeftOutlined />}
          title="Aligner à gauche"
          size="small"
          type={activeStyles.justifyLeft ? "primary" : "default"}
        />
        <Button
          onClick={() => {
            execCommand("justifyCenter");
            setActiveStyles((prev) => ({
              ...prev,
              justifyCenter: true,
              justifyLeft: false,
              justifyRight: false,
            }));
          }}
          icon={<AlignCenterOutlined />}
          title="Centrer"
          size="small"
          type={activeStyles.justifyCenter ? "primary" : "default"}
        />
        <Button
          onClick={() => {
            execCommand("justifyRight");
            setActiveStyles((prev) => ({
              ...prev,
              justifyRight: true,
              justifyLeft: false,
              justifyCenter: false,
            }));
          }}
          icon={<AlignRightOutlined />}
          title="Aligner à droite"
          size="small"
          type={activeStyles.justifyRight ? "primary" : "default"}
        />

        <div className="mx-1 h-6 w-px bg-gray-300" />

        <Button
          onClick={() => {
            execCommand("insertOrderedList");
            setActiveStyles((prev) => ({
              ...prev,
              insertOrderedList: !prev.insertOrderedList,
            }));
          }}
          icon={<OrderedListOutlined />}
          title="Liste numérotée"
          size="small"
          type={activeStyles.insertOrderedList ? "primary" : "default"}
        />
        <Button
          onClick={() => {
            execCommand("insertUnorderedList");
            setActiveStyles((prev) => ({
              ...prev,
              insertUnorderedList: !prev.insertUnorderedList,
            }));
          }}
          icon={<UnorderedListOutlined />}
          title="Liste à puces"
          size="small"
          type={activeStyles.insertUnorderedList ? "primary" : "default"}
        />

        <div className="mx-1 h-6 w-px bg-gray-300" />

        <Button
          onClick={handleLinkClick}
          icon={<LinkOutlined />}
          title="Insérer un lien"
          size="small"
        />
        <Button
          onClick={handleImageUpload}
          icon={<UploadOutlined />}
          title="Upload une image"
          size="small"
        />
        <Button
          onClick={insertImageFromUrl}
          icon={<PictureOutlined />}
          title="Insérer une image par URL"
          size="small"
        />
        <Button
          onClick={openButtonModal}
          icon={<MdSmartButton />}
          title="Insérer un bouton"
          size="small"
        />
        <Button
          onClick={openHtmlModal}
          icon={<CodeOutlined />}
          title="Insérer du HTML"
          size="small"
        />
        <Button
          onClick={() => execCommand("formatBlock", "blockquote")}
          icon={<QuestionCircleOutlined />}
          title="Citation"
          size="small"
        />
        {colors.map((color) => (
          <Button
            key={color}
            onClick={() => handleColorChange(color)}
            className={
              selectedColor === color ? "border-gray-400" : "border-gray-200"
            }
            style={{
              backgroundColor: color,
              width: "24px",
              height: "24px",
              minWidth: "unset",
              padding: 0,
              border:
                selectedColor === color ? "2px solid #666" : "1px solid #ddd",
            }}
            title={`Couleur ${color}`}
          />
        ))}
        <TableEditor
          editorRef={editorRef}
          savedSelection={savedSelection}
          setContent={setContent}
        />
      </div>
    </div>
  );
};

export default EditorToolbar;
