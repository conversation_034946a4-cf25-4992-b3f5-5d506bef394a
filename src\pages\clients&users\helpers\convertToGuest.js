import { Form, Input, Modal } from "antd";
import { convertToGuest } from "../services/services";
import { SendOutlined, WarningOutlined } from "@ant-design/icons";
import { truncateString } from "pages/voip/helpers/helpersFunc";
import { toastNotification } from "components/ToastNotification";
import { roles } from "utils/role";
import { useEffect, useRef, useState } from "react";
import { FiMail } from "react-icons/fi";

const convertContactToGuest = async (
  t,
  id,
  label,
  triggerUpdate = () => {},
  showPopupConfirm = true,
  role
) => {
  //
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  //
  const processConvert = async (email = "", close = () => {}) => {
    try {
      const formData = new FormData();
      formData.append("contact_id", id);
      formData.append("email", email);
      await convertToGuest(formData);
      close();
      triggerUpdate(true);
    } catch (err) {
      const status = err?.response?.status;
      const msg = err?.response?.data?.message;
      // console.log({ err, status, msg });
      switch (status) {
        case 422:
          switch (msg) {
            case "The email field is required.":
              openEnterEmailModal(false);
              // openEnterEmailModal(true, false, "<EMAIL>");
              return;

            case "The email has already been taken .":
              close();
              openEnterEmailModal(true, email);
              return;
            default:
              toastNotification(
                "error",
                t("toasts.somethingWrong"),
                "topRight"
              );
              return;
          }
        case 500:
          const error = err?.response?.data;
          const message = error?.error;
          const emailError = error?.email;
          if (message === "email has already been taken") {
            close();
            openEnterEmailModal(true, emailError);
            return;
          } else {
            toastNotification("error", t("toasts.somethingWrong"), "topRight");
            return;
          }
        case 401:
          return;
        default:
          toastNotification("error", t("toasts.somethingWrong"), "topRight");
          return;
      }
    }
  };
  //
  const openEnterEmailModal = (
    showEmailTakenError = false,
    prefilledEmail = ""
  ) => {
    let localEmail = prefilledEmail;
    //
    return Modal.confirm({
      icon: <WarningOutlined style={{ color: "#faad14", fontSize: 20 }} />,
      title: showEmailTakenError
        ? t("contacts.emailTakenError")
        : t("contacts.enterEmailTitle"),
      content: (
        <EnterEmailFormModal
          t={t}
          showEmailTakenError={showEmailTakenError}
          prefilledEmail={prefilledEmail}
          localEmail={localEmail}
        />
      ),
      onOk: (close) => {
        return new Promise((resolve, reject) => {
          document.querySelector(".ant-modal-content form")?.requestSubmit();
          const form = document.querySelector(".ant-modal-content form");
          const emailInput = form?.querySelector("#email");
          const emailValue = emailInput?.value;

          if (emailValue && emailRegex.test(emailValue)) {
            processConvert(emailValue, close)
              .then(() => resolve())
              .catch(() => reject());
          } else {
            reject();
          }
        });
      },
      okText: t("profile.confirm"),
      cancelText: t("profile.cancel"),
    });
  };

  //console.log(role, roles.includes(role));
  if (!roles.includes(role)) {
    toastNotification("error", t("contacts.inviteNotAllowed"), "topRight");
    return;
  }
  //
  if (showPopupConfirm) {
    const confirm = Modal.confirm({
      icon: <SendOutlined style={{ color: "rgb(22, 119, 255)" }} />,
      title: (
        <p className="truncate">
          {t("contacts.inviteContacts", {
            name: truncateString(label, 19),
          })}
        </p>
      ),
      content: t("contacts.deleteConfirmMsg"),
      okText: t("profile.confirm"),
      cancelText: t("profile.cancel"),
      onCancel() {},
      onOk: async () => await processConvert(),
    });
    return confirm;
  } else {
    await processConvert();
  }
};

export const EnterEmailFormModal = ({ t, showEmailTakenError, localEmail }) => {
  //
  const inputRef = useRef();
  const [form] = Form.useForm();
  //
  const [email, setEmail] = useState(localEmail ?? "");
  //
  useEffect(() => {
    setTimeout(() => {
      showEmailTakenError &&
        form.setFields([
          {
            name: "email",
            errors: [t("contacts.emailTakenError")],
          },
        ]);
      if (inputRef.current) {
        showEmailTakenError
          ? inputRef.current.select()
          : inputRef.current.focus();
      }
    }, 100);
  }, [form, showEmailTakenError, t]);
  //

  //
  return (
    <Form layout="vertical" form={form}>
      <Form.Item
        name="email"
        label="Email"
        required
        initialValue={email}
        rules={[
          {
            type: "email",
            message: t("contacts.enterValidEmail"),
          },
          {
            required: true,
            message: t("contacts.fieldXRequired", { x: "Email" }),
          },
        ]}
      >
        <Input
          ref={inputRef}
          type="email"
          autoComplete="off"
          prefix={<FiMail className="mr-1 h-5 w-5" />}
          placeholder={t("mailing.ValidMail")}
          onChange={(e) => {
            const value = e.target.value;
            localEmail = value;
            setEmail(value);
          }}
        />
      </Form.Item>
    </Form>
  );
};

export default convertContactToGuest;
