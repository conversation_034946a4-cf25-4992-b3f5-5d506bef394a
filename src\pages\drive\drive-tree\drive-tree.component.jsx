import React, { useState } from "react";
import { Tree, Spin, Alert, Dropdown, Modal, Input, Form, Progress, Card } from "antd";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import {  getTreeDrive, getDriveStorageInfo } from "../../../services/main.service";
import { PiFolderDuotone } from "react-icons/pi";
import { TbFolderFilled } from "react-icons/tb";
import { DownOutlined, PlusOutlined, EditOutlined, DeleteOutlined, ExclamationCircleOutlined, CloudOutlined, FileOutlined, FolderOutlined } from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";
import { setDriveSelectedFolder, setDriveBreadcrumb, setDriveParentItems, setDriveTreeData, updateDriveTreeNode } from "../../../new-redux/actions/drive.actions/drive";
import { 
  createFolderInTree, 
  deleteFolderFromTree, 
  renameFolderInTree, 
  addNodeToTree, 
  removeNodeFromTree, 
  updateNodeInTree 
} from "../drive-helpers/functions";
import "./drive-tree.css";

const DriveTree = ({ isError: parentIsError, setPagination }) => {
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  const selectedFolder = useSelector((state) => state.drive.selectedFolder);
  const parentItem = useSelector((state) => state.drive.parentItem);
  const treeData = useSelector((state) => state.drive.treeData);
  
  // Modal states
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);
  const [isRenameModalVisible, setIsRenameModalVisible] = useState(false);
  const [contextMenuNode, setContextMenuNode] = useState(null);
  const [form] = Form.useForm();
 

  const {
    data: treeResponse,
    isLoading: treeLoading,
    isError: treeError,
  } = useQuery({
    queryKey: ["drive-tree"],
    queryFn: ({ signal }) => getTreeDrive(),
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    retry: false,
    select: (data) => [{
      title: t("drive.myDrive"),
      key: "1",
      isLeaf: false,
      children:
        Object?.entries(data?.data || {})?.map(([id, name]) => ({
          title: name,
          key: id,
          children: [],
          isLeaf: false,
        })) || [],
    }],
    onSuccess:(data)=>{
      dispatch(setDriveTreeData(data));
    }
  });

  // Storage info query
  const {
    data: storageInfo,
    isLoading: storageLoading,
    isError: storageError,
  } = useQuery({
    queryKey: ["drive-storage-info"],
    queryFn: ({ signal }) => getDriveStorageInfo(),
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    retry: false,
    refetchInterval: 100000,
  });

  // Mutations
  const createFolderMutation = useMutation({
    mutationFn: ({ folderName, parentId }) => createFolderInTree(folderName, parentId, t),
    onSuccess: (response, { parentId }) => {
      if (response?.success && response?.data) {
        const newNode = {
          title: response.data.name,
          key: response.data.id,
          children: [],
          isLeaf: false,
        };
        
        const updatedTreeData = addNodeToTree(treeData, parentId, newNode);
        dispatch(setDriveTreeData(updatedTreeData));
        queryClient.invalidateQueries(["drive-items"]);
      }
      setIsCreateModalVisible(false);
      form.resetFields();
    },
    onError: (error) => {
      console.error("Create folder error:", error);
    }
  });

  const deleteFolderMutation = useMutation({
    mutationFn: (folderId) => deleteFolderFromTree(folderId, t),
    onSuccess: (response, folderId) => {
      const updatedTreeData = removeNodeFromTree(treeData, folderId);
      dispatch(setDriveTreeData(updatedTreeData));
      
      // If deleted folder was selected, select parent or root
      if (selectedFolder === folderId) {
        dispatch(setDriveSelectedFolder("1"));
        dispatch(setDriveParentItems(""));
        dispatch(setDriveBreadcrumb([{ id: "", name: t("drive.myDrive"), path: "" }]));
      }
      
      queryClient.invalidateQueries(["drive-items"]);
    },
    onError: (error) => {
      console.error("Delete folder error:", error);
    }
  });

  const renameFolderMutation = useMutation({
    mutationFn: ({ folderId, newName }) => renameFolderInTree(folderId, newName, t),
    onSuccess: (response, { folderId, newName }) => {
      const updatedTreeData = updateNodeInTree(treeData, folderId, { title: newName });
      dispatch(setDriveTreeData(updatedTreeData));
      queryClient.invalidateQueries(["drive-items"]);
      setIsRenameModalVisible(false);
      form.resetFields();
    },
    onError: (error) => {
      console.error("Rename folder error:", error);
    }
  });

 

  const loadData = async (treeNode) => {
    const { key } = treeNode;
    if(key==="1") return;

    try {
      const response = await getTreeDrive(key);
      if (response?.success && response?.data) {
        const subfolders = response.data;

        const newChildren = Object.entries(subfolders).length !== 0
          ? Object.entries(subfolders).map(([id, name]) => ({
              title: name,
              key: id,
              children: [],
              isLeaf: false,
            }))
          : [];
        
        dispatch(updateDriveTreeNode(key, newChildren));
        
      }
    } catch (error) {
      console.error("Error loading subfolder data:", error);
    }
  };

  // Function to build breadcrumb path from tree data
  const buildBreadcrumbPath = (targetKey, treeData, currentPath = []) => {
    for (const node of treeData) {
      const newPath = [...currentPath, { id: node.key, name: node.title, path: node.key }];
      
      if (node.key === targetKey) {
        return newPath;
      }
      
      if (node.children && node.children.length > 0) {
        const result = buildBreadcrumbPath(targetKey, node.children, newPath);
        if (result) {
          return result;
        }
      }
    }
    return null;
  };

  const handleTreeSelect = (selectedKeys) => {
    if (selectedKeys.length > 0) {
      const selectedKey = selectedKeys[0];
      dispatch(setDriveSelectedFolder(selectedKey));
      dispatch(setDriveParentItems(selectedKey === "1" ? "" : selectedKey));
      setPagination(1);
      if (selectedKey === "1") {
        dispatch(setDriveBreadcrumb([{ id: "", name: t("drive.myDrive"), path: "" }]));
      } else {
        const breadcrumbPath = buildBreadcrumbPath(selectedKey, treeData);
        if (breadcrumbPath) {
          const updatedPath = [
            { id: "", name: t("drive.myDrive"), path: "" },
            ...breadcrumbPath.slice(1) // Skip the "1" root node
          ];
          dispatch(setDriveBreadcrumb(updatedPath));
        }
      }
    }
  };

  // Context menu handlers
  const handleCreateFolder = (node) => {
    setContextMenuNode(node);
    setIsCreateModalVisible(true);
  };

  const handleRenameFolder = (node) => {
    if (node.key === "1") return; // Cannot rename root
    setContextMenuNode(node);
    form.setFieldsValue({ name: node.title });
    setIsRenameModalVisible(true);
  };

  const handleDeleteFolder = (node) => {
    if (node.key === "1") return; // Cannot delete root
    
    Modal.confirm({
      title: t("drive.confirmDeleteFolderTitle"),
      content: t("drive.confirmDeleteFolder"),
      icon: <ExclamationCircleOutlined />,
      okText: t("drive.delete"),
      cancelText: t("drive.deleteConfirmation.cancel"),
      okType: "danger",
      onOk: () => {
        deleteFolderMutation.mutate(node.key);
      },
    });
  };

  // Modal handlers
  const handleCreateSubmit = (values) => {
    const parentId = contextMenuNode?.key || "1";
    createFolderMutation.mutate({
      folderName: values.name,
      parentId: parentId
    });
  };

  const handleRenameSubmit = (values) => {
    if (contextMenuNode) {
      renameFolderMutation.mutate({
        folderId: contextMenuNode.key,
        newName: values.name
      });
    }
  };

  // Generate context menu items
  const getContextMenuItems = (node) => [
    {
      key: 'add',
      label: t("drive.addFolder"),
      icon: <PlusOutlined />,
      onClick: () => handleCreateFolder(node),
    },
    ...(node.key !== "1" ? [
      {
        key: 'rename',
        label: t("drive.rename"),
        icon: <EditOutlined />,
        onClick: () => handleRenameFolder(node),
      },
      {
        key: 'delete',
        label: t("drive.delete"),
        icon: <DeleteOutlined />,
        danger: true,
        onClick: () => handleDeleteFolder(node),
      }
    ] : [])
  ];


  const handleRightClick = ({ event, node }) => {
    event.preventDefault();
    
  };

  // Storage Info Component
  const StorageInfo = () => {
    if (storageLoading || storageError || !storageInfo?.data) {
      return null;
    }

    const data = storageInfo.data.data;
    const usedPercentage = parseFloat(data.used_percentage?.replace('%', '').replace(',', '.')) || 0;
    const normalizedPercentage = Math.min(usedPercentage, 100); // Cap at 100% for display
    
    return (
      <Card size="small" className="mt-2 mx-2 shadow-sm">
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <CloudOutlined className="text-blue-500" />
            <span className="font-medium text-sm">{t("drive.storage") || "Storage"}</span>
          </div>
          
            <Progress
              percent={normalizedPercentage}
              size="small"
              status={normalizedPercentage > 90 ? "exception" : normalizedPercentage > 75 ? "normal" : "success"}
              showInfo={false}
              style={{marginTop:"0px"}}
            />
            
            <div className="flex justify-between items-center text-xs text-gray-600">
              <span>{data.used_space}</span>
              <span>{t("stat.of") || "of"} {data.total_space}</span>
            </div>         

          <div className="grid grid-cols-2 gap-2  border-t border-gray-100">
            <div className="flex items-center gap-1 text-xs text-gray-600">
              <FileOutlined className="text-blue-400" />
              <span>{data.file_count} {t("drive.files") || "files"}</span>
            </div>
            <div className="flex items-center gap-1 text-xs text-gray-600">
              <FolderOutlined className="text-yellow-500" />
              <span>{data.folder_count} {t("drive.folders") || "folders"}</span>
            </div>
          </div>
        </div>
      </Card>
    );
  };



  if (parentIsError || treeError) {
    return (
      <div className="flex h-full items-center justify-center p-4">
        <Alert
          message="Error"
          description={t("drive.failedToLoadItems")}
          type="error"
          showIcon
        />
      </div>
    );
  }

  return (
    <div className="drive-tree-container p-2 h-full flex flex-col">
      <div className="drive-tree-header">
        <h3 className="text-lg font-medium">{t("drive.folders")}</h3>
      </div>
      <div className="h-[55%] overflow-auto">
        <Spin spinning={treeLoading}>
        {treeResponse && (
          <Tree
            icon={(data) => {
              return data.selected ? (
                <TbFolderFilled style={{ fontSize: "24px" }} />
              ) : (
                <PiFolderDuotone style={{ fontSize: "24px" }} />
              );
            }}
            showIcon
            defaultExpandedKeys={["1"]}
            treeData={treeData || []}
            switcherIcon={<DownOutlined />}
            defaultExpandParent
            selectedKeys={[selectedFolder || (parentItem ? parentItem : "1")]}
            onSelect={handleTreeSelect}
            onRightClick={handleRightClick}
            className="drive-tree bg-transparent"
            loadData={loadData}
            expandAction={"doubleClick"}
            titleRender={(node) => (
              <Dropdown
                menu={{ items: getContextMenuItems(node) }}
                trigger={['contextMenu']}
              >
                <span className="tree-node-title">{node.title}</span>
              </Dropdown>
            )}
          />
        )}
        </Spin>
      </div>

      {/* Storage Information */}
      <StorageInfo />

    
      <Modal
        title={t("drive.addFolder")}
        open={isCreateModalVisible}
        onCancel={() => {
          setIsCreateModalVisible(false);
          form.resetFields();
        }}
        footer={null}
        destroyOnClose
      >
        <Form
          form={form}
          onFinish={handleCreateSubmit}
          layout="vertical"
          autoComplete="off"
        >
          <Form.Item
            name="name"
            label={t("drive.createFolderModal.folderName")}
            rules={[
              { required: true, message: t("drive.createFolderModal.required") },
              { min: 1, message: t("drive.createFolderModal.tooShort") },
              { max: 50, message: t("drive.createFolderModal.tooLong") }
            ]}
          >
            <Input
              placeholder={t("drive.enterFolderName")}
              autoFocus
            />
          </Form.Item>
          <Form.Item className="mb-0 flex justify-end">
            <div className="space-x-2">
              <button
                type="button"
                onClick={() => {
                  setIsCreateModalVisible(false);
                  form.resetFields();
                }}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                {t("drive.createFolderModal.cancel")}
              </button>
              <button
                type="submit"
                disabled={createFolderMutation.isLoading}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {createFolderMutation.isLoading ? t("drive.processing") : t("drive.createFolderModal.create")}
              </button>
            </div>
          </Form.Item>
        </Form>
      </Modal>

     
      <Modal
        title={t("drive.renameModal.title", { name: contextMenuNode?.title || "" })}
        open={isRenameModalVisible}
        onCancel={() => {
          setIsRenameModalVisible(false);
          form.resetFields();
        }}
        footer={null}
        destroyOnClose
      >
        <Form
          form={form}
          onFinish={handleRenameSubmit}
          layout="vertical"
          autoComplete="off"
        >
          <Form.Item
            name="name"
            label={t("drive.renameModal.name")}
            rules={[
              { required: true, message: t("drive.renameModal.required") },
              { min: 2, message: t("drive.renameModal.minText") },
              { max: 30, message: t("drive.renameModal.maxText") }
            ]}
          >
            <Input
              placeholder={t("drive.renameModal.placeholder")}
              autoFocus
            />
          </Form.Item>
          <Form.Item className="mb-0 flex justify-end">
            <div className="space-x-2">
              <button
                type="button"
                onClick={() => {
                  setIsRenameModalVisible(false);
                  form.resetFields();
                }}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                {t("drive.renameModal.cancel")}
              </button>
              <button
                type="submit"
                disabled={renameFolderMutation.isLoading}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {renameFolderMutation.isLoading ? t("drive.processing") : t("drive.renameModal.save")}
              </button>
            </div>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default DriveTree;
