import { Modal } from "antd";
import { SendOutlined } from "@ant-design/icons";
import { sendUserInvite } from "../services/services";
import { toastNotification } from "../../../components/ToastNotification";

const sendInvitation = async (
  t,
  elementID,
  label,
  selectedRowKeys,
  setSelectedRowKeys,
  showPopup = true
) => {
  const processInvite = async () => {
    try {
      const formData = new FormData();
      const elementsToDelete = elementID ? [elementID] : selectedRowKeys;
      elementsToDelete?.forEach((e) => formData.append("ids[]", e));
      const { status } = await sendUserInvite(formData);

      if (status === 200) {
        !elementID && setSelectedRowKeys([]);
        toastNotification(
          "success",
          <div className="flex flex-col">
            <p>{t("contacts.inviteUserProcess1")}</p>
            <p>{t("contacts.inviteUserProcess2")}</p>
          </div>,
          "topRight",
          5,
          null,
          null,
          "processInviteUser"
        );
      }
    } catch (err) {
      const status = err?.response?.status;
      switch (status) {
        case 401:
          return;
        default:
          toastNotification("error", t("toasts.somethingWrong"), "topRight");
          return;
      }
    }
  };

  if (showPopup) {
    const confirm = Modal.confirm({
      icon: <SendOutlined style={{ color: "rgb(22, 119, 255)" }} />,
      title: (
        <p className="truncate">{`${t("contacts.invite")} ${
          label ? label : `${selectedRowKeys?.length} ${t("contacts.users")}`
        }`}</p>
      ),
      content: t("contacts.deleteConfirmMsg"),
      okText: t("profile.confirm"),
      cancelText: t("profile.cancel"),
      onCancel() {},
      onOk: async () => await processInvite(),
    });
    return confirm;
  } else {
    await processInvite();
  }
};

export default sendInvitation;
