import React, { useEffect, useRef } from "react";
import { Form, Space, Tag, Switch, Table } from "antd";
import { useState } from "react";
import { generateAxios } from "../services/axiosInstance";
import { useTranslation } from "react-i18next";
import { toastNotification } from "./ToastNotification";
import { useDispatch, useSelector } from "react-redux";
import SearchInTable from "../pages/components/Search";
import { setSearch } from "../new-redux/actions/menu.actions/menu";
import FilterCallLogs from "../pages/voip/components/FilterCallLogs";
import FilterTable from "./FilterTable";
import { URL_ENV } from "index";
const TableCountries = () => {
  const [loadSwitchUsed, setLoadSwitchUsed] = useState(false);
  const [switchId, setSwitchId] = useState("");
  const [data, setData] = useState([]);
  const [allData, setAllData] = useState([]);
  const [openFilterTable, setOpenFilterTable] = useState(false);
  const [loading, setLoading] = useState(true);
  const [t] = useTranslation("common");
  const { search } = useSelector((state) => state.form);
  const dispatch = useDispatch();
  const maxHeight = `calc(100vh - 300px)`;
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
  });
  const [sorter, setSorter] = useState({
    field: null,
    order: null,
  });
  const [filter, setFilter] = useState({});
  const changeUsed = async (checked, event, reccord) => {
    setSwitchId(reccord.id);
    setLoadSwitchUsed(true);
    if (checked) {
      let formData = new FormData();
      formData.append("selected", 1);
      try {
        const {
          data: { data },
        } = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post(
          `countries_selected/update/${reccord.id}
      `,

          formData
        );
        setData(
          (previous) => previous.map((el) => (el.id == reccord.id ? data : el))
          //.sort((a,b)=>b.selected-a.selected)
        );
        setAllData((previous) =>
          previous.map((el) => (el.id == reccord.id ? data : el))
        );
        setLoadSwitchUsed(false);
        setSwitchId("");
        toastNotification(
          "success",
          reccord.name_en + t("toasts.used"),
          "topRight"
        );
      } catch (err) {
        setSwitchId("");

        setLoadSwitchUsed(false);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    } else {
      let formData = new FormData();
      formData.append("selected", 0);
      try {
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post(
          `countries_selected/update/${reccord.id}
        `,

          formData
        );
        setData(data.map((el) => (el.id == reccord.id ? res.data.data : el)));
        setAllData((prev) =>
          prev.map((el) => (el.id == reccord.id ? res.data.data : el))
        );
        //.sort((a,b)=>b.selected-a.selected));
        setLoadSwitchUsed(false);
        setSwitchId("");

        toastNotification(
          "success",
          reccord.name_en + t("toasts.notUsed"),
          "topRight"
        );
      } catch (err) {
        setLoadSwitchUsed(false);
        setSwitchId("");
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    }
  };
  const sortData = (data, sortField, sortOrder) => {
    // Trier les données en fonction de la colonne sélectionnée par l'utilisateur
    let compareFn;
    if (sortField === "selected")
      compareFn = (a, b) =>
        sortOrder === "ascend"
          ? a[sortField] - b[sortField]
          : sortOrder === "descend"
          ? b[sortField] - a[sortField]
          : data;
    else
      compareFn = (a, b) =>
        sortOrder === "ascend"
          ? a[sortField].localeCompare(b[sortField])
          : sortOrder === "descend"
          ? b[sortField].localeCompare(a[sortField])
          : data;
    return data.filter((el) => el.name_en).sort(compareFn);
  };
  useEffect(() => {
    const getCountries = async () => {
      setLoading(true);
      try {
        const {
          data: { data },
        } = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get("countries");
        setAllData(data.sort((a, b) => b.selected - a.selected));
        setData(
          sortData(
            data.filter((object) => {
              if (filter.selected && filter.selected.length > 0) {
                return filter.selected.includes(object.selected.toString());
              } else {
                return object;
              }
            }),
            sorter.field,
            sorter.order
          )
        );

        //.sort((a,b)=>b.selected-a.selected));
        setLoading(false);
      } catch (err) {
        setLoading(false);
      }
    };
    getCountries();
    return () => dispatch(setSearch(""));
  }, []);

  useEffect(() => {
    setData(
      sortData(
        allData.filter((object) => {
          if (filter.selected && filter.selected.length > 0) {
            return filter.selected.includes(object.selected.toString());
          } else {
            return object;
          }
        }),
        sorter.field,
        sorter.order
      )
    );
  }, [sorter, filter, allData]);

  const handleTableChange = (pagination, filters, sorter) => {
    setPagination(pagination);
    setSorter({
      field: sorter.field,
      order: sorter.order,
    });
    setFilter(filters);
  };
  let columns = [
    {
      title: t("table.header.name"),
      dataIndex: "name_en",
      key: "name_en",
      editable: true,
      sorter: true,
    },
    {
      title: t("table.header.region"),
      dataIndex: "region",
      key: "region",
      editable: true,
      sorter: true,
    },
    // {
    //   title: t("table.header.currency"),
    //   dataIndex: "currency",
    //   key: "currency",
    //   editable: true,
    //   sorter: true,
    // },
    {
      title: t("table.header.prefix"),
      dataIndex: "dial_code",
      key: "dial_code",
      editable: true,
      sorter: (a, b) => a.dial_code - b.dial_code,
    },
    {
      title: t("table.header.flag"),
      dataIndex: "flag",
      key: "flag",
      editable: true,
    },
    {
      title: t("table.header.timezone"),
      dataIndex: "timezones",
      key: "timezones",
      editable: true,
      render: (_, { timezones }) => (
        <div>
          {timezones &&
            JSON.parse(timezones).map((timezone, i) => (
              <Tag key={i}>{timezone}</Tag>
            ))}
        </div>
      ),
    },
    {
      title: t("sales.used"),
      dataIndex: "selected",
      key: "selected",
      sorter: true,
      defaultSortOrder: "descend",

      // sorter: (a, b) => b.selected - a.selected,
      // filters: openFilterTable
      //   ? null
      //   : [
      //       {
      //         text: t(`sales.used`),
      //         value: "1",
      //       },
      //       {
      //         text: t(`sales.unused`),
      //         value: "0",
      //       },
      //     ],
      // onFilter: (value, record) => record.selected == value,
      render: (_, reccord) => (
        <Switch
          loading={reccord.id == switchId ? loadSwitchUsed : ""}
          // disabled={reccord.selected == 1}
          size="small"
          defaultChecked={reccord?.selected == 1 ? true : false}
          checked={reccord?.selected == 1 ? true : false}
          onChange={(checked, event) => changeUsed(checked, event, reccord)}
        />
      ),
    },
  ];
  const sortedData = sortData(data, sorter.field, sorter.order);
  const paginatedData = sortedData.slice(
    (pagination.current - 1) * pagination.pageSize,
    pagination.current * pagination.pageSize
  );
  const filteredData = sortedData.filter((item) => {
    return (
      item.name_en.toLowerCase().includes(search.toLowerCase()) ||
      item.region.toLowerCase().includes(search.toLowerCase()) ||
      item.currency.toLowerCase().includes(search.toLowerCase()) ||
      item.dial_code.toLowerCase().includes(search.toLowerCase())
    );
  });

  return (
    <Space direction="vertical" style={{ width: "100%" }}>
      <div className="flex w-full space-x-1 px-4">
        <SearchInTable />
        <FilterTable
          setFilter={setFilter}
          selectedFilter={filter?.selected}
          filters={[
            {
              text: t(`sales.used`),
              value: "1",
            },
            {
              text: t(`sales.unused`),
              value: "0",
            },
          ]}
          t={t}
          openFilterTable={openFilterTable}
          setOpenFilterTable={setOpenFilterTable}
        />
      </div>
      <Table
        scroll={{ y: maxHeight }}
        // dataSource={filteredData.slice(
        //   (pagination.current - 1) * pagination.pageSize,
        //   pagination.current * pagination.pageSize
        // )}
        dataSource={filteredData}
        columns={columns}
        pagination={{ ...pagination, total: filteredData.length }}
        loading={loading}
        onChange={handleTableChange}
      />
    </Space>
  );
};
export default TableCountries;
