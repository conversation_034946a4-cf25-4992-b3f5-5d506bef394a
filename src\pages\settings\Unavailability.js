import React, { memo, useCallback, useEffect, useRef } from "react";
import { Button, DatePicker, Form, Input, Select, Space } from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";

import { useDispatch, useSelector } from "react-redux";
import { generateAxios } from "../../services/axiosInstance";
import { toastNotification } from "../../components/ToastNotification";
import { setSearch } from "../../new-redux/actions/menu.actions/menu";
import BottomButtonAddRow from "../../components/BottomButtonAddRow";
import NewTableDraggable from "../../components/NewTableDraggable";
import Header from "../../components/configurationHelpDesk/Header";
import moment from "moment";
import dayjs from "dayjs";
import { SubmitKeyPress } from "../../utils/SubmitKeyPress";
import { URL_ENV } from "index";

const Unavailability = () => {
  const [form] = Form.useForm();
  const [count, setCount] = useState(0);
  const [data, setData] = useState([]);
  const [editingKey, setEditingKey] = useState("");
  const [id, setId] = useState(null);
  const [loading, setLoading] = useState(true);
  const isEditing = (record) => record.key === editingKey;
  const [t] = useTranslation("common");
  const inputRefs = useRef([]);
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [maxDate, setMaxDate] = useState("");
  const [dateSelected, setDataSelected] = useState("");

  const [changeStartDate, setChangeStartDate] = useState(false);

  const [nature, setNature] = useState([]);
  const [choiceNature, setChoiceNature] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [oldPage, setOldPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [sorter, setSorter] = useState({
    field: null,
    order: null,
  });
  const dispatch = useDispatch();
  const { search } = useSelector((state) => state.form);
  const range = (start, end) => {
    const result = [];
    for (let i = start; i < end; i++) {
      result.push(i);
    }
    return result;
  };
  useEffect(() => {
    inputRefs.current.forEach((input) => {
      input?.focus();
    });
  }, [data.length, id]);

  const filteredData = data.filter((item) => {
    return item?.nature?.toLowerCase().includes(search.toLowerCase());
  });
  // useEffect(() => {
  //   if (editingKey && oldPage !== currentPage) {
  //     setData((prev) => prev.filter((el) => el.id));
  //     setEditingKey("");
  //   }
  // }, [currentPage, editingKey, oldPage]);
  const handleKeyPress = (event) => {
    SubmitKeyPress(event, form);
  };
  const onFinishFailed = (values) => {
    console.log(values);
  };

  function getNombreJours(dateDebut, dateFin) {
    let jours = 0;
    let currentDate = moment(dateDebut);

    while (currentDate.isSameOrBefore(dateFin)) {
      if (currentDate.day() !== 0 && currentDate.day() !== 6) {
        jours++;
      }
      currentDate.add(1, "day");
    }
    return jours;
  }
  const selectedDate = (dates, dateStrings, dataIndex) => {
    if (dataIndex === "start_date") {
      const dateTime1 = dayjs(dateStrings).format("YYYY-MM-DD ");
      const dateTime2 = dayjs(endDate).format("YYYY-MM-DD ");
      setChangeStartDate(true);
      if (!endDate) {
        form.setFieldsValue({
          end_date: dayjs(dateStrings, "YYYY-MM-DD HH:mm").add(1, "minute"),
        });
      }
      if (
        (t(`unavailability.${choiceNature}`) ===
          t("unavailability.Authorization") ||
          t(`unavailability.${choiceNature}`) === t("unavailability.Other")) &&
        endDate
      ) {
        if (dayjs(dateTime1).isSame(dayjs(dateTime2)))
          form.setFieldsValue({
            end_date: dayjs(dateStrings, "YYYY-MM-DD HH:mm").add(1, "minute"),
          });
      }

      if (
        endDate &&
        dateTime1 == dateTime2 &&
        moment(endDate).diff(dateStrings, "minutes") <= 0
      ) {
        form.setFieldsValue({
          end_date: dayjs(dateStrings, "YYYY-MM-DD HH:mm").add(1, "minute"),
        });
      }
      if (
        endDate &&
        moment(endDate).isBefore(moment(dateStrings, "YYYY-MM-DD HH:mm"), "day")
      ) {
        form.setFieldsValue({
          end_date: dayjs(dateStrings, "YYYY-MM-DD HH:mm").add(1, "minute"),
        });
      }
      setStartDate(dateStrings);
      if (!dateStrings) {
        form.setFieldsValue({ end_date: "", start_date: "" });
        setChangeStartDate(false);
      }

      // if (
      //   (choiceNature === t("unavailability.Authorization") ||
      //     choiceNature === t("unavailability.Other")) &&
      //   dateStrings
      // ) {
      //   setEndDate(dayjs(dateStrings, "YYYY-MM-DD HH:mm").add(1, "hour"));

      //   form.setFieldsValue({
      //     end_date: dayjs(dateStrings, "YYYY-MM-DD HH:mm").add(1, "hour"),
      //   });
      // }
      // if (choiceNature === t("unavailability.Other") && dateStrings) {
      //   setEndDate(dayjs(dateStrings, "YYYY-MM-DD HH:mm").add(1, "hour"));

      //   form.setFieldsValue({
      //     end_date: dayjs(dateStrings, "YYYY-MM-DD HH:mm").add(1, "hour"),
      //     // .add(1, "minute"),
      //   });
      // }
      // if (
      //   endDate &&
      //   choiceNature === t("unavailability.Leave") &&
      //   dayjs(dateStrings, "YYYY-MM-DD").format("YYYY-MM-DD") ===
      //     dayjs(endDate, "YYYY-MM-DD").format("YYYY-MM-DD")
      // ) {
      //   if (dayjs(dateStrings, "YYYY-MM-DD HH:mm").format("HH") < 12)
      //     form.setFieldsValue({
      //       end_date: dayjs(dateStrings, "YYYY-MM-DD HH:mm")
      //         .set("hour", 12)
      //         .set("minute", 0),
      //       start_date: dayjs(dateStrings, "YYYY-MM-DD HH:mm")
      //         .set("hour", 8)
      //         .set("minute", 0),
      //     });
      //   else {
      //     form.setFieldsValue({
      //       end_date: dayjs(dateStrings, "YYYY-MM-DD HH:mm")
      //         .set("hour", 17)
      //         .set("minute", 0),
      //       start_date: dayjs(dateStrings, "YYYY-MM-DD HH:mm")
      //         .set("hour", 12)
      //         .set("minute", 0),
      //     });
      //   }
      // }
      // if (choiceNature === t("unavailability.Leave") && dateStrings) {
      //   if (+dayjs(dateStrings).format("HH") < 12) {
      //     form.setFieldsValue({
      //       end_date: dayjs(dateStrings, "YYYY-MM-DD HH:mm").add(4, "hour"),
      //     });
      //   }
      // }
      const futureDates = dateSelected.filter(({ start_date }) =>
        moment(start_date, "YYYY-MM-DD HH:mm").isAfter(
          moment(dateStrings, "YYYY-MM-DD HH:mm")
        )
      );
      futureDates.length > 0
        ? setMaxDate(moment.min(futureDates.map((el) => moment(el.start_date))))
        : setMaxDate(moment().endOf("year"));
      // console.log(closestStartDate.format("YYYY-MM-DD"));

      // for (let i = 0; i < data.length; i++) {
      //   const { start_date } = data[i];
      //   const startDateItem = moment(2023 - 10 - 10, "YYYY-MM-DD HH:mm");

      //   if (startDateItem.isSameOrBefore(dates)) {
      //     closestStartDate = "";
      //   }
      //   if (
      //     startDateItem.isAfter(dates) &&
      //     startDateItem.diff(dates) < closestDiff
      //   ) {
      //     closestDiff = startDateItem.diff(dateStrings);
      //     closestStartDate = startDateItem.format("YYYY-MM-DD");
      //   }
      // }
    }
    if (dataIndex === "end_date") {
      setEndDate(dateStrings);
      getNombreJours(startDate, dateStrings);
      const dateTime1 = dayjs(dateStrings).format("YYYY-MM-DD ");
      const dateTime2 = dayjs(startDate).format("YYYY-MM-DD ");
      if (
        (t(`unavailability.${choiceNature}`) ===
          t("unavailability.Authorization") ||
          t(`unavailability.${choiceNature}`) === t("unavailability.Other")) &&
        startDate &&
        dayjs(dateTime1).isSame(dayjs(dateTime2))
      ) {
        form.setFieldsValue({
          start_date: dayjs(dateStrings, "YYYY-MM-DD HH:mm").add(-1, "minute"),
        });
      }
      // if (
      //   choiceNature === t("unavailability.Authorization") &&
      //   dayjs(dateStrings, "YYYY-MM-DD HH:mm").format("HH") -
      //     dayjs(startDate, "YYYY-MM-DD HH:mm").format("HH") ==
      //     2 &&
      //   dayjs(dateStrings, "YYYY-MM-DD HH:mm").format("mm") >
      //     dayjs(startDate, "YYYY-MM-DD HH:mm").format("mm")
      // ) {
      //   form.setFieldsValue({
      //     end_date: dayjs(startDate, "YYYY-MM-DD HH:mm").add(2, "hour"),
      //   });
      // }

      // if (
      //   choiceNature === t("unavailability.Leave") &&
      //   dayjs(dateStrings, "YYYY-MM-DD").format("YYYY-MM-DD") ===
      //     dayjs(startDate, "YYYY-MM-DD").format("YYYY-MM-DD")
      // ) {
      //   if (dayjs(dateStrings, "YYYY-MM-DD HH:mm").format("HH") <= 12)
      //     form.setFieldsValue({
      //       start_date: dayjs(dateStrings, "YYYY-MM-DD HH:mm")
      //         .set("hour", 8)
      //         .set("minute", 0),
      //       end_date: dayjs(dateStrings, "YYYY-MM-DD HH:mm")
      //         .set("hour", 12)
      //         .set("minute", 0),
      //     });
      //   else {
      //     form.setFieldsValue({
      //       start_date: dayjs(dateStrings, "YYYY-MM-DD HH:mm")
      //         .set("hour", 12)
      //         .set("minute", 0),
      //       end_date: dayjs(dateStrings, "YYYY-MM-DD HH:mm")
      //         .set("hour", 17)
      //         .set("minute", 0),
      //     });
      //   }
      // }
      // if (
      //   choiceNature === t("unavailability.Other") &&
      //   dayjs(dateStrings).format("YYYY-MM-DD HH:mm") <
      //     dayjs(startDate).format("YYYY-MM-DD HH:mm")
      // )
      //   form.setFieldsValue({
      //     end_date: dayjs(startDate, "YYYY-MM-DD HH:mm").add(1, "hour"),
      //   });
    }
  };
  // const selectedDate = (date, dataIndex) => {
  //   const dateStrings = dayjs(date, "YYYY-MM-DD HH:mm").format(
  //     "YYYY-MM-DD HH:mm"
  //   );

  //   if (dataIndex === "start_date") {
  //     setChangeStartDate(true);
  //     if (!endDate) {
  //       form.setFieldsValue({
  //         end_date: dayjs(dateStrings, "YYYY-MM-DD HH:mm").add(1, "minute"),
  //       });
  //     }
  //     if (endDate && moment(endDate).diff(dateStrings, "minutes") <= 0) {
  //       form.setFieldsValue({
  //         end_date: dayjs(dateStrings, "YYYY-MM-DD HH:mm").add(1, "minute"),
  //       });
  //     }
  //     setStartDate(dateStrings);
  //     if (!dateStrings) {
  //       form.setFieldsValue({ end_date: "", start_date: "" });
  //       setChangeStartDate(false);
  //     }
  //     // if (
  //     //   (choiceNature === t("unavailability.Authorization") ||
  //     //     choiceNature === t("unavailability.Other")) &&
  //     //   dateStrings
  //     // ) {
  //     //   setEndDate(dayjs(dateStrings, "YYYY-MM-DD HH:mm").add(1, "hour"));

  //     //   form.setFieldsValue({
  //     //     end_date: dayjs(dateStrings, "YYYY-MM-DD HH:mm").add(1, "hour"),
  //     //   });
  //     // }
  //     // if (choiceNature === t("unavailability.Other") && dateStrings) {
  //     //   setEndDate(dayjs(dateStrings, "YYYY-MM-DD HH:mm").add(1, "hour"));

  //     //   form.setFieldsValue({
  //     //     end_date: dayjs(dateStrings, "YYYY-MM-DD HH:mm").add(1, "hour"),
  //     //     // .add(1, "minute"),
  //     //   });
  //     // }
  //     // if (
  //     //   endDate &&
  //     //   choiceNature === t("unavailability.Leave") &&
  //     //   dayjs(dateStrings, "YYYY-MM-DD").format("YYYY-MM-DD") ===
  //     //     dayjs(endDate, "YYYY-MM-DD").format("YYYY-MM-DD")
  //     // ) {
  //     //   if (dayjs(dateStrings, "YYYY-MM-DD HH:mm").format("HH") < 12)
  //     //     form.setFieldsValue({
  //     //       end_date: dayjs(dateStrings, "YYYY-MM-DD HH:mm")
  //     //         .set("hour", 12)
  //     //         .set("minute", 0),
  //     //       start_date: dayjs(dateStrings, "YYYY-MM-DD HH:mm")
  //     //         .set("hour", 8)
  //     //         .set("minute", 0),
  //     //     });
  //     //   else {
  //     //     form.setFieldsValue({
  //     //       end_date: dayjs(dateStrings, "YYYY-MM-DD HH:mm")
  //     //         .set("hour", 17)
  //     //         .set("minute", 0),
  //     //       start_date: dayjs(dateStrings, "YYYY-MM-DD HH:mm")
  //     //         .set("hour", 12)
  //     //         .set("minute", 0),
  //     //     });
  //     //   }
  //     // }
  //     // if (choiceNature === t("unavailability.Leave") && dateStrings) {
  //     //   if (+dayjs(dateStrings).format("HH") < 12) {
  //     //     form.setFieldsValue({
  //     //       end_date: dayjs(dateStrings, "YYYY-MM-DD HH:mm").add(4, "hour"),
  //     //     });
  //     //   }
  //     // }
  //     const futureDates = dateSelected.filter(({ start_date }) =>
  //       moment(start_date, "YYYY-MM-DD HH:mm").isAfter(
  //         moment(dateStrings, "YYYY-MM-DD HH:mm")
  //       )
  //     );
  //     futureDates.length > 0
  //       ? setMaxDate(moment.min(futureDates.map((el) => moment(el.start_date))))
  //       : setMaxDate(moment().endOf("year"));
  //     // console.log(closestStartDate.format("YYYY-MM-DD"));

  //     // for (let i = 0; i < data.length; i++) {
  //     //   const { start_date } = data[i];
  //     //   const startDateItem = moment(2023 - 10 - 10, "YYYY-MM-DD HH:mm");

  //     //   if (startDateItem.isSameOrBefore(dates)) {
  //     //     closestStartDate = "";
  //     //   }
  //     //   if (
  //     //     startDateItem.isAfter(dates) &&
  //     //     startDateItem.diff(dates) < closestDiff
  //     //   ) {
  //     //     closestDiff = startDateItem.diff(dateStrings);
  //     //     closestStartDate = startDateItem.format("YYYY-MM-DD");
  //     //   }
  //     // }
  //   }
  //   if (dataIndex === "end_date") {
  //     setEndDate(dateStrings);
  //     getNombreJours(startDate, dateStrings);

  //     // if (
  //     //   choiceNature === t("unavailability.Authorization") &&
  //     //   dayjs(dateStrings, "YYYY-MM-DD HH:mm").format("HH") -
  //     //     dayjs(startDate, "YYYY-MM-DD HH:mm").format("HH") ==
  //     //     2 &&
  //     //   dayjs(dateStrings, "YYYY-MM-DD HH:mm").format("mm") >
  //     //     dayjs(startDate, "YYYY-MM-DD HH:mm").format("mm")
  //     // ) {
  //     //   form.setFieldsValue({
  //     //     end_date: dayjs(startDate, "YYYY-MM-DD HH:mm").add(2, "hour"),
  //     //   });
  //     // }

  //     // if (
  //     //   choiceNature === t("unavailability.Leave") &&
  //     //   dayjs(dateStrings, "YYYY-MM-DD").format("YYYY-MM-DD") ===
  //     //     dayjs(startDate, "YYYY-MM-DD").format("YYYY-MM-DD")
  //     // ) {
  //     //   if (dayjs(dateStrings, "YYYY-MM-DD HH:mm").format("HH") <= 12)
  //     //     form.setFieldsValue({
  //     //       start_date: dayjs(dateStrings, "YYYY-MM-DD HH:mm")
  //     //         .set("hour", 8)
  //     //         .set("minute", 0),
  //     //       end_date: dayjs(dateStrings, "YYYY-MM-DD HH:mm")
  //     //         .set("hour", 12)
  //     //         .set("minute", 0),
  //     //     });
  //     //   else {
  //     //     form.setFieldsValue({
  //     //       start_date: dayjs(dateStrings, "YYYY-MM-DD HH:mm")
  //     //         .set("hour", 12)
  //     //         .set("minute", 0),
  //     //       end_date: dayjs(dateStrings, "YYYY-MM-DD HH:mm")
  //     //         .set("hour", 17)
  //     //         .set("minute", 0),
  //     //     });
  //     //   }
  //     // }
  //     // if (
  //     //   choiceNature === t("unavailability.Other") &&
  //     //   dayjs(dateStrings).format("YYYY-MM-DD HH:mm") <
  //     //     dayjs(startDate).format("YYYY-MM-DD HH:mm")
  //     // )
  //     //   form.setFieldsValue({
  //     //     end_date: dayjs(startDate, "YYYY-MM-DD HH:mm").add(1, "hour"),
  //     //   });
  //   }
  // };

  const disabledHours = (current, dataIndex) => {
    if (current) {
      if (dataIndex === "start_date") {
        if (
          dayjs(current).format("YYYY-MM-DD") === dayjs().format("YYYY-MM-DD")
        ) {
          return {
            disabledHours: () =>
              Array.from({ length: 24 }, (_, index) => +index).filter(function (
                element
              ) {
                return (
                  element < 8 || element > 16 || element > +dayjs().format("HH")
                );
              }),
            // disabledMinutes: () => range(dayjs(endDate).format('mm'), 60),
            // disabledSeconds: () => [55, 56],
          };
        }
        if (
          (choiceNature === t("unavailability.Authorization") ||
            choiceNature === t("unavailability.Other")) &&
          dayjs(current).format("YYYY-MM-DD") ===
            dayjs(endDate).format("YYYY-MM-DD")
        ) {
          return {
            disabledHours: () =>
              Array.from({ length: 24 }, (_, index) => +index).filter(function (
                element
              ) {
                return (
                  element < 8 ||
                  element > 16 ||
                  element > +dayjs(endDate).format("HH") - 1
                );
              }),
            // disabledMinutes: () => range(dayjs(endDate).format('mm'), 60),
            // disabledSeconds: () => [55, 56],
          };
        }
        if (
          endDate &&
          dayjs(current).format("YYYY-MM-DD ") ===
            dayjs(endDate).format("YYYY-MM-DD ")
        ) {
          // Si endDate est égale à la date actuelle, désactiver les heures après l'heure actuelle
          // console.log(...Array(moment().hour() + 1).keys())
          // return [...Array(moment().hour() + 1).keys()];
          if (choiceNature === t("unavailability.Leave")) {
            return {
              disabledHours: () =>
                Array.from({ length: 24 }, (_, index) => +index).filter(
                  function (element) {
                    return element !== 8 && element !== 12;
                  }
                ),
              disabledMinutes: () =>
                Array.from({ length: 60 }, (_, index) => +index).filter(
                  function (element) {
                    return element !== 0;
                  }
                ),
              // disabledSeconds: () => [55, 56],
            };
          }
          return {
            disabledHours: () =>
              range(0, 60).splice(dayjs(endDate).format("HH"), 24),
            // disabledMinutes: () => range(dayjs(endDate).format('mm'), 60),
            // disabledSeconds: () => [55, 56],
          };
        }

        return {
          disabledHours: () => [
            0, 1, 2, 3, 4, 5, 6, 7, 17, 18, 19, 20, 21, 22, 23, 24,
          ],
          // disabledMinutes: () => range(dayjs(endDate).format('mm'), 60),
          // disabledSeconds: () => [55, 56],
        };
      }
      if (dataIndex === "end_date") {
        if (
          startDate &&
          dayjs(current).format("YYYY-MM-DD HH") ===
            dayjs(startDate).format("YYYY-MM-DD HH")
        ) {
          // Si endDate est égale à la date actuelle, désactiver les heures après l'heure actuelle
          // console.log(...Array(moment().hour() + 1).keys())
          // return [...Array(moment().hour() + 1).keys()];*

          if (choiceNature === t("unavailability.Other")) {
            return {
              // disabledHours: () =>
              //   // range(0, 60).splice(0, +dayjs(startDate).format("HH") + 1, 20),
              //   Array.from({ length: 24 }, (_, index) => index).filter(
              //     (el) => el < +dayjs(startDate).format("HH") || el > 19
              //   ),
              disabledMinutes: () =>
                Array.from({ length: 59 }, (_, index) => index).filter(
                  (el) => el < +dayjs(startDate).format("mm") + 1
                ),
              // disabledSeconds: () => [55, 56],
            };
          }
        }
        if (
          startDate &&
          dayjs(current).format("YYYY-MM-DD ") ===
            dayjs(startDate).format("YYYY-MM-DD ")
        ) {
          // Si endDate est égale à la date actuelle, désactiver les heures après l'heure actuelle
          // console.log(...Array(moment().hour() + 1).keys())
          // return [...Array(moment().hour() + 1).keys()];*
          if (choiceNature === t("unavailability.Other")) {
            return {
              disabledHours: () =>
                // range(0, 60).splice(0, +dayjs(startDate).format("HH") + 1, 20),
                Array.from({ length: 24 }, (_, index) => index).filter(
                  (el) => el < +dayjs(startDate).format("HH") || el > 18
                ),
              // disabledMinutes: () => range(dayjs(endDate).format('mm'), 60),
              // disabledSeconds: () => [55, 56],
            };
          }
          if (choiceNature === t("unavailability.Leave")) {
            return {
              disabledHours: () =>
                // range(0, 60).splice(0, +dayjs(startDate).format("HH") + 1, 20),
                Array.from({ length: 24 }, (_, index) => index).filter(
                  (el) => el !== 12 && el !== 17
                  // ||
                  // (el === +dayjs(startDate).format("HH") + 1 &&
                  //   el === +dayjs(startDate).format("HH") + 2)
                ),
              disabledMinutes: () =>
                Array.from({ length: 60 }, (_, index) => +index).filter(
                  function (element) {
                    return element !== 0;
                  }
                ),
              // disabledMinutes: () => range(dayjs(endDate).format('mm'), 60),
              // disabledSeconds: () => [55, 56],
            };
          }
          if (choiceNature === t("unavailability.Authorization")) {
            return {
              disabledHours: () =>
                Array.from({ length: 24 }, (_, index) => index).filter(
                  (el) =>
                    el !== +dayjs(startDate).format("HH") + 1 &&
                    el !== +dayjs(startDate).format("HH") + 2
                ),
              disabledMinutes: () =>
                dayjs(current).format("HH") - dayjs(startDate).format("HH") == 2
                  ? Array.from({ length: 59 }, (_, index) => index).filter(
                      (el) => el > +dayjs(startDate).format("mm")
                    )
                  : "",

              // disabledSeconds: () => [55, 56],
            };
          }
        }
        return {
          disabledHours: () => [0, 1, 2, 3, 4, 5, 6, 7, 20, 21, 22, 23, 24],
          // disabledMinutes: () => range(dayjs(endDate).format('mm'), 60),
          // disabledSeconds: () => [55, 56],
        };
      }
    } else
      return {
        disabledHours: () => range(0, 24).splice(0, 24),
        disabledMinutes: () => range(0, 59).splice(0, 59),
        disabledSeconds: () => range(0, 59).splice(0, 59),
      };
  };
  function disabledTime(current, dataIndex) {
    if (current) {
      if (
        dataIndex === "end_date" &&
        dayjs(current).format("YYYY-MM-DD HH") ===
          dayjs(startDate).format("YYYY-MM-DD HH")
      ) {
        return {
          disabledHours: () =>
            // range(0, 60).splice(0, +dayjs(startDate).format("HH") + 1, 20),
            Array.from({ length: 24 }, (_, index) => index).filter(
              (el) => el < +dayjs(startDate).format("HH")
            ),
          disabledMinutes: () =>
            Array.from({ length: 59 }, (_, index) => index).filter(
              (el) => el < +dayjs(startDate).format("mm") + 1
            ),
          // disabledSeconds: () => [55, 56],
        };
      }
      if (
        dataIndex === "end_date" &&
        dayjs(current).format("YYYY-MM-DD") ===
          dayjs(startDate).format("YYYY-MM-DD")
      ) {
        return {
          disabledHours: () =>
            // range(0, 60).splice(0, +dayjs(startDate).format("HH") + 1, 20),
            Array.from({ length: 24 }, (_, index) => index).filter((el) =>
              dayjs(startDate).format("mm") == 59
                ? el < +dayjs(startDate).format("HH") + 1
                : el < +dayjs(startDate).format("HH")
            ),

          // disabledSeconds: () => [55, 56],
        };
      } else
        return {
          disabledHours: () => range(0, 24).splice(0, 0),
          disabledMinutes: () => range(0, 59).splice(0, 0),
        };
    }
  }
  function disabledDate(current) {
    // if (choiceNature.includes("congé")) {
    //   return (
    //     current &&
    //     (endDate
    //       ? current.isAfter(endDate, "day") ||
    //         // current.isBefore(moment(maxDate), "day") ||
    //         // current.isSame(moment(maxDate), "day") ||
    //         current.isSame(moment(endDate), "day") ||
    //         current < moment().startOf("day") ||
    //         [0, 6].includes(current.day())
    //       : current < moment().startOf("day") || [0, 6].includes(current.day()))
    //     // ||
    //     // current.isBefore(moment(maxDate), "day") ||
    //     // current.isSame(moment(maxDate), "day")
    //   );
    // }
    // if (choiceNature?.toLowerCase() === "authorisation") {
    //   return (
    //     current &&
    //     (endDate
    //       ? current.isAfter(endDate, "day") ||
    //         // current.isBefore(moment(maxDate), "day") ||
    //         // current.isSame(moment(maxDate), "day") ||
    //         current < moment().startOf("day") ||
    //         [0, 6].includes(current.day())
    //       : current < moment().startOf("day") ||
    //         [0, 6].includes(current.day()) ||
    //         current.isBefore(moment(startDate), "day"))
    //     //  ||
    //     // current.isBefore(moment(maxDate), "day") ||
    //     // current.isSame(moment(maxDate), "day")
    //   );
    // }

    // return dateSelected?.filter((el) => el?.id).length > 0
    //   ? dateSelected
    //       ?.filter((el) => el?.id)
    //       .some(({ start_date, end_date }) => {
    //         const startDateItem = moment(start_date, "YYYY-MM-DD HH:mm");
    //         const endDateItem = moment(end_date, "YYYY-MM-DD HH:mm");
    //         // if (
    //         //   choiceNature === t("unavailability.Authorization") &&
    //         //   endDateItem.diff(startDateItem, "minutes") <= 60
    //         // ) {
    //         //   // console.log(endDateItem.diff(startDateItem, "minutes"));
    //         //   return (
    //         //     current < moment().startOf("day") ||
    //         //     (endDate && current.isAfter(endDate, "day")) ||
    //         //     [0, 6].includes(current.day())
    //         //   );
    //         //   // console.log(data.filter((el) => el._id));
    //         //   // console.log(startDateItem);
    //         //   // console.log(endDateItem);
    //         // } else {
    //         return (
    //           // current.isSame(startDateItem, "day") ||
    //           // current.isSame(endDateItem, "day") ||
    //           // (current.isAfter(startDateItem, "day") &&
    //           //   current.isBefore(endDateItem, "day")) ||
    //           current < moment().startOf("day") ||
    //           (endDate && current.isAfter(endDate, "day")) ||
    //           [0, 6].includes(current.day())
    //         );
    //         // }
    //       })
    //   : current < moment().startOf("day") ||
    //       (endDate && current.isAfter(endDate, "day")) ||
    //       [0, 6].includes(current.day());

    return (
      // current.isSame(startDateItem, "day") ||
      // current.isBefore(moment(startDate, "YYYY-MM-DD HH:mm"), "day") ||
      // current.isSame(endDateItem, "day") ||
      // (current.isAfter(startDateItem, "day") &&
      //   current.isBefore(endDateItem, "day")) ||
      // current.isAfter(maxDate, "day") ||
      // current.isAfter(closestStartDate, "day") ||
      current < moment().startOf("day") || [0, 6].includes(current.day())
    );
    // return current && maxDate &&   (endDate  ? current.isAfter(endDate, 'day')  || current < moment().startOf('day')  || [0, 6].includes(current.day())
    // ||current.isBefore(moment(maxDate), 'day') || current.isSame(moment(maxDate), 'day')   :  current < moment().startOf('day')  || [0, 6].includes(current.day()) ) ;

    // return current &&  (endDate  ? current.isAfter(endDate, 'day')  || current < moment().startOf('day')  || [0, 6].includes(current.day()):current < moment().startOf('day')  || [0, 6].includes(current.day()));
  }

  const disabledEndDate = (current) => {
    //     if(choiceNature==='congé de maladie'){
    // return current && ( maxDate ? current.isBefore(startDate, 'day') || current.isSame(startDate, 'day') ||  [0, 6].includes(current.day() ) || current.isBefore(moment(maxDate), 'day') || current.isSame(moment(maxDate), 'day') :current.isBefore(startDate, 'day')|| current.isSame(startDate, 'day') ||  [0, 6].includes(current.day() ) ) ;
    //     }
    //     if(choiceNature==='Authorisation'){
    //       return current && ( !current.isSame(startDate, 'day') ||  [0, 6].includes(current.day() ) ) ;

    //     }
    // if (choiceNature === t("unavailability.Leave")) {
    //   console.log(dateSelected);
    //   console.log(choiceNature);

    //   dateSelected?.length > 0
    //     ? dateSelected?.some(({ start_date, end_date }) => {
    //         console.log("ok");
    //         const startDateItem = moment(start_date, "YYYY-MM-DD HH:mm");
    //         const endDateItem = moment(end_date, "YYYY-MM-DD HH:mm");
    //         return (
    //           current.isSame(startDateItem, "day") ||
    //           current.isBefore(moment(startDate, "YYYY-MM-DD HH:mm"), "day") ||
    //           current.isSame(endDateItem, "day") ||
    //           (current.isAfter(startDateItem, "day") &&
    //             current.isBefore(endDateItem, "day")) ||
    //           current.isAfter(maxDate, "day") ||
    //           // current.isAfter(closestStartDate, "day") ||
    //           current < moment().startOf("day") ||
    //           (startDate && current.isBefore(startDate, "day")) ||
    //           [0, 6].includes(current.day())
    //         );
    //       })
    //     : current.isBefore(moment(startDate, "YYYY-MM-DD HH:mm"), "day") ||
    //       current.isAfter(maxDate, "day") ||
    //       // current.isAfter(closestStartDate, "day") ||
    //       current < moment().startOf("day") ||
    //       (startDate && current.isBefore(startDate, "day")) ||
    //       [0, 6].includes(current.day());
    // }
    // if (
    //   choiceNature === t("unavailability.Authorization") ||
    //   choiceNature === t("unavailability.Other")
    // ) {
    //   return dateSelected?.some(({ start_date, end_date }) => {
    //     const startDateItem = moment(start_date, "YYYY-MM-DD HH:mm");
    //     const endDateItem = moment(end_date, "YYYY-MM-DD HH:mm");
    //     return (
    //       // current.isSame(startDateItem, "day") ||
    //       // current.isSame(endDateItem, "day") ||
    //       // (current.isAfter(startDateItem, "day") &&
    //       //   current.isBefore(endDateItem, "day")) ||
    //       current < moment().startOf("day") ||
    //       current.isBefore(startDate, "day") ||
    //       [0, 6].includes(current.day())
    //       // ||
    //       // !current.isSame(moment(startDate), "day")
    //     );
    //   });
    // }

    // return dateSelected?.some(({ start_date, end_date }) => {
    //   const startDateItem = moment(start_date, "YYYY-MM-DD HH:mm");
    //   const endDateItem = moment(end_date, "YYYY-MM-DD HH:mm");
    //   console.log(startDateItem);
    //   console.log(endDateItem);
    //   return (
    //     // current.isSame(startDateItem, "day") ||
    //     // current.isBefore(moment(startDate, "YYYY-MM-DD HH:mm"), "day") ||
    //     // current.isSame(endDateItem, "day") ||
    //     // (current.isAfter(startDateItem, "day") &&
    //     //   current.isBefore(endDateItem, "day")) ||
    //     // current.isAfter(maxDate, "day") ||
    //     // current.isAfter(closestStartDate, "day") ||
    //     // current < moment().startOf("day") ||
    //     (startDate && current.isBefore(startDate, "day")) ||
    //     [0, 6].includes(current.day())
    //   );
    // });
    return (
      // current.isSame(startDateItem, "day") ||
      // current.isBefore(moment(startDate, "YYYY-MM-DD HH:mm"), "day") ||
      // current.isSame(endDateItem, "day") ||
      // (current.isAfter(startDateItem, "day") &&
      //   current.isBefore(endDateItem, "day")) ||
      // current.isAfter(maxDate, "day") ||
      // current.isAfter(closestStartDate, "day") ||
      // current < moment().startOf("day") ||
      (startDate && current.isBefore(startDate, "day")) ||
      (startDate && current.isBefore(startDate, "minute")) ||
      [0, 6].includes(current.day())
    );
  };
  const handleClick = (event) => {
    event.stopPropagation();
  };
  const handleConditionCheck = (dataIndex) => {
    if (dataIndex === "end_date") {
      if (!choiceNature || !changeStartDate) {
        return true;
      }
      // if (choiceNature?.toLowerCase() === "authorisation" && !changeStartDate) {
      //   return true;
      // }
      else return false;
    }
    if (dataIndex === "start_date") {
      if (!choiceNature) {
        return true;
      }
      return false;
    }
  };
  const clickInDatePicker = (current, dataIndex) => {
    const dateTime1 = moment(dayjs(startDate).format("YYYY-MM-DD "));
    const dateTime2 = moment(dayjs(current).format("YYYY-MM-DD "));
    if (dataIndex === "end_date" && dateTime2.diff(dateTime1, "minutes") > 0) {
      form.setFieldsValue({
        end_date: dayjs(current, "YYYY-MM-DD HH:mm")
          .set("hour", 8)
          .set("minute", 0),
      });
    }
    if (dataIndex === "end_date" && dateTime2.diff(dateTime1, "minutes") == 0) {
      form.setFieldsValue({
        end_date: dayjs(current, "YYYY-MM-DD HH:mm").add(1, "minute"),
      });
    }

    if (
      dataIndex === "end_date" &&
      dayjs(current).format("YYYY-MM-DD HH:mm") ===
        dayjs(startDate).format("YYYY-MM-DD HH:mm")
    ) {
      if (dayjs(startDate).format("mm") == 59) {
        form.setFieldsValue({
          end_date: dayjs(current, "YYYY-MM-DD HH:mm").add(1, "hour"),
        });
      } else
        form.setFieldsValue({
          end_date: dayjs(current, "YYYY-MM-DD HH:mm").add(1, "minute"),
        });
    }
  };
  const EditableCell = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    const inputNode =
      inputType === "date" ? (
        <DatePicker
          size="middle"
          showTime={{
            defaultValue:
              dataIndex === "end_date" &&
              choiceNature === t("unavailability.Authorization")
                ? dayjs(`${+dayjs(startDate).format("HH") + 1}:00:00`, "HH:mm")
                : dayjs("08:00:00", "HH:mm"),
          }}
          // showTime={{
          //   defaultValue:
          //     dataIndex === "start_date"
          //       ? dayjs("08:00", "HH:mm")
          //       : dayjs(startDate).format("mm") == 59
          //       ? dayjs(
          //           `${String(
          //             Number(dayjs(startDate).format("HH")) + 1
          //           ).padStart(2, "0")}:00`,
          //           "HH:mm"
          //         )
          //       : dayjs(
          //           `${dayjs(startDate).format("HH")}:${String(
          //             Number(dayjs(startDate).format("mm")) + 1
          //           ).padStart(2, "0")}`,
          //           "HH:mm"
          //         ),
          // }}

          format="YYYY-MM-DD HH:mm"
          disabled={
            handleConditionCheck(dataIndex)
            // choiceNature &&
            //   ? false
            //   : choiceNature?.toLowerCase() === "authorisation" &&
            //     !changeStartDate &&
            //     dataIndex === "end_date"
            //   ? true
            //   : true
          }
          inputReadOnly={true}
          disabledTime={(current) => {
            return disabledTime(current, dataIndex);
          }}
          // disabledTime={(current) => disabledHours(current, dataIndex)}
          showNow={false}
          disabledDate={
            dataIndex === "start_date" ? disabledDate : disabledEndDate
          }
          placeholder={
            dataIndex === "start_date"
              ? t("unavailability.start_date")
              : t("unavailability.end_date")
          }
          // onSelect={(t) => clickInDatePicker(t, dataIndex)}
          onChange={(t, i) => selectedDate(t, i, dataIndex)}
          // onOk={(t) => selectedDate(t, dataIndex)}
        />
      ) : inputType === "select" ? (
        <Select
          placeholder="Nature"
          // autoFocus
          options={nature}
          onChange={(e) => {
            setChoiceNature(e);

            if (t(`unavailability.${e}`) === t("unavailability.Other")) {
              form.setFieldsValue({
                start_date: "",
                end_date: "",
                description: "",
              });
            } else {
              form.setFieldsValue({
                start_date: "",
                end_date: "",
                description: t(`unavailability.${e}`),
              });
            }
            setChangeStartDate(false);

            // setStartDate(moment());
            setEndDate("");
          }}
          showSearch
          filterOption={(input, option) =>
            (option?.label?.toLowerCase() ?? "").includes(input.toLowerCase())
          }
        />
      ) : (
        <Input
          disabled={choiceNature ? false : true}
          onKeyPress={handleKeyPress}
          placeholder={
            t(`unavailability.${choiceNature}`) === t("unavailability.Other")
              ? t("unavailability.otherDesc")
              : ""
          }
        />
      );

    return (
      <td {...restProps}>
        {editing ? (
          <Form.Item
            name={dataIndex.toLowerCase()}
            style={{
              margin: 0,
            }}
            rules={[
              {
                required: true,
                message: `${t(`unavailability.${dataIndex}`)} ${t(
                  "table.header.isrequired"
                )}`,
              },
            ]}>
            {inputNode}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };
  const edit = (record) => {
    setDataSelected(data.filter((el) => el.id !== record.id));
    setChoiceNature(t(`unavailability.${record?.nature}`));
    // setMaxDate(getMaxEndDate(data.filter((el) => el.id !== record.id)));
    if (record) {
      // if (record.id)
      form.setFieldsValue({
        start_date: dayjs(record.start_date, "YYYY-MM-DD HH:mm"),
        end_date: dayjs(record.end_date, "YYYY-MM-DD HH:mm"),
        nature: record?.nature,
        description: record?.description,
      });
      setStartDate(record.startDate);
      setEndDate(record.end_date);

      setChoiceNature(record?.nature);
      setId(record.id);
      setChangeStartDate(true);
    }

    setEditingKey(record.key);
  };
  const cancel = (record) => {
    setEditingKey("");
    setChoiceNature("");
    setChangeStartDate(false);
    setId(null);
    if (!record.id) {
      setData(data.filter((item) => item.key !== record.key));
      setDataSelected(data.filter((item) => item.key !== record.key));
    }
  };

  const save = async () => {
    const row = await form.validateFields();
    setLoading(true);
    if (id) {
      try {
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post(
          `/indisponibilite/${id}`,
          {
            start_date: dayjs(row.start_date).format("YYYY-MM-DD HH:mm:ss"),
            end_date: dayjs(row.end_date).format("YYYY-MM-DD HH:mm:ss"),
            nature: row?.nature,
            description: row?.description,
          },
          {
            headers: {
              "content-type": "application/x-www-form-urlencoded;charset=utf-8",
            },
          }
        );
        setEditingKey("");
        setData(
          data.map((el) =>
            el.id === id
              ? {
                  ...res.data.data,
                  key: el.key,
                  id: el._id,
                  index: el.index,
                }
              : el
          )
        );
        setDataSelected(
          data.map((el) =>
            el.id === id
              ? {
                  ...res.data.data,
                  key: el.key,
                  id: el._id,
                  index: el.index,
                }
              : el
          )
        );
        form.setFieldsValue({
          start_date: "",
          end_date: "",
          nature: null,
          description: "",
        });
        const maxDateObject = getMaxEndDate(
          data.map((el) =>
            el.id === id
              ? {
                  ...res.data.data,
                  key: el.key,
                  id: el._id,
                  index: el.index,
                }
              : el
          )
        );
        // setMaxDate(maxDateObject);
        setEndDate("");
        // setStartDate(moment());
        setChoiceNature("");
        setChangeStartDate(false);

        setLoading(false);

        toastNotification(
          "success",
          t("menu2.unavailability") + t("toasts.edit"),
          "topRight"
        );
      } catch (errInfo) {
        setLoading(false);
        if (errInfo?.response?.data?.message) {
          toastNotification(
            "error",
            t(
              `unavailability.${errInfo?.response?.data?.message.replace(
                /\s/g,
                ""
              )}`
            ),
            "topRight"
          );
        } else
          toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    } else {
      try {
        const row = await form.validateFields();
        const maxValue =
          data.length > 1 ? Math.max(...data.map((obj) => obj.index)) : 0;
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post("/indisponibilite", {
          start_date: dayjs(row.start_date)?.format("YYYY-MM-DD HH:mm:ss"),
          end_date: dayjs(row.end_date)?.format("YYYY-MM-DD HH:mm:ss"),
          nature: row?.nature,
          description: row?.description,
        });
        setEditingKey("");

        setData([
          ...data.filter((el) => el._id),
          {
            ...res.data.data,
            key: maxValue + 1,
            id: res.data.data._id,
            index: maxValue + 1,

            // companies_ids: res.data.data.companies
            //   .map((el) => el.social_reason)
            //   .join(","),
          },
        ]);
        setDataSelected([
          ...data.filter((el) => el._id),
          {
            ...res.data.data,
            key: maxValue + 1,
            id: res.data.data._id,
            index: maxValue + 1,

            // companies_ids: res.data.data.companies
            //   .map((el) => el.social_reason)
            //   .join(","),
          },
        ]);
        form.setFieldsValue({
          start_date: "",
          end_date: "",
          nature: null,
          description: "",
        });

        const maxDateObject = getMaxEndDate([
          ...data.filter((el) => el._id),
          {
            ...res.data.data,
            key: maxValue + 1,
            id: res.data.data._id,
            index: maxValue + 1,

            // companies_ids: res.data.data.companies
            //   .map((el) => el.social_reason)
            //   .join(","),
          },
        ]);
        // setMaxDate(maxDateObject);
        setEndDate("");
        // setStartDate(moment());
        setChangeStartDate(false);
        setChoiceNature("");
        setLoading(false);
        toastNotification(
          "success",
          t("menu2.unavailability") + t("toasts.created"),
          "topRight"
        );
      } catch (errInfo) {
        console.log(errInfo);

        setLoading(false);
        if (errInfo?.response?.data?.message) {
          toastNotification(
            "error",
            t(
              `unavailability.${errInfo?.response?.data?.message.replace(
                /\s/g,
                ""
              )}`
            ),
            "topRight"
          );
        } else
          toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    }
  };
  function getMaxEndDate(data) {
    let maxEndDate = null;
    for (let i = 0; i < data.length; i++) {
      const endDate = moment(data[i].end_date);
      if (!maxEndDate || endDate.isAfter(maxEndDate)) {
        maxEndDate = endDate;
      }
    }
    return maxEndDate?.format("YYYY-MM-DD HH:mm");
  }
  useEffect(() => {
    const getIndisponibilite = async () => {
      setLoading(true);
      try {
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get("indisponibilite");

        if (res.data.data.length > 0) {
          setCount(Math.max(...data.map((el) => el.id)));
        }
        if (res.status === 200) {
          const {
            data: { data },
          } = await generateAxios(
            URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
          ).get("nature");
          setNature(
            data?.map((el) => ({
              value: el?.label,
              label: t(`unavailability.${el?.label}`),
            }))
          );

          // if (res.data.data.length > 0) {
          //   const maxDateObject = getMaxEndDate(res.data.data);

          //   // setMaxDate(maxDateObject);
          //   setStartDate(moment(maxDateObject));
          // }
          // setEndDate(moment(maxDateObject))

          setData(
            res.data.data.map((el, i) => ({
              ...el,
              id: el._id,
              key: i + 1,
              rank: i + 1,
              index: i + 1,
            }))
          );
          setDataSelected(
            res.data.data.map((el, i) => ({
              ...el,
              id: el._id,
              key: i + 1,
              rank: i + 1,
              index: i + 1,
            }))
          );
          //   setCompanies(data);
          setLoading(false);
        }
      } catch (err) {
        console.log(err);
        setLoading(false);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    };
    if (!search && data.length == 0) getIndisponibilite();
    // return () => dispatch(setSearch(""));
  }, [search]);
  useEffect(() => {
    return () => dispatch(setSearch(""));
  }, []);
  const columns = [
    {
      title: "Nature",
      dataIndex: "nature",
      key: "nature",

      editable: true,
      render: (_, { nature }) => t(`unavailability.${nature}`),
    },
    {
      title: "Description",
      dataIndex: "description",
      key: "description",

      editable: true,
    },
    {
      title: t("unavailability.start_date"),
      dataIndex: "start_date",
      key: "start_date",
      editable: true,
      sorter: (a, b) => a.start_date.localeCompare(b.start_date),
    },
    {
      title: t("unavailability.end_date"),
      dataIndex: "end_date",
      key: "end_date",
      sorter: (a, b) => a.end_date.localeCompare(b.start_date),

      editable: true,
    },
  ];
  const handleAdd = () => {
    setId(null);
    const ids = data.map((object) => {
      return object.index;
    });
    const newData = {
      key: Math.max(...ids) + 1,
      index: Math.max(...ids) + 1,
      start_date: `  `,
      end_date: "",
      nature: null,
      disabled: true,
    };
    setCurrentPage(Math.ceil([...data, newData].length / pageSize));
    setOldPage(Math.ceil([...data, newData].length / pageSize));

    setData([...data, newData]);
    setDataSelected([...data, newData]);
    form.setFieldsValue({
      start_date: "",
      end_date: "",
      nature: null,
      description: "",
    });
    setEditingKey(Math.max(...ids) + 1);
    setCount(Math.max(...ids) + 1);
  };
  const onRow = () => {};
  //   const filteredData = data.filter((item) => {
  //     let isMatch = false;
  //     item.companies?.forEach((el) => {
  //       if (el.start_date.toLowerCase().includes(search.toLowerCase())) isMatch = true;
  //     });

  //     return item.start_date.toLowerCase().includes(search.toLowerCase()) || isMatch;
  //   });
  return (
    <Space direction="vertical" style={{ width: "100%", paddingTop: "1rem" }}>
      <Header
        active={"2"}
        editingKey={editingKey}
        handleAdd={handleAdd}
        btnText={t("chat.add")}
        disabled={loading ? true : editingKey ? true : search ? true : false}
        data={search ? filteredData : data}
        api="indisponibilite"
      />

      <NewTableDraggable
        columns={columns}
        setLoading={setLoading}
        isEditing={isEditing}
        data={search ? filteredData : data}
        setData={setData}
        loading={loading}
        save={save}
        edit={edit}
        EditableCell={EditableCell}
        onFinishFailed={onFinishFailed}
        cancel={cancel}
        form={form}
        apiRank="/rank-indisponibilite"
        editingKey={editingKey}
        api="indisponibilite"
        onRow={onRow}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        pageSize={pageSize}
        setPageSize={setPageSize}
        setMaxDate={setMaxDate}
      />

      <BottomButtonAddRow
        editingKey={editingKey}
        data={search ? filteredData : data}
        text={t("chat.add")}
        handleAdd={handleAdd}
        loading={loading}
        search={search || ""}
      />
    </Space>
  );
};
export default memo(Unavailability);
