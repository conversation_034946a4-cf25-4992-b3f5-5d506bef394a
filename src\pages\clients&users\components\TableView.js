import { Empty, Table } from "antd";
import { useSelector } from "react-redux";
import { TableLoader } from "./SkeletonLoader";
import { useWindowSize } from "./WindowSize";
import { handlePageSizeOptions } from "../../voip/helpers/helpersFunc";
import { useTranslation } from "react-i18next";
import "../index.css";
import { useLocation } from "react-router-dom";
import { isGuestConnected } from "utils/role";
import { useCallback, useMemo } from "react";

const TableView = ({
  columns,
  showColumns,
  dataTable,
  isLoading,
  total,
  pageLimit,
  numberOfPage,
  setNumberOfPage,
  setPageLimit,
  setSelectedRowKeys,
  selectedRowKeys,
  isDataExist,
  tableRef,
  handleSort,
  sortedInfo,
}) => {
  //
  const [t] = useTranslation("common");
  const windowSize = useWindowSize();
  const location = useLocation();
  const sphereRole = useSelector((state) => state?.user?.user?.role);
  const chatRole = useSelector((state) => state?.chat?.currentUser?.role);
  //
  // console.log(111, { columns });
  //
  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };
  //
  // console.log({ isLoading, isDataExist });
  const onChange = (_, __, sorter, extra) => {
    if (extra.action === "sort")
      sorter?.order ? handleSort(sorter) : handleSort({});
  };
  //
  const scrollConfig = useMemo(
    () => ({
      x: showColumns?.totalShowColumns * 220,
      y:
        windowSize?.height -
        (location.pathname === "/settings/users" ||
        location.pathname === "/settings/products"
          ? 380
          : location.pathname === "/settings/guests"
          ? 335
          : 300),
    }),
    [location.pathname, showColumns?.totalShowColumns, windowSize]
  );
  //
  const sortGroupChildren = useCallback((columns, showColumns) => {
    const { groups } = showColumns;

    return columns.map((group, index) => {
      //
      if (index === 0 || group.title === "Timestamps") return group;
      if (!group.children?.length) return group;

      const childrenMap = new Map(
        group.children.map((child) => [child.title, child])
      );

      return {
        ...group,
        children: (groups[group.title]?.allColumns || [])
          .map((title) => childrenMap.get(title))
          .filter(Boolean),
      };
    });
  }, []);

  //
  const showAndHideColumn = useCallback((columns, showColumns) => {
    if (!showColumns?.totalColumns) return [...columns];

    return columns.reduce((acc, group, index) => {
      if (index === 0) return [...acc, group];

      const showColumnsList = showColumns.groups[group.title]?.showColumns;
      if (!showColumnsList) return acc;

      const filteredChildren = group.children?.filter((col) =>
        showColumnsList.includes(col?.title)
      );

      return filteredChildren?.length
        ? [...acc, { ...group, children: filteredChildren }]
        : acc;
    }, []);
  }, []);
  //
  const updateSortOrder = useCallback((columns, fieldId, order) => {
    return columns.map((col) => ({
      ...col,
      ...(Array.isArray(col.children)
        ? { children: updateSortOrder(col.children, fieldId, order) }
        : {
            sortOrder: col.dataIndex === fieldId ? order : null,
          }),
    }));
  }, []);
  //
  // Memoize the columns to prevent unnecessary re-renders
  const memoizedColumns = useMemo(() => {
    if (!columns.length) return [];
    const sortedCols = sortGroupChildren(columns, showColumns);
    const filtered = showAndHideColumn(sortedCols, showColumns);
    return updateSortOrder(filtered, sortedInfo.field, sortedInfo.order);
  }, [columns, showColumns, sortedInfo.field, sortedInfo.order]);
  //
  // Memoize the dataSource to prevent unnecessary re-renders
  const memoizedDataSource = useMemo(() => dataTable, [dataTable]);

  //
  if (!columns?.length) return <TableLoader Table={Table} rowNum={10} />;
  //
  // console.log("isGuestConnected", isGuestConnected());
  const rowSelection = isGuestConnected(chatRole, sphereRole)
    ? undefined
    : {
        selectedRowKeys,
        onChange: onSelectChange,
      };
  //
  return (
    <div className="table-view table-view-families-cell pl-[1px] pr-[2px]">
      <Table
        bordered
        onChange={onChange}
        ref={tableRef}
        columns={memoizedColumns}
        dataSource={memoizedDataSource}
        size={"small"}
        loading={isLoading || (!isLoading && !dataTable?.length && isDataExist)}
        rowSelection={rowSelection}
        sticky={true}
        scroll={scrollConfig}
        locale={{
          triggerAsc: t("contacts.triggerAsc"),
          triggerDesc: t("contacts.triggerDesc"),
          cancelSort: t("contacts.cancelSort"),
          emptyText:
            isLoading || isDataExist ? (
              <div className=" flex h-64 flex-row items-center justify-center space-x-2 text-xl">
                {t("contacts.loadingDataTable")}
                <span className="animate-bounce">...</span>
              </div>
            ) : (
              <Empty />
            ),
        }}
        pagination={
          total <= 10
            ? {
                showTotal: (total, range) =>
                  `${range[0]}-${range[1]} of ${total} items`,
              }
            : {
                showTotal: (total, range) =>
                  `${range[0]}-${range[1]} of ${total} items`,
                showSizeChanger: true,
                // showQuickJumper: true,
                total: total,
                pageSize: total >= pageLimit ? pageLimit : total,
                current: numberOfPage,
                onChange: (page) => setNumberOfPage(page),
                onShowSizeChange: (current, size) => {
                  setPageLimit(size);
                },
                pageSizeOptions: handlePageSizeOptions(total),
                size: "small",
              }
        }
      />
    </div>
  );
};

export default TableView;
