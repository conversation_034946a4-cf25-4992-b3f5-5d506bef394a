import { GET_NOTIF_SUCCESS, GET_NOTIF_ERROR } from "../../constants";
import MainService from "../../../services/main.service";

export const getNotif = (poste) => async (dispatch) => {
    try {
        const response = await MainService.notifApiIPBX(poste);
        dispatch({
            type: GET_NOTIF_SUCCESS,
            payload: response?.data,
        });
    } catch (error) {
        dispatch({
            type: GET_NOTIF_ERROR,
            payload: error,
        });
    }
};
