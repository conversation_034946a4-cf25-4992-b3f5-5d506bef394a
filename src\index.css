@tailwind base;
:root {
  --heightIPBX: 230px;
}
#root {
  width: 100%;
  height: 100%;
}
.ant-typography {
  margin-bottom: 0 !important ;
}
h4.ant-typography {
  @apply mb-0 font-medium text-slate-500;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  margin-bottom: 0.1em !important;
}
@tailwind components;

/* Menu 2  */
.ant-menu-light .ant-menu-item,
.ant-menu-light .ant-menu-submenu {
  @apply text-slate-700;
}

.ant-menu-light .ant-menu-item:hover,
.ant-menu-light .ant-menu-submenu .ant-menu-submenu-title:hover {
  @apply bg-blue-50 text-blue-700 !important;
}
.ant-menu-light:not(.ant-menu-horizontal) .ant-menu-item-selected,
.ant-menu-light:not(.ant-menu-horizontal) .ant-menu-submenu-selected,
.ant-menu-light:not(.ant-menu-horizontal)
  .ant-menu-submenu.ant-menu-submenu-open {
  @apply bg-blue-50 text-blue-700;
}
.ant-menu .ant-menu-item .ant-menu-item-icon,
.ant-menu-light .ant-menu-submenu .ant-menu-item-icon {
  @apply text-slate-400;
}

.ant-menu-light.ant-menu-inline .ant-menu-sub.ant-menu-inline {
  @apply bg-blue-50 pl-7;
}
.ant-menu.ant-menu-light .ant-menu-item:hover .ant-menu-item-icon,
.ant-menu-light
  .ant-menu-submenu
  .ant-menu-submenu-title:hover
  .ant-menu-item-icon {
  @apply text-blue-700;
}
.ant-menu-light:not(.ant-menu-horizontal)
  .ant-menu-submenu-selected
  .ant-menu-item-icon,
.ant-menu-light:not(.ant-menu-horizontal)
  .ant-menu-item-selected
  .ant-menu-item-icon,
.ant-menu-submenu-open,
.ant-menu-light:not(.ant-menu-horizontal)
  .ant-menu-submenu.ant-menu-submenu-open
  .ant-menu-item-icon {
  @apply text-blue-700;
}
.ant-menu.ant-menu-dark
  .ant-menu-item.ant-menu-item-selected
  .ant-menu-item-icon {
  @apply text-white;
}
/* .ant-menu-light .ant-menu-item,
.ant-menu-light .ant-menu-submenu .ant-menu-submenu-title {
  @apply pl-3 !important;
} */

.ant-menu-light.ant-menu-inline .ant-menu-item {
  height: 34px;
  line-height: 34px;
}
/* Fin Menu 2  */

/* Menu principal */
.ant-menu-inline-collapsed > .ant-menu-item,
.ant-menu-inline-collapsed > .ant-menu-submenu > .ant-menu-submenu-title {
  padding-inline: calc(50% - 15px);
}

.ant-menu-vertical > .ant-menu-item {
  line-height: 46px;
}
.uploadGroupWiki:where(.css-dev-only-do-not-override-f8ehde).ant-upload-wrapper
  .ant-upload-list
  .ant-upload-list-item-container {
  transition: none;
}
/* .upload-list-inline .ant-upload-list-item {
  float: left;
  width: 200px;
  margin-inline-end: 8px;
} */
/* Fin Menu Light  */

.ant-upload-rtl.upload-list-inline .ant-upload-list-item {
  float: right;
}

.clickable-row {
  cursor: pointer;
}

.ck-editor__editable_inline {
  min-height: 20vh !important;
  max-height: 80vh !important;
}

.ant-tooltip:has(.profile) {
  display: none !important;
}

.sidebarBottomMenu {
  width: -webkit-fill-available !important;
}

p {
  @apply mb-0 !important;
}

.ant-avatar-string {
  @apply text-sm;
}

/* Tableau */
.ant-table-wrapper
  .ant-table-container
  table
  > thead
  > tr
  > th:first-child:not(.ant-table-selection-column) {
  @apply pl-2;
}
.ant-table-wrapper
  .ant-table:not(.ant-table-bordered)
  .ant-table-tbody
  > tr
  > td:first-child:not(.ant-table-selection-column) {
  @apply pl-2;
}
.ant-pagination-total-text {
  @apply mr-auto;
}

.ant-table-wrapper .ant-table-thead > tr > th {
  @apply text-xs uppercase tracking-wider text-slate-500;
}

.ant-table-wrapper .ant-table {
  @apply text-sm text-slate-700;
}

.ant-table-wrapper .ant-table .ant-table-title,
.ant-table-wrapper .ant-table .ant-table-header,
.ant-table-wrapper
  .ant-table-container
  table
  > thead
  > tr:first-child
  > *:first-child,
.ant-table-wrapper
  .ant-table:not(.ant-table-bordered)
  .ant-table-tbody
  > tr.ant-table-row.ant-table-row-selected
  > td:first-child,
.ant-table-wrapper
  .ant-table:not(.ant-table-bordered)
  .ant-table-tbody
  > tr.ant-table-row.ant-table-row-selected
  > td:last-child {
  @apply rounded-none;
}
.ant-table-wrapper .ant-table-thead > tr > th,
.ant-table-wrapper .ant-table-thead > tr > td {
  @apply bg-slate-50;
}
.ant-table-wrapper
  .ant-table
  .ant-table-tbody
  > tr.ant-table-row:hover
  > td:first-child {
  @apply rounded-none !important;
}

/*  Layouts */

.ant-layout-sider-children {
  @apply flex flex-col;
}
.ant-layout-sider.ant-layout-sider-light.menu2-mailing:not(
    .ant-layout-sider-collapsed
  ) {
  min-width: 240px !important;
}
.ant-layout-sider-collapsed {
  /* overflow-hidden: comment this to enable scroll on collapse menu2. */
  @apply w-16 min-w-0 max-w-none  !important;
}

.ant-layout .ant-layout-sider-children {
  @apply h-auto;
}

.ant-layout .ant-layout-sider-collapsed .ant-layout-sider-children {
  @apply h-full;
}

.ant-layout .ant-layout-sider-light,
.ant-menu-light.ant-menu-root.ant-menu-inline,
.ant-menu-light.ant-menu-root.ant-menu-vertical {
  @apply bg-slate-50;
}

.ant-menu-light.ant-menu-root.ant-menu-inline,
.ant-menu-light.ant-menu-root.ant-menu-vertical {
  @apply border-r-0 px-2;
}

.ant-layout .ant-layout-header {
  line-height: 57px;
  height: 57px;
}

/* .ant-list .ant-list-item {
  @apply px-4 py-1.5 bg-white cursor-pointer;
}

.ant-divider-horizontal.ant-divider-with-text {
  @apply my-1;
} */

/* CHAT */
.ant-badge .ant-badge-dot {
  width: 7px;
  height: 7px;
  min-width: 7px;
}

.scrollableDiv {
  height: calc(100vh - var(--heightIPBX));
}

.ant-form-item {
  @apply mb-3;
}



.ant-avatar {
  @apply border-0;
}

.ant-list.membersList .ant-list-item .ant-list-item-meta {
  @apply flex items-center;
}

.ant-list.membersList
  .ant-list-item
  .ant-list-item-meta
  .ant-list-item-meta-title {
  @apply mb-0  text-slate-900;
}
.ant-list.membersList
  .ant-list-item
  .ant-list-item-meta
  .ant-list-item-meta-description {
  @apply text-slate-500;
}

.ant-list.membersList
  .ant-list-item.isActive
  .ant-list-item-meta
  .ant-list-item-meta-title {
  @apply font-semibold text-slate-700;
}
.ant-list.membersList
  .ant-list-item.isActive
  .ant-list-item-meta
  .ant-list-item-meta-description {
  @apply font-medium text-slate-700;
}

.ant-list .ant-list-item .ant-list-item-meta .ant-list-item-meta-title {
  @apply text-sm;
}

.ant-list .ant-list-item .ant-list-item-meta .ant-badge .ant-badge-dot {
  @apply h-2 w-2;
}
.ant-list .ant-list-item .ant-list-item-meta .ant-badge .ant-badge-dot {
  box-shadow: 0 0 0 1.5px #ffffff;
}
.ant-list.membersList .ant-list-item.isActive .time {
  @apply font-medium text-slate-700;
}
.ant-divider .ant-divider-inner-text {
  @apply text-xs  tracking-widest text-slate-500;
}

.ant-list.membersList .ant-list-item {
  @apply rounded-md border-b-0 px-2 transition-all  duration-300 hover:bg-slate-200;
}
.ant-list.membersList .ant-list-item.notOpenDropDown {
  @apply rounded-md border-b-0 transition-all  duration-300 hover:bg-slate-200;
}
.ant-list.membersList .ant-list-item.openDropDown {
  @apply rounded-md border-b-0 transition-all duration-300 hover:bg-slate-200;
}

.ant-list.membersList .ant-list-item {
  @apply py-1;
}

.ant-list.membersList
  .ant-list-item
  .ant-list-item-meta
  .ant-list-item-meta-avatar {
  @apply mr-2;
}
.ant-list.membersList
  .ant-list-item
  .ant-list-item-meta
  .ant-list-item-meta-description {
  @apply truncate text-xs;
}
.ant-list.membersList .ant-list-item {
  @apply mb-1;
}
.ant-divider-vertical {
  @apply mx-1 border-slate-300;
}

.response .message {
  border-left: 3px solid #ccc;
  padding-left: 6px;
  padding-top: 1px;
}
.ant-typography p {
  margin-bottom: 0.5rem !important;
}
.ant-list.membersList .time {
  font-size: 10px;
}

.time {
  @apply text-slate-400;
}
/* MESSAGES */
.ant-list.messagesList .ant-list-item {
  @apply relative items-start justify-start rounded-md  px-2 py-1 transition duration-300 hover:bg-slate-50;
}
.drawer .ant-list-split .ant-list-item {
  /* @apply border-0; */
  padding: 2px 0px !important;
}
.drawer .ant-tabs-content-holder {
  @apply h-full overflow-y-auto;
}
.ant-list.messagesList
  .ant-list-item
  .ant-list-item-meta
  .ant-list-item-meta-title {
  @apply mb-0  text-sm font-medium text-slate-900;
}
.ant-list.messagesList
  .ant-list-item
  .ant-list-item-meta
  .ant-list-item-meta-description {
  @apply text-slate-700;
}
.ant-list.messagesList .ant-list-item .ant-list-item-meta {
  @apply flex items-start;
}
.ant-list.messagesList
  .ant-list-item
  .ant-list-item-meta
  .ant-list-item-meta-avatar {
  @apply mr-2;
}
.ant-list.messagesList .time {
  @apply text-xs;
}
.ant-list.messagesList .actions {
  @apply absolute right-0 top-0 z-50 hidden rounded-full bg-white px-1 py-0.5 shadow-md;
}

/* Infos */
.ant-descriptions-item-container {
  @apply flex flex-col;
}
.ant-descriptions .ant-descriptions-item-label {
  @apply text-slate-500;
}

.file {
  @apply flex cursor-pointer items-center  justify-between rounded-lg border border-solid border-slate-300 bg-white px-3 py-1 shadow-sm transition-all duration-300 hover:bg-slate-100;
}
.message .file {
  @apply max-w-lg;
}

/* DRAWER CHAT */

/* END CHAT */

.ant-card-small > .ant-card-body {
  @apply bg-slate-50 pb-0;
}

/* .ant-card .ant-card-actions {
  @apply bg-slate-100;
} */

/* .ant-card-small>.ant-card-body {
  @apply px-0;
} */

/* .ant-input {
  @apply text-sm text-slate-600;
} */

.ant-card .ant-card-actions > li {
  @apply m-0 py-3;
}

.ant-card .ant-card-actions {
  @apply items-center;
}

.ant-input-group.ant-input-group-compact {
  @apply flex;
}

/*
  .ant-menu .ant-menu-item, .ant-menu .ant-menu-submenu, .ant-menu .ant-menu-submenu-title {
    @apply rounded-md !important;

  }
  .ant-menu-inline .ant-menu-item {
    @apply my-0.5;
  }
  .ant-menu-light.ant-menu-inline .ant-menu-sub.ant-menu-inline {
    @apply pb-1;
  }
  .ant-menu-inline .ant-menu-item, .ant-menu-inline >.ant-menu-item, .ant-menu-inline >.ant-menu-submenu>.ant-menu-submenu-title {
    height: 32px;
    line-height: 32px;
  }
  .ant-menu-submenu-title, .ant-menu-item {
    @apply pl-2 !important;
  }
  .ant-menu-light.ant-menu-inline .ant-menu-sub.ant-menu-inline {
    @apply bg-gray-100;
  }
  .ant-menu-item.ant-menu-item-only-child {
    @apply pl-10 !important;

  }

.ant-badge.ant-badge-status .ant-badge-status-text {
    @apply text-xs text-slate-600
  }

  .ant-badge .ant-badge-dot {
    top: -20px;

  }
*/
.ant-radio-button-wrapper-disabled:where(
    .css-dev-only-do-not-override-1kl525f
  ).ant-radio-button-wrapper-checked {
  background-color: #2253d5;
  color: white;
}

.trigger {
  @apply pl-6 pr-3 text-slate-500 hover:text-blue-600;
}

.logo {
  height: 32px;
  margin: 16px;
  background: rgba(255, 255, 255, 0.3);
}

::-webkit-scrollbar {
  width: 5px;
  height: 5px;
  border: 1px;
  border-radius: 2.5px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
  max-width: 3px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #888;
  max-width: 3px;
  border-radius: 4px;
}

.rc-virtual-list-scrollbar-thumb {
  max-width: 6px;
  min-height: 12px;
  right: 0;
  cursor: pointer;
}

/* Hide scroll handlers in element card */
/*************************/
.hide-scrollbar-element-card::-webkit-scrollbar-thumb {
  display: none;
}

.hide-scrollbar-element-card.rc-virtual-list-scrollbar-thumb {
  display: none;
}

.hide-scrollbar-element-card::-webkit-scrollbar-track {
  display: none;
}
/*************************/

.rc-virtual-list-scrollbar.rc-virtual-list-scrollbar-vertical {
  background: #f1f1f1;
  max-width: 6px;
  visibility: visible !important;
}
/*  virtual list part */

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #555;
  max-width: 4px;
  border-radius: 4px;
}
.line-clamp {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
pre {
  white-space: break-spaces;
}
.syncData .ant-modal-content .ant-modal-header {
  margin-bottom: 0 !important;
}
.ant-modal .ant-modal-header {
  @apply mb-4;
}

.ant-modal .ant-modal-title {
  @apply text-base font-medium text-slate-700;
}

.ant-menu .ant-menu-item-divider {
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.ant-menu- :not(.ant-menu-horizontal) .ant-menu-item-selected,
.ant-menu- :not(.ant-menu-horizontal) .ant-menu-item:focus {
  @apply bg-gray-800 text-white;
}

/* .ant-menu-inline-collapsed>.ant-menu-item {
  @apply p-0
} */

/*  Drawer */

.ant-drawer .ant-drawer-title {
  @apply text-base text-slate-500;
}
.ant-form-vertical .ant-form-item-label {
  @apply pb-0;
}

.ant-form-item .ant-form-item-label > label {
  @apply pb-1 font-medium text-slate-500;
}

/* tasks */
.ant-select-item.ant-select-item-option.selectOption.ant-select-item-option-active {
  @apply rounded-lg border-b-0 transition-all duration-300 hover:bg-slate-200 hover:pl-1.5;
}

.removeCSS {
  all: unset;
}

::-webkit-scrollbar {
  width: 5px;

  height: 5px;

  border: 1px;

  border-radius: 2.5px;
}

/* Track */

::-webkit-scrollbar-track {
  background: #f1f1f1;

  max-width: 3px;
}

/* Handle */

::-webkit-scrollbar-thumb {
  background: #888;

  max-width: 4px;

  border-radius: 4px;
}

/* Handle on hover */

::-webkit-scrollbar-thumb:hover {
  background: #555;

  max-width: 4px;

  border-radius: 4px;
}

/* .ant-form-item {
  @apply pb-2 mb-2
} */

/* Collegues VoIP */
.demo-loadmore-list {
  min-height: 350px;
}

@tailwind utilities;

/*to disable the scroll when the dropdown is open */
/* .messagesList.ant-list-items {
  overflow: hidden !important;
}
.memmbersList .ant-list-items {
  overflow: hidden !important;
} */

.modal-addGroup .ant-modal-body {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
  overflow-x: auto;
  margin-right: -23px;
}
.ant-table-row.editingRow {
  display: table-row;

  vertical-align: top;
}
.flipflop .g2-html-annotation {
  font-size: 15px !important;
}

.homeCopyable .anticon.anticon-copy {
  color: rgb(249 250 251);
}

.itemDashboard:hover,
.itemDashboard:hover .cardDashboard {
  text-decoration: none;
  color: #fff;
}
.itemDashboard:hover .cardDashboard {
  /* -webkit-transform: scale(10);
  -ms-transform: scale(10);
  transform: scale(10); */
  background-color: red !important;
}

.card {
  padding: 56px 16px 16px 16px;
  border-radius: 15px;
  cursor: pointer;
  position: relative;
  transition: box-shadow 0.25s;
}

.card::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: 15px;
}

.card .icon {
  z-index: 2;
  position: relative;
  display: table;
  padding: 8px;
}

.card .icon::after {
  content: "";
  position: absolute;
  inset: 4.5px;
  border-radius: 50%;
  backdrop-filter: blur(2px);
  transition: background-color 0.25s, border-color 0.25s;
}

.card .icon svg {
  position: relative;
  z-index: 1;
  display: block;
  width: 24px;
  height: 24px;
  transform: translateZ(0);
  transition: color 0.25s;
}

.card h4 {
  z-index: 2;
  position: relative;
  margin: 12px 0 4px 0;
  font-family: inherit;
  font-weight: 600;
  font-size: 14px;
  line-height: 2;
}

.card p {
  z-index: 2;
  position: relative;
  margin: 0;
  font-size: 14px;
  line-height: 1.7;
}

.card .shine {
  border-radius: inherit;
  position: absolute;
  inset: 0;
  z-index: 1;
  overflow: hidden;
  opacity: 0;
  transition: opacity 0.5s;
}

.card .shine::before {
  content: "";
  width: 150%;
  padding-bottom: 150%;
  border-radius: 50%;
  position: absolute;
  left: 50%;
  bottom: 55%;
  filter: blur(35px);
  opacity: var(--card-shine-opacity);
  transform: translateX(-50%);
  background-image: var(--card-shine-gradient);
}

.card .background {
  border-radius: inherit;
  position: absolute;
  inset: 0;
  overflow: hidden;
  -webkit-mask-image: radial-gradient(
    circle at 60% 5%,
    black 0%,
    black 15%,
    transparent 60%
  );
  mask-image: radial-gradient(
    circle at 60% 5%,
    black 0%,
    black 15%,
    transparent 60%
  );
}

.card .background .tiles {
  opacity: 0;
  transition: opacity 0.25s;
}

.card .background .tile {
  position: absolute;
  background-color: var(--card-tile-color);
  animation-duration: 8s;
  animation-iteration-count: infinite;
  opacity: 0;
}

.ant-pagination-options-quick-jumper {
  padding-right: 1rem;
}
.ant-notification .ant-notification-notice .ant-notification-notice-message {
  margin-bottom: 0 !important;
}
#webpack-dev-server-client-overlay-div {
  position: none !important;
}
.ant-btn-circle.ant-btn-text.buttonIconCheck:hover {
  background-color: rgba(0, 128, 0, 0.075);
}

/* ... Rest of the code ... */

.ant-row.container:hover .cardActivities .ant-card.notchoiced {
  opacity: 0.5;
}
.ant-row.container .cardActivities .ant-card.notchoiced:hover {
  transform: perspective(800px) rotateY(0deg);
  opacity: 1;
}
.tabs360-general .ant-tabs-nav {
  position: sticky;
  top: 1px;
  z-index: 100;
  background: white;
}
.tabs-360-activities .ant-tabs-nav {
  position: sticky;
  top: 40px;
  z-index: 100;
  background: white;
}
.sider-360 .ant-layout-sider-children {
  position: sticky;
  top: 0;
}
.rc-virtual-list.list-activities-360 .rc-virtual-list-holder {
  margin-right: -15px;
  padding-right: 15px;
}

.rc-virtual-list.list-overview-360 .rc-virtual-list-holder {
  margin-right: -20px;
  padding-right: 15px;
}

/* audio::-webkit-media-controls-play-button,
     audio::-webkit-media-controls-panel {
     background-color: white;
     color: #000;
     } */

.templateEmail .ql-tooltip.ql-editing {
  left: 0 !important;
  top: 0 !important;
}

.skeletonSelectDepartments .ant-skeleton.ant-skeleton-element {
  width: 100%;
}
.ql-omega:after {
  content: "⛶";
  font-size: 20px;
  margin-top: -7px;
  display: flex;
  font-weight: bold;
}
.ql-deleteTable:after {
  content: "X";
  font-size: 20px;
  margin-top: -7px;
  display: flex;
  font-weight: bold;
}
.content-wiki .ql-editor {
  background: white;
}
.custom-dropdown .ant-select-dropdown {
  max-height: 200px; /* Ajustez la hauteur selon vos besoins */
  overflow-y: auto;
}

.selectUserPostApi .ant-select-selector {
  border: 0 !important;
}
.selectUserPostApi .ant-select-selection-item {
  background: transparent !important;
}

.btnSelectedUser.ant-btn-link:disabled {
  cursor: auto;
}

.selectUserPostApi .ant-select-selection-overflow {
  flex-wrap: nowrap !important;
  overflow-x: auto;
  overflow-y: hidden;
}
.editorTemplate .ql-editor {
  min-height: 200px;
  height: 100%;
}
.senderSmsTemplateEmail label {
  width: 100% !important;
}
.senderSmsTemplateEmail .ant-col.ant-form-item-label {
  width: 100% !important;
}
.ql-editor table {
  position: relative;
}
.ql-editor table td {
  position: relative;
  z-index: 1;
}
.ql-editor table td:before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: 15px;
  z-index: -1;
  background-color: #fff;
}
.moyen_ringing_calls .ant-statistic-content-value span:first-child {
  display: none;
}
.moyen_ringing_calls .ant-statistic-content-value {
  /* display: none; */
  visibility: hidden;
  width: 0;
}
.action-mail {
  @apply absolute right-0 z-10 flex h-full items-center;
}
.list-activities-360
  .ant-list-item
  .ant-list-item-meta
  .ant-list-item-meta-content {
  width: 100% !important;
}
.ql-picker-options {
  z-index: 999 !important;
}
/* .tabs360-general .ant-tabs-nav-more{
  display: none;

} */
/* .tabs360-general .ant-tabs-nav-wrap{
max-width: 100% !important;
overflow: auto !important;
} */
.ant-statistic-content {
  display: flex !important;
  align-items: center !important;
}
.ant-statistic-content-prefix {
  display: flex !important;
}
.v2 .ant-timeline-item-head-custom {
  background: transparent !important;
}
.menuViewSphereLeft .ant-menu-item-divider {
  border: 1px solid #00000012;
}
.menuViewSphere .ant-menu-item-divider {
  border: 1px solid #00000012;
}
iframe#webpack-dev-server-client-overlay {
  display: none !important;
}
.lineViewSphere {
  display: inline-block;
  border-top: 1px solid #cbd5e1;
  width: 2em;
}
#showMeViewSphere {
  animation: cssAnimation 0s 400ms forwards;
  visibility: hidden;
}

@keyframes cssAnimation {
  to {
    visibility: visible;
  }
}
.ant-btn.associateViewSphere:hover {
  color: black !important;
}
.viewSphere .ant-layout-sider-trigger {
  position: absolute;
  bottom: 65px;
  background: transparent;
  color: black;
  font-size: medium;
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
}
.ViewSphereExterne .ant-layout-sider-trigger {
  background: transparent;
  color: black;
  font-size: medium;
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
}

.menuViewSphere.ant-menu-vertical > .ant-menu-item {
  line-height: 40px;
  height: 40px;
}
.menuViewSphere.ant-menu-inline-collapsed > .ant-menu-item {
  inset-inline-start: 0;
  /* padding-inline: calc(50% - calc(16px / 2) - 4px); */
  text-overflow: clip;
}
.menuViewSphere .ant-menu-vertical {
  /* padding-left: 12px !important; */
  padding-left: 0 !important;
}
.menu1ViewSphere .ant-menu-item {
  /* padding-left: 12px !important; */
  padding-inline: 10px !important;
}
.menuViewSphere .ant-menu-item-icon {
  color: #170848 !important;
}
/* .menuViewSphere .ant-menu-item{
  padding-left: initial !important;
} */
/* .menuViewSphere .ant-menu-item:not(.ant-menu-item-selected):hover{
  background: #0000000f !important;

}
.menuViewSphere .ant-menu-item.ant-menu-item-selected{
  background: #00000006 !important;

} */
.viewSphereSelect .ant-badge.ant-badge-status.ant-badge-not-a-wrapper {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}
.selected-row {
  background-color: #edf5ff !important;
  cursor: text !important;
}
.selected-row .ant-table-cell {
  background: transparent !important;
}
.selected-row:hover {
  background-color: #edf5ff;
}

.dragTable .ant-table-row:hover {
  /* background-color: #EDF5FF !important; */

  cursor: pointer;
}
.ant-table-cell.remove_border_right_tr {
  padding-left: 8px !important;
}
.menuViewSphere .ant-menu-item-disabled:hover {
  color: #00000040 !important;
  background: transparent !important;
}
.menuLeftViewSphere .ant-menu-item-disabled:hover {
  color: #00000040 !important;
  background: transparent !important;
}
.chatPopover .textEditor {
  width: 515px;
}

.chatPopoverMax .textEditor {
  width: calc(100vw - 215px);
  max-width: calc(100vw - 215px) !important;
}
.chatPopoverDrawerMax .textEditor {
  width: 870px;
  max-width: 870px !important;
}
.chatviewsphere .typing {
  width: auto !important;
  top: -17px;
}
.chatPopover .typing {
  width: auto !important;
}
.chatViewSphereCollapsed .textEditor {
  width: calc(100vw - 365px);
  max-width: calc(100vw - 365px) !important;
}
.chatViewSphere .textEditor {
  width: calc(100vw - 427px);
  max-width: calc(100vw - 427px) !important;
}
.chatViewSphereCollapsedGuest .textEditor {
  width: calc(100vw - 275px);
  max-width: calc(100vw - 275px) !important;
}
.chatViewSphereGuest .textEditor {
  width: calc(100vw - 340px);
  max-width: calc(100vw - 340px) !important;
}
.fromchat .textEditor {
  width: calc(100vw - 460px);
  max-width: calc(100vw - 460px) !important;
}
.chatViewSphereCollapsedFromDrawer .textEditor {
  width: 817px;
}
.chatViewSphereCollapsedFromDrawer .fromchat .textEditor {
  width: 785px;
}

.chatViewSphereFromDrawer .textEditor {
  width: 676px;
}
.chatViewSphere .typing {
  width: auto !important;
}
.chatViewSphereCollapsed .typing {
  width: auto !important;
}
.menuViewSphere .ant-menu-item-disabled span {
  color: rgba(0, 0, 0, 0.25) !important;
}
.menuViewSphereLeft .ant-menu-item-disabled:hover {
  color: rgba(0, 0, 0, 0.25) !important;
  background: transparent !important;
}
.menuViewSphereLeft .ant-popover {
  z-index: 11 !important;
}

.ant-image-preview-img-wrapper {
  padding-left: 5rem !important;
  padding-right: 1rem !important;
}

.ant-image-preview-switch-left {
  left: 5rem !important;
}
.highcharts-credits {
  display: none !important;
}
.highcharts-title {
  font-size: 1em !important;
}
.chatviewsphere #chat {
  padding-bottom: 0 !important;
}
.table-voip-stats .ant-table-thead th {
  background-color: #f1f5f9 !important;
}
.table-voip-stats .ant-table-thead th:hover {
  background-color: #e2e8f0 !important;
}
.selectViewSphere .ant-select-selector{
  padding: 0 !important;
  padding-right: 7px !important;
}
.alertDeal .ant-alert-message{
  margin-bottom: 0!important;
}
.alertDeal{
  background: transparent !important;
  border:0;
  padding:2px !important
}
.alertDeal .ant-alert-icon{
 font-size: 20px !important;
}
.appMobile-input-container {
  position: relative;
  margin: 10px 0;
}

.appMobile-input-label {
  position: absolute;
  left: 10px;
  top: -10px;
  background: white; /* Couleur de fond pour cacher l'input */
  padding: 0 5px; /* Espace autour du texte */
  color: #999; /* Couleur du texte */
  transition: 0.2s; /* Transition pour l'animation */
}

.appMobile-input-field {
  width: 100%;
  padding: 8px;
  border: 2px solid #ccc;
  border-radius: 5px;
}

.appMobile-input-field:focus + .appMobile-input-label,
.appMobile-input-field:not(:placeholder-shown) + .appMobile-input-label {
  top: -10px;
  left: 5px;
  font-size: 12px;
  color: #333; /* Couleur en focus */
}

.ant-tour-indicators{
  display: flex !important;
  flex-grow: 1 !important;
}
.ant-statistic-title{
  color:#6b7280 !important;
}
.cardGraphDashboard{
  width: 100% !important;
  height: 174px !important;
  padding:0 2px !important;
  background: white !important;
  border-top-right-radius: 6px !important;
  border-bottom-right-radius: 6px !important;
}
.profileDropdown li:first-child {
  cursor: text !important;
}
.profileDropdown li:first-child:hover {
  background: transparent !important;
}
.lean-notification {
  will-change: transform, opacity;
}
.association-table-viewSphere .ant-table-body{
  /* overflow: hidden !important; */
  overflow-y: hidden !important;
  overflow-x: auto !important;

}
.filterTable .ant-popover-title{
  margin-bottom: 0!important;
}
.association-table-viewSphere .ant-table-thead > tr > th,
.association-table-viewSphere .ant-table-tbody > tr > td {
  min-width: 180px !important;
}
.iconConfigCompany .ant-upload-list-item-container{
  width: 100% !important;
  height: 60px !important;
}
.iconConfigCompany .ant-upload.ant-upload-select{
  width: 100% !important;
  height: 60px !important;
}

.items-associations-view-sphere .ant-list-item-meta-title{
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}