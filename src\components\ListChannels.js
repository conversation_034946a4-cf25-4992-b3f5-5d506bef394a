import React, { useEffect, useRef } from "react";
import { Form, Typography, Input, Button, Select, Space, Badge } from "antd";
import { useState } from "react";
import { generateAxios } from "../services/axiosInstance";
import { useTranslation } from "react-i18next";
import { PlusCircleOutlined } from "@ant-design/icons";
import { allIcons } from "./Icons";
import ChoiceIcons from "../pages/components/ChoiceIcons";
import { toastNotification } from "./ToastNotification";
import Header from "./configurationHelpDesk/Header";
import NewTableDraggable from "./NewTableDraggable";
import { setSearch } from "../new-redux/actions/menu.actions/menu";
import { useDispatch, useSelector } from "react-redux";
import LabelTable from "./LabelTable";
import BottomButtonAddRow from "./BottomButtonAddRow";
import { SubmitKeyPress } from "../utils/SubmitKeyPress";
import { URL_ENV } from "index";
import ColumnColors from "./ColumnColors";
import { colors } from "./Colors";

const ListChannels = () => {
  const [form] = Form.useForm();
  const [count, setCount] = useState(0);
  const [dataform, setDataForm] = useState({});
  const [data, setData] = useState([]);
  const [editingKey, setEditingKey] = useState("");
  const [id, setId] = useState(null);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [oldPage, setOldPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  const isEditing = (record) => record.key === editingKey;
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const { search } = useSelector((state) => state.form);
  const inputRefs = useRef([]);

  useEffect(() => {
    inputRefs.current.forEach((input) => {
      input?.focus();
    });
  }, [id, data.length]);
  // useEffect(() => {
  //   if (editingKey && oldPage !== currentPage) {
  //     setData((prev) => prev.filter((el) => el.id));
  //     setEditingKey("");
  //   }
  // }, [currentPage, editingKey, oldPage]);
  const onFinishFailed = (values) => {
    console.log(values);
  };
  const handleKeyPress = (event) => {
    SubmitKeyPress(event, form);
  };
  const EditableCell = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    const inputNode =
      inputType === "select" ? (
        <Select
          placeholder={title}
          showSearch
          style={{
            minWidth: 250,
          }}
          options={allIcons.map((el) => ({
            label: (
              <Space className="px-1">
                <Typography.Text>{el.label}</Typography.Text>
                <Typography.Text type="secondary">
                  {el.value.replaceAll("Outlined", "")}{" "}
                </Typography.Text>
              </Space>
            ),
            value: el.value,
          }))}
          optionFilterProp="children"
          filterOption={(input, option) =>
            (option?.value.toLowerCase() ?? "").includes(input.toLowerCase())
          }
          filterSort={(optionA, optionB) =>
            (optionA?.value ?? "")
              .toLowerCase()
              .localeCompare((optionB?.value ?? "").toLowerCase())
          }
          allowClear
        />
      ) : inputType === "radio" ? (
        // <GroupColors color={color} setColor={setColor} />
        <Select
          showSearch
          placeholder={t("tags.selectcolor")}
          style={{
            minWidth: 100,
          }}
          options={colors.map((el) => ({
            label: (
              <Space>
                <Badge color={el.value} /> {t(`colors.${el.label}`)}
              </Space>
            ),
            value: el.value,
          }))}
          optionFilterProp="children"
          filterOption={(input, option) =>
            (
              colors
                .find((el) => el.value === option.value)
                ?.label?.toLowerCase() ?? ""
            ).includes(input.toLowerCase())
          }
          filterSort={(optionA, optionB) =>
            (optionA?.value ?? "")
              .toLowerCase()
              .localeCompare((optionB?.value ?? "").toLowerCase())
          }
          allowClear
        />
      ) : (
        <Input
          ref={(el) => (inputRefs.current[0] = el)}
          onKeyPress={handleKeyPress}
          placeholder={t("activities.name")}
        />
      );
    return (
      <td {...restProps}>
        {editing ? (
          <Form.Item
            name={dataIndex.toLowerCase()}
            style={{
              margin: 0,
            }}
            rules={[
              {
                // required: dataIndex === "icon" ? false : true,
                required: dataIndex === "color" ? false : true,
                message: `${t(`channels.${dataIndex.toLowerCase()}`)} ${t(
                  "table.header.isrequired"
                )}`,
              },
            ]}
          >
            {inputNode}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };
  const edit = (record) => {
    if (record) {
      form.setFieldsValue({
        label: record.label,
        icon: record.icon,
        color: record.color,
      });
      setDataForm({
        label: record.label,
        icon: record.icon,
        color: record.color,
      });
      setId(record.id);
    } else {
      form.setFieldsValue({
        label: "",
        icon: "",
        color: "",
      });
    }
    setEditingKey(record.key);
  };
  const cancel = (record) => {
    setEditingKey("");
    setId(null);
    if (!record.id) {
      setData(data.filter((item) => item.key !== record.key));
    }
  };
  const save = async () => {
    setLoading(true);
    if (id) {
      try {
        const row = await form.validateFields();
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post(`/channels/update/${id}`, {
          label: row.label,
          icon: row.icon === undefined ? "" : row.icon,
          color: row.color === undefined ? "" : row.color,
        });
        setEditingKey("");
        setData(
          data.map((el) =>
            el.id === res.data.data.id
              ? {
                  ...res.data.data,
                  key: res.data.data.id,
                }
              : el
          )
        );
        form.setFieldsValue({
          label: "",
          icon: "",
          color: "",
        });
        setDataForm({});
        setLoading(false);
        toastNotification("success", row.label + t("toasts.edit"), "topRight");
      } catch (errInfo) {
        setLoading(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    } else {
      try {
        const row = await form.validateFields();
        console.log(row);
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post("/channels", row);
        setEditingKey("");
        setData([
          ...data.filter((el) => el.id),
          { ...res.data.data, key: res.data.data.id },
        ]);
        form.setFieldsValue({
          label: "",
          icon: "",
          color: "",
        });
        setDataForm({});

        setLoading(false);
        toastNotification(
          "success",
          row.label + t("toasts.created"),
          "topRight"
        );
      } catch (errInfo) {
        setLoading(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    }
  };
  useEffect(() => {
    const getChannels = async () => {
      setLoading(true);
      try {
        const {
          data: { data },
        } = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get("/channels");
        setData(data.map((el, i) => ({ ...el, key: el.id })));
        if (data.length > 0) {
          setCount(Math.max(...data.map((el) => el.id)));
        }
        setLoading(false);
      } catch (err) {
        console.log(err);
        setLoading(false);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    };
    getChannels();
    return () => dispatch(setSearch(""));
  }, []);

  const handleClick = (event) => {
    event.stopPropagation();
  };
  const columns = [
    {
      title: t("activities.name"),
      dataIndex: "label",
      key: "label",
      editable: true,
      width: "200px",
      sorter: (a, b) => a.label.localeCompare(b.label),
      render: (_, record) => {
        return (
          <LabelTable record={record} editingKey={editingKey} edit={edit} />
        );
      },
    },
    {
      title: t("activities.icon"),
      dataIndex: "icon",
      key: "icon",
      editable:
        data.find((el) => el.id === editingKey)?.system === 1 ? true : true,
      render: (_, { icon }) => (
        <div
          className={`${
            data.find((el) => el.id === editingKey)?.system === 1
              ? "absolute top-1/2 -translate-y-1/2 transform"
              : "relative "
          }`}
        >
          <ChoiceIcons icon={icon} fontSize={16} />
        </div>
      ),
    },
    {
      title: t("activities.color"),
      dataIndex: "color",
      key: "color",

      editable: true,
      render: (_, { color }) => <ColumnColors color={color} colors={colors} />,
    },
  ];

  const handleAdd = () => {
    const ids = data.map((object) => {
      return object.id;
    });
    setId(null);

    const newData = {
      key: Math.max(...ids) + 1,
      label: `  `,
      color: null,
      disabled: true,
    };
    setCurrentPage(Math.ceil([...data, newData].length / pageSize));
    setOldPage(Math.ceil([...data, newData].length / pageSize));

    setData([...data, newData]);
    form.setFieldsValue({
      label: "",
      color: null,
    });
    setEditingKey(Math.max(...ids) + 1);
    setCount(Math.max(...ids) + 1);
  };
  const onRow = () => {};
  const filteredData = data.filter((item) => {
    return item.label?.toLowerCase().includes(search.toLowerCase());
  });
  return (
    <Space direction="vertical" style={{ width: "100%" }}>
      <Header
        active={"4"}
        editingKey={editingKey}
        handleAdd={handleAdd}
        btnText={t("helpDesk.addChannel")}
        disabled={loading ? true : editingKey ? true : search ? true : false}
        api={"channels"}
      />

      <NewTableDraggable
        columns={columns}
        setLoading={setLoading}
        isEditing={isEditing}
        data={filteredData}
        setData={setData}
        loading={loading}
        save={save}
        edit={edit}
        EditableCell={EditableCell}
        onFinishFailed={onFinishFailed}
        cancel={cancel}
        form={form}
        apiRank="change-channel-rank"
        editingKey={editingKey}
        api="channels"
        onRow={onRow}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        pageSize={pageSize}
        setPageSize={setPageSize}
      />
      <BottomButtonAddRow
        editingKey={editingKey}
        data={filteredData}
        text={t("helpDesk.addChannel")}
        handleAdd={handleAdd}
        loading={loading}
        search={search || ""}
      />
    </Space>
  );
};
export default ListChannels;
