import React, { useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { MdOutlineTimer, MdOutlineQueryBuilder } from "react-icons/md";
import { Button, Modal, notification } from "antd";
import { useTranslation } from "react-i18next";

import { updateMessages } from "../pages/layouts/chat/utils/rqUpdate";
import {
  getName,
  getOrGenerateTabId,
} from "../pages/layouts/chat/utils/ConversationUtils";
import { RightCircleOutlined } from "@ant-design/icons";
import { store } from "../new-redux/store";
import {
  SET_NEW_MISSED_CALL,
  SET_NEW_VOICE_MESSAGING,
  SET_FORWARDING_CALL,
  SET_NEW_LOG_GROUPS_QUEUES,
  SET_NEW_TAG_GROUPS_QUEUES,
  UPDATE_NOTIFICATION_MAILING,
  SET_RETURNED_QUEUE_MISSED_CALL,
  SET_NEW_MISSED_QUEUE_GROUP,
  UPDATE_CURRENT_USER_STATUS_PRESENCE,
  SET_CALL_FIRST_TIME,
  SET_CONTACT_INFO_FROM_DRAWER,
  SET_USER_INFO_CHAT,
  SET_CONF_INFO,
  RESET_CONF_INFO,
  SET_NEW_UPDATE_ON_MESSAGE,
} from "../new-redux/constants";
import { toastNotification } from "../components/ToastNotification";
import {
  // setEventMercureTasks,
  setIsUserNotified,
  setNewIncomingTaskNotification,
  setNotificationDescription,
  setNotificationPayload,
  setRelationId,
  setRelationType,
  setRemindersArray,
  setTaskNotificationAction,
  setTotalNotificationsNumber,
} from "../new-redux/actions/tasks.actions/realTime";
import { AvatarChat } from "../components/Chat";
import { getTokenRoom } from "../new-redux/actions/visio.actions/createVisio";
import { setIsImportJobDone } from "../new-redux/actions/import.actions/realtime";
import useActionCall from "../pages/voip/helpers/ActionCall";
import {
  logoutOut,
  setUserInfos,
} from "../new-redux/actions/user.actions/getUser";
import {
  createVisioMercure,
  deleteVisioMercure,
  setCountNotificationVisio,
  setCountReminders,
  setNotificationList,
  setRemindersList,
  updateVisioMercure,
} from "../new-redux/actions/visio.actions/visio";
import { moment_timezone } from "../App";
import {
  setHasNewMessage,
  setRefreshMailInbox,
  setRefreshNumberNotification,
  setSynchroType,
  updateEmailsStats,
} from "../new-redux/actions/mail.actions";
import { URL_ENV } from "index";
import {
  setEndBuild,
  setInavlidConfigMail,
} from "new-redux/actions/menu.actions/menu";
import {
  setCallFirstTime,
  setNewCallLog,
} from "new-redux/actions/voip.actions/setNewCallLog";
import dayjs from "dayjs";
import { assignTranslatedRole } from "utils/assignTranslatedRole";
import { getLogs } from "new-redux/actions/voip.actions/getLogs";
import {
  actionCallFromRmc,
  generateUrlToView360,
  humanDate,
  truncateString,
} from "pages/voip/helpers/helpersFunc";
import { CiMail } from "react-icons/ci";
import { UpdateSelectedEmail } from "../pages/rmc/mailing/services/ActionsApi";
import { updateChatSelectedConversation } from "../new-redux/actions/chat.actions";
import { handleEventMercure } from "pages/clients&users/helpers";
import { checkShowNotification } from "../utils/real-time-function/chat/function";
import {
  getStatsCall,
  getStatsTasks,
  setQueueInDashboard,
  setTasksInDashboard,
} from "new-redux/actions/dashboard.actions";
import {
  addFile360,
  deleteFileForMercure,
  updateFileForMercure,
  updateFileLabelForMercure,
} from "new-redux/actions/files.actions/files";
import {
  addSelfNote,
  addToNotesNotificationsList,
  decrementNotesNotificationsCount,
  deleteSelfNote,
  incrementNotesNotificationsCount,
  modifySelfNote,
  removeFromNotesNotificationsList,
  removeSelectedNote,
  setSelectedNote,
} from "new-redux/actions/selfnotes.actions/selfnotes";
import MainService from "services/main.service";
import { EXTENSIONS_ARRAY } from "pages/tasks/helpers/calculateSum";
import {
  setChatInViewSPhereFromDrawer,
  setNbrChatInViewSPhere,
  setNewInteraction,
} from "new-redux/actions/vue360.actions/vue360";
import {
  setNotifRmc,
  setOPenVisioRmc,
  setVisioRmcElementId,
} from "new-redux/actions/rmc.actions";
import i18next from "i18next";
import { getFamilyNameById } from "pages/clients&users/FamilyRouting";
import { useMemo } from "react";
import { fetchProfile } from "pages/profile/services";
import { getElementSystemDetails } from "pages/clients&users/services/services";
import { setOpenTaskRoomDrawer } from "new-redux/actions/tasks.actions/handleTaskRoomDrawer";
import { closeDrawerChat } from "new-redux/actions/voip.actions/handleDrawerChat";
import { handleFamilyEvent } from "pages/clients&users/helpers/handleFamiliesEvents";
import { handleEventCallInProgress } from "pages/voip/helpers/handleEventCallInProgress";
import {
  addNote360,
  removeNote360,
  updateNote360,
} from "new-redux/actions/notes.actions/notes";
import { generateAxios } from "services/axiosInstance";
import {
  setDetailsOpenIntegration,
  setOpenModalTicketGlpi,
} from "../new-redux/actions/chat.actions/Input";
import {
  setMsgTask,
  setMsgTaskId,
} from "new-redux/actions/tasks.actions/handleTaskDrawer";
import { useQueryClient } from "@tanstack/react-query";

const displayAvatarOnNotification = (avatar, name = "", isBot = false) => {
  return (
    <AvatarChat
      className="mr-1 "
      size={32}
      height={8}
      width={8}
      hasImage={EXTENSIONS_ARRAY?.includes(avatar?.split(".")?.pop())}
      url={`${
        (isBot
          ? URL_ENV?.REACT_APP_OAUTH_CHAT_API +
            process.env.REACT_APP_SUFFIX_CHAT_GROUP_STORAGE
          : URL_ENV?.REACT_APP_BASE_URL +
            URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL) + avatar
      }`}
      name={getName(name, "avatar") ?? avatar}
    />
  );
};

const useEventMercure = () => {
   const queryClient = useQueryClient();
  const eventMercure = useSelector((state) => state.ChatRealTime.eventMercure);
  const { selectedViewInTask } = useSelector((state) => state?.TasksRealTime);
  const currentUser = useSelector((state) => state.chat.currentUser);
  const currentUserSphere = useSelector((state) => state.user.user);
  // const selectedSelfNote = useSelector(
  //   (state) => state.selfNotesReducer.selectedNote
  // );
  // const contact = useSelector((state) => state.contacts.contactHeaderInfo);

  const fileContact = useSelector(
    (state) => state.files.currentSelectedContact
  );

  // const selfNotes = useSelector((state) => state.selfNotesReducer.selfNotes);

  const { user } = useSelector((state) => state.user);
  const { invalidConfigMail } = useSelector((state) => state.menu);
  const logs = useSelector((state) => state.voip.logs);

  const { dataAccounts } = useSelector((state) => state.mailReducer);
  const usedAccount = useMemo(
    () => dataAccounts?.find((item) => item?.selected),
    [dataAccounts]
  );

  const call = useActionCall();
  const notificationMessage = useRef({});
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { t } = useTranslation("common");

  // controll notifcations
  /***
   * @param {string} type
   * @param {string} message
   * @param {string} placement
   * @param {number} duration
   * @param {ReactNode} icon
   * @param {number} maxCount
   * @param {string} key
   * @param {object} style
   
   * 
   */
  const controllNotification = async ({
    type = "",
    message = "",
    placement = "",
    duration = 7,
    icon = null,
    maxCount = 1,
    key = "key_notif",
    style = {},
  }) => {
    const currentUserLocal = await store.getState().chat.currentUser;

    if (currentUserLocal.online === "busy") return;
    toastNotification(
      type,
      message,
      placement,
      duration,
      icon,
      maxCount,
      key,
      style
    );
  };
  const getNotificationsVisio = async () =>
    await store.getState().visioList.notificationList;
  const getTotalTasksNotifications = async () =>
    await store.getState()?.TasksRealTime?.totalNotificationNumber;
  const getRemindersTask = async () =>
    await store.getState()?.TasksRealTime?.remindersList;
  const getTotalVisioNotifications = async () =>
    await store.getState()?.visioList?.notificationCount;
  const getRemindersVisio = async () =>
    await store.getState()?.visioList?.remindersListVisio;
  const getCountRemindersVisio = async () =>
    await store.getState()?.visioList?.countReminders;
  const getChatInViewSphere = async () =>
    await store.getState()?.vue360?.chatInViewSphere;
  const getChatInViewSphereFromDrawer = async () =>
    await store.getState()?.vue360?.chatInViewSphereFromDrawer;
  const getContactInfo = async () =>
    await store.getState()?.contacts?.contactHeaderInfo;
  const getContactInfoFromDrawer = async () =>
    await store.getState()?.vue360?.contactInfoFromDrawer;
  const isOpenChatInViewSphere = async () =>
    await store.getState()?.vue360?.openChatInViewSphere;
  const getActiveTabViewSphere = async () =>
    await store.getState()?.vue360?.activeTab360;

  // Add avatars on notification (for tasks notifications).
  const getEventsVisio = async (message) => {
    const notifs = await getTotalVisioNotifications();
    const notificationList = await getNotificationsVisio();
    const remindersListVisio = await getRemindersVisio();
    const countRemindersVisio = await getCountRemindersVisio();

    if (message && message?.tasks_type_id === 3) {
      if (message?.type_event === "new_task") {
        dispatch(setCountNotificationVisio(notifs + 1));
        dispatch(
          setNotificationList([
            {
              id_data: message?.task_id,
              user: message?.initiator?.label,
              action: message?.action,
              avatar: message?.initiator?.avatar,
              // read: 0,
              created_at: moment_timezone().format("YYYY-MM-DD HH:mm"),
            },
            ...notificationList,
          ])
        );
        dispatch(
          createVisioMercure(
            {
              data: {
                ...message,
                data: {
                  ...message.data,
                  // read: 0,
                  action: message.action,
                },
              },
            },
            t
          )
        );
      } else if (
        message?.type_event === "update_task" ||
        message?.type_event === "update_priority_task" ||
        message?.type_event === "update_stage_task" ||
        message?.type_event === "update_note_task" ||
        message?.type_event === "update_dates_task"
      ) {
        dispatch(setCountNotificationVisio(notifs + 1));
        dispatch(
          setNotificationList([
            {
              id_data: message?.task_id,
              user: message?.initiator?.label,
              action: message?.action,
              avatar: message?.initiator?.avatar,
              // read: 0,
              created_at: moment_timezone().format("YYYY-MM-DD HH:mm"),
            },
            ...notificationList,
          ])
        );
        dispatch(
          updateVisioMercure({
            data: {
              ...message,
              data: {
                ...message.data,
                // read: 0,
                action: message.action,
              },
            },
            t,
            lastStartDate: message?.old_start_date,
          })
        );
      } else if (message?.type_event === "delete_task") {
        // dispatch(setCountNotificationVisio(notifs + 1));
        // dispatch(
        //   setNotificationList([
        //     {
        //       id_data: message?.task_id,
        //       user: message?.initiator?.label,
        //       action: message?.action,
        //       avatar: message?.initiator?.avatar,
        //       // read: 0,
        //       created_at: moment_timezone().format("YYYY-MM-DD HH:mm"),
        //     },
        //     ...notificationList,
        //   ])
        // );
        dispatch(
          deleteVisioMercure({
            data: {
              ...message,
              data: {
                ...message.data,
                // read: 0,
                action: message.action,
              },
            },
            t,
          })
        );
        // dispatch(onChangeTabVisio({ value: tabKey, keyMeet: "", t }));
      } else if (message?.type_event === "update_priority_task") {
      } else if (message?.type_event === "task_reminder") {
        dispatch(setCountReminders(countRemindersVisio + 1));

        dispatch(
          setRemindersList({
            ...remindersListVisio,
            meta: remindersListVisio.meta,
            data: [
              {
                taskId: message?.task_id,
                action: message?.message,
                label: message?.task_label,
                currentDate: message?.current_date_time,
              },
              ...remindersListVisio.data,
            ],
          })
        );
      } else if (message?.type_event === "update_dates_task") {
      } else if (message?.type_event === "update_stage_task") {
      } else if (message?.type_event === "update_note_task") {
      }

      let timer = setTimeout(() => {
        dispatch(setIsUserNotified(false));
        dispatch(setNotificationDescription(null));
        dispatch(setNotificationPayload(null));
        dispatch(setTaskNotificationAction(null));
        clearTimeout(timer);
      }, 100);
      return () => clearTimeout(timer);
    }
  };

  const handleNavigateModule = (type, payload, notifKey = null) => {
    switch (type) {
      case "tasks":
        navigate("/tasks");
        localStorage.setItem("redirect-activity-id", payload);
        notification.destroy(notifKey);
        break;
      case "mailing":
        document
          .getElementById("toast-new-email-btn")
          ?.setAttribute("disabled", "true");
        console.log({ payload });

        navigate(`/mailing/${payload.id}/inbox/${payload.idEmail}`);
        notification.destroy("toast-new-mail");
        const time = setTimeout(() => {
          UpdateSelectedEmail(dispatch, user, payload?.account_id);
          clearTimeout(time);
        }, 10);
        break;

      case "companies":
        navigate(`/companies/${payload}`);
        break;
      case "notes":
        // if (payload != "note") {
        //   console.log("payload", payload);
        navigate("/notes", { state: { id: payload } });
        // } else {
        //   navigate(`/notes`);
        // }

        break;
      default:
        break;
    }
  };

  // event listeners
  useEffect(() => {
    const manageEventMercure = async (messageEvent) => {
      const message = JSON.parse(messageEvent.data);
      const openChatInViewSphere = await isOpenChatInViewSphere();
      const chatInViewSphere = await getChatInViewSphere();
      const chatInViewSphereFromDrawer = await getChatInViewSphereFromDrawer();
      const contactInfo = await getContactInfo();
      const contactInfoFromDrawer = await getContactInfoFromDrawer();
      const currentUserLocal = await store.getState().chat.currentUser;

      const getAccessDisussion = async (contactInfo) => {
        try {
          const { data: element } = await getElementSystemDetails(
            contactInfo?.id
          );

          dispatch({
            type: contactInfoFromDrawer?.id
              ? SET_CONTACT_INFO_FROM_DRAWER
              : "SET_CONTACT_HEADER_INFO",
            payload: {
              ...contactInfo,
              access_discussion: element.access_discussion,
            },
          });
        } catch (err) {}
      };
       if (message?.type_event === "drive_item_shared") {
        queryClient.invalidateQueries(["drive-tree"], { exact: false });
        queryClient.invalidateQueries([["drive-items"]], { exact: false });
          controllNotification({
                    type: "success",
                    message: (
                      <div className="flex w-full flex-col pl-2">
                        <p
                          className="mt-0.5"
                          dangerouslySetInnerHTML={{
                            __html: t("toasts.driveItemShared", {
                              user: getName(message?.shared_by?.name, "name"),
                              itemName: message?.item?.name,
                              itemType: message?.item?.type === "file" ? t("drive.file") : t("drive.folder"),
                            }),
                          }}
                        ></p>
                        <Button
                          type="link"
                          className="ml-auto w-full"
                          onClick={() => {
                            navigate(`/drive?id=${message?.item?.id}`);
                            notification.destroy(message?.type_event);
                          }}
                        >
                          {t("common:chat.goto")}
                          <RightCircleOutlined
                            style={{
                              fontSize: "1rem",
                            }}
                          />
                        </Button>
                      </div>
                    ),
                    placement: "topRight",
                    duration: 5,
                    icon: displayAvatarOnNotification(
                      message?.shared_by?.avatar,
                      message?.shared_by?.name
                    ),
                    maxCount: 3,
                    key: message?.type_event,
                    style: {
                      padding: "20px 24px 20px 24px",
                      margin: "10px 0px",
                    },
                  });
                } else if (message?.type_event === "drive_item_unshared") {
                  queryClient.invalidateQueries(["drive-tree"], { exact: false });
                  queryClient.invalidateQueries([["drive-items"]], { exact: false });
                  controllNotification({
                    type: "info",
                    message: (
                      <div className="flex w-full flex-col pl-2">
                        <p
                          className="mt-0.5"
                          dangerouslySetInnerHTML={{
                            __html: t("toasts.driveItemUnshared", {
                              user: getName(message?.unshared_by?.name, "name"),
                              itemName: message?.item?.name,
                              itemType: message?.item?.type === "file" ? t("drive.file") : t("drive.folder"),
                            }),
                          }}
                        ></p>
                        <Button
                          type="link"
                          className="ml-auto w-full"
                          onClick={() => {
                            navigate(`/drive`);
                            notification.destroy(message?.type_event);
                          }}
                        >
                          {t("common:chat.goto")}
                          <RightCircleOutlined
                            style={{
                              fontSize: "1rem",
                            }}
                          />
                        </Button>
                      </div>
                    ),
                    placement: "topRight",
                    duration: 5,
                    icon: displayAvatarOnNotification(
                      message?.unshared_by?.avatar,
                      message?.unshared_by?.name
                    ),
                    maxCount: 3,
                    key: message?.type_event,
                    style: {
                      padding: "20px 24px 20px 24px",
                      margin: "10px 0px",
                    },
                                     });
                 } else if (message?.type_event === "drive_item_trashed") {
                  queryClient.invalidateQueries(["drive-tree"], { exact: false });
                  queryClient.invalidateQueries([["drive-items"]], { exact: false });
                   controllNotification({
                     type: "warning",
                     message: (
                       <div className="flex w-full flex-col pl-2">
                         <p
                           className="mt-0.5"
                           dangerouslySetInnerHTML={{
                             __html: t("toasts.driveItemTrashed", {
                               user: getName(message?.trashed_by?.name, "name"),
                               itemName: message?.item?.name,
                               itemType: message?.item?.type === "file" ? t("drive.file") : t("drive.folder"),
                             }),
                           }}
                         ></p>
                         <Button
                           type="link"
                           className="ml-auto w-full"
                           onClick={() => {
                             navigate("/drive");
                             notification.destroy(message?.type_event);
                           }}
                         >
                           {t("common:chat.goto")}
                           <RightCircleOutlined
                             style={{
                               fontSize: "1rem",
                             }}
                           />
                         </Button>
                       </div>
                     ),
                     placement: "topRight",
                     duration: 5,
                     icon: displayAvatarOnNotification(
                       message?.trashed_by?.avatar,
                       message?.trashed_by?.name
                     ),
                     maxCount: 3,
                     key: message?.type_event,
                     style: {
                       padding: "20px 24px 20px 24px",
                       margin: "10px 0px",
                     },
                   });
                 } else if (message?.type_event === "drive_item_moved") {
                  queryClient.invalidateQueries(["drive-tree"], { exact: false });
                  queryClient.invalidateQueries([["drive-items"]], { exact: false });
                   controllNotification({
                     type: "info",
                     message: (
                       <div className="flex w-full flex-col pl-2">
                         <p
                           className="mt-0.5"
                           dangerouslySetInnerHTML={{
                             __html: t("toasts.driveItemMoved", {
                               user: getName(message?.moved_by?.name, "name"),
                               itemName: message?.item?.name,
                               itemType: message?.item?.type === "file" ? t("drive.file") : t("drive.folder"),
                             }),
                           }}
                         ></p>
                         <Button
                           type="link"
                           className="ml-auto w-full"
                           onClick={() => {
                             navigate(`/drive`);
                             notification.destroy(message?.type_event);
                           }}
                         >
                           {t("common:chat.goto")}
                           <RightCircleOutlined
                             style={{
                               fontSize: "1rem",
                             }}
                           />
                         </Button>
                       </div>
                     ),
                     placement: "topRight",
                     duration: 5,
                     icon: displayAvatarOnNotification(
                       message?.moved_by?.avatar,
                       message?.moved_by?.name
                     ),
                     maxCount: 3,
                     key: message?.type_event,
                     style: {
                       padding: "20px 24px 20px 24px",
                       margin: "10px 0px",
                     },
                   });
                 } else if (message?.type_event === "drive_item_renamed") {
                  queryClient.invalidateQueries(["drive-tree"], { exact: false });
                  queryClient.invalidateQueries([["drive-items"]], { exact: false });
                   controllNotification({
                     type: "success",
                     message: (
                       <div className="flex w-full flex-col pl-2">
                         <p
                           className="mt-0.5"
                           dangerouslySetInnerHTML={{
                             __html: t("toasts.driveItemRenamed", {
                               user: getName(message?.renamed_by?.name, "name"),
                               itemName: message?.item?.name,
                               itemType: message?.item?.type === "file" ? t("drive.file") : t("drive.folder"),
                             }),
                           }}
                         ></p>
                         <Button
                           type="link"
                           className="ml-auto w-full"
                           onClick={() => {
                             navigate(`/drive`);
                             notification.destroy(message?.type_event);
                           }}
                         >
                           {t("common:chat.goto")}
                           <RightCircleOutlined
                             style={{
                               fontSize: "1rem",
                             }}
                           />
                         </Button>
                       </div>
                     ),
                     placement: "topRight",
                     duration: 5,
                     icon: displayAvatarOnNotification(
                       message?.renamed_by?.avatar,
                       message?.renamed_by?.name
                     ),
                     maxCount: 3,
                     key: message?.type_event,
                     style: {
                       padding: "20px 24px 20px 24px",
                       margin: "10px 0px",
                     },
                   });
                 }
        if (message.type_event === "message_rmc") {
        dispatch(setNotifRmc(true));
      }
      if (contactInfo?.id === message?.element_id) {
        if (
          message.type_event.includes("update_family_element") &&
          message?.user?._id !== currentUserSphere.id
        ) {
          dispatch(setNewInteraction({ type: "updateElement" }));
        } else if (
          message.type_event.includes("update_stage_family_element") &&
          message?.user?._id !== currentUserSphere.id
        ) {
          dispatch(setNewInteraction({ type: "updateStage" }));
        }
        // else if (
        //   message.type_event.includes("update_family_element") &&
        //   activeTabViewSphere === 3
        // ) {
        //   dispatch(setNewInteraction({ type: "updateElement" }));
        // }
        else if (
          message.type_event.includes("associate_family_element") &&
          message?.user?._id !== currentUserSphere.id
        ) {
          dispatch(setNewInteraction({ type: "associateElement" }));
          if (
            (message.family_id === 4 &&
              currentUserSphere.id === message?.data?.new_user) ||
            currentUserSphere.id === contactInfo?.owner?.id
          ) {
            getAccessDisussion(contactInfo);
          } else if (
            (message.family_id === 4 &&
              currentUserSphere.id === message?.data?.old_user) ||
            currentUserSphere.id === contactInfo?.owner?.id
          ) {
            getAccessDisussion(contactInfo);

            if (openChatInViewSphere)
              Modal.warning({
                title: i18next.t(
                  "common:chat.message_system.removed_auth_user_modal"
                ),
              });
          }
        }
      }
      if (contactInfoFromDrawer?.id === message?.element_id) {
        if (
          message.type_event.includes("update_family_element") &&
          message?.user?._id !== currentUserSphere.id
        ) {
          dispatch(setNewInteraction({ type: "updateElementFromDrawer" }));
        } else if (
          message.type_event.includes("update_stage_family_element") &&
          message?.user?._id !== currentUserSphere.id
        ) {
          dispatch(setNewInteraction({ type: "updateStageFromDrawer" }));
        }
        // else if (
        //   message.type_event.includes("update_family_element") &&
        //   activeTabViewSphere === 3
        // ) {
        //   dispatch(setNewInteraction({ type: "updateElement" }));
        // }
        else if (
          message.type_event.includes("associate_family_element") &&
          message?.user?._id !== currentUserSphere.id
        ) {
          dispatch(setNewInteraction({ type: "associateElementFromDrawer" }));
          if (
            (message.family_id === 4 &&
              currentUserSphere.id === message?.data?.new_user) ||
            contactInfoFromDrawer?.owner?.id === currentUserSphere.id
          ) {
            getAccessDisussion(contactInfoFromDrawer);
          } else if (
            message.family_id === 4 &&
            currentUserSphere.id === message?.data?.old_user
          ) {
            getAccessDisussion(contactInfoFromDrawer);
            if (openChatInViewSphere)
              Modal.warning({
                title: i18next.t(
                  "common:chat.message_system.removed_auth_user_modal"
                ),
              });
          }
        }
      }
      if (message.type_event === "is_connected_cron") {
        const typingUsers = await store.getState().ChatRealTime.typingUsers;

        typingUsers.forEach((element) => {
          if (
            message.data[element.user_id] === undefined ||
            Math.floor(new Date().getTime() / 1000) - element.date > 10
          )
            dispatch((dispatch) => {
              import("../new-redux/actions/chat.actions/Input")
                .then((module) =>
                  dispatch(
                    module.setTypingUser({
                      user_id: message.user_id,
                      typing: "delete",
                    })
                  )
                )
                .catch(() => {
                  return;
                });
            });
        });

        if (message.data[currentUserLocal?.uuid]) {
          dispatch({
            type: UPDATE_CURRENT_USER_STATUS_PRESENCE,
            payload: message.data[currentUserLocal?.uuid],
          });
        }
        dispatch((dispatch) => {
          import("../new-redux/actions/chat.actions/realTime")
            .then((module) => dispatch(module.setOnlineUser(message.data)))
            .catch(() => {
              return;
            });
        });
      } else if (message.type_event === "integration-external-rmc") {
        if (
          Array.isArray(currentUserLocal?.integrations) &&
          currentUserLocal?.integrations.length > 0
        ) {
          const item = currentUserLocal?.integrations.find(
            (el) => el.app_name === "GLPI"
          );
          dispatch(setOpenModalTicketGlpi(true));
          dispatch(setDetailsOpenIntegration({ ...item, fromRmc: true }));

          dispatch(setMsgTask(message?.message));
        }
      } else if (message?.type_event === "family_drawer") {
        switch (message?.family_id) {
          case "Task":
            dispatch({ type: "RMC_OPEN_CREATE_TASK" });
            break;
          case "Call":
            if (message?.row === "null") {
              controllNotification({
                type: "error",
                message: t("voip.numPhoneNotFoundRmc"),
                placement: "topRight",
                duration: 6,
              });
            } else {
              const [number, date] = message.row.split(",");
              // call(message?.row);
              actionCallFromRmc(number, date, call);
            }
            break;
          case "Visio":
            dispatch(setVisioRmcElementId(message?.row));
            let timer = setTimeout(() => {
              dispatch(setOPenVisioRmc(true));
              return () => clearTimeout(timer);
            }, 500);
            break;
          default:
            dispatch({
              type: "RMC_OPEN_CREATE_FORM",
              payload: {
                family_id: Number(message?.family_id),
                data: message?.row,
              },
            });
            break;
        }
        // if (message?.family_id === "Task")
        //   dispatch({ type: "RMC_OPEN_CREATE_TASK" });
        // else if (message?.family_id === "Call") {
        //   if (message?.row === "null") {
        //     controllNotification({
        //       type: "error",
        //       message: t("voip.numPhoneNotFoundRmc"),
        //       placement: "topRight",
        //       duration: 6,
        //     });
        //   } else {
        //     const [number, date] = message.row.split(",");
        //     // call(message?.row);
        //     actionCallFromRmc(number, date, call);
        //   }
        // } else if (message?.family_id === "Visio") {
        //   dispatch(setVisioRmcElementId(message?.row));
        //   let timer = setTimeout(() => {
        //     dispatch(setOPenVisioRmc(true));
        //     return () => clearTimeout(timer);
        //   }, 500);
        // } else {
        //   dispatch({
        //     type: "RMC_OPEN_CREATE_FORM",
        //     payload: {
        //       family_id: Number(message?.family_id),
        //       data: message?.row,
        //     },
        //   });
        // }
      } else if (message?.type_event === "queue_group_tag_affectation") {
        dispatch({
          type: SET_NEW_TAG_GROUPS_QUEUES,
          payload: message?.data,
        });
        // forwarding call in case not answer
      } else if (message?.type_event === "voice_mail") {
        dispatch(getLogs());
        dispatch({
          type: SET_NEW_VOICE_MESSAGING,
          payload: {
            data: message?.data,
            nbr: message?.data?.length,
          },
        });
        // dispatch(getVoiceFromMissedCall(logs, message?.data));
      } else if (message?.type_event === "forwarding_call") {
        // in case there are internal forward from the receiver him self
        // console.log({ message });
        dispatch({
          type: SET_FORWARDING_CALL,
          payload: { ...message?.data, exactPayload: true },
        });
        // forwarding call after answer
      } else if (message?.type_event === "blind_forwarding_call") {
        // action fired bye the API
        // console.log({ message });
        dispatch({
          type: SET_FORWARDING_CALL,
          payload: {
            exactPayload: false,
            dst: message.data.src,
            dst_forwarding: [
              {
                image: message.data.dst_forwarding_image,

                name: message.data.dst_forwarding_name,
                num: parseInt(message.data.dst_forwarding),
              },
            ],
          },
        });
      } else if (message?.type_event === "new_call") {
        dispatch(setNewCallLog(message.data, logs));
        window.location.pathname.includes("dashboard") &&
          dispatch(getStatsCall(message.data));
      } else if (message?.type_event === "missed_call") {
        dispatch({
          type: SET_NEW_MISSED_CALL,
          payload: {
            // data: message?.data,
            nbr: message?.data?.length,
          },
        });
      } else if (message?.type_event === "first_call") {
        let timer = setTimeout(() => {
          dispatch({ type: SET_CALL_FIRST_TIME, payload: message?.data });
          // dispatch(setCallFirstTime(message?.data, logs));
          return () => clearTimeout(timer);
        }, 500);
      } else if (message?.type_event === "returned_call") {
        let timer = setTimeout(() => {
          dispatch(getLogs(false, Math.max(logs.length, 50)));
          clearTimeout(timer);
        }, 50);
      } else if (message?.type_event === "queue_group_returned_call") {
        dispatch({
          type: SET_RETURNED_QUEUE_MISSED_CALL,
          payload: true,
        });
      } else if (
        message?.type_event === "queue_group" ||
        message?.type_event === "missed_call_queue_group"
      ) {
        dispatch({
          type: SET_NEW_LOG_GROUPS_QUEUES,
          payload: message?.data,
        });
        window.location.pathname.includes("dashboard") &&
          dispatch(setQueueInDashboard());

        if (message?.type_event === "missed_call_queue_group")
          dispatch({
            type: SET_NEW_MISSED_QUEUE_GROUP,
            payload: { nbr: 1 },
          });
        // } else if (message?.type_event === "missed_call_queue_group") {
        //   dispatch({
        //     type: SET_NEW_LOG_GROUPS_QUEUES,
        //     payload: message?.data,
        //   });
        //   dispatch({
        //     type: SET_NEW_MISSED_QUEUE_GROUP,
        //     payload: { nbr: 1 },
        //   });
      } else if (message?.type_event === "in_progress_call") {
        dispatch(handleEventCallInProgress(message.data, user.id, t));
      } else if (
        message?.type_event === "start_conference" ||
        message?.type_event === "end_conference"
      ) {
        const members =
          Array.isArray(message.data) &&
          message.data.length &&
          JSON.parse(message.data);

        switch (message.type_event) {
          case "start_conference":
            members?.length &&
              dispatch({
                type: SET_CONF_INFO,
                payload: members.find((members) => members.id !== user.id),
              });
            break;
          case "end_conference":
            dispatch({
              type: RESET_CONF_INFO,
            });
            break;
          default:
            break;
        }
      } else if (message?.type_event === "user_role_updated") {
        dispatch({
          type: "SET_USER_INFOS",
          payload: {
            ...user,
            role: message.data,
          },
        });
      }
      // user table
      else if (
        message?.type_event === "users_unblocked" ||
        message?.type_event === "users_blocked" ||
        message?.type_event === "send_invitation" ||
        message?.type_event === "user_accept_invitation"
      ) {
        const url = new URL(window.location.href);
        url?.pathname === "/settings/users"
          ? dispatch({
              type: "SET_USER_EVENT",
              payload: message,
            })
          : dispatch(handleEventMercure(null, message, t));
      } else if (
        message?.type_event === "update_family_element" ||
        message?.type_event === "create_family_element" ||
        message?.type_event === "delete_family_element"
      ) {
        dispatch(handleFamilyEvent(message, user.id, t, navigate));
      } else if (message?.type_event?.split("_").includes("task")) {
        getEventsVisio(message);
        // if (window.location.pathname === "/dashboard") {
        //   dispatch(getStatsTasks());
        //   dispatch(setTasksInDashboard());
        // }

        dispatch(setIsUserNotified(true));
        if (window?.location?.pathname !== "/tasks") {
          dispatch(setNewIncomingTaskNotification(true));
        }

        const notifs = await getTotalTasksNotifications();
        if (message?.type_event === "new_task") {
          controllNotification({
            type: "success",
            message: (
              <div className="flex w-full flex-col   pl-2 ">
                <p
                  className=" mt-0.5 "
                  dangerouslySetInnerHTML={{
                    __html: t("toasts.addTaskNotification", {
                      user: getName(message?.initiator?.label, "name"),
                      role: assignTranslatedRole(message?.role),
                      label: message?.data?.label,
                    }),
                  }}
                ></p>{" "}
                <Button
                  type="link"
                  className=" ml-auto  w-full "
                  onClick={() =>
                    handleNavigateModule(
                      "tasks",
                      message?.task_id,
                      message?.type_event
                    )
                  }
                >
                  {t("common:chat.goto")}
                  <RightCircleOutlined
                    style={{
                      fontSize: "1rem",
                    }}
                  />
                </Button>
              </div>
            ),
            placement: "topRight",
            duration: 7,
            icon: displayAvatarOnNotification(
              message?.initiator?.avatar,
              message?.initiator?.label
            ),
            maxCount: 3,
            key: message?.type_event,
            style: {
              padding: "20px  24px 20px 24px",
              margin: "10px 0px",
            },
          });
          dispatch(setTaskNotificationAction("create"));
          dispatch(setNotificationPayload(message?.data));
          dispatch(
            setNotificationDescription({
              initiator: message?.initiator,
              action: message?.action,
              id: message?.task_id,
              logId: message?.log_id,
            })
          );
          dispatch(setTotalNotificationsNumber(notifs + 1));
        } else if (message?.type_event === "update_task") {
          controllNotification({
            type: "success",
            message: (
              <div className="flex w-full flex-col   pl-2 ">
                <p
                  className=" mt-0.5 "
                  dangerouslySetInnerHTML={
                    message?.role && message?.role !== "removed"
                      ? {
                          __html: t("toasts.updateRoleTaskNotification", {
                            user: getName(message?.initiator?.label, "name"),
                            role: assignTranslatedRole(message?.role),
                            label: message?.label,
                          }),
                        }
                      : {
                          __html: t("toasts.updateTaskNotification", {
                            user: getName(message?.initiator?.label, "name"),
                            label: message?.label,
                          }),
                        }
                  }
                ></p>{" "}
                <Button
                  type="link"
                  className=" ml-auto  w-full "
                  onClick={() =>
                    handleNavigateModule(
                      "tasks",
                      message?.task_id,
                      message?.type_event
                    )
                  }
                >
                  {t("common:chat.goto")}
                  <RightCircleOutlined
                    style={{
                      fontSize: "1rem",
                    }}
                  />
                </Button>
              </div>
            ),
            placement: "topRight",
            duration: 7,
            icon: displayAvatarOnNotification(
              message?.initiator?.avatar,
              message?.initiator?.label
            ),
            maxCount: 3,
            key: message?.type_event,
            style: {
              padding: "20px  24px 20px 24px",
              margin: "10px 0px",
            },
          });
          dispatch(
            setTaskNotificationAction(
              message?.role === "removed" ? "update_task" : "delete_task"
            )
          );
          dispatch(setNotificationPayload(message?.data));
          dispatch(
            setNotificationDescription(
              message?.role !== "removed"
                ? {
                    initiator: message?.initiator,
                    action: [message?.action, message?.label],
                    id: message?.task_id,
                    logId: message?.log_id,
                  }
                : { action: "removed" }
            )
          );
          dispatch(
            setTotalNotificationsNumber(
              message?.role !== "removed" ? notifs + 1 : notifs
            )
          );
        } else if (message?.type_event === "delete_task") {
          // controllNotification({
          //   type: "success",
          //   message: t("toasts.deleteTaskNotification", {
          //     user: getName(message?.initiator?.label, "name"),
          //   }),
          //   placement: "topRight",
          //   duration: 7,
          //   icon: displayAvatarOnNotification(message?.initiator?.avatar),
          // });
          dispatch(setTaskNotificationAction("delete_task"));
          // dispatch(setNotificationPayload(message?.tasks_id));
          // dispatch(
          //   setNotificationDescription({
          //     initiator: message?.initiator,
          //     action: message?.action,
          //     id: message?.task_id,
          //     logId: message?.log_id,
          //   })
          // );
          dispatch(setTotalNotificationsNumber(notifs + 1));
        } else if (message?.type_event === "update_priority_task") {
          controllNotification({
            type: "success",
            message: (
              <div className="flex w-full flex-col   pl-2 ">
                <p
                  className=" mt-0.5 "
                  dangerouslySetInnerHTML={{
                    __html: t("toasts.updateTaskpriorityNotification", {
                      user: getName(message?.initiator?.label, "name"),
                      label: message?.data?.label,
                    }),
                  }}
                ></p>{" "}
                <Button
                  type="link"
                  className=" ml-auto  w-full "
                  onClick={() =>
                    handleNavigateModule(
                      "tasks",
                      message?.task_id,
                      message?.type_event
                    )
                  }
                >
                  {t("common:chat.goto")}
                  <RightCircleOutlined
                    style={{
                      fontSize: "1rem",
                    }}
                  />
                </Button>
              </div>
            ),
            placement: "topRight",
            duration: 7,
            icon: displayAvatarOnNotification(
              message?.initiator?.avatar,
              message?.initiator?.label
            ),
            maxCount: 3,
            key: message?.type_event,
            style: {
              padding: "20px  24px 20px 24px",
              margin: "10px 0px",
            },
          });
          dispatch(setTaskNotificationAction("update_task"));
          dispatch(setNotificationPayload(message?.data));
          dispatch(
            setNotificationDescription({
              initiator: message?.initiator,
              action: message?.action,
              id: message?.task_id,
              logId: message?.log_id,
            })
          );
          dispatch(setTotalNotificationsNumber(notifs + 1));
        } else if (message?.type_event === "task_reminder") {
          const remindersList = await getRemindersTask();
          dispatch(
            setRemindersArray({
              ...remindersList,
              meta: {
                ...remindersList.meta,
                total: remindersList?.meta?.total + 1,
              },
              data: [
                {
                  taskId: message?.task_id,
                  action: message?.message,
                  label: message?.task_label,
                  currentDate: message?.current_date_time,
                  id: message?.task_id,
                },
                ...remindersList?.data,
              ],
            })
          );
          if (message?.task_type === 3) {
            const remindersListVisio = await getRemindersVisio();
            const countRemindersVisio = await getCountRemindersVisio();
            dispatch(setCountReminders(countRemindersVisio + 1));

            dispatch(
              setRemindersList({
                ...remindersListVisio,
                meta: remindersListVisio.meta,
                data: [
                  {
                    taskId: message?.task_id,
                    action: message?.message,
                    label: message?.task_label,
                    currentDate: message?.current_date_time,
                    id: message?.task_id,
                  },
                  ...remindersListVisio.data,
                ],
              })
            );
          }
          controllNotification({
            type: "success",
            message: (
              <div className="flex w-full flex-col   pl-2 ">
                <p
                  className=" mt-0.5 "
                  dangerouslySetInnerHTML={{
                    __html: t("tasks.mercureReminder", {
                      taskLabel: message?.message[0],
                      time: message?.message[1],
                    }),
                  }}
                ></p>{" "}
                <Button
                  type="link"
                  className=" ml-auto  w-full "
                  onClick={() =>
                    handleNavigateModule(
                      "tasks",
                      message?.task_id,
                      message?.type_event
                    )
                  }
                >
                  {t("common:chat.goto")}
                  <RightCircleOutlined
                    style={{
                      fontSize: "1rem",
                    }}
                  />
                </Button>
              </div>
            ),
            placement: "topRight",
            duration: 0,
            icon: (
              <MdOutlineQueryBuilder className="animate-spin text-red-500" />
            ),
            maxCount: 3,
            key: message?.type_event,
            style: {
              padding: "20px  24px 20px 24px",
              margin: "10px 0px",
            },
          });
          dispatch(setTaskNotificationAction("reminder"));
          dispatch(
            setNotificationDescription({
              logId: message?.log_id,
              taskId: message?.task_id,
              action: message?.message,
              label: message?.task_label,
              currentDate: message?.current_date_time,
            })
          );
          checkShowNotification({
            type: "task",
            conversation: null,
            navigate: () => {
              navigate("/tasks/" + message?.task_id);
            },
            notificationContent: {
              sender: {
                image: "abc",
                name: message?.task_label,
                // number: s.remoteIdentity?.uri?.user,
              },
              message,
            },
          });
        } else if (message?.type_event === "update_dates_task") {
          controllNotification({
            type: "success",
            message: (
              <div className="flex w-full flex-col   pl-2 ">
                <p
                  className=" mt-0.5 "
                  dangerouslySetInnerHTML={{
                    __html: t("toasts.updateTaskDatesNotification", {
                      user: getName(message?.initiator?.label, "name"),
                      label: message?.data?.label,
                    }),
                  }}
                ></p>{" "}
                <Button
                  type="link"
                  className=" ml-auto  w-full "
                  onClick={() =>
                    handleNavigateModule(
                      "tasks",
                      message?.task_id,
                      message?.type_event
                    )
                  }
                >
                  {t("common:chat.goto")}
                  <RightCircleOutlined
                    style={{
                      fontSize: "1rem",
                    }}
                  />
                </Button>
              </div>
            ),
            placement: "topRight",
            duration: 7,
            icon: displayAvatarOnNotification(
              message?.initiator?.avatar,
              message?.initiator?.label
            ),
            maxCount: 3,
            key: message?.type_event,
            style: {
              padding: "20px  24px 20px 24px",
              margin: "10px 0px",
            },
          });
          dispatch(setTaskNotificationAction("update_task"));
          dispatch(setNotificationPayload(message?.data));
          dispatch(
            setNotificationDescription({
              initiator: message?.initiator,
              action: message?.action,
              id: message?.task_id,
              logId: message?.log_id,
            })
          );
          dispatch(setTotalNotificationsNumber(notifs + 1));
        }
        else if (message?.type_event === "update_stage_task") {
          controllNotification({
            type: "success",
            message: (
              <div className="flex w-full flex-col   pl-2 ">
                <p
                  className=" mt-0.5 "
                  dangerouslySetInnerHTML={{
                    __html: t("toasts.updateTaskStageNotification", {
                      user: getName(message?.initiator?.label, "name"),
                      label: message?.data?.label,
                    }),
                  }}
                ></p>{" "}
                <Button
                  type="link"
                  className=" ml-auto  w-full "
                  onClick={() =>
                    handleNavigateModule(
                      "tasks",
                      message?.task_id,
                      message?.type_event
                    )
                  }
                >
                  {t("common:chat.goto")}
                  <RightCircleOutlined
                    style={{
                      fontSize: "1rem",
                    }}
                  />
                </Button>
              </div>
            ),
            placement: "topRight",
            duration: 7,
            icon: displayAvatarOnNotification(
              message?.initiator?.avatar,
              message?.initiator?.label
            ),
            maxCount: 3,
            key: message?.type_event,
            style: {
              padding: "20px  24px 20px 24px",
              margin: "10px 0px",
            },
          });
          dispatch(
            setTaskNotificationAction(
              selectedViewInTask === "Kanban"
                ? "update_task_stage"
                : "update_task"
            )
          );
          dispatch(setNotificationPayload(message?.data));
          dispatch(
            setNotificationDescription({
              initiator: message?.initiator,
              action: message?.action,
              id: message?.task_id,
              logId: message?.log_id,
            })
          );
          dispatch(setTotalNotificationsNumber(notifs + 1));
        } else if (message?.type_event === "update_note_description_task") {
          controllNotification({
            type: "success",
            message: (
              <div className="flex w-full flex-col   pl-2 ">
                <p
                  className=" mt-0.5 "
                  dangerouslySetInnerHTML={{
                    __html: t("toasts.updateTaskNoteNotification", {
                      user: getName(message?.initiator?.label, "name"),
                      noteDescription:
                        message?.type === "description"
                          ? "description"
                          : "note",
                      label: message?.label,
                    }),
                  }}
                ></p>{" "}
                <Button
                  type="link"
                  className=" ml-auto  w-full "
                  onClick={() =>
                    handleNavigateModule(
                      "tasks",
                      message?.task_id,
                      message?.type_event
                    )
                  }
                >
                  {t("common:chat.goto")}
                  <RightCircleOutlined
                    style={{
                      fontSize: "1rem",
                    }}
                  />
                </Button>
              </div>
            ),
            placement: "topRight",
            duration: 7,
            icon: displayAvatarOnNotification(
              message?.initiator?.avatar,
              message?.initiator?.label
            ),
            maxCount: 1,
            key: message?.type_event,
            style: {
              padding: "20px  24px 20px 24px",
              margin: "10px 0px",
            },
          });
          dispatch(setTaskNotificationAction("update_task"));
          dispatch(setNotificationPayload(message?.data));
          dispatch(
            setNotificationDescription({
              initiator: message?.initiator,
              action: message?.action,
              id: message?.task_id,
              logId: message?.log_id,
            })
          );
          dispatch(setTotalNotificationsNumber(notifs + 1));
        } else if (message?.type_event === "task_reminder_start") {
          if (message?.task_type === 3) {
            const remindersListVisio = await getRemindersVisio();
            const countRemindersVisio = await getCountRemindersVisio();

            dispatch(
              setRemindersList({
                ...remindersListVisio,
                meta: remindersListVisio.meta,
                data: [
                  {
                    action: [
                      "Reminder",
                      message?.task_label,
                      dayjs(message?.current_date_time).format(
                        user.location.date_format +
                          " " +
                          user.location.time_format
                      ),
                      0,
                    ],
                    created_at: dayjs(message?.current_date_time).format(
                      user.location.date_format +
                        " " +
                        user.location.time_format
                    ),
                    read: 0,
                    user: "System",
                    // taskId: message?.task_id,
                    // action: message?.message,
                    // label: message?.task_label,
                    // currentDate: message?.current_date_time,
                  },
                  ...remindersListVisio.data,
                ],
              })
            );
            dispatch(setCountReminders(countRemindersVisio + 1));
          } else {
            const remindersList = await getRemindersTask();
            dispatch(
              setRemindersArray({
                ...remindersList,
                meta: {
                  ...remindersList.meta,
                  total: remindersList?.meta?.total + 1,
                },
                data: [
                  {
                    action: [
                      "Reminder",
                      message?.task_label,
                      dayjs(message?.current_date_time).format(
                        user.location.date_format +
                          " " +
                          user.location.time_format
                      ),
                      0,
                    ],
                    created_at: dayjs(message?.current_date_time).format(
                      user.location.date_format +
                        " " +
                        user.location.time_format
                    ),
                    read: 0,
                    user: "System",
                    // taskId: message?.task_id,
                    // action: message?.message,
                    // label: message?.task_label,
                    // currentDate: message?.current_date_time,
                  },
                  ...remindersList?.data,
                ],
              })
            );
          }
          controllNotification({
            type: "success",
            message: (
              <div className="flex w-full flex-col   pl-2 ">
                <p
                  className=" mt-0.5 "
                  dangerouslySetInnerHTML={{
                    __html: t("tasks.activityStartsNow", {
                      activityLabel: message?.task_label,
                    }),
                  }}
                ></p>{" "}
                <Button
                  type="link"
                  className=" ml-auto  w-full "
                  onClick={() => {
                    if (message?.task_type === 3) {
                      dispatch(
                        getTokenRoom({
                          room: message?.room_name,
                          errorText1: t("toasts.errorFetchApi"),
                          errorText2: t("toasts.errorRoomNotFound"),
                        })
                      );
                    } else {
                      handleNavigateModule(
                        "tasks",
                        message?.task_id,
                        message?.type_event
                      );
                    }
                  }}
                >
                  {message?.task_type === 3 ? (
                    <>
                      {t("visio.participateMeet")}
                      <RightCircleOutlined
                        style={{
                          fontSize: "1rem",
                        }}
                      />
                    </>
                  ) : (
                    <>
                      {t("common:chat.goto")}
                      <RightCircleOutlined
                        style={{
                          fontSize: "1rem",
                        }}
                      />
                    </>
                  )}
                </Button>
              </div>
            ),
            placement: "topRight",
            duration: 0,
            icon: <MdOutlineTimer className="animate-bounce text-red-500" />,
            maxCount: 3,
            key: message?.type_event,
            style: {
              padding: "20px  24px 20px 24px",
              margin: "10px 0px",
            },
          });
          checkShowNotification({
            type: "task",
            conversation: null,
            navigate: () => {
              navigate("/tasks/" + message?.task_id);
            },
            notificationContent: {
              sender: {
                image: "abc",
                name: message?.task_label,
                // number: s.remoteIdentity?.uri?.user,
              },
              message,
            },
          });
        } else if (message?.type_event === "task_reminder_end") {
          if (message?.task_type === 3) {
            const remindersListVisio = await getRemindersVisio();
            const countRemindersVisio = await getCountRemindersVisio();

            dispatch(
              setRemindersList({
                ...remindersListVisio,
                meta: remindersListVisio.meta,
                data: [
                  {
                    action: [
                      "Reminder",
                      message?.task_label,
                      dayjs(message?.current_date_time).format(
                        user.location.date_format +
                          " " +
                          user.location.time_format
                      ),
                      0,
                    ],
                    created_at: dayjs(message?.current_date_time).format(
                      user.location.date_format +
                        " " +
                        user.location.time_format
                    ),
                    read: 0,
                    user: "System",
                    // taskId: message?.task_id,
                    // action: message?.message,
                    // label: message?.task_label,
                    // currentDate: message?.current_date_time,
                  },
                  ...remindersListVisio.data,
                ],
              })
            );
            dispatch(setCountReminders(countRemindersVisio + 1));
          } else {
            const remindersList = await getRemindersTask();
            dispatch(
              setRemindersArray({
                ...remindersList,
                meta: {
                  ...remindersList.meta,
                  total: remindersList?.meta?.total + 1,
                },
                data: [
                  {
                    action: [
                      "Reminder",
                      message?.task_label,
                      dayjs(message?.current_date_time).format(
                        user.location.date_format +
                          " " +
                          user.location.time_format
                      ),
                      0,
                    ],
                    created_at: dayjs(message?.current_date_time).format(
                      user.location.date_format +
                        " " +
                        user.location.time_format
                    ),
                    read: 0,
                    user: "System",
                    // taskId: message?.task_id,
                    // action: message?.message,
                    // label: message?.task_label,
                    // currentDate: message?.current_date_time,
                  },
                  ...remindersList?.data,
                ],
              })
            );
          }

          controllNotification({
            type: "success",
            message: (
              <div className="flex w-full flex-col   pl-2 ">
                <p
                  className=" mt-0.5 "
                  dangerouslySetInnerHTML={{
                    __html: t("tasks.activityEndsNow", {
                      activityLabel: message?.task_label,
                      endTime: dayjs().to(
                        dayjs(`${message?.message[2]} ${message?.message[3]}`)
                      ),
                    }),
                  }}
                ></p>{" "}
                <Button
                  type="link"
                  className=" ml-auto  w-full "
                  onClick={() => {
                    if (message?.task_type === 3) {
                      dispatch(
                        getTokenRoom({
                          room: message?.room_name,
                          errorText1: t("toasts.errorFetchApi"),
                          errorText2: t("toasts.errorRoomNotFound"),
                        })
                      );
                    } else {
                      handleNavigateModule(
                        "tasks",
                        message?.task_id,
                        message?.type_event
                      );
                    }
                  }}
                >
                  {message?.task_type === 3 ? (
                    <>
                      {t("visio.participateMeet")}
                      <RightCircleOutlined
                        style={{
                          fontSize: "1rem",
                        }}
                      />
                    </>
                  ) : (
                    <>
                      {t("common:chat.goto")}
                      <RightCircleOutlined
                        style={{
                          fontSize: "1rem",
                        }}
                      />
                    </>
                  )}
                </Button>
              </div>
            ),
            placement: "topRight",
            duration: 0,
            icon: <MdOutlineTimer className="animate-bounce text-red-500" />,
            maxCount: 3,
            key: message?.type_event,
            style: {
              padding: "20px  24px 20px 24px",
              margin: "10px 0px",
            },
          });
          checkShowNotification({
            type: "task",
            conversation: null,
            navigate: () => {
              navigate("/tasks/" + message?.task_id);
            },
            notificationContent: {
              sender: {
                image: "abc",
                name: message?.task_label,
                // number: s.remoteIdentity?.uri?.user,
              },
              message,
            },
          });
        } else if (message?.type_event === "update_label_task") {
          controllNotification({
            type: "success",
            message: (
              <div className="flex w-full flex-col   pl-2 ">
                <p
                  className=" mt-0.5 "
                  dangerouslySetInnerHTML={{
                    __html: t("toasts.updateActivityLabel", {
                      user: getName(message?.initiator?.label, "name"),
                      newLabel: message?.data?.label,
                    }),
                  }}
                ></p>{" "}
                <Button
                  type="link"
                  className=" ml-auto  w-full "
                  onClick={() =>
                    handleNavigateModule(
                      "tasks",
                      message?.task_id,
                      message?.type_event
                    )
                  }
                >
                  {t("common:chat.goto")}
                  <RightCircleOutlined
                    style={{
                      fontSize: "1rem",
                    }}
                  />
                </Button>
              </div>
            ),
            placement: "topRight",
            duration: 7,
            icon: displayAvatarOnNotification(
              message?.initiator?.avatar,
              message?.initiator?.label
            ),
            maxCount: 3,
            key: message?.type_event,
            style: {
              padding: "20px  24px 20px 24px",
              margin: "10px 0px",
            },
          });
          dispatch(setTaskNotificationAction("update_task"));
          dispatch(setNotificationPayload(message?.data));
          dispatch(
            setNotificationDescription({
              initiator: message?.initiator,
              action: message?.action,
              id: message?.task_id,
              logId: message?.log_id,
            })
          );
          dispatch(setTotalNotificationsNumber(notifs + 1));
        } else if (message?.type_event === "update_type_task") {
          controllNotification({
            type: "success",
            message: (
              <div className="flex w-full flex-col   pl-2 ">
                <p
                  className=" mt-0.5 "
                  dangerouslySetInnerHTML={{
                    __html: t("toasts.updateActivityType", {
                      user: getName(message?.initiator?.label, "name"),
                      label: message?.label,
                      newType: message?.action[3],
                      oldType: message?.action[2],
                    }),
                  }}
                ></p>{" "}
                <Button
                  type="link"
                  className=" ml-auto  w-full "
                  onClick={() =>
                    handleNavigateModule(
                      "tasks",
                      message?.task_id,
                      message?.type_event
                    )
                  }
                >
                  {t("common:chat.goto")}
                  <RightCircleOutlined
                    style={{
                      fontSize: "1rem",
                    }}
                  />
                </Button>
              </div>
            ),
            placement: "topRight",
            duration: 7,
            icon: displayAvatarOnNotification(
              message?.initiator?.avatar,
              message?.initiator?.label
            ),
            maxCount: 3,
            key: message?.type_event,
            style: {
              padding: "20px  24px 20px 24px",
              margin: "10px 0px",
            },
          });
          dispatch(setTaskNotificationAction("update_task"));
          dispatch(setNotificationPayload(message?.data));
          dispatch(
            setNotificationDescription({
              initiator: message?.initiator,
              action: message?.action,
              id: message?.task_id,
              logId: message?.log_id,
            })
          );
          dispatch(setTotalNotificationsNumber(notifs + 1));
        } else if (message?.type_event === "update_family_element_task") {
          controllNotification({
            type: "success",
            message: (
              <div className="flex w-full flex-col pl-2">
                <p
                  className=" mt-0.5 "
                  dangerouslySetInnerHTML={{
                    __html: t("toasts.updateActivityElement", {
                      user: getName(message?.initiator?.label, "name"),
                      label: message?.label,
                      element: message?.action[3],
                      module: message?.action[2],
                    }),
                  }}
                ></p>{" "}
                <Button
                  type="link"
                  className=" ml-auto  w-full "
                  onClick={() =>
                    handleNavigateModule(
                      "tasks",
                      message?.task_id,
                      message?.type_event
                    )
                  }
                >
                  {t("common:chat.goto")}
                  <RightCircleOutlined
                    style={{
                      fontSize: "1rem",
                    }}
                  />
                </Button>
              </div>
            ),
            placement: "topRight",
            duration: 7,
            icon: displayAvatarOnNotification(
              message?.initiator?.avatar,
              message?.initiator?.label
            ),
            maxCount: 3,
            key: message?.type_event,
            style: {
              padding: "20px  24px 20px 24px",
              margin: "10px 0px",
            },
          });
          dispatch(setTaskNotificationAction("update_task"));
          dispatch(setNotificationPayload(message?.data));
          dispatch(
            setNotificationDescription({
              initiator: message?.initiator,
              action: message?.action,
              id: message?.task_id,
              logId: message?.log_id,
            })
          );
          dispatch(setTotalNotificationsNumber(notifs + 1));
        } else if (message?.type_event === "update_reminder_task") {
          controllNotification({
            type: "success",
            message: (
              <div className="flex w-full flex-col pl-2">
                <p
                  className=" mt-0.5 "
                  dangerouslySetInnerHTML={{
                    __html: t("toasts.updateReminder", {
                      user: getName(message?.initiator?.label, "name"),
                      label: message?.label,
                    }),
                  }}
                ></p>{" "}
                <Button
                  type="link"
                  className=" ml-auto  w-full "
                  onClick={() =>
                    handleNavigateModule(
                      "tasks",
                      message?.task_id,
                      message?.type_event
                    )
                  }
                >
                  {t("common:chat.goto")}
                  <RightCircleOutlined
                    style={{
                      fontSize: "1rem",
                    }}
                  />
                </Button>
              </div>
            ),
            placement: "topRight",
            duration: 7,
            icon: displayAvatarOnNotification(
              message?.initiator?.avatar,
              message?.initiator?.label
            ),
            maxCount: 3,
            key: message?.type_event,
            style: {
              padding: "20px  24px 20px 24px",
              margin: "10px 0px",
            },
          });
          dispatch(setTaskNotificationAction("update_task"));
          dispatch(setNotificationPayload(message?.data));
          dispatch(
            setNotificationDescription({
              initiator: message?.initiator,
              action: message?.action,
              id: message?.task_id,
              logId: message?.log_id,
            })
          );
          dispatch(setTotalNotificationsNumber(notifs + 1));
        } else if (message?.type_event === "update_files_task") {
          controllNotification({
            type: "success",
            message: (
              <div className="flex w-full flex-col pl-2">
                <p
                  className=" mt-0.5 "
                  dangerouslySetInnerHTML={{
                    __html: t("toasts.addFilesInActivity", {
                      user: getName(message?.initiator?.label, "name"),
                      label: message?.label,
                    }),
                  }}
                ></p>{" "}
                <Button
                  type="link"
                  className=" ml-auto  w-full "
                  onClick={() =>
                    handleNavigateModule(
                      "tasks",
                      message?.task_id,
                      message?.type_event
                    )
                  }
                >
                  {t("common:chat.goto")}
                  <RightCircleOutlined
                    style={{
                      fontSize: "1rem",
                    }}
                  />
                </Button>
              </div>
            ),
            placement: "topRight",
            duration: 7,
            icon: displayAvatarOnNotification(
              message?.initiator?.avatar,
              message?.initiator?.label
            ),
            maxCount: 3,
            key: message?.type_event,
            style: {
              padding: "20px  24px 20px 24px",
              margin: "10px 0px",
            },
          });
          dispatch(setTaskNotificationAction("update_task"));
          dispatch(setNotificationPayload(message?.data));
          dispatch(
            setNotificationDescription({
              initiator: message?.initiator,
              action: message?.action,
              id: message?.task_id,
              logId: message?.log_id,
            })
          );
          dispatch(setTotalNotificationsNumber(notifs + 1));
        } else if (
          message?.type_event === "update_todoList_task" ||
          message?.type_event === "add_new_todoList_task" ||
          message?.type_event === "delete_todoList_task"
        ) {
          controllNotification({
            type: "success",
            message: (
              <div className="flex w-full flex-col   pl-2 ">
                <p
                  className=" mt-0.5 "
                  dangerouslySetInnerHTML={{
                    __html: t(
                      message?.type_event === "add_new_todoList_task"
                        ? "toasts.createTaskListNotification"
                        : message?.type_event === "update_todoList_task"
                        ? "toasts.updateTaskListNotification"
                        : "toasts.deleteTaskListNotification",
                      {
                        user: getName(message?.user?.label_data, "name"),
                        labelList: message?.list_name,
                        label: message?.task_label,
                      }
                    ),
                  }}
                ></p>{" "}
                <Button
                  type="link"
                  className=" ml-auto  w-full "
                  onClick={() =>
                    handleNavigateModule(
                      "tasks",
                      message?.id,
                      message?.type_event
                    )
                  }
                >
                  {t("common:chat.goto")}
                  <RightCircleOutlined
                    style={{
                      fontSize: "1rem",
                    }}
                  />
                </Button>
              </div>
            ),
            placement: "topRight",
            duration: 7,
            icon: displayAvatarOnNotification(
              message?.user?.avatar,
              message?.user?.label_data
            ),
            maxCount: 3,
            key: message?.type_event,
            style: {
              padding: "20px  24px 20px 24px",
              margin: "10px 0px",
            },
          });
          dispatch(setTaskNotificationAction("update_task"));
          dispatch(setNotificationPayload(message?.data));
          dispatch(
            setNotificationDescription({
              initiator: message?.user,
              action: message?.action,
              id: message?.id,
              logId: message?.log_id,
            })
          );
          // dispatch(setTotalNotificationsNumber(notifs + 1));
        }
        let timer = setTimeout(() => {
          dispatch(setNotificationDescription(null));
          dispatch(setNotificationPayload(null));
          dispatch(setTaskNotificationAction(null));
          dispatch(setIsUserNotified(false));
          clearTimeout(timer);
        }, 100);
        return () => clearTimeout(timer);
      } else if (message?.type_event === "upload_family") {
        dispatch(setIsImportJobDone(true));
        controllNotification({
          type: "success",
          message: " import completed",
          placement: "topRight",
          duration: 7,
        });
      } else if (message?.type_event === "logout")
        dispatch(logoutOut(navigate));
      else if (message.type_event === "moderator_join") {
        if (message?.owner === user?.id) return;
        import("../utils/real-time-function/chat/function")
          .then((module) => module.getVisio(message))
          .catch(() => {});
        //   });
        // TODO FOR ANIS
        dispatch((dispatch) => {
          import("../new-redux/actions/chat.actions/realTime")
            .then((module) => dispatch(module.setOnlineUser(message.data)))
            .catch(() => {
              return;
            });
        });
      } else if (message?.type_event === "build_start") {
        dispatch(setEndBuild(false));

        toastNotification("success", t("build.start"), "topRight");
      } else if (message?.type_event === "build_end") {
        dispatch(setEndBuild(true));
        toastNotification("success", t("build.end"), "topRight");
      } else if (message?.type_event === "build_fail") {
        dispatch(setEndBuild(true));
        toastNotification("success", t("build.fail"), "topRight");
      }
      //
      //
      else if (message?.type_event === "new_email") {
        dispatch({
          type: UPDATE_NOTIFICATION_MAILING,
          payload: { account_id: message?.account_id, seen: 1 },
        });
        if (Number(message?.notification) === 1) {
          const displayMessage = (
            <div className="space-y-2">
              <span
                className="text-sm"
                dangerouslySetInnerHTML={{
                  __html: t("toasts.newEmail", {
                    from: message?.sender_address,
                    to: message?.email,
                  }),
                }}
              />
              <div className="flex justify-end">
                <Button
                  type="link"
                  icon={<RightCircleOutlined />}
                  onClick={() => {
                    handleNavigateModule("mailing", {
                      id: message?.id_account,
                      idEmail: message?.id_email,
                      account_id: message?.id_account,
                    });
                  }}
                >
                  {t("common:chat.goto")}
                </Button>
              </div>
            </div>
          );
          toastNotification(
            "open",
            displayMessage,
            "topRight",
            5,
            <CiMail className="h-6 w-6 text-[#2b73bf]" />,
            2,
            "new_email"
          );
        }
        if (usedAccount?.value === message?.id_account) {
          dispatch(setRefreshMailInbox(true));
        }
        const ids_labels = message?.ids_labels || [];
        dispatch(
          updateEmailsStats({
            id: message?.id_account,
            type: "add",
            typeEmail: message?.label.replace(/\\/g, "").toLowerCase(), //add back type new email: inbox or spam
            ids_labels,
          })
        );
      } else if (message.type_event === "Transfert-email") {
        if (usedAccount?.value === message.id_account) {
          dispatch(setRefreshMailInbox(true));
        }
        const details = message?.details;
        const displayMessage = (
          <div className="space-y-2">
            <span
              className="text-sm"
              dangerouslySetInnerHTML={{
                __html: t("mailing.moveToast", {
                  owner: details?.label_data_owner,
                  from: details?.email_source,
                  to: details?.email,
                  subject: truncateString(details?.subject, 50),
                }),
              }}
            />
            <div className="flex justify-end">
              <Button
                type="link"
                icon={<RightCircleOutlined />}
                onClick={() => {
                  handleNavigateModule("mailing", {
                    id: details?.account_id,
                    idEmail: details?.emailId,
                    account_id: details?.account_id,
                  });
                }}
              >
                {t("common:chat.goto")}
              </Button>
            </div>
          </div>
        );
        toastNotification(
          "open",
          displayMessage,
          "topRight",
          5,
          <CiMail className="h-6 w-6 text-[#2b73bf]" />,
          2,
          "Transfert-email"
        );
      } else if (message.type_event === "expire_Processing_Time") {
        dispatch(setRefreshNumberNotification());
        dispatch(setRefreshMailInbox(true));
        const data = message?.data || {};
        const displayMessage = (
          <div className="space-y-2">
            <span
              className="text-sm"
              dangerouslySetInnerHTML={{
                __html: t("mailing.expireProcessing", {
                  subject: truncateString(data?.subject, 50),
                  address: data?.address,
                  user: data?.user,
                  startDate: humanDate(
                    data?.expire_Processing_Time,
                    t,
                    "table"
                  ),
                }),
              }}
            />
            <div className="flex justify-end">
              <Button
                type="link"
                icon={<RightCircleOutlined />}
                onClick={() => {
                  handleNavigateModule("mailing", {
                    id: data?.account_id,
                    idEmail: data?.emailId,
                    account_id: data?.account_id,
                  });
                }}
              >
                {t("common:chat.goto")}
              </Button>
            </div>
          </div>
        );
        toastNotification(
          "open",
          displayMessage,
          "topRight",
          5,
          <CiMail className="h-6 w-6 text-[#2b73bf]" />,
          2,
          "expire_Processing_Time"
        );
      } else if (message.type_event === "number_emails") {
        dispatch(setHasNewMessage(+message.number_new_emails));

        // controllNotification({
        //   type: "success",
        //   message: "Vous avez un nouveau email",
        //   placement: "topRight",
        //   duration: 5,
        // });
      } else if (message.type_event === "assign-email") {
        if (usedAccount?.value === message.id_account) {
          dispatch(setRefreshMailInbox(true));
        }
        const data = message.details?.[0] || null;
        if (!data) return;
        const displayMessage = (
          <div className="space-y-2">
            <span
              className="text-sm"
              dangerouslySetInnerHTML={{
                __html: t(
                  data.user_id === data.owner
                    ? "mailing.selfAssignEmail"
                    : "mailing.assignEmail",
                  {
                    subject: truncateString(data?.subject, 50),
                    sender: data?.sender_address,
                    account: data?.address_account,
                    owner:
                      data?.owner === currentUserSphere?.id
                        ? t("chat.you")
                        : data?.label_data,
                    user: data?.user,
                  }
                ),
              }}
            />
            <div className="flex justify-end">
              <Button
                type="link"
                icon={<RightCircleOutlined />}
                onClick={() => {
                  handleNavigateModule("mailing", {
                    id: message?.id_account,
                    idEmail: data?.emailId,
                    account_id: message?.id_account,
                  });
                }}
              >
                {t("common:chat.goto")}
              </Button>
            </div>
          </div>
        );
        // console.log({ message });
        toastNotification(
          "open",
          displayMessage,
          "topRight",
          5,
          <CiMail className="h-6 w-6 text-[#2b73bf]" />,
          2,
          "assign_email"
        );
      } else if (message.type_event === "delete-assign-email") {
        if (usedAccount?.value === message.id_account) {
          dispatch(setRefreshMailInbox(true));
        }
        const displayMessage = (
          <div className="space-y-2">
            <span
              className="text-sm"
              dangerouslySetInnerHTML={{
                __html: t(
                  message.details === message.user
                    ? "mailing.selfDeleteAssignEmail"
                    : "mailing.deleteAssignEmail",
                  {
                    subject: truncateString(message?.subject, 50),
                    sender: message?.sender_address,
                    account: message?.address_account,
                    owner:
                      message?.details === currentUserSphere?.label
                        ? t("chat.you")
                        : message?.details,
                    user: message?.user,
                  }
                ),
              }}
            />
            <div className="flex justify-end">
              <Button
                type="link"
                icon={<RightCircleOutlined />}
                onClick={() => {
                  handleNavigateModule("mailing", {
                    id: message?.id_account,
                    idEmail: message?.emailId,
                    account_id: message?.id_account,
                  });
                }}
              >
                {t("common:chat.goto")}
              </Button>
            </div>
          </div>
        );
        // console.log({ message });
        toastNotification(
          "open",
          displayMessage,
          "topRight",
          5,
          <CiMail className="h-6 w-6 text-[#2b73bf]" />,
          2,
          "delete-assign-email"
        );
      } else if (message.type_event === "affectation-email") {
        if (usedAccount?.value === message.id_account) {
          dispatch(setRefreshMailInbox(true));
        }
        // console.log(message.type_event, { message });
        const data = message.details;
        const displayMessage = (
          <div className="space-y-2">
            <span
              className="text-sm"
              dangerouslySetInnerHTML={{
                __html: t(
                  data?.user === "61cf99800000000000000004"
                    ? "mailing.autoAffectEmail"
                    : "mailing.affectEmail",
                  {
                    subject: truncateString(data?.subject, 50),
                    sender: data?.sender_address,
                    account: data?.address_account,
                    family: getFamilyNameById(t, data?.family_id),
                    element: data?.element,
                    user: data?.user,
                  }
                ),
              }}
            />
            <div className="flex justify-end">
              <Button
                type="link"
                icon={<RightCircleOutlined />}
                onClick={() => {
                  handleNavigateModule("mailing", {
                    id: message?.id_account,
                    idEmail: message?.emailId,
                    account_id: message?.id_account,
                  });
                }}
              >
                {t("common:chat.goto")}
              </Button>
            </div>
          </div>
        );
        toastNotification(
          "open",
          displayMessage,
          "topRight",
          5,
          <CiMail className="h-6 w-6 text-[#2b73bf]" />,
          2,
          "affectation-email"
        );
      } else if (message.type_event === "delete-affectation-email") {
        if (usedAccount?.value === message.id_account) {
          dispatch(setRefreshMailInbox(true));
        }
        console.log(message.type_event, { message });
      } else if (
        message.type_event === "qualification-email" ||
        message.type_event === "update-qualification-email"
      ) {
        if (usedAccount?.value === message.id_account) {
          dispatch(setRefreshMailInbox(true));
        }
        // console.log(message.type_event, { message });
        const data = message.details || null;
        if (!data) return;
        const displayMessage = (
          <div className="space-y-2">
            <span
              className="text-sm"
              dangerouslySetInnerHTML={{
                __html: t("mailing.qualificationEmail", {
                  subject: truncateString(data?.subject, 50),
                  sender: data?.sender_address,
                  tags: data?.tags?.map((e) => e?.label).join(", "),
                  user: data?.user,
                }),
              }}
            />
            <div className="flex justify-end">
              {console.log({ message })}
              <Button
                type="link"
                icon={<RightCircleOutlined />}
                onClick={() => {
                  handleNavigateModule("mailing", {
                    id: message?.id_account,
                    idEmail: message?.details?.emailId,
                    account_id: message?.id_account,
                  });
                  notification.destroy("qualification-email");
                }}
              >
                {t("common:chat.goto")}
              </Button>
            </div>
          </div>
        );
        toastNotification(
          "open",
          displayMessage,
          "topRight",
          5,
          <CiMail className="h-6 w-6 text-[#2b73bf]" />,
          2,
          "qualification-email"
        );
      } else if (message.type_event === "delete-qualification-email") {
        if (usedAccount?.value === message.id_account) {
          dispatch(setRefreshMailInbox(true));
        }
        // console.log(message.type_event, { message });
        const data = message.details || null;
        if (!data) return;
        const displayMessage = (
          <div className="space-y-2">
            <span
              className="text-sm"
              dangerouslySetInnerHTML={{
                __html: t("mailing.deleteQualificationEmail", {
                  subject: truncateString(data?.subject, 50),
                  sender: data?.sender_address,
                  user: data?.user,
                }),
              }}
            />
            {/* <div className="flex justify-end">
              <Button
                type="link"
                icon={<RightCircleOutlined />}
                onClick={() => {
                  handleNavigateModule("mailing", {
                    id: message?.id_account,
                    idEmail: message?.emailId,
                    account_id: message?.id_account,
                  });
                }}
              >
                {t("common:chat.goto")}
              </Button>
            </div> */}
          </div>
        );
        toastNotification(
          "open",
          displayMessage,
          "topRight",
          5,
          <CiMail className="h-6 w-6 text-[#2b73bf]" />,
          2
        );
      } else if (message.type_event === "identification-email") {
        if (usedAccount?.value === message.id_account) {
          dispatch(setRefreshMailInbox(true));
        }
        // console.log(message.type_event, { message });
        const data = message.details || null;
        if (!data) return;
        const displayMessage = (
          <div className="space-y-2">
            <span
              className="text-sm"
              dangerouslySetInnerHTML={{
                __html: t("mailing.identificationEmail", {
                  subject: truncateString(data?.subject, 50),
                  sender: data?.sender_address,
                  family: getFamilyNameById(t, data?.family_id),
                  element: data?.label_data,
                  user: data?.user,
                }),
              }}
            />
            <div className="flex justify-end">
              <Button
                type="link"
                icon={<RightCircleOutlined />}
                onClick={() => {
                  handleNavigateModule("mailing", {
                    id: message?.id_account,
                    idEmail: message?.emailId,
                    account_id: message?.id_account,
                  });
                }}
              >
                {t("common:chat.goto")}
              </Button>
            </div>
          </div>
        );
        toastNotification(
          "open",
          displayMessage,
          "topRight",
          5,
          <CiMail className="h-6 w-6 text-[#2b73bf]" />,
          2,
          "identification-email"
        );
      } else if (message.type_event === "delete-identification-email") {
        if (usedAccount?.value === message.id_account) {
          dispatch(setRefreshMailInbox(true));
        }
        // console.log(message.type_event, { message });
        const data = message.details || null;
        if (!data) return;
        const displayMessage = (
          <div className="space-y-2">
            <span
              className="text-sm"
              dangerouslySetInnerHTML={{
                __html: t("mailing.deleteIdentificationEmail", {
                  subject: truncateString(message?.subject, 50),
                  sender: data?.sender_address,
                  family: getFamilyNameById(t, data?.family_id),
                  element: data?.label_data,
                  user: message?.user,
                }),
              }}
            />
            {/* <div className="flex justify-end">
              <Button
                type="link"
                icon={<RightCircleOutlined />}
                onClick={() => {
                  handleNavigateModule("mailing", {
                    id: message?.id_account,
                    idEmail: message?.emailId,
                    account_id: message?.id_account,
                  });
                }}
              >
                {t("common:chat.goto")}
              </Button>
            </div> */}
          </div>
        );
        toastNotification(
          "open",
          displayMessage,
          "topRight",
          5,
          <CiMail className="h-6 w-6 text-[#2b73bf]" />,
          2,
          "deleteIdentificationEmail"
        );
      } else if (message.type_event === "update-state-email") {
        if (usedAccount?.value === message.id_account) {
          dispatch(setRefreshMailInbox(true));
        }
        // console.log(message.type_event, { message });
        const data = message.details || null;
        if (!data) return;
        const stateName = (state) =>
          !state
            ? ""
            : state === "new"
            ? t("mailing.new")
            : state === "in-progress"
            ? t("mailing.inProgress")
            : state === "processed"
            ? t("mailing.processed")
            : t("mailing.closed");

        const displayMessage = (
          <div className="space-y-2">
            <span
              className="text-sm"
              dangerouslySetInnerHTML={{
                __html: t("mailing.updateStateEmail", {
                  subject: truncateString(message?.subject, 50),
                  sender: message?.sender_address,
                  lastState: stateName(data?.last_state),
                  newState: stateName(data?.new_state),
                  user: message?.owner,
                }),
              }}
            />
            <div className="flex justify-end">
              <Button
                type="link"
                icon={<RightCircleOutlined />}
                onClick={() => {
                  handleNavigateModule("mailing", {
                    id: message?.id_account,
                    idEmail: message?.emailId,
                    account_id: message?.id_account,
                  });
                }}
              >
                {t("common:chat.goto")}
              </Button>
            </div>
          </div>
        );
        toastNotification(
          "open",
          displayMessage,
          "topRight",
          5,
          <CiMail className="h-6 w-6 text-[#2b73bf]" />,
          2,
          "update-state-email"
        );
      } else if (message.type_event === "Synchronization_started") {
        if (usedAccount?.value === message.id_account) {
          dispatch(setSynchroType("start"));
        }
      } else if (message.type_event === "Synchronization_failed") {
        if (usedAccount?.value === message.id_account) {
          dispatch(setSynchroType("fail"));
          controllNotification({
            type: "error",
            message: t("mailing.synchroFailed"),
            placement: "topRight",
            duration: 5,
          });
        }
      } else if (message.type_event === "Synchronization_successful") {
        if (usedAccount?.value === message.id_account) {
          dispatch(setSynchroType("success"));
          controllNotification({
            type: "success",
            message: t("mailing.synchroSuccess"),
            placement: "topRight",
            duration: 5,
          });
        }
      }
      //
      //
      else if (message.type_event === "create_file") {
        if (user?.id != message.creator_id) {
          // console.log("itsss me");
          const el = await getContactInfo();
          if (message.element_id == el?.id) dispatch(addFile360(message));
          controllNotification({
            type: "success",
            message: (
              <div className="flex w-full flex-col   pl-2 ">
                <p
                  dangerouslySetInnerHTML={{
                    __html: t("contacts.createdFileEvent", {
                      creator_name: truncateString(message?.creator?.label, 15),
                      family_name: getFamilyNameById(t, message?.family_id),
                      element_name: truncateString(message?.element_label, 25),
                    }),
                  }}
                />
                {/* <p
                  className=" mt-0.5 "
                  dangerouslySetInnerHTML={{
                    __html: t("New file created", {
                      user: getName(message?.creator?.label, "name"),
                      label: "File created",
                    }),
                  }}
                ></p> */}{" "}
                <Button
                  type="link"
                  className=" ml-auto  w-full "
                  onClick={() =>
                    navigate(
                      generateUrlToView360(
                        message?.family_id,
                        message?.element_id,
                        "v2"
                      )
                    )
                  }
                >
                  {t("common:chat.goto")}
                  <RightCircleOutlined
                    style={{
                      fontSize: "1rem",
                    }}
                  />
                </Button>
              </div>
            ),
            placement: "topRight",
            duration: 5,
            icon: displayAvatarOnNotification(
              message?.creator?.avatar,
              message?.creator?.label
            ),
            maxCount: 3,
            key: "toast-new-message",
            style: {
              padding: "20px  24px 20px 24px",
              margin: "10px 0px",
            },
          });
        }

        //   message: (
        //     <div className="flex w-full flex-col   pl-2 ">
        //       <p
        //         className=" mt-0.5 "
        //         dangerouslySetInnerHTML={{
        //           __html: t("New file uploaded", {
        //             user: getName("samer_mansouri", "name"),
        //             label: message?.filename,
        //           }),
        //         }}
        //       ></p>{" "}
        //       {/* <Button
        //         type="link"
        //         className=" ml-auto  w-full "
        //         onClick={() =>
        //           handleNavigateModule("tasks", message?.task_id)
        //         }
        //       >
        //         {t("common:chat.goto")}
        //         <RightCircleOutlined
        //           style={{
        //             fontSize: "1rem",
        //           }}
        //         />
        //       </Button> */}
        //     </div>
        //   ),
        //   placement: "topRight",
        //   duration: 7,
        //   // icon: displayAvatarOnNotification(
        //   //   message?.initiator?.avatar,
        //   //   message?.initiator?.label
        //   // ),
        //   maxCount: 3,
        //   key: "toast-new-file",
        //   style: {
        //     padding: "20px  24px 20px 24px",
        //     margin: "10px 0px",
        //   },
        // });
      } else if (message.type_event === "note_create_view360") {
        if (user?.id != message?.data?.user) {
          console.log("Aint me");
          const el = await getContactInfo();
          if (message?.data?.element_id != el?.id || el?.id == null) {
            // dispatch(addFile360(message?.data));

            controllNotification({
              type: "success",
              message: (
                <div className="flex w-full flex-col   pl-2 ">
                  <p
                    dangerouslySetInnerHTML={{
                      __html: t("contacts.createdCommentEvent", {
                        creator_name: truncateString(
                          message?.data?.user_data?.label_data,
                          15
                        ),
                        family_name: getFamilyNameById(
                          t,
                          message?.data?.family_id
                        ),
                        element_name: truncateString(
                          message?.data?.label_element,
                          25
                        ),
                      }),
                    }}
                  />
                  {/* <p
                    className=" mt-0.5 "
                    dangerouslySetInnerHTML={{
                      __html: t("New file created", {
                        user: getName(message?.creator?.label, "name"),
                        label: "File created",
                      }),
                    }}
                  ></p> */}{" "}
                  <Button
                    type="link"
                    className=" ml-auto  w-full "
                    onClick={() =>
                      navigate(
                        generateUrlToView360(
                          message?.data?.family_id,
                          message?.data?.element_id,
                          "v2"
                        )
                      )
                    }
                  >
                    {t("common:chat.goto")}
                    <RightCircleOutlined
                      style={{
                        fontSize: "1rem",
                      }}
                    />
                  </Button>
                </div>
              ),
              placement: "topRight",
              duration: 5,
              icon: displayAvatarOnNotification(
                message?.data?.user_data?.avatar,
                message?.data?.user_data?.label_data
              ),
              maxCount: 3,
              key: "toast-new-message",
              style: {
                padding: "20px  24px 20px 24px",
                margin: "10px 0px",
              },
            });
          } else if (message?.data?.element_id == el?.id) {
            dispatch(
              addNote360({
                ...message?.data,
                permission: 0,
              })
            );
          }
        }
        console.log("note_create_view360", message);
      } else if (message.type_event === "note_update_view360") {
        console.log("note_update_view360", message);
        if (user?.id != message?.data?.user) {
          const el = await getContactInfo();

          if (message?.data?.element_id != el?.id || el?.id == null) {
            controllNotification({
              type: "success",
              message: (
                <div className="flex w-full flex-col   pl-2 ">
                  <p
                    dangerouslySetInnerHTML={{
                      __html: t("contacts.updatedCommentEvent", {
                        creator_name: truncateString(
                          message?.data?.user_data?.label_data,
                          15
                        ),
                        family_name: getFamilyNameById(
                          t,
                          message?.data?.family_id
                        ),
                        element_name: truncateString(
                          message?.data?.label_element,
                          25
                        ),
                      }),
                    }}
                  />
                  {/* <p
                    className=" mt-0.5 "
                    dangerouslySetInnerHTML={{
                      __html: t("New file created", {
                        user: getName(message?.creator?.label, "name"),
                        label: "File created",
                      }),
                    }}
                  ></p> */}{" "}
                  <Button
                    type="link"
                    className=" ml-auto  w-full "
                    onClick={() =>
                      navigate(
                        generateUrlToView360(
                          message?.data?.family_id,
                          message?.data?.element_id,
                          "v2"
                        )
                      )
                    }
                  >
                    {t("common:chat.goto")}
                    <RightCircleOutlined
                      style={{
                        fontSize: "1rem",
                      }}
                    />
                  </Button>
                </div>
              ),
              placement: "topRight",
              duration: 5,
              icon: displayAvatarOnNotification(
                message?.data?.user_data?.avatar,
                message?.data?.user_data?.label_data
              ),
              maxCount: 3,
              key: "toast-new-message",
              style: {
                padding: "20px  24px 20px 24px",
                margin: "10px 0px",
              },
            });
          } else if (message?.data?.element_id == el?.id) {
            // dispatch(addFile360(message?.data));
            dispatch(updateNote360(message?.data));
          }
        }
      } else if (message.type_event === "note_delete_view360") {
        if (user?.id != message?.data?.user) {
          const el = await getContactInfo();
          if (message?.data?.element_id == el?.id) {
            // dispatch(addFile360(message?.data));
            dispatch(removeNote360(message?.data?._id));
          }
        }
      }
      //
      // else if () {}
      else if (message.type_event === "update_file") {
        const el = await getContactInfo();
        if (message.element_id == el?.id) {
          dispatch(updateFileForMercure(message));
        }
      } else if (message.type_event === "update_label") {
        console.log("update_label", message);
        const el = await getContactInfo();
        if (message.element_id == el?.id) {
          dispatch(updateFileLabelForMercure(message));
        }
      } else if (message.type_event === "delete_file") {
        const el = await getContactInfo();
        if (message.element_id == el?.id) {
          dispatch(deleteFileForMercure(message));
        }
      } else if (message.type_event === "note_share") {
        //check if data._id is not in the list of notes
        //if not add it to the list of notes
        console.log("note_share", message);
        // let check = selfNotes?.find((note) => note?._id == message?.data?._id);
        const selfNotesList = await store.getState().selfNotesReducer.selfNotes;
        const check = selfNotesList?.find(
          (note) => note?._id == message?.data?._id
        );
        console.log("check", check);
        if (!check || check == undefined) {
          dispatch(addSelfNote(message.data));
        }

        if (check) {
          dispatch(modifySelfNote(message?.data));
        }

        if (window.location.pathname != "/notes" && !check) {
          dispatch(incrementNotesNotificationsCount());
          dispatch(addToNotesNotificationsList(message?.data?._id));
          controllNotification({
            type: "success",
            message: (
              <div className="flex w-full flex-col   pl-2 ">
                <p
                  className=" mt-0.5 "
                  // dangerouslySetInnerHTML={{
                  //   __html: t("Note shared with you", {
                  //     user: getName(message?.dataUser?.label_data, "name"),
                  //     label: "Shared a note with you",
                  //   }),
                  // }}
                >
                  {getName(message?.data?.dataUser?.label_data, "name")} has
                  shared a note with you
                </p>{" "}
                <Button
                  type="link"
                  className=" ml-auto  w-full "
                  onClick={() =>
                    handleNavigateModule("notes", message?.data?._id)
                  }
                >
                  {t("common:chat.goto")}
                  <RightCircleOutlined
                    style={{
                      fontSize: "1rem",
                    }}
                  />
                </Button>
              </div>
            ),
            placement: "topRight",
            duration: 7,
            icon: displayAvatarOnNotification(
              message?.data?.dataUser?.avatar?.path,
              message?.data?.dataUser?.label_data
            ),
            maxCount: 3,
            key: "toast-new-message",
            style: {
              padding: "20px  24px 20px 24px",
              margin: "10px 0px",
            },
          });
        }
      } else if (message.type_event === "note_share_modif") {
        dispatch(modifySelfNote(message?.data));

        const selN = await store.getState().selfNotesReducer.selectedNote;

        if (message?.data?._id == selN?._id) {
          dispatch(setSelectedNote(message?.data));
        }
      } else if (message.type_event === "note_association") {
        dispatch(modifySelfNote(message?.data));

        const selN = await store.getState().selfNotesReducer.selectedNote;

        if (message?.data?._id == selN?._id) {
          dispatch(setSelectedNote(message?.data));
        }
      } else if (message.type_event === "note_dessociation") {
        dispatch(modifySelfNote(message?.data));

        const selN = await store.getState().selfNotesReducer.selectedNote;

        if (message?.data?._id == selN?._id) {
          dispatch(setSelectedNote(message?.data));
        }
      } else if (message.type_event === "update_room_note") {
        dispatch(modifySelfNote(message?.data));

        const selN = await store.getState().selfNotesReducer.selectedNote;

        if (message?.data?._id == selN?._id) {
          dispatch(setSelectedNote(message?.data));
        }
      } else if (message.type_event === "note_update") {
        // console.log("note_update", message);
        dispatch(modifySelfNote(message?.data));

        const selN = await store.getState().selfNotesReducer.selectedNote;

        console.log(
          "currentSelectedSelfNote",
          selN,
          " upcoming id",
          message?.data
        );

        if (message?.data?._id == selN?._id) {
          dispatch(setSelectedNote(message?.data));
        }
      } else if (message.type_event === "note_lock") {
        console.log("note_lock", message);

        dispatch(modifySelfNote(message?.data));

        const selN = await store.getState().selfNotesReducer.selectedNote;

        if (message?.data?._id == selN?._id) {
          dispatch(setSelectedNote(message?.data));
        }
      } else if (message.type_event === "note_unlock") {
        dispatch(modifySelfNote(message?.data));

        const selN = await store.getState().selfNotesReducer.selectedNote;

        if (message?.data?._id == selN?._id) {
          dispatch(setSelectedNote(message?.data));
          //TODO: make this as a function and import it from a file
          MainService.lockNote(selN?._id)
            .then((res) => {
              console.log("lockNote", res);
              dispatch(modifySelfNote(res?.data?.data));
              dispatch(setSelectedNote(res?.data?.data));
            })
            .catch((err) => {
              console.log("lockNote", err);
            });
        }
      } else if (message.type_event === "note_unshare") {
        dispatch(deleteSelfNote(message?.data?._id));
        const selN = await store?.getState()?.selfNotesReducer?.selectedNote;
        if (message?.data?._id == selN?._id) {
          dispatch(removeSelectedNote());
        }

        const selectedConversationId = await store.getState().ChatRealTime
          .selectedConversation?.id;

        if (
          selectedConversationId === message?.data?.room_id &&
          selectedConversationId != null
        ) {
          dispatch(closeDrawerChat());
          dispatch(setOpenTaskRoomDrawer(false));
        }

        dispatch(decrementNotesNotificationsCount());
        dispatch(removeFromNotesNotificationsList(message?.data?._id));
      } else if (message.type_event === "note_delete") {
        console.log("note_delete", message);

        dispatch(deleteSelfNote(message?.data?._id));

        const selN = await store.getState()?.selfNotesReducer?.selectedNote;

        console.log(
          "selectedNote",
          selN?._id,
          "deletedNote",
          message?.data?._id
        );
        if (message?.data?._id == selN?._id) {
          console.log("its working");
          dispatch(removeSelectedNote());
        }

        const selectedConversationId = await store.getState().ChatRealTime
          .selectedConversation?.id;

        if (
          selectedConversationId === message?.data?.room_id &&
          selectedConversationId != null
        ) {
          dispatch(closeDrawerChat());
          dispatch(setOpenTaskRoomDrawer(false));
        }
      } else if (message.type_event === "update_label") {
        if (message.element_id == fileContact)
          dispatch(updateFileForMercure(message));
      } else if (message.tab_id !== getOrGenerateTabId()) {
        // here

        if (message.type_event?.includes("new_message")) {
          import("../utils/real-time-function/chat/function")
            .then((module) =>
              module.newMessage({
                message,
                type: message.type_event === "new_message" ? "user" : "room",
                notificationContent: notificationMessage.current,
                navigate,
              })
            )
            .catch(() => {
              return;
            });
        } else if (message.type_event === "forward_message_details") {
          import("../utils/real-time-function/chat/function")
            .then((module) => module.forwardMessageDetails(message))
            .catch(() => {
              return;
            });
        } else if (message.type_event === "create_ticket_chat") {
          updateMessages(
            message, //integrations
            "new_integration",
            message.message_id, //msgid

            message?.room_id ??
              (message?.sender_id === currentUser?._id
                ? message?.receiver_id
                : message?.sender_id),
            message?.room_id ? "room" : "user",

            null
          );
        } else if (message.type_event?.includes("typing_user")) {
          dispatch((dispatch) => {
            import("../new-redux/actions/chat.actions/Input")
              .then((module) =>
                dispatch(
                  module.setTypingUser({
                    user_id: message.user_id,
                    room_id: Object.keys(message).includes("room_id")
                      ? message.room_id
                      : null,
                    typing: message.typing,
                  })
                )
              )
              .catch(() => {
                return;
              });
          });
        } else if (message.type_event?.includes("make_message_read")) {
          if (message.room_id === chatInViewSphere?.id) {
            dispatch(
              setNbrChatInViewSPhere({
                id: "",
                number: 0,
                relation_id: "",
              })
            );
          }

          if (message.room_id === chatInViewSphereFromDrawer?.id) {
            dispatch(
              setChatInViewSPhereFromDrawer({
                id: "",
                number: 0,
                relation_id: "",
              })
            );
          }
          import("../utils/real-time-function/chat/function")
            .then((module) =>
              module.makeReadFunction(
                message,
                "event",
                message.type_event === "make_message_read" ? "user" : "room"
              )
            )
            .catch(() => {
              return;
            });
        } else if (message.type_event?.includes("react_message")) {
          import("../utils/real-time-function/chat/function")
            .then((module) => module.reactMessage(message))
            .catch(() => {
              return;
            });
        } else if (message.type_event?.includes("delete_message")) {
          import("../utils/real-time-function/chat/function")
            .then((module) => module.deleteMessage(message))

            .catch(() => {
              return;
            });
        } else if (message.type_event?.includes("important_message")) {
          import("../utils/real-time-function/chat/function")
            .then((module) => module.makeMessageImportant(message))
            .catch((e) => {
              console.log(e);
              return;
            });
        } else if (message.type_event?.includes("message_favorite")) {
          import("../utils/real-time-function/chat/function")
            .then((module) => module.makeMessageFavorite(message))
            .catch(() => {
              return;
            });
        } else if (message.type_event?.includes("update_message")) {
          import("../utils/real-time-function/chat/function")
            .then((module) =>
              module.editMessage(
                message,
                message.type_event?.includes("room") ? "room" : "user"
              )
            )
            .catch((e) => {
              console.log(e);
              return;
            });
        } else if (message.type_event === "update_room") {
          import("../utils/real-time-function/chat/function")
            .then((module) => module.updateRoom(message))
            .catch(() => {
              return;
            });
        } else if (
          message.type_event === "invite_user_room" ||
          message.type_event === "remove_user_room"
        ) {
          import("../utils/real-time-function/chat/function")
            .then((module) =>
              module.updateParticipants(
                message,
                message.type_event?.includes("invite") ? "add" : "remove"
              )
            )
            .catch(() => {
              return;
            });
        } else if (message.type_event === "leave_user_room") {
          import("../utils/real-time-function/chat/function")
            .then((module) =>
              module.updateParticipants(message, "leave_user_room")
            )
            .catch(() => {
              return;
            });
        } else if (message.type_event === "create_room") {
          dispatch((dispatch) => {
            import("../new-redux/actions/chat.actions")
              .then((module) =>
                dispatch(
                  module.AddGroupToMembersGroupChat({
                    ...message.room,
                    conversation_id: message.conversation_id,
                  })
                )
              )
              .catch(() => {
                return;
              });
          });
        } else if (message.type_event === "mute_conversation") {
          dispatch((dispatch) => {
            import("../new-redux/actions/chat.actions")
              .then(async (module) => {
                const selectedConversation = await store.getState().ChatRealTime
                  .selectedConversation;
                if (
                  selectedConversation?.conversationId ===
                  message.coversation_id
                )
                  dispatch(
                    updateChatSelectedConversation({
                      muted_status: true,
                    })
                  );
                dispatch(module.addToMuteList({ id: message.coversation_id }));
              })
              .catch(() => {
                return;
              });
          });
        } else if (message.type_event === "unmute_conversation") {
          dispatch((dispatch) => {
            import("../new-redux/actions/chat.actions")
              .then(async (module) => {
                const selectedConversation = await store.getState().ChatRealTime
                  .selectedConversation;
                if (
                  selectedConversation?.conversationId ===
                  message.coversation_id
                )
                  dispatch(
                    updateChatSelectedConversation({
                      muted_status: false,
                    })
                  );
                dispatch(
                  module.removeFromMuteList({ id: message.coversation_id })
                );
              })

              .catch(() => {
                return;
              });
          });
        } else if (message.type_event === "archive_room") {
          import("../utils/real-time-function/chat/function")
            .then((module) => module.archiveRoomFunction(message))
            .catch(() => {
              return;
            });
        } else if (message.type_event === "add_archive_conversation") {
          import("../utils/real-time-function/chat/function")
            .then((module) => module.archiveDiscussion(message))
            .catch(() => {
              return;
            });
        } else if (message.type_event === "remove_archive_conversation") {
          import("../utils/real-time-function/chat/function")
            .then((module) => module.unArchiveDiscussion(message))
            .catch(() => {
              return;
            });
        } else if (message.type_event === "new_user") {
          dispatch((dispatch) => {
            import("../new-redux/actions/chat.actions")
              .then((module) =>
                dispatch(
                  module.addChatUserToList({
                    type: message.user ? "one" : "many",
                    data: message.user ?? [...message.users],
                  })
                )
              )
              .catch(() => {
                return;
              });
          });
        } else if (message.type_event === "remove_user") {
          if (message.user_id === currentUser?._id)
            dispatch((dispatch) => {
              import("../new-redux/actions/chat.actions")
                .then((module) =>
                  dispatch(module.updateCurrentUserStatusBlocked())
                )
                .catch(() => {
                  return;
                });
            });

          dispatch((dispatch) => {
            import("../new-redux/actions/chat.actions")
              .then((module) =>
                dispatch(module.removeChatUserToList(message.user_id))
              )
              .catch(() => {
                return;
              });
          });
        } else if (message.type_event === "update_user") {
          await fetchProfile(dispatch, setUserInfos);
          import("../utils/real-time-function/chat/function")
            .then((module) => module.updateUserList(message.user))
            .catch(() => {
              return;
            });
        } else if (message.type_event?.includes("answer")) {
          if (message?.sender_id === currentUser?._id)
            dispatch((dispatch) => {
              import("../new-redux/actions/chat.actions")
                .then((module) =>
                  dispatch(
                    module.setPollsVote(Math.floor(Math.random() * 1000 + 1))
                  )
                )
                .catch(() => {
                  return;
                });
            });
          updateMessages(
            message?.poll,
            "add_vote_event",
            message.message_id,
            Object.keys(message).includes("room_id")
              ? message?.room_id
              : message?.sender_id === currentUser?._id
              ? message?.receiver_id
              : message?.sender_id,
            message?.room_id ? "room" : "user",
            message?.sender_id
          );
        } else if (message.type_event === "update_integration") {
          try {
            const res = await generateAxios(
              URL_ENV?.REACT_APP_OAUTH_CHAT_API +
                "/" +
                process.env.REACT_APP_SUFFIX_API
            ).get("/integrations");
            dispatch({
              type: SET_USER_INFO_CHAT,
              payload: {
                ...currentUser,
                integrations: res?.data?.data?.filter(
                  (el) => el.is_active === 1
                ),
              },
            });
          } catch (err) {}
        } else if (message.type_event === "error-config-email") {
          dispatch(
            setInavlidConfigMail([
              ...new Set([...invalidConfigMail, message?.id]),
            ])
          );
          toastNotification(
            "error",
            t("mailing.invalidConfig", {
              email: message?.email,
            }),
            "topRight"
          );
        } else if (message.type_event === "valid-config-email") {
          dispatch(
            setInavlidConfigMail(
              invalidConfigMail.filter((id) => id !== message?.id)
            )
          );
        }
        const searchChatSideBar = await store.getState().chat.searchChatSideBar;
        if (searchChatSideBar !== "") {
          setTimeout(() => {
            window.dispatchEvent(
              new CustomEvent(SET_NEW_UPDATE_ON_MESSAGE, {
                detail: {
                  conversation_id: message?.conversation_id,
                },
              })
            );
          }, 0);
        }
      }

      if (
        message?.type_event === "new_message_room" &&
        message.user_id !== currentUser?._id
      ) {
        if (message?.room?.relation_id !== null) {
          dispatch(setRelationId(message?.room?.relation_id));
          dispatch(setRelationType(message?.room?.relation_type));
        }
        if (
          message?.room?.relation_id === contactInfo?.id
          // &&
          // !openChatInViewSphere
        ) {
          dispatch(
            setNbrChatInViewSPhere({
              number: chatInViewSphere?.number + 1,
              id: message?.room?._id,
              relation_id: contactInfo?.id,
            })
          );
        }
        if (message?.room?.relation_id === contactInfoFromDrawer?.id) {
          dispatch(
            setChatInViewSPhereFromDrawer({
              number: chatInViewSphereFromDrawer?.number + 1,
              id: message?.room?._id,
              relation_id: contactInfo?.id,
            })
          );
        }
      }
    };
    if (eventMercure && window?.location?.pathname !== "/editor") {
      eventMercure.addEventListener("message", manageEventMercure);
    }
  
    return () => {
      if (eventMercure) {
        eventMercure.removeEventListener("message", manageEventMercure);
        dispatch(setIsImportJobDone(false));
        eventMercure.close();
      }
    };
  }, [dispatch, eventMercure]);

  return null;
};

export default useEventMercure;
