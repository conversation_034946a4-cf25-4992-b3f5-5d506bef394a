import { Button, List, Skeleton, Space } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { ChatBodyItem } from "../conversation/body/item";
import InputChat from "../conversation/input";
import {
  CloseOutlined,
  RollbackOutlined,
  CompressOutlined,
  ExpandOutlined,
} from "@ant-design/icons";
import { setOpenDrawer } from "new-redux/actions/chat.actions";
import { systemMessageTypes } from "../utils/ConversationUtils";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { moment_timezone } from "App";
const ChatDrawerThread = ({ source }) => {
  const { openDrawer, threadList, threadLoading } = useSelector(
    (state) => state.chat
  );

  const dispatch = useDispatch();
  const [t] = useTranslation("common");
  let prevMessage = null;
  const [isBig, setIsBig] = useState(true);
  const [position, setPosition] = useState("fixed");
  useEffect(() => {
    if (openDrawer?.type === "thread") {
      setIsBig(true);
      setPosition("fixed");
    }
  }, [openDrawer?.type]);
  const skl = () =>
    Array.from(
      { length: Math.floor(document.body.clientHeight / 3) },
      (_, i) => i + 1
    ).map((item) => (
      <div className="flex  items-center px-1" key={`sklt_${item}`}>
        <Skeleton
          avatar
          paragraph={{
            rows: Math.floor(
              (Math.random() + 1) *
                Array.from({ length: 3 }, (i) => i + 1).length
            ),
          }}
          active
        />
      </div>
    ));
  return (
    <>
      {openDrawer?.type === "thread" && (
        <motion.div
          animate={{
            width: isBig && source !== "external" ? 800 : 384,
            zIndex: 100,
            right: 0,

            position: source !== "external" ? position : "relative", //: "fixed", // isBig ? "fixed" : "relative",
          }}
          initial={{
            position: "relative",
          }}
          transition={{
            duration: isBig && source !== "external" ? 0.5 : 0.1,
          }}
          style={{
            height:
              position === "fixed" && source !== "external"
                ? "calc(100% - 57px)"
                : "100%",
          }}
          className={` 
          flex flex-col justify-between overflow-x-hidden  bg-slate-50   pb-1 pt-3 drop-shadow-2xl  `}
        >
          <div className="flex h-full flex-col overflow-hidden ">
            {/* header */}
            <div
              className=" flex items-center justify-between px-2  py-4"
              style={{
                boxShadow: "0 1px 2px 0 rgb(0 0 0 / 0.05)",
              }}
            >
              <div className="flex w-full items-center justify-between">
                <h2 className="m-0 text-lg font-bold text-gray-500">
                  <RollbackOutlined /> {t("chat.thread")}
                </h2>
                <Space>
                  {source !== "external" && (
                    <Button
                      onClick={() => {
                        let time;
                        setIsBig((p) => !p);

                        time = setTimeout(
                          () => {
                            setPosition((p) =>
                              p === "relative" ? "fixed" : "relative"
                            );
                            clearTimeout(time);
                          },
                          isBig ? 200 : 0
                        );
                      }}
                      type="text"
                      shape="circle"
                      size="small"
                      icon={!isBig ? <ExpandOutlined /> : <CompressOutlined />}
                    />
                  )}
                  <Button
                    onClick={() => {
                      dispatch(setOpenDrawer({ type: "" }));
                    }}
                    type="text"
                    shape="circle"
                    size="small"
                    icon={<CloseOutlined />}
                  />
                </Space>
              </div>
            </div>
            {/* list */}
            <div className="flex  flex-1 flex-col space-y-1  overflow-y-hidden">
              {threadLoading ? (
                skl()
              ) : (
                <List
                  size="large"
                  className="messagesList  flex  h-full w-full flex-col-reverse   overflow-y-auto "
                  dataSource={threadList}
                  rowKey={(item) => item?._id}
                  renderItem={(item, index) => {
                    const sameDay = prevMessage
                      ? moment_timezone(item?.created_at).isSame(
                          moment_timezone(prevMessage.created_at),
                          "day"
                        )
                      : false;

                    const isSameUser =
                      prevMessage &&
                      prevMessage._id !== item._id &&
                      prevMessage.type !== "message_from_bot" &&
                      item.type !== "message_from_bot" &&
                      prevMessage.sender_id === item.sender_id;

                    const isLessThanOneMinute =
                      prevMessage &&
                      prevMessage._id !== item._id &&
                      moment_timezone(item.created_at).diff(
                        moment_timezone(prevMessage.created_at),
                        "minutes"
                      ) < 1;
                    const isMessageSystem =
                      prevMessage &&
                      prevMessage._id !== item._id &&
                      systemMessageTypes.includes(prevMessage.type) ===
                        systemMessageTypes.includes(item.type);
                    const isFirstItem =
                      prevMessage && index === 1 ? false : true;
                    const hideAvatar = !prevMessage
                      ? false
                      : isSameUser &&
                        isLessThanOneMinute &&
                        isMessageSystem &&
                        isFirstItem;

                    prevMessage = item;

                    return (
                      <>
                        <div className="flex items-center">
                          <ChatBodyItem
                            source="reply"
                            scrollToBottom={() => {}}
                            hideAvatar={hideAvatar}
                            display_header_icon={false}
                            sameDay={sameDay}
                            item={item}
                            index={threadList?.length - 1 - index}
                            data={threadList}
                            tabopenDropDown={{}}
                            setTabOpenDropDown={() => {}}
                            from={true}
                            isReply={true}
                          />
                        </div>
                      </>
                    );
                  }}
                />
              )}
            </div>
          </div>
          <div className="pb-0">
            <InputChat
              from={true}
              from_big={source !== "external" ? isBig : false}
            />
          </div>
        </motion.div>
      )}
    </>
  );
};
export default ChatDrawerThread;
