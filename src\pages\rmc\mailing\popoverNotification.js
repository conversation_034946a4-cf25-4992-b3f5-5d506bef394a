import { moment_timezone } from "App";
import { <PERSON><PERSON>, <PERSON><PERSON>, Divider, Empty, Popover, Spin, Tooltip } from "antd";
import { AvatarChat } from "components/Chat";
import dayjs from "dayjs";
import { URL_ENV } from "index";
import { Bell } from "lucide-react";
import { getName } from "pages/layouts/chat/utils/ConversationUtils";
import React, { Fragment, useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { AiOutlineClockCircle } from "react-icons/ai";
import { GiStopwatch } from "react-icons/gi";
import { IoMdNotificationsOutline } from "react-icons/io";
import InfiniteScroll from "react-infinite-scroll-component";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import MainService from "services/main.service";

const PopoverNotification = ({
  popoverVisible,
  setPopoverVisible,
  usedAccount,
}) => {
  const [dataNotif, setDataNotif] = useState([]);
  const [dataNumberNotif, setDataNumberNotif] = useState("");
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState("");
  const [lastPage, setlastPage] = useState(1);
  const [total, setTotal] = useState(1);
  const { numberNotification } = useSelector((state) => state.mailReducer);
  const { user } = useSelector((state) => state.user);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const isToday = require("dayjs/plugin/isToday");

  dayjs.extend(isToday);

  const [t] = useTranslation("common");

  const handleVisibleChange = (visible) => {
    setPopoverVisible(visible);
  };

  const handleNextPage = async () => {
    setPage((prevPage) => prevPage + 1);
  };

  const getNotification = useCallback(async () => {
    setLoading(page === 1 ? "general" : "more");
    try {
      const response = await MainService.getNotificationEmail(page);
      if (response.status === 200) {
        setDataNotif((p) => [...p, ...response.data.data]);
        if (page === 1) {
          setDataNumberNotif(0);
          setlastPage(response.data.meta.last_page);
          setTotal(response.data.meta.total);
        }

        setLoading("");
      }
    } catch (error) {
      setLoading("");

      console.log(error);
    }
  }, [popoverVisible, page]);

  const getNumberNotification = useCallback(async () => {
    // if (numberNotification) return;

    try {
      const response = await MainService.getNumberNotificationEmail();
      if (response.status === 200) {
        setDataNumberNotif(response.data.expire_Processing_Time);
      }
    } catch (error) {
      console.log(error);
    }
  }, [numberNotification]);

  const readNotification = async (id) => {
    try {
      const response = await MainService.readNotificationEmail(id);
      if (response.status === 200) {
        // console.log("read", response.data.data);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const content = (
    <div className="h-[140px] p-1" id="scrollableDiv">
      <InfiniteScroll
        height={300}
        dataLength={dataNotif?.length}
        next={handleNextPage}
        hasMore={page < lastPage}
        loader={loading === "more" && <div>{t("chat.loading")}... </div>}
        scrollableTarget="scrollableDiv"
      >
        {dataNotif?.length === 0 ? (
          <div className="mt-[15%]">
            <Empty />
          </div>
        ) : (
          dataNotif?.map((item, index) => (
            <Fragment key={item._id}>
              <div
                onClick={() => {
                  readNotification(item._id);
                  navigate(
                    `/mailing/${usedAccount?.value}/inbox/${item?.data?.emailId}`
                  );
                }}
                className={
                  item.receivers?.find((el) => el.id === user?.id)?.read === 0
                    ? `rounded-  my-2 flex cursor-pointer items-center justify-center space-x-2 rounded-md bg-blue-50 p-2 hover:bg-blue-100`
                    : `my-2  flex cursor-pointer items-center  justify-center space-x-2 rounded-md p-2 hover:bg-gray-100`
                }
              >
                <Tooltip
                  title={item?.identification?.address}
                  placement="bottom"
                >
                  <>
                    <AvatarChat
                      hasImage={item.identification.avatar}
                      url={`${
                        URL_ENV?.REACT_APP_BASE_URL +
                        URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                      }${item?.identification.avatar}`}
                      size={36}
                      height={8}
                      fontSize={11}
                      width={8}
                      name={getName(item.user_name, "avatar")}
                    />
                  </>
                </Tooltip>
                <div className="flex  flex-1 flex-col space-y-1   ">
                  <p
                    className={` ${
                      item.receivers?.find((el) => el.id === user?.id)?.read ===
                      0
                        ? "text-xs font-semibold"
                        : "text-[12px]"
                    }   text-gray-500`}
                  >
                    {t("mailing.processEmail")}{" "}
                    <span className="font-semibold">{item.data.subject}</span>{" "}
                    {t("helpDesk.assignedTo")}{" "}
                    <span className="font-semibold">
                      {getName(item.user_name, "name")}
                    </span>{" "}
                    {t("mailing.depassed")}.
                    <p className="flex items-center space-x-0.5">
                      <GiStopwatch
                        className={`h-4 w-4 cursor-default text-[#ba1435]`}
                      />
                      <p className="mt-0.5 font-bold">
                        {dayjs(item.data.expire_Processing_Time).isToday()
                          ? moment_timezone(item.data.expire_Processing_Time)
                              .startOf("hour")
                              .fromNow()
                          : moment_timezone(item.data.expire_Processing_Time)
                              .startOf("day")
                              .fromNow()}
                      </p>
                    </p>
                  </p>
                </div>

                {item.receivers?.find((el) => el.id === user?.id)?.read ===
                  0 && <div className="h-2.5 w-2.5 rounded-full bg-blue-700" />}
              </div>

              {index < dataNotif.length - 1 ? (
                <Divider plain className="my-2 bg-gray-300" />
              ) : null}
            </Fragment>
          ))
        )}
      </InfiniteScroll>
    </div>
  );

  useEffect(() => {
    if (!popoverVisible) {
      setPage(1);
      setDataNotif([]);
    }
  }, [popoverVisible]);

  useEffect(() => {
    if (popoverVisible) getNotification();
  }, [getNotification]);

  useEffect(() => {
    getNumberNotification();
  }, [getNumberNotification]);

  return (
    <Popover
      key="popover-notification-mail"
      destroyTooltipOnHide
      arrow={false}
      title={
        <div className="sticky top-0 flex items-center  space-x-1">
          <AiOutlineClockCircle className=" h-4 w-4  cursor-pointer text-red-500" />

          <p className="text-xs ">{t("mailing.expiredEmail")}</p>
        </div>
      }
      placement="bottom"
      content={
        loading === "general" ? (
          <div className="flex justify-center py-14">
            <Spin />
          </div>
        ) : (
          content
        )
      }
      open={popoverVisible}
      onOpenChange={handleVisibleChange}
      trigger="click"
      overlayInnerStyle={{
        width: "400px",
        height: "350px",
      }}
    >
      {/* <Tooltip placement="topLeft" title="NOTIFICATION"> */}
      <Button
        type="text"
        shape="circle"
        icon={
          <Badge
            style={{
              borderRadius: "50%", // makes it rounded
              width: "20px", // adjust width and height to create a perfect circle
              height: "20px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
            count={dataNumberNotif}
          >
            {/* <IoMdNotificationsOutline
              style={{ fontSize: "20px", marginTop: "1px" }}
            /> */}
            <Bell size={18} className="mt-1 text-slate-600" />
          </Badge>
        }
      />

      {/* </Tooltip> */}
    </Popover>
  );
};

export default PopoverNotification;
