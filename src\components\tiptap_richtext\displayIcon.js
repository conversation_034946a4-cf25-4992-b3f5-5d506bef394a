import {
  MdOutlineFormatBold,
  MdOutlineFormatItalic,
  MdOutlineFormatListBulleted,
  MdOutlineFormatListNumbered,
  MdOutlineFormatSize,
  MdOutlineFormatUnderlined,
  MdOutlineRedo,
  MdOutlineStrikethroughS,
  MdOutlineUndo,
  MdFormatColorText,
  MdFormatColorFill,
  MdCode,
  MdOutlineCheckBox,
} from "react-icons/md";

export const displayEditorIcon = (iconType, fontSize, width, height) => {
  switch (iconType) {
    case "color":
      return <MdFormatColorText style={{ fontSize: fontSize }} />;
    case "highlight":
      return <MdFormatColorFill style={{ fontSize: fontSize }} />;
    case "bold":
      return <MdOutlineFormatBold style={{ fontSize: fontSize }} />;
    case "italic":
      return <MdOutlineFormatItalic style={{ fontSize: fontSize }} />;
    case "underline":
      return <MdOutlineFormatUnderlined style={{ fontSize: fontSize }} />;
    case "redo":
      return <MdOutlineRedo style={{ fontSize: fontSize }} />;
    case "undo":
      return <MdOutlineUndo style={{ fontSize: fontSize }} />;
    case "size":
      return <MdOutlineFormatSize style={{ fontSize: fontSize }} />;
    case "strike":
      return <MdOutlineStrikethroughS style={{ fontSize: fontSize }} />;
    case "unorderedList":
      return <MdOutlineFormatListBulleted style={{ fontSize: fontSize }} />;
    case "orderedList":
      return <MdOutlineFormatListNumbered style={{ fontSize: fontSize }} />;
    case "chevronDown":
      return (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="feather feather-chevron-down text-black"
        >
          <polyline points="6 9 12 15 18 9"></polyline>
        </svg>
      );
    case "code":
      return <MdCode style={{ fontSize: fontSize }} />;
    case "checkboxes_list":
      return <MdOutlineCheckBox style={{ fontSize: fontSize }} />;
    default:
      break;
  }
};
