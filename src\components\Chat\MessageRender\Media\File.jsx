import { DownloadOutlined, FileOutlined } from "@ant-design/icons";
import { <PERSON>lt<PERSON>, Button, Typography } from "antd";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import {
  getName,
  handleDownloadFile,
} from "../../../../pages/layouts/chat/utils/ConversationUtils";
import { moment_timezone } from "../../../../App";
import { URL_ENV } from "index";
import { HiOutlineCalendar } from "react-icons/hi2";
import AvatarChat from "components/Chat/Avatar/AvatarChat";
import OpenFileButton from "pages/onlyOffice/OpenFileButton";
import { useSelector } from "react-redux";
const { Link } = Typography;

const File = ({ file, index, GoToComponent, fromInfosTab = false }) => {
  const { t } = useTranslation("common");
  const [loadingDownload, setLoading] = useState(false);
  const currentUser = useSelector((state) => state.chat.currentUser);
  return (
    <div
      className={`
    flex ${
      GoToComponent ? "my-2.5" : ""
    } file  w-full  items-center justify-between
    `}
    >
      <div className="w-full">
        <div
          className={`flex w-full  flex-col`}
          style={{
            whiteSpace: "nowrap",
            overflow: "hidden",
            textOverflow: "ellipsis",
          }}
        >
          <div className="flex w-full flex-col space-y-2 text-sm  ">
            <div className="flex w-full items-center justify-between">
              <Link
                className="flex w-full items-center space-x-1"
                key={index}
                href={
                  URL_ENV?.REACT_APP_OAUTH_CHAT_API +
                  process.env.REACT_APP_SUFFIX_CHAT_GROUP_STORAGE +
                  file.path
                }
                target="_blank"
              >
                <FileOutlined className="text-3xl" />

                <div className="flex flex-col space-y-1">
                  <span
                    className={`whitespace-normal font-medium  ${
                      GoToComponent ? " line-clamp-2 truncate text-xs" : ""
                    }`}
                  >
                    {file.file_name}
                  </span>
                  <span className="text-xs font-medium ">{file.size}</span>
                </div>
              </Link>

              {fromInfosTab && GoToComponent && <>{GoToComponent}</>}
            </div>

            <div className="flex w-full items-center  justify-between text-xs font-medium text-black">
              <div className="flex items-center space-x-3">
                {file?.user_info && GoToComponent && (
                  <div className="flex items-center space-x-1">
                    <AvatarChat
                      isPublic={false}
                      type="user"
                      fontBold="font-semibold"
                      fontSize="10px"
                      hasImage={file?.user_info?.image}
                      height={6}
                      width={6}
                      size={24}
                      url={
                        file?.user_info?._id === currentUser?._id
                          ? URL_ENV?.REACT_APP_BASE_URL +
                            URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                            file?.user_info?.image
                          : URL_ENV?.REACT_APP_BASE_URL +
                            URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                            file?.user_info?.image
                      }
                      name={getName(file?.user_info?.name, "avatar")}
                    />
                    <Tooltip title={file?.user_info?.name}>
                      <span className=" max-w-md truncate whitespace-normal">
                        {getName(file?.user_info?.name, "name")}
                      </span>
                    </Tooltip>
                  </div>
                )}

                <div className="flex items-center space-x-1">
                  <HiOutlineCalendar className="text-sm font-medium" />
                  <span>
                    {moment_timezone(file.created_at).format(
                      " DD MMM YYYY HH:mm"
                    )}
                  </span>
                </div>
              </div>
              <div>
                {file?.is_office === 1 && (
                  <OpenFileButton
                    key={file?._id}
                    file={file}
                    GoToComponent={GoToComponent}
                  />
                )}
                <Tooltip
                  title={t("import.downloadFileWithTitle", {
                    filename: file.file_name,
                  })}
                >
                  <Button
                    loading={loadingDownload}
                    onClick={(e) => handleDownloadFile(e, file, setLoading)}
                    shape={GoToComponent && "  circle"}
                    icon={<DownloadOutlined />}
                    size="small"
                  >
                    {GoToComponent ? "" : t("import.download")}
                  </Button>
                </Tooltip>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default File;
