import StarterKit from "@tiptap/starter-kit";
import { Node } from "@tiptap/core";
import RootBlock from "./root-block";
import Keymap from "./keymap";
import Placeholder from "@tiptap/extension-placeholder";
import SlashCommand from "./slash-command";

const Document = Node.create({
  name: "doc",
  topNode: true,
  content: "rootblock+",
});

export const TipTapEditorExtensions = [
  Document,
  RootBlock,
  Keymap,
  StarterKit.configure({
    document: false,
  }),
  Placeholder.configure({
    placeholder: ({ node }) => {
      // Disable placeholder for table-related nodes
      if (["table", "tableRow", "tableCell"].includes(node.type.name)) {
        return null; // No placeholder for these nodes
      }

      // Placeholder for headings
      if (node.type.name === "heading") {
        return `Heading ${node.attrs.level}`;
      }

      // Default placeholder
      return "Start typing...";
    },
    includeChildren: true, // Include placeholders for child nodes, except for excluded types
  }),

  SlashCommand,
];
