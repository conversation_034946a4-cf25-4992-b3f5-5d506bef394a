import React from "react";
import Highcharts from "highcharts";
import HighchartsVariablePie from "highcharts/modules/variable-pie";
import HighchartsReact from "highcharts-react-official";

// Load the module
if (typeof HighchartsVariablePie === "function") {
  HighchartsVariablePie(Highcharts);
}

const Variablepie = ({ pivotData }) => {
  if (
    !pivotData ||
    !pivotData.rowKeys ||
    !pivotData.colKeys ||
    !pivotData.getAggregator
  ) {
    return <p className="text-center text-red-600">No Data</p>;
  }

  const rowKeys = pivotData.rowKeys;
  const colKeys = pivotData.colKeys;

  if (rowKeys.length === 0 && colKeys.length === 0) {
    return <p className="text-center text-red-600">No Data</p>;
  }
  const pieData = (rowKeys.length > 0 ? rowKeys : colKeys)
    .map((entry) => {
      const label = entry.join(" - ") || "Total";
      const otherAxis = rowKeys.length > 0 ? colKeys : rowKeys;

      const sum =
        otherAxis.length > 0
          ? otherAxis.reduce((acc, val) => {
              const r = rowKeys.length > 0 ? entry : val;
              const c = rowKeys.length > 0 ? val : entry;
              return acc + (pivotData.getAggregator(r, c)?.value() || 0);
            }, 0)
          : pivotData.getAggregator(entry, [])?.value() ||
            pivotData.getAggregator([], entry)?.value() ||
            0;

      return {
        name: label,
        y: sum,
        z: Math.sqrt(sum),
      };
    })
    .filter((item) => item.y > 0);

  if (pieData.length === 0) {
    return <p className="text-center text-red-600">No Data</p>;
  }

  const options = {
    chart: {
      type: "variablepie",
    },
    title: {
      text: `📊 ${pivotData.props.rows.join(" ")}${
        pivotData.props.cols.length
          ? " : " + pivotData.props.cols.join(" ")
          : ""
      }`,
      align: "left",
      style: {
        fontWeight: "normal",
        fontSize: "20px",
        fontFamily: "Inter, sans-serif",
      },
    },
    tooltip: {
      headerFormat: "",
      pointFormat:
        '<span style="color:{point.color}">\u25CF</span> <b>{point.name}</b><br/>' +
        "Value: <b>{point.y}</b><br/>" +
        "Radius (z): <b>{point.z}</b><br/>",
    },
    plotOptions: {
      variablepie: {
        dataLabels: {
          enabled: true,
          format: "{point.name}",
        },
      },
    },
    credits: {
      enabled: false,
    },
    series: [
      {
        name: "Data",
        data: pieData,
      },
    ],
  };

  return (
    <div className="highcharts-figure">
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
};

export default Variablepie;
