/**
 * * @name FieldsSettingArea
 *
 * @description Fields manager allows users to customize and configure the fields used across various modules of the system.
 * It provides a centralized interface where administrators can define, edit, or remove fields that capture specific data points,
 * ensuring that each module has the right fields tailored to the business's needs. Users can set field types (e.g., text, date, select, ...),
 * specify mandatory fields, and manage field visibility or accessibility. This module enhances flexibility and ensures that the
 * CRM can adapt to different business processes and workflows.
 *
 *
 * @param {Boolean} loadFields To show loader on table when data is being retrieved.
 * @param {Number} page current fields table page.
 * @param {Function} setEditingKey Set the key of the selected row in multi-options field.
 * @param {Number} editingKey The key of the option row in multi-options field.
 * @param {Number} groupId The id of the field's group.
 * @param {String} source The source where the component is being called from.
 * @param {Boolean} openDrawer Open the drawer in viewsphere.
 * @param {Number} familyIdFromDrawer The id of the family from drawer.
 * @param {Function} setIsUpdateFromVue360 Re-trigger the get fields api on add/update field from viewsphere.
 *
 * @returns {JSX.Element} Field settings wrapper (create/update field drawer + handle options table for multi-options field)
 */

import React, {
  useState,
  useEffect,
  useLayoutEffect,
  useRef,
  useContext,
  useCallback,
} from "react";
import { useSelector, useDispatch } from "react-redux";
import { useParams, useNavigate } from "react-router-dom";
import {
  Drawer,
  Button,
  Form,
  Input,
  Radio,
  Checkbox,
  Row,
  Table,
  Select,
  message,
  Col,
  Tooltip,
  Typography,
  Alert,
  Space,
  Tag,
  Progress,
} from "antd";
import { DndContext } from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import {
  PlusOutlined,
  CloseOutlined,
  MenuOutlined,
  SwapOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { motion, useAnimation } from "framer-motion";
import update from "immutability-helper";

import { toastNotification } from "../ToastNotification";
import { getFields } from "../../new-redux/actions/fields.actions/getFields";
import { createNewField } from "../../new-redux/actions/fields.actions/createNewField";
import { updateField } from "../../new-redux/actions/fields.actions/updateField";
import { RESET_FIELD_STATE } from "../../new-redux/constants";
import { displayRightIcon } from "../../utils/displayIcon";
import { deleteFieldOption } from "../../new-redux/actions/fields.actions/deleteFieldOption";
import CollapsedDraggableTable from "../../pages/collapse/CollapsedDraggableTable";
import MainService from "../../services/main.service";
import { updateFieldOptionRank } from "../../new-redux/actions/fields.actions/updateFieldOptionRank";
import ActionsHeaderTable from "../ActionsHeaderTable";
import { setOpenFieldDrawer } from "../../new-redux/actions/fields.actions/fieldDrawer";
import { optionsDateFormat } from "../../pages/settings/LocalisationSettings";
import "../style.css";
import "../EditableTable.css";

//Refers to antd editable cells: https://ant.design/components/table#components-table-demo-edit-cell
const EditableContext = React.createContext(null);

// Define a constant for common toast settings
const toastOptions = {
  position: "bottomRight",
  duration: "4.5",
};

const FieldsSettingArea = ({
  loadFields,
  page,
  setEditingKey,
  editingKey,
  groupId = null,
  source = "",
  openDrawer,
  familyIdFromDrawer = "",
  setIsUpdateFromVue360 = () => {},
}) => {
  const [updateFieldProps, setUpdateFieldProps] = useState({});
  const [familyId, setFamilyId] = useState(null);
  const [mem, setMem] = useState(
    Object.keys(updateFieldProps).length !== 0 ? updateFieldProps?.type : ""
  );
  const [limit, setLimit] = useState(70);
  const [selectedModule, setSelectedModule] = useState("");
  const [modulesList, setModulesList] = useState([]);
  const [optsValidationStatus, setOptsValidationStatus] = useState(false);
  const [deletedOptionId, setDeletedOptionId] = useState(null);
  const [groupList, setGroupList] = useState([]);
  const [loadGroups, setLoadGroups] = useState(false);
  const [selectedGroupId, setSelectedGroupId] = useState(null);
  const [selectedRowKey, setSelectedRowKey] = useState("");
  const [loadModuleList, setLoadModuleList] = useState(false);
  const [selectMultipleCountries, setSelectMultipleCountries] = useState(0);
  const [displayOptions, setDisplayOptions] = useState([]);

  // Instances declarations, extract data from redux store, destructurings.
  const {
    data,
    updateFieldOptionRankLoading,
    isError,
    errors,
    isFieldCreated,
    isFieldUpdated,
    isOptDeleted,
    deleteFieldOptLoading,
    isCreateFieldLoading,
    isUpdateFieldLoading,
    openFieldDrawer,
    drawerTitle,
    isAvatarUsed,
    isFieldRemoved,
  } = useSelector((state) => state.fields);
  const { activeMenu360, contactInfoFromDrawer, openView360InDrawer } =
    useSelector((state) => state.vue360);
    const { user } = useSelector((state) => state.user);
  const [form] = Form.useForm();
  const { types } = useSelector((state) => state.types);
  const { families } = useSelector((state) => state.families);
  const { search } = useSelector((state) => state.form);
  const { contactHeaderInfo: contactInfo } = useSelector(
    (state) => state?.contacts
  );
  const dispatch = useDispatch();
  const { id } = useParams();
  const navigate = useNavigate();
  const ref1 = useRef(null);
  const aliasInputRef = useRef(null);
  const [t] = useTranslation("common");
  const labelValue = Form.useWatch("alias", form);
  const controls2 = useAnimation();
  let updateRankFunctionOccurence = 0;
  // Table row on update an already existing field.
  const TableRow = ({ children, ...props }) => {
    const {
      attributes,
      listeners,
      setNodeRef,
      setActivatorNodeRef,
      transform,
      transition,
      isDragging,
    } = useSortable({
      id: props["data-row-key"],
    });
    const style = {
      ...props.style,
      transform: CSS.Transform.toString(
        transform && {
          ...transform,
          scaleY: 1,
        }
      ),
      transition,
      ...(isDragging
        ? {
            position: "relative",
            zIndex: 9999,
          }
        : {}),
    };
    return (
      <EditableContext.Provider value={form}>
        <tr {...props} ref={setNodeRef} style={style} {...attributes}>
          {React.Children.map(children, (child) => {
            if (
              child.key === "sort" &&
              child?.props?.record?.id !== undefined
            ) {
              return React.cloneElement(child, {
                children: (
                  <MenuOutlined
                    ref={setActivatorNodeRef}
                    style={{
                      touchAction: "none",
                      cursor: "move",
                    }}
                    {...listeners}
                  />
                ),
              });
            }
            return child;
          })}
        </tr>
      </EditableContext.Provider>
    );
  };

  // Table row on create new field.
  const EditableRow = ({ index, className, moveRow, style, ...props }) => {
    return (
      <EditableContext.Provider value={form}>
        <tr {...props} />
      </EditableContext.Provider>
    );
  };

  // Editable table cell.
  const EditableCell = ({
    title,
    editable,
    children,
    dataIndex,
    record,
    handleSave,
    optsValidationStatus,
    optionsForm,
    isTypeRadio,
    inputsList,
    ...restProps
  }) => {
    const [t] = useTranslation("common");
    const [editing, setEditing] = useState(false);
    let otherRef = useRef(null);
    const form = useContext(EditableContext);
    const optionValue = Form.useWatch(
      `listElementValue_${
        typeof record?.id === "undefined" ? record?.key : record?.id
      }`,
      optionsForm
    );

    // Replace special characters.
    const generateSlug = (str) => {
      if (typeof str !== "undefined") {
        const slug = str
          .toLowerCase()
          .trim()
          .normalize("NFD")
          .replace(/[\u0300-\u036f]/g, "")
          .replace(/[^a-z0-9]+/g, "_")
          .replace(/(^-|-$)+/g, "");
        return slug;
      } else {
        return null;
      }
    };

    useEffect(() => {
      if (record?.key !== undefined) {
        setEditing(true);
        form.setFieldsValue({
          [dataIndex]: record[dataIndex],
        });
      }
    }, [form, dataIndex, record]);

    useLayoutEffect(() => {
      if (record?.id !== undefined) {
        form.setFieldsValue({
          [dataIndex]: record[dataIndex],
        });
      }
    }, [editing, form, record, dataIndex]);

    // Fill the option value input simultaneously with label input.
    useEffect(() => {
      form.setFieldsValue({
        [`value_${
          typeof record?.id === "undefined" ? record?.key : record?.id
        }`]: generateSlug(optionValue),
      });
    }, [optionValue]);

    useEffect(() => {
      if (otherRef.current) {
        otherRef.focus();
      }
    }, [otherRef]);

    const toggleEdit = () => {
      setEditing(!editing);
      form.setFieldsValue({
        [dataIndex]: record[dataIndex],
      });
    };

    const save = async () => {
      try {
        const values = await form.validateFields();

        handleSave({
          ...record,
          ...values,
        });
      } catch (errInfo) {
        console.log("Save failed:", errInfo);
      }
    };

    // REFER TO https://stackblitz.com/edit/set-focus-dynamic-input-field-react?file=index.js

    let childNode = children;
    if (editable) {
      childNode =
        editing && record?.isModule === undefined ? (
          <>
            <Form.Item
              style={{
                margin: 0,
                width: "100%",
              }}
              rules={[
                {
                  required: true,
                  message:
                    dataIndex === "listElementValue"
                      ? t("fields_management.optLabelError")
                      : t("fields_management.optValueError"),
                },
              ]}
              name={
                typeof record?.id === "undefined"
                  ? `${dataIndex}_${record?.key}`
                  : dataIndex
              }
            >
              <Input
                ref={(el) => (otherRef = el)}
                onPressEnter={save}
                onBlur={save}
                id={
                  typeof record?.id === "undefined" &&
                  `${dataIndex}_${record?.key}`
                }
                name={dataIndex}
                placeholder={
                  dataIndex === "listElementValue"
                    ? t("fields_management.opt_label")
                    : t("fields_management.opt_value")
                }
              />
            </Form.Item>
          </>
        ) : (
          <div
            // className="editable-cell-value-wrap"
            style={{
              paddingRight: 24,
            }}
            onClick={toggleEdit}
          >
            <Tooltip title={t("fields_management.optTooltip")}>
              {children}
            </Tooltip>
          </div>
        );
    }
    return <td {...restProps}>{childNode}</td>;
  };

  // Update table's data on load.
  const [fieldsArray, setFieldsArray] = useState(
    data?.fields ? data?.fields?.data?.fields : null
  );

  // Trigger get fields API from redux actions.
  const fetchFields = useCallback(
    (familyId) => {
      dispatch(getFields(familyId, page, limit, search, ""));
      setFamilyId(familyId);
    },
    [page,id, limit]
  );

  // Fire getFields API call in redux store.
  useEffect(() => {
    if (familyIdFromDrawer) {
      fetchFields(familyIdFromDrawer);//whats this params to open drawer 
    } else if (id !== null) {
      let familyId = openView360InDrawer //
        ? contactInfoFromDrawer?.family_id
        : contactInfo?.family_id
        ? contactInfo?.family_id
        : families && families.filter((element) => element?.label == id)[0]?.id;
      if (familyId) {
        fetchFields(familyId);
      }
    }
  }, [fetchFields, id, familyIdFromDrawer]);

  //Custom hook to get the previous value after change.
  const usePreviousValue = (value) => {
    const memRef = useRef();
    useEffect(() => {
      memRef.current = value;
    });
    return memRef.current;
  };

  let prevValue = usePreviousValue(mem);

  // Get groups list whenever family changes.
  const getGroupsByFamily = useCallback(async () => {
    let response;

    try {
      setLoadGroups(true);
      if (familyIdFromDrawer) {
        response = await MainService.getGroupsByFamily(familyIdFromDrawer);
      } else {
        let familyIdentifier =
          families && families.find((element) => element?.label == id)?.id;
        response = await MainService.getGroupsByFamily(familyIdentifier);
      }
      setGroupList(response?.data?.data);
      setLoadGroups(false);
    } catch (error) {
      setLoadGroups(false);
      console.log(`Error ${error}`);
    }
  }, [id, familyIdFromDrawer]);

  useEffect(() => {
    getGroupsByFamily();
    //  selectedGroupId IN DEPENDENCY ARRAY
  }, [getGroupsByFamily]);

  // Get the display parameters. These params handle where the values of fields to be shown.
  const getDisplayParams = async () => {
    try {
      const response = await MainService.getDisplayOptions(familyId);
      setDisplayOptions(response?.data?.data);
    } catch (error) {
      console.log(`Error ${error}`);
    }
  };

  // Re-trigger the display parameters api whenever the family id changes.
  useEffect(() => {
    if (familyId || isFieldCreated || isFieldUpdated || isFieldRemoved) {
      getDisplayParams();
    }
  }, [familyId, isFieldCreated, isFieldUpdated, isFieldRemoved]);

  // Format the display parameters in the select component.
  let displayParamData =
    displayOptions &&
    displayOptions?.map((item) => ({
      label: (
        <div className="flex flex-row items-center justify-between">
          <p>{item?.label.replaceAll("_", " ")}</p>
          <span className="flex flex-row items-center">
            <span className="text-[10px] text-[#94a3b8]">{`${item?.current_content}/${item?.max_content}`}</span>
            <Progress
              type="circle"
              percent={(item?.current_content / item?.max_content) * 100}
              size={18}
              className="ml-1"
              showInfo={false}
            />
          </span>
        </div>
      ),
      option: item?.label.replaceAll("_", " "),
      value: item?.id,
      disabled:
        updateFieldProps?.field_display?.some(
          (el) => Number(el?.id) === Number(item?.id)
        ) &&
        updateFieldProps?.system_param?.field_display?.some(
          (el) => Number(el) === Number(item?.id)
        ),
    }));

  // State that holds options values.
  const [dataSource, setDataSource] = useState([
    { key: 0, listElementValue: "", value: "", inputRef: null },
  ]);

  // Reset options table in multi-options field.
  useLayoutEffect(() => {
    if (
      (!prevValue || mem !== prevValue) &&
      Object.keys(updateFieldProps)?.length === 0
    ) {
      setDataSource([
        {
          key: 0,
          listElementValue: "",
          value: "",
          inputRef: null,
        },
      ]);
    }
  }, [mem, updateFieldProps]);

  // Show notification on add new Field + reset inputs + close drawer.
  useEffect(() => {
    if (isFieldCreated === true) {
      toastNotification(
        "success",
        t("toasts.fieldCreated"),
        toastOptions.position,
        toastOptions.duration
      );
      resetData();
      setSelectedRowKey(selectedGroupId);
      setDataSource([
        {
          key: 0,
          listElementValue: "",
          value: "",
        },
      ]);
      setMem("");
      dispatch(setOpenFieldDrawer(false));
      // Reset redux store after 3 seconds.
      let timer = setTimeout(() => {
        dispatch({ type: RESET_FIELD_STATE });
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [dispatch, isFieldCreated]);

  // Show notification on update a Field + reset inputs + close drawer.
  useEffect(() => {
    if (isFieldUpdated === true) {
      toastNotification(
        "success",
        t("toasts.fieldUpdated"),
        toastOptions.position,
        toastOptions.duration
      );
      resetData();
      setUpdateFieldProps({});
      setDataSource([
        {
          key: 0,
          listElementValue: "",
          value: "",
        },
      ]);
      setMem("");
      dispatch(setOpenFieldDrawer(false));
      // Reset redux store after 3 seconds.
      let timer = setTimeout(() => {
        dispatch({ type: RESET_FIELD_STATE });
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [dispatch, isFieldUpdated]);

  // Show notification on delete an option on a multiselect Field + reset inputs + close drawer.
  useEffect(() => {
    if (isOptDeleted === true) {
      toastNotification(
        "success",
        t("toasts.fieldDeleted"),
        toastOptions.position,
        toastOptions.duration
      );
      let filteredData = dataSource.filter(
        (element) => element?.id != deletedOptionId.toString()
      );
      setDataSource(filteredData);
      // Reset redux store after 3 seconds.
      let timer = setTimeout(() => {
        dispatch({ type: RESET_FIELD_STATE });
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [dispatch, isOptDeleted, deletedOptionId]);

  // Show notification if any error occurs.

  const errorMessages = {
    409: t("toasts.usedField"),
    500: (errors) => `${errors?.data?.message}`,
    "You have exceeded the maximum number of fields": (errors) => (
      <div
        dangerouslySetInnerHTML={{
          __html: t("fields_management.exceedDisplayError", {
            views: errors?.data?.views_error?.toString(),
          }),
        }}
      ></div>
    ),
    "field label already exist": t("toasts.existingFieldLabelError"),
    "Unable to modify properties as hidden and required simultaneously.": t(
      "fields_management.changeParamsError"
    ),
    "field group label already exist": t("toasts.duplicatedGrpNameError"),
    "This group contains fields that are in use and, therefore, cannot be deleted":
      t("fields_management.deleteGrpError"),
    "Unable to modify the system display views .": t(
      "fields_management.deleteSystemViewError"
    ),
    "Unable to store properties as hidden and required simultaneously.": t(
      "fields_management.hiddenAndRequiredError"
    ),
  };

  useEffect(() => {
    if (isError) {
      console.log({ errors });

      // Check for specific errors
      let errorMessage;
      if (errors?.data?.[0]) {
        errorMessage = errorMessages[errors?.data[0]];
      }

      // Check for status-based errors
      if (!errorMessage && errors?.status) {
        errorMessage =
          errorMessages[errors?.status] || errorMessages[errors?.data?.message];
      }

      // Show toast if an error message exists
      if (errorMessage) {
        toastNotification(
          "error",
          errorMessage,
          toastOptions.position,
          toastOptions.duration
        );
      } else {
        toastNotification(
          "error",
          t("toasts.somethingWrong"),
          toastOptions.position,
          toastOptions.duration
        );
      }

      // Reset redux store after 3 seconds
      const timer = setTimeout(() => {
        dispatch({ type: RESET_FIELD_STATE });
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [dispatch, isError, errors]);

  // Detect if the type select is module.
  const isModule = (typeId, typesArray) => {
    const item =
      typesArray && typesArray.find((element) => element?.id == typeId);
    if (item?.module == 1) {
      return true;
    } else return false;
  };

  // Returs boolean. If true options table will be displayed.
  const showOptionsInput = (typeId, typesArray) => {
    const item =
      typesArray && typesArray.find((element) => element?.id == typeId);
    if (item?.list === true && item?.fieldType !== "country") {
      return true;
    } else return false;
  };

  // Returs boolean. If true options table will be displayed.
  const FielTypeIsCountry = (typeId, typesArray) => {
    const item =
      typesArray && typesArray.find((element) => element?.id == typeId);
    if (item?.fieldType === "country") {
      return true;
    } else return false;
  };

  // Prefill the form on update field.
  useEffect(() => {
    if (Object.keys(updateFieldProps).length !== 0) {
      form.setFieldsValue({
        defaultType: updateFieldProps?.field_type_id,
        label: updateFieldProps?.label,
        alias: updateFieldProps?.alias,
        multipleCheckbox: updateFieldProps?.multiple === 1 ? true : false,
        fieldDescription: updateFieldProps?.description,
        placeholder: updateFieldProps?.placeholder,
        dateFormat: updateFieldProps?.format,
        module:
          modulesList &&
          modulesList.find(
            (element) => element?.id == updateFieldProps?.field_module
          )?.label,
        fieldGroup:
          groupList &&
          groupList.find(
            (element) => element?.id == updateFieldProps?.field_group_id
          )?.label,
        checkboxGroup: [
          updateFieldProps?.avatar ? "avatar" : null,
          updateFieldProps?.hidden ? "hidden" : null,
          updateFieldProps?.uniqueValue ? "uniqueValue" : null,
          updateFieldProps?.required ? "required" : null,
          updateFieldProps?.read_only ? "readOnly" : null,
          updateFieldProps?.guest ? "showToGuest" : null,
          updateFieldProps?.form_guest ? "showInGuestForm" : null,
        ],
        display: updateFieldProps?.field_display
          ? updateFieldProps?.field_display?.map((el) => el?.id)
          : [],
      });
      if (isModule(updateFieldProps?.field_type_id, types)) {
        form.setFieldsValue({
          moduleFieldType: updateFieldProps?.module_type,
        });
      } else {
        setDataSource(
          updateFieldProps?.field_list_value?.map((el) => {
            return { ...el, updateKey: el?.id };
          })
        );
      }
    } else if (Object.keys(updateFieldProps).length === 0) {
      setDataSource([
        {
          key: 0,
          listElementValue: "",
          value: "",
        },
      ]);
    }
  }, [form, updateFieldProps, modulesList]);

  // Function that resets forms inputs.
  const resetData = () => {
    form.resetFields();
    form.setFieldsValue({
      radioGroup: "",
      label: "",
      checkboxGroup: [],
      dateFormat: null,
    });
    setSelectMultipleCountries(0);
  };

  // Save new field (without options).
  const saveNewField = async (values) => {
    let normalFieldPayload = {
      label: values?.label.trim(),
      alias: values?.alias.trim(),
      field_type_id: values?.radioGroup,
      family_id: familyId,
      placeholder: values?.placeholder ? values?.placeholder : "",
      description: values?.fieldDescription ? values?.fieldDescription : "",
      field_format: isFieldOfTypeDate(mem) ? values?.dateFormat : null,
      field_group_id:
        typeof values?.fieldGroup == "string"
          ? groupList &&
            groupList.find((element) => element?.label == values?.fieldGroup)
              ?.id
          : values?.fieldGroup,
      hidden: values?.checkboxGroup?.includes("hidden") ? 1 : 0,
      uniqueValue: values?.checkboxGroup?.includes("uniqueValue") ? 1 : 0,
      required: values?.checkboxGroup?.includes("required") ? 1 : 0,
      read_only: values?.checkboxGroup?.includes("readOnly") ? 1 : 0,
      avatar: values?.checkboxGroup?.includes("avatar") ? 1 : 0,
      guest: values?.checkboxGroup?.includes("showToGuest") ? 1 : 0,
      form_guest: values?.checkboxGroup?.includes("showInGuestForm") ? 1 : 0,
      multiple: selectMultipleCountries ? 1 : 0,
      display: values?.display ? values?.display.toString() : null,
    };

    if (
      form
        .getFieldsError()
        .map((element) => element.errors)
        .flat().length === 0
    ) {
      dispatch(createNewField(normalFieldPayload));
    }
  };

  // Save new field (with options).
  const saveNewFieldWithOptions = async (values) => {
    var formData = new FormData();

    formData.append("label", values?.label.trim());
    formData.append("alias", values?.alias.trim());
    formData.append("field_type_id", values?.radioGroup);
    formData.append("family_id", familyId);
    formData.append(
      "field_group_id",
      typeof values?.fieldGroup == "string"
        ? groupList &&
            groupList.find((element) => element?.label == values?.fieldGroup)
              ?.id
        : values?.fieldGroup
    );
    formData.append(
      "placeholder",
      values?.placeholder ? values?.placeholder : ""
    );
    formData.append(
      "description",
      values?.fieldDescription ? values?.fieldDescription : ""
    );
    formData.append(
      "hidden",
      values?.checkboxGroup?.includes("hidden") ? 1 : 0
    );
    formData.append(
      "uniqueValue",
      values?.checkboxGroup?.includes("uniqueValue") ? 1 : 0
    );
    formData.append(
      "required",
      values?.checkboxGroup?.includes("required") ? 1 : 0
    );
    formData.append(
      "read_only",
      values?.checkboxGroup?.includes("readOnly") ? 1 : 0
    );
    formData.append(
      "guest",
      values?.checkboxGroup?.includes("showToGuest") ? 1 : 0
    );
    formData.append(
      "form_guest",
      values?.checkboxGroup?.includes("showInGuestForm") ? 1 : 0
    );
    formData.append(
      "display",
      values?.display ? values?.display?.toString() : null
    );

    if (isModule(mem, types)) {
      formData.append("field_module_id", values?.module);
      formData.append("module_type", values?.moduleFieldType);
    }

    // Format the options of the field.
    if (showOptionsInput(mem, types) && !isModule(mem, types)) {
      var i = 0;
      var j = 0;
      for (const property in values) {
        delete values["radioGroup"] &&
          delete values["key"] &&
          delete values["isModule"] &&
          delete values["inputRef"] &&
          delete values["checkboxGroup"] &&
          delete values["fieldGroup"] &&
          delete values["label"] &&
          delete values["fieldDescription"] &&
          delete values["placeholder"] &&
          delete values["alias"] &&
          delete values["display"];

        //It should be in this format:
        // label: listElementValue_[key_of_row]
        // internal name: value_[key_of_row]
        let payloadLabel = `options[${j}][${property.split("_")[0]}]`;
        let payloadValue = values[property];

        i !== 0 &&
          formData.append(
            payloadLabel,
            typeof payloadValue == "string"
              ? payloadValue?.trim()
              : payloadValue
          );
        if (i % 2 === 0) {
          j++;
        }
        i++;
      }
    }
    dispatch(createNewField(formData));
  };

  // Update a specific field (with or without options).
  const updateLabelField = async (values) => {
    var formData = new FormData();

    formData.append("label", values?.label.trim());
    formData.append("alias", values?.alias.trim());
    formData.append("field_type_id", updateFieldProps?.field_type_id);
    formData.append("hidden", values?.checkboxGroup.includes("hidden") ? 1 : 0);
    formData.append(
      "required",
      values?.checkboxGroup?.includes("required") ? 1 : 0
    );
    formData.append(
      "read_only",
      values?.checkboxGroup?.includes("readOnly") ? 1 : 0
    );
    formData.append("multiple", selectMultipleCountries ? 1 : 0);
    formData.append(
      "field_group_id",
      typeof values?.fieldGroup == "string"
        ? groupList &&
            groupList.find((element) => element?.label == values?.fieldGroup)
              ?.id
        : values?.fieldGroup
    );
    formData.append(
      "placeholder",
      values?.placeholder ? values?.placeholder : ""
    );
    formData.append(
      "description",
      values?.fieldDescription ? values?.fieldDescription : ""
    );
    formData.append("minValue", updateFieldProps?.minValue);
    formData.append("maxValue", updateFieldProps?.maxValue);
    formData.append("rank", updateFieldProps?.rank);
    formData.append(
      "uniqueValue",
      values?.checkboxGroup?.includes("uniqueValue") ? 1 : 0
    );
    formData.append(
      "avatar",
      values?.checkboxGroup?.includes("avatar") ? 1 : 0
    );
    formData.append(
      "guest",
      values?.checkboxGroup?.includes("showToGuest") ? 1 : 0
    );
    formData.append(
      "form_guest",
      values?.checkboxGroup?.includes("showInGuestForm") ? 1 : 0
    );
    formData.append("family_id", updateFieldProps?.family_id);
    formData.append(
      "field_format",
      values?.dateFormat ? values?.dateFormat : null
    );
    formData.append(
      "display",
      values?.display ? values?.display?.toString() : null
    );
    if (isModule(updateFieldProps?.field_type_id, types)) {
      formData.append("module_type", values?.moduleFieldType);
    }

    if (showOptionsInput(updateFieldProps?.field_type_id, types)) {
      let filter = dataSource
        .filter((object) => !object?.key)
        .map(({ id, value, listElementValue }, el) => ({
          id,
          value,
          listElementValue,
        }));
      for (const key in values) {
        delete values["defaultType"] &&
          delete values["checkboxGroup"] &&
          delete values["fieldGroup"] &&
          delete values["label"] &&
          delete values["fieldDescription"] &&
          delete values["placeholder"] &&
          delete values["alias"] &&
          delete values["display"];
      }

      // Return the options and format the payload:
      // the differentiation between the new option and the old option happens in the backend
      //if it is an update of an existing option, send the id: listElementValue_[id of option]
      //if it is a new option, send the key of row: listElementValue_[key of row]
      let newArr = Object.keys(values).map((el) => {
        return {
          [el]: values[el],
        };
      });
      let anotherNewArr = newArr
        ?.map(
          (el, i) => i % 2 === 0 && { ...newArr.at(i), ...newArr.at(i + 1) }
        )
        ?.filter((el) => typeof el !== "boolean");
      let newValues = [...filter, ...anotherNewArr];

      newValues?.forEach((element, i) => {
        for (const property in element) {
          let payloadLabel = `options[${i}][${
            property?.includes("_") ? property.split("_")[0] : property
          }]`;
          let payloadValue = element[property];
          formData.append(
            payloadLabel,
            typeof payloadValue == "string"
              ? payloadValue?.trim()
              : payloadValue
          );
        }
      });
    }

    //The final payload to be sent to the server.
    let payload = { fieldId: updateFieldProps?.id, formData };

    dispatch(updateField(payload));
  };

  // Function will be triggered if no error on form.
  const onFinish = (values) => {
    if (
      (showOptionsInput(mem, types) || isModule(mem, types)) &&
      Object.keys(updateFieldProps).length === 0
    ) {
      saveNewFieldWithOptions(values);
    } else if (
      showOptionsInput(mem, types) === false &&
      Object.keys(updateFieldProps).length === 0
    ) {
      saveNewField(values);
    } else if (Object.keys(updateFieldProps).length !== 0) {
      updateLabelField(values);
    }
    if (activeMenu360 === "4" || activeMenu360 === "1") {
      setIsUpdateFromVue360(true);
    }
  };

  // Function will be triggered if an error occurs on form.
  const onFinishFailed = (errorInfo) => {};

  // Prefill associated group select on open create field's drawer.
  useEffect(() => {
    if (openFieldDrawer && Object.keys(updateFieldProps).length == 0) {
      form.setFieldsValue({
        fieldGroup:
          groupList &&
          groupList.find((element) => element?.id == selectedGroupId)?.label,
      });
    }
  }, [form, openFieldDrawer, selectedGroupId]);

  // Replace special characters.
  const generateSlug = (str) => {
    if (str !== undefined) {
      const slug = str
        .toLowerCase()
        .trim()
        .normalize("NFD")
        .replace(/[\u0300-\u036f]/g, "")
        .replace(/[^a-z0-9]+/g, "_")
        .replace(/(^-|-$)+/g, "");
      return slug;
    } else {
      return null;
    }
  };

  // Fill the alias input simultaneously with label input.
  useEffect(() => {
    if (Object.keys(updateFieldProps).length === 0) {
      form.setFieldsValue({
        label: generateSlug(labelValue),
      });
    }
  }, [updateFieldProps, form, labelValue]);

  // Handle add new key on add row (options table).
  const [count, setCount] = useState(1);

  // Delete a row / Delete an option (w/API).
  const handleDeleteOption = (key) => {
    // just delete a row
    if (typeof key !== "string") {
      const newData = dataSource.filter((item) => item.key != key);
      setDataSource(newData);
    } else {
      // Dispatch remove option api.
      dispatch(deleteFieldOption(updateFieldProps?.id, key));
      setDeletedOptionId(key);
    }
  };

  // Default options table columns.
  const defaultColumns = [
    {
      key: "sort",
      width: "50px",
      className: "remove_border_right_tr",
      hidden: Object.keys(updateFieldProps).length === 0,
    },
    {
      dataIndex: "operation",
      width: "10%",
      render: (_, props) => (
        <ActionsHeaderTable
          record={props}
          handleDelete={handleDeleteOption}
          form={form}
          isEditing={() => {}}
          updateOptionField={Object.keys(updateFieldProps)?.length}
          data={dataSource}
          setData={setDataSource}
          editingKey={props?.default == 1 ? "1" : ""}
          api=""
          source="fieldOptions"
        />
      ),
    },
    {
      title: (
        <>
          <span
            style={{
              fontSize: "15px",
              color: "#ff4d4f",
              marginInlineEnd: "4px",
              lineHeight: 1,
            }}
          >
            *
          </span>
          {t("fields_management.opt_label")}
        </>
      ),
      dataIndex: "listElementValue",
      editable: true,
      width: "45%",
    },
    {
      title: (
        <>
          <span
            style={{
              fontSize: "15px",
              color: "#ff4d4f",
              marginInlineEnd: "4px",
              lineHeight: 1,
            }}
          >
            *
          </span>
          {t("fields_management.opt_value")}
        </>
      ),
      dataIndex: "value",
      editable: true,
      width: "45%",
    },
  ];

  // Add new row in options table (on create/update field with options).
  const handleAdd = () => {
    const newData = {
      key: Number(count + 1),
      listElementValue: "",
      value: "",
      inputRef: null,
    };
    setDataSource([...dataSource, newData]);
    setCount(count + 1);
  };

  // Save the newly added row (remove focus and disable editing).
  const handleSave = (row) => {
    const newData = [...dataSource];
    // Index varies on newly added row and already existing option.
    const index =
      Object.keys(updateFieldProps).length !== 0
        ? newData.findIndex((item) =>
            row?.id === undefined ? row?.key == item?.key : row.id == item?.id
          )
        : newData.findIndex((item) => row?.key == item?.key);

    const item = newData[index];
    newData.splice(index, 1, {
      ...item,
      ...row,
    });
    setDataSource(newData);
  };

  // Update the rank of the field options. (after drag and drop)
  const moveRow = useCallback(
    (dragIndex, hoverIndex) => {
      const dragRow = dataSource[dragIndex];
      updateOptionsRank(
        update(dataSource, {
          $splice: [
            [dragIndex, 1],
            [hoverIndex, 0, dragRow],
          ],
        })
      );
      setDataSource(
        update(dataSource, {
          $splice: [
            [dragIndex, 1],
            [hoverIndex, 0, dragRow],
          ],
        })
      );
    },
    [dataSource]
  );

  // Options of the draggable options table.
  const components = {
    body: {
      row: EditableRow,
      cell: EditableCell,
    },
  };

  // Merge columns in the options table (refer to antd docs).
  const columns = defaultColumns
    .filter((el) => !el?.hidden)
    ?.map((col, i) => {
      if (!col.editable) {
        return col;
      }
      return {
        ...col,
        onCell: (record) => ({
          record,
          editable: col.editable,
          dataIndex: col.dataIndex,
          title: col.title,
          hidden: col.hidden,
          handleSave,
          optsValidationStatus: optsValidationStatus,
          optionsForm: form,
          isTypeRadio:
            types &&
            types.find((type) => type?.fieldType == "radio")?.id == mem,
          inputsList: dataSource,
        }),
      };
    });

  //Retrieve the list of modules.
  const getModulesList = async () => {
    try {
      setLoadModuleList(true);
      const response = await MainService.getModules();
      setModulesList(response?.data?.data);
      setLoadModuleList(false);
    } catch (error) {
      setLoadModuleList(false);
      console.log(`Error ${error}`);
    }
  };

  // Trigger on component first mount.
  useEffect(() => {
    getModulesList();
  }, []);


  // Format the modules options before insert them in the select.
  const modelOptions =
    modulesList &&
    modulesList?.filter(item => {

      const moduleAccess = user?.access[item?.label?.toLowerCase()];
  
      return moduleAccess === undefined || moduleAccess !== "1";
    })
      ?.sort((a, b) => a.label.localeCompare(b.label))
      ?.map((element) => ({
        label: element?.label,
        value: element?.id,
      }));

  // groups options.
  const FieldsGroupsOptions =
    groupList &&
    groupList.map((element) => ({
      label: element?.label,
      value: element?.id,
    }));

  // On select the module type, user should select how to display the values of the module (select, radio, ...).
  const moduleFieldTypeOptions =
    types &&
    types
      .filter(
        (element) =>
          element?.fieldType === "multiselect" ||
          element?.fieldType === "radio" ||
          element?.fieldType === "select" ||
          element?.fieldType === "checkbox" ||
          element?.fieldType === "autocomplete"
      )
      ?.map((element) => ({
        label: (
          <p className="flex items-center justify-between">
            {element?.fieldType}
            {displayRightIcon(element?.fieldType, 4, 4)}
          </p>
        ),
        value: element?.id,
      }));

  // Format the field types.
  const selectTypeOptions =
    types &&
    types?.map((type, index) => ({
      key: index,
      typeName: type?.fieldType.replaceAll("_", " "),
      value: type?.id?.toString(),
      label: (
        <Space className="px-1">
          {displayRightIcon(type?.fieldType, 4, 4)}
          {type?.fieldType.replaceAll("_", " ")}{" "}
        </Space>
      ),
    }));

  // Dnd-kit drag handler. (on drop, it will trigger change option rank API), after changing the fields rank.
  const onDragEnd = ({ active, over }) => {
    if (active?.id != over?.id) {
      if (
        dataSource[dataSource.findIndex((i) => i.id === over?.id)]?.id ==
        undefined
      ) {
        return;
      } else {
        updateOptionsRank(
          arrayMove(
            dataSource,
            dataSource.findIndex((i) => i?.id == active?.id),
            dataSource.findIndex((i) => i?.id == over?.id)
          )
        );
        setDataSource((previous) => {
          const activeIndex = previous.findIndex((i) => i?.id == active?.id);
          const overIndex = previous.findIndex((i) => i?.id == over?.id);
          return arrayMove(previous, activeIndex, overIndex);
        });
      }
    }
  };

  // Update rank api call.
  const updateOptionsRank = async (array) => {
    var formData = new FormData();
    array &&
      array.forEach((item, i) => {
        if (item) {
          let key = `rank[${item?.id}]`;
          let value = i + 1;
          formData.append(key, value);
        }
      });
    let fieldId = updateFieldProps?.id;

    let payload = {
      fieldId,
      formData,
    };
    updateRankFunctionOccurence += 1;
    try {
      dispatch(updateFieldOptionRank(payload));
      message.success(t("toasts.rankChanged"), 3);
    } catch (error) {
      console.log("errr", error);
      message.error(t("toasts.rankFailed"), 3);
    }
  };

  // Animate the options table on select a multi-options field type.
  useEffect(() => {
    if (showOptionsInput(mem, types)) {
      controls2.start({ height: "auto", opacity: 1 });
    }
  }, [mem]);

  // Check if field of type date to display 'date format' select.
  const isFieldOfTypeDate = (selectedType) => {
    let fieldType =
      types && types.find((el) => Number(el?.id) === Number(selectedType));
    return (
      fieldType?.fieldType === "date" ||
      fieldType?.fieldType === "date_time" ||
      fieldType?.fieldType === "range"
    );
  };

  // Check if field of type image.
  const isTypeImage = (selection) => {
    let fieldType =
      types && types.find((el) => Number(el?.id) === Number(selection));
    return fieldType?.fieldType === "image";
  };

  // Check if field of type country.
  const isFieldOfTypeCountry = (selectedType) => {
    let fieldType =
      types && types.find((el) => Number(el?.id) === Number(selectedType));
    return fieldType?.fieldType === "country";
  };

  // Row className for options table.
  const rowClassName = (record) => {
    if (record["key"] === editingKey) {
      return "editingRow editable-row";
    }
    if (selectedRowKey == record.key) {
      return "selected-row editable-row";
    }

    return "editable-row";
  };

  //Select group id for fields table from drawer.
  useEffect(() => {
    setSelectedGroupId(groupId);
    setSelectedRowKey(groupId);
  }, [groupId]);

  //Handle close drawer and reset fields.
  const handleDrawerClose = () => {
    dispatch(setOpenFieldDrawer(false));
    setMem("");
    setOptsValidationStatus(false);

    // Reset field data and set to empty
    resetData();
    setUpdateFieldProps({});
    setSelectedModule("");

    if (Object.keys(updateFieldProps)?.length === 0 && isModule(mem, types)) {
      // Reset if conditions match
      setSelectedModule("");
    }
  };

  //Field parameter checkboxes.
  const checkboxConfig = (isAvatarUsed, updateFieldProps, mem, t) => [
    {
      value: "avatar",
      label: "Avatar",
      disabled:
        isAvatarUsed === 1 &&
        (updateFieldProps?.avatar === 0 || isTypeImage(mem)),
      condition: isTypeImage(updateFieldProps?.field_type_id || mem),
    },
    {
      value: "hidden",
      label: t("fields_management.hidden"),
      disabled: updateFieldProps?.system_param?.hidden === 1,
    },
    {
      value: "uniqueValue",
      label: t("fields_management.uniqueValue"),
      disabled: updateFieldProps?.system_param?.uniqueValue === 1,
    },
    {
      value: "required",
      label: t("fields_management.required"),
      disabled: updateFieldProps?.system_param?.required === 1,
    },
    {
      value: "readOnly",
      label: t("fields_management.readOnly"),
      disabled: updateFieldProps?.system_param?.read_only === 1,
    },
    {
      value: "showToGuest",
      label: t("fields_management.externalUserParam"),
      disabled: false, // Add condition here if needed
      tooltip: t("fields_management.externalUserParamInfo"),
    },
    {
      value: "showInGuestForm",
      label: t("fields_management.showInGuestForm"),
      disabled: false, // Add condition here if needed
    },
  ];

  return (
    <>
      <div>
        {/* Main Card with the fields management table */}
        <section className="flex flex-col justify-around">
          <CollapsedDraggableTable
            // key={familyId} commentitou 5ater ya3mel y3awed fel api deux fois
            source={source}
            setUpdateFieldProps={setUpdateFieldProps}
            loadFields={loadFields}
            fieldsArray={fieldsArray}
            familyId={familyIdFromDrawer}
            setLimit={setLimit}
            limit={limit}
            groupList={groupList}
            setGroupList={setGroupList}
            loadGroups={loadGroups}
            setEditingKey={setEditingKey}
            editingKey={editingKey}
            selectedGroupId={selectedGroupId}
            setSelectedGroupId={setSelectedGroupId}
            setSelectedRowKey={setSelectedRowKey}
            selectedRowKey={selectedRowKey}
            modulesList={modulesList}
            openDrawerFromStage={openDrawer}
            setIsUpdateFromVue360={setIsUpdateFromVue360}
          />
        </section>
        {/* Create & Update drawer */}
        <Drawer
          title={drawerTitle}
          open={openFieldDrawer}
          maskClosable={false}
          onClose={handleDrawerClose}
          size="large"
          footer={
            <Form.Item>
              <Button
                form="drawer-form"
                onClick={handleDrawerClose}
                style={{ marginRight: "10px" }}
              >
                {t("fields_management.drawerCloseBtn")}
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={isCreateFieldLoading || isUpdateFieldLoading}
                form="drawer-form"
                id="submit-btn"
              >
                {t("fields_management.drawerOkBtn")}
              </Button>
            </Form.Item>
          }
          /* extra={
            // Go to module config when module type is selected
            isModule(updateFieldProps?.field_type_id, types) && (
              <Button
                onClick={() => {
                  window.open(
                    `/settings/general/${
                      modulesList &&
                      modulesList.find(
                        (element) =>
                          element?.id == updateFieldProps?.field_module
                      )?.label
                    }`,
                    "_blank"
                  );
                }}
                type="link"
                ref={ref1}
                icon={<SwapOutlined />}
                loading={loadModuleList}
              >
                {isModule(updateFieldProps?.field_type_id, types)
                  ? t("fields_management.moduleFieldRedirectBtn", {
                      moduleName:
                        modulesList &&
                        modulesList.find(
                          (element) =>
                            element?.id == updateFieldProps?.field_module
                        )?.label,
                    })
                  : FielTypeIsCountry(updateFieldProps?.field_type_id, types)
                  ? t("fields_management.redirectToCountries")
                  : t("fields_management.opt_table_btn")}
              </Button>
            )
          } */
        >
          {/* The main fields form */}
          <Form
            id="drawer-form"
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
            className="flex flex-col divide-y divide-gray-200 bg-white"
            layout="vertical"
            form={form}
            name="mainForm"
            onValuesChange={(changedValues, allValues) => {
              setMem(allValues?.radioGroup);
            }}
          >
            <div className="h-0 flex-1 overflow-y-auto">
              <div className="flex flex-1 flex-col justify-between">
                <div className="divide-y divide-gray-200 px-4 sm:px-6">
                  {Object.keys(updateFieldProps).length > 0 && (
                    <div className="flex flex-row items-center justify-between">
                      <Form.Item
                        name="defaultType"
                        label={t("fields_management.selectedFieldType")}
                      >
                        <Radio.Group buttonStyle="solid" optionType="button">
                          {types?.map((element) =>
                            element?.id == updateFieldProps?.field_type_id ? (
                              <Radio.Button
                                value={updateFieldProps?.field_type_id}
                              >
                                <p className="flex items-center justify-between">
                                  {element?.fieldType}
                                  {displayRightIcon(element?.fieldType, 4, 4)}
                                </p>
                              </Radio.Button>
                            ) : null
                          )}
                        </Radio.Group>
                      </Form.Item>
                      {isModule(updateFieldProps?.field_type_id, types) && (
                        <>
                          <Form.Item label={t("fields_management.moduleName")}>
                            <Tag>
                              {modulesList &&
                                modulesList.find(
                                  (element) =>
                                    Number(element?.id) ===
                                    Number(updateFieldProps?.field_module)
                                )?.label}
                            </Tag>
                          </Form.Item>
                          <Form.Item
                            label={t("fields_management.displayedType")}
                          >
                            <Tag
                              icon={displayRightIcon(
                                types?.find(
                                  (element) =>
                                    element?.id == updateFieldProps?.module_type
                                )?.fieldType,
                                2,
                                4
                              )}
                            >
                              {
                                types?.find(
                                  (element) =>
                                    element?.id == updateFieldProps?.module_type
                                )?.fieldType
                              }
                            </Tag>
                          </Form.Item>
                        </>
                      )}
                    </div>
                  )}
                  {/* Display this select only for module type */}
                  {(Object.keys(updateFieldProps)?.length === 0 &&//whats this condition for
                    isModule(mem, types)) ||
                  (Object.keys(updateFieldProps).length > 0 &&
                    isModule(updateFieldProps?.field_type_id, types)) ? (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={
                        isModule(mem, types)
                          ? { height: "auto", opacity: 1 }
                          : { height: 0, opacity: 0 }
                      }
                      transition={{ duration: 0.5 }}
                    >
                      <Form.Item
                        name="module"
                        label="Module"
                        rules={[
                          {
                            required: true,
                            message: t(
                              "fields_management.selectModuleErrorMsg"
                            ),
                          },
                        ]}
                      >
                        <Select
                          showSearch
                          placeholder={t(
                            "fields_management.selectModulePlaceholder"
                          )}
                          allowClear
                          filterOption={(inputValue, option) =>
                            option?.label
                              ?.toLowerCase()
                              ?.includes(inputValue?.toLowerCase())
                          }
                          style={{
                            width: "100%",
                          }}
                          options={modelOptions}
                          onChange={(value, option) => {
                            setSelectedModule(option?.label);
                          }}
                          disabled={Object.keys(updateFieldProps).length > 0}
                          loading={loadModuleList}
                        />
                      </Form.Item>
                      <Form.Item
                        name="moduleFieldType"
                        label={t("fields_management.moduleType")}
                        initialValue={5}
                      >
                        <Radio.Group
                          buttonStyle="solid"
                          optionType="button"
                          options={moduleFieldTypeOptions}
                          defaultValue={5}
                        />
                      </Form.Item>
                    </motion.div>
                  ) : null}
                  {Object.keys(updateFieldProps)?.length === 0 &&
                  types?.find((type) => type?.id === mem)?.fieldType ===
                    "multiselect" ? (
                    <Alert
                      message={t("fields_management.multiselectInfo")}
                      type="info"
                      showIcon
                      style={{ marginBottom: "10px" }}
                    />
                  ) : null}
                  {/* Field types grid at the beginning of the creation of a field */}
                  {!mem && Object.keys(updateFieldProps).length === 0 ? (
                    <Form.Item
                      name="radioGroup"
                      label={t("fields_management.field_type_label")}
                      rules={[
                        {
                          required: true,
                          message: t("fields_management.fieldTypeError"),
                        },
                      ]}
                      style={{ position: "relative" }}
                    >
                      <Row gutter={16}>
                        {types &&
                          types?.map((element, index) => (
                            <Col
                              key={`field_type_${index}`}
                              className="gutter-row"
                              span={6}
                              style={{
                                padding: "8px 0",
                              }}
                            >
                              <Radio.Button
                                size="large"
                                style={{
                                  display: "flex",
                                  alignItems: "center",
                                  justifyContent: "center",
                                  height: "50px",
                                  width: "100px",
                                  padding: "8px 0",
                                }}
                                value={element?.id?.toString()}
                              >
                                <p className="relative flex flex-col-reverse items-center">
                                  {element?.fieldType}
                                  {displayRightIcon(element?.fieldType, 4, 4)}
                                </p>
                              </Radio.Button>
                            </Col>
                          ))}
                      </Row>
                    </Form.Item>
                  ) : (
                    <>
                      {/* The create field after selecting the field type */}
                      {Object.keys(updateFieldProps)?.length === 0 && (
                        <Form.Item
                          label={t("fields_management.field_type_label")}
                          name="radioGroup"
                          rules={[
                            {
                              required: true,
                              message: t("fields_management.fieldTypeError"),
                            },
                          ]}
                        >
                          <Select
                            showSearch
                            options={selectTypeOptions}
                            onSearch={(value) => {
                              selectTypeOptions.filter((el) =>
                                (el?.typeName ?? "")
                                  ?.toLowerCase()
                                  .includes(value?.toLowerCase())
                              );
                            }}
                            optionFilterProp="typeName"
                          />
                        </Form.Item>
                      )}
                      {isFieldOfTypeCountry(
                        Object.keys(updateFieldProps).length === 0
                          ? mem
                          : updateFieldProps?.field_type_id
                      ) && (
                        <Row>
                          <Form.Item
                            name="multipleCheckbox"
                            valuePropName="checked"
                          >
                            <Checkbox
                              value="multiple"
                              style={{
                                lineHeight: "32px",
                              }}
                              onChange={(e) =>
                                setSelectMultipleCountries(e?.target?.checked)
                              }
                              disabled={
                                Object.keys(updateFieldProps).length > 0
                              }
                            >
                              {t("fields_management.multiple")}
                            </Checkbox>
                          </Form.Item>
                          <Tooltip title={t("fields_management.multipleInfo")}>
                            <InfoCircleOutlined
                              className="hover:cursor-help"
                              style={{
                                color: "rgba(0, 0, 0, 0.45)",
                                marginBottom: "10px",
                              }}
                            />
                          </Tooltip>
                        </Row>
                      )}
                      <Form.Item
                        label={t("fields_management.field_alias")}
                        name="alias"
                        rules={[
                          {
                            required: true,
                            message: t("fields_management.fieldLabelError"),
                          },
                        ]}
                      >
                        <Input
                          placeholder={t("fields_management.field_alias")}
                          allowClear
                          ref={aliasInputRef}
                        />
                      </Form.Item>
                      <Form.Item
                        label={t("fields_management.field_label")}
                        name="label"
                        rules={[
                          {
                            transform: (val) => val.trim().replaceAll(" ", "_"),
                          },
                        ]}
                      >
                        <Input
                          placeholder={t("fields_management.field_label")}
                          allowClear
                          onPaste={(e) => {
                            e?.preventDefault();
                            return false;
                          }}
                          disabled={
                            Object.keys(updateFieldProps).length !== 0 &&
                            updateFieldProps?.default === 1
                          }
                        />
                      </Form.Item>
                      {/* When selected type is from the date/time format */}
                      {(isFieldOfTypeDate(updateFieldProps?.field_type_id) ||
                        isFieldOfTypeDate(mem)) && (
                        <Form.Item
                          name="dateFormat"
                          label={t("fields_management.dateFormatLabel")}
                          tooltip={{
                            title: t("fields_management.dateFormatInfo"),
                            icon: <InfoCircleOutlined />,
                          }}
                        >
                          <Select
                            showSearch
                            filterOption={["label"]}
                            options={optionsDateFormat(t)?.map((option) => ({
                              label: option?.label,
                              value: option?.value,
                            }))}
                            placeholder={t(
                              "fields_management.dateFormatPlaceholder"
                            )}
                          />
                        </Form.Item>
                      )}
                      <Form.Item
                        label={t("fields_management.placeholderField")}
                        name="placeholder"
                      >
                        <Input placeholder="Ex placeholder...." allowClear />
                      </Form.Item>
                      <Form.Item
                        label={t("fields_management.fieldDescription")}
                        name="fieldDescription"
                      >
                        <Input.TextArea
                          showCount
                          maxLength={200}
                          rows={3}
                          placeholder="Description..."
                          allowClear
                        />
                      </Form.Item>
                      <Form.Item
                        label={t("fields_management.associatedGroup")}
                        name="fieldGroup"
                        rules={[
                          {
                            required: true,
                            message: t("fields_management.selectGroupErrorMsg"),
                          },
                        ]}
                      >
                        <Select
                          disabled={updateFieldProps?.default === 1}
                          options={FieldsGroupsOptions}
                          style={{
                            width: "100%",
                          }}
                          placeholder="Select a group"
                        />
                      </Form.Item>
                      <Form.Item
                        name="display"
                        label={t("fields_management.display")}
                        tooltip={{
                          title: t("fields_management.displayModelInfo"),
                          icon: <InfoCircleOutlined />,
                        }}
                      >
                        <Select
                          mode="multiple"
                          optionLabelProp="option"
                          optionFilterProp="option"
                          maxTagCount="responsive"
                          style={{
                            width: "100%",
                          }}
                          placeholder={t("fields_management.display")}
                          options={displayParamData}
                          menuItemSelectedIcon={null}
                        />
                      </Form.Item>
                      {/* Field parameters section */}
                      <Form.Item name="checkboxGroup">
                        <Checkbox.Group
                          style={{ display: "flex", flexDirection: "column" }}
                        >
                          {checkboxConfig(
                            isAvatarUsed,
                            updateFieldProps,
                            mem,
                            t
                          )
                            ?.filter((item) => item.condition !== false)
                            ?.map((item) => (
                              <Row key={item.value}>
                                <Checkbox
                                  value={item.value}
                                  style={{ lineHeight: "32px" }}
                                  disabled={item.disabled}
                                >
                                  {item.label}
                                  {item.tooltip && (
                                    <Tooltip title={item.tooltip}>
                                      <InfoCircleOutlined
                                        style={{ color: "#64748b" }}
                                      />
                                    </Tooltip>
                                  )}
                                </Checkbox>
                              </Row>
                            ))}
                        </Checkbox.Group>
                      </Form.Item>
                      {/* Options table goes here. (Update drawer)*/}
                      {Object.keys(updateFieldProps).length !== 0 &&
                      types
                        .filter((element) => element?.list === true)
                        .filter(
                          (el) => el?.id == updateFieldProps?.field_type_id
                        ).length > 0 ? (
                        <>
                          <Col
                            style={{
                              float:
                                !FielTypeIsCountry(
                                  updateFieldProps?.field_type_id,
                                  types
                                ) &&
                                !isModule(
                                  updateFieldProps?.field_type_id,
                                  types
                                ) &&
                                "right",
                            }}
                          >
                            <Row>
                              <Form.Item
                                help={
                                  (FielTypeIsCountry(
                                    updateFieldProps?.field_type_id,
                                    types
                                  ) ||
                                    isModule(
                                      updateFieldProps?.field_type_id,
                                      types
                                    )) &&
                                  t("fields_management.redirectBtnHelpLine", {
                                    dept: FielTypeIsCountry(
                                      updateFieldProps?.field_type_id,
                                      types
                                    )
                                      ? t("fields_management.country")
                                      : modulesList &&
                                        modulesList.find(
                                          (element) =>
                                            element?.id ==
                                            updateFieldProps?.field_module
                                        )?.label,
                                  })
                                }
                              >
                                <Button
                                  onClick={() => {
                                    if (
                                      isModule(
                                        updateFieldProps?.field_type_id,
                                        types
                                      )
                                    ) {
                                      window.open(
                                        `/settings/general/${
                                          modulesList &&
                                          modulesList.find(
                                            (element) =>
                                              element?.id ==
                                              updateFieldProps?.field_module
                                          )?.label
                                        }`,
                                        "_blank"
                                      );
                                      dispatch(setOpenFieldDrawer(false));
                                    } else if (
                                      FielTypeIsCountry(
                                        updateFieldProps?.field_type_id,
                                        types
                                      )
                                    ) {
                                      navigate(`/settings/general/countries`);
                                      dispatch(setOpenFieldDrawer(false));
                                    } else {
                                      handleAdd();
                                    }
                                  }}
                                  type="primary"
                                  ref={ref1}
                                  icon={
                                    isModule(
                                      updateFieldProps?.field_type_id,
                                      types
                                    ) ||
                                    FielTypeIsCountry(
                                      updateFieldProps?.field_type_id,
                                      types
                                    ) ? (
                                      <SwapOutlined />
                                    ) : (
                                      <PlusOutlined />
                                    )
                                  }
                                  loading={loadModuleList}
                                >
                                  {isModule(
                                    updateFieldProps?.field_type_id,
                                    types
                                  )
                                    ? t(
                                        "fields_management.moduleFieldRedirectBtn",
                                        {
                                          moduleName:
                                            modulesList &&
                                            modulesList.find(
                                              (element) =>
                                                element?.id ==
                                                updateFieldProps?.field_module
                                            )?.label,
                                        }
                                      )
                                    : FielTypeIsCountry(
                                        updateFieldProps?.field_type_id,
                                        types
                                      )
                                    ? t("fields_management.redirectToCountries")
                                    : t("fields_management.opt_table_btn")}
                                </Button>
                              </Form.Item>
                            </Row>
                          </Col>
                          {/* Options table on update field */}
                          {!isModule(
                            updateFieldProps?.field_type_id,
                            types
                          ) && (
                            <Typography.Title level={4} underline>
                              {t("fields_management.optionsTable")}
                            </Typography.Title>
                          )}
                          {!isModule(updateFieldProps?.field_type_id, types) &&
                            !FielTypeIsCountry(
                              updateFieldProps?.field_type_id,
                              types
                            ) && (
                              <DndContext onDragEnd={onDragEnd}>
                                <SortableContext
                                  items={
                                    dataSource &&
                                    dataSource.map((element) => element?.id)
                                  }
                                  strategy={verticalListSortingStrategy}
                                >
                                  <Table
                                    components={{
                                      body: {
                                        row: TableRow,
                                        cell: EditableCell,
                                      },
                                    }}
                                    rowClassName={rowClassName}
                                    bordered={false}
                                    dataSource={dataSource}
                                    columns={columns}
                                    pagination={false}
                                    size="small"
                                    rowKey={(record) => record?.id}
                                    onRow={(_, index) => {
                                      const attr = {
                                        index,
                                        moveRow,
                                      };
                                      return attr;
                                    }}
                                    scroll={{
                                      x: "max-content",
                                      y: isModule(
                                        updateFieldProps?.field_type_id,
                                        types
                                      )
                                        ? 200
                                        : 250,
                                    }}
                                    loading={
                                      updateFieldOptionRankLoading ||
                                      deleteFieldOptLoading
                                    }
                                  />
                                </SortableContext>
                              </DndContext>
                            )}
                        </>
                      ) : null}
                    </>
                  )}
                </div>
              </div>
            </div>
            {/* Create field with options, options table (Create drawer)*/}
            {(showOptionsInput(mem, types) &&//whats mean this condition
              Object.keys(updateFieldProps).length === 0 &&
              !isModule(mem, types)) ||
            (isModule(mem, types) && selectedModule !== "") ? (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{
                  opacity: showOptionsInput(mem, types) ? 1 : 0,
                  y: showOptionsInput(mem, types) ? 0 : -20,
                  transition: { duration: 0.2, ease: "easeInOut" },
                }}
                className=" px-5"
              >
                <Row style={{ width: "100%", justifyContent: "space-between" }}>
                  <Typography.Title level={4} underline>
                    {t("fields_management.optionsTable")}
                  </Typography.Title>
                  <Button
                    onClick={() => {
                      if (isModule(mem, types)) {
                        navigate(`/settings/general/${selectedModule}`);
                        dispatch(setOpenFieldDrawer(false));
                      } else {
                        handleAdd();
                      }
                    }}
                    type="primary"
                    style={{
                      marginBottom: 16,
                      float: "right",
                    }}
                    ref={ref1}
                    icon={
                      isModule(mem, types) ? <SwapOutlined /> : <PlusOutlined />
                    }
                    disabled={selectedModule == undefined}
                  >
                    {isModule(mem, types)
                      ? `Go to ${selectedModule} settings`
                      : t("fields_management.opt_table_btn")}
                  </Button>
                </Row>
                {/* Options table on create multi-options field */}
                {!(isModule(mem, types) && selectedModule !== "") && (
                  <Table
                    components={components}
                    rowClassName={() => "editable-row"}
                    bordered={false}
                    dataSource={dataSource}
                    columns={columns}
                    pagination={false}
                    onRow={(_, index) => {
                      const attr = {
                        index,
                        moveRow,
                      };
                      return attr;
                    }}
                    size="small"
                    scroll={{
                      x: "max-content",
                      y: isModule(updateFieldProps?.field_type_id, types)
                        ? 200
                        : 250,
                    }}
                  />
                )}
              </motion.div>
            ) : null}
          </Form>
        </Drawer>
      </div>
    </>
  );
};

export default FieldsSettingArea;
