import React, { useEffect, useRef } from "react";
import {
  Form,
  Input,
  Button,
  Space,
  Select,
  Typography,
  Badge,
  Tag,
  Switch,
  Tooltip,
} from "antd";
import { useState } from "react";
import { generateAxios } from "../services/axiosInstance";
import { useTranslation } from "react-i18next";
import { toastNotification } from "./ToastNotification";
import Header from "./configurationHelpDesk/Header";
import NewTableDraggable from "./NewTableDraggable";
import { setSearch } from "../new-redux/actions/menu.actions/menu";
import { useDispatch, useSelector } from "react-redux";
import LabelTable from "./LabelTable";
import BottomButtonAddRow from "./BottomButtonAddRow";
import { SubmitKeyPress } from "../utils/SubmitKeyPress";
import ChoiceIcons from "../pages/components/ChoiceIcons";
import ColumnColors from "./ColumnColors";
import { colors } from "./Colors";
import { allIcons } from "./Icons";
import { URL_ENV } from "index";
import { AiOutlineInfoCircle } from "react-icons/ai";

const Folders = () => {
  const [form] = Form.useForm();
  const [, setCount] = useState(0);

  const [data, setData] = useState([]);
  const [editingKey, setEditingKey] = useState("");
  const [id, setId] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [oldPage, setOldPage] = useState(1);
  const [loading, setLoading] = useState(true);
  const [pageSize, setPageSize] = useState(20);
  const isEditing = (record) => record.key === editingKey;
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const { search } = useSelector((state) => state.form);
  const { families } = useSelector((state) => state.families);
  const inputRefs = useRef([]);
  useEffect(() => {
    inputRefs.current.forEach((input) => {
      input?.focus();
    });
  }, [data.length, id]);
  // useEffect(() => {
  //   if (editingKey && oldPage !== currentPage) {
  //     setData((prev) => prev.filter((el) => el.id));
  //     setEditingKey("");
  //   }
  // }, [currentPage, editingKey, oldPage]);
  const onFinishFailed = (values) => {
    console.log(values);
  };
  const EditableCell = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    const inputNode =
      inputType === "switch" ? (
        <>
          <Switch
            size="small"
            defaultChecked={
              !loading
                ? record[dataIndex] == 1
                  ? true
                  : false
                : form.getFieldsValue()[dataIndex] == 0
                ? false
                : true
            }
          />
        </>
      ) : inputType === "select" ? (
        <Select
          placeholder={t("tags.selecticon")}
          showSearch
          style={{
            minWidth: 100,
          }}
          options={allIcons.map((el) => ({
            label: (
              <Space className="px-1">
                <Typography.Text>{el.label}</Typography.Text>
                <Typography.Text type="secondary">
                  {el.value.replaceAll("Outlined", "")}{" "}
                </Typography.Text>
              </Space>
            ),
            value: el.value,
          }))}
          optionFilterProp="children"
          filterOption={(input, option) =>
            (option?.value.toLowerCase() ?? "").includes(input.toLowerCase())
          }
          filterSort={(optionA, optionB) =>
            (optionA?.value ?? "")
              .toLowerCase()
              .localeCompare((optionB?.value ?? "").toLowerCase())
          }
          allowClear
        />
      ) : inputType === "categories" ? (
        <Select
          // mode="multiple"
          placeholder={t("tags.selectFamily")}
          showSearch
          style={{
            minWidth: 100,
          }}
          options={families.map((el) => ({
            label: t(`fields_management.${el?.label.toLowerCase()}`).includes(
              "fields_management"
            )
              ? el?.label
              : t(`fields_management.${el?.label.toLowerCase()}`),
            value: el.id,
          }))}
          optionFilterProp="children"
          filterOption={(input, option) =>
            (option?.label.toLowerCase() ?? "").includes(input.toLowerCase())
          }
          // filterSort={(optionA, optionB) =>
          //   (optionA?.value ?? "")
          //     .toLowerCase()
          //     .localeCompare((optionB?.value ?? "").toLowerCase())
          // }
          allowClear
        />
      ) : inputType === "radio" ? (
        <Select
          showSearch
          placeholder={t("tags.selectcolor")}
          style={{
            minWidth: 100,
          }}
          options={colors.map((el) => ({
            label: (
              <Space>
                <Badge color={el.value} /> {t(`colors.${el.label}`)}
              </Space>
            ),
            value: el.value,
            label2: el.label,
          }))}
          optionFilterProp="children"
          filterOption={(input, option) => {
            var _a;
            return (
              (_a =
                option === null || option === void 0
                  ? void 0
                  : t(`colors.${option.label2}`)) !== null && _a !== void 0
                ? _a
                : ""
            )
              .toLowerCase()
              .includes(input.toLowerCase());
          }}
          allowClear
        />
      ) : (
        <Input
          ref={(el) => (inputRefs.current[index] = el)}
          onKeyPress={handleKeyPress}
          placeholder={t("activities.name")}
        />
      );
    return (
      <td {...restProps}>
        {editing ? (
          <Form.Item
            name={dataIndex.toLowerCase()}
            style={{
              margin: 0,
            }}
            rules={[
              {
                required: true,
                message: `${title} ${t("table.header.isrequired")}`,
              },
            ]}
          >
            {inputNode}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };
  const edit = (record) => {
    if (record) {
      form.setFieldsValue({
        label: record.label,
        icon: record.icon,
        color: record.color,
        family_id: record.family_id,
        hidden: record.hidden,
        status: record.status,
      });
      setId(record.id);
    } else {
      form.setFieldsValue({
        label: "",
        icon: "",
        color: "",
        family_id: null,
      });
    }
    setEditingKey(record.key);
  };
  const cancel = (record) => {
    setEditingKey("");
    setId(null);
    if (!record.id) {
      setData(data.filter((item) => item.key !== record.key));
    }
  };
  const save = async (key) => {
    setLoading(true);
    if (id) {
      try {
        const row = await form.validateFields();
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).put(
          `/folders/${id}`,
          { ...row, status: row.status ? 1 : 0, hidden: row.hidden ? 1 : 0 },
          {
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
            },
          }
        );
        setEditingKey("");
        setData(
          data.map((el) =>
            el.id === res.data.data.id
              ? {
                  ...res.data.data,
                  key: res.data.data.id,
                }
              : el
          )
        );
        form.setFieldsValue({
          label: "",
          icon: "",
          color: "",
          family_id: null,
        });
        setLoading(false);

        toastNotification(
          "success",
          res?.data?.data?.label + t("toasts.edit"),
          "topRight"
        );
      } catch (errInfo) {
        setLoading(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    } else {
      try {
        const row = await form.validateFields();
        const res = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).post("/folders", {
          ...row,
          status: row.status ? 1 : 0,
          hidden: row.hidden ? 1 : 0,
        });
        setData([
          ...data.filter((el) => el.id),
          { ...res.data.data[0], children: "" },
        ]);
        form.setFieldsValue({
          label: "",
          icon: null,
          color: null,
          family_id: null,
          hidden: 0,
          status: 0,
        });
        setLoading(false);
        setEditingKey("");
        toastNotification(
          "success",
          row.label + t("toasts.created"),
          "topRight"
        );
      } catch (errInfo) {
        setLoading(false);

        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    }
  };
  useEffect(() => {
    const getFolders = async () => {
      setLoading(true);
      try {
        const {
          data: { data },
        } = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get("/folders");
        setData(data.map((el, i) => ({ ...el, key: el.id, rank: i + 1 })));
        if (data.length > 0) {
          setCount(Math.max(...data.map((el) => el.id)));
        }
        setLoading(false);
      } catch (err) {
        setLoading(false);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    };
    getFolders();
    return () => dispatch(setSearch(""));
  }, []);

  const handleKeyPress = (event) => {
    SubmitKeyPress(event, form);
  };

  const UpdateSwitch = async (record, status, type) => {
    let Obj = {};
    setLoading(true);
    try {
      if (type === "hidden") {
        Obj = { hidden: status ? 1 : 0 };
      } else if (type === "status") {
        Obj = { status: status ? 1 : 0 };
      }

      const res = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).put(
        `/folders/${record.id}`,

        Obj,
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      );
      setEditingKey("");
      setData(
        data.map((el) =>
          el.id === res.data.data.id
            ? {
                ...res.data.data,
                key: res.data.data.id,
              }
            : el
        )
      );
      setLoading(false);

      // setLoadingSwitch(false);
      toastNotification("success", record.label + t("toasts.edit"), "topRight");
    } catch (errInfo) {
      setLoading(false);

      // setLoadingSwitch(false);

      toastNotification("error", t("toasts.somethingWrong"), "topRight");
    }
  };

  const columns = [
    {
      title: t("activities.name"),
      dataIndex: "label",
      key: "label",
      width: 200,
      editable:
        data.find((el) => el.id === editingKey)?.seeder === 1 ? false : true,
      sorter: (a, b) => a.label.localeCompare(b.label),
      render: (_, record) => {
        return (
          <div
            className={`${
              data.find((el) => el.id === editingKey)?.seeder === 1
                ? "absolute top-1/2 -translate-y-1/2 transform"
                : "relative "
            }`}
          >
            <LabelTable
              record={record}
              editingKey={editingKey}
              edit={edit}
              width={190}
            />
          </div>
        );
      },
    },
    {
      title: t("tags.color"),
      dataIndex: "color",
      key: "color",
      editable: true,
      render: (_, { color }) => <ColumnColors color={color} colors={colors} />,
    },
    {
      title: t("tags.icon"),
      dataIndex: "icon",
      key: "icon",
      editable: true,
      width: "150px",

      render: (_, { icon }) => <ChoiceIcons icon={icon} fontSize={16} />,
    },
    {
      title: t("import.family"),
      dataIndex: "family_id",
      key: "family_id",
      editable:
        data.find((el) => el.id === editingKey)?.seeder === 1 ? false : true,
      render: (_, { family_id }) => (
        <Space size={[0, 8]} wrap>
          {
            <div
              className={`${
                data.find((el) => el.id === editingKey)?.seeder === 1
                  ? "absolute top-1/2 -translate-y-1/2 transform"
                  : "relative "
              }`}
            >
              {family_id && families.find((el) => el.id == family_id)?.label}
            </div>

            // .filter((x) => family?.indexOf(x.id) > -1)
            // .map((item, i) => <Tag key={i}>{item?.label} </Tag>)
          }
        </Space>
      ),
    },
    {
      title: (
        <div>
          {t("table.header.hidden")}

          <Tooltip title="Liste des dossiers a afficher dans la zone recherche">
            <AiOutlineInfoCircle />
          </Tooltip>
        </div>
      ),
      key: "hidden",
      dataIndex: "hidden",
      editable: true,
      filters: [
        {
          text: t(`helpDesk.actif`),
          value: "1",
        },
        {
          text: t(`helpDesk.noActif`),
          value: "0",
        },
      ],
      onFilter: (value, record) => record.hidden == value,
      sorter: (a, b) => a.hidden - b.hidden,

      render: (_, record) => (
        <>
          <Switch
            // loading={loadingSwitch}
            size="small"
            checked={record.hidden == 1 ? true : false}
            onChange={(e) => UpdateSwitch(record, e, "hidden")}
          />
        </>
      ),
    },
    {
      title: (
        <div>
          {t("helpDesk.status")}
          <Tooltip title="Liste des dossiers a afficher dans le filtre">
            <AiOutlineInfoCircle />
          </Tooltip>
        </div>
      ),
      key: "status",
      dataIndex: "status",
      editable: true,
      filters: [
        {
          text: t(`helpDesk.actif`),
          value: "1",
        },
        {
          text: t(`helpDesk.noActif`),
          value: "0",
        },
      ],
      onFilter: (value, record) => record.status == value,
      sorter: (a, b) => a.status - b.status,

      render: (_, record) => (
        <>
          <Switch
            // loading={loadingSwitch}
            size="small"
            checked={record.status == 1 ? true : false}
            onChange={(e) => UpdateSwitch(record, e, "status")}
          />
        </>
      ),
    },
  ];

  const handleAdd = () => {
    const ids = data.map((object) => {
      return object.id;
    });
    setId(null);
    const newData = {
      key: Math.max(...ids) + 1,
      label: `  `,
      color: "",
      disabled: true,
    };
    setCurrentPage(Math.ceil([...data, newData].length / pageSize));
    setOldPage(Math.ceil([...data, newData].length / pageSize));
    setData([...data, newData]);
    form.setFieldsValue({
      label: "",
      color: null,
      icon: null,
      family_id: null,
      status: false,
      hidden: false,
    });
    setEditingKey(Math.max(...ids) + 1);
    setCount(Math.max(...ids) + 1);
  };

  //do not delete this line
  const onRow = () => {};

  const filteredData = data.filter((item) => {
    return (
      item?.label?.toLowerCase().includes(search.toLowerCase()) ||
      families
        ?.find((el) => el.id === item.family_id)
        ?.label?.toLowerCase()
        .includes(search.toLowerCase())
    );
  });
  return (
    <Space direction="vertical" style={{ width: "100%" }}>
      <div className="pt-4">
        <Header
          active={"5"}
          editingKey={editingKey}
          handleAdd={handleAdd}
          btnText={t("helpDesk.addFolder")}
          disabled={loading ? true : editingKey ? true : search ? true : false}
        />
      </div>

      <NewTableDraggable
        columns={columns}
        setLoading={setLoading}
        isEditing={isEditing}
        data={filteredData}
        setData={setData}
        loading={loading}
        save={save}
        edit={edit}
        EditableCell={EditableCell}
        onFinishFailed={onFinishFailed}
        cancel={cancel}
        form={form}
        apiRank="/rank-folders"
        editingKey={editingKey}
        api="folders"
        onRow={onRow}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        pageSize={pageSize}
        setPageSize={setPageSize}
      />

      <BottomButtonAddRow
        editingKey={editingKey}
        data={filteredData}
        text={t("helpDesk.addFolder")}
        handleAdd={handleAdd}
        loading={loading}
        search={search || ""}
      />
    </Space>
  );
};
export default Folders;
