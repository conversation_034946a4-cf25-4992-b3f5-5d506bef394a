import React from "react";
import {
  UserOutlined,
  SearchOutlined,
  LoadingOutlined,
  PhoneFilled,
  TeamOutlined,
  PauseOutlined,
  PlusOutlined,
  AudioOutlined,
  SwapOutlined,
  HolderOutlined,
  CaretRightOutlined,
} from "@ant-design/icons";
import { Avatar, Col, Divider, Row, Button, Tooltip } from "antd";

const InCall = () => {
  return (
    <>
      <div className="flex w-full flex-col items-center justify-center pb-3 h-64">
        <Avatar size={72} icon={<UserOutlined />} />
        <div className="text-sm font-medium mt-1">98 165 002</div>
        <div className="text-xs text-slate-400">
          Sonnerie en cours ... <LoadingOutlined />
        </div>
        <div className="px-0 my-4 mt-8">
          <Row gutter={[6, 8]}>
            <Col className="gutter-row" span={4}>
              <div className="flex flex-col w-full items-center  text-slate-400 text-xs">
                <Tooltip title="Pause">
                  <Button
                    icon={<PauseOutlined />}
                    size="middle"
                    className="text-slate-400"
                  />
                </Tooltip>
              </div>
            </Col>
            <Col className="gutter-row" span={4}>
              <div className="flex flex-col w-full items-center text-slate-400 text-xs">
                <Tooltip title="Appel">
                  <Button
                    icon={<PlusOutlined />}
                    size="middle"
                    className="text-slate-400"
                  />
                </Tooltip>
              </div>
            </Col>
            <Col className="gutter-row" span={4}>
              <div className="flex flex-col w-full items-center text-slate-400 text-xs">
                <Tooltip title="Silence">
                  <Button
                    icon={<AudioOutlined />}
                    size="middle"
                    className="text-slate-400"
                  />
                </Tooltip>
              </div>
            </Col>
            <Col className="gutter-row" span={4}>
              <div className="flex flex-col w-full items-center text-slate-400 text-xs">
                <Tooltip title="Transfert">
                  <Button
                    icon={<SwapOutlined />}
                    size="middle"
                    className="text-slate-400"
                  />
                </Tooltip>
              </div>
            </Col>
            <Col className="gutter-row" span={4}>
              <div className="flex flex-col w-full items-center text-slate-400 text-xs">
                <Tooltip title="DMF">
                  <Button
                    icon={<HolderOutlined />}
                    size="middle"
                    className="text-slate-400"
                  />
                </Tooltip>
              </div>
            </Col>
            <Col className="gutter-row" span={4}>
              <div className="flex flex-col w-full items-center text-slate-400 text-xs">
                <Tooltip title="Conférence">
                  <Button
                    icon={<TeamOutlined />}
                    size="middle"
                    className="text-slate-400"
                  />
                </Tooltip>
              </div>
            </Col>
          </Row>
        </div>
        <div>
          <Button
            type="primary"
            shape="circle"
            size="middle"
            danger
            icon={<PhoneFilled />}
          />
        </div>
      </div>
    </>
  );
};

export default InCall;
