import React from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";

const AreaChart = ({ pivotData }) => {
  if (
    !pivotData ||
    !pivotData.rowKeys ||
    !pivotData.colKeys ||
    !pivotData.getAggregator
  ) {
    return <p className="text-center text-red-600">No Data</p>;
  }

  const rowKeys = pivotData.rowKeys;
  const colKeys = pivotData.colKeys;

  if (rowKeys.length === 0 && colKeys.length === 0) {
    return <p className="text-center text-red-600">No Data</p>;
  }
  const isRow = rowKeys.length > 0;
  const baseKeys = isRow ? rowKeys : colKeys;
  const seriesKeys = isRow ? colKeys : rowKeys;
  const categories = baseKeys.map((key) => key.join(" - "));
  const series =
    seriesKeys.length > 0
      ? seriesKeys.map((seriesKey) => ({
          name: seriesKey.join(" - ") || "Total",
          data: baseKeys.map((baseKey) => {
            const r = isRow ? baseKey : seriesKey;
            const c = isRow ? seriesKey : baseKey;
            const value = pivotData.getAggregator(r, c)?.value() || 0;
            return {
              y: value,
              z: value,
            };
          }),
        }))
      : [
          {
            name: "Total",
            data: baseKeys.map((baseKey) => {
              const r = isRow ? baseKey : [];
              const c = isRow ? [] : baseKey;
              const value = pivotData.getAggregator(r, c)?.value() || 0;
              return {
                y: value,
                z: value,
              };
            }),
          },
        ];

  const options = {
    chart: {
      type: "area",
    },
    title: {
      text: `📊
            ${pivotData.props.rows.length ? pivotData.props.rows.join(" ") : ""}
            ${
              pivotData.props.cols.length
                ? " : " + pivotData.props.cols.join(" ")
                : ""
            }`,
      align: "left",
      dispalay: "block",
      fontFamily: "Arial, sans-serif",
      style: {
        fontWeight: "normal",
        fontSize: "20px",
        fontFamily: "Inter, sans-serif",
      },
    },
    xAxis: {
      categories,
      allowDecimals: false,
    },
    tooltip: {
      pointFormat:
        "{series.name} had stockpiled <b>{point.y:,.0f}</b> warheads in {point.x}",
    },
    plotOptions: {
      area: {
        stacking: "normal",
        marker: {
          enabled: false,
          symbol: "circle",
          radius: 2,
          states: {
            hover: {
              enabled: true,
            },
          },
        },
      },
    },
    credits: {
      enabled: false,
    },
    series,
  };

  return (
    <div
      id="container"
      style={{ width: "100%", height: "100%" }}
      className="highcharts-figure"
    >
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
};

export default AreaChart;
