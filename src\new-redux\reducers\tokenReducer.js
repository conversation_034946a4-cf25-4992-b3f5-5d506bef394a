import {
  CLEAR_TOKEN,
  SET_TOKEN,
  SET_REFRESH_TOKEN,
  RESET_STATE,
} from "../constants";

const initialState = {
  token: "",
  refreshToken: "",
};

const token = (state = initialState, action) => {
  const { type, payload } = action;
  switch (type) {
    case SET_TOKEN:
    case CLEAR_TOKEN:
      return {
        ...state,
        token: payload,
        refreshToken: payload,
      };
    case SET_REFRESH_TOKEN:
      return {
        ...state,
        refreshToken: payload,
      };
    case RESET_STATE: {
      return initialState;
    }
    default:
      return state;
  }
};

export default token;
