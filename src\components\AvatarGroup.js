import { memo, Fragment } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, But<PERSON> } from "antd";
import { CloseCircleFilled } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { useLocation } from "react-router-dom";

import { getName } from "../pages/layouts/chat/utils/ConversationUtils";
import { AvatarChat } from "./Chat";
import { URL_ENV } from "..";
import { uncheckGuest } from "pages/tasks/helpers/handleCheck";
import { EXTENSIONS_ARRAY } from "pages/tasks/helpers/calculateSum";
import ActionsComponent from "pages/tasks/ActionsComponent";

const AvatarGroup = memo(function AvatarGroup({
  usersArray,
  disableDelete,
  source,
  setCheckedItems,
  dispatchRemoveMembers = () => {},
  role = null,
  roleInActivity = null,
  from = null,
}) {
  const [t] = useTranslation("common");
  const location = useLocation();

  let shownAvatars = usersArray?.filter((_, i) =>
    source === "taskTable" || source === "elementKanban" ? i < 2 : i < 12
  );

  let excessAvatars = usersArray?.filter((_, i) =>
    source === "taskTable" || source === "elementKanban" ? i >= 2 : i >= 12
  );

  // Display family name under the label in participants list.
  const displayFamilyName = (familyName) => {
    if (familyName === "User") {
      return t("tasks.userFamily");
    } else if (familyName === "Organisation") {
      return t("tasks.orgFamily");
    } else if (familyName === "Contact") {
      return t("tasks.contactFamily");
    } else if (familyName === "Leads") {
      return t("contacts.lead");
    }
  };

  const ExcessAvatars = () => (
    <List
      style={{
        height: "100%",
        maxHeight: 400,
        width: 270,
        overflow: "auto",
      }}
      size="small"
      bordered={false}
      dataSource={excessAvatars}
      renderItem={(item) => (
        <List.Item
          key={item?.id}
          style={{
            padding: 0,
            height: 50,
          }}
        >
          <List.Item.Meta
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
            avatar={
              <ActionsComponent
                elementValue={item}
                tooltipText={null}
                showDropdown={!from}
              >
                <AvatarChat
                  fontSize="0.7rem"
                  className="mx-1.5"
                  size={34}
                  height={14}
                  width={14}
                  url={`${
                    URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                  }${item?.avatar}`}
                  hasImage={EXTENSIONS_ARRAY?.includes(
                    item?.avatar?.split(".")?.pop()
                  )}
                  name={getName(item?.label, "avatar")}
                  type="user"
                />
              </ActionsComponent>
            }
            title={`${item?.label}`}
            description={displayFamilyName(item?.family_name)}
          />
          {disableDelete ? null : (
            <Button
              type="text"
              shape="circle"
              icon={<CloseCircleFilled />}
              danger
              onClick={() => {
                dispatchRemoveMembers(role, item?.id);
                uncheckGuest(usersArray, setCheckedItems, item?.id);
              }}
            />
          )}
        </List.Item>
      )}
    />
  );

  return (
    <>
      <Avatar.Group
        size={
          source === "taskTable" || source === "elementKanban"
            ? "small"
            : "default"
        }
      >
        {shownAvatars?.map((el, i) => (
          <Fragment key={`ActionsComponent_${i}`}>
            <ActionsComponent
              elementValue={el}
              tooltipText={
                <>
                  {roleInActivity && (
                    <p className="text-[10px] underline">
                      {roleInActivity === "participants"
                        ? t("tasks.guests", {
                            s: "",
                          })
                        : t("tasks.followers", {
                            s: "",
                          })}
                    </p>
                  )}
                  <div className="flex flex-col items-center">
                    <p>{getName(el?.label, "name")}</p>
                    {["/tasks", "/visio"]?.includes(location?.pathname) && (
                      <span>({displayFamilyName(el?.family_name)})</span>
                    )}
                  </div>
                </>
              }
            >
              <Badge
                count={
                  disableDelete ? null : (
                    <CloseCircleFilled
                      style={{
                        color: "red",
                        right: "10px",
                      }}
                      onClick={(e) => {
                        e && e.stopPropagation();
                        e && e.preventDefault();
                        dispatchRemoveMembers(role, el?.id);
                        uncheckGuest(usersArray, setCheckedItems, el?.id);
                      }}
                      disabled={disableDelete}
                    />
                  )
                }
              >
                <AvatarChat
                  fontSize={
                    source !== "taskTable" && source !== "elementKanban"
                      ? "0.875rem"
                      : "0.7rem"
                  }
                  className={
                    source !== "taskTable"
                      ? ""
                      : "flex items-center justify-center"
                  }
                  size={
                    source === "elementKanban"
                      ? "small"
                      : source !== "taskTable" && 42
                  }
                  height={
                    source === "elementKanban"
                      ? "32px"
                      : source !== "taskTable"
                      ? 14
                      : "32px"
                  }
                  width={
                    source === "elementKanban"
                      ? "32px"
                      : source !== "taskTable"
                      ? 14
                      : "32px"
                  }
                  url={`${
                    URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                  }${el?.avatar}`}
                  hasImage={EXTENSIONS_ARRAY?.includes(
                    el?.avatar?.split(".")?.pop()
                  )}
                  name={getName(el?.label, "avatar")}
                  type="user"
                />
              </Badge>
            </ActionsComponent>
          </Fragment>
        ))}
        {(usersArray?.length > 12 ||
          (["taskTable", "elementKanban"].includes(source) &&
            usersArray?.length > 2)) && (
          <Popover
            trigger={["hover"]}
            content={ExcessAvatars}
            overlayStyle={{ zIndex: "99999" }}
          >
            <Avatar
              className={`text-center text-xs`}
              shape="circle"
              size={
                source === "elementKanban"
                  ? "small"
                  : source !== "taskTable" && 42
              }
              alt={"item_avatar"}
              style={
                source !== "taskTable" && source !== "elementKanban"
                  ? {
                      cursor: "pointer",
                      backgroundColor: "#dbeafe",
                    }
                  : {
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      backgroundColor: "#fde3cf",
                    }
              }
            >
              <span
                style={
                  source !== "taskTable" && source !== "elementKanban"
                    ? { fontSize: "1rem", fontWeight: "500", color: "#1e40af" }
                    : { fontSize: "0.7rem", color: "#f56a00" }
                }
              >{`+${excessAvatars?.length}`}</span>
            </Avatar>
          </Popover>
        )}
      </Avatar.Group>
    </>
  );
});

export default AvatarGroup;
