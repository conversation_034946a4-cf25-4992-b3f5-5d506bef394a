import { NEW_NOTIF, OPEN_VISIO_RMC, VISIO_RMC_ELEMENT_ID } from "new-redux/constants";

const initialState = {
  family_id: null,
  openCreateForm: false,
  openCreateTask: false,
  createFormData: null,
  notif: false,
  openVisioRmc: false,
  rmcElementId: null,
};

const rmc = (state = initialState, action) => {
  const { type, payload } = action;
  switch (type) {
    case "RMC_OPEN_CREATE_FORM":
      return {
        ...state,
        family_id: payload?.family_id,
        openCreateForm: true,
        createFormData: JSON.parse(payload?.data),
      };
    case "RMC_OPEN_CREATE_TASK":
      return {
        ...state,
        openCreateTask: true,
      };
    case "RMC_RESET_STATE_CREATE_FORM":
      return {
        ...state,
        family_id: null,
        openCreateForm: false,
        createFormData: null,
      };
    case "RMC_RESET_STATE_CREATE_TASK":
      return {
        ...state,
        openCreateTask: false,
      };
    case NEW_NOTIF:
      return {
        ...state,
        notif: payload,
      };
    case OPEN_VISIO_RMC:
      return {
        ...state,
        openVisioRmc: payload,
      };
    case VISIO_RMC_ELEMENT_ID:
      return {
        ...state,
        rmcElementId: payload,
      };
    default:
      return state;
  }
};

export default rmc;
