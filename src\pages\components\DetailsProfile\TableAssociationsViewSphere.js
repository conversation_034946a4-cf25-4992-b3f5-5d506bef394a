import { Button, Input, Space, Table, Typography } from "antd";
import { AvatarChat } from "components/Chat";
import { URL_ENV } from "index";
import { getName } from "pages/layouts/chat/utils/ConversationUtils";
import React, { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import {
  DropDownActionViewSphere,
  formattingNumPhones,
  handleDispatchActions,
} from "./GridView";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import { CustomTag } from "pages/clients&users/components/RenderColumnsTable";
import { FiPhoneForwarded } from "react-icons/fi";
import { SearchOutlined } from "@ant-design/icons";
import { familyIcons } from "./ViewSphere2";
import DisplayAvatar from "pages/voip/components/DisplayAvatar";

const TableAssociationsViewSphere = ({
  item,
  contactInfo,
  setElementDetailsGridView,
  call,
  setReceiverEmail,
  copyIcon,
  setOpenDrawerUpdate,
  setOpenChat,
  setSelectedKeySideBar,
  setOpenPipeline,
}) => {
  const [t] = useTranslation("common");
  const { user } = useSelector((state) => state.user);
  const dispatch = useDispatch();
  const [filterVisible, setFilterVisible] = useState(false);
  const [filteredTable, setFilteredTable] = useState(
    item?.child?.map((el) => ({
      key: el.id,
      type: el.field,
      name: el.label_data,
      pipeline: el.pipeline_label,
      stage: el.stage_label,
      ...el,
    }))
  );
  const { openView360InDrawer } = useSelector((state) => state?.vue360);

  useEffect(() => {
    if (filterVisible) {
      searchInput?.current?.focus();
    }
  }, [filterVisible]);

  const searchInput = useRef(null);

  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState("");
  const [filteredPipelines, setFilteredPipelines] = useState(
    Array.from(
      new Set(
        item?.child
          ?.map((el) => el.pipeline_label)
          .filter((pipeline) => pipeline != null && pipeline !== "")
      )
    ).map((pipeline) => ({
      text: pipeline,
      value: pipeline,
    }))
  ); // État pour stocker les pipelines filtrés

  const handleFilterChange = (searchText) => {
    const lowerCaseSearchText = searchText.toLowerCase();

    const newFilteredPipelines = item?.child
      ?.filter((el) => {
        const nameMatch = el.name?.toLowerCase().includes(lowerCaseSearchText);
        const ownerMatch = el.owner?.name
          ?.toLowerCase()
          .includes(lowerCaseSearchText);
        return nameMatch || ownerMatch;
      })
      .map((el) => el.pipeline_label)
      .filter((pipeline) => pipeline != null && pipeline !== "");

    setFilteredPipelines(Array.from(new Set(newFilteredPipelines)));
  };
  const handleSearch = (selectedKeys, confirm, dataIndex) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
    handleFilterChange(selectedKeys[0]);
  };

  const handleReset = (clearFilters, confirm) => {
    clearFilters();
    setSearchText("");
    confirm();
  };
  const highlightText = (text, search) => {
    if (!search) return text; // Si aucun texte recherché, retourne le texte original
    const parts = text.split(new RegExp(`(${search})`, "gi"));
    return parts.map((part, index) =>
      part.toLowerCase() === search.toLowerCase() ? (
        <span key={index} style={{ backgroundColor: "yellow" }}>
          {part}
        </span>
      ) : (
        part
      )
    );
  };
  const getColumnSearchProps = (dataIndex) => ({
    placement: "top",
    filterDropdown: ({
      setSelectedKeys,
      selectedKeys,
      confirm,
      clearFilters,
      close,
      visible,
    }) => {
      setFilterVisible(visible);

      return (
        <div
          style={{
            padding: 8,
          }}
          onKeyDown={(e) => e.stopPropagation()}
        >
          <Input
            ref={searchInput}
            allowClear
            placeholder={`${t("activities.search")}...`}
            value={selectedKeys[0]}
            onChange={(e) =>
              setSelectedKeys(e.target.value ? [e.target.value] : [])
            }
            onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
            style={{
              marginBottom: 8,
              display: "flex",
            }}
          />
          <Space>
            <Button
              type="primary"
              onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
              icon={<SearchOutlined />}
              size="small"
              style={{
                width: 110,
              }}
            >
              {t("activities.search")}
            </Button>
            <Button
              onClick={() => {
                clearFilters && handleReset(clearFilters, confirm);
              }}
              size="small"
              style={{
                width: 90,
              }}
              disabled={!searchText}
            >
              {t("activities.reset")}
            </Button>

            <Button
              type="link"
              size="small"
              onClick={() => {
                close();
              }}
            >
              {t("activities.close")}
            </Button>
          </Space>
        </div>
      );
    },
    filterIcon: (filtered) => (
      <SearchOutlined
        style={{
          color: filtered ? "#1677ff" : undefined,
        }}
      />
    ),
    onFilter: (value, record) => {
      if (!value) return false; // Si aucune valeur n'est fournie, ne filtre pas

      const lowerCaseValue = value.toLowerCase();

      // Vérifier la longueur de dataIndex
      if (dataIndex.length === 1) {
        // Accès direct à la propriété
        const fieldValue = record[dataIndex[0]]; // Assurez-vous que fieldValue n'est pas null
        return fieldValue?.toString().toLowerCase().includes(lowerCaseValue);
      } else if (dataIndex.length === 2) {
        // Accès à une propriété imbriquée
        const nestedValue = record[dataIndex[0]]?.[dataIndex[1]]; // Assurez-vous que nestedValue n'est pas null
        return nestedValue?.toString().toLowerCase().includes(lowerCaseValue);
      }

      return false; // Retourne false si dataIndex n'est ni de longueur 1 ni 2
    },
    // onFilterDropdownVisibleChange: visible => {
    //     if (visible) {
    //       placement="top"
    //     }
    //   },

    // render: (text) =>
    //   searchedColumn === dataIndex ? (
    //     <Highlighter
    //       highlightStyle={{
    //         backgroundColor: '#ffc069',
    //         padding: 0,
    //       }}
    //       searchWords={[searchText]}
    //       autoEscape
    //       textToHighlight={text ? text.toString() : ''}
    //     />
    //   ) : (
    //     text
    //   ),
  });
  const columns = [
    {
      title:
        item?.family_id == 6
          ? t("vue360.reference")
          : t("emailTemplates.fullName"),
      dataIndex: ["name"],
      fixed: "left",
      width: 250,
      render: (_, props) => (
        <div className="flex max-w-[250px] items-center justify-between">
          <div className="flex items-center gap-x-1 truncate">
            {props.avatar ? (
              <AvatarChat
                fontSize="0.625rem"
                url={
                  URL_ENV?.REACT_APP_BASE_URL +
                  URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                  props?.avatar
                }
                type="user"
                size={24}
                height={10}
                width={10}
                name={getName(props?.name, "avatar")}
                hasImage={true}
              />
            ) : (
              <DisplayAvatar
                background="rgb(219, 234, 254)"
                size={28}
                cursor={
                  contactInfo?.uid || contactInfo?.extension ? "pointer" : ""
                }
                icon={
                  familyIcons(t).find((el) => el.value === item.family_id)?.icon
                }
                name={contactInfo?.name}
              />
            )}
            <Typography.Link
              className="w-[187px] cursor-pointer truncate font-medium"
              onClick={() =>
                handleDispatchActions(
                  dispatch,
                  props,
                  openView360InDrawer,
                  setOpenChat,
                  setSelectedKeySideBar
                )
              }
            >
              {getName(props?.name, "name")}
            </Typography.Link>
          </div>
          {props?.id !== user?.id ? (
            <DropDownActionViewSphere
              item={{
                family_id: item?.family_id,
                extension: props?.extension,
                name: props?.label_data,
                phone: formattingNumPhones(props?.phone, user),
                email: props?.email,
                uuid: props?.uuid,
                id: props?.key,
              }}
              contactInfo={contactInfo}
              setElementDetailsGridView={setElementDetailsGridView}
              t={t}
              call={call}
              dispatch={dispatch}
              setReceiverEmail={setReceiverEmail}
              copyIcon={copyIcon}
              setOpenDrawerUpdate={setOpenDrawerUpdate}
              setOpenChat={setOpenChat}
              setSelectedKeySideBar={setSelectedKeySideBar}
            />
          ) : null}
        </div>
      ),
      ...getColumnSearchProps(["name"]),
      sorter: (a, b) => a?.name?.localeCompare(b?.name),
      //   sortDirections: ["descend", "ascend"],
    },
    {
      title: t("tasks.tableOwner"),
      dataIndex: ["owner", "name"],
      width: 250,
      sorter: (a, b) => a?.owner?.name.localeCompare(b?.owner?.name),
      //   sortDirections: ["descend", "ascend"],
      render: (_, props) => (
        <>
          {props?.owner ? (
            <div className="flex max-w-[250px] items-center justify-between">
              <div className="flex items-center gap-x-1 truncate">
                <AvatarChat
                  fontSize="0.625rem"
                  url={
                    URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                    props?.owner?.avatar
                  }
                  type="user"
                  size={24}
                  height={10}
                  width={10}
                  name={getName(props?.owner?.name, "avatar")}
                  hasImage={
                    props?.owner?.avatar &&
                    props?.owner?.avatar !== "/storage/uploads/"
                  }
                />
                <Typography.Text
                  className="w-[186px] truncate"
                  // onClick={() =>
                  //   handleDispatchActions(
                  //     dispatch,
                  //     props?.owner,
                  //     openView360InDrawer,
                  //     setOpenChat,
                  //     setSelectedKeySideBar
                  //   )
                  // }
                >
                  {getName(props?.owner?.name, "name")}
                </Typography.Text>
              </div>
              {props?.owner?.id !== user?.id ? (
                <DropDownActionViewSphere
                  item={{
                    family_id: 4,
                    extension: props?.owner?.extension,
                    name: props?.owner?.name,
                    phone: formattingNumPhones(props?.owner?.phones, user),
                    email: props?.owner?.email,
                    uuid: props?.owner?.uuid,
                    id: props?.owner?.id,
                  }}
                  contactInfo={contactInfo}
                  t={t}
                  call={call}
                  dispatch={dispatch}
                  setElementDetailsGridView={setElementDetailsGridView}
                  setOpenDrawerUpdate={setOpenDrawerUpdate}
                  setReceiverEmail={setReceiverEmail}
                  setOpenChat={setOpenChat}
                  setSelectedKeySideBar={setSelectedKeySideBar}
                />
              ) : null}
            </div>
          ) : null}
        </>
      ),
      ...getColumnSearchProps(["owner", "name"]),
    },
    // {
    //   title: t("fields_management.addField"),
    //   dataIndex: "type",
    //   width: 180,
    //   filters: Array.from(
    //     new Set(
    //       filteredTable
    //         ?.map((el) => el.type)
    //         .filter((type) => type != null && type !== "")
    //     )
    //   ).map((type) => ({
    //     text: type,
    //     value: type,
    //   })),
    //   onFilter: (value, record) => record.type === value,
    // },
    {
      title: "Pipeline",
      dataIndex: "pipeline",
      width: "100%",
      minWidth: 180,
      filters: Array.from(
        new Set(
          filteredTable
            ?.map((el) => el.pipeline_label)
            .filter((pipeline) => pipeline != null && pipeline !== "")
        )
      ).map((pipeline) => ({
        text: pipeline,
        value: pipeline,
      })),
      onFilter: (value, record) => record.pipeline === value,
      render: (_, props) => {
        // console.log(props?.pipeline);
        return <span>{props?.pipeline}</span>;
      },
    },
    {
      title: "Stage",
      dataIndex: "stage",
      width: "100%",
      minWidth: 180,
      filters: Array.from(
        new Set(
          filteredTable
            ?.map((el) => el.stage_label)
            .filter((stage) => stage != null && stage !== "")
        )
      ).map((stage) => ({
        text: stage,
        value: stage,
      })),
      onFilter: (value, record) => record.stage === value,
      render: (_, props) => (
        <CustomTag
          content={props?.stage_label}
          color={props?.stage_color}
          maxChars={14}
        />
      ),
    },
    ...(item?.family_id == 4
      ? [
          {
            title: t("dashboard.postNumber"),
            dataIndex: "extension",
            width: "100%",
            minWidth: 120,
            render: (_, props) => (
              <div className="group flex flex-row items-center justify-between">
                <span
                  className={`truncate text-slate-800 underline decoration-dashed underline-offset-4`}
                >
                  {props?.extension}
                </span>
                <div className="hidden items-center group-hover:block">
                  <Space>
                    <span>{copyIcon(props?.extension)}</span>
                    <Button
                      onClick={() =>
                        call(`${props?.extension}`, props?.id, props?.family_id)
                      }
                      icon={<FiPhoneForwarded style={{ fontSize: "14px" }} />}
                      type="link"
                      size="small"
                    />
                  </Space>
                </div>
              </div>
            ),
          },
          {
            title: t("dashboard.phoneNumber"),
            dataIndex: "phones",
            width: "100%",
            minWidth: 180,
            // width: 180,
            render: (_, props) => (
              <div>
                {formattingNumPhones(props?.phone, user)?.map(
                  ({ callNum, copyNum, displayNum }) => (
                    <div
                      className="group flex flex-row items-center justify-between"
                      key={displayNum}
                    >
                      <span
                        className={`truncate text-slate-800 underline decoration-dashed underline-offset-4`}
                      >
                        {displayNum}
                      </span>
                      <div className="hidden group-hover:block">
                        <Space>
                          <span>{copyIcon(copyNum)}</span>
                          <Button
                            onClick={() =>
                              call(callNum, props?.id, props?.family_id)
                            }
                            icon={
                              <FiPhoneForwarded style={{ fontSize: "14px" }} />
                            }
                            type="link"
                            size="small"
                          />
                        </Space>
                      </div>
                    </div>
                  )
                )}
              </div>
            ),
          },
          {
            title: "Email",
            dataIndex: "email",
            width: "100%",
            minWidth: 180,
            // width: 250,
            render: (_, props) => (
              <div className="group flex flex-row items-center justify-between">
                <span
                  className={`truncate text-slate-800 underline decoration-dashed underline-offset-4`}
                >
                  {props?.email}
                </span>
                <div className="hidden group-hover:block">
                  {copyIcon(props?.email)}
                </div>
              </div>
            ),
          },
        ]
      : []),
  ];

  return (
    <>
      {item?.family_id && (
        <Table
          columns={columns}
          size="small"
          dataSource={item?.child?.map((el) => ({
            key: el.id,
            type: el.field,
            name: el.label_data,
            pipeline: el.pipeline_label,
            stage: el.stage_label,
            ...el,
          }))}
          onChange={(pagination, filters, sorter, extra) => {
            setFilteredTable(extra.currentDataSource);
          }}
          className="association-table-viewSphere"
          pagination={{
            showSizeChanger: true,
            pageSizeOptions: ["5", "10"],
            defaultPageSize: 5,
          }}
          scroll={{
            y: "max-content",
            x: "max-content",
          }}
        />
      )}
    </>
  );
};

export default TableAssociationsViewSphere;
