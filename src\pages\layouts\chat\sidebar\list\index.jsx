import {
  Suspense,
  lazy,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from "react";
import {
  <PERSON><PERSON>,
  Button,
  Divider,
  Empty,
  List,
  Skeleton,
  Space,
  Spin,
  Tooltip,
  Typography,
} from "antd";
import VirtualList from "rc-virtual-list";

import DOMPurify from "dompurify";
import { useTranslation } from "react-i18next";
import {
  RightOutlined,
  AudioOutlined,
  DeleteOutlined,
  ExclamationCircleFilled,
  FieldTimeOutlined,
  FileImageOutlined,
  FileOutlined,
  ReloadOutlined,
  VideoCameraOutlined,
} from "@ant-design/icons";
import { FiBellOff } from "react-icons/fi";
import { useDispatch, useSelector } from "react-redux";
import { BsCheckAll, BsCheckLg } from "react-icons/bs";
import {
  HiOutlineCalendar,
  HiPhoneIncoming,
  HiPhoneOutgoing,
} from "react-icons/hi";
import { BiPoll } from "react-icons/bi";
import { MdPhoneMissed } from "react-icons/md";

import {
  setChatSelectedConversation,
  setChatSelectedParticipants,
  setUpdateMessageInChatMembers,
  resetStateOtherUser,
  setNumberUnreadMsg,
} from "new-redux/actions/chat.actions";
import { AvatarChat, DropDownGroupIndex, Loader } from "components/Chat";
import {
  audioMessageTypes,
  getName,
  simpleMessageTypes,
  simpleMessageReplyTypes,
  forwardMessageTypes,
  fileMessageTypes,
  imageMessageTypes,
  react_emojis,
  convertToPlain,
  systemMessageTypes,
  isRoom,
  isPublicRoom,
  getUserFromMsg,
} from "../../utils/ConversationUtils";
import {
  getArchivedConversations,
  getConversationApi,
  goToMessage,
} from "new-redux/services/chat.services";

import { moment_timezone } from "App";
import useFilterGroups from "../../hooks/useFilterGroups";
import { lazyRetry } from "utils/lazyRetry";
import useGetInfoDiscussion from "../../hooks/useGetInfoDiscussion";

import "./groups.css";
import { URL_ENV } from "index";
import { useWindowSize } from "custom-hooks/useWindowSize";
import { LoadingSideBarStatus } from "new-redux/reducers/chatReducer";
import { openDrawerChat } from "new-redux/actions/voip.actions/handleDrawerChat";
import { useInView } from "react-intersection-observer";
import { useLocation } from "react-router-dom";
import SearchFilterItem from "../search/searchFilterItem";
const config = {
  ALLOWED_TAGS: ["p", "span"],
  ALLOWED_ATTR: ["target", "class", "data-type"],
};

const ModalQuitGroup = lazy(() =>
  lazyRetry(
    () => import("../../../../../components/Chat/Modal/ModalQuitGroup"),
    "ModalQuitGroup"
  )
);

const highlightMessage = (message, keyword, withoutHighlight = false) => {
  if (withoutHighlight || !message || !keyword) return message;
  const escapedKeyword = keyword.trim().replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
  const regex = new RegExp(`(${escapedKeyword})`, "gi");
  return message.replace(regex, '<span class="bg-yellow-500">$1</span>');
};
const colorByTag = {
  0: "#108ee9",
  1: "#009900",
  2: "#ff4d4f",
  NaN: "#108ee9",
};

const GroupsSideBarChat = ({ source }) => {
  const [isRefetching, setRefetching] = useState(false);

  const dispatch = useDispatch();
  const [t, i18n] = useTranslation("common");
  const location = useLocation();

  const {
    searchChatSideBar,
    errorMessages,
    errorChatMember,
    currentUser,
    userList,
    openModalQuitGroup,
    loadingSideBar,
    dataConversation,
    lastMessage,
    sidebarDrawer,
    openDrawer,
  } = useSelector((state) => state.chat);
  const onlineUser = useSelector((state) => state.ChatRealTime.onlineUser);

  const typingUsers = useSelector((state) => state.ChatRealTime.typingUsers);
  const selectedConversation = useSelector(
    (state) => state.ChatRealTime.selectedConversation
  );

  const membersGroupsChatFetched = useSelector(
    (state) => state.ChatRealTime.membersGroupsChatFetched
  );
  const archivedListFetched = useSelector(
    (state) => state.ChatRealTime.archivedListFetched
  );

  const { user } = useSelector((state) => state.user);

  const { data, isFetched } = useGetInfoDiscussion();

  const { ref: bottomElement, inView: inViewLastElement } = useInView({
    threshold: 0.3,
    root: document.getElementById("containerList"),
  });

  const [_, height] = useWindowSize();
  useEffect(() => {
    if (isFetched) {
      if (data?.data.room_info?.participants)
        dispatch(
          setChatSelectedParticipants({
            selectedParticipants: data?.data.room_info?.participants,
          })
        );
    }
  }, [openDrawer, data?.data.room_info?.participants, dispatch, isFetched]);

  const loadData = async (refetch = false) => {
    if (membersGroupsChatFetched || archivedListFetched) {
      return;
    }

    try {
      if (refetch) setRefetching(true);
      if (sidebarDrawer === "chat")
        await dispatch(
          getConversationApi({ errorText: t("toasts.errorFetchApi") })
        );
      else dispatch(getArchivedConversations());
    } catch (error) {
      return;
    } finally {
      setRefetching(false);
    }
  };
  const showDate = (a) => new Date(a.last_message_date);

  const getTypeOfMessage = (item) => {
    if (fileMessageTypes.includes(item?.type))
      return t("chat.message_type.file");
    else if (imageMessageTypes.includes(item?.type))
      return t("chat.message_type.image");
    else if (audioMessageTypes.includes(item?.type)) return t("chat.audio");
    else if (item?.type === "message_missed_call")
      return t("chat.message_system.missed_call");
    else if (item?.type === "message_received_call")
      return t("chat.message_system.incoming_call");
    else if (
      [...simpleMessageTypes, "poll_message", "draft", "message_task"].includes(
        item?.type
      )
    )
      return highlightMessage(
        DOMPurify.sanitize(item?.message, config),
        searchChatSideBar,
        item.type_item !== "messages"
      );
  };

  const renderTypingUser = (currentItem) => {
    const type = currentItem.contact !== null ? "user" : "room";
    switch (type) {
      case "user":
        return (
          typingUsers.find(
            (item) =>
              item.user_id === currentItem.contact._id && item.room_id === null
          ) && (
            <span className="text-xs font-semibold text-blue-500 first-letter:capitalize">
              {" "}
              {getName(
                userList.find((item) => item._id === currentItem.contact._id)
                  ?.name,
                "name"
              ) +
                " " +
                t("chat.typing")}
            </span>
          )
        );
      case "room": {
        let users_typing = [];
        //31, 34, 158, 161, 162, 24, 42, 90
        typingUsers.forEach((element) => {
          if (element.room_id === currentItem.room._id)
            users_typing.push(element.user_id);
        });
        users_typing = [...new Set([...users_typing])].filter(
          (user) => user !== currentUser?._id
        );
        return (
          <div className="flex w-full items-center space-x-1   ">
            {users_typing.slice(0, 1).map((element, index) => (
              <span
                key={`typing_user_${index}`}
                className="text-xs  font-semibold  text-blue-500 first-letter:capitalize"
              >
                {getName(
                  userList.find((item) => item._id === element)?.name,
                  "name"
                )}
                {index !== users_typing.slice(0, 1).length - 1 && ", "}
              </span>
            ))}
            {users_typing.length > 1 && (
              <span className="ml-1  text-xs  font-semibold text-blue-500">
                {t("chat.others")}
              </span>
            )}
            {users_typing.length > 0 && (
              <span className="ml-1  text-xs  font-semibold text-blue-500">
                {users_typing.length === 1
                  ? t("chat.typing")
                  : t("chat.userstyping")}
              </span>
            )}
          </div>
        );
      }
      default:
        break;
    }
    return;
  };

  const ReadNotif = ({ item }) => {
    if (item?.sender?._id !== currentUser?._id) return <></>;
    if (item?.last_message?.unread === 2)
      return (
        <FieldTimeOutlined
          size={14}
          className="mr-0.5 flex-none text-gray-600"
        />
      );
    if (item?.last_message?.unread === 1) {
      return <BsCheckLg size={14} className="mr-0.5 flex-none" />;
    } else if (item?.last_message?.unread === 0) {
      return (
        <BsCheckAll size={16} className="mr-0.5 flex-none text-blue-400" />
      );
    } else return <></>;
  };
  const suffixMessage = (msg, sender_item, all) =>
    isRoom(msg) ? (
      <>
        <span className={`mr-0.5 text-xs`}>{`${
          sender_item?._id === currentUser?._id
            ? `${t("chat.you")}`
            : getName(
                sender_item?._id === currentUser?._id
                  ? user?.label
                  : sender_item?.name,
                "name"
              )
        } : `}</span>{" "}
      </>
    ) : all ? (
      <ReadNotif item={msg} />
    ) : (
      <></>
    );

  const getMessage = (item) => {
    const sender_item = getUserFromMsg(item?.sender?._id);

    const typing = typingUsers.find(
      (el) =>
        (item.contact &&
          el.user_id === getCurrentItem(item)?._id &&
          el.room_id === null) ||
        (item.room && el.room_id === getCurrentItem(item)?._id)
    );

    const lastMessageValue = lastMessage.find(
      ({ conversationId }) =>
        conversationId ===
        `${isRoom(item) ? item.room?._id : item.contact?._id}_${
          isRoom(item) ? "room" : "user"
        }_chat_main`
    )?.value;
    if (typing) return renderTypingUser(item);
    if (currentUser?.config?.hidden_message === 1) return;

    if (lastMessageValue && convertToPlain(lastMessageValue).length > 0)
      return (
        <div className="flex w-full items-center space-x-1   ">
          <span className="ml-1  text-xs  font-semibold text-blue-500">
            {t("chat.message_system.draft")}:
          </span>

          <p
            className="h-4 w-[200px] overflow-hidden truncate  [&_p]:truncate"
            dangerouslySetInnerHTML={{
              __html: getTypeOfMessage({
                type_item: item.type_item,
                type: "draft",
                message: lastMessageValue,
              }),
            }}
          ></p>
        </div>
      );

    if (item?.sender === null) {
      return (
        <>
          <span>
            {item?.room?.admin_id === currentUser?._id
              ? t("chat.create_group_by_auth", { name: item?.room?.name })
              : t("chat.create_group", { name: item?.room?.name })}
          </span>
        </>
      );
    } else if (item.type_item === "users") {
      return (
        <div className="   flex w-full ">
          {" "}
          <p className=" truncate">
            {t("chat.searchSide.new_user", {
              name: getName(item?.name, "name"),
            })}
          </p>
        </div>
      );
    }
    // reaction render
    else if (
      item?.sender &&
      item?.reaction !== null &&
      item?.last_message !== null &&
      item?.last_message?.type !== "message_from_bot"
    ) {
      return (
        <div className="flex items-center space-x-1">
          <span>
            {getName(
              sender_item?._id === currentUser?._id
                ? user?.label
                : sender_item?.name,

              "name"
            )}
            {" " + t("chat.reacted")}
          </span>
          <img
            src={process.env.PUBLIC_URL + react_emojis[item?.reaction]?.image}
            loading="lazy"
            className=" h-4 w-4 cursor-pointer object-cover "
            alt={react_emojis[item?.reaction]?.name}
          />
          <span className="mx-0.5">{t("chat.onMessage")}</span>

          <p
            className=" h-4  w-14   [&_p]:truncate"
            dangerouslySetInnerHTML={{
              __html: getTypeOfMessage({
                ...item.last_message,

                type_item: item.type_item,
              }),
            }}
          ></p>
        </div>
      );
    } else if (item?.last_message?.type === "poll_message") {
      return (
        <div className="flex flex-1 items-center justify-start">
          {suffixMessage(item, sender_item, true)}

          <div className="flex items-center">
            <BiPoll className=" text-yellow-400" size={20} />

            <p
              dangerouslySetInnerHTML={{
                __html: getTypeOfMessage({
                  ...item?.last_message,

                  type_item: item.type_item,
                }),
              }}
            ></p>
          </div>
        </div>
      );
    } else if (item?.last_message === null) {
      return (
        <>
          {suffixMessage(item, sender_item, false)}

          <DeleteOutlined />
          <span> {t("chat.delete.messaageDeleted")}</span>
        </>
      );
    } else if (item?.last_message?.type === "message_from_bot") {
      return (
        <>
          <div className="flex items-center">
            <span
              className={`text-xs font-medium ${
                item.bot?.name
                  ? `bg-red-50 text-red-800`
                  : `bg-blue-50 text-blue-800`
              }  rounded px-1 py-0.5`}
            >
              {item.bot && item.bot?.name
                ? getName(item.bot?.name, "name")
                : sender_item && sender_item?.name
                ? getName(
                    item.sender?._id === currentUser?._id
                      ? user?.label
                      : sender_item?.name,
                    "name"
                  )
                : ""}
              :
            </span>
            <div className="h-4 ">
              <p
                className=" max-w-[150px] overflow-hidden truncate [&_p]:truncate  "
                dangerouslySetInnerHTML={{
                  __html: DOMPurify.sanitize(
                    item?.last_message?.message,
                    config
                  ),
                }}
              ></p>
            </div>
          </div>
        </>
      );
    } else if (simpleMessageTypes.includes(item?.last_message?.type)) {
      return (
        <div className="flex flex-1 items-center justify-start">
          {suffixMessage(item, sender_item, true)}
          {forwardMessageTypes.includes(item?.last_message?.type) && (
            <p>{t("chat.forward.message_sidebar")} : </p>
          )}
          <div className="flex items-center space-x-0.5">
            <p>
              {item?.last_message?.type?.includes("replay")
                ? `${t("chat.reply.sidebar")}`
                : ""}
            </p>
            <div
              className={`
            
            ${
              item?.last_message?.type?.includes("replay")
                ? "   w-[155px] "
                : "w-[220px]"
            }
              h-4

            `}
            >
              <p
                className="  truncate [&_p]:truncate"
                dangerouslySetInnerHTML={{
                  __html: getTypeOfMessage({
                    ...item?.last_message,

                    type_item: item.type_item,
                  }),
                }}
              ></p>
            </div>
          </div>{" "}
        </div>
      );
    } else if (simpleMessageReplyTypes.includes(item?.last_message?.type)) {
      return (
        <div className="flex flex-1 items-center justify-start">
          {suffixMessage(item, sender_item, true)}
          {t("chat.respond")}
          <div className="h-4   w-[220px]  ">
            <p
              className="  truncate [&_p]:truncate"
              dangerouslySetInnerHTML={{
                __html: getTypeOfMessage({
                  ...item?.last_message,

                  type_item: item.type_item,
                }),
              }}
            ></p>
          </div>{" "}
        </div>
      );
    } else if (fileMessageTypes.includes(item?.last_message?.type)) {
      return (
        <div className="flex flex-1  items-center justify-start space-x-1">
          {suffixMessage(item, sender_item, true)}

          {forwardMessageTypes.includes(item?.last_message?.type) && (
            <p>{t("chat.forward.message_sidebar")} : </p>
          )}
          {item?.last_message?.type.includes("replay") && (
            <p>{t("chat.reply.sidebar")} </p>
          )}
          <div className="gap-x-1">
            <FileOutlined />
            <span> {t("chat.message_type.file")}</span>
          </div>
        </div>
      );
    } else if (imageMessageTypes.includes(item?.last_message?.type)) {
      return (
        <div className="flex flex-1 items-center justify-start space-x-0.5">
          {suffixMessage(item, sender_item, true)}

          {forwardMessageTypes.includes(item?.last_message?.type) && (
            <p className="mx-1">{t("chat.forward.message_sidebar")} : </p>
          )}
          {item?.last_message?.type.includes("replay") && (
            <p>{t("chat.reply.sidebar")} </p>
          )}
          <FileImageOutlined className="mr-0.5" />
          <span className="mx-0.5"> {t("chat.message_type.image")}</span>
        </div>
      );
    } else if (audioMessageTypes.includes(item?.last_message?.type)) {
      return (
        <div className="flex flex-1  items-center justify-start space-x-0.5">
          {suffixMessage(item, sender_item, true)}

          {forwardMessageTypes.includes(item?.last_message?.type) && (
            <p className="mx-1">{t("chat.forward.message_sidebar")} : </p>
          )}
          {item?.last_message?.type.includes("replay") && (
            <p>{t("chat.reply.sidebar")} </p>
          )}
          <AudioOutlined className="mr-0.5" />
          <span> {t("chat.audio")}</span>
        </div>
      );
    } else if (
      [systemMessageTypes[0], systemMessageTypes[1]].includes(
        item?.last_message?.type
      )
    ) {
      return (
        <div className="flex flex-1 items-center justify-start">
          {!isRoom(item) && <ReadNotif item={item} />}

          {item.sender?._id === currentUser?._id ? (
            item?.last_message?.type === systemMessageTypes[0] ? (
              <>
                <HiPhoneOutgoing className="mr-1 h-3 w-3  fill-gray-500" />
                <span>{t("chat.message_system.outgoing_call")}</span>
              </>
            ) : item?.last_message?.type === systemMessageTypes[1] ? (
              <>
                <HiPhoneOutgoing className="mr-1 h-3 w-3  fill-blue-500" />
                <span>{t("chat.message_system.outgoing_call")}</span>
              </>
            ) : (
              <></>
            )
          ) : item?.last_message?.type === systemMessageTypes[0] ? (
            <>
              <MdPhoneMissed className="mr-1 h-3 w-3  fill-red-500" />
              <span>{t("chat.message_system.missed_call")}</span>
            </>
          ) : item?.last_message?.type === systemMessageTypes[1] ? (
            <>
              <HiPhoneIncoming className="mr-1 h-3 w-3  fill-green-500" />
              <span> {t("chat.message_system.incoming_call")}</span>
            </>
          ) : (
            <></>
          )}
        </div>
      );
    } else if (systemMessageTypes[2] === item?.last_message?.type)
      return (
        <div className="flex flex-1 items-center justify-start">
          {!isRoom(item) && <ReadNotif item={item} />}

          <HiOutlineCalendar
            className="mr-1 h-3 w-3  text-pink-500"
            style={{ fontSize: "21px" }}
          />
          <span className={`mr-0.5 text-xs`}>{`${
            sender_item?._id === currentUser?._id
              ? `${t("chat.you")}`
              : getName(
                  sender_item?._id === currentUser?._id
                    ? user?.label
                    : sender_item?.name,
                  "name"
                )
          } `}</span>

          <span>
            {convertToPlain(item?.last_message?.message)?.split("_")[0] ===
            "create"
              ? sender_item?._id === currentUser?._id
                ? t("chat.message_system.task_created_auth")
                : t("chat.message_system.task_created")
              : t("chat.message_system.task_updated_stage")}
          </span>
        </div>
      );
    else if (systemMessageTypes[3] === item?.last_message?.type)
      return (
        <div className="flex flex-1 items-center justify-start">
          {!isRoom(item) && <ReadNotif item={item} />}
          <VideoCameraOutlined className="w-3.h-3.5 mr-1 h-3.5  text-gray-500" />
          <span className={`mr-0.5 text-xs`}>{`${
            sender_item?._id === currentUser?._id
              ? `${t("chat.you")}`
              : getName(
                  sender_item?._id === currentUser?._id
                    ? user?.label
                    : sender_item?.name,
                  "name"
                )
          } `}</span>
          <span>
            {sender_item?._id === currentUser?._id
              ? t("chat.message_system.visio-created-auth")
              : t("chat.message_system.visio-created")}
          </span>
        </div>
      );
    else return <></>;
  };

  const getCurrentItem = (item) => {
    if (item.contact !== null) {
      let newItem =
        userList?.find((user) => user._id === item.contact?._id) ??
        item.contact;
      return newItem;
    } else if (item.room !== null) {
      return item.room;
    } else if (item.bot !== null) {
      return item.bot;
    }
  };
  const getTypeCurrentItem = (item) => {
    if (isRoom(item)) {
      return "room";
    } else {
      return "user";
    }
  };
  const setCurrentSelectedItem = async (item, external) => {
    !external &&
      location.pathname !== "/chat" &&
      dispatch(openDrawerChat(null, item._id, "popoverChat"));
    const currentItem = getCurrentItem(item);
    if (
      item.type_item === "messages" ||
      currentItem?._id !== selectedConversation?.id ||
      selectedConversation?.type !== getTypeCurrentItem(item) ||
      !selectedConversation ||
      selectedConversation?.id === null ||
      selectedConversation?.external
    ) {
      dispatch(
        resetStateOtherUser({
          keepDrawerOpened: external,
          forced: true,
          item: null,
        })
      );
      Number(item?.total_unread) > 0 &&
        dispatch(
          setNumberUnreadMsg({
            id: currentItem?._id,
            number: item?.total_unread,
          })
        );
      if (isRoom(item)) {
        try {
          dispatch(
            setChatSelectedConversation({
              selectedConversation: {
                name: currentItem?.name,
                description: currentItem?.description,
                image: currentItem?.image,
                admin_id: currentItem?.admin_id,
                bot: item.bot,
                predefined: currentItem?.predefined,
                id: currentItem?._id,
                last_message_date: item?.last_message_date,
                type: "room",
                source: "chat",

                muted_status: item?.muted_status,
                conversationId:
                  item.type_item === "messages"
                    ? item.conversation_id
                    : item?._id,
                external,
              },
            })
          );
        } catch (error) {
          console.error(error);
        }
      } else {
        dispatch(
          setChatSelectedParticipants({
            selectedParticipants: [
              {
                email: currentItem?.email,
                name: currentItem?.name,
                description: null,
                image: currentItem?.image,
                uuid: currentItem?.uuid,
                admin_id: null,

                bot: null,
                _id: currentItem?._id,
                post_number: currentItem?.post_number,
                type: "user",
              },
              currentUser,
            ],
          })
        );
        dispatch(
          setChatSelectedConversation({
            selectedConversation: {
              email: currentItem?.email,
              name: currentItem?.name,
              image: currentItem?.image,
              uuid: currentItem?.uuid,
              id: currentItem?._id,
              post_number: currentItem?.post_number,
              last_message_date: item?.last_message_date,
              muted_status: item?.muted_status,
              source: "chat",

              conversationId:
                item.type_item === "messages"
                  ? item.conversation_id
                  : !item.type_item
                  ? item?._id
                  : null,
              type: "user",

              external,
            },
          })
        );
      }
    }

    if (item.type_item === "messages") {
      dispatch(
        await goToMessage({
          id_search: item?.message_id,
          type: item?.room ? "room" : "user",
        })
      );
    }
  };

  const {
    membersGroupsFilteredChat,
    archivedListFiltred,
    searchMeta,
    setSearchMeta,
  } = useFilterGroups({
    inViewLastElement,
  });
  useEffect(() => {
    if (errorMessages && errorMessages?.length > 0 && typingUsers.length > 0) {
      errorMessages.forEach((item) => {
        dispatch(
          setUpdateMessageInChatMembers({
            data: item?.message,
            unread: "error",
            type: item?.message?.room_id ? "room" : "user",
            reaction: null,
            discussion_id: item?.discussion_id,
            sender: item?.message?.sender,
          })
        );
      });
    }
  }, [errorMessages, typingUsers, dispatch]);

  const transformData = useCallback(() => {
    let data =
      sidebarDrawer === "chat"
        ? membersGroupsFilteredChat
        : archivedListFiltred;
    const sections = [];
    if (
      (searchMeta?.filter === "all" ||
        searchMeta?.filter === "conversations") &&
      data.conversations &&
      data.conversations.length
    ) {
      sections.push();
      sections.push(
        { _id: 1, type: "conversations", isHeader: true },
        ...data.conversations
      );
    }
    if (
      (searchMeta?.filter === "all" || searchMeta?.filter === "users") &&
      data.users &&
      data.users.length
    ) {
      sections.push({ _id: 2, type: "users", isHeader: true }, ...data.users);
    }

    if (
      (searchMeta?.filter === "all" || searchMeta?.filter === "messages") &&
      data.messages &&
      data.messages.length
    ) {
      sections.push(
        { _id: 3, type: "messages", isHeader: true },
        ...data.messages
      );
    }

    return sections;
  }, [
    archivedListFiltred,
    membersGroupsFilteredChat,
    searchMeta?.filter,
    sidebarDrawer,
  ]);
  const listData = useMemo(() => transformData(), [transformData]);
  return (
    <>
      <div id="containerDIV" className="my-2">
        <SearchFilterItem
          searchMeta={searchMeta}
          setSelectedFilter={setSearchMeta}
        />

        {loadingSideBar === LoadingSideBarStatus.hard ? (
          Array.apply(null, { length: 10 }).map((e, i) => (
            <Skeleton
              className="px-2"
              avatar
              active
              paragraph={{
                rows: 0,
              }}
              key={i}
            />
          ))
        ) : errorChatMember ? (
          <Empty
            className="  flex  flex-1 flex-col items-center justify-center "
            description={
              <Space size={3} className="flex flex-col">
                <Typography.Text type="danger">
                  {t("toasts.errorFetchApi")}
                </Typography.Text>
                <Tooltip title={t("chat.reload")}></Tooltip>
              </Space>
            }
          >
            <Tooltip title={t("chat.reload")}>
              <Button
                loading={isRefetching}
                danger
                type="primary"
                onClick={() => loadData(true)}
                icon={<ReloadOutlined />}
              >
                {t("chat.reload")}
              </Button>
            </Tooltip>
          </Empty>
        ) : (
          <List id="containerList" className="membersList  h-full">
            {listData?.length === 0 ? (
              <div style={{ height: height - 32 - 210 }}>
                <Spin spinning={searchMeta?.loading}>
                  <Empty />
                </Spin>
              </div>
            ) : (
              <>
                <VirtualList
                  //
                  id="messagesList"
                  data={listData}
                  lang={i18n.language}
                  height={height - 32 - 210}
                  itemKey="_id"
                  itemHeight={70}
                >
                  {(item, index) => (
                    <div
                      id={"listItem-" + index}
                      className="group  flex w-full flex-col"
                    >
                      {searchChatSideBar !== "" && item.isHeader && (
                        <>
                          <Divider
                            key={item.type}
                            plain
                            style={{
                              margin: "0 !important",
                              color: "#CCC",
                            }}
                            orientation="left"
                          >
                            <Typography.Text className="  text-xs font-medium uppercase text-[#388cfa]">
                              {t("chat.searchSide." + item.type)}
                            </Typography.Text>
                          </Divider>
                        </>
                      )}

                      {!item.isHeader && (
                        <List.Item
                          ref={
                            searchMeta?.keyword &&
                            searchMeta?.hasMore?.[searchMeta?.filter] &&
                            searchMeta?.filter !== "all" &&
                            index === listData?.length - 1
                              ? bottomElement
                              : undefined
                          }
                          key={`${item._id}---${index}`}
                          className=" flex-1  hover:cursor-pointer"
                          style={
                            item.type_item !== "messages" &&
                            selectedConversation?.id ===
                              getCurrentItem(item)?._id &&
                            selectedConversation?.type ===
                              getTypeCurrentItem(item) &&
                            !selectedConversation?.external
                              ? {
                                  "--tw-bg-opacity": 0.6,
                                  backgroundColor:
                                    "rgb(226 232 240 / var(--tw-bg-opacity))",
                                }
                              : {}
                          }
                        >
                          <div
                            onClick={() => setCurrentSelectedItem(item, false)}
                            className="flex w-full items-center space-x-2"
                          >
                            <List.Item.Meta
                              avatar={
                                <Badge
                                  dot={!isRoom(item)}
                                  color={
                                    onlineUser[item?.contact?.uuid] === "away"
                                      ? "orange"
                                      : onlineUser[item?.contact?.uuid] ===
                                        "busy"
                                      ? "red"
                                      : onlineUser[item?.contact?.uuid] ===
                                        "online"
                                      ? "green"
                                      : "#a6a6a6"
                                  }
                                  offset={[-4, 40]}
                                >
                                  <AvatarChat
                                    isPublic={isPublicRoom(item)}
                                    roomPro={item.room?.predefined}
                                    type={isRoom(item) ? "room" : "user"}
                                    hasImage={getCurrentItem(item)?.image}
                                    size={48}
                                    height={8}
                                    width={8}
                                    url={
                                      //process.env.PUBLIC_URL + "/avatar/08000001.jpg"
                                      isRoom(item)
                                        ? URL_ENV?.REACT_APP_OAUTH_CHAT_API +
                                          process.env
                                            .REACT_APP_SUFFIX_CHAT_GROUP_STORAGE +
                                          getCurrentItem(item)?.image
                                        : URL_ENV?.REACT_APP_BASE_URL +
                                          URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                                          getCurrentItem(item)?.image
                                    }
                                    name={getName(
                                      getCurrentItem(item)?.name,
                                      "avatar"
                                    )}
                                    backgroundColor={
                                      selectedConversation?.id ===
                                        getCurrentItem(item)?._id && "#cde2fe"
                                    }
                                  />
                                </Badge>
                              }
                              title={
                                <div
                                  className={`${
                                    item?.total_unread > 0 ||
                                    (isRoom(item) && item?.sender === null)
                                      ? " font-semibold "
                                      : ""
                                  }  flex  items-center
                                        `}
                                >
                                  <span
                                    dangerouslySetInnerHTML={{
                                      __html: highlightMessage(
                                        getName(
                                          getCurrentItem(item)?.name,
                                          "name"
                                        ),
                                        searchChatSideBar,
                                        item.type_item === "messages"
                                      ),
                                    }}
                                    className=" max-w-56 truncate  "
                                  ></span>
                                </div>
                              }
                              description={
                                <div className="flex items-center">
                                  <div
                                    className={`${
                                      item?.total_unread > 0
                                        ? "font-semibold text-gray-900"
                                        : isRoom(item) && item?.sender === null
                                        ? " font-medium"
                                        : ""
                                    }`}
                                  >
                                    {getMessage(item)}
                                  </div>
                                </div>
                              }
                            />

                            <div className="flex flex-col items-end justify-end transition-all duration-300 group-hover:hidden">
                              {item.type_item === "users" ? (
                                <RightOutlined className="text-gray-500" />
                              ) : (
                                showDate(item) && (
                                  <>
                                    {moment_timezone(showDate(item)).isSame(
                                      moment_timezone(),
                                      "day"
                                    ) ? (
                                      <span
                                        style={{
                                          color:
                                            item.total_unread > 0
                                              ? item.contact === null
                                                ? colorByTag[
                                                    Number(item.tag_status)
                                                  ]
                                                : colorByTag[0]
                                              : "",
                                        }}
                                        className="time"
                                      >
                                        {moment_timezone(
                                          showDate(item)
                                        )?.format("LT")}
                                      </span>
                                    ) : moment_timezone(showDate(item)).isSame(
                                        moment_timezone().subtract(1, "days"),
                                        "days"
                                      ) ? (
                                      <span
                                        style={{
                                          color:
                                            item.total_unread > 0
                                              ? colorByTag[
                                                  Number(item.tag_status)
                                                ]
                                              : "",
                                        }}
                                        className="time"
                                      >
                                        {moment_timezone(
                                          showDate(item)
                                        )?.calendar(null, {
                                          lastDay: () =>
                                            i18n.language === "fr"
                                              ? "[Hier]"
                                              : "[Yesterday]",
                                          sameDay: () =>
                                            i18n.language === "fr"
                                              ? "[Aujourd'hui]"
                                              : "[Today]",
                                        })}
                                      </span>
                                    ) : new Date() - new Date(showDate(item)) <
                                      7 * 1000 * 3600 * 24 ? (
                                      <span
                                        style={{
                                          color:
                                            item.total_unread > 0
                                              ? colorByTag[
                                                  Number(item.tag_status)
                                                ]
                                              : "",
                                        }}
                                        className="time"
                                      >
                                        {moment_timezone(
                                          showDate(item)
                                        )?.format("ddd")}
                                      </span>
                                    ) : (
                                      <span
                                        style={{
                                          color:
                                            item.total_unread > 0
                                              ? colorByTag[
                                                  Number(item.tag_status)
                                                ]
                                              : "",
                                        }}
                                        className="time"
                                      >
                                        {moment_timezone(
                                          showDate(item)
                                        )?.format("DD MMM")}
                                      </span>
                                    )}
                                    {item.type_item === undefined && (
                                      <span
                                        className={`flex ${
                                          item?.muted_status ? "space-x-1" : ""
                                        }`}
                                      >
                                        {item.last_message?.unread ===
                                        "error" ? (
                                          <Badge size="small">
                                            <Tooltip
                                              title={t(
                                                "chat.error_message.message_send"
                                              )}
                                            >
                                              <ExclamationCircleFilled className="animate-none text-lg text-[#ff4d4f] " />
                                            </Tooltip>
                                          </Badge>
                                        ) : item.total_unread > 0 ? (
                                          <Badge
                                            count={item.total_unread}
                                            color={
                                              colorByTag[
                                                Number(item.tag_status)
                                              ]
                                            }
                                            size="small"
                                          ></Badge>
                                        ) : isRoom(item) &&
                                          item?.sender === null ? (
                                          <Badge
                                            count={t("chat.new")}
                                            color="red"
                                            size="small"
                                          ></Badge>
                                        ) : null}
                                        {item?.muted_status && (
                                          <FiBellOff
                                            className="time"
                                            style={{ fontSize: "13px" }}
                                          />
                                        )}
                                      </span>
                                    )}
                                  </>
                                )
                              )}
                            </div>
                          </div>
                          {item.type_item ? (
                            <RightOutlined className="hidden text-gray-700 group-hover:block group-hover:scale-[1.2] " />
                          ) : (
                            (!item.last_message ||
                              (typeof item.last_message?.unread === "number" &&
                                item.last_message?.unread <= 1)) &&
                            source !== "external" && (
                              <span className="hidden transition-all duration-300 group-hover:block group-hover:px-1">
                                <Suspense fallback={<></>}>
                                  <DropDownGroupIndex
                                    source={source}
                                    index={index}
                                    item={item}
                                    setCurrentSelectedItem={
                                      setCurrentSelectedItem
                                    }
                                  />
                                </Suspense>
                              </span>
                            )
                          )}
                        </List.Item>
                      )}
                      {(searchMeta?.loadingMore[searchMeta.filter] ||
                        searchMeta?.loading) &&
                        listData.length - 1 <= index && (
                          <div className=" my-1 flex flex-1  items-center justify-center  space-x-1.5  text-xs text-gray-400">
                            <Spin size="small" />
                            <span>{t("chat.loading")}...</span>
                          </div>
                        )}
                    </div>
                  )}
                </VirtualList>
              </>
            )}
          </List>
        )}
      </div>
      {openModalQuitGroup && (
        <Suspense
          fallback={
            <div className="fixed inset-0 z-[9999] flex h-full w-full   items-center justify-center bg-black/5 ">
              <Loader size="2rem" />
            </div>
          }
        >
          <ModalQuitGroup
            openModalQuitGroup={openModalQuitGroup}
            item={dataConversation}
            informations={false}
          />
        </Suspense>
      )}
      {/* {openModalEmail && (
        <Suspense
          fallback={
            <div className="fixed inset-0 z-[9999] flex h-full w-full   items-center justify-center bg-black/5 ">
              <Loader size="2rem" />
            </div>
          }
        >
          <ModalMessageMail
            defaultObject={
              !dataConversation?.owner ? "" : `${t("chat.header.visio.title")}`
            }
            defaultMessage={
              !dataConversation?.owner
                ? ""
                : `<p><strong>${t("chat.header.visio.title")} ${
                    dataConversation?.owner
                  }</strong></p>
               <p>${user?.label} ${t("chat.header.visio.description")}</p>
               <p><a href=${dataConversation?.href}rel="noreferrer noopenner"
              target="_blank">${dataConversation?.url}</a></p>`
            }
            title={t("chat.header.shareWithEmail")}
            receiver={selectedConversation?.email}
            sender={
              user?.accounts_email?.length > 0
                ? user?.accounts_email?.find(
                    (item) =>
                      parseInt(item.selected) === 1 ||
                      parseInt(item.shared) === 1
                  )?.id || user?.accounts_email[0]?.id
                : user?.email
            }
          />
        </Suspense>
      )} */}
    </>
  );
};
export default GroupsSideBarChat;
