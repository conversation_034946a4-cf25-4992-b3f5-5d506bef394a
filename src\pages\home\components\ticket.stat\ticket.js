import { URL_ENV } from "index";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { generateAxios } from "services/axiosInstance";
import {
  BigDonutChart,
  DonutChartWithDrillDown,
  DualAxes,
  GaugeChart,
  HorizontalOneBarChart,
  OneBarChart,
  PieChartWithLegend,
} from "../ChartsDashboard";
import {
  Card,
  Col,
  Row,
  Select,
  Skeleton,
  Spin,
  Statistic,
  Typography,
} from "antd";
import MainService from "services/main.service";
import { toastNotification } from "components/ToastNotification";
import { icons } from "lucide-react"; // Importer tous les icônes de Lucide
import TableStatsTickets from "./TableStatsTickets";
import { isGuestConnected } from "utils/role";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import {
  setPipelinesTicket,
  setSelectedPipelineInTicket,
} from "new-redux/actions/dashboard.actions";
import {
  getStatByFamily,
  getStatFamilyByField,
} from "pages/home/<USER>";
import CardStat from "../CardStat";
export function LucideIcon({ iconName, color = "", size = 24 }) {
  // Vérifier si l'icône demandée existe
  if (icons[iconName]) {
    const IconComponent = icons[iconName];

    return <IconComponent color={color} size={size} />;
  } else {
    return null;
  }
}
const DashboardTicket = ({ start, end, from = "", idPipeline = "" }) => {
  const [statsByFamily, setStatsByFamily] = useState({});
  const [statsPriorities, setStatsPriorities] = useState({});
  const [statsPermiters, setStatsPerimiters] = useState({});
  const [statsTypes, setStatsTypes] = useState({});
  const [statsChannels, setStatsChannels] = useState({});
  const [statsProducts, setStatsProducts] = useState({});
  const [statsClosedTickets, setStatsClosedTickets] = useState({});
  const [agentsClosedTickets, setagentsClosedTickets] = useState({});
  const [loading, setLoading] = useState(false);
  const [cardsTickets, setCardsTickets] = useState([]);
  const [loadCardsTickets, setLoadCardsTickets] = useState([]);

  const [topCLient, setTopClient] = useState({});

  const { i18n } = useTranslation("common");
  const [t] = useTranslation("common");
  const {
    selectedPipelineInTicket: selectedPipeline,
    pipelinesTicket: pipelines,
  } = useSelector((state) => state.dashboardRealTime);
  const dispatch = useDispatch();
  const getStatFamilyByFieldInTicket = async (field) => {
    try {
      const result = await getStatFamilyByField(
        start,
        end,
        6,
        field,
        selectedPipeline
      );
      return result;
    } catch (error) {
      console.error("Error in getStatFamilyByFieldInDeal:", error);
      throw error;
    }
  };

  useEffect(() => {
    const getPipelines = async () => {
      setLoading(true);
      try {
        const response = await MainService.getPipelinesByFamily(6);

        dispatch(
          setPipelinesTicket(
            response?.data?.data.map((el) => ({
              value: el.id,
              label: el.label,
            }))
          )
        );
        if (!selectedPipeline?.value) {
          dispatch(
            setSelectedPipelineInTicket({
              label: response?.data?.data[0]?.label,
              value: response?.data?.data[0]?.id,
            })
          );
        }
        setLoading(false);
      } catch (error) {
        console.log(`Error ${error}`);
        setLoading(false);
        toastNotification("error", t("toasts.somethingWrong"));
      }
    };
    if (!selectedPipeline?.value) getPipelines();
  }, [selectedPipeline?.value, t]);
  useEffect(() => {
    const fetchData = async () => {
      setLoadCardsTickets(true);
      try {
        const res = await generateAxios(
          `${URL_ENV?.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
        ).post(`/get_cardkpi_family`, {
          date_start: start,
          date_end: end,
          lang: i18n.language,
          family_id: 6,
          pipeline_id: selectedPipeline?.value,
          // field_system_id: 55,
        });
        setCardsTickets(res.data.data);
        setLoadCardsTickets(false);

        // setStatsClosedTickets(res.data?.da ta);
      } catch (err) {
        setLoadCardsTickets(false);
      }
    };
    if (selectedPipeline?.value) {
      fetchData();
    }
  }, [start, end, i18n.language, selectedPipeline.value]);
  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await generateAxios(
          `${URL_ENV?.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
        ).post(`/get_sum_kpiclosed_family`, {
          date_start: start,
          date_end: end,
          lang: i18n.language,
          family_id: 6,
          pipeline_id: selectedPipeline?.value,
          // field_system_id: 55,
        });

        setStatsClosedTickets(res.data);
      } catch (err) {}
    };
    if (selectedPipeline?.value) {
      fetchData();
    }
  }, [start, end, i18n.language, selectedPipeline.value]);
  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await generateAxios(
          `${URL_ENV?.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
        ).post(`/get_agent_closed_family`, {
          date_start: start,
          date_end: end,
          lang: i18n.language,
          family_id: 6,
          pipeline_id: selectedPipeline?.value,
          // field_system_id: 55,
        });
        setagentsClosedTickets(res.data);
      } catch (err) {}
    };
    if (selectedPipeline && !isGuestConnected()) {
      fetchData();
    }
  }, [start, end, i18n.language, selectedPipeline.value]);
  //no
  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await getStatByFamily(
          start,
          end,
          6,
          selectedPipeline?.value
        );
        setStatsByFamily({
          ...res.data.data,
          drilldown: {
            series: Object.keys(res.data.data.drilldown).map((pipeline) => ({
              id: pipeline,
              data: res.data.data.drilldown[pipeline].data, // Drilldown data
            })),
          },
        });
      } catch (err) {}
    };
    if (selectedPipeline?.value) fetchData();
  }, [start, end, i18n.language, selectedPipeline.value]);
  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await getStatFamilyByFieldInTicket(57);

        setStatsPriorities(res.data?.data);
      } catch (err) {}
    };
    if (selectedPipeline && !isGuestConnected()) {
      fetchData();
    }
  }, [start, end, i18n.language, selectedPipeline.value]);
  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await getStatFamilyByFieldInTicket(52);

        setStatsPerimiters(res.data?.data);
      } catch (err) {}
    };
    if (selectedPipeline && !isGuestConnected()) {
      fetchData();
    }
  }, [start, end, i18n.language, selectedPipeline.value]);
  //no
  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await getStatFamilyByFieldInTicket(54);

        setStatsTypes(res.data?.data);
      } catch (err) {}
    };
    if (selectedPipeline?.value) {
      fetchData();
    }
  }, [start, end, i18n.language, selectedPipeline.value]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await getStatFamilyByFieldInTicket(55);

        setStatsChannels(res.data?.data);
      } catch (err) {}
    };
    if (selectedPipeline && !isGuestConnected()) {
      fetchData();
    }
  }, [start, end, i18n.language, selectedPipeline.value]);
  //no

  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await generateAxios(
          `${URL_ENV?.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
        ).post(`/get_topclient_family`, {
          date_start: start,
          date_end: end,
          lang: i18n.language,
          family_id: 6,
          pipeline_id: selectedPipeline?.value,
          // field_system_id: 55,
        });
        setTopClient(res.data);
      } catch (err) {}
    };
    if (selectedPipeline && !isGuestConnected()) {
      fetchData();
    }
  }, [start, end, i18n.language, selectedPipeline.value]);
  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await getStatFamilyByFieldInTicket(59);
        setStatsProducts(res.data?.data);
      } catch (err) {}
    };
    if (selectedPipeline && !isGuestConnected()) {
      fetchData();
    }
  }, [start, end, i18n.language, selectedPipeline.value]);

  // useEffect(() => {
  //   const fetchData = async () => {
  //     try {
  //       const res = await generateAxios(
  //         `${URL_ENV?.REACT_APP_BASE_URL}${process.env.REACT_APP_SUFFIX_API}`
  //       ).post(`/get_stat_family`, {
  //         date_start: start,
  //         date_end: end,
  //         lang: i18n.language,
  //         family_id: 6,
  //       });

  //       setStatsByFamily({
  //         ...res.data.data,
  //         drilldown: {
  //           series: Object.keys(res.data.data.drilldown).map((pipeline) => ({
  //             id: pipeline,
  //             data: res.data.data.drilldown[pipeline].data, // Drilldown data
  //           })),
  //         },
  //       });
  //     } catch (err) {}
  //   };
  //   fetchData();
  // }, [start, end, i18n.language]);

  return (
    <Spin spinning={loading}>
      <Card
        style={{
          // background: "#F8FAFC",
          border: 0,
          paddingLeft: from === "guest" ? 8 : 0,
          paddingRight: from === "guest" ? 8 : 0,
        }}
        styles={{ body: { padding: "6px 0px" } }}
        title={
          <div className="mb-3 flex max-w-[350px] flex-col  justify-start gap-y-0.5">
            <Typography.Text>
              {t("emailTemplates.plsSelect")} pipeline
            </Typography.Text>
            <Select
              showSearch
              popupMatchSelectWidth={false}
              filterOption={(input, option) =>
                (option?.label ?? "")
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
              style={{ minWidth: 250 }}
              onChange={(value, data) => {
                dispatch(
                  setSelectedPipelineInTicket({
                    label: data.label,
                    value: data.value,
                  })
                );
              }}
              value={selectedPipeline?.value}
              options={pipelines}
            />
          </div>
        }
      >
        <div
          style={{
            height: `calc(100vh - ${
              from.includes("drawer") ? "164" : from === "guest" ? "192" : "250"
            }px)`,
          }}
          className={`flex flex-col gap-4 overflow-y-auto overflow-x-hidden`}
        >
          <Row gutter={[16, 16]}>
            {!loadCardsTickets
              ? cardsTickets.map((el, i) => (
                  <Col
                    span={from === "drawer" ? 12 : 6}
                    key={`cardsTickets_Col_${i}`}
                  >
                    <Card
                      bordered={true}
                      // style={{ background: el.color }}
                    >
                      <Statistic
                        title={
                          <div className="flex items-center justify-between">
                            <span className="font-semibold">
                              {el.name?.toUpperCase()}
                            </span>
                            <LucideIcon iconName={el?.icon} color={el?.color} />
                          </div>
                        }
                        value={el?.value}
                        precision={0}
                        valueStyle={{
                          color: el?.color,
                        }}
                        // prefix={<ArrowUpOutlined />}
                        // suffix="%"
                      />
                    </Card>
                  </Col>
                ))
              : Array.from(
                  { length: from === "drawer" ? 2 : 4 },
                  () => null
                ).map((_, index) => (
                  <Col
                    style={{ flex: from === "drawer" ? "0 0 50%" : "0 0 25%" }}
                    key={`load_cardsTickets_Col_${index}`}
                  >
                    <div className="skeletonSelectDepartments grow">
                      <Skeleton.Input
                        key={index}
                        active
                        style={{ width: "100%", height: "84px" }}
                      />
                    </div>
                  </Col>
                ))}
          </Row>
          <Row gutter={[16, 16]}>
            <Col className="gutter-row" span={from === "drawer" ? 12 : 6}>
              <CardStat title={statsClosedTickets?.name}>
                <GaugeChart
                  data={{
                    total: statsClosedTickets?.data?.total,
                    used_storage: statsClosedTickets?.data?.used,
                    title: statsClosedTickets?.data?.name,
                    unit: statsClosedTickets?.data?.name,
                  }}
                  title=""
                  name={statsClosedTickets?.name}
                  inverseColor={true}
                />
              </CardStat>
            </Col>
            <Col className="gutter-row" span={from === "drawer" ? 12 : 11}>
              {isGuestConnected() ? (
                <CardStat title={t("dashboard.ticket_by_type")}>
                  <BigDonutChart data={statsTypes?.data} name="" />
                </CardStat>
              ) : (
                <CardStat title={agentsClosedTickets?.name}>
                  <DualAxes
                    data={{ ...agentsClosedTickets, name: "" }}
                    // isExistDate={false}
                  />
                </CardStat>
              )}
            </Col>
            <Col span={from === "drawer" ? 12 : 7}>
              <Card
                title={
                  <div className="flex flex-col">
                    <div
                      style={{
                        fontSize: "1.2em",
                        color: "rgb(51, 51, 51)",
                        fontWeight: "bold",
                        fill: "rgb(51, 51, 51)",
                      }}
                    >
                      {statsByFamily?.name}
                    </div>
                    <div>
                      {" "}
                      {statsByFamily?.parent_name !== undefined &&
                      statsByFamily?.series?.find(
                        (el) => el.name === selectedPipeline.label
                      )?.y > 0
                        ? statsByFamily?.parent_name +
                          " " +
                          statsByFamily?.series?.find(
                            (el) => el.name === selectedPipeline.label
                          )?.y
                        : ""}
                    </div>
                  </div>
                }
              >
                <DonutChartWithDrillDown
                  data={{ ...statsByFamily, name: "" }}
                  subtitle={t("dashboard.independantFilter", {
                    name: "pipeline",
                  })}
                  selected={selectedPipeline.label}
                />
              </Card>
              {/* {isGuestConnected() ? (
                <DonutChartWithDrillDown
                  data={statsByFamily}
                  subtitle={t("dashboard.independantFilter", {
                    name: "pipeline",
                  })}
                  selected={namePipeline}
                />
              ) : (
                <DonutChartWithDrillDown
                  data={statsByFamily}
                  // subtitle={t("dashboard.independantFilter", {
                  //   name: "pipeline",
                  // })}
                  subtitle=""
                  selected={""}
                />
              )} */}
            </Col>
            {!isGuestConnected() && (
              <>
                <Col span={from === "drawer" ? 12 : 8}>
                  <CardStat title={t("dashboard.ticket_by_priority")}>
                    <PieChartWithLegend
                      data={{
                        ...statsPriorities,
                        name: "",
                      }}
                      withArrow={true}
                    />
                  </CardStat>
                </Col>
                <Col className="gutter-row" span={from === "drawer" ? 12 : 8}>
                  <CardStat title={t("dashboard.ticket_by_channel")}>
                    <OneBarChart
                      data={{
                        ...statsChannels,
                        name: "",
                      }}
                    />
                  </CardStat>
                </Col>
                <Col span={from === "drawer" ? 12 : 8}>
                  <CardStat
                    title={t("dashboard.ticket_by_perimiter")}
                    style={{
                      fontSize: "1.2em",
                      color: "rgb(51, 51, 51)",
                      fontWeight: "bold",
                      fill: "rgb(51, 51, 51)",
                    }}
                  >
                    <PieChartWithLegend
                      data={{
                        ...statsPermiters,
                        name: "",
                      }}
                      withArrow={true}
                    />
                  </CardStat>
                </Col>
                <Col span={from === "drawer" ? 12 : 8}>
                  <CardStat
                    title={t("dashboard.ticket_by_type")}
                    style={{
                      fontSize: "1.2em",
                      color: "rgb(51, 51, 51)",
                      fontWeight: "bold",
                      fill: "rgb(51, 51, 51)",
                    }}
                  >
                    <BigDonutChart data={statsTypes?.data} name="" />
                  </CardStat>
                </Col>

                <Col className="gutter-row" span={from === "drawer" ? 12 : 8}>
                  <CardStat title={topCLient?.name}>
                    <HorizontalOneBarChart
                      data={{ ...topCLient, name: "" }}
                      // isExistDate={false}
                    />
                  </CardStat>
                </Col>
                <Col span={from === "drawer" ? 12 : 8}>
                  <CardStat title={t("dashboard.ticket_by_product")}>
                    <PieChartWithLegend
                      data={{
                        ...statsProducts,
                        name: "",
                      }}
                      withArrow={true}
                    />
                  </CardStat>
                </Col>
                {selectedPipeline?.value ? (
                  <Col span={24}>
                    <TableStatsTickets
                      start={start}
                      end={end}
                      family_id={6}
                      pipeline_id={selectedPipeline?.value}
                    />
                  </Col>
                ) : null}
              </>
            )}
          </Row>
        </div>
      </Card>
    </Spin>
  );
};

export default DashboardTicket;
