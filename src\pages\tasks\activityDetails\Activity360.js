import {
  useState,
  useEffect,
  useMemo,
  memo,
  useRef,
  lazy,
  Suspense,
  useCallback,
} from "react";

import {
  Badge,
  Button,
  Layout,
  List,
  Popover,
  Space,
  Typography,
  Input,
  Modal,
  Tooltip,
  Form,
  Skeleton,
  Upload,
  message,
  Row,
  Checkbox,
  Select,
  Result,
  Tag,
  Cascader,
} from "antd";
import {
  ArrowLeftOutlined,
  ClockCircleOutlined,
  CopyOutlined,
  EditOutlined,
  LoadingOutlined,
  LoginOutlined,
  PaperClipOutlined,
  ShareAltOutlined,
  TeamOutlined,
  VideoCameraOutlined,
  SisternodeOutlined,
  MessageOutlined,
} from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { debounce } from "lodash";
import moment from "moment";
import { BiMessageRoundedError } from "react-icons/bi";
import { <PERSON><PERSON>ellOff, FiSearch } from "react-icons/fi";
import { MdEventBusy } from "react-icons/md";
import { MdVideoCameraFront } from "react-icons/md";

import MainService from "../../../services/main.service";
import {
  resetStateOtherUser,
  setChatSelectedConversation,
  setChatSelectedParticipants,
  setExternalItem,
} from "../../../new-redux/actions/chat.actions";
import {
  filterReadMessage,
  setOpenTaskRoomDrawer,
} from "../../../new-redux/actions/tasks.actions/handleTaskRoomDrawer";
import ChatConversations from "../../layouts/chat/conversation/body";
import InputChat from "../../layouts/chat/conversation/input";
import { ConnectedUsersListItem } from "../../../components/Chat/ConnectedUsersPopover/listItem";
import { AvatarChat } from "../../../components/Chat";
import { getName } from "../../layouts/chat/utils/ConversationUtils";
import { URL_ENV } from "index";
import RichTextInput from "components/tiptap_richtext/RichTextInput";
import UploadFiles from "components/UploadFiles";
import GeneralInfos from "./GeneralInfos";
import { getTokenRoom } from "new-redux/actions/visio.actions/createVisio";
import useInfoRoomInModules from "pages/layouts/chat/hooks/useInfoRoomInModules";
import { EXTENSIONS_ARRAY } from "../helpers/calculateSum";
import { closeDrawerChat } from "new-redux/actions/voip.actions/handleDrawerChat";
import ActionsComponent from "../ActionsComponent";
import { skl } from "../helpers/displaySkeletonInChat";
import { lazyRetry } from "utils/lazyRetry";
import SiderButton from "./SiderButton";
import ChoiceIcons from "pages/components/ChoiceIcons";
import useDebounce from "pages/components/UseDebounce/UseDebounce";
import { toastNotification } from "components/ToastNotification";

const AttachmentsList = lazy(
  () => lazyRetry(() => import("./AttachmentsList")),
  "AttachmentsList"
);
const HistoryFeed = lazy(
  () => lazyRetry(() => import("./HistoryFeed")),
  "HistoryFeed"
);
const ShareVisio = lazy(
  () => lazyRetry(() => import("./ShareVisio")),
  "ShareVisio"
);
const VisioRecordings = lazy(
  () => lazyRetry(() => import("./VisioRecordings")),
  "VisioRecordings"
);
const CheckList = lazy(
  () => lazyRetry(() => import("pages/components/DetailsProfile/CheckList")),
  "CheckList"
);

const { Sider, Header, Content } = Layout;
const { SHOW_CHILD } = Cascader;
const Activity360 = memo(function Activity360({
  openActivity360,
  setOpenActivity360,
  taskToUpdate,
  singleTaskData,
  setSingleTaskData,
  loadSpecificTask,
  tasksTypes,
  setTaskToUpdate,
  pipelines,
  guestsList,
  checkedItems,
  guestsSearchQuery,
  followersSearchQuery,
  setGuestsSearchQuery,
  guestsListPage,
  setGuestsListPage,
  guestsListLastPage,
  setCheckedItems,
  ownersList,
  checkedFollowers,
  setCheckedFollowers,
  setFollowersSearchQuery,
  loadOwners,
  loadGuests,
  setAddOnsValues,
  files,
  setFiles,
  setCountChanges,
  setSelectedFamilyMembers,
  source = "",
  form,
  setShowCardPopover = () => {},
  deletedTaskIndicator,
  setDeletedTaskIndicator = () => {},
  notFoundTaskIndicator,
  setNotFoundTaskIndicator = () => {},
  totalEntities,
  setOwnersList,
  followersListPage,
  setFollowersListPage,
}) {
  const { user } = useSelector((state) => state.user);
  const [searchParticipant, setSearchParticipant] = useState("");
  const [searchOnlineParticipant, setSearchOnlineParticipant] = useState("");
  const [chatRoomData, setChatRoomData] = useState(null);
  const [selectedChatParticipants, setSelectedChatParticipants] = useState([]);
  const [historyList, setHistoryList] = useState([]);
  const [selectedSider, setSelectedSider] = useState(
    user?.access["chat"] === "1" ? "chat" : "history"
  );
  const [logLastPage, setLogLastPage] = useState(null);
  const [logCurrentPage, setCurrentLogPage] = useState(1);
  const [reminderValidator, setReminderValidator] = useState(false);
  const [loadLogs, setLoadLogs] = useState(false);
  const [updateLabel, setUpdateLabel] = useState(false);
  const [showUploadList, setShowUploadList] = useState(true);
  const [siderWidth, setSiderWidth] = useState(null);
  const [openTodoList, setOpenTodoList] = useState(false);
  const [departments, setDepartments] = useState([]);
  const [exams, setExams] = useState([]);
  const [todoListRecords, setTodoListRecords] = useState({
    total: 0,
    done: 0,
  });
  const [tags, setTags] = useState([]);
  const [firstLoadExams, setFirstLoadExams] = useState(false);
  const selectedConversation = useSelector(
    (state) => state?.ChatRealTime?.selectedConversation
  );
  const selectedParticipants = useSelector(
    (state) => state.chat.selectedParticipants
  );
  const activitiesMessages = useSelector(
    (state) => state?.TasksRealTime?.activitiesMessages
  );
  const isUserNotified = useSelector(
    (state) => state?.TasksRealTime?.isUserNotified
  );
  const currentUser = useSelector((state) => state?.user);
  const onlineUsers = useSelector((state) => state?.ChatRealTime?.onlineUser);
  const openDrawerChat = useSelector(({ voip }) => voip.openDrawerChat);
  const dispatch = useDispatch();
  const location = useLocation();
  const navigate = useNavigate();
  const modalSiderRef = useRef(null);
  const timeoutRef = useRef(null);
  const [t] = useTranslation("common");
  const [messageApi, contextHolder] = message.useMessage();
  const { status, data, isFetched } = useInfoRoomInModules(
    singleTaskData?.id,
    singleTaskData?.can_create_room
  );

  // to close the drawer or bubble chat if it's open
  useEffect(() => {
    if (openActivity360) {
      const getData = async () => {
        // setLoadSpecificTask(true); // Start loading specific task
        setFirstLoadExams(true);
        try {
          const [departmentsResponse, tagsResponse, examsResponse] =
            await Promise.all([
              MainService.getDepartments(),
              MainService.getTagsTasks(),
              MainService.getDataFamilies({ family_id: 5 }),
            ]);

          // Set departments
          const departments = departmentsResponse.data.data.map((el) => ({
            ...el,
            value: el.id,
          }));
          setDepartments(departments);

          // Set tags
          const tags = tagsResponse.data.map((el) => ({
            label: el.label,
            value: el.id,
            icon: <ChoiceIcons icon={el.icon} />,
          }));
          setTags(tags);

          // Set exams
          setExams(examsResponse.data);
        } catch (err) {
          console.log(err);
          toastNotification("error", t("toasts.somethingWrong"));
        } finally {
          setFirstLoadExams(false);
          // setLoadSpecificTask(false);
        }
      };

      getData();
    }
    if (openActivity360 && openDrawerChat) dispatch(closeDrawerChat());
  }, [openActivity360]);

  useEffect(() => {
    if (status === "success" && isFetched) {
      dispatch(
        resetStateOtherUser({
          forced: true,
          keepDrawerOpened: false,
          item: null,
        })
      );
      dispatch(
        setChatSelectedParticipants({
          selectedParticipants: data?.data?.room?.participants ?? [],
        })
      );
      dispatch(
        setChatSelectedConversation({
          selectedConversation: {
            name: data?.data?.room?.name,
            description: data?.data?.room?.description,
            image: data?.data?.room?.image,
            admin_id: data?.data?.room?.admin_id,
            bot: null,
            id: data?.data?.room?._id,
            type: "room",
            source: "task",
            muted_status: false,
            external: false,
          },
        })
      );
      setChatRoomData({
        name: data?.data?.room?.name,
        description: data?.data?.room?.description,
        image: data?.data?.room?.image,
        admin_id: data?.data?.room?.admin_id,
        bot: null,
        id: data?.data?.room?._id,
        type: "room",
        mode: "members",
        muted_status: false,
        external: false,
      });
      setSelectedChatParticipants(data?.data?.room?.participants ?? []);
      let activatedRoom =
        activitiesMessages &&
        activitiesMessages?.unread_msg_room?.find(
          (room) => room?.room_id === data?.data?.room?._id
        );
      if (activatedRoom) {
        dispatch(
          setExternalItem({
            _id: activatedRoom?.room_id,
            total_unread: activatedRoom?.messages_count,
            last_message: { unread: 1 },
            tag_status: false,
          })
        );
        dispatch(filterReadMessage(data?.data?.room?._id));
      }
    }
  }, [
    dispatch,
    taskToUpdate,
    openActivity360,
    singleTaskData?.can_create_room,
    status,
    isFetched,
  ]);

  const connectedUser = useMemo(() => {
    let users = 0;
    if (selectedConversation?.id)
      selectedParticipants?.forEach((element) => {
        if (onlineUsers[element?.uuid] === "online") users++;
      });

    return users;
  }, [selectedConversation?.id, selectedParticipants, onlineUsers]);

  //Handle close modal side effects
  const onCloseModal = () => {
    // Reset state values
    setOpenActivity360(false);
    setSingleTaskData({});
    setTaskToUpdate(null);
    setCheckedItems([]);
    setCheckedFollowers([]);
    setAddOnsValues((prev) => ({ ...prev, note: "", description: "" }));
    setCurrentLogPage(1);
    setLogLastPage(null);
    setGuestsSearchQuery("");
    setFollowersSearchQuery("");
    setDeletedTaskIndicator(false);
    setNotFoundTaskIndicator(false);
    // Dispatch actions
    dispatch(setOpenTaskRoomDrawer(false));
    dispatch(setChatSelectedConversation({ selectedConversation: null }));
    // Reset form values and fields
    form.setFieldsValue({ taskType: null, title: null });
    form.resetFields();
    // Navigate to "/tasks" if needed
    const isTasksPage =
      location?.pathname?.includes("/tasks") &&
      location?.pathname.split("/")[2];
    if (isTasksPage) {
      navigate("/tasks");
    }
  };

  // On remove file trigger delete file
  const removeUploadedFile = async (fileObject) => {
    let fileIndex = files?.findIndex(
      (file) => file?.fileName === fileObject?.name
    );
    try {
      let formData = new FormData();
      formData.append("id", files[fileIndex]?.id || fileObject?.id);
      await MainService.deleteFileInTask(formData);
      setSingleTaskData((prev) => ({
        ...prev,
        upload: singleTaskData?.upload?.filter((file) =>
          fileObject?.id
            ? fileObject?.id !== file?.id
            : files[fileIndex]?.id !== file?.id
        ),
      }));
      setFiles(
        files?.filter((file) =>
          fileObject?.id
            ? fileObject?.id !== file?.id
            : files[fileIndex]?.id !== file?.id
        )
      );
      setCountChanges((prev) => prev + 1);
    } catch (error) {
      console.log(`Error ${error}`);
    }
  };
  const updateSelectedTags = async (values) => {
    try {
      let payload = new FormData();
      if (values.length > 0) {
        values.forEach((el) => payload.append("tags_ids[]", el));
      } else payload.append("tags_ids[]", values);
      const response = await MainService.updateActivityTags(
        singleTaskData?.id,
        payload
      );
      setSingleTaskData(response?.data?.data);
      setCountChanges((prev) => prev + 1);
    } catch (error) {
      console.log(`Error ${error}`);
    }
  };
  // Validate file size and extension before upload.
  const beforeUploadValidation = (file) => {
    let allowedExtensions = [
      "pdf",
      "docx",
      "xlsx",
      "pptx",
      "txt",
      "csv",
      "jpg",
      "jpeg",
      "png",
      "svg",
      "zip",
    ];
    // Maximum file size in MB
    const maxFileSize = 15; // 15MB
    const fileExt = file?.name?.split(".")?.pop()?.toLowerCase();
    const isAllowed = allowedExtensions?.includes(fileExt);
    const isSizeValid = file.size / 1024 / 1024 < maxFileSize;

    if (!isAllowed) {
      messageApi.open({
        duration: 3,
        type: "error",
        content: t("tasks.fileExtensionError"),
        style: {
          marginTop: "60vh",
        },
      });
      return Upload.LIST_IGNORE;
    }
    if (!isSizeValid) {
      messageApi.open({
        duration: 3,
        type: "error",
        content: t("chat.file.error_taile"),
        style: {
          marginTop: "60vh",
        },
      });
      return Upload.LIST_IGNORE;
    }

    return isAllowed && isSizeValid;
  };

  // Trigger upload-docs API on upload files in create task form.
  const uploadImage = async (options) => {
    const { onSuccess, onError, file, onProgress } = options;
    try {
      setShowUploadList(true);
      const fmData = new FormData();
      // Handle progress bar indicator.
      const config = {
        onUploadProgress: (event) => {
          onProgress({ percent: (event?.loaded / event?.total) * 100 });
        },
      };
      fmData.append("upload[]", file);
      fmData.append("task_id", singleTaskData?.id);
      const response = await MainService.uploadFileInTask(fmData, config);
      onSuccess("Ok");
      setFiles((prev) => [...prev, response?.data?.data[0]]);
      setSingleTaskData({
        ...singleTaskData,
        upload: [...singleTaskData?.upload, response?.data?.data[0]],
      });
      setCountChanges((prev) => prev + 1);
      getActivityHistory();
      if (selectedSider === "files") {
        setShowUploadList(false);
      }
    } catch (err) {
      console.log("Eroor: ", err);
      onError({ err });
      setShowUploadList(false);
    }
  };

  //Save description/note API.
  const saveContent = async (type, text) => {
    try {
      let payload = {
        type,
        text,
      };
      let response = await MainService.updateActivityNoteDescription(
        singleTaskData?.id,
        payload
      );
      setSingleTaskData(response?.data?.data);
    } catch (error) {
      console.log(`Error ${error}`);
    }
  };
  const updateActivityDepartment = async (value) => {
    try {
      let payload = {
        department_id: value,
      };
      let response = await MainService.updateActivityDepartment(
        singleTaskData?.id,
        payload
      );
      setSingleTaskData(response?.data?.data);
    } catch (error) {
      console.log(`Error ${error}`);
    }
  };
  const updateActivityCode = (e) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(async () => {
      try {
        let payload = {
          code: e.target.value,
        };
        let response = await MainService.updateActivityCode(
          singleTaskData?.id,
          payload
        );
        setSingleTaskData(response?.data?.data);
      } catch (error) {
        console.log(`Error ${error}`);
      }
    }, 500);
  };
  const updateActivityExam = async (values) => {
    const formData = new FormData();

    try {
      values.forEach((id) => {
        formData.append("exam_id[]", id);
      });

      let response = await MainService.updateActivityExam(
        singleTaskData?.id,
        formData
      );
    } catch (error) {
      console.log(`Error: ${error}`);
    }
  };
  //Get history
  const getActivityHistory = async () => {
    try {
      setLoadLogs(true);
      const response = await MainService.activityHistory(
        singleTaskData?.id,
        logCurrentPage
      );
      if (logCurrentPage === 1) {
        setHistoryList(response?.data?.data);
      } else {
        setHistoryList((prev) => [...prev, ...response?.data?.data]);
      }
      setLogLastPage(response?.data?.meta?.last_page);
      setLoadLogs(false);
    } catch (error) {
      setLoadLogs(false);
      console.log(`Error ${error}`);
    }
  };

  useEffect(() => {
    if (Object.keys(singleTaskData)?.length > 0 || isUserNotified) {
      getActivityHistory();
    }
  }, [singleTaskData, logCurrentPage, isUserNotified]);

  //The select displayed after (on the right side of) the reminder input.
  const reminderAddon = useMemo(
    () => (
      <Form.Item name="addonAfter" noStyle initialValue="minutes">
        <Select
          style={{ width: 100 }}
          placeholder={t("tasks.reminderSelect")}
          disabled={singleTaskData?.can_update_task === 0}
        >
          {["minutes", "hours", "days", "weeks"].map((value) => (
            <Select.Option key={value} value={value}>
              {value === "minutes" ? "Minutes" : t(`tasks.${value}`)}
            </Select.Option>
          ))}
        </Select>
      </Form.Item>
    ),
    [singleTaskData, t]
  );

  //Update reminder.
  const updateReminder = async (
    reminderValue = null,
    reminderParameter = null,
    source = null
  ) => {
    try {
      let reminderParamPayload = {
        reminder_before_end: form.getFieldValue("overviewReminderEndDate")
          ? 1
          : 0,
      };
      let payload = {
        reminder:
          (reminderValue && reminderValue.trim() === "") || !reminderParameter
            ? null
            : `${reminderValue} ${reminderParameter}`,
      };
      const response = await MainService.updateReminder(
        singleTaskData?.id,
        source === "beforeEnd" ? reminderParamPayload : payload
      );
      setSingleTaskData(response?.data?.data);
    } catch (error) {
      console.log(`Error ${error}`);
    }
  };

  const debouncedReminder = debounce((e) => {
    const { value } = e?.target;
    if (value.trim() === "") {
      setReminderValidator(true);
    }
    updateReminder(value, form.getFieldValue("addonAfter"));
  }, 500);

  useEffect(() => {
    if (Object.keys(singleTaskData)?.length > 0 && openActivity360) {
      form.setFieldsValue({
        overviewReminderEndDate: singleTaskData?.reminder_before_end === 1,
        overviewReminder: singleTaskData?.Reminder
          ? singleTaskData?.Reminder.split(" ")[0]
          : "30",
        addonAfter: singleTaskData?.Reminder
          ? singleTaskData?.Reminder.split(" ")[1]
          : "minutes",
        tags_ids: singleTaskData?.tags_ids,
        exam_id: singleTaskData?.exam_id?.map((item) => {
          return [Number(item[0]), item[1]];
        }),
        department_id: singleTaskData?.department_id || undefined,
        code: singleTaskData?.code,
      });
    }
  }, [form, openActivity360, singleTaskData]);

  const updateActivityLabel = async (newLabel) => {
    setUpdateLabel(true);
    try {
      const urlEncoded = new URLSearchParams();
      urlEncoded.append("label", newLabel);
      const response = await MainService.updateTaskLabel(
        singleTaskData?.id,
        urlEncoded
      );
      setSingleTaskData(response?.data?.data);
      setCountChanges((prev) => prev + 1);
    } catch (error) {
      console.log(`Error ${error}`);
    } finally {
      setUpdateLabel(false);
    }
  };

  // Handle update activity's label.
  const handleSubmitNewTitle = () => {
    let trimmedNewString = newString?.trim();
    if (trimmedNewString === "" || trimmedNewString === singleTaskData?.label) {
      return;
    }
    updateActivityLabel(newString);
  };

  // Handle join visio meeting.
  const joinVisioMeeting = () => {
    dispatch(
      getTokenRoom({
        room: singleTaskData?.location,
        errorText1: t("toasts.errorFetchApi"),
        errorText2: t("toasts.errorRoomNotFound"),
      })
    );
  };

  // Handle close card popover (in calendar view) on open the modal.
  const handleSideEffectsOnOpenModal = (open) => {
    if (open) setShowCardPopover({});
  };

  // Prevent the user from typing non numeric characters in reminder input.
  const preventTypingNonNumeric = (e) => {
    if (!/^[0-9]$/.test(e.key)) {
      e.preventDefault();
    } else {
      setReminderValidator(false);
    }
  };

  // Return to chat room after opening a o2o in activity.
  const returnToPreviousChatRoom = () => {
    dispatch(
      setChatSelectedConversation({
        selectedConversation: {
          ...chatRoomData,
        },
      })
    );
    dispatch(
      setChatSelectedParticipants({
        selectedParticipants: selectedChatParticipants,
      })
    );
  };

  const deleteRecord = async (recordId) => {
    try {
      const response = await MainService.deleteVisioRecord(
        singleTaskData?.id,
        recordId
      );
      setSingleTaskData(response?.data?.data);
      setCountChanges((prev) => prev + 1);
    } catch (error) {
      console.log(`Error ${error}`);
    }
  };

  useEffect(() => {
    if (openActivity360 && modalSiderRef.current) {
      const updateHeight = () => {
        setSiderWidth(modalSiderRef.current.clientWidth);
      };
      updateHeight();
    }
  }, [openActivity360]);
  const examsOptionsInSelect = exams?.map((item) => ({
    label: item.label,
    value: item.id,
    children: item.items.map((subItem) => ({
      label: subItem.label,
      value: subItem._id,
    })),
  }));

  let newString;
  const filter = (inputValue, path) => {
    return path.some(
      (option) =>
        option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1
    );
  };
  return (
    <Modal
      id="activity-overview-modal"
      open={openActivity360}
      onCancel={onCloseModal}
      width={"95%"}
      className="activity-360-modal-layout ml-[3rem] h-auto overflow-hidden"
      centered
      footer={null}
      title={
        !deletedTaskIndicator &&
        !notFoundTaskIndicator && (
          <div className="grid grid-cols-6">
            <div className="col-span-4 flex w-[90%] flex-col">
              <Typography.Title
                className="m-0 whitespace-normal break-words"
                level={3}
                disabled={updateLabel}
                ellipsis={{
                  tooltip: {
                    title: singleTaskData?.label,
                  },
                }}
                editable={
                  singleTaskData?.can_update_task === 1
                    ? {
                        icon: <EditOutlined style={{ fontSize: "14px" }} />,
                        tooltip: t("tasks.rename"),
                        triggerType: ["icon"],
                        text: singleTaskData?.label,
                        onChange: (key) => {
                          newString = key;
                        },
                        onEnd: handleSubmitNewTitle,
                      }
                    : false
                }
                copyable={{
                  icon: <CopyOutlined style={{ fontSize: "14px" }} />,
                  text: `${URL_ENV?.REACT_APP_DOMAIN}/tasks/${singleTaskData?.id}`,
                  tooltips: [t("tasks.copyLink"), t("visio.linkClicked")],
                }}
              >
                {singleTaskData?.label}
              </Typography.Title>
              {Object.keys(singleTaskData)?.length > 0 &&
                Number(singleTaskData?.tasks_type_id) === 3 && (
                  <div className="flex flex-row justify-items-end">
                    <Button
                      style={{
                        marginTop: "16px",
                      }}
                      target="_blank"
                      type="primary"
                      icon={<LoginOutlined />}
                      onClick={joinVisioMeeting}
                    >
                      {t("chat.header.visio.join")}
                    </Button>
                    {singleTaskData?.visio_in_progress &&
                    singleTaskData?.visio_in_progress === 1 ? (
                      <>
                        <Tag
                          icon={
                            <MdVideoCameraFront className="-mb-1 mr-1.5 animate-pulse text-sm" />
                          }
                          color="error"
                          style={{
                            marginLeft: "20px",
                            marginTop: "29px",
                          }}
                          bordered={false}
                        >
                          {t("tasks.visioInProgress")}
                        </Tag>
                      </>
                    ) : null}
                  </div>
                )}
            </div>
            <div className="col-span-2 justify-items-end pr-1">
              {Object.keys(singleTaskData)?.length === 0 &&
              taskToUpdate !== null ? (
                <Space>
                  <Skeleton.Avatar
                    active={true}
                    size="default"
                    shape="circle"
                  />
                  <Skeleton.Input active={true} size="default" />
                </Space>
              ) : (
                <div
                  className="flex flex-row items-center text-[13px] text-[#000000A6]"
                  type="secondary"
                >
                  {t("tasks.drawerHeaderOne")}{" "}
                  {loadSpecificTask &&
                  Object.keys(singleTaskData)?.length === 0 ? (
                    <LoadingOutlined />
                  ) : (
                    <ActionsComponent elementValue={singleTaskData?.creator}>
                      <AvatarChat
                        className={"mx-1.5 flex items-center justify-center"}
                        fontSize={"0.875rem"}
                        height={"32px"}
                        width={"32px"}
                        url={`${
                          URL_ENV?.REACT_APP_BASE_URL +
                          URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
                        }${singleTaskData?.creator?.avatar}`}
                        hasImage={EXTENSIONS_ARRAY?.includes(
                          singleTaskData?.creator?.avatar?.split(".")?.pop()
                        )}
                        name={getName(singleTaskData?.creator?.label, "avatar")}
                        type="user"
                      />
                    </ActionsComponent>
                  )}{" "}
                  {loadSpecificTask &&
                  Object.keys(singleTaskData)?.length === 0 ? (
                    <LoadingOutlined />
                  ) : (
                    <Typography.Text mark>
                      {t("tasks.creatorInfoTip", {
                        date: moment(singleTaskData?.create_at).format(
                          currentUser?.user?.location?.date_format
                        ),
                        time: moment(singleTaskData?.create_at).format(
                          currentUser?.user?.location?.time_format
                        ),
                      })}
                    </Typography.Text>
                  )}
                </div>
              )}
            </div>
          </div>
        )
      }
      afterOpenChange={handleSideEffectsOnOpenModal}
    >
      {taskToUpdate &&
      Object.keys(singleTaskData)?.length === 0 &&
      deletedTaskIndicator ? (
        <Result
          icon={<MdEventBusy style={{ fontSize: "72px", color: "red" }} />}
          title={t("tasks.activityDeleted")}
        />
      ) : notFoundTaskIndicator ? (
        <Result
          icon={<MdEventBusy style={{ fontSize: "72px", color: "red" }} />}
          title={t("tasks.userRemovedFromActivity")}
        />
      ) : (
        <>
          {contextHolder}
          <Layout className="h-[80vh] bg-[#fff]">
            {/* Main overview (details, text editors, upload files) */}
            <Layout className="overflow-auto bg-white">
              <Header className="hidden bg-white p-0">
                <div className="right-0 flex w-[100%] justify-end p-2"></div>
              </Header>
              <Content>
                {loadSpecificTask &&
                Object.keys(singleTaskData)?.length === 0 ? (
                  <Skeleton
                    avatar
                    active
                    paragraph={{
                      rows: 4,
                    }}
                  />
                ) : (
                  <>
                    <Form
                      form={form}
                      layout="vertical"
                      name="activity360Details"
                      style={{ paddingBottom: "50px" }}
                    >
                      <GeneralInfos
                        singleTaskData={singleTaskData}
                        tasksTypes={tasksTypes}
                        pipelines={pipelines}
                        guestsList={guestsList}
                        checkedItems={checkedItems}
                        guestsSearchQuery={guestsSearchQuery}
                        followersSearchQuery={followersSearchQuery}
                        setGuestsSearchQuery={setGuestsSearchQuery}
                        guestsListPage={guestsListPage}
                        setGuestsListPage={setGuestsListPage}
                        guestsListLastPage={guestsListLastPage}
                        setCheckedItems={setCheckedItems}
                        setSingleTaskData={setSingleTaskData}
                        ownersList={ownersList}
                        checkedFollowers={checkedFollowers}
                        setCheckedFollowers={setCheckedFollowers}
                        setFollowersSearchQuery={setFollowersSearchQuery}
                        loadOwners={loadOwners}
                        loadGuests={loadGuests}
                        form={form}
                        setCountChanges={setCountChanges}
                        setSelectedFamilyMembers={setSelectedFamilyMembers}
                        source={source}
                        openActivity360={openActivity360}
                        onCloseModal={onCloseModal}
                        totalEntities={totalEntities}
                        setOwnersList={setOwnersList}
                        followersListPage={followersListPage}
                        setFollowersListPage={setFollowersListPage}
                      />
                      <Row className="mt-2 grid w-full grid-cols-3 gap-2 pr-2">
                        <Form.Item
                          label={
                            currentUser?.user?.tenant === "spheredev2" ||
                            currentUser?.user?.tenant === "taoufikhospitals"
                              ? t("tasks.clinics")
                              : t("voip.departments")
                          }
                          name="department_id"
                        >
                          {firstLoadExams ? (
                            <div className="skeletonSelectDepartments">
                              <Skeleton.Input
                                style={{
                                  width: "100%",
                                  minWidth: 150,
                                  display: "inline-flex",
                                }}
                              />
                            </div>
                          ) : (
                            <Select
                              showSearch
                              style={{
                                width: "100%",
                              }}
                              filterOption={(input, option) =>
                                (option?.label ?? "")
                                  .toLowerCase()
                                  .includes(input.toLowerCase())
                              }
                              placeholder={
                                currentUser?.user?.tenant === "spheredev2" ||
                                currentUser?.user.tenant === "taoufikhospitals"
                                  ? t("tasks.selectClinic")
                                  : t("services.selectdepartment")
                              }
                              options={departments}
                              onChange={updateActivityDepartment}
                              allowClear
                            />
                          )}
                        </Form.Item>
                        {singleTaskData?.tasks_type_id !== 3 && (
                          <Form.Item label={t("menu2.tags")} name="tags_ids">
                            {firstLoadExams ? (
                              <div className="skeletonSelectDepartments">
                                <Skeleton.Input
                                  style={{
                                    width: "100%",
                                    minWidth: 150,
                                    display: "inline-flex",
                                  }}
                                />
                              </div>
                            ) : (
                              <Select
                                allowClear
                                placeholder={t("tasks.selectTags")}
                                // style={{ maxWidth: 300 }}
                                mode="multiple"
                                showSearch
                                options={tags}
                                loading={firstLoadExams}
                                filterOption={(input, option) => {
                                  var _a;
                                  return (
                                    (_a =
                                      option === null || option === void 0
                                        ? void 0
                                        : option.label) !== null &&
                                    _a !== void 0
                                      ? _a
                                      : ""
                                  )
                                    .toLowerCase()
                                    .includes(input.toLowerCase());
                                }}
                                optionRender={(option) => (
                                  <Space>
                                    <span
                                      role="img"
                                      aria-label={option.data.label}
                                    >
                                      {option.data.icon}
                                    </span>
                                    {option.data.label}
                                  </Space>
                                )}
                                onChange={updateSelectedTags}
                                // onClear={handleResetRolesSelect}
                              />
                            )}
                          </Form.Item>
                        )}

                        <Form.Item
                          label={
                            currentUser?.user?.tenant === "spheredev2" ||
                            currentUser?.user.tenant === "taoufikhospitals"
                              ? t("tasks.exams")
                              : t("tasks.products")
                          }
                          name="exam_id"
                        >
                          {firstLoadExams ? (
                            <div className="skeletonSelectDepartments">
                              <Skeleton.Input
                                style={{
                                  width: "100%",
                                  minWidth: 150,
                                  display: "inline-flex",
                                }}
                              />
                            </div>
                          ) : (
                            <Cascader
                              style={{ width: "100%" }}
                              options={examsOptionsInSelect}
                              showSearch={{
                                filter,
                              }}
                              onChange={updateActivityExam}
                              multiple
                              maxTagCount="responsive"
                              showCheckedStrategy={SHOW_CHILD}
                            />
                          )}
                        </Form.Item>

                        <Tooltip
                          title={
                            <div
                              dangerouslySetInnerHTML={{
                                __html: t("toasts.reminderTooltip"),
                              }}
                            />
                          }
                          open={reminderValidator}
                        >
                          <Form.Item
                            label={t("tasks.reminder")}
                            name="overviewReminder"
                          >
                            <Input
                              min={1}
                              placeholder="Reminder"
                              addonAfter={reminderAddon}
                              disabled={
                                Object.keys(singleTaskData).length > 0 &&
                                singleTaskData?.can_update_task === 0
                              }
                              style={{ width: "100%" }}
                              onKeyPress={preventTypingNonNumeric}
                              onChange={(e) => {
                                debouncedReminder(e);
                              }}
                              onBlur={() => {
                                setReminderValidator(false);
                              }}
                            />
                          </Form.Item>
                        </Tooltip>
                        <Form.Item label="Code" name="code">
                          <Input
                            placeholder="Code"
                            onChange={updateActivityCode}
                          />
                        </Form.Item>
                      </Row>
                      <Row>
                        <Form.Item
                          name="overviewReminderStartDate"
                          valuePropName="checked"
                          initialValue={true}
                        >
                          <Checkbox disabled checked>
                            {t("tasks.reminderBeforeStart")}
                          </Checkbox>
                        </Form.Item>
                        <Tooltip
                          title={t("tasks.reminderInfo")}
                          placement="right"
                        >
                          <Form.Item
                            name="overviewReminderEndDate"
                            valuePropName="checked"
                          >
                            <Checkbox
                              disabled={
                                Object.keys(singleTaskData).length > 0 &&
                                singleTaskData?.can_update_task === 0
                              }
                              onChange={() =>
                                updateReminder(
                                  null,
                                  null,
                                  (source = "beforeEnd")
                                )
                              }
                            >
                              {t("tasks.reminderBeforeDue")}
                            </Checkbox>
                          </Form.Item>
                        </Tooltip>
                      </Row>
                      <Form.Item
                        name="description"
                        label="Description"
                        help={t("tasks.descriptionHelp")}
                        className="activity-description-details mr-[20px] pb-[15px]"
                      >
                        <span>
                          <RichTextInput
                            key={`description_${singleTaskData?.id}`}
                            showEmojiInEditor={false}
                            source="tasks"
                            setEditorContent={() => {}}
                            saveContent={(data) =>
                              saveContent("description", data)
                            }
                            editorContent={singleTaskData?.description}
                            editable={singleTaskData?.can_update_task === 1}
                          />
                        </span>
                      </Form.Item>
                      <Form.Item
                        name="note"
                        help={t("tasks.noteHelp")}
                        label="Note"
                        className="activity-note-details mr-[20px] pb-[15px]"
                      >
                        <span>
                          <RichTextInput
                            key={`note_${singleTaskData?.id}`}
                            showEmojiInEditor={false}
                            source="tasks"
                            setEditorContent={() => {}}
                            saveContent={(data) => saveContent("note", data)}
                            editorContent={singleTaskData?.note}
                            editable={singleTaskData?.can_update_task === 1}
                          />
                        </span>
                      </Form.Item>
                      <Form.Item
                        name="upload"
                        // label={t("tasks.upload")}
                        label={
                          <div className="flex flex-col justify-start">
                            {t("tasks.upload")}{" "}
                            <span className="text-[11px] text-[#999]">
                              {t("tasks.uploadRulesInfo")}
                            </span>
                          </div>
                        }
                        style={{ marginRight: "20px" }}
                      >
                        <UploadFiles
                          customRequest={uploadImage}
                          multiple={true}
                          listType="picture"
                          maxCount={6}
                          defaultFileList={
                            files?.length > 0
                              ? files?.map((el) => ({
                                  id: el?.id,
                                  name: el?.fileName,
                                  url: `${URL_ENV?.REACT_APP_BASE_URL}${el?.path}`,
                                }))
                              : []
                          }
                          showUploadList={showUploadList}
                          className="upload-list-inline"
                          removeUploadedFile={removeUploadedFile}
                          t={t}
                          disabled={
                            Object.keys(singleTaskData).length > 0 &&
                            singleTaskData?.can_update_task === 0
                          }
                          beforeUpload={beforeUploadValidation}
                          allowedFiles={[
                            "pdf",
                            "docx",
                            "xlsx",
                            "pptx",
                            "txt",
                            "csv",
                            "jpg",
                            "jpeg",
                            "png",
                            "gif",
                          ]}
                          directory={false}
                        />
                      </Form.Item>
                    </Form>
                  </>
                )}
              </Content>
            </Layout>
            {/* Overview sider (Chat, history, attachments, ...) */}
            <Sider
              width={"30%"}
              collapsedWidth={0}
              className="activity-sider-360 h-[100%] rounded-[6px]"
              ref={modalSiderRef}
            >
              <Layout className="rounded-[5px] bg-[#F8FAFC] p-[5px]">
                <Header>
                  {selectedSider === "chat" ? (
                    singleTaskData?.can_create_room === 1 && (
                      <Space
                        style={{
                          display: "flex",
                          justifyContent: "flex-start",
                          height: "100%",
                        }}
                      >
                        {selectedConversation?.type === "user" && (
                          <div className="">
                            <Button
                              icon={<ArrowLeftOutlined />}
                              type="text"
                              shape="circle"
                              onClick={returnToPreviousChatRoom}
                            />
                          </div>
                        )}
                        <Badge
                          offset={[-4, 40]}
                          style={{ width: "8px", height: "8px" }}
                        >
                          <AvatarChat
                            type={selectedConversation?.type}
                            fontBold="font-semibold"
                            hasImage={selectedConversation?.image}
                            height={12}
                            width={12}
                            size={52}
                            url={
                              selectedConversation?.type === "room"
                                ? URL_ENV?.REACT_APP_OAUTH_CHAT_API +
                                  process.env
                                    .REACT_APP_SUFFIX_CHAT_GROUP_STORAGE +
                                  selectedConversation?.image
                                : URL_ENV?.REACT_APP_BASE_URL +
                                  URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                                  selectedConversation?.image
                            }
                            name={getName(selectedConversation?.name, "avatar")}
                          />
                        </Badge>
                        <div className="flex flex-col justify-center p-1">
                          <Typography.Text
                            className="text-lg font-medium text-slate-800 first-letter:capitalize"
                            style={{
                              maxWidth: siderWidth - 70,
                              whiteSpace: "nowrap",
                            }}
                            ellipsis={{
                              tooltip: true,
                            }}
                          >
                            {" "}
                            {getName(selectedConversation?.name, "name")}{" "}
                            {selectedConversation?.muted_status && (
                              <FiBellOff
                                className="time"
                                style={{ fontSize: "13px" }}
                              />
                            )}{" "}
                          </Typography.Text>
                          {selectedConversation?.id &&
                            selectedConversation?.type === "room" && (
                              <div className="flex flex-row">
                                {selectedConversation?.id && (
                                  <Popover
                                    content={
                                      <div className="flex w-80 flex-col space-y-2">
                                        <Input
                                          size="middle"
                                          placeholder={t(
                                            "chat.searchSide.searchMembers"
                                          )}
                                          prefix={
                                            <FiSearch className="text-slate-500" />
                                          }
                                          value={searchParticipant}
                                          onChange={(e) =>
                                            setSearchParticipant(
                                              e.target.value
                                                .trimStart()
                                                .replace(/\s{1,} /g, " ")
                                            )
                                          }
                                          className="w-full flex-1"
                                          allowClear
                                          autoFocus={true}
                                        />
                                        <List
                                          className="membersList max-h-80 overflow-auto"
                                          dataSource={selectedParticipants?.filter(
                                            (el) =>
                                              getName(el.name, "name")
                                                ?.toLowerCase()
                                                ?.includes(
                                                  searchParticipant.toLowerCase()
                                                )
                                          )}
                                          renderItem={(item) => (
                                            <ConnectedUsersListItem
                                              setPreviousRoomParticipants={
                                                setSelectedChatParticipants
                                              }
                                              item={item}
                                              source="chat"
                                            />
                                          )}
                                        />
                                      </div>
                                    }
                                    trigger="click"
                                  >
                                    <Button
                                      type="text"
                                      className="space-x-1 transition duration-300"
                                    >
                                      <TeamOutlined className="text-gray-400" />
                                      <span className="text-gray-400">
                                        {selectedParticipants?.length}
                                      </span>
                                      <span className="text-gray-400">
                                        {t("chat.header.members")}
                                      </span>
                                    </Button>
                                  </Popover>
                                )}
                                {selectedConversation?.id && (
                                  <Popover
                                    content={
                                      <div className="flex w-80 flex-col space-y-2">
                                        <Input
                                          size="middle"
                                          placeholder={t(
                                            "chat.searchSide.searchMembers"
                                          )}
                                          prefix={
                                            <FiSearch className="text-slate-500" />
                                          }
                                          value={searchOnlineParticipant}
                                          onChange={(e) =>
                                            setSearchOnlineParticipant(
                                              e.target.value
                                                .trimStart()
                                                .replace(/\s{1,} /g, " ")
                                            )
                                          }
                                          className="w-full flex-1"
                                          allowClear
                                          autoFocus={true}
                                        />
                                        <List
                                          className="membersList max-h-80 overflow-auto"
                                          dataSource={
                                            selectedParticipants?.filter(
                                              (user) =>
                                                onlineUsers[user?.uuid] ===
                                                "online"
                                            ) &&
                                            selectedParticipants
                                              ?.filter(
                                                (user) =>
                                                  onlineUsers[user?.uuid] ===
                                                  "online"
                                              )
                                              ?.filter((el) =>
                                                getName(el.name, "name")
                                                  ?.toLowerCase()
                                                  ?.includes(
                                                    searchOnlineParticipant.toLowerCase()
                                                  )
                                              )
                                          }
                                          renderItem={(item) => (
                                            <ConnectedUsersListItem
                                              setPreviousRoomParticipants={
                                                setSelectedChatParticipants
                                              }
                                              item={item}
                                              source="chat"
                                            />
                                          )}
                                        />
                                      </div>
                                    }
                                    trigger="click"
                                  >
                                    <Button
                                      className="space-x-1 transition duration-300 hover:cursor-pointer hover:underline"
                                      type="text"
                                    >
                                      <Badge status="success" />
                                      <span className="text-gray-400">
                                        {" " + connectedUser}
                                      </span>
                                      <span className="text-gray-400">
                                        {t("chat.connected")}
                                      </span>
                                    </Button>
                                  </Popover>
                                )}
                              </div>
                            )}
                        </div>
                      </Space>
                    )
                  ) : (
                    <>
                      <Typography.Title level={3}>
                        {{
                          history: t("tasks.logFeedTitle"),
                          visioParams: t("visio.infoCnxTitle"),
                          visioRecordings: t("tasks.recordings"),
                          subtasks: t("tasks.subtasksTitle"),
                        }[selectedSider] || t("tasks.attachments", { s: "s" })}
                      </Typography.Title>
                      {/*{selectedSider === "subtasks" ? (
                        <div className="w-1/3">
                          <Progress
                            format={(number) =>
                              `${todoListRecords?.done} / ${todoListRecords?.total}`
                            }
                            percent={
                              (todoListRecords?.done / todoListRecords?.total) *
                              100
                            }
                          />
                        </div>
                      ) : null} */}
                    </>
                  )}
                </Header>
                <Content className="bg-[#F8FAFC]">
                  {selectedSider === "chat" ? (
                    singleTaskData?.can_create_room === 1 ? (
                      status === "loading" ? (
                        skl()
                      ) : status === "error" ? (
                        <div className="relative top-1/3 flex justify-center">
                          <div className="flex flex-col items-center">
                            <p className="text-red-500">
                              {t("tasks.loadRoomError")}
                            </p>
                          </div>
                        </div>
                      ) : (
                        selectedConversation?.id && (
                          <div className="flex h-[100%] flex-1 flex-col justify-between overflow-hidden">
                            <div className=" flex-1 overflow-hidden">
                              <ChatConversations source="no_chat" />
                            </div>
                            <InputChat source="no_chat" />
                          </div>
                        )
                      )
                    ) : (
                      singleTaskData?.can_create_room === 0 && (
                        <div className="relative top-1/3 flex h-full w-full justify-center">
                          <div className="flex flex-col items-center">
                            <BiMessageRoundedError className="text-[50px]" />
                            <p>{t("tasks.noChat")}</p>
                            <span className="text-[#8c8c8c]">
                              {t("tasks.noChatInfo")}
                            </span>
                          </div>
                        </div>
                      )
                    )
                  ) : selectedSider === "history" ? (
                    <Suspense className="w-full" fallback>
                      <HistoryFeed
                        historyList={historyList}
                        logLastPage={logLastPage}
                        logCurrentPage={logCurrentPage}
                        setCurrentLogPage={setCurrentLogPage}
                        loadLogs={loadLogs}
                      />
                    </Suspense>
                  ) : selectedSider === "visioParams" ? (
                    <Suspense fallback>
                      <ShareVisio
                        item={singleTaskData?.visio}
                        visioLink={singleTaskData?.location}
                      />
                    </Suspense>
                  ) : selectedSider === "visioRecordings" ? (
                    <Suspense className="w-full" fallback>
                      <VisioRecordings
                        items={singleTaskData?.recordings}
                        visioLink={singleTaskData?.location}
                        deleteRecord={deleteRecord}
                      />
                    </Suspense>
                  ) : selectedSider === "subtasks" ? (
                    <Suspense fallback>
                      <CheckList
                        openModal={openTodoList}
                        setOpenModal={setOpenTodoList}
                        setKpi={() => {}}
                        headerHeight={100}
                        from="activities"
                        contactInfo={{ id: singleTaskData?.id }}
                        setTodoListRecords={setTodoListRecords}
                        setCountChanges={setCountChanges}
                      />
                    </Suspense>
                  ) : (
                    <Suspense fallback>
                      <AttachmentsList
                        attachmentsList={files}
                        removeUploadedFile={removeUploadedFile}
                      />
                    </Suspense>
                  )}
                </Content>
              </Layout>
            </Sider>
            {/* Sider buttons */}
            <div className="flex flex-col pl-2">
              {user?.access?.["chat"] === "1" && (
                <SiderButton
                  selectedSider={selectedSider}
                  setSelectedSider={setSelectedSider}
                  sideKey="chat"
                  icon={<MessageOutlined />}
                  title="Chat"
                  showUploadList={true}
                  placement="top"
                />
              )}
              <SiderButton
                selectedSider={selectedSider}
                setSelectedSider={setSelectedSider}
                sideKey="history"
                icon={<ClockCircleOutlined />}
                title={t("tasks.historyBtn")}
                placement="left"
                showUploadList={true}
              />
              <SiderButton
                selectedSider={selectedSider}
                setSelectedSider={setSelectedSider}
                sideKey="files"
                icon={<PaperClipOutlined />}
                title={t("tasks.attachments", {
                  s: singleTaskData?.upload?.length === 1 ? "" : "s",
                })}
                badgeCount={files?.length}
                placement="left"
                showUploadList={false}
              />
              <SiderButton
                selectedSider={selectedSider}
                setSelectedSider={setSelectedSider}
                sideKey="subtasks"
                icon={<SisternodeOutlined />}
                title={t("tasks.subtasksTitle")}
                placement={
                  Number(singleTaskData?.tasks_type_id) === 3
                    ? "left"
                    : "bottom"
                }
                showUploadList={false}
              />
              {Number(singleTaskData?.tasks_type_id) === 3 &&
                singleTaskData?.recordings?.length > 0 && (
                  <SiderButton
                    selectedSider={selectedSider}
                    setSelectedSider={setSelectedSider}
                    sideKey="visioRecordings"
                    icon={
                      <Badge
                        offset={[4, 2]}
                        count={singleTaskData?.recordings?.length}
                        size="small"
                        color="#faad14"
                      >
                        <VideoCameraOutlined />
                      </Badge>
                    }
                    title={t("tasks.recordings")}
                    placement="left"
                    showUploadList={false}
                  />
                )}
              {Number(singleTaskData?.tasks_type_id) === 3 && (
                <SiderButton
                  selectedSider={selectedSider}
                  setSelectedSider={setSelectedSider}
                  sideKey="visioParams"
                  icon={<ShareAltOutlined />}
                  title={t("visio.infoCnx")}
                  placement="bottom"
                  showUploadList={false}
                />
              )}
            </div>
          </Layout>
        </>
      )}
    </Modal>
  );
});

export default Activity360;
