import React, { useEffect, useRef } from "react";
import { Form, Space, Tag, Switch, Table, DatePicker, Typography } from "antd";
import { useState } from "react";
import { generateAxios } from "../services/axiosInstance";
import { useTranslation } from "react-i18next";
import { toastNotification } from "./ToastNotification";
import { useDispatch, useSelector } from "react-redux";
import SearchInTable from "../pages/components/Search";
import { setSearch } from "../new-redux/actions/menu.actions/menu";
import moment from "moment";
import {
  disabledDate,
  disabledTime,
  handleDateTimeRange,
  humanDate,
  rangePresets,
} from "../pages/voip/helpers/helpersFunc";
import dayjs from "dayjs";
import { getName } from "../pages/layouts/chat/utils/ConversationUtils";
import { Audio } from "./Chat";
import { URL_ENV } from "index";
const { RangePicker } = DatePicker;

const LogActions = () => {
  const [loadSwitchUsed, setLoadSwitchUsed] = useState(false);
  const [switchId, setSwitchId] = useState("");
  const [data, setData] = useState([]);
  const [meta, setMeta] = useState({ current_page: 1, per_page: 20 });
  const [dateRange, setDateRange] = useState([null, null]);
  const [disableDateToStart, setDisableDateToStart] = useState(null);

  const [loading, setLoading] = useState(true);
  const [t] = useTranslation("common");
  const { search } = useSelector((state) => state.form);
  const { user } = useSelector((state) => state.user);

  const dispatch = useDispatch();
  const maxHeight = `calc(100vh - 300px)`;
  const [date, setDate] = useState({ start: "", end: "" });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
  });
  const [sorter, setSorter] = useState({
    field: null,
    order: null,
  });
  const showTime = {
    format: user?.location?.time_format ? user?.location?.time_format : "HH:mm",
    defaultValue: [dayjs("00:00", "HH:mm"), dayjs("23:59", "HH:mm")],
  };

  useEffect(() => {
    const getLogs = async () => {
      setLoading(true);
      try {
        const { data } = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get(
          `log-action?page=${meta.current_page}&limit=${
            meta.per_page || 20
          }&search=${search}&start=${date?.start.toUpperCase()}&end=${date?.end.toUpperCase()}`
        );
        setMeta(data.meta);
        setData(data.data);
        setDisableDateToStart(null);
        //.sort((a,b)=>b.selected-a.selected));
        setLoading(false);
      } catch (err) {
        setLoading(false);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    };
    if (search.length == 0 || search.length > 2) getLogs();
    // return () => dispatch(setSearch(""));
  }, [search, date]);
  useEffect(() => {
    return () => {
      dispatch(setSearch(""));
    };
  }, []);

  //   useEffect(() => {
  //     console.log(filter);
  //     setData(
  //       sortData(
  //         allData.filter((object) => {
  //           if (filter.selected && filter.selected.length > 0) {
  //             return filter.selected.includes(object.selected.toString());
  //           } else {
  //             return object;
  //           }
  //         }),
  //         sorter.field,
  //         sorter.order
  //       )
  //     );
  //   }, [sorter, filter, allData]);

  //   const handleTableChange = (pagination, filters, sorter) => {
  //     setPagination(pagination);
  //     setSorter({
  //       field: sorter.field,
  //       order: sorter.order,
  //     });
  //     setFilter(filters);
  //   };

  const columns = [
    // {
    //   title: "User",
    //   dataIndex: "user",
    //   key: "user",

    //   width: "300px",
    // },
    {
      title: "Action",
      dataIndex: "action",
      key: "action",
      fixed: "left",
      width: "70%",

      sorter: (a, b) => a.action.localeCompare(b.action),
      render: (
        _,
        {
          date,
          user,
          action_type,
          id_data,
          type,
          label_data,
          family_id,
          action,
        }
      ) =>
        !type || type === "note" ? (
          <div>
            <span>{getName(user, "name")}</span> {action}
          </div>
        ) : (
          <div>
            {/* ${
        item.action_type === "create"
          ? " bg-[#00800014]"
          : item.action_type === "update"
          ? "bg-[#ebf3fe] "
          : item.action_type === "delete"
          ? "bg-[#ff000d21]"
          : "bg-gray-50"
      } */}

            <div className="flex  items-center space-x-1 ">
              <span>{getName(user, "name")}</span>{" "}
              <span>
                {action_type === "create"
                  ? t("vue360.actionCreate")
                  : action_type === "update"
                  ? t("vue360.actionUpdate")
                  : action_type === "ANSWERED"
                  ? t("vue360.actionCall")
                  : action_type === "FAILED"
                  ? t("vue360.actionCall")
                  : action_type === "BUSY"
                  ? t("vue360.actionCall")
                  : action_type === "NO ANSWER"
                  ? t("vue360.actionCall")
                  : action_type === "CONGESTION"
                  ? t("vue360.actionCall")
                  : action_type === "delete"
                  ? t("vue360.actionDelete")
                  : action_type === "transfer"
                  ? t("vue360.transfer")
                  : action_type === "relaunch"
                  ? t("vue360.relaunch")
                  : action_type === "move"
                  ? t("vue360.move")
                  : action_type === "assign"
                  ? t("vue360.assign")
                  : action_type === "merge"
                  ? t("vue360.merge")
                  : action_type === "affectation"
                  ? t("vue360.affectation")
                  : action_type === "qualification"
                  ? t("vue360.qualification")
                  : action_type === "return"
                  ? t("vue360.return")
                  : action_type === "update_qualification"
                  ? t("vue360.update_qualification")
                  : action_type === "delete_qualification"
                  ? t("vue360.delete_qualification")
                  : action_type === "update_affectation"
                  ? t("vue360.update_affectation")
                  : action_type === "delete_affectation"
                  ? t("vue360.delete_affectation")
                  : action_type}{" "}
                {type !== "voip" ? t(`vue360.${type}`) : action_type}
              </span>
              <Space>
                <Typography.Text>
                  {label_data && typeof label_data === "string"
                    ? getName(label_data, "name")
                        ?.replace(/from (\d+)/, `${t("vue360.from")} $1`)
                        ?.replace(/in (\d+)/, `${t("vue360.to")} $1`)
                        ?.replace("with duration", t("vue360.withDuration"))
                    : action}
                </Typography.Text>
                {/* {family_id === "call" &&
                  data.map((el) => <Audio url={el} isFullURL={true} />)} */}
              </Space>
            </div>
            {/* {Object.keys(changes).length > 0 ? (
        <ChangesInteractions item={item} />
      ) : null} */}
          </div>
        ),
    },

    {
      title: "Date",
      dataIndex: "date",
      key: "date",
      render: (_, { date }) => humanDate(date, t),
    },
    {
      title: "Ip",
      dataIndex: "ip",
      key: "ip",
    },
  ];
  const handleChangePage = async (page, pageSize) => {
    setLoading(true);
    try {
      const { data } = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).get(
        `log-action?page=${page}&limit=${
          pageSize || 20
        }&search=${search}&start=${date?.start.toUpperCase()}&end=${date?.end.toUpperCase()}`
      );
      setMeta({ ...meta, current_page: page, per_page: pageSize });

      setMeta(data.meta);
      setData(data.data);

      //.sort((a,b)=>b.selected-a.selected));
      setLoading(false);
    } catch (err) {
      setLoading(false);
      toastNotification("error", t("toasts.somethingWrong"), "topRight");
    }
  };

  const handleChangePageSize = (current, size) => {
    setMeta({ ...meta, current_page: current, per_page: size });
    //   setCurrentPage(1);
    //   setPageSize(size);
  };
  const changeTime = (date, dateString) => {
    setMeta({ ...meta, current_page: 1 });
    setDate({ start: dateString[0], end: dateString[1] });
  };
  //   const sortedData = sortData(data, sorter.field, sorter.order);
  //   const paginatedData = sortedData.slice(
  //     (pagination.current - 1) * pagination.pageSize,
  //     pagination.current * pagination.pageSize
  //   );
  //   const filteredData = sortedData.filter((item) => {
  //     return (
  //       item.name_en.toLowerCase().includes(search.toLowerCase()) ||
  //       item.region.toLowerCase().includes(search.toLowerCase()) ||
  //       item.currency.toLowerCase().includes(search.toLowerCase()) ||
  //       item.dial_code.toLowerCase().includes(search.toLowerCase())
  //     );
  //   });
  const changePageNumber = (number) => {
    setMeta((prev) => ({ ...prev, current_page: number }));
  };

  return (
    <Space direction="vertical" style={{ width: "100%" }}>
      <div className="flex w-full justify-between p-4">
        <SearchInTable
          placeholder={t("table.search3")}
          setPageNumber={changePageNumber}
        />

        <DatePicker.RangePicker
          disabled={!data?.length && !dateRange[0] && !dateRange[1]}
          placement="bottomRight"
          presets={rangePresets(t)}
          onChange={(date, dateString) => {
            handleDateTimeRange(dateString, setDateRange, showTime !== null);
            changeTime(date, dateString);
          }}
          format={
            showTime !== null
              ? `${user?.location?.date_format} ${user?.location?.time_format}`
              : user?.location?.date_format
          }
          showTime={showTime}
          allowClear
          disabledDate={(current) => disabledDate(disableDateToStart, current)}
          // disabledTime={disabledTime}
        />
      </div>
      <Table
        scroll={{ x: 750, y: maxHeight }}
        // dataSource={filteredData.slice(
        //   (pagination.current - 1) * pagination.pageSize,
        //   pagination.current * pagination.pageSize
        // )}
        dataSource={data}
        columns={columns}
        // pagination={{ ...pagination, total: filteredData.length }}

        pagination={{
          current: meta.current_page,
          pageSize: meta.per_page,
          total: meta.total,
          onChange: handleChangePage,
          onShowSizeChange: handleChangePageSize,
          showSizeChanger: true,
          pageSizeOptions: ["10", "20", "50", "100"],
          hideOnSinglePage: data.length < 11 && true,
        }}
        loading={loading}
        // onChange={handleTableChange}
      />
    </Space>
  );
};
export default LogActions;
