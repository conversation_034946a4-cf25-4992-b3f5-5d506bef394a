import {
  Fragment,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import { useWindowSize } from "../../clients&users/components/WindowSize";
import { useTranslation } from "react-i18next";
import {
  Badge,
  Button,
  Checkbox,
  DatePicker,
  Divider,
  Dropdown,
  Empty,
  Input,
  Popover,
  Select,
  Space,
  Switch,
  Table,
  Tag,
  Tooltip,
  Typography,
} from "antd";
import {
  FilterTwoTone,
  InfoCircleOutlined,
  InfoCircleTwoTone,
  MessageOutlined,
  PhoneOutlined,
  SyncOutlined,
} from "@ant-design/icons";
import { FiCopy, FiMoreVertical, FiSearch } from "react-icons/fi";
import {
  CenterContents,
  billSecToHumanReadable,
  disabledDate,
  formatDatePickerRange,
  generateUrlToView360,
  handleDateTimeRange,
  handleLogGroupsQueues,
  handlePageSizeOptions,
  humanDate,
  rangePresets,
} from "../helpers/helpersFunc";
import { toastNotification } from "../../../components/ToastNotification";
import {
  getFirstCallDate,
  getGroupsAndQueues,
  getConfigQueuesAndGroups,
  getTags,
  getKpiCall,
  seenCallsOrVoicesOrGroups,
} from "../services/services";
import DisplayAvatar from "../components/DisplayAvatar";
import TagColumn from "../components/tag_column/TagColumn";
import AffectationColumn from "../components/affection_column/AffectationColumn";
import { debounce } from "lodash";
import { HighlightSearchW } from "../components";
import useActionCall from "../helpers/ActionCall";
import CreateTask from "../components/CreateTask";
import FormCreate from "pages/clients&users/components/FormCreate";
import DisplayElementInfo from "../components/DisplayElementInfo";
import { MdOutlinePersonAddAlt } from "react-icons/md";
import { CgUserlane } from "react-icons/cg";
import { HiOutlineBuildingOffice, HiOutlineUserGroup } from "react-icons/hi2";
import { FaUsers } from "react-icons/fa";
import { ImUsers } from "react-icons/im";
import DisplayModuleIconAndText from "../components/DisplayModuleIconAndText";
import { useNavigate } from "react-router-dom";
import { Tb360View } from "react-icons/tb";
import dayjs from "dayjs";
import ExportButton from "../components/ExportButton";
import { openDrawerChat } from "new-redux/actions/voip.actions/handleDrawerChat";
import CardStat from "pages/components/CardStat";
import {
  RESET_MISSED_QUEUE_GROUP,
  SET_RETURNED_QUEUE_MISSED_CALL,
} from "new-redux/constants";
import DurationColumnCallLog from "../components/DurationColumnCallLog";

const GroupsAndQueues = () => {
  //
  const navigate = useNavigate();
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const call = useActionCall();
  const windowSize = useWindowSize();
  const tableRef = useRef(null);
  //
  const user = useSelector(({ user }) => user?.user);
  const access = user.access || {};
  const newLogGroupsQueues = useSelector(({ voip }) => voip.newLogGroupsQueues);
  const newTagOrAffectationGroupsQueues = useSelector(
    ({ voip }) => voip.newTagOrAffectationGroupsQueues
  );
  const nbrMissedQueueGroup = useSelector(
    ({ voip }) => voip?.nbrMissedQueueGroup
  );
  const returnedQueueMissedCall = useSelector(
    ({ voip }) => voip?.returnedQueueMissedCall
  );
  //
  const [shouldFetchData, setShouldFetchData] = useState(true);
  const [dataSource, setDataSource] = useState([]);
  const [loadingTable, setLoadingTable] = useState(false);
  const [isDataExist, setIsDataExist] = useState(true);
  const [dateRange, setDateRange] = useState([null, null]);
  const [showTime, setShowTime] = useState(null);
  const [disableDateToStart, setDisableDateToStart] = useState(null);
  const [totalLogs, setTotalLogs] = useState(0);
  const [filterCalls, setFilterCalls] = useState([]);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(20);
  const [search, setSearch] = useState("");
  const [displaySearch, setDisplaySearch] = useState("");
  const [elementDetails, setElementDetails] = useState({});
  const [familyToAdd, setFamilyToAdd] = useState(null);
  const [nbrPhoneToAdd, setNbrPhoneToAdd] = useState(null);
  const [openDrawerCreate, setOpenDrawerCreate] = useState(false);
  const [openDrawerInfo, setOpenDrawerInfo] = useState(false);
  const [catchChange, setCatchChange] = useState(true);
  const [allTags, setAllTags] = useState([]);
  const [queuesAndGroups, setQueuesAndGroups] = useState([]);
  const [selectedGroup, setSelectedGroup] = useState("");

  const [callGroupsKpi, setCallGroupsKpi] = useState([]);

  const [openTask, setOpenTask] = useState(false);
  const [idCallForTask, setIdCallForTask] = useState("");
  const [taskId, setTaskId] = useState(null);
  const [externalSource, setExternalSource] = useState({});

  //
  // console.log({ queuesAndGroups });
  //
  useEffect(() => {
    setShouldFetchData(true);
    setIsDataExist(true);
  }, [page, search, filterCalls, dateRange, limit, catchChange, selectedGroup]);
  //
  useEffect(() => {
    setPage(1);
  }, [search, filterCalls, dateRange]);
  //
  const getLogGroupsAndQueues = useCallback(
    async (isMounted) => {
      if (!shouldFetchData) return;
      else if (isMounted)
        try {
          if (!allTags?.length) {
            const tags = await getTags();
            setAllTags(tags);
          }

          if (newLogGroupsQueues?.length)
            dispatch({ type: "RESET_LOG_GROUPS_QUEUES" });
          if (newTagOrAffectationGroupsQueues?.length)
            dispatch({ type: "RESET_TAG_GROUPS_QUEUES" });
          setLoadingTable(true);
          const {
            data: {
              data,
              meta: { total },
            },
          } = await getGroupsAndQueues(
            limit,
            page,
            search,
            dateRange?.[0],
            dateRange?.[1],
            filterCalls,
            selectedGroup
          );
          setTotalLogs(total);
          const result = handleLogGroupsQueues(data, user, t);
          !result?.length && setIsDataExist(false);
          setDataSource(result);
          if (data.length && !disableDateToStart) {
            const {
              data: { first_call_date },
            } = await getFirstCallDate("queue");
            setDisableDateToStart(first_call_date);
          }
          if (data.length && !queuesAndGroups.length) {
            const transformWithOptions = (items, categoryLabel) => {
              // Check if the items object is empty
              const isEmpty = Object.keys(items)?.length === 0;
              if (isEmpty) {
                return {
                  label: <span className="font-semibold">{categoryLabel}</span>,
                  options: [
                    {
                      disabled: true,
                      label: `No ${
                        categoryLabel === t("voip.groups")
                          ? t("voip.groups")
                          : t("voip.queues")
                      }`,
                    },
                  ],
                };
              } else
                return {
                  label: <span className="font-semibold">{categoryLabel}</span>,
                  options: Object.entries(items).map(([key, value]) => ({
                    isMember: value?.is_member,
                    label: `${key} ${
                      !!value.is_member ? `(${t("chat.header.member")})` : ``
                    }`,
                    value:
                      categoryLabel === t("voip.groups")
                        ? `group[]=${value?.number}`
                        : `queue[]=${value?.number}`,
                  })),
                };
            };

            const { data } = await getConfigQueuesAndGroups();
            if (data && Object.keys(data)?.length > 0) {
              setQueuesAndGroups([
                { label: t("voip.all"), value: "all" },
                transformWithOptions(
                  data?.groups ? data.groups : {},
                  t("voip.groups")
                ),
                transformWithOptions(
                  data?.queues ? data.queues : {},
                  t("voip.queues")
                ),
              ]);
              setSelectedGroup("all");
            }
          }
        } catch (err) {
          if (err?.response?.status === 401 || err?.code === "ERR_CANCELED")
            return;
          else {
            toastNotification("error", t("toasts.somethingWrong"), "topRight");
            throw new Error(err?.message ? err.message : err);
          }
        } finally {
          setLoadingTable(false);
          setShouldFetchData(false);
          returnedQueueMissedCall &&
            dispatch({
              type: SET_RETURNED_QUEUE_MISSED_CALL,
              payload: false,
            });
        }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [shouldFetchData]
  );

  useEffect(() => {
    let isMounted = true;
    getLogGroupsAndQueues(isMounted);
    return () => {
      isMounted = false;
    };
  }, [getLogGroupsAndQueues]);
  //
  // Fetch Call group Kpi
  const getCallGroupsKpi = useCallback(
    async (isMounted) => {
      if (!isMounted || !selectedGroup.includes("queue[]")) return;
      try {
        const [start, end] = dateRange;
        const {
          data: { data },
        } = await getKpiCall("queue", start, end);
        setCallGroupsKpi(data);
      } catch (err) {
        if (err?.response?.status === 401 || err?.code === "ERR_CANCELED")
          return;
        else {
          toastNotification("error", t("toasts.somethingWrong"), "topRight");
          throw new Error(err?.message ? err.message : err);
        }
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [dateRange, newLogGroupsQueues, selectedGroup]
  );

  useEffect(() => {
    let isMounted = true;
    getCallGroupsKpi(isMounted);
    //
    return () => {
      isMounted = false;
    };
  }, [getCallGroupsKpi]);
  //
  // Catch new log Groups Queues
  const catchNewCallGroups = useCallback(() => {
    if ((loadingTable && isDataExist) || !newLogGroupsQueues?.length) return;

    const shouldFetch =
      page === 1 &&
      !search?.length &&
      !filterCalls?.length &&
      !dateRange[0] &&
      !dateRange[1] &&
      !selectedGroup;

    if (!shouldFetch) {
      if (page === 1) setShouldFetchData(true);
      return;
    }

    setLoadingTable(true);
    const updateData = () => {
      const newItems = handleLogGroupsQueues(newLogGroupsQueues, user, t);

      const newIds = newItems.map((it) => it.id_appel);

      const filteredOld = dataSource.filter(
        (old) => !newIds.includes(old.id_appel)
      );

      setDataSource([...newItems, ...filteredOld]);

      setLoadingTable(false);
      dispatch({ type: "RESET_LOG_GROUPS_QUEUES" });
    };

    const timer = setTimeout(updateData, 100);
    return () => clearTimeout(timer);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [newLogGroupsQueues.length]);

  useEffect(() => {
    let isMounted = true;
    if (isMounted) {
      catchNewCallGroups();
    }
    return () => {
      isMounted = false;
    };
  }, [catchNewCallGroups]);

  // Catch new tag or affectation Groups Queues
  const catchNewTagOrAffectationGroups = useCallback(() => {
    if (!newTagOrAffectationGroupsQueues.length) return;
    else {
      const _id = newTagOrAffectationGroupsQueues?.[0]?._id;
      if (_id) {
        let index = dataSource.findIndex((obj) => obj._id === _id);
        if (index !== -1) {
          let newDataSource = [...dataSource];
          let newLine = handleLogGroupsQueues(
            newTagOrAffectationGroupsQueues,
            user,
            t
          );
          newDataSource[index] = newLine?.[0];
          setDataSource(newDataSource);
        }
      } else return;
      dispatch({ type: "RESET_TAG_GROUPS_QUEUES" });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [newTagOrAffectationGroupsQueues.length]);

  useEffect(() => {
    let isMounted = true;
    if (isMounted) {
      catchNewTagOrAffectationGroups();
    }
    return () => {
      isMounted = false;
    };
  }, [catchNewTagOrAffectationGroups]);

  // Re-fetch data if returnedQueueMissedCall true
  useEffect(() => {
    if (returnedQueueMissedCall) setShouldFetchData(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [returnedQueueMissedCall]);
  //
  // handle calls seen when unmount or leave the interface
  useEffect(() => {
    seenCallsOrVoicesOrGroups("queue_group");
    return () => {
      seenCallsOrVoicesOrGroups("queue_group");
    };
  }, []);
  //
  const handleNotifBadge = useCallback(() => {
    if (page === 1 && nbrMissedQueueGroup) {
      const timer = setTimeout(() => {
        dispatch({ type: RESET_MISSED_QUEUE_GROUP });
        clearTimeout(timer);
      }, 500);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [nbrMissedQueueGroup, page]);

  useEffect(() => {
    handleNotifBadge();
  }, [handleNotifBadge]);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedSearch = useCallback(
    debounce((nextValue) => setSearch(nextValue), 1000),
    []
  );

  const handleSearch = (event) => {
    const inputValue = event?.target?.value;
    setDisplaySearch(inputValue);
    const cleanInputValue = inputValue.trim();
    if (cleanInputValue?.length > 2) debouncedSearch(cleanInputValue);
    else debouncedSearch("");
  };

  //
  const handleAddItem = (familyId, phoneNbr) => {
    setFamilyToAdd(familyId);
    setNbrPhoneToAdd(phoneNbr);
    setOpenDrawerCreate(true);
  };
  //
  const handleDisplayElementInfo = (name, info) => {
    setElementDetails({
      label: name,
      id: info?.id,
      familyId: info?.familyId,
    });
    setOpenDrawerInfo(true);
  };
  //
  const handleSwitchChange = (checked) => {
    if (checked) {
      setShowTime({
        format: "HH:mm",
        defaultValue: [dayjs("00:00", "HH:mm"), dayjs("23:59", "HH:mm")],
      });
    } else {
      setShowTime(null);
    }
  };

  // Memoize the columns array to prevent unnecessary re-renders
  const columns = useMemo(
    () => [
      {
        // title: t("table.header.type"),
        dataIndex: "icon",
        key: "icon",
        width: "50px",
        fixed: "left",
        render: (icon, { key, tooltipMsg }) => (
          // <CenterContents key={key} vertical={"center"} horizontal={"center"}>
          <Fragment key={key}>
            <Tooltip title={tooltipMsg} placement="topLeft">
              {icon}
            </Tooltip>
          </Fragment>
          // </CenterContents>
        ),
      },
      {
        title: t("table.header.caller"),
        dataIndex: "src",
        key: "src",
        fixed: "left",
        // width: "27%",
        render: (_, record) => (
          <CallerColumn
            key={record.key}
            record={record}
            t={t}
            handleAddItem={handleAddItem}
            call={call}
            user={user}
            dispatch={dispatch}
            handleDisplayElementInfo={handleDisplayElementInfo}
            search={search}
            navigate={navigate}
          />
        ),
      },
      {
        title: t("voip.groupsOrQueues"),
        dataIndex: "groupInfo",
        key: "groupInfo",
        // width: "",
        fixed: "left",
        render: (
          { name, image, number },
          { key, isTreated, isGroup, isQueue, isMeTheSrc, isTreatedByMe }
        ) => (
          <div
            className="relative flex w-full flex-row items-center space-x-1.5"
            key={key}
          >
            <DisplayAvatar
              name={name}
              urlImg={image}
              size={38}
              icon={isQueue ? <ImUsers /> : isGroup ? <FaUsers /> : null}
            />
            <div
              className={`flex w-9/12 flex-col ${
                !isTreated && !isMeTheSrc ? "text-red-500" : ""
              }`}
            >
              <div className="flex flex-row space-x-0.5 ">
                {/* {isGroup ? (
                <Tooltip title={t("voip.callGroup")}>
                  <FaUsers
                    style={{
                      fontSize: 16,
                      cursor: "help",
                    }}
                  />
                </Tooltip>
              ) : isQueue ? (
                <Tooltip title={t("voip.queues")}>
                  <ImUsers
                    style={{
                      fontSize: 15,
                      cursor: "help",
                    }}
                  />
                </Tooltip>
              ) : null} */}
                <p className="w-11/12 truncate font-semibold">
                  {HighlightSearchW(name, search)}
                </p>
              </div>
              <p
                className={`font-medium leading-4 ${
                  !isTreated && !isMeTheSrc ? "text-red-500" : "text-slate-500"
                }`}
              >
                {HighlightSearchW(number, search)}
              </p>
            </div>
          </div>
        ),
      },
      {
        title: t("voip.treatedBy"),
        dataIndex: "dst",
        key: "dst",
        // width: "4%",
        fixed: "left",
        render: (
          { name, image, number },
          { key, isTreated, returned, isTreatedByMe }
        ) =>
          !isTreated ? (
            <div key={key} className="flex flex-row justify-between">
              <p className="font-semibold text-red-500">{t("voip.noAnswer")}</p>
              {returned ? (
                <Popover
                  placement="topRight"
                  content={
                    <div className="flex flex-col space-y-3">
                      <p className="font-semibold">{returned.status}</p>
                      <div className="flex flex-col space-y-1.5">
                        <Space>
                          <p className="text-slate-500">Date</p>
                          <p>{returned.date}</p>
                        </Space>

                        <Space>
                          <p className="text-slate-500">
                            {t("voip.recalledBy")}
                          </p>
                          <Space>
                            <DisplayAvatar
                              name={returned.name}
                              urlImg={returned.image}
                              size={32}
                            />
                            <div className="flex flex-col">
                              <p className="font-semibold">
                                {returned.id === user.id
                                  ? t("voip.me")
                                  : returned.name}
                              </p>
                              <p className="leading-4 text-slate-500">
                                {returned.number}
                              </p>
                            </div>
                            {returned.id !== user.id && (
                              <Space size={3}>
                                <Button
                                  disabled={
                                    !returned.uuid || returned.id === user.id
                                  }
                                  onClick={() =>
                                    dispatch(openDrawerChat(returned.uuid))
                                  }
                                  size="small"
                                  type="link"
                                  icon={
                                    <MessageOutlined style={{ fontSize: 14 }} />
                                  }
                                />

                                <Button
                                  onClick={() =>
                                    call(
                                      returned?.number,
                                      returned?.id,
                                      returned?.familyId
                                    )
                                  }
                                  disabled={
                                    returned?.number === `${user?.extension}`
                                  }
                                  size="small"
                                  type="link"
                                  icon={
                                    <PhoneOutlined
                                      rotate={100}
                                      style={{ fontSize: 16 }}
                                    />
                                  }
                                />
                              </Space>
                            )}
                          </Space>
                        </Space>
                      </div>
                    </div>
                  }
                >
                  {returned.displayIcon}
                </Popover>
              ) : null}
            </div>
          ) : (
            <div
              className="relative flex w-full flex-row items-center space-x-1.5"
              key={key}
            >
              <DisplayAvatar name={name} urlImg={image} size={38} />
              <div
                className={`flex w-9/12 flex-col ${
                  !isTreated ? "text-red-500" : ""
                }`}
              >
                <p className="truncate font-semibold">
                  {HighlightSearchW(
                    isTreatedByMe ? t("voip.me") : name ?? number,
                    search
                  )}
                </p>
                {!!name && (
                  <p
                    className={`font-medium leading-4 ${
                      !isTreated ? "text-red-500" : "text-slate-500"
                    }`}
                  >
                    {HighlightSearchW(number, search)}
                  </p>
                )}
              </div>
            </div>
          ),
      },
      {
        title: "Date",
        dataIndex: "humanDate",
        key: "humanDate",
        width: "10%",
        // fixed: "left",
        render: (humanDate, { key, isTreated, isMeTheSrc }) => (
          <CenterContents key={key} vertical={"center"}>
            <span className={!isTreated && !isMeTheSrc ? "text-red-500" : ""}>
              {humanDate}
            </span>
          </CenterContents>
        ),
      },
      {
        title: t("voip.waitingTime"),
        dataIndex: "waitingTime",
        key: "waitingTime",
        width: "7%",
        render: (waitingTime, { key, isTreated, isMeTheSrc }) => (
          <CenterContents key={key} vertical={"center"}>
            <span className={!isTreated && !isMeTheSrc ? "text-red-500" : ""}>
              {waitingTime}
            </span>
          </CenterContents>
        ),
        // fixed: "left",
      },
      {
        title: t("table.header.duration"),
        dataIndex: "callDuration",
        key: "callDuration",
        width: "10%",
        render: (_, { key, callDuration, audioRecording, disposition }) => (
          <CenterContents key={key} vertical={"center"}>
            {callDuration ? (
              <DurationColumnCallLog
                key={`${callDuration}-${audioRecording}`}
                audioRecording={audioRecording}
                // width={getColumnWidth(tableRef, 6)}
                duration={callDuration}
                t={t}
              />
            ) : disposition === "IN_PROGRESS" ? (
              <Tag
                icon={<SyncOutlined spin style={{ fontSize: 12 }} />}
                bordered={false}
                color="processing"
                style={{ paddingLeft: 3, paddingRight: 3, borderRadius: 10 }}
              >
                <span className=" text-xs font-semibold	">
                  {t("voip.callInProcess")}
                </span>
              </Tag>
            ) : null}
          </CenterContents>
        ),
      },
      {
        title: "Qualification",
        dataIndex: "tags",
        key: "tags",
        className: "py-0",
        // width: "18%",
        render: (
          tags,
          { _id, src, isTreatedByMe, isTreated, key, disposition }
        ) =>
          disposition !== "IN_PROGRESS" && (
            <TagColumn
              key={key}
              tags={tags}
              id={_id}
              info={{ name: src.name, number: src.number }}
              data={allTags}
              setDataSource={setDataSource}
              setOpenTask={setOpenTask}
              setIdCall={setIdCallForTask}
              setTaskId={setTaskId}
              canUserCreateOrUpdate={isTreatedByMe}
              access={access}
            />
          ),
      },
      {
        title: "Affectation",
        dataIndex: "affectation",
        key: "affectation",
        // width: "18%",
        render: (
          affectation,
          { _id, isTreatedByMe, isTreated, key, disposition }
        ) =>
          disposition !== "IN_PROGRESS" && (
            <AffectationColumn
              key={key}
              id={_id}
              affectation={affectation}
              setDataSource={setDataSource}
              t={t}
              access={access}
              user={user}
              canUserCreateOrUpdate={isTreatedByMe}
              setFamilyToAdd={setFamilyToAdd}
              setOpenDrawerCreate={setOpenDrawerCreate}
              setExternalSource={setExternalSource}
              handleDisplayElementInfo={handleDisplayElementInfo}
            />
          ),
      },
    ],
    [
      t,
      search,
      user,
      call,
      dispatch,
      handleAddItem,
      handleDisplayElementInfo,
      allTags,
      access,
      setDataSource,
      setOpenTask,
      setIdCallForTask,
      setTaskId,
      setFamilyToAdd,
      setOpenDrawerCreate,
      setExternalSource,
    ]
  );

  // Memoize the dataSource to prevent unnecessary re-renders
  const memoizedDataSource = useMemo(() => dataSource, [dataSource]);

  //
  const displayCardKpi = useMemo(() => {
    if (!callGroupsKpi?.length || !selectedGroup) return false;
    const parameterKey = "queue[]=";
    if (!selectedGroup.startsWith(parameterKey)) return false;
    const selectedQueue = selectedGroup.substring(parameterKey?.length);
    if (!selectedQueue) return false;
    return (
      callGroupsKpi?.find((queue) => queue?.queue_num === selectedQueue) ??
      false
    );
  }, [callGroupsKpi, selectedGroup]);
  //
  if (window.location.pathname !== "/telephony/groupsLog") return null;

  //

  return (
    <div className="relative w-full space-y-3 pt-0">
      <div className="flex w-full justify-between px-6">
        <div className="flex flex-row space-x-2">
          <Input
            style={{ width: 300 }}
            disabled={!dataSource?.length && !search?.length}
            allowClear
            placeholder={t("voip.group_queue_search")}
            value={displaySearch}
            onChange={handleSearch}
            prefix={<FiSearch className="h-4 w-4 text-slate-400" />}
            suffix={
              <Tooltip title={t("voip.search3chart")}>
                <InfoCircleTwoTone
                  style={{
                    fontSize: 14,
                    cursor: "help",
                  }}
                />
              </Tooltip>
            }
          />

          <div className="flex flex-row space-x-2">
            <FilterLogGroupsQueues
              setFilterState={setFilterCalls}
              filterState={filterCalls}
              t={t}
              disabled={!dataSource?.length && !filterCalls.length}
            />

            {filterCalls?.length ? (
              <Button type="link" onClick={() => setFilterCalls([])}>
                {t("tags.reset")}
              </Button>
            ) : (
              ""
            )}

            <Select
              disabled={
                !queuesAndGroups?.length ||
                (!selectedGroup && !dataSource.length)
              }
              style={{
                width: 300,
              }}
              // showSearch
              value={selectedGroup}
              placeholder={t("voip.selectGroupOrQueue")}
              optionFilterProp="children"
              onChange={setSelectedGroup}
              // filterOption={
              //   (input, option) => console.log({ option, input })
              //   // (option?.label ?? "")
              //   //   .toLowerCase()
              //   //   .includes(input.toLowerCase())
              // }
              options={queuesAndGroups}
            />
          </div>
        </div>
        <Space>
          <DatePicker.RangePicker
            disabled={!dataSource?.length && !dateRange[0] && !dateRange[1]}
            placement="bottomRight"
            presets={rangePresets(t)}
            onChange={(date, dateString) =>
              handleDateTimeRange(
                dateString,
                setDateRange,
                showTime !== null,
                user?.location
              )
            }
            format={formatDatePickerRange(showTime, user?.location)}
            showTime={showTime}
            renderExtraFooter={() => (
              <div className="ml-2">
                <span>{t("voip.displayTime")}: </span>
                <Switch
                  size="small"
                  // defaultChecked
                  onChange={handleSwitchChange}
                />
              </div>
            )}
            allowClear
            disabledDate={(current) =>
              disabledDate(disableDateToStart, current)
            }
            // disabledTime={disabledTime}
          />
          <ExportButton
            search={search}
            startDate={dateRange[0]}
            endDate={dateRange[1]}
            filter={filterCalls}
            // role={user.role}
            isGroupLog={true}
            disabled={!dataSource.length}
          />
        </Space>
      </div>
      {displayCardKpi && selectedGroup.includes("queue[]") && (
        <div className="flex justify-center">
          <div>
            <Divider plain style={{ margin: "0px 0px 4px" }}>
              {dateRange?.[0] && dateRange?.[1]
                ? `${t("voip.queues")} (${
                    selectedGroup.split("=")[1]
                  }): ${humanDate(dateRange[0], t, "table")} - ${humanDate(
                    dateRange[1],
                    t,
                    "table"
                  )}`
                : `${t("voip.queues")} (${selectedGroup.split("=")[1]}): ${t(
                    "voip.today"
                  )}`}
            </Divider>

            <Space wrap size={15}>
              <CardStat
                item={{
                  title: t("voip.answered_calls"),
                  value: displayCardKpi?.answered_calls,
                }}
              />
              <CardStat
                item={{
                  title: t("voip.no_answered_calls"),
                  value: displayCardKpi?.no_answered_calls,
                }}
              />
              <CardStat
                item={{
                  title: t("voip.missed_calls_not_returned_today"),
                  value: displayCardKpi?.no_answered_calls_not_returned,
                }}
              />
              <CardStat
                item={{
                  title: t("voip.moyen_ringing_calls"),
                  value:
                    displayCardKpi?.moyen_ringing_calls === "00:00:00"
                      ? "0"
                      : billSecToHumanReadable(
                          displayCardKpi?.moyen_ringing_calls
                        ),
                }}
              />
              <CardStat
                item={{
                  title: t("voip.moyen_treatement_calls"),
                  value:
                    displayCardKpi?.moyen_treatement_calls === "00:00:00"
                      ? "0"
                      : billSecToHumanReadable(
                          displayCardKpi?.moyen_treatement_calls
                        ),
                }}
              />
            </Space>
          </div>
        </div>
      )}
      <div className="table-view ">
        <Table
          ref={tableRef}
          columns={columns}
          dataSource={memoizedDataSource}
          loading={
            loadingTable || (!loadingTable && !dataSource.length && isDataExist)
          }
          rowClassName={(record) =>
            record.disposition === "IN_PROGRESS" ? "opacity-75" : ""
          }
          size="small"
          pagination={
            totalLogs <= 10
              ? false
              : {
                  showTotal: (total, range) =>
                    `${range[0]}-${
                      range[1] >= dataSource.length
                        ? range[1]
                        : dataSource.length
                    } of ${total}`,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  total: totalLogs,
                  pageSize: limit,
                  current: page,
                  onChange: (page) => setPage(page),
                  onShowSizeChange: (current, size) => setLimit(size),
                  pageSizeOptions:
                    totalLogs && handlePageSizeOptions(totalLogs),
                  size: "small",
                }
          }
          locale={{
            emptyText:
              loadingTable || isDataExist ? (
                <div className=" flex h-64 flex-row items-center justify-center space-x-2 text-xl">
                  {t("contacts.loadingDataTable")}
                  <span className="animate-bounce">...</span>
                </div>
              ) : (
                <Empty />
              ),
          }}
          scroll={{
            y: windowSize?.height - (displayCardKpi ? 340 : 270),
            x: 1600,
          }}
        />
      </div>

      <FormCreate
        open={openDrawerCreate}
        setOpen={setOpenDrawerCreate}
        familyId={familyToAdd}
        setCatchChange={setCatchChange}
        nbrPhoneFromVoip={nbrPhoneToAdd}
        externalSource={externalSource}
        setExternalSource={setExternalSource}
      />
      <DisplayElementInfo
        open={openDrawerInfo}
        setOpen={setOpenDrawerInfo}
        elementDetails={elementDetails}
      />
      <CreateTask
        open={openTask}
        setOpen={setOpenTask}
        mask={false}
        idCall={idCallForTask}
        source={"call"}
        taskId={taskId}
        setTaskId={setTaskId}
        setShouldFetchData={setShouldFetchData}
      />
    </div>
  );
};

export const CallerColumn = (props) => {
  //
  const {
    record: {
      key,
      src: { name, image, number, familyId },
      isTreated,
      isMeTheSrc,
      disposition,
    },
    search,
    t,
  } = props;
  //
  // console.log(name, { isTreated }, key);
  return (
    <div
      key={key}
      className="relative flex w-full flex-row items-center justify-between space-x-1"
    >
      <div className="relative flex w-10/12 flex-row items-center  space-x-1.5">
        <DisplayAvatar name={name} urlImg={image} size={38} />
        <div
          className={`flex w-9/12 flex-col ${
            !isTreated && !isMeTheSrc ? "text-red-500" : ""
          }`}
        >
          <p className="truncate font-semibold">
            <DisplayModuleIconAndText familyId={familyId} t={t} />{" "}
            {HighlightSearchW(name || "", search)}
          </p>
          <p
            className={`${name ? `font-medium` : `font-semibold`} leading-4 ${
              !isTreated && !isMeTheSrc
                ? "text-red-500"
                : name
                ? "text-slate-500"
                : ""
            }`}
          >
            {HighlightSearchW(number, search)}
          </p>
        </div>
      </div>
      {disposition !== "IN_PROGRESS" && <DropDownAction {...props} />}
    </div>
  );
};

export const DropDownAction = (props) => {
  //
  const {
    record: { key, src, dst, groupInfo, isTreated, returned },
    t,
    handleAddItem,
    call,
    user,
    dispatch,

    handleDisplayElementInfo,
    navigate,
  } = props;
  //
  const copyIcon = (text) => (
    <div className="pl-1">
      <Typography.Paragraph
        copyable={{
          text: text,
          icon: [
            <FiCopy
              style={{
                color: "rgb(22, 119, 255)",
                marginTop: "2px",
                fontSize: "15px",
              }}
            />,
          ],
        }}
      />
    </div>
  );
  //
  const splitName = (name) => {
    if (!name) return "";
    if (name.includes(" ")) return name?.split(" ")?.[0];
    else return name.length > 10 ? `${name.slice(0, 10)}...` : name;
  };
  //
  const menuDropdown = () => {
    const items = [];
    //
    const pushItem = (key, icon, label, onClick, disabled, children) => {
      items.push({
        key,
        icon,
        label,
        onClick,
        disabled,
        children,
      });
    };
    //
    const srcValid = src.id && src.familyId && src.id !== user.id;
    const dstValid = dst.id && dst.familyId && dst.id !== user.id;

    //
    // First, collect all valid call targets
    const callTargets = [
      {
        entity: src,
        condition: src.id !== user.id,
        label: src?.name
          ? `${splitName(src?.name)} (${src?.number})`
          : src?.number,
        number: src?.number,
        id: src?.id,
        familyId: src?.familyId,
      },
      {
        entity: groupInfo,
        condition: true, // Always show group info
        label: `${splitName(groupInfo?.name)} (${groupInfo?.number})`,
        number: groupInfo.number,
      },
      {
        entity: dst,
        condition: dst?.id && dst.id !== user.id,
        label: `${splitName(dst?.name)} (${dst?.number})`,
        number: dst?.number,
        id: dst?.id,
        familyId: dst?.familyId,
      },
      {
        entity: returned,
        condition: returned?.id && returned.id !== user.id,
        label: `${splitName(returned?.name)} (${returned?.number})`,
        number: returned?.number,
        id: returned?.id,
        familyId: returned?.familyId,
      },
    ].filter((target) => target.condition);

    // If multiple valid call targets, group them under "Call" parent
    if (callTargets.length > 1) {
      pushItem(
        "call-group",
        <PhoneOutlined rotate={100} style={{ fontSize: 15 }} />,
        t("voip.call"), // "Call" translation key
        null, // No onClick for parent
        false,
        callTargets.map((target) => ({
          key: `${key}-${target.number}`,
          label: (
            <div className="flex flex-row justify-between space-x-1">
              {target.label}
              {copyIcon(target.number)}
            </div>
          ),
          onClick: () => call(target.number, target.id, target.familyId),
        }))
      );
    }
    // Single call target case
    else if (callTargets.length === 1) {
      const target = callTargets[0];
      pushItem(
        `${key}-${target.number}`,
        <PhoneOutlined rotate={100} style={{ fontSize: 15 }} />,
        <div className="flex flex-row justify-between space-x-1">
          {target.label}
          {copyIcon(target.number)}
        </div>,
        () => call(target.number, target.id, target.familyId)
      );
    }
    if (src?.uuid || returned?.uuid || dst?.uuid) {
      items.push({ type: "divider" });

      // Filter valid chat targets
      const chatTargets = [
        { entity: dst, condition: dst?.uuid && dst.id !== user.id },
        { entity: src, condition: src?.uuid && src.id !== user.id },
        { entity: returned, condition: returned?.uuid && src.id !== user.id },
      ].filter((item) => item.condition);

      // If multiple valid chat targets, group them
      if (chatTargets.length > 1) {
        pushItem(
          "chat-group",
          <MessageOutlined style={{ fontSize: 14 }} />,
          t("voip.chatWith"),
          null, // No onClick for parent
          false,
          chatTargets.map((target) => ({
            key: `chat-${key}-${target.entity.uuid}`,
            label: splitName(target.entity.name),
            onClick: () => dispatch(openDrawerChat(target.entity.uuid)),
            disabled: !target.entity.uuid,
          }))
        );
      }
      // Single chat target case
      else if (chatTargets.length === 1) {
        const target = chatTargets[0].entity;
        pushItem(
          `chat-${key}-${target.uuid}`,
          <MessageOutlined style={{ fontSize: 14 }} />,
          `${t("voip.chatWith")} ${splitName(target.name)}`,
          () => dispatch(openDrawerChat(target.uuid)),
          !target.uuid
        );
      }
    }
    if (
      (src.id && src.familyId && src.id !== user.id) ||
      (dst.id && dst.familyId && dst.id !== user.id) ||
      (returned?.id && returned?.familyId && returned?.id !== user.id)
    ) {
      items.push({ type: "divider" });
      if (srcValid || dstValid) {
        // If both are valid, create a grouped menu
        if (srcValid && dstValid) {
          pushItem(
            "more-info-group",
            <InfoCircleOutlined style={{ fontSize: 14 }} />,
            t("voip.moreInfoWith"),
            null, // No onClick for parent item
            false,
            [
              {
                key: `more-info-src-${src.id}`,
                label: `${splitName(src.name)}`,
                onClick: () =>
                  handleDisplayElementInfo(src.name, {
                    id: src.id,
                    familyId: src.familyId,
                  }),
              },
              {
                key: `more-info-dst-${dst.id}`,
                label: `${splitName(dst.name)}`,
                onClick: () =>
                  handleDisplayElementInfo(dst.name, {
                    id: dst.id,
                    familyId: dst.familyId,
                  }),
              },
            ]
          );

          pushItem(
            "view-360-group",
            <Tb360View style={{ fontSize: 17 }} />,
            t("voip.view360"),
            null, // No onClick for parent item
            false,
            [
              {
                key: `view-360-src-${src.id}`,
                label: splitName(src.name),
                onClick: () => {
                  dispatch({
                    type: "SET_CONTACT_HEADER_INFO",
                    payload: { name: src.name, id: src.id },
                  });
                  navigate(generateUrlToView360(src.familyId, src.id, "v2"));
                },
              },
              {
                key: `view-360-dst-${dst.id}`,
                label: splitName(dst.name),
                onClick: () => {
                  dispatch({
                    type: "SET_CONTACT_HEADER_INFO",
                    payload: { name: dst.name, id: dst.id },
                  });
                  navigate(generateUrlToView360(dst.familyId, dst.id, "v2"));
                },
              },
            ]
          );
        } else {
          // Handle individual cases when only one is valid
          const target = srcValid ? src : dst;

          if (srcValid) {
            pushItem(
              `more-info-${src.id}`,
              <InfoCircleOutlined style={{ fontSize: 14 }} />,
              `${t("voip.moreInfoWith")} ${splitName(src.name)}`,
              () =>
                handleDisplayElementInfo(src.name, {
                  id: src.id,
                  familyId: src.familyId,
                })
            );

            pushItem(
              `view-360-${src.id}`,
              <Tb360View style={{ fontSize: 17 }} />,
              `${t("voip.view360")} ${splitName(src.name)}`,
              () => {
                dispatch({
                  type: "SET_CONTACT_HEADER_INFO",
                  payload: { name: src.name, id: src.id },
                });
                navigate(generateUrlToView360(src.familyId, src.id, "v2"));
              }
            );
          }

          if (dstValid) {
            pushItem(
              `more-info-${dst.id}`,
              <InfoCircleOutlined style={{ fontSize: 14 }} />,
              `${t("voip.moreInfoWith")} ${splitName(dst.name)}`,
              () =>
                handleDisplayElementInfo(dst.name, {
                  id: dst.id,
                  familyId: dst.familyId,
                })
            );

            pushItem(
              `view-360-${dst.id}`,
              <Tb360View style={{ fontSize: 17 }} />,
              `${t("voip.view360")} ${splitName(dst.name)}`,
              () => {
                dispatch({
                  type: "SET_CONTACT_HEADER_INFO",
                  payload: { name: dst.name, id: dst.id },
                });
                navigate(generateUrlToView360(dst.familyId, dst.id, "v2"));
              }
            );
          }
        }
      }
    }

    if (!src.name && !src.id) {
      items.push({ type: "divider" });
      pushItem(
        "add",
        <MdOutlinePersonAddAlt style={{ fontSize: 15 }} />,
        t("voip.addToModule"),
        null,
        null,
        [
          {
            label: t("contacts.leads"),
            key: "add-leads",
            icon: <CgUserlane style={{ fontSize: 15 }} />,
            onClick: () => handleAddItem(9, src?.number),
          },
          {
            label: t("contacts.contact"),
            key: "add-contact",
            icon: <HiOutlineUserGroup style={{ fontSize: 15 }} />,
            onClick: () => handleAddItem(2, src?.number),
          },
          {
            label: t("contacts.company"),
            key: "add-company",
            icon: <HiOutlineBuildingOffice style={{ fontSize: 15 }} />,
            onClick: () => handleAddItem(1, src?.number),
          },
        ]
      );
    }

    return { items };
  };

  //
  return (
    <Dropdown trigger={["click"]} placement="bottomRight" menu={menuDropdown()}>
      <FiMoreVertical className="h-[18px] w-[18px] cursor-pointer text-gray-400 hover:text-gray-700" />
    </Dropdown>
  );
};

export const FilterLogGroupsQueues = ({
  t,
  setFilterState,
  disabled,
  filterState,
}) => {
  //
  const [openPopover, setOpenPopover] = useState(false);
  //
  const checkBoxStyle = {
    display: "flex",
    flexDirection: "column",
    gap: ".5rem",
    // marginLeft: "5px",
  };
  //
  return (
    <Popover
      trigger={["click"]}
      placement="bottomRight"
      // title={t("voip.filterCallByType")}
      content={
        <div className="flex flex-col space-y-3">
          <p className="flex font-semibold">{t("tags.filter")}</p>
          <Checkbox.Group
            style={checkBoxStyle}
            value={filterState}
            onChange={(check) => setFilterState(check)}
          >
            <Checkbox key={"treated"} value="treated">
              <Typography.Text>{t("voip.treated")}</Typography.Text>
            </Checkbox>
            <Checkbox key={"missed"} value="missed">
              <Typography.Text>{t("voip.missed")}</Typography.Text>
            </Checkbox>
            <Checkbox key={"failed"} value="failed">
              <Typography.Text>{t("voip.failed")}</Typography.Text>
            </Checkbox>
          </Checkbox.Group>
        </div>
      }
      open={openPopover}
      onOpenChange={(newOpen) => setOpenPopover(newOpen)}
    >
      <Button
        disabled={disabled}
        type="text"
        shape="circle"
        icon={
          <Badge dot={filterState?.length}>
            <FilterTwoTone />
          </Badge>
        }
        //   onClick={() => setOpenPopover(true)}
      />
    </Popover>
  );
};

export default GroupsAndQueues;
