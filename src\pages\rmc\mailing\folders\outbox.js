import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Badge } from "antd";
import { useCallback, useEffect, useMemo, useState } from "react";
import { DeleteOutlined } from "@ant-design/icons";

import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import MarkDown from "../Markdown";
import MainService from "../../../../services/main.service";
import FormCreate from "../../../clients&users/components/FormCreate";
import CreateTask from "../../../voip/components/CreateTask";
import useDebounce from "../../../components/UseDebounce/UseDebounce";
import {
  ReadUnreadMessages,
  SeenMessage,
  TrashListMessages,
} from "../services/ActionsApi";

import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import {
  setNumberEmailThread,
  setPage,
  setPageSize,
} from "new-redux/actions/mail.actions";
import ActionsStarredImportant from "../actionsStarredImportant";
import DropdownActionsTable from "../dropdownActionsTable";
import Log from "../components/Log";
import Affectation from "../Affectation";
import Qualification from "../Qualification";
import { formatDateForDisplay } from "pages/tasks/helpers/formatDateToDisplay";
import ModalDeleteEmail from "../components/ModalDeleteEmail";
import "../mailing.css";
import { renderHighlight } from "pages/global-search/components/render-search-items";
import { checkAccessRoleAccount } from "../mailing";

const Outbox = ({ setDetailsMail, dataAccounts, refresh }) => {
  // const [clickStar, setClickStar] = useState(false);
  // const [page, setPage] = useState(1);
  const [dataMailOutbox, setDataMailOutbox] = useState([]);
  const [metaMailOutbox, setMetaMailOutbox] = useState({});

  const [error, setError] = useState(false);
  const [openForm, setOpenForm] = useState(false);
  const [familyId, setFamilyId] = useState(null);
  const [openTask, setOpenTask] = useState(false);
  const [titleTask, setTiltleTask] = useState("");
  const [openModal, setOpenModal] = useState(false);
  const [EmailId, setEmailId] = useState("");
  const [typeDelete, setTypeDelete] = useState("");
  const [thirdid, setThirdId] = useState("");
  const [openLogDrawer, setOpenLogDrawer] = useState(null);
  const [dataTags, setDataTags] = useState([]);
  const [loading, setLoading] = useState({ state: false, type: null });
  const [clickArchive, setClickArchive] = useState(false);
  const [expandedRows, setExpandedRows] = useState([]);
  const [t] = useTranslation("common");
  const navigate = useNavigate();
  const { page, pageSize, searchEmail } = useSelector(
    (state) => state.mailReducer
  );
  const { user } = useSelector(({ user }) => user);
  const access = user.access || {};
  const debouncedSearchValue = useDebounce(searchEmail, 500);
  const usedAccount = useMemo(
    () => dataAccounts?.find((item) => item?.selected),
    [dataAccounts]
  );
  const dispatch = useDispatch();

  const getMailsOutbox = useCallback(async () => {
    if (
      dataAccounts?.length === 0
      // ||(!refreshMail && Object.values(metaMailOutbox).length > 0 && !refresh)
    )
      return;

    setLoading({ state: true, type: "mails" });
    let response;
    try {
      if (debouncedSearchValue.length > 0) {
        response = await MainService.searchMailsOutbox(
          usedAccount?.value,
          debouncedSearchValue,
          page,
          pageSize
        );
      } else {
        response = await MainService.getOutboxEmails(
          usedAccount?.value,
          page,
          pageSize
        );
      }
      if (response?.status === 200) {
        //  dispatch(setRefreshMail(false));
        checkAccessRoleAccount(response, navigate, t);

        setDataMailOutbox(response.data.data);
        setMetaMailOutbox(response.data.meta);
        setError(false);
      }
    } catch (err) {
      setError(true);
    } finally {
      setLoading({ state: false, type: "mails" });
      //     setRefresh(false);
    }
  }, [
    dataAccounts?.length,
    usedAccount?.value,
    debouncedSearchValue,
    page,
    refresh,
    pageSize,
  ]);

  const dataSourceInbox = useMemo(() => {
    const result = [];
    const highlightedRows = [];
    dataMailOutbox.forEach((item) => {
      if (!!item.highlight) {
        highlightedRows.push(item.id);
      }
      result.push({
        key: item.id,
        seen: item.seen,
        nbr: item.nbr,
        toEmail: item.to,
        subject: item.subject,
        body: item.body,
        date: item.date,
        starred: item.starred,
        important: item.important,
        third_id: item.third_id,
        tags: item.tags,
        affectation: item.affectation,
        highlight: item.highlight,
      });
    });
    if (!!debouncedSearchValue) setExpandedRows(highlightedRows);
    else setExpandedRows([]);
    return result;
  }, [dataMailOutbox]);

  const getTags = useCallback(async () => {
    try {
      const response = await MainService.getTags();
      if (response?.status === 200) {
        const data =
          response?.data?.map((tag) => ({
            id: tag?.typetask
              ? `${tag?.id}-${tag?.typetask?.id}`
              : `${tag?.id}`,
            label: tag?.label,
            color: tag?.color,
            icon: tag?.icon,
            taskType: tag?.typetask ? true : false,
          })) || [];
        setDataTags(data);
      }
    } catch (error) {
      console.log(error);
    }
  }, []);

  // console.log({ dataMailOutbox });
  const columns = [
    {
      dataIndex: "starred",
      width: "90px",
      fixed: "left",

      render: (_, record) => (
        <>
          <ActionsStarredImportant
            record={record}
            getMails={getMailsOutbox}
            usedAccount={usedAccount}
          />
        </>
      ),
    },
    {
      title: t("mailing.Inbox.To"),
      dataIndex: "toEmail",
      // width: "140px",
      fixed: "left",
      render: (text, record) => {
        return (
          <div className={`flex cursor-pointer items-center `}>
            <Tooltip
              placement="topLeft"
              title={text
                .map((item) =>
                  item?.name?.length > 0 ? item.name : item.address
                )
                .join(", ")}
            >
              <p
                className=" max-w-sm truncate"
                style={{ width: "80%", cursor: "pointer", marginLeft: "3px" }}
              >
                {text
                  .map((item) =>
                    item?.name?.length > 0 ? item.name : item.address
                  )
                  .join(", ")}
              </p>
            </Tooltip>

            <div className="action-mail ">
              {" "}
              <Badge
                style={{
                  backgroundColor: "transparent",
                  border: "none",
                  outline: "none",
                  margin: 0,
                  color: "gray",
                  fontSize: 12,
                  fontWeight: record?.seen === 0 ? "bold" : "normal",
                }}
                count={record?.nbr}
              ></Badge>
              <div className="items-center gap-x-1  ">
                <DropdownActionsTable
                  record={record}
                  setThirdId={setThirdId}
                  setOpenLogDrawer={setOpenLogDrawer}
                  t={t}
                  conditionActions={false}
                  usedAccount={usedAccount}
                  dataMailOutbox={dataMailOutbox}
                  setDataMailOutbox={setDataMailOutbox}
                  user={user}
                  access={access}
                  dataTags={dataTags}
                  setOpenTask={setOpenTask}
                  setEmailId={setEmailId}
                  setOpenModal={setOpenModal}
                  setTypeDelete={setTypeDelete}
                  getMailsInbox={getMailsOutbox}
                  clickArchive={clickArchive}
                  setClickArchive={setClickArchive}
                  type="outbox"
                />
              </div>
            </div>
          </div>
        );
      },
    },
    {
      title: t("mailing.Inbox.subject"),
      dataIndex: "subject",
      // width: "150px",
      ellipsis: true,
      render: (text, record) => {
        return (
          <div
            style={{
              cursor: "pointer",
            }}
          >
            <span
              style={{
                cursor: "pointer",
              }}
            >
              {text?.length > 30 && usedAccount?.shared != 1 ? (
                // <MarkDown>{text.toString()?.substring(0, 30)}...</MarkDown>
                <span
                  dangerouslySetInnerHTML={{
                    __html: text.toString()?.substring(0, 30) + "...",
                  }}
                />
              ) : (
                // <MarkDown>{text}</MarkDown>
                <span dangerouslySetInnerHTML={{ __html: text }} />
              )}
            </span>
            {usedAccount?.shared == 1 && (
              <div
                className=" flex items-center space-x-3"
                onClick={(e) => e.stopPropagation()}
              >
                {record.affectation?.affect_label ? (
                  <Affectation
                    type="inbox"
                    id={record.key}
                    owner={record?.owner}
                    fromName={record?.from?.name}
                    fromEmail={record?.from?.address}
                    idEmail={record?.key}
                    transfert={record?.transfert}
                    affectation={record.affectation}
                    setDataSource={setDataMailOutbox}
                    usedAccount={usedAccount}
                    t={t}
                    user={user}
                    access={access}
                  />
                ) : null}

                {record.tags?.tags.length > 0 ? (
                  <Qualification
                    tags={record.tags}
                    id={record.key}
                    info={{
                      name: record?.qualification?.label,
                      number: record?.qualification?.note,
                    }}
                    owner={record?.owner}
                    transfert={record?.transfert}
                    data={dataTags}
                    setDataSource={setDataMailOutbox}
                    setOpenTask={setOpenTask}
                    usedAccount={usedAccount}
                    type="inbox"

                    // setIdCall={setIdCallForTask}
                  />
                ) : null}
              </div>
            )}
          </div>
        );
      },
    },
    ...(usedAccount?.shared == 0
      ? [
          {
            title: t("mailing.Inbox.message"),
            dataIndex: "body",
            // width: "300px",
            ellipsis: true,
            render: (text, record) => {
              return (
                // <div style={{ cursor: "pointer" }}>
                //   <MarkDown>{text}</MarkDown>
                // </div>

                <div className="flex cursor-pointer items-center justify-between ">
                  <div className="space-y-1.5">
                    <div
                      className={`flex max-h-[80px] max-w-[340px] items-center space-x-3 overflow-hidden truncate text-ellipsis whitespace-nowrap   lg:max-w-[350px] `}
                    >
                      <span
                        style={{
                          fontWeight: record.seen === 0 ? "bold" : "",
                          cursor: "pointer",
                        }}
                      >
                        {/* <MarkDown>{text}</MarkDown> */}
                        <span dangerouslySetInnerHTML={{ __html: text }} />
                      </span>
                    </div>
                    <div
                      className=" flex items-center space-x-3"
                      onClick={(e) => e.stopPropagation()}
                    >
                      {record.affectation?.affect_label ? (
                        <Affectation
                          type="inbox"
                          id={record.key}
                          owner={record?.owner}
                          fromName={record?.from?.name}
                          fromEmail={record?.from?.address}
                          idEmail={record?.key}
                          transfert={record?.transfert}
                          affectation={record.affectation}
                          setDataSource={setDataMailOutbox}
                          usedAccount={usedAccount}
                          t={t}
                          user={user}
                          access={access}
                        />
                      ) : null}

                      {record.tags?.tags.length > 0 ? (
                        <Qualification
                          tags={record.tags}
                          id={record.key}
                          info={{
                            name: record?.qualification?.label,
                            number: record?.qualification?.note,
                          }}
                          owner={record?.owner}
                          transfert={record?.transfert}
                          data={dataTags}
                          setDataSource={setDataMailOutbox}
                          setOpenTask={setOpenTask}
                          usedAccount={usedAccount}
                          type="inbox"

                          // setIdCall={setIdCallForTask}
                        />
                      ) : null}
                    </div>
                  </div>

                  {/* <div
              className="ml-1 hidden items-center gap-x-1 group-hover:flex"
              onClick={(e) => e.stopPropagation()}
            >
              {!record.affectation?.affect_label ? (
                <Affectation
                  type="inbox"
                  id={record.key}
                  owner={record?.owner}
                  transfert={record?.transfert}
                  affectation={record.affectation}
                  setDataSource={setDataMailOutbox}
                  usedAccount={usedAccount}
                  t={t}
                  user={user}
                  access={access}
                />
              ) : null}

              {!record.tags?.tags.length > 0 ? (
                <div
                // onClick={() => {
                //   setMailingProps({
                //     label:
                //       record?.subject?.length > 0
                //         ? record?.subject
                //         : record?.address,
                //     idEmail: record?.key,
                //   });
                // }}
                >
                  <Qualification
                    tags={record.tags}
                    id={record.key}
                    info={{
                      name: record?.qualification?.label,
                      number: record?.qualification?.note,
                    }}
                    owner={record?.owner}
                    transfert={record?.transfert}
                    data={dataTags}
                    setDataSource={setDataMailOutbox}
                    setOpenTask={setOpenTask}
                    usedAccount={usedAccount}
                    type="inbox"
                    // setIdCall={setIdCallForTask}
                  />
                </div>
              ) : null}
            </div> */}
                </div>
              );
            },
          },
        ]
      : []),

    {
      dataIndex: "Date",
      title: "Date",
      // width: "100px",
      render: (_, record) => (
        <div
          className=""
          style={{
            fontWeight: record.seen === 0 ? "bold" : "",
            cursor: "pointer",
          }}
        >
          {formatDateForDisplay(
            record.date,
            `${user?.location?.date_format} ${user?.location?.time_format}`,
            user,
            t
          )}
        </div>
      ),
    },

    // {
    //   title: t("mailing.Inbox.date"),
    //   dataIndex: "date",
    //   width: "120px",
    //   render: (dateTime, record) => (
    //     <span
    //       style={{
    //         cursor: "pointer",
    //       }}
    //     >
    //       {dayjs_timezone(record.date).isToday()
    //         ? moment(record.date).format("LT")
    //         : moment(record.date).format("llll")}
    //     </span>
    //   ),
    // },
  ];

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  const toggleSelection = (record) => {
    const key = record.key;
    const newSelectedRowKeys = selectedRowKeys.includes(key)
      ? selectedRowKeys.filter((k) => k !== key)
      : [...selectedRowKeys, key];
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const handleMarkAsReadUnread = async (status, selectedRowKeys) => {
    const response = await ReadUnreadMessages({
      usedAccount,
      status,
      selectedRowKeys,
      setSelectedRowKeys,
      t,
      dispatch,
    });

    if (response && debouncedSearchValue?.length === 0) getMailsOutbox();
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
    renderCell: (checked, record, index, originNode) => (
      <div className="relative">
        <Button
          style={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
          }}
          size="large"
          type="text"
          shape="circle"
          onClick={(e) => {
            e.stopPropagation();
            toggleSelection(record);
          }}
        >
          {originNode}
        </Button>
      </div>
    ),
  };
  const hasSelected = selectedRowKeys.length > 0;

  const DeleteMail = async () => {
    setLoading({ state: true, type: "delete" });
    const response = await TrashListMessages({
      usedAccount,
      id: EmailId,
      setSelectedRowKeys,
      selectedRowKeys,
      setOpenModal,
      folder: "sent",
      typeDelete,
      setLoading,
      t,
    });
    if (response) getMailsOutbox();
  };

  useEffect(() => {
    getMailsOutbox(1);
  }, [getMailsOutbox]);

  useEffect(() => {
    getTags();
  }, []);

  return (
    <>
      {hasSelected ? (
        <div className="mb-[8px] ml-[20px] flex items-center space-x-3">
          <Button
            type="primary"
            danger
            icon={<DeleteOutlined />}
            onClick={() => {
              setOpenModal(true);
              setTypeDelete("multiple");
            }}
          >
            {t("mailing.DeleteButton")} ({selectedRowKeys.length}{" "}
            {selectedRowKeys.length > 1 ? "emails" : "email"})
          </Button>
          {usedAccount?.shared == 0 ? (
            <>
              <Button
                type="default"
                onClick={() => handleMarkAsReadUnread(1, selectedRowKeys)}
              >
                {t("mailing.markRead")} ({selectedRowKeys.length})
              </Button>

              <Button
                type="default"
                onClick={() => handleMarkAsReadUnread(0, selectedRowKeys)}
              >
                {t("mailing.markUnread")} ({selectedRowKeys.length})
              </Button>
            </>
          ) : null}
        </div>
      ) : null}

      {openLogDrawer !== null ? (
        <Log
          openLogDrawer={openLogDrawer}
          setOpenLogDrawer={setOpenLogDrawer}
          setLoading={setLoading}
          thirdid={thirdid}
        />
      ) : null}

      <FormCreate open={openForm} setOpen={setOpenForm} familyId={familyId} />

      <CreateTask
        open={openTask}
        setOpen={setOpenTask}
        mask={false}
        source="mailing"
        object={titleTask}
      />

      <Table
        className="mailing-custom-row"
        loading={loading.state && loading.type === "mails"}
        columns={columns}
        dataSource={dataSourceInbox}
        showSizeChanger={false}
        pagination={{
          current: page,
          pageSize: pageSize,
          pageSizeOptions: ["10", "20", "30"],
          total: metaMailOutbox.total === 0 ? 1 : metaMailOutbox.total,
          showSizeChanger: true,
          onChange: (page, pageSize) => {
            dispatch(setPage(page));
            dispatch(setPageSize(pageSize));
          },
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total}`,
        }}
        rowSelection={rowSelection}
        scroll={{ y: "calc(100vh - 250px)" }}
        onRow={(record) => {
          return {
            onClick: () => {
              setDetailsMail([]);
              dispatch(setNumberEmailThread(record.nbr));
              navigate(`/mailing/${usedAccount?.value}/sent/${record.key}`);
            },
          };
        }}
        locale={{
          emptyText: (
            <p className={error ? "mt-4 text-red-600" : "mt-4 text-[#898282]"}>
              {error
                ? t("toasts.errorFetchApi")
                : loading.state && loading.type === "mails"
                ? "Loading ..."
                : t("mailing.noData")}
            </p>
          ),
        }}
        rowClassName={(_, index) => `${"clickable-row"}  group`}
        expandable={
          !!debouncedSearchValue
            ? {
                expandedRowKeys: expandedRows,
                expandedRowRender: (record) =>
                  !!debouncedSearchValue ? (
                    <p className="m-0 ml-[10%]">
                      {renderHighlight(record.highlight, t, "email")}
                    </p>
                  ) : null,
                rowExpandable: (record) => !!record.highlight,
                showExpandColumn: false,
              }
            : {}
        }
        size="small"
      />

      <ModalDeleteEmail
        t={t}
        openModal={openModal}
        setOpenModal={setOpenModal}
        loading={loading}
        DeleteMail={DeleteMail}
        typeDelete={typeDelete}
        selectedRowKeysLength={selectedRowKeys.length}
      />
    </>
  );
};

export default Outbox;
