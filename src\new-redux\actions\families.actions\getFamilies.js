import {
  GET_FAMILIES_ERROR,
  GET_FAMILIES_SUCCESS,
  GET_SELECTED_FAMILY,
} from "../../constants";
import MainService from "../../../services/main.service";
import { store } from "new-redux/store";

export const getFamilies = () => async (dispatch) => {
  const { user } = await store.getState().user;

  try {
    if (user?.id) {
      const modifiedAccess = { ...user?.access };
      Object.keys(modifiedAccess).forEach((key) => {
        if (key === "colleague") {
          modifiedAccess["user"] = modifiedAccess[key];
          delete modifiedAccess[key];
        }
        if (key === "companies") {
          modifiedAccess["organisation"] = modifiedAccess[key];
          delete modifiedAccess[key];
        } else if (key.endsWith("s")) {
          const newKey = key.slice(0, -1); // Supprimer le dernier caractère
          modifiedAccess[newKey] = modifiedAccess[key];
          delete modifiedAccess[key];
        }
      });
      const response = await MainService.getFamilies();

      dispatch({
        type: GET_FAMILIES_SUCCESS,
        payload: response.data.data
          ?.map((el) => ({
            ...el,
            label: el.label.replace(/s$/, ""),
          }))
          ?.filter(
            (family) => modifiedAccess[family.label?.toLowerCase()] === "1"
          ),
      });
    }
  } catch (error) {
    dispatch({
      type: GET_FAMILIES_ERROR,
      payload: error,
    });
  }
};

export const setSelectedFamily = (payload) => (dispatch) => {
  dispatch({
    type: GET_SELECTED_FAMILY,
    payload: payload,
  });
};
