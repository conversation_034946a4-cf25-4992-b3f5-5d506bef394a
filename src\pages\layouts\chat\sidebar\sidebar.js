import { <PERSON><PERSON>, Divider, notification, Spin } from "antd";
import GroupsSideBarChat from "./list";
import { InputSearchChat } from "./search/InputSearchChat";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import {
  resetStateOtherUser,
  setChatSelectedConversation,
  setLoadingSideBar,
  setOpenDrawer,
  setSearchChatSidebar,
  setSearchList,
  setSidebarDrawer,
} from "new-redux/actions/chat.actions";
import { useEffect, useRef } from "react";
import {
  getArchivedConversations,
  getArchivedConversationsIds,
} from "new-redux/services/chat.services";
import { ArrowLeftOutlined, ContainerOutlined } from "@ant-design/icons";
import { setSearchMessageTerm } from "new-redux/actions/chat.actions/Input";
import useKeyDown from "custom-hooks/useKeyDown";
import { setOpenModalEmail } from "new-redux/actions/mail.actions";
import { LoadingSideBarStatus } from "new-redux/reducers/chatReducer";
import { Refs_IDs } from "components/tour/tourConfig";

function ChatSidebar({ source }) {
  const [t] = useTranslation("common");
  const dispatch = useDispatch();
  const {
    sidebarDrawer,
    archivedListIds,
    count_archived_msg,
    searchChatSideBar,
    loadingSideBar,
  } = useSelector((state) => state.chat);
  const selectedConversation = useSelector(
    (state) => state.ChatRealTime.selectedConversation
  );
  const inputRef = useRef(null);

  useEffect(() => {
    notification.destroy("toast-new-message");
    dispatch(setSidebarDrawer("chat"));
    dispatch(getArchivedConversationsIds());

    dispatch(setSearchChatSidebar(""));
    dispatch(setOpenModalEmail(false));
  }, [dispatch]);

  useEffect(() => {
    if (archivedListIds.length === 0) {
      dispatch(setSidebarDrawer("chat"));
    }
  }, [dispatch, archivedListIds]);

  useKeyDown(75, true, "keydown", () => {
    inputRef.current.focusInput();
  });
  useKeyDown(71, true, "keydown", () => {
    if (selectedConversation?.id) {
      dispatch(setOpenDrawer({ type: "search" }));
      dispatch(setSearchList({ list: [], page: 1 }));
      dispatch(
        setSearchMessageTerm({
          value: "",
          forced: false,
          focus: Math.floor(Math.random() * 10000 + 1),
        })
      );
    }
  });

  return (
    <>
      {/* <div className="flex items-center justify-between px-4">
        <SortAddBar source={source} />
      </div> */}
      <div className="mt-4  px-4" ref={Refs_IDs.searchMsgsChat}>
        <InputSearchChat ref={inputRef} />
      </div>

      <div className="my-2 flex  items-center justify-between px-4">
        {searchChatSideBar === "" && archivedListIds.length > 0 && (
          <Button
            type="link"
            className="pl-0 text-slate-700"
            icon={
              sidebarDrawer === "archive" ? (
                <ArrowLeftOutlined />
              ) : sidebarDrawer === "chat" && archivedListIds.length > 0 ? (
                <ContainerOutlined />
              ) : null
            }
            onClick={() => {
              // archive
              dispatch(
                resetStateOtherUser({
                  forced: false,
                  keepDrawerOpened: false,
                  item: null,
                })
              );

              if (sidebarDrawer === "chat" && archivedListIds.length > 0) {
                dispatch(setSidebarDrawer("archive"));
                dispatch(getArchivedConversations());
              }

              // chat
              else if (sidebarDrawer === "archive") {
                dispatch(setSidebarDrawer("chat"));
                dispatch(setLoadingSideBar(LoadingSideBarStatus.idle));

                dispatch(
                  setChatSelectedConversation({
                    selectedConverstation: null,
                  })
                );
              }
            }}
          >
            {sidebarDrawer === "chat" && archivedListIds.length > 0
              ? t("chat.archiveList") +
                (count_archived_msg > 0 ? "(" + count_archived_msg + ")" : "")
              : sidebarDrawer === "archive"
              ? t("chat.back")
              : ""}
          </Button>
        )}

        {loadingSideBar === LoadingSideBarStatus.soft && (
          <div className=" my-1 flex  flex-1 items-center  space-x-1.5  text-xs text-gray-400">
            <Spin size="small" />
            <span>{t("chat.loading")}...</span>
          </div>
        )}
      </div>
      <Divider
        className={
          searchChatSideBar || archivedListIds.length > 0 ? "my-2" : "my-5"
        }
      />

      <GroupsSideBarChat source={source} />
    </>
  );
}
export default ChatSidebar;
