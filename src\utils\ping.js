function request_image(url) {
  return new Promise(function (resolve, reject) {
    try {
      let img = new Image();
      img.onload = function () {
        resolve(img);
      };
      img.onerror = function () {
        reject(url);
      };
      img.src =
        url +
        "?random-no-cache=" +
        Math.floor((1 + Math.random()) * 0x10000).toString(16);
    } catch (e) {
      reject(e);
    }
  });
}
export function pingFunction(url, multiplier) {
  return new Promise(function (resolve, reject) {
    let time;
    let start = new Date().getTime();
    let response = function () {
      let delta = new Date().getTime() - start;
      delta *= multiplier || 1;
      resolve(delta);
    };
    request_image(url).then(response).catch(response);

    // Set a timeout for max-pings, 5s.
    time = setTimeout(function () {
      reject(Error("Timeout"));
      clearTimeout(time);
    }, 15000);
  });
}
