import { Button } from "antd";
import {
  Check,
  ChevronDown,
  Heading1,
  Heading2,
  Heading3,
  TextQuote,
  ListOrdered,
  Code,
  Pilcrow,
  ListTodo,
} from "lucide-react";

function safeToggleNode(editor, type, fallbackType) {
  console.log("safeToggleNode", editor, type, fallbackType);
  const isActive = editor.isActive(type);
  if (isActive) {
    return editor.chain().focus().setNode(fallbackType).run();
  } else {
    return editor.chain().focus().setNode(type).run();
  }
}

export const NodeSelector = ({ editor, isOpen, setIsOpen }) => {
  const items = [
    {
      name: "Paragraph",
      icon: Pilcrow,
      command: () =>
        // editor.chain().focus().toggleNode("paragraph", "paragraph").run(),
        safeToggleNode(editor, "paragraph", "paragraph"),
      // I feel like there has to be a more efficient way to do this – feel free to PR if you know how!
      isActive: () =>
        editor?.isActive("paragraph") &&
        !editor?.isActive("bulletList") &&
        !editor?.isActive("taskList") &&
        !editor?.isActive("orderedList"),
    },
    {
      name: "Heading 1",
      icon: Heading1,
      command: () => editor.chain().focus().toggleHeading({ level: 1 }).run(),
      isActive: () => editor?.isActive("heading", { level: 1 }),
    },
    {
      name: "Heading 2",
      icon: Heading2,
      command: () => editor.chain().focus().toggleHeading({ level: 2 }).run(),
      isActive: () => editor?.isActive("heading", { level: 2 }),
    },
    {
      name: "Heading 3",
      icon: Heading3,
      command: () => editor.chain().focus().toggleHeading({ level: 3 }).run(),
      isActive: () => editor?.isActive("heading", { level: 3 }),
    },
    {
      name: "Bullet List",
      icon: ListOrdered,
      command: () => editor.chain().focus().toggleBulletList().run(),
      isActive: () => editor?.isActive("bulletList"),
    },
    {
      name: "Numbered List",
      icon: ListOrdered,
      command: () => editor.chain().focus().toggleOrderedList().run(),
      isActive: () => editor?.isActive("orderedList"),
    },
    {
      name: "Checkboxes List",
      icon: ListTodo,
      command: () => editor.chain().focus().toggleTaskList().run(),
      isActive: () => {
        editor?.isActive("taskList");
      },
    },
    {
      name: "Quote",
      icon: TextQuote,
      command: () =>
        editor
          .chain()
          .focus()
          .toggleNode("paragraph", "paragraph")
          .toggleBlockquote()
          .run(),
      isActive: () => editor?.isActive("blockquote"),
    },
    {
      name: "Code",
      icon: Code,
      command: () => editor.chain().focus().toggleCodeBlock().run(),
      isActive: () => editor?.isActive("codeBlock"),
    },
  ];

  const activeItem = items?.filter((item) => item?.isActive()).pop() ?? {
    name: "Multiple",
  };

  return (
    <div className="relative h-full">
      <Button
        type="text"
        className="flex h-full items-center gap-1 p-2 text-sm font-medium text-stone-600 hover:bg-stone-100 active:bg-stone-200"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span>{activeItem?.name}</span>

        <ChevronDown className="h-4 w-4" />
      </Button>

      {isOpen && (
        <section className="animate-in fade-in slide-in-from-top-1 fixed top-full z-[99999] mt-1 flex w-48 flex-col overflow-hidden rounded border border-stone-200 bg-white p-1 shadow-xl">
          {items.map((item, index) => (
            <Button
              type="text"
              key={index}
              onClick={() => {
                item.command();
                setIsOpen(false);
              }}
              className={`flex items-center justify-between rounded-sm px-2 py-1 text-sm text-stone-600 hover:bg-stone-100 ${
                item.isActive() ? "text-blue-600" : ""
              }`}
            >
              <div className="flex items-center space-x-2">
                <div className="rounded-sm border border-stone-200 p-1">
                  <item.icon className="h-3 w-3" />
                </div>
                <span>{item.name}</span>
              </div>
              {activeItem.name === item.name && <Check className="h-4 w-4" />}
            </Button>
          ))}
        </section>
      )}
    </div>
  );
};
