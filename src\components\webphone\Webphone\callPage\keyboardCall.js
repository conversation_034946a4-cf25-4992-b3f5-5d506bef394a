import React, { useState } from "react";
function KeyboardCall({
  callClick,
  callClick<PERSON>ideo,
  echecCurrent<PERSON><PERSON>,
  cancelEchec<PERSON><PERSON>,
}) {
  const [number, setNumber] = useState("");
  const handler = (event) => {
    if (event.key === "Enter") {
      callClick(number);
    }
  };

  return (
    <div className="dropdown">
      <div id="sip-dialpad" className="dropdown-menu">
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            marginLeft: "30px",
          }}
        >
          <input
            type="text"
            name="number"
            id="numDisplay"
            value={number}
            onChange={(e) => setNumber(e.target.value)}
            onKeyPress={(e) => handler(e)}
          />
          <button onClick={() => setNumber(number.slice(0, -1))}>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              strokeWidth={2}
              style={{ color: "#bdbbbb" }}
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M12 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2M3 12l6.414 6.414a2 2 0 001.414.586H19a2 2 0 002-2V7a2 2 0 00-2-2h-8.172a2 2 0 00-1.414.586L3 12z"
              />
            </svg>
          </button>
        </div>
        <div className="dialLine">
          <button
            onClick={() => setNumber(number + 1)}
            type="button"
            className="btn btn-default digit cursor-pointer h-12 w-12"
            data-digit="1"
          >
            1<span>&nbsp;</span>
          </button>
          <button
            onClick={() => setNumber(number + 2)}
            type="button"
            className="btn btn-default digit cursor-pointer h-12 w-12"
            data-digit="2"
          >
            2<span>A B C</span>
          </button>
          <button
            onClick={() => setNumber(number + 3)}
            type="button"
            className="btn btn-default digit cursor-pointer h-12 w-12"
            data-digit="3"
          >
            3<span>D E F</span>
          </button>
        </div>
        <div className="dialLine">
          <button
            onClick={() => setNumber(number + 4)}
            type="button"
            className="btn btn-default digit cursor-pointer h-12 w-12"
            data-digit="4"
          >
            4<span>G H I</span>
          </button>
          <button
            onClick={() => setNumber(number + 5)}
            type="button"
            className="btn btn-default digit cursor-pointer h-12 w-12"
            data-digit="5"
          >
            5<span>J K L</span>
          </button>
          <button
            onClick={() => setNumber(number + 6)}
            type="button"
            className="btn btn-default digit cursor-pointer h-12 w-12"
            data-digit="6"
          >
            6<span>M N O</span>
          </button>
        </div>
        <div className="dialLine">
          <button
            onClick={() => setNumber(number + 7)}
            type="button"
            className="btn btn-default digit cursor-pointer h-12 w-12"
            data-digit="7"
          >
            7<span>P Q R S</span>
          </button>
          <button
            onClick={() => setNumber(number + 8)}
            type="button"
            className="btn btn-default digit cursor-pointer h-12 w-12"
            data-digit="8"
          >
            8<span>T U V</span>
          </button>
          <button
            onClick={() => setNumber(number + 9)}
            type="button"
            className="btn btn-default digit cursor-pointer h-12 w-12"
            data-digit="9"
          >
            9<span>W X Y Z</span>
          </button>
        </div>
        <div className="dialLine">
          <button
            onClick={() => setNumber(number + "*")}
            type="button"
            className="btn btn-default digit cursor-pointer h-12 w-12"
            data-digit="*"
          >
            *<span>P Q R S</span>
          </button>
          <button
            onClick={() => setNumber(number + 0)}
            type="button"
            className="btn btn-default digit cursor-pointer h-12 w-12"
            data-digit="0"
          >
            0<span>T U V</span>
          </button>
          <button
            onClick={() => setNumber(number + "#")}
            type="button"
            className="btn btn-default digit cursor-pointer h-12 w-12"
            data-digit="#"
          >
            #<span>W X Y Z</span>
          </button>
        </div>
        <div className="dialLine">
          <div className="flex justify-around">
            <button
              style={{ marginRight: "5px" }}
              id="MakeCall"
              onClick={() => callClick(number)}
              className="btnCall h-12 w-12"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                viewBox="0 0 20 20"
                fill="currentColor"
                style={{ margin: "auto" }}
              >
                {" "}
                <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />{" "}
              </svg>
            </button>

            {/* <button id="redial" className="btnCall h-12 w-12">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                strokeWidth="2"
                style={{ margin: "auto" }}
              >
                {" "}
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />{" "}
              </svg>
            </button> */}
          </div>
        </div>
      </div>
    </div>
  );
}
export default KeyboardCall;
