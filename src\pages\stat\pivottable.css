/*
  PivotTable.css
  Modernized and Themed for Sphere Front Core
  This stylesheet has been refactored to use CSS variables for a consistent and maintainable theme,
  aligning with the design language of the application (as seen in general.jsx).
  It includes enhanced styling for light and dark modes, improved interactivity, and a cleaner layout.
*/

:root {
  /* Design Tokens for Theming */
  --pvt-font-family: 'Inter', sans-serif;

  /* Colors */
  --pvt-bg-primary: #ffffff;
  --pvt-bg-secondary: #f8fafc;
  /* gray-50 */
  --pvt-bg-tertiary: #f1f5f9;
  /* gray-100 */
  --pvt-bg-interactive: #F8FAFC;
  /* slate-200 */
  --pvt-bg-interactive-hover: #F8FAFC;
  /* slate-300 */

  --pvt-text-primary: #1e293b;
  /* slate-800 */
  --pvt-text-secondary: #64748b;
  /* slate-500 */
  --pvt-text-on-accent: #ffffff;

  --pvt-border-primary: #e5e7eb;
  /* gray-200 */
  --pvt-border-interactive: #cbd5e1;
  /* slate-300 */

  /* Brand/Action Colors from general.jsx */
  --pvt-color-primary: #3b82f6;
  /* blue-500 */
  --pvt-color-primary-hover: #2563eb;
  /* blue-600 */
  --pvt-color-primary-light: rgba(59, 130, 246, 0.1);
  --pvt-color-danger: #ef4444;
  /* red-500 */
  --pvt-color-danger-hover: #dc2626;
  /* red-600 */

  /* Sizing & Spacing */
  --pvt-radius-large: 16px;
  /* rounded-2xl */
  --pvt-radius-medium: 12px;
  /* rounded-xl */
  --pvt-radius-small: 8px;
  /* rounded-lg */
  --pvt-spacing-unit: 4px;

  /* Shadows */
  --pvt-shadow-low: 0 1px 3px 0 rgb(0 0 0 / 0.07);
  --pvt-shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.08);
  --pvt-shadow-high: 0 12px 24px rgba(0, 0, 0, 0.1), 0 4px 8px rgba(0, 0, 0, 0.05);

  /* Transitions */
  --pvt-transition-speed: 0.2s;
}

/* .dark {
 
  --pvt-bg-primary: #1f2937;
 
  --pvt-bg-secondary: #334155;

  --pvt-bg-tertiary: #374151;
 
  --pvt-bg-interactive: #475569;

  --pvt-bg-interactive-hover: #64748b;


  --pvt-text-primary: #f1f5f9;
 
  --pvt-text-secondary: #94a3b8;


  --pvt-border-primary: #374151;
 
  --pvt-border-interactive: #4b5563;
 

  --pvt-shadow-medium: 0 10px 25px rgba(0, 0, 0, 0.3);
  --pvt-shadow-high: 0 12px 20px rgba(0, 0, 0, 0.2), 0 4px 8px rgba(0, 0, 0, 0.15);
} */

/* --- Base & Global Styles --- */

.pvtUi {
  color: var(--pvt-text-primary);
  border-collapse: separate;
  border-spacing: calc(var(--pvt-spacing-unit) * 2);
  margin: 0 auto;
  width: 80vw;
  max-width: 1500px;
  min-width: 1000px;
}

.pvtUi>tbody>tr>td {
  vertical-align: top;
}

/* --- Drag & Drop Areas --- */

.pvtAxisContainer,
.pvtUnused {
  background: var(--pvt-bg-primary);
  padding: calc(var(--pvt-spacing-unit) * 3);
  border-radius: var(--pvt-radius-medium);
  border: 1px solid var(--pvt-border-interactive);
  min-height: 60px;
  transition: all var(--pvt-transition-speed) ease;
  min-width: 12%;
  max-width: 22%;
  width: 18%;
}

.pvtVals,
.pvtRows {
  background: var(--pvt-bg-primary);
  padding: calc(var(--pvt-spacing-unit) * 3);
  border-radius: var(--pvt-radius-medium);
  border: 1px solid var(--pvt-border-interactive);
  min-height: 60px;
  transition: all var(--pvt-transition-speed) ease;
  min-width: 12%;
  max-width: 22%;
  width: 2%;
}

.pvtRenderers {
  background: var(--pvt-bg-primary);
  padding: calc(var(--pvt-spacing-unit) * 3);
  border-radius: var(--pvt-radius-medium);
  border: 1px solid var(--pvt-border-interactive);
  min-height: 60px;
  transition: all var(--pvt-transition-speed) ease;
  min-width: 12%;
  max-width: 22%;
  width: 2%;
}

.pvtUnused {
  display: flex;
  flex-direction: column;
  gap: calc(var(--pvt-spacing-unit) * 1.5);
  max-height: 450px;
  overflow-y: auto;
  min-width: 200px;
}


.pvtUnused:has(:nth-child(1)) {
  display: block;
  flex-direction: row;
}

.pvtRows {
  grid-area: rows;
}

.pvtCols {
  grid-area: cols;
}

.pvtAxisContainer li {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.pvtAxisContainer li span.pvtAttr {
  background-color: var(--pvt-bg-secondary);
  color: var(--pvt-text-primary);
  padding: calc(var(--pvt-spacing-unit) * 2) calc(var(--pvt-spacing-unit) * 3);
  margin: calc(var(--pvt-spacing-unit) * 1);
  border-radius: var(--pvt-radius-small);
  border: 1px solid var(--pvt-border-primary);
  box-shadow: var(--pvt-shadow-low);
  cursor: grab;
  transition: all 0.2s ease;
  font-size: 13px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  white-space: normal;
  word-break: break-word;
}

.pvtAxisContainer li span.pvtAttr:hover {
  background-color: var(--pvt-bg-tertiary);
  transform: translateY(-2px);
  box-shadow: var(--pvt-shadow-medium);
  border-color: var(--pvt-border-interactive);
}

/* --- Dropdown Controls --- */

.pvtDropdown {
  display: inline-block;
  position: relative;
}

.pvtDropdownCurrent,
.pvtUnused {
  width: 100%;
  min-width: 240px;
  max-width: 340px;
  box-sizing: border-box;
}

.pvtDropdownCurrent {
  padding: 8px 12px;
  background: var(--pvt-bg-primary);
  border: 1px solid var(--pvt-border-interactive);
  border-radius: var(--pvt-radius-small);
  transition: all var(--pvt-transition-speed) ease;
  font-size: 14px;
  font-family: var(--pvt-font-family);
  cursor: pointer;
  color: var(--pvt-text-primary);
}

.pvtDropdownCurrent:hover,
.pvtDropdownCurrent:focus {
  border-color: var(--pvt-color-primary);
  box-shadow: 0 0 0 4px var(--pvt-color-primary-light);
  outline: none;
}

.pvtDropdownMenu {
  position: absolute;
  z-index: 10;
  padding: calc(var(--pvt-spacing-unit) * 3);
  display: flex;
  flex-wrap: wrap;
  gap: calc(var(--pvt-spacing-unit) * 2);
  border-radius: var(--pvt-radius-medium);
  background-color: var(--pvt-bg-primary);
  border: 1px solid var(--pvt-border-primary);
  box-shadow: var(--pvt-shadow-high);
  width: 600px;
}

.pvtDropdownValue {
  flex: 1 1 180px;
  padding: 10px 14px;
  border-radius: var(--pvt-radius-small);
  background: var(--pvt-bg-interactive);
  color: var(--pvt-text-primary);
  transition: background-color var(--pvt-transition-speed);
  cursor: pointer;
  font-family: var(--pvt-font-family);
  font-size: 14px;
}

.pvtDropdownValue:hover {
  background: var(--pvt-bg-interactive-hover);
}

/* --- Filter Box --- */

/* .pvtFilterBox {
  width: 100%;
  max-width: 400px;
  background-color: var(--pvt-bg-primary);
  border: 1px solid var(--pvt-border-primary);
  border-radius: var(--pvt-radius-medium);
  box-shadow: var(--pvt-shadow-high);
  padding: calc(var(--pvt-spacing-unit) * 6);
  font-family: var(--pvt-font-family);
  font-size: 14px;
  color: var(--pvt-text-primary);
  z-index: 100;
  position: absolute;
}

.pvtFilterBox h4 {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 16px 0;
}

.pvtCloseX {
  float: right;
  font-size: 20px;
  font-weight: bold;
  color: var(--pvt-color-danger);
  cursor: pointer;
  transition: transform 0.2s ease, color var(--pvt-transition-speed) ease;
  margin: 2%;
}

.pvtDragHandle {
  margin: 2%;
}

.pvtCloseX:hover {
  transform: scale(1.1);
  color: var(--pvt-color-danger-hover);
}

.pvtSearch {
  width: 100%;
  height: 40px;
  padding: 12px 16px;
  border: 1px solid var(--pvt-border-interactive);
  border-radius: var(--pvt-radius-small);
  margin-bottom: 16px;
  font-size: 14px;
  background-color: var(--pvt-bg-secondary);
  color: var(--pvt-text-primary);
  transition: border-color var(--pvt-transition-speed) ease, box-shadow var(--pvt-transition-speed) ease;
}

.pvtSearch:focus {
  border-color: var(--pvt-color-primary);
  box-shadow: 0 0 0 4px var(--pvt-color-primary-light);
  outline: none;
}

.pvtCheckContainer p {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  margin: 2px 0;
  font-size: 14px;
  transition: all 0.2s ease;
  border-radius: var(--pvt-radius-small);
  cursor: pointer;
}

.pvtCheckContainer p:hover {
  background-color: var(--pvt-bg-tertiary);
}

.pvtCheckContainer .selected {
  background-color: var(--pvt-color-primary-light);
  font-weight: 500;
  color: var(--pvt-color-primary-hover);
}



.pvtButton {
  display: inline-block;
  background: var(--pvt-color-primary);
  color: var(--pvt-text-on-accent);
  font-weight: 600;
  padding: 10px 16px;
  border: none;
  border-radius: var(--pvt-radius-small);
  text-align: center;
  margin-top: 12px;
  cursor: pointer;
  text-decoration: none;
  transition: background-color var(--pvt-transition-speed) ease, box-shadow var(--pvt-transition-speed) ease;
}

.pvtButton:hover {
  background: var(--pvt-color-primary-hover);
  box-shadow: var(--pvt-shadow-low);
}

.pvtOnly {
  color: var(--pvt-text-secondary);
  font-size: 12px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 4px;
  margin-right: 8px;
  transition: background-color var(--pvt-transition-speed) ease;
}

.pvtOnly:hover {
  background-color: var(--pvt-bg-interactive);
  color: var(--pvt-text-primary);
} */

/* --- Final Output Table --- */
.pvtCheckContainer {
  margin-top: 1%;
}

.pvtTable {
  width: 82%;
  border-collapse: separate;
  border-spacing: 0;
  font-size: 14px;
  font-family: var(--pvt-font-family);
  background-color: var(--pvt-bg-primary);
  border: 1px solid var(--pvt-border-primary);
  border-radius: var(--pvt-radius-medium);
  overflow: hidden;
  margin-top: calc(var(--pvt-spacing-unit) * 4);
  min-width: 60%;
  max-width: 100%;
}

.pvtTable td,
.pvtTable th {
  padding: calc(var(--pvt-spacing-unit) * 3);
  /* 12px */
  border-bottom: 1px solid var(--pvt-border-primary);
  text-align: left;
  transition: background-color var(--pvt-transition-speed) ease;
}

.pvtTable tbody tr:last-child td,
.pvtTable tbody tr:last-child th {
  border-bottom: none;
}

.pvtTable thead th {
  background-color: var(--pvt-bg-secondary);
  font-weight: 600;
  color: var(--pvt-text-primary);
}

.pvtTable tbody tr:hover td,
.pvtTable tbody tr:hover th {
  background-color: var(--pvt-bg-tertiary);
}

.pvtTotal,
.pvtGrandTotal {
  font-weight: bold;
}

.pvtRowLabel,
.pvtColLabel {
  font-weight: 600;
}

/* --- Scrollbar --- */
/* .pvtCheckContainer::-webkit-scrollbar {
  width: 6px;
}

.pvtCheckContainer::-webkit-scrollbar-thumb {
  background-color: var(--pvt-border-interactive);
  border-radius: 3px;
} */

/* .dark .pvtCheckContainer::-webkit-scrollbar-thumb {
  background-color: var(--pvt-bg-interactive-hover);
} */

/* Scrollbar for unused fields */
.pvtUnused::-webkit-scrollbar {
  width: 6px;
}

.pvtUnused::-webkit-scrollbar-track {
  background: transparent;
}

.pvtUnused::-webkit-scrollbar-thumb {
  background-color: var(--pvt-border-interactive);
  border-radius: 3px;
}

.pvtUnused::-webkit-scrollbar-thumb:hover {
  background-color: var(--pvt-bg-interactive-hover);
}

/* --- Responsive Design --- */
@media (max-width: 992px) {
  /* The default table layout is already somewhat responsive */
}

@media (max-width: 768px) {
  .pvtDropdownMenu {
    width: 90vw;
    max-width: 400px;
  }
}

/* --- Legacy & Highcharts Styles --- */
/* These are kept for compatibility but could be refactored if the consuming components are updated */

.highcharts-figure,
.addChart,
.result {
  background-color: var(--pvt-bg-primary);
  border-radius: var(--pvt-radius-large);
  padding: 24px;
  border: 2px solid var(--pvt-border-primary);
  transition: background-color var(--pvt-transition-speed) ease;
  margin-top: 20px;
}

/* .dark .addChart,
.dark .result,
.dark .highcharts-figure {
  background-color: var(--pvt-bg-primary);
  border-color: var(--pvt-border-interactive);
} */

.br {
  border-top: 1px solid var(--pvt-border-primary);
}

.brd {
  border-bottom: 1px solid var(--pvt-border-primary);
}


.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 21, 41, 0.05);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #001529, #002140);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #002140, #001529);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

.shadow-3xl {
  box-shadow: 0 25px 50px -12px rgba(0, 21, 41, 0.4);
}