import { Badge, Checkbox, ConfigProvider, Divider, Empty, List, Typography } from "antd";
import { useCallback, useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { FiMail, FiPhone } from "react-icons/fi";

import { URL_ENV } from "index";
import { AvatarChat } from "components/Chat";
import { getName } from "../../utils/ConversationUtils";
import useDebounce from "pages/components/UseDebounce/UseDebounce";
const MembersAdd = ({ listAllUsers, setAllListUsers, search, add = false }) => {
  const [checkAll, setCheckAll] = useState(false);
  const [indeterminate, setIndeterminate] = useState(false);
  const [fin, setFin] = useState(10);
  const onlineUser = useSelector((state) => state.ChatRealTime.onlineUser);
  const { t } = useTranslation("common");
  const deferredSearch = useDebounce(search, 250);
  const onChange = ({ checked = false, id = null, type = "default" }) => {
    let array = [...listAllUsers];
    array.map((el) => {
      if (el._id === id || type === "all") el.selected = checked;
      return el;
    });
    setAllListUsers([...array]);
  };
  useEffect(() => {
    const filterListLength = listAllUsers?.filter((el) => el.selected)?.length;
    if (filterListLength === 0) {
      setCheckAll(false);
      setIndeterminate(false);
    } else if (filterListLength === listAllUsers.length) {
      setCheckAll(true);
      setIndeterminate(false);
    } else if (filterListLength !== listAllUsers.length) {
      setCheckAll(false);

      setIndeterminate(true);
    }
  }, [listAllUsers]);

  const divRef = useRef(null);

  const calculateScrollPercentage = () => {
    const element = divRef.current;
    if (element) {
      const { scrollTop, scrollHeight, clientHeight } = element;
      if (scrollTop + clientHeight >= scrollHeight - 10) {
        setFin((prevFin) => prevFin + 10);
      }
    }
  };

  useEffect(() => {
    const handleScroll = () => {
      calculateScrollPercentage();
    };

    const element = divRef.current;
    if (element) {
      element.addEventListener("scroll", handleScroll);
    }

    return () => {
      if (element) {
        element.removeEventListener("scroll", handleScroll);
      }
    };
  }, []);
  const getIndexDivider = useCallback(
    (type = "") =>
      [
        ...listAllUsers
          .filter(
            (el) =>
              getName(el?.name, "name")?.toLowerCase()?.includes(deferredSearch.toLowerCase()) ||
              el?.email?.toLowerCase()?.includes(deferredSearch.toLowerCase()) ||
              el?.post_number?.toString()?.includes(deferredSearch)
          )
          .slice(0, fin),
      ].findIndex((item) => item.type === type),
    [deferredSearch, fin, listAllUsers]
  );
  return (
    <div className="min-w-[320px]">
      <div className="my-2 flex w-full items-center justify-between">
        <Typography.Text type="secondary">
          {(listAllUsers?.filter((el) => el.selected)?.length ?? 0) +
            "/" +
            listAllUsers.length +
            " " +
            t("chat.header.members")}
        </Typography.Text>
        <Checkbox
          indeterminate={indeterminate}
          onClick={(e) =>
            onChange({
              checked: e.target.checked,
              id: null,
              type: "all",
            })
          }
          checked={checkAll}
        >
          {t("chat.selectAll")}
        </Checkbox>
      </div>

      <div
        id="scrollableDiv"
        className="relative"
        style={{
          maxHeight: 180,
          overflow: "auto",
          padding: "0",
        }}
        ref={divRef}
      >
        <ConfigProvider renderEmpty={() => <Empty />}>
          <List
            rowKey={(item, index) => `list_item_key${item._id}_${item.type}_${index}`}
            className="membersList"
            dataSource={listAllUsers
              .filter(
                (el) =>
                  getName(el?.name, "name")
                    ?.toLowerCase()
                    ?.includes(deferredSearch?.toLowerCase()) ||
                  el?.email?.toLowerCase()?.includes(deferredSearch?.toLowerCase()) ||
                  el?.post_number?.toString()?.includes(deferredSearch)
              )
              ?.slice(0, fin)}
            renderItem={(item, index) => (
              <div key={`item_key${item._id}_${item.type}`} className=" flex  flex-col">
                {getIndexDivider("disc") !== -1 && index === 0 && (
                  <Divider
                    plain
                    style={{
                      color: "#CCC",

                      margin: "0 !important",
                    }}
                    orientation="left"
                  >
                    <Typography.Text className="  py-2   text-xs font-medium uppercase text-[#388cfa]">
                      {t("chat.searchSide.conversations")}
                    </Typography.Text>
                  </Divider>
                )}
                {index === getIndexDivider("users") && (
                  <Divider
                    plain
                    style={{
                      color: "#CCC",
                      margin: "0 !important",

                      backgroundColor: "white",
                    }}
                    orientation="left"
                  >
                    <Typography.Text className="py-2 text-xs font-medium uppercase text-[#388cfa]">
                      {t("chat.searchSide.colleagues")}
                    </Typography.Text>
                  </Divider>
                )}
                <List.Item
                  key={item._id}
                  onClick={() =>
                    onChange({
                      checked: !item.selected,
                      id: item._id,
                      type: "default",
                    })
                  }
                  style={{
                    zIndex: 1,
                    cursor: "pointer",
                    backgroundColor: item.selected ? "rgb(241 245 249)" : "",
                  }}
                  className=" hover:bg-slate-300"
                >
                  <List.Item.Meta
                    avatar={
                      <Badge
                        dot={item.admin_id === undefined}
                        color={
                          onlineUser[item?.uuid] === "away"
                            ? "orange"
                            : onlineUser[item?.uuid] === "busy"
                            ? "red"
                            : onlineUser[item?.uuid] === "online"
                            ? "green"
                            : "#a6a6a6"
                        }
                        offset={[-5, 30]}
                      >
                        <AvatarChat
                          iconPublicSize="14px"
                          isPublic={item.predefined === 2}
                          url={
                            item.admin_id
                              ? URL_ENV?.REACT_APP_OAUTH_CHAT_API +
                                process.env.REACT_APP_SUFFIX_CHAT_GROUP_STORAGE +
                                item?.image
                              : URL_ENV?.REACT_APP_BASE_URL +
                                URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                                item?.image
                          }
                          fontSize="0.875rem"
                          type={item.admin_id ? "room" : "user"}
                          size={36}
                          height={9}
                          width={9}
                          name={getName(item?.name, "avatar")}
                          hasImage={item.image}
                        />
                      </Badge>
                    }
                    title={
                      <label className="flex items-center gap-x-0.5">
                        {getName(item.name, "name")}
                        <em className="text-xs text-gray-400">
                          {item.admin_id ? "(" + t("chat.chat_group") + ") " : ""}
                        </em>
                      </label>
                    }
                    description={
                      <>
                        {item.admin_id ? (
                          <span>
                            {" "}
                            {["undefined", "null"].includes(item.description) || !item.description
                              ? ""
                              : item.description}{" "}
                          </span>
                        ) : (
                          <div className="flex items-center space-x-2">
                            <span
                              className={`flex ${
                                add ? "w-40" : "auto"
                              } items-center space-x-1 overflow-hidden`}
                            >
                              {" "}
                              <FiMail className="mr-1" />
                              <span className="truncate">{item.email}</span>
                            </span>
                            <span className="flex items-center space-x-1">
                              {" "}
                              <FiPhone className="mr-1" />
                              {item.post_number}
                            </span>
                          </div>
                        )}
                      </>
                    }
                  />
                  <div className="pr-2">
                    <Checkbox checked={item.selected}></Checkbox>
                  </div>
                </List.Item>
              </div>
            )}
          />
        </ConfigProvider>
      </div>
    </div>
  );
};
export default MembersAdd;
