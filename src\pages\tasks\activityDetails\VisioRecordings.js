import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Popconfirm } from "antd";
import {
  DeleteOutlined,
  DownloadOutlined,
  QuestionCircleOutlined,
} from "@ant-design/icons";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";

import { dayjs_timezone } from "App";
import { URL_ENV } from "index";

const VisioRecordings = ({ items, deleteRecord }) => {
  const [downloadProgress, setDownloadProgress] = useState(false);
  const [keyOfVideo, setKeyOfVideo] = useState(null);

  const [t] = useTranslation("common");
  const user = useSelector((state) => state?.user?.user);

  const handleDownloadFile = (e, video) => {
    if (e) e.preventDefault();
    setDownloadProgress(true);
    if (video) {
      fetch(`${URL_ENV?.REACT_APP_BASE_URL}/${video?.path}`)
        .then((response) => response?.blob())
        .then((blob) => {
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement("a");
          a.style.display = "none";
          a.href = url;
          a.download =
            `${t("visio.recordedDownloadTitle")} ${t(
              "voip.at"
            )} ${dayjs_timezone(video?.recordingStartDate).format(
              `${user?.location?.date_format} ${user?.location?.time_format}`
            )}.mp4` || "video.mp4";
          document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(url);
        })
        .catch((error) => {
          console.log({ error });
        })
        .finally(() => {
          setDownloadProgress(false);
          setKeyOfVideo(null);
        });
    }
  };

  return (
    <div className="relative grid h-auto grid-cols-2 justify-center gap-4 overflow-auto pb-2 pl-3">
      {items?.map((video, i) => (
        <div className="relative self-center justify-self-center p-1">
          <Card
            className={`record-video-card`}
            hoverable
            style={{
              width: 200,
              height: "auto",
            }}
            cover={
              <video
                id={"videoID-" + video?._id}
                key={video?.id}
                src={`${URL_ENV?.REACT_APP_BASE_URL}/${video?.path}`}
                controls
                className={`cursor-pointer p-1.5`}
              >
                Your browser does not support the video tag.
              </video>
            }
            actions={[
              <Button
                type="text"
                size="small"
                shape="circle"
                icon={<DownloadOutlined key="DownloadOutlined" />}
                onClick={(e) => {
                  setKeyOfVideo(video?.path);
                  handleDownloadFile(e, video);
                }}
                loading={keyOfVideo === video?.path && downloadProgress}
              />,
              <Popconfirm
                title={t("voip.areYouSureToDelete")}
                icon={
                  <QuestionCircleOutlined
                    style={{
                      color: "red",
                    }}
                  />
                }
                onConfirm={() => deleteRecord(video?._id)}
              >
                <Button
                  danger
                  type="text"
                  size="small"
                  shape="circle"
                  icon={<DeleteOutlined key="DeleteOutlined" />}
                />
              </Popconfirm>,
            ]}
            styles={{
              actions: {
                position: "relative",
                height: "30px",
                padding: 0,
              },
            }}
          >
            <Card.Meta
              title={
                <Typography.Text
                  ellipsis={{
                    tooltip: true,
                  }}
                >{`${t("visio.recordedDownloadTitle")} ${t(
                  "voip.at"
                )} ${dayjs_timezone(video?.recordingStartDate).format(
                  `${user?.location?.date_format} ${user?.location?.time_format}`
                )}`}</Typography.Text>
              }
              description={video?.size}
            />
          </Card>
        </div>
      ))}
    </div>
  );
};

export default VisioRecordings;

/*  */
