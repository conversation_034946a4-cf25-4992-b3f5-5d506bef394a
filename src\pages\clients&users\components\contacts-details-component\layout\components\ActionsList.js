import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import {
  setActiveActivity360,
  setActiveTab360,
} from "../../../../../../new-redux/actions/vue360.actions/vue360";
import { Button, Dropdown, Space, Tooltip, Typography } from "antd";
import {
  CalendarOutlined,
  MailOutlined,
  PlayCircleOutlined,
  PlusOutlined,
  VideoCameraOutlined,
} from "@ant-design/icons";
import { HiOutlineCalendar, HiOutlineDocumentDownload } from "react-icons/hi";
import { FiCopy, FiEdit, FiList } from "react-icons/fi";
import { GrDocumentNotes } from "react-icons/gr";
import CreateTask from "../../../../../voip/components/CreateTask";
import useActionCall from "../../../../../voip/helpers/ActionCall";
import FormCreate from "../../../FormCreate";
import ShareVisio from "../../../../../layouts/visio/components/share-visio";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { BookCopy, HeartHandshake, Smartphone } from "lucide-react";
import { toastNotification } from "../../../../../../components/ToastNotification";
import { getMailsAndPhones } from "../../../../services/services";
import { useSelector } from "react-redux";
import { checkIfPathOnView360 } from "pages/voip/helpers/helpersFunc";
import { useLocation } from "react-router-dom";
import TicketIconSphere from "components/icons/TicketIconSphere";
import { setEmailFields } from "pages/rmc/mailing/main-components/email-composer-modal/utils";

const { Paragraph, Link } = Typography;
const acceptedFamilyIds = [1, 2, 4, 9];

const ActionsList = ({
  id,
  name,
  family_id,
  elementID,
  module,
  source,
  isElementUpdate,
  isAssociationUpdate,
  setCatchChange,
  setSelectedKey = () => {}, //optionnal,
  from = "",
}) => {
  //
  const { t } = useTranslation("common");
  const call = useActionCall();
  const dispatch = useDispatch();
  const location = useLocation();
  const callNumber = useRef(null);
  const currentUser = useSelector((state) => state.user.user);
  const numberCallInProgress = useSelector(
    (state) => state.voipBlackList.receiverInfo?.number
  );
  const contactInfo = useSelector(
    (state) => state?.contacts?.contactHeaderInfo
  );
  const { openView360InDrawer, contactInfoFromDrawer, openChatInViewSphere } =
    useSelector((state) => state?.vue360);

  const [openQuickVideoCall, setOpenQuickVideoCall] = useState(false);
  const [openTaskVisio, setOpenTaskVisio] = useState(false);
  const [openTask, setOpenTask] = useState(false);
  const [selectedFamily, setSelectedFamily] = useState(null);
  const [openCreateForm, setOpenCreateForm] = useState(false);
  const [phones, setPhones] = useState([]);
  const [emails, setEmails] = useState([]);
  const [extension, setExtension] = useState(null);
  //
  //
  const fetchPhonesAndMails = useCallback(async () => {
    try {
      let familyId = module ? module : family_id;
      let elementId = elementID ? elementID : id;
      if (!familyId || !elementId) return;
      const { data } = await getMailsAndPhones(familyId, elementId);
      setPhones(data?.phones);
      setEmails(data?.emails);
      !!data?.extension && setExtension(data?.extension);
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      throw new Error(err?.message ? err.message : err);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    module,
    family_id,
    elementID,
    id,
    t,
    isElementUpdate,
    isAssociationUpdate,
  ]);

  useEffect(() => {
    fetchPhonesAndMails();
  }, [fetchPhonesAndMails]);
  //

  const copyIcon = (text) => (
    <Paragraph
      copyable={{
        text: text,
        icon: [
          <FiCopy
            style={{
              color: "rgb(22, 119, 255)",
              marginTop: "2px",
              fontSize: "15px",
            }}
          />,
        ],
      }}
    />
  );
  //
  useEffect(() => {
    callNumber.current = numberCallInProgress;
  }, [numberCallInProgress]);

  //
  const callMenu = useMemo(
    () => [
      ...(family_id === 4 && !!extension
        ? [
            {
              key: "extension",
              label: (
                <div className="flex flex-row items-center justify-between space-x-1.5">
                  <Link>{extension}</Link>
                  {copyIcon(`${extension}`)}
                </div>
              ),
              onClick: () =>
                !!extension &&
                currentUser?.extension !== extension &&
                call(`${extension}`, id, family_id),
            },
          ]
        : []),
      ...(phones?.length
        ? phones.map(([dial, num], i) => ({
            key: i,
            label: (
              <Space>
                <Link>{`(${dial}) ${num}`}</Link>
                {copyIcon(`${dial?.replace("+", "00")}${num}`)}
              </Space>
            ),
            onClick: () =>
              call(`${dial?.replace("+", "00")}${num}`, id, family_id),
            // children: item?.phone
            //   ?.filter((innerArr) =>
            //     innerArr.every((element) => element !== null)
            //   )
            //   ?.map((phone, index) => ({
            //     key: index,
            //     label: (
            //       <Space>
            //         <Link>{`(${phone?.[0]}) ${phone?.[1]}`}</Link>
            //         {copyIcon(`${phone?.[0]?.replace("+", "00")}${phone?.[1]}`)}
            //       </Space>
            //     ),
            //     onClick: () =>
            //       call(
            //         `${phone?.[0]?.replace("+", "00")}${phone?.[1]}`,
            //         id,
            //         family_id
            //       ),
            //   })),
          }))
        : []),
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [phones, extension]
  );
  // console.log({ callMenu });
  const emailMenu = useMemo(
    () => [
      ...(emails?.length
        ? emails.map((email, i) => ({
            key: i,
            label: (
              <Space>
                <Link>{email}</Link>
                {copyIcon(email)}
              </Space>
            ),
            onClick: () => {
              dispatch(
                setEmailFields({
                  receivers: [email],
                  contactId: contactInfo?.id,
                  familyId: contactInfo?.family_id,
                })
              );
            },
          }))
        : []),
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [emails]
  );

  const visioMenu = useMemo(
    () => [
      {
        key: "dropdown1",
        label: t("chat.header.visio.createVideoConferance"),
        icon: <PlayCircleOutlined style={{ fontSize: "15px" }} />,
        onClick: () => setOpenQuickVideoCall((p) => !p),
      },
      {
        key: "dropdown2",
        label: t("chat.header.createVideoLater"),
        icon: <HiOutlineCalendar style={{ fontSize: "15px" }} />,
        onClick: () => setOpenTaskVisio((p) => !p),
      },
    ],
    [t]
  );

  const plusMenu = useMemo(
    () => [
      {
        key: "deal",
        label: "Create deal",
        icon: <HeartHandshake size={18} />,
        onClick: () => {
          setSelectedFamily(3);
          setOpenCreateForm(true);
        },
      },
      {
        key: "ticket",
        label: "Create ticket",
        icon: <TicketIconSphere size={20} />,
        onClick: () => {
          setSelectedFamily(6);
          setOpenCreateForm(true);
        },
      },
      {
        key: "dropdown4",
        label: "Add document",
        icon: <HiOutlineDocumentDownload className="h-[19px] w-[19px]" />,
        onClick: () => {
          // alert("tt");
          setSelectedKey("7");

          dispatch(setActiveTab360(8));
          dispatch(setActiveActivity360("1"));
        },
      },
    ],
    []
  );
  //
  // const handleDropdownClick = (e) => {
  //   const key = e?.key;
  //   switch (key) {
  //     case "edit":
  //       elementInfo?.id && setOpenDrawerUpdate(true);
  //       break;
  //     default:
  //       message.info("This feature is not ready yet!");
  //       break;
  //   }
  // };
  const DropdownProps = {
    items: [
      {
        label: t("contacts.edit"),
        key: "edit",
        icon: <FiEdit className="h-4 w-4 text-slate-500" />,
      },

      {
        label: "Clone",
        icon: <BookCopy className="w-[16px] text-slate-500" />,

        key: "3",
      },
      // {
      //   label: "Archived",
      //   icon: <Archive className="w-[16px] text-slate-500" />,
      //   key: "4",
      // },
      // {
      //   type: "divider",
      // },
      // {
      //   label: t("contacts.delete"),
      //   key: "1",
      //   icon: <FiTrash className="h-4 w-4 " />,
      //   danger: true,
      // },
    ],
    // onClick: (e) => handleDropdownClick(e),
  };
  const getElementId = () => {
    const element = openView360InDrawer
      ? {
          id: contactInfoFromDrawer?.id,
          familyId: contactInfoFromDrawer?.family_id,
        }
      : { id: contactInfo?.id, familyId: contactInfo?.family_id };
    if (element.id && acceptedFamilyIds.includes(element.familyId)) {
      return element.id;
    }
    return null;
  };
  const isEntity = () => {
    const element = openView360InDrawer
      ? contactInfoFromDrawer?.family_id
      : contactInfo?.family_id;
    if (element && acceptedFamilyIds.includes(element)) {
      return true;
    }
    return false;
  };

  return (
    <div
      className={`flex justify-center space-x-1 ${
        from === "viewSphere" || from === "directory" ? "pt-0" : "pt-2"
      }`}
    >
      {isEntity() ? (
        // <Tooltip title="Call">
        <Dropdown
          menu={{ items: callMenu }}
          disabled={phones.length === 0 && !extension}
        >
          <Button
            size={
              from === "viewSphere"
                ? "small"
                : from === "directory"
                ? "small"
                : "large"
            }
            style={{ height: "30px", width: "34px" }}
            icon={<Smartphone size={16} />}
          />
        </Dropdown>
      ) : null}

      {isEntity() ? (
        emails?.length ? (
          <Dropdown menu={{ items: emailMenu }}>
            <Button
              size={
                from === "viewSphere"
                  ? "small"
                  : from === "directory"
                  ? "small"
                  : "large"
              }
              style={{ height: "30px", width: "34px" }}
              icon={<MailOutlined />}
              // onClick={() => setOpenModalMessage(true)}
            />
          </Dropdown>
        ) : (
          <Tooltip title={t("vue360.noEmail")}>
            <Button
              size={
                from === "viewSphere"
                  ? "small"
                  : from === "directory"
                  ? "small"
                  : "large"
              }
              style={{ height: "30px", width: "34px" }}
              disabled
              icon={<MailOutlined />}
            />
          </Tooltip>
        )
      ) : null}

      <Tooltip title={t("voip.createTask")}>
        <Button
          size={
            from === "viewSphere"
              ? "small"
              : from === "directory"
              ? "small"
              : "large"
          }
          style={{ height: "30px", width: "34px" }}
          icon={<CalendarOutlined />}
          onClick={() => setOpenTask(true)}
        />
      </Tooltip>
      <Tooltip title="Visio">
        <Dropdown
          placement="bottomRight"
          menu={{ items: visioMenu }}
          disabled={openChatInViewSphere}
        >
          <Button
            size={
              from === "viewSphere"
                ? "small"
                : from === "directory"
                ? "small"
                : "large"
            }
            style={{ height: "30px", width: "34px" }}
            icon={<VideoCameraOutlined />}
          />
        </Dropdown>
      </Tooltip>
      {from !== "viewSphere" && from !== "directory" && (
        <Tooltip title="Add note">
          <Button
            size={
              from === "viewSphere"
                ? "small"
                : from === "directory"
                ? "small"
                : "large"
            }
            style={{ height: "30px", width: "34px" }}
            icon={<GrDocumentNotes className="h-[15px] w-[15px]" />}
            onClick={() => {
              setSelectedKey("7");
              dispatch(setActiveActivity360("1"));
              dispatch(setActiveTab360(6));
            }}
          />
        </Tooltip>
      )}

      {from !== "viewSphere" && from !== "directory" && (
        <Tooltip title={t("todolist.createList")}>
          <Button
            size={
              from === "viewSphere"
                ? "small"
                : from === "directory"
                ? "small"
                : "large"
            }
            style={{ height: "30px", width: "34px" }}
            icon={<FiList className="h-[18px] w-[18px]" />}
            onClick={() => {
              // setSelectedKey("3");
              // dispatch(setActiveActivity360("7"));
              // setOpenModalCheckList(true);
            }}
          />
        </Tooltip>
      )}
      {from !== "viewSphere" && from !== "directory" && (
        <Dropdown
          placement="bottomRight"
          menu={{
            items: plusMenu,
          }}
        >
          <Button
            size={
              from === "viewSphere"
                ? "small"
                : from === "directory"
                ? "small"
                : "large"
            }
            style={{ height: "30px", width: "34px" }}
            icon={<PlusOutlined />}
          />
        </Dropdown>
      )}

      {openQuickVideoCall && (
        <ShareVisio
          open={openQuickVideoCall}
          onClose={() => setOpenQuickVideoCall(false)}
          guest={getElementId()}
        />
      )}
      <CreateTask
        titleLabel={"Visio Conf " + moment().format("DDMMYYYY HH:mm:ss")}
        open={openTaskVisio}
        source={"visio"}
        setOpen={setOpenTaskVisio}
        mask={false}
        fromVue360={true}
        setSelectedKey={setSelectedKey}
      />
      <CreateTask
        open={openTask}
        setOpen={setOpenTask}
        mask={false}
        fromVue360={true}
        setSelectedKey={setSelectedKey}
      />
      <FormCreate
        open={selectedFamily && openCreateForm}
        setOpen={setOpenCreateForm}
        familyId={selectedFamily}
        externalSource={{
          id: elementID ? elementID : id,
          source: "call",
          familyId: selectedFamily,
          callInProcess: !(
            checkIfPathOnView360(location.pathname) ||
            // location.pathname === "/directory"
            location.pathname === "/telephony/directory"
          ),
          callNumber: callNumber.current,
        }}
        setCatchChange={setCatchChange}
      />
    </div>
  );
};

export default ActionsList;
