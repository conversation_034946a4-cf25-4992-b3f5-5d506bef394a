{"name": "sphere", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^4.8.0", "@ant-design/plots": "^1.2.6", "@babel/core": "^7.23.9", "@babel/preset-env": "^7.23.9", "@babel/preset-react": "^7.23.3", "@babel/register": "^7.18.9", "@dnd-kit/core": "^6.0.8", "@dnd-kit/sortable": "^7.0.2", "@dnd-kit/utilities": "^3.2.1", "@emoji-mart/data": "^1.0.8", "@emoji-mart/react": "^1.0.1", "@fullcalendar/core": "^6.1.4", "@fullcalendar/daygrid": "^6.1.4", "@fullcalendar/interaction": "^6.1.4", "@fullcalendar/list": "^6.1.5", "@fullcalendar/react": "^6.1.4", "@fullcalendar/timegrid": "^6.1.4", "@headlessui/react": "^1.7.7", "@heroicons/react": "^2.0.13", "@jitsi/react-sdk": "^1.4.0", "@onlyoffice/document-editor-react": "^1.5.1", "@tanstack/react-query": "^4.29.1", "@tanstack/react-query-devtools": "^4.29.17", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@tippyjs/react": "^4.2.6", "@tiptap/core": "^2.0.3", "@tiptap/extension-bubble-menu": "^2.2.1", "@tiptap/extension-code-block": "^2.1.6", "@tiptap/extension-code-block-lowlight": "^2.2.1", "@tiptap/extension-color": "^2.0.3", "@tiptap/extension-heading": "^2.0.3", "@tiptap/extension-highlight": "^2.0.3", "@tiptap/extension-image": "^2.0.3", "@tiptap/extension-link": "^2.0.3", "@tiptap/extension-mention": "^2.0.3", "@tiptap/extension-paragraph": "^2.0.3", "@tiptap/extension-placeholder": "^2.0.3", "@tiptap/extension-table": "^2.2.1", "@tiptap/extension-table-cell": "^2.2.1", "@tiptap/extension-table-header": "^2.2.1", "@tiptap/extension-table-row": "^2.2.1", "@tiptap/extension-task-item": "^2.9.1", "@tiptap/extension-task-list": "^2.9.1", "@tiptap/extension-text-align": "^2.2.1", "@tiptap/extension-text-style": "^2.0.3", "@tiptap/extension-underline": "^2.0.3", "@tiptap/pm": "^2.0.3", "@tiptap/react": "^2.0.3", "@tiptap/starter-kit": "^2.0.3", "@tiptap/suggestion": "^2.0.3", "antd": "^5.4.7", "axios": "^1.2.1", "babel-loader": "^9.1.3", "dayjs": "^1.11.7", "dompurify": "^3.0.6", "emoji-mart": "^5.3.3", "emoji-regex": "^10.2.1", "framer-motion": "^4.1.17", "highcharts": "^11.4.8", "highcharts-react-official": "^3.2.1", "html-react-parser": "^5.1.7", "html2canvas": "^1.4.1", "i18next": "^22.4.9", "i18next-browser-languagedetector": "^8.1.0", "immutability-helper": "^3.1.1", "jquery": "^3.6.3", "jspdf": "^2.5.1", "less": "^4.1.0", "lodash": "^4.17.20", "lowlight": "^3.1.0", "lucide-react": "^0.469.0", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "node-html-parser": "^6.1.13", "party-js": "^2.2.0", "plotly.js": "^2.30.1", "prop-types": "^15.7.2", "prosemirror-tables": "^1.3.7", "quill": "^1.3.7", "quill-blot-formatter": "^1.0.5", "quill-image-resize-module-react": "^3.0.0", "quill-image-uploader": "^1.3.0", "quill-mention": "^3.0.4", "quill-table": "^1.0.0", "rc-virtual-list": "^3.11.4", "react": "^17.0.2", "react-beautiful-dnd": "^13.1.1", "react-color": "^2.19.3", "react-countup": "^6.4.2", "react-dom": "^17.0.2", "react-draggable": "^4.4.5", "react-highlight-words": "^0.18.0", "react-i18next": "^12.1.4", "react-icons": "^4.7.1", "react-infinite-scroll-component": "^6.1.0", "react-intersection-observer": "^9.5.2", "react-lazy-load-image-component": "^1.5.5", "react-mic": "^12.4.6", "react-pivottable": "^0.11.0", "react-plotly.js": "^2.6.0", "react-quill": "^1.3.5", "react-quill-emoji": "^0.1.9", "react-redux": "^8.0.5", "react-rnd": "^10.5.2", "react-router-dom": "6.6.1", "react-scripts": "5.0.1", "redux": "^4.2.0", "redux-persist": "^6.0.0", "redux-thunk": "^2.4.2", "rehype-parse": "^9.0.0", "rehype-remark": "^10.0.0", "remark": "^15.0.1", "remark-html": "^16.0.1", "remark-stringify": "^11.0.0", "scroll-into-view-if-needed": "^3.0.10", "sip.js": "^0.7.8", "tailwind-scrollbar": "^1.3.2", "tippy.js": "^6.2.5", "tiptap-extension-resize-image": "^1.0.3", "url-regex": "^5.0.0", "wavesurfer.js": "^7.7.11", "web-vitals": "^2.1.4", "webpack": "^5.90.2", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.0.2"}, "scripts": {"start": "react-scripts --max_old_space_size=4096 start", "build": "CI=false react-scripts --max_old_space_size=8192 build && compress-cra --max_old_space_size=8192"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/plugin-transform-private-property-in-object": "^7.22.11", "@tailwindcss/forms": "^0.5.3", "autoprefixer": "^10.4.13", "compress-create-react-app": "^1.3.1", "patch-package": "^8.0.0", "postcss": "^8.4.20", "postinstall-postinstall": "^2.1.0", "prettier": "^2.8.8", "prettier-plugin-tailwindcss": "^0.3.0", "tailwindcss": "^3.2.4"}}