import { FilterOutlined, ReloadOutlined } from "@ant-design/icons";
import { <PERSON>ge, Button, Input, Tooltip } from "antd";
import { setFilterEmail, setSearchEmail } from "new-redux/actions/mail.actions";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { FiSearch } from "react-icons/fi";

import { useDispatch } from "react-redux";
import PopoverNotification from "./popoverNotification";
import useDebounce from "pages/components/UseDebounce/UseDebounce";
import KPI from "./KPI";
import { useLocation, useParams } from "react-router-dom";
import { useSelector } from "react-redux";

const Header = ({ dataAccounts, setRefresh }) => {
  const [t] = useTranslation("common");
  const [popoverVisible, setPopoverVisible] = useState(false);

  const usedAccount = dataAccounts?.find((item) => item.selected === true);
  const dispatch = useDispatch();

  const { filterEmailActive, searchEmail } = useSelector(
    (state) => state.mailReducer
  );
  const [localSearchEmail, setLocalSearchEmail] = useState(searchEmail);
  const debouncedSearchEmail = useDebounce(localSearchEmail, 500);
  const location = useLocation();
  const { accountId } = useParams();

  useEffect(() => {
    setLocalSearchEmail(searchEmail);
  }, [searchEmail]);

  useEffect(() => {
    dispatch(setSearchEmail(debouncedSearchEmail));
    if (searchEmail === "") {
      handleChange("");
    }
  }, [debouncedSearchEmail, dispatch]);

  const handleChange = (e) => {
    setLocalSearchEmail(e);
  };

  return (
    <div>
      <div className=" h-[5rem] w-full items-center justify-between pl-4 pr-4 lg:flex lg:h-[3rem]">
        <div className="flex items-center space-x-3 text-gray-600">
          <Tooltip placement="top" title="Refresh">
            <Button
              shape="circle"
              type="text"
              icon={<ReloadOutlined style={{ color: "#4c4747" }} />}
              onClick={() => setRefresh((p) => !p)}
            />
          </Tooltip>
          <KPI dataAccounts={dataAccounts} />
        </div>
        <div className="flex items-center space-x-3 text-gray-600">
          <Input
            style={{ width: "180px", height: "30px" }}
            allowClear
            placeholder={t("mailing.search")}
            value={localSearchEmail}
            onChange={(e) => handleChange(e.target.value)}
            prefix={<FiSearch className="h-4 w-4 text-slate-400" />}
          />
          {/*Show filter and notification if folder is inbox */}
          {location.pathname === `/mailing/${accountId}/inbox` ? (
            <div className="mr-8 mt-[6px] flex  items-center lg:mt-[0px] ">
              {usedAccount?.shared == 1 ? (
                <>
                  <Tooltip placement="top" title={t("mailing.filter")}>
                    <Button
                      icon={
                        <Badge dot={filterEmailActive}>
                          <FilterOutlined style={{ fontSize: "17px" }} />{" "}
                        </Badge>
                      }
                      type="link"
                      onClick={() => dispatch(setFilterEmail(true))}
                    />
                  </Tooltip>
                  <PopoverNotification
                    popoverVisible={popoverVisible}
                    setPopoverVisible={setPopoverVisible}
                    usedAccount={usedAccount}
                  />
                </>
              ) : null}
            </div>
          ) : null}
        </div>
      </div>
    </div>
  );
};

export default Header;
