import { useDispatch, useSelector } from "react-redux";
import { Button, Space, Tag, Tooltip, Typography, Image, Rate } from "antd";
import { FiCopy, FiPhoneForwarded } from "react-icons/fi";
import {
  extractPhoneNum,
  formatDateComparisonTableFamily,
  humanDate,
  truncateFileName,
} from "pages/voip/helpers/helpersFunc";
import { colors } from "components/Colors";
import { CustomTag, RenderAlbum, renderIcon } from "./RenderColumnsTable";
import { URL_ENV } from "index";
import { SendHorizontal } from "lucide-react";
import { setEmailFields } from "pages/rmc/mailing/main-components/email-composer-modal/utils";
import DisplayAvatar from "pages/voip/components/DisplayAvatar";

const textStyle = "text-slate-800";

const RenderDescriptionDetails = ({
  type,
  value,
  t,
  call,
  isProfile = false,
}) => {
  //
  const dispatch = useDispatch();
  const dataAccounts = useSelector(state=> state.mailReducer.dataAccounts)
  //
  if (!value) return null;
  //
  //
  switch (type) {
    case "phone":
      if (!value?.length || !value?.[1]) return null;
      const numberToDisplay = extractPhoneNum(value);
      const number = numberToDisplay
        ?.join()
        ?.replaceAll(",", "")
        ?.replace("+", "00");
      return (
        <div className="flex items-center space-x-2">
          <span className={textStyle}>
            {numberToDisplay?.length === 2
              ? `(${numberToDisplay[0]}) ${numberToDisplay[1]}`
              : numberToDisplay}
          </span>
          <Space size="small">
            {copyIcon(number)}
            <Tooltip title={t("voip.call")}>
              <Button
                onClick={() => call(number)}
                icon={<FiPhoneForwarded style={{ fontSize: "15px" }} />}
                type="link"
                size="small"
              />
            </Tooltip>
          </Space>
        </div>
      );
    case "email":
      return (
        <Space>
          <span className={textStyle}>{value}</span>
          <div className="flex space-x-1">
            {copyIcon(value)}
           {!isProfile && dataAccounts?.length && <Tooltip title={t("contacts.sendEmail")}>
              <Button
              size="small"
              type="link"
              icon={<SendHorizontal size={16} strokeWidth={1.5} />}
              onClick={() =>
                dispatch(setEmailFields({ sender: null, receivers: [value] }))
              }
            />
            </Tooltip>}
          </div>
        </Space>
      );
    case "extension":
      return (
        <Space>
          <span className={textStyle}>{value}</span>
          {!isProfile && (
            <Tooltip title={t("voip.call")}>
              <Button
                onClick={() => call(value, null, 4)}
                icon={<FiPhoneForwarded style={{ fontSize: "15px" }} />}
                type="link"
                size="small"
              />
            </Tooltip>
          )}
        </Space>
      );
    case "color":
      const findColor = colors.find((c) => c.value === value) ?? value;
      return (
        <Tag
          style={{ paddingLeft: 2, paddingRight: 2 }}
          bordered={false}
          color={findColor?.tagColor || findColor}
        >
          {findColor?.label ? t(`colors.${findColor?.label}`) : findColor}
        </Tag>
      );
    case "link":
      return (
        // <div className="group flex flex-row items-center space-x-1">
        <Tooltip title={t("contacts.openInNewTab")} placement="top">
          <a
            className={` truncate `}
            href={`${value}`}
            target="_blank"
            rel="noreferrer"
          >
            {value}
            {/* https://gitcmk.comunikcrm.info/front/sphere/-/commits/devhttps://gitcmk.comunikcrm.info/front/sphere/-/commits/dev */}
          </a>
        </Tooltip>
      );
    case "monetary":
      return (
        !!(value?.length && value?.[1]) && (
          <span className={textStyle}>
            {`(${value[0]}) ${Number(value[1])
              .toFixed(3)
              .replace(/\d(?=(\d{3})+\.)/g, "$&,")}`}
          </span>
        )
      );
    case "date_time":
      return <span className={textStyle}>{humanDate(value, t, "table")}</span>;
    case "date":
      return (
        <span className={textStyle}>
          {formatDateComparisonTableFamily(value)}
        </span>
      );
    case "range":
      return (
        !!value?.length && (
          <span className={textStyle}>{`${formatDateComparisonTableFamily(
            value[0]
          )} -> ${formatDateComparisonTableFamily(value[1])}`}</span>
        )
      );
    case "ip address":
      return (
        <div className="group flex flex-row  items-center justify-between">
          <span className={`${textStyle}`}>{value}</span>

          {copyIcon(value)}
        </div>
      );
    case "image":
      return (
        <Image
          height={35}
          src={`${
            URL_ENV?.REACT_APP_BASE_URL + URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
          }${value.path}`}
        />
      );
    case "album":
      return <RenderAlbum values={value} size={35} />;
    case "file":
      const RenderFile = ({ file, t }) => {
        const baseUrl =
          URL_ENV?.REACT_APP_BASE_URL + URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL;
        const firstFileName = file?.file_name || file;
        const firstFileUrl = `${baseUrl}${file?.path || file}`;
        return (
          <Tooltip title={t("contacts.openInNewTab")} placement="top">
            <a
              className={`${textStyle}`}
              href={firstFileUrl}
              target="_blank"
              rel="noreferrer"
            >
              {truncateFileName(firstFileName, 40)}
            </a>
          </Tooltip>
        );
      };
      return (
        <Space direction="vertical" size="small">
          {value.map((file, i) => (
            <RenderFile key={i} file={file} t={t} />
          ))}
        </Space>
      );
    case "country":
      const language = localStorage.getItem("language") === "fr" ? "fr" : "en";
      const countriesList = [
        ...(Array.isArray(value) && value.length > 0 ? value : [value]),
      ];
      const CountryTagContent = ({ flag, name }) => (
        <div className="flex items-center space-x-1">
          <span className="text-base">{flag}</span>
          <span className={textStyle}>{name}</span>
        </div>
      );
      return (
        <Space direction="vertical" size="small">
          {countriesList?.map((country, i) => (
            <CustomTag
              key={i}
              isCountry={true}
              maxChars={30}
              content={
                <CountryTagContent
                  flag={country?.flag}
                  name={language === "fr" ? country?.name_fr : country?.name_en}
                />
              }
            />
          ))}
        </Space>
      );

    case "rate":
      return <Rate disabled allowHalf defaultValue={Number(value)} />;

    case "radio":
    case "select":
      const isFamily = !!value?.family_id;

      return (
        <CustomTag
          content={value?.label || value}
          maxChars={35}
          icon={value?.icon}
          color={value?.color}
          avatar={
            isFamily && (
              <DisplayAvatar
                name={value?.label}
                size={22}
                icon={renderIcon(value?.family_id, 16)}
                urlImg={
                  !!value?.avatar &&
                  URL_ENV?.REACT_APP_BASE_URL +
                    URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                    value?.avatar
                }
              />
            )
          }
        />
      );

    case "checkbox":
    case "multiselect":
      const values = value.length ? value : [];
      return (
        <Space size="small" direction="vertical">
          {values?.map((value, i) => (
            <CustomTag
              key={i}
              content={value?.label || value}
              maxChars={35}
              icon={value?.icon}
              color={value?.color}
              avatar={
                !!value?.family_id && (
                  <DisplayAvatar
                    name={value?.label}
                    size={22}
                    icon={renderIcon(value?.family_id, 16)}
                    urlImg={
                      !!value?.avatar &&
                      URL_ENV?.REACT_APP_BASE_URL +
                        URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                        value?.avatar
                    }
                  />
                )
              }
            />
          ))}
        </Space>
      );
    case "autocomplete":
      const valuesList = [
        ...(Array.isArray(value) && value.length > 0 ? value : [value]),
      ];
      return (
        <Space size="small" direction="vertical">
          {valuesList?.map((value, i) => (
            <CustomTag
              key={i}
              content={value?.label || value}
              maxChars={35}
              icon={value?.icon}
              color={value?.color}
              avatar={
                !!value?.family_id && (
                  <DisplayAvatar
                    name={value?.label}
                    size={22}
                    icon={renderIcon(value?.family_id, 16)}
                    urlImg={
                      !!value?.avatar &&
                      URL_ENV?.REACT_APP_BASE_URL +
                        URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL +
                        value?.avatar
                    }
                  />
                )
              }
            />
          ))}
        </Space>
      );
    default:
      // return <span className={`${textStyle} truncate`}>{value}</span>;
      return (
        <Typography.Paragraph
          style={{ color: "rgb(30, 41, 59)" }}
          ellipsis={{
            rows: 2,
            expandable: true,
            defaultExpanded: false,
            symbol: <span className="capitalize">{t("contacts.more")}</span>,
            // onExpand: (_, info) => setExpanded(info.expanded),
          }}
        >
          {value}
        </Typography.Paragraph>
      );
  }
};
//
export const copyIcon = (text) => (
  <Typography.Paragraph
    copyable={{
      text: text,
      icon: [
        <FiCopy
          style={{
            color: "rgb(22, 119, 255)",
            marginTop: 4,
            fontSize: 15,
          }}
        />,
      ],
    }}
  />
);
//

export default RenderDescriptionDetails;
