import { GET_VISIO_SUCCESS, GET_VISIO_ERROR, IS_LOADING_VISIO } from '../../constants'
import MainService from '../../../services/main.service'
import { toastNotification } from '../../../components/ToastNotification';

export const getVisio = (nbrPage) => async (dispatch) => {
    try {
        dispatch({ type: IS_LOADING_VISIO });
        const response = await MainService.getVisioTasks(nbrPage)
        dispatch({
            type: GET_VISIO_SUCCESS,
            payload: response?.data,
        })
    } catch (error) {
        dispatch({
            type: GET_VISIO_ERROR,
            payload: error,
        })
        toastNotification(
            "error",
            // "Something went wrong, please try again!",
            "You don't have the power to accomplish this action!",
            "topRight"
        );
    }
}
