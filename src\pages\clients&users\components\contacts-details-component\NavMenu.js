import { useState } from "react";
import { Button, Menu } from "antd";
import {
  AlignLeftOutlined,
  ContainerOutlined,
  IdcardOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  ProfileOutlined,
  TagsOutlined,
} from "@ant-design/icons";
import { HeartHandshake } from "lucide-react";

const NavMenu = ({ setSelectedMenuItem }) => {
  const [collapsed, setCollapsed] = useState(false);

  const getItem = (label, key, icon, children, type) => {
    return {
      label,
      key,
      icon,
      children,
      type,
    };
  };

  const navIconStyle = { fontSize: `${collapsed ? "20px" : "17px"}` };

  const menuItems = [
    getItem(
      "Activities",
      "activities",
      <AlignLeftOutlined style={navIconStyle} />
    ),
    getItem(
      "Profile Details",
      "profile_details",
      <IdcardOutlined style={navIconStyle} />
      // subProfileDetailsKeys
    ),
    getItem(
      "Deals",
      "deals",
      <HeartHandshake size={`${collapsed ? 22 : 20}`} />
    ),
    getItem("Tickets", "tickets", <TagsOutlined style={navIconStyle} />),
    getItem(
      "Documents",
      "documents",
      <ContainerOutlined style={navIconStyle} />
    ),
    getItem(
      "Reportings",
      "reportings",
      <ProfileOutlined style={navIconStyle} />
    ),
  ];

  return (
    <div style={{ maxWidth: "20%" }}>
      <Button
        type="primary"
        onClick={() => setCollapsed(!collapsed)}
        style={{
          marginBottom: 16,
        }}
      >
        {collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
      </Button>
      <Menu
        items={menuItems}
        onSelect={({ key, keyPath, selectedKeys }) => setSelectedMenuItem(key)}
        defaultSelectedKeys={["profile_details"]}
        inlineCollapsed={collapsed}
        mode="inline"
        style={{ backgroundColor: "rgb(248 250 252)", padding: "1rem" }}
      />
    </div>
  );
};

export default NavMenu;
