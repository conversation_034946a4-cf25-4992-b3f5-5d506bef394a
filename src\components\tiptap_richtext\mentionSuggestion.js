import { <PERSON>act<PERSON><PERSON><PERSON> } from "@tiptap/react";
import tippy from "tippy.js";
import "tippy.js/dist/tippy.css";

import { MentionList } from "./MentionList.js";
import { store } from "../../new-redux/store.js";
import { setMentionState } from "../../new-redux/actions/chat.actions/Input.js";
import { getName } from "../../pages/layouts/chat/utils/ConversationUtils.js";
import i18next from "i18next";
const getParticipantsList = async (query) => {
  const selectedConversation = await store.getState().ChatRealTime.selectedConversation;
  const participantsChat = (await store.getState().chat.selectedParticipants) || [];
  //const participantsTask = [...(selectedConversation?.participants || [])];

  //  const selectedParticipants =
  //  urlChatComponent.includes(
  //   window?.location?.pathname
  // )
  //   ? participantsChat
  //   : participantsTask;

  let filteredNamesList = [...participantsChat];

  if (selectedConversation?.type === "room")
    filteredNamesList = [
      ...participantsChat,
      {
        name: i18next.language === "en" ? "ALL" : "TOUS",
        _id: 0,
        image: selectedConversation?.image,
        type: "room",
      },
    ].filter(
      (item) =>
        (item?.status === 1 && item?.name?.toLowerCase()?.includes(query?.toLowerCase()?.trim())) ||
        (("all".includes(query.toLowerCase().trim()) ||
          "tous".includes(query.toLowerCase().trim())) &&
          item?._id === 0)
    );
  return filteredNamesList.map((user) => {
    return { ...user, name: getName(user?.name, "name") };
  });
};

export default {
  items: ({ query }) => {
    return new Promise((resolve) => {
      const time = setTimeout(async () => {
        resolve(await getParticipantsList(query));
        clearTimeout(time);
      }, 0);
    });
  },

  render: () => {
    let component;
    let popup;

    return {
      onStart: (props) => {
        store.dispatch(setMentionState(true));
        component = new ReactRenderer(MentionList, {
          props,
          editor: props.editor,
        });

        if (!props.clientRect) {
          return;
        }

        popup = tippy("body", {
          getReferenceClientRect: props.clientRect,
          appendTo: () => document.body,
          content: component.element,
          showOnCreate: true,
          interactive: true,
          trigger: "manual",
          placement: "bottom-start",
        });
      },

      onUpdate(props) {
        component.updateProps(props);

        if (!props.clientRect) {
          return;
        }

        popup[0].setProps({
          getReferenceClientRect: props.clientRect,
        });
      },

      onKeyDown(props) {
        if (props.event.key === "Escape") {
          popup[0].hide();

          return true;
        }

        return component.ref?.onKeyDown(props);
      },

      onExit() {
        popup[0].destroy();
        component.destroy();
        const time = setTimeout(() => {
          popup[0]?.state?.isDestroyed === true && store.dispatch(setMentionState(false));

          clearTimeout(time);
        }, 1);
      },
    };
  },
};
