import {
  ADD_FILE360,
  REMOVE_FILE360,
  UPDATE_FILE360,
  SET_FILES360,
  ADD_FILES360_TO_LIST,
  MODIFY_FILE360_LABEL,
  MODIFY_FILE360_NAME,
  SEARCH_FILE360,
  RESET_SEARCH_FILE360,
  SET_CURRENT_SELECTED_CONTACT,
  UNSET_CURRENT_SELECTED_CONTACT,
  UPDATE_FILE_FOR_MERCURE,
  DELETE_FILE_FOR_MERCURE,
  UPDATE_FILE_LABEL_FOR_MERCURE
} from "../../constants";

export const addFile360 = (payload) => ({
  type: ADD_FILE360,
  payload: payload,
});

export const removeFile360 = (payload) => ({
  type: REMOVE_FILE360,
  payload: payload,
});

export const updateFile360 = (payload) => ({
  type: UPDATE_FILE360,
  payload: payload,
});

export const setFiles360 = (payload) => ({
  type: SET_FILES360,
  payload: payload,
});

export const addFiles360ToList = (payload) => ({
  type: ADD_FILES360_TO_LIST,
  payload: payload,
});

export const modifyFile360Label = (payload) => ({
  type: MODIFY_FILE360_LABEL,
  payload: payload,
});

export const modifyFile360Name = (payload) => ({
  type: MODIFY_FILE360_NAME,
  payload: payload,
});

export const searchFile360 = (payload) => ({
  type: SEARCH_FILE360,
  payload: payload,
});

export const resetSearchFile360 = () => ({
  type: RESET_SEARCH_FILE360,
});

export const setCurrentSelectedContact = (payload) => ({
  type: SET_CURRENT_SELECTED_CONTACT,
  payload: payload,
});

export const unsetCurrentSelectedContact = () => ({
  type: UNSET_CURRENT_SELECTED_CONTACT,
});

export const updateFileForMercure = (payload) => ({
  type: UPDATE_FILE_FOR_MERCURE,
  payload: payload,
});

export const deleteFileForMercure = (payload) => ({
  type: DELETE_FILE_FOR_MERCURE,
  payload: payload,
});

export const updateFileLabelForMercure = (payload) => ({
  type: UPDATE_FILE_LABEL_FOR_MERCURE,
  payload: payload,
});
