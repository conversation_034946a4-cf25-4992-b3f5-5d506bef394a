import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Checkbox,
  Popover,
  Space,
  Typography,
} from "antd";
import { FilterTwoTone } from "@ant-design/icons";

const FilterCallLogs = ({
  filterState,
  setFilterState,
  t,
  disabled,
  headerParamsFilterCalls,
}) => {
  //

  //
  const [selectedReceipt, setSelectedReceipt] = useState([]);
  const [selectedIssued, setSelectedIssued] = useState([]);
  const [openPopover, setOpenPopover] = useState(false);

  //
  const handleApplyFilter = () => {
    setFilterState([...selectedIssued, ...selectedReceipt]);
    setOpenPopover(false);
  };

  useEffect(() => {
    if (!filterState.length) {
      setSelectedIssued([]);
      setSelectedReceipt([]);
    }
  }, [filterState]);
  //
  useEffect(() => {
    if (headerParamsFilterCalls) {
      setSelectedReceipt(headerParamsFilterCalls);
    }
  }, [headerParamsFilterCalls]);
  //
  const checkBoxStyle = {
    display: "flex",
    flexDirection: "column",
    gap: ".5rem",
    marginLeft: "5px",
  };
  //
  const handleReset = () => {
    setSelectedIssued([]);
    setSelectedReceipt([]);
    setFilterState([]);
  };
  //
  const handleParentCheck = (event, parent) => {
    const check = event.target.checked;
    if (parent === "receipt") {
      if (check) {
        setSelectedReceipt([
          "incoming_missed",
          "incoming_failed",
          "incoming_received",
        ]);
        return;
      } else {
        setSelectedReceipt([]);
        return;
      }
    }
    if (parent === "issued") {
      if (check) {
        setSelectedIssued([
          "outgoing_missed",
          "outgoing_failed",
          "outgoing_received",
        ]);
        return;
      } else {
        setSelectedIssued([]);
        return;
      }
    }
  };
  //
  const ReceiptContent = () => (
    <Card
      size="small"
      type="inner"
      bodyStyle={{ backgroundColor: "white", padding: "0.5rem" }}
      title={
        <div className="flex flex-row  space-x-2">
          <Checkbox
            indeterminate={
              selectedReceipt.length >= 1 && selectedReceipt?.length <= 2
            }
            checked={selectedReceipt?.length === 3}
            onChange={(event) => handleParentCheck(event, "receipt")}
          />
          <span>{t("voip.receipts")}</span>
        </div>
      }
    >
      <Checkbox.Group
        style={checkBoxStyle}
        value={selectedReceipt}
        onChange={(check) => setSelectedReceipt(check)}
      >
        <Checkbox key={1} value="incoming_missed">
          <Typography.Text>{t("voip.missed")}</Typography.Text>
        </Checkbox>
        <Checkbox key={2} value="incoming_failed">
          <Typography.Text>{t("voip.failed")}</Typography.Text>
        </Checkbox>
        <Checkbox key={3} value="incoming_received">
          <Typography.Text>{t("voip.answered")}</Typography.Text>
        </Checkbox>
      </Checkbox.Group>
    </Card>
  );

  const IssuedContent = () => (
    <Card
      size="small"
      type="inner"
      bodyStyle={{ backgroundColor: "white", padding: ".5rem" }}
      title={
        <div className="flex flex-row space-x-2">
          <Checkbox
            indeterminate={
              selectedIssued.length >= 1 && selectedIssued?.length <= 2
            }
            checked={selectedIssued?.length === 3}
            onChange={(event) => handleParentCheck(event, "issued")}
          />
          <span>{t("voip.issued")}</span>
        </div>
      }
    >
      <Checkbox.Group
        style={checkBoxStyle}
        value={selectedIssued}
        onChange={(check) => setSelectedIssued(check)}
      >
        <Checkbox key={1} value="outgoing_missed">
          <Typography.Text>{t("voip.outgoing_missed")}</Typography.Text>
        </Checkbox>
        <Checkbox key={2} value="outgoing_failed">
          <Typography.Text>{t("voip.failed")}</Typography.Text>
        </Checkbox>
        <Checkbox key={3} value="outgoing_received">
          <Typography.Text>{t("voip.answered")}</Typography.Text>
        </Checkbox>
      </Checkbox.Group>
    </Card>
  );
  //
  const filterContent = (
    <div className="flex flex-col space-y-4">
      <Space>
        <ReceiptContent />
        <IssuedContent />
      </Space>
      <div className="flex flex-row-reverse space-x-2">
        <Space>
          <Button
            size="small"
            type="link"
            disabled={!selectedReceipt.length && !selectedIssued.length}
            onClick={handleReset}
          >
            {t("tags.reset")}
          </Button>
          <Button
            type="primary"
            size="small"
            disabled={!selectedIssued?.length && !selectedReceipt?.length}
            onClick={handleApplyFilter}
          >
            {t("voip.apply")}
          </Button>
        </Space>
      </div>
    </div>
  );
  //
  return (
    <Popover
      trigger={["click"]}
      placement="bottomRight"
      title={t("voip.filterCallByType")}
      content={filterContent}
      open={openPopover}
      onOpenChange={(newOpen) => setOpenPopover(newOpen)}
    >
      <Button
        disabled={disabled}
        type="text"
        shape="circle"
        icon={
          <Badge dot={filterState?.length}>
            <FilterTwoTone />
          </Badge>
        }
        //   onClick={() => setOpenPopover(true)}
      />
    </Popover>
  );
};

export default FilterCallLogs;
