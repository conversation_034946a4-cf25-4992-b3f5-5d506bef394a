import { memo, useMemo } from "react";
import { DownOutlined } from "@ant-design/icons";
import { Checkbox, Dropdown, Pagination } from "antd";
import { setOpenTaskRoomDrawer } from "new-redux/actions/tasks.actions/handleTaskRoomDrawer";
import { handlePageSizeOptions } from "pages/voip/helpers/helpersFunc";

export const scrollToTop = (scrollY = 0) => {
  const tableBody = document.querySelector(".ant-table-body");
  if (tableBody) {
    tableBody.scrollTop = scrollY;
  }
};
//
export const handleDataTable = (rows = [], isSearch = false) => {
  if (!Array.isArray(rows)) return [];

  return rows.map((item) => {
    const {
      id,
      from_Thread = [],
      from: singleFrom = {},
      identification: topIdentification = {},
    } = item;

    const hasThread = Array.isArray(from_Thread) && from_Thread.length > 0;

    const from =
      !isSearch && hasThread
        ? from_Thread
        : [
            {
              ...singleFrom,
              identification: topIdentification?.id ? topIdentification : {},
            },
          ];

    const identification = topIdentification?.label_data
      ? topIdentification
      : hasThread && from_Thread[0]?.identification?.label_data
      ? from_Thread[0].identification
      : [];

    return {
      ...item,
      key: id,
      from,
      identification,
    };
  });
};
//
export const MailPagination = memo(
  ({ total, page, setPage, limit, setLimit }) =>
    !total ? null : total <= 20 ? (
      <span className="p-2 text-slate-700">{`${1}-${total} of ${total}`}</span>
    ) : (
      <Pagination
        size="small"
        showSizeChanger
        current={page}
        total={total}
        pageSize={total >= limit ? limit : total}
        pageSizeOptions={handlePageSizeOptions(total, 100)}
        showTotal={(total, range) => (
          <span className="text-slate-700">{`${range[0]}-${range[1]} of ${total}`}</span>
        )}
        onShowSizeChange={(current, size) => setLimit(size)}
        onChange={(page) => setPage(page)}
      />
    )
);
//
export const SelectRows = memo(
  ({
    selectedRowKeys,
    setSelectedRowKeys,
    dataSource,
    usedAccount,
    user,
    disabled,
  }) => {
    //

    //
    const selectableRowKeys = useMemo(
      () =>
        dataSource
          ?.filter((record) => conditionActions(record, usedAccount, user))
          ?.map((record) => record.key),
      [dataSource, usedAccount, user]
    );
    //
    const onCheckAllChange = (e) => {
      setSelectedRowKeys(e.target.checked ? selectableRowKeys : []);
    };
    //
    const allSelected =
      selectableRowKeys.length > 0 &&
      selectableRowKeys.every((key) => selectedRowKeys.includes(key));
    //
    const handleMenuClick = (e) => {
      switch (e.key) {
        case "1":
          const readRowKeys = dataSource
            .filter(
              (record) =>
                conditionActions(record, usedAccount, user) && record.seen === 1
            )
            .map((record) => record.key);
          setSelectedRowKeys(readRowKeys);
          break;
        case "2":
          const unreadRowKeys = dataSource
            .filter(
              (record) =>
                conditionActions(record, usedAccount, user) && record.seen === 0
            )
            .map((record) => record.key);
          setSelectedRowKeys(unreadRowKeys);
          break;
        case "3":
          const allRowKeys = dataSource
            .filter((record) => conditionActions(record, usedAccount, user))
            .map((record) => record.key);
          setSelectedRowKeys(allRowKeys);
          break;
        case "4":
          setSelectedRowKeys([]);
          break;

        default:
          break;
      }
    };
    //
    return (
      <Dropdown.Button
        type="text"
        size="small"
        style={{ width: "4rem", marginLeft: -12 }}
        trigger={["click"]}
        placement="bottom"
        icon={<DownOutlined />}
        menu={{
          onClick: handleMenuClick,
          items: [
            { label: "Tous", key: "3" },
            { label: "Aucun", key: "4" },
            { label: "Lus", key: "1" },
            { label: "Non lus", key: "2" },
          ],
        }}
        disabled={disabled}
      >
        <Checkbox
          onChange={onCheckAllChange}
          checked={allSelected}
          indeterminate={selectedRowKeys.length > 0 && !allSelected}
        />
      </Dropdown.Button>
    );
  }
);
//
// D'apres le Ouk (3ou9, Aymen)
export const conditionActions = (record, usedAccount, user) => {
  let condition = false;
  if (
    record?.owner?.owner &&
    record?.transfert &&
    record?.owner?.owner === user?.id &&
    record?.transfert?.account_id === String(usedAccount?.value)
  ) {
    condition = true;
  } else if (
    !record?.owner?.owner &&
    record?.transfert &&
    record?.transfert?.account_id === String(usedAccount?.value)
  ) {
    condition = true;
  } else if (
    record?.owner?.owner &&
    !record?.transfert &&
    record?.owner?.owner === user?.id &&
    record?.owner?.user_id === user?.id
  ) {
    condition = true;
  } else if (!record?.owner?.owner && !record?.transfert) {
    condition = true;
  } else if (
    record?.owner?.owner &&
    !record?.transfert &&
    record?.owner?.owner === user?.id &&
    record?.owner?.user_id !== user?.id
  ) {
    return true;
  } else {
    // verifier
    condition = false;
  }
  return condition;
};
//
export const handleFormData = (values, formData) => {
  Object.entries(values).forEach(([key, value]) => {
    if (key === "affectation") {
      formData.append(
        "element_id_affectation_filter[]",
        value.split("//")?.pop()
      );
    } else if (key === "date") {
      formData.append("start_date", value[0]);
      formData.append("end_date", value[1]);
    } else if (Array.isArray(value) && value.length) {
      value.forEach((val) => formData.append(`${key}[]`, val));
    } else formData.append(`${key}`, value);
  });
};
//
export const openMailingRoomChat = ({
  usedAccount,
  user,
  thirdId,
  dispatch,
  setOpenRoomChat,
}) => {
  const formData = new FormData();
  formData.append("account_id", usedAccount.value);
  formData.append("creator_id", user.id);
  formData.append("thread_id", thirdId);
  usedAccount.departmentId.forEach((dep) =>
    formData.append("department_ids[]", dep)
  );
  setOpenRoomChat({ open: true, elementId: thirdId, formData });
  dispatch(setOpenTaskRoomDrawer(true));
};
//
