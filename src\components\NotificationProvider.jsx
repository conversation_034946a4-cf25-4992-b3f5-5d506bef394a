import { useEffect } from "react";
import { notification } from "antd";
import { __setToastApi__, throttledFire } from "./ToastNotification";

export function NotificationProvider({ children }) {
  // hook-based notification API + context holder
  const [api, contextHolder] = notification.useNotification();

  useEffect(() => {
    // configure *inside* your React tree so context (theme, rtl, etc.) works
    notification.config({
      maxCount: 1,
      getContainer: () => document.body,
      motion: false, // AntD 5: kill all animations
    });

    // give our module the hook API
    __setToastApi__(api);

    return () => {
      // on unmount, clear out the API and cancel any pending toasts
      __setToastApi__(null);
      throttledFire.cancel();
    };
  }, [api]);

  return (
    <>
      {contextHolder}
      {children}
    </>
  );
}
