import PhoneCpt, { decryptC } from "./Webphone/PhoneCpt";
import { useSelector } from "react-redux";
import { useEffect } from "react";
import { useDispatch } from "react-redux";
import { getParamIPBX } from "../../new-redux/actions/voip.actions/getParam";
import { URL_ENV } from "index";
import { Loader } from "components/Chat";
import useVoipRegister from "./Webphone/useVoipRegister";
import { toastNotification } from "components/ToastNotification";

// number of zIndex depend on visio conf =>
// - default : 1005 , 1006 for  small(30%)|meduim(70%) ,  1007(100%) for large
// - layout: 1010
export default function WebphoneContainer() {
  const param = useSelector((state) => state.voip.param);
  const { isOpen, size } = useSelector((state) => state.visio);
  const { user } = useSelector((state) => state.user);
  const dispatch = useDispatch();
  //
  useEffect(() => {
    if (user?.extension) {
      dispatch(getParamIPBX(user.extension));
      return;
    }
    const timer = setTimeout(() => {
      if (!user?.extension && !!user?.id) {
        toastNotification(
          "error",
          "Vous n'avez pas d'extension pour le webPhone ! Veuillez contacter l'administration.",
          "topRight",
          10
        );
        console.warn("[[WebPhone]] User extension not found!");
      }
    }, 5000);
    return () => clearTimeout(timer);
  }, [dispatch, user?.extension]);

  const { phone, status, stopReconnectTone } = useVoipRegister(param, user);

  // const sipInstance = useVoipRegister();
  // !!sipInstance &&
  //   console.log(
  //     { sipInstance },
  //     "isRegistered():",
  //     sipInstance.isRegistered(),
  //     "transport&&transport.isConnected():",
  //     sipInstance.transport &&
  //       typeof sipInstance.transport.isConnected === "function"
  //       ? sipInstance?.transport && sipInstance?.transport?.isConnected()
  //       : false
  //   );

  if (!param?.param_1 && user?.id) {
    console.warn("[[WebPhone]] ''param.param_1'' is missing or undefined.");
    return (
      <div className="fixed bottom-5 left-24">
        <Loader size={30} />
      </div>
    );
  } else if (
    !user?.extension ||
    decryptC(param?.param_5 || "") === "false" ||
    Object.values(URL_ENV).length === 0
  ) {
    console.warn(
      "[[WebPhone]] PhoneCpt not rendered due to missing extension or invalid parameters."
    );
    return <></>;
  } else
    return (
      <div
        className="webphone"
        style={{
          zIndex: isOpen && size !== "small" ? 1050 : 1029,
          position: "fixed",
          bottom: "0",
          left: "0",
          width: "25%",
        }}
      >
        <div className="relative w-[320px] bg-white">
          {user?.extension && (
            <PhoneCpt
              cmkPhone={phone}
              status={status}
              stopReconnectTone={stopReconnectTone}
            />
          )}
        </div>
      </div>
    );
}
