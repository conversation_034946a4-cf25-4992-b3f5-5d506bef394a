import { useEffect, useCallback, useState, useMemo, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getVoice } from "../../../../new-redux/actions/voip.actions/getVoice";
import { useTranslation } from "react-i18next";
import { RESET_VOICE_MESSAGING } from "../../../../new-redux/constants";
import {
  changeStateVoiceMsg,
  seenCallsOrVoicesOrGroups,
} from "../../../../pages/voip/services/services";
import {
  billSecToHumanReadable,
  formatDateComparisonWebPhone,
  humanDate,
} from "../../../../pages/voip/helpers/helpersFunc";
import { Badge, Button, Collapse, Divider, Empty, Input, Tooltip } from "antd";
import { MessageOutlined, PhoneOutlined } from "@ant-design/icons";
import useActionCall from "../../../../pages/voip/helpers/ActionCall";
import "../../../../pages/voip/index.css";
import { toastNotification } from "../../../ToastNotification";
import "./index.css";
import { FiSearch } from "react-icons/fi";
import { useLocation } from "react-router-dom";
import { debounce } from "lodash";
import { HighlightSearchW } from "../../../../pages/voip/components";
import DisplayAvatar from "../../../../pages/voip/components/DisplayAvatar";
import { URL_ENV } from "index";
import { loaderOptionTemplate } from "pages/layouts/webphone/call";

const RenderLabelItem = ({ name, image, number, state, search }) => {
  //
  return (
    <div className="flex flex-row space-x-2 ">
      <DisplayAvatar urlImg={image} name={name} size={38} />
      <div className="w-[11rem]">
        <div className="flex flex-row space-x-2">
          <p className="truncate font-semibold leading-5">
            {HighlightSearchW(name || number, search)}
          </p>
          {state === "Nouveau" && <Badge status="processing" />}
        </div>

        <p className={"leading-4 text-slate-500"}>
          {HighlightSearchW(number, search)}
        </p>
      </div>
    </div>
  );
};
//
const RenderExtraItem = ({ date, duration, t }) => (
  <div className=" text-slate-500">
    <Tooltip title={humanDate(date, t)} placement="topRight">
      <p className="cursor-help justify-self-end font-semibold  leading-5">
        {date?.split(" ")?.[1]?.substring(0, 5)}
      </p>
    </Tooltip>
    <p className={" justify-self-end leading-4 "}>{duration || "0 sec"}</p>
  </div>
);
//
const RenderChildItem = ({
  id,
  duration,
  state,
  date,
  audio,
  number,
  uuid,
  actionSeen,
  t,
  handleOpenMgsDrawer,
  call,
  info,
}) => {
  //
  const audioRef = useRef(null);
  const [isLoaded, setIsLoaded] = useState(false);
  //
  // Change the bg & color of the audio depend on it state
  document.documentElement.style.setProperty(
    "--color-audio-webPhone",
    // state === "Nouveau" ? "rgb(241 245 249)" : "rgb(248 250 252)"
    "white"
  );
  //
  const handlePlayClick = () => {
    if (!isLoaded) {
      audioRef.current.src = audio;
      setIsLoaded(true);
    }
    if (state === "Nouveau" && actionSeen) {
      actionSeen(id);
    }
  };
  useEffect(() => {
    handlePlayClick();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  //
  if (duration === "00:00:00" && state === "Nouveau" && actionSeen)
    actionSeen(id);
  //
  return (
    <div id={`panel-${id}`} key={id}>
      {/* <Divider orientation="left" plain style={{ margin: "0" }}>
        {humanDate(date, t)}
      </Divider> */}
      {duration !== "00:00:00" && (
        <div className="voicemail-webPhone ">
          <audio
            id={`audio-${id}`}
            ref={audioRef}
            className="audio-controls"
            style={{
              width: 300,
              height: 30,
              marginLeft: -15,
            }}
            controls={isLoaded}
            // onCanPlay={() => audioRef.current.play()}
          ></audio>
        </div>
      )}
      <div className="mr-2  flex flex-row space-x-1">
        <Tooltip title={!!uuid && t("voip.chat")}>
          <Button
            disabled={!uuid}
            onClick={() => handleOpenMgsDrawer(uuid)}
            size="small"
            type="link"
            // shape="circle"
            icon={<MessageOutlined style={{ fontSize: 14 }} />}
          />
        </Tooltip>
        <Tooltip title={t("voip.call")}>
          <Button
            onClick={() => call(number, info?.id, info?.familyId)}
            // disabled={props?.extension === poste}
            size="small"
            type="link"
            // shape="circle"
            icon={<PhoneOutlined rotate={100} style={{ fontSize: 14 }} />}
          />
        </Tooltip>
      </div>
    </div>
  );
};

function Voicecall({ handleOpenMgsDrawer, isOpenDrawerChat }) {
  const [t] = useTranslation("common");
  const call = useActionCall();
  const location = useLocation();
  const user = useSelector((state) => state.user.user);
  const posteVoip = user?.extension?.toString();
  const voiceTab = useSelector((state) => state.voip.voice);
  const { nbrVoiceMessaging } = useSelector(({ voip }) => voip);

  const dispatch = useDispatch();

  const [voiceMsg, setVoiceMsg] = useState([]);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [displaySearchText, setDisplaySearchText] = useState("");
  const [search, setSearch] = useState("");

  // console.log({ expandedKeys });

  const handleVoiceTab = useCallback(() => {
    if (!voiceTab.length) return;
    let voices =
      voiceTab?.map((voice) => ({
        id: voice?.id,
        uuid: voice.caller_uuid || null,
        info: {
          id: voice?.caller_id,
          familyId: voice?.caller_family_id,
        },
        name: voice?.caller_name?.replaceAll("_", " "),
        image:
          voice?.caller_image &&
          `${
            URL_ENV?.REACT_APP_BASE_URL +
            URL_ENV?.REACT_APP_SUFFIX_AVATAR_URL
          }${voice?.caller_image}`,
        number: `${voice?.caller_num}`,
        duration: billSecToHumanReadable(voice?.duration, "webPhoneVoiceCall"),
        date: voice?.origtime,
        audio: voice?.chemain_fichier_audio,
        state: voice?.etat,
      })) || [];

    if (search?.length) {
      voices = voices?.filter(
        (item) =>
          item?.name?.toLowerCase()?.includes(search) ||
          item?.number?.includes(search)
      );
    }

    setVoiceMsg(voices);
  }, [voiceTab, search]);

  useEffect(() => {
    handleVoiceTab();
  }, [handleVoiceTab]);

  const fetchVoice = useCallback(() => {
    dispatch(getVoice());
    // isLoading && setIsLoading(false);
  }, [dispatch]);

  useEffect(() => {
    fetchVoice();
  }, [fetchVoice]);
  //
  useEffect(() => {
    seenCallsOrVoicesOrGroups("voice_mail");
  }, []);
  //
  useEffect(() => {
    dispatch({ type: RESET_VOICE_MESSAGING });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [nbrVoiceMessaging]);
  //
  const playAudio = async (audio_id) => {
    try {
      const resp = await changeStateVoiceMsg(posteVoip, audio_id);
      if (resp?.status === 200) {
        setVoiceMsg((prev) =>
          prev?.map((voice) =>
            voice?.id === audio_id ? { ...voice, state: "Consulté" } : voice
          )
        );
        return;
      }
    } catch (err) {
      err?.response?.status !== 401 &&
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      console.error(err);
    }
  };
  //
  const onCollapseChange = (keys) => {
    const collapsedKey = expandedKeys.find((key) => !keys.includes(key));

    if (collapsedKey) {
      const audioElement = document.getElementById(`audio-${collapsedKey}`);
      if (audioElement) {
        audioElement.pause();
      }
    }
    //
    const expandedKey = keys.find((key) => !expandedKeys.includes(key));
    if (expandedKey) {
      // console.log("hello", { expandedKey });
      const timer = setTimeout(() => {
        // Find the panel by its key
        const element = document.querySelector(`#panel-${expandedKey}`);
        if (element) {
          element.scrollIntoView({ behavior: "smooth", block: "nearest" });
        }
        clearTimeout(timer);
      }, 300);
      // const audioElement = document.getElementById(`audio-${expandedKey}`);
      // if (audioElement) {
      //   audioElement.play();
      // }
    }

    setExpandedKeys(keys);
  };
  //
  const items = useMemo(() => {
    if (!voiceMsg?.length) return [];

    let previousDate = null;
    const formattedItems = [];

    voiceMsg.forEach((voice, i) => {
      const currentDate = voice.date.split(" ")[0];
      if (currentDate !== previousDate) {
        formattedItems.push({
          isDivider: true,
          dividerDate: formatDateComparisonWebPhone(voice.date, t),
        });
        previousDate = currentDate;
      }
      formattedItems.push({
        key: voice.id,
        label: <RenderLabelItem {...voice} search={search} />,
        children: (
          <RenderChildItem
            {...voice}
            actionSeen={playAudio}
            t={t}
            handleOpenMgsDrawer={handleOpenMgsDrawer}
            isOpenDrawerChat={isOpenDrawerChat}
            call={call}
            location={location}
          />
        ),
        extra: <RenderExtraItem {...voice} t={t} />,
        showArrow: false,
        style: {
          background: expandedKeys.includes(`${voice.id}`)
            ? "white"
            : voice.state === "Nouveau"
            ? "rgb(241 245 249)"
            : "rgb(248 250 252)",
          marginBottom: 0,
          padding: expandedKeys.includes(`${voice.id}`) ? "0 6px" : "0",
        },
      });
    });

    return formattedItems;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location, t, voiceMsg, expandedKeys]);

  //
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedSearch = useCallback(
    debounce((nextValue) => setSearch(nextValue), 300),
    []
  );

  const handleOnChange = (e) => {
    const inputValue = e?.target?.value;

    debouncedSearch(inputValue?.trim());
    setDisplaySearchText(inputValue);
  };
  //
  return (
    <div className="relative h-[23rem] ">
      <Divider plain orientation="left" style={{ marginTop: 0 }}>
        <span style={{ fontSize: 14 }} className="font-semibold ">
          {t("voip.renvoi-vers-boite")}
        </span>
      </Divider>
      <div className="my-2 flex justify-center">
        <Input
          autoFocus
          allowClear
          value={displaySearchText}
          onChange={handleOnChange}
          disabled={!voiceTab.length}
          prefix={
            <FiSearch className="text-slate-400" style={{ fontSize: 16 }} />
          }
          placeholder={`${t("voip.searchDirectory")}`}
          style={{
            width: "94%",
          }}
        />
      </div>
      <div className="custom-collapse-panel-webPhone-voiceMail h-[292px] overflow-y-auto">
        {items?.length ? (
          <Collapse
            accordion
            size="small"
            // bordered={false}
            onChange={onCollapseChange}
            ghost
            activeKey={expandedKeys}
          >
            {items.map((item, index) =>
              item.isDivider ? (
                <div
                  key={`divider-${index}`}
                  className="sticky top-0 z-10 bg-slate-50"
                >
                  <Divider style={{ margin: "0rem", fontWeight: 600 }} plain>
                    {item.dividerDate}
                  </Divider>
                </div>
              ) : (
                <Collapse.Panel
                  key={item.key}
                  header={item.label}
                  extra={item.extra}
                  style={item.style}
                  showArrow={false}
                >
                  {item.children}
                </Collapse.Panel>
              )
            )}
          </Collapse>
        ) : !voiceTab.length ? (
          <div className="flex justify-center p-4 text-sm font-semibold">
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={t("voip.noVoicemailFound")}
            />
          </div>
        ) : (
          loaderOptionTemplate(3)
        )}
      </div>
    </div>
  );
}

export default Voicecall;
