import i18next from "i18next";
import {
  SET_LOADING_TAB_VISIO,
  GET_DATA_CHANGE_VISIO,
  SET_LOADING_DETAILS_VISIO,
  GET_DETAILS_VISIO,
  SET_LIST_MEET,
  SET_PAGE,
  SET_KEY_MEET,
  SET_TAB_KEY,
  SET_DETAILS_MEET,
  SET_NOW,
  SET_LATER,
  SET_LAST_PAGE,
  SET_HISTORY_COUNT,
  SET_LABEL,
  SET_DETAILS_MEET_EXTERNAL,
  SET_OPEN_DRAWER_VISIO,
  SET_NOTIFICATION_COUNT,
  SET_NOTIFICATION_LIST,
  SET_REMINDERS_LIST,
  GET_REMINDERS_LIST,
  SET_COUNT_REMINDERS,
  SET_LIMIT,
  SET_SEARCH_LIST_VISIO,
  SET_KPI,
  SET_KPI_DATE,
  SET_LOAD_NOTIF,
  UPDATE_NOTIFICATION_LIST,
  <PERSON>KE_ALL_NOTIFS_READ,
} from "../constants/index";

const initialState = {
  listMeet: [],
  lastPage: 1,
  keyMeet: "",
  page: 1,
  total: 1,
  loadTabs: false,
  loadDetails: false,
  detailsMeet: {},
  tabKey: 1,
  now: 0,
  later: 0,
  countToday: 0,
  countUpComing: 0,
  countHistory: 0,
  openDrawerVisio: false,
  detailsMeetExternal: {},
  notificationCount: 0,
  notificationList: [],
  pageNotificationList: 1,
  lastPageNotificationList: null,
  remindersListVisio: [],
  countReminders: 0,
  limit: 10,
  search: "",
  listKpi: [
    { title: i18next.t("tasks.kpiVisioLabel"), value: "-" },
    { title: i18next.t("tasks.kpiIsOverdueLabel"), value: "-" },
    { title: i18next.t("tasks.kpiCreatedLabel"), value: "-" },
    { title: i18next.t("tasks.KpiChatSource"), value: "-" },
  ],
  listKpiDate: [
    { title: i18next.t("visio.today"), value: "-" },
    { title: i18next.t("tasks.this_week"), value: "-" },
    { title: i18next.t("tasks.tomrrow"), value: "-" },
    { title: i18next.t("tasks.upcoming"), value: "-" },
  ],
  loadNotifsVisio: false,
};

const visioList = (state = initialState, action) => {
  const { payload, type } = action;
  switch (type) {
    case GET_DATA_CHANGE_VISIO:
      return { ...state, ...payload };
    case SET_LOADING_DETAILS_VISIO:
      return { ...state, loadDetails: payload };
    case SET_LOADING_TAB_VISIO:
      return { ...state, loadTabs: payload };
    case SET_LIST_MEET:
      return { ...state, listMeet: payload };
    case SET_PAGE:
      return { ...state, page: payload };
    case GET_DETAILS_VISIO:
      return { ...state, ...payload };
    case SET_KEY_MEET:
      return { ...state, keyMeet: payload };
    case SET_TAB_KEY:
      return { ...state, tabKey: payload };
    case SET_OPEN_DRAWER_VISIO:
      return { ...state, openDrawerVisio: payload };
    case SET_DETAILS_MEET:
      return { ...state, detailsMeet: payload };
    case SET_LAST_PAGE:
      return { ...state, lastPage: payload };
    case SET_NOW:
      return { ...state, now: payload.now, countToday: payload.countToday };
    case SET_LIMIT:
      return { ...state, limit: payload };
    case SET_LATER:
      return {
        ...state,
        later: payload.later,
        countUpComing: payload.countUpComing,
      };
    case SET_LABEL:
      return {
        ...state,
        detailsMeet: { ...state.detailsMeet, label: payload },
      };
    case SET_KPI:
      return {
        ...state,
        listKpi: payload,
      };
    case SET_KPI_DATE:
      return {
        ...state,
        listKpiDate: payload,
      };
    case SET_HISTORY_COUNT:
      return {
        ...state,
        countHistory: payload.countHistory,
      };
    case SET_DETAILS_MEET_EXTERNAL:
      return { ...state, detailsMeetExternal: payload };
    case SET_NOTIFICATION_COUNT:
      return { ...state, notificationCount: payload };
    case SET_NOTIFICATION_LIST:
      return {
        ...state,
        notificationList: payload.notificationList,
        pageNotificationList: payload.pageNotificationList,
        lastPageNotificationList: payload.lastPageNotificationList,
      };
    case UPDATE_NOTIFICATION_LIST:
      return {
        ...state,
        notificationList: payload,
      };
    case MAKE_ALL_NOTIFS_READ:
      return {
        ...state,
        notificationList:
          payload &&
          payload.map((notification) =>
            notification?.read === 0
              ? { ...notification, read: 1 }
              : notification
          ),
      };
    case GET_REMINDERS_LIST:
      return {
        ...state,
        remindersListVisio: payload,
      };
    case SET_REMINDERS_LIST:
      return {
        ...state,
        remindersListVisio: payload.remindersListVisio,
      };
    case SET_COUNT_REMINDERS:
      return {
        ...state,
        countReminders: payload,
      };
    case SET_SEARCH_LIST_VISIO:
      return {
        ...state,
        search: payload,
      };
    case SET_LOAD_NOTIF:
      return {
        ...state,
        loadNotifsVisio: payload,
      };

    default:
      return state;
  }
};

export default visioList;
