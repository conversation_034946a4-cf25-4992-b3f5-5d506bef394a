import { Badge, Dropdown, Typography } from "antd";
import {
  InfoCircleOutlined,
  MessageOutlined,
  PhoneOutlined,
} from "@ant-design/icons";
import { HighlightSearchW } from ".";
import { FiCopy, FiMoreVertical } from "react-icons/fi";
import DisplayAvatar from "./DisplayAvatar";
import { MdOutlinePersonAddAlt } from "react-icons/md";
import { CgUserlane } from "react-icons/cg";
import {
  HiOutlineBuildingOffice,
  HiOutlineUserGroup,
  HiOutlineVideoCamera,
} from "react-icons/hi2";
import { openDrawerChat } from "new-redux/actions/voip.actions/handleDrawerChat";

export const ActionMenu = ({
  record: {
    name,
    number,
    info: { id, familyId, uuid },
  },
  handleAddItem,
  handleDisplayElementInfo,
  call,
  t,
  dispatch,
}) => {
  //
  // console.log(name, { record });

  const menuDropdown = () => {
    //
    const items = [];
    //
    const copyIcon = (text) => (
      <div className="pl-1">
        <Typography.Paragraph
          copyable={{
            text: text,
            icon: [
              <FiCopy
                style={{
                  color: "rgb(22, 119, 255)",
                  marginTop: "2px",
                  fontSize: "15px",
                }}
              />,
            ],
          }}
        />
      </div>
    );

    const pushItem = (key, icon, label, onClick, disabled, children) => {
      items.push({
        key,
        icon,
        label,
        onClick,
        disabled,
        children,
      });
    };

    if (name) {
      pushItem(
        `${number}`,
        <PhoneOutlined rotate={100} style={{ fontSize: 15 }} />,
        <div className="flex flex-row justify-between">
          {name ? `${name?.split(" ")?.[0]} (${number})` : number}
          {copyIcon(number)}
        </div>,
        () => call(number, id, familyId)
      );
    } else {
      pushItem(
        `${number}`,
        <PhoneOutlined rotate={100} style={{ fontSize: 15 }} />,
        <div className="flex flex-row justify-between">
          {`${number}`} {copyIcon(number)}
        </div>,
        () => call(number, id, familyId)
      );
    }
    if (uuid) {
      items.push({ type: "divider" });
      pushItem(
        `chat-${number}`,
        <MessageOutlined style={{ fontSize: 14 }} />,
        `${t("voip.chatWith")} ${name?.split(" ")?.[0]}`,
        () => dispatch(openDrawerChat(uuid))
      );
    }
    if (id && familyId) {
      items.push({ type: "divider" });
      pushItem(
        "more-info",
        <InfoCircleOutlined style={{ fontSize: 14 }} />,
        `${t("voip.moreInfoWith")} ${name?.split(" ")?.[0]}`,
        () =>
          handleDisplayElementInfo(name, {
            id: id,
            familyId: familyId,
          })
      );
    } else {
      items.push({ type: "divider" });
      pushItem(
        "add",
        <MdOutlinePersonAddAlt style={{ fontSize: 15 }} />,
        t("voip.addToModule"),
        null,
        null,
        [
          {
            label: t("contacts.leads"),
            key: "add-leads",
            icon: <CgUserlane style={{ fontSize: 15 }} />,
            onClick: () => handleAddItem(9, number),
          },
          {
            label: t("contacts.contact"),
            key: "add-contact",
            icon: <HiOutlineUserGroup style={{ fontSize: 15 }} />,
            onClick: () => handleAddItem(2, number),
          },
          {
            label: t("contacts.company"),
            key: "add-company",
            icon: <HiOutlineBuildingOffice style={{ fontSize: 15 }} />,
            onClick: () => handleAddItem(1, number),
          },
        ]
      );
    }

    return { items };
  };

  return (
    <Dropdown trigger={["click"]} placement="bottomRight" menu={menuDropdown()}>
      <FiMoreVertical className="h-[18px] w-[18px] cursor-pointer text-gray-400 hover:text-gray-700" />
    </Dropdown>
  );
};

const CallerColumnMessaging = (props) => {
  //
  const {
    record: { name, image, state, number },
    searchText,
  } = props;
  //
  const mainText = `font-semibold truncate`;
  const secondaryTextStyle = `  leading-4 ${
    state === "Nouveau" ? "font-semibold" : "font-medium"
  } text-slate-500`;
  //
  return (
    <div className="relative flex w-full flex-row justify-between">
      <div className="relative flex w-10/12 flex-row items-center space-x-2">
        <div className="_avatar_">
          <DisplayAvatar
            name={name}
            urlImg={image}
            size={40}
            icon={number === "sphere_visio" && <HiOutlineVideoCamera />}
          />
        </div>
        <div className="w-full flex-col space-y-0.5 ">
          <p className={mainText}>
            {state === "Nouveau" ? <Badge status="processing" /> : null}{" "}
            {name ? HighlightSearchW(name, searchText) : ""}
          </p>
          <p className={name ? secondaryTextStyle : mainText}>
            {HighlightSearchW(number, searchText)}
          </p>
        </div>
      </div>
      <div className="flex items-center ">
        {number !== "sphere_visio" && <ActionMenu {...props} />}
      </div>
    </div>
  );
};

export default CallerColumnMessaging;
