import React, { useState } from "react";
import {
  Input,
  Select,
  Button,
  Table,
  InputNumber,
  Tooltip,
  Empty,
} from "antd";
import {
  DeleteOutlined,
  ReloadOutlined,
  PlusOutlined,
  EditOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";

import EditableFormItem from "./EditableFormItem";
import { handleResetRow } from "./utils/utilsFunctions";
import { useWindowSize } from "pages/clients&users/components/WindowSize";

const EditableCartTable = (props) => {
  const {
    rowElement,
    form,
    remove,
    productsList,
    discountsList,
    add,
    selectedCurrencySymbol,
    openCreatModal,
    setEditingIndex,
    editingIndex,
    addingIndex,
    setaddingIndex
  } = props;

  const [t] = useTranslation("common");
  const windowSize = useWindowSize();

  const handleDeleteRow = (idx) => {
    setEditingIndex((prev) => prev.filter((i) => i !== idx));
    remove(idx);
  };
  const [isNewProduct, setNewProduct] = useState(false);
  // Prevent the user from typing non numeric characters in number input.
  const preventTypingNonNumeric = (e) => {
    if (!/^[0-9]$/.test(e?.key)) {
      e?.preventDefault();
    }
  };

  const onCancel = (index) => {
    if (isNewProduct) {
      remove(index);
    } else {
      form.resetFields([["users", index, "age"], ["users", index, "name"]]);
    }

    setNewProduct(false);
    setEditingIndex(undefined);
  };


  const columns = [
    {
      title: t("import.product"),
      dataIndex: "product",
      width:180,
      render: (value, row, index) => (
        <>
          <EditableFormItem
            name={[index, "product"]}
            rules={[{ required: true }]}
            editing={ editingIndex?.includes(index) || addingIndex?.includes(index)}
          >
            <Select
              placeholder={t("cart.selectProduct")}
              options={productsList}
            />
          </EditableFormItem>
          <EditableFormItem
            editing={ editingIndex?.includes(index) || addingIndex?.includes(index)}
            name={[index, "productDescription"]}
          >
            <Input.TextArea placeholder={t("unavailability.description")} />
          </EditableFormItem>
        </>
      ),
    },
    {
      title: t("cart.quantity"),
      dataIndex: "qty",
      width:80,
      render: (value, row, index) => (
        <>
          <EditableFormItem
            editing={ editingIndex?.includes(index) || addingIndex?.includes(index)}
            name={[index, "qty"]}
            rules={[{ required: true }]}
          >
            <InputNumber
              placeholder={t("cart.quantity")}
              style={{ width: "100%" }}
              onKeyPress={preventTypingNonNumeric}
            />
          </EditableFormItem>
          <EditableFormItem editing={ editingIndex?.includes(index) || addingIndex?.includes(index)}  name={[index, "unit"]}>
            <Input placeholder={t("cart.unit")} />
          </EditableFormItem>
        </>
      ),
    },
    {
      title: t("cart.unitPrice"),
      dataIndex: "unitPrice",
      width:80,
      render: (value, row, index) => (
        <EditableFormItem
        editing={ editingIndex?.includes(index) || addingIndex?.includes(index)}
          name={[index, "unitPrice"]}
          rules={[{ required: true }]}
        >
          <InputNumber
            placeholder={t("cart.unitPrice")}
            style={{ width: "100%" }}
            onKeyPress={preventTypingNonNumeric}
          />
        </EditableFormItem>
      ),
    },
    {
      title: t("cart.discount"),
      dataIndex: "discount",
      width:80,
      render: (value, row, index) => (
        <EditableFormItem
        editing={ editingIndex?.includes(index) || addingIndex?.includes(index)}
          name={[index, "discount"]}
        >
          <Select
            placeholder={t("cart.discount")}
            style={{ width: "100%" }}
            options={discountsList}
          />
        </EditableFormItem>
      ),
    },
    {
      title: t("cart.tax"),
      dataIndex: "tax",
      width:80,
      render: (value, row, index) => (
        <EditableFormItem
        editing={ editingIndex?.includes(index) || addingIndex?.includes(index)}
          name={[index, "tax"]}
        >
          <InputNumber
            placeholder={t("cart.tax")}
            style={{ width: "100%" }}
            onKeyPress={preventTypingNonNumeric}
          />
        </EditableFormItem>
      ),
    },
    {
      title: t("cart.amount"),
      dataIndex: "amount",
      width:80,
      render: (value, row, index) => {
        const test= form.getFieldValue("products") && form.getFieldValue("products")[index]
        
       return  test ? (test?.qty * test?.unitPrice * (1 - test.discount / 100) * (1 + test.tax / 100)).toFixed(2): "-"
       
       
       /*<EditableFormItem
          editing={!!editingIndex?.includes(index)}
          name={[index, "amount"]}
        >{`$ 100`}</EditableFormItem>*/}
      
    },
    {
      title: "Actions",
      dataIndex: "actions",
      width: 80,
      render: (value, row, index) =>
        !!editingIndex?.includes(index) ? (
          <div className="w-full">
              {!addingIndex?.includes(index) && <Tooltip title="Reset Fields">
            <Button
              type="text"
              shape="circle"
              icon={<ReloadOutlined />}
              onClick={() => {
                setEditingIndex(editingIndex.filter((i) => i !== index));
                // let dd = form.getFieldsValue(true);
                // console.log({ dd });
                //  handleResetRow(form, rowElement[index])
              }}
            />
          </Tooltip>}
            <Tooltip title={t("table.delete")}>
              <Button
                type="text"
                shape="circle"
                icon={<DeleteOutlined />}
                danger
                onClick={() => {
                  handleDeleteRow(index);
                }}
              />
            </Tooltip> 
          </div>
        ) : (
          <div className="flex items-center">
           {!addingIndex?.includes(index) && <Button
              icon={<EditOutlined />}
              type="text"
              shape="circle"
              disabled={editingIndex?.length > 0}
              onClick={() =>[
                
                setEditingIndex(old=>[...old,index])]}
            />}
            <Tooltip title={t("table.delete")}>
              <Button
                type="text"
                shape="circle"
                icon={<DeleteOutlined />}
                danger
                onClick={() => {
                  handleDeleteRow(index);
                }}
              />
            </Tooltip>
          </div>
        ),
    },
  ];

  const addProduct = () => {
    add();
    setaddingIndex(prev=>[...prev,rowElement.length]);
  };

  
  return rowElement?.length > 0 ? (
    <Table
      dataSource={rowElement}
      pagination={false}
      rowClassName="cart-table-row"
      columns={columns}
      scroll={{
        x: '100%' ,
        y: windowSize?.height - 420,
      }}
      footer={() => (
        <Button onClick={addProduct} icon={<PlusOutlined />} type="text">
          {t("cart.addNewProductBtn")}
        </Button>
      )}
    />
  ) : (
    <div className="flex flex-col justify-center">
      <Empty
        image={Empty.PRESENTED_IMAGE_SIMPLE}
        description={t("cart.emptyCart")}
      />
      <div className="flex justify-center">
        <Button onClick={addProduct} icon={<PlusOutlined />} type="primary">
          {t("cart.startAddingProducts")}
        </Button>
      </div>
    </div>
  );
};

export default EditableCartTable;
