import { getCurrentTime } from "components/webphone/Webphone/useVoipRegister";
import { URL_ENV } from "index";
import { useEffect, useState, useRef, useCallback } from "react";
import { useSelector } from "react-redux";

export const useMercurePanelLive = ({ fetchLivePanelData }) => {
  //
  const currentUser = useSelector((state) => state.chat.currentUser);
  //
  const [data, setData] = useState(null);

  const eventSourceRef = useRef(null);
  const retryCountRef = useRef(0);

  const buildMercureURL = useCallback(() => {
    //
    const mercureURL = new URL(URL_ENV?.REACT_APP_URL_MERCURE);
    //
    mercureURL.searchParams.append(
      "topic",
      process.env.REACT_APP_MERCURE_PANEL_LIVE + URL_ENV?.REACT_APP_TENANT_NAME
    );
    //
    mercureURL.searchParams.set("authorization", currentUser?.jwt_mercure);
    //
    return mercureURL;
  }, [currentUser?.jwt_mercure]);

  const connectToMercure = useCallback(() => {
    //
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
    }
    //
    const url = buildMercureURL();
    const es = new EventSource(url);

    eventSourceRef.current = es;
    //
    es.onopen = () => {
      console.log("Mercure P_Live connection opened", getCurrentTime());
      // Optionally fetch the latest data once connected:
      if (retryCountRef.current > 0) {
        console.log("Fetch the latest data");
        fetchLivePanelData();
      }
      // Reset retry count on successful connection
      retryCountRef.current = 0;
    };

    // Listen
    es.addEventListener("message", (event) => {
      if (!event.data) return;
      try {
        const parsed = JSON.parse(event.data);
        // Filter Events
        if (
          parsed.type_event === "connected_posts" ||
          (parsed.type_event === "change_status" &&
            parsed.data?.webphone?.status !== "NOT_INUSE" &&
            parsed.data?.webphone?.status !== "UNAVAILABLE")
        ) {
          // console.log("reeeeeeeeee", parsed);
          return;
        }
        console.log("P_Live event:", parsed);
        setData(parsed);
      } catch (err) {
        console.error("Error parsing event data P_Live:", err);
      }
    });
    //
    // Handle errors
    es.onerror = (error) => {
      console.error(
        "Mercure EventSource P_Live error:",
        getCurrentTime(),
        error
      );

      es.close();

      // Optional fallback: fetch data so the UI isn't stale
      // fetchLivePanelData?.();

      //
      retryCountRef.current += 1;
      const retryDelay = Math.min(3000 * retryCountRef.current, 30000);
      // 3s, 6s, 9s, up to 30s max

      console.log(`Reconnecting in ${retryDelay / 1000} seconds...`);
      setTimeout(() => {
        connectToMercure();
      }, retryDelay);
    };
  }, [buildMercureURL, fetchLivePanelData]);

  //
  //
  useEffect(() => {
    //
    if (!currentUser?.jwt_mercure) return;
    //
    connectToMercure();
    //
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
    };
  }, [currentUser?.jwt_mercure, connectToMercure]);

  return data;
};
