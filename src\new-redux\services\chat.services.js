import MainService from "../../services/main.service";
import {
  addNotification,
  addToMuteList,
  removeFromMuteList,
  resetStateOtherUser,
  searchMsgInListConversations,
  serachMessageData,
  setAllPinnedFetched,
  setAllStarredFetched,
  setArchivedListChatFetched,
  setChatSelectedConversation,
  setChatSelectedParticipants,
  setExternalItem,
  setLoadingSideBar,
  setMembersGroupsChat,
  setMembersGroupsChatFetched,
  setNumberUnreadMsg,
  setOpenDrawer,
  setSyncNewMsg,
  substractNotification,
  updateChatSelectedConversation,
} from "../actions/chat.actions";
import {
  GET_ARCHIVE_CONVERSATIONS,
  LEAVE_PARTICIPANT,
  SET_ARCHIVED_LIST_IDS,
  SET_CHAT_USER_LIST,
  SET_MESSAGE_READ_CHAT,
  SET_NOTIFICATION_NUMBER,
  SET_PINNED_LIST,
  SET_CONFIG_USER_CHAT,
  SET_SEARCH_LIST,
  SET_STARRED_LIST,
  SET_USER_INFO_CHAT,
  SET_MESSAGE_UNDO_READ_CHAT,
  UPDATE_ROOM_BY_ID_CHAT,
  SET_ALL_STARRED_LIST,
  SET_ALL_PINNED_LIST,
  SET_POLL_LIST,
  SET_ALL_POLL_LIST,
  SET_ERROR_MEMBERS_GROUPS_CHAT,
  SET_NEW_UPDATE_ON_MESSAGE,
} from "../constants";

import { toastNotification } from "../../components/ToastNotification";
import { updateMessages } from "../../pages/layouts/chat/utils/rqUpdate";
import { store } from "../store";
import { updateParticipantsList } from "../../pages/layouts/chat/utils/infoRoom";
import {
  start_call,
  start_video_call,
  stop_call,
} from "../actions/voip.actions/callIPBX";
import {
  LATENCE_ADDING_TIME,
  getOrGenerateTabId,
  isRoom,
} from "../../pages/layouts/chat/utils/ConversationUtils";
import { decryptC } from "../../components/webphone/Webphone/PhoneCpt";
import { moment_timezone } from "../../App";
import { LoadingSideBarStatus } from "new-redux/reducers/chatReducer";
import i18next from "i18next";
import { setLatenceTimeout } from "new-redux/actions/chat.actions/realTime";
import { pingFunction } from "utils/ping";
import { URL_ENV } from "index";
import { closeDrawerChat } from "new-redux/actions/voip.actions/handleDrawerChat";
import {
  setChatInViewSPhereFromDrawer,
  setNbrChatInViewSPhere,
} from "new-redux/actions/vue360.actions/vue360";
const getChatInViewSphere = async () =>
  await store.getState()?.vue360?.chatInViewSphere;
const getChatInViewSphereFromDrawer = async () =>
  await store.getState()?.vue360?.chatInViewSphereFromDrawer;
const getOpenChatInViewSphere = async () =>
  await store.getState()?.vue360?.openChatInViewSphere;

export const pingPongFunction = () => async (dispatch) => {
  try {
    const latenceLocal = await pingFunction(
      URL_ENV?.REACT_APP_OAUTH_CHAT_API + "/" + process.env.REACT_APP_SUFFIX_API
    );
    dispatch(setLatenceTimeout(Math.floor(latenceLocal) + LATENCE_ADDING_TIME));
    return latenceLocal;
  } catch (error) {
    dispatch(setLatenceTimeout(Math.floor(LATENCE_ADDING_TIME * 2)));
  }
};
export const getUserListChat = () => (dispatch) => {
  const promise = new Promise(async (resolve) => {
    try {
      let response = await MainService.getAllUsers();

      dispatch({
        type: SET_CHAT_USER_LIST,
        payload: response?.data?.all_users || [],
      });

      resolve();
    } catch (error) {
      //   reject(error);
    }
  });

  return promise;
};
export const getUserChatInfo = () => (dispatch) => {
  const promise = new Promise(async (resolve, reject) => {
    try {
      const response = await MainService.getInfoChat();
      if (response?.status === 200) {
        const payload = {
          ...response?.data?.user,
          jwt_mercure: response?.data?.jwt_mercure,
          integrations: response?.data?.integrations,
        };
        dispatch({
          type: SET_USER_INFO_CHAT,
          payload: payload || null,
        });
        resolve();
      }
    } catch (error) {
      reject(error);
    }
  });

  return promise;
};
export const redirectToChat = (email, navigate) => (dispatch) => {
  const promise = new Promise(async (resolve, reject) => {
    try {
      const response = await MainService.redirectToChat({ email });
      let item = null;
      if (response.data.old_conversation) {
        const isItemRoom = isRoom(response.data.conversation);
        item = {
          ...response.data.conversation,
          type: isItemRoom ? "room" : "user",
          isCurrentItem: true,
          _id: isItemRoom
            ? response.data.conversation?.room?._id
            : response.data.conversation?.contact?._id,
          _idConversation: response.data.conversation?._id,
          participants: !isItemRoom,
        };
      } else {
        item = { ...response.data.user, type: "user" };
      }
      dispatch(
        resetStateOtherUser({
          forced: false,
          keepDrawerOpened: false,
          item: {
            ...item,
            isCurrentItem: true,
            participants: true,
          },
        })
      );
      resolve(response);
    } catch (error) {
      reject(error);
    } finally {
      navigate && navigate("/chat");
      localStorage.removeItem("redirectChatLink");
    }
  });

  return promise;
};
export const setMessageReadChat = (payload) => (dispatch) => {
  const promise = new Promise(async (resolve, reject) => {
    try {
      if (payload._id === undefined) return resolve();

      let response;
      const searchChatSideBar = await store.getState().chat.searchChatSideBar;

      dispatch({
        type: SET_MESSAGE_READ_CHAT,
        payload: {
          idRead: payload._id,
          type: payload.type,
        },
      });
      if (searchChatSideBar !== "") {
        setTimeout(() => {
          window.dispatchEvent(
            new CustomEvent(SET_NEW_UPDATE_ON_MESSAGE, {
              detail: {
                conversation_id: payload.item._id,
              },
            })
          );
        }, 0);
      }

      const chatInViewSphere = await getChatInViewSphere();
      const chatInViewSphereFromDrawer = await getChatInViewSphereFromDrawer();
      const openChatInViewSphere = await getOpenChatInViewSphere();
      const form = new FormData();
      form.append("tab_id", getOrGenerateTabId());

      if (payload.type === "room") {
        form.append("room_id", payload._id);
        form.append("count_unread_msg", 1);
        // if (payload.item.total_unread === 0) return resolve();
      } else {
        form.append("sender_id", payload._id);
      }
      response = await MainService.makeReadMessage(form, payload.type);
      const currentUser = await store.getState().chat.currentUser;
      if (payload.source !== "no_chat")
        dispatch(
          substractNotification({
            number: payload.number,
            discussion_id: payload.item._id,
          })
        );
      updateMessages(
        null,
        payload.type === "user" ? "make_read" : "make_read_room",
        null,
        payload._id,
        payload.type,
        currentUser?._id
      );

      dispatch(setExternalItem(null));

      if (payload._id === chatInViewSphere?.id && openChatInViewSphere) {
        dispatch(
          setNbrChatInViewSPhere({
            id: "",
            number: 0,
            relation_id: "",
          })
        );
      }

      if (
        payload._id === chatInViewSphereFromDrawer?.id &&
        openChatInViewSphere
      ) {
        dispatch(
          setChatInViewSPhereFromDrawer({
            id: "",
            number: 0,
            relation_id: "",
          })
        );
      }

      // dispatch(setNbrChatInViewSPhere({ number: 0, id: "" }));
      resolve(response.data);
    } catch (err) {
      if (err?.response?.status && err?.response?.status === 401) return;
      if (payload.source !== "no_chat") {
        dispatch({
          type: SET_MESSAGE_UNDO_READ_CHAT,
          payload: { item: payload.item, id: payload._id, type: payload.type },
        });
        //  dispatch(scrollToBottom(Math.floor(Math.random() * 1000000 + 1)));

        dispatch(
          addNotification({
            number: payload.number,
            discussion_id: payload.item._id,
          })
        );
      }
      toastNotification("error", payload.errorText, "topRight");

      setTimeout(() => {
        window.dispatchEvent(
          new CustomEvent(SET_NEW_UPDATE_ON_MESSAGE, {
            detail: {
              conversation_id: payload.item._id,
            },
          })
        );
      }, 0);
      reject(err);
    }
  });
  return promise;
};

export const setConfigChatUser = (payload) => (dispatch) => {
  const promise = new Promise(async (resolve, reject) => {
    const currentUser = await store.getState().chat.currentUser;
    payload = {
      sort_message: payload.sort_message || 0,
      hidden_message: payload.hidden_message || 0,
      sound_notification: payload.sound_notification ?? 1,
      notification: payload.notification ?? 1,
    };
    try {
      dispatch({
        type: SET_CONFIG_USER_CHAT,
        payload: payload,
      });
      await MainService.updateConfigChatUser(payload);

      resolve();
    } catch (error) {
      dispatch({
        type: SET_CONFIG_USER_CHAT,
        payload: currentUser?.config,
      });
      reject(error);
    }
  });
  return promise;
};

export const updateRoomInfosChat = (payload) => (dispatch) => {
  const promise = new Promise(async (resolve, reject) => {
    try {
      const selectedConversation = await store.getState().ChatRealTime
        .selectedConversation;

      const data = { ...payload, tab_id: getOrGenerateTabId() };
      delete data.id;

      const response = await MainService.updateRoomInfo(payload.id, data);
      if (response.data.success) {
        dispatch({
          type: UPDATE_ROOM_BY_ID_CHAT,
          payload: response.data.room,
        });
        if (payload.new_admin_id) {
          updateParticipantsList(
            response.data.room?.participants ?? [],
            response.data.room?._id,
            "room"
          );
          dispatch(
            setChatSelectedParticipants({
              selectedParticipants: response.data.room?.participants ?? [],
            })
          );
        }

        dispatch(
          setChatSelectedConversation({
            selectedConversation: {
              ...selectedConversation,
              name: response.data.room.name,
              description: response.data.room.description,
              image: response.data.room.image,
              admin_id: response.data.room.admin_id,
              id: response.data.room?._id,
              type: "room",
            },
          })
        );

        // toastNotification(
        //   "success",
        //   `${response?.data?.room?.name} ${payload.editText}`,
        //   "topRight"
        // );
        resolve(response.data);
      } else {
        reject(new Error(response.data));
      }
    } catch (error) {
      reject(error);
    }
  });
  return promise;
};
export const leaveParticpant = (payload) => (dispatch) => {
  const promise = new Promise(async (resolve, reject) => {
    try {
      let response = await MainService.leaveUserFromRoom({
        ...payload,
        tab_id: getOrGenerateTabId(),
      });
      if (response.data.success) {
        dispatch({
          type: LEAVE_PARTICIPANT,
          payload,
        });
        // toastNotification(
        //   "success",
        //   `${response?.data?.room?.name} ${payload.editText}`,
        //   "topRight"
        // );
      }

      resolve();
    } catch (error) {
      reject(error);
    }
  });
  return promise;
};

export const paginatationSearchMsg = (payload) => (dispatch) => {
  const promise = new Promise(async (resolve, reject) => {
    try {
      if (!payload.last_id) {
        dispatch(
          searchMsgInListConversations({
            miniLoadingPrevious: false,
            miniLoadingNext: false,
            orientation: payload.type,
            hasMorePrevious: false,
            hasMoreNext: undefined,
          })
        );
        return resolve();
      }
      dispatch(
        searchMsgInListConversations({
          orientation: payload.type,
          miniLoadingPrevious: payload.type === "previous" ? true : undefined,
          miniLoadingNext: payload.type === "next" ? true : undefined,
        })
      );
      let formData = new FormData();
      formData.append("last_id", payload.last_id);
      formData.append(
        payload.type_discussion === "user" ? "receiver_id" : "room_id",
        payload.receiver_id
      );
      formData.append("type", payload.type);
      let response = await MainService.paginationSearchMsg(
        formData,
        payload.type_discussion,
        payload.page
      );
      if (response.data.success) {
        dispatch(
          serachMessageData({
            data: response.data.data,
            type: payload.type,
          })
        );

        dispatch(
          searchMsgInListConversations({
            miniLoadingPrevious: false,
            miniLoadingNext: false,
            orientation: payload.type,
            hasMorePrevious:
              payload.type === "previous"
                ? response.data.data.length > 0
                  ? response.data.meta.current_page <
                    response.data.meta.last_page
                  : false
                : undefined,
            hasMoreNext:
              payload.type === "next"
                ? response.data.data.length > 0
                  ? response.data.meta.current_page <
                    response.data.meta.last_page
                  : false
                : undefined,
          })
        );

        resolve();
      } else {
        reject();
      }
    } catch (error) {
      reject(error);
    }
  });
  return promise;
};

export const callApi = (payload) => async (dispatch) => {
  try {
    if (payload.setClicked) payload.setClicked(true);
    const params = await store.getState().voip.param;
    const callIPBX = await store.getState().voipBlackList.callIPBX;

    if (callIPBX?.post_numberR && callIPBX?.disableCall) return;
    else if (decryptC(params.param_5 || "") === "false") {
      const { user } = await store.getState().user;

      const response = await MainService.callApiIPBX(
        user.extension,
        payload.post_numberR
      );
      if (response.status === 200) payload.setClicked(false);
    } else {
      await dispatch(
        start_call({
          ...payload,
          disableCall: true,
        })
      );

      if (payload.setClicked) payload.setClicked(false);
    }
  } catch (error) {
    dispatch(stop_call());

    if (payload.setClicked) payload.setClicked(false);
    // toastNotification("error", payload.errorText, "topRight");
  } finally {
    if (payload.setClicked) payload.setClicked(false);
  }
};
export const callVideo = (payload) => async (dispatch) => {
  try {
    if (payload.setClicked) payload.setClicked(true);
    await dispatch(start_video_call(String(payload.post_numberR)));

    payload.setClicked(false);
  } catch (error) {
    if (payload.setClicked) payload.setClicked(false);
    // toastNotification("error", payload.errorText, "topRight");
  }
};
const getItemFromErrorMessage = (array = [], id = null, type_disc = "") =>
  array.find(
    (item) => item.discussion_id === id && item.type_conversation === type_disc
  );

export const getConversationApi = (payload) => async (dispatch) => {
  const promise = new Promise(async (resolve, reject) => {
    try {
      const membersGroupsChatFetched = await store.getState().ChatRealTime
        .membersGroupsChatFetched;
      const errorMessages = await store.getState().chat.errorMessages;

      if (membersGroupsChatFetched) return resolve();

      const response = await MainService.getConversations();
      if (response?.data?.success) {
        dispatch(setMembersGroupsChatFetched(true));

        let arrayResponse = response.data.conversations || [];
        const error_ids = errorMessages.map((item) => item.discussion_id);
        arrayResponse = arrayResponse.map((item) => {
          let id_discussion =
            item.room !== null ? item.room?._id : item.contact?._id;
          const item_error_message = getItemFromErrorMessage(
            errorMessages,
            id_discussion,
            item.room !== null ? "room" : "user"
          )?.message;
          if (error_ids.includes(id_discussion)) {
            return {
              ...item,
              last_message: {
                ...item.last_message,

                message: item_error_message?.message,
                type: item_error_message?.type,
                unread: "error",
              },
              sender: item_error_message?.sender,
              last_message_date: moment_timezone(new Date()).format(),
            };
          }
          return item;
        });

        // for (let i = 0; i < 100; i++) {
        //   const randomId = Math.floor(i * 90000) + 2;
        //   arrayResponse = arrayResponse.map((item, i) => ({ ...item, id: randomId + i }));

        //   arrayResponse = [...arrayResponse, ...response.data.conversations];
        // }
        Promise.all([
          dispatch(
            setMembersGroupsChat({
              list: arrayResponse,
            })
          ),
          dispatch({
            type: SET_ERROR_MEMBERS_GROUPS_CHAT,
            payload: false,
          }),
        ]);

        return resolve(arrayResponse);
      } else {
        reject();
      }
    } catch (error) {
      dispatch(setMembersGroupsChatFetched(false));
      dispatch({
        type: SET_ERROR_MEMBERS_GROUPS_CHAT,
        payload: true,
      });
      toastNotification("error", payload.errorText, "topRight");
      reject(error);
    } finally {
    }
  });
  return promise;
};
export const getLastMessageAPI = (payload) => async (dispatch) => {
  if (!payload.message_id) return;
  // in case refetch is false : fetching data for the first time
  // in case refetch is true : fetching data for the second time and it throw error
  !payload.refetch
    ? dispatch(setSyncNewMsg(true))
    : dispatch(
        setSyncNewMsg({
          id: payload?.id,
          message_id: payload?.message_id,
          type: payload?.type,
          loading: true,
        })
      );
  const promise = new Promise(async (resolve, reject) => {
    try {
      const latence = await store.getState().chat.latence;
      const abort = new AbortController();
      const formData = new FormData();
      const time = setTimeout(() => {
        abort.abort();
        clearTimeout(time);
      }, latence + LATENCE_ADDING_TIME * 3);

      formData.append(
        payload.type === "room" ? "room_id" : "receiver_id",
        payload.id
      );
      formData.append("message_id", payload.message_id);

      const response = await MainService.getLastMessage(
        formData,
        payload.type,
        abort.signal
      );
      dispatch(setSyncNewMsg(null));

      updateMessages(
        response.data.messages.reverse(),
        "new_messages",
        null,
        payload.id,
        payload.type,
        null
      );
      response.data.messages.forEach((element) => {
        updateMessages(
          null,
          payload.type === "user" ? "make_read" : "make_read_room",
          element._id,
          payload.type === "user" ? element.sender_id : element?.room_id,
          payload.type,
          payload.type === "user" ? null : element.sender_id
        );
      });

      dispatch(
        setNumberUnreadMsg({
          id: payload.id,
          number: response.data.messages?.length || 0,
        })
      );

      return resolve(response.data);
    } catch (error) {
      dispatch(
        setSyncNewMsg({
          id: payload._id,
          message_id: payload.message_id,
          type: payload.type,
          loading: false,
        })
      );
      if (error.name === "CanceledError") return;
      toastNotification("error", payload.errorText, "topRight");
      reject(error);
    }
  });

  return promise;
};
export const totalNotification = () => (dispatch) => {
  const promise = new Promise(async (resolve, reject) => {
    try {
      const abortController = new AbortController();
      const latence = await store.getState().chat.latence;

      setTimeout(
        () => abortController.abort(),
        latence || LATENCE_ADDING_TIME * 3
      );

      let response = await MainService.getTotalNotification(
        abortController.signal
      );

      dispatch({
        type: SET_NOTIFICATION_NUMBER,
        payload: {
          total: response.data.total_messages_count,
          number: response.data.total_messages.length,
          conversations: response.data.total_messages.map((item) => item._id),
        },
      });
      return resolve(response.data);
    } catch (error) {
      if (error.name === "AbortError") return;
      reject(error);
    }
  });

  return promise;
};

export const starredMessages = (payload) => async (dispatch) => {
  const promise = new Promise(async (resolve, reject) => {
    try {
      if (payload.currentPage > 1) {
        payload.setLoadingMore(true);
      } else payload.setLoading(true);
      const form = new FormData();

      form.append(
        payload.type === "room" ? "room_id" : "receiver_id",
        payload.id
      );

      let response = await MainService.getStarredMessages(form, payload);
      dispatch({
        type: SET_STARRED_LIST,
        payload: response.data.data,
      });
      payload.setHasMore(
        response.data.meta.current_page < response.data.meta.last_page
      );

      return resolve(response.data.data);
    } catch (error) {
      payload.setError(false);

      reject(error);
    } finally {
      payload.setLoadingMore(false);
      payload.setLoading(false);
    }
  });

  return promise;
};

export const pinnedMessages = (payload) => async (dispatch) => {
  const promise = new Promise(async (resolve, reject) => {
    try {
      if (payload.currentPage > 1) {
        payload.setLoadingMore(true);
      } else payload.setLoading(true);
      const form = new FormData();
      form.append("tab_id", getOrGenerateTabId());
      form.append(
        payload.type === "room" ? "room_id" : "receiver_id",
        payload.id
      );

      let response = await MainService.getPinnedMessages(form, payload);
      dispatch({
        type: SET_PINNED_LIST,
        payload: response.data.data,
      });
      payload.setHasMore(
        response.data.meta.current_page < response.data.meta.last_page
      );

      return resolve(response.data.data);
    } catch (error) {
      payload.setError(error);

      reject(error);
    } finally {
      payload.setLoadingMore(false);
      payload.setLoading(false);
    }
  });

  return promise;
};

export const searchMessages = (payload) => async (dispatch) => {
  const promise = new Promise(async (resolve, reject) => {
    try {
      const abort = new AbortController();
      let time = null;
      time = setTimeout(() => {
        abort.abort();
        throw new Error("CanceledError");
      }, 25000);
      const form = new FormData();
      form.append(
        payload.type === "room" ? "room_id" : "receiver_id",
        payload.id
      );

      form.append("keyword", payload.keyword);
      if (payload.currentPage > 1) payload.setLoadingMore(true);
      else payload.setLoading(true);
      const response = await MainService.getSearchMessages(
        form,
        payload,
        abort.signal
      );
      clearTimeout(time);

      dispatch({
        type: SET_SEARCH_LIST,
        payload: {
          page: payload.currentPage,
          list: response.data.data,
        },
      });
      payload.setHasMore(
        response.data.meta.current_page < response.data.meta.last_page
      );
      payload.setLoadingMore(false);
      payload.setLoading(false);
      payload.setTotalSearch &&
        payload.setTotalSearch(response?.data?.meta?.total || 0);
      payload.setError("");

      return resolve(response.data.data);
    } catch (error) {
      //      if (error.name === "CanceledError") return;
      payload.setError(error);
      payload.setLoading(false);
      reject(error);
    }
  });

  return promise;
};

export const archiveRoom = (payload) => async (dispatch) => {
  const promise = new Promise(async (resolve, reject) => {
    try {
      const formData = new FormData();
      formData.append("room_id", payload);
      formData.append("tab_id", getOrGenerateTabId());
      let response = await MainService.archiveRoom(formData);
      dispatch(closeDrawerChat());

      dispatch(
        setChatSelectedConversation({
          selectedConversation: null,
        })
      );
      dispatch({
        type: LEAVE_PARTICIPANT,
        payload: {
          room_id: payload,
          all: true,
        },
      });

      return resolve(response?.data?.data || []);
    } catch (error) {
      reject(error);
    }
  });

  return promise;
};

export const addArchivedConversation = (payload) => (dispatch) =>
  new Promise(async (resolve, reject) => {
    const selectedConversation = await store.getState().ChatRealTime
      .selectedConversation;
    const form = new FormData();
    form.append("conversation_id", payload._id);
    form.append("tab_id", getOrGenerateTabId());
    if (typeof payload?._id === "undefined")
      return reject(new Error(i18next.t("common:toasts.errorFetchApi")));

    const response = await MainService.addArchivedCoversation(form);
    if (response.data.success) {
      if (selectedConversation?.conversationId === payload._id) {
        dispatch(closeDrawerChat());

        dispatch(
          setChatSelectedConversation({
            selectedConversation: null,
          })
        );
        dispatch(setOpenDrawer({ type: "" }));
      }

      if (payload.item.total_unread > 0) {
        dispatch(
          substractNotification({
            number: payload.item.total_unread,
            discussion_id: payload._id,
          })
        );
      }

      toastNotification(
        "success",
        i18next.t("common:chat.successfullyArchived"),
        "topRight"
      );

      return resolve(response.data.conversation);
    } else return reject(new Error(i18next.t("common:toasts.errorFetchApi")));
  }).catch(() => {
    toastNotification(
      "error",
      i18next.t("common:toasts.errorFetchApi"),
      "topRight"
    );
  });

export const removeArchivedConversation = (payload) => (dispatch) =>
  new Promise(async (resolve, reject) => {
    try {
      const form = new FormData();
      form.append("conversation_id", payload._id);
      form.append("tab_id", getOrGenerateTabId());
      if (typeof payload?._id === "undefined")
        return reject(new Error(i18next.t("common:toasts.errorFetchApi")));
      const response = await MainService.removeArchivedCoversation(form);

      if (payload.item.total_unread > 0)
        dispatch(
          addNotification({
            number: payload.item.total_unread,
            discussion_id: payload._id,
          })
        );

      toastNotification(
        "success",
        i18next.t("common:chat.successfullyRestored"),
        "topRight"
      );

      return resolve(response.data.conversation);
    } catch (error) {
      reject(new Error(i18next.t("common:toasts.errorFetchApi")));
    }
  }).catch(() =>
    toastNotification(
      "error",
      i18next.t("common:toasts.errorFetchApi"),
      "topRight"
    )
  );

export const getArchivedConversations = () => async (dispatch) => {
  const promise = new Promise(async (resolve, reject) => {
    try {
      dispatch(setLoadingSideBar(LoadingSideBarStatus.load));

      let response = await MainService.getArchivedConversations();
      await dispatch({
        type: GET_ARCHIVE_CONVERSATIONS,
        payload: response?.data?.conversations || [],
      });
      dispatch(setLoadingSideBar(LoadingSideBarStatus.idle));

      await dispatch(setArchivedListChatFetched(true));

      return resolve(response?.data?.conversations || []);
    } catch (error) {
      reject(error);
      dispatch(setLoadingSideBar(LoadingSideBarStatus.idle));
    }
  });

  return promise;
};

export const getArchivedConversationsIds = () => async (dispatch) => {
  const promise = new Promise(async (resolve, reject) => {
    try {
      let response = await MainService.getArchivedConversationsIds();
      await dispatch({
        type: SET_ARCHIVED_LIST_IDS,
        payload: {
          list: response?.data?.conversations || [],
          count_archived_msg: response?.data?.total_unread_message_archive || 0,
        },
      });
      return resolve(response?.data?.conversations || []);
    } catch (error) {
      reject(error);
    }
  });

  return promise;
};

export const setMuteConversation = (payload) => async (dispatch) => {
  const promise = new Promise(async (resolve, reject) => {
    try {
      const urlencoded = new URLSearchParams();
      if (typeof payload?._id === "undefined")
        return reject(new Error(i18next.t("common:toasts.errorFetchApi")));
      urlencoded.append("tab_id", getOrGenerateTabId());
      const response = await MainService.setMuteConversation(
        payload?._id,
        urlencoded
      );

      if (response.data.success) {
        dispatch(addToMuteList(payload));
        return resolve(response.data);
      } else {
        reject(new Error(i18next.t("common:toasts.errorFetchApi")));
      }
    } catch (error) {
      reject(error);
    }
  });

  return promise;
};

export const setUnmuteConversation = (payload) => async (dispatch) => {
  const promise = new Promise(async (resolve, reject) => {
    try {
      if (typeof payload?._id === "undefined")
        return reject(new Error(i18next.t("common:toasts.errorFetchApi")));
      const urlencoded = new URLSearchParams();
      urlencoded.append("tab_id", getOrGenerateTabId());
      let response = await MainService.setUnmuteConversation(
        payload?._id,
        urlencoded
      );
      if (response.data.success) {
        dispatch(removeFromMuteList(payload));
        return resolve(response.data);
      } else {
        reject(new Error(i18next.t("common:toasts.errorFetchApi")));
      }
    } catch (error) {
      reject(error);
    }
  });

  return promise;
};

export const getAllStarredMessages = (payload) => async (dispatch) => {
  const promise = new Promise(async (resolve, reject) => {
    try {
      const allStarredListFetched = await store.getState().ChatRealTime
        .allStarredListFetched;
      if (allStarredListFetched && payload.currentPage === 1) return resolve();
      if (payload.currentPage > 1) payload.setLoadingMore(true);
      else {
        payload.setLoading(true);
      }
      const response = await MainService.getAllStarredMessages(
        payload.currentPage
      );

      dispatch({
        type: SET_ALL_STARRED_LIST,
        payload: {
          data: response.data.data,
          page: payload.currentPage,
        },
      });
      payload.setHasMore(
        response.data.meta.current_page < response.data.meta.last_page
      );

      return resolve(response.data.data);
    } catch (error) {
      payload.setError(error);
      dispatch(setAllStarredFetched(false));

      reject(error);
    } finally {
      payload.setLoadingMore(false);
      payload.setLoading(false);
    }
  });

  return promise;
};

export const getAllPinnedMessages = (payload) => async (dispatch) => {
  const promise = new Promise(async (resolve, reject) => {
    try {
      const allPinnedListFetched = await store.getState().ChatRealTime
        .allPinnedListFetched;
      if (allPinnedListFetched && payload.currentPage === 1) return resolve();
      if (payload.currentPage > 1) payload.setLoadingMore(true);
      else {
        payload.setLoading(true);
      }
      const response = await MainService.getAllPinnedMessages(
        payload.currentPage
      );
      if (!response.data.data) throw new Error("No data");
      dispatch({
        type: SET_ALL_PINNED_LIST,
        payload: {
          data: response.data.data,
          page: payload.currentPage,
        },
      });
      payload.setHasMore(payload.currentPage < response.data?.meta?.last_page);

      return resolve(response.data.data || []);
    } catch (error) {
      payload.setError(error);
      dispatch(setAllPinnedFetched(false));

      reject(error);
    } finally {
      payload.setLoadingMore(false);
      payload.setLoading(false);
    }
  });

  return promise;
};
export const getPolls = (payload) => async (dispatch) => {
  const promise = new Promise(async (resolve, reject) => {
    try {
      if (payload.currentPage > 1) payload.setLoadingMore(true);
      else {
        payload.setLoading(true);
      }
      const formData = new FormData();
      formData.append(
        isRoom(payload.item) ? "room_id" : "receiver_id",
        isRoom(payload.item)
          ? payload?.item?.room?._id
          : payload?.item?.contact?._id
      );

      let response = await MainService.getPolls(
        formData,
        isRoom(payload.item) ? "room" : "user",
        payload.currentPage
      );
      dispatch({
        type: SET_POLL_LIST,
        payload: {
          data: response?.data?.data,
          page: payload.currentPage,
        },
      });

      payload.setHasMore(payload.currentPage < response?.data?.meta?.last_page);
      payload.setLoadingMore(false);
      payload.setLoading(false);
      payload.setError("");

      return resolve(response?.data?.data || []);
    } catch (error) {
      payload.setError(error);
      payload.setLoadingMore(false);
      payload.setLoading(false);
      reject(error);
    }
  });

  return promise;
};
export const getAllPolls = (payload) => async (dispatch) => {
  const promise = new Promise(async (resolve, reject) => {
    try {
      if (payload.currentPage > 1) payload.setLoadingMore(true);
      else {
        payload.setLoading(true);
      }
      const formData = new FormData();
      formData.append(
        isRoom(payload.item) ? "room_id" : "receiver_id",
        isRoom(payload.item)
          ? payload?.item?.room?._id
          : payload?.item?.contact?._id
      );
      let response = await MainService.getAllPolls(
        formData,
        isRoom(payload.item) ? "room" : "user",
        payload.currentPage
      );
      dispatch({
        type: SET_ALL_POLL_LIST,
        payload: {
          data: response?.data?.data,
          page: payload.currentPage,
        },
      });

      payload.setHasMore(payload.currentPage < response?.data?.meta?.last_page);
      payload.setLoadingMore(false);
      payload.setLoading(false);
      payload.setError("");

      return resolve(response?.data?.data || []);
    } catch (error) {
      payload.setError(error);
      payload.setLoadingMore(false);
      payload.setLoading(false);
      reject(error);
    }
  });

  return promise;
};

export const isConnectedFunc = (payload) => async () => {
  const abortController = new AbortController();
  const latence = await store.getState().chat.latence;

  const time = setTimeout(() => {
    abortController.abort();

    clearTimeout(time);
  }, latence || LATENCE_ADDING_TIME * 2);

  const promise = new Promise(async (resolve, reject) => {
    try {
      const formData = new FormData();
      formData.append("tab_id", getOrGenerateTabId());
      formData.append("user_id", payload);

      const response = await MainService.getOnlineList(
        abortController.signal,
        formData
      );
      resolve(response?.data?.is_connected);
    } catch (error) {
      if (process.env.NODE_ENV !== "prod" && error.name === "AbortError")
        return;
      reject(error);
    }
  });
  return promise;
};

export const getDiscussionContact = async () => {
  const promise = new Promise(async (resolve, reject) => {
    try {
      let response = await MainService.getDiscussionContact();
      resolve(response.data);
    } catch (error) {
      reject(error);
    }
  });
  return promise;
};

export const goToMessage = async (payload) => async (dispatch) => {
  const selectedConversation = await store.getState().ChatRealTime
    .selectedConversation;
  dispatch(
    searchMsgInListConversations({
      loading: true,
    })
  );

  const response = await MainService.goToMsg(payload.id_search, payload.type);
  if (response.status === 200) {
    const arrayOfResponse = [
      ...response.data.previous_messages.reverse(),
      response.data.data,
      ...response.data.next_messages,
    ];

    dispatch(serachMessageData({ data: [...arrayOfResponse], type: "normal" }));
    dispatch(
      searchMsgInListConversations({
        id: payload.id_search,
        virtual: payload.id_search,
        orientation: "",
        discussion_id: selectedConversation?.conversationId,
        last_id: response.data.last_previous_id,
        first_id: response.data.last_next_id,
        loading: false,
      })
    );
    if (selectedConversation?.external)
      dispatch(
        updateChatSelectedConversation({
          external: false,
        })
      );
  }
};

export const globalSearch = (payload) => () => {
  return new Promise(async (resolve, reject) => {
    try {
      const formData = new FormData();
      formData.append("keyword", payload.keyword);
      payload.filter && formData.append("filter", payload.filter);
      const response = await MainService.globalSearch(
        formData,
        payload.signal,
        payload.page
      );
      resolve(response.data);
    } catch (error) {
      if (error.name === "CanceledError") return;
      reject([]);
      toastNotification("error", payload.errorText, "topRight");
    }
  });
};

export const generateTextWithAi = (payload) => () => {
  return new Promise(async (resolve, reject) => {
    try {
      // setTimeout(() => {
      //   resolve("abcdef  ");
      // }, 3000);
      const formData = new FormData();
      formData.append("message", payload.message);
      const response = await MainService.generateText(formData, payload.signal);
      resolve(response.data?.improved);
    } catch (error) {
      if (error.name === "CanceledError") return;
      reject(new Error(i18next.t("common:toasts.errorFetchApi")));
    }
  });
};
