import { mergeAttributes, Node } from "@tiptap/core";
import { ReactNodeViewRenderer } from "@tiptap/react";

import RootBlockComponent from "./root-block-component";

// Extend the Commands interface to include RootBlockCommands

// Create and export the RootBlock node
const RootBlock = Node.create({
  name: "rootblock",
  group: "block", // Allow rootblock to coexist with other block nodes
  content:
    "(paragraph | heading | taskList | bulletList | orderedList | table)+", // Support multiple block types
  draggable: true,
  selectable: false,
  priority: 1000,

  addOptions() {
    return {
      HTMLAttributes: {},
    };
  },

  addCommands() {
    return {
      setRootBlock:
        (position) =>
        ({ state, chain }) => {
          const pos = position ?? state.selection.from;
          return chain()
            .insertContentAt(pos, {
              type: "rootblock",
              content: [{ type: "paragraph" }],
            })
            .focus(pos + 4)
            .run();
        },
    };
  },

  parseHTML() {
    return [{ tag: 'div[data-type="rootblock"]' }];
  },

  renderHTML({ HTMLAttributes }) {
    return [
      "div",
      mergeAttributes(HTMLAttributes, { "data-type": "rootblock" }),
      0,
    ];
  },

  addNodeView() {
    return ReactNodeViewRenderer(RootBlockComponent);
  },
});

export default RootBlock;

// export default RootBlock;
