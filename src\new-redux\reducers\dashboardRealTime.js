import WhatsApp from "../../assets/WhatsApp.svg";
import Messenger from "../../assets/messenger.svg";
import Instagram from "../../assets/instagram.png";
import Livechat from "../../assets/livechat.svg";

import {
  GET_DATA,
  SET_UNREAD_MSG_ONE_TO_ONE,
  SET_UNREAD_MSG_GROUPS,
  SET_MISSED_CALLS,
  SET_RECEIVED_CALLS,
  SET_OUTGOING_CALLS,
  SET_VISITORS_RMC,
  SET_NEW_MSGS_RMC,
  SET_TOTAL_CONVERSATION_RMC,
  SET_TASKS,
  SET_LOADING,
  SET_RMC,
  SET_VOIP_DASHBOARD,
  SET_SELECTED_MAIL,
  SET_EMAIL_DASHBOARD,
  GET_STATS_CHAT,
  SET_SELECTED_QUEUE,
  SET_QUEUE_DASHBOARD,
  SET_STATS_TASKS_DASHBOARD,
  SET_DATE_DASHBOARD,
  SET_SELECTED_PIPELINE_IN_TICKET,
  SET_PIPELINES_TICKET,
  SET_SELECTED_ACOUNT_IN_EMAIL,
  SET_PIPELINES_LEAD,
  SET_SELECTED_PIPELINE_IN_LEAD,
  SET_SELECTED_PIPELINE_IN_CONTACT,
  SET_PIPELINES_CONTACT,
  ADD_TASK_IN_DASHBOARD,
  REMOVE_TASK_IN_DASHBOARD,
  UPDATE_TASK_IN_DASHBOARD,
  SET_STATS_DEALS,
  RESET_DATA,
  SET_NAMES_QUEUES,
  DESTROY_DASHBOARD_STATE,
  SET_SELECTED_DEP_RMC,
  SET_STATS_TICKETS,
  SET_STATS_LEADS_CONTACTS,
} from "../constants";
const getInitialStateFromLocalStorage = () => {
  try {
    // Remplacez "yourStorageKey" par la clé que vous utilisez
    const storedData = localStorage.getItem("dateDashboard");
    return storedData ? JSON.parse(storedData) : null;
  } catch (error) {
    console.error("Failed to parse localStorage data", error);
    return null;
  }
};
const initialState = {
  startDate: getInitialStateFromLocalStorage()?.startDate || "",
  endDate: getInitialStateFromLocalStorage()?.endDate || "",
  isMount: false,
  unreadMsgOneToOne: 0,
  unreadMessagesInGroups: 0,
  missedCalls: 0,
  receivedCalls: 0,
  outgoingCalls: 0,
  visitorsRmc: 0,
  newMsgsRmcOfDay: 0,
  totalConversationRmc: 0,
  selectedDepRmc: "all",
  tasks: [],
  stages: [],
  iconsTasks: [],
  loading: true,
  totalCalls: 0,
  totalMessages: 0,
  unreadEmail: 0,
  totalEmails: [],
  selectedMail: "",
  selectedQueue: "",
  missedTodayCall: 0,
  outgoingTodayCall: 0,
  receivedTodayCall: 0,
  totalCalltoday: 0,
  existRmc: null,
  missedUnreturnedCalls: 0,
  totalunreadMessagesTags: 0,
  totalUnreadEmailReceived: 0,
  totalUnreadEmailSent: 0,
  unreadArchivedMessages: 0,
  totalQueues: [],
  emailsReceived: 0,
  allQueues: [],
  totalFamilies: [],
  selectedPipelineInTicket: { value: "", label: "" },
  selectedPipelineInLead: { value: "", label: "" },
  selectedPipelineInContact: { value: "", label: "" },
  selectedAccountInEmail: "",
  channelsRmc: [
    {
      channel: "wa",
      count: 0,
      icon: (
        <img
          src={WhatsApp}
          alt="WhatsApp"
          style={{ width: "30px", height: "30px" }}
        />
      ),
      color: "#4BC557",
    },
    {
      channel: "ig",
      count: 0,
      icon: (
        <img
          src={Instagram}
          alt="Instagram"
          style={{ width: "23px", height: "23px" }}
        />
      ),
      color: "#e1306c",
    },
    {
      channel: "fb",
      count: 0,
      icon: (
        <img
          src={Messenger}
          alt="Messenger"
          style={{ width: "23px", height: "23px" }}
        />
      ),
      color: "#168AFF",
    },
    {
      channel: "chat",
      count: 0,

      icon: (
        <img
          src={Livechat}
          alt="livechat"
          style={{ width: "23px", height: "23px" }}
        />
      ),
      color: "#049CFF",
    },
  ],
  depsRmc: [],
  statsTasks: [],
  pipelinesTicket: [],
  pipelinesLead: [],
  pipelinesContact: [],
  statsDeals: [],
  statsTickets: {
    cards: [
      { name: "Total des ticket", value: 0, color: "#1d4ed8", icon: "Ticket" },
      {
        name: "Ticket Créé",
        value: 0,
        color: "#10b981",
        icon: "TicketPercent",
      },
      {
        name: "SLA Respecté",
        value: 0,
        color: "#28a745",
        icon: "CalendarCheck2",
      },
      { name: "SLA Dépassé", value: 0, color: "#dc3545", icon: "CalendarX2" },
    ],
    gauge: {},
  },
  leads: [],
  contacts: [],
};
const DashboardRealTime = (state = initialState, action) => {
  const { payload, type } = action;
  switch (type) {
    case GET_DATA:
      return { ...state, ...payload };
    case RESET_DATA:
      return {
        ...state,
        unreadMsgOneToOne: 0,
        unreadMessagesInGroups: 0,
        missedCalls: 0,
        receivedCalls: 0,
        outgoingCalls: 0,
        visitorsRmc: 0,
        newMsgsRmcOfDay: 0,
        totalConversationRmc: 0,
        tasks: [],
        stages: [],
        iconsTasks: [],
        loading: false,
        totalCalls: 0,
        totalMessages: 0,
        unreadEmail: 0,
        missedTodayCall: 0,
        outgoingTodayCall: 0,
        receivedTodayCall: 0,
        totalCalltoday: 0,
        existRmc: null,
        missedUnreturnedCalls: 0,
        totalunreadMessagesTags: 0,
        totalUnreadEmailReceived: 0,
        totalUnreadEmailSent: 0,
        unreadArchivedMessages: 0,
        statsDeals: [],
        channelsRmc: [
          {
            channel: "wa",
            count: 0,
            icon: (
              <img
                src={WhatsApp}
                alt="WhatsApp"
                style={{ width: "30px", height: "30px" }}
              />
            ),
            color: "#4BC557",
          },
          {
            channel: "ig",
            count: 0,
            icon: (
              <img
                src={Instagram}
                alt="Instagram"
                style={{ width: "23px", height: "23px" }}
              />
            ),
            color: "#e1306c",
          },
          {
            channel: "fb",
            count: 0,
            icon: (
              <img
                src={Messenger}
                alt="Messenger"
                style={{ width: "23px", height: "23px" }}
              />
            ),
            color: "#168AFF",
          },
          {
            channel: "chat",
            count: 0,

            icon: (
              <img
                src={Livechat}
                alt="livechat"
                style={{ width: "23px", height: "23px" }}
              />
            ),
            color: "#049CFF",
          },
        ],
      };
    case SET_VOIP_DASHBOARD:
      return {
        ...state,
        missedUnreturnedCalls: payload?.missed_calls_not_returned_today,
        missedTodayCall: payload?.missed_today_call,
        outgoingTodayCall: payload?.outgoing_today_call,
        receivedTodayCall: payload?.received_today_call,
        totalCalltoday: payload?.total_today,
      };
    case SET_LOADING:
      return { ...state, loading: payload };
    case SET_UNREAD_MSG_ONE_TO_ONE:
      return { ...state, unreadMsgOneToOne: payload };

    case SET_UNREAD_MSG_GROUPS:
      return { ...state, unreadMessagesInGroups: payload };

    case SET_MISSED_CALLS:
      return { ...state, openTaskRoomDrawer: payload };

    case SET_RECEIVED_CALLS:
      return { ...state, selectedViewInTask: payload };
    case SET_RMC:
      return { ...state, channelsRmc: payload };
    case SET_OUTGOING_CALLS:
      return { ...state, isUserNotified: payload };
    case SET_VISITORS_RMC:
      return { ...state, isUserNotified: payload };
    case SET_NEW_MSGS_RMC:
      return { ...state, isUserNotified: payload };
    case SET_TOTAL_CONVERSATION_RMC:
      return { ...state, isUserNotified: payload };
    case SET_TASKS:
      return {
        ...state,
        tasks: payload.tasks,
        stages: payload.stages,
        iconsTasks: payload.iconsTasks,
      };
    case ADD_TASK_IN_DASHBOARD:
      return {
        ...state,
        tasks: [...state.tasks, payload],
      };
    case REMOVE_TASK_IN_DASHBOARD:
      return {
        ...state,
        tasks: state.tasks.filter((task) => task.id !== payload),
      };
    case UPDATE_TASK_IN_DASHBOARD:
      return {
        ...state,
        tasks: state.tasks.map((task) =>
          task.id === payload?.id ? { ...task, ...payload } : task
        ),
      };
    case SET_SELECTED_MAIL:
      return { ...state, selectedMail: payload };
    case SET_SELECTED_QUEUE:
      return { ...state, selectedQueue: payload };
    case SET_EMAIL_DASHBOARD:
      return {
        ...state,
        unreadEmail: payload.unreadEmail,
        totalEmails: payload.totalEmails,
        totalUnreadEmailReceived: payload.totalUnreadEmailReceived,
        totalUnreadEmailSent: payload.totalUnreadEmailSent,
        emailsReceived: payload.emailsReceived,
      };
    case GET_STATS_CHAT:
      return {
        ...state,
        unreadMsgOneToOne: payload.unreadMsgOneToOne,
        unreadMessagesInGroups: payload.unreadMessagesInGroups,
        totalunreadMessagesTags: payload.totalunreadMessagesTags,
        unreadArchivedMessages: payload.unreadArchivedMessages,
      };
    case SET_QUEUE_DASHBOARD:
      return {
        ...state,
        totalQueues: payload.totalQueues.map((el) => ({
          ...el,
          answered_calls: String(el.answered_calls),
          no_answered_calls: String(el.no_answered_calls),
          no_answered_calls_not_returned: String(el.no_answered_calls),
        })),
      };
    case SET_STATS_TASKS_DASHBOARD:
      return {
        ...state,
        statsTasks: payload,
      };
    case SET_DATE_DASHBOARD:
      return {
        ...state,
        startDate: payload?.startDate,
        endDate: payload?.endDate,
      };
    case SET_SELECTED_PIPELINE_IN_TICKET:
      return {
        ...state,
        selectedPipelineInTicket: payload,
      };
    case SET_SELECTED_PIPELINE_IN_CONTACT:
      return {
        ...state,
        selectedPipelineInContact: payload,
      };
    case SET_SELECTED_PIPELINE_IN_LEAD:
      return {
        ...state,
        selectedPipelineInLead: payload,
      };
    case SET_SELECTED_ACOUNT_IN_EMAIL:
      return {
        ...state,
        selectedAccountInEmail: payload,
      };
    case SET_PIPELINES_TICKET:
      return {
        ...state,
        pipelinesTicket: payload,
      };
    case SET_PIPELINES_LEAD:
      return {
        ...state,
        pipelinesLead: payload,
      };
    case SET_PIPELINES_CONTACT:
      return {
        ...state,
        pipelinesContact: payload,
      };
    case SET_STATS_DEALS:
      return {
        ...state,
        statsDeals: payload,
      };
    case SET_NAMES_QUEUES:
      return {
        ...state,
        allQueues: payload,
      };
    case SET_SELECTED_DEP_RMC:
      return {
        ...state,
        selectedDepRmc: payload,
      };
    case SET_STATS_TICKETS:
      return {
        ...state,
        statsTickets: payload,
      };
    case SET_STATS_LEADS_CONTACTS:
      return {
        ...state,
        leads: payload?.leads,
        contacts: payload?.contacts,
      };
    case DESTROY_DASHBOARD_STATE:
      return {
        ...initialState,
        startDate: state.startDate,
        endDate: state.endDate,
      };
    default:
      return state;
  }
};

export default DashboardRealTime;
