import React, { useContext, useEffect, useMemo, useState } from "react";
import { HolderOutlined, MenuOutlined } from "@ant-design/icons";
import { DndContext } from "@dnd-kit/core";
// import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Button, Switch, Table } from "antd";
import { useTranslation } from "react-i18next";
import { generateAxios } from "services/axiosInstance";
import { URL_ENV } from "index";
import { toastNotification } from "components/ToastNotification";
import i18n from "translations/i18n";
const RowContext = React.createContext({});

const DragHandle = () => {
  const { setActivatorNodeRef, listeners } = useContext(RowContext);

  return (
    <Button
      type="text"
      size="small"
      icon={<MenuOutlined />}
      style={{
        cursor: "move",
      }}
      ref={setActivatorNodeRef}
      {...listeners}
    />
  );
};

const Row = (props) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    setActivatorNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: props["data-row-key"],
  });
  const style = {
    ...props.style,
    transform: CSS.Translate.toString(transform),
    transition,
    ...(isDragging
      ? {
          position: "relative",
          zIndex: 9999,
        }
      : {}),
  };
  const contextValue = useMemo(
    () => ({
      setActivatorNodeRef,
      listeners,
    }),
    [setActivatorNodeRef, listeners]
  );
  return (
    <RowContext.Provider value={contextValue}>
      <tr {...props} ref={setNodeRef} style={style} {...attributes} />
    </RowContext.Provider>
  );
};
const BannerIndicatorsConfigMail = () => {
  const [t] = useTranslation("common");
  const [dataSource, setDataSource] = useState([]);
  const [dataBeforeRank, setDataBeforeRank] = useState([]);

  const [loading, setLoading] = useState(false);
  const changeSwitch = async (bandeau_id, is_active) => {
    setLoading(true);

    try {
      const {
        data: { data },
      } = await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).post(`/update-bandeau-active`, {
        bandeau_id,
        is_active: is_active ? 1 : 0,
      });
      setDataSource((prev) =>
        prev.map((el) =>
          el.id === bandeau_id ? { ...el, is_active: is_active } : el
        )
      );
      setDataBeforeRank((prev) =>
        prev.map((el) =>
          el.id === bandeau_id ? { ...el, is_active: is_active } : el
        )
      );
      setLoading(false);
    } catch (err) {
      setLoading(false);
      toastNotification("error", t("toasts.somethingWrong"), "topRight");
    }
  };
  const columns = [
    {
      key: "sort",
      align: "center",
      width: 80,
      render: () => <DragHandle />,
    },
    {
      title: "label",
      dataIndex: "label",
    },
    {
      title: "Age",
      dataIndex: "is_active",
      render: (_, record) => (
        <div className="ml-1">
          <Switch
            size="small"
            checked={record?.is_active}
            onChange={(value) => changeSwitch(record.id, value)}
          />
        </div>
      ),
    },
  ];
  useEffect(() => {
    const getData = async () => {
      setLoading(true);
      try {
        const {
          data: { data },
        } = await generateAxios(
          URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
        ).get(`/kpi-bandeau?language=${i18n.language}`);
        setDataSource(data.map((el) => ({ ...el, key: el.rank })));
        setDataBeforeRank(data.map((el) => ({ ...el, key: el.rank })));
        setLoading(false);
      } catch (err) {
        setLoading(false);
        toastNotification("error", t("toasts.somethingWrong"), "topRight");
      }
    };
    getData();
  }, []);
  const updateRank = async (array) => {
    const formData = new FormData();
    array &&
      array.forEach((item, i) => formData.append(`rank[${item?.id}]`, i + 1));
    try {
      setLoading(true);
      await generateAxios(
        URL_ENV?.REACT_APP_BASE_URL + process.env.REACT_APP_SUFFIX_API
      ).post("update-bandeau-rank", formData);
      setDataBeforeRank(dataSource);
      setLoading(false);
    } catch (err) {
      console.log(err);
      setDataSource(dataBeforeRank);
      setLoading(false);
      toastNotification("error", `rank failed`, "topRight");
    }
  };
  const onDragEnd = ({ active, over }) => {
    if (active.id !== over?.id) {
      setDataSource((prevState) => {
        const activeIndex = prevState.findIndex(
          (record) => record.key === active?.id
        );
        const overIndex = prevState.findIndex(
          (record) => record.key === over?.id
        );
        updateRank(arrayMove(prevState, activeIndex, overIndex));
        return arrayMove(prevState, activeIndex, overIndex);
      });
    }
  };

  return (
    <DndContext onDragEnd={onDragEnd}>
      <SortableContext
        items={dataSource.map((i) => i.key)}
        strategy={verticalListSortingStrategy}
      >
        <Table
          rowKey="key"
          showHeader={false}
          components={{
            body: {
              row: Row,
            },
          }}
          loading={loading}
          columns={columns}
          dataSource={dataSource}
          pagination={false}
        />
      </SortableContext>
    </DndContext>
  );
};
export default BannerIndicatorsConfigMail;
