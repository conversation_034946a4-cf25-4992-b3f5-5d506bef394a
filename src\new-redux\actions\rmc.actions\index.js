import { NEW_NOTIF, OPEN_VISIO_RMC, VISIO_RMC_ELEMENT_ID } from "new-redux/constants";

export const setNotifRmc = (payload) => (dispatch) => {
  dispatch({ type: NEW_NOTIF, payload });
};

export const setOPenVisioRmc = (payload) => (dispatch) => {
  dispatch({ type: OPEN_VISIO_RMC, payload });
};

export const setVisioRmcElementId = (payload) => (dispatch) => {
  dispatch({ type: VISIO_RMC_ELEMENT_ID, payload });
};
